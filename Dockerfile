FROM devops.digitalvolvo.com/nexus/repository/newbie-yongyou-docker-registry/alpine-jdk-8-sw-arthas:8u192-jdk-sw8u3

LABEL description = "this is dmscus.customer"

COPY dmscus.customer/target/dmscus.customer-0.0.1-SNAPSHOT.jar /dmscus.customer.jar
COPY dmscus.customer/startup.sh /startup.sh
RUN chmod +x /startup.sh
WORKDIR /

EXPOSE 8002

#ENTRYPOINT  java $JAVA_OPTS -Dspring.profiles.active=test -jar /dmscus.customer.jar
ENTRYPOINT  /startup.sh