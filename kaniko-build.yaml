apiVersion: v1
kind: Pod
metadata:
  name: kaniko-dmscus-customer
  namespace: jenkins-build
spec:
  imagePullSecrets:
    - name: acr-secret
  containers:
    - name: kaniko
      env:
        - name: DOCKER_CONFIG
          value: /kaniko/.docker/
      image: registry-vpc.cn-shanghai.aliyuncs.com/volvocedapac/kaniko-volvo:0.0.3
      args: ["--dockerfile=/workspace/workspace/dmscus-customer-build/dmscus.customer/Dockerfile",
             "--context=dir:///workspace/workspace/dmscus-customer-build",
             "--destination=devops.digitalvolvo.com/nexus/repository/newbie-yongyou-docker-registry/dmscus-customer:0.0.1-uat",
             "--skip-tls-verify=true",
             "--skip-tls-verify-pull=true"]
      volumeMounts:
        - name: kaniko-secret
          mountPath: /secret
        - name: build-workspace
          mountPath: /workspace
        - name: cache-volume
          mountPath: /cache
      #- name: kaniko-docker-config-volume
      #  mountPath: /kaniko/.docker/config.json
      #  subPath: config.json
  nodeSelector:
    failure-domain.beta.kubernetes.io/zone: cn-shanghai-e
  restartPolicy: Never
  volumes:
    - name: kaniko-secret
      secret:
        secretName: volvo-nexus-key
    - name: build-workspace
      persistentVolumeClaim:
        claimName: jenkins-home-newbie-build2-nas
    - name: cache-volume
      persistentVolumeClaim:
        claimName: kaniko-cache-nas
        #- name: kaniko-docker-config-volume
        #  configMap:
        #    name: kaniko-docker-config

