<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds">
    <!--为了防止进程退出时，内存中的数据丢失，请加上此选项-->
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>

    <property resource="bootstrap.yml"/>
    <springProperty scope="context" name="appName" source="spring.application.name" defaultValue="app-default-center"/>

    <!-- 文件输出格式 -->
    <property name="PATTERN"
              value="%-12(%d{yyyy-MM-dd HH:mm:ss.SSS}) | [${appName}] | gtraceid: %X{apm-gtraceid} | traceid: %X{apm-traceid} | spanId: %X{apm-spanid} | [%level] | [%thread] %c#%L | %msg%n"/>
    <!-- Console output -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoder defaults to ch.qos.logback.classic.encoder.PatternLayoutEncoder -->
        <encoder >
            <pattern>%red(%d{yyyy-MM-dd HH:mm:ss.SSS}) | [${appName}] | gtraceid: %X{apm-gtraceid} | traceid: %X{apm-traceid} | spanId: %X{apm-spanid} | %-5level | %blue(${PID:-}) --- [%t] %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
