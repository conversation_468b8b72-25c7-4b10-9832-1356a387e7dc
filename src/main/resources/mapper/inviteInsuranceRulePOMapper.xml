<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceRuleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceRulePO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="invite_type" property="inviteType"/>
        <result column="invite_rule" property="inviteRule"/>
        <result column="rule_value" property="ruleValue"/>
        <result column="day_in_advance" property="dayInAdvance"/>
        <result column="remind_interval" property="remindInterval"/>
        <result column="is_use" property="isUse"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="close_interval" property="closeInterval"/>
        <result column="update_is_execute" property="updateIsExecute"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, dealer_code, invite_type,
           invite_rule, rule_value, day_in_advance, remind_interval, is_use, data_sources,
           is_deleted, is_valid, created_at, updated_at, close_interval, update_is_execute
        </sql>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceRulePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_insurance_rule t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.inviteType !=null and params.inviteType != '' ">
            AND t.invite_type = #{params.inviteType}
        </if>
        <if test=" params.inviteRule !=null and params.inviteRule != '' ">
            AND t.invite_rule = #{params.inviteRule}
        </if>
        <if test=" params.dayInAdvance !=null and params.dayInAdvance != '' ">
            AND t.day_in_advance = #{params.dayInAdvance}
        </if>
        <if test=" params.remindInterval !=null and params.remindInterval != '' ">
            AND t.remind_interval = #{params.remindInterval}
        </if>
        <if test=" params.isUse !=null and params.isUse != '' ">
            AND t.is_use = #{params.isUse}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySqlVcdc" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceRulePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_insurance_rule t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.inviteType !=null and params.inviteType != '' ">
            AND t.invite_type = #{params.inviteType}
        </if>
        <if test=" params.inviteRule !=null and params.inviteRule != '' ">
            AND t.invite_rule = #{params.inviteRule}
        </if>
        <if test=" params.dayInAdvance !=null and params.dayInAdvance != '' ">
            AND t.day_in_advance = #{params.dayInAdvance}
        </if>
        <if test=" params.remindInterval !=null and params.remindInterval != '' ">
            AND t.remind_interval = #{params.remindInterval}
        </if>
        <if test=" params.isUse !=null and params.isUse != '' ">
            AND t.is_use = #{params.isUse}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <select id="getInviteInsuranceRuleDlr" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceRulePO">
        select IFNULL(r.id,t.id) as id,IFNULL(r.dealer_code,t.dealer_code) as
        dealer_code,t.invite_type,t.invite_rule,IFNULL(r.rule_value,t.rule_value) as rule_value,
        IFNULL(r.day_in_advance,t.day_in_advance) as day_in_advance,IFNULL(r.remind_interval,t.remind_interval) as remind_interval,
        IFNULL(r.close_interval,t.close_interval) as close_interval,IFNULL(r.is_use,t.is_use) as is_use
        from tt_invite_insurance_rule t
        left JOIN  tt_invite_insurance_rule r on t.invite_type=r.invite_type and t.invite_rule=r.invite_rule and
        r.dealer_code=#{params.dealerCode}
        where t.dealer_code='VCDC'
    </select>

    <!-- 商业险 -->
    <select id="getViInviteInsuranceRuleDlr" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceRulePO">
        select IFNULL(r.id,t.id) as id,IFNULL(r.dealer_code,t.dealer_code) as
        dealer_code,t.invite_type,t.invite_rule,IFNULL(r.rule_value,t.rule_value) as rule_value,
        IFNULL(r.day_in_advance,t.day_in_advance) as day_in_advance,IFNULL(r.remind_interval,t.remind_interval) as remind_interval,
        IFNULL(r.close_interval,t.close_interval) as close_interval,IFNULL(r.is_use,t.is_use) as is_use
        from tt_invite_insurance_rule t
        left JOIN  tt_invite_insurance_rule r on t.invite_type=r.invite_type and t.invite_rule=r.invite_rule and
        r.dealer_code=#{dealerCode} and r.invite_type = 2
        where t.dealer_code='VCDC' and t.invite_type = 2
    </select>

    <!-- 交强险 -->
    <select id="getClivtaInviteInsuranceRuleDlr" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceRulePO">
        select IFNULL(r.id,t.id) as id,IFNULL(r.dealer_code,t.dealer_code) as
        dealer_code,t.invite_type,t.invite_rule,IFNULL(r.rule_value,t.rule_value) as rule_value,
        IFNULL(r.day_in_advance,t.day_in_advance) as day_in_advance,IFNULL(r.remind_interval,t.remind_interval) as remind_interval,
        IFNULL(r.close_interval,t.close_interval) as close_interval,IFNULL(r.is_use,t.is_use) as is_use
        from tt_invite_insurance_rule t
        left JOIN  tt_invite_insurance_rule r on t.invite_type=r.invite_type and t.invite_rule=r.invite_rule and
        r.dealer_code=#{dealerCode} and r.invite_type = 1
        where t.dealer_code='VCDC' and t.invite_type = 1
    </select>


    <select id="getViInviteInsuranceRuleExist" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_insurance_rule t
        WHERE 1=1
        and t.invite_type = 2
        <if test=" dealerCode !=null and dealerCode != '' ">
            AND t.dealer_code = #{dealerCode}
        </if>
    </select>

    <select id="getClivtaInviteInsuranceRuleExist" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_insurance_rule t
        WHERE 1=1
        and t.invite_type = 1
        <if test=" dealerCode !=null and dealerCode != '' ">
            AND t.dealer_code = #{dealerCode}
        </if>
    </select>

    <select id="getViInviteInsuranceRuleCount" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_insurance_rule t
        WHERE 1=1
        and t.invite_type = 2
        <if test=" dealerCode !=null and dealerCode != '' ">
            AND t.dealer_code != #{dealerCode}
        </if>
    </select>

    <select id="getClivtaInviteInsuranceRuleCount" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_insurance_rule t
        WHERE 1=1
        and t.invite_type = 1
        <if test=" dealerCode !=null and dealerCode != '' ">
            AND t.dealer_code != #{dealerCode}
        </if>
    </select>


    <update id="updateAllViInsuranceRule">
        update tt_invite_insurance_rule
        set is_use = #{params.isUse},
            day_in_advance = #{params.dayInAdvance},
            close_interval = #{params.closeInterval},
            updated_at = now(),
            updated_by = #{userId}
        where
        invite_type = 2
        and dealer_code != #{params.dealerCode}
    </update>

    <update id="updateAllClivtaInsuranceRule">
        update tt_invite_insurance_rule
        set is_use = #{params.isUse},
            day_in_advance = #{params.dayInAdvance},
            close_interval = #{params.closeInterval},
            updated_at = now(),
            updated_by = #{userId}
        where
        invite_type = 1
        and dealer_code != #{params.dealerCode}
    </update>


    <select id="getInviteInsuranceRuleVcdc" resultMap="BaseResultMap" parameterType="java.lang.String">
        select IFNULL(r.id,t.id) as id,#{dealerCode} as
        dealer_code,t.invite_type,t.invite_rule,IFNULL(r.rule_value,t.rule_value) as rule_value,
        IFNULL(r.day_in_advance,t.day_in_advance) as day_in_advance,IFNULL(r.remind_interval,t.remind_interval) as remind_interval,
        IFNULL(r.close_interval,t.close_interval) as close_interval,
        IFNULL(r.is_use,t.is_use) as is_use
        from tt_invite_insurance_rule t
        left JOIN  tt_invite_insurance_rule r on t.invite_type=r.invite_type and t.invite_rule=r.invite_rule and
        r.dealer_code=#{dealerCode}
        where t.dealer_code='VCDC'
    </select>



</mapper>
