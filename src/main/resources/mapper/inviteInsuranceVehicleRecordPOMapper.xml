<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="parent_id" property="parentId"/>
        <result column="is_main" property="isMain"/>
        <result column="source_type" property="sourceType"/>
        <result column="vin" property="vin"/>
        <result column="license_plate_num" property="licensePlateNum"/>
        <result column="name" property="name"/>
        <result column="tel" property="tel"/>
        <result column="invite_type" property="inviteType"/>
        <result column="advise_in_date" property="adviseInDate"/>
        <result column="new_advise_in_date" property="newAdviseInDate"/>
        <result column="newest_advise_in_date" property="newestAdviseInDate"/>
        <result column="plan_follow_date" property="planFollowDate"/>
        <result column="actual_follow_date" property="actualFollowDate"/>
        <result column="plan_remind_date" property="planRemindDate"/>
        <result column="actual_remind_date" property="actualRemindDate"/>
        <result column="SA_ID" property="saId"/>
        <result column="SA_NAME" property="saName"/>
        <result column="LAST_SA_ID" property="lastSaId"/>
        <result column="LAST_SA_NAME" property="lastSaName"/>
        <result column="follow_status" property="followStatus"/>
        <result column="is_book" property="isBook"/>
        <result column="book_no" property="bookNo"/>
        <result column="order_status" property="orderStatus"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="sex" property="sex"/>
        <result column="age" property="age"/>
        <result column="model" property="model"/>
        <result column="item_code" property="itemCode"/>
        <result column="item_name" property="itemName"/>
        <result column="item_type" property="itemType"/>
        <result column="part_item_rule_id" property="partItemRuleId"/>
        <result column="last_change_date" property="lastChangeDate"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="is_voc" property="isVoc"/>
        <result column="last_maintenance_date" property="lastMaintenanceDate"/>
        <result column="date_interval" property="dateInterval"/>
        <result column="son_invite_type" property="sonInviteType"/>
        <result column="advise_in_mileage" property="adviseInMileage"/>
        <result column="invite_time" property="inviteTime"/>
        <result column="first_follow_date" property="firstFollowDate"/>
        <result column="order_finish_date" property="orderFinishDate"/>
        <result column="insurance_status" property="insuranceStatus"/>
        <result column="is_joint_guarantee" property="isJointGuarantee"/>
        <result column="total_score" property="score"/>
        <result column="call_length" property="callLength"></result>
        <result column="call_detail_id" property="callDetailId"></result>
        <result column="call_time" property="callTime"></result>
        <result column="close_interval" property="closeInterval"></result>
        <result column="content" property="content"></result>
        <result column="lose_reason" property="loseReason"></result>
        <result column="follow_total" property="followTotal"/>
        <result column="insurance_type" property="insuranceType"/>
        <result column="insurance_bill_id" property="insuranceBillId"/>
        <result column="clue_type" property="clueType"/>
        <result column="new_insure_no" property="newInsureNo"/>
        <result column="total_score" property="score" />
        <result column="call_length" property="callLength" />
        <result column="start_time" property="callTime" />
        <result column="callDetailId" property="callDetailId" />
        <result column="insurance_name" property="insuranceName"/>
        <result column="is_insure_success" property="isInsureSuccess"/>
        <result column="advise_date_interval" property="adviseDateInterval" />
        <result column="plan_date_interval" property="planDateInterval" />
        <result column="insurance_id" property="insuranceId" />

       <!-- <result column="small_org_name" property="smallOrgName"/>
        <result column="PROVINCE_ID" property="provinceId"/>
        <result column="PROVINCE_NAME" property="provinceName"/>
        <result column="CITY_ID" property="cityId"/>
        <result column="CITY_NAME" property="cityName"/>
        <result column="GROUP_COMPANY_NAME" property="groupCompanyName"/>-->
        <result column="factory_insurance_expiry_date" property="factoryInsuranceExpiryDate"/>
        <result column="advance_issuance_date" property="advanceIssuanceDate"/>
        <result column="suggested_care_date" property="suggestedCareDate"/>
        <result column="clue_issuance_date" property="clueIssuanceDate"/>
        <result column="clue_data_source" property="clueDataSource"/>
        <result column="clue_issuance_type" property="clueIssuanceType"/>
        <result column="has_instore_upgrade" property="hasInstoreUpgrade"/>
        <result column="instore_upgrade_time" property="instoreUpgradeTime"/>
        <result column="clue_generator" property="clueGenerator"/>
        <result column="has_instore_modified" property="hasInstoreModified"/>
        <result column="instore_modification_time" property="instoreModificationTime"/>
        <result column="insurance_no" property="insuranceNo"/>
        <result column="insuranceNames" property="insuranceNames"/>
        <result column="policy_creation_date" property="policyCreationDate"/>
        <result column="policy_source" property="policySource"/>
        <result column="policy_effective_date" property="policyEffectiveDate"/>
        <result column="policy_expiration_date" property="policyExpirationDate"/>
        <result column="owner_type" property="ownerType" />
        <result column="new_insurance_no" property="newInsuranceNo"/>
        <result column="new_insurance_name" property="newInsuranceName"/>
        <result column="new_policy_creation_date" property="newPolicyCreationDate"/>
        <result column="new_policy_source" property="newPolicySource"/>
        <result column="new_policy_effective_date" property="newPolicyEffectiveDate"/>
        <result column="commercial_insurance_type" property="commercialInsuranceType"/>
        <result column="new_commercial_insurance_type" property="newCommercialInsuranceType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.app_id, t.owner_code, t.owner_par_code, t.org_id, t.id, t.parent_id, t.is_main, t.source_type, t.vin,
        t.license_plate_num, t.name, t.tel, t.invite_type, t.advise_in_date, t.new_advise_in_date,
        t.newest_advise_in_date, t.plan_follow_date, t.actual_follow_date, t.plan_remind_date, t.actual_remind_date,
        t.SA_ID, t.SA_NAME, t.follow_status, t.is_book, t.book_no, t.order_status, t.data_sources, t.is_deleted,
        t.is_valid, t.created_at, t.updated_at, t.sex, t.age, t.model, t.part_item_rule_id, t.last_change_date,
        t.dealer_code,t.item_code,t.item_name,t.item_type,t.first_follow_date,t.order_finish_date,t.content,t.lose_reason,
        t.insurance_type,t.insurance_bill_id,t.clue_type,t.new_insure_no,t.insurance_name
    </sql>

    <!-- 保险跟进 查询-->
    <!-- case when insurance_type = 81761001 then 81761002
             when insurance_type = 81761002 then 81761003
             when insurance_type = 81761003 then 81761003
             when insurance_type = 81761004 then 81761003
        else null end as  insurance_type,

        case when t.item_type = 10041001 then 81761003
        else 81761004 end as insurance_type,
        -->
    <select id="selectFollowInsureRecord" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordPO">
        SELECT
        <include refid="Base_Column_List"/>,f.sa_id as LAST_SA_ID,f.sa_name as LAST_SA_NAME,
        ext.factory_insurance_expiry_date,
        ext.advance_issuance_date,
        ext.suggested_care_date,
        ext.clue_issuance_date,
        ext.clue_data_source,
        ext.clue_issuance_type,
        ext.has_instore_upgrade,
        ext.instore_upgrade_time,
        ext.clue_generator,
        ext.has_instore_modified,
        ext.instore_modification_time,
        ext.insurance_no,
        ext.insurance_name insuranceNames,
        ext.policy_creation_date,
        ext.policy_source,
        ext.policy_effective_date,
        ext.policy_expiration_date,
        ext.owner_type,
        ext.new_insurance_no,
        ext.new_insurance_name,
        ext.new_policy_creation_date,
        ext.new_policy_source,
        ext.new_policy_effective_date,
        ext.commercial_insurance_type,
        ext.new_commercial_insurance_type,
        case when tib.insurance_status is not null then 80721001
        else '' end as insurance_status,
        case when tib.insurance_status is not null then tib.is_joint_guarantee
        else null end as is_joint_guarantee,

        (
        SELECT count(invite_id)
        FROM tt_invite_insurance_vehicle_record_detail trd
        where trd.invite_id = t.id
        ) follow_total,

        if( (select count(1) from tt_insurance_bill v  where v.is_deleted=0 and dealer_code=t.dealer_code and vin=t.vin and v.insurance_status in (80721001,80721003)) > 0,10041001,10041002 ) Is_Insure_Success,
        datediff(t.advise_in_date,now()) as advise_date_interval,
        datediff(t.plan_follow_date,now()) as plan_date_interval


        FROM tt_invite_insurance_vehicle_record t

        left join tt_invite_insurance_vehicle_sa_ref f on t.vin=f.vin and t.dealer_code=f.dealer_code
        left join
        tt_invite_insurance_vehicle_record_ext ext on t.id = ext.clue_id
        left join (
        select vin, dealer_code, insurance_status,
        case when is_joint_guarantee=10041001
        and SYSDATE() <![CDATA[<=]]> DATE_ADD(created_at,INTERVAL joint_guarantee_year_limit YEAR) then 10041001
        else 10041002 end as is_joint_guarantee
        from tt_insurance_bill b1
        where b1.id = (
        SELECT id from tt_insurance_bill b2 where
        b2.vin = b1.vin and b2.dealer_code = b1.dealer_code ORDER BY b2.created_at desc limit 1
        )
        )
        tib on tib.vin = t.vin and tib.dealer_code = t.dealer_code
        WHERE
        t.is_main=1 and t.is_deleted = 0
        and t.invite_type = 82381003
        <!--and t.order_status != 83681005 -->
        <if test=" params.followStatusParam !=null and params.followStatusParam.size > 0 ">
            AND t.follow_status in
            <foreach collection="params.followStatusParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.insuranceType !=null ">
            and t.insurance_type = #{params.insuranceType}
        </if>
        <if test=" params.clueType !=null ">
            and t.clue_type = #{params.clueType}
        </if>
        <if test=" params.orderStatus !=null ">
            and t.order_status = #{params.orderStatus}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.planFollowDateStart !=null and params.planFollowDateStart != '' ">
            AND t.plan_follow_date &gt;= CONCAT(#{params.planFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.planFollowDateEnd !=null and params.planFollowDateEnd != '' ">
            AND t.plan_follow_date &lt;= CONCAT(#{params.planFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num = #{params.licensePlateNum}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.actualFollowDateStart !=null and params.actualFollowDateStart != '' ">
            AND t.actual_follow_date &gt;= CONCAT(#{params.actualFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.actualFollowDateEnd !=null and params.actualFollowDateEnd != '' ">
            AND t.actual_follow_date &lt;= CONCAT(#{params.actualFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name like "%" #{params.name}"%"
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.SA_ID = #{params.saId}
        </if>
        <if test=" params.saName != null and params.saName != '' ">
            AND t.SA_NAME like "%" #{params.saName}"%"
        </if>
        <if test=" params.adviseInDateStart !=null and params.adviseInDateStart != '' ">
            AND t.advise_in_date &gt;= CONCAT(#{params.adviseInDateStart},' 00:00:00')
        </if>
        <if test=" params.adviseInDateEnd !=null and params.adviseInDateEnd != '' ">
            AND t.advise_in_date &lt;= CONCAT(#{params.adviseInDateEnd},' 23:59:59')
        </if>
        <if test=" params.insuranceName !=null and params.insuranceName != '' ">
            AND ext.insurance_name like CONCAT(#{params.insuranceName}, '%')
        </if>

        <if test=" params.factoryInsuranceExpiryDateStart !=null and params.factoryInsuranceExpiryDateStart != '' ">
            AND ext.factory_insurance_expiry_date &gt;= CONCAT(#{params.factoryInsuranceExpiryDateStart},' 00:00:00')
        </if>
        <if test=" params.factoryInsuranceExpiryDateEnd !=null and params.factoryInsuranceExpiryDateEnd != '' ">
            AND ext.factory_insurance_expiry_date &lt;= CONCAT(#{params.factoryInsuranceExpiryDateEnd},' 23:59:59')
        </if>
        <if test=" params.suggestedCareDateStart !=null and params.suggestedCareDateStart != '' ">
            AND ext.suggested_care_date &gt;= CONCAT(#{params.suggestedCareDateStart},' 00:00:00')
        </if>
        <if test=" params.suggestedCareDateEnd !=null and params.suggestedCareDateEnd != '' ">
            AND ext.suggested_care_date &lt;= CONCAT(#{params.suggestedCareDateEnd},' 23:59:59')
        </if>
        <if test=" params.clueDataSource != null and params.clueDataSource != '' ">
            AND ext.clue_data_source = #{params.clueDataSource}
        </if>
        <if test=" params.clueIssuanceType != null and params.clueIssuanceType != '' ">
            AND ext.clue_issuance_type = #{params.clueIssuanceType}
        </if>
        <if test=" params.hasInstoreUpgrade != null and params.hasInstoreUpgrade != '' ">
            AND ext.has_instore_upgrade = #{params.hasInstoreUpgrade}
        </if>
        <if test=" params.hasInstoreModified != null and params.hasInstoreModified != '' ">
            AND ext.has_instore_modified = #{params.hasInstoreModified}
        </if>
        order by case when t.plan_follow_date is null then t.advise_in_date else t.plan_follow_date end asc
    </select>

    <select id="selectInsuranceBill" resultType="com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InsuranceBillDTO">
        select
        id,
		dealer_code as dealerCode,
		insurance_no as insuranceNo,
		insurance_type as insuranceType,
		insurance_status as insuranceStatus,
		license_no as licenseNo,
		vin,
		is_deleted as isDeleted,
		is_valid as isValid
        FROM tt_insurance_bill t
        where 1=1
        and t.is_deleted = 0
        and t.dealer_code=#{dealerCode}
        and t.vin = #{vin}
        AND t.insurance_status in (80721001,80721003)
    </select>

    <select id="exportExcelFollowInsure" resultType="map">
        SELECT
        t.dealer_code as dealerCode,
        t.name,
        t.vin,
        t.license_plate_num as licensePlateNum,
        case when t.clue_type=1 then '交强险'
        else '商业险' end as clueType,
        t.advise_in_date as adviseInDate,
        t.new_advise_in_date as newAdviseInDate,
        t.newest_advise_in_date as newestAdviseInDate,
        t.plan_follow_date as planFollowDate,
        t.actual_follow_date as actualFollowDate,
        t.SA_NAME as saName,
        case when t.follow_status=82401001 then '未跟进'
        when t.follow_status=82401002 then '成功跟进'
        when t.follow_status=82401003 then '失败跟进'
        when t.follow_status=82401004 then '继续跟进'
        when t.follow_status=82401005 then '不需跟进'
        else '' end as followStatusName,
        case when t.is_book=0 then '否'
        when t.is_book=1 then '是'
        else '' end as isBook,
        case when t.order_status=83681001 then '未完成'
        when t.order_status=83681002 then '完成'
        when t.order_status=83681003 then '流失客户'
        when t.order_status=83681004 then '他店进厂'
        when t.order_status=83681005 then '关闭'
        else '' end as orderStatus,f.sa_name as lastSaName,t.created_at as createdAt,
        case when tib.insurance_status is not null then '未完成'
        else '' end as insuranceStatus,
        case
        when t.insurance_type = 81761002 then '新转续'
        when t.insurance_type = 81761003 then '续转续'
        else '在修不在保' end as insuranceType,
        case when tib.insurance_status is not null and tib.is_joint_guarantee=10041001 then '是'
        when tib.insurance_status is not null and tib.is_joint_guarantee=10041002 then '否'
        else '' end as isJointGuarantee,
        t.content as content,
        t.lose_reason as loseReason,
        trd.follow_total as followTotal
        FROM
        tt_invite_insurance_vehicle_record t
        left join (
        select invite_id,count(invite_id) as follow_total from tt_invite_insurance_vehicle_record_detail
        group by invite_id
        ) trd on trd.invite_id = t.id
        left join tt_invite_insurance_vehicle_sa_ref f on t.vin=f.vin and t.dealer_code=f.dealer_code
        left join (
        select vin, dealer_code, insurance_status,
        case when is_joint_guarantee=10041001
        and SYSDATE() <![CDATA[<=]]> DATE_ADD(created_at,INTERVAL joint_guarantee_year_limit YEAR) then 10041001
        else 10041002 end as is_joint_guarantee
        from tt_insurance_bill b1
        where b1.id = (
        SELECT id from tt_insurance_bill b2 where
        b2.vin = b1.vin and b2.dealer_code = b1.dealer_code ORDER BY b2.created_at desc limit 1
        )
        )
        tib on tib.vin = t.vin and tib.dealer_code = t.dealer_code
        WHERE
        t.is_main=1
        and t.invite_type = 82381003
        <if test=" params.followStatusParam !=null and params.followStatusParam.size > 0 ">
            AND t.follow_status in
            <foreach collection="params.followStatusParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.insuranceType !=null ">
            and t.insurance_type = #{params.insuranceType}
        </if>
        <if test=" params.clueType !=null ">
            and t.clue_type = #{params.clueType}
        </if>
        <if test=" params.orderStatus !=null ">
            and t.order_status = #{params.orderStatus}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.planFollowDateStart !=null and params.planFollowDateStart != '' ">
            AND t.plan_follow_date &gt;= CONCAT(#{params.planFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.planFollowDateEnd !=null and params.planFollowDateEnd != '' ">
            AND t.plan_follow_date &lt;= CONCAT(#{params.planFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num like "%" #{params.licensePlateNum}"%"
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin like "%" #{params.vin}"%"
        </if>
        <if test=" params.actualFollowDateStart !=null and params.actualFollowDateStart != '' ">
            AND t.actual_follow_date &gt;= CONCAT(#{params.actualFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.actualFollowDateEnd !=null and params.actualFollowDateEnd != '' ">
            AND t.actual_follow_date &lt;= CONCAT(#{params.actualFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name like "%" #{params.name}"%"
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.SA_ID = #{params.saId}
        </if>
        <if test=" params.saName != null and params.saName != '' ">
            AND t.SA_NAME like "%" #{params.saName}"%"
        </if>
        <if test=" params.adviseInDateStart !=null and params.adviseInDateStart != '' ">
            AND t.advise_in_date &gt;= CONCAT(#{params.adviseInDateStart},' 00:00:00')
        </if>
        <if test=" params.adviseInDateEnd !=null and params.adviseInDateEnd != '' ">
            AND t.advise_in_date &lt;= CONCAT(#{params.adviseInDateEnd},' 23:59:59')
        </if>
        order by case when t.plan_follow_date is null then t.advise_in_date else t.plan_follow_date end asc
    </select>


    <update id="updateInsureFollowStatus">
        update tt_invite_insurance_vehicle_record t
        set t.order_status = 83681003,
        t.updated_at = SYSDATE(),
        t.updated_by = #{userId}
        where 1=1
        <if test=" idList !=null and idList.size() > 0 ">
            and t.id in
            <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="updateInsureFollowTaskStatus">
        update tt_invite_insurance_vehicle_task tv
        set
        tv.close_times = tv.close_times + 1,
        tv.updated_at = SYSDATE(),
        tv.updated_by = #{userId}
        where 1=1
        <if test=" idList !=null and idList.size() > 0 ">
            and tv.invite_id in
            <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <!-- t.vin in (select DISTINCT vin from
        tt_insurance_bill where NOW() <![CDATA[>]]> date_add(vi_finish_date, INTERVAL 30 DAY) and is_deleted = 0) -->
    <select id="selectInsuranceInvitePlan" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_insurance_vehicle_record t
        left join
        tt_invite_insurance_vehicle_record_detail b on t.id=b.invite_id
        where
        t.invite_type=82381003
        and t.follow_status != 82401002
        and t.order_status = 83681001
        and NOW() <![CDATA[>]]> date_add(t.advise_in_date, INTERVAL 30 DAY)
        and (b.lose_reason != 81851006 or b.invite_id is null)
        and
        not exists (select 1 from tt_insurance_bill tb where tb.vin = t.vin and tb.is_deleted = 0)
    </select>

    <insert id="insertInsureInviteTask">
        INSERT INTO tt_invite_insurance_vehicle_task (vin, license_plate_num, owner_code, dealer_code, name, tel,
        age, sex, model, daily_mileage, advise_in_date_update_time, invite_type, day_in_advance,
        remind_interval,close_interval,item_type,
        insurance_type,insurance_bill_id,clue_type,new_insure_no,
        advise_in_date,
        create_invite_time,
        is_create_invite,
        close_times,
        data_sources,
        created_at,created_by)
        select vin, license_plate_num, owner_code, dealer_code, name, tel,
        age, sex, model, daily_mileage, advise_in_date_update_time, invite_type, day_in_advance,
        remind_interval,close_interval,
        10041002,
        insurance_type,insurance_bill_id,clue_type,new_insure_no,
        date_add(advise_in_date, INTERVAL 1 YEAR),
        date_add(create_invite_time, INTERVAL 1 YEAR),
        0,
        0,
        data_sources,
        CURRENT_TIMESTAMP, #{userId}
        from tt_invite_insurance_vehicle_task where 1=1
        <if test=" idList !=null and idList.size() > 0 ">
            and invite_id in
            <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </insert>

    <select id="selectInviteInsuranceVehicleRecord" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordPO">
        SELECT
        <include refid="Base_Column_List"/>,f.sa_id as LAST_SA_ID,f.sa_name as
        LAST_SA_NAME,datediff(t.advise_in_date,now()) as date_interval,
        -- calld.total_score,calld.call_length,calld.id as call_detail_id,calld.start_time as call_time,
        task.close_interval,
        ext.insurance_name insuranceNames
        FROM tt_invite_insurance_vehicle_record t
        left join tt_invite_insurance_vehicle_task task on t.id=task.invite_id  and t.clue_type=task.clue_type
        left join tt_invite_insurance_vehicle_sa_ref f on t.vin=f.vin and t.dealer_code=f.dealer_code
        left join
        tt_invite_insurance_vehicle_record_ext ext on t.id = ext.clue_id
--         left join (
--         SELECT
--         a.total_score,
--         b.insurance_id as invite_id,
--         a.call_length,
--         a.start_time,
--         a.id
--         FROM
--         tt_call_details a
--         INNER join
--         tt_invite_insurance_vehicle_customer_number b
--         on a.call_id = b.call_id
--         WHERE not exists(
--         select 1 from tt_call_details x
--         INNER join tt_invite_insurance_vehicle_customer_number y on x.call_id = y.call_id
--         where
--         y.insurance_id=b.insurance_id
--         and (x.total_score>a.total_score or (x.total_score=a.total_score and x.id>a.id)))
--         ) calld on calld.invite_id=t.id
        WHERE 1=1
        and t.is_deleted = 0
        and t.is_main=1
        and t.invite_type = 82381003 and t.order_status != 83681005
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.dealerCodes !=null and params.dealerCodes.size > 0">
            AND t.dealer_code in
            <foreach collection="params.dealerCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.planFollowDateStart !=null and params.planFollowDateStart != '' ">
            AND t.plan_follow_date &gt;= CONCAT(#{params.planFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.planFollowDateEnd !=null and params.planFollowDateEnd != '' ">
            AND t.plan_follow_date &lt;= CONCAT(#{params.planFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num like "%" #{params.licensePlateNum}"%"
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin like "%" #{params.vin}"%"
        </if>
        <if test=" params.actualFollowDateStart !=null and params.actualFollowDateStart != '' ">
            AND t.actual_follow_date &gt;= CONCAT(#{params.actualFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.actualFollowDateEnd !=null and params.actualFollowDateEnd != '' ">
            AND t.actual_follow_date &lt;= CONCAT(#{params.actualFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.adviseInDateStart !=null and params.adviseInDateStart != '' ">
            AND t.advise_in_date &gt;= CONCAT(#{params.adviseInDateStart},' 00:00:00')
        </if>
        <if test=" params.adviseInDateEnd !=null and params.adviseInDateEnd != '' ">
            AND t.advise_in_date &lt;= CONCAT(#{params.adviseInDateEnd},' 23:59:59')
        </if>
        <if test=" params.followStatusParam !=null and params.followStatusParam.size > 0 ">
            AND t.follow_status in
            <foreach collection="params.followStatusParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.isNoDistribute !=null and params.isNoDistribute != '' ">
            and (
            <if test=" params.leaveIds !=null and params.leaveIds.size > 0 ">
                t.sa_id in
                <foreach collection="params.leaveIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or
            </if>
            not exists(select 1 from tt_invite_insurance_sa_allocate_rule_detail a where a.sa_id=t.sa_id and a.dealer_code=t.dealer_code)
            )
        </if>
        <if test=" params.orderStatus !=null and params.orderStatus != '' ">
            AND t.order_status = #{params.orderStatus}
        </if>
        <if test=" params.clueType !=null and params.clueType != '' ">
            AND t.clue_type = #{params.clueType}
        </if>
        <if test=" params.saName !=null and params.saName != '' ">
            AND t.SA_NAME like "%" #{params.saName}"%"
        </if>
        <if test=" params.createdAtStart !=null and params.createdAtStart != '' ">
            AND t.created_at &gt;= CONCAT(#{params.createdAtStart},' 00:00:00')
        </if>
        <if test=" params.createdAtEnd !=null and params.createdAtEnd != '' ">
            AND t.created_at &lt;= CONCAT(#{params.createdAtEnd},' 23:59:59')
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.sa_id = #{params.saId}
        </if>
        <if test=" params.isNoDistribute !=null and params.isNoDistribute != '' ">
            AND t.sa_id is null
        </if>
        <if test=" params.insuranceName !=null and params.insuranceName != '' ">
            AND ext.insurance_name like CONCAT(#{params.insuranceName}, '%')
        </if>
        <if test=" params.insuranceTypeList !=null and params.insuranceTypeList.size > 0">
            AND t.insurance_type in
            <foreach collection="params.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by case when t.plan_follow_date is null then t.advise_in_date else t.plan_follow_date end asc
    </select>

    <!-- and t.follow_status = 82401001 -->
    <select id="getNeedDistribute" resultType="java.lang.Integer">
        SELECT count(id)
        FROM tt_invite_insurance_vehicle_record t
        WHERE 1=1
        and t.is_deleted = 0
        and t.is_main=1
        and t.order_status = 83681001
        and t.invite_type = 82381003
        AND t.follow_status in ( 82401001, 82401004 )
        AND t.dealer_code=#{dealerCode}
        and (
        <if test=" leaveIds !=null and leaveIds.size > 0 ">
            t.sa_id in
            <foreach collection="leaveIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            or
        </if>
        not exists(select 1 from tt_invite_insurance_sa_allocate_rule_detail a where a.sa_id=t.sa_id and a.dealer_code=t.dealer_code)
        )
        AND t.sa_id is null
    </select>

    <select id="queryWaitAllocationRecodeForInsurance" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,f.sa_id as LAST_SA_ID,f.sa_name as LAST_SA_NAME
        FROM tt_invite_insurance_vehicle_record t
        left join tt_invite_insurance_vehicle_sa_ref f on t.vin=f.vin and t.dealer_code=f.dealer_code
        where 1=1
        and t.source_type=1
        and t.is_main = 1
        and t.sa_id is null
        and t.invite_type =82381003
        and t.created_at&gt;=CONCAT(#{createDate},' 00:00:00')
        and t.created_at&lt;=CONCAT(#{createDate},' 23:59:59')
    </select>

    <select id="selectInsuranceVehicleRecordAndSubcues" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,datediff(t.advise_in_date,now()) as date_interval,
        k.advise_in_mileage,k.invite_time
        FROM tt_invite_insurance_vehicle_record t
        left join tt_invite_insurance_vehicle_task k on t.id=k.invite_id
        where t.id=#{id}
    </select>



    <select id="selectFollowInsureRecordVCDC" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordPO">
        SELECT
        <include refid="Base_Column_List"/>,
        -- f.sa_id as LAST_SA_ID,f.sa_name as LAST_SA_NAME,
--         case when tib.insurance_status is not null then 80721001
--         else '' end as insurance_status,
--         case when tib.insurance_status is not null then tib.is_joint_guarantee
--         else null end as is_joint_guarantee,
        -- trd.follow_total,
        calls.total_score,
        calls.call_length,
        calls.start_time,
        calls.id callDetailId
        FROM tt_invite_insurance_vehicle_record t
--         left join (
--         select invite_id,count(invite_id) as follow_total from tt_invite_insurance_vehicle_record_detail
--         group by invite_id
--         ) trd on trd.invite_id = t.id
        -- left join tt_invite_insurance_vehicle_sa_ref f on t.vin=f.vin and t.dealer_code=f.dealer_code
--         left join (
--         select vin, dealer_code, insurance_status,
--         case when is_joint_guarantee=10041001
--         and SYSDATE() <![CDATA[<=]]> DATE_ADD(created_at,INTERVAL joint_guarantee_year_limit YEAR) then 10041001
--         else 10041002 end as is_joint_guarantee
--         from tt_insurance_bill b1
--         where b1.id = (
--         SELECT id from tt_insurance_bill b2 where
--         b2.vin = b1.vin and b2.dealer_code = b1.dealer_code ORDER BY b2.created_at desc limit 1
--         )
--         )
--         tib on tib.vin = t.vin and tib.dealer_code = t.dealer_code
        left join (
            SELECT
            a.total_score,
            b.insurance_id,
            a.call_length,
            a.start_time,
            a.id
            FROM
            tt_call_details a
            INNER JOIN tt_invite_insurance_vehicle_customer_number b ON a.call_id = b.call_id
            WHERE
            NOT EXISTS (
            SELECT
            1
            FROM
            tt_call_details x
            INNER JOIN tt_invite_insurance_vehicle_customer_number y ON x.call_id = y.call_id
            WHERE
            y.insurance_id = b.insurance_id
            AND (
            ifnull( x.total_score, 0 )> ifnull( a.total_score, 0 )
            OR ( ifnull( x.total_score, 0 )= ifnull( a.total_score, 0 ) AND x.id > a.id )
            )
            )
        ) calls on calls.insurance_id=t.id
        WHERE
        t.is_main=1
        and t.invite_type = 82381003
        <if test=" params.followStatusParam !=null and params.followStatusParam.size > 0 ">
            AND t.follow_status in
            <foreach collection="params.followStatusParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.insuranceType !=null ">
            and t.insurance_type = #{params.insuranceType}
        </if>
        <if test=" params.clueType !=null ">
            and t.clue_type = #{params.clueType}
        </if>
        <if test=" params.orderStatus !=null ">
            and t.order_status = #{params.orderStatus}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.planFollowDateStart !=null and params.planFollowDateStart != '' ">
            AND t.plan_follow_date &gt;= CONCAT(#{params.planFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.planFollowDateEnd !=null and params.planFollowDateEnd != '' ">
            AND t.plan_follow_date &lt;= CONCAT(#{params.planFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num like "%" #{params.licensePlateNum}"%"
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin like "%" #{params.vin}"%"
        </if>
        <if test=" params.actualFollowDateStart !=null and params.actualFollowDateStart != '' ">
            AND t.actual_follow_date &gt;= CONCAT(#{params.actualFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.actualFollowDateEnd !=null and params.actualFollowDateEnd != '' ">
            AND t.actual_follow_date &lt;= CONCAT(#{params.actualFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name like "%" #{params.name}"%"
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.SA_ID = #{params.saId}
        </if>
        <if test=" params.adviseInDateStart !=null and params.adviseInDateStart != '' ">
            AND t.advise_in_date &gt;= CONCAT(#{params.adviseInDateStart},' 00:00:00')
        </if>
        <if test=" params.adviseInDateEnd !=null and params.adviseInDateEnd != '' ">
            AND t.advise_in_date &lt;= CONCAT(#{params.adviseInDateEnd},' 23:59:59')
        </if>
        <if test=" params.createdAtStart != null and params.createdAtStart != '' ">
            AND t.created_at >= #{params.createdAtStart}
        </if>
        <if test=" params.createdAtEnd != null and params.createdAtEnd != '' ">
            AND t.created_at &lt;= CONCAT( #{params.createdAtStart},' 23:59:59')
        </if>
        <if test=" params.dealerCodes !=null and params.dealerCodes.size > 0">
            AND t.dealer_code in
            <foreach collection="params.dealerCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by case when t.plan_follow_date is null then t.advise_in_date else t.plan_follow_date end asc
    </select>





    <select id="exportExcel" resultType="Map"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordPO">
        SELECT
        t.dealer_code as dealerCode,
        t.name,
        t.vin,
        t.license_plate_num as licensePlateNum,
        case when t.clue_type=1 then '交强险'
        else '商业险' end as clueType,
        DATE_FORMAT(t.advise_in_date,"%Y-%m-%d %H:%i:%S") adviseInDate,
        t.new_advise_in_date as newAdviseInDate,
        t.newest_advise_in_date as newestAdviseInDate,
        DATE_FORMAT(t.plan_follow_date,"%Y-%m-%d %H:%i:%S") planFollowDate,
        DATE_FORMAT(t.actual_follow_date,"%Y-%m-%d %H:%i:%S") actualFollowDate,
        t.SA_NAME as saName,
        case when t.follow_status=82401001 then '未跟进'
        when t.follow_status=82401002 then '成功跟进'
        when t.follow_status=82401003 then '失败跟进'
        when t.follow_status=82401004 then '继续跟进'
        when t.follow_status=82401005 then '不需跟进'
        else '' end as followStatusName,
        case when t.is_book=0 then '否'
        when t.is_book=1 then '是'
        else '' end as isBook,
        case when t.order_status=83681001 then '未完成'
        when t.order_status=83681002 then '完成'
        when t.order_status=83681003 then '流失客户'
        when t.order_status=83681004 then '他店进厂'
        when t.order_status=83681005 then '关闭'
        else '' end as orderStatus,
        -- f.sa_name as lastSaName,
        t.created_at as createdAt,
--         case when tib.insurance_status is not null then '未完成'
--         else '' end as insuranceStatus,
        case
        when t.insurance_type = 81761002 then '新转续'
        when t.insurance_type = 81761003 then '续转续'
        else '在修不在保' end as insuranceType,
--         case when tib.insurance_status is not null and tib.is_joint_guarantee=10041001 then '是'
--         when tib.insurance_status is not null and tib.is_joint_guarantee=10041002 then '否'
--         else '' end as isJointGuarantee,
        t.content as content,
        t.lose_reason as loseReason,
        -- f.sa_id as LAST_SA_ID,f.sa_name as LAST_SA_NAME,
--         case when tib.insurance_status is not null then 80721001
--         else '' end as insurance_status,
--         case when tib.insurance_status is not null then tib.is_joint_guarantee
--         else null end as is_joint_guarantee,
        -- trd.follow_total,
        calls.total_score,
        calls.call_length,
        calls.start_time
        FROM tt_invite_insurance_vehicle_record t
--         left join (
--         select invite_id,count(invite_id) as follow_total from tt_invite_insurance_vehicle_record_detail
--         group by invite_id
--         ) trd on trd.invite_id = t.id
       -- left join tt_invite_insurance_vehicle_sa_ref f on t.vin=f.vin and t.dealer_code=f.dealer_code
--         left join (
--         select vin, dealer_code, insurance_status,
--         case when is_joint_guarantee=10041001
--         and SYSDATE() <![CDATA[<=]]> DATE_ADD(created_at,INTERVAL joint_guarantee_year_limit YEAR) then 10041001
--         else 10041002 end as is_joint_guarantee
--         from tt_insurance_bill b1
--         where b1.id = (
--         SELECT id from tt_insurance_bill b2 where
--         b2.vin = b1.vin and b2.dealer_code = b1.dealer_code ORDER BY b2.created_at desc limit 1
--         )
--         )
--         tib on tib.vin = t.vin and tib.dealer_code = t.dealer_code
        left join (
        SELECT
        a.total_score,
        b.insurance_id,
        a.call_length,
        a.start_time,
        a.id
        FROM
        tt_call_details a
        INNER JOIN tt_invite_insurance_vehicle_customer_number b ON a.call_id = b.call_id
        WHERE
        NOT EXISTS (
        SELECT
        1
        FROM
        tt_call_details x
        INNER JOIN tt_invite_insurance_vehicle_customer_number y ON x.call_id = y.call_id
        WHERE
        y.insurance_id = b.insurance_id
        AND (
        ifnull( x.total_score, 0 )> ifnull( a.total_score, 0 )
        OR ( ifnull( x.total_score, 0 )= ifnull( a.total_score, 0 ) AND x.id > a.id )
        )
        )
        ) calls on calls.insurance_id=t.id
        WHERE
        t.is_main=1
        and t.invite_type = 82381003
        <if test=" params.followStatusParam !=null and params.followStatusParam.size > 0 ">
            AND t.follow_status in
            <foreach collection="params.followStatusParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.insuranceType !=null ">
            and t.insurance_type = #{params.insuranceType}
        </if>
        <if test=" params.clueType !=null ">
            and t.clue_type = #{params.clueType}
        </if>
        <if test=" params.orderStatus !=null ">
            and t.order_status = #{params.orderStatus}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.planFollowDateStart !=null and params.planFollowDateStart != '' ">
            AND t.plan_follow_date &gt;= CONCAT(#{params.planFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.planFollowDateEnd !=null and params.planFollowDateEnd != '' ">
            AND t.plan_follow_date &lt;= CONCAT(#{params.planFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num like "%" #{params.licensePlateNum}"%"
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin like "%" #{params.vin}"%"
        </if>
        <if test=" params.actualFollowDateStart !=null and params.actualFollowDateStart != '' ">
            AND t.actual_follow_date &gt;= CONCAT(#{params.actualFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.actualFollowDateEnd !=null and params.actualFollowDateEnd != '' ">
            AND t.actual_follow_date &lt;= CONCAT(#{params.actualFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name like "%" #{params.name}"%"
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.SA_ID = #{params.saId}
        </if>
        <if test=" params.adviseInDateStart !=null and params.adviseInDateStart != '' ">
            AND t.advise_in_date &gt;= CONCAT(#{params.adviseInDateStart},' 00:00:00')
        </if>
        <if test=" params.adviseInDateEnd !=null and params.adviseInDateEnd != '' ">
            AND t.advise_in_date &lt;= CONCAT(#{params.adviseInDateEnd},' 23:59:59')
        </if>
        <if test=" params.createdAtStart != null and params.createdAtStart != '' ">
            AND t.created_at >= #{params.createdAtStart}
        </if>
        <if test=" params.createdAtEnd != null and params.createdAtEnd != '' ">
            AND t.created_at &lt;= CONCAT( #{params.createdAtStart},' 23:59:59')
        </if>
        <if test=" params.dealerCodes !=null and params.dealerCodes.size > 0">
            AND t.dealer_code in
            <foreach collection="params.dealerCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by case when t.plan_follow_date is null then t.advise_in_date else t.plan_follow_date end asc
    </select>

    <select id="selectLatelyCall"  resultMap="BaseResultMap">
          SELECT
            a.total_score,
            a.call_length,
            a.start_time,
            a.id as call_detail_id
            FROM
            tt_call_details a
            INNER JOIN tt_invite_insurance_vehicle_customer_number b ON a.call_id = b.call_id
            WHERE
            NOT EXISTS (
            SELECT
            1
            FROM
            tt_call_details x
            INNER JOIN tt_invite_insurance_vehicle_customer_number y ON x.call_id = y.call_id
            WHERE
            y.insurance_id = b.insurance_id
            AND (
            ifnull( x.total_score, 0 )> ifnull( a.total_score, 0 )
            OR ( ifnull( x.total_score, 0 )= ifnull( a.total_score, 0 ) AND x.id > a.id )
            )
            and y.insurance_id=#{insuranceId}
           ) and b.insurance_id=#{insuranceId}

    </select>

    <select id="selectLatelyCalls"  resultMap="BaseResultMap">
        SELECT
            a.total_score,
            a.call_length,
            a.start_time,
            a.id as call_detail_id,
            b.insurance_id
        FROM
        tt_call_details a
        INNER JOIN tt_invite_insurance_vehicle_customer_number b ON a.call_id = b.call_id
        WHERE
        NOT EXISTS
        (
            SELECT
                1
            FROM
            tt_call_details x
            INNER JOIN tt_invite_insurance_vehicle_customer_number y ON x.call_id = y.call_id
            WHERE
                y.insurance_id = b.insurance_id
            AND
            (
                ifnull( x.total_score, 0 ) > ifnull( a.total_score, 0 )
                OR ( ifnull( x.total_score, 0 ) = ifnull( a.total_score, 0 ) AND x.id > a.id )
            )
            and y.insurance_id in
            <foreach collection="insuranceIds" item="insuranceId" open="(" separator="," close=")">
                #{insuranceId}
            </foreach>
        )
        and b.insurance_id in
        <foreach collection="insuranceIds" item="insuranceId" open="(" separator="," close=")">
            #{insuranceId}
        </foreach>
    </select>

    <select id="selectLossOfRecord" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_insurance_vehicle_record t
        where
        t.invite_type=82381003 and t.is_deleted = 0
        and t.follow_status in (82401001,82401004)
        and t.order_status = 83681001
        and NOW() > date_add(t.advise_in_date, INTERVAL 30 DAY)
        and
        not exists (select 1 from tt_insurance_bill tb where tb.vin = t.vin and tb.is_deleted = 0
				and YEAR(tb.created_at)=YEAR(t.advise_in_date))
    </select>


    <update id="updateRecordOrderStatus" >
         update tt_invite_insurance_vehicle_record set order_status = 83681003,updated_at=now()
         where id in (
          select id from (
         SELECT
               t.id
                FROM tt_invite_insurance_vehicle_record t
                where
                t.invite_type=82381003
                and t.follow_status in (82401001,82401004)
                and t.order_status = 83681001
                and NOW() > date_add(t.advise_in_date, INTERVAL 30 DAY)
                and
                not exists (select 1 from tt_insurance_bill tb where tb.vin = t.vin and tb.is_deleted = 0
                        and YEAR(tb.created_at)=YEAR(t.advise_in_date))
                        ) a
        )
    </update>
    
    <update id="updateRecordStatusClose">
         update tt_invite_insurance_vehicle_record set order_status = 83681006,updated_at=now()
         where vin=#{vin} and dealer_code=#{dealerCode}
    </update>



    <select id="selectInviteInsuranceVehicleRecordCPort" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_insurance_vehicle_record t
        WHERE 1=1 and t.is_main=1
        and t.invite_type = 82381003
        and t.order_status != 83681005
        and t.follow_status in (82401001,82401004)
        AND t.clue_type = 1
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.dealerCodes !=null and params.dealerCodes.size > 0">
            AND t.dealer_code in
            <foreach collection="params.dealerCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.planFollowDateStart !=null and params.planFollowDateStart != '' ">
            AND t.plan_follow_date &gt;= CONCAT(#{params.planFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.planFollowDateEnd !=null and params.planFollowDateEnd != '' ">
            AND t.plan_follow_date &lt;= CONCAT(#{params.planFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num like "%" #{params.licensePlateNum}"%"
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin like "%" #{params.vin}"%"
        </if>
        <if test=" params.actualFollowDateStart !=null and params.actualFollowDateStart != '' ">
            AND t.actual_follow_date &gt;= CONCAT(#{params.actualFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.actualFollowDateEnd !=null and params.actualFollowDateEnd != '' ">
            AND t.actual_follow_date &lt;= CONCAT(#{params.actualFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.adviseInDateStart !=null and params.adviseInDateStart != '' ">
            AND t.advise_in_date &gt;= CONCAT(#{params.adviseInDateStart},' 00:00:00')
        </if>
        <if test=" params.adviseInDateEnd !=null and params.adviseInDateEnd != '' ">
            AND t.advise_in_date &lt;= CONCAT(#{params.adviseInDateEnd},' 23:59:59')
        </if>
        <if test=" params.isNoDistribute !=null and params.isNoDistribute != '' ">
            and (
            <if test=" params.leaveIds !=null and params.leaveIds.size > 0 ">
                t.sa_id in
                <foreach collection="params.leaveIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or
            </if>
            not exists(select 1 from tt_invite_insurance_sa_allocate_rule_detail a where a.sa_id=t.sa_id and a.dealer_code=t.dealer_code)
            )
        </if>
        <if test=" params.orderStatus !=null and params.orderStatus != '' ">
            AND t.order_status = #{params.orderStatus}
        </if>
        <if test=" params.saName !=null and params.saName != '' ">
            AND t.SA_NAME like "%" #{params.saName}"%"
        </if>
        <if test=" params.createdAtStart !=null and params.createdAtStart != '' ">
            AND t.created_at &gt;= CONCAT(#{params.createdAtStart},' 00:00:00')
        </if>
        <if test=" params.createdAtEnd !=null and params.createdAtEnd != '' ">
            AND t.created_at &lt;= CONCAT(#{params.createdAtEnd},' 23:59:59')
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.sa_id = #{params.saId}
        </if>
        <if test=" params.isNoDistribute !=null and params.isNoDistribute != '' ">
            AND t.sa_id is null
        </if>
        order by case when t.plan_follow_date is null then t.advise_in_date else t.plan_follow_date end asc
    </select>
    <update id="updateList" parameterType="java.util.List">
        update tt_invite_insurance_vehicle_record set order_status = 83681003,updated_at=now() where  id  in
        <foreach collection="updateList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <update id="updates">
        <foreach collection="pos" item="po" separator=";">
            update tt_invite_insurance_vehicle_record
            set order_status = #{po.orderStatus},
            updated_at = #{po.updatedAt},
            updated_by = #{po.updatedBy}
            where id = #{po.id}
        </foreach>
    </update>
</mapper>
