<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.accidentClues.AccidentCluesFollowMapper">
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesFollowPO">
        <id column="follow_id" property="followId"/>
        <result column="app_Id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="follow_id" property="followId"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="ac_id" property="acId"/>
        <result column="is_insured" property="isInsured"/>
        <result column="double_accident" property="doubleAccident"/>
        <result column="duty_division" property="dutyDivision"/>
        <result column="is_report" property="isReport"/>
        <result column="is_trailer" property="isTrailer"/>
        <result column="ro_no" property="roNo"/>
        <result column="follow_type" property="followType"/>
        <result column="follow_text" property="followText"/>
        <result column="follow_status" property="followStatus"/>
        <result column="follow_fail_why" property="followFailWhy"/>
        <result column="next_follow_date" property="nextFollowDate"/>
        <result column="is_appointment" property="isAppointment"/>
        <result column="customer_appointment" property="customerAppointment"/>
        <result column="appointment_into_date" property="appointmentIntoDate"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="follow_people" property="followPeople"/>
        <result column="follow_people_name" property="followPeopleName"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.app_id,
        t.owner_code,
        t.owner_par_code,
        t.org_id,
        t.follow_id,
        t.dealer_code,
        t.ac_id,
        t.is_insured,
        t.double_accident,
        t.duty_division,
        t.is_report,
        t.is_trailer,
        t.ro_no,
        t.follow_type,
        t.follow_text,
        t.follow_status,
        t.follow_fail_why,
        t.next_follow_date,
        t.is_appointment,
        t.customer_appointment,
        t.appointment_into_date,
        t.data_sources,
        t.is_deleted,
        t.is_valid,
        t.created_at,
        t.created_by,
        t.updated_at,
        t.updated_by,
        t.record_version,
        t.into_dealer_date,
        t.into_dealer_code,
        t.follow_people,
        t.follow_people_name
    </sql>


    <select id="selectPageBySql" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tt_accident_clues_follow t
        where t.ac_id=#{params.acId} and t.is_deleted = 0
    </select>


    <select id="selectMaxFollwByAcId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tt_accident_clues_follow t where t.ac_id = #{acId} and t.is_deleted = 0 ORDER BY created_at desc limit 1
    </select>

    <select id="getFollowList" resultType="com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesFollowDTO">
        select <include refid="Base_Column_List"/>
        ,tc.is_bruise
        from tt_accident_clues_follow t
        left join tt_accident_clues tc on t.`ac_id` = tc.`ac_id`
        where t.ac_id=#{acId} and t.is_deleted = 0
        order by t.follow_id desc
    </select>
</mapper>
