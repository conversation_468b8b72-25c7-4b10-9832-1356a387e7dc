<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillInvoiceRecordMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillInvoiceRecordPO">
          	<id column="id" property="id"/>
           	<result column="APP_ID" property="appId"/>
           	<result column="OWNER_CODE" property="ownerCode"/>
           	<result column="OWNER_PAR_CODE" property="ownerParCode"/>
           	<result column="ORG_ID" property="orgId"/>
           	<result column="goodwill_apply_id" property="goodwillApplyId"/>
           	<result column="notice_invoice_id" property="noticeInvoiceId"/>
           	<result column="invoice_no" property="invoiceNo"/>
           	<result column="invoice_price" property="invoicePrice"/>
           	<result column="invoice_date" property="invoiceDate"/>
           	<result column="invoice_type" property="invoiceType"/>
           	<result column="express_company" property="expressCompany"/>
           	<result column="express_no" property="expressNo"/>
           	<result column="express_date" property="expressDate"/>
           	<result column="received_invoice_date" property="receivedInvoiceDate"/>
           	<result column="received_invoice_price" property="receivedInvoicePrice"/>
           	<result column="invoice_id" property="invoiceId"/>
           	<result column="is_valid" property="isValid"/>
           	<result column="is_deleted" property="isDeleted"/>
           	<result column="created_at" property="createdAt"/>
           	<result column="updated_at" property="updatedAt"/>
       		<result column="created_by" property="createdBy"/>
            <result column="updated_by" property="updatedBy"/>
            <result column="record_version" property="recordVersion"/>
        </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, goodwill_apply_id, notice_invoice_id, invoice_no, invoice_price, invoice_date, invoice_type, express_company, express_no, express_date, received_invoice_date, received_invoice_price, invoice_id, is_valid, is_deleted, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillInvoiceRecordPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_invoice_record t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.noticeInvoiceId !=null and params.noticeInvoiceId != '' ">
                AND t.notice_invoice_id = #{params.noticeInvoiceId}
            </if>
                    <if test=" params.invoiceNo !=null and params.invoiceNo != '' ">
                AND t.invoice_no = #{params.invoiceNo}
            </if>
                    <if test=" params.invoicePrice !=null and params.invoicePrice != '' ">
                AND t.invoice_price = #{params.invoicePrice}
            </if>
                    <if test=" params.invoiceDate !=null and params.invoiceDate != '' ">
                AND t.invoice_date = #{params.invoiceDate}
            </if>
                    <if test=" params.invoiceType !=null and params.invoiceType != '' ">
                AND t.invoice_type = #{params.invoiceType}
            </if>
                    <if test=" params.expressCompany !=null and params.expressCompany != '' ">
                AND t.express_company = #{params.expressCompany}
            </if>
                    <if test=" params.expressNo !=null and params.expressNo != '' ">
                AND t.express_no = #{params.expressNo}
            </if>
                    <if test=" params.expressDate !=null and params.expressDate != '' ">
                AND t.express_date = #{params.expressDate}
            </if>
                    <if test=" params.receivedInvoiceDate !=null and params.receivedInvoiceDate != '' ">
                AND t.received_invoice_date = #{params.receivedInvoiceDate}
            </if>
                    <if test=" params.receivedInvoicePrice !=null and params.receivedInvoicePrice != '' ">
                AND t.received_invoice_price = #{params.receivedInvoicePrice}
            </if>
                    <if test=" params.invoiceId !=null and params.invoiceId != '' ">
                AND t.invoice_id = #{params.invoiceId}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillInvoiceRecordPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_invoice_record t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.noticeInvoiceId !=null and params.noticeInvoiceId != '' ">
                AND t.notice_invoice_id = #{params.noticeInvoiceId}
            </if>
                    <if test=" params.invoiceNo !=null and params.invoiceNo != '' ">
                AND t.invoice_no = #{params.invoiceNo}
            </if>
                    <if test=" params.invoicePrice !=null and params.invoicePrice != '' ">
                AND t.invoice_price = #{params.invoicePrice}
            </if>
                    <if test=" params.invoiceDate !=null and params.invoiceDate != '' ">
                AND t.invoice_date = #{params.invoiceDate}
            </if>
                    <if test=" params.invoiceType !=null and params.invoiceType != '' ">
                AND t.invoice_type = #{params.invoiceType}
            </if>
                    <if test=" params.expressCompany !=null and params.expressCompany != '' ">
                AND t.express_company = #{params.expressCompany}
            </if>
                    <if test=" params.expressNo !=null and params.expressNo != '' ">
                AND t.express_no = #{params.expressNo}
            </if>
                    <if test=" params.expressDate !=null and params.expressDate != '' ">
                AND t.express_date = #{params.expressDate}
            </if>
                    <if test=" params.receivedInvoiceDate !=null and params.receivedInvoiceDate != '' ">
                AND t.received_invoice_date = #{params.receivedInvoiceDate}
            </if>
                    <if test=" params.receivedInvoicePrice !=null and params.receivedInvoicePrice != '' ">
                AND t.received_invoice_price = #{params.receivedInvoicePrice}
            </if>
                    <if test=" params.invoiceId !=null and params.invoiceId != '' ">
                AND t.invoice_id = #{params.invoiceId}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>
            
            <select id="queryInvoiceInfo" resultType="map">
            SELECT
		        t.id, t.goodwill_apply_id goodwillApplyId, tt.id noticeInvoiceId, t.invoice_no invoiceNo, 
		        IFNULL(t.invoice_price,tt.notice_invoice_price) invoicePrice, t.invoice_date invoiceDate, t.invoice_type invoiceType, t.express_company expressCompany,
		        t.express_no expressNo, t.express_date expressDate, t.received_invoice_date receivedInvoiceDate,
		        t.received_invoice_price receivedInvoicePrice, t.is_valid isValid, t.is_deleted isDeleted,
		        tt.notice_invoice_date noticeInvoiceDate,tt.is_commit isCommit, tt.invoice_id invoiceId ,tt.is_confirm isConfirm
		        FROM tt_goodwill_invoice_record t
		        left join tt_goodwill_notice_invoice_info tt on t.invoice_id=tt.invoice_id
		        WHERE 1=1
             	AND tt.goodwill_apply_id = #{applyId}
            </select>
            
            <select id="queryOemInvoiceInfo" resultType="map">
            SELECT
		        t.id, t.goodwill_apply_id goodwillApplyId, tt.id noticeInvoiceId, t.invoice_no invoiceNo, 
		        t.invoice_price invoicePrice, t.invoice_date invoiceDate, t.invoice_type invoiceType, t.express_company expressCompany,
		        t.express_no expressNo, t.express_date expressDate, IFNULL(t.received_invoice_date,NOW()) receivedInvoiceDate,
		        t.received_invoice_price receivedInvoicePrice, t.is_valid isValid, t.is_deleted isDeleted,
		        tt.notice_invoice_date noticeInvoiceDate,tt.is_commit isCommit, tt.invoice_id invoiceId ,tt.is_confirm isConfirm
		        FROM tt_goodwill_invoice_record t
		        left join tt_goodwill_notice_invoice_info tt on t.invoice_id=tt.invoice_id
		        WHERE 1=1
             	AND tt.goodwill_apply_id = #{applyId}
            </select>

</mapper>
