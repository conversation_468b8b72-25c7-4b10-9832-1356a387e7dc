<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.accidentClues.AccidentCluesSaNumberMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesSaNumberPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="follow_id" property="followId"/>
        <result column="call_id" property="callId"/>
        <result column="sa_id" property="saId"/>
        <result column="cus_name" property="cusName"/>
        <result column="cus_number" property="cusNumber"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="dealer_code" property="dealerCode"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        app_id, owner_code, owner_par_code, org_id, id, follow_id, call_id, sa_id, cus_name, cus_number,
        data_sources, is_deleted, is_valid,dealer_code, created_at, updated_at
    </sql>


    <select id="selectCusList" resultMap="BaseResultMap">
        SELECT cus_name,cus_number from tt_accident_clues_sa_number
        where cus_number is not null and cus_number != ''
         and ac_id=#{acId}
        UNION
        SELECT contacts,contacts_phone from tt_accident_clues
        where contacts_phone is not null and contacts_phone != ''
        and ac_id=#{acId}
    </select>

    <select id="selectCusListB" resultType="com.yonyou.dmscus.customer.dto.AccidentCluesUserDto">
        select
            t.contacts,
            t.contacts_phone,
            ext.owner_name,
            ext.owner_mobile,
            ext2.cdp_contacts,
            ext2.cdp_contacts_phone,
            ext2.order_contacts_info
        from tt_accident_clues t
                 LEFT JOIN tt_accident_clues_ext ext on ext.`ac_id` = t.`ac_id`
                 LEFT JOIN tt_accident_clues_ext_2 ext2 on ext2.`ac_id` = t.`ac_id`
        where  t.ac_id=#{acId} and t.is_deleted = 0
    </select>

    <update id="updateFollowId">
        update tt_accident_clues_sa_number set follow_id=#{followId} where ac_id=#{acId} and (follow_id is null or follow_id = '')
    </update>
</mapper>
