<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyFinalPartInfoMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyFinalPartInfoPO">
              <id column="id" property="id"/>
              <result column="APP_ID" property="appId"/>
              <result column="OWNER_CODE" property="ownerCode"/>
              <result column="OWNER_PAR_CODE" property="ownerParCode"/>
              <result column="ORG_ID" property="orgId"/>
              <result column="goodwill_apply_id" property="goodwillApplyId"/>
              <result column="goodwill_type" property="goodwillType"/>
              <result column="part_code" property="partCode"/>
              <result column="part_name" property="partName"/>
              <result column="unit_price_no_tax" property="unitPriceNoTax"/>
              <result column="rate" property="rate"/>
              <result column="unit_price_tax" property="unitPriceTax"/>
              <result column="quantity" property="quantity"/>
              <result column="amount_tax" property="amountTax"/>
              <result column="support_proportion" property="supportProportion"/>
              <result column="goodwill_amount_tax" property="goodwillAmountTax"/>
              <result column="is_valid" property="isValid"/>
              <result column="is_deleted" property="isDeleted"/>
              <result column="created_at" property="createdAt"/>
              <result column="updated_at" property="updatedAt"/>
         	  <result column="created_by" property="createdBy"/>
              <result column="updated_by" property="updatedBy"/>
              <result column="record_version" property="recordVersion"/>
          </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, goodwill_apply_id, goodwill_type, part_code, part_name, unit_price_no_tax, rate, unit_price_tax, quantity, amount_tax, support_proportion, goodwill_amount_tax, is_valid, is_deleted, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyFinalPartInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_final_part_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.goodwillType !=null and params.goodwillType != '' ">
                AND t.goodwill_type = #{params.goodwillType}
            </if>
                    <if test=" params.partCode !=null and params.partCode != '' ">
                AND t.part_code = #{params.partCode}
            </if>
                    <if test=" params.partName !=null and params.partName != '' ">
                AND t.part_name = #{params.partName}
            </if>
                    <if test=" params.unitPriceNoTax !=null and params.unitPriceNoTax != '' ">
                AND t.unit_price_no_tax = #{params.unitPriceNoTax}
            </if>
                    <if test=" params.rate !=null and params.rate != '' ">
                AND t.rate = #{params.rate}
            </if>
                    <if test=" params.unitPriceTax !=null and params.unitPriceTax != '' ">
                AND t.unit_price_tax = #{params.unitPriceTax}
            </if>
                    <if test=" params.quantity !=null and params.quantity != '' ">
                AND t.quantity = #{params.quantity}
            </if>
                    <if test=" params.amountTax !=null and params.amountTax != '' ">
                AND t.amount_tax = #{params.amountTax}
            </if>
                    <if test=" params.supportProportion !=null and params.supportProportion != '' ">
                AND t.support_proportion = #{params.supportProportion}
            </if>
                    <if test=" params.goodwillAmountTax !=null and params.goodwillAmountTax != '' ">
                AND t.goodwill_amount_tax = #{params.goodwillAmountTax}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyFinalPartInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_final_part_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.goodwillType !=null and params.goodwillType != '' ">
                AND t.goodwill_type = #{params.goodwillType}
            </if>
                    <if test=" params.partCode !=null and params.partCode != '' ">
                AND t.part_code = #{params.partCode}
            </if>
                    <if test=" params.partName !=null and params.partName != '' ">
                AND t.part_name = #{params.partName}
            </if>
                    <if test=" params.unitPriceNoTax !=null and params.unitPriceNoTax != '' ">
                AND t.unit_price_no_tax = #{params.unitPriceNoTax}
            </if>
                    <if test=" params.rate !=null and params.rate != '' ">
                AND t.rate = #{params.rate}
            </if>
                    <if test=" params.unitPriceTax !=null and params.unitPriceTax != '' ">
                AND t.unit_price_tax = #{params.unitPriceTax}
            </if>
                    <if test=" params.quantity !=null and params.quantity != '' ">
                AND t.quantity = #{params.quantity}
            </if>
                    <if test=" params.amountTax !=null and params.amountTax != '' ">
                AND t.amount_tax = #{params.amountTax}
            </if>
                    <if test=" params.supportProportion !=null and params.supportProportion != '' ">
                AND t.support_proportion = #{params.supportProportion}
            </if>
                    <if test=" params.goodwillAmountTax !=null and params.goodwillAmountTax != '' ">
                AND t.goodwill_amount_tax = #{params.goodwillAmountTax}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
    </select>
      
       <!-- 查询亲善金额及分类零配件信息 -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="queryPartAmountInfo" resultMap="BaseResultMap" >
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_final_part_info t
        WHERE 1=1 AND t.goodwill_apply_id = #{goodwillApplyId} and t.goodwill_type=#{goodwillType}
    </select>

</mapper>
