<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.voicemanage.CallScoreItemsMapper">

    <insert id="saveScoreItem" parameterType="java.util.List" >
        insert into tt_call_score_items
        (
        id,
        itemText,
        model,
        score,
        scoreItemId,
        serviceId,
        sessionId,
        dateCreate,
        created_by,
        record_version
       ) values
        <foreach collection="scoreItem" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.itemText},
            #{item.model},
            #{item.score},
            #{item.scoreItemId},
            #{item.serviceId},
            #{item.sessionId},
            #{item.dateCreate},
            '-1',1)
        </foreach>
    </insert>

    <insert id="saveScoreZJItem" parameterType="java.util.List" >
        insert into tt_call_score_items
        (
        id,
        itemText,
        model,
        score,
        scoreItemId,
        serviceId,
        sessionId,
        dateCreate,
        created_by,
        record_version
        ) values
        <foreach collection="scoreItem" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.itemText},
            #{item.model},
            #{item.score},
            #{item.scoreItemId},
            #{item.serviceId},
            #{item.sessionId},
            #{item.dateCreate},
            '-1',1)
        </foreach>
    </insert>
</mapper>