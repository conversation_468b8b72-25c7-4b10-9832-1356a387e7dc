<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillDealerLinesMaintainMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillDealerLinesMaintainPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                            <result column="APP_ID" property="appId"/>
                        <result column="OWNER_CODE" property="ownerCode"/>
                        <result column="OWNER_PAR_CODE" property="ownerParCode"/>
                        <result column="ORG_ID" property="orgId"/>
                        <result column="goodwill_nature" property="goodwillNature"/>
                        <result column="dealer_code" property="dealerCode"/>
                        <result column="valid_start_date" property="validStartDate"/>
                        <result column="valid_end_date" property="validEndDate"/>
                        <result column="yearly_budget" property="yearlyBudget"/>
                        <result column="found_date" property="foundDate"/>
                        <result column="is_valid" property="isValid"/>
                        <result column="is_deleted" property="isDeleted"/>
                        <result column="created_at" property="createdAt"/>
                        <result column="updated_at" property="updatedAt"/>
                    	<result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, goodwill_nature, dealer_code, valid_start_date, valid_end_date, yearly_budget, found_date, is_valid, is_deleted, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillDealerLinesMaintainDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tm_goodwill_dealer_lines_maintain t
        WHERE 1=1 AND t.is_deleted =0
            <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
            <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
            <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
            <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
            <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
            <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
            <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code like CONCAT('%',#{params.dealerCode},'%')
            </if>
            <if test=" params.validStartDate !=null  ">
                <![CDATA[   and DATE_FORMAT(t.valid_end_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.validStartDate}, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.validEndDate !=null  ">
                <![CDATA[   and DATE_FORMAT(t.valid_end_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.validEndDate}, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.yearlyBudget !=null and params.yearlyBudget != '' ">
                AND t.yearly_budget = #{params.yearlyBudget}
            </if>
            <if test=" params.foundDateStart !=null ">
                <![CDATA[   and DATE_FORMAT(t.found_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.foundDateStart}, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.foundDateEnd !=null  ">
                <![CDATA[   and DATE_FORMAT(t.found_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.foundDateEnd}, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
            
            <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
            <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            <if test=" params.areaManages !=null and params.areaManages != '' ">
                AND t.dealer_code in (${params.areaManages})
            </if>
            </select>
            
        <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageCountBySql" resultType="integer" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillDealerLinesMaintainDTO">
        SELECT
        COUNT(1)
       
       FROM  tm_goodwill_dealer_lines_maintain t
        WHERE 1=1  AND t.is_deleted =0
            <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
            <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
            <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
            <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
            <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
            <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
            <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code like CONCAT('%',#{params.dealerCode},'%')
            </if>
            <if test=" params.validStartDate !=null  ">
                <![CDATA[   and DATE_FORMAT(t.valid_end_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.validStartDate}, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.validEndDate !=null ">
                <![CDATA[   and DATE_FORMAT(t.valid_end_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.validEndDate}, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.yearlyBudget !=null and params.yearlyBudget != '' ">
                AND t.yearly_budget = #{params.yearlyBudget}
            </if>
            <if test=" params.foundDateStart !=null  ">
                <![CDATA[   and DATE_FORMAT(t.found_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.foundDateStart}, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.foundDateEnd !=null  ">
                <![CDATA[   and DATE_FORMAT(t.found_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.foundDateEnd}, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
            
            <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
            <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
           <if test=" params.areaManages !=null and params.areaManages != '' ">
                AND t.dealer_code in (${params.areaManages})
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillDealerLinesMaintainPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tm_goodwill_dealer_lines_maintain t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
                    <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code like CONCAT('%',#{params.dealerCode},'%')
            </if>
                    <if test=" params.validStartDate !=null and params.validStartDate != '' ">
                AND t.valid_start_date = #{params.validStartDate}
            </if>
                    <if test=" params.validEndDate !=null and params.validEndDate != '' ">
                AND t.valid_end_date = #{params.validEndDate}
            </if>
                    <if test=" params.yearlyBudget !=null and params.yearlyBudget != '' ">
                AND t.yearly_budget = #{params.yearlyBudget}
            </if>
                    <if test=" params.foundDate !=null and params.foundDate != '' ">
                AND t.found_date = #{params.foundDate}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>
 <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectExportListBySql" resultType="hashMap">
        SELECT
        APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, CASE WHEN goodwill_nature = 82351001 THEN '特殊亲善' ELSE '' end  goodwill_nature, dealer_code, valid_start_date, valid_end_date, yearly_budget, found_date, CASE WHEN is_valid=10011001 THEN '有效' WHEN is_valid=10011002 THEN '无效' END is_valid, is_deleted, created_at, updated_at

        FROM tm_goodwill_dealer_lines_maintain t
        WHERE 1=1 AND t.is_deleted =0
           
             <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
            <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
            <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
            <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
            <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
            <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
            <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code like CONCAT('%',#{params.dealerCode},'%')
            </if>
            <if test=" params.validStartDate !=null  ">
                <![CDATA[   and DATE_FORMAT(t.valid_end_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.validStartDate}, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.validEndDate !=null ">
                <![CDATA[   and DATE_FORMAT(t.valid_end_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.validEndDate}, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.yearlyBudget !=null and params.yearlyBudget != '' ">
                AND t.yearly_budget = #{params.yearlyBudget}
            </if>
            <if test=" params.foundDateStart !=null  ">
                <![CDATA[   and DATE_FORMAT(t.found_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.foundDateStart}, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.foundDateEnd !=null  ">
                <![CDATA[   and DATE_FORMAT(t.found_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.foundDateEnd}, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
            
            <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
            <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
           <if test=" params.areaManages !=null and params.areaManages != '' ">
                AND t.dealer_code in (${params.areaManages})
            </if>
             
            </select>
</mapper>
