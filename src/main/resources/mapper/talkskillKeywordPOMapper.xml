<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.talkskill.TalkskillKeywordMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillKeywordPO">
            <id column="key_id" property="keyId"/>
            <result column="num" property="num"/>
            <result column="data_sources" property="dataSources"/>
            <result column="is_deleted" property="isDeleted"/>
            <result column="is_valid" property="isValid"/>
            <result column="created_at" property="createdAt"/>
            <result column="updated_at" property="updatedAt"/>
            <result column="created_by" property="createdBy"/>
            <result column="updated_by" property="updatedBy"/>
            <result column="record_version" property="recordVersion"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           key_id, keyword, num
        </sql>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillKeywordPO">
        SELECT key_id, keyword, num FROM tt_talkskill_keyword t
        <if test=" params.keyword !=null and params.keyword != '' ">
            where keyword = #{params.keyword}
        </if>
    </select>
    <select id="countNum" resultType="java.lang.Integer" parameterType="com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillKeywordPO">
        SELECT count(*) FROM tt_talkskill_keyword t
        <if test=" keyword !=null and keyword != '' ">
            where keyword = #{keyword}
        </if>
    </select>
</mapper>
