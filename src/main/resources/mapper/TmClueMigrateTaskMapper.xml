<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.clueMigrate.TmClueMigrateTaskMapper">

    <select id="sqlTest" resultType="java.lang.Integer">
        select 1
        from tt_invite_vehicle_record
        limit 1
    </select>

    <resultMap id="clueMigrateTaskVOResMap" type="com.yonyou.dmscus.customer.vo.clueMigrate.ClueMigrateTaskVO">
        <id property="id" column="id"/>
        <result property="ownerCode" column="owner_code"/>
        <result property="sourceOwnerCode" column="s_owner_code"/>
        <result property="vin" column="vin"/>
        <result property="syncType" column="sync_type"/>
        <result property="syncStatus" column="sync_status"/>
        <result property="finishTime" column="finish_time"/>
        <result property="startTime" column="start_time"/>
        <result property="ownerParCode" column="owner_par_code"/>
        <result property="createdBy" column="created_by"/>
        <result property="migratedCdp" column="migrated_cdp"/>
    </resultMap>

    <sql id="getListVOSql">
        select tcmt.id,
        tcmt.owner_code,
        tcmt.s_owner_code,
        tcmt.sync_type,
        tcmt.sync_status,
        tcmt.finish_time,
        tcmt.vin,
        tcmt.start_time,
        tcmt.owner_par_code,
        tcmt.created_by,
        tcmt.migrated_cdp
        from tm_clue_migrate_task tcmt
        <where>
            <if test=" clueMigrateTaskQueryDTO.ownerCode != null and clueMigrateTaskQueryDTO.ownerCode != '' ">
                and tcmt.owner_code like concat('%', #{ clueMigrateTaskQueryDTO.ownerCode }, '%')
            </if>
            <if test=" clueMigrateTaskQueryDTO.sourceOwnerCode != null and clueMigrateTaskQueryDTO.sourceOwnerCode != '' ">
                and tcmt.s_owner_code like concat(#{ clueMigrateTaskQueryDTO.sourceOwnerCode }, '%')
            </if>
            <if test=" clueMigrateTaskQueryDTO.syncStatus != null ">
                and tcmt.sync_status = #{ clueMigrateTaskQueryDTO.syncStatus }
            </if>
            <if test=" clueMigrateTaskQueryDTO.syncType != null ">
                and tcmt.sync_type = #{ clueMigrateTaskQueryDTO.syncType }
            </if>
            <if test=" clueMigrateTaskQueryDTO.vin != null and  clueMigrateTaskQueryDTO.vin != ''">
                and tcmt.vin like concat('%', #{ clueMigrateTaskQueryDTO.vin }, '%')
            </if>
        </where>
        order by tcmt.created_at desc
    </sql>

    <select id="pageList" resultMap="clueMigrateTaskVOResMap">
        <include refid="getListVOSql"/>
    </select>

    <select id="getClueMigrateTaskVOList" resultMap="clueMigrateTaskVOResMap">
        <include refid="getListVOSql"/>
    </select>

</mapper>
