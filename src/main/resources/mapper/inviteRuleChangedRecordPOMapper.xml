<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.inviteRule.InviteRuleChangedRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRuleChangedRecordPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="invite_type" property="inviteType"/>
        <result column="invite_rule" property="inviteRule"/>
        <result column="rule_value" property="ruleValue"/>
        <result column="last_rule_value" property="lastRuleValue"/>
        <result column="remind_interval" property="remindInterval"/>
        <result column="last_remind_interval" property="lastRemindInterval"/>
        <result column="last_close_interval" property="lastCloseInterval"/>
        <result column="close_interval" property="closeInterval"/>
        <result column="is_use" property="isUse"/>
        <result column="last_is_use" property="lastIsUse"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="update_is_execute" property="updateIsExecute"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        app_id, owner_code, owner_par_code, org_id, id, dealer_code, invite_type, invite_rule, rule_value,
        last_rule_value, remind_interval, last_remind_interval, last_close_interval, close_interval, is_use,
        last_is_use, data_sources, is_deleted, is_valid, created_at, updated_at, update_is_execute,remark
    </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRuleChangedRecordPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_rule_changed_record t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.inviteType !=null and params.inviteType != '' ">
            AND t.invite_type = #{params.inviteType}
        </if>
        <if test=" params.inviteRule !=null and params.inviteRule != '' ">
            AND t.invite_rule = #{params.inviteRule}
        </if>
        <if test=" params.ruleValue !=null and params.ruleValue != '' ">
            AND t.rule_value = #{params.ruleValue}
        </if>
        <if test=" params.lastRuleValue !=null and params.lastRuleValue != '' ">
            AND t.last_rule_value = #{params.lastRuleValue}
        </if>
        <if test=" params.remindInterval !=null and params.remindInterval != '' ">
            AND t.remind_interval = #{params.remindInterval}
        </if>
        <if test=" params.lastRemindInterval !=null and params.lastRemindInterval != '' ">
            AND t.last_remind_interval = #{params.lastRemindInterval}
        </if>
        <if test=" params.lastCloseInterval !=null and params.lastCloseInterval != '' ">
            AND t.last_close_interval = #{params.lastCloseInterval}
        </if>
        <if test=" params.closeInterval !=null and params.closeInterval != '' ">
            AND t.close_interval = #{params.closeInterval}
        </if>
        <if test=" params.isUse !=null and params.isUse != '' ">
            AND t.is_use = #{params.isUse}
        </if>
        <if test=" params.lastIsUse !=null and params.lastIsUse != '' ">
            AND t.last_is_use = #{params.lastIsUse}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        <if test=" params.updateIsExecute !=null and params.updateIsExecute != '' ">
            AND t.update_is_execute = #{params.updateIsExecute}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRuleChangedRecordPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_rule_changed_record t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.inviteType !=null and params.inviteType != '' ">
            AND t.invite_type = #{params.inviteType}
        </if>
        <if test=" params.inviteRule !=null and params.inviteRule != '' ">
            AND t.invite_rule = #{params.inviteRule}
        </if>
        <if test=" params.ruleValue !=null and params.ruleValue != '' ">
            AND t.rule_value = #{params.ruleValue}
        </if>
        <if test=" params.lastRuleValue !=null and params.lastRuleValue != '' ">
            AND t.last_rule_value = #{params.lastRuleValue}
        </if>
        <if test=" params.remindInterval !=null and params.remindInterval != '' ">
            AND t.remind_interval = #{params.remindInterval}
        </if>
        <if test=" params.lastRemindInterval !=null and params.lastRemindInterval != '' ">
            AND t.last_remind_interval = #{params.lastRemindInterval}
        </if>
        <if test=" params.lastCloseInterval !=null and params.lastCloseInterval != '' ">
            AND t.last_close_interval = #{params.lastCloseInterval}
        </if>
        <if test=" params.closeInterval !=null and params.closeInterval != '' ">
            AND t.close_interval = #{params.closeInterval}
        </if>
        <if test=" params.isUse !=null and params.isUse != '' ">
            AND t.is_use = #{params.isUse}
        </if>
        <if test=" params.lastIsUse !=null and params.lastIsUse != '' ">
            AND t.last_is_use = #{params.lastIsUse}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        <if test=" params.updateIsExecute !=null and params.updateIsExecute != '' ">
            AND t.update_is_execute = #{params.updateIsExecute}
        </if>
    </select>

    <select id="getUpdateNoExecute" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_rule_changed_record t
        where t.update_is_execute=0
        and t.invite_type=#{inviteType}
    </select>

    <update id="updateIsExecute">
        update
            tt_invite_rule_changed_record t
        set t.update_is_execute=1,
            t.remark='存在新的影响改变'
        where t.update_is_execute=0
         and t.dealer_code=#{dealerCode}
         and t.invite_type=#{inviteType}
        <if test=" inviteRule !=null ">
            and t.invite_rule=#{inviteRule}
        </if>

    </update>

</mapper>
