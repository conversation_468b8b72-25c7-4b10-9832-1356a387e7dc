<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.InviteTag.InviteVehicleTagMapper">

	<select id="selectByDtoForPage" resultType="com.yonyou.dmscus.customer.dto.InviteVehicleTagDTO">
		SELECT vt.VIN, group_CONCAT(distinct tag.name) as tagName
	      FROM tt_invite_vehicle_tag vt
 	     INNER JOIN tt_invite_tag tag on tag.id = vt.tag_id
		 <where>
 	        vt.is_deleted = 0
 	     	<if test="param.vin != null and param.vin != ''">
	 	        AND vt.vin = #{param.vin}
 	     	</if>
 	     	<choose>
 	     		<when test="param.tagNameList != null and param.tagNameList.size > 0">
 	     			AND tag.name IN
 	     			<foreach collection="param.tagNameList" item="tagName" open="(" close=")" separator=",">
 	     				#{tagName}
 	     			</foreach>
 	     		</when>
 	     		<otherwise>
 	     		 <if test="param.tagName != null and param.tagName != ''">
						AND tag.name = #{param.tagName}
 	     		 </if>
 	     		</otherwise>
 	     	</choose>
 	     </where>
		GROUP by vt.vin
		ORDER BY vt.vin
	</select>
	
	<select id="selectByDto" resultType="com.yonyou.dmscus.customer.dto.InviteVehicleTagDTO">
		SELECT vt.VIN, group_CONCAT(distinct tag.name) as tagName
	      FROM tt_invite_vehicle_tag vt 
 	     INNER JOIN tt_invite_tag tag on tag.id  = vt.tag_id
		 <where>
			 vt.is_deleted = 0
 	     	<if test="param.vin != null and param.vin != ''">
	 	        AND vt.vin = #{param.vin}
 	     	</if>
 	     	<choose>
 	     		<when test="param.tagNameList != null and param.tagNameList.size > 0">
 	     			AND tag.name IN
 	     			<foreach collection="param.tagNameList" item="tagName" open="(" close=")" separator=",">
 	     				#{tagName}
 	     			</foreach>
 	     		</when>
 	     		<otherwise>
 	     		 <if test="param.tagName != null and param.tagName != ''">
 					AND tag.name = #{param.tagName}
 	     		 </if>
 	     		</otherwise>
 	     	</choose>
 	     </where>
		GROUP by vt.vin
		ORDER BY vt.vin
	</select>
	
	<select id="selectVinTagByVIN" resultType="com.yonyou.dmscus.customer.dto.InviteTagDto">
		SELECT name,tag_desc,topic
	      FROM tt_invite_vehicle_tag vt 
 	     INNER JOIN tt_invite_tag tag on tag.id  = vt.tag_id
 	     <where>
			vt.is_deleted = 0
 	     	AND vt.vin = #{vin}
 	     </where>
	</select>
	
	<insert id="insertBatch" parameterType="java.util.List">
		insert into tt_invite_vehicle_tag
		(
		app_id,
		vin,
		tag_id,
		bind_type,
		created_by) values
		<foreach collection="tagPos" item="item" index="index" separator=",">
			(
			#{item.appId},
			#{item.vin},
			#{item.tagId},
			#{item.bindType},
			#{item.createdBy}
			)
		</foreach>
	</insert>

	<select id="selectByVins" resultType="com.yonyou.dmscus.customer.dto.InviteVehicleTagDTO">
		SELECT vt.VIN, group_CONCAT(distinct tag.name) as tagName
		FROM tt_invite_vehicle_tag vt
		INNER JOIN tt_invite_tag tag on tag.id = vt.tag_id
		<where>
			vt.is_deleted = 0
			<if test="vins !=null and vins.size > 0">
				AND vt.vin IN
				<foreach collection="vins" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
		GROUP by vt.vin
	</select>

</mapper>
