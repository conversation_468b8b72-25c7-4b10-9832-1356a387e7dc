<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleRecordDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordDetailPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="invite_id" property="inviteId"/>
        <result column="content" property="content"/>
        <result column="feedback" property="feedback"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="lose_reason" property="loseReason"/>
        <result column="not_follow_reason" property="notFollowReason"/>
        <result column="plan_date" property="planDate"/>
        <result column="actual_date" property="actualDate"/>
        <result column="mode" property="mode"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="SA_ID" property="saId"/>
        <result column="SA_NAME" property="saName"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="call_id" property="callId"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="invite_type" property="inviteType"/>
        <result column="advise_in_date" property="adviseInDate"/>
        <result column="new_advise_in_date" property="newAdviseInDate"/>
        <result column="vin" property="vin"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
           owner_code, id, invite_id, content, feedback, remark,
           status, lose_reason, not_follow_reason, plan_date, actual_date, mode, data_sources,
           is_deleted, is_valid, created_at, updated_at, vin, advise_in_date, invite_type,
           SA_ID, SA_NAME, dealer_code, call_id
        </sql>

    <select id="getInviteInsuranceVehicleRecordInfoDlr" resultMap="BaseResultMap">
        select
        d.id,t.invite_type,t.advise_in_date,t.new_advise_in_date,d.invite_id,
        d.actual_date,d.SA_NAME,d.status,d.content,d.mode
        from tt_invite_insurance_vehicle_record_detail d
        left join tt_invite_insurance_vehicle_record t on t.id=d.invite_id
        where d.invite_id=#{id}
        order by d.actual_date desc
    </select>


    <select id="selectFollowInviteInsureDetailHistory" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM tt_invite_insurance_vehicle_record_detail t
        WHERE 1=1
        <if test=" vin !=null and vin != '' ">
            AND t.vin = #{vin}
        </if>
        <if test=" dealerCode !=null and dealerCode != '' ">
            AND t.dealer_code = #{dealerCode}
        </if>
        order by t.actual_date desc
    </select>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordDetailPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_insurance_vehicle_record_detail t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.inviteId !=null and params.inviteId != '' ">
            AND t.invite_id = #{params.inviteId}
        </if>
        <if test=" params.content !=null and params.content != '' ">
            AND t.content = #{params.content}
        </if>
        <if test=" params.feedback !=null and params.feedback != '' ">
            AND t.feedback = #{params.feedback}
        </if>
        <if test=" params.remark !=null and params.remark != '' ">
            AND t.remark = #{params.remark}
        </if>
        <if test=" params.status !=null and params.status != '' ">
            AND t.status = #{params.status}
        </if>
        <if test=" params.loseReason !=null and params.loseReason != '' ">
            AND t.lose_reason = #{params.loseReason}
        </if>
        <if test=" params.notFollowReason !=null and params.notFollowReason != '' ">
            AND t.not_follow_reason = #{params.notFollowReason}
        </if>
        <if test=" params.planDate !=null and params.planDate != '' ">
            AND t.plan_date = #{params.planDate}
        </if>
        <if test=" params.actualDate !=null and params.actualDate != '' ">
            AND t.actual_date = #{params.actualDate}
        </if>
        <if test=" params.mode !=null and params.mode != '' ">
            AND t.mode = #{params.mode}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.SA_ID = #{params.saId}
        </if>
        <if test=" params.saName !=null and params.saName != '' ">
            AND t.SA_NAME = #{params.saName}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.callId !=null and params.callId != '' ">
            AND t.call_id = #{params.callId}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordDetailPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_insurance_vehicle_record_detail t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.inviteId !=null and params.inviteId != '' ">
            AND t.invite_id = #{params.inviteId}
        </if>
        <if test=" params.content !=null and params.content != '' ">
            AND t.content = #{params.content}
        </if>
        <if test=" params.feedback !=null and params.feedback != '' ">
            AND t.feedback = #{params.feedback}
        </if>
        <if test=" params.remark !=null and params.remark != '' ">
            AND t.remark = #{params.remark}
        </if>
        <if test=" params.status !=null and params.status != '' ">
            AND t.status = #{params.status}
        </if>
        <if test=" params.loseReason !=null and params.loseReason != '' ">
            AND t.lose_reason = #{params.loseReason}
        </if>
        <if test=" params.notFollowReason !=null and params.notFollowReason != '' ">
            AND t.not_follow_reason = #{params.notFollowReason}
        </if>
        <if test=" params.planDate !=null and params.planDate != '' ">
            AND t.plan_date = #{params.planDate}
        </if>
        <if test=" params.actualDate !=null and params.actualDate != '' ">
            AND t.actual_date = #{params.actualDate}
        </if>
        <if test=" params.mode !=null and params.mode != '' ">
            AND t.mode = #{params.mode}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>


</mapper>
