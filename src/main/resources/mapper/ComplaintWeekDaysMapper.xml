<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.complaint.ComplaintWeekDaysMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.complaint.HolidayPO">
        <id column="id" property="id"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="holiday_date" property="holidayDate"/>
    </resultMap>

    <select id="selectWeekDays" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.complaint.HolidayPO">
        select * from tm_holiday
            where 1=1
        <if test=" calltime !=null ">
        and holiday_date>#{calltime}
        </if>
        <if test=" nowtime !=null ">
        and #{nowtime}>holiday_date
        </if>
    </select>

    <select id="selectOneDays" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.complaint.HolidayPO">
        select * from tm_holiday
            where 1=1
        <if test=" time !=null ">
            and #{time}<![CDATA[ >= ]]>holiday_date
        </if>
        order by holiday_date desc
        limit 1
    </select>
</mapper>