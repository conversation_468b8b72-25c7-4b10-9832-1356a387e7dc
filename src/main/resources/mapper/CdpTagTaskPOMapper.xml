<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.voc.CdpTagTaskMapper">


    <select id="selectInviteStatusPlan" resultType="java.lang.String">
        SELECT t.vin
        FROM tt_invite_vehicle_record t
        left join tt_cdp_tag_task a
        on a.biz_no=t.vin
        AND a.since_type = 1
        and a.created_at &gt;= #{startTime}
        and a.created_at &lt;= #{endTime}
        where t.invite_type IN (82381001,82381002)
        and t.order_status = 82411002
        AND a.biz_no is null
        and t.source_type = 4
        <if test=" whiteList !=null and whiteList.size() > 0 ">
            and t.dealer_code in
                <foreach
                        collection="whiteList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
        </if>
    </select>

    <insert id="insertCdpTagTask">
        insert into tt_cdp_tag_task (biz_no,since_type)
        values
        <foreach collection="vinList" item="item" index="index" separator=",">
            (
            #{item},
            1
            )
        </foreach>
    </insert>

    <select id="selectCountCdpTag" resultType="java.lang.Integer">
        SELECT count(1)
        FROM tt_invite_vehicle_record t
        left join tt_cdp_tag_task a
        on a.biz_no=t.vin
        AND a.since_type = 1
        and a.created_at &gt;= #{startTime}
        and a.created_at &lt;= #{endTime}
        where t.invite_type IN (82381001,82381002)
        and t.order_status = 82411002
        AND a.biz_no is null
        <if test=" whiteList !=null and whiteList.size() > 0 ">
            and t.source_type = 4
            and t.dealer_code in
                <foreach
                    collection="whiteList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
        </if>
    </select>

    <select id="selectTaskList" resultType="java.lang.String">
        SELECT t.biz_no
        FROM tt_cdp_tag_task t
        where
            since_type = 1
            and t.task_status IN (0,2)
            and t.retry_count &lt; 3
            and t.created_at &gt;= #{startTime}
            and t.created_at &lt;= #{endTime}
    </select>

    <select id="selectTaskCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM tt_cdp_tag_task t
        where
            since_type = 1
            and t.task_status IN (0,2)
            and t.retry_count &lt; 3
            and t.created_at &gt;= #{startTime}
            and t.created_at &lt;= #{endTime}
    </select>

    <update id="updateTagTask">
        UPDATE tt_cdp_tag_task t
        set task_status = IF(retry_count=2,-1,2) , retry_count = retry_count + 1 ,last_retry_time = now() , updated_at  = now()
        <if test=" message !=null and message != '' ">
            , error_message = #{message}
        </if>
        where biz_no in
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </update>

    <update id="updateTagTaskError">
        UPDATE tt_cdp_tag_task t
        set task_status = IF(retry_count=2,-1,2) , retry_count = retry_count + 1 ,last_retry_time = now() , updated_at  = now()
        <if test=" message !=null and message != '' ">
            , error_message = #{message}
        </if>
        where biz_no in
            <foreach collection="vinList" index="index" item="item" open="(" separator="," close=")">
                #{item.vin}
            </foreach>
    </update>

    <update id="updateCdpTagTask">
        <foreach collection="vinList" item="item"  separator=";">
            UPDATE tt_cdp_tag_task t
            set task_status = 1 , retry_count = retry_count + 1 , last_retry_time = now() , updated_at  = now()
            where biz_no = #{item.vin}
        </foreach>
    </update>

    <insert id="insertCdpTagTaskError">
        insert into tt_cdp_tag_task (biz_no,since_type,error_message,task_status)
        values
        <foreach collection="vinList" item="item" index="index" separator=",">
            (
            #{item},
            #{sinceType},
            #{message},
            2
            )
        </foreach>
    </insert>


    <update id="updateCdpTagTaskSuccess">
        <foreach collection="list" item="item"  separator=";">
            UPDATE tt_cdp_tag_task t
            set task_status = 1 , retry_count = retry_count + 1 , last_retry_time = now() , updated_at  = now()
            where biz_no = #{item}
        </foreach>
    </update>

    <update id="updateTagTaskErrorByBizNo">
        UPDATE tt_cdp_tag_task t
        set task_status = IF(retry_count=2,-1,2) , retry_count = retry_count + 1 ,last_retry_time = now() , updated_at  = now()
        <if test=" message !=null and message != '' ">
            , error_message = #{message}
        </if>
        where biz_no in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <insert id="batchInsertHighlightFlagClueId">
        insert into tt_cdp_tag_task
        (
         since_type,
         biz_no,
         created_at,
         updated_at,
         created_by,
         updated_by
        ) values
        <foreach collection="clueId" item="item" index="index" separator=",">
        (
         2,
         #{item},
         NOW(),
         NOW(),
         '1',
         '1'
        )
        </foreach>
    </insert>
    <update id="updateFaultLightClueById">
        update `dms_manage`.`tt_cdp_tag_task` set retry_count = retry_count+1,task_status = 1,last_retry_time= now(),updated_at = now()
        where id in
        <foreach close=")" collection="listItem" item="listItem" open="(" separator=",">
            #{listItem}
        </foreach>
    </update>
    <update id="updateErrorTask">
        <foreach close=")" collection="listItem" item="listItem" open="(" separator=",">
        update `dms_manage`.`tt_cdp_tag_task` set
        retry_count = #{listItem.retryCount},
        task_status = #{listItem.taskStatus},
        last_retry_time= #{listItem.lastRetryTime},
        updated_at = NOW(),
        error_message = #{listItem.errorMessage},
        where id =#{listItem.id}
        </foreach>
    </update>
    <select id="queryFaultLightClueTotal" resultType="java.lang.Integer">
        SELECT COUNT(biz_no) FROM `dms_manage`.`tt_cdp_tag_task` WHERE since_type =2 AND task_status IN (0,2)
    </select>
    <select id="queryFaultLightClueId"
            resultType="com.yonyou.dmscus.customer.entity.po.vocfunctional.CdpTagTaskPo">
        SELECT id,biz_no,retry_count FROM `dms_manage`.`tt_cdp_tag_task` WHERE since_type =2 AND task_status IN (0,2)
    </select>

</mapper>
