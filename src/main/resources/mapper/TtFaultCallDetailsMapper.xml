<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.faultLight.TtFaultCallDetailsMapper">

    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultCallDetailsPO" >
        <result column="app_id" property="appId" />
        <result column="owner_code" property="ownerCode" />
        <result column="owner_par_code" property="ownerParCode" />
        <result column="org_id" property="orgId" />
        <result column="id" property="id" />
        <result column="session_id" property="sessionId" />
        <result column="call_id" property="callId" />
        <result column="display_number" property="displayNumber" />
        <result column="caller_number" property="callerNumber" />
        <result column="callee_number" property="calleeNumber" />
        <result column="work_number" property="workNumber" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="call_length" property="callLength" />
        <result column="service_type" property="serviceType" />
        <result column="total_score" property="totalScore" />
        <result column="data_sources" property="dataSources" />
        <result column="is_deleted" property="isDeleted" />
        <result column="is_valid" property="isValid" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
        <result column="record_version" property="recordVersion" />
        <result column="dealer_code" property="dealerCode" />
        <result column="mark" property="mark" />
        <result column="detail_id" property="detailId" />
        <result column="call_type" property="callType" />
    </resultMap>

    <sql id="Base_Column_List">
        app_id,
                owner_code,
                owner_par_code,
                org_id,
                id,
                session_id,
                call_id,
                display_number,
                caller_number,
                callee_number,
                work_number,
                start_time,
                end_time,
                call_length,
                service_type,
                total_score,
                data_sources,
                is_deleted,
                is_valid,
                created_by,
                created_at,
                updated_by,
                updated_at,
                record_version,
                dealer_code,
                mark,
                detail_id,
                call_type
    </sql>
    <select id="queryCallDetails"
            resultType="com.yonyou.dmscus.customer.entity.dto.faultLight.TtFaultCallDetailsDTO">
        SELECT
            t.cus_name as cusName,
            t.cus_number as cusNumber,
            t1.call_length as callLength,
            t1.start_time as startTime
        FROM
            tt_fault_call_register t
        LEFT JOIN
            tt_fault_call_details t1 on t.call_id = t1.call_id
        WHERE
            t.invite_id = #{inviteId}
    </select>

    <update id="updateFaultLightDetailId">
        update
        tt_fault_call_register
        set detail_id = #{detailId}
        where invite_id = #{inviteId}
        and detail_id is null
    </update>


</mapper>