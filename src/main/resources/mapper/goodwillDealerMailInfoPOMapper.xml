<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillDealerMailInfoMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillDealerMailInfoPO">
	        <result column="OWNER_CODE" property="ownerCode"/>
	        <result column="OWNER_PAR_CODE" property="ownerParCode"/>
	        <result column="ORG_ID" property="orgId"/>
	        <result column="dealer_code" property="dealerCode"/>
	        <result column="e_mail1" property="eMail1"/>
	        <result column="e_mail2" property="eMail2"/>
	        <result column="update_date" property="updateDate"/>
	        <result column="is_valid" property="isValid"/>
	        <result column="is_deleted" property="isDeleted"/>
	        <result column="created_at" property="createdAt"/>
	        <result column="updated_at" property="updatedAt"/>
	    	<result column="created_by" property="createdBy"/>
	        <result column="updated_by" property="updatedBy"/>
	        <result column="record_version" property="recordVersion"/>
        </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, dealer_code, e_mail1, e_mail2, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%S') update_date, is_valid, is_deleted, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillDealerMailInfoPO">
        SELECT
        <include refid="Base_Column_List"/> 
        FROM tm_goodwill_dealer_mail_info t
        WHERE 1=1
            <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
            <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
            <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
            <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
            <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
            <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND (t.dealer_code like CONCAT('%',#{params.dealerCode},'%')  or t.dealer_name like CONCAT('%',#{params.dealerCode},'%'))
            </if>
            <if test=" params.areaManage !=null and params.areaManage != '' ">
                AND t.dealer_code in (${params.areaManage})
            </if>
            <if test=" params.bloc !=null and params.bloc != '' ">
                AND t.dealer_code in (${params.bloc})
            </if>
            <if test=" params.eMail1 !=null and params.eMail1 != '' ">
                AND (t.e_mail1 like CONCAT('%',#{params.eMail1},'%') or t.e_mail2 like CONCAT('%',#{params.eMail1},'%') )
                
            </if>
            <if test=" params.eMail2 !=null and params.eMail2 != '' ">
                AND t.e_mail2 = #{params.eMail2}
            </if>
            <if test=" params.updateDate !=null and params.updateDate != '' ">
                AND t.update_date = #{params.updateDate}
            </if>
            <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
            <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
             <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
            <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>
            
          <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageCountBySql" resultType="integer" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillDealerMailInfoPO">
        SELECT
        COUNT(1)
        FROM tm_goodwill_dealer_mail_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code like CONCAT('%',#{params.dealerCode},'%')  
            </if>
            <if test=" params.areaManage !=null and params.areaManage != '' ">
                AND t.dealer_code in (${params.areaManage})
            </if>
            <if test=" params.bloc !=null and params.bloc != '' ">
                AND t.dealer_code in (${params.bloc})
            </if>
                    <if test=" params.eMail1 !=null and params.eMail1 != '' ">
                AND (t.e_mail1 like CONCAT('%',#{params.eMail1},'%') or t.e_mail2 like CONCAT('%',#{params.eMail1},'%') )

            </if>
                    <if test=" params.eMail2 !=null and params.eMail2 != '' ">
                AND t.e_mail2 = #{params.eMail2}
            </if>
                    <if test=" params.updateDate !=null and params.updateDate != '' ">
                AND t.update_date = #{params.updateDate}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillDealerMailInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tm_goodwill_dealer_mail_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code like CONCAT('%',#{params.dealerCode},'%') 
            </if>
                    <if test=" params.eMail1 !=null and params.eMail1 != '' ">
                AND t.e_mail1 = #{params.eMail1}
            </if>
                    <if test=" params.eMail2 !=null and params.eMail2 != '' ">
                AND t.e_mail2 = #{params.eMail2}
            </if>
                    <if test=" params.updateDate !=null and params.updateDate != '' ">
                AND t.update_date = #{params.updateDate}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>
		<select id="selectByDealerCode" resultMap="BaseResultMap">
		select 
		<include refid="Base_Column_List"/>
		from tm_goodwill_dealer_mail_info
		where dealer_code=#{dealerCode}
		</select>
		
		 <select id="queryDealerIndoByDealerCode" resultType="integer" >
        	SELECT
           count(id)
        	FROM tm_goodwill_dealer_mail_info t
        	WHERE 1=1  and (t.is_valid = 10011001 or t.is_valid is null)
            <if test=" dealerCode !=null and dealerCode != '' ">
                AND t.dealer_code like #{dealerCode}
            </if>
            <if test=" id !=null and id != '' ">
                AND t.id<![CDATA[ <> ]]> #{id}
            </if>
          </select>
</mapper>
