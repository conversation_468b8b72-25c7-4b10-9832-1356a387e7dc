<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.accidentClues.AccidentCluesImportMapper">

    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesImportPO">
        <id column="id" property="id"/>
        <result column="license" property="license"/>
        <result column="models" property="models"/>
        <result column="clues_resource" property="cluesResource"/>
        <result column="contacts" property="contacts"/>
        <result column="contacts_phone" property="contactsPhone"/>
        <result column="outside_amount" property="outsideAmount"/>
        <result column="report_date" property="reportDate"/>
        <result column="accident_type" property="accidentType"/>
        <result column="is_bruise" property="isBruise"/>
        <result column="accident_address" property="accidentAddress"/>
        <result column="insurance_company_name" property="insuranceCompanyName"/>
        <result column="remark" property="remark"/> 
        <result column="is_error" property="isError"/>
        <result column="error_msg" property="errorMsg"/>
        <result column="line_number" property="lineNumber"/>
        
        
    </resultMap>


    <delete id="deleteByCreatedBy" >
        delete from tt_accident_clues_import where created_by =#{createdBy}
    </delete>

    <insert id="bulkInsert">
        insert into tt_accident_clues_import(license,models,dealer_code,
        contacts,contacts_phone,clues_resource,is_error,error_msg,line_number,
        created_by,created_at,record_version,outside_amount,report_date,accident_type,is_bruise,accident_address,insurance_company_name,remark) values
        <foreach collection ="addList" item="dto" separator =",">
            (#{dto.license},#{dto.models},#{dto.dealerCode},
            #{dto.contacts},#{dto.contactsPhone},#{dto.cluesResource},#{dto.isError},#{dto.errorMsg},#{dto.lineNumber}
            ,#{userId},now(),1,#{dto.outsideAmount},#{dto.reportDate},#{dto.accidentType},#{dto.isBruise},#{dto.accidentAddress},#{dto.insuranceCompanyName},#{dto.remark})
        </foreach >
    </insert>

    <select id="queryError"
            resultMap="BaseResultMap">
        SELECT
        *
        FROM tt_accident_clues_import t where t.is_error=1 and
        t.created_by=#{userId}
    </select>


    <select id="querySuccess"
            resultMap="BaseResultMap">
        SELECT
        *
        FROM tt_accident_clues_import t where t.is_error=0 and
        t.created_by=#{userId}
    </select>

    <!-- 查询显示成功导入的数据条数 -->
    <select id="querySucessCount"
            resultType="java.lang.Integer"> SELECT count(1) FROM tt_accident_clues_import t
        where t.is_error=0 and t.created_by=#{userId}
    </select>
    
     <select id="selectErrorPage"
            resultMap="BaseResultMap">
        SELECT
        *
        FROM tt_accident_clues_import t where t.is_error=1 and
        t.created_by=#{userId}
    </select>


    <select id="selectSuccessPage"
            resultMap="BaseResultMap">
        SELECT
        *
        FROM tt_accident_clues_import t where t.is_error=0 and
        t.created_by=#{userId}
    </select>
    
    <update id="updateErrorById">
        update tt_accident_clues_import set is_error = 1 ,error_msg = '车型代码不存在'
        where created_by=#{userId} and models in
         <foreach item="item" index="index" collection="list" 
                         open="(" separator="," close=")">
                        #{item}
                </foreach> 
    </update>
    
    
    <update id="updateError">
        update tt_accident_clues_import
		 set is_error = 1 , error_msg = '同一个车牌号在48小时内不能重复创建事故线索单据'
		where license in (
		select license from (
			SELECT
			license
		FROM
			tt_accident_clues_import a
		where dealer_code=#{dealerCode}
		GROUP BY license  HAVING count(1) > 1
		union
		SELECT a.license from tt_accident_clues_import a
		where EXISTS (SELECT * from tt_accident_clues b where a.license=b.LICENSE and a.dealer_code=b.dealer_code and
        ABS(TIMESTAMPDIFF(HOUR,a.created_at, b.created_at)) &lt; 48
		                                                                                            )
		) b
		)
		 and created_at in (
		 select created_at from (
			SELECT
                created_at
		FROM
			tt_accident_clues_import a
		where dealer_code=#{dealerCode}
		GROUP BY license  HAVING count(1) > 1
		union
		SELECT a.created_at from tt_accident_clues_import a
		where EXISTS (SELECT * from tt_accident_clues b where a.license=b.LICENSE and a.dealer_code=b.dealer_code and
            ABS(TIMESTAMPDIFF(HOUR,a.created_at, b.created_at)) &lt; 48
            )
		) b
		
		)
		
       
    </update>
    
    
    
    <select id="selectPhone" resultType="com.yonyou.dmscus.customer.dto.CustomerInfoCenterDTO">
        SELECT
        contacts name,
        contacts_phone mobile
        FROM tt_accident_clues_import t where t.is_error=0 and
        t.created_by=#{userId}
    </select>
    
    
   
</mapper>
