<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.accidentClues.AccidentCluesMapper">
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesPO">
        <id column="ac_id" property="acId"/>

        <result column="app_Id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="license" property="license"/>
        <result column="models_id" property="modelsId"/>
        <result column="insurance_company_id" property="insuranceCompanyId"/>
        <result column="clues_resource" property="cluesResource"/>
        <result column="contacts" property="contacts"/>
        <result column="contacts_phone" property="contactsPhone"/>
        <result column="report_date" property="reportDate"/>
        <result column="accident_address" property="accidentAddress"/>
        <result column="accident_type" property="accidentType"/>
        <result column="is_bruise" property="isBruise"/>
        <result column="outside_amount" property="outsideAmount"/>
        <result column="follow_people" property="followPeople"/>
        <result column="remark" property="remark"/>
        <result column="is_insured" property="isInsured"/>
        <result column="accident_duty" property="accidentDuty"/>
        <result column="follow_status" property="followStatus"/>
        <result column="clues_status" property="cluesStatus"/>
        <result column="into_dealer_code" property="intoDealerCode"/>
        <result column="into_dealer_date" property="intoDealerDate"/>
        <result column="into_ro_no" property="intoRoNo"/>
        <result column="next_follow_date" property="nextFollowDate"/>
        <result column="is_appointment" property="isAppointment"/>
        <result column="follow_count" property="followCount"/>
        <result column="clues_type" property="cluesType"/>
        <result column="insurance_company_name" property="insuranceCompanyName"/>
        <result column="follow_people_name" property="followPeopleName"/>
        <result column="double_accident" property="doubleAccident"/>
        <result column="after_big_area_id"  property="afterBigAreaId"/>
        <result column="after_big_area_name"  property="afterBigAreaName"/>
        <result column="after_small_area_id"  property="afterSmallAreaId"/>
        <result column="after_small_area_name"  property="afterSmallAreaName"/>

        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="appointment_into_date" property="appointmentIntoDate"/>
        <result column="booking_order_no" property="bookingOrderNo"/>
        <result column="insurance_source" property="insuranceSource"/>

        <result column="province_name" property="provinceName"/>
        <result column="city_name" property="cityName"/>
        <result column="data_status_remark" property="dataStatusRemark"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.app_id,
            t.owner_code,
            t.owner_par_code,
            t.org_id,
            t.ac_id,
            t.dealer_code,
            t.license,
            t.models_id,
            t.insurance_company_id,
            t.clues_resource,
            t.contacts,
            t.contacts_phone,
            t.report_date,
            t.accident_address,
            t.accident_type,
            t.outside_amount,
            t.is_bruise,
            t.follow_people,
            t.remark,
            t.is_insured,
            t.accident_duty,
            t.follow_status,
            t.clues_status,
            t.into_dealer_code,
            t.into_dealer_date,
            t.into_ro_no,
            t.next_follow_date,
            t.is_appointment,
            t.follow_count,
            t.clues_type,
            t.data_sources,
            t.is_deleted,
            t.is_valid,
            t.created_at,
            t.created_by,
            t.updated_at,
            t.updated_by,
            t.record_version,
            t.insurance_company_name,
            t.follow_people_name,
            t.double_accident,
            t.after_big_area_id,
            t.after_big_area_name,
            t.after_small_area_id,
            t.after_small_area_name,
            t.booking_order_no
    </sql>

    <sql id="selectListCondition">
        <if test="params.dealerCode !='' and params.dealerCode != null ">
            and  t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" 'vcdc' != params.source">
            and  t.dealer_code = #{params.dealerCode}
        </if>
        <if test="params.license != null and params.license != '' ">
            and t.license like  "%" #{params.license} "%"
        </if>
        <if test="params.reportDateStart != null">
            and t.report_date >= #{params.reportDateStart}
        </if>
        <if test="params.reportDateEnd != null ">
            and t.report_date <![CDATA[<]]> #{params.reportDateEnd}
        </if>
        <if test="params.insuranceCompanyId != null and params.insuranceCompanyId != '' ">
            and t.insurance_company_id = #{params.insuranceCompanyId}

        </if>
        <if test="params.insuranceCompanyName != null and params.insuranceCompanyName != '' ">
            and t.insurance_company_name = #{params.insuranceCompanyName}

        </if>
        <if test="params.followStatus != null and params.followStatus != '' ">
            and  t.follow_status= #{params.followStatus}

        </if>
        <if test="dto.followStatusList !=null and dto.followStatusList.size>0">
            and t.follow_status in
            <foreach collection="dto.followStatusList" item="follow" open="(" close=")" separator=",">
                #{follow}
            </foreach>
        </if>

        <if test="params.crmId != null and params.crmId != '' ">
            and  ext.crm_id= #{params.crmId}
        </if>
        <if test="params.cluesStatus != null and params.cluesStatus != '' ">
            and t.clues_status= #{params.cluesStatus}
        </if>

        <if test="params.nextFollowDateStart != null">
            and t.next_follow_date >= #{params.nextFollowDateStart}
        </if>
        <if test="params.nextFollowDateEnd != null ">
            and t.next_follow_date <![CDATA[<]]> #{params.nextFollowDateEnd}
        </if>
        <if test="params.followPeople != null and params.followPeople !='' ">
            and t.follow_People=#{params.followPeople}
        </if>
        <if test="25651001 == params.allotStatus">
            and (t.dealer_code is null or t.dealer_code = '' )
        </if>
        <if test="25651002 == params.allotStatus">
            and t.dealer_code is not null
        </if>
        <if test="params.afterBigAreaId != null">
            and t.after_big_area_id=#{params.afterBigAreaId}
        </if>
        <if test="params.afterSmallAreaId != null">
            and t.after_small_area_id=#{params.afterSmallAreaId}
        </if>
        <if test="params.bookingOrderNo != null and params.bookingOrderNo != ''">
            and t.booking_order_no = #{params.bookingOrderNo}
        </if>
        <if test="dto.createdDateStart!=null">
            and t.created_at &gt;=#{dto.createdDateStart}
        </if>
        <if test="dto.createdDateEnd!=null">
            and t.created_at &lt;=#{dto.createdDateEnd}
        </if>
        <if test="params.acId!=null and params.acId!=''">
            and t.ac_id=#{params.acId}
        </if>
        <if test="params.vin !=null and params.vin!=''">
            and t.vin=#{params.vin}
        </if>
        <if test="dto.cluesResourceList!=null and dto.cluesResourceList.size()>0">
            and t.clues_resource in
            <foreach collection="dto.cluesResourceList" item="source" open="(" close=")" separator=",">
                #{source}
            </foreach>
        </if>
        <if test="params.dataStatus == null or params.dataStatus == 1">
            and t.follow_status != 83531007
        </if>
        <if test="params.insuranceSource !=null and params.insuranceSource != ''">
            and ext2.insurance_source = #{params.insuranceSource}
        </if>
        <if test="params.provinceId != null">
            and ext.province_id=#{params.provinceId}
        </if>
        <if test="params.cityId != null">
            and ext.city_id=#{params.cityId}
        </if>
        <if test="dto.dealerCodeList !=null and dto.dealerCodeList.size>0">
            and t.dealer_code in
            <foreach collection="dto.dealerCodeList" item="dealerCode" open="(" close=")" separator=",">
                #{dealerCode}
            </foreach>
        </if>
        <if test="dto.insuranceSourceList !=null and dto.insuranceSourceList.size>0">
            and ext2.insurance_source in
            <foreach collection="dto.insuranceSourceList" item="insuranceSource" open="(" close=")" separator=",">
                #{insuranceSource}
            </foreach>
        </if>

    </sql>

    <sql id="range">
        <if test="userList!=null and userList.size()>0">
            and  (   c.follow_people in
            <foreach collection="userList" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
            or c.created_by in
            <foreach collection="userList" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
            )
        </if>
    </sql>

    <select id="selectPageSql" resultMap="BaseResultMap">
        select
        t.app_id,
        t.owner_code,
        t.owner_par_code,
        t.org_id,
        t.ac_id,
        t.dealer_code,
        t.license,
        t.models_id,
        t.insurance_company_id,
        t.clues_resource,
        t.contacts,
        t.contacts_phone,
        t.report_date,
        t.accident_address,
        t.accident_type,
        t.outside_amount,
        t.is_bruise,
        t.follow_people,
        t.remark,
        t.is_insured,
        t.accident_duty,
        t.follow_status,
        t.clues_status,
        t.into_dealer_code,
        t.into_dealer_date,
        t.into_ro_no,
        t.next_follow_date,
        t.is_appointment,
        t.follow_count,
        t.clues_type,
        t.data_sources,
        t.is_deleted,
        t.is_valid,
        t.created_at,
        t.created_by,
        t.updated_at,
        t.updated_by,
        t.record_version,
        t.insurance_company_name,
        t.follow_people_name,
        t.double_accident,
        t.after_big_area_id,
        t.after_big_area_name,
        t.after_small_area_id,
        t.after_small_area_name,
        t.booking_order_no,
        t.vin,
        ext.crm_id,
        case
        when ext.crm_id is null then 10041002
        when ext.repeat_son_lead_id is null then 10041002
        when convert(ext.crm_id,char)=convert(ext.repeat_son_lead_id,char) then 10041002
        else 10041001 end repeatLead,
        ext.accident_date,
        ext.accident_reason,
        ext.contacts_name,
        ext.call_type,
        ext.call_police_flag,
        ext.source_channel,
        ext.channel_type,
        ext.regist_no,
        ext.model_name,
        ext.owner_name,
        ext.owner_mobile,
        ext.client_type,
        ext.first_follow_time,
        ext.last_follow_time,
        ext.follow_text,
        ext.follow_fail_why,
        ext2.virtual_phone_flag,
        ext2.insurance_source,
        ext.province_name,
        ext.city_name,
        ext2.data_status_remark
        from tt_accident_clues t
        LEFT JOIN tt_accident_clues_ext ext on ext.`ac_id` = t.`ac_id`
        LEFT JOIN tt_accident_clues_ext_2 ext2 on ext2.`ac_id` = t.`ac_id`
        where  t.is_deleted=0   AND (ext.parent_crm_id = 0 or ext.parent_crm_id is null)
        <include refid="selectListCondition"/>
        order by t.created_at desc
    </select>

    <select id="selectCountBySql" resultType="long">
        select  count(1)
        from tt_accident_clues t
        where  is_deleted=0
        and follow_status in(83531001,83531002)
        and dealer_code =#{ownerCode}
        <if test="limitDate != null and limitDate != ''">
            and created_at > #{limitDate}
        </if>
        <include refid="range"/>
    </select>


    <select id="exportExcelAccident" resultType="java.util.Map">
    SELECT
        t.dealer_code,
        t.after_big_area_name,
        t.after_small_area_name,
        t.license,
        t.contacts,
        t.contacts_phone,
        date_format(t.report_date, '%Y-%m-%d %H:%i:%s') AS report_date,
        CASE t.follow_status
        WHEN 83531001 THEN
            '未跟进'
        WHEN 83531002 THEN
            '继续跟进'
        WHEN 83531003 THEN
            '跟进成功'
        WHEN 83531004 THEN
            '跟进失败'
        WHEN 83531005 THEN
            '超时未跟进'
        WHEN 83531006 THEN
            '超时关闭'
        END follow_status,
        CASE t.clues_status
        WHEN 83511002 THEN
            ''
        WHEN 83511001 THEN
            '已开单已结算'
        WHEN 83511004 THEN
            '已开单未结算'
        WHEN 83511003 THEN
            '他店进厂'
        WHEN 83511005 THEN
            ''
        END clues_status,
        t.into_dealer_code,
        date_format(t.into_dealer_date, '%Y-%m-%d %H:%i:%s') AS into_dealer_date,
        t.into_ro_no,
        t.follow_people_name,
        t.follow_people,
        date_format(t.next_follow_date, '%Y-%m-%d %H:%i:%s') AS next_follow_date,
        t.insurance_company_name,
        date_format(t.created_at, '%Y-%m-%d %H:%i:%s') AS created_at,
        case t.clues_resource
            when 15211012 then '其他'
            when 15211013 then '集团下发'
            when 15211014 then '保险公司短信推送'
            when 15211015 then '客户来电'
            when 15211016 then '外拓业务'
            when 15211017 then '400'
            when 15211018 then '平安易保'
        END clues_resource,
        t.vin,
        ce.channel_type AS channel_type,
        ce.regist_no AS regist_no,
        ce.model_name AS model_name,
        ce.owner_name AS owner_name,
        ce.owner_mobile AS owner_mobile,
        date_format(ce.accident_date, '%Y-%m-%d %H:%i:%s') AS accident_date,
        ce.accident_reason AS accident_reason,
        t.accident_address AS accident_address,
        date_format(ce.first_follow_time, '%Y-%m-%d %H:%i:%s') AS first_follow_time,
        date_format(ce.last_follow_time, '%Y-%m-%d %H:%i:%s') AS last_follow_time,
        t.follow_count AS follow_count,
        case ce.follow_fail_why
        when 83561001 then '未投保商业险（车损）'
        when 83561002 then '车辆已全损'
        when 83561003 then '有熟悉的4S店'
        when 83561004 then '有熟悉的修理厂'
        when 83561005 then '外地出险在当地维修'
        when 83561006 then '外地车回当地维修'
        when 83561007 then '已被其他渠道留修'
        when 83561008 then '服务不满意，拒绝返厂维修'
        when 83561009 then '联系客户时决定其他渠道维修'
        when 83561010 then '联系方式错误'
        when 83561011 then '电话无人接听，联系不到客户'
        when 83561012 then '其他'
        when 83561013 then '客户已去过其他门店'
        when 83561014 then '客户确认不维修'
        when 83561015 then '客户未确认进店时间'
        end follow_fail_why,
        ce.follow_text AS follow_text,
        case ce.client_type when 0 then '返修' when 1 then '送修' END client_type,
        case
        when ce.crm_id is null then '否'
        when ce.repeat_son_lead_id is null then '否'
        when convert(ce.crm_id,char)= convert(ce.repeat_son_lead_id,char) then '否'
        else '是' end repeat_lead,
        case when t.into_dealer_code is null then '尚未留修'
             when t.into_dealer_code = '' then '尚未留修'
            when t.into_dealer_code=t.dealer_code then '本店留修'
            when t.into_dealer_code!=t.dealer_code then '他店留修' END repair_situation,
        t.booking_order_no AS booking_order_no
    FROM
        tt_accident_clues t
        LEFT JOIN tt_accident_clues_ext ce ON t.ac_id = ce.ac_id
        where t.is_deleted=0
        <if test="params.dealerCode !='' and params.dealerCode != null ">
            and t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" 'vcdc' != params.source">
            and t.dealer_code = #{params.dealerCode}
        </if>
        <if test="params.license != null and params.license != '' ">
            and t.license like "%" #{params.license} "%"
        </if>
        <if test="params.reportDateStart != null">
            and t.report_date >= #{params.reportDateStart}
        </if>
        <if test="params.reportDateEnd != null ">
            and t.report_date <![CDATA[<]]> #{params.reportDateEnd}
        </if>
        <if test="params.insuranceCompanyId != null and params.insuranceCompanyId != '' ">
            and t.insurance_company_id = #{params.insuranceCompanyId}
        </if>
        <if test="params.insuranceCompanyName != null and params.insuranceCompanyName != '' ">
            and t.insurance_company_name = #{params.insuranceCompanyName}
        </if>
        <if test="params.followStatus != null and params.followStatus != '' ">
            and t.follow_status= #{params.followStatus}
        </if>
        <if test="params.cluesStatus != null and params.cluesStatus != '' ">
            and t.clues_status= #{params.cluesStatus}
        </if>
        <if test="params.nextFollowDateStart != null">
            and t.next_follow_date >= #{params.nextFollowDateStart}
        </if>
        <if test="params.nextFollowDateEnd != null ">
            and t.next_follow_date <![CDATA[<]]> #{params.nextFollowDateEnd}
        </if>
        <if test="params.followPeople != null and params.followPeople !='' ">
            and t.follow_People=#{params.followPeople}
        </if>
        <if test="25651001 == params.allotStatus">
            and (t.follow_people is null or t.follow_people = '' )
        </if>
        <if test="25651002 == params.allotStatus">
            and t.follow_people is not null
        </if>
        <if test="params.acIdList != null and params.acIdList.size() > 0">
            and t.ac_id in
            <foreach collection="params.acIdList" separator="," close=")" open="(" item="id">
                #{id}
            </foreach>
        </if>
        <if test="params.followStatusList !=null and params.followStatusList.size>0">
            and t.follow_status in
            <foreach collection="params.followStatusList" item="follow" open="(" close=")" separator=",">
                #{follow}
            </foreach>
        </if>
        <if test="params.createdDateStart!=null">
            and t.created_at &gt;=#{params.createdDateStart}
        </if>
        <if test="params.createdDateEnd!=null">
            and t.created_at &lt;#{params.createdDateEnd}
        </if>
        <if test="params.vin !=null and params.vin!=''">
            and t.vin=#{params.vin}
        </if>
        <if test="params.cluesResourceList!=null and params.cluesResourceList.size()>0">
            and t.clues_resource in
            <foreach collection="params.cluesResourceList" item="source" open="(" close=")" separator=",">
                #{source}
            </foreach>
        </if>
        <if test="params.afterBigAreaId != null">
            and t.after_big_area_id=#{params.afterBigAreaId}
        </if>
        <if test="params.afterSmallAreaId != null">
            and t.after_small_area_id=#{params.afterSmallAreaId}
        </if>
    </select>


    <insert id="batchInsert">
         insert into tt_accident_clues(
		   dealer_code,
			 license,
			 models_id,
			 contacts,
			 contacts_phone,
			 clues_resource,
			 outside_amount,
			 report_date,
			 accident_type,
			 is_bruise,
			 accident_address,
			 insurance_company_name,
			 remark,
			 created_by,
			 created_at,
			 record_version,
			 follow_status,
			 clues_status
		)
		select
		   dealer_code,
			 license,
			 models,
			 contacts,
			 contacts_phone,
			 clues_resource,
			 outside_amount,
			 report_date,
			 accident_type,
			 is_bruise,
			 accident_address,
			 insurance_company_name,
			 remark,
			 created_by,
		   now(),
			 1,
			 83531001,
			 83511002
		from tt_accident_clues_import
		where created_by = #{userId} and is_error =0

    </insert>

    <select id="getCountByLicense" resultType="java.lang.Integer">
        SELECT
			count(1)
		FROM
			tt_accident_clues
		where license =#{license} and dealer_code=#{dealerCode}
		and date(report_date) = date(#{reportDate})
    </select>

    <select id="checkClueIfExists" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            tt_accident_clues
        where license =#{license} and dealer_code=#{dealerCode}
          and (date(report_date) = date(#{reportDate})
          or report_date is null)
    </select>

     <update id="updateOneId" parameterType="java.util.List">
	  <foreach collection="oneIdList" item="item" index="index" separator=";">
	    update tt_accident_clues
	     <set >
		     <if test="item.id != null">
		          one_id = #{item.id},
		     </if>
			  updated_at = now()
	      </set>
	     where  contacts_phone = #{item.mobile} and one_id is null
	  </foreach>
	</update>

	<update id="updateTimeOut"> <!-- 跟进状态是“继续跟进” ，工单状态为“未完成 ”，最新的跟进记录的 创建时间超过14天 -->
	    update tt_accident_clues a
		INNER JOIN (
				select ac_id from tt_accident_clues
				where into_ro_no is null and clues_status in (83511002,83511004)
				and datediff(now(),created_at) > 14
				union
				  select a.ac_id from (SELECT
						c.ac_id,
						c.CREATED_AT,
						c.follow_status
					FROM
						tt_accident_clues_follow c
						INNER JOIN ( SELECT ac_id, max( follow_id ) follow_id FROM tt_accident_clues_follow b GROUP BY ac_id ) d ON c.follow_id  =d.follow_id
						) a
					INNER join tt_accident_clues b on a.ac_id=b.ac_id
					where b.clues_status in (83511002,83511004)   and b.into_ro_no is null
					and datediff(now(),a.created_at) > 14
		) b	on a.ac_id=b.ac_id
		set a.clues_status=83511005
	</update>
    <update id="updateTimeOutClues">
        update tt_accident_clues a
        INNER JOIN (
        select ac_id from tt_accident_clues
        where into_ro_no is null and clues_status = 83511002
        and follow_status = 83531001
        and datediff(now(),created_at) > #{expireDay}
        ) b	on a.ac_id=b.ac_id
        set a.clues_status=83511005,updated_at = now()
    </update>

	<select id="appointmentNotInto" resultMap="BaseResultMap">
	    select b.license,b.ac_id,b.report_date,a.appointment_into_date,b.created_by,b.dealer_code from (SELECT
				d.follow_id,
				c.ac_id,
				c.appointment_into_date,
				c.ro_no,
				c.follow_status
			FROM
				tt_accident_clues_follow c
				INNER JOIN ( SELECT ac_id, max( follow_id ) follow_id FROM tt_accident_clues_follow b GROUP BY ac_id ) d ON c.follow_id  =d.follow_id
				) a
			INNER join tt_accident_clues b on a.ac_id=b.ac_id
			where b.clues_status=83511002  and a.follow_status=83531003
			and a.appointment_into_date is not null and a.ro_no is null
			and a.appointment_into_date BETWEEN DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 10 MINUTE),'%Y-%m-%d %H:%i')
			and DATE_FORMAT(NOW(),'%Y-%m-%d %H:%i')
	</select>

	<select id="selectTimeoutNotFollow" resultMap="BaseResultMap">
	      select b.license,b.ac_id,b.report_date,a.next_follow_date,b.created_by,b.dealer_code,b.follow_people,b.created_at
	      from (SELECT
				d.follow_id,
				c.ac_id,
				c.ro_no,
				c.follow_status,
				c.next_follow_date
			FROM
				tt_accident_clues_follow c
				INNER JOIN ( SELECT ac_id, max( follow_id ) follow_id FROM tt_accident_clues_follow b GROUP BY ac_id ) d ON c.follow_id  =d.follow_id
				) a
			INNER join tt_accident_clues b on a.ac_id=b.ac_id
			where b.clues_status=83511002  and a.follow_status=83531002
			and a.next_follow_date BETWEEN DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 10 MINUTE),'%Y-%m-%d %H:%i')
			and DATE_FORMAT(NOW(),'%Y-%m-%d %H:%i')
            and b.remind IS NULL
	</select>

    <select id="advanceFollowList" resultMap="BaseResultMap">
        select b.license,b.ac_id,b.report_date,a.next_follow_date,b.created_by,b.dealer_code,b.follow_people,b.created_at from (SELECT
               d.follow_id,
               c.ac_id,
               c.ro_no,
               c.follow_status,
               c.next_follow_date
                 FROM
                     tt_accident_clues_follow c
                         INNER JOIN ( SELECT ac_id, max( follow_id ) follow_id FROM tt_accident_clues_follow b GROUP BY ac_id ) d ON c.follow_id  =d.follow_id
                ) a
            INNER join tt_accident_clues b on a.ac_id=b.ac_id
        where b.clues_status=83511002  and a.follow_status=83531002
        and a.next_follow_date BETWEEN DATE_FORMAT(DATE_ADD(NOW(),INTERVAL 30 MINUTE),'%Y-%m-%d %H:%i')
            and DATE_FORMAT(DATE_ADD(NOW(),INTERVAL 35 MINUTE),'%Y-%m-%d %H:%i')
    </select>

    <select id="getAppointmentTimeOutList" resultMap="BaseResultMap">
        select b.license,b.ac_id,b.report_date,a.next_follow_date,b.created_by,b.dealer_code,b.follow_people,a.appointment_into_date,b.created_at
        from (
        SELECT
        d.follow_id,
        c.ac_id,
        c.ro_no,
        c.follow_status,
        c.next_follow_date,
        c.appointment_into_date
        FROM
        tt_accident_clues_follow c
        INNER JOIN ( SELECT ac_id, max( follow_id ) follow_id FROM tt_accident_clues_follow b GROUP BY ac_id ) d ON c.follow_id  =d.follow_id
        ) a
        INNER join tt_accident_clues b on a.ac_id=b.ac_id
        where b.clues_status=83511002
        and a.follow_status=83531003
        and a.appointment_into_date <![CDATA[<]]> NOW()
    </select>

	<select id="nextAppintmentDataAccident" resultMap="BaseResultMap">
	     select b.license,b.ac_id,b.report_date,a.appointment_into_date,b.created_by,b.dealer_code from (SELECT
				d.follow_id,
				c.ac_id,
				c.appointment_into_date,
				c.ro_no,
				c.follow_status
			FROM
				tt_accident_clues_follow c
				INNER JOIN ( SELECT ac_id, max( follow_id ) follow_id FROM tt_accident_clues_follow b GROUP BY ac_id ) d ON c.follow_id  =d.follow_id
				) a
			INNER join tt_accident_clues b on a.ac_id=b.ac_id
			where b.clues_status=83511002  and a.follow_status=83531003 and remind is null
			and a.appointment_into_date is not null and a.ro_no is null
			and a.appointment_into_date BETWEEN DATE_FORMAT(date_add(now(), interval 50 minute),'%Y-%m-%d %H:%i')
			and DATE_FORMAT(date_add(now(),interval 70 minute),'%Y-%m-%d %H:%i')
	</select>

	<update id="updateRmind">

	    update tt_accident_clues a
         inner join (
        select b.license,b.ac_id,b.report_date,a.appointment_into_date,b.created_by,b.dealer_code from (SELECT
				d.follow_id,
				c.ac_id,
				c.appointment_into_date,
				c.ro_no,
				c.follow_status
			FROM
				tt_accident_clues_follow c
				INNER JOIN ( SELECT ac_id, max( follow_id ) follow_id FROM tt_accident_clues_follow b GROUP BY ac_id ) d ON c.follow_id  =d.follow_id
				) a
			INNER join tt_accident_clues b on a.ac_id=b.ac_id
			where b.clues_status=83511002  and a.follow_status=83531003 and remind is null
			and a.appointment_into_date is not null and a.ro_no is null
			and a.appointment_into_date BETWEEN DATE_FORMAT(date_add(now(), interval 50 minute),'%Y-%m-%d %H:%i')
			and DATE_FORMAT(date_add(now(),interval 70 minute),'%Y-%m-%d %H:%i')
			) b on a.ac_id=b.ac_id
			set remind = 1
	</update>

    <select id="getList" resultType="com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentClueVO">

        SELECT
        tt.ac_id,
        tt.follow_status,
        tt.license,
        tt.contacts,
        tt.contacts_phone,
        tt.report_date,
        tt.follow_people,
        tt.follow_people_name,
        tt.insurance_company_id,
        tt.insurance_company_name,
        tt.created_at,
        tt.accident_type,
        tt.clues_resource,
        tt.is_bruise,
        tt.clues_status,
        tt.next_follow_date,
        tt.accident_address,
        ext.crm_id,
        ext.regist_no,
        case
        when ext.crm_id is null then 10041002
        when ext.repeat_son_lead_id is null then 10041002
        when convert(ext.crm_id,char)=convert(ext.repeat_son_lead_id,char) then 10041002
        else 10041001 end repeatLead,
        ext.accident_date,
        ext.accident_reason,
        ext.contacts_name,
        ext.call_type,
        ext.call_police_flag,
        ext.source_channel,
        ext.model_name,
        ext2.insurance_source
        FROM tt_accident_clues tt
        LEFT JOIN tt_accident_clues_ext ext on ext.`ac_id` = tt.`ac_id`
        LEFT JOIN tt_accident_clues_ext_2 ext2 ON tt.ac_id = ext2.ac_id
        WHERE tt.dealer_code = #{params.dealerCode}
        AND tt.IS_DELETED = 0
        AND (ext.parent_crm_id = 0 or ext.parent_crm_id is null)
        AND tt.follow_status != 83531007
        <if test="params.subParam != null and params.subParam != ''">
            <choose>
                <when test="params.subParam.length() >= 17">
                    AND tt.VIN = #{params.subParam}
                </when>
                <otherwise>
                    AND (
                    tt.license LIKE concat(#{params.subParam},'%')
                    OR tt.VIN LIKE concat(#{params.subParam},'%')
                    <if test="contactAcIds != null and contactAcIds.size() > 0">
                        OR tt.AC_ID in
                        <foreach collection="contactAcIds" item="acId" open="(" separator="," close=")">
                            #{acId}
                        </foreach>
                    </if>
                    )
                </otherwise>
            </choose>
        </if>
        <if test="params.followIds != null and params.followIds.size() > 0">
            AND tt.FOLLOW_PEOPLE in
            <foreach collection="params.followIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="params.reportDateStart != null and params.reportDateStart != ''">
            AND tt.report_date <![CDATA[>=]]> concat(#{params.reportDateStart},' 00:00:00')
        </if>
        <if test="params.reportDateEnd != null and params.reportDateEnd != ''">
            AND tt.report_date <![CDATA[<=]]> concat(#{params.reportDateEnd},' 23:59:59')
        </if>
        <if test="params.nextFollowDateStart != null and params.nextFollowDateStart != ''">
            AND tt.next_follow_date <![CDATA[>=]]> concat(#{params.nextFollowDateStart},' 00:00:00')
        </if>
        <if test="params.nextFollowDateEnd != null and params.nextFollowDateEnd != ''">
            AND tt.next_follow_date <![CDATA[<=]]> concat(#{params.nextFollowDateEnd},' 23:59:59')
        </if>
        <if test="params.createdDateStart != null and params.createdDateStart != ''">
            AND tt.created_at <![CDATA[>=]]> concat(#{params.createdDateStart},' 00:00:00')
        </if>
        <if test="params.createdDateEnd != null and params.createdDateEnd != ''">
            AND tt.created_at <![CDATA[<=]]> concat(#{params.createdDateEnd},' 23:59:59')
        </if>
        <if test="params.followStatusList != null and params.followStatusList.size() > 0">
            AND tt.follow_status in
            <foreach collection="params.followStatusList" item="followStatus" open="(" separator="," close=")">
                #{followStatus}
            </foreach>
        </if>
        <if test="params.cluesStatusList != null and params.cluesStatusList.size() > 0">
            AND tt.clues_status in
            <foreach collection="params.cluesStatusList" item="cluesStatus" open="(" separator="," close=")">
                #{cluesStatus}
            </foreach>
        </if>
        <if test="params.insuranceCodeList != null and params.insuranceCodeList.size() > 0">
            AND tt.insurance_company_id in
            <foreach collection="params.insuranceCodeList" item="insuranceCode" open="(" separator="," close=")">
                #{insuranceCode}
            </foreach>
        </if>
        <if test="userList!=null and userList.size()>0">
            and  (   tt.follow_people in
            <foreach collection="userList" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
            or tt.created_by in
            <foreach collection="userList" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
            )
        </if>
        <if test="limitDate != null and limitDate != ''">
            AND tt.CREATED_AT >= #{limitDate}
        </if>
        <if test="limitDate != null and limitDate != ''">
            AND tt.CREATED_AT >= #{limitDate}
        </if>
        <if test="params.insuranceSource !=null and params.insuranceSource != ''">
            and ext2.insurance_source = #{params.insuranceSource}
        </if>
        <if test="params.insuranceSourceList !=null and params.insuranceSourceList.size>0">
            and ext2.insurance_source in
            <foreach collection="params.insuranceSourceList" item="insuranceSource" open="(" close=")" separator=",">
                #{insuranceSource}
            </foreach>
        </if>
        order by ${orderByStr}
    </select>

    <select id="followCount"
            resultType="com.yonyou.dmscus.customer.entity.vo.accidentClues.AccidentClueFollowVo">
        SELECT
        sum(case when tt.follow_status=83531001 then 1 else 0 end) as notFollow,
        sum(case when tt.follow_status=83531002 then 1 else 0 end) as keepOnFollow,
        sum(case when tt.follow_status=83531005 then 1 else 0 end) as followTimeOut,
        sum(1) as total
        FROM tt_accident_clues tt
        LEFT JOIN tt_accident_clues_ext ext on ext.`ac_id` = tt.`ac_id`
        LEFT JOIN tt_accident_clues_ext_2 ext2 on ext2.`ac_id` = tt.`ac_id`
        WHERE tt.dealer_code = #{params.dealerCode}
        AND tt.IS_DELETED = 0
        AND (ext.parent_crm_id = 0 or ext.parent_crm_id is null)
        <if test="params.subParam != null and params.subParam != ''">
            <choose>
                <when test="params.subParam.length() >= 17">
                    AND tt.VIN = #{params.subParam}
                </when>
                <otherwise>
                    AND (
                    tt.license LIKE concat(#{params.subParam},'%')
                    OR tt.VIN LIKE concat(#{params.subParam},'%')
                    <if test="contactAcIds != null and contactAcIds.size() > 0">
                        OR tt.AC_ID in
                        <foreach collection="contactAcIds" item="acId" open="(" separator="," close=")">
                            #{acId}
                        </foreach>
                    </if>
                    )
                </otherwise>
            </choose>
        </if>
        <if test="params.followIds != null and params.followIds.size() > 0">
            AND tt.FOLLOW_PEOPLE in
            <foreach collection="params.followIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="params.reportDateStart != null and params.reportDateStart != ''">
            AND tt.report_date <![CDATA[>=]]> concat(#{params.reportDateStart},' 00:00:00')
        </if>
        <if test="params.reportDateEnd != null and params.reportDateEnd != ''">
            AND tt.report_date <![CDATA[<=]]> concat(#{params.reportDateEnd},' 23:59:59')
        </if>
        <if test="params.nextFollowDateStart != null and params.nextFollowDateStart != ''">
            AND tt.next_follow_date <![CDATA[>=]]> concat(#{params.nextFollowDateStart},' 00:00:00')
        </if>
        <if test="params.nextFollowDateEnd != null and params.nextFollowDateEnd != ''">
            AND tt.next_follow_date <![CDATA[<=]]> concat(#{params.nextFollowDateEnd},' 23:59:59')
        </if>
        <if test="params.createdDateStart != null and params.createdDateStart != ''">
            AND tt.created_at <![CDATA[>=]]> concat(#{params.createdDateStart},' 00:00:00')
        </if>
        <if test="params.createdDateEnd != null and params.createdDateEnd != ''">
            AND tt.created_at <![CDATA[<=]]> concat(#{params.createdDateEnd},' 23:59:59')
        </if>
        <if test="params.followStatusList != null and params.followStatusList.size() > 0">
            AND tt.follow_status in
            <foreach collection="params.followStatusList" item="followStatus" open="(" separator="," close=")">
                #{followStatus}
            </foreach>
        </if>
        <if test="params.cluesStatusList != null and params.cluesStatusList.size() > 0">
            AND tt.clues_status in
            <foreach collection="params.cluesStatusList" item="cluesStatus" open="(" separator="," close=")">
                #{cluesStatus}
            </foreach>
        </if>
        <if test="params.insuranceCodeList != null and params.insuranceCodeList.size() > 0">
            AND tt.insurance_company_id in
            <foreach collection="params.insuranceCodeList" item="insuranceCode" open="(" separator="," close=")">
                #{insuranceCode}
            </foreach>
        </if>
        <if test="params.userList!=null and params.userList.size()>0">
            and  (   tt.follow_people in
            <foreach collection="params.userList" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
            or tt.created_by in
            <foreach collection="params.userList" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
            )
        </if>
        <if test="limitDate != null and limitDate != ''">
            AND tt.CREATED_AT >= #{limitDate}
        </if>
        <if test="params.insuranceSourceList != null and params.insuranceSourceList.size() > 0">
            AND ext2.insurance_source in
            <foreach collection="params.insuranceSourceList" item="insuranceSource" open="(" separator="," close=")">
                #{insuranceSource}
            </foreach>
        </if>
    </select>

    <select id="getUserAllotCount" resultType="java.util.Map">
        select follow_people,count(1) ct

        FROM tt_accident_clues

        WHERE dealer_code = #{ownerCode}

        and   follow_status in (83531001,83531002)

        <choose>
        <when test="userSet!=null and userSet.size()>0">
          and   follow_people in
         <foreach collection="userSet"  item="it" open="(" close=")" separator=",">
             #{it}
         </foreach>
          group by follow_people
        </when>
        <otherwise>
            and 1!=1
        </otherwise>
        </choose>
    </select>

    <sql id="getListMainQuery">
        <if test="params.subParam != null and params.subParam != ''">
            <choose>
                <when test="params.subParam.length() >= 17">
                    AND c.VIN = #{params.subParam}
                </when>
                <otherwise>
                    AND (
                    c.license LIKE concat(#{params.subParam},'%')
                    OR c.VIN LIKE concat(#{params.subParam},'%')
                    <if test="contactAcIds != null and contactAcIds.size() > 0">
                        OR c.AC_ID in
                        <foreach collection="contactAcIds" item="acId" open="(" separator="," close=")">
                            #{acId}
                        </foreach>
                    </if>
                    )
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="getListDetailQuery">
        <if test="params.followIds != null and params.followIds.size() > 0">
            AND c.FOLLOW_PEOPLE in
            <foreach collection="params.followIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="params.reportDateStart != null and params.reportDateStart != ''">
            AND c.report_date <![CDATA[>=]]> concat(#{params.reportDateStart},' 00:00:00')
        </if>
        <if test="params.reportDateEnd != null and params.reportDateEnd != ''">
            AND c.report_date <![CDATA[<=]]> concat(#{params.reportDateEnd},' 23:59:59')
        </if>
        <if test="params.nextFollowDateStart != null and params.nextFollowDateStart != ''">
            AND c.next_follow_date <![CDATA[>=]]> concat(#{params.nextFollowDateStart},' 00:00:00')
        </if>
        <if test="params.nextFollowDateEnd != null and params.nextFollowDateEnd != ''">
            AND c.next_follow_date <![CDATA[<=]]> concat(#{params.nextFollowDateEnd},' 23:59:59')
        </if>
        <if test="params.createdDateStart != null and params.createdDateStart != ''">
            AND c.created_at <![CDATA[>=]]> concat(#{params.createdDateStart},' 00:00:00')
        </if>
        <if test="params.createdDateEnd != null and params.createdDateEnd != ''">
            AND c.created_at <![CDATA[<=]]> concat(#{params.createdDateEnd},' 23:59:59')
        </if>
        <if test="params.repeatLead!=null and params.repeatLead !=''">
            AND ce.repeat_lead =#{params.repeatLead}
        </if>
        <if test="params.followStatusList != null and params.followStatusList.size() > 0">
            AND c.follow_status in
            <foreach collection="params.followStatusList" item="followStatus" open="(" separator="," close=")">
                #{followStatus}
            </foreach>
        </if>
        <if test="params.cluesStatusList != null and params.cluesStatusList.size() > 0">
            AND c.clues_status in
            <foreach collection="params.cluesStatusList" item="cluesStatus" open="(" separator="," close=")">
                #{cluesStatus}
            </foreach>
        </if>
        <if test="params.insuranceCodeList != null and params.insuranceCodeList.size() > 0">
            AND c.insurance_company_id in
            <foreach collection="params.insuranceCodeList" item="insuranceCode" open="(" separator="," close=")">
                #{insuranceCode}
            </foreach>
        </if>
    </sql>

    <update id="updateClueRemind">
        update tt_accident_clues set remind = 1 where ac_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="queryDashBoardInfo" resultType="com.yonyou.dmscus.customer.entity.vo.AccidentClueDashBoardVO">
        select
        count(1) as totalCount,
        sum(case when follow_status = 83531001 then 1 else 0 end) as notFollowCount, /*未跟进数量*/
        sum(case when follow_status = 83531002 then 1 else 0 end) as followUpCount, /*跟进中数量*/
        sum(case when follow_status = 83531003 then 1 else 0 end) as successFollowCount, /*跟进成功数量*/
        sum(case when follow_status = 83531004 then 1 else 0 end) as followFailCount /*跟进失败数量*/
        from tt_accident_clues
        where dealer_code = #{dealerCode}
        and follow_people = #{followUser}
        and is_deleted = 0
        and created_at <![CDATA[ >= ]]> concat(#{beginDate},' 00:00:00')
        and created_at <![CDATA[ <= ]]> concat(#{endDate}, ' 23:59:59')
        group by dealer_code,follow_people
        order by dealer_code,follow_people asc
    </select>

    <select id="queryDashBoardDayDetail" resultType="com.yonyou.dmscus.customer.entity.vo.HistogramDataVO">
        select
        date(created_at),
        DATE_FORMAT(date(created_at), '%Y/%m/%d') as `histogramDate`,
        sum(case when follow_status = 83531003 then 1 else 0 end) as successCount, /*跟进成功数量*/
        sum(case when follow_status = 83531004 then 1 else 0 end) as failCount,  /*跟进失败数量*/
        sum(case when is_insured = 10041001 then 1 else 0 end) as isInsuranceCount,  /*本店承保数*/
        sum(case when is_insured = 10041001 and into_ro_no is not null and into_ro_no <![CDATA[ <> ]]> '' and into_dealer_code = dealer_code then 1 else 0 end) as isInsuranceRepairCount, /*本店承保维修数*/
        sum(case when is_insured = 10041002 then 1 else 0 end) as unInsuranceCount, /*非本店承保数*/
        sum(case when is_insured = 10041002 and into_ro_no is not null and into_ro_no <![CDATA[ <> ]]> '' and into_dealer_code = dealer_code then 1 else 0 end) as unInsuranceRepairCount /*非本店承保维修数*/
        from tt_accident_clues
        where dealer_code = #{dealerCode}
        and follow_people = #{followUser}
        and is_deleted = 0
        and created_at <![CDATA[ >= ]]> concat(#{beginDate},' 00:00:00')
        and created_at <![CDATA[ <= ]]> concat(#{endDate}, ' 23:59:59')
        group by date(created_at)
        order by date(created_at) asc
    </select>

    <select id="queryDashBoardMonthDetail" resultType="com.yonyou.dmscus.customer.entity.vo.HistogramDataVO">
        select
        year(created_at) as `year`,
        month(created_at) as month,
        concat(year(created_at),'/',month(created_at)) as `histogramDate`,
        sum(case when follow_status = 83531003 then 1 else 0 end) as successCount, /*跟进成功数量*/
        sum(case when follow_status = 83531004 then 1 else 0 end) as failCount, /*跟进失败数量*/
        sum(case when is_insured = 10041001 then 1 else 0 end) as isInsuranceCount,  /*本店承保数*/
        sum(case when is_insured = 10041001 and into_ro_no is not null and into_ro_no <![CDATA[ <> ]]> '' and into_dealer_code = dealer_code then 1 else 0 end) as isInsuranceRepairCount, /*本店承保维修数*/
        sum(case when is_insured = 10041002 then 1 else 0 end) as unInsuranceCount, /*非本店承保数*/
        sum(case when is_insured = 10041002 and into_ro_no is not null and into_ro_no <![CDATA[ <> ]]> '' and into_dealer_code = dealer_code then 1 else 0 end) as unInsuranceRepairCount /*非本店承保维修数*/
        from tt_accident_clues
        where dealer_code = #{dealerCode}
        and follow_people = #{followUser}
        and is_deleted = 0
        and created_at <![CDATA[ >= ]]> concat(#{beginDate},' 00:00:00')
        and created_at <![CDATA[ <= ]]> concat(#{endDate}, ' 23:59:59')
        group by `year`, month
        order by `year`, month asc
    </select>

    <select id="queryDealerDashBoardInfo" resultType="com.yonyou.dmscus.customer.entity.vo.AccidentClueDashBoardVO">
        select
            count(1) as totalCount,
            sum(case when follow_status = 83531001 then 1 else 0 end) as notFollowCount, /*未跟进数量*/
            sum(case when follow_status = 83531002 then 1 else 0 end) as followUpCount, /*跟进中数量*/
            sum(case when follow_status = 83531003 then 1 else 0 end) as successFollowCount, /*跟进成功数量*/
            sum(case when follow_status = 83531004 then 1 else 0 end) as followFailCount /*跟进失败数量*/
        from tt_accident_clues
        where dealer_code = #{dealerCode}
          and is_deleted = 0
          and created_at <![CDATA[ >= ]]> concat(#{beginDate}, ' 00:00:00')
          and created_at <![CDATA[ <= ]]> concat(#{endDate}, ' 23:59:59')
        group by dealer_code
        order by dealer_code asc
    </select>

    <select id="queryDashBoardUserDetail" resultType="com.yonyou.dmscus.customer.entity.vo.HistogramDataVO">
        select
            follow_people,
            follow_people_name,
            sum(case when follow_status = 83531003 then 1 else 0 end) as successCount, /*跟进成功数量*/
            sum(case when follow_status = 83531004 then 1 else 0 end) as failCount, /*跟进失败数量*/
            sum(case when is_insured = 10041001 then 1 else 0 end) as isInsuranceCount, /*本店承保数*/
            sum(case when is_insured = 10041001 and into_ro_no is not null and into_ro_no <![CDATA[ <> ]]> '' and into_dealer_code = dealer_code then 1 else 0 end) as isInsuranceRepairCount, /*本店承保维修数*/
            sum(case when is_insured = 10041002 then 1 else 0 end) as unInsuranceCount, /*非本店承保数*/
            sum(case when is_insured = 10041002 and into_ro_no is not null and into_ro_no <![CDATA[ <> ]]> '' and into_dealer_code = dealer_code then 1 else 0 end) as unInsuranceRepairCount /*非本店承保维修数*/
        from tt_accident_clues
        where dealer_code = #{dealerCode}
          and is_deleted = 0
          and created_at <![CDATA[ >= ]]> concat(#{beginDate},' 00:00:00')
          and created_at <![CDATA[ <= ]]> concat(#{endDate}, ' 23:59:59')
          and follow_people is not null
        group by follow_people
        order by follow_people asc
    </select>

    <select id="queryDealerDashBoardDayDetail" resultType="com.yonyou.dmscus.customer.entity.vo.HistogramDataVO">
        select
            date(created_at),
            DATE_FORMAT(date(created_at), '%Y/%m/%d') as `histogramDate`,
            sum(case when follow_status = 83531003 then 1 else 0 end) as successCount, /*跟进成功数量*/
            sum(case when follow_status = 83531004 then 1 else 0 end) as failCount, /*跟进失败数量*/
            sum(case when is_insured = 10041001 then 1 else 0 end) as isInsuranceCount, /*本店承保数*/
            sum(case when is_insured = 10041001 and into_ro_no is not null and into_ro_no <![CDATA[ <> ]]> '' and into_dealer_code = dealer_code then 1 else 0 end) as isInsuranceRepairCount, /*本店承保维修数*/
            sum(case when is_insured = 10041002 then 1 else 0 end) as unInsuranceCount, /*非本店承保数*/
            sum(case when is_insured = 10041002 and into_ro_no is not null and into_ro_no <![CDATA[ <> ]]> '' and into_dealer_code = dealer_code then 1 else 0 end) as unInsuranceRepairCount /*非本店承保维修数*/
        from tt_accident_clues
        where dealer_code = #{dealerCode}
          and is_deleted = 0
          and created_at <![CDATA[ >= ]]> concat(#{beginDate},' 00:00:00')
          and created_at <![CDATA[ <= ]]> concat(#{endDate}, ' 23:59:59')
        group by date(created_at)
        order by date(created_at) asc
    </select>

    <select id="queryDealerDashBoardMonthDetail" resultType="com.yonyou.dmscus.customer.entity.vo.HistogramDataVO">
        select
            year(created_at) as `year`,
            month(created_at) as month,
            concat(year(created_at),'/',month(created_at)) as `histogramDate`,
            sum(case when follow_status = 83531003 then 1 else 0 end) as successCount, /*跟进成功数量*/
            sum(case when follow_status = 83531004 then 1 else 0 end) as failCount, /*跟进失败数量*/
            sum(case when is_insured = 10041001 then 1 else 0 end) as isInsuranceCount, /*本店承保数*/
            sum(case when is_insured = 10041001 and into_ro_no is not null and into_ro_no <![CDATA[ <> ]]> '' and into_dealer_code = dealer_code then 1 else 0 end) as isInsuranceRepairCount, /*本店承保维修数*/
            sum(case when is_insured = 10041002 then 1 else 0 end) as unInsuranceCount, /*非本店承保数*/
            sum(case when is_insured = 10041002 and into_ro_no is not null and into_ro_no <![CDATA[ <> ]]> '' and into_dealer_code = dealer_code then 1 else 0 end) as unInsuranceRepairCount /*非本店承保维修数*/
        from tt_accident_clues
        where dealer_code = #{dealerCode}
          and is_deleted = 0
          and created_at <![CDATA[ >= ]]> concat(#{beginDate},' 00:00:00')
          and created_at <![CDATA[ <= ]]> concat(#{endDate}, ' 23:59:59')
        group by `year`, month
        order by `year`, month asc
    </select>
    <select id="selectClueWithoutExt"
            resultType="com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesDTO">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            tt_accident_clues t
                LEFT JOIN tt_accident_clues_ext e ON t.ac_id = e.ac_id
        WHERE
            t.is_deleted = 0
          AND e.ac_id IS NULL
    </select>

    <update id="updateBookingOrderNo">
        update tt_accident_clues t1
        JOIN tt_accident_clues_ext t2 on t1.ac_id = t2.ac_id
        set
        t1.booking_order_no = #{bookingOrderNo},t1.follow_status = #{followStatus}
        where
        t1.is_deleted =0
        and  t2.crm_id in
        <foreach collection="crmList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>
