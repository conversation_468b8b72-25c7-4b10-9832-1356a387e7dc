<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintFollowMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintFollowPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <result column="app_id" property="appId"/>
                                                        <result column="owner_code" property="ownerCode"/>
                                                        <result column="owner_par_code" property="ownerParCode"/>
                                                        <result column="org_id" property="orgId"/>
                                                                                    <result column="complaint_info_id" property="complaintInfoId"/>
                                                        <result column="object" property="object"/>
                                                        <result column="follow_time" property="followTime"/>
                                                        <result column="follower" property="follower"/>
                                                        <result column="follow_content" property="followContent"/>
                                                        <result column="ccm_not_publish" property="ccmNotPublish"/>
                                                        <result column="ccm_subject" property="ccmSubject"/>
                                                        <result column="status" property="status"/>
                                                        <result column="advise" property="advise"/>
                                                        <result column="ccm_part" property="ccmPart"/>
                                                        <result column="ccm_subdivision_part" property="ccmSubdivisionPart"/>
                                                        <result column="cc_main_reason" property="ccMainReason"/>
                                                        <result column="cc_result" property="ccResult"/>
                                                        <result column="keyword" property="keyword"/>
                                                        <result column="classification1" property="classification1"/>
                                                        <result column="classification2" property="classification2"/>
                                                        <result column="classification3" property="classification3"/>
                                                        <result column="classification4" property="classification4"/>
                                                        <result column="classification5" property="classification5"/>
                                                        <result column="classification6" property="classification6"/>
                                                        <result column="plan_follow_time" property="planFollowTime"/>
                                                        <result column="actuall_follow_time2" property="actuallFollowTime2"/>
                                                        <result column="data_sources" property="dataSources"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="is_valid" property="isValid"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                        <result column="quality_classification1" property="qualityClassification1"/>
                                                        <result column="quality_classification2" property="qualityClassification2"/>
                                                        <result column="quality_classification3" property="qualityClassification3"/>
                                                        <result column="quality_classification4" property="qualityClassification4"/>
                                                        <result column="fault_classification" property="faultClassification"/>
                                                        <result column="remark1" property="remark1"/>
                                                        <result column="remark2" property="remark2"/>
                                                        <result column="remark3" property="remark3"/>
                                                        <result column="remark4" property="remark4"/>
                                                        <result column="is_ccm" property="isCcm"/>
                                                        <result column="regional_manager_comments" property="regionalManagerComments"/>
                                                        <result column="follower_name" property="followerName"/>
                                                        <result column="dealer_not_publish" property="dealerNotPublish"/>
                                                        <result column="interface_push_type" property="interfacePushType"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, complaint_info_id, object, follow_time, follower, follow_content, ccm_not_publish, ccm_subject, status, advise, ccm_part, ccm_subdivision_part, cc_main_reason, cc_result, keyword, classification1, classification2, classification3, classification4, classification5, classification6, plan_follow_time, actuall_follow_time2, data_sources, is_deleted, is_valid, created_at, updated_at, quality_classification1, quality_classification2, quality_classification3, quality_classification4, fault_classification, remark1, remark2, remark3, remark4, is_ccm, regional_manager_comments, follower_name, dealer_not_publish, interface_push_type
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintFollowPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_follow t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.complaintInfoId !=null and params.complaintInfoId != '' ">
                AND t.complaint_info_id = #{params.complaintInfoId}
            </if>
                    <if test=" params.object !=null and params.object != '' ">
                AND t.object = #{params.object}
            </if>
                    <if test=" params.followTime !=null and params.followTime != '' ">
                AND t.follow_time = #{params.followTime}
            </if>
                    <if test=" params.follower !=null and params.follower != '' ">
                AND t.follower = #{params.follower}
            </if>
                    <if test=" params.followContent !=null and params.followContent != '' ">
                AND t.follow_content = #{params.followContent}
            </if>
                    <if test=" params.ccmNotPublish !=null and params.ccmNotPublish != '' ">
                AND t.ccm_not_publish = #{params.ccmNotPublish}
            </if>
                    <if test=" params.ccmSubject !=null and params.ccmSubject != '' ">
                AND t.ccm_subject = #{params.ccmSubject}
            </if>
                    <if test=" params.status !=null and params.status != '' ">
                AND t.status = #{params.status}
            </if>
                    <if test=" params.advise !=null and params.advise != '' ">
                AND t.advise = #{params.advise}
            </if>
                    <if test=" params.ccmPart !=null and params.ccmPart != '' ">
                AND t.ccm_part = #{params.ccmPart}
            </if>
                    <if test=" params.ccmSubdivisionPart !=null and params.ccmSubdivisionPart != '' ">
                AND t.ccm_subdivision_part = #{params.ccmSubdivisionPart}
            </if>
                    <if test=" params.ccMainReason !=null and params.ccMainReason != '' ">
                AND t.cc_main_reason = #{params.ccMainReason}
            </if>
                    <if test=" params.ccResult !=null and params.ccResult != '' ">
                AND t.cc_result = #{params.ccResult}
            </if>
                    <if test=" params.keyword !=null and params.keyword != '' ">
                AND t.keyword = #{params.keyword}
            </if>
                    <if test=" params.classification1 !=null and params.classification1 != '' ">
                AND t.classification1 = #{params.classification1}
            </if>
                    <if test=" params.classification2 !=null and params.classification2 != '' ">
                AND t.classification2 = #{params.classification2}
            </if>
                    <if test=" params.classification3 !=null and params.classification3 != '' ">
                AND t.classification3 = #{params.classification3}
            </if>
                    <if test=" params.classification4 !=null and params.classification4 != '' ">
                AND t.classification4 = #{params.classification4}
            </if>
                    <if test=" params.classification5 !=null and params.classification5 != '' ">
                AND t.classification5 = #{params.classification5}
            </if>
                    <if test=" params.classification6 !=null and params.classification6 != '' ">
                AND t.classification6 = #{params.classification6}
            </if>
                    <if test=" params.planFollowTime !=null and params.planFollowTime != '' ">
                AND t.plan_follow_time = #{params.planFollowTime}
            </if>
                    <if test=" params.actuallFollowTime2 !=null and params.actuallFollowTime2 != '' ">
                AND t.actuall_follow_time2 = #{params.actuallFollowTime2}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
                    <if test=" params.qualityClassification1 !=null and params.qualityClassification1 != '' ">
                AND t.quality_classification1 = #{params.qualityClassification1}
            </if>
                    <if test=" params.qualityClassification2 !=null and params.qualityClassification2 != '' ">
                AND t.quality_classification2 = #{params.qualityClassification2}
            </if>
                    <if test=" params.qualityClassification3 !=null and params.qualityClassification3 != '' ">
                AND t.quality_classification3 = #{params.qualityClassification3}
            </if>
                    <if test=" params.qualityClassification4 !=null and params.qualityClassification4 != '' ">
                AND t.quality_classification4 = #{params.qualityClassification4}
            </if>
                    <if test=" params.faultClassification !=null and params.faultClassification != '' ">
                AND t.fault_classification = #{params.faultClassification}
            </if>
                    <if test=" params.remark1 !=null and params.remark1 != '' ">
                AND t.remark1 = #{params.remark1}
            </if>
                    <if test=" params.remark2 !=null and params.remark2 != '' ">
                AND t.remark2 = #{params.remark2}
            </if>
                    <if test=" params.remark3 !=null and params.remark3 != '' ">
                AND t.remark3 = #{params.remark3}
            </if>
                    <if test=" params.remark4 !=null and params.remark4 != '' ">
                AND t.remark4 = #{params.remark4}
            </if>
                    <if test=" params.isCcm !=null and params.isCcm != '' ">
                AND t.is_ccm = #{params.isCcm}
            </if>
                    <if test=" params.regionalManagerComments !=null and params.regionalManagerComments != '' ">
                AND t.regional_manager_comments = #{params.regionalManagerComments}
            </if>
                    <if test=" params.followerName !=null and params.followerName != '' ">
                AND t.follower_name = #{params.followerName}
            </if>
                    <if test=" params.dealerNotPublish !=null and params.dealerNotPublish != '' ">
                AND t.dealer_not_publish = #{params.dealerNotPublish}
            </if>
                    <if test=" params.interfacePushType !=null and params.interfacePushType != '' ">
                AND t.interface_push_type = #{params.interfacePushType}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintFollowPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_follow t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.complaintInfoId !=null and params.complaintInfoId != '' ">
                AND t.complaint_info_id = #{params.complaintInfoId}
            </if>
                    <if test=" params.object !=null and params.object != '' ">
                AND t.object = #{params.object}
            </if>
                    <if test=" params.followTime !=null and params.followTime != '' ">
                AND t.follow_time = #{params.followTime}
            </if>
                    <if test=" params.follower !=null and params.follower != '' ">
                AND t.follower = #{params.follower}
            </if>
                    <if test=" params.followContent !=null and params.followContent != '' ">
                AND t.follow_content = #{params.followContent}
            </if>
                    <if test=" params.ccmNotPublish !=null and params.ccmNotPublish != '' ">
                AND t.ccm_not_publish = #{params.ccmNotPublish}
            </if>
                    <if test=" params.ccmSubject !=null and params.ccmSubject != '' ">
                AND t.ccm_subject = #{params.ccmSubject}
            </if>
                    <if test=" params.status !=null and params.status != '' ">
                AND t.status = #{params.status}
            </if>
                    <if test=" params.advise !=null and params.advise != '' ">
                AND t.advise = #{params.advise}
            </if>
                    <if test=" params.ccmPart !=null and params.ccmPart != '' ">
                AND t.ccm_part = #{params.ccmPart}
            </if>
                    <if test=" params.ccmSubdivisionPart !=null and params.ccmSubdivisionPart != '' ">
                AND t.ccm_subdivision_part = #{params.ccmSubdivisionPart}
            </if>
                    <if test=" params.ccMainReason !=null and params.ccMainReason != '' ">
                AND t.cc_main_reason = #{params.ccMainReason}
            </if>
                    <if test=" params.ccResult !=null and params.ccResult != '' ">
                AND t.cc_result = #{params.ccResult}
            </if>
                    <if test=" params.keyword !=null and params.keyword != '' ">
                AND t.keyword = #{params.keyword}
            </if>
                    <if test=" params.classification1 !=null and params.classification1 != '' ">
                AND t.classification1 = #{params.classification1}
            </if>
                    <if test=" params.classification2 !=null and params.classification2 != '' ">
                AND t.classification2 = #{params.classification2}
            </if>
                    <if test=" params.classification3 !=null and params.classification3 != '' ">
                AND t.classification3 = #{params.classification3}
            </if>
                    <if test=" params.classification4 !=null and params.classification4 != '' ">
                AND t.classification4 = #{params.classification4}
            </if>
                    <if test=" params.classification5 !=null and params.classification5 != '' ">
                AND t.classification5 = #{params.classification5}
            </if>
                    <if test=" params.classification6 !=null and params.classification6 != '' ">
                AND t.classification6 = #{params.classification6}
            </if>
                    <if test=" params.planFollowTime !=null and params.planFollowTime != '' ">
                AND t.plan_follow_time = #{params.planFollowTime}
            </if>
                    <if test=" params.actuallFollowTime2 !=null and params.actuallFollowTime2 != '' ">
                AND t.actuall_follow_time2 = #{params.actuallFollowTime2}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
                    <if test=" params.qualityClassification1 !=null and params.qualityClassification1 != '' ">
                AND t.quality_classification1 = #{params.qualityClassification1}
            </if>
                    <if test=" params.qualityClassification2 !=null and params.qualityClassification2 != '' ">
                AND t.quality_classification2 = #{params.qualityClassification2}
            </if>
                    <if test=" params.qualityClassification3 !=null and params.qualityClassification3 != '' ">
                AND t.quality_classification3 = #{params.qualityClassification3}
            </if>
                    <if test=" params.qualityClassification4 !=null and params.qualityClassification4 != '' ">
                AND t.quality_classification4 = #{params.qualityClassification4}
            </if>
                    <if test=" params.faultClassification !=null and params.faultClassification != '' ">
                AND t.fault_classification = #{params.faultClassification}
            </if>
                    <if test=" params.remark1 !=null and params.remark1 != '' ">
                AND t.remark1 = #{params.remark1}
            </if>
                    <if test=" params.remark2 !=null and params.remark2 != '' ">
                AND t.remark2 = #{params.remark2}
            </if>
                    <if test=" params.remark3 !=null and params.remark3 != '' ">
                AND t.remark3 = #{params.remark3}
            </if>
                    <if test=" params.remark4 !=null and params.remark4 != '' ">
                AND t.remark4 = #{params.remark4}
            </if>
                    <if test=" params.isCcm !=null and params.isCcm != '' ">
                AND t.is_ccm = #{params.isCcm}
            </if>
                    <if test=" params.regionalManagerComments !=null and params.regionalManagerComments != '' ">
                AND t.regional_manager_comments = #{params.regionalManagerComments}
            </if>
                    <if test=" params.followerName !=null and params.followerName != '' ">
                AND t.follower_name = #{params.followerName}
            </if>
                    <if test=" params.dealerNotPublish !=null and params.dealerNotPublish != '' ">
                AND t.dealer_not_publish = #{params.dealerNotPublish}
            </if>
                    <if test=" params.interfacePushType !=null and params.interfacePushType != '' ">
                AND t.interface_push_type = #{params.interfacePushType}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListByDealer" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintFollowPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_follow t
        WHERE 1=1
        <if test=" params.complaintInfoId !=null and params.complaintInfoId != '' ">
            AND t.complaint_info_id = #{params.complaintInfoId}
        </if>
        and t.is_deleted=0 and t.ccm_not_publish=1
    </select>
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListByVcdc" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintFollowPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_follow t
        WHERE 1=1
        <if test=" params.complaintInfoId !=null and params.complaintInfoId != '' ">
            AND t.complaint_info_id = #{params.complaintInfoId}
        </if>
        and t.is_deleted=0
    </select>

    <select id="queryNextFollowing" resultType="com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO">
        SELECT tci.complaint_id,tci.dealer_code,tci.region_manager,tci.subject,tcf.follow_time as followTime,tcf.created_by as followName from tt_sale_complaint_follow tcf
        left JOIN tt_sale_complaint_info  tci on tci.id=tcf.complaint_info_id
        where tcf.plan_follow_time is not null
        and date_add(NOW(),interval 60 minute)>=tcf.plan_follow_time
        and  date_add(NOW(),interval 50 minute)&lt;=tcf.plan_follow_time
    </select>

</mapper>
