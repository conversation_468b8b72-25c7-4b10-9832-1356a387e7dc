<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.qb.InviteQBVehicleImportMapper">

    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.qb.InviteQBVehicleImportPO">
        <result column="owner_code" property="ownerCode"/>
        <result column="app_id" property="appId"></result>
        <result column="dealer_code" property="dealerCode"></result>
        <result column="vin" property="vin"></result>
        <result column="qb_number" property="qbNumber"></result>
        <result column="is_error" property="isError"></result>
        <result column="error_msg" property="errorMsg"></result>
        <result column="line_number" property="lineNumber"></result>
        <result column="created_by" property="createdBy"></result>
    </resultMap>


    <delete id="deleteByCreatedBy" >
        delete from tt_invite_qb_vehicle_import where created_by =#{createdBy}
    </delete>

    <insert id="bulkInsert">
        insert into tt_invite_qb_vehicle_import(dealer_code,
        vin,qb_number,is_error,error_msg,line_number,
        created_by,created_at,record_version) values
        <foreach collection ="addList" item="dto" separator =",">
            (#{dto.dealerCode},
            #{dto.vin},#{dto.qbNumber},#{dto.isError},#{dto.errorMsg},#{dto.lineNumber}
            ,#{userId},now(),1)
        </foreach >
    </insert>

    <select id="queryError"
            resultMap="BaseResultMap">
        SELECT
        *
        FROM tt_invite_qb_vehicle_import t where t.is_error=1 and
        t.created_by=#{userId}
    </select>


    <select id="querySuccess"
            resultMap="BaseResultMap">
        SELECT
        *
        FROM tt_invite_qb_vehicle_import t where t.is_error=0 and
        t.created_by=#{userId}
    </select>

    <!-- 查询显示成功导入的数据条数 -->
    <select id="querySucessCount"
            resultType="java.lang.Integer"> SELECT count(1) FROM tt_invite_qb_vehicle_import t
        where t.is_error=0 and t.created_by=#{userId}
    </select>
    
     <select id="selectErrorPage"
            resultMap="BaseResultMap">
        SELECT
        *
        FROM tt_invite_qb_vehicle_import t where t.is_error=1 and
        t.created_by=#{userId}
    </select>


    <select id="selectSuccessPage"
            resultMap="BaseResultMap">
        SELECT
        *
        FROM tt_invite_qb_vehicle_import t where t.is_error=0 and
        t.created_by=#{userId}
    </select>
    
    <update id="updateErrorById">
        update tt_invite_qb_vehicle_import set is_error = 1 ,error_msg = 'VIN不存在'
        where created_by=#{userId} and vin in
         <foreach item="item" index="index" collection="list" 
                         open="(" separator="," close=")">
                        #{item}
                </foreach> 
    </update>


    <select id="selectMatchID"
            resultType="java.lang.Long">
        SELECT
            a.id
        FROM
            tt_qb_vehicle_info a
            LEFT JOIN tt_qb_number_info b on a.qb_number_id=b.qb_number_id
            INNER JOIN tt_invite_qb_vehicle_import t on a.vin =t.vin and a.dealer_code = t.dealer_code and b.qb_number = t.qb_number
        where a.is_performed = 10041002 and b.is_closed = 10041002
              and not exists(
                select 1 from tt_invite_vehicle_task  c where b.qb_number=c.qb_number
                                                              and a.vin=c.vin
        )
        and t.created_by=#{userId}
    </select>
</mapper>
