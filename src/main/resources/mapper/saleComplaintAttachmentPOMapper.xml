<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintAttachmentMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintAttachmentPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                        <result column="app_id" property="appId"/>
                                                        <result column="owner_code" property="ownerCode"/>
                                                        <result column="owner_par_code" property="ownerParCode"/>
                                                        <result column="org_id" property="orgId"/>
                                                                                    <result column="complaint_info_id" property="complaintInfoId"/>
                                                        <result column="object" property="object"/>
                                                        <result column="attachment_name" property="attachmentName"/>
                                                        <result column="url" property="url"/>
                                                        <result column="size" property="size"/>
                                                        <result column="data_sources" property="dataSources"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="is_valid" property="isValid"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                        <result column="complaint_no" property="complaintNo"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, complaint_info_id, object, attachment_name, url, size, data_sources, is_deleted, is_valid, created_at, updated_at, complaint_no
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintAttachmentPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_attachment t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.complaintInfoId !=null and params.complaintInfoId != '' ">
                AND t.complaint_info_id = #{params.complaintInfoId}
            </if>
                    <if test=" params.object !=null and params.object != '' ">
                AND t.object = #{params.object}
            </if>
                    <if test=" params.attachmentName !=null and params.attachmentName != '' ">
                AND t.attachment_name = #{params.attachmentName}
            </if>
                    <if test=" params.url !=null and params.url != '' ">
                AND t.url = #{params.url}
            </if>
                    <if test=" params.size !=null and params.size != '' ">
                AND t.size = #{params.size}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
                    <if test=" params.complaintNo !=null and params.complaintNo != '' ">
                AND t.complaint_no = #{params.complaintNo}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintAttachmentPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_attachment t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.complaintInfoId !=null and params.complaintInfoId != '' ">
                AND t.complaint_info_id = #{params.complaintInfoId}
            </if>
                    <if test=" params.object !=null and params.object != '' ">
                AND t.object = #{params.object}
            </if>
                    <if test=" params.attachmentName !=null and params.attachmentName != '' ">
                AND t.attachment_name = #{params.attachmentName}
            </if>
                    <if test=" params.url !=null and params.url != '' ">
                AND t.url = #{params.url}
            </if>
                    <if test=" params.size !=null and params.size != '' ">
                AND t.size = #{params.size}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
                    <if test=" params.complaintNo !=null and params.complaintNo != '' ">
                AND t.complaint_no = #{params.complaintNo}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql1" resultType="com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentTestDTO" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintAttachmentPO">
        SELECT
        t.id, t.complaint_info_id, t.object, t.attachment_name, t.url, t.size, t.data_sources,
        t.is_deleted, t.is_valid, t.created_at, t.updated_at,t.created_by as userId
        FROM tt_sale_complaint_attachment t
        WHERE 1=1
        <if test=" params.complaintNo !=null and params.complaintNo != '' ">
            AND t.complaint_no = #{params.complaintNo}
        </if>
        and t.is_deleted=0
    </select>

    <update id="updateAttachment">
        UPDATE tt_sale_complaint_attachment set complaint_info_id= #{params.id} where complaint_no=#{params.no} and is_deleted=0;
    </update>

</mapper>
