<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.voc.VocFunctionalStatusRecordMapper">
    <insert id="insertList" parameterType="com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusRecordPO">
        insert into  tt_voc_functional_status_record
        (
        vin,
        update_time,
        modify_time,
        activated_state,
        subscription_startdate,
        subscription_enddate
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.vin},
            #{item.updateTime},
            #{item.modifyTime},
            #{item.activatedState},
            #{item.subscriptionStartdate},
            #{item.subscriptionEnddate}
            )
        </foreach>

    </insert>

    <update id="updateFunctionalIsExecute" parameterType="java.util.List">
        update tt_voc_functional_status_record set
        is_execute = 1
        where id in
        <foreach collection="updateList" index="index" item="item" open="(" separator="," close=")">
        #{item.id}
        </foreach>
    </update>

    <update id="updateList" parameterType="java.util.List">
        <foreach collection="updateList" item="item" index="index" open="" close="" separator=";">
            update tt_voc_functional_status_record set
            update_time =  #{item.updateTime},
            modify_time =  #{item.modifyTime},
            activated_state = #{item.activatedState},
            subscription_startdate = #{item.subscriptionStartdate},
            subscription_enddate = #{item.subscriptionEnddate} ,
            is_execute = 0,
            is_deleted = 0,
            updated_at = now(),
            updated_by =-2,
            version = version+1 where id  = #{item.id}
        </foreach>
    </update>
    <select id="selectListStatusRecord" parameterType="java.util.List" resultType="com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusRecordPO">
        select
             id,
             vin,
             update_time,
             modify_time,
             activated_state,
             subscription_startdate,
             subscription_enddate
        from  tt_voc_functional_status_record
        where  1=1  and   is_deleted = 0 and  vin in
        <foreach collection="selectList" index="index" item="item" open="(" separator="," close=")">
            #{item.vin}
        </foreach>

    </select>

    <select id="selectListByVins" parameterType="java.util.List" resultType="com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusRecordPO">
        select
        id,
        vin,
        update_time,
        modify_time,
        activated_state,
        subscription_startdate,
        subscription_enddate
        from  tt_voc_functional_status_record
        where  1=1  and   is_deleted = 0 and  vin in
        <foreach collection="selectList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

</mapper>