<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.faultLight.TtFaultLightMqRecordMapper">

    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightMqRecordPO" >
        <result column="id" property="id" />
        <result column="icm_id" property="icmId" />
        <result column="biz_status" property="bizStatus" />
        <result column="follow_up_status" property="followUpStatus" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="created_at" property="createdAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="create_sqlby" property="createSqlby" />
        <result column="update_sqlby" property="updateSqlby" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
                icm_id,
                biz_status,
                follow_up_status,
                updatetime,
                is_deleted,
                created_at,
                created_by,
                updated_at,
                updated_by,
                create_sqlby,
                update_sqlby
    </sql>

</mapper>