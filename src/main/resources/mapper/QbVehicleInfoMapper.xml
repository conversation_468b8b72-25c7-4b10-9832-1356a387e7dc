<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.qb.QbVehicleInfoMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.dto.qb.QbVehicleInfoDTO">
                 <id column="id" property="id"/>                                                                                                                                                                                                                                                                                                                             <result column="qb_number_id" property="qbNumberId"/>
                 <result column="vin" property="vin"/>
                 <result column="dealer_code" property="dealerCode"/>
                 <result column="dealer_name" property="dealerName" />
                 <result column="is_performed" property="isPerformed"/>
                 <result column="performed_time" property="performedTime"/>
				<result column="APP_ID" property="appId"/>
				<result column="OWNER_CODE" property="ownerCode"/>
				<result column="OWNER_PAR_CODE" property="ownerParCode"/>
				<result column="ORG_ID" property="orgId"/>
				<result column="data_sources" property="dataSources"/>
				<result column="is_valid" property="isValid"/>
				<result column="is_deleted" property="isDeleted"/>
				<result column="created_at" property="createdAt"/>
				<result column="updated_at" property="updatedAt"/>
			    <result column="created_by" property="createdBy"/>

                <result column="qb_number" property="qbNumber"/>
                <result column="is_closed" property="isClosed"/>
         </resultMap>


    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.dto.qb.QbVehicleInfoDTO">
            SELECT
                a.id,
                b.qb_number,
                a.dealer_code,
                a.vin,
                b.is_closed,
                a.is_performed
            FROM
              tt_qb_vehicle_info a
            LEFT JOIN tt_qb_number_info b on a.qb_number_id=b.qb_number_id
            where a.is_performed = 10041002 and b.is_closed = 10041002
            and not exists(
                select 1 from tt_invite_vehicle_task  c where b.qb_number=c.qb_number
                and a.vin=c.vin
            )
            <if test=" params.qbNumber !=null and params.qbNumber != '' ">
				AND b.qb_number like "%" #{params.qbNumber} "%"
			</if>
			<if test=" params.isPerformed !=null and params.isPerformed != '' ">
				AND a.is_Performed = #{params.isPerformed}
			</if>
			<if test=" params.isClosed !=null and params.isClosed != '' ">
				AND b.is_closed = #{params.isClosed}
			</if>
			<if test=" params.vin !=null and params.vin != '' ">
				AND a.vin like "%" #{params.vin} "%"
			</if>
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
				AND a.dealer_code like "%" #{params.dealerCode} "%"
			</if>

      </select>


</mapper>
