<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.inviteSaAllocateRule.InviteSaAllocateRuleDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule.InviteSaAllocateRuleDetailPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="sa_id" property="saId"/>
        <result column="sa_name" property="saName"/>
        <result column="sa_code" property="saCode"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="is_insurance" property="isInsurance"/>
        <result column="is_other" property="isOther"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, dealer_code, sa_id, sa_name,sa_code, data_sources,
           is_deleted, is_valid, created_at, updated_at,is_insurance,is_other
        </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule.InviteSaAllocateRuleDetailPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_sa_allocate_rule_detail t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.sa_id = #{params.saId}
        </if>
        <if test=" params.saName !=null and params.saName != '' ">
            AND t.sa_name = #{params.saName}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule.InviteSaAllocateRuleDetailPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_sa_allocate_rule_detail t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.sa_id = #{params.saId}
        </if>
        <if test=" params.saName !=null and params.saName != '' ">
            AND t.sa_name = #{params.saName}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>


</mapper>
