<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyAuditInfoMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditInfoPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                    <result column="APP_ID" property="appId"/>
                                                        <result column="OWNER_CODE" property="ownerCode"/>
                                                        <result column="OWNER_PAR_CODE" property="ownerParCode"/>
                                                        <result column="ORG_ID" property="orgId"/>
                                                                                    <result column="goodwill_apply_id" property="goodwillApplyId"/>
                                                        <result column="is_audit" property="isAudit"/>
                                                        <result column="audit_result" property="auditResult"/>
                                                        <result column="notice_dealer_time" property="noticeDealerTime"/>
                                                        <result column="is_valid" property="isValid"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, goodwill_apply_id, is_audit, audit_result, notice_dealer_time, is_valid, is_deleted, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_audit_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.isAudit !=null and params.isAudit != '' ">
                AND t.is_audit = #{params.isAudit}
            </if>
                    <if test=" params.auditResult !=null and params.auditResult != '' ">
                AND t.audit_result = #{params.auditResult}
            </if>
                    <if test=" params.noticeDealerTime !=null and params.noticeDealerTime != '' ">
                AND t.notice_dealer_time = #{params.noticeDealerTime}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_audit_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.isAudit !=null and params.isAudit != '' ">
                AND t.is_audit = #{params.isAudit}
            </if>
                    <if test=" params.auditResult !=null and params.auditResult != '' ">
                AND t.audit_result = #{params.auditResult}
            </if>
                    <if test=" params.noticeDealerTime !=null and params.noticeDealerTime != '' ">
                AND t.notice_dealer_time = #{params.noticeDealerTime}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>
            
            <select id="selectByGoodwillId" resultType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditInfoPO"> 
            SELECT
	        <include refid="Base_Column_List"/>
	        FROM tt_goodwill_apply_audit_info t
	        WHERE 1=1 and goodwill_apply_id=#{goodwillApplyId}
            
            
            </select>

</mapper>
