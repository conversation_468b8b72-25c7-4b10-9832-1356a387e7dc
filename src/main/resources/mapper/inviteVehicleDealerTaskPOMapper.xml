<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.invitationCreate.InviteVehicleDealerTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="plan_no" property="planNo"/>
        <result column="invite_type" property="inviteType"/>
        <result column="invite_name" property="inviteName"/>
        <result column="follow_mode" property="followMode"/>
        <result column="daily_mileage" property="dailyMileage"/>
        <result column="plan_remind_date" property="planRemindDate"/>
        <result column="advise_in_date" property="adviseInDate"/>
        <result column="is_create_invite" property="isCreateInvite"/>
        <result column="invite_id" property="inviteId"/>
        <result column="distribution_person" property="distributionPerson"/>
        <result column="distribution_date" property="distributionDate"/>
        <result column="follow_content" property="followContent"/>
        <result column="follow_sa_id" property="followSaId"/>
        <result column="service_activity" property="serviceActivity"/>
        <result column="follow_status" property="followStatus"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
    <result column="created_name" property="createdName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, dealer_code, plan_no, invite_type, invite_name, follow_mode,
           daily_mileage, plan_remind_date, advise_in_date, is_create_invite, invite_id, distribution_person,
           distribution_date, follow_content, follow_sa_id, service_activity, follow_status, data_sources, is_deleted,
           is_valid,created_name, created_at, updated_at
        </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_vehicle_dealer_task t
        WHERE 1=1
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.inviteType !=null and params.inviteType != '' ">
            AND t.invite_type = #{params.inviteType}
        </if>
        <if test=" params.inviteName !=null and params.inviteName != '' ">
            AND t.invite_name  like "%"#{params.inviteName}"%"
        </if>
        <if test=" params.createdName !=null and params.createdName != '' ">
            AND t.created_name  like "%"#{params.createdName}"%"
        </if>
        <if test=" params.createdAtStart !=null and params.createdAtStart != '' ">
            AND t.created_at &gt;= CONCAT(#{params.createdAtStart},' 00:00:00')
        </if>
        <if test=" params.createdAtEnd !=null and params.createdAtEnd != '' ">
            AND t.created_at &lt;= CONCAT(#{params.createdAtEnd},' 23:59:59')
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_vehicle_dealer_task t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.planNo !=null and params.planNo != '' ">
            AND t.plan_no = #{params.planNo}
        </if>
        <if test=" params.inviteType !=null and params.inviteType != '' ">
            AND t.invite_type = #{params.inviteType}
        </if>
        <if test=" params.inviteName !=null and params.inviteName != '' ">
            AND t.invite_name = #{params.inviteName}
        </if>
        <if test=" params.followMode !=null and params.followMode != '' ">
            AND t.follow_mode = #{params.followMode}
        </if>
        <if test=" params.dailyMileage !=null and params.dailyMileage != '' ">
            AND t.daily_mileage = #{params.dailyMileage}
        </if>
        <if test=" params.planRemindDate !=null and params.planRemindDate != '' ">
            AND t.plan_remind_date = #{params.planRemindDate}
        </if>
        <if test=" params.adviseInDate !=null and params.adviseInDate != '' ">
            AND t.advise_in_date = #{params.adviseInDate}
        </if>
        <if test=" params.isCreateInvite !=null and params.isCreateInvite != '' ">
            AND t.is_create_invite = #{params.isCreateInvite}
        </if>
        <if test=" params.inviteId !=null and params.inviteId != '' ">
            AND t.invite_id = #{params.inviteId}
        </if>
        <if test=" params.distributionPerson !=null and params.distributionPerson != '' ">
            AND t.distribution_person = #{params.distributionPerson}
        </if>
        <if test=" params.distributionDate !=null and params.distributionDate != '' ">
            AND t.distribution_date = #{params.distributionDate}
        </if>
        <if test=" params.followContent !=null and params.followContent != '' ">
            AND t.follow_content = #{params.followContent}
        </if>
        <if test=" params.followSaId !=null and params.followSaId != '' ">
            AND t.follow_sa_id = #{params.followSaId}
        </if>
        <if test=" params.serviceActivity !=null and params.serviceActivity != '' ">
            AND t.service_activity = #{params.serviceActivity}
        </if>
        <if test=" params.followStatus !=null and params.followStatus != '' ">
            AND t.follow_status = #{params.followStatus}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <insert id="insertInvitationDlr" >
         insert into tt_invite_vehicle_dealer_task(
            app_id,
            dealer_code,
            plan_no,
            invite_type,
            invite_name,
            follow_mode,
            advise_in_date,
            created_by,
            created_at,
            record_version,
            created_name,
            data_sources
		)
        SELECT
            'volvo',
            t.dealer_code,
			#{planNo},
			82381010,
			t.invite_name,
			case t.follow_mode   when '电话' then 82391001
                                 when '短信' then 82391002
                                 when '沃世界' then 82391003
                                 when '微信' then 82391004
                                 when '问卷' then 82391005
                                 when '邮件' then 82391006
                                 when 'QQ' then 82391007
                                 when '其他' then 82391008
            else null
            end follow_mode,
            t.advise_in_date,
            t.created_by,
            now(),
            1,
            #{userCode},
            10451001
        from tt_invite_vehicle_dealer_task_import t
            where t.created_by=#{userId}
            order by t.created_at desc limit 1

    </insert>

    <insert id="insertInvitationVCDC">
        insert into tt_invite_vehicle_dealer_task(
            app_id,
            dealer_code,
            plan_no,
            invite_type,
            invite_name,
            advise_in_date,
            created_by,
            created_at,
            record_version,
            created_name,
            data_sources,
            owner_code
        )
            SELECT
                'volvo',
                t.dealer_code,
                #{planNo},
                82381011,
                t.invite_name,
                t.advise_in_date,
                t.created_by,
                now(),
                1,
                #{userCode},
                10451002,
                #{ownerCode}
            from tt_invite_vehicle_dealer_task_import t
            where t.created_by=#{userId}
            order by t.created_at desc limit 1
    </insert>
</mapper>
