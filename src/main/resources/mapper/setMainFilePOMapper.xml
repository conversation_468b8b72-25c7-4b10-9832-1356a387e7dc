<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.busSetting.SetMainFileMapper">




    <select id="querySetMainFileListNew" resultType="com.yonyou.dmscus.customer.entity.dto.busSetting.SetMainFileVO">
        SELECT
        tf.id,
        tf.set_code,
        tf.set_name,
        tf.set_type,tr.roType,
        tf.enable_date,CONVERT ((tt.partAmount+ts.labourAmount), DECIMAL(10,2))  as totalAmount
        FROM
        tt_set_main_file tf
        LEFT JOIN (
        SELECT
        set_id,
        sum(ifnull(a.quantity,0) * ifnull(b.unit_price,0)*ifnull(a.discount,0)/100)  as partAmount
        FROM
        tt_set_main_file_part a
        LEFT JOIN tm_parts_info b ON a.part_code = b.parts_no where  1=1 group  by   a.set_id
        ) tt ON tf.id = tt.set_id
        LEFT JOIN (SELECT
        c.set_id,
        sum(ifnull(c.labor_hour,0) * (select ifnull(LABOUR_PRICE, 0) from tm_labour_price_dlr where LABOUR_PRICE_CODE = '0001' and dealer_Code = #{params.dealerCode}) * ifnull(c.discount,0)/100)  as labourAmount
        FROM
        tt_set_main_file_item  c
        WHERE
        1 = 1
        GROUP BY
        c.set_id )ts ON tf.id = ts.set_id

        LEFT JOIN (
        SELECT t.set_id,GROUP_CONCAT(t.ro_type SEPARATOR ',') roType  FROM tt_set_main_file_ro_type t    GROUP BY t.set_id
        ) tr ON  tf.id = tr.set_id
        WHERE
        1 = 1
        AND  (tf.enable_date <![CDATA[<=]]> CURRENT_TIMESTAMP and DATE_FORMAT(tf.discontinue_date,'%Y-%m-%d')  >=DATE_FORMAT(CURRENT_TIMESTAMP,'%Y-%m-%d'))
        and tf.is_recommend=10041001
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND ( tf.dealer_code = #{params.dealerCode} OR tf.data_sources = 10451002)
        </if>
        <if test=" params.setType !=null and params.setType != '' ">
            AND tf.set_type = #{params.setType}
        </if>
        <if test=" params.setName !=null and params.setName != '' ">
            AND tf.set_name like  "%"#{params.setName}"%"
        </if>
        <if test=" params.setCode !=null and params.setCode != '' ">
            AND tf.set_code like  "%"#{params.setCode}"%"
        </if>

        <if test=" params.modelCode !=null and params.modelCode != '' ">
            AND (tf.model_code = #{params.modelCode} or tf.model_code = 'XXX')
        </if>
        <!-- 发动机 -->
        <if test=" params.engineCode !=null and params.engineCode != '' ">
            AND EXISTS (
            SELECT
            1
            FROM
            tt_set_main_file_engine_rule r
            WHERE
            r.set_id = tf.id
            AND (SUBSTRING(
            r.engine_code,
            LOCATE('，', r.engine_code) + 1
            ) IN (#{params.engineCode})
            or r.engine_code = '000'
            or r.engine_code = CONCAT(SUBSTRING(r.engine_code,1,LOCATE('，', r.engine_code)-1),'，All')
            )
            )
        </if>

        <!-- 进厂行驶里程 -->
        <if test="params.inMileage !=null and params.inMileage !=''">
            AND EXISTS (
            SELECT
            1
            FROM
            tt_set_main_file_mileage_rule tr
            WHERE
            tr.set_id = tf.id
            AND tr.mileage_upper_limit  <![CDATA[>=]]> #{params.inMileage}
            AND tr.mileage_lower_limit  <![CDATA[<=]]> #{params.inMileage}
            )
        </if>

    </select>


    <select id="querySetMainFileDetail" resultType="com.yonyou.dmscus.customer.entity.dto.busSetting.SetMainFileDetailVO">
        select
        p.part_code as code,
        p.part_name as name,
        p.quantity as quantity
        from  tt_set_main_file_part p
        where p.is_deleted=0 and p.set_id=#{id}
        UNION ALL
        select
        i.op_code as code,
        i.op_name as name,
        i.labor_hour as quantity
        from  tt_set_main_file_item i
        where i.is_deleted=0 and i.set_id=#{id}
    </select>



</mapper>
