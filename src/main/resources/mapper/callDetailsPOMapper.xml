<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.voicemanage.CallDetailsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="session_Id" property="sessionId"/>
        <result column="call_id" property="callId"/>
        <result column="display_number" property="displayNumber"/>
        <result column="caller_number" property="callerNumber"/>
        <result column="callee_number" property="calleeNumber"/>
        <result column="work_number" property="workNumber"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="call_length" property="callLength"/>
        <result column="service_type" property="serviceType"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="cus_name" property="cusName"/>
        <result column="cus_number" property="cusNumber"/>
        <result column="total_score" property="totalScore" />
        <result column="voice_url" property="voiceUrl"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        app_id, owner_code, owner_par_code, org_id, id, session_Id, call_id, display_number, caller_number,total_score,
        callee_number, work_number, start_time, end_time, call_length, service_type, data_sources, is_deleted, is_valid,dealer_code, created_at, updated_at
    </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_call_details t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.sessionId !=null and params.sessionId != '' ">
            AND t.session_Id = #{params.sessionId}
        </if>
        <if test=" params.callId !=null and params.callId != '' ">
            AND t.call_id = #{params.callId}
        </if>
        <if test=" params.displayNumber !=null and params.displayNumber != '' ">
            AND t.display_number = #{params.displayNumber}
        </if>
        <if test=" params.callerNumber !=null and params.callerNumber != '' ">
            AND t.caller_number = #{params.callerNumber}
        </if>
        <if test=" params.calleeNumber !=null and params.calleeNumber != '' ">
            AND t.callee_number = #{params.calleeNumber}
        </if>
        <if test=" params.workNumber !=null and params.workNumber != '' ">
            AND t.work_number = #{params.workNumber}
        </if>
        <if test=" params.startTime !=null and params.startTime != '' ">
            AND t.start_time = #{params.startTime}
        </if>
        <if test=" params.endTime !=null and params.endTime != '' ">
            AND t.end_time = #{params.endTime}
        </if>
        <if test=" params.callLength !=null and params.callLength != '' ">
            AND t.call_length = #{params.callLength}
        </if>
        <if test=" params.serviceType !=null and params.serviceType != '' ">
            AND t.service_type = #{params.serviceType}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_call_details t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.sessionId !=null and params.sessionId != '' ">
            AND t.session_Id = #{params.sessionId}
        </if>
        <if test=" params.callId !=null and params.callId != '' ">
            AND t.call_id = #{params.callId}
        </if>
        <if test=" params.displayNumber !=null and params.displayNumber != '' ">
            AND t.display_number = #{params.displayNumber}
        </if>
        <if test=" params.callerNumber !=null and params.callerNumber != '' ">
            AND t.caller_number = #{params.callerNumber}
        </if>
        <if test=" params.calleeNumber !=null and params.calleeNumber != '' ">
            AND t.callee_number = #{params.calleeNumber}
        </if>
        <if test=" params.workNumber !=null and params.workNumber != '' ">
            AND t.work_number = #{params.workNumber}
        </if>
        <if test=" params.startTime !=null and params.startTime != '' ">
            AND t.start_time = #{params.startTime}
        </if>
        <if test=" params.endTime !=null and params.endTime != '' ">
            AND t.end_time = #{params.endTime}
        </if>
        <if test=" params.callLength !=null and params.callLength != '' ">
            AND t.call_length = #{params.callLength}
        </if>
        <if test=" params.serviceType !=null and params.serviceType != '' ">
            AND t.service_type = #{params.serviceType}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <select id="getCallDetails"   resultMap="BaseResultMap">
      select b.cus_name,b.cus_number,a.call_length,a.session_Id,a.start_time,a.call_length,a.total_score,a.id,c.voice_url 
          from tt_call_details a
        left join tt_sa_customer_number b on  a.call_id=b.call_id
        left join tt_call_voice c on a.session_Id=c.session_Id
        where a.detail_id=#{detailId}
    </select>

    <insert id="insertCallDetails" parameterType="java.util.List">
      insert into tt_call_details(app_id,owner_code,owner_par_code,session_Id,call_id,display_number,caller_number,callee_number,work_number,start_time,end_time,call_length,service_type,dealer_code,created_by,record_version) values
      <foreach collection="allDetail" item="item" index="index" separator=",">
      ('volvo',#{item.ownerCode},#{item.ownerParCode},#{item.sessionId},#{item.callId},#{item.displayNbr},#{item.callerNbr},#{item.calleeNbr},#{item.workNbr},#{item.startDate},#{item.endDate},#{item.duration},#{item.serviceType},#{item.dealerCode},'-1',1)
      </foreach>
    </insert>
    
    <update id="updateTotalScore" parameterType="list">
       update tt_call_details
       <trim prefix="set" suffixOverrides=",">
            <trim prefix="total_score =case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.id!=null">
                        when id=#{item.id} 
                         then #{item.totalScore}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="updateList" item="item" index="index" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>
    
    <select id="selectLatest" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
            a.id,
            a.total_score,
            a.start_time,
            a.dealer_code,
            a.session_Id,
            a.call_length,
            a.call_id
        FROM
            tt_call_details a,
            tt_sa_customer_number b
        WHERE
            a.call_id = b.call_id
          and b.invite_id = #{id}
          and a.call_length >= 0
        ORDER BY a.created_at desc
            LIMIT 0,1
    </select>

    <select id="selectIsCurrentMonth" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        a.id,
        a.total_score,
        a.start_time,
        a.dealer_code,
        a.session_Id,
        a.call_length,
        a.call_id
        FROM
        tt_call_details a,
        tt_sa_customer_number b
        WHERE
        a.call_id = b.call_id
        and b.invite_id = #{id}
        and a.call_length >= 0
        and DATE_FORMAT(a.start_time, '%Y-%m') = DATE_FORMAT(now(), '%Y-%m')
        ORDER BY a.created_at desc
        LIMIT 0,1
    </select>
    
    
     <select id="getNotVoiceCallDetails" resultType="java.util.HashMap">
        SELECT a.session_Id,a.start_time,a.owner_code,a.owner_par_code,a.dealer_code,a.call_id from tt_call_details a
		 where is_deleted=0 
		 and session_Id is not null
			 and not EXISTS (
			 SELECT 1 from tt_call_voice b where a.session_Id = b.session_Id
	      )
    </select>


    <select id="selectNotTotalScore"  resultType="java.util.HashMap">
	    SELECT
            a.session_Id,
            a.dealer_code,
            b.start_time,
            b.id,
            b.created_at,
            a.call_id
        FROM
            tt_call_voice a
        LEFT JOIN tt_call_details b on a.session_id=b.session_id
        where a.is_send =10041001 and b.total_score is null and  a.call_id not  in ('')
        and a.created_at > DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        and a.created_at &lt;  DATE_ADD(CURDATE(), INTERVAL 1 DAY)
        order by a.created_at desc

    </select>


    <update id="updateCallDetailsForDetailId">
        update tt_call_details a,tt_sa_customer_number b
            set a.detail_id=#{detailId}
        where a.detail_id is null and a.call_id=b.call_id and b.invite_id=#{inviteId}
    </update>

    <select id="queryInviteCallDetails" resultType="java.lang.Long">
        select a.id
        from tt_call_details a
        inner join tt_sa_customer_number b on a.call_id = b.call_id
        and b.invite_id = #{inviteId}
        where a.detail_id is null
    </select>

    <update id="updateCallRecordsDetailId">
        update tt_call_details
        set detail_id = #{detailId}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateTwiceFollow">
        update tt_invite_vehicle_record_twice a
        set a.is_ai=#{isAi},a.is_followed = 1
        where a.invite_id=#{inviteId} and a.month = DATE_FORMAT(now(), '%Y-%m')
    </update>


    <select id="getAccidentCluesCallDetails"   resultMap="BaseResultMap">
      select b.cus_name,b.cus_number,a.call_length,a.session_Id,a.start_time,a.call_length,a.total_score,a.id,c.voice_url
          from tt_call_details a
        inner join tt_accident_clues_sa_number b on a.call_id=b.call_id
        left join tt_call_voice c on a.session_Id=c.session_Id
        where b.follow_id=#{followId}
    </select>
    
    
    <select id="getDetailsByInsuranceDetailId"   resultMap="BaseResultMap">
      select b.cus_name,b.cus_number,a.call_length,a.session_Id,a.start_time,a.call_length,a.total_score,a.id,c.voice_url
          from tt_call_details a
        inner join tt_invite_insurance_vehicle_customer_number b on a.call_id=b.call_id
        left join tt_call_voice c on a.session_Id=c.session_Id
        where b.insurance_detail_id=#{insuranceDetailId}
    </select>

    <select id="selectInviteIdAndInvitetype"  resultType="java.util.HashMap">
    select
        case WHEN  c.`invite_id`  is null then e.`insurance_id`  else c.`invite_id` end  as invite_id,
		CASE
				WHEN d.invite_type IS NULL THEN
				f.invite_type ELSE d.invite_type
			END AS invite_type
        from  tt_call_details b
        left join tt_sa_customer_number c on b.call_id=c.call_id
        left join tt_invite_vehicle_record d on c.invite_id=d.id
        LEFT JOIN tt_invite_insurance_vehicle_customer_number e ON b.call_id = e.call_id
	    LEFT JOIN tt_invite_insurance_vehicle_record f ON e.insurance_id = f.id
        where b.call_id =#{callId} and  b.session_Id   =  #{sessionId}
    </select>
    <select id="selectCallVoicePo"  resultType="java.util.HashMap">
	    SELECT
	    b.session_Id,b.start_time,b.created_at,b.data_sources,
            CASE
                WHEN
                    c.sa_id IS NOT NULL THEN
                        c.sa_id ELSE
                    CASE
                            WHEN e.sa_id IS NOT NULL THEN
                            e.sa_id ELSE h.sa_id
                        END
                        END AS sa_id
                    FROM
                        tt_call_details b
                        LEFT JOIN tt_sa_customer_number c ON b.call_id = c.call_id
                        LEFT JOIN tt_invite_insurance_vehicle_customer_number e ON b.call_id = e.call_id
                        LEFT JOIN tt_accident_clues_sa_number h ON h.call_id = b.call_id
                WHERE   b.id = #{id}
            limit 1
    </select>
    <select id="selectSaCustomerNumber"
            resultType="com.yonyou.dmscus.customer.entity.dto.voicemanage.SaCustomerNumberDTO">
        select a.insurance_id inviteid,"xb" saType,
         a.call_id, a.sa_id, a.cus_name, a.cus_number, a.data_sources, a.is_deleted, a.is_valid, a.created_by, a.created_at, a.updated_by, a.updated_at, a.record_version, a.dealer_code, a.sa_name, a.sa_number, a.work_number, a.mark, a.batch_no,
         b.session_Id, b.call_id, b.display_number, b.caller_number, b.callee_number, b.work_number, b.start_time, b.end_time, b.call_length, b.service_type, b.total_score, b.data_sources, b.is_deleted, b.is_valid, b.created_by, b.created_at, b.updated_by, b.updated_at, b.record_version, b.dealer_code, b.mark, b.detail_id, b.call_type
        from tt_invite_insurance_vehicle_customer_number a
        left join tt_call_details b on a.call_id=b.call_id
        where a.batch_no=#{batchNo}
        union all
        select a.invite_id inviteid,"yy" saType,
            a.call_id, a.sa_id, a.cus_name, a.cus_number, a.data_sources, a.is_deleted, a.is_valid, a.created_by, a.created_at, a.updated_by, a.updated_at, a.record_version, a.dealer_code, a.sa_name, a.sa_number, a.work_number, a.mark, a.batch_no,
            b.session_Id, b.call_id, b.display_number, b.caller_number, b.callee_number, b.work_number, b.start_time, b.end_time, b.call_length, b.service_type, b.total_score, b.data_sources, b.is_deleted, b.is_valid, b.created_by, b.created_at, b.updated_by, b.updated_at, b.record_version, b.dealer_code, b.mark, b.detail_id, b.call_type
        from tt_sa_customer_number
        left join tt_call_details b on a.call_id=b.call_id
        where batch_no=#{batchNo}
        union all
        select a.invite_id inviteid,"gzd" saType,
               a.call_id, a.sa_id, a.cus_name, a.cus_number, a.data_sources, a.is_deleted, a.is_valid, a.created_by, a.created_at, a.updated_by, a.updated_at, a.record_version, a.dealer_code, a.sa_name, a.sa_number, a.work_number, a.mark, a.batch_no,
        b.session_Id, b.call_id, b.display_number, b.caller_number, b.callee_number, b.work_number, b.start_time, b.end_time, b.call_length, b.service_type, b.total_score, b.data_sources, b.is_deleted, b.is_valid, b.created_by, b.created_at, b.updated_by, b.updated_at, b.record_version, b.dealer_code, b.mark, b.detail_id, b.call_type
        from tt_fault_call_register a
        left join tt_fault_call_details b on a.call_id=b.call_id
        where a.batch_no=#{batchNo}
    </select>

    <select id="selectSaCustomerNumbers"
            resultType="com.yonyou.dmscus.customer.entity.dto.voicemanage.SaCustomerNumberDTO">
        select a.insurance_id inviteid,"xb" saType,
        a.call_id, a.sa_id, a.cus_name, a.cus_number, a.data_sources, a.is_deleted, a.is_valid, a.created_by, a.created_at, a.updated_by, a.updated_at, a.record_version, a.dealer_code, a.sa_name, a.sa_number, a.work_number, a.mark, a.batch_no,
        b.session_Id, b.call_id, b.display_number, b.caller_number, b.callee_number, b.work_number, b.start_time, b.end_time, b.call_length, b.service_type, b.total_score, b.data_sources, b.is_deleted, b.is_valid, b.created_by, b.created_at, b.updated_by, b.updated_at, b.record_version, b.dealer_code, b.mark, b.detail_id, b.call_type
        from tt_invite_insurance_vehicle_customer_number a
        left join tt_call_details b on a.call_id=b.call_id
        where a.insurance_detail_id is null and  a.insurance_id in
        <foreach collection="xbIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        union all

        select a.invite_id inviteid,"yy" saType,
        a.call_id, a.sa_id, a.cus_name, a.cus_number, a.data_sources, a.is_deleted, a.is_valid, a.created_by, a.created_at, a.updated_by, a.updated_at, a.record_version, a.dealer_code, a.sa_name, a.sa_number, a.work_number, a.mark, a.batch_no,
        b.session_Id, b.call_id, b.display_number, b.caller_number, b.callee_number, b.work_number, b.start_time, b.end_time, b.call_length, b.service_type, b.total_score, b.data_sources, b.is_deleted, b.is_valid, b.created_by, b.created_at, b.updated_by, b.updated_at, b.record_version, b.dealer_code, b.mark, b.detail_id, b.call_type
        from tt_sa_customer_number a
        left join tt_call_details b on a.call_id=b.call_id
        where  a.detail_id is null and a.invite_id in
        <foreach collection="yyIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        union all

        select a.invite_id inviteid,"gzd" saType,
        a.call_id, a.sa_id, a.cus_name, a.cus_number, a.data_sources, a.is_deleted, a.is_valid, a.created_by, a.created_at, a.updated_by, a.updated_at, a.record_version, a.dealer_code, a.sa_name, a.sa_number, a.work_number, a.mark, a.batch_no,
        b.session_Id, b.call_id, b.display_number, b.caller_number, b.callee_number, b.work_number, b.start_time, b.end_time, b.call_length, b.service_type, b.total_score, b.data_sources, b.is_deleted, b.is_valid, b.created_by, b.created_at, b.updated_by, b.updated_at, b.record_version, b.dealer_code, b.mark, b.detail_id, b.call_type
        from tt_fault_call_register a
        left join tt_fault_call_details b on a.call_id=b.call_id
        where  a.detail_id is null and a.invite_id in
        <foreach collection="gzdIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>
