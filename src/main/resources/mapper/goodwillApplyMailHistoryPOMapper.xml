<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyMailHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyMailHistoryPO">
        <id column="id" property="id"/>
        <result column="APP_ID" property="appId"/>
        <result column="OWNER_CODE" property="ownerCode"/>
        <result column="OWNER_PAR_CODE" property="ownerParCode"/>
        <result column="ORG_ID" property="orgId"/>
        <result column="goodwill_apply_id" property="goodwillApplyId"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="mail_type" property="mailType"/>
        <result column="send_by" property="sendBy"/>
        <result column="receiver_mail" property="receiverMail"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="send_status" property="sendStatus"/>
        <result column="send_time" property="sendTime"/>
        <result column="is_valid" property="isValid"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="vin" property="vin"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, goodwill_apply_id, dealer_code, mail_type, send_by,
        receiver_mail, title, content, send_status, send_time, is_valid, is_deleted, created_at, updated_at
    </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyMailHistoryPO">
        SELECT
        DISTINCT   t.id
            ,t.APP_ID
            ,t.OWNER_CODE
            ,t.OWNER_PAR_CODE
            ,t.ORG_ID
            ,t.goodwill_apply_id
            ,t.dealer_code
            ,t.mail_type
            ,t.send_by
            ,t.receiver_mail
            ,t.title
            ,t.content
            ,t.send_status
            ,t.send_time
            ,t.is_valid
            ,t.is_deleted
            ,t.created_at
            ,t.updated_at
            ,s.vin
            ,s.apply_no
            ,s.dealer_name
            ,s.area_Manage areaManage
            ,s.small_area smallArea
        FROM tt_goodwill_apply_mail_history t
        left join tt_goodwill_apply_info s
        on t.goodwill_apply_id=s.id
        left join   tt_goodwill_audit_info tau  on  s.id=tau.goodwill_apply_id and  tau.audit_object =0 and tau.is_deleted =0 and tau.audit_role= 'SHQYJL'
        WHERE 1=1

        <if test=" params.applyNo!=null and params.applyNo != '' ">
            AND s.apply_no like CONCAT('%',#{params.applyNo},'%')
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND (s.dealer_code  like CONCAT('%',#{params.dealerCode},'%') or s.dealer_name  like CONCAT('%',#{params.dealerCode},'%'))
        </if>
        <if test=" params.blocId !=null and params.blocId != '' ">
            AND s.bloc = #{params.blocId}
        </if>
        <if test=" params.areaManage !=null and params.areaManage != '' ">
            AND s.area_Manage_Id IN (${params.areaManage})
        </if>
        <if test=" params.smallArea !=null and params.smallArea != '' ">
            AND s.small_area_id IN (${params.smallArea})
        </if>
        <if test=" params.auditName1 !=null and params.auditName1.size > 0 ">
            AND tau.audit_name in
            <foreach collection="params.auditName1" item="audit_name" open="(" separator="," close=")">
                #{audit_name}
            </foreach>
        </if>
        <if test=" params.auditName !=null and params.auditName != '' ">
            AND tau.audit_name = #{ params.auditName }
        </if>
        <if test=" params.mailType !=null and params.mailType != '' ">
            AND t.mail_type = #{params.mailType}
        </if>

        <if test=" params.sendStatus !=null and params.sendStatus != '' ">
            AND t.send_status = #{params.sendStatus}
        </if>
        <if test=" params.createdStartAt !=null ">
            AND DATE_FORMAT(t.send_time, '%Y-%m-%d') <![CDATA[>=]]>#{params.createdStartAt}
        </if>

        <if test=" params.createdEndAt !=null ">
            AND DATE_FORMAT(t.send_time, '%Y-%m-%d') <![CDATA[<=]]>#{params.createdEndAt}
        </if>
            AND t.is_deleted = 0
		ORDER BY SEND_TIME DESC
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyMailHistoryPO">
        SELECT
        DISTINCT
             t.id
            ,t.APP_ID
            ,t.OWNER_CODE
            ,t.OWNER_PAR_CODE
            ,t.ORG_ID
            ,t.id
            ,t.goodwill_apply_id
            ,t.dealer_code
            ,t.mail_type
            ,t.send_by
            ,t.receiver_mail
            ,t.title
            ,t.content
            ,t.send_status
            ,t.send_time
            ,t.is_valid
            ,t.is_deleted
            ,t.created_at
            ,t.updated_at
            ,s.vin
            ,s.area_Manage areaManage
            ,s.small_area smallArea
            ,s.apply_no
            ,s.dealer_name
        FROM tt_goodwill_apply_mail_history t
        left join tt_goodwill_apply_info s
        on t.goodwill_apply_id=s.id
        left join   tt_goodwill_audit_info tau  on  s.id=tau.goodwill_apply_id and  tau.audit_object =0 and tau.is_deleted =0 and tau.audit_role= 'SHQYJL'
        WHERE 1=1

        <if test=" params.applyNo!=null and params.applyNo != '' ">
            AND s.apply_no like CONCAT('%',#{params.applyNo},'%')
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND (s.dealer_code  like CONCAT('%',#{params.dealerCode},'%') or s.dealer_name  like CONCAT('%',#{params.dealerCode},'%'))
        </if>
        <if test=" params.blocId !=null and params.blocId != '' ">
            AND s.bloc = #{params.blocId}
        </if>
        <if test=" params.areaManage !=null and params.areaManage != '' ">
            AND s.area_Manage_Id IN (${params.areaManage})
        </if>
        <if test=" params.smallArea !=null and params.smallArea != '' ">
            AND s.small_area_id IN (${params.smallArea})
        </if>
        <if test=" params.auditName1 !=null and params.auditName1.size > 0 ">
            AND tau.audit_name in
            <foreach collection="params.auditName1" item="audit_name" open="(" separator="," close=")">
                #{audit_name}
            </foreach>
        </if>
        <if test=" params.mailType !=null and params.mailType != '' ">
            AND t.mail_type = #{params.mailType}
        </if>

        <if test=" params.sendStatus !=null and params.sendStatus != '' ">
            AND t.send_status = #{params.sendStatus}
        </if>
        <if test=" params.createdStartAt !=null ">
            AND DATE_FORMAT(t.send_time, '%Y-%m-%d') <![CDATA[>=]]>#{params.createdStartAt}
        </if>

        <if test=" params.createdEndAt !=null ">
            AND DATE_FORMAT(t.send_time, '%Y-%m-%d') <![CDATA[<=]]>#{params.createdEndAt}
        </if>
            AND t.is_deleted = 0
		ORDER BY SEND_TIME DESC
    	</select>
    
    	<insert id="insertMailHistory" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyMailHistoryDTO">
	    INSERT INTO tt_goodwill_apply_mail_history (`OWNER_CODE`,`goodwill_apply_id`,`dealer_code`,
		`mail_type`,`send_by`,`receiver_mail`,`title`,`content`,`send_status`,`send_time`,
		`is_valid`,`is_deleted`,`created_at`,`created_by`,`updated_at`,`updated_by`,`record_version` 
		)VALUES('VCDC',#{params.goodwillApplyId},#{params.dealerCode},#{params.mailType},#{params.sendBy},#{params.receiverMail},#{params.title},#{params.content},
		#{params.sendStatus},now(),NULL,0,now(),'-1',now(),'-1',0 );
    	</insert>

		<select id="queryIsSendEmail" resultType="integer" >
        	SELECT
           count(id)
        	FROM tt_goodwill_apply_mail_history t
        	WHERE 1=1  and t.send_status=82771001
           and t.goodwill_apply_id=#{goodwillApplyId} and t.mail_type=#{mailType}
          </select>
</mapper>
