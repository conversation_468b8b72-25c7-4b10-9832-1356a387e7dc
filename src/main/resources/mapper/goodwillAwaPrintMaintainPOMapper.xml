<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillAwaPrintMaintainMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAwaPrintMaintainPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       <result column="APP_ID" property="appId"/>
                       <result column="OWNER_CODE" property="ownerCode"/>
                       <result column="OWNER_PAR_CODE" property="ownerParCode"/>
                       <result column="ORG_ID" property="orgId"/>
                       <result column="approval" property="approval"/>
                       <result column="requestor" property="requestor"/>
                       <result column="line_director" property="lineDirector"/>
                       <result column="cs_vp" property="csVp"/>
                       <result column="date_one" property="dateOne"/>
                       <result column="date_two" property="dateTwo"/>
                       <result column="date_three" property="dateThree"/>
                       <result column="cfo" property="cfo"/>
                       <result column="coo" property="coo"/>
                       <result column="md" property="md"/>
                       <result column="date_four" property="dateFour"/>
                       <result column="date_five" property="dateFive"/>
                       <result column="date_six" property="dateSix"/>
                       <result column="explain_one" property="explainOne"/>
                       <result column="explain_two" property="explainTwo"/>
                       <result column="is_valid" property="isValid"/>
                       <result column="is_deleted" property="isDeleted"/>
                       <result column="created_at" property="createdAt"/>
                       <result column="updated_at" property="updatedAt"/>
                  		 <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, approval, requestor, line_director, cs_vp, date_one, date_two, date_three, cfo, coo, md, date_four, date_five, date_six, explain_one, explain_two, is_valid, is_deleted, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAwaPrintMaintainPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tm_goodwill_awa_print_maintain t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.approval !=null and params.approval != '' ">
                AND t.approval = #{params.approval}
            </if>
                    <if test=" params.requestor !=null and params.requestor != '' ">
                AND t.requestor = #{params.requestor}
            </if>
                    <if test=" params.lineDirector !=null and params.lineDirector != '' ">
                AND t.line_director = #{params.lineDirector}
            </if>
                    <if test=" params.csVp !=null and params.csVp != '' ">
                AND t.cs_vp = #{params.csVp}
            </if>
                    <if test=" params.dateOne !=null and params.dateOne != '' ">
                AND t.date_one = #{params.dateOne}
            </if>
                    <if test=" params.dateTwo !=null and params.dateTwo != '' ">
                AND t.date_two = #{params.dateTwo}
            </if>
                    <if test=" params.dateThree !=null and params.dateThree != '' ">
                AND t.date_three = #{params.dateThree}
            </if>
                    <if test=" params.cfo !=null and params.cfo != '' ">
                AND t.cfo = #{params.cfo}
            </if>
                    <if test=" params.coo !=null and params.coo != '' ">
                AND t.coo = #{params.coo}
            </if>
                    <if test=" params.md !=null and params.md != '' ">
                AND t.md = #{params.md}
            </if>
                    <if test=" params.dateFour !=null and params.dateFour != '' ">
                AND t.date_four = #{params.dateFour}
            </if>
                    <if test=" params.dateFive !=null and params.dateFive != '' ">
                AND t.date_five = #{params.dateFive}
            </if>
                    <if test=" params.dateSix !=null and params.dateSix != '' ">
                AND t.date_six = #{params.dateSix}
            </if>
                    <if test=" params.explainOne !=null and params.explainOne != '' ">
                AND t.explain_one = #{params.explainOne}
            </if>
                    <if test=" params.explainTwo !=null and params.explainTwo != '' ">
                AND t.explain_two = #{params.explainTwo}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAwaPrintMaintainPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tm_goodwill_awa_print_maintain t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.approval !=null and params.approval != '' ">
                AND t.approval = #{params.approval}
            </if>
                    <if test=" params.requestor !=null and params.requestor != '' ">
                AND t.requestor = #{params.requestor}
            </if>
                    <if test=" params.lineDirector !=null and params.lineDirector != '' ">
                AND t.line_director = #{params.lineDirector}
            </if>
                    <if test=" params.csVp !=null and params.csVp != '' ">
                AND t.cs_vp = #{params.csVp}
            </if>
                    <if test=" params.dateOne !=null and params.dateOne != '' ">
                AND t.date_one = #{params.dateOne}
            </if>
                    <if test=" params.dateTwo !=null and params.dateTwo != '' ">
                AND t.date_two = #{params.dateTwo}
            </if>
                    <if test=" params.dateThree !=null and params.dateThree != '' ">
                AND t.date_three = #{params.dateThree}
            </if>
                    <if test=" params.cfo !=null and params.cfo != '' ">
                AND t.cfo = #{params.cfo}
            </if>
                    <if test=" params.coo !=null and params.coo != '' ">
                AND t.coo = #{params.coo}
            </if>
                    <if test=" params.md !=null and params.md != '' ">
                AND t.md = #{params.md}
            </if>
                    <if test=" params.dateFour !=null and params.dateFour != '' ">
                AND t.date_four = #{params.dateFour}
            </if>
                    <if test=" params.dateFive !=null and params.dateFive != '' ">
                AND t.date_five = #{params.dateFive}
            </if>
                    <if test=" params.dateSix !=null and params.dateSix != '' ">
                AND t.date_six = #{params.dateSix}
            </if>
                    <if test=" params.explainOne !=null and params.explainOne != '' ">
                AND t.explain_one = #{params.explainOne}
            </if>
                    <if test=" params.explainTwo !=null and params.explainTwo != '' ">
                AND t.explain_two = #{params.explainTwo}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>
            
                <select id="selectAwaPrintMaintainInfo" resultType="java.util.HashMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAwaPrintMaintainPO">
	            SELECT 
	             <include refid="Base_Column_List"/>
	 		
	            FROM tm_goodwill_awa_print_maintain t
	            WHERE 1=1 and (t.is_valid = 10011001 or t.is_valid is null)
	            and is_deleted=0
            </select>

</mapper>
