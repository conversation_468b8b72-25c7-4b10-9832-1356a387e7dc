<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.invitationKanban.InvitationKanbanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.yonyou.dmscus.customer.entity.dto.invitationKanban.InvitationKanbanInfoDTO">
        <result column="invite_type" property="inviteType"/>
        <result column="invite_count" property="inviteCount"/>
        <result column="invite_finish_count" property="inviteFinishCount"/>
        <result column="invite_rate" property="inviteRate"/>
        <result column="booking_count" property="bookingCount"/>
        <result column="order_count" property="orderCount"/>
        <result column="has_order_rate" property="hasOrderRate"/>
    </resultMap>



    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
          a.invite_type,a.invite_count,a.invite_finish_count,a.booking_count,case when a.invite_count=0 then 0.0 else
        a.invite_finish_count*1.0/a.invite_count end as invite_rate,0 as order_count,0.0 as has_order_rate
        </sql>


    <select id="getInvitationKanbanInfoByDealerCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        (
        select
        t.invite_type,
        SUM(CASE WHEN t.follow_status=82401005 THEN 0 ELSE 1 END) AS invite_count,
        SUM(CASE WHEN t.follow_status=82401002 THEN 1 ELSE 0 END) AS invite_finish_count,
        SUM(CASE WHEN t.is_book=1 THEN 1 ELSE 0 END) AS booking_count
        from tt_invite_vehicle_record t
        where
        DATE_FORMAT(t.plan_follow_date, '%Y-%m-%d %H:%i:%s')&gt;= CONCAT(#{params.monthNo},'-01 00:00:00')
        and DATE_FORMAT(t.plan_follow_date, '%Y-%m-%d %H:%i:%s')&lt;= CONCAT(#{params.monthNo},'-31 23:59:59')
        and t.dealer_code=#{params.dealerCode}
        GROUP by t.invite_type
        ) a
    </select>

    <select id="getInvitationKanbanInfoByDealers" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        (
        select
        t.invite_type,
        SUM(CASE WHEN t.follow_status=82401005 THEN 0 ELSE 1 END) AS invite_count,
        SUM(CASE WHEN t.follow_status=82401002 THEN 1 ELSE 0 END) AS invite_finish_count,
        SUM(CASE WHEN t.is_book=1 THEN 1 ELSE 0 END) AS booking_count
        from tt_invite_vehicle_record t
        where
        DATE_FORMAT(t.plan_follow_date, '%Y-%m-%d %H:%i:%s')&gt;= CONCAT(#{params.monthNo},'-01 00:00:00')
        and DATE_FORMAT(t.plan_follow_date, '%Y-%m-%d %H:%i:%s')&lt;= CONCAT(#{params.monthNo},'-31 23:59:59')
        AND t.dealer_code in
        <foreach collection="params.dealers" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP by t.invite_type
        ) a
    </select>

    <select id="getInvitationKanbanInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        (
        select
        t.invite_type,
        SUM(CASE WHEN t.follow_status=82401005 THEN 0 ELSE 1 END) AS invite_count,
        SUM(CASE WHEN t.follow_status=82401002 THEN 1 ELSE 0 END) AS invite_finish_count,
        SUM(CASE WHEN t.is_book=1 THEN 1 ELSE 0 END) AS booking_count
        from tt_invite_vehicle_record t
        where
        DATE_FORMAT(t.plan_follow_date, '%Y-%m-%d %H:%i:%s')&gt;= CONCAT(#{params.monthNo},'-01 00:00:00')
        and DATE_FORMAT(t.plan_follow_date, '%Y-%m-%d %H:%i:%s')&lt;= CONCAT(#{params.monthNo},'-31 23:59:59')
        GROUP by t.invite_type
        )  a
    </select>
</mapper>
