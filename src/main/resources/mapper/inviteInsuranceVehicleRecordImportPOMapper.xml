<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleRecordImportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordImportPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="vin" property="vin"/>
        <result column="license_plate_num" property="licensePlateNum"/>
        <result column="name" property="name"/>
        <result column="tel" property="tel"/>
        <result column="vi_finish_date" property="viFinishDate"/>
        <result column="clivta_finish_date" property="clivtaFinishDate"/>
        <result column="SA_ID" property="saId"/>
        <result column="SA_NAME" property="saName"/>
        <result column="LAST_SA_ID" property="lastSaId"/>
        <result column="LAST_SA_NAME" property="lastSaName"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="age" property="age"/>
        <result column="sex" property="sex"/>
        <result column="model" property="model"/>
        <result column="one_id" property="oneId"/>
        <result column="is_error" property="isError"/>
        <result column="error_msg" property="errorMsg"/>
        <result column="line_number" property="lineNumber"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="insurance_name" property="insuranceName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, vin, license_plate_num, name,
        tel, vi_finish_date, clivta_finish_date, SA_ID, SA_NAME,
         LAST_SA_ID, LAST_SA_NAME, dealer_code, age, sex, model,
         one_id, data_sources,
         is_error, error_msg, line_number, is_deleted, is_valid, created_at, updated_at,insurance_name
        </sql>

    <!-- 删除当前用户在临时表存的数据 -->
    <delete id="deleteAll"> delete from
		te_invite_insurance_vehicle_record_import where
		created_by=#{userId}
	</delete>

    <insert id="insertInsuranceImportData">
        INSERT INTO te_invite_insurance_vehicle_record_import
        (app_id, vin, vi_finish_date, clivta_finish_date,
        SA_ID, dealer_code, data_sources,
        line_number, is_error, error_msg, created_by, created_at,insurance_name)
        VALUES
        <foreach collection="tmpDtoList" item="dto" separator=",">
            ('volvo',#{dto.vin},#{dto.viFinishDate},#{dto.clivtaFinishDate},
            #{dto.saId},#{dto.dealerCode},3,
            #{dto.lineNumber},#{dto.isError},#{dto.errorMsg},#{userId}, now(),#{dto.insuranceName})
        </foreach>
    </insert>

    <!-- 判断vin是否为空 -->
    <update id="updateCheckVinEmpty">
		UPDATE te_invite_insurance_vehicle_record_import t SET
		t.is_error = 1, t.ERROR_MSG= CONCAT(IFNULL(error_msg,''), 'VIN为空;')
		where 1=1 and (t.vin is null or t.vin = '') and t.created_by = #{userId}
	</update>

    <!-- 判断商业险到期日期+交强险到期日期是否都为空 -->
    <update id="updateCheckDateEmpty">
		UPDATE te_invite_insurance_vehicle_record_import t SET
		t.is_error = 1, t.ERROR_MSG= CONCAT(IFNULL(error_msg,''), '商业险到期日期+交强险到期日期都为空;')
		where 1=1 and t.vi_finish_date is null and t.clivta_finish_date is null and t.created_by = #{userId}
	</update>

    <!-- 查询正确的数据 -->
    <select id="getSuccessData"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM te_invite_insurance_vehicle_record_import t where t.is_error=0 and
        t.created_by=#{userId}
    </select>

    <!-- 删除当前用户在临时表存的车主车辆数据 -->
    <delete id="deleteVehicleOwnerByUserId"> delete from
		te_invite_insurance_vehicle_owner_info where
		created_by=#{userId}
	</delete>

    <insert id="insertVehicleOwner">
        INSERT INTO te_invite_insurance_vehicle_owner_info
        (app_id, vin, license, model, model_name, invoice_date,
        one_id, name, tel, age, sex, created_by, created_at)
        VALUES
        <foreach collection="vinList" item="dto" separator=",">
            ('volvo',#{dto.vin},#{dto.license},#{dto.model},#{dto.modelName},#{dto.invoiceDate},
            #{dto.oneId},#{dto.name},#{dto.tel},#{dto.age},#{dto.sex},#{userId}, now())
        </foreach>
    </insert>

    <!-- 删除当前用户在临时表存的保险专员数据 -->
    <delete id="deleteRoleUserByUserId"> delete from
		te_invite_insurance_role_user_info where
		created_by=#{userId}
	</delete>

    <insert id="insertRoleUser">
        INSERT INTO te_invite_insurance_role_user_info
        (app_id, owner_code, user_id, user_code, employee_name, created_by, created_at)
        VALUES
        <foreach collection="userList" item="dto" separator=",">
            ('volvo',#{dto.companyCode},#{dto.userId},#{dto.userCode},#{dto.employeeName}, #{userId}, now())
        </foreach>
    </insert>

    <!-- 判断vin是否存在 -->
    <update id="updateExistVinOnMiddle">
		UPDATE te_invite_insurance_vehicle_record_import t SET
		t.is_error = 1, t.ERROR_MSG= CONCAT(IFNULL(error_msg,''), 'VIN不存在')
		where 1=1
		and	not exists(select distinct vin from te_invite_insurance_vehicle_owner_info te
		where te.vin=t.vin and te.created_by = t.created_by)
		and t.created_by = #{userId}
	</update>


    <!-- 临时表数据导入正式表 ===修改
     and td.vin = te.vin
		and td.created_by = te.created_by
		and td.SA_ID = tw.user_code
		and td.created_by = tw.created_by
		and td.dealer_code = tw.owner_code
     -->
    <update id="updateImportInsuranceRecord">
		update te_invite_insurance_vehicle_record_import td
		left join
		(select distinct vin, license, model, one_id, name, tel, age, sex, created_by
		from te_invite_insurance_vehicle_owner_info
		where
		created_by=#{userId}
		) te
		on
		td.vin = te.vin
		and
		td.created_by = te.created_by
		left join
		(select distinct user_code, owner_code, user_id, employee_name, created_by
		from te_invite_insurance_role_user_info
		where
		created_by=#{userId}
		) tw
		on td.SA_ID = tw.user_code
		and td.created_by = tw.created_by
		and td.dealer_code = tw.owner_code
		set
		td.license_plate_num = te.license,
		td.model = te.model,
		td.one_id = te.one_id,
		td.name = te.name,
		td.tel = te.tel,
		td.age = te.age,
		td.sex = te.sex,
        td.LAST_SA_ID = tw.user_id,
		td.LAST_SA_NAME = tw.employee_name,
		td.updated_at = SYSDATE(),
		td.updated_by = #{userId}
        WHERE
        td.created_by = #{userId}
        and td.is_error=0

	</update>

    <!-- 查询显示成功导入的数据条数 -->
    <select id="querySuccessCount"
            resultType="java.lang.Integer"> SELECT count(1) FROM te_invite_insurance_vehicle_record_import t
		where t.is_error=0 and t.created_by=#{userId}
	</select>


    <!-- 查询显示错误数据 -->
    <select id="queryError"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM te_invite_insurance_vehicle_record_import t where t.is_error=1 and
        t.created_by=#{userId}
    </select>

    <select id="selectImportErrorInsuranceRecord" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM te_invite_insurance_vehicle_record_import t WHERE 1=1
        and t.is_error=1 and
        t.created_by=#{userId}
    </select>


    <!-- 查询显示成功导入的数据 -->
    <select id="querySuccess"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM te_invite_insurance_vehicle_record_import t where t.is_error=0 and
        t.created_by=#{userId}
    </select>


    <select id="selectImportSuccessInsuranceRecord" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM te_invite_insurance_vehicle_record_import t WHERE 1=1
        and t.is_error=0 and
        t.created_by=#{userId}
    </select>

    <!-- and tv.follow_status = 82401001 -->
    <update id="updateBeforeViInsuranceRecord">
        update
        tt_invite_insurance_vehicle_record tv
        set
        tv.order_status = 83681005,
        tv.updated_at = now(),
        tv.updated_by = #{userId}
        where
        1=1
        and tv.is_main = 1
        and tv.order_status = 83681001
        and tv.clue_type = 2
        <if test=" viList !=null and viList.size > 0 ">
            AND tv.vin IN
            <foreach collection="viList" item="item" separator="," open="(" close=")">
                #{item.vin}
            </foreach>
            AND tv.dealer_code IN
            <foreach collection="viList" item="item" separator="," open="(" close=")">
                #{item.dealerCode}
            </foreach>
        </if>
    </update>

    <insert id="importViInsuranceData">
        INSERT INTO tt_invite_insurance_vehicle_record
        (app_id, owner_code, is_main, source_type, vin, license_plate_num,
        name, tel, invite_type, advise_in_date,
        plan_follow_date,
        SA_ID, SA_NAME, follow_status, order_status, data_sources,
        sex, age, model, dealer_code,one_id, item_type, clue_type,
        created_by, created_at,insurance_name)
        VALUES
        <foreach collection="viList" item="dto" separator=",">
            ('volvo',#{dto.dealerCode},1,2,#{dto.vin},#{dto.licensePlateNum},
            #{dto.name},#{dto.tel},82381003,#{dto.viFinishDate},
            DATE_SUB(#{dto.viFinishDate}, INTERVAL #{viDay} DAY),
            #{dto.lastSaId},#{dto.lastSaName},82401001,83681001,4,
            #{dto.sex},#{dto.age},#{dto.model},#{dto.dealerCode},#{dto.oneId},10041002,2,
            #{userId}, now(),#{dto.insuranceName})
        </foreach>
    </insert>

    <!-- and tv.follow_status = 82401001 -->
    <update id="updateBeforeClivtaInsuranceRecord">
        update
        tt_invite_insurance_vehicle_record tv
        set
        tv.order_status = 83681005,
        tv.updated_at = now(),
        tv.updated_by = #{userId}
        where
        1=1
        and tv.is_main = 1
        and tv.order_status = 83681001
        and tv.clue_type = 1
        <if test=" clivtaList !=null and clivtaList.size > 0 ">
            AND tv.vin IN
            <foreach collection="clivtaList" item="item" separator="," open="(" close=")">
                #{item.vin}
            </foreach>
            AND tv.dealer_code IN
            <foreach collection="clivtaList" item="item" separator="," open="(" close=")">
                #{item.dealerCode}
            </foreach>
        </if>
    </update>

    <insert id="importClivtaInsuranceData">
        INSERT INTO tt_invite_insurance_vehicle_record
        (app_id, owner_code, is_main, source_type, vin, license_plate_num,
        name, tel, invite_type, advise_in_date,
        plan_follow_date,
        SA_ID, SA_NAME, follow_status, order_status, data_sources,
        sex, age, model, dealer_code,one_id, item_type, clue_type,
        created_by, created_at,insurance_name)
        VALUES
        <foreach collection="clivtaList" item="dto" separator=",">
            ('volvo',#{dto.dealerCode},1,2,#{dto.vin},#{dto.licensePlateNum},
            #{dto.name},#{dto.tel},82381003,#{dto.clivtaFinishDate},
            DATE_SUB(#{dto.clivtaFinishDate}, INTERVAL #{cliDay} DAY),
            #{dto.lastSaId},#{dto.lastSaName},82401001,83681001,4,
            #{dto.sex},#{dto.age},#{dto.model},#{dto.dealerCode},#{dto.oneId},10041002,1,
            #{userId}, now(),#{dto.insuranceName})
        </foreach>
    </insert>


    <!-- and tv.clue_type = 1 -->
    <update id="updateInsuranceRecordData">
        update
        tt_invite_insurance_vehicle_record tv
        left join (
        select vin, dealer_code,insurance_type,clivta_bill_no,vi_bill_no,clivta_finish_date,vi_finish_date
        from tt_insurance_bill b1
        where b1.id = (
        SELECT id from tt_insurance_bill b2 where
        b2.vin = b1.vin and b2.dealer_code = b1.dealer_code ORDER BY b2.created_at desc limit 1
        )
        )
        tib on tib.vin = tv.vin and tib.dealer_code = tv.dealer_code
        set
        tv.insurance_type = (
        case when tib.insurance_type=81761001 then 81761002
        when tv.clue_type = 1 and DATE_SUB(tv.advise_in_date,INTERVAL 1 YEAR) <![CDATA[<=]]> tib.clivta_finish_date and
        tib.clivta_finish_date <![CDATA[<=]]> tv.advise_in_date then 81761003
        when tv.clue_type = 2 and DATE_SUB(tv.advise_in_date,INTERVAL 1 YEAR) <![CDATA[<=]]> tib.vi_finish_date and
        tib.vi_finish_date <![CDATA[<=]]> tv.advise_in_date then 81761003
        else 81761004 end
        )
        where
        1=1
        and tib.vin = tv.vin
        and tib.dealer_code = tv.dealer_code
        and tv.is_main = 1
        and tv.order_status = 83681001
        and tv.dealer_code = #{dealerCode}
        and tv.created_by = concat(#{userId})
        <if test=" vinList !=null and vinList.size > 0 ">
            AND tv.vin IN
            <foreach collection="vinList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </update>

</mapper>
