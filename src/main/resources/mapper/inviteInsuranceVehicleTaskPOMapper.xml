<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleTaskPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="vin" property="vin"/>
        <result column="license_plate_num" property="licensePlateNum"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="name" property="name"/>
        <result column="tel" property="tel"/>
        <result column="age" property="age"/>
        <result column="sex" property="sex"/>
        <result column="model" property="model"/>
        <result column="daily_mileage" property="dailyMileage"/>
        <result column="advise_in_date" property="adviseInDate"/>
        <result column="advise_in_date_update_time" property="adviseInDateUpdateTime"/>
        <result column="invite_type" property="inviteType"/>
        <result column="day_in_advance" property="dayInAdvance"/>
        <result column="remind_interval" property="remindInterval"/>
        <result column="invite_rule_type" property="inviteRuleType"/>
        <result column="invite_rule_id" property="inviteRuleId"/>
        <result column="is_create_invite" property="isCreateInvite"/>
        <result column="invite_id" property="inviteId"/>
        <result column="create_invite_time" property="createInviteTime"/>
        <result column="follow_status" property="followStatus"/>
        <result column="reinvite_time" property="reinviteTime"/>
        <result column="invalid_reason" property="invalidReason"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="last_change_date" property="lastChangeDate"/>
        <result column="invite_time" property="inviteTime"/>
        <result column="item_type" property="itemType"/>
        <result column="item_code" property="itemCode"/>
        <result column="item_name" property="itemName"/>
        <result column="qb_number" property="qbNumber"/>
        <result column="close_interval" property="closeInterval"/>
        <result column="close_times" property="closeTimes"/>
        <result column="advise_in_mileage" property="adviseInMileage"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="insurance_type" property="insuranceType"/>
        <result column="insurance_bill_id" property="insuranceBillId"/>
        <result column="clue_type" property="clueType"/>
        <result column="new_insure_no" property="newInsureNo"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, vin, license_plate_num, dealer_code,
           name, tel, age, sex, model, daily_mileage, advise_in_date, advise_in_date_update_time,
            invite_type, day_in_advance, remind_interval, invite_rule_type, invite_rule_id,
            is_create_invite, invite_id, create_invite_time, follow_status, reinvite_time,
            invalid_reason, data_sources, is_deleted, is_valid, created_at, updated_at,
            last_change_date, invite_time, item_type, item_code, item_name, qb_number,
            close_interval, close_times, advise_in_mileage,insurance_type,insurance_bill_id,clue_type,new_insure_no
        </sql>

    <select id="queryInsuranceTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_insurance_vehicle_task t
        where 1=1
        and t.invite_type = 82381003
        and
        (t.is_create_invite=0
        and t.create_invite_time&lt;=STR_TO_DATE(#{createDate},'%Y-%m-%d'))
        <if test="null != ownerCode and '' != ownerCode">
            and owner_code = #{ownerCode}
        </if>
        <if test="null != vin and '' != vin">
            and vin = #{vin}
        </if>
    </select>


    <select id="queryTaskByInviteId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_insurance_vehicle_task t
        where 1=1
        and t.invite_type = 82381003
        and t.invite_id = #{inviteId}

    </select>

    <update id="updateInsuranceVehicleTaskDlr">
        update
           tt_invite_insurance_vehicle_task t
        set t.day_in_advance = #{params.dayInAdvance},
            t.close_interval = #{params.closeInterval},
            t.create_invite_time = DATE_SUB(t.advise_in_date, INTERVAL #{params.dayInAdvance} DAY),
            t.updated_at = now(),
            t.updated_by = #{userId}
        where
        t.invite_id is null
        and t.is_create_invite=0
        and t.clue_type = #{params.inviteType}
        and t.dealer_code = #{params.dealerCode}
    </update>

    <!-- and tv.follow_status = 82401001 -->
    <update id="updateInsuranceVehicleRecordDlr">
        update
           tt_invite_insurance_vehicle_record tv
        set
            tv.plan_follow_date = DATE_SUB(tv.advise_in_date, INTERVAL #{params.dayInAdvance} DAY),
            tv.updated_at = now(),
            tv.updated_by = #{userId}
        where
        1=1
        and tv.is_main = 1
        and tv.order_status = 83681001
        and tv.clue_type = #{params.inviteType}
        and tv.dealer_code = #{params.dealerCode}
    </update>


    <update id="updateInsuranceVehicleTask">
        update
           tt_invite_insurance_vehicle_task t
        set t.day_in_advance = #{params.dayInAdvance},
            t.close_interval = #{params.closeInterval},
            t.create_invite_time = DATE_SUB(t.advise_in_date, INTERVAL #{params.dayInAdvance} DAY),
            t.updated_at = now(),
            t.updated_by = #{userId}

        where
        t.invite_id is null
        and t.is_create_invite=0
        and t.clue_type = #{params.inviteType}
    </update>

    <!-- and tv.follow_status = 82401001 -->
    <update id="updateInsuranceVehicleRecord">
        update
           tt_invite_insurance_vehicle_record tv
        set
            tv.plan_follow_date = DATE_SUB(tv.advise_in_date, INTERVAL #{params.dayInAdvance} DAY),
            tv.updated_at = now(),
            tv.updated_by = #{userId}
        where 1=1
        and tv.is_main = 1
        and tv.order_status = 83681001
        and tv.clue_type = #{params.inviteType}
    </update>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleTaskPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_insurance_vehicle_task t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num = #{params.licensePlateNum}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.tel !=null and params.tel != '' ">
            AND t.tel = #{params.tel}
        </if>
        <if test=" params.age !=null and params.age != '' ">
            AND t.age = #{params.age}
        </if>
        <if test=" params.sex !=null and params.sex != '' ">
            AND t.sex = #{params.sex}
        </if>
        <if test=" params.model !=null and params.model != '' ">
            AND t.model = #{params.model}
        </if>
        <if test=" params.dailyMileage !=null and params.dailyMileage != '' ">
            AND t.daily_mileage = #{params.dailyMileage}
        </if>
        <if test=" params.adviseInDate !=null and params.adviseInDate != '' ">
            AND t.advise_in_date = #{params.adviseInDate}
        </if>
        <if test=" params.adviseInDateUpdateTime !=null and params.adviseInDateUpdateTime != '' ">
            AND t.advise_in_date_update_time = #{params.adviseInDateUpdateTime}
        </if>
        <if test=" params.inviteType !=null and params.inviteType != '' ">
            AND t.invite_type = #{params.inviteType}
        </if>
        <if test=" params.dayInAdvance !=null and params.dayInAdvance != '' ">
            AND t.day_in_advance = #{params.dayInAdvance}
        </if>
        <if test=" params.remindInterval !=null and params.remindInterval != '' ">
            AND t.remind_interval = #{params.remindInterval}
        </if>
        <if test=" params.inviteRuleType !=null and params.inviteRuleType != '' ">
            AND t.invite_rule_type = #{params.inviteRuleType}
        </if>
        <if test=" params.inviteRuleId !=null and params.inviteRuleId != '' ">
            AND t.invite_rule_id = #{params.inviteRuleId}
        </if>
        <if test=" params.isCreateInvite !=null and params.isCreateInvite != '' ">
            AND t.is_create_invite = #{params.isCreateInvite}
        </if>
        <if test=" params.inviteId !=null and params.inviteId != '' ">
            AND t.invite_id = #{params.inviteId}
        </if>
        <if test=" params.createInviteTime !=null and params.createInviteTime != '' ">
            AND t.create_invite_time = #{params.createInviteTime}
        </if>
        <if test=" params.followStatus !=null and params.followStatus != '' ">
            AND t.follow_status = #{params.followStatus}
        </if>
        <if test=" params.reinviteTime !=null and params.reinviteTime != '' ">
            AND t.reinvite_time = #{params.reinviteTime}
        </if>
        <if test=" params.invalidReason !=null and params.invalidReason != '' ">
            AND t.invalid_reason = #{params.invalidReason}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        <if test=" params.lastChangeDate !=null and params.lastChangeDate != '' ">
            AND t.last_change_date = #{params.lastChangeDate}
        </if>
        <if test=" params.inviteTime !=null and params.inviteTime != '' ">
            AND t.invite_time = #{params.inviteTime}
        </if>
        <if test=" params.itemType !=null and params.itemType != '' ">
            AND t.item_type = #{params.itemType}
        </if>
        <if test=" params.itemCode !=null and params.itemCode != '' ">
            AND t.item_code = #{params.itemCode}
        </if>
        <if test=" params.itemName !=null and params.itemName != '' ">
            AND t.item_name = #{params.itemName}
        </if>
        <if test=" params.qbNumber !=null and params.qbNumber != '' ">
            AND t.qb_number = #{params.qbNumber}
        </if>
        <if test=" params.closeInterval !=null and params.closeInterval != '' ">
            AND t.close_interval = #{params.closeInterval}
        </if>
        <if test=" params.closeTimes !=null and params.closeTimes != '' ">
            AND t.close_times = #{params.closeTimes}
        </if>
        <if test=" params.adviseInMileage !=null and params.adviseInMileage != '' ">
            AND t.advise_in_mileage = #{params.adviseInMileage}
        </if>
    </select>

    <select id="getInvitationDlrRule"
            resultType="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceRulePO">
        select IFNULL(r.id,t.id) as id,IFNULL(r.dealer_code,t.dealer_code) as dealerCode,
        t.invite_type as inviteType,
        t.invite_rule as inviteRule,
        IFNULL(r.rule_value,t.rule_value) as ruleValue,
        IFNULL(r.day_in_advance,t.day_in_advance) as dayInAdvance,
        IFNULL(r.remind_interval,t.remind_interval) as remindInterval,
        IFNULL(r.close_interval,t.close_interval) as closeInterval,
        IFNULL(r.is_use,t.is_use) as isUse
        from tt_invite_insurance_rule t
        left JOIN  tt_invite_insurance_rule r on t.invite_type=r.invite_type and t.invite_rule=r.invite_rule and
        r.dealer_code=#{dealerCode}
        where t.dealer_code='VCDC' and t.invite_type=#{inviteType} and t.invite_rule=#{inviteRule}
        and t.is_use = 1
        Limit 0,1
    </select>

    <select id="queryInsuranceTaskRecordDesc" resultMap="BaseResultMap">
        select
        t.id, t.vin, t.license_plate_num, t.dealer_code, t.name, t.tel, t.age, t.sex, t.model, t.daily_mileage,
        t.advise_in_date,t.advise_in_date_update_time, t.invite_type, t.day_in_advance, t.remind_interval,
        t.invite_rule_type, t.invite_rule_id,t.is_create_invite, t.invite_id, t.create_invite_time,
		t.follow_status,
        t.reinvite_time, t.invalid_reason, t.data_sources,t.is_deleted, t.is_valid, t.created_at, t.updated_at,
        t.last_change_date,t.invite_time,t.close_interval,t.close_times,t.item_type,t.item_code,t.item_name,
        t.advise_in_mileage
        from( select
        t.id, t.vin, t.license_plate_num, t.dealer_code, t.name, t.tel, t.age, t.sex, t.model, t.daily_mileage,
        t.advise_in_date,t.advise_in_date_update_time, t.invite_type, t.day_in_advance, t.remind_interval,
        t.invite_rule_type, t.invite_rule_id,t.is_create_invite, t.invite_id, t.create_invite_time,
				t.follow_status,
        t.reinvite_time, t.invalid_reason, t.data_sources,t.is_deleted, t.is_valid, t.created_at, t.updated_at,
        t.last_change_date,t.invite_time,t.close_interval,t.close_times,t.item_type,t.item_code,t.item_name,
        t.advise_in_mileage
        from tt_invite_insurance_vehicle_task t
        where
        t.invite_id is null
        and t.is_deleted = 0
        and t.invite_type =82381003
        and t.vin = #{vin}
        and t.clue_type = #{viClivta}
        and t.is_create_invite != 2
        and DATE_FORMAT(t.advise_in_date,'%Y') = DATE_FORMAT(now(),'%Y')

				union all
				select
        t.id, t.vin, t.license_plate_num, t.dealer_code, t.name, t.tel, t.age, t.sex, t.model, t.daily_mileage,
        t.advise_in_date,t.advise_in_date_update_time, t.invite_type, t.day_in_advance, t.remind_interval,
        t.invite_rule_type, t.invite_rule_id,t.is_create_invite, t.invite_id, t.create_invite_time,
				t.follow_status,
        t.reinvite_time, t.invalid_reason, t.data_sources,t.is_deleted, t.is_valid, t.created_at, t.updated_at,
        t.last_change_date,t.invite_time,t.close_interval,t.close_times,t.item_type,t.item_code,t.item_name,
        t.advise_in_mileage
        from tt_invite_insurance_vehicle_task t,tt_invite_insurance_vehicle_record tv
        where
        t.invite_id is not null and t.invite_id = tv.id
        and tv.is_main = 1
        and tv.order_status = 83681001
        and tv.clue_type = #{viClivta}
        and t.is_deleted = 0
        and t.invite_type =82381003
        and t.vin = #{vin}
        and t.clue_type = #{viClivta}
        and t.is_create_invite != 2
        and DATE_FORMAT(t.advise_in_date,'%Y') = DATE_FORMAT(now(),'%Y')
        
        ) t
        order by t.data_sources
    </select>

</mapper>
