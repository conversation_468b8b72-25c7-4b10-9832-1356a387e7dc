<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.talkskill.TalkskillTagMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillTagPO">
            <id column="tag_id" property="tagId"/>
            <result column="tag_code" property="tagCode"/>
            <result column="tag_name" property="tagName"/>
            <result column="level" property="level"/>
            <result column="data_sources" property="dataSources"/>
            <result column="is_deleted" property="isDeleted"/>
            <result column="is_valid" property="isValid"/>
            <result column="created_at" property="createdAt"/>
            <result column="updated_at" property="updatedAt"/>
            <result column="created_by" property="createdBy"/>
            <result column="updated_by" property="updatedBy"/>
            <result column="record_version" property="recordVersion"/>
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           tag_id, parent_id, tag_code, tag_name, level, data_sources, is_deleted, is_valid, created_at, updated_at
        </sql>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillTagPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_talkskill_tag t
        WHERE 1=1
                    <if test=" params.tagId !=null and params.tagId != '' ">
                AND t.tag_id = #{params.tagId}
            </if>
                    <if test=" params.parentId !=null and params.parentId != '' ">
                AND t.parent_id = #{params.parentId}
            </if>
                    <if test=" params.tagCode !=null and params.tagCode != '' ">
                AND t.tag_code = #{params.tagCode}
            </if>
                    <if test=" params.tagName !=null and params.tagName != '' ">
                AND t.tag_name = #{params.tagName}
            </if>
                    <if test=" params.level !=null and params.level != '' ">
                AND t.level = #{params.level}
            </if>

                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

</mapper>
