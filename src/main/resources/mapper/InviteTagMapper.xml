<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.InviteTag.InviteTagMapper">


	<select id="selectInviteTagByName" parameterType="List" resultType="com.yonyou.dmscus.customer.entity.po.inviteTag.InviteTagPO">
		SELECT id,name
	      FROM tt_invite_tag tag
		 <where>
 	     	<if test="tagNameList != null and tagNameList.size > 0">
 	     		AND tag.name in
	 	    	<foreach collection="tagNameList" item="item" open="(" close=")" separator=",">
		 			 #{item}
	 	    	</foreach>
 	     	</if>
			AND tag.topic = #{topic}
 	     </where>
	</select>
	
	<insert id="insertBatchInviteTag">
		INSERT INTO tt_invite_tag (name, tag_desc, topic) VALUES
		<foreach collection="tagNameList" item="tagName" separator=",">
			(#{tagName}, #{tagName}, #{topic})
		</foreach>
	</insert>

 
</mapper>
