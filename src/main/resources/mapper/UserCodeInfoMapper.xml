<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.userCode.UserCodeInfoMapper">

        <select id="getCountByUserCode" parameterType="java.lang.String" resultType="java.lang.Integer">
        select  count(user_code) from  tt_user_code_info where user_code =  #{userCode} and is_valid = 10041001 and  is_deleted = 0
</select>
        <select id="selectPageBySql" parameterType="com.yonyou.dmscus.customer.dto.UserCodeVo" resultType="com.yonyou.dmscus.customer.entity.dto.userCode.UserCodeInfoDto">
                SELECT


                id,
                belonging,
                belonging_name as belongingName,
                employee_name as employee<PERSON><PERSON>,
                employee_no as employeeNo,
                position,
                user_code  as userCode,
                mobile ,
                email as email,
                is_valid as isValid,
                created_at  as createdAt,
                created_by as createdBy,
                updated_by as  updatedBy,
                updated_at as updatedAt
                from  tt_user_code_info
                where  1= 1  and  is_deleted = 0
                <if test=" params.userCode !=null and params.userCode != '' ">
                        AND user_code like "%" #{params.userCode} "%"
                </if>

        </select>
        <insert id="batchInsert">
                insert into tt_user_code_info(
                        belonging,
                        belonging_name ,
                        employee_name ,
                        employee_no ,
                        position,
                        user_code  ,
                        mobile ,
                        email ,
                        is_valid ,
                        is_deleted,
                        created_at  ,
                        created_by ,
                        record_version
                )
                SELECT
                        t.belonging,
                        t.belonging_name ,
                        t.employee_name ,
                        t.employee_no ,
                        t.position,
                        t.user_code  ,
                        t.mobile ,
                        t.email ,
                        t.is_valid ,
                        0,
                        now()  ,
                        t.created_by ,
                        1
                from tt_user_code_info_import t
                where t.created_by=#{userId} and  is_error = 0
        </insert>

        <select id="selectCountByUserCode" parameterType="java.lang.String" resultType="Integer">
                select  count(user_code) from  tt_user_code_info where  1= 1  and  is_deleted = 0

                        AND user_code =#{userCode}


        </select>
</mapper>