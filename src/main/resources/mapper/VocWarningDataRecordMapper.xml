<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.voc.VocWarningDataRecordMapper">
    <insert id="insertList" parameterType="com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataRecordPo">
        insert into  tt_voc_warning_daily_record
        (
            vin,
            report_date,
            modify_time,
            status_name,
            status_value,
            status_change,
            mileage,
            mileage_km,
            year_model,
            model
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.vin},
            #{item.reportDate},
            #{item.modifyTime},
            #{item.statusName},
            #{item.statusValue},
            #{item.statusChange},
            #{item.mileage},
            #{item.mileageKm},
            #{item.yearModel},
            #{item.model}
            )
        </foreach>

    </insert>

    <update id="updateWarningIsExecute" parameterType="java.util.List">
        update tt_voc_warning_daily_record set
        is_execute = 1
        where id in
        <foreach collection="updateList" index="index" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updateList" parameterType="java.util.List">
        <foreach collection="updateList" item="item" index="index" open="" close="" separator=";">
            update tt_voc_warning_daily_record set
            report_date =#{item.reportDate},
            modify_time =#{item.modifyTime},
            status_name =#{item.statusName},
            status_value =#{item.statusValue},
            status_change =#{item.statusChange},
            mileage =#{item.mileage},
            mileage_km =#{item.mileageKm},
            year_model =#{item.yearModel},
            model =#{item.model},
            is_execute = 0,
            is_deleted = 0,
            updated_at = now(),
            updated_by =-2,
            version = version+1 where id  = #{item.id}
        </foreach>
    </update>
    <select id="selectListWarningDataRecord" parameterType="java.util.List" resultType="com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataRecordPo">
        select
             id,
             vin,
             report_date,
             modify_time,
             status_name,
             status_value,
             status_change,
             mileage,
             mileage_km,
             year_model,
             model
        from  tt_voc_warning_daily_record
        where  1=1  and  is_deleted = 0 and  vin in
        <foreach collection="selectList" index="index" item="item" open="(" separator="," close=")">
            #{item.vin}
        </foreach>

    </select>
    <select id="selectOneByVinAndDate" resultType="java.lang.String">
        select mileage_km from tt_voc_warning_data_log
        where vin = #{vin}
        and dt &gt;= #{endtime}
        and dt &lt;=#{datetime}
        and mileage_km != '/N'
        and mileage_km != '\\N'
        and mileage_km is not null
        order BY dt desc LIMIT 1
    </select>


</mapper>