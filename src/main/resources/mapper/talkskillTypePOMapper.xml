<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.talkskill.TalkskillTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillTypePO">
        <id column="type_id" property="typeId"/>
        <result column="type_name" property="typeName"/>
        <result column="remark" property="remark"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
       type_id, type_code, type_name, remark, data_sources, is_deleted, is_valid, created_at, updated_at
    </sql>
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillTypePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_talkskill_type t
        WHERE 1=1
        <if test=" params.typeId !=null and params.typeId != '' ">
                AND t.type_id = #{params.typeId}
        </if>
        <if test=" params.typeCode !=null and params.typeCode != '' ">
                AND t.type_code = #{params.typeCode}
        </if>
        <if test=" params.typeName !=null and params.typeName != '' ">
                AND t.type_name = #{params.typeName}
        </if>
        <if test=" params.remark !=null and params.remark != '' ">
                AND t.remark = #{params.remark}
        </if>

        <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
        </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>
    <insert id="insertList" parameterType="java.util.List">
        insert into tt_talkskill_type (type_code,type_name,remark,is_valid) values
        <foreach collection="list" item="item" separator="," >
            (#{typeCode},#{typeName},#{remark},#{isValid})
        </foreach>
    </insert>
</mapper>
