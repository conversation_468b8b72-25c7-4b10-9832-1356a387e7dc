<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.invitationautocreate.InviteVehicleTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="vin" property="vin"/>
        <result column="license_plate_num" property="licensePlateNum"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="name" property="name"/>
        <result column="tel" property="tel"/>
        <result column="age" property="age"/>
        <result column="sex" property="sex"/>
        <result column="model" property="model"/>
        <result column="daily_mileage" property="dailyMileage"/>
        <result column="advise_in_date" property="adviseInDate"/>
        <result column="advise_in_date_update_time" property="adviseInDateUpdateTime"/>
        <result column="invite_type" property="inviteType"/>
        <result column="day_in_advance" property="dayInAdvance"/>
        <result column="remind_interval" property="remindInterval"/>
        <result column="close_interval" property="closeInterval"/>
        <result column="close_times" property="closeTimes"/>
        <result column="invite_rule_type" property="inviteRuleType"/>
        <result column="invite_rule_id" property="inviteRuleId"/>
        <result column="is_create_invite" property="isCreateInvite"/>
        <result column="invite_id" property="inviteId"/>
        <result column="create_invite_time" property="createInviteTime"/>
        <result column="follow_status" property="followStatus"/>
        <result column="reinvite_time" property="reinviteTime"/>
        <result column="invalid_reason" property="invalidReason"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="last_change_date" property="lastChangeDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="invite_time" property="inviteTime"/>
        <result column="item_type" property="itemType"/>
        <result column="item_code" property="itemCode"/>
        <result column="item_name" property="itemName"/>
        <result column="advise_in_mileage" property="adviseInMileage"/>
        <result column="is_voc" property="isVoc"/>
        <result column="mileage_interval" property="mileageInterval"/>
        <result column="date_interval" property="dateInterval"/>
        <result column="daily_average_mileage" property="dailyAverageMileage"/>
        <result column="voc_mileage" property="vocMileage"/>
        <result column="out_mileage" property="outMileage"/>
        <result column="last_maintain_date" property="lastMaintainDate"/>
        <result column="last_maintenance_date" property="lastMaintenanceDate"/>
        <result column="source_type" property="sourceType"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id, t.vin, t.license_plate_num, t.dealer_code, t.name, t.tel, t.age, t.sex, t.model, t.daily_mileage,
        t.advise_in_date,t.advise_in_date_update_time, t.invite_type, t.day_in_advance, t.remind_interval,
        t.invite_rule_type, t.invite_rule_id,t.is_create_invite, t.invite_id, t.create_invite_time, t.follow_status,
        t.reinvite_time, t.invalid_reason, t.data_sources,t.is_deleted, t.is_valid, t.created_at, t.updated_at,
        t.last_change_date,t.invite_time,t.close_interval,t.close_times,t.item_type,t.item_code,t.item_name,
        t.advise_in_mileage,t.out_mileage
    </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_vehicle_task t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num = #{params.licensePlateNum}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.tel !=null and params.tel != '' ">
            AND t.tel = #{params.tel}
        </if>
        <if test=" params.age !=null and params.age != '' ">
            AND t.age = #{params.age}
        </if>
        <if test=" params.sex !=null and params.sex != '' ">
            AND t.sex = #{params.sex}
        </if>
        <if test=" params.model !=null and params.model != '' ">
            AND t.model = #{params.model}
        </if>
        <if test=" params.dailyMileage !=null and params.dailyMileage != '' ">
            AND t.daily_mileage = #{params.dailyMileage}
        </if>
        <if test=" params.adviseInDate !=null and params.adviseInDate != '' ">
            AND t.advise_in_date = #{params.adviseInDate}
        </if>
        <if test=" params.adviseInDateUpdateTime !=null and params.adviseInDateUpdateTime != '' ">
            AND t.advise_in_date_update_time = #{params.adviseInDateUpdateTime}
        </if>
        <if test=" params.inviteType !=null and params.inviteType != '' ">
            AND t.invite_type = #{params.inviteType}
        </if>
        <if test=" params.dayInAdvance !=null and params.dayInAdvance != '' ">
            AND t.day_in_advance = #{params.dayInAdvance}
        </if>
        <if test=" params.remindInterval !=null and params.remindInterval != '' ">
            AND t.remind_interval = #{params.remindInterval}
        </if>
        <if test=" params.inviteRuleType !=null and params.inviteRuleType != '' ">
            AND t.invite_rule_type = #{params.inviteRuleType}
        </if>
        <if test=" params.inviteRuleId !=null and params.inviteRuleId != '' ">
            AND t.invite_rule_id = #{params.inviteRuleId}
        </if>
        <if test=" params.isCreateInvite !=null and params.isCreateInvite != '' ">
            AND t.is_create_invite = #{params.isCreateInvite}
        </if>
        <if test=" params.inviteId !=null and params.inviteId != '' ">
            AND t.invite_id = #{params.inviteId}
        </if>
        <if test=" params.createInviteTime !=null and params.createInviteTime != '' ">
            AND t.create_invite_time = #{params.createInviteTime}
        </if>
        <if test=" params.followStatus !=null and params.followStatus != '' ">
            AND t.follow_status = #{params.followStatus}
        </if>
        <if test=" params.reinviteTime !=null and params.reinviteTime != '' ">
            AND t.reinvite_time = #{params.reinviteTime}
        </if>
        <if test=" params.invalidReason !=null and params.invalidReason != '' ">
            AND t.invalid_reason = #{params.invalidReason}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        <if test=" params.lastChangeDate !=null and params.lastChangeDate != '' ">
            AND t.last_change_date = #{params.lastChangeDate}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_vehicle_task t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num = #{params.licensePlateNum}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.tel !=null and params.tel != '' ">
            AND t.tel = #{params.tel}
        </if>
        <if test=" params.age !=null and params.age != '' ">
            AND t.age = #{params.age}
        </if>
        <if test=" params.sex !=null and params.sex != '' ">
            AND t.sex = #{params.sex}
        </if>
        <if test=" params.model !=null and params.model != '' ">
            AND t.model = #{params.model}
        </if>
        <if test=" params.dailyMileage !=null and params.dailyMileage != '' ">
            AND t.daily_mileage = #{params.dailyMileage}
        </if>
        <if test=" params.adviseInDate !=null and params.adviseInDate != '' ">
            AND t.advise_in_date = #{params.adviseInDate}
        </if>
        <if test=" params.adviseInDateUpdateTime !=null and params.adviseInDateUpdateTime != '' ">
            AND t.advise_in_date_update_time = #{params.adviseInDateUpdateTime}
        </if>
        <if test=" params.inviteType !=null and params.inviteType != '' ">
            AND t.invite_type = #{params.inviteType}
        </if>
        <if test=" params.dayInAdvance !=null and params.dayInAdvance != '' ">
            AND t.day_in_advance = #{params.dayInAdvance}
        </if>
        <if test=" params.remindInterval !=null and params.remindInterval != '' ">
            AND t.remind_interval = #{params.remindInterval}
        </if>
        <if test=" params.inviteRuleType !=null and params.inviteRuleType != '' ">
            AND t.invite_rule_type = #{params.inviteRuleType}
        </if>
        <if test=" params.inviteRuleId !=null and params.inviteRuleId != '' ">
            AND t.invite_rule_id = #{params.inviteRuleId}
        </if>
        <if test=" params.isCreateInvite !=null and params.isCreateInvite != '' ">
            AND t.is_create_invite = #{params.isCreateInvite}
        </if>
        <if test=" params.inviteId !=null and params.inviteId != '' ">
            AND t.invite_id = #{params.inviteId}
        </if>
        <if test=" params.createInviteTime !=null and params.createInviteTime != '' ">
            AND t.create_invite_time = #{params.createInviteTime}
        </if>
        <if test=" params.followStatus !=null and params.followStatus != '' ">
            AND t.follow_status = #{params.followStatus}
        </if>
        <if test=" params.reinviteTime !=null and params.reinviteTime != '' ">
            AND t.reinvite_time = #{params.reinviteTime}
        </if>
        <if test=" params.invalidReason !=null and params.invalidReason != '' ">
            AND t.invalid_reason = #{params.invalidReason}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        <if test=" params.lastChangeDate !=null and params.lastChangeDate != '' ">
            AND t.last_change_date = #{params.lastChangeDate}
        </if>
    </select>


    <select id="queryQbTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_vehicle_task t
        where 1=1
        and invite_type =82381007
        and
        (is_create_invite=0 and create_invite_time&lt;=STR_TO_DATE(#{createDate},'%Y-%m-%d'))


    </select>

    <select id="queryMaintainTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,v.last_maintain_date,v.last_maintenance_date
        from tt_invite_vehicle_task t
        left join tm_vehicle v on t.vin=v.vin
        where 1=1
        and invite_type in(82381001,82381002)
        and
        (is_create_invite=0 and create_invite_time&lt;=STR_TO_DATE(#{createDate},'%Y-%m-%d'))
        order by t.advise_in_date desc

    </select>

    <select id="queryRegularMaintainTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_vehicle_task t
        where 1=1
        and vin = #{vin}
        and invite_type =82381002
        and is_create_invite=0

    </select>

    <select id="queryCustomerLossTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,v.last_maintain_date,v.last_maintenance_date
        from tt_invite_vehicle_task t
        left join tm_vehicle v on t.vin=v.vin
        where 1=1
        and vin = #{vin}
        and invite_type =82381006
        and is_create_invite=0
        and dealer_code= #{dealerCode}

    </select>

    <!-- and create_invite_time&lt;=STR_TO_DATE(#{createDate},'%Y-%m-%d'))-->
    <select id="queryVulnerableTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_vehicle_task t
        where 1=1
        and invite_type =82381005
        and is_create_invite=0



    </select>
    <select id="queryInsuranceTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_vehicle_task t
        where 1=1
        and invite_type =82381003
        and
        (is_create_invite=0 and create_invite_time&lt;=STR_TO_DATE(#{createDate},'%Y-%m-%d'))
    </select>

    <select id="queryGuaranteeTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,v.last_maintain_date,v.last_maintenance_date
        from tt_invite_vehicle_task t
        left join tm_vehicle v on t.vin=v.vin
        where 1=1
        and invite_type =82381009
        and
        (is_create_invite=0 and create_invite_time&lt;=STR_TO_DATE(#{createDate},'%Y-%m-%d'))
    </select>

    <select id="queryCustomerLoss" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,v.last_maintain_date,v.last_maintenance_date
        from tt_invite_vehicle_task t
        left join tm_vehicle v on t.vin=v.vin
        left join   tt_voc_invite_vehicle_task_record b  on b.task_id  = t.id
        where 1=1
        and t.invite_type =82381006
        and
        (is_create_invite=0 and create_invite_time&lt;=STR_TO_DATE(#{createDate},'%Y-%m-%d'))
        and not exists(select 1 from tt_invite_vehicle_record a left  join  tt_voc_invite_vehicle_task_record c on c.record_id  = a.id
        where a.vin=t.vin and a.dealer_code=t.dealer_code and a.invite_type in (82381001,82381002)
        and a.order_status = 82411002 and  c.record_type =87891001 and c.`is_deleted`  = 0)  and b.record_type = 87891001 and b.`is_deleted`  = 0
        union  all
        select
        <include refid="Base_Column_List"/>,v.last_maintain_date,v.last_maintenance_date
        from tt_invite_vehicle_task t
        left join tm_vehicle v on t.vin=v.vin
        left join   tt_voc_invite_vehicle_task_record b  on b.task_id  = t.id
        where 1=1
        and t.invite_type =82381006
        and
        (is_create_invite=0 and create_invite_time&lt;=STR_TO_DATE(#{createDate},'%Y-%m-%d'))
        and not exists(select 1 from tt_invite_vehicle_record a left  join  tt_voc_invite_vehicle_task_record c on c.record_id  = a.id
        where a.vin=t.vin and a.dealer_code=t.dealer_code and a.invite_type in (82381001,82381002)
        and a.order_status = 82411002 and  c.record_type =87891002 and c.`is_deleted`  = 0 and  c.loss_type = 87901001 )  and b.record_type = 87891002 and b.`is_deleted`  = 0 and b.loss_type = 87901001
    </select>

    <select id="queryFirstMaintainTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,v.last_maintain_date,v.last_maintenance_date
        from tt_invite_vehicle_task t
        left join tm_vehicle v on t.vin=v.vin
        where 1=1
        and vin = #{vin}
        and invite_type =82381001
        and is_create_invite=0

    </select>

    <select id="getWaitCloseRecord" resultMap="BaseResultMap">
        select
        a.id,a.invite_type,a.item_type,a.vin,a.license_plate_num,a.dealer_code,a.name,a.tel,a.age,a.sex,a.model,
        date_add(DATE_FORMAT(a.advise_in_date,'%Y-%m-%d 00:00:00'), INTERVAL 3 MONTH) as advise_in_date,a.item_type,a.item_code,a.item_name,
        a.invite_time,v.is_voc,v.daily_average_mileage,v.voc_mileage,a.source_type
        from tt_invite_vehicle_record a
        left join tm_vehicle v on a.vin=v.vin
        where 1=1
        and a.invite_type in (82381001,82381002)
        and a.order_status = 82411002
        and a.source_type != 4
        and a.is_deleted = 0
        and date_add(DATE_FORMAT(a.advise_in_date,'%Y-%m-%d'), INTERVAL 3 MONTH)
        &lt;=STR_TO_DATE(#{createDate},'%Y-%m-%d')
    </select>

    <update id="updateInviteTaskByDealerCode">
        update tt_invite_vehicle_task
        set dealer_code=#{dealerCode}
        where
            vin = #{vin}
            and is_create_invite = 0
            and dealer_code=#{lastDealerCode}
    </update>

    <update id="updateTaskCloseById">
        update tt_invite_vehicle_task
        set is_create_invite=2,invalid_reason='进厂关闭任务'
        where
            vin = #{vin}
            and is_create_invite = 0
            and invite_type=#{inviteType}
        <if test=" dealerCode !=null and dealerCode != '' ">
            AND dealer_code = #{dealerCode}
        </if>
    </update>

    <update id="updateTaskPartCloseById">
        update tt_invite_vehicle_task
        set is_create_invite=2,invalid_reason='进厂关闭任务'
        where
            vin = #{vin}
            and item_type=#{itemType}
            and item_code=#{itemCode}
            and is_create_invite = 0
            and invite_type=82381005
    </update>

    <select id="getFirstMaintainTask" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tt_invite_vehicle_task t
        left join   tt_voc_invite_vehicle_task_record b  on b.task_id  = t.id and b.record_type =1 and b.`is_deleted`  = 0
        where t.vin=#{vin}
        and t.is_create_invite in (0,1)
        and t.invite_type=82381001
        <if test=" nextMonthDay !=null and nextMonthDay != '' ">
            AND t.advise_in_date >= STR_TO_DATE(#{nextMonthDay},'%Y-%m-%d')
        </if>
    </select>

    <select id="getFirstMaintainTasks" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tt_invite_vehicle_task t
        left join   tt_voc_invite_vehicle_task_record b  on b.task_id  = t.id and b.record_type =1 and b.`is_deleted`  = 0
        where t.vin in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and t.is_create_invite in (0,1)
        and t.invite_type=82381001
        <if test=" nextMonthDay !=null and nextMonthDay != '' ">
            AND t.advise_in_date >= STR_TO_DATE(#{nextMonthDay},'%Y-%m-%d')
        </if>
    </select>

    <select id="getMaintainTask" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/>
          from tt_invite_vehicle_task t
        left join   tt_voc_invite_vehicle_task_record b  on b.task_id  = t.id and b.record_type =1 and b.`is_deleted`  = 0
        where t.vin=#{vin}
        and t.is_create_invite in (0,1)
        and t.invite_type=82381002
        <if test=" nextMonthDay !=null and nextMonthDay != '' ">
            AND t.advise_in_date >= STR_TO_DATE(#{nextMonthDay},'%Y-%m-%d')
        </if>
    </select>

    <select id="getMaintainTasks" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tt_invite_vehicle_task t
        left join   tt_voc_invite_vehicle_task_record b  on b.task_id  = t.id and b.record_type =1 and b.`is_deleted`  = 0
        where t.vin in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and t.is_create_invite in (0,1)
        and t.invite_type=82381002
        <if test=" nextMonthDay !=null and nextMonthDay != '' ">
            AND t.advise_in_date >= STR_TO_DATE(#{nextMonthDay},'%Y-%m-%d')
        </if>
    </select>

    <select id="getMaintainTaskAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>, d.daily_average_mileage
        from tt_invite_vehicle_task t
        left join tm_vehicle d on t.vin = d.vin
        where t.vin in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and t.is_create_invite in (0,1)
        and t.invite_type in (82381001, 82381002)
        <if test=" nextMonthDay !=null and nextMonthDay != '' ">
            AND t.advise_in_date >= STR_TO_DATE(#{nextMonthDay},'%Y-%m-%d')
        </if>
    </select>

    <select id="getMaintainTaskWithDateRange" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tt_invite_vehicle_task t
        where t.vin=#{vin}
        and t.is_create_invite in (0,1)
        and t.invite_type=82381002
        and t.advise_in_date &gt;= #{ startDateTime }
        and t.advise_in_date &lt;= #{ endDateTime }
    </select>


    <select id="getVulnerableTask" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>,r.mileage_interval,r.date_interval
        from tt_invite_vehicle_task t
        left join tt_invite_part_item_rule r on t.item_type=r.type and t.item_code=r.code
        where t.vin=#{vin}
        and t.is_create_invite in (0,1)
        and t.invite_type=82381005
        and (Item_code!='7726')
        <if test=" nextMonthDay !=null and nextMonthDay != '' ">
            AND t.advise_in_date >= STR_TO_DATE(#{nextMonthDay},'%Y-%m-%d')
        </if>
    </select>

    <select id="queryByFuseRule" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tt_invite_vehicle_task t
        left join tm_vehicle v on t.vin=v.vin
        where t.is_create_invite=0
        and v.is_voc=10041001
        and t.invite_type in (82381001,82381002,82381005)
        and t.advise_in_mileage is not null
        and t.advise_in_mileage&lt;=v.voc_mileage
    </select>

    <update id="updateFordailyAverageMileageByIds">
        <foreach collection="params" item="item" index="index" open="" close="" separator=";">
        update tt_invite_vehicle_task
          set advise_in_date=#{item.adviseInDate},
              updated_by=#{item.updatedBy},
              updated_at=#{item.updatedAt},
              create_invite_time=#{item.createInviteTime},
              daily_mileage=#{item.dailyMileage},
              changed_id=#{item.changedId},
              advise_in_mileage=#{item.adviseInMileage}
        where id=#{item.id}
        </foreach>
    </update>

    <update id="updateFordailyAverageMileageById">
        update tt_invite_vehicle_task
        set advise_in_date=#{params.adviseInDate},
            updated_by=#{params.updatedBy},
            updated_at=#{params.updatedAt},
            create_invite_time=#{params.createInviteTime},
            daily_mileage=#{params.dailyMileage},
            changed_id=#{params.changedId},
            advise_in_mileage=#{params.adviseInMileage}
        where id=#{params.id}
    </update>

    <select id="getVulnerableTaskForRuleChanged" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>,r.mileage_interval,r.date_interval
        from tt_invite_vehicle_task t
        left join tt_invite_vehicle_record record on t.invite_id=record.id
        left join tt_invite_part_item_rule r on t.item_type=r.type and t.item_code=r.code
        where  1=1
        and t.is_create_invite in (0,1)
        and t.invite_type=82381005
        and t.Item_code=#{code}
        and t.item_type=#{type}
        and (record.id is null or record.order_status=82411002)
        and ifnull(t.changed_id,0)!=#{changedId}
        <if test=" nextMonthDay !=null and nextMonthDay != '' ">
            AND t.advise_in_date >= STR_TO_DATE(#{nextMonthDay},'%Y-%m-%d')
        </if>
        limit 0,400000
    </select>

    <select id="countVulnerableTaskForRuleChanged" resultType="java.lang.Integer">
        select count(1)
        from tt_invite_vehicle_task t
        left join tt_invite_vehicle_record record on t.invite_id=record.id
        left join tt_invite_part_item_rule r on t.item_type=r.type and t.item_code=r.code
        where  1=1
        and t.is_create_invite in (0,1)
        and t.invite_type=82381005
        and t.Item_code=#{code}
        and t.item_type=#{type}
        and (record.id is null or record.order_status=82411002)
        and ifnull(t.changed_id,0)!=#{changedId}
        <if test=" nextMonthDay !=null and nextMonthDay != '' ">
            AND t.advise_in_date >= STR_TO_DATE(#{nextMonthDay},'%Y-%m-%d')
        </if>
    </select>


    <select id="getFirstMaintainTaskForRuleChanged" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tt_invite_vehicle_task t
        left join tt_invite_vehicle_record record on t.invite_id=record.id
        where 1=1
        and t.is_create_invite in (0,1)
        and t.invite_type=82381001
        and (record.id is null or record.order_status=82411002)
        AND t.advise_in_date >= STR_TO_DATE(#{nextMonthDay},'%Y-%m-%d')
        and ifnull(t.changed_id,0)!=#{changedId}
        <if test=" dealerCode !=null and dealerCode =='VCDC' ">
            and not exists(select 1 from tt_invite_rule r where r.invite_type=82031002  and t.dealer_code=r.dealer_code and
            r.dealer_code!='VCDC')
        </if>
        <if test=" dealerCode !=null and dealerCode !='VCDC' ">
            and t.dealer_Code=#{dealerCode}
        </if>
        limit 0,400000
    </select>


    <select id="getCustomerLossTaskForRuleChanged" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tt_invite_vehicle_task t
        left join tt_invite_vehicle_record record on t.invite_id=record.id
        where  1=1
        and t.is_create_invite in (0,1)
        and t.invite_type=82381006
        and (record.id is null or record.order_status=82411002)
        AND t.advise_in_date >= STR_TO_DATE(#{nextMonthDay},'%Y-%m-%d')
        and ifnull(t.changed_id,0)!=#{changedId}
        <if test=" dealerCode !=null and dealerCode =='VCDC' ">
            and not exists(select 1 from tt_invite_rule r where r.invite_type=82031004  and  t.dealer_code=r.dealer_code and
            r.dealer_code!='VCDC')
        </if>
        <if test=" dealerCode !=null and dealerCode !='VCDC' ">
            and t.dealer_Code=#{dealerCode}
        </if>
        limit 0,400000
    </select>

    <select id="getGuaranteeTaskForRuleChanged" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tt_invite_vehicle_task t
        left join tt_invite_vehicle_record record on t.invite_id=record.id
        where  1=1
        and t.is_create_invite in (0,1)
        and t.invite_type=82381009
        and (record.id is null or record.order_status=82411002)
        AND t.advise_in_date >= STR_TO_DATE(#{nextMonthDay},'%Y-%m-%d')
        and ifnull(t.changed_id,0)!=#{changedId}
        <if test=" dealerCode !=null and dealerCode =='VCDC' ">
            and not exists(select 1 from tt_invite_rule r where r.invite_type=82031006  and  t.dealer_code=r.dealer_code and
            r.dealer_code!='VCDC')
        </if>
        <if test=" dealerCode !=null and dealerCode !='VCDC' ">
            and t.dealer_Code=#{dealerCode}
        </if>
        limit 0,400000
    </select>

    <select id="getMaintainTaskForRuleChanged" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tt_invite_vehicle_task t
        left join tt_invite_vehicle_record record on t.invite_id=record.id
        where  1=1
        and t.is_create_invite in (0,1)
        and t.invite_type=82381002
        and (record.id is null or record.order_status=82411002)
        AND t.advise_in_date >= STR_TO_DATE(#{nextMonthDay},'%Y-%m-%d')
        and ifnull(t.changed_id,0)!=#{changedId}
        <if test=" dealerCode !=null and dealerCode =='VCDC' ">
            and not exists(select 1 from tt_invite_rule r where r.invite_type=82031001  and  t.dealer_code=r.dealer_code and
            r.dealer_code!='VCDC')
        </if>
        <if test=" dealerCode !=null and dealerCode !='VCDC' ">
            and t.dealer_Code=#{dealerCode}
        </if>
        limit 0,400000
    </select>


    <select id="countWaitUpdateNoExecute" resultType="java.lang.Integer">
        select count(1)
        from tt_invite_vehicle_task t
        left join tt_invite_vehicle_record record on t.invite_id=record.id
        where  1=1
        and t.is_create_invite in (0,1)
        and t.invite_type=#{inviteType}
        and (record.id is null or record.order_status=82411002)
        AND t.advise_in_date >= STR_TO_DATE(#{nextMonthDay},'%Y-%m-%d')
        and ifnull(t.changed_id,0)!=#{changedId}
        <if test=" dealerCode !=null and dealerCode =='VCDC' ">
            and not exists(select 1 from tt_invite_rule r where r.invite_type=82031001  and  t.dealer_code=r.dealer_code and
            r.dealer_code!='VCDC')
        </if>
        <if test=" dealerCode !=null and dealerCode !='VCDC' ">
            and t.dealer_Code=#{dealerCode}
        </if>
    </select>

    <update id="updateCloseInterval">
        update tt_invite_vehicle_task t
        <if test=" dealerCode !=null and dealerCode =='VCDC' ">
            left join tt_invite_rule rule on rule.invite_type=#{ruleType}  and  t.dealer_code=rule.dealer_code and
            rule.dealer_code!='VCDC'
        </if>
            set
                t.close_interval=#{closeInterval},t.remind_interval=#{remindInterval},t.updated_at=now(),t.updated_by='1'
        where t.invite_type=#{inviteType}
         and  t.is_create_invite=0
        <if test=" dealerCode !=null and dealerCode =='VCDC' ">
            and rule.id is null
        </if>
        <if test=" dealerCode !=null and dealerCode !='VCDC' ">
            and t.dealer_Code=#{dealerCode}
        </if>
    </update>

    <update id="updateAdviseInMileage">
        update tt_invite_vehicle_task t
        <if test=" dealerCode !=null and dealerCode =='VCDC' ">
            left join tt_invite_rule rule on rule.invite_type=#{ruleType} and rule.invite_rule=#{inviteRule}
            and  t.dealer_code=rule.dealer_code and   rule.dealer_code!='VCDC'
        </if>
        set
        t.advise_in_mileage=#{ruleValue},t.changed_id=#{changedId},t.updated_at=now(),t.updated_by='1'
        where t.invite_type=#{inviteType}
        and  t.is_create_invite=0
        <if test=" dealerCode !=null and dealerCode =='VCDC' ">
            and rule.id is null
        </if>
        <if test=" dealerCode !=null and dealerCode !='VCDC' ">
            and t.dealer_Code=#{dealerCode}
        </if>
    </update>
    
    <update id="updateAdviseInMileageHasDown">
        update tt_invite_vehicle_task t
        <if test=" dealerCode !=null and dealerCode =='VCDC' ">
            left join tt_invite_rule rule on rule.invite_type=#{ruleType}  and  t.dealer_code=rule.dealer_code and
            rule.dealer_code!='VCDC'
        </if>
        ,tt_invite_vehicle_record r
        set
        t.advise_in_mileage=#{ruleValue},t.changed_id=#{changedId},t.updated_at=now(),t.updated_by='1'
        where t.invite_type=#{inviteType}
        and  t.is_create_invite=1 and r.id=t.invite_id and r.order_status=82411002
        <if test=" dealerCode !=null and dealerCode =='VCDC' ">
            and rule.id is null
        </if>
        <if test=" dealerCode !=null and dealerCode !='VCDC' ">
            and t.dealer_Code=#{dealerCode}
        </if>
    </update>

    <update id="doProDataUpdateTask">
        <foreach collection="params" item="item" index="index" open="" close="" separator=";">
            update tt_invite_vehicle_task
            set
            name=#{item.name},
            tel=#{item.tel},
            age=#{item.age},
            sex=#{item.sex},
            updated_at = now()
            where id = #{item.id}
        </foreach>
    </update>

    <update id="doProDataCloseTask">
        update tt_invite_vehicle_task
        set
        is_create_invite = 2,
        is_deleted = 1,
        invalid_reason = '重复数据关闭',
        updated_at = now()
        where id in
        <foreach collection="params" index="index" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>


    <update id="updateCloseIntervalHasDown">
        update tt_invite_vehicle_task t
        <if test=" dealerCode !=null and dealerCode =='VCDC' ">
            left join tt_invite_rule rule on rule.invite_type=#{ruleType}  and  t.dealer_code=rule.dealer_code and
            rule.dealer_code!='VCDC'
        </if>
        ,tt_invite_vehicle_record r
        set
        t.close_interval=#{closeInterval},t.remind_interval=#{remindInterval},t.updated_at=now(),t.updated_by='1'
        where t.invite_type=#{inviteType}
        and  t.is_create_invite=1 and r.id=t.invite_id and r.order_status=82411002
        <if test=" dealerCode !=null and dealerCode =='VCDC' ">
            and rule.id is null
        </if>
        <if test=" dealerCode !=null and dealerCode !='VCDC' ">
            and t.dealer_Code=#{dealerCode}
        </if>
    </update>

    <select id="selectListByVinAndInviteTypeAndDealerCodeOrderById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_vehicle_task t
       where 1= 1 and t.invite_type = #{inviteType}
        <if test=" vin !=null and vin !='' ">
            and t.vin=#{vin}
        </if>
        <if test=" dealerCode !=null and dealerCode !='' ">
            and t.dealer_Code=#{dealerCode}
        </if>
        order by id desc
        limit 1

    </select>
    <select id="selectAllByVinAndInviteTypeAndDealerCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_vehicle_task t
       where 1= 1  and  t.is_create_invite= 0 and t.invite_type = #{inviteType}
        <if test=" vin !=null and vin !='' ">
            and t.vin=#{vin}
        </if>
        <if test=" dealerCode !=null and dealerCode !='' ">
            and t.dealer_Code=#{dealerCode}
        </if>

    </select>
    <update id="newUpdateTaskCloseById">
        update tt_invite_vehicle_task
        set is_create_invite=2,invalid_reason='新进厂关闭任务'
        where
        vin = #{vin}
        and is_create_invite = 0
        and invite_type=#{inviteType}
        <if test=" dealerCode !=null and dealerCode != '' ">
            AND dealer_code = #{dealerCode}
        </if>
    </update>

    <select id="selectAllByVinAndDealerCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_vehicle_task t
        where 1= 1  and  t.is_create_invite= 0 and t.invite_type in (82381001,82381002)
        <if test=" vin !=null and vin !='' ">
            and t.vin=#{vin}
        </if>
        <if test=" dealerCode !=null and dealerCode !='' ">
            and t.dealer_Code=#{dealerCode}
        </if>

    </select>
    <select id="getTaskByAdviseInDate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
         FROM  tt_invite_vehicle_task  t where
        t.`is_create_invite` =0
        and t.`invite_type` in (82381002,82381001)
        and t.`is_deleted`  = 0
        <if test=" startDate !=null and startDate != '' ">
            AND t.advise_in_date &gt;= STR_TO_DATE(#{startDate},'%Y-%m-%d')
        </if>
        <if test=" endDate !=null and endDate != '' ">
            AND t.advise_in_date &lt; STR_TO_DATE(#{endDate},'%Y-%m-%d')
        </if>
    </select>
    <select id="selectByInviteId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        SELECT <include refid="Base_Column_List"/>
         FROM  tt_invite_vehicle_task  t  where  t.invite_id = #{inviteId}

    </select>
    <update id="updateList" parameterType="java.util.List">
        <foreach collection="updateList" item="item" index="index" open="" close="" separator=";">
            update tt_invite_vehicle_task set
            is_create_invite =2,
            invalid_reason='voc定时任务关闭',
            updated_at = now(),
            record_version = record_version+1 where id  = #{item}
        </foreach>
    </update>
    <update id="updateRecordIdListById" parameterType="com.yonyou.dmscus.customer.entity.po.vocfunctional.VocInviteVehicleTaskRecordPo">
        <foreach collection="updateList" item="item" index="index" open="" close="" separator=";">
            update tt_invite_vehicle_task set
            is_create_invite =1,
            invite_id=#{item.recordId},
            updated_at = now(),
            record_version = record_version+1 where id  =#{item.taskId}
        </foreach>
    </update>
    <insert id="insertList" parameterType="com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO" useGeneratedKeys="true" keyProperty="id">


        insert into  tt_invite_vehicle_task
        (
        vin,
        license_plate_num,
        dealer_code,
        name,
        tel,
        age,
        sex,
        model,
        daily_mileage,
        invite_type,
        advise_in_date,
        remind_interval,
        is_create_invite,
        invite_time,
        close_interval,
        create_invite_time,
        item_type,
        item_name,
        item_code,
        advise_in_mileage,
        out_mileage,
        qb_number,
        record_version,
        created_by,
        follow_status
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.vin},
            #{item.licensePlateNum},
            #{item.dealerCode},
            #{item.name},
            #{item.tel},
            #{item.age},
            #{item.sex},
            #{item.model},
            #{item.dailyMileage},
            #{item.inviteType},
            #{item.adviseInDate},
            #{item.remindInterval},
            #{item.isCreateInvite},
            #{item.inviteTime},
            #{item.closeInterval},
            #{item.createInviteTime},
            #{item.itemType},
            #{item.itemName},
            #{item.itemCode},
            #{item.adviseInMileage},
            #{item.outMileage},
            #{item.qbNumber},
            1,
            '-9',
            82401001
            )
        </foreach>

    </insert>

    <insert id="insertLossAddList" parameterType="com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO" useGeneratedKeys="true" keyProperty="id">
        insert into  tt_invite_vehicle_task
        (
        vin,
        license_plate_num,
        dealer_code,
        name,
        tel,
        age,
        sex,
        model,
        daily_mileage,
        invite_type,
        advise_in_date,
        remind_interval,
        create_invite_time,
        invite_time,
        close_interval,
        advise_in_mileage,
        out_mileage,
        record_version,
        created_by,
        updated_by
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.vin},
            #{item.licensePlateNum},
            #{item.dealerCode},
            #{item.name},
            #{item.tel},
            #{item.age},
            #{item.sex},
            #{item.model},
            #{item.dailyMileage},
            #{item.inviteType},
            #{item.adviseInDate},
            #{item.remindInterval},
            #{item.createInviteTime},
            #{item.inviteTime},
            #{item.closeInterval},
            #{item.adviseInMileage},
            #{item.outMileage},
             1,
             1,
             1
            )
        </foreach>

    </insert>

    <select id="selectListTaskVocByTime" parameterType="java.lang.String" resultType="com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO">
         select <include refid="Base_Column_List"/> from tt_invite_vehicle_task t
        left join
        tt_voc_invite_vehicle_task_record r2  on  t.id = r2.task_id  and  r2.record_type =0  and r2.is_deleted = 0  and  r2.loss_type =2
        where t.invite_type =  82381006  and  t.is_create_invite = 0  and  DATE_FORMAT(t. advise_in_date ,  '%Y-%m-%d')= DATE_FORMAT( #{dateTime} ,    '%Y-%m-%d' )  and  t.is_deleted =0
    </select>
    <update id="updateLossVocByVin" parameterType="java.lang.String">
          update tt_invite_vehicle_task r1  set  r1.is_create_invite =2 ,  r1.updated_at = now(),r1.invalid_reason='VOC流失客户定时任务关闭',
            r1.record_version = r1.record_version+1
            where r1.id  in  (
            select r2.task_id  from tt_voc_invite_vehicle_task_record r2 where  r2.vin = #{vin} and  r2.record_type=0  and r2.invite_type =82381006 and r2.loss_type=1
            )  and r1.vin =#{vin}
    </update>

    <select id="selectFixedInsuranceTask" resultType="com.yonyou.dmscus.customer.entity.po.invitationautocreate.InvVehLossCleanTaskPO">
        select id,dealer_code,vin,advise_in_date,invite_time,license_plate_num,name,
               tel,age,sex,model,daily_mileage,remind_interval,close_interval,advise_in_mileage,out_mileage
        from tt_invite_vehicle_task
        where invite_type = 82381002
          and is_create_invite = 0
          and is_deleted = 0
        limit #{startNum},#{endNum}
     </select>

    <select id="selectDeletionLossTask" resultType="com.yonyou.dmscus.customer.entity.po.invitationautocreate.InvVehLossCleanTaskPO">
        select t1.id,t1.vin
        from tt_invite_vehicle_task t1
                 left join tt_voc_invite_vehicle_task_record t2 on t1.id = t2.task_id
        where t1.invite_type = 82381006
          and t1.is_create_invite = 0
          and t1.created_at >= #{startDate}
          and t1.created_at &lt;= #{endDate}
          and t2.id is null
    </select>

    <select id="selectIncompleteTask" resultType="com.yonyou.dmscus.customer.entity.po.invitationautocreate.InvVehLossCleanTaskPO">
        select t1.id, t1.dealer_code,t1.vin,t1.advise_in_date,t1.invite_time,t1.license_plate_num,t1.name,
               t1.tel,t1.age,t1.sex,t1.model,t1.daily_mileage,t1.remind_interval,t1.close_interval,t1.advise_in_mileage,
               t1.out_mileage
        from tt_invite_vehicle_task t1
        join
        (
        select a.id
        from
        tt_invite_vehicle_record a
        left join tt_invite_vehicle_record b on a.vin = b.vin
                                             and a.dealer_code = b.dealer_code
                                             and a.invite_type = b.invite_type
                                             and a.id &lt; b.id
        where b.id is NULL
        AND a.invite_type = 82381002 and a.order_status = #{orderStatus}
        and a.dealer_code = #{dealerCode}
        ) t2
        on t1.invite_id = t2.id
        left join tt_invite_vehicle_task t3 on t1.vin = t3.vin
                                            and t1.dealer_code = t3.dealer_code
                                            and t3.invite_type = 82381002
                                            and t3.is_create_invite = 0
        left join tt_invite_vehicle_record t4 on t1.vin = t4.vin
                                              and t1.dealer_code = t4.dealer_code
                                              and t4.invite_type = 82381006
                                              and t4.order_status = 82411002
        where
        t1.invite_time >= '2022-01-01'
        and t1.is_create_invite = 1
        and t1.is_deleted = 0
        and t3.id is null
        and t4.id is null
    </select>


    <select id="selectLossTask" resultType="com.yonyou.dmscus.customer.entity.po.invitationautocreate.InvVehLossCleanTaskPO">
        select id,dealer_code,vin,advise_in_date,create_invite_time
        from tt_invite_vehicle_task
        where invite_type = 82381006
        and is_create_invite = 0
        and is_deleted = 0
        and dealer_code is not null
        and vin in
        <foreach collection="listVin" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateLossUpList" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update tt_invite_vehicle_task set
            advise_in_date = #{item.adviseInDate},
            create_invite_time = #{item.createInviteTime},
            invite_time = #{item.inviteTime},
            updated_at = now()
            where id  = #{item.id}
        </foreach>
    </update>

    <update id="updateLossDelList" parameterType="java.util.List">
        update tt_invite_vehicle_task set
        is_create_invite = 2,
        invalid_reason = '流失任务刷数据',
        is_deleted = 1,
        updated_at = now()
        where id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateIsCreateInviteByVin">
        update tt_invite_vehicle_task set
        is_create_invite = 2,
        invalid_reason = '线索黑名单',
        is_deleted = 1,
        updated_at = now()
        where vin = #{vin}
        and is_create_invite = 0
    </update>
</mapper>
