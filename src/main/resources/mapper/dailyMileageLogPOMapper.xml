<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.invitationautocreate.DailyMileageLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.invitationautocreate.DailyMileageLogPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="vin" property="vin"/>
        <result column="time_one" property="timeOne"/>
        <result column="time_two" property="timeTwo"/>
        <result column="time_three" property="timeThree"/>
        <result column="time_four" property="timeFour"/>
        <result column="sales_date" property="salesDate"/>
        <result column="mileage_one" property="mileageOne"/>
        <result column="mileage_two" property="mileageTwo"/>
        <result column="mileage_three" property="mileageThree"/>
        <result column="mileage_four" property="mileageFour"/>
        <result column="last_daily_mileage" property="lastDailyMileage"/>
        <result column="daily_mileage" property="dailyMileage"/>
        <result column="count_type" property="countType"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        app_id, owner_code, owner_par_code, org_id, id, data_sources, is_deleted, is_valid, created_at, updated_at, vin, time_one, time_two, time_three, time_four, sales_date, mileage_one, mileage_two, mileage_three, mileage_four, last_daily_mileage, daily_mileage, count_type
    </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.invitationautocreate.DailyMileageLogPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_daily_mileage_log t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.timeOne !=null and params.timeOne != '' ">
            AND t.time_one = #{params.timeOne}
        </if>
        <if test=" params.timeTwo !=null and params.timeTwo != '' ">
            AND t.time_two = #{params.timeTwo}
        </if>
        <if test=" params.timeThree !=null and params.timeThree != '' ">
            AND t.time_three = #{params.timeThree}
        </if>
        <if test=" params.timeFour !=null and params.timeFour != '' ">
            AND t.time_four = #{params.timeFour}
        </if>
        <if test=" params.salesDate !=null and params.salesDate != '' ">
            AND t.sales_date = #{params.salesDate}
        </if>
        <if test=" params.mileageOne !=null and params.mileageOne != '' ">
            AND t.mileage_one = #{params.mileageOne}
        </if>
        <if test=" params.mileageTwo !=null and params.mileageTwo != '' ">
            AND t.mileage_two = #{params.mileageTwo}
        </if>
        <if test=" params.mileageThree !=null and params.mileageThree != '' ">
            AND t.mileage_three = #{params.mileageThree}
        </if>
        <if test=" params.mileageFour !=null and params.mileageFour != '' ">
            AND t.mileage_four = #{params.mileageFour}
        </if>
        <if test=" params.lastDailyMileage !=null and params.lastDailyMileage != '' ">
            AND t.last_daily_mileage = #{params.lastDailyMileage}
        </if>
        <if test=" params.dailyMileage !=null and params.dailyMileage != '' ">
            AND t.daily_mileage = #{params.dailyMileage}
        </if>
        <if test=" params.countType !=null and params.countType != '' ">
            AND t.count_type = #{params.countType}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.invitationautocreate.DailyMileageLogPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_daily_mileage_log t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.timeOne !=null and params.timeOne != '' ">
            AND t.time_one = #{params.timeOne}
        </if>
        <if test=" params.timeTwo !=null and params.timeTwo != '' ">
            AND t.time_two = #{params.timeTwo}
        </if>
        <if test=" params.timeThree !=null and params.timeThree != '' ">
            AND t.time_three = #{params.timeThree}
        </if>
        <if test=" params.timeFour !=null and params.timeFour != '' ">
            AND t.time_four = #{params.timeFour}
        </if>
        <if test=" params.salesDate !=null and params.salesDate != '' ">
            AND t.sales_date = #{params.salesDate}
        </if>
        <if test=" params.mileageOne !=null and params.mileageOne != '' ">
            AND t.mileage_one = #{params.mileageOne}
        </if>
        <if test=" params.mileageTwo !=null and params.mileageTwo != '' ">
            AND t.mileage_two = #{params.mileageTwo}
        </if>
        <if test=" params.mileageThree !=null and params.mileageThree != '' ">
            AND t.mileage_three = #{params.mileageThree}
        </if>
        <if test=" params.mileageFour !=null and params.mileageFour != '' ">
            AND t.mileage_four = #{params.mileageFour}
        </if>
        <if test=" params.lastDailyMileage !=null and params.lastDailyMileage != '' ">
            AND t.last_daily_mileage = #{params.lastDailyMileage}
        </if>
        <if test=" params.dailyMileage !=null and params.dailyMileage != '' ">
            AND t.daily_mileage = #{params.dailyMileage}
        </if>
        <if test=" params.countType !=null and params.countType != '' ">
            AND t.count_type = #{params.countType}
        </if>
    </select>


    <insert id="SetErrorlog">
        INSERT into tt_voc_handle_log (handle_type,vin,error_msg,error_stack_msg)
                values(#{type},#{vin},#{errorMsg},#{errorStackMsg})
    </insert>

    <insert id="insertDailyMileageLog">
        INSERT into
            tt_daily_mileage_log(vin,time_one,time_two,time_three,time_four,sales_date,mileage_one,mileage_two,mileage_three,
                                 mileage_four,last_daily_mileage,daily_mileage,count_type,ro_no,dealer_code)
                VALUES(#{params.vin},#{params.timeOne},#{params.timeTwo},#{params.timeThree},#{params.timeFour},#{params.salesDate},
                       #{params.mileageOne}, #{params.mileageTwo},#{params.mileageThree},#{params.mileageFour},#{params.lastDailyMileage},
                       #{params.dailyMileage},#{params.countType},#{params.roNo},#{params.dealerCode})
    </insert>


</mapper>
