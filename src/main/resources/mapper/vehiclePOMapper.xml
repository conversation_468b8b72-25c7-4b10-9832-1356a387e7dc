<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.common.VehicleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.common.VehiclePO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="vin" property="vin"/>
        <result column="owner_no" property="ownerNo"/>
        <result column="customer_no" property="customerNo"/>
        <result column="owner_no_old" property="ownerNoOld"/>
        <result column="license" property="license"/>
        <result column="engine_no" property="engineNo"/>
        <result column="gear_box" property="gearBox"/>
        <result column="factory_date" property="factoryDate"/>
        <result column="found_date" property="foundDate"/>
        <result column="inner_color" property="innerColor"/>
        <result column="innerId" property="innerId"/>
        <result column="brand" property="brand"/>
        <result column="series" property="series"/>
        <result column="model" property="model"/>
        <result column="color" property="color"/>
        <result column="apackage" property="apackage"/>
        <result column="model_year" property="modelYear"/>
        <result column="exhaust_quantity" property="exhaustQuantity"/>
        <result column="product_date" property="productDate"/>
        <result column="shift_type" property="shiftType"/>
        <result column="fuel_type" property="fuelType"/>
        <result column="vehicle_purpose" property="vehiclePurpose"/>
        <result column="business_kind" property="businessKind"/>
        <result column="business_date" property="businessDate"/>
        <result column="engine_no_old" property="engineNoOld"/>
        <result column="last_repair_dealer" property="lastRepairDealer"/>
        <result column="change_engine_desc" property="changeEngineDesc"/>
        <result column="sales_agent_name" property="salesAgentName"/>
        <result column="consultant" property="consultant"/>
        <result column="is_allow_invitation" property="isAllowInvitation"/>
        <result column="is_self_company" property="isSelfCompany"/>
        <result column="sales_date" property="salesDate"/>
        <result column="sales_mileage" property="salesMileage"/>
        <result column="vehicle_price" property="vehiclePrice"/>
        <result column="wrt_begin_date" property="wrtBeginDate"/>
        <result column="wrt_end_date" property="wrtEndDate"/>
        <result column="wrt_begin_mileage" property="wrtBeginMileage"/>
        <result column="wrt_end_mileage" property="wrtEndMileage"/>
        <result column="license_date" property="licenseDate"/>
        <result column="mileage" property="mileage"/>
        <result column="is_change_odograph" property="isChangeOdograph"/>
        <result column="total_change_mileage" property="totalChangeMileage"/>
        <result column="change_date" property="changeDate"/>
        <result column="add_equipment" property="addEquipment"/>
        <result column="first_in_date" property="firstInDate"/>
        <result column="next_maintain_date" property="nextMaintainDate"/>
        <result column="next_maintain_mileage" property="nextMaintainMileage"/>
        <result column="daily_average_mileage" property="dailyAverageMileage"/>
        <result column="last_inspect_date" property="lastInspectDate"/>
        <result column="next_inspect_date" property="nextInspectDate"/>
        <result column="expired_date" property="expiredDate"/>
        <result column="deliverer" property="deliverer"/>
        <result column="deliverer_gender" property="delivererGender"/>
        <result column="deliverer_phone" property="delivererPhone"/>
        <result column="deliverer_mobile" property="delivererMobile"/>
        <result column="deliverer_hobby_contact" property="delivererHobbyContact"/>
        <result column="deliverer_relation_to_owner" property="delivererRelationToOwner"/>
        <result column="deliverer_company" property="delivererCompany"/>
        <result column="deliverer_credit" property="delivererCredit"/>
        <result column="deliverer_address" property="delivererAddress"/>
        <result column="zip_code" property="zipCode"/>
        <result column="chief_technician" property="chiefTechnician"/>
        <result column="service_advisor" property="serviceAdvisor"/>
        <result column="insurance_advisor" property="insuranceAdvisor"/>
        <result column="maintain_advisor" property="maintainAdvisor"/>
        <result column="exclusive_service_consultant" property="exclusiveServiceConsultant"/>
        <result column="last_sa" property="lastSa"/>
        <result column="last_maintain_date" property="lastMaintainDate"/>
        <result column="last_maintain_mileage" property="lastMaintainMileage"/>
        <result column="last_maintenance_date" property="lastMaintenanceDate"/>
        <result column="last_maintenance_mileage" property="lastMaintenanceMileage"/>
        <result column="discount_mode_code" property="discountModeCode"/>
        <result column="remark" property="remark"/>
        <result column="submit_time" property="submitTime"/>
        <result column="gear_type" property="gearType"/>
        <result column="year_model" property="yearModel"/>
        <result column="product_code" property="productCode"/>
        <result column="producting_area" property="productingArea"/>
        <result column="ro_create_date" property="roCreateDate"/>
        <result column="is_dcrc_advisor" property="isDcrcAdvisor"/>
        <result column="dcrc_advisor" property="dcrcAdvisor"/>
        <result column="vsn" property="vsn"/>
        <result column="discharge_standard" property="dischargeStandard"/>
        <result column="system_remark" property="systemRemark"/>
        <result column="system_last_maintenance_date" property="systemLastMaintenanceDate"/>
        <result column="system_update_date" property="systemUpdateDate"/>
        <result column="district" property="district"/>
        <result column="city" property="city"/>
        <result column="province" property="province"/>
        <result column="current_mileage" property="currentMileage"/>
        <result column="current_mileage_date" property="currentMileageDate"/>
        <result column="ways_to_buy" property="waysToBuy"/>
        <result column="vehicle_category" property="vehicleCategory"/>
        <result column="v_invoice_no" property="vInvoiceNo"/>
        <result column="invoice_type" property="invoiceType"/>
        <result column="approach_maintenance_time" property="approachMaintenanceTime"/>
        <result column="invoice_date" property="invoiceDate"/>
        <result column="commpany_property" property="commpanyProperty"/>
        <result column="unit_name" property="unitName"/>
        <result column="bzd" property="bzd"/>
        <result column="efw_card" property="efwCard"/>
        <result column="jd_identification" property="jdIdentification"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="driving_license" property="drivingLicense"/>
        <result column="option_packag" property="optionPackag"/>
        <result column="smuggled_goods_vehicle" property="smuggledGoodsVehicle"/>
        <result column="dynamic_code" property="dynamicCode"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="first_in_dealer" property="firstInDealer"/>
        <result column="one_id" property="oneId"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
    </resultMap>

    <!-- 里程数据 -->
    <resultMap id="vocMileagMap" type="com.yonyou.dmscus.customer.dto.VocMileageVO">
        <result column="vin" property="vin"/>
        <result column="mileage_km" property="mileageKm"/>
        <result column="get_time" property="getTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        owner_code, owner_par_code, org_id, id, vin, owner_no, customer_no, owner_no_old, license, engine_no, gear_box,
        factory_date, found_date, inner_color, innerId, brand, series, model, color, apackage, model_year, exhaust_quantity,
        product_date, shift_type, fuel_type, vehicle_purpose, business_kind, business_date, engine_no_old, last_repair_dealer,
        change_engine_desc, sales_agent_name, consultant, is_allow_invitation, is_self_company, sales_date, sales_mileage,
        vehicle_price, wrt_begin_date, wrt_end_date, wrt_begin_mileage, wrt_end_mileage, license_date, mileage,
        is_change_odograph, total_change_mileage, change_date, add_equipment, first_in_date, next_maintain_date,
        next_maintain_mileage, daily_average_mileage, last_inspect_date, next_inspect_date, expired_date, deliverer,
        deliverer_gender, deliverer_phone, deliverer_mobile, deliverer_hobby_contact, deliverer_relation_to_owner,
        deliverer_company, deliverer_credit, deliverer_address, zip_code, chief_technician, service_advisor, insurance_advisor,
        maintain_advisor, exclusive_service_consultant, last_sa, case when is_voc=10041001 then voc_time  else last_maintain_date end as last_maintain_date,
        case when is_voc=10041001 then voc_mileage else last_maintain_mileage end as last_maintain_mileage,
        last_maintenance_date, last_maintenance_mileage, discount_mode_code, remark, submit_time, gear_type, year_model,
        product_code, producting_area, ro_create_date, is_dcrc_advisor, dcrc_advisor, vsn, discharge_standard, system_remark,
        system_last_maintenance_date, system_update_date, district, city, province, current_mileage, current_mileage_date,
        ways_to_buy, vehicle_category, v_invoice_no, invoice_type, approach_maintenance_time, invoice_date, commpany_property,
        unit_name, bzd, efw_card, jd_identification, is_deleted, is_valid, driving_license, option_packag, smuggled_goods_vehicle,
        dynamic_code, created_at, updated_at, first_in_dealer, one_id
    </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.common.VehiclePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tm_vehicle t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.ownerNo !=null and params.ownerNo != '' ">
            AND t.owner_no = #{params.ownerNo}
        </if>
        <if test=" params.customerNo !=null and params.customerNo != '' ">
            AND t.customer_no = #{params.customerNo}
        </if>
        <if test=" params.ownerNoOld !=null and params.ownerNoOld != '' ">
            AND t.owner_no_old = #{params.ownerNoOld}
        </if>
        <if test=" params.license !=null and params.license != '' ">
            AND t.license = #{params.license}
        </if>
        <if test=" params.engineNo !=null and params.engineNo != '' ">
            AND t.engine_no = #{params.engineNo}
        </if>
        <if test=" params.gearBox !=null and params.gearBox != '' ">
            AND t.gear_box = #{params.gearBox}
        </if>
        <if test=" params.factoryDate !=null and params.factoryDate != '' ">
            AND t.factory_date = #{params.factoryDate}
        </if>
        <if test=" params.foundDate !=null and params.foundDate != '' ">
            AND t.found_date = #{params.foundDate}
        </if>
        <if test=" params.innerColor !=null and params.innerColor != '' ">
            AND t.inner_color = #{params.innerColor}
        </if>
        <if test=" params.innerId !=null and params.innerId != '' ">
            AND t.innerId = #{params.innerId}
        </if>
        <if test=" params.brand !=null and params.brand != '' ">
            AND t.brand = #{params.brand}
        </if>
        <if test=" params.series !=null and params.series != '' ">
            AND t.series = #{params.series}
        </if>
        <if test=" params.model !=null and params.model != '' ">
            AND t.model = #{params.model}
        </if>
        <if test=" params.color !=null and params.color != '' ">
            AND t.color = #{params.color}
        </if>
        <if test=" params.apackage !=null and params.apackage != '' ">
            AND t.apackage = #{params.apackage}
        </if>
        <if test=" params.modelYear !=null and params.modelYear != '' ">
            AND t.model_year = #{params.modelYear}
        </if>
        <if test=" params.exhaustQuantity !=null and params.exhaustQuantity != '' ">
            AND t.exhaust_quantity = #{params.exhaustQuantity}
        </if>
        <if test=" params.productDate !=null and params.productDate != '' ">
            AND t.product_date = #{params.productDate}
        </if>
        <if test=" params.shiftType !=null and params.shiftType != '' ">
            AND t.shift_type = #{params.shiftType}
        </if>
        <if test=" params.fuelType !=null and params.fuelType != '' ">
            AND t.fuel_type = #{params.fuelType}
        </if>
        <if test=" params.vehiclePurpose !=null and params.vehiclePurpose != '' ">
            AND t.vehicle_purpose = #{params.vehiclePurpose}
        </if>
        <if test=" params.businessKind !=null and params.businessKind != '' ">
            AND t.business_kind = #{params.businessKind}
        </if>
        <if test=" params.businessDate !=null and params.businessDate != '' ">
            AND t.business_date = #{params.businessDate}
        </if>
        <if test=" params.engineNoOld !=null and params.engineNoOld != '' ">
            AND t.engine_no_old = #{params.engineNoOld}
        </if>
        <if test=" params.lastRepairDealer !=null and params.lastRepairDealer != '' ">
            AND t.last_repair_dealer = #{params.lastRepairDealer}
        </if>
        <if test=" params.changeEngineDesc !=null and params.changeEngineDesc != '' ">
            AND t.change_engine_desc = #{params.changeEngineDesc}
        </if>
        <if test=" params.salesAgentName !=null and params.salesAgentName != '' ">
            AND t.sales_agent_name = #{params.salesAgentName}
        </if>
        <if test=" params.consultant !=null and params.consultant != '' ">
            AND t.consultant = #{params.consultant}
        </if>
        <if test=" params.isAllowInvitation !=null and params.isAllowInvitation != '' ">
            AND t.is_allow_invitation = #{params.isAllowInvitation}
        </if>
        <if test=" params.isSelfCompany !=null and params.isSelfCompany != '' ">
            AND t.is_self_company = #{params.isSelfCompany}
        </if>
        <if test=" params.salesDate !=null and params.salesDate != '' ">
            AND t.sales_date = #{params.salesDate}
        </if>
        <if test=" params.salesMileage !=null and params.salesMileage != '' ">
            AND t.sales_mileage = #{params.salesMileage}
        </if>
        <if test=" params.vehiclePrice !=null and params.vehiclePrice != '' ">
            AND t.vehicle_price = #{params.vehiclePrice}
        </if>
        <if test=" params.wrtBeginDate !=null and params.wrtBeginDate != '' ">
            AND t.wrt_begin_date = #{params.wrtBeginDate}
        </if>
        <if test=" params.wrtEndDate !=null and params.wrtEndDate != '' ">
            AND t.wrt_end_date = #{params.wrtEndDate}
        </if>
        <if test=" params.wrtBeginMileage !=null and params.wrtBeginMileage != '' ">
            AND t.wrt_begin_mileage = #{params.wrtBeginMileage}
        </if>
        <if test=" params.wrtEndMileage !=null and params.wrtEndMileage != '' ">
            AND t.wrt_end_mileage = #{params.wrtEndMileage}
        </if>
        <if test=" params.licenseDate !=null and params.licenseDate != '' ">
            AND t.license_date = #{params.licenseDate}
        </if>
        <if test=" params.mileage !=null and params.mileage != '' ">
            AND t.mileage = #{params.mileage}
        </if>
        <if test=" params.isChangeOdograph !=null and params.isChangeOdograph != '' ">
            AND t.is_change_odograph = #{params.isChangeOdograph}
        </if>
        <if test=" params.totalChangeMileage !=null and params.totalChangeMileage != '' ">
            AND t.total_change_mileage = #{params.totalChangeMileage}
        </if>
        <if test=" params.changeDate !=null and params.changeDate != '' ">
            AND t.change_date = #{params.changeDate}
        </if>
        <if test=" params.addEquipment !=null and params.addEquipment != '' ">
            AND t.add_equipment = #{params.addEquipment}
        </if>
        <if test=" params.firstInDate !=null and params.firstInDate != '' ">
            AND t.first_in_date = #{params.firstInDate}
        </if>
        <if test=" params.nextMaintainDate !=null and params.nextMaintainDate != '' ">
            AND t.next_maintain_date = #{params.nextMaintainDate}
        </if>
        <if test=" params.nextMaintainMileage !=null and params.nextMaintainMileage != '' ">
            AND t.next_maintain_mileage = #{params.nextMaintainMileage}
        </if>
        <if test=" params.dailyAverageMileage !=null and params.dailyAverageMileage != '' ">
            AND t.daily_average_mileage = #{params.dailyAverageMileage}
        </if>
        <if test=" params.lastInspectDate !=null and params.lastInspectDate != '' ">
            AND t.last_inspect_date = #{params.lastInspectDate}
        </if>
        <if test=" params.nextInspectDate !=null and params.nextInspectDate != '' ">
            AND t.next_inspect_date = #{params.nextInspectDate}
        </if>
        <if test=" params.expiredDate !=null and params.expiredDate != '' ">
            AND t.expired_date = #{params.expiredDate}
        </if>
        <if test=" params.deliverer !=null and params.deliverer != '' ">
            AND t.deliverer = #{params.deliverer}
        </if>
        <if test=" params.delivererGender !=null and params.delivererGender != '' ">
            AND t.deliverer_gender = #{params.delivererGender}
        </if>
        <if test=" params.delivererPhone !=null and params.delivererPhone != '' ">
            AND t.deliverer_phone = #{params.delivererPhone}
        </if>
        <if test=" params.delivererMobile !=null and params.delivererMobile != '' ">
            AND t.deliverer_mobile = #{params.delivererMobile}
        </if>
        <if test=" params.delivererHobbyContact !=null and params.delivererHobbyContact != '' ">
            AND t.deliverer_hobby_contact = #{params.delivererHobbyContact}
        </if>
        <if test=" params.delivererRelationToOwner !=null and params.delivererRelationToOwner != '' ">
            AND t.deliverer_relation_to_owner = #{params.delivererRelationToOwner}
        </if>
        <if test=" params.delivererCompany !=null and params.delivererCompany != '' ">
            AND t.deliverer_company = #{params.delivererCompany}
        </if>
        <if test=" params.delivererCredit !=null and params.delivererCredit != '' ">
            AND t.deliverer_credit = #{params.delivererCredit}
        </if>
        <if test=" params.delivererAddress !=null and params.delivererAddress != '' ">
            AND t.deliverer_address = #{params.delivererAddress}
        </if>
        <if test=" params.zipCode !=null and params.zipCode != '' ">
            AND t.zip_code = #{params.zipCode}
        </if>
        <if test=" params.chiefTechnician !=null and params.chiefTechnician != '' ">
            AND t.chief_technician = #{params.chiefTechnician}
        </if>
        <if test=" params.serviceAdvisor !=null and params.serviceAdvisor != '' ">
            AND t.service_advisor = #{params.serviceAdvisor}
        </if>
        <if test=" params.insuranceAdvisor !=null and params.insuranceAdvisor != '' ">
            AND t.insurance_advisor = #{params.insuranceAdvisor}
        </if>
        <if test=" params.maintainAdvisor !=null and params.maintainAdvisor != '' ">
            AND t.maintain_advisor = #{params.maintainAdvisor}
        </if>
        <if test=" params.exclusiveServiceConsultant !=null and params.exclusiveServiceConsultant != '' ">
            AND t.exclusive_service_consultant = #{params.exclusiveServiceConsultant}
        </if>
        <if test=" params.lastSa !=null and params.lastSa != '' ">
            AND t.last_sa = #{params.lastSa}
        </if>
        <if test=" params.lastMaintainDate !=null and params.lastMaintainDate != '' ">
            AND t.last_maintain_date = #{params.lastMaintainDate}
        </if>
        <if test=" params.lastMaintainMileage !=null and params.lastMaintainMileage != '' ">
            AND t.last_maintain_mileage = #{params.lastMaintainMileage}
        </if>
        <if test=" params.lastMaintenanceDate !=null and params.lastMaintenanceDate != '' ">
            AND t.last_maintenance_date = #{params.lastMaintenanceDate}
        </if>
        <if test=" params.lastMaintenanceMileage !=null and params.lastMaintenanceMileage != '' ">
            AND t.last_maintenance_mileage = #{params.lastMaintenanceMileage}
        </if>
        <if test=" params.discountModeCode !=null and params.discountModeCode != '' ">
            AND t.discount_mode_code = #{params.discountModeCode}
        </if>
        <if test=" params.remark !=null and params.remark != '' ">
            AND t.remark = #{params.remark}
        </if>
        <if test=" params.submitTime !=null and params.submitTime != '' ">
            AND t.submit_time = #{params.submitTime}
        </if>
        <if test=" params.gearType !=null and params.gearType != '' ">
            AND t.gear_type = #{params.gearType}
        </if>
        <if test=" params.yearModel !=null and params.yearModel != '' ">
            AND t.year_model = #{params.yearModel}
        </if>
        <if test=" params.productCode !=null and params.productCode != '' ">
            AND t.product_code = #{params.productCode}
        </if>
        <if test=" params.productingArea !=null and params.productingArea != '' ">
            AND t.producting_area = #{params.productingArea}
        </if>
        <if test=" params.roCreateDate !=null and params.roCreateDate != '' ">
            AND t.ro_create_date = #{params.roCreateDate}
        </if>
        <if test=" params.isDcrcAdvisor !=null and params.isDcrcAdvisor != '' ">
            AND t.is_dcrc_advisor = #{params.isDcrcAdvisor}
        </if>
        <if test=" params.dcrcAdvisor !=null and params.dcrcAdvisor != '' ">
            AND t.dcrc_advisor = #{params.dcrcAdvisor}
        </if>
        <if test=" params.vsn !=null and params.vsn != '' ">
            AND t.vsn = #{params.vsn}
        </if>
        <if test=" params.dischargeStandard !=null and params.dischargeStandard != '' ">
            AND t.discharge_standard = #{params.dischargeStandard}
        </if>
        <if test=" params.systemRemark !=null and params.systemRemark != '' ">
            AND t.system_remark = #{params.systemRemark}
        </if>
        <if test=" params.systemLastMaintenanceDate !=null and params.systemLastMaintenanceDate != '' ">
            AND t.system_last_maintenance_date = #{params.systemLastMaintenanceDate}
        </if>
        <if test=" params.systemUpdateDate !=null and params.systemUpdateDate != '' ">
            AND t.system_update_date = #{params.systemUpdateDate}
        </if>
        <if test=" params.district !=null and params.district != '' ">
            AND t.district = #{params.district}
        </if>
        <if test=" params.city !=null and params.city != '' ">
            AND t.city = #{params.city}
        </if>
        <if test=" params.province !=null and params.province != '' ">
            AND t.province = #{params.province}
        </if>
        <if test=" params.currentMileage !=null and params.currentMileage != '' ">
            AND t.current_mileage = #{params.currentMileage}
        </if>
        <if test=" params.currentMileageDate !=null and params.currentMileageDate != '' ">
            AND t.current_mileage_date = #{params.currentMileageDate}
        </if>
        <if test=" params.waysToBuy !=null and params.waysToBuy != '' ">
            AND t.ways_to_buy = #{params.waysToBuy}
        </if>
        <if test=" params.vehicleCategory !=null and params.vehicleCategory != '' ">
            AND t.vehicle_category = #{params.vehicleCategory}
        </if>
        <if test=" params.vInvoiceNo !=null and params.vInvoiceNo != '' ">
            AND t.v_invoice_no = #{params.vInvoiceNo}
        </if>
        <if test=" params.invoiceType !=null and params.invoiceType != '' ">
            AND t.invoice_type = #{params.invoiceType}
        </if>
        <if test=" params.approachMaintenanceTime !=null and params.approachMaintenanceTime != '' ">
            AND t.approach_maintenance_time = #{params.approachMaintenanceTime}
        </if>
        <if test=" params.invoiceDate !=null and params.invoiceDate != '' ">
            AND t.invoice_date = #{params.invoiceDate}
        </if>
        <if test=" params.commpanyProperty !=null and params.commpanyProperty != '' ">
            AND t.commpany_property = #{params.commpanyProperty}
        </if>
        <if test=" params.unitName !=null and params.unitName != '' ">
            AND t.unit_name = #{params.unitName}
        </if>
        <if test=" params.bzd !=null and params.bzd != '' ">
            AND t.bzd = #{params.bzd}
        </if>
        <if test=" params.efwCard !=null and params.efwCard != '' ">
            AND t.efw_card = #{params.efwCard}
        </if>
        <if test=" params.jdIdentification !=null and params.jdIdentification != '' ">
            AND t.jd_identification = #{params.jdIdentification}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.drivingLicense !=null and params.drivingLicense != '' ">
            AND t.driving_license = #{params.drivingLicense}
        </if>
        <if test=" params.optionPackag !=null and params.optionPackag != '' ">
            AND t.option_packag = #{params.optionPackag}
        </if>
        <if test=" params.smuggledGoodsVehicle !=null and params.smuggledGoodsVehicle != '' ">
            AND t.smuggled_goods_vehicle = #{params.smuggledGoodsVehicle}
        </if>
        <if test=" params.dynamicCode !=null and params.dynamicCode != '' ">
            AND t.dynamic_code = #{params.dynamicCode}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        <if test=" params.version !=null and params.version != '' ">
            AND t.version = #{params.version}
        </if>
        <if test=" params.firstInDealer !=null and params.firstInDealer != '' ">
            AND t.first_in_dealer = #{params.firstInDealer}
        </if>
        <if test=" params.oneId !=null and params.oneId != '' ">
            AND t.one_id = #{params.oneId}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.common.VehiclePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tm_vehicle t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.ownerNo !=null and params.ownerNo != '' ">
            AND t.owner_no = #{params.ownerNo}
        </if>
        <if test=" params.customerNo !=null and params.customerNo != '' ">
            AND t.customer_no = #{params.customerNo}
        </if>
        <if test=" params.ownerNoOld !=null and params.ownerNoOld != '' ">
            AND t.owner_no_old = #{params.ownerNoOld}
        </if>
        <if test=" params.license !=null and params.license != '' ">
            AND t.license = #{params.license}
        </if>
        <if test=" params.engineNo !=null and params.engineNo != '' ">
            AND t.engine_no = #{params.engineNo}
        </if>
        <if test=" params.gearBox !=null and params.gearBox != '' ">
            AND t.gear_box = #{params.gearBox}
        </if>
        <if test=" params.factoryDate !=null and params.factoryDate != '' ">
            AND t.factory_date = #{params.factoryDate}
        </if>
        <if test=" params.foundDate !=null and params.foundDate != '' ">
            AND t.found_date = #{params.foundDate}
        </if>
        <if test=" params.innerColor !=null and params.innerColor != '' ">
            AND t.inner_color = #{params.innerColor}
        </if>
        <if test=" params.innerId !=null and params.innerId != '' ">
            AND t.innerId = #{params.innerId}
        </if>
        <if test=" params.brand !=null and params.brand != '' ">
            AND t.brand = #{params.brand}
        </if>
        <if test=" params.series !=null and params.series != '' ">
            AND t.series = #{params.series}
        </if>
        <if test=" params.model !=null and params.model != '' ">
            AND t.model = #{params.model}
        </if>
        <if test=" params.color !=null and params.color != '' ">
            AND t.color = #{params.color}
        </if>
        <if test=" params.apackage !=null and params.apackage != '' ">
            AND t.apackage = #{params.apackage}
        </if>
        <if test=" params.modelYear !=null and params.modelYear != '' ">
            AND t.model_year = #{params.modelYear}
        </if>
        <if test=" params.exhaustQuantity !=null and params.exhaustQuantity != '' ">
            AND t.exhaust_quantity = #{params.exhaustQuantity}
        </if>
        <if test=" params.productDate !=null and params.productDate != '' ">
            AND t.product_date = #{params.productDate}
        </if>
        <if test=" params.shiftType !=null and params.shiftType != '' ">
            AND t.shift_type = #{params.shiftType}
        </if>
        <if test=" params.fuelType !=null and params.fuelType != '' ">
            AND t.fuel_type = #{params.fuelType}
        </if>
        <if test=" params.vehiclePurpose !=null and params.vehiclePurpose != '' ">
            AND t.vehicle_purpose = #{params.vehiclePurpose}
        </if>
        <if test=" params.businessKind !=null and params.businessKind != '' ">
            AND t.business_kind = #{params.businessKind}
        </if>
        <if test=" params.businessDate !=null and params.businessDate != '' ">
            AND t.business_date = #{params.businessDate}
        </if>
        <if test=" params.engineNoOld !=null and params.engineNoOld != '' ">
            AND t.engine_no_old = #{params.engineNoOld}
        </if>
        <if test=" params.lastRepairDealer !=null and params.lastRepairDealer != '' ">
            AND t.last_repair_dealer = #{params.lastRepairDealer}
        </if>
        <if test=" params.changeEngineDesc !=null and params.changeEngineDesc != '' ">
            AND t.change_engine_desc = #{params.changeEngineDesc}
        </if>
        <if test=" params.salesAgentName !=null and params.salesAgentName != '' ">
            AND t.sales_agent_name = #{params.salesAgentName}
        </if>
        <if test=" params.consultant !=null and params.consultant != '' ">
            AND t.consultant = #{params.consultant}
        </if>
        <if test=" params.isAllowInvitation !=null and params.isAllowInvitation != '' ">
            AND t.is_allow_invitation = #{params.isAllowInvitation}
        </if>
        <if test=" params.isSelfCompany !=null and params.isSelfCompany != '' ">
            AND t.is_self_company = #{params.isSelfCompany}
        </if>
        <if test=" params.salesDate !=null and params.salesDate != '' ">
            AND t.sales_date = #{params.salesDate}
        </if>
        <if test=" params.salesMileage !=null and params.salesMileage != '' ">
            AND t.sales_mileage = #{params.salesMileage}
        </if>
        <if test=" params.vehiclePrice !=null and params.vehiclePrice != '' ">
            AND t.vehicle_price = #{params.vehiclePrice}
        </if>
        <if test=" params.wrtBeginDate !=null and params.wrtBeginDate != '' ">
            AND t.wrt_begin_date = #{params.wrtBeginDate}
        </if>
        <if test=" params.wrtEndDate !=null and params.wrtEndDate != '' ">
            AND t.wrt_end_date = #{params.wrtEndDate}
        </if>
        <if test=" params.wrtBeginMileage !=null and params.wrtBeginMileage != '' ">
            AND t.wrt_begin_mileage = #{params.wrtBeginMileage}
        </if>
        <if test=" params.wrtEndMileage !=null and params.wrtEndMileage != '' ">
            AND t.wrt_end_mileage = #{params.wrtEndMileage}
        </if>
        <if test=" params.licenseDate !=null and params.licenseDate != '' ">
            AND t.license_date = #{params.licenseDate}
        </if>
        <if test=" params.mileage !=null and params.mileage != '' ">
            AND t.mileage = #{params.mileage}
        </if>
        <if test=" params.isChangeOdograph !=null and params.isChangeOdograph != '' ">
            AND t.is_change_odograph = #{params.isChangeOdograph}
        </if>
        <if test=" params.totalChangeMileage !=null and params.totalChangeMileage != '' ">
            AND t.total_change_mileage = #{params.totalChangeMileage}
        </if>
        <if test=" params.changeDate !=null and params.changeDate != '' ">
            AND t.change_date = #{params.changeDate}
        </if>
        <if test=" params.addEquipment !=null and params.addEquipment != '' ">
            AND t.add_equipment = #{params.addEquipment}
        </if>
        <if test=" params.firstInDate !=null and params.firstInDate != '' ">
            AND t.first_in_date = #{params.firstInDate}
        </if>
        <if test=" params.nextMaintainDate !=null and params.nextMaintainDate != '' ">
            AND t.next_maintain_date = #{params.nextMaintainDate}
        </if>
        <if test=" params.nextMaintainMileage !=null and params.nextMaintainMileage != '' ">
            AND t.next_maintain_mileage = #{params.nextMaintainMileage}
        </if>
        <if test=" params.dailyAverageMileage !=null and params.dailyAverageMileage != '' ">
            AND t.daily_average_mileage = #{params.dailyAverageMileage}
        </if>
        <if test=" params.lastInspectDate !=null and params.lastInspectDate != '' ">
            AND t.last_inspect_date = #{params.lastInspectDate}
        </if>
        <if test=" params.nextInspectDate !=null and params.nextInspectDate != '' ">
            AND t.next_inspect_date = #{params.nextInspectDate}
        </if>
        <if test=" params.expiredDate !=null and params.expiredDate != '' ">
            AND t.expired_date = #{params.expiredDate}
        </if>
        <if test=" params.deliverer !=null and params.deliverer != '' ">
            AND t.deliverer = #{params.deliverer}
        </if>
        <if test=" params.delivererGender !=null and params.delivererGender != '' ">
            AND t.deliverer_gender = #{params.delivererGender}
        </if>
        <if test=" params.delivererPhone !=null and params.delivererPhone != '' ">
            AND t.deliverer_phone = #{params.delivererPhone}
        </if>
        <if test=" params.delivererMobile !=null and params.delivererMobile != '' ">
            AND t.deliverer_mobile = #{params.delivererMobile}
        </if>
        <if test=" params.delivererHobbyContact !=null and params.delivererHobbyContact != '' ">
            AND t.deliverer_hobby_contact = #{params.delivererHobbyContact}
        </if>
        <if test=" params.delivererRelationToOwner !=null and params.delivererRelationToOwner != '' ">
            AND t.deliverer_relation_to_owner = #{params.delivererRelationToOwner}
        </if>
        <if test=" params.delivererCompany !=null and params.delivererCompany != '' ">
            AND t.deliverer_company = #{params.delivererCompany}
        </if>
        <if test=" params.delivererCredit !=null and params.delivererCredit != '' ">
            AND t.deliverer_credit = #{params.delivererCredit}
        </if>
        <if test=" params.delivererAddress !=null and params.delivererAddress != '' ">
            AND t.deliverer_address = #{params.delivererAddress}
        </if>
        <if test=" params.zipCode !=null and params.zipCode != '' ">
            AND t.zip_code = #{params.zipCode}
        </if>
        <if test=" params.chiefTechnician !=null and params.chiefTechnician != '' ">
            AND t.chief_technician = #{params.chiefTechnician}
        </if>
        <if test=" params.serviceAdvisor !=null and params.serviceAdvisor != '' ">
            AND t.service_advisor = #{params.serviceAdvisor}
        </if>
        <if test=" params.insuranceAdvisor !=null and params.insuranceAdvisor != '' ">
            AND t.insurance_advisor = #{params.insuranceAdvisor}
        </if>
        <if test=" params.maintainAdvisor !=null and params.maintainAdvisor != '' ">
            AND t.maintain_advisor = #{params.maintainAdvisor}
        </if>
        <if test=" params.exclusiveServiceConsultant !=null and params.exclusiveServiceConsultant != '' ">
            AND t.exclusive_service_consultant = #{params.exclusiveServiceConsultant}
        </if>
        <if test=" params.lastSa !=null and params.lastSa != '' ">
            AND t.last_sa = #{params.lastSa}
        </if>
        <if test=" params.lastMaintainDate !=null and params.lastMaintainDate != '' ">
            AND t.last_maintain_date = #{params.lastMaintainDate}
        </if>
        <if test=" params.lastMaintainMileage !=null and params.lastMaintainMileage != '' ">
            AND t.last_maintain_mileage = #{params.lastMaintainMileage}
        </if>
        <if test=" params.lastMaintenanceDate !=null and params.lastMaintenanceDate != '' ">
            AND t.last_maintenance_date = #{params.lastMaintenanceDate}
        </if>
        <if test=" params.lastMaintenanceMileage !=null and params.lastMaintenanceMileage != '' ">
            AND t.last_maintenance_mileage = #{params.lastMaintenanceMileage}
        </if>
        <if test=" params.discountModeCode !=null and params.discountModeCode != '' ">
            AND t.discount_mode_code = #{params.discountModeCode}
        </if>
        <if test=" params.remark !=null and params.remark != '' ">
            AND t.remark = #{params.remark}
        </if>
        <if test=" params.submitTime !=null and params.submitTime != '' ">
            AND t.submit_time = #{params.submitTime}
        </if>
        <if test=" params.gearType !=null and params.gearType != '' ">
            AND t.gear_type = #{params.gearType}
        </if>
        <if test=" params.yearModel !=null and params.yearModel != '' ">
            AND t.year_model = #{params.yearModel}
        </if>
        <if test=" params.productCode !=null and params.productCode != '' ">
            AND t.product_code = #{params.productCode}
        </if>
        <if test=" params.productingArea !=null and params.productingArea != '' ">
            AND t.producting_area = #{params.productingArea}
        </if>
        <if test=" params.roCreateDate !=null and params.roCreateDate != '' ">
            AND t.ro_create_date = #{params.roCreateDate}
        </if>
        <if test=" params.isDcrcAdvisor !=null and params.isDcrcAdvisor != '' ">
            AND t.is_dcrc_advisor = #{params.isDcrcAdvisor}
        </if>
        <if test=" params.dcrcAdvisor !=null and params.dcrcAdvisor != '' ">
            AND t.dcrc_advisor = #{params.dcrcAdvisor}
        </if>
        <if test=" params.vsn !=null and params.vsn != '' ">
            AND t.vsn = #{params.vsn}
        </if>
        <if test=" params.dischargeStandard !=null and params.dischargeStandard != '' ">
            AND t.discharge_standard = #{params.dischargeStandard}
        </if>
        <if test=" params.systemRemark !=null and params.systemRemark != '' ">
            AND t.system_remark = #{params.systemRemark}
        </if>
        <if test=" params.systemLastMaintenanceDate !=null and params.systemLastMaintenanceDate != '' ">
            AND t.system_last_maintenance_date = #{params.systemLastMaintenanceDate}
        </if>
        <if test=" params.systemUpdateDate !=null and params.systemUpdateDate != '' ">
            AND t.system_update_date = #{params.systemUpdateDate}
        </if>
        <if test=" params.district !=null and params.district != '' ">
            AND t.district = #{params.district}
        </if>
        <if test=" params.city !=null and params.city != '' ">
            AND t.city = #{params.city}
        </if>
        <if test=" params.province !=null and params.province != '' ">
            AND t.province = #{params.province}
        </if>
        <if test=" params.currentMileage !=null and params.currentMileage != '' ">
            AND t.current_mileage = #{params.currentMileage}
        </if>
        <if test=" params.currentMileageDate !=null and params.currentMileageDate != '' ">
            AND t.current_mileage_date = #{params.currentMileageDate}
        </if>
        <if test=" params.waysToBuy !=null and params.waysToBuy != '' ">
            AND t.ways_to_buy = #{params.waysToBuy}
        </if>
        <if test=" params.vehicleCategory !=null and params.vehicleCategory != '' ">
            AND t.vehicle_category = #{params.vehicleCategory}
        </if>
        <if test=" params.vInvoiceNo !=null and params.vInvoiceNo != '' ">
            AND t.v_invoice_no = #{params.vInvoiceNo}
        </if>
        <if test=" params.invoiceType !=null and params.invoiceType != '' ">
            AND t.invoice_type = #{params.invoiceType}
        </if>
        <if test=" params.approachMaintenanceTime !=null and params.approachMaintenanceTime != '' ">
            AND t.approach_maintenance_time = #{params.approachMaintenanceTime}
        </if>
        <if test=" params.invoiceDate !=null and params.invoiceDate != '' ">
            AND t.invoice_date = #{params.invoiceDate}
        </if>
        <if test=" params.commpanyProperty !=null and params.commpanyProperty != '' ">
            AND t.commpany_property = #{params.commpanyProperty}
        </if>
        <if test=" params.unitName !=null and params.unitName != '' ">
            AND t.unit_name = #{params.unitName}
        </if>
        <if test=" params.bzd !=null and params.bzd != '' ">
            AND t.bzd = #{params.bzd}
        </if>
        <if test=" params.efwCard !=null and params.efwCard != '' ">
            AND t.efw_card = #{params.efwCard}
        </if>
        <if test=" params.jdIdentification !=null and params.jdIdentification != '' ">
            AND t.jd_identification = #{params.jdIdentification}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.drivingLicense !=null and params.drivingLicense != '' ">
            AND t.driving_license = #{params.drivingLicense}
        </if>
        <if test=" params.optionPackag !=null and params.optionPackag != '' ">
            AND t.option_packag = #{params.optionPackag}
        </if>
        <if test=" params.smuggledGoodsVehicle !=null and params.smuggledGoodsVehicle != '' ">
            AND t.smuggled_goods_vehicle = #{params.smuggledGoodsVehicle}
        </if>
        <if test=" params.dynamicCode !=null and params.dynamicCode != '' ">
            AND t.dynamic_code = #{params.dynamicCode}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        <if test=" params.version !=null and params.version != '' ">
            AND t.version = #{params.version}
        </if>
        <if test=" params.firstInDealer !=null and params.firstInDealer != '' ">
            AND t.first_in_dealer = #{params.firstInDealer}
        </if>
        <if test=" params.oneId !=null and params.oneId != '' ">
            AND t.one_id = #{params.oneId}
        </if>
    </select>

    <select id="getVehicleByVin" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tm_vehicle t
        WHERE 1=1
        and t.vin = #{vin}
    </select>

    <select id="selectDateByVin" resultMap="BaseResultMap">
        SELECT last_maintain_date,last_maintenance_date
        FROM tm_vehicle
        WHERE vin = #{vin}
    </select>

    <insert id="insertVoc">
        INSERT into tm_vehicle (vin,voc_mileage,voc_time,is_voc,daily_average_mileage,created_by)
        values(#{params.vin},#{params.vocMileage},#{params.vocTime},#{params.isVoc},#{params.dailyAverageMileage},'1')
    </insert>

    <insert id="insertVocAll">
        INSERT into tm_vehicle (vin,voc_mileage,voc_time,is_voc,daily_average_mileage,created_by)
        values
        <foreach collection="params" item="item" index="index" separator="," >
            (#{item.vin},#{item.vocMileage},#{item.vocTime},#{item.isVoc},#{item.dailyAverageMileage},'1')
        </foreach>
    </insert>


    <update id="updateVocById">
        UPDATE tm_vehicle
        set is_voc=#{params.isVoc}
        <if test=" params.vocMileage !=null">
          ,voc_mileage = #{params.vocMileage}
        </if>
        <if test=" params.vocTime !=null">
          ,voc_time = #{params.vocTime}
        </if>
        <if test=" params.dailyAverageMileage !=null">
            ,daily_average_mileage = #{params.dailyAverageMileage}
        </if>
        where
           id=#{params.id}
    </update>

    <update id="updateVocByIdAll">
    <foreach collection="params" item="item" index="index" open="" close="" separator=";">
        UPDATE tm_vehicle
        set is_voc=#{item.isVoc}
        <if test=" item.vocMileage !=null">
            ,voc_mileage = #{item.vocMileage}
        </if>
        <if test=" item.vocTime !=null">
            ,voc_time = #{item.vocTime}
        </if>
        <if test=" item.dailyAverageMileage !=null">
            ,daily_average_mileage = #{item.dailyAverageMileage}
        </if>
        where
        id=#{item.id}
    </foreach>
    </update>


    <select id="getAllVeh" resultMap="BaseResultMap">
        select id,vin,invoice_date from dms_manage.tm_vehicle a
            where 1=1
            and exists(select 1 from cyx_repair.TT_REPAIR_ORDER_HIS ro where ro.vin=a.vin and ro.REPAIR_TYPE_CODE !='P')
            and not exists(select 1 from dms_manage.tt_daily_mileage_log l where a.vin=l.vin)
        LIMIT 0,100000
    </select>

    <select id="getMileage" resultMap="vocMileagMap">
        SELECT
        t.vin,
        t.mileage_km,
        t.get_time
        FROM
        (
        SELECT
        b.vin,
        ifnull(b.OUT_MILEAGE,b.IN_MILEAGE)     AS mileage_km,
        b.FOR_BALANCE_TIME AS get_time
        FROM cyx_repair.TT_REPAIR_ORDER_HIS b
        WHERE b.FOR_BALANCE_TIME IS NOT NULL AND vin = #{vin}
        AND b.REPAIR_TYPE_CODE !='P'
        ) t
        ORDER BY t.get_time DESC
    </select>

    <update id="updateDailyAverageMileageById">
        UPDATE dms_manage.tm_vehicle
        set daily_average_mileage = #{params.dailyAverageMileage}
        where id=#{params.id}
    </update>

</mapper>
