<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintNotCloseCaseReasonMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintNotCloseCaseReasonPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <result column="app_id" property="appId"/>
                                                        <result column="owner_code" property="ownerCode"/>
                                                        <result column="owner_par_code" property="ownerParCode"/>
                                                        <result column="org_id" property="orgId"/>
                                                                                    <result column="complaint_info_id" property="complaintInfoId"/>
                                                        <result column="object" property="object"/>
                                                        <result column="follow_time" property="followTime"/>
                                                        <result column="follower" property="follower"/>
                                                        <result column="class_code" property="classCode"/>
                                                        <result column="class_name" property="className"/>
                                                        <result column="other" property="other"/>
                                                        <result column="duration" property="duration"/>
                                                        <result column="data_sources" property="dataSources"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="is_valid" property="isValid"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                        <result column="follower_name" property="followerName"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, complaint_info_id, object, follow_time, follower, class_code, class_name, other, duration, data_sources, is_deleted, is_valid, created_at, updated_at, follower_name
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintNotCloseCaseReasonPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_not_close_case_reason t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.complaintInfoId !=null and params.complaintInfoId != '' ">
                AND t.complaint_info_id = #{params.complaintInfoId}
            </if>
                    <if test=" params.object !=null and params.object != '' ">
                AND t.object = #{params.object}
            </if>
                    <if test=" params.followTime !=null and params.followTime != '' ">
                AND t.follow_time = #{params.followTime}
            </if>
                    <if test=" params.follower !=null and params.follower != '' ">
                AND t.follower = #{params.follower}
            </if>
                    <if test=" params.classCode !=null and params.classCode != '' ">
                AND t.class_code = #{params.classCode}
            </if>
                    <if test=" params.className !=null and params.className != '' ">
                AND t.class_name = #{params.className}
            </if>
                    <if test=" params.other !=null and params.other != '' ">
                AND t.other = #{params.other}
            </if>
                    <if test=" params.duration !=null and params.duration != '' ">
                AND t.duration = #{params.duration}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
                    <if test=" params.followerName !=null and params.followerName != '' ">
                AND t.follower_name = #{params.followerName}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintNotCloseCaseReasonPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_not_close_case_reason t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.complaintInfoId !=null and params.complaintInfoId != '' ">
                AND t.complaint_info_id = #{params.complaintInfoId}
            </if>
                    <if test=" params.object !=null and params.object != '' ">
                AND t.object = #{params.object}
            </if>
                    <if test=" params.followTime !=null and params.followTime != '' ">
                AND t.follow_time = #{params.followTime}
            </if>
                    <if test=" params.follower !=null and params.follower != '' ">
                AND t.follower = #{params.follower}
            </if>
                    <if test=" params.classCode !=null and params.classCode != '' ">
                AND t.class_code = #{params.classCode}
            </if>
                    <if test=" params.className !=null and params.className != '' ">
                AND t.class_name = #{params.className}
            </if>
                    <if test=" params.other !=null and params.other != '' ">
                AND t.other = #{params.other}
            </if>
                    <if test=" params.duration !=null and params.duration != '' ">
                AND t.duration = #{params.duration}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
                    <if test=" params.followerName !=null and params.followerName != '' ">
                AND t.follower_name = #{params.followerName}
            </if>
            </select>

    <select id="selectPageBySql2" resultType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintNotCloseCaseReasonTestPO" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintNotCloseCaseReasonTestPO">
        SELECT
        t.app_id, t.owner_code, t.owner_par_code, t.org_id, t.id, t.complaint_info_id, t.object, t.follow_time, t.follower, t.class_code, t.class_name, t.other, t.duration, t.data_sources, t.is_deleted, t.is_valid, t.created_at, t.updated_at,
        t.follower_name
        FROM tt_sale_complaint_not_close_case_reason t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.complaintInfoId !=null and params.complaintInfoId != '' ">
            AND t.complaint_info_id = #{params.complaintInfoId}
        </if>
        <if test=" params.object !=null and params.object != '' ">
            AND t.object = #{params.object}
        </if>
        <if test=" params.followTime !=null and params.followTime != '' ">
            AND t.follow_time = #{params.followTime}
        </if>
        <if test=" params.follower !=null and params.follower != '' ">
            AND t.follower = #{params.follower}
        </if>
        <if test=" params.bigClass !=null and params.bigClass != '' ">
            AND t.big_class = #{params.bigClass}
        </if>
        <if test=" params.bigClassName !=null and params.bigClassName != '' ">
            AND t.big_class_name = #{params.bigClassName}
        </if>
        <if test=" params.smallClass !=null and params.smallClass != '' ">
            AND t.small_class = #{params.smallClass}
        </if>
        <if test=" params.smallClassName !=null and params.smallClassName != '' ">
            AND t.small_class_name = #{params.smallClassName}
        </if>
        <if test=" params.other !=null and params.other != '' ">
            AND t.other = #{params.other}
        </if>
        <if test=" params.duration !=null and params.duration != '' ">
            AND t.duration = #{params.duration}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        and t.is_deleted=0
    </select>
    <select id="selectfollowTime" resultMap="BaseResultMap" parameterType="long">
        SELECT
        id,follow_time
        FROM tt_sale_complaint_not_close_case_reason
        WHERE 1=1 and is_deleted=0 and complaint_info_id =#{id} ORDER BY follow_time desc

    </select>



</mapper>
