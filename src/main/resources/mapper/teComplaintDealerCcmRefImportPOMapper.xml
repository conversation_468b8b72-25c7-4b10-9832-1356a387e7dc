<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.complaint.TeComplaintDealerCcmRefImportMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.complaint.TeComplaintDealerCcmRefImportPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                        <result column="app_id" property="appId"/>
                                                        <result column="owner_code" property="ownerCode"/>
                                                        <result column="owner_par_code" property="ownerParCode"/>
                                                        <result column="org_id" property="orgId"/>
                                                                                    <result column="dealer_code" property="dealerCode"/>
                                                        <result column="dealer_name" property="dealerName"/>
                                                        <result column="ccm_man" property="ccmMan"/>
                                                        <result column="is_error" property="isError"/>
                                                        <result column="error_msg" property="errorMsg"/>
                                                        <result column="line_number" property="lineNumber"/>
                                                        <result column="data_sources" property="dataSources"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="is_valid" property="isValid"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, dealer_code, dealer_name, ccm_man, is_error, error_msg, line_number, data_sources, is_deleted, is_valid, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.TeComplaintDealerCcmRefImportPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM te_complaint_dealer_ccm_ref_import t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code = #{params.dealerCode}
            </if>
                    <if test=" params.dealerName !=null and params.dealerName != '' ">
                AND t.dealer_name = #{params.dealerName}
            </if>
                    <if test=" params.ccmMan !=null and params.ccmMan != '' ">
                AND t.ccm_man = #{params.ccmMan}
            </if>
                    <if test=" params.isError !=null and params.isError != '' ">
                AND t.is_error = #{params.isError}
            </if>
                    <if test=" params.errorMsg !=null and params.errorMsg != '' ">
                AND t.error_msg = #{params.errorMsg}
            </if>
                    <if test=" params.lineNumber !=null and params.lineNumber != '' ">
                AND t.line_number = #{params.lineNumber}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.TeComplaintDealerCcmRefImportPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM te_complaint_dealer_ccm_ref_import t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code = #{params.dealerCode}
            </if>
                    <if test=" params.dealerName !=null and params.dealerName != '' ">
                AND t.dealer_name = #{params.dealerName}
            </if>
                    <if test=" params.ccmMan !=null and params.ccmMan != '' ">
                AND t.ccm_man = #{params.ccmMan}
            </if>
                    <if test=" params.isError !=null and params.isError != '' ">
                AND t.is_error = #{params.isError}
            </if>
                    <if test=" params.errorMsg !=null and params.errorMsg != '' ">
                AND t.error_msg = #{params.errorMsg}
            </if>
                    <if test=" params.lineNumber !=null and params.lineNumber != '' ">
                AND t.line_number = #{params.lineNumber}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>
    <!-- 查询显示成功导入的数据 -->
    <select id="querySuccess" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM te_complaint_dealer_ccm_ref_import where is_error=0 and
        created_by=#{params.userId}
    </select>
    <!-- 查询显示成功导入的数据条数 -->
    <select id="querySucessCount" parameterType="java.lang.Long"
            resultType="java.lang.Integer"> SELECT count(1) FROM te_complaint_dealer_ccm_ref_import t
        where t.is_error=0 and t.created_by=#{params.userId}
    </select>
    <select id="selectAllCodes" resultMap="BaseResultMap"
            parameterType="java.lang.Long">
        SELECT
        app_id, owner_code, owner_par_code, org_id, id, dealer_code, dealer_name, ccm_man, is_error,
        error_msg, line_number, data_sources, is_deleted, is_valid, created_at, updated_at
        FROM
        te_complaint_dealer_ccm_ref_import
        where created_by=#{params.userId}
    </select>
    <update id="updateCheckDealerCodeExist">
        UPDATE te_complaint_dealer_ccm_ref_import SET
        is_error = 1, ERROR_MSG= IFNULL(ERROR_MSG,'经销商代码错误或不存在')
        where created_by=#{userId}
        AND dealer_code in
        <foreach collection="errorList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <!-- 查询显示错误数据 -->
    <select id="queryError" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM te_complaint_dealer_ccm_ref_import t where t.is_error=1 and
        t.created_by=#{params.userId}
    </select>

    <!-- 删除当前用户在临时表存的数据 -->
    <delete id="deleteAll" parameterType="java.lang.Long"> delete from
        te_complaint_dealer_ccm_ref_import where created_by=#{params.userId}
    </delete>

</mapper>
