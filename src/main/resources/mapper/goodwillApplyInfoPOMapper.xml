<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyInfoMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO">
                   <id column="id" property="id"/>
                   <result column="APP_ID" property="appId"/>
                   <result column="OWNER_CODE" property="ownerCode"/>
                   <result column="OWNER_PAR_CODE" property="ownerParCode"/>
                   <result column="ORG_ID" property="orgId"/>
                   <result column="apply_no" property="applyNo"/>
                   <result column="dealer_code" property="dealerCode"/>
                   <result column="dealer_name" property="dealerName"/>
                   <result column="small_area_id" property="smallAreaId" />
                   <result column="small_area" property="smallArea" />
                   <result column="area_manage_id" property="areaManageId"/>
                   <result column="area_manage" property="areaManage"/>
                   <result column="bloc_id" property="blocId"/>
                   <result column="bloc" property="bloc"/>
                   <result column="audit_type" property="auditType"/>
                   <result column="goodwill_nature" property="goodwillNature"/>
                   <result column="apply_time" property="applyTime"/>
                   <result column="complaint_falut" property="complaintFalut"/>
                   <result column="complaint_id" property="complaintId"/>
                   <result column="vehicle_use" property="vehicleUse"/>
                   <result column="sales_dealer" property="salesDealer"/>
                   <result column="apply_amount" property="applyAmount"/>
                   <result column="audit_amount" property="auditAmount"/>
                   <result column="settlement_amount" property="settlementAmount"/>
                   <result column="invoice_amount" property="invoiceAmount"/>
                   <result column="goodwill_status" property="goodwillStatus"/>
                   <result column="customer_pain" property="customerPain"/>
                   <result column="vin" property="vin"/>
                   <result column="license" property="license"/>
                   <result column="customer_name" property="customerName"/>
                   <result column="customer_mobile" property="customerMobile"/>
                   <result column="mileage" property="mileage"/>
                   <result column="model" property="model"/>
                   <result column="buy_car_date" property="buyCarDate"/>
                   <result column="warranty_start_date" property="warrantyStartDate"/>
                   <result column="is_extend_warranty" property="isExtendWarranty"/>
                   <result column="extend_warranty_name" property="extendWarrantyName"/>
                   <result column="extend_warranty_start_date" property="extendWarrantyStartDate"/>
                   <result column="extend_warranty_end_date" property="extendWarrantyEndDate"/>
                   <result column="maintain_cost" property="maintainCost"/>
                   <result column="extend_warranty_cost" property="extendWarrantyCost"/>
                   <result column="accessory_cost" property="accessoryCost"/>
                   <result column="voucher_cost" property="voucherCost"/>
                   <result column="walking_car_price" property="walkingCarPrice"/>
                   <result column="volvo_integral" property="volvoIntegral"/>
                   <result column="return_change_car_price" property="returnChangeCarPrice"/>
                   <result column="other_price" property="otherPrice"/>
                   <result column="cost_total" property="costTotal"/>
                   <result column="customer_pay" property="customerPay"/>
                   <result column="dealer_undertake" property="dealerUndertake"/>
                   <result column="volvo_support_goodwill_amount" property="volvoSupportGoodwillAmount"/>
                   <result column="remark" property="remark"/>
                   <result column="apply_file" property="applyFile"/>
                   <result column="cost_statistics_file" property="costStatisticsFile"/>
                   <result column="cost_screenshot_file" property="costScreenshotFile"/>
                   <result column="ring_check_file" property="ringCheckFile"/>
                   <result column="trouble_repair_requisition_file" property="troubleRepairRequisitionFile"/>
                   <result column="work_order_file" property="workOrderFile"/>
                   <result column="situation_settlement_agreement_file" property="situationSettlementAgreementFile"/>
                   <result column="supplementary_material_file" property="supplementaryMaterialFile"/>
                   <result column="management_review_email_vp_file" property="managementReviewEmailVpFile"/>
                   <result column="management_review_email_ceo_file" property="managementReviewEmailCeoFile"/>
                   <result column="cost_update_file" property="costUpdateFile"/>
                   <result column="vcdc_else_file" property="vcdcElseFile"/>
                   <result column="customer_identification" property="customerIdentification"/>
                   <result column="else_file" property="elseFile"/>
                   <result column="commit_time" property="commitTime"/>
                   <result column="is_valid" property="isValid"/>
                   <result column="is_deleted" property="isDeleted"/>
                   <result column="created_at" property="createdAt"/>
                   <result column="updated_at" property="updatedAt"/>
               	   <result column="created_by" property="createdBy"/>
                   <result column="updated_by" property="updatedBy"/>
                   <result column="record_version" property="recordVersion"/>
                   <result column="pass_time" property="passTime"/>
          </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, apply_no, dealer_code,dealer_name,area_manage_id,area_manage,bloc,bloc_id, audit_type, goodwill_nature, apply_time, pass_time,complaint_falut, complaint_id, vehicle_use, sales_dealer, apply_amount, audit_amount, settlement_amount, invoice_amount, goodwill_status, customer_pain, vin, license, customer_name, customer_mobile, mileage, model, buy_car_date, warranty_start_date, is_extend_warranty, extend_warranty_name, extend_warranty_start_date, extend_warranty_end_date, maintain_cost, extend_warranty_cost, accessory_cost, voucher_cost, walking_car_price, volvo_integral, return_change_car_price, other_price, cost_total, customer_pay, dealer_undertake, volvo_support_goodwill_amount, remark, apply_file, cost_statistics_file, cost_screenshot_file, ring_check_file, trouble_repair_requisition_file, work_order_file, situation_settlement_agreement_file, supplementary_material_file, management_review_email_vp_file, management_review_email_ceo_file, cost_update_file, vcdc_else_file, customer_identification, else_file, commit_time, is_valid, is_deleted, created_at, updated_at
        </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.applyNo !=null and params.applyNo != '' ">
                AND t.apply_no = #{params.applyNo}
            </if>
                    <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code = #{params.dealerCode}
            </if>
                    <if test=" params.auditType !=null ">
                AND t.audit_type = #{params.auditType}
            </if>
                    <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
                    <if test=" params.applyTime !=null and params.applyTime != '' ">
                AND t.apply_time = #{params.applyTime}
            </if>
                    <if test=" params.complaintFalut !=null and params.complaintFalut != '' ">
                AND t.complaint_falut = #{params.complaintFalut}
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id = #{params.complaintId}
            </if>
                    <if test=" params.vehicleUse !=null and params.vehicleUse != '' ">
                AND t.vehicle_use = #{params.vehicleUse}
            </if>
                    <if test=" params.salesDealer !=null and params.salesDealer != '' ">
                AND t.sales_dealer = #{params.salesDealer}
            </if>
                    <if test=" params.applyAmount !=null and params.applyAmount != '' ">
                AND t.apply_amount = #{params.applyAmount}
            </if>
                    <if test=" params.auditAmount !=null and params.auditAmount != '' ">
                AND t.audit_amount = #{params.auditAmount}
            </if>
                    <if test=" params.settlementAmount !=null and params.settlementAmount != '' ">
                AND t.settlement_amount = #{params.settlementAmount}
            </if>
                    <if test=" params.invoiceAmount !=null and params.invoiceAmount != '' ">
                AND t.invoice_amount = #{params.invoiceAmount}
            </if>
                    <if test=" params.goodwillStatus !=null and params.goodwillStatus != '' ">
                AND t.goodwill_status = #{params.goodwillStatus}
            </if>
                    <if test=" params.customerPain !=null and params.customerPain != '' ">
                AND t.customer_pain = #{params.customerPain}
            </if>
                    <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin = #{params.vin}
            </if>
                    <if test=" params.license !=null and params.license != '' ">
                AND t.license = #{params.license}
            </if>
                    <if test=" params.customerName !=null and params.customerName != '' ">
                AND t.customer_name = #{params.customerName}
            </if>
                    <if test=" params.customerMobile !=null and params.customerMobile != '' ">
                AND t.customer_mobile = #{params.customerMobile}
            </if>
                    <if test=" params.mileage !=null and params.mileage != '' ">
                AND t.mileage = #{params.mileage}
            </if>
                    <if test=" params.model !=null and params.model != '' ">
                AND t.model = #{params.model}
            </if>
                    <if test=" params.buyCarDate !=null and params.buyCarDate != '' ">
                AND t.buy_car_date = #{params.buyCarDate}
            </if>
                    <if test=" params.warrantyStartDate !=null and params.warrantyStartDate != '' ">
                AND t.warranty_start_date = #{params.warrantyStartDate}
            </if>
                    <if test=" params.isExtendWarranty !=null and params.isExtendWarranty != '' ">
                AND t.is_extend_warranty = #{params.isExtendWarranty}
            </if>
                    <if test=" params.extendWarrantyName !=null and params.extendWarrantyName != '' ">
                AND t.extend_warranty_name = #{params.extendWarrantyName}
            </if>
                    <if test=" params.extendWarrantyStartDate !=null and params.extendWarrantyStartDate != '' ">
                AND t.extend_warranty_start_date = #{params.extendWarrantyStartDate}
            </if>
                    <if test=" params.extendWarrantyEndDate !=null and params.extendWarrantyEndDate != '' ">
                AND t.extend_warranty_end_date = #{params.extendWarrantyEndDate}
            </if>
                    <if test=" params.maintainCost !=null and params.maintainCost != '' ">
                AND t.maintain_cost = #{params.maintainCost}
            </if>
                    <if test=" params.extendWarrantyCost !=null and params.extendWarrantyCost != '' ">
                AND t.extend_warranty_cost = #{params.extendWarrantyCost}
            </if>
                    <if test=" params.accessoryCost !=null and params.accessoryCost != '' ">
                AND t.accessory_cost = #{params.accessoryCost}
            </if>
                    <if test=" params.voucherCost !=null and params.voucherCost != '' ">
                AND t.voucher_cost = #{params.voucherCost}
            </if>
                    <if test=" params.walkingCarPrice !=null and params.walkingCarPrice != '' ">
                AND t.walking_car_price = #{params.walkingCarPrice}
            </if>
                    <if test=" params.volvoIntegral !=null and params.volvoIntegral != '' ">
                AND t.volvo_integral = #{params.volvoIntegral}
            </if>
                    <if test=" params.returnChangeCarPrice !=null and params.returnChangeCarPrice != '' ">
                AND t.return_change_car_price = #{params.returnChangeCarPrice}
            </if>
                    <if test=" params.otherPrice !=null and params.otherPrice != '' ">
                AND t.other_price = #{params.otherPrice}
            </if>
                    <if test=" params.costTotal !=null and params.costTotal != '' ">
                AND t.cost_total = #{params.costTotal}
            </if>
                    <if test=" params.customerPay !=null and params.customerPay != '' ">
                AND t.customer_pay = #{params.customerPay}
            </if>
                    <if test=" params.dealerUndertake !=null and params.dealerUndertake != '' ">
                AND t.dealer_undertake = #{params.dealerUndertake}
            </if>
                    <if test=" params.volvoSupportGoodwillAmount !=null and params.volvoSupportGoodwillAmount != '' ">
                AND t.volvo_support_goodwill_amount = #{params.volvoSupportGoodwillAmount}
            </if>
                    <if test=" params.remark !=null and params.remark != '' ">
                AND t.remark = #{params.remark}
            </if>
                    <if test=" params.applyFile !=null and params.applyFile != '' ">
                AND t.apply_file = #{params.applyFile}
            </if>
                    <if test=" params.costStatisticsFile !=null and params.costStatisticsFile != '' ">
                AND t.cost_statistics_file = #{params.costStatisticsFile}
            </if>
                    <if test=" params.costScreenshotFile !=null and params.costScreenshotFile != '' ">
                AND t.cost_screenshot_file = #{params.costScreenshotFile}
            </if>
                    <if test=" params.ringCheckFile !=null and params.ringCheckFile != '' ">
                AND t.ring_check_file = #{params.ringCheckFile}
            </if>
                    <if test=" params.troubleRepairRequisitionFile !=null and params.troubleRepairRequisitionFile != '' ">
                AND t.trouble_repair_requisition_file = #{params.troubleRepairRequisitionFile}
            </if>
                    <if test=" params.workOrderFile !=null and params.workOrderFile != '' ">
                AND t.work_order_file = #{params.workOrderFile}
            </if>
                    <if test=" params.situationSettlementAgreementFile !=null and params.situationSettlementAgreementFile != '' ">
                AND t.situation_settlement_agreement_file = #{params.situationSettlementAgreementFile}
            </if>
                    <if test=" params.supplementaryMaterialFile !=null and params.supplementaryMaterialFile != '' ">
                AND t.supplementary_material_file = #{params.supplementaryMaterialFile}
            </if>
                    <if test=" params.managementReviewEmailVpFile !=null and params.managementReviewEmailVpFile != '' ">
                AND t.management_review_email_vp_file = #{params.managementReviewEmailVpFile}
            </if>
                    <if test=" params.managementReviewEmailCeoFile !=null and params.managementReviewEmailCeoFile != '' ">
                AND t.management_review_email_ceo_file = #{params.managementReviewEmailCeoFile}
            </if>
                    <if test=" params.costUpdateFile !=null and params.costUpdateFile != '' ">
                AND t.cost_update_file = #{params.costUpdateFile}
            </if>
                    <if test=" params.vcdcElseFile !=null and params.vcdcElseFile != '' ">
                AND t.vcdc_else_file = #{params.vcdcElseFile}
            </if>
                    <if test=" params.customerIdentification !=null and params.customerIdentification != '' ">
                AND t.customer_identification = #{params.customerIdentification}
            </if>
                    <if test=" params.elseFile !=null and params.elseFile != '' ">
                AND t.else_file = #{params.elseFile}
            </if>
                    <if test=" params.commitTime !=null and params.commitTime != '' ">
                AND t.commit_time = #{params.commitTime}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.applyNo !=null and params.applyNo != '' ">
                AND t.apply_no = #{params.applyNo}
            </if>
                    <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code = #{params.dealerCode}
            </if>
                    <if test=" params.auditType !=null ">
                AND t.audit_type = #{params.auditType}
            </if>
                    <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
                    <if test=" params.applyTime !=null and params.applyTime != '' ">
                AND t.apply_time = #{params.applyTime}
            </if>
                    <if test=" params.complaintFalut !=null and params.complaintFalut != '' ">
                AND t.complaint_falut = #{params.complaintFalut}
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id = #{params.complaintId}
            </if>
                    <if test=" params.vehicleUse !=null and params.vehicleUse != '' ">
                AND t.vehicle_use = #{params.vehicleUse}
            </if>
                    <if test=" params.salesDealer !=null and params.salesDealer != '' ">
                AND t.sales_dealer = #{params.salesDealer}
            </if>
                    <if test=" params.applyAmount !=null and params.applyAmount != '' ">
                AND t.apply_amount = #{params.applyAmount}
            </if>
                    <if test=" params.auditAmount !=null and params.auditAmount != '' ">
                AND t.audit_amount = #{params.auditAmount}
            </if>
                    <if test=" params.settlementAmount !=null and params.settlementAmount != '' ">
                AND t.settlement_amount = #{params.settlementAmount}
            </if>
                    <if test=" params.invoiceAmount !=null and params.invoiceAmount != '' ">
                AND t.invoice_amount = #{params.invoiceAmount}
            </if>
                    <if test=" params.goodwillStatus !=null and params.goodwillStatus != '' ">
                AND t.goodwill_status = #{params.goodwillStatus}
            </if>
                    <if test=" params.customerPain !=null and params.customerPain != '' ">
                AND t.customer_pain = #{params.customerPain}
            </if>
                    <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin = #{params.vin}
            </if>
                    <if test=" params.license !=null and params.license != '' ">
                AND t.license = #{params.license}
            </if>
                    <if test=" params.customerName !=null and params.customerName != '' ">
                AND t.customer_name = #{params.customerName}
            </if>
                    <if test=" params.customerMobile !=null and params.customerMobile != '' ">
                AND t.customer_mobile = #{params.customerMobile}
            </if>
                    <if test=" params.mileage !=null and params.mileage != '' ">
                AND t.mileage = #{params.mileage}
            </if>
                    <if test=" params.model !=null and params.model != '' ">
                AND t.model = #{params.model}
            </if>
                    <if test=" params.buyCarDate !=null and params.buyCarDate != '' ">
                AND t.buy_car_date = #{params.buyCarDate}
            </if>
                    <if test=" params.warrantyStartDate !=null and params.warrantyStartDate != '' ">
                AND t.warranty_start_date = #{params.warrantyStartDate}
            </if>
                    <if test=" params.isExtendWarranty !=null and params.isExtendWarranty != '' ">
                AND t.is_extend_warranty = #{params.isExtendWarranty}
            </if>
                    <if test=" params.extendWarrantyName !=null and params.extendWarrantyName != '' ">
                AND t.extend_warranty_name = #{params.extendWarrantyName}
            </if>
                    <if test=" params.extendWarrantyStartDate !=null and params.extendWarrantyStartDate != '' ">
                AND t.extend_warranty_start_date = #{params.extendWarrantyStartDate}
            </if>
                    <if test=" params.extendWarrantyEndDate !=null and params.extendWarrantyEndDate != '' ">
                AND t.extend_warranty_end_date = #{params.extendWarrantyEndDate}
            </if>
                    <if test=" params.maintainCost !=null and params.maintainCost != '' ">
                AND t.maintain_cost = #{params.maintainCost}
            </if>
                    <if test=" params.extendWarrantyCost !=null and params.extendWarrantyCost != '' ">
                AND t.extend_warranty_cost = #{params.extendWarrantyCost}
            </if>
                    <if test=" params.accessoryCost !=null and params.accessoryCost != '' ">
                AND t.accessory_cost = #{params.accessoryCost}
            </if>
                    <if test=" params.voucherCost !=null and params.voucherCost != '' ">
                AND t.voucher_cost = #{params.voucherCost}
            </if>
                    <if test=" params.walkingCarPrice !=null and params.walkingCarPrice != '' ">
                AND t.walking_car_price = #{params.walkingCarPrice}
            </if>
                    <if test=" params.volvoIntegral !=null and params.volvoIntegral != '' ">
                AND t.volvo_integral = #{params.volvoIntegral}
            </if>
                    <if test=" params.returnChangeCarPrice !=null and params.returnChangeCarPrice != '' ">
                AND t.return_change_car_price = #{params.returnChangeCarPrice}
            </if>
                    <if test=" params.otherPrice !=null and params.otherPrice != '' ">
                AND t.other_price = #{params.otherPrice}
            </if>
                    <if test=" params.costTotal !=null and params.costTotal != '' ">
                AND t.cost_total = #{params.costTotal}
            </if>
                    <if test=" params.customerPay !=null and params.customerPay != '' ">
                AND t.customer_pay = #{params.customerPay}
            </if>
                    <if test=" params.dealerUndertake !=null and params.dealerUndertake != '' ">
                AND t.dealer_undertake = #{params.dealerUndertake}
            </if>
                    <if test=" params.volvoSupportGoodwillAmount !=null and params.volvoSupportGoodwillAmount != '' ">
                AND t.volvo_support_goodwill_amount = #{params.volvoSupportGoodwillAmount}
            </if>
                    <if test=" params.remark !=null and params.remark != '' ">
                AND t.remark = #{params.remark}
            </if>
                    <if test=" params.applyFile !=null and params.applyFile != '' ">
                AND t.apply_file = #{params.applyFile}
            </if>
                    <if test=" params.costStatisticsFile !=null and params.costStatisticsFile != '' ">
                AND t.cost_statistics_file = #{params.costStatisticsFile}
            </if>
                    <if test=" params.costScreenshotFile !=null and params.costScreenshotFile != '' ">
                AND t.cost_screenshot_file = #{params.costScreenshotFile}
            </if>
                    <if test=" params.ringCheckFile !=null and params.ringCheckFile != '' ">
                AND t.ring_check_file = #{params.ringCheckFile}
            </if>
                    <if test=" params.troubleRepairRequisitionFile !=null and params.troubleRepairRequisitionFile != '' ">
                AND t.trouble_repair_requisition_file = #{params.troubleRepairRequisitionFile}
            </if>
                    <if test=" params.workOrderFile !=null and params.workOrderFile != '' ">
                AND t.work_order_file = #{params.workOrderFile}
            </if>
                    <if test=" params.situationSettlementAgreementFile !=null and params.situationSettlementAgreementFile != '' ">
                AND t.situation_settlement_agreement_file = #{params.situationSettlementAgreementFile}
            </if>
                    <if test=" params.supplementaryMaterialFile !=null and params.supplementaryMaterialFile != '' ">
                AND t.supplementary_material_file = #{params.supplementaryMaterialFile}
            </if>
                    <if test=" params.managementReviewEmailVpFile !=null and params.managementReviewEmailVpFile != '' ">
                AND t.management_review_email_vp_file = #{params.managementReviewEmailVpFile}
            </if>
                    <if test=" params.managementReviewEmailCeoFile !=null and params.managementReviewEmailCeoFile != '' ">
                AND t.management_review_email_ceo_file = #{params.managementReviewEmailCeoFile}
            </if>
                    <if test=" params.costUpdateFile !=null and params.costUpdateFile != '' ">
                AND t.cost_update_file = #{params.costUpdateFile}
            </if>
                    <if test=" params.vcdcElseFile !=null and params.vcdcElseFile != '' ">
                AND t.vcdc_else_file = #{params.vcdcElseFile}
            </if>
                    <if test=" params.customerIdentification !=null and params.customerIdentification != '' ">
                AND t.customer_identification = #{params.customerIdentification}
            </if>
                    <if test=" params.elseFile !=null and params.elseFile != '' ">
                AND t.else_file = #{params.elseFile}
            </if>
                    <if test=" params.commitTime !=null and params.commitTime != '' ">
                AND t.commit_time = #{params.commitTime}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>
      <select id="queryPartDetailInfo" resultType="java.util.HashMap" parameterType="java.util.Map">
        	SELECT t.parts_id AS id, t.parts_no AS partCode,t.parts_name AS partName,
            IF(A.purchase_price IS NOT NULL,A.purchase_price,t.cls12) AS  unitPriceNoTax,
        	#{params.rate} rates,
        	'1' as quantity,
            t.cls12,
            A.purchase_price,
            IF(A.purchase_price IS NOT NULL,'10041001','10041002') AS isSpecialPrice,
            A.activity_id,
            A.activity_no
           FROM tm_parts_info t
          LEFT JOIN (
                  SELECT
                  activity_id,
                  activity_no,
                  part_no,
                  part_name,
                  purchase_price,
                  sale_price,
                  guarantee_price
                  FROM
                    (
                      SELECT
                      p.activity_id,
                      s.activity_no,
                      p.part_no,
                      p.part_name,
                      p.purchase_price,
                      p.sale_price,
                      p.guarantee_price,
                      row_number () over (
                      PARTITION BY p.part_no
                      ORDER BY
                      p.created_at DESC
                      ) rowNum
                      FROM
                      tt_parts_price_detail p
                      INNER JOIN tt_special_parts_price_setting s ON p.activity_id = s.id
                      where
                      s.activity_status = 10011001
                      AND s.submit_status = 10651002
                      AND s.binding_type = 81571001
                      and s.begin_date &lt;= #{params.nowDate}
                      and s.end_date >= #{params.nowDate}
                  ) fq
                  WHERE
                  fq.rowNum = 1
            ) A on t.parts_no = A.part_no
           WHERE 1=1
           <if test=" params.partNo !=null and params.partNo != '' ">
                AND t.parts_no like  CONCAT('%',#{params.partNo},'%')
            </if>
            <if test=" params.partName !=null and params.partName != '' ">
                AND t.parts_name like  CONCAT('%',#{params.partName},'%')
            </if>
      </select>
      <select id="queryPartDetailCountInfo" resultType="integer" parameterType="java.util.Map">

        	SELECT count(1)
           FROM tm_parts_info t
          LEFT JOIN (
          SELECT
          activity_id,
          activity_no,
          part_no,
          part_name,
          purchase_price,
          sale_price,
          guarantee_price
          FROM
          (
          SELECT
          p.activity_id,
          s.activity_no,
          p.part_no,
          p.part_name,
          p.purchase_price,
          p.sale_price,
          p.guarantee_price,
          row_number () over (
          PARTITION BY p.part_no
          ORDER BY
          p.created_at DESC
          ) rowNum
          FROM
          tt_parts_price_detail p
          INNER JOIN tt_special_parts_price_setting s ON p.activity_id = s.id
          where
          s.activity_status = 10011001
          AND s.submit_status = 10651002
          AND s.binding_type = 81571001
          and s.begin_date &lt;= #{params.nowDate}
          and s.end_date >= #{params.nowDate}
          ) fq
          WHERE
          fq.rowNum = 1
          ) A on t.parts_no = A.part_no
           WHERE 1=1
           <if test=" params.partNo !=null and params.partNo != '' ">
                AND t.parts_no like  CONCAT('%',#{params.partNo},'%')
            </if>
            <if test=" params.partName !=null and params.partName != '' ">
                AND t.parts_name like  CONCAT('%',#{params.partName},'%')
            </if>
      </select>

      <select id="getByApplyId" resultType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO" parameterType="long">
      	SELECT    tgai.APP_ID appId, tgai.OWNER_CODE ownerCode, tgai.OWNER_PAR_CODE ownerParCode,tgai.updated_person updatedPerson,
      	    tgai.ORG_ID orgId, tgai.id, tgai.apply_no applyNo, tgai.dealer_code dealerCode, tgai.audit_type auditType,
      	    tgai.dealer_name dealerName,tgai.area_manage_id areaManageId,tgai.area_manage areaManage,tgai.bloc_id blocId,tgai.bloc bloc,
      	    tgai.goodwill_nature goodwillNature, tgai.apply_time applyTime,
      	 	tgai.complaint_falut complaintFalut, tgai.complaint_id complaintId, DATE_FORMAT(tgai.complaint_date, '%Y-%m-%d') complaintDate,
      	 	tgai.vehicle_use vehicleUse, tgai.sales_dealer salesDealer,
   	  	 	tgai.apply_amount applyAmount, tgai.audit_amount auditAmount,tgai.audit_amount auditPrice, tgai.settlement_amount settlementAmount,tgai.invoice_amount invoiceAmount,
      	   	tgai.goodwill_status goodwillStatus, tgai.customer_pain customerPain, tgai.vin,
      	   	tgai.license license, tgai.customer_name customerName,
      	    tgai.customer_mobile customerMobile, tgai.mileage mileage, tgai.model model, tgai.buy_car_date buyCarDate,
   	     	tgai.warranty_start_date warrantyStartDate, tgai.is_extend_warranty isExtendWarranty, tgai.extend_warranty_name extendWarrantyName,
      	    tgai.extend_warranty_start_date extendWarrantyStartDate, tgai.extend_warranty_end_date extendWarrantyEndDate, tgai.maintain_cost maintainCost,
      	    tgai.extend_warranty_cost extendWarrantyCost, tgai.accessory_cost accessoryCost,
      	    case when tgai.voucher_cost=0 then null else tgai.voucher_cost end voucherCost,
      	    case when tgai.walking_car_price=0 then null else tgai.walking_car_price end  walkingCarPrice,
      	    case when tgai.volvo_integral=0 then null else tgai.volvo_integral end volvoIntegral,
      	    case when tgai.return_change_car_price=0 then null else tgai.return_change_car_price end  returnChangeCarPrice,
      	    case when tgai.other_price=0 then null else tgai.other_price end otherPrice, tgai.cost_total costTotal,
      	    tgai.customer_pay customerPay, tgai.dealer_undertake dealerUndertake, tgai.volvo_support_goodwill_amount volvoSupportGoodwillAmount,
      	    tgai.remark remark, tgai.apply_file applyFile, tgai.cost_statistics_file costStatisticsFile, tgai.cost_screenshot_file costScreenshotFile,
      	    tgai.ring_check_file ringCheckFile, tgai.trouble_repair_requisition_file troubleRepairRequisitionFile, tgai.work_order_file workOrderFile,
      	    tgai.situation_settlement_agreement_file situationSettlementAgreementFile, tgai.supplementary_material_file supplementaryMaterialFile,
      	    tgai.management_review_email_vp_file managementReviewEmailVpFile, tgai.management_review_email_ceo_file managementReviewEmailCeoFile,
      	    tgai.cost_update_file costUpdateFile, tgai.vcdc_else_file vcdcElseFile, tgai.customer_identification customerIdentification,
          	tgai.else_file elseFile, tgai.commit_time commitTime, tgai.is_valid isValid, tgai.is_deleted isDeleted,
      	    ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(tgai.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(IFNULL(tgai.material_pass_time,NOW()), '%Y-%m-%d %H:%i:%S'))/24,1) materialAuditLength,
      	    IFNULL(ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(tgai.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(tgai.material_commit_time, '%Y-%m-%d %H:%i:%S'))/24,1),'') materialCommitLength,
      	    tgma.customer_background customerBackground, tgma.customer_background_en customerBackgroundEn,
      	    tgma.reason_and_dispose reasonAndDispose,tgma.reason_and_dispose_en reasonAndDisposeEn,
      	    tgma.repair_solution repairSolution, tgma.repair_solution_en repairSolutionEn,
      	    tgma.customer_require customerRequire, tgma.customer_require_en customerRequireEn,
      	    tgma.potential_risk potentialRisk, tgma.potential_risk_en potentialRiskEn,
      	    tgma.vr_or_tj_no_en vrOrTjNoEn, tgma.vr_or_tj_no vrOrTjNo,tgma.id materialId,
      	    tgma.business_goodwill_apply_detail_en businessGoodwillApplyDetailEn, tgma.business_goodwill_apply_detail businessGoodwillApplyDetail,
      	    (select count(1) from tt_goodwil_apply_audit_process where goodwill_apply_id = #{id} and audit_object=0 and audit_status=82801001) applyCount,
      	    (select count(1) from tt_goodwil_apply_audit_process where goodwill_apply_id = #{id} and audit_object=1 and audit_status=82801001) materialCount,
      	    (select count(1) from tt_goodwill_notice_invoice_info where goodwill_apply_id = #{id} ) noticeTimes,
      	    (select audit_role auditRole from tt_goodwil_apply_audit_process where goodwill_apply_id = #{id} and audit_object=1 and audit_status is null order by audit_position limit 1) auditRole,
      	    (select count(1)  from  tt_goodwill_apply_info aa left join (
			select  *  from  tt_goodwil_apply_audit_process where goodwill_apply_id=#{id} AND audit_object=1 and audit_status is null order by audit_position limit 1 ) bb on aa.id=bb.goodwill_apply_id
			where aa.id=#{id} and aa.is_need_translate=10041001  and bb.audit_role like 'CFO') auditCcmq,
			tgaai.ID auditId,tgaai.audit_result auditResult,tgaai.notice_dealer_time noticeDealerTime
      	    ,tg.id noticeInvoiceId
	      FROM  tt_goodwill_apply_info tgai
	      LEFT JOIN tt_goodwill_material_audit tgma on tgai.ID=tgma.goodwill_apply_id
	      LEFT JOIN tt_goodwill_apply_audit_info tgaai on tgai.ID=tgaai.goodwill_apply_id
	      LEFT JOIN tt_goodwill_notice_invoice_info tg on tgai.ID=tg.goodwill_apply_id and tg.invoice_object='0'
	      WHERE 1=1 AND tgai.ID=#{id}
       </select>

        <select id="queryComplaintInfo" resultType="java.util.HashMap" parameterType="java.util.Map">
        	SELECT id,complaint_id complaintId, type complaintType,source complaintSource,DATE_FORMAT(call_time,'%Y-%m-%d')  complaintDate,call_name customerName,license_plate_num license,
        	vin,problem complaintContent
           FROM tt_complaint_info t
           WHERE 1=1 and t.dealer_code=#{params.dealerCode}
            <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id like  CONCAT('%',#{params.complaintId},'%')
            </if>
           <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461003">
                and  (t.data_sources='82101002' or t.is_report='1' )
            </if>
           <if test=" params.license !=null and params.license != '' ">
                AND t.license_plate_num like  CONCAT('%',#{params.license},'%')
            </if>
            <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin like  CONCAT('%',#{params.vin},'%')
            </if>
             <if test=" params.complaintDateStart !=null  ">
                <![CDATA[   and DATE_FORMAT(t.call_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.complaintDateStart }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.complaintDateEnd !=null  ">
                <![CDATA[   and DATE_FORMAT(t.call_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.complaintDateEnd}, '%Y-%m-%d')   ]]>
            </if>
      </select>
      <select id="queryComplaintCountInfo" resultType="integer" parameterType="java.util.Map">

        	SELECT count(1)
           FROM tt_complaint_info t
            WHERE 1=1 and t.dealer_code=#{params.dealerCode}
          <if test=" params.complaintId !=null and params.complaintId != '' ">
              AND t.complaint_id like  CONCAT('%',#{params.complaintId},'%')
          </if>
           <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461003">
                and ( t.data_sources='82101002' or t.is_report='1' )
            </if>
            <if test=" params.license !=null and params.license != '' ">
                AND t.license_plate_num like  CONCAT('%',#{params.license},'%')
            </if>
            <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin like  CONCAT('%',#{params.vin},'%')
            </if>
             <if test=" params.complaintDateStart !=null  ">
                <![CDATA[   and DATE_FORMAT(t.call_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.complaintDateStart }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.complaintDateEnd !=null  ">
                <![CDATA[   and DATE_FORMAT(t.call_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.complaintDateEnd}, '%Y-%m-%d')   ]]>
            </if>
      </select>

      <select id="selectCheckedGoodWill" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO">
        SELECT
          tau.auditor,tau.audit_name auditName,
	        t.APP_ID,t.OWNER_CODE,t.OWNER_PAR_CODE,t.ORG_ID,t.id,t.apply_no,t.audit_type auditPart,
	        t.dealer_code,t.dealer_name,t.area_manage_id,t.area_manage,t.bloc,t.updated_person,
	        t.bloc_id,t.audit_type,t.goodwill_nature,t.apply_time,t.pass_time,t.material_commit_time,
	        t.complaint_falut,t.complaint_id,t.complaint_date,t.vehicle_use,t.sales_dealer,t.material_pass_time,
	        t.apply_amount,t.audit_amount,t.settlement_amount,t.invoice_amount,
	        t.goodwill_status,t.customer_pain,t.vin,t.license,t.customer_name,
	        t.customer_mobile,t.mileage,t.model,t.buy_car_date,t.warranty_start_date,
	        t.is_extend_warranty,t.extend_warranty_name,t.extend_warranty_start_date,
	        t.extend_warranty_end_date,t.maintain_cost,t.extend_warranty_cost,
	        t.accessory_cost,t.voucher_cost,t.walking_car_price,t.volvo_integral,
	        t.return_change_car_price,t.other_price,t.cost_total,t.customer_pay,
	        t.dealer_undertake,t.volvo_support_goodwill_amount,t.remark,t.apply_file,
	        t.cost_statistics_file,t.cost_screenshot_file,t.ring_check_file,
	        t.trouble_repair_requisition_file,t.work_order_file,t.situation_settlement_agreement_file,
	        t.supplementary_material_file,t.management_review_email_vp_file,
	        t.management_review_email_ceo_file,t.cost_update_file,t.vcdc_else_file,
	        t.customer_identification,t.else_file,t.commit_time,t.is_valid,t.is_deleted,
	        ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(IFNULL(t.material_pass_time,NOW()), '%Y-%m-%d %H:%i:%S'))/24,1) materialAuditLength,
      	    IFNULL(ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'))/24,1),'') materialCommitLength,
	        t.created_at,t.updated_at,tt.audit_time newAuditDate,tt.audit_result newAuditResult,tt.audit_name newestAuditor,
			ttt.audit_time unSupportDate,ttt.audit_opinion unSupportReason,
			tttt.audit_time refuseSupportDate,tttt.audit_opinion refuseSupportReason,
			ttttt.audit_time restartDate,ttttt.audit_opinion restartReason,
			case when ttttt.id is null then 10041002 else 10041001 end isRestart,tg.notice_invoice_date,
			CASE WHEN t.material_commit_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialSummit,
			CASE WHEN t.material_pass_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialAudit
	        ,tis.voucherInvoiceAmount ,tgi.areaRejectedTimes,
			case when times>0 then t.settlement_amount else '' end as noticeOrInvoicePrice,
			tis.voucherNotInvoiceAmount,tgii.ccmqRejectedTimes
			FROM tt_goodwill_apply_info t
	        left join tt_goodwill_audit_info s on t.id=s.goodwill_apply_id
			left join (select count(id) times,goodwill_apply_id from tt_goodwill_notice_invoice_info group by goodwill_apply_id) tin on t.id=tin.goodwill_apply_id
			left join (select count(id) areaRejectedTimes,goodwill_apply_id  from tt_goodwill_audit_info where audit_result=82801002 and  audit_role in ('SHQYJL','SHDQJL','SHQYZJ') and return_to is not null and return_to not in ('SHQYJL','SHDQJL','SHQYZJ','CCMQ','OEM-CWJL','VP','CFO','CEO','CCMGJJL','CCMZJ') group by goodwill_apply_id )tgi on t.id=tgi.goodwill_apply_id
	        left join (select count(id) ccmqRejectedTimes,goodwill_apply_id  from tt_goodwill_audit_info where audit_result=82801002 and  audit_role in ('CCMQ') and return_to is not null and return_to  in ('SHQYJL','SHDQJL') group by goodwill_apply_id )tgii on t.id=tgii.goodwill_apply_id
	        left join (select *  from (select max(id) max_id from tt_goodwill_audit_info  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  )tt on t.id=tt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801003  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) ttt on  t.id=ttt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801004  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) tttt on  t.id=tttt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801005  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) ttttt on  t.id=ttttt.goodwill_apply_id
	       	left join ( select t.id,t.goodwill_apply_id,t.invoice_object,t.notice_invoice_date,tt.invoice_date  from  tt_goodwill_notice_invoice_info t left join tt_goodwill_invoice_record tt on t.id=tt.notice_invoice_id) tg on t.id=tg.goodwill_apply_id and tg.invoice_object='0'
	        left join (SELECT t.goodwill_apply_id,IFNULL(ttt.invoice_price,0) voucherInvoiceAmount,
				IFNULL(t.voucher_recharge_price,0)-IFNULL(ttt.invoice_price,0)	 voucherNotInvoiceAmount
				FROM tt_goodwill_notice_invoice_info t
				LEFT JOIN ( SELECT sum( notice_invoice_price ) invoicedPrice, goodwill_apply_id FROM tt_goodwill_notice_invoice_info WHERE voucher_type = 1 GROUP BY goodwill_apply_id ) tttt ON t.goodwill_apply_id = tttt.goodwill_apply_id
				LEFT JOIN ( SELECT sum( invoice_price ) invoice_price, goodwill_apply_id FROM tt_goodwill_invoice_record WHERE CHAR_LENGTH( invoice_id )> 14 GROUP BY goodwill_apply_id ) ttt ON t.goodwill_apply_id = ttt.goodwill_apply_id
				WHERE t.voucher_status = 0 AND t.voucher_type = 0 GROUP BY t.invoice_id) tis on t.id=tis.goodwill_apply_id
                left join ( select auditor,goodwill_apply_id,audit_name  from   tt_goodwill_audit_info where audit_object =0 and is_deleted =0 and audit_role= 'SHQYJL'  ) tau on t.id=tau.goodwill_apply_id
             WHERE 1=1

             <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND (t.dealer_code  like CONCAT('%',#{params.dealerCode},'%') or t.dealer_name  like CONCAT('%',#{params.dealerCode},'%'))
            </if>
            <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461001 ">
                AND t.dealer_code = #{params.ownerCodes}
            </if>
            <if test=" params.blocId !=null and params.blocId != '' ">
	                AND t.bloc_id = #{params.blocId}
	            </if>
	            <if test=" params.areaManageId !=null and params.areaManageId != '' ">
	                AND t.area_Manage_Id = #{params.areaManageId}
	            </if>
              <if test=" params.smallAreaId !=null and params.smallAreaId != '' ">
                  AND t.small_area_id = #{params.smallAreaId}
              </if>
              <if test=" params.auditor !=null and params.auditor != '' ">
                  AND tau.auditor = #{params.auditor}
              </if>
              <if test=" params.auditName !=null and params.auditName != '' ">
                  AND tau.audit_name = #{params.auditName}
              </if>
            <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
            <if test=" params.applyPerson !=null and params.applyPerson != '' ">
                AND t.apply_person  like CONCAT('%',#{params.applyPerson},'%')
            </if>
            <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
            <if test=" params.applyNo !=null and params.applyNo != '' ">
                AND t.apply_no like CONCAT('%',#{params.applyNo},'%')
            </if>
            <if test=" params.customerName !=null and params.customerName != '' ">
                AND (t.customer_name like CONCAT('%',#{params.customerName},'%') or t.customer_mobile like CONCAT('%',#{params.customerName},'%'))
            </if>
                    <if test=" params.customerMobile !=null and params.customerMobile != '' ">
                AND t.customer_mobile = #{params.customerMobile}
            </if>
             <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin = #{params.vin}
            </if>
            <if test=" params.license !=null and params.license != '' ">
                AND (t.license like CONCAT('%',#{params.license},'%')  or t.vin like CONCAT('%',#{params.license},'%') )
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id  like CONCAT('%',#{params.complaintId},'%')
            </if>

            <if test=" params.complaintFalut1 !=null and params.complaintFalut1.length > 0 ">
                AND t.complaint_falut in
					<foreach collection="params.complaintFalut1" index="index" item="item"
					             open="(" separator="," close=")">
					        #{item}
					    </foreach>
            </if>
            <if test=" params.goodwillStatus !=null and params.goodwillStatus != '' ">
                AND t.goodwill_status = #{params.goodwillStatus}
            </if>
            <if test=" params.auditType !=null ">
                AND t.audit_type = #{params.auditType}
            </if>

            <if test=" params.applyStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.applyStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.applyEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.applyEndTime }, '%Y-%m-%d')   ]]>
            </if>
                    <if test=" params.applyStartAmount !=null and params.applyStartAmount != '' ">
                AND <![CDATA[ t.apply_amount>= ]]>  #{params.applyStartAmount}
            </if>
                    <if test=" params.applyEndAmount !=null and params.applyEndAmount != '' ">
                AND <![CDATA[ t.apply_amount<= ]]> #{params.applyEndAmount}
            </if>

            <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>

<!--             <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>-->

            <if test=" params.invoiceStartDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.invoice_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.invoiceStartDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.invoiceEndDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.invoice_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.invoiceEndDate }, '%Y-%m-%d')   ]]>
            </if>

            <if test=" params.noticeInvoiceStartDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.notice_invoice_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.noticeInvoiceStartDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.noticeInvoiceEndDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.notice_invoice_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.noticeInvoiceEndDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.updatedStartAt !=null  ">
                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')>=  DATE_FORMAT(#{params.updatedStartAt }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.updatedEndAt !=null  ">
                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')<=  DATE_FORMAT(#{params.updatedEndAt }, '%Y-%m-%d')   ]]>
            </if>

			and s.updated_by = #{params.userId}
			group by t.apply_no  order by t.apply_time desc
            </select>
		<select id="exportCheckedApplyInfo" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO">
        	SELECT
            tau.auditor,tau.audit_name auditName,
	        t.APP_ID,t.OWNER_CODE,t.OWNER_PAR_CODE,t.ORG_ID,t.id,t.apply_no,t.audit_type auditPart,
	        t.dealer_code,t.dealer_name,t.small_area_id,t.small_area,t.area_manage_id,t.area_manage,t.bloc,t.updated_person,
	        t.bloc_id,t.audit_type,t.goodwill_nature,t.apply_time,t.pass_time,t.material_commit_time,
	        t.complaint_falut,t.complaint_id,t.complaint_date,t.vehicle_use,t.sales_dealer,t.material_pass_time,
	        t.apply_amount,t.audit_amount,t.settlement_amount,t.invoice_amount,
	        t.goodwill_status,t.customer_pain,t.vin,t.license,t.customer_name,
	        t.customer_mobile,t.mileage,t.model,t.buy_car_date,t.warranty_start_date,
	        t.is_extend_warranty,t.extend_warranty_name,t.extend_warranty_start_date,
	        t.extend_warranty_end_date,t.maintain_cost,t.extend_warranty_cost,
	        t.accessory_cost,t.voucher_cost,t.walking_car_price,t.volvo_integral,
	        t.return_change_car_price,t.other_price,t.cost_total,t.customer_pay,
	        t.dealer_undertake,t.volvo_support_goodwill_amount,t.remark,t.apply_file,
	        t.cost_statistics_file,t.cost_screenshot_file,t.ring_check_file,
	        t.trouble_repair_requisition_file,t.work_order_file,t.situation_settlement_agreement_file,
	        t.supplementary_material_file,t.management_review_email_vp_file,
	        t.management_review_email_ceo_file,t.cost_update_file,t.vcdc_else_file,
	        t.customer_identification,t.else_file,t.commit_time,t.is_valid,t.is_deleted,
	        ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(IFNULL(t.material_pass_time,NOW()), '%Y-%m-%d %H:%i:%S'))/24,1) materialAuditLength,
      	    IFNULL(ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'))/24,1),'') materialCommitLength,
	        t.created_at,t.updated_at,tt.audit_time newAuditDate,tt.audit_result newAuditResult,tt.audit_name newestAuditor,
			ttt.audit_time unSupportDate,ttt.audit_opinion unSupportReason,
			tttt.audit_time refuseSupportDate,tttt.audit_opinion refuseSupportReason,
			ttttt.audit_time restartDate,ttttt.audit_opinion restartReason,
			case when ttttt.id is null then 10041002 else 10041001 end isRestart,tg.notice_invoice_date,
			CASE WHEN t.material_commit_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialSummit,
			CASE WHEN t.material_pass_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialAudit
	        ,tis.voucherInvoiceAmount ,tgi.areaRejectedTimes,
			case when times>0 then t.settlement_amount else '' end as noticeOrInvoicePrice,
			tis.voucherNotInvoiceAmount,tgii.ccmqRejectedTimes
			FROM tt_goodwill_apply_info t
	        left join tt_goodwill_audit_info s on t.id=s.goodwill_apply_id
			left join (select count(id) times,goodwill_apply_id from tt_goodwill_notice_invoice_info group by goodwill_apply_id) tin on t.id=tin.goodwill_apply_id
			left join (select count(id) areaRejectedTimes,goodwill_apply_id  from tt_goodwill_audit_info where audit_result=82801002 and  audit_role in ('SHQYJL','SHDQJL','SHQYZJ') and return_to is not null and return_to not in ('SHQYJL','SHDQJL','SHQYZJ','CCMQ','OEM-CWJL','VP','CFO','CEO','CCMGJJL','CCMZJ') group by goodwill_apply_id )tgi on t.id=tgi.goodwill_apply_id
	        left join (select count(id) ccmqRejectedTimes,goodwill_apply_id  from tt_goodwill_audit_info where audit_result=82801002 and  audit_role in ('CCMQ') and return_to is not null and return_to  in ('SHQYJL','SHDQJL') group by goodwill_apply_id )tgii on t.id=tgii.goodwill_apply_id
	        left join (select *  from (select max(id) max_id from tt_goodwill_audit_info  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  )tt on t.id=tt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801003  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) ttt on  t.id=ttt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801004  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) tttt on  t.id=tttt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801005  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) ttttt on  t.id=ttttt.goodwill_apply_id
	       	left join ( select t.id,t.goodwill_apply_id,t.invoice_object,t.notice_invoice_date,tt.invoice_date  from  tt_goodwill_notice_invoice_info t left join tt_goodwill_invoice_record tt on t.id=tt.notice_invoice_id) tg on t.id=tg.goodwill_apply_id and tg.invoice_object='0'
	        left join (SELECT t.goodwill_apply_id,IFNULL(ttt.invoice_price,0) voucherInvoiceAmount,
				IFNULL(t.voucher_recharge_price,0)-IFNULL(ttt.invoice_price,0)	 voucherNotInvoiceAmount
				FROM tt_goodwill_notice_invoice_info t
				LEFT JOIN ( SELECT sum( notice_invoice_price ) invoicedPrice, goodwill_apply_id FROM tt_goodwill_notice_invoice_info WHERE voucher_type = 1 GROUP BY goodwill_apply_id ) tttt ON t.goodwill_apply_id = tttt.goodwill_apply_id
				LEFT JOIN ( SELECT sum( invoice_price ) invoice_price, goodwill_apply_id FROM tt_goodwill_invoice_record WHERE CHAR_LENGTH( invoice_id )> 14 GROUP BY goodwill_apply_id ) ttt ON t.goodwill_apply_id = ttt.goodwill_apply_id
				WHERE t.voucher_status = 0 AND t.voucher_type = 0 GROUP BY t.invoice_id) tis on t.id=tis.goodwill_apply_id
            left join ( select auditor,goodwill_apply_id,audit_name  from   tt_goodwill_audit_info where audit_object =0 and is_deleted =0 and audit_role= 'SHQYJL'  ) tau on t.id=tau.goodwill_apply_id
             WHERE 1=1

                    <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND (t.dealer_code  like CONCAT('%',#{params.dealerCode},'%') or t.dealer_name  like CONCAT('%',#{params.dealerCode},'%'))
            </if>
            <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461001 ">
                AND t.dealer_code = #{params.ownerCodes}
            </if>
            <if test=" params.blocId !=null and params.blocId != '' ">
	                AND t.bloc_id = #{params.blocId}
	            </if>
	            <if test=" params.areaManageId !=null and params.areaManageId != '' ">
	                AND t.area_Manage_Id = #{params.areaManageId}
	            </if>
            <if test=" params.smallAreaId !=null and params.smallAreaId != '' ">
                AND t.small_area_id = #{params.smallAreaId}
            </if>
            <if test=" params.auditor !=null and params.auditor != '' ">
                AND tau.auditor = #{params.auditor}
            </if>
            <if test=" params.auditName !=null and params.auditName != '' ">
                AND tau.audit_name = #{params.auditName}
            </if>
            <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
            <if test=" params.applyPerson !=null and params.applyPerson != '' ">
                AND t.apply_person  like CONCAT('%',#{params.applyPerson},'%')
            </if>
            <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
            <if test=" params.applyNo !=null and params.applyNo != '' ">
                AND t.apply_no like CONCAT('%',#{params.applyNo},'%')
            </if>
            <if test=" params.customerName !=null and params.customerName != '' ">
                AND (t.customer_name like CONCAT('%',#{params.customerName},'%') or t.customer_mobile like CONCAT('%',#{params.customerName},'%'))
            </if>
                    <if test=" params.customerMobile !=null and params.customerMobile != '' ">
                AND t.customer_mobile = #{params.customerMobile}
            </if>
             <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin = #{params.vin}
            </if>
            <if test=" params.license !=null and params.license != '' ">
                AND (t.license like CONCAT('%',#{params.license},'%')  or t.vin like CONCAT('%',#{params.license},'%') )
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id  like CONCAT('%',#{params.complaintId},'%')
            </if>

            <if test=" params.complaintFalut1 !=null and params.complaintFalut1.length > 0 ">
                AND t.complaint_falut in
					<foreach collection="params.complaintFalut1" index="index" item="item"
					             open="(" separator="," close=")">
					        #{item}
					    </foreach>
            </if>
            <if test=" params.goodwillStatus !=null and params.goodwillStatus != '' ">
                AND t.goodwill_status = #{params.goodwillStatus}
            </if>
            <if test=" params.auditType !=null ">
                AND t.audit_type = #{params.auditType}
            </if>

            <if test=" params.applyStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.applyStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.applyEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.applyEndTime }, '%Y-%m-%d')   ]]>
            </if>
                    <if test=" params.applyStartAmount !=null and params.applyStartAmount != '' ">
                AND <![CDATA[ t.apply_amount>= ]]>  #{params.applyStartAmount}
            </if>
                    <if test=" params.applyEndAmount !=null and params.applyEndAmount != '' ">
                AND <![CDATA[ t.apply_amount<= ]]> #{params.applyEndAmount}
            </if>

            <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>

<!--             <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>-->

            <if test=" params.invoiceStartDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.invoice_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.invoiceStartDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.invoiceEndDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.invoice_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.invoiceEndDate }, '%Y-%m-%d')   ]]>
            </if>

            <if test=" params.noticeInvoiceStartDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.notice_invoice_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.noticeInvoiceStartDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.noticeInvoiceEndDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.notice_invoice_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.noticeInvoiceEndDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.updatedStartAt !=null  ">
                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')>=  DATE_FORMAT(#{params.updatedStartAt }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.updatedEndAt !=null  ">
                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')<=  DATE_FORMAT(#{params.updatedEndAt }, '%Y-%m-%d')   ]]>
            </if>

			and s.updated_by = #{params.userId}
			group by t.apply_no  order by t.apply_time desc
            </select>
		 <select id="queryApplyHistory" resultMap="BaseResultMap">
       SELECT *  from tt_goodwill_apply_info t
       where t.vin=#{vin} and t.apply_no &lt;&gt; #{applyNo}

       </select>
 	<select id="queryApplyByDealerCode" resultType="map">
       select COUNT(dealer_code) applyTimes,
			(select COUNT(1) from tt_goodwill_apply_info  where dealer_code=#{dealerCode} AND DATE_FORMAT(apply_time,'%Y')=#{year} AND GOODWILL_STATUS=82551015) refuseSupportTimes,
			(select COUNT(1) from tt_goodwill_apply_info  where dealer_code=#{dealerCode} AND DATE_FORMAT(apply_time,'%Y')=#{year} AND GOODWILL_STATUS=82551016) unSupportTimes,
			(select IFNULL(SUM(apply_amount),0) from tt_goodwill_apply_info  where dealer_code=#{dealerCode} AND DATE_FORMAT(apply_time,'%Y')=#{year} AND GOODWILL_STATUS not in (82551015,82551016)) validAmount,
			(select IFNULL(SUM(settlement_amount),0) from tt_goodwill_apply_info  where dealer_code=#{dealerCode} AND DATE_FORMAT(apply_time,'%Y')=#{year} AND GOODWILL_STATUS not in (82551015,82551016)) validSettlementAmount
		from tt_goodwill_apply_info  where dealer_code=#{dealerCode} AND DATE_FORMAT(apply_time,'%Y')=#{year}

       </select>
       <select id="selectGoodwillApplyInfo" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO">
        	SELECT
           tau.auditor,tau.audit_name auditName,
	        t.APP_ID,t.OWNER_CODE,t.OWNER_PAR_CODE,t.ORG_ID,t.id,t.apply_no,
	        t.dealer_code,t.dealer_name,t.area_manage_id,t.area_manage,t.bloc,t.updated_person,
	        t.bloc_id,t.audit_type,t.goodwill_nature,t.apply_time,t.pass_time,
	        t.complaint_falut,t.complaint_id,t.vehicle_use,t.sales_dealer,
	        t.apply_amount,t.audit_amount,t.settlement_amount,t.invoice_amount,
	        t.goodwill_status,t.customer_pain,t.vin,t.license,t.customer_name,
	        t.customer_mobile,t.mileage,t.model,t.buy_car_date,t.warranty_start_date,
	        t.is_extend_warranty,t.extend_warranty_name,t.extend_warranty_start_date,
	        t.extend_warranty_end_date,t.maintain_cost,t.extend_warranty_cost,
	        t.accessory_cost,t.voucher_cost,t.walking_car_price,t.volvo_integral,
	        t.return_change_car_price,t.other_price,t.cost_total,t.customer_pay,
	        t.dealer_undertake,t.volvo_support_goodwill_amount,t.remark,t.apply_file,
	        t.cost_statistics_file,t.cost_screenshot_file,t.ring_check_file,
	        t.trouble_repair_requisition_file,t.work_order_file,t.situation_settlement_agreement_file,
	        t.supplementary_material_file,t.management_review_email_vp_file,
	        t.management_review_email_ceo_file,t.cost_update_file,t.vcdc_else_file,
	        t.customer_identification,t.else_file,t.commit_time,t.is_valid,t.is_deleted,
	        t.created_at,t.updated_at
        FROM tt_goodwill_apply_info t
           left join    tt_goodwill_audit_info  tau on  t.id=tau.goodwill_apply_id and   tau.audit_object =0 and tau.is_deleted =0 and tau.audit_role= 'SHQYJL'
        <!-- left join tt_goodwill_audit_info s on t.id=s.goodwill_apply_id -->
        WHERE 1=1  and t.goodwill_status=82551001

            <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND (t.dealer_code  like CONCAT('%',#{params.dealerCode},'%') or t.dealer_name  like CONCAT('%',#{params.dealerCode},'%'))
            </if>
             <if test=" params.blocId !=null and params.blocId != '' ">
	                AND t.bloc_id = #{params.blocId}
	            </if>
	            <if test=" params.areaManageId !=null and params.areaManageId != '' ">
	                AND t.area_Manage_Id = #{params.areaManageId}
	            </if>
            <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461001 ">
                AND t.dealer_code = #{params.ownerCodes}
            </if>
            <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461003 ">
                AND t.created_by = #{params.userId}
            </if>
            <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
            <if test=" params.applyPerson !=null and params.applyPerson != '' ">
                AND t.apply_person = #{params.applyPerson}
            </if>
            <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
            <if test=" params.auditType !=null ">
                AND t.audit_type = #{params.auditType}
            </if>
            <if test=" params.applyNo !=null and params.applyNo != '' ">
                AND t.apply_no like CONCAT('%',#{params.applyNo},'%')
            </if>
            <if test=" params.customerName !=null and params.customerName != '' ">
                AND (t.customer_name like CONCAT('%',#{params.customerName},'%') or t.customer_mobile like CONCAT('%',#{params.customerName},'%'))
            </if>
                    <if test=" params.customerMobile !=null and params.customerMobile != '' ">
                AND t.customer_mobile = #{params.customerMobile}
            </if>
             <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin = #{params.vin}
            </if>
            <if test=" params.license !=null and params.license != '' ">
                AND (t.license like CONCAT('%',#{params.license},'%')  or t.vin like CONCAT('%',#{params.license},'%') )
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id  like CONCAT('%',#{params.complaintId},'%')
            </if>
              <if test=" params.complaintFalut !=null and params.complaintFalut != '' ">
                AND t.complaint_falut = #{params.complaintFalut}
            </if>

            <if test=" params.goodwillStatus !=null and params.goodwillStatus != '' ">
                AND t.goodwill_status = #{params.goodwillStatus}
            </if>

            <if test=" params.applyStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.applyStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.applyEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.applyEndTime }, '%Y-%m-%d')   ]]>
            </if>
                    <if test=" params.applyStartAmount !=null and params.applyStartAmount != '' ">
                AND <![CDATA[ t.apply_amount>= ]]>  #{params.applyStartAmount}
            </if>
                    <if test=" params.applyEndAmount !=null and params.applyEndAmount != '' ">
                AND <![CDATA[ t.apply_amount<= ]]> #{params.applyEndAmount}
            </if>

            <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>

<!--             <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>

            <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if> -->

            <if test=" params.updatedStartAt !=null  ">
                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')>=  DATE_FORMAT(#{params.updatedStartAt }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.updatedEndAt !=null  ">
                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')<=  DATE_FORMAT(#{params.updatedEndAt }, '%Y-%m-%d')   ]]>
            </if>
             order by apply_time desc
			<!-- and s.updated_by = #{params.userId} -->
            </select>

            <select id="selectDealerTodoGoodwillApplyInfo" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO">
        	SELECT
                tau.auditor,tau.audit_name auditName,
	        t.APP_ID,t.OWNER_CODE,t.OWNER_PAR_CODE,t.ORG_ID,t.id,t.apply_no,
	        t.dealer_code,t.dealer_name,t.area_manage_id,t.area_manage,t.bloc,t.updated_person,
	        t.bloc_id,t.audit_type,t.goodwill_nature,t.apply_time,t.pass_time,t.material_commit_time,
	        t.complaint_falut,t.complaint_id,t.vehicle_use,t.sales_dealer,t.material_pass_time,
	        t.apply_amount,t.audit_amount,t.settlement_amount,t.invoice_amount,
	        t.goodwill_status,t.customer_pain,t.vin,t.license,t.customer_name,
	        t.customer_mobile,t.mileage,t.model,t.buy_car_date,t.warranty_start_date,
	        t.is_extend_warranty,t.extend_warranty_name,t.extend_warranty_start_date,
	        t.extend_warranty_end_date,t.maintain_cost,t.extend_warranty_cost,
	        t.accessory_cost,t.voucher_cost,t.walking_car_price,t.volvo_integral,
	        t.return_change_car_price,t.other_price,t.cost_total,t.customer_pay,
	        t.dealer_undertake,t.volvo_support_goodwill_amount,t.remark,t.apply_file,
	        t.cost_statistics_file,t.cost_screenshot_file,t.ring_check_file,
	        t.trouble_repair_requisition_file,t.work_order_file,t.situation_settlement_agreement_file,
	        t.supplementary_material_file,t.management_review_email_vp_file,
	        t.management_review_email_ceo_file,t.cost_update_file,t.vcdc_else_file,
	        t.customer_identification,t.else_file,t.commit_time,t.is_valid,t.is_deleted,
	        t.created_at,t.updated_at,tiv.noticeInvoicePrice,tir.invoicePrice,
	        ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(IFNULL(t.material_pass_time,NOW()), '%Y-%m-%d %H:%i:%S'))/24,1) materialAuditLength,
      	    IFNULL(ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'))/24,1),'') materialCommitLength,
	        ttt.notice_invoice_date,case when ttttt.id is null then 10041002 else 10041001 end isRestart,
	        CASE WHEN t.material_commit_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialSummit,
			CASE WHEN t.material_pass_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialAudit

        FROM tt_goodwill_apply_info t
        left join (select sum(notice_invoice_price) noticeInvoicePrice,goodwill_apply_id from  tt_goodwill_notice_invoice_info group by goodwill_apply_id) tiv on t.id=tiv.goodwill_apply_id
        left join (select  sum(ttt.invoice_price) invoicePrice,tt.goodwill_apply_id  from  tt_goodwill_notice_invoice_info tt left join tt_goodwill_invoice_record ttt on tt.invoice_id=ttt.invoice_id group by tt.goodwill_apply_id)tir on t.id=tir.goodwill_apply_id
        left join   tt_goodwill_notice_invoice_info ttt on t.id=ttt.goodwill_apply_id and ttt.invoice_object='0'
        left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where 1=1  group by goodwill_apply_id ) t
			left join tt_goodwill_audit_info tt on t.max_id=tt.id  )tt on t.id=tt.goodwill_apply_id
		left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801004  group by goodwill_apply_id ) t
			left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) tttt on  t.id=tttt.goodwill_apply_id
		left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801005  group by goodwill_apply_id ) t
			left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) ttttt on  t.id=ttttt.goodwill_apply_id
       left join ( select auditor,goodwill_apply_id,audit_name  from   tt_goodwill_audit_info where audit_object =0 and is_deleted =0 and audit_role= 'SHQYJL'  ) tau on t.id=tau.goodwill_apply_id
        WHERE 1=1    and (t.goodwill_status in (82551001,82551004,82551008,82551012)
               or (t.goodwill_status in (82551003,82551006) and tt.return_to =t.dealer_code))
            <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code  = #{params.dealerCode}
            </if>
            <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461001 ">
                AND t.dealer_code = #{params.ownerCodes}
            </if>
            <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
            <if test=" params.applyPerson !=null and params.applyPerson != '' ">
                AND t.apply_person = #{params.applyPerson}
            </if>
            <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
            <if test=" params.applyNo !=null and params.applyNo != '' ">
                AND t.apply_no like CONCAT('%',#{params.applyNo},'%')
            </if>
            <if test=" params.customerName !=null and params.customerName != '' ">
                AND (t.customer_name like CONCAT('%',#{params.customerName},'%') or t.customer_mobile like CONCAT('%',#{params.customerName},'%'))
            </if>
                    <if test=" params.customerMobile !=null and params.customerMobile != '' ">
                AND t.customer_mobile = #{params.customerMobile}
            </if>
             <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin = #{params.vin}
            </if>
            <if test=" params.license !=null and params.license != '' ">
                AND (t.license like CONCAT('%',#{params.license},'%')  or t.vin like CONCAT('%',#{params.license},'%') )
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id  like CONCAT('%',#{params.complaintId},'%')
            </if>
              <if test=" params.complaintFalut !=null and params.complaintFalut != '' ">
                AND t.complaint_falut = #{params.complaintFalut}
            </if>

            <if test=" params.goodwillStatus !=null and params.goodwillStatus != '' ">
                AND t.goodwill_status = #{params.goodwillStatus}
            </if>
            <if test=" params.auditType !=null  ">
                AND t.audit_type = #{params.auditType}
            </if>

            <if test=" params.applyStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.applyStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.applyEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.applyEndTime }, '%Y-%m-%d')   ]]>
            </if>
                    <if test=" params.applyStartAmount !=null and params.applyStartAmount != '' ">
                AND <![CDATA[ t.apply_amount>= ]]>  #{params.applyStartAmount}
            </if>
                    <if test=" params.applyEndAmount !=null and params.applyEndAmount != '' ">
                AND <![CDATA[ t.apply_amount<= ]]> #{params.applyEndAmount}
            </if>

            <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>

<!--             <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>

            <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if> -->

            <if test=" params.noticeInvoiceStartDate !=null  ">
                <![CDATA[   and DATE_FORMAT(ttt.notice_invoice_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.noticeInvoiceStartDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.noticeInvoiceEndDate !=null  ">
                <![CDATA[   and DATE_FORMAT(ttt.notice_invoice_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.noticeInvoiceEndDate }, '%Y-%m-%d')   ]]>
            </if>
              group by t.id     order by apply_time desc
			<!-- and s.updated_by = #{params.userId} -->
            </select>

            <select id="selectOemTodoGoodwillApplyInfo" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO">
        	SELECT
	        t.APP_ID,t.OWNER_CODE,t.OWNER_PAR_CODE,t.ORG_ID,t.id,t.apply_no,
	        t.dealer_code,t.dealer_name,t.area_manage_id,t.area_manage,t.small_area_id,t.small_area,tau.audit_name,t.bloc,t.updated_person,
	        t.bloc_id,t.audit_type,t.goodwill_nature,t.apply_time,t.pass_time,t.material_commit_time,
	        t.complaint_falut,t.complaint_id,t.vehicle_use,t.sales_dealer,t.material_pass_time,
	        t.apply_amount,t.audit_amount,t.settlement_amount,t.invoice_amount,
	        t.goodwill_status,t.customer_pain,t.vin,t.license,t.customer_name,t.apply_person,
	        t.customer_mobile,t.mileage,t.model,t.buy_car_date,t.warranty_start_date,
	        t.is_extend_warranty,t.extend_warranty_name,t.extend_warranty_start_date,
	        t.extend_warranty_end_date,t.maintain_cost,t.extend_warranty_cost,
	        t.accessory_cost,t.voucher_cost,t.walking_car_price,t.volvo_integral,
	        t.return_change_car_price,t.other_price,t.cost_total,t.customer_pay,
	        t.dealer_undertake,t.volvo_support_goodwill_amount,t.remark,t.apply_file,
	        t.cost_statistics_file,t.cost_screenshot_file,t.ring_check_file,
	        t.trouble_repair_requisition_file,t.work_order_file,t.situation_settlement_agreement_file,
	        t.supplementary_material_file,t.management_review_email_vp_file,
	        t.management_review_email_ceo_file,t.cost_update_file,t.vcdc_else_file,
	        t.customer_identification,t.else_file,t.commit_time,t.is_valid,t.is_deleted,
	        ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(IFNULL(t.material_pass_time,NOW()), '%Y-%m-%d %H:%i:%S'))/24,1) materialAuditLength,
      	    IFNULL(ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'))/24,1),'') materialCommitLength,
	        CASE WHEN t.material_commit_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialSummit,
			CASE WHEN t.material_pass_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialAudit,
	        case when ttttt.id is null then 10041002 else 10041001 end isRestart,
	        case when t.goodwill_status=82551005 and s.audit_role like 'CFO' and t.is_need_translate=10041001 then 10041001 else 10041002 end as isCcmq,
	        t.created_at,t.updated_at,ttt.notice_invoice_date
        FROM tt_goodwill_apply_info t
        LEFT JOIN tt_goodwill_material_audit tgma on t.ID=tgma.goodwill_apply_id
       	left join   tt_goodwill_notice_invoice_info ttt on t.id=ttt.goodwill_apply_id and ttt.invoice_object='0'
        left join (select *  from (select max(id) max_id from tt_goodwill_audit_info  group by goodwill_apply_id ) t
			left join tt_goodwill_audit_info tt on t.max_id=tt.id  )tt on t.id=tt.goodwill_apply_id
		left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801004  group by goodwill_apply_id ) t
			left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) tttt on  t.id=tttt.goodwill_apply_id
		left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801005  group by goodwill_apply_id ) t
			left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) ttttt on  t.id=ttttt.goodwill_apply_id
        left join (  SELECT * from tt_goodwil_apply_audit_process where id in (
                        SELECT min(id) AS min_id
                    FROM tt_goodwil_apply_audit_process t
                    WHERE t.audit_status IS NULL
                    GROUP BY t.goodwill_apply_id)) s on t.id=s.goodwill_apply_id
                left join ( select auditor,goodwill_apply_id,audit_name  from   tt_goodwill_audit_info where audit_object =0 and is_deleted =0 and audit_role= 'SHQYJL'  ) tau on t.id=tau.goodwill_apply_id
        WHERE 1=1 and (t.goodwill_status in (82551002,82551005,82551007) or (t.goodwill_status in (82551003,82551006) and tt.return_to in ('SHQYJL','SHDQJL','CCMQ','SHQYZJ','VP','CEO','CCMGJJL','CCMZJ','OEM-CWJL','CFO')))

            <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND (t.dealer_code  like CONCAT('%',#{params.dealerCode},'%') or t.dealer_name  like CONCAT('%',#{params.dealerCode},'%'))
            </if>
            <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461001 ">
                AND t.dealer_code = #{params.ownerCodes}
            </if>
             <if test=" params.blocId !=null and params.blocId != '' ">
	                AND t.bloc_id = #{params.blocId}
	            </if>
	            <if test=" params.areaManageId1 !=null and params.areaManageId1 != '' ">
	                AND t.area_Manage_Id IN (${params.areaManageId1})
	            </if>
                <if test=" params.smallAreaId1 !=null and params.smallAreaId1 != '' ">
	                AND t.small_area_id IN (${params.smallAreaId1})
	            </if>
                <if test=" params.auditName !=null and params.auditName != '' ">
                AND tau.audit_name = #{params.auditName}
            </if>
                <if test=" params.auditName1 !=null and params.auditName1.size > 0 ">
                    AND tau.audit_name in
                    <foreach collection="params.auditName1" item="audit_name" open="(" separator="," close=")">
                        #{audit_name}
                    </foreach>
                </if>
            <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
            <if test=" params.applyPerson !=null and params.applyPerson != '' ">
                AND t.apply_person  like CONCAT('%',#{params.applyPerson},'%')
            </if>
            <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
            <if test=" params.applyNo !=null and params.applyNo != '' ">
                AND t.apply_no like CONCAT('%',#{params.applyNo},'%')
            </if>
            <if test=" params.customerName !=null and params.customerName != '' ">
                AND (t.customer_name like CONCAT('%',#{params.customerName},'%') or t.customer_mobile like CONCAT('%',#{params.customerName},'%'))
            </if>
                    <if test=" params.customerMobile !=null and params.customerMobile != '' ">
                AND t.customer_mobile = #{params.customerMobile}
            </if>
             <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin = #{params.vin}
            </if>
            <if test=" params.license !=null and params.license != '' ">
                AND (t.license like CONCAT('%',#{params.license},'%')  or t.vin like CONCAT('%',#{params.license},'%') )
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id  like CONCAT('%',#{params.complaintId},'%')
            </if>
              <if test=" params.complaintFalut !=null and params.complaintFalut != '' ">
                AND t.complaint_falut = #{params.complaintFalut}
            </if>

            <if test=" params.goodwillStatus !=null and params.goodwillStatus != '' ">
                AND t.goodwill_status = #{params.goodwillStatus}
            </if>
            <if test=" params.auditType !=null">
                AND t.audit_type = #{params.auditType}
            </if>

            <if test=" params.applyStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.applyStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.applyEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.applyEndTime }, '%Y-%m-%d')   ]]>
            </if>
                    <if test=" params.applyStartAmount !=null and params.applyStartAmount != '' ">
                AND <![CDATA[ t.apply_amount>= ]]>  #{params.applyStartAmount}
            </if>
                    <if test=" params.applyEndAmount !=null and params.applyEndAmount != '' ">
                AND <![CDATA[ t.apply_amount<= ]]> #{params.applyEndAmount}
            </if>

            <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>

             <if test=" params.updatedStartAt !=null  ">
                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')>=  DATE_FORMAT(#{params.updatedStartAt }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.updatedEndAt !=null  ">
                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')<=  DATE_FORMAT(#{params.updatedEndAt }, '%Y-%m-%d')   ]]>
            </if>

           <!-- <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if> -->
            <if test=" params.roleList !=null and params.roleList.length > 0 ">
                AND ((t.goodwill_status =82551002 and s.audit_role not in("VP","CEO") and s.audit_role in
					<foreach collection="params.roleList" index="index" item="item"
					             open="(" separator="," close=")">
					        #{item}
					    </foreach>
				   )or (t.goodwill_status=82551005 and s.audit_role in
					<foreach collection="params.roleList" index="index" item="item"
					             open="(" separator="," close=")">
					        #{item}
					    </foreach>
				   ) or (t.goodwill_status in (82551003,82551006) and tt.return_to in
				    <foreach collection="params.roleList" index="index" item="item"
				             open="(" separator="," close=")">
				        #{item}
				    </foreach>
					    )
					or ((t.goodwill_status=82551007 or ((t.goodwill_status=82551002 and (s.audit_role in ('VP','CEO')) or(t.goodwill_status=82551005 and s.audit_role like 'CFO' and t.is_need_translate=10041001)))) AND 'CCMQ' in
					<foreach collection="params.roleList" index="index" item="item"
				             open="(" separator="," close=")">
				        #{item}
				    </foreach>
				    )
				)
            </if>
             <if test=" params.roleList1 !=null and params.roleList1 !='' ">
                and (t.goodwill_status in (82551002,82551005) and (s.audit_role =#{params.roleList1} and t.small_area_id= #{params.orgId})
                or ( t.goodwill_status in (82551003,82551006) and tt.return_to =#{params.roleList1} and t.small_area_id= #{params.orgId}))
            </if>
            <if test=" params.roleList2 !=null and params.roleList2.length > 0 ">
                and (t.goodwill_status in (82551002,82551005) and (s.audit_role in
					<foreach collection="params.roleList2" index="index" item="item"
				             open="(" separator="," close=")">
				        #{item}
				    </foreach>
				   and t.area_manage_id= #{params.orgId})
				   or
				   (t.goodwill_status in (82551003,82551006) and (tt.return_to in
				<foreach collection="params.roleList2" index="index" item="item"
				             open="(" separator="," close=")">
				        #{item}
				    </foreach>
				   and t.area_manage_id= #{params.orgId}))
				   )
            </if>
            <if test=" params.noticeInvoiceStartDate !=null  ">
                <![CDATA[   and DATE_FORMAT(ttt.notice_invoice_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.noticeInvoiceStartDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.noticeInvoiceEndDate !=null  ">
                <![CDATA[   and DATE_FORMAT(ttt.notice_invoice_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.noticeInvoiceEndDate }, '%Y-%m-%d')   ]]>
            </if>
             group by t.id     order by apply_time desc
			<!-- and s.updated_by = #{params.userId} -->
            </select>
            <select id="selectOemTodoListGoodwillApplyInfo" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO">
        	SELECT
	        t.APP_ID,t.OWNER_CODE,t.OWNER_PAR_CODE,t.ORG_ID,t.id,t.apply_no,
	        t.dealer_code,t.dealer_name,t.area_manage_id,t.area_manage,t.bloc,t.updated_person,
	        t.bloc_id,t.audit_type,t.goodwill_nature,t.apply_time,t.pass_time,t.material_commit_time,
	        t.complaint_falut,t.complaint_id,t.vehicle_use,t.sales_dealer,t.material_pass_time,
	        t.apply_amount,t.audit_amount,t.settlement_amount,t.invoice_amount,
	        t.goodwill_status,t.customer_pain,t.vin,t.license,t.customer_name,t.apply_person,
	        t.customer_mobile,t.mileage,t.model,t.buy_car_date,t.warranty_start_date,
	        t.is_extend_warranty,t.extend_warranty_name,t.extend_warranty_start_date,
	        t.extend_warranty_end_date,t.maintain_cost,t.extend_warranty_cost,
	        t.accessory_cost,t.voucher_cost,t.walking_car_price,t.volvo_integral,
	        t.return_change_car_price,t.other_price,t.cost_total,t.customer_pay,
	        t.dealer_undertake,t.volvo_support_goodwill_amount,t.remark,t.apply_file,
	        t.cost_statistics_file,t.cost_screenshot_file,t.ring_check_file,
	        t.trouble_repair_requisition_file,t.work_order_file,t.situation_settlement_agreement_file,
	        t.supplementary_material_file,t.management_review_email_vp_file,
	        t.management_review_email_ceo_file,t.cost_update_file,t.vcdc_else_file,
	        t.customer_identification,t.else_file,t.commit_time,t.is_valid,t.is_deleted,
	        ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(IFNULL(t.material_pass_time,NOW()), '%Y-%m-%d %H:%i:%S'))/24,1) materialAuditLength,
      	    IFNULL(ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'))/24,1),'') materialCommitLength,
	        CASE WHEN t.material_commit_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialSummit,
			CASE WHEN t.material_pass_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialAudit,
	        case when ttttt.id is null then 10041002 else 10041001 end isRestart,
	        t.created_at,t.updated_at,ttt.notice_invoice_date
        FROM tt_goodwill_apply_info t
        LEFT JOIN tt_goodwill_material_audit tgma on t.ID=tgma.goodwill_apply_id
       	left join   tt_goodwill_notice_invoice_info ttt on t.id=ttt.goodwill_apply_id and ttt.invoice_object='0'
        left join (select *  from (select max(id) max_id from tt_goodwill_audit_info  group by goodwill_apply_id ) t
			left join tt_goodwill_audit_info tt on t.max_id=tt.id  )tt on t.id=tt.goodwill_apply_id
		left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801004  group by goodwill_apply_id ) t
			left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) tttt on  t.id=tttt.goodwill_apply_id
		left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801005  group by goodwill_apply_id ) t
			left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) ttttt on  t.id=ttttt.goodwill_apply_id
        left join (select *  from (select min(id) min_id from tt_goodwil_apply_audit_process t where  t.audit_status is null group by t.goodwill_apply_id)t
			left join tt_goodwil_apply_audit_process tt on t.min_id=tt.id ) s on t.id=s.goodwill_apply_id
        WHERE 1=1 and t.goodwill_status in (82551005)

            <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND (t.dealer_code  like CONCAT('%',#{params.dealerCode},'%') or t.dealer_name  like CONCAT('%',#{params.dealerCode},'%'))
            </if>
            <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461001 ">
                AND t.dealer_code = #{params.ownerCodes}
            </if>
             <if test=" params.blocId !=null and params.blocId != '' ">
	                AND t.bloc_id = #{params.blocId}
	            </if>
	            <if test=" params.areaManageId !=null and params.areaManageId != '' ">
	                AND t.area_Manage_Id = #{params.areaManageId}
	            </if>
            <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
            <if test=" params.applyPerson !=null and params.applyPerson != '' ">
                AND t.apply_person  like CONCAT('%',#{params.applyPerson},'%')
            </if>
            <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
            <if test=" params.applyNo !=null and params.applyNo != '' ">
                AND t.apply_no like CONCAT('%',#{params.applyNo},'%')
            </if>
            <if test=" params.customerName !=null and params.customerName != '' ">
                AND (t.customer_name like CONCAT('%',#{params.customerName},'%') or t.customer_mobile like CONCAT('%',#{params.customerName},'%'))
            </if>
                    <if test=" params.customerMobile !=null and params.customerMobile != '' ">
                AND t.customer_mobile = #{params.customerMobile}
            </if>
             <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin = #{params.vin}
            </if>
            <if test=" params.license !=null and params.license != '' ">
                AND (t.license like CONCAT('%',#{params.license},'%')  or t.vin like CONCAT('%',#{params.license},'%') )
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id  like CONCAT('%',#{params.complaintId},'%')
            </if>
              <if test=" params.complaintFalut !=null and params.complaintFalut != '' ">
                AND t.complaint_falut = #{params.complaintFalut}
            </if>

            <if test=" params.goodwillStatus !=null and params.goodwillStatus != '' ">
                AND t.goodwill_status = #{params.goodwillStatus}
            </if>
            <if test=" params.auditType !=null">
                AND t.audit_type = #{params.auditType}
            </if>

            <if test=" params.applyStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.applyStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.applyEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.applyEndTime }, '%Y-%m-%d')   ]]>
            </if>
                    <if test=" params.applyStartAmount !=null and params.applyStartAmount != '' ">
                AND <![CDATA[ t.apply_amount>= ]]>  #{params.applyStartAmount}
            </if>
                    <if test=" params.applyEndAmount !=null and params.applyEndAmount != '' ">
                AND <![CDATA[ t.apply_amount<= ]]> #{params.applyEndAmount}
            </if>

            <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>

             <if test=" params.updatedStartAt !=null  ">
                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')>=  DATE_FORMAT(#{params.updatedStartAt }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.updatedEndAt !=null  ">
                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')<=  DATE_FORMAT(#{params.updatedEndAt }, '%Y-%m-%d')   ]]>
            </if>

           <!-- <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if> -->
            <if test=" params.roleList !=null and params.roleList.length > 0 ">
                AND  t.is_need_translate=10041002 and s.audit_role in
					<foreach collection="params.roleList" index="index" item="item"
					             open="(" separator="," close=")">
					        #{item}
					    </foreach>
            </if>
            <if test=" params.noticeInvoiceStartDate !=null  ">
                <![CDATA[   and DATE_FORMAT(ttt.notice_invoice_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.noticeInvoiceStartDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.noticeInvoiceEndDate !=null  ">
                <![CDATA[   and DATE_FORMAT(ttt.notice_invoice_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.noticeInvoiceEndDate }, '%Y-%m-%d')   ]]>
            </if>
             group by t.id     order by apply_time desc
			<!-- and s.updated_by = #{params.userId} -->
            </select>

             <select id="selectOemSearchGoodwillApplyInfo" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO">
        	SELECT
             tau.auditor,tau.audit_name auditName,
	        t.APP_ID,t.OWNER_CODE,t.OWNER_PAR_CODE,t.ORG_ID,t.id,t.apply_no,t.audit_type auditPart,
	        t.dealer_code,t.dealer_name,t.small_area_id,t.small_area,t.area_manage_id,t.area_manage,t.bloc,t.updated_person,
	        t.bloc_id,t.audit_type,t.goodwill_nature,t.apply_time,t.pass_time,t.material_commit_time,
	        t.complaint_falut,t.complaint_id,t.complaint_date,t.vehicle_use,t.sales_dealer,t.material_pass_time,
	        t.apply_amount,t.audit_amount,t.settlement_amount,t.invoice_amount,
	        t.goodwill_status,t.customer_pain,t.vin,t.license,t.customer_name,
	        t.customer_mobile,t.mileage,t.model,t.buy_car_date,t.warranty_start_date,
	        t.is_extend_warranty,t.extend_warranty_name,t.extend_warranty_start_date,
	        t.extend_warranty_end_date,t.maintain_cost,t.extend_warranty_cost,
	        t.accessory_cost,t.voucher_cost,t.walking_car_price,t.volvo_integral,
	        t.return_change_car_price,t.other_price,t.cost_total,t.customer_pay,
	        t.dealer_undertake,t.volvo_support_goodwill_amount,t.remark,t.apply_file,
	        t.cost_statistics_file,t.cost_screenshot_file,t.ring_check_file,
	        t.trouble_repair_requisition_file,t.work_order_file,t.situation_settlement_agreement_file,
	        t.supplementary_material_file,t.management_review_email_vp_file,
	        t.management_review_email_ceo_file,t.cost_update_file,t.vcdc_else_file,
	        t.customer_identification,t.else_file,t.commit_time,t.is_valid,t.is_deleted,t.cost_rate,
	        ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(IFNULL(t.material_pass_time,NOW()), '%Y-%m-%d %H:%i:%S'))/24,1) materialAuditLength,
      	    IFNULL(ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'))/24,1),'') materialCommitLength,
	        t.created_at,t.updated_at,t.coupon_id,tt.audit_time newAuditDate,tt.audit_result newAuditResult,tt.audit_name newestAuditor,
			ttt.audit_time unSupportDate,ttt.audit_opinion unSupportReason,
			tttt.audit_time refuseSupportDate,tttt.audit_opinion refuseSupportReason,
			ttttt.audit_time restartDate,ttttt.audit_opinion restartReason,
			case when ttttt.id is null then 10041002 else 10041001 end isRestart,tg.notice_invoice_date,tg.voucher_recharge_price rechargeAmount,
			CASE WHEN t.material_commit_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialSummit,
			CASE WHEN t.material_pass_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialAudit,
			tis.voucherInvoiceAmount ,tgi.areaRejectedTimes,
            case when times>0 then IFNULL(tg.voucher_recharge_price,0)+IFNULL(tg.notice_invoice_price,0)+IFNULL(tg.volvo_credits_recharge_price,0) else '' end as noticeOrInvoicePrice,
			tis.voucherNotInvoiceAmount,tgii.ccmqRejectedTimes,tg.voucher_coupon_face_recharge_price as voucherCouponFaceRechargePrice
	        FROM tt_goodwill_apply_info t
	        left join (select count(id) times,goodwill_apply_id from tt_goodwill_notice_invoice_info group by goodwill_apply_id) tin on t.id=tin.goodwill_apply_id
	        left join (select count(id) areaRejectedTimes,goodwill_apply_id  from tt_goodwill_audit_info where audit_result=82801002 and  audit_role in ('SHQYJL','SHDQJL','SHQYZJ') and return_to is not null and return_to not in ('SHQYJL','SHDQJL','SHQYZJ','CCMQ','OEM-CWJL','VP','CFO','CEO','CCMGJJL','CCMZJ') group by goodwill_apply_id )tgi on t.id=tgi.goodwill_apply_id
	        left join (select count(id) ccmqRejectedTimes,goodwill_apply_id  from tt_goodwill_audit_info where audit_result=82801002 and  audit_role in ('CCMQ') and return_to is not null and return_to  in ('SHQYJL','SHDQJL') group by goodwill_apply_id )tgii on t.id=tgii.goodwill_apply_id
	        left join (select *  from (select max(id) max_id from tt_goodwill_audit_info  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  )tt on t.id=tt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801003  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) ttt on  t.id=ttt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801004  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) tttt on  t.id=tttt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801005  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) ttttt on  t.id=ttttt.goodwill_apply_id
	        left join ( select t.notice_invoice_price,t.volvo_credits_recharge_price,t.id,t.goodwill_apply_id,t.invoice_object,t.notice_invoice_date,t.voucher_recharge_price,tt.invoice_date,t.voucher_coupon_face_recharge_price from  tt_goodwill_notice_invoice_info t left join tt_goodwill_invoice_record tt on t.invoice_id=tt.invoice_id where t.invoice_object=0) tg on t.id=tg.goodwill_apply_id
			left join (SELECT t.goodwill_apply_id,IFNULL(ttt.invoice_price,0) voucherInvoiceAmount,
				IFNULL(t.voucher_recharge_price,0)-IFNULL(ttt.invoice_price,0)	 voucherNotInvoiceAmount
				FROM tt_goodwill_notice_invoice_info t
				LEFT JOIN ( SELECT sum( notice_invoice_price ) invoicedPrice, goodwill_apply_id FROM tt_goodwill_notice_invoice_info WHERE voucher_type = 1 GROUP BY goodwill_apply_id ) tttt ON t.goodwill_apply_id = tttt.goodwill_apply_id
				LEFT JOIN ( SELECT sum( invoice_price ) invoice_price, goodwill_apply_id FROM tt_goodwill_invoice_record WHERE CHAR_LENGTH( invoice_id )> 14 GROUP BY goodwill_apply_id ) ttt ON t.goodwill_apply_id = ttt.goodwill_apply_id
				WHERE t.voucher_status = 0 AND t.voucher_type = 0 GROUP BY t.invoice_id) tis on t.id=tis.goodwill_apply_id
             left join ( select auditor,goodwill_apply_id,audit_name  from   tt_goodwill_audit_info where audit_object =0 and is_deleted =0 and audit_role= 'SHQYJL'  ) tau on t.id=tau.goodwill_apply_id
		  WHERE 1=1 and t.goodwill_status not in (82551001)
             <if test=" params.roleList1 !=null and params.roleList1 != ''and params.roleList1=='SHQYJL'">
                AND t.small_area_id=#{params.orgId}
            </if>
             <if test=" params.roleList1 !=null and params.roleList1 != ''and params.roleList1=='SHDQJL'">
                AND t.area_manage_id=#{params.orgId}
            </if>
            <if test=" params.roleList1 !=null and params.roleList1 != ''and params.roleList1=='SHQYZJ'">
                AND t.area_manage_id=#{params.orgId}
            </if>
           <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND (t.dealer_code  like CONCAT('%',#{params.dealerCode},'%') or t.dealer_name  like CONCAT('%',#{params.dealerCode},'%'))
            </if>
            <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461001 ">
                AND t.dealer_code = #{params.ownerCodes}
            </if>
             <if test=" params.blocId !=null and params.blocId != '' ">
	                AND t.bloc_id = #{params.blocId}
	            </if>
	            <if test=" params.areaManageId1 !=null and params.areaManageId1 != '' ">
	                AND t.area_Manage_Id IN (${params.areaManageId1})
	            </if>
                 <if test=" params.smallAreaId1 !=null and params.smallAreaId1 != '' ">
                     AND t.small_area_id IN (${params.smallAreaId1})
                 </if>
                 <if test=" params.auditor !=null ">
                     AND tau.auditor = #{params.auditor}
                 </if>
                 <if test=" params.auditName !=null and params.auditName != '' ">
                     AND tau.audit_name IN (#{params.auditName})
                 </if>
                 <if test=" params.auditName1 !=null and params.auditName1.size > 0 ">
                     AND tau.audit_name in
                     <foreach collection="params.auditName1" item="audit_name" open="(" separator="," close=")">
                         #{audit_name}
                     </foreach>
                 </if>
            <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
            <if test=" params.applyPerson !=null and params.applyPerson != '' ">
                AND t.apply_person  like CONCAT('%',#{params.applyPerson},'%')
            </if>
            <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
            <if test=" params.applyNo !=null and params.applyNo != '' ">
                AND t.apply_no like CONCAT('%',#{params.applyNo},'%')
            </if>
            <if test=" params.customerName !=null and params.customerName != '' ">
                AND (t.customer_name like CONCAT('%',#{params.customerName},'%') or t.customer_mobile like CONCAT('%',#{params.customerName},'%'))
            </if>
                    <if test=" params.customerMobile !=null and params.customerMobile != '' ">
                AND t.customer_mobile = #{params.customerMobile}
            </if>
             <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin = #{params.vin}
            </if>
            <if test=" params.license !=null and params.license != '' ">
                AND (t.license like CONCAT('%',#{params.license},'%')  or t.vin like CONCAT('%',#{params.license},'%') )
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id  like CONCAT('%',#{params.complaintId},'%')
            </if>

            <if test=" params.complaintFalut1 !=null and params.complaintFalut1.length > 0 ">
                AND t.complaint_falut in
					<foreach collection="params.complaintFalut1" index="index" item="item"
					             open="(" separator="," close=")">
					        #{item}
					    </foreach>
            </if>
            <if test=" params.goodwillStatus !=null and params.goodwillStatus != '' ">
                AND t.goodwill_status = #{params.goodwillStatus}
            </if>
            <if test=" params.auditType !=null ">
                AND t.audit_type = #{params.auditType}
            </if>

            <if test=" params.applyStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.applyStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.applyEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.applyEndTime }, '%Y-%m-%d')   ]]>
            </if>
                    <if test=" params.applyStartAmount !=null and params.applyStartAmount != '' ">
                AND <![CDATA[ t.apply_amount>= ]]>  #{params.applyStartAmount}
            </if>
                    <if test=" params.applyEndAmount !=null and params.applyEndAmount != '' ">
                AND <![CDATA[ t.apply_amount<= ]]> #{params.applyEndAmount}
            </if>

            <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>

<!--             <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>-->

            <if test=" params.invoiceStartDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.invoice_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.invoiceStartDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.invoiceEndDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.invoice_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.invoiceEndDate }, '%Y-%m-%d')   ]]>
            </if>

            <if test=" params.noticeInvoiceStartDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.notice_invoice_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.noticeInvoiceStartDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.noticeInvoiceEndDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.notice_invoice_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.noticeInvoiceEndDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.updatedStartAt !=null  ">
                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')>=  DATE_FORMAT(#{params.updatedStartAt }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.updatedEndAt !=null  ">
                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')<=  DATE_FORMAT(#{params.updatedEndAt }, '%Y-%m-%d')   ]]>
            </if>
             group by t.id    order by apply_time desc
			<!-- and s.updated_by = #{params.userId} -->
            </select>
          <select id="querySupportApplyDealerSearchInfo" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO">
        	SELECT
              tau.auditor,tau.audit_name auditName,
	        t.APP_ID,t.OWNER_CODE,t.OWNER_PAR_CODE,t.ORG_ID,t.id,t.apply_no,t.audit_type auditPart,
	        t.dealer_code,t.dealer_name,t.small_area_id,t.small_area,t.area_manage_id,t.area_manage,t.bloc,t.updated_person,
	        t.bloc_id,t.audit_type,t.goodwill_nature,t.apply_time,t.pass_time,t.material_commit_time,
	        t.complaint_falut,t.complaint_id,t.complaint_date,t.vehicle_use,t.sales_dealer,t.material_pass_time,
	        t.apply_amount,t.audit_amount,t.settlement_amount,t.invoice_amount,
	        t.goodwill_status,t.customer_pain,t.vin,t.license,t.customer_name,
	        t.customer_mobile,t.mileage,t.model,t.buy_car_date,t.warranty_start_date,
	        t.is_extend_warranty,t.extend_warranty_name,t.extend_warranty_start_date,
	        t.extend_warranty_end_date,t.maintain_cost,t.extend_warranty_cost,
	        t.accessory_cost,t.voucher_cost,t.walking_car_price,t.volvo_integral,
	        t.return_change_car_price,t.other_price,t.cost_total,t.customer_pay,
	        t.dealer_undertake,t.volvo_support_goodwill_amount,t.remark,t.apply_file,
	        t.cost_statistics_file,t.cost_screenshot_file,t.ring_check_file,
	        t.trouble_repair_requisition_file,t.work_order_file,t.situation_settlement_agreement_file,
	        t.supplementary_material_file,t.management_review_email_vp_file,
	        t.management_review_email_ceo_file,t.cost_update_file,t.vcdc_else_file,
	        t.customer_identification,t.else_file,t.commit_time,t.is_valid,t.is_deleted,t.coupon_id,
	        ttt.audit_time unSupportDate,ttt.audit_opinion unSupportReason,
			tttt.audit_time refuseSupportDate,tttt.audit_opinion refuseSupportReason,
			ttttt.audit_time restartDate,ttttt.audit_opinion restartReason,
	        case when ttttt.id is null then 10041002 else 10041001 end isRestart,tg.notice_invoice_date,tg.voucher_recharge_price rechargeAmount,
	        ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(IFNULL(t.material_pass_time,NOW()), '%Y-%m-%d %H:%i:%S'))/24,1) materialAuditLength,
      	    IFNULL(ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'))/24,1),'') materialCommitLength,
	        CASE WHEN t.material_commit_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialSummit,
			CASE WHEN t.material_pass_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialAudit,
	        t.created_at,t.updated_at,tt.audit_time newAuditDate,tt.audit_result newAuditResult,tt.audit_name newestAuditor,
        	tis.voucherInvoiceAmount,tgi.areaRejectedTimes,t.cost_rate,
            case when times>0 then IFNULL(tg.voucher_recharge_price,0)+IFNULL(tg.notice_invoice_price,0)+IFNULL(tg.volvo_credits_recharge_price,0) else '' end as noticeOrInvoicePrice,
			tis.voucherNotInvoiceAmount,tgii.ccmqRejectedTimes,tg.voucher_coupon_face_recharge_price as voucherCouponFaceRechargePrice
	        FROM tt_goodwill_apply_info t
            left join (select count(id) times,goodwill_apply_id from tt_goodwill_notice_invoice_info group by goodwill_apply_id) tin on t.id=tin.goodwill_apply_id
        	left join (select count(id) areaRejectedTimes,goodwill_apply_id  from tt_goodwill_audit_info where audit_result=82801002 and  audit_role in ('SHQYJL','SHDQJL','SHQYZJ') and return_to is not null and return_to not in ('SHQYJL','SHDQJL','SHQYZJ','CCMQ','OEM-CWJL','VP','CFO','CEO','CCMGJJL','CCMZJ') group by goodwill_apply_id )tgi on t.id=tgi.goodwill_apply_id
        	left join (select count(id) ccmqRejectedTimes,goodwill_apply_id  from tt_goodwill_audit_info where audit_result=82801002 and  audit_role in ('CCMQ') and return_to is not null and return_to  in ('SHQYJL','SHDQJL') group by goodwill_apply_id )tgii on t.id=tgii.goodwill_apply_id
        	left join (select *  from (select max(id) max_id from tt_goodwill_audit_info  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  )tt on t.id=tt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801003  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) ttt on  t.id=ttt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801004  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) tttt on  t.id=tttt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801005  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) ttttt on  t.id=ttttt.goodwill_apply_id
			left join ( select t.volvo_credits_recharge_price,t.notice_invoice_price,t.id,t.goodwill_apply_id,t.invoice_object,t.notice_invoice_date,t.voucher_recharge_price,t.voucher_coupon_face_recharge_price from  tt_goodwill_notice_invoice_info t where t.invoice_object=0) tg on t.id=tg.goodwill_apply_id
			left join (SELECT t.goodwill_apply_id,IFNULL(ttt.invoice_price,0) voucherInvoiceAmount,
				IFNULL(t.voucher_recharge_price,0)-IFNULL(ttt.invoice_price,0)	 voucherNotInvoiceAmount
				FROM tt_goodwill_notice_invoice_info t
				LEFT JOIN ( SELECT sum( notice_invoice_price ) invoicedPrice, goodwill_apply_id FROM tt_goodwill_notice_invoice_info WHERE voucher_type = 1 GROUP BY goodwill_apply_id ) tttt ON t.goodwill_apply_id = tttt.goodwill_apply_id
				LEFT JOIN ( SELECT sum( invoice_price ) invoice_price, goodwill_apply_id FROM tt_goodwill_invoice_record WHERE CHAR_LENGTH( invoice_id )> 14 GROUP BY goodwill_apply_id ) ttt ON t.goodwill_apply_id = ttt.goodwill_apply_id
				WHERE t.voucher_status = 0 AND t.voucher_type = 0 GROUP BY t.invoice_id) tis on t.id=tis.goodwill_apply_id
              left join ( select auditor,goodwill_apply_id,audit_name  from   tt_goodwill_audit_info where audit_object =0 and is_deleted =0 and audit_role= 'SHQYJL'  ) tau on t.id=tau.goodwill_apply_id
            WHERE 1=1 and t.goodwill_status not in (82551001)

            <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND (t.dealer_code  like CONCAT('%',#{params.dealerCode},'%') or t.dealer_name  like CONCAT('%',#{params.dealerCode},'%'))
            </if>
            <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461001 ">
                AND t.dealer_code = #{params.ownerCodes}
            </if>
            <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
            <if test=" params.applyPerson !=null and params.applyPerson != '' ">
                AND t.apply_person  like CONCAT('%',#{params.applyPerson},'%')
            </if>
            <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
            <if test=" params.applyNo !=null and params.applyNo != '' ">
                AND t.apply_no like CONCAT('%',#{params.applyNo},'%')
            </if>
            <if test=" params.customerName !=null and params.customerName != '' ">
                AND (t.customer_name like CONCAT('%',#{params.customerName},'%') or t.customer_mobile like CONCAT('%',#{params.customerName},'%'))
            </if>
                    <if test=" params.customerMobile !=null and params.customerMobile != '' ">
                AND t.customer_mobile = #{params.customerMobile}
            </if>
             <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin = #{params.vin}
            </if>
            <if test=" params.license !=null and params.license != '' ">
                AND (t.license like CONCAT('%',#{params.license},'%')  or t.vin like CONCAT('%',#{params.license},'%') )
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id  like CONCAT('%',#{params.complaintId},'%')
            </if>
              <if test=" params.complaintFalut !=null and params.complaintFalut != '' ">
                AND t.complaint_falut = #{params.complaintFalut}
            </if>

            <if test=" params.goodwillStatus !=null and params.goodwillStatus != '' ">
                AND t.goodwill_status = #{params.goodwillStatus}
            </if>
            <if test=" params.auditType !=null ">
                AND t.audit_type = #{params.auditType}
            </if>

            <if test=" params.applyStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.applyStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.applyEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.applyEndTime }, '%Y-%m-%d')   ]]>
            </if>
                    <if test=" params.applyStartAmount !=null and params.applyStartAmount != '' ">
                AND <![CDATA[ t.apply_amount>= ]]>  #{params.applyStartAmount}
            </if>
                    <if test=" params.applyEndAmount !=null and params.applyEndAmount != '' ">
                AND <![CDATA[ t.apply_amount<= ]]> #{params.applyEndAmount}
            </if>

            <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>

<!--             <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>

            <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if> -->

            <if test=" params.noticeInvoiceStartDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.notice_invoice_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.noticeInvoiceStartDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.noticeInvoiceEndDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.notice_invoice_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.noticeInvoiceEndDate }, '%Y-%m-%d')   ]]>
            </if>
             group by t.id     order by apply_time desc
			<!-- and s.updated_by = #{params.userId} -->
            </select>
			<select id="querySupportApplyAuditInfo" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO">
	        	SELECT
		        t.APP_ID,t.OWNER_CODE,t.OWNER_PAR_CODE,t.ORG_ID,t.id,t.apply_no,
		        t.dealer_code,t.dealer_name,t.area_manage_id,t.area_manage,t.bloc,t.updated_person,
		        t.bloc_id,t.audit_type,t.goodwill_nature,t.apply_time,t.pass_time,t.material_commit_time,
		        t.complaint_falut,t.complaint_id,t.complaint_date,t.vehicle_use,t.sales_dealer,t.material_pass_time,
		        t.apply_amount,t.audit_amount,t.settlement_amount,t.invoice_amount,
		        t.goodwill_status,t.customer_pain,t.vin,t.license,t.customer_name,
		        t.customer_mobile,t.mileage,t.model,t.buy_car_date,t.warranty_start_date,
		        t.is_extend_warranty,t.extend_warranty_name,t.extend_warranty_start_date,
		        t.extend_warranty_end_date,t.maintain_cost,t.extend_warranty_cost,
		        t.accessory_cost,t.voucher_cost,t.walking_car_price,t.volvo_integral,
		        t.return_change_car_price,t.other_price,t.cost_total,t.customer_pay,
		        t.dealer_undertake,t.volvo_support_goodwill_amount,t.remark,t.apply_file,
		        t.cost_statistics_file,t.cost_screenshot_file,t.ring_check_file,
		        t.trouble_repair_requisition_file,t.work_order_file,t.situation_settlement_agreement_file,
		        t.supplementary_material_file,t.management_review_email_vp_file,
		        t.management_review_email_ceo_file,t.cost_update_file,t.vcdc_else_file,
		        t.customer_identification,t.else_file,t.commit_time,t.is_valid,t.is_deleted,
		        ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(IFNULL(t.material_pass_time,NOW()), '%Y-%m-%d %H:%i:%S'))/24,1) materialAuditLength,
      	    	IFNULL(ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'))/24,1),'') materialCommitLength,
      	    	case when ttttt.id is null then 10041002 else 10041001 end isRestart,
		        t.created_at,t.updated_at,tt.audit_time,tt.is_audit,tt.audit_result
		        FROM tt_goodwill_apply_info t
		         left join tt_goodwill_apply_audit_info tt on t.id=tt.goodwill_apply_id
		         left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801005  group by goodwill_apply_id ) t
			left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) ttttt on  t.id=ttttt.goodwill_apply_id
		         left join (
		         	select ttt.audit_id,group_concat(ttt.audit_way) auditWay  from tt_goodwill_apply_info t
					left join tt_goodwill_apply_audit_info tt on t.id=tt.goodwill_apply_id
					left join tt_goodwill_apply_audit_detail ttt on tt.id=ttt.audit_id
					where 1=1 group by ttt.audit_id
				)ttt on tt.id=ttt.audit_id

		        WHERE 1=1 and t.goodwill_status in (82551007,82551008,82551009,82551010,82551011,82551012,82551013,82551014,82551015)

	            <if test=" params.dealerCode !=null and params.dealerCode != '' ">
	                AND t.dealer_code = #{params.dealerCode}
	            </if>
	            <if test=" params.blocId !=null and params.blocId != '' ">
	                AND t.bloc_id = #{params.blocId}
	            </if>
	            <if test=" params.areaManageId !=null and params.areaManageId != '' ">
	                AND t.area_Manage_Id = #{params.areaManageId}
	            </if>
	            <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
	                AND t.goodwill_nature = #{params.goodwillNature}
	            </if>
	            <if test=" params.applyPerson !=null and params.applyPerson != '' ">
	                AND t.apply_person = #{params.applyPerson}
	            </if>
	            <if test=" params.id !=null and params.id != '' ">
	                AND t.id = #{params.id}
	            </if>
	            <if test=" params.applyNo !=null and params.applyNo != '' ">
	                AND t.apply_no like CONCAT('%',#{params.applyNo},'%')
	            </if>
	            <if test=" params.customerName !=null and params.customerName != '' ">
	                AND t.customer_name like CONCAT('%',#{params.customerName},'%')
	            </if>
	                    <if test=" params.customerMobile !=null and params.customerMobile != '' ">
	                AND t.customer_mobile = #{params.customerMobile}
	            </if>
	             <if test=" params.vin !=null and params.vin != '' ">
	                AND t.vin like CONCAT('%',#{params.vin},'%')
	            </if>
	                    <if test=" params.license !=null and params.license != '' ">
	                AND t.license = #{params.license}
	            </if>
	                    <if test=" params.complaintId !=null and params.complaintId != '' ">
	                AND t.complaint_id = #{params.complaintId}
	            </if>
	              <if test=" params.complaintFalut !=null and params.complaintFalut != '' ">
	                AND t.complaint_falut = #{params.complaintFalut}
	            </if>

	            <if test=" params.goodwillStatus !=null and params.goodwillStatus != '' ">
	                AND t.goodwill_status = #{params.goodwillStatus}
	            </if>

	            <if test=" params.applyStartTime !=null  ">
	                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.applyStartTime }, '%Y-%m-%d')   ]]>
	            </if>
	            <if test=" params.applyEndTime !=null  ">
	                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.applyEndTime }, '%Y-%m-%d')   ]]>
	            </if>
	                    <if test=" params.applyStartAmount !=null and params.applyStartAmount != '' ">
	                AND <![CDATA[ t.apply_amount>= ]]>  #{params.applyStartAmount}
	            </if>
	                    <if test=" params.applyEndAmount !=null and params.applyEndAmount != '' ">
	                AND <![CDATA[ t.apply_amount<= ]]> #{params.applyEndAmount}
	            </if>

	            <if test=" params.passStartTime !=null  ">
	                <![CDATA[   and DATE_FORMAT(t.material_pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
	            </if>
	            <if test=" params.passEndTime !=null  ">
	                <![CDATA[   and DATE_FORMAT(t.material_pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
	            </if>

	            <if test=" params.auditStartTime !=null  ">
	                <![CDATA[   and DATE_FORMAT(tt.audit_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.auditStartTime }, '%Y-%m-%d')   ]]>
	            </if>
	            <if test=" params.auditEndTime !=null  ">
	                <![CDATA[   and DATE_FORMAT(tt.audit_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.auditEndTime }, '%Y-%m-%d')   ]]>
	            </if>

	          <if test=" params.isAudit !=null and params.isAudit != '' ">
	                AND tt.is_audit = #{params.isAudit}
	            </if>
	            <if test=" params.auditResult !=null and params.auditResult != '' ">
	                AND tt.audit_result = #{params.auditResult}
	            </if>
	            <if test=" params.auditWay !=null and params.auditWay != '' ">
	                AND ttt.auditWay  like CONCAT('%',#{params.auditWay},'%')
	            </if>

	            <if test=" params.updatedStartAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')>=  DATE_FORMAT(#{params.updatedStartAt }, '%Y-%m-%d')   ]]>
	            </if>
	            <if test=" params.updatedEndAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')<=  DATE_FORMAT(#{params.updatedEndAt }, '%Y-%m-%d')   ]]>
	            </if>
	             group by t.id     order by apply_time desc
				<!-- and s.updated_by = #{params.userId} -->
            </select>

          	<select id="queryNoticeInvoiceInfo" resultType="java.util.HashMap" >
        		select  id,
				case when invoice_object=0 then '开票/充值通知'
				when invoice_object=1 then concat('亲善券开票通知',substring(invoice_id, 16)) end applyNo,
				notice_invoice_date noticeTime,notice_invoice_price noticePrice
				 from  tt_goodwill_notice_invoice_info
				where goodwill_apply_id=#{goodwillApplyId}

      		</select>
		    <select id="queryNoticeInvoiceCount" resultType="integer" >
		       select  count(id)
		       from  tt_goodwill_notice_invoice_info
				where goodwill_apply_id=#{goodwillApplyId}
	       </select>

           <select id="queryPrintInfoByCcm" resultType="java.util.HashMap" >
               select
                   t.audit_name auditName,
                   DATE_FORMAT(t.audit_time, '%Y-%m-%d') auditTime,
                   t.goodwill_apply_id
               from
                   tt_goodwill_audit_info t
                       right join (
                       select
                           max(o.id) id
                       from
                           tt_goodwill_audit_info o
                               join tt_goodwill_notice_invoice_info f on o.goodwill_apply_id = f.goodwill_apply_id
                       where
                           f.id = #{noticeInvoiceId}
                         and o.audit_object = 1
                         and o.audit_role = 'CCMZJ'
                         and o.audit_result = 82801001
                       group by
                           o.goodwill_apply_id
                   ) tt on t.id = tt.id
           </select>

	       <select id="queryPrintInfo" resultType="java.util.HashMap" >
               select ta.*,
                      tg.approval,
                      tg.cfo,
                      tg.coo,
                      tg.cs_vp,
                      tg.date_six,
                      tg.date_five,
                      tg.date_four,
                      tg.date_three,
                      tg.date_two,
                      tg.date_one,
                      tg.explain_two,
                      tg.explain_one,
                      tg.line_director,
                      tg.md,
                      tg.requestor,
                      DATE_FORMAT(now(), '%Y-%m-%d') nowTime
               from (select ti.id,
                            ti.invoice_id,
                            format(ti.notice_invoice_price, 2)                    notice_invoice_price,
                            t.vin,
                            t.model,
                            concat(t.mileage, 'Km')                               mileage,
                            DATE_FORMAT(t.complaint_date, '%Y-%m-%d')             complaint_date,
                            DATE_FORMAT(t.buy_car_date, '%Y-%m-%d')               buy_car_date,
                            t.license,
                            DATE_FORMAT(t.warranty_start_date, '%Y-%m-%d')        warranty_start_date,
                            DATE_FORMAT(t.extend_warranty_start_date, '%Y-%m-%d') extend_warranty_start_date,
                            DATE_FORMAT(t.extend_warranty_end_date, '%Y-%m-%d')   extend_warranty_end_date,
                            tt.reason_and_dispose,
                            tt.repair_solution,
                            tt.customer_require,
                            tt.business_goodwill_apply_detail,
                            case
                                when t.is_extend_warranty = '10041001' then '是'
                                when t.is_extend_warranty = '10041002' then '否'
                                else ''
                                end                                               is_extend_warranty,
                            t1.audit_name                                         auditName1,
                            DATE_FORMAT(t1.audit_time, '%Y-%m-%d')                auditTime1,
                            t2.audit_name                                         auditName2,
                            DATE_FORMAT(t2.audit_time, '%Y-%m-%d')                auditTime2,
                            t3.audit_name                                         auditName3,
                            DATE_FORMAT(t3.audit_time, '%Y-%m-%d')                auditTime3,
                            t4.audit_name                                         auditName4,
                            DATE_FORMAT(t4.audit_time, '%Y-%m-%d')                auditTime4,
                            t5.audit_name                                         auditName5,
                            DATE_FORMAT(t5.audit_time, '%Y-%m-%d')                auditTime5,
                            t6.audit_name                                         auditName6,
                            DATE_FORMAT(t6.audit_time, '%Y-%m-%d')                auditTime6,
                            t7.audit_name                                         auditName7,
                            DATE_FORMAT(t7.audit_time, '%Y-%m-%d')                auditTime7,
                            t8.audit_name                                         auditName8,
                            DATE_FORMAT(t8.audit_time, '%Y-%m-%d')                auditTime8
                     from tt_goodwill_notice_invoice_info ti
                              LEFT JOIN tt_goodwill_apply_info t ON ti.goodwill_apply_id = t.id
                              left join tt_goodwill_material_audit tt on t.id = tt.goodwill_apply_id
                              left join(select t.audit_name, t.audit_time, t.goodwill_apply_id
                                        from tt_goodwill_audit_info t
                                                 right join(select max(o.id) id
                                                            from tt_goodwill_audit_info o
                                                                     join tt_goodwill_notice_invoice_info f
                                                                          on o.goodwill_apply_id = f.goodwill_apply_id
                                                            where f.id = #{noticeInvoiceId}
                                                              and o.audit_object = 1
                                                              and o.audit_role = 'SHQYJL'
                                                              and o.audit_result = 82801001
                                                            group by o.goodwill_apply_id) tt on t.id = tt.id) t1
                                       on t.id = t1.goodwill_apply_id
                              left join(select t.audit_name,
                                               t.audit_time,
                                               t.goodwill_apply_id
                                        from tt_goodwill_audit_info t
                                                 right join(select max(o.id) id
                                                            from tt_goodwill_audit_info o
                                                                     join tt_goodwill_notice_invoice_info f
                                                                          on o.goodwill_apply_id = f.goodwill_apply_id
                                                            where f.id = #{noticeInvoiceId}
                                                              and o.audit_object = 1
                                                              and o.audit_role = 'SHDQJL'
                                                              and o.audit_result = 82801001
                                                            group by o.goodwill_apply_id) tt on t.id = tt.id) t2
                                       on t.id = t2.goodwill_apply_id
                              left join(select t.audit_name,
                                               t.audit_time,
                                               t.goodwill_apply_id
                                        from tt_goodwill_audit_info t
                                                 right join(select max(o.id) id
                                                            from tt_goodwill_audit_info o
                                                                     join tt_goodwill_notice_invoice_info f
                                                                          on o.goodwill_apply_id = f.goodwill_apply_id
                                                            where f.id = #{noticeInvoiceId}
                                                              and o.audit_object = 1
                                                              and o.audit_role = 'SHQYZJ'
                                                              and o.audit_result = 82801001
                                                            group by o.goodwill_apply_id) tt on t.id = tt.id) t3
                                       on t.id = t3.goodwill_apply_id
                              left join(select t.audit_name,
                                               t.audit_time,
                                               t.goodwill_apply_id
                                        from tt_goodwill_audit_info t
                                                 right join(select max(o.id) id
                                                            from tt_goodwill_audit_info o
                                                                     join tt_goodwill_notice_invoice_info f
                                                                          on o.goodwill_apply_id = f.goodwill_apply_id
                                                            where f.id = #{noticeInvoiceId}
                                                              and o.audit_object = 1
                                                              and o.audit_role = 'VP'
                                                              and o.audit_result = 82801001
                                                            group by o.goodwill_apply_id) tt on t.id = tt.id) t4
                                       on t.id = t4.goodwill_apply_id
                              left join(select t.audit_name,
                                               t.audit_time,
                                               t.goodwill_apply_id
                                        from tt_goodwill_audit_info t
                                                 right join(select max(o.id) id
                                                            from tt_goodwill_audit_info o
                                                                     join tt_goodwill_notice_invoice_info f
                                                                          on o.goodwill_apply_id = f.goodwill_apply_id
                                                            where f.id = #{noticeInvoiceId}
                                                              and o.audit_object = 1
                                                              and o.audit_role = 'OEM-CWJL'
                                                              and o.audit_result = 82801001
                                                            group by o.goodwill_apply_id) tt on t.id = tt.id) t5
                                       on t.id = t5.goodwill_apply_id
                              left join(select t.audit_name,
                                               t.audit_time,
                                               t.goodwill_apply_id
                                        from tt_goodwill_audit_info t
                                                 right join(select max(o.id) id
                                                            from tt_goodwill_audit_info o
                                                                     join tt_goodwill_notice_invoice_info f
                                                                          on o.goodwill_apply_id = f.goodwill_apply_id
                                                            where f.id = #{noticeInvoiceId}
                                                              and o.audit_object = 1
                                                              and o.audit_role = 'CFO'
                                                              and o.audit_result = 82801001
                                                            group by o.goodwill_apply_id) tt on t.id = tt.id) t6
                                       on t.id = t6.goodwill_apply_id
                              left join(select tt.id,
                                               t.audit_name,
                                               t.audit_time,
                                               t.goodwill_apply_id
                                        from tt_goodwill_audit_info t
                                                 right join(select max(o.id) id
                                                            from tt_goodwill_audit_info o
                                                                     join tt_goodwill_notice_invoice_info f
                                                                          on o.goodwill_apply_id = f.goodwill_apply_id
                                                            where f.id = #{noticeInvoiceId}
                                                              and o.audit_object = 1
                                                              and o.audit_role = 'CEO'
                                                              and o.audit_result = 82801001
                                                            group by o.goodwill_apply_id) tt on t.id = tt.id) t7
                                       on t.id = t7.goodwill_apply_id
                              left join(select tt.id,
                                               t.audit_name,
                                               t.audit_time,
                                               t.goodwill_apply_id
                                        from tt_goodwill_audit_info t
                                                 right join(select max(o.id) id
                                                            from tt_goodwill_audit_info o
                                                                     join tt_goodwill_notice_invoice_info f
                                                                          on o.goodwill_apply_id = f.goodwill_apply_id
                                                            where f.id = #{noticeInvoiceId}
                                                              and o.audit_object = 1
                                                              and o.audit_role = 'CCMQ'
                                                              and o.audit_result = 82801001
                                                            group by o.goodwill_apply_id) tt on t.id = tt.id) t8
                                       on t.id = t8.goodwill_apply_id
                     where ti.id = #{noticeInvoiceId}
                     group by ti.invoice_id
               ) ta
  left join tm_goodwill_awa_print_maintain tg on 1=1
      		</select>

      		<select id="queryRechargeInfo" resultType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillStampsPO" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillStampsDTO">
      		select aa.*  from (
      			SELECT
                tau.audit_name ,
      			tt.id goodwillApplyId,
      			t.id noticeId,
      			tt.coupon_id couponId,
      			tt.bloc,
      			tt.bloc_id,
				tt.area_Manage_Id,
                tt.small_area_Id,
                tt.area_Manage areaManager,
				tt.area_Manage areaManage,
                tt.small_area smallArea,
				tt.dealer_code,
				tt.apply_no applyNo,
				tt.dealer_name dealerName,
				tt.customer_name veOwnerName,
				tt.license,
				tt.vin,
				t.voucher_status voucher_status,
				'' credits_status,
				t.notice_invoice_date noticeDate,
				t.voucher_recharge_price noticeAmount,
				'代金券' resourceType,t.voucher_time rechargeDate,t.voucher_recharge_price rechargeAmount,
                t.voucher_coupon_face_recharge_price  voucherCouponFaceRechargePrice,tt.cost_rate,
				IFNULL(tttt.invoicedPrice,0) invoicedAmount,
				IFNULL(ttt.invoice_price,0) voucherInvoiceAmount,
				IFNULL(t.voucher_recharge_price,0)-IFNULL(ttt.invoice_price,0)	 voucherNotInvoiceAmount
				FROM
				tt_goodwill_notice_invoice_info t
				LEFT JOIN tt_goodwill_apply_info tt ON t.goodwill_apply_id = tt.id
				left join (select sum(notice_invoice_price) invoicedPrice,goodwill_apply_id from tt_goodwill_notice_invoice_info where voucher_type=1 group by goodwill_apply_id) tttt on tt.id=tttt.goodwill_apply_id
				left join (select sum(invoice_price) invoice_price,goodwill_apply_id from  tt_goodwill_invoice_record where CHAR_LENGTH(invoice_id)>14 group by goodwill_apply_id) ttt on tt.id=ttt.goodwill_apply_id
                left join   tt_goodwill_audit_info tau  on  t.id=tau.goodwill_apply_id and  tau.audit_object =0 and tau.is_deleted =0 and tau.audit_role= 'SHQYJL'
				where t.voucher_status=0 and t.voucher_type=0 group by t.invoice_id
				UNION ALL
				SELECT
                tau.audit_name ,
				tt.id goodwillApplyId,
				t.id noticeId,
				'' couponId,
				tt.bloc,
				tt.bloc_id,
				tt.area_Manage_Id,
                tt.small_area_Id,
                tt.area_Manage areaManager,
                tt.area_Manage areaManage,
                tt.small_area smallArea,
				tt.dealer_code,
				tt.apply_no applyNo,
				tt.dealer_name dealerName,
				tt.customer_name veOwnerName,
				tt.license,
				tt.vin,
				'' voucher_status,
				t.credits_status,
				t.notice_invoice_date noticeDate,
				t.volvo_credits_recharge_price noticeAmount,
				'积分' resourceType,t.credits_time rechargeDate,
				t.volvo_credits_recharge_price rechargeAmount,
                t.voucher_coupon_face_recharge_price  voucherCouponFaceRechargePrice,tt.cost_rate,
				'' invoicedAmount,
				'' voucherInvoiceAmount,
				'' voucherNotInvoiceAmount
				FROM
				tt_goodwill_notice_invoice_info t
				LEFT JOIN tt_goodwill_apply_info tt ON t.goodwill_apply_id = tt.id
                left join   tt_goodwill_audit_info tau  on  t.id=tau.goodwill_apply_id and  tau.audit_object =0 and tau.is_deleted =0 and tau.audit_role= 'SHQYJL'
                where t.credits_status=0 and t.voucher_type=0 group by t.invoice_id) aa
				where 1=1
				 <if test=" params.group !=null and params.group != '' ">
	                AND aa.bloc_id = #{params.group}
	            </if>
	            <if test=" params.areaManage !=null and params.areaManage != '' ">
	                AND aa.area_Manage_Id IN (${params.areaManage})
	            </if>
	            <if test=" params.smallArea !=null and params.smallArea != '' ">
	                AND aa.small_area_id IN (${params.smallArea})
	            </if>
	            <if test=" params.auditName !=null and params.auditName != '' ">
	                AND aa.audit_name  = #{params.auditName}
	            </if>
                <if test=" params.auditName1 !=null and params.auditName1.size > 0 ">
                    AND aa.audit_name in
                    <foreach collection="params.auditName1" item="audit_name" open="(" separator="," close=")">
                        #{audit_name}
                    </foreach>
                </if>
	            <if test=" params.dealerCode !=null and params.dealerCode != '' and params.dataType!=10461001">
                	AND (aa.dealer_code  like CONCAT('%',#{params.dealerCode},'%') or aa.dealerName  like CONCAT('%',#{params.dealerCode},'%'))
	            </if>
	            <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461001 ">
	                AND aa.dealer_code = #{params.dealerCode}
	            </if>
	            <if test=" params.applyNo !=null and params.applyNo != '' ">
	                AND aa.applyNo like CONCAT('%',#{params.applyNo},'%')
	            </if>
	            <if test=" params.veOwnerName !=null and params.veOwnerName != '' ">
	                AND aa.veOwnerName like CONCAT('%',#{params.veOwnerName},'%')
	            </if>
	             <if test=" params.vin !=null and params.vin != '' ">
	                AND aa.vin like CONCAT('%',#{params.vin},'%')
	            </if>
	             <if test=" params.license !=null and params.license != '' ">
	                AND aa.license like CONCAT('%',#{params.license},'%')
	            </if>
	            <if test=" params.isChargerd !=null and params.isChargerd != '' ">
	                <if test=" params.isChargerd ==10041001 ">
	                and (aa.voucher_status='0' or aa.credits_status='0')
	            	</if>
	            	<if test=" params.isChargerd ==10041002 ">
	                	and (aa.voucher_status='1' or aa.credits_status='1')
	            	</if>
	            </if>
	            <if test=" params.noticeStartdAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(aa.noticeDate, '%Y-%m-%d')>=  DATE_FORMAT(#{params.noticeStartdAt }, '%Y-%m-%d')   ]]>
	            </if>
	            <if test=" params.noticeEndAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(aa.noticeDate, '%Y-%m-%d')<=  DATE_FORMAT(#{params.noticeEndAt }, '%Y-%m-%d')   ]]>
	            </if>

	            <if test=" params.rechargeStartdAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(aa.rechargeDate, '%Y-%m-%d')>=  DATE_FORMAT(#{params.rechargeStartdAt }, '%Y-%m-%d')   ]]>
	            </if>
	            <if test=" params.rechargeEndAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(aa.rechargeDate, '%Y-%m-%d')<=  DATE_FORMAT(#{params.rechargeEndAt }, '%Y-%m-%d')   ]]>
	            </if>
	            order by aa.rechargeDate desc
      		</select>

      		<select id="queryRechargeExport" resultType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillStampsDTO" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillStampsDTO">
      		select aa.*  from (
      			SELECT
                tau.audit_name ,
      			tt.id goodwillApplyId,
				t.id noticeId,
				tt.coupon_id couponId,
      			tt.bloc,
      			tt.bloc_id,
				tt.area_Manage_Id,
                tt.small_area_id,
				tt.area_Manage areaManage,
				tt.small_area smallArea,
				tt.dealer_code,
				tt.apply_no applyNo,
				tt.dealer_name dealerName,
				tt.customer_name veOwnerName,
				tt.license,
				tt.vin,
				t.voucher_status,
				'' credits_status,
				t.notice_invoice_date noticeDate,
				t.voucher_recharge_price noticeAmount,tt.cost_rate,
				'代金券' resourceType,t.voucher_time rechargeDate,t.voucher_recharge_price rechargeAmount,
				IFNULL(tttt.invoicedPrice,0) invoicedAmount,
				IFNULL(ttt.invoice_price,0) voucherInvoiceAmount,
				IFNULL(t.voucher_recharge_price,0)-IFNULL(ttt.invoice_price,0)	 voucherNotInvoiceAmount,t.voucher_coupon_face_recharge_price as voucherCouponFaceRechargePrice
				FROM
				tt_goodwill_notice_invoice_info t
				LEFT JOIN tt_goodwill_apply_info tt ON t.goodwill_apply_id = tt.id
				left join (select sum(notice_invoice_price) invoicedPrice,goodwill_apply_id from tt_goodwill_notice_invoice_info where voucher_type=1 group by goodwill_apply_id) tttt on tt.id=tttt.goodwill_apply_id
				left join (select sum(invoice_price) invoice_price,goodwill_apply_id from  tt_goodwill_invoice_record where CHAR_LENGTH(invoice_id)>14 group by goodwill_apply_id) ttt on tt.id=ttt.goodwill_apply_id
                left join   tt_goodwill_audit_info tau  on  t.id=tau.goodwill_apply_id and  tau.audit_object =0 and tau.is_deleted =0 and tau.audit_role= 'SHQYJL'
				where t.voucher_status=0 and t.voucher_type=0 group by t.invoice_id
				UNION ALL
				SELECT
                tau.audit_name ,
				tt.id goodwillApplyId,
				t.id noticeId,
				'' couponId,
				tt.bloc,
				tt.bloc_id,
				tt.area_Manage_Id,
                tt.small_area_id,
                tt.area_Manage areaManage,
                tt.small_area smallArea,
				tt.dealer_code,
				tt.apply_no applyNo,
				tt.dealer_name dealerName,
				tt.customer_name veOwnerName,
				tt.license,
				tt.vin,
				'' voucher_status,
				t.credits_status,
				t.notice_invoice_date noticeDate,
				t.volvo_credits_recharge_price noticeAmount,tt.cost_rate,
				'积分' resourceType,t.credits_time rechargeDate,
				t.volvo_credits_recharge_price rechargeAmount,
				'' invoicedAmount,
				'' voucherInvoiceAmount,
				'' voucherNotInvoiceAmount,t.voucher_coupon_face_recharge_price as voucherCouponFaceRechargePrice
				FROM
				tt_goodwill_notice_invoice_info t
				LEFT JOIN tt_goodwill_apply_info tt ON t.goodwill_apply_id = tt.id
                left join   tt_goodwill_audit_info tau  on  t.id=tau.goodwill_apply_id and  tau.audit_object =0 and tau.is_deleted =0 and tau.audit_role= 'SHQYJL'
				where t.credits_status=0 and t.voucher_type=0 group by t.invoice_id) aa
				where 1=1
				  <if test=" params.group !=null and params.group != '' ">
	                AND aa.bloc_id = #{params.group}
	            </if>
	            <if test=" params.areaManage !=null and params.areaManage != '' ">
	                AND aa.area_Manage_Id IN (${params.areaManage})
	            </if>
	            <if test=" params.smallArea !=null and params.smallArea != '' ">
	                AND aa.small_area_id IN (${params.smallArea})
	            </if>
                <if test=" params.auditName1 !=null and params.auditName1.size > 0 ">
                    AND aa.audit_name in
                    <foreach collection="params.auditName1" item="audit_name" open="(" separator="," close=")">
                        #{audit_name}
                    </foreach>
                </if>
                <if test=" params.auditName !=null and params.auditName != '' ">
                    AND aa.audit_name  = #{params.auditName}
                </if>
	            <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                	AND (aa.dealer_code  like CONCAT('%',#{params.dealerCode},'%') or aa.dealerName  like CONCAT('%',#{params.dealerCode},'%'))
	            </if>
	            <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461001 ">
	                AND aa.dealer_code = #{params.dealerCode}
	            </if>
	            <if test=" params.applyNo !=null and params.applyNo != '' ">
	                AND aa.applyNo like CONCAT('%',#{params.applyNo},'%')
	            </if>
	            <if test=" params.veOwnerName !=null and params.veOwnerName != '' ">
	                AND aa.veOwnerName like CONCAT('%',#{params.veOwnerName},'%')
	            </if>
	             <if test=" params.vin !=null and params.vin != '' ">
	                AND aa.vin like CONCAT('%',#{params.vin},'%')
	            </if>
	             <if test=" params.isChargerd !=null and params.isChargerd != '' ">
	                <if test=" params.isChargerd ==10041001 ">
	                and (aa.voucher_status='0' or aa.credits_status='0')
	            	</if>
	            	<if test=" params.isChargerd ==10041002 ">
	                	and (aa.voucher_status='1' or aa.credits_status='1')
	            	</if>
	            </if>
	            <if test=" params.license !=null and params.license != '' ">
	                AND aa.license like CONCAT('%',#{params.license},'%')
	            </if>
	             <if test=" params.noticeStartdAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(aa.noticeDate, '%Y-%m-%d')>=  DATE_FORMAT(#{params.noticeStartdAt }, '%Y-%m-%d')   ]]>
	            </if>
	            <if test=" params.noticeEndAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(aa.noticeDate, '%Y-%m-%d')<=  DATE_FORMAT(#{params.noticeEndAt }, '%Y-%m-%d')   ]]>
	            </if>

	            <if test=" params.rechargeStartdAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(aa.rechargeDate, '%Y-%m-%d')>=  DATE_FORMAT(#{params.rechargeStartdAt }, '%Y-%m-%d')   ]]>
	            </if>
	            <if test=" params.rechargeEndAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(aa.rechargeDate, '%Y-%m-%d')<=  DATE_FORMAT(#{params.rechargeEndAt }, '%Y-%m-%d')   ]]>
	            </if>
	            order by aa.rechargeDate desc
      		</select>
      		<select id="queryExtendWarrantyByVin" resultType="map">
		       select  product_name,effective_date,expire_date
				from tt_extended_warranty_purchase_give  where vin=#{vin} order by effective_date desc limit 1

       		</select>
  			<select id="queryInvoiceHistory" resultType="java.util.HashMap" >
        		 SELECT
		        t.id, t.goodwill_apply_id goodwillApplyId, tt.id noticeInvoiceId, t.invoice_no invoiceNo,
		        t.invoice_price invoicePrice, t.invoice_date invoiceDate, t.invoice_type invoiceType, t.express_company expressCompany,
		        t.express_no expressNo, t.express_date expressDate, t.received_invoice_date receivedInvoiceDate,
		        t.received_invoice_price receivedInvoicePrice, t.is_valid isValid, t.is_deleted isDeleted,
		        tt.notice_invoice_date noticeInvoiceDate,tt.is_commit isCommit, tt.invoice_id invoiceId ,tt.is_confirm isConfirm
		        FROM tt_goodwill_invoice_record t
		        left join tt_goodwill_notice_invoice_info tt on t.invoice_id=tt.invoice_id
		        WHERE 1=1 and tt.invoice_object='1'
             	AND tt.goodwill_apply_id = #{goodwillApplyId}

      		</select>
		    <select id="queryInvoiceHistoryCount" resultType="integer" >
		       select  count(t.id)
		        FROM tt_goodwill_invoice_record t
		        left join tt_goodwill_notice_invoice_info tt on t.invoice_id=tt.invoice_id
		        WHERE 1=1 and tt.invoice_object='1'
             	AND tt.goodwill_apply_id = #{goodwillApplyId}
	       </select>
      		 <select id="exportSupportApplyOemSearchInfo" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO">
        	SELECT
                 tau.auditor,tau.audit_name auditName,
	        t.APP_ID,t.OWNER_CODE,t.OWNER_PAR_CODE,t.ORG_ID,t.id,t.apply_no,t.audit_type auditPart,
	        t.dealer_code,t.dealer_name,t.small_area_id,t.small_area,t.area_manage_id,t.area_manage,t.bloc,t.updated_person,
	        t.bloc_id, t.audit_type,t.goodwill_nature,t.apply_time,t.pass_time,t.material_commit_time,
	        t.complaint_falut,t.complaint_id,t.complaint_date,t.vehicle_use,t.sales_dealer,t.material_pass_time,
	        t.apply_amount,t.audit_amount,t.settlement_amount,t.invoice_amount,
	        t.goodwill_status,t.customer_pain,t.vin,t.license,t.customer_name,
	        t.customer_mobile,t.mileage,t.model,t.buy_car_date,t.warranty_start_date,
	        t.is_extend_warranty,t.extend_warranty_name,t.extend_warranty_start_date,
	        t.extend_warranty_end_date,t.maintain_cost,t.extend_warranty_cost,
	        t.accessory_cost,t.voucher_cost,t.walking_car_price,t.volvo_integral,
	        t.return_change_car_price,t.other_price,t.cost_total,t.customer_pay,
	        t.dealer_undertake,t.volvo_support_goodwill_amount,t.remark,t.apply_file,
	        t.cost_statistics_file,t.cost_screenshot_file,t.ring_check_file,
	        t.trouble_repair_requisition_file,t.work_order_file,t.situation_settlement_agreement_file,
	        t.supplementary_material_file,t.management_review_email_vp_file,
	        t.management_review_email_ceo_file,t.cost_update_file,t.vcdc_else_file,
	        t.customer_identification,t.else_file,t.commit_time,t.is_valid,t.is_deleted,t.coupon_id,
	        ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(IFNULL(t.material_pass_time,NOW()), '%Y-%m-%d %H:%i:%S'))/24,1) materialAuditLength,
      	    IFNULL(ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'))/24,1),'') materialCommitLength,
	        t.created_at,t.updated_at,tt.audit_time newAuditDate,tt.audit_result newAuditResult,tt.audit_name newestAuditor,tt.audit_opinion newAuditOpinion,
			ttt.audit_time unSupportDate,ttt.audit_opinion unSupportReason,
			tttt.audit_time refuseSupportDate,tttt.audit_opinion refuseSupportReason,
			ttttt.audit_time restartDate,ttttt.audit_opinion restartReason,
			case when ttttt.id is null then 10041002 else 10041001 end isRestart,tg.notice_invoice_date,tg.voucher_recharge_price rechargeAmount,tg.voucher_coupon_face_recharge_price voucherCouponFaceRechargePrice,t.cost_rate costRate,
			CASE WHEN t.material_commit_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialSummit,
			CASE WHEN t.material_pass_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialAudit,
	        tis.voucherInvoiceAmount ,tgma.reason_and_dispose reasonAndDispose,tgma.repair_solution repairSolution,tgma.business_goodwill_apply_detail businessGoodwillApplyDetail,
			<!-- case when times>0 then t.settlement_amount else '' end as noticeOrInvoicePrice,-->
			case when times>0 then IFNULL(tg.voucher_recharge_price,0)+IFNULL(tg.notice_invoice_price,0)+IFNULL(tg.volvo_credits_recharge_price,0) else '' end as noticeOrInvoicePrice,
			tis.voucherNotInvoiceAmount,tgii.ccmqRejectedTimes,tgi.areaRejectedTimes
	        FROM tt_goodwill_apply_info t
	        left join tt_goodwill_material_audit tgma on t.id=tgma.goodwill_apply_id
	        left join (select count(id) times,goodwill_apply_id from tt_goodwill_notice_invoice_info group by goodwill_apply_id) tin on t.id=tin.goodwill_apply_id
	        left join (select count(id) areaRejectedTimes,goodwill_apply_id  from tt_goodwill_audit_info where audit_result=82801002 and audit_role in ('SHQYJL','SHDQJL','SHQYZJ') and return_to is not null and return_to not in ('SHQYJL','SHDQJL','SHQYZJ','CCMQ','OEM-CWJL','VP','CFO','CEO','CCMGJJL','CCMZJ') group by goodwill_apply_id )tgi on t.id=tgi.goodwill_apply_id
	        left join (select count(id) ccmqRejectedTimes,goodwill_apply_id  from tt_goodwill_audit_info where audit_result=82801002 and  audit_role in ('CCMQ') and return_to is not null and return_to  in ('SHQYJL','SHDQJL') group by goodwill_apply_id )tgii on t.id=tgii.goodwill_apply_id
	        left join (select *  from (select max(id) max_id from tt_goodwill_audit_info  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  )tt on t.id=tt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801003  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) ttt on  t.id=ttt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801004  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) tttt on  t.id=tttt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801005  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) ttttt on  t.id=ttttt.goodwill_apply_id
	       	left join ( select t.id,t.goodwill_apply_id,t.invoice_object,t.notice_invoice_date,t.voucher_recharge_price,t.notice_invoice_price,tt.invoice_date,t.voucher_coupon_face_recharge_price,t.volvo_credits_recharge_price from  tt_goodwill_notice_invoice_info t left join tt_goodwill_invoice_record tt on t.invoice_id=tt.invoice_id where t.invoice_object=0) tg on t.id=tg.goodwill_apply_id
			left join (SELECT t.goodwill_apply_id,IFNULL(ttt.invoice_price,0) voucherInvoiceAmount,
				IFNULL(t.voucher_recharge_price,0)-IFNULL(ttt.invoice_price,0)	 voucherNotInvoiceAmount
				FROM tt_goodwill_notice_invoice_info t
				LEFT JOIN ( SELECT sum( notice_invoice_price ) invoicedPrice, goodwill_apply_id FROM tt_goodwill_notice_invoice_info WHERE voucher_type = 1 GROUP BY goodwill_apply_id ) tttt ON t.goodwill_apply_id = tttt.goodwill_apply_id
				LEFT JOIN ( SELECT sum( invoice_price ) invoice_price, goodwill_apply_id FROM tt_goodwill_invoice_record WHERE CHAR_LENGTH( invoice_id )> 14 GROUP BY goodwill_apply_id ) ttt ON t.goodwill_apply_id = ttt.goodwill_apply_id
				WHERE t.voucher_status = 0 AND t.voucher_type = 0 GROUP BY t.invoice_id) tis on t.id=tis.goodwill_apply_id
                 left join ( select auditor,goodwill_apply_id,audit_name  from   tt_goodwill_audit_info where audit_object =0 and is_deleted =0 and audit_role= 'SHQYJL'  ) tau on t.id=tau.goodwill_apply_id
			WHERE 1=1 and t.goodwill_status not in (82551001)
            <if test=" params.roleList1 !=null and params.roleList1 != ''and params.roleList1=='SHQYJL'">
                AND t.small_area_id=#{params.orgId}
            </if>
             <if test=" params.roleList1 !=null and params.roleList1 != ''and params.roleList1=='SHDQJL'">
                AND t.area_manage_id=#{params.orgId}
            </if>
            <if test=" params.roleList1 !=null and params.roleList1 != ''and params.roleList1=='SHQYZJ'">
                AND t.area_manage_id=#{params.orgId}
            </if>
           <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND (t.dealer_code  like CONCAT('%',#{params.dealerCode},'%') or t.dealer_name  like CONCAT('%',#{params.dealerCode},'%'))
            </if>
            <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461001 ">
                AND t.dealer_code = #{params.ownerCodes}
            </if>
             <if test=" params.blocId !=null and params.blocId != '' ">
	                AND t.bloc_id = #{params.blocId}
	            </if>
	            <if test=" params.areaManageId !=null and params.areaManageId != '' ">
	                AND t.area_Manage_Id = #{params.areaManageId}
	            </if>
                 <if test=" params.smallAreaId !=null and params.smallAreaId != '' ">
                     AND t.small_area_id = #{params.smallAreaId}
                 </if>
                 <if test=" params.auditor !=null and params.auditor != '' ">
                     AND tau.auditor = #{params.auditor}
                 </if>
                 <if test=" params.auditName !=null and params.auditName != '' ">
                     AND tau.audit_name = #{params.auditName}
                 </if>
                     <if test=" params.areaManageId1 !=null and params.areaManageId1 != '' ">
                         AND t.area_Manage_Id IN (${params.areaManageId1})
                     </if>
                     <if test=" params.smallAreaId1 !=null and params.smallAreaId1 != '' ">
                         AND t.small_area_id IN (${params.smallAreaId1})
                     </if>
                     <if test=" params.auditName1 !=null and params.auditName1.size > 0 ">
                         AND tau.audit_name in
                         <foreach collection="params.auditName1" item="audit_name" open="(" separator="," close=")">
                             #{audit_name}
                         </foreach>
                     </if>
            <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
            <if test=" params.applyPerson !=null and params.applyPerson != '' ">
                AND t.apply_person  like CONCAT('%',#{params.applyPerson},'%')
            </if>
            <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
            <if test=" params.applyNo !=null and params.applyNo != '' ">
                AND t.apply_no like CONCAT('%',#{params.applyNo},'%')
            </if>
            <if test=" params.customerName !=null and params.customerName != '' ">
                AND (t.customer_name like CONCAT('%',#{params.customerName},'%') or t.customer_mobile like CONCAT('%',#{params.customerName},'%'))
            </if>
                    <if test=" params.customerMobile !=null and params.customerMobile != '' ">
                AND t.customer_mobile = #{params.customerMobile}
            </if>
             <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin = #{params.vin}
            </if>
            <if test=" params.license !=null and params.license != '' ">
                AND (t.license like CONCAT('%',#{params.license},'%')  or t.vin like CONCAT('%',#{params.license},'%') )
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id  like CONCAT('%',#{params.complaintId},'%')
            </if>
            <if test=" params.complaintFalut1 !=null and params.complaintFalut1.length > 0 ">
                AND t.complaint_falut in
					<foreach collection="params.complaintFalut1" index="index" item="item"
					             open="(" separator="," close=")">
					        #{item}
					    </foreach>
            </if>
            <if test=" params.goodwillStatus !=null and params.goodwillStatus != '' ">
                AND t.goodwill_status = #{params.goodwillStatus}
            </if>
            <if test=" params.auditType !=null ">
                AND t.audit_type = #{params.auditType}
            </if>
            <if test=" params.applyStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.applyStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.applyEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.applyEndTime }, '%Y-%m-%d')   ]]>
            </if>
                    <if test=" params.applyStartAmount !=null and params.applyStartAmount != '' ">
                AND <![CDATA[ t.apply_amount>= ]]>  #{params.applyStartAmount}
            </if>
                    <if test=" params.applyEndAmount !=null and params.applyEndAmount != '' ">
                AND <![CDATA[ t.apply_amount<= ]]> #{params.applyEndAmount}
            </if>
            <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.invoiceStartDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.invoice_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.invoiceStartDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.invoiceEndDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.invoice_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.invoiceEndDate }, '%Y-%m-%d')   ]]>
            </if>

            <if test=" params.noticeInvoiceStartDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.notice_invoice_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.noticeInvoiceStartDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.noticeInvoiceEndDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.notice_invoice_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.noticeInvoiceEndDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.updatedStartAt !=null  ">
                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')>=  DATE_FORMAT(#{params.updatedStartAt }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.updatedEndAt !=null  ">
                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')<=  DATE_FORMAT(#{params.updatedEndAt }, '%Y-%m-%d')   ]]>
            </if>
             group by t.id    order by apply_time desc
           </select>

      		 <select id="exportSupportApplyAuditListInfo" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO">
                 select
                 case
                 when t.goodwill_status IN ( 82551016 , 82551015) then  '已完成'
                 when t2.audit_role = 'SHQYJL' then  '售后区域经理'
                 when t2.audit_role = 'SHDQJL' then  '售后区域高级经理'
                 when t2.audit_role = 'SHQYZJ' then  '区域总监'
                 when t2.audit_role = 'VP' then  'VP'
                 when t2.audit_role = 'CEO' then  'CEO'
                 when t2.audit_role = 'CCMQ' then  'CCMQ'
                 when t2.audit_role = 'CCMGJJL' then  'CCM高级经理'
                 when t2.audit_role = 'OEM-CWJL' then  '财务经理'
                 when t2.audit_role = 'CFO' then  'CFO'
                 else '已完成' end as "auditRole",t.id,t.goodwill_status from dms_manage.tt_goodwill_apply_info t
                 left join dms_manage.tt_goodwil_apply_audit_process t2 on t.id = t2.goodwill_apply_id and t2.audit_status is null
                 left join dms_manage.tt_goodwil_apply_audit_process t3 on t2.goodwill_apply_id = t3.goodwill_apply_id and t3.audit_status is null
                 AND t2.id > t3.id
			WHERE t3.id is null and t.goodwill_status not in (82551001)
            <if test=" params.roleList1 !=null and params.roleList1 != ''and params.roleList1=='SHQYJL'">
                AND t.small_area_id=#{params.orgId}
            </if>
             <if test=" params.roleList1 !=null and params.roleList1 != ''and params.roleList1=='SHDQJL'">
                AND t.area_manage_id=#{params.orgId}
            </if>
            <if test=" params.roleList1 !=null and params.roleList1 != ''and params.roleList1=='SHQYZJ'">
                AND t.area_manage_id=#{params.orgId}
            </if>
           <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND (t.dealer_code  like CONCAT('%',#{params.dealerCode},'%') or t.dealer_name  like CONCAT('%',#{params.dealerCode},'%'))
            </if>
            <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461001 ">
                AND t.dealer_code = #{params.ownerCodes}
            </if>
             <if test=" params.blocId !=null and params.blocId != '' ">
	                AND t.bloc_id = #{params.blocId}
	            </if>
	            <if test=" params.areaManageId !=null and params.areaManageId != '' ">
	                AND t.area_Manage_Id = #{params.areaManageId}
	            </if>
                 <if test=" params.smallAreaId !=null and params.smallAreaId != '' ">
                     AND t.small_area_id = #{params.smallAreaId}
                 </if>
                     <if test=" params.areaManageId1 !=null and params.areaManageId1 != '' ">
                         AND t.area_Manage_Id IN (${params.areaManageId1})
                     </if>
                     <if test=" params.smallAreaId1 !=null and params.smallAreaId1 != '' ">
                         AND t.small_area_id IN (${params.smallAreaId1})
                     </if>
            <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
            <if test=" params.applyPerson !=null and params.applyPerson != '' ">
                AND t.apply_person  like CONCAT('%',#{params.applyPerson},'%')
            </if>
            <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
            <if test=" params.applyNo !=null and params.applyNo != '' ">
                AND t.apply_no like CONCAT('%',#{params.applyNo},'%')
            </if>
            <if test=" params.customerName !=null and params.customerName != '' ">
                AND (t.customer_name like CONCAT('%',#{params.customerName},'%') or t.customer_mobile like CONCAT('%',#{params.customerName},'%'))
            </if>
                    <if test=" params.customerMobile !=null and params.customerMobile != '' ">
                AND t.customer_mobile = #{params.customerMobile}
            </if>
             <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin = #{params.vin}
            </if>
            <if test=" params.license !=null and params.license != '' ">
                AND (t.license like CONCAT('%',#{params.license},'%')  or t.vin like CONCAT('%',#{params.license},'%') )
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id  like CONCAT('%',#{params.complaintId},'%')
            </if>
            <if test=" params.complaintFalut1 !=null and params.complaintFalut1.length > 0 ">
                AND t.complaint_falut in
					<foreach collection="params.complaintFalut1" index="index" item="item"
					             open="(" separator="," close=")">
					        #{item}
					    </foreach>
            </if>
            <if test=" params.goodwillStatus !=null and params.goodwillStatus != '' ">
                AND t.goodwill_status = #{params.goodwillStatus}
            </if>
            <if test=" params.auditType !=null ">
                AND t.audit_type = #{params.auditType}
            </if>
            <if test=" params.applyStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.applyStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.applyEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.applyEndTime }, '%Y-%m-%d')   ]]>
            </if>
                    <if test=" params.applyStartAmount !=null and params.applyStartAmount != '' ">
                AND <![CDATA[ t.apply_amount>= ]]>  #{params.applyStartAmount}
            </if>
                    <if test=" params.applyEndAmount !=null and params.applyEndAmount != '' ">
                AND <![CDATA[ t.apply_amount<= ]]> #{params.applyEndAmount}
            </if>
            <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.updatedStartAt !=null  ">
                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')>=  DATE_FORMAT(#{params.updatedStartAt }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.updatedEndAt !=null  ">
                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')<=  DATE_FORMAT(#{params.updatedEndAt }, '%Y-%m-%d')   ]]>
            </if>
             group by t.id    order by apply_time desc
           </select>

            <select id="exportSupportApplyDealerSearchInfo" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO">
        	SELECT
	        t.APP_ID,t.OWNER_CODE,t.OWNER_PAR_CODE,t.ORG_ID,t.id,t.apply_no,t.audit_type auditPart,
	        t.dealer_code,t.dealer_name,t.small_area_id,t.small_area,t.area_manage_id,t.area_manage,t.bloc,t.updated_person,
	        t.bloc_id,t.audit_type,t.goodwill_nature,t.apply_time,t.pass_time,t.material_commit_time,
	        t.complaint_falut,t.complaint_id,t.complaint_date,t.vehicle_use,t.sales_dealer,t.material_pass_time,
	        t.apply_amount,t.audit_amount,t.settlement_amount,t.invoice_amount,
	        t.goodwill_status,t.customer_pain,t.vin,t.license,t.customer_name,
	        t.customer_mobile,t.mileage,t.model,t.buy_car_date,t.warranty_start_date,
	        t.is_extend_warranty,t.extend_warranty_name,t.extend_warranty_start_date,
	        t.extend_warranty_end_date,t.maintain_cost,t.extend_warranty_cost,
	        t.accessory_cost,t.voucher_cost,t.walking_car_price,t.volvo_integral,
	        t.return_change_car_price,t.other_price,t.cost_total,t.customer_pay,
	        t.dealer_undertake,t.volvo_support_goodwill_amount,t.remark,t.apply_file,
	        t.cost_statistics_file,t.cost_screenshot_file,t.ring_check_file,
	        t.trouble_repair_requisition_file,t.work_order_file,t.situation_settlement_agreement_file,
	        t.supplementary_material_file,t.management_review_email_vp_file,
	        t.management_review_email_ceo_file,t.cost_update_file,t.vcdc_else_file,
	        t.customer_identification,t.else_file,t.commit_time,t.is_valid,t.is_deleted,t.coupon_id,
	        ttt.audit_time unSupportDate,ttt.audit_opinion unSupportReason,
			tttt.audit_time refuseSupportDate,tttt.audit_opinion refuseSupportReason,
			ttttt.audit_time restartDate,ttttt.audit_opinion restartReason,
	        case when ttttt.id is null then 10041002 else 10041001 end isRestart,tg.notice_invoice_date,tg.voucher_recharge_price rechargeAmount,
	        ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(IFNULL(t.material_pass_time,NOW()), '%Y-%m-%d %H:%i:%S'))/24,1) materialAuditLength,
      	    IFNULL(ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'))/24,1),'') materialCommitLength,
	        CASE WHEN t.material_commit_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialSummit,
			CASE WHEN t.material_pass_time IS NULL THEN IFNULL(TIMESTAMPDIFF(DAY,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%S')),'0') else '0' end materialAudit,
	       t.created_at,t.updated_at,tt.audit_time newAuditDate,tt.audit_result newAuditResult,tt.audit_name newestAuditor,tt.audit_opinion newAuditOpinion,
	        tis.voucherInvoiceAmount ,tgma.reason_and_dispose reasonAndDispose,tgma.repair_solution repairSolution,tgma.business_goodwill_apply_detail businessGoodwillApplyDetail,
			tis.voucherNotInvoiceAmount,tgii.ccmqRejectedTimes,tgi.areaRejectedTimes,t.cost_rate costRate,
            case when times>0 then IFNULL(tg.voucher_recharge_price,0)+IFNULL(tg.notice_invoice_price,0)+IFNULL(tg.volvo_credits_recharge_price,0) else '' end as noticeOrInvoicePrice
        FROM tt_goodwill_apply_info t
        left join tt_goodwill_material_audit tgma on t.id=tgma.goodwill_apply_id
        left join (select count(id) times,goodwill_apply_id from tt_goodwill_notice_invoice_info group by goodwill_apply_id) tin on t.id=tin.goodwill_apply_id
        left join (select count(id) areaRejectedTimes,goodwill_apply_id  from tt_goodwill_audit_info where audit_result=82801002 and  audit_role in ('SHQYJL','SHDQJL','SHQYZJ') and return_to is not null and return_to not in ('SHQYJL','SHDQJL','SHQYZJ','CCMQ','OEM-CWJL','VP','CFO','CEO','CCMGJJL','CCMZJ') group by goodwill_apply_id )tgi on t.id=tgi.goodwill_apply_id
        left join (select count(id) ccmqRejectedTimes,goodwill_apply_id  from tt_goodwill_audit_info where audit_result=82801002 and  audit_role in ('CCMQ') and return_to is not null and return_to  in ('SHQYJL','SHDQJL') group by goodwill_apply_id )tgii on t.id=tgii.goodwill_apply_id
        left join (select *  from (select max(id) max_id from tt_goodwill_audit_info  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  )tt on t.id=tt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801003  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) ttt on  t.id=ttt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801004  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) tttt on  t.id=tttt.goodwill_apply_id
			left join (select *  from (select max(id) max_id from tt_goodwill_audit_info where audit_result=82801005  group by goodwill_apply_id ) t
				left join tt_goodwill_audit_info tt on t.max_id=tt.id  ) ttttt on  t.id=ttttt.goodwill_apply_id
			left join ( select t.notice_invoice_price,t.volvo_credits_recharge_price,t.id,t.goodwill_apply_id,t.invoice_object,t.notice_invoice_date,t.voucher_recharge_price from  tt_goodwill_notice_invoice_info t where t.invoice_object=0) tg on t.id=tg.goodwill_apply_id
			left join (SELECT t.goodwill_apply_id,IFNULL(ttt.invoice_price,0) voucherInvoiceAmount,
				IFNULL(t.voucher_recharge_price,0)-IFNULL(ttt.invoice_price,0)	 voucherNotInvoiceAmount
				FROM tt_goodwill_notice_invoice_info t
				LEFT JOIN ( SELECT sum( notice_invoice_price ) invoicedPrice, goodwill_apply_id FROM tt_goodwill_notice_invoice_info WHERE voucher_type = 1 GROUP BY goodwill_apply_id ) tttt ON t.goodwill_apply_id = tttt.goodwill_apply_id
				LEFT JOIN ( SELECT sum( invoice_price ) invoice_price, goodwill_apply_id FROM tt_goodwill_invoice_record WHERE CHAR_LENGTH( invoice_id )> 14 GROUP BY goodwill_apply_id ) ttt ON t.goodwill_apply_id = ttt.goodwill_apply_id
				WHERE t.voucher_status = 0 AND t.voucher_type = 0 GROUP BY t.invoice_id) tis on t.id=tis.goodwill_apply_id
			WHERE 1=1 and t.goodwill_status not in (82551001)

            <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND (t.dealer_code  like CONCAT('%',#{params.dealerCode},'%') or t.dealer_name  like CONCAT('%',#{params.dealerCode},'%'))
            </if>
            <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461001 ">
                AND t.dealer_code = #{params.ownerCodes}
            </if>
            <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
                AND t.goodwill_nature = #{params.goodwillNature}
            </if>
            <if test=" params.applyPerson !=null and params.applyPerson != '' ">
                AND t.apply_person  like CONCAT('%',#{params.applyPerson},'%')
            </if>
            <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
            <if test=" params.applyNo !=null and params.applyNo != '' ">
                AND t.apply_no like CONCAT('%',#{params.applyNo},'%')
            </if>
            <if test=" params.customerName !=null and params.customerName != '' ">
                AND (t.customer_name like CONCAT('%',#{params.customerName},'%') or t.customer_mobile like CONCAT('%',#{params.customerName},'%'))
            </if>
                    <if test=" params.customerMobile !=null and params.customerMobile != '' ">
                AND t.customer_mobile = #{params.customerMobile}
            </if>
             <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin = #{params.vin}
            </if>
            <if test=" params.license !=null and params.license != '' ">
                AND (t.license like CONCAT('%',#{params.license},'%')  or t.vin like CONCAT('%',#{params.license},'%') )
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id  like CONCAT('%',#{params.complaintId},'%')
            </if>
              <if test=" params.complaintFalut !=null and params.complaintFalut != '' ">
                AND t.complaint_falut = #{params.complaintFalut}
            </if>

            <if test=" params.goodwillStatus !=null and params.goodwillStatus != '' ">
                AND t.goodwill_status = #{params.goodwillStatus}
            </if>
            <if test=" params.auditType !=null ">
                AND t.audit_type = #{params.auditType}
            </if>

            <if test=" params.applyStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.applyStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.applyEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.applyEndTime }, '%Y-%m-%d')   ]]>
            </if>
                    <if test=" params.applyStartAmount !=null and params.applyStartAmount != '' ">
                AND <![CDATA[ t.apply_amount>= ]]>  #{params.applyStartAmount}
            </if>
                    <if test=" params.applyEndAmount !=null and params.applyEndAmount != '' ">
                AND <![CDATA[ t.apply_amount<= ]]> #{params.applyEndAmount}
            </if>

            <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>

<!--             <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if>

            <if test=" params.passStartTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.passEndTime !=null  ">
                <![CDATA[   and DATE_FORMAT(t.pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
            </if> -->

            <if test=" params.noticeInvoiceStartDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.notice_invoice_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.noticeInvoiceStartDate }, '%Y-%m-%d')   ]]>
            </if>
            <if test=" params.noticeInvoiceEndDate !=null  ">
                <![CDATA[   and DATE_FORMAT(tg.notice_invoice_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.noticeInvoiceEndDate }, '%Y-%m-%d')   ]]>
            </if>
             group by t.id     order by apply_time desc
			<!-- and s.updated_by = #{params.userId} -->
            </select>
            <select id="exportSupportApplyAuditInfo" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO">
	        	SELECT
		        t.APP_ID,t.OWNER_CODE,t.OWNER_PAR_CODE,t.ORG_ID,t.id,t.apply_no,
		        t.dealer_code,t.dealer_name,t.small_area_id,t.small_area,t.area_manage_id,t.area_manage,t.bloc,t.updated_person,
		        t.bloc_id,t.audit_type,t.goodwill_nature,t.apply_time,t.pass_time,t.material_commit_time,
		        t.complaint_falut,t.complaint_id,t.complaint_date,t.vehicle_use,t.sales_dealer,t.material_pass_time,
		        t.apply_amount,t.audit_amount,t.settlement_amount,t.invoice_amount,
		        t.goodwill_status,t.customer_pain,t.vin,t.license,t.customer_name,
		        t.customer_mobile,t.mileage,t.model,t.buy_car_date,t.warranty_start_date,
		        t.is_extend_warranty,t.extend_warranty_name,t.extend_warranty_start_date,
		        t.extend_warranty_end_date,t.maintain_cost,t.extend_warranty_cost,
		        t.accessory_cost,t.voucher_cost,t.walking_car_price,t.volvo_integral,
		        t.return_change_car_price,t.other_price,t.cost_total,t.customer_pay,
		        t.dealer_undertake,t.volvo_support_goodwill_amount,t.remark,t.apply_file,
		        t.cost_statistics_file,t.cost_screenshot_file,t.ring_check_file,
		        t.trouble_repair_requisition_file,t.work_order_file,t.situation_settlement_agreement_file,
		        t.supplementary_material_file,t.management_review_email_vp_file,
		        t.management_review_email_ceo_file,t.cost_update_file,t.vcdc_else_file,
		        t.customer_identification,t.else_file,t.commit_time,t.is_valid,t.is_deleted,
		        ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(IFNULL(t.material_pass_time,NOW()), '%Y-%m-%d %H:%i:%S'))/24,1) materialAuditLength,
      	    	IFNULL(ROUND(TIMESTAMPDIFF(HOUR,DATE_FORMAT(t.pass_time, '%Y-%m-%d %H:%i:%S'),DATE_FORMAT(t.material_commit_time, '%Y-%m-%d %H:%i:%S'))/24,1),'') materialCommitLength,
		        t.created_at,t.updated_at,tt.audit_time,tt.is_audit,tt.audit_result,ttt.audit_way,ttt.audit_time audit_times,ttt.trouble_spots,ttt.punish_result,ttt.deductions_price,ttt.is_notification
		        FROM tt_goodwill_apply_info t
		         left join tt_goodwill_apply_audit_info tt on t.id=tt.goodwill_apply_id
		         left join tt_goodwill_apply_audit_detail ttt on tt.id=ttt.audit_id
		         <!-- left join (
		         	select ttt.audit_id,group_concat(ttt.audit_way) auditWay  from tt_goodwill_apply_info t
					left join tt_goodwill_apply_audit_info tt on t.id=tt.goodwill_apply_id
					left join tt_goodwill_apply_audit_detail ttt on tt.id=ttt.audit_id
					where 1=1 group by ttt.audit_id
				)ttt on tt.id=ttt.audit_id -->

		        WHERE 1=1 and t.goodwill_status in (82551007,82551008,82551009,82551010,82551011,82551012,82551013,82551014,82551015)

	            <if test=" params.dealerCode !=null and params.dealerCode != '' ">
	                AND t.dealer_code = #{params.dealerCode}
	            </if>
	            <if test=" params.blocId !=null and params.blocId != '' ">
	                AND t.bloc_id = #{params.blocId}
	            </if>
	            <if test=" params.areaManageId !=null and params.areaManageId != '' ">
	                AND t.area_Manage_Id = #{params.areaManageId}
	            </if>
	            <if test=" params.goodwillNature !=null and params.goodwillNature != '' ">
	                AND t.goodwill_nature = #{params.goodwillNature}
	            </if>
	            <if test=" params.applyPerson !=null and params.applyPerson != '' ">
	                AND t.apply_person = #{params.applyPerson}
	            </if>
	            <if test=" params.id !=null and params.id != '' ">
	                AND t.id = #{params.id}
	            </if>
	            <if test=" params.applyNo !=null and params.applyNo != '' ">
	                AND t.apply_no like CONCAT('%',#{params.applyNo},'%')
	            </if>
	            <if test=" params.customerName !=null and params.customerName != '' ">
	                AND t.customer_name like CONCAT('%',#{params.customerName},'%')
	            </if>
	                    <if test=" params.customerMobile !=null and params.customerMobile != '' ">
	                AND t.customer_mobile = #{params.customerMobile}
	            </if>
	             <if test=" params.vin !=null and params.vin != '' ">
	                AND t.vin like CONCAT('%',#{params.vin},'%')
	            </if>
	                    <if test=" params.license !=null and params.license != '' ">
	                AND t.license = #{params.license}
	            </if>
	                    <if test=" params.complaintId !=null and params.complaintId != '' ">
	                AND t.complaint_id = #{params.complaintId}
	            </if>
	              <if test=" params.complaintFalut !=null and params.complaintFalut != '' ">
	                AND t.complaint_falut = #{params.complaintFalut}
	            </if>

	            <if test=" params.goodwillStatus !=null and params.goodwillStatus != '' ">
	                AND t.goodwill_status = #{params.goodwillStatus}
	            </if>

	            <if test=" params.applyStartTime !=null  ">
	                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.applyStartTime }, '%Y-%m-%d')   ]]>
	            </if>
	            <if test=" params.applyEndTime !=null  ">
	                <![CDATA[   and DATE_FORMAT(t.apply_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.applyEndTime }, '%Y-%m-%d')   ]]>
	            </if>
	                    <if test=" params.applyStartAmount !=null and params.applyStartAmount != '' ">
	                AND <![CDATA[ t.apply_amount>= ]]>  #{params.applyStartAmount}
	            </if>
	                    <if test=" params.applyEndAmount !=null and params.applyEndAmount != '' ">
	                AND <![CDATA[ t.apply_amount<= ]]> #{params.applyEndAmount}
	            </if>

	            <if test=" params.passStartTime !=null  ">
	                <![CDATA[   and DATE_FORMAT(t.material_pass_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.passStartTime }, '%Y-%m-%d')   ]]>
	            </if>
	            <if test=" params.passEndTime !=null  ">
	                <![CDATA[   and DATE_FORMAT(t.material_pass_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.passEndTime }, '%Y-%m-%d')   ]]>
	            </if>

	            <if test=" params.auditStartTime !=null  ">
	                <![CDATA[   and DATE_FORMAT(tt.audit_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.auditStartTime }, '%Y-%m-%d')   ]]>
	            </if>
	            <if test=" params.auditEndTime !=null  ">
	                <![CDATA[   and DATE_FORMAT(tt.audit_time, '%Y-%m-%d')<=  DATE_FORMAT(#{params.auditEndTime }, '%Y-%m-%d')   ]]>
	            </if>

	          <if test=" params.isAudit !=null and params.isAudit != '' ">
	                AND tt.is_audit = #{params.isAudit}
	            </if>
	            <if test=" params.auditResult !=null and params.auditResult != '' ">
	                AND tt.audit_result = #{params.auditResult}
	            </if>
	            <if test=" params.auditWay !=null and params.auditWay != '' ">
	                AND ttt.audit_way  = #{params.auditWay}
	            </if>

	            <if test=" params.updatedStartAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')>=  DATE_FORMAT(#{params.updatedStartAt }, '%Y-%m-%d')   ]]>
	            </if>
	            <if test=" params.updatedEndAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(t.updated_at, '%Y-%m-%d')<=  DATE_FORMAT(#{params.updatedEndAt }, '%Y-%m-%d')   ]]>
	            </if>
	               order by apply_time desc
				<!-- and s.updated_by = #{params.userId} -->
            </select>
            <select id="queryConsumeInfo" resultType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillStampsPO" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillStampsDTO">
      		select aa.*  from (
      			SELECT
                tau.audit_name ,
      			tt.id goodwillApplyId,
      			t.id noticeId,
      			tt.coupon_id couponId,
      			tt.bloc,
      			tt.bloc_id,
				tt.area_manage_id,
				tt.area_Manage,
				tt.small_area_id,
				tt.small_area,
				tt.dealer_code,
				tt.apply_no applyNo,
				tt.dealer_name dealerName,
				tt.customer_name veOwnerName,
				tt.license,
				tt.vin,
				tgi.invoice_no invoiceNumber,
				tgi.invoice_date invoiceDate,
				tgi.express_company courierCompany,
				tgi.received_invoice_date receiveDate,
				tgi.express_no courierNumber,
				tgi.express_date deliveryDate,
				tgi.invoice_id,
				t.consume_id consumes,
				t.notice_invoice_date noticeDate,
				t.notice_invoice_price noticeInvoicePrice,
				t.name invoiceTitle,
				tgn.voucher_recharge_price noticeAmount,
				tgn.voucher_time rechargeDate,
				tgn.voucher_recharge_price rechargeAmount,

				IFNULL(ttt.invoice_price,0) voucherInvoiceAmount,
				IFNULL(tgn.voucher_recharge_price,0)-IFNULL(ttt.invoice_price,0)	 voucherNotInvoiceAmount
				FROM   tt_goodwill_notice_invoice_info t
				LEFT JOIN  tt_goodwill_apply_info tt ON t.goodwill_apply_id = tt.id and t.invoice_object='1'
				LEFT JOIN tt_goodwill_notice_invoice_info tgn  ON tgn.goodwill_apply_id = tt.id and tgn.invoice_object='0'
				left join tt_goodwill_invoice_record tgi on t.invoice_id=tgi.invoice_id
                left join   tt_goodwill_audit_info tau  on  tt.id=tau.goodwill_apply_id and  tau.audit_object =0 and tau.is_deleted =0 and tau.audit_role= 'SHQYJL'
				left join (select sum(notice_invoice_price) invoicedPrice,goodwill_apply_id from tt_goodwill_notice_invoice_info where voucher_type=1 group by goodwill_apply_id) tttt on tt.id=tttt.goodwill_apply_id
				left join (select sum(invoice_price) invoice_price,goodwill_apply_id from  tt_goodwill_invoice_record where CHAR_LENGTH(invoice_id)>14 group by goodwill_apply_id) ttt on tt.id=ttt.goodwill_apply_id
				where tt.coupon_id is not null
				) aa
				where 1=1
				<if test=" params.consumes !=null and params.consumes != '' ">
				AND find_in_set(#{params.consumes},aa.consumes)
	            </if>
				<if test=" params.auditName !=null and params.auditName != '' ">
				AND aa.audit_name = #{ params.auditName }
	            </if>
	            order by aa.rechargeDate desc
      		</select>

			<select id="queryConsumeInfoByIds" resultType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillStampsPO" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillStampsDTO">
      		SELECT
      			tau.audit_name ,
      			tt.id goodwillApplyId,
      			t.id noticeId,
      			tt.coupon_id couponId,
      			tt.bloc,
      			tt.bloc_id,
				tt.area_Manage_Id,
				tt.dealer_code,
				tt.apply_no applyNo,
				tt.dealer_name dealerName,
				tt.customer_name veOwnerName,
				tt.license,
				tt.vin,
				tgi.invoice_no invoiceNumber,
				tgi.invoice_date invoiceDate,
				tgi.express_company courierCompany,
				tgi.received_invoice_date receiveDate,
				tgi.express_no courierNumber,
				tgi.express_date deliveryDate,
				tgi.invoice_id,
				t.consume_id consumes,
				t.notice_invoice_date noticeDate,
				t.notice_invoice_price noticeInvoicePrice,
				t.name invoiceTitle,
				tgn.voucher_recharge_price noticeAmount,
				tgn.voucher_time rechargeDate,
				tgn.voucher_recharge_price rechargeAmount,
				IFNULL(ttt.invoice_price,0) voucherInvoiceAmount,
				IFNULL(tgn.voucher_recharge_price,0)-IFNULL(ttt.invoice_price,0)	 voucherNotInvoiceAmount
				FROM   tt_goodwill_notice_invoice_info t
				LEFT JOIN  tt_goodwill_apply_info tt ON t.goodwill_apply_id = tt.id and t.invoice_object='1'
				LEFT JOIN tt_goodwill_notice_invoice_info tgn  ON tgn.goodwill_apply_id = tt.id and tgn.invoice_object='0'
				left join tt_goodwill_invoice_record tgi on t.invoice_id=tgi.invoice_id
                left join   tt_goodwill_audit_info tau  on  tt.id=tau.goodwill_apply_id and  tau.audit_object =0 and tau.is_deleted =0 and tau.audit_role= 'SHQYJL'
				left join (select sum(notice_invoice_price) invoicedPrice,goodwill_apply_id from tt_goodwill_notice_invoice_info where voucher_type=1 group by goodwill_apply_id) tttt on tt.id=tttt.goodwill_apply_id
				left join (select sum(invoice_price) invoice_price,goodwill_apply_id from  tt_goodwill_invoice_record where CHAR_LENGTH(invoice_id)>14 group by goodwill_apply_id) ttt on tt.id=ttt.goodwill_apply_id
				where tt.coupon_id is not null and t.id in
				<foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
					#{item}
		    	</foreach>
		    	<if test=" params.auditName !=null and params.auditName != '' ">
				AND tau.audit_name = #{ params.auditName }
	            </if>

      		</select>

      		<select id="queryCouponId" resultType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillStampsPO" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillStampsDTO">
      		select aa.*  from (
      			SELECT
      			tt.coupon_id couponId,
				FROM  tt_goodwill_apply_info tt
				where tt.coupon_id is not null
				) aa
				where 1=1
				 <if test=" params.group !=null and params.group != '' ">
	                AND aa.bloc_id = #{params.group}
	            </if>
	            <if test=" params.areaManager !=null and params.areaManager != '' ">
	                AND aa.area_Manage_Id = #{params.areaManager}
	            </if>
	            <if test=" params.dealerCode !=null and params.dealerCode != '' and params.dataType!=10461001">
                	AND (aa.dealer_code  like CONCAT('%',#{params.dealerCode},'%') or aa.dealerName  like CONCAT('%',#{params.dealerCode},'%'))
	            </if>
	            <if test=" params.dataType !=null and params.dataType != '' and params.dataType==10461001 ">
	                AND aa.dealer_code = #{params.dealerCode}
	            </if>
	            <if test=" params.applyNo !=null and params.applyNo != '' ">
	                AND aa.applyNo like CONCAT('%',#{params.applyNo},'%')
	            </if>
	            <if test=" params.veOwnerName !=null and params.veOwnerName != '' ">
	                AND aa.veOwnerName like CONCAT('%',#{params.veOwnerName},'%')
	            </if>
	             <if test=" params.vin !=null and params.vin != '' ">
	                AND aa.vin like CONCAT('%',#{params.vin},'%')
	            </if>
	             <if test=" params.license !=null and params.license != '' ">
	                AND aa.license like CONCAT('%',#{params.license},'%')
	            </if>
	            <if test=" params.isChargerd !=null and params.isChargerd != '' ">
	                <if test=" params.isChargerd ==10041001 ">
	                and (aa.voucher_status='0' or aa.credits_status='0')
	            	</if>
	            	<if test=" params.isChargerd ==10041002 ">
	                	and (aa.voucher_status='1' or aa.credits_status='1')
	            	</if>
	            </if>
	            <if test=" params.noticeStartdAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(aa.noticeDate, '%Y-%m-%d')>=  DATE_FORMAT(#{params.noticeStartdAt }, '%Y-%m-%d')   ]]>
	            </if>
	            <if test=" params.noticeEndAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(aa.noticeDate, '%Y-%m-%d')<=  DATE_FORMAT(#{params.noticeEndAt }, '%Y-%m-%d')   ]]>
	            </if>

	            <if test=" params.rechargeStartdAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(aa.rechargeDate, '%Y-%m-%d')>=  DATE_FORMAT(#{params.rechargeStartdAt }, '%Y-%m-%d')   ]]>
	            </if>
	            <if test=" params.rechargeEndAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(aa.rechargeDate, '%Y-%m-%d')<=  DATE_FORMAT(#{params.rechargeEndAt }, '%Y-%m-%d')   ]]>
	            </if>
	            order by aa.rechargeDate desc
      		</select>

      		<select id="findbyCouponId" resultType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO" parameterType="long">
      		SELECT
            DISTINCT
      		tgai.APP_ID appId,
      	    tgai.id, tgai.apply_no applyNo, tgai.dealer_code dealerCode,
      	    tgai.dealer_name dealerName,tgai.area_manage_id areaManageId,tgai.area_manage areaManage,tgai.small_area_id smallAreaId,tgai.small_area smallArea,tgai.bloc_id blocId,tgai.bloc bloc,
      	    tgai.goodwill_nature goodwillNature,
      	   	tgai.goodwill_status goodwillStatus, tgai.customer_pain customerPain, tgai.vin,
      	   	tgai.license license, tgai.customer_name customerName,
      	    tgai.customer_mobile customerMobile, tgai.mileage mileage, tgai.model model, tgai.buy_car_date buyCarDate,
      	    tg.consume_id consumes,
			tg.notice_invoice_date noticeDate,
			tg.voucher_recharge_price rechargeAmount,
            tgai.cost_rate,
			IFNULL(tttt.invoicedPrice,0) noticeInvoicePrice,
			IFNULL(ttt.invoice_price,0) voucherInvoiceAmount,
			IFNULL(tg.voucher_recharge_price,0)-IFNULL(ttt.invoice_price,0)	 voucherNotInvoiceAmount
	      FROM  tt_goodwill_apply_info tgai
	      LEFT JOIN tt_goodwill_notice_invoice_info tg on tgai.ID=tg.goodwill_apply_id and tg.invoice_object='0'
	      left join (select sum(notice_invoice_price) invoicedPrice,count(id) countInvoice,goodwill_apply_id from tt_goodwill_notice_invoice_info where voucher_type=1 and CHAR_LENGTH(invoice_id)>14 group by goodwill_apply_id) tttt on tgai.id=tttt.goodwill_apply_id
		  left join (select sum(invoice_price) invoice_price,goodwill_apply_id from  tt_goodwill_invoice_record where CHAR_LENGTH(invoice_id)>14 group by goodwill_apply_id) ttt on tgai.id=ttt.goodwill_apply_id
          left join   tt_goodwill_audit_info tau  on  tgai.id=tau.goodwill_apply_id and  tau.audit_object =0 and tau.is_deleted =0 and tau.audit_role= 'SHQYJL'
          WHERE 1=1 AND tgai.COUPON_ID=#{couponId}
	       <if test=" params.group !=null and params.group != '' ">
                AND tgai.bloc_id = #{params.group}
            </if>
            <if test=" params.areaManage !=null and params.areaManage != '' ">
                AND tgai.area_Manage_Id IN (${params.areaManage})
            </if>
            <if test=" params.smallArea !=null and params.smallArea != '' ">
                AND tgai.small_area_id IN (${params.smallArea})
            </if>
            <if test=" params.auditName1 !=null and params.auditName1.size > 0 ">
                AND tau.audit_name in
                <foreach collection="params.auditName1" item="audit_name" open="(" separator="," close=")">
                    #{audit_name}
                </foreach>
            </if>
	      <if test=" params.applyNo !=null and params.applyNo != '' ">
	                AND tgai.apply_No like CONCAT('%',#{params.applyNo},'%')
           </if>
           <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND tgai.dealer_code like CONCAT('%',#{params.dealerCode},'%')
           </if>
           <if test=" params.veOwnerName !=null and params.veOwnerName != '' ">
                AND tgai.customer_name like CONCAT('%',#{params.veOwnerName},'%')
            </if>
             <if test=" params.vin !=null and params.vin != '' ">
                AND tgai.vin like CONCAT('%',#{params.vin},'%')
            </if>
             <if test=" params.license !=null and params.license != '' ">
                AND tgai.license like CONCAT('%',#{params.license},'%')
            </if>


       </select>

		<select id="findApplyInfobyCouponIds" resultType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO">
      		SELECT
            DISTINCT
      		tgai.APP_ID appId,
      	    tgai.id, tgai.apply_no applyNo, tgai.dealer_code dealerCode,
      	    tgai.dealer_name dealerName,tgai.area_manage_id areaManageId,tgai.area_manage areaManage,tgai.small_area_id smallAreaId,tgai.small_area smallArea,tgai.bloc_id blocId,tgai.bloc bloc,
      	    tgai.goodwill_nature goodwillNature,
      	   	tgai.goodwill_status goodwillStatus, tgai.customer_pain customerPain, tgai.vin,
      	   	tgai.license license, tgai.customer_name customerName,
      	    tgai.customer_mobile customerMobile, tgai.mileage mileage, tgai.model model, tgai.buy_car_date buyCarDate,
      	    tg.consume_id consumes,
			tg.notice_invoice_date noticeDate,
			tg.voucher_recharge_price rechargeAmount,
            tgai.cost_rate,
            tgai.COUPON_ID couponId,
			IFNULL(tttt.invoicedPrice,0) noticeInvoicePrice,
			IFNULL(ttt.invoice_price,0) voucherInvoiceAmount,
			IFNULL(tg.voucher_recharge_price,0)-IFNULL(ttt.invoice_price,0)	 voucherNotInvoiceAmount
	      FROM  tt_goodwill_apply_info tgai
	      LEFT JOIN tt_goodwill_notice_invoice_info tg on tgai.ID=tg.goodwill_apply_id and tg.invoice_object='0'
          left join   tt_goodwill_audit_info tau  on  tgai.id=tau.goodwill_apply_id and  tau.audit_object =0 and tau.is_deleted =0 and tau.audit_role= 'SHQYJL'
          left join (select sum(notice_invoice_price) invoicedPrice,count(id) countInvoice,goodwill_apply_id from tt_goodwill_notice_invoice_info where voucher_type=1 and CHAR_LENGTH(invoice_id)>14 group by goodwill_apply_id) tttt on tgai.id=tttt.goodwill_apply_id
		  left join (select sum(invoice_price) invoice_price,goodwill_apply_id from  tt_goodwill_invoice_record where CHAR_LENGTH(invoice_id)>14 group by goodwill_apply_id) ttt on tgai.id=ttt.goodwill_apply_id
	      WHERE tgai.COUPON_ID in
	      <foreach collection="couponIdList" index="index" item="item" open="(" separator="," close=")">
			#{item}
		  </foreach>
		  <if test=" params.group !=null and params.group != '' ">
                AND tgai.bloc_id = #{params.group}
            </if>
            <if test=" params.areaManage !=null and params.areaManage != '' ">
                AND tgai.area_Manage_Id IN (${params.areaManage})
            </if>
            <if test=" params.smallArea !=null and params.smallArea != '' ">
                AND tgai.small_area_id IN (${params.smallArea})
            </if>
            <if test=" params.auditName1 !=null and params.auditName1.size > 0 ">
                AND tau.audit_name in
                <foreach collection="params.auditName1" item="audit_name" open="(" separator="," close=")">
                    #{audit_name}
                </foreach>
            </if>
	      <if test=" params.applyNo !=null and params.applyNo != '' ">
	                AND tgai.apply_No like CONCAT('%',#{params.applyNo},'%')
           </if>
           <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND tgai.dealer_code like CONCAT('%',#{params.dealerCode},'%')
           </if>
           <if test=" params.veOwnerName !=null and params.veOwnerName != '' ">
                AND tgai.customer_name like CONCAT('%',#{params.veOwnerName},'%')
            </if>
             <if test=" params.vin !=null and params.vin != '' ">
                AND tgai.vin like CONCAT('%',#{params.vin},'%')
            </if>
             <if test=" params.license !=null and params.license != '' ">
                AND tgai.license like CONCAT('%',#{params.license},'%')
            </if>

       </select>

       <select id="getConsumeId" resultType="java.lang.String" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillStampsDTO">
      		select DISTINCT(aa.consume_id) consume  from (
      			select t.consume_id,t.invoice_id,t.notice_invoice_date,tt.invoice_no,tt.invoice_date  from tt_goodwill_notice_invoice_info t left join tt_goodwill_invoice_record tt on t.invoice_id=tt.invoice_id
      			) aa
				where 1=1
	             <if test=" params.invoiceNumber !=null and params.invoiceNumber != '' ">
	                AND aa.invoice_no like CONCAT('%',#{params.invoiceNumber},'%')
	            </if>
	            <if test=" params.invoiceId !=null and params.invoiceId != '' ">
	                AND aa.invoice_id like CONCAT('%',#{params.invoiceId},'%')
	            </if>
	            <if test=" params.noticeStartdAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(aa.notice_invoice_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.noticeStartdAt }, '%Y-%m-%d')   ]]>
	            </if>
	            <if test=" params.noticeEndAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(aa.notice_invoice_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.noticeEndAt }, '%Y-%m-%d')   ]]>
	            </if>

	            <if test=" params.invoiceStartdAt !=null  ">
	                <![CDATA[   and DATE_FORMAT(aa.invoice_date, '%Y-%m-%d')>=  DATE_FORMAT(#{params.invoiceStartdAt }, '%Y-%m-%d')   ]]>
	            </if>
	            <if test=" params.invoiceEndAt!=null  ">
	                <![CDATA[   and DATE_FORMAT(aa.invoice_date, '%Y-%m-%d')<=  DATE_FORMAT(#{params.invoiceEndAt }, '%Y-%m-%d')   ]]>
	            </if>
      		</select>

      	<select id="queryRefuse" resultMap="BaseResultMap">
	       SELECT
	       <include refid="Base_Column_List"/>
  			from tt_goodwill_apply_info t
	       where t.goodwill_status=82551004 and DATE_FORMAT(date_add(t.pass_time,INTERVAL #{month} MONTH), '%Y-%m-%d') &lt; DATE_FORMAT(now(), '%Y-%m-%d')
       </select>
      	<update id="updateStatusById" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO">
			update tt_goodwill_apply_info
			set goodwill_status=#{params.goodwillStatus},last_goodwill_status=#{params.lastGoodwillStatus},
			updated_by='-1',updated_at=now()
			where id = #{params.id}
		</update>
		<select id="queryApplyTimeOut" resultMap="BaseResultMap">
	       select
			<include refid="Base_Column_List"/>
	       from  tt_goodwill_apply_info t
			left join (select  tt.audit_date,tt.goodwill_apply_id  from (
			select  max(id) maxId  from tt_goodwil_apply_audit_process where audit_object=0 and audit_status is not null group by goodwill_apply_id ) t
			left join tt_goodwil_apply_audit_process tt on t.maxId=tt.id) tt on t.id=tt.goodwill_apply_id
			where t.goodwill_status=82551002 and ( DATE_FORMAT(date_add(ifnull(tt.audit_date,t.commit_time),INTERVAL #{days} DAY), '%Y-%m-%d') &lt; DATE_FORMAT(now(), '%Y-%m-%d'))
       </select>
      	<select id="goodwillMatrialAuditTask" resultMap="BaseResultMap">
	       select
			<include refid="Base_Column_List"/>
	       from  tt_goodwill_apply_info t
			left join (select  tt.audit_date,tt.goodwill_apply_id  from (
			select  max(id) maxId  from tt_goodwil_apply_audit_process where audit_object=1 and audit_status is not null group by goodwill_apply_id ) t
			left join tt_goodwil_apply_audit_process tt on t.maxId=tt.id) tt on t.id=tt.goodwill_apply_id
			where t.goodwill_status=82551005
			<if test=" type !=null and type==1 ">
	                and ( DATE_FORMAT(date_add(ifnull(tt.audit_date,t.material_commit_time),INTERVAL #{days} DAY), '%Y-%m-%d') &lt; DATE_FORMAT(now(), '%Y-%m-%d'))
	        </if>
	        <if test=" type !=null and type==2 ">
	                and ( DATE_FORMAT(date_add(ifnull(tt.audit_date,t.material_commit_time),INTERVAL #{days} DAY), '%Y-%m-%d') &lt; DATE_FORMAT(now(), '%Y-%m-%d'))
	        </if>

       </select>
       <select id="goodwillMatrialCommitTask" resultMap="BaseResultMap">
	       select
			<include refid="Base_Column_List"/>
	       from  tt_goodwill_apply_info t
			left join (select  tt.audit_date,tt.goodwill_apply_id  from (
			select  max(id) maxId  from tt_goodwil_apply_audit_process where audit_object=1 and audit_status is not null group by goodwill_apply_id ) t
			left join tt_goodwil_apply_audit_process tt on t.maxId=tt.id) tt on t.id=tt.goodwill_apply_id
			where t.goodwill_status=82551004
			<if test=" type !=null and type==1 ">
	                and ( DATE_FORMAT(date_add(ifnull(tt.audit_date,t.pass_time),INTERVAL #{days} DAY), '%Y-%m-%d') &lt; DATE_FORMAT(now(), '%Y-%m-%d'))
	        </if>
	        <if test=" type !=null and type==2 ">
	                and ( DATE_FORMAT(date_add(ifnull(tt.audit_date,t.pass_time),INTERVAL #{days} DAY), '%Y-%m-%d') &lt; DATE_FORMAT(now(), '%Y-%m-%d'))
	        </if>

       </select>
       <select id="goodwillSupplyMatrialCommitTask" resultMap="BaseResultMap">
	       select
			<include refid="Base_Column_List"/>
	        from  tt_goodwill_apply_info t
			left join (select  tt.audit_time,tt.goodwill_apply_id,tt.return_to  from (
			select  max(id) maxId  from tt_goodwill_audit_info where audit_object=1 and audit_result=82801002   group by goodwill_apply_id ) t
			left join tt_goodwill_audit_info tt on t.maxId=tt.id) tt on t.id=tt.goodwill_apply_id
			where t.goodwill_status=82551006 AND t.dealer_code=tt.return_to
			<if test=" type !=null and type==1 ">
	                and ( DATE_FORMAT(date_add(tt.audit_time,INTERVAL #{days} DAY), '%Y-%m-%d') &lt; DATE_FORMAT(now(), '%Y-%m-%d'))
	        </if>
	        <if test=" type !=null and type==2 ">
	                and ( DATE_FORMAT(date_add(tt.audit_time,INTERVAL #{days} DAY), '%Y-%m-%d') &lt; DATE_FORMAT(now(), '%Y-%m-%d'))
	        </if>

       </select>
       <select id="goodwillMatrialCommitTimeOutTask" resultMap="BaseResultMap">
	       select
			<include refid="Base_Column_List"/>
	       from  tt_goodwill_apply_info t
			left join (select  tt.audit_date,tt.goodwill_apply_id  from (
			select  max(id) maxId  from tt_goodwil_apply_audit_process where audit_object=1 and audit_status is not null group by goodwill_apply_id ) t
			left join tt_goodwil_apply_audit_process tt on t.maxId=tt.id) tt on t.id=tt.goodwill_apply_id
			where t.goodwill_status=82551004
	         and ( DATE_FORMAT(date_add(ifnull(tt.audit_date,t.pass_time),INTERVAL #{days} DAY), '%Y-%m-%d') &lt; DATE_FORMAT(now(), '%Y-%m-%d'))
       </select>
       <select id="goodwillInvoiceTimeOutTask" resultMap="BaseResultMap">
	       select
			<include refid="Base_Column_List"/>
	       from  tt_goodwill_apply_info t
			left join (select  t.goodwill_apply_id,t.notice_invoice_date  from  tt_goodwill_notice_invoice_info t
			left join tt_goodwill_invoice_record tt on t.invoice_id=tt.invoice_id
			where t.invoice_object=0 and tt.invoice_no is null and t.notice_invoice_price &gt; 0) tt on t.id=tt.goodwill_apply_id
			where t.goodwill_status in (82551008,82551012) and ( DATE_FORMAT(date_add(tt.notice_invoice_date,INTERVAL #{days} DAY), '%Y-%m-%d')&lt; DATE_FORMAT(now(), '%Y-%m-%d'))
       </select>

       <select id="queryMobile" resultType="java.util.HashMap">
       		select ifnull(ifnull(deliverer_mobile,null),deliverer_phone) mobile  from  tt_product_deliverer  where vin=#{vin} order by updated_at desc  limit 1
       </select>

       <select id="getGoodwillFirstPage" resultType="java.util.HashMap">
       		select
			(select count(1) stagingNumber  from tt_goodwill_apply_info where 1=1
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and dealer_code= #{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND area_manage_id=#{params.bigAreaManage}
            </if>
			and goodwill_status=82551001) stagingNumber,
			(select count(1) applyToAuditNumber  from tt_goodwill_apply_info t  left join (select *  from (select min(id) min_id from tt_goodwil_apply_audit_process t where  t.audit_status is null group by t.goodwill_apply_id)t
			left join tt_goodwil_apply_audit_process tt on t.min_id=tt.id ) tt on t.id=tt.goodwill_apply_id where 1=1
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and t.dealer_code= #{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND t.small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND t.area_manage_id=#{params.bigAreaManage}
            </if>
            <if test=" params.roleList !=null and params.roleList != ''">
                 and (tt.audit_role in
					<foreach collection="params.roleList" index="index" item="item"
					             open="(" separator="," close=")">
					        #{item}
					</foreach>
				or (tt.audit_role in ('VP','CEO') and 'CCMQ' IN
					<foreach collection="params.roleList" index="index" item="item"
					             open="(" separator="," close=")">
					        #{item}
					</foreach>
					))
            </if>

			and goodwill_status=82551002) applyToAuditNumber,
			(select count(1) applyRejectedNumber  from tt_goodwill_apply_info t left join (select *  from (select max(id) max_id from tt_goodwill_audit_info  group by goodwill_apply_id ) t
			left join tt_goodwill_audit_info tt on t.max_id=tt.id  )tt on t.id=tt.goodwill_apply_id where 1=1
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and t.dealer_code= #{params.dealerCode} and tt.return_to=#{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND t.small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND t.area_manage_id=#{params.bigAreaManage}
            </if>
            <if test=" params.roleList !=null and params.roleList != ''">
                 and tt.return_to in
				    <foreach collection="params.roleList" index="index" item="item"
				             open="(" separator="," close=")">
				        #{item}
				    </foreach>
            </if>

			and goodwill_status=82551003) applyRejectedNumber,
			(select count(1) materialToUploadNumber  from tt_goodwill_apply_info where 1=1
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and dealer_code= #{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND area_manage_id=#{params.bigAreaManage}
            </if>
			and goodwill_status=82551004) materialToUploadNumber,
			(select count(1) materialToAuditNumber  from tt_goodwill_apply_info t  left join (select *  from (select min(id) min_id from tt_goodwil_apply_audit_process t where  t.audit_status is null group by t.goodwill_apply_id)t
			left join tt_goodwil_apply_audit_process tt on t.min_id=tt.id ) tt on t.id=tt.goodwill_apply_id where 1=1
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and dealer_code= #{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND area_manage_id=#{params.bigAreaManage}
            </if>
            <if test=" params.roleList !=null and params.roleList != ''">
                 and tt.audit_role in
					<foreach collection="params.roleList" index="index" item="item"
					             open="(" separator="," close=")">
					        #{item}
					</foreach>
            </if>
			and goodwill_status=82551005) materialToAuditNumber,
			(select count(1) toBeTranslatedNumber  from tt_goodwill_apply_info t  left join (select *  from (select min(id) min_id from tt_goodwil_apply_audit_process t where  t.audit_status is null group by t.goodwill_apply_id)t
			left join tt_goodwil_apply_audit_process tt on t.min_id=tt.id ) tt on t.id=tt.goodwill_apply_id where 1=1 and tt.audit_role like 'CFO' and t.is_need_translate=10041001
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and t.dealer_code= #{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND t.small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND t.area_manage_id=#{params.bigAreaManage}
            </if>
			and t.goodwill_status=82551005) toBeTranslatedNumber,
			(select count(1) materialRejectedNumber  from tt_goodwill_apply_info t left join (select *  from (select max(id) max_id from tt_goodwill_audit_info  group by goodwill_apply_id ) t
			left join tt_goodwill_audit_info tt on t.max_id=tt.id  )tt on t.id=tt.goodwill_apply_id where 1=1
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and t.dealer_code= #{params.dealerCode} and tt.return_to=#{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND t.small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND t.area_manage_id=#{params.bigAreaManage}
            </if>
            <if test=" params.roleList !=null and params.roleList != ''">
                 and tt.return_to in
				    <foreach collection="params.roleList" index="index" item="item"
				             open="(" separator="," close=")">
				        #{item}
				    </foreach>
            </if>
			and goodwill_status=82551006) materialRejectedNumber,
			(select count(1) toNoticeInvoiceNumber  from tt_goodwill_apply_info where 1=1
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and dealer_code= #{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND area_manage_id=#{params.bigAreaManage}
            </if>
			and goodwill_status=82551007) toNoticeInvoiceNumber,
			(select count(1) toInvoicedNumber  from tt_goodwill_apply_info where 1=1
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and dealer_code= #{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND area_manage_id=#{params.bigAreaManage}
            </if>
			and goodwill_status in (82551008)) toInvoicedNumber,

			(select count(1) toInvoicedNumber  from tt_goodwill_apply_info where 1=1
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and dealer_code= #{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND area_manage_id=#{params.bigAreaManage}
            </if>
			and goodwill_status in (82551012)) toInvoicedNumberAndRecharge,


			(select count(1) toConfirmInvoiceNumber  from tt_goodwill_apply_info where 1=1
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and dealer_code= #{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND area_manage_id=#{params.bigAreaManage}
            </if>
			and goodwill_status in (82551009)) toConfirmInvoiceNumber,

			<!-- 待确认发票/已充值 -->
			(select count(1) toConfirmInvoiceNumber  from tt_goodwill_apply_info where 1=1
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and dealer_code= #{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND area_manage_id=#{params.bigAreaManage}
            </if>
			and goodwill_status in (82551013)) toConfirmInvoiceNumberAndRecharge,


			(select count(1) refusedSupportNumber  from tt_goodwill_apply_info where 1=1
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and dealer_code= #{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND area_manage_id=#{params.bigAreaManage}
            </if>
			and goodwill_status=82551015) refusedSupportNumber,
			(select count(1) noNeedSupportNumber  from tt_goodwill_apply_info where 1=1
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and dealer_code= #{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND area_manage_id=#{params.bigAreaManage}
            </if>
			and goodwill_status=82551016) noNeedSupportNumber,
			(select count(1) sendMailNumber  from tt_goodwill_apply_mail_history t left join tt_goodwill_apply_info tt on t.goodwill_apply_id=tt.id where 1=1
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and tt.dealer_code= #{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND tt.small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND tt.area_manage_id=#{params.bigAreaManage}
            </if>
			) sendMailNumber,
			(select count(1) sendFailMailNumber  from tt_goodwill_apply_mail_history t left join tt_goodwill_apply_info tt on t.goodwill_apply_id=tt.id where 1=1
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and tt.dealer_code= #{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND tt.small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND tt.area_manage_id=#{params.bigAreaManage}
            </if>
			and t.send_status=82771002) sendFailMailNumber,
			(select count(1) allGoodwillApplyNumber  from tt_goodwill_apply_info where 1=1
			<if test=" params.dealerCode !=null and params.dealerCode != '' ">
			and dealer_code= #{params.dealerCode}
			</if>
			<if test=" params.smallAreaManage !=null and params.smallAreaManage != ''">
                AND small_area_id=#{params.smallAreaManage}
            </if>
            <if test=" params.bigAreaManage !=null and params.bigAreaManage != ''">
                AND area_manage_id=#{params.bigAreaManage}
            </if>
			and goodwill_status &lt;&gt; 82551001) allGoodwillApplyNumber
       </select>

    <!--  根据查询参数查询亲善申请单 add czm 20210721 -->
    <select id="getGoodwillApplyInfoDatas"
            resultType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_info t
        WHERE 1=1
        <if test=" params.applyNoList !=null and params.applyNoList.size() > 0 ">
            AND t.apply_no IN
            <foreach collection="params.applyNoList" item="applyNo" separator="," open="(" close=")">
                #{applyNo}
            </foreach>
        </if>
        AND t.ccm_id is not null
    </select>

        <select id="queryDealer" resultType="java.lang.String">
            SELECT dealer_code FROM tt_goodwill_apply_info group by dealer_code
    </select>

        <update id="regionInformationRefresh" >
            <foreach collection="params" item="item" index="index" open="" close="" separator=";">
                UPDATE tt_goodwill_apply_info
                <set>
                    <if test=" item.afterSmallAreaId !=null">
                        small_area_id = #{item.afterSmallAreaId},
                    </if>
                    <if test=" item.afterSmallAreaName !=null">
                        small_area = #{item.afterSmallAreaName},
                    </if>
                    <if test=" item.afterBigAreaId !=null">
                        area_manage_id = #{item.afterBigAreaId},
                    </if>
                    <if test=" item.afterBigAreaName !=null">
                        area_manage = #{item.afterBigAreaName},
                    </if>
                </set>
                <where>
                    dealer_code = #{item.companyCode}
                </where>
            </foreach>
        </update>
</mapper>
