<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.voc.LossDataRecordMapper">
    <insert id="insertList" parameterType="com.yonyou.dmscus.customer.entity.po.vocfunctional.LossDataRecordPo">
        insert into  tt_loss_data_record
        (
        vin,
        dealer_code,
        order_at,
        last_dealer_code,
        record_at,
        invoice_date,
        owner_name,
        mobile,
        record_id
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.vin},
            #{item.dealerCode},
            #{item.orderAt},
            #{item.lastDealerCode},
            #{item.recordAt},
            #{item.invoiceDate},
            #{item.ownerName},
            #{item.mobile},
            #{item.recordId}
            )
        </foreach>

    </insert>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageList" resultType="com.yonyou.dmscus.customer.entity.dto.loss.LossDataRecordDto" parameterType="com.yonyou.dmscus.customer.dto.LossDataRecordVo">
        SELECT
           id,
           vin,
           dealer_code,
           order_at,
           last_dealer_code,
           record_at,
           invoice_date,
           active_at,
           active_dealer_code,
           is_active,
           owner_name,
           mobile
        FROM tt_loss_data_record t
        WHERE 1=1  and t.is_deleted = 0
        <if test="params.dealerCodes !=null and  params.indexFactory ==0 ">
                AND t.dealer_code =#{params.dealerCode}
        </if>
        <if test="params.dealerCodes !=null and  params.indexFactory ==1 ">

            <if test=" params.dealerCodes !=null and params.dealerCodes.size()>0 ">
                AND t.dealer_code in
                <foreach collection="params.dealerCodes" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin  like "%" #{params.vin}"%"
        </if>
        <if test=" params.lastDealerCodes !=null and params.lastDealerCodes.size()>0 ">
            AND t.last_dealer_code in
            <foreach collection="params.lastDealerCodes" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.isActive !=null  ">
            AND t.is_active = #{params.isActive}
        </if>
        <if test=" params.orderAt !=null and params.orderAt != '' ">
            AND t.order_at &gt;= CONCAT(#{params.orderAt},' 00:00:00')
        </if>
        <if test=" params.endOrderAt !=null and params.endOrderAt != '' ">
            AND t.order_at &lt;= CONCAT(#{params.endOrderAt},' 23:59:59')
        </if>

        <if test=" params.recordAt !=null and params.recordAt != '' ">
            AND t.record_at &gt;= CONCAT(#{params.recordAt},' 00:00:00')
        </if>
        <if test=" params.endRecordAt !=null and params.endRecordAt != '' ">
            AND t.record_at &lt;= CONCAT(#{params.endRecordAt},' 23:59:59')
        </if>

        <if test=" params.invoiceDate !=null and params.invoiceDate != '' ">
            AND t.invoice_date &gt;= CONCAT(#{params.invoiceDate},' 00:00:00')
        </if>
        <if test=" params.endInvoiceDate !=null and params.endInvoiceDate != '' ">
            AND t.invoice_date &lt;= CONCAT(#{params.endInvoiceDate},' 23:59:59')
        </if>

    </select>



    <select id="exportExcel" resultType="Map"
            parameterType="com.yonyou.dmscus.customer.dto.LossDataRecordVo">
        SELECT
        t.vin ,
        t.dealer_code as dealerCode,
        t.order_at as orderAt,
        t.last_dealer_code as lastDealerCode,
        t.record_at as recordAt,
        t.invoice_date as invoiceDate,
        t.active_at as activeAt,
        t.active_dealer_code as activeDealerCode,
        case
        when t.is_active = 0 then '否'
        else '是' end as isActive,
        t.owner_name as ownerName,
        t.mobile
        FROM tt_loss_data_record t
        WHERE 1=1  and t.is_deleted = 0
        <if test=" params.dealerCodes !=null and params.dealerCodes.size()>0 ">
            AND t.dealer_code in
            <foreach collection="params.dealerCodes" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin  like "%" #{params.vin}"%"
        </if>
        <if test=" params.lastDealerCodes !=null and params.lastDealerCodes.size()>0 ">
            AND t.last_dealer_code in
            <foreach collection="params.lastDealerCodes" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.isActive !=null  ">
            AND t.is_active = #{params.isActive}
        </if>
        <if test=" params.orderAt !=null and params.orderAt != '' ">
            AND t.order_at &gt;= CONCAT(#{params.orderAt},' 00:00:00')
        </if>
        <if test=" params.endOrderAt !=null and params.endOrderAt != '' ">
            AND t.order_at &lt;= CONCAT(#{params.endOrderAt},' 23:59:59')
        </if>

        <if test=" params.recordAt !=null and params.recordAt != '' ">
            AND t.record_at &gt;= CONCAT(#{params.recordAt},' 00:00:00')
        </if>
        <if test=" params.endRecordAt !=null and params.endRecordAt != '' ">
            AND t.record_at &lt;= CONCAT(#{params.endRecordAt},' 23:59:59')
        </if>

        <if test=" params.invoiceDate !=null and params.invoiceDate != '' ">
            AND t.invoice_date &gt;= CONCAT(#{params.invoiceDate},' 00:00:00')
        </if>
        <if test=" params.endInvoiceDate !=null and params.endInvoiceDate != '' ">
            AND t.invoice_date &lt;= CONCAT(#{params.endInvoiceDate},' 23:59:59')
        </if>
        order by t.id desc
    </select>

</mapper>