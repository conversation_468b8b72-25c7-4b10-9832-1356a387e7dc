<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.vocAccidentInvitation.TempInviteVehicleVocTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.yonyou.dmscus.customer.entity.po.vocAccidentInvitation.TempInviteVehicleVocTaskPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="error_msg" property="errorMsg"/>
        <result column="line_number" property="lineNumber"/>
        <result column="is_error" property="isError"/>
        <result column="name" property="name"/>
        <result column="tel" property="tel"/>
        <result column="vin" property="vin"/>
        <result column="license_plate_num" property="licensePlateNum"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="contact_date" property="contactDate"/>
        <result column="contact_situation" property="contactSituation"/>
        <result column="accident_no" property="accidentNo"/>
        <result column="accident_detail" property="accidentDetail"/>
        <result column="remark" property="remark"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="contact_date_value" property="contactDateValue"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, error_msg, line_number, is_error, name, tel, vin, license_plate_num, dealer_code, contact_date, contact_situation, accident_no, accident_detail, remark, created_at, updated_at
        </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.vocAccidentInvitation.TempInviteVehicleVocTaskPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM temp_invite_vehicle_voc_task t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.errorMsg !=null and params.errorMsg != '' ">
            AND t.error_msg = #{params.errorMsg}
        </if>
        <if test=" params.lineNumber !=null and params.lineNumber != '' ">
            AND t.line_number = #{params.lineNumber}
        </if>
        <if test=" params.isError !=null and params.isError != '' ">
            AND t.is_error = #{params.isError}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.tel !=null and params.tel != '' ">
            AND t.tel = #{params.tel}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num = #{params.licensePlateNum}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.contactDate !=null and params.contactDate != '' ">
            AND t.contact_date = #{params.contactDate}
        </if>
        <if test=" params.contactSituation !=null and params.contactSituation != '' ">
            AND t.contact_situation = #{params.contactSituation}
        </if>
        <if test=" params.accidentNo !=null and params.accidentNo != '' ">
            AND t.accident_no = #{params.accidentNo}
        </if>
        <if test=" params.accidentDetail !=null and params.accidentDetail != '' ">
            AND t.accident_detail = #{params.accidentDetail}
        </if>
        <if test=" params.remark !=null and params.remark != '' ">
            AND t.remark = #{params.remark}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.vocAccidentInvitation.TempInviteVehicleVocTaskPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM temp_invite_vehicle_voc_task t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.errorMsg !=null and params.errorMsg != '' ">
            AND t.error_msg = #{params.errorMsg}
        </if>
        <if test=" params.lineNumber !=null and params.lineNumber != '' ">
            AND t.line_number = #{params.lineNumber}
        </if>
        <if test=" params.isError !=null and params.isError != '' ">
            AND t.is_error = #{params.isError}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.tel !=null and params.tel != '' ">
            AND t.tel = #{params.tel}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num = #{params.licensePlateNum}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.contactDate !=null and params.contactDate != '' ">
            AND t.contact_date = #{params.contactDate}
        </if>
        <if test=" params.contactSituation !=null and params.contactSituation != '' ">
            AND t.contact_situation = #{params.contactSituation}
        </if>
        <if test=" params.accidentNo !=null and params.accidentNo != '' ">
            AND t.accident_no = #{params.accidentNo}
        </if>
        <if test=" params.accidentDetail !=null and params.accidentDetail != '' ">
            AND t.accident_detail = #{params.accidentDetail}
        </if>
        <if test=" params.remark !=null and params.remark != '' ">
            AND t.remark = #{params.remark}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <delete id="deleteAll" parameterType="java.lang.Long">
        delete from temp_invite_vehicle_voc_task where created_by=#{userId}
    </delete>

    <select id="getDealerCodes" resultType="map" >
        select DISTINCT t.dealer_code from temp_invite_vehicle_voc_task t where
        t.created_by=#{userId}
    </select>

    <select id="getVins" resultType="map" >
        select DISTINCT t.vin from temp_invite_vehicle_voc_task t where
        t.created_by=#{userId}
    </select>


    <update id="updateEmptyDealerCode" parameterType="java.lang.Long">
        update temp_invite_vehicle_voc_task TMP SET is_error = 1,ERROR_MSG=IFNULL(ERROR_MSG,'经销商代码错误')
        where TMP.created_by=#{userId} and (TMP.dealer_code is null or TRIM(TMP.dealer_code)='')
    </update>

    <update id="updateEmptyVin" parameterType="java.lang.Long">
        update temp_invite_vehicle_voc_task TMP SET is_error = 1,ERROR_MSG=IFNULL(ERROR_MSG,'车架号错误')
        where TMP.created_by=#{userId} and (TMP.vin is null or TRIM(TMP.vin)='')
    </update>



    <update id="updateErrorDealerCode">
        update temp_invite_vehicle_voc_task TMP SET is_error = 1,ERROR_MSG=IFNULL(ERROR_MSG,'经销商代码错误')
        where TMP.created_by=#{userId} and TMP.dealer_code in
        <foreach collection="errorList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateErrorVin">
        update temp_invite_vehicle_voc_task TMP SET is_error = 1,ERROR_MSG=IFNULL(ERROR_MSG,'车架号错误')
        where TMP.created_by=#{userId} and TMP.vin in
        <foreach collection="errorList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>



    <update id="updateCheckVinError"  parameterType="java.lang.Long">
        UPDATE temp_invite_vehicle_voc_task TMP SET is_error = 1,ERROR_MSG=IFNULL(ERROR_MSG,'车架号错误')
        where TMP.created_by=#{userId} and (TMP.vin is null or binary TMP.vin  not REGEXP '^[A-Z0-9]{17}$')
    </update>

    <update id="updateCheckVinRepeat" parameterType="java.lang.Long">
        UPDATE temp_invite_vehicle_voc_task a,(SELECT id,vin,created_by FROM temp_invite_vehicle_voc_task ) f
        SET is_error = 1,ERROR_MSG= IFNULL(ERROR_MSG,'车架号重复')
        where a.created_by=#{userId} and  a.vin=f.vin and a.id!=f.id
        and  a.created_by=f.created_by
    </update>
    
    <update id="updateCheckLicense" parameterType="java.lang.Long">
         UPDATE temp_invite_vehicle_voc_task TMP SET is_error = 1,ERROR_MSG=IFNULL(ERROR_MSG,'车牌号错误')
        where TMP.created_by=#{userId} and (TMP.license_plate_num is null or  license_plate_num  not REGEXP '^.{1,20}$')
    </update>

    <update id="updateCheckName" parameterType="java.lang.Long">
        UPDATE temp_invite_vehicle_voc_task TMP SET is_error = 1,ERROR_MSG=IFNULL(ERROR_MSG,'联系人姓名错误')
        where TMP.created_by=#{userId} and (TMP.name is null or TMP.name  not REGEXP '^.{1,20}$')
    </update>

    <update id="updateCheckTel" parameterType="java.lang.Long">
        UPDATE temp_invite_vehicle_voc_task TMP SET is_error = 1,ERROR_MSG=IFNULL(ERROR_MSG,'联系电话错误')
        where TMP.created_by=#{userId} and (TMP.tel is null or  TMP.tel  not REGEXP '^.{1,20}$')
    </update>

    <update id="updateCheckContactSituation" parameterType="java.lang.Long">
        UPDATE temp_invite_vehicle_voc_task TMP SET is_error = 1,ERROR_MSG=IFNULL(ERROR_MSG,'与客户通话情况错误')
        where TMP.created_by=#{userId} and TMP.contact_situation!='接通' and TMP.contact_situation!='未接通'
    </update>

    <update id="updateCheckAccidentNo" parameterType="java.lang.Long">
        UPDATE temp_invite_vehicle_voc_task TMP SET is_error = 1,ERROR_MSG=IFNULL(ERROR_MSG,'VOC事故号过长')
        where TMP.created_by=#{userId} and (TMP.accident_no is not null and TMP.accident_no  not REGEXP '^.{0,30}$')
    </update>

    <update id="updateCheckAccidentDetail" parameterType="java.lang.Long">
        UPDATE temp_invite_vehicle_voc_task TMP SET is_error = 1,ERROR_MSG=IFNULL(ERROR_MSG,'VOC事故说明过长')
        where TMP.created_by=#{userId} and (TMP.accident_detail is not null and TMP.accident_detail  not REGEXP '^.{0,200}$')
    </update>

    <update id="updateCheckRemark" parameterType="java.lang.Long">
        UPDATE temp_invite_vehicle_voc_task TMP SET is_error = 1,ERROR_MSG=IFNULL(ERROR_MSG,'备注过长')
        where TMP.created_by=#{userId} and (TMP.remark is not null and TMP.remark  not REGEXP '^.{0,200}$')
    </update>

    <select id="queryError" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM temp_invite_vehicle_voc_task t
        where t.is_error=1 and t.created_by=#{userId}
    </select>

    <select id="querySucessCount" resultType="java.lang.Integer" parameterType="java.lang.Long">
        SELECT
        count(1)
        FROM temp_invite_vehicle_voc_task t
        where t.is_error=0 and t.created_by=#{userId}
    </select>

    <select id="querywaitImport" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>,t.contact_date as contact_date_value
        FROM temp_invite_vehicle_voc_task t
        where t.is_error=0 and t.created_by=#{userId}
    </select>



</mapper>
