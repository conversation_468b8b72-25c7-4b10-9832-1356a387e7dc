<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="vin" property="vin"/>
        <result column="license_plate_num" property="licensePlateNum"/>
        <result column="name" property="name"/>
        <result column="dealer_name" property="dealerName"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="last_dealer_name" property="lastDealerName"/>
        <result column="last_dealer_code" property="lastDealerCode"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="operator_name" property="operatorName"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, vin, license_plate_num, name, dealer_name, dealer_code,
           last_dealer_name, last_dealer_code, data_sources, is_deleted, is_valid,operator_name, created_at, updated_at
        </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_vehicle_dealer_allocate_history t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num = #{params.licensePlateNum}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.dealerName !=null and params.dealerName != '' ">
            AND t.dealer_name = #{params.dealerName}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.lastDealerName !=null and params.lastDealerName != '' ">
            AND t.last_dealer_name = #{params.lastDealerName}
        </if>
        <if test=" params.lastDealerCode !=null and params.lastDealerCode != '' ">
            AND t.last_dealer_code = #{params.lastDealerCode}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_vehicle_dealer_allocate_history t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num = #{params.licensePlateNum}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.dealerName !=null and params.dealerName != '' ">
            AND t.dealer_name = #{params.dealerName}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.lastDealerName !=null and params.lastDealerName != '' ">
            AND t.last_dealer_name = #{params.lastDealerName}
        </if>
        <if test=" params.lastDealerCode !=null and params.lastDealerCode != '' ">
            AND t.last_dealer_code = #{params.lastDealerCode}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>


    <insert id="insertAllocateHistory">
        INSERT into
            tt_invite_vehicle_dealer_allocate_history(vin,license_plate_num,name,dealer_code,dealer_name,last_dealer_code,
                                                      last_dealer_name,operator_name,created_by)
        VALUES (#{params.vin},#{params.licensePlateNum},#{params.name},#{params.dealerCode},#{params.dealerName},
                #{params.lastDealerCode},#{params.lastDealerName},#{params.operatorName},#{params.createdBy})
    </insert>


</mapper>
