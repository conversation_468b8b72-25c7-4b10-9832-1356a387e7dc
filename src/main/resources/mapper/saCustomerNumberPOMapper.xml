<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.voicemanage.SaCustomerNumberMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.voicemanage.SaCustomerNumberPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="invite_id" property="inviteId"/>
        <result column="call_id" property="callId"/>
        <result column="sa_id" property="saId"/>
        <result column="cus_name" property="cusName"/>
        <result column="cus_number" property="cusNumber"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="dealer_code" property="dealerCode"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        app_id, owner_code, owner_par_code, org_id, id, invite_id, call_id, sa_id, cus_name, cus_number,
        data_sources, is_deleted, is_valid,dealer_code, created_at, updated_at
    </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.voicemanage.SaCustomerNumberPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sa_customer_number t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.inviteId !=null and params.inviteId != '' ">
            AND t.invite_id = #{params.inviteId}
        </if>
        <if test=" params.callId !=null and params.callId != '' ">
            AND t.call_id = #{params.callId}
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.sa_id = #{params.saId}
        </if>
        <if test=" params.cusName !=null and params.cusName != '' ">
            AND t.cus_name = #{params.cusName}
        </if>
        <if test=" params.cusNumber !=null and params.cusNumber != '' ">
            AND t.cus_number = #{params.cusNumber}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.voicemanage.SaCustomerNumberPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sa_customer_number t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.inviteId !=null and params.inviteId != '' ">
            AND t.invite_id = #{params.inviteId}
        </if>
        <if test=" params.callId !=null and params.callId != '' ">
            AND t.call_id = #{params.callId}
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.sa_id = #{params.saId}
        </if>
        <if test=" params.cusName !=null and params.cusName != '' ">
            AND t.cus_name = #{params.cusName}
        </if>
        <if test=" params.cusNumber !=null and params.cusNumber != '' ">
            AND t.cus_number = #{params.cusNumber}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <select id="selectYesterdayAll" resultType="java.util.HashMap">
        select owner_code,call_id,dealer_code,owner_par_code 
        from tt_sa_customer_number a
        where is_deleted=0 
        and not EXISTS (
		 SELECT 1 from tt_call_details b where a.call_id=b.call_id
		)
    </select>

    <select id="getSaCustomerNumber" resultMap="BaseResultMap">
        select a.cus_name,a.cus_number
        from tt_sa_customer_number a
        where a.is_deleted=0
        and a.invite_id= #{inviteId}
        order by a.created_at desc
        limit 0,1
    </select>



    <!-- 通用查询映射结果 -->
    <resultMap id="CusResultMap" type="com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceCustomerInfoDTO">
        <id column="tiic_id" property="tiicId"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="insurance_id" property="insuranceId"/>
        <result column="insure_name" property="insureName"/>
        <result column="insure_number" property="insureNumber"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="mark" property="mark"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Cus_Column_List">
        app_id, owner_code, owner_par_code, org_id, tiic_id, insurance_id, insure_name, insure_number, dealer_code, data_sources, is_deleted, is_valid, created_at, updated_at, mark
    </sql>

    <insert id="insertPo">
        insert into tt_invite_customer_info_his(insurance_id, insure_name, insure_number, dealer_code) values(
            #{params.insuranceId},#{params.insureName},#{params.insureNumber},#{params.dealerCode}
        )
    </insert>


    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectAllInsuranceCustomerInfo" resultMap="CusResultMap">
        SELECT
        <include refid="Cus_Column_List"/>
        FROM tt_invite_customer_info_his t
        WHERE 1=1
        <if test=" insuranceId !=null and insuranceId != '' ">
            AND t.insurance_id = #{insuranceId}
        </if>
        <if test=" dealerCode !=null and dealerCode != '' ">
            AND t.dealer_code = #{dealerCode}
        </if>
    </select>

    <update id="updateInsuranceCustomerInfo">
        update tt_invite_customer_info_his t
        set
        <if test=" params.insureName !=null and params.insureName != '' ">
            t.insure_name = #{params.insureName},
        </if>
        <if test=" params.insureNumber !=null and params.insureNumber != '' ">
            t.insure_number = #{params.insureNumber},
        </if>
        t.updated_at = now(),
        t.updated_by = #{userId}
        where
        t.tiic_id = #{params.tiicId}
    </update>

    <delete id="deleteInsuranceCustomerInfo">
        delete from tt_invite_customer_info_his
        where tiic_id = #{tiicId}
    </delete>

</mapper>
