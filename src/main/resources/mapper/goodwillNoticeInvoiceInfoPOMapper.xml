<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillNoticeInvoiceInfoMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillNoticeInvoiceInfoPO">
            <id column="id" property="id"/>
            <result column="APP_ID" property="appId"/>
            <result column="OWNER_CODE" property="ownerCode"/>
            <result column="OWNER_PAR_CODE" property="ownerParCode"/>
            <result column="ORG_ID" property="orgId"/>
            <result column="goodwill_apply_id" property="goodwillApplyId"/>
            <result column="invoice_id" property="invoiceId"/>
            <result column="invoice_title" property="invoiceTitle"/>
            <result column="name" property="name"/>
            <result column="taxpayer_identification_number" property="taxpayerIdentificationNumber"/>
            <result column="phone" property="phone"/>
            <result column="address" property="address"/>
            <result column="open_bank" property="openBank"/>
            <result column="account" property="account"/>
            <result column="notice_invoice_date" property="noticeInvoiceDate"/>
            <result column="notice_invoice_price" property="noticeInvoicePrice"/>
            <result column="voucher_recharge_price" property="voucherRechargePrice"/>
            <result column="volvo_credits_recharge_price" property="volvoCreditsRechargePrice"/>
            <result column="is_valid" property="isValid"/>
            <result column="is_deleted" property="isDeleted"/>
            <result column="created_at" property="createdAt"/>
            <result column="updated_at" property="updatedAt"/>
        	<result column="created_by" property="createdBy"/>
            <result column="updated_by" property="updatedBy"/>
            <result column="record_version" property="recordVersion"/>
        </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, goodwill_apply_id, invoice_id, invoice_title, name, taxpayer_identification_number, phone, address, open_bank, account,notice_invoice_date, notice_invoice_price, voucher_recharge_price,voucher_coupon_face_recharge_price, volvo_credits_recharge_price, is_valid, is_deleted, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillNoticeInvoiceInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_notice_invoice_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.invoiceId !=null and params.invoiceId != '' ">
                AND t.invoice_id = #{params.invoiceId}
            </if>
                    <if test=" params.invoiceTitle !=null and params.invoiceTitle != '' ">
                AND t.invoice_title = #{params.invoiceTitle}
            </if>
                    <if test=" params.name !=null and params.name != '' ">
                AND t.name = #{params.name}
            </if>
                    <if test=" params.taxpayerIdentificationNumber !=null and params.taxpayerIdentificationNumber != '' ">
                AND t.taxpayer_identification_number = #{params.taxpayerIdentificationNumber}
            </if>
                    <if test=" params.phone !=null and params.phone != '' ">
                AND t.phone = #{params.phone}
            </if>
                    <if test=" params.address !=null and params.address != '' ">
                AND t.address = #{params.address}
            </if>
                    <if test=" params.openBank !=null and params.openBank != '' ">
                AND t.open_bank = #{params.openBank}
            </if>
                    <if test=" params.account !=null and params.account != '' ">
                AND t.account = #{params.account}
            </if>
                    <if test=" params.noticeInvoicePrice !=null and params.noticeInvoicePrice != '' ">
                AND t.notice_invoice_price = #{params.noticeInvoicePrice}
            </if>
                    <if test=" params.voucherRechargePrice !=null and params.voucherRechargePrice != '' ">
                AND t.voucher_recharge_price = #{params.voucherRechargePrice}
            </if>
                    <if test=" params.volvoCreditsRechargePrice !=null and params.volvoCreditsRechargePrice != '' ">
                AND t.volvo_credits_recharge_price = #{params.volvoCreditsRechargePrice}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillNoticeInvoiceInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_notice_invoice_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.invoiceId !=null and params.invoiceId != '' ">
                AND t.invoice_id = #{params.invoiceId}
            </if>
                    <if test=" params.invoiceTitle !=null and params.invoiceTitle != '' ">
                AND t.invoice_title = #{params.invoiceTitle}
            </if>
                    <if test=" params.name !=null and params.name != '' ">
                AND t.name = #{params.name}
            </if>
                    <if test=" params.taxpayerIdentificationNumber !=null and params.taxpayerIdentificationNumber != '' ">
                AND t.taxpayer_identification_number = #{params.taxpayerIdentificationNumber}
            </if>
                    <if test=" params.phone !=null and params.phone != '' ">
                AND t.phone = #{params.phone}
            </if>
                    <if test=" params.address !=null and params.address != '' ">
                AND t.address = #{params.address}
            </if>
                    <if test=" params.openBank !=null and params.openBank != '' ">
                AND t.open_bank = #{params.openBank}
            </if>
                    <if test=" params.account !=null and params.account != '' ">
                AND t.account = #{params.account}
            </if>
                    <if test=" params.noticeInvoicePrice !=null and params.noticeInvoicePrice != '' ">
                AND t.notice_invoice_price = #{params.noticeInvoicePrice}
            </if>
                    <if test=" params.voucherRechargePrice !=null and params.voucherRechargePrice != '' ">
                AND t.voucher_recharge_price = #{params.voucherRechargePrice}
            </if>
                    <if test=" params.volvoCreditsRechargePrice !=null and params.volvoCreditsRechargePrice != '' ">
                AND t.volvo_credits_recharge_price = #{params.volvoCreditsRechargePrice}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

         <select id="queryPaymentInfoByGoodwillApplyId" resultMap="BaseResultMap">
	         <!--  SELECT
	        (select sum(tt.notice_invoice_price) from tt_goodwill_notice_invoice_info tt where tt.goodwill_apply_id = #{goodwillApplyId}) notice_invoice_price,
	         id, goodwill_apply_id, invoice_id, invoice_title, name, taxpayer_identification_number, phone, address, open_bank, account,notice_invoice_date, voucher_recharge_price, volvo_credits_recharge_price
	        FROM tt_goodwill_notice_invoice_info t
	        WHERE 1=1  AND t.goodwill_apply_id = #{goodwillApplyId}
	        and t.invoice_object=0 -->
	        select *  from (	select  max(id) max_id from tt_goodwill_notice_invoice_info group by goodwill_apply_id) t
			left join (
			SELECT (select sum(tt.notice_invoice_price) from tt_goodwill_notice_invoice_info tt where tt.goodwill_apply_id = #{goodwillApplyId})  notice_invoice_price,
	         id, goodwill_apply_id, invoice_id, invoice_title, name, taxpayer_identification_number, phone, address, open_bank, account,notice_invoice_date, voucher_recharge_price, volvo_credits_recharge_price
	        FROM tt_goodwill_notice_invoice_info t )tt on t.max_id=tt.id
					
	        WHERE 1=1  AND tt.goodwill_apply_id = #{goodwillApplyId}
         
         </select>

    <select id="queryLastNoticeInoviceInfo" resultType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillNoticeInvoiceInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_notice_invoice_info t
        WHERE 1=1  AND t.goodwill_apply_id = #{goodwillApplyId}
        order by created_at desc limit 1
    </select>
    <select id="queryVoucherByGoodwillApplyId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_notice_invoice_info t
        WHERE 1=1  AND t.goodwill_apply_id = #{goodwillApplyId} and t.invoice_object=0
         limit 1
    </select>

    <select id="queryNoticeInvoice" resultType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillNoticeInvoiceInfoPO">
        select t1.consume_id,t1.invoice_object,t1.notice_invoice_price,t1.voucher_recharge_price,t2.cost_rate as costRate
        from  tt_goodwill_notice_invoice_info t1
        left join tt_goodwill_apply_info t2 ON t1.goodwill_apply_id = t2.id
        where t1.goodwill_apply_id=#{goodwillApplyId}
    </select>


         <update id="updateStatus" >
         update tt_goodwill_notice_invoice_info set is_commit=10041001 where invoice_id=#{invoiceId}
         </update>  
         

	<select id="queryConsumeList" resultType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillNoticeInvoiceInfoPO">
		SELECT id,consume_id as consumeId FROM tt_goodwill_notice_invoice_info where consume_id is not NULL
	</select>
            

</mapper>
