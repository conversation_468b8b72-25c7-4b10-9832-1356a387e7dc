<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyRepairPartInfoMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyRepairPartInfoPO">
              <id column="id" property="id"/>
              <result column="APP_ID" property="appId"/>
              <result column="OWNER_CODE" property="ownerCode"/>
              <result column="OWNER_PAR_CODE" property="ownerParCode"/>
              <result column="ORG_ID" property="orgId"/>
              <result column="goodwill_apply_id" property="goodwillApplyId"/>
              <result column="repair_order_no" property="repairOrderNo"/>
              <result column="part_no" property="partNo"/>
              <result column="part_name" property="partName"/>
              <result column="quantity" property="quantity"/>
              <result column="order_date" property="orderDate"/>
              <result column="arrival_date" property="arrivalDate"/>
              <result column="remark" property="remark"/>
              <result column="is_valid" property="isValid"/>
              <result column="is_deleted" property="isDeleted"/>
              <result column="created_at" property="createdAt"/>
              <result column="updated_at" property="updatedAt"/>
          	  <result column="created_by" property="createdBy"/>
              <result column="updated_by" property="updatedBy"/>
              <result column="record_version" property="recordVersion"/>
        </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, goodwill_apply_id, repair_order_no, part_no, part_name, quantity, order_date, arrival_date, remark, is_valid, is_deleted, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyRepairPartInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_repair_part_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.repairOrderNo !=null and params.repairOrderNo != '' ">
                AND t.repair_order_no = #{params.repairOrderNo}
            </if>
                    <if test=" params.partNo !=null and params.partNo != '' ">
                AND t.part_no = #{params.partNo}
            </if>
                    <if test=" params.partName !=null and params.partName != '' ">
                AND t.part_name = #{params.partName}
            </if>
                    <if test=" params.quantity !=null and params.quantity != '' ">
                AND t.quantity = #{params.quantity}
            </if>
                    <if test=" params.orderDate !=null and params.orderDate != '' ">
                AND t.order_date = #{params.orderDate}
            </if>
                    <if test=" params.arrivalDate !=null and params.arrivalDate != '' ">
                AND t.arrival_date = #{params.arrivalDate}
            </if>
                    <if test=" params.remark !=null and params.remark != '' ">
                AND t.remark = #{params.remark}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyRepairPartInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_repair_part_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.repairOrderNo !=null and params.repairOrderNo != '' ">
                AND t.repair_order_no = #{params.repairOrderNo}
            </if>
                    <if test=" params.partNo !=null and params.partNo != '' ">
                AND t.part_no = #{params.partNo}
            </if>
                    <if test=" params.partName !=null and params.partName != '' ">
                AND t.part_name = #{params.partName}
            </if>
                    <if test=" params.quantity !=null and params.quantity != '' ">
                AND t.quantity = #{params.quantity}
            </if>
                    <if test=" params.orderDate !=null and params.orderDate != '' ">
                AND t.order_date = #{params.orderDate}
            </if>
                    <if test=" params.arrivalDate !=null and params.arrivalDate != '' ">
                AND t.arrival_date = #{params.arrivalDate}
            </if>
                    <if test=" params.remark !=null and params.remark != '' ">
                AND t.remark = #{params.remark}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
      </select>
       <!-- 集合查询,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="getSupportApplyPartInfoById" resultMap="BaseResultMap" parameterType="long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_repair_part_info t
        WHERE 1=1 AND t.goodwill_apply_id = #{goodwillApplyId}
  	</select>
      

</mapper>
