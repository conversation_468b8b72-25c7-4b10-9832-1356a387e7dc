<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.vocAccidentInvitation.InviteVehicleVocTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.yonyou.dmscus.customer.entity.po.vocAccidentInvitation.InviteVehicleVocTaskPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="name" property="name"/>
        <result column="tel" property="tel"/>
        <result column="vin" property="vin"/>
        <result column="license_plate_num" property="licensePlateNum"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="contact_date" property="contactDate"/>
        <result column="contact_situation" property="contactSituation"/>
        <result column="accident_no" property="accidentNo"/>
        <result column="accident_detail" property="accidentDetail"/>
        <result column="remark" property="remark"/>
        <result column="is_create_invite" property="isCreateInvite"/>
        <result column="invite_id" property="inviteId"/>
        <result column="follow_status" property="followStatus"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="plan_date" property="planDate"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, name, tel, vin, license_plate_num, dealer_code,
           contact_date, contact_situation, accident_no, accident_detail, remark, is_create_invite, invite_id,
           follow_status,data_sources, is_deleted, is_valid, created_at, updated_at
        </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.vocAccidentInvitation.InviteVehicleVocTaskPO">
        SELECT
        <include refid="Base_Column_List"/>,date_add(t.contact_date,interval 3 Minute) as plan_date
        FROM tt_invite_vehicle_voc_task t
        WHERE 1=1
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.tel !=null and params.tel != '' ">
            AND t.tel = #{params.tel}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num = #{params.licensePlateNum}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code like "%" #{params.dealerCode}"%"
        </if>
        <if test=" params.dealerCodes !=null and params.dealerCodes.size > 0 ">
        AND t.dealer_code in
        <foreach collection="params.dealerCodes" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        </if>
        <if test=" params.contactDateStart !=null and params.contactDateStart != '' ">
            AND t.contact_date &gt;= CONCAT(#{params.contactDateStart},' 00:00:00')
        </if>
        <if test=" params.contactDateEnd !=null and params.contactDateEnd != '' ">
            AND t.contact_date &lt;= CONCAT(#{params.contactDateEnd},' 23:59:59')
        </if>
        <if test=" params.newContactDateStart !=null and params.newContactDateStart != '' ">
            AND t.contact_date &gt;= CONCAT(#{params.newContactDateStart},' 00:00:00')
        </if>
        <if test=" params.newContactDateEnd !=null and params.newContactDateEnd != '' ">
            AND t.contact_date &lt;= CONCAT(#{params.newContactDateEnd},' 23:59:59')
        </if>
        <if test=" params.createdAtStart !=null and params.createdAtStart != '' ">
            AND t.created_at &gt;= CONCAT(#{params.createdAtStart},' 00:00:00')
        </if>
        <if test=" params.createdAtEnd !=null and params.createdAtEnd != '' ">
            AND t.created_at &lt;= CONCAT(#{params.createdAtEnd},' 23:59:59')
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.vocAccidentInvitation.InviteVehicleVocTaskPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_vehicle_voc_task t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.tel !=null and params.tel != '' ">
            AND t.tel = #{params.tel}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num = #{params.licensePlateNum}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.contactDate !=null and params.contactDate != '' ">
            AND t.contact_date = #{params.contactDate}
        </if>
        <if test=" params.contactSituation !=null and params.contactSituation != '' ">
            AND t.contact_situation = #{params.contactSituation}
        </if>
        <if test=" params.accidentNo !=null and params.accidentNo != '' ">
            AND t.accident_no = #{params.accidentNo}
        </if>
        <if test=" params.accidentDetail !=null and params.accidentDetail != '' ">
            AND t.accident_detail = #{params.accidentDetail}
        </if>
        <if test=" params.remark !=null and params.remark != '' ">
            AND t.remark = #{params.remark}
        </if>
        <if test=" params.isCreateInvite !=null and params.isCreateInvite != '' ">
            AND t.is_create_invite = #{params.isCreateInvite}
        </if>
        <if test=" params.inviteId !=null and params.inviteId != '' ">
            AND t.invite_id = #{params.inviteId}
        </if>
        <if test=" params.followStatus !=null and params.followStatus != '' ">
            AND t.follow_status = #{params.followStatus}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>


</mapper>
