<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.voc.VocWarningDataLogMapper">
    <insert id="insertList" parameterType="com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataLogPo">
        insert into  tt_voc_warning_data_log
        (
        vin,
        dt,
        status_name,
        status_value,
        status_change,
        mileage_m,
        mileage_km,
        model_year,
        model
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.vin},
            #{item.dt},
            #{item.statusName},
            #{item.statusValue},
            #{item.statusChange},
            #{item.mileagem},
            #{item.mileageKm},
            #{item.modelYear},
            #{item.model}
            )
        </foreach>

    </insert>


</mapper>