<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="parent_id" property="parentId"/>
        <result column="is_main" property="isMain"/>
        <result column="source_type" property="sourceType"/>
        <result column="vin" property="vin"/>
        <result column="license_plate_num" property="licensePlateNum"/>
        <result column="name" property="name"/>
        <result column="tel" property="tel"/>
        <result column="invite_type" property="inviteType"/>
        <result column="advise_in_date" property="adviseInDate"/>
        <result column="new_advise_in_date" property="newAdviseInDate"/>
        <result column="newest_advise_in_date" property="newestAdviseInDate"/>
        <result column="plan_follow_date" property="planFollowDate"/>
        <result column="actual_follow_date" property="actualFollowDate"/>
        <result column="plan_remind_date" property="planRemindDate"/>
        <result column="actual_remind_date" property="actualRemindDate"/>
        <result column="SA_ID" property="saId"/>
        <result column="SA_NAME" property="saName"/>
        <result column="LAST_SA_ID" property="lastSaId"/>
        <result column="LAST_SA_NAME" property="lastSaName"/>
        <result column="follow_status" property="followStatus"/>
        <result column="is_book" property="isBook"/>
        <result column="book_no" property="bookNo"/>
        <result column="order_status" property="orderStatus"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="sex" property="sex"/>
        <result column="age" property="age"/>
        <result column="model" property="model"/>
        <result column="item_code" property="itemCode"/>
        <result column="item_name" property="itemName"/>
        <result column="item_type" property="itemType"/>
        <result column="part_item_rule_id" property="partItemRuleId"/>
        <result column="last_change_date" property="lastChangeDate"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="is_voc" property="isVoc"/>
        <result column="last_maintenance_date" property="lastMaintenanceDate"/>
        <result column="date_interval" property="dateInterval"/>
        <result column="son_invite_type" property="sonInviteType"/>
        <result column="advise_in_mileage" property="adviseInMileage"/>
        <result column="invite_time" property="inviteTime"/>
        <result column="first_follow_date" property="firstFollowDate"/>
        <result column="order_finish_date" property="orderFinishDate"/>
        <result column="insurance_status" property="insuranceStatus"/>
        <result column="is_joint_guarantee" property="isJointGuarantee"/>
        <result column="total_score" property="score"/>
        <result column="call_length" property="callLength"/>
        <result column="call_detail_id" property="callDetailId"/>
        <result column="call_time" property="callTime"/>
        <result column="close_interval" property="closeInterval"/>
        <result column="content" property="content"/>
        <result column="finish_dealer_code" property="finishDealerCode"/>
        <result column="RO_CREATE_DATE" property="roCreateDate"/>
        <result column="OUT_MILEAGE" property="outMileage"/>
        <result column="repair_type_code" property="repairTypeCode"/>
        <result column="ro_no" property="roNo"/>
        <result column="last_in_date" property="lastInDate"/>
        <result column="daily_average_mileage" property="dailyAverageMileage"/>
        <result column="is_ai" property="isAi"/>
        <result column="record_num" property="recordNum"/>
        <result column="ai_at" property="aiAt"/>
        <result column="order_at" property="orderAt"/>
        <result column="record_at" property="recordAt"/>
        <result column="record_type" property="recordType"/>
        <result column="daily_mile" property="dailyMile"/>
        <result column="verify_status" property="verifyStatus"/>
        <result column="icm_id" property="icmId"/>
        <result column="loss_type" property="lossType"/>
        <result column="loss_warning_type" property="lossWarningType"/>
        <result column="isFlag" property="isFlag"/>
        <result column="bev_flag" property="bevFlag"/>

    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.app_id, t.owner_code, t.owner_par_code, t.org_id, t.id, t.parent_id, t.is_main, t.source_type, t.vin,
        t.license_plate_num, t.name, t.tel, t.invite_type, t.advise_in_date, t.new_advise_in_date,
        t.newest_advise_in_date, t.plan_follow_date, t.actual_follow_date, t.plan_remind_date, t.actual_remind_date,
        t.SA_ID, t.SA_NAME, t.follow_status, t.is_book, t.book_no, t.order_status, t.data_sources, t.is_deleted,
        t.is_valid, t.created_at, t.updated_at, t.sex, t.age, t.model, t.part_item_rule_id, t.last_change_date,
        t.dealer_code,t.item_code,t.item_name,t.item_type,t.first_follow_date,t.order_finish_date,t.content,t.lose_reason,
        t.finish_dealer_code,t.RO_CREATE_DATE,t.OUT_MILEAGE,t.ro_no,t.last_in_date
    </sql>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List2">
        t.app_id, t.owner_code, t.owner_par_code, t.org_id, t.id, t.parent_id, t.is_main, t.source_type, t.vin,
        t.license_plate_num, t.name, t.tel, t.invite_type, t.advise_in_date, t.new_advise_in_date,
        t.newest_advise_in_date, t.plan_follow_date, t.actual_follow_date, t.plan_remind_date, t.actual_remind_date,
        t.SA_ID, t.SA_NAME, t.follow_status, t.is_book, t.book_no, t.order_status, t.data_sources, t.is_deleted,
        t.is_valid, t.created_at, t.updated_at, t.sex, t.age, t.model, t.part_item_rule_id, t.last_change_date,
        t.dealer_code,t.item_code,t.item_name,t.item_type,t.first_follow_date,t.order_finish_date,t.content,t.lose_reason,
        t.finish_dealer_code,t.RO_CREATE_DATE,task.OUT_MILEAGE,t.ro_no,t.last_in_date,task.advise_in_mileage
    </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_vehicle_record t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.parentId !=null and params.parentId != '' ">
            AND t.parent_id = #{params.parentId}
        </if>
        <if test=" params.isMain !=null and params.isMain != '' ">
            AND t.is_main = #{params.isMain}
        </if>
        <if test=" params.sourceType !=null and params.sourceType != '' ">
            AND t.source_type = #{params.sourceType}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num = #{params.licensePlateNum}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.tel !=null and params.tel != '' ">
            AND t.tel = #{params.tel}
        </if>
        <if test=" params.inviteType !=null and params.inviteType != '' ">
            AND t.invite_type = #{params.inviteType}
        </if>
        <if test=" params.adviseInDate !=null and params.adviseInDate != '' ">
            AND t.advise_in_date = #{params.adviseInDate}
        </if>
        <if test=" params.newAdviseInDate !=null and params.newAdviseInDate != '' ">
            AND t.new_advise_in_date = #{params.newAdviseInDate}
        </if>
        <if test=" params.newestAdviseInDate !=null and params.newestAdviseInDate != '' ">
            AND t.newest_advise_in_date = #{params.newestAdviseInDate}
        </if>
        <if test=" params.planFollowDate !=null and params.planFollowDate != '' ">
            AND t.plan_follow_date = #{params.planFollowDate}
        </if>
        <if test=" params.actualFollowDate !=null and params.actualFollowDate != '' ">
            AND t.actual_follow_date = #{params.actualFollowDate}
        </if>
        <if test=" params.planRemindDate !=null and params.planRemindDate != '' ">
            AND t.plan_remind_date = #{params.planRemindDate}
        </if>
        <if test=" params.actualRemindDate !=null and params.actualRemindDate != '' ">
            AND t.actual_remind_date = #{params.actualRemindDate}
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.SA_ID = #{params.saId}
        </if>
        <if test=" params.saName !=null and params.saName != '' ">
            AND t.SA_NAME = #{params.saName}
        </if>
        <if test=" params.lastSaId !=null and params.lastSaId != '' ">
            AND t.LAST_SA_ID = #{params.lastSaId}
        </if>
        <if test=" params.lastSaName !=null and params.lastSaName != '' ">
            AND t.LAST_SA_NAME = #{params.lastSaName}
        </if>
        <if test=" params.followStatus !=null and params.followStatus != '' ">
            AND t.follow_status = #{params.followStatus}
        </if>
        <if test=" params.isBook !=null and params.isBook != '' ">
            AND t.is_book = #{params.isBook}
        </if>
        <if test=" params.bookNo !=null and params.bookNo != '' ">
            AND t.book_no = #{params.bookNo}
        </if>
        <if test=" params.orderStatus !=null and params.orderStatus != '' ">
            AND t.order_status = #{params.orderStatus}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_vehicle_record t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.parentId !=null and params.parentId != '' ">
            AND t.parent_id = #{params.parentId}
        </if>
        <if test=" params.isMain !=null and params.isMain != '' ">
            AND t.is_main = #{params.isMain}
        </if>
        <if test=" params.sourceType !=null and params.sourceType != '' ">
            AND t.source_type = #{params.sourceType}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num = #{params.licensePlateNum}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.tel !=null and params.tel != '' ">
            AND t.tel = #{params.tel}
        </if>
        <if test=" params.inviteType !=null and params.inviteType != '' ">
            AND t.invite_type = #{params.inviteType}
        </if>
        <if test=" params.adviseInDate !=null and params.adviseInDate != '' ">
            AND t.advise_in_date = #{params.adviseInDate}
        </if>
        <if test=" params.newAdviseInDate !=null and params.newAdviseInDate != '' ">
            AND t.new_advise_in_date = #{params.newAdviseInDate}
        </if>
        <if test=" params.newestAdviseInDate !=null and params.newestAdviseInDate != '' ">
            AND t.newest_advise_in_date = #{params.newestAdviseInDate}
        </if>
        <if test=" params.planFollowDate !=null and params.planFollowDate != '' ">
            AND t.plan_follow_date = #{params.planFollowDate}
        </if>
        <if test=" params.actualFollowDate !=null and params.actualFollowDate != '' ">
            AND t.actual_follow_date = #{params.actualFollowDate}
        </if>
        <if test=" params.planRemindDate !=null and params.planRemindDate != '' ">
            AND t.plan_remind_date = #{params.planRemindDate}
        </if>
        <if test=" params.actualRemindDate !=null and params.actualRemindDate != '' ">
            AND t.actual_remind_date = #{params.actualRemindDate}
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.SA_ID = #{params.saId}
        </if>
        <if test=" params.saName !=null and params.saName != '' ">
            AND t.SA_NAME = #{params.saName}
        </if>
        <if test=" params.lastSaId !=null and params.lastSaId != '' ">
            AND t.LAST_SA_ID = #{params.lastSaId}
        </if>
        <if test=" params.lastSaName !=null and params.lastSaName != '' ">
            AND t.LAST_SA_NAME = #{params.lastSaName}
        </if>
        <if test=" params.followStatus !=null and params.followStatus != '' ">
            AND t.follow_status = #{params.followStatus}
        </if>
        <if test=" params.isBook !=null and params.isBook != '' ">
            AND t.is_book = #{params.isBook}
        </if>
        <if test=" params.bookNo !=null and params.bookNo != '' ">
            AND t.book_no = #{params.bookNo}
        </if>
        <if test=" params.orderStatus !=null and params.orderStatus != '' ">
            AND t.order_status = #{params.orderStatus}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>


    <select id="selectInviteVehicleRecord" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO">
        SELECT
        <include refid="Base_Column_List2"/>,f.sa_id as LAST_SA_ID,f.sa_name as
        LAST_SA_NAME,v.is_voc,v.last_maintenance_date,
        case when t.source_type = 4 then tr.daily_mile
        else v.daily_average_mileage
        end as daily_average_mileage,
        datediff(t.advise_in_date,now()) as date_interval,
        tr.record_num,tr.ai_at,tr.order_at,tr.record_at,tr.record_type,tr.loss_type,tr.loss_warning_type,tr.coupon_code,
        tr.coupon_name,tr.verify_status,tr.bev_flag,
        tr.return_intention_level as returnIntentionLevel,
        calld.total_score,calld.call_length,calld.id as call_detail_id,task.close_interval,calld.start_time as call_time
        ,type.repair_type_name as repair_type_code
	<if test=" params.monthTwice !=null and params.monthTwice != '' ">
        ,case when mt.is_ai=0 then '否'
        when mt.is_ai=1 then '是'
        else '' end as is_ai
	</if>
        FROM tt_invite_vehicle_record t
        left join tt_invite_vehicle_task task on t.id=task.invite_id
        left join tt_invite_vehicle_sa_ref f on t.vin=f.vin and t.dealer_code=f.dealer_code
        left join tm_vehicle v on t.vin=v.vin
        left join tm_repair_type type on t.repair_type_code=type.repair_type_code
        left join  tt_voc_invite_vehicle_task_record tr  on  t.id = tr.record_id   and tr.is_deleted = 0
        left join (
        SELECT
        a.total_score,
        b.invite_id,
        a.call_length,
        a.start_time,
        a.id
        FROM
        tt_call_details a
        INNER join
        tt_sa_customer_number b
        on a.call_id = b.call_id
        WHERE not exists(
        select 1 from tt_call_details x
        INNER join tt_sa_customer_number y on x.call_id = y.call_id
        where
        y.invite_id=b.invite_id
        and (ifnull(x.total_score,0)>ifnull(a.total_score,0) or (ifnull(x.total_score,0)=ifnull(a.total_score,0) and
        x.id>a.id)))
        ) calld on calld.invite_id=t.id
	<if test=" params.monthTwice !=null and params.monthTwice != '' ">
        left join tt_invite_vehicle_record_twice mt on t.id = mt.invite_id
	</if>
        WHERE 1=1
        AND t.is_deleted = 0
        <if test=" params.monthTwice !=null and params.monthTwice != '' ">
            AND mt.month = #{params.monthTwice}
        </if>
        <if test=" params.isMain !=null and params.isMain != '' ">
            AND t.is_main = #{params.isMain}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code =#{params.dealerCode}
        </if>
        <if test=" params.dealerCodes !=null and params.dealerCodes.size > 0">
            AND t.dealer_code in
            <foreach collection="params.dealerCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.inviteTypeParam !=null and params.inviteTypeParam.size > 0 ">
            AND
            <if test=" params.overdue !=null and params.overdue != 0 ">
                (
            </if>
            t.invite_type in
            <foreach collection="params.inviteTypeParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test=" params.overdue !=null and params.overdue != 0 ">
                or (t.invite_type=82401001 and t.plan_follow_date &lt; current_timestamp()))
            </if>
        </if>
        <if test="params.inviteTypeParam.size == 0 ">
            AND t.invite_type not in (82381005)
        </if>
        <if test=" params.planFollowDateStart !=null and params.planFollowDateStart != '' ">
            AND t.plan_follow_date &gt;= CONCAT(#{params.planFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.planFollowDateEnd !=null and params.planFollowDateEnd != '' ">
            AND t.plan_follow_date &lt;= CONCAT(#{params.planFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num like "%" #{params.licensePlateNum}"%"
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin like #{params.vin}"%"
        </if>
        <if test=" params.actualFollowDateStart !=null and params.actualFollowDateStart != '' ">
            AND t.actual_follow_date &gt;= CONCAT(#{params.actualFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.actualFollowDateEnd !=null and params.actualFollowDateEnd != '' ">
            AND t.actual_follow_date &lt;= CONCAT(#{params.actualFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.isBook !=null and params.isBook != '' ">
            AND t.is_book = #{params.isBook}
        </if>
        <if test=" params.adviseInDateStart !=null and params.adviseInDateStart != '' ">
            AND t.advise_in_date &gt;= CONCAT(#{params.adviseInDateStart},' 00:00:00')
        </if>
        <if test=" params.adviseInDateEnd !=null and params.adviseInDateEnd != '' ">
            AND t.advise_in_date &lt;= CONCAT(#{params.adviseInDateEnd},' 23:59:59')
        </if>
        <if test=" params.followStatusParam !=null and params.followStatusParam.size > 0 ">
            AND t.follow_status in
            <foreach collection="params.followStatusParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test=" params.isNoDistribute !=null and params.isNoDistribute != '' ">
            and (
          <if test=" params.leaveIds !=null and params.leaveIds.size > 0 ">
              t.sa_id in
              <foreach collection="params.leaveIds" index="index" item="item" open="(" separator="," close=")">
                  #{item}
              </foreach>
              or
          </if>
            not exists(select 1 from tt_invite_sa_allocate_rule_detail a where a.sa_id=t.sa_id and a.dealer_code=t.dealer_code)
            )
        </if>
        <if test=" params.orderStatusParam !=null and params.orderStatusParam.size > 0">
            AND t.order_status in
            <foreach collection="params.orderStatusParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.orderStatus !=null and params.orderStatus != '' ">
            AND t.order_status = #{params.orderStatus}
        </if>
        <if test=" params.saName !=null and params.saName != '' ">
            AND t.SA_NAME like "%" #{params.saName}"%"
        </if>
        <if test=" params.createdAtStart !=null and params.createdAtStart != '' ">
            AND t.created_at &gt;= CONCAT(#{params.createdAtStart},' 00:00:00')
        </if>
        <if test=" params.createdAtEnd !=null and params.createdAtEnd != '' ">
            AND t.created_at &lt;= CONCAT(#{params.createdAtEnd},' 23:59:59')
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.sa_id = #{params.saId}
        </if>
        <if test=" params.isNoDistribute !=null and params.isNoDistribute != '' ">
            AND t.sa_id is null
        </if>
        <if test=" params.recordType !=null  ">
            AND tr.record_type = #{params.recordType}
        </if>
        <if test=" params.recordTypeParam !=null and params.recordTypeParam.size > 0">
            AND tr.record_type in
            <foreach collection="params.recordTypeParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.lossType !=null  ">
            AND tr.loss_type =#{params.lossType}
        </if>
        <if test=" params.lossWarningType !=null  ">
            AND tr.loss_warning_type =#{params.lossWarningType}
        </if>
        <if test="null != params.couponCode and '' != params.couponCode">
            AND tr.coupon_code like #{params.couponCode}"%"
        </if>
        <if test="null != params.couponName and '' != params.couponName">
            AND tr.coupon_name like #{params.couponName}"%"
        </if>
        <if test="params.bevFlag!=null">
            AND tr.bev_flag = #{params.bevFlag}
        </if>

        <if test=" params.returnIntentionLevel !=null and 0 != params.returnIntentionLevel ">
            AND tr.return_intention_level =#{params.returnIntentionLevel} and t.invite_type IN (82381001,82381002,82381006,82381012)
        </if>

        <if test="!params.isPartClue">
            order by case when t.plan_follow_date is null then t.advise_in_date else t.plan_follow_date end asc
        </if>
        <if test="params.isPartClue">
            order by t.vin, t.advise_in_date desc
        </if>
    </select>

    <select id="getInviteVehicleRecordForSaSllocate" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO">
        SELECT
        <include refid="Base_Column_List"/>,f.sa_id as LAST_SA_ID,f.sa_name as
        LAST_SA_NAME,v.is_voc,v.last_maintenance_date,datediff(t.advise_in_date,now()) as date_interval,
        tr.return_intention_level as returnIntentionLevel
        FROM tt_invite_vehicle_record t
        left join tt_invite_vehicle_sa_ref f on t.vin=f.vin and t.dealer_code=f.dealer_code
        left join tm_vehicle v on t.vin=v.vin
        left join  tt_voc_invite_vehicle_task_record tr  on  t.id = tr.record_id   and tr.is_deleted = 0
        WHERE 1=1 and t.is_deleted = 0
        <if test=" params.isMain !=null and params.isMain != '' ">
            AND t.is_main = #{params.isMain}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code =#{params.dealerCode}
        </if>
        <if test=" params.dealerCodes !=null and params.dealerCodes.size > 0">
            AND t.dealer_code in
            <foreach collection="params.dealerCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.inviteTypeParam !=null and params.inviteTypeParam.size > 0 ">
            AND
            <if test=" params.overdue !=null and params.overdue != 0 ">
                (
            </if>
            t.invite_type in
            <foreach collection="params.inviteTypeParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test=" params.overdue !=null and params.overdue != 0 ">
                or (t.invite_type=82401001 and t.plan_follow_date &lt; current_timestamp()))
            </if>
        </if>
        <if test="params.inviteTypeParam.size == 0 ">
            AND t.invite_type not in (82381005)
        </if>
        <if test=" params.planFollowDateStart !=null and params.planFollowDateStart != '' ">
            AND t.plan_follow_date &gt;= CONCAT(#{params.planFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.planFollowDateEnd !=null and params.planFollowDateEnd != '' ">
            AND t.plan_follow_date &lt;= CONCAT(#{params.planFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num like "%" #{params.licensePlateNum}"%"
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin like "%" #{params.vin}"%"
        </if>
        <if test=" params.actualFollowDateStart !=null and params.actualFollowDateStart != '' ">
            AND t.actual_follow_date &gt;= CONCAT(#{params.actualFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.actualFollowDateEnd !=null and params.actualFollowDateEnd != '' ">
            AND t.actual_follow_date &lt;= CONCAT(#{params.actualFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.isBook !=null and params.isBook != '' ">
            AND t.is_book = #{params.isBook}
        </if>
        <if test=" params.adviseInDateStart !=null and params.adviseInDateStart != '' ">
            AND t.advise_in_date &gt;= CONCAT(#{params.adviseInDateStart},' 00:00:00')
        </if>
        <if test=" params.adviseInDateEnd !=null and params.adviseInDateEnd != '' ">
            AND t.advise_in_date &lt;= CONCAT(#{params.adviseInDateEnd},' 23:59:59')
        </if>
        <if test=" params.followStatusParam !=null and params.followStatusParam.size > 0 ">
            AND t.follow_status in
            <foreach collection="params.followStatusParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.isNoDistribute !=null and params.isNoDistribute != '' ">
            and (
            <if test=" params.leaveIds !=null and params.leaveIds.size > 0 ">
                t.sa_id in
                <foreach collection="params.leaveIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or
            </if>
            not exists(select 1 from tt_invite_sa_allocate_rule_detail a where a.sa_id=t.sa_id and a.dealer_code=t.dealer_code)
            )
        </if>
        <if test=" params.orderStatusParam !=null and params.orderStatusParam.size > 0 ">
            AND t.order_status in
            <foreach collection="params.orderStatusParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.orderStatus !=null and params.orderStatus != '' ">
            AND t.order_status = #{params.orderStatus}
        </if>
        <if test=" params.saName !=null and params.saName != '' ">
            AND t.SA_NAME like "%" #{params.saName}"%"
        </if>
        <if test=" params.createdAtStart !=null and params.createdAtStart != '' ">
            AND t.created_at &gt;= CONCAT(#{params.createdAtStart},' 00:00:00')
        </if>
        <if test=" params.createdAtEnd !=null and params.createdAtEnd != '' ">
            AND t.created_at &lt;= CONCAT(#{params.createdAtEnd},' 23:59:59')
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.sa_id = #{params.saId}
        </if>
        <if test=" params.isNoDistribute !=null and params.isNoDistribute != '' ">
            AND t.sa_id is null
        </if>
        <if test=" params.returnIntentionLevel !=null and 0 != params.returnIntentionLevel ">
            AND tr.return_intention_level =#{params.returnIntentionLevel} and t.invite_type IN (82381001,82381002,82381006,82381012)
        </if>
    </select>


    <select id="selectVehicleRecordAndSubcues" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,datediff(t.advise_in_date,now()) as date_interval,
        k.advise_in_mileage,k.invite_time,v.daily_average_mileage
        FROM tt_invite_vehicle_record t
        left join tt_invite_vehicle_task k on t.id=k.invite_id
        left join tm_vehicle v on t.vin=v.vin
        where (t.id=#{id} or t.parent_id=#{id})
    </select>

    <select id="getHasExtendedWarrantBugRecord" resultType="java.lang.Integer">
        SELECT 1
        FROM dual
        WHERE EXISTS
        (SELECT 1
         FROM tt_extended_warranty_purchase_give
         WHERE dealer_code != 'VCDC' AND vin = #{vin})
    </select>

    <select id="exportExcelinviteVehicleRecord" resultType="map">
        SELECT
        t.vin,t.license_plate_num as licensePlateNum,t.name,
        case when t.invite_type=82381001 then '首保'
        when t.invite_type=82381002 then '定保'
        when t.invite_type=82381003 then '续保'
        when t.invite_type=82381004 then 'VOC事故'
        when t.invite_type=82381012 then '流失预警'
        when t.invite_type=82381006 then '流失客户'
        when t.invite_type=82381007 then '召回'
        when t.invite_type=82381008 then '服务活动'
        when t.invite_type=82381009 then '保修'
        when t.invite_type=82381010 then '店端自建'
        when t.invite_type=82381011 then '厂端自建'
        else '' end as inviteTypeName,t.advise_in_date as adviseInDate,t.dealer_code as dealerCode,t.new_advise_in_date
        as newAdviseInDate,
        t.plan_follow_date as planFollowDate,t.actual_follow_date as actualFollowDate,
        t.SA_NAME as saName,
        case when t.follow_status=82401001 then '未跟进'
        when t.follow_status=82401002 then '跟进成功'
        when t.follow_status=82401003 then '跟进失败'
        when t.follow_status=82401004 then '继续跟进'
        when t.follow_status=82401005 then '不需跟进'
        else '' end as followStatusName,
        case when t.is_book=0 then '否'
        when t.is_book=1 then '是'
        else '' end as isBook,
        case when t.order_status=82411001 then '完成'
        when t.order_status=82411002 then '未完成'
        when t.order_status=82411003 then '超时'
        when t.order_status=82411004 then '他店进厂'
        else '' end as orderStatusName,f.sa_name as lastSaName,t.created_at as
        createdAt,calld.total_score as totalScore,calld.call_length as callLength,
        calld.id as callDetailId,calld.start_time as callTime,(
        select GROUP_CONCAT(z.son_invite_type SEPARATOR ',') from
        (select distinct
        case when r.invite_type=82381001 then '首保'
        when r.invite_type=82381002 then '定保'
        when r.invite_type=82381005 then concat('易损件',r.item_code)
        when r.invite_type=82381006 then '流失客户'
        end as son_invite_type,r.parent_id from tt_invite_vehicle_record r where r.is_main=0
        <if test=" params.vin !=null and params.vin != '' ">
            AND r.vin like "%" #{params.vin}"%"
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND r.dealer_code =#{params.dealerCode}
        </if>
        ) z where z.parent_id=t.id group by
        z.parent_id) as sonInviteType,
        t.order_finish_date as orderFinishDate,
        t.content,
        t.lose_reason
        FROM
        tt_invite_vehicle_record t
        left join tt_invite_vehicle_sa_ref f on t.vin=f.vin and t.dealer_code=f.dealer_code
        left join (
        SELECT
        a.total_score,
        b.invite_id,
        a.call_length,
        a.start_time,
        a.id
        FROM
        tt_call_details a
        INNER join
        tt_sa_customer_number b
        on a.call_id = b.call_id
        WHERE not exists(
        select 1 from tt_call_details x
        INNER join tt_sa_customer_number y on x.call_id = y.call_id
        where
        y.invite_id=b.invite_id
        and (ifnull(x.total_score,0)>ifnull(a.total_score,0) or (ifnull(x.total_score,0)=ifnull(a.total_score,0) and
        x.id>a.id)))
        ) calld on calld.invite_id=t.id
        WHERE 1=1
        <if test=" params.isMain !=null and params.isMain != '' ">
            AND t.is_main = #{params.isMain}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.dealerCodes !=null and params.dealerCodes.size > 0 ">
            AND t.dealer_code in
            <foreach collection="params.dealerCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.dealerCodes !=null and params.dealerCodes.size = 0 ">
            and 1=2
        </if>
        <if test=" params.inviteTypeParam !=null and params.inviteTypeParam.size > 0 ">
            AND
            <if test=" params.overdue !=null and params.overdue != 0 ">
                (
            </if>
            t.invite_type in
            <foreach collection="params.inviteTypeParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test=" params.overdue !=null and params.overdue != 0 ">
                or (t.invite_type=82401001 and t.plan_follow_date &lt; current_timestamp()))
            </if>
        </if>
        <if test="params.inviteTypeParam.size == 0 ">
            AND t.invite_type not in (82381005)
        </if>
        <if test=" params.planFollowDateStart !=null and params.planFollowDateStart != '' ">
            AND t.plan_follow_date &gt;= CONCAT(#{params.planFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.planFollowDateEnd !=null and params.planFollowDateEnd != '' ">
            AND t.plan_follow_date &lt;= CONCAT(#{params.planFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num like "%" #{params.licensePlateNum}"%"
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin like "%" #{params.vin}"%"
        </if>
        <if test=" params.actualFollowDateStart !=null and params.actualFollowDateStart != '' ">
            AND t.actual_follow_date &gt;= CONCAT(#{params.actualFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.actualFollowDateEnd !=null and params.actualFollowDateEnd != '' ">
            AND t.actual_follow_date &lt;= CONCAT(#{params.actualFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.isBook !=null and params.isBook != '' ">
            AND t.is_book = #{params.isBook}
        </if>
        <if test=" params.adviseInDateStart !=null and params.adviseInDateStart != '' ">
            AND t.advise_in_date &gt;= CONCAT(#{params.adviseInDateStart},' 00:00:00')
        </if>
        <if test=" params.adviseInDateEnd !=null and params.adviseInDateEnd != '' ">
            AND t.advise_in_date &lt;= CONCAT(#{params.adviseInDateEnd},' 23:59:59')
        </if>
        <if test=" params.followStatusParam !=null and params.followStatusParam.size > 0 ">
            AND t.follow_status in
            <foreach collection="params.followStatusParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.saName !=null and params.saName != '' ">
            AND t.SA_NAME like "%" #{params.saName}"%"
        </if>
        <if test=" params.createdAtStart !=null and params.createdAtStart != '' ">
            AND t.created_at &gt;= CONCAT(#{params.createdAtStart},' 00:00:00')
        </if>
        <if test=" params.createdAtEnd !=null and params.createdAtEnd != '' ">
            AND t.created_at &lt;= CONCAT(#{params.createdAtEnd},' 23:59:59')
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.sa_id = #{params.saId}
        </if>
        <if test=" params.orderStatus !=null and params.orderStatus != '' ">
            AND t.order_status = #{params.orderStatus}
        </if>

        <if test="null != params.couponCode and '' != params.couponCode">
            AND tr.coupon_code = #{params.couponCode}
        </if>
        <if test="null != params.couponName and '' != params.couponName">
            AND tr.coupon_name like #{params.couponName}"%"
        </if>

    </select>

    <!-- 保险跟进 查询-->
    <select id="selectFollowInsureRecord" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO">
        SELECT
        <include refid="Base_Column_List"/>,f.sa_id as LAST_SA_ID,f.sa_name as LAST_SA_NAME,
        tib.insurance_status, tib.is_joint_guarantee
        FROM tt_invite_vehicle_record t
        left join tt_invite_vehicle_sa_ref f on t.vin=f.vin and t.dealer_code=f.dealer_code
        left join (
        select vin, dealer_code, insurance_status,
        case when is_joint_guarantee=10041001
           and SYSDATE() <![CDATA[<=]]> DATE_ADD(created_at,INTERVAL joint_guarantee_year_limit YEAR) then 10041001
        else 10041002 end as is_joint_guarantee
        from tt_insurance_bill b1
        where b1.id = (
            SELECT id from tt_insurance_bill b2 where
            b2.vin = b1.vin and b2.dealer_code = b1.dealer_code ORDER BY b2.created_at desc limit 1
            )
        )
        tib on tib.vin = t.vin and tib.dealer_code = t.dealer_code
        WHERE
        t.invite_type = 82381003
        <if test=" params.followStatusParam !=null and params.followStatusParam.size > 0 ">
            AND ( t.follow_status in
            <foreach collection="params.followStatusParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test=" params.orderStatusParam !=null and params.orderStatusParam.size > 0 ">
                OR t.order_status in
                <foreach collection="params.orderStatusParam" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            )
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.planFollowDateStart !=null and params.planFollowDateStart != '' ">
            AND t.plan_follow_date &gt;= CONCAT(#{params.planFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.planFollowDateEnd !=null and params.planFollowDateEnd != '' ">
            AND t.plan_follow_date &lt;= CONCAT(#{params.planFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num like "%" #{params.licensePlateNum}"%"
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin like "%" #{params.vin}"%"
        </if>
        <if test=" params.actualFollowDateStart !=null and params.actualFollowDateStart != '' ">
            AND t.actual_follow_date &gt;= CONCAT(#{params.actualFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.actualFollowDateEnd !=null and params.actualFollowDateEnd != '' ">
            AND t.actual_follow_date &lt;= CONCAT(#{params.actualFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.SA_ID = #{params.saId}
        </if>
        <if test=" params.adviseInDateStart !=null and params.adviseInDateStart != '' ">
            AND t.advise_in_date &gt;= CONCAT(#{params.adviseInDateStart},' 00:00:00')
        </if>
        <if test=" params.adviseInDateEnd !=null and params.adviseInDateEnd != '' ">
            AND t.advise_in_date &lt;= CONCAT(#{params.adviseInDateEnd},' 23:59:59')
        </if>
        order by case when t.plan_follow_date is null then t.advise_in_date else t.plan_follow_date end asc
    </select>

    <select id="selectInsuranceBill" resultType="com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InsuranceBillDTO">
        select
        id,
		dealer_code as dealerCode,
		insurance_no as insuranceNo,
		insurance_type as insuranceType,
		insurance_status as insuranceStatus,
		license_no as licenseNo,
		vin,
		is_deleted as isDeleted,
		is_valid as isValid
        FROM tt_insurance_bill t
        where 1=1
        and t.is_deleted = 0
        and t.dealer_code=#{dealerCode}
        and t.vin = #{vin}
        AND t.insurance_status in (80721001,80721003)
    </select>

    <select id="exportExcelFollowInsure" resultType="map">
        SELECT
        t.dealer_code as dealerCode,
        t.name,
        t.vin,
        t.license_plate_num as licensePlateNum,
        case when t.invite_type=82381003 then '续保'
        else '' end as inviteTypeName,
        t.advise_in_date as adviseInDate,
        t.new_advise_in_date as newAdviseInDate,
        t.newest_advise_in_date as newestAdviseInDate,
        t.plan_follow_date as planFollowDate,
        t.actual_follow_date as actualFollowDate,
        t.SA_NAME as saName,
        case when t.follow_status=82401001 then '未跟进'
        when t.follow_status=82401002 then '成功跟进'
        when t.follow_status=82401003 then '失败跟进'
        when t.follow_status=82401004 then '继续跟进'
        when t.follow_status=82401005 then '不需跟进'
        else '' end as followStatusName,
        case when t.is_book=0 then '否'
        when t.is_book=1 then '是'
        else '' end as isBook,
        case when t.order_status=82411001 then '完成'
        when t.order_status=82411002 then '未完成'
        when t.order_status=82411003 then '超时'
        when t.order_status=82411004 then '他店进厂'
        else '' end as orderStatusName,f.sa_name as lastSaName,t.created_at as createdAt,
        case when tib.insurance_status=80721001 then '未完成'
        when tib.insurance_status=80721003 then '已完成'
        when tib.insurance_status=80721004 then '已作废'
        else '' end as insuranceStatus,
        case when tib.is_joint_guarantee=10041001 then '是'
        when tib.is_joint_guarantee=10041002 then '否'
        else '' end as isJointGuarantee
        FROM
        tt_invite_vehicle_record t
        left join tt_invite_vehicle_sa_ref f on t.vin=f.vin and t.dealer_code=f.dealer_code
        left join (
        select vin, dealer_code, insurance_status,
        case when is_joint_guarantee=10041001
        and SYSDATE() <![CDATA[<=]]> DATE_ADD(created_at,INTERVAL joint_guarantee_year_limit YEAR) then 10041001
        else 10041002 end as is_joint_guarantee
        from tt_insurance_bill b1
        where b1.id = (
        SELECT id from tt_insurance_bill b2 where
        b2.vin = b1.vin and b2.dealer_code = b1.dealer_code ORDER BY b2.created_at desc limit 1
        )
        )
        tib on tib.vin = t.vin and tib.dealer_code = t.dealer_code
        WHERE
        t.invite_type = 82381003
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code like "%" #{params.dealerCode}"%"
        </if>
        <if test=" params.planFollowDateStart !=null and params.planFollowDateStart != '' ">
            AND t.plan_follow_date &gt;= CONCAT(#{params.planFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.planFollowDateEnd !=null and params.planFollowDateEnd != '' ">
            AND t.plan_follow_date &lt;= CONCAT(#{params.planFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num like "%" #{params.licensePlateNum}"%"
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin like "%" #{params.vin}"%"
        </if>
        <if test=" params.actualFollowDateStart !=null and params.actualFollowDateStart != '' ">
            AND t.actual_follow_date &gt;= CONCAT(#{params.actualFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.actualFollowDateEnd !=null and params.actualFollowDateEnd != '' ">
            AND t.actual_follow_date &lt;= CONCAT(#{params.actualFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.followStatusParam !=null and params.followStatusParam.size > 0 ">
            AND (t.follow_status in
            <foreach collection="params.followStatusParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test=" params.orderStatusParam !=null and params.orderStatusParam.size > 0 ">
                OR t.order_status in
                <foreach collection="params.orderStatusParam" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            )
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.SA_ID = #{params.saId}
        </if>
        <if test=" params.adviseInDateStart !=null and params.adviseInDateStart != '' ">
            AND t.advise_in_date &gt;= CONCAT(#{params.adviseInDateStart},' 00:00:00')
        </if>
        <if test=" params.adviseInDateEnd !=null and params.adviseInDateEnd != '' ">
            AND t.advise_in_date &lt;= CONCAT(#{params.adviseInDateEnd},' 23:59:59')
        </if>
        order by case when t.plan_follow_date is null then t.advise_in_date else t.plan_follow_date end asc
    </select>


    <select id="queryInviteRecordForTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM tt_invite_vehicle_record t
        where 1=1
        and t.source_type=1
        and t.is_main = 1
        and t.dealer_code=#{dealerCode}
        and t.vin=#{vin}
        and t.id!=#{id}
        and t.invite_type in (82381001,82381002,82381012,82381006,82381007,82381009)
        and t.follow_status in (82401001,82401004)
        and t.order_status = 82411002
        and date_sub(t.advise_in_date,interval #{mergeRule} day)&lt;=#{adviseInDate}
        and date_add(t.advise_in_date,interval #{mergeRule} day)&gt;=#{adviseInDate}
        order by advise_in_date asc
    </select>

    <select id="queryWaitAllocationRecodeForOther" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,f.sa_id as LAST_SA_ID,f.sa_name as LAST_SA_NAME
        FROM tt_invite_vehicle_record t
        left join tt_invite_vehicle_sa_ref f on t.vin=f.vin and t.dealer_code=f.dealer_code
        where 1=1
        and t.source_type in (1,4)
        and t.is_main = 1
        and t.sa_id is null
        and t.invite_type in (82381001,82381002,82381005,82381006,82381012)
        and t.created_at&gt;=CONCAT(#{createDate},' 00:00:00')
        and t.created_at&lt;=CONCAT(#{createDate},' 23:59:59')
    </select>

    <select id="queryWaitAllocationRecodeForVin" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,f.sa_id as LAST_SA_ID,f.sa_name as LAST_SA_NAME
        FROM tt_invite_vehicle_record t
        left join tt_invite_vehicle_sa_ref f on t.vin=f.vin and t.dealer_code=f.dealer_code
        where 1=1
        and t.is_deleted = 0
        and t.is_main = 1
        and t.sa_id is null
        and t.invite_type in (82381001,82381002,82381012,82381006)
        <if test=" vin !=null and vin !='' ">
        and t.vin = #{vin}
        </if>
    </select>

    <select id="queryWaitAllocationRecodeForInsurance" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,f.sa_id as LAST_SA_ID,f.sa_name as LAST_SA_NAME
        FROM tt_invite_vehicle_record t
        left join tt_invite_vehicle_sa_ref f on t.vin=f.vin and t.dealer_code=f.dealer_code
        where 1=1
        and t.source_type=1
        and t.is_main = 1
        and t.sa_id is null
        and t.invite_type =82381003 
        and t.created_at&gt;=CONCAT(#{createDate},' 00:00:00')
        and t.created_at&lt;=CONCAT(#{createDate},' 23:59:59')
    </select>

    <select id="queryInviteSubtextByid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM tt_invite_vehicle_record t
        where 1=1
        and t.parent_id=#{id}
        and t.source_type in (1,2,3)
        order by t.advise_in_date asc
    </select>

    <select id="findCluesToClose" resultMap="BaseResultMap">
        select t1.id,t1.dealer_code,t2.icm_id,t1.order_status,t1.follow_status,t2.verify_status,t1.source_type
        from tt_invite_vehicle_record t1
        left join tt_voc_invite_vehicle_task_record t2 on t1.id = t2.record_id
        where t1.is_deleted = 0
          and t1.invite_type in (82381001, 82381002)
          and t1.order_status = 82411002
          and t1.advise_in_date >= #{startDate}
          and t1.advise_in_date &lt;= #{endDate}
    </select>

    <select id="getExpiredPendingLeads" resultMap="BaseResultMap">
        select t1.id,t1.follow_status,t1.order_status,t1.dealer_code,t2.vin,t2.icm_id,t1.source_type
               from tt_invite_vehicle_record  t1
               join tt_voc_invite_vehicle_task_record t2 on t2.record_id = t1.id
        WHERE t2.verify_status = 87911002
          AND t2.verify_time &lt; DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
          AND t1.invite_type in (82381001,82381002,82381006,82381012)
    </select>

    <select id="selectInviteClue" parameterType="com.yonyou.dmscus.customer.entity.dto.clue.InviteClueParamDTO" resultType="com.yonyou.dmscus.customer.entity.dto.clue.InviteClueResultDTO">
        select t1.id,t1.app_id,t1.owner_code,t1.source_type,t1.vin,t1.license_plate_num,t1.name,t1.tel,t1.invite_type,t1.advise_in_date,
               t1.plan_follow_date,t1.actual_follow_date,t1.SA_ID,t1.SA_NAME,t1.LAST_SA_ID,t1.LAST_SA_NAME,t1.follow_status,t1.is_book,
               t1.book_no,t1.order_status,t1.data_sources,t1.dealer_code,t1.age,t1.sex,t1.model,t1.RO_NO,t1.REPAIR_TYPE_CODE,t1.OUT_MILEAGE,
               t1.RO_CREATE_DATE,t1.finish_dealer_code,t2.record_num,t2.record_type,t2.loss_type,t2.icm_id,t2.coupon_id,t2.coupon_code,
               t2.coupon_name,t2.detail_id,t2.exchange_owner_code,t2.exchange_ro_no,t2.coupon_state,t2.coupon_state_time,t2.daily_mile,
               t2.verify_status,t2.loss_warning_type,t2.verify_time,t1.created_at
               from tt_invite_vehicle_record t1
        left join tt_voc_invite_vehicle_task_record t2 on t1.id = t2.record_id
        <where>
                and t1.is_deleted = 0
            <if test=" params.vin !=null and params.vin.size > 0 ">
                and t1.vin in
                <foreach collection="params.vin" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" params.verifyStatus !=null and params.verifyStatus.size > 0 ">
                and t2.verify_status in
                <foreach collection="params.verifyStatus" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" params.orderStatus !=null and params.orderStatus.size > 0 ">
                and t1.order_status in
                <foreach collection="params.orderStatus" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" params.inviteType !=null and params.inviteType.size > 0 ">
                and t1.invite_type in
                <foreach collection="params.inviteType" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" params.followStatus !=null and params.followStatus.size > 0 ">
                and t1.follow_status in
                <foreach collection="params.followStatus" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" params.sourceType !=null and params.sourceType.size > 0 ">
                and t1.source_type in
                <foreach collection="params.sourceType" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t1.dealer_code = #{params.dealerCode}
            </if>
            <if test=" params.adviseStartDate !=null and params.adviseStartDate != '' ">
                AND t1.advise_in_date >= #{params.adviseStartDate}
            </if>
            <if test=" params.adviseEndDate !=null and params.adviseEndDate != '' ">
                AND t1.advise_in_date &lt;= #{params.adviseEndDate}
            </if>
        </where>
    </select>

    <select id="selectNewInviteClue" resultType="com.yonyou.dmscus.customer.entity.dto.clue.InviteClueResultDTO">
        select t1.id,t1.app_id,t1.owner_code,t1.source_type,t1.vin,t1.license_plate_num,t1.name,t1.tel,t1.invite_type,t1.advise_in_date,
        t1.plan_follow_date,t1.actual_follow_date,t1.SA_ID,t1.SA_NAME,t1.LAST_SA_ID,t1.LAST_SA_NAME,t1.follow_status,t1.is_book,
        t1.book_no,t1.order_status,t1.data_sources,t1.dealer_code,t1.age,t1.sex,t1.model,t1.RO_NO,t1.REPAIR_TYPE_CODE,t1.OUT_MILEAGE,
        t1.RO_CREATE_DATE,t1.finish_dealer_code,t2.record_num,t2.record_type,t2.loss_type,t2.icm_id,t2.coupon_id,t2.coupon_code,
        t2.coupon_name,t2.detail_id,t2.exchange_owner_code,t2.exchange_ro_no,t2.coupon_state,t2.coupon_state_time,t2.daily_mile,
        t2.verify_status,t2.loss_warning_type,t2.verify_time,t1.created_at
        from tt_invite_vehicle_record t1
        left join tt_voc_invite_vehicle_task_record t2 on t1.id = t2.record_id
        <where>
            and t1.is_deleted = 0
            and t1.vin = #{vin}
            <if test=" dealerCode !=null and dealerCode != '' ">
                AND t1.dealer_code = #{dealerCode}
            </if>
            <if test=" leadsType !=null and leadsType == 101 ">
                and t1.invite_type in (82381001,82381002,82381006,82381012)
            </if>
        </where>
        order by t1.created_at desc
        limit 1
    </select>

    <select id="selectIdByLimit" resultType="com.yonyou.dmscus.customer.entity.dto.clue.dictionary.DictionaryIdDTO">
        select min(a.id) as minId, max(a.id) as maxId
        from (
                 select id
                 from tt_invite_vehicle_record
                 order by id LIMIT #{startPage},#{endPage}
             ) a
    </select>

    <select id="findMissingExtendedDataByClue" resultType="com.yonyou.dmscus.customer.entity.dto.clue.InviteClueResultDTO">
        SELECT t1.id,t1.vin,t1.invite_type,t1.order_status
        FROM tt_invite_vehicle_record t1
        LEFT JOIN tt_voc_invite_vehicle_task_record t2 ON t1.id = t2.record_id
        WHERE t2.record_id IS NULL
        AND t1.invite_type IN (82381001, 82381002, 82381006, 82381012)
    </select>

    <update id="updateMainInvite">
        UPDATE tt_invite_vehicle_record t
            set is_main=1,parent_id=null
        where
        t.id = #{id}
        and t.source_type in (1,2,3)
    </update>

    <update id="updateOrderStatusById">
        UPDATE tt_invite_vehicle_record t
        set order_status = #{orderStatus}, updated_at = now()
        where t.id = #{id}
    </update>

    <update id="updateIsAi">
        UPDATE tt_invite_vehicle_record_twice
        SET is_ai = 1
        WHERE
            MONTH = DATE_FORMAT( now(), '%Y-%m' )
            AND is_ai = 0
            AND invite_id IN (
            SELECT
                b.`invite_id`
            FROM
                tt_sa_customer_number b
                LEFT JOIN tt_call_details a ON a.call_id = b.call_id
            WHERE
                a.call_length >=0
                AND DATE_FORMAT( a.start_time, '%Y-%m' ) = DATE_FORMAT( now(), '%Y-%m' )
            ORDER BY
            a.created_at DESC
	    )
  </update>

    <update id="updateSubtextInvite">
        UPDATE tt_invite_vehicle_record t
        set parent_id=#{mainId}
        where
            t.id = #{id}
        and t.source_type in (1,2,3)
    </update>

    <select id="getWaitCloseMaintainRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_vehicle_record t
        where 1=1
        and t.vin=#{vin}
        and t.invite_type in (82381001,82381002)
        and t.order_status = 82411002
    </select>


    <select id="getWaitCloseGuaranteeRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_vehicle_record t
        left join tm_vehicle v on t.vin = v.vin
        where 1=1
        and t.invite_type = 82381009
        and t.order_status = 82411002
        and v.wrt_end_date&lt;=STR_TO_DATE(#{createDate},'%Y-%m-%d')
    </select>

    <select id="getWaitCloseGuaranteeRecordByVin" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_vehicle_record t
        where 1=1
        and t.vin=#{vin}
        and t.invite_type = 82381009
        and t.order_status = 82411002
    </select>

    <select id="getWaitCloseVocAccidentRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_vehicle_record t
        where 1=1
        and t.vin=#{vin}
        and t.invite_type = 82381004
        and t.order_status = 82411002
        and t.advise_in_date&lt;=STR_TO_DATE(#{createDate},'%Y-%m-%d')
    </select>



    <select id="getWaitCloseLostRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_vehicle_record t
        where 1=1
        and t.vin=#{vin}
        <if test=" dealerCode !=null and dealerCode != '' ">
            and t.dealer_code=#{dealerCode}
        </if>
        and t.invite_type =82381006
        and t.order_status = 82411002
    </select>

    <select id="getWaitCloseAlertRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_vehicle_record t
        where 1=1
        and t.vin=#{vin}
        <if test=" dealerCode !=null and dealerCode != '' ">
            and t.dealer_code=#{dealerCode}
        </if>
        and t.invite_type =82381012
        and t.order_status = 82411002
    </select>

    <select id="getWaitCloseVulnerableRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_vehicle_record t
        where 1=1
              and t.vin=#{vin}
              and t.item_code=#{code}
              and t.item_type=#{type}
              and t.invite_type = 82381005
              and t.order_status = 82411002
    </select>

    <select id="getLatestError" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_invite_vehicle_record t
        where 1=1
        and t.vin=#{vin}
        and t.dealer_code=#{dealerCode}
        and t.invite_type in (82381001,82381002,82381006,82381012)
        order by t.created_at desc
        limit 0,1
    </select>

    <update id="updateInviteByDealerCode">
        update tt_invite_vehicle_record
        set dealer_code=#{dealerCode},SA_ID=null,SA_NAME=null,actual_follow_date=null,follow_status=82401001
            ,is_book=0,book_no=null
        where
            vin= #{vin}
            and order_status = 82411002
            and dealer_code=#{lastDealerCode}
    </update>

    <select id="getNeedDistribute" resultType="java.lang.Integer">
        SELECT count(id)
        FROM tt_invite_vehicle_record t
        WHERE 1=1
        and is_main=1
        and t.IS_DELETED = 0
        and order_status = 82411002
        AND t.dealer_code=#{dealerCode}
            and (
            <if test=" leaveIds !=null and leaveIds.size > 0 ">
                t.sa_id in
                <foreach collection="leaveIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or
            </if>
            not exists(select 1 from tt_invite_sa_allocate_rule_detail a where a.sa_id=t.sa_id and a.dealer_code=t.dealer_code)
            )
            AND t.sa_id is null
    </select>

    <update id="updateInsureFollowStatus">
        update tt_invite_vehicle_record t,tt_invite_vehicle_task tv
        set t.order_status = 82411003,
        t.updated_at = SYSDATE(),
		t.updated_by = #{userId},
		tv.close_times = tv.close_times + 1,
        tv.updated_at = SYSDATE(),
        tv.updated_by = #{userId}
        where t.id = tv.invite_id
        <if test=" idList !=null and idList.size() > 0 ">
            and t.id in
            <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <!-- t.vin in (select DISTINCT vin from
        tt_insurance_bill where NOW() <![CDATA[>]]> date_add(vi_finish_date, INTERVAL 30 DAY) and is_deleted = 0) -->
    <select id="selectInsuranceInvitePlan" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_vehicle_record t
        left join
        tt_invite_vehicle_record_detail b on t.id=b.invite_id
        where
        t.invite_type=82381003
        and t.follow_status != 82401002
        and t.order_status = 82411002
        and NOW() <![CDATA[>]]> date_add(t.advise_in_date, INTERVAL 30 DAY)
        and (b.lose_reason != 81851006 or b.invite_id is null)
        and
        not exists (select 1 from tt_insurance_bill tb where tb.vin = t.vin and tb.is_deleted = 0)
    </select>

    <insert id="insertInsureInviteTask">
        INSERT INTO tt_invite_vehicle_task (vin, license_plate_num, owner_code, dealer_code, name, tel,
        age, sex, model, daily_mileage, advise_in_date_update_time, invite_type, day_in_advance,
        remind_interval,close_interval,
        advise_in_date,
        create_invite_time,
        is_create_invite,
        close_times,
        created_at,created_by)
		select vin, license_plate_num, owner_code, dealer_code, name, tel,
        age, sex, model, daily_mileage, advise_in_date_update_time, invite_type, day_in_advance,
        remind_interval,close_interval,
        date_add(advise_in_date, INTERVAL 1 YEAR),
        date_add(create_invite_time, INTERVAL 1 YEAR),
        0,
        0,
		CURRENT_TIMESTAMP, #{userId}
		from tt_invite_vehicle_task where 1=1
        <if test=" idList !=null and idList.size() > 0 ">
            and invite_id in
            <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </insert>

    <insert id="batchInsert">
        insert into tt_invite_vehicle_record(
             app_id,
             owner_code,
             source_type,
             vin,
             license_plate_num,
             name,
             tel,
             invite_type,
             advise_in_date,
             follow_status,
             order_status,
             created_by,
             created_at,
             record_version,
             dealer_code,
             item_name
		)
        SELECT 'volvo',
                t.dealer_code,
                2,
                vin,
                license_plate_num,
                name,
                tel,
                82381010,
                advise_in_date,
                82401001,
                82411002,
                t.created_by,
                now(),
                1,
                t.dealer_code,
                t.invite_name
        from tt_invite_vehicle_dealer_task_import t
        where t.created_by=#{userId}
    </insert>

    <insert id="batchVCDCInsert">
        insert into tt_invite_vehicle_record(
            app_id,
            owner_code,
            source_type,
            vin,
            license_plate_num,
            name,
            tel,
            invite_type,
            advise_in_date,
            follow_status,
            order_status,
            created_by,
            created_at,
            record_version,
            dealer_code,
            item_name
        )
            SELECT 'volvo',
                t.dealer_code,
                2,
                vin,
                license_plate_num,
                name,
                tel,
                82381011,
                advise_in_date,
                82401001,
                82411002,
                t.created_by,
                now(),
                1,
                t.dealer_code,
                invite_name
            from tt_invite_vehicle_dealer_task_import t
            where t.created_by=#{userId}
    </insert>


    <update id="updateOneIdByMobile">
        update tt_invite_vehicle_record
         SET one_id=#{params.id}
        where
            created_at >= date_sub(now(), interval 1 hour)
            and tel=#{params.mobile}
    </update>

    <update id="batchUpdateOneIdByMobile">
        update tt_invite_vehicle_record
        set one_id = case
        <foreach collection="list" item="item" separator="">
            when tel = #{item.mobile} then #{item.id}
        </foreach>
        end
        where tel in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.mobile}
        </foreach>
        and created_at >= date_sub(now(), interval 1 hour)
    </update>

    <update id="updateFordailyAverageMileageById">
        update tt_invite_vehicle_record
        set advise_in_date=#{params.adviseInDate},
            updated_at=#{params.updatedAt}
        where id=#{params.id}
        and order_status = 82411002
    </update>

    <update id="updateFordailyAverageMileageByIds">
        <foreach collection="params" item="item" index="index" open="" close="" separator=";">
        update tt_invite_vehicle_record
        set advise_in_date=#{item.adviseInDate},
            updated_at=#{item.updatedAt}
        where id=#{item.id}
          and order_status = 82411002
          and source_type in (1,2,3)
        </foreach>
    </update>

    <update id="completeLeadsByIds">
        <foreach collection="listUpPo" item="item" index="index" open="" close="" separator=";">
            update tt_invite_vehicle_record
            set
            order_finish_date = #{item.orderFinishDate},
            ro_no = #{item.roNo},
            RO_CREATE_DATE = #{item.roCreateDate},
            repair_type_code = #{item.repairTypeCode},
            finish_dealer_code = #{item.finishDealerCode},
            OUT_MILEAGE = #{item.outMileage},
            order_status = #{item.orderStatus}
            where id = #{item.id}
            and order_status = 82411002
        </foreach>
    </update>

    <update id="doProDataUpdateClue">
        <foreach collection="params" item="item" index="index" open="" close="" separator=";">
            update tt_invite_vehicle_record
            set
            name=#{item.name},
            tel=#{item.tel},
            sex=#{item.sex},
            age=#{item.age},
            updated_at = now()
            where id = #{item.id}
        </foreach>
    </update>

    <update id="doProDataCloseClue">
            update tt_invite_vehicle_record
            set
            dealer_code = CONCAT(dealer_code,"V"),
            is_deleted = 1,
            updated_at = now()
            where id in
        <foreach collection="params" index="index" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="doProDataCloseSubClue">
        update tt_invite_vehicle_record
        set
        dealer_code = CONCAT(dealer_code,"V"),
        is_deleted = 1,
        updated_at = now()
        where parent_id in
        <foreach collection="params" index="index" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
        and is_main = 0
    </update>

    <update id="closeLeadById">
        update tt_invite_vehicle_record
        set
        order_status = #{orderStatus},
        updated_at = now()
        where id in
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and order_status = 82411002
        and is_deleted = 0
    </update>
    <sql id="exportExcelinviteVehicleCommonSql">
        SELECT
        t.vin,t.license_plate_num as licensePlateNum,t.name,
        tr.record_num as recordNum,tr.ai_at as aiAt,tr.order_at as orderAt,tr.record_at as recordAt,
        tr.coupon_code AS couponCode,tr.coupon_name AS couponName,
        case when tr.bev_flag = 0 then '否'
        when tr.bev_flag = 1 then '是'
        else '' end as bevFlag,
        case when tr.loss_type = 0 then  ''
        when tr.loss_type = 87901001 then  '18个月未保养'
        when tr.loss_type = 87901002 then '流失客户*'
        when tr.loss_type = 1 then  '18个月未保养'
        when tr.loss_type = 2 then '流失客户*'
        when tr.loss_type = 3 then '流失客户保养灯亮'
        else '' end as "lossType",
        case when tr.loss_warning_type = 0 then  ''
        when tr.loss_warning_type = 87902001 then  '3个月流失预警'
        when tr.loss_warning_type = 87902002 then '保养灯流失预警'
        else '' end as "lossWarningType",
        case when tr.verify_status = 0 then  ''
        when tr.verify_status = 87911001 then  '未开始'
        when tr.verify_status = 87911002 then '待验证'
        when tr.verify_status = 87911003 then '已验证'
        else '' end as "verifyStatus",
        case when tr.record_type = 87891001 then '普通线索'
        when tr.record_type = 87891002 then 'VOC线索'
        when tr.record_type = 87891003 then '保养灯线索'
        when tr.record_type =1 then '普通线索'
        when tr.record_type =0 then 'VOC线索'
        else '普通线索' end as recordType,
        case when tr.return_intention_level = 3 then  '三星'
        when tr.return_intention_level = 4 then  '四星'
        when tr.return_intention_level = 5 then '五星'
        else '' end as "returnIntentionLevel",
        case when t.invite_type=82381001 then '首保'
        when t.invite_type=82381002 then '定保'
        when t.invite_type=82381003 then '续保'
        when t.invite_type=82381004 then 'VOC事故'
        when t.invite_type=82381012 then '流失预警'
        when t.invite_type=82381006 then '流失客户'
        when t.invite_type=82381007 then '召回'
        when t.invite_type=82381008 then '服务活动'
        when t.invite_type=82381009 then '保修'
        when t.invite_type=82381010 then '店端自建'
        when t.invite_type=82381011 then '厂端自建'
        when t.invite_type=82381013 then '商城零附件'
        else '' end as inviteTypeName,DATE_FORMAT(t.advise_in_date,'%Y-%m-%d') as adviseInDate,t.dealer_code as
        dealerCode,t.new_advise_in_date as newAdviseInDate, DATE_FORMAT(t.plan_follow_date,'%Y-%m-%d') as planFollowDate,
        DATE_FORMAT(t.actual_follow_date,'%Y-%m-%d') as actualFollowDate,t.SA_NAME as saName,
        case when t.follow_status=82401001 then '未跟进'
        when t.follow_status=82401002 then '跟进成功'
        when t.follow_status=82401003 then '跟进失败'
        when t.follow_status=82401004 then '继续跟进'
        when t.follow_status=82401005 then '不需跟进'
        else '' end as followStatusName,
        case when t.is_book=0 then '否'
        when t.is_book=1 then '是'
        else '' end as isBook,
        case when t.order_status=82411001 then '自店完成'
        when t.order_status=82411002 then '未完成'
        when t.order_status=82411003 then '逾期关闭'
        when t.order_status=82411004 then '他店完成'
        when t.order_status=82411006 then '流失关闭'
        else '' end as orderStatusName,f.sa_name as lastSaName,t.created_at as
        createdAt,calld.total_score as totalScore,calld.call_length as callLength,
        calld.id as callDetailId,DATE_FORMAT(calld.start_time,'%Y-%m-%d %H:%i:%s') as callTime,
        DATE_FORMAT(t.order_finish_date,'%Y-%m-%d') as orderFinishDate,
        t.finish_dealer_code as finishDealerCode,
        t.item_name as itemName,
        t.content,
        case when t.lose_reason=82431001 then '多次电话未接通'
        when t.lose_reason=82431002 then '无人接听'
        when t.lose_reason=82431003 then '电话关机'
        when t.lose_reason=82431004 then '号码错误'
        when t.lose_reason=82431005 then '客户拒绝'
        when t.lose_reason=82431006 then '他店进厂'
        when t.lose_reason=82431007 then '其他'
        when t.lose_reason=82431008 then '外地使用'
        when t.lose_reason=82431009 then '车辆已卖'
        when t.lose_reason=82431010 then '无正确联系方式'
        when t.lose_reason=82431011 then '同城他店客户'
        when t.lose_reason=82431012 then '异地用车'
        when t.lose_reason=82431013 then '修理厂保养'
        when t.lose_reason=82431014 then '报废'
        when t.lose_reason=82431015 then '里程差异超过三个月'
	when t.lose_reason=82431016 then '客户拒绝，未说原因'
        else '' end as loseReason,
        case when t.item_code=83841001 then '车已进厂'
        when t.item_code=83841002 then '联系方式缺失'
        when t.item_code=83841003 then '工作号故障/缺失'
        when t.item_code=83841004 then '集团线索重复'
        when t.item_code=83841005 then '历史工单里程错误'
	when t.item_code=83841006 then '客户已主动预约（有预约单）'
        else '' end as itemCode,
        case when t.item_type=83851001 then '客户拒绝邀约'
        when t.item_type=83851002 then '代步车'
        when t.item_type=83851003 then '车已报废'
        when t.item_type=83851004 then '大客户'
        when t.item_type=83851005 then '无异常'
        when t.item_type=83851006 then '试驾车'
        else '' end as itemType
	<if test=" params.monthTwice !=null and params.monthTwice != '' ">
        ,case when mt.is_ai=0 then '否'
        when mt.is_ai=1 then '是'
        else '' end as isAi
	</if>
        FROM
        tt_invite_vehicle_record t
        left join  tt_voc_invite_vehicle_task_record tr  on  t.id = tr.record_id   and tr.is_deleted = 0
        left join tt_invite_vehicle_sa_ref f on t.vin=f.vin and t.dealer_code=f.dealer_code
        left join (
        SELECT
        a.total_score,
        b.invite_id,
        a.call_length,
        a.start_time,
        a.id
        FROM
        tt_call_details a
        INNER join
        tt_sa_customer_number b
        on a.call_id = b.call_id
        WHERE not exists(
        select 1 from tt_call_details x
        INNER join tt_sa_customer_number y on x.call_id = y.call_id
        where
        y.invite_id=b.invite_id
        and (ifnull(x.total_score,0)>ifnull(a.total_score,0) or (ifnull(x.total_score,0)=ifnull(a.total_score,0) and
        x.id>a.id)))
        ) calld on calld.invite_id=t.id
	<if test=" params.monthTwice !=null and params.monthTwice != '' ">
        left join tt_invite_vehicle_record_twice mt on t.id = mt.invite_id
	</if>
        WHERE 1=1
        AND t.is_deleted = 0
        <if test=" params.monthTwice !=null and params.monthTwice != '' ">
            AND mt.month = #{params.monthTwice}
        </if>
        <if test=" params.isMain !=null and params.isMain != '' ">
            AND t.is_main = #{params.isMain}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.dealerCodes !=null and params.dealerCodes.size > 0 ">
            AND t.dealer_code in
            <foreach collection="params.dealerCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.dealerCodes !=null and params.dealerCodes.size = 0 ">
            and 1=2
        </if>
        <if test=" params.inviteTypeParam !=null and params.inviteTypeParam.size > 0 ">
            AND
            <if test=" params.overdue !=null and params.overdue != 0 ">
                (
            </if>
            t.invite_type in
            <foreach collection="params.inviteTypeParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test=" params.overdue !=null and params.overdue != 0 ">
                or (t.invite_type=82401001 and t.plan_follow_date &lt; current_timestamp()))
            </if>
        </if>
        <if test="params.inviteTypeParam.size == 0 ">
            AND t.invite_type not in (82381005)
        </if>
        <if test=" params.planFollowDateStart !=null and params.planFollowDateStart != '' ">
            AND t.plan_follow_date &gt;= CONCAT(#{params.planFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.planFollowDateEnd !=null and params.planFollowDateEnd != '' ">
            AND t.plan_follow_date &lt;= CONCAT(#{params.planFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num like "%" #{params.licensePlateNum}"%"
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin like "%" #{params.vin}"%"
        </if>
        <if test=" params.actualFollowDateStart !=null and params.actualFollowDateStart != '' ">
            AND t.actual_follow_date &gt;= CONCAT(#{params.actualFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.actualFollowDateEnd !=null and params.actualFollowDateEnd != '' ">
            AND t.actual_follow_date &lt;= CONCAT(#{params.actualFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.isBook !=null and params.isBook != '' ">
            AND t.is_book = #{params.isBook}
        </if>
        <if test=" params.adviseInDateStart !=null and params.adviseInDateStart != '' ">
            AND t.advise_in_date &gt;= CONCAT(#{params.adviseInDateStart},' 00:00:00')
        </if>
        <if test=" params.adviseInDateEnd !=null and params.adviseInDateEnd != '' ">
            AND t.advise_in_date &lt;= CONCAT(#{params.adviseInDateEnd},' 23:59:59')
        </if>
        <if test=" params.followStatusParam !=null and params.followStatusParam.size > 0 ">
            AND t.follow_status in
            <foreach collection="params.followStatusParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.saName !=null and params.saName != '' ">
            AND t.SA_NAME like "%" #{params.saName}"%"
        </if>
        <if test=" params.createdAtStart !=null and params.createdAtStart != '' ">
            AND t.created_at &gt;= CONCAT(#{params.createdAtStart},' 00:00:00')
        </if>
        <if test=" params.createdAtEnd !=null and params.createdAtEnd != '' ">
            AND t.created_at &lt;= CONCAT(#{params.createdAtEnd},' 23:59:59')
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.sa_id = #{params.saId}
        </if>
        <if test=" params.orderStatus !=null and params.orderStatus != '' ">
            AND t.order_status = #{params.orderStatus}
        </if>
        <if test=" params.orderStatusParam !=null and params.orderStatusParam.size > 0 ">
            AND t.order_status in
            <foreach collection="params.orderStatusParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.recordType !=null  ">
            AND tr.record_type =#{params.recordType}
        </if>

        <if test="null != params.couponCode and '' != params.couponCode">
            AND tr.coupon_code like #{params.couponCode}"%"
        </if>
        <if test="null != params.couponName and '' != params.couponName">
            AND tr.coupon_name like #{params.couponName}"%"
        </if>
        <if test=" params.lossType !=null  ">
            AND tr.loss_type = #{params.lossType}
        </if>
        <if test=" params.lossWarningType !=null  ">
            AND tr.loss_warning_type = #{params.lossWarningType}
        </if>
        <if test=" params.returnIntentionLevel !=null and 0 != params.returnIntentionLevel ">
            AND tr.return_intention_level =#{params.returnIntentionLevel} and t.invite_type IN (82381001,82381002,82381006,82381012)
        </if>
	</sql>
    <select id="exportExcelinviteVehicle" resultType="java.util.HashMap">
		<include refid="exportExcelinviteVehicleCommonSql"></include>
    </select>
    <select id="exportExcelinviteVehicleDownlod" resultType="java.util.HashMap">
		<include refid="exportExcelinviteVehicleCommonSql"></include>
		LIMIT #{offset},#{pSize};
    </select>

    <insert id="createTwiceFollow">
        insert into tt_invite_vehicle_record_twice(invite_id,month,is_ai,is_followed)
        select
          t.id,
          DATE_FORMAT(now(), '%Y-%m'),
          0,
          0
        from tt_invite_vehicle_record t
        where t.invite_type in (82381001,82381002) and t.is_main = 1 and t.order_status = 82411002 and t.is_book = 0
        and 
	(
		(
		DATE_FORMAT(t.advise_in_date, '%Y-%m') =  DATE_FORMAT(date_sub(now(),interval 1 month), '%Y-%m')
		and t.follow_status = 82401002
		)
		or 
		(
		DATE_FORMAT(t.plan_follow_date, '%Y-%m')  = DATE_FORMAT(now(), '%Y-%m')
		and DATE_FORMAT(t.advise_in_date, '%Y-%m') &lt;=  DATE_FORMAT(date_sub(now(),interval 1 month), '%Y-%m')
		and t.follow_status = 82401004
		)
		or 
		(
		DATE_FORMAT(t.plan_follow_date, '%Y-%m')  = DATE_FORMAT(date_sub(now(),interval 1 month), '%Y-%m')
		and DATE_FORMAT(t.advise_in_date, '%Y-%m') =  DATE_FORMAT(date_sub(now(),interval 1 month), '%Y-%m')
		and t.follow_status = 82401004
		)
        )
    </insert>


    <select id="getInviteVehicleRecordCPort"
            resultType="com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_vehicle_record t
        where 1=1
        AND t.follow_status in (82401001,82401004)
        <if test=" params.isMain !=null and params.isMain != '' ">
            AND t.is_main = #{params.isMain}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code =#{params.dealerCode}
        </if>
        <if test=" params.dealerCodes !=null and params.dealerCodes.size > 0">
            AND t.dealer_code in
            <foreach collection="params.dealerCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.inviteTypeParam !=null and params.inviteTypeParam.size > 0 ">
            AND
            <if test=" params.overdue !=null and params.overdue != 0 ">
                (
            </if>
            t.invite_type in
            <foreach collection="params.inviteTypeParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test=" params.overdue !=null and params.overdue != 0 ">
                or (t.invite_type=82401001 and t.plan_follow_date &lt; current_timestamp()))
            </if>
        </if>
        <if test="params.inviteTypeParam.size == 0 ">
            AND t.invite_type not in (82381005)
        </if>
        <if test=" params.planFollowDateStart !=null and params.planFollowDateStart != '' ">
            AND t.plan_follow_date &gt;= CONCAT(#{params.planFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.planFollowDateEnd !=null and params.planFollowDateEnd != '' ">
            AND t.plan_follow_date &lt;= CONCAT(#{params.planFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num like "%" #{params.licensePlateNum}"%"
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin like "%" #{params.vin}"%"
        </if>
        <if test=" params.actualFollowDateStart !=null and params.actualFollowDateStart != '' ">
            AND t.actual_follow_date &gt;= CONCAT(#{params.actualFollowDateStart},' 00:00:00')
        </if>
        <if test=" params.actualFollowDateEnd !=null and params.actualFollowDateEnd != '' ">
            AND t.actual_follow_date &lt;= CONCAT(#{params.actualFollowDateEnd},' 23:59:59')
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.isBook !=null and params.isBook != '' ">
            AND t.is_book = #{params.isBook}
        </if>
        <if test=" params.adviseInDateStart !=null and params.adviseInDateStart != '' ">
            AND t.advise_in_date &gt;= CONCAT(#{params.adviseInDateStart},' 00:00:00')
        </if>
        <if test=" params.adviseInDateEnd !=null and params.adviseInDateEnd != '' ">
            AND t.advise_in_date &lt;= CONCAT(#{params.adviseInDateEnd},' 23:59:59')
        </if>
        <if test=" params.orderStatusParam !=null and params.orderStatusParam.size > 0">
            AND t.order_status in
            <foreach collection="params.orderStatusParam" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" params.orderStatus !=null and params.orderStatus != '' ">
            AND t.order_status = #{params.orderStatus}
        </if>
        <if test=" params.saName !=null and params.saName != '' ">
            AND t.SA_NAME like "%" #{params.saName}"%"
        </if>
        <if test=" params.createdAtStart !=null and params.createdAtStart != '' ">
            AND t.created_at &gt;= CONCAT(#{params.createdAtStart},' 00:00:00')
        </if>
        <if test=" params.createdAtEnd !=null and params.createdAtEnd != '' ">
            AND t.created_at &lt;= CONCAT(#{params.createdAtEnd},' 23:59:59')
        </if>
        <if test=" params.saId !=null and params.saId != '' ">
            AND t.sa_id = #{params.saId}
        </if>
        <if test=" params.isNoDistribute !=null and params.isNoDistribute != '' ">
            AND t.sa_id is null
        </if>
        order by case when t.plan_follow_date is null then t.advise_in_date else t.plan_follow_date end asc
    </select>

    <select id="getRecordByAdviseInDate" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT  <include refid="Base_Column_List"/>
            FROM tt_invite_vehicle_record t
        WHERE
           t.`order_status` = 82411002
        and t.`invite_type`  in (82381002,82381001)
        <if test=" startDate !=null and startDate != '' ">
            AND t.advise_in_date &gt;= STR_TO_DATE(#{startDate},'%Y-%m-%d')
        </if>
        <if test=" endDate !=null and endDate != '' ">
            AND t.advise_in_date &lt; STR_TO_DATE(#{endDate},'%Y-%m-%d')
        </if>
        and t.`is_deleted`  = 0
    </select>
    <update id="updateList" parameterType="java.util.List">
        <foreach collection="updateList" item="item" index="index" open="" close="" separator=";">
            update tt_invite_vehicle_record set
            order_status = 82411003,
            updated_at = now(),
            record_version = record_version+1 where id  = #{item}
        </foreach>
    </update>

    <insert id="insertList" parameterType="com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO" useGeneratedKeys="true" keyProperty="id">


        insert into  tt_invite_vehicle_record
        (
        source_type,
        is_main,
        last_change_date,
        order_status,
        last_in_date,
        follow_status,
        vin,
        license_plate_num,
        dealer_code,
        name,
        tel,
        age,
        sex,
        model,
        invite_type,
        advise_in_date,
        record_version,
        created_by
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            1,
            1,
            #{item.lastChangeDate},
            #{item.orderStatus},
            #{item.lastInDate},
            #{item.followStatus},
            #{item.vin},
            #{item.licensePlateNum},
            #{item.dealerCode},
            #{item.name},
            #{item.tel},
            #{item.age},
            #{item.sex},
            #{item.model},
            #{item.inviteType},
            #{item.adviseInDate},
            1,
            '-9'
            )
        </foreach>

    </insert>
    <select id="selectListByVin" parameterType="java.lang.String" resultType="com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO">
         select r1.id,r1.dealer_code from tt_invite_vehicle_record r1
        left join
        tt_voc_invite_vehicle_task_record r2  on  r1.id = r2.record_id
        where r1.vin =#{vin} and r1.order_status =82411002 and  r1.is_deleted =0  and  r1.invite_type in  (82381001,82381002)
         and  r2.record_type = 0  and r2.is_deleted = 0
    </select>
    <update id="updateAlertVocByVin" parameterType="java.lang.String">
          update tt_invite_vehicle_record r1  set  r1.order_status =82411003 ,  r1.updated_at = now(),
            r1.record_version = r1.record_version+1
            where r1.id  in  (
            select r2.record_id  from tt_voc_invite_vehicle_task_record r2 where  r2.vin = #{vin} and  r2.record_type=0  and r2.invite_type =82381012
            ) and r1.vin =#{vin}

    </update>

    <select id="selectVocLossByVin" parameterType="java.lang.String" resultType="com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO">
         select r1.id,r1.advise_in_date,r1.dealer_code from tt_invite_vehicle_record r1
        left join
        tt_voc_invite_vehicle_task_record r2  on  r1.id = r2.record_id
        where r1.vin =#{vin} and r1.order_status =82411002 and  r1.is_deleted =0  and  r1.invite_type in  (82381006)    and  r2.record_type = 0  and r2.is_deleted = 0
    </select>
    <select id="selectVocByVin" parameterType="java.lang.String" resultType="com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO">
         select r1.id ,r1.advise_in_date,r1.dealer_code  from tt_invite_vehicle_record r1
        left join
        tt_voc_invite_vehicle_task_record r2  on  r1.id = r2.record_id
        where r1.vin =#{vin} and r1.order_status =82411002 and  r1.is_deleted =0  and  r1.invite_type in  (82381001,82381002)    and  r2.record_type = 0  and r2.is_deleted = 0
    </select>
    <select id="selectLossTag" resultType="java.lang.Integer">
        select COUNT(1)
        from tt_invite_vehicle_record
        where vin = #{vin}
          and order_status = 82411002
          and is_deleted = 0
          and invite_type = 82381006
          and advise_in_date &gt;= '2020-01-01 00:00:00'
          and advise_in_date &lt;= #{endTime}
    </select>

    <select id="selectDealerCodeGroupBy" resultType="java.lang.String">
        select dealer_code from tt_invite_vehicle_record group by dealer_code
    </select>

    <select id="getOpenLeads" resultMap="BaseResultMap">
        select t1.id,t1.dealer_code,t1.order_status,t1.follow_status,t1.invite_type,
               t1.advise_in_date,t1.vin,t2.verify_status,t2.icm_id,t2.bev_flag,t1.source_type
        from tt_invite_vehicle_record t1
        left join tt_voc_invite_vehicle_task_record t2 on t1.id = t2.record_id
        <where>
            t1.is_deleted = 0
            and t1.order_status = 82411002
            <if test="dealerCode != null and dealerCode !=''">
            and t1.dealer_code = #{dealerCode}
            </if>
            <if test="createDate != null and createDate !=''">
                and t1.created_at &lt; #{createDate}
            </if>
            <if test="vinList != null and vinList.size > 0">
                and t1.vin in
                <foreach collection="vinList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="inviteList != null and inviteList.size > 0">
                and t1.invite_type in
                <foreach collection="inviteList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectClueByVinAndInviteType" resultMap="BaseResultMap">
        select t1.dealer_code,t1.vin,t1.invite_type
        from tt_invite_vehicle_record t1
        left join tt_voc_invite_vehicle_task_record t2 on t1.id = t2.record_id
        where t1.is_deleted = 0
        <choose>
            <when test="flag">
                and t2.verify_status != 87911003
            </when>
            <otherwise>
                and t1.order_status = 82411002
            </otherwise>
        </choose>
          and t1.vin = #{vin}
          and t1.invite_type in (82381001,82381002,82381006,82381012)
    </select>

    <select id="searchClues" resultMap="BaseResultMap">
        select t1.id,t1.order_status,t1.order_finish_date,t1.order_finish_date > DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY) as isFlag,t2.bev_flag,t1.vin
        from tt_invite_vehicle_record t1
        left join tt_voc_invite_vehicle_task_record t2 on t1.id = t2.record_id
        where t1.dealer_code in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
          and t1.invite_type in (82381001,82381002,82381006,82381012)
          and t1.source_type in (1,2,3)
    </select>


    <update id="updateAdviseInDate" parameterType="java.util.List">
        <foreach collection="vinList" item="item" index="index" open="" close="" separator=";">
            update tt_invite_vehicle_record
            set advise_in_date=#{item.adviseInDate}
            where vin = #{item.vin}
            and order_status = 82411002
            and invite_type IN (82381001,82381002)
            and date_format(advise_in_date,'%Y-%m') >= DATE_FORMAT(date_add(now(), interval 1 month),'%Y-%m')
            and source_type = 4
            <if test=" item.whiteList !=null and item.whiteList.size() > 0 ">
                and dealer_code in
                <foreach
                        collection="item.whiteList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </foreach>
    </update>

    <update id="updateVerifyStatus" parameterType="java.lang.Integer">
        update tt_invite_vehicle_record t1
            left join tt_voc_invite_vehicle_task_record t2 on t1.id = t2.record_id
            set t2.verify_status = 87911001
        where t1.invite_type in (82381001,82381002,82381006,82381012)
          and t1.order_status = 82411002
          and t2.verify_status = 0
    </update>

    <update id="updateDealerCodeByVin">
        update tt_invite_vehicle_record
        set dealer_code = CONCAT(dealer_code, 'VBLACK'),is_deleted = 1
        where invite_type in (82381001,82381002,82381006,82381012)
          and order_status = 82411002
          and vin = #{vin}
          and is_deleted = 0
    </update>
</mapper>