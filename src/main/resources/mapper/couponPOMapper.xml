<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.CouponMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.CouponPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <result column="app_id" property="appId"/>
                                                        <result column="owner_code" property="ownerCode"/>
                                                        <result column="owner_par_code" property="ownerParCode"/>
                                                        <result column="org_id" property="orgId"/>
                                                                                    <result column="coupon_code" property="couponCode"/>
                                                        <result column="coupon_name" property="couponName"/>
                                                        <result column="coupon_type" property="couponType"/>
                                                        <result column="coupon_status" property="couponStatus"/>
                                                        <result column="validity_period_type" property="validityPeriodType"/>
                                                        <result column="validity_period_days" property="validityPeriodDays"/>
                                                        <result column="coupon_begin_date" property="couponBeginDate"/>
                                                        <result column="coupon_end_date" property="couponEndDate"/>
                                                        <result column="shelves_status" property="shelvesStatus"/>
                                                        <result column="coupon_purpose" property="couponPurpose"/>
                                                        <result column="usage_scenarios" property="usageScenarios"/>
                                                        <result column="is_available_unlimited" property="isAvailableUnlimited"/>
                                                        <result column="available_total_count" property="availableTotalCount"/>
                                                        <result column="got_total_count" property="gotTotalCount"/>
                                                        <result column="coupon_denomination" property="couponDenomination"/>
                                                        <result column="each_available_most_count" property="eachAvailableMostCount"/>
                                                        <result column="is_superposition" property="isSuperposition"/>
                                                        <result column="single_superposition_count" property="singleSuperpositionCount"/>
                                                        <result column="recipients_range" property="recipientsRange"/>
                                                        <result column="recipients_range_partion" property="recipientsRangePartion"/>
                                                        <result column="volvo_age_lower_limit" property="volvoAgeLowerLimit"/>
                                                        <result column="volvo_age_upper_limit" property="volvoAgeUpperLimit"/>
                                                        <result column="user_label" property="userLabel"/>
                                                        <result column="recipients_range_group_count" property="recipientsRangeGroupCount"/>
                                                        <result column="use_rules" property="useRules"/>
                                                        <result column="standard_limit_dealer_required" property="standardLimitDealerRequired"/>
                                                        <result column="standard_limit_repair_type" property="standardLimitRepairType"/>
                                                        <result column="standard_limit_repair_type_code" property="standardLimitRepairTypeCode"/>
                                                        <result column="standard_use_threshold" property="standardUseThreshold"/>
                                                        <result column="standard_use_threshold_available_amount" property="standardUseThresholdAvailableAmount"/>
                                                        <result column="standard_has_superposition" property="standardHasSuperposition"/>
                                                        <result column="standard_activation_required" property="standardActivationRequired"/>
                                                        <result column="standard_model" property="standardModel"/>
                                                        <result column="standard_vehicle_age" property="standardVehicleAge"/>
                                                        <result column="standard_vehicle_age_lower" property="standardVehicleAgeLower"/>
                                                        <result column="standard_vehicle_age_upper" property="standardVehicleAgeUpper"/>
                                                        <result column="advanced_limit_dealer_required" property="advancedLimitDealerRequired"/>
                                                        <result column="advanced_limit_repair_type" property="advancedLimitRepairType"/>
                                                        <result column="advanced_limit_repair_type_code" property="advancedLimitRepairTypeCode"/>
                                                        <result column="advanced_use_threshold" property="advancedUseThreshold"/>
                                                        <result column="advanced_use_threshold_available_amount" property="advancedUseThresholdAvailableAmount"/>
                                                        <result column="advanced_has_superposition" property="advancedHasSuperposition"/>
                                                        <result column="use_threshold_group_count" property="useThresholdGroupCount"/>
                                                        <result column="apply_project" property="applyProject"/>
                                                        <result column="assign_apply_project" property="assignApplyProject"/>
                                                        <result column="purponse_desc" property="purponseDesc"/>
                                                        <result column="coupon_instructions" property="couponInstructions"/>
                                                        <result column="data_sources" property="dataSources"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                        <result column="advanced_model" property="advancedModel"/>
                                                        <result column="created_by_name" property="createdByName"/>
                                                        <result column="advanced_vehicle_age_lower" property="advancedVehicleAgeLower"/>
                                                        <result column="advanced_vehicle_age_upper" property="advancedVehicleAgeUpper"/>
                                                        <result column="advanced_vehicle_age" property="advancedVehicleAge"/>
                                                        <result column="mid_coupon_id" property="midCouponId"/>
                                                        <result column="item_category" property="itemCategory"/>
                                                        <result column="is_use_multiple" property="isUseMultiple"/>
                                                        <result column="total_amount" property="totalAmount"/>
                                                        <result column="coupon_discount" property="couponDiscount"/>
                                                        <result column="yh_type" property="yhType"/>
                                                        <result column="is_goodwill" property="isGoodwill"/>
                                                        <result column="collect_range" property="collectRange"/>
                                                        <result column="is_superposition_similar" property="isSuperpositionSimilar"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, coupon_code, coupon_name, coupon_type, coupon_status, validity_period_type, validity_period_days, coupon_begin_date, coupon_end_date, shelves_status, coupon_purpose, usage_scenarios, is_available_unlimited, available_total_count, got_total_count, coupon_denomination, each_available_most_count, is_superposition, single_superposition_count, recipients_range, recipients_range_partion, volvo_age_lower_limit, volvo_age_upper_limit, user_label, recipients_range_group_count, use_rules, standard_limit_dealer_required, standard_limit_repair_type, standard_limit_repair_type_code, standard_use_threshold, standard_use_threshold_available_amount, standard_has_superposition, standard_activation_required, standard_model, standard_vehicle_age, standard_vehicle_age_lower, standard_vehicle_age_upper, advanced_limit_dealer_required, advanced_limit_repair_type, advanced_limit_repair_type_code, advanced_use_threshold, advanced_use_threshold_available_amount, advanced_has_superposition, use_threshold_group_count, apply_project, assign_apply_project, purponse_desc, coupon_instructions, data_sources, is_deleted, created_at, updated_at, advanced_model, created_by_name, advanced_vehicle_age_lower, advanced_vehicle_age_upper, advanced_vehicle_age, mid_coupon_id, item_category, is_use_multiple, total_amount, coupon_discount, yh_type, is_goodwill, collect_range, is_superposition_similar
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.CouponPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_coupon t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.couponCode !=null and params.couponCode != '' ">
                AND t.coupon_code = #{params.couponCode}
            </if>
                    <if test=" params.couponName !=null and params.couponName != '' ">
                AND t.coupon_name = #{params.couponName}
            </if>
                    <if test=" params.couponType !=null and params.couponType != '' ">
                AND t.coupon_type = #{params.couponType}
            </if>
                    <if test=" params.couponStatus !=null and params.couponStatus != '' ">
                AND t.coupon_status = #{params.couponStatus}
            </if>
                    <if test=" params.validityPeriodType !=null and params.validityPeriodType != '' ">
                AND t.validity_period_type = #{params.validityPeriodType}
            </if>
                    <if test=" params.validityPeriodDays !=null and params.validityPeriodDays != '' ">
                AND t.validity_period_days = #{params.validityPeriodDays}
            </if>
                    <if test=" params.couponBeginDate !=null and params.couponBeginDate != '' ">
                AND t.coupon_begin_date = #{params.couponBeginDate}
            </if>
                    <if test=" params.couponEndDate !=null and params.couponEndDate != '' ">
                AND t.coupon_end_date = #{params.couponEndDate}
            </if>
                    <if test=" params.shelvesStatus !=null and params.shelvesStatus != '' ">
                AND t.shelves_status = #{params.shelvesStatus}
            </if>
                    <if test=" params.couponPurpose !=null and params.couponPurpose != '' ">
                AND t.coupon_purpose = #{params.couponPurpose}
            </if>
                    <if test=" params.usageScenarios !=null and params.usageScenarios != '' ">
                AND t.usage_scenarios = #{params.usageScenarios}
            </if>
                    <if test=" params.isAvailableUnlimited !=null and params.isAvailableUnlimited != '' ">
                AND t.is_available_unlimited = #{params.isAvailableUnlimited}
            </if>
                    <if test=" params.availableTotalCount !=null and params.availableTotalCount != '' ">
                AND t.available_total_count = #{params.availableTotalCount}
            </if>
                    <if test=" params.gotTotalCount !=null and params.gotTotalCount != '' ">
                AND t.got_total_count = #{params.gotTotalCount}
            </if>
                    <if test=" params.couponDenomination !=null and params.couponDenomination != '' ">
                AND t.coupon_denomination = #{params.couponDenomination}
            </if>
                    <if test=" params.eachAvailableMostCount !=null and params.eachAvailableMostCount != '' ">
                AND t.each_available_most_count = #{params.eachAvailableMostCount}
            </if>
                    <if test=" params.isSuperposition !=null and params.isSuperposition != '' ">
                AND t.is_superposition = #{params.isSuperposition}
            </if>
                    <if test=" params.singleSuperpositionCount !=null and params.singleSuperpositionCount != '' ">
                AND t.single_superposition_count = #{params.singleSuperpositionCount}
            </if>
                    <if test=" params.recipientsRange !=null and params.recipientsRange != '' ">
                AND t.recipients_range = #{params.recipientsRange}
            </if>
                    <if test=" params.recipientsRangePartion !=null and params.recipientsRangePartion != '' ">
                AND t.recipients_range_partion = #{params.recipientsRangePartion}
            </if>
                    <if test=" params.volvoAgeLowerLimit !=null and params.volvoAgeLowerLimit != '' ">
                AND t.volvo_age_lower_limit = #{params.volvoAgeLowerLimit}
            </if>
                    <if test=" params.volvoAgeUpperLimit !=null and params.volvoAgeUpperLimit != '' ">
                AND t.volvo_age_upper_limit = #{params.volvoAgeUpperLimit}
            </if>
                    <if test=" params.userLabel !=null and params.userLabel != '' ">
                AND t.user_label = #{params.userLabel}
            </if>
                    <if test=" params.recipientsRangeGroupCount !=null and params.recipientsRangeGroupCount != '' ">
                AND t.recipients_range_group_count = #{params.recipientsRangeGroupCount}
            </if>
                    <if test=" params.useRules !=null and params.useRules != '' ">
                AND t.use_rules = #{params.useRules}
            </if>
                    <if test=" params.standardLimitDealerRequired !=null and params.standardLimitDealerRequired != '' ">
                AND t.standard_limit_dealer_required = #{params.standardLimitDealerRequired}
            </if>
                    <if test=" params.standardLimitRepairType !=null and params.standardLimitRepairType != '' ">
                AND t.standard_limit_repair_type = #{params.standardLimitRepairType}
            </if>
                    <if test=" params.standardLimitRepairTypeCode !=null and params.standardLimitRepairTypeCode != '' ">
                AND t.standard_limit_repair_type_code = #{params.standardLimitRepairTypeCode}
            </if>
                    <if test=" params.standardUseThreshold !=null and params.standardUseThreshold != '' ">
                AND t.standard_use_threshold = #{params.standardUseThreshold}
            </if>
                    <if test=" params.standardUseThresholdAvailableAmount !=null and params.standardUseThresholdAvailableAmount != '' ">
                AND t.standard_use_threshold_available_amount = #{params.standardUseThresholdAvailableAmount}
            </if>
                    <if test=" params.standardHasSuperposition !=null and params.standardHasSuperposition != '' ">
                AND t.standard_has_superposition = #{params.standardHasSuperposition}
            </if>
                    <if test=" params.standardActivationRequired !=null and params.standardActivationRequired != '' ">
                AND t.standard_activation_required = #{params.standardActivationRequired}
            </if>
                    <if test=" params.standardModel !=null and params.standardModel != '' ">
                AND t.standard_model = #{params.standardModel}
            </if>
                    <if test=" params.standardVehicleAge !=null and params.standardVehicleAge != '' ">
                AND t.standard_vehicle_age = #{params.standardVehicleAge}
            </if>
                    <if test=" params.standardVehicleAgeLower !=null and params.standardVehicleAgeLower != '' ">
                AND t.standard_vehicle_age_lower = #{params.standardVehicleAgeLower}
            </if>
                    <if test=" params.standardVehicleAgeUpper !=null and params.standardVehicleAgeUpper != '' ">
                AND t.standard_vehicle_age_upper = #{params.standardVehicleAgeUpper}
            </if>
                    <if test=" params.advancedLimitDealerRequired !=null and params.advancedLimitDealerRequired != '' ">
                AND t.advanced_limit_dealer_required = #{params.advancedLimitDealerRequired}
            </if>
                    <if test=" params.advancedLimitRepairType !=null and params.advancedLimitRepairType != '' ">
                AND t.advanced_limit_repair_type = #{params.advancedLimitRepairType}
            </if>
                    <if test=" params.advancedLimitRepairTypeCode !=null and params.advancedLimitRepairTypeCode != '' ">
                AND t.advanced_limit_repair_type_code = #{params.advancedLimitRepairTypeCode}
            </if>
                    <if test=" params.advancedUseThreshold !=null and params.advancedUseThreshold != '' ">
                AND t.advanced_use_threshold = #{params.advancedUseThreshold}
            </if>
                    <if test=" params.advancedUseThresholdAvailableAmount !=null and params.advancedUseThresholdAvailableAmount != '' ">
                AND t.advanced_use_threshold_available_amount = #{params.advancedUseThresholdAvailableAmount}
            </if>
                    <if test=" params.advancedHasSuperposition !=null and params.advancedHasSuperposition != '' ">
                AND t.advanced_has_superposition = #{params.advancedHasSuperposition}
            </if>
                    <if test=" params.useThresholdGroupCount !=null and params.useThresholdGroupCount != '' ">
                AND t.use_threshold_group_count = #{params.useThresholdGroupCount}
            </if>
                    <if test=" params.applyProject !=null and params.applyProject != '' ">
                AND t.apply_project = #{params.applyProject}
            </if>
                    <if test=" params.assignApplyProject !=null and params.assignApplyProject != '' ">
                AND t.assign_apply_project = #{params.assignApplyProject}
            </if>
                    <if test=" params.purponseDesc !=null and params.purponseDesc != '' ">
                AND t.purponse_desc = #{params.purponseDesc}
            </if>
                    <if test=" params.couponInstructions !=null and params.couponInstructions != '' ">
                AND t.coupon_instructions = #{params.couponInstructions}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
                    <if test=" params.advancedModel !=null and params.advancedModel != '' ">
                AND t.advanced_model = #{params.advancedModel}
            </if>
                    <if test=" params.createdByName !=null and params.createdByName != '' ">
                AND t.created_by_name = #{params.createdByName}
            </if>
                    <if test=" params.advancedVehicleAgeLower !=null and params.advancedVehicleAgeLower != '' ">
                AND t.advanced_vehicle_age_lower = #{params.advancedVehicleAgeLower}
            </if>
                    <if test=" params.advancedVehicleAgeUpper !=null and params.advancedVehicleAgeUpper != '' ">
                AND t.advanced_vehicle_age_upper = #{params.advancedVehicleAgeUpper}
            </if>
                    <if test=" params.advancedVehicleAge !=null and params.advancedVehicleAge != '' ">
                AND t.advanced_vehicle_age = #{params.advancedVehicleAge}
            </if>
                    <if test=" params.midCouponId !=null and params.midCouponId != '' ">
                AND t.mid_coupon_id = #{params.midCouponId}
            </if>
                    <if test=" params.itemCategory !=null and params.itemCategory != '' ">
                AND t.item_category = #{params.itemCategory}
            </if>
                    <if test=" params.isUseMultiple !=null and params.isUseMultiple != '' ">
                AND t.is_use_multiple = #{params.isUseMultiple}
            </if>
                    <if test=" params.totalAmount !=null and params.totalAmount != '' ">
                AND t.total_amount = #{params.totalAmount}
            </if>
                    <if test=" params.couponDiscount !=null and params.couponDiscount != '' ">
                AND t.coupon_discount = #{params.couponDiscount}
            </if>
                    <if test=" params.yhType !=null and params.yhType != '' ">
                AND t.yh_type = #{params.yhType}
            </if>
                    <if test=" params.isGoodwill !=null and params.isGoodwill != '' ">
                AND t.is_goodwill = #{params.isGoodwill}
            </if>
                    <if test=" params.collectRange !=null and params.collectRange != '' ">
                AND t.collect_range = #{params.collectRange}
            </if>
                    <if test=" params.isSuperpositionSimilar !=null and params.isSuperpositionSimilar != '' ">
                AND t.is_superposition_similar = #{params.isSuperpositionSimilar}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.CouponPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_coupon t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.couponCode !=null and params.couponCode != '' ">
                AND t.coupon_code = #{params.couponCode}
            </if>
                    <if test=" params.couponName !=null and params.couponName != '' ">
                AND t.coupon_name = #{params.couponName}
            </if>
                    <if test=" params.couponType !=null and params.couponType != '' ">
                AND t.coupon_type = #{params.couponType}
            </if>
                    <if test=" params.couponStatus !=null and params.couponStatus != '' ">
                AND t.coupon_status = #{params.couponStatus}
            </if>
                    <if test=" params.validityPeriodType !=null and params.validityPeriodType != '' ">
                AND t.validity_period_type = #{params.validityPeriodType}
            </if>
                    <if test=" params.validityPeriodDays !=null and params.validityPeriodDays != '' ">
                AND t.validity_period_days = #{params.validityPeriodDays}
            </if>
                    <if test=" params.couponBeginDate !=null and params.couponBeginDate != '' ">
                AND t.coupon_begin_date = #{params.couponBeginDate}
            </if>
                    <if test=" params.couponEndDate !=null and params.couponEndDate != '' ">
                AND t.coupon_end_date = #{params.couponEndDate}
            </if>
                    <if test=" params.shelvesStatus !=null and params.shelvesStatus != '' ">
                AND t.shelves_status = #{params.shelvesStatus}
            </if>
                    <if test=" params.couponPurpose !=null and params.couponPurpose != '' ">
                AND t.coupon_purpose = #{params.couponPurpose}
            </if>
                    <if test=" params.usageScenarios !=null and params.usageScenarios != '' ">
                AND t.usage_scenarios = #{params.usageScenarios}
            </if>
                    <if test=" params.isAvailableUnlimited !=null and params.isAvailableUnlimited != '' ">
                AND t.is_available_unlimited = #{params.isAvailableUnlimited}
            </if>
                    <if test=" params.availableTotalCount !=null and params.availableTotalCount != '' ">
                AND t.available_total_count = #{params.availableTotalCount}
            </if>
                    <if test=" params.gotTotalCount !=null and params.gotTotalCount != '' ">
                AND t.got_total_count = #{params.gotTotalCount}
            </if>
                    <if test=" params.couponDenomination !=null and params.couponDenomination != '' ">
                AND t.coupon_denomination = #{params.couponDenomination}
            </if>
                    <if test=" params.eachAvailableMostCount !=null and params.eachAvailableMostCount != '' ">
                AND t.each_available_most_count = #{params.eachAvailableMostCount}
            </if>
                    <if test=" params.isSuperposition !=null and params.isSuperposition != '' ">
                AND t.is_superposition = #{params.isSuperposition}
            </if>
                    <if test=" params.singleSuperpositionCount !=null and params.singleSuperpositionCount != '' ">
                AND t.single_superposition_count = #{params.singleSuperpositionCount}
            </if>
                    <if test=" params.recipientsRange !=null and params.recipientsRange != '' ">
                AND t.recipients_range = #{params.recipientsRange}
            </if>
                    <if test=" params.recipientsRangePartion !=null and params.recipientsRangePartion != '' ">
                AND t.recipients_range_partion = #{params.recipientsRangePartion}
            </if>
                    <if test=" params.volvoAgeLowerLimit !=null and params.volvoAgeLowerLimit != '' ">
                AND t.volvo_age_lower_limit = #{params.volvoAgeLowerLimit}
            </if>
                    <if test=" params.volvoAgeUpperLimit !=null and params.volvoAgeUpperLimit != '' ">
                AND t.volvo_age_upper_limit = #{params.volvoAgeUpperLimit}
            </if>
                    <if test=" params.userLabel !=null and params.userLabel != '' ">
                AND t.user_label = #{params.userLabel}
            </if>
                    <if test=" params.recipientsRangeGroupCount !=null and params.recipientsRangeGroupCount != '' ">
                AND t.recipients_range_group_count = #{params.recipientsRangeGroupCount}
            </if>
                    <if test=" params.useRules !=null and params.useRules != '' ">
                AND t.use_rules = #{params.useRules}
            </if>
                    <if test=" params.standardLimitDealerRequired !=null and params.standardLimitDealerRequired != '' ">
                AND t.standard_limit_dealer_required = #{params.standardLimitDealerRequired}
            </if>
                    <if test=" params.standardLimitRepairType !=null and params.standardLimitRepairType != '' ">
                AND t.standard_limit_repair_type = #{params.standardLimitRepairType}
            </if>
                    <if test=" params.standardLimitRepairTypeCode !=null and params.standardLimitRepairTypeCode != '' ">
                AND t.standard_limit_repair_type_code = #{params.standardLimitRepairTypeCode}
            </if>
                    <if test=" params.standardUseThreshold !=null and params.standardUseThreshold != '' ">
                AND t.standard_use_threshold = #{params.standardUseThreshold}
            </if>
                    <if test=" params.standardUseThresholdAvailableAmount !=null and params.standardUseThresholdAvailableAmount != '' ">
                AND t.standard_use_threshold_available_amount = #{params.standardUseThresholdAvailableAmount}
            </if>
                    <if test=" params.standardHasSuperposition !=null and params.standardHasSuperposition != '' ">
                AND t.standard_has_superposition = #{params.standardHasSuperposition}
            </if>
                    <if test=" params.standardActivationRequired !=null and params.standardActivationRequired != '' ">
                AND t.standard_activation_required = #{params.standardActivationRequired}
            </if>
                    <if test=" params.standardModel !=null and params.standardModel != '' ">
                AND t.standard_model = #{params.standardModel}
            </if>
                    <if test=" params.standardVehicleAge !=null and params.standardVehicleAge != '' ">
                AND t.standard_vehicle_age = #{params.standardVehicleAge}
            </if>
                    <if test=" params.standardVehicleAgeLower !=null and params.standardVehicleAgeLower != '' ">
                AND t.standard_vehicle_age_lower = #{params.standardVehicleAgeLower}
            </if>
                    <if test=" params.standardVehicleAgeUpper !=null and params.standardVehicleAgeUpper != '' ">
                AND t.standard_vehicle_age_upper = #{params.standardVehicleAgeUpper}
            </if>
                    <if test=" params.advancedLimitDealerRequired !=null and params.advancedLimitDealerRequired != '' ">
                AND t.advanced_limit_dealer_required = #{params.advancedLimitDealerRequired}
            </if>
                    <if test=" params.advancedLimitRepairType !=null and params.advancedLimitRepairType != '' ">
                AND t.advanced_limit_repair_type = #{params.advancedLimitRepairType}
            </if>
                    <if test=" params.advancedLimitRepairTypeCode !=null and params.advancedLimitRepairTypeCode != '' ">
                AND t.advanced_limit_repair_type_code = #{params.advancedLimitRepairTypeCode}
            </if>
                    <if test=" params.advancedUseThreshold !=null and params.advancedUseThreshold != '' ">
                AND t.advanced_use_threshold = #{params.advancedUseThreshold}
            </if>
                    <if test=" params.advancedUseThresholdAvailableAmount !=null and params.advancedUseThresholdAvailableAmount != '' ">
                AND t.advanced_use_threshold_available_amount = #{params.advancedUseThresholdAvailableAmount}
            </if>
                    <if test=" params.advancedHasSuperposition !=null and params.advancedHasSuperposition != '' ">
                AND t.advanced_has_superposition = #{params.advancedHasSuperposition}
            </if>
                    <if test=" params.useThresholdGroupCount !=null and params.useThresholdGroupCount != '' ">
                AND t.use_threshold_group_count = #{params.useThresholdGroupCount}
            </if>
                    <if test=" params.applyProject !=null and params.applyProject != '' ">
                AND t.apply_project = #{params.applyProject}
            </if>
                    <if test=" params.assignApplyProject !=null and params.assignApplyProject != '' ">
                AND t.assign_apply_project = #{params.assignApplyProject}
            </if>
                    <if test=" params.purponseDesc !=null and params.purponseDesc != '' ">
                AND t.purponse_desc = #{params.purponseDesc}
            </if>
                    <if test=" params.couponInstructions !=null and params.couponInstructions != '' ">
                AND t.coupon_instructions = #{params.couponInstructions}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
                    <if test=" params.advancedModel !=null and params.advancedModel != '' ">
                AND t.advanced_model = #{params.advancedModel}
            </if>
                    <if test=" params.createdByName !=null and params.createdByName != '' ">
                AND t.created_by_name = #{params.createdByName}
            </if>
                    <if test=" params.advancedVehicleAgeLower !=null and params.advancedVehicleAgeLower != '' ">
                AND t.advanced_vehicle_age_lower = #{params.advancedVehicleAgeLower}
            </if>
                    <if test=" params.advancedVehicleAgeUpper !=null and params.advancedVehicleAgeUpper != '' ">
                AND t.advanced_vehicle_age_upper = #{params.advancedVehicleAgeUpper}
            </if>
                    <if test=" params.advancedVehicleAge !=null and params.advancedVehicleAge != '' ">
                AND t.advanced_vehicle_age = #{params.advancedVehicleAge}
            </if>
                    <if test=" params.midCouponId !=null and params.midCouponId != '' ">
                AND t.mid_coupon_id = #{params.midCouponId}
            </if>
                    <if test=" params.itemCategory !=null and params.itemCategory != '' ">
                AND t.item_category = #{params.itemCategory}
            </if>
                    <if test=" params.isUseMultiple !=null and params.isUseMultiple != '' ">
                AND t.is_use_multiple = #{params.isUseMultiple}
            </if>
                    <if test=" params.totalAmount !=null and params.totalAmount != '' ">
                AND t.total_amount = #{params.totalAmount}
            </if>
                    <if test=" params.couponDiscount !=null and params.couponDiscount != '' ">
                AND t.coupon_discount = #{params.couponDiscount}
            </if>
                    <if test=" params.yhType !=null and params.yhType != '' ">
                AND t.yh_type = #{params.yhType}
            </if>
                    <if test=" params.isGoodwill !=null and params.isGoodwill != '' ">
                AND t.is_goodwill = #{params.isGoodwill}
            </if>
                    <if test=" params.collectRange !=null and params.collectRange != '' ">
                AND t.collect_range = #{params.collectRange}
            </if>
                    <if test=" params.isSuperpositionSimilar !=null and params.isSuperpositionSimilar != '' ">
                AND t.is_superposition_similar = #{params.isSuperpositionSimilar}
            </if>
            </select>

</mapper>
