<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.faultLight.CluesDiagnosticInfoRelationMapper">
    <update id="batchUpdateProcessingStatusByIds">
        <foreach collection="params" item="param" index="index" open="" close="" separator=";">
            UPDATE tt_clues_diagnostic_info_relation set processing_status = #{param.processingStatus} WHERE leads_preprocessing_id = #{param.leadsPreprocessingId}
        </foreach>
    </update>

    <update id="batchUpdateNumberOfExecutionsByIds">
        <foreach collection="params" item="param" index="index" open="" close="" separator=";">
            UPDATE tt_clues_diagnostic_info_relation set number_of_executions = #{param.numberOfExecutions} WHERE id = #{param.id}
        </foreach>
    </update>

    <select id="queryDiagnosticInfoRelationList" resultType="com.yonyou.dmscus.customer.entity.po.faultLight.CluesDiagnosticInfoRelationPO">
        select id, leads_preprocessing_id leadsPreprocessingId, number_of_executions numberOfExecutions
        from tt_clues_diagnostic_info_relation
        where created_at > #{createdAt} and processing_status = #{processingStatus} and is_deleted = 0
        order by id
        LIMIT #{offset}, #{limit}
    </select>

    <select id="queryDiagnosticInfoRelationCount" resultType="java.lang.Integer">
        select count(*)
        from tt_clues_diagnostic_info_relation
        where created_at > #{createdAt} and processing_status = #{processingStatus} and is_deleted = 0
    </select>
</mapper>