<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.faultLight.TtFaultLightInvitationMapper">

    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightInvitationPO" >
        <result column="id" property="id" />
        <result column="contact_time" property="contactTime" />
        <result column="contacts_name" property="contactsName" />
        <result column="contact_res_time" property="contactResTime" />
        <result column="contact_overtime" property="contactOvertime" />
        <result column="contact_result" property="contactResult" />
        <result column="dlr" property="dlr" />
        <result column="is_dlr" property="isDlr" />
        <result column="clue_dis_time" property="clueDisTime" />
        <result column="cus_name" property="cusName" />
        <result column="cus_phone" property="cusPhone" />
        <result column="invite_time" property="inviteTime" />
        <result column="invite_name" property="inviteName" />
        <result column="invite_res_time" property="inviteResTime" />
        <result column="invite_overtime" property="inviteOvertime" />
        <result column="invite_result" property="inviteResult" />
        <result column="forecast_time" property="forecastTime" />
        <result column="into_time" property="intoTime" />
        <result column="into_on_time" property="intoOnTime" />
        <result column="no_into" property="noInto" />
        <result column="self_into" property="selfInto" />
        <result column="is_deleted" property="isDeleted" />
        <result column="created_at" property="createdAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="create_sqlby" property="createSqlby" />
        <result column="update_sqlby" property="updateSqlby" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
                contact_time,
                contacts_name,
                contact_res_time,
                contact_overtime,
                contact_result,
                dlr,
                is_dlr,
                clue_dis_time,
                cus_name,
                cus_phone,
                invite_time,
                invite_name,
                invite_res_time,
                invite_overtime,
                invite_result,
                forecast_time,
                into_time,
                into_on_time,
                no_into,
                self_into,
                is_deleted,
                created_at,
                created_by,
                updated_at,
                updated_by,
                create_sqlby,
                update_sqlby
    </sql>

    <update id="updateByClueId"  parameterType="com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightInvitationPO">
        update tt_fault_light_invitation
        <set>
            <if test="contactTime!=null">
                contact_time = #{contactTime},
            </if>
            <if test="contactsName!=null">
                contacts_name = #{contactsName},
            </if>
            <if test="contactResTime!=null">
                contact_res_time = #{contactResTime},
            </if>
            <if test="contactOvertime!=null">
                contact_overtime = #{contactOvertime},
            </if>
            <if test="contactResult!=null">
                contact_result = #{contactResult},
            </if>
            <if test="comments!=null">
                comments = #{comments},
            </if>
            <if test="dlr!=null">
                dlr = #{dlr},
            </if>
            <if test="isDlr!=null">
                is_dlr = #{isDlr},
            </if>
            <if test="clueDisTime!=null">
                clue_dis_time = #{clueDisTime},
            </if>
            <if test="cusName!=null">
                cus_name = #{cusName},
            </if>
            <if test="cusPhone!=null">
                cus_phone = #{cusPhone},
            </if>
            <if test="gender!=null">
                gender = #{gender},
            </if>
            <if test="age!=null">
                age = #{age},
            </if>
            <if test="inviteTime!=null">
                invite_time = #{inviteTime},
            </if>
            <if test="inviteName!=null">
                invite_name = #{inviteName},
            </if>
            <if test="saId !=null">
                sa_id = #{saId},
            </if>
            <if test="inviteResTime!=null">
                invite_res_time=#{inviteResTime},
            </if>
            <if test="inviteOvertime!=null">
                invite_overtime=#{inviteOvertime},
            </if>
            <if test="inviteResult!=null">
                invite_result=#{inviteResult},
            </if>
            <if test="forecastTime!=null">
                forecast_time=#{forecastTime},
            </if>
            <if test="intoTime!=null">
                into_time=#{intoTime},
            </if>
            <if test="intoOnTime!=null">
                into_on_time=#{intoOnTime},
            </if>
            <if test="noInto!=null">
                no_into=#{noInto},
            </if>
            <if test="selfInto!=null">
                self_into=#{selfInto},
            </if>
            <if test="failureReason!=null">
                failure_reason=#{failureReason},
            </if>
            <if test="reInviteTime!=null">
                re_invite_time=#{reInviteTime},
            </if>
        </set>
        where clue_id = #{clueId}
    </update>

    <update id="updateNoIntoById" parameterType="java.util.List">
        <foreach collection="params" item="params" index="index" open="" close="" separator=";">
        UPDATE
        tt_fault_light_invitation
        <set>
            <if test="params.intoTime!=null">
                into_time=#{params.intoTime},
            </if>
            <if test="params.intoOnTime!=null">
                into_on_time=#{params.intoOnTime},
            </if>
            <if test="params.noInto!=null">
                no_into=#{params.noInto},
            </if>
            <if test="params.selfInto!=null">
                self_into=#{params.selfInto},
            </if>
        </set>
        where
        clue_id = #{params.clueId}
        </foreach>
    </update>

    <select id="selectIdByClueId" resultType="java.lang.Long">
        select count(1) from tt_fault_light_invitation where clue_id = #{clueId}
    </select>
    <select id="queryFaultLightInvitationByClurId"
            resultType="com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightInvitationPO">
        select id,clue_id,re_invite_time,cus_phone,cus_name,contacts_name from tt_fault_light_invitation where clue_id = #{clueId}
    </select>

</mapper>
