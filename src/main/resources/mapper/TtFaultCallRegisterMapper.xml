<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.faultLight.TtFaultCallRegisterMapper">

    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultCallRegisterPO" >
        <result column="app_id" property="appId" />
        <result column="owner_code" property="ownerCode" />
        <result column="owner_par_code" property="ownerParCode" />
        <result column="org_id" property="orgId" />
        <result column="id" property="id" />
        <result column="invite_id" property="inviteId" />
        <result column="call_id" property="callId" />
        <result column="sa_id" property="saId" />
        <result column="cus_name" property="cusName" />
        <result column="cus_number" property="cusNumber" />
        <result column="data_sources" property="dataSources" />
        <result column="is_deleted" property="isDeleted" />
        <result column="is_valid" property="isValid" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
        <result column="record_version" property="recordVersion" />
        <result column="dealer_code" property="dealerCode" />
        <result column="sa_name" property="saName" />
        <result column="sa_number" property="saNumber" />
        <result column="work_number" property="workNumber" />
        <result column="mark" property="mark" />
    </resultMap>

    <sql id="Base_Column_List">
        app_id,
                owner_code,
                owner_par_code,
                org_id,
                id,
                invite_id,
                call_id,
                sa_id,
                cus_name,
                cus_number,
                data_sources,
                is_deleted,
                is_valid,
                created_by,
                created_at,
                updated_by,
                updated_at,
                record_version,
                dealer_code,
                sa_name,
                sa_number,
                work_number,
                mark
    </sql>

</mapper>