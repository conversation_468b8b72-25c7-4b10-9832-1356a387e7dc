<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyFileInfoMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyFileInfoPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                                                    <result column="APP_ID" property="appId"/>
                                                        <result column="OWNER_CODE" property="ownerCode"/>
                                                        <result column="OWNER_PAR_CODE" property="ownerParCode"/>
                                                        <result column="ORG_ID" property="orgId"/>
                                                                                    <result column="goodwill_apply_id" property="goodwillApplyId"/>
                                                        <result column="file_type" property="fileType"/>
                                                        <result column="upload_person" property="uploadPerson"/>
                                                        <result column="upload_time" property="uploadTime"/>
                                                        <result column="file_name" property="fileName"/>
                                                        <result column="url" property="url"/>
                                                        <result column="file_size" property="fileSize"/>
                                                        <result column="is_valid" property="isValid"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                        <result column="apply_no" property="applyNo"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, goodwill_apply_id, file_type, upload_person, upload_time, file_name, url, file_size, is_valid, is_deleted, created_at, updated_at, apply_no
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyFileInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_file_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.fileType !=null and params.fileType != '' ">
                AND t.file_type = #{params.fileType}
            </if>
                    <if test=" params.uploadPerson !=null and params.uploadPerson != '' ">
                AND t.upload_person = #{params.uploadPerson}
            </if>
                    <if test=" params.uploadTime !=null and params.uploadTime != '' ">
                AND t.upload_time = #{params.uploadTime}
            </if>
                    <if test=" params.fileName !=null and params.fileName != '' ">
                AND t.file_name = #{params.fileName}
            </if>
                    <if test=" params.url !=null and params.url != '' ">
                AND t.url = #{params.url}
            </if>
                    <if test=" params.fileSize !=null and params.fileSize != '' ">
                AND t.file_size = #{params.fileSize}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
                    <if test=" params.applyNo !=null and params.applyNo != '' ">
                AND t.apply_no = #{params.applyNo}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyFileInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_file_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.fileType !=null and params.fileType != '' ">
                AND t.file_type = #{params.fileType}
            </if>
                    <if test=" params.uploadPerson !=null and params.uploadPerson != '' ">
                AND t.upload_person = #{params.uploadPerson}
            </if>
                    <if test=" params.uploadTime !=null and params.uploadTime != '' ">
                AND t.upload_time = #{params.uploadTime}
            </if>
                    <if test=" params.fileName !=null and params.fileName != '' ">
                AND t.file_name = #{params.fileName}
            </if>
                    <if test=" params.url !=null and params.url != '' ">
                AND t.url = #{params.url}
            </if>
                    <if test=" params.fileSize !=null and params.fileSize != '' ">
                AND t.file_size = #{params.fileSize}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
                    <if test=" params.applyNo !=null and params.applyNo != '' ">
                AND t.apply_no = #{params.applyNo}
            </if>
            </select>
	<!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql1" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyFileInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_file_info t
        WHERE 1=1
        <if test=" params.applyNo !=null and params.applyNo != '' ">
            AND t.apply_no = #{params.applyNo}
        </if>
         <if test=" params.fileType !=null and params.fileType != '' ">
                AND t.file_type = #{params.fileType}
            </if>
        and t.is_deleted=0
    </select>
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectMaterialUploadList" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyFileInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_file_info t
        WHERE 1=1
        <if test=" params.applyNo !=null and params.applyNo != '' ">
            AND t.apply_no = #{params.applyNo}
        </if>
            AND t.file_type in (82821002,82821003,82821004,82821005,82821006,82821007,82821008,82821013,82821014)
        and t.is_deleted=0
    </select>
    
    	<select id="selectCeoFile" resultType="integer">
		select count(t.id)
		from tt_goodwill_apply_file_info t
		where   t.apply_no = #{applyNo}  AND t.file_type=82821010 and t.is_deleted=0
		</select>
		<select id="selectVpFile" resultType="integer">
		select count(t.id)
		from tt_goodwill_apply_file_info t
		where   t.apply_no = #{applyNo}  AND t.file_type=82821009 and t.is_deleted=0
		</select>
		<select id="selectVpAndCeoFile" resultType="integer">
		select count(t.id)
		from tt_goodwill_apply_file_info t
		where   t.apply_no = #{applyNo}  AND (t.file_type =82821009 or t.file_type=82821010) and t.is_deleted=0
		</select>
</mapper>
