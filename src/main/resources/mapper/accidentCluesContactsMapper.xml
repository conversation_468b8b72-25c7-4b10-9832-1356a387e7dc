<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.accidentClues.AccidentCluesContactsMapper">

    <insert id="saveBatch">

        insert into tt_accident_clues_contacts (dealer_code, ac_id, contacts, contacts_phone, one_id, is_owner)
        values
        <foreach collection="contactList" item="contact" separator=",">
            (
             #{dealerCode},
            #{acId},
            #{contact.contacts},
            #{contact.contactsPhone},
            #{contact.oneId},
            #{contact.isOwner}
            )
        </foreach>
    </insert>

    <select id="getAccidentClueContacts" resultType="com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentClueContact">

        SELECT
            id,
            ac_id,
            contacts,
            contacts_phone,
            one_id,
            is_owner
        FROM
            tt_accident_clues_contacts
        WHERE dealer_code = #{dealerCode}
          AND is_deleted = 0
          AND ac_id IN
        <foreach collection="acIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="isOwner != null and isOwner != ''">
            AND is_owner = #{isOwner}
        </if>
    </select>

    <select id="queryAcIdByContactInfo" resultType="java.lang.Integer">
        SELECT ac_id FROM tt_accident_clues_contacts
        WHERE
        <choose>
            <when test="param.length() == 11">
                contacts_phone = #{param}
            </when>
            <otherwise>
                contacts_phone like concat(#{param},'%') OR contacts like concat(#{param},'%')
            </otherwise>
        </choose>
        AND is_deleted = 0
        group by ac_id
    </select>
</mapper>
