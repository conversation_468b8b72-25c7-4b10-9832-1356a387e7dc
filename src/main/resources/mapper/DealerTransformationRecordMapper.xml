<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.inviteInsurance.DealerTransformationRecordMapper">
<select id="selectOneBydealerCode" resultType="com.yonyou.dmscus.customer.dto.DealerTransformationRecordPO" >
  select  *   from   tt_dealer_transformation_record where    dealer_code=  #{dealerCode} and is_deleted = 0
</select>
</mapper>