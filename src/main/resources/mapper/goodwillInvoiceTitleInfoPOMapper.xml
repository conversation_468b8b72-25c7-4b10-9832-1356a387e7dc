<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillInvoiceTitleInfoMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillInvoiceTitleInfoPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                        <result column="APP_ID" property="appId"/>
               <result column="OWNER_CODE" property="ownerCode"/>
               <result column="OWNER_PAR_CODE" property="ownerParCode"/>
               <result column="ORG_ID" property="orgId"/>
               <result column="invoice_title" property="invoiceTitle"/>
               <result column="name" property="name"/>
               <result column="taxpayer_identification_number" property="taxpayerIdentificationNumber"/>
               <result column="phone" property="phone"/>
               <result column="address" property="address"/>
               <result column="open_bank" property="openBank"/>
               <result column="account" property="account"/>
               <result column="is_valid" property="isValid"/>
               <result column="is_deleted" property="isDeleted"/>
               <result column="created_at" property="createdAt"/>
               <result column="updated_at" property="updatedAt"/>
               <result column="created_by" property="createdBy"/>
               <result column="updated_by" property="updatedBy"/>
               <result column="record_version" property="recordVersion"/>
           </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, invoice_title, name, taxpayer_identification_number, phone, address, open_bank, account, is_valid, is_deleted, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillInvoiceTitleInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tm_goodwill_invoice_title_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.invoiceTitle !=null and params.invoiceTitle != '' ">
                AND t.invoice_title = #{params.invoiceTitle}
            </if>
                    <if test=" params.name !=null and params.name != '' ">
                AND t.name = #{params.name}
            </if>
                    <if test=" params.taxpayerIdentificationNumber !=null and params.taxpayerIdentificationNumber != '' ">
                AND t.taxpayer_identification_number = #{params.taxpayerIdentificationNumber}
            </if>
                    <if test=" params.phone !=null and params.phone != '' ">
                AND t.phone = #{params.phone}
            </if>
                    <if test=" params.address !=null and params.address != '' ">
                AND t.address = #{params.address}
            </if>
                    <if test=" params.openBank !=null and params.openBank != '' ">
                AND t.open_bank = #{params.openBank}
            </if>
                    <if test=" params.account !=null and params.account != '' ">
                AND t.account = #{params.account}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillInvoiceTitleInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tm_goodwill_invoice_title_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.invoiceTitle !=null and params.invoiceTitle != '' ">
                AND t.invoice_title = #{params.invoiceTitle}
            </if>
                    <if test=" params.name !=null and params.name != '' ">
                AND t.name = #{params.name}
            </if>
                    <if test=" params.taxpayerIdentificationNumber !=null and params.taxpayerIdentificationNumber != '' ">
                AND t.taxpayer_identification_number = #{params.taxpayerIdentificationNumber}
            </if>
                    <if test=" params.phone !=null and params.phone != '' ">
                AND t.phone = #{params.phone}
            </if>
                    <if test=" params.address !=null and params.address != '' ">
                AND t.address = #{params.address}
            </if>
                    <if test=" params.openBank !=null and params.openBank != '' ">
                AND t.open_bank = #{params.openBank}
            </if>
                    <if test=" params.account !=null and params.account != '' ">
                AND t.account = #{params.account}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>
          <select id="queryGoodwillInvoiceTitleInfoByInvoiceTitle" resultType="integer" >
        	SELECT
           count(id)
        	FROM tm_goodwill_invoice_title_info t
        	WHERE 1=1  and (t.is_valid = ******** or t.is_valid is null)
            <if test=" invoiceTitle !=null and invoiceTitle != '' ">
                AND t.invoice_title = #{invoiceTitle}
            </if>
            <if test=" id !=null and id != '' ">
                AND t.id<![CDATA[ <> ]]> #{id}
            </if>
          </select>
          
          <select id="queryInvoiceInfo" resultMap="BaseResultMap" >
            select *  from  tm_goodwill_invoice_title_info
            where invoice_title=#{invoiceTitle} and is_valid=******** and is_deleted=0
            
            </select>

</mapper>
