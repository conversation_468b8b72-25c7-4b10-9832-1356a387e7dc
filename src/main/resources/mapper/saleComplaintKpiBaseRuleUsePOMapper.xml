<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintKpiBaseRuleUseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintKpiBaseRuleUsePO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="rule_id" property="ruleId"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="user" property="user"/>
        <result column="warn_value" property="warnValue"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
    </resultMap>
    <resultMap id="BaseResultMap1" type="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRuleUseTestPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="rule_id" property="ruleId"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="user" property="user"/>
        <result column="warn_value" property="warnValue"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, rule_id, dealer_code, user, warn_value, data_sources, is_deleted, is_valid, created_at, updated_at
        </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintKpiBaseRuleUsePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_kpi_base_rule_use t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.ruleId !=null and params.ruleId != '' ">
            AND t.rule_id = #{params.ruleId}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.user !=null and params.user != '' ">
            AND t.user = #{params.user}
        </if>
        <if test=" params.warnValue !=null and params.warnValue != '' ">
            AND t.warn_value = #{params.warnValue}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintKpiBaseRuleUsePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_kpi_base_rule_use t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.ruleId !=null and params.ruleId != '' ">
            AND t.rule_id = #{params.ruleId}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.user !=null and params.user != '' ">
            AND t.user = #{params.user}
        </if>
        <if test=" params.warnValue !=null and params.warnValue != '' ">
            AND t.warn_value = #{params.warnValue}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql1" resultMap="BaseResultMap1"
            parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRuleUseTestPO">
        select t.app_id, t.owner_code,ifnull(t1.warn_value,t.index_value) as warn_value, t.owner_par_code, t.org_id, t.id,
        t.kpi_name, t.kpi, t.index_value, t.rule_name, t.rule, t.score, t.formula,
        t.data_sources, t.is_deleted, t.is_valid, t.created_at, t.updated_at
        from tt_sale_complaint_kpi_base_rule t
        LEFT JOIN tt_sale_complaint_kpi_base_rule_use t1 on t.id=t1.rule_id and
        t1.dealer_code=#{params.dealerCode}
        <if test=" params.user !=null and params.user != '' ">
            AND t1.user = #{params.user}
        </if>
        where
        t.is_deleted=0
        and t.data_sources=82101001
    </select>

</mapper>
