<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleCustomerNumberMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleCustomerNumberPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="insurance_id" property="insuranceId"/>
        <result column="insurance_detail_id" property="insuranceDetailId"/>
        <result column="call_id" property="callId"/>
        <result column="sa_id" property="saId"/>
        <result column="cus_name" property="cusName"/>
        <result column="cus_number" property="cusNumber"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="dealer_code" property="dealerCode"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        app_id, owner_code, owner_par_code, org_id, id, follow_id, call_id, sa_id, cus_name, cus_number,
        data_sources, is_deleted, is_valid,dealer_code, created_at, updated_at
    </sql>


    <select id="selectCusList" resultMap="BaseResultMap">
        SELECT cus_name,cus_number from tt_invite_insurance_vehicle_customer_number
        where cus_number is not null and cus_number != ''
         and insurance_id=#{id}
        UNION
        SELECT name,tel from tt_invite_insurance_vehicle_record
        where tel is not null and tel != ''
        and id=#{id}
    </select> 

    <update id="updateInsuranceDetailId">
        update tt_invite_insurance_vehicle_customer_number set insurance_detail_id=#{insuranceDetailId} where insurance_id=#{insuranceId} and (insurance_detail_id is null or insurance_detail_id = '')
    </update>


    <select id="selectInsuranceUserByVin" resultType="com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleCustomerNumberPO">
        SELECT applicant_mobile as applicantMobile,
        applicant_name as applicantName,
        policy_holder_mobile as policyHolderMobile,
        policy_holder_name as policyHolderName
        from tt_insurance_bill
        where (
        (applicant_mobile is not null and applicant_mobile != ''
        and applicant_name is not null and applicant_name != '')
        or (policy_holder_mobile is not null and policy_holder_mobile != ''
        and policy_holder_name is not null and policy_holder_name != '')
        )
        and vin = #{vin}
        and dealer_code = #{dealerCode}
        order by created_at desc limit 0,1
    </select>

</mapper>
