<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.faultLight.RvdcVehOnlineOfflineChangeRecordMapper">

    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.faultLight.RvdcVehOnlineOfflineChangeRecordPO">
        <result column="id" property="id"/>
        <result column="vin" property="vin"/>
        <result column="stat_type" property="statType"/>
        <result column="send_time" property="sendTime"/>
        <result column="province_id" property="provinceId"/>
        <result column="province_name_cn" property="provinceNameCn"/>
        <result column="city_id" property="cityId"/>
        <result column="city_name_cn" property="cityNameCn"/>
        <result column="dist_id" property="distId"/>
        <result column="dist_name_cn" property="distNameCn"/>
        <result column="mileage" property="mileage"/>
        <result column="etl_time" property="etlTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="remarks" property="remarks"/>
        <result column="created_at" property="createdAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="create_sqlby" property="createSqlby"/>
        <result column="update_sqlby" property="updateSqlby"/>
    </resultMap>

    <select id="selectEndTime" resultMap="BaseResultMap">
        SELECT id,veh_vin,stat_type,send_time,mileage
        FROM tt_rvdc_veh_online_offline_change_record
            WHERE stat_type = 13
                AND veh_vin = #{vin}
                AND send_time >= #{startTime}
        ORDER BY send_time
    </select>


</mapper>