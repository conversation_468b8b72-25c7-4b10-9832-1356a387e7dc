<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillAuditProcessMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAuditProcessPO">
                        <result column="OWNER_CODE" property="ownerCode"/>
                        <result column="OWNER_PAR_CODE" property="ownerParCode"/>
                        <result column="ORG_ID" property="orgId"/>
                        <result column="audit_object" property="auditObject"/>
                        <result column="audit_type" property="auditType"/>
                        <result column="audit_position" property="auditPosition"/>
                        <result column="min_value_relation" property="minValueRelation"/>
                        <result column="min_audit_price" property="minAuditPrice"/>
                        <result column="max_value_relation" property="maxValueRelation"/>
                        <result column="max_audit_price" property="maxAuditPrice"/>
                        <result column="audit_sort" property="auditSort"/>
                        <result column="is_valid" property="isValid"/>
                        <result column="is_deleted" property="isDeleted"/>
                        <result column="created_at" property="createdAt"/>
                        <result column="updated_at" property="updatedAt"/>
                        <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, audit_object, audit_type, audit_position, min_value_relation, min_audit_price, max_value_relation, max_audit_price, audit_sort, is_valid, is_deleted, created_at, updated_at
        </sql>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAuditProcessPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tm_goodwill_audit_process t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.auditObject !=null and params.auditObject != '' ">
                AND t.audit_object = #{params.auditObject}
            </if>
                    <if test=" params.auditType !=null and params.auditType != '' ">
                AND t.audit_type = #{params.auditType}
            </if>
                    <if test=" params.auditPosition !=null and params.auditPosition != '' ">
                AND t.audit_position = #{params.auditPosition}
            </if>
                    <if test=" params.minValueRelation !=null and params.minValueRelation != '' ">
                AND t.min_value_relation = #{params.minValueRelation}
            </if>
                    <if test=" params.minAuditPrice !=null and params.minAuditPrice != '' ">
                AND t.min_audit_price = #{params.minAuditPrice}
            </if>
                    <if test=" params.maxValueRelation !=null and params.maxValueRelation != '' ">
                AND t.max_value_relation = #{params.maxValueRelation}
            </if>
                    <if test=" params.maxAuditPrice !=null and params.maxAuditPrice != '' ">
                AND t.max_audit_price = #{params.maxAuditPrice}
            </if>
                    <if test=" params.auditSort !=null and params.auditSort != '' ">
                AND t.audit_sort = #{params.auditSort}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAuditProcessPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tm_goodwill_audit_process t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.auditObject !=null and params.auditObject != '' ">
                AND t.audit_object = #{params.auditObject}
            </if>
                    <if test=" params.auditType !=null and params.auditType != '' ">
                AND t.audit_type = #{params.auditType}
            </if>
                    <if test=" params.auditPosition !=null and params.auditPosition != '' ">
                AND t.audit_position = #{params.auditPosition}
            </if>
                    <if test=" params.minValueRelation !=null and params.minValueRelation != '' ">
                AND t.min_value_relation = #{params.minValueRelation}
            </if>
                    <if test=" params.minAuditPrice !=null and params.minAuditPrice != '' ">
                AND t.min_audit_price = #{params.minAuditPrice}
            </if>
                    <if test=" params.maxValueRelation !=null and params.maxValueRelation != '' ">
                AND t.max_value_relation = #{params.maxValueRelation}
            </if>
                    <if test=" params.maxAuditPrice !=null and params.maxAuditPrice != '' ">
                AND t.max_audit_price = #{params.maxAuditPrice}
            </if>
                    <if test=" params.auditSort !=null and params.auditSort != '' ">
                AND t.audit_sort = #{params.auditSort}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>
            
            <select id="selectAuditProcessInfo" resultType="java.util.HashMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAuditProcessPO">
	            SELECT 
	             
	 		MAX( 	case when audit_object=0   and  audit_type=0  and  audit_position= 1 then min_value_relation else 10011002 end)  areaMinRelation,
	 		MAX( 	case when audit_object=0   and  audit_type=0  and  audit_position= 1 then min_audit_price else '' end)  areaMinAmount,
	 		MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 1 then max_value_relation else 10011002 end)  areaMaxRelation,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 1 then max_audit_price else '' end) areaMaxAmount,
	 		MAX( 	case when audit_object=0   and  audit_type=0  and  audit_position= 1 then id else '' end) areaId,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 2 then min_value_relation else 10011001 end)  bigAreaMinRelation,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 2 then min_audit_price else '' end ) bigAreaMinAmount,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 2 then max_value_relation else 10011002 end)  bigAreaMaxRelation,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 2 then max_audit_price else '' end) bigAreaMaxAmount,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 2 then id else '' end) bigAreaId,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 3 then min_value_relation else 10011001 end)  regionalDirectMinRelation,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 3 then min_audit_price else '' end)  regionalDirectMinAmount,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 3 then max_value_relation else 10011002 end)  regionalDirectMaxRelation,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 3 then max_audit_price else '' end)  regionalDirectMaxAmount,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 3 then id else '' end) regionalDirectId,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 4 then min_value_relation else 10011001 end)  vpMinRelation,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 4 then min_audit_price else '' end)  vpminAmount,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 4 then max_value_relation else 10011002 end)  vpMaxRelation,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 4 then max_audit_price else '' end) vpMaxAmount,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 4 then id else '' end) vpId,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 5 then min_audit_price else '' end)  ceoAmount,
			MAX(	case when audit_object=0   and  audit_type=0  and  audit_position= 5 then id else '' end) ceoId,
			MAX(	case when audit_object=0   and  audit_type=1  and  audit_position= 1 then min_value_relation else 10011002 end)  ccmHighManageMinRelation,
			MAX(	case when audit_object=0   and  audit_type=1  and  audit_position= 1 then min_audit_price else '' end) ccmHighManageMinAmount,
			MAX(	case when audit_object=0   and  audit_type=1  and  audit_position= 1 then max_value_relation else 10011002 end)  ccmHighManageMaxRelation,
			MAX(	case when audit_object=0   and  audit_type=1  and  audit_position= 1 then max_audit_price else '' end) ccmHighManageMaxAmount,
			MAX(	case when audit_object=0   and  audit_type=1  and  audit_position= 1 then id else '' end) ccmHighManageId,
			MAX(	case when audit_object=0   and  audit_type=1  and  audit_position= 2 then min_value_relation else 10011001 end)  ccDirectMinRelation,
			MAX(	case when audit_object=0   and  audit_type=1  and  audit_position= 2 then min_audit_price else '' end) ccDirectMinAmount,
			MAX(	case when audit_object=0   and  audit_type=1  and  audit_position= 2 then max_value_relation else 10011002 end)  ccDirectMaxRelation,
		    MAX(	case when audit_object=0   and  audit_type=1  and  audit_position= 2 then max_audit_price else '' end) ccDirectMaxAmount,
			MAX(	case when audit_object=0   and  audit_type=1  and  audit_position= 2 then id else '' end) ccDirectId,      
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 4 then min_value_relation else 10011002 end)  regionalDirectMinRelations,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 4 then min_audit_price else '' end) regionalDirectMinAmounts,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 4 then max_value_relation else 10011002 end)  regionalDirectMaxRelations,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 4 then max_audit_price else '' end) regionalDirectMaxAmounts,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 4 then id  else '' end) regionalDirectIds,
			MAX(	case when audit_object=1   and  audit_type=1  and  audit_position= 3 then min_value_relation else 10011002 end)  ccmDirectMinRelation,
			MAX(	case when audit_object=1   and  audit_type=1  and  audit_position= 3 then min_audit_price else '' end)  ccmDirectMinAmount,
			MAX(	case when audit_object=1   and  audit_type=1  and  audit_position= 3 then max_value_relation else 10011002 end)  ccmDirectMaxRelation,
			MAX(	case when audit_object=1   and  audit_type=1  and  audit_position= 3 then max_audit_price else '' end) ccmDirectMaxAmount,
			MAX(	case when audit_object=1   and  audit_type=1  and  audit_position= 3 then id else '' end) ccmDirectId,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 5 then min_value_relation else 10011001 end)  vpMinRelations,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 5 then min_audit_price else '' end) vpMinAmounts,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 5 then max_value_relation else 10011002 end)  vpMaxRelations,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 5 then max_audit_price else '' end) vpMaxAmounts,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 5 then id else '' end) vpIds,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 6 then min_value_relation else 10011001 end)  financeManageMinRelation,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 6 then min_audit_price else '' end)  financeManageMinAmount,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 6 then max_value_relation else 10011002 end)  financeManageMaxRelation,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 6 then max_audit_price else '' end) financeManageMaxAmount,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 6 then id else '' end) financeManageId,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 7 then min_audit_price else '' end) cfoAmount,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 7 then id else '' end) cfoId,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 8 then min_audit_price else '' end) ceoAmounts,
			MAX(	case when audit_object=1   and  audit_type=0  and  audit_position= 8 then id else '' end ) ceoIds,
			MAX(	case when audit_object=2   and  audit_type=2  and  audit_position= 2 then month else '' end ) month,
			MAX(	case when audit_object=2   and  audit_type=2  and  audit_position= 2 then id else '' end ) monthId
				
	            FROM tm_goodwill_audit_process t
	            WHERE 1=1 and (t.is_valid = 10011001 or t.is_valid is null)
	            and is_deleted=0
            </select>
            
         <select id="selectList" resultMap="BaseResultMap">
        	SELECT 
        	<include refid="Base_Column_List"/> 
        	FROM  tm_goodwill_audit_process t
        	WHERE 1=1 AND  (t.audit_type=#{auditType} or t.is_public=10041001) order by audit_object,audit_position
		</select>
		<select id="queryRefuseTime" resultType="java.util.HashMap">
        	SELECT 
        	t.month
        	FROM  tm_goodwill_audit_process t
        	WHERE 1=1 AND t.audit_type=2 and t.audit_object=2 and t.audit_position=2
		</select>
		
		
</mapper>
