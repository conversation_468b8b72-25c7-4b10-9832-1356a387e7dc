<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.faultLight.TtFaultLightFollowRecordMapper">

    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightFollowRecordPO" >
        <result column="id" property="id" />
        <result column="clue_id" property="clueId" />
        <result column="fault_id" property="faultId" />
        <result column="fault_city_name" property="faultCityName" />
        <result column="clue_dis_time" property="clueDisTime" />
        <result column="follow_time" property="followTime" />
        <result column="clue_status" property="clueStatus" />
        <result column="follow_status" property="followStatus" />
        <result column="follow_name" property="followName" />
        <result column="is_deleted" property="isDeleted" />
        <result column="created_at" property="createdAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="create_sqlby" property="createSqlby" />
        <result column="update_sqlby" property="updateSqlby" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
                clue_id,
                fault_id,
                fault_city_name,
                clue_dis_time,
                follow_time,
                clue_status,
                follow_status,
                follow_name,
                is_deleted,
                created_at,
                created_by,
                updated_at,
                updated_by,
                create_sqlby,
                update_sqlby
    </sql>
    <insert id="batchInsert">
        INSERT INTO
            tt_fault_light_follow_record
            (fault_id,
             fault_city_name,
             clue_dis_time,
             clue_status,
             follow_status,
             clue_id,
             follow_name,
             follow_time)
        VALUES
        <foreach collection ="params" item="params" separator =",">
            (#{params.faultId},
             #{params.faultCityName},
             #{params.clueDisTime},
             #{params.clueStatus},
             #{params.followStatus},
             #{params.clueId},
             #{params.followName},
             #{params.followTime})
        </foreach >

    </insert>
    <update id="updateFollowRecord">
        UPDATE
            tt_fault_light_follow_record t
        SET
            t.clue_status = #{params.clueStatus},
            t.follow_status = #{params.followStatus}
        WHERE
            t.clue_id = #{params.clueId}
          AND t.clue_status = #{clueState}
    </update>

    <select id="queryFollowRecord"
            resultType="com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightFollowRecordDTO">
        SELECT
        t.id as id,
        t.clue_id as clueId,
        t.fault_id as faultId,
        t.fault_city_name as faultCityName,
        t.clue_dis_time as clueDisTime,
        t.follow_time as followTime,
        t.clue_status as clueStatus,
        t.follow_status as followStatus,
        t.follow_name as followName,
        t1.warning_name as warningName
        FROM tt_fault_light_follow_record t
        LEFT JOIN
        tt_fault_light_dispose t1 ON t.fault_id = t1.id
        WHERE 1=1
        <if test=" params.clueId !=null">
            AND t.clue_id = #{params.clueId}
        </if>
        <if test=" params.faultId !=null">
            AND t.fault_id = #{params.faultId}
        </if>
        <if test=" params.faultCityName !=null and params.faultCityName != '' ">
            AND t.fault_city_name = #{params.faultCityName}
        </if>
        <if test=" params.clueDisTime !=null">
            AND t.clue_dis_time = #{params.clueDisTime}
        </if>
        <if test=" params.followTime !=null">
            AND t.follow_time = #{params.followTime}
        </if>
        <if test=" params.clueStatus !=null">
            AND t.clue_status = #{params.clueStatus}
        </if>
        <if test=" params.followStatus !=null">
            AND t.follow_status = #{params.followStatus}
        </if>
        <if test=" params.followName !=null and params.followName != '' ">
            AND t.follow_name = #{params.followName}
        </if>
        AND t.is_deleted = 0
        ORDER BY t.created_at DESC
    </select>

</mapper>