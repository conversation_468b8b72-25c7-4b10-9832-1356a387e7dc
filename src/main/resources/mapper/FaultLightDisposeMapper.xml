<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yonyou.dmscus.customer.dao.faultLight.FaultLightDisposeMapper">

    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.faultLight.FaultLightPO">
        <id column="id" property="id"/>
        <result column="waring_en_description" property="waringEnDescription"/>
        <result column="waring_cn_description" property="waringCnDescription"/>
        <result column="warning_name" property="warningName"/>
        <result column="fault_grade" property="faultGrade"/>
        <result column="num" property="num"/>
        <result column="expire_number" property="expireNumber"/>
        <result column="produce_status" property="produceStatus"/>
        <result column="duty_status" property="dutyStatus"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="created_at" property="createdAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="create_sqlby" property="createSqlby"/>
        <result column="update_sqlby" property="updateSqlby"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, waring_en_description, waring_cn_description,warning_name, warning_en_name, fault_grade, num, expire_number,
        produce_status, duty_status, is_deleted, created_at, created_by, updated_at, updated_by, create_sqlby, update_sqlby,
        vehicle_model_ids
    </sql>
    <insert id="addTypeInfo">
        insert into tt_fault_light_dispose(
        id, waring_en_description, waring_cn_description,warning_name, warning_en_name, fault_grade, num, expire_number, produce_status, duty_status
        ) values
        <foreach collection ="params" item="params" separator =",">
            (#{params.id},
            #{params.waringEnDescription},
            #{params.waringCnDescription},
            #{params.warningName},
            #{params.warningEnName},
            #{params.faultGrade},
            #{params.num},
            #{params.expireNumber},
            #{params.produceStatus},
            #{params.dutyStatus}
             )
        </foreach >
    </insert>

    <update id="batchUpdate" parameterType="list">
            update
            tt_fault_light_dispose t
            <set>
                <if test=" params.produceStatus !=null">
                    t.produce_status = #{params.produceStatus},
                </if>
                <if test=" params.dutyStatus !=null">
                    t.duty_status = #{params.dutyStatus},
                </if>
                <if test=" params.num !=null">
                    t.num = #{params.num},
                </if>
                <if test=" params.expireNumber !=null">
                    t.expire_number = #{params.expireNumber},
                </if>
            </set>
            <where>
                t.id in
                <foreach close=")" collection="listItem" item="listItem" open="(" separator=",">
                    #{listItem}
                </foreach>
            </where>
    </update>

    <select id="selectTypeInfo" resultType="com.yonyou.dmscus.customer.entity.po.faultLight.FaultLightPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_fault_light_dispose t
        WHERE
        t.warning_name = #{params.warningName}
        AND t.is_deleted = 0
    </select>

    <select id="selectPageTypeInfo" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.faultLight.FaultLightPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_fault_light_dispose t
        WHERE 1=1
        <if test=" params.warningName !=null and params.warningName != '' ">
            AND t.warning_name like CONCAT(#{params.warningName},'%')
        </if>
        <if test=" params.produceStatus !=null">
            AND t.produce_status = #{params.produceStatus}
        </if>
        <if test=" params.dutyStatus !=null">
            AND t.duty_status = #{params.dutyStatus}
        </if>
        AND t.is_deleted = 0
    </select>

    <select id="selectDutyStatusByIds" resultType="java.lang.Integer">
        SELECT count(1)
        FROM tt_fault_light_dispose t
        WHERE
        t.is_deleted = 0
        AND duty_status = 1
        AND id in
        <foreach collection="list" item="list" open="(" separator="," close=")">
            #{list}
        </foreach>
    </select>
</mapper>