<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.faultLight.DtcCodeMapper">

    <select id="selectNameByEcuDtc" resultType="com.yonyou.dmscus.customer.entity.dto.faultLight.DtcCodeDTO">
        SELECT ecu_dtc, dtc_name FROM tm_dtc_code_mapping
        WHERE
        is_deleted = 0
        and ecu_dtc IN
        <foreach item="ecuDtc" collection="list" open="(" separator="," close=")">
            #{ecuDtc}
        </foreach>
    </select>

</mapper>
