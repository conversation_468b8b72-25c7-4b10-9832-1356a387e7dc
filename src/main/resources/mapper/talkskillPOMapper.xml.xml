<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.talkskill.TalkskillMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillPO">
        <id column="talk_id" property="talkId"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="title" property="title"/>
        <result column="type" property="type"/>
        <result column="begin_date" property="beginDate"/>
        <result column="end_date" property="endDate"/>
        <result column="tag1" property="tag1"/>
        <result column="tag2" property="tag2"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="vcdc_id" property="vcdcId"/>
        <result column="is_dealer_mod" property="isDealerMod"/>
        <result column="org_id" property="orgId"/>
        <result column="talkskill" property="talkskill"/>
        <result column="keyword1" property="keyword1"/>
        <result column="keyword2" property="keyword2"/>
        <result column="keyword3" property="keyword3"/>
        <result column="is_compel" property="isCompel"/>
        <result column="is_enable" property="isEnable"/>
        <result column="is_vcdc" property="isVcdc"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.talk_id, t.dealer_code, t.title, t.type, t.begin_date, t.end_date, t.tag1, t.tag2, t.talkskill, t.keyword1,
        t.keyword2, t.keyword3, t.is_compel, t.is_enable, t.is_vcdc, t.data_sources, t.is_deleted, t.is_valid,t.vcdc_id,t.is_dealer_mod
    </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillPO">
        SELECT
         t.talk_id,t.org_id,t.owner_code,t.dealer_code,t.title, t.type, t.begin_date, t.end_date, t.tag1, t.tag2, t.talkskill, t.keyword1, t.keyword2, t.keyword3, t.is_compel, t.is_enable, t.is_vcdc, t.data_sources, t.is_deleted, t.is_valid,
        (SELECT tag1.tag_name FROM tt_talkskill_tag tag1 WHERE tag1.tag_id = t.tag1 ) as tag1_name,(SELECT tag2.tag_name FROM tt_talkskill_tag tag2 WHERE tag2.tag_id = t.tag2 ) as tag2_name
        FROM tt_talkskill t
        WHERE 1=1
        <if test=" params.talkId !=null and params.talkId != '' ">
            AND t.talk_id = #{params.talkId}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.title !=null and params.title != '' ">
            AND t.title LIKE "%"#{params.title}"%"
        </if>
        <if test=" params.type !=null and params.type != '' ">
            AND t.type = #{params.type}
        </if>
        <if test=" params.beginDate !=null  ">
            AND t.begin_date >= #{params.beginDate}
        </if>
        <if test=" params.endDate !=null  ">
            AND t.end_date &lt;= #{params.endDate}
        </if>
        <if test=" params.tag1 !=null and params.tag1 != '' ">
            AND t.tag1 = #{params.tag1}
        </if>
        <if test=" params.tag2 !=null and params.tag2 != '' ">
            AND t.tag2 = #{params.tag2}
        </if>
        <if test=" params.talkskill !=null and params.talkskill != '' ">
            AND t.talkskill = #{params.talkskill}   
        </if>
        <if test=" params.keyword1 !=null and params.keyword1 != '' ">
           AND (t.keyword1 like "%"#{params.keyword1}"%" OR t.keyword2 like "%"#{params.keyword1}"%" OR t.keyword3 like "%"#{params.keyword1}"%")
        </if>
 
        <if test=" params.isCompel !=null and params.isCompel != '' ">
            AND t.is_compel = #{params.isCompel}
        </if>
        <if test=" params.isEnable !=null and params.isEnable != '' ">
            AND t.is_enable = #{params.isEnable}
        </if>
        <if test=" params.isVcdc !=null ">
            AND t.is_vcdc = #{params.isVcdc}
        </if>

        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        <if test=" params.vcdcId !=null ">
            AND t.vcdc_id = #{params.vcdcId}
        </if>
        <if test=" params.isDealerMod !=null ">
            AND t.is_dealer_mod = #{params.isDealerMod}
        </if>
        order by t.created_at desc
    </select>
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillPO" >
        select
        <include refid="Base_Column_List"/>
        from tt_talkskill t
        where 1=1
        <if test=" params.vcdcId !=null ">
            AND t.vcdc_id = #{params.vcdcId}
        </if>
        <if test=" params.isDealerMod !=null ">
            AND t.is_dealer_mod = #{params.isDealerMod}
        </if>

    </select>
    <update id="updataByIdEnable" parameterType="com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillDTO" >
        update tt_talkskill set is_enable = #{isEnable} where talk_id = #{talkId}
    </update>

    <select id="queryTalkskill" resultMap="BaseResultMap" >
        select
          <include refid="Base_Column_List"/>
        from tt_talkskill t
        left join tt_talkskill_tag t1 on t.tag1=t1.tag_id
        left join tt_talkskill_tag t2 on t.tag2=t2.tag_id
        left join tt_talkskill_type t3 on t.type=t3.type_id
        where t.is_enable=10031001 and t.dealer_code=#{dealerCode}
          and ((t3.type_name LIKE "%"#{type}"%" and locate(#{name},t1.tag_name)!=0) or (t1.tag_name LIKE
        "%"#{type}"%" and
            locate(#{name},t2.tag_name)!=0))
    </select>

    <select id="queryTalkskill1" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_talkskill t
        where t.is_enable=10031001 and t.dealer_code=#{dealerCode}
        and (
          (locate(t.keyword1,#{keyword})!=0 and  t.keyword1 is not null and t.keyword1!='')
        or(locate(t.keyword2,#{keyword})!=0 and  t.keyword2 is not null and t.keyword2!='')
        or(locate(t.keyword3,#{keyword})!=0 and  t.keyword3 is not null and t.keyword3!='')
        )
    </select>

</mapper>
