<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.voc.VocInviteVehicleTaskRecordMapper">

    <insert id="insertList" parameterType="com.yonyou.dmscus.customer.entity.po.vocfunctional.VocInviteVehicleTaskRecordPo"  useGeneratedKeys="true" keyProperty="id">


        insert into  tt_voc_invite_vehicle_task_record
        (
        vin,
        task_id,
        record_id,
        invite_type,
        record_type,
        loss_type,
        version,
        created_by,
        verify_status,
        verify_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.vin},
            #{item.taskId},
            <choose>
                <when test="item.recordId !=null">
                    #{item.recordId},
                </when>
                <otherwise>
                  0,
                </otherwise>
            </choose>
            #{item.inviteType},
            #{item.recordType},
            #{item.lossType},
            1,
            '-9',
            #{item.verifyStatus},
            #{item.verifyTime}
            )
        </foreach>

    </insert>
    <select id="selectVocCount"  resultType="java.lang.Integer">
        select count(r1.vin ) from tt_invite_vehicle_record r1
        left join
        tt_voc_invite_vehicle_task_record r2  on  r1.id = r2.record_id
        where r1.vin =#{vin} and r1.id =#{id}  and r1.order_status =82411002 and  r1.is_deleted =0 and  r1.invite_type in  (82381001,82381002,82381006,82381012)  and  r2.record_type = 1  and r2.is_deleted = 0  and  r2.record_id  is not null
    </select>
    <select id="selectVocCountT"  resultType="java.lang.Integer">
        select count(r1.vin ) from tt_invite_vehicle_record r1
                                       left join
                                   tt_voc_invite_vehicle_task_record r2  on  r1.id = r2.record_id
        where r1.vin =#{vin} and r1.id =#{id}  and r1.order_status =82411002 and  r1.is_deleted =0 and  r1.invite_type in  (82381001,82381002,82381006)  and  r2.record_type = 1  and r2.is_deleted = 0  and  r2.record_id  is not null
    </select>
    <select id="selectTypeXIIByVin" parameterType="java.lang.String" resultType="java.lang.Long">
        select r1.id from tt_invite_vehicle_record r1
        left join
        tt_voc_invite_vehicle_task_record r2  on  r1.id = r2.record_id
        where r1.vin =#{vin} and r1.order_status =82411002 and  r1.is_deleted =0  and  r1.invite_type =82381012  and  r2.record_type = 1  and r2.is_deleted = 0  and  r2.record_id  is not null
        and    r1.advise_in_date &lt;=date_add(NOW(),interval -3 month )
    </select>

    <select id="selectLossWarningByVin" parameterType="java.lang.String" resultType="java.lang.Integer">
        select COUNT(1) from tt_invite_vehicle_record r1
                                 left join
                             tt_voc_invite_vehicle_task_record r2  on  r1.id = r2.record_id
        where r1.vin =#{vin}
          and r1.order_status =82411002 and  r1.is_deleted =0  and  r1.invite_type =82381012  and  r2.record_type = 1  and r2.is_deleted = 0  and  r2.record_id  is not null
    </select>

    <select id="selectTaskRecordByInviteId" resultType="java.lang.Integer">
        select COUNT(1) from tt_voc_invite_vehicle_task_record
        where record_id =#{recordId}
    </select>

    <select id="selectCountByIcmId" resultType="java.lang.Integer">
        select COUNT(1) from tt_voc_invite_vehicle_task_record
        where icm_id =#{icmId}
    </select>

    <select id="selectIcmIdByRecordId" resultType="com.yonyou.dmscus.customer.entity.po.vocfunctional.VocInviteVehicleTaskRecordPo">
        select icm_id,verify_status from tt_voc_invite_vehicle_task_record
        where record_id =#{recordId}
    </select>

    <select id="selectIdByLimit" resultType="com.yonyou.dmscus.customer.entity.dto.clue.dictionary.DictionaryIdDTO">
        select min(a.id) as minId, max(a.id) as maxId
        from (
                select id
                      from tt_voc_invite_vehicle_task_record
                      order by id LIMIT #{startPage},#{endPage}
              ) a
    </select>

    <update id="updateVerifyStatusById" >
        update tt_voc_invite_vehicle_task_record set verify_status = #{verifyStatus}, verify_time = #{verifyTime}
        where record_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateHistoryValidationStatusByOverdue" >
        UPDATE `dms_manage`.`tt_voc_invite_vehicle_task_record` set verify_status = 87911003
        where record_id in (
            select id from (
            select t1.id from tt_invite_vehicle_record t1
            left join `dms_manage`.`tt_voc_invite_vehicle_task_record` t2 on t1.id = t2.record_id
            where t1.invite_type in (82381001,82381002,82381006,82381012)
              and t1.source_type in (1,2,3)
              and t1.order_status = 82411003
              and t2.verify_status != 87911003
            ) a
        )
    </update>

    <update id="updateHistoryValidationStatusByComplete" >
        UPDATE `dms_manage`.`tt_voc_invite_vehicle_task_record` set verify_status = 87911003
        where record_id in (
            select id from (
            select t1.id from tt_invite_vehicle_record t1
            left join `dms_manage`.`tt_voc_invite_vehicle_task_record` t2 on t1.id = t2.record_id
            where t1.invite_type in (82381001,82381002,82381006,82381012)
              and t1.source_type in (1,2,3)
              and t1.order_status in (82411001, 82411004)
              and t1.order_finish_date is null
              and t2.verify_status != 87911003
            ) a
        )
    </update>

    <update id="updateHistoryValidationStatusByNoNull" >
        UPDATE `dms_manage`.`tt_voc_invite_vehicle_task_record` t2
        left join `dms_manage`.`tt_invite_vehicle_record` t1 on t2.record_id = t1.id
        set t2.verify_status = 87911002, t2.verify_time = t1.order_finish_date
        where t1.invite_type in (82381001,82381002,82381006,82381012)
        and t1.source_type in (1,2,3)
        and t1.order_status in (82411001, 82411004)
        and t1.order_finish_date is not null
        and t2.verify_status != 87911002
        and t2.verify_status != 87911003
    </update>

    <update id="updateTypeById" >
        update tt_voc_invite_vehicle_task_record SET loss_type = 87901001 where id >= #{minId} and id &lt;= #{maxId} and loss_type = 1;
        update tt_voc_invite_vehicle_task_record SET loss_type = 87901002 where id >= #{minId} and id &lt;= #{maxId} and loss_type = 2;
        update tt_voc_invite_vehicle_task_record SET loss_type = 87901003 where id >= #{minId} and id &lt;= #{maxId} and loss_type = 3;
        update tt_voc_invite_vehicle_task_record set record_type = 87891002 where id >= #{minId} and id &lt;= #{maxId} and record_type = 0;
        update tt_voc_invite_vehicle_task_record set record_type = 87891001 where id >= #{minId} and id &lt;= #{maxId} and record_type = 1;
    </update>

    <update id="updateByPo" >
        update tt_voc_invite_vehicle_task_record set record_id =#{recordId} where task_id=#{taskId}
    </update>
    <update id="updateRecordNum" parameterType="java.lang.Long" >
        update tt_voc_invite_vehicle_task_record set  record_num =  record_num +1  where record_id =#{recordId}
    </update>

    <update id="updateVerifyStatusAllById" parameterType="java.util.List">
        <!-- 循环遍历参数中的对象 -->
        <foreach collection="list" item="item" separator=";">
            update tt_voc_invite_vehicle_task_record set verify_status = #{item.verifyStatus}, verify_time = #{item.verifyTime}
            where record_id = #{item.recordId}
        </foreach>
    </update>

    <update id="updateDailyMile">
        <foreach collection="vinList" item="item"  separator=";">
            update tt_voc_invite_vehicle_task_record voc
            JOIN tt_invite_vehicle_record i ON i.id = voc.record_id
            set voc.daily_mile= #{item.dailyMile}
            where i.vin = #{item.vin}
            and i.invite_type IN (82381001,82381002)
            and date_format(i.advise_in_date,'%Y-%m') >= DATE_FORMAT(date_add(now(), interval 1 month),'%Y-%m')
            and i.order_status = 82411002
            and i.source_type = 4
            <if test=" item.whiteList !=null and item.whiteList.size() > 0 ">
                and i.dealer_code in
                <foreach
                        collection="item.whiteList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </foreach>
    </update>

    <update id="updateBevLeadList">
        update tt_voc_invite_vehicle_task_record set bev_flag=1
        where record_id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <select id="queryTtInviteVehicleRecordByOrderId" resultType="com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO">
    select
      t1.app_id,
      t1.owner_code,
      t1.owner_par_code,
      t1.org_id,
      t1.id,
      t1.parent_id,
      t1.is_main,
      t1.source_type,
      t1.vin,
      t1.license_plate_num,
      t1.name,
      t1.tel,
      t1.invite_type,
      t1.advise_in_date,
      t1.new_advise_in_date,
      t1.newest_advise_in_date,
      t1.plan_follow_date,
      t1.actual_follow_date,
      t1.plan_remind_date,
      t1.actual_remind_date,
      t1.SA_ID,
      t1.SA_NAME,
      t1.LAST_SA_ID,
      t1.LAST_SA_NAME,
      t1.follow_status,
      t1.is_book,
      t1.book_no,
      t1.order_status,
      t1.data_sources,
      t1.is_deleted,
      t1.is_valid,
      t1.created_by,
      t1.created_at,
      t1.updated_by,
      t1.updated_at,
      t1.record_version,
      t1.dealer_code,
      t1.age,
      t1.sex,
      t1.model,
      t1.part_item_rule_id,
      t1.last_change_date,
      t1.invite_time,
      t1.item_type,
      t1.item_code,
      t1.item_name,
      t1.first_follow_date,
      t1.order_finish_date,
      t1.mark,
      t1.one_id,
      t1.content,
      t1.lose_reason,
      t1.RO_NO,
      t1.REPAIR_TYPE_CODE,
      t1.OUT_MILEAGE,
      t1.RO_CREATE_DATE,
      t1.finish_dealer_code,
      t1.last_in_date,
      t2.icm_id
        from
          tt_invite_vehicle_record t1
          LEFT JOIN tt_voc_invite_vehicle_task_record t2 ON t1.`id` = t2.`record_id`
        where  t1.invite_type=82381013
        and t1.dealer_code=#{dealerCode}
        and t2.detail_Id=#{detailId}
        ORDER BY t1.created_at desc
        LIMIT 1,1

    </select>

</mapper>
