<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.common.LeadsBlackMapper">

    <select id="selectBlackByVinAndType" resultType="long">
        SELECT count(1)
        FROM tm_leads_black
        where vin = #{vin}
        and leads_type = #{leadsType}
        and is_deleted = 0
    </select>

</mapper>
