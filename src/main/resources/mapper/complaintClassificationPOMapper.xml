<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.complaint.ComplaintClassificationMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintClassificationPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                            <result column="app_id" property="appId"/>
                                                        <result column="owner_code" property="ownerCode"/>
                                                        <result column="owner_par_code" property="ownerParCode"/>
                                                        <result column="org_id" property="orgId"/>
                                                                                    <result column="parent_id" property="parentId"/>
                                                        <result column="cate_code" property="cateCode"/>
                                                        <result column="cate_name" property="cateName"/>
                                                        <result column="cate_level" property="cateLevel"/>
                                                        <result column="cate_status" property="cateStatus"/>
                                                        <result column="data_sources" property="dataSources"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="is_valid" property="isValid"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, parent_id, cate_code, cate_name, cate_level, cate_status, data_sources, is_deleted, is_valid, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintClassificationPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_classification t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.parentId !=null and params.parentId != '' ">
                AND t.parent_id = #{params.parentId}
            </if>
                    <if test=" params.cateCode !=null and params.cateCode != '' ">
                AND t.cate_code = #{params.cateCode}
            </if>
                    <if test=" params.cateName !=null and params.cateName != '' ">
                AND t.cate_name = #{params.cateName}
            </if>
                    <if test=" params.cateLevel !=null and params.cateLevel != '' ">
                AND t.cate_level = #{params.cateLevel}
            </if>
                    <if test=" params.cateStatus !=null and params.cateStatus != '' ">
                AND t.cate_status = #{params.cateStatus}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintClassificationPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_classification t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.parentId !=null and params.parentId != '' ">
                AND t.parent_id = #{params.parentId}
            </if>
                    <if test=" params.cateCode !=null and params.cateCode != '' ">
                AND t.cate_code = #{params.cateCode}
            </if>
                    <if test=" params.cateName !=null and params.cateName != '' ">
                AND t.cate_name = #{params.cateName}
            </if>
                    <if test=" params.cateLevel !=null and params.cateLevel != '' ">
                AND t.cate_level = #{params.cateLevel}
            </if>
                    <if test=" params.cateStatus !=null and params.cateStatus != '' ">
                AND t.cate_status = #{params.cateStatus}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
        and t.is_deleted=0
            </select>

    <select id="selectComplaintCategory" resultMap="BaseResultMap" >
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_classification t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.parentId !=null and params.parentId != '' ">
            AND t.parent_id = #{params.parentId}
        </if>
        <if test=" params.parentId1 !=null and params.parentId1 != '' ">
            AND t.parent_id in(${params.parentId1})
        </if>
        <if test=" params.cateCode !=null and params.cateCode != '' ">
            AND t.cate_code = #{params.cateCode}
        </if>
        <if test=" params.cateName !=null and params.cateName != '' ">
            AND t.cate_name = #{params.cateName}
        </if>
        <if test=" params.cateLevel !=null and params.cateLevel != '' ">
            AND t.cate_level = #{params.cateLevel}
        </if>
        <if test=" params.cateStatus !=null and params.cateStatus != '' ">
            AND t.cate_status = #{params.cateStatus}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        and t.is_deleted=0
--         and t.cate_level!=0

    </select>
    <select id="getByIds" resultType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintClassificationPO">
        SELECT
        id,
        cate_name
        FROM
        tt_complaint_classification
        WHERE
        id IN
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
