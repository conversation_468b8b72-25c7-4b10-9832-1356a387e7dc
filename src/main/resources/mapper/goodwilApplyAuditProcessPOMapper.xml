<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwilApplyAuditProcessMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwilApplyAuditProcessPO">
            <id column="id" property="id"/>
            <result column="APP_ID" property="appId"/>
            <result column="OWNER_CODE" property="ownerCode"/>
            <result column="OWNER_PAR_CODE" property="ownerParCode"/>
            <result column="ORG_ID" property="orgId"/>
            <result column="goodwill_apply_id" property="goodwillApplyId"/>
            <result column="audit_object" property="auditObject"/>
            <result column="audit_role" property="auditRole"/>
            <result column="audit_type" property="auditType"/>
            <result column="audit_position" property="auditPosition"/>
            <result column="audit_sort" property="auditSort"/>
            <result column="audit_status" property="auditStatus"/>
            <result column="audit_date" property="auditDate"/>
            <result column="is_valid" property="isValid"/>
            <result column="is_deleted" property="isDeleted"/>
            <result column="created_at" property="createdAt"/>
            <result column="updated_at" property="updatedAt"/>
            <result column="created_by" property="createdBy"/>
            <result column="updated_by" property="updatedBy"/>
            <result column="record_version" property="recordVersion"/>
        </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, goodwill_apply_id, audit_object, audit_type,audit_role, audit_position, audit_sort, audit_status, audit_date, is_valid, is_deleted, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.bean.entity.GoodwilApplyAuditProcessPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwil_apply_audit_process t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.auditObject !=null and params.auditObject != '' ">
                AND t.audit_object = #{params.auditObject}
            </if>
                    <if test=" params.auditType !=null and params.auditType != '' ">
                AND t.audit_type = #{params.auditType}
            </if>
                    <if test=" params.auditPosition !=null and params.auditPosition != '' ">
                AND t.audit_position = #{params.auditPosition}
            </if>
                    <if test=" params.auditSort !=null and params.auditSort != '' ">
                AND t.audit_sort = #{params.auditSort}
            </if>
                    <if test=" params.auditStatus !=null and params.auditStatus != '' ">
                AND t.audit_status = #{params.auditStatus}
            </if>
                    <if test=" params.auditDate !=null and params.auditDate != '' ">
                AND t.audit_date = #{params.auditDate}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.bean.entity.GoodwilApplyAuditProcessPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwil_apply_audit_process t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.auditObject !=null and params.auditObject != '' ">
                AND t.audit_object = #{params.auditObject}
            </if>
                    <if test=" params.auditType !=null and params.auditType != '' ">
                AND t.audit_type = #{params.auditType}
            </if>
                    <if test=" params.auditPosition !=null and params.auditPosition != '' ">
                AND t.audit_position = #{params.auditPosition}
            </if>
                    <if test=" params.auditSort !=null and params.auditSort != '' ">
                AND t.audit_sort = #{params.auditSort}
            </if>
                    <if test=" params.auditStatus !=null and params.auditStatus != '' ">
                AND t.audit_status = #{params.auditStatus}
            </if>
                    <if test=" params.auditDate !=null and params.auditDate != '' ">
                AND t.audit_date = #{params.auditDate}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>
            
       <select id="selectByGoodwillApplyId" resultMap="BaseResultMap">
	        SELECT
	        APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, goodwill_apply_id, audit_object, audit_type,audit_role, audit_position, audit_sort, audit_status, audit_date, is_valid, is_deleted, created_at, updated_at,record_version,
	        (select count(1) from tt_goodwil_apply_audit_process a where a.goodwill_apply_id=#{params.goodwillApplyId} and audit_status is null ) counts,
	        (select count(1) from tt_goodwil_apply_audit_process a where a.goodwill_apply_id=#{params.goodwillApplyId} and audit_status is null and audit_object=0) applyCounts
	        FROM tt_goodwil_apply_audit_process t 
	        where t.goodwill_apply_id = #{params.goodwillApplyId} and t.audit_status is null order by t.audit_object,t.audit_position limit 1   
       </select> 
       
       <select id="queryReturnTo" resultType="hashMap">
       SELECT *  from (select
	        id,audit_role, audit_position,
	          case 	when audit_role='SHQYJL' then '售后区域经理'
			        when audit_role='SHDQJL' then '售后区域高级经理'
			        when audit_role='SHQYZJ' then '区域总监'
			        when audit_role='VP' then 'VP'
			        when audit_role='CEO' then 'CEO'
			        when audit_role='CCMQ' then 'CCMQ'
			        when audit_role='CCMGJJL' then 'CCM高级经理'
			        when audit_role='CCMZJ' then 'CCM总监'
			        when audit_role='OEM-CWJL' then '财务经理'
			        when audit_role='CFO' then 'CFO' end as roleName,
			  case when audit_role='SHQYJL' then 'Regional Manager'
			        when audit_role='SHDQJL' then 'Senior Regional Manager'
			        when audit_role='SHQYZJ' then 'Regional Director'
			        when audit_role='CCMGJJL' then 'Senior CCM Manager'
			        when audit_role='CCMZJ' then 'CCM Director'
			         end as roleNameEn
	        FROM tt_goodwil_apply_audit_process t
				where t.goodwill_apply_id = #{goodwillApplyId} and t.audit_status=82801001 and t.audit_object=#{auditObject} 
				and t.audit_role not in ('CCMQ','OEM-CWJL','CFO','CEO','VP')
					union all select 0,dealer_code,0,'经销商','Dealer' from tt_goodwill_apply_info where id=#{goodwillApplyId})tt
					where 1=1 order by tt.audit_position  
       </select>    
		<update id="updateProcess" >
			update tt_goodwil_apply_audit_process
			set audit_status=null
			where goodwill_apply_id = #{goodwillApplyId} and audit_object=#{auditObject} 
			 and id>=#{id}
		
		</update>
		<select id="queryAuditProcess" resultType="java.util.Map">
		select id, DATE_FORMAT(audit_date,'%Y-%m-%d') auditDate,  
		case when audit_role='SHQYJL' then '售后区域经理' when audit_role='SHDQJL' then '售后区域高级经理' 
		when audit_role='CCMQ' then 'CCMQ' 
		when audit_role='SHQYZJ' then '区域总监' when audit_role='VP' then 'VP'
		when audit_role='CEO' then 'CEO' when audit_role='CCMGJJL' then 'CCM高级经理' 
		when audit_role='CCMZJ' then 'CCM总监' 
		when audit_role='OEM-CWJL' then '财务经理' when audit_role='CFO' then 'CFO'  end as roleName
		from tt_goodwil_apply_audit_process 
		where  goodwill_apply_id = #{goodwillApplyId} and audit_object=#{auditObject} order by audit_position
		</select>
		<select id="selectNextAudit" resultMap="BaseResultMap">
		
	         SELECT
	        APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, goodwill_apply_id, audit_object, audit_type,audit_role, audit_position, audit_sort, audit_status, audit_date, is_valid, is_deleted, created_at, updated_at,record_version
	        FROM tt_goodwil_apply_audit_process t 
	        where t.goodwill_apply_id = #{params.goodwillApplyId} and t.audit_status is null order by t.audit_object,t.audit_position limit 1,1   
       </select> 
		<update id="updateStatusById" >
			update tt_goodwil_apply_audit_process
			set audit_status=82801001,audit_date=now()
			where goodwill_apply_id = #{goodwillApplyId} and audit_object=0
			 and audit_status is null
		
		</update>

    <!--  迁移历史数据：根据亲善申请单ID更新流程审批时间 add 20210721  -->
    <update id="updateGoodwillFlowAuditTime">
        UPDATE tt_goodwil_apply_audit_process p,
         (
            SELECT
                a.goodwill_apply_id,
                a.audit_object,
                a.audit_type,
                a.audit_role,
                max(a.audit_time) AS max_audit_time
            FROM
                tt_goodwill_audit_info a
            WHERE
                a.goodwill_apply_id = #{goodwillApplyId}
            AND a.audit_result = 82801001
            GROUP BY
                a.goodwill_apply_id,
                a.audit_object,
                a.audit_type,
                a.audit_role
        ) M
        SET p.audit_date = M.max_audit_time
        WHERE
            p.goodwill_apply_id = M.goodwill_apply_id
        AND p.audit_object = M.audit_object
        AND p.audit_type = M.audit_type
        AND p.audit_role = M.audit_role
        AND p.goodwill_apply_id = #{goodwillApplyId}
    </update>
</mapper>
