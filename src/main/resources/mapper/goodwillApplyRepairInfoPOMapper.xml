<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyRepairInfoMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyRepairInfoPO">
                  <id column="id" property="id"/>
                  <result column="APP_ID" property="appId"/>
                  <result column="OWNER_CODE" property="ownerCode"/>
                  <result column="OWNER_PAR_CODE" property="ownerParCode"/>
                  <result column="ORG_ID" property="orgId"/>
                  <result column="goodwill_apply_id" property="goodwillApplyId"/>
                  <result column="repair_no" property="repairNo"/>
                  <result column="into_factory_date" property="intoFactoryDate"/>
                  <result column="leave_factory_date" property="leaveFactoryDate"/>
                  <result column="mileage" property="mileage"/>
                  <result column="vehicle_fault_detail" property="vehicleFaultDetail"/>
                  <result column="check_result" property="checkResult"/>
                  <result column="remark" property="remark"/>
                  <result column="is_valid" property="isValid"/>
                  <result column="is_deleted" property="isDeleted"/>
                  <result column="created_at" property="createdAt"/>
                  <result column="updated_at" property="updatedAt"/>
                  <result column="created_by" property="createdBy"/>
                  <result column="updated_by" property="updatedBy"/>
                  <result column="record_version" property="recordVersion"/>
              </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, goodwill_apply_id, repair_no, into_factory_date, leave_factory_date, mileage, vehicle_fault_detail, check_result, remark, is_valid, is_deleted, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyRepairInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_repair_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.repairNo !=null and params.repairNo != '' ">
                AND t.repair_no = #{params.repairNo}
            </if>
                    <if test=" params.intoFactoryDate !=null and params.intoFactoryDate != '' ">
                AND t.into_factory_date = #{params.intoFactoryDate}
            </if>
                    <if test=" params.leaveFactoryDate !=null and params.leaveFactoryDate != '' ">
                AND t.leave_factory_date = #{params.leaveFactoryDate}
            </if>
                    <if test=" params.mileage !=null and params.mileage != '' ">
                AND t.mileage = #{params.mileage}
            </if>
                    <if test=" params.vehicleFaultDetail !=null and params.vehicleFaultDetail != '' ">
                AND t.vehicle_fault_detail = #{params.vehicleFaultDetail}
            </if>
                    <if test=" params.checkResult !=null and params.checkResult != '' ">
                AND t.check_result = #{params.checkResult}
            </if>
                    <if test=" params.remark !=null and params.remark != '' ">
                AND t.remark = #{params.remark}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyRepairInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_repair_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.repairNo !=null and params.repairNo != '' ">
                AND t.repair_no = #{params.repairNo}
            </if>
                    <if test=" params.intoFactoryDate !=null and params.intoFactoryDate != '' ">
                AND t.into_factory_date = #{params.intoFactoryDate}
            </if>
                    <if test=" params.leaveFactoryDate !=null and params.leaveFactoryDate != '' ">
                AND t.leave_factory_date = #{params.leaveFactoryDate}
            </if>
                    <if test=" params.mileage !=null and params.mileage != '' ">
                AND t.mileage = #{params.mileage}
            </if>
                    <if test=" params.vehicleFaultDetail !=null and params.vehicleFaultDetail != '' ">
                AND t.vehicle_fault_detail = #{params.vehicleFaultDetail}
            </if>
                    <if test=" params.checkResult !=null and params.checkResult != '' ">
                AND t.check_result = #{params.checkResult}
            </if>
                    <if test=" params.remark !=null and params.remark != '' ">
                AND t.remark = #{params.remark}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
     </select>
<!-- 集合查询& ,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="getSupportApplyRepairInfoById" resultMap="BaseResultMap" parameterType="long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_repair_info t
        WHERE 1=1 AND t.goodwill_apply_id = #{goodwillApplyId}
     </select>
</mapper>
