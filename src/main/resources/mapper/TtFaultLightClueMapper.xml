<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.faultLight.TtFaultLightClueMapper">

    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightCluePO">
        <result column="id" property="id"/>
        <result column="icm_id" property="icmId"/>
        <result column="vin" property="vin"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="dealer_name" property="dealerName"/>
        <result column="fault_id" property="faultId"/>
        <result column="alarm_time" property="alarmTime"/>
        <result column="region_id" property="regionId"/>
        <result column="region_name" property="regionName"/>
        <result column="cell_id" property="cellId"/>
        <result column="cell_name" property="cellName"/>
        <result column="city_id" property="cityId"/>
        <result column="city_name" property="cityName"/>
        <result column="group_company_short_name" property="groupCompanyShortName"/>
        <result column="clue_status" property="clueStatus"/>
        <result column="follow_status" property="followStatus"/>
        <result column="clue_gen_time" property="clueGenTime"/>
        <result column="clue_dis_time" property="clueDisTime"/>
        <result column="ro_no" property="roNo"/>
        <result column="ro_start_time" property="roStartTime"/>
        <result column="ro_end_time" property="roEndTime"/>
        <result column="repair_com_time" property="repairComTime"/>
        <result column="clue_clo_time" property="clueCloTime"/>
        <result column="clue_com_time" property="clueComTime"/>
        <result column="ro_type" property="roType"/>
        <result column="ro_amount" property="roAmount"/>
        <result column="miss_parts" property="missParts"/>
        <result column="no_repair" property="noRepair"/>
        <result column="lights_up" property="lightsUp"/>
        <result column="whe_res" property="wheRes"/>
        <result column="invite_overtime" property="inviteOvertime"/>
        <result column="forecast_time" property="forecastTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="remarks" property="remarks"/>
        <result column="created_at" property="createdAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="create_sqlby" property="createSqlby"/>
        <result column="update_sqlby" property="updateSqlby"/>
        <result column="fault_city_name" property="faultCityName"/>
        <result column="warning_name" property="warningName"/>
    </resultMap>

    <sql id="Base_Column_List">
                id,
                icm_id,
                vin,
                dealer_code,
                dealer_name,
                fault_id,
                alarm_time,
                region_id,
                region_name,
                cell_id,
                cell_name,
                city_id,
                city_name,
                group_company_short_name,
                clue_status,
                follow_status,
                clue_gen_time,
                clue_dis_time,
                ro_no,
                ro_start_time,
                ro_end_time,
                repair_com_time,
                clue_clo_time,
                clue_com_time,
                ro_type,
                ro_amount,
                miss_parts,
                no_repair,
                lights_up,
                whe_res,
                invite_overtime,
                forecast_time,
                is_deleted,
                remarks,
                created_at,
                created_by,
                updated_at,
                updated_by,
                create_sqlby,
                update_sqlby
    </sql>

    <update id="updateFaultLightClueById"
            parameterType="com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightCluePO">
        UPDATE
        tt_fault_light_clue tt
        <set>
            <if test="clueStatus!=null">
                clue_status = #{clueStatus},
            </if>
            <if test="followStatus!=null">
                follow_status = #{followStatus},
            </if>
            <if test="inviteTime!=null">
                invite_time=#{inviteTime},
            </if>
            <if test="roNo!=null">
                ro_no=#{roNo},
            </if>
            <if test="roStartTime!=null">
                ro_start_time=#{roStartTime},
            </if>
            <if test="roEndTime!=null">
                ro_end_time=#{roEndTime},
            </if>
            <if test="repairComTime!=null">
                repair_com_time=#{repairComTime},
            </if>
            <if test="clueCloTime!=null">
                clue_clo_time=#{clueCloTime},
            </if>
            <if test="clueComTime!=null">
                clue_com_time=#{clueComTime},
            </if>
            <if test="roType!=null">
                ro_type=#{roType},
            </if>
            <if test="roAmount!=null">
                ro_amount=#{roAmount},
            </if>
            <if test="missParts!=null">
                miss_parts=#{missParts},
            </if>
            <if test="noRepair!=null">
                no_repair=#{noRepair},
            </if>
            <if test="lightsUp!=null">
                lights_up=#{lightsUp},
            </if>
            <if test="wheRes!=null">
                whe_res=#{wheRes},
            </if>
            <if test="inviteOvertime!=null">
                invite_overtime=#{inviteOvertime},
            </if>
            <if test="forecastTime!=null">
                forecast_time=#{forecastTime},
            </if>
            <if test="highlightFlag!=null">
                highlight_flag=#{highlightFlag},
            </if>
        </set>
        <where>
            id = #{id}
            <if test="afClueStatus!=null">
                and clue_status = #{afClueStatus}
            </if>
        </where>
    </update>

    <update id="updateInviteOvertime" parameterType="list">
        UPDATE
        tt_fault_light_clue tt
        SET
        highlight_flag = 2
        WHERE tt.id IN
        <foreach close=")" collection="listItem" item="listItem" open="(" separator=",">
            #{listItem}
        </foreach>
    </update>
    <update id="cancelHighlights">
        UPDATE
        tt_fault_light_clue tt
        SET
        highlight_flag = 1
        WHERE tt.id IN
        <foreach close=")" collection="ids" item="ids" open="(" separator=",">
            #{ids}
        </foreach>
    </update>

    <select id="selectCountByIcmId" resultMap="BaseResultMap">
        select
            t1.id,t1.icm_id,t1.vin,t1.dealer_code,t1.dealer_name,t1.fault_id,t1.alarm_time,
            t1.region_id,t1.region_name,t1.cell_id,t1.cell_name,t1.city_id,t1.city_name,
            t1.group_company_short_name,t1.clue_status,t1.follow_status,t1.clue_gen_time,
            t1.clue_dis_time,t1.ro_no,t1.ro_start_time,t1.ro_end_time,t1.repair_com_time,
            t1.clue_clo_time,t1.clue_com_time,t1.ro_type,t1.ro_amount,t1.miss_parts,
            t1.no_repair,t1.lights_up,t1.whe_res,t1.invite_overtime,t1.forecast_time,
            t1.is_deleted,t1.remarks,t1.created_at,t1.created_by,t1.updated_at,
            t1.updated_by,t1.create_sqlby,t1.update_sqlby,t1.fault_city_name,t2.warning_name
        from tt_fault_light_clue t1
        left join tt_fault_light_dispose t2 on t1.fault_id  = t2.id
        where t1.icm_id = #{icmId}
    </select>

    <select id="queryClueInfoList"
            resultType="com.yonyou.dmscus.customer.entity.dto.faultLight.ClueInfoQueryRespDTO">
        SELECT
        t1.id as id,
        t1.vin as vin,
        t1.alarm_time as alarmTime,
        t1.city_name as cityName,
        t1.dealer_code as dealerCode,
        t1.cell_name as cellName,
        t1.region_name as regionName,
        t1.group_company_short_name as groupCompanyShortName,
        t1.clue_status as clueStatus,
        t1.follow_status as followStatus,
        t1.clue_gen_time as clueGenTime,
        t1.clue_dis_time as clueDisTime,
        t1.ro_no as roNo,
        t1.ro_start_time as roStartTime,
        t1.ro_end_time as roEndTime,
        t1.repair_com_time as repairComTime,
        t1.clue_clo_time as clueCloTime,
        t1.clue_com_time as clueComTime,
        t1.ro_type as roType,
        t1.ro_amount as roAmount,
        t1.miss_parts as missParts,
        t1.no_repair as noRepair,
        t1.lights_up as lightsUp,
        t1.whe_res as wheRes,
        t1.invite_time as inviteTime,
        t1.remarks as remarks,
        t1.source_type as sourceType,
        t1.trouble_code as troubleCode,
        t1.source_clue_id as sourceClueId,
        t2.warning_en_name as warningEnName,
        t2.warning_name as warningName,
        t2.fault_grade as faultGrade,
        t3.contact_time as contactTime,
        t3.contacts_name as contactsName,
        t3.contact_res_time as contactResTime,
        t3.contact_overtime as contactOvertime,
        t3.contact_result as contactResult,
        t3.dlr as dlr,
        t3.is_dlr as isDlr,
        t3.invite_name as inviteName,
        t3.invite_res_time as inviteResTime,
        t3.invite_result as inviteResult,
        t3.forecast_time as forecastTime,
        t3.into_time as intoTime,
        t3.into_on_time as intoOnTime,
        t3.no_into as noInto,
        t3.invite_overtime as inviteOvertime,
        t3.self_into as selfInto,
        t3.comments as comments,
        t3.failure_reason as failureReason,
        t3.re_invite_time as reInviteTime,
        t4.fault_category as faultCategory
        FROM tt_fault_light_clue t1
        LEFT JOIN
        tt_fault_light_dispose t2 ON t1.fault_id = t2.id
        LEFT JOIN
        tt_fault_light_invitation t3 ON t3.clue_id = t1.id
        LEFT JOIN
        tm_dtc_clues_category t4 ON t1.fault_id = t4.id
        WHERE 1=1
        <if test=" params.dealerCode !=null">
            AND t1.dealer_code IN
            <foreach close=")" collection="params.dealerCode" item="dealerCode" open="(" separator=",">
                #{dealerCode}
            </foreach>
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t1.vin like CONCAT(#{params.vin},'%')
        </if>
        <if test=" params.warningName !=null and params.warningName != '' ">
            AND t2.warning_name like CONCAT(#{params.warningName},'%')
        </if>
        <if test=" params.groupCompanyShortName !=null and params.groupCompanyShortName != '' ">
            AND t1.group_company_short_name like CONCAT(#{params.groupCompanyShortName},'%')
        </if>
        <if test=" params.followStatus !=null">
            AND t1.follow_status IN
            <foreach close=")" collection="params.followStatus" item="followStatus" open="(" separator=",">
                #{followStatus}
            </foreach>
        </if>
        <if test=" params.regionId !=null ">
            AND t1.region_id = #{params.regionId}
        </if>
        <if test=" params.cellId !=null">
            AND t1.cell_id = #{params.cellId}
        </if>
        <if test=" params.cityId !=null ">
            AND t1.city_id = #{params.cityId}
        </if>
        <if test=" params.cityName !=null and params.cityName != '' ">
            AND t1.city_name = #{params.cityName}
        </if>
        <if test=" params.cellName !=null and params.cellName != '' ">
            AND t1.cell_name = #{params.cellName}
        </if>
        <if test=" params.regionName !=null and params.regionName != '' ">
            AND t1.region_name = #{params.regionName}
        </if>
        <if test=" params.clueGenStartTime !=null and params.clueGenStartTime != '' ">
            AND t1.clue_gen_time &gt;= #{params.clueGenStartTime}
        </if>
        <if test=" params.clueGenEndTime !=null and params.clueGenEndTime != '' ">
            AND t1.clue_gen_time &lt;= #{params.clueGenEndTime}
        </if>
        <if test=" params.clueDisStartTime !=null and params.clueDisStartTime != '' ">
            AND t1.clue_dis_time &gt;= #{params.clueDisStartTime}
        </if>
        <if test=" params.clueDisEndTime !=null and params.clueDisEndTime != '' ">
            AND t1.clue_dis_time &lt;= #{params.clueDisEndTime}
        </if>
        <if test=" params.wheRes !=null">
            AND t1.whe_res = #{params.wheRes}
        </if>
        <if test=" params.clueStatus !=null">
            AND t1.clue_status IN
            <foreach close=")" collection="params.clueStatus" item="clueStatus" open="(" separator=",">
                #{clueStatus}
            </foreach>
        </if>
        AND t1.is_deleted = 0
        ORDER BY t1.clue_dis_time DESC
    </select>

    <select id="queryCityDropdownDox" resultMap="BaseResultMap">
        select region_id, region_name, cell_id, cell_name, city_id, city_name
        from tt_fault_light_clue
        group by region_id, cell_id, city_id
    </select>

    <update id="updateClueInfo">
        UPDATE
            tt_fault_light_clue t
        SET t.clue_status   = #{params.clueStatus},
            t.follow_status = #{params.followStatus},
            t.whe_res       = #{params.wheRes},
            t.clue_com_time = #{params.clueComTime},
            t.remarks = #{params.remarks}
        WHERE t.id = #{params.id}
          AND t.clue_status = #{clueState}
    </update>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="params" item="params" index="index" open="" close="" separator=";">
            UPDATE
            tt_fault_light_clue t
            <set>
                <if test=" params.roNo !=null">
                    t.ro_no = #{params.roNo},
                </if>
                <if test=" params.roType !=null">
                    t.ro_type = #{params.roType},
                </if>
                <if test=" params.roStartTime !=null">
                    t.ro_start_time = #{params.roStartTime},
                </if>
                <if test=" params.clueStatus !=null">
                    t.clue_status = #{params.clueStatus},
                </if>
                <if test=" params.followStatus !=null">
                    t.follow_status = #{params.followStatus},
                </if>
                <if test=" params.lightsUp !=null">
                    t.lights_up = #{params.lightsUp},
                </if>
                <if test=" params.wheRes !=null">
                    t.whe_res = #{params.wheRes},
                </if>
                <if test=" params.clueComTime !=null">
                    t.clue_com_time = #{params.clueComTime},
                </if>
                <if test=" params.clueCloTime !=null">
                    t.clue_clo_time = #{params.clueCloTime},
                </if>
            </set>
            WHERE
            t.id = #{params.id}
        </foreach>
    </update>
    <update id="updateHighlightFlagByClueDisTime">
        UPDATE tt_fault_light_clue tt
        SET highlight_flag=2
        where dealer_code=#{dealerCode}
        AND is_deleted=0
        AND invite_Time is NULL
        AND  clue_status = 10541002
        AND highlight_flag = 0
        AND clue_dis_time&lt;=DATE_SUB(NOW(),INTERVAL 120 MINUTE)
    </update>

    <update id="batchUpdates" parameterType="java.util.List">
        <foreach collection="params" item="params" index="index" open="" close="" separator=";">
            UPDATE
            tt_fault_light_clue t
            <set>
                <if test=" params.clueStatus !=null">
                    t.clue_status = #{params.clueStatus},
                </if>
                <if test=" params.followStatus !=null">
                    t.follow_status = #{params.followStatus},
                </if>
                <if test=" params.lightsUp !=null">
                    t.lights_up = #{params.lightsUp},
                </if>
                <if test=" params.wheRes !=null">
                    t.whe_res = #{params.wheRes},
                </if>
                <if test=" params.clueComTime !=null">
                    t.clue_com_time = #{params.clueComTime},
                </if>
                <if test=" params.clueCloTime !=null">
                    t.clue_clo_time = #{params.clueCloTime},
                </if>
                <if test=" params.repairComTime !=null">
                    t.repair_com_time = #{params.repairComTime},
                </if>
                <if test=" params.troubleCode !=null">
                    t.trouble_code = #{params.troubleCode}
                </if>
                <if test=" params.roEndTime !=null">
                    t.ro_end_time = #{params.roEndTime},
                </if>
                <if test=" params.roAmount !=null">
                    t.ro_amount = #{params.roAmount},
                </if>
            </set>
            WHERE
            t.id = #{params.id}
        </foreach>
    </update>

    <select id="selectFaultLightFollow"
            resultType="com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightFollowDTO">
        select t2.cus_name,
               t2.cus_phone,
               t2.gender,
               t2.age,
               t1.vin,
               t1.clue_status,
               t1.follow_status,
               t1.invite_time,
               t1.daily_mile,
               t2.forecast_time,
               t1.ro_no,
               t1.clue_gen_time AS "warningTime",
               t1.fault_cn AS "waringCnDescription",
               t1.fault_city_name AS "warningCity",
               t4.daily_average_mileage,
               t2.comments,
               t2.booking_order_no,
               t1.dealer_code,
               t1.icm_id
        from tt_fault_light_clue t1
                 left join tt_fault_light_invitation t2 on t1.id = t2.clue_id
                 left join tm_vehicle t4 on t1.vin = t4.vin
        where t1.id = #{id}
    </select>
    <select id="selectClueInfoList"
            resultType="com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightCluePO">
        SELECT
            t.*
        FROM
        tt_fault_light_clue t
        LEFT JOIN `tt_fault_light_invitation` AS t2 ON t.id = t2.clue_id
        WHERE
        t.ro_no IS NULL
        AND
        t.clue_status in (10541003,10541004)
        AND
        t2.booking_order_no IS NULL
        AND
        t.is_deleted = 0

    </select>

    <select id="selectClueArriveList"
            resultType="com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightCluePO">
        SELECT t.*
        FROM
            dms_manage.tt_fault_light_clue t
            LEFT JOIN `tt_fault_light_invitation` AS t2 ON t.id = t2.clue_id
        WHERE
            t.clue_status = 10541004
          AND
            t2.booking_order_no IS NULL
          AND
            t.ro_no IS NOT NULL
          AND
            t.is_deleted = 0
    </select>

    <select id="selectClueInfoListV4" resultType="com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightClueDTO">
        SELECT
            t1.*, t2.booking_order_no
        FROM
            `tt_fault_light_clue` AS t1
            LEFT JOIN `tt_fault_light_invitation` AS t2 ON t1.id = t2.clue_id
        WHERE
            t1.clue_status = #{clueStatus}
            AND
            t2.booking_order_no IS NOT NULL
            AND
            t1.is_deleted = 0
    </select>

    <select id="selectBookingClueInfoListWithoutStatus" resultType="com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightClueDTO">
        SELECT
            t1.*, t2.booking_order_no
        FROM
            `tt_fault_light_clue` AS t1
            LEFT JOIN `tt_fault_light_invitation` AS t2 ON t1.id = t2.clue_id
        <where>
            <if test="finishClueStatusList != null and finishClueStatusList.size() > 0">
                t1.clue_status NOT IN
                <foreach collection="finishClueStatusList" item="clueStatus" open="(" close=")" separator=",">
                    #{clueStatus}
                </foreach>
            </if>
            AND t2.booking_order_no IS NOT NULL
            AND t1.is_deleted = 0
        </where>
    </select>

    <select id="queryDealerList"
            resultType="com.yonyou.dmscus.customer.entity.dto.faultLight.TtFaultLightClueDTO">
        SELECT
        t.dealer_code AS dealerCode,
        t.dealer_name AS dealerName
        FROM
        tt_fault_light_clue t
        WHERE 1=1
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t1.dealer_code like CONCAT(#{params.dealerCode},'%')
        </if>
        <if test=" params.dealerName !=null and params.dealerName != '' ">
            AND t1.dealer_name like CONCAT(#{params.dealerName},'%')
        </if>
        AND t.dealer_code IS NOT NULL
        AND t.dealer_name IS NOT NULL
        GROUP BY t.dealer_code
    </select>

    <select id="selectPoByClueStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_fault_light_clue
        WHERE
            clue_status = #{clueStatus}
        AND
            is_deleted = 0
    </select>
    <select id="queryPendingAppointment" resultType="java.lang.Long">
        SELECT id
        FROM `dms_manage`.`tt_fault_light_clue`
        WHERE is_deleted=0
        AND clue_status = 10541002
        AND follow_status=10551002
        AND forecast_time is NULL
        AND clue_dis_time &lt;=DATE_SUB(NOW(),INTERVAL 120 SECOND)
    </select>
    <select id="querySecondAppointment" resultType="java.lang.Long">
        SELECT t1.id
        FROM `dms_manage`.`tt_fault_light_clue` t1
        LEFT JOIN `dms_manage`.`tt_fault_light_invitation` t2 ON t1.id =t2.clue_id
        LEFT JOIN `dms_manage`.`tt_cdp_tag_task` t3 ON t3.biz_no = t1.id
        AND t3.since_type =2
        and t3.created_at &gt;=DATE_FORMAT(#{createDate},'%Y-%m-%d 00:00:00')
        AND t3.created_at &lt;=DATE_FORMAT(#{endDate},'%Y-%m-%d 23:59:59')
        WHERE t2.re_invite_time =DATE_FORMAT(NOW(),'%Y-%m-%d 00:00:00')
          AND t1.clue_status = 10541002
          AND t1.follow_status=10551022
          AND t1.is_deleted = 0
          AND t3.id IS NULL
    </select>
    <select id="queryIdByCallId" resultType="java.lang.Long">
        select t1.id
        from tt_fault_light_clue t1
                 left join tt_fault_call_register t2 on t1.id=t2.invite_id
        where  t2.call_id=#{callId};
    </select>
    <select id="queryTotal" resultType="java.lang.Integer">
        SELECT count(1)
        FROM `dms_manage`.`tt_fault_light_clue` t1
        LEFT JOIN `dms_manage`.`tt_fault_light_invitation` t2 ON t1.id =t2.clue_id
        LEFT JOIN `dms_manage`.`tt_cdp_tag_task` t3 ON t3.biz_no = t1.id
        AND t3.since_type =2
        and t3.created_at &gt;=DATE_FORMAT(#{createDate},'%Y-%m-%d 00:00:00')
        AND t3.created_at &lt;=DATE_FORMAT(#{endDate},'%Y-%m-%d 23:59:59')
        WHERE t2.re_invite_time =DATE_FORMAT(NOW(),'%Y-%m-%d 00:00:00')
        AND t1.clue_status = 10541002
        AND t1.follow_status=10551022
        AND t1.is_deleted = 0
        AND t3.id IS NULL
    </select>

    <select id="selectPoByClueStatusPage" resultType="com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightOnlineOfflineDTO">
        SELECT
            f.id,
            f.icm_id,
            f.vin,
            f.dealer_code,
            f.dealer_name,
            f.fault_id,
            f.alarm_time,
            f.region_id,
            f.region_name,
            f.cell_id,
            f.cell_name,
            f.city_id,
            f.city_name,
            f.group_company_short_name,
            f.clue_status,
            f.follow_status,
            f.clue_gen_time,
            f.clue_dis_time,
            f.ro_no,
            f.ro_start_time,
            f.ro_end_time,
            f.repair_com_time,
            f.clue_clo_time,
            f.clue_com_time,
            f.ro_type,
            f.ro_amount,
            f.miss_parts,
            f.no_repair,
            f.lights_up,
            f.whe_res,
            f.invite_overtime,
            f.forecast_time,
            f.is_deleted,
            f.remarks,
            f.created_at,
            f.created_by,
            f.updated_at,
            f.updated_by,
            f.create_sqlby,
            f.update_sqlby,
        r.veh_vin,
        r.send_time
        FROM tt_fault_light_clue f
        LEFT JOIN tt_rvdc_veh_online_offline_change_record r
        ON f.vin = r.veh_vin
        AND r.stat_type = 13
        AND r.send_time &gt;= f.repair_com_time
        AND r.send_time &lt;= DATE_ADD(f.repair_com_time, INTERVAL 30 DAY)
        WHERE
        f.clue_status = #{clueStatus}
        AND f.source_type = #{sourceType}
        AND
        f.is_deleted = 0
        ORDER BY r.send_time
    </select>

    <select id="selectPoByClueStatusDtcPage" resultType="com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightOnlineOfflineDTO">
        SELECT
            f.id,
            f.icm_id,
            f.vin,
            f.dealer_code,
            f.dealer_name,
            f.fault_id,
            f.alarm_time,
            f.region_id,
            f.region_name,
            f.cell_id,
            f.cell_name,
            f.city_id,
            f.city_name,
            f.group_company_short_name,
            f.clue_status,
            f.follow_status,
            f.clue_gen_time,
            f.clue_dis_time,
            f.ro_no,
            f.ro_start_time,
            f.ro_end_time,
            f.repair_com_time,
            f.clue_clo_time,
            f.clue_com_time,
            f.ro_type,
            f.ro_amount,
            f.miss_parts,
            f.no_repair,
            f.lights_up,
            f.whe_res,
            f.invite_overtime,
            f.forecast_time,
            f.is_deleted,
            f.remarks,
            f.created_at,
            f.created_by,
            f.updated_at,
            f.updated_by,
            f.create_sqlby,
            f.update_sqlby,
        r.ct_upload AS ctUpload
        FROM tt_fault_light_clue f
        LEFT JOIN tt_clues_diagnostic_info_relation r
        ON f.source_clue_id = r.leads_preprocessing_id
        AND r.ct_upload &gt;= f.repair_com_time
        AND r.ct_upload &lt;= DATE_ADD(f.repair_com_time, INTERVAL 30 DAY)
        WHERE
        f.clue_status = #{clueStatus}
        AND f.source_type = #{sourceType}
        AND
        f.is_deleted = 0
        ORDER BY r.send_time
    </select>

    <select id="selectPoByClueStatusCount" resultType="integer" >
        SELECT
        count(1)
        FROM tt_fault_light_clue f
        LEFT JOIN tt_rvdc_veh_online_offline_change_record r
            ON f.vin = r.veh_vin
            AND r.stat_type = 13
            AND r.send_time &gt;= f.repair_com_time
            AND r.send_time &lt;= DATE_ADD(f.repair_com_time, INTERVAL 30 DAY)
        WHERE
            f.clue_status = #{clueStatus}
            AND f.source_type = #{sourceType}
          AND
            f.is_deleted = 0
        ORDER BY r.send_time
    </select>

    <select id="selectPoByClueStatusDtcCount" resultType="integer" >
        SELECT
        count(1)
        FROM tt_fault_light_clue f
        LEFT JOIN tt_clues_diagnostic_info_relation r
            ON f.source_clue_id = r.leads_preprocessing_id
            AND r.ct_upload &gt;= f.repair_com_time
            AND r.ct_upload &lt;= DATE_ADD(f.repair_com_time, INTERVAL 30 DAY)
        WHERE
            f.clue_status = #{clueStatus}
          AND
            f.is_deleted = 0
        ORDER BY r.ct_upload
    </select>


    <select id="getFaultLightClueChange" resultType="com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightCluePO">
        SELECT id,dealer_code,vin,ro_no,icm_id FROM dms_manage.tt_fault_light_clue
        WHERE clue_status = 10541004 AND follow_status = 10551005 AND ro_no IS NOT NULL
    </select>

    <select id="selectClueStatus" resultType="java.lang.Integer">
        SELECT count(1) FROM dms_manage.tt_fault_light_clue
        WHERE clue_status IN (10541002,10541003,10541004)
        and dealer_code = #{dealerCode}
        and vin = #{vin}
    </select>

    <update id="batchUpdateTroubleCodeBySourceClueId" parameterType="java.util.List">
        <foreach collection="params" item="param" index="index" open="" close="" separator=";">
            UPDATE tt_fault_light_clue t set trouble_code = #{param.troubleCode} WHERE t.id = #{param.id}
        </foreach>
    </update>

    <select id="queryRecordCountByIds" resultType="com.yonyou.dmscus.customer.entity.dto.faultLight.SourceClueIdRelationDto">
        select id cluePrimaryId, source_clue_id sourceClueId from tt_fault_light_clue where source_clue_id in
        <foreach collection="params" item="sourceClueId" open="(" separator="," close=")">
            #{sourceClueId}
        </foreach>
        and source_type = 'DIM'
    </select>
    <select id="getFaultLightStatusRenovate"  resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dms_manage.tt_fault_light_clue
        WHERE ro_no IS NOT NULL
        AND `repair_com_time` IS NOT NULL
        AND (ro_end_time IS NULL
        OR ro_amount IS NULL)
    </select>

    <update id="batchUpdateRoAmount" parameterType="java.util.List">
        <foreach collection="params" item="params" index="index" open="" close="" separator=";">
            UPDATE
            tt_fault_light_clue t
            <set>
                <if test=" params.roEndTime !=null">
                    t.ro_end_time = #{params.roEndTime},
                </if>
                <if test=" params.roAmount !=null">
                    t.ro_amount = #{params.roAmount},
                </if>
            </set>
            WHERE
            t.id = #{params.id}
        </foreach>
    </update>

    <update id="updateForecastTime" parameterType="java.util.List">
        <foreach collection="params" item="param" index="index" open="" close="" separator=";">
            UPDATE
            tt_fault_light_clue t
            LEFT JOIN
            tt_fault_light_invitation t3 ON t3.clue_id = t.id
            set t.forecast_time = #{param.forecastTime},
            t3.forecast_time = #{param.forecastTime}
            WHERE
            t.icm_id = #{param.icmId}
        </foreach>
    </update>

    <select id="getUpdateForecastTime" resultType="com.yonyou.dmscus.customer.dto.BookingOrderInfoVO">
        SELECT
        t1.icm_id,
        t1.dealer_code AS ownerCode,
        t3.booking_order_no
        FROM dms_manage.tt_fault_light_clue t1
        LEFT JOIN
        tt_fault_light_invitation t3 ON t3.clue_id = t1.id
        WHERE t1.follow_status = 10551025
        AND t3.booking_order_no is not null
        and t1.is_deleted = 0
    </select>


    <select id="selectClueInfoListV5" resultType="com.yonyou.dmscus.customer.dto.BookingOrderInfoVO">
        SELECT
        t1.id,
        t1.dealer_code AS ownerCode,
        t1.ro_no,
        t1.icm_id ,
        t2.booking_order_no
        FROM
        `tt_fault_light_clue` AS t1
        LEFT JOIN `tt_fault_light_invitation` AS t2 ON t1.id = t2.clue_id
        WHERE
        t1.ro_no IS NOT NULL
        AND
        t1.clue_status = #{clueStatus}
        AND
        t2.booking_order_no IS NOT NULL
        AND
        t1.is_deleted = 0
    </select>

</mapper>
