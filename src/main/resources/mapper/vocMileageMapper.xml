<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.yonyou.dmscus.customer.dao.vocmileage.VocMileageMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
			   type="com.yonyou.dmscus.customer.entity.dto.vocmileage.VocMileageDTO">
		<id column="id" property="id" />
		<result column="app_id" property="appId" />
		<result column="owner_code" property="ownerCode" />
		<result column="owner_par_code" property="ownerParCode" />
		<result column="org_id" property="orgId" />
		<result column="vin" property="vin" />
		<result column="mileage_m" property="mileageM" />
		<result column="mileage_km" property="mileageKm" />
		<result column="get_time" property="getTime" />
		<result column="created_code" property="createdCode" />
		<result column="data_sources" property="dataSources" />
		<result column="is_deleted" property="isDeleted" />
		<result column="is_valid" property="isValid" />
		<result column="created_at" property="createdAt" />
		<result column="updated_at" property="updatedAt" />
		<result column="created_by" property="createdBy" />
		<result column="updated_by" property="updatedBy" />
		<result column="record_version" property="recordVersion" />
	</resultMap>


	<!-- 通用查询映射结果 -->
	<resultMap id="PoResultMap"
			   type="com.yonyou.dmscus.customer.entity.po.vocmileage.VocMileagePO">
		<id column="id" property="id" />
		<result column="app_id" property="appId" />
		<result column="owner_code" property="ownerCode" />
		<result column="owner_par_code" property="ownerParCode" />
		<result column="org_id" property="orgId" />
		<result column="vin" property="vin" />
		<result column="mileage_m" property="mileageM" />
		<result column="mileage_km" property="mileageKm" />
		<result column="get_time" property="getTime" />
		<result column="created_code" property="createdCode" />
		<result column="data_sources" property="dataSources" />
		<result column="is_deleted" property="isDeleted" />
		<result column="is_valid" property="isValid" />
		<result column="created_at" property="createdAt" />
		<result column="updated_at" property="updatedAt" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		app_id, owner_code, owner_par_code, org_id, id, vin, mileage_m, mileage_km,
		get_time, created_code, data_sources, is_deleted, is_valid,
		created_at, updated_at
	</sql>


	<!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
	<!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
	<select id="queryVocMileagePageInfo" resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List" />
		FROM tt_voc_mileage t
		WHERE 1=1
		<if test=" params.appId !=null and params.appId != '' ">
			AND t.app_id = #{params.appId}
		</if>
		<if test=" params.ownerCode !=null and params.ownerCode != '' ">
			AND t.owner_code = #{params.ownerCode}
		</if>
		<if
			test=" params.ownerParCode !=null and params.ownerParCode != '' ">
			AND t.owner_par_code = #{params.ownerParCode}
		</if>
		<if test=" params.orgId !=null and params.orgId != '' ">
			AND t.org_id = #{params.orgId}
		</if>
		<if test=" params.id !=null and params.id != '' ">
			AND t.id = #{params.id}
		</if>
		<if test=" params.vin !=null and params.vin != '' ">
			AND t.vin like "%" #{params.vin} "%"
		</if>
		<if test=" params.mileageM !=null and params.mileageM != '' ">
			AND t.mileage_m = #{params.mileageM}
		</if>
		<if test=" params.mileageKm !=null and params.mileageKm != '' ">
			AND t.mileage_km = #{params.mileageKm}
		</if>
		<if test=" params.getTime !=null and params.getTime != '' ">
			AND t.get_time = #{params.getTime}
		</if>
		<if test=" params.createdCode !=null and params.createdCode != '' ">
			AND t.created_code like "%" #{params.createdCode} "%"
		</if>
		<if test=" params.dataSources !=null and params.dataSources != '' ">
			AND t.data_sources = #{params.dataSources}
		</if>
		<if test=" params.isDeleted !=null and params.isDeleted != '' ">
			AND t.is_deleted = #{params.isDeleted}
		</if>
		<if test=" params.isValid !=null and params.isValid != '' ">
			AND t.is_valid = #{params.isValid}
		</if>
		<if test=" params.createdAt !=null and params.createdAt != '' ">
			AND t.created_at = #{params.createdAt}
		</if>
		<if test=" params.updatedAt !=null and params.updatedAt != '' ">
			AND t.updated_at = #{params.updatedAt}
		</if>
		
		<if test=" params.getTimeBegin !=null and params.getTimeBegin != '' ">
			AND t.get_time  <![CDATA[>=]]>  #{params.getTimeBegin}
		</if>
		<if test=" params.getTimeEnd !=null and params.getTimeEnd != '' ">
			AND t.get_time  <![CDATA[<=]]>  #{params.getTimeEnd}
		</if>
		
		<if test=" params.createdAtBegin !=null and params.createdAtBegin != '' ">
			AND t.created_at  <![CDATA[>=]]>  #{params.createdAtBegin}
		</if>
		<if test=" params.createdAtEnd !=null and params.createdAtEnd != '' ">
			AND t.created_at  <![CDATA[<=]]>  #{params.createdAtEnd}
		</if>
		order by t.get_time desc
	</select>

	<!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
	<!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
	<select id="selectListBySql" resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List" />
		FROM tt_voc_mileage t
		WHERE 1=1
		<if test=" params.appId !=null and params.appId != '' ">
			AND t.app_id = #{params.appId}
		</if>
		<if test=" params.ownerCode !=null and params.ownerCode != '' ">
			AND t.owner_code = #{params.ownerCode}
		</if>
		<if
			test=" params.ownerParCode !=null and params.ownerParCode != '' ">
			AND t.owner_par_code = #{params.ownerParCode}
		</if>
		<if test=" params.orgId !=null and params.orgId != '' ">
			AND t.org_id = #{params.orgId}
		</if>
		<if test=" params.id !=null and params.id != '' ">
			AND t.id = #{params.id}
		</if>
		<if test=" params.vin !=null and params.vin != '' ">
			AND t.vin = #{params.vin}
		</if>
		<if test=" params.mileageM !=null and params.mileageM != '' ">
			AND t.mileage_m = #{params.mileageM}
		</if>
		<if test=" params.mileageKm !=null and params.mileageKm != '' ">
			AND t.mileage_km = #{params.mileageKm}
		</if>
		<if test=" params.getTime !=null and params.getTime != '' ">
			AND t.get_time = #{params.getTime}
		</if>
		<if test=" params.createdCode !=null and params.createdCode != '' ">
			AND t.created_code = #{params.createdCode}
		</if>
		<if test=" params.dataSources !=null and params.dataSources != '' ">
			AND t.data_sources = #{params.dataSources}
		</if>
		<if test=" params.isDeleted !=null and params.isDeleted != '' ">
			AND t.is_deleted = #{params.isDeleted}
		</if>
		<if test=" params.isValid !=null and params.isValid != '' ">
			AND t.is_valid = #{params.isValid}
		</if>
		<if test=" params.createdAt !=null and params.createdAt != '' ">
			AND t.created_at = #{params.createdAt}
		</if>
		<if test=" params.updatedAt !=null and params.updatedAt != '' ">
			AND t.updated_at = #{params.updatedAt}
		</if>
		
		<if test=" params.getTimeBegin !=null and params.getTimeBegin != '' ">
			AND t.get_time  <![CDATA[>=]]>  #{params.getTimeBegin}
		</if>
		<if test=" params.getTimeEnd !=null and params.getTimeEnd != '' ">
			AND t.get_time  <![CDATA[<=]]>  #{params.getTimeEnd}
		</if>
		
		<if test=" params.createdAtBegin !=null and params.createdAtBegin != '' ">
			AND t.created_at  <![CDATA[>=]]>  #{params.createdAtBegin}
		</if>
		<if test=" params.createdAtEnd !=null and params.createdAtEnd != '' ">
			AND t.created_at  <![CDATA[<=]]>  #{params.createdAtEnd}
		</if>
	</select>
	<!-- Excel批量导入的数据插入 -->
	<insert id="insertBatchData">
		INSERT INTO `tt_voc_mileage` (
			`app_id`,
			`owner_code`,
			`owner_par_code`,
			`org_id`,
			`vin`,
			`mileage_m`,
			`mileage_km`,
			`get_time`,
			`created_code`,
			`data_sources`,
			`created_by`,
			`created_at`,
			`updated_by`,
			`updated_at`
		)
		VALUES
		<foreach collection ="insertList" item="po" separator =",">
			(#{po.appId},#{po.ownerCode},#{po.ownerParCode},#{po.orgId},#{po.vin},#{po.mileageM},#{po.mileageKm},#{po.getTime},#{po.createdCode},#{po.dataSources},#{po.createdBy},#{po.createdAt},#{po.updatedBy},#{po.updatedAt})
		</foreach>
	</insert>

	<select id="getAllVocVeh" resultMap="PoResultMap">
		select DISTINCT vin
		 from tt_voc_mileage
	</select>

	<select id="getVocMileage" resultMap="PoResultMap">
		select  vin,mileage_km,get_time
		from tt_voc_mileage
		where
			 date_add(created_at, INTERVAL 1 DAY) &gt;=
		STR_TO_DATE(CONCAT(#{createDate},' 00:00:00'),'%Y-%m-%d %H:%i:%s')
		AND created_at &lt;= STR_TO_DATE(CONCAT(#{createDate}, ' 23:59:59'),'%Y-%m-%d %H:%i:%s')
		order by get_time asc
	</select>



	<select id="getVocMileageLast" resultMap="PoResultMap">
		select get_time,mileage_km
		from tt_voc_mileage
		  where vin=#{vin}
		order by get_time desc
		limit 0,1
	</select>

	<select id="getVocMileageByInterval" resultMap="PoResultMap">
		select get_time,mileage_km
		from tt_voc_mileage
		where vin=#{vin}  and date_add(get_time, INTERVAL #{interval} DAY)&lt; #{getTime}
			and mileage_km &lt;=#{mileageKm}
		order by get_time desc
		limit 0,1
	</select>

</mapper>
