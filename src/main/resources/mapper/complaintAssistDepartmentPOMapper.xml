<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.complaint.ComplaintAssistDepartmentMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistDepartmentPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <result column="app_id" property="appId"/>
                                                        <result column="owner_code" property="ownerCode"/>
                                                        <result column="owner_par_code" property="ownerParCode"/>
                                                        <result column="org_id" property="orgId"/>
                                                                                    <result column="complaint_info_id" property="complaintInfoId"/>
                                                        <result column="object" property="object"/>
                                                        <result column="follow_time" property="followTime"/>
                                                        <result column="follower" property="follower"/>
                                                        <result column="assist_department" property="assistDepartment"/>
                                                        <result column="assist_department_name" property="assistDepartmentName"/>
                                                        <result column="assist_dealer_code" property="assistDealerCode"/>
                                                        <result column="assist_dealer_name" property="assistDealerName"/>
                                                        <result column="hope_reply_time" property="hopeReplyTime"/>
                                                        <result column="assist_explain" property="assistExplain"/>
                                                        <result column="is_reply" property="isReply"/>
                                                        <result column="reply_time" property="replyTime"/>
                                                        <result column="is_finish" property="isFinish"/>
                                                        <result column="finish_time" property="finishTime"/>
                                                        <result column="is_read" property="isRead"/>
                                                        <result column="data_sources" property="dataSources"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="is_valid" property="isValid"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>
                <resultMap id="BaseResultMap1" type="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistPO">
                    <id column="id" property="id"/>
                    <result column="app_id" property="appId"/>
                    <result column="owner_code" property="ownerCode"/>
                    <result column="owner_par_code" property="ownerParCode"/>
                    <result column="org_id" property="orgId"/>
                    <result column="complaint_info_id" property="complaintInfoId"/>
                    <result column="object" property="object"/>
                    <result column="follow_time" property="followTime"/>
                    <result column="follower" property="follower"/>
                    <result column="follower_name" property="followerName"/>
                    <result column="assist_department" property="assistDepartment"/>
                    <result column="assist_department_name" property="assistDepartmentName"/>
                    <result column="assist_dealer_code" property="assistDealerCode"/>
                    <result column="assist_dealer_name" property="assistDealerName"/>
                    <result column="hope_reply_time" property="hopeReplyTime"/>
                    <result column="assist_explain" property="assistExplain"/>
                    <result column="is_reply" property="isReply"/>
                    <result column="reply_time" property="replyTime"/>
                    <result column="is_finish" property="isFinish"/>
                    <result column="finish_time" property="finishTime"/>
                    <result column="is_read" property="isRead"/>
                    <result column="data_sources" property="dataSources"/>
                    <result column="is_deleted" property="isDeleted"/>
                    <result column="is_valid" property="isValid"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
                    <result column="created_by" property="createdBy"/>
                    <result column="updated_by" property="updatedBy"/>
                    <result column="record_version" property="recordVersion"/>
                    <result column="allDealer" property="allDealer"/>

                </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, complaint_info_id, object, follow_time, follower, assist_department, assist_department_name, assist_dealer_code, assist_dealer_name, hope_reply_time, assist_explain, is_reply, reply_time, is_finish, finish_time, is_read, data_sources, is_deleted, is_valid, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistDepartmentPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_assist_department t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.complaintInfoId !=null and params.complaintInfoId != '' ">
                AND t.complaint_info_id = #{params.complaintInfoId}
            </if>
                    <if test=" params.object !=null and params.object != '' ">
                AND t.object = #{params.object}
            </if>
                    <if test=" params.followTime !=null and params.followTime != '' ">
                AND t.follow_time = #{params.followTime}
            </if>
                    <if test=" params.follower !=null and params.follower != '' ">
                AND t.follower = #{params.follower}
            </if>
                    <if test=" params.assistDepartment !=null and params.assistDepartment != '' ">
                AND t.assist_department = #{params.assistDepartment}
            </if>
                    <if test=" params.assistDepartmentName !=null and params.assistDepartmentName != '' ">
                AND t.assist_department_name = #{params.assistDepartmentName}
            </if>
                    <if test=" params.assistDealerCode !=null and params.assistDealerCode != '' ">
                AND t.assist_dealer_code = #{params.assistDealerCode}
            </if>
                    <if test=" params.assistDealerName !=null and params.assistDealerName != '' ">
                AND t.assist_dealer_name = #{params.assistDealerName}
            </if>
                    <if test=" params.hopeReplyTime !=null and params.hopeReplyTime != '' ">
                AND t.hope_reply_time = #{params.hopeReplyTime}
            </if>
                    <if test=" params.assistExplain !=null and params.assistExplain != '' ">
                AND t.assist_explain = #{params.assistExplain}
            </if>
                    <if test=" params.isReply !=null and params.isReply != '' ">
                AND t.is_reply = #{params.isReply}
            </if>
                    <if test=" params.replyTime !=null and params.replyTime != '' ">
                AND t.reply_time = #{params.replyTime}
            </if>
                    <if test=" params.isFinish !=null and params.isFinish != '' ">
                AND t.is_finish = #{params.isFinish}
            </if>
                    <if test=" params.finishTime !=null and params.finishTime != '' ">
                AND t.finish_time = #{params.finishTime}
            </if>
                    <if test=" params.isRead !=null and params.isRead != '' ">
                AND t.is_read = #{params.isRead}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            AND t.is_deleted=0
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistDepartmentPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_assist_department t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.complaintInfoId !=null and params.complaintInfoId != '' ">
                AND t.complaint_info_id = #{params.complaintInfoId}
            </if>
                    <if test=" params.object !=null and params.object != '' ">
                AND t.object = #{params.object}
            </if>
                    <if test=" params.followTime !=null and params.followTime != '' ">
                AND t.follow_time = #{params.followTime}
            </if>
                    <if test=" params.follower !=null and params.follower != '' ">
                AND t.follower = #{params.follower}
            </if>
                    <if test=" params.assistDepartment !=null and params.assistDepartment != '' ">
                AND t.assist_department = #{params.assistDepartment}
            </if>
                    <if test=" params.assistDepartmentName !=null and params.assistDepartmentName != '' ">
                AND t.assist_department_name = #{params.assistDepartmentName}
            </if>
                    <if test=" params.assistDealerCode !=null and params.assistDealerCode != '' ">
                AND t.assist_dealer_code = #{params.assistDealerCode}
            </if>
                    <if test=" params.assistDealerName !=null and params.assistDealerName != '' ">
                AND t.assist_dealer_name = #{params.assistDealerName}
            </if>
                    <if test=" params.hopeReplyTime !=null and params.hopeReplyTime != '' ">
                AND t.hope_reply_time = #{params.hopeReplyTime}
            </if>
                    <if test=" params.assistExplain !=null and params.assistExplain != '' ">
                AND t.assist_explain = #{params.assistExplain}
            </if>
                    <if test=" params.isReply !=null and params.isReply != '' ">
                AND t.is_reply = #{params.isReply}
            </if>
                    <if test=" params.replyTime !=null and params.replyTime != '' ">
                AND t.reply_time = #{params.replyTime}
            </if>
                    <if test=" params.isFinish !=null and params.isFinish != '' ">
                AND t.is_finish = #{params.isFinish}
            </if>
                    <if test=" params.finishTime !=null and params.finishTime != '' ">
                AND t.finish_time = #{params.finishTime}
            </if>
                    <if test=" params.isRead !=null and params.isRead != '' ">
                AND t.is_read = #{params.isRead}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectAssist" resultMap="BaseResultMap1" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistPO">
        SELECT
        t.app_id, t.owner_code, t.owner_par_code, t.org_id, t.id, t.complaint_info_id,
        ifnull(t.assist_department_name,t.assist_dealer_name) as allDealer,
        t.object, t.follow_time, t.follower,t.follower_name, t.assist_department, t.assist_department_name,
        t.assist_dealer_code, t.assist_dealer_name, t.hope_reply_time, t.assist_explain,
        t.is_reply, t.reply_time, t.is_finish, t.finish_time, t.is_read, t.data_sources,
        t.is_deleted, t.is_valid, t.created_at, t.updated_at
        FROM tt_complaint_assist_department t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.complaintInfoId !=null and params.complaintInfoId != '' ">
            AND t.complaint_info_id = #{params.complaintInfoId}
        </if>
        <if test=" params.object !=null and params.object != '' ">
            AND t.object = #{params.object}
        </if>
        <if test=" params.followTime !=null and params.followTime != '' ">
            AND t.follow_time = #{params.followTime}
        </if>
        <if test=" params.follower !=null and params.follower != '' ">
            AND t.follower = #{params.follower}
        </if>
        <if test=" params.assistDepartment !=null and params.assistDepartment != '' ">
            AND t.assist_department = #{params.assistDepartment}
        </if>
        <if test=" params.assistDepartmentName !=null and params.assistDepartmentName != '' ">
            AND t.assist_department_name = #{params.assistDepartmentName}
        </if>
        <if test=" params.assistDealerCode !=null and params.assistDealerCode != '' ">
            AND t.assist_dealer_code = #{params.assistDealerCode}
        </if>
        <if test=" params.assistDealerName !=null and params.assistDealerName != '' ">
            AND t.assist_dealer_name = #{params.assistDealerName}
        </if>
        <if test=" params.hopeReplyTime !=null and params.hopeReplyTime != '' ">
            AND t.hope_reply_time = #{params.hopeReplyTime}
        </if>
        <if test=" params.assistExplain !=null and params.assistExplain != '' ">
            AND t.assist_explain = #{params.assistExplain}
        </if>
        <if test=" params.isReply !=null and params.isReply != '' ">
            AND t.is_reply = #{params.isReply}
        </if>
        <if test=" params.replyTime !=null and params.replyTime != '' ">
            AND t.reply_time = #{params.replyTime}
        </if>
        <if test=" params.isFinish !=null and params.isFinish != '' ">
            AND t.is_finish = #{params.isFinish}
        </if>
        <if test=" params.finishTime !=null and params.finishTime != '' ">
            AND t.finish_time = #{params.finishTime}
        </if>
        <if test=" params.isRead !=null and params.isRead != '' ">
            AND t.is_read = #{params.isRead}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        AND t.is_deleted=0
    </select>

    <select id="selectAssistByid" resultMap="BaseResultMap1" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_assist_department t
        WHERE 1=1
        <if test=" params.complaintInfoId !=null and params.complaintInfoId != '' ">
            AND t.complaint_info_id = #{params.complaintInfoId}
        </if>
        ORDER BY t.created_at desc  LIMIT 1

    </select>
    <update id="updatestatus">
        UPDATE  tt_complaint_assist_department SET is_valid=10041002 where complaint_info_id=#{id} and assist_dealer_code!=""
    </update>
    <update id="updatestatus1">
        UPDATE  tt_complaint_assist_department SET is_valid=10041002 where complaint_info_id=#{id} and assist_department!=""
    </update>


    <update id="updateIsfinish">
        UPDATE  tt_complaint_assist_department SET is_finish=10041001 ,is_valid =10041002,is_reply=10041001,reply_time=NOW() where complaint_info_id=#{id} and assist_department=#{dem} and is_valid !=10041002
    </update>
    <update id="updateIsfinish1">
        UPDATE  tt_complaint_assist_department SET is_finish=10041002 ,is_reply=10041001,reply_time=NOW() where complaint_info_id=#{id} and assist_department=#{dem} and is_valid !=10041002
    </update>
    <update id="updateIsfinishByDealer">
        UPDATE  tt_complaint_assist_department SET is_finish=10041001 ,is_valid =10041002,is_reply=10041001,reply_time=NOW() where complaint_info_id=#{id} and assist_dealer_code=#{dem} and is_valid !=10041002
    </update>
    <update id="updateIsfinishByDealer1">
        UPDATE  tt_complaint_assist_department SET is_finish=10041002 ,is_reply=10041001,reply_time=NOW() where complaint_info_id=#{id} and assist_dealer_code=#{dem} and is_valid !=10041002
    </update>

    <update id="setAssistDepartmentNotEidt">
        UPDATE  tt_complaint_assist_department SET is_valid=10041002 where  complaint_info_id=#{id}
    </update>
    <select id="selectAssistValidByid" resultMap="BaseResultMap1" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_assist_department t
        WHERE 1=1
        <if test=" params.complaintInfoId !=null and params.complaintInfoId != '' ">
            AND t.complaint_info_id = #{params.complaintInfoId}
        </if>
        AND t.assist_department = #{params.assistDepartment}
        and t.is_valid !=10041002
        ORDER BY t.created_at desc  LIMIT 1

    </select>
    <select id="selectAssistList" resultType="com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAssistDepartmentDTO" parameterType="com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAssistDTO">
        SELECT  <include refid="Base_Column_List"/>
        FROM tt_complaint_assist_department t
        WHERE 1=1
            AND t.complaint_info_id = #{params.id}
        <if test=" params.assistDealerCode !=null and params.assistDealerCode != '' ">
            AND t.assist_dealer_code = #{params.assistDealerCode}
        </if>
        <if test=" params.assistDepartment !=null and params.assistDepartment != '' ">
            AND t.assist_department = #{params.assistDepartment}
        </if>
    </select>
    <select id="selectNo" resultType="com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfoDTO" parameterType="long">
        SELECT complaint_id from tt_complaint_info where id=#{complaintInfoId}
    </select>





</mapper>
