<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.voc.VocFunctionalStatusLogMapper">
    <insert id="insertList" parameterType="com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusLogPO">
        insert into  tt_voc_functional_status_log
        (
        vin,
        dt,
        activated_state,
        subscription_startdate,
        subscription_enddate
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.vin},
            #{item.dt},
            #{item.activatedState},
            #{item.subscriptionStartdate},
            #{item.subscriptionEnddate}
            )
        </foreach>

    </insert>


</mapper>