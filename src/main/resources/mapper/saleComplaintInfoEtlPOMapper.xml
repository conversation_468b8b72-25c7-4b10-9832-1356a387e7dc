<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintInfoEtlMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoEtlPO">
                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <result column="call_time" property="callTime"/>
                                                        <result column="complaint_id" property="complaintId"/>
                                                        <result column="work_order_nature" property="workOrderNature"/>
                                                        <result column="category1" property="category1"/>
                                                        <result column="category2" property="category2"/>
                                                        <result column="category3" property="category3"/>
                                                        <result column="buy_dealer_code" property="buyDealerCode"/>
                                                        <result column="buy_dealer_name" property="buyDealerName"/>
                                                        <result column="dealer_code" property="dealerCode"/>
                                                        <result column="dealer_name" property="dealerName"/>
                                                        <result column="customer_type" property="customerType"/>
                                                        <result column="call_name" property="callName"/>
                                                        <result column="call_tel" property="callTel"/>
                                                        <result column="reply_tel" property="replyTel"/>
                                                        <result column="reply_tel2" property="replyTel2"/>
                                                        <result column="vin" property="vin"/>
                                                        <result column="name" property="name"/>
                                                        <result column="phone" property="phone"/>
                                                        <result column="model" property="model"/>
                                                        <result column="license_plate_num" property="licensePlateNum"/>
                                                        <result column="buy_time" property="buyTime"/>
                                                        <result column="life_cycle" property="lifeCycle"/>
                                                        <result column="mileage" property="mileage"/>
                                                        <result column="problem" property="problem"/>
                                                        <result column="work_order_status" property="workOrderStatus"/>
                                                        <result column="close_case_status" property="closeCaseStatus"/>
                                                        <result column="dealer_fisrt_reply_time" property="dealerFisrtReplyTime"/>
                                                        <result column="close_case_time" property="closeCaseTime"/>
                                                        <result column="solve_time" property="solveTime"/>
                                                        <result column="reviewer" property="reviewer"/>
                                                        <result column="activity_source" property="activitySource"/>
                                                        <result column="revisit_demand" property="revisitDemand"/>
                                                        <result column="email_status" property="emailStatus"/>
                                                        <result column="is_48H_send_email" property="is48hSendEmail"/>
                                                        <result column="type" property="type"/>
                                                        <result column="again_call_time" property="againCallTime"/>
                                                        <result column="SMS" property="sms"/>
                                                        <result column="effective_complaint" property="effectiveComplaint"/>
                                                        <result column="source" property="source"/>
                                                        <result column="no_close_reasons_classification" property="noCloseReasonsClassification"/>
                                                        <result column="last_follow_time" property="lastFollowTime"/>
                                                        <result column="last_complaint_time" property="lastComplaintTime"/>
                                                        <result column="complaint_number" property="complaintNumber"/>
                                                        <result column="responsibility_determine" property="responsibilityDetermine"/>
                                                        <result column="subject" property="subject"/>
                                                        <result column="classification1" property="classification1"/>
                                                        <result column="classification2" property="classification2"/>
                                                        <result column="classification3" property="classification3"/>
                                                        <result column="classification4" property="classification4"/>
                                                        <result column="classification5" property="classification5"/>
                                                        <result column="classification6" property="classification6"/>
                                                        <result column="information_source" property="informationSource"/>
                                                        <result column="keyword" property="keyword"/>
                                                        <result column="consultation_and_solution" property="consultationAndSolution"/>
                                                        <result column="solution" property="solution"/>
                                                        <result column="is_FAQ" property="isFaq"/>
                                                        <result column="emotion_index_creation" property="emotionIndexCreation"/>
                                                        <result column="sentiment_index_current" property="sentimentIndexCurrent"/>
                                                        <result column="ccm_deal" property="ccmDeal"/>
                                                        <result column="first_revisit_time" property="firstRevisitTime"/>
                                                        <result column="restart_time" property="restartTime"/>
                                                        <result column="restart_number" property="restartNumber"/>
                                                        <result column="second_restart_time" property="secondRestartTime"/>
                                                        <result column="seconde_close_case_time" property="secondeCloseCaseTime"/>
                                                        <result column="case_level" property="caseLevel"/>
                                                        <result column="is_24H_reply" property="is24hReply"/>
                                                        <result column="is_48H_revisit" property="is48hRevisit"/>
                                                        <result column="is_5D_close_case" property="is5dCloseCase"/>
                                                        <result column="regional_feedback" property="regionalFeedback"/>
                                                        <result column="regional_feedback_time" property="regionalFeedbackTime"/>
                                                        <result column="no_revisit_reason" property="noRevisitReason"/>
                                                        <result column="no_close_reasons" property="noCloseReasons"/>
                                                        <result column="service_commitment" property="serviceCommitment"/>
                                                        <result column="first_return_time" property="firstReturnTime"/>
                                                    <result column="created_by" property="createdBy"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           id, call_time, complaint_id, work_order_nature, category1, category2, category3, buy_dealer_code, buy_dealer_name, dealer_code, dealer_name, customer_type, call_name, call_tel, reply_tel, reply_tel2, vin, name, phone, model, license_plate_num, buy_time, life_cycle, mileage, problem, work_order_status, close_case_status, dealer_fisrt_reply_time, close_case_time, solve_time, reviewer, activity_source, revisit_demand, email_status, is_48H_send_email, type, again_call_time, SMS, effective_complaint, source, no_close_reasons_classification, last_follow_time, last_complaint_time, complaint_number, responsibility_determine, subject, classification1, classification2, classification3, classification4, classification5, classification6, information_source, keyword, consultation_and_solution, solution, is_FAQ, emotion_index_creation, sentiment_index_current, ccm_deal, first_revisit_time, restart_time, restart_number, second_restart_time, seconde_close_case_time, case_level, is_24H_reply, is_48H_revisit, is_5D_close_case, regional_feedback, regional_feedback_time, no_revisit_reason, no_close_reasons, service_commitment, first_return_time
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoEtlPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_info_etl t
        WHERE 1=1
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.callTime !=null and params.callTime != '' ">
            AND t.call_time = #{params.callTime}
        </if>
        <if test=" params.complaintId !=null and params.complaintId != '' ">
            AND t.complaint_id  like "%" #{params.complaintId} "%"
        </if>
        <if test=" params.workOrderNature !=null and params.workOrderNature != '' ">
            AND t.work_order_nature = #{params.workOrderNature}
        </if>
        <if test=" params.category1 !=null and params.category1 != '' ">
            AND t.category1 = #{params.category1}
        </if>
        <if test=" params.category2 !=null and params.category2 != '' ">
            AND t.category2 = #{params.category2}
        </if>
        <if test=" params.category3 !=null and params.category3 != '' ">
            AND t.category3 = #{params.category3}
        </if>
        <if test=" params.buyDealerCode !=null and params.buyDealerCode != '' ">
            AND t.buy_dealer_code = #{params.buyDealerCode}
        </if>
        <if test=" params.buyDealerName !=null and params.buyDealerName != '' ">
            AND t.buy_dealer_name = #{params.buyDealerName}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.dealerName !=null and params.dealerName != '' ">
            AND t.dealer_name = #{params.dealerName}
        </if>
        <if test=" params.customerType !=null and params.customerType != '' ">
            AND t.customer_type = #{params.customerType}
        </if>
        <if test=" params.callName !=null and params.callName != '' ">
            AND t.call_name  like "%" #{params.callName} "%"
        </if>
        <if test=" params.callTel !=null and params.callTel != '' ">
            AND t.call_tel  like "%" #{params.callTel} "%"
        </if>
        <if test=" params.replyTel !=null and params.replyTel != '' ">
            AND t.reply_tel = #{params.replyTel}
        </if>
        <if test=" params.replyTel2 !=null and params.replyTel2 != '' ">
            AND t.reply_tel2 = #{params.replyTel2}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin like "%" #{params.vin} "%"
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name like "%" #{params.name} "%"
        </if>
        <if test=" params.phone !=null and params.phone != '' ">
            AND t.phone like "%" #{params.phone} "%"
        </if>
        <if test=" params.model !=null and params.model != '' ">
            AND t.model like "%" #{params.model} "%"
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num like "%" #{params.licensePlateNum} "%"
        </if>
        <if test=" params.buyTime !=null and params.buyTime != '' ">
            AND t.buy_time = #{params.buyTime}
        </if>
        <if test=" params.lifeCycle !=null and params.lifeCycle != '' ">
            AND t.life_cycle = #{params.lifeCycle}
        </if>
        <if test=" params.mileage !=null and params.mileage != '' ">
            AND t.mileage = #{params.mileage}
        </if>
        <if test=" params.problem !=null and params.problem != '' ">
            AND t.problem = #{params.problem}
        </if>
        <if test=" params.workOrderStatus !=null and params.workOrderStatus != '' ">
            AND t.work_order_status = #{params.workOrderStatus}
        </if>
        <if test=" params.closeCaseStatus !=null and params.closeCaseStatus != '' ">
            AND t.close_case_status = #{params.closeCaseStatus}
        </if>
        <if test=" params.dealerFisrtReplyTime !=null and params.dealerFisrtReplyTime != '' ">
            AND t.dealer_fisrt_reply_time = #{params.dealerFisrtReplyTime}
        </if>
        <if test=" params.closeCaseTime !=null and params.closeCaseTime != '' ">
            AND t.close_case_time = #{params.closeCaseTime}
        </if>
        <if test=" params.solveTime !=null and params.solveTime != '' ">
            AND t.solve_time = #{params.solveTime}
        </if>
        <if test=" params.reviewer !=null and params.reviewer != '' ">
            AND t.reviewer = #{params.reviewer}
        </if>
        <if test=" params.activitySource !=null and params.activitySource != '' ">
            AND t.activity_source = #{params.activitySource}
        </if>
        <if test=" params.revisitDemand !=null and params.revisitDemand != '' ">
            AND t.revisit_demand = #{params.revisitDemand}
        </if>
        <if test=" params.emailStatus !=null and params.emailStatus != '' ">
            AND t.email_status = #{params.emailStatus}
        </if>
        <if test=" params.is48hSendEmail !=null and params.is48hSendEmail != '' ">
            AND t.is_48H_send_email = #{params.is48hSendEmail}
        </if>
        <if test=" params.type !=null and params.type != '' ">
            AND t.type = #{params.type}
        </if>
        <if test=" params.againCallTime !=null and params.againCallTime != '' ">
            AND t.again_call_time = #{params.againCallTime}
        </if>
        <if test=" params.sms !=null and params.sms != '' ">
            AND t.SMS = #{params.sms}
        </if>
        <if test=" params.effectiveComplaint !=null and params.effectiveComplaint != '' ">
            AND t.effective_complaint = #{params.effectiveComplaint}
        </if>
        <if test=" params.source !=null and params.source != '' ">
            AND t.source = #{params.source}
        </if>
        <if test=" params.noCloseReasonsClassification !=null and params.noCloseReasonsClassification != '' ">
            AND t.no_close_reasons_classification = #{params.noCloseReasonsClassification}
        </if>
        <if test=" params.lastFollowTime !=null and params.lastFollowTime != '' ">
            AND t.last_follow_time = #{params.lastFollowTime}
        </if>
        <if test=" params.lastComplaintTime !=null and params.lastComplaintTime != '' ">
            AND t.last_complaint_time = #{params.lastComplaintTime}
        </if>
        <if test=" params.complaintNumber !=null and params.complaintNumber != '' ">
            AND t.complaint_number = #{params.complaintNumber}
        </if>
        <if test=" params.responsibilityDetermine !=null and params.responsibilityDetermine != '' ">
            AND t.responsibility_determine = #{params.responsibilityDetermine}
        </if>
        <if test=" params.subject !=null and params.subject != '' ">
            AND t.subject like "%" #{params.subject} "%"
        </if>
        <if test=" params.classification1 !=null and params.classification1 != '' ">
            AND t.classification1 = #{params.classification1}
        </if>
        <if test=" params.classification2 !=null and params.classification2 != '' ">
            AND t.classification2 = #{params.classification2}
        </if>
        <if test=" params.classification3 !=null and params.classification3 != '' ">
            AND t.classification3 = #{params.classification3}
        </if>
        <if test=" params.classification4 !=null and params.classification4 != '' ">
            AND t.classification4 = #{params.classification4}
        </if>
        <if test=" params.classification5 !=null and params.classification5 != '' ">
            AND t.classification5 = #{params.classification5}
        </if>
        <if test=" params.classification6 !=null and params.classification6 != '' ">
            AND t.classification6 = #{params.classification6}
        </if>
        <if test=" params.informationSource !=null and params.informationSource != '' ">
            AND t.information_source = #{params.informationSource}
        </if>
        <if test=" params.keyword !=null and params.keyword != '' ">
            AND t.keyword = #{params.keyword}
        </if>
        <if test=" params.consultationAndSolution !=null and params.consultationAndSolution != '' ">
            AND t.consultation_and_solution = #{params.consultationAndSolution}
        </if>
        <if test=" params.solution !=null and params.solution != '' ">
            AND t.solution = #{params.solution}
        </if>
        <if test=" params.isFaq !=null and params.isFaq != '' ">
            AND t.is_FAQ = #{params.isFaq}
        </if>
        <if test=" params.emotionIndexCreation !=null and params.emotionIndexCreation != '' ">
            AND t.emotion_index_creation = #{params.emotionIndexCreation}
        </if>
        <if test=" params.sentimentIndexCurrent !=null and params.sentimentIndexCurrent != '' ">
            AND t.sentiment_index_current = #{params.sentimentIndexCurrent}
        </if>
        <if test=" params.ccmDeal !=null and params.ccmDeal != '' ">
            AND t.ccm_deal = #{params.ccmDeal}
        </if>
        <if test=" params.firstRevisitTime !=null and params.firstRevisitTime != '' ">
            AND t.first_revisit_time = #{params.firstRevisitTime}
        </if>
        <if test=" params.restartTime !=null and params.restartTime != '' ">
            AND t.restart_time = #{params.restartTime}
        </if>
        <if test=" params.restartNumber !=null and params.restartNumber != '' ">
            AND t.restart_number = #{params.restartNumber}
        </if>
        <if test=" params.secondRestartTime !=null and params.secondRestartTime != '' ">
            AND t.second_restart_time = #{params.secondRestartTime}
        </if>
        <if test=" params.secondeCloseCaseTime !=null and params.secondeCloseCaseTime != '' ">
            AND t.seconde_close_case_time = #{params.secondeCloseCaseTime}
        </if>
        <if test=" params.caseLevel !=null and params.caseLevel != '' ">
            AND t.case_level = #{params.caseLevel}
        </if>
        <if test=" params.is24hReply !=null and params.is24hReply != '' ">
            AND t.is_24H_reply = #{params.is24hReply}
        </if>
        <if test=" params.is48hRevisit !=null and params.is48hRevisit != '' ">
            AND t.is_48H_revisit = #{params.is48hRevisit}
        </if>
        <if test=" params.is5dCloseCase !=null and params.is5dCloseCase != '' ">
            AND t.is_5D_close_case = #{params.is5dCloseCase}
        </if>
        <if test=" params.regionalFeedback !=null and params.regionalFeedback != '' ">
            AND t.regional_feedback = #{params.regionalFeedback}
        </if>
        <if test=" params.regionalFeedbackTime !=null and params.regionalFeedbackTime != '' ">
            AND t.regional_feedback_time = #{params.regionalFeedbackTime}
        </if>
        <if test=" params.noRevisitReason !=null and params.noRevisitReason != '' ">
            AND t.no_revisit_reason = #{params.noRevisitReason}
        </if>
        <if test=" params.noCloseReasons !=null and params.noCloseReasons != '' ">
            AND t.no_close_reasons = #{params.noCloseReasons}
        </if>
        <if test=" params.serviceCommitment !=null and params.serviceCommitment != '' ">
            AND t.service_commitment = #{params.serviceCommitment}
        </if>
        <if test=" params.firstReturnTime !=null and params.firstReturnTime != '' ">
            AND t.first_return_time = #{params.firstReturnTime}
        </if>
        <if test=" params.startCallTime !=null and params.startCallTime != '' ">
            AND t.call_time >= #{params.startCallTime}
        </if>
        <if test=" params.endCallTime !=null and params.endCallTime != '' ">
            AND t.call_time &lt;= #{params.endCallTime}
        </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoEtlPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_info_etl t
        WHERE 1=1
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.callTime !=null and params.callTime != '' ">
                AND t.call_time = #{params.callTime}
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id = #{params.complaintId}
            </if>
                    <if test=" params.workOrderNature !=null and params.workOrderNature != '' ">
                AND t.work_order_nature = #{params.workOrderNature}
            </if>
                    <if test=" params.category1 !=null and params.category1 != '' ">
                AND t.category1 = #{params.category1}
            </if>
                    <if test=" params.category2 !=null and params.category2 != '' ">
                AND t.category2 = #{params.category2}
            </if>
                    <if test=" params.category3 !=null and params.category3 != '' ">
                AND t.category3 = #{params.category3}
            </if>
                    <if test=" params.buyDealerCode !=null and params.buyDealerCode != '' ">
                AND t.buy_dealer_code = #{params.buyDealerCode}
            </if>
                    <if test=" params.buyDealerName !=null and params.buyDealerName != '' ">
                AND t.buy_dealer_name = #{params.buyDealerName}
            </if>
                    <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code = #{params.dealerCode}
            </if>
                    <if test=" params.dealerName !=null and params.dealerName != '' ">
                AND t.dealer_name = #{params.dealerName}
            </if>
                    <if test=" params.customerType !=null and params.customerType != '' ">
                AND t.customer_type = #{params.customerType}
            </if>
                    <if test=" params.callName !=null and params.callName != '' ">
                AND t.call_name = #{params.callName}
            </if>
                    <if test=" params.callTel !=null and params.callTel != '' ">
                AND t.call_tel = #{params.callTel}
            </if>
                    <if test=" params.replyTel !=null and params.replyTel != '' ">
                AND t.reply_tel = #{params.replyTel}
            </if>
                    <if test=" params.replyTel2 !=null and params.replyTel2 != '' ">
                AND t.reply_tel2 = #{params.replyTel2}
            </if>
                    <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin = #{params.vin}
            </if>
                    <if test=" params.name !=null and params.name != '' ">
                AND t.name = #{params.name}
            </if>
                    <if test=" params.phone !=null and params.phone != '' ">
                AND t.phone = #{params.phone}
            </if>
                    <if test=" params.model !=null and params.model != '' ">
                AND t.model = #{params.model}
            </if>
                    <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
                AND t.license_plate_num = #{params.licensePlateNum}
            </if>
                    <if test=" params.buyTime !=null and params.buyTime != '' ">
                AND t.buy_time = #{params.buyTime}
            </if>
                    <if test=" params.lifeCycle !=null and params.lifeCycle != '' ">
                AND t.life_cycle = #{params.lifeCycle}
            </if>
                    <if test=" params.mileage !=null and params.mileage != '' ">
                AND t.mileage = #{params.mileage}
            </if>
                    <if test=" params.problem !=null and params.problem != '' ">
                AND t.problem = #{params.problem}
            </if>
                    <if test=" params.workOrderStatus !=null and params.workOrderStatus != '' ">
                AND t.work_order_status = #{params.workOrderStatus}
            </if>
                    <if test=" params.closeCaseStatus !=null and params.closeCaseStatus != '' ">
                AND t.close_case_status = #{params.closeCaseStatus}
            </if>
                    <if test=" params.dealerFisrtReplyTime !=null and params.dealerFisrtReplyTime != '' ">
                AND t.dealer_fisrt_reply_time = #{params.dealerFisrtReplyTime}
            </if>
                    <if test=" params.closeCaseTime !=null and params.closeCaseTime != '' ">
                AND t.close_case_time = #{params.closeCaseTime}
            </if>
                    <if test=" params.solveTime !=null and params.solveTime != '' ">
                AND t.solve_time = #{params.solveTime}
            </if>
                    <if test=" params.reviewer !=null and params.reviewer != '' ">
                AND t.reviewer = #{params.reviewer}
            </if>
                    <if test=" params.activitySource !=null and params.activitySource != '' ">
                AND t.activity_source = #{params.activitySource}
            </if>
                    <if test=" params.revisitDemand !=null and params.revisitDemand != '' ">
                AND t.revisit_demand = #{params.revisitDemand}
            </if>
                    <if test=" params.emailStatus !=null and params.emailStatus != '' ">
                AND t.email_status = #{params.emailStatus}
            </if>
                    <if test=" params.is48hSendEmail !=null and params.is48hSendEmail != '' ">
                AND t.is_48H_send_email = #{params.is48hSendEmail}
            </if>
                    <if test=" params.type !=null and params.type != '' ">
                AND t.type = #{params.type}
            </if>
                    <if test=" params.againCallTime !=null and params.againCallTime != '' ">
                AND t.again_call_time = #{params.againCallTime}
            </if>
                    <if test=" params.sms !=null and params.sms != '' ">
                AND t.SMS = #{params.sms}
            </if>
                    <if test=" params.effectiveComplaint !=null and params.effectiveComplaint != '' ">
                AND t.effective_complaint = #{params.effectiveComplaint}
            </if>
                    <if test=" params.source !=null and params.source != '' ">
                AND t.source = #{params.source}
            </if>
                    <if test=" params.noCloseReasonsClassification !=null and params.noCloseReasonsClassification != '' ">
                AND t.no_close_reasons_classification = #{params.noCloseReasonsClassification}
            </if>
                    <if test=" params.lastFollowTime !=null and params.lastFollowTime != '' ">
                AND t.last_follow_time = #{params.lastFollowTime}
            </if>
                    <if test=" params.lastComplaintTime !=null and params.lastComplaintTime != '' ">
                AND t.last_complaint_time = #{params.lastComplaintTime}
            </if>
                    <if test=" params.complaintNumber !=null and params.complaintNumber != '' ">
                AND t.complaint_number = #{params.complaintNumber}
            </if>
                    <if test=" params.responsibilityDetermine !=null and params.responsibilityDetermine != '' ">
                AND t.responsibility_determine = #{params.responsibilityDetermine}
            </if>
                    <if test=" params.subject !=null and params.subject != '' ">
                AND t.subject = #{params.subject}
            </if>
                    <if test=" params.classification1 !=null and params.classification1 != '' ">
                AND t.classification1 = #{params.classification1}
            </if>
                    <if test=" params.classification2 !=null and params.classification2 != '' ">
                AND t.classification2 = #{params.classification2}
            </if>
                    <if test=" params.classification3 !=null and params.classification3 != '' ">
                AND t.classification3 = #{params.classification3}
            </if>
                    <if test=" params.classification4 !=null and params.classification4 != '' ">
                AND t.classification4 = #{params.classification4}
            </if>
                    <if test=" params.classification5 !=null and params.classification5 != '' ">
                AND t.classification5 = #{params.classification5}
            </if>
                    <if test=" params.classification6 !=null and params.classification6 != '' ">
                AND t.classification6 = #{params.classification6}
            </if>
                    <if test=" params.informationSource !=null and params.informationSource != '' ">
                AND t.information_source = #{params.informationSource}
            </if>
                    <if test=" params.keyword !=null and params.keyword != '' ">
                AND t.keyword = #{params.keyword}
            </if>
                    <if test=" params.consultationAndSolution !=null and params.consultationAndSolution != '' ">
                AND t.consultation_and_solution = #{params.consultationAndSolution}
            </if>
                    <if test=" params.solution !=null and params.solution != '' ">
                AND t.solution = #{params.solution}
            </if>
                    <if test=" params.isFaq !=null and params.isFaq != '' ">
                AND t.is_FAQ = #{params.isFaq}
            </if>
                    <if test=" params.emotionIndexCreation !=null and params.emotionIndexCreation != '' ">
                AND t.emotion_index_creation = #{params.emotionIndexCreation}
            </if>
                    <if test=" params.sentimentIndexCurrent !=null and params.sentimentIndexCurrent != '' ">
                AND t.sentiment_index_current = #{params.sentimentIndexCurrent}
            </if>
                    <if test=" params.ccmDeal !=null and params.ccmDeal != '' ">
                AND t.ccm_deal = #{params.ccmDeal}
            </if>
                    <if test=" params.firstRevisitTime !=null and params.firstRevisitTime != '' ">
                AND t.first_revisit_time = #{params.firstRevisitTime}
            </if>
                    <if test=" params.restartTime !=null and params.restartTime != '' ">
                AND t.restart_time = #{params.restartTime}
            </if>
                    <if test=" params.restartNumber !=null and params.restartNumber != '' ">
                AND t.restart_number = #{params.restartNumber}
            </if>
                    <if test=" params.secondRestartTime !=null and params.secondRestartTime != '' ">
                AND t.second_restart_time = #{params.secondRestartTime}
            </if>
                    <if test=" params.secondeCloseCaseTime !=null and params.secondeCloseCaseTime != '' ">
                AND t.seconde_close_case_time = #{params.secondeCloseCaseTime}
            </if>
                    <if test=" params.caseLevel !=null and params.caseLevel != '' ">
                AND t.case_level = #{params.caseLevel}
            </if>
                    <if test=" params.is24hReply !=null and params.is24hReply != '' ">
                AND t.is_24H_reply = #{params.is24hReply}
            </if>
                    <if test=" params.is48hRevisit !=null and params.is48hRevisit != '' ">
                AND t.is_48H_revisit = #{params.is48hRevisit}
            </if>
                    <if test=" params.is5dCloseCase !=null and params.is5dCloseCase != '' ">
                AND t.is_5D_close_case = #{params.is5dCloseCase}
            </if>
                    <if test=" params.regionalFeedback !=null and params.regionalFeedback != '' ">
                AND t.regional_feedback = #{params.regionalFeedback}
            </if>
                    <if test=" params.regionalFeedbackTime !=null and params.regionalFeedbackTime != '' ">
                AND t.regional_feedback_time = #{params.regionalFeedbackTime}
            </if>
                    <if test=" params.noRevisitReason !=null and params.noRevisitReason != '' ">
                AND t.no_revisit_reason = #{params.noRevisitReason}
            </if>
                    <if test=" params.noCloseReasons !=null and params.noCloseReasons != '' ">
                AND t.no_close_reasons = #{params.noCloseReasons}
            </if>
                    <if test=" params.serviceCommitment !=null and params.serviceCommitment != '' ">
                AND t.service_commitment = #{params.serviceCommitment}
            </if>
                    <if test=" params.firstReturnTime !=null and params.firstReturnTime != '' ">
                AND t.first_return_time = #{params.firstReturnTime}
            </if>
            </select>
    <select id="exportSaleComplaintHistory" resultType="hashmap"
            parameterType="com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoEtlDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_info_etl t
        WHERE 1=1
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.callTime !=null and params.callTime != '' ">
            AND t.call_time = #{params.callTime}
        </if>
        <if test=" params.complaintId !=null and params.complaintId != '' ">
            AND t.complaint_id  like "%" #{params.complaintId} "%"
        </if>
        <if test=" params.workOrderNature !=null and params.workOrderNature != '' ">
            AND t.work_order_nature = #{params.workOrderNature}
        </if>
        <if test=" params.category1 !=null and params.category1 != '' ">
            AND t.category1 = #{params.category1}
        </if>
        <if test=" params.category2 !=null and params.category2 != '' ">
            AND t.category2 = #{params.category2}
        </if>
        <if test=" params.category3 !=null and params.category3 != '' ">
            AND t.category3 = #{params.category3}
        </if>
        <if test=" params.buyDealerCode !=null and params.buyDealerCode != '' ">
            AND t.buy_dealer_code = #{params.buyDealerCode}
        </if>
        <if test=" params.buyDealerName !=null and params.buyDealerName != '' ">
            AND t.buy_dealer_name = #{params.buyDealerName}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.dealerName !=null and params.dealerName != '' ">
            AND t.dealer_name = #{params.dealerName}
        </if>
        <if test=" params.customerType !=null and params.customerType != '' ">
            AND t.customer_type = #{params.customerType}
        </if>
        <if test=" params.callName !=null and params.callName != '' ">
            AND t.call_name  like "%" #{params.callName} "%"
        </if>
        <if test=" params.callTel !=null and params.callTel != '' ">
            AND t.call_tel  like "%" #{params.callTel} "%"
        </if>
        <if test=" params.replyTel !=null and params.replyTel != '' ">
            AND t.reply_tel = #{params.replyTel}
        </if>
        <if test=" params.replyTel2 !=null and params.replyTel2 != '' ">
            AND t.reply_tel2 = #{params.replyTel2}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin like "%" #{params.vin} "%"
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name like "%" #{params.name} "%"
        </if>
        <if test=" params.phone !=null and params.phone != '' ">
            AND t.phone like "%" #{params.phone} "%"
        </if>
        <if test=" params.model !=null and params.model != '' ">
            AND t.model like "%" #{params.model} "%"
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num like "%" #{params.licensePlateNum} "%"
        </if>
        <if test=" params.buyTime !=null and params.buyTime != '' ">
            AND t.buy_time = #{params.buyTime}
        </if>
        <if test=" params.lifeCycle !=null and params.lifeCycle != '' ">
            AND t.life_cycle = #{params.lifeCycle}
        </if>
        <if test=" params.mileage !=null and params.mileage != '' ">
            AND t.mileage = #{params.mileage}
        </if>
        <if test=" params.problem !=null and params.problem != '' ">
            AND t.problem = #{params.problem}
        </if>
        <if test=" params.workOrderStatus !=null and params.workOrderStatus != '' ">
            AND t.work_order_status = #{params.workOrderStatus}
        </if>
        <if test=" params.closeCaseStatus !=null and params.closeCaseStatus != '' ">
            AND t.close_case_status = #{params.closeCaseStatus}
        </if>
        <if test=" params.dealerFisrtReplyTime !=null and params.dealerFisrtReplyTime != '' ">
            AND t.dealer_fisrt_reply_time = #{params.dealerFisrtReplyTime}
        </if>
        <if test=" params.closeCaseTime !=null and params.closeCaseTime != '' ">
            AND t.close_case_time = #{params.closeCaseTime}
        </if>
        <if test=" params.solveTime !=null and params.solveTime != '' ">
            AND t.solve_time = #{params.solveTime}
        </if>
        <if test=" params.reviewer !=null and params.reviewer != '' ">
            AND t.reviewer = #{params.reviewer}
        </if>
        <if test=" params.activitySource !=null and params.activitySource != '' ">
            AND t.activity_source = #{params.activitySource}
        </if>
        <if test=" params.revisitDemand !=null and params.revisitDemand != '' ">
            AND t.revisit_demand = #{params.revisitDemand}
        </if>
        <if test=" params.emailStatus !=null and params.emailStatus != '' ">
            AND t.email_status = #{params.emailStatus}
        </if>
        <if test=" params.is48hSendEmail !=null and params.is48hSendEmail != '' ">
            AND t.is_48H_send_email = #{params.is48hSendEmail}
        </if>
        <if test=" params.type !=null and params.type != '' ">
            AND t.type = #{params.type}
        </if>
        <if test=" params.againCallTime !=null and params.againCallTime != '' ">
            AND t.again_call_time = #{params.againCallTime}
        </if>
        <if test=" params.sms !=null and params.sms != '' ">
            AND t.SMS = #{params.sms}
        </if>
        <if test=" params.effectiveComplaint !=null and params.effectiveComplaint != '' ">
            AND t.effective_complaint = #{params.effectiveComplaint}
        </if>
        <if test=" params.source !=null and params.source != '' ">
            AND t.source = #{params.source}
        </if>
        <if test=" params.noCloseReasonsClassification !=null and params.noCloseReasonsClassification != '' ">
            AND t.no_close_reasons_classification = #{params.noCloseReasonsClassification}
        </if>
        <if test=" params.lastFollowTime !=null and params.lastFollowTime != '' ">
            AND t.last_follow_time = #{params.lastFollowTime}
        </if>
        <if test=" params.lastComplaintTime !=null and params.lastComplaintTime != '' ">
            AND t.last_complaint_time = #{params.lastComplaintTime}
        </if>
        <if test=" params.complaintNumber !=null and params.complaintNumber != '' ">
            AND t.complaint_number = #{params.complaintNumber}
        </if>
        <if test=" params.responsibilityDetermine !=null and params.responsibilityDetermine != '' ">
            AND t.responsibility_determine = #{params.responsibilityDetermine}
        </if>
        <if test=" params.subject !=null and params.subject != '' ">
            AND t.subject like "%" #{params.subject} "%"
        </if>
        <if test=" params.classification1 !=null and params.classification1 != '' ">
            AND t.classification1 = #{params.classification1}
        </if>
        <if test=" params.classification2 !=null and params.classification2 != '' ">
            AND t.classification2 = #{params.classification2}
        </if>
        <if test=" params.classification3 !=null and params.classification3 != '' ">
            AND t.classification3 = #{params.classification3}
        </if>
        <if test=" params.classification4 !=null and params.classification4 != '' ">
            AND t.classification4 = #{params.classification4}
        </if>
        <if test=" params.classification5 !=null and params.classification5 != '' ">
            AND t.classification5 = #{params.classification5}
        </if>
        <if test=" params.classification6 !=null and params.classification6 != '' ">
            AND t.classification6 = #{params.classification6}
        </if>
        <if test=" params.informationSource !=null and params.informationSource != '' ">
            AND t.information_source = #{params.informationSource}
        </if>
        <if test=" params.keyword !=null and params.keyword != '' ">
            AND t.keyword = #{params.keyword}
        </if>
        <if test=" params.consultationAndSolution !=null and params.consultationAndSolution != '' ">
            AND t.consultation_and_solution = #{params.consultationAndSolution}
        </if>
        <if test=" params.solution !=null and params.solution != '' ">
            AND t.solution = #{params.solution}
        </if>
        <if test=" params.isFaq !=null and params.isFaq != '' ">
            AND t.is_FAQ = #{params.isFaq}
        </if>
        <if test=" params.emotionIndexCreation !=null and params.emotionIndexCreation != '' ">
            AND t.emotion_index_creation = #{params.emotionIndexCreation}
        </if>
        <if test=" params.sentimentIndexCurrent !=null and params.sentimentIndexCurrent != '' ">
            AND t.sentiment_index_current = #{params.sentimentIndexCurrent}
        </if>
        <if test=" params.ccmDeal !=null and params.ccmDeal != '' ">
            AND t.ccm_deal = #{params.ccmDeal}
        </if>
        <if test=" params.firstRevisitTime !=null and params.firstRevisitTime != '' ">
            AND t.first_revisit_time = #{params.firstRevisitTime}
        </if>
        <if test=" params.restartTime !=null and params.restartTime != '' ">
            AND t.restart_time = #{params.restartTime}
        </if>
        <if test=" params.restartNumber !=null and params.restartNumber != '' ">
            AND t.restart_number = #{params.restartNumber}
        </if>
        <if test=" params.secondRestartTime !=null and params.secondRestartTime != '' ">
            AND t.second_restart_time = #{params.secondRestartTime}
        </if>
        <if test=" params.secondeCloseCaseTime !=null and params.secondeCloseCaseTime != '' ">
            AND t.seconde_close_case_time = #{params.secondeCloseCaseTime}
        </if>
        <if test=" params.caseLevel !=null and params.caseLevel != '' ">
            AND t.case_level = #{params.caseLevel}
        </if>
        <if test=" params.is24hReply !=null and params.is24hReply != '' ">
            AND t.is_24H_reply = #{params.is24hReply}
        </if>
        <if test=" params.is48hRevisit !=null and params.is48hRevisit != '' ">
            AND t.is_48H_revisit = #{params.is48hRevisit}
        </if>
        <if test=" params.is5dCloseCase !=null and params.is5dCloseCase != '' ">
            AND t.is_5D_close_case = #{params.is5dCloseCase}
        </if>
        <if test=" params.regionalFeedback !=null and params.regionalFeedback != '' ">
            AND t.regional_feedback = #{params.regionalFeedback}
        </if>
        <if test=" params.regionalFeedbackTime !=null and params.regionalFeedbackTime != '' ">
            AND t.regional_feedback_time = #{params.regionalFeedbackTime}
        </if>
        <if test=" params.noRevisitReason !=null and params.noRevisitReason != '' ">
            AND t.no_revisit_reason = #{params.noRevisitReason}
        </if>
        <if test=" params.noCloseReasons !=null and params.noCloseReasons != '' ">
            AND t.no_close_reasons = #{params.noCloseReasons}
        </if>
        <if test=" params.serviceCommitment !=null and params.serviceCommitment != '' ">
            AND t.service_commitment = #{params.serviceCommitment}
        </if>
        <if test=" params.firstReturnTime !=null and params.firstReturnTime != '' ">
            AND t.first_return_time = #{params.firstReturnTime}
        </if>
        <if test=" params.startCallTime !=null and params.startCallTime != '' ">
            AND t.call_time >= #{params.startCallTime}
        </if>
        <if test=" params.endCallTime !=null and params.endCallTime != '' ">
            AND t.call_time &lt;= #{params.endCallTime}
        </if>
    </select>

</mapper>
