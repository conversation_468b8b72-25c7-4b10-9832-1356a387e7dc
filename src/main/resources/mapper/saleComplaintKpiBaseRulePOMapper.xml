<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintKpiBaseRuleMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintKpiBaseRulePO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                                                    <result column="app_id" property="appId"/>
                                                        <result column="owner_code" property="ownerCode"/>
                                                        <result column="owner_par_code" property="ownerParCode"/>
                                                        <result column="org_id" property="orgId"/>
                                                                                    <result column="kpi_name" property="kpiName"/>
                                                        <result column="kpi" property="kpi"/>
                                                        <result column="index_value" property="indexValue"/>
                                                        <result column="rule_name" property="ruleName"/>
                                                        <result column="rule" property="rule"/>
                                                        <result column="score" property="score"/>
                                                        <result column="formula" property="formula"/>
                                                        <result column="data_sources" property="dataSources"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="is_valid" property="isValid"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, kpi_name, kpi, index_value, rule_name, rule, score, formula, data_sources, is_deleted, is_valid, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintKpiBaseRulePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_kpi_base_rule t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.kpiName !=null and params.kpiName != '' ">
                AND t.kpi_name = #{params.kpiName}
            </if>
                    <if test=" params.kpi !=null and params.kpi != '' ">
                AND t.kpi = #{params.kpi}
            </if>
                    <if test=" params.indexValue !=null and params.indexValue != '' ">
                AND t.index_value = #{params.indexValue}
            </if>
                    <if test=" params.ruleName !=null and params.ruleName != '' ">
                AND t.rule_name = #{params.ruleName}
            </if>
                    <if test=" params.rule !=null and params.rule != '' ">
                AND t.rule = #{params.rule}
            </if>
                    <if test=" params.score !=null and params.score != '' ">
                AND t.score = #{params.score}
            </if>
                    <if test=" params.formula !=null and params.formula != '' ">
                AND t.formula = #{params.formula}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintKpiBaseRulePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_kpi_base_rule t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.kpiName !=null and params.kpiName != '' ">
                AND t.kpi_name = #{params.kpiName}
            </if>
                    <if test=" params.kpi !=null and params.kpi != '' ">
                AND t.kpi = #{params.kpi}
            </if>
                    <if test=" params.indexValue !=null and params.indexValue != '' ">
                AND t.index_value = #{params.indexValue}
            </if>
                    <if test=" params.ruleName !=null and params.ruleName != '' ">
                AND t.rule_name = #{params.ruleName}
            </if>
                    <if test=" params.rule !=null and params.rule != '' ">
                AND t.rule = #{params.rule}
            </if>
                    <if test=" params.score !=null and params.score != '' ">
                AND t.score = #{params.score}
            </if>
                    <if test=" params.formula !=null and params.formula != '' ">
                AND t.formula = #{params.formula}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <update id="updateKpi" parameterType="com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintKpiBaseRuleDTO">
        UPDATE  tt_sale_complaint_kpi_base_rule set index_value = #{params.indexValue},score = #{params.score},is_valid = #{params.isValid}
        ,updated_at = now(),updated_by=#{params.createdBy} where kpi = #{params.kpi}
    </update>

</mapper>
