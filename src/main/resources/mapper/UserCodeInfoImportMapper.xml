<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.userCode.UserCodeInfoImportMapper">
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.userCode.UserCodeInfoImportPo">
            <result column="id" property="id"/>
            <result column="belonging" property="belonging"/>
            <result column="belonging_name" property="belongingName"/>
            <result column="employee_name" property="employeeName"/>
            <result column="employee_no" property="employeeNo"/>
            <result column="position" property="position"/>
            <result column="user_code" property="userCode"/>
            <result column="mobile" property="mobile"/>
            <result column="email" property="email"/>
            <result column="is_valid" property="isValid"/>
            <result column="is_error" property="isError"></result>
            <result column="error_msg" property="errorMsg"></result>
            <result column="line_number" property="lineNumber"></result>
            <result column="created_by" property="createdBy"></result>
        </resultMap>


        <select id="selectErrorPage" resultType="com.yonyou.dmscus.customer.entity.po.userCode.UserCodeInfoImportPo">
        SELECT
          id ,
          belonging ,
          belonging_name,
          employee_name ,
          employee_no,
          position ,
          user_code,
          mobile ,
          email ,
          is_valid ,
          is_error ,
          error_msg ,
          line_number ,
          created_at ,
          created_by ,
          updated_by ,
          udpated_at ,
          record_version
        FROM tt_user_code_info_import  where is_error=1 and
        created_by=#{userId}
    </select>
    <delete id="deleteByCreatedBy" parameterType="java.lang.String">
        delete from tt_user_code_info_import where created_by =#{userId}
    </delete>

    <select id="queryError" resultMap="BaseResultMap">
        SELECT
          id ,
          belonging ,
          belonging_name ,
          employee_name ,
          employee_no,
          position ,
          user_code,
          mobile ,
          email ,
          is_valid ,
          is_error ,
          error_msg ,
          line_number ,
          created_at ,
          created_by ,
          updated_by ,
          udpated_at ,
          record_version
        FROM tt_user_code_info_import  where is_error=1 and
        created_by=#{userId}
    </select>


    <select id="querySuccess"
            resultMap="BaseResultMap">
        SELECT
             id ,
          belonging ,
          belonging_name,
          employee_name ,
          employee_no,
          position ,
          user_code,
          mobile ,
          email ,
          is_valid ,
          is_error ,
          error_msg ,
          line_number ,
          created_at ,
          created_by ,
          updated_by ,
          udpated_at ,
          record_version
        FROM tt_user_code_info_import t where t.is_error=0 and
        t.created_by=#{userId}
    </select>

    <!-- 查询显示成功导入的数据条数 -->
    <select id="querySucessCount"
            resultType="java.lang.Integer"> SELECT count(1) FROM tt_user_code_info_import t
        where t.is_error=0 and t.created_by=#{userId}
    </select>

    <insert id="bulkInsert">
        insert into tt_user_code_info_import(
            belonging ,
            belonging_name,
            employee_name ,
            employee_no,
            position ,
            user_code,
            mobile ,
            email ,
            is_valid ,
            is_error ,
            error_msg ,
            line_number ,
            created_at ,
            created_by ,
            updated_by ,
            udpated_at ,
            record_version
        ) values
        <foreach collection ="addList" item="dto" separator =",">
            (#{dto.belonging},
            #{dto.belongingName},
            #{dto.employeeName},
            #{dto.employeeNo},
            #{dto.position},
            #{dto.userCode},
            #{dto.mobile},
            #{dto.email},
            #{dto.isValid},
            #{dto.isError},
            #{dto.errorMsg},
            #{dto.lineNumber},
            now(),
            #{userId},
            1,
            now(),
            1
            )
        </foreach >
    </insert>

</mapper>