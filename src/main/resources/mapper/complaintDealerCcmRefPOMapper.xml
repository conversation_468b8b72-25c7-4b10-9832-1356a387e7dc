<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.complaint.ComplaintDealerCcmRefMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintDealerCcmRefPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                                                    <result column="app_id" property="appId"/>
                                                        <result column="owner_code" property="ownerCode"/>
                                                        <result column="owner_par_code" property="ownerParCode"/>
                                                        <result column="org_id" property="orgId"/>
                                                        <result column="region" property="region"/>
                                                        <result column="region_manager" property="regionManager"/>
                                                        <result column="bloc" property="bloc"/>
                                                        <result column="dealer_code" property="dealerCode"/>
                                                        <result column="dealer_name" property="dealerName"/>
                                                        <result column="ccm_man" property="ccmMan"/>
                                                        <result column="ccm_man_id" property="ccmManId"/>
                                                        <result column="data_sources" property="dataSources"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="is_valid" property="isValid"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, region, region_manager, bloc, dealer_code, dealer_name, ccm_man, ccm_man_id, data_sources, is_deleted, is_valid, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintDealerCcmRefPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_dealer_ccm_ref t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.region !=null and params.region != '' ">
                AND t.region = #{params.region}
            </if>
                    <if test=" params.regionManager !=null and params.regionManager != '' ">
                AND t.region_manager = #{params.regionManager}
            </if>
                    <if test=" params.bloc !=null and params.bloc != '' ">
                AND t.bloc = #{params.bloc}
            </if>
                    <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code = #{params.dealerCode}
            </if>
                    <if test=" params.dealerName !=null and params.dealerName != '' ">
                AND t.dealer_name = #{params.dealerName}
            </if>
                    <if test=" params.ccmMan !=null and params.ccmMan != '' ">
                AND t.ccm_man = #{params.ccmMan}
            </if>
                    <if test=" params.ccmManId !=null and params.ccmManId != '' ">
                AND t.ccm_man_id = #{params.ccmManId}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
        and t.is_deleted=0
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintDealerCcmRefPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_dealer_ccm_ref t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.region !=null and params.region != '' ">
                AND t.region = #{params.region}
            </if>
                    <if test=" params.regionManager !=null and params.regionManager != '' ">
                AND t.region_manager = #{params.regionManager}
            </if>
                    <if test=" params.bloc !=null and params.bloc != '' ">
                AND t.bloc = #{params.bloc}
            </if>
                    <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code = #{params.dealerCode}
            </if>
                    <if test=" params.dealerName !=null and params.dealerName != '' ">
                AND t.dealer_name = #{params.dealerName}
            </if>
                    <if test=" params.ccmMan !=null and params.ccmMan != '' ">
                AND t.ccm_man = #{params.ccmMan}
            </if>
                    <if test=" params.ccmManId !=null and params.ccmManId != '' ">
                AND t.ccm_man_id = #{params.ccmManId}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
        and t.is_deleted=0
            </select>


    <insert id="importCcm"
            parameterType="com.yonyou.dmscus.customer.entity.dto.complaint.CcmImportDto1">
        INSERT INTO tt_complaint_dealer_ccm_ref
        (dealer_code,
        dealer_name,ccm_man,region,region_id,region_manager_id,region_manager,bloc,app_id, owner_code, owner_par_code,
        org_id,created_at,created_by,ccm_man_id)
        select a.dealer_code,
        #{params.dealerName},
       a.ccm_man,#{params.afterBigAreaName},#{params.afterBigAreaId},#{params.afterSmallAreaId},#{params.afterSmallAreaName},#{params.groupCompanyName},a.app_id,
        a.owner_code,
        a.owner_par_code,a.org_id,
        CURRENT_TIMESTAMP, a.created_by,#{params.ccmManId}
        from
        te_complaint_dealer_ccm_ref_import a
        where
        a.is_deleted=0 and
        a.is_error=0 and
        a.created_by=#{params.userId}
        and a.id = #{params.id}
    </insert>

    <update id="updateCcmAll" parameterType="com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintDealerCcmRefDTO">
        UPDATE tt_complaint_dealer_ccm_ref SET ccm_man=#{params.ccmMan},ccm_man_id=#{params.ccmManId},created_by=#{params.id},
        created_at=CURRENT_TIMESTAMP
        where is_deleted=0 and  region_id=#{params.regionId}
        <if test=" params.regionManagerId !=null and params.regionManagerId != '' ">
            AND region_manager_id = #{params.regionManagerId}
        </if>

    </update>

    <delete id="deleteAll">
        DELETE  FROM tt_complaint_dealer_ccm_ref
        where  dealer_code = #{dealerCode}

    </delete>
</mapper>
