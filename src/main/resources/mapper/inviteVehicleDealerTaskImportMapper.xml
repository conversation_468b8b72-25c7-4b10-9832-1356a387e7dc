<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.invitationCreate.InviteVehicleDealerTaskImportMapper">

    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskImportPO">
        <result column="owner_code" property="ownerCode"/>
        <result column="app_id" property="appId"></result>
        <result column="dealer_code" property="dealerCode"></result>
        <result column="invite_name" property="inviteName"></result>
        <result column="follow_mode" property="followMode"></result>
        <result column="advise_in_date" property="adviseInDate"></result>
        <result column="vin" property="vin"></result>
        <result column="name" property="name"></result>
        <result column="tel" property="tel"></result>
        <result column="is_error" property="isError"></result>
        <result column="error_msg" property="errorMsg"></result>
        <result column="line_number" property="lineNumber"></result>
        <result column="license_plate_num" property="licensePlateNum"></result>
        <result column="created_by" property="createdBy"></result>
    </resultMap>


    <delete id="deleteByCreatedBy" >
        delete from tt_invite_vehicle_dealer_task_import where created_by =#{createdBy}
    </delete>

    <insert id="bulkInsert">
        insert into tt_invite_vehicle_dealer_task_import(dealer_code,invite_name,follow_mode,
        vin,name,tel,is_error,error_msg,line_number,
        created_by,created_at,record_version,advise_in_date,license_plate_num) values
        <foreach collection ="addList" item="dto" separator =",">
            (#{dto.dealerCode},#{dto.inviteName},#{dto.followMode},
            #{dto.vin},#{dto.name},#{dto.tel},#{dto.isError},#{dto.errorMsg},#{dto.lineNumber}
            ,#{userId},now(),1,#{dto.adviseInDate},#{dto.licensePlateNum})
        </foreach >
    </insert>

    <select id="queryError"
            resultMap="BaseResultMap">
        SELECT
        *
        FROM tt_invite_vehicle_dealer_task_import t where t.is_error=1 and
        t.created_by=#{userId}
    </select>


    <select id="querySuccess"
            resultMap="BaseResultMap">
        SELECT
        *
        FROM tt_invite_vehicle_dealer_task_import t where t.is_error=0 and
        t.created_by=#{userId}
    </select>

    <!-- 查询显示成功导入的数据条数 -->
    <select id="querySucessCount" resultType="java.lang.Integer">
        SELECT count(1) FROM tt_invite_vehicle_dealer_task_import t
        where t.is_error=0 and t.created_by=#{userId}
    </select>
    
     <select id="selectErrorPage"
            resultMap="BaseResultMap">
        SELECT
        *
        FROM tt_invite_vehicle_dealer_task_import t where t.is_error=1 and
        t.created_by=#{userId}
    </select>


    <select id="selectSuccessPage"
            resultMap="BaseResultMap">
        SELECT
        *
        FROM tt_invite_vehicle_dealer_task_import t where t.is_error=0 and
        t.created_by=#{userId}
    </select>
    
    <update id="updateErrorById">
        update tt_invite_vehicle_dealer_task_import set is_error = 1 ,error_msg = 'VIN不存在'
        where created_by=#{userId} and vin in
         <foreach item="item" index="index" collection="list" 
                         open="(" separator="," close=")">
                        #{item}
                </foreach> 
    </update>
</mapper>
