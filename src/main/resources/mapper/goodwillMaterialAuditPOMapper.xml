<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillMaterialAuditMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMaterialAuditPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <result column="APP_ID" property="appId"/>
                                                        <result column="OWNER_CODE" property="ownerCode"/>
                                                        <result column="OWNER_PAR_CODE" property="ownerParCode"/>
                                                        <result column="ORG_ID" property="orgId"/>
                                                                                    <result column="goodwill_apply_id" property="goodwillApplyId"/>
                                                        <result column="customer_background" property="customerBackground"/>
                                                        <result column="customer_background_en" property="customerBackgroundEn"/>
                                                        <result column="reason_and_dispose" property="reasonAndDispose"/>
                                                        <result column="reason_and_dispose_en" property="reasonAndDisposeEn"/>
                                                        <result column="repair_solution" property="repairSolution"/>
                                                        <result column="repair_solution_en" property="repairSolutionEn"/>
                                                        <result column="customer_require" property="customerRequire"/>
                                                        <result column="customer_require_en" property="customerRequireEn"/>
                                                        <result column="potential_risk" property="potentialRisk"/>
                                                        <result column="potential_risk_en" property="potentialRiskEn"/>
                                                        <result column="vr_or_tj_no_en" property="vrOrTjNoEn"/>
                                                        <result column="vr_or_tj_no" property="vrOrTjNo"/>
                                                        <result column="business_goodwill_apply_detail_en" property="businessGoodwillApplyDetailEn"/>
                                                        <result column="business_goodwill_apply_detail" property="businessGoodwillApplyDetail"/>
                                                        <result column="is_valid" property="isValid"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, goodwill_apply_id, customer_background, customer_background_en, reason_and_dispose, reason_and_dispose_en, repair_solution, repair_solution_en, customer_require, customer_require_en, potential_risk, potential_risk_en, vr_or_tj_no_en, vr_or_tj_no, business_goodwill_apply_detail_en, business_goodwill_apply_detail, is_valid, is_deleted, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMaterialAuditPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_material_audit t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.customerBackground !=null and params.customerBackground != '' ">
                AND t.customer_background = #{params.customerBackground}
            </if>
                    <if test=" params.customerBackgroundEn !=null and params.customerBackgroundEn != '' ">
                AND t.customer_background_en = #{params.customerBackgroundEn}
            </if>
                    <if test=" params.reasonAndDispose !=null and params.reasonAndDispose != '' ">
                AND t.reason_and_dispose = #{params.reasonAndDispose}
            </if>
                    <if test=" params.reasonAndDisposeEn !=null and params.reasonAndDisposeEn != '' ">
                AND t.reason_and_dispose_en = #{params.reasonAndDisposeEn}
            </if>
                    <if test=" params.repairSolution !=null and params.repairSolution != '' ">
                AND t.repair_solution = #{params.repairSolution}
            </if>
                    <if test=" params.repairSolutionEn !=null and params.repairSolutionEn != '' ">
                AND t.repair_solution_en = #{params.repairSolutionEn}
            </if>
                    <if test=" params.customerRequire !=null and params.customerRequire != '' ">
                AND t.customer_require = #{params.customerRequire}
            </if>
                    <if test=" params.customerRequireEn !=null and params.customerRequireEn != '' ">
                AND t.customer_require_en = #{params.customerRequireEn}
            </if>
                    <if test=" params.potentialRisk !=null and params.potentialRisk != '' ">
                AND t.potential_risk = #{params.potentialRisk}
            </if>
                    <if test=" params.potentialRiskEn !=null and params.potentialRiskEn != '' ">
                AND t.potential_risk_en = #{params.potentialRiskEn}
            </if>
                    <if test=" params.vrOrTjNoEn !=null and params.vrOrTjNoEn != '' ">
                AND t.vr_or_tj_no_en = #{params.vrOrTjNoEn}
            </if>
                    <if test=" params.vrOrTjNo !=null and params.vrOrTjNo != '' ">
                AND t.vr_or_tj_no = #{params.vrOrTjNo}
            </if>
                    <if test=" params.businessGoodwillApplyDetailEn !=null and params.businessGoodwillApplyDetailEn != '' ">
                AND t.business_goodwill_apply_detail_en = #{params.businessGoodwillApplyDetailEn}
            </if>
                    <if test=" params.businessGoodwillApplyDetail !=null and params.businessGoodwillApplyDetail != '' ">
                AND t.business_goodwill_apply_detail = #{params.businessGoodwillApplyDetail}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMaterialAuditPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_material_audit t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.customerBackground !=null and params.customerBackground != '' ">
                AND t.customer_background = #{params.customerBackground}
            </if>
                    <if test=" params.customerBackgroundEn !=null and params.customerBackgroundEn != '' ">
                AND t.customer_background_en = #{params.customerBackgroundEn}
            </if>
                    <if test=" params.reasonAndDispose !=null and params.reasonAndDispose != '' ">
                AND t.reason_and_dispose = #{params.reasonAndDispose}
            </if>
                    <if test=" params.reasonAndDisposeEn !=null and params.reasonAndDisposeEn != '' ">
                AND t.reason_and_dispose_en = #{params.reasonAndDisposeEn}
            </if>
                    <if test=" params.repairSolution !=null and params.repairSolution != '' ">
                AND t.repair_solution = #{params.repairSolution}
            </if>
                    <if test=" params.repairSolutionEn !=null and params.repairSolutionEn != '' ">
                AND t.repair_solution_en = #{params.repairSolutionEn}
            </if>
                    <if test=" params.customerRequire !=null and params.customerRequire != '' ">
                AND t.customer_require = #{params.customerRequire}
            </if>
                    <if test=" params.customerRequireEn !=null and params.customerRequireEn != '' ">
                AND t.customer_require_en = #{params.customerRequireEn}
            </if>
                    <if test=" params.potentialRisk !=null and params.potentialRisk != '' ">
                AND t.potential_risk = #{params.potentialRisk}
            </if>
                    <if test=" params.potentialRiskEn !=null and params.potentialRiskEn != '' ">
                AND t.potential_risk_en = #{params.potentialRiskEn}
            </if>
                    <if test=" params.vrOrTjNoEn !=null and params.vrOrTjNoEn != '' ">
                AND t.vr_or_tj_no_en = #{params.vrOrTjNoEn}
            </if>
                    <if test=" params.vrOrTjNo !=null and params.vrOrTjNo != '' ">
                AND t.vr_or_tj_no = #{params.vrOrTjNo}
            </if>
                    <if test=" params.businessGoodwillApplyDetailEn !=null and params.businessGoodwillApplyDetailEn != '' ">
                AND t.business_goodwill_apply_detail_en = #{params.businessGoodwillApplyDetailEn}
            </if>
                    <if test=" params.businessGoodwillApplyDetail !=null and params.businessGoodwillApplyDetail != '' ">
                AND t.business_goodwill_apply_detail = #{params.businessGoodwillApplyDetail}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

</mapper>
