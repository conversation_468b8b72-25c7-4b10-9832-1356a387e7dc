<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.maintaininfo.InviteMaintainInfoMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.maintaininfo.InviteMaintainInfoPO">
        <id column="id" property="id"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="ro_no" property="roNo"/>
        <result column="vin" property="vin"/>
        <result column="maintain_mileage" property="maintainMileage"/>
        <result column="maintain_time" property="maintainTime"/>
        <result column="before_maintain_mileage" property="beforeMaintainMileage"/>
        <result column="before_maintain_time" property="beforeMaintainTime"/>
        <result column="diff_mileage" property="diffMileage"/>
        <result column="diff_month" property="diffMonth"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="advance_mileage" property="advanceMileage"/>
        <result column="advance_month" property="advanceMonth"/>
    </resultMap>

    <insert id="insertInviteMaintainInfo" parameterType="java.util.List">
        insert into tt_maintain_info
        (
        ro_no,
        owner_code,
        vin,
        maintain_mileage,
        maintain_time,
        before_maintain_mileage,
        before_maintain_time,
        diff_mileage,
        diff_month,
        datum_mileage,
        datum_month,
        advance_mileage,
        advance_month,
        create_by
         ) values
        <foreach collection="infoPos" item="item" index="index" separator=",">
            (
            #{item.roNo},
            #{item.ownerCode},
            #{item.vin},
            #{item.maintainMileage},
            #{item.maintainTime},
            #{item.beforeMaintainMileage},
            #{item.beforeMaintainTime},
            #{item.diffMileage},
            #{item.diffMonth},
            #{item.datumMileage},
            #{item.datumMonth},
            #{item.advanceMileage},
            #{item.advanceMonth},
            #{item.createBy}
            )
        </foreach>
        ON duplicate KEY UPDATE id = id
    </insert>

    <select id="selectMainInfoByVin" resultMap="BaseResultMap">
        select vin, advance_mileage, advance_month
        from tt_maintain_info
        where vin = #{vin}
        order by maintain_time desc limit 1
    </select>
</mapper>