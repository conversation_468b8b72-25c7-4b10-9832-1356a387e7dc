<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.complaint.ComplaintCustomFieldUseMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                                                                                <result column="app_id" property="appId"/>
                                                        <result column="owner_code" property="ownerCode"/>
                                                        <result column="owner_par_code" property="ownerParCode"/>
                                                        <result column="org_id" property="orgId"/>
                                                        <result column="custom_field_id" property="customFieldId"/>
                                                        <result column="table_name" property="tableName"/>
                                                        <result column="field_name" property="fieldName"/>
                                                        <result column="field_describe" property="fieldDescribe"/>
                                                        <result column="is_query" property="isQuery"/>
                                                        <result column="is_sort" property="isSort"/>
                                                        <result column="sort_type" property="sortType"/>
                                                        <result column="user_id" property="userId"/>
                                                        <result column="data_sources" property="dataSources"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="is_valid" property="isValid"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, custom_field_id, table_name, field_name, field_describe, is_query, is_sort, sort_type, user_id, data_sources, is_deleted, is_valid, created_at, updated_at
        </sql>



    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field_use t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.customFieldId !=null and params.customFieldId != '' ">
                AND t.custom_field_id = #{params.customFieldId}
            </if>
                    <if test=" params.tableName !=null and params.tableName != '' ">
                AND t.table_name = #{params.tableName}
            </if>
                    <if test=" params.fieldName !=null and params.fieldName != '' ">
                AND t.field_name = #{params.fieldName}
            </if>
                    <if test=" params.fieldDescribe !=null and params.fieldDescribe != '' ">
                AND t.field_describe = #{params.fieldDescribe}
            </if>
                    <if test=" params.isQuery !=null and params.isQuery != '' ">
                AND t.is_query = #{params.isQuery}
            </if>
                    <if test=" params.isSort !=null and params.isSort != '' ">
                AND t.is_sort = #{params.isSort}
            </if>
                    <if test=" params.sortType !=null and params.sortType != '' ">
                AND t.sort_type = #{params.sortType}
            </if>
                    <if test=" params.userId !=null and params.userId != '' ">
                AND t.user_id = #{params.userId}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field_use t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.customFieldId !=null and params.customFieldId != '' ">
                AND t.custom_field_id = #{params.customFieldId}
            </if>
                    <if test=" params.tableName !=null and params.tableName != '' ">
                AND t.table_name = #{params.tableName}
            </if>
                    <if test=" params.fieldName !=null and params.fieldName != '' ">
                AND t.field_name = #{params.fieldName}
            </if>
                    <if test=" params.fieldDescribe !=null and params.fieldDescribe != '' ">
                AND t.field_describe = #{params.fieldDescribe}
            </if>
                    <if test=" params.isQuery !=null and params.isQuery != '' ">
                AND t.is_query = #{params.isQuery}
            </if>
                    <if test=" params.isSort !=null and params.isSort != '' ">
                AND t.is_sort = #{params.isSort}
            </if>
                    <if test=" params.sortType !=null and params.sortType != '' ">
                AND t.sort_type = #{params.sortType}
            </if>
                    <if test=" params.userId !=null and params.userId != '' ">
                AND t.user_id = #{params.userId}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 查询自定义中ccm部位,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="queryCcmPart" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field_use t
        WHERE 1=1 and t.user_id=#{userId} and t.field_name="ccm_part" and t.is_deleted=0

    </select>

    <!-- 查询自定义中ccm细分部位,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="queryCcmSubdivisionPart" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field_use t
        WHERE 1=1 and t.user_id=#{userId}  and t.field_name="ccm_subdivision_part" and t.is_deleted=0
    </select>

    <!-- 查询自定义中CCM跟进状态,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="queryfollowStatus" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field_use t
        WHERE 1=1 and t.user_id=#{userId}  and t.field_name="follow_status" and t.is_deleted=0
    </select>
    <!-- 查询自定义中CCM主要原因,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="queryCcMainReason" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field_use t
        WHERE 1=1 and t.user_id=#{userId}  and t.field_name="cc_main_reason" and t.is_deleted=0
    </select>

    <!-- 查询自定义中CCM主要原因,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="queryccResult" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field_use t
        WHERE 1=1 and t.user_id=#{userId}  and t.field_name="cc_result" and t.is_deleted=0
    </select>
    <!-- 查询自定义中客诉单类别一阶层,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="queryCategory1" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field_use t
        WHERE 1=1 and t.user_id=#{userId}  and t.field_name="category1" and t.is_deleted=0
    </select>

    <!-- 查询自定义中客诉单类别二阶层,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="queryCategory2" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field_use t
        WHERE 1=1 and t.user_id=#{userId}  and t.field_name="category2" and t.is_deleted=0
    </select>
    <!-- 查询自定义中客诉单类别三阶层,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="queryCategory3" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field_use t
        WHERE 1=1 and t.user_id=#{userId} and t.field_name="category3" and t.is_deleted=0
    </select>
    <!-- 查询自定义中CCM分类一,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="queryClassification1" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field_use t
        WHERE 1=1 and t.user_id=#{userId}  and t.field_name="classification11" and t.is_deleted=0
    </select>
    <!-- 查询自定义中CCM分类二,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="queryClassification2" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field_use t
        WHERE 1=1 and t.user_id=#{userId}  and t.field_name="classification21" and t.is_deleted=0
    </select>

    <select id="queryClassification3" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field_use t
        WHERE 1=1 and t.user_id=#{userId} and t.field_name="classification31" and t.is_deleted=0
    </select>

    <select id="queryisValid" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field_use t
        WHERE 1=1 and t.user_id=#{userId} and t.field_name="is_valid" and t.is_deleted=0
    </select>

    <update id="updateQuery" parameterType="com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldUseDTO">
        UPDATE  tt_complaint_custom_field_use set field_describe=null ,updated_at=now() where id=#{params.id} and user_id=#{params.userId}
    </update>

    <!-- 查询5日未结案原因,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="querysmallClass2" resultMap="BaseResultMap"  >
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field_use t
        WHERE 1=1 and t.user_id=#{userId}  and t.field_name="small_class" and t.is_deleted=0
    </select>
    <!-- 查询自定义排序投诉ID,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="querysort" resultMap="BaseResultMap"  >
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field_use t
        WHERE 1=1
          and  t.user_id=#{params.userId}
        and t.table_name=#{params.tableName} and t.field_name=#{params.fieldName} and t.is_deleted=0
    </select>




    <update id="deletesort">
        UPDATE tt_complaint_custom_field_use SET  is_sort=0  ,updated_at=now(),updated_by=#{userID} where user_id=#{userID} and table_name is null or
        table_name in("tt_complaint_info","tt_complaint_dealer_ccm_ref") and field_name in("ccm_man","region_manager","type","work_order_nature","complaint_id","call_time",
        "fisrt_restart_dealer_fisrt_reply_time","subject","work_order_status","dealer_code","dealer_name","bloc","region","source","importance_level","call_name",
        "call_tel","model","model_year","config_name","license_plate_num","is_close_case","close_case_status","close_case_time","dealer_fisrt_reply_time","is_revisit",
        "vin","newest_restart_time","first_close_case_time","fisrt_restart_time","first_restart_close_case_time")
    </update>

    <update id="resetFied">
        UPDATE tt_complaint_custom_field_use SET  is_sort=0  ,updated_at=now(),updated_by=#{userId} where user_id=#{userId}  and  is_deleted=0;
    </update>
</mapper>
