<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillAuditInfoMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAuditInfoPO">
                        <id column="id" property="id"/>
                        <result column="APP_ID" property="appId"/>
                        <result column="OWNER_CODE" property="ownerCode"/>
                        <result column="OWNER_PAR_CODE" property="ownerParCode"/>
                        <result column="ORG_ID" property="orgId"/>
                        <result column="goodwill_apply_id" property="goodwillApplyId"/>
                        <result column="audit_object" property="auditObject"/>
                        <result column="audit_type" property="auditType"/>
                        <result column="audit_role" property="auditRole"/>
                        <result column="auditor" property="auditor"/>
                        <result column="audit_time" property="auditTime"/>
                        <result column="audit_result" property="auditResult"/>
                        <result column="audit_opinion" property="auditOpinion"/>
                        <result column="audit_price" property="auditPrice"/>
                        <result column="return_to" property="returnTo"/>
                        <result column="is_valid" property="isValid"/>
                        <result column="is_deleted" property="isDeleted"/>
                        <result column="created_at" property="createdAt"/>
                        <result column="updated_at" property="updatedAt"/>
                    	<result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, goodwill_apply_id, audit_object, audit_type, audit_role, auditor, audit_time, audit_result, audit_opinion, audit_price, return_to, is_valid, is_deleted, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAuditInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_audit_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.auditObject !=null and params.auditObject != '' ">
                AND t.audit_object = #{params.auditObject}
            </if>
                    <if test=" params.auditType !=null and params.auditType != '' ">
                AND t.audit_type = #{params.auditType}
            </if>
                    <if test=" params.auditRole !=null and params.auditRole != '' ">
                AND t.audit_role = #{params.auditRole}
            </if>
                    <if test=" params.auditor !=null and params.auditor != '' ">
                AND t.auditor = #{params.auditor}
            </if>
                    <if test=" params.auditTime !=null and params.auditTime != '' ">
                AND t.audit_time = #{params.auditTime}
            </if>
                    <if test=" params.auditResult !=null and params.auditResult != '' ">
                AND t.audit_result = #{params.auditResult}
            </if>
                    <if test=" params.auditOpinion !=null and params.auditOpinion != '' ">
                AND t.audit_opinion = #{params.auditOpinion}
            </if>
                    <if test=" params.auditPrice !=null and params.auditPrice != '' ">
                AND t.audit_price = #{params.auditPrice}
            </if>
                    <if test=" params.returnTo !=null and params.returnTo != '' ">
                AND t.return_to = #{params.returnTo}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAuditInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_audit_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.goodwillApplyId !=null and params.goodwillApplyId != '' ">
                AND t.goodwill_apply_id = #{params.goodwillApplyId}
            </if>
                    <if test=" params.auditObject !=null and params.auditObject != '' ">
                AND t.audit_object = #{params.auditObject}
            </if>
                    <if test=" params.auditType !=null and params.auditType != '' ">
                AND t.audit_type = #{params.auditType}
            </if>
                    <if test=" params.auditRole !=null and params.auditRole != '' ">
                AND t.audit_role = #{params.auditRole}
            </if>
                    <if test=" params.auditor !=null and params.auditor != '' ">
                AND t.auditor = #{params.auditor}
            </if>
                    <if test=" params.auditTime !=null and params.auditTime != '' ">
                AND t.audit_time = #{params.auditTime}
            </if>
                    <if test=" params.auditResult !=null and params.auditResult != '' ">
                AND t.audit_result = #{params.auditResult}
            </if>
                    <if test=" params.auditOpinion !=null and params.auditOpinion != '' ">
                AND t.audit_opinion = #{params.auditOpinion}
            </if>
                    <if test=" params.auditPrice !=null and params.auditPrice != '' ">
                AND t.audit_price = #{params.auditPrice}
            </if>
                    <if test=" params.returnTo !=null and params.returnTo != '' ">
                AND t.return_to = #{params.returnTo}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>
            
         <select id="queryApplyHistory" resultType="java.util.Map">
			select  id, goodwill_apply_id, audit_object, audit_type, audit_role, audit_name auditPerson, audit_time auditTime, audit_result auditResult, audit_opinion auditOpinion, audit_price, return_to,
				case when audit_role='SHQYJL' then '售后区域经理' when audit_role='SHDQJL' then '售后区域高级经理' 
				when audit_role='CCMQ' then 'CCMQ' 
				when audit_role='SHQYZJ' then '区域总监' when audit_role='VP' then 'VP'
				when audit_role='CEO' then 'CEO' when audit_role='CCMGJJL' then 'CCM高级经理' 
				when audit_role='CCMZJ' then 'CCM总监' when audit_role='SHQYZJ' then '区域总监'
				when audit_role='OEM-CWJL' then '财务经理' when audit_role='CFO' then 'CFO'  end as auditRole
 				from tt_goodwill_audit_info 
				where  goodwill_apply_id = #{goodwillApplyId} and audit_object=#{auditObject} order by audit_time
		</select>
		
		 <select id="queryReturnList" resultType="java.util.Map" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditInfoDTO">
			select  id, goodwill_apply_id, audit_object, audit_type, audit_role, audit_name auditPerson, audit_time auditTime, audit_result auditResult, audit_opinion auditOpinion, audit_price, return_to,
				case when audit_role='SHQYJL' then '售后区域经理' when audit_role='SHDQJL' then '售后区域高级经理' 
				when audit_role='CCMQ' then 'CCMQ' 
				when audit_role='SHQYZJ' then '区域总监' when audit_role='VP' then 'VP'
				when audit_role='CEO' then 'CEO' when audit_role='CCMGJJL' then 'CCM高级经理' 
				when audit_role='CCMZJ' then 'CCM总监' when audit_role='SHQYZJ' then '区域总监'
				when audit_role='OEM-CWJL' then '财务经理' when audit_role='CFO' then 'CFO' else audit_role end as auditRole
 				from tt_goodwill_audit_info 
				where audit_result &lt;&gt; 82801001 and  goodwill_apply_id = #{params.goodwillApplyId} and audit_object=#{params.auditObject} 
				<if test=" params.auditRole !=null and params.auditRole != '' ">
	                AND return_to = #{params.auditRole}
	            </if>
				order by audit_time
		</select>
		
		<insert id="insertAuditInfo" parameterType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditInfoDTO">
		INSERT INTO tt_goodwill_audit_info (`OWNER_CODE`,`goodwill_apply_id`,`audit_object`,`audit_type`,
		`audit_time`,`audit_result`,`audit_opinion`,`is_valid`,
		`is_deleted`,`created_at`,`created_by`,`updated_at`,`updated_by`,`record_version` 
		)VALUES	('VCDC',#{params.goodwillApplyId},#{params.auditObject},#{params.auditType},now(),#{params.auditResult},#{params.auditOpinion},10041001,0,
		now(),'-1',	now(),'-1',1 );
		</insert>

</mapper>
