<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceCustomerInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceCustomerInfoDTO">
        <id column="tiic_id" property="tiicId"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="insurance_id" property="insuranceId"/>
        <result column="insure_name" property="insureName"/>
        <result column="insure_number" property="insureNumber"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="mark" property="mark"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, tiic_id, insurance_id, insure_name, insure_number, dealer_code, data_sources, is_deleted, is_valid, created_at, updated_at, mark
        </sql>



    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectAllInsuranceCustomerInfo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_insurance_customer_info t
        WHERE 1=1
        <if test=" insuranceId !=null and insuranceId != '' ">
            AND t.insurance_id = #{insuranceId}
        </if>
        <if test=" dealerCode !=null and dealerCode != '' ">
            AND t.dealer_code = #{dealerCode}
        </if>
    </select>

    <update id="updateInsuranceCustomerInfo">
        update tt_invite_insurance_customer_info t
        set
        <if test=" params.insureName !=null and params.insureName != '' ">
            t.insure_name = #{params.insureName},
        </if>
        <if test=" params.insureNumber !=null and params.insureNumber != '' ">
            t.insure_number = #{params.insureNumber},
        </if>
            t.updated_at = now(),
            t.updated_by = #{userId}
        where
        t.tiic_id = #{params.tiicId}
    </update>

    <delete id="deleteInsuranceCustomerInfo">
        delete from tt_invite_insurance_customer_info
        where tiic_id = #{tiicId}
    </delete>

</mapper>
