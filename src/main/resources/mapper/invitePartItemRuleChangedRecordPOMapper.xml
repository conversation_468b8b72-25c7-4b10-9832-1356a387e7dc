<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.inviteRule.InvitePartItemRuleChangedRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.inviteRule.InvitePartItemRuleChangedRecordPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="type" property="type"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="remind_interval" property="remindInterval"/>
        <result column="mileage_interval" property="mileageInterval"/>
        <result column="date_interval" property="dateInterval"/>
        <result column="vin" property="vin"/>
        <result column="model_code" property="modelCode"/>
        <result column="model_year" property="modelYear"/>
        <result column="engine_code" property="engineCode"/>
        <result column="gearbox_code" property="gearboxCode"/>
        <result column="rule_relationship" property="ruleRelationship"/>
        <result column="is_use" property="isUse"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="update_is_execute" property="updateIsExecute"/>
        <result column="last_remind_interval" property="lastRemindInterval"/>
        <result column="last_mileage_interval" property="lastMileageInterval"/>
        <result column="last_date_interval" property="lastDateInterval"/>
        <result column="last_vin" property="lastVin"/>
        <result column="last_model_code" property="lastModelCode"/>
        <result column="last_model_year" property="lastModelYear"/>
        <result column="last_engine_code" property="lastEngineCode"/>
        <result column="last_gearbox_code" property="lastGearboxCode"/>
        <result column="last_rule_relationship" property="lastRuleRelationship"/>
        <result column="last_is_use" property="lastIsUse"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        app_id, owner_code, owner_par_code, org_id, id, dealer_code, type, code, name, remind_interval,
        mileage_interval, date_interval, vin, model_code, model_year, engine_code, gearbox_code, rule_relationship,
        is_use, data_sources, is_deleted, is_valid, created_at, updated_at, update_is_execute, last_remind_interval,
        last_mileage_interval, last_date_interval, last_vin, last_model_code, last_model_year, last_engine_code,
        last_gearbox_code, last_rule_relationship, last_is_use,remark
    </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteRule.InvitePartItemRuleChangedRecordPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_part_item_rule_changed_record t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.type !=null and params.type != '' ">
            AND t.type = #{params.type}
        </if>
        <if test=" params.code !=null and params.code != '' ">
            AND t.code = #{params.code}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.remindInterval !=null and params.remindInterval != '' ">
            AND t.remind_interval = #{params.remindInterval}
        </if>
        <if test=" params.mileageInterval !=null and params.mileageInterval != '' ">
            AND t.mileage_interval = #{params.mileageInterval}
        </if>
        <if test=" params.dateInterval !=null and params.dateInterval != '' ">
            AND t.date_interval = #{params.dateInterval}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.modelCode !=null and params.modelCode != '' ">
            AND t.model_code = #{params.modelCode}
        </if>
        <if test=" params.modelYear !=null and params.modelYear != '' ">
            AND t.model_year = #{params.modelYear}
        </if>
        <if test=" params.engineCode !=null and params.engineCode != '' ">
            AND t.engine_code = #{params.engineCode}
        </if>
        <if test=" params.gearboxCode !=null and params.gearboxCode != '' ">
            AND t.gearbox_code = #{params.gearboxCode}
        </if>
        <if test=" params.ruleRelationship !=null and params.ruleRelationship != '' ">
            AND t.rule_relationship = #{params.ruleRelationship}
        </if>
        <if test=" params.isUse !=null and params.isUse != '' ">
            AND t.is_use = #{params.isUse}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        <if test=" params.updateIsExecute !=null and params.updateIsExecute != '' ">
            AND t.update_is_execute = #{params.updateIsExecute}
        </if>
        <if test=" params.lastRemindInterval !=null and params.lastRemindInterval != '' ">
            AND t.last_remind_interval = #{params.lastRemindInterval}
        </if>
        <if test=" params.lastMileageInterval !=null and params.lastMileageInterval != '' ">
            AND t.last_mileage_interval = #{params.lastMileageInterval}
        </if>
        <if test=" params.lastDateInterval !=null and params.lastDateInterval != '' ">
            AND t.last_date_interval = #{params.lastDateInterval}
        </if>
        <if test=" params.lastVin !=null and params.lastVin != '' ">
            AND t.last_vin = #{params.lastVin}
        </if>
        <if test=" params.lastModelCode !=null and params.lastModelCode != '' ">
            AND t.last_model_code = #{params.lastModelCode}
        </if>
        <if test=" params.lastModelYear !=null and params.lastModelYear != '' ">
            AND t.last_model_year = #{params.lastModelYear}
        </if>
        <if test=" params.lastEngineCode !=null and params.lastEngineCode != '' ">
            AND t.last_engine_code = #{params.lastEngineCode}
        </if>
        <if test=" params.lastGearboxCode !=null and params.lastGearboxCode != '' ">
            AND t.last_gearbox_code = #{params.lastGearboxCode}
        </if>
        <if test=" params.lastRuleRelationship !=null and params.lastRuleRelationship != '' ">
            AND t.last_rule_relationship = #{params.lastRuleRelationship}
        </if>
        <if test=" params.lastIsUse !=null and params.lastIsUse != '' ">
            AND t.last_is_use = #{params.lastIsUse}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.inviteRule.InvitePartItemRuleChangedRecordPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_invite_part_item_rule_changed_record t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.type !=null and params.type != '' ">
            AND t.type = #{params.type}
        </if>
        <if test=" params.code !=null and params.code != '' ">
            AND t.code = #{params.code}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.remindInterval !=null and params.remindInterval != '' ">
            AND t.remind_interval = #{params.remindInterval}
        </if>
        <if test=" params.mileageInterval !=null and params.mileageInterval != '' ">
            AND t.mileage_interval = #{params.mileageInterval}
        </if>
        <if test=" params.dateInterval !=null and params.dateInterval != '' ">
            AND t.date_interval = #{params.dateInterval}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.modelCode !=null and params.modelCode != '' ">
            AND t.model_code = #{params.modelCode}
        </if>
        <if test=" params.modelYear !=null and params.modelYear != '' ">
            AND t.model_year = #{params.modelYear}
        </if>
        <if test=" params.engineCode !=null and params.engineCode != '' ">
            AND t.engine_code = #{params.engineCode}
        </if>
        <if test=" params.gearboxCode !=null and params.gearboxCode != '' ">
            AND t.gearbox_code = #{params.gearboxCode}
        </if>
        <if test=" params.ruleRelationship !=null and params.ruleRelationship != '' ">
            AND t.rule_relationship = #{params.ruleRelationship}
        </if>
        <if test=" params.isUse !=null and params.isUse != '' ">
            AND t.is_use = #{params.isUse}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        <if test=" params.updateIsExecute !=null and params.updateIsExecute != '' ">
            AND t.update_is_execute = #{params.updateIsExecute}
        </if>
        <if test=" params.lastRemindInterval !=null and params.lastRemindInterval != '' ">
            AND t.last_remind_interval = #{params.lastRemindInterval}
        </if>
        <if test=" params.lastMileageInterval !=null and params.lastMileageInterval != '' ">
            AND t.last_mileage_interval = #{params.lastMileageInterval}
        </if>
        <if test=" params.lastDateInterval !=null and params.lastDateInterval != '' ">
            AND t.last_date_interval = #{params.lastDateInterval}
        </if>
        <if test=" params.lastVin !=null and params.lastVin != '' ">
            AND t.last_vin = #{params.lastVin}
        </if>
        <if test=" params.lastModelCode !=null and params.lastModelCode != '' ">
            AND t.last_model_code = #{params.lastModelCode}
        </if>
        <if test=" params.lastModelYear !=null and params.lastModelYear != '' ">
            AND t.last_model_year = #{params.lastModelYear}
        </if>
        <if test=" params.lastEngineCode !=null and params.lastEngineCode != '' ">
            AND t.last_engine_code = #{params.lastEngineCode}
        </if>
        <if test=" params.lastGearboxCode !=null and params.lastGearboxCode != '' ">
            AND t.last_gearbox_code = #{params.lastGearboxCode}
        </if>
        <if test=" params.lastRuleRelationship !=null and params.lastRuleRelationship != '' ">
            AND t.last_rule_relationship = #{params.lastRuleRelationship}
        </if>
        <if test=" params.lastIsUse !=null and params.lastIsUse != '' ">
            AND t.last_is_use = #{params.lastIsUse}
        </if>
    </select>

    <select id="getUpdateNoExecute" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM tt_invite_part_item_rule_changed_record t
        where t.update_is_execute=0
        LIMIT 1
    </select>

    <update id="updateIsExecute">
        update
            tt_invite_part_item_rule_changed_record t
        set t.update_is_execute=1,
            t.remark='存在新的影响改变'
        where t.update_is_execute=0
              and t.type=#{type}
              and t.code=#{code}
    </update>

</mapper>
