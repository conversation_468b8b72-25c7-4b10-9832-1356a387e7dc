<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.complaint.ComplaintInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="complaint_id" property="complaintId"/>
        <result column="type" property="type"/>
        <result column="classification" property="classification"/>
        <result column="source" property="source"/>
        <result column="call_time" property="callTime"/>
        <result column="fisrt_restart_time" property="fisrtRestartTime"/>
        <result column="newest_restart_time" property="newestRestartTime"/>
        <result column="call_name" property="callName"/>
        <result column="sex" property="sex"/>
        <result column="work_order_status" property="workOrderStatus"/>
        <result column="is_close_case" property="isCloseCase"/>

        <result column="is_report" property="isReport"/>
        <result column="owner_address" property="ownerAddress"/>
        <result column="call_tel" property="callTel"/>
        <result column="name" property="name"/>
        <result column="license_plate_num" property="licensePlateNum"/>
        <result column="vin" property="vin"/>
        <result column="buy_time" property="buyTime"/>
        <result column="model" property="model"/>
        <result column="model_year" property="modelYear"/>
        <result column="buy_dealer_name" property="buyDealerName"/>
        <result column="dealer_name" property="dealerName"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="mileage" property="mileage"/>
        <result column="reply_tel" property="replyTel"/>
        <result column="reply_tel2" property="replyTel2"/>
        <result column="subject" property="subject"/>
        <result column="problem" property="problem"/>
        <result column="illustrate" property="illustrate"/>
        <result column="category1" property="category1"/>
        <result column="category2" property="category2"/>
        <result column="category3" property="category3"/>
        <result column="cc_part" property="ccPart"/>
        <result column="cc_subdivision_part" property="ccSubdivisionPart"/>
        <result column="part" property="part"/>
        <result column="subdivision_part" property="subdivisionPart"/>
        <result column="cc_problem" property="ccProblem"/>
        <result column="cc_requirement" property="ccRequirement"/>
        <result column="department" property="department"/>
        <result column="receptionist" property="receptionist"/>
        <result column="importance_level" property="importanceLevel"/>
        <result column="dealer_fisrt_reply_time" property="dealerFisrtReplyTime"/>
        <result column="fisrt_restart_dealer_fisrt_reply_time" property="fisrtRestartDealerFisrtReplyTime"/>
        <result column="is_revisit" property="isRevisit"/>
        <result column="revisit_time" property="revisitTime"/>
        <result column="revisit_result" property="revisitResult"/>
        <result column="revisit_content" property="revisitContent"/>
        <result column="apply_time" property="applyTime"/>
        <result column="is_agree" property="isAgree"/>
        <result column="submit_time" property="submitTime"/>
        <result column="close_case_time" property="closeCaseTime"/>
        <result column="restart_close_case_time" property="restartCloseCaseTime"/>
        <result column="close_case_status" property="closeCaseStatus"/>
        <result column="follow_status" property="followStatus"/>
        <result column="basic_reason" property="basicReason"/>
        <result column="is_repaired" property="isRepaired"/>
        <result column="tech_maintain_plan" property="techMaintainPlan"/>
        <result column="rapport_plan" property="rapportPlan"/>
        <result column="risk" property="risk"/>
        <result column="dealer_is_read" property="dealerIsRead"/>
        <result column="manager_is_read" property="managerIsRead"/>
        <result column="ccm_is_read" property="ccmIsRead"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
    </resultMap>
    <resultMap id="BaseResultMap1" type="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfMorePO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="complaint_id" property="complaintId"/>
        <result column="type" property="type"/>
        <result column="classification" property="classification"/>
        <result column="source" property="source"/>
        <result column="call_time" property="callTime"/>
        <result column="first_restart_time" property="fisrtRestartTime"/>
        <result column="newest_restart_time" property="newestRestartTime"/>
        <result column="call_name" property="callName"/>
        <result column="sex" property="sex"/>
        <result column="work_order_status" property="workOrderStatus"/>
        <result column="is_close_case" property="isCloseCase"/>
        <result column="is_report" property="isReport"/>
        <result column="owner_address" property="ownerAddress"/>
        <result column="call_tel" property="callTel"/>
        <result column="name" property="name"/>
        <result column="license_plate_num" property="licensePlateNum"/>
        <result column="vin" property="vin"/>
        <result column="buy_time" property="buyTime"/>
        <result column="model" property="model"/>
        <result column="model_year" property="modelYear"/>
        <result column="buy_dealer_name" property="buyDealerName"/>
        <result column="dealer_name" property="dealerName"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="mileage" property="mileage"/>
        <result column="reply_tel" property="replyTel"/>
        <result column="reply_tel2" property="replyTel2"/>
        <result column="subject" property="subject"/>
        <result column="problem" property="problem"/>
        <result column="illustrate" property="illustrate"/>
        <result column="category1" property="category1"/>
        <result column="category2" property="category2"/>
        <result column="category3" property="category3"/>
        <result column="cc_part" property="ccPart"/>
        <result column="cc_subdivision_part" property="ccSubdivisionPart"/>
        <result column="part" property="part"/>
        <result column="subdivision_part" property="subdivisionPart"/>
        <result column="cc_problem" property="ccProblem"/>
        <result column="cc_requirement" property="ccRequirement"/>
        <result column="department" property="department"/>
        <result column="receptionist" property="receptionist"/>
        <result column="importance_level" property="importanceLevel"/>
        <result column="dealer_fisrt_reply_time" property="dealerFisrtReplyTime"/>
        <result column="fisrt_restart_dealer_fisrt_reply_time" property="fisrtRestartDealerFisrtReplyTime"/>
        <result column="is_revisit" property="isRevisit"/>
        <result column="revisit_time" property="revisitTime"/>
        <result column="revisit_result" property="revisitResult"/>
        <result column="revisit_content" property="revisitContent"/>
        <result column="apply_time" property="applyTime"/>
        <result column="is_agree" property="isAgree"/>
        <result column="submit_time" property="submitTime"/>
        <result column="close_case_time" property="closeCaseTime"/>
        <result column="restart_close_case_time" property="restartCloseCaseTime"/>
        <result column="close_case_status" property="closeCaseStatus"/>
        <result column="follow_status" property="followStatus"/>
        <result column="basic_reason" property="basicReason"/>
        <result column="is_repaired" property="isRepaired"/>
        <result column="tech_maintain_plan" property="techMaintainPlan"/>
        <result column="rapport_plan" property="rapportPlan"/>
        <result column="risk" property="risk"/>
        <result column="dealer_is_read" property="dealerIsRead"/>
        <result column="manager_is_read" property="managerIsRead"/>
        <result column="ccm_is_read" property="ccmIsRead"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="region" property="region"/>
        <result column="region_manager" property="regionManager"/>
        <result column="is_satisfied" property="isSatisfied"/>
        <result column="work_order_nature" property="workOrderNature"/>
        <result column="work_order_classification" property="workOrderClassification"/>
        <result column="reply_name" property="replyName"/>
        <result column="service_commitment" property="serviceCommitment"/>
        <result column="ccm_system_assisted_processing" property="ccmSystemAssistedProcessing"/>
        <result column="ccm_man" property="ccmMan"/>
        <result column="first_close_case_time" property="firstCloseCaseTime" />
        <result column="first_restart_close_case_time" property="firstRestartCloseCaseTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code,work_order_status,is_close_case, org_id, id,is_report, complaint_id, type, classification, source, call_time,owner_address, first_restart_time as fisrt_restart_time, newest_restart_time, call_name,sex, call_tel, name, license_plate_num, vin, buy_time, model, model_year, buy_dealer_name, dealer_name, dealer_code, mileage, reply_tel, reply_tel2, subject, problem, illustrate, category1, category2, category3, part,subdivision_part,cc_part, cc_subdivision_part, cc_problem, cc_requirement, department, receptionist, importance_level, dealer_fisrt_reply_time, fisrt_restart_dealer_fisrt_reply_time, is_revisit, revisit_time, revisit_result, revisit_content, apply_time, is_agree, submit_time, close_case_time, restart_close_case_time, close_case_status, follow_status, basic_reason, is_repaired, tech_maintain_plan, rapport_plan, risk, dealer_is_read, manager_is_read, ccm_is_read, data_sources, is_deleted, is_valid, created_at, updated_at
        </sql>
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_info t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.complaintId !=null and params.complaintId != '' ">
            AND t.complaint_id = #{params.complaintId}
        </if>
        <if test=" params.type !=null and params.type != '' ">
            AND t.type = #{params.type}
        </if>
        <if test=" params.classification !=null and params.classification != '' ">
            AND t.classification = #{params.classification}
        </if>
        <if test=" params.source !=null and params.source != '' ">
            AND t.source = #{params.source}
        </if>
        <if test=" params.callTime !=null and params.callTime != '' ">
            AND t.call_time = #{params.callTime}
        </if>
        <if test=" params.fisrtRestartTime !=null and params.fisrtRestartTime != '' ">
            AND t.first_restart_time = #{params.fisrtRestartTime}
        </if>
        <if test=" params.newestRestartTime !=null and params.newestRestartTime != '' ">
            AND t.newest_restart_time = #{params.newestRestartTime}
        </if>
        <if test=" params.callName !=null and params.callName != '' ">
            AND t.call_name = #{params.callName}
        </if>
        <if test=" params.callTel !=null and params.callTel != '' ">
            AND t.call_tel = #{params.callTel}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num = #{params.licensePlateNum}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.buyTime !=null and params.buyTime != '' ">
            AND t.buy_time = #{params.buyTime}
        </if>
        <if test=" params.model !=null and params.model != '' ">
            AND t.model = #{params.model}
        </if>
        <if test=" params.modelYear !=null and params.modelYear != '' ">
            AND t.model_year = #{params.modelYear}
        </if>
        <if test=" params.buyDealerName !=null and params.buyDealerName != '' ">
            AND t.buy_dealer_name = #{params.buyDealerName}
        </if>
        <if test=" params.dealerName !=null and params.dealerName != '' ">
            AND t.dealer_name = #{params.dealerName}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.mileage !=null and params.mileage != '' ">
            AND t.mileage = #{params.mileage}
        </if>
        <if test=" params.replyTel !=null and params.replyTel != '' ">
            AND t.reply_tel = #{params.replyTel}
        </if>
        <if test=" params.replyTel2 !=null and params.replyTel2 != '' ">
            AND t.reply_tel2 = #{params.replyTel2}
        </if>
        <if test=" params.subject !=null and params.subject != '' ">
            AND t.subject = #{params.subject}
        </if>
        <if test=" params.problem !=null and params.problem != '' ">
            AND t.problem = #{params.problem}
        </if>
        <if test=" params.illustrate !=null and params.illustrate != '' ">
            AND t.illustrate = #{params.illustrate}
        </if>
        <if test=" params.category1 !=null and params.category1 != '' ">
            AND t.category1 = #{params.category1}
        </if>
        <if test=" params.category2 !=null and params.category2 != '' ">
            AND t.category2 = #{params.category2}
        </if>
        <if test=" params.category3 !=null and params.category3 != '' ">
            AND t.category3 = #{params.category3}
        </if>
        <if test=" params.ccPart !=null and params.ccPart != '' ">
            AND t.cc_part = #{params.ccPart}
        </if>
        <if test=" params.ccSubdivisionPart !=null and params.ccSubdivisionPart != '' ">
            AND t.cc_subdivision_part = #{params.ccSubdivisionPart}
        </if>
        <if test=" params.ccProblem !=null and params.ccProblem != '' ">
            AND t.cc_problem = #{params.ccProblem}
        </if>
        <if test=" params.ccRequirement !=null and params.ccRequirement != '' ">
            AND t.cc_requirement = #{params.ccRequirement}
        </if>
        <if test=" params.department !=null and params.department != '' ">
            AND t.department = #{params.department}
        </if>
        <if test=" params.receptionist !=null and params.receptionist != '' ">
            AND t.receptionist = #{params.receptionist}
        </if>
        <if test=" params.importanceLevel !=null and params.importanceLevel != '' ">
            AND t.importance_level = #{params.importanceLevel}
        </if>
        <if test=" params.dealerFisrtReplyTime !=null and params.dealerFisrtReplyTime != '' ">
            AND t.dealer_fisrt_reply_time = #{params.dealerFisrtReplyTime}
        </if>
        <if test=" params.fisrtRestartDealerFisrtReplyTime !=null and params.fisrtRestartDealerFisrtReplyTime != '' ">
            AND t.fisrt_restart_dealer_fisrt_reply_time = #{params.fisrtRestartDealerFisrtReplyTime}
        </if>
        <if test=" params.isRevisit !=null and params.isRevisit != '' ">
            AND t.is_revisit = #{params.isRevisit}
        </if>
        <if test=" params.revisitTime !=null and params.revisitTime != '' ">
            AND t.revisit_time = #{params.revisitTime}
        </if>
        <if test=" params.revisitResult !=null and params.revisitResult != '' ">
            AND t.revisit_result = #{params.revisitResult}
        </if>
        <if test=" params.revisitContent !=null and params.revisitContent != '' ">
            AND t.revisit_content = #{params.revisitContent}
        </if>
        <if test=" params.applyTime !=null and params.applyTime != '' ">
            AND t.apply_time = #{params.applyTime}
        </if>
        <if test=" params.isAgree !=null and params.isAgree != '' ">
            AND t.is_agree = #{params.isAgree}
        </if>
        <if test=" params.submitTime !=null and params.submitTime != '' ">
            AND t.submit_time = #{params.submitTime}
        </if>
        <if test=" params.closeCaseTime !=null and params.closeCaseTime != '' ">
            AND t.close_case_time = #{params.closeCaseTime}
        </if>
        <if test=" params.restartCloseCaseTime !=null and params.restartCloseCaseTime != '' ">
            AND t.restart_close_case_time = #{params.restartCloseCaseTime}
        </if>
        <if test=" params.closeCaseStatus !=null and params.closeCaseStatus != '' ">
            AND t.close_case_status = #{params.closeCaseStatus}
        </if>
        <if test=" params.followStatus !=null and params.followStatus != '' ">
            AND t.follow_status = #{params.followStatus}
        </if>
        <if test=" params.basicReason !=null and params.basicReason != '' ">
            AND t.basic_reason = #{params.basicReason}
        </if>
        <if test=" params.isRepaired !=null and params.isRepaired != '' ">
            AND t.is_repaired = #{params.isRepaired}
        </if>
        <if test=" params.techMaintainPlan !=null and params.techMaintainPlan != '' ">
            AND t.tech_maintain_plan = #{params.techMaintainPlan}
        </if>
        <if test=" params.rapportPlan !=null and params.rapportPlan != '' ">
            AND t.rapport_plan = #{params.rapportPlan}
        </if>
        <if test=" params.risk !=null and params.risk != '' ">
            AND t.risk = #{params.risk}
        </if>
        <if test=" params.dealerIsRead !=null and params.dealerIsRead != '' ">
            AND t.dealer_is_read = #{params.dealerIsRead}
        </if>
        <if test=" params.managerIsRead !=null and params.managerIsRead != '' ">
            AND t.manager_is_read = #{params.managerIsRead}
        </if>
        <if test=" params.ccmIsRead !=null and params.ccmIsRead != '' ">
            AND t.ccm_is_read = #{params.ccmIsRead}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_info t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.complaintId !=null and params.complaintId != '' ">
            AND t.complaint_id = #{params.complaintId}
        </if>
        <if test=" params.type !=null and params.type != '' ">
            AND t.type = #{params.type}
        </if>
        <if test=" params.classification !=null and params.classification != '' ">
            AND t.classification = #{params.classification}
        </if>
        <if test=" params.source !=null and params.source != '' ">
            AND t.source = #{params.source}
        </if>
        <if test=" params.callTime !=null and params.callTime != '' ">
            AND t.call_time = #{params.callTime}
        </if>
        <if test=" params.fisrtRestartTime !=null and params.fisrtRestartTime != '' ">
            AND t.first_restart_time = #{params.fisrtRestartTime}
        </if>
        <if test=" params.newestRestartTime !=null and params.newestRestartTime != '' ">
            AND t.newest_restart_time = #{params.newestRestartTime}
        </if>
        <if test=" params.callName !=null and params.callName != '' ">
            AND t.call_name = #{params.callName}
        </if>
        <if test=" params.callTel !=null and params.callTel != '' ">
            AND t.call_tel = #{params.callTel}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num = #{params.licensePlateNum}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.buyTime !=null and params.buyTime != '' ">
            AND t.buy_time = #{params.buyTime}
        </if>
        <if test=" params.model !=null and params.model != '' ">
            AND t.model = #{params.model}
        </if>
        <if test=" params.modelYear !=null and params.modelYear != '' ">
            AND t.model_year = #{params.modelYear}
        </if>
        <if test=" params.buyDealerName !=null and params.buyDealerName != '' ">
            AND t.buy_dealer_name = #{params.buyDealerName}
        </if>
        <if test=" params.dealerName !=null and params.dealerName != '' ">
            AND t.dealer_name = #{params.dealerName}
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code = #{params.dealerCode}
        </if>
        <if test=" params.mileage !=null and params.mileage != '' ">
            AND t.mileage = #{params.mileage}
        </if>
        <if test=" params.replyTel !=null and params.replyTel != '' ">
            AND t.reply_tel = #{params.replyTel}
        </if>
        <if test=" params.replyTel2 !=null and params.replyTel2 != '' ">
            AND t.reply_tel2 = #{params.replyTel2}
        </if>
        <if test=" params.subject !=null and params.subject != '' ">
            AND t.subject = #{params.subject}
        </if>
        <if test=" params.problem !=null and params.problem != '' ">
            AND t.problem = #{params.problem}
        </if>
        <if test=" params.illustrate !=null and params.illustrate != '' ">
            AND t.illustrate = #{params.illustrate}
        </if>
        <if test=" params.category1 !=null and params.category1 != '' ">
            AND t.category1 = #{params.category1}
        </if>
        <if test=" params.category2 !=null and params.category2 != '' ">
            AND t.category2 = #{params.category2}
        </if>
        <if test=" params.category3 !=null and params.category3 != '' ">
            AND t.category3 = #{params.category3}
        </if>
        <if test=" params.ccPart !=null and params.ccPart != '' ">
            AND t.cc_part = #{params.ccPart}
        </if>
        <if test=" params.ccSubdivisionPart !=null and params.ccSubdivisionPart != '' ">
            AND t.cc_subdivision_part = #{params.ccSubdivisionPart}
        </if>
        <if test=" params.ccProblem !=null and params.ccProblem != '' ">
            AND t.cc_problem = #{params.ccProblem}
        </if>
        <if test=" params.ccRequirement !=null and params.ccRequirement != '' ">
            AND t.cc_requirement = #{params.ccRequirement}
        </if>
        <if test=" params.department !=null and params.department != '' ">
            AND t.department = #{params.department}
        </if>
        <if test=" params.receptionist !=null and params.receptionist != '' ">
            AND t.receptionist = #{params.receptionist}
        </if>
        <if test=" params.importanceLevel !=null and params.importanceLevel != '' ">
            AND t.importance_level = #{params.importanceLevel}
        </if>
        <if test=" params.dealerFisrtReplyTime !=null and params.dealerFisrtReplyTime != '' ">
            AND t.dealer_fisrt_reply_time = #{params.dealerFisrtReplyTime}
        </if>
        <if test=" params.fisrtRestartDealerFisrtReplyTime !=null and params.fisrtRestartDealerFisrtReplyTime != '' ">
            AND t.fisrt_restart_dealer_fisrt_reply_time = #{params.fisrtRestartDealerFisrtReplyTime}
        </if>
        <if test=" params.isRevisit !=null and params.isRevisit != '' ">
            AND t.is_revisit = #{params.isRevisit}
        </if>
        <if test=" params.revisitTime !=null and params.revisitTime != '' ">
            AND t.revisit_time = #{params.revisitTime}
        </if>
        <if test=" params.revisitResult !=null and params.revisitResult != '' ">
            AND t.revisit_result = #{params.revisitResult}
        </if>
        <if test=" params.revisitContent !=null and params.revisitContent != '' ">
            AND t.revisit_content = #{params.revisitContent}
        </if>
        <if test=" params.applyTime !=null and params.applyTime != '' ">
            AND t.apply_time = #{params.applyTime}
        </if>
        <if test=" params.isAgree !=null and params.isAgree != '' ">
            AND t.is_agree = #{params.isAgree}
        </if>
        <if test=" params.submitTime !=null and params.submitTime != '' ">
            AND t.submit_time = #{params.submitTime}
        </if>
        <if test=" params.closeCaseTime !=null and params.closeCaseTime != '' ">
            AND t.close_case_time = #{params.closeCaseTime}
        </if>
        <if test=" params.restartCloseCaseTime !=null and params.restartCloseCaseTime != '' ">
            AND t.restart_close_case_time = #{params.restartCloseCaseTime}
        </if>
        <if test=" params.closeCaseStatus !=null and params.closeCaseStatus != '' ">
            AND t.close_case_status = #{params.closeCaseStatus}
        </if>
        <if test=" params.followStatus !=null and params.followStatus != '' ">
            AND t.follow_status = #{params.followStatus}
        </if>
        <if test=" params.basicReason !=null and params.basicReason != '' ">
            AND t.basic_reason = #{params.basicReason}
        </if>
        <if test=" params.isRepaired !=null and params.isRepaired != '' ">
            AND t.is_repaired = #{params.isRepaired}
        </if>
        <if test=" params.techMaintainPlan !=null and params.techMaintainPlan != '' ">
            AND t.tech_maintain_plan = #{params.techMaintainPlan}
        </if>
        <if test=" params.rapportPlan !=null and params.rapportPlan != '' ">
            AND t.rapport_plan = #{params.rapportPlan}
        </if>
        <if test=" params.risk !=null and params.risk != '' ">
            AND t.risk = #{params.risk}
        </if>
        <if test=" params.dealerIsRead !=null and params.dealerIsRead != '' ">
            AND t.dealer_is_read = #{params.dealerIsRead}
        </if>
        <if test=" params.managerIsRead !=null and params.managerIsRead != '' ">
            AND t.manager_is_read = #{params.managerIsRead}
        </if>
        <if test=" params.ccmIsRead !=null and params.ccmIsRead != '' ">
            AND t.ccm_is_read = #{params.ccmIsRead}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        and t.is_deleted=0
    </select>

    <select id="queryid" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoPO">
        SELECT
        id,complaint_id
        FROM tt_complaint_info t
        WHERE 1=1
        <if test=" params.complaintId !=null and params.complaintId != '' ">
            AND t.complaint_id = #{params.complaintId}
        </if>
        and t.is_deleted=0

    </select>

    <select id="selectCusByDeal" resultMap="BaseResultMap1"
            parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfMorePO">
        SELECT
        DISTINCT t.id,
        t.call_time,
        t.inform_time,
        t2.ccm_man AS ccm_man,
        t.region_manager AS handleRegionManager,
        t.type,
        t.work_order_nature,
        t.complaint_id,
        t.fisrt_restart_dealer_fisrt_reply_time,
        t.SUBJECT,
        t.work_order_status,
        t.dealer_code,
        t.dealer_name AS handleDealName,
        t.bloc AS handleBloc,
        t.region AS handleRegion,
        t1.after_small_area_name AS area_id,
        t.source,
        importance_level,
        t.call_name,
        t.call_tel,
        t.model,
        model_year,
        t.config_name,
        t.license_plate_num,
        t.is_close_case,
        t.close_case_status,
        t.close_case_time,
        t.dealer_fisrt_reply_time,
        t.is_revisit,
        t.vin,
        t.data_sources,
        t.first_restart_time,
        t.first_close_case_time,
        t.first_restart_close_case_time,
        <if test=" params.assitdep !=null and params.assitdep != ''  ">
            t5.is_valid AS is_valid,
        </if>
        <if test=" params.assisDepartment !=null and params.assisDepartment != ''  ">
            t5.is_valid AS is_valid,
        </if>
        t.newest_restart_time,
        CASE
        WHEN MODEL is not null and MODEL = #{model} THEN 0
        ELSE 1
        END md,
        CASE
        WHEN is_close_case is not null and is_close_case = 10041002 THEN 0
        ELSE 1
        END ccs
        FROM tt_complaint_info t
        INNER JOIN tt_complaint_info_extend t1 on t.id=t1.complaint_info_id
        LEFT JOIN tt_complaint_dealer_ccm_ref t2 on t.dealer_code=t2.dealer_code
        LEFT join tt_complaint_follow t3 on t.id=t3.complaint_info_id
        LEFT join tt_complaint_not_close_case_reason t4 on t.id=t4.complaint_info_id
        LEFT JOIN tt_complaint_assist_department t5 on t.id=t5.complaint_info_id
        <if test=" params.assitdep !=null and params.assitdep != ''  ">
            AND
            (t5.assist_dealer_code=#{params.assitdep}or t5.assist_department=#{params.assitdep})
        </if>
        <if test=" params.assisDepartment !=null and params.assisDepartment != ''  ">
             and  t5.assist_department=#{params.assisDepartment}
        </if>
        WHERE 1=1
        AND t.work_order_status != 82451006
        <if test=" params.workOrderNature !=null and params.workOrderNature != '' ">
            AND t.work_order_nature = #{params.workOrderNature}
        </if>
        <if test=" params.isValid1 !=null and params.isValid1 != ''  ">
            and   t5.is_valid =#{params.isValid1}
        </if>
        <if test=" params.smallClass !=null and params.smallClass != ''  ">
            and (${params.smallClass})
        </if>
        <if test=" params.assisDepartment !=null and params.assisDepartment != ''  ">
            and t.id in (select complaint_info_id from tt_complaint_assist_department where assist_department
            =#{params.assisDepartment} )
        </if>

        <if test=" params.classification3 !=null and params.classification3 != ''  ">
            AND t3.classification3 like "%" #{params.classification3} "%"
        </if>
        <if test=" params.classification21 !=null and params.classification21 != ''  ">
            AND (${params.classification21})
        </if>
        <if test=" params.ccResult !=null and params.ccResult != ''  ">
            AND (${params.ccResult})
        </if>
        <if test=" params.classification11 !=null and params.classification11 != ''  ">
            AND (${params.classification11})
        </if>
        <if test=" params.ccmSubdivisionPart !=null and params.ccmSubdivisionPart != ''  ">
            AND (${params.ccmSubdivisionPart})
        </if>
        <if test=" params.ccmPart !=null and params.ccmPart != '' ">
            AND (${params.ccmPart})
        </if>
        <if test=" params.ccMainReason and params.ccMainReason != ''  ">
            AND (${params.ccMainReason})
        </if>
        <if test=" params.planFollowTime1 !=null ">
            AND t3.plan_follow_time >= #{params.planFollowTime1}
        </if>
        <if test=" params.planFollowTime2 !=null ">
            AND t3.plan_follow_time &lt;= #{params.planFollowTime2}
        </if>
        <if test=" params.actuallFollowTime !=null ">
            AND t3.actuall_follow_time2 >= #{params.actuallFollowTime}
        </if>
        <if test=" params.actuallFollowTime1 !=null ">
            AND t3.actuall_follow_time2 &lt;= #{params.actuallFollowTime1}
        </if>
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.complaintId !=null and params.complaintId != '' ">
            AND t.complaint_id  like "%" #{params.complaintId} "%"
        </if>
        <if test=" params.type !=null and params.type != '' ">
            AND t.type in (${params.type})
        </if>
        <if test=" params.classification !=null and params.classification != '' ">
            AND t.classification = #{params.classification}
        </if>
        <if test=" params.source !=null and params.source != '' ">
            AND t.source in (${params.source})
        </if>
        <if test=" params.source !=null and params.source != '' ">
            AND t.source in (${params.source})
        </if>
        <if test=" params.callTime !=null ">
            AND t.inform_time >= #{params.callTime}
        </if>
        <if test=" params.callTime2 !=null ">
            AND t.inform_time &lt;= #{params.callTime2}
        </if>
        <if test=" params.closeCaseTime !=null ">
            AND t.close_case_time >= #{params.closeCaseTime}
        </if>
        <if test=" params.closeCaseTime2 !=null ">
            AND t.close_case_time &lt;= #{params.closeCaseTime2}
        </if>
        <if test=" params.firstCloseCaseTime !=null ">
            AND t.first_close_case_time &gt;= #{params.firstCloseCaseTime}
        </if>
        <if test=" params.firstCloseCaseTime2 !=null ">
            AND t.first_close_case_time &lt;= #{params.firstCloseCaseTime2}
        </if>
        <if test=" params.firstRestartCloseCaseTime !=null ">
            AND t.first_restart_close_case_time &gt;= #{params.firstRestartCloseCaseTime}
        </if>
        <if test=" params.firstRestartCloseCaseTime2 !=null ">
            AND t.first_restart_close_case_time &lt;= #{params.firstRestartCloseCaseTime2}
        </if>
        <if test=" params.fisrtRestartTime !=null  ">
            AND t.first_restart_time >= #{params.fisrtRestartTime}
        </if>
        <if test=" params.fisrtRestartTime2 !=null  ">
            AND t.first_restart_time &lt;= #{params.fisrtRestartTime2}
        </if>
        <if test=" params.newestRestartTime !=null and params.newestRestartTime != '' ">
            AND t.newest_restart_time = #{params.newestRestartTime}
        </if>
        <if test=" params.callName !=null and params.callName != '' ">
            AND t.call_name like "%"  #{params.callName} "%"
        </if>
        <if test=" params.callTel !=null and params.callTel != '' ">
            AND t.call_tel = #{params.callTel}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name  like "%" #{params.name} "%"
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num like "%"  #{params.licensePlateNum} "%"
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin like "%"  #{params.vin} "%"
        </if>
        <if test=" params.buyTime !=null and params.buyTime != '' ">
            AND t.buy_time = #{params.buyTime}
        </if>
        <if test=" params.model !=null and params.model != '' ">
            AND t.model in(${params.model})
        </if>
        <if test=" params.blocId !=null and params.blocId != '' ">
            AND t.bloc_id in(${params.blocId})
        </if>
        <if test=" params.regionId !=null and params.regionId != '' ">
            AND t.region_id in(${params.regionId})
        </if>
        <if test=" params.areaId !=null and params.areaId != '' ">
            AND t.region_manager_id in(${params.areaId})
        </if>
        <if test=" params.regionManagerId !=null and params.regionManagerId != '' ">
            AND t.region_manager in(${params.regionManagerId})
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code in(${params.dealerCode})
        </if>
        <if test=" params.ccmManId !=null and params.ccmManId != '' ">
            AND t.dealer_code in(select dealer_code from tt_complaint_dealer_ccm_ref where ccm_man_id in
            (${params.ccmManId}) )
        </if>
        <if test=" params.modelYear !=null and params.modelYear != '' ">
            AND t.model_year like "%"  #{params.modelYear} "%"
        </if>
        <if test=" params.buyDealerName !=null and params.buyDealerName != '' ">
            AND t.buy_dealer_name = #{params.buyDealerName}
        </if>
        <if test=" params.dealerName !=null and params.dealerName != '' ">
            AND t.dealer_name = #{params.dealerName}
        </if>
        <if test=" params.mileage !=null and params.mileage != '' ">
            AND t.mileage = #{params.mileage}
        </if>
        <if test=" params.replyTel !=null and params.replyTel != '' ">
            AND t.reply_tel = #{params.replyTel}
        </if>
        <if test=" params.replyTel2 !=null and params.replyTel2 != '' ">
            AND t.reply_tel2 = #{params.replyTel2}
        </if>
        <if test=" params.subject !=null and params.subject != '' ">
            AND t.subject like "%" #{params.subject} "%"
        </if>
        <if test=" params.problem !=null and params.problem != '' ">
            AND t.problem = #{params.problem}
        </if>
        <if test=" params.illustrate !=null and params.illustrate != '' ">
            AND t.illustrate = #{params.illustrate}
        </if>
        <if test=" params.category1 !=null and params.category2 != '' ">
            AND (${params.category1})
        </if>
        <if test=" params.category2 !=null and params.category2 != '' ">
            AND (${params.category2})
        </if>
        <if test=" params.category3 !=null and params.category3 != '' ">
            AND (${params.category3})
        </if>
        <if test=" params.ccPart !=null and params.ccPart != '' ">
            AND t.cc_part = #{params.ccPart}
        </if>
        <if test=" params.ccSubdivisionPart !=null and params.ccSubdivisionPart != '' ">
            AND t.cc_subdivision_part = #{params.ccSubdivisionPart}
        </if>
        <if test=" params.ccProblem !=null and params.ccProblem != '' ">
            AND t.cc_problem = #{params.ccProblem}
        </if>
        <if test=" params.ccRequirement !=null and params.ccRequirement != '' ">
            AND t.cc_requirement = #{params.ccRequirement}
        </if>
        <if test=" params.department !=null and params.department != '' ">
            AND t.department = #{params.department}
        </if>
        <if test=" params.receptionist !=null and params.receptionist != '' ">
            AND t.receptionist = #{params.receptionist}
        </if>
        <if test=" params.importanceLevel !=null and params.importanceLevel != '' ">
            AND t.importance_level = #{params.importanceLevel}
        </if>
        <if test=" params.dealerFisrtReplyTime !=null and params.dealerFisrtReplyTime != '' ">
            AND t.dealer_fisrt_reply_time = #{params.dealerFisrtReplyTime}
        </if>
        <if test=" params.fisrtRestartDealerFisrtReplyTime !=null and params.fisrtRestartDealerFisrtReplyTime != '' ">
            AND t.fisrt_restart_dealer_fisrt_reply_time = #{params.fisrtRestartDealerFisrtReplyTime}
        </if>
        <if test=" params.isRevisit !=null and params.isRevisit != '' ">
            AND t.is_revisit = #{params.isRevisit}
        </if>
        <if test=" params.revisitTime !=null and params.revisitTime != '' ">
            AND t.revisit_time = #{params.revisitTime}
        </if>
        <if test=" params.revisitResult !=null and params.revisitResult != '' ">
            AND t.revisit_result = #{params.revisitResult}
        </if>
        <if test=" params.revisitContent !=null and params.revisitContent != '' ">
            AND t.revisit_content = #{params.revisitContent}
        </if>
        <if test=" params.applyTime !=null and params.applyTime != '' ">
            AND t.apply_time = #{params.applyTime}
        </if>
        <if test=" params.isAgree !=null and params.isAgree != '' ">
            AND t.is_agree = #{params.isAgree}
        </if>
        <if test=" params.submitTime !=null and params.submitTime != '' ">
            AND t.submit_time = #{params.submitTime}
        </if>
        <if test=" params.restartCloseCaseTime !=null and params.restartCloseCaseTime != '' ">
            AND t.restart_close_case_time = #{params.restartCloseCaseTime}
        </if>
        <if test=" params.closeCaseStatus !=null and params.closeCaseStatus != '' ">
            AND t.close_case_status = #{params.closeCaseStatus}
        </if>
        <if test=" params.followStatus !=null and params.followStatus != '' ">
            AND ${params.followStatus}
        </if>
        <if test=" params.basicReason !=null and params.basicReason != '' ">
            AND t.basic_reason = #{params.basicReason}
        </if>
        <if test=" params.isRepaired !=null and params.isRepaired != '' ">
            AND t.is_repaired = #{params.isRepaired}
        </if>
        <if test=" params.techMaintainPlan !=null and params.techMaintainPlan != '' ">
            AND t.tech_maintain_plan = #{params.techMaintainPlan}
        </if>
        <if test=" params.rapportPlan !=null and params.rapportPlan != '' ">
            AND t.rapport_plan = #{params.rapportPlan}
        </if>
        <if test=" params.risk !=null and params.risk != '' ">
            AND t.risk = #{params.risk}
        </if>
        <if test=" params.workOrderStatus !=null and params.workOrderStatus != '' ">
            AND t.work_order_status = #{params.workOrderStatus}
        </if>

        <if test=" params.dealerIsRead !=null and params.dealerIsRead != '' ">
            AND t.dealer_is_read = #{params.dealerIsRead}
        </if>
        <if test=" params.managerIsRead !=null and params.managerIsRead != '' ">
            AND t.manager_is_read = #{params.managerIsRead}
        </if>
        <if test=" params.ccmIsRead !=null and params.ccmIsRead != '' ">
            AND t.ccm_is_read = #{params.ccmIsRead}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isCloseCase !=null and params.isCloseCase != '' ">
            AND t.is_close_case = #{params.isCloseCase}
        </if>
        <if test=" params.isReport !=null ">
            AND t.is_report = #{params.isReport}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
        <if test=" params.invalidCaseHidden !=null and params.invalidCaseHidden != '' and params.invalidCaseHidden == 10041001">
            and t.work_order_status != 82451006
        </if>
        <if test=" params.sql1 !=null and params.sql1 != '' ">
            and ${params.sql1}
        </if>
        and t.is_deleted=0
        <if test=" params.sql !=null and params.sql != '' ">
            ${params.sql}
        </if>
    </select>
    <select id="selectCusByDealAll" resultMap="BaseResultMap1"
            parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfMorePO">
        SELECT
        distinct t.id, t.app_id, t.owner_code,t.work_order_status,t.is_close_case, t.owner_par_code,
        (select abs(UNIX_TIMESTAMP(t3.plan_follow_time)-UNIX_TIMESTAMP(NOW())) from
        tt_complaint_follow t3 WHERE t.id=t3.complaint_info_id and t3.plan_follow_time is not null ORDER BY
        t3.follow_time desc LIMIT 1
        ) as nextPlanFollowTime,
        t.is_satisfied,
        t.ccm_system_assisted_processing,
        t.work_order_nature,t.work_order_classification,t.reply_name,t.service_commitment,
        t.org_id,t.is_report, t.complaint_id, t.type, t.classification, t.source, t.call_time,
        t.owner_address, t.first_restart_time, t.newest_restart_time, t.call_name,sex, t.call_tel,
        t.name, t.license_plate_num, t.vin, t.buy_time, t.model, t.model_year, t.buy_dealer_name,
        t.dealer_code, t.mileage, t.reply_tel, t.reply_tel2, t.subject, t.problem, t.illustrate,
        t.category1, t.category2, t.category3, t.part,t.subdivision_part,t.cc_part, t.cc_subdivision_part,
        t.cc_problem, t.cc_requirement, t.department, t.receptionist, t.importance_level, t.dealer_fisrt_reply_time,
        t.fisrt_restart_dealer_fisrt_reply_time, t.is_revisit, t.revisit_time, t.revisit_result, t.revisit_content,
        t.apply_time, t.is_agree, t.submit_time, t.close_case_time, t.restart_close_case_time, t.close_case_status,
        t.follow_status, t.basic_reason, t.is_repaired, t.tech_maintain_plan, t.rapport_plan, t.risk, t.dealer_is_read,
        t.manager_is_read, t.ccm_is_read, t.data_sources, t.is_deleted, t.created_at, t.updated_at
        ,t2.region AS region,t2.region_manager AS region_manager,t2.bloc AS bloc,t2.dealer_name AS dealer_name,
        (SELECT t3.quality_classification1 from tt_complaint_follow t3 where t.id=t3.complaint_info_id
        and t3.quality_classification1 is not null ORDER BY
        t3.follow_time desc LIMIT 1) AS quality_classification1,
        (SELECT t3.quality_classification2 from tt_complaint_follow t3 where t.id=t3.complaint_info_id
        and t3.quality_classification2 is not null ORDER BY
        t3.follow_time desc LIMIT 1) AS quality_classification2,
        (SELECT t3.quality_classification3 from tt_complaint_follow t3 where t.id=t3.complaint_info_id
        and t3.quality_classification3 is not null ORDER BY
        t3.follow_time desc LIMIT 1) AS quality_classification3,
        (SELECT t3.quality_classification4 from tt_complaint_follow t3 where t.id=t3.complaint_info_id
        and t3.quality_classification4 is not null ORDER BY
        t3.follow_time desc LIMIT 1) AS quality_classification4,
        (SELECT t3.fault_classification from tt_complaint_follow t3 where t.id=t3.complaint_info_id
        and t3.fault_classification is not null ORDER BY
        t3.follow_time desc LIMIT 1) AS fault_classification,
        (SELECT t3.remark1 from tt_complaint_follow t3 where t.id=t3.complaint_info_id  and t3.remark1 is not null ORDER BY t3.follow_time desc
        LIMIT 1) AS remark1,
        (SELECT t3.remark2 from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.remark2 is not null ORDER BY t3.follow_time desc
        LIMIT 1) AS remark2,
        (SELECT t3.remark3 from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.remark3 is not null ORDER BY t3.follow_time desc
        LIMIT 1) AS remark3,
        (SELECT t3.remark4 from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.remark4 is not null ORDER BY t3.follow_time desc
        LIMIT 1) AS remark4,
        (SELECT t3.follow_content from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1 ORDER BY
        t3.follow_time desc LIMIT 1) AS follow_content,
        (SELECT t3.classification1 from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1 ORDER BY
        t3.follow_time desc LIMIT 1) AS classification1,
        (SELECT t3.classification2 from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1 ORDER BY
        t3.follow_time desc LIMIT 1) AS classification2,
        (SELECT t3.classification3 from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1 ORDER BY
        t3.follow_time desc LIMIT 1) AS classification3,
        (SELECT t3.classification4 from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1 ORDER BY
        t3.follow_time desc LIMIT 1) AS classification4,
        (SELECT t3.ccm_not_publish from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1 ORDER BY
        t3.follow_time desc LIMIT 1) AS ccm_not_publish,
        (SELECT t3.ccm_subject from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1 ORDER BY
        t3.follow_time desc LIMIT 1) AS ccm_subject,
        (SELECT t3.status from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1 ORDER BY
        t3.follow_time desc LIMIT 1) AS status,
        (SELECT t3.advise from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1 ORDER BY
        t3.follow_time desc LIMIT 1) AS advise,
        (SELECT t3.ccm_part from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1 ORDER BY
        t3.follow_time desc LIMIT 1) AS ccm_part,
        (SELECT t3.ccm_subdivision_part from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1
        ORDER BY t3.follow_time desc LIMIT 1) AS ccm_subdivision_part,
        (SELECT t3.cc_main_reason from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1 ORDER BY
        t3.follow_time desc LIMIT 1) AS cc_main_reason,
        (SELECT t3.cc_result from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1 ORDER BY
        t3.follow_time desc LIMIT 1) AS cc_result,
        (SELECT t3.keyword from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1 ORDER BY
        t3.follow_time desc LIMIT 1) AS keyword,
        (SELECT t3.classification5 from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1 ORDER BY
        t3.follow_time desc LIMIT 1) AS classification5,
        (SELECT t3.classification6 from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1 ORDER BY
        t3.follow_time desc LIMIT 1) AS classification6,
        (SELECT t3.plan_follow_time from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.is_ccm=1 ORDER BY
        t3.follow_time desc LIMIT 1) AS plan_follow_time,
        (SELECT t2.ccm_man from tt_complaint_dealer_ccm_ref t2 where t.dealer_code=t2.dealer_code LIMIT 1) AS ccm_man,
        t.buy_dealer_name AS buyDealName,
        t.buy_bloc AS buyBloc,
        t.buy_region_manager AS buyRegionManager,
        t.dealer_name AS handleDealName,
        t.bloc AS handleBloc,
        t.region as handleRegion,
        t.region_manager AS handleRegionManager,
        <if test=" params.assitdep !=null and params.assitdep != ''  ">
            t5.is_valid AS is_valid,
        </if>
        <if test=" params.assisDepartment !=null and params.assisDepartment != ''  ">
            t5.is_valid AS is_valid,
        </if>
        (SELECT group_concat(t5.big_class_name separator ',') from tt_complaint_not_close_case_reason t5 where
        t5.complaint_info_id=t.id) AS bigClassName,
        (SELECT group_concat(t5.small_class_name separator ',') from tt_complaint_not_close_case_reason t5 where
        t5.complaint_info_id=t.id ) AS smallClassNameOther
        FROM tt_complaint_info t
        LEFT JOIN tt_complaint_dealer_ccm_ref t2 on t.dealer_code=t2.dealer_code
        LEFT join tt_complaint_follow t3 on t.id=t3.complaint_info_id
        LEFT join tt_complaint_not_close_case_reason t4 on t.id=t4.complaint_info_id
        LEFT JOIN tt_complaint_assist_department t5 on t.id=t5.complaint_info_id
        <if test=" params.assitdep !=null and params.assitdep != ''  ">
            AND
            (t5.assist_dealer_code=#{params.assitdep}or t5.assist_department=#{params.assitdep})
        </if>
        <if test=" params.assisDepartment !=null and params.assisDepartment != ''  ">
            and t5.assist_department is not null
        </if>

        WHERE 1=1
--         and t.work_order_nature=83611002
        <if test=" params.isValid1 !=null and params.isValid1 != ''  ">
            and   t5.is_valid =10041001
        </if>
        <if test=" params.smallClass !=null and params.smallClass != ''  ">
            and (${params.smallClass})
        </if>
        <if test=" params.assisDepartment !=null and params.assisDepartment != ''  ">
            and t.id in (select complaint_info_id from tt_complaint_assist_department where assist_department
            =#{params.assisDepartment} )
        </if>

        <if test=" params.classification3 !=null and params.classification3 != ''  ">
            AND t3.classification3 like "%" #{params.classification3} "%"
        </if>
        <if test=" params.classification21 !=null and params.classification21 != ''  ">
            AND (${params.classification21})
        </if>
        <if test=" params.ccResult !=null and params.ccResult != ''  ">
            AND (${params.ccResult})
        </if>
        <if test=" params.classification11 !=null and params.classification11 != ''  ">
            AND (${params.classification11})
        </if>
        <if test=" params.ccmSubdivisionPart !=null and params.ccmSubdivisionPart != ''  ">
            AND (${params.ccmSubdivisionPart})
        </if>
        <if test=" params.ccmPart !=null and params.ccmPart != '' ">
            AND (${params.ccmPart})
        </if>
        <if test=" params.ccMainReason and params.ccMainReason != ''  ">
            AND (${params.ccMainReason})
        </if>
        <if test=" params.planFollowTime1 !=null ">
            AND t3.plan_follow_time >= #{params.planFollowTime1}
        </if>
        <if test=" params.planFollowTime2 !=null ">
            AND t3.plan_follow_time &lt;= #{params.planFollowTime2}
        </if>
        <if test=" params.actuallFollowTime !=null ">
            AND t3.actuall_follow_time2 >= #{params.actuallFollowTime}
        </if>
        <if test=" params.actuallFollowTime1 !=null ">
            AND t3.actuall_follow_time2 &lt;= #{params.actuallFollowTime1}
        </if>
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.complaintId !=null and params.complaintId != '' ">
            AND t.complaint_id  like "%"  #{params.complaintId} "%"
        </if>
        <if test=" params.type !=null and params.type != '' ">
            AND t.type in (${params.type})
        </if>
        <if test=" params.classification !=null and params.classification != '' ">
            AND t.classification = #{params.classification}
        </if>
        <if test=" params.source !=null and params.source != '' ">
            AND t.source in (${params.source})
        </if>
        <if test=" params.callTime !=null ">
            AND t.inform_time >= #{params.callTime}
        </if>
        <if test=" params.callTime2 !=null ">
            AND t.inform_time &lt;= #{params.callTime2}
        </if>
        <if test=" params.closeCaseTime !=null ">
            AND t.close_case_time >= #{params.closeCaseTime}
        </if>
        <if test=" params.closeCaseTime2 !=null ">
            AND t.close_case_time &lt;= #{params.closeCaseTime2}
        </if>
        <if test=" params.fisrtRestartTime !=null  ">
            AND t.first_restart_time >= #{params.fisrtRestartTime}
        </if>
        <if test=" params.fisrtRestartTime2 !=null  ">
            AND t.first_restart_time &lt;= #{params.fisrtRestartTime2}
        </if>
        <if test=" params.newestRestartTime !=null and params.newestRestartTime != '' ">
            AND t.newest_restart_time = #{params.newestRestartTime}
        </if>
        <if test=" params.callName !=null and params.callName != '' ">
            AND t.call_name like "%"  #{params.callName} "%"
        </if>
        <if test=" params.callTel !=null and params.callTel != '' ">
            AND t.call_tel = #{params.callTel}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name like "%"  #{params.name} "%"
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num like "%"  #{params.licensePlateNum} "%"
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin like "%"  #{params.vin} "%"
        </if>
        <if test=" params.buyTime !=null and params.buyTime != '' ">
            AND t.buy_time = #{params.buyTime}
        </if>
        <if test=" params.model !=null and params.model != '' ">
            AND t.model in(${params.model})
        </if>
        <if test=" params.blocId !=null and params.blocId != '' ">
            AND t.bloc_id in(${params.blocId})
        </if>
        <if test=" params.regionId !=null and params.regionId != '' ">
            AND t.region_id in(${params.regionId})
        </if>
        <if test=" params.regionManagerId !=null and params.regionManagerId != '' ">
            AND t.region_manager_id in(${params.regionManagerId})
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code in(${params.dealerCode})
        </if>
        <if test=" params.ccmManId !=null and params.ccmManId != '' ">
            AND t.dealer_code in(select dealer_code from tt_complaint_dealer_ccm_ref where ccm_man_id in
            (${params.ccmManId}) )
        </if>
        <if test=" params.modelYear !=null and params.modelYear != '' ">
            AND t.model_year like "%"  #{params.modelYear} "%"
        </if>
        <if test=" params.buyDealerName !=null and params.buyDealerName != '' ">
            AND t.buy_dealer_name = #{params.buyDealerName}
        </if>
        <if test=" params.dealerName !=null and params.dealerName != '' ">
            AND t.dealer_name = #{params.dealerName}
        </if>
        <if test=" params.mileage !=null and params.mileage != '' ">
            AND t.mileage = #{params.mileage}
        </if>
        <if test=" params.replyTel !=null and params.replyTel != '' ">
            AND t.reply_tel = #{params.replyTel}
        </if>
        <if test=" params.replyTel2 !=null and params.replyTel2 != '' ">
            AND t.reply_tel2 = #{params.replyTel2}
        </if>
        <if test=" params.subject !=null and params.subject != '' ">
            AND t.subject like "%" #{params.subject} "%"
        </if>
        <if test=" params.problem !=null and params.problem != '' ">
            AND t.problem = #{params.problem}
        </if>
        <if test=" params.illustrate !=null and params.illustrate != '' ">
            AND t.illustrate = #{params.illustrate}
        </if>
        <if test=" params.category1 !=null and params.category2 != '' ">
            AND (${params.category1})
        </if>
        <if test=" params.category2 !=null and params.category2 != '' ">
            AND (${params.category2})
        </if>
        <if test=" params.category3 !=null and params.category3 != '' ">
            AND (${params.category3})
        </if>
        <if test=" params.ccPart !=null and params.ccPart != '' ">
            AND t.cc_part = #{params.ccPart}
        </if>
        <if test=" params.ccSubdivisionPart !=null and params.ccSubdivisionPart != '' ">
            AND t.cc_subdivision_part = #{params.ccSubdivisionPart}
        </if>
        <if test=" params.ccProblem !=null and params.ccProblem != '' ">
            AND t.cc_problem = #{params.ccProblem}
        </if>
        <if test=" params.ccRequirement !=null and params.ccRequirement != '' ">
            AND t.cc_requirement = #{params.ccRequirement}
        </if>
        <if test=" params.department !=null and params.department != '' ">
            AND t.department = #{params.department}
        </if>
        <if test=" params.receptionist !=null and params.receptionist != '' ">
            AND t.receptionist = #{params.receptionist}
        </if>
        <if test=" params.importanceLevel !=null and params.importanceLevel != '' ">
            AND t.importance_level = #{params.importanceLevel}
        </if>
        <if test=" params.dealerFisrtReplyTime !=null and params.dealerFisrtReplyTime != '' ">
            AND t.dealer_fisrt_reply_time = #{params.dealerFisrtReplyTime}
        </if>
        <if test=" params.fisrtRestartDealerFisrtReplyTime !=null and params.fisrtRestartDealerFisrtReplyTime != '' ">
            AND t.fisrt_restart_dealer_fisrt_reply_time = #{params.fisrtRestartDealerFisrtReplyTime}
        </if>
        <if test=" params.isRevisit !=null and params.isRevisit != '' ">
            AND t.is_revisit = #{params.isRevisit}
        </if>
        <if test=" params.revisitTime !=null and params.revisitTime != '' ">
            AND t.revisit_time = #{params.revisitTime}
        </if>
        <if test=" params.revisitResult !=null and params.revisitResult != '' ">
            AND t.revisit_result = #{params.revisitResult}
        </if>
        <if test=" params.revisitContent !=null and params.revisitContent != '' ">
            AND t.revisit_content = #{params.revisitContent}
        </if>
        <if test=" params.applyTime !=null and params.applyTime != '' ">
            AND t.apply_time = #{params.applyTime}
        </if>
        <if test=" params.isAgree !=null and params.isAgree != '' ">
            AND t.is_agree = #{params.isAgree}
        </if>
        <if test=" params.submitTime !=null and params.submitTime != '' ">
            AND t.submit_time = #{params.submitTime}
        </if>
        <if test=" params.restartCloseCaseTime !=null and params.restartCloseCaseTime != '' ">
            AND t.restart_close_case_time = #{params.restartCloseCaseTime}
        </if>
        <if test=" params.closeCaseStatus !=null and params.closeCaseStatus != '' ">
            AND t.close_case_status = #{params.closeCaseStatus}
        </if>
        <if test=" params.followStatus !=null and params.followStatus != '' ">
            AND ${params.followStatus}
        </if>
        <if test=" params.basicReason !=null and params.basicReason != '' ">
            AND t.basic_reason = #{params.basicReason}
        </if>
        <if test=" params.isRepaired !=null and params.isRepaired != '' ">
            AND t.is_repaired = #{params.isRepaired}
        </if>
        <if test=" params.techMaintainPlan !=null and params.techMaintainPlan != '' ">
            AND t.tech_maintain_plan = #{params.techMaintainPlan}
        </if>
        <if test=" params.rapportPlan !=null and params.rapportPlan != '' ">
            AND t.rapport_plan = #{params.rapportPlan}
        </if>
        <if test=" params.risk !=null and params.risk != '' ">
            AND t.risk = #{params.risk}
        </if>
        <if test=" params.dealerIsRead !=null and params.dealerIsRead != '' ">
            AND t.dealer_is_read = #{params.dealerIsRead}
        </if>
        <if test=" params.managerIsRead !=null and params.managerIsRead != '' ">
            AND t.manager_is_read = #{params.managerIsRead}
        </if>
        <if test=" params.ccmIsRead !=null and params.ccmIsRead != '' ">
            AND t.ccm_is_read = #{params.ccmIsRead}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isCloseCase !=null and params.isCloseCase != '' ">
            AND t.is_close_case = #{params.isCloseCase}
        </if>
        <if test=" params.isReport !=null ">
            AND t.is_report = #{params.isReport}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>

        <if test=" params.sql1 !=null and params.sql1 != '' ">
            and ${params.sql1}
        </if>
        and t.is_deleted=0
        <if test=" params.sql !=null and params.sql != '' ">
            ${params.sql}
        </if>
    </select>

    <select id="getByvin" resultType="com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO"
            parameterType="String">
        SELECT    tgai.APP_ID appId, tgai.OWNER_CODE ownerCode, tgai.OWNER_PAR_CODE ownerParCode,
        tgai.ORG_ID orgId, tgai.id, tgai.apply_no applyNo, tgai.dealer_code dealerCode, tgai.audit_type auditType,
        tgai.dealer_name dealerName,tgai.area_manage_id areaManageId,tgai.area_manage areaManage,tgai.bloc_id blocId,tgai.bloc bloc,
        tgai.goodwill_nature goodwillNature, tgai.apply_time applyTime,
        tgai.complaint_falut complaintFalut, tgai.complaint_id complaintId,tgai.complaint_date complaintDate,
        tgai.vehicle_use vehicleUse, tgai.sales_dealer salesDealer,
        tgai.apply_amount applyAmount, tgai.audit_amount auditAmount,tgai.audit_amount auditPrice, tgai.settlement_amount settlementAmount,tgai.invoice_amount invoiceAmount,
        tgai.goodwill_status goodwillStatus, tgai.customer_pain customerPain, tgai.vin,
        tgai.license license, tgai.customer_name customerName,
        tgai.customer_mobile customerMobile, tgai.mileage mileage, tgai.model model, tgai.buy_car_date buyCarDate,
        tgai.warranty_start_date warrantyStartDate, tgai.is_extend_warranty isExtendWarranty, tgai.extend_warranty_name extendWarrantyName,
        tgai.extend_warranty_start_date extendWarrantyStartDate, tgai.extend_warranty_end_date extendWarrantyEndDate, tgai.maintain_cost maintainCost,
        tgai.extend_warranty_cost extendWarrantyCost, tgai.accessory_cost accessoryCost,
        tgai.voucher_cost voucherCost, tgai.walking_car_price walkingCarPrice,
        tgai.volvo_integral volvoIntegral, tgai.return_change_car_price returnChangeCarPrice,
        tgai.other_price otherPrice, tgai.cost_total costTotal,
        tgai.material_pass_time materialPassTime,tgai.pass_time passTime,
        tgai.customer_pay customerPay, tgai.dealer_undertake dealerUndertake, tgai.volvo_support_goodwill_amount volvoSupportGoodwillAmount,
        tgai.remark remark, tgai.apply_file applyFile, tgai.cost_statistics_file costStatisticsFile, tgai.cost_screenshot_file costScreenshotFile,
        tgai.ring_check_file ringCheckFile, tgai.trouble_repair_requisition_file troubleRepairRequisitionFile, tgai.work_order_file workOrderFile,
        tgai.situation_settlement_agreement_file situationSettlementAgreementFile, tgai.supplementary_material_file supplementaryMaterialFile,
        tgai.management_review_email_vp_file managementReviewEmailVpFile, tgai.management_review_email_ceo_file managementReviewEmailCeoFile,
        tgai.cost_update_file costUpdateFile, tgai.vcdc_else_file vcdcElseFile, tgai.customer_identification customerIdentification,
        tgai.else_file elseFile, tgai.commit_time commitTime, tgai.is_valid isValid, tgai.is_deleted isDeleted,
        tgma.customer_background customerBackground, tgma.customer_background_en customerBackgroundEn,
        tgma.reason_and_dispose reasonAndDispose,tgma.reason_and_dispose_en reasonAndDisposeEn,
        tgma.repair_solution repairSolution, tgma.repair_solution_en repairSolutionEn,
        tgma.customer_require customerRequire, tgma.customer_require_en customerRequireEn,
        tgma.potential_risk potentialRisk, tgma.potential_risk_en potentialRiskEn,
        tgma.vr_or_tj_no_en vrOrTjNoEn, tgma.vr_or_tj_no vrOrTjNo,tgma.id materialId,
        tgma.business_goodwill_apply_detail_en businessGoodwillApplyDetailEn, tgma.business_goodwill_apply_detail businessGoodwillApplyDetail

        FROM  tt_goodwill_apply_info tgai
        LEFT JOIN tt_goodwill_material_audit tgma on tgai.ID=tgma.goodwill_apply_id
        WHERE 1=1 AND tgai.vin=#{params.vin}
    </select>

    <select id="queryDealerFirstReplyTime" resultMap="BaseResultMap" parameterType="long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_info t
        WHERE 1=1
        AND t.id = #{complaintInfoId}
        and t.is_deleted=0

    </select>

    <select id="queryComplaintData" resultMap="BaseResultMap" parameterType="long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_info t
        WHERE 1=1
            AND t.id = #{params.id}
        and t.is_deleted=0

    </select>
    <select id="getCategory1" resultType="com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintClassificationDTO"
            parameterType="com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintClassificationDTO">
        select id,cate_name from tt_complaint_classification where id=#{params.id}
    </select>
    <select id="selectCusDetailById" resultMap="BaseResultMap1"
            parameterType="com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO">
        SELECT
        distinct t.id, t.app_id, t.owner_code,t.work_order_status,t.is_close_case, t.owner_par_code,
        (select abs(UNIX_TIMESTAMP(t3.plan_follow_time)-UNIX_TIMESTAMP(NOW())) from
        tt_complaint_follow t3 WHERE t.id=t3.complaint_info_id and t3.plan_follow_time is not null ORDER BY
        t3.follow_time desc LIMIT 1
        ) as nextPlanFollowTime,
        t.is_satisfied,
        t.regional_manager_comments as regionalManagerComments,
        t.ccm_system_assisted_processing,
        t.work_order_nature,t.work_order_classification,t.reply_name,t.service_commitment,
        t.org_id,t.is_report, t.complaint_id, t.type, t.classification, t.source, t.call_time,t.inform_time,
        t.owner_address, t.first_restart_time, t.newest_restart_time, t.call_name,sex, t.call_tel,
        t.name, t.license_plate_num, t.vin, t.buy_time, t.model, t.model_year,t.config_name as configName, t.buy_dealer_name,
        t.dealer_code, t.mileage, t.reply_tel, t.reply_tel2, t.subject, t.problem, t.illustrate,
        t.category1, t.category2, t.category3, t.part,t.subdivision_part,t.cc_part, t.cc_subdivision_part,
        t.cc_problem, t.cc_requirement, t.department, t.receptionist, t.importance_level, t.dealer_fisrt_reply_time,
        t.fisrt_restart_dealer_fisrt_reply_time, t.is_revisit, t.revisit_time, t.revisit_result, t.revisit_content,
        t.apply_time, t.is_agree, t.submit_time, t.close_case_time, t.restart_close_case_time, t.close_case_status,
        t.follow_status, t.basic_reason, t.is_repaired, t.tech_maintain_plan, t.rapport_plan, t.risk, t.dealer_is_read,
        t.manager_is_read, t.ccm_is_read, t.data_sources, t.is_deleted, t.created_at, t.updated_at
        ,t2.region AS region,t2.region_manager AS region_manager,t2.bloc AS bloc,t2.dealer_name AS dealer_name,
         t6.classification1,t6.classification2,t6.classification3,t6.classification4,t6.classification5,t6.classification6,t6.ccm_not_publish,t6.ccm_subject,
         t6.status,t6.advise,t6.ccm_part,t6.ccm_subdivision_part,t6.cc_main_reason,t6.cc_result,t6.keyword,t6.plan_follow_time,
        (SELECT t3.quality_classification1 from tt_complaint_follow t3 where t.id=t3.complaint_info_id
        and t3.quality_classification1 is not null ORDER BY
        t3.follow_time desc LIMIT 1) AS quality_classification1,
        (SELECT t3.quality_classification2 from tt_complaint_follow t3 where t.id=t3.complaint_info_id
        and t3.quality_classification2 is not null ORDER BY
        t3.follow_time desc LIMIT 1) AS quality_classification2,
        (SELECT t3.quality_classification3 from tt_complaint_follow t3 where t.id=t3.complaint_info_id
        and t3.quality_classification3 is not null ORDER BY
        t3.follow_time desc LIMIT 1) AS quality_classification3,
        (SELECT t3.quality_classification4 from tt_complaint_follow t3 where t.id=t3.complaint_info_id
        and t3.quality_classification4 is not null ORDER BY
        t3.follow_time desc LIMIT 1) AS quality_classification4,
        (SELECT t3.fault_classification from tt_complaint_follow t3 where t.id=t3.complaint_info_id
        and t3.fault_classification is not null ORDER BY
        t3.follow_time desc LIMIT 1) AS fault_classification,
        (SELECT t3.remark1 from tt_complaint_follow t3 where t.id=t3.complaint_info_id  and t3.remark1 is not null ORDER BY t3.follow_time desc
        LIMIT 1) AS remark1,
        (SELECT t3.remark2 from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.remark2 is not null ORDER BY t3.follow_time desc
        LIMIT 1) AS remark2,
        (SELECT t3.remark3 from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.remark3 is not null ORDER BY t3.follow_time desc
        LIMIT 1) AS remark3,
        (SELECT t3.remark4 from tt_complaint_follow t3 where t.id=t3.complaint_info_id and t3.remark4 is not null ORDER BY t3.follow_time desc
        LIMIT 1) AS remark4,
        (SELECT t2.ccm_man from tt_complaint_dealer_ccm_ref t2 where t.dealer_code=t2.dealer_code LIMIT 1) AS ccm_man,
        t.buy_dealer_code AS buyDealerCode,
        t.buy_dealer_name AS buyDealName,
        t.buy_bloc AS buyBloc,
        t.buy_region_manager AS buyRegionManager,
        t.dealer_name AS handleDealName,
        t.bloc AS handleBloc,
        t.region as handleRegion,
        t.region_manager AS handleRegionManager,
        (SELECT group_concat(t5.big_class_name separator ',') from tt_complaint_not_close_case_reason t5 where
        t5.complaint_info_id=t.id) AS bigClassName,
        (SELECT group_concat(t5.small_class_name separator ',') from tt_complaint_not_close_case_reason t5 where
        t5.complaint_info_id=t.id ) AS smallClassNameOther
        FROM tt_complaint_info t
        LEFT JOIN tt_complaint_dealer_ccm_ref t2 on t.dealer_code=t2.dealer_code
        LEFT join tt_complaint_follow t3 on t.id=t3.complaint_info_id
        LEFT join tt_complaint_not_close_case_reason t4 on t.id=t4.complaint_info_id
        LEFT JOIN tt_complaint_assist_department t5 on t.id=t5.complaint_info_id
        LEFT JOIN (select a.id,a.complaint_info_id,a.classification1,a.classification2,a.classification3,a.classification4,
        a.classification5,a.classification6,a.ccm_not_publish,a.ccm_subject,a.status,a.advise,a.ccm_part,a.ccm_subdivision_part,
        a.cc_main_reason,a.cc_result,a.keyword,a.plan_follow_time
        from  tt_complaint_follow a
        INNER JOIN   (select c.complaint_info_id,max(c.id) id from tt_complaint_follow c where c.is_ccm=1
        GROUP BY c.complaint_info_id) b on a.id=b.id and a.`complaint_info_id` =b.complaint_info_id )t6 on t6.complaint_info_id=t.id
        WHERE 1=1
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>

    </select>
    <select id="selectHoildayList" resultType="com.yonyou.dmscus.customer.entity.dto.common.HolidayDTO">
        SELECT year ,month  from tm_holiday where year=#{params.year} and  month=#{params.month}+1
    </select>

    <insert id="insertHoliday">
        INSERT INTO tm_holiday (year,month,holiday_date,created_at,created_by,updated_at,updated_by)
        VALUES
        <foreach collection="hoildayList" index="index" item="item" separator =","   >
            (#{params.year},#{params.month}+1,#{item},now(),'admin',now(),'admin')
        </foreach>
    </insert>

    <select id="queryDealer" resultType="java.lang.String">
        SELECT dealer_code from tt_complaint_info group by dealer_code
    </select>

    <select id="querySaleDealer" resultType="java.lang.String">
        SELECT dealer_code from tt_sale_complaint_info group by dealer_code
    </select>

    <update id="regionInformationRefresh" >
        <foreach collection="params" item="item" index="index" open="" close="" separator=";">
            UPDATE tt_complaint_info i
            INNER JOIN tt_complaint_info_extend e ON i.complaint_id = e.complaint_id
            <set>
                <if test=" item.afterSmallAreaName !=null">
                    e.after_small_area_name = #{item.afterSmallAreaName},
                </if>
                <if test=" item.afterSmallAreaId !=null">
                    i.region_manager_id = #{item.afterSmallAreaId},
                </if>
                <if test=" item.afterBigAreaName !=null">
                    i.region = #{item.afterBigAreaName},
                </if>
                <if test=" item.afterBigAreaId !=null">
                    i.region_id = #{item.afterBigAreaId},
                </if>
                <if test=" item.regionManager !=null">
                    i.region_manager = #{item.regionManager},
                </if>
            </set>
            <where>
                i.dealer_code = #{item.companyCode}
            </where>
        </foreach>
    </update>

    <update id="buyRegionInformationRefresh" >
        <foreach collection="params" item="item" index="index" open="" close="" separator=";">
            UPDATE tt_complaint_info
            <set>
                <if test=" item.saleBigAreaName !=null">
                    buy_region = #{item.saleBigAreaName},
                </if>
                <if test=" item.saleRegionManager !=null">
                    buy_region_manager = #{item.saleRegionManager},
                </if>
                <if test=" item.saleBigAreaId !=null">
                    buy_region_id = #{item.saleBigAreaId},
                </if>
                <if test=" item.saleSmallAreaId !=null">
                    buy_region_manager_id = #{item.saleSmallAreaId},
                </if>
            </set>
            <where>
                buy_dealer_code = #{item.companyCode}
            </where>
        </foreach>
    </update>

    <update id="saleRegionInformationRefresh" >
        <foreach collection="params" item="item" index="index" open="" close="" separator=";">
            UPDATE tt_sale_complaint_info
            <set>
                <if test=" item.saleBigAreaName !=null">
                    region = #{item.saleBigAreaName},
                </if>
                <if test=" item.saleRegionManager !=null">
                    region_manager = #{item.saleRegionManager},
                </if>
                <if test=" item.saleBigAreaId !=null">
                    region_id = #{item.saleBigAreaId},
                </if>
                <if test=" item.saleSmallAreaId !=null">
                    region_manager_id = #{item.saleSmallAreaId},
                </if>
            </set>
            <where>
                dealer_code = #{item.companyCode}
            </where>
        </foreach>
    </update>

    <update id="buySaleRegionInformationRefresh" >
        <foreach collection="params" item="item" index="index" open="" close="" separator=";">
            UPDATE tt_sale_complaint_info
            <set>
                <if test=" item.saleBigAreaName !=null">
                    buy_region = #{item.saleBigAreaName},
                </if>
                <if test=" item.saleRegionManager !=null">
                    buy_region_manager = #{item.saleRegionManager},
                </if>
                <if test=" item.saleBigAreaId !=null">
                    buy_region_id = #{item.saleBigAreaId},
                </if>
                <if test=" item.saleSmallAreaId !=null">
                    buy_region_manager_id = #{item.saleSmallAreaId},
                </if>
            </set>
            <where>
                buy_dealer_code = #{item.companyCode}
            </where>
        </foreach>
    </update>

    <update id="ccmRegionInformationRefresh" >
        <foreach collection="params" item="item" index="index" open="" close="" separator=";">
            UPDATE tt_complaint_dealer_ccm_ref
            <set>
                <if test=" item.afterBigAreaName !=null">
                    region = #{item.afterBigAreaName},
                </if>
                <if test=" item.regionManager !=null">
                    region_manager = #{item.regionManager},
                </if>
                <if test=" item.afterBigAreaId !=null">
                    region_id = #{item.afterBigAreaId},
                </if>
                <if test=" item.afterSmallAreaId !=null">
                    region_manager_id = #{item.afterSmallAreaId},
                </if>
            </set>
            <where>
                dealer_code = #{item.companyCode}
            </where>
        </foreach>
    </update>

</mapper>
