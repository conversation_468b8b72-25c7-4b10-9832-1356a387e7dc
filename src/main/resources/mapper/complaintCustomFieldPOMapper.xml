<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.complaint.ComplaintCustomFieldMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                            <result column="app_id" property="appId"/>
                                                        <result column="owner_code" property="ownerCode"/>
                                                        <result column="owner_par_code" property="ownerParCode"/>
                                                        <result column="org_id" property="orgId"/>
                                                                                    <result column="table_name" property="tableName"/>
                                                        <result column="field_name" property="fieldName"/>
                                                        <result column="field_describe" property="fieldDescribe"/>
                                                        <result column="is_query" property="isQuery"/>
                                                        <result column="is_sort" property="isSort"/>
                                                        <result column="data_sources" property="dataSources"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="is_valid" property="isValid"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, table_name, field_name, field_describe, is_query, is_sort, data_sources, is_deleted, is_valid, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.tableName !=null and params.tableName != '' ">
                AND t.table_name = #{params.tableName}
            </if>
                    <if test=" params.fieldName !=null and params.fieldName != '' ">
                AND t.field_name = #{params.fieldName}
            </if>
                    <if test=" params.fieldDescribe !=null and params.fieldDescribe != '' ">
                AND t.field_describe = #{params.fieldDescribe}
            </if>
                    <if test=" params.isQuery !=null and params.isQuery != '' ">
                AND t.is_query = #{params.isQuery}
            </if>
                    <if test=" params.isSort !=null and params.isSort != '' ">
                AND t.is_sort = #{params.isSort}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_custom_field t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.tableName !=null and params.tableName != '' ">
                AND t.table_name = #{params.tableName}
            </if>
                    <if test=" params.fieldName !=null and params.fieldName != '' ">
                AND t.field_name = #{params.fieldName}
            </if>
                    <if test=" params.fieldDescribe !=null and params.fieldDescribe != '' ">
                AND t.field_describe = #{params.fieldDescribe}
            </if>
                    <if test=" params.isQuery !=null and params.isQuery != '' ">
                AND t.is_query = #{params.isQuery}
            </if>
                    <if test=" params.isSort !=null and params.isSort != '' ">
                AND t.is_sort = #{params.isSort}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

</mapper>
