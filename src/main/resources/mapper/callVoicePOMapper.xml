<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.voicemanage.CallVoiceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.voicemanage.CallVoicePO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="session_Id" property="sessionId"/>
        <result column="call_id" property="callId"/>
        <result column="display_number" property="displayNumber"/>
        <result column="caller_number" property="callerNumber"/>
        <result column="callee_number" property="calleeNumber"/>
        <result column="work_number" property="workNumber"/>
        <result column="file_size" property="fileSize"/>
        <result column="voice_url" property="voiceUrl"/>
        <result column="voice_type" property="voiceType"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
        <result column="dealer_code" property="dealerCode"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        app_id, owner_code, owner_par_code, org_id, id, session_Id, call_id, display_number, caller_number,
        callee_number, work_number, file_size, voice_url, voice_type, data_sources, is_deleted, is_valid,dealer_code, created_at, updated_at
    </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.voicemanage.CallVoicePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_call_voice t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.sessionId !=null and params.sessionId != '' ">
            AND t.session_Id = #{params.sessionId}
        </if>
        <if test=" params.callId !=null and params.callId != '' ">
            AND t.call_id = #{params.callId}
        </if>
        <if test=" params.displayNumber !=null and params.displayNumber != '' ">
            AND t.display_number = #{params.displayNumber}
        </if>
        <if test=" params.callerNumber !=null and params.callerNumber != '' ">
            AND t.caller_number = #{params.callerNumber}
        </if>
        <if test=" params.calleeNumber !=null and params.calleeNumber != '' ">
            AND t.callee_number = #{params.calleeNumber}
        </if>
        <if test=" params.workNumber !=null and params.workNumber != '' ">
            AND t.work_number = #{params.workNumber}
        </if>
        <if test=" params.fileSize !=null and params.fileSize != '' ">
            AND t.file_size = #{params.fileSize}
        </if>
        <if test=" params.voiceUrl !=null and params.voiceUrl != '' ">
            AND t.voice_url = #{params.voiceUrl}
        </if>
        <if test=" params.voiceType !=null and params.voiceType != '' ">
            AND t.voice_type = #{params.voiceType}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.voicemanage.CallVoicePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_call_voice t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.sessionId !=null and params.sessionId != '' ">
            AND t.session_Id = #{params.sessionId}
        </if>
        <if test=" params.callId !=null and params.callId != '' ">
            AND t.call_id = #{params.callId}
        </if>
        <if test=" params.displayNumber !=null and params.displayNumber != '' ">
            AND t.display_number = #{params.displayNumber}
        </if>
        <if test=" params.callerNumber !=null and params.callerNumber != '' ">
            AND t.caller_number = #{params.callerNumber}
        </if>
        <if test=" params.calleeNumber !=null and params.calleeNumber != '' ">
            AND t.callee_number = #{params.calleeNumber}
        </if>
        <if test=" params.workNumber !=null and params.workNumber != '' ">
            AND t.work_number = #{params.workNumber}
        </if>
        <if test=" params.fileSize !=null and params.fileSize != '' ">
            AND t.file_size = #{params.fileSize}
        </if>
        <if test=" params.voiceUrl !=null and params.voiceUrl != '' ">
            AND t.voice_url = #{params.voiceUrl}
        </if>
        <if test=" params.voiceType !=null and params.voiceType != '' ">
            AND t.voice_type = #{params.voiceType}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>
    </select>

    <insert id="insertCallVoice" parameterType="java.util.List">
        insert into tt_call_voice(app_id,owner_code,owner_par_code,session_Id,call_id,caller_number,callee_number,work_number,file_size,voice_url,voice_type,dealer_code,created_by,record_version) values
        <foreach collection="allDetail" item="item" index="index" separator=",">
         ('volvo',#{item.ownerCode},#{item.ownerParCode},#{item.sessionId},#{item.callId},#{item.callerNbr},#{item.calleeNbr},#{item.workNbr},#{item.fileSize},#{item.voiceAddr},#{item.type},#{item.dealerCode},'-1',1)
        </foreach>
    </insert>

    <select id="selectSendCallVoice" resultType="java.util.Map" parameterType="java.lang.Integer">
        SELECT a.id,a.session_Id,a.call_id,a.display_number,a.caller_number,a.callee_number,a.work_number,a.file_size,a.voice_url,a.voice_type,a.dealer_code,b.start_time from tt_call_voice a
        left join tt_call_details b on a.session_Id=b.session_Id
        where a.is_send=#{isSend}
    </select>
    
</mapper>
