<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillMailTemplateMaintainMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMailTemplateMaintainPO">
                    <id column="id" property="id"/>
                    <result column="APP_ID" property="appId"/>
                    <result column="OWNER_CODE" property="ownerCode"/>
                    <result column="OWNER_PAR_CODE" property="ownerParCode"/>
                    <result column="ORG_ID" property="orgId"/>
                    <result column="mail_type" property="mailType"/>
                    <result column="send_type" property="sendType"/>
                    <result column="is_use" property="isUse"/>
                    <result column="days" property="days"/>
                    <result column="send_object" property="sendObject"/>
                    <result column="mail_title" property="mailTitle"/>
                    <result column="mail_content" property="mailContent"/>
                    <result column="is_valid" property="isValid"/>
                    <result column="is_deleted" property="isDeleted"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
                	<result column="created_by" property="createdBy"/>
                    <result column="updated_by" property="updatedBy"/>
                    <result column="record_version" property="recordVersion"/>
           </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, mail_type, send_type, is_use,days, send_object, mail_title, mail_content, is_valid, is_deleted, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMailTemplateMaintainPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tm_goodwill_mail_template_maintain t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.mailType !=null and params.mailType != '' ">
                AND t.mail_type = #{params.mailType}
            </if>
                    <if test=" params.sendType !=null and params.sendType != '' ">
                AND t.send_type = #{params.sendType}
            </if>
                    <if test=" params.isUse !=null and params.isUse != '' ">
                AND t.is_use = #{params.isUse}
            </if>
                    <if test=" params.sendObject !=null and params.sendObject != '' ">
                AND t.send_object = #{params.sendObject}
            </if>
                    <if test=" params.mailTitle !=null and params.mailTitle != '' ">
                AND t.mail_title = #{params.mailTitle}
            </if>
                    <if test=" params.mailContent !=null and params.mailContent != '' ">
                AND t.mail_content = #{params.mailContent}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMailTemplateMaintainPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tm_goodwill_mail_template_maintain t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.mailType !=null and params.mailType != '' ">
                AND t.mail_type = #{params.mailType}
            </if>
                    <if test=" params.sendType !=null and params.sendType != '' ">
                AND t.send_type = #{params.sendType}
            </if>
                    <if test=" params.isUse !=null and params.isUse != '' ">
                AND t.is_use = #{params.isUse}
            </if>
                    <if test=" params.sendObject !=null and params.sendObject != '' ">
                AND t.send_object = #{params.sendObject}
            </if>
                    <if test=" params.mailTitle !=null and params.mailTitle != '' ">
                AND t.mail_title = #{params.mailTitle}
            </if>
                    <if test=" params.mailContent !=null and params.mailContent != '' ">
                AND t.mail_content = #{params.mailContent}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>
		<select id="selectTemplateMaintain" resultMap="BaseResultMap">
			select 
			<include refid="Base_Column_List"/>
        	FROM tm_goodwill_mail_template_maintain t
			where t.mail_type=#{mailType} and t.is_use=1
		</select>
			
</mapper>
