<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.talkskill.TalkskillPageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillPagePO">
        <id column="page_id" property="pageId"/>
        <result column="page_name" property="pageName"/>
        <result column="tag_name" property="tagName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
       page_id, tag_name, page_name
    </sql>
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillPagePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_talkskill_page t
    </select>

</mapper>
