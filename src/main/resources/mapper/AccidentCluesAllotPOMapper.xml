<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.accidentClues.AccidentCluesAllotMapper">
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesAllotPO">
        <id column="allot_id" property="allotId"/>

        <result column="app_Id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="ac_id" property="acId"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="follow_people" property="followPeople"/>
        <result column="allot_personnel" property="allotPersonnel"/>
        <result column="allot_date" property="allotDate"/>

        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
    </resultMap>

<!--    <select id="selectPageBySql" resultMap="BaseResultMap"-->
<!--            parameterType="">-->
<!--    </select>-->
</mapper>