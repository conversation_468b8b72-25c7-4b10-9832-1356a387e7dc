<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.complaint.ComplaintSendEmailRecordMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintSendEmailRecordPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <result column="app_id" property="appId"/>
                                                        <result column="owner_code" property="ownerCode"/>
                                                        <result column="owner_par_code" property="ownerParCode"/>
                                                        <result column="org_id" property="orgId"/>
                                                         <result column="complaint_info_id" property="complaintInfoId"/>
                                                        <result column="complaint_id" property="complaintId"/>
                                                        <result column="send_email" property="sendEmail"/>
                                                        <result column="email_type" property="emailType"/>
                                                        <result column="send_time" property="sendTime"/>
                                                        <result column="send_status" property="sendStatus"/>
                                                        <result column="email" property="email"/>
                                                        <result column="title" property="title"/>
                                                        <result column="contect" property="contect"/>
                                                        <result column="data_sources" property="dataSources"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="is_valid" property="isValid"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                        <result column="receipter" property="receipter"/>
                                                        <result column="assist_department" property="assistDepartment"/>
                                                        <result column="assist_dealer_code" property="assistDealerCode"/>
                                                        <result column="assist_department_name" property="assistDepartmentName"/>
                                                        <result column="assist_dealer_name" property="assistDealerName"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, complaint_info_id, complaint_id, send_email, email_type, send_time, send_status, email, title, contect, data_sources, is_deleted, is_valid, created_at, updated_at, receipter,
           assist_department, assist_dealer_code,assist_department_name, assist_dealer_name
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintSendEmailRecordPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_send_email_record t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.complaintInfoId !=null and params.complaintInfoId != '' ">
                AND t.complaint_info_id = #{params.complaintInfoId}
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id = #{params.complaintId}
            </if>
                    <if test=" params.sendEmail !=null and params.sendEmail != '' ">
                AND t.send_email like "%" #{params.sendEmail} "%"
            </if>
                    <if test=" params.emailType !=null and params.emailType != '' ">
                AND t.email_type = #{params.emailType}
            </if>
                    <if test=" params.sendTime !=null and params.sendTime != '' ">
                AND t.send_time = #{params.sendTime}
            </if>
                    <if test=" params.sendStatus !=null and params.sendStatus != '' ">
                AND t.send_status = #{params.sendStatus}
            </if>
                    <if test=" params.email !=null and params.email != '' ">
                AND t.email like "%" #{params.email} "%"
            </if>
                    <if test=" params.title !=null and params.title != '' ">
                AND t.title like "%" #{params.title} "%"
            </if>
                    <if test=" params.contect !=null and params.contect != '' ">
                AND t.contect like "%" #{params.contect} "%"
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null  ">
                AND t.send_time >= #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null  ">
                AND t.send_time &lt;= #{params.updatedAt}
            </if>
                    <if test=" params.receipter !=null and params.receipter != '' ">
                AND t.receipter = #{params.receipter}
            </if>
                    <if test=" params.assistDepartment !=null and params.assistDepartment != '' ">
                AND t.assist_department = #{params.assistDepartment}
            </if>
                    <if test=" params.assistDealerCode !=null and params.assistDealerCode != '' ">
                AND t.assist_dealer_code = #{params.assistDealerCode}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintSendEmailRecordPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_send_email_record t
        WHERE 1=1
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.complaintInfoId !=null and params.complaintInfoId != '' ">
            AND t.complaint_info_id = #{params.complaintInfoId}
        </if>
        <if test=" params.complaintId !=null and params.complaintId != '' ">
            AND t.complaint_id = #{params.complaintId}
        </if>
        <if test=" params.sendEmail !=null and params.sendEmail != '' ">
            AND t.send_email like "%" #{params.sendEmail} "%"
        </if>
        <if test=" params.emailType !=null and params.emailType != '' ">
            AND t.email_type = #{params.emailType}
        </if>
        <if test=" params.sendTime !=null and params.sendTime != '' ">
            AND t.send_time = #{params.sendTime}
        </if>
        <if test=" params.sendStatus !=null and params.sendStatus != '' ">
            AND t.send_status = #{params.sendStatus}
        </if>
        <if test=" params.email !=null and params.email != '' ">
            AND t.email like "%" #{params.email} "%"
        </if>
        <if test=" params.title !=null and params.title != '' ">
            AND t.title like "%" #{params.title} "%"
        </if>
        <if test=" params.contect !=null and params.contect != '' ">
            AND t.contect like "%" #{params.contect} "%"
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null  ">
            AND t.send_time >= #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null  ">
            AND t.send_time &lt;= #{params.updatedAt}
        </if>
        <if test=" params.receipter !=null and params.receipter != '' ">
            AND t.receipter = #{params.receipter}
        </if>
        <if test=" params.assistDepartment !=null and params.assistDepartment != '' ">
            AND t.assist_department = #{params.assistDepartment}
        </if>
        <if test=" params.assistDealerCode !=null and params.assistDealerCode != '' ">
            AND t.assist_dealer_code = #{params.assistDealerCode}
        </if>
            </select>

    <select id="selectLastEmail"  resultType="com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailRecordDTO" parameterType="long" >
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_complaint_send_email_record t
        WHERE 1=1  and  t.created_by=#{userId}
        order by t.created_at desc LIMIT 1
    </select>
    <select id="selectEmaillist"  resultType="com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailRecordDTO" parameterType="long" >
        SELECT
        t.send_email
        FROM tt_complaint_send_email_record t
        WHERE 1=1  and  t.created_by=#{userId}
        order by t.created_at desc
    </select>
</mapper>
