<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyAuditDetailMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditDetailPO">
          	<id column="id" property="id"/>
          	<result column="APP_ID" property="appId"/>
          	<result column="OWNER_CODE" property="ownerCode"/>
          	<result column="OWNER_PAR_CODE" property="ownerParCode"/>
          	<result column="ORG_ID" property="orgId"/>
          	<result column="audit_id" property="auditId"/>
          	<result column="audit_way" property="auditWay"/>
          	<result column="audit_time" property="auditTime"/>
          	<result column="trouble_spots" property="troubleSpots"/>
          	<result column="punish_result" property="punishResult"/>
          	<result column="deductions_price" property="deductionsPrice"/>
          	<result column="is_notification" property="isNotification"/>
          	<result column="is_valid" property="isValid"/>
          	<result column="is_deleted" property="isDeleted"/>
          	<result column="created_at" property="createdAt"/>
          	<result column="updated_at" property="updatedAt"/>
   			<result column="created_by" property="createdBy"/>
          	<result column="updated_by" property="updatedBy"/>
          	<result column="record_version" property="recordVersion"/>
       	</resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           APP_ID, OWNER_CODE, OWNER_PAR_CODE, ORG_ID, id, audit_id, audit_way, audit_time, trouble_spots, punish_result, deductions_price, is_notification, is_valid, is_deleted, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditDetailPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_audit_detail t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.auditId !=null and params.auditId != '' ">
                AND t.audit_id = #{params.auditId}
            </if>
                    <if test=" params.auditWay !=null and params.auditWay != '' ">
                AND t.audit_way = #{params.auditWay}
            </if>
                    <if test=" params.auditTime !=null and params.auditTime != '' ">
                AND t.audit_time = #{params.auditTime}
            </if>
                    <if test=" params.troubleSpots !=null and params.troubleSpots != '' ">
                AND t.trouble_spots = #{params.troubleSpots}
            </if>
                    <if test=" params.punishResult !=null and params.punishResult != '' ">
                AND t.punish_result = #{params.punishResult}
            </if>
                    <if test=" params.deductionsPrice !=null and params.deductionsPrice != '' ">
                AND t.deductions_price = #{params.deductionsPrice}
            </if>
                    <if test=" params.isNotification !=null and params.isNotification != '' ">
                AND t.is_notification = #{params.isNotification}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditDetailPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_goodwill_apply_audit_detail t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.APP_ID = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.OWNER_CODE = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.OWNER_PAR_CODE = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.ORG_ID = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.auditId !=null and params.auditId != '' ">
                AND t.audit_id = #{params.auditId}
            </if>
                    <if test=" params.auditWay !=null and params.auditWay != '' ">
                AND t.audit_way = #{params.auditWay}
            </if>
                    <if test=" params.auditTime !=null and params.auditTime != '' ">
                AND t.audit_time = #{params.auditTime}
            </if>
                    <if test=" params.troubleSpots !=null and params.troubleSpots != '' ">
                AND t.trouble_spots = #{params.troubleSpots}
            </if>
                    <if test=" params.punishResult !=null and params.punishResult != '' ">
                AND t.punish_result = #{params.punishResult}
            </if>
                    <if test=" params.deductionsPrice !=null and params.deductionsPrice != '' ">
                AND t.deductions_price = #{params.deductionsPrice}
            </if>
                    <if test=" params.isNotification !=null and params.isNotification != '' ">
                AND t.is_notification = #{params.isNotification}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>
			<select id="queryAuditDetailInfo" resultMap="BaseResultMap">
			select 
				<include refid="Base_Column_List"/>
	        FROM tt_goodwill_apply_audit_detail t
	        WHERE 1=1 and t.audit_id=#{auditId}
			
			</select>


</mapper>
