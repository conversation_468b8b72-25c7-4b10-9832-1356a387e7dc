<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.pushRecord.RemotePushRecordMapper">

    <select id="countAcRemotePushCompensateList" resultType="java.lang.Integer">

        SELECT COUNT(1)
        FROM `tt_remote_push_record`
        WHERE since_type = #{sinceType}
        AND  retry_count <![CDATA[ < ]]> 3
        AND (task_status = 2 OR(task_status = 0 AND TIMESTAMPDIFF(SECOND, created_at, NOW()) > 300) )
    </select>

    <select id="queryAcRemotePushCompensateList" resultType="com.yonyou.dmscus.customer.entity.po.pushRecord.RemotePushRecordPO">

        SELECT id, since_type, sub_since_type, biz_no, sub_biz_no, req_params, retry_count, last_retry_time, task_status, created_at, updated_at
        FROM `tt_remote_push_record`
        WHERE since_type = #{sinceType}
        AND  retry_count <![CDATA[ < ]]> 3
        AND (task_status = 2 OR(task_status = 0 AND TIMESTAMPDIFF(SECOND, created_at, NOW()) > 300) )
        LIMIT #{limit},#{offset}
    </select>
</mapper>
