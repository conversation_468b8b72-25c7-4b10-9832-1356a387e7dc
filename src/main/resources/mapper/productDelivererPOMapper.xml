<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.productdeliverer.ProductDelivererMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.productdeliverer.ProductDelivererPO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="dealer_name" property="dealerName"/>
        <result column="vin" property="vin"/>
        <result column="deliverer" property="deliverer"/>
        <result column="deliverer_phone" property="delivererPhone"/>
        <result column="deliverer_mobile" property="delivererMobile"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="record_version" property="recordVersion"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        app_id, owner_code, owner_par_code, org_id, id, dealer_code, dealer_name, vin, deliverer, deliverer_phone, deliverer_mobile, data_sources, is_deleted, is_valid, created_at, updated_at
    </sql>


    <select id="queryProductDeliverer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        FROM tt_product_deliverer t
        where
         1=1
        AND t.dealer_code = #{dealerCode}
        AND t.vin = #{vin}
    </select>


</mapper>
