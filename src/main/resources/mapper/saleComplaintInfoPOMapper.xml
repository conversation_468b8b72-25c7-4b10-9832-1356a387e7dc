<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintInfoMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <result column="app_id" property="appId"/>
                                                        <result column="owner_code" property="ownerCode"/>
                                                        <result column="owner_par_code" property="ownerParCode"/>
                                                        <result column="org_id" property="orgId"/>
                                                                                    <result column="complaint_id" property="complaintId"/>
                                                        <result column="type" property="type"/>
                                                        <result column="classification" property="classification"/>
                                                        <result column="source" property="source"/>
                                                        <result column="call_time" property="callTime"/>
                                                        <result column="fisrt_restart_time" property="fisrtRestartTime"/>
                                                        <result column="newest_restart_time" property="newestRestartTime"/>
                                                        <result column="call_name" property="callName"/>
                                                        <result column="call_tel" property="callTel"/>
                                                        <result column="name" property="name"/>
                                                        <result column="license_plate_num" property="licensePlateNum"/>
                                                        <result column="vin" property="vin"/>
                                                        <result column="buy_time" property="buyTime"/>
                                                        <result column="model" property="model"/>
                                                        <result column="model_year" property="modelYear"/>
                                                        <result column="buy_dealer_name" property="buyDealerName"/>
                                                        <result column="dealer_name" property="dealerName"/>
                                                        <result column="dealer_code" property="dealerCode"/>
                                                        <result column="mileage" property="mileage"/>
                                                        <result column="reply_tel" property="replyTel"/>
                                                        <result column="reply_tel2" property="replyTel2"/>
                                                        <result column="subject" property="subject"/>
                                                        <result column="subdivision_part" property="subdivisionPart"/>
                                                        <result column="part" property="part"/>
                                                        <result column="problem" property="problem"/>
                                                        <result column="illustrate" property="illustrate"/>
                                                        <result column="category1" property="category1"/>
                                                        <result column="category2" property="category2"/>
                                                        <result column="category3" property="category3"/>
            <result column="cc_part" property="ccPart"/>
                                                        <result column="cc_subdivision_part" property="ccSubdivisionPart"/>
                                                        <result column="cc_problem" property="ccProblem"/>
                                                        <result column="cc_requirement" property="ccRequirement"/>
                                                        <result column="department" property="department"/>
                                                        <result column="receptionist" property="receptionist"/>
                                                        <result column="importance_level" property="importanceLevel"/>
                                                        <result column="dealer_fisrt_reply_time" property="dealerFisrtReplyTime"/>
                                                        <result column="fisrt_restart_dealer_fisrt_reply_time" property="fisrtRestartDealerFisrtReplyTime"/>
                                                        <result column="is_revisit" property="isRevisit"/>
                                                        <result column="revisit_time" property="revisitTime"/>
                                                        <result column="revisit_result" property="revisitResult"/>
                                                        <result column="revisit_content" property="revisitContent"/>
                                                        <result column="apply_time" property="applyTime"/>
                                                        <result column="is_agree" property="isAgree"/>
                                                        <result column="submit_time" property="submitTime"/>
                                                        <result column="close_case_time" property="closeCaseTime"/>
                                                        <result column="restart_close_case_time" property="restartCloseCaseTime"/>
                                                        <result column="work_status" property="workStatus"/>
                                                        <result column="close_case_status" property="closeCaseStatus"/>
                                                        <result column="follow_status" property="followStatus"/>
                                                        <result column="basic_reason" property="basicReason"/>
                                                        <result column="is_repaired" property="isRepaired"/>
                                                        <result column="tech_maintain_plan" property="techMaintainPlan"/>
                                                        <result column="rapport_plan" property="rapportPlan"/>
                                                        <result column="risk" property="risk"/>
                                                        <result column="dealer_is_read" property="dealerIsRead"/>
                                                        <result column="manager_is_read" property="managerIsRead"/>
                                                        <result column="ccm_is_read" property="ccmIsRead"/>
                                                        <result column="data_sources" property="dataSources"/>
                                                        <result column="is_report" property="isReport"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="is_valid" property="isValid"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                        <result column="sex" property="sex"/>
                                                        <result column="owner_address" property="ownerAddress"/>
                                                        <result column="work_order_status" property="workOrderStatus"/>
                                                        <result column="is_close_case" property="isCloseCase"/>
                                                        <result column="regional_manager_comments" property="regionalManagerComments"/>
                                                        <result column="region" property="region"/>
                                                        <result column="region_manager" property="regionManager"/>
                                                        <result column="region_id" property="regionId"/>
                                                        <result column="region_manager_id" property="regionManagerId"/>
                                                        <result column="bloc" property="bloc"/>
                                                        <result column="buy_dealer_code" property="buyDealerCode"/>
                                                        <result column="buy_region" property="buyRegion"/>
                                                        <result column="buy_region_manager" property="buyRegionManager"/>
                                                        <result column="buy_region_id" property="buyRegionId"/>
                                                        <result column="buy_region_manager_id" property="buyRegionManagerId"/>
                                                        <result column="buy_bloc" property="buyBloc"/>
                                                        <result column="bloc_id" property="blocId"/>
                                                        <result column="is_satisfied" property="isSatisfied"/>
                                                        <result column="work_order_nature" property="workOrderNature"/>
                                                        <result column="work_order_classification" property="workOrderClassification"/>
                                                        <result column="service_commitment" property="serviceCommitment"/>
                                                        <result column="reply_name" property="replyName"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>
    <resultMap id="BaseResultMap1" type="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfMorePO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="owner_code" property="ownerCode"/>
        <result column="owner_par_code" property="ownerParCode"/>
        <result column="org_id" property="orgId"/>
        <result column="complaint_id" property="complaintId"/>
        <result column="type" property="type"/>
        <result column="classification" property="classification"/>
        <result column="source" property="source"/>
        <result column="call_time" property="callTime"/>
        <result column="fisrt_restart_time" property="fisrtRestartTime"/>
        <result column="newest_restart_time" property="newestRestartTime"/>
        <result column="call_name" property="callName"/>
        <result column="sex" property="sex"/>
        <result column="work_order_status" property="workOrderStatus"/>
        <result column="is_close_case" property="isCloseCase"/>
        <result column="is_report" property="isReport"/>
        <result column="owner_address" property="ownerAddress"/>
        <result column="call_tel" property="callTel"/>
        <result column="name" property="name"/>
        <result column="license_plate_num" property="licensePlateNum"/>
        <result column="vin" property="vin"/>
        <result column="buy_time" property="buyTime"/>
        <result column="model" property="model"/>
        <result column="model_year" property="modelYear"/>
        <result column="buy_dealer_name" property="buyDealerName"/>
        <result column="dealer_name" property="dealerName"/>
        <result column="dealer_code" property="dealerCode"/>
        <result column="mileage" property="mileage"/>
        <result column="reply_tel" property="replyTel"/>
        <result column="reply_tel2" property="replyTel2"/>
        <result column="subject" property="subject"/>
        <result column="problem" property="problem"/>
        <result column="illustrate" property="illustrate"/>
        <result column="category1" property="category1"/>
        <result column="category2" property="category2"/>
        <result column="category3" property="category3"/>
        <result column="cc_part" property="ccPart"/>
        <result column="cc_subdivision_part" property="ccSubdivisionPart"/>
        <result column="part" property="part"/>
        <result column="subdivision_part" property="subdivisionPart"/>
        <result column="cc_problem" property="ccProblem"/>
        <result column="cc_requirement" property="ccRequirement"/>
        <result column="department" property="department"/>
        <result column="receptionist" property="receptionist"/>
        <result column="importance_level" property="importanceLevel"/>
        <result column="dealer_fisrt_reply_time" property="dealerFisrtReplyTime"/>
        <result column="fisrt_restart_dealer_fisrt_reply_time" property="fisrtRestartDealerFisrtReplyTime"/>
        <result column="is_revisit" property="isRevisit"/>
        <result column="revisit_time" property="revisitTime"/>
        <result column="revisit_result" property="revisitResult"/>
        <result column="revisit_content" property="revisitContent"/>
        <result column="apply_time" property="applyTime"/>
        <result column="is_agree" property="isAgree"/>
        <result column="submit_time" property="submitTime"/>
        <result column="close_case_time" property="closeCaseTime"/>
        <result column="restart_close_case_time" property="restartCloseCaseTime"/>
        <result column="close_case_status" property="closeCaseStatus"/>
        <result column="follow_status" property="followStatus"/>
        <result column="basic_reason" property="basicReason"/>
        <result column="is_repaired" property="isRepaired"/>
        <result column="tech_maintain_plan" property="techMaintainPlan"/>
        <result column="rapport_plan" property="rapportPlan"/>
        <result column="risk" property="risk"/>
        <result column="dealer_is_read" property="dealerIsRead"/>
        <result column="manager_is_read" property="managerIsRead"/>
        <result column="ccm_is_read" property="ccmIsRead"/>
        <result column="data_sources" property="dataSources"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="region" property="region"/>
        <result column="region_manager" property="regionManager"/>
        <result column="is_satisfied" property="isSatisfied"/>
        <result column="work_order_nature" property="workOrderNature"/>
        <result column="work_order_classification" property="workOrderClassification"/>
        <result column="is_opinion" property="isOpinion"/>
        <result column="close_case_remark" property="closeCaseRemark"/>
        <result column="reply_name" property="replyName"/>
        <result column="service_commitment" property="serviceCommitment"/>
        <result column="restart_reply_time" property="restartReplyTime"/>
        <result column="ccm_man" property="ccmMan"/>

    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, complaint_id, type, classification, source, call_time, fisrt_restart_time, newest_restart_time, call_name, call_tel, name, license_plate_num, vin, buy_time, model, model_year, buy_dealer_name, dealer_name, dealer_code, mileage, reply_tel, reply_tel2, subject, subdivision_part, part, problem, illustrate, category1, category2, category3, cc_part, cc_subdivision_part, cc_problem, cc_requirement, department, receptionist, importance_level, dealer_fisrt_reply_time, fisrt_restart_dealer_fisrt_reply_time, is_revisit, revisit_time, revisit_result, revisit_content, apply_time, is_agree, submit_time, close_case_time, restart_close_case_time, work_status, close_case_status, follow_status, basic_reason, is_repaired, tech_maintain_plan, rapport_plan, risk, dealer_is_read, manager_is_read, ccm_is_read, data_sources, is_report, is_deleted, is_valid, created_at, updated_at, sex, owner_address, work_order_status, is_close_case, regional_manager_comments, region, region_manager, region_id, region_manager_id, bloc, buy_dealer_code, buy_region, buy_region_manager, buy_region_id, buy_region_manager_id, buy_bloc, bloc_id, is_satisfied, work_order_nature, work_order_classification, service_commitment, reply_name
        </sql>


    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id = #{params.complaintId}
            </if>
                    <if test=" params.type !=null and params.type != '' ">
                AND t.type = #{params.type}
            </if>
                    <if test=" params.classification !=null and params.classification != '' ">
                AND t.classification = #{params.classification}
            </if>
                    <if test=" params.source !=null and params.source != '' ">
                AND t.source = #{params.source}
            </if>
                    <if test=" params.callTime !=null and params.callTime != '' ">
                AND t.call_time = #{params.callTime}
            </if>
                    <if test=" params.fisrtRestartTime !=null and params.fisrtRestartTime != '' ">
                AND t.fisrt_restart_time = #{params.fisrtRestartTime}
            </if>
                    <if test=" params.newestRestartTime !=null and params.newestRestartTime != '' ">
                AND t.newest_restart_time = #{params.newestRestartTime}
            </if>
                    <if test=" params.callName !=null and params.callName != '' ">
                AND t.call_name = #{params.callName}
            </if>
                    <if test=" params.callTel !=null and params.callTel != '' ">
                AND t.call_tel = #{params.callTel}
            </if>
                    <if test=" params.name !=null and params.name != '' ">
                AND t.name = #{params.name}
            </if>
                    <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
                AND t.license_plate_num = #{params.licensePlateNum}
            </if>
                    <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin = #{params.vin}
            </if>
                    <if test=" params.buyTime !=null and params.buyTime != '' ">
                AND t.buy_time = #{params.buyTime}
            </if>
                    <if test=" params.model !=null and params.model != '' ">
                AND t.model = #{params.model}
            </if>
                    <if test=" params.modelYear !=null and params.modelYear != '' ">
                AND t.model_year = #{params.modelYear}
            </if>
                    <if test=" params.buyDealerName !=null and params.buyDealerName != '' ">
                AND t.buy_dealer_name = #{params.buyDealerName}
            </if>
                    <if test=" params.dealerName !=null and params.dealerName != '' ">
                AND t.dealer_name = #{params.dealerName}
            </if>
                    <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code = #{params.dealerCode}
            </if>
                    <if test=" params.mileage !=null and params.mileage != '' ">
                AND t.mileage = #{params.mileage}
            </if>
                    <if test=" params.replyTel !=null and params.replyTel != '' ">
                AND t.reply_tel = #{params.replyTel}
            </if>
                    <if test=" params.replyTel2 !=null and params.replyTel2 != '' ">
                AND t.reply_tel2 = #{params.replyTel2}
            </if>
                    <if test=" params.subject !=null and params.subject != '' ">
                AND t.subject = #{params.subject}
            </if>
                    <if test=" params.subdivisionPart !=null and params.subdivisionPart != '' ">
                AND t.subdivision_part = #{params.subdivisionPart}
            </if>
                    <if test=" params.part !=null and params.part != '' ">
                AND t.part = #{params.part}
            </if>
                    <if test=" params.problem !=null and params.problem != '' ">
                AND t.problem = #{params.problem}
            </if>
                    <if test=" params.illustrate !=null and params.illustrate != '' ">
                AND t.illustrate = #{params.illustrate}
            </if>
                    <if test=" params.category1 !=null and params.category1 != '' ">
                AND t.category1 = #{params.category1}
            </if>
                    <if test=" params.category2 !=null and params.category2 != '' ">
                AND t.category2 = #{params.category2}
            </if>
                    <if test=" params.category3 !=null and params.category3 != '' ">
                AND t.category3 = #{params.category3}
            </if>
                    <if test=" params.ccPart !=null and params.ccPart != '' ">
                AND t.cc_part = #{params.ccPart}
            </if>
                    <if test=" params.ccSubdivisionPart !=null and params.ccSubdivisionPart != '' ">
                AND t.cc_subdivision_part = #{params.ccSubdivisionPart}
            </if>
                    <if test=" params.ccProblem !=null and params.ccProblem != '' ">
                AND t.cc_problem = #{params.ccProblem}
            </if>
                    <if test=" params.ccRequirement !=null and params.ccRequirement != '' ">
                AND t.cc_requirement = #{params.ccRequirement}
            </if>
                    <if test=" params.department !=null and params.department != '' ">
                AND t.department = #{params.department}
            </if>
                    <if test=" params.receptionist !=null and params.receptionist != '' ">
                AND t.receptionist = #{params.receptionist}
            </if>
                    <if test=" params.importanceLevel !=null and params.importanceLevel != '' ">
                AND t.importance_level = #{params.importanceLevel}
            </if>
                    <if test=" params.dealerFisrtReplyTime !=null and params.dealerFisrtReplyTime != '' ">
                AND t.dealer_fisrt_reply_time = #{params.dealerFisrtReplyTime}
            </if>
                    <if test=" params.fisrtRestartDealerFisrtReplyTime !=null and params.fisrtRestartDealerFisrtReplyTime != '' ">
                AND t.fisrt_restart_dealer_fisrt_reply_time = #{params.fisrtRestartDealerFisrtReplyTime}
            </if>
                    <if test=" params.isRevisit !=null and params.isRevisit != '' ">
                AND t.is_revisit = #{params.isRevisit}
            </if>
                    <if test=" params.revisitTime !=null and params.revisitTime != '' ">
                AND t.revisit_time = #{params.revisitTime}
            </if>
                    <if test=" params.revisitResult !=null and params.revisitResult != '' ">
                AND t.revisit_result = #{params.revisitResult}
            </if>
                    <if test=" params.revisitContent !=null and params.revisitContent != '' ">
                AND t.revisit_content = #{params.revisitContent}
            </if>
                    <if test=" params.applyTime !=null and params.applyTime != '' ">
                AND t.apply_time = #{params.applyTime}
            </if>
                    <if test=" params.isAgree !=null and params.isAgree != '' ">
                AND t.is_agree = #{params.isAgree}
            </if>
                    <if test=" params.submitTime !=null and params.submitTime != '' ">
                AND t.submit_time = #{params.submitTime}
            </if>
                    <if test=" params.closeCaseTime !=null and params.closeCaseTime != '' ">
                AND t.close_case_time = #{params.closeCaseTime}
            </if>
                    <if test=" params.restartCloseCaseTime !=null and params.restartCloseCaseTime != '' ">
                AND t.restart_close_case_time = #{params.restartCloseCaseTime}
            </if>
                    <if test=" params.workStatus !=null and params.workStatus != '' ">
                AND t.work_status = #{params.workStatus}
            </if>
                    <if test=" params.closeCaseStatus !=null and params.closeCaseStatus != '' ">
                AND t.close_case_status = #{params.closeCaseStatus}
            </if>
                    <if test=" params.followStatus !=null and params.followStatus != '' ">
                AND t.follow_status = #{params.followStatus}
            </if>
                    <if test=" params.basicReason !=null and params.basicReason != '' ">
                AND t.basic_reason = #{params.basicReason}
            </if>
                    <if test=" params.isRepaired !=null and params.isRepaired != '' ">
                AND t.is_repaired = #{params.isRepaired}
            </if>
                    <if test=" params.techMaintainPlan !=null and params.techMaintainPlan != '' ">
                AND t.tech_maintain_plan = #{params.techMaintainPlan}
            </if>
                    <if test=" params.rapportPlan !=null and params.rapportPlan != '' ">
                AND t.rapport_plan = #{params.rapportPlan}
            </if>
                    <if test=" params.risk !=null and params.risk != '' ">
                AND t.risk = #{params.risk}
            </if>
                    <if test=" params.dealerIsRead !=null and params.dealerIsRead != '' ">
                AND t.dealer_is_read = #{params.dealerIsRead}
            </if>
                    <if test=" params.managerIsRead !=null and params.managerIsRead != '' ">
                AND t.manager_is_read = #{params.managerIsRead}
            </if>
                    <if test=" params.ccmIsRead !=null and params.ccmIsRead != '' ">
                AND t.ccm_is_read = #{params.ccmIsRead}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isReport !=null and params.isReport != '' ">
                AND t.is_report = #{params.isReport}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
                    <if test=" params.sex !=null and params.sex != '' ">
                AND t.sex = #{params.sex}
            </if>
                    <if test=" params.ownerAddress !=null and params.ownerAddress != '' ">
                AND t.owner_address = #{params.ownerAddress}
            </if>
                    <if test=" params.workOrderStatus !=null and params.workOrderStatus != '' ">
                AND t.work_order_status = #{params.workOrderStatus}
            </if>
                    <if test=" params.isCloseCase !=null and params.isCloseCase != '' ">
                AND t.is_close_case = #{params.isCloseCase}
            </if>
                    <if test=" params.regionalManagerComments !=null and params.regionalManagerComments != '' ">
                AND t.regional_manager_comments = #{params.regionalManagerComments}
            </if>
                    <if test=" params.region !=null and params.region != '' ">
                AND t.region = #{params.region}
            </if>
                    <if test=" params.regionManager !=null and params.regionManager != '' ">
                AND t.region_manager = #{params.regionManager}
            </if>
                    <if test=" params.regionId !=null and params.regionId != '' ">
                AND t.region_id = #{params.regionId}
            </if>
                    <if test=" params.regionManagerId !=null and params.regionManagerId != '' ">
                AND t.region_manager_id = #{params.regionManagerId}
            </if>
                    <if test=" params.bloc !=null and params.bloc != '' ">
                AND t.bloc = #{params.bloc}
            </if>
                    <if test=" params.buyDealerCode !=null and params.buyDealerCode != '' ">
                AND t.buy_dealer_code = #{params.buyDealerCode}
            </if>
                    <if test=" params.buyRegion !=null and params.buyRegion != '' ">
                AND t.buy_region = #{params.buyRegion}
            </if>
                    <if test=" params.buyRegionManager !=null and params.buyRegionManager != '' ">
                AND t.buy_region_manager = #{params.buyRegionManager}
            </if>
                    <if test=" params.buyRegionId !=null and params.buyRegionId != '' ">
                AND t.buy_region_id = #{params.buyRegionId}
            </if>
                    <if test=" params.buyRegionManagerId !=null and params.buyRegionManagerId != '' ">
                AND t.buy_region_manager_id = #{params.buyRegionManagerId}
            </if>
                    <if test=" params.buyBloc !=null and params.buyBloc != '' ">
                AND t.buy_bloc = #{params.buyBloc}
            </if>
                    <if test=" params.blocId !=null and params.blocId != '' ">
                AND t.bloc_id = #{params.blocId}
            </if>
                    <if test=" params.isSatisfied !=null and params.isSatisfied != '' ">
                AND t.is_satisfied = #{params.isSatisfied}
            </if>
                    <if test=" params.workOrderNature !=null and params.workOrderNature != '' ">
                AND t.work_order_nature = #{params.workOrderNature}
            </if>
                    <if test=" params.workOrderClassification !=null and params.workOrderClassification != '' ">
                AND t.work_order_classification = #{params.workOrderClassification}
            </if>
                    <if test=" params.serviceCommitment !=null and params.serviceCommitment != '' ">
                AND t.service_commitment = #{params.serviceCommitment}
            </if>
                    <if test=" params.replyName !=null and params.replyName != '' ">
                AND t.reply_name = #{params.replyName}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_info t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.complaintId !=null and params.complaintId != '' ">
                AND t.complaint_id = #{params.complaintId}
            </if>
                    <if test=" params.type !=null and params.type != '' ">
                AND t.type = #{params.type}
            </if>
                    <if test=" params.classification !=null and params.classification != '' ">
                AND t.classification = #{params.classification}
            </if>
                    <if test=" params.source !=null and params.source != '' ">
                AND t.source = #{params.source}
            </if>
                    <if test=" params.callTime !=null and params.callTime != '' ">
                AND t.call_time = #{params.callTime}
            </if>
                    <if test=" params.fisrtRestartTime !=null and params.fisrtRestartTime != '' ">
                AND t.fisrt_restart_time = #{params.fisrtRestartTime}
            </if>
                    <if test=" params.newestRestartTime !=null and params.newestRestartTime != '' ">
                AND t.newest_restart_time = #{params.newestRestartTime}
            </if>
                    <if test=" params.callName !=null and params.callName != '' ">
                AND t.call_name = #{params.callName}
            </if>
                    <if test=" params.callTel !=null and params.callTel != '' ">
                AND t.call_tel = #{params.callTel}
            </if>
                    <if test=" params.name !=null and params.name != '' ">
                AND t.name = #{params.name}
            </if>
                    <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
                AND t.license_plate_num = #{params.licensePlateNum}
            </if>
                    <if test=" params.vin !=null and params.vin != '' ">
                AND t.vin = #{params.vin}
            </if>
                    <if test=" params.buyTime !=null and params.buyTime != '' ">
                AND t.buy_time = #{params.buyTime}
            </if>
                    <if test=" params.model !=null and params.model != '' ">
                AND t.model = #{params.model}
            </if>
                    <if test=" params.modelYear !=null and params.modelYear != '' ">
                AND t.model_year = #{params.modelYear}
            </if>
                    <if test=" params.buyDealerName !=null and params.buyDealerName != '' ">
                AND t.buy_dealer_name = #{params.buyDealerName}
            </if>
                    <if test=" params.dealerName !=null and params.dealerName != '' ">
                AND t.dealer_name = #{params.dealerName}
            </if>
                    <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code = #{params.dealerCode}
            </if>
                    <if test=" params.mileage !=null and params.mileage != '' ">
                AND t.mileage = #{params.mileage}
            </if>
                    <if test=" params.replyTel !=null and params.replyTel != '' ">
                AND t.reply_tel = #{params.replyTel}
            </if>
                    <if test=" params.replyTel2 !=null and params.replyTel2 != '' ">
                AND t.reply_tel2 = #{params.replyTel2}
            </if>
                    <if test=" params.subject !=null and params.subject != '' ">
                AND t.subject = #{params.subject}
            </if>
                    <if test=" params.subdivisionPart !=null and params.subdivisionPart != '' ">
                AND t.subdivision_part = #{params.subdivisionPart}
            </if>
                    <if test=" params.part !=null and params.part != '' ">
                AND t.part = #{params.part}
            </if>
                    <if test=" params.problem !=null and params.problem != '' ">
                AND t.problem = #{params.problem}
            </if>
                    <if test=" params.illustrate !=null and params.illustrate != '' ">
                AND t.illustrate = #{params.illustrate}
            </if>
                    <if test=" params.category1 !=null and params.category1 != '' ">
                AND t.category1 = #{params.category1}
            </if>
                    <if test=" params.category2 !=null and params.category2 != '' ">
                AND t.category2 = #{params.category2}
            </if>
                    <if test=" params.category3 !=null and params.category3 != '' ">
                AND t.category3 = #{params.category3}
            </if>
                    <if test=" params.ccPart !=null and params.ccPart != '' ">
                AND t.cc_part = #{params.ccPart}
            </if>
                    <if test=" params.ccSubdivisionPart !=null and params.ccSubdivisionPart != '' ">
                AND t.cc_subdivision_part = #{params.ccSubdivisionPart}
            </if>
                    <if test=" params.ccProblem !=null and params.ccProblem != '' ">
                AND t.cc_problem = #{params.ccProblem}
            </if>
                    <if test=" params.ccRequirement !=null and params.ccRequirement != '' ">
                AND t.cc_requirement = #{params.ccRequirement}
            </if>
                    <if test=" params.department !=null and params.department != '' ">
                AND t.department = #{params.department}
            </if>
                    <if test=" params.receptionist !=null and params.receptionist != '' ">
                AND t.receptionist = #{params.receptionist}
            </if>
                    <if test=" params.importanceLevel !=null and params.importanceLevel != '' ">
                AND t.importance_level = #{params.importanceLevel}
            </if>
                    <if test=" params.dealerFisrtReplyTime !=null and params.dealerFisrtReplyTime != '' ">
                AND t.dealer_fisrt_reply_time = #{params.dealerFisrtReplyTime}
            </if>
                    <if test=" params.fisrtRestartDealerFisrtReplyTime !=null and params.fisrtRestartDealerFisrtReplyTime != '' ">
                AND t.fisrt_restart_dealer_fisrt_reply_time = #{params.fisrtRestartDealerFisrtReplyTime}
            </if>
                    <if test=" params.isRevisit !=null and params.isRevisit != '' ">
                AND t.is_revisit = #{params.isRevisit}
            </if>
                    <if test=" params.revisitTime !=null and params.revisitTime != '' ">
                AND t.revisit_time = #{params.revisitTime}
            </if>
                    <if test=" params.revisitResult !=null and params.revisitResult != '' ">
                AND t.revisit_result = #{params.revisitResult}
            </if>
                    <if test=" params.revisitContent !=null and params.revisitContent != '' ">
                AND t.revisit_content = #{params.revisitContent}
            </if>
                    <if test=" params.applyTime !=null and params.applyTime != '' ">
                AND t.apply_time = #{params.applyTime}
            </if>
                    <if test=" params.isAgree !=null and params.isAgree != '' ">
                AND t.is_agree = #{params.isAgree}
            </if>
                    <if test=" params.submitTime !=null and params.submitTime != '' ">
                AND t.submit_time = #{params.submitTime}
            </if>
                    <if test=" params.closeCaseTime !=null and params.closeCaseTime != '' ">
                AND t.close_case_time = #{params.closeCaseTime}
            </if>
                    <if test=" params.restartCloseCaseTime !=null and params.restartCloseCaseTime != '' ">
                AND t.restart_close_case_time = #{params.restartCloseCaseTime}
            </if>
                    <if test=" params.workStatus !=null and params.workStatus != '' ">
                AND t.work_status = #{params.workStatus}
            </if>
                    <if test=" params.closeCaseStatus !=null and params.closeCaseStatus != '' ">
                AND t.close_case_status = #{params.closeCaseStatus}
            </if>
                    <if test=" params.followStatus !=null and params.followStatus != '' ">
                AND t.follow_status = #{params.followStatus}
            </if>
                    <if test=" params.basicReason !=null and params.basicReason != '' ">
                AND t.basic_reason = #{params.basicReason}
            </if>
                    <if test=" params.isRepaired !=null and params.isRepaired != '' ">
                AND t.is_repaired = #{params.isRepaired}
            </if>
                    <if test=" params.techMaintainPlan !=null and params.techMaintainPlan != '' ">
                AND t.tech_maintain_plan = #{params.techMaintainPlan}
            </if>
                    <if test=" params.rapportPlan !=null and params.rapportPlan != '' ">
                AND t.rapport_plan = #{params.rapportPlan}
            </if>
                    <if test=" params.risk !=null and params.risk != '' ">
                AND t.risk = #{params.risk}
            </if>
                    <if test=" params.dealerIsRead !=null and params.dealerIsRead != '' ">
                AND t.dealer_is_read = #{params.dealerIsRead}
            </if>
                    <if test=" params.managerIsRead !=null and params.managerIsRead != '' ">
                AND t.manager_is_read = #{params.managerIsRead}
            </if>
                    <if test=" params.ccmIsRead !=null and params.ccmIsRead != '' ">
                AND t.ccm_is_read = #{params.ccmIsRead}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isReport !=null and params.isReport != '' ">
                AND t.is_report = #{params.isReport}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.isValid !=null and params.isValid != '' ">
                AND t.is_valid = #{params.isValid}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
                    <if test=" params.sex !=null and params.sex != '' ">
                AND t.sex = #{params.sex}
            </if>
                    <if test=" params.ownerAddress !=null and params.ownerAddress != '' ">
                AND t.owner_address = #{params.ownerAddress}
            </if>
                    <if test=" params.workOrderStatus !=null and params.workOrderStatus != '' ">
                AND t.work_order_status = #{params.workOrderStatus}
            </if>
                    <if test=" params.isCloseCase !=null and params.isCloseCase != '' ">
                AND t.is_close_case = #{params.isCloseCase}
            </if>
                    <if test=" params.regionalManagerComments !=null and params.regionalManagerComments != '' ">
                AND t.regional_manager_comments = #{params.regionalManagerComments}
            </if>
                    <if test=" params.region !=null and params.region != '' ">
                AND t.region = #{params.region}
            </if>
                    <if test=" params.regionManager !=null and params.regionManager != '' ">
                AND t.region_manager = #{params.regionManager}
            </if>
                    <if test=" params.regionId !=null and params.regionId != '' ">
                AND t.region_id = #{params.regionId}
            </if>
                    <if test=" params.regionManagerId !=null and params.regionManagerId != '' ">
                AND t.region_manager_id = #{params.regionManagerId}
            </if>
                    <if test=" params.bloc !=null and params.bloc != '' ">
                AND t.bloc = #{params.bloc}
            </if>
                    <if test=" params.buyDealerCode !=null and params.buyDealerCode != '' ">
                AND t.buy_dealer_code = #{params.buyDealerCode}
            </if>
                    <if test=" params.buyRegion !=null and params.buyRegion != '' ">
                AND t.buy_region = #{params.buyRegion}
            </if>
                    <if test=" params.buyRegionManager !=null and params.buyRegionManager != '' ">
                AND t.buy_region_manager = #{params.buyRegionManager}
            </if>
                    <if test=" params.buyRegionId !=null and params.buyRegionId != '' ">
                AND t.buy_region_id = #{params.buyRegionId}
            </if>
                    <if test=" params.buyRegionManagerId !=null and params.buyRegionManagerId != '' ">
                AND t.buy_region_manager_id = #{params.buyRegionManagerId}
            </if>
                    <if test=" params.buyBloc !=null and params.buyBloc != '' ">
                AND t.buy_bloc = #{params.buyBloc}
            </if>
                    <if test=" params.blocId !=null and params.blocId != '' ">
                AND t.bloc_id = #{params.blocId}
            </if>
                    <if test=" params.isSatisfied !=null and params.isSatisfied != '' ">
                AND t.is_satisfied = #{params.isSatisfied}
            </if>
                    <if test=" params.workOrderNature !=null and params.workOrderNature != '' ">
                AND t.work_order_nature = #{params.workOrderNature}
            </if>
                    <if test=" params.workOrderClassification !=null and params.workOrderClassification != '' ">
                AND t.work_order_classification = #{params.workOrderClassification}
            </if>
                    <if test=" params.serviceCommitment !=null and params.serviceCommitment != '' ">
                AND t.service_commitment = #{params.serviceCommitment}
            </if>
                    <if test=" params.replyName !=null and params.replyName != '' ">
                AND t.reply_name = #{params.replyName}
            </if>
            </select>

    <select id="queryid" resultMap="BaseResultMap"
            parameterType="com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoPO">
        SELECT
        id
        FROM tt_sale_complaint_info t
        WHERE 1=1
        <if test=" params.complaintId !=null and params.complaintId != '' ">
            AND t.complaint_id = #{params.complaintId}
        </if>
        and t.is_deleted=0

    </select>
    <select id="selectCusByDeal" resultType="com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO">
        SELECT
        distinct t.id,
        t.work_order_nature,
        t.complaint_id,
        t.call_time,
        t.close_case_time,
        t.region as handleRegion,
        t.region_manager AS handleRegionManager,
        t.dealer_code,
        t.dealer_name AS handleDealName,
        t.bloc AS handleBloc,
        t.subject,
        t.source,
        t.type,
        t.importance_level,
        t.work_order_status,
        t.close_case_status,
        t.call_name,
        t.call_tel,
        t.model,
        t.model_year,
        t.license_plate_num,
        t.is_close_case,
        t.data_sources,
        t.vin,
        t.newest_restart_time as "newestRestartTime",
        t.fisrt_restart_time,
        tsce.case_create_time as caseCreateTime,
        tsce.risk_type as riskType
        FROM tt_sale_complaint_info t
        LEFT JOIN tt_complaint_dealer_ccm_ref t2 on t.dealer_code=t2.dealer_code
        LEFT join tt_sale_complaint_follow t3 on t.id=t3.complaint_info_id
        LEFT join tt_sale_complaint_not_close_case_reason t4 on t.id=t4.complaint_info_id
        LEFT JOIN tt_sale_complaint_ext tsce on t.id = tsce.sale_complaint_id
        WHERE 1=1
--         and t.work_order_nature=83611001
        <!--<if test=" params.assisDepartment !=null and params.assisDepartment != ''  ">-->
        <!--and   t5.is_valid =10041001-->
        <!--</if>-->
        <!--<if test=" params.isValid1 !=null and params.isValid1 != ''  ">-->
        <!--and   t5.is_valid =#{params.isValid1}-->
        <!--</if>-->
        <if test=" params.smallClass !=null and params.smallClass != ''  ">
            and (${params.smallClass})
        </if>
        <if test=" params.assisDepartment !=null and params.assisDepartment != ''  ">
            and t.id in (select complaint_info_id from tt_complaint_assist_department where assist_department
            =#{params.assisDepartment} )
        </if>

        <if test=" params.classification3 !=null and params.classification3 != ''  ">
            AND t3.classification3 like "%" #{params.classification3} "%"
        </if>
        <if test=" params.classification21 !=null and params.classification21 != ''  ">
            AND (${params.classification21})
        </if>
        <if test=" params.ccResult !=null and params.ccResult != ''  ">
            AND (${params.ccResult})
        </if>
        <if test=" params.classification11 !=null and params.classification11 != ''  ">
            AND (${params.classification11})
        </if>
        <if test=" params.ccmSubdivisionPart !=null and params.ccmSubdivisionPart != ''  ">
            AND (${params.ccmSubdivisionPart})
        </if>
        <if test=" params.ccmPart !=null and params.ccmPart != '' ">
            AND (${params.ccmPart})
        </if>
        <if test=" params.ccMainReason and params.ccMainReason != ''  ">
            AND (${params.ccMainReason})
        </if>
        <if test=" params.planFollowTime1 !=null ">
            AND t3.plan_follow_time >= #{params.planFollowTime1}
        </if>
        <if test=" params.planFollowTime2 !=null ">
            AND t3.plan_follow_time &lt;= #{params.planFollowTime2}
        </if>
        <if test=" params.actuallFollowTime !=null ">
            AND t3.actuall_follow_time2 >= #{params.actuallFollowTime}
        </if>
        <if test=" params.workOrderNature !=null and params.workOrderNature != '' ">
            AND t.work_order_nature = #{params.workOrderNature}
        </if>
        <if test=" params.actuallFollowTime1 !=null ">
            AND t3.actuall_follow_time2 &lt;= #{params.actuallFollowTime1}
        </if>
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.complaintId !=null and params.complaintId != '' ">
            AND t.complaint_id = #{params.complaintId}
        </if>
        <if test=" params.type !=null and params.type != '' ">
            AND t.type in (${params.type})
        </if>
        <if test=" params.classification !=null and params.classification != '' ">
            AND t.classification = #{params.classification}
        </if>
        <if test=" params.source !=null and params.source != '' ">
            AND t.source in (${params.source})
        </if>
        <!--调整售前案件时间-->
        <if test=" params.callTime !=null ">
            AND tsce.case_create_time >= #{params.callTime}
        </if>
        <if test=" params.callTime2 !=null ">
            AND tsce.case_create_time &lt;= #{params.callTime2}
        </if>
        <if test=" params.fisrtRestartTime !=null  ">
            AND t.fisrt_restart_time >= #{params.fisrtRestartTime}
        </if>
        <if test=" params.fisrtRestartTime2 !=null  ">
            AND t.fisrt_restart_time &lt;= #{params.fisrtRestartTime2}
        </if>
        <if test=" params.newestRestartTime !=null and params.newestRestartTime != '' ">
            AND t.newest_restart_time = #{params.newestRestartTime}
        </if>
        <if test=" params.callName !=null and params.callName != '' ">
            AND t.call_name = #{params.callName}
        </if>
        <if test=" params.callTel !=null and params.callTel != '' ">
            AND t.call_tel = #{params.callTel}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num = #{params.licensePlateNum}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.buyTime !=null and params.buyTime != '' ">
            AND t.buy_time = #{params.buyTime}
        </if>
        <if test=" params.model !=null and params.model != '' ">
            AND t.model in(${params.model})
        </if>
        <if test=" params.blocId !=null and params.blocId != '' ">
            AND t.bloc_id in(${params.blocId})
        </if>
        <if test=" params.regionId !=null and params.regionId != '' ">
            AND t.region_id in(${params.regionId})
        </if>
        <if test=" params.regionManagerId !=null and params.regionManagerId != '' ">
            AND t.region_manager_id in(${params.regionManagerId})
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code in(${params.dealerCode})
        </if>
        <if test=" params.ccmManId !=null and params.ccmManId != '' ">
            AND t.dealer_code in(select dealer_code from tt_complaint_dealer_ccm_ref where ccm_man_id in
            (${params.ccmManId}) )
        </if>
        <if test=" params.modelYear !=null and params.modelYear != '' ">
            AND t.model_year = #{params.modelYear}
        </if>
        <if test=" params.buyDealerName !=null and params.buyDealerName != '' ">
            AND t.buy_dealer_name = #{params.buyDealerName}
        </if>
        <if test=" params.dealerName !=null and params.dealerName != '' ">
            AND t.dealer_name = #{params.dealerName}
        </if>
        <if test=" params.mileage !=null and params.mileage != '' ">
            AND t.mileage = #{params.mileage}
        </if>
        <if test=" params.replyTel !=null and params.replyTel != '' ">
            AND t.reply_tel = #{params.replyTel}
        </if>
        <if test=" params.replyTel2 !=null and params.replyTel2 != '' ">
            AND t.reply_tel2 = #{params.replyTel2}
        </if>
        <if test=" params.subject !=null and params.subject != '' ">
            AND t.subject like "%" #{params.subject} "%"
        </if>
        <if test=" params.problem !=null and params.problem != '' ">
            AND t.problem = #{params.problem}
        </if>
        <if test=" params.illustrate !=null and params.illustrate != '' ">
            AND t.illustrate = #{params.illustrate}
        </if>
        <if test=" params.category1 !=null and params.category2 != '' ">
            AND (${params.category1})
        </if>
        <if test=" params.category2 !=null and params.category2 != '' ">
            AND (${params.category2})
        </if>
        <if test=" params.category3 !=null and params.category3 != '' ">
            AND (${params.category3})
        </if>
        <if test=" params.ccPart !=null and params.ccPart != '' ">
            AND t.cc_part = #{params.ccPart}
        </if>
        <if test=" params.ccSubdivisionPart !=null and params.ccSubdivisionPart != '' ">
            AND t.cc_subdivision_part = #{params.ccSubdivisionPart}
        </if>
        <if test=" params.ccProblem !=null and params.ccProblem != '' ">
            AND t.cc_problem = #{params.ccProblem}
        </if>
        <if test=" params.ccRequirement !=null and params.ccRequirement != '' ">
            AND t.cc_requirement = #{params.ccRequirement}
        </if>
        <if test=" params.department !=null and params.department != '' ">
            AND t.department = #{params.department}
        </if>
        <if test=" params.receptionist !=null and params.receptionist != '' ">
            AND t.receptionist = #{params.receptionist}
        </if>
        <if test=" params.importanceLevelData !=null and params.importanceLevelData != '' ">
            AND t.importance_level in (${params.importanceLevelData})
        </if>
        <if test=" params.dealerFisrtReplyTime !=null and params.dealerFisrtReplyTime != '' ">
            AND t.dealer_fisrt_reply_time = #{params.dealerFisrtReplyTime}
        </if>
        <if test=" params.fisrtRestartDealerFisrtReplyTime !=null and params.fisrtRestartDealerFisrtReplyTime != '' ">
            AND t.fisrt_restart_dealer_fisrt_reply_time = #{params.fisrtRestartDealerFisrtReplyTime}
        </if>
        <if test=" params.isRevisit !=null and params.isRevisit != '' ">
            AND t.is_revisit = #{params.isRevisit}
        </if>
        <if test=" params.revisitTime !=null and params.revisitTime != '' ">
            AND t.revisit_time = #{params.revisitTime}
        </if>
        <if test=" params.revisitResult !=null and params.revisitResult != '' ">
            AND t.revisit_result = #{params.revisitResult}
        </if>
        <if test=" params.revisitContent !=null and params.revisitContent != '' ">
            AND t.revisit_content = #{params.revisitContent}
        </if>
        <if test=" params.applyTime !=null and params.applyTime != '' ">
            AND t.apply_time = #{params.applyTime}
        </if>
        <if test=" params.isAgree !=null and params.isAgree != '' ">
            AND t.is_agree = #{params.isAgree}
        </if>
        <if test=" params.submitTime !=null and params.submitTime != '' ">
            AND t.submit_time = #{params.submitTime}
        </if>
        <if test=" params.closeCaseTime !=null and params.closeCaseTime != '' ">
            AND t.close_case_time = #{params.closeCaseTime}
        </if>
        <if test=" params.restartCloseCaseTime !=null and params.restartCloseCaseTime != '' ">
            AND t.restart_close_case_time = #{params.restartCloseCaseTime}
        </if>
        <if test=" params.closeCaseStatus !=null and params.closeCaseStatus != '' ">
            AND t.close_case_status = #{params.closeCaseStatus}
        </if>
        <if test=" params.workOrderStatusData !=null and params.workOrderStatusData != '' ">
            AND t.work_order_status in (${params.workOrderStatusData})
        </if>
        <if test=" params.followStatus !=null and params.followStatus != '' ">
            AND ${params.followStatus}
        </if>
        <if test=" params.basicReason !=null and params.basicReason != '' ">
            AND t.basic_reason = #{params.basicReason}
        </if>
        <if test=" params.isRepaired !=null and params.isRepaired != '' ">
            AND t.is_repaired = #{params.isRepaired}
        </if>
        <if test=" params.techMaintainPlan !=null and params.techMaintainPlan != '' ">
            AND t.tech_maintain_plan = #{params.techMaintainPlan}
        </if>
        <if test=" params.rapportPlan !=null and params.rapportPlan != '' ">
            AND t.rapport_plan = #{params.rapportPlan}
        </if>
        <if test=" params.risk !=null and params.risk != '' ">
            AND t.risk = #{params.risk}
        </if>
        <if test=" params.dealerIsRead !=null and params.dealerIsRead != '' ">
            AND t.dealer_is_read = #{params.dealerIsRead}
        </if>
        <if test=" params.managerIsRead !=null and params.managerIsRead != '' ">
            AND t.manager_is_read = #{params.managerIsRead}
        </if>
        <if test=" params.ccmIsRead !=null and params.ccmIsRead != '' ">
            AND t.ccm_is_read = #{params.ccmIsRead}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isCloseCase !=null and params.isCloseCase != '' ">
            AND t.is_close_case = #{params.isCloseCase}
        </if>
        <if test=" params.isReport !=null ">
            AND t.is_report = #{params.isReport}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.isAnonymous !=null and params.isAnonymous != '' ">
            AND t.is_anonymous = #{params.isAnonymous}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>

        <if test=" params.sql1 !=null and params.sql1 != '' ">
            and ${params.sql1}
        </if>
        and t.is_deleted=0
        <if test=" params.sql !=null and params.sql != '' ">
            ${params.sql}
        </if>
    </select>

    <select id="queryDealerFirstReplyTime" resultMap="BaseResultMap" parameterType="long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_info t
        WHERE 1=1
        AND t.id = #{complaintInfoId}
        and t.is_deleted=0

    </select>

    <select id="queryComplaintData" resultMap="BaseResultMap" parameterType="long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_sale_complaint_info t
        WHERE 1=1
        AND t.id = #{params.id}
        and t.is_deleted=0

    </select>
    <update id="restart">
        UPDATE  tt_sale_complaint_info  set close_case_time=null , restart_close_case_time=null ,close_case_status=null
         where  1=1 AND id = #{id}
        and is_deleted=0
    </update>


    <select id="selectCusByDealAll" resultMap="BaseResultMap1"
            parameterType="com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfMorePO">
        SELECT
        distinct t.id,
        DATE_FORMAT(t.call_time,'%Y-%m-%d %H:%i:%s') as call_time,
        t.complaint_id,
        t6.cate_name as workOrderNature,
        case t.is_close_case
        when 10041001 then DATEDIFF(t.call_time,now())+5
        else DATEDIFF(t.call_time,t.close_case_time)+5 end as closeCaseDays,
        t.region as handleRegion,
        t.region_manager AS handleRegionManager,
        t.buy_dealer_code AS buyDealCode,
        t.buy_dealer_name AS buyDealName,
        t.dealer_code AS handleDealCode,
        t.dealer_name AS handleDealName,
        t7.cate_name as category1,
        t8.cate_name as category2,
        t9.cate_name as category3,
        t.subject as `subject`,
        t.source as source,
        t10.type_name as type,
        t.importance_level,
        t.work_order_status,
        t.close_case_status,
        t.name,
        t.call_tel,
        t.model,
        t.model_year,
        t.license_plate_num,
        t.is_close_case,
        t.data_sources,
        t.newest_restart_time,
        (SELECT group_concat(t5.class_name separator ',') from tt_sale_complaint_not_close_case_reason t5 where
        t5.complaint_info_id=t.id ) AS smallClassNameOther,
        t.reply_tel,
        t.reply_tel2,
        t.buy_time,
        t.mileage,
        t.problem,
        t.dealer_fisrt_reply_time,
        t.close_case_time,
        (select t11.follow_time from tt_sale_complaint_follow t11 where t11.complaint_info_id=t.id order by created_at DESC LIMIT 1 ) as lastFollowTime,
        t.newest_restart_time,
        CASE
        WHEN TIMESTAMPDIFF(
        DAY,
        t.call_time,
        t.close_case_time
        ) &lt;= 5 then 10041001
        else 10041002 end as isFiveCloseCase,
        CASE
        WHEN TIMESTAMPDIFF(
        HOUR,
        t.call_time,
        t.dealer_fisrt_reply_time
        ) &lt;= 24 then 10041001
        else 10041002 end as is24HourReply,
        CASE
        WHEN TIMESTAMPDIFF(
        HOUR,
        t.call_time,
        t.dealer_fisrt_reply_time
        ) &lt;= 48 then 10041001
        else 10041002 end as is48HourReply
        FROM tt_sale_complaint_info t
        LEFT JOIN tt_complaint_dealer_ccm_ref t2 on t.dealer_code=t2.dealer_code
        LEFT join tt_sale_complaint_follow t3 on t.id=t3.complaint_info_id
        LEFT join tt_sale_complaint_not_close_case_reason t4 on t.id=t4.complaint_info_id
        LEFT join tt_complaint_classification t6 on t.work_order_nature=t6.id
        left join tt_complaint_classification t7 on t7.id=t.category1
        left join tt_complaint_classification t8 on t8.id=t.category2
        left join tt_complaint_classification t9 on t9.id=t.category3
        LEFT JOIN tt_sale_complaint_type t10 on t10.id=t.type
        WHERE 1=1
        <if test=" params.smallClass !=null and params.smallClass != ''  ">
            and (${params.smallClass})
        </if>
        <if test=" params.assisDepartment !=null and params.assisDepartment != ''  ">
            and t.id in (select complaint_info_id from tt_complaint_assist_department where assist_department
            =#{params.assisDepartment} )
        </if>

        <if test=" params.classification3 !=null and params.classification3 != ''  ">
            AND t3.classification3 like "%" #{params.classification3} "%"
        </if>
        <if test=" params.classification21 !=null and params.classification21 != ''  ">
            AND (${params.classification21})
        </if>
        <if test=" params.ccResult !=null and params.ccResult != ''  ">
            AND (${params.ccResult})
        </if>
        <if test=" params.classification11 !=null and params.classification11 != ''  ">
            AND (${params.classification11})
        </if>
        <if test=" params.ccmSubdivisionPart !=null and params.ccmSubdivisionPart != ''  ">
            AND (${params.ccmSubdivisionPart})
        </if>
        <if test=" params.ccmPart !=null and params.ccmPart != '' ">
            AND (${params.ccmPart})
        </if>
        <if test=" params.ccMainReason and params.ccMainReason != ''  ">
            AND (${params.ccMainReason})
        </if>
        <if test=" params.planFollowTime1 !=null ">
            AND t3.plan_follow_time >= #{params.planFollowTime1}
        </if>
        <if test=" params.planFollowTime2 !=null ">
            AND t3.plan_follow_time &lt;= #{params.planFollowTime2}
        </if>
        <if test=" params.actuallFollowTime !=null ">
            AND t3.actuall_follow_time2 >= #{params.actuallFollowTime}
        </if>
        <if test=" params.actuallFollowTime1 !=null ">
            AND t3.actuall_follow_time2 &lt;= #{params.actuallFollowTime1}
        </if>
        <if test=" params.appId !=null and params.appId != '' ">
            AND t.app_id = #{params.appId}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.owner_code = #{params.ownerCode}
        </if>
        <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
            AND t.owner_par_code = #{params.ownerParCode}
        </if>
        <if test=" params.orgId !=null and params.orgId != '' ">
            AND t.org_id = #{params.orgId}
        </if>
        <if test=" params.id !=null and params.id != '' ">
            AND t.id = #{params.id}
        </if>
        <if test=" params.complaintId !=null and params.complaintId != '' ">
            AND t.complaint_id = #{params.complaintId}
        </if>
        <if test=" params.type !=null and params.type != '' ">
            AND t.type in (${params.type})
        </if>
        <if test=" params.classification !=null and params.classification != '' ">
            AND t.classification = #{params.classification}
        </if>
        <if test=" params.source !=null and params.source != '' ">
            AND t.source in (${params.source})
        </if>
        <if test=" params.callTime !=null ">
            AND t.call_time >= #{params.callTime}
        </if>
        <if test=" params.callTime2 !=null ">
            AND t.call_time &lt;= #{params.callTime2}
        </if>
        <if test=" params.fisrtRestartTime !=null  ">
            AND t.fisrt_restart_time >= #{params.fisrtRestartTime}
        </if>
        <if test=" params.fisrtRestartTime2 !=null  ">
            AND t.fisrt_restart_time &lt;= #{params.fisrtRestartTime2}
        </if>
        <if test=" params.newestRestartTime !=null and params.newestRestartTime != '' ">
            AND t.newest_restart_time = #{params.newestRestartTime}
        </if>
        <if test=" params.callName !=null and params.callName != '' ">
            AND t.call_name = #{params.callName}
        </if>
        <if test=" params.callTel !=null and params.callTel != '' ">
            AND t.call_tel = #{params.callTel}
        </if>
        <if test=" params.name !=null and params.name != '' ">
            AND t.name = #{params.name}
        </if>
        <if test=" params.licensePlateNum !=null and params.licensePlateNum != '' ">
            AND t.license_plate_num = #{params.licensePlateNum}
        </if>
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.buyTime !=null and params.buyTime != '' ">
            AND t.buy_time = #{params.buyTime}
        </if>
        <if test=" params.model !=null and params.model != '' ">
            AND t.model in(${params.model})
        </if>
        <if test=" params.blocId !=null and params.blocId != '' ">
            AND t.bloc_id in(${params.blocId})
        </if>
        <if test=" params.regionId !=null and params.regionId != '' ">
            AND t.region_id in(${params.regionId})
        </if>
        <if test=" params.regionManagerId !=null and params.regionManagerId != '' ">
            AND t.region_manager_id in(${params.regionManagerId})
        </if>
        <if test=" params.dealerCode !=null and params.dealerCode != '' ">
            AND t.dealer_code in(${params.dealerCode})
        </if>
        <if test=" params.ccmManId !=null and params.ccmManId != '' ">
            AND t.dealer_code in(select dealer_code from tt_complaint_dealer_ccm_ref where ccm_man_id in
            (${params.ccmManId}) )
        </if>
        <if test=" params.modelYear !=null and params.modelYear != '' ">
            AND t.model_year = #{params.modelYear}
        </if>
        <if test=" params.buyDealerName !=null and params.buyDealerName != '' ">
            AND t.buy_dealer_name = #{params.buyDealerName}
        </if>
        <if test=" params.dealerName !=null and params.dealerName != '' ">
            AND t.dealer_name = #{params.dealerName}
        </if>
        <if test=" params.mileage !=null and params.mileage != '' ">
            AND t.mileage = #{params.mileage}
        </if>
        <if test=" params.replyTel !=null and params.replyTel != '' ">
            AND t.reply_tel = #{params.replyTel}
        </if>
        <if test=" params.replyTel2 !=null and params.replyTel2 != '' ">
            AND t.reply_tel2 = #{params.replyTel2}
        </if>
        <if test=" params.subject !=null and params.subject != '' ">
            AND t.subject like "%" #{params.subject} "%"
        </if>
        <if test=" params.problem !=null and params.problem != '' ">
            AND t.problem = #{params.problem}
        </if>
        <if test=" params.illustrate !=null and params.illustrate != '' ">
            AND t.illustrate = #{params.illustrate}
        </if>
        <if test=" params.category1 !=null and params.category2 != '' ">
            AND (${params.category1})
        </if>
        <if test=" params.category2 !=null and params.category2 != '' ">
            AND (${params.category2})
        </if>
        <if test=" params.category3 !=null and params.category3 != '' ">
            AND (${params.category3})
        </if>
        <if test=" params.ccPart !=null and params.ccPart != '' ">
            AND t.cc_part = #{params.ccPart}
        </if>
        <if test=" params.ccSubdivisionPart !=null and params.ccSubdivisionPart != '' ">
            AND t.cc_subdivision_part = #{params.ccSubdivisionPart}
        </if>
        <if test=" params.ccProblem !=null and params.ccProblem != '' ">
            AND t.cc_problem = #{params.ccProblem}
        </if>
        <if test=" params.ccRequirement !=null and params.ccRequirement != '' ">
            AND t.cc_requirement = #{params.ccRequirement}
        </if>
        <if test=" params.department !=null and params.department != '' ">
            AND t.department = #{params.department}
        </if>
        <if test=" params.receptionist !=null and params.receptionist != '' ">
            AND t.receptionist = #{params.receptionist}
        </if>
        <if test=" params.importanceLevelData !=null and params.importanceLevelData != '' ">
            AND t.importance_level in (${params.importanceLevelData})
        </if>
        <if test=" params.dealerFisrtReplyTime !=null and params.dealerFisrtReplyTime != '' ">
            AND t.dealer_fisrt_reply_time = #{params.dealerFisrtReplyTime}
        </if>
        <if test=" params.fisrtRestartDealerFisrtReplyTime !=null and params.fisrtRestartDealerFisrtReplyTime != '' ">
            AND t.fisrt_restart_dealer_fisrt_reply_time = #{params.fisrtRestartDealerFisrtReplyTime}
        </if>
        <if test=" params.isRevisit !=null and params.isRevisit != '' ">
            AND t.is_revisit = #{params.isRevisit}
        </if>
        <if test=" params.revisitTime !=null and params.revisitTime != '' ">
            AND t.revisit_time = #{params.revisitTime}
        </if>
        <if test=" params.revisitResult !=null and params.revisitResult != '' ">
            AND t.revisit_result = #{params.revisitResult}
        </if>
        <if test=" params.revisitContent !=null and params.revisitContent != '' ">
            AND t.revisit_content = #{params.revisitContent}
        </if>
        <if test=" params.applyTime !=null and params.applyTime != '' ">
            AND t.apply_time = #{params.applyTime}
        </if>
        <if test=" params.isAgree !=null and params.isAgree != '' ">
            AND t.is_agree = #{params.isAgree}
        </if>
        <if test=" params.submitTime !=null and params.submitTime != '' ">
            AND t.submit_time = #{params.submitTime}
        </if>
        <if test=" params.closeCaseTime !=null and params.closeCaseTime != '' ">
            AND t.close_case_time = #{params.closeCaseTime}
        </if>
        <if test=" params.restartCloseCaseTime !=null and params.restartCloseCaseTime != '' ">
            AND t.restart_close_case_time = #{params.restartCloseCaseTime}
        </if>
        <if test=" params.closeCaseStatus !=null and params.closeCaseStatus != '' ">
            AND t.close_case_status = #{params.closeCaseStatus}
        </if>
        <if test=" params.workOrderStatusData !=null and params.workOrderStatusData != '' ">
            AND t.work_order_status in (${params.workOrderStatusData})
        </if>
        <if test=" params.followStatus !=null and params.followStatus != '' ">
            AND ${params.followStatus}
        </if>
        <if test=" params.basicReason !=null and params.basicReason != '' ">
            AND t.basic_reason = #{params.basicReason}
        </if>
        <if test=" params.isRepaired !=null and params.isRepaired != '' ">
            AND t.is_repaired = #{params.isRepaired}
        </if>
        <if test=" params.techMaintainPlan !=null and params.techMaintainPlan != '' ">
            AND t.tech_maintain_plan = #{params.techMaintainPlan}
        </if>
        <if test=" params.rapportPlan !=null and params.rapportPlan != '' ">
            AND t.rapport_plan = #{params.rapportPlan}
        </if>
        <if test=" params.risk !=null and params.risk != '' ">
            AND t.risk = #{params.risk}
        </if>
        <if test=" params.dealerIsRead !=null and params.dealerIsRead != '' ">
            AND t.dealer_is_read = #{params.dealerIsRead}
        </if>
        <if test=" params.managerIsRead !=null and params.managerIsRead != '' ">
            AND t.manager_is_read = #{params.managerIsRead}
        </if>
        <if test=" params.ccmIsRead !=null and params.ccmIsRead != '' ">
            AND t.ccm_is_read = #{params.ccmIsRead}
        </if>
        <if test=" params.dataSources !=null and params.dataSources != '' ">
            AND t.data_sources = #{params.dataSources}
        </if>
        <if test=" params.isDeleted !=null and params.isDeleted != '' ">
            AND t.is_deleted = #{params.isDeleted}
        </if>
        <if test=" params.isCloseCase !=null and params.isCloseCase != '' ">
            AND t.is_close_case = #{params.isCloseCase}
        </if>
        <if test=" params.workOrderNature !=null and params.workOrderNature != '' ">
            AND t.work_order_nature = #{params.workOrderNature}
        </if>


        <if test=" params.isReport !=null ">
            AND t.is_report = #{params.isReport}
        </if>
        <if test=" params.isValid !=null and params.isValid != '' ">
            AND t.is_valid = #{params.isValid}
        </if>
        <if test=" params.createdAt !=null and params.createdAt != '' ">
            AND t.created_at = #{params.createdAt}
        </if>
        <if test=" params.updatedAt !=null and params.updatedAt != '' ">
            AND t.updated_at = #{params.updatedAt}
        </if>

        <if test=" params.sql1 !=null and params.sql1 != '' ">
            and ${params.sql1}
        </if>
        and t.is_deleted=0
        <if test=" params.sql !=null and params.sql != '' ">
            ${params.sql}
        </if>
    </select>
    <select id="selectSaleCusDetailById"
            resultType="com.yonyou.dmscus.customer.dto.ComplaintInfMoreVo">
		SELECT
        distinct t.id, t.app_id, t.owner_code,t.work_order_status,t.is_close_case, t.owner_par_code,
        t.is_opinion ,t.close_case_remark,t.is_agree_region ,t.region_comments, t.region_audit_time,
        t.is_agree_headquarters,t.headquarters_comments,
        (select abs(UNIX_TIMESTAMP(t3.plan_follow_time)-UNIX_TIMESTAMP(NOW())) from
        tt_sale_complaint_follow t3 WHERE t.id=t3.complaint_info_id and t3.plan_follow_time is not null ORDER BY
        t3.follow_time desc LIMIT 1
        ) as nextPlanFollowTime,
        t.is_satisfied,
        t.restart_reply_time,
        t.work_order_nature,t.work_order_classification,t.reply_name,t.service_commitment,
        t.org_id,t.is_report, t.complaint_id, t.type, t.classification, t.source, t.call_time,
        t.owner_address, t.fisrt_restart_time, t.newest_restart_time, t.call_name,sex, t.call_tel,
        t.name, t.license_plate_num, t.vin, t.buy_time, t.model, t.model_year, t.buy_dealer_name,
        t.dealer_code, t.mileage, t.reply_tel, t.reply_tel2, t.subject, t.problem, t.illustrate,
        t.category1, t.category2, t.category3, t.part,t.subdivision_part,t.cc_part, t.cc_subdivision_part,
        t.cc_problem, t.cc_requirement, t.department, t.receptionist, t.importance_level, t.dealer_fisrt_reply_time,
        t.fisrt_restart_dealer_fisrt_reply_time, t.is_revisit, t.revisit_time, t.revisit_result, t.revisit_content,
        t.apply_time as applyTime, t.is_agree, t.submit_time, t.close_case_time, t.restart_close_case_time, t.close_case_status,
        t.follow_status, t.basic_reason, t.is_repaired, t.tech_maintain_plan, t.rapport_plan, t.risk, t.dealer_is_read,
        t.manager_is_read, t.ccm_is_read, t.data_sources, t.is_deleted, t.created_at, t.updated_at,
        t.work_order_nature as workOrderNature ,t.work_order_classification as workOrderClassification,
        t2.region AS region,t2.region_manager AS region_manager,t2.bloc AS bloc,t2.dealer_name AS dealer_name,
        t.region_satisfied_case as regionSatisfiedCase,t.HQ_satisfied_case as HQSatisfiedCase,
        t3.follow_content AS follow_content,
        t3.ccm_not_publish AS ccm_not_publish,
        t3.ccm_subject AS ccm_subject,
        t3.status AS status,
        t3.advise AS advise,
        t3.plan_follow_time AS plan_follow_time,

        (SELECT t2.ccm_man from tt_complaint_dealer_ccm_ref t2 where t.dealer_code=t2.dealer_code LIMIT 1) AS ccm_man,
        t.buy_dealer_code AS  buyDealerCode,
        t.buy_dealer_name AS buyDealName,
        t.buy_bloc AS buyBloc,
        t.buy_region_manager AS buyRegionManager,
        t.dealer_name AS handleDealName,
        t.dealer_code AS handleDealCode,
        t.bloc AS handleBloc,
        t.region as handleRegion,
        t.region_manager AS handleRegionManager,
        tsce.case_create_time as caseCreateTime,
        tsce.risk_type as riskType,
        (SELECT group_concat(t5.class_name separator ',') from tt_sale_complaint_not_close_case_reason t5 where
        t5.complaint_info_id=t.id ) AS smallClassNameOther,
		tsce.plant_description AS plantDescription
        FROM tt_sale_complaint_info t
        LEFT JOIN tt_complaint_dealer_ccm_ref t2 on t.dealer_code=t2.dealer_code
        LEFT join (select complaint_info_id,ccm_subject,follow_content,ccm_not_publish,status,advise,plan_follow_time,is_ccm from tt_sale_complaint_follow where is_ccm=1 and complaint_info_id = #{params.id} ORDER BY follow_time desc LIMIT 1) t3 on t.id=t3.complaint_info_id
        LEFT join tt_sale_complaint_not_close_case_reason t4 on t.id=t4.complaint_info_id
        LEFT JOIN tt_sale_complaint_ext tsce on t.id = tsce.sale_complaint_id
        WHERE 1=1
        AND t.id = #{params.id}
    </select>
    <select id="select51dkCusByDeal"
            resultType="com.yonyou.dmscus.customer.entity.vo.V51dkComplaintInfMoreVO">
        SELECT
        distinct
        t.id,
        t.complaint_id,
        t.work_order_status,
        t.dealer_name,
        t.dealer_code,
        t.call_time,
        t1.cate_code AS category1,
        t2.cate_code AS category2,
        t3.cate_code AS category3,
        t.subject
        FROM tt_complaint_info t
        LEFT JOIN tt_complaint_classification t1 ON t.category1 = t1.id
        LEFT JOIN tt_complaint_classification t2 ON t.category2 = t2.id
        LEFT JOIN tt_complaint_classification t3 ON t.category3 = t3.id
        WHERE 1=1
        <if test=" params.vin !=null and params.vin != '' ">
            AND t.vin = #{params.vin}
        </if>
        <if test=" params.ownerCode !=null and params.ownerCode != '' ">
            AND t.dealer_code = #{params.ownerCode}
        </if>
        and t.is_deleted = 0
    </select>

</mapper>
