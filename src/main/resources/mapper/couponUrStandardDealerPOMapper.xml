<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.goodwill.CouponUrStandardDealerMapper">

                <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.goodwill.CouponUrStandardDealerPO">
                                                                                                                                                        <id column="id" property="id"/>
                                                                                                                                                                                                                                                                                                                                                                                                                    <result column="app_id" property="appId"/>
                                                        <result column="owner_code" property="ownerCode"/>
                                                        <result column="owner_par_code" property="ownerParCode"/>
                                                        <result column="org_id" property="orgId"/>
                                                                                    <result column="coupon_id" property="couponId"/>
                                                        <result column="dealer_code" property="dealerCode"/>
                                                        <result column="dealer_name" property="dealerName"/>
                                                        <result column="big_area_id" property="bigAreaId"/>
                                                        <result column="small_area_id" property="smallAreaId"/>
                                                        <result column="big_area" property="bigArea"/>
                                                        <result column="small_area" property="smallArea"/>
                                                        <result column="dealer_address" property="dealerAddress"/>
                                                        <result column="data_sources" property="dataSources"/>
                                                        <result column="is_deleted" property="isDeleted"/>
                                                        <result column="created_at" property="createdAt"/>
                                                        <result column="updated_at" property="updatedAt"/>
                                                    <result column="created_by" property="createdBy"/>
                        <result column="updated_by" property="updatedBy"/>
                        <result column="record_version" property="recordVersion"/>
                    </resultMap>

                <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
           app_id, owner_code, owner_par_code, org_id, id, coupon_id, dealer_code, dealer_name, big_area_id, small_area_id, big_area, small_area, dealer_address, data_sources, is_deleted, created_at, updated_at
        </sql>

    
    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectPageBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.CouponUrStandardDealerPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_coupon_ur_standard_dealer t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.couponId !=null and params.couponId != '' ">
                AND t.coupon_id = #{params.couponId}
            </if>
                    <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code = #{params.dealerCode}
            </if>
                    <if test=" params.dealerName !=null and params.dealerName != '' ">
                AND t.dealer_name = #{params.dealerName}
            </if>
                    <if test=" params.bigAreaId !=null and params.bigAreaId != '' ">
                AND t.big_area_id = #{params.bigAreaId}
            </if>
                    <if test=" params.smallAreaId !=null and params.smallAreaId != '' ">
                AND t.small_area_id = #{params.smallAreaId}
            </if>
                    <if test=" params.bigArea !=null and params.bigArea != '' ">
                AND t.big_area = #{params.bigArea}
            </if>
                    <if test=" params.smallArea !=null and params.smallArea != '' ">
                AND t.small_area = #{params.smallArea}
            </if>
                    <if test=" params.dealerAddress !=null and params.dealerAddress != '' ">
                AND t.dealer_address = #{params.dealerAddress}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

    <!-- 集合查询& 分页查询例子,入参数为Map,出参数也为Map -->
    <!--t.user_id=#{user_id,jdbcType=NUMBERIC} -->
    <select id="selectListBySql" resultMap="BaseResultMap" parameterType="com.yonyou.dmscus.customer.entity.po.goodwill.CouponUrStandardDealerPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tt_coupon_ur_standard_dealer t
        WHERE 1=1
                    <if test=" params.appId !=null and params.appId != '' ">
                AND t.app_id = #{params.appId}
            </if>
                    <if test=" params.ownerCode !=null and params.ownerCode != '' ">
                AND t.owner_code = #{params.ownerCode}
            </if>
                    <if test=" params.ownerParCode !=null and params.ownerParCode != '' ">
                AND t.owner_par_code = #{params.ownerParCode}
            </if>
                    <if test=" params.orgId !=null and params.orgId != '' ">
                AND t.org_id = #{params.orgId}
            </if>
                    <if test=" params.id !=null and params.id != '' ">
                AND t.id = #{params.id}
            </if>
                    <if test=" params.couponId !=null and params.couponId != '' ">
                AND t.coupon_id = #{params.couponId}
            </if>
                    <if test=" params.dealerCode !=null and params.dealerCode != '' ">
                AND t.dealer_code = #{params.dealerCode}
            </if>
                    <if test=" params.dealerName !=null and params.dealerName != '' ">
                AND t.dealer_name = #{params.dealerName}
            </if>
                    <if test=" params.bigAreaId !=null and params.bigAreaId != '' ">
                AND t.big_area_id = #{params.bigAreaId}
            </if>
                    <if test=" params.smallAreaId !=null and params.smallAreaId != '' ">
                AND t.small_area_id = #{params.smallAreaId}
            </if>
                    <if test=" params.bigArea !=null and params.bigArea != '' ">
                AND t.big_area = #{params.bigArea}
            </if>
                    <if test=" params.smallArea !=null and params.smallArea != '' ">
                AND t.small_area = #{params.smallArea}
            </if>
                    <if test=" params.dealerAddress !=null and params.dealerAddress != '' ">
                AND t.dealer_address = #{params.dealerAddress}
            </if>
                    <if test=" params.dataSources !=null and params.dataSources != '' ">
                AND t.data_sources = #{params.dataSources}
            </if>
                    <if test=" params.isDeleted !=null and params.isDeleted != '' ">
                AND t.is_deleted = #{params.isDeleted}
            </if>
                    <if test=" params.createdAt !=null and params.createdAt != '' ">
                AND t.created_at = #{params.createdAt}
            </if>
                    <if test=" params.updatedAt !=null and params.updatedAt != '' ">
                AND t.updated_at = #{params.updatedAt}
            </if>
            </select>

</mapper>
