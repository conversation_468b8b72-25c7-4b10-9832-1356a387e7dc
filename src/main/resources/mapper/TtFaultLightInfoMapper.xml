<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.faultLight.TtFaultLightInfoMapper">

    <resultMap id="BaseResultMap" type="com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightInfoPO" >
        <result column="id" property="id" />
        <result column="source_clue_id" property="sourceClueId" />
        <result column="vehicle_vin" property="vehicleVin" />
        <result column="dealer_code" property="dealerCode" />
        <result column="dealer_name" property="dealerName" />
        <result column="recommend_dealer_flag" property="recommendDealerFlag" />
        <result column="leads_receive_time" property="leadsReceiveTime" />
        <result column="warning_id" property="warningId" />
        <result column="warning_name" property="warningName" />
        <result column="warning_en" property="warningEn" />
        <result column="warning_cn" property="warningCn" />
        <result column="warning_priority" property="warningPriority" />
        <result column="warining_category" property="wariningCategory" />
        <result column="warning_time" property="warningTime" />
        <result column="warning_province_cn" property="warningProvinceCn" />
        <result column="warning_province_id" property="warningProvinceId" />
        <result column="warning_city_cn" property="warningCityCn" />
        <result column="warning_city_id" property="warningCityId" />
        <result column="type_name" property="typeName" />
        <result column="type_code" property="typeCode" />
        <result column="customer_name" property="customerName" />
        <result column="customer_mobile" property="customerMobile" />
        <result column="gender" property="gender" />
        <result column="status" property="status" />
        <result column="first_call_time" property="firstCallTime" />
        <result column="first_seat_name" property="firstSeatName" />
        <result column="first_call_status" property="firstCallStatus" />
        <result column="call_result" property="callResult" />
        <result column="comments" property="comments" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
                source_clue_id,
                vehicle_vin,
                dealer_code,
                dealer_name,
                recommend_dealer_flag,
                leads_receive_time,
                warning_id,
                warning_name,
                warning_en,
                warning_cn,
                warning_priority,
                warining_category,
                warning_time,
                warning_province_cn,
                warning_province_id,
                warning_city_cn,
                warning_city_id,
                type_name,
                type_code,
                customer_name,
                customer_mobile,
                gender,
                status,
                first_call_time,
                first_seat_name,
                first_call_status,
                call_result,
                comments
    </sql>

</mapper>