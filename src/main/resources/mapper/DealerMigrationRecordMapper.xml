<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yonyou.dmscus.customer.dao.dealermigrationrecord.DealerMigrationRecordMapper">

    <delete id="cleanDealerMigrationRecord" >
        delete from tm_dealer_migration_records
    </delete>

    <insert id="insertList">
        insert into tm_dealer_migration_records (original_dealer_code,target_dealer_code,migration_time,operator,migration_type)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.originalDealerCode},
            #{item.targetDealerCode},
            NOW(),
            'system_user',
            #{item.migrationType}
            )
        </foreach>
    </insert>

</mapper>
