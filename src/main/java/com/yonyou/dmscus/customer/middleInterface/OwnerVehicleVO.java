package com.yonyou.dmscus.customer.middleInterface;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel(value = "OwnerVehicleVO", description = "OwnerVehicleVO")
@Data
public class OwnerVehicleVO {

    /*** 车辆信息 */
    @ApiModelProperty(value = "id", required = true)
    private Long    id;
    @ApiModelProperty(value = "客户信息表主键")
    private Long    customerId;
    @ApiModelProperty(value = "公司代码")
    private String  companyCode;
    @ApiModelProperty(value = "所有者的父组织代码（用于二网使用）")
    private String  ownerParCode;
    @ApiModelProperty(value = "经销商代码   原产品OWNER_CODE")
    private String  dealerCode;
    @ApiModelProperty(value = "VIN")
    private String  vin;
    @ApiModelProperty(value = "发动机号")
    private String  engineNo;
    @ApiModelProperty(value = "制造日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date    productDate;
    @ApiModelProperty(value = "出厂日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date    factoryDate;
    @ApiModelProperty(value = "品牌id")
    private Long    brandId;
    @ApiModelProperty(value = "车系id")
    private Long    seriesId;
    @ApiModelProperty(value = "车型id")
    private Long    modelId;
    @ApiModelProperty(value = "配置id")
    private Long    configId;
    @ApiModelProperty(value = "颜色")
    private Long    colorId;
    @ApiModelProperty(value = "内饰ID")
    private Long    trimId;
    private String  productId;
    @ApiModelProperty(value = "里程")
    private Integer mileage;
    @ApiModelProperty(value = "开票时间（保修起始日期）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date    invoiceDate;
    @ApiModelProperty(value = "车牌号")
    private String  plateNumber;
    @ApiModelProperty(value = "钥匙编号")
    private String  keyNo;
    @ApiModelProperty(value = "产地")
    private String  productingArea;
    @ApiModelProperty(value = "国Ⅲ、国Ⅳ、国Ⅲ+OBD、欧Ⅲ、欧Ⅳ、欧Ⅳ+OBD、国Ⅲ欧Ⅳ、国V")
    private Integer dischargeStandard;
    @ApiModelProperty(value = "排气量")
    private String  exhaustQuantity;
    @ApiModelProperty(value = "备注")
    private String  remark;
    @ApiModelProperty(value = "是否有合格证")
    private Integer hasCertificate;
    @ApiModelProperty(value = "合格证号")
    private String  certificateNumber;
    @ApiModelProperty(value = "合格证存放地")
    private String  certificateLocus;
    @ApiModelProperty(value = "是否")
    private Integer oemTag;
    @ApiModelProperty(value = "是，否")
    private Integer isDirect;
    @ApiModelProperty(value = "代收经销商姓名")
    private String  collectingDealer;
    @ApiModelProperty(value = "仓库代码")
    private String  storageId;
    @ApiModelProperty(value = "库位代码")
    private String  storagePositionCode;
    @ApiModelProperty(value = "库存状态(1413)")
    private Integer ownStockStatus;
    @ApiModelProperty(value = "配车状态(1414)")
    private Integer dispatchedStatus;
    @ApiModelProperty(value = "是否锁定")
    private Integer isLock;
    @ApiModelProperty(value = "运损状态")
    private Integer trafficMarStatus;
    @ApiModelProperty(value = "是，否试驾车")
    private Integer isTestDrive;
    @ApiModelProperty(value = "入库类型(1317)")
    private Integer entryType;
    @ApiModelProperty(value = "首次入库日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date    firstStockInDate;
    @ApiModelProperty(value = "最后入库日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date    latestStockInDate;
    @ApiModelProperty(value = "最后入库人")
    private String  lastStockInBy;
    @ApiModelProperty(value = "出库类型(1318)")
    private Integer deliveryType;
    @ApiModelProperty(value = "首次出库日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date    firstStockOutDate;
    @ApiModelProperty(value = "最后出库日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date    latestStockOutDate;
    @ApiModelProperty(value = "最后出库人")
    private String  lastStockOutBy;
    @ApiModelProperty(value = "车辆配置代码")
    private String  vsn;
    @ApiModelProperty(value = "含税采购价")
    private Double  purchasePrice;
    @ApiModelProperty(value = "车厂销售指导价")
    private Double  oemDirectivePrice;
    @ApiModelProperty(value = "销售指导价")
    private Double  directivePrice;
    @ApiModelProperty(value = "批售指导价格")
    private Double  wholesaleDirectivePrice;
    @ApiModelProperty(value = "采购日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date    purchaseDate;
    @ApiModelProperty(value = "是否外借")
    private Integer isBorrowed;
    @ApiModelProperty(value = "保养手册号")
    private String  warrantyManualNo;
    @ApiModelProperty(value = "数据来源的系统代码")
    private String  sourceSystem;
    @ApiModelProperty(value = "APP_ID")
    private String  appId;
    @ApiModelProperty(value = "保险开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date    insuranceStartDate;
    @ApiModelProperty(value = "首选 经销商")
    private String  firstDealer;
    @ApiModelProperty(value = "保险开始日期来源")
    private String  insuranceStartDateSource;
    @ApiModelProperty(value = "保险开始日期页面是否可编辑")
    private Integer insuranceStartDatePageCanBeEdited;
    @ApiModelProperty(value = "车主上报销售日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date    ownerReportedSalesDate;
    @ApiModelProperty(value = "品牌代码")
    private String  brandCode;
    @ApiModelProperty(value = "品牌名称")
    private String  brandName;
    @ApiModelProperty(value = "品牌英文名称")
    private String  brandNameEn;
    @ApiModelProperty(value = "配置代码 车款代码")
    private String  configCode;
    @ApiModelProperty(value = "配置名称 车款名称")
    private String  configName;
    @ApiModelProperty(value = "配置名称英文名 车款英文名")
    private String  configNameEn;
    @ApiModelProperty(value = "配置年份 年款")
    private String  configYear;
    @ApiModelProperty(value = "变速箱")
    private String  transMission;
    @ApiModelProperty(value = "水货车(0:否 1:是)")
    private Integer wwCar;
    @ApiModelProperty(value = "行驶证号")
    private String  licenseNo;
    @ApiModelProperty(value = "上牌日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date    licenseDate;
    @ApiModelProperty(value = "保修结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date    invoiceEndDate;
    @ApiModelProperty(value = "车系名称")
    private String  seriesName;
    @ApiModelProperty(value = "车型名称")
    private String  modelName;
    @ApiModelProperty(value = "经销商名称")
    private String  dealerName;
    /*** 车型信息 */
    @ApiModelProperty(value = "车型代码")
    private String  modelCode;
    @ApiModelProperty(value = "车型英文名称")
    private String  modelNameEn;
    @ApiModelProperty(value = "燃料类型(4106)")
    private Integer fuelType;
    @ApiModelProperty(value = "数据来源")
    private String  dataSources;
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;
    /*** 车主信息 */
    @ApiModelProperty(value = "客户唯一ID")
    private Long    oneId;
    @ApiModelProperty(value = "潜客姓名")
    private String  name;
    @ApiModelProperty(value = "手机")
    private String  mobile;
    @ApiModelProperty(value = "联系人名称")
    private String  contactName;
    @ApiModelProperty(value = "联系方式一")
    private String  contactorMobile;
    @ApiModelProperty(value = "联系方式二 固定电话")
    private String  contactorPhone;
    @ApiModelProperty(value = "QQ")
    private String  qq;
    @ApiModelProperty(value = "所属行业(互联网/IT/电子/通信:70301002.广告/传媒/文化/体育:70301002.金融:70301003.教育培训:70301004.制药/医疗:70301005.交通/物流/贸易/零售:70301006.专业服务:70301007.房地产/建筑:70301008.汽车:70301009.机械/制造:70301010.消费品:70301011.服务业:70301012.能源/化工/环保:70301013.政府/非盈利机构/其他:70301014)")
    private Integer industry;
    @ApiModelProperty(value = "企业性质(机关:70291001,事业:70291002,企业:70291003,运营:70291004,公检司法:70291005,其它:70291006)")
    private Integer enterpriseType;
    @ApiModelProperty(value = "首次成交时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date    bargainDate;
    @ApiModelProperty(value = "客户类型(企业:40451001,私人:40451002,政府:40451003,事业单位:40451004,租赁:40451005,车改:40451006,团购:40451007)")
    private Integer customerType;
    @ApiModelProperty(value = "性别(先生:10021001,女士:10021002,未知:10021003)")
    private Integer gender;
    @ApiModelProperty(value = "学历（小学:70271001,初中:70271002,高中/中专/技校:70271003,大专:70271004,本科:70271005,硕士及以上:70271006）")
    private Integer education;
    @ApiModelProperty(value = "职业(私营公司老板/自由职业者/个体户:15271001,公司高管:15271002,中层管理人员:15271003,销售人员/销售代表:15271004,私营公司职员/主管:15271005,家庭主妇/夫:15271006,退休:15271007,无工作:15271008,学生:15271009,技术工人:15271010,公务员/教师/警察:15271011,行政职员:15271012,专业人士:15271013,其他:15271014)")
    private Integer occupation;
    @ApiModelProperty(value = "生日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date    birthday;
    @ApiModelProperty(value = "证件类型(身份证:15081001,护照:15081002,军官证:15081003,士兵证:15081004,警察证:15081005,其他:15081006,机构代码:15081007)")
    private Integer ctCode;
    @ApiModelProperty(value = "证件号码")
    private String  certificateNo;
    @ApiModelProperty(value = "邮箱")
    private String  eMail;
    @ApiModelProperty(value = "地址")
    private String  address;
    @ApiModelProperty(value = "邮编")
    private String  zipCode;
    @ApiModelProperty(value = "婚姻状况(已婚:10361001,未婚:10361002,离异:10361003)")
    private Integer maritalStatus;
    @ApiModelProperty(value = "省份(1000)")
    private String  province;
    @ApiModelProperty(value = "城市(1000)")
    private String  city;
    @ApiModelProperty(value = "区县(1000)")
    private String  district;
    @ApiModelProperty(value = "建档日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date    createdDate;
    /*** 组织信息 */
    @ApiModelProperty(value = "经销商代码id")
    private Long    companyId;
    @ApiModelProperty(value = "经销商名称中文")
    private String  companyNameCn;
    @ApiModelProperty(value = "经销商简称中文")
    private String  companyShortNameCn;
    @ApiModelProperty(value = "公司类型:1005")
    private Integer companyType;
    @ApiModelProperty(value = "集团")
    private String  groupCompanyId;
    @ApiModelProperty(value = "集团名称")
    private String  groupCompanyName;
    @ApiModelProperty(value = "组织id")
    private Long    orgId;
    @ApiModelProperty(value = "省份")
    private Integer provinceId;
    @ApiModelProperty(value = "城市")
    private Integer cityId;
    @ApiModelProperty(value = "区域")
    private Integer countyId;
    @ApiModelProperty(value = "地址")
    private String  addressZh;
    @ApiModelProperty(value = "经度")
    private double  longitude;
    @ApiModelProperty(value = "纬度")
    private double  latitude;
    @ApiModelProperty(value = "电话")
    private String  phone;
    @ApiModelProperty(value = "传真")
    private String  fax;
    @ApiModelProperty(value = "经销商类型")
    private Integer dealerType;
    @ApiModelProperty(value = "经销商规模")
    private Integer dealerScale;
    @ApiModelProperty(value = "是否批售授权经销商")
    private Integer wholesaleGrant;
    @ApiModelProperty(value = "状态")
    private Integer status;
    @ApiModelProperty(value = "销售热线")
    private String  salesLine;
    @ApiModelProperty(value = "销售ParmaCode")
    private String  salesParmaCode;
    @ApiModelProperty(value = "客户评分销售")
    private String  gradeSales;
    @ApiModelProperty(value = "开户银行销售")
    private String  bankSales;
    @ApiModelProperty(value = "开户账号销售")
    private String  bankAccountSales;
    @ApiModelProperty(value = "税号销售")
    private String  taxSales;
    @ApiModelProperty(value = "售后热线")
    private String  afterLine;
    @ApiModelProperty(value = "售后ParmaCode")
    private String  afterParmaCode;
    @ApiModelProperty(value = "客户评分售后")
    private String  gradeAfter;
    @ApiModelProperty(value = "开户银行售后")
    private String  bankAfter;
    @ApiModelProperty(value = "开户账号售后")
    private String  bankAccountAfter;
    @ApiModelProperty(value = "税号售后")
    private String  taxAfter;
    @ApiModelProperty(value = "FACILITY")
    private String  facility;
    @ApiModelProperty(value = "DEALERSHIP_OUTLET")
    private String  dealershipOutlet;
    @ApiModelProperty(value = "OPERATION_DATE_DN")
    private String  operationDateDn;
    @ApiModelProperty(value = "OPERATION_DATE_RD")
    private String  operationDateRd;
    @ApiModelProperty(value = "OPERATION_DATE_INTERNET")
    private String  operationDateInterent;
    @ApiModelProperty(value = "RELOCATION_OR_UPGRADE")
    private String  relocationOrUpgrade;
    @ApiModelProperty(value = "SHORT_DATE")
    private String  shortDate;
    @ApiModelProperty(value = "VIPS_CODE")
    private String  vipsCode;
    @ApiModelProperty(value = "VR_OOED")
    private String  vrCode;
    @ApiModelProperty(value = "VMI_LDC")
    private Integer vmiLdc;
    @ApiModelProperty(value = "零配件仓库")
    private String  partWarehouse;
    @ApiModelProperty(value = "仓库发货地址")
    private String  warehouseAddress;
    @ApiModelProperty(value = "开店时间")
    private String  openTime;
    @ApiModelProperty(value = "关店时间")
    private String  closeTime;
    @ApiModelProperty(value = "经销商门头照片url")
    private String  storefrontPhotoUrl;
    @ApiModelProperty(value = "售后大区ID")
    private Long    afterBigAreaId;
    @ApiModelProperty(value = "售后大区名称")
    private String  afterBigAreaName;
    @ApiModelProperty(value = "售后小区ID")
    private Long    afterSmallAreaId;
    @ApiModelProperty(value = "售后小区名称")
    private String  afterSmallAreaName;
    @ApiModelProperty(value = "销售大区ID")
    private Long    saleBigAreaId;
    @ApiModelProperty(value = "销售大区名称")
    private String  saleBigAreaName;
    @ApiModelProperty(value = "销售小区ID")
    private Long    saleSmallAreaId;
    @ApiModelProperty(value = "销售小区名称")
    private String  saleSmallAreaName;
    @ApiModelProperty(value = "微信号")
    private String  weChat;
    @ApiModelProperty(value = "是否延保")
    private Integer isExtendWarranty;
    @ApiModelProperty(value = "延保名称")
    private String  extendWarrantyName;
    @ApiModelProperty(value = "延保开始日期")
    private String  extendWarrantyStartDate;
    @ApiModelProperty(value = "延保结束日期")
    private String  extendWarrantyEndDate;
}
