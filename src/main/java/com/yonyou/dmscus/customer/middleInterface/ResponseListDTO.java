package com.yonyou.dmscus.customer.middleInterface;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 中台数据返回
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */

public class ResponseListDTO<T>  implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 返回代码，0表示成功，其他表示失败
     */
    private String returnCode;

    /**
     * 返回描述
     */
    private String returnMessage;

    /**
     * 返回数据
     */
    private List<T> data;


    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMessage() {
        return returnMessage;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage = returnMessage;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }
}
