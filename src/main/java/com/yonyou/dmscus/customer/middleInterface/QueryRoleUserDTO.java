package com.yonyou.dmscus.customer.middleInterface;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
@ApiModel(value = "查询角色对应用户", description = "查询角色对应用户")
public class QueryRoleUserDTO {
	@ApiModelProperty(value = "组织类型Id")
	private Integer orgId;

	@ApiModelProperty(value = "组织类型")
	private Integer orgType;

	@ApiModelProperty(value = "公司Id")
	private Integer companyId;

	@ApiModelProperty(value = "角色代码")
	private List<String> roleCode;

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Integer getOrgType() {
		return orgType;
	}

	public void setOrgType(Integer orgType) {
		this.orgType = orgType;
	}

	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

	public List<String> getRoleCode() {
		return roleCode;
	}

	public void setRoleCode(List<String> roleCode) {
		this.roleCode = roleCode;
	}
}
