package com.yonyou.dmscus.customer.middleInterface;

import java.util.List;

public class IsExistByCodeVO {
	
	private List<String> notExistCompanyCodeList;
	
	private boolean isAllExist;

	public List<String> getNotExistCompanyCodeList() {
		return notExistCompanyCodeList;
	}

	public void setNotExistCompanyCodeList(List<String> notExistCompanyCodeList) {
		this.notExistCompanyCodeList = notExistCompanyCodeList;
	}

	public boolean getIsAllExist() {
		return isAllExist;
	}

	public void setISAllExist(boolean isAllExist) {
		this.isAllExist = isAllExist;
	}
	
}
