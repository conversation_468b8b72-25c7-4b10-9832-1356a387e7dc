package com.yonyou.dmscus.customer.middleInterface;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(description = "请求")
public class RequestDTO<T> implements Serializable {
	private static final long serialVersionUID = Constant.SERIAL_VERSION_UID;

	@ApiModelProperty(value = "请求数据")
	private T data;

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}

	@Override
	public String toString() {
		return "RequestDTO{" + "data=" + data + '}';
	}
}
