package com.yonyou.dmscus.customer.middleInterface;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "UpdateCouponDetailDTO ", description = "卡券明细表 卡券领取模版")
public class SaveCouponDetailDTO {

	@ApiModelProperty(value = "tt_coupon_info表ID")
	private Long couponId;

	@ApiModelProperty(value = "卡券状态(已领取:31061001 已锁定:31061002 已使用:31061003 已过期 已作废:31061004)")
	private Integer ticketState;
	@ApiModelProperty(value = "卡券来源(83241001 VCDC发券 83241002 沃世界领券 83241003 商城购买)")
	private Integer couponSource;
	@ApiModelProperty(value = "领取日期")
	private String getDate;
	@ApiModelProperty(value = "one_id")
	private Integer oneId;
	@ApiModelProperty(value = "vin")
	private String vin;
	@ApiModelProperty(value = "余额")
	private Integer leftValue;
	@ApiModelProperty(value = "memberId")
	private Long memberId;

	public Long getMemberId() {
		return memberId;
	}

	public void setMemberId(Long memberId) {
		this.memberId = memberId;
	}

	public Integer getLeftValue() {
		return leftValue;
	}

	public void setLeftValue(Integer leftValue) {
		this.leftValue = leftValue;
	}

	public Long getCouponId() {
		return couponId;
	}

	public void setCouponId(Long couponId) {
		this.couponId = couponId;
	}

	public Integer getTicketState() {
		return ticketState;
	}

	public void setTicketState(Integer ticketState) {
		this.ticketState = ticketState;
	}

	public Integer getCouponSource() {
		return couponSource;
	}

	public void setCouponSource(Integer couponSource) {
		this.couponSource = couponSource;
	}

	public String getGetDate() {
		return getDate;
	}

	public void setGetDate(String getDate) {
		this.getDate = getDate;
	}

	public Integer getOneId() {
		return oneId;
	}

	public void setOneId(Integer oneId) {
		this.oneId = oneId;
	}

	public String getVin() {
		return vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

}
