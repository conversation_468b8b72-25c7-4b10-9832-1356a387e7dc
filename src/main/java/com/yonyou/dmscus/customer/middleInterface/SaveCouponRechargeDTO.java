package com.yonyou.dmscus.customer.middleInterface;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "RechargeCouponDetailDTO ", description = "卡券明细表 卡券充值模版")
public class SaveCouponRechargeDTO {

	@ApiModelProperty(value = "tt_coupon_info表ID")
	private Long couponId;
	@ApiModelProperty(value = "卡券领用tt_coupon_detail表id")
	private Long couponDetailId;
	@ApiModelProperty(value = "用户tt_customer_info表id")
	private Long customerId;
	@ApiModelProperty(value = "充值后金额")
	private BigDecimal beforeRechargeAmount;
	@ApiModelProperty(value = "充值前金额")
	private BigDecimal afterRechargeAmount;
	@ApiModelProperty(value = "充值金额")
	private BigDecimal rechargeAmount;
	@ApiModelProperty(value = "充值时间")
	private String rechargeTime;

	public Long getCouponId() {
		return couponId;
	}

	public void setCouponId(Long couponId) {
		this.couponId = couponId;
	}

	public Long getCouponDetailId() {
		return couponDetailId;
	}

	public void setCouponDetailId(Long couponDetailId) {
		this.couponDetailId = couponDetailId;
	}

	public Long getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Long customerId) {
		this.customerId = customerId;
	}

	public BigDecimal getBeforeRechargeAmount() {
		return beforeRechargeAmount;
	}

	public void setBeforeRechargeAmount(BigDecimal beforeRechargeAmount) {
		this.beforeRechargeAmount = beforeRechargeAmount;
	}

	public BigDecimal getAfterRechargeAmount() {
		return afterRechargeAmount;
	}

	public void setAfterRechargeAmount(BigDecimal afterRechargeAmount) {
		this.afterRechargeAmount = afterRechargeAmount;
	}

	public BigDecimal getRechargeAmount() {
		return rechargeAmount;
	}

	public void setRechargeAmount(BigDecimal rechargeAmount) {
		this.rechargeAmount = rechargeAmount;
	}

	public String getRechargeTime() {
		return rechargeTime;
	}

	public void setRechargeTime(String rechargeTime) {
		this.rechargeTime = rechargeTime;
	}

}
