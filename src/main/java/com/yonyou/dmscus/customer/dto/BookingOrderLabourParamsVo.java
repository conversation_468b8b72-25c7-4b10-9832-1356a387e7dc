package com.yonyou.dmscus.customer.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BookingOrderLabourParamsVo {



    /**
     * 项目编号ID(工时)
     */
    @ApiModelProperty(value = "交修项目编号ID工时",name = "itemIdHandProject")
    private Long itemIdHandProject;

    /**
     * 维修项目代码
     */
    @ApiModelProperty(value = "维修项目代码",name = "labourCode")
    private String labourCode;

    /**
     * 维修项目名称
     */
    @ApiModelProperty(value = "维修项目名称",name = "labourName")
    private String labourName;

    /**
     * 标准工时
     */
    @ApiModelProperty(value = "标准工时",name = "stdLabourHour")
    private double stdLabourHour;

    /**
     * 套餐代码
     */
    @ApiModelProperty(value = "套餐代码",name = "setCode")
    private String setCode;

    /**
     * 适用维修类型
     */
    @ApiModelProperty(name = "适用维修类型",value = "jobType" )
    private String jobType;

    /**
     * 适用账类
     */
    @ApiModelProperty(name = "适用账类",value = "accountGroup")
    private String accountGroup;

    /**
     * 折扣率
     */
    @ApiModelProperty(name = "折扣率",value = "discount")
    private Double discount;

}
