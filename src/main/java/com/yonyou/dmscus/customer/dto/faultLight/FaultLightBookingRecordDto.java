package com.yonyou.dmscus.customer.dto.faultLight;

import java.io.Serializable;
import java.util.Date;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 故障灯转预约单记录表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FaultLightBookingRecordDto implements Serializable {
    /**
    * 主键ID
    */
    private Long id;

    /**
    * 预约进店时间
    */
    private Date forecastTime;

    /**
    * 预约单号
    */
    private String bookingOrderNo;

    /**
    * 经销商代码
    */
    private String dealerCode;

    /**
    * icm线索ID
    */
    private Long icmId;

    /**
    * 是否删除:1,删除；0,未删除
    */
    private Byte isDeleted;

    /**
    * 数据创建时间
    */
    private Date createdAt;

    /**
    * 数据创建人
    */
    private String createdBy;

    /**
    * 数据修改时间
    */
    private Date updatedAt;

    /**
    * 数据修改人
    */
    private String updatedBy;

    /**
    * 创建sql人
    */
    private String createSqlby;

    /**
    * 更新人sql人
    */
    private String updateSqlby;

    /**
    * 资料来源 
    */
    private Integer bookingSource;
}