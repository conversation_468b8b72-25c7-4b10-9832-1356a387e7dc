package com.yonyou.dmscus.customer.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class HandRepairProjectParamsVo {

    /**
     * 交修项目代码
     */
    @ApiModelProperty(value = "交修项目代码",name = "handRepairProjectCode")
    private String handRepairProjectCode;

    /**
     * 交修项目名称
     */
    @ApiModelProperty(value = "交修项目名称",name = "handRepairProjectName")
    private String handRepairProjectName;

    /**
     * JOB_NO
     */
    @ApiModelProperty(value = "jobNo",name = "jobNo")
    private String jobNo;

    /**
     * 套餐代码
     */
    @ApiModelProperty(value = "套餐代码",name = "setCode")
    private String setCode;

    /**
     * 适用维修类型
     */
    @ApiModelProperty(name = "适用维修类型",value = "jobType" )
    private String jobType;

    /**
     * 适用账类
     */
    @ApiModelProperty(name = "适用账类",value = "accountGroup")
    private String accountGroup;

    /**
     * 折扣率
     */
    @ApiModelProperty(name = "折扣率",value = "discount")
    private Double discount;
}
