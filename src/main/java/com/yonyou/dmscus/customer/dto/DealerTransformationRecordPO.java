package com.yonyou.dmscus.customer.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * SA呼叫登记
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Data
@TableName("tt_dealer_transformation_record")
public class DealerTransformationRecordPO {

    private static final long serialVersionUID = 1L;


    /**
     * 所有者代码
     */
    @TableField("new_dealer_code")
    private String newDealerCode;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;



    /**
     * 经销商代码
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 经销商代码
     */
    @TableField("record_version")
    private Integer recordVersion;


    @TableField("created_at")
    private Date createdAt;

    @TableField("created_by")
    private String createdBy;

    @TableField("updated_at")
    private Date updatedAt;

    @TableField("updated_by")
    private String updatedBy;

}
