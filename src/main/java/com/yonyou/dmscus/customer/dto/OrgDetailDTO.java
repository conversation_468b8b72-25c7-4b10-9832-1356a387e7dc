package com.yonyou.dmscus.customer.dto;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value = "OrgDetailDTO", description="组织架构查询信息")
public class OrgDetailDTO extends BaseDTO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "组织ID")
    private Long    id;

    @ApiModelProperty(value = "本身所在company_id")
    private Long  	companyId;

    @ApiModelProperty(value = "所属公司code")
    private String  companyCode;

    @ApiModelProperty(value = "所属公司名称")
    private String  companyNameCn;


    @ApiModelProperty(value = "组织代码")
    private String  orgCode;

    @ApiModelProperty(value = "组织名")
    private String  orgName;

    @ApiModelProperty(value = "组织名称缩写")
    private String  orgShortName;

    @ApiModelProperty(value = "组织描述")
    private String  orgDesc;

    @ApiModelProperty(value = "组织类型ID")
    private Integer orgType;

    @ApiModelProperty(value = "上级组织ID")
    private String    parentOrgId;

    @ApiModelProperty(value = "上级组织名称")
    private String    parentOrgName;

    @ApiModelProperty(value = "数据类型")
    private Integer dataType;

    @ApiModelProperty(value = "数据来源")
    private Integer dataSource;

    @ApiModelProperty(value = "有效，无效")
    private Integer isValid;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "子集")
    private List<OrgDetailDTO> children;


    public List<OrgDetailDTO> getChildren() {
        return children;
    }

    public void setChildren(List<OrgDetailDTO> children) {
        this.children = children;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyNameCn() {
        return companyNameCn;
    }

    public void setCompanyNameCn(String companyNameCn) {
        this.companyNameCn = companyNameCn;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgShortName() {
        return orgShortName;
    }

    public void setOrgShortName(String orgShortName) {
        this.orgShortName = orgShortName;
    }

    public String getOrgDesc() {
        return orgDesc;
    }

    public void setOrgDesc(String orgDesc) {
        this.orgDesc = orgDesc;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public String getParentOrgId() {
        return parentOrgId;
    }

    public void setParentOrgId(String parentOrgId) {
        this.parentOrgId = parentOrgId;
    }

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public Integer getDataSource() {
        return dataSource;
    }

    public void setDataSource(Integer dataSource) {
        this.dataSource = dataSource;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getParentOrgName() {
        return parentOrgName;
    }

    public void setParentOrgName(String parentOrgName) {
        this.parentOrgName = parentOrgName;
    }


    @Override
    public String toString() {
        return "OrgDetailDTO{" +
                "id=" + id +
                ", companyId=" + companyId +
                ", companyCode='" + companyCode + '\'' +
                ", companyNameCn='" + companyNameCn + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", orgName='" + orgName + '\'' +
                ", orgShortName='" + orgShortName + '\'' +
                ", orgDesc='" + orgDesc + '\'' +
                ", orgType=" + orgType +
                ", parentOrgId='" + parentOrgId + '\'' +
                ", parentOrgName='" + parentOrgName + '\'' +
                ", dataType=" + dataType +
                ", dataSource=" + dataSource +
                ", isValid=" + isValid +
                ", isDeleted=" + isDeleted +
                ", children=" + children +
                '}';
    }
}