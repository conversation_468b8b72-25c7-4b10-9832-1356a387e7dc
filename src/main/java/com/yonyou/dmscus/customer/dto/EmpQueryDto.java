package com.yonyou.dmscus.customer.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class EmpQueryDto {


    private SimpleOrgInfo data;

    private Integer page=1;

    private Integer pageSize=999;

    public EmpQueryDto() {

    }

    public EmpQueryDto(Long orgId) {
        EmpQueryDto.SimpleOrgInfo simpleOrgInfo = new EmpQueryDto.SimpleOrgInfo();
        simpleOrgInfo.setOrgId(orgId);
        this.data=simpleOrgInfo;
    }

    public EmpQueryDto(List<Integer> userIds) {
        EmpQueryDto.SimpleOrgInfo simpleOrgInfo = new EmpQueryDto.SimpleOrgInfo();
        simpleOrgInfo.setUserIds(userIds);
        this.data=simpleOrgInfo;
    }
    public EmpQueryDto(String ownerCode) {
        EmpQueryDto.SimpleOrgInfo simpleOrgInfo = new EmpQueryDto.SimpleOrgInfo();
        simpleOrgInfo.setCompanyCode(ownerCode);
        this.data=simpleOrgInfo;
    }


@Data
@Accessors(chain = true)
public static class SimpleOrgInfo{

    private Long orgId;

    private List<Integer> userIds;

    private String companyCode;

}

}
