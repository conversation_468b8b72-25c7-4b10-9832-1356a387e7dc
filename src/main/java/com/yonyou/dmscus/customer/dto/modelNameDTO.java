package com.yonyou.dmscus.customer.dto;

import lombok.Data;

/**
 * 查询车型名称
 */
@Data
public class modelNameDTO {
    /**
     * 公司代码
     */
    private String companyCode;

    /**
     * 数据来源
     */
    private String dataSources;

    /**
     * 燃料类型(4106)
     */
    private Integer fuelType;

    /**
     *车型ID
     */
    private  Integer id;

    /**
     * 是否直售车型:10041001:是；10041002:否
     */

    private  Integer   isDirectModel;

    /**
     * 是否有效
     */

    private  Integer isValid;

    /**
     * 车型代码/沃世界车型名称
     */

    private String keyWord;

    /**
     * 车型代码
     */
    private  String modelCode;

    /**
     *车型名称
     */

    private  String modelName;

    /**
     * 车型名称(沃世界)
     */
    private  String modelNameC;

    /**
     * 车型英文名称
     */
    private  String modelNameEn;

    /**
     * 年款
     */
    private  String modelYear;

    /**
     * 是否在售
     */
    private  Integer onSale;

    /**
     * 车系ID
     */

    private  Integer seriesId;


}
