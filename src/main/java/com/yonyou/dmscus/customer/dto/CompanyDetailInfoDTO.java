package com.yonyou.dmscus.customer.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("经销商详细信息")
@Getter
@Setter
public class CompanyDetailInfoDTO {

    /**
     * 经销商代码
     */
    @ApiModelProperty(value = "经销商代码")
    private String companyCode;

    /**
     * 经销商名称中文
     */
    @ApiModelProperty(value = "经销商名称中文")
    private String companyNameCn;

    /**
     * 经销商简称中文
     */
    @ApiModelProperty(value = "经销商简称中文")
    private String companyShortNameCn;

    /**
     * 经销商经营状态，字典1603
     */
    @ApiModelProperty(value = "经销商经营状态，字典1603")
    private Integer status;

    /**
     * 所属经销商code
     */
    @ApiModelProperty(value = "所属经销商code")
    private String ownedCompanyName;

    /**
     * 所属经销商名称中文
     */
    @ApiModelProperty(value = "所属经销商名称中文")
    private String ownedCompanyNameCn;

    /**
     * 所属经销商简称中文
     */
    @ApiModelProperty(value = "所属经销商简称中文")
    private String ownedCompanyShortNameCn;

    /**
     * 所属经销商经营状态，字典1603
     */
    @ApiModelProperty(value = "所属经销商经营状态，字典1603")
    private Integer ownedCompanyStatus;

    /**
     * 售后大区ID
     */
    @ApiModelProperty(value = "售后大区ID")
    private Long afterBigAreaId;

    /**
     * 售后大区名称
     */
    @ApiModelProperty(value = "售后大区名称")
    private String afterBigAreaName;

    /**
     * 售后区域经理
     */
    @ApiModelProperty(value = "售后区域经理")
    private Long afterSmallAreaId;

    /**
     * 售后小区名称
     */
    @ApiModelProperty(value = "售后小区名称")
    private String afterSmallAreaName;

    /**
     * 销售大区ID
     */
    @ApiModelProperty(value = "销售大区ID")
    private Long saleBigAreaId;

    /**
     * 销售大区名称
     */
    @ApiModelProperty(value = "销售大区名称")
    private String saleBigAreaName;

    /**
     * 销售小区ID
     */
    @ApiModelProperty(value = "销售小区ID")
    private Long saleSmallAreaId;

    /**
     * 销售小区名称
     */
    @ApiModelProperty(value = "销售小区名称")
    private String saleSmallAreaName;

    /**
     * 区域经理
     */
    @ApiModelProperty(value = "区域经理")
    private String regionManager;

    /**
     * 区域经理
     */
    @ApiModelProperty(value = "销售区域经理")
    private String saleRegionManager;

}
