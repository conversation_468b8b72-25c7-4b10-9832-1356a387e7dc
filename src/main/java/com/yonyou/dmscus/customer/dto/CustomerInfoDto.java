package com.yonyou.dmscus.customer.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("会员信息dto")
public class CustomerInfoDto {
    //会员id
    private String memberId;
    //手机号
    private String mobile;
    //客户姓名
    private String customerName;
    //oneid
    private String oneId;
    //头像url
    private String memberUrl;
    //车架号
    private String vin;
    //车牌号
    private String license;
    //（CDP 车主/中台车主 / 自店车主 / 送修人 / 故障灯客户，枚举参见：3530）
    private Integer customerType;





}
