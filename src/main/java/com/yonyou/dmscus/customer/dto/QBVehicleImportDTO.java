package com.yonyou.dmscus.customer.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.service.excel.ExcelColumnDefine;
import com.yonyou.dmscloud.function.domains.dto.DataImportDto;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/8/3 0003
 */
@ApiModel(value = "QB线索Excel导入实体")
@Data
public class QBVehicleImportDTO extends DataImportDto {

    /**
     * 责任经销商
     */
    @ExcelColumnDefine(value = 1)
    private String dealerCode;

    /**
     * 车架号
     */
    @ExcelColumnDefine(value = 2)
    private String vin;


    /**
     * QB号
     */
    @ExcelColumnDefine(value = 3)
    private String qbNumber;


}
