package com.yonyou.dmscus.customer.dto;

import com.yonyou.dmscus.customer.entity.dto.clue.LiteCrmClueResultDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel("业务指标监控用户接入")
@Data
public class BizLogDto {
    //服务
    private String appServiceName;
    //B端售后线索业务类型
    private String bizCode;
    //线索类型 100:故障灯类型, 101:保养等类型, 102:零附件类型
    private String leadsType;
    //邀约类型 82381001:首保, 82381002:定保,  82381006:流失客户,
    private String inviteType;
    //状态 0-失败  1-成功（必填）
    private String status;
    //错误提示信息（可为空）
    private String errorMsg;
    //data
    private LiteCrmClueResultDTO data;

}
