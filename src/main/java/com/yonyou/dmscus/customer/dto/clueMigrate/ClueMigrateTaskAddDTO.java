package com.yonyou.dmscus.customer.dto.clueMigrate;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel
@Data
public class ClueMigrateTaskAddDTO implements java.io.Serializable {

    private Integer id;

    /**
     * 同步类型 1 店店迁移 2 车店迁移
     */
    @ApiModelProperty( hidden = true )
    private Integer syncType;

    /**
     * 原经销商代码
     */
    @ApiModelProperty( "原经销商代码" )
    private String sourceOwnerCode;

    /**
     * 经销商代码
     */
    @ApiModelProperty( "经销商代码" )
    private String ownerCode;

    @ApiModelProperty( "vin" )
    private String vin;

    /**
     * 同步状态 0 初始等待 1 完成 2 失败
     */
    @ApiModelProperty( hidden = true )
    private Integer syncStatus;

    /**
     * 完成时间
     */
    @ApiModelProperty( hidden = true )
    private LocalDateTime finishTime;

    @ApiModelProperty( "备注" )
    private String remark;

    private String ownerParCode;

    @ApiModelProperty("是否迁移CDP(0: 不迁移, 1: 迁移)")
    private Integer migratedCdp;
}
