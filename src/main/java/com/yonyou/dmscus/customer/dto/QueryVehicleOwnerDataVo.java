package com.yonyou.dmscus.customer.dto;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleOwnerInfoDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 根据vin集合 查询车主车辆信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-22
 */
@Data
public class QueryVehicleOwnerDataVo implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<InviteInsuranceVehicleOwnerInfoDTO> vehicleOwnerInfoList;

}
