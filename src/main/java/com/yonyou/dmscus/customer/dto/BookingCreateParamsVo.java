package com.yonyou.dmscus.customer.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 *
 * TODO description
 * <AUTHOR> @date 2020年6月23日
 */
@ApiModel("预约创建确认接口入参Vo")
@Data
public class BookingCreateParamsVo {

    @ApiModelProperty(value = "预约单号",name = "bookingOrderNo")
    private String bookingOrderNo;

    @ApiModelProperty(value = "经销商代码",name = "ownerCode")
    private String ownerCode;

    @ApiModelProperty(value = "预约进厂日期",name = "bookingComeTime")    //invitationsDate
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bookingComeTime;

    @ApiModelProperty(value = "预约人OneID",name = "oneId")
    private Integer oneId;

    @ApiModelProperty(value = "预约人姓名",name = "contactorName") //customerName
    private String contactorName;

    @ApiModelProperty(value = "预约人手机",name = "contactorPhone")  //customerPhone
    private String contactorPhone;

    @ApiModelProperty(value = "车主OneID",name = "ownerOneId")
    private Integer ownerOneId;

    @ApiModelProperty(value = "预约服务类型",name = "bookingTypeCode") // serviceType
    private String bookingTypeCode;

    @ApiModelProperty(value = "预约套餐Code",name = "bookingPackageCode")
    private String bookingPackageCode;

    @ApiModelProperty(value = "预约套餐名称",name = "bookingPackageName")
    private String bookingPackageName;

    @ApiModelProperty(value = "保养/检修预约备注",name = "remark") //remark
    private String remark;

    @ApiModelProperty(value = "vin码",name = "vin")//vin
    private String vin;

    @ApiModelProperty(value = "车牌号",name = "license")  //vehicleNumber
    private String license;

    @ApiModelProperty(value = "是否餐食",name = "isMeal")     //有isMeal
    private Integer isMeal;

    @ApiModelProperty(value = "服务顾问ID",name = "serviceAdvisor") //consultant
    private String serviceAdvisor;

    @ApiModelProperty(value = "是否提车",name = "needAscDrive")     //有isTakeCar
    private Integer needAscDrive;

    @ApiModelProperty(value = "是否送车",name = "isDeliver")        //isGiveCar
    private Integer isDeliver;

    @ApiModelProperty(value = "送车联系人姓名",name = "deliverName") //contactName
    private String deliverName;

    @ApiModelProperty(value = "送车联系人电话",name = "deliverPhone") //contactPhone
    private String deliverPhone;

    @ApiModelProperty(value = "C端确认状态", name = "portMode")
    private String cPortMode;

    @ApiModelProperty(value = "预约来源", name = "bookingSource")
    private Integer bookingSource;

    @ApiModelProperty(value = "业务码",name = "operationCode")
    private String operationCode;
    @ApiModelProperty(value = "预约单状态（默认未确认：80671006,未进厂：80671001,延迟进厂：80671002,提前进厂：80671003,准时进厂：80671004,取消进厂：80671005）", name = "bookingOrderStatus")
    private Integer bookingOrderStatus;

    @ApiModelProperty(value = "交修项目List",name = "orderHandrepair")
    List<HandRepairProjectParamsVo> orderHandRepair;

    @ApiModelProperty(value = "配件list",name = "orderpart")
    List<BookingOrderPartParamsVo> orderPart;

    @ApiModelProperty(value = "工时项目List",name = "orderlabour")
    List<BookingOrderLabourParamsVo> orderLabour;

}
