package com.yonyou.dmscus.customer.dto;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 根据vin判断 车辆24个月内是否有过进厂  参数
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-22
 */
@Data
public class CheckRepairOrderDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * vin
     */
    private String vin;

    /**
     * 是否进厂
     */
    private Integer isEnterFactory;

    /**
     * 经销商代码
     */
    private String ownerCode;

    private List<RepairOrderVO> orderVOList;

}
