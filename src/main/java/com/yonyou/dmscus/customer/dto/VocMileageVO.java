package com.yonyou.dmscus.customer.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * VOC里程基础表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-08
 */
@Data
public class VocMileageVO {

    private static final long serialVersionUID = 1L;


    /**
     * VIN
     */
    private String vin;


    /**
     * 里程(公里)
     */
    private Integer mileageKm;

    /**
     * 获取里程时间
     */
    private Date getTime;


    /**
     * 开票时间
     */
    private Date invoiceDate;

    BigDecimal dailyAverageMileage;

}
