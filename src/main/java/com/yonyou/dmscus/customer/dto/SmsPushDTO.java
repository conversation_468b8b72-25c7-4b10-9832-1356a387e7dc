package com.yonyou.dmscus.customer.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * description 短信推送对象
 *
 * <AUTHOR>
 * @date 2023/7/20 14:33
 */
@ApiModel(description = "短信推送对象")
@Data
public class SmsPushDTO {

    @ApiModelProperty(value = "推送OneId列表，多个时，用半角逗号(,)分隔")
    private String oneIds;

    @ApiModelProperty(value = "推送EmployeeId列表，多个时，用半角逗号(,)分隔")
    private String employeeIds;

    @ApiModelProperty(value = "推送手机号列表，多个时，用半角逗号(,)分隔")
    private String mobiles;

    @ApiModelProperty(value = "模板ID，需要提前在第三方短信平台上定义")
    private String templateId;

    @ApiModelProperty(value = "模板参数值，用于替换模板中定义的变量")
    private Object paramMap;

    @ApiModelProperty(hidden = true)
    private String batchId;

    private String appId;
}
