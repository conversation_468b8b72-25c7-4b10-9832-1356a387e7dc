package com.yonyou.dmscus.customer.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 用于返回经销商是否存在
 */
public class IsExistDealerResponseDto implements Serializable {

	private List<String> notExistCompanyCodeList;

	private boolean isAllExist;

	public List<String> getNotExistCompanyCodeList() {
		return notExistCompanyCodeList;
	}

	public void setNotExistCompanyCodeList(List<String> notExistCompanyCodeList) {
		this.notExistCompanyCodeList = notExistCompanyCodeList;
	}

	public boolean isAllExist() {
		return isAllExist;
	}

	public void setAllExist(boolean allExist) {
		isAllExist = allExist;
	}
}
