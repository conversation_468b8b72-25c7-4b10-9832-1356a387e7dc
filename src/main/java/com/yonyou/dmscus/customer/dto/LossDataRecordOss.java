package com.yonyou.dmscus.customer.dto;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: LossDataRecord
 * @projectName dmscus.customer
 * @date 2022/12/1217:35
 */
@Data
@ApiModel(value = "LossDataRecordOss", description = "流失客户表")
public class LossDataRecordOss extends BaseDTO {
    @ApiModelProperty(value = "vin",notes = "vin",required = true,name = "vin",example = "SHJ",dataType = "String")
    private String vin;

    @ApiModelProperty(value = "经销商",notes = "经销商",required = true,name = "dealerCode",example = "SHJ",dataType = "String")
    private List<String> dealerCodes;

    @ApiModelProperty(value = "最后一次保养经销商:指机率工单经销商",notes = "最后一次保养经销商:指机率工单经销商",required = true,name = "lastDealerCode",example = "SHJ",dataType = "String")
    private List<String>  lastDealerCodes;

    @ApiModelProperty(value = "最后一次保养时间",notes = "最后一次保养时间:指上一次含机滤工单结算日期",required = true,name = "orderAt",example = "",dataType = "String")
    private String orderAt;

    @ApiModelProperty(value = "最后一次保养时间",notes = "最后一次保养时间:指上一次含机滤工单结算日期",required = true,name = "endOrderAt",example = "",dataType = "String")
    private String endOrderAt;

    @ApiModelProperty(value = "开票时间",notes = "开票时间",required = true,name = "invoiceDate",example = "",dataType = "String")
    private String invoiceDate;

    @ApiModelProperty(value = "开票时间",notes = "开票时间",required = true,name = "endInvoiceDate",example = "",dataType = "String")
    private String endInvoiceDate;

    @ApiModelProperty(value = "是否激活:1,是；0,否",notes = "是否激活:1,是；0,否",required = true,name = "isActive",example = "",dataType = "Integer")
    private Integer isActive;

    @ApiModelProperty(value = "流失线索下发时间",notes = "流失线索下发时间",required = true,name = "recordAt",example = "",dataType = "String")
    private String recordAt;
    
    @ApiModelProperty(value = "流失线索下发时间",notes = "流失线索下发时间",required = true,name = "endRecordAt",example = "",dataType = "String")
    private String endRecordAt;

    private String dealerCode;

    private Integer indexFactory;
}
