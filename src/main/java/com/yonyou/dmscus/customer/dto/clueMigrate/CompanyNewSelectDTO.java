package com.yonyou.dmscus.customer.dto.clueMigrate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value = "经销商多条件查询", description="经销商多条件查询")
public class CompanyNewSelectDTO {

	@ApiModelProperty(value = "经销商代码")
	private List<String>  companyCodes;

	@ApiModelProperty(value = "经销商代码IK")
	private String  companyCodeLike;

	@ApiModelProperty(value = "经销商名称中文")
	private String  companyNameCn;

	@ApiModelProperty(value = "省份(可多个,用','分隔)")
	private String provinceId;

	@ApiModelProperty(value = "城市(可多个,用','分隔)")
	private String cityId;

	@ApiModelProperty(value = "售后大区(可多个,用','分隔)")
	private String  afterBigArea;

	@ApiModelProperty(value = "售后小区(可多个,用','分隔)")
	private String  afterSmallArea;

	public String getCompanyCodeLike() {
		return companyCodeLike;
	}

	public void setCompanyCodeLike(String companyCodeLike) {
		this.companyCodeLike = companyCodeLike;
	}

	public List<String> getCompanyCodes() {
		return companyCodes;
	}

	public void setCompanyCodes(List<String> companyCodes) {
		this.companyCodes = companyCodes;
	}

	public String getCompanyNameCn() {
		return companyNameCn;
	}

	public void setCompanyNameCn(String companyNameCn) {
		this.companyNameCn = companyNameCn;
	}

	public String getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(String provinceId) {
		this.provinceId = provinceId;
	}

	public String getCityId() {
		return cityId;
	}

	public void setCityId(String cityId) {
		this.cityId = cityId;
	}

	public String getAfterBigArea() {
		return afterBigArea;
	}

	public void setAfterBigArea(String afterBigArea) {
		this.afterBigArea = afterBigArea;
	}

	public String getAfterSmallArea() {
		return afterSmallArea;
	}

	public void setAfterSmallArea(String afterSmallArea) {
		this.afterSmallArea = afterSmallArea;
	}

}
