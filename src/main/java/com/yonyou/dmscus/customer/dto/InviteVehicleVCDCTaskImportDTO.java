package com.yonyou.dmscus.customer.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.service.excel.ExcelColumnDefine;
import com.yonyou.dmscloud.function.domains.dto.DataImportDto;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/11/3 0003
 */
@ApiModel(value = "邀约线索Excel导入实体")
@Data
public class InviteVehicleVCDCTaskImportDTO extends DataImportDto {

    /**
     * 邀约名称
     */
    @ExcelColumnDefine(value = 1)
    private String inviteName;

    /**
     * 经销商名称
     */
    @ExcelColumnDefine(value = 2)
    private String dealerCode;

    /**
     * 建议进厂日期
     */
    @ExcelColumnDefine(value = 3)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date adviseInDate;


    /**
     * 车架号
     */
    @ExcelColumnDefine(value = 4)
    private String vin;

    /**
     * 车架号
     */
    @ExcelColumnDefine(value = 5)
    private String licensePlateNum;

    /**
     * 联系人名称
     */
    @ExcelColumnDefine(value = 6)
    private String name;

    /**
     * 联系人电话
     */
    @ExcelColumnDefine(value = 7)
    private String tel;



}
