package com.yonyou.dmscus.customer.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@ApiModel("查询经销商详情的经销商参数")
@Getter
@Setter
@Accessors(chain = true)
public class CompanyParamDTO {

    /**
     * 经销商代码(可多个,用','隔开)
     */
    @ApiModelProperty(value = "经销商代码(可多个,用','隔开)")
    private String companyCode;

}
