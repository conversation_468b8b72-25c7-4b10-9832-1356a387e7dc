package com.yonyou.dmscus.customer.dto;


import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscus.customer.middleInterface.Constant;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserOrgInfoDTO extends BaseDTO {

	private static final long serialVersionUID = Constant.SERIAL_VERSION_UID;

    private Long userId;
	
    private String account;

    private String username;
    
    private String employeeNameEn;

    private String idcardNumber;

    private String phone;

    private String email;

    private String createBy;

    private LocalDateTime createTime;

    private String updateBy;

    private LocalDateTime updateTime;
    
    private Integer accountStatus;
    
    private String province;

    private Integer gender;

    private String country;

    private Integer score;

    private String heardImgUrl;

    private Integer workYears;

    private String unionId;
    
    private String roleCode;
    
    private String roleName;
    
    private Long  	companyId;
	
    private String  orgCode;
	
    private String  orgName;
	
    private String  orgShortName;
	
    private String  orgDesc;
	
    private Integer orgType;
	
    private String    parentOrgId;
	
    private Integer dataType;
	
    private Integer dataSource;
    
	private Integer isValid;

	private Long    orgid;

	private Long    orgId;

	private String providerCode;
	
	private String providerAccount;
	
	private String cdsid;

    private String mobilePhone;

    private String employeeName;


    public void setOrgid(Long orgid) {
        this.orgid = orgid;
        this.orgId = orgid;
    }

    public void setOrgId(Long orgId) {
        this.orgid = orgId;
        this.orgId = orgId;
    }

    public Long getOrgid() {
        return orgid;
    }

    public Long getOrgId() {
        return orgId;
    }
}
