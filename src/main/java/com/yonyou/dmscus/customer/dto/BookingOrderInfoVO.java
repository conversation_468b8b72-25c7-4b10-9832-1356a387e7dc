package com.yonyou.dmscus.customer.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BookingOrderInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单号
     */
    private String roNo;

    /**
     * 预约单创建时间
     */
    private Date createdAt;

    /**
     * 工单类型
     */
    private String roType;

    /**
     * 预约单号
     */
    private String bookingOrderNo;


    /**
     * 预约进厂时间
     */
    private Date bookingComeTime;

    /**
     * 中台预约单ID
     */
    private String appointmentId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * icmId
     */
    private long icmId;

    private Long id;

    public BookingOrderInfoVO(String bookingOrderNo, String dealerCode) {
        this.bookingOrderNo = bookingOrderNo;
        this.ownerCode = dealerCode;
    }
}
