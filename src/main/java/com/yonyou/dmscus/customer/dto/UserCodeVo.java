package com.yonyou.dmscus.customer.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: UserCodeVo
 * @projectName server2
 * @description: TODO
 * @date 2022/7/1914:49
 */
@Data
@ApiModel
public class UserCodeVo {
    @ApiModelProperty(value = "登录账号")
    private String  userCode;
    @ApiModelProperty(value = "currentPage")
    Long currentPage;
    @ApiModelProperty(value = "pageSize")
    Long pageSize;

}
