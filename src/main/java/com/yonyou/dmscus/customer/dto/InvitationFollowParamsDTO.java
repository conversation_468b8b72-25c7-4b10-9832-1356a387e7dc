package com.yonyou.dmscus.customer.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel("线索查询参数")
public class InvitationFollowParamsDTO {


    @ApiModelProperty(value = "邀约类型",name = "inviteType")
    private List<Integer> inviteType;
    @ApiModelProperty(value = "划跟进开始时间",name = "planFollowDateStart")
    private String planFollowDateStart;
    @ApiModelProperty(value = "划跟进结束时间",name = "planFollowDateEnd")
    private String planFollowDateEnd;

    private String licensePlateNum;
    private String vin;
    private String actualFollowDateStart;
    private String actualFollowDateEnd;
    private String name;
    private Integer isBook;
    private String adviseInDateStart;
    private String adviseInDateEnd;
    private List<Integer> followStatus;
    private List<Integer> leaveIds;
    private Integer orderStatus;
    private List<Integer> orderStatusParam;
    private String saName;
    private  String saId;
    @ApiModelProperty(value = "线索创建时间开始",name = "createdAtStart")
    private String createdAtStart;
    @ApiModelProperty(value = "线索创建时间结束",name = "createdAtEnd")
    private String createdAtEnd;
    private Integer isself;
    private Long currentPage;
    private Long pageSize;
    /**
     * 大区 查询条件
     */
    private String largeAreaId;

    /**
     * 小区 查询条件
     */
    private String areaId;
    /**
     * 经销商代码
     */
    @ApiModelProperty(value = "经销商代码",name = "dealerCode")
    private String dealerCode;

    /**
     * 二次跟进月份
     */
    private String monthTwice;

    /**
     * 是否二次跟进
     */
    @ApiModelProperty(value = "线索类型:0 VOC线索,1普通线索 ",name = "线索类型")
    private Integer recordType;

    /**
     *线索类型:0 ：非VOC类型,1 ：18个月未保养，2：非4S店保养流失,3:流失客户保养灯亮
     */
    private Integer lossType;

    /**
     * 流失预警类型
     */
    @ApiModelProperty(value = "流失预警类型")
    private Integer lossWarningType;

    @ApiModelProperty(value = "卡劵code",name = "couponCode")
    private String couponCode;

    @ApiModelProperty(value = "卡劵名称",name = "couponName")
    private String couponName;

    /**
     * 线索类型 查询条件
     */
    @ApiModelProperty(value = "线索类型",name = "recordTypeParam", example = "[87891001,87891002,87891003]")
    private List<Integer> recordTypeParam;

    @ApiModelProperty(value = "返厂意向",name = "returnIntentionLevel")
    private int returnIntentionLevel;
    @ApiModelProperty("是否bev")
    private Integer bevFlag;
}
