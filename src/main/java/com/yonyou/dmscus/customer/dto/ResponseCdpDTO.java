package com.yonyou.dmscus.customer.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
@Data
@ApiModel(description = "数据组返回结果")
public class ResponseCdpDTO implements Serializable {
	private static final long serialVersionUID = 1L;
	public static final String SUCCESS = "00000";
	@ApiModelProperty(value = "返回代码，0表示成功，其他表示失败")
	private String retCode;

	@ApiModelProperty(value = "返回描述")
	private String retInfo;

	@ApiModelProperty(value = "返回数据")
	private JSONObject retResult;
}
