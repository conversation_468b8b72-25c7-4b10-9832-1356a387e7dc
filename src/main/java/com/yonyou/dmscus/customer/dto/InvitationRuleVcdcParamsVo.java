package com.yonyou.dmscus.customer.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel("邀约规则查询参数")
public class InvitationRuleVcdcParamsVo {

    @ApiModelProperty(value = "大区",name = "largeAreaId")
    private String largeAreaId;

    @ApiModelProperty(value = "小区",name = "areaId")
    private String areaId;

    @ApiModelProperty(value = "区域负责人",name = "areaManageId")
    private String areaManageId;

    @ApiModelProperty(value = "经销商代码",name = "dealerCode")
    private String dealerCode;

    @ApiModelProperty(value = "经销商名称",name = "dealerName")
    private String dealerName;
    @ApiModelProperty(value = "邀约类型",name = "inviteType")
    private Integer inviteType;
    @ApiModelProperty(value = "邀约规则",name = "inviteRule")
    private String inviteRule;
}
