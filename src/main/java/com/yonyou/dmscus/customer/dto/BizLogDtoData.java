package com.yonyou.dmscus.customer.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("业务指标监控用户接入")
@Data
public class BizLogDtoData {

    /**
     * 拒收的经销商code
     */
    @ApiModelProperty(value = "拒收的经销商code", required = true, example = "[\"SHJ\",\"SHN\"]")
    private List<String> rejectionCodes;

    /**
     * 执行成功与否
     */
    @ApiModelProperty(value = "执行成功与否", required = true, example = "true")
    private Boolean flag;

}
