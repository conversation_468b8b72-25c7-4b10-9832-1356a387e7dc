package com.yonyou.dmscus.customer.dto;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscus.customer.ossexcel.ExcelExportColumn;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 下载对象
 */
@Data
public class DownloadDTO extends BaseDTO {

    @ApiModelProperty("回调的url")
    private String serviceUrl;
    @ApiModelProperty("查询条件")
    private Map<String, Object> queryParams;
    @ApiModelProperty("表头信息")
    private List<ExcelExportColumn> excelExportColumnList;
    @ApiModelProperty("excel命名")
    private String excelName;
    @ApiModelProperty("sheet命名")
    private String sheetName;
    @ApiModelProperty("id列属性名")
    private String fieldId;


}
