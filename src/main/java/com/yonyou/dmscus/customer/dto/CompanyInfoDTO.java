package com.yonyou.dmscus.customer.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

@ApiModel("公司详细信息")
@Getter
@Setter
public class CompanyInfoDTO {

    /**
     * 经销商代码
     */
    @ApiModelProperty(value = "经销商代码")
    private String companyCode;

    /**
     * 公司中文全称
     */
    @ApiModelProperty(value = "公司中文全称")
    private String companyNameCn;

    /**
     * 公司英文全称
     */
    @ApiModelProperty(value = "公司英文全称")
    private String companyNameEn;

    /**
     * 公司简称中文
     */
    @ApiModelProperty(value = "公司简称中文")
    private String companyShortNameCn;

    /**
     * 公司简称英文
     */
    @ApiModelProperty(value = "公司简称英文")
    private String companyShortNameEn;

    /**
     * 经销商类型（3S，4S，SC，SR，SC+SR）
     */
    @ApiModelProperty(value = "经销商类型（3S，4S，SC，SR，SC+SR），字典1502")
    private Integer dealerType;

    /**
     * 是否卫星店
     */
    @ApiModelProperty(value = "是否卫星店")
    private Integer isSatelliteStore;

    /**
     * 所属经销商Code
     */
    @ApiModelProperty(value = "所属经销商Code")
    private String ownedCompanyName;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CompanyInfoDTO that = (CompanyInfoDTO) o;
        return Objects.equals(companyCode, that.companyCode) &&
                Objects.equals(companyNameCn, that.companyNameCn) &&
                Objects.equals(companyNameEn, that.companyNameEn) &&
                Objects.equals(companyShortNameCn, that.companyShortNameCn) &&
                Objects.equals(companyShortNameEn, that.companyShortNameEn) &&
                Objects.equals(dealerType, that.dealerType) &&
                Objects.equals(isSatelliteStore, that.isSatelliteStore) &&
                Objects.equals(ownedCompanyName, that.ownedCompanyName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(companyCode, companyNameCn, companyNameEn, companyShortNameCn, companyShortNameEn, dealerType, isSatelliteStore, ownedCompanyName);
    }
}
