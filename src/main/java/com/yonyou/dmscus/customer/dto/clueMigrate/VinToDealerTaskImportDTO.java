package com.yonyou.dmscus.customer.dto.clueMigrate;

import com.yonyou.dmscloud.framework.service.excel.ExcelColumnDefine;
import com.yonyou.dmscloud.function.domains.dto.DataImportDto;
import lombok.Data;

@Data
public class VinToDealerTaskImportDTO extends DataImportDto implements java.io.Serializable {

    @ExcelColumnDefine( value = 1)
    private String sourceOwnerCode;

    /**
     * vin
     */
    @ExcelColumnDefine( value = 2)
    private String vin;

    /**
     * 经销商代码
     */
    @ExcelColumnDefine( value = 3)
    private String ownerCode;



}
