package com.yonyou.dmscus.customer.dto.cdp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CdpTagTaskParameterDto {

    @ApiModelProperty("vin")
    private String vin;

    @ApiModelProperty("dailyMile")
    private String dailyMile;

    @ApiModelProperty("adviseInDate")
    private String adviseInDate;

    @ApiModelProperty("returnIntentionLevel")
    private String returnIntentionLevel;

    @ApiModelProperty("whiteList")
    private List<String> whiteList;

}
