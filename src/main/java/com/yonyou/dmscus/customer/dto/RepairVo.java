package com.yonyou.dmscus.customer.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: RepairVo
 * @projectName dmscus.report
 * @date 2022/12/1314:28
 */
@Data
@ApiModel(value = "客户流失",description = "客户流失")
public class RepairVo {
    private Date orderAt;

    private String lastDealerCode;


    private Date invoiceDate;

    private String ownerName;

    private String mobile;
    private  String vin;
    private  String dealerCode;

}
