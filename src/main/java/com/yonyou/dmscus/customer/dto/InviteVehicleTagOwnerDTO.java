package com.yonyou.dmscus.customer.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
public class InviteVehicleTagOwnerDTO implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
    /** 车架号 */
    private String vin;
    
    /** 经销商代码 */
    private String dealerCode;
    
    /** 经销商名称 */
    private String dealerName;
	
    /** 开票日期 */
    private Date invoiceDate;
    
    
    /** 车牌号 */
    private String plateNumber;
    

    /** 车主姓名 */
    private String name;

    /** 车主电话 */
    private String mobile;

    /** 客户唯一Id */
    private Long oneId; 
    
    /** 是否大客户代码 : (是:10041001,否:10041002) */
    private Integer FleetCode;
    
    /** 是否大客户 */
    private boolean isFleet;
    
    /** 标签格式化字符 */
	private String tagName;
	
	/** 标签集合 */
	private List<String> tagNameList;
    

}
