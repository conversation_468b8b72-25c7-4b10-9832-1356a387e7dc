package com.yonyou.dmscus.customer.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * description 保养灯上下线信号入参
 * <AUTHOR>
 * @date 2023/9/15 15:07
 */
@Data
public class OnlineOfflineDTO {

	@ApiModelProperty(value = "车架号", required = true, example = "[1,1,1]")
	private List<String> vehicle_vin;

	@ApiModelProperty(value = "当前页，不填时默认值为1", example = "1")
	private int pageNum;

	@ApiModelProperty(value = "每页的记录数量，不填时默认值为20，最大值为100，超过100的值，按100处理", example = "100")
	private int pageSize;
}
