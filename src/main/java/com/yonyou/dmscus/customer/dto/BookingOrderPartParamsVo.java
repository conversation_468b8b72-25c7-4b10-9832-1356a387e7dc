package com.yonyou.dmscus.customer.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BookingOrderPartParamsVo {

    /**
     * 交修项目编号ID(配件)
     */
    @ApiModelProperty(value = "交修项目编号ID配件",name = "itemIdHandProject")
    private Long itemIdHandProject;

    /**
     * 仓库代码
     */
    @ApiModelProperty(value = "仓库代码",name = "storageCode")
    private String storageCode;

    /**
     * 配件代码
     */
    @ApiModelProperty(value = "配件代码",name = "partNo")
    private String partNo;

    /**
     * 配件名称
     */
    @ApiModelProperty(value = "配件名称",name = "partName")
    private String partName;

    /**
     * 销售价
     */
    @ApiModelProperty(value = "销售价",name = "salesPrice")
    private double salesPrice;

    /**
     * 套餐代码
     */
    @ApiModelProperty(value = "套餐代码",name = "setCode")
    private String setCode;

    /**
     * 预约数量
     */
    @ApiModelProperty(value = "预约数量",name = "bookingQuantity")
    private double bookingQuantity ;

    /**
     * 适用维修类型
     */
    @ApiModelProperty(name = "适用维修类型",value = "jobType" )
    private String jobType;

    /**
     * 适用账类
     */
    @ApiModelProperty(name = "适用账类",value = "accountGroup")
    private String accountGroup;

    /**
     * 折扣率
     */
    @ApiModelProperty(name = "折扣率",value = "discount")
    private Double discount;

}
