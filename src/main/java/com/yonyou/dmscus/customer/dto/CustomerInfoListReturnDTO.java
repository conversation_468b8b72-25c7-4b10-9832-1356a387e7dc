package com.yonyou.dmscus.customer.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 客户列表保存返回信息
 * <AUTHOR>
 */
@Data
@ApiModel(value = "客户列表保存返回信息", description="客户列表保存返回信息")
public class CustomerInfoListReturnDTO {

    @ApiModelProperty(value = "返回状态")
    private Integer  record;
    @ApiModelProperty(value = "客户ID")
    private Long  	id;
    @ApiModelProperty(value = "客户电话")
    private String  	mobile;
    @ApiModelProperty(value = "状态说明")
    private String  message;

}
