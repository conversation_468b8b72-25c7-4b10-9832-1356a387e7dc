package com.yonyou.dmscus.customer.dto.clueMigrate;

import com.yonyou.dmscus.customer.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel
@Data
public class ClueMigrateTaskQueryDTO extends PageQueryDTO implements java.io.Serializable {

    @ApiModelProperty( hidden = true )
    private Integer syncType;

    @ApiModelProperty( "经销商编码" )
    private String ownerCode;

    @ApiModelProperty( "原经销商编码" )
    private String sourceOwnerCode;

    @ApiModelProperty( "同步状态 0 初始等待 1 完成 2 失败" )
    private Integer syncStatus;

    @ApiModelProperty( "vin" )
    private String vin;

}
