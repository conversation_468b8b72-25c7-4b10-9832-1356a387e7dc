package com.yonyou.dmscus.customer.dto;

import java.time.LocalDateTime;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import lombok.Data;

/**
 * @Author: yugu
 * @CreateDate: 2019/9/6
 * @Description:
 */
@Data
public class UserInfoOutDTO extends BaseDTO {

	private Long id;

	private Long userId;

	private Long empId;

	private String employeeName;

	private String account;

	private String username;

	private String idcardNumber;

	private String phone;

	private String email;

	private Long createBy;

	private LocalDateTime createTime;

	private Long updateBy;

	private LocalDateTime updateTime;

	private Integer accountStatus;

	private String province;

	private Integer gender;

	private String country;

	private Integer score;

	private String heardImgUrl;

	private Integer workYears;

	private String unionId;

	private Long companyId;

	private String roleName;

	private String companyNameCn;

}
