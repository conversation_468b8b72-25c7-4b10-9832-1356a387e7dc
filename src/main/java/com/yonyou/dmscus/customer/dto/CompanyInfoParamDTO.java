package com.yonyou.dmscus.customer.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@ApiModel("查询公司详情的参数")
@Getter
@Setter
public class CompanyInfoParamDTO {

    @ApiModelProperty("经销商代码列表")
    private List<String> companyCodes;

    @ApiModelProperty("公司类型，字典1506")
    private List<String> companyTypes;

    @ApiModelProperty("经销商类型列表（3S，4S，SC，SR，SC+SR），字典1502")
    private List<Integer> dealerTypes;

    @ApiModelProperty("经销商id列表")
    private List<Integer> ids;

    @ApiModelProperty("是否卫星店")
    private Integer isSatelliteStore;

    @ApiModelProperty("所属经销商Code")
    private String ownedCompanyName;

    @ApiModelProperty("经销商营业状态")
    private Integer status;

}
