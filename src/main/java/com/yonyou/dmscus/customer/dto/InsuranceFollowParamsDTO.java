package com.yonyou.dmscus.customer.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
public class InsuranceFollowParamsDTO {


    private List<Integer> inviteType;
    private String planFollowDateStart;
    private String planFollowDateEnd;
    private String licensePlateNum;
    private String vin;
    private String actualFollowDateStart;
    private String actualFollowDateEnd;
    private String name;
    private Integer isBook;
    private String adviseInDateStart;
    private String adviseInDateEnd;
    private List<Integer> followStatus;
    private List<Integer> leaveIds;
    private Integer orderStatus;
    private List<Integer> orderStatusParam;
    private String saName;
    private  String saId;
    private String createdAtStart;
    private String createdAtEnd;
    private Integer isself;
    private Long currentPage;
    private Long pageSize;
    /**
     * 大区 查询条件
     */
    private String largeAreaId;

    /**
     * 小区 查询条件
     */
    private String areaId;
    /**
     * 经销商代码
     */
    private String dealerCode;

    private Integer clueType;

    private Integer insuranceType;

    /**
     * 邀约创建日期
     */
    private String invitationDateStart;
    private String invitationDateEnd;

    /**
     * 保险公司
     */
    private String insuranceName;


    /**
     * 保险到期日期  日历选择	现有字段，续保到期日期
     */
    private String adviseInDate;

    /**
     * 厂家保险到期日期	日历选择
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String factoryInsuranceExpiryDate;


    /**
     * 建议关怀日期	日历选择
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String suggestedCareDate;

    /**
     * 线索数据源	下拉选择
     */
    private Integer clueDataSource;

    /**
     * 线索下发类型	下拉选择
     */
    private Integer clueIssuanceType;

    /**
     * 是否有自店线索升级	下拉选择
     */
    private Integer hasInstoreUpgrade;

    /**
     * 是否有自店修改保险到期日期	下拉选择
     */
    private Integer hasInstoreModified;

    private String factoryInsuranceExpiryDateStart;

    private String factoryInsuranceExpiryDateEnd;

    private String suggestedCareDateStart;

    private String suggestedCareDateEnd;

}
