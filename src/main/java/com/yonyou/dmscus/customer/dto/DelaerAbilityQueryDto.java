package com.yonyou.dmscus.customer.dto;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

public class DelaerAbilityQueryDto extends BaseDTO implements Serializable {

	private Integer year;
	private Integer month;
	private String dealerArea;
	private String area;
	private String dealerCode;
	private String dealerName;
	private String dealerScale;
	private String accountCode;
	private String accountStatus;
	private Date serviceStartTime;
	private String sname;
	private String suname;
	private String postName;
	private String abilityName;
	private Date abilityGetTime;
	private Date updateTime;

	private Long bigAreaId;
	private String bigArea;
	private Long smallAreaId;
	private String smallArea;

	private int currentPage;
	private int pageSize;
	private String yearAndMonth;
	private String dealerCodeOrName;

	public Integer getYear() {
		return year;
	}

	public void setYear(Integer year) {
		this.year = year;
	}

	public Integer getMonth() {
		return month;
	}

	public void setMonth(Integer month) {
		this.month = month;
	}

	public String getDealerArea() {
		return dealerArea;
	}

	public void setDealerArea(String dealerArea) {
		this.dealerArea = dealerArea;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getDealerCode() {
		return dealerCode;
	}

	public void setDealerCode(String dealerCode) {
		this.dealerCode = dealerCode;
	}

	public String getDealerName() {
		return dealerName;
	}

	public void setDealerName(String dealerName) {
		this.dealerName = dealerName;
	}

	public String getDealerScale() {
		return dealerScale;
	}

	public void setDealerScale(String dealerScale) {
		this.dealerScale = dealerScale;
	}

	public String getAccountCode() {
		return accountCode;
	}

	public void setAccountCode(String accountCode) {
		this.accountCode = accountCode;
	}

	public String getAccountStatus() {
		return accountStatus;
	}

	public void setAccountStatus(String accountStatus) {
		this.accountStatus = accountStatus;
	}

	public Date getServiceStartTime() {
		return serviceStartTime;
	}

	public void setServiceStartTime(Date serviceStartTime) {
		this.serviceStartTime = serviceStartTime;
	}

	public String getSname() {
		return sname;
	}

	public void setSname(String sname) {
		this.sname = sname;
	}

	public String getSuname() {
		return suname;
	}

	public void setSuname(String suname) {
		this.suname = suname;
	}

	public String getPostName() {
		return postName;
	}

	public void setPostName(String postName) {
		this.postName = postName;
	}

	public String getAbilityName() {
		return abilityName;
	}

	public void setAbilityName(String abilityName) {
		this.abilityName = abilityName;
	}

	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	public Date getAbilityGetTime() {
		return abilityGetTime;
	}

	public void setAbilityGetTime(Date abilityGetTime) {
		this.abilityGetTime = abilityGetTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public int getCurrentPage() {
		return currentPage;
	}

	public void setCurrentPage(int currentPage) {
		this.currentPage = currentPage;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public String getYearAndMonth() {
		return yearAndMonth;
	}

	public void setYearAndMonth(String yearAndMonth) {
		this.yearAndMonth = yearAndMonth;
	}

	public String getDealerCodeOrName() {
		return dealerCodeOrName;
	}

	public void setDealerCodeOrName(String dealerCodeOrName) {
		this.dealerCodeOrName = dealerCodeOrName;
	}

	public Long getBigAreaId() {
		return bigAreaId;
	}

	public void setBigAreaId(Long bigAreaId) {
		this.bigAreaId = bigAreaId;
	}

	public String getBigArea() {
		return bigArea;
	}

	public void setBigArea(String bigArea) {
		this.bigArea = bigArea;
	}

	public Long getSmallAreaId() {
		return smallAreaId;
	}

	public void setSmallAreaId(Long smallAreaId) {
		this.smallAreaId = smallAreaId;
	}

	public String getSmallArea() {
		return smallArea;
	}

	public void setSmallArea(String smallArea) {
		this.smallArea = smallArea;
	}
}
