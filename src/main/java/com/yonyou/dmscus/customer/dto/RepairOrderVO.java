package com.yonyou.dmscus.customer.dto;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 * VOC里程基础表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-08
 */
@Data
public class RepairOrderVO {

    private static final long serialVersionUID = 1L;

    /**
     * VIN
     */
    private String vin;
    /**
     * 出厂时间
     */
    private Date deliveryDate;
    /**
     * 店代码
     */
    private String dealerCode;
    /**
     * 车店分配后的店代码或者销售经销商
     */
    private String salesDealerCode;
    /**
     * 车店分配时间
     */
    private Date newTime;
    /**
     * 工单号
     */
    private String roNo;
    /**
     * 工单开单时间
     */
    private Date roCreateDate;
    /**
     * 工单开单时间
     */
    private Double outMileage;
    /**
     * 维修类型
     */
    private String repairTypeCode;
    /**
     * 代码
     */
    private String ownerCode;
    /**
     * 送修人
     */
    private String deliverer;
    /**
     * 送修人性别
     */
    private String delivererGender;
    /**
     * 送修人电话
     */
    private String delivererPhone;
    /**
     * 送修人手机
     */
    private String delivererMobile;
    /**
     * 结算时间
     */
    private Date balanceTime;
    /**
     * fg
     */
    private String functionGroup;
}
