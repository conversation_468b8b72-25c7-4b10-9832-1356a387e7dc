package com.yonyou.dmscus.customer.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@ApiModel("续保线索查询")
public class SaSllocateDlrParamsDTO {
    private List<Integer> inviteType;
    private  String planFollowDateStart;
    private  String planFollowDateEnd;
    private  String licensePlateNum;
    private  String vin;
    private  String actualFollowDateStart;
    private  String actualFollowDateEnd;
    private  String name;
    private  Integer isBook;
    @ApiModelProperty(value = "续保到期日期开始",name = "adviseInDateStart")
    private  String adviseInDateStart;
    @ApiModelProperty(value = "续保到期日期结束",name = "adviseInDateEnd")
    private  String adviseInDateEnd;
    private  List<Integer> followStatus;
    private  String saName;
    private  String saId;
    private  String createdAtStart;
    private  String createdAtEnd;
    private  Boolean isNoDistribute;
    private  Boolean isWaitDistribute;
    private  List<Integer> leaveIds;
    private Integer orderStatus;
    private Integer clueType;
    private  List<Integer> orderStatusParam;
    private  int currentPage;
    private  int pageSize;
    @ApiModelProperty(value = "返厂意向",name = "returnIntentionLevel")
    private int returnIntentionLevel;

    /**
     * 保险公司
     */
    private String insuranceName;
    @ApiModelProperty(value = "续保客户类型",name = "insuranceTypeList")
    private  List<Integer> insuranceTypeList;
    
}
