package com.yonyou.dmscus.customer.dto;

import com.yonyou.dmscus.customer.entity.dto.clue.LiteCrmClueDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationCreate.InviteVehicleDealerTaskDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteRule.InviteVehicleDealerTaskImportDto;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(description = "线索下发通用接口参数")
public class EM90MessageRemindDto {

	/**
	 * 厂端自建线索对象
	 */
	private InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDto;

	/**
	 * 导入线索 vinList
	 */
	private InviteVehicleDealerTaskImportDto inviteVehicleDealerTaskImportDto;

	/**
	 *
	 */
	private LiteCrmClueDTO liteCrmClueDto;
}
