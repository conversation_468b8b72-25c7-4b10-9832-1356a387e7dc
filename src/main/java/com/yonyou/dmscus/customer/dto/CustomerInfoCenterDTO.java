package com.yonyou.dmscus.customer.dto;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscus.customer.middleInterface.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 客户信息新增对象
 * <AUTHOR>
 */
@Data
@ApiModel(value="客户信息新增对象", description="客户信息的新增")
public class CustomerInfoCenterDTO extends BaseDTO {

    private static final long serialVersionUID = Constant.SERIAL_VERSION_UID;

    @ApiModelProperty(value = "OPEN_ID", name = "openId", required = false)
    private String openId;

    @ApiModelProperty(value = "UNION_ID", name = "unionId", required = false)
    private String unionId;

    @ApiModelProperty(value = "客户姓名", name = "name", required = false)
    private String name;

    @ApiModelProperty(value = "手机", name = "mobile", required = false)
    private String mobile;

    @ApiModelProperty(value = "状态代码(有效:31141001,无效:31141002)", name = "statusCode", required = false)
    private Integer statusCode;

    @ApiModelProperty(value = "客户类型(个人:15231001,公司:15231002)", name = "customerType", required = false)
    private Integer customerType;

    @ApiModelProperty(value = "证件类型(身份证:15081001,护照:15081002,军官证:15081003,士兵证:15081004,警察证:15081005,其他:15081006,机构代码:15081007)", name = "ctCode", required = false)
    private Integer ctCode;

    @ApiModelProperty(value = "证件号码", name = "certificateNo", required = false)
    private String certificateNo;

    @ApiModelProperty(value = "税号", name = "dutyParagraph", required = false)
    private String dutyParagraph;

    @ApiModelProperty("车牌")
    private String license;

    public CustomerInfoCenterDTO() {
    }

    public CustomerInfoCenterDTO(String name, String mobile) {

        this.name = name;
        this.mobile = mobile;
    }
}
