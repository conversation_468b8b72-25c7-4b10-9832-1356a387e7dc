package com.yonyou.dmscus.customer.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.swing.plaf.PanelUI;

/**
 * description 保养灯上下线信号返回参数
 * <AUTHOR>
 * @date 2023/9/15 15:07
 */
@Data
public class OnlineOfflineResultDTO {
	public static final String NO = "否";
	public static final String YES = "是";
	@ApiModelProperty(value = "车架号", required = true, example = "1")
	private String vehicle_vin;

	@ApiModelProperty(value = "最近一次跟进中的售后邀约线索下发后是否保养灯消失",  required = true, example = "是")
	private String light_status_tag;

	@ApiModelProperty(value = "近2个月是否有车辆上下线信号",required = true, example = "是")
	private String onoff_status_tag;

	@ApiModelProperty(value = "标签计算日期 (yyyymmdd)",required = true, example = "20230915")
	private String tag_upd_date;

	@ApiModelProperty(value = "最近一次保养灯信号ID (标签计算日期之前)",required = true, example = "1002")
	private String warning_id;

	@ApiModelProperty(value = "最近一次保养灯信号名称 (标签计算日期之前)",required = true, example = "轮胎故障")
	private String warning_name;

	@ApiModelProperty(value = "最近一次保养灯信号出发时间 (标签计算日期之前)",required = true, example = "20230906")
	private String warning_time;
}
