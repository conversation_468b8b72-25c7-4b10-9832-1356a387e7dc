package com.yonyou.dmscus.customer.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;


import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 客诉详情查询
 */
@Data
public class ComplaintInfMoreVo implements Serializable {

    /**
     * 系统ID
     */
    @TableField("app_id")
    private String appId;

    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    @TableField("owner_par_code")
    private String ownerParCode;

    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 投诉ID（投诉编号）可 用于和呼叫中心对接
     */
    @TableField("complaint_id")
    private String complaintId;

    /**
     * 投诉类型
     */
    @TableField("type")
    private String type;

    /**
     * 投诉类型(销售)
     */
    private Integer saleType;

    /**
     * 投诉分类
     */
    @TableField("classification")
    private Integer classification;

    /**
     * 投诉来源
     */
    @TableField("source")
    private String source;

    /**
     * 来电时间/投诉日期
     */
    @TableField("call_time")
    private Date callTime;

    /**
     * 通知时间
     */
    @TableField("inform_time")
    private String informTime;

    /**
     * 通知时间
     */
    @TableField("first_restart_time")
    private String firstRestartTime;

    /**
     * 首次重启日期
     */
    @TableField("fisrt_restart_time")
    private Date fisrtRestartTime;
    private Date fisrtRestartTime2;

    /**
     * 最新重启日期
     */
    @TableField("newest_restart_time")
    private Date newestRestartTime;

    /**
     * 来电客户姓名/投诉人姓名
     */
    @TableField("call_name")
    private String callName;

    /**
     * 工单状态
     */
    @TableField("work_order_status")
    private Integer workOrderStatus;
    /**
     * 是否结案
     */
    @TableField("is_close_case")
    private Integer isCloseCase;

    /**
     * 投诉人性别
     */
    @TableField("sex")
    private Integer sex;

    /**
     * 来电电话/投诉人电话
     */
    @TableField("call_tel")
    private String callTel;

    /**
     * 车主姓名
     */
    @TableField("name")
    private String name;

    /**
     * 车牌号
     */
    @TableField("license_plate_num")
    private String licensePlateNum;

    /**
     * 车架号
     */
    @TableField("vin")
    private String vin;

    /**
     * 购车时间
     */
    @TableField("buy_time")
    private Date buyTime;

    /**
     * 车型
     */
    @TableField("model")
    private String model;

    /**
     * 年款
     */
    @TableField("model_year")
    private String modelYear;
    /**
     * 车款名称
     */
    @TableField("config_name")
    private String configName;

    /**
     * 购买经销商
     */
    @TableField("buy_dealer_name")
    private String buyDealerName;

    /**
     * 处理经销商
     */
    @TableField("dealer_name")
    private String dealerName;

    /**
     * 处理经销商代码
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 购买经销商代码
     */
    @TableField("buy_dealer_code")
    private String buyDealerCode;

    /**
     * 里程
     */
    @TableField("mileage")
    private Integer mileage;
    /**
     * 车主地址
     */
    @TableField("owner_address")
    private String ownerAddress;

    /**
     * 回复联系人手机1
     */
    @TableField("reply_tel")
    private String replyTel;

    /**
     * 回复联系人手机2
     */
    @TableField("reply_tel2")
    private String replyTel2;

    /**
     * 投诉主题
     */
    @TableField("subject")
    private String subject;

    /**
     * 问题描述
     */
    @TableField("problem")
    private String problem;

    /**
     * 坐席主管说明
     */
    @TableField("illustrate")
    private String illustrate;

    /**
     * 投诉单类别一级层
     */
    @TableField("category1")
    private String category1;

    /**
     * 投诉单类别二级层
     */
    @TableField("category2")
    private String category2;

    /**
     * 投诉单类别三级层
     */
    @TableField("category3")
    private String category3;

    /**
     * 部位
     */
    @TableField("part")
    private String part;

    /**
     * 细分部位
     */
    @TableField("subdivision_part")
    private String subdivisionPart;

    /**
     * 问题
     */
    @TableField("problem_info")
    private String ProblemInfo;

    /**
     * 客户要求
     */
    @TableField("cus_requirement")
    private String cusRequirement;

    /**
     * CC部位
     */
    @TableField("cc_part")
    private String ccPart;

    /**
     * CC细分部位
     */
    @TableField("cc_subdivision_part")
    private String ccSubdivisionPart;

    /**
     * CC问题
     */
    @TableField("cc_problem")
    private String ccProblem;

    /**
     * CC要求
     */
    @TableField("cc_requirement")
    private String ccRequirement;

    /**
     * 是否上报
     */
    @TableField("is_report")
    private Boolean isReport;

    /**
     * 投诉部门
     */
    @TableField("department")
    private String department;

    /**
     * 接待员
     */
    @TableField("receptionist")
    private String receptionist;

    /**
     * 重要等级
     */
    @TableField("importance_level")
    private Integer importanceLevel;

    /**
     * 经销商首次回复时间
     */
    @TableField("dealer_fisrt_reply_time")
    private Date dealerFisrtReplyTime;

    /**
     * 首次重启经销商首次回复时间
     */
    @TableField("fisrt_restart_dealer_fisrt_reply_time")
    private Date fisrtRestartDealerFisrtReplyTime;

    /**
     * 是否回访
     */
    @TableField("is_revisit")
    private Integer isRevisit;

    /**
     * 回访时间
     */
    @TableField("revisit_time")
    private Date revisitTime;

    /**
     * 回访结果
     */
    @TableField("revisit_result")
    private String revisitResult;

    /**
     * 回访内容
     */
    @TableField("revisit_content")
    private String revisitContent;

    /**
     * 经销商申请结案时间
     */
    @TableField("apply_time")
    private Date applyTime;

    /**
     * 区域经理是否同意
     */
    @TableField("is_agree")
    private Integer isAgree;

    /**
     * 区域经理提交结案时间
     */
    @TableField("submit_time")
    private Date submitTime;

    /**
     * 结案时间
     */
    @TableField("close_case_time")
    private Date closeCaseTime;

    /**
     * 重启结案时间
     */
    @TableField("restart_close_case_time")
    private Date restartCloseCaseTime;

    /**
     * 结案状态（投诉单状态）
     */
    @TableField("close_case_status")
    private Integer closeCaseStatus;

    /**
     * 跟进状态
     */
    @TableField("follow_status")
    private String followStatus;

    /**
     * 投诉的根本原因 多选用逗号分隔
     */
    @TableField("basic_reason")
    private String basicReason;

    /**
     * 车辆是否修复
     */
    @TableField("is_repaired")
    private Integer isRepaired;

    /**
     * 技术与维修方案
     */
    @TableField("tech_maintain_plan")
    private String techMaintainPlan;

    /**
     * 亲善方案
     */
    @TableField("rapport_plan")
    private String rapportPlan;

    /**
     * 潜在风险
     */
    @TableField("risk")
    private String risk;

    /**
     * 进销商是否已读 1 已读 0 未读
     */
    @TableField("dealer_is_read")
    private Boolean dealerIsRead;

    /**
     * 区域经理是否已读 1 已读 0 未读
     */
    @TableField("manager_is_read")
    private Boolean managerIsRead;

    /**
     * CCM是否已读 1 已读 0 未读
     */
    @TableField("ccm_is_read")
    private Boolean ccmIsRead;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;
    /**
     * 区域
     */
    @TableField("region")
    private String region;

    /**
     * 区域经理
     */
    @TableField("region_manager")
    private String regionManager;

    /**
     * 集团
     */
    @TableField("bloc")
    private String bloc;

    /**
     * 区域ID
     */
    @TableField("region_id")
    private  String regionId;


    /**
     * 区域经理ID
     */
    @TableField("region_manager_id")
    private  String regionManagerId;


    /**
     * CCM负责人
     */
    @TableField("ccm_man")
    private String ccmMan;

    /**
     * 质量部分类1
     */
    @TableField("quality_classification1")
    private Integer qualityClassification1;

    /**
     * 质量部分类2
     */
    @TableField("quality_classification2")
    private Integer qualityClassification2;
    /**
     * 质量部分类3
     */
    @TableField("quality_classification3")
    private String qualityClassification3;
    /**
     * 质量部分类4
     */
    @TableField("quality_classification4")
    private Integer qualityClassification4;
    /**
     * 故障分类
     */
    @TableField("fault_classification")
    private String faultClassification;
    /**
     * 备注1
     */
    @TableField("remark1")
    private String remark1;
    /**
     * 备注2
     */
    @TableField("remark2")
    private String remark2;
    /**
     * 备注3
     */
    @TableField("remark3")
    private String remark3;
    /**
     * 备注4
     */
    @TableField("remark4")
    private String remark4;
    /**
     * CCM跟进内容是否全网发布 1 公开 0 不公开
     */
    @TableField("ccm_not_publish")
    private Boolean ccmNotPublish;

    /**
     * 跟进内容
     */
    @TableField("follow_content")
    private String followContent;

    /**
     * CCM主题
     */
    @TableField("ccm_subject")
    private String ccmSubject;

    /**
     * 最新状态
     */
    @TableField("status")
    private String status;

    /**
     * 防止再发建议
     */
    @TableField("advise")
    private Integer advise;

    /**
     * CCM部位 多选用逗号分隔
     */
    @TableField("ccm_part")
    private String ccmPart;

    /**
     * CCM细分部位 多选用逗号分隔
     */
    @TableField("ccm_subdivision_part")
    private String ccmSubdivisionPart;

    /**
     * CCM主要原因 多选用逗号分隔
     */
    @TableField("cc_main_reason")
    private String ccMainReason;

    /**
     * CC解决结果 多选用逗号分隔
     */
    @TableField("cc_result")
    private String ccResult;

    /**
     * 关键字
     */
    @TableField("keyword")
    private String keyword;
    /**
     * 分类1
     */
    @TableField("classification1")
    private Integer classification1;

    /**
     * 分类2
     */
    @TableField("classification2")
    private String classification2;

    /**
     * 分类3
     */
    @TableField("classification3")
    private String classification3;

    /**
     * 分类4
     */
    @TableField("classification4")
    private String classification4;

    /**
     * 分类5
     */
    @TableField("classification5")
    private String classification5;

    /**
     * 分类6
     */
    @TableField("classification6")
    private String classification6;

    /**
     * 下次跟进时间
     */
    @TableField("plan_follow_time")
    private Date planFollowTime;
    /**
     * CCM负责人ID
     */
    private String ccmManId;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date closeCaseTime2;
    private Date firstCloseCaseTime2;
    private Date firstRestartCloseCaseTime2;
    private Date callTime2;
    private  String[] closeCaseTime1;
    private Date actuallFollowTime;
    private Date actuallFollowTime1;
    private Date planFollowTime1;
    private Date planFollowTime2;
    private  String classification11;
    private  String classification21;
    private  String classification31;
    private  String smallClass;
    private  String sql;
    private  String sql1;
    private  String buyDealName;
    private  String buyBloc;
    private  String buyRegionManager;
    private  String handleDealName;
    private  String handleBloc;
    private  String handleRegionManager;
    private  String handleRegion;
    private  String bigClassName;
    private  String smallClassNameOther;
    private List model1;
    private  List bloc1;
    private  List dealerCode1;
    private  List ccmManId1;
    private  List region1;
    private  List regionManager1;
    private  String assitdep;
    private List followStatus1;
    private  List ccMainReason1;
    private  List ccResult1;
    private  List ccmPart1;
    private  List ccmSubdivisionPart1;
    private  List category11;
    private  List category21;
    private  List category31;
    private  List classification12;
    private  List classification22;
    private  List smallClass1;
    private String lastFollowTime;
    private  String isFiveCloseCase;
    private  String is24HourReply;
    private  String is48HourReply;
    private  Integer invalidCaseHidden;
    /**
     * 集团Id
     */
    private String blocId;
    private  String assisDepartment;

    /**
     * 客户是否满意
     */
    private  Integer isSatisfied;

    private  String isValid1;

    /**
     * 工单性质
     */
    private  Integer workOrderNature;

    /**
     * 工单分类
     */
    private  Integer workOrderClassification;

    /**
     * 回复联系人姓名
     * @return
     */
    private  String replyName;

    /**
     * 服务承诺
     * @return
     */
    private  String serviceCommitment;

    private String followName;

    private  Date followTime;

    private List workOrderStatus1;
    private List importanceLevel1;

    private  String  workOrderStatusData;

    private  String importanceLevelData;

    /**
     * 是否有舆情风险
     */

    private  Integer isOpinion;
    /**
     * 结案备注
     */
    private  String closeCaseRemark;
    /**
     *区域经理是否同意结案
     */
    private Integer isAgreeRegion;

    /**
     * 区域经理意见
     */
    private  String regionComments;

    /**
     *区域经理审核时间
     */
    private  Date regionAuditTime;

    /**
     *总部是否同意结案
     */
    private Integer isAgreeHeadquarters;

    /**
     * 总部意见
     */
    private  String headquartersComments;

    /**
     * CCM系统协助处理
     */
    private  String ccmSystemAssistedProcessing;

    /**
     * 提交重启时间
     */
    private  Date restartReplyTime;

    /**
     * 案件重启后是否回访
     */
    private Integer isRestartRevisit;

    /**
     * 案件首次结案时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date firstCloseCaseTime;

    /**
     * 首次重启结案时间
     */
    private Date firstRestartCloseCaseTime;

    /**
     * 区域经理意见
     */
    private String regionalManagerComments;

    /**
     *是否匿名
     */
    private Integer isAnonymous;

    /**
     *区域经理是否满意结案（低满意度）
     */
    private Integer regionSatisfiedCase;

    /**
     *总部是否满意结案（低满意度）
     */
    private Integer HQSatisfiedCase;


    @ApiModelProperty(value = "风险分类(SCRT1001 售前-一般案件、SCRT1002 售前-重要案件 SCRT1003 售前-预警案件 、SCRT1004 售前-重要案件-媒体风险)")
    private String riskType;

    @ApiModelProperty(value = "客诉案件创建时间 yyyy-MM-dd HH:mm:ss")
    private String caseCreateTime;

    @ApiModelProperty(value = "厂端描述")
    private String plantDescription;

    public String getPlantDescription() {
        return plantDescription;
    }

    public void setPlantDescription(String plantDescription) {
        this.plantDescription = plantDescription;
    }

    public Integer getInvalidCaseHidden() {
        return invalidCaseHidden;
    }

    public void setInvalidCaseHidden(Integer invalidCaseHidden) {
        this.invalidCaseHidden = invalidCaseHidden;
    }

    public Integer getRegionSatisfiedCase() {
        return regionSatisfiedCase;
    }

    public Integer getHQSatisfiedCase() {
        return HQSatisfiedCase;
    }

    public void setHQSatisfiedCase(Integer HQSatisfiedCase) {
        this.HQSatisfiedCase = HQSatisfiedCase;
    }

    public void setRegionSatisfiedCase(Integer regionSatisfiedCase) {
        this.regionSatisfiedCase = regionSatisfiedCase;
    }

    public Integer getIsAnonymous() {
        return isAnonymous;
    }

    public void setIsAnonymous(Integer isAnonymous) {
        this.isAnonymous = isAnonymous;
    }

    public String getLastFollowTime() {
        return lastFollowTime;
    }

    public void setLastFollowTime(String lastFollowTime) {
        this.lastFollowTime = lastFollowTime;
    }

    public String getIsFiveCloseCase() {
        return isFiveCloseCase;
    }

    public void setIsFiveCloseCase(String isFiveCloseCase) {
        this.isFiveCloseCase = isFiveCloseCase;
    }

    public String getIs24HourReply() {
        return is24HourReply;
    }

    public void setIs24HourReply(String is24HourReply) {
        this.is24HourReply = is24HourReply;
    }

    public String getIs48HourReply() {
        return is48HourReply;
    }

    public void setIs48HourReply(String is48HourReply) {
        this.is48HourReply = is48HourReply;
    }

    public String getRegionalManagerComments() {
        return regionalManagerComments;
    }

    public void setRegionalManagerComments(String regionalManagerComments) {
        this.regionalManagerComments = regionalManagerComments;
    }

    public Integer getIsRestartRevisit() {
        return isRestartRevisit;
    }

    public void setIsRestartRevisit(Integer isRestartRevisit) {
        this.isRestartRevisit = isRestartRevisit;
    }

    public Date getFirstCloseCaseTime() {
        return firstCloseCaseTime;
    }

    public void setFirstCloseCaseTime(Date firstCloseCaseTime) {
        this.firstCloseCaseTime = firstCloseCaseTime;
    }

    public String getBuyDealerCode() {
        return buyDealerCode;
    }

    public void setBuyDealerCode(String buyDealerCode) {
        this.buyDealerCode = buyDealerCode;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public Date getRestartReplyTime() {
        return restartReplyTime;
    }

    public Date getCloseCaseTime2() {
        return closeCaseTime2;
    }

    public void setCloseCaseTime2(Date closeCaseTime2) {
        this.closeCaseTime2 = closeCaseTime2;
    }

    public String[] getCloseCaseTime1() {
        return closeCaseTime1;
    }

    public void setCloseCaseTime1(String[] closeCaseTime1) {
        this.closeCaseTime1 = closeCaseTime1;
    }

    public void setRestartReplyTime(Date restartReplyTime) {
        this.restartReplyTime = restartReplyTime;
    }

    public String getCcmSystemAssistedProcessing() {
        return ccmSystemAssistedProcessing;
    }

    public void setCcmSystemAssistedProcessing(String ccmSystemAssistedProcessing) {
        this.ccmSystemAssistedProcessing = ccmSystemAssistedProcessing;
    }

    public Date getRegionAuditTime() {
        return regionAuditTime;
    }

    public void setRegionAuditTime(Date regionAuditTime) {
        this.regionAuditTime = regionAuditTime;
    }

    public Integer getIsAgreeHeadquarters() {
        return isAgreeHeadquarters;
    }

    public void setIsAgreeHeadquarters(Integer isAgreeHeadquarters) {
        this.isAgreeHeadquarters = isAgreeHeadquarters;
    }

    public String getHeadquartersComments() {
        return headquartersComments;
    }

    public void setHeadquartersComments(String headquartersComments) {
        this.headquartersComments = headquartersComments;
    }

    public Integer getIsAgreeRegion() {
        return isAgreeRegion;
    }

    public void setIsAgreeRegion(Integer isAgreeRegion) {
        this.isAgreeRegion = isAgreeRegion;
    }

    public String getRegionComments() {
        return regionComments;
    }

    public void setRegionComments(String regionComments) {
        this.regionComments = regionComments;
    }

    public Integer getIsOpinion() {
        return isOpinion;
    }

    public void setIsOpinion(Integer isOpinion) {
        this.isOpinion = isOpinion;
    }

    public String getCloseCaseRemark() {
        return closeCaseRemark;
    }

    public void setCloseCaseRemark(String closeCaseRemark) {
        this.closeCaseRemark = closeCaseRemark;
    }

    public String getWorkOrderStatusData() {
        return workOrderStatusData;
    }

    public void setWorkOrderStatusData(String workOrderStatusData) {
        this.workOrderStatusData = workOrderStatusData;
    }

    public String getImportanceLevelData() {
        return importanceLevelData;
    }

    public void setImportanceLevelData(String importanceLevelData) {
        this.importanceLevelData = importanceLevelData;
    }

    public List getWorkOrderStatus1() {
        return workOrderStatus1;
    }

    public void setWorkOrderStatus1(List workOrderStatus1) {
        this.workOrderStatus1 = workOrderStatus1;
    }

    public List getImportanceLevel1() {
        return importanceLevel1;
    }

    public void setImportanceLevel1(List importanceLevel1) {
        this.importanceLevel1 = importanceLevel1;
    }

    public Integer getSaleType() {
        return saleType;
    }

    public void setSaleType(Integer saleType) {
        this.saleType = saleType;
    }

    public Date getFollowTime() {
        return followTime;
    }

    public void setFollowTime(Date followTime) {
        this.followTime = followTime;
    }

    public String getFollowName() {
        return followName;
    }

    public void setFollowName(String followName) {
        this.followName = followName;
    }

    public Integer getWorkOrderNature() {
        return workOrderNature;
    }

    public void setWorkOrderNature(Integer workOrderNature) {
        this.workOrderNature = workOrderNature;
    }

    public Integer getWorkOrderClassification() {
        return workOrderClassification;
    }

    public void setWorkOrderClassification(Integer workOrderClassification) {
        this.workOrderClassification = workOrderClassification;
    }

    public String getReplyName() {
        return replyName;
    }

    public void setReplyName(String replyName) {
        this.replyName = replyName;
    }

    public String getServiceCommitment() {
        return serviceCommitment;
    }

    public void setServiceCommitment(String serviceCommitment) {
        this.serviceCommitment = serviceCommitment;
    }

    public String getIsValid1() {
        return isValid1;
    }

    public void setIsValid1(String isValid1) {
        this.isValid1 = isValid1;
    }

    private  String role;

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public Integer getIsSatisfied() {
        return isSatisfied;
    }

    public void setIsSatisfied(Integer isSatisfied) {
        this.isSatisfied = isSatisfied;
    }

    public String getAssisDepartment() {
        return assisDepartment;
    }

    public void setAssisDepartment(String assisDepartment) {
        this.assisDepartment = assisDepartment;
    }

    public String getBlocId() {
        return blocId;
    }

    public void setBlocId(String blocId) {
        this.blocId = blocId;
    }

    public List getFollowStatus1() {
        return followStatus1;
    }

    public void setFollowStatus1(List followStatus1) {
        this.followStatus1 = followStatus1;
    }

    public List getCcMainReason1() {
        return ccMainReason1;
    }

    public void setCcMainReason1(List ccMainReason1) {
        this.ccMainReason1 = ccMainReason1;
    }

    public List getCcResult1() {
        return ccResult1;
    }

    public void setCcResult1(List ccResult1) {
        this.ccResult1 = ccResult1;
    }

    public List getCcmPart1() {
        return ccmPart1;
    }

    public void setCcmPart1(List ccmPart1) {
        this.ccmPart1 = ccmPart1;
    }

    public List getCcmSubdivisionPart1() {
        return ccmSubdivisionPart1;
    }

    public void setCcmSubdivisionPart1(List ccmSubdivisionPart1) {
        this.ccmSubdivisionPart1 = ccmSubdivisionPart1;
    }

    public List getCategory11() {
        return category11;
    }

    public void setCategory11(List category11) {
        this.category11 = category11;
    }

    public List getCategory21() {
        return category21;
    }

    public void setCategory21(List category21) {
        this.category21 = category21;
    }

    public List getCategory31() {
        return category31;
    }

    public void setCategory31(List category31) {
        this.category31 = category31;
    }

    public List getClassification12() {
        return classification12;
    }

    public void setClassification12(List classification12) {
        this.classification12 = classification12;
    }

    public List getClassification22() {
        return classification22;
    }

    public void setClassification22(List classification22) {
        this.classification22 = classification22;
    }

    public List getSmallClass1() {
        return smallClass1;
    }

    public void setSmallClass1(List smallClass1) {
        this.smallClass1 = smallClass1;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionManagerId() {
        return regionManagerId;
    }

    public void setRegionManagerId(String regionManagerId) {
        this.regionManagerId = regionManagerId;
    }

    public String getClassification31() {
        return classification31;
    }

    public void setClassification31(String classification31) {
        this.classification31 = classification31;
    }

    public String getSql1() {
        return sql1;
    }

    public String getCcmManId() {
        return ccmManId;
    }

    public void setCcmManId(String ccmManId) {
        this.ccmManId = ccmManId;
    }

    public void setSql1(String sql1) {
        this.sql1 = sql1;
    }

    public String getAssitdep() {
        return assitdep;
    }

    public void setAssitdep(String assitdep) {
        this.assitdep = assitdep;
    }

    public Boolean getDealerIsRead() {
        return dealerIsRead;
    }

    public void setDealerIsRead(Boolean dealerIsRead) {
        this.dealerIsRead = dealerIsRead;
    }

    public Boolean getManagerIsRead() {
        return managerIsRead;
    }

    public void setManagerIsRead(Boolean managerIsRead) {
        this.managerIsRead = managerIsRead;
    }

    public Boolean getCcmIsRead() {
        return ccmIsRead;
    }

    public void setCcmIsRead(Boolean ccmIsRead) {
        this.ccmIsRead = ccmIsRead;
    }

    public Boolean getDeleted() {
        return isDeleted;
    }

    public void setDeleted(Boolean deleted) {
        isDeleted = deleted;
    }

    public Boolean getCcmNotPublish() {
        return ccmNotPublish;
    }

    public void setCcmNotPublish(Boolean ccmNotPublish) {
        this.ccmNotPublish = ccmNotPublish;
    }

    public String getFollowContent() {
        return followContent;
    }

    public void setFollowContent(String followContent) {
        this.followContent = followContent;
    }

    public String getCcmSubject() {
        return ccmSubject;
    }

    public void setCcmSubject(String ccmSubject) {
        this.ccmSubject = ccmSubject;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getAdvise() {
        return advise;
    }

    public void setAdvise(Integer advise) {
        this.advise = advise;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getClassification5() {
        return classification5;
    }

    public void setClassification5(String classification5) {
        this.classification5 = classification5;
    }

    public String getClassification6() {
        return classification6;
    }

    public void setClassification6(String classification6) {
        this.classification6 = classification6;
    }

    public Date getPlanFollowTime() {
        return planFollowTime;
    }

    public void setPlanFollowTime(Date planFollowTime) {
        this.planFollowTime = planFollowTime;
    }

    public Integer getClassification1() {
        return classification1;
    }

    public void setClassification1(Integer classification1) {
        this.classification1 = classification1;
    }

    public String getClassification2() {
        return classification2;
    }

    public void setClassification2(String classification2) {
        this.classification2 = classification2;
    }

    public String getClassification3() {
        return classification3;
    }

    public void setClassification3(String classification3) {
        this.classification3 = classification3;
    }

    public String getClassification4() {
        return classification4;
    }

    public void setClassification4(String classification4) {
        this.classification4 = classification4;
    }

    public List getBloc1() {
        return bloc1;
    }

    public void setBloc1(List bloc1) {
        this.bloc1 = bloc1;
    }

    public List getDealerCode1() {
        return dealerCode1;
    }

    public void setDealerCode1(List dealerCode1) {
        this.dealerCode1 = dealerCode1;
    }

    public List getCcmManId1() {
        return ccmManId1;
    }

    public void setCcmManId1(List ccmManId1) {
        this.ccmManId1 = ccmManId1;
    }

    public List getRegion1() {
        return region1;
    }

    public void setRegion1(List region1) {
        this.region1 = region1;
    }

    public List getRegionManager1() {
        return regionManager1;
    }

    public void setRegionManager1(List regionManager1) {
        this.regionManager1 = regionManager1;
    }

    public Integer getQualityClassification1() {
        return qualityClassification1;
    }

    public void setQualityClassification1(Integer qualityClassification1) {
        this.qualityClassification1 = qualityClassification1;
    }

    public Integer getQualityClassification2() {
        return qualityClassification2;
    }

    public void setQualityClassification2(Integer qualityClassification2) {
        this.qualityClassification2 = qualityClassification2;
    }

    public String getQualityClassification3() {
        return qualityClassification3;
    }

    public void setQualityClassification3(String qualityClassification3) {
        this.qualityClassification3 = qualityClassification3;
    }

    public Integer getQualityClassification4() {
        return qualityClassification4;
    }

    public void setQualityClassification4(Integer qualityClassification4) {
        this.qualityClassification4 = qualityClassification4;
    }

    public String getFaultClassification() {
        return faultClassification;
    }

    public void setFaultClassification(String faultClassification) {
        this.faultClassification = faultClassification;
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1;
    }

    public String getRemark2() {
        return remark2;
    }

    public void setRemark2(String remark2) {
        this.remark2 = remark2;
    }

    public String getRemark3() {
        return remark3;
    }

    public void setRemark3(String remark3) {
        this.remark3 = remark3;
    }

    public String getRemark4() {
        return remark4;
    }

    public void setRemark4(String remark4) {
        this.remark4 = remark4;
    }

    public List getModel1() {
        return model1;
    }

    public void setModel1(List model1) {
        this.model1 = model1;
    }

    public String getBigClassName() {
        return bigClassName;
    }

    public void setBigClassName(String bigClassName) {
        this.bigClassName = bigClassName;
    }

    public String getSmallClassNameOther() {
        return smallClassNameOther;
    }

    public void setSmallClassNameOther(String smallClassNameOther) {
        this.smallClassNameOther = smallClassNameOther;
    }

    public Integer getWorkOrderStatus() {
        return workOrderStatus;
    }

    public void setWorkOrderStatus(Integer workOrderStatus) {
        this.workOrderStatus = workOrderStatus;
    }

    public Integer getIsCloseCase() {
        return isCloseCase;
    }

    public void setIsCloseCase(Integer isCloseCase) {
        this.isCloseCase = isCloseCase;
    }

    public String getBuyDealName() {
        return buyDealName;
    }

    public void setBuyDealName(String buyDealName) {
        this.buyDealName = buyDealName;
    }

    public String getBuyBloc() {
        return buyBloc;
    }

    public void setBuyBloc(String buyBloc) {
        this.buyBloc = buyBloc;
    }

    public String getBuyRegionManager() {
        return buyRegionManager;
    }

    public void setBuyRegionManager(String buyRegionManager) {
        this.buyRegionManager = buyRegionManager;
    }

    public String getHandleDealName() {
        return handleDealName;
    }

    public void setHandleDealName(String handleDealName) {
        this.handleDealName = handleDealName;
    }

    public String getHandleBloc() {
        return handleBloc;
    }

    public void setHandleBloc(String handleBloc) {
        this.handleBloc = handleBloc;
    }

    public String getHandleRegionManager() {
        return handleRegionManager;
    }

    public void setHandleRegionManager(String handleRegionManager) {
        this.handleRegionManager = handleRegionManager;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public String getSmallClass() {
        return smallClass;
    }

    public void setSmallClass(String smallClass) {
        this.smallClass = smallClass;
    }

    public String getClassification11() {
        return classification11;
    }

    public void setClassification11(String classification11) {
        this.classification11 = classification11;
    }

    public String getClassification21() {
        return classification21;
    }

    public void setClassification21(String classification21) {
        this.classification21 = classification21;
    }

    public String getCcResult() {
        return ccResult;
    }

    public void setCcResult(String ccResult) {
        this.ccResult = ccResult;
    }

    public String getCcmSubdivisionPart() {
        return ccmSubdivisionPart;
    }

    public void setCcmSubdivisionPart(String ccmSubdivisionPart) {
        this.ccmSubdivisionPart = ccmSubdivisionPart;
    }

    public String getCcmPart() {
        return ccmPart;
    }

    public void setCcmPart(String ccmPart) {
        this.ccmPart = ccmPart;
    }

    public String getCcMainReason() {
        return ccMainReason;
    }

    public void setCcMainReason(String ccMainReason) {
        this.ccMainReason = ccMainReason;
    }

    public Date getPlanFollowTime1() {
        return planFollowTime1;
    }

    public void setPlanFollowTime1(Date planFollowTime1) {
        this.planFollowTime1 = planFollowTime1;
    }

    public Date getPlanFollowTime2() {
        return planFollowTime2;
    }

    public void setPlanFollowTime2(Date planFollowTime2) {
        this.planFollowTime2 = planFollowTime2;
    }

    public Date getFisrtRestartTime2() {
        return fisrtRestartTime2;
    }

    public void setFisrtRestartTime2(Date fisrtRestartTime2) {
        this.fisrtRestartTime2 = fisrtRestartTime2;
    }

    public Date getActuallFollowTime1() {
        return actuallFollowTime1;
    }

    public void setActuallFollowTime1(Date actuallFollowTime1) {
        this.actuallFollowTime1 = actuallFollowTime1;
    }

    public Date getActuallFollowTime() {
        return actuallFollowTime;
    }

    public void setActuallFollowTime(Date actuallFollowTime) {
        this.actuallFollowTime = actuallFollowTime;
    }

    public Date getCallTime2() {
        return callTime2;
    }

    public void setCallTime2(Date callTime2) {
        this.callTime2 = callTime2;
    }


    public String getOwnerAddress() {
        return ownerAddress;
    }

    public void setOwnerAddress(String ownerAddress) {
        this.ownerAddress = ownerAddress;
    }

    public String getOwnerCode(){
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }


    public Integer getOrgId(){
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId(){
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getComplaintId(){
        return complaintId;
    }

    public void setComplaintId(String complaintId) {
        this.complaintId = complaintId;
    }

    public String getType(){
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getReport() {
        return isReport;
    }

    public void setReport(Boolean report) {
        isReport = report;
    }

    public Integer getClassification(){
        return classification;
    }

    public void setClassification(Integer classification) {
        this.classification = classification;
    }

    public String getSource(){
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Date getCallTime(){
        return callTime;
    }

    public void setCallTime(Date callTime) {
        this.callTime = callTime;
    }

    public Date getFisrtRestartTime(){
        return fisrtRestartTime;
    }

    public void setFisrtRestartTime(Date fisrtRestartTime) {
        this.fisrtRestartTime = fisrtRestartTime;
    }

    public Date getNewestRestartTime(){
        return newestRestartTime;
    }

    public void setNewestRestartTime(Date newestRestartTime) {
        this.newestRestartTime = newestRestartTime;
    }

    public String getCallName(){
        return callName;
    }

    public void setCallName(String callName) {
        this.callName = callName;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getCallTel(){
        return callTel;
    }

    public void setCallTel(String callTel) {
        this.callTel = callTel;
    }

    public String getName(){
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLicensePlateNum(){
        return licensePlateNum;
    }

    public void setLicensePlateNum(String licensePlateNum) {
        this.licensePlateNum = licensePlateNum;
    }

    public String getVin(){
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public Date getBuyTime(){
        return buyTime;
    }

    public void setBuyTime(Date buyTime) {
        this.buyTime = buyTime;
    }

    public String getModel(){
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getModelYear(){
        return modelYear;
    }

    public void setModelYear(String modelYear) {
        this.modelYear = modelYear;
    }

    public String getBuyDealerName(){
        return buyDealerName;
    }

    public void setBuyDealerName(String buyDealerName) {
        this.buyDealerName = buyDealerName;
    }

    public String getDealerName(){
        return dealerName;
    }

    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    public String getDealerCode(){
        return dealerCode;
    }

    public void setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
    }

    public Integer getMileage(){
        return mileage;
    }

    public void setMileage(Integer mileage) {
        this.mileage = mileage;
    }

    public String getReplyTel(){
        return replyTel;
    }

    public void setReplyTel(String replyTel) {
        this.replyTel = replyTel;
    }

    public String getReplyTel2(){
        return replyTel2;
    }

    public void setReplyTel2(String replyTel2) {
        this.replyTel2 = replyTel2;
    }

    public String getPart() {
        return part;
    }

    public void setPart(String part) {
        this.part = part;
    }

    public String getSubdivisionPart() {
        return subdivisionPart;
    }

    public void setSubdivisionPart(String subdivisionPart) {
        this.subdivisionPart = subdivisionPart;
    }

    public String getSubject(){
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getProblem(){
        return problem;
    }

    public void setProblem(String problem) {
        this.problem = problem;
    }

    public String getIllustrate(){
        return illustrate;
    }

    public void setIllustrate(String illustrate) {
        this.illustrate = illustrate;
    }

    public String getCategory1(){
        return category1;
    }

    public void setCategory1(String category1) {
        this.category1 = category1;
    }

    public String getCategory2(){
        return category2;
    }

    public void setCategory2(String category2) {
        this.category2 = category2;
    }

    public String getCategory3(){
        return category3;
    }

    public void setCategory3(String category3) {
        this.category3 = category3;
    }

    public String getCcPart(){
        return ccPart;
    }

    public void setCcPart(String ccPart) {
        this.ccPart = ccPart;
    }

    public String getCcSubdivisionPart(){
        return ccSubdivisionPart;
    }

    public void setCcSubdivisionPart(String ccSubdivisionPart) {
        this.ccSubdivisionPart = ccSubdivisionPart;
    }

    public String getCcProblem(){
        return ccProblem;
    }

    public void setCcProblem(String ccProblem) {
        this.ccProblem = ccProblem;
    }

    public String getCcRequirement(){
        return ccRequirement;
    }

    public void setCcRequirement(String ccRequirement) {
        this.ccRequirement = ccRequirement;
    }

    public String getDepartment(){
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getReceptionist(){
        return receptionist;
    }

    public void setReceptionist(String receptionist) {
        this.receptionist = receptionist;
    }

    public Integer getImportanceLevel(){
        return importanceLevel;
    }

    public void setImportanceLevel(Integer importanceLevel) {
        this.importanceLevel = importanceLevel;
    }

    public Date getDealerFisrtReplyTime(){
        return dealerFisrtReplyTime;
    }

    public void setDealerFisrtReplyTime(Date dealerFisrtReplyTime) {
        this.dealerFisrtReplyTime = dealerFisrtReplyTime;
    }

    public Date getFisrtRestartDealerFisrtReplyTime(){
        return fisrtRestartDealerFisrtReplyTime;
    }

    public void setFisrtRestartDealerFisrtReplyTime(Date fisrtRestartDealerFisrtReplyTime) {
        this.fisrtRestartDealerFisrtReplyTime = fisrtRestartDealerFisrtReplyTime;
    }

    public Integer getIsRevisit(){
        return isRevisit;
    }

    public void setIsRevisit(Integer isRevisit) {
        this.isRevisit = isRevisit;
    }

    public Date getRevisitTime(){
        return revisitTime;
    }

    public void setRevisitTime(Date revisitTime) {
        this.revisitTime = revisitTime;
    }

    public String getRevisitResult(){
        return revisitResult;
    }

    public void setRevisitResult(String revisitResult) {
        this.revisitResult = revisitResult;
    }

    public String getRevisitContent(){
        return revisitContent;
    }

    public void setRevisitContent(String revisitContent) {
        this.revisitContent = revisitContent;
    }

    public Date getApplyTime(){
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public Integer getIsAgree(){
        return isAgree;
    }

    public void setIsAgree(Integer isAgree) {
        this.isAgree = isAgree;
    }

    public Date getSubmitTime(){
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public Date getCloseCaseTime(){
        return closeCaseTime;
    }

    public void setCloseCaseTime(Date closeCaseTime) {
        this.closeCaseTime = closeCaseTime;
    }

    public Date getRestartCloseCaseTime(){
        return restartCloseCaseTime;
    }

    public void setRestartCloseCaseTime(Date restartCloseCaseTime) {
        this.restartCloseCaseTime = restartCloseCaseTime;
    }

    public Integer getCloseCaseStatus(){
        return closeCaseStatus;
    }

    public void setCloseCaseStatus(Integer closeCaseStatus) {
        this.closeCaseStatus = closeCaseStatus;
    }

    public String getFollowStatus(){
        return followStatus;
    }

    public void setFollowStatus(String followStatus) {
        this.followStatus = followStatus;
    }

    public String getBasicReason(){
        return basicReason;
    }

    public void setBasicReason(String basicReason) {
        this.basicReason = basicReason;
    }

    public Integer getIsRepaired(){
        return isRepaired;
    }

    public void setIsRepaired(Integer isRepaired) {
        this.isRepaired = isRepaired;
    }

    public String getTechMaintainPlan(){
        return techMaintainPlan;
    }

    public void setTechMaintainPlan(String techMaintainPlan) {
        this.techMaintainPlan = techMaintainPlan;
    }

    public String getRapportPlan(){
        return rapportPlan;
    }

    public void setRapportPlan(String rapportPlan) {
        this.rapportPlan = rapportPlan;
    }

    public String getRisk(){
        return risk;
    }

    public void setRisk(String risk) {
        this.risk = risk;
    }

    public Boolean getIsDealerIsRead(){
        return dealerIsRead;
    }

    public void setIsDealerIsRead(Boolean dealerIsRead) {
        this.dealerIsRead = dealerIsRead;
    }

    public Boolean getIsManagerIsRead(){
        return managerIsRead;
    }

    public void setIsManagerIsRead(Boolean managerIsRead) {
        this.managerIsRead = managerIsRead;
    }

    public Boolean getIsCcmIsRead(){
        return ccmIsRead;
    }

    public void setIsCcmIsRead(Boolean ccmIsRead) {
        this.ccmIsRead = ccmIsRead;
    }

    public Integer getDataSources(){
        return dataSources;
    }

    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted(){
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid(){
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }


    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getRegionManager() {
        return regionManager;
    }

    public void setRegionManager(String regionManager) {
        this.regionManager = regionManager;
    }

    public String getBloc() {
        return bloc;
    }

    public void setBloc(String bloc) {
        this.bloc = bloc;
    }

    public String getCcmMan() {
        return ccmMan;
    }

    public void setCcmMan(String ccmMan) {
        this.ccmMan = ccmMan;
    }

    public String getHandleRegion() {
        return handleRegion;
    }

    public void setHandleRegion(String handleRegion) {
        this.handleRegion = handleRegion;
    }

    public Date getFirstRestartCloseCaseTime(){ return firstRestartCloseCaseTime; }

    public void setFirstRestartCloseCaseTime(Date firstRestartCloseCaseTime){ this.firstRestartCloseCaseTime = firstRestartCloseCaseTime ;}


    @Override
    public String toString(){
        return"ComplaintInfoPO{" +
                "appId=" + appId +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", complaintId=" + complaintId +
                ", type=" + type +
                ", classification=" + classification +
                ", source=" + source +
                ", callTime=" + callTime +
                ", fisrtRestartTime=" + fisrtRestartTime +
                ", newestRestartTime=" + newestRestartTime +
                ", callName=" + callName +
                ", callTel=" + callTel +
                ", name=" + name +
                ", licensePlateNum=" + licensePlateNum +
                ", vin=" + vin +
                ", buyTime=" + buyTime +
                ", model=" + model +
                ", modelYear=" + modelYear +
                ", buyDealerName=" + buyDealerName +
                ", dealerName=" + dealerName +
                ", dealerCode=" + dealerCode +
                ", mileage=" + mileage +
                ", replyTel=" + replyTel +
                ", replyTel2=" + replyTel2 +
                ", subject=" + subject +
                ", problem=" + problem +
                ", illustrate=" + illustrate +
                ", category1=" + category1 +
                ", category2=" + category2 +
                ", category3=" + category3 +
                ", ccPart=" + ccPart +
                ", ccSubdivisionPart=" + ccSubdivisionPart +
                ", ccProblem=" + ccProblem +
                ", ccRequirement=" + ccRequirement +
                ", department=" + department +
                ", receptionist=" + receptionist +
                ", importanceLevel=" + importanceLevel +
                ", dealerFisrtReplyTime=" + dealerFisrtReplyTime +
                ", fisrtRestartDealerFisrtReplyTime=" + fisrtRestartDealerFisrtReplyTime +
                ", isRevisit=" + isRevisit +
                ", revisitTime=" + revisitTime +
                ", revisitResult=" + revisitResult +
                ", revisitContent=" + revisitContent +
                ", applyTime=" + applyTime +
                ", isAgree=" + isAgree +
                ", submitTime=" + submitTime +
                ", closeCaseTime=" + closeCaseTime +
                ", restartCloseCaseTime=" + restartCloseCaseTime +
                ", closeCaseStatus=" + closeCaseStatus +
                ", followStatus=" + followStatus +
                ", basicReason=" + basicReason +
                ", isRepaired=" + isRepaired +
                ", techMaintainPlan=" + techMaintainPlan +
                ", rapportPlan=" + rapportPlan +
                ", risk=" + risk +
                ", dealerIsRead=" + dealerIsRead +
                ", managerIsRead=" + managerIsRead +
                ", ccmIsRead=" + ccmIsRead +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", firstRestartCloseCaseTime=" + firstRestartCloseCaseTime +
                "}";
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"id");
    }

    public String getInformTime() {
        return informTime;
    }

    public void setInformTime(String informTime) {
        this.informTime = informTime;
    }

    public String getFirstRestartTime() {
        return firstRestartTime;
    }

    public void setFirstRestartTime(String firstRestartTime) {
        this.firstRestartTime = firstRestartTime;
    }

    public Date getFirstCloseCaseTime2() {
        return firstCloseCaseTime2;
    }

    public void setFirstCloseCaseTime2(Date firstCloseCaseTime2) {
        this.firstCloseCaseTime2 = firstCloseCaseTime2;
    }

    public Date getFirstRestartCloseCaseTime2() {
        return firstRestartCloseCaseTime2;
    }

    public void setFirstRestartCloseCaseTime2(Date firstRestartCloseCaseTime2) {
        this.firstRestartCloseCaseTime2 = firstRestartCloseCaseTime2;
    }
}
