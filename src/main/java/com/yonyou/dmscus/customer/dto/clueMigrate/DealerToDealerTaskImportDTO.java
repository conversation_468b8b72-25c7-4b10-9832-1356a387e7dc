package com.yonyou.dmscus.customer.dto.clueMigrate;

import com.yonyou.dmscloud.framework.service.excel.ExcelColumnDefine;
import com.yonyou.dmscloud.function.domains.dto.DataImportDto;
import lombok.Data;

@Data
public class DealerToDealerTaskImportDTO extends DataImportDto implements java.io.Serializable {

    /**
     * 原经销商代码
     */
    @ExcelColumnDefine( value = 1)
    private String sourceOwnerCode;

    /**
     * 经销商代码
     */
    @ExcelColumnDefine( value = 2)
    private String ownerCode;

    /**
     * 是否迁移CDP
     */
    @ExcelColumnDefine(value = 3)
    private String migratedCdpString;
}
