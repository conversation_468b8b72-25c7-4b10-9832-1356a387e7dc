package com.yonyou.dmscus.customer.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
@Data
public class BookingRecordDetailDTO {
private String license;
    private String vin;
    private String tel;
    private String name;
    private Integer oneId;
    private  Boolean selectRecall;
    private  Boolean selectCustomerLose;
    private  Boolean selectRenewal;
    private  Boolean selectVoc;
    private  Boolean selectExtensionInsurance;
    private List<HandRepairProjectParamsVo> orderHandrepair;
    private List<BookingOrderPartParamsVo> orderpart;
    private List<BookingOrderLabourParamsVo> orderlabour;

    /**
     * 邀约线索id
     */
    private Long inviteId;

    /**
     * 跟进内容
     */
    private String content;

    /**
     * 客户反馈
     */
    private String feedback;

    /**
     * 备注
     */
    private String remark;

    /**
     * 跟进状态
     */
    private Integer status;

    /**
     * 失败原因
     */
    private Integer loseReason;
    /**
     * 跟进方式
     */
    private Integer mode;

    /**
     * 下次跟进日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planDate;

    /**
     * 未使用AI原因
     */
    private String noAIreason;
    /**
     * 线索异常
     */
    private Integer errorStatus;

}
