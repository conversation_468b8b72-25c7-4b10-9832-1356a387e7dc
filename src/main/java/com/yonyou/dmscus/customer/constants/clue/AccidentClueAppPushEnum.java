package com.yonyou.dmscus.customer.constants.clue;

import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2023/11/1 11:12
 * @Version 1.0
 */
@Getter
@ToString
public enum AccidentClueAppPushEnum {

    PUSH_TYPE_ALLOT("1001", "【%s】的事故线索分配给了您，请及时跟进。"),
    PUSH_TYPE_TIMEOUT_REMIND("1002", "事故车辆【%s】，报案时间【%s】，已超过上次设置的跟进时间，请及时跟进。"),
    PUSH_TYPE_BEFORE_FOLLOW("1003", "您预计于【%s】跟进车牌号【%s】的事故线索，请及时跟进。"),
    PUSH_TYPE_NOT_COMING("1004", "事故车辆【%s】还未进厂，已超过预约时间【%s】，请及时跟进。");
    private final String type;
    private final String msg;

    AccidentClueAppPushEnum(String type, String msg) {
        this.type = type;
        this.msg = msg;
    }
}
