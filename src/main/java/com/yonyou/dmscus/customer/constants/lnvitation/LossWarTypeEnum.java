package com.yonyou.dmscus.customer.constants.lnvitation;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description 流失类型枚举类
 * <AUTHOR>
 * @date 2023/8/22 14:44
 */
@Getter
@AllArgsConstructor
public enum LossWarTypeEnum {
    NOT_MAINTAINED_18_MONTHS("87902001","3个月流失预警",87902001),
    CUSTOMER_LOST("87902002","保养灯流失预警", 87902002);

    private final String code;
    private final String name;
    private final Integer intCode;
    public static String getCodeByName(String name) {
        for (LossWarTypeEnum clueType : LossWarTypeEnum.values()) {
            if (clueType.getName().equals(name)) {
                return clueType.getCode();
            }
        }
        return null;
    }

    public String getNameByCode(String code) {
        for (LossWarTypeEnum clueType : LossWarTypeEnum.values()) {
            if (clueType.getCode().equals(code)) {
                return clueType.getName();
            }
        }
        return null;
    }

    public static boolean containsCode(String code) {
        for (LossWarTypeEnum inviteType : LossWarTypeEnum.values()) {
            if (inviteType.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
