package com.yonyou.dmscus.customer.constants.lnvitation;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description 返厂模型枚举类
 * <AUTHOR>
 * @date 2024/4/7 10:44
 */
@Getter
@AllArgsConstructor
public enum IntentionLevelEnum {
    THREE_STAR("3", "三星", 3),
    FOUR_STAR("4", "四星", 4),
    FIVE_STAR("5", "五星", 5);
    private final String code;
    private final String name;
    private final Integer intCode;

    public static String getCodeByName(String name) {
        for (IntentionLevelEnum verifyType : IntentionLevelEnum.values()) {
            if (verifyType.getName().equals(name)) {
                return verifyType.getCode();
            }
        }
        return null;
    }

    public String getNameByCode(String code) {
        for (IntentionLevelEnum verifyType : IntentionLevelEnum.values()) {
            if (verifyType.getCode().equals(code)) {
                return verifyType.getName();
            }
        }
        return null;
    }
}
