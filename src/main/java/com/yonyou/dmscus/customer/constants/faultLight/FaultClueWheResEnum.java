package com.yonyou.dmscus.customer.constants.faultLight;

import lombok.Getter;

@Getter
public enum FaultClueWheResEnum {

    WHE_RES_ZERO(0, ""),

    WHE_RES_ONE(1, "无责"),//否

    WHE_RES_TWO(2, "有责"),//是

    WHE_RES_THREE(3, "待确认");

    /**
     * code
     */
    private final Integer code;
    /**
     * msg
     */
    private final String msg;

    FaultClueWheResEnum(final Integer code, final String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static FaultClueWheResEnum getFaultClueWherEsEnum(Integer code) {

        FaultClueWheResEnum faultClueWherEsEnum = null;
        for (FaultClueWheResEnum faultClueWherEsEnum1 : FaultClueWheResEnum.values()) {
            if (faultClueWherEsEnum1.getCode().equals(code)) {
                faultClueWherEsEnum = faultClueWherEsEnum1;
                break;
            }
        }
        return faultClueWherEsEnum;
    }

    public static String getWherEsMsg(Integer code) {

        FaultClueWheResEnum faultClueWherEsEnum = null;
        for (FaultClueWheResEnum faultClueWherEsEnum1 : FaultClueWheResEnum.values()) {
            if (faultClueWherEsEnum1.getCode().equals(code)) {
                faultClueWherEsEnum = faultClueWherEsEnum1;
                break;
            }
        }
        if(faultClueWherEsEnum == null){
            return null;
        }
        return faultClueWherEsEnum.getMsg();
    }


}
