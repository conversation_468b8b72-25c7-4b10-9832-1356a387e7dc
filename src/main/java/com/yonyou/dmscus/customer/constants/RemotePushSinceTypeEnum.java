package com.yonyou.dmscus.customer.constants;

import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2023/11/21 13:56
 * @Version 1.0
 */
@Getter
@ToString
public enum RemotePushSinceTypeEnum {

    //事故线索信息
    CLUE_INFO(2,1),
    //事故线索未跟进
    CLUE_STATUS_FOLLOW_NOT_START(2,2),
    //事故线索跟进中
    CLUE_STATUS_FOLLOW_UP(2,3),
    //事故线索跟进成功
    CLUE_STATUS_FOLLOW_SUCCESS(2,4),
    //事故线索跟进失败
    CLUE_STATUS_FOLLOW_FAIL(2,5),
    // 事故线索跟进状态未变化
    CLUE_STATUS_FOLLOW_NONE(0, 0),
    // 续保线索 跟进成功
    RENEWAL_INSURANCE_STATUS_SUCCES(4,2),
    // 续保线索 跟进失败
    RENEWAL_INSURANCE_STATUS_FAIL(4,3),
    // 续保线索 继续跟进
    RENEWAL_INSURANCE_STATUS_KEEP(4,4);

    private final Integer sinceType;
    private final Integer subSinceType;

    RemotePushSinceTypeEnum(Integer sinceType, Integer subSinceType) {
        this.sinceType = sinceType;
        this.subSinceType = subSinceType;
    }
}
