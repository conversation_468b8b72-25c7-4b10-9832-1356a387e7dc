package com.yonyou.dmscus.customer.constants.clue;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description 线索类型枚举类
 * <AUTHOR>
 * @date 2023/8/21 15:31
 */
@Getter
@AllArgsConstructor
public enum LeadsTypeEnum {
    POST_SALE("100","故障灯"),
    POST_LiGHT_LEADS("101","保养灯"),
    POST_ZERMATT_LEADS("102","零附件");
    private final String code;
    private final String name;


    public static String getCodeByName(String name) {
        for (LeadsTypeEnum clueType : LeadsTypeEnum.values()) {
            if (clueType.getName().equals(name)) {
                return clueType.getCode();
            }
        }
        return null;
    }

    public String getNameByCode(String code) {
        for (LeadsTypeEnum clueType : LeadsTypeEnum.values()) {
            if (clueType.getCode().equals(code)) {
                return clueType.getName();
            }
        }
        return null;
    }
}
