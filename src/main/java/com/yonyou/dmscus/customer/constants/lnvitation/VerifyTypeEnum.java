package com.yonyou.dmscus.customer.constants.lnvitation;

import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description 保养灯线索验证类型枚举类
 * <AUTHOR>
 * @date 2023/9/5 10:44
 */
@Getter
@AllArgsConstructor
public enum VerifyTypeEnum {
    NOT_STARTED("87911001","未开始", 87911001),
    PENDING_VERIFICATION("87911002","待验证", 87911002),
    VERIFIED("87911003","已验证", 87911003);
    private final String code;
    private final String name;
    private final Integer intCode;

    public static String getCodeByName(String name) {
        for (VerifyTypeEnum verifyType : VerifyTypeEnum.values()) {
            if (verifyType.getName().equals(name)) {
                return verifyType.getCode();
            }
        }
        return null;
    }

    public String getNameByCode(String code) {
        for (VerifyTypeEnum verifyType : VerifyTypeEnum.values()) {
            if (verifyType.getCode().equals(code)) {
                return verifyType.getName();
            }
        }
        return null;
    }
}
