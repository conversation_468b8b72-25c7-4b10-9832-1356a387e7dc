package com.yonyou.dmscus.customer.constants;

/**
 * <AUTHOR>
 * @Date 2023/12/10 15:08
 * @Version 1.0
 */
public class RedisConstants {

    /**
     * base key
     */
    public static final String BASE_KEY = "dmscus-customer:";
    /**
     * 事故线索白名单缓存key
     */
    public static final String KEY_ACCIDENT_CLUE_WHITELIST = BASE_KEY + "accident:clue:whitelist:{0}";

    /**
     * 事故线索白名单缓存过期时间(分钟)
     */
    public static final Long EXPIRE_ACCIDENT_CLUE_WHITELIST = 30L;

    /**
     * 事故线索推送LiteCrm token key
     */
    public static final String KEY_AC_PUSH_LITE_CRM_TOKEN = BASE_KEY + "accident:clue:liteCrm:token";

    /**
     * 事故线索推送LiteCrm token 过期时间(小时)
     */
    public static final Long EXPIRE_AC_PUSH_LITE_CRM_TOKEN = 230L;

    public static final String KEY_AC_PUSH_LITE_CRM_TOKEN_LOCK = "accident:clue:liteCrm:lock:getToken";
}
