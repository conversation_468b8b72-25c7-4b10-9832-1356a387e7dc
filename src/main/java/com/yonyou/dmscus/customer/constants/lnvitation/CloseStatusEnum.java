package com.yonyou.dmscus.customer.constants.lnvitation;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description 关闭状态
 * <AUTHOR>
 * @date 2023/8/21 15:31
 */
@Getter
@AllArgsConstructor
public enum CloseStatusEnum {
    SELF_COMPLETED("82411001", "自店完成", 82411001),
    INCOMPLETE("82411002", "未完成", 82411002),
    OVERDUE_CLOSED("82411003", "逾期关闭", 82411003),
    OTHER_STORE_COMPLETED("82411004", "他店完成", 82411004),
    CLOSED("82411005", "关闭", 82411005),
    LOST_CLOSED("82411006", "流失关闭", 82411006);
    private final String code;
    private final String name;
    private final Integer intCode;

    public static String getCodeByName(String name) {
        for (CloseStatusEnum clueType : CloseStatusEnum.values()) {
            if (clueType.getName().equals(name)) {
                return clueType.getCode();
            }
        }
        return null;
    }

    public String getNameByCode(String code) {
        for (CloseStatusEnum clueType : CloseStatusEnum.values()) {
            if (clueType.getCode().equals(code)) {
                return clueType.getName();
            }
        }
        return null;
    }

    public static boolean containsCode(String code) {
        for (CloseStatusEnum inviteType : CloseStatusEnum.values()) {
            if (inviteType.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
