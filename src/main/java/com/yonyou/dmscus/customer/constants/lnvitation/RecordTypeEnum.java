package com.yonyou.dmscus.customer.constants.lnvitation;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description 保养灯线索类型枚举类
 * <AUTHOR>
 * @date 2023/8/21 15:31
 */
@Getter
@AllArgsConstructor
public enum RecordTypeEnum {
    POST_SALE("87891001","普通线索", 87891001),
    POST_LiGHT_LEADS("87891002","VOC线索", 87891002),
    POST_ZERMATT_LEADS("87891003","保养灯线索", 87891003);
    private final String code;
    private final String name;
    private final Integer intCode;


    public static String getCodeByName(String name) {
        for (RecordTypeEnum clueType : RecordTypeEnum.values()) {
            if (clueType.getName().equals(name)) {
                return clueType.getCode();
            }
        }
        return null;
    }

    public String getNameByCode(String code) {
        for (RecordTypeEnum clueType : RecordTypeEnum.values()) {
            if (clueType.getCode().equals(code)) {
                return clueType.getName();
            }
        }
        return null;
    }

    public static boolean containsCode(String code) {
        for (RecordTypeEnum inviteType : RecordTypeEnum.values()) {
            if (inviteType.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
