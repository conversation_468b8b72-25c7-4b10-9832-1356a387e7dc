package com.yonyou.dmscus.customer.constants.faultLight;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预约单状态枚举
 */
@Getter
@AllArgsConstructor
public enum BookingOrderStatusEnum {

    DEFAULT_NOT_CONFIRM(80671006, "默认未确认"),

    NOT_ENTER_FACTORY(80671001, "未进厂"),

    DELAY_ENTER_FACTORY(80671002, "延迟进厂"),

    AHEAD_ENTER_FACTORY(80671003, "提前进厂"),

    ON_TIME_ENTER_FACTORY(80671004, "准时进厂"),

    CANCEL_ENTER_FACTORY(80671005, "取消进厂"),

    TIMEOUT_CANCEL(80671012, "超时取消"),

    ;

    private final Integer code;
    private final String name;

}
