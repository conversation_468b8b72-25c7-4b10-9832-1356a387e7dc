package com.yonyou.dmscus.customer.constants;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

@ApiModel("工单性质枚举")
@Getter
public enum WorkOrderNatureEnum {

    COMPLAIN(2, "投诉类"),
    PROVIDE_ASSISTANCE(500, "协助类"),
    LOW_SATISFACTION(600, "低满意度");

    /**
     * 工单性质编码
     */
    @ApiModelProperty("工单性质编码")
    private Integer code;

    /**
     * 工单性质描述
     */
    @ApiModelProperty("工单性质描述")
    private String desc;

    WorkOrderNatureEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 获取工单性质描述
     * @param code
     * @return
     */
    public static String getWorkOrderNatureDesc(Integer code) {

        for (WorkOrderNatureEnum e : WorkOrderNatureEnum.values()) {
            if (e.getCode() == code) {
                return e.getDesc();
            }
        }

        return "";
    }
}
