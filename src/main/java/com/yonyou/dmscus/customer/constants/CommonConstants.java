package com.yonyou.dmscus.customer.constants;



/**
 * 常量类
 */
public class CommonConstants {
	private CommonConstants() {
		throw new IllegalStateException("Utility class");
	}
	/**
	 * 有效状态
	 */
	public static final String DICT_VALIDS_TYPE = "1001"; // 有效状态类别
	public static final int DICT_VALIDS_VALID = 10011001; // 有效
	public static final int DICT_VALIDS_INVALID = 10011002; // 无效
	/**
	 * 亲善单状态
	 */
	public static final int GOODWILL_STATUS_SAVED = 82551001; // 已保存
	public static final int GOODWILL_STATUS_APPLY_AUDIT = 82551002; // 预申请待审核
	public static final int GOODWILL_STATUS_APPLY_RETURN = 82551003; // 预申请驳回
	public static final int GOODWILL_STATUS_MATERIAL_UPLOAD = 82551004; // 材料待上传
	public static final int GOODWILL_STATUS_MATERIAL_AUDIT = 82551005; // 材料待审核
	public static final int GOODWILL_STATUS_MATERIAL_RETURN = 82551006; // 材料审核驳回
	public static final int GOODWILL_STATUS_NOTICE_INVOICE = 82551007; // 待通知开票/充值
	public static final int GOODWILL_STATUS_ENTRY_INVOICE = 82551008; // 待录入发票信息
	public static final int GOODWILL_STATUS_CONFIRM_INVOICE = 82551009; // 待确认发票
	public static final int GOODWILL_STATUS_PAYMENTING = 82551010; // 已进入付款流程
	public static final int GOODWILL_STATUS_TOPUP = 82551011; // 已充值
	public static final int GOODWILL_STATUS_ENTRY_OR_TOPUP = 82551012; // 待录入发票信息/已充值
	public static final int GOODWILL_STATUS_CONFIRM_OR_TOPUP = 82551013; // 待确认发票/已充值
	public static final int GOODWILL_STATUS_SPAYMENTING_OR_TOPUP = 82551014; // 已进入付款流程/已充值
	public static final int GOODWILL_STATUS_REFUSE_SUPPORT = 82551015; // 拒绝支持
	public static final int GOODWILL_STATUS_NO_NEED_SUPPORT = 82551016; // 无需支持
	public static final int DATASOURCES_VCDC = 10051002;
	public static final int DATASOURCES_DEALER = 10051001;

	/**
	 * 是否状态
	 */
	public static final int DICT_IS_YES = 10041001; // 是
	public static final int DICT_IS_NO = 10041002; // 否

	/**
	 * 定保
	 */
	public static final Integer INVITE_TYPE_FIXED_WARRANTY = 82031001;
	/**
	 * 首保
	 */
	public static final Integer INVITE_TYPE_FIRST_GUARANTEE = 82031002;
	/**
	 * 保险
	 */
	public static final Integer INVITE_TYPE_INSURANCE = 82031003;
	/**
	 * 客户流失
	 */
	public static final Integer INVITE_TYPE_CUS_LOSS = 82031004;
	/**
	 * 召回
	 */
	public static final Integer INVITE_TYPE_CALL_BACK = 82031005;

	/**
	 * 保修
	 */
	public static final Integer INVITE_TYPE_GUARANTEE = 82031006;

	/**
	 * 流失预警
	 */
	public static final Integer INVITE_TYPE_LOSS_ALERT = 82031007;

	/**
	 * 首保
	 */
	public static final Integer INVITE_TYPE_I = 82381001;
	/**
	 * 定保
	 */
	public static final Integer INVITE_TYPE_II = 82381002;
	/**
	 * 续保
	 */
	public static final Integer INVITE_TYPE_III = 82381003;
	/**
	 * VOC事故
	 */
	public static final Integer INVITE_TYPE_IV = 82381004;
	/**
	 * 易损件
	 */
	public static final Integer INVITE_TYPE_V = 82381005;
	/**
	 * 流失客户
	 */
	public static final Integer INVITE_TYPE_VI = 82381006;
	/**
	 * 召回
	 */
	public static final Integer INVITE_TYPE_VII = 82381007;

	/**
	 * 服务活动
	 */
	public static final Integer INVITE_TYPE_VIII = 82381008;

	/**
	 * 保修
	 */
	public static final Integer INVITE_TYPE_IX = 82381009;

	/**
	 * 店端自建
	 */
	public static final Integer INVITE_TYPE_X = 82381010;

	/**
	 * 厂端自建
	 */
	public static final Integer INVITE_TYPE_XI = 82381011;

	/**
	 * 流失预警
	 */
	public static final Integer INVITE_TYPE_XII = 82381012;

	/**
	 * 零附件线索
	 */
	public static final Integer INVITE_TYPE_XIII = 82381013;

	/**
	 * 销售日期间隔（月）
	 */
	public static final Integer INVITE_RULE_I = 82041001;
	/**
	 * 保养里程间隔（Km）
	 */
	public static final Integer INVITE_RULE_II = 82041002;
	/**
	 * 保养时间间隔（月）
	 */
	public static final Integer INVITE_RULE_III = 82041003;
	/**
	 * 保险到期日
	 */
	public static final Integer INVITE_RULE_IV = 82041004;
	/**
	 * 未进厂时间间隔（月）
	 */
	public static final Integer INVITE_RULE_V = 82041005;
	/**
	 * 保修到期日
	 */
	public static final Integer INVITE_RULE_VI = 82041006;

	/**
	 * 下发间隔天数
	 */
	public static final Integer UP_DAY = -90;

	/**
	 * 下发间隔月份
	 */
	public static final Integer UP_MONTH = -1;

	/**
	 * 下发间隔天数
	 */
	public static final Integer UP_LOSS_MONTH = -1;

	/**
	 * 下发间隔天数
	 */
	public static final Integer UP_RECODE_MONTH = -3;

	/**
	 * 与
	 */
	public static final Integer RULE_AND = 81071001;
	/**
	 * 或
	 */
	public static final Integer RULE_OR = 81071002;

	/**
	 * 完成
	 */
	public static final Integer ORDER_STATUS_I = 82411001;

	/**
	 * 未完成
	 */
	public static final Integer ORDER_STATUS_II = 82411002;

	/**
	 * 超时
	 */
	public static final Integer ORDER_STATUS_III = 82411003;
	/**
	 * 他店进厂
	 */
	public static final Integer ORDER_STATUS_IV = 82411004;

	/**
	 * 未跟进
	 */
	public static final Integer FOLLOW_STATUS_I = 82401001;
	/**
	 * 成功跟进
	 */
	public static final Integer FOLLOW_STATUS_II = 82401002;
	/**
	 * 失败跟进
	 */
	public static final Integer FOLLOW_STATUS_III = 82401003;
	/**
	 * 继续跟进
	 */
	public static final Integer FOLLOW_STATUS_IV = 82401004;

	/**
	 * 沃尔沃默认邮箱
	 */
	public static final String VOLVO_MAIL = "<EMAIL>";

	/**
	 * 沃尔沃默认邮箱
	 */
	public static final String REPAIR_TYPE_CODE_ACCIDENT = "I";

	/**
	 * 工单状态
	 */
	public static final String DICT_RO_STATUS_TYPE = "8049"; // 工单状态类别
	public static final String DICT_RO_STATUS_TYPE_ON_REPAIR = "80491001"; // 在修
	public static final String DICT_RO_STATUS_TYPE_FOR_BALANCE = "80491002"; // 已提交结算
	public static final String DICT_RO_STATUS_TYPE_BALANCED = "80491003"; // 已结算

	/**
	 * 线索状态
	 */
	public static final Integer CLUES_STATUS_UNFINISHED = 83511002; // 未完成

	public static final Integer CLUES_STATUS_FINISHED = 83511001; // 已完成

	public static final Integer CLUES_STATUS_INTO = 83511004; // 已进厂

	public static final Integer CLUES_STATUS_TA_INTO = 83511003; // 他店进厂

	public static final Integer CLUES_STATUS_TIMEOUT = 83511005; // 超时


	/**
	 * 跟进状态
	 */
	public static final Integer FOLLOW_STATUS_WGJ=83531001;  //未跟进



	/**
	 * 线索来源
	 */
	public static final Integer CLUES_RESOURCE_BXGSTS= 15211014; //保险公司短信推送

	public static final Integer CLUES_RESOURCE_KHLD=15211015; //客户来电

	public static final Integer CLUES_RESOURCE_WTYW=15211016; //外拓业务

	public static final Integer CLUES_RESOURCE_QT= 15211012; // 其他


	/**
	 * 事故来源
	 */
	public static final Integer ACCIDENT_TYPE_QW  = 83521001;  //轻微
	public static final Integer ACCIDENT_TYPE_YB  = 83521002; //一般
	public static final Integer ACCIDENT_TYPE_JD  = 83521003  ; //较大
	public static final Integer ACCIDENT_TYPE_ZD  = 83521004  ; //重大


	/**
	 * 电信新平台运营商标识
	 */
	public static final Integer NEW_TELECOM_OPERATOR = 10781003;
	public static final String NEW_TELECOM_OPERATOR_ZH = "电信新平台";

	/**
	 * 联通运营商标识
	 */
	public static final Integer UNICOM_OPERATOR = 10781002;
	public static final String UNICOM_OPERATOR_ZH = "联通";
	/**
	 * 工作号被叫
	 */
	public static final String WORK_NUMBER_CALLEE = "gzh-callee";

	public static final String WORK_NUMBER_MENTHODTYPE_INFO = "info";

	public static final String WORK_NUMBER_MENTHODTYPE_BIND = "bind";

	public static final String WORK_NUMBER_MENTHODTYPE_UNBIND = "unbind";

	public static final String WORK_NUMBER_MENTHODTYPE_REGISTER = "register";
	public static final boolean WORK_NUMBER_IS_AXN_YES = true;

	public static final boolean WORK_NUMBER_IS_AXN_NO = false;


	public static final String NEW_TELECOM_TOKEN_KEY = "cti:newtelecom:token:";

	/**
	 * 电信新平台AXB
	 */
	public static final Integer NEW_TELECOM_AXB = 1078100302;

	/**
	 * 数据来源 经销商
	 */
	public static final Integer DATA_SOURCES_DEALER = 10451001;
	/**
	 * 数据来源 车厂
	 */
	public static final Integer DATA_SOURCES_VCDC = 10451002;

	// 每次批量插入最大条数
	public static final Integer INSERT_BATCH_LIMIT_COUNT = 100;

	//邀约线索分配分配类型
	/**
	 * 平均分配
	 */

	public static final Integer SASLLOCATEDLRDTO_RULE_TYPE_82121001 =  82121001;
	/**
	 * 根据上次接待SA分配
	 */

	public static final Integer SASLLOCATEDLRDTO_RULE_TYPE_82121002 =  82121002;
	/**
	 * 以上次接待SA为主,平均分配为辅
	 */
	public static final Integer SASLLOCATEDLRDTO_RULE_TYPE_82121003 =  82121003;

	/**
	 * 邀约线索完成状态线索完成状态(续保)
	 */
	//未完成

	public static final Integer INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681001 =  83681001;
	//已完成
	public static final Integer INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681002 =  83681002;
	//流失客户
	public static final Integer INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681003 =  83681003;
	//他店进厂
	public static final Integer INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681004 =  83681004;
	//关闭
	public static final Integer INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681005 =  83681005;
	//续保关闭
	public static final Integer INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681006 =  83681006;

	//邀约类型
	public static final Integer INVITE_TYPE_82381003 =82381003;
	public static final Integer  TT_INVITE_INSURANCE_VEHICLE_TASK_ITEM_TYPE_10041002 = 10041002;
	public static final Integer  TT_INVITE_INSURANCE_VEHICLE_TASK_ITEM_TYPE_10041001 = 10041001;
	public static final Integer  TT_INVITE_INSURANCE_VEHICLE_TASK_INSURANCE_TYPE_81761002 = 81761002;
	//邀约规则
	/**
	 * 保险到期日
	 */
	public static final Integer INVITE_RULE_82041004 = 82041004;

	//类型：易损件、项目
	public static final Integer 	ITEM_TYPE_10041001 = 10041001;
	public static final Integer 	ITEM_TYPE_10041002 = 10041002;

	public static final String UNDERLINE = "-";

	//标签绑定类型  导入
	public static final int TAG_BINTTYPE_IMPORT = 1;

	//标签绑定类型 计算
	public static final int TAG_BINTTYPE_AGLOR = 2;

	public static final String DEFAULT_APPID = "volvo";

	public static final String TAG_GROUP_VIN = "vin";

	public static final String KEY_CUSTOMERS = "65A";
	public  static  final  String ZERO = "0";

//===================voc=========================//
	public  static  final  String VOC_NORMAL = "Normal";
	public  static  final  String VOC_TIMEEXCEEDED = "TimeExceeded";
	public  static  final  String VOC_ALMOSTTIMEFORSERVICE = "AlmostTimeForService";
	public  static  final  String VOC_TIMEFORSERVICE = "TimeForService";

	public  static  final  int VOC_NORMAL_NUM =83981001;
	public  static  final  int VOC_TIMEEXCEEDED_NUM= 83981002;
	public  static  final  int VOC_ALMOSTTIMEFORSERVICE_NUM = 83981003;
	public  static  final  int VOC_TIMEFORSERVICE_NUM = 83981004;

	public  static  final  String VOC_INSERT = "新增";
	public  static  final  String VOC_UPDATE = "改变";
	public  static  final  String VOC_FADED  = "消失";
	public  static  final  String   LOG_S1 = "查询结果,size{}";
	public  static  final  String   LOG_S2 = "开始创建首保任务{}";
	public  static  final  String   LOG_S3 = "不存在首保日期间隔规则{}";
	public  static  final  String   LOG_S4 = "开票日期{}";
	public  static  final  String   LOG_S5 = "规则间隔月{}";
	public  static  final  String   LOG_S6 = "建议进厂{}";
	public  static  final  String   LOG_S7= "开始创建客户流失任务{}" ;

	public  static  final  String   LOG_S8= "修改流失客户，数据小于200" ;
	public  static  final  String   LOG_S9= "修改流失客户，数据大于200{},{}" ;
	public  static  final  String   LOG_S10= "voc任务下发成功，数据小于200" ;
	public  static  final  String   LOG_S11= "voc任务下发成功，数据大于200{},{}";
	public  static  final  String   LOG_S12= "voc线索下发成功，数据大于200{},{}" ;
	public  static  final  String   LOG_S13= "voc线索下发成功，数据小于200";
	public  static  final  String   LOG_S14= "voc线索多出字段保存VocInviteVehicleTaskRecordPo成功，数据小于200";
	public  static  final  String   LOG_S15= "voc线索多出字段保存VocInviteVehicleTaskRecordPo成功，数据大于200{},{}";
	public  static  final  String   LOG_S16= "修改流失预警，数据小于200";
	public  static  final  String   LOG_S17= "修改流失预警，数据大于200{},{}";
	public  static  final  String   LOG_S18= "voc新增任务返回数据{}";
	public  static  final  String   LOG_S19= "voc新增任务返回数据1{}";
	public  static  final  String   LOG_S20= "查询结果,size{}";
	public  static  final  String   LOG_S21= "查询结果，rule{}";
	public  static  final  String   LOG_S22= "18个月upsr6VOC修改数据{}";
	public  static  final  String   LOG_S23= "18个月upst6VOC修改数据{}";
	public  static  final  String   LOG_S24= "文件内容为空";
	public  static  final  String   LOG_S25= "客户流失初始化开始:{}";

	//线索类型:0 VOC线索,1普通线索
	public  static  final  int  RECORD_TYPE_0= 87891002;
	public  static  final  int  RECORD_TYPE_1= 87891001;
	//线索类型:0 ：非VOC类型,1 ：18个月未保养，2：非4S店保养流失,3:流失客户保养灯亮
	public  static  final  int  LOSS_TYPE_0= 0;
	public  static  final  int  LOSS_TYPE_1= 87901001;
	public  static  final  int  LOSS_TYPE_2= 87901002;
	public  static  final  int  LOSS_TYPE_3= 87901003;

	public  static  final  int  MODTYPE_91111002= 91111002;
	//保养灯白名单
	public  static  final  int  MOD_TYPE_91111011= 91111011;
	//初始化白名单
	public  static  final  int  MOD_TYPE_91111015= 91111015;

	public  static  final  int  MOD_TYPE_91112011= 91112011;

	/**
	 * 企微事故线索类型（白名单）
	 */
	public static final Integer ACCIDENT_LEAD_TYPE = 91111029;
	/**
	 * oneId替换功能百名单
	 */
	public static final Integer ONEID_REPLACEMENT_WHITELIST = 91111036;
	//===================voc=========================//


	public static final Integer SHQYJL_ORGTYPE= 15061008;

	public static final String SHQYJL_ROLECODE= "SHQYJL";

	public static final String XSQYJL_ROLECODE= "XSQYJL";

	/**
	 * 精准邀约
	 */
	public static final Integer PRECISE_INVITA_FUN = 91111003;

	/**
	 * 白名单
	 */
	public static final Integer WHITE_LIST = 0;
	//黑名单
	public static final Integer BLACK_LIST = 1;

	public static final int ONE_HUNDRED_TWENTY = 120;

	public static final int FORTY_EIGHT_HOURS = 48;

	/**故障灯线索使用常量-start-*/
	public static final int NOTIFY_LENGTH = 50;
	/**故障灯线索使用常量-end-*/

	/**在职*/
	public static final Integer ON_THE_JOB = 10081001;
	/**离职*/
	public static final Integer RESIGNATION = 10081002;
    /**主子线索*/
	public static final Integer MAINLINE_CABLE = 1;
    /**线索来源 区分自建,邀约,故事线索*/
	/**VCDC下发邀约*/
	public static final Integer VCDC_INVITATION = 1;
	/**经销商自建邀约*/
	public static final Integer DEALER_INVITATION = 2;
	/**VOC事故邀约*/
	public static final Integer VOC_ACCIDENT_INVITATION = 3;
	/**CRM 下发线索*/
	public static final int CRM_LEAD = 4;
	public static final Integer DATA_SOURCE_YB = 1; // 易保线索：易保来的线索，商业/交强 可以去根据保单号去查询
	public static final Integer DATA_SOURCE_TB = 2; // 投保线索
	public static final Integer DATA_SOURCE_NEW_CAR = 3; // 新车销售：开票时间拼接
	public static final Integer DATA_SOURCE_IMPORT = 4; // 导入线索：手动导入
	public static final Integer DATA_SOURCE_REPAIR_ORDER_24 = 5; // 24个月进厂工单
	public static final Integer DATA_SOURCE_REPAIR_ORDER_SA = 6; // 进厂工单：SA手动维护的续保到期时间

	public static final Integer DATA_SOURCE_NEW_YB = 8; // 易保线索：易保来的线索，商业/交强 可以去根据保单号去查询
	public static final Integer DATA_SOURCE_NEW_TB = 2; // 投保线索
	public static final Integer DATA_SOURCE_NEW_IMPORT = 10; // 导入线索：手动导入
	public static final Integer DATA_SOURCE_NEW_ENTER = 9;
	public static final Integer DATA_SOURCE_NEW_REPAIR_ORDER_24 = 11; // 24个月进厂工单
	public static final Integer DATA_SOURCE_NEW_REPAIR_ORDER_SA = 7; // 进厂工单：SA手动维护的续保到期时间

	//SHQYJL:售后区域经理 , SHDQJL :售后区域高级经理'
	public static final String SHQYJL_ROLE_CODE= "SHQYJL";
	public static final String SHDQJL_ROLE_CODE= "SHDQJL";

	/**不生成*/
	public static final Integer NOT_GENERATING = 2;

	/**
	 * QW IM
	 */
	public static final String TALKSKILL_TYPE_CODE_QWIM = "QWIM"; // 类型 CODE

	public static final String TALKSKILL_TAG1_SURROUND_SAVE_ORDE = "35071001"; // 环检单转工单-工单保存
	public static final String TALKSKILL_TAG1_SURROUND = "35071002"; 	// 环检单-工单保存
	public static final String TALKSKILL_TAG1_ORDER_CONFIRMATION = "35071003"; 	// 工单确认
	public static final String TALKSKILL_TAG1_HEALTH_REPORT_START = "35071004";  // 车辆健康检查报告初检
	public static final String TALKSKILL_TAG1_ADDITIONAL_MAINTENANCE = "35071005";  // 增项维护
	public static final String TALKSKILL_TAG1_HEALTH_REPORT_END = "35071006";  // 车辆健康检查报告终检
	public static final String TALKSKILL_TAG1_EXPENSE_SETTLEMENT = "35071007";  // 费用结算
	public static final String TALKSKILL_TAG1_EDIT_SAVE_ORDE = "35071008";  // 工单保存/编辑保

	/**
	 * 启用状态
	 */
	public static final int TALKSKILL_ENABLE_ENABLE = 10031001; // 启用
	public static final int TALKSKILL_ENABLE_DISABLE = 10031002; // 禁用

	/**
	 * 表示店端
	 */
	public static final Integer DATATYPE_STORE = 10461001;

	/**
	 * 表示厂端
	 */
	public static final Integer DATATYPE_FACTORY = 10461003;

	public static final Integer FOLLOW_STATUS_FINISHED=83531003;  //已完成
	public static final Integer FOLLOW_STATUS_FAIL=83531004;  //跟进失败
	public static final Integer INSURE_TO_B= 15211017; // 保险公司下发
	public static final String SUCCESS ="成功" ;
	public static final String FAIL = "失败";
	public static final int NUM_0 = 0;
	public static final int NUM_1 = 1;
	public static final int NUM_2 = 2;
	public static final int NUM_3 = 3;
	public static final int NUM_4 = 4;
	public static final int NUM_5 = 5;
	public static final int NUM_7 = 7;
	public static final int NUM_31 = 31;
	public static final int NUM_180 = 180;
	public static final Long GENERAL_LEVEL = 33081001L;
	public static final String PUSH_TYPE_ALLOT = "1001";
	public static final String PUSH_TYPE_TIMEOUT_REMIND = "1002";
	public static final String PUSH_TYPE_BEFORE_FOLLOW = "1003";
	public static final String PUSH_TYPE_NOT_COMING = "1004";

	/**故障灯分页size*/
	public static final Integer FAULT_LIGHT_SIZE = 500;

	/**
	 * 维修类型-事故
	 */
	public static final String REPAIR_CATEGORY_ACCIDENT = "I";

	/**
	 * 预约类别-被动预约
	 */
	public static final Integer BOOKING_TYPE_PASSIVELY = 80551002;


	public static final Integer SINCE_TYPE_Y = 1; // 保养灯刷日均
	public static final Integer SINCE_TYPE_G = 2; // 故障灯刷高亮

	/**
	 * 是否BEV线索
	 *
	 */
	public static final Integer BEV_LEAD = 1;
	public static final Integer NOT_BEV_LEAD = 0;


	/**重试次数*/
	public static final Integer FAULT_LIGHT_RETRY_COUNT_TWO = 2;

	/**重试次数*/
	public static final Integer FAULT_LIGHT_RETRY_COUNT_THREE = 3;

	/**故障灯重试最大次数异常*/
	public static final Integer FAULT_LIGHT_TASK_STATUS_MAX = -1;
	/**故障灯重试失败*/
	public static final Integer FAULT_LIGHT_TASK_STATUS_CLOSE = 2;
	public static final int CLUE_FOLLOW_STATUS_UP = 83531002;
	public static final int CLUE_FOLLOW_STATUS_SUCCESS = 83531003;
	public static final int CLUE_FOLLOW_STATUS_FAIL = 83531004;

	public static final Integer SINCE_TYPE_L =3; //返厂意向等级
	//异常
	public static final Integer CDP_TAG_FAILURE =2;
	//
	public static final String REDIS_CPD_KEY ="UPDATE_CDP_INTENTION_LEVEL_TAG";
	/**
	 * 线索任务完成状态
	 */
	public static final Integer CLUE_TASK_TODO=0;
	public static final Integer CLUE_TASK_SUCCESS=1;
	public static final Integer CLUE_TASK_TO_RETRY=2;
	public static final Integer CLUE_TASK_FAILED=-1;

	public static final String COMMON_CONFIG_COMPLETE_LEAD_TIME_FRAME = "completeLeadByMaintenanceOrderTimeFrame";

	/**
	 * 标点符号 逗号
	 */
	public static final String STR_COMMA = ",";

	/**
	 * 分隔符 逗号和点号
	 */
	public static final String STR_COMMA_UP_DOT = "','";

	/**
	 * 标点符号 点号
	 */
	public static final String STR_UP_DOT = "'";

	/**
	 * 空字符串
	 */
	public static final String STR_NULL = "null";

	/**
	 * 返回的成功状态码 0
	 */
	public static final String RETURN_CODE_0 = "0";

	public static final String EM90_MODEL_ID_CONFIG_KEY = "em90-modelId";

	public static final String GOODWILL_APPLY_MODEL_ID_CONFIG_KEY = "goodwillApply-modelId";
	 /**
     * 	事故线索状态：83531001(未跟进)
     */
    public static final int NOT_FOLLOW = 83531001;
    /**
     * 	事故线索状态：83531002(继续跟进)
     */
    public static final int KEEP_ON_FOLLOW = 83531002;
    /**
     * 	事故线索状态：83531003(跟进成功)
     */
    public static final int FOLLOW_SUCCESS = 83531003;
    /**
     * 	事故线索状态：83531004(跟进失败)
     */
    public static final int FOLLOW_FAIL = 83531004;
    /**
     * 	事故线索状态：83531005(超时未跟进)
     */
    public static final int FOLLOW_TIME_OUT = 83531005;
    /**
     * 	事故线索状态：83531006(超时关闭)
     */
    public static final int FOLLOW_TIME_OUT_CLOSE = 83531006;
    /**
     * 	事故线索状态：0 全部
     */
    public static final int ALL_FOLLOW_STATUS = 0;
    /**
     * 排序默认值
     */
    public static final String ORDER_BY_STR = "tt.created_at asc ";

	/**
	 * 是否结案 10041001-是  10041002-否
	 */
	public static final Integer IS_CLOSE_CASE_YES = 10041001;
	public static final Integer IS_CLOSE_CASE_NO = 10041002;

	/**
	 * 返回的状态码200
	 */
	public static final String RESULT_CODE_200 = "200";

	/**
	 * LocalDateTime 的中国时区
	 */
	public static final String LOCALDATETIME_CHINA_ZONE = "Asia/Shanghai";

	public static final String LEADS_TYPE_103 = "103";






	/**
	 * CDP SUCCESS
	 */
	public static final String SUCCESS_CODE = "0";
	public static final String SUCCESS_MSG = "成功";

	/**
	 * cdp 客户数据缓存key前缀
	 */
	public static final String CDP_CUSTOMER_PROFILE_TOKEN = "cdp:customerProfile:token:";
	public static final String CDP_CUSTOMER_OR_VEHICLE_PREFIX = "cdp:customerOrVehicle:";
	public static final String CDP_VALUE_UTF8 = "utf-8";
	public static final String CDP_TOKEN = "cdp:TOKEN-VALUE";
	public static final int CDP_TOKEN_TIME = 235;
	/**
	 * 续保线索跟进状态
	 */
	public static final int RENEWAL_INSURANCE_STATUS_SUCCES = 82401002; //跟进成功
	public static final int RENEWAL_INSURANCE_STATUS_FAIL = 82401003; // 跟进失败
	public static final int RENEWAL_INSURANCE_STATUS_KEEP = 82401004; // 继续跟进

	/**
	 * 续保线索白名单字典
	 */
	public static final Integer RENEWAL_INSURANCE_WHITE = 91111033;

	public static final Integer WECOM_ACCIDENT_ROSTER_TYPE_WHITE = 0;

	public static final String VOC_TOKEN_KEY = "voc:token:";

	public static final String RECALL_VOICE_URL = "recallVoiceUrl";
	public static final String RECALL_VOICE_URL_HIS = "recallVoiceUrl_his";
	public static final String BYTEDANCE = "BYTEDANCE";
	public static final String ALL = "ALL";
	public static final String VOC = "VOC";

}
