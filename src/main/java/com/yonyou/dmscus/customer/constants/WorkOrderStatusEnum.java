package com.yonyou.dmscus.customer.constants;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

@ApiModel("销售客诉工单状态枚举")
@Getter
public enum WorkOrderStatusEnum {

    WAITING_FOR_FOLLOW_UP(82451001, "待跟进"),
    FOLLOWING_UP(82451002, "跟进中"),
    REOPEN(82451003, "重启"),
    SUBMISSION_FOR_CLOSURE(82451004, "提交结案"),
    REPEAL(82451005, "撤销"),
    BECOME_INVALID(82451006, "作废"),
    RESTARTING_REVIEW_IN_PROGRESS(82451007, "重启审核中");

    /**
     * 工单状态code
     */
    @ApiModelProperty("工单状态code")
    private Integer code;
    /**
     * 工单状态描述
     */
    @ApiModelProperty("工单状态描述")
    private String desc;

    WorkOrderStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getWorkOrderStatusDesc(Integer code) {

        if (null == code) {
            return "";
        }

        for (WorkOrderStatusEnum source : WorkOrderStatusEnum.values()) {

            if (String.valueOf(source.getCode()).equals(String.valueOf(code))) {
                return source.getDesc();
            }

        }

        return "";

    }


}
