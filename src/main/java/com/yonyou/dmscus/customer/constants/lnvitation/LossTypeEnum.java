package com.yonyou.dmscus.customer.constants.lnvitation;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description 流失类型枚举类
 * <AUTHOR>
 * @date 2023/8/22 14:44
 */
@Getter
@AllArgsConstructor
public enum LossTypeEnum {
    NOT_MAINTAINED_18_MONTHS("87901001","18个月未保养",87901001),
    CUSTOMER_LOST("87901002","流失客户*", 87901002),

    CUSTOMER_MAIN_LIGHT("87901003","流失客户保养灯亮", 87901003);
    private final String code;
    private final String name;
    private final Integer intCode;
    public static String getCodeByName(String name) {
        for (LossTypeEnum clueType : LossTypeEnum.values()) {
            if (clueType.getName().equals(name)) {
                return clueType.getCode();
            }
        }
        return null;
    }

    public String getNameByCode(String code) {
        for (LossTypeEnum clueType : LossTypeEnum.values()) {
            if (clueType.getCode().equals(code)) {
                return clueType.getName();
            }
        }
        return null;
    }

    public static boolean containsCode(String code) {
        for (LossTypeEnum inviteType : LossTypeEnum.values()) {
            if (inviteType.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
