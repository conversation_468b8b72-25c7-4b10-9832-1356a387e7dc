package com.yonyou.dmscus.customer.constants.faultLight;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**性别枚举类*/
@Getter
@AllArgsConstructor
public enum FaultGenderEnum {

    GENDER_SIR("100500001","先生", "10021001", "80121001"),
    GENDER_MADAM("100500002","女士", "10021002", "80121002"),
    GENDER_UNKNOWN("100500003","未知", "10021003", "80121003");
    private final String code;
    private final String name;

    private final String nbCode;
    private final String noCode;
    public static String getNameByCode(String code){
        for(FaultGenderEnum nodeEnum : FaultGenderEnum.values()){
            if (nodeEnum.code.equals(code)){
                return nodeEnum.name;
            }
        }
        return null;
    }

    public static String getNbCodeByCode(String code){
        for(FaultGenderEnum nodeEnum : FaultGenderEnum.values()){
            if (nodeEnum.code.equals(code)){
                return nodeEnum.nbCode;
            }
        }
        return null;
    }

    public static String getNbCodeByNoCode(String code){
        for(FaultGenderEnum nodeEnum : FaultGenderEnum.values()){
            if (nodeEnum.noCode.equals(code)){
                return nodeEnum.nbCode;
            }
        }
        return null;
    }

    public static String getCodeByName(String name){
        for(FaultGenderEnum nodeEnum : FaultGenderEnum.values()){
            if (StringUtils.equals(nodeEnum.name, name)){
                return nodeEnum.code;
            }
        }
        return null;
    }

    private static Map<String, FaultGenderEnum> map = Arrays.stream(FaultGenderEnum.values()).collect(Collectors.toMap(FaultGenderEnum::getCode, e -> e));
    private static Map<String,String> valueMap = Arrays.stream(FaultGenderEnum.values()).collect(Collectors.toMap(FaultGenderEnum::getCode, e -> e.getName()));

}
