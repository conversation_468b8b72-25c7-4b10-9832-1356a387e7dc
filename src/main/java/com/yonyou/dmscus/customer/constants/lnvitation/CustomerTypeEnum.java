package com.yonyou.dmscus.customer.constants.lnvitation;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description 客户类型枚举类
 * <AUTHOR>
 * @date 2023/9/11 18:23
 */
@Getter
@AllArgsConstructor
public enum CustomerTypeEnum {
    SHOP_CLIENT("1000", "店端客户"),
    REPAIR_SENDER("1001", "送修人");
    private final String code;
    private final String name;
    public static String getCodeByName(String name) {
        for (CustomerTypeEnum clueType : CustomerTypeEnum.values()) {
            if (clueType.getName().equals(name)) {
                return clueType.getCode();
            }
        }
        return null;
    }

    public String getNameByCode(String code) {
        for (CustomerTypeEnum clueType : CustomerTypeEnum.values()) {
            if (clueType.getCode().equals(code)) {
                return clueType.getName();
            }
        }
        return null;
    }

    public static boolean containsCode(String code) {
        for (CustomerTypeEnum inviteType : CustomerTypeEnum.values()) {
            if (inviteType.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
