package com.yonyou.dmscus.customer.constants.faultLight;

import lombok.Getter;

@Getter
public enum StateEscapeEnum {

    STATE_ONE("1","否"),

    STATE_TWO("2","是");

    public final String code;

    public final String msg;

    StateEscapeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getStateMsg(String code) {

        String msg = null;
        for (StateEscapeEnum tradeType : StateEscapeEnum.values()) {
            if (tradeType.getCode().equals(code)) {
                msg = tradeType.msg;
                break;
            }
        }
        return msg;
    }
}
