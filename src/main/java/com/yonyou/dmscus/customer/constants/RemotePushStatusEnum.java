package com.yonyou.dmscus.customer.constants;

import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2023/11/21 14:40
 * @Version 1.0
 */
@Getter
@ToString
public enum RemotePushStatusEnum {

    //待处理
    STATUS_PENDING(0),
    //处理成功
    STATUS_SUCCESS(1),
    //处理失败
    STATUS_FAIL(2),
    //重试最大次数异常
    STATUS_EXCEPTION(-1);

    private final Integer status;

    RemotePushStatusEnum(Integer status) {
        this.status = status;
    }
}
