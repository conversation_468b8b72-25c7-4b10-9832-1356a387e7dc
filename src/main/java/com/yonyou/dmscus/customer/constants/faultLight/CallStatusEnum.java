package com.yonyou.dmscus.customer.constants.faultLight;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**通话结果与拨打状态枚举类*/
@Getter
@AllArgsConstructor
public enum CallStatusEnum {
    /** 拨打状态
     call_status_001	接通
     call_status_002	空号
     call_status_003	无人接听
     call_status_004	停机
     call_status_005	人号不对
     call_status_006	拒访
     call_status_007	下次联系
     call_status_008	无法接通
     call_status_009	关机
     call_status_010	预约回访
     call_status_999	其他
     **/
    /** 通话结果
     final_call_result_001	关机/停机
     final_call_result_002	拒访
     final_call_result_003	空号
     final_call_result_004	想购车不联系
     final_call_result_005	无购车意向
     final_call_result_006	已购车
     final_call_result_007	三次无法接通
     final_call_result_008	三次无人接听
     final_call_result_009	人号不匹配
     final_call_result_010	下次联系
     final_call_result_011	成功访问
     final_call_result_013  客户不需要
     final_call_result_999	其他
     **/
    CALL_STATUS_001("call_status_001","接通"),
    CALL_STATUS_002("call_status_002","空号"),
    CALL_STATUS_003("call_status_003","无人接听"),
    CALL_STATUS_004("call_status_004","停机"),
    CALL_STATUS_005("call_status_005","人号不对"),
    CALL_STATUS_006("call_status_006","拒访"),
    CALL_STATUS_007("call_status_007","下次联系"),
    CALL_STATUS_008("call_status_008","无法接通"),
    CALL_STATUS_009("call_status_009","关机"),
    CALL_STATUS_010("call_status_010","预约回访"),
    CALL_STATUS_999("call_status_999","其他"),
    FINAL_CALL_RESULT_001("final_call_result_001","关机/停机"),
    FINAL_CALL_RESULT_002("final_call_result_002","拒访"),
    FINAL_CALL_RESULT_003("final_call_result_003","空号"),
    FINAL_CALL_RESULT_004("final_call_result_004","想购车不联系"),
    FINAL_CALL_RESULT_005("final_call_result_005","无购车意向"),
    FINAL_CALL_RESULT_006("final_call_result_006","已购车"),
    FINAL_CALL_RESULT_007("final_call_result_007","三次无法接通"),
    FINAL_CALL_RESULT_008("final_call_result_008","三次无人接听"),
    FINAL_CALL_RESULT_009("final_call_result_009","人号不匹配"),
    FINAL_CALL_RESULT_010("final_call_result_010","下次联系"),
    FINAL_CALL_RESULT_011("final_call_result_011","成功访问"),
    FINAL_CALL_RESULT_013("final_call_result_013","客户不需要"),
    FINAL_CALL_RESULT_999("final_call_result_999","其他");

    private final String code;
    private final String name;

    public static String getNameByCode(String code){
        for(CallStatusEnum nodeEnum : CallStatusEnum.values()){
            if (nodeEnum.code.equals(code)){
                return nodeEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name){
        for(CallStatusEnum nodeEnum : CallStatusEnum.values()){
            if (StringUtils.equals(nodeEnum.name, name)){
                return nodeEnum.code;
            }
        }
        return null;
    }

    private static Map<String, CallStatusEnum> map = Arrays.stream(CallStatusEnum.values()).collect(Collectors.toMap(CallStatusEnum::getCode, e -> e));
    private static Map<String,String> valueMap = Arrays.stream(CallStatusEnum.values()).collect(Collectors.toMap(CallStatusEnum::getCode, e -> e.getName()));

}
