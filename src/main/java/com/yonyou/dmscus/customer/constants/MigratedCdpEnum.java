package com.yonyou.dmscus.customer.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MigratedCdpEnum {
    NO_MIGRATE(0,"否"),
    MIGRATE(1,"是");

    private final Integer code;
    private final String name;
    public static Integer getCodeByName(String name) {
        for (MigratedCdpEnum clueType : MigratedCdpEnum.values()) {
            if (clueType.getName().equals(name)) {
                return clueType.getCode();
            }
        }
        return null;
    }
}
