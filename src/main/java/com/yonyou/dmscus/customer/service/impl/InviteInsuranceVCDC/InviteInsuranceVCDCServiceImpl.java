package com.yonyou.dmscus.customer.service.impl.InviteInsuranceVCDC;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleRecordMapper;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.TmVehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InsuranceBillDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordPO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.inviteInsuranceVCDC.InviteInsuranceVCDCService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/3/9 0009
 */
@Slf4j
@Service
public class InviteInsuranceVCDCServiceImpl implements InviteInsuranceVCDCService {

    @Resource
    InviteInsuranceVehicleRecordMapper inviteInsuranceVehicleRecordMapper;

    @Resource
    BusinessPlatformService businessPlatformService;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 分页查询
     * @param page
     * @param inviteInsuranceVehicleRecordDTO
     * @return
     */
    @Override
    public IPage<InviteInsuranceVehicleRecordDTO> selectInsuranceVCDCPage(Page page, InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO) {
        InviteInsuranceVehicleRecordPO inviteInsuranceVehicleRecordPO = inviteInsuranceVehicleRecordDTO.transDtoToPo(InviteInsuranceVehicleRecordPO.class);
        if (!StringUtils.isNullOrEmpty(inviteInsuranceVehicleRecordDTO.getLargeAreaId()) ||
                !StringUtils.isNullOrEmpty(inviteInsuranceVehicleRecordDTO.getAreaId()) ||
                !StringUtils.isNullOrEmpty(inviteInsuranceVehicleRecordDTO.getDealerCode())) {

            List<String> codes = businessPlatformService.getDealercodes(inviteInsuranceVehicleRecordDTO.getLargeAreaId(),
                    inviteInsuranceVehicleRecordDTO.getAreaId(), inviteInsuranceVehicleRecordDTO.getDealerCode(), null);
            log.info("调用中台查询经销商代码===="+codes.toString());
            inviteInsuranceVehicleRecordPO.setDealerCodes(codes);
            if (codes.size() == 0) {
                page.setRecords(new ArrayList<>());
                return page;
            }
        }
        List<InviteInsuranceVehicleRecordPO> list = inviteInsuranceVehicleRecordMapper.selectFollowInsureRecordVCDC(page,inviteInsuranceVehicleRecordPO);

        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteInsuranceVehicleRecordDTO> result = list.stream().map(m -> m.transPoToDto(InviteInsuranceVehicleRecordDTO.class)
            ).collect(Collectors.toList());
//            for (InviteInsuranceVehicleRecordDTO recordDTO : result) {
//                List<InsuranceBillDTO> insuranceBillList = inviteInsuranceVehicleRecordMapper.selectInsuranceBill(recordDTO
//                        .getVin(), recordDTO.getDealerCode());
//                if (CommonUtils.isNullOrEmpty(insuranceBillList)) {
//                    recordDTO.setIsInsureSuccess(10041002);
//                } else {
//                    recordDTO.setIsInsureSuccess(10041001);
//                }
//            }
           page.setRecords(result);
            return page;
        }
    }

    /**
     * 查询车辆基本信息
     * @param vin
     * @return
     */
    @Override
    public Map<String, Object> selectVehicleByVin(String vin) {
        Map<String,Object> map=new HashMap<String,Object>();
        TmVehicleDTO dto = businessPlatformService.getVehicleByVIN(vin);
        if (dto != null) {
            //销售经销商
            map.put("dealerCode",dto.getDealerCode());
            //日均行驶里程
            map.put("dailyAverageMileage",dto.getMileage());
        }
        return  map;
    }

    /**
     * 导出
     * @param inviteInsuranceVehicleRecordDTO
     * @return
     */
    @Override
    public List<Map> exportExcel(InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO) {
        InviteInsuranceVehicleRecordPO inviteInsuranceVehicleRecordPO = inviteInsuranceVehicleRecordDTO.transDtoToPo(InviteInsuranceVehicleRecordPO.class);
        if (!StringUtils.isNullOrEmpty(inviteInsuranceVehicleRecordDTO.getAreaId()) ||
                !StringUtils.isNullOrEmpty(inviteInsuranceVehicleRecordDTO.getAreaId()) ||
                !StringUtils.isNullOrEmpty(inviteInsuranceVehicleRecordDTO.getDealerCode())) {
            List<String> codes = businessPlatformService.getDealercodes(inviteInsuranceVehicleRecordDTO.getAreaId(),
                    inviteInsuranceVehicleRecordDTO.getAreaId(), inviteInsuranceVehicleRecordDTO.getDealerCode(), null);
            inviteInsuranceVehicleRecordPO.setDealerCodes(codes);
            if (codes.size() == 0) {
                return new ArrayList<Map>();
            }
        }
        Page page = new Page<>(inviteInsuranceVehicleRecordDTO.getCurrentPage(),inviteInsuranceVehicleRecordDTO.getPageSize());
        logger.info("page-->"+ JSON.toJSONString(page));
        List<Map> list = inviteInsuranceVehicleRecordMapper.exportExcel(page,inviteInsuranceVehicleRecordPO);
        return list;
    }
}
