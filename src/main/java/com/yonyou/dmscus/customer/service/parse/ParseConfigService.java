package com.yonyou.dmscus.customer.service.parse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yonyou.dmscus.customer.entity.dto.parse.ParseDto;
import com.yonyou.dmscus.customer.entity.po.parse.ParseConfigPo;

import java.util.List;

public interface ParseConfigService extends IService<ParseConfigPo> {

    /*
    * 根据parseId获取解析正则
    * */
    List<ParseDto> getByParseIdList(List<Long> idList, int i);
}
