package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillDealerMailInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.IsExistByCodeDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.DealerInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillDealerMailInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.companySelectDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.queryUserByOrgTypeDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillDealerMailInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillDealerMailInfoService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 亲善邮箱维护-经销商信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
@Service
public class GoodwillDealerMailInfoServiceImpl extends
		ServiceImpl<GoodwillDealerMailInfoMapper, GoodwillDealerMailInfoPO> implements GoodwillDealerMailInfoService {
	/** 日志对象 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillDealerMailInfoMapper goodwillDealerMailInfoMapper;
	@Autowired
	HttpServletRequest request;

	@Resource
	private RestTemplate directRestTemplate;

	@Autowired
	private MidUrlProperties midUrlProperties;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillDealerMailInfoDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.GoodwillDealerMailInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	@Transactional(readOnly = true)
	public IPage<GoodwillDealerMailInfoDTO> selectPageBysql(Page page,
			GoodwillDealerMailInfoDTO goodwillDealerMailInfoDTO) {
		if (goodwillDealerMailInfoDTO == null) {
			goodwillDealerMailInfoDTO = new GoodwillDealerMailInfoDTO();
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String dealerCode = "";
		GoodwillDealerMailInfoPO goodwillDealerMailInfoPo = goodwillDealerMailInfoDTO
				.transDtoToPo(GoodwillDealerMailInfoPO.class);
		// 查询所选区域所有经销商
		if (!StringUtils.isNullOrEmpty(goodwillDealerMailInfoDTO.getAreaManage())) {

			String successCodes = "0";
			String requestUrls = midUrlProperties.getMidEndOrgCenter()+midUrlProperties.getSelectCompanyInfo();
			companySelectDTO dtos = new companySelectDTO();
			dtos.setAfterSmallArea(goodwillDealerMailInfoDTO.getAreaManage() + "");
			dtos.setCompanyType("15061003");
			HttpHeaders httpHeaderss = new HttpHeaders();
			httpHeaderss.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity<companySelectDTO> httpEntitys = new HttpEntity<>(dtos, httpHeaderss);
			ResponseEntity<ResponseDTO> responseEntitys = directRestTemplate.exchange(requestUrls, HttpMethod.POST,
					httpEntitys, ResponseDTO.class);
			List<DealerInfoDTO> dealerList = new ArrayList<>();

			if (responseEntitys.getBody() != null) {
				if (successCodes.equals(responseEntitys.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					dealerList = objectMapper.convertValue(responseEntitys.getBody().getData(), List.class);
					if (!CommonUtils.isNullOrEmpty(dealerList)) {
						StringBuffer buf = new StringBuffer();
						for (Object object : dealerList) {
							JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
							DealerInfoDTO dealerDto = jsonObject.toJavaObject(DealerInfoDTO.class);
							buf.append("'" + dealerDto.getCompanyCode() + "',");

						}
						dealerCode = buf.toString().substring(0, buf.toString().lastIndexOf(","));
					}

				} else {
					throw new DALException("经销商查询接口异常，请稍后再试");
				}
			}
			goodwillDealerMailInfoPo.setAreaManage(dealerCode);

		}
		// 查询所选集团所有经销商
		if (!StringUtils.isNullOrEmpty(goodwillDealerMailInfoDTO.getBloc())) {

			String successCodes = "0";
			String requestUrls = midUrlProperties.getMidEndOrgCenter() + midUrlProperties.getSelectDealerByGroupCode();
			Map map = new HashMap(16);
			map.put("companyGroupCode", goodwillDealerMailInfoDTO.getBloc());
			HttpHeaders httpHeaderss = new HttpHeaders();
			httpHeaderss.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity<Map> httpEntitys = new HttpEntity<>(map, httpHeaderss);
			ResponseEntity<ResponseDTO> responseEntitys = directRestTemplate.exchange(requestUrls, HttpMethod.POST,
					httpEntitys, ResponseDTO.class);
			List<DealerInfoDTO> dealerList = new ArrayList<>();

			if (responseEntitys.getBody() != null) {
				if (successCodes.equals(responseEntitys.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					dealerList = objectMapper.convertValue(responseEntitys.getBody().getData(), List.class);
					if (!CommonUtils.isNullOrEmpty(dealerList)) {
						StringBuffer buf = new StringBuffer();
						for (Object object : dealerList) {
							JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
							DealerInfoDTO dealerDto = jsonObject.toJavaObject(DealerInfoDTO.class);
							buf.append("'" + dealerDto.getCompanyCode() + "',");

						}
						dealerCode = buf.toString().substring(0, buf.toString().lastIndexOf(","));
					}

				} else {
					throw new DALException("经销商集团代码查询经销商接口异常，请稍后再试");
				}
			}
			goodwillDealerMailInfoPo.setBloc(dealerCode);

		}

		List<GoodwillDealerMailInfoPO> list = goodwillDealerMailInfoMapper.selectPageBySql(page,
				goodwillDealerMailInfoPo);
		Integer count = goodwillDealerMailInfoMapper.selectPageCountBySql(goodwillDealerMailInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			page.setTotal(count);
			return page;
		} else {
			List<GoodwillDealerMailInfoDTO> result = list.stream()
					.map(m -> m.transPoToDto(GoodwillDealerMailInfoDTO.class)).collect(Collectors.toList());
			String dealerCodes = "";
			StringBuffer buf = new StringBuffer();
			for (GoodwillDealerMailInfoDTO dto : result) {
				buf.append("" + dto.getDealerCode() + ",");
			}
			dealerCodes = buf.toString().substring(0, buf.toString().lastIndexOf(","));

			String successCode = "0";
			String requestUrl = midUrlProperties.getMidEndOrgCenter()+midUrlProperties.getSelectByCompanyCode();
			IsExistByCodeDTO dto = new IsExistByCodeDTO();
			dto.setCompanyCode(dealerCodes);
			HttpHeaders httpHeaders = new HttpHeaders();
			httpHeaders.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity<IsExistByCodeDTO> httpEntity = new HttpEntity<>(dto, httpHeaders);
			ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrl, HttpMethod.POST, httpEntity,
					ResponseDTO.class);
			List<Map> interList = new ArrayList<>();
			List<Map> resultList = new ArrayList<>();
			// 接口数据
			if (responseEntity.getBody() != null) {
				if (successCode.equals(responseEntity.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					interList = objectMapper.convertValue(responseEntity.getBody().getData(), List.class);
					if (!CommonUtils.isNullOrEmpty(interList)) {
						for (Object object : interList) {
							JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
							Map map1 = jsonObject.toJavaObject(Map.class);
							for (GoodwillDealerMailInfoDTO dealerMailInfoDTO : result) {
								if (dealerMailInfoDTO.getDealerCode().equals(map1.get("companyCode"))) {
									if (!StringUtils.isNullOrEmpty(map1.get("afterSmallAreaId"))) {
										dealerMailInfoDTO
												.setAreaManage(Integer.valueOf(map1.get("afterSmallAreaId") + ""));
									}
									if (!StringUtils.isNullOrEmpty(map1.get("groupCompanyName"))) {
										dealerMailInfoDTO.setBloc(map1.get("groupCompanyName").toString());
									}
									if (!StringUtils.isNullOrEmpty(map1.get("companyNameCn"))) {
										dealerMailInfoDTO.setDealerName(map1.get("companyNameCn").toString());
									}

								}
							}

						}
					}

				} else {
					throw new DALException("根据经销商代码查询经销商详情接口异常，请稍后再试");
				}
			}
			page.setRecords(result);
			page.setTotal(count);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillDealerMailInfoDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.GoodwillDealerMailInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	@Transactional(readOnly = true)
	public List<GoodwillDealerMailInfoDTO> selectListBySql(GoodwillDealerMailInfoDTO goodwillDealerMailInfoDTO) {
		if (goodwillDealerMailInfoDTO == null) {
			goodwillDealerMailInfoDTO = new GoodwillDealerMailInfoDTO();
		}
		GoodwillDealerMailInfoPO goodwillDealerMailInfoPo = goodwillDealerMailInfoDTO
				.transDtoToPo(GoodwillDealerMailInfoPO.class);
		List<GoodwillDealerMailInfoPO> list = goodwillDealerMailInfoMapper.selectListBySql(goodwillDealerMailInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillDealerMailInfoDTO.class)).collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.GoodwillDealerMailInfoDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	@Transactional(readOnly = true)
	public GoodwillDealerMailInfoDTO getById(Long id) {
		GoodwillDealerMailInfoPO goodwillDealerMailInfoPo = goodwillDealerMailInfoMapper.selectById(id);
		if (goodwillDealerMailInfoPo != null) {
			return goodwillDealerMailInfoPo.transPoToDto(GoodwillDealerMailInfoDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillDealerMailInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillDealerMailInfoDTO goodwillDealerMailInfoDTO) {
		// 对对象进行赋值操作
		GoodwillDealerMailInfoPO goodwillDealerMailInfoPo = goodwillDealerMailInfoDTO
				.transDtoToPo(GoodwillDealerMailInfoPO.class);
		// 执行插入
		int row = goodwillDealerMailInfoMapper.insert(goodwillDealerMailInfoPo);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillDealerMailInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillDealerMailInfoDTO goodwillDealerMailInfoDTO) {
		// 校验该经销商是否存在
		Integer number = goodwillDealerMailInfoMapper
				.queryDealerIndoByDealerCode(goodwillDealerMailInfoDTO.getDealerCode(), id);
		if (number != null && number > 0) {
			throw new DALException("该经销商信息已存在，请确认后再输入");
		}
		if (id != 0) {
			GoodwillDealerMailInfoPO goodwillDealerMailInfoPo = goodwillDealerMailInfoMapper.selectById(id);
			// 对对象进行赋值操作
			goodwillDealerMailInfoDTO.transDtoToPo(goodwillDealerMailInfoPo);
			// 执行更新
			int row = goodwillDealerMailInfoMapper.updateById(goodwillDealerMailInfoPo);
		} else {
			// 对对象进行赋值操作
			GoodwillDealerMailInfoPO goodwillDealerMailInfoPo = goodwillDealerMailInfoDTO
					.transDtoToPo(GoodwillDealerMailInfoPO.class);
			// 执行插入
			int row = goodwillDealerMailInfoMapper.insert(goodwillDealerMailInfoPo);
		}

		return 1;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillDealerMailInfoMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillDealerMailInfoMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public List<Map> blocSelect() {
		// 接口数据
		Map map = new HashMap(16);
		map.put("roleId", "1");
		map.put("roleName", "集团1");
		Map map2 = new HashMap(16);
		map2.put("roleId", "2");
		map2.put("roleName", "集团2");

		List<Map> list = new ArrayList<Map>();
		list.add(map);
		list.add(map2);
		return list;
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public List<Map> areaManageSelect() {
		// 接口数据，需拼装
		Map map = new HashMap(16);
		map.put("roleId", "1-1");
		map.put("roleName", "东区-王大伟");
		Map map2 = new HashMap(16);
		map2.put("roleId", "2-1");
		map2.put("roleName", "西区-王小伟");

		List<Map> list = new ArrayList<Map>();
		list.add(map);
		list.add(map2);
		return list;
	}

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillDealerMailInfoDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.GoodwillDealerMailInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@SuppressWarnings("unchecked")
	@Override
	@Transactional(readOnly = true)
	public IPage<List> selectVcdcPageBysql(Page page, String role) {

		String[] roleList = new String[] { "SHQYJL", "SHDQJL", "SHQYZJ", "CCMQ", "OEM-CWJL", "VP", "CFO", "CEO",
				"CCMGJJL", "CCMZJ" };

		List<Map> list = new ArrayList<>();
		List<Map> resultList = new ArrayList<>();
		if (!StringUtils.isNullOrEmpty(role)) {
			String successCode = "0";
			String requestUrl = midUrlProperties.getMidEndAuthCenter() + midUrlProperties.getOrgRole();
			queryUserByOrgTypeDTO dto = new queryUserByOrgTypeDTO();
			Map map = new HashMap(16);
			map.put("roleCode", role);
			dto.setData(map);
			HttpHeaders httpHeaders = new HttpHeaders();
			httpHeaders.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity<queryUserByOrgTypeDTO> httpEntity = new HttpEntity<queryUserByOrgTypeDTO>(dto, httpHeaders);
			ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrl, HttpMethod.POST, httpEntity,
					ResponseDTO.class);
			// 接口数据
			if (responseEntity.getBody() != null) {
				if (successCode.equals(responseEntity.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					list = objectMapper.convertValue(responseEntity.getBody().getData(), List.class);
					if (!CommonUtils.isNullOrEmpty(list)) {
						for (Object object : list) {
							JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
							Map map1 = jsonObject.toJavaObject(Map.class);
							Map resultMap = new HashMap(16);
							resultMap.put("chName", map1.get("username"));
							resultMap.put("enName", map1.get("employeeNameEn"));
							resultMap.put("cdsid", map1.get("cdsid"));
							resultMap.put("phone", map1.get("phone"));
							resultMap.put("email", map1.get("email"));
							resultMap.put("role", map1.get("roleName"));
							resultList.add(resultMap);

						}
					}

				} else {
					throw new DALException("组织角色查询员工接口异常，请稍后再试");
				}
			}
		} else {
			for (int i = 0; i < roleList.length; i++) {
				String successCode = "0";
				String requestUrl = midUrlProperties.getMidEndAuthCenter() + midUrlProperties.getOrgRole();

				queryUserByOrgTypeDTO dto = new queryUserByOrgTypeDTO();
				Map map = new HashMap(16);
				map.put("roleCode", roleList[i]);
				dto.setData(map);
				HttpHeaders httpHeaders = new HttpHeaders();
				httpHeaders.setContentType(MediaType.APPLICATION_JSON);
				HttpEntity<queryUserByOrgTypeDTO> httpEntity = new HttpEntity<queryUserByOrgTypeDTO>(dto, httpHeaders);
				ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrl, HttpMethod.POST,
						httpEntity, ResponseDTO.class);

				// 接口数据
				if (responseEntity.getBody() != null) {
					if (successCode.equals(responseEntity.getBody().getReturnCode())) {
						ObjectMapper objectMapper = new ObjectMapper();
						list = objectMapper.convertValue(responseEntity.getBody().getData(), List.class);
						if (!CommonUtils.isNullOrEmpty(list)) {
							for (Object object : list) {
								JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
								Map map1 = jsonObject.toJavaObject(Map.class);
								Map resultMap = new HashMap(16);
								resultMap.put("chName", map1.get("username"));
								resultMap.put("enName", map1.get("employeeNameEn"));
								resultMap.put("cdsid", map1.get("cdsid"));
								resultMap.put("phone", map1.get("phone"));
								resultMap.put("email", map1.get("email"));
								resultMap.put("role", map1.get("roleName"));
								resultList.add(resultMap);

							}
						}

					} else {
						throw new DALException("组织角色查询员工接口异常，请稍后再试");
					}
				}
			}
		}

		if (CommonUtils.isNullOrEmpty(resultList)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			page.setTotal(resultList.size());
			page.setRecords(resultList.subList((int) ((page.getCurrent() - 1) * page.getSize()),
					(int) ((resultList.size() - (page.getCurrent() - 1) * page.getSize()) <= page.getSize()
							? resultList.size()
							: (page.getCurrent() - 1) * page.getSize() + page.getSize())));
			return page;
		}
	}
}
