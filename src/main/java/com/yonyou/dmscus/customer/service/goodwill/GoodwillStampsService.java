package com.yonyou.dmscus.customer.service.goodwill;

import java.text.ParseException;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillNoticeInvoiceInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillStampsDTO;

/**
 * <p>
 * 亲善券表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface GoodwillStampsService {
	public IPage<GoodwillStampsDTO> selectPageBysql(Page page, GoodwillStampsDTO goodwillStampsDTO);

	List<GoodwillStampsDTO> selectPageByList(GoodwillStampsDTO goodwillStampsDTO);

	IPage<GoodwillStampsDTO> selectPageByConsume(Page page, GoodwillStampsDTO goodwillStampsDTO) throws ParseException;

	void exportconGoodwillStamps(GoodwillStampsDTO goodwillStampsDTO);

	/**
	 * 下载导出代金券使用明细（下载中心回调使用）
	 * @param goodwillStampsDTO
	 * @return
	 */
	List<GoodwillStampsDTO> downloadConGoodwillStamps(Page<GoodwillStampsDTO> page, GoodwillStampsDTO goodwillStampsDTO) throws ParseException;

	public int editNoticeInvoiceInfo(GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto);

	public IPage<List> queryInvoiceHistory(Page page, Long goodwillApplyId);

}
