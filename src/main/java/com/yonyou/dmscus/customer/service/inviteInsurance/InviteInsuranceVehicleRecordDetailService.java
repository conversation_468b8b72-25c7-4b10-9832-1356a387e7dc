package com.yonyou.dmscus.customer.service.inviteInsurance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDetailDTO;

import java.util.List;

/**
 * <p>
 * 车辆邀约续保记录明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
public interface InviteInsuranceVehicleRecordDetailService {

    Long addInviteInsuranceVehicleRecordDetail (InviteInsuranceVehicleRecordDetailDTO inviteInsuranceVehicleRecordDetailDTO);

    //void updateInviteInsuranceVehicleRecordDetail(InviteInsuranceVehicleRecordDetailDTO recordDetail);

    /**
     * 续保店端查询跟进记录
     * @param vin
     * @param id
     * @return
     */
    List<InviteInsuranceVehicleRecordDetailDTO> getInviteInsuranceVehicleRecordInfoDlr(String vin, Long id);

    /**
     * 店端查询续保线索跟进明细历史
     * @param vin
     * @param dealerCode
     * @return
     */
    IPage<InviteInsuranceVehicleRecordDetailDTO> selectFollowInviteInsureDetailHistory(Page page, String vin, String dealerCode);

}
