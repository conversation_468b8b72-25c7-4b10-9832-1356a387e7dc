package com.yonyou.dmscus.customer.service.voicemanage;


import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.souche.api.SoucheResponse;
import com.yonyou.dmscus.customer.dao.voicemanage.CallDetailsMapper;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.CallDetailsDTO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO;
import com.yonyou.dmscus.customer.service.common.IBaseService;


/**
 * <p>
 * 通话详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
public interface CallDetailsService  extends IBaseService<CallDetailsDTO> {

    List<CallDetailsDTO> getCallDetails(String detailId);
    
    
    void getCallDetailsByAi();
    
    String sendSouche(String sessionId,String serviceId,Date startTime);


    
    String getVoiceUrl(Long id);
    String getVoiceUrl1(Long id);

    String obtainToken();

    void getCallTotalScore();
    void getCallTotalScorezj();
    String sendSouchezj(String sessionId,String serviceId,Date startTime);

    void insertList(List<CallDetailsPO> callDetailsPOList);


}
