package com.yonyou.dmscus.customer.service.faultLight;

import com.yonyou.dmscus.customer.entity.dto.faultLight.HandleDimCluesDto;
import com.yonyou.dmscus.customer.entity.po.faultLight.CluesDiagnosticInfoRelationPO;

import java.util.List;

public interface CluesDiagnosticInfoRelationService {
    void markIsDisplayDiagnosticInfo(HandleDimCluesDto handleDimCluesDto);
    HandleDimCluesDto recordNumberOfExecutions(List<CluesDiagnosticInfoRelationPO> relationPOList);
    List<CluesDiagnosticInfoRelationPO> queryDiagnosticInfoRelationList(int offset, int limit, String createdAt, Integer processingStatus);
    Integer queryDiagnosticInfoRelationCount(String createdAt, Integer processingStatus);
}
