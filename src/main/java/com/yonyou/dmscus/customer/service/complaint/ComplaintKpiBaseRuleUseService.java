package com.yonyou.dmscus.customer.service.complaint;

    import com.baomidou.mybatisplus.core.metadata.IPage;
    import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
    import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiSetDTO;
    import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseDTO;
    import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseTestDTO;
    import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiTest;
    import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRuleUsePO;

    import java.util.List;


/**
 * <p>
 * 客户投诉KP基础规则使用表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
public interface ComplaintKpiBaseRuleUseService {
    /**
     * 分页查询
     * @param page
     * @param complaintKpiBaseRuleUseDTO
     * @return
     */
     IPage<ComplaintKpiBaseRuleUseDTO> selectPageBysql(Page page, ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO);

    /**
     * 集合查询
     * @param complaintKpiBaseRuleUseDTO
     * @return
     */
     List<ComplaintKpiBaseRuleUseDTO> selectListBySql(ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO);

    /**
     * 通过id查询
     * @param id
     * @return
     */
     ComplaintKpiBaseRuleUseDTO getById(Long id);

    /**
     * 新增
     * @param complaintKpiBaseRuleUseDTO
     * @return
     */
    int insert(ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO);

    /**
     * 更新
     * @param id
     * @param complaintKpiBaseRuleUseDTO
     * @return
     */
    int update(Long id, ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO);

    /**
     * 通过id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 分页查询
     * @param page
     * @param complaintKpiBaseRuleUseTestDTO
     * @return
     */
    IPage<ComplaintKpiBaseRuleUseTestDTO> selectPageBysql1(Page page, ComplaintKpiBaseRuleUseTestDTO complaintKpiBaseRuleUseTestDTO);

    /**
     * 集合查询
     * @param complaintKpiBaseRuleUseTestDTO
     * @return
     */
    List<ComplaintKpiBaseRuleUseTestDTO> selectListBySql1(ComplaintKpiBaseRuleUseTestDTO complaintKpiBaseRuleUseTestDTO);

    /**
     * 更新警戒值
     * @param list
     * @return
     */
    int updateWarnValue(List<ComplaintKpiTest> list);
}
