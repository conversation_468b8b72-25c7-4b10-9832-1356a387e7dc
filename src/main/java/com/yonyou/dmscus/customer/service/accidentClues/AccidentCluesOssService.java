package com.yonyou.dmscus.customer.service.accidentClues;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.*;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesFollowPO;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesImportPO;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesPO;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesSaNumberPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/16 0016
 */
public interface AccidentCluesOssService {


    void exportExcelAccidentExcel(AccidentCluesExportDTO dto);
}
