package com.yonyou.dmscus.customer.service.goodwill;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairPartInfoDTO;


                                                                        /**
 * <p>
 * 亲善预申请子表——维修零配件信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
public interface GoodwillApplyRepairPartInfoService  {
	public IPage<GoodwillApplyRepairPartInfoDTO>selectPageBysql(Page page,GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO);
	public List<GoodwillApplyRepairPartInfoDTO>selectListBySql(GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO);
	public GoodwillApplyRepairPartInfoDTO getById(Long id);
	public int insert(GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO);
	public int update(Long id, GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO);
	public int deleteById(Long id);
	public int deleteBatchIds(String ids);
	public List<GoodwillApplyRepairPartInfoDTO> getSupportApplyPartInfoById(Long goodwillApplyId);
   
}
