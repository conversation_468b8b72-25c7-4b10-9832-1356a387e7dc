package com.yonyou.dmscus.customer.service.invitationautocreate;


import com.yonyou.dmscus.customer.dto.DailyAverageMileageVo;
import com.yonyou.dmscus.customer.dto.RepairOrderVO;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.entity.dto.invitationautocreate.InviteVehicleTaskDTO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO;
import com.yonyou.dmscus.customer.service.common.IBaseService;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>
 * 车辆邀约任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-07
 */
public interface InviteVehicleTaskService extends IBaseService<InviteVehicleTaskDTO> {

    /**
     *  创建邀约任务
     * @param createDate
     * @return
     */
    int inviteAutoCreateTask1(String createDate);

    /**
     *  创建邀约任务
     * @param createDate
     * @return
     */
    int inviteAutoCreateTask2(String createDate);

    /**
     * 关闭线索
     */
    void closeClues(String createDate);

    /**
     *  创建邀约任务(补数据)
     * @param createDate
     * @return
     */
    int inviteAutoCreateTaskCompensate(String createDate, String vin, String dealerCode);

    /**
     * 关闭保修线索
     * @param createDate
     * @return
     */
    int closeGuarantee(String createDate);

    /**
     *
     * @param createDate
     * @return
     */
    int createTyreTask(String createDate);

    /**
     * 根据邀约任务创建邀约线索
     * @param createDate
     * @return
     */
    int createInviteByTask(String createDate);

    int partitionComputeDailyAverageMileage(String createDate, Integer page, Integer number, Integer size);

    int partitionGetNotVocVeh(String createDate, Integer page, Integer number, Integer size);

    DailyAverageMileageVo commonCountDailyAverageMileage(String vin, Date invoiceDate);

    int closeInvite(String createDate);

    int inviteAutoCreateQb();

    int fuseRule();

    int inviteAutoCreateTask3(String createDate, Integer days);

    /**
     * 初始化数据
     * @param createDate
     * @param days
     * @return
     */
    int initInviteInfo(String createDate, Integer days);

    int computeDailyAverageMileageAll();

    /**重新拆分合并线索
     *
     * @param po
     */
    void mergeInvite(InviteVehicleRecordPO po);


    /**
     * 易损件
     * @param createDate
     * @return
     */
    int updateInviteVulnerable(String createDate);

    /**
     * 定保
     * @param createDate
     * @return
     */
    int updateInviteMaintainByRuleChanged(String createDate);

    /**
     * 首保
     * @param createDate
     * @return
     */
    int updateInviteFristMaintainByRuleChanged(String createDate);

    /**
     * 客户流失
     * @param createDate
     * @return
     */
    int updateInviteCustomerLossByRuleChanged(String createDate);

    /**
     * 保修
     * @param createDate
     * @return
     */
    int updateInviteGuaranteeByRuleChanged(String createDate);

    /**
     * 二次跟进
     * @param createDate
     * @return
     */
    int createTwiceFollow(String createDate);

    /**
     * 自动分配SA
     */
    void taskAllocationAsync(String vin);

    int inviteAutoCreateTaskList(List<String> list);

    List<InviteVehicleTaskPO> getWaitCloseRecordByVin(String vin);

    InviteVehicleTaskPO createCustomerLossTaskVoc(InviteVehicleTaskPO t, Integer inm);
    InviteVehicleTaskPO createWarnTask(InviteVehicleTaskPO t, Integer inm);

    InviteVehicleTaskPO selectByInviteId(Long id);

    InviteVehicleTaskPO createCustomerLossTaskVoc18(InviteVehicleTaskPO t, Integer inm);

    void createCustomerTaskVoc1(String vin, List<InviteVehicleTaskPO> inst1, List<InviteVehicleTaskPO> inst66);

    void createCustomerTaskVoc2(Integer inm, List<InviteVehicleTaskPO> inst2, List<InviteVehicleTaskPO> inst66, String vin, Date deliveryDate);

    int inser1List1(List<InviteVehicleTaskPO> inst1);

    int inser1List2(List<InviteVehicleTaskPO> inst2);

    int inser1List12(List<InviteVehicleTaskPO> inst12);

    int inser1List6(List<InviteVehicleTaskPO> inst6);

    int inser1List66(List<InviteVehicleTaskPO> inst66);

    int updateList(List<Long> upst6);

    int inser1List63(List<InviteVehicleTaskPO> inst63);

    void createCustomerTaskVoc12(String vin, List<InviteVehicleTaskPO> inst12);

    void createCustomerLossTaskVoc6(List<InviteVehicleTaskPO> inst6, String vin);
    void createCustomerLossTaskVoc63(List<InviteVehicleTaskPO> inst63, String vin);
    void createCustomerLossTaskVoc63v(List<InviteVehicleTaskPO> inst63, String vin,String code);

    List<InviteVehicleTaskPO> selectListTaskVocByTime(String dateTime);

    int updateLossVocByVin(String vin);

    InviteVehicleRecordPO issued(InviteVehicleTaskPO po);

    void taskAllocationVocList(List<InviteVehicleRecordPO> pos);

    void losspush(String dateTime);

    InviteVehicleTaskPO createCustomerTaskVoc6(InviteVehicleRecordPO po);

    void inser123List12(List<InviteVehicleTaskPO> inst123);

    int saveListInit6(List<InviteVehicleTaskPO> inst6);
    void createCustomerTaskVoc12v(String vin, List<InviteVehicleTaskPO> inst12,String code);

    //修补流失客户任务数据
    void doRepairLossTask(Integer page, List<InviteVehicleTaskPO> addList,
                         List<Long> delList,
                         List<InviteVehicleTaskPO> upList);

    void batchQueryUserInfo(List<VehicleOwnerVO> list);

    void doMaintainTask(VehicleOwnerVO vo, RepairOrderVO ro,List<String> listCode);

}
