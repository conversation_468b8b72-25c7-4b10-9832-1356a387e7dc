package com.yonyou.dmscus.customer.service.inviteInsurance;


import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceSaAllocateRuleDetailDTO;

import java.util.List;

/**
 * <p>
 * 续保SA分配规则明细表,通过经销商CODE与invite_insurance_sa_allocate_rule关联，用于经销商SA分配中平均分配 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
public interface InviteInsuranceSaAllocateRuleDetailService {

    List<InviteInsuranceSaAllocateRuleDetailDTO> getInsuranceSaAllocateRuleDetail(String dealerCode);

    int saveInsuranceSaAllocateRuleDetail(List<InviteInsuranceSaAllocateRuleDetailDTO> list);

    List<InviteInsuranceSaAllocateRuleDetailDTO> getInsuranceSaAllocateRuleDetailList(String dealerCodeList);
}
