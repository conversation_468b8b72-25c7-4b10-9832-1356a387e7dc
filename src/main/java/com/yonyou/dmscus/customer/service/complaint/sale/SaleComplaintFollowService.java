package com.yonyou.dmscus.customer.service.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintFollowDTO;

import java.text.ParseException;
import java.util.List;


/**
 * <p>
 * 销售客户投诉跟进表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-11
 */
public interface SaleComplaintFollowService {
    IPage<SaleComplaintFollowDTO> selectPageBysql(Page page, SaleComplaintFollowDTO saleComplaintFollowDTO);
    List<SaleComplaintFollowDTO> selectListBySql(String flag,SaleComplaintFollowDTO saleComplaintFollowDTO);
    SaleComplaintFollowDTO getById(Long id);
    int insert(SaleComplaintFollowDTO saleComplaintFollowDTO);
    int update(Long id, SaleComplaintFollowDTO saleComplaintFollowDTO);
    int deleteById(Long id);

    int insertcCus(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO)throws ParseException;

    int reportVeh(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO) throws ParseException;

    int insertRegionCus(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO)throws ParseException;

    List<ComplaintInfMoreDTO> queryNextFollowing();
}
