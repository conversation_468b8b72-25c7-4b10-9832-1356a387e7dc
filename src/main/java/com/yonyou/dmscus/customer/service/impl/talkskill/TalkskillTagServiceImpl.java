package com.yonyou.dmscus.customer.service.impl.talkskill;


import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;

import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.talkskill.TalkskillTagMapper;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillTagDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillTagPO;
import com.yonyou.dmscus.customer.service.talkskill.TalkskillTagService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;


/**
* <p>
* 话术标签 服务实现类
* </p>
*
* <AUTHOR>
* @since 2020-03-20
*/
@Service
@Transactional(rollbackFor=Exception.class)
public class TalkskillTagServiceImpl  implements TalkskillTagService {
    private final Logger logger=LoggerFactory.getLogger(this.getClass());
    @Resource
    TalkskillTagMapper talkskillTagMapper;

    /**
    * 根据查询条件返回结果集
    *
    * @param talkskillTagDTO 查询条件
    * @return java.util.List<com.yonyou.dmscus.repair.entity.dto.TalkskillTagDTO>
    * <AUTHOR>
    * @since 2018/7/22 0022
    */
    @Override
    @Transactional(readOnly=true)
    public List<TalkskillTagPO> selectListBySql(TalkskillTagDTO talkskillTagDTO){
        if(talkskillTagDTO ==null){
            talkskillTagDTO =new TalkskillTagDTO();
        }
        TalkskillTagPO talkskillTagPo =talkskillTagDTO.transDtoToPo(TalkskillTagPO.class);
        List<TalkskillTagPO> list= talkskillTagMapper.selectListBySql(talkskillTagPo);
        if(CommonUtils.isNullOrEmpty(list)){
            return null;
        }else{
            return list;
        }
    }

    /**
    * 根据查询条件返回结果集
    *
    * @param id 主键ID
    * @return com.yonyou.dmscus.repair.entity.dto.TalkskillTagDTO
    * <AUTHOR>
    * @since 2018/7/22 0022
    */
    @Override
    @Transactional(readOnly=true)
    public TalkskillTagDTO getById(Long id){
        TalkskillTagPO talkskillTagPo = talkskillTagMapper.selectById(id);
        if(talkskillTagPo!=null){
            return talkskillTagPo.transPoToDto(TalkskillTagDTO.class);
        }else{
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
    * 根据DTO 进行数据新增
    *
    * @param talkskillTagDTO 页面DTO
    * @return int
    * <AUTHOR>
    * @since 2018/7/21 0021
    */
    @Override
    public int insert(TalkskillTagDTO talkskillTagDTO){
        TalkskillTagPO talkskillTagPo = talkskillTagDTO.transDtoToPo(TalkskillTagPO.class);
        return talkskillTagMapper.insert(talkskillTagPo);
    }

    private static List<String> talkskillTag1s = new ArrayList<>();
    static {
        talkskillTag1s.add(CommonConstants.TALKSKILL_TAG1_SURROUND_SAVE_ORDE);
        talkskillTag1s.add(CommonConstants.TALKSKILL_TAG1_SURROUND);
        talkskillTag1s.add(CommonConstants.TALKSKILL_TAG1_ORDER_CONFIRMATION);
        talkskillTag1s.add(CommonConstants.TALKSKILL_TAG1_HEALTH_REPORT_START);
        talkskillTag1s.add(CommonConstants.TALKSKILL_TAG1_ADDITIONAL_MAINTENANCE);
        talkskillTag1s.add(CommonConstants.TALKSKILL_TAG1_HEALTH_REPORT_END);
        talkskillTag1s.add(CommonConstants.TALKSKILL_TAG1_EXPENSE_SETTLEMENT);
        talkskillTag1s.add(CommonConstants.TALKSKILL_TAG1_EDIT_SAVE_ORDE);
    }

    /**
    * 根据DTO 及ID 进行数据更新
    *
    *
    * @param talkskillTagDTO 页面DTO
    * @return int
    * <AUTHOR>
    * @since 2018/7/21 0021
    */
    @Override
    public int updateList(List<TalkskillTagDTO> talkskillTagDTO){


        for(int i=0;i<talkskillTagDTO.size();i++){
            TalkskillTagPO talkskillTagPo = talkskillTagDTO.get(i).transDtoToPo(TalkskillTagPO.class);;
            //对对象进行赋值操作
            if(talkskillTagDTO.get(i).getTagId() == null){
                String tagCode = talkskillTagDTO.get(i).getTagCode();
                if (talkskillTag1s.contains(tagCode)) {
                    throw new ServiceBizException(tagCode + " 为定制化标签，请勿重复添加！");
                }

                talkskillTagMapper.insert(talkskillTagPo);
            }else {
                talkskillTagMapper.updateById(talkskillTagPo);
            }
        }
        return 1;
    }
    /**
     * 根据DTO 及ID 进行数据更新
     *
     *
     * @param talkskillTagDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, TalkskillTagDTO talkskillTagDTO){
        TalkskillTagPO talkskillTagPo = talkskillTagMapper.selectById(id);
        talkskillTagDTO.transDtoToPo(talkskillTagPo);
        return talkskillTagMapper.updateById(talkskillTagPo);
    }
}
