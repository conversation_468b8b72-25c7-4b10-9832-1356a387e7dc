package com.yonyou.dmscus.customer.service.complaint.impl.complaint;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintInfoMapper;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintInfoMapper;
import com.yonyou.dmscus.customer.dto.GetModelNameDTO;
import com.yonyou.dmscus.customer.dto.modelNameDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.AcceptComlaintData.AcceptNewComplaintDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.SelectSmallManagerDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.SmallManagerDataDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoPO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoPO;
import com.yonyou.dmscus.customer.middleInterface.CompanyDetailByCodeDTO;
import com.yonyou.dmscus.customer.service.complaint.AcceptComplaintService;

import com.yonyou.dmscus.customer.service.complaint.ComplaintFollowService;
import com.yonyou.dmscus.customer.service.complaint.ComplaintInfoService;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintFollowService;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintInfoService;
import com.yonyou.dmscus.customer.service.impl.CommonServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
public class AcceptComplaintServiceImpl  implements AcceptComplaintService {
    @Autowired
    ComplaintInfoService complaintInfoService;
    @Resource
    ComplaintInfoMapper complaintInfoMapper;
    @Autowired
    ComplaintFollowService complaintFollowService;
    @Autowired
    CommonServiceImpl commonServiceImpl;
    @Autowired
    ComplaintDealerCcmRefServiceImpl complaintDealerCcmRefService;
    @Resource
    SaleComplaintInfoMapper saleComplaintInfoMapper;
    @Autowired
    SaleComplaintInfoService saleComplaintInfoService;
    @Autowired
    SaleComplaintFollowService saleComplaintFollowService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int acceptNewComplaint(AcceptNewComplaintDTO acceptNewComplaintDTO) {
        int row=0;
        if(acceptNewComplaintDTO.getWorkOrderNature().equals("售后")){
            ComplaintInfoDTO complaintInfoDTO=new ComplaintInfoDTO();
            complaintInfoDTO.setComplaintId(acceptNewComplaintDTO.getComplaintId());
            complaintInfoDTO.setWorkOrderNature(83611002);
            switch (acceptNewComplaintDTO.getType()){
                case "产品质量":
                    complaintInfoDTO.setType(81981001);
                    break;
                case "销售":
                    complaintInfoDTO.setType(81981002);
                    break;
                case "服务":
                    complaintInfoDTO.setType(81981003);
                    break;
                case "重要":
                    complaintInfoDTO.setType(81981004);
                    break;
            }
            complaintInfoDTO.setWorkOrderClassification(getWorkOrderClassification(acceptNewComplaintDTO.getWorkOrderClassification()));
            complaintInfoDTO.setSource(getSource(acceptNewComplaintDTO.getSource()));
            //工单状态设为未跟进(默认)
            complaintInfoDTO.setWorkOrderStatus(82451001);
            //未结案
            complaintInfoDTO.setIsCloseCase(10041002);
            //400下发
            complaintInfoDTO.setDataSources(82101002);
            //未结案
            complaintInfoDTO.setCloseCaseStatus(82441004);
            complaintInfoDTO.setServiceCommitment(getServiceCommitment(acceptNewComplaintDTO.getServiceCommitment()));
            complaintInfoDTO.setName(acceptNewComplaintDTO.getName());
            complaintInfoDTO.setCallName(acceptNewComplaintDTO.getCallName());
            complaintInfoDTO.setCallTime(acceptNewComplaintDTO.getCallTime());
            complaintInfoDTO.setCallTel(acceptNewComplaintDTO.getCallTel());
            complaintInfoDTO.setVin(acceptNewComplaintDTO.getVin());
            complaintInfoDTO.setLicensePlateNum(acceptNewComplaintDTO.getLicensePlateNum());
            complaintInfoDTO.setBuyTime(acceptNewComplaintDTO.getBuyTime());
            //车型
            modelNameDTO modelNameDTO=new modelNameDTO();
            modelNameDTO.setModelName(acceptNewComplaintDTO.getModel());
            List<GetModelNameDTO> list= commonServiceImpl.getModelName(modelNameDTO);
            complaintInfoDTO.setModel(String.valueOf(list.get(0).getId()));
            complaintInfoDTO.setModelYear(acceptNewComplaintDTO.getModelYear());
            complaintInfoDTO.setBuyDealerCode(acceptNewComplaintDTO.getBuyDealerCode());
            CompanyDetailByCodeDTO companyInfo = complaintDealerCcmRefService.getCompanyInfo(acceptNewComplaintDTO.getDealerCode());
            complaintInfoDTO.setRegion(companyInfo.getAfterBigAreaName());
            SelectSmallManagerDTO selectSmallManagerDTO = new SelectSmallManagerDTO();
            selectSmallManagerDTO.setOrgId(companyInfo.getAfterSmallAreaId().intValue());
            List<String> roleCode = new ArrayList<>();
            roleCode.add("SHQYJL");
            selectSmallManagerDTO.setRoleCode(roleCode);
            List<SmallManagerDataDTO> smallManagerList = complaintDealerCcmRefService.getSmallManager(selectSmallManagerDTO);
            complaintInfoDTO.setBloc(companyInfo.getGroupCompanyName());
            complaintInfoDTO.setRegionId(companyInfo.getAfterBigAreaId());
            complaintInfoDTO.setRegionManagerId(companyInfo.getAfterSmallAreaId());
            complaintInfoDTO.setDealerName(companyInfo.getCompanyNameCn());
            complaintInfoDTO.setBlocId(Long.valueOf(companyInfo.getGroupCompanyId()));
            CompanyDetailByCodeDTO companyInfoBuy = complaintDealerCcmRefService.getCompanyInfo(acceptNewComplaintDTO.getBuyDealerCode());
            complaintInfoDTO.setBuyRegion(companyInfoBuy.getAfterBigAreaName());
            selectSmallManagerDTO = new SelectSmallManagerDTO();
            selectSmallManagerDTO.setOrgId(companyInfoBuy.getAfterSmallAreaId().intValue());
            selectSmallManagerDTO.setRoleCode(roleCode);
            smallManagerList = complaintDealerCcmRefService.getSmallManager(selectSmallManagerDTO);
            if (smallManagerList.size() != 0) {
                complaintInfoDTO.setBuyRegionManager(smallManagerList.get(0).getEmployeeName());
            }
            complaintInfoDTO.setBuyBloc(companyInfoBuy.getGroupCompanyName());
            complaintInfoDTO.setBuyRegionId(companyInfoBuy.getAfterBigAreaId());
            complaintInfoDTO.setBuyRegionManagerId(companyInfoBuy.getAfterSmallAreaId());
            complaintInfoDTO.setWorkOrderClassification(83621001);
            if (smallManagerList.size() != 0) {
                complaintInfoDTO.setRegionManager(smallManagerList.get(0).getEmployeeName());
            }
            if (smallManagerList.size() != 0) {
                complaintInfoDTO.setRegionManager(smallManagerList.get(0).getEmployeeName());
            }
            complaintInfoDTO.setRegion(companyInfo.getAfterBigAreaName());
            complaintInfoDTO.setBuyDealerName(acceptNewComplaintDTO.getBuyDealerName());
            complaintInfoDTO.setDealerCode(acceptNewComplaintDTO.getDealerCode());
            complaintInfoDTO.setDealerName(acceptNewComplaintDTO.getDealerName());
            complaintInfoDTO.setMileage(acceptNewComplaintDTO.getMileage());
            complaintInfoDTO.setReplyName(acceptNewComplaintDTO.getReplyName());
            complaintInfoDTO.setReplyTel(acceptNewComplaintDTO.getReplyTel());
            complaintInfoDTO.setReplyTel2(acceptNewComplaintDTO.getReplyTel2());
            complaintInfoDTO.setSubject(acceptNewComplaintDTO.getSubject());
            complaintInfoDTO.setProblem(acceptNewComplaintDTO.getProblem());
            complaintInfoDTO.setHopeReplyTime(acceptNewComplaintDTO.getHopeReplyTime());
            if(acceptNewComplaintDTO.getCategory1()!=null){
                complaintInfoDTO.setCategory1(getCategory1(acceptNewComplaintDTO.getCategory1()));
            }
            if(acceptNewComplaintDTO.getCategory2()!=null){
                complaintInfoDTO.setCategory2(getCategory2(acceptNewComplaintDTO.getCategory2()));
            }
            if(acceptNewComplaintDTO.getCategory3()!=null){
                complaintInfoDTO.setCategory3(getCategory3(acceptNewComplaintDTO.getCategory3()));
            }
            if(acceptNewComplaintDTO.getCcPart()!=null){
                complaintInfoDTO.setCcPart(getCcPart(acceptNewComplaintDTO.getCcPart()));
            }
            if(acceptNewComplaintDTO.getCcSubdivisionPart()!=null){
                complaintInfoDTO.setCcSubdivisionPart(getCcSubdivisionPart(acceptNewComplaintDTO.getCcSubdivisionPart()));
            }
            if(acceptNewComplaintDTO.getCcProblem()!=null){
                complaintInfoDTO.setCcProblem(getCcProblem(acceptNewComplaintDTO.getCcProblem()));
            }
            if(acceptNewComplaintDTO.getCcRequirement()!=null){
                complaintInfoDTO.setCcRequirement(getCcRequirement(acceptNewComplaintDTO.getCcRequirement()));
            }
            complaintInfoDTO.setImportanceLevel(getImportanceLevel(acceptNewComplaintDTO.getImportanceLevel()));
            complaintInfoDTO.setReport(true);
            complaintInfoService.insert(complaintInfoDTO);
            if (acceptNewComplaintDTO.getIllustrate()!=null){
                //跟进信息赋值
                List<ComplaintInfoPO> queryid = complaintInfoMapper.queryid(complaintInfoDTO);
                long id = queryid.get(0).getId();
                ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
                complaintFollowDTO.setFollowContent(acceptNewComplaintDTO.getIllustrate());
                complaintFollowDTO.setComplaintInfoId(id);
                complaintFollowDTO.setDealerNotPublish(false);
                complaintFollowDTO.setCcmNotPublish(true);
                complaintFollowDTO.setFollowTime(new Date());
                complaintFollowDTO.setFollower("4001");
                complaintFollowDTO.setFollowerName("坐席主管");
                complaintFollowService.insert(complaintFollowDTO);
                complaintInfoDTO=new ComplaintInfoDTO();
                complaintInfoDTO.setWorkOrderStatus(82451002);
                complaintInfoService.update(id,complaintInfoDTO);
            }
        }else {
            SaleComplaintInfoDTO saleComplaintInfoDTO=new SaleComplaintInfoDTO();
            saleComplaintInfoDTO.setComplaintId(acceptNewComplaintDTO.getComplaintId());
            saleComplaintInfoDTO.setWorkOrderNature(83611001);
            switch (acceptNewComplaintDTO.getType()){
                case "车辆配置相关":
                    saleComplaintInfoDTO.setType(10);
                    break;
                case "销售人员专业水准":
                    saleComplaintInfoDTO.setType(11);
                    break;
                case "交车时间":
                    saleComplaintInfoDTO.setType(12);
                    break;
                case "手续办理":
                    saleComplaintInfoDTO.setType(13);
                    break;
                case "交车时状况":
                    saleComplaintInfoDTO.setType(14);
                    break;
                case "客户退订":
                    saleComplaintInfoDTO.setType(15);
                    break;
                case "销售价格":
                    saleComplaintInfoDTO.setType(16);
                    break;
                case "大客户相关":
                    saleComplaintInfoDTO.setType(17);
                    break;
                case "二手车相关":
                    saleComplaintInfoDTO.setType(18);
                    break;
                case "促销政策或活动":
                    saleComplaintInfoDTO.setType(19);
                    break;
                case "金融相关":
                    saleComplaintInfoDTO.setType(20);
                    break;
                case "保险相关":
                    saleComplaintInfoDTO.setType(21);
                    break;
                case "上牌相关":
                    saleComplaintInfoDTO.setType(22);
                    break;
                case "捆绑销售":
                    saleComplaintInfoDTO.setType(23);
                    break;
                case "销售承若兑现":
                    saleComplaintInfoDTO.setType(24);
                    break;
                case "销售服务":
                    saleComplaintInfoDTO.setType(25);
                    break;
                case "其他":
                    saleComplaintInfoDTO.setType(26);
                    break;
            }
            saleComplaintInfoDTO.setWorkOrderClassification(getWorkOrderClassification(acceptNewComplaintDTO.getWorkOrderClassification()));
            saleComplaintInfoDTO.setSource(getSource(acceptNewComplaintDTO.getSource()));
            //工单状态设为未跟进(默认)
            saleComplaintInfoDTO.setWorkOrderStatus(82451001);
            //未结案
            saleComplaintInfoDTO.setIsCloseCase(10041002);
            //400下发
            saleComplaintInfoDTO.setDataSources(82101002);
            saleComplaintInfoDTO.setServiceCommitment(getServiceCommitment(acceptNewComplaintDTO.getServiceCommitment()));
            saleComplaintInfoDTO.setName(acceptNewComplaintDTO.getName());
            saleComplaintInfoDTO.setCallName(acceptNewComplaintDTO.getCallName());
            saleComplaintInfoDTO.setCallTime(acceptNewComplaintDTO.getCallTime());
            saleComplaintInfoDTO.setCallTel(acceptNewComplaintDTO.getCallTel());
            saleComplaintInfoDTO.setVin(acceptNewComplaintDTO.getVin());
            saleComplaintInfoDTO.setLicensePlateNum(acceptNewComplaintDTO.getLicensePlateNum());
            saleComplaintInfoDTO.setBuyTime(acceptNewComplaintDTO.getBuyTime());
            //车型
            modelNameDTO modelNameDTO=new modelNameDTO();
            modelNameDTO.setModelName(acceptNewComplaintDTO.getModel());
            List<GetModelNameDTO> list= commonServiceImpl.getModelName(modelNameDTO);
            saleComplaintInfoDTO.setModel(String.valueOf(list.get(0).getId()));
            saleComplaintInfoDTO.setModelYear(acceptNewComplaintDTO.getModelYear());
            saleComplaintInfoDTO.setBuyDealerCode(acceptNewComplaintDTO.getBuyDealerCode());
            CompanyDetailByCodeDTO companyInfo = complaintDealerCcmRefService.getCompanyInfo(acceptNewComplaintDTO.getDealerCode());
            saleComplaintInfoDTO.setRegion(companyInfo.getAfterBigAreaName());
            SelectSmallManagerDTO selectSmallManagerDTO = new SelectSmallManagerDTO();
            selectSmallManagerDTO.setOrgId(companyInfo.getAfterSmallAreaId().intValue());
            List<String> roleCode = new ArrayList<>();
            roleCode.add("XSQYJL");
            selectSmallManagerDTO.setRoleCode(roleCode);
            List<SmallManagerDataDTO> smallManagerList = complaintDealerCcmRefService.getSmallManager(selectSmallManagerDTO);
            saleComplaintInfoDTO.setBloc(companyInfo.getGroupCompanyName());
            saleComplaintInfoDTO.setRegionId(companyInfo.getAfterBigAreaId());
            saleComplaintInfoDTO.setRegionManagerId(companyInfo.getAfterSmallAreaId());
            saleComplaintInfoDTO.setDealerName(companyInfo.getCompanyNameCn());
            saleComplaintInfoDTO.setBlocId(Long.valueOf(companyInfo.getGroupCompanyId()));
            CompanyDetailByCodeDTO companyInfoBuy = complaintDealerCcmRefService.getCompanyInfo(acceptNewComplaintDTO.getBuyDealerCode());
            saleComplaintInfoDTO.setBuyRegion(companyInfoBuy.getAfterBigAreaName());
            selectSmallManagerDTO = new SelectSmallManagerDTO();
            selectSmallManagerDTO.setOrgId(companyInfoBuy.getAfterSmallAreaId().intValue());
            selectSmallManagerDTO.setRoleCode(roleCode);
            smallManagerList = complaintDealerCcmRefService.getSmallManager(selectSmallManagerDTO);
            if (smallManagerList.size() != 0) {
                saleComplaintInfoDTO.setBuyRegionManager(smallManagerList.get(0).getEmployeeName());
            }
            saleComplaintInfoDTO.setBuyBloc(companyInfoBuy.getGroupCompanyName());
            saleComplaintInfoDTO.setBuyRegionId(companyInfoBuy.getAfterBigAreaId());
            saleComplaintInfoDTO.setBuyRegionManagerId(companyInfoBuy.getAfterSmallAreaId());
            saleComplaintInfoDTO.setWorkOrderClassification(83621001);
            if (smallManagerList.size() != 0) {
                saleComplaintInfoDTO.setRegionManager(smallManagerList.get(0).getEmployeeName());
            }
            if (smallManagerList.size() != 0) {
                saleComplaintInfoDTO.setRegionManager(smallManagerList.get(0).getEmployeeName());
            }
            saleComplaintInfoDTO.setRegion(companyInfo.getAfterBigAreaName());
            saleComplaintInfoDTO.setBuyDealerName(acceptNewComplaintDTO.getBuyDealerName());
            saleComplaintInfoDTO.setDealerCode(acceptNewComplaintDTO.getDealerCode());
            saleComplaintInfoDTO.setDealerName(acceptNewComplaintDTO.getDealerName());
            saleComplaintInfoDTO.setMileage(acceptNewComplaintDTO.getMileage());
            saleComplaintInfoDTO.setReplyName(acceptNewComplaintDTO.getReplyName());
            saleComplaintInfoDTO.setReplyTel(acceptNewComplaintDTO.getReplyTel());
            saleComplaintInfoDTO.setReplyTel2(acceptNewComplaintDTO.getReplyTel2());
            saleComplaintInfoDTO.setSubject(acceptNewComplaintDTO.getSubject());
            saleComplaintInfoDTO.setProblem(acceptNewComplaintDTO.getProblem());
            saleComplaintInfoDTO.setHopeReplyTime(acceptNewComplaintDTO.getHopeReplyTime());
            if(acceptNewComplaintDTO.getCategory1()!=null){
                saleComplaintInfoDTO.setCategory1(getCategory1(acceptNewComplaintDTO.getCategory1()));
            }
            if(acceptNewComplaintDTO.getCategory2()!=null){
                saleComplaintInfoDTO.setCategory2(getCategory2(acceptNewComplaintDTO.getCategory2()));
            }
            if(acceptNewComplaintDTO.getCategory3()!=null){
                saleComplaintInfoDTO.setCategory3(getCategory3(acceptNewComplaintDTO.getCategory3()));
            }
            if(acceptNewComplaintDTO.getCcPart()!=null){
                saleComplaintInfoDTO.setCcPart(getCcPart(acceptNewComplaintDTO.getCcPart()));
            }
            if(acceptNewComplaintDTO.getCcSubdivisionPart()!=null){
                saleComplaintInfoDTO.setCcSubdivisionPart(getCcSubdivisionPart(acceptNewComplaintDTO.getCcSubdivisionPart()));
            }
            if(acceptNewComplaintDTO.getCcProblem()!=null){
                saleComplaintInfoDTO.setCcProblem(getCcProblem(acceptNewComplaintDTO.getCcProblem()));
            }
            if(acceptNewComplaintDTO.getCcRequirement()!=null){
                saleComplaintInfoDTO.setCcRequirement(getCcRequirement(acceptNewComplaintDTO.getCcRequirement()));
            }
            saleComplaintInfoDTO.setImportanceLevel(getImportanceLevel(acceptNewComplaintDTO.getImportanceLevel()));
            saleComplaintInfoDTO.setReport(true);
            saleComplaintInfoService.insert(saleComplaintInfoDTO);
            if (acceptNewComplaintDTO.getIllustrate()!=null){
                //跟进信息赋值
                List<SaleComplaintInfoPO> queryid = saleComplaintInfoMapper.queryid(saleComplaintInfoDTO);
                long id = queryid.get(0).getId();
                SaleComplaintFollowDTO saleComplaintFollowDTO = new SaleComplaintFollowDTO();
                saleComplaintFollowDTO.setFollowContent(acceptNewComplaintDTO.getIllustrate());
                saleComplaintFollowDTO.setComplaintInfoId(id);
                saleComplaintFollowDTO.setDealerNotPublish(false);
                saleComplaintFollowDTO.setCcmNotPublish(true);
                saleComplaintFollowDTO.setFollowTime(new Date());
                saleComplaintFollowDTO.setFollower("4001");
                saleComplaintFollowDTO.setFollowerName("坐席主管");
                saleComplaintFollowService.insert(saleComplaintFollowDTO);
                saleComplaintInfoDTO=new SaleComplaintInfoDTO();
                saleComplaintInfoDTO.setWorkOrderStatus(82451002);
                saleComplaintInfoService.update(id,saleComplaintInfoDTO);
            }

        }

        return row;
        }
    /**
     * 工单类型赋值
     */
    public  int getWorkOrderClassification(String workOrderNatureData){
        int workOrderNature=0;
        switch (workOrderNatureData){
            case "投诉":
                workOrderNature=83621001;
                break;
            case "协助":
                workOrderNature=83621002;
                break;
            case "咨询":
                workOrderNature=83621003;
                break;
        }

        return  workOrderNature;
    }
    /**
     * 工单类型赋值
     */
    public  int getSource(String SourceData){
        int source=0;
        switch (SourceData){
            case "400热线":
                source=81971001;
                break;
            case "邮件投诉":
                source=81971002;
                break;
            case "官网投诉":
                source=81971007;
                break;
            case "售后CEM":
                source=81971008;
                break;
            case "媒体公关":
                source=81971009;
                break;
            case "媒体网站":
                source=81971010;
                break;
            case "沃世界":
                source=81971011;
                break;
            case "工商质监":
                source=81971019;
                break;
            case "微信调研":
                source=81971004;
                break;
            case "CVS":
                source=81971013;
                break;
            case "不满意案件":
                source=81971014;
                break;
            case "DPAC":
                source=81971015;
                break;
            case "经销商报备":
                source=81971016;
                break;
            case "CCM预警":
                source=81971017;
                break;
            case "经销商协助处理":
                source=81971018;
                break;

        }

        return  source;
    }
    /**
     * 工单类型赋值
     */
    public  int getServiceCommitment(String serviceCommitmentData){
        int serviceCommitment=0;
        switch (serviceCommitmentData){
            case "1小时保养":
                serviceCommitment=83631001;
                break;
            case "超长时营业":
                serviceCommitment=83631002;
                break;
            case "尊享代步车":
                serviceCommitment=83631003;
                break;
            case "免费取送车":
                serviceCommitment=83631004;
                break;
            case "零件终身保":
                serviceCommitment=83631005;
                break;
        }

        return  serviceCommitment;
    }
    /**
     * 投诉单类别一级层赋值
     */
    public  String getCategory1(String Category1Data){
        String Category1="";
        switch (Category1Data){
            case "媒体公关":
                Category1="82251001";
                break;
            case "售后案件":
                Category1="82251002";
                break;
            case "售前案件":
                Category1="82251003";
                break;
            case "IB投诉":
                Category1="82251004";
                break;
        }

        return  Category1;
    }
    /**
     * 投诉单类别二级层赋值
     */
    public  String getCategory2(String Category2Data){
        String Category2="";
        switch (Category2Data){
            case "媒体公关售后案件":
                Category2="82261001";
                break;
            case "媒体公关售前案件":
                Category2="82261002";
                break;
            case "DPAC":
                Category2="82261003";
                break;
            case "媒体网站":
                Category2="82261004";
                break;
            case "售后案件-售后市场活动":
                Category2="82261005";
                break;
            case "售后案件-车主俱乐部相关":
                Category2="82261006";
                break;
            case "售后案件-附件":
                Category2="82261007";
                break;
            case "售后案件-配件":
                Category2="82261008";
                break;
            case "售后案件-技术":
                Category2="82261010";
                break;
            case "售后案件-保修":
                Category2="82261009";
                break;
            case "售后案件-区域管理":
                Category2="82261011";
                break;
            case "沃世界":
                Category2="82261012";
                break;
            case "售前案件":
                Category2="82261013";
                break;
            case "IB投诉":
                Category2="82261014";
                break;

        }

        return  Category2;
    }
    /**
     * 投诉单类别二级层赋值
     */
    public  String getCategory3(String Category3Data){
        String Category3="";
        switch (Category3Data){
            case "媒体来电反馈客户售后投诉":
                Category3="82271001";
                break;
            case "媒体来电反馈客户销售投诉":
                Category3="82271002";
                break;
            case "DPAC售后案件":
                Category3="82271003";
                break;
            case "媒体网站售后案件":
                Category3="82271004";
                break;
            case "售后案件-售后市场活动":
                Category3="82271005";
                break;
            case "售后案件-车主俱乐部相关":
                Category3="82271006";
                break;
            case "附件供应（到货时间不能满足客户需求）/价格/质量":
                Category3="82271007";
                break;
            case "配件信息（件号/替代件号等问题）":
                Category3="82271008";
                break;
            case "配件供应（到货时间不能满足客户需求）":
                Category3="82271009";
                break;
            case "售后-技术-车身及内外饰":
                Category3="82271010";
                break;
            case "售后-技术-转向系统":
                Category3="82271011";
                break;
            case "售后-技术-传动系统":
                Category3="82271012";
                break;
            case "售后-技术-动力总成":
                Category3="82271013";
                break;
            case "售后-技术-底盘":
                Category3="82271014";
                break;
            case "售后-技术-电器系统":
                Category3="82271015";
                break;
            case "保修争议（客户在保内，经销商不给予保修，客户不认可不满）":
                Category3="82271016";
                break;
            case "保修范围（客户已过保，要求保修）":
                Category3="82271017";
                break;
            case "经销商管理其他":
                Category3="82271018";
                break;
            case "经销商的服务收费":
                Category3="82271019";
                break;
            case "经销商的服务态度":
                Category3="82271020";
                break;
            case "沃世界平台售后案件":
                Category3="82271021";
                break;
            case "沃世界售后案件":
                Category3="82271022";
                break;
            case "直销车":
                Category3="82271023";
                break;
            case "服务费用":
                Category3="82271024";
                break;
            case "车辆资源":
                Category3="82271025";
                break;
            case "沃世界微信绑定问题":
                Category3="82271026";
                break;
            case "合格证问题（重大）":
                Category3="82271027";
                break;
            case "欺诈销售（重大）":
                Category3="82271028";
                break;
            case "合格证关单":
                Category3="82271029";
                break;
            case "二手车":
                Category3="82271030";
                break;
            case "大客户":
                Category3="82271031";
                break;
            case "上牌问题":
                Category3="82271032";
                break;
            case "大客户二手车":
                Category3="82271033";
                break;
            case "展厅服务":
                Category3="82271034";
                break;
            case "定（订）金问题":
                Category3="82271035";
                break;
            case "其他问题":
                Category3="82271036";
                break;
            case "订单优惠":
                Category3="82271037";
                break;
            case "交车问题":
                Category3="82271038";
                break;
            case "金融保险":
                Category3="82271039";
                break;
            case "客服人员服务态度不佳":
                Category3="82271040";
                break;
            case "客服人员专业知识不足/提供错误资讯":
                Category3="82271041";
                break;
        }

        return  Category3;
    }
    /**
     * CC部位赋值
     */
    public  String getCcPart(String ccPartData){
        List<String> ccParttList = Arrays.asList(ccPartData.split(","));
        StringBuffer ccPart = new StringBuffer();
        for (int i = 0; i < ccParttList.size(); i++) {
            if (i == ccParttList.size() - 1) {
                switch (ccParttList.get(i)){
                    case "发动机":
                        ccPart.append("81991001");
                        break;
                    case "驱动系统":
                        ccPart.append("81991002");
                        break;
                    case "底盘":
                        ccPart.append("81991003");
                        break;
                    case "转向/轮胎":
                        ccPart.append("81991004");
                        break;
                    case "车身":
                        ccPart.append("81991005");
                        break;
                    case "车饰内装":
                        ccPart.append("81991006");
                        break;
                    case "门/窗/天窗":
                        ccPart.append("81991007");
                        break;
                    case "电器":
                        ccPart.append("81991008");
                        break;
                    case "加装精品":
                        ccPart.append("81991009");
                        break;
                    case "品质其他":
                        ccPart.append("81991010");
                        break;
                    case "服务其他":
                        ccPart.append("81991011");
                        break;
                }
            } else {
                switch (ccParttList.get(i)){
                    case "发动机":
                        ccPart.append("81991001"+",");
                        break;
                    case "驱动系统":
                        ccPart.append("81991002"+",");
                        break;
                    case "底盘":
                        ccPart.append("81991003"+",");
                        break;
                    case "转向/轮胎":
                        ccPart.append("81991004"+",");
                        break;
                    case "车身":
                        ccPart.append("81991005"+",");
                        break;
                    case "车饰内装":
                        ccPart.append("81991006"+",");
                        break;
                    case "门/窗/天窗":
                        ccPart.append("81991007"+",");
                        break;
                    case "电器":
                        ccPart.append("81991008"+",");
                        break;
                    case "加装精品":
                        ccPart.append("81991009"+",");
                        break;
                    case "品质其他":
                        ccPart.append("81991010"+",");
                        break;
                    case "服务其他":
                        ccPart.append("81991011"+",");
                        break;
                }
            }
        }
        return  ccPart.toString();
    }
    /**
     * CC细分部位赋值
     */
    public  String getCcSubdivisionPart(String ccSubdivisionPartData){
        List<String> ccSubdivisionPartList = Arrays.asList(ccSubdivisionPartData.split(","));
        StringBuffer ccSubdivisionPart = new StringBuffer();
        for (int i = 0; i < ccSubdivisionPartList.size(); i++) {
            if (i == ccSubdivisionPartList.size() - 1) {
                switch (ccSubdivisionPartList.get(i)){
                    case "发动机（本机）":
                        ccSubdivisionPart.append("82001001");
                        break;
                    case "汽门系统":
                        ccSubdivisionPart.append("82001002");
                        break;
                    case "充电/起动系":
                        ccSubdivisionPart.append("82001003");
                        break;
                    case "点火系":
                        ccSubdivisionPart.append("82001004");
                        break;
                    case "润滑系":
                        ccSubdivisionPart.append("82001005");
                        break;
                    case "冷却系":
                        ccSubdivisionPart.append("82001006");
                        break;
                }
            } else {
                switch (ccSubdivisionPartList.get(i)){
                    case "发动机（本机）":
                        ccSubdivisionPart.append("82001001"+",");
                        break;
                    case "汽门系统":
                        ccSubdivisionPart.append("82001002"+",");
                        break;
                    case "充电/起动系":
                        ccSubdivisionPart.append("82001003"+",");
                        break;
                    case "点火系":
                        ccSubdivisionPart.append("82001004"+",");
                        break;
                    case "润滑系":
                        ccSubdivisionPart.append("82001005"+",");
                        break;
                    case "冷却系":
                        ccSubdivisionPart.append("82001006"+",");
                        break;
                }
            }
        }
        return  ccSubdivisionPart.toString();
    }

    /**
     * CC问题赋值
     */
    public  String getCcProblem(String ccProblemData){
        List<String> ccProblemList = Arrays.asList(ccProblemData.split(","));
        StringBuffer ccProblem = new StringBuffer();
        for (int i = 0; i < ccProblemList.size(); i++) {
            if (i == ccProblemList.size() - 1) {
                switch (ccProblemList.get(i)){
                    case "噪音":
                        ccProblem.append("82011001");
                        break;
                    case "机油消耗大":
                        ccProblem.append("82011002");
                        break;
                    case "燃油消耗大":
                        ccProblem.append("82011003");
                        break;
                    case "熄火":
                        ccProblem.append("82011004");
                        break;
                    case "异响":
                        ccProblem.append("82011005");
                        break;
                    case "捣缸":
                        ccProblem.append("82011006");
                        break;
                    case "抖动":
                        ccProblem.append("82011007");
                        break;
                    case "警告灯亮":
                        ccProblem.append("82011008");
                        break;
                    case "动力不足":
                        ccProblem.append("82011009");
                        break;
                    case "起火":
                        ccProblem.append("82011010");
                        break;
                    case "无法启动":
                        ccProblem.append("82011011");
                        break;
                    case "启动不良":
                        ccProblem.append("82011012");
                        break;
                    case "漏油":
                        ccProblem.append("82011013");
                        break;
                    case "其他":
                        ccProblem.append("82011014");
                        break;
                }
            } else {
                switch (ccProblemList.get(i)){
                    case "噪音":
                        ccProblem.append("82011001"+",");
                        break;
                    case "机油消耗大":
                        ccProblem.append("82011002"+",");
                        break;
                    case "燃油消耗大":
                        ccProblem.append("82011003"+",");
                        break;
                    case "熄火":
                        ccProblem.append("82011004"+",");
                        break;
                    case "异响":
                        ccProblem.append("82011005"+",");
                        break;
                    case "捣缸":
                        ccProblem.append("82011006"+",");
                        break;
                    case "抖动":
                        ccProblem.append("82011007"+",");
                        break;
                    case "警告灯亮":
                        ccProblem.append("82011008"+",");
                        break;
                    case "动力不足":
                        ccProblem.append("82011009"+",");
                        break;
                    case "起火":
                        ccProblem.append("82011010"+",");
                        break;
                    case "无法启动":
                        ccProblem.append("82011011"+",");
                        break;
                    case "启动不良":
                        ccProblem.append("82011012"+",");
                        break;
                    case "漏油":
                        ccProblem.append("82011013"+",");
                        break;
                    case "其他":
                        ccProblem.append("82011014"+",");
                        break;
                }
            }
        }
        return  ccProblem.toString();
    }
    /**
     * CC要求赋值
     */
    public  String getCcRequirement(String ccRequirementData){
        List<String> ccRequirementList = Arrays.asList(ccRequirementData.split(","));
        StringBuffer ccRequirement = new StringBuffer();
        for (int i = 0; i < ccRequirementList.size(); i++) {
            if (i == ccRequirementList.size() - 1) {
                switch (ccRequirementList.get(i)){
                    case "彻底修理":
                        ccRequirement.append("82021001");
                        break;
                    case "免费修理":
                        ccRequirement.append("82021002");
                        break;
                    case "优惠修理":
                        ccRequirement.append("82021003");
                        break;
                    case "退车":
                        ccRequirement.append("82021004");
                        break;
                    case "换车":
                        ccRequirement.append("82021005");
                        break;
                    case "提供代步车":
                        ccRequirement.append("82021006");
                        break;
                    case "补偿保养":
                        ccRequirement.append("82021007");
                        break;
                    case "代金券":
                        ccRequirement.append("82021008");
                        break;
                    case "现金":
                        ccRequirement.append("82021009");
                        break;
                    case "履行约定":
                        ccRequirement.append("82021010");
                        break;
                    case "道歉":
                        ccRequirement.append("82021011");
                        break;
                    case "其他赔偿":
                        ccRequirement.append("82021012");
                        break;
                    case "要求扩大维修":
                        ccRequirement.append("82021013");
                        break;
                    case "尽快提供配件":
                        ccRequirement.append("82021014");
                        break;
                    case "尽快提车（新车）":
                        ccRequirement.append("82021015");
                        break;
                    case "尽快提车（维修保养）":
                        ccRequirement.append("82021016");
                        break;
                    case "要求解释说明":
                        ccRequirement.append("82021017");
                        break;
                    case "其他要求":
                        ccRequirement.append("82021018");
                        break;
                }
            } else {
                switch (ccRequirementList.get(i)){
                    case "彻底修理":
                        ccRequirement.append("82021001"+",");
                        break;
                    case "免费修理":
                        ccRequirement.append("82021002"+",");
                        break;
                    case "优惠修理":
                        ccRequirement.append("82021003"+",");
                        break;
                    case "退车":
                        ccRequirement.append("82021004"+",");
                        break;
                    case "换车":
                        ccRequirement.append("82021005"+",");
                        break;
                    case "提供代步车":
                        ccRequirement.append("82021006"+",");
                        break;
                    case "补偿保养":
                        ccRequirement.append("82021007"+",");
                        break;
                    case "代金券":
                        ccRequirement.append("82021008"+",");
                        break;
                    case "现金":
                        ccRequirement.append("82021009"+",");
                        break;
                    case "履行约定":
                        ccRequirement.append("82021010"+",");
                        break;
                    case "道歉":
                        ccRequirement.append("82021011"+",");
                        break;
                    case "其他赔偿":
                        ccRequirement.append("82021012"+",");
                        break;
                    case "要求扩大维修":
                        ccRequirement.append("82021013"+",");
                        break;
                    case "尽快提供配件":
                        ccRequirement.append("82021014"+",");
                        break;
                    case "尽快提车（新车）":
                        ccRequirement.append("82021015"+",");
                        break;
                    case "尽快提车（维修保养）":
                        ccRequirement.append("82021016"+",");
                        break;
                    case "要求解释说明":
                        ccRequirement.append("82021017"+",");
                        break;
                    case "其他要求":
                        ccRequirement.append("82021018"+",");
                        break;
                }
            }
        }
        return  ccRequirement.toString();
    }

    /**
     * 投诉单类别一级层赋值
     */
    public  int getImportanceLevel(String importanceLevelData){
        int importanceLevel=0;
        switch (importanceLevelData){
            case "重要":
                importanceLevel=82091001;
                break;
            case "普通":
                importanceLevel=82091002;
                break;
        }

        return  importanceLevel;
    }


}
