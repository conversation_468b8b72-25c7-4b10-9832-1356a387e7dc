package com.yonyou.dmscus.customer.service.parse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yonyou.dmscus.customer.entity.po.parse.ParsePo;
import com.yonyou.dmscus.customer.entity.vo.AccidentClueVo;

import java.util.List;

public interface ParseService extends IService<ParsePo> {

    /*
    * 根据文字获取数据
    * */
    AccidentClueVo parseContent(String param);

    /*
    * 根据选择文字类型获取匹配规则
    * */
    List<Long> getTextMatch(Long parseId);
}
