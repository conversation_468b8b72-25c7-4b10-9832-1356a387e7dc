package com.yonyou.dmscus.customer.service.invitationCreate;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.invitationCreate.InviteVehicleDealerImportDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationCreate.InviteVehicleDealerTaskDTO;
import com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskImportPO;
import com.yonyou.dmscus.customer.service.common.IBaseService;

import org.springframework.web.multipart.MultipartFile;


/**
 * <p>
 * 车辆特约店自建邀约任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-29
 */
public interface InviteVehicleDealerTaskService extends IBaseService<InviteVehicleDealerTaskDTO> {


    /**
     * 店端自建邀约
     * @param inviteVehicleDealerTaskDTO
     * @return
     */
    int saveInvitationDlr(InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDTO);

    /**
     * 厂端自建邀约
     * @param inviteVehicleDealerTaskDTO
     * @return
     */
    int saveInvitationVCDC(InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDTO);

    ImportTempResult<InviteVehicleDealerTaskImportPO> importTemp(MultipartFile importFile) throws Exception;


    ImportTempResult<InviteVehicleDealerTaskImportPO> importInviteVehicleVCDCTemp(MultipartFile importFile) throws   Exception;

    void batchInsert();

    void batchVCDCInsert();
    
    IPage<InviteVehicleDealerImportDTO> selectErrorPage(Page page);
    
    IPage<InviteVehicleDealerImportDTO> selectSuccessPage(Page page);



}
