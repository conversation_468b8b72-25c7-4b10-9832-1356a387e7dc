package com.yonyou.dmscus.customer.service.impl.invitationautocreate;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.redis.RedisClient;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.constants.lnvitation.InviteTypeEnum;
import com.yonyou.dmscus.customer.constants.lnvitation.RecordTypeEnum;
import com.yonyou.dmscus.customer.constants.lnvitation.VerifyTypeEnum;
import com.yonyou.dmscus.customer.dao.common.VehicleMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordDetailMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dao.invitationautocreate.DailyMileageLogMapper;
import com.yonyou.dmscus.customer.dao.invitationautocreate.InviteVehicleTaskMapper;
import com.yonyou.dmscus.customer.dao.inviteInsurance.DealerTransformationRecordMapper;
import com.yonyou.dmscus.customer.dao.inviteRule.InvitePartItemRuleChangedRecordMapper;
import com.yonyou.dmscus.customer.dao.inviteRule.InvitePartItemRuleMapper;
import com.yonyou.dmscus.customer.dao.inviteRule.InviteRuleChangedRecordMapper;
import com.yonyou.dmscus.customer.dao.inviteRule.InviteRuleMapper;
import com.yonyou.dmscus.customer.dao.inviteSaAllocateRule.InviteSaAllocateRuleDetailMapper;
import com.yonyou.dmscus.customer.dao.inviteSaAllocateRule.InviteSaAllocateRuleMapper;
import com.yonyou.dmscus.customer.dao.voc.VocInviteVehicleTaskRecordMapper;
import com.yonyou.dmscus.customer.dto.*;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.UserInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationautocreate.InviteVehicleTaskDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteRule.InviteDuplicateRemovalRuleDTO;
import com.yonyou.dmscus.customer.entity.dto.saSllocateDlr.SaSllocateDlrDto;
import com.yonyou.dmscus.customer.entity.po.common.VehiclePO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordDetailPO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.DailyMileageLogPO;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InvitePartItemRuleChangedRecordPO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InvitePartItemRulePO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRuleChangedRecordPO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRulePO;
import com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule.InviteSaAllocateRuleDetailPO;
import com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule.InviteSaAllocateRulePO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.LossDataRecordPo;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocInviteVehicleTaskRecordPo;
import com.yonyou.dmscus.customer.feign.MidEndCustomerCenterClient;
import com.yonyou.dmscus.customer.feign.RepairCommonClient;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.feign.WhitelistQueryService;
import com.yonyou.dmscus.customer.service.common.businessPlatform.dto.VehicleOwnerDetailByIdListDTO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.dto.VehicleOwnerSelectByIdListDTO;
import com.yonyou.dmscus.customer.service.httplog.HttpLogAiService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.invitationautocreate.InviteVehicleTaskService;
import com.yonyou.dmscus.customer.service.invitationautocreate.VocManageService;
import com.yonyou.dmscus.customer.service.inviteRule.InviteDuplicateRemovalRuleService;
import com.yonyou.dmscus.customer.service.inviteRule.InviteRuleService;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.LossDataRecordService;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocFunctionalStatusRecordService;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocInviteVehicleTaskRecordService;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocWarningDataRecordService;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.dmscus.customer.util.common.Utility;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * 车辆邀约任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-07
 */
@Service
@EnableAsync
@RefreshScope
public class InviteVehicleTaskServiceImpl implements InviteVehicleTaskService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteVehicleTaskMapper inviteVehicleTaskMapper;
    @Resource
    InviteVehicleRecordMapper inviteVehicleRecordMapper;
    @Resource
    InviteVehicleRecordDetailMapper inviteVehicleRecordDetailMapper;
    @Resource
    VehicleMapper vehicleMapper;
    @Resource
    InvitePartItemRuleMapper invitePartItemRuleMapper;
    @Resource
    InviteSaAllocateRuleMapper inviteSaAllocateRuleMapper;
    @Resource
    InviteSaAllocateRuleDetailMapper inviteSaAllocateRuleDetailMapper;
    @Resource
    InviteRuleMapper inviteRuleMapper;
    @Autowired
    InviteDuplicateRemovalRuleService inviteDuplicateRemovalRuleService;
    @Autowired
    ReportCommonClient reportCommonClient;
    @Autowired
    InviteRuleService inviteRuleService;
    @Autowired
    InviteVehicleRecordService inviteVehicleRecordService;
    @Resource
    DailyMileageLogMapper dailyMileageLogMapper;
    @Autowired
    VocManageService vocManageService;
    @Autowired
    MidEndCustomerCenterClient midEndCustomerCenterClient;

    @Autowired
    RepairCommonClient repairCommonClient;
    @Resource
    InviteRuleChangedRecordMapper inviteRuleChangedRecordMapper;
    @Resource
    InvitePartItemRuleChangedRecordMapper invitePartItemRuleChangedRecordMapper;

    @Resource
    private DealerTransformationRecordMapper dealerTransformationRecordMapper;
    @Resource
    private VocInviteVehicleTaskRecordMapper vocInviteVehicleTaskRecordMapper;

    @Autowired
    private HttpLogAiService httpLogAiService;

    @Autowired
    VocWarningDataRecordService warningDataRecordService;

    @Autowired
    VocFunctionalStatusRecordService   vocFunctionalStatusRecordService;

    @Autowired
    LossDataRecordService  lossDataRecordService;
    @Autowired
    private VocInviteVehicleTaskRecordService taskRecordService;
    @Value("${voc.task.firstInvite:-1}")
    private Integer firstInvite;
    @Value("${voc.task.twoInvite:-1}")
    private Integer twoInvite;
    @Value("${voc.task.lossInvite:-1}")
    private Integer lossInvite;
    @Autowired
    WhitelistQueryService whitelistQueryService;
    @Autowired
    RedisClient redisClient;

    @Value("${maintain.mileage:-1000}")
    private double mileageMax;

    @Value("${maintain.month:-1}")
    private int monthMax;

    /**VOC job开关*/
    @Value("${voc.job.switch:1}")
    private int vocSwitch;
    /**
     * 分页查询对应数据
     *
     * @param page                 分页对象
     * @param inviteVehicleTaskDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto
            *       .
            *       InviteVehicleTaskDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<InviteVehicleTaskDTO> selectPageBysql(Page page, InviteVehicleTaskDTO inviteVehicleTaskDTO) {
        if (inviteVehicleTaskDTO == null) {
            inviteVehicleTaskDTO = new InviteVehicleTaskDTO();
        }
        InviteVehicleTaskPO inviteVehicleTaskPO = inviteVehicleTaskDTO.transDtoToPo(InviteVehicleTaskPO.class);

        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.selectPageBySql(page, inviteVehicleTaskPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteVehicleTaskDTO> result = list.stream().map(m -> m.transPoToDto(InviteVehicleTaskDTO.class))
                    .collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param inviteVehicleTaskDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.InviteVehicleTaskDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<InviteVehicleTaskDTO> selectListBySql(InviteVehicleTaskDTO inviteVehicleTaskDTO) {
        if (inviteVehicleTaskDTO == null) {
            inviteVehicleTaskDTO = new InviteVehicleTaskDTO();
        }
        InviteVehicleTaskPO inviteVehicleTaskPO = inviteVehicleTaskDTO.transDtoToPo(InviteVehicleTaskPO.class);
        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.selectListBySql(inviteVehicleTaskPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteVehicleTaskDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.InviteVehicleTaskDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public InviteVehicleTaskDTO getById(Long id) {
        InviteVehicleTaskPO inviteVehicleTaskPO = inviteVehicleTaskMapper.selectById(id);
        if (inviteVehicleTaskPO != null) {
            return inviteVehicleTaskPO.transPoToDto(InviteVehicleTaskDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param inviteVehicleTaskDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(InviteVehicleTaskDTO inviteVehicleTaskDTO) {
        //对对象进行赋值操作
        InviteVehicleTaskPO inviteVehicleTaskPO = inviteVehicleTaskDTO.transDtoToPo(InviteVehicleTaskPO.class);
        //执行插入
        int row = inviteVehicleTaskMapper.insert(inviteVehicleTaskPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                   主键ID
     * @param inviteVehicleTaskDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, InviteVehicleTaskDTO inviteVehicleTaskDTO) {
        InviteVehicleTaskPO inviteVehicleTaskPO = inviteVehicleTaskMapper.selectById(id);
        //对对象进行赋值操作
        inviteVehicleTaskDTO.transDtoToPo(inviteVehicleTaskPO);
        //执行更新
        int row = inviteVehicleTaskMapper.updateById(inviteVehicleTaskPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = inviteVehicleTaskMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = inviteVehicleTaskMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 创建邀约任务
     *
     * @param createDate
     * @return
     */
    @Override
    public int inviteAutoCreateTask1(String createDate) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        //新车销售
        this.createTaskByNewCarSales(createDate);
        //保险任务创建
        //this.createInsuranceTask(createDate);
        //保修提醒
        this.createGuaranteeTask(createDate);

        return 1;
    }


    /**
     * 创建邀约任务
     *
     * @param createDate
     * @return
     */
    @Override
    public int inviteAutoCreateTask2(String createDate) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        //工单
        this.createTaskByRepairOrder(createDate);
        //关闭保修线索
        this.closeGuarantee(createDate);
        //易损件-轮胎
        this.createTyreTask(createDate);
        return 1;
    }

    /**
     * 创建邀约任务
     *
     * @param createDate
     * @return
     */
    @Override
    public int inviteAutoCreateTaskCompensate(String createDate, String vin, String dealerCode) {
        this.createTaskByRepairOrder(createDate, vin, dealerCode);
        return 1;
    }

    /**
     * 关闭保修线索
     *
     * @param createDate
     */
    @Override
    public int closeGuarantee(String createDate) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }

        List<InviteVehicleRecordPO> list = this.getWaitCloseGuarantee(createDate);
        for (InviteVehicleRecordPO po : list) {
            po.setOrderStatus(CommonConstants.ORDER_STATUS_I);
            po.setOrderFinishDate(new Date());
            po.setUpdatedAt(new Date());
            inviteVehicleRecordMapper.updateById(po);
            this.mergeInvite(po);
        }
        return 1;
    }

    /**
     * @param createDate
     * @return
     */
    private List<InviteVehicleRecordPO> getWaitCloseGuarantee(String createDate) {
        return inviteVehicleRecordService.getWaitCloseGuaranteeRecord(createDate);
    }


    /**
     * @param vin
     * @return
     */
    private List<InviteVehicleRecordPO> getWaitCloseGuaranteeByVin(String vin) {
        return inviteVehicleRecordService.getWaitCloseGuaranteeRecordByVin(vin);
    }

    @Override
    public int inviteAutoCreateTask3(String createDate, Integer days) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        logger.info("开始工单查询工单，beginDate{},days{}", createDate, days);
        //查询1天内的工单
        List<VehicleOwnerVO> list = reportCommonClient.queryRegularMaintain(createDate, days);
        logger.info(CommonConstants.LOG_S20, list.size());
        logger.info("开始查询有效的易损件规则,createDate{}", createDate);
        //查询有效的易损件规则
        InvitePartItemRulePO param = new InvitePartItemRulePO();
        //有效的
        param.setIsUse(1);
        List<InvitePartItemRulePO> rules = invitePartItemRuleMapper.selectListBySql(param);
        logger.info("查询结果,rulesSize{}", rules.size());
        for (VehicleOwnerVO vo : list) {
            logger.info("开始工单触发创建邀约任务,vin{},roNo{}", vo.getVin(), vo.getRoNo());
            this.createRegularMaintainTask(vo);
            this.createVulnerableTask(vo, rules);
            this.createCustomerLossTask(vo);

        }

        return 1;
    }

    @Override
    public int initInviteInfo(String createDate, Integer days) {
        logger.info("开始邀约数据初始化，createDate{},days{}", createDate, days);
        Calendar c = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            c.setTime(sdf.parse(createDate));
        } catch (ParseException e) {
            logger.info("时间异常{}", createDate);
        }
        for (int i = 0; i < days; i++) {
            logger.info("当前执行时间，date{}", sdf.format(c.getTime()));
            try {
                //新车销售
                this.createTaskByNewCarSales(sdf.format(c.getTime()));
                //保险任务创建
                this.createInsuranceTask(sdf.format(c.getTime()));
                //计算日均里程
                this.computeDailyAverageMileageForInit(sdf.format(c.getTime()));
                //工单生成定保,客户流失
                this.createTaskByRepairOrderForInit(sdf.format(c.getTime()));
                //根据邀约任务创建线索
                this.createInviteByTaskForInit(sdf.format(c.getTime()));
                //超时关闭
                this.closeInvite(sdf.format(c.getTime()));
                dailyMileageLogMapper.SetErrorlog("initInviteInfo", sdf.format(c.getTime()) + "执行完成", null, null);
                c.add(Calendar.DATE, 1);
            } catch (Exception e) {
                logger.info("错误终止时间，date{}", sdf.format(c.getTime()));
                logger.info("errorMessage{}", e.toString());
                dailyMileageLogMapper.SetErrorlog("initInviteInfo", "错误终止时间，date" + sdf.format(c.getTime()) + "," +
                        "errorMessage" + e.getMessage(), e.getStackTrace()[0].toString() + e.getStackTrace()[1] + e
                        .getStackTrace()[2] + e.getStackTrace()[3] + e.getStackTrace()[4], null);
                e.printStackTrace();
                return 0;
            }
        }
        logger.info("结束邀约数据初始化，date{}", sdf.format(c.getTime()));
        return 1;
    }


    /**
     * 新车销售
     *
     * @param createDate
     */
    private void createTaskByNewCarSales(String createDate) {
        logger.info("createTaskByNewCarSales,开始查询新车销售且不存在首保任务,createDate{}", createDate);
        List<VehicleOwnerVO> list = reportCommonClient.queryFirstMaintain(createDate);
        //查询vin对应的车主信息
        this.batchQueryUserInfo(list);

        logger.info(CommonConstants.LOG_S20, list.size());
        logger.info("createTaskByNewCarSales,开始查询有效的易损件规则,createDate{}", createDate);
        InvitePartItemRulePO param = new InvitePartItemRulePO();
        //有效的
        param.setIsUse(1);
        List<InvitePartItemRulePO> rules = invitePartItemRuleMapper.selectListBySql(param);
        logger.info("createTaskByNewCarSales,查询结果,rulesSize{}", rules.size());
        for (VehicleOwnerVO vo : list) {
            this.createFirstMaintainTaskByNewCarSales(vo);
            //this.createRegularMaintainTaskByNewCarSales(vo);
            this.createCustomerLossTaskByNewCarSales(vo);
            //易损易耗迁移至原厂服务提醒
            //this.createVulnerableTaskByNewCarSales(vo, rules);
        }
        logger.info("createTaskByNewCarSales,end");
    }

    /**
     * 新车销售创建易损易耗件
     *
     * @param vo
     */
    private void createVulnerableTaskByNewCarSales(VehicleOwnerVO vo, List<InvitePartItemRulePO> rules) {
        for (InvitePartItemRulePO rule : rules) {
            this.createTaskByNewCarSales(vo, rule);
        }
    }


    /**
     * 工单创建邀约任务
     *
     * @param createDate
     */
    @Transactional(rollbackFor = Exception.class)
    public void createTaskByRepairOrder(String createDate) {
        logger.info("createTaskByRepairOrder,start");
        //设置将父线程的HttpServletRequest对象设置共享
        RequestContextHolder.setRequestAttributes(RequestContextHolder.getRequestAttributes(), true);
        logger.info("createTaskByRepairOrder,开始工单查询工单，{}", createDate);
        //查询1天内的工单
        List<VehicleOwnerVO> list = reportCommonClient.queryRegularMaintain(createDate, 1);
        //查询vin对应的车主信息
        this.batchQueryUserInfo(list);

        logger.info(CommonConstants.LOG_S20, list.size());
        logger.info("createTaskByRepairOrder,开始查询有效的易损件规则,createDate{}", createDate);
        //查询有效的易损件规则
        InvitePartItemRulePO param = new InvitePartItemRulePO();
        //有效的
        param.setIsUse(1);
        List<InvitePartItemRulePO> rules = invitePartItemRuleMapper.selectListBySql(param);
        logger.info("createTaskByRepairOrder,查询结果,rulesSize{}", rules.size());

        try {
            // 线程数量
            int threadSize = 10;
            ExecutorService service = Executors.newFixedThreadPool(threadSize);
            // 闭锁，等待所有任务执行完成
            final CountDownLatch latch = new CountDownLatch(list.size());
            for (VehicleOwnerVO vo : list) {
                Runnable task = new Runnable() {
                    public void run() {
                        try {
                            logger.info("createTaskByRepairOrder,开始工单触发创建邀约任务,vin{},roNo{}", vo.getVin(), vo.getRoNo());
                            createRegularMaintainTask(vo);
                            //易损易耗迁移至原厂服务提醒
                            //createVulnerableTask(vo, rules);
                            createCustomerLossTask(vo);
                            closeVocAccident(vo, createDate);
                            //增加工单进厂关闭保修到期线索
                            closeGuaranteeByRo(vo, createDate);
                        } catch (Exception e) {
                            logger.error("createTaskByRepairOrder,>>>工单创建邀约任务异常:{}", e);
                            dailyMileageLogMapper.SetErrorlog("createTaskByRepairOrder,工单创建邀约任务异常", e.getMessage(), e.getStackTrace()
                                    .toString(), vo.getVin());
                        } finally {
                            latch.countDown();
                        }
                    }
                };
                service.submit(task);
            }
            latch.await();
            service.shutdown();
        }catch (Exception e){
            dailyMileageLogMapper.SetErrorlog("createTaskByRepairOrder,工单创建邀约任务异常", e.getMessage(), e.getStackTrace()
                    .toString(),"createTaskByRepairOrder,多线程异常");
        }
        logger.info("createTaskByRepairOrder,end");
    }

    public void closeClues(String createDate) {
        logger.info("closeClues,start:{}", createDate);
        //查询1天内的工单
        List<VehicleOwnerVO> list = reportCommonClient.queryRegularMaintain(createDate, 1);
        for (VehicleOwnerVO vo : list) {
            if (CommonConstants.REPAIR_TYPE_CODE_ACCIDENT.equals(vo.getRepairTypeCode())) {
                //VOC事故关闭
                closeVocAccident(vo, createDate);
            }else {
                //增加工单进厂关闭保修到期线索
                closeGuaranteeByRo(vo, createDate);
            }
        }
        logger.info("closeClues,end");
    }

    /**
     * 工单创建邀约任务(补偿数据)
     *
     * @param createDate
     */
    @Transactional(rollbackFor = Exception.class)
    public void createTaskByRepairOrder(String createDate, String vin, String dealerCode) {
        logger.info("createTaskByRepairOrder,createDate{},vin{},dealerCode{}", createDate, vin, dealerCode);
        //查询1天内的工单
        List<VehicleOwnerVO> list = reportCommonClient.queryRegularMaintain(createDate, 1);
        logger.info(CommonConstants.LOG_S20, list.size());
        logger.info("createTaskByRepairOrder,开始查询有效的易损件规则,createDate{}", createDate);
        //查询有效的易损件规则
        InvitePartItemRulePO param = new InvitePartItemRulePO();
        //有效的
        param.setIsUse(1);
        List<InvitePartItemRulePO> rules = invitePartItemRuleMapper.selectListBySql(param);
        logger.info("createTaskByRepairOrder查询结果,rulesSize{}", rules.size());
        String vinOrder;
        String dealerCodeOrder;
        try {
            for (VehicleOwnerVO vo : list) {
                vinOrder = vo.getVin();
                dealerCodeOrder = vo.getDealerCode();
                if (vin.equals(vinOrder) && dealerCode.equals(dealerCodeOrder)) {
                    logger.info("createTaskByRepairOrder开始工单触发创建邀约任务,vin{},roNo{}", vo.getVin(), vo.getRoNo());
                    createRegularMaintainTask(vo);
                    createCustomerLossTask(vo);
                    closeVocAccident(vo, createDate);
                    //增加工单进厂关闭保修到期线索
                    closeGuaranteeByRo(vo, createDate);
                }
            }
            logger.info("createTaskByRepairOrder,end");
        } catch (Exception e) {
            logger.error("createTaskByRepairOrder>>>工单创建邀约任务异常:{}", e);
            dailyMileageLogMapper.SetErrorlog("createTaskByRepairOrder工单创建邀约任务异常", e.getMessage(), e.getStackTrace()
                    .toString(), vin);
        }
    }

    /**
     * 工单创建邀约任务
     *
     * @param createDate
     */
    public void createTaskByRepairOrderForInit(String createDate) {
        logger.info("开始工单查询工单，{}", createDate);
        //        //查询1天内的工单
        List<VehicleOwnerVO> list = reportCommonClient.queryRegularMaintainForAverageMileage(createDate, 1);
        for (VehicleOwnerVO vo : list) {
            logger.info("开始工单触发创建邀约任务,vin{},roNo{}", vo.getVin(), vo.getRoNo());
            this.createRegularMaintainTask(vo);
            this.createCustomerLossTask(vo);

        }
    }


    /**
     * 创建易损件任务
     *
     * @param createDate
     */
    private void createVulnerableTask(VehicleOwnerVO vo, List<InvitePartItemRulePO> rules) {
        for (InvitePartItemRulePO rule : rules) {
            this.createTask(vo, rule);
        }
    }


    /**
     * 创建或者更新易损件任务
     *
     * @param vo
     * @param rule
     */
    private void createTaskByNewCarSales(VehicleOwnerVO vo, InvitePartItemRulePO rule) {
        logger.info("开始创建易损件任务{}", vo.getVin());
        //检查是否匹配规则
        if (this.checkSuitRule(vo, rule)) {
            vo.setDayInAdvance(rule.getDayInAdvance());
            vo.setRemindInterval(rule.getRemindInterval());
            vo.setCloseInterval(0);
            vo.setItemType(rule.getType());
            vo.setItemCode(rule.getCode());
            vo.setItemName(rule.getName());
            vo.setInviteType(CommonConstants.INVITE_TYPE_V);
            logger.info("开始计算易损件建议入厂时间{}", vo.getVin());
            Date adviseInDate = this.setAdviseInDate(vo, vo.getInvoiceDate(), vo.getDailyAverageMileage(), rule
                    .getMileageInterval(), rule.getDateInterval());
            logger.info("得到建议入厂时间{}", adviseInDate);
            if (adviseInDate != null) {
                vo.setAdviseInDate(adviseInDate);
                this.createInviteTask(vo, CommonConstants.UP_DAY);
            }
        }
    }

    /**
     * VOC 事故关闭
     *
     * @param vo
     */
    private void closeVocAccident(VehicleOwnerVO vo, String createDate) {
        logger.info("closeVocAccident,start");
        //如果是事故类型
        if (CommonConstants.REPAIR_TYPE_CODE_ACCIDENT.equals(vo.getRepairTypeCode())) {
            logger.info("closeVocAccident,开始查询未关闭线索，vin{}", vo.getVin());
            List<InviteVehicleRecordPO> list = this.getWaitCloseVocAccidentRecord(createDate, vo.getVin());
            logger.info(CommonConstants.LOG_S20, list.size());
            for (InviteVehicleRecordPO item : list) {
                item = inviteVehicleRecordMapper.selectById(item.getId());
                if (vo.getDealerCode().equals(item.getDealerCode())) {
                    logger.info("closeVocAccident,本店，完成,recordId{},dealerCode{}", item.getId(), item.getDealerCode());
                    //完成
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_I);
                    item.setOrderFinishDate(vo.getDeliveryDate());
                    item.setRoNo(vo.getRoNo());
                    item.setRoCreateDate(vo.getRoCreateDate());
                    item.setRepairTypeCode(vo.getRepairTypeCode());
                    item.setFinishDealerCode(vo.getDealerCode());
                    item.setOutMileage(vo.getMileage());
                } else {
                    logger.info("closeVocAccident,他店，他店进厂,recordId{},dealerCode{}", item.getId(), item.getDealerCode());
                    //他店进厂
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_IV);
                    item.setOrderFinishDate(vo.getDeliveryDate());
                    item.setRoNo(vo.getRoNo());
                    item.setRoCreateDate(vo.getRoCreateDate());
                    item.setRepairTypeCode(vo.getRepairTypeCode());
                    item.setFinishDealerCode(vo.getDealerCode());
                    item.setOutMileage(vo.getMileage());
                }
                inviteVehicleRecordMapper.updateById(item);
            }
        }
        logger.info("closeVocAccident,end");
    }

    /**
     * 任意非事故进厂 保修到期线索关闭
     *
     * @param vo
     */
    private void closeGuaranteeByRo(VehicleOwnerVO vo, String createDate) {
        logger.info("closeGuaranteeByRo,start");
        //如果不是事故类型
        if (!CommonConstants.REPAIR_TYPE_CODE_ACCIDENT.equals(vo.getRepairTypeCode())) {
            logger.info("closeGuaranteeByRo,开始查询未关闭线索，vin{}", vo.getVin());
            List<InviteVehicleRecordPO> list = this.getWaitCloseGuaranteeByVin(vo.getVin());
            logger.info(CommonConstants.LOG_S20, list.size());
            for (InviteVehicleRecordPO item : list) {
                item = inviteVehicleRecordMapper.selectById(item.getId());
                if (vo.getDealerCode().equals(item.getDealerCode())) {
                    logger.info("closeGuaranteeByRo,本店，完成,recordId{},dealerCode{}", item.getId(), item.getDealerCode());
                    //完成
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_I);
                    item.setOrderFinishDate(vo.getDeliveryDate());
                    item.setRoNo(vo.getRoNo());
                    item.setRoCreateDate(vo.getRoCreateDate());
                    item.setRepairTypeCode(vo.getRepairTypeCode());
                    item.setFinishDealerCode(vo.getDealerCode());
                    item.setOutMileage(vo.getMileage());
                } else {
                    logger.info("closeGuaranteeByRo,他店，他店进厂,recordId{},dealerCode{}", item.getId(), item.getDealerCode());
                    //他店进厂
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_IV);
                    item.setOrderFinishDate(vo.getDeliveryDate());
                    item.setRoNo(vo.getRoNo());
                    item.setRoCreateDate(vo.getRoCreateDate());
                    item.setRepairTypeCode(vo.getRepairTypeCode());
                    item.setFinishDealerCode(vo.getDealerCode());
                    item.setOutMileage(vo.getMileage());
                }
                inviteVehicleRecordMapper.updateById(item);
                this.mergeInvite(item);
            }
        }
        logger.info("closeGuaranteeByRo,end");
    }

    /**
     * 查询满足条件的voc事故线索
     *
     * @param createDate
     * @param vin
     * @return
     */
    private List<InviteVehicleRecordPO> getWaitCloseVocAccidentRecord(String createDate, String vin) {
        return inviteVehicleRecordService.getWaitCloseVocAccidentRecord(createDate, vin);
    }


    /**
     * 创建保养任务
     *
     * @param createDate
     */
    private void createRegularMaintainTask(VehicleOwnerVO vo) {
        vo.setInviteType(CommonConstants.INVITE_TYPE_II);
//        List<InviteVehicleTaskPO> rs = inviteVehicleTaskMapper.queryRegularMaintainTask(vo.getVin());
//        //有未下发的定保，更新建议进厂
//        if (rs != null && rs.size() > 0) {
//            for (InviteVehicleTaskPO task : rs) {
//                //设基准日期
//                vo.setLastMaintainDate(task.getInviteTime());
//                //删除任务
//                inviteVehicleTaskMapper.deleteById(task.getId());
//            }
//        }
        logger.info("createRegularMaintainTask,查询是否保养工单,roNo{}", vo.getRoNo());
        RepairOrderVO ro = reportCommonClient.getMaintainRepairOrderLast(vo.getDealerCode(), vo.getRoNo());
        //如果是保养工单
        if (ro != null) {
            String roDealerCode = vo.getDealerCode();
            logger.info("createRegularMaintainTask,保养工单,roNo{},roDealerCode{}", vo.getRoNo(), roDealerCode);
            //设置基准日期
            vo.setLastMaintainDate(ro.getDeliveryDate());
            logger.info("createRegularMaintainTask,保养工单基准日期,deliveryDate{}", ro.getDeliveryDate());
            vo.setOutMileage(ro.getOutMileage().intValue());
            logger.info("createRegularMaintainTask,开始查询未关闭线索，vin{}", vo.getVin());
            List<InviteVehicleRecordPO> list = this.getWaitCloseMaintainRecord(vo.getVin());
            logger.info(CommonConstants.LOG_S20, list.size());
            for (InviteVehicleRecordPO item : list) {
                item = inviteVehicleRecordMapper.selectById(item.getId());
                if (ro.getDealerCode().equals(item.getDealerCode())) {
                    logger.info("createRegularMaintainTask,本店，完成,recordId{},dealerCode{}", item.getId(), item.getDealerCode());
                    //完成
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_I);
                    item.setOrderFinishDate(ro.getDeliveryDate());
                    item.setRoNo(ro.getRoNo());
                    item.setRoCreateDate(ro.getRoCreateDate());
                    item.setRepairTypeCode(ro.getRepairTypeCode());
                    item.setFinishDealerCode(ro.getDealerCode());
                    item.setOutMileage(ro.getOutMileage());
                } else {
                    logger.info("createRegularMaintainTask,他店，他店进厂,recordId{},dealerCode{}", item.getId(), item.getDealerCode());
                    //他店进厂
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_IV);
                    item.setOrderFinishDate(ro.getDeliveryDate());
                    item.setRoNo(ro.getRoNo());
                    item.setRoCreateDate(ro.getRoCreateDate());
                    item.setRepairTypeCode(ro.getRepairTypeCode());
                    item.setFinishDealerCode(ro.getDealerCode());
                    item.setOutMileage(ro.getOutMileage());
                }
                inviteVehicleRecordMapper.updateById(item);
                logger.info("createRegularMaintainTask,开始拆分线索，重新合并线索,recordId{},InviteType{}", item.getId(), item.getInviteType());
                this.mergeInvite(item);
            }
            logger.info("createRegularMaintainTask,关闭首保未下发任务,vin{}", vo.getVin());
            //关闭首保未下发任务
            this.updateTaskCloseById(null, CommonConstants.INVITE_TYPE_I, vo.getVin());
            logger.info("createRegularMaintainTask,关闭定保未下发任务,vin{}", vo.getVin());
            //关闭定保未下发任务
            this.updateTaskCloseById(null, CommonConstants.INVITE_TYPE_II, vo.getVin());
            logger.info("createRegularMaintainTask,查询最近两次工单的经销商,vin{}", vo.getVin());
            //查询最近两次工单 的经销商
            List<RepairOrderVO> orders = reportCommonClient.getRepairOrderLast(vo.getVin());
            String dealerCode = null;
            logger.info(CommonConstants.LOG_S20, orders.size());
            for (RepairOrderVO order : orders) {
                if (dealerCode == null) {
                    dealerCode = order.getDealerCode();
                    vo.setDealerCode(order.getDealerCode());
                    logger.info("createRegularMaintainTask,经销商,dealerCode{}", dealerCode);
                } else {
                    logger.info("createRegularMaintainTask,经销商,dealerCode{}", order.getDealerCode());
                    if (!dealerCode.equals(order.getDealerCode())) {
                        logger.info("createRegularMaintainTask,经销商不同,dealerCode1{},,dealerCode2{}", dealerCode, order.getDealerCode());
                        vo.setDealerCode(order.getDealerCode());
                    } else {
                        continue;
                    }
                }
                Date adviseInDate = this.setAdviseInDate(vo, vo.getDealerCode(), vo.getLastMaintainDate(), vo
                        .getDailyAverageMileage());
                if (adviseInDate != null) {
                    vo.setAdviseInDate(adviseInDate);
                    this.createInviteTaskNew(vo, twoInvite);
                }
            }
            //重设工单门店
            vo.setDealerCode(roDealerCode);
        } else {
            logger.info("createRegularMaintainTask,非保养工单,vin{},roNo{}", vo.getVin(), vo.getRoNo());
        }
    }


    /**
     * 创建Qb任务
     *
     * @return
     */
    @Override
    public int inviteAutoCreateQb() {
        List<VehicleOwnerVO> list = reportCommonClient.queryCreateTaskForQb();
        for (VehicleOwnerVO vo : list) {
            vo.setInviteType(CommonConstants.INVITE_TYPE_VII);
            vo.setAdviseInDate(new Date());
            //计划跟进时间+1天
            vo.setDayInAdvance(-1);
            this.createInviteTask(vo, CommonConstants.UP_DAY);
            RepairOrderVO order = reportCommonClient.getRepairOrderLastOne(vo.getVin());
            if (order != null && !order.getDealerCode().equals(vo.getDealerCode())) {
                vo.setDealerCode(order.getDealerCode());
                this.createInviteTask(vo, CommonConstants.UP_DAY);
            }
        }
        return 1;
    }

    /**
     * 定保 保险丝规则
     *
     * @return
     */
    @Override
    public int fuseRule() {
        logger.info("保险丝规则,开始查询需要处理数据{}", "");
        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.queryByFuseRule();
        logger.info(CommonConstants.LOG_S20, list.size());
        //下发时间调整为前一天
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DATE, -1);

        for (InviteVehicleTaskPO po : list) {
            po.setAdviseInDate(new Date());
            po.setCreateInviteTime(c.getTime());
            inviteVehicleTaskMapper.updateById(po);
        }
        return 1;
    }


    /**
     * 关闭未下发的任务
     *
     * @param id
     */
    private void newUpdateTaskCloseById(String dealerCode, Integer inviteType, String vin) {
        inviteVehicleTaskMapper.newUpdateTaskCloseById(dealerCode, inviteType, vin);
    }
    /**
     * 关闭未下发的任务
     *
     * @param id
     */
    private void updateTaskCloseById(String dealerCode, Integer inviteType, String vin) {
        inviteVehicleTaskMapper.updateTaskCloseById(dealerCode, inviteType, vin);
    }

    /**
     * 关闭未下发的易损件任务
     *
     * @param id
     */
    private void updateTaskPartCloseById(Integer inviteType, String vin, Integer itemType, String itemCode) {
        inviteVehicleTaskMapper.updateTaskPartCloseById(vin, itemType, itemCode);
    }

    /**
     * 查询未关闭定保
     *
     * @param vin
     * @return
     */
    private List<InviteVehicleRecordPO> getWaitCloseMaintainRecord(String vin) {
        return inviteVehicleRecordService.getWaitCloseMaintainRecord(vin);
    }


    /**
     * 查询未关闭易损件
     *
     * @param vin
     * @param code
     * @return
     */
    private List<InviteVehicleRecordPO> getWaitCloseVulnerableRecord(String vin, String code, Integer type) {
        return inviteVehicleRecordService.getWaitCloseVulnerableRecord(vin, code, type);
    }


    /**
     * 创建首保任务
     *
     * @param createDate
     */
    private void createFirstMaintainTaskByNewCarSales(VehicleOwnerVO vo) {
        logger.info("开始创建首保任务{}", vo.getVin());
        //查询首保日期间隔规则
        InviteRulePO rule = inviteRuleMapper.getInvitationDlrRule(vo.getDealerCode(),
                CommonConstants.INVITE_TYPE_FIRST_GUARANTEE, CommonConstants.INVITE_RULE_I);
        if (rule == null || rule.getIsUse() == null || rule.getIsUse() == 0) {
            //不存在首保日期间隔规则，跳过
            logger.info("不存在首保日期间隔规则{}", vo.getVin());
            return;
        }
        //查询首保里程间隔规则
        InviteRulePO mileageRule = inviteRuleMapper.getInvitationDlrRule(vo.getDealerCode(),
                CommonConstants.INVITE_TYPE_FIRST_GUARANTEE, CommonConstants.INVITE_RULE_II);
        vo.setInviteType(CommonConstants.INVITE_TYPE_I);
        //基准日期
        vo.setLastMaintainDate(vo.getInvoiceDate());
        logger.info("开票日期{}", vo.getInvoiceDate());
        Calendar c = Calendar.getInstance();
        c.setTime(vo.getInvoiceDate());
        c.add(Calendar.MONTH, rule.getRuleValue());
        logger.info("规则间隔月{}", rule.getRuleValue());
        vo.setAdviseInDate(c.getTime());
        logger.info("建议进厂日期{}", vo.getAdviseInDate());
        vo.setDayInAdvance(rule.getDayInAdvance());
        vo.setInviteRule(rule.getInviteRule());
        vo.setRuleValue(rule.getRuleValue());
        vo.setRemindInterval(rule.getRemindInterval());
        vo.setCloseInterval(rule.getCloseInterval());
        if (mileageRule != null && mileageRule.getIsUse() != null && rule.getIsUse() != 0) {
            vo.setAdviseInMileage(mileageRule.getRuleValue());
        } else {
            vo.setAdviseInMileage(null);
        }
        this.createInviteTaskNew(vo, firstInvite);
    }

    /**
     * 创建定保任务,新车销售
     *
     * @param createDate
     */
    private void createRegularMaintainTaskByNewCarSales(VehicleOwnerVO vo) {
        logger.info("创建定保任务{}", vo.getVin());
        //查询定保日期间隔规则
        InviteRulePO rule = inviteRuleMapper.getInvitationDlrRule(vo.getDealerCode(), CommonConstants
                .INVITE_TYPE_FIXED_WARRANTY, CommonConstants.INVITE_RULE_III);
        if (rule == null || rule.getIsUse() == null || rule.getIsUse() == 0) {
            //不存在定保日期间隔规则，跳过
            logger.info("不存在定保日期间隔规则{}", vo.getVin());
            return;
        }
        vo.setInviteType(CommonConstants.INVITE_TYPE_II);
        //基准日期
        vo.setLastMaintainDate(vo.getInvoiceDate());
        Calendar c = Calendar.getInstance();
        logger.info("开票日期{}", vo.getInvoiceDate().toString());
        c.setTime(vo.getInvoiceDate());
        c.add(Calendar.MONTH, rule.getRuleValue());
        logger.info("间隔月{}", rule.getRuleValue().toString());
        vo.setAdviseInDate(c.getTime());
        logger.info("建议进厂{}", vo.getAdviseInDate().toString());
        vo.setDayInAdvance(rule.getDayInAdvance());
        vo.setInviteRule(rule.getInviteRule());
        vo.setRuleValue(rule.getRuleValue());
        vo.setRemindInterval(rule.getRemindInterval());
        vo.setCloseInterval(rule.getCloseInterval());
        this.createInviteTask(vo, CommonConstants.UP_DAY);
    }

    /**
     * 客户流失
     *
     * @param createDate
     */
    private void createCustomerLossTaskByNewCarSales(VehicleOwnerVO vo) {
        logger.info("开始创建客户流失任务{}", vo.getVin());
        //查询客户流失间隔规则
        InviteRulePO rule = inviteRuleMapper.getInvitationDlrRule(vo.getDealerCode(), CommonConstants
                .INVITE_TYPE_CUS_LOSS, CommonConstants.INVITE_RULE_V);
        if (rule == null || rule.getIsUse() == null || rule.getIsUse() == 0) {
            //不存在客户流失间隔规则，跳过
            logger.info("不存在客户流失间隔规则{}", vo.getVin());
            return;
        }
        vo.setInviteType(CommonConstants.INVITE_TYPE_VI);
        //基准日期
        vo.setLastMaintainDate(vo.getInvoiceDate());
        logger.info("开票日期{}", vo.getInvoiceDate());
        Calendar c = Calendar.getInstance();
        c.setTime(vo.getInvoiceDate());
        c.add(Calendar.MONTH, rule.getRuleValue());
        logger.info("规则间隔月{}", rule.getRuleValue());
        vo.setAdviseInDate(c.getTime());
        logger.info("建议进厂{}", vo.getAdviseInDate());
        vo.setDayInAdvance(rule.getDayInAdvance());
        vo.setInviteRule(rule.getInviteRule());
        vo.setRuleValue(rule.getRuleValue());
        vo.setRemindInterval(rule.getRemindInterval());
        vo.setCloseInterval(rule.getCloseInterval());
        this.createInviteTaskNew(vo, lossInvite);
    }


    /**
     * 创建轮胎任务
     *
     * @param createDate
     */
    @Override
    public int createTyreTask(String createDate) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        //每年8月1日执行
        if (!this.checkExecuteDate(createDate, "MM-dd", "09-25")) {
            logger.info("当前日期{},不执轮胎规则", createDate);
            return 1;
        }
        HashMap<String, List<VehicleOwnerVO>> map = new HashMap<>();
        //查询轮胎邀约任务范围
        List<VehicleOwnerVO> list = reportCommonClient.queryVehicleForTyre(createDate, 30000, 36);
        logger.info("开始查询轮胎邀约任务范围{},{},{}", createDate, 30000, 36);
        for (VehicleOwnerVO vo : list) {
            vo.setItemType(82131001);
            vo.setItemCode("7726");
            vo.setItemName("轮胎");
            vo.setInviteType(CommonConstants.INVITE_TYPE_V);
            vo.setRemindInterval(3);
            //vo.setDayInAdvance(0);
            logger.info("开始查询最近两张工单{}", vo.getVin());
            List<RepairOrderVO> orders = reportCommonClient.getRepairOrderLast(vo.getVin());
            String dealerCode = null;
            logger.info(CommonConstants.LOG_S20, orders.size());
            if (orders.size() == 0) {
                dealerCode = vo.getDealerCode();
                if (map.containsKey(dealerCode)) {
                    map.get(dealerCode).add(vo);
                } else {
                    List<VehicleOwnerVO> array = new ArrayList<>();
                    array.add(vo);
                    map.put(dealerCode, array);
                }
            }
            for (RepairOrderVO order : orders) {
                if (dealerCode == null) {
                    dealerCode = order.getDealerCode();
                    logger.info("第一张工单门店{}", dealerCode);
                    vo.setDealerCode(order.getDealerCode());
                    if (map.containsKey(dealerCode)) {
                        map.get(dealerCode).add(vo);
                    } else {
                        List<VehicleOwnerVO> array = new ArrayList<>();
                        array.add(vo);
                        map.put(dealerCode, array);
                    }
                } else {
                    logger.info("第二张工单门店{}", order.getDealerCode());
                    if (!dealerCode.equals(order.getDealerCode())) {
                        logger.info("工单门店不同{}", order.getDealerCode());

                        try {
                            //如果最近两次工单在不同的店，创建两次邀约任务
                            VehicleOwnerVO copy = (VehicleOwnerVO) vo.clone();
                            copy.setDealerCode(order.getDealerCode());

                            if (map.containsKey(order.getDealerCode())) {
                                map.get(order.getDealerCode()).add(copy);
                            } else {
                                List<VehicleOwnerVO> array = new ArrayList<>();
                                array.add(copy);
                                map.put(order.getDealerCode(), array);
                            }
                        } catch (CloneNotSupportedException e) {

                        }
                    }
                }
            }
        }
        logger.info("开始平均分配任务{}", map.values().size());
        for (List<VehicleOwnerVO> vehList : map.values()) {
            try {
                this.distribute(createDate, vehList);
            } catch (Exception e) {

            }
        }
        return 1;
    }


    /**
     * 创建保险到期邀约任务
     *
     * @param createDate
     */
    private void createInsuranceTask(String createDate) {
        List<VehicleOwnerVO> list = reportCommonClient.queryVehileOwnerForInsuranceTask(createDate);
        for (VehicleOwnerVO vo : list) {
            //如果不存在有效的邀约规则
            if (vo.getIsUse() == null || vo.getIsUse() == 0) {
                continue;
            }
            vo.setInviteType(CommonConstants.INVITE_TYPE_III);
            Calendar c = Calendar.getInstance();
            //基准日期
            vo.setLastMaintainDate(vo.getInvoiceDate());
            c.setTime(vo.getInvoiceDate());
            //上牌或开票日期+1年
            c.add(Calendar.YEAR, 1);
            vo.setAdviseInDate(c.getTime());
            this.createInviteTask(vo, CommonConstants.UP_DAY);
        }
    }

    /**
     * 保修任务创建
     *
     * @param createDate
     */
    private void createGuaranteeTask(String createDate) {
        List<VehicleOwnerVO> list = reportCommonClient.queryVehileOwnerForGuaranteeTask(createDate);
        for (VehicleOwnerVO vo : list) {
            //如果不存在有效的邀约规则
            if (vo.getIsUse() == null || vo.getIsUse() == 0) {
                continue;
            }
            vo.setInviteType(CommonConstants.INVITE_TYPE_IX);
            Calendar c = Calendar.getInstance();
            c.setTime(vo.getInvoiceDate());
            //基准日期
            vo.setLastMaintainDate(vo.getInvoiceDate());
            //上牌或开票日期+3年
            c.add(Calendar.YEAR, 3);
            c.add(Calendar.DATE, -1);
            c.add(Calendar.DATE, -(vo.getRuleValue()));
            vo.setAdviseInDate(c.getTime());
            this.createInviteTask(vo, CommonConstants.UP_DAY);
        }
    }


    /**
     * 客户流失
     *
     * @param createDate
     */
    private void createCustomerLossTask(VehicleOwnerVO vo) {
        logger.info("开始客户流失{}", vo.getVin());
        logger.info("查询客户流失规则，DealerCode{}", vo.getDealerCode());
        //查询客户流失间隔规则
        InviteRulePO rule = inviteRuleMapper.getInvitationDlrRule(vo.getDealerCode(), 82031004, 82041005);
        logger.info(CommonConstants.LOG_S21, rule);
        if (rule == null || rule.getIsUse() == null || rule.getIsUse() == 0) {
            //不存在客户流失间隔规则，跳过
            return;
        }
        //增加流失预警判断是否保养工单
        logger.info("查询是否保养工单，roNo{},DealerCode{}", vo.getRoNo(), vo.getDealerCode());
        RepairOrderVO ro = reportCommonClient.getMaintainRepairOrderLast(vo.getDealerCode(), vo.getRoNo());
        //如果是保养工单
        if (ro != null) {
            logger.info("保养工单，vin{}", vo.getVin());
            logger.info("查询待关闭预警线索，vin{}", vo.getVin());
            List<InviteVehicleRecordPO> list = this.getWaitCloseAlertRecord("", vo.getVin());
            logger.info("查询结果，size{}", list.size());
            for (InviteVehicleRecordPO item : list) {
                item = inviteVehicleRecordMapper.selectById(item.getId());
                //完成
                if (ro.getDealerCode().equals(item.getDealerCode())) {
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_I);
                } else {
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_IV);
                }

                item.setOrderFinishDate(vo.getDeliveryDate());
                item.setRoNo(vo.getRoNo());
                item.setRoCreateDate(vo.getRoCreateDate());
                item.setRepairTypeCode(vo.getRepairTypeCode());
                item.setFinishDealerCode(vo.getDealerCode());
                item.setOutMileage(vo.getOutMileage().doubleValue());
                inviteVehicleRecordMapper.updateById(item);
                this.mergeInvite(item);
            }
            //没有未下发的流失预警
            //logger.info("关闭未下发的流失预警，vin{},DealerCode{}", vo.getVin(), vo.getDealerCode());
            //this.updateTaskCloseById(vo.getDealerCode(), CommonConstants.INVITE_TYPE_XII, vo.getVin());

            //保养关闭所有流失线索
            logger.info("查询待关闭线索，vin{}", vo.getVin());
            List<InviteVehicleRecordPO> listAll = this.getWaitCloseLostRecord("", vo.getVin());
            logger.info("查询结果，size{}", listAll.size());
            for (InviteVehicleRecordPO item : listAll) {
                item = inviteVehicleRecordMapper.selectById(item.getId());
                //完成
                if (ro.getDealerCode().equals(item.getDealerCode())) {
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_I);
                } else {
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_IV);
                }
                item.setOrderFinishDate(vo.getDeliveryDate());
                item.setRoNo(vo.getRoNo());
                item.setRoCreateDate(vo.getRoCreateDate());
                item.setRepairTypeCode(vo.getRepairTypeCode());
                item.setFinishDealerCode(vo.getDealerCode());
                item.setOutMileage(vo.getOutMileage().doubleValue());
                inviteVehicleRecordMapper.updateById(item);
                this.mergeInvite(item);
            }
            logger.info("关闭未下发的流失任务，vin{},DealerCode{}", vo.getVin(), vo.getDealerCode());
            this.updateTaskCloseById("", CommonConstants.INVITE_TYPE_VI, vo.getVin());

            //创建任务
            vo.setInviteType(CommonConstants.INVITE_TYPE_VI);
            Calendar c = Calendar.getInstance();
            c.setTime(vo.getDeliveryDate());
            c.add(Calendar.MONTH, rule.getRuleValue());
            vo.setAdviseInDate(c.getTime());
            vo.setDayInAdvance(rule.getDayInAdvance());
            vo.setInviteRule(rule.getInviteRule());
            vo.setRuleValue(rule.getRuleValue());
            vo.setRemindInterval(rule.getRemindInterval());
            vo.setCloseInterval(rule.getCloseInterval());
            vo.setLastMaintainDate(vo.getRoCreateDate());
            this.createInviteTaskNew(vo, lossInvite);
        } /*else { //如果不是保养 只关闭本店线索
            logger.info("查询待关闭线索，vin{}", vo.getVin());
            List<InviteVehicleRecordPO> list = this.getWaitCloseLostRecord(vo.getDealerCode(), vo.getVin());
            logger.info("查询结果，size{}", list.size());
            for (InviteVehicleRecordPO item : list) {
                item = inviteVehicleRecordMapper.selectById(item.getId());
                //完成
                item.setOrderStatus(CommonConstants.ORDER_STATUS_I);
                item.setOrderFinishDate(vo.getDeliveryDate());
                item.setRoNo(vo.getRoNo());
                item.setRoCreateDate(vo.getRoCreateDate());
                item.setRepairTypeCode(vo.getRepairTypeCode());
                item.setFinishDealerCode(vo.getDealerCode());
                item.setOutMileage(vo.getOutMileage().doubleValue());
                inviteVehicleRecordMapper.updateById(item);
                this.mergeInvite(item);
            }
            logger.info("关闭未下发的流失任务，vin{},DealerCode{}", vo.getVin(), vo.getDealerCode());
            this.updateTaskCloseById(vo.getDealerCode(), CommonConstants.INVITE_TYPE_VI, vo.getVin());

        }*/
    }

    /**
     * 创建定保任务逻辑
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doMaintainTask(VehicleOwnerVO vo, RepairOrderVO ro, List<String> listCode){
        logger.info("doMaintainTask,start");
        vo.setInviteType(CommonConstants.INVITE_TYPE_II);
        String roDealerCode = vo.getDealerCode();
        logger.info("doMaintainTask,保养工单,roNo{},roDealerCode{}", vo.getRoNo(), roDealerCode);
        //设置基准日期
        vo.setLastMaintainDate(ro.getDeliveryDate());
        logger.info("doMaintainTask,保养工单基准日期,deliveryDate{}", ro.getDeliveryDate());
        vo.setOutMileage(ro.getOutMileage().intValue());
        logger.info("doMaintainTask,开始查询未关闭线索，vin{}", vo.getVin());
        List<InviteVehicleRecordPO> list = this.getWaitCloseMaintainRecord(vo.getVin());
        if(CollectionUtils.isNotEmpty(list) && CollectionUtils.isNotEmpty(listCode)){
            logger.info("doMaintainTask,,list:{}", list.size());
            for (InviteVehicleRecordPO item : list) {
                if(listCode.contains(item.getDealerCode())){
                    logger.info("doMaintainTask,listCode.contains(item.getDealerCode())");
                    continue;
                }
                item = inviteVehicleRecordMapper.selectById(item.getId());
                if (ro.getDealerCode().equals(item.getDealerCode())) {
                    logger.info("doMaintainTask,本店，完成,recordId{},dealerCode{}", item.getId(), item.getDealerCode());
                    //完成
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_I);
                    item.setOrderFinishDate(ro.getDeliveryDate());
                    item.setRoNo(ro.getRoNo());
                    item.setRoCreateDate(ro.getRoCreateDate());
                    item.setRepairTypeCode(ro.getRepairTypeCode());
                    item.setFinishDealerCode(ro.getDealerCode());
                    item.setOutMileage(ro.getOutMileage());
                } else {
                    logger.info("doMaintainTask,他店，他店进厂,recordId{},dealerCode{}", item.getId(), item.getDealerCode());
                    //他店进厂
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_IV);
                    item.setOrderFinishDate(ro.getDeliveryDate());
                    item.setRoNo(ro.getRoNo());
                    item.setRoCreateDate(ro.getRoCreateDate());
                    item.setRepairTypeCode(ro.getRepairTypeCode());
                    item.setFinishDealerCode(ro.getDealerCode());
                    item.setOutMileage(ro.getOutMileage());
                }
                inviteVehicleRecordMapper.updateById(item);
                logger.info("doMaintainTask,开始拆分线索，重新合并线索,recordId{},InviteType{}", item.getId(), item.getInviteType());
                this.mergeInvite(item);
            }
        }
        logger.info("doMaintainTask,关闭首保未下发任务,vin{}", vo.getVin());
        //关闭首保未下发任务
        this.updateTaskCloseById(null, CommonConstants.INVITE_TYPE_I, vo.getVin());
        logger.info("doMaintainTask,关闭定保未下发任务,vin{}", vo.getVin());
        //关闭定保未下发任务
        this.updateTaskCloseById(null, CommonConstants.INVITE_TYPE_II, vo.getVin());
        logger.info("doMaintainTask,查询最近两次工单的经销商,vin{}", vo.getVin());
        //查询最近两次工单 的经销商
        List<RepairOrderVO> orders = reportCommonClient.getRepairOrderLast(vo.getVin());
        String dealerCode = null;
        logger.info("doMaintainTask, orders:{}", orders.size());
        List<String> ordList = new ArrayList<>();
        for (RepairOrderVO order : orders) {
            if (dealerCode == null) {
                dealerCode = order.getDealerCode();
                vo.setDealerCode(order.getDealerCode());
                logger.info("doMaintainTask,经销商,dealerCode{}", dealerCode);
            } else {
                logger.info("doMaintainTask,经销商,dealerCode{}", order.getDealerCode());
                if (!dealerCode.equals(order.getDealerCode())) {
                    logger.info("doMaintainTask,经销商不同,dealerCode1{},dealerCode2{}", dealerCode, order.getDealerCode());
                    vo.setDealerCode(order.getDealerCode());
                } else {
                    continue;
                }
            }
            Date adviseInDate = this.setAdviseInDate(vo, vo.getDealerCode(), vo.getLastMaintainDate(), vo
                    .getDailyAverageMileage());
            if (adviseInDate != null) {
                vo.setAdviseInDate(adviseInDate);
                ordList.add(vo.getDealerCode());
                this.createInviteTaskNew(vo, twoInvite);
            }
        }
        //重设工单门店
        vo.setDealerCode(roDealerCode);
        this.doMaintainLossTask(vo,ro, ordList, listCode);
        logger.info("doMaintainTask,end");
    }

    /**
     * 流失线索关闭
     * */
    private void doMaintainLossTask(VehicleOwnerVO vo, RepairOrderVO ro, List<String> orders, List<String> listCode){
        logger.info("doMaintainLossTask,开始客户流失{}", vo.getVin());
        logger.info("doMaintainLossTask,查询客户流失规则，DealerCode{}", vo.getDealerCode());
        //查询客户流失间隔规则
        InviteRulePO rule = inviteRuleMapper.getInvitationDlrRule(vo.getDealerCode(), 82031004, 82041005);
        logger.info(CommonConstants.LOG_S21, rule);
        if (rule == null || rule.getIsUse() == null || rule.getIsUse() == 0) {
            logger.info("doMaintainLossTask,rule == null || rule.getIsUse() == null || rule.getIsUse() == 0");
            //不存在客户流失间隔规则，跳过
            return;
        }
        //如果是保养工单
        logger.info("doMaintainLossTask,保养工单，vin{}", vo.getVin());
        logger.info("doMaintainLossTask,查询待关闭预警线索，vin{}", vo.getVin());
        List<InviteVehicleRecordPO> list = this.getWaitCloseAlertRecord("", vo.getVin());
        if(CollectionUtils.isNotEmpty(list) && CollectionUtils.isNotEmpty(listCode)){
            logger.info("doMaintainLossTask,查询结果，size{}", list.size());
            for (InviteVehicleRecordPO item : list) {
                if(listCode.contains(item.getDealerCode())){
                    logger.info("doMaintainLossTask,流失预警，continue");
                    continue;
                }
                item = inviteVehicleRecordMapper.selectById(item.getId());
                //完成
                if (ro.getDealerCode().equals(item.getDealerCode())) {
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_I);
                } else {
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_IV);
                }

                item.setOrderFinishDate(vo.getDeliveryDate());
                item.setRoNo(vo.getRoNo());
                item.setRoCreateDate(vo.getRoCreateDate());
                item.setRepairTypeCode(vo.getRepairTypeCode());
                item.setFinishDealerCode(vo.getDealerCode());
                item.setOutMileage(vo.getOutMileage().doubleValue());
                inviteVehicleRecordMapper.updateById(item);
                this.mergeInvite(item);
            }
        }
        //保养关闭所有流失线索
        logger.info("doMaintainLossTask,查询待关闭线索，vin{}", vo.getVin());
        List<InviteVehicleRecordPO> listAll = this.getWaitCloseLostRecord("", vo.getVin());
        if(CollectionUtils.isNotEmpty(listAll) && CollectionUtils.isNotEmpty(listCode)){
            logger.info("doMaintainLossTask,查询结果，size{}", listAll.size());
            for (InviteVehicleRecordPO item : listAll) {
                if(listCode.contains(item.getDealerCode())){
                    logger.info("doMaintainLossTask,流失线索，continue");
                    continue;
                }
                item = inviteVehicleRecordMapper.selectById(item.getId());
                //完成
                if (ro.getDealerCode().equals(item.getDealerCode())) {
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_I);
                } else {
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_IV);
                }
                item.setOrderFinishDate(vo.getDeliveryDate());
                item.setRoNo(vo.getRoNo());
                item.setRoCreateDate(vo.getRoCreateDate());
                item.setRepairTypeCode(vo.getRepairTypeCode());
                item.setFinishDealerCode(vo.getDealerCode());
                item.setOutMileage(vo.getOutMileage().doubleValue());
                inviteVehicleRecordMapper.updateById(item);
                this.mergeInvite(item);
            }
        }
        logger.info("doMaintainLossTask,关闭未下发的流失任务，vin{},DealerCode{}", vo.getVin(), vo.getDealerCode());
        this.updateTaskCloseById("", CommonConstants.INVITE_TYPE_VI, vo.getVin());
        //创建任务
        vo.setInviteType(CommonConstants.INVITE_TYPE_VI);
        Calendar c = Calendar.getInstance();
        c.setTime(vo.getDeliveryDate());
        c.add(Calendar.MONTH, rule.getRuleValue());
        vo.setAdviseInDate(c.getTime());
        vo.setDayInAdvance(rule.getDayInAdvance());
        vo.setInviteRule(rule.getInviteRule());
        vo.setRuleValue(rule.getRuleValue());
        vo.setRemindInterval(rule.getRemindInterval());
        vo.setCloseInterval(rule.getCloseInterval());
        vo.setLastMaintainDate(vo.getRoCreateDate());
        for (String order : orders) {
            logger.info("doMaintainLossTask,createInviteTaskNew,order", order);
            if(ObjectUtil.isNotEmpty(order)){
                vo.setDealerCode(order);
            }
            logger.info("doMaintainLossTask,createInviteTaskNew,DealerCode", vo.getDealerCode());
            this.createInviteTaskNew(vo, lossInvite);
        }
        logger.info("doMaintainLossTask,end");
    }

    /**
     * 查询流失线索
     *
     * @param dealerCode
     * @param vin
     * @return
     */
    private List<InviteVehicleRecordPO> getWaitCloseLostRecord(String dealerCode, String vin) {
        return inviteVehicleRecordService.getWaitCloseLostRecord(dealerCode, vin);
    }

    /**
     * 查询预警线索
     *
     * @param dealerCode
     * @param vin
     * @return
     */
    private List<InviteVehicleRecordPO> getWaitCloseAlertRecord(String dealerCode, String vin) {
        return inviteVehicleRecordService.getWaitCloseAlertRecord(dealerCode, vin);
    }

    /**
     * 平均分配到未来4个月每天
     *
     * @param createDate
     * @param vehList
     * @throws Exception
     */
    private void distribute(String createDate, List<VehicleOwnerVO> vehList) throws Exception {
        Date date = Utility.parseString2Date(createDate, "yyyy-MM-dd");
        logger.info("分配基准日期{}", date.toString());
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        Date one = calendar.getTime();
        logger.info("未来第一个月初始日期{}", one.toString());
        int oneDays = calendar.getActualMaximum(Calendar.DATE);
        logger.info("第一个月最大天数{}", oneDays);
        calendar.add(Calendar.MONTH, 1);
        Date two = calendar.getTime();
        logger.info("未来第二个月初始日期{}", two.toString());
        int twoDays = calendar.getActualMaximum(Calendar.DATE);
        logger.info("第二个月最大天数{}", twoDays);
        calendar.add(Calendar.MONTH, 1);
        Date three = calendar.getTime();
        logger.info("未来第三个月初始日期{}", three.toString());
        int threeDays = calendar.getActualMaximum(Calendar.DATE);
        logger.info("第三个月最大天数{}", threeDays);
        calendar.add(Calendar.MONTH, 1);
        Date four = calendar.getTime();
        logger.info("未来第四个月初始日期{}", four.toString());
        int fourDays = calendar.getActualMaximum(Calendar.DATE);
        logger.info("第四个月最大天数{}", fourDays);
        //排序集合
        Collections.sort(vehList);
        int total = vehList.size();
        logger.info("总条数{}", total);
        int fourMonth = (int) (0.15 * total);
        logger.info("第四月分配数量{}", fourMonth);
        int threeMonth = (int) (0.25 * total);
        logger.info("第三月分配数量{}", threeMonth);
        int twoMonth = (int) (0.3 * total);
        logger.info("第二月分配数量{}", twoMonth);
        int oneMonth = total - twoMonth - threeMonth - fourMonth;
        logger.info("第一月分配数量{}", oneMonth);
        double eachOne = oneMonth * 1.0 / oneDays;
        logger.info("第一月平均每天数量{}", eachOne);
        double eachTwo = twoMonth * 1.0 / twoDays;
        logger.info("第二月平均每天数量{}", eachTwo);
        double eachThree = threeMonth * 1.0 / threeDays;
        logger.info("第三月平均每天数量{}", eachThree);
        double eachFour = fourMonth * 1.0 / fourDays;
        logger.info("第四月平均每天数量{}", eachFour);
        int dayCount = 0;
        int MonthCount = 1;
        for (int i = 0; i < total; i++) {
            VehicleOwnerVO vo = vehList.get(i);
            if (i + 1 <= oneMonth) {
                dayCount++;
                vo.setAdviseInDate(one);
                if (dayCount >= eachOne && MonthCount < oneDays) {
                    Calendar c = new GregorianCalendar();
                    c.setTime(one);
                    c.add(Calendar.DATE, 1);
                    one = c.getTime();
                    dayCount = 0;
                    MonthCount++;
                }
                if (i + 1 == oneMonth) {
                    dayCount = 0;
                    MonthCount = 1;
                }
            } else if (i + 1 <= oneMonth + twoMonth) {
                dayCount++;
                vo.setAdviseInDate(two);
                if (dayCount >= eachTwo && MonthCount < twoDays) {
                    Calendar c = new GregorianCalendar();
                    c.setTime(two);
                    c.add(Calendar.DATE, 1);
                    two = c.getTime();
                    dayCount = 0;
                    MonthCount++;
                }
                if (i + 1 == oneMonth + twoMonth) {
                    dayCount = 0;
                    MonthCount = 1;
                }
            } else if (i + 1 <= oneMonth + twoMonth + threeMonth) {
                dayCount++;
                vo.setAdviseInDate(three);
                if (dayCount >= eachThree && MonthCount < threeDays) {
                    Calendar c = new GregorianCalendar();
                    c.setTime(three);
                    c.add(Calendar.DATE, 1);
                    three = c.getTime();
                    dayCount = 0;
                    MonthCount++;
                }
                if (i + 1 == oneMonth + twoMonth + threeMonth) {
                    dayCount = 0;
                    MonthCount = 1;
                }
            } else if (i + 1 <= oneMonth + twoMonth + threeMonth + fourMonth) {
                dayCount++;
                vo.setAdviseInDate(four);
                if (dayCount >= eachFour && MonthCount < fourDays) {
                    Calendar c = new GregorianCalendar();
                    c.setTime(four);
                    c.add(Calendar.DATE, 1);
                    four = c.getTime();
                    dayCount = 0;
                    MonthCount++;
                }
            }
            logger.info("vin{},建议入厂日期{}", vo.getVin(), vo.getAdviseInDate());
            this.createInviteTask(vo, CommonConstants.UP_DAY);
        }
    }


    /**
     * 检查是否执行日
     *
     * @param createDate
     * @return
     */
    private boolean checkExecuteDate(String createDate, String dateFormat, String da) {
        try {
            Date date = Utility.parseString2Date(createDate, "yyyy-MM-dd");
            DateFormat f = new SimpleDateFormat(dateFormat);
            return da.equals(f.format(date));
        } catch (Exception e) {

        }
        return false;
    }

    /**
     * 创建或者更新易损件任务
     *
     * @param vo
     * @param rule
     */
    private void createTask(VehicleOwnerVO vo, InvitePartItemRulePO rule) {
        logger.info("开始易损件任务", vo.getVin());
        //检查是否匹配规则
        if (this.checkSuitRule(vo, rule)) {
            vo.setDayInAdvance(rule.getDayInAdvance());
            vo.setRemindInterval(rule.getRemindInterval());
            vo.setItemType(rule.getType());
            vo.setItemCode(rule.getCode());
            vo.setItemName(rule.getName());
            vo.setCloseInterval(0);
            vo.setInviteType(CommonConstants.INVITE_TYPE_V);
            logger.info("开始查询是否对应易损件工单,vin{},roNo{},dealerCode{}", vo.getVin(), vo.getRoNo(), vo.getDealerCode());
            RepairOrderVO ro = reportCommonClient.queryRepairOrderByRoNo(vo.getDealerCode(), vo.getRoNo(), rule
                    .getType(), rule.getCode());
            if (ro != null) {
                String roDealerCode = vo.getDealerCode();
                logger.info("存在工单基准日期:{},日均里程:{},里程间隔:{},日期间隔:{}", ro.getDeliveryDate(), vo
                        .getDailyAverageMileage(), rule
                        .getMileageInterval(), rule.getDateInterval());
                vo.setLastMaintainDate(ro.getDeliveryDate());
                logger.info("设置基准日期:InviteTime{}", ro.getDeliveryDate());
                vo.setOutMileage(ro.getOutMileage().intValue());
                Date adviseInDate = this.setAdviseInDate(vo, ro.getDeliveryDate(), vo.getDailyAverageMileage(), rule
                        .getMileageInterval(), rule.getDateInterval());
                vo.setAdviseInDate(adviseInDate);
                logger.info("建议进厂日期:{}", adviseInDate);
                logger.info("开始查询未关闭的易损件,vin{},Code{},Type{}", vo.getVin(), rule.getCode(), rule.getType());
                List<InviteVehicleRecordPO> list = this.getWaitCloseVulnerableRecord(vo.getVin(), rule.getCode(),
                        rule.getType());
                logger.info("查询结果,size", list.size());
                for (InviteVehicleRecordPO item : list) {
                    item = inviteVehicleRecordMapper.selectById(item.getId());
                    if (ro.getDealerCode().equals(item.getDealerCode())) {
                        logger.info("本店，完成,recordId{},dealerCode{}", item.getId(), item.getDealerCode());
                        //完成
                        item.setOrderStatus(CommonConstants.ORDER_STATUS_I);
                        item.setOrderFinishDate(ro.getDeliveryDate());
                        item.setRoNo(ro.getRoNo());
                        item.setRoCreateDate(ro.getRoCreateDate());
                        item.setRepairTypeCode(ro.getRepairTypeCode());
                        item.setFinishDealerCode(ro.getDealerCode());
                        item.setOutMileage(ro.getOutMileage());
                    } else {
                        logger.info("他店，他店进厂,recordId{},dealerCode{}", item.getId(), item.getDealerCode());
                        //他店进厂
                        item.setOrderStatus(CommonConstants.ORDER_STATUS_IV);
                        item.setOrderFinishDate(ro.getDeliveryDate());
                        item.setRoNo(ro.getRoNo());
                        item.setRoCreateDate(ro.getRoCreateDate());
                        item.setRepairTypeCode(ro.getRepairTypeCode());
                        item.setFinishDealerCode(ro.getDealerCode());
                        item.setOutMileage(ro.getOutMileage());
                    }
                    inviteVehicleRecordMapper.updateById(item);
                    logger.info("开始拆分线索，重新合并线索,recordId{},InviteType{}", item.getId(), item.getInviteType());
                    this.mergeInvite(item);
                }
                logger.info("关闭未下发任务,vin{}", vo.getVin());
                this.updateTaskPartCloseById(CommonConstants.INVITE_TYPE_V, vo.getVin(), rule
                        .getType(), rule.getCode());
                logger.info("开始查询最近两次工单:{}", vo.getVin());
                //查询最近两次工单 的经销商
                List<RepairOrderVO> orders = reportCommonClient.getRepairOrderLast(vo.getVin());
                logger.info("查询结果size:{}", orders.size());
                String dealerCode = null;
                for (RepairOrderVO order : orders) {
                    if (dealerCode == null) {
                        logger.info("最近工单门店code:{}", order.getDealerCode());
                        dealerCode = order.getDealerCode();
                        vo.setDealerCode(order.getDealerCode());
                    } else {
                        if (!dealerCode.equals(order.getDealerCode())) {
                            //如果最近两次工单在不同的店，创建两次邀约任务
                            vo.setDealerCode(order.getDealerCode());
                            logger.info("另一张工单门店code:{}", order.getDealerCode());
                        } else {
                            continue;
                        }
                    }
                    this.createInviteTask(vo, CommonConstants.UP_DAY);
                }
                //重设工单门店
                vo.setDealerCode(roDealerCode);
            }
        }
    }


    /**
     * 计算建议进厂时间
     *
     * @param inviteTime
     * @param dailyAverageMileage
     * @param mileageInterval
     * @param dateInterval
     * @return
     */
    protected Date setAdviseInDate(VehicleOwnerVO vo, Date inviteTime, Double dailyAverageMileage, Integer
            mileageInterval, Integer dateInterval) {
        logger.info("基准时间inviteTime{},日均里程dailyAverageMileage{},里程间隔{}，日期间隔{}", inviteTime, dailyAverageMileage,
                mileageInterval, dateInterval);
        if ((mileageInterval == 0 || dailyAverageMileage == null || dailyAverageMileage == 0) && dateInterval == 0) {
            logger.info("必要计算条件为null,不计算{}", vo.getVin());
            return null;
        }
        Date adviseInDate1 = null;
        Date adviseInDate2 = null;
        //日期间隔
        if (dateInterval != 0) {
            Calendar c = Calendar.getInstance();
            c.setTime(inviteTime);
            c.add(Calendar.MONTH, dateInterval);
            adviseInDate1 = c.getTime();
            logger.info("日期间隔,建议入厂时间{}", adviseInDate1);
        }
        //里程间隔
        if (mileageInterval != 0 && dailyAverageMileage != null && dailyAverageMileage > 0) {
            Calendar c = Calendar.getInstance();
            c.setTime(inviteTime);
            int amount = (int) (mileageInterval / dailyAverageMileage);
            c.add(Calendar.DATE, amount);
            adviseInDate2 = c.getTime();
            logger.info("里程间隔,建议入厂时间{}", adviseInDate2);
        }
        if (vo.getOutMileage() == null) {
            logger.info("基准里程为null,设置初始里程为0{}", vo.getOutMileage());
            vo.setOutMileage(0);
        }
        vo.setAdviseInMileage(vo.getOutMileage() + mileageInterval);
        logger.info("建议入厂里程{}", vo.getAdviseInMileage());
        //日期间隔为空取里程间隔
        if (dateInterval == 0) {
            return adviseInDate2;
        }
        //里程间隔为空或平均里程为空 取日期间隔
        if (mileageInterval == 0 || dailyAverageMileage == null || dailyAverageMileage <= 0) {
            return adviseInDate1;
        }
        //存在两条，取较小的建议进厂日期
        return adviseInDate1.compareTo(adviseInDate2) > 0 ? adviseInDate2 : adviseInDate1;
    }

    /**
     * 计算建议进厂时间
     *
     * @param inviteTime
     * @param dailyAverageMileage
     * @param mileageInterval
     * @param dateInterval
     * @return
     */
    private Date setAdviseInDate(Date inviteTime, Double dailyAverageMileage, Integer
            mileageInterval, Integer dateInterval) {
        if ((mileageInterval == 0 || dailyAverageMileage == null || dailyAverageMileage == 0) && dateInterval == 0) {
            return null;
        }
        Date adviseInDate1 = null;
        Date adviseInDate2 = null;
        //日期间隔
        if (dateInterval != 0) {
            Calendar c = Calendar.getInstance();
            c.setTime(inviteTime);
            c.add(Calendar.MONTH, dateInterval);
            adviseInDate1 = c.getTime();
        }
        //里程间隔
        if (mileageInterval != 0 && dailyAverageMileage != null && dailyAverageMileage != 0) {
            Calendar c = Calendar.getInstance();
            c.setTime(inviteTime);
            int amount = (int) (mileageInterval / dailyAverageMileage);
            c.add(Calendar.DATE, amount);
            adviseInDate2 = c.getTime();
        }
        //日期间隔为空取里程间隔
        if (dateInterval == 0) {
            return adviseInDate2;
        }
        //里程间隔为空或平均里程为空 取日期间隔
        if (mileageInterval == 0 || dailyAverageMileage == null || dailyAverageMileage == 0) {
            return adviseInDate1;
        }
        //存在两条，取较小的建议进厂日期
        return adviseInDate1.compareTo(adviseInDate2) > 0 ? adviseInDate2 : adviseInDate1;
    }


    /**
     * 检查是否匹配易损件规则
     *
     * @param vo
     * @param rule
     * @return
     */
    private boolean checkSuitRule(VehicleOwnerVO vo, InvitePartItemRulePO rule) {
        logger.info("开始检查是否匹配易损件规则{}", vo.getVin());
        logger.info("vin{},vinRule{}", vo.getVin(), rule.getVin());
        logger.info("modelCode{},modelCodeRule{}", vo.getModelCode(), rule.getModelCode());
        logger.info("ModelYear{},ModelYearRule{}", vo.getConfigYear(), rule.getModelYear());
        logger.info("EngineCode{},EngineCodeRule{}", vo.getEngineNo(), rule.getEngineCode());
        logger.info("GearboxCode{},GearboxCodeRule{}", vo.getTransMission(), rule.getGearboxCode());
        //与
        if (CommonConstants.RULE_AND.equals(rule.getRuleRelationship())) {
            logger.info("规则匹配与,code{}", rule.getCode());
            //vin
            if (rule.getVin() != null && !this.checkVin(rule.getVin(), vo.getVin())) {
                logger.info("规则不匹配vin,vin{},vinRule{}", vo.getVin(), rule.getVin());
                return false;
            }
            //车型
            if (rule.getModelCode() != null && !rule.getModelCode().equals(vo.getModelCode())) {
                logger.info("规则不匹配车型,modelCode{},modelCodeRule{}", vo.getModelCode(), rule.getModelCode());
                return false;
            }
            //年款
            if (rule.getModelYear() != null && !rule.getModelYear().equals(vo.getConfigYear())) {
                logger.info("规则不匹配车型年,ModelYear{},ModelYearRule{}", vo.getConfigYear(), rule.getModelYear());
                return false;
            }
            //发动机号
            if (rule.getEngineCode() != null && !rule.getEngineCode().equals(vo.getEngineNo())) {
                logger.info("规则不匹配发动机号,EngineCode{},EngineCodeRule{}", vo.getEngineNo(), rule.getEngineCode());
                return false;
            }
            //变速箱号
            if (rule.getGearboxCode() != null && !rule.getGearboxCode().equals(vo.getTransMission())) {
                logger.info("规则不匹配变速箱号,GearboxCode{},GearboxCodeRule{}", vo.getTransMission(), rule.getGearboxCode());
                return false;
            }
            logger.info("规则匹配,code{}", rule.getCode());
            return true;
        }
        //或
        if (CommonConstants.RULE_OR.equals(rule.getRuleRelationship())) {
            logger.info("规则匹配或,code{}", rule.getCode());
            if (rule.getVin() == null && rule.getModelCode() == null && rule.getModelYear() == null && rule
                    .getEngineCode()
                    == null && rule.getGearboxCode() == null) {
                logger.info("规则匹配,条件全为null,code{}", rule.getCode());
                return true;
            }
            //vin
            if (rule.getVin() != null && this.checkVin(rule.getVin(), vo.getVin())) {
                logger.info("规则匹配vin,vin{},vinRule{}", vo.getVin(), rule.getVin());
                return true;
            }
            //车型
            if (rule.getModelCode() != null && rule.getModelCode().equals(vo.getModelCode())) {
                logger.info("规则匹配车型,modelCode{},modelCodeRule{}", vo.getModelCode(), rule.getModelCode());
                return true;
            }
            //年款
            if (rule.getModelYear() != null && rule.getModelYear().equals(vo.getConfigYear())) {
                logger.info("规则匹配车型年,ModelYear{},ModelYearRule{}", vo.getConfigYear(), rule.getModelYear());
                return true;
            }
            //发动机号
            if (rule.getEngineCode() != null && rule.getEngineCode().equals(vo.getEngineNo())) {
                logger.info("规则匹配发动机号,EngineCode{},EngineCodeRule{}", vo.getEngineNo(), rule.getEngineCode());
                return true;
            }
            //变速箱号
            if (rule.getGearboxCode() != null && rule.getGearboxCode().equals(vo.getTransMission())) {
                logger.info("规则匹配变速箱号,GearboxCode{},GearboxCodeRule{}", vo.getTransMission(), rule.getGearboxCode());
                return true;
            }
            logger.info("规则不匹配,code{}", rule.getCode());
            return false;
        }
        return true;
    }

    /**
     * 检查 是否匹配vin
     *
     * @param vinRule
     * @param vin
     * @return
     */
    private boolean checkVin(String vinRule, String vin) {
        char[] ar = vinRule.toCharArray();
        for (int i = 0; i < ar.length; i++) {
            if (ar[i] == '-') {
                continue;
            } else {
                if (vin.charAt(i) != ar[i]) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 定保跟据定保规则 计算建议进厂时间
     *
     * @param dealerCode
     * @param inviteTime时间
     * @param dailyAverageMileage
     * @return
     */
    protected Date setAdviseInDate(VehicleOwnerVO vo, String dealerCode, Date inviteTime, Double dailyAverageMileage) {
        logger.info("开始计算建议入厂,vin{}", vo.getVin());
        logger.info("开始查询定保规则,dealerCode{}", dealerCode);
        List<InviteRulePO> rules = inviteRuleService.getRegularMaintainRule(dealerCode);
        logger.info("定保规则数量,size{}", rules.size());
        if (rules.size() == 0) {
            logger.info("规则不存在,size{}", rules.size());
            //规则不存在时返回 null
            return null;
        }
        Date adviseInDate1 = null;
        Date adviseInDate2 = null;
        InviteRulePO ru1 = null;
        InviteRulePO ru2 = null;
        for (int i = 0; i < rules.size(); i++) {
            InviteRulePO rule = rules.get(i);
            Date adviseInDate = null;
            //日期间隔
            if (CommonConstants.INVITE_RULE_III.equals(rule.getInviteRule())) {
                Integer ruleValue = rule.getRuleValue();
                Calendar c = Calendar.getInstance();
                c.setTime(inviteTime);
                c.add(Calendar.MONTH, ruleValue);
                adviseInDate = c.getTime();
                logger.info("日期间隔规则,adviseInDate{},vin:{}", adviseInDate,vo.getVin());
            }
            //里程间隔
            else if (CommonConstants.INVITE_RULE_II.equals(rule.getInviteRule())) {
                if (dailyAverageMileage != null && dailyAverageMileage > 0) {
                    adviseInDate = getIncomingDate(rule,dailyAverageMileage,vo);
                    logger.info("里程间隔规则,adviseInDate{},vin{}", adviseInDate,vo.getVin());
                    logger.info("里程间隔规则,adviseInMileage{},vin{}", vo.getAdviseInMileage(),vo.getVin());
                } else {
                    continue;
                }
                //定保保存建议进厂里程
                vo.setAdviseInMileage(vo.getOutMileage() + rule.getRuleValue());
            }
            if (i == 0) {
                adviseInDate1 = adviseInDate;
                ru1 = rule;
            } else if (i == 1) {
                adviseInDate2 = adviseInDate;
                ru2 = rule;
            }
        }
        logger.info("计算建议入厂日期,adviseInDate1{}，adviseInDate2{},vin{}", adviseInDate1, adviseInDate2,vo.getVin());
        //存在两条，取较小的建议进厂日期
        if (adviseInDate2 != null && adviseInDate1 != null) {
            if (adviseInDate1.compareTo(adviseInDate2) > 0) {
                vo.setDayInAdvance(ru2.getDayInAdvance());
                vo.setInviteRule(ru2.getInviteRule());
                vo.setRuleValue(ru2.getRuleValue());
                vo.setRemindInterval(ru2.getRemindInterval());
                vo.setCloseInterval(ru2.getCloseInterval());
                logger.info("计算出的建议进厂日期,adviseInDate{}", adviseInDate2);
                return adviseInDate2;
            } else {
                vo.setDayInAdvance(ru1.getDayInAdvance());
                vo.setInviteRule(ru1.getInviteRule());
                vo.setRuleValue(ru1.getRuleValue());
                vo.setRemindInterval(ru1.getRemindInterval());
                vo.setCloseInterval(ru1.getCloseInterval());
                logger.info("计算出的建议进厂日期,adviseInDate{}", adviseInDate1);
                return adviseInDate1;
            }
        }

        if (adviseInDate2 != null) {
            vo.setDayInAdvance(ru2.getDayInAdvance());
            vo.setInviteRule(ru2.getInviteRule());
            vo.setRuleValue(ru2.getRuleValue());
            vo.setRemindInterval(ru2.getRemindInterval());
            vo.setCloseInterval(ru2.getCloseInterval());
            logger.info("计算出的建议进厂日期,adviseInDate{}", adviseInDate2);
            return adviseInDate2;
        } else {
            vo.setDayInAdvance(ru1.getDayInAdvance());
            vo.setInviteRule(ru1.getInviteRule());
            vo.setRuleValue(ru1.getRuleValue());
            vo.setRemindInterval(ru1.getRemindInterval());
            vo.setCloseInterval(ru1.getCloseInterval());
            logger.info("计算出的建议进厂日期,adviseInDate{}", adviseInDate1);
            return adviseInDate1;
        }
    }

    private Date getIncomingDate(InviteRulePO rule, Double dailyAverageMileage, VehicleOwnerVO vo) {
        //查询最近一次结算工单 获取 进出场里程数，结算时间
        VehicleOwnerVO   vehicleOwnerVO=     reportCommonClient.queryRepairOrderByVinAndCode(vo.getVin(),vo.getDealerCode());
        logger.info("任务下载getIncomingDate查询最近一次结算工单{}", JSONObject.toJSON(vehicleOwnerVO));
        Integer out =  vehicleOwnerVO.getOutMileage();
        Date deliveryDate =  vehicleOwnerVO.getDeliveryDate();
        //查询最近一次机滤结算工单，获取结算时间。
        VehicleOwnerVO   vo1= reportCommonClient.queryRepairOrderByVinAndCodeAndJL(vo.getVin(),vo.getDealerCode());
        logger.info("任务下载getIncomingDate查询最近一次机滤结算工单{}", JSONObject.toJSON(vo1));
        logger.info("任务下载getIncomingDate规则里程{}", rule.getRuleValue());
        Integer out1 = 0 ;
        if(vo1!=null){
            out1 =   vo1.getOutMileage();
        }
        if(out1+rule.getRuleValue()-out<=0){
            //返回下個月1號
            return   nextMonthFirstDate();
        }else {

            Calendar c = Calendar.getInstance();
            c.setTime(deliveryDate);
            int amount = (int) ((out1+rule.getRuleValue()-out) / dailyAverageMileage);
            c.add(Calendar.DATE, amount);
            return  c.getTime();
        }
    }
    public static Date nextMonthFirstDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.MONTH, 1);
        return calendar.getTime();
    }

    /**
     * 计算日均行驶里程
     *
     * @param vin
     * @return
     */
    private BigDecimal countDailyAverageMileage(VehicleOwnerVO owner, String vin, Date invoiceDate, List<VehiclePO> upList, List<VehiclePO> addList, List<VocMileageVO> lists) {
        //公共计算日均里程逻辑
        DailyAverageMileageVo dailyAverageMileageVo = commonCountDailyAverageMileage( vin, invoiceDate);
        BigDecimal dailyAverageMileage = dailyAverageMileageVo.getDailyAverageMileage();
        List<VocMileageVO> vocMileageVOS = dailyAverageMileageVo.getVocMileageVOS();
        long startTime = System.currentTimeMillis();
        logger.info("countDailyAverageMileage,vin{},startTime:{}", vin,startTime);
        LambdaQueryWrapper<VehiclePO> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(VehiclePO::getVin, vin);
        VehiclePO rs = vehicleMapper.selectOne(queryWrapper);
        logger.info("countDailyAverageMileage,vin{},DealerCode{},RoNo{},invoiceDate{},dailyAverageMileage{}", vin, owner.getDealerCode
                (), owner.getRoNo(), invoiceDate, dailyAverageMileage);
        this.saveDailyMileageLog(vin, owner.getDealerCode(), owner.getRoNo(), invoiceDate, vocMileageVOS.size() >= 1 ? vocMileageVOS.get(0) : null,
                vocMileageVOS.size() >= 2 ? vocMileageVOS.get(1) : null,
                vocMileageVOS.size() >= 3 ? vocMileageVOS.get(2) : null,
                vocMileageVOS.size() >= 4 ? vocMileageVOS.get(3) : null, rs,
                dailyAverageMileage);
        if (dailyAverageMileage == null || dailyAverageMileage.compareTo(BigDecimal.ZERO) <= 0) {
            logger.info("countDailyAverageMileage,vin:{},dailyAverageMileage == null:{}", vin, dailyAverageMileage);
            return null;
        }
        VocMileageVO vehInfo = new VocMileageVO();
        logger.info("countDailyAverageMileage,vin:{}", vin);
        vehInfo.setVin(vin);
        vehInfo.setInvoiceDate(invoiceDate);
        vehInfo.setDailyAverageMileage(dailyAverageMileage);
        lists.add(vehInfo);
        if (rs != null) {
            rs.setDailyAverageMileage(dailyAverageMileage);
            rs.setIsVoc(10041002);
            upList.add(rs);
            //vehicleMapper.updateById(rs);
        } else {
            VehiclePO insert = new VehiclePO();
            insert.setVin(vin);
            insert.setDailyAverageMileage(dailyAverageMileage);
            insert.setIsVoc(10041002);
            addList.add(insert);
            //vehicleMapper.insert(insert);
        }
        long endTime = System.currentTimeMillis();
        logger.info("countDailyAverageMileage,vin:{},endTime:{},dailyAverageMileage:{}", vin, endTime, dailyAverageMileage);
        return dailyAverageMileage;
    }

    /**
     * 计算日均行驶里程
     *
     * @param vin
     * @return
     */
    private BigDecimal countDailyAverageMileage(VehicleOwnerVO owner, String vin, Date invoiceDate) {
        //公共计算日均里程逻辑
        DailyAverageMileageVo dailyAverageMileageVo = commonCountDailyAverageMileage( vin, invoiceDate);
        BigDecimal dailyAverageMileage = dailyAverageMileageVo.getDailyAverageMileage();
        List<VocMileageVO> vocMileageVOS = dailyAverageMileageVo.getVocMileageVOS();
        long startTime = System.currentTimeMillis();
        logger.info("开始查询最近工单，vin{},startTime:{}", vin,startTime);
        LambdaQueryWrapper<VehiclePO> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(VehiclePO::getVin, vin);
        VehiclePO rs = vehicleMapper.selectOne(queryWrapper);
        logger.info("日均里程计算结果,vin{},DealerCode{},RoNo{},invoiceDate{},dailyAverageMileage{}", vin, owner.getDealerCode
                (), owner.getRoNo(), invoiceDate, dailyAverageMileage);
        this.saveDailyMileageLog(vin, owner.getDealerCode(), owner.getRoNo(), invoiceDate, vocMileageVOS.size() >= 1 ? vocMileageVOS.get(0) : null,
                vocMileageVOS.size() >= 2 ? vocMileageVOS.get(1) : null,
                vocMileageVOS.size() >= 3 ? vocMileageVOS.get(2) : null,
                vocMileageVOS.size() >= 4 ? vocMileageVOS.get(3) : null, rs,
                dailyAverageMileage);
        if (dailyAverageMileage == null || dailyAverageMileage.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        if (rs != null) {
            rs.setDailyAverageMileage(dailyAverageMileage);
            vehicleMapper.updateById(rs);
        } else {
            VehiclePO insert = new VehiclePO();
            insert.setVin(vin);
            insert.setDailyAverageMileage(dailyAverageMileage);
            vehicleMapper.insert(insert);
        }
        long endTime = System.currentTimeMillis();
        logger.info("开始查询最近工单，endTime:{}", endTime);
        return dailyAverageMileage;
    }

    /**
     * 日均里程计算公共计算逻辑
     * */
    public DailyAverageMileageVo commonCountDailyAverageMileage( String vin, Date invoiceDate){
        DailyAverageMileageVo dailyAverageMileageVo = new DailyAverageMileageVo();
        BigDecimal dailyAverageMileage = new BigDecimal(0.0);
        List<VocMileageVO> list = reportCommonClient.getAllMileage(vin);
        if (list.size() == 0) {
            dailyMileageLogMapper.SetErrorlog("repair", "不存在有效工单", null,
                    vin);
            return null;
        }
        //对比所有里程，删除时间间隔小于三天的里程
        for(int i=0; i < list.size()-1; i++){
            if( !compareDateThree(list.get(i).getGetTime(),list.get(i+1).getGetTime())){
                list.remove(i+1);
                i--;
            }
        }
        //过滤时间间隔为3天以上后的里程数据
        List<VocMileageVO> lastFiveMileageList = list.stream().limit(5).collect(Collectors.toList());
        dailyAverageMileageVo.setVocMileageVOS(lastFiveMileageList);
        //所有实际需要计算的日均里程
        List<Map<String,Object>> practicalMileageDayList = new ArrayList<>();
        if(lastFiveMileageList.size() > 1) {
            //所有计算的日均里程
            List<Map<String,Object>> mileageDayList = new ArrayList<>();
            //循环获取间隔的里程和天数
            for (int j = 0; j < lastFiveMileageList.size()-1; j++) {
                //日均里程
                Double avgMileage = Double.valueOf((lastFiveMileageList.get(j).getMileageKm() - lastFiveMileageList.get(j+1).getMileageKm())/getCompareDate(lastFiveMileageList.get(j).getGetTime(),lastFiveMileageList.get(j+1).getGetTime()));
                //日均里程为负数则剔除当前的里程
                Map<String,Object> mileageDay = new HashMap<>();
                mileageDay.put("intervalMileage", lastFiveMileageList.get(j).getMileageKm() - lastFiveMileageList.get(j+1).getMileageKm());
                mileageDay.put("intervalDay",DateUtils.daysDateBetween(lastFiveMileageList.get(j+1).getGetTime(),lastFiveMileageList.get(j).getGetTime()));
                mileageDay.put("avgMileage",avgMileage);
                mileageDayList.add(mileageDay);
            }
            /**
             * 剔除日均实际里程规则--间断里程剔除前段计算的实际里程
             * 1.连续两个大于100的则都不剔除
             * 2.有负数则剔除负数
             * 3.有大于100的则剔除
             * */
            //是否有连续大于100的平均公里-不剔除
            boolean continuousGt = isContinuousGt(mileageDayList);
            //剔除负数的平均公里数-或者是没有连续但是大于等于100的平均公里
            practicalMileageDayList = delMinusList(mileageDayList,continuousGt);
        }
        logger.info("当前工单{},计算日均里程对应的数据为{}",vin,practicalMileageDayList.toString());
        if(lastFiveMileageList.size() == 1 || !CollectionUtils.isNotEmpty(practicalMileageDayList)){
            if (invoiceDate != null) {
                int days = DateUtils.daysDateBetween(invoiceDate, lastFiveMileageList.get(0).getGetTime());
                if (days == 0) {
                    dailyAverageMileage = BigDecimal.valueOf(lastFiveMileageList.get(0).getMileageKm()).setScale(2,BigDecimal.ROUND_HALF_UP);
                } else {
                    dailyAverageMileage = BigDecimal.valueOf(lastFiveMileageList.get(0).getMileageKm()).divide(BigDecimal.valueOf(days),2,BigDecimal.ROUND_HALF_UP);
                }
                logger.info("有一张工单,计算日均里程{}", dailyAverageMileage);
            } else {
                logger.info("缺少实际里程数据,数据异常", dailyAverageMileage);
                dailyMileageLogMapper.SetErrorlog("repair", "缺少实际里程数据,数据异常", null, vin);
                return null;
            }
        }else {
            //总公里
            double intervalMileage = practicalMileageDayList.stream().mapToDouble(value -> Double.parseDouble(value.get("intervalMileage").toString())).sum();
            //总天数
            double intervalDay = practicalMileageDayList.stream().mapToDouble(value -> Double.parseDouble(value.get("intervalDay").toString())).sum();
            logger.info("当前工单{},对应的计算出来的总天数为{}，总里程为{}",vin,intervalDay,intervalMileage);
            //计算平均里程
            dailyAverageMileage = BigDecimal.valueOf(intervalMileage).divide(BigDecimal.valueOf(intervalDay),2,BigDecimal.ROUND_HALF_UP);
        }
        dailyAverageMileageVo.setDailyAverageMileage(dailyAverageMileage);
        logger.info("当前工单{},对应的计算出来的日均里程为{}",vin,dailyAverageMileageVo);
        return dailyAverageMileageVo;
    }

    /**
     * 保存日平均里程计算log
     *
     * @param vin
     * @param dealerCode
     * @param roNo
     * @param timeOne
     * @param mileageOne
     * @param timeTwo
     * @param mileageTwo
     * @param timeThree
     * @param mileageThree
     * @param timeFour
     * @param mileageFour
     */
    public void saveDailyMileageLog(String vin, String dealerCode, String roNo, Date invoiceDate, VocMileageVO one,
                                    VocMileageVO two,
                                    VocMileageVO three, VocMileageVO four, VehiclePO po, BigDecimal dailyAverageMileage) {
        DailyMileageLogPO log = new DailyMileageLogPO();
        log.setVin(vin);
        log.setDealerCode(dealerCode);
        log.setRoNo(roNo);
        log.setSalesDate(invoiceDate);
        if (one != null) {
            log.setTimeOne(one.getGetTime());
            log.setMileageOne(one.getMileageKm());
        }
        if (two != null) {
            log.setTimeTwo(two.getGetTime());
            log.setMileageTwo(two.getMileageKm());
        }
        if (three != null) {
            log.setTimeThree(three.getGetTime());
            log.setMileageThree(three.getMileageKm());
        }
        if (four != null) {
            log.setTimeFour(four.getGetTime());
            log.setMileageFour(four.getMileageKm());
        }
        if (po != null) {
            log.setLastDailyMileage(po.getDailyAverageMileage());
        }
        log.setCountType("repair");
        log.setDailyMileage(dailyAverageMileage);
        dailyMileageLogMapper.insertDailyMileageLog(log);
    }

    /**
     * 判断是否有连段日均里程大于等于100
     * */
    private boolean isContinuousGt(List<Map<String,Object>> mileageDayList) {
        boolean flag = false;
        for (int i = 0; i < mileageDayList.size()-1; i++) {
            if(Double.parseDouble(mileageDayList.get(i).get("avgMileage").toString()) >= 100 && Double.parseDouble(mileageDayList.get(i+1).get("avgMileage").toString()) >= 100){
                flag = true;
                break;
            }
        }
        return flag;
    }

    /**
     * 删除负数的平均公里数 ，或者在没有连续大于100的平均公里则删除100的平均公里
     * */
    private List<Map<String,Object>> delMinusList(List<Map<String,Object>> mileageDayList, boolean continuousGt) {
        for (int i = mileageDayList.size() - 1; i >=0 ; i--) {
            if(Double.parseDouble(mileageDayList.get(i).get("avgMileage").toString()) <= 0 || (!continuousGt && Double.parseDouble(mileageDayList.get(i).get("avgMileage").toString()) >= 100)){
                mileageDayList.remove(i);
            }
        }
        return mileageDayList;
    }
    /**
     * 判断间隔是否大于3天
     *
     * @param a
     * @param b
     * @return
     */
    private boolean compareDateThree(Date a, Date b) {
        int days = DateUtils.daysDateBetween(b,a);
        return days > 3;
    }

    /**
     * 判断间隔是否大于15天
     *
     * @param a
     * @param b
     * @return
     */
    private boolean compareDate(Date a, Date b) {
        Long days = this.getCompareDate(a, b);
        return days > 15;
    }

    private Long getCompareDate(Date a, Date b) {
        Long rs = (a.getTime() - b.getTime()) / (1000 * 60 * 60 * 24);
        logger.info("间隔天数{}", rs);
        return rs;
    }


    /**
     * 创建邀约任务
     *
     * @param vo
     */
    private void createInviteTask(VehicleOwnerVO vo, Integer days) {

        InviteVehicleTaskPO po = new InviteVehicleTaskPO();
        po.setVin(vo.getVin());
        po.setLicensePlateNum(vo.getPlateNumber());
        po.setDealerCode(vo.getDealerCode());
        po.setName(vo.getName());
        po.setTel(vo.getMobile());
        po.setAge(vo.getAge());
        po.setSex(vo.getSex());
        po.setModel(vo.getModelId());
        po.setDailyMileage(vo.getDailyAverageMileage());
        po.setInviteType(vo.getInviteType());
        po.setAdviseInDate(vo.getAdviseInDate());
        //po.setDayInAdvance(vo.getDayInAdvance());
        po.setRemindInterval(vo.getRemindInterval());
        po.setCloseInterval(vo.getCloseInterval());
        po.setIsCreateInvite(0);
        po.setInviteTime(vo.getLastMaintainDate());
        Calendar c = Calendar.getInstance();
        c.setTime(po.getAdviseInDate());
        c.add(Calendar.DATE, days);
        po.setCreateInviteTime(c.getTime());
        po.setItemType(vo.getItemType());
        po.setItemCode(vo.getItemCode());
        po.setItemName(vo.getItemName());
        po.setAdviseInMileage(vo.getAdviseInMileage());
        po.setOutMileage(vo.getOutMileage());
        po.setQbNumber(vo.getQbNumber());
        inviteVehicleTaskMapper.insert(po);
        logger.info("2096定保，流失客户任务：{}，{}，{}，{}",po.getVin(),po.getInviteType(),po.getId(),po.getInviteId());
        this.saveVocInviteVehicleTaskRecordPo(po);
    }

    @Override
    @Async("asyncThreadPool")
    public void taskAllocationAsync(String vin) {
        logger.info("taskAllocationAsync,start:{}", vin);
        List<InviteVehicleRecordPO> listPo = inviteVehicleRecordMapper.queryWaitAllocationRecodeForVin(vin);
        if (CollectionUtils.isEmpty(listPo)) {
            return;
        }
        List<InviteVehicleRecordDTO> listDto = listPo.stream().map(m -> m.transPoToDto(InviteVehicleRecordDTO.class))
                .collect(Collectors.toList());
        Map<String, List<InviteVehicleRecordDTO>> map = listDto.stream().collect(Collectors.groupingBy(InviteVehicleRecordDTO::getDealerCode));
        for (Map.Entry<String, List<InviteVehicleRecordDTO>> entry : map.entrySet()) {
            this.allocation(entry.getKey(), entry.getValue(), Boolean.FALSE);
        }
        logger.info("taskAllocationAsync,end:{}", vin);
    }

    /**
     * 自动分配sa
     */
    private void taskAllocation(String createDate, Boolean isInsurance) {
        HashMap<String, List<InviteVehicleRecordDTO>> map = new HashMap<>();
        List<InviteVehicleRecordPO> list = null;
        if (isInsurance) {
            list = inviteVehicleRecordMapper.queryWaitAllocationRecodeForInsurance(createDate);
        } else {
            list = inviteVehicleRecordMapper.queryWaitAllocationRecodeForOther(createDate);
        }
        if (!CommonUtils.isNullOrEmpty(list)) {
            List<InviteVehicleRecordDTO> result = list.stream().map(m -> m.transPoToDto(InviteVehicleRecordDTO.class))
                    .collect(Collectors.toList());
            for (InviteVehicleRecordDTO dto : result) {
                if (map.containsKey(dto.getDealerCode())) {
                    map.get(dto.getDealerCode()).add(dto);
                } else {
                    List<InviteVehicleRecordDTO> array = new ArrayList<>();
                    array.add(dto);
                    map.put(dto.getDealerCode(), array);
                }
            }

            for (Map.Entry<String, List<InviteVehicleRecordDTO>> entry : map.entrySet()) {
                this.allocation(entry.getKey(), entry.getValue(), isInsurance);
            }
        }
    }

    /**
     * 分配sa
     *
     * @param list
     */
    private void allocation(String dealerCode, List<InviteVehicleRecordDTO> list, Boolean isInsurance) {
        LambdaQueryWrapper<InviteSaAllocateRulePO> qu = new QueryWrapper().lambda();
        qu.eq(InviteSaAllocateRulePO::getDealerCode, dealerCode);
        qu.eq(InviteSaAllocateRulePO::getIsDeleted, 0); // 排除软删除
        qu.orderByDesc(InviteSaAllocateRulePO::getCreatedAt); // 根据时间取最后插入的信息
        List<InviteSaAllocateRulePO> pos = inviteSaAllocateRuleMapper.selectList(qu);
        //没有分配规则， 不自动分配
        if (CollectionUtils.isEmpty(pos)) {
            return;
        }
        LambdaQueryWrapper<InviteSaAllocateRuleDetailPO> qde = new QueryWrapper().lambda();
        qde.eq(InviteSaAllocateRuleDetailPO::getDealerCode, dealerCode);
        if (isInsurance) {
            qde.eq(InviteSaAllocateRuleDetailPO::getIsInsurance, 10041001);
        } else {
            qde.eq(InviteSaAllocateRuleDetailPO::getIsOther, 10041001);
        }
        List<InviteSaAllocateRuleDetailPO> users = inviteSaAllocateRuleDetailMapper.selectList(qde);
        //没有可分配sa， 不自动分配
        if (users == null || users.size() == 0) {
            return;
        }
        List<UserInfoDTO> selectSa = new ArrayList<>();
        for (InviteSaAllocateRuleDetailPO user : users) {
            UserInfoDTO u = new UserInfoDTO();
            u.setId(Long.parseLong(user.getSaId()));
            u.setUsername(user.getSaName());
            selectSa.add(u);
        }
        SaSllocateDlrDto saSllocateDlrDto = new SaSllocateDlrDto();
        saSllocateDlrDto.setRuleType(pos.get(0).getRuleType());
        saSllocateDlrDto.setInviteVehicleRecordList(list);
        saSllocateDlrDto.setSelectSa(selectSa);
        inviteVehicleRecordService.saveSaSllocate(saSllocateDlrDto);
    }

    public int partitionComputeDailyAverageMileage(String createDate, Integer page, Integer number, Integer size){
        Integer returnCount = 0;
        Integer partitionSize = size == null ? 1000 : size;
        Integer start = page == null ? 0 : page;
        Integer nThreads = number == null ? 20 : number;
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        long startTimeMillis = System.currentTimeMillis();
        logger.info("DailyMileage,开始计算日均行驶里程,createDate:{},startTimeMillis:{}", createDate, startTimeMillis);
        List<VehicleOwnerVO> list = null;
        while (true){
            Integer begIndex = start * partitionSize;
            logger.info("DailyMileage ,createDate:{},begIndex:{},partitionSize:{}", createDate, begIndex, partitionSize);
            list = reportCommonClient.queryRegularMaintainWithMon(createDate, begIndex, partitionSize);
            //查询开关
            logger.info("DailyMileage, vocSwitch:{}, start:{}", vocSwitch, start);
            if(vocSwitch == 0){
                return returnCount;
            }
            if(list == null || list.size() <= 0){
                logger.info("DailyMileage执行完成");
                break;
            }else {
                start++;
                logger.info("DailyMileage开始执行,start:{},size:{}", start, list.size());
                int i = computeDailyAverageMileage(start,createDate, list, nThreads);
                returnCount += i;
                logger.info("DailyMileage,returnCount:{}", returnCount);
            }
        }
        long endTimeMillis = System.currentTimeMillis();
        long execTime = endTimeMillis - startTimeMillis;
        logger.info("DailyMileage,returnCount:{},endTimeMillis:{},execTime:{}",returnCount, endTimeMillis, execTime);
        return returnCount;
    }

    @Override
    public int partitionGetNotVocVeh(String createDate, Integer page, Integer number, Integer size){
        Integer returnCount = 0;
        Integer partitionSize = size == null ? 10000 : size;
        Integer start = page == null ? 0 : page;
        Integer nThreads = number == null ? 35 : number;
        long startTimeMillis = System.currentTimeMillis();
        logger.info("partitionGetNotVocVeh,开始计算日均行驶里程,createDate:{},startTimeMillis:{}", createDate, startTimeMillis);
        List<VehicleOwnerVO> list = null;
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        while (true){
            Integer begIndex = start * partitionSize;
            Integer endIndex = partitionSize;
            String sql = getSql(createDate, begIndex, endIndex);
            logger.info("partitionGetNotVocVeh ,sql:{}", sql);
            logger.info("partitionGetNotVocVeh ,createDate:{},begIndex:{},partitionSize:{}", createDate, begIndex, partitionSize);
            list = reportCommonClient.queryRegularMaintainWithMon(createDate, begIndex, partitionSize);
            //查询开关
            logger.info("partitionGetNotVocVeh, start:{}", start);
            if(list == null || list.size() == 0){
                logger.info("partitionGetNotVocVeh,执行完成");
                break;
            }else {
                start++;
                logger.info("partitionGetNotVocVeh,开始执行,start:{},size:{}", start, list.size());
                int i = computeDailyAverageMileage(start,createDate, list, nThreads, sql);
                returnCount += i;
                logger.info("partitionGetNotVocVeh,returnCount:{}", returnCount);
            }
        }
        long endTimeMillis = System.currentTimeMillis();
        long execTime = endTimeMillis - startTimeMillis;
        logger.info("partitionGetNotVocVeh,当月工单总分页数,returnCount:{},endTimeMillis:{},execTime:{}",returnCount, endTimeMillis, execTime);
        return returnCount;
    }

    private String getSql(String createDate, Integer begIndex, Integer endIndex) {
        StringBuffer sb = new StringBuffer();
        sb.append("select * from(SELECT ro.vin FROM cyx_repair.tt_repair_order ro  LEFT JOIN vehicle.tm_vehicle b ON ro.vin = b.vin ");
        sb.append("WHERE ro.REPAIR_TYPE_CODE !='P' AND ro.FOR_BALANCE_TIME IS NOT NULL and b.vin is not null ");
        sb.append("AND date_add(ro.FOR_BALANCE_TIME, INTERVAL '1 MONTH') >= STR_TO_DATE(CONCAT('" + createDate + "',' 00:00:00'),'%Y-%m-%d %H:%i:%s') ");
        sb.append("AND ro.FOR_BALANCE_TIME < STR_TO_DATE(CONCAT('" + createDate + "', ' 00:00:00'),'%Y-%m-%d %H:%i:%s') ");
        sb.append("GROUP BY ro.vin ,ro.ro_no ,ro.OWNER_CODE Union All ");

        sb.append("SELECT ro.vin FROM cyx_repair.tt_repair_order_his ro LEFT JOIN vehicle.tm_vehicle b ON ro.vin = b.vin  ");
        sb.append("WHERE  ro.REPAIR_TYPE_CODE !='P' AND ro.FOR_BALANCE_TIME IS NOT NULL and b.vin is not null ");
        sb.append("AND date_add(ro.CREATED_AT, INTERVAL '1 MONTH') >= STR_TO_DATE(CONCAT('" + createDate + "',' 00:00:00'),'%Y-%m-%d %H:%i:%s') ");
        sb.append("AND ro.CREATED_AT < STR_TO_DATE(CONCAT('" + createDate + "', ' 00:00:00'),'%Y-%m-%d %H:%i:%s') ");
        sb.append("GROUP BY ro.vin ,ro.ro_no ,ro.OWNER_CODE ) t order by vin limit " + begIndex + "," + endIndex);
        return sb.toString();
    }

    /**
     * 计算日均行驶里程
     *
     * @param createDate
     * @return
     */
    private int computeDailyAverageMileage(Integer page, String createDate,List<VehicleOwnerVO> list,Integer nThreads,String sql){
        //设置将父线程的HttpServletRequest对象设置共享
        RequestContextHolder.setRequestAttributes(RequestContextHolder.getRequestAttributes(), true);
        long sTime = new Date().getTime();
        int size = list.size();
        logger.info("getNotVocVeh查询获得待计算数量,size:{}", size);
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        String nextMonthDay = this.getNextMonthFristDay(createDate);
        logger.info("getNotVocVeh下月基准时间{}", nextMonthDay);
        List<VehiclePO> upList = Collections.synchronizedList(new ArrayList<>());
        List<VehiclePO> addList = Collections.synchronizedList(new ArrayList<>());
        List<VocMileageVO> lists = Collections.synchronizedList(new ArrayList<>());
        ExecutorService executorService = Executors.newFixedThreadPool(nThreads);
        final CountDownLatch latch = new CountDownLatch(size);
        int num = 0;
        for (VehicleOwnerVO vo : list) {
            int finalNum = num++;
            executorService.submit(() -> {
                try {
                    long startTime = new Date().getTime();
                    //voc车辆跳过
                    if (vo.getIsVoc() != 10041001) {
                        countDailyAverageMileage(vo, vo.getVin(), vo.getInvoiceDate(), upList, addList, lists);
                    }
                    long endTime = new Date().getTime();
                    logger.info("getNotVocVeh,page,num,endTime-startTime:{},{},{}", page, finalNum, endTime - startTime);
                }catch (Exception e){
                    logger.info("getNotVocVeh, Exception:{}", e);
                    dailyMileageLogMapper.SetErrorlog("orderDailyMileage", e.getMessage(), e.getStackTrace()
                            .toString(),"非voc计算日均里程异常");
                } finally {
                    latch.countDown();
                }

            });
        }
        try {
            latch.await();
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("getNotVocVeh:{}", e.getMessage());
        }
        if (null != executorService) {
            executorService.shutdown();
        }
        //批量添加
        int addNum = 0;
        logger.info("getNotVocVeh, addList:{}", addList.size());
        if (addList.size() > 0) {
            addNum = vehicleMapper.insertVocAll(addList);
        }
        //批量修改
        int upNum = 0;
        logger.info("getNotVocVeh, upList:{}", upList.size());
        if (upList.size() > 0) {
            upNum = vehicleMapper.updateVocByIdAll(upList);
        }
        logger.info("getNotVocVeh, addNum:{}, upNum:{}", addNum, upNum);
        logger.info("getNotVocVeh, lists:{}", lists.size());
        if(lists.size() > 0){
            try {
                vocManageService.updateFirstMaintainTask(nextMonthDay, lists, sql);
                vocManageService.updateMaintainTask(nextMonthDay, lists, sql);
            } catch (Exception e) {
                logger.info("getNotVocVeh, Exception e:{}", e);
                throw new ServiceBizException(e);
            }
        }
        long eTime = new Date().getTime();
        logger.info("getNotVocVeh,page,num,endTime-startTime:{},{},{}", page, eTime - sTime);
        return 1;
    }

    /**
     * 计算日均行驶里程
     *
     * @param createDate
     * @return
     */
    private int computeDailyAverageMileage(Integer page, String createDate,List<VehicleOwnerVO> list,Integer nThreads){
        //设置将父线程的HttpServletRequest对象设置共享
        RequestContextHolder.setRequestAttributes(RequestContextHolder.getRequestAttributes(), true);
        int size = list.size();
        logger.info("getAllVocVeh查询获得待计算数量,size:{}", size);
        String nextMonthDay = this.getNextMonthFristDay(createDate);
        logger.info("getAllVocVeh下月基准时间{}", nextMonthDay);
        ExecutorService executorService = Executors.newFixedThreadPool(nThreads);
        final CountDownLatch latch = new CountDownLatch(size);
        int num = 0;
        for (VehicleOwnerVO vo : list) {
            int finalNum = num++;
            executorService.submit(() -> {
                try {
                    long startTime = new Date().getTime();
                    //voc车辆跳过
                    if (vo.getIsVoc() != 10041001) {
                        BigDecimal averageMileage = countDailyAverageMileage(vo, vo.getVin(), vo.getInvoiceDate());
                        if (averageMileage != null) {
                            VehicleOwnerVO vehInfo = new VehicleOwnerVO();
                            vehInfo.setVin(vo.getVin());
                            vehInfo.setInvoiceDate(vo.getInvoiceDate());
                            /**更新建议入场日期*/
                            vocManageService.updateFirstMaintainTask(nextMonthDay, vehInfo, vo.getVin(), averageMileage.doubleValue());
                            vocManageService.updateMaintainTask(nextMonthDay, vehInfo, vo.getVin(), averageMileage.doubleValue());
                        }
                    }
                    long endTime = new Date().getTime();
                    logger.info("getAllVocVeh,page,num,endTime-startTime:{},{},{}", page, finalNum, endTime - startTime);
                }catch (Exception e){
                    dailyMileageLogMapper.SetErrorlog("orderDailyMileage", e.getMessage(), e.getStackTrace()
                            .toString(),"非voc计算日均里程异常");
                } finally {
                    latch.countDown();
                }

            });
        }
        try {
            latch.await();
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("{}", e.getMessage());
        }
        if (null != executorService) {
            executorService.shutdown();
        }
        return 1;
    }

    private String getNextMonthFristDay(String createDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(sdf.parse(createDate));
            cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + 1, 1);
        } catch (Exception e) {

        }
        return sdf.format(cal.getTime());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int computeDailyAverageMileageAll() {
        //查询5万每次
        List<VehiclePO> list = vehicleMapper.getAllVeh();
        // 线程数量
        int maxThreadSize = 5;
        int runsize = 5;
        if (list.size() < runsize) {
            runsize = list.size();
        }
        ThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(maxThreadSize);
        CountDownLatch countDownLatch = new CountDownLatch(runsize);
        List<String> errorList = new ArrayList<>();
        for (VehiclePO po : list) {
            computeDailyAverageMileageThread thread = new computeDailyAverageMileageThread(po, countDownLatch,
                    errorList);
            executor.execute(thread);
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        executor.shutdown();
        return 1;
    }


    /**
     * 多线程处理
     */
    public class computeDailyAverageMileageThread implements Runnable {
        private VehiclePO po;
        private CountDownLatch countDownLatch;
        private List<String> errorVins;

        public computeDailyAverageMileageThread(VehiclePO po, CountDownLatch countDownLatch,
                                                List<String> errorVins) {
            super();
            this.po = po;
            this.countDownLatch = countDownLatch;
            this.errorVins = errorVins;
        }

        @Override
        public void run() {
            try {
                batchUpdateData(po);
            } catch (Exception e) {
                logger.error(">>>多线程处理数据异常:{}", e);
                dailyMileageLogMapper.SetErrorlog("computeAvgMileage", e.getMessage(), e.getStackTrace()
                        .toString(), po.getVin());
                if (errorVins != null) {
                    //发生错误加入集合
                    errorVins.add(po.getVin());

                }
            } finally {
                if (countDownLatch != null) {
                    countDownLatch.countDown();
                }
            }
        }
    }

    /**
     * 更新日均里程
     *
     * @param po
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateData(VehiclePO po) {
        Double dailyAverageMileage = null;
        logger.info("开始查询最近工单,vin{}", po.getVin());
        List<VocMileageVO> list = vehicleMapper.getMileage(po.getVin());
        logger.info("查询结果size{}", list.size());
        int times = 0;
        VocMileageVO one = null;
        VocMileageVO two = null;
        VocMileageVO three = null;
        VocMileageVO four = null;
        for (VocMileageVO vo : list) {
            if (times == 0) {
                one = vo;
                times++;
                logger.info("最近工单里程{},时间{}", vo.getMileageKm(), vo.getGetTime());
            } else if (times == 1) {
                logger.info("检查第二工单时间{}", vo.getGetTime());
                if (this.compareDate(one.getGetTime(), vo.getGetTime())) {
                    two = vo;
                    times++;
                    logger.info("第二工单里程{},时间{}", vo.getMileageKm(), vo.getGetTime());
                } else {
                    logger.info("时间不满足条件{}", vo.getGetTime());
                }
            } else if (times == 2) {
                logger.info("检查第三工单时间{}", vo.getGetTime());
                if (this.compareDate(two.getGetTime(), vo.getGetTime())) {
                    three = vo;
                    times++;
                    logger.info("第三工单里程{},时间{}", vo.getMileageKm(), vo.getGetTime());
                } else {
                    logger.info("时间不满足条件{}", vo.getGetTime());
                }
            } else if (times == 3) {
                logger.info("检查第四工单时间{}", vo.getGetTime());
                if (this.compareDate(three.getGetTime(), vo.getGetTime())) {
                    four = vo;
                    times++;
                    logger.info("第四工单里程{},时间{}", vo.getMileageKm(), vo.getGetTime());
                    break;
                } else {
                    logger.info("时间不满足条件{}", vo.getGetTime());
                }
            }
        }
        //只有本次工单
        if (times == 1) {
            Long days = this.getCompareDate(one.getGetTime(), po.getInvoiceDate());
            if (days == 0) {
                dailyAverageMileage = one.getMileageKm() * 1.0;
            } else {
                dailyAverageMileage = one.getMileageKm() * 1.0 / days;
            }
            logger.info("有一张工单,计算日均里程{}", dailyAverageMileage);
        }
        if (times == 2) {
            dailyAverageMileage = (one.getMileageKm() - two.getMileageKm()) * 1.0 / this.getCompareDate(one
                    .getGetTime(), two
                    .getGetTime());
            logger.info("有二张工单,计算日均里程{}", dailyAverageMileage);
        }
        if (times == 3) {
            dailyAverageMileage = ((one.getMileageKm() - two.getMileageKm()) * 1.0 / this.getCompareDate(one
                    .getGetTime(), two
                    .getGetTime()) + (one.getMileageKm() - three.getMileageKm()) * 1.0 / this.getCompareDate(one
                    .getGetTime(), three
                    .getGetTime())) / 2.0;
            logger.info("有三张工单,计算日均里程{}", dailyAverageMileage);
        }
        if (times == 4) {
            dailyAverageMileage = ((one.getMileageKm() - two.getMileageKm()) * 1.0 / this.getCompareDate(one
                    .getGetTime(), two
                    .getGetTime()) + (one.getMileageKm() - three.getMileageKm()) * 1.0 / this.getCompareDate(one
                    .getGetTime(), three
                    .getGetTime()) + (one.getMileageKm() - four.getMileageKm()) * 1.0 / this.getCompareDate(one
                    .getGetTime(), four
                    .getGetTime()))
                    / 3.0;
            logger.info("有四张工单,计算日均里程{}", dailyAverageMileage);
        }
        logger.info("日均里程计算结果,vin{},invoiceDate{},dailyAverageMileage{}", po.getVin(), po.getInvoiceDate(),
                dailyAverageMileage);
        this.saveDailyMileageLog(po.getVin(), "init", null, po.getInvoiceDate(), one, two, three, four, null,
                new BigDecimal(dailyAverageMileage));
        if (dailyAverageMileage != null && dailyAverageMileage >= 0) {
            po.setDailyAverageMileage(new BigDecimal(dailyAverageMileage));
            vehicleMapper.updateDailyAverageMileageById(po);
        }
        return 1;
    }

    /**
     * 计算日均行驶里程
     *
     * @param createDate
     * @return
     */
    public int computeDailyAverageMileageForInit(String createDate) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        logger.info("开始计算日均行驶里程,createDate{}", createDate);
        List<VehicleOwnerVO> list = reportCommonClient.queryRegularMaintainForAverageMileage(createDate, 1);
        logger.info("查询获得待计算数量,size{}", list.size());
        for (VehicleOwnerVO vo : list) {
            BigDecimal averageMileage = this.countDailyAverageMileage(vo, vo.getVin(), vo.getInvoiceDate());
            //this.updateAdviseInDate(vo, averageMileage);
        }
        return 1;
    }

    /**
     * 关闭线索
     *
     * @param createDate
     * @return
     */
    @Override
    public int closeInvite(String createDate) {
        logger.info("closeInvite,start");
        List<String> listInCode = inviteVehicleRecordService.getWhiteList(CommonConstants.MOD_TYPE_91111015);
        List<String> listCode = inviteVehicleRecordService.getWhiteList(CommonConstants.MOD_TYPE_91111011);
        logger.info("closeInvite,listCode:{},listInCode:{}", listCode, listInCode);
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        List<InviteVehicleTaskPO> list = this.getWaitCloseRecord(createDate);
        Integer sourceType;
        for (InviteVehicleTaskPO task : list) {
            logger.info("closeInvite,task:{}", task);
            sourceType = task.getSourceType();
            if(CollectionUtils.isEmpty(listCode) &&
                    CollectionUtils.isEmpty(listInCode) &&
                    Objects.equals(CommonConstants.CRM_LEAD, sourceType)){
                logger.info("CollectionUtils.isEmpty(listCode) && CollectionUtils.isEmpty(listInCode) && Objects.equals(CommonConstants.CRM_LEAD, sourceType)");
                continue;
            }
            if(listInCode.contains(task.getDealerCode()) &&
                    Objects.equals(CommonConstants.CRM_LEAD, sourceType)){
                logger.info("CollectionUtils.isNotEmpty(listInCode) && listInCode.contains(task.getDealerCode()) && Objects.equals(CommonConstants.CRM_LEAD, sourceType)");
                continue;
            }
            if (listCode.contains(task.getDealerCode())) {
                logger.info("listCode.contains(task.getDealerCode())");
                continue;
            }
            logger.info("closeInvite:关闭线索：{},{}",task.getVin(),task.getId());
            //更新线索关闭
            InviteVehicleRecordPO po = new InviteVehicleRecordPO();
            po.setId(task.getId());
            //po = inviteVehicleRecordMapper.selectById(task.getInviteId());
            po.setOrderStatus(82411003);
            inviteVehicleRecordMapper.updateOrderStatusById(task.getId(), 82411003);
            //this.mergeInvite(po);
            //如果是定首保超时 生成流失预警
            if(task.getInviteType() == 82381001 || task.getInviteType() == 82381002 ) {
                if (po.getItemType() != null && po.getItemType() != 83851005) {
                    task.setItemType(po.getItemType());
                }
                //this.createAlertTask(task, createDate);
                this.createAlertInviteByTask(createDate, task);
            }
            //this.createTaskAgain(task);
        }
        //维护历史线索的验证状态
        try {
            logger.info("closeInvite,维护历史线索的验证状态");
            taskRecordService.markHistoryDataAsValid();
        } catch (Exception e) {
            logger.error("closeInvite,维护历史线索的验证状态,e:",e);
        }
        logger.info("closeInvite,end");
        return 1;
    }

    /**
     * 创建流失预警线索
     *
     * @param vo InviteVehicleTaskPO
     */
    private void createAlertTask(InviteVehicleTaskPO vo, String createDate) {
        InviteVehicleTaskPO po = new InviteVehicleTaskPO();
        po.setVin(vo.getVin());
        po.setLicensePlateNum(vo.getLicensePlateNum());
        po.setDealerCode(vo.getDealerCode());
        po.setName(vo.getName());
        po.setTel(vo.getTel());
        po.setAge(vo.getAge());
        po.setSex(vo.getSex());
        po.setModel(vo.getModel());
        po.setDailyMileage(vo.getDailyMileage());
        po.setDayInAdvance(vo.getDayInAdvance());
        po.setRemindInterval(vo.getRemindInterval());
//        po.setCloseInterval(vo.getCloseInterval());
        po.setCloseTimes(vo.getCloseTimes() + 1);

        po.setIsCreateInvite(0);
        //基准日期为关闭任务的建议进厂日期
        po.setInviteTime(vo.getAdviseInDate());
        //基准日期+ 间隔月
        Date date = DateUtils.parseDateStrToDate(createDate + " 00:00:00","yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        c.setTime(date);
//        c.setTime(po.getInviteTime());
//        c.add(Calendar.MONTH, po.getRemindInterval());
        po.setAdviseInDate(c.getTime());
        c = Calendar.getInstance();
        c.setTime(po.getAdviseInDate());
        //建议进厂时间-90天
        c.add(Calendar.DATE, CommonConstants.UP_DAY);
        po.setCreateInviteTime(c.getTime());

        po.setInviteType(CommonConstants.INVITE_TYPE_XII);
        po.setAdviseInMileage(vo.getAdviseInMileage());
        po.setCloseTimes(0);

        po.setItemType(vo.getItemType());
        po.setItemCode(vo.getItemCode());
        po.setItemName(vo.getItemName());

        inviteVehicleTaskMapper.insert(po);
        //直接下发
        InviteVehicleRecordPO newId = this.createAlertInviteByTask(Utility.getDate(), po);
    }

    /**
     * 创建邀约线索
     *
     * @param po
     * @return
     */
    public InviteVehicleRecordPO createAlertInviteByTask(String createDate, InviteVehicleTaskPO po) {
        // 新增线索下发逻辑
        InviteVehicleRecordPO record = new InviteVehicleRecordPO();
        record.setDealerCode(po.getDealerCode());
        //VCDC下发邀约
        record.setSourceType(1);
        record.setIsMain(1);
        record.setVin(po.getVin());
        record.setLicensePlateNum(po.getLicensePlateNum());
        record.setName(po.getName());
        record.setTel(po.getTel());
        record.setAge(po.getAge());
        record.setSex(po.getSex());
        record.setModel(po.getModel());
        record.setInviteType(CommonConstants.INVITE_TYPE_XII);
        record.setAdviseInDate(po.getAdviseInDate());
        record.setItemType(po.getItemType());
        record.setItemCode(po.getItemCode());
        record.setItemName(po.getItemName());
        record.setLastChangeDate(po.getInviteTime());
        //未完成
        record.setOrderStatus(82411002);
        if (CommonConstants.INVITE_TYPE_I.equals(record.getInviteType())
                || CommonConstants.INVITE_TYPE_VII.equals(record.getInviteType())
                || CommonConstants.INVITE_TYPE_IX.equals(record.getInviteType())) {
            record.setLastInDate(po.getLastMaintainDate());
        } else if (CommonConstants.INVITE_TYPE_V.equals(record.getInviteType())) {
            record.setLastInDate(po.getInviteTime());
        } else if (CommonConstants.INVITE_TYPE_II.equals(record.getInviteType())
                || CommonConstants.INVITE_TYPE_VI.equals(record.getInviteType())
                || CommonConstants.INVITE_TYPE_XII.equals(record.getInviteType())) {
            record.setLastInDate(po.getLastMaintenanceDate());
        }
        //未跟进
        record.setFollowStatus(82401001);
        SimpleDateFormat simdate = new SimpleDateFormat("yyyy-MM-dd");
        try {
            record.setCreatedAt(simdate.parse(createDate));
        } catch (ParseException e) {
            logger.info("时间异常{}", createDate);
        }
        inviteVehicleRecordMapper.insert(record);
        logger.info("流失预警：{},{},{},{},{}",po.getVin(),po.getInviteType(),po.getId(),po.getInviteId(),record.getId());
        po.setInviteId(record.getId());
        po.setId(null);
        this.saveVocInviteVehicleTaskRecordPo(po);
        return record;
    }

    /**
     * 跟据规则再次创建任务
     *
     * @param po
     */
    private void createTaskAgain(InviteVehicleTaskPO vo) {
        //重新生成任务小于两次
        if (vo.getCloseTimes() < 2) {
            InviteVehicleTaskPO po = new InviteVehicleTaskPO();
            po.setVin(vo.getVin());
            po.setLicensePlateNum(vo.getLicensePlateNum());
            po.setDealerCode(vo.getDealerCode());
            po.setName(vo.getName());
            po.setTel(vo.getTel());
            po.setAge(vo.getAge());
            po.setSex(vo.getSex());
            po.setModel(vo.getModel());
            po.setDailyMileage(vo.getDailyMileage());
            po.setInviteType(vo.getInviteType());
            po.setDayInAdvance(vo.getDayInAdvance());
            po.setRemindInterval(vo.getRemindInterval());
            po.setCloseInterval(vo.getCloseInterval());
            po.setCloseTimes(vo.getCloseTimes() + 1);

            po.setIsCreateInvite(0);
            //基准日期为关闭任务的建议进厂日期
            po.setInviteTime(vo.getAdviseInDate());
            //基准日期+ 间隔月
            Calendar c = Calendar.getInstance();
            c.setTime(po.getInviteTime());
            c.add(Calendar.MONTH, po.getRemindInterval());
            po.setAdviseInDate(c.getTime());
            c = Calendar.getInstance();
            c.setTime(po.getAdviseInDate());
            //建议进厂时间-90天
            c.add(Calendar.DATE, CommonConstants.UP_DAY);
            po.setCreateInviteTime(c.getTime());
            //如果是首保，超时改为定保任务
            if (CommonConstants.INVITE_TYPE_I.equals(po.getInviteType())) {
                logger.info("首保超时改为定保任务，vin{}", vo.getVin());
                logger.info("开始查询定保规则，vin{},dealerCode{}", vo.getVin(), vo.getDealerCode());
                List<InviteRulePO> rules = inviteRuleService.getRegularMaintainRule(vo.getDealerCode());
                logger.info("定保规则数量,size{}", rules.size());
                if (rules.size() == 0) {
                    //规则不存在时返回 null
                    return;
                }
                po.setCloseInterval(rules.get(0).getCloseInterval());
                po.setRemindInterval(rules.get(0).getRemindInterval());
                po.setDayInAdvance(rules.get(0).getDayInAdvance());
                po.setInviteType(CommonConstants.INVITE_TYPE_II);
                po.setAdviseInMileage(vo.getAdviseInMileage());
                po.setCloseTimes(0);
            }
            //如果定保
            if (CommonConstants.INVITE_TYPE_II.equals(po.getInviteType())) {
                po.setAdviseInMileage(vo.getAdviseInMileage() == null ? 9000 : vo.getAdviseInMileage() + 9000);
            }

            po.setItemType(vo.getItemType());
            po.setItemCode(vo.getItemCode());
            po.setItemName(vo.getItemName());
            if (vo.getIsVoc() != null && vo.getIsVoc() == 10041001 && CommonConstants.INVITE_TYPE_II.equals(po
                    .getInviteType())) {
                VehicleOwnerVO owner = new VehicleOwnerVO();
                //voc当前里程计算
                owner.setOutMileage(vo.getVocMileage());
                Date adviseInDate = this.setAdviseInDate(owner, vo.getDealerCode(), vo.getAdviseInDate(), vo
                        .getDailyAverageMileage());
                if (adviseInDate != null) {
                    //重新按照获取到的规则设置任务
                    po.setDayInAdvance(owner.getDayInAdvance());
                    po.setRemindInterval(owner.getRemindInterval());
                    po.setCloseInterval(owner.getCloseInterval());
                    po.setAdviseInDate(adviseInDate);
                    po.setAdviseInMileage(owner.getAdviseInMileage());
                    c = Calendar.getInstance();
                    c.setTime(po.getAdviseInDate());
                    //建议进厂时间-90天
                    c.add(Calendar.DATE, CommonConstants.UP_DAY);
                    po.setCreateInviteTime(c.getTime());
                } else {
                    return;
                }
            }
            inviteVehicleTaskMapper.insert(po);
        }
    }


    /**
     * 重新处理合并线索
     *
     * @param po
     */
    @Override
    public void mergeInvite(InviteVehicleRecordPO po) {
        logger.info("待处理的关闭线索，IsMain{}", po.getIsMain());
        //主线索
        if (po.getIsMain() == 1) {
            logger.info("开始查询对应子线索，id{}", po.getId());
            //查询对应子线索
            List<InviteVehicleRecordPO> list = inviteVehicleRecordMapper.queryInviteSubtextByid(po.getId());
            logger.info("查询结果，size{}", list.size());
            if (list != null && list.size() > 0) {
                InviteVehicleRecordPO newMain = null;
                for (InviteVehicleRecordPO item : list) {
                    if (newMain == null) {
                        newMain = item;
                        logger.info("更新新的主线索，id{},type{}", newMain.getId(), newMain.getInviteType());
                        //更新为主线索
                        inviteVehicleRecordMapper.updateMainInvite(newMain.getId());
                        logger.info("复制原主线索跟进记录，新id{},原id{}", newMain.getId(), po.getId());
                        //复制原主线索跟进记录
                        inviteVehicleRecordDetailMapper.insertCopyRecordDetail(newMain.getId(), po.getId());
                    } else {
                        logger.info("更新其他子线索关系，新id{},子线索id{}", newMain.getId(), item.getId());
                        //更新子线索关系
                        inviteVehicleRecordMapper.updateSubtextInvite(newMain.getId(), item.getId());
                    }
                }
            }
        }
        //如果是子线索
        else {
            logger.info("复制原主线索跟进记录，子线索id{},主线索id{}", po.getId(), po.getParentId());
            //复制原主线索跟进记录
            inviteVehicleRecordDetailMapper.insertCopyRecordDetail(po.getId(), po.getParentId());
            logger.info("解绑原来的主子关系，子线索id{}", po.getId());
            //更新为主线索
            inviteVehicleRecordMapper.updateMainInvite(po.getId());

        }
    }


    /**
     * 更新易损件任务线索
     *
     * @param createDate
     */
    @Override
    public int updateInviteVulnerable(String createDate) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        List<InvitePartItemRuleChangedRecordPO> list =
                invitePartItemRuleChangedRecordMapper.getUpdateNoExecute();
        String nextMonthDay = this.getNextMonthFristDay(createDate);
        logger.info("下月基准时间{}", nextMonthDay);
        for (InvitePartItemRuleChangedRecordPO item : list) {
            vocManageService.updateVulnerableTaskForRuleChanged(nextMonthDay, item.getType(), item.getCode(), item
                    .getLastMileageInterval(), item.getId());
            Integer count = inviteVehicleTaskMapper.countVulnerableTaskForRuleChanged(nextMonthDay, item.getType(),
                    item.getCode(), item.getId());
            if (count == 0) {
                item.setUpdateIsExecute(true);
                item.setUpdatedAt(new Date());
                item.setUpdatedBy("1");
                item.setRemark("修改处理完成");
                invitePartItemRuleChangedRecordMapper.updateById(item);
            }
        }
        return 1;
    }

    @Override
    public int updateInviteMaintainByRuleChanged(String createDate) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        this.updateInviteTask(createDate, CommonConstants.INVITE_TYPE_FIXED_WARRANTY);
        return 1;
    }

    @Override
    public int updateInviteFristMaintainByRuleChanged(String createDate) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        this.updateInviteTask(createDate, CommonConstants.INVITE_TYPE_FIRST_GUARANTEE);
        return 1;
    }

    @Override
    public int updateInviteCustomerLossByRuleChanged(String createDate) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        this.updateInviteTask(createDate, CommonConstants.INVITE_TYPE_CUS_LOSS);
        return 1;
    }

    @Override
    public int updateInviteGuaranteeByRuleChanged(String createDate) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        this.updateInviteTask(createDate, CommonConstants.INVITE_TYPE_GUARANTEE);
        return 1;
    }


    /**
     * 由于邀约规则变更更新任务线索
     *
     * @param createDate
     */
    private void updateInviteTask(String createDate, Integer inviteType) {
        List<InviteRuleChangedRecordPO> list =
                inviteRuleChangedRecordMapper.getUpdateNoExecute(inviteType);
        String nextMonthDay = this.getNextMonthFristDay(createDate);
        logger.info("下月基准时间{}", nextMonthDay);
        Integer type = null;
        for (InviteRuleChangedRecordPO item : list) {
            //定保规则修改
            if (inviteType.equals(CommonConstants.INVITE_TYPE_FIXED_WARRANTY)) {
                vocManageService.updateMaintainTaskForRuleChanged(nextMonthDay, item.getDealerCode(), item
                                .getRuleValue(),
                        item.getCloseInterval(), item.getRemindInterval(), item.getInviteRule(), item.getLastRuleValue()
                        , item.getId());
                type = CommonConstants.INVITE_TYPE_II;
                //首保规则修改
            } else if (inviteType.equals(CommonConstants.INVITE_TYPE_FIRST_GUARANTEE)) {
                vocManageService.updateFirstMaintainTaskForRuleChanged(nextMonthDay, item.getInviteRule(), item
                                .getDealerCode(), item.getRuleValue(), item.getCloseInterval(), item
                                .getRemindInterval(),
                        item.getId());
                type = CommonConstants.INVITE_TYPE_I;
                //客户流失规则修改
            } else if (inviteType.equals(CommonConstants.INVITE_TYPE_CUS_LOSS)) {
                vocManageService.updateCustomerLossTaskForRuleChanged(nextMonthDay, item.getDealerCode(), item
                        .getRuleValue(), item.getCloseInterval(), item.getRemindInterval(), item.getId());
                type = CommonConstants.INVITE_TYPE_VI;
                //保修规则修改
            } else if (inviteType.equals(CommonConstants.INVITE_TYPE_GUARANTEE)) {
                vocManageService.updateGuaranteeTaskForRuleChanged(nextMonthDay, item.getDealerCode(), item
                        .getRuleValue(), item.getCloseInterval(), item.getRemindInterval(), item.getId());
                type = CommonConstants.INVITE_TYPE_IX;
            }
            Integer count = inviteVehicleTaskMapper.countWaitUpdateNoExecute(
                    nextMonthDay, item.getDealerCode(), item.getId(), type);
            if (count == 0) {
                item.setUpdateIsExecute(true);
                item.setUpdatedAt(new Date());
                item.setUpdatedBy("1");
                item.setRemark("修改处理完成");
                inviteRuleChangedRecordMapper.updateById(item);
            }
        }
    }


    /**
     * 查询超时关闭
     *
     * @param createDate
     * @param type
     * @return
     */
    private List<InviteVehicleTaskPO> getWaitCloseRecord(String createDate) {
        return inviteVehicleRecordService.getWaitCloseRecord(createDate);
    }


    /**
     * 更新首保的建议进厂日期
     *
     * @param vo
     * @param averageMileage
     */
    private void updateAdviseInDate(VehicleOwnerVO vo, Double averageMileage) {
        if (averageMileage == null || averageMileage == 0) {
            //日均里程未0 跳过
            return;
        }
        //更新首保任务建议进厂日期
        //查询首保里程规则
        InviteRulePO rule = inviteRuleMapper.getInvitationDlrRule(vo.getDealerCode(), 82031002, 82041002);
        if (rule == null || rule.getIsUse() == null || rule.getIsUse() == 0) {
            //不存在首保里程规则，跳过
            return;
        }
        //查询未下发线索的首保任务
        InviteVehicleTaskPO po = inviteVehicleTaskMapper.queryFirstMaintainTask(vo.getVin());
        if (po != null) {
            int days = (int) (rule.getRuleValue() / averageMileage);
            Calendar c = Calendar.getInstance();
            c.setTime(po.getInviteTime());
            c.add(Calendar.DATE, days);
            //如果里程间隔计算的建议进厂日期较早，更新建议进厂日期
            if (c.getTime().compareTo(po.getAdviseInDate()) < 0) {
                po.setAdviseInDate(c.getTime());
                inviteVehicleTaskMapper.updateById(po);
            }
        }
    }


    /**
     * 根据邀约任务创建邀约线索
     *
     * @param createDate
     * @return
     */
    @Override
    public int createInviteByTask(String createDate) {
        //定时任务开始 设置redis 过期时间25分钟
        redisClient.set("createInviteByTask","1",1000*60*25);
        try {
            if (StringUtils.isNullOrEmpty(createDate)) {
                //如果未传时间取当前时间
                createDate = Utility.getDate();
            }
            //邀约去重规则
            Integer type1 = null;
            //邀约合并规则
            Integer type2 = null;
            //邀约易损件生成规则
            Integer type3 = null;
            //邀约易损件合并规则
            Integer type4 = null;
            //查询去重规则
            List<InviteDuplicateRemovalRuleDTO> list = inviteDuplicateRemovalRuleService.selectListBySqlNew(new
                    InviteDuplicateRemovalRuleDTO());
            for (InviteDuplicateRemovalRuleDTO dto : list) {
                if (dto.getRuleType() == 1) {
                    type1 = dto.getRule();
                } else if (dto.getRuleType() == 2) {
                    type2 = dto.getRule();
                } else if (dto.getRuleType() == 3) {
                    type3 = dto.getRule();
                } else if (dto.getRuleType() == 4) {
                    type4 = dto.getRule();
                }
            }
            //this.createQb(createDate,type2);
            // 查询白名单
            List<String> whiteList = inviteVehicleRecordService.getWhiteList(CommonConstants.MOD_TYPE_91111011);
            // 初始化白名单
            List<String> inWhiteList = inviteVehicleRecordService.getWhiteList(CommonConstants.MOD_TYPE_91111015);
            if(CollectionUtils.isNotEmpty(whiteList) || CollectionUtils.isNotEmpty(inWhiteList)){
                //首定保下发
                this.createMaintainNew(createDate, type2);
            }
            //流失下发
            this.createCustomerLoss(createDate, type2);
            ////易损易耗迁移至原厂服务提醒
            //this.createVulnerable(createDate, type4);
            //this.createInsurance(createDate);
            this.createGuarantee(createDate, type2);

            //续保类型 自动分配
            //this.taskAllocation(createDate, true);
            //定首保及其它类型 自动分配
            this.taskAllocation(createDate, false);
        }catch (Exception e){
            logger.error("根据邀约任务创建邀约线索异常",e);
            redisClient.del("createInviteByTask");
        }finally {
            redisClient.del("createInviteByTask");
        }
        try {
            //补充扩展数据
            inviteVehicleRecordService.batchInsertExtendedData();
            //初始化线索的验证状
            inviteVehicleRecordMapper.updateVerifyStatus();
        }catch (Exception e){
            logger.error("补充扩展数据异常",e);
        }
        return 1;
    }

    /**
     * 根据邀约任务创建邀约线索
     *
     * @param createDate
     * @return
     */

    public int createInviteByTaskForInit(String createDate) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        //邀约去重规则
        Integer type1 = null;
        //邀约合并规则
        Integer type2 = null;
        //邀约易损件生成规则
        Integer type3 = null;
        //邀约易损件合并规则
        Integer type4 = null;
        //查询去重规则
        List<InviteDuplicateRemovalRuleDTO> list = inviteDuplicateRemovalRuleService.selectListBySql(new
                InviteDuplicateRemovalRuleDTO());
        for (InviteDuplicateRemovalRuleDTO dto : list) {
            if (dto.getRuleType() == 1) {
                type1 = dto.getRule();
            } else if (dto.getRuleType() == 2) {
                type2 = dto.getRule();
            } else if (dto.getRuleType() == 3) {
                type3 = dto.getRule();
            } else if (dto.getRuleType() == 4) {
                type4 = dto.getRule();
            }
        }
        //this.createQb(createDate,type2);
        this.createMaintain(createDate, type2);
        //this.createVulnerable(createDate,type4);
        this.createInsurance(createDate);
        this.createCustomerLoss(createDate, type2);
        //续保类型 自动分配
        //this.taskAllocation(createDate, true);
        //定首保及其它类型 自动分配
        //this.taskAllocation(createDate, false);
        return 1;
    }


    /**
     * 创建召回线索
     *
     * @param createDate
     */
    private void createQb(String createDate, Integer mergeRule) {
        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.queryQbTask(createDate);
        for (InviteVehicleTaskPO po : list) {
            //创建邀约线索
            InviteVehicleRecordPO newId = this.createInviteByTask(createDate, po);
            List<InviteVehicleRecordPO> records = this.queryInviteRecordForTask(po.getDealerCode(), newId
                    .getAdviseInDate(), po
                    .getVin(), mergeRule, newId.getId());
            this.mergeInvite(records, newId);
        }
    }


    /**
     * 创建首保定保线索
     *
     * @param createDate
     */
    private void createMaintain(String createDate, Integer mergeRule) {
        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.queryMaintainTask(createDate);
        for (InviteVehicleTaskPO po : list) {
            try{
                //异常标识根据经销商代码带入下次线索
                InviteVehicleRecordPO ivrp = inviteVehicleRecordMapper.getLatestError(po.getDealerCode(),po.getVin());
                if(ivrp != null && ivrp.getItemType() != null && ivrp.getItemType() != 83851005) {
                        po.setItemType(ivrp.getItemType());
                }
                //创建邀约线索
                InviteVehicleRecordPO newId = this.createInviteByTask(createDate, po);
                List<InviteVehicleRecordPO> records = this.queryInviteRecordForTask(po.getDealerCode(), newId
                        .getAdviseInDate(), po
                        .getVin(), mergeRule, newId.getId());
                this.mergeInvite(records, newId);
            }catch (Exception e){
                logger.error("创建首保定保线索异常:vin:[{}],po:[{}],Exception:[{}],", po.getVin(),JSON.toJSONString(po),e);
            }
        }
    }
    /**
     * 创建首保定保线索
     *
     * @param createDate
     */
    private void createMaintainNew(String createDate, Integer mergeRule) {
        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.queryMaintainTask(createDate);
        if(list!=null&& list.size()>0) {
            logger.info("创建定保线索：list{}", list.size());
            Map<String, List<InviteVehicleTaskPO>> ma = new HashMap<>();
            for (InviteVehicleTaskPO inviteVehicleTaskPO : list) {
                if (inviteVehicleTaskPO.getInviteType().intValue() == 82381002) {
                    if (StringUtils.isNullOrEmpty(ma.get(inviteVehicleTaskPO.getVin() + DateUtils.dateToString(inviteVehicleTaskPO.getAdviseInDate(),DateUtils.PATTERN_YYYY_MM_DD) + inviteVehicleTaskPO.getInviteType()))) {
                        List<InviteVehicleTaskPO> ax = new ArrayList<>();
                        ax.add(inviteVehicleTaskPO);
                        ma.put(inviteVehicleTaskPO.getVin() + DateUtils.dateToString(inviteVehicleTaskPO.getAdviseInDate() ,DateUtils.PATTERN_YYYY_MM_DD)  + inviteVehicleTaskPO.getInviteType(), ax);
                    } else {
                        List<InviteVehicleTaskPO> ax = ma.get(inviteVehicleTaskPO.getVin() +  DateUtils.dateToString(inviteVehicleTaskPO.getAdviseInDate(),DateUtils.PATTERN_YYYY_MM_DD)  + inviteVehicleTaskPO.getInviteType());
                        ax.add(inviteVehicleTaskPO);
                        ma.put(inviteVehicleTaskPO.getVin() +  DateUtils.dateToString(inviteVehicleTaskPO.getAdviseInDate(),DateUtils.PATTERN_YYYY_MM_DD)  + inviteVehicleTaskPO.getInviteType(), ax);
                    }
                }
            }
            Map<String, String> map = new HashMap<>();
            Map<String, Map<String, InviteVehicleRecordPO>> map2 = new HashMap<>();
            Map<String, String> map2x = new HashMap<>();
            for (InviteVehicleTaskPO po : list) {
                logger.info("createMaintainNew,po:{}",po);
                try {
                    //异常标识根据经销商代码带入下次线索
                    InviteVehicleRecordPO ivrp = inviteVehicleRecordMapper.getLatestError(po.getDealerCode(), po.getVin());
                    if (ivrp != null && ivrp.getItemType() != null && ivrp.getItemType() != 83851005) {
                            po.setItemType(ivrp.getItemType());
                    }
                    //创建邀约线索
                    if (po.getInviteType() != null && po.getInviteType().intValue() == 82381002) {
                        fixedWarranty(createDate, po,ma,map2,map,mergeRule,map2x);
                    } else {
                        //首保白名单
                        List<String> listCode = inviteVehicleRecordService.
                                doWhitelistDetermination(Lists.newArrayList(po.getDealerCode()), po.getVin(), po.getInviteType(), Boolean.FALSE);
                        if(CollectionUtils.isEmpty(listCode)){
                            logger.info("createInviteByTaskNew,CollectionUtils.isEmpty(listCode)");
                            //关闭线索,不再下发
                            po.setIsCreateInvite(CommonConstants.NOT_GENERATING);
                            po.setInvalidReason("白名单关闭任务");
                            inviteVehicleTaskMapper.updateById(po);
                            continue;
                        }
                        InviteVehicleRecordPO newId = this.createInviteByTask(createDate, po);
                        List<InviteVehicleRecordPO> records = this.queryInviteRecordForTask(po.getDealerCode(), newId
                                .getAdviseInDate(), po
                                .getVin(), mergeRule, newId.getId());
                        this.mergeInvite(records, newId);
                    }
                } catch (Exception e) {
                    logger.error("创建首保定保线索异常:vin:[{}],po:[{}],Exception:[{}],", po.getVin(), JSON.toJSONString(po), e);
                }
            }
        }
    }
//判断是否为VOC
    private int checkVoc(String vin) {
        return vocFunctionalStatusRecordService.selectByVin(vin);
    }
    //判断是否为VOC转非VOC
    private int checkVocToUnVoc(String vin) {
        return  vocFunctionalStatusRecordService.selectIsStatusByVin(vin);
    }
    // 获取新的建议进厂日期
    private  Date getAd (Date ad){
        if(ad==null){
            return DateUtils.getDate();
        }
        //下发时间超过进厂时间时,更新进厂时间取当前时间,没有超过则不变
        if(DateUtils.comDayto(ad)){
            return ad ;
        }else{
            DateUtils.getDate();
        }
        return  ad ;
    }

    private void fixedWarranty(String createDate, InviteVehicleTaskPO po, Map<String, List<InviteVehicleTaskPO>> ma, Map<String, Map<String, InviteVehicleRecordPO>> map2, Map<String, String> map, Integer mergeRule, Map<String, String> map2x) {
//         QueryWrapper queryWrapper =    new   QueryWrapper<InviteVehicleTaskPO>();
//        queryWrapper.eq("vin",po.getVin());
//        queryWrapper.eq("invite_type",CommonConstants.INVITE_TYPE_VI);
//        queryWrapper.eq("dealer_code",po.getDealerCode());
//        queryWrapper.last("limit 1");
//        queryWrapper.orderByDesc(po.getId());
        logger.info("fixedWarranty线索下发vin:{}",po.getVin());
        logger.info("fixedWarranty线索下发getDealerCode:{}",po.getDealerCode());
        logger.info("fixedWarranty线索下发vin:{}",po.getVin());
        logger.info("fixedWarranty查询待关闭预警线索，ma:{}",JSONObject.toJSON(ma));
        List<InviteVehicleTaskPO> list =  inviteVehicleTaskMapper.selectListByVinAndInviteTypeAndDealerCodeOrderById(po.getVin(),CommonConstants.INVITE_TYPE_VI,po.getDealerCode());
        if (StringUtils.isNullOrEmpty(map.get(po.getVin() +   DateUtils.dateToString( po.getAdviseInDate() ,DateUtils.PATTERN_YYYY_MM_DD)  + po.getInviteType()))) {
            //创建邀约线索
            InviteVehicleTaskPO pox =  new  InviteVehicleTaskPO();
            BeanMapperUtil.copyProperties(po,pox);
            InviteVehicleRecordPO newId = this.createInviteByTaskNew(createDate, pox, map2);
            if(newId == null){
                logger.info("fixedWarranty,newId is null");
                return;
            }
            logger.info("fixedWarranty线索下发获取的{},最近2条经销商newId：{}",po.getVin(),JSONObject.toJSON(newId));
            List<InviteVehicleRecordPO> records = this.queryInviteRecordForTask(newId.getDealerCode(), newId
                    .getAdviseInDate(), po
                    .getVin(), mergeRule, newId.getId());
            this.mergeInvite(records, newId);
            createCustomerLossTaskfixedWarranty(po,newId.getDealerCode(),po.getVin(),createDate,mergeRule,list);
            map2x.put(po.getVin() +  DateUtils.dateToString( po.getAdviseInDate() ,DateUtils.PATTERN_YYYY_MM_DD) + po.getInviteType(),newId.getDealerCode());
            //第2条线索
            Map<String, InviteVehicleRecordPO> mapx = map2.get(po.getVin() +  DateUtils.dateToString( po.getAdviseInDate() ,DateUtils.PATTERN_YYYY_MM_DD) + po.getInviteType());
            if(mapx !=null  &&  mapx.size()>0){
                logger.info("fixedWarranty线索下发线索2条：{}",JSONObject.toJSON(mapx));
                for (String key : mapx.keySet()) {
                    List<InviteVehicleRecordPO> records2 = this.queryInviteRecordForTask( mapx.get(key).getDealerCode(),  mapx.get(key)
                            .getAdviseInDate(), po
                            .getVin(), mergeRule,  mapx.get(key).getId());
                    this.mergeInvite(records2,  mapx.get(key));
                    if(ma.get(po.getVin() +   DateUtils.dateToString( po.getAdviseInDate() ,DateUtils.PATTERN_YYYY_MM_DD)+ po.getInviteType()).size()==1){
                        InviteVehicleTaskPO po2 = new InviteVehicleTaskPO();
                        BeanMapperUtil.copyProperties(po,po2,"id");
                        po2.setId(null);
                        po2.setDealerCode(mapx.get(key).getDealerCode());
                        po2.setInviteId(mapx.get(key).getId());
                        po2.setIsCreateInvite(1);
                        inviteVehicleTaskMapper.insert(po2);
                        logger.info("fixedWarranty3167首保，定保，流失客户线索1：{}，{}，{}，{}",po2.getVin(),po2.getInviteType(),po2.getId(),po2.getInviteId());
                        this.saveVocInviteVehicleTaskRecordPo(po2);
                        createCustomerLossTaskfixedWarranty(null,mapx.get(key).getDealerCode(),po.getVin(),createDate,mergeRule,list);
                    }
                }
            }
            map.put(po.getVin()+ DateUtils.dateToString( po.getAdviseInDate()  ,DateUtils.PATTERN_YYYY_MM_DD)+po.getInviteType(),po.getVin()+DateUtils.dateToString( po.getAdviseInDate()  ,DateUtils.PATTERN_YYYY_MM_DD)+po.getInviteType());
        } else {
            //如果任务2条以上 则判断 线索是否2条
            Map<String, InviteVehicleRecordPO> mapx = map2.get((po.getVin() + DateUtils.dateToString( po.getAdviseInDate()  ,DateUtils.PATTERN_YYYY_MM_DD) + po.getInviteType()));
            logger.info("fixedWarranty创建首保定保线索：mapx{}",JSONObject.toJSON(mapx));
            if (mapx != null && mapx.size() > 0) {
                //线索为二条时 ，获取 缓存的线索信息 更新当前任务。
                //更新邀约任务
                String dealerCode = null;
                for (String key : mapx.keySet()) {
                    InviteVehicleTaskPO po2 = new InviteVehicleTaskPO();
                    BeanMapperUtil.copyProperties(po,po2);
                    po2.setDealerCode(mapx.get(key).getDealerCode());
                    po2.setInviteId(mapx.get(key).getId());
                    po2.setIsCreateInvite(1);
                    inviteVehicleTaskMapper.updateById(po2);
                    this.updateVocInviteVehicleTaskRecordPo(po2);
                    logger.info("fixedWarranty3190首保，定保，流失客户线索1：{}，{}，{}，{}",po2.getVin(),po2.getInviteType(),po2.getId(),po2.getInviteId());
                  String lastCode = map2x.get(po.getVin() + DateUtils.dateToString( po.getAdviseInDate()  ,DateUtils.PATTERN_YYYY_MM_DD) + po.getInviteType());
                  if(!lastCode.equals(po.getDealerCode())){
                      if(po.getDealerCode().equals(mapx.get(key).getDealerCode())){
                          //查询po.getDealerCode()
                       List<InviteVehicleTaskPO>  pox = inviteVehicleTaskMapper.selectAllByVinAndInviteTypeAndDealerCode(po.getDealerCode(),po.getVin(),CommonConstants.INVITE_TYPE_VI);
                       if(pox!=null && pox.size()>0){
                       }else{
                           createCustomerLossTaskfixedWarranty(null,mapx.get(key).getDealerCode(),po.getVin(),createDate,mergeRule,list);
                       }
                      }else{
                          createCustomerLossTaskfixedWarranty(po,mapx.get(key).getDealerCode(),po.getVin(),createDate,mergeRule,list);
                      }
                  }else{
                      createCustomerLossTaskfixedWarranty(null,mapx.get(key).getDealerCode(),po.getVin(),createDate,mergeRule,list);
                  }
                    dealerCode = mapx.get(key).getDealerCode();
                }
                mapx.remove(dealerCode);
                map2.put(po.getVin()+DateUtils.dateToString( po.getAdviseInDate() ,DateUtils.PATTERN_YYYY_MM_DD)+po.getInviteType(),mapx);
            } else {
                //线索为一条时 关闭当前任务
                //更新邀约任务
                if(ma.get(po.getVin() +   DateUtils.dateToString( po.getAdviseInDate() ,DateUtils.PATTERN_YYYY_MM_DD)+ po.getInviteType()).size()>2){
                    po.setIsCreateInvite(2);
                    po.setInvalidReason("新进厂关闭任务");
                    inviteVehicleTaskMapper.updateById(po);
                }else{
                    String lastCode = map2x.get(po.getVin() + DateUtils.dateToString( po.getAdviseInDate()  ,DateUtils.PATTERN_YYYY_MM_DD) + po.getInviteType());
                    if(!lastCode.equals(po.getDealerCode())){
                        po.setIsCreateInvite(2);
                        po.setInvalidReason("新进厂关闭任务");
                        inviteVehicleTaskMapper.updateById(po);
                        createCustomerLossTaskfixedWarranty(po,null,po.getVin(),createDate,mergeRule,list);
                    }else{
                        po.setIsCreateInvite(2);
                        po.setInvalidReason("新进厂关闭任务");
                        inviteVehicleTaskMapper.updateById(po);
                    }

                }

            }
        }
    }


    /**
     *  @param po  原经销商信息
     * @param dealerCode 新经销商编码
     * @param vin
     * @param list
     */
    private void createCustomerLossTaskfixedWarranty(InviteVehicleTaskPO po, String dealerCode, String vin, String createDate, Integer mergeRule, List<InviteVehicleTaskPO> list) {
        if(po==null){
            if (!StringUtils.isNullOrEmpty(dealerCode)) {
                if (list != null && list.size() > 0) {
                    InviteVehicleTaskPO po2 = new InviteVehicleTaskPO();
                    BeanMapperUtil.copyProperties(list.get(0), po2, "id");
                    po2.setId(null);
                    po2.setCreatedAt(null);
                    po2.setDealerCode(dealerCode);
                    po2.setIsCreateInvite(0);
                    po2.setInviteId(null);
                    po2.setInvalidReason(null);
                    inviteVehicleTaskMapper.insert(po2);
                    logger.info("3256首保，定保，流失客户线索1：{}，{}，{}，{}",po2.getVin(),po2.getInviteType(),po2.getId(),po2.getInviteId());
                    this.saveVocInviteVehicleTaskRecordPo(po2);
                }
            }
        }
        else{
            if(!StringUtils.isEquals(po.getDealerCode(),dealerCode)) {
                //流失线索下发时关闭预警线索
                logger.info("查询待关闭预警线索，vin{}", po.getVin());
                logger.info("查询待关闭预警线索，getDealerCode:{},dealerCode:{}", po.getDealerCode(),dealerCode);
                List<InviteVehicleRecordPO> alertlist = this.getWaitCloseAlertRecord(po.getDealerCode(), po.getVin());
                logger.info("查询结果，size{}", alertlist.size());
                logger.info("查询待关闭预警线索：{}",JSONObject.toJSON(alertlist));
                logger.info("查询待关闭预警线索list：{}",JSONObject.toJSON(list));
                for (InviteVehicleRecordPO item : alertlist) {
                    item = inviteVehicleRecordMapper.selectById(item.getId());
                    //完成
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_I);
                    item.setOrderFinishDate(new Date());
                    item.setFinishDealerCode(po.getDealerCode());
                    inviteVehicleRecordMapper.updateById(item);
                    this.mergeInvite(item);
                }
                logger.info("关闭未下发的流失预警，vin{},DealerCode{}", po.getVin(), po.getDealerCode());
                this.newUpdateTaskCloseById(po.getDealerCode(), CommonConstants.INVITE_TYPE_XII, po.getVin());
                //保养关闭所有流失线索
                List<InviteVehicleRecordPO> listAll = this.getWaitCloseLostRecord(po.getDealerCode(), vin);
                logger.info("查询结果，size{}", listAll.size());
                for (InviteVehicleRecordPO item : listAll) {
                    item = inviteVehicleRecordMapper.selectById(item.getId());
                    //完成
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_I);
                    item.setOrderFinishDate(new Date());
                    item.setFinishDealerCode(po.getDealerCode());
                    inviteVehicleRecordMapper.updateById(item);
                    this.mergeInvite(item);
                }
                logger.info("关闭未下发的流失任务，vin{},DealerCode{}", vin, po.getDealerCode());
                this.newUpdateTaskCloseById(po.getDealerCode(), CommonConstants.INVITE_TYPE_VI, po.getVin());
                //创建任务
                //删除任务下的告警和流失客户，不需要新增流失客户
                if (!StringUtils.isNullOrEmpty(dealerCode)) {
                    if (list != null && list.size() > 0) {
                        InviteVehicleTaskPO po2 = new InviteVehicleTaskPO();
                        BeanMapperUtil.copyProperties(list.get(0), po2, "id");
                        po2.setId(null);
                        po2.setCreatedAt(null);
                        po2.setDealerCode(dealerCode);
                        po2.setIsCreateInvite(0);
                        po2.setInviteId(null);
                        po2.setInvalidReason(null);
                        inviteVehicleTaskMapper.insert(po2);
                        logger.info("3308首保，定保，流失客户线索1：{}，{}，{}，{}",po2.getVin(),po2.getInviteType(),po2.getId(),po2.getInviteId());
                        this.saveVocInviteVehicleTaskRecordPo(po2);
                    }
                }else{
                    List<InviteVehicleTaskPO> list1 =  inviteVehicleTaskMapper.selectAllByVinAndDealerCode(po.getDealerCode(),po.getVin());
                    if(list1!=null  &&  list1.size()>0){
                        if (list != null && list.size() > 0) {
                            InviteVehicleTaskPO po2 = new InviteVehicleTaskPO();
                            BeanMapperUtil.copyProperties(list.get(0), po2, "id");
                            po2.setId(null);
                            po2.setCreatedAt(null);
                            po2.setDealerCode(po.getDealerCode());
                            po2.setIsCreateInvite(0);
                            po2.setInviteId(null);
                            po2.setInvalidReason(null);
                            inviteVehicleTaskMapper.insert(po2);
                            logger.info("3324首保，定保，流失客户线索1：{}，{}，{}，{}",po2.getVin(),po2.getInviteType(),po2.getId(),po2.getInviteId());
                            this.saveVocInviteVehicleTaskRecordPo(po2);
                        }
                    }
                }
            }else{
                InviteVehicleTaskPO po2z =    list.get(0);
                po2z.setIsCreateInvite(0);
                po2z.setInvalidReason(null);
                inviteVehicleTaskMapper.updateById(po2z);
            }
        }

    }

    /**
     * 创建保险到期邀约线索
     *
     * @param createDate
     * @param duplicateRule
     * @param mergeRule
     */
    private void createInsurance(String createDate) {
        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.queryInsuranceTask(createDate);
        for (InviteVehicleTaskPO po : list) {
            //创建邀约线索
            InviteVehicleRecordPO newId = this.createInviteByTask(createDate, po);
        }
    }


    /**
     * 创建保修到期邀约线索
     *
     * @param createDate
     * @param duplicateRule
     * @param mergeRule
     */
    private void createGuarantee(String createDate, Integer mergeRule) {
        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.queryGuaranteeTask(createDate);
        for (InviteVehicleTaskPO po : list) {
            //创建邀约线索
            InviteVehicleRecordPO newId = this.createInviteByTask(createDate, po);
            List<InviteVehicleRecordPO> records = this.queryInviteRecordForTask(po.getDealerCode(), newId
                    .getAdviseInDate(), po.getVin(), mergeRule, newId.getId());
            this.mergeInvite(records, newId);
        }
    }

    /**
     * 创建客户流失线索
     *
     * @param createDate
     * @param mergeRule
     */
    private void createCustomerLoss(String createDate, Integer mergeRule) {
        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.queryCustomerLoss(createDate);
        for (InviteVehicleTaskPO po : list) {
            if(ObjectUtil.isNull(po)){
                logger.info("createCustomerLoss, ObjectUtil.isNull(po)");
                continue;
            }

            //白名单
            List<String> listCode = inviteVehicleRecordService.
                    doWhitelistDetermination(Lists.newArrayList(po.getDealerCode()), po.getVin(), po.getInviteType(), Boolean.FALSE);
            if (CollectionUtils.isEmpty(listCode)) {
                logger.info("createCustomerLoss,CollectionUtils.isEmpty(listCode)");
                //关闭线索,不再下发
                po.setIsCreateInvite(CommonConstants.NOT_GENERATING);
                po.setInvalidReason("白名单关闭任务");
                inviteVehicleTaskMapper.updateById(po);
                continue;
            }
            //更正流失下发时间
            Date correctLossCreateTime = this.fixLossAdviseInDate(po);
            if (Objects.nonNull(correctLossCreateTime) && DateUtil.compare(correctLossCreateTime, DateUtil.parse(Utility.getDate())) > 0) {
                logger.info("fixed loss advise in date continue");
                continue;
            }

            //异常标识根据经销商代码带入下次线索
                InviteVehicleRecordPO ivrp = inviteVehicleRecordMapper.getLatestError(po.getDealerCode(), po.getVin());
                if (ivrp != null && ivrp.getItemType() != null && ivrp.getItemType() != 83851005) {
                        po.setItemType(ivrp.getItemType());
                }

                //创建邀约线索
                InviteVehicleRecordPO newId = this.createInviteByTask(createDate, po);
                this.savelossDataRecordService(newId);
                //流失线索下发时关闭预警线索
                logger.info("createCustomerLoss,查询待关闭预警线索，vin{}", po.getVin());
                List<InviteVehicleRecordPO> alertlist = this.getWaitCloseAlertRecord(po.getDealerCode(), po.getVin());
                logger.info("createCustomerLoss,查询结果，size{}", alertlist.size());
                for (InviteVehicleRecordPO item : alertlist) {
                    item = inviteVehicleRecordMapper.selectById(item.getId());
                    //完成
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_III);
                    item.setOrderFinishDate(new Date());
                    item.setFinishDealerCode(po.getDealerCode());
                    inviteVehicleRecordMapper.updateById(item);
                    this.mergeInvite(item);
                }
                logger.info("createCustomerLoss,关闭未下发的流失预警，vin{},DealerCode{}", po.getVin(), po.getDealerCode());
                this.updateTaskCloseById(po.getDealerCode(), CommonConstants.INVITE_TYPE_XII, po.getVin());


            List<InviteVehicleRecordPO> records = this.queryInviteRecordForTask(po.getDealerCode(), newId
                    .getAdviseInDate(), po
                    .getVin(), mergeRule, newId.getId());
            this.mergeInvite(records, newId);
        }
    }



    /**
     * 更正流失下发时间
     *
     */
    private Date fixLossAdviseInDate(InviteVehicleTaskPO po) {
        //流失线索下发-建议进厂日期校验
        InviteVehicleRecordPO checkRecord = inviteVehicleRecordMapper.selectOne(
            new LambdaQueryWrapper<InviteVehicleRecordPO>().in(InviteVehicleRecordPO::getInviteType,
                    CollUtil.newArrayList(CommonConstants.INVITE_TYPE_I, CommonConstants.INVITE_TYPE_II))
                .eq(InviteVehicleRecordPO::getVin, po.getVin())
                .eq(InviteVehicleRecordPO::getDealerCode, po.getDealerCode())
                .orderByDesc(InviteVehicleRecordPO::getId)
                .last("limit 1"));
        if (Objects.isNull(checkRecord)) {
            logger.info("首定保线索不存在");
            return null;
        }
        InviteVehicleTaskPO checkTask = inviteVehicleTaskMapper.selectOne(
            new LambdaQueryWrapper<InviteVehicleTaskPO>().eq(InviteVehicleTaskPO::getInviteId, checkRecord.getId()).last("limit 1"));
        if (Objects.isNull(checkTask) || Objects.isNull(checkTask.getInviteTime()) || !Objects.equals(checkRecord.getOrderStatus(),
            CommonConstants.ORDER_STATUS_III)) {
            logger.info("首定保线索不符合更新要求");
            return null;
        }
        DateTime correctLossInAdvise = DateUtil.offsetMonth(checkTask.getInviteTime(), 18);
        DateTime correctLossCreateTime = DateUtil.offsetMonth(correctLossInAdvise, -1);
        logger.info("correct loss create time old:{},new:{}",po.getAdviseInDate(),correctLossInAdvise);
        if (DateUtil.compare(correctLossInAdvise, po.getAdviseInDate()) > 0) {
            inviteVehicleTaskMapper.update(null, new LambdaUpdateWrapper<InviteVehicleTaskPO>().eq(InviteVehicleTaskPO::getId, po.getId())
                .set(InviteVehicleTaskPO::getAdviseInDate, correctLossInAdvise)
                .set(InviteVehicleTaskPO::getCreateInviteTime, correctLossCreateTime));
        }
        return correctLossCreateTime;
    }

    private void savelossDataRecordService(InviteVehicleRecordPO po) {
        saveLoss(po.getVin(), po.getDealerCode(), po.getCreatedAt(),po.getId());
    }

    /**
     * 合并线索
     *
     * @param records
     */
    private void mergeInvite(List<InviteVehicleRecordPO> records, InviteVehicleRecordPO newpo) {
        //只有一条不合并
        if (records.size() == 0) {
            return;
        }
        //如果是首保 定保 ，做为主线索
        if (newpo.getInviteType().equals(CommonConstants.INVITE_TYPE_I) ||
                newpo.getInviteType().equals(CommonConstants.INVITE_TYPE_II)) {
            for (InviteVehicleRecordPO po : records) {
                po.setParentId(newpo.getId());
                po.setIsMain(0);
                //更新sa
                newpo.setSaId(po.getSaId());
                newpo.setSaName(po.getSaName());
                inviteVehicleRecordMapper.updateById(newpo);
                //更新可能存在的子线索
                LambdaUpdateWrapper<InviteVehicleRecordPO> upw = new UpdateWrapper().lambda();
                upw.eq(InviteVehicleRecordPO::getParentId, po.getId());
                InviteVehicleRecordPO uprecord = new InviteVehicleRecordPO();
                uprecord.setParentId(newpo.getId());
                inviteVehicleRecordMapper.update(uprecord, upw);
                //更新可能存在的跟进记录
                LambdaUpdateWrapper<InviteVehicleRecordDetailPO> updateWrapper = new UpdateWrapper().lambda();
                updateWrapper.eq(InviteVehicleRecordDetailPO::getInviteId, po.getId());
                InviteVehicleRecordDetailPO up = new InviteVehicleRecordDetailPO();
                up.setInviteId(newpo.getId());
                inviteVehicleRecordDetailMapper.update(up, updateWrapper);
                inviteVehicleRecordMapper.updateById(po);
                return;
            }
            //其他类型,作为子线索
        } else {
            for (InviteVehicleRecordPO po : records) {
                newpo.setIsMain(0);
                newpo.setParentId(po.getId());
                inviteVehicleRecordMapper.updateById(newpo);
                break;
            }
        }

    }


    /**
     * 创建邀约线索
     *
     * @param po
     * @param map2
     * @return
     */
    public InviteVehicleRecordPO createInviteByTaskNew(String createDate, InviteVehicleTaskPO po, Map<String, Map<String, InviteVehicleRecordPO>> map2) {
        // 新增线索下发逻辑
        try{
            Map<String,String> dealerCodes = this.getDealerCodes(po);
            String vin = po.getVin();
            logger.info("createInviteByTaskNew,线索下发获取的{},最近2条经销商：{}", vin, JSONObject.toJSON(dealerCodes));
            List<String> dealerList = dealerCodes.entrySet().stream()
                    .map(Map.Entry::getValue)
                    .collect(Collectors.toList());
            //白名单判断
            List<String> listCode = inviteVehicleRecordService.doWhitelistDetermination(dealerList, vin, po.getInviteType(), Boolean.FALSE);
            if(CollectionUtils.isEmpty(listCode)){
                logger.info("createInviteByTaskNew,CollectionUtils.isEmpty(listCode)");
                //关闭线索,不再下发
                po.setIsCreateInvite(CommonConstants.NOT_GENERATING);
                po.setInvalidReason("白名单关闭任务");
                inviteVehicleTaskMapper.updateById(po);
                return null;
            }
            //求交集
            Map<String, String> intersection = new HashMap<>();
            for (Map.Entry<String, String> entry : dealerCodes.entrySet()) {
                if (listCode.contains(entry.getKey())) {
                    intersection.put(entry.getKey(), entry.getValue());
                }
            }
            logger.info("createInviteByTaskNew,intersection:{}", intersection);
            String dearcode = po.getDealerCode();
            for (String key:intersection.keySet()){
                //TODO:
                //查询任务是否经销商不同其他相同
                //如果是则对另外一条任务做逻辑删除操作，或者如果需要下发的经销商是2条,则修改另外一条的经销商，
                // 同时修改为已生成线索， 如果 需要下发的经销商为一条 ,则更具任务做同一条则改经销商，不是则逻辑删除另外一条
                InviteVehicleRecordPO record = new InviteVehicleRecordPO();
                record.setDealerCode(intersection.get(key));
                //VCDC下发邀约
                record.setSourceType(1);
                record.setIsMain(1);
                record.setVin(po.getVin());
                record.setLicensePlateNum(po.getLicensePlateNum());
                if(record.getDealerCode().equals(dearcode)){
                    record.setName(po.getName());
                    record.setTel(po.getTel());
                    record.setAge(po.getAge());
                    record.setSex(po.getSex());
                }else{
                    //查询车辆信息获取车主信息
                    VehicleOwnerVO  lix=    reportCommonClient.vehicleByVinAndCode(record.getDealerCode(),po.getVin());
                    if(lix !=null ){
                        record.setName(lix.getName());
                        record.setTel(lix.getMobile());
                        record.setAge(lix.getAge());
                        record.setSex(lix.getSex());
                        record.setLicensePlateNum(lix.getPlateNumber());
                    }else {
                        record.setName(po.getName());
                        record.setTel(po.getTel());
                        record.setAge(po.getAge());
                        record.setSex(po.getSex());
                    }
                }
                record.setInviteType(po.getInviteType());
                record.setAdviseInDate(po.getAdviseInDate());
                record.setItemType(po.getItemType());
                record.setItemCode(po.getItemCode());
                record.setItemName(po.getItemName());
                record.setLastChangeDate(po.getInviteTime());
                //未完成
                record.setOrderStatus(82411002);
                if (CommonConstants.INVITE_TYPE_I.equals(record.getInviteType())
                        || CommonConstants.INVITE_TYPE_VII.equals(record.getInviteType())
                        || CommonConstants.INVITE_TYPE_IX.equals(record.getInviteType())) {
                    record.setLastInDate(po.getLastMaintainDate());
                } else if (CommonConstants.INVITE_TYPE_V.equals(record.getInviteType())) {
                    record.setLastInDate(po.getInviteTime());
                } else if (CommonConstants.INVITE_TYPE_II.equals(record.getInviteType())
                        || CommonConstants.INVITE_TYPE_VI.equals(record.getInviteType())
                        || CommonConstants.INVITE_TYPE_XII.equals(record.getInviteType())) {
                    record.setLastInDate(po.getLastMaintenanceDate());
                }


                //未跟进
                record.setFollowStatus(82401001);
                SimpleDateFormat simdate = new SimpleDateFormat("yyyy-MM-dd");
                try {
                    record.setCreatedAt(simdate.parse(createDate));
                } catch (ParseException e) {
                    logger.info("时间异常{}", createDate);
                }
                inviteVehicleRecordMapper.insert(record);
                if(!StringUtils.isNullOrEmpty(map2.get(po.getVin()+DateUtils.dateToString( po.getAdviseInDate() ,DateUtils.PATTERN_YYYY_MM_DD)+po.getInviteType()))){
                    Map<String ,InviteVehicleRecordPO > mapx =  map2.get(po.getVin()+DateUtils.dateToString( po.getAdviseInDate() ,DateUtils.PATTERN_YYYY_MM_DD)+po.getInviteType());
                    mapx.put(record.getDealerCode(),record);
                    map2.put(po.getVin()+DateUtils.dateToString( po.getAdviseInDate() ,DateUtils.PATTERN_YYYY_MM_DD)+po.getInviteType(),mapx);
                }else{
                    Map<String ,InviteVehicleRecordPO > mapx = new HashMap<>() ;
                    mapx.put(record.getDealerCode(),record);
                    map2.put(po.getVin()+DateUtils.dateToString( po.getAdviseInDate() ,DateUtils.PATTERN_YYYY_MM_DD)+po.getInviteType(),mapx);
                }
                dearcode= record.getDealerCode();
            }
            Map<String ,InviteVehicleRecordPO > mapx =    map2.get(po.getVin()+DateUtils.dateToString( po.getAdviseInDate() ,DateUtils.PATTERN_YYYY_MM_DD)+po.getInviteType());
            InviteVehicleRecordPO px =  mapx.get(dearcode);
            InviteVehicleTaskPO po2 = new InviteVehicleTaskPO();
            BeanMapperUtil.copyProperties(po,po2,"id");
            //更新邀约任务
            //po.setIsCreateInvite(1);
            //po.setDealerCode(px.getDealerCode());
            //po.setInviteId(px.getId());
            po.setIsCreateInvite(2);
            po.setInvalidReason("新进厂关闭任务");
            inviteVehicleTaskMapper.updateById(po);
            po2.setIsCreateInvite(1);
            po2.setDealerCode(px.getDealerCode());
            po2.setInviteId(px.getId());
            inviteVehicleTaskMapper.insert(po2);
            logger.info("3919首保，定保，流失客户任务：{}，{}，{}，{}",po2.getVin(),po2.getInviteType(),po2.getId(),po2.getInviteId());
            this.saveVocInviteVehicleTaskRecordPo(po2);
            mapx.remove(dearcode);
            map2.put(po.getVin()+DateUtils.dateToString( po.getAdviseInDate() ,DateUtils.PATTERN_YYYY_MM_DD)+po.getInviteType(),mapx);
            logger.info("线索下发map2:{}",JSONObject.toJSON(map2));
            return px;
        }catch ( Exception e ){
            httpLogAiService.saveHttpLog("创建线索",null,null,null,"500","创建线索失败，调用report服务异常","-999");
            throw  new ServiceBizException("调用服务异常："+e);
        }
    }
    /**
     * 创建邀约线索
     *
     * @param po
     * @return
     */
    public InviteVehicleRecordPO createInviteByTask(String createDate, InviteVehicleTaskPO po) {
        // 新增线索下发逻辑
        InviteVehicleRecordPO record = new InviteVehicleRecordPO();
        record.setDealerCode(po.getDealerCode());
        //VCDC下发邀约
        record.setSourceType(1);
        record.setIsMain(1);
        record.setVin(po.getVin());
        record.setLicensePlateNum(po.getLicensePlateNum());
        record.setName(po.getName());
        record.setTel(po.getTel());
        record.setAge(po.getAge());
        record.setSex(po.getSex());
        record.setModel(po.getModel());
        record.setInviteType(po.getInviteType());
        record.setAdviseInDate(po.getAdviseInDate());
        record.setItemType(po.getItemType());
        record.setItemCode(po.getItemCode());
        record.setItemName(po.getItemName());
        record.setLastChangeDate(po.getInviteTime());
        //未完成
        record.setOrderStatus(82411002);
        if (CommonConstants.INVITE_TYPE_I.equals(record.getInviteType())
                || CommonConstants.INVITE_TYPE_VII.equals(record.getInviteType())
                || CommonConstants.INVITE_TYPE_IX.equals(record.getInviteType())) {
            record.setLastInDate(po.getLastMaintainDate());
        } else if (CommonConstants.INVITE_TYPE_V.equals(record.getInviteType())) {
            record.setLastInDate(po.getInviteTime());
        } else if (CommonConstants.INVITE_TYPE_II.equals(record.getInviteType())
                || CommonConstants.INVITE_TYPE_VI.equals(record.getInviteType())
                || CommonConstants.INVITE_TYPE_XII.equals(record.getInviteType())) {
            record.setLastInDate(po.getLastMaintenanceDate());
        }


        //未跟进
        record.setFollowStatus(82401001);
        SimpleDateFormat simdate = new SimpleDateFormat("yyyy-MM-dd");
        try {
            record.setCreatedAt(simdate.parse(createDate));
        } catch (ParseException e) {
            logger.info("时间异常{}", createDate);
        }
        inviteVehicleRecordMapper.insert(record);

        //更新邀约任务
        po.setIsCreateInvite(1);
        po.setInviteId(record.getId());
        inviteVehicleTaskMapper.updateById(po);
        logger.info("3985首定保流失客户任务：{}，{}，{}，{}",po.getVin(),po.getInviteType(),po.getId(),po.getInviteId());
        if(CommonConstants.INVITE_TYPE_I.equals(po.getInviteType())  ||
                CommonConstants.INVITE_TYPE_II.equals(po.getInviteType()) ||
                CommonConstants.INVITE_TYPE_VI.equals(po.getInviteType())   ||
                CommonConstants.INVITE_TYPE_XII.equals(po.getInviteType())
        ){
            this.updateVocInviteVehicleTaskRecordPo(po);
        }
        return record;
    }

    private Map<String,String> getDealerCodes(InviteVehicleTaskPO po) {
        // 线索生成后在下发推送给经销商时，需要对经销商做是否退网的判断；
        //线索推送时，需要查询已维护的“退网经销商代码转换”关系表，对于已经退网的经销商，在线索下发时，下发给新的经销商；
        //目前线索推送经销商逻辑是在线索生成时，推送经最近两次进厂经销商。调整为在线索下发时，推送最近两次进厂经销商，如果为同一家则推送一家。
        //且最近两次进厂经销商增加时间范围：只看最近2年内进厂经销商，时间后台设置为可配置；
        //如果未找到2年内进厂经销商，则推送给最后一次进厂经销商；
        //调用report 获取2年内最后的2个经销商或者2年内无进厂记录的取最后一条记录的经销商
        //判断查询的经销商是否退网，如果退网查询“退网经销商代码转换”关系表，如果未维护则继续下发给这家经销商，如果维护则去维护后的经销商，维护后的经销商需要一直查询到没有退网的经销商，
        //返回下发经销商信息。
        List<RepairOrderVO> list = reportCommonClient.getRepairOrderLastNew(po.getVin());
        logger.info("getDealerCodes 线索下发获取的{},最近2条经销商list：{}",po.getVin(),JSONObject.toJSON(list));
        Map<String,String> mapList =new HashMap(2);
        if(list!=null &&  list.size()>0){
            for (RepairOrderVO vo :list) {
                //查询该经销商是否退网
                int count =    reportCommonClient.queryCompany(vo.getDealerCode());
                if (count ==1){
                    //已经退网，查询“退网经销商代码转换”关系表
                    //是否存在关系
                    DealerTransformationRecordPO recordPO = dealerTransformationRecordMapper.selectOneBydealerCode(vo.getDealerCode());
                    if(recordPO !=null ){
                        String newcode =     recordPO.getNewDealerCode();
                        String newCodex =   recordPO.getNewDealerCode();
                        while (!StringUtils.isNullOrEmpty(newCodex)){
                            int countx =    reportCommonClient.queryCompany(newCodex);
                            if(countx ==1){
                                DealerTransformationRecordPO recordPOx = dealerTransformationRecordMapper.selectOneBydealerCode(newCodex);
                                if(recordPOx!= null ){
                                    newCodex =   recordPOx.getNewDealerCode();
                                    newcode =  recordPOx.getNewDealerCode();
                                }else{
                                    newcode = newCodex;
                                    newCodex = null;
                                }
                            }else {
                                newcode = newCodex;
                                newCodex = null;
                            }

                        }
                        mapList.put(newcode,newcode);
                    }else{
                        mapList.put(vo.getDealerCode(),vo.getDealerCode());
                    }
                }else{
                    mapList.put(vo.getDealerCode(),vo.getDealerCode());
                }
            }
        }else{
            mapList.put(po.getDealerCode(),po.getDealerCode());
        }
        logger.info("getDealerCodes 线索下发获取的{},最近2条经销商mapList：{}",po.getVin(),JSONObject.toJSON(mapList));
        return mapList;
    }


    /**
     * 检查重复生成规则
     *
     * @param vin
     * @param duplicateRule 天数内不再生成
     * @return
     */
    private boolean checkHasRepairOrder(String createDate, String vin, Integer duplicateRule) {
        Integer res = reportCommonClient.checkHasRepairOrder(createDate, vin, duplicateRule);
        if (res != null) {
            return false;
        }
        return true;
    }


    /**
     * 查询未完成的邀约线索
     *
     * @param createDate
     * @param vin
     * @param mergeRule
     * @return
     */
    private List<InviteVehicleRecordPO> queryInviteRecordForTask(String dealerCode, Date adviseInDate, String vin,
                                                                 Integer mergeRule, Long id) {
        return inviteVehicleRecordMapper.queryInviteRecordForTask(dealerCode, adviseInDate, vin, mergeRule, id);
    }

    /**
     * 生成二次跟进数据
     *
     * @param createDate
     * @return
     */
    @Override
    public int createTwiceFollow(String createDate) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        //每月1日执行
        if (!this.checkExecuteDate(createDate, "dd", "01")) {
            logger.info("当前日期{},不执行二次跟进生成", createDate);
            return 1;
        }

        inviteVehicleRecordMapper.createTwiceFollow();
        return 1;
    }

    @Override
    public int inviteAutoCreateTaskList(List<String> list) {
        this.createTaskByRepairOrderList(list);
        return 0;
    }



    /**
     * 工单创建邀约任务
     *
     * @param listX
     * */
    @Transactional(rollbackFor = Exception.class)
    public void createTaskByRepairOrderList(List<String> listX) {
        //设置将父线程的HttpServletRequest对象设置共享
        RequestContextHolder.setRequestAttributes(RequestContextHolder.getRequestAttributes(), true);
        logger.info("开始工单查询工单，{},{}", listX.size(),JSONObject.toJSON(listX));
        //查询机油工单
        List<VehicleOwnerVO> listxx = reportCommonClient.queryRegularMaintainList(listX);
        logger.info("查询结果,size{},{}",listxx.size(), JSONObject.toJSON(listxx));
        //刷选每个vin 第1条
        Map<String, VehicleOwnerVO> map = listxx.parallelStream().collect(Collectors.groupingBy(VehicleOwnerVO::getVin,
                Collectors.collectingAndThen( Collectors.reducing(( c1,  c2) -> c1.getDeliveryDate().getTime() > c2.getDeliveryDate().getTime() ? c1 : c2), Optional::get)));
        List<VehicleOwnerVO> list  = new ArrayList<>(map.values());
        logger.info("查询结果,size{},{}", list.size(),JSONObject.toJSON(list));

        try {
            // 线程数量
            int threadSize = 10;
            ExecutorService service = Executors.newFixedThreadPool(threadSize);
            // 闭锁，等待所有任务执行完成
            final CountDownLatch latch = new CountDownLatch(list.size());
            for (VehicleOwnerVO vo : list) {
                Runnable task = new Runnable() {
                    public void run() {
                        try {
                            logger.info("开始工单触发创建邀约任务,vin{},roNo{}", vo.getVin(), vo.getRoNo());
                            createRegularMaintainTaskList(vo);
                        } catch (Exception e) {
                            logger.error(">>>工单创建邀约任务异常:{}", e);
                            dailyMileageLogMapper.SetErrorlog("工单创建邀约任务异常", e.getMessage(), e.getStackTrace()
                                    .toString(), vo.getVin());
                        } finally {
                            latch.countDown();
                        }
                    }
                };
                service.submit(task);
            }
            latch.await();
            service.shutdown();
        }catch (Exception e){
            dailyMileageLogMapper.SetErrorlog("工单创建邀约任务异常", e.getMessage(), e.getStackTrace()
                    .toString(),"多线程异常");
        }
    }


    /**
     * 创建保养任务
     *
     */
    private void createRegularMaintainTaskList(VehicleOwnerVO vo) throws ParseException {
        vo.setInviteType(CommonConstants.INVITE_TYPE_II);
//        List<InviteVehicleTaskPO> rs = inviteVehicleTaskMapper.queryRegularMaintainTask(vo.getVin());
//        //有未下发的定保，更新建议进厂
//        if (rs != null && rs.size() > 0) {
//            for (InviteVehicleTaskPO task : rs) {
//                //设基准日期
//                vo.setLastMaintainDate(task.getInviteTime());
//                //删除任务
//                inviteVehicleTaskMapper.deleteById(task.getId());
//            }
//        }
        logger.info("查询是否保养工单,roNo{}", vo.getRoNo());
        RepairOrderVO ro = reportCommonClient.getMaintainRepairOrderLast(vo.getDealerCode(), vo.getRoNo());
        //如果是保养工单
        if (ro != null) {
            String roDealerCode = vo.getDealerCode();
            logger.info("保养工单,roNo{},roDealerCode{}", vo.getRoNo(), roDealerCode);
            //设置基准日期
            vo.setLastMaintainDate(ro.getDeliveryDate());
            logger.info("保养工单基准日期,deliveryDate{}", ro.getDeliveryDate());
            vo.setOutMileage(ro.getOutMileage().intValue());
            logger.info("开始查询未关闭线索，vin{}", vo.getVin());
            List<InviteVehicleRecordPO> list = this.getWaitCloseMaintainRecord(vo.getVin());
            logger.info("查询结果，size{}", list.size());
            for (InviteVehicleRecordPO item : list) {
                item = inviteVehicleRecordMapper.selectById(item.getId());
                if (ro.getDealerCode().equals(item.getDealerCode())) {
                    logger.info("本店，完成,recordId{},dealerCode{}", item.getId(), item.getDealerCode());
                    //完成
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_I);
                    item.setOrderFinishDate(ro.getDeliveryDate());
                    item.setRoNo(ro.getRoNo());
                    item.setRoCreateDate(ro.getRoCreateDate());
                    item.setRepairTypeCode(ro.getRepairTypeCode());
                    item.setFinishDealerCode(ro.getDealerCode());
                    item.setOutMileage(ro.getOutMileage());
                } else {
                    logger.info("他店，他店进厂,recordId{},dealerCode{}", item.getId(), item.getDealerCode());
                    //他店进厂
                    item.setOrderStatus(CommonConstants.ORDER_STATUS_IV);
                    item.setOrderFinishDate(ro.getDeliveryDate());
                    item.setRoNo(ro.getRoNo());
                    item.setRoCreateDate(ro.getRoCreateDate());
                    item.setRepairTypeCode(ro.getRepairTypeCode());
                    item.setFinishDealerCode(ro.getDealerCode());
                    item.setOutMileage(ro.getOutMileage());
                }
                inviteVehicleRecordMapper.updateById(item);
                logger.info("开始拆分线索，重新合并线索,recordId{},InviteType{}", item.getId(), item.getInviteType());
                this.mergeInvite(item);
            }
            logger.info("关闭首保未下发任务,vin{}", vo.getVin());
            //关闭首保未下发任务
            this.updateTaskCloseById(null, CommonConstants.INVITE_TYPE_I, vo.getVin());
            logger.info("关闭定保未下发任务,vin{}", vo.getVin());
            //关闭定保未下发任务
            this.updateTaskCloseById(null, CommonConstants.INVITE_TYPE_II, vo.getVin());
            logger.info("查询最近两次工单的经销商,vin{}", vo.getVin());
            //查询最近两次工单 的经销商
            List<RepairOrderVO> orders = reportCommonClient.getRepairOrderLast(vo.getVin());
            String dealerCode = null;
            logger.info("查询结果,size{}", orders.size());
            for (RepairOrderVO order : orders) {
                if (dealerCode == null) {
                    dealerCode = order.getDealerCode();
                    vo.setDealerCode(order.getDealerCode());
                    logger.info("经销商,dealerCode{}", dealerCode);
                } else {
                    logger.info("经销商,dealerCode{}", order.getDealerCode());
                    if (!dealerCode.equals(order.getDealerCode())) {
                        logger.info("经销商不同,dealerCode1{},,dealerCode2{}", dealerCode, order.getDealerCode());
                        vo.setDealerCode(order.getDealerCode());
                    } else {
                        continue;
                    }
                }
                Date adviseInDate = this.setAdviseInDate(vo, vo.getDealerCode(), vo.getLastMaintainDate(), vo
                        .getDailyAverageMileage());
                if (adviseInDate != null) {
                    //如果时间在9月之前改为10月1号
                    SimpleDateFormat simpleDateFormat =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date   date2 =simpleDateFormat.parse("2022-10-01 08:00:00");
                    if (!adviseInDate.before(date2)) {
                        vo.setAdviseInDate(adviseInDate);
                    }else{
                        vo.setAdviseInDate(date2);
                    }
                    this.createInviteTask(vo, CommonConstants.UP_DAY);
                }
            }
            //重设工单门店
            vo.setDealerCode(roDealerCode);
        } else {
            logger.info("非保养工单,vin{},roNo{}", vo.getVin(), vo.getRoNo());
        }
    }
    @Override
    public List<InviteVehicleTaskPO> getWaitCloseRecordByVin(String vin) {
       QueryWrapper<InviteVehicleTaskPO>  qw =  new  QueryWrapper<>();
        qw.eq("invite_type",CommonConstants.INVITE_TYPE_VI);
        qw.eq("is_create_invite",0);
        qw.eq("is_deleted",0);
        qw.eq("vin",vin);
        return inviteVehicleTaskMapper.selectList(qw);
    }

    @Override
    public InviteVehicleTaskPO createCustomerLossTaskVoc(InviteVehicleTaskPO t, Integer inm) {
        InviteVehicleTaskPO po = new InviteVehicleTaskPO();
        po.setVin(t.getVin());
        po.setLicensePlateNum(t.getLicensePlateNum());
        po.setDealerCode(t.getDealerCode());
        po.setName(t.getName());
        po.setTel(t.getTel());
        po.setAge(t.getAge());
        po.setSex(t.getSex());
        po.setModel(t.getModel());
        po.setDailyMileage(t.getDailyMileage());
        po.setDayInAdvance(t.getDayInAdvance());
        po.setRemindInterval(t.getRemindInterval());
        po.setCloseInterval(0);
        po.setIsCreateInvite(0);
        po.setCloseTimes(0);
        //基准日期为关闭任务的建议进厂日期
        po.setAdviseInDate(DateUtils.getDate());
        po.setInviteTime(t.getLastMaintainDate());
        po.setCreateInviteTime(DateUtils.getDate());
        po.setInviteType(CommonConstants.INVITE_TYPE_VI);
        po.setAdviseInMileage(inm);
        return  po;
    }

    @Override
    public InviteVehicleTaskPO createWarnTask(InviteVehicleTaskPO t, Integer inm) {
        InviteVehicleTaskPO po = new InviteVehicleTaskPO();
        po.setVin(t.getVin());
        po.setLicensePlateNum(t.getLicensePlateNum());
        po.setDealerCode(t.getDealerCode());
        po.setName(t.getName());
        po.setTel(t.getTel());
        po.setAge(t.getAge());
        po.setSex(t.getSex());
        po.setModel(t.getModel());
        po.setDailyMileage(t.getDailyMileage());
        po.setDayInAdvance(t.getDayInAdvance());
        po.setRemindInterval(t.getRemindInterval());
        po.setIsCreateInvite(0);
        po.setCloseInterval(0);
        po.setCloseTimes(0);
        po.setAdviseInDate(DateUtils.getDate());
        po.setCreateInviteTime(DateUtils.getDate());
        po.setInviteTime(DateUtils.getDate());
        po.setInviteType(CommonConstants.INVITE_TYPE_XII);
        po.setAdviseInMileage(inm);
        return  po;
    }

    @Override
    public InviteVehicleTaskPO selectByInviteId(Long id) {
        QueryWrapper<InviteVehicleTaskPO> qw =  new  QueryWrapper<>();
        qw.eq("invite_id",id);
        qw.orderByDesc("id");
        qw.last("limit 1");
        return inviteVehicleTaskMapper.selectOne(qw);
    }

    @Override
    public InviteVehicleTaskPO createCustomerLossTaskVoc18(InviteVehicleTaskPO t, Integer inm) {
        return createCustomerLossTaskVoc(t,inm);
    }
     //首保 取新车数据
    @Override
    public void createCustomerTaskVoc1( String vin, List<InviteVehicleTaskPO> inst1, List<InviteVehicleTaskPO> inst66) {
        List<VehicleOwnerVO> list = reportCommonClient.queryFirstMaintainByVin(vin);
          if(CollUtil.isNotEmpty(list)){
              VehicleOwnerVO vo =  list.get(0);
                  this.createFirstMaintainTaskByNewCarSalesVoc(vo,inst1);
                  this.createCustomerLossTaskByNewCarSalesVoc(vo,inst66);
          }
    }



    /**
     * 创建首保任务
     *
     */
    private void createFirstMaintainTaskByNewCarSalesVoc(VehicleOwnerVO vo,  List<InviteVehicleTaskPO> inst1) {
        logger.info(CommonConstants.LOG_S2, vo.getVin());
        //查询首保日期间隔规则
        InviteRulePO rule = inviteRuleMapper.getInvitationDlrRule(vo.getDealerCode(),
                CommonConstants.INVITE_TYPE_FIRST_GUARANTEE, CommonConstants.INVITE_RULE_I);
        if (rule == null || rule.getIsUse() == null || rule.getIsUse() == 0) {
            //不存在首保日期间隔规则，跳过
            logger.info(CommonConstants.LOG_S3, vo.getVin());
            return;
        }
        //查询首保里程间隔规则
        InviteRulePO mileageRule = inviteRuleMapper.getInvitationDlrRule(vo.getDealerCode(),
                CommonConstants.INVITE_TYPE_FIRST_GUARANTEE, CommonConstants.INVITE_RULE_II);
        vo.setInviteType(CommonConstants.INVITE_TYPE_I);
        //基准日期
        vo.setLastMaintainDate(vo.getInvoiceDate());
        logger.info(CommonConstants.LOG_S4, vo.getInvoiceDate());
        logger.info(CommonConstants.LOG_S5, rule.getRuleValue());
        vo.setAdviseInDate(DateUtils.getDate());
        logger.info(CommonConstants.LOG_S6, vo.getAdviseInDate());
        vo.setDayInAdvance(rule.getDayInAdvance());
        vo.setInviteRule(rule.getInviteRule());
        vo.setRuleValue(rule.getRuleValue());
        vo.setRemindInterval(rule.getRemindInterval());
        vo.setCloseInterval(rule.getCloseInterval());
        if (mileageRule != null && mileageRule.getIsUse() != null && rule.getIsUse() != 0) {
            vo.setAdviseInMileage(mileageRule.getRuleValue());
        } else {
            vo.setAdviseInMileage(null);
        }
        inst1.add(this.createInviteTaskVoc1(vo));
    }

    /**
     * 创建邀约任务
     *
     * @param vo
     */
    private InviteVehicleTaskPO createInviteTaskVoc1(VehicleOwnerVO vo) {

        InviteVehicleTaskPO po = new InviteVehicleTaskPO();
        po.setVin(vo.getVin());
        po.setLicensePlateNum(vo.getPlateNumber());
        po.setDealerCode(vo.getDealerCode());
        po.setName(vo.getName());
        po.setTel(vo.getMobile());
        po.setAge(vo.getAge());
        po.setSex(vo.getSex());
        po.setModel(vo.getModelId());
        po.setDailyMileage(vo.getDailyAverageMileage());
        po.setInviteType(vo.getInviteType());
        po.setAdviseInDate(vo.getAdviseInDate());
        po.setRemindInterval(vo.getRemindInterval());
        po.setCloseInterval(vo.getCloseInterval());
        po.setIsCreateInvite(0);
        po.setInviteTime(vo.getLastMaintainDate());
        po.setCreateInviteTime(po.getAdviseInDate());
        po.setItemType(vo.getItemType());
        po.setItemCode(vo.getItemCode());
        po.setItemName(vo.getItemName());
        po.setAdviseInMileage(vo.getAdviseInMileage());
        po.setOutMileage(vo.getOutMileage());
        po.setQbNumber(vo.getQbNumber());
       return po ;
    }


    /**
     * 客户流失
     *
     * @param vo
     * @param inst66
     */
    private void createCustomerLossTaskByNewCarSalesVoc(VehicleOwnerVO vo,  List<InviteVehicleTaskPO> inst66) {
        logger.info(CommonConstants.LOG_S7, vo.getVin());
        //查询客户流失间隔规则
        InviteRulePO rule = inviteRuleMapper.getInvitationDlrRule(vo.getDealerCode(), 82031004, 82041005);
        logger.info(CommonConstants.LOG_S21, rule);
        if (rule == null || rule.getIsUse() == null || rule.getIsUse() == 0) {
            //不存在客户流失间隔规则，跳过
            return  ;
        }
        vo.setInviteType(CommonConstants.INVITE_TYPE_VI);
        //基准日期
        vo.setLastMaintainDate(vo.getInvoiceDate());
        logger.info(CommonConstants.LOG_S4, vo.getInvoiceDate());
        Calendar c = Calendar.getInstance();
        c.setTime(DateUtils.getDate());
        c.add(Calendar.MONTH, rule.getRuleValue());
        logger.info(CommonConstants.LOG_S5, rule.getRuleValue());
        vo.setAdviseInDate(c.getTime());
        logger.info(CommonConstants.LOG_S6, vo.getAdviseInDate());
        vo.setDayInAdvance(rule.getDayInAdvance());
        vo.setInviteRule(rule.getInviteRule());
        vo.setRuleValue(rule.getRuleValue());
        vo.setRemindInterval(rule.getRemindInterval());
        vo.setCloseInterval(rule.getCloseInterval());
        inst66.add( this.createLossInviteTaskVoc(vo, lossInvite));
    }

    /**
     * 创建邀约任务
     *
     * @param vo
     */
    private InviteVehicleTaskPO createLossInviteTaskVoc(VehicleOwnerVO vo,Integer month) {
        InviteVehicleTaskPO po = new InviteVehicleTaskPO();
        po.setVin(vo.getVin());
        po.setLicensePlateNum(vo.getPlateNumber());
        po.setDealerCode(vo.getDealerCode());
        po.setName(vo.getName());
        po.setTel(vo.getMobile());
        po.setAge(vo.getAge());
        po.setSex(vo.getSex());
        po.setModel(vo.getModelId());
        po.setDailyMileage(vo.getDailyAverageMileage());
        po.setInviteType(vo.getInviteType());
        po.setAdviseInDate(vo.getAdviseInDate());
        po.setRemindInterval(vo.getRemindInterval());
        po.setCloseInterval(vo.getCloseInterval());
        po.setIsCreateInvite(0);
        po.setInviteTime(DateUtils.getDate());
        Calendar c = Calendar.getInstance();
        c.setTime(po.getAdviseInDate());
        c.add(Calendar.MONTH, month);
        po.setCreateInviteTime(c.getTime());
        po.setItemType(vo.getItemType());
        po.setItemCode(vo.getItemCode());
        po.setItemName(vo.getItemName());
        po.setAdviseInMileage(vo.getAdviseInMileage());
        po.setOutMileage(vo.getOutMileage());
        po.setQbNumber(vo.getQbNumber());
        return po ;
    }

    /**
     *  定保
     * @param inm
     * @param inst2
     * @param inst66
     * @param vin
     * @param deliveryDate
     * @return
     */
    @Override
    public void createCustomerTaskVoc2(Integer inm, List<InviteVehicleTaskPO> inst2, List<InviteVehicleTaskPO> inst66, String vin, Date deliveryDate) {
        //查询最近2次工单然后生成任务生成线索
        List<VehicleOwnerVO> list =       reportCommonClient.queryFirstMaintainByVin(vin);
        if(CollUtil.isNotEmpty(list)){
            VehicleOwnerVO   vo =   list.get(0);
            Map<String ,String> map  =  this.getDealerCodes(vin);
            if(CollUtil.isNotEmpty(map)){
                //任务
                map.forEach((key, value) -> {
                    //查询首保里程间隔规则
                    InviteRulePO ru2 = inviteRuleMapper.getInvitationDlrRule(key,
                            CommonConstants.INVITE_TYPE_FIXED_WARRANTY, CommonConstants.INVITE_RULE_III);
                    vo.setOutMileage(inm);
                    vo.setDealerCode(key);
                    vo.setAdviseInDate(DateUtils.getDate());
                    vo.setLastMaintainDate(deliveryDate);
                    vo.setDayInAdvance(ru2.getDayInAdvance());
                    vo.setInviteRule(ru2.getInviteRule());
                    vo.setRuleValue(ru2.getRuleValue());
                    vo.setRemindInterval(ru2.getRemindInterval());
                    vo.setCloseInterval(ru2.getCloseInterval());
                    vo.setInviteType(CommonConstants.INVITE_TYPE_II);
                    this.createInviteTaskVoc2(vo,inst2);
                    this.createCustomerLossTask2(vo,inst66);
                });
            }else{
                InviteRulePO ru2 = inviteRuleMapper.getInvitationDlrRule(vo.getDealerCode(),
                        CommonConstants.INVITE_TYPE_FIXED_WARRANTY, CommonConstants.INVITE_RULE_III);
                vo.setOutMileage(inm);
                vo.setDealerCode(vo.getDealerCode());
                vo.setAdviseInDate(DateUtils.getDate());
                vo.setLastMaintainDate(deliveryDate);
                vo.setDayInAdvance(ru2.getDayInAdvance());
                vo.setInviteRule(ru2.getInviteRule());
                vo.setRuleValue(ru2.getRuleValue());
                vo.setRemindInterval(ru2.getRemindInterval());
                vo.setCloseInterval(ru2.getCloseInterval());
                vo.setInviteType(CommonConstants.INVITE_TYPE_II);
                this.createInviteTaskVoc2(vo,inst2);
                this.createCustomerLossTask2(vo,inst66);
            }
        }

    }




    private Map<String,String> getDealerCodes(String vin) {
        // 线索生成后在下发推送给经销商时，需要对经销商做是否退网的判断；
        //线索推送时，需要查询已维护的“退网经销商代码转换”关系表，对于已经退网的经销商，在线索下发时，下发给新的经销商；
        //目前线索推送经销商逻辑是在线索生成时，推送经最近两次进厂经销商。调整为在线索下发时，推送最近两次进厂经销商，如果为同一家则推送一家。
        //且最近两次进厂经销商增加时间范围：只看最近2年内进厂经销商，时间后台设置为可配置；
        //如果未找到2年内进厂经销商，则推送给最后一次进厂经销商；
        //调用report 获取2年内最后的2个经销商或者2年内无进厂记录的取最后一条记录的经销商
        //判断查询的经销商是否退网，如果退网查询“退网经销商代码转换”关系表，如果未维护则继续下发给这家经销商，如果维护则去维护后的经销商，维护后的经销商需要一直查询到没有退网的经销商，
        //返回下发经销商信息。
        List<RepairOrderVO> list = reportCommonClient.getRepairOrderLastNew(vin);
        Map<String,String> mapList =new HashMap(2);
        if(CollUtil.isNotEmpty(list)){
            for (RepairOrderVO vo :list) {
                //查询该经销商是否退网
                int count =    reportCommonClient.queryCompany(vo.getDealerCode());
                if (count ==1){
                    //已经退网，查询“退网经销商代码转换”关系表
                    //是否存在关系
                    adData(mapList,vo);
                }else{
                    mapList.put(vo.getDealerCode(),vo.getDealerCode());
                }
            }
        }
        return mapList;
    }
    private void   adData(Map<String, String> mapList, RepairOrderVO vo){
        DealerTransformationRecordPO recordPO = dealerTransformationRecordMapper.selectOneBydealerCode(vo.getDealerCode());
        if(recordPO !=null ){
            String newcode =     recordPO.getNewDealerCode();
            String newCodex =   recordPO.getNewDealerCode();
            while (!StringUtils.isNullOrEmpty(newCodex)){
                int countx =    reportCommonClient.queryCompany(newCodex);
                if(countx ==1){
                    DealerTransformationRecordPO recordPOx = dealerTransformationRecordMapper.selectOneBydealerCode(newCodex);
                    if(recordPOx!= null ){
                        newCodex =   recordPOx.getNewDealerCode();
                        newcode =  recordPOx.getNewDealerCode();
                    }else{
                        newcode = newCodex;
                        newCodex = null;
                    }
                }else {
                    newcode = newCodex;
                    newCodex = null;
                }

            }
            mapList.put(newcode,newcode);
        }else{
            mapList.put(vo.getDealerCode(),vo.getDealerCode());
        }
    }



    /**
     * 客户流失
     *  @param vo
     * @param inst66
     */
    private void createCustomerLossTask2(VehicleOwnerVO vo, List<InviteVehicleTaskPO> inst66) {
        logger.info("开始客户流失{}", vo.getVin());
        logger.info("查询客户流失规则，DealerCode{}", vo.getDealerCode());
        //查询客户流失间隔规则
        InviteRulePO rule = inviteRuleMapper.getInvitationDlrRule(vo.getDealerCode(), 82031004, 82041005);
        logger.info(CommonConstants.LOG_S21, rule);
        if (rule == null || rule.getIsUse() == null || rule.getIsUse() == 0) {
            //不存在客户流失间隔规则，跳过
            return;
        }

        //创建任务
        vo.setInviteType(CommonConstants.INVITE_TYPE_VI);
        Calendar c = Calendar.getInstance();
        c.setTime(DateUtils.getDate());
        c.add(Calendar.MONTH, rule.getRuleValue());
        vo.setAdviseInDate(c.getTime());
        vo.setDayInAdvance(rule.getDayInAdvance());
        vo.setInviteRule(rule.getInviteRule());
        vo.setRuleValue(rule.getRuleValue());
        vo.setRemindInterval(rule.getRemindInterval());
        vo.setCloseInterval(rule.getCloseInterval());
        inst66.add(this.createLossInviteTaskVoc(vo, lossInvite));
    }

    /**
     * 创建邀约任务
     *  @param vo
     * @param inst2
     */
    private void createInviteTaskVoc2(VehicleOwnerVO vo, List<InviteVehicleTaskPO> inst2) {

        InviteVehicleTaskPO po = new InviteVehicleTaskPO();
        po.setVin(vo.getVin());
        po.setLicensePlateNum(vo.getPlateNumber());
        po.setDealerCode(vo.getDealerCode());
        po.setName(vo.getName());
        po.setTel(vo.getMobile());
        po.setAge(vo.getAge());
        po.setSex(vo.getSex());
        po.setModel(vo.getModelId());
        po.setDailyMileage(vo.getDailyAverageMileage());
        po.setInviteType(vo.getInviteType());
        po.setRemindInterval(vo.getRemindInterval());
        po.setCloseInterval(vo.getCloseInterval());
        po.setIsCreateInvite(0);
        po.setInviteTime(vo.getLastMaintainDate());
        po.setAdviseInDate(DateUtils.getDate());
        po.setCreateInviteTime(DateUtils.getDate());
        po.setItemType(vo.getItemType());
        po.setItemCode(vo.getItemCode());
        po.setItemName(vo.getItemName());
        po.setAdviseInMileage(vo.getAdviseInMileage());
        po.setOutMileage(vo.getOutMileage());
        po.setQbNumber(vo.getQbNumber());
        inst2.add(po);
    }


    @Override
    public int inser1List1(List<InviteVehicleTaskPO> inst1) {
        //下发线索
        List<InviteVehicleRecordPO> recordPOS = new ArrayList<>(inst1.size());
        //线索字段表
        List<VocInviteVehicleTaskRecordPo> recordPOS1 = new ArrayList<>(inst1.size());
        Map<String,Long> map1 = new HashMap<>(inst1.size());
        //生成任务
         insertList1(inst1,recordPOS,map1);
         insertRecordList1(recordPOS,map1,recordPOS1);
         logger.info(CommonConstants.LOG_S18,JSONObject.toJSON(map1));
         updateTaskList1(recordPOS1);
         insertRecordList2(recordPOS1);
        this.taskAllocationVoc(recordPOS,false);
        return inst1.size();
    }

    private void updateTaskList1(List<VocInviteVehicleTaskRecordPo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int numPerTimes = 200;
        if (list.size() <= numPerTimes) {
            updateBatchList(list); //此处插入少于200条list
            logger.info(CommonConstants.LOG_S10);
        } else {

            int maxIndex = list.size();
            int maxTimes = maxIndex / numPerTimes;
            maxTimes += (maxIndex % numPerTimes) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimes) {
                int fromIndex = numPerTimes * currentTimes;
                int toIndex = fromIndex + numPerTimes;
                toIndex = toIndex > maxIndex ? maxIndex : toIndex;
                List<VocInviteVehicleTaskRecordPo> subList = list.subList(fromIndex, toIndex);
                updateBatchList(subList);//此处循环插入200条list
                logger.info(CommonConstants.LOG_S11, fromIndex, toIndex);
                currentTimes++;
            }
        }
    }
    /**
     * 批量新增
     *  @param list
     */
    private void updateBatchList(List<VocInviteVehicleTaskRecordPo> list) {
        inviteVehicleTaskMapper.updateRecordIdListById(list);
    }

    private void insertRecordList2(List<VocInviteVehicleTaskRecordPo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int numPerTimes = 200;
        if (list.size() <= numPerTimes) {
            batchLogAdd2(list); //此处插入少于200条list
            logger.info(CommonConstants.LOG_S14);
        } else {

            int maxIndex = list.size();
            int maxTimes = maxIndex / numPerTimes;
            maxTimes += (maxIndex % numPerTimes) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimes) {
                int fromIndex = numPerTimes * currentTimes;
                int toIndex = fromIndex + numPerTimes;
                toIndex = toIndex > maxIndex ? maxIndex : toIndex;
                List<VocInviteVehicleTaskRecordPo> subList = list.subList(fromIndex, toIndex);
                batchLogAdd2(subList);//此处循环插入200条list
                logger.info(CommonConstants.LOG_S15, fromIndex, toIndex);
                currentTimes++;
            }
        }

    }
    private void batchLogAdd2( List<VocInviteVehicleTaskRecordPo> list) {
        vocInviteVehicleTaskRecordMapper.insertList(list);
        logger.info("voc定时任务生成voc类型：{}",JSONObject.toJSON(list));
    }

    private void insertRecordList1(List<InviteVehicleRecordPO> list, Map<String, Long> map1, List<VocInviteVehicleTaskRecordPo> recordPOS1) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int numPerTimes = 200;
        if (list.size() <= numPerTimes) {
            batchLogAdd1(list,recordPOS1,map1); //此处插入少于200条list
            logger.info(CommonConstants.LOG_S13);
        } else {

            int maxIndex = list.size();
            int maxTimes = maxIndex / numPerTimes;
            maxTimes += (maxIndex % numPerTimes) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimes) {
                int fromIndex = numPerTimes * currentTimes;
                int toIndex = fromIndex + numPerTimes;
                toIndex = toIndex > maxIndex ? maxIndex : toIndex;
                List<InviteVehicleRecordPO> subList = list.subList(fromIndex, toIndex);
                batchLogAdd1(subList,recordPOS1,map1);//此处循环插入200条list
                logger.info(CommonConstants.LOG_S12, fromIndex, toIndex);
                currentTimes++;
            }
        }
    }

    private void batchLogAdd1(List<InviteVehicleRecordPO> list, List<VocInviteVehicleTaskRecordPo> recordPOS1, Map<String, Long> map1) {
        inviteVehicleRecordMapper.insertList(list);
        for (InviteVehicleRecordPO po:list) {
            //下发线索
            recordPOS1.add(this.createInviteByTaskRecodeVoc(po,map1));
        }
    }

    private VocInviteVehicleTaskRecordPo createInviteByTaskRecodeVoc(InviteVehicleRecordPO po, Map<String, Long> map1) {
        VocInviteVehicleTaskRecordPo pox = new  VocInviteVehicleTaskRecordPo();
        pox.setInviteType(po.getInviteType());
        pox.setVin(po.getVin());
        pox.setRecordId(po.getId());
        pox.setTaskId(map1.get(po.getVin() + po.getDealerCode()));
        pox.setRecordType(CommonConstants.RECORD_TYPE_0);
        pox.setLossType(CommonConstants.LOSS_TYPE_0);
        logger.info("createInviteByTaskRecodeVoc查询vocTask:{},{},{}",po.getVin(),po.getId(),map1.get(po.getVin()));
        return  pox;
    }


    /**
     * @param list
     * @param recordPOS
     * @param map1
     */
    private void insertList1(List<InviteVehicleTaskPO> list, List<InviteVehicleRecordPO> recordPOS, Map<String, Long> map1) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int numPerTimes = 200;
        if (list.size() <= numPerTimes) {
            batchLogAdd(list,recordPOS,map1); //此处插入少于200条list
            logger.info(CommonConstants.LOG_S10);
        } else {

            int maxIndex = list.size();
            int maxTimes = maxIndex / numPerTimes;
            maxTimes += (maxIndex % numPerTimes) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimes) {
                int fromIndex = numPerTimes * currentTimes;
                int toIndex = fromIndex + numPerTimes;
                toIndex = toIndex > maxIndex ? maxIndex : toIndex;
                List<InviteVehicleTaskPO> subList = list.subList(fromIndex, toIndex);
                batchLogAdd(subList,recordPOS,map1);//此处循环插入200条list
                logger.info(CommonConstants.LOG_S11, fromIndex, toIndex);
                currentTimes++;
            }
        }

    }


    /**
     * 自动分配sa
     */
    private void taskAllocationVoc(List<InviteVehicleRecordPO> list, Boolean isInsurance) {
        HashMap<String, List<InviteVehicleRecordDTO>> map = new HashMap<>();
        if (!CommonUtils.isNullOrEmpty(list)) {
            List<InviteVehicleRecordDTO> result = list.stream().map(m -> m.transPoToDto(InviteVehicleRecordDTO.class))
                    .collect(Collectors.toList());
            for (InviteVehicleRecordDTO dto : result) {
                if (map.containsKey(dto.getDealerCode())) {
                    map.get(dto.getDealerCode()).add(dto);
                } else {
                    List<InviteVehicleRecordDTO> array = new ArrayList<>();
                    array.add(dto);
                    map.put(dto.getDealerCode(), array);
                }
            }

            for (Map.Entry<String, List<InviteVehicleRecordDTO>> entry : map.entrySet()) {
                this.allocation(entry.getKey(), entry.getValue(), isInsurance);
            }
        }
    }

    /**
     * 创建邀约线索
     *
     * @param po
     * @return
     */
    public InviteVehicleRecordPO createInviteByTaskVoc( InviteVehicleTaskPO po) {
        // 新增线索下发逻辑
        InviteVehicleRecordPO record = new InviteVehicleRecordPO();
        record.setDealerCode(po.getDealerCode());
        //VCDC下发邀约
        record.setSourceType(1);
        record.setIsMain(1);
        record.setVin(po.getVin());
        record.setLicensePlateNum(po.getLicensePlateNum());
        record.setName(po.getName());
        record.setTel(po.getTel());
        record.setAge(po.getAge());
        record.setSex(po.getSex());
        record.setModel(po.getModel());
        record.setInviteType(po.getInviteType());
        record.setAdviseInDate(po.getAdviseInDate());
        record.setItemType(po.getItemType());
        record.setItemCode(po.getItemCode());
        record.setItemName(po.getItemName());
        record.setLastChangeDate(po.getInviteTime());
        //未完成
        record.setOrderStatus(82411002);
        if (CommonConstants.INVITE_TYPE_I.equals(record.getInviteType())
                || CommonConstants.INVITE_TYPE_VII.equals(record.getInviteType())
                || CommonConstants.INVITE_TYPE_IX.equals(record.getInviteType())) {
            record.setLastInDate(po.getLastMaintainDate());
        } else if (CommonConstants.INVITE_TYPE_V.equals(record.getInviteType())) {
            record.setLastInDate(po.getInviteTime());
        } else if (CommonConstants.INVITE_TYPE_II.equals(record.getInviteType())
                || CommonConstants.INVITE_TYPE_VI.equals(record.getInviteType())
                || CommonConstants.INVITE_TYPE_XII.equals(record.getInviteType())) {
            record.setLastInDate(po.getLastMaintenanceDate());
        }
        //未跟进
        record.setFollowStatus(82401001);
        return record;
    }
    /**
     * 批量新增
     *  @param list
     * @param recordPOS
     * @param map1
     */
    private void batchLogAdd(List<InviteVehicleTaskPO> list, List<InviteVehicleRecordPO> recordPOS, Map<String, Long> map1) {
        inviteVehicleTaskMapper.insertList(list);
        for (InviteVehicleTaskPO po:list) {
            logger.info(CommonConstants.LOG_S19,JSONObject.toJSON(po));
            //下发线索
            recordPOS.add(this.createInviteByTaskVoc(po));
            map1.put(po.getVin() + po.getDealerCode(),po.getId());
        }
    }

    @Override
    public int inser1List2(List<InviteVehicleTaskPO> inst2) {
       return inser1List1(inst2);
    }

    @Override
    public int inser1List12(List<InviteVehicleTaskPO> inst12) {
        //生成任务
        //下发线索
        //线索字段表
        return inser1List1(inst12);
    }

    @Override
    public int inser1List6(List<InviteVehicleTaskPO> inst6) {
        //下发线索
        List<InviteVehicleRecordPO> recordPOS = new ArrayList<>(inst6.size());
        //线索字段表
        List<VocInviteVehicleTaskRecordPo> recordPOS1 = new ArrayList<>(inst6.size());
        Map<String,Long> map1 = new HashMap<>(inst6.size());
        //生成任务
        insertList1(inst6,recordPOS,map1);
        insertRecordList6(recordPOS,map1,recordPOS1);
        updateTaskList1(recordPOS1);
        insertRecordList2(recordPOS1);
        //新增流失客户报表
        this.saveLossList(recordPOS);
        this.taskAllocationVoc(recordPOS,false);
        return inst6.size();
    }

    private void insertRecordList6(List<InviteVehicleRecordPO> list, Map<String, Long> map1, List<VocInviteVehicleTaskRecordPo> recordPOS1) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int numPerTimes = 200;
        if (list.size() <= numPerTimes) {
            batchLogAdd6(list,recordPOS1,map1); //此处插入少于200条list
            logger.info(CommonConstants.LOG_S13);
        } else {

            int maxIndex = list.size();
            int maxTimes = maxIndex / numPerTimes;
            maxTimes += (maxIndex % numPerTimes) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimes) {
                int fromIndex = numPerTimes * currentTimes;
                int toIndex = fromIndex + numPerTimes;
                toIndex = toIndex > maxIndex ? maxIndex : toIndex;
                List<InviteVehicleRecordPO> subList = list.subList(fromIndex, toIndex);
                batchLogAdd6(subList,recordPOS1,map1);//此处循环插入200条list
                logger.info(CommonConstants.LOG_S12, fromIndex, toIndex);
                currentTimes++;
            }
        }
    }

    private void batchLogAdd6(List<InviteVehicleRecordPO> list, List<VocInviteVehicleTaskRecordPo> recordPOS1, Map<String, Long> map1) {
        inviteVehicleRecordMapper.insertList(list);
        for (InviteVehicleRecordPO po:list) {
            //下发线索
            recordPOS1.add(this.createInviteByTaskRecodeVoc6(po,map1));
        }
    }

    private VocInviteVehicleTaskRecordPo createInviteByTaskRecodeVoc6(InviteVehicleRecordPO po, Map<String, Long> map1) {
        VocInviteVehicleTaskRecordPo pox = new  VocInviteVehicleTaskRecordPo();
        pox.setInviteType(po.getInviteType());
        pox.setVin(po.getVin());
        pox.setRecordId(po.getId());
        pox.setTaskId(map1.get(po.getVin() + po.getDealerCode()));
        pox.setRecordType(CommonConstants.RECORD_TYPE_0);
        pox.setLossType(CommonConstants.LOSS_TYPE_3);
        logger.info("createInviteByTaskRecodeVoc6查询vocTask:{},{},{}",po.getVin(),po.getId(),map1.get(po.getVin()));

        return  pox;
    }





    @Override
    public int inser1List66(List<InviteVehicleTaskPO> inst66) {
        //线索字段表
        List<VocInviteVehicleTaskRecordPo> recordPOS1 = new ArrayList<>(inst66.size());
        //生成任务
        insertList66(inst66,recordPOS1);
        insertRecordList66(recordPOS1);
        return inst66.size();

    }

    private void insertRecordList66(List<VocInviteVehicleTaskRecordPo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int numPerTimes = 200;
        if (list.size() <= numPerTimes) {
            batchLogAdd66(list); //此处插入少于200条list
            logger.info(CommonConstants.LOG_S14);
        } else {

            int maxIndex = list.size();
            int maxTimes = maxIndex / numPerTimes;
            maxTimes += (maxIndex % numPerTimes) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimes) {
                int fromIndex = numPerTimes * currentTimes;
                int toIndex = fromIndex + numPerTimes;
                toIndex = toIndex > maxIndex ? maxIndex : toIndex;
                List<VocInviteVehicleTaskRecordPo> subList = list.subList(fromIndex, toIndex);
                batchLogAdd66(subList);//此处循环插入200条list
                logger.info(CommonConstants.LOG_S15, fromIndex, toIndex);
                currentTimes++;
            }
        }

    }
    private void batchLogAdd66( List<VocInviteVehicleTaskRecordPo> list) {
        vocInviteVehicleTaskRecordMapper.insertList(list);
    }

    /**
     * @param list
     * @param recordPOS
     */
    private void insertList66(List<InviteVehicleTaskPO> list, List<VocInviteVehicleTaskRecordPo> recordPOS) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int numPerTimes = 200;
        if (list.size() <= numPerTimes) {
            batchLogAdd66(list,recordPOS); //此处插入少于200条list
            logger.info(CommonConstants.LOG_S10);
        } else {

            int maxIndex = list.size();
            int maxTimes = maxIndex / numPerTimes;
            maxTimes += (maxIndex % numPerTimes) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimes) {
                int fromIndex = numPerTimes * currentTimes;
                int toIndex = fromIndex + numPerTimes;
                toIndex = toIndex > maxIndex ? maxIndex : toIndex;
                List<InviteVehicleTaskPO> subList = list.subList(fromIndex, toIndex);
                batchLogAdd66(subList,recordPOS);//此处循环插入200条list
                logger.info(CommonConstants.LOG_S11, fromIndex, toIndex);
                currentTimes++;
            }
        }

    }
    /**
     * 批量新增
     *  @param list
     * @param recordPOS
     */
    private void batchLogAdd66(List<InviteVehicleTaskPO> list, List<VocInviteVehicleTaskRecordPo> recordPOS) {
        inviteVehicleTaskMapper.insertList(list);
        for (InviteVehicleTaskPO po:list) {
            //下发线索
            VocInviteVehicleTaskRecordPo pox = new  VocInviteVehicleTaskRecordPo();
            pox.setInviteType(po.getInviteType());
            pox.setVin(po.getVin());
            pox.setTaskId(po.getId());
            pox.setRecordType(CommonConstants.RECORD_TYPE_0);
            pox.setLossType(CommonConstants.LOSS_TYPE_1);
            recordPOS.add(pox);
        }
    }

    @Override
    public int inser1List63(List<InviteVehicleTaskPO> inst63) {
        //线索字段表
        List<VocInviteVehicleTaskRecordPo> recordPOS1 = new ArrayList<>(inst63.size());
        //生成任务
        insertList63(inst63,recordPOS1);
        insertRecordList63(recordPOS1);
        return inst63.size();
    }



    /**
     * @param list
     * @param recordPOS
     */
    private void insertList63(List<InviteVehicleTaskPO> list, List<VocInviteVehicleTaskRecordPo> recordPOS) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int numPerTimes = 200;
        if (list.size() <= numPerTimes) {
            batchLogAdd63(list,recordPOS); //此处插入少于200条list
            logger.info(CommonConstants.LOG_S10);
        } else {

            int maxIndex = list.size();
            int maxTimes = maxIndex / numPerTimes;
            maxTimes += (maxIndex % numPerTimes) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimes) {
                int fromIndex = numPerTimes * currentTimes;
                int toIndex = fromIndex + numPerTimes;
                toIndex = toIndex > maxIndex ? maxIndex : toIndex;
                List<InviteVehicleTaskPO> subList = list.subList(fromIndex, toIndex);
                batchLogAdd63(subList,recordPOS);//此处循环插入200条list
                logger.info(CommonConstants.LOG_S11, fromIndex, toIndex);
                currentTimes++;
            }
        }

    }
    /**
     * 批量新增
     *  @param list
     * @param recordPOS
     */
    private void batchLogAdd63(List<InviteVehicleTaskPO> list, List<VocInviteVehicleTaskRecordPo> recordPOS) {
        inviteVehicleTaskMapper.insertList(list);
        for (InviteVehicleTaskPO po:list) {
            //下发线索
            VocInviteVehicleTaskRecordPo pox = new  VocInviteVehicleTaskRecordPo();
            pox.setInviteType(po.getInviteType());
            pox.setVin(po.getVin());
            pox.setTaskId(po.getId());
            pox.setRecordType(CommonConstants.RECORD_TYPE_0);
            pox.setLossType(CommonConstants.LOSS_TYPE_2);
            recordPOS.add(pox);
        }
    }

    private void insertRecordList63(List<VocInviteVehicleTaskRecordPo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int numPerTimes = 200;
        if (list.size() <= numPerTimes) {
            batchLogAdd63(list); //此处插入少于200条list
            logger.info(CommonConstants.LOG_S14);
        } else {

            int maxIndex = list.size();
            int maxTimes = maxIndex / numPerTimes;
            maxTimes += (maxIndex % numPerTimes) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimes) {
                int fromIndex = numPerTimes * currentTimes;
                int toIndex = fromIndex + numPerTimes;
                toIndex = toIndex > maxIndex ? maxIndex : toIndex;
                List<VocInviteVehicleTaskRecordPo> subList = list.subList(fromIndex, toIndex);
                batchLogAdd63(subList);//此处循环插入200条list
                logger.info(CommonConstants.LOG_S15, fromIndex, toIndex);
                currentTimes++;
            }
        }

    }
    private void batchLogAdd63( List<VocInviteVehicleTaskRecordPo> list) {
        vocInviteVehicleTaskRecordMapper.insertList(list);
    }


    @Override
    public int updateList(List<Long> upst6) {
        update(upst6);
        return upst6.size();
    }



    /**
     * @param list
     */
    private void update(List<Long> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        int numPerTimes = 200;
        if (list.size() <= numPerTimes) {
            batchRecordUpdate(list); //此处插入少于200条list
            logger.info(CommonConstants.LOG_S8);
        } else {
            int maxIndex = list.size();
            int maxTimes = maxIndex / numPerTimes;
            maxTimes += (maxIndex % numPerTimes) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimes) {
                int fromIndex = numPerTimes * currentTimes;
                int toIndex = fromIndex + numPerTimes;
                toIndex = toIndex > maxIndex ? maxIndex : toIndex;
                List<Long> subList = list.subList(fromIndex, toIndex);
                batchRecordUpdate(subList);//此处循环插入200条list
                logger.info(CommonConstants.LOG_S9, fromIndex, toIndex);
                currentTimes++;
            }
        }

    }
    /**
     * 批量修改
     *
     * @param list
     */
    private void batchRecordUpdate(List<Long> list) {
        inviteVehicleTaskMapper.updateList(list);
    }
    @Override
    public void createCustomerTaskVoc12(String vin, List<InviteVehicleTaskPO> inst12) {
        List<VehicleOwnerVO> list = reportCommonClient.queryFirstMaintainByVin(vin);
        if(CollUtil.isNotEmpty(list)){
            logger.info(CommonConstants.LOG_S1, list.size());
            VehicleOwnerVO vo =  list.get(0);
            this.createVocAlertTask(vo,inst12);
        }
    }



    /**
     * 创建流失预警线索
     *
     * @param vo InviteVehicleTaskPO
     */
    private void createVocAlertTask(VehicleOwnerVO vo , List<InviteVehicleTaskPO> inst12) {


        InviteVehicleTaskPO po = new InviteVehicleTaskPO();
        po.setVin(vo.getVin());
        po.setLicensePlateNum(vo.getPlateNumber());
        po.setDealerCode(vo.getDealerCode());
        po.setName(vo.getName());
        po.setTel(vo.getMobile());
        po.setAge(vo.getAge());
        po.setSex(vo.getSex());
        po.setModel(vo.getModelId());
        po.setDailyMileage(vo.getDailyAverageMileage());
        po.setInviteType(vo.getInviteType());
        po.setAdviseInDate(DateUtils.getDate());
        po.setRemindInterval(vo.getRemindInterval());
        po.setCloseInterval(vo.getCloseInterval());
        po.setIsCreateInvite(0);
        po.setInviteTime(vo.getLastMaintainDate());
        po.setCreateInviteTime(DateUtils.getDate());
        po.setAdviseInDate(DateUtils.getDate());
        po.setItemType(vo.getItemType());
        po.setItemCode(vo.getItemCode());
        po.setItemName(vo.getItemName());
        po.setAdviseInMileage(vo.getAdviseInMileage());
        po.setOutMileage(vo.getOutMileage());
        po.setQbNumber(vo.getQbNumber());
        po.setInviteType(CommonConstants.INVITE_TYPE_XII);
         inst12.add(po);

    }

    @Override
    public void createCustomerLossTaskVoc6(List<InviteVehicleTaskPO> inst6, String vin) {
        //生成任务
        List<VehicleOwnerVO> list = reportCommonClient.queryFirstMaintainByVin(vin);
        if(CollUtil.isNotEmpty(list)){
            logger.info(CommonConstants.LOG_S1, list.size());
            VehicleOwnerVO vo =  list.get(0);
            this.createCustomerLossTaskByNewCarSalesVoc6(vo,inst6);
        }

    }

    @Override
    public List<InviteVehicleTaskPO> selectListTaskVocByTime(String dateTime) {
        return inviteVehicleTaskMapper.selectListTaskVocByTime(dateTime);
    }

    @Override
    public int updateLossVocByVin(String vin) {
        return inviteVehicleTaskMapper.updateLossVocByVin(vin);
    }

    @Override
    public InviteVehicleRecordPO issued(InviteVehicleTaskPO po) {
       return createInviteByLossVocTask(DateUtils.getDate(),po);
    }

    @Override
    public void taskAllocationVocList(List<InviteVehicleRecordPO> pos) {
        this.taskAllocationVoc(pos,false);
    }




    /**
     * 创建邀约线索
     *
     * @param po
     * @return
     */
    public InviteVehicleRecordPO createInviteByLossVocTask(Date createDate, InviteVehicleTaskPO po) {
        // 新增线索下发逻辑
        InviteVehicleRecordPO record = new InviteVehicleRecordPO();
        record.setDealerCode(po.getDealerCode());
        //VCDC下发邀约
        record.setSourceType(1);
        record.setIsMain(1);
        record.setVin(po.getVin());
        record.setLicensePlateNum(po.getLicensePlateNum());
        record.setName(po.getName());
        record.setTel(po.getTel());
        record.setAge(po.getAge());
        record.setSex(po.getSex());
        record.setModel(po.getModel());
        record.setInviteType(po.getInviteType());
        record.setAdviseInDate(po.getAdviseInDate());
        record.setItemType(po.getItemType());
        record.setItemCode(po.getItemCode());
        record.setItemName(po.getItemName());
        record.setLastChangeDate(po.getInviteTime());
        //未完成
        record.setOrderStatus(82411002);
        if (CommonConstants.INVITE_TYPE_I.equals(record.getInviteType())
                || CommonConstants.INVITE_TYPE_VII.equals(record.getInviteType())
                || CommonConstants.INVITE_TYPE_IX.equals(record.getInviteType())) {
            record.setLastInDate(po.getLastMaintainDate());
        } else if (CommonConstants.INVITE_TYPE_V.equals(record.getInviteType())) {
            record.setLastInDate(po.getInviteTime());
        } else if (CommonConstants.INVITE_TYPE_II.equals(record.getInviteType())
                || CommonConstants.INVITE_TYPE_VI.equals(record.getInviteType())
                || CommonConstants.INVITE_TYPE_XII.equals(record.getInviteType())) {
            record.setLastInDate(po.getLastMaintenanceDate());
        }


        //未跟进
        record.setFollowStatus(82401001);
            record.setCreatedAt(createDate);
        inviteVehicleRecordMapper.insert(record);
        //更新邀约任务
        po.setIsCreateInvite(1);
        po.setInviteId(record.getId());
        inviteVehicleTaskMapper.updateById(po);
        vocInviteVehicleTaskRecordMapper.updateByPo(po.getId(),record.getId());
        saveLoss(po.getVin(), po.getDealerCode(), po.getCreatedAt(), po.getId());
        return record;
    }

    private void saveLoss(String vin, String dealerCode, Date createdAt, Long id) {
        RepairVo vo = reportCommonClient.queryRepairOrderByVinAndCodeLimitOne(vin, dealerCode);
        LossDataRecordPo pox = new LossDataRecordPo();
        pox.setVin(vin);
        pox.setDealerCode(dealerCode);
        pox.setLastDealerCode(vo.getLastDealerCode());
        pox.setOrderAt(vo.getOrderAt());
        pox.setRecordAt(createdAt);
        pox.setInvoiceDate(vo.getInvoiceDate());
        pox.setIsActive(0);
        pox.setOwnerName(vo.getOwnerName());
        pox.setMobile(vo.getMobile());
        pox.setRecordId(id);
        lossDataRecordService.save(pox);
    }

    private void saveVocInviteVehicleTaskRecordPo(InviteVehicleTaskPO po) {
        logger.info("saveVocInviteVehicleTaskRecordPo:{}",JSONObject.toJSON(po));
        LambdaQueryWrapper<VocInviteVehicleTaskRecordPo>  qw = new LambdaQueryWrapper<>();
        qw.eq(VocInviteVehicleTaskRecordPo::getTaskId,po.getId());
        qw.eq(VocInviteVehicleTaskRecordPo::getIsDeleted,0);
        VocInviteVehicleTaskRecordPo pox =  vocInviteVehicleTaskRecordMapper.selectOne(qw);
        if(pox==null ){
            VocInviteVehicleTaskRecordPo vocInviteVehicleTaskRecordPo = new  VocInviteVehicleTaskRecordPo();
            vocInviteVehicleTaskRecordPo.setTaskId(po.getId());
            if(po.getInviteId()!=null){
                vocInviteVehicleTaskRecordPo.setRecordId(po.getInviteId());
            }
            vocInviteVehicleTaskRecordPo.setRecordType(RecordTypeEnum.POST_SALE.getIntCode());
            if(CommonConstants.INVITE_TYPE_VI.equals(po.getInviteType())){
                vocInviteVehicleTaskRecordPo.setLossType(CommonConstants.LOSS_TYPE_1);
            }else{
                vocInviteVehicleTaskRecordPo.setLossType(CommonConstants.LOSS_TYPE_0);
            }
            vocInviteVehicleTaskRecordPo.setVin(po.getVin());
            vocInviteVehicleTaskRecordPo.setInviteType(po.getInviteType());
            vocInviteVehicleTaskRecordPo.setCreatedBy("-9");
            //验证状态
            vocInviteVehicleTaskRecordPo.setVerifyStatus(VerifyTypeEnum.NOT_STARTED.getIntCode());
            //验证变更时间
            vocInviteVehicleTaskRecordPo.setVerifyTime(LocalDate.now());
            vocInviteVehicleTaskRecordMapper.insert(vocInviteVehicleTaskRecordPo);
        }else{
            pox.setRecordId(po.getInviteId());
            //验证状态
            pox.setVerifyStatus(VerifyTypeEnum.NOT_STARTED.getIntCode());
            //验证变更时间
            pox.setVerifyTime(LocalDate.now());
            vocInviteVehicleTaskRecordMapper.updateById(pox);
        }
    }
    private void updateVocInviteVehicleTaskRecordPo(InviteVehicleTaskPO po) {
        logger.info("updateVocInviteVehicleTaskRecordPo:{}",JSONObject.toJSON(po));
        LambdaQueryWrapper<VocInviteVehicleTaskRecordPo>  qw = new LambdaQueryWrapper<>();
        qw.eq(VocInviteVehicleTaskRecordPo::getTaskId,po.getId());
        qw.eq(VocInviteVehicleTaskRecordPo::getIsDeleted,0);
        VocInviteVehicleTaskRecordPo pox =  vocInviteVehicleTaskRecordMapper.selectOne(qw);
        if(pox != null){
            pox.setRecordId(po.getInviteId());
            //新增验证状态
            pox.setVerifyStatus(VerifyTypeEnum.NOT_STARTED.getIntCode());
            pox.setVerifyTime(LocalDate.now());
            pox.setRecordType(RecordTypeEnum.POST_SALE.getIntCode());
            pox.setUpdatedAt(new Date());
            vocInviteVehicleTaskRecordMapper.updateById(pox);
        }else{
            //扩展表
            VocInviteVehicleTaskRecordPo recPo = new VocInviteVehicleTaskRecordPo();
            recPo.setRecordId(po.getInviteId());
            //线索类型
            recPo.setRecordType(RecordTypeEnum.POST_SALE.getIntCode());
            //邀约类型
            recPo.setInviteType(po.getInviteType());
            //车架号
            recPo.setVin(po.getVin());
            //验证状态
            recPo.setVerifyStatus(VerifyTypeEnum.NOT_STARTED.getIntCode());
            //验证变更时间
            recPo.setVerifyTime(LocalDate.now());
            //流失类型
            if(CommonConstants.INVITE_TYPE_VI.equals(po.getInviteType())){
                recPo.setLossType(CommonConstants.LOSS_TYPE_1);
            }else{
                recPo.setLossType(CommonConstants.LOSS_TYPE_0);
            }
            //添加扩展表数据
            taskRecordService.addRecord(recPo);
        }
    }


    @Override
    public void losspush(String dateTime) {
        logger.info(CommonConstants.LOG_S25,dateTime);
    //需要初始化所有VIN，如果在18个月内没有保养进厂，生成该报表数据
     if(StringUtils.isNullOrEmpty(dateTime)){
         dateTime = DateUtils.dateToString(new Date(), DateUtils.PATTERN_YYYY_MM_DD);
     }

        pageList(dateTime);
        logger.info("客户流失初始化结束");
    }

    private void pageList(String dateTime) {
        //分页查询
        Integer returnCount = 0;
        Integer partitionSize = 200;
        Integer start = 0;
        long startTimeMillis = System.currentTimeMillis();
        logger.info("客户流失初始化,createDate:{},startTimeMillis:{}", dateTime, startTimeMillis);
        List<String> list = null;
        while (true) {
            Integer begIndex = start * partitionSize;
            logger.info("客户流失初始化,createDate:{},begIndex:{},partitionSize:{}", dateTime, begIndex, partitionSize);
            list = reportCommonClient.selectVin(begIndex, partitionSize);
            if (list == null || list.isEmpty()) {
                logger.info("---客户流失初始化,执行完成---");
                break;
            } else {
                start++;
                logger.info("---客户流失初始化,开始执行---,start:{},size:{}", start, list.size());
                int i = issuedTo(list, dateTime);
                returnCount += i;
                logger.info("客户流失初始化,returnCount:{}", returnCount);
            }
        }
        long endTimeMillis = System.currentTimeMillis();
        long execTime = endTimeMillis - startTimeMillis;
        logger.info("客户流失初始化总分页数,returnCount:{},endTimeMillis:{},execTime:{}", returnCount, endTimeMillis, execTime);
    }

    private int issuedTo(List<String> list, String dateTime) {
        logger.info(CommonConstants.LOG_S25,dateTime);
        logger.info(CommonConstants.LOG_S25,JSONObject.toJSON(list));
        if(CollUtil.isNotEmpty(list)){
            logger.info(CommonConstants.LOG_S25,list.size());
            List<RepairVo> vos = reportCommonClient.queryVinByRepair(dateTime,list);
            if(CollUtil.isNotEmpty(vos)){
                logger.info(CommonConstants.LOG_S25,vos.size());
                List<LossDataRecordPo> pos  = new ArrayList<>(vos.size());
                for ( RepairVo vo: vos) {
                    LossDataRecordPo pox = new LossDataRecordPo();
                    pox.setVin(vo.getVin());
                    pox.setDealerCode(vo.getDealerCode());
                    pox.setLastDealerCode(vo.getDealerCode());
                    pox.setOrderAt(vo.getOrderAt());
                    pox.setRecordAt(DateUtils.getDate());
                    pox.setInvoiceDate(vo.getInvoiceDate());
                    pox.setIsActive(0);
                    pox.setOwnerName(vo.getOwnerName());
                    pox.setMobile(vo.getMobile());
                    pox.setRecordId(0L);
                    pos.add(pox);
                }
                if(CollUtil.isNotEmpty(pos)){
                    lossDataRecordService.insertList(pos);
                }
                logger.info("客户流失初始化结束：{}",pos.size());
            }
        }
        return  1;
    }
    @Override
    public InviteVehicleTaskPO createCustomerTaskVoc6(InviteVehicleRecordPO po) {

        return createInviteLossVocTask(po);
    }



    private InviteVehicleTaskPO createInviteLossVocTask(InviteVehicleRecordPO pox) {
           List<InviteVehicleTaskPO> vos = inviteVehicleTaskMapper.selectByInviteId(pox.getId());
           if(CollUtil.isNotEmpty(vos)){
               InviteVehicleTaskPO vo = vos.get(0);
               InviteVehicleTaskPO po = new InviteVehicleTaskPO();
               po.setVin(vo.getVin());
               po.setLicensePlateNum(vo.getLicensePlateNum());
               po.setDealerCode(vo.getDealerCode());
               po.setName(vo.getName());
               po.setTel(vo.getTel());
               po.setAge(vo.getAge());
               po.setSex(vo.getSex());
               po.setModel(vo.getModel());
               po.setDailyMileage(vo.getDailyAverageMileage());
               po.setInviteType(vo.getInviteType());
               po.setAdviseInDate(DateUtils.getDate());
               po.setRemindInterval(vo.getRemindInterval());
               po.setCloseInterval(vo.getCloseInterval());
               po.setIsCreateInvite(0);
               po.setInviteTime(vo.getLastMaintainDate());
               po.setCreateInviteTime(DateUtils.getDate());
               po.setItemType(vo.getItemType());
               po.setItemCode(vo.getItemCode());
               po.setItemName(vo.getItemName());
               po.setAdviseInMileage(vo.getAdviseInMileage());
               po.setOutMileage(vo.getOutMileage());
               po.setQbNumber(vo.getQbNumber());
               return  po;
           }
           return  null;

    }

    /**
     * 创建邀约任务
     *
     * @param vo
     */
    private void createInviteTaskNew(VehicleOwnerVO vo, Integer month) {

        InviteVehicleTaskPO po = new InviteVehicleTaskPO();
        po.setVin(vo.getVin());
        po.setLicensePlateNum(vo.getPlateNumber());
        po.setDealerCode(vo.getDealerCode());
        po.setName(vo.getName());
        po.setTel(vo.getMobile());
        po.setAge(vo.getAge());
        po.setSex(vo.getSex());
        po.setModel(vo.getModelId());
        po.setDailyMileage(vo.getDailyAverageMileage());
        po.setInviteType(vo.getInviteType());
        po.setAdviseInDate(vo.getAdviseInDate());
        po.setRemindInterval(vo.getRemindInterval());
        po.setCloseInterval(vo.getCloseInterval());
        po.setIsCreateInvite(0);
        po.setInviteTime(vo.getLastMaintainDate());
        Calendar c = Calendar.getInstance();
        c.setTime(po.getAdviseInDate());
        c.add(Calendar.MONTH, month);
        po.setCreateInviteTime(c.getTime());
        po.setItemType(vo.getItemType());
        po.setItemCode(vo.getItemCode());
        po.setItemName(vo.getItemName());
        po.setAdviseInMileage(vo.getAdviseInMileage());
        po.setOutMileage(vo.getOutMileage());
        po.setQbNumber(vo.getQbNumber());
        inviteVehicleTaskMapper.insert(po);
        logger.info("2096定保，流失客户任务：{}，{}，{}，{}",po.getVin(),po.getInviteType(),po.getId(),po.getInviteId());
        this.saveVocInviteVehicleTaskRecordPo(po);
    }

    /**
     * 客户流失
     *
     * @param vo
     * @param inst6
     */
    private void createCustomerLossTaskByNewCarSalesVoc6(VehicleOwnerVO vo,  List<InviteVehicleTaskPO> inst6) {
        logger.info(CommonConstants.LOG_S7, vo.getVin());
        //查询客户流失间隔规则
        InviteRulePO rule = inviteRuleMapper.getInvitationDlrRule(vo.getDealerCode(), 82031004, 82041005);
        logger.info(CommonConstants.LOG_S21, rule);
        if (rule == null || rule.getIsUse() == null || rule.getIsUse() == 0) {
            //不存在客户流失间隔规则，跳过
            return  ;
        }
        vo.setInviteType(CommonConstants.INVITE_TYPE_VI);
        //基准日期
        vo.setLastMaintainDate(vo.getInvoiceDate());
        logger.info(CommonConstants.LOG_S4, vo.getInvoiceDate());
        Calendar c = Calendar.getInstance();
        c.setTime(DateUtils.getDate());
        vo.setAdviseInDate(c.getTime());
        logger.info(CommonConstants.LOG_S6, vo.getAdviseInDate());
        vo.setDayInAdvance(rule.getDayInAdvance());
        vo.setInviteRule(rule.getInviteRule());
        vo.setRuleValue(rule.getRuleValue());
        vo.setRemindInterval(rule.getRemindInterval());
        vo.setCloseInterval(rule.getCloseInterval());
        inst6.add( this.createLossInviteTaskVoc6(vo));
    }
    /**
     * 创建邀约任务
     *
     * @param vo
     */
    private InviteVehicleTaskPO createLossInviteTaskVoc6(VehicleOwnerVO vo) {
        InviteVehicleTaskPO po = new InviteVehicleTaskPO();
        po.setVin(vo.getVin());
        po.setLicensePlateNum(vo.getPlateNumber());
        po.setDealerCode(vo.getDealerCode());
        po.setName(vo.getName());
        po.setTel(vo.getMobile());
        po.setAge(vo.getAge());
        po.setSex(vo.getSex());
        po.setModel(vo.getModelId());
        po.setDailyMileage(vo.getDailyAverageMileage());
        po.setInviteType(vo.getInviteType());
        po.setAdviseInDate(vo.getAdviseInDate());
        po.setRemindInterval(vo.getRemindInterval());
        po.setCloseInterval(vo.getCloseInterval());
        po.setIsCreateInvite(0);
        po.setInviteTime(DateUtils.getDate());
        po.setCreateInviteTime(DateUtils.getDate());
        po.setItemType(vo.getItemType());
        po.setItemCode(vo.getItemCode());
        po.setItemName(vo.getItemName());
        po.setAdviseInMileage(vo.getAdviseInMileage());
        po.setOutMileage(vo.getOutMileage());
        po.setQbNumber(vo.getQbNumber());
        return po ;
    }

    @Override
    public void inser123List12(List<InviteVehicleTaskPO> inst123) {
        //线索字段表
        List<VocInviteVehicleTaskRecordPo> recordPOS1 = new ArrayList<>(inst123.size());
        Map<String,Long> map1 = new HashMap<>(inst123.size());
        List<InviteVehicleRecordPO> recordPOS = new ArrayList<>(inst123.size());
        //生成任务
        insertList123(inst123,recordPOS,map1);
        insert12RecordList123(recordPOS,map1,recordPOS1);
        logger.info(CommonConstants.LOG_S18,JSONObject.toJSON(map1));
        updateTaskList1(recordPOS1);
        insertRecordList2(recordPOS1);
        this.taskAllocationVoc(recordPOS,false);
    }

    /**
     * @param list
     * @param map1
     */
    private void insertList123(List<InviteVehicleTaskPO> list,List<InviteVehicleRecordPO> recordPOS, Map<String,Long> map1) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int numPerTimes = 200;
        if (list.size() <= numPerTimes) {
            batchLogAdd123(list,recordPOS,map1); //此处插入少于200条list
            logger.info(CommonConstants.LOG_S10);
        } else {

            int maxIndex = list.size();
            int maxTimes = maxIndex / numPerTimes;
            maxTimes += (maxIndex % numPerTimes) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimes) {
                int fromIndex = numPerTimes * currentTimes;
                int toIndex = fromIndex + numPerTimes;
                toIndex = toIndex > maxIndex ? maxIndex : toIndex;
                List<InviteVehicleTaskPO> subList = list.subList(fromIndex, toIndex);
                batchLogAdd123(subList,recordPOS,map1);//此处循环插入200条list
                logger.info(CommonConstants.LOG_S11, fromIndex, toIndex);
                currentTimes++;
            }
        }

    }

    /**
     * 批量新增
     *  @param list
     * @param recordPOS
     */
    private void batchLogAdd123(List<InviteVehicleTaskPO> list, List<InviteVehicleRecordPO> recordPOS,Map<String ,Long> map1) {
        inviteVehicleTaskMapper.insertList(list);
        for (InviteVehicleTaskPO po:list) {
            //下发线索
          recordPOS.add(this.createInviteByTaskVoc(po));
            map1.put(po.getVin() + po.getDealerCode(),po.getId());
        }
    }

    @Override
    public void createCustomerLossTaskVoc63(List<InviteVehicleTaskPO> inst63, String vin) {
        //生成任务
        List<VehicleOwnerVO> list = reportCommonClient.queryFirstMaintainByVin(vin);
        if(CollUtil.isNotEmpty(list)){
            logger.info(CommonConstants.LOG_S1, list.size());
            VehicleOwnerVO vo =  list.get(0);
            this.createCustomerLossTaskByNewCarSalesVoc63(vo,inst63);
        }

    }

    /**
     * 客户流失
     *
     * @param vo
     * @param inst6
     */
    private void createCustomerLossTaskByNewCarSalesVoc63(VehicleOwnerVO vo,  List<InviteVehicleTaskPO> inst63) {
        logger.info(CommonConstants.LOG_S7, vo.getVin());
        //查询客户流失间隔规则
        InviteRulePO rule = inviteRuleMapper.getInvitationDlrRule(vo.getDealerCode(), 82031004, 82041005);
        logger.info(CommonConstants.LOG_S21, rule);
        if (rule == null || rule.getIsUse() == null || rule.getIsUse() == 0) {
            //不存在客户流失间隔规则，跳过
            return  ;
        }
        vo.setDayInAdvance(rule.getDayInAdvance());
        vo.setInviteRule(rule.getInviteRule());
        vo.setRuleValue(rule.getRuleValue());
        vo.setRemindInterval(rule.getRemindInterval());
        vo.setCloseInterval(rule.getCloseInterval());
        vo.setInviteType(CommonConstants.INVITE_TYPE_VI);
        //基准日期
        vo.setLastMaintainDate(vo.getInvoiceDate());
        logger.info(CommonConstants.LOG_S4, vo.getInvoiceDate());
        Calendar c = Calendar.getInstance();
        c.setTime(DateUtils.getDate());
        logger.info(CommonConstants.LOG_S5, rule.getRuleValue());
        vo.setAdviseInDate(c.getTime());
        logger.info(CommonConstants.LOG_S6, vo.getAdviseInDate());
        inst63.add( this.createLossInviteTaskVoc63(vo));
    }

    /**
     * 创建邀约任务
     *
     * @param vo
     */
    private InviteVehicleTaskPO createLossInviteTaskVoc63(VehicleOwnerVO vo) {
        InviteVehicleTaskPO po = new InviteVehicleTaskPO();
        po.setVin(vo.getVin());
        po.setLicensePlateNum(vo.getPlateNumber());
        po.setDealerCode(vo.getDealerCode());
        po.setInviteType(vo.getInviteType());
        po.setAdviseInDate(vo.getAdviseInDate());
        po.setRemindInterval(vo.getRemindInterval());
        po.setCloseInterval(vo.getCloseInterval());
        po.setIsCreateInvite(0);
        po.setName(vo.getName());
        po.setTel(vo.getMobile());
        po.setAge(vo.getAge());
        po.setSex(vo.getSex());
        po.setModel(vo.getModelId());
        po.setDailyMileage(vo.getDailyAverageMileage());
        po.setInviteTime(DateUtils.getDate());
        po.setCreateInviteTime(DateUtils.getAfterDate(DateUtils.getDate(),3));
        po.setItemType(vo.getItemType());
        po.setItemName(vo.getItemName());
        po.setAdviseInMileage(vo.getAdviseInMileage());
        po.setOutMileage(vo.getOutMileage());
        po.setQbNumber(vo.getQbNumber());
        po.setItemCode(vo.getItemCode());
        return po ;
    }

    private Map<String, String> getbmd() {
        List<String>  list =   whitelistQueryService.selectWhiteListString(CommonConstants.MODTYPE_91111002,0);
        if(CollUtil.isNotEmpty(list)){

            List<String>  vins =  reportCommonClient.selectVinByCode(list);
            if(CollUtil.isNotEmpty(vins)){
                return  vins.stream().collect(Collectors.toMap(item ->item,item->item,(item1, item2) -> item1));
            }
        }
        return  new HashMap<>(1);
    }
    @Override
    public void createCustomerLossTaskVoc63v(List<InviteVehicleTaskPO> inst63, String vin,String code) {
        //生成任务
        List<VehicleOwnerVO> list = reportCommonClient.queryFirstMaintainByVin(vin);
        if(CollUtil.isNotEmpty(list)){
            logger.info(CommonConstants.LOG_S1, list.size());
            VehicleOwnerVO vo =  list.get(0);
            vo.setDealerCode(code);
            this.createCustomerLossTaskByNewCarSalesVoc63(vo,inst63);
        }

    }

    private void insert12RecordList123(List<InviteVehicleRecordPO> list, Map<String, Long> map1, List<VocInviteVehicleTaskRecordPo> recordPOS1) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int numPerTimes = 200;
        if (list.size() <= numPerTimes) {
            batch12Add123(list,recordPOS1,map1); //此处插入少于200条list
            logger.info(CommonConstants.LOG_S13);
        } else {

            int maxIndex = list.size();
            int maxTimes = maxIndex / numPerTimes;
            maxTimes += (maxIndex % numPerTimes) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimes) {
                int fromIndex = numPerTimes * currentTimes;
                int toIndex = fromIndex + numPerTimes;
                toIndex = toIndex > maxIndex ? maxIndex : toIndex;
                List<InviteVehicleRecordPO> subList = list.subList(fromIndex, toIndex);
                batch12Add123(subList,recordPOS1,map1);//此处循环插入200条list
                logger.info(CommonConstants.LOG_S12, fromIndex, toIndex);
                currentTimes++;
            }
        }
    }

    private void batch12Add123(List<InviteVehicleRecordPO> list, List<VocInviteVehicleTaskRecordPo> recordPOS1, Map<String, Long> map1) {
        inviteVehicleRecordMapper.insertList(list);
        for (InviteVehicleRecordPO po:list) {
            //下发线索
            recordPOS1.add(this.createInviteByTaskRecodeVoc123(po,map1));
        }
    }

    private VocInviteVehicleTaskRecordPo createInviteByTaskRecodeVoc123(InviteVehicleRecordPO po, Map<String, Long> map1) {
        VocInviteVehicleTaskRecordPo pox = new  VocInviteVehicleTaskRecordPo();
        pox.setRecordId(po.getId());
        pox.setTaskId(map1.get(po.getVin() + po.getDealerCode()));
        pox.setVin(po.getVin());
        pox.setRecordType(CommonConstants.RECORD_TYPE_1);
        pox.setLossType(CommonConstants.LOSS_TYPE_0);
        pox.setInviteType(po.getInviteType());
        logger.info("createInviteByTaskRecodeVoc123查询vocTask:{},{},{}",po.getVin(),po.getId(),map1.get(po.getVin()));
        return  pox;
    }

    private void saveLossList(List<InviteVehicleRecordPO>  recordPOS) {
        if(CollUtil.isNotEmpty(recordPOS)){
            List<LossDataRecordPo> list = new ArrayList<>(recordPOS.size());
            for (InviteVehicleRecordPO po:recordPOS) {
                RepairVo vo = reportCommonClient.queryRepairOrderByVinAndCodeLimitOne(po.getVin(), po.getDealerCode());
                LossDataRecordPo pox = new LossDataRecordPo();
                pox.setVin(po.getVin());
                pox.setDealerCode(po.getDealerCode());
                pox.setLastDealerCode(vo.getLastDealerCode());
                pox.setOrderAt(vo.getOrderAt());
                pox.setRecordAt(po.getCreatedAt());
                pox.setInvoiceDate(vo.getInvoiceDate());
                pox.setIsActive(0);
                pox.setOwnerName(vo.getOwnerName());
                pox.setMobile(vo.getMobile());
                pox.setRecordId(po.getId());
                list.add(pox);
            }
            if(CollUtil.isNotEmpty(list)){
                lossDataRecordService.insertList(list);
            }
        }

    }
    @Override
    public int saveListInit6(List<InviteVehicleTaskPO> inst6) {
        //下发线索
        List<InviteVehicleRecordPO> recordPOS = new ArrayList<>(inst6.size());
        //线索字段表
        List<VocInviteVehicleTaskRecordPo> recordPOS1 = new ArrayList<>(inst6.size());
        Map<String,Long> map1 = new HashMap<>(inst6.size());
        //生成任务
        insertList1(inst6,recordPOS,map1);
        insertRecordList6(recordPOS,map1,recordPOS1);
        updateTaskList1(recordPOS1);
        insertRecordList2(recordPOS1);
        this.taskAllocationVoc(recordPOS,false);
        return inst6.size();
    }


    @Override
    public void createCustomerTaskVoc12v(String vin, List<InviteVehicleTaskPO> inst12,String code) {
        List<VehicleOwnerVO> list = reportCommonClient.queryFirstMaintainByVin(vin);
        if(CollUtil.isNotEmpty(list)){
            logger.info(CommonConstants.LOG_S1, list.size());
            VehicleOwnerVO vo =  list.get(0);
            vo.setDealerCode(code);
            this.createVocAlertTask(vo,inst12);
        }
    }

    @Override
    public void batchQueryUserInfo(List<VehicleOwnerVO> list){
        logger.info("batchQueryUserInfo,start");
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        logger.info("batchQueryUserInfo,list:{}", list.size());
        List<Long> ids = list.stream().map(VehicleOwnerVO::getCustomerId).distinct().collect(Collectors.toList());
        logger.info("batchQueryUserInfo,ids:{}", ids.size());
        List<List<Long>> partition = Lists.partition(ids, 500);
        VehicleOwnerSelectByIdListDTO dto;
        List<VehicleOwnerDetailByIdListDTO> listDto;
        List<VehicleOwnerDetailByIdListDTO> pageOwner = new ArrayList<>();
        for (List<Long> longs : partition) {
            dto = new VehicleOwnerSelectByIdListDTO();
            dto.setList(longs);
            listDto = selectVehicleOwnerByIdList(dto);
            if(CollectionUtils.isNotEmpty(listDto)){
                pageOwner.addAll(listDto);
            }
        }
        logger.info("batchQueryUserInfo,pageOwner:{}", pageOwner.size());
        Map<Long,VehicleOwnerDetailByIdListDTO> map = pageOwner.stream().collect(Collectors.toMap(VehicleOwnerDetailByIdListDTO::getOneId, Function.identity(), (key1, key2) -> key2));
        //赋值
        VehicleOwnerDetailByIdListDTO owner;
        Long customerId;
        for (VehicleOwnerVO vo : list) {
            customerId = vo.getCustomerId();
            owner = map.get(customerId);
            if(ObjectUtil.isEmpty(owner)){
                logger.info("batchQueryUserInfo,ObjectUtil.isEmpty(owner)");
                continue;
            }
            vo.setName(owner.getName());
            vo.setMobile(owner.getMobile());
            vo.setSex(ObjectUtils.toString(owner.getGender(), null));
            vo.setAge(getDetailedAge(owner.getBirthday()));
        }
        logger.info("batchQueryUserInfo,end");
    }

    private List<VehicleOwnerDetailByIdListDTO> selectVehicleOwnerByIdList(VehicleOwnerSelectByIdListDTO dto){
        logger.info("selectVehicleOwnerByIdList,start");
        List<VehicleOwnerDetailByIdListDTO> dtoList;
        if (ObjectUtil.isEmpty(dto)){
            logger.info("selectVehicleOwnerByIdList,ObjectUtil.isEmpty(dto)");
            return null;
        }
        List<Long> listLong = dto.getList();
        if(CollectionUtils.isEmpty(listLong)){
            logger.info("selectVehicleOwnerByIdList,CollectionUtils.isEmpty(listLong)");
            return null;
        }
        logger.info("selectVehicleOwnerByIdList,listLong:{}", listLong.size());
        ResponseDTO<List<VehicleOwnerDetailByIdListDTO>> list;
        try {
            list = midEndCustomerCenterClient.selectVehicleOwnerByOneIdList(dto);
        } catch (Exception e) {
            logger.info("selectVehicleOwnerByIdList,e:{}", e);
            throw new RuntimeException("selectVehicleOwnerByIdList,fail");
        }
        dtoList = list.getData();
        logger.info("selectVehicleOwnerByIdList,end");
        return dtoList;
    }

    /**
     * 计算年龄
     * @param birth
     * @return
     */
    public static String getDetailedAge(LocalDate birth) {
        if(birth == null){
            return null;
        }
        LocalDate nowDate = LocalDate.now();
        //使用period就可以获得年份
        Period period = Period.between(birth, nowDate);
        int age = period.getYears();
        return ObjectUtils.toString(age, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doRepairLossTask(Integer page, List<InviteVehicleTaskPO> addList, List<Long> delList, List<InviteVehicleTaskPO> upList) {
        long startTime = new Date().getTime();
        logger.info("doRepairLossTask, start:{}, page:{}", startTime, page);
        //批量修改
        if (CollectionUtils.isNotEmpty(upList)) {
            logger.info("doRepairLossTask,page:{}, upList:{}", page, upList.size());
            Lists.partition(upList,500).forEach(inviteVehicleTaskMapper::updateLossUpList);
        }
        //批量删除
        if(CollectionUtils.isNotEmpty(delList)){
            logger.info("doRepairLossTask,page:{}, delList:{}", page, delList.size());
            Lists.partition(delList,500).forEach(inviteVehicleTaskMapper::updateLossDelList);
        }

        //批量新增
        if (CollectionUtils.isNotEmpty(addList)) {
            logger.info("doRepairLossTask,page:{}, addList:{}", page, addList.size());
            Lists.partition(addList,500).forEach(inviteVehicleTaskMapper::insertLossAddList);
            List<VocInviteVehicleTaskRecordPo> list = new ArrayList<>();
            for (InviteVehicleTaskPO po : addList) {
                //扩展表
                VocInviteVehicleTaskRecordPo vocPo = new  VocInviteVehicleTaskRecordPo();
                Long id = po.getId();
                logger.info("doRepairLossTask,id:{}",id);
                vocPo.setTaskId(id);
                if(po.getInviteId()!=null){
                    vocPo.setRecordId(po.getInviteId());
                }
                vocPo.setRecordType(1);
                if(CommonConstants.INVITE_TYPE_VI.equals(po.getInviteType())){
                    vocPo.setLossType(CommonConstants.LOSS_TYPE_1);
                }else{
                    vocPo.setLossType(CommonConstants.LOSS_TYPE_0);
                }
                vocPo.setVin(po.getVin());
                vocPo.setInviteType(po.getInviteType());
                vocPo.setCreatedBy("-9");
                list.add(vocPo);
            }
            if(CollectionUtils.isNotEmpty(list)){
                logger.info("doRepairLossTask,list:{}",list.size());
                Lists.partition(list,500).forEach(vocInviteVehicleTaskRecordMapper::insertList);
            }
        }
        long endTime = new Date().getTime();
        logger.info("doRepairLossTask,end:{},page:{}, takeUpTime:{}", endTime,page, endTime - startTime);
    }
}
