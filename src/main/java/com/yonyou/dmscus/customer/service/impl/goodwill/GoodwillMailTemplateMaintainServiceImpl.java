package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillMailTemplateMaintainMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMailTemplateMaintainDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMailTemplateMaintainPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillMailTemplateMaintainService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 亲善邮件模板维护 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-28
 */
@Service
public class GoodwillMailTemplateMaintainServiceImpl
		extends ServiceImpl<GoodwillMailTemplateMaintainMapper, GoodwillMailTemplateMaintainPO>
		implements GoodwillMailTemplateMaintainService {
	/** 日志对象 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillMailTemplateMaintainMapper goodwillMailTemplateMaintainMapper;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillMailTemplateMaintainDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMailTemplateMaintainDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@SuppressWarnings("unchecked")
	@Override
	public IPage<GoodwillMailTemplateMaintainDTO> selectPageBysql(Page page,
			GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO) {
		if (goodwillMailTemplateMaintainDTO == null) {
			goodwillMailTemplateMaintainDTO = new GoodwillMailTemplateMaintainDTO();
		}
		GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainDTO
				.transDtoToPo(GoodwillMailTemplateMaintainPO.class);

		List<GoodwillMailTemplateMaintainPO> list = goodwillMailTemplateMaintainMapper.selectPageBySql(page,
				goodwillMailTemplateMaintainPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillMailTemplateMaintainDTO> result = list.stream()
					.map(m -> m.transPoToDto(GoodwillMailTemplateMaintainDTO.class)).collect(Collectors.toList());
			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillMailTemplateMaintainDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMailTemplateMaintainDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillMailTemplateMaintainDTO> selectListBySql(
			GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO) {
		if (goodwillMailTemplateMaintainDTO == null) {
			goodwillMailTemplateMaintainDTO = new GoodwillMailTemplateMaintainDTO();
		}
		GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainDTO
				.transDtoToPo(GoodwillMailTemplateMaintainPO.class);
		List<GoodwillMailTemplateMaintainPO> list = goodwillMailTemplateMaintainMapper
				.selectListBySql(goodwillMailTemplateMaintainPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			for (GoodwillMailTemplateMaintainPO po : list) {
				if (!StringUtils.isNullOrEmpty(po.getSendObject())) {
					po.setSendObjects(po.getSendObject().split(","));
				}
			}
			return list.stream().map(m -> m.transPoToDto(GoodwillMailTemplateMaintainDTO.class))
					.collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMailTemplateMaintainDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillMailTemplateMaintainDTO getById(Long id) {
		GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainMapper
				.selectById(id);
		if (goodwillMailTemplateMaintainPo != null) {
			return goodwillMailTemplateMaintainPo.transPoToDto(GoodwillMailTemplateMaintainDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillMailTemplateMaintainDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO) {
		// 对对象进行赋值操作
		GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainDTO
				.transDtoToPo(GoodwillMailTemplateMaintainPO.class);
		// 执行插入
		int row = goodwillMailTemplateMaintainMapper.insert(goodwillMailTemplateMaintainPo);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillMailTemplateMaintainDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO) {
		GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainMapper
				.selectById(id);
		// 对对象进行赋值操作
		goodwillMailTemplateMaintainDTO.transDtoToPo(goodwillMailTemplateMaintainPo);
		// 执行更新
		int row = goodwillMailTemplateMaintainMapper.updateById(goodwillMailTemplateMaintainPo);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillMailTemplateMaintainMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillMailTemplateMaintainMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据DTO进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillMailTemplateMaintainDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int updateEmailMaintain(List<GoodwillMailTemplateMaintainDTO> list) {
		for (GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO : list) {
			GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainMapper
					.selectById(goodwillMailTemplateMaintainDTO.getId());
			String sendObject = "";
			if (!StringUtils.isNullOrEmpty(goodwillMailTemplateMaintainDTO.getSendObjects())) {
				if (goodwillMailTemplateMaintainDTO.getSendObjects().length > 0) {
					StringBuffer buf = new StringBuffer();
					for (int i = 0; i < goodwillMailTemplateMaintainDTO.getSendObjects().length; i++) {
						buf.append("" + goodwillMailTemplateMaintainDTO.getSendObjects()[i] + ",");
					}
					sendObject = buf.toString().substring(0, buf.toString().lastIndexOf(","));
					goodwillMailTemplateMaintainDTO.setSendObject(sendObject);
				}
			}
			// 对对象进行赋值操作
			goodwillMailTemplateMaintainDTO.transDtoToPo(goodwillMailTemplateMaintainPo);
			// goodwillMailTemplateMaintainPo.setSendObject(sendObject);
			// 执行更新
			int row = goodwillMailTemplateMaintainMapper.updateById(goodwillMailTemplateMaintainPo);

		}
		return 1;
	}
}
