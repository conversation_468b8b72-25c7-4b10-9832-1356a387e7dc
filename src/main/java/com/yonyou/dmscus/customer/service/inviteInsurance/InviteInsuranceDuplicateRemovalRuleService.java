package com.yonyou.dmscus.customer.service.inviteInsurance;

import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceDuplicateRemovalRuleDTO;

import java.util.List;

/**
 * <p>
 * 续保去重规则表，本章表所存储的数据，应该都是一个默认值。 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
public interface InviteInsuranceDuplicateRemovalRuleService {

    List<InviteInsuranceDuplicateRemovalRuleDTO> selectListBySql(InviteInsuranceDuplicateRemovalRuleDTO
                                                                         inviteInsuranceDuplicateRemovalRuleDTO);


}
