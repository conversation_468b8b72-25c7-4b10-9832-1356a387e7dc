package com.yonyou.dmscus.customer.service.common.businessPlatform;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.dto.CustomerInfoCenterDTO;
import com.yonyou.dmscus.customer.dto.CustomerInfoListReturnDTO;
import com.yonyou.dmscus.customer.dto.ResponseDto;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.*;
import com.yonyou.dmscus.customer.service.common.businessPlatform.dto.VehicleOwnerDetailByIdListDTO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.dto.VehicleOwnerSelectByIdListDTO;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 车辆跟进人员对应关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
@Service
public class BusinessPlatformServiceImpl implements BusinessPlatformService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RestTemplate restTemplate;

    @Resource
    private RestTemplate directRestTemplate;

    @Autowired
    private MidUrlProperties midUrlProperties;

    @Value("${access.volvo.dmscloudService.Url}")
    private String cloudUrl;

    /**
     * 中台大小区code 查询经销商
     * @param areaId
     * @param largeAreaId
     */
    @Override
    public List<String> getDealercodes(String areaId,String largeAreaId,String dealerCode,String dealerName) {
        List<String> codes= new ArrayList<String>();
        List<CompanyDetailDTO> rs = this.getDealer(areaId,largeAreaId,dealerCode,dealerName);
        for (CompanyDetailDTO detailDTO:rs) {
            codes.add(detailDTO.getCompanyCode());
        }
        return codes;
    }

    /**
     * 中台大小区code 查询经销商
     * @param areaId
     * @param largeAreaId
     */
    @Override
    public List<CompanyDetailDTO> getDealer(String areaId,String largeAreaId,String dealerCode,String dealerName){
        List<CompanyDetailDTO> rs = new ArrayList<CompanyDetailDTO>();
        String successCode = "0";
        String requestUrl = midUrlProperties.getMidEndOrgCenter()+midUrlProperties.getSelectCompanyInfo();
        CompanySelectDTO dto = new CompanySelectDTO();
        if(!StringUtils.isNullOrEmpty(largeAreaId)){
            dto.setAfterBigArea(largeAreaId);
        }
        if(!StringUtils.isNullOrEmpty(areaId)){
            dto.setAfterSmallArea(areaId);
        }
        if(!StringUtils.isNullOrEmpty(dealerCode)){
            dto.setCompanyCode(dealerCode);
        }
        if(!StringUtils.isNullOrEmpty(dealerName)){
            dto.setCompanyNameCn(dealerName);
        }
        dto.setCompanyType("15061003");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<CompanySelectDTO> httpEntity = new HttpEntity<CompanySelectDTO>(dto, httpHeaders);
        ResponseEntity<ResponseDTO> responseEntity =directRestTemplate.exchange(requestUrl, HttpMethod.POST,httpEntity,
                ResponseDTO.class);
        if(responseEntity!=null&&responseEntity.getBody()!=null) {
            if(successCode.equals(responseEntity.getBody().getReturnCode())) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                if(responseEntity.getBody().getData()!=null){
                    rs= objectMapper.convertValue(responseEntity.getBody().getData(), new
                            TypeReference<List<CompanyDetailDTO>>(){});
                }
            }else{
                throw new DALException("经销商查询接口异常，请稍后再试");
            }
        }
        return rs;
    }



    /**
     * 中台车辆信息查询
     * @param vin
     * @return
     */
    @Override
    public TmVehicleDTO getVehicleByVIN(String vin){
        String successCode = "0";
        String requestUrl = midUrlProperties.getMidEndVehicleCenter()+midUrlProperties.getVehicleVin()+vin;
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity httpEntity = new HttpEntity<>(null, httpHeaders);
        ResponseEntity<ResponseDTO> responseEntity =directRestTemplate.exchange(requestUrl, HttpMethod.GET,httpEntity,
                ResponseDTO.class);
        logger.info("mid-end-vehicle-center/vehicle/vin/ resp is:{}", JSON.toJSONString(responseEntity.getBody()));
        if(responseEntity!=null&&responseEntity.getBody()!=null) {
            if(successCode.equals(responseEntity.getBody().getReturnCode())) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                if(responseEntity.getBody().getData()!=null){
                    TmVehicleDTO dto= objectMapper.convertValue(responseEntity.getBody().getData(), TmVehicleDTO
                            .class) ;
                    return dto;
                }

            }
        }
        return null;
    }


    /**
     * 查询车辆 中台接口(未实现)
     * @param dto
     * @return
     */
    @Override
    public IPage<OwnerVehicleVO> getVehicle(PageRequestDTO<OwnerVehicleDTO> dto){
        Page<OwnerVehicleVO> page =  null;
        String successCode = "0";
        String requestUrl = midUrlProperties.getMidEndVehicleCenter() + midUrlProperties.getListOwnerVehiclePage();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity httpEntity = new HttpEntity<>(dto, httpHeaders);
        ResponseEntity<ResponseDTO> responseEntity =directRestTemplate.exchange(requestUrl, HttpMethod.POST,httpEntity,
                ResponseDTO.class);
        if(responseEntity!=null&&responseEntity.getBody()!=null) {
            if(successCode.equals(responseEntity.getBody().getReturnCode())) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                if(responseEntity.getBody().getData()!=null){
                    page= objectMapper.convertValue(responseEntity.getBody().getData
                            (),Page.class) ;
                    List<OwnerVehicleVO> list = objectMapper.convertValue(page.getRecords(),new
                            TypeReference<List<OwnerVehicleVO>>(){}) ;
                    page.setRecords(list);
                    return page;
                }
            }
        }
        return page;
    }


    /**
     * 批量获取oneid
     * @param list
     * @return
     */
    @Override
    public List<CustomerInfoListReturnDTO> getOneId(List<CustomerInfoCenterDTO> list){
        List<CustomerInfoListReturnDTO> rs = new ArrayList<>();
        String successCode = "0";
        String requestUrl = midUrlProperties.getMidEndCustomerCenter()+midUrlProperties.getSaveList();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        RequestDTO dto = new  RequestDTO();
        dto.setData(list);
        HttpEntity<RequestDTO> httpEntity = new HttpEntity<>(dto, httpHeaders);
        ResponseEntity<ResponseDTO> responseEntity =directRestTemplate.exchange(requestUrl, HttpMethod.PUT,httpEntity,
                ResponseDTO.class);
        if(responseEntity!=null&&responseEntity.getBody()!=null) {
            if(successCode.equals(responseEntity.getBody().getReturnCode())) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                if(responseEntity.getBody().getData()!=null){
                    rs= objectMapper.convertValue(responseEntity.getBody().getData(), new
                            TypeReference<List<CustomerInfoListReturnDTO>>(){});
                }
            }else{
                throw new DALException("中台批量获取oneId异常，请稍后再试");
            }
        }
        return rs;
    }

    /**
     * 校验经销商是否在经销商列表中存在
     * @param dealerCodes
     */
    @Override
    public IsExistByCodeDTO checkDealerCodeExist(List<String> dealerCodes) {
        String codes = StringUtils.listToString(dealerCodes, ',');
        String successCode = "0";
        String requestUrl = midUrlProperties.getMidEndOrgCenter()+ midUrlProperties.getIsExistByCode();
        IsExistByCodeDTO dto = new IsExistByCodeDTO();
        dto.setCompanyCode(codes);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<IsExistByCodeDTO> httpEntity = new HttpEntity<>(dto, httpHeaders);
        ResponseEntity<ResponseDTO> responseEntity =directRestTemplate.exchange(requestUrl,HttpMethod.POST,httpEntity,
                ResponseDTO.class);
        if(responseEntity!=null&&responseEntity.getBody()!=null) {
            if(successCode.equals(responseEntity.getBody().getReturnCode())) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                if(responseEntity.getBody().getData()!=null){
                    dto= objectMapper.convertValue(responseEntity.getBody().getData(), IsExistByCodeDTO
                            .class);
                    return dto;
                }
            }
        }
        throw new DALException("经销商代码校验接口异常，请稍后再试");
    }

    /**
     * 中台接口检查vin 是否存在
     */
    @Override
    public CheckTmVehicleDTO checkVin(List<String> vins) {
        String successCode = "0";
        String requestUrl = midUrlProperties.getMidEndVehicleCenter() + midUrlProperties.getVinList();
        RequestDTO<CheckTmVehicleDTO> dto = new RequestDTO<CheckTmVehicleDTO>();
        CheckTmVehicleDTO check = new CheckTmVehicleDTO();

        check.setVinList(vins);
        dto.setData(check);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<RequestDTO> httpEntity = new HttpEntity<RequestDTO>(dto, httpHeaders);
        ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrl, HttpMethod.POST, httpEntity,
                ResponseDTO.class);
        if (responseEntity!=null&&responseEntity.getBody() != null) {
            if (successCode.equals(responseEntity.getBody().getReturnCode())) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                if(responseEntity.getBody().getData()!=null){
                    check = objectMapper.convertValue(responseEntity.getBody().getData(), CheckTmVehicleDTO
                            .class);
                    return check;
                }
            }
        }
        throw new DALException("车架号校验接口异常，请稍后再试");
    }

    @Override
    public VehicleDTO listOwnerVehiclePage(String vin) {
        String successCode = "0";
        String requestUrl = midUrlProperties.getMidEndVehicleCenter() + midUrlProperties.getListOwnerVehiclePage();
        PageRequestDTO<VehicleDTO> dto = new PageRequestDTO<VehicleDTO>();
        dto.setPageSize(1L);
        dto.setPage(1L);
        VehicleDTO veh = new VehicleDTO();
        veh.setVin(vin);
        dto.setData(veh);
        JSONObject json =directRestTemplate.postForEntity(requestUrl,dto, JSONObject.class).getBody();
        String returnCode = json.getString("returnCode");
        if(!returnCode.equals("0")){
            throw new DALException("获取车主车辆接口异常，请稍后再试!");
        }
        JSONObject data = json.getJSONObject("data");
        PageResponseDTO<List<VehicleDTO>>  records = JSONObject.parseObject(data.toJSONString(),new com.alibaba.fastjson
                .TypeReference<PageResponseDTO<List<VehicleDTO>>>(VehicleDTO.class) {
        });
        if(records.getRecords()==null||records.getRecords().size()==0){
            throw new DALException("车辆信息不存在，请联系管理员!");
        }
        return records.getRecords().get(0);
    }


    @Override
    public List<InviteVulDTO> getVulInfo(String vin) {
        String requestUrl = cloudUrl+"LabourGroupApi/queryNewAndOldDataSynDcs";

        InviteVulDTO dto = new InviteVulDTO();
        dto.setVin(vin);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<InviteVulDTO> httpEntity = new HttpEntity<>(dto, httpHeaders);
        ResponseEntity<ResponseDto> responseEntity =restTemplate.exchange(requestUrl,HttpMethod.POST,httpEntity,
                ResponseDto.class);
        if(responseEntity.getBody()!=null) {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.convertValue(responseEntity.getBody().getData(),
                    new TypeReference<List<InviteVulDTO>>(){});

        }else{
            throw new DALException("获取原产服务提醒异常，请稍后再试");
        }
    }

    @Override
    public UserInfoNewDto getRoleList() {
        UserInfoNewDto rs = new UserInfoNewDto();
        String successCode = "0";
        String requestUrl = midUrlProperties.getMidEndAuthCenter()+midUrlProperties.getUserInfoNew();
        ResponseEntity<ResponseDTO> responseEntity =directRestTemplate.exchange(requestUrl, HttpMethod.GET,null,
                ResponseDTO.class);
        logger.info("url:{}",requestUrl);
        logger.info("responseEntity:{}",JSONObject.toJSON(responseEntity));
        if(responseEntity!=null&&responseEntity.getBody()!=null) {
            logger.info("responseEntity:{}",JSONObject.toJSON(responseEntity.getBody()));
            if(successCode.equals(responseEntity.getBody().getReturnCode())) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                if(responseEntity.getBody().getData()!=null){
                    rs= objectMapper.convertValue(responseEntity.getBody().getData(), new
                            TypeReference<UserInfoNewDto>(){});
                }
            }else{
                throw new DALException("查询角色接口异常，请稍后再试");
            }
        }
        return rs;
    }


}
