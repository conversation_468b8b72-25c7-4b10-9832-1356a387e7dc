package com.yonyou.dmscus.customer.service.goodwill;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillInvoiceRecordDTO;

/**
 * <p>
 * 亲善管理录入发票信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-19
 */
public interface GoodwillInvoiceRecordService {
	public IPage<GoodwillInvoiceRecordDTO> selectPageBysql(Page page,
			GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO);

	public List<GoodwillInvoiceRecordDTO> selectListBySql(GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO);

	public GoodwillInvoiceRecordDTO getById(Long id);

	public int insert(GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO);

	public int update(Long id, GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO);

	public int deleteById(Long id);

	public int deleteBatchIds(String ids);

}
