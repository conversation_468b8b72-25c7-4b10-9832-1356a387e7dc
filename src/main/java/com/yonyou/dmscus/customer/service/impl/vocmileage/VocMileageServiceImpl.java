
/** 
*Copyright 2020 Yonyou Corporation Ltd. All Rights Reserved.
* This software is published under the terms of the Yonyou Software
* License version 1.0, a copy of which has been included with this
* distribution in the LICENSE.txt file.
*
* @Project Name : dmscus.customer
*
* @File name : VocMileageServiceImpl.java
*
* <AUTHOR> caizhonming
*
* @Date : 2020年7月8日
*
*/
	
package com.yonyou.dmscus.customer.service.impl.vocmileage;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.common.AjaxResponse;
import com.yonyou.dmscus.customer.common.VocMileageCheckVO;
import com.yonyou.dmscus.customer.dao.vocmileage.VocMileageMapper;
import com.yonyou.dmscus.customer.entity.dto.vocmileage.VocMileageDTO;
import com.yonyou.dmscus.customer.entity.po.vocmileage.VocMileagePO;
import com.yonyou.dmscus.customer.enums.VocMileageAddEnum;
import com.yonyou.dmscus.customer.service.vocmileage.VocMileageService;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import com.yonyou.dmscus.customer.util.common.ExcelDataUtil;


/**
* VOC里程实现类
* <AUTHOR>
* @date 2020年7月8日
*/
@Service
public class VocMileageServiceImpl implements VocMileageService {
    
    /**日志对象*/
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    /**DAO的Mapper服务注入*/
    @Resource
    private VocMileageMapper  vocMileageMapper;


    /**
     * 分页查询VOC里程
    * @date 2020年7月8日
    * @param page
    * @param queryVocMileageDTO
    * @return
    * @throws ServiceBizException
    * (non-Javadoc)
    * @see com.yonyou.dmscus.customer.service.vocmileage.VocMileageService#queryVocMileagePageInfo(com.baomidou.mybatisplus.extension.plugins.pagination.Page, com.yonyou.dmscus.customer.entity.dto.vocmileage.VocMileageDTO)
    */
    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Override
    public IPage<VocMileageDTO> queryVocMileagePageInfo(Page page,
                                                        VocMileageDTO queryVocMileageDTO) throws ServiceBizException {
        if(queryVocMileageDTO == null) {
            queryVocMileageDTO = new VocMileageDTO();
        }
        //处理日期增加时分秒
        this.dealVocMileageDTO(queryVocMileageDTO);
        // dto转map
        Map<String, Object> queryParams = queryVocMileageDTO.toMaps();
        List<VocMileageDTO> resList = vocMileageMapper.queryVocMileagePageInfo(page, queryParams);
        // 准备返回数据
        if (CommonUtils.isNullOrEmpty(resList)) {
            page.setRecords(Collections.emptyList());
        } else {
            page.setRecords(resList);
        }
        return page;
    }

    
    /**
    * 查询日期时分秒处理
    * <AUTHOR>
    * @date 2020年7月8日
    * @param queryVocMileageDTO
    */
    	
    private void dealVocMileageDTO(VocMileageDTO queryVocMileageDTO) {
        //获取里程截止时间
        if(StringUtils.isNotBlank(queryVocMileageDTO.getGetTimeEnd())) {
            queryVocMileageDTO.setGetTimeEnd(queryVocMileageDTO.getGetTimeEnd()+" 23:59:59");
        }
        //导入截止时间
        if(StringUtils.isNotBlank(queryVocMileageDTO.getCreatedAtEnd())) {
            queryVocMileageDTO.setCreatedAtEnd(queryVocMileageDTO.getCreatedAtEnd()+" 23:59:59");
        }
    }

    /**
    * @date 2020年7月8日
    * @param queryVocMileageDTO
    * @return VOC里程列表
    * @throws ServiceBizException
    * (non-Javadoc)
    * @see com.yonyou.dmscus.customer.service.vocmileage.VocMileageService#findVocMileageByParams(com.yonyou.dmscus.customer.entity.dto.vocmileage.VocMileageDTO)
    */

    @Override
    public List<VocMileageDTO> findVocMileageByParams(VocMileageDTO queryVocMileageDTO) throws ServiceBizException {
        if(queryVocMileageDTO == null) {
            queryVocMileageDTO = new VocMileageDTO();
        }
        //处理日期增加时分秒
        this.dealVocMileageDTO(queryVocMileageDTO);
        // dto转map
        Map<String, Object> queryParams = queryVocMileageDTO.toMaps();
        List<VocMileageDTO> resList = vocMileageMapper.selectListBySql(queryParams);
        // 准备返回数据
        return resList;
    }


    
    /**
    * <AUTHOR>
    * @date 2020年7月8日
    * @param operationId 操作人ID
    * @param operationName 操作人姓名
    * @param importDataList
    * @return
    * (non-Javadoc)
    * @see com.yonyou.dmscus.customer.service.vocmileage.VocMileageService#importExcelData(java.lang.Long, java.lang.String, java.util.List)
    */
    	
    @Override
    public AjaxResponse importExcelData(Long operationId, String operationName,
                                        List<Map<Integer, String>> importDataList) {
        AjaxResponse ajaxResponse = new AjaxResponse();
        //要保存的对象
        List<VocMileagePO> insertList = Lists.newArrayList();
        //验证对象
        VocMileageCheckVO vocMileageCheckVO = new VocMileageCheckVO();
        //准备验证数据
        this.prepareVocMileageCheckData(vocMileageCheckVO,importDataList);
        //验证导入导入基本数据
        this.checkVocMileageImportData(vocMileageCheckVO, importDataList,
                                       insertList, ajaxResponse);
        logger.info("=3=vocMileageDataImport-importExcelData excel数据放入对象===");
        //存在错误的信息返回
        if(ajaxResponse.isFail()){
            ajaxResponse.addObject("errList", vocMileageCheckVO.getImportErrorList());
            return ajaxResponse;
        }
        //插入数据库成功数量(安全)
        List<Integer> successCntList = new CopyOnWriteArrayList<Integer>();
        //线程插入数据
        this.addThreadVocMileageData(insertList, successCntList);
        logger.info("=5=vocMileageDataImport 线程结束===");
        //成功总数
        long sum = successCntList.stream().reduce(Integer::sum).orElse(0);
        ajaxResponse.setSuccess("200", "操作成功");
        ajaxResponse.addObject("successCnt", sum);
        return ajaxResponse;
    }
    
    
    /**
    * 多线程处理数据导入
    * <AUTHOR>
    * @date 2020年7月10日
    * @param insertList
    * @param successCntList
    */
    	
    private void addThreadVocMileageData(final List<VocMileagePO> insertList,final List<Integer> successCntList) {
        //每次批量保存数据量(经过测试越小越快)
        int count = 50;
        int listSize = insertList.size();
        int runSize = (listSize / count)+1;
        // 线程数量
        int maxThreadSize = 10;
        if(runSize < maxThreadSize) {
            maxThreadSize = runSize;
        }
        ThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(maxThreadSize);
        CountDownLatch countDownLatch = new CountDownLatch(runSize);
        List<VocMileagePO> newList = null;
        for(int i = 0; i < runSize; i++) {
            if((i+1)==runSize){
                int startIndex = (i*count);
                int endIndex = insertList.size();
                newList =insertList.subList(startIndex,endIndex);
            }else{
                int startIndex = i*count;
                int endIndex = (i+1)*count;
                newList =insertList.subList(startIndex,endIndex);
            }
            VocMileageThread vocMileageThread = new VocMileageThread(newList,countDownLatch,successCntList);
            executor.execute(vocMileageThread);
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        executor.shutdown();
    }
    
    
    /**
    * 多线程拆分插入数据库
    * <AUTHOR>
    * @date 2020年7月10日
    */
    	
    class VocMileageThread implements Runnable{
        private List<VocMileagePO> subList;
        private CountDownLatch countDownLatch;
        private List<Integer> successCntList;
        public VocMileageThread(List<VocMileagePO> subList,CountDownLatch countDownLatch,List<Integer> successCntList) {
            super();
            this.subList = subList;
            this.countDownLatch = countDownLatch;
            this.successCntList = successCntList;
        }
        @Override
        public void run() {
            try {
                int cnt = batchAddData(subList);
                if (successCntList != null) {
                    //插入成功数量加入集合
                    successCntList.add(cnt);
                }
            } catch (Exception e) {
                logger.error(">>>多线程查询数据异常:{}", e);
            }finally{
                if(countDownLatch != null) {
                    countDownLatch.countDown();
                }
            }
        }
    }
    
    
    /**
           * 批量插入语句
    * <AUTHOR>
    * @date 2020年7月10日
    * @param insertList
    */	
    private int batchAddData(List<VocMileagePO> insertList) {
        if(CollectionUtils.isNotEmpty(insertList)) {
           logger.info("=4=vocMileageDataImport-insertBatchData批量插入数据库===");
           return  vocMileageMapper.insertBatchData(insertList);
        }else {
           return 0;
        }
    }

    /**
           * 导入EXCEL数据的基本校验
    * <AUTHOR>
    * @date 2020年7月8日
    * @param checkDataVO
    * @param importDataList
    * @param insertList
    * @param ajaxResponse
    */
    	
    private void checkVocMileageImportData(VocMileageCheckVO checkDataVO,
                                           List<Map<Integer, String>> importDataList,
                                           List<VocMileagePO> insertList, AjaxResponse ajaxResponse) {
        VocMileagePO vocMileagePO = null;
        // 获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        Date nowTime = new Date();
        //第一行标题，从excel数据第二行开始
        int rowId = 2;
        for(Map<Integer, String> rowData : importDataList) {
            try {
                //设置某行数据
                checkDataVO.setRowData(rowData);
                //设置第几行数字
                checkDataVO.setRowId(rowId);
                vocMileagePO = new VocMileagePO();
                //check检查【VIN】
                this.checkVin(checkDataVO,vocMileagePO);
                //check检查【里程(米)】
                this.checkmileageM(checkDataVO,vocMileagePO);
                //check检查【获取里程时间】
                this.checkGetTime(checkDataVO,vocMileagePO);
                
                if(checkDataVO.getErrorList().size() > 100) {
                    break;
                }
                //设置隐藏对象值
                this.setVocMileageInfo(vocMileagePO,loginInfoDto,nowTime);
                //下一行
                rowId++;
                //加入集合
                insertList.add(vocMileagePO);
            } catch (Exception e) {
                logger.error("VocMileageServiceImpl.checkVocMileageImportData("+JSON.toJSONString(rowData)+") VOC里程EXCEL导入记录校验出错:{}",e);
                if (e != null) {
                    checkDataVO.addError("VOC里程EXCEL导入校验出错：" + e.getMessage());
                }
            }
        }
        //获取错误的验证信息
        List<String> errorMsgList = checkDataVO.getErrorList();
        if(CollectionUtils.isNotEmpty(errorMsgList)){
            ajaxResponse.setResult(AjaxResponse.FAILD);
        }
    }

    /**
    * 设置VOC里程对象隐藏值
    * <AUTHOR>
    * @date 2020年7月8日
    * @param vocMileagePO
    * @param loginInfoDto
    */
    	
    private void setVocMileageInfo(VocMileagePO vocMileagePO, LoginInfoDto loginInfoDto,Date nowTime) {
        if(loginInfoDto != null) {
            vocMileagePO.setAppId(loginInfoDto.getAppId());
            vocMileagePO.setOwnerCode(loginInfoDto.getOwnerCode());
            vocMileagePO.setOwnerParCode(loginInfoDto.getOwnerParCode());
            if (loginInfoDto.getOrgId() != null) {
                vocMileagePO.setOrgId(loginInfoDto.getOrgId().intValue());
            }
            //数据来源 (1：界面新增   2：Excel导入)
            vocMileagePO.setDataSources(2);
            //导入账号
            vocMileagePO.setCreatedCode(loginInfoDto.getUserCode());
            //导入时间
            vocMileagePO.setCreatedAt(nowTime);
            //导入人
            vocMileagePO.setCreatedBy(loginInfoDto.getUserId()+"-"+loginInfoDto.getUserName());
            vocMileagePO.setUpdatedBy(vocMileagePO.getCreatedBy());
            vocMileagePO.setUpdatedAt(nowTime);
        }
    }


    /**
    * check检查【获取里程时间】
    * <AUTHOR>
    * @date 2020年7月8日
    * @param checkDataVO
    * @param vocMileagePO
    */
    	
    private void checkGetTime(VocMileageCheckVO checkDataVO, VocMileagePO vocMileagePO) {
        Date getTime =  null;
        //获取列值
        String columnVal = checkDataVO.getColumnValue(VocMileageAddEnum.GET_TIME.code());
        if(StringUtils.isNotBlank(columnVal) && columnVal.contains(":")) {//带时分秒的时间
            getTime = ExcelDataUtil.checkAndGetpatternDate(checkDataVO, VocMileageAddEnum.GET_TIME, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS);
        }else {
            getTime = ExcelDataUtil.checkAndGetpatternDate(checkDataVO, VocMileageAddEnum.GET_TIME, DateUtils.PATTERN_YYYY_MM_DD);
        }
        vocMileagePO.setGetTime(getTime);
    }


    /**
    * check检查【里程(米)】
    * <AUTHOR>
    * @date 2020年7月8日
    * @param checkDataVO
    * @param vocMileagePO
    */
    	
    private void checkmileageM(VocMileageCheckVO checkDataVO, VocMileagePO vocMileagePO) {
        BigDecimal mileageM =  ExcelDataUtil.checkAndGetPrice(checkDataVO, VocMileageAddEnum.MILEAGE_M);
        if(mileageM != null) {
            //设置里程(米)
            vocMileagePO.setMileageM(mileageM.intValue());
            //转换公里
            Double mileageKm = Math.round(vocMileagePO.getMileageM()/100d)/10d;
            //四舍五入取整
            BigDecimal bd=new BigDecimal(mileageKm).setScale(0, BigDecimal.ROUND_HALF_UP);
            if (bd != null) {
                //设置里程(公里)
                vocMileagePO.setMileageKm(Integer.valueOf(bd.toString()));
            }
        }
    }


    /**
    * 校验VIN码
    * <AUTHOR>
    * @date 2020年7月8日
    * @param checkDataVO
    * @param vocMileagePO
    */
    	
    private void checkVin(VocMileageCheckVO checkDataVO, VocMileagePO vocMileagePO) {
        String vin = ExcelDataUtil.checkAndGetValue(checkDataVO, VocMileageAddEnum.VIN);
        vocMileagePO.setVin(vin);
    }


    /**
    * 准备EXCEL导入前的验证数据
    * <AUTHOR>
    * @date 2020年7月8日
    * @param vocMileageCheckVO
    * @param importDataList
    */
    	
    private void prepareVocMileageCheckData(VocMileageCheckVO vocMileageCheckVO,
                                            List<Map<Integer, String>> importDataList) {
        
    }
    
    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     */
    @Override
    public int deleteBatchIds(String ids){
        List<Long>longList = com.yonyou.dmscus.customer.util.common.StringUtils.convertStrToArray(ids,",", Long.class);
        int deleteCount= vocMileageMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}",deleteCount);
        if(deleteCount!=longList.size()){
            throw new DALException("删除失败,没有符合条件的数据");
        }else{
            return deleteCount;
        }
    }


    
    /**
    * @导入
    * @date 2020年7月16日
    * @param importDataList
    * @return
    * (non-Javadoc)
    * @see com.yonyou.dmscus.customer.service.vocmileage.VocMileageService#importExcelData2(java.util.List)
    */
    	
    @Override
    public AjaxResponse importExcelData2(List<String[]> importDataList) {
        AjaxResponse ajaxResponse = new AjaxResponse();
        //要保存的对象
        List<VocMileagePO> insertList = Lists.newArrayList();
        this.prepareNewVocMileagePOData(insertList,importDataList);
        //插入数据库成功数量(安全)
        List<Integer> successCntList = new CopyOnWriteArrayList<Integer>();
        //线程插入数据
        this.addThreadVocMileageData(insertList, successCntList);
        logger.info("=5=importExcelData2 线程结束===");
        //成功总数
        long sum = successCntList.stream().reduce(Integer::sum).orElse(0);
        ajaxResponse.setSuccess("200", "操作成功");
        ajaxResponse.addObject("successCnt", sum);
        return ajaxResponse;
    }


    private void prepareNewVocMileagePOData(List<VocMileagePO> insertList, List<String[]> importDataList) {
        VocMileagePO vocMileagePO = null;
        // 获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        Date nowTime = new Date();
        for(String[] arry:importDataList) {
            try {
                if(arry != null && arry.length == 3) {
                    vocMileagePO = new VocMileagePO();
                    vocMileagePO.setVin(arry[0]);
                    if (arry[1] != null) {
                        vocMileagePO.setMileageM(Integer.parseInt(arry[1]));
                        //转换公里
                        Double mileageKm = Math.round(vocMileagePO.getMileageM()/100d)/10d;
                        //四舍五入取整
                        BigDecimal bd=new BigDecimal(mileageKm).setScale(0, BigDecimal.ROUND_HALF_UP);
                        if (bd != null) {
                            //设置里程(公里)
                            vocMileagePO.setMileageKm(Integer.valueOf(bd.toString()));
                        }
                    }
                    if (arry[2] != null) {
                        vocMileagePO.setGetTime(DateUtils.parseDateStrToDate(arry[2], DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
                    } 
                    //设置隐藏对象值
                    this.setVocMileageInfo(vocMileagePO,loginInfoDto,nowTime);
                    //加入集合
                    insertList.add(vocMileagePO);
                }
            } catch (Exception e) {
                logger.error("VocMileageServiceImpl.checkVocMileageImportData("+JSON.toJSONString(arry)+")转换对象数据出错:{}",e);
            }
        }
    }
}
