package com.yonyou.dmscus.customer.service.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMaterialUploadGuideDTO;


/**
 * <p>
 * 亲善材料上传指南 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
public interface GoodwillMaterialUploadGuideService  {
	public IPage<GoodwillMaterialUploadGuideDTO>selectPageBysql(Page page,GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO);
	public GoodwillMaterialUploadGuideDTO selecMaterialtInfo(GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO);
	public GoodwillMaterialUploadGuideDTO getById(Long id);
	public int insert(GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO);
	public int update(Long id, GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO);
	public int deleteById(Long id);
	public int deleteBatchIds(String ids);
	int updateList(GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO);
   
}
