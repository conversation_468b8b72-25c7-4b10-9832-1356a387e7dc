package com.yonyou.dmscus.customer.service.talkskill;

import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillTagDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillTagPO;

import java.util.List;


/**
 * <p>
 * 话术标签 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-20
 */
public interface TalkskillTagService  {

    List<TalkskillTagPO> selectListBySql(TalkskillTagDTO talkskillTagDTO);
    TalkskillTagDTO getById(Long id);

    int insert(TalkskillTagDTO talkskillTagDTO);
    int updateList(List<TalkskillTagDTO> talkskillTagDTO);
    int update(Long id,TalkskillTagDTO talkskillTagDTO);

}
