package com.yonyou.dmscus.customer.service.impl.invitationautocreate;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.common.VehicleMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dao.invitationautocreate.DailyMileageLogMapper;
import com.yonyou.dmscus.customer.dao.invitationautocreate.InviteVehicleTaskMapper;
import com.yonyou.dmscus.customer.dao.vocmileage.VocMileageMapper;
import com.yonyou.dmscus.customer.dto.DailyAverageMileageVo;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.dto.VocMileageVO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.po.common.VehiclePO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.DailyMileageLogPO;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRulePO;
import com.yonyou.dmscus.customer.entity.po.maintaininfo.InviteMaintainInfoPO;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.feign.WhitelistQueryService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.invitationautocreate.InviteVehicleTaskService;
import com.yonyou.dmscus.customer.service.invitationautocreate.VocManageService;
import com.yonyou.dmscus.customer.service.inviteRule.InviteRuleService;
import com.yonyou.dmscus.customer.service.maintaininfo.InviteMaintainInfoService;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.dmscus.customer.util.common.Utility;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@RefreshScope
public class VocManageServiceImpl implements VocManageService {


    /**
     * 日志对象
     */
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private VocMileageMapper vocMileageMapper;
    @Resource
    VehicleMapper vehicleMapper;
    @Resource
    DailyMileageLogMapper dailyMileageLogMapper;
    @Resource
    InviteVehicleTaskMapper inviteVehicleTaskMapper;
    @Resource
    InviteVehicleRecordMapper inviteVehicleRecordMapper;
    @Autowired
    ReportCommonClient reportCommonClient;
    @Autowired
    InviteRuleService inviteRuleService;
    @Autowired
    InviteVehicleRecordService inviteVehicleRecordService;
    @Autowired
    InviteVehicleTaskService inviteVehicleTaskService;

    @Autowired
    private InviteMaintainInfoService inviteMaintainInfoService;

    @Autowired
    private WhitelistQueryService whitelistQueryService;

    @Value("${maintain.mileage:-1000}")
    private double mileageMax;

    @Value("${maintain.month:-1}")
    private int monthMax;

    /**VOC job开关*/
    @Value("${voc.job.switch:1}")
    private int vocSwitch;
    /**
     * 检查是否执行日
     *
     * @param createDate
     * @return
     */
    private boolean checkExecuteDate(String createDate, String da) {
        try {
            Date date = Utility.parseString2Date(createDate, "yyyy-MM-dd");
            DateFormat f = new SimpleDateFormat("dd");
            return da.equals(f.format(date));
        } catch (Exception e) {

        }
        return false;
    }

    @Override
    public int partitionGetAllVocVeh(String createDate, Integer page, Integer number, Integer size){
        Integer returnCount = 0;
        Integer partitionSize = size == null ? 10000 : size;
        Integer start = page == null ? 0 : page;
        Integer nThreads = number == null ? 35 : number;
        long startTimeMillis = System.currentTimeMillis();
        List<VocMileageVO> list = null;
        Integer begIndex;
        Integer endIndex;

        logger.info("partitionGetAllVocVeh开始计算VOC日均行驶里程,createDate:{},startTimeMillis:{}", createDate, startTimeMillis);
        while (true){
            begIndex = start * partitionSize;
            endIndex = partitionSize;
            String sql = getSql(begIndex, endIndex);
            logger.info("partitionGetAllVocVeh, sql:{}", sql);
            logger.info("partitionGetAllVocVeh, createDate:{},begIndex:{},partitionSize:{}", createDate, begIndex, partitionSize);
            //查询开关
            logger.info("partitionGetAllVocVeh, start:{}", start);
             list = reportCommonClient.getAllVocVehWithMon(createDate,start * partitionSize,partitionSize);
            if(list == null || list.size() == 0){
                logger.info("partitionGetAllVocVeh,执行完成");
                break;
            }else {
                start++;
                logger.info("partitionGetAllVocVeh,开始执行,start:{},size:{}", start, list.size());
                //计算里程
                getAllVocVeh(start,list, createDate, nThreads,sql);
                returnCount += 1;
                logger.info("partitionGetAllVocVeh,returnCount:{}", returnCount);
            }
        }
        long endTimeMillis = System.currentTimeMillis();
        long execTime = endTimeMillis - startTimeMillis;
        logger.info("partitionGetAllVocVeh 当月VOC里程计算总分页数,returnCount:{},endTimeMillis:{},execTime:{}",returnCount, endTimeMillis, execTime);
        return returnCount;
    }

    public int partitionGetAllVocVehOld(String createDate, Integer page, Integer number, Integer size){
        Integer returnCount = 0;
        Integer partitionSize = size == null ? 10000 : size;
        Integer start = page == null ? 0 : page;
        Integer nThreads = number == null ? 20 : number;
        long startTimeMillis = System.currentTimeMillis();
        logger.info("开始计算VOC日均行驶里程,createDate:{},startTimeMillis:{}", createDate, startTimeMillis);
        List<VocMileageVO> list = null;
        while (true){
            Integer begIndex = start * partitionSize;
            logger.info("createDate:{},begIndex:{},partitionSize:{}", createDate, begIndex, partitionSize);
            //查询开关
            logger.info("partitionGetAllVocVeh, vocSwitch:{}, start:{}", vocSwitch, start);
            if(vocSwitch == 0){
                return returnCount;
            }
            list = reportCommonClient.getAllVocVehWithMon(createDate,start * partitionSize,partitionSize);
            if(list == null || list.size() == 0){
                logger.info("---执行完成---");
                break;
            }else {
                start++;
                logger.info("---开始执行---,start:{},size:{}", start, list.size());
                int i = getAllVocVehOld(start,list, createDate, nThreads);
                returnCount += i;
                logger.info("returnCount:{}", returnCount);
            }
        }
        long endTimeMillis = System.currentTimeMillis();
        long execTime = endTimeMillis - startTimeMillis;
        logger.info("当月VOC里程计算总分页数,returnCount:{},endTimeMillis:{},execTime:{}",returnCount, endTimeMillis, execTime);
        return returnCount;
    }

    private String getSql( Integer begIndex, Integer endIndex){
        StringBuffer sb = new StringBuffer("select DISTINCT a.vin ");
        sb.append("from dms_manage.tc_mid_vehicle_voc_log a ");
        sb.append("left join vehicle.tm_vehicle b on a.vin=b.vin order by a.vin ");
        sb.append("limit " + begIndex + "," + endIndex);
        return sb.toString();
    }

    /**
     * 多线程处理
     */
    public class VocMileageThread {
        private String vin;
        private Date invoiceDate;
        private String nextMonthDay;
        private List<String> errorVins;

        public VocMileageThread(String nextMonthDay, String vin, Date invoiceDate,
                                List<String> errorVins) {
            super();
            this.vin = vin;
            this.invoiceDate = invoiceDate;
            this.errorVins = errorVins;
            this.nextMonthDay = nextMonthDay;
        }

        public void start() {
            try {
                batchExecuteData(this.nextMonthDay, this.vin, this.invoiceDate);
            } catch (Exception e) {
                logger.error(">>执行数据异常:{}", e);
                dailyMileageLogMapper.SetErrorlog("vocDailyMileage", e.getMessage(), e.getStackTrace().toString(),
                        this.vin);
                e.printStackTrace();
                if (errorVins != null) {
                    //发生错误加入集合
                    errorVins.add(vin);
                }
            }
        }
    }

    /**
     * 计算日均里程
     *
     * @param vin
     * @return
     */
    public int batchExecuteData(String nextMonthDay, String vin, Date invoiceDate) {
        VehicleOwnerVO vehInfo = new VehicleOwnerVO();
        vehInfo.setVin(vin);
        vehInfo.setInvoiceDate(invoiceDate);
        //公共计算日均里程逻辑
        DailyAverageMileageVo dailyAverageMileageVo =  inviteVehicleTaskService.commonCountDailyAverageMileage(vin, invoiceDate);
        List<VocMileageVO> vocMileageVOS = dailyAverageMileageVo.getVocMileageVOS();
        BigDecimal dailyAverageMileage = dailyAverageMileageVo.getDailyAverageMileage();
        LambdaQueryWrapper<VehiclePO> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(VehiclePO::getVin, vin);
        VehiclePO rs = vehicleMapper.selectOne(queryWrapper);
        this.saveDailyMileageLog(vin, vehInfo.getInvoiceDate(), vocMileageVOS.size() >= 1 ? vocMileageVOS.get(0) : null,
                vocMileageVOS.size() >= 2 ? vocMileageVOS.get(1) : null,
                vocMileageVOS.size() >= 3 ? vocMileageVOS.get(2) : null,
                vocMileageVOS.size() >= 4 ? vocMileageVOS.get(3) : null, rs,
                dailyAverageMileage);
        if (dailyAverageMileage != null && dailyAverageMileage.compareTo(BigDecimal.ZERO) >= 0) {
            if (rs != null) {
                rs.setDailyAverageMileage(dailyAverageMileage);
                rs.setIsVoc(10041001);
                vehicleMapper.updateVocById(rs);
            } else {
                VehiclePO insert = new VehiclePO();
                insert.setVin(vin);
                insert.setDailyAverageMileage(dailyAverageMileage);
                insert.setIsVoc(10041001);
                vehicleMapper.insertVoc(insert);
            }
            //增加首保更新建议入场时间
            this.updateFirstMaintainTask(nextMonthDay, vehInfo, vin, dailyAverageMileage.doubleValue());
            this.updateMaintainTask(nextMonthDay, vehInfo, vin, dailyAverageMileage.doubleValue());
            //删除易损件更新
            //this.updateVulnerableTask(nextMonthDay, vehInfo, vin, dailyAverageMileage);
        }
        return 1;
    }

    /**
     * 计算voc日均里程
     *
     * @return
     */
    private int getAllVocVehOld(Integer page ,List<VocMileageVO> list, String createDate, Integer nThreads) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        int size = list.size();
        logger.info("getAllVocVeh需执行数量{}", size);
        logger.info("getAllVocVeh当前时间{}", new Date());
        String nextMonthDay = this.getNextMonthFristDay(createDate);
        logger.info("getAllVocVeh下月基准时间{}", nextMonthDay);
        List<String> errorList = new ArrayList<>();
        ExecutorService executorService = Executors.newFixedThreadPool(nThreads);
        final CountDownLatch latch = new CountDownLatch(size);
        for (int i = 0; i < list.size(); i++) {
            VocMileageVO po = list.get(i);
            int finalI = i;
            executorService.submit(() -> {
                try {
                    long startTime = new Date().getTime();
                    VocMileageThread vocMileageThread = new VocMileageThread(nextMonthDay, po.getVin(), po.getInvoiceDate(),
                            errorList);
                    vocMileageThread.start();
                    long endTime = new Date().getTime();
                    logger.info("getAllVocVeh,page,num,endTime-startTime:{},{},{}", page, finalI, endTime - startTime);
                } catch (Exception e) {
                    dailyMileageLogMapper.SetErrorlog("allVocVeh", e.getMessage(), e.getStackTrace()
                            .toString(), "voc计算日均里程异常");
                } finally {
                    latch.countDown();
                }
            });
        }
        try {
            latch.await();
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("getAllVocVeh:{}", e.getMessage());
        }
        if (null != executorService) {
            executorService.shutdown();
        }
        return 1;
    }

    /**
     * 计算voc日均里程
     *
     * @return
     */
    private void getAllVocVeh(Integer page ,List<VocMileageVO> list, String createDate, Integer nThreads, String sql) {
        int size = list.size();
        long sTime = new Date().getTime();
        logger.info("getAllVocVeh需执行数量{}", size);
        logger.info("getAllVocVeh当前时间{}", new Date());
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        String nextMonthDay = this.getNextMonthFristDay(createDate);
        logger.info("getAllVocVeh下月基准时间{}", nextMonthDay);
        List<VehiclePO> upList = Collections.synchronizedList(new ArrayList<>());
        List<VehiclePO> addList = Collections.synchronizedList(new ArrayList<>());
        List<VocMileageVO> lists = Collections.synchronizedList(new ArrayList<>());
        ExecutorService executorService = Executors.newFixedThreadPool(nThreads);
        final CountDownLatch latch = new CountDownLatch(size);
        for (int i = 0; i < list.size(); i++) {
            VocMileageVO po = list.get(i);
            int finalI = i;
            executorService.submit(() -> {
                try {
                    long startTime = new Date().getTime();
                    batchExecuteData(po, upList, addList, lists);
                    long endTime = new Date().getTime();
                    logger.info("getAllVocVeh,page,num,endTime-startTime:{},{},{}", page, finalI, endTime - startTime);
                } catch (Exception e) {
                    logger.info("getAllVocVeh, Exception:{}", e);
                    dailyMileageLogMapper.SetErrorlog("allVocVeh", e.getMessage(), e.getStackTrace()
                            .toString(), "voc计算日均里程异常");
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            latch.await();
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("getAllVocVeh:{}", e.getMessage());
        }
        if (null != executorService) {
            executorService.shutdown();
        }
        //批量添加
        int addNum = 0;
        logger.info("getAllVocVeh, addList:{}", addList.size());
        if (addList.size() > 0) {
            addNum = vehicleMapper.insertVocAll(addList);
        }
        //批量修改
        int upNum = 0;
        logger.info("getAllVocVeh, upList:{}", upList.size());
        if (upList.size() > 0) {
            upNum = vehicleMapper.updateVocByIdAll(upList);
        }
        logger.info("getAllVocVeh, addNum:{}, upNum:{}", addNum, upNum);
        logger.info("getAllVocVeh, lists:{}", lists.size());
        //计算进场时间
        if (lists.size() > 0) {
            try {
                updateFirstMaintainTask(nextMonthDay, lists, sql);
                updateMaintainTask(nextMonthDay, lists, sql);
            } catch (Exception e) {
                logger.info("getAllVocVeh, Exception e:{}", e);
                throw new ServiceBizException(e);
            }
        }
        long eTime = new Date().getTime();
        logger.info("getAllVocVeh,page,end,endTime-startTime:{},{},{}", page, eTime - sTime);
    }

    //this.updateFirstMaintainTask(nextMonthDay, vehInfo, vin, dailyAverageMileage.doubleValue());
    //this.updateMaintainTask(nextMonthDay, vehInfo, vin, dailyAverageMileage.doubleValue());



    /**
     * 计算voc日均里程
     *
     * @return
     */
    private int calculateVocMileage(Integer page ,List<VocMileageVO> list, String createDate, Integer nThreads) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        int size = list.size();
        logger.info("calculateVocMileage需执行数量{}", size);
        logger.info("calculateVocMileage当前时间{}", new Date());
        String nextMonthDay = this.getNextMonthFristDay(createDate);
        logger.info("calculateVocMileage下月基准时间{}", nextMonthDay);




        return 1;
    }

    @Override
    public int computeDaMiForVocWithDateRange(LocalDate startDate, LocalDate endDate) {


        List<VocMileageVO> list = reportCommonClient.getAllVocVeh();
        logger.info("需执行数量{}", list.size());
        logger.info("当前时间{}", new Date());
//        String nextMonthDay = this.getNextMonthFristDay(createDate);
//        logger.info("下月基准时间{}", nextMonthDay);
        ThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(5);
        CountDownLatch countDownLatch = new CountDownLatch(5);
        List<String> errorList = new ArrayList<>();
        for (VocMileageVO po : list) {
            VocMileageThreadWithDateRange vocMileageThread = new VocMileageThreadWithDateRange( startDate, endDate , po.getVin(), po.getInvoiceDate(),
                    countDownLatch,
                    errorList);
            executor.execute(vocMileageThread);
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        executor.shutdown();
        logger.info("执行失败vins:{}", errorList.toString());
        logger.info("完成时间{}", new Date());
        return 1;
    }

    private String getNextMonthFristDay(String createDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(sdf.parse(createDate));
            cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + 1, 1);
        } catch (Exception e) {

        }
        return sdf.format(cal.getTime());
    }


    /**
     * 更新voc当前里程
     *
     * @param createDate
     * @return
     */
    @Override
    public int updateVoc(String createDate) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        List<VocMileageVO> list = reportCommonClient.getVocMileage(createDate);
        // 线程数量
        int maxThreadSize = 5;
        int runsize = 5;
        if (list.size() < runsize) {
            runsize = list.size();
        }
        ThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(maxThreadSize);
        CountDownLatch countDownLatch = new CountDownLatch(runsize);
        List<String> errorList = new ArrayList<>();
        for (VocMileageVO po : list) {
            if (po == null){
                continue;
            }
            VocMileageUpdateThread vocMileageUpdateThread = new VocMileageUpdateThread(po, countDownLatch,
                    errorList);
            executor.execute(vocMileageUpdateThread);
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        executor.shutdown();
        return 1;
    }

    /**
     * 多线程处理
     */
    public class VocMileageUpdateThread implements Runnable {
        private VocMileageVO po;
        private CountDownLatch countDownLatch;
        private List<String> errorVins;

        public VocMileageUpdateThread(VocMileageVO po, CountDownLatch countDownLatch,
                                      List<String> errorVins) {
            super();
            this.po = po;
            this.countDownLatch = countDownLatch;
            this.errorVins = errorVins;
        }

        @Override
        public void run() {
            try {
                batchUpdateData(po);
            } catch (Exception e) {
                logger.error(">>>多线程处理数据异常:{}", e);
                dailyMileageLogMapper.SetErrorlog("updateVoc", e.getMessage(), e.getStackTrace().toString(), po
                        .getVin());
                if (errorVins != null) {
                    //发生错误加入集合
                    errorVins.add(po.getVin());

                }
            } finally {
                if (countDownLatch != null) {
                    countDownLatch.countDown();
                }
            }
        }
    }

    public class VocMileageThreadWithDateRange implements Runnable {
        private String vin;
        private CountDownLatch countDownLatch;
        private Date invoiceDate;
        private LocalDate startDate;
        private LocalDate endDate;
        private List<String> errorVins;

        public VocMileageThreadWithDateRange(LocalDate startDate,
                                             LocalDate endDate,
                                             String vin, Date invoiceDate,
                                             CountDownLatch countDownLatch,
                                             List<String> errorVins) {
            super();
            this.vin = vin;
            this.invoiceDate = invoiceDate;
            this.countDownLatch = countDownLatch;
            this.errorVins = errorVins;
            this.startDate = startDate;
            this.endDate = endDate;
        }

        @Override
        public void run() {
            try {
                batchExecuteDataWithDateRange( startDate, endDate, this.vin, this.invoiceDate);
            } catch (Exception e) {
                logger.error(">>>多线程处理数据异常:{}", e);
                dailyMileageLogMapper.SetErrorlog("vocDailyMileage", e.getMessage(), e.getStackTrace().toString(),
                        this.vin);
                e.printStackTrace();
                if (errorVins != null) {
                    //发生错误加入集合
                    errorVins.add(vin);
                }
            } finally {
                if (countDownLatch != null) {
                    countDownLatch.countDown();
                }
            }
        }
    }


    /**
     * 更新当前里程
     *
     * @param po
     * @return
     */
    public int batchUpdateData(VocMileageVO po) {
        LambdaQueryWrapper<VehiclePO> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(VehiclePO::getVin, po.getVin());
        VehiclePO rs = vehicleMapper.selectOne(queryWrapper);
        if (rs != null && po.getMileageKm()!=null && po.getGetTime()!=null) {
            Date time = rs.getVocTime();
            time = time == null ? po.getGetTime() : time;
            Integer vocMileage = rs.getVocMileage();
            vocMileage = vocMileage == null ? po.getMileageKm() : vocMileage;
            if (rs.getIsVoc() == 10041002 ||
                    (time.getTime() <= po.getGetTime().getTime() && vocMileage <= po
                            .getMileageKm())) {
                rs.setVocMileage(po.getMileageKm());
                rs.setVocTime(po.getGetTime());
                rs.setIsVoc(10041001);
                vehicleMapper.updateVocById(rs);
            }
        } else {
            VehiclePO insert = new VehiclePO();
            insert.setVin(po.getVin());
            insert.setVocMileage(po.getMileageKm());
            insert.setVocTime(po.getGetTime());
            insert.setIsVoc(10041001);
            vehicleMapper.insertVoc(insert);
        }
        return 1;
    }


    /**
     * 计算日均里程
     *
     * @param
     * @return
     */
    public int batchExecuteData(VocMileageVO po, List<VehiclePO> upList, List<VehiclePO> addList, List<VocMileageVO> lists) {
        String vin = po.getVin();
        Date invoiceDate = po.getInvoiceDate();
        //公共计算日均里程逻辑
        DailyAverageMileageVo dailyAverageMileageVo =  inviteVehicleTaskService.commonCountDailyAverageMileage(vin, invoiceDate);
        if(dailyAverageMileageVo == null){
            return 1;
        }
        List<VocMileageVO> vocMileageVOS = dailyAverageMileageVo.getVocMileageVOS();
        BigDecimal dailyAverageMileage = dailyAverageMileageVo.getDailyAverageMileage();
        LambdaQueryWrapper<VehiclePO> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(VehiclePO::getVin, vin);
        VehiclePO rs = vehicleMapper.selectOne(queryWrapper);
        VehicleOwnerVO vehInfo = new VehicleOwnerVO();
        vehInfo.setVin(vin);
        vehInfo.setInvoiceDate(invoiceDate);
        this.saveDailyMileageLog(vin, vehInfo.getInvoiceDate(), vocMileageVOS.size() >= 1 ? vocMileageVOS.get(0) : null,
                vocMileageVOS.size() >= 2 ? vocMileageVOS.get(1) : null,
                vocMileageVOS.size() >= 3 ? vocMileageVOS.get(2) : null,
                vocMileageVOS.size() >= 4 ? vocMileageVOS.get(3) : null, rs,
                dailyAverageMileage);
        if (dailyAverageMileage != null && dailyAverageMileage.compareTo(BigDecimal.ZERO) >= 0) {
            po.setDailyAverageMileage(dailyAverageMileage);
            lists.add(po);
            if (rs != null) {
                rs.setDailyAverageMileage(dailyAverageMileage);
                rs.setIsVoc(10041001);
                upList.add(rs);
            } else {
                VehiclePO insert = new VehiclePO();
                insert.setVin(vin);
                insert.setDailyAverageMileage(dailyAverageMileage);
                insert.setIsVoc(10041001);
                addList.add(insert);
            }
            //删除易损件更新
            //this.updateVulnerableTask(nextMonthDay, vehInfo, vin, dailyAverageMileage);
        }
        return 1;
    }

    public int batchExecuteDataWithDateRange(LocalDate startDate, LocalDate endDate, String vin, Date invoiceDate) {
        int times = 1;
        VocMileageVO one = reportCommonClient.getVocMileageLast(vin);
        VehicleOwnerVO vehInfo = new VehicleOwnerVO();
        vehInfo.setVin(vin);
        vehInfo.setInvoiceDate(invoiceDate);
        if (vehInfo == null) {
            return 1;
        }
        VocMileageVO two = null;
        VocMileageVO three = null;
        VocMileageVO four = null;
        Double dailyAverageMileage = 0.0;
        two = reportCommonClient.getVocMileageByInterval(vin, one.getGetTime(), one.getMileageKm());
        if (two != null) {
            times++;
            three = reportCommonClient.getVocMileageByInterval(vin, two.getGetTime(), two.getMileageKm());
            if (three != null) {
                times++;
                four = reportCommonClient.getVocMileageByInterval(vin, three.getGetTime(), three.getMileageKm());
                if (four != null) {
                    times++;
                }
            }
        }
        //只有本次工单
        if (times == 1) {
            if (vehInfo.getInvoiceDate() == null) {
                new DALException("销售日期不存在!");
            }
            logger.info("第一次voc{},销售日期：{}", one.getGetTime(), vehInfo.getInvoiceDate());
            long days = this.getCompareDate(one.getGetTime(), vehInfo.getInvoiceDate());
            if (days != 0) {
                dailyAverageMileage = one.getMileageKm() * 1.0 / days;
            } else {
                dailyAverageMileage = one.getMileageKm() * 1.0;
            }
        }
        if (times == 2) {
            logger.info("第一次voc{},第二次voc：{}", one.getGetTime(), two.getGetTime());
            dailyAverageMileage = (one.getMileageKm() - two.getMileageKm()) * 1.0 / this.getCompareDate(one
                    .getGetTime(), two
                    .getGetTime());
        }
        if (times == 3) {
            logger.info("第一次voc{},第二次voc：{},第三次voc：{}", one.getGetTime(), two.getGetTime(), three.getGetTime());
            dailyAverageMileage = ((one.getMileageKm() - two.getMileageKm()) * 1.0 / this.getCompareDate(one
                    .getGetTime(), two
                    .getGetTime()) + (one.getMileageKm() - three.getMileageKm()) * 1.0 / this.getCompareDate(one
                    .getGetTime(), three
                    .getGetTime())) / 2.0;
        }
        if (times == 4) {
            logger.info("第一次voc{},第二次voc：{},第三次voc：{},第四次voc：{}", one.getGetTime(), two.getGetTime(), three.getGetTime
                    (), four.getGetTime());
            dailyAverageMileage = ((one.getMileageKm() - two.getMileageKm()) * 1.0 / this.getCompareDate(one
                    .getGetTime(), two
                    .getGetTime()) + (one.getMileageKm() - three.getMileageKm()) * 1.0 / this.getCompareDate(one
                    .getGetTime(), three
                    .getGetTime()) + (one.getMileageKm() - four.getMileageKm()) * 1.0 / this.getCompareDate(one
                    .getGetTime(), four
                    .getGetTime()))
                    / 3.0;
        }
        LambdaQueryWrapper<VehiclePO> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(VehiclePO::getVin, vin);
        VehiclePO rs = vehicleMapper.selectOne(queryWrapper);
        this.saveDailyMileageLog(vin, vehInfo.getInvoiceDate(), one, two, three, four, rs,
               new BigDecimal(dailyAverageMileage));
        if (dailyAverageMileage != null && dailyAverageMileage >= 0) {
            if (rs != null) {
                rs.setDailyAverageMileage(new BigDecimal(dailyAverageMileage));
                rs.setIsVoc(10041001);
                vehicleMapper.updateVocById(rs);
            } else {
                VehiclePO insert = new VehiclePO();
                insert.setVin(vin);
                insert.setDailyAverageMileage(new BigDecimal(dailyAverageMileage));
                insert.setIsVoc(10041001);
                vehicleMapper.insertVoc(insert);
            }
            //增加首保更新建议入场时间
//            this.updateFirstMaintainTask(nextMonthDay, vehInfo, vin, dailyAverageMileage);
            this.updateMaintainTaskWithDateRange( startDate, endDate , vehInfo, vin, dailyAverageMileage);
        }
        return 1;
    }

    /**
     * 更新首保邀约任务 （增加）2023-05-12
     *
     * @param nextMonthDay
     * @param list
     */
    @Override
    public void updateFirstMaintainTask(String nextMonthDay, List<VocMileageVO> list, String sql){
        logger.info("updateFirstMaintainTask, start: {}", list.size());
        Long startTime = new Date().getTime();
        logger.info("updateFirstMaintainTask, startTime:{}", startTime);
        list = list.stream().filter(a -> ObjectUtils.isNotEmpty(a) && ObjectUtils.isNotEmpty(a.getInvoiceDate()))
                .collect(Collectors.<VocMileageVO>toList());
        List<String> vinList = list.stream().map(VocMileageVO::getVin).collect(Collectors.toList());
        Map<String, VocMileageVO> map = list.stream().collect(Collectors.toMap(vo -> vo.getVin().toUpperCase(), Function.identity(), (key1, key2) -> key2));
        //查询首定保任务
        List<InviteVehicleTaskPO> taskPos = inviteVehicleTaskMapper.getFirstMaintainTasks(nextMonthDay, vinList);
        //查询计算数据
        //查询最近一次结算工单 获取 进出场里程数，结算时间
        Map<String, VehicleOwnerVO> map1 = reportCommonClient.queryRepairOrderByVinAndCodeList(sql);
        //查询最近一次voc里程
        Map<String, VehicleOwnerVO> map2  = reportCommonClient.queryVocByVinList(sql);
        //查询最近一次机滤结算工单，获取结算时间。
        Map<String, VehicleOwnerVO> map3 = reportCommonClient.queryRepairOrderByVinList(sql);

        List<InviteVehicleTaskPO> listPo = new ArrayList<>();
        List<InviteVehicleRecordDTO> listDto = new ArrayList<>();
        VehicleOwnerVO vo;
        VocMileageVO mileageVO;
        String vin;
        for (InviteVehicleTaskPO po : taskPos) {
            vin = po.getVin().toUpperCase();
            logger.info("updateFirstMaintainTask,vin:{}",vin);
            vo = new VehicleOwnerVO();
            mileageVO = map.get(vin);
            if(ObjectUtils.isEmpty(mileageVO)){
                logger.info("updateFirstMaintainTask,ObjectUtils.isEmpty(mileageVO),vin:{}",vin);
                continue;
            }
            vo.setVin(vin);
            vo.setInvoiceDate(mileageVO.getInvoiceDate());
            Date adviseInDate = this.setAdviseInDate(vo, po.getDealerCode(), vo.getInvoiceDate(),
                    mileageVO.getDailyAverageMileage().doubleValue(), map1, map2, map3);
            logger.info("updateFirstMaintainTask,adviseInDate:{}", DateUtils.dateToString(adviseInDate, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
            adviseInDate = this.getDateForVoc(adviseInDate, nextMonthDay);
            logger.info("updateFirstMaintainTask,adviseInDate:{}", DateUtils.dateToString(adviseInDate, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
            if(! DateUtils.comto(po.getAdviseInDate())){
                vo.setOutMileage(po.getOutMileage());
                logger.info("updateFirstMaintainTask,!DateUtils.comto(po.getAdviseInDate())");
                //当月之后的线索，计算出来的建议入场日期如果是在当前月份之前，则更新为下月1号；
                //当月之后的线索，计算出来的建议入场日期如果是在当前月份，则更新为下月1号；
                //当月之后的线索，计算出来的建议入场日期如果是在当前月份之后，则直接更新；
                // 时间在 当前 之后
                if(! DateUtils.comto(adviseInDate)){
                    po.setAdviseInDate(adviseInDate);
                }else{
                    po.setAdviseInDate(nextMonthFirstDate());
                }
                //删除判断早于原建议入厂时间
                //建议进厂时间-1月
                Calendar c = Calendar.getInstance();
                c.setTime(po.getAdviseInDate());
                c.add(Calendar.MONTH, CommonConstants.UP_MONTH);
                po.setCreateInviteTime(c.getTime());
                po.setUpdatedAt(new Date());
                po.setDailyMileage(po.getDailyAverageMileage());
                po.setAdviseInMileage(vo.getAdviseInMileage());
                po.setUpdatedBy("1");
                listPo.add(po);
                if (po.getInviteId() != null) {
                    InviteVehicleRecordDTO dto = new InviteVehicleRecordDTO();
                    dto.setAdviseInDate(po.getAdviseInDate());
                    dto.setId(po.getInviteId());
                    dto.setUpdatedAt(new Date());
                    listDto.add(dto);
                }
            }
        }
        //批量修改
        if(listPo.size() > 0){
            logger.info("updateFirstMaintainTask,listPo:{}", listPo.size());
            Lists.partition(listPo,500).forEach(inviteVehicleTaskMapper::updateFordailyAverageMileageByIds);
        }
        if(listDto.size() > 0){
            logger.info("updateFirstMaintainTask,listDto:{}", listDto.size());
            Lists.partition(listDto,500).forEach(inviteVehicleRecordMapper::updateFordailyAverageMileageByIds);
        }
        map.clear();map1.clear();map2.clear();map3.clear();
        Long endTime = new Date().getTime();
        logger.info("updateFirstMaintainTask,end,耗时:{}", endTime - startTime);
    }

    /**
     * 更新定保邀约任务
     */
    @Override
    public void updateMaintainTask(String nextMonthDay, List<VocMileageVO> list, String sql) {
        logger.info("updateMaintainTask, start: {}", list.size());
        Long startTime = new Date().getTime();
        logger.info("updateMaintainTask, startTime:{}", startTime);
        list = list.stream().filter(a -> ObjectUtils.isNotEmpty(a) && ObjectUtils.isNotEmpty(a.getInvoiceDate()))
                .collect(Collectors.<VocMileageVO>toList());
        List<String> vinList = list.stream().map(VocMileageVO::getVin).collect(Collectors.toList());
        Map<String, VocMileageVO> map = list.stream().collect(Collectors.toMap(vo -> vo.getVin().toUpperCase(), Function.identity(), (key1, key2) -> key2));
        //查询首定保任务
        List<InviteVehicleTaskPO> taskPos = inviteVehicleTaskMapper.getMaintainTasks(nextMonthDay, vinList);
        //查询计算数据
        //查询最近一次结算工单 获取 进出场里程数，结算时间
        Map<String, VehicleOwnerVO> map1 = reportCommonClient.queryRepairOrderByVinAndCodeList(sql);
        //查询最近一次voc里程
        Map<String, VehicleOwnerVO> map2 = reportCommonClient.queryVocByVinList(sql);
        //查询最近一次机滤结算工单，获取结算时间。
        Map<String, VehicleOwnerVO> map3 = reportCommonClient.queryRepairOrderByVinList(sql);

        List<InviteVehicleTaskPO> listPo = new ArrayList<>();
        List<InviteVehicleRecordDTO> listDto = new ArrayList<>();
        VehicleOwnerVO vo;
        VocMileageVO mileageVO;
        String vin;
        for (InviteVehicleTaskPO po : taskPos) {
            vin = po.getVin().toUpperCase();
            mileageVO = map.get(vin);
            if(ObjectUtils.isEmpty(mileageVO)){
                logger.info("updateFirstMaintainTask,ObjectUtils.isEmpty(mileageVO),vin:{}",vin);
                continue;
            }
            logger.info("updateMaintainTask,vin:{}",vin);
            vo = new VehicleOwnerVO();
            vo.setVin(vin);
            vo.setInvoiceDate(mileageVO.getInvoiceDate());
            vo.setOutMileage(po.getOutMileage());
            Date adviseInDate = this.setAdviseInDate(vo, po.getDealerCode(), po.getInviteTime(),
                    mileageVO.getDailyAverageMileage().doubleValue(), map1, map2, map3);
            logger.info("updateMaintainTask,adviseInDate:{}", DateUtils.dateToString(adviseInDate, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
            adviseInDate = this.getDateForVoc(adviseInDate, nextMonthDay);
            logger.info("updateMaintainTask,adviseInDate:{}", DateUtils.dateToString(adviseInDate, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
            if(! DateUtils.comto(po.getAdviseInDate())){
                logger.info("updateMaintainTask,!DateUtils.comto(po.getAdviseInDate())");
                if(! DateUtils.comto(adviseInDate)){
                    po.setAdviseInDate(adviseInDate);
                }else{
                    po.setAdviseInDate(nextMonthFirstDate());
                }
                //建议进厂时间-1月
                Calendar c = Calendar.getInstance();
                c.setTime(po.getAdviseInDate());
                c.add(Calendar.MONTH, CommonConstants.UP_MONTH);
                po.setCreateInviteTime(c.getTime());
                po.setUpdatedAt(new Date());
                po.setDailyMileage(po.getDailyAverageMileage());
                po.setAdviseInMileage(vo.getAdviseInMileage());
                po.setUpdatedBy("1");
                listPo.add(po);
                if (po.getInviteId() != null) {
                    InviteVehicleRecordDTO dto = new InviteVehicleRecordDTO();
                    dto.setAdviseInDate(po.getAdviseInDate());
                    dto.setId(po.getInviteId());
                    dto.setUpdatedAt(new Date());
                    listDto.add(dto);
                }
            }
        }
        //批量修改
        if(listPo.size() > 0){
            logger.info("updateMaintainTask, listPo:{}", listPo.size());
            Lists.partition(listPo,500).forEach(inviteVehicleTaskMapper::updateFordailyAverageMileageByIds);
        }
        if(listDto.size() > 0){
            logger.info("updateMaintainTask, listDto:{}", listDto.size());
            Lists.partition(listDto,500).forEach(inviteVehicleRecordMapper::updateFordailyAverageMileageByIds);
        }
        map.clear();map1.clear();map2.clear();map3.clear();
        Long endTime = new Date().getTime();
        logger.info("updateMaintainTask,end,耗时:{}", endTime - startTime);
    }

    /**
     * 更新首保邀约任务 （增加）2021-02-23
     *
     * @param vin
     * @param dailyAverageMileage
     */
    @Override
    public void updateFirstMaintainTask(String nextMonthDay, VehicleOwnerVO vo, String vin, Double dailyAverageMileage) {
        long startTime = System.currentTimeMillis();
        logger.info("25voc定时任务：{}，{},{},startTime:{}",nextMonthDay,vin,dailyAverageMileage,startTime);
        StopWatch sw = new StopWatch();

        sw.start("updateFirstMaintainTask:查询需要跟新的首保任务");
        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.getFirstMaintainTask(nextMonthDay, vin);
        sw.stop();

           // logger.info("25voc定时任务查询到数据：{}", JSONObject.toJSON(list));
        for (InviteVehicleTaskPO po : list) {
            vo.setOutMileage(po.getOutMileage());
            //logger.info("25voc定时任务查询到数据vO：{}", JSONObject.toJSON(vo));
            logger.info("25voc定时任务查询到数据po.getDealerCode()：{}", po.getDealerCode());
            //logger.info("25voc定时任务查询到数据po.getInviteTime()()：{}", po.getInviteTime());
            //使用最新的开票日期
            sw.start("updateFirstMaintainTask:查询需要跟新的首保建议进厂时间");
            Date adviseInDate = this.setAdviseInDate(vo, po.getDealerCode(), vo.getInvoiceDate(), dailyAverageMileage);
            //logger.info("25voc定时任务查询到数据adviseInDate：{}", adviseInDate);
            adviseInDate = this.getDateForVoc(adviseInDate, nextMonthDay);
            sw.stop();
            //logger.info("25voc定时任务查询到数据adviseInDate：{}", adviseInDate);
            //当前月份及之前的线索不更新；
            // 时间在 当前 之后
            sw.start("updateFirstMaintainTask:跟新的首保任务和线索");
            if(! DateUtils.comto(po.getAdviseInDate())){
                logger.info("25voc定时任务查询到数据DateUtils.comto({})：{}",po.getAdviseInDate(), true);

                //当月之后的线索，计算出来的建议入场日期如果是在当前月份之前，则更新为下月1号；
                //当月之后的线索，计算出来的建议入场日期如果是在当前月份，则更新为下月1号；
                //当月之后的线索，计算出来的建议入场日期如果是在当前月份之后，则直接更新；
                // 时间在 当前 之后
                if(! DateUtils.comto(adviseInDate)){
                    po.setAdviseInDate(adviseInDate);
                }else{
                    po.setAdviseInDate(nextMonthFirstDate());
                }
                //删除判断早于原建议入厂时间
                //if( adviseInDate.before( po.getAdviseInDate())) {
               // po.setAdviseInDate(adviseInDate);
                //建议进厂时间-1月
                Calendar c = Calendar.getInstance();
                c.setTime(po.getAdviseInDate());
                c.add(Calendar.MONTH, CommonConstants.UP_MONTH);
                po.setCreateInviteTime(c.getTime());
                po.setUpdatedAt(new Date());
                po.setDailyMileage(dailyAverageMileage);
                po.setAdviseInMileage(vo.getAdviseInMileage());
                po.setUpdatedBy("1");
                inviteVehicleTaskMapper.updateFordailyAverageMileageById(po);
                if (po.getInviteId() != null) {
                    InviteVehicleRecordDTO dto = new InviteVehicleRecordDTO();
                    dto.setAdviseInDate(po.getAdviseInDate());
                    dto.setId(po.getInviteId());
                    dto.setUpdatedAt(new Date());
                    inviteVehicleRecordMapper.updateFordailyAverageMileageById(dto);
                }
                //}
            }
            sw.stop();

        }
        long endTime = System.currentTimeMillis();
        logger.info("25voc定时任务,endTime:{}",endTime);
        logger.info("updateFirstMaintainTask:{}",sw.prettyPrint());
    }

    /**
     * 更新定保邀约任务
     *
     * @param vin
     * @param dailyAverageMileage
     */
    @Override
    public void updateMaintainTask(String nextMonthDay, VehicleOwnerVO vo, String vin, Double dailyAverageMileage) {
        long startTime = System.currentTimeMillis();
        StopWatch sw = new StopWatch();
        logger.info("25voc定时任务定保：{}，{},{},startTime:{}",nextMonthDay,vin,dailyAverageMileage, startTime);
        sw.start("updateMaintainTask:查询需要跟新的定保任务");
        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.getMaintainTask(nextMonthDay, vin);
        sw.stop();

        //logger.info("25voc定时任务查询定保到数据：{}", JSONObject.toJSON(list));
        for (InviteVehicleTaskPO po : list) {
            vo.setOutMileage(po.getOutMileage());
            //logger.info("25voc定时任务查询到数据vO：{}", JSONObject.toJSON(vo));
            logger.info("25voc定时任务查询到数据po.getDealerCode()：{}", po.getDealerCode());
            logger.info("25voc定时任务查询到数据po.getInviteTime()()：{}", po.getInviteTime());
            sw.start("updateMaintainTask:获取建议进厂日期");
            Date adviseInDate = this.setAdviseInDate(vo, po.getDealerCode(), po.getInviteTime(), dailyAverageMileage);
            sw.stop();
            //logger.info("25voc定时任务查询到数据adviseInDate：{}", adviseInDate);
            adviseInDate = this.getDateForVoc(adviseInDate, nextMonthDay);
            //logger.info("25voc定时任务查询到数据adviseInDate：{}", adviseInDate);
            //当前月份及之前的线索不更新；
            // 时间在 当前 之后
            sw.start("updateMaintainTask:更新任务和线索");
            if(! DateUtils.comto(po.getAdviseInDate())){
                logger.info("25voc定时任务查询到数据DateUtils.comto({})：{}",po.getAdviseInDate(), true);
                //当月之后的线索，计算出来的建议入场日期如果是在当前月份之前，则更新为下月1号；
                //当月之后的线索，计算出来的建议入场日期如果是在当前月份，则更新为下月1号；
                //当月之后的线索，计算出来的建议入场日期如果是在当前月份之后，则直接更新；
                // 时间在 当前 之后
                if(! DateUtils.comto(adviseInDate)){
                    po.setAdviseInDate(adviseInDate);
                }else{
                    po.setAdviseInDate(nextMonthFirstDate());
                }
                //建议进厂时间-1月
                Calendar c = Calendar.getInstance();
                c.setTime(po.getAdviseInDate());
                c.add(Calendar.MONTH, CommonConstants.UP_MONTH);
                po.setCreateInviteTime(c.getTime());
                po.setUpdatedAt(new Date());
                po.setDailyMileage(dailyAverageMileage);
                po.setAdviseInMileage(vo.getAdviseInMileage());
                po.setUpdatedBy("1");
                inviteVehicleTaskMapper.updateFordailyAverageMileageById(po);
                if (po.getInviteId() != null) {
                    InviteVehicleRecordDTO dto = new InviteVehicleRecordDTO();
                    dto.setAdviseInDate(po.getAdviseInDate());
                    dto.setId(po.getInviteId());
                    dto.setUpdatedAt(new Date());
                    inviteVehicleRecordMapper.updateFordailyAverageMileageById(dto);
                }
            }
            sw.stop();
        }
        long endTime = System.currentTimeMillis();
        logger.info("25voc定时任务定保：endTime:{}", endTime);

        logger.info("updateMaintainTask:{}",sw.prettyPrint());
    }

    @Override
    public void updateMaintainTaskWithDateRange(LocalDate startDate, LocalDate endDate, VehicleOwnerVO vo, String vin, Double dailyAverageMileage) {

        if ( startDate == null || endDate == null ) return;

        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.getMaintainTaskWithDateRange( startDate.atStartOfDay(), LocalDateTime.of( endDate, LocalTime.MAX ), vin);
        for (InviteVehicleTaskPO po : list) {
            try {
                vo.setOutMileage(po.getOutMileage());
                Date adviseInDate = this.setAdviseInDate(vo, po.getDealerCode(), po.getInviteTime(), dailyAverageMileage);
                /**
                 * 关于建议入场日期的修改
                 * 邀约任务表中的数据都修改 邀约线索表中的只修改线索未完成的
                 * 邀约线索表中的建议入场日期修改逻辑：
                 * 如果计算出的建议入场日期小于 endDate(此方法的参数) 那么不修改
                 * 如果计算出的建议入场日期大于 endDate(此方法的参数) 那么修改
                 */
                if ( adviseInDate != null ) {
                    LocalDate adviseInDateLocateDate = Instant.ofEpochMilli( adviseInDate.getTime()).atZone(ZoneId.systemDefault()).toLocalDate();
                    Long inviteId = po.getInviteId();
                    po.setAdviseInDate(adviseInDate);
                    //建议进厂时间-90天
                    Calendar c = Calendar.getInstance();
                    c.setTime(po.getAdviseInDate());
                    c.add(Calendar.DATE, CommonConstants.UP_DAY);
                    po.setCreateInviteTime(c.getTime());
                    po.setUpdatedAt(new Date());
                    po.setDailyMileage(dailyAverageMileage);
                    po.setAdviseInMileage(vo.getAdviseInMileage());
                    po.setUpdatedBy("1");
                    inviteVehicleTaskMapper.updateFordailyAverageMileageById(po);
                    if ( inviteId != null) {
                        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordMapper.selectById( inviteId );
                        if ( inviteVehicleRecordPO != null ) {
                            Integer orderStatus = inviteVehicleRecordPO.getOrderStatus();
                            if ( CommonConstants.ORDER_STATUS_II.equals(  orderStatus ) && adviseInDateLocateDate.isAfter( endDate ) ) {
                                inviteVehicleRecordPO.setAdviseInDate( adviseInDate );
                                inviteVehicleRecordMapper.updateById( inviteVehicleRecordPO );
                            }
                        }
                    }
                }
            }catch ( Exception e ) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 更新易损件邀约任务
     *
     * @param vin
     * @param dailyAverageMileage
     */
    @Override
    public void updateVulnerableTask(String nextMonthDay, VehicleOwnerVO vo, String vin, Double dailyAverageMileage) {
        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.getVulnerableTask(nextMonthDay, vin);
        for (InviteVehicleTaskPO po : list) {
            vo.setOutMileage(po.getOutMileage());
            Date adviseInDate = this.setAdviseInDate(vo, po.getInviteTime(), dailyAverageMileage, po
                    .getMileageInterval(), po.getDateInterval());
            adviseInDate = this.getDateForVoc(adviseInDate, nextMonthDay);
            po.setAdviseInDate(adviseInDate);
            //建议进厂时间-90天
            Calendar c = Calendar.getInstance();
            c.setTime(po.getAdviseInDate());
            c.add(Calendar.DATE, CommonConstants.UP_DAY);
            po.setCreateInviteTime(c.getTime());
            po.setDailyMileage(dailyAverageMileage);
            po.setUpdatedAt(new Date());
            po.setUpdatedBy("1");
            inviteVehicleTaskMapper.updateFordailyAverageMileageById(po);
            if (po.getInviteId() != null) {
                InviteVehicleRecordDTO dto = new InviteVehicleRecordDTO();
                dto.setAdviseInDate(adviseInDate);
                dto.setId(po.getInviteId());
                dto.setUpdatedAt(new Date());
                inviteVehicleRecordMapper.updateFordailyAverageMileageById(dto);
            }
        }
    }

    @Override
    public void updateVulnerableTaskForRuleChanged(String nextMonthDay, Integer type,
                                                   String code, Integer lastMileageInterval, Long id) {
        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.getVulnerableTaskForRuleChanged(nextMonthDay, type,
                code, id);
        for (InviteVehicleTaskPO po : list) {
            VehicleOwnerVO vo = new VehicleOwnerVO();
            vo.setVin(po.getVin());
            vo.setOutMileage(po.getOutMileage());
            Date adviseInDate = this.setAdviseInDate(vo, po.getInviteTime(), po.getDailyMileage(),
                    po.getMileageInterval(), po.getDateInterval());
            adviseInDate = this.getDateForVoc(adviseInDate, nextMonthDay);
            po.setAdviseInDate(adviseInDate);
            //建议进厂时间-90天
            Calendar c = Calendar.getInstance();
            c.setTime(po.getAdviseInDate());
            c.add(Calendar.DATE, CommonConstants.UP_DAY);
            po.setCreateInviteTime(c.getTime());
            po.setDailyMileage(po.getDailyMileage());
            po.setAdviseInMileage(vo.getAdviseInMileage());
            po.setChangedId(id);
            po.setUpdatedAt(new Date());
            po.setUpdatedBy("1");
            inviteVehicleTaskMapper.updateFordailyAverageMileageById(po);
            if (po.getInviteId() != null) {
                InviteVehicleRecordDTO dto = new InviteVehicleRecordDTO();
                dto.setAdviseInDate(adviseInDate);
                dto.setId(po.getInviteId());
                dto.setUpdatedAt(new Date());
                inviteVehicleRecordMapper.updateFordailyAverageMileageById(dto);
                InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordMapper.selectById(po.getInviteId());
                inviteVehicleTaskService.mergeInvite(inviteVehicleRecordPO);
            }
        }
    }


    /**
     * 首保规则修改
     *
     * @param nextMonthDay
     * @param dealerCode
     * @param ruleValue
     * @param closeInterval
     * @param remindInterval
     */
    @Override
    public void updateFirstMaintainTaskForRuleChanged(
            String nextMonthDay, Integer inviteRule, String dealerCode,
            Integer ruleValue, Integer closeInterval, Integer remindInterval, Long id) {
        //更新未下发超时关闭间隔和再提醒间隔
        inviteVehicleTaskMapper.updateCloseInterval(CommonConstants.INVITE_TYPE_FIRST_GUARANTEE,
                CommonConstants.INVITE_TYPE_I, dealerCode, closeInterval, remindInterval);
        //更新已下发超时关闭间隔和再提醒间隔
        inviteVehicleTaskMapper.updateCloseIntervalHasDown(CommonConstants.INVITE_TYPE_FIRST_GUARANTEE,
                CommonConstants.INVITE_TYPE_I, dealerCode, closeInterval, remindInterval);
        if(inviteRule.equals(CommonConstants.INVITE_RULE_I)) {
            List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.getFirstMaintainTaskForRuleChanged(nextMonthDay,
                    dealerCode, id);
            for (InviteVehicleTaskPO po : list) {

                Calendar c = Calendar.getInstance();
                c.setTime(po.getInviteTime());
                c.add(Calendar.MONTH, ruleValue);
                Date adviseInDate = this.getDateForVoc(c.getTime(), nextMonthDay);
                po.setAdviseInDate(adviseInDate);
                //建议进厂时间-90天
                c = Calendar.getInstance();
                c.setTime(po.getAdviseInDate());
                c.add(Calendar.DATE, CommonConstants.UP_DAY);
                po.setCreateInviteTime(c.getTime());
                po.setDailyMileage(po.getDailyMileage());
                po.setChangedId(id);
                po.setUpdatedAt(new Date());
                po.setUpdatedBy("1");
                inviteVehicleTaskMapper.updateFordailyAverageMileageById(po);
                if (po.getInviteId() != null) {
                    InviteVehicleRecordDTO dto = new InviteVehicleRecordDTO();
                    if(inviteRule.equals(CommonConstants.INVITE_RULE_I)) {
                        dto.setAdviseInDate(adviseInDate);
                    }
                    dto.setId(po.getInviteId());
                    dto.setUpdatedAt(new Date());
                    inviteVehicleRecordMapper.updateFordailyAverageMileageById(dto);
                    InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordMapper.selectById(po.getInviteId
                            ());
                    inviteVehicleTaskService.mergeInvite(inviteVehicleRecordPO);
                }
            }
        }else if(inviteRule.equals(CommonConstants.INVITE_RULE_II)){
            //更新未下发超时关闭间隔和再提醒间隔
            inviteVehicleTaskMapper.updateAdviseInMileage(CommonConstants.INVITE_TYPE_FIRST_GUARANTEE,
                    CommonConstants.INVITE_RULE_II,CommonConstants.INVITE_TYPE_I, dealerCode, ruleValue,id);
            //更新已下发超时关闭间隔和再提醒间隔
            inviteVehicleTaskMapper.updateAdviseInMileageHasDown(CommonConstants.INVITE_TYPE_FIRST_GUARANTEE,
                    CommonConstants.INVITE_RULE_II,CommonConstants.INVITE_TYPE_I, dealerCode, ruleValue,id);
        }

    }

    /**
     * 定保规则修改
     *
     * @param nextMonthDay
     */
    @Override
    public void updateMaintainTaskForRuleChanged(String nextMonthDay, String dealerCode, Integer ruleValue, Integer
            closeInterval, Integer remindInterval, Integer inviteRule, Integer lastRuleValue, Long id) {
        //更新未下发超时关闭间隔和再提醒间隔
        inviteVehicleTaskMapper.updateCloseInterval(CommonConstants.INVITE_TYPE_FIXED_WARRANTY,
                CommonConstants.INVITE_TYPE_II, dealerCode, closeInterval, remindInterval);
        //更新已下发超时关闭间隔和再提醒间隔
        inviteVehicleTaskMapper.updateCloseIntervalHasDown(CommonConstants.INVITE_TYPE_FIXED_WARRANTY,
                CommonConstants.INVITE_TYPE_II, dealerCode, closeInterval, remindInterval);
        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.getMaintainTaskForRuleChanged(nextMonthDay,
                dealerCode, id);
        for (InviteVehicleTaskPO po : list) {
            Date adviseInDate = null;
            Calendar c = Calendar.getInstance();
            if (po.getCloseTimes() == 0) {
                VehicleOwnerVO vo = new VehicleOwnerVO();
                vo.setVin(po.getVin());
                vo.setOutMileage(po.getOutMileage());
                adviseInDate = this.setAdviseInDate(vo, po.getDealerCode(), po.getInviteTime(), po.getDailyMileage());
                po.setAdviseInMileage(vo.getAdviseInMileage());
            } else {
                c.setTime(po.getInviteTime());
                c.add(Calendar.MONTH, remindInterval);
                adviseInDate = c.getTime();
            }
            adviseInDate = this.getDateForVoc(adviseInDate, nextMonthDay);
            po.setAdviseInDate(adviseInDate);
            //建议进厂时间-90天
            c.setTime(po.getAdviseInDate());
            c.add(Calendar.DATE, CommonConstants.UP_DAY);
            po.setCreateInviteTime(c.getTime());
            po.setUpdatedAt(new Date());
            po.setDailyMileage(po.getDailyMileage());
            po.setChangedId(id);
            po.setUpdatedBy("1");
            inviteVehicleTaskMapper.updateFordailyAverageMileageById(po);
            if (po.getInviteId() != null) {

                InviteVehicleRecordDTO dto = new InviteVehicleRecordDTO();
                dto.setAdviseInDate(adviseInDate);
                dto.setId(po.getInviteId());
                dto.setUpdatedAt(new Date());
                inviteVehicleRecordMapper.updateFordailyAverageMileageById(dto);
                InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordMapper.selectById(po.getInviteId());
                inviteVehicleTaskService.mergeInvite(inviteVehicleRecordPO);
            }
        }
    }

    /**
     * 客户流失规则修改
     *
     * @param nextMonthDay
     */
    @Override
    public void updateCustomerLossTaskForRuleChanged(String nextMonthDay, String dealerCode, Integer ruleValue, Integer
            closeInterval, Integer remindInterval, Long id) {
        //更新未下发超时关闭间隔和再提醒间隔
        inviteVehicleTaskMapper.updateCloseInterval(CommonConstants.INVITE_TYPE_CUS_LOSS,
                CommonConstants.INVITE_TYPE_VI, dealerCode, closeInterval, remindInterval);
        //更新已下发超时关闭间隔和再提醒间隔
        inviteVehicleTaskMapper.updateCloseIntervalHasDown(CommonConstants.INVITE_TYPE_CUS_LOSS,
                CommonConstants.INVITE_TYPE_VI, dealerCode, closeInterval, remindInterval);
        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.getCustomerLossTaskForRuleChanged(nextMonthDay,
                dealerCode, id);
        for (InviteVehicleTaskPO po : list) {
            Calendar c = Calendar.getInstance();
            if (po.getCloseTimes() == 0) {
                c.setTime(po.getInviteTime());
                c.add(Calendar.MONTH, ruleValue);
            } else {
                c.setTime(po.getInviteTime());
                c.add(Calendar.MONTH, remindInterval);
            }
            Date adviseInDate = this.getDateForVoc(c.getTime(), nextMonthDay);
            po.setAdviseInDate(adviseInDate);
            //建议进厂时间-90天
            c = Calendar.getInstance();
            c.setTime(po.getAdviseInDate());
            c.add(Calendar.DATE, CommonConstants.UP_DAY);
            po.setCreateInviteTime(c.getTime());
            po.setDailyMileage(po.getDailyMileage());
            po.setChangedId(id);
            po.setUpdatedAt(new Date());
            po.setUpdatedBy("1");
            inviteVehicleTaskMapper.updateFordailyAverageMileageById(po);
            if (po.getInviteId() != null) {
                InviteVehicleRecordDTO dto = new InviteVehicleRecordDTO();
                dto.setAdviseInDate(adviseInDate);
                dto.setId(po.getInviteId());
                dto.setUpdatedAt(new Date());
                inviteVehicleRecordMapper.updateFordailyAverageMileageById(dto);
                InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordMapper.selectById(po.getInviteId
                        ());
                inviteVehicleTaskService.mergeInvite(inviteVehicleRecordPO);
            }
        }
    }

    /**
     * 保修规则修改
     *
     * @param nextMonthDay
     */
    @Override
    public void updateGuaranteeTaskForRuleChanged(String nextMonthDay, String dealerCode, Integer ruleValue, Integer
            closeInterval, Integer remindInterval, Long id) {
        //更新未下发超时关闭间隔和再提醒间隔
        inviteVehicleTaskMapper.updateCloseInterval(CommonConstants.INVITE_TYPE_GUARANTEE,
                CommonConstants.INVITE_TYPE_IX, dealerCode, closeInterval, remindInterval);
        //更新已下发超时关闭间隔和再提醒间隔
        inviteVehicleTaskMapper.updateCloseIntervalHasDown(CommonConstants.INVITE_TYPE_GUARANTEE,
                CommonConstants.INVITE_TYPE_IX, dealerCode, closeInterval, remindInterval);
        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.getGuaranteeTaskForRuleChanged(nextMonthDay,
                dealerCode, id);
        for (InviteVehicleTaskPO po : list) {
            Calendar c = Calendar.getInstance();
            if (po.getCloseTimes() == 0) {
                c.setTime(po.getInviteTime());
                c.add(Calendar.YEAR, 3);
                c.add(Calendar.DATE, -1);
                c.add(Calendar.DATE, -(ruleValue));
            } else {
                c.setTime(po.getInviteTime());
                c.add(Calendar.MONTH, remindInterval);
            }
            Date adviseInDate = this.getDateForVoc(c.getTime(), nextMonthDay);
            po.setAdviseInDate(adviseInDate);
            //建议进厂时间-90天
            c = Calendar.getInstance();
            c.setTime(po.getAdviseInDate());
            c.add(Calendar.DATE, CommonConstants.UP_DAY);
            po.setCreateInviteTime(c.getTime());
            po.setDailyMileage(po.getDailyMileage());
            po.setChangedId(id);
            po.setUpdatedAt(new Date());
            po.setUpdatedBy("1");
            inviteVehicleTaskMapper.updateFordailyAverageMileageById(po);
            if (po.getInviteId() != null) {
                InviteVehicleRecordDTO dto = new InviteVehicleRecordDTO();
                dto.setAdviseInDate(adviseInDate);
                dto.setId(po.getInviteId());
                dto.setUpdatedAt(new Date());
                inviteVehicleRecordMapper.updateFordailyAverageMileageById(dto);
                InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordMapper.selectById(po.getInviteId
                        ());
                inviteVehicleTaskService.mergeInvite(inviteVehicleRecordPO);
            }
        }
    }


    private Date getDateForVoc(Date adviseInDate, String nextMonthDay) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date tag = null;
        try {
            tag = sdf.parse(nextMonthDay);
            if (adviseInDate.getTime() < tag.getTime()) {
                return tag;
            }
        } catch (Exception e) {

        }
        return adviseInDate;
    }


    private Boolean compareDate(Date adviseInDate, String nextMonthDay) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date tag = null;
        Boolean rs = null;
        try {
            tag = sdf.parse(nextMonthDay);
            rs = adviseInDate.getTime() < tag.getTime();
        } catch (Exception e) {

        }
        return rs;
    }


    /**
     * 计算建议进厂时间
     *
     * @param inviteTime
     * @param dailyAverageMileage
     * @param mileageInterval
     * @param dateInterval
     * @return
     */
    private Date setAdviseInDate(VehicleOwnerVO vo, Date inviteTime, Double dailyAverageMileage, Integer
            mileageInterval, Integer dateInterval) {
        if ((mileageInterval == 0 || dailyAverageMileage == null || dailyAverageMileage == 0) && dateInterval == 0) {
            return null;
        }
        Date adviseInDate1 = null;
        Date adviseInDate2 = null;
        //日期间隔
        if (dateInterval != 0) {
            Calendar c = Calendar.getInstance();
            c.setTime(inviteTime);
            c.add(Calendar.MONTH, dateInterval);
            adviseInDate1 = c.getTime();
        }
        //里程间隔
        if (mileageInterval != 0 && dailyAverageMileage != null && dailyAverageMileage != 0) {
            Calendar c = Calendar.getInstance();
            c.setTime(inviteTime);
            int amount = (int) (mileageInterval / dailyAverageMileage);
            c.add(Calendar.DATE, amount);
            adviseInDate2 = c.getTime();
        }
        if (vo.getOutMileage() == null) {
            logger.info("基准里程为null,设置初始里程为0{}", vo.getOutMileage());
            vo.setOutMileage(0);
        }
        vo.setAdviseInMileage(vo.getOutMileage() + mileageInterval);
        logger.info("建议入厂里程{}", vo.getAdviseInMileage());

        //日期间隔为空取里程间隔
        if (dateInterval == 0) {
            return adviseInDate2;
        }
        //里程间隔为空或平均里程为空 取日期间隔
        if (mileageInterval == 0 || dailyAverageMileage == null || dailyAverageMileage == 0) {
            return adviseInDate1;
        }
        //存在两条，取较小的建议进厂日期
        return adviseInDate1.compareTo(adviseInDate2) > 0 ? adviseInDate2 : adviseInDate1;
    }

    /**
     * 定保跟据定保规则 计算建议进厂时间
     *
     * @param dealerCode
     * @param inviteTime
     * @param dailyAverageMileage
     * @return
     */
    private Date setAdviseInDate(VehicleOwnerVO vo, String dealerCode, Date inviteTime, Double dailyAverageMileage) {
        logger.info("setAdviseInDate-getRegularMaintainRule开始：{}",vo.getVin());
        List<InviteRulePO> rules = inviteRuleService.getRegularMaintainRule(dealerCode);
        logger.info("setAdviseInDate-getRegularMaintainRule结束：{}",vo.getVin());
        if (rules.size() == 0) {
            //规则不存在时返回 null
            return null;
        }
        //白名单查询精准邀约权限
        Double advanceMileage = 0D;
        Integer advanceMonth = 0;
        boolean flag = false;
        List<String> list = whitelistQueryService.selectWhiteListString(CommonConstants.PRECISE_INVITA_FUN,CommonConstants.WHITE_LIST);
        if(CollectionUtils.isEmpty(list)){
            flag = true;
        }else if (list.contains(dealerCode)){
            flag = true;
        }
        logger.info("setAdviseInDate-getRegularMaintainRule whiteFlag:{}",flag);
        if(flag){
            InviteMaintainInfoPO po = inviteMaintainInfoService.selectMainInfoByVin(vo.getVin());
            if(po != null && po.getAdvanceMileage() != null && po.getAdvanceMonth() != null){
                logger.info("po {}", JSON.toJSONString(po));
                advanceMileage = po.getAdvanceMileage() > mileageMax ? po.getAdvanceMileage() : mileageMax;
                advanceMonth = po.getAdvanceMonth() > monthMax ? po.getAdvanceMonth() : monthMax;
                logger.info("advanceMileage {} advanceMonth {}", advanceMileage, advanceMonth);
            }
        }

        Date adviseInDate1 = null;
        Date adviseInDate2 = null;
        InviteRulePO ru1 = null;
        InviteRulePO ru2 = null;
        for (int i = 0; i < rules.size(); i++) {
            InviteRulePO rule = rules.get(i);
            Date adviseInDate = null;
            //日期间隔
            if (rule.getInviteRule() == 82041003) {
                Integer ruleValue = rule.getRuleValue();
                if(advanceMonth < 0){
                    ruleValue = ruleValue + advanceMonth;
                }
                Calendar c = Calendar.getInstance();
                c.setTime(inviteTime);
                c.add(Calendar.MONTH, ruleValue);
                adviseInDate = c.getTime();
            }
            //里程间隔
            else if (rule.getInviteRule() == 82041002) {
                if (dailyAverageMileage != null && dailyAverageMileage != 0) {
                    if(advanceMileage < 0){
                        Integer ruleValue = rule.getRuleValue();
                        ruleValue = ruleValue + advanceMileage.intValue();
                        logger.info("advanceMileage < 0 ruleValue {}", ruleValue);
                        rule.setRuleValue(ruleValue);
                    }
                    logger.info("setAdviseInDate-getIncomingDate开始：{},adviseInDate:{}",vo.getVin(),adviseInDate);
                    adviseInDate = getIncomingDate(rule,dailyAverageMileage,vo,dealerCode);
                    logger.info("setAdviseInDate-getIncomingDate结束：{},adviseInDate:{}",vo.getVin(),adviseInDate);
                }
                //定保保存建议进厂里程
                if (vo.getOutMileage() != null) {
                    vo.setAdviseInMileage(vo.getOutMileage() + rule.getRuleValue());
                }
            }
            if (i == 0) {
                adviseInDate1 = adviseInDate;
                ru1 = rule;
            } else if (i == 1) {
                adviseInDate2 = adviseInDate;
                ru2 = rule;
            }
        }
        //存在两条，取较小的建议进厂日期
        if (adviseInDate2 != null && adviseInDate1 != null) {
            if (adviseInDate1.compareTo(adviseInDate2) > 0) {
                vo.setDayInAdvance(ru2.getDayInAdvance());
                vo.setInviteRule(ru2.getInviteRule());
                vo.setRuleValue(ru2.getRuleValue());
                vo.setRemindInterval(ru2.getRemindInterval());
                vo.setCloseInterval(ru2.getCloseInterval());
                logger.info("计算出的建议进厂日期,adviseInDate{}", adviseInDate2);
                return adviseInDate2;
            } else {
                vo.setDayInAdvance(ru1.getDayInAdvance());
                vo.setInviteRule(ru1.getInviteRule());
                vo.setRuleValue(ru1.getRuleValue());
                vo.setRemindInterval(ru1.getRemindInterval());
                vo.setCloseInterval(ru1.getCloseInterval());
                logger.info("计算出的建议进厂日期,adviseInDate{}", adviseInDate1);
                return adviseInDate1;
            }
        }

        if (adviseInDate2 != null) {
            vo.setDayInAdvance(ru2.getDayInAdvance());
            vo.setInviteRule(ru2.getInviteRule());
            vo.setRuleValue(ru2.getRuleValue());
            vo.setRemindInterval(ru2.getRemindInterval());
            vo.setCloseInterval(ru2.getCloseInterval());
            logger.info("计算出的建议进厂日期,adviseInDate{}", adviseInDate2);
            return adviseInDate2;
        } else {
            vo.setDayInAdvance(ru1.getDayInAdvance());
            vo.setInviteRule(ru1.getInviteRule());
            vo.setRuleValue(ru1.getRuleValue());
            vo.setRemindInterval(ru1.getRemindInterval());
            vo.setCloseInterval(ru1.getCloseInterval());
            logger.info("计算出的建议进厂日期,adviseInDate{}", adviseInDate1);
            return adviseInDate1;
        }
    }

    private Date getIncomingDate(InviteRulePO rule, Double dailyAverageMileage, VehicleOwnerVO vo, String dealerCode) {
        //查询最近一次结算工单 获取 进出场里程数，结算时间
        VehicleOwnerVO   vehicleOwnerVO=     reportCommonClient.queryRepairOrderByVinAndCode(vo.getVin(),dealerCode);
        Integer    out =0;
        Date   deliveryDate =null;
        if(vehicleOwnerVO!=null){
            out =  vehicleOwnerVO.getOutMileage();
            deliveryDate =  vehicleOwnerVO.getDeliveryDate();

        }
        logger.info("25voc定时任务最近一次结算工单vin{},：{}，{}",vo.getVin(),out,deliveryDate);
        //查询最近一次voc里程
        VehicleOwnerVO voc =  reportCommonClient.queryVocByVin(vo.getVin());
        if(voc != null  ){
            logger.info("25voc定时任务最近一次结算工单vin{},：{}，{}",vo.getVin(),voc.getOutMileage(),voc.getDeliveryDate());
            if(vehicleOwnerVO ==null  || voc.getDeliveryDate().compareTo(deliveryDate)>0){
                out =  voc.getOutMileage();
                deliveryDate =  voc.getDeliveryDate();
            }
        }
        logger.info("25voc定时任务最近一次结算工单vin{},：{}，{}",vo.getVin(),out,deliveryDate);
        //查询最近一次机滤结算工单，获取结算时间。
        VehicleOwnerVO   vo1= reportCommonClient.queryRepairOrderByVinAndCodeAndJL(vo.getVin(),dealerCode);
        Integer out1 = 0 ;
        if(vo1!=null){
            out1 =   vo1.getOutMileage();
        }
        logger.info("25voc定时任务最近一次结算工单vin{},out1：{}",vo.getVin(),out1);
        if(out1+rule.getRuleValue()-out<=0){
            //返回下個月1號
            return   nextMonthFirstDate();
        }else {
            if(deliveryDate==null){
                return  null;
            }
            Calendar c = Calendar.getInstance();
            c.setTime(deliveryDate);
            int amount = (int) ((out1+rule.getRuleValue()-out) / dailyAverageMileage);
            c.add(Calendar.DATE, amount);
            return  c.getTime();
        }
    }

    /**
     * 定保跟据定保规则 计算建议进厂时间
     *
     * @param dealerCode
     * @param inviteTime
     * @param dailyAverageMileage
     * @return
     */
    private Date setAdviseInDate(VehicleOwnerVO vo, String dealerCode, Date inviteTime, Double dailyAverageMileage,
                                 Map<String, VehicleOwnerVO> map1, Map<String, VehicleOwnerVO> map2,Map<String, VehicleOwnerVO> map3) {
        String vin = vo.getVin().toUpperCase();
        logger.info("setAdviseInDate,start：{}",vin);
        List<InviteRulePO> rules = inviteRuleService.getRegularMaintainRule(dealerCode);
        if (rules.size() == 0) {
            logger.info("setAdviseInDate,rules.size() == 0");
            //规则不存在时返回 null
            return null;
        }
        Date adviseInDate1 = null;
        Date adviseInDate2 = null;
        InviteRulePO ru1 = null;
        InviteRulePO ru2 = null;
        for (int i = 0; i < rules.size(); i++) {
            InviteRulePO rule = rules.get(i);
            Date adviseInDate = null;
            //日期间隔
            if (rule.getInviteRule() == 82041003) {
                Integer ruleValue = rule.getRuleValue();
                Calendar c = Calendar.getInstance();
                c.setTime(inviteTime);
                c.add(Calendar.MONTH, ruleValue);
                adviseInDate = c.getTime();
                logger.info("setAdviseInDate,vin:{},adviseInDate:{},82041003", vin,
                        DateUtils.dateToString(adviseInDate, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
            }
            //里程间隔
            else if (rule.getInviteRule() == 82041002) {
                if (dailyAverageMileage != null && dailyAverageMileage != 0) {
                    adviseInDate = getIncomingDate(rule,dailyAverageMileage,vo,dealerCode,map1,map2,map3);
                    logger.info("setAdviseInDate,vin:{},adviseInDate:{},82041002", vin,
                            DateUtils.dateToString(adviseInDate, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
                }
                //定保保存建议进厂里程
                if (vo.getOutMileage() != null) {
                    vo.setAdviseInMileage(vo.getOutMileage() + rule.getRuleValue());
                }
            }
            if (i == 0) {
                adviseInDate1 = adviseInDate;
                ru1 = rule;
            } else if (i == 1) {
                adviseInDate2 = adviseInDate;
                ru2 = rule;
            }
        }
        //存在两条，取较小的建议进厂日期
        if (adviseInDate2 != null && adviseInDate1 != null) {
            if (adviseInDate1.compareTo(adviseInDate2) > 0) {
                vo.setDayInAdvance(ru2.getDayInAdvance());
                vo.setInviteRule(ru2.getInviteRule());
                vo.setRuleValue(ru2.getRuleValue());
                vo.setRemindInterval(ru2.getRemindInterval());
                vo.setCloseInterval(ru2.getCloseInterval());
                logger.info("setAdviseInDate,adviseInDat2{}", adviseInDate2);
                return adviseInDate2;
            } else {
                vo.setDayInAdvance(ru1.getDayInAdvance());
                vo.setInviteRule(ru1.getInviteRule());
                vo.setRuleValue(ru1.getRuleValue());
                vo.setRemindInterval(ru1.getRemindInterval());
                vo.setCloseInterval(ru1.getCloseInterval());
                logger.info("setAdviseInDate,adviseInDate1{}", adviseInDate1);
                return adviseInDate1;
            }
        }

        if (adviseInDate2 != null) {
            vo.setDayInAdvance(ru2.getDayInAdvance());
            vo.setInviteRule(ru2.getInviteRule());
            vo.setRuleValue(ru2.getRuleValue());
            vo.setRemindInterval(ru2.getRemindInterval());
            vo.setCloseInterval(ru2.getCloseInterval());
            logger.info("setAdviseInDate,adviseInDate2{}", adviseInDate2);
            return adviseInDate2;
        } else {
            vo.setDayInAdvance(ru1.getDayInAdvance());
            vo.setInviteRule(ru1.getInviteRule());
            vo.setRuleValue(ru1.getRuleValue());
            vo.setRemindInterval(ru1.getRemindInterval());
            vo.setCloseInterval(ru1.getCloseInterval());
            logger.info("setAdviseInDate,adviseInDate1{}", adviseInDate1);
            return adviseInDate1;
        }
    }
    private Date getIncomingDate(InviteRulePO rule, Double dailyAverageMileage, VehicleOwnerVO vo, String dealerCode,
                                 Map<String, VehicleOwnerVO> map1, Map<String, VehicleOwnerVO> map2,Map<String, VehicleOwnerVO> map3) {
        String vin = vo.getVin().toUpperCase();
        //查询最近一次结算工单 获取 进出场里程数，结算时间
        VehicleOwnerVO vehicleOwnerVO= map1.get(vin);
        Integer    out =0;
        Date   deliveryDate =null;
        if(vehicleOwnerVO!=null){
                out =  vehicleOwnerVO.getOutMileage();
               deliveryDate =  vehicleOwnerVO.getDeliveryDate();

        }
        logger.info("getIncomingDate,vin{},out：{}，deliveryDate:{}",vin,out,
                DateUtils.dateToString(deliveryDate, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
        //查询最近一次voc里程
        VehicleOwnerVO voc = map2.get(vin);
        if(voc != null  ){
            logger.info("getIncomingDate,voc,vin{},：{}，{}",vin,voc.getOutMileage(),voc.getDeliveryDate());
            if(vehicleOwnerVO ==null  || voc.getDeliveryDate().compareTo(deliveryDate)>0){
                    out =  voc.getOutMileage();
                   deliveryDate =  voc.getDeliveryDate();
            }
        }
        logger.info("getIncomingDate,vin{},out：{}，deliveryDate:{}",vin,out,
                DateUtils.dateToString(deliveryDate, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
        //查询最近一次机滤结算工单，获取结算时间。
        VehicleOwnerVO vo1= map3.get(vin);
        Integer out1 = 0 ;
        if(vo1!=null){
             out1 =   vo1.getOutMileage();
        }
        logger.info("getIncomingDate,vin{},out1：{}",vin,out1);
        if(out1+rule.getRuleValue()-out<=0){
            logger.info("getIncomingDate,out1+rule.getRuleValue()-out<=0");
            //返回下個月1號
            return   nextMonthFirstDate();
        }else {
            if(deliveryDate==null){
                return  null;
            }
            Calendar c = Calendar.getInstance();
            c.setTime(deliveryDate);
            int amount = (int) ((out1+rule.getRuleValue()-out) / dailyAverageMileage);
            logger.info("getIncomingDate,vin{},out1:{},rule:{},out:{},dailyAverageMileage:{}",vin, out1, rule.getRuleValue(), out, dailyAverageMileage);
            logger.info("getIncomingDate,vin{},out1+rule.getRuleValue()-out<=0/dailyAverageMileage:{}",vin, amount);
            c.add(Calendar.DATE, amount);
            return  c.getTime();
        }
    }
    public static Date nextMonthFirstDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.MONTH, 1);
        return calendar.getTime();
    }

    /**
     * 获取日期天数间隔
     *
     * @param a
     * @param b
     * @return
     */
    private Long getCompareDate(Date a, Date b) {
        return (a.getTime() - b.getTime()) / (1000 * 60 * 60 * 24);
    }

    /**
     * 判断间隔是否大于15天
     *
     * @param a
     * @param b
     * @return
     */
    private boolean compareDate(Date a, Date b) {
        Long days = this.getCompareDate(a, b);
        return days > 15 || days < -15;
    }


    /**
     * 保存日平均里程计算log
     *
     * @param vin
     * @param invoiceDate
     * @param one
     * @param two
     * @param three
     * @param four
     * @param po
     * @param dailyAverageMileage
     */
    public void saveDailyMileageLog(String vin, Date invoiceDate, VocMileageVO one, VocMileageVO two,
                                    VocMileageVO three, VocMileageVO four, VehiclePO po, BigDecimal dailyAverageMileage) {
        DailyMileageLogPO log = new DailyMileageLogPO();
        log.setVin(vin);
        log.setSalesDate(invoiceDate);
        if (one != null) {
            log.setTimeOne(one.getGetTime());
            log.setMileageOne(one.getMileageKm());
        }
        if (two != null) {
            log.setTimeTwo(two.getGetTime());
            log.setMileageTwo(two.getMileageKm());
        }
        if (three != null) {
            log.setTimeThree(three.getGetTime());
            log.setMileageThree(three.getMileageKm());
        }
        if (four != null) {
            log.setTimeFour(four.getGetTime());
            log.setMileageFour(four.getMileageKm());
        }
        if (po != null) {
            log.setLastDailyMileage(po.getDailyAverageMileage());
        }
        log.setCountType("voc");
        log.setDailyMileage(dailyAverageMileage);
        dailyMileageLogMapper.insertDailyMileageLog(log);
    }
    public int pushpartitionGetAllVocVeh(String createDate){
        Integer returnCount = 0;
        Integer partitionSize = 200000;
        Integer start = 0;
        long startTimeMillis = System.currentTimeMillis();
        logger.info("开始计算VOC日均行驶里程,createDate:{},startTimeMillis:{}", createDate, startTimeMillis);
        List<VocMileageVO> list = null;
        while (true){
            Integer begIndex = start * partitionSize;
            logger.info("createDate:{},begIndex:{},partitionSize:{}", createDate, begIndex, partitionSize);
            list = reportCommonClient.getAllVocVehWithMon(createDate,start * partitionSize,partitionSize);
            if(list == null || list.size() <= 0){
                logger.info("---执行完成---");
                break;
            }else {
                start++;
                logger.info("---开始执行---,start:{},size:{}", start, list.size());
                int i = pushgetAllVocVeh(list,createDate);
                returnCount += i;
                logger.info("returnCount:{}", returnCount);
            }
        }
        long endTimeMillis = System.currentTimeMillis();
        long execTime = endTimeMillis - startTimeMillis;
        logger.info("当月VOC里程计算总分页数,returnCount:{},endTimeMillis:{},execTime:{}",returnCount, endTimeMillis, execTime);
        return returnCount;
    }
    /**
     * 计算voc日均里程
     *
     * @return
     */
    private int pushgetAllVocVeh(List<VocMileageVO> list,String createDate) {
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        int size = list.size();
        logger.info("需执行数量{}", size);
        logger.info("当前时间{}", new Date());
        String nextMonthDay = this.getNextMonthFristDay(createDate);
        logger.info("下月基准时间{}", nextMonthDay);
        List<String> errorList = new ArrayList<>();
        ExecutorService executorService = Executors.newFixedThreadPool(50);
        final CountDownLatch latch = new CountDownLatch(size);
        for (VocMileageVO po : list) {
            executorService.submit(() -> {
                try {
                    VocMileageThreadPush vocMileageThread = new VocMileageThreadPush(nextMonthDay, po.getVin(), po.getInvoiceDate(),
                            errorList);
                    vocMileageThread.start();
                } catch (Exception e){
                    dailyMileageLogMapper.SetErrorlog("allVocVeh", e.getMessage(), e.getStackTrace()
                            .toString(),"voc计算日均里程异常");
                } finally {
                    latch.countDown();
                }
            });
        }
        try {
            latch.await();
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("{}", e.getMessage());
        }
        if (null != executorService) {
            executorService.shutdown();
        }
        return 1;
    }

    /**
     * 多线程处理
     */
    public class VocMileageThreadPush {
        private String vin;
        private Date invoiceDate;
        private String nextMonthDay;
        private List<String> errorVins;

        public VocMileageThreadPush(String nextMonthDay, String vin, Date invoiceDate,
                                List<String> errorVins) {
            super();
            this.vin = vin;
            this.invoiceDate = invoiceDate;
            this.errorVins = errorVins;
            this.nextMonthDay = nextMonthDay;
        }

        public void start() {
            try {
                pushbatchExecuteData(this.nextMonthDay, this.vin, this.invoiceDate);
            } catch (Exception e) {
                logger.error(">>执行数据异常:{}", e);
                dailyMileageLogMapper.SetErrorlog("vocDailyMileage", e.getMessage(), e.getStackTrace().toString(),
                        this.vin);
                e.printStackTrace();
                if (errorVins != null) {
                    //发生错误加入集合
                    errorVins.add(vin);
                }
            }
        }
    }

    /**
     * 计算日均里程
     *
     * @param vin
     * @return
     */
    public int pushbatchExecuteData(String nextMonthDay, String vin, Date invoiceDate) {
        VehicleOwnerVO vehInfo = new VehicleOwnerVO();
        vehInfo.setVin(vin);
        vehInfo.setInvoiceDate(invoiceDate);
        LambdaQueryWrapper<VehiclePO> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(VehiclePO::getVin, vin);
        VehiclePO rs = vehicleMapper.selectOne(queryWrapper);
        BigDecimal   dailyAverageMileage = new BigDecimal("0");
        if(rs  !=null && rs.getDailyAverageMileage()!=null ){
            dailyAverageMileage = rs.getDailyAverageMileage();
        }
        logger.info("获取日均里程：{},{}",dailyAverageMileage,vin);
            //增加首保更新建议入场时间
            this.updateFirstMaintainTask(nextMonthDay, vehInfo, vin, dailyAverageMileage.doubleValue());
            this.updateMaintainTask(nextMonthDay, vehInfo, vin, dailyAverageMileage.doubleValue());
            //删除易损件更新
            //this.updateVulnerableTask(nextMonthDay, vehInfo, vin, dailyAverageMileage);
        return 1;
    }
    /**
     *  非voc建议进厂补数据。 一次性
     *  方案：
     *  查询需要跟新的非voc车的线索（10月1及以后，线索状态未完成）
      */
    @Override
    public int pushComputeDailyAverageMileageRecord(String startDate, String endDate) {
        /**
         * *
         *          *
         *          *
         *          *
         *          *
         *          * SELECT count(1) from  `tt_invite_vehicle_record`
         *          * WHERE  `order_status` = 82411002 and `invite_type`
         *          * in (82381002,82381001) and  `advise_in_date`  >='2022-10-01 00:00:00'
         *          * and  `advise_in_date`  <'2022-11-01 00:00:00'
         *          * and `is_deleted`  = 0
         *          * and vin not in  (
         *          * SELECT vin   FROM `tm_vehicle` WHERE `is_voc`   = 10041001
         *          * )
         *          * order by  `advise_in_date`  desc;
         */
        Integer returnCount = 0;
        long startTimeMillis = System.currentTimeMillis();
        logger.info("非voc建议进厂补数据一次性任务和线索,createDate:{},endDate：{},startTimeMillis:{}", startDate,endDate, startTimeMillis);
        List<InviteVehicleRecordPO> list = inviteVehicleRecordMapper.getRecordByAdviseInDate(startDate,endDate);
        if(list!=null && list.size()>0){
            logger.info("---非voc建议进厂补数据一次性任务和线索开始执行---,size:{}", list.size());
            int i = pushRecord(list);
        }else{
            logger.info("---非voc建议进厂补数据一次性任务和线索开始执行---,size:0");
        }
        long endTimeMillis = System.currentTimeMillis();
        long execTime = endTimeMillis - startTimeMillis;
        logger.info("非voc建议进厂补数据一次性任务和线索,returnCount:{},endTimeMillis:{},execTime:{}",returnCount, endTimeMillis, execTime);
        return returnCount;
    }

    private int pushRecord(List<InviteVehicleRecordPO> list) {
        int size = list.size();

        List<String> errorList = new ArrayList<>();
        ExecutorService executorService = Executors.newFixedThreadPool(50);
        final CountDownLatch latch = new CountDownLatch(size);
        for (InviteVehicleRecordPO po : list) {
            executorService.submit(() -> {
                try {
                    RecordThread taskThread = new RecordThread(po,errorList);
                    taskThread.start();
                } catch (Exception e){
                    dailyMileageLogMapper.SetErrorlog("pushRecord", e.getMessage(), e.getStackTrace()
                            .toString(),po.getVin());
                } finally {
                    latch.countDown();
                }
            });
        }
        try {
            latch.await();
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("{}", e.getMessage());
        }
        if (null != executorService) {
            executorService.shutdown();
        }
        return 1;
    }

    /**
     * 多线程处理
     */
    public class RecordThread {
        private InviteVehicleRecordPO po;
        private List<String> errorVins;
        public RecordThread( InviteVehicleRecordPO po,
                           List<String> errorVins) {
            super();
            this.po = po;
            this.errorVins = errorVins;
        }

        public void start() {
            try {
                pushRecordbatchExecuteData(po);
            } catch (Exception e) {
                logger.error(">>执行数据异常:{}", e);
                dailyMileageLogMapper.SetErrorlog("RecordThread", e.getMessage(), e.getStackTrace().toString(),
                        po.getVin());
                e.printStackTrace();
                if (errorVins != null) {
                    //发生错误加入集合
                    errorVins.add(po.getVin());
                }
            }
        }
    }

    /**
     * 计算日均里程
     *
     * @return
     */
    public int pushRecordbatchExecuteData(InviteVehicleRecordPO po) {
        VehicleOwnerVO rsx =  reportCommonClient.getVehicleInfo(po.getVin());
        if(rsx==null){
            logger.info("非voc建议进厂补数据一次性任务和线索,vin:{} 中台数据不存在", po.getVin());
            return 1;
        }
        if(rsx.getInvoiceDate() ==null){
            logger.info("非voc建议进厂补数据一次性任务和线索,vin:{} 中台销售日期为空", po.getVin());
            return  1;
        }
        if(po.getInviteType().equals(82381001)){
            //首保
            this.updateFirstMaintainRecordOne(rsx, rsx.getDailyAverageMileage(),po);
        }else {
            //定保
            this.updateMaintainRecordOne(rsx, rsx.getDailyAverageMileage(),po);
        }
        return 1;
    }

    /**
     * 更新首保邀约任务 （增加）2021-02-23
     *
     * @param dailyAverageMileage
     */
    public void updateFirstMaintainRecordOne( VehicleOwnerVO vo, Double dailyAverageMileage,InviteVehicleRecordPO pox) {
        long startTime = System.currentTimeMillis();
        logger.info("非voc未下发邀约线索定保定时任务线索首保：startTime:{}", startTime);
        StopWatch sw = new StopWatch();
        //查询任务
       List<InviteVehicleTaskPO>  list = inviteVehicleTaskMapper.selectByInviteId(pox.getId());
        InviteVehicleTaskPO po =   list.get(0);
            // logger.info("25voc定时任务查询到数据：{}", JSONObject.toJSON(list));
            vo.setOutMileage(po.getOutMileage());
            //logger.info("25voc定时任务查询到数据vO：{}", JSONObject.toJSON(vo));
            logger.info("非voc未下发邀约线索任务定时任务线索查询到数据po.getDealerCode()：{},getInvoiceDate:{},vin:{},dailyAverageMileage:{}", po.getDealerCode(), vo.getInvoiceDate(), vo.getVin(), dailyAverageMileage);
            //logger.info("25voc定时任务查询到数据po.getInviteTime()()：{}", po.getInviteTime());
            //使用最新的开票日期
            sw.start("updateFirstMaintainRecordOne:查询需要跟新的首保建议进厂时间");
            Date adviseInDate = this.setAdviseInDate(vo, po.getDealerCode(), vo.getInvoiceDate(), dailyAverageMileage);
            logger.info("25voc定时任务查询到数据{}adviseInDate：{}", vo.getVin(),adviseInDate);
            //  adviseInDate = this.getDateForVoc(adviseInDate, nextMonthDay);
            sw.stop();
            //logger.info("25voc定时任务查询到数据adviseInDate：{}", adviseInDate);
            //当前月份及之前的线索不更新；
            // 时间在 当前 之后
            sw.start("updateFirstMaintainRecordOne:跟新的首保任务线索");
            if (!DateUtils.comto(po.getAdviseInDate())) {
                logger.info("非voc未下发邀约线索任务定时首保任务线索查询到数据DateUtils.comto({})：{}", po.getAdviseInDate(), true);

                //当月之后的线索，计算出来的建议入场日期如果是在当前月份之前，则更新为下月1号；
                //当月之后的线索，计算出来的建议入场日期如果是在当前月份，则更新为下月1号；
                //当月之后的线索，计算出来的建议入场日期如果是在当前月份之后，则直接更新；
                // 时间在 当前 之后
                if (!DateUtils.comto(adviseInDate)) {
                    po.setAdviseInDate(adviseInDate);
                } else {
                    po.setAdviseInDate(nextMonthFirstDate());
                }
                logger.info("DateUtils.comto(adviseInDate)：{},po.getAdviseInDate():{}", DateUtils.comto(adviseInDate),po.getAdviseInDate());
                //删除判断早于原建议入厂时间
                //建议进厂时间-90天
                Calendar c = Calendar.getInstance();
                c.setTime(po.getAdviseInDate());
                c.add(Calendar.DATE, CommonConstants.UP_DAY);
                po.setCreateInviteTime(c.getTime());
                po.setUpdatedAt(new Date());
                po.setDailyMileage(dailyAverageMileage);
                po.setAdviseInMileage(vo.getAdviseInMileage());
                po.setUpdatedBy("1");
                inviteVehicleTaskMapper.updateFordailyAverageMileageById(po);
                InviteVehicleRecordDTO dto = new InviteVehicleRecordDTO();
                dto.setAdviseInDate(po.getAdviseInDate());
                dto.setId(pox.getId());
                dto.setUpdatedAt(new Date());
                inviteVehicleRecordMapper.updateFordailyAverageMileageById(dto);
            }
        sw.stop();
        long endTime = System.currentTimeMillis();
        logger.info("非voc未下发邀约线索首保任务线索定时任务,endTime:{}",endTime);
        logger.info("updateFirstMaintainRecordOne:{}",sw.prettyPrint());
    }

    /**
     * 更新定保邀约任务
     *
     * @param dailyAverageMileage
     */
    public void updateMaintainRecordOne( VehicleOwnerVO vo, Double dailyAverageMileage,InviteVehicleRecordPO pox) {
        long startTime = System.currentTimeMillis();
        logger.info("非voc未下发邀约线索定保定时任务定保：startTime:{}", startTime);
        StopWatch sw = new StopWatch();
        //查询任务
        List<InviteVehicleTaskPO>  list = inviteVehicleTaskMapper.selectByInviteId(pox.getId());
        InviteVehicleTaskPO po =   list.get(0);
        vo.setOutMileage(po.getOutMileage());
        //logger.info("25voc定时任务查询到数据vO：{}", JSONObject.toJSON(vo));
        logger.info("非voc未下发邀约线索定保定时任务查询到数据po.getDealerCode()：{}", po.getDealerCode());
        logger.info("非voc未下发邀约线索定保定时任务查询到数据po.getInviteTime()()：{},vin:{}，dailyAverageMileage：{}", po.getInviteTime(),po.getVin(),dailyAverageMileage);
        sw.start("updateMaintainRecordOne:获取建议进厂日期");
        Date adviseInDate = this.setAdviseInDate(vo, po.getDealerCode(), po.getInviteTime(), dailyAverageMileage);
        logger.info("25voc定时任务查询到数据vin{}adviseInDate：{}", vo.getVin(),adviseInDate);
        sw.stop();
        //logger.info("25voc定时任务查询到数据adviseInDate：{}", adviseInDate);
        //logger.info("25voc定时任务查询到数据adviseInDate：{}", adviseInDate);
        //当前月份及之前的线索不更新；
        // 时间在 当前 之后
        sw.start("updateMaintainRecordOne:更新定保任务线索");
        if(! DateUtils.comto(po.getAdviseInDate())){
            logger.info("非voc未下发邀约线索定保定时任务查询到数据DateUtils.comto({})：{}",po.getAdviseInDate(), true);
            //当月之后的线索，计算出来的建议入场日期如果是在当前月份之前，则更新为下月1号；
            //当月之后的线索，计算出来的建议入场日期如果是在当前月份，则更新为下月1号；
            //当月之后的线索，计算出来的建议入场日期如果是在当前月份之后，则直接更新；
            // 时间在 当前 之后
            if(! DateUtils.comto(adviseInDate)){
                po.setAdviseInDate(adviseInDate);
            }else{
                po.setAdviseInDate(nextMonthFirstDate());
            }
            logger.info("DateUtils.comto(adviseInDate)：{},po.getAdviseInDate(){}", DateUtils.comto(adviseInDate),po.getAdviseInDate());
            //建议进厂时间-90天
            Calendar c = Calendar.getInstance();
            c.setTime(po.getAdviseInDate());
            c.add(Calendar.DATE, CommonConstants.UP_DAY);
            po.setCreateInviteTime(c.getTime());
            po.setUpdatedAt(new Date());
            po.setDailyMileage(dailyAverageMileage);
            po.setAdviseInMileage(vo.getAdviseInMileage());
            po.setUpdatedBy("1");
            inviteVehicleTaskMapper.updateFordailyAverageMileageById(po);
            InviteVehicleRecordDTO dto = new InviteVehicleRecordDTO();
            dto.setAdviseInDate(po.getAdviseInDate());
            dto.setId(pox.getId());
            dto.setUpdatedAt(new Date());
            inviteVehicleRecordMapper.updateFordailyAverageMileageById(dto);
        }
        sw.stop();
        long endTime = System.currentTimeMillis();
        logger.info("非voc未下发邀约线索定保定时任务线索定保：endTime:{}", endTime);

        logger.info("updateMaintainRecordOne:{}",sw.prettyPrint());
    }



    /**
     *  非voc建议进厂补数据。 一次性
     *  方案：
     *  查询需要跟新的非voc车的（10月1之后的）未下发的任务
     */
    @Override
    public int pushComputeDailyAverageMileageTask(String startDate, String endDate) {
        /**
         * SELECT COUNT(1)  FROM  tt_invite_vehicle_task where `is_create_invite` =0
         * and `invite_type` in (82381002,82381001)
         * and  `advise_in_date`  >='2022-10-01 00:00:00'
         *  and  `advise_in_date`  <'2022-11-01 00:00:00'
         * and `is_deleted`  = 0
         * and vin not in  (
         * SELECT vin   FROM `tm_vehicle` WHERE `is_voc`   = 10041001
         * )
         * ORDER BY  `advise_in_date`  asc ;
         */
        Integer returnCount = 0;
        long startTimeMillis = System.currentTimeMillis();
        logger.info("非voc建议进厂补数据一次性任务,createDate:{},endDate：{},startTimeMillis:{}", startDate,endDate, startTimeMillis);
        List<InviteVehicleTaskPO> list = inviteVehicleTaskMapper.getTaskByAdviseInDate(startDate,endDate);
        if(list!=null && list.size()>0){
            logger.info("---非voc建议进厂补数据一次性任务开始执行---,size:{}", list.size());
            int i = pushTask(list);
        }else{
            logger.info("---非voc建议进厂补数据一次性任务开始执行---,size:0");
        }
        long endTimeMillis = System.currentTimeMillis();
        long execTime = endTimeMillis - startTimeMillis;
        logger.info("非voc建议进厂补数据一次性任务,returnCount:{},endTimeMillis:{},execTime:{}",returnCount, endTimeMillis, execTime);
        return returnCount;
    }


    /**
     * 计算voc日均里程
     *
     * @return
     */
    private int pushTask(List<InviteVehicleTaskPO> list) {
        int size = list.size();

        List<String> errorList = new ArrayList<>();
        ExecutorService executorService = Executors.newFixedThreadPool(50);
        final CountDownLatch latch = new CountDownLatch(size);
        for (InviteVehicleTaskPO po : list) {
            executorService.submit(() -> {
                try {
                    TaskThread taskThread = new TaskThread(po,errorList);
                    taskThread.start();
                } catch (Exception e){
                    dailyMileageLogMapper.SetErrorlog("pushTask", e.getMessage(), e.getStackTrace()
                            .toString(),po.getVin());
                } finally {
                    latch.countDown();
                }
            });
        }
        try {
            latch.await();
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("{}", e.getMessage());
        }
        if (null != executorService) {
            executorService.shutdown();
        }
        return 1;
    }



    /**
     * 多线程处理
     */
    public class TaskThread {
        private InviteVehicleTaskPO po;
        private List<String> errorVins;
        public TaskThread( InviteVehicleTaskPO po,
                                List<String> errorVins) {
            super();
            this.po = po;
            this.errorVins = errorVins;
        }

        public void start() {
            try {
                pushTaskbatchExecuteData(po);
            } catch (Exception e) {
                logger.error(">>执行数据异常:{}", e);
                dailyMileageLogMapper.SetErrorlog("TaskThread", e.getMessage(), e.getStackTrace().toString(),
                        po.getVin());
                e.printStackTrace();
                if (errorVins != null) {
                    //发生错误加入集合
                    errorVins.add(po.getVin());
                }
            }
        }
    }
    /**
     * 计算日均里程
     *
     * @return
     */
    public int pushTaskbatchExecuteData(InviteVehicleTaskPO po) {
        VehicleOwnerVO rsx =  reportCommonClient.getVehicleInfo(po.getVin());
            if(rsx==null){
                logger.info("非voc建议进厂补数据一次性任务,vin:{} 中台数据不存在", po.getVin());
                return 1;
            }
            if(rsx.getInvoiceDate() ==null){
                logger.info("非voc建议进厂补数据一次性任务,vin:{} 中台销售日期为空", po.getVin());
                return  1;
            }
            if(po.getInviteType().equals(82381001)){
                //首保
                this.updateFirstMaintainTaskOne(rsx, rsx.getDailyAverageMileage(),po);
            }else {
                //定保
                this.updateMaintainTaskOne(rsx, rsx.getDailyAverageMileage(),po);
            }
        return 1;
    }

    /**
     * 更新首保邀约任务 （增加）2021-02-23
     *
     * @param dailyAverageMileage
     */
    public void updateFirstMaintainTaskOne( VehicleOwnerVO vo, Double dailyAverageMileage,InviteVehicleTaskPO po) {
        long startTime = System.currentTimeMillis();
        logger.info("非voc未下发邀约线索定保定时任务首保：startTime:{}", startTime);
        StopWatch sw = new StopWatch();


        // logger.info("25voc定时任务查询到数据：{}", JSONObject.toJSON(list));
            vo.setOutMileage(po.getOutMileage());
            //logger.info("25voc定时任务查询到数据vO：{}", JSONObject.toJSON(vo));
            logger.info("非voc未下发邀约线索任务定时任务查询到数据po.getDealerCode()：{},getInvoiceDate:{},vin:{},dailyAverageMileage:{}", po.getDealerCode(),vo.getInvoiceDate(),vo.getVin(),dailyAverageMileage);
            //logger.info("25voc定时任务查询到数据po.getInviteTime()()：{}", po.getInviteTime());
            //使用最新的开票日期
            sw.start("updateFirstMaintainTaskOne:查询需要跟新的首保建议进厂时间");
            Date adviseInDate = this.setAdviseInDate(vo, po.getDealerCode(), vo.getInvoiceDate(), dailyAverageMileage);
            logger.info("25voc定时任务查询到数据{}adviseInDate：{}",vo.getVin(), adviseInDate);
          //  adviseInDate = this.getDateForVoc(adviseInDate, nextMonthDay);
            sw.stop();
            //logger.info("25voc定时任务查询到数据adviseInDate：{}", adviseInDate);
            //当前月份及之前的线索不更新；
            // 时间在 当前 之后
            sw.start("updateFirstMaintainTaskOne:跟新的首保任务");
            if(! DateUtils.comto(po.getAdviseInDate())){
                logger.info("非voc未下发邀约线索任务定时首保任务查询到数据DateUtils.comto({})：{}",po.getAdviseInDate(), true);

                //当月之后的线索，计算出来的建议入场日期如果是在当前月份之前，则更新为下月1号；
                //当月之后的线索，计算出来的建议入场日期如果是在当前月份，则更新为下月1号；
                //当月之后的线索，计算出来的建议入场日期如果是在当前月份之后，则直接更新；
                // 时间在 当前 之后
                if(! DateUtils.comto(adviseInDate)){
                    po.setAdviseInDate(adviseInDate);
                }else{
                    po.setAdviseInDate(nextMonthFirstDate());
                }
                //删除判断早于原建议入厂时间
                //建议进厂时间-90天
                Calendar c = Calendar.getInstance();
                c.setTime(po.getAdviseInDate());
                c.add(Calendar.DATE, CommonConstants.UP_DAY);
                po.setCreateInviteTime(c.getTime());
                po.setUpdatedAt(new Date());
                po.setDailyMileage(dailyAverageMileage);
                po.setAdviseInMileage(vo.getAdviseInMileage());
                po.setUpdatedBy("1");
                inviteVehicleTaskMapper.updateFordailyAverageMileageById(po);
            }
            sw.stop();

        long endTime = System.currentTimeMillis();
        logger.info("非voc未下发邀约线索首保任务定时任务,endTime:{}",endTime);
        logger.info("updateFirstMaintainTaskOne:{}",sw.prettyPrint());
    }

    /**
     * 更新定保邀约任务
     *
     * @param dailyAverageMileage
     */
    public void updateMaintainTaskOne( VehicleOwnerVO vo, Double dailyAverageMileage,InviteVehicleTaskPO po) {
        long startTime = System.currentTimeMillis();
        logger.info("非voc未下发邀约线索定保定时任务定保：startTime:{}", startTime);
        StopWatch sw = new StopWatch();

        //logger.info("25voc定时任务查询定保到数据：{}", JSONObject.toJSON(list));
            vo.setOutMileage(po.getOutMileage());
            //logger.info("25voc定时任务查询到数据vO：{}", JSONObject.toJSON(vo));
            logger.info("非voc未下发邀约线索定保定时任务查询到数据po.getDealerCode()：{}", po.getDealerCode());
            logger.info("非voc未下发邀约线索定保定时任务查询到数据po.getInviteTime()()：{},vin:{}，dailyAverageMileage：{}", po.getInviteTime(),po.getVin(),dailyAverageMileage);
            sw.start("updateMaintainTaskOne:获取建议进厂日期");
            Date adviseInDate = this.setAdviseInDate(vo, po.getDealerCode(), po.getInviteTime(), dailyAverageMileage);
            sw.stop();
            logger.info("25voc定时任务查询到数据{}adviseInDate：{}", vo.getVin(),adviseInDate);
            //logger.info("25voc定时任务查询到数据adviseInDate：{}", adviseInDate);
            //当前月份及之前的线索不更新；
            // 时间在 当前 之后
            sw.start("updateMaintainTaskOne:更新定保任务");
            if(! DateUtils.comto(po.getAdviseInDate())){
                logger.info("非voc未下发邀约线索定保定时任务查询到数据DateUtils.comto({})：{}",po.getAdviseInDate(), true);
                //当月之后的线索，计算出来的建议入场日期如果是在当前月份之前，则更新为下月1号；
                //当月之后的线索，计算出来的建议入场日期如果是在当前月份，则更新为下月1号；
                //当月之后的线索，计算出来的建议入场日期如果是在当前月份之后，则直接更新；
                // 时间在 当前 之后
                if(! DateUtils.comto(adviseInDate)){
                    po.setAdviseInDate(adviseInDate);
                }else{
                    po.setAdviseInDate(nextMonthFirstDate());
                }
                //建议进厂时间-90天
                Calendar c = Calendar.getInstance();
                c.setTime(po.getAdviseInDate());
                c.add(Calendar.DATE, CommonConstants.UP_DAY);
                po.setCreateInviteTime(c.getTime());
                po.setUpdatedAt(new Date());
                po.setDailyMileage(dailyAverageMileage);
                po.setAdviseInMileage(vo.getAdviseInMileage());
                po.setUpdatedBy("1");
                inviteVehicleTaskMapper.updateFordailyAverageMileageById(po);
            }
            sw.stop();
        long endTime = System.currentTimeMillis();
        logger.info("非voc未下发邀约线索定保定时任务定保：endTime:{}", endTime);

        logger.info("updateMaintainTaskOne:{}",sw.prettyPrint());
    }

}
