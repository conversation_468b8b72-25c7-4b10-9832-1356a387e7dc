package com.yonyou.dmscus.customer.service.inviteInsurance;


import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceCustomerInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceCustomerInfoDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import javax.annotation.Resource;


/**
 * <p>
 * 续保呼叫登记自建联系人 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-15
 */
@Service
public class InviteInsuranceCustomerInfoServiceImpl implements InviteInsuranceCustomerInfoService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteInsuranceCustomerInfoMapper inviteInsuranceCustomerInfoMapper;


    @Override
    public List<InviteInsuranceCustomerInfoDTO> selectAllInsuranceCustomerInfo(Long insuranceId) {
        if(insuranceId != null){
            //获取登录用户信息
            LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
            return inviteInsuranceCustomerInfoMapper.selectAllInsuranceCustomerInfo(insuranceId,loginInfoDto.getOwnerCode());
        }else{
            return null;
        }
    }

    @Override
    public void updateInsuranceCustomerInfo(InviteInsuranceCustomerInfoDTO infoDTO) {
        if(infoDTO != null){
            //获取登录用户信息
            LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
            inviteInsuranceCustomerInfoMapper.updateInsuranceCustomerInfo(infoDTO,loginInfoDto.getUserId());
        }else{
            throw new DALException("请求参数为空，修改失败!");
        }
    }

    @Override
    public void deleteInsuranceCustomerInfo(Long tiicId) {
        if(tiicId != null){
            inviteInsuranceCustomerInfoMapper.deleteInsuranceCustomerInfo(tiicId);
        }else{
            throw new DALException("请求参数为空，删除失败!");
        }
    }
}
