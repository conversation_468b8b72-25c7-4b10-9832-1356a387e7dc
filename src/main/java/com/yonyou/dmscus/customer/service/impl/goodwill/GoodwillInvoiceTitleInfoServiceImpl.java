package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillInvoiceTitleInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillInvoiceTitleInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillInvoiceTitleInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillInvoiceTitleInfoService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 亲善发票抬头信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-17
 */
@Service
public class GoodwillInvoiceTitleInfoServiceImpl implements GoodwillInvoiceTitleInfoService {
	/** 日志对象 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillInvoiceTitleInfoMapper goodwillInvoiceTitleInfoMapper;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillInvoiceTitleInfoDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.GoodwillInvoiceTitleInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillInvoiceTitleInfoDTO> selectPageBysql(Page page,
			GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO) {
		if (goodwillInvoiceTitleInfoDTO == null) {
			goodwillInvoiceTitleInfoDTO = new GoodwillInvoiceTitleInfoDTO();
		}
		GoodwillInvoiceTitleInfoPO goodwillInvoiceTitleInfoPo = goodwillInvoiceTitleInfoDTO
				.transDtoToPo(GoodwillInvoiceTitleInfoPO.class);

		List<GoodwillInvoiceTitleInfoPO> list = goodwillInvoiceTitleInfoMapper.selectPageBySql(page,
				goodwillInvoiceTitleInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillInvoiceTitleInfoDTO> result = list.stream()
					.map(m -> m.transPoToDto(GoodwillInvoiceTitleInfoDTO.class)).collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillInvoiceTitleInfoDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.GoodwillInvoiceTitleInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillInvoiceTitleInfoDTO> selectListBySql(GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO) {
		if (goodwillInvoiceTitleInfoDTO == null) {
			goodwillInvoiceTitleInfoDTO = new GoodwillInvoiceTitleInfoDTO();
		}
		GoodwillInvoiceTitleInfoPO goodwillInvoiceTitleInfoPo = goodwillInvoiceTitleInfoDTO
				.transDtoToPo(GoodwillInvoiceTitleInfoPO.class);
		List<GoodwillInvoiceTitleInfoPO> list = goodwillInvoiceTitleInfoMapper
				.selectListBySql(goodwillInvoiceTitleInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillInvoiceTitleInfoDTO.class))
					.collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.GoodwillInvoiceTitleInfoDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillInvoiceTitleInfoDTO getById(Long id) {
		GoodwillInvoiceTitleInfoPO goodwillInvoiceTitleInfoPo = goodwillInvoiceTitleInfoMapper.selectById(id);
		if (goodwillInvoiceTitleInfoPo != null) {
			return goodwillInvoiceTitleInfoPo.transPoToDto(GoodwillInvoiceTitleInfoDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillInvoiceTitleInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO) {

		// 对对象进行赋值操作
		GoodwillInvoiceTitleInfoPO goodwillInvoiceTitleInfoPo = goodwillInvoiceTitleInfoDTO
				.transDtoToPo(GoodwillInvoiceTitleInfoPO.class);
		// 校验开票抬头是否存在
		Integer number = goodwillInvoiceTitleInfoMapper.queryGoodwillInvoiceTitleInfoByInvoiceTitle(
				goodwillInvoiceTitleInfoDTO.getInvoiceTitle(), goodwillInvoiceTitleInfoDTO.getId());
		if (number != null && number > 0) {
			throw new DALException("该开票抬头已存在，请确认后再输入");
		}
		// 执行插入
		goodwillInvoiceTitleInfoPo.setIsValid(10011001);
		int row = goodwillInvoiceTitleInfoMapper.insert(goodwillInvoiceTitleInfoPo);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillInvoiceTitleInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO) {

		GoodwillInvoiceTitleInfoPO goodwillInvoiceTitleInfoPo = goodwillInvoiceTitleInfoMapper.selectById(id);
		// 校验开票抬头是否存在
		Integer number = goodwillInvoiceTitleInfoMapper
				.queryGoodwillInvoiceTitleInfoByInvoiceTitle(goodwillInvoiceTitleInfoDTO.getInvoiceTitle(), id);
		if (number != null && number > 0) {
			throw new DALException("该开票抬头已存在，请确认后再输入");
		}
		// 对对象进行赋值操作
		goodwillInvoiceTitleInfoDTO.transDtoToPo(goodwillInvoiceTitleInfoPo);
		// 执行更新
		goodwillInvoiceTitleInfoPo.setIsValid(10011001);
		int row = goodwillInvoiceTitleInfoMapper.updateById(goodwillInvoiceTitleInfoPo);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillInvoiceTitleInfoMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillInvoiceTitleInfoMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillNoticeInvoiceInfoDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillInvoiceTitleInfoDTO queryInvoiceInfo(Integer invoiceTitle) {
		GoodwillInvoiceTitleInfoPO goodwillInvoiceTitleInfoPo = goodwillInvoiceTitleInfoMapper
				.queryInvoiceInfo(invoiceTitle);
		if (goodwillInvoiceTitleInfoPo != null) {
			return goodwillInvoiceTitleInfoPo.transPoToDto(GoodwillInvoiceTitleInfoDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}
}
