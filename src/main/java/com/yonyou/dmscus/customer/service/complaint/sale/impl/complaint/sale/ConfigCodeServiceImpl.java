package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yonyou.dmscus.customer.dao.complaint.CommonConfigMapper;
import com.yonyou.dmscus.customer.entity.po.complaint.CommonConfigPO;
import com.yonyou.dmscus.customer.service.complaint.sale.ConfigCodeService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@AllArgsConstructor
@Service
public class ConfigCodeServiceImpl implements ConfigCodeService {

    private CommonConfigMapper commonConfigMapper;

    @Override
    public List<CommonConfigPO> getByConfigCode(String configCode) {
        LambdaQueryWrapper<CommonConfigPO> queryWrapper = new LambdaQueryWrapper<CommonConfigPO>()
                .eq(CommonConfigPO::getConfigCode, configCode).eq(CommonConfigPO::getIsDeleted, 0);
        return commonConfigMapper.selectList(queryWrapper);
    }


    /**
     * 根据分组和KEY获取配置信息
     *
     * @param groupType 分组类型
     * @param configKey 配置key
     */
    @Override
    public CommonConfigPO getConfigByKey(String groupType, String configKey) {
        LambdaQueryWrapper<CommonConfigPO> queryWrapper = new LambdaQueryWrapper<CommonConfigPO>()
                .eq(CommonConfigPO::getConfigKey, configKey)
                .eq(CommonConfigPO::getIsDeleted, 0);
        if (!StringUtils.isBlank(groupType)) {
            queryWrapper.eq(CommonConfigPO::getGroupType, groupType);
        }
        queryWrapper.last("limit 1");
        return commonConfigMapper.selectOne(queryWrapper);
    }


}
