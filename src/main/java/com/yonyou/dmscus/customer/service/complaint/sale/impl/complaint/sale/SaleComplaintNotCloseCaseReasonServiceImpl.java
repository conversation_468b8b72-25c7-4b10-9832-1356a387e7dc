package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ReasonsforFiveDto;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintNotCloseCaseReasonPO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintNotCloseCaseReasonTestPO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintNotCloseCaseReasonPO;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintNotCloseCaseReasonMapper;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintNotCloseCaseReasonService;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintNotCloseCaseReasonDTO;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 5日未结案原因 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
@Service
public class SaleComplaintNotCloseCaseReasonServiceImpl implements SaleComplaintNotCloseCaseReasonService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    SaleComplaintNotCloseCaseReasonMapper saleComplaintNotCloseCaseReasonMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                               分页对象
     * @param saleComplaintNotCloseCaseReasonDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintNotCloseCaseReasonDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<SaleComplaintNotCloseCaseReasonDTO> selectPageBysql(Page page, SaleComplaintNotCloseCaseReasonDTO saleComplaintNotCloseCaseReasonDTO) {
        if (saleComplaintNotCloseCaseReasonDTO == null) {
            saleComplaintNotCloseCaseReasonDTO = new SaleComplaintNotCloseCaseReasonDTO();
        }
        SaleComplaintNotCloseCaseReasonPO saleComplaintNotCloseCaseReasonPO = saleComplaintNotCloseCaseReasonDTO.transDtoToPo(SaleComplaintNotCloseCaseReasonPO.class);

        List<SaleComplaintNotCloseCaseReasonPO> list = saleComplaintNotCloseCaseReasonMapper.selectPageBySql(page, saleComplaintNotCloseCaseReasonPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<SaleComplaintNotCloseCaseReasonDTO> result = list.stream().map(m -> m.transPoToDto(SaleComplaintNotCloseCaseReasonDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param saleComplaintNotCloseCaseReasonDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintNotCloseCaseReasonDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<SaleComplaintNotCloseCaseReasonDTO> selectListBySql(SaleComplaintNotCloseCaseReasonDTO saleComplaintNotCloseCaseReasonDTO) {
        if (saleComplaintNotCloseCaseReasonDTO == null) {
            saleComplaintNotCloseCaseReasonDTO = new SaleComplaintNotCloseCaseReasonDTO();
        }
        SaleComplaintNotCloseCaseReasonPO saleComplaintNotCloseCaseReasonPO = saleComplaintNotCloseCaseReasonDTO.transDtoToPo(SaleComplaintNotCloseCaseReasonPO.class);
        List<SaleComplaintNotCloseCaseReasonPO> list = saleComplaintNotCloseCaseReasonMapper.selectListBySql(saleComplaintNotCloseCaseReasonPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(SaleComplaintNotCloseCaseReasonDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintNotCloseCaseReasonDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public SaleComplaintNotCloseCaseReasonDTO getById(Long id) {
        SaleComplaintNotCloseCaseReasonPO saleComplaintNotCloseCaseReasonPO = saleComplaintNotCloseCaseReasonMapper.selectById(id);
        if (saleComplaintNotCloseCaseReasonPO != null) {
            return saleComplaintNotCloseCaseReasonPO.transPoToDto(SaleComplaintNotCloseCaseReasonDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param saleComplaintNotCloseCaseReasonDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(SaleComplaintNotCloseCaseReasonDTO saleComplaintNotCloseCaseReasonDTO) {
        //对对象进行赋值操作
        SaleComplaintNotCloseCaseReasonPO saleComplaintNotCloseCaseReasonPO = saleComplaintNotCloseCaseReasonDTO.transDtoToPo(SaleComplaintNotCloseCaseReasonPO.class);
        //执行插入
        int row = saleComplaintNotCloseCaseReasonMapper.insert(saleComplaintNotCloseCaseReasonPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                                 主键ID
     * @param saleComplaintNotCloseCaseReasonDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, SaleComplaintNotCloseCaseReasonDTO saleComplaintNotCloseCaseReasonDTO) {
        SaleComplaintNotCloseCaseReasonPO saleComplaintNotCloseCaseReasonPO = saleComplaintNotCloseCaseReasonMapper.selectById(id);
        //对对象进行赋值操作
        saleComplaintNotCloseCaseReasonDTO.transDtoToPo(saleComplaintNotCloseCaseReasonPO);
        //执行更新
        int row = saleComplaintNotCloseCaseReasonMapper.updateById(saleComplaintNotCloseCaseReasonPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = saleComplaintNotCloseCaseReasonMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = saleComplaintNotCloseCaseReasonMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    @Override
    public IPage<ComplaintNotCloseCaseReasonTestDTO> selectPageBysql3(Page page, ComplaintNotCloseCaseReasonTestDTO complaintNotCloseCaseReasonTestDTO) {
        if (complaintNotCloseCaseReasonTestDTO == null) {
            complaintNotCloseCaseReasonTestDTO = new ComplaintNotCloseCaseReasonTestDTO();
        }
        ComplaintNotCloseCaseReasonTestPO complaintNotCloseCaseReasonTestPo = complaintNotCloseCaseReasonTestDTO.transDtoToPo(ComplaintNotCloseCaseReasonTestPO.class);

        List<ComplaintNotCloseCaseReasonTestPO> list = saleComplaintNotCloseCaseReasonMapper.selectPageBySql2(page, complaintNotCloseCaseReasonTestPo);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<ComplaintNotCloseCaseReasonTestDTO> result = list.stream().map(m -> m.transPoToDto(ComplaintNotCloseCaseReasonTestDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    @Override
    public int insertReasonsforFive(ReasonsforFiveDto reasonsforFiveDto) {
        SaleComplaintNotCloseCaseReasonDTO saleComplaintNotCloseCaseReasonDTO = new SaleComplaintNotCloseCaseReasonDTO();
        Long id = reasonsforFiveDto.getComplaintInfoId();
        int resulttime = 0;
        List<SaleComplaintNotCloseCaseReasonPO> followTimeList = saleComplaintNotCloseCaseReasonMapper.selectfollowTime(id);
        if (followTimeList.size() != 0) {
            SaleComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasontestDTO = new SaleComplaintNotCloseCaseReasonDTO();
            Date lastFollowTime = followTimeList.get(0).getFollowTime();
            long id1 = followTimeList.get(0).getId();
            Calendar aCalendar = Calendar.getInstance();
            aCalendar.setTime(lastFollowTime);
            int day1 = aCalendar.get(Calendar.DAY_OF_YEAR);
            aCalendar.setTime(new Date());
            int day2 = aCalendar.get(Calendar.DAY_OF_YEAR);
            resulttime = day2 - day1;
            complaintNotCloseCaseReasontestDTO.setDuration(resulttime);
            update(id1, complaintNotCloseCaseReasontestDTO);
        }
        int row = 0;
        String claseCode = "";
        String claseName = "";
        saleComplaintNotCloseCaseReasonDTO.setComplaintInfoId(id);

        if (reasonsforFiveDto.getReason().getCustomerReason1() != null && reasonsforFiveDto.getReason().getCustomerReason1() == true) {
            claseCode += "83661001,";
            claseName += "正在与客户沟通处理中,";
        }
        if (reasonsforFiveDto.getReason().getCustomerReason2() != null && reasonsforFiveDto.getReason().getCustomerReason2() == true) {
            claseCode += "83661002,";
            claseName += "客户不同意经销商给到的处理方案,";
        }
        if (reasonsforFiveDto.getReason().getCustomerReason3() != null && reasonsforFiveDto.getReason().getCustomerReason3() == true) {
            claseCode += "83661003,";
            claseName += "客户已同意方案,走流程需时间(退款流程或重新订车),";
        }
        if (reasonsforFiveDto.getReason().getCustomerReason4() != null && reasonsforFiveDto.getReason().getCustomerReason4() == true) {
            claseCode += "83661004,";
            claseName += "客户期待值过高,";
        }
        if (reasonsforFiveDto.getReason().getCustomerReason5() != null && reasonsforFiveDto.getReason().getCustomerReason5() == true) {
            claseCode += "83661005,";
            claseName += "客户暂时联系不到,";
        }
        if (reasonsforFiveDto.getReason().getCustomerReason6() != null && reasonsforFiveDto.getReason().getCustomerReason6() == true) {
            saleComplaintNotCloseCaseReasonDTO.setOther(reasonsforFiveDto.getReason().getInput1());
        } else {
            saleComplaintNotCloseCaseReasonDTO.setOther("");
        }
        saleComplaintNotCloseCaseReasonDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
        saleComplaintNotCloseCaseReasonDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
        saleComplaintNotCloseCaseReasonDTO.setFollowTime(new Date());
        saleComplaintNotCloseCaseReasonDTO.setClassCode(claseCode);
        saleComplaintNotCloseCaseReasonDTO.setClassName(claseName);
        saleComplaintNotCloseCaseReasonDTO.setDuration(0);
        return insert(saleComplaintNotCloseCaseReasonDTO);
    }
}
