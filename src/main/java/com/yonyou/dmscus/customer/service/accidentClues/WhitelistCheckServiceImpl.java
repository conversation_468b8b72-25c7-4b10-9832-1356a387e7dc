package com.yonyou.dmscus.customer.service.accidentClues;

import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.RedisConstants;
import com.yonyou.dmscus.customer.feign.WhitelistQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2023/11/28 11:07
 * @Version 1.0
 */
@Service
@Slf4j
public class WhitelistCheckServiceImpl implements WhitelistCheckService{

    @Resource
    private WhitelistQueryService whitelistQueryService;
    @Resource
    private RedisTemplate redisTemplate;

    @Value("${accidentClue.whitelist.modType:}")
    private Integer modType;

    @Override
    public Boolean checkWhitelist() throws ServiceBizException {

        if (ObjectUtils.isEmpty(FrameworkUtil.getLoginInfo())){
            return false;
        }

        String dealerCode = FrameworkUtil.getLoginInfo().getOwnerCode();
        Boolean result = this.findIsWhitelistByCache(dealerCode);
        if (ObjectUtils.isEmpty(result)){
            result = whitelistQueryService.checkWhitelist(dealerCode, modType, 0, "");
            log.info("白名单获取结果===>>{}", result);
            this.cacheWhitelist(dealerCode, result);
        }

        return result;
    }

    @Override
    public Boolean checkWhitelist(String dealerCode) throws ServiceBizException {

        Boolean result = this.findIsWhitelistByCache(dealerCode);
        if (ObjectUtils.isEmpty(result)){
            result = whitelistQueryService.checkWhitelist(dealerCode, modType, 0, "");
            log.info("白名单获取结果===>>{}", result);
            this.cacheWhitelist(dealerCode, result);
        }

        return result;
    }

    /**
     * 获取缓存的白名单结果
     * @param dealerCode
     * @return
     * @throws ServiceBizException
     */
    private Boolean findIsWhitelistByCache(String dealerCode) throws ServiceBizException{

        Object redisObj = redisTemplate.opsForValue().get(MessageFormat.format(RedisConstants.KEY_ACCIDENT_CLUE_WHITELIST, dealerCode));
        if (ObjectUtils.isEmpty(redisObj)){
            return null;
        }
        log.info("缓存获取白名单结果===>>{}", redisObj);

        return Boolean.valueOf(redisObj.toString());
    }

    /**
     * 缓存白名单结果
     * @param dealerCode
     * @param isWhiteList
     * @throws ServiceBizException
     */
    private void cacheWhitelist(String dealerCode, Boolean isWhiteList) throws ServiceBizException{

        redisTemplate.opsForValue().set(MessageFormat.format(RedisConstants.KEY_ACCIDENT_CLUE_WHITELIST, dealerCode),
                isWhiteList, RedisConstants.EXPIRE_ACCIDENT_CLUE_WHITELIST, TimeUnit.MINUTES);
    }
}
