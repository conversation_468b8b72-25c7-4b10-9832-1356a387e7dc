package com.yonyou.dmscus.customer.service.impl.qb;

import java.lang.reflect.Constructor;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.dao.qb.InviteQBVehicleImportMapper;
import com.yonyou.dmscus.customer.dto.QBVehicleImportDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskImportPO;
import com.yonyou.dmscus.customer.entity.po.qb.InviteQBVehicleImportPO;
import com.yonyou.dmscus.customer.entity.po.qb.QbVehicleInfoPO;
import com.yonyou.dmscus.customer.middleInterface.ResponseDTO;
import com.yonyou.dmscus.customer.service.common.IMiddleGroundVehicleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.exception.UtilException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscloud.function.utils.common.StringUtils;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordDetailMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dao.invitationautocreate.InviteVehicleTaskMapper;
import com.yonyou.dmscus.customer.dao.inviteSaAllocateRule.InviteSaAllocateRuleDetailMapper;
import com.yonyou.dmscus.customer.dao.inviteSaAllocateRule.InviteSaAllocateRuleMapper;
import com.yonyou.dmscus.customer.dao.qb.QbVehicleInfoMapper;
import com.yonyou.dmscus.customer.dto.RepairOrderVO;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.UserInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteRule.InviteDuplicateRemovalRuleDTO;
import com.yonyou.dmscus.customer.entity.dto.qb.QbVehicleInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.saSllocateDlr.SaSllocateDlrDto;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordDetailPO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO;
import com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule.InviteSaAllocateRuleDetailPO;
import com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule.InviteSaAllocateRulePO;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.inviteRule.InviteDuplicateRemovalRuleService;
import com.yonyou.dmscus.customer.service.qb.QbVehicleInfoService;
import com.yonyou.dmscus.customer.util.common.Utility;
import org.springframework.web.multipart.MultipartFile;
import com.google.common.collect.Sets;
import com.yonyou.dmscloud.framework.service.excel.ExcelRead;
import com.yonyou.dmscloud.framework.service.excel.ExcelReadCallBack;
import com.yonyou.dmscloud.framework.service.excel.impl.AbstractExcelReadCallBack;
import org.apache.commons.collections.CollectionUtils;
import com.alibaba.fastjson.JSON;

/**
 * <AUTHOR>
 * @date 2020/11/10 0010
 */
@Service
public class QbVehicleInfoServiceImpl implements QbVehicleInfoService {

    Logger logger= LoggerFactory.getLogger(QbVehicleInfoServiceImpl.class);

    @Resource
    QbVehicleInfoMapper  qbVehicleInfoMapper;

    @Resource
    ReportCommonClient reportCommonClient;

    @Resource
    ExcelRead<QBVehicleImportDTO> excelReadService;

    @Resource
    IMiddleGroundVehicleService iMiddleGroundVehicleService;

    @Resource
    InviteQBVehicleImportMapper inviteQBVehicleImportMapper;

    @Resource
    InviteVehicleTaskMapper inviteVehicleTaskMapper;

    @Resource
    InviteVehicleRecordMapper inviteVehicleRecordMapper;

    @Resource
    InviteSaAllocateRuleMapper inviteSaAllocateRuleMapper;
    @Resource
    InviteSaAllocateRuleDetailMapper inviteSaAllocateRuleDetailMapper;

    @Resource
    InviteVehicleRecordService inviteVehicleRecordService;

    @Resource
    InviteDuplicateRemovalRuleService inviteDuplicateRemovalRuleService;

    @Resource
    InviteVehicleRecordDetailMapper inviteVehicleRecordDetailMapper;



    /**
     * 分页查询
     * @param page
     * @param dto
     * @return
     */
    @Override
    public IPage<QbVehicleInfoDTO> selectPageBysql(Page page, QbVehicleInfoDTO dto) {
        List<QbVehicleInfoDTO> list=qbVehicleInfoMapper.selectPageBySql(page,dto);
        page.setRecords(list);
        return page;
    }

    /**
     * 根据查询数据生成邀约
     * @return
     */
    @Override
    public int createdInviteVehicle(QbVehicleInfoDTO dto) {
//        logger.info("qb线索保存开始=========");
//        //查询去重规则
//        Integer type2 = null;
//        List<InviteDuplicateRemovalRuleDTO> list2 = inviteDuplicateRemovalRuleService.selectListBySql(new
//                InviteDuplicateRemovalRuleDTO());
//        for (InviteDuplicateRemovalRuleDTO d : list2) {
//            if (d.getRuleType() == 2) {
//                type2 = d.getRule();
//            }
//        }

        List<VehicleOwnerVO> list=reportCommonClient.queryQbListData(dto.getQbNumber(),dto.getIsPerformed(),dto.getIsClosed(),dto.getVin(),dto.getDealerCode(),null);
        return save(list);
        //        if(!CommonUtils.isNullOrEmpty(list) && list.size()>0){
//            for (VehicleOwnerVO vo : list) {
//                vo.setInviteType(CommonConstants.INVITE_TYPE_VII);
//                vo.setAdviseInDate(new Date());
//                //计划跟进时间+1天
//                vo.setDayInAdvance(-1);
//                this.createInviteTask(vo,CommonConstants.UP_DAY,type2);
//                RepairOrderVO order = reportCommonClient.getRepairOrderLastOne(vo.getVin());
//                if (order != null && !order.getDealerCode().equals(vo.getDealerCode())) {
//                    vo.setDealerCode(order.getDealerCode());
//                    this.createInviteTask(vo,CommonConstants.UP_DAY,type2);
//                }
//            }
//            //定首保及其它类型 自动分配
//            this.taskAllocation(Utility.getDate(), false);
//        }else{
//            return 0;
//        }
//        logger.info("qb线索保存结束=========");
//        return 1;
    }

    /**
     * 创建邀约任务
     *
     * @param vo
     */
    private void createInviteTask(VehicleOwnerVO vo,Integer days,Integer type2) {
        InviteVehicleTaskPO po = new InviteVehicleTaskPO();
        po.setVin(vo.getVin());
        po.setLicensePlateNum(vo.getPlateNumber());
        po.setDealerCode(vo.getDealerCode());
        po.setName(vo.getName());
        po.setTel(vo.getMobile());
        po.setAge(vo.getAge());
        po.setSex(vo.getSex());
        po.setModel(vo.getModelId());
        po.setDailyMileage(vo.getDailyAverageMileage());
        po.setInviteType(vo.getInviteType());
        po.setAdviseInDate(vo.getAdviseInDate());
        //po.setDayInAdvance(vo.getDayInAdvance());
        po.setRemindInterval(vo.getRemindInterval());
        po.setCloseInterval(vo.getCloseInterval());
        po.setIsCreateInvite(0);
        po.setInviteTime(vo.getLastMaintainDate());
        Calendar c = Calendar.getInstance();
        c.setTime(po.getAdviseInDate());
        c.add(Calendar.DATE, days);
        po.setCreateInviteTime(c.getTime());
        po.setItemType(vo.getItemType());
        po.setItemCode(vo.getItemCode());
        po.setItemName(vo.getItemName());
        po.setAdviseInMileage(vo.getAdviseInMileage());
        po.setQbNumber(vo.getQbNumber());

        //邀约记录
        Long invite = this.createInviteByTask(po,type2);

        po.setIsCreateInvite(1);
        po.setInviteId(invite);
        inviteVehicleTaskMapper.insert(po);
    }

    public Long createInviteByTask(InviteVehicleTaskPO po,Integer mergeRule) {
        InviteVehicleRecordPO record = new InviteVehicleRecordPO();
        record.setDealerCode(po.getDealerCode());
        //VCDC下发邀约
        record.setSourceType(1);
        record.setIsMain(1);
        record.setVin(po.getVin());
        record.setLicensePlateNum(po.getLicensePlateNum());
        record.setName(po.getName());
        record.setTel(po.getTel());
        record.setAge(po.getAge());
        record.setSex(po.getSex());
        record.setModel(po.getModel());
        record.setInviteType(po.getInviteType());
        record.setAdviseInDate(po.getAdviseInDate());
        record.setItemType(po.getItemType());
        record.setItemCode(po.getItemCode());
        record.setItemName(po.getItemName());
        record.setLastChangeDate(po.getInviteTime());
        //未完成
        record.setOrderStatus(82411002);
        record.setFollowStatus(82401001);
        record.setCreatedAt(new Date());
        inviteVehicleRecordMapper.insert(record);

        List<InviteVehicleRecordPO> records = this.queryInviteRecordForTask(po.getDealerCode(), po.getAdviseInDate(), po
                .getVin(), mergeRule, record.getId());
        this.mergeInvite(records, record);

        return record.getId();
    }

    /**
     * 自动分配sa
     */
    private void taskAllocation(String createDate, Boolean isInsurance) {
        HashMap<String, List<InviteVehicleRecordDTO>> map = new HashMap<>();
        List<InviteVehicleRecordPO> list = null;
        if (isInsurance) {
            list = inviteVehicleRecordMapper.queryWaitAllocationRecodeForInsurance(createDate);
        } else {
            list = inviteVehicleRecordMapper.queryWaitAllocationRecodeForOther(createDate);
        }
        if (!CommonUtils.isNullOrEmpty(list)) {
            List<InviteVehicleRecordDTO> result = list.stream().map(m -> m.transPoToDto(InviteVehicleRecordDTO.class))
                    .collect(Collectors.toList());
            for (InviteVehicleRecordDTO dto : result) {
                if (map.containsKey(dto.getDealerCode())) {
                    map.get(dto.getDealerCode()).add(dto);
                } else {
                    List<InviteVehicleRecordDTO> array = new ArrayList<>();
                    array.add(dto);
                    map.put(dto.getDealerCode(), array);
                }
            }

            for (Map.Entry<String, List<InviteVehicleRecordDTO>> entry : map.entrySet()) {
                this.allocation(entry.getKey(), entry.getValue(), isInsurance);
            }
        }
    }

    /**
     * 分配sa
     *
     * @param list
     */
    private void allocation(String dealerCode, List<InviteVehicleRecordDTO> list, Boolean isInsurance) {
        LambdaQueryWrapper<InviteSaAllocateRulePO> qu = new QueryWrapper().lambda();
        qu.eq(InviteSaAllocateRulePO::getDealerCode, dealerCode);
        InviteSaAllocateRulePO po = inviteSaAllocateRuleMapper.selectOne(qu);
        //没有分配规则， 不自动分配
        if (po == null) {
            return;
        }
        LambdaQueryWrapper<InviteSaAllocateRuleDetailPO> qde = new QueryWrapper().lambda();
        qde.eq(InviteSaAllocateRuleDetailPO::getDealerCode, dealerCode);
        if (isInsurance) {
            qde.eq(InviteSaAllocateRuleDetailPO::getIsInsurance, 10041001);
        } else {
            qde.eq(InviteSaAllocateRuleDetailPO::getIsOther, 10041001);
        }
        List<InviteSaAllocateRuleDetailPO> users = inviteSaAllocateRuleDetailMapper.selectList(qde);
        //没有可分配sa， 不自动分配
        if (users == null || users.size() == 0) {
            return;
        }
        List<UserInfoDTO> selectSa = new ArrayList<>();
        for (InviteSaAllocateRuleDetailPO user : users) {
            UserInfoDTO u = new UserInfoDTO();
            u.setId(Long.parseLong(user.getSaId()));
            u.setUsername(user.getSaName());
            selectSa.add(u);
        }
        SaSllocateDlrDto saSllocateDlrDto = new SaSllocateDlrDto();
        saSllocateDlrDto.setRuleType(po.getRuleType());
        saSllocateDlrDto.setInviteVehicleRecordList(list);
        saSllocateDlrDto.setSelectSa(selectSa);
        inviteVehicleRecordService.saveSaSllocate(saSllocateDlrDto);
    }


    /**
     * 查询未完成的邀约线索
     *
     * @param vin
     * @param mergeRule
     * @return
     */
    private List<InviteVehicleRecordPO> queryInviteRecordForTask(String dealerCode, Date adviseInDate, String vin,
                                                                 Integer mergeRule, Long id) {
        return inviteVehicleRecordMapper.queryInviteRecordForTask(dealerCode, adviseInDate, vin, mergeRule, id);
    }

    /**
     * 合并线索
     *
     * @param records
     */
    private void mergeInvite(List<InviteVehicleRecordPO> records, InviteVehicleRecordPO newpo) {
        //只有一条不合并
        if (records.size() == 0) {
            return;
        }
        //如果是首保 定保 ，做为主线索
        if (newpo.getInviteType().equals(CommonConstants.INVITE_TYPE_I) ||
                newpo.getInviteType().equals(CommonConstants.INVITE_TYPE_II)) {
            for (InviteVehicleRecordPO po : records) {
                po.setParentId(newpo.getId());
                po.setIsMain(0);
                //更新sa
                newpo.setSaId(po.getSaId());
                newpo.setSaName(po.getSaName());
                inviteVehicleRecordMapper.updateById(newpo);
                //更新可能存在的子线索
                LambdaUpdateWrapper<InviteVehicleRecordPO> upw = new UpdateWrapper().lambda();
                upw.eq(InviteVehicleRecordPO::getParentId, po.getId());
                InviteVehicleRecordPO uprecord = new InviteVehicleRecordPO();
                uprecord.setParentId(newpo.getId());
                inviteVehicleRecordMapper.update(uprecord, upw);
                //更新可能存在的跟进记录
                LambdaUpdateWrapper<InviteVehicleRecordDetailPO> updateWrapper = new UpdateWrapper().lambda();
                updateWrapper.eq(InviteVehicleRecordDetailPO::getInviteId, po.getId());
                InviteVehicleRecordDetailPO up = new InviteVehicleRecordDetailPO();
                up.setInviteId(newpo.getId());
                inviteVehicleRecordDetailMapper.update(up, updateWrapper);
                inviteVehicleRecordMapper.updateById(po);
                return;
            }
            //其他类型,作为子线索
        } else {
            for (InviteVehicleRecordPO po : records) {
                newpo.setIsMain(0);
                newpo.setParentId(po.getId());
                inviteVehicleRecordMapper.updateById(newpo);
                break;
            }
        }

    }

    /**
           * 创建线索
     */
    @Override
	public int createQb(String listString) {
    	logger.info("QbIds====>"+listString);
        List<Long> ids = convertStrToArray(listString,",", Long.class);
        List<VehicleOwnerVO> list=reportCommonClient.queryQbListData(null,null,null,null,null,ids);
        return save(list);
       
	}
    
    /**
     * 保存线索
     * @param list
     * @return
     */
    private int save(List<VehicleOwnerVO> list) {
    	logger.info("qb线索保存开始=========");
    	Integer type2 = null;
        List<InviteDuplicateRemovalRuleDTO> list2 = inviteDuplicateRemovalRuleService.selectListBySql(new
                InviteDuplicateRemovalRuleDTO());
        for (InviteDuplicateRemovalRuleDTO d : list2) {
            if (d.getRuleType() == 2) {
                type2 = d.getRule();
            }
        }
        if(!CommonUtils.isNullOrEmpty(list) && list.size()>0){
            for (VehicleOwnerVO vo : list) {
                vo.setInviteType(CommonConstants.INVITE_TYPE_VII);
                vo.setAdviseInDate(new Date());
                //计划跟进时间+1天
                vo.setDayInAdvance(-1);
                this.createInviteTask(vo,CommonConstants.UP_DAY,type2);

                //逻辑删除 只发给责任经销商
//                RepairOrderVO order = reportCommonClient.getRepairOrderLastOne(vo.getVin());
//                if (order != null && !order.getDealerCode().equals(vo.getDealerCode())) {
//                    vo.setDealerCode(order.getDealerCode());
//                    this.createInviteTask(vo,CommonConstants.UP_DAY,type2);
//                }
            }
            //定首保及其它类型 自动分配
            this.taskAllocation(Utility.getDate(), false);
        }else{
            return 0;
        }
        logger.info("qb线索保存结束=========");
        return 1;
    }
    
    /**
     * 根据分隔符，将字符串转化为对应的数组
     *
     * @param str
     * @param split
     * @return java.lang.Long[]
     * @throws
     * <AUTHOR>
     * @since 2018/7/24 0024
     */
    public static <T> List<T> convertStrToArray(String str, String split, Class<T> returnClass) {
        if (StringUtils.isNullOrEmpty(str)) {
            return Collections.emptyList();
        }
        try {
            //转化为数组
            String[] splitArray = str.split(split);
            List<T> returnList = new ArrayList<>();
            for (int i = 0; i < splitArray.length; i++) {
                Constructor<T> constructor = returnClass.getConstructor(String.class);
                T instance = constructor.newInstance(splitArray[i]);
                returnList.add(instance);
            }
            return returnList;
        } catch (Exception e) {
            throw new UtilException(e.getMessage(), e);
        }

    }

    @Override
    public IPage<InviteQBVehicleImportPO> selectErrorPage(Page page) {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if(StringUtils.isNullOrEmpty(loginInfoDto.getUserId())){
            throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
        }
        List<InviteQBVehicleImportPO>  list=inviteQBVehicleImportMapper.selectErrorPage(page,loginInfoDto.getUserId());
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {

            page.setRecords(list);
            return page;
        }
    }
    @Override
    public ImportTempResult<InviteQBVehicleImportPO> importQBTemp(MultipartFile importFile) throws Exception {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        List<InviteQBVehicleImportPO> addList=new ArrayList<InviteQBVehicleImportPO>();

        Collection<String> vinList=Sets.newHashSet();
        Collection<String> dealerList=Sets.newHashSet();
        //删除历史数据
        inviteQBVehicleImportMapper.deleteByCreatedBy(""+loginInfoDto.getUserId());

        excelReadService.analyzeExcelFirstSheet(importFile, new AbstractExcelReadCallBack<QBVehicleImportDTO>(
                QBVehicleImportDTO.class, new ExcelReadCallBack<QBVehicleImportDTO>() {
            private Integer seq = 1;
            @Override
            public void readRowCallBack(QBVehicleImportDTO dto, boolean b) {
                InviteQBVehicleImportPO po=new InviteQBVehicleImportPO();
                //校验excel数据
                if(validationVCDCData(dto,po)) {
                    po.setVin(dto.getVin());
                    po.setDealerCode(dto.getDealerCode());
                    po.setQbNumber(dto.getQbNumber());
                    po.setIsError(0);
                    vinList.add(dto.getVin());
                    dealerList.add(dto.getDealerCode());
                }
                po.setLineNumber(++seq);
                addList.add(po);

            }
        }));
        if (!CommonUtils.isNullOrEmpty(addList)) {
            int listSize = addList.size();
            int toIndex = 100;
            for (int i = 0; i < addList.size(); i += toIndex) {
                if (i + toIndex > listSize) {        //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
                    toIndex = listSize - i;
                }
                List<InviteQBVehicleImportPO> insertList = addList.subList(i, i + toIndex);
                inviteQBVehicleImportMapper.bulkInsert(insertList, loginInfoDto.getUserId());
            }
        }
        return this.checkVCDCTmpData(vinList,dealerList);
    }

    /**
     * 校验导入excel数据
     * @param dto
     * @param po
     * @return
     */
    private boolean validationVCDCData(QBVehicleImportDTO dto,InviteQBVehicleImportPO po) {
        boolean isOk=true;
        if(StringUtils.isNullOrEmpty(dto.getDealerCode())) {
            po.setErrorMsg("经销商代码不能为空");
            po.setIsError(1);
            isOk=false;
        }
        if(StringUtils.isNullOrEmpty(dto.getVin()) || dto.getVin().length()>17) {
            po.setErrorMsg("VIN不能为空并且长度不能超过17");
            po.setIsError(1);
            isOk=false;
        }
        if(StringUtils.isNullOrEmpty(dto.getQbNumber())) {
            po.setErrorMsg("QB号不能为空");
            po.setIsError(1);
            isOk=false;
        }
        return isOk;
    }

    private ImportTempResult<InviteQBVehicleImportPO> checkVCDCTmpData(Collection<String> vinList, Collection<String> dealerList) {
        ImportTempResult<InviteQBVehicleImportPO> importResult = new ImportTempResult<InviteQBVehicleImportPO>();
        // 获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //中台验证车架号的数据准备
        if(!CommonUtils.isNullOrEmpty(vinList)) {
            logger.info("===调用中台地址查询VIN是否存在（开始）====");
            Map<String, Object> paramMap = new HashMap<String,Object>();
            paramMap.put("vinList", vinList);
            ResponseDTO<Map<String, Object>> response=iMiddleGroundVehicleService.getVinListCheckInfo(paramMap);
            Map<String,Object> notExistVinMap = response.getData();
            if(notExistVinMap != null && CollectionUtils.isNotEmpty(notExistVinMap.keySet())) {
                logger.info("返回1："+JSON.toJSONString(response));
                Map<String,Object> result= response.getData();
                List<String> list=new ArrayList<String>();
                if(!CommonUtils.isNullOrEmpty(result) && result.size()>0) {
                    list=getKeyListBySet(result.keySet());
                }
                if(!CommonUtils.isNullOrEmpty(list) && list.size() > 0) {
                    inviteQBVehicleImportMapper.updateErrorById(loginInfoDto.getUserId(),list);
                }
//            	if(notExistVinMap.get("notExistVinList")!=null) {
//            		logger.info("返回2："+JSON.toJSONString(notExistVinMap.get("notExistVinList")));
//            		List<Long> list=objToList(notExistVinMap.get("notExistVinList"));
//
//            	};

            }

            logger.info("===调用中台地址查询VIN是否存在（结束）====");
        }
        //查询错误项
        List<InviteQBVehicleImportPO> listError = inviteQBVehicleImportMapper.queryError(loginInfoDto.getUserId());
        if (!CommonUtils.isNullOrEmpty(listError)) {
            importResult.setErrorList(listError);
        } else {
            logger.info("未查到错误数据！");
        }
        // 查询成功项
        List<InviteQBVehicleImportPO> listSuccess = inviteQBVehicleImportMapper.querySuccess(loginInfoDto.getUserId());
        if (!CommonUtils.isNullOrEmpty(listSuccess)) {
            importResult.setSuccessList(listSuccess);
        } else {
            //inviteVehicleDealerTaskImportMapper.deleteByCreatedBy(loginInfoDto.getUserId()+"");
            logger.info("未查到正确数据！");
        }
        // 查询正确数据数
        importResult.setSuccessCount(inviteQBVehicleImportMapper.querySucessCount(loginInfoDto.getUserId()));
        return importResult;
    }

    /**
     * 根据key的set返回key的list
     *
     * @param set
     * @return
     */
    public  List<String> getKeyListBySet(Set<String> set) {
        List<String> keyList = new ArrayList<String>();
        keyList.addAll(set);
        return keyList;
    }

    @Override
    public int batchInsert() {
        int ret = 0;
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //主单

        List<Long> ids = inviteQBVehicleImportMapper.selectMatchID(loginInfoDto.getUserId());
        if(!CommonUtils.isNullOrEmpty(ids) && ids.size() > 0)
        {
            ret = ids.size();
            List<VehicleOwnerVO> list=reportCommonClient.queryQbListDataPost(ids);
            save(list);
        }


        //删除历史数据
        inviteQBVehicleImportMapper.deleteByCreatedBy(""+loginInfoDto.getUserId());

        return ret;
    }
}
