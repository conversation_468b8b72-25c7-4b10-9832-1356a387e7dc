package com.yonyou.dmscus.customer.service.complaint.sale;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoEtlDTO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 销售客户投诉信息表-etl 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-22
 */
public interface SaleComplaintInfoEtlService  {
    IPage<SaleComplaintInfoEtlDTO> selectPageBysql(Page page, SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO);
    List<SaleComplaintInfoEtlDTO> selectListBySql(SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO);
    SaleComplaintInfoEtlDTO getById(Long id);
    int insert(SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO);
    int update(Long id, SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO);
    int deleteById(Long id);

    List<Map> exportSaleComplaintHistory(SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO);
}
