package com.yonyou.dmscus.customer.service.impl.invitationFollow;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import static com.yonyou.dmscus.customer.constants.CommonConstants.UNDERLINE;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.framework.util.bean.ApplicationContextHelper;
import com.yonyou.dmscloud.framework.util.redis.RedisClient;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.constants.RedisEnum;
import com.yonyou.dmscus.customer.constants.clue.LeadsTypeEnum;
import com.yonyou.dmscus.customer.constants.faultLight.FaultGenderEnum;
import com.yonyou.dmscus.customer.constants.lnvitation.*;
import com.yonyou.dmscus.customer.dao.busSetting.SetMainFileMapper;
import com.yonyou.dmscus.customer.dao.common.VehicleMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleSaRefMapper;
import com.yonyou.dmscus.customer.dao.invitationautocreate.InviteVehicleTaskMapper;
import com.yonyou.dmscus.customer.dao.inviteRule.InvitePartItemRuleMapper;
import com.yonyou.dmscus.customer.dao.voc.VocInviteVehicleTaskRecordMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.CallDetailsMapper;
import com.yonyou.dmscus.customer.dao.voc.CdpTagTaskMapper;
import com.yonyou.dmscus.customer.dto.BookingCreateParamsVo;
import com.yonyou.dmscus.customer.dto.CheckRepairOrderDTO;
import com.yonyou.dmscus.customer.dto.CustomerInfoListReturnDTO;
import com.yonyou.dmscus.customer.dto.OnlineOfflineDTO;
import com.yonyou.dmscus.customer.dto.OnlineOfflineResultDTO;
import com.yonyou.dmscus.customer.dto.RepairOrderVO;
import com.yonyou.dmscus.customer.dto.VehicleInviteVO;
import com.yonyou.dmscus.customer.dto.cdp.CdpTagTaskParameterDto;
import com.yonyou.dmscus.customer.enevt.LeadStatusChangeEvent;
import com.yonyou.dmscus.customer.entity.dto.busSetting.SetMainFileDetailVO;
import com.yonyou.dmscus.customer.entity.dto.busSetting.SetMainFileParamsVO;
import com.yonyou.dmscus.customer.entity.dto.busSetting.SetMainFileVO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.InviteVulDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.TmVehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.UserInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.VehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.CarInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.ClueDataDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.ContactInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.DealerInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.InviteClueParamDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.InviteClueResultDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.LiteCrmClueDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.LiteCrmClueResultDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.MaintenanceLightInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightTopicDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.RecommendationDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InsuranceBillDTO;
import com.yonyou.dmscus.customer.entity.dto.saSllocateDlr.SaSllocateDlrDto;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillDTO;
import com.yonyou.dmscus.customer.entity.po.common.VehiclePO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.ClueCompleteLogPo;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleSaRefPO;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InvitePartItemRulePO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocInviteVehicleTaskRecordPo;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.CdpTagTaskPo;
import com.yonyou.dmscus.customer.entity.vo.InviteVehicleRecordVo;
import com.yonyou.dmscus.customer.enums.CacheKeyEnum;
import com.yonyou.dmscus.customer.enums.PowerTypeEnum;
import com.yonyou.dmscus.customer.feign.*;
import com.yonyou.dmscus.customer.feign.dto.CommonConfigDTO;
import com.yonyou.dmscus.customer.feign.MidEndCustomerCenterClient;
import com.yonyou.dmscus.customer.feign.RepairCommonClient;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.feign.WhitelistQueryService;
import com.yonyou.dmscus.customer.feign.vo.ClueParamVO;
import com.yonyou.dmscus.customer.feign.vo.OwnerInfoResultsVo;
import com.yonyou.dmscus.customer.service.ExternalAPIService;
import com.yonyou.dmscus.customer.service.cdpTagTask.qb.CdpTagTaskService;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.common.businessPlatform.dto.VehicleOwnerDetailByIdListDTO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.dto.VehicleOwnerSelectByIdListDTO;
import com.yonyou.dmscus.customer.service.common.ownervehicle.VehicleService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordDetailService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.talkskill.TalkskillService;
import com.yonyou.dmscus.customer.service.invitationautocreate.ClueCompleteLogService;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocInviteVehicleTaskRecordService;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.dmscus.customer.util.common.Utility;
import com.yonyou.dmscus.customer.utils.RedisLockUtil.DistributedLockUtil;
import com.yonyou.dmscus.customer.utils.RedisLockUtil.IDistributedLock;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;


/**
 * <p>
 * 车辆邀约记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
@Service
public class InviteVehicleRecordServiceImpl implements InviteVehicleRecordService {
    private static final int MONTH_1 = 1;
    private static final int MONTH_3 = 3;
    private static final String FUNCTION_GROUP_2223 = "2223";
    private static final String FUNCTION_GROUP_8724 = "8724";
    private static final String RESPONSE_SUCCESS = "200";
    private static final String RESPONSE_FAIL = "500";
    private static final String POWER_TYPE_BEV = "电动";
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteVehicleRecordMapper inviteVehicleRecordMapper;
    @Resource
    SetMainFileMapper setMainFileMapper;
    @Resource
    InviteVehicleTaskMapper inviteVehicleTaskMapper;
    @Resource
    InvitePartItemRuleMapper invitePartItemRuleMapper;
    @Resource
    VehicleMapper vehicleMapper;
    @Resource
    CallDetailsMapper callDetailsMapper;
    @Resource
    InviteVehicleSaRefMapper inviteVehicleSaRefMapper;
    @Resource
    VocInviteVehicleTaskRecordMapper vocInviteVehicleTaskRecordMapper;

    @Autowired
    TalkskillService talkskillService;
    @Autowired
    VehicleService vehicleService;
    @Resource
    InviteVehicleRecordDetailService inviteVehicleRecordDetailService;
    @Autowired
    BusinessPlatformService businessPlatformService;
    @Autowired
    VocInviteVehicleTaskRecordService taskRecordService;
    @Autowired
    RepairCommonClient repairCommonClient;
    @Autowired
    ReportCommonClient reportCommonClient;
    @Autowired
    WhitelistQueryService whitelistQueryService;
    @Autowired
    MidEndCustomerCenterClient midEndCustomerCenterClient;
    @Autowired
    ClueCompleteLogService clueCompleteLogService;
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    @Autowired
    private ExternalAPIService externalAPIService;
    @Autowired
    private RedisClient redisClient;
    @Autowired
    Executor asyncUpdateReturnIntentionLevel;
    @Autowired
    CdpTagTaskService cdpTagTaskService;
    @Resource
    CdpTagTaskMapper cdpTagTaskMapper;
    @Autowired
    private DmscloudServiceClient dmscloudServiceClient;


    /**
     * 分页查询对应数据
     *
     * @param page                   分页对象
     * @param inviteVehicleRecordDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto
            * . InviteVehicleRecordDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<InviteVehicleRecordDTO> selectPageBysql(Page page, InviteVehicleRecordDTO inviteVehicleRecordDTO) {

        if (inviteVehicleRecordDTO == null) {
            inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        }
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordDTO.transDtoToPo(InviteVehicleRecordPO.class);

        List<InviteVehicleRecordPO> list = inviteVehicleRecordMapper.selectPageBySql(page, inviteVehicleRecordPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteVehicleRecordDTO> result = list.stream().map(m -> m.transPoToDto(InviteVehicleRecordDTO.class)
            ).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 查询邀约线索
     *
     * @param page
     * @param inviteVehicleRecordDTO
     * @return
     */
    @Override
    public IPage<InviteVehicleRecordDTO> getInviteVehicleRecord(Page page,
                                                                InviteVehicleRecordDTO inviteVehicleRecordDTO) {
        logger.info("getInviteVehicleRecord");
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (inviteVehicleRecordDTO == null) {
            inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        }
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordDTO.transDtoToPo(InviteVehicleRecordPO.class);
        inviteVehicleRecordPO.setReturnIntentionLevel(inviteVehicleRecordDTO.getReturnIntentionLevel());
        inviteVehicleRecordPO.setDealerCode(loginInfoDto.getOwnerCode());
        if (inviteVehicleRecordDTO.getIsself() != null && inviteVehicleRecordDTO.getIsself() == 1) {
            inviteVehicleRecordPO.setSaId(String.valueOf(loginInfoDto.getUserId()));
        }
        if (inviteVehicleRecordDTO.getIsNoDistribute() != null && inviteVehicleRecordDTO.getIsNoDistribute()) {
            inviteVehicleRecordPO.setIsNoDistribute(10041001);
        }
        if (inviteVehicleRecordDTO.getIsWaitDistribute() != null && inviteVehicleRecordDTO.getIsNoDistribute()) {
            inviteVehicleRecordPO.setIsWaitDistribute(10041001);
        }
        //查询主线索
        inviteVehicleRecordPO.setIsMain(1);
        // 是否是零附件线索
        List<Integer> inviteTypeParam = inviteVehicleRecordPO.getInviteTypeParam();
        if (!org.springframework.util.CollectionUtils.isEmpty(inviteTypeParam)) {
            logger.info("!isEmpty inviteTypeParam");
            long count = inviteTypeParam.stream()
                    .filter(Objects::nonNull)
                    .filter(v -> Objects.equals(v.toString(), CommonConstants.INVITE_TYPE_XIII + ""))
                    .count();
            int size = inviteTypeParam.size();
            logger.info("size:{},count:{}", size, count);
            if (Objects.equals(size + "", count + "")) {
                logger.info("size = count");
                inviteVehicleRecordPO.setPartClue(true);
            }
        }
        List<InviteVehicleRecordDTO> list = inviteVehicleRecordMapper.selectInviteVehicleRecord(page,
                inviteVehicleRecordPO);
        page.setRecords(list);
        return page;
    }

    /**
     * 定时任务改变二次跟进状态
     */
    @Override
    public void updateIsAi() {
        inviteVehicleRecordMapper.updateIsAi();
    }

    @Override
    public IPage<InviteVehicleRecordDTO> getInviteVehicleRecordForSaSllocate(Page page,
                                                                             InviteVehicleRecordDTO inviteVehicleRecordDTO) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (inviteVehicleRecordDTO == null) {
            inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        }
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordDTO.transDtoToPo(InviteVehicleRecordPO.class);
        inviteVehicleRecordPO.setDealerCode(loginInfoDto.getOwnerCode());
        inviteVehicleRecordPO.setReturnIntentionLevel(inviteVehicleRecordDTO.getReturnIntentionLevel());
        if (inviteVehicleRecordDTO.getIsself() != null && inviteVehicleRecordDTO.getIsself() == 1) {
            inviteVehicleRecordPO.setSaId(String.valueOf(loginInfoDto.getUserId()));
        }
        if (inviteVehicleRecordDTO.getIsNoDistribute() != null && inviteVehicleRecordDTO.getIsNoDistribute()) {
            inviteVehicleRecordPO.setIsNoDistribute(10041001);
        }
        if (inviteVehicleRecordDTO.getIsWaitDistribute() != null && inviteVehicleRecordDTO.getIsNoDistribute()) {
            inviteVehicleRecordPO.setIsWaitDistribute(10041001);
        }
        //查询主线索
        inviteVehicleRecordPO.setIsMain(1);
        List<InviteVehicleRecordPO> list = inviteVehicleRecordMapper.getInviteVehicleRecordForSaSllocate(page,
                inviteVehicleRecordPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteVehicleRecordDTO> result = list.stream().map(m -> m.transPoToDto(InviteVehicleRecordDTO.class)
            ).collect(Collectors.toList());
            page.setRecords(result);
            return page;
        }
    }


    /**
     * 邀约线索分配
     *
     * @param saSllocateDlrDto
     * @return
     */
    @Override
    public int saveSaSllocate(SaSllocateDlrDto saSllocateDlrDto) {
        logger.info("saveSaLocateDlr,start");
        List<InviteVehicleRecordDTO> inviteVehicleRecordList = saSllocateDlrDto.getInviteVehicleRecordList();
        List<UserInfoDTO> saList = saSllocateDlrDto.getSelectSa();
        if (CollectionUtils.isNotEmpty(saList)) {
            Iterator<UserInfoDTO> iterator = saList.iterator();
            while (iterator.hasNext()) {
                if (getEmployeeStatus(String.valueOf(iterator.next().getId())) == 10081002) {
                    logger.info("删除离职状态的数据");
                    iterator.remove();
                }
            }
        }
        if (CollectionUtils.isEmpty(saList)) {
            return 1;
        }
        logger.info("saveSaLocateDlr,saList:{}", JSON.toJSONString(saList));
        logger.info("saveSaLocateDlr,ruleType:{}", saSllocateDlrDto.getRuleType());
        Map<Long,String> mapSa = saList.stream().collect(Collectors.toMap(UserInfoDTO::getId, UserInfoDTO::getUsername, (key1, key2) -> key2));
        int count = saList.size();
        if (saSllocateDlrDto.getRuleType() == 82121001) {//平均分配
            for (int i = 0; i < inviteVehicleRecordList.size(); i++) {
                InviteVehicleRecordDTO record = inviteVehicleRecordList.get(i);
                int index = i % count;
                UserInfoDTO sa = saList.get(index);
                record.setSaId(String.valueOf(sa.getId()));
                record.setSaName(sa.getUsername());
                this.update(record.getId(), record);
            }
        } else if (saSllocateDlrDto.getRuleType() == 82121002) {//根据上次接待SA分配
            for (int i = 0; i < inviteVehicleRecordList.size(); i++) {
                InviteVehicleRecordDTO record = inviteVehicleRecordList.get(i);
                record.setSaId(record.getLastSaId());
                record.setSaName(record.getLastSaName());
                this.update(record.getId(), record);
            }
        } else if (saSllocateDlrDto.getRuleType() == 82121003) {//以上次接待SA为主,平均分配为辅
            //每个人的平均值,超过平均值就不在给这个人分配任务
            double average = inviteVehicleRecordList.size() * 1.0 / count;
            //记录每个任务的分配数量
            HashMap<String, Integer> map = new HashMap<String, Integer>();
            int hasCount = 0;//已经分配数量，除去上次接待SA分配
            for (int i = 0; i < inviteVehicleRecordList.size(); i++) {
                InviteVehicleRecordDTO record = inviteVehicleRecordList.get(i);
                int flag = 0;//判断上次接待SA在不在，本次分配人员列表中
                if (record.getLastSaId() != null) {
                    for (int o = 0; o < saList.size(); o++) {
                        if (String.valueOf(saList.get(o).getId()).equals(record.getLastSaId())) {
                            flag = 1;
                            break;
                        }
                    }
                }
                UserInfoDTO sa = null;
                if (flag == 1) {
                    record.setSaId(record.getLastSaId());
                    Long id = Long.valueOf(record.getLastSaId());
                    String saName = mapSa.get(id);
                    logger.info("saveSaLocateDlr,id:{},saName:{}",record.getId(),saName);
                    record.setSaName(saName);
                    this.update(record.getId(), record);
                } else {
                    int index = hasCount % count;
                    sa = saList.get(index);
                    record.setSaId(String.valueOf(sa.getId()));
                    record.setSaName(sa.getUsername());
                    this.update(record.getId(), record);
                    hasCount++;
                }
                Integer cur = map.get(record.getSaId());
                if (cur == null) {
                    cur = Integer.valueOf(1);
                } else {
                    cur = cur + 1;
                }
                map.put(record.getSaId(), cur);
                if (record.getLastSaId() == null && cur >= average) {//如果不是上次接待SA分配，且分配完当前人已经比平均值大,
                    saList.remove(sa);
                    count = saList.size();//改变分配基数
                }
            }
        }else if (saSllocateDlrDto.getRuleType() == 82121004) { //以上次跟进人员为主,平均分配为辅,对待分配线索做排序,基于意向等级倒排就行
            returnIntentionLevelSa(inviteVehicleRecordList, saList, count, mapSa);
        }
        logger.info("saveSaLocateDlr,end");
        return 1;
    }

    private boolean returnIntentionLevelSa(List<InviteVehicleRecordDTO> inviteVehicleRecordList, List<UserInfoDTO> saList, int count,Map<Long,String> mapSa) {
        logger.info("inviteVehicleRecordList先根据上次跟进人员分批:{}",inviteVehicleRecordList);
        Date date=new Date();
        for (int i = 0; i < inviteVehicleRecordList.size(); i++) {
            InviteVehicleRecordDTO record = inviteVehicleRecordList.get(i);
            int flag =0 ;//判断上次接待SA在不在，本次分配人员列表中
            if (record.getLastSaId() != null) {
                for (int o = 0; o < saList.size(); o++) {
                    if (String.valueOf(saList.get(o).getId()).equals(record.getLastSaId())) {
                        flag = 1;
                        break;
                    }
                }
            }
            //先分批上次接待sa
            if (flag == 1) {
                record.setSaId(record.getLastSaId());
                Long id = Long.valueOf(record.getLastSaId());
                String saName = mapSa.get(id);
                logger.info("saveSaLocateDlr,id:{},saName:{}",record.getId(),saName);
                record.setSaName(saName);
                record.setUpdatedAt(date);
                record.setAllocation(true);
                this.update(record.getId(), record);
            }
        }
        //基于返厂意向等级平均分配
        Map<Long,InviteVehicleRecordDTO> longInviteVehicleRecordMap= inviteVehicleRecordList.stream().collect(Collectors.toMap(InviteVehicleRecordDTO::getId, InviteVehicleRecordDTO->InviteVehicleRecordDTO));
        List<Long> ids = inviteVehicleRecordList.stream().filter(e-> !e.isAllocation()).map(InviteVehicleRecordDTO::getId).collect(Collectors.toList());
        logger.info("inviteVehicleRecordList根据厂意向等级平均分配 ids:{}",ids);
        if (CollectionUtils.isEmpty(ids)){
            return true;
        }
        List<VocInviteVehicleTaskRecordPo> list = queryVocInviteVehicleTaskRecordByIds(ids);
        logger.info("VocInviteVehicleTaskRecordPo list:{}",list);
        for (int i = 0; i < list.size(); i++){
            logger.info("VocInviteVehicleTaskRecordPo 根据厂意向等级平均分配:{}",list.get(i));
            int index = i % count;
            UserInfoDTO sa = saList.get(index);
            InviteVehicleRecordDTO record = longInviteVehicleRecordMap.get(list.get(i).getRecordId());
            if (Objects.nonNull(record)) {
                record.setSaId(String.valueOf(sa.getId()));
                record.setSaName(sa.getUsername());
                record.setAllocation(true);
                this.update(record.getId(), record);
            }
        }
        //最后将没有查询到拓展表数据的进行平均分配
        List<InviteVehicleRecordDTO> inviteVehicleRecordDTOS = inviteVehicleRecordList.stream().filter(e-> !e.isAllocation()).collect(Collectors.toList());
        logger.info("inviteVehicleRecordDTOS 最后将没有查询到拓展表数据的进行平均分配:{}",inviteVehicleRecordDTOS);
        for (int i = 0; i < inviteVehicleRecordDTOS.size(); i++) {
            InviteVehicleRecordDTO record = inviteVehicleRecordDTOS.get(i);
            int index = i % count;
            UserInfoDTO sa = saList.get(index);
            record.setSaId(String.valueOf(sa.getId()));
            record.setSaName(sa.getUsername());
            this.update(record.getId(), record);
        }

        return false;
    }

    private List<VocInviteVehicleTaskRecordPo> queryVocInviteVehicleTaskRecordByIds(List<Long> ids) {
        LambdaQueryWrapper<VocInviteVehicleTaskRecordPo> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(VocInviteVehicleTaskRecordPo::getRecordId, ids);
        queryWrapper.eq(VocInviteVehicleTaskRecordPo::getIsDeleted, 0);
        queryWrapper.orderByDesc(VocInviteVehicleTaskRecordPo::getReturnIntentionLevel);
        return vocInviteVehicleTaskRecordMapper.selectList(queryWrapper);
    }

    /**
     * 查询当前登录人邮箱
     *
     * @return
     */
    private Integer getEmployeeStatus(String userId) {
        Integer employeeStatus = 0;
        try {
            employeeStatus = reportCommonClient.queryUserInfo(userId);
        } catch (Exception e) {
            logger.info("员工信息查询异常：{}", e.getMessage());
            throw new DALException("员工信息查询接口异常，请稍后再试");
        }
        return employeeStatus;
    }

    @Override
    public List<RecommendationDTO> getRecommendation(String vin) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        List<RecommendationDTO> list = this.queryRecommendation(loginInfoDto.getOwnerCode(), vin);
        for (RecommendationDTO dto : list) {
            List<TalkskillDTO> skillList = talkskillService.queryTalkskill(loginInfoDto.getOwnerCode(), "邀约", dto
                    .getName());
            List<String> talkskill = new ArrayList<String>();
            for (TalkskillDTO talk : skillList) {
                talkskill.add(talk.getTalkskill());
            }
            dto.setTalkskill(talkskill);
        }
        return list;
    }

    @Override
    public List<InviteVehicleRecordDTO> getInviteVehicleRecordInfo(String vin, Long id) {
        StopWatch sw = new StopWatch();
        sw.start("selectVehicleRecordAndSubcues");
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        List<InviteVehicleRecordPO> invs = inviteVehicleRecordMapper.selectVehicleRecordAndSubcues(id);
        List<InviteVehicleRecordDTO> list = invs.stream().map(m -> m.transPoToDto(InviteVehicleRecordDTO.class)).collect
                (Collectors.toList());
        sw.stop();
        sw.start("getVehInfo");
        VehicleDTO veh = this.getVehInfo(vin);
        sw.stop();
        sw.start("talkskill");
        VehicleInviteVO vo = repairCommonClient.getVehicleInvite(vin);
        // VehicleInviteVO vo = reportCommonClient.getVehicleInvite(vin);
        boolean b = vo != null;
        for (InviteVehicleRecordDTO dto : list) {
            if (dto.getInviteType() == 82381001 || dto.getInviteType() == 82381002) {
                //首保、定保
                if (dto.getInviteType() == 82381001) {
                    veh.setMileage(10000);
                } else if (dto.getInviteType() == 82381002) {
                    veh.setMileage(dto.getAdviseInMileage());
                }
                if (b) {
                    dto.setLastMaintenanceDate(vo.getLastMaintenanceDate());
                    dto.setLastMaintenanceMileage(vo.getLastMaintenanceMileage());
                    dto.setLastMaintenanceDealer(vo.getLastMaintenanceDealer());
                }
                this.setMaintain(veh, dto);
            } else if (dto.getInviteType() == 82381005) {
                //易损件
                List<TalkskillDTO> skillList = talkskillService.queryTalkskill(loginInfoDto.getOwnerCode(), dto
                        .getItemName());
                dto.setTalkskill(skillList);
            } else if (dto.getInviteType() == 82381007) {
                //召回
                List<TalkskillDTO> skillList = talkskillService.queryTalkskill(loginInfoDto.getOwnerCode(), "邀约", "召回");
                dto.setTalkskill(skillList);
            } else if (dto.getInviteType() == 82381006) {
                //客户流失
                List<TalkskillDTO> skillList = talkskillService.queryTalkskill(loginInfoDto.getOwnerCode(), "邀约",
                        "客户流失");
                dto.setTalkskill(skillList);
            } else if (dto.getInviteType() == 82381003) {
                //续保
                List<TalkskillDTO> skillList = talkskillService.queryTalkskill(loginInfoDto.getOwnerCode(), "邀约",
                        "续保");
                dto.setTalkskill(skillList);
            } else if (dto.getInviteType() == 82381004) {
                //VOC事故
                List<TalkskillDTO> skillList = talkskillService.queryTalkskill(loginInfoDto.getOwnerCode(), "邀约",
                        "事故");
                dto.setTalkskill(skillList);
            } else if (dto.getInviteType() == 82381009) {
                //VOC事故
                List<TalkskillDTO> skillList = talkskillService.queryTalkskill(loginInfoDto.getOwnerCode(), "邀约",
                        "保修");
                dto.setTalkskill(skillList);
            }
        }
        sw.stop();
        sw.start("延保");
        //保修未到期
        if (veh.getInvoiceEndDate() != null && veh.getInvoiceEndDate().getTime() > System.currentTimeMillis()) {
            //查询是否存在购买记录
            Integer rs = inviteVehicleRecordMapper.getHasExtendedWarrantBugRecord(vin);
            if (rs == null) {
                Calendar c = Calendar.getInstance();
                c.setTime(veh.getInvoiceEndDate());
                InviteVehicleRecordDTO dto = new InviteVehicleRecordDTO();
                dto.setExtensionInsurance("可在" + c.get(Calendar.YEAR) + "年" + (c.get(Calendar.MONTH) + 1) + "月" + c
                        .get(Calendar
                                .DATE) + "日前购买延保");
                dto.setInviteType(82381010);//延保
                List<TalkskillDTO> skillList = talkskillService.queryTalkskill(loginInfoDto.getOwnerCode(), "邀约",
                        "延保");
                dto.setTalkskill(skillList);
                list.add(dto);
            }
        }
        sw.stop();
        logger.info(sw.prettyPrint());
        return list;
    }


    @Override
    public List<InviteVehicleRecordDTO> getInviteVehicleVulInfo(String vin) {
        List<InviteVehicleRecordDTO> list = new ArrayList<>();
        List<InviteVulDTO> ivList = businessPlatformService.getVulInfo(vin);
        InvitePartItemRulePO param = new InvitePartItemRulePO();
        VehiclePO vpo = vehicleMapper.getVehicleByVin(vin);
        TmVehicleDTO dto = businessPlatformService.getVehicleByVIN(vin);


        Double dailyMile = null;
        Date invoiceDate = null;
        if (dto != null) {
            invoiceDate = dto.getInvoiceDate();
        }
        if (vpo != null) {
            if (vpo.getDailyAverageMileage() != null) {
                dailyMile = vpo.getDailyAverageMileage().doubleValue();
            }
        }
        //有效的
        param.setIsUse(1);
        List<InvitePartItemRulePO> rules = invitePartItemRuleMapper.selectListBySql(param);

        for (InvitePartItemRulePO rule : rules) {
            InviteVehicleRecordDTO vo = new InviteVehicleRecordDTO();
            vo.setItemType(rule.getType());
            vo.setItemCode(rule.getCode());
            vo.setItemName(rule.getName());
            vo.setInviteType(CommonConstants.INVITE_TYPE_V);
            boolean exists = false;
            if (!CommonUtils.isNullOrEmpty(ivList)) {
                for (InviteVulDTO ivDto : ivList) {
                    if (ivDto.getFunctionGroup().equals(rule.getCode())) {
                        if (ivDto.getRoBalanceTimeFirst() == null) {
                            break;
                        }
                        vo.setLastChangeDate(ivDto.getRoBalanceTimeFirst());
                        vo.setOutMileage(ivDto.getOutMileage());
                        Date adviseInDate = this.setAdviseInDate(vo, ivDto.getRoBalanceTimeFirst(), dailyMile, rule
                                .getMileageInterval(), rule.getDateInterval());
                        vo.setAdviseInDate(adviseInDate);
                        exists = true;
                        break;
                    }
                }
            }
            if (!exists) {
                if (invoiceDate != null) {
                    vo.setLastChangeDate(invoiceDate);
                    Date adviseInDate = this.setAdviseInDate(vo, invoiceDate, dailyMile, rule
                            .getMileageInterval(), rule.getDateInterval());
                    vo.setAdviseInDate(adviseInDate);
                }
            }
            list.add(vo);
        }
        return list;
    }

    protected Date setAdviseInDate(InviteVehicleRecordDTO vo, Date inviteTime, Double dailyAverageMileage, Integer
            mileageInterval, Integer dateInterval) {
        if ((mileageInterval == 0 || dailyAverageMileage == null || dailyAverageMileage == 0) && dateInterval == 0) {
            return null;
        }
        Date adviseInDate1 = null;
        Date adviseInDate2 = null;
        //日期间隔
        if (dateInterval != 0) {
            Calendar c = Calendar.getInstance();
            c.setTime(inviteTime);
            c.add(Calendar.MONTH, dateInterval);
            adviseInDate1 = c.getTime();
            logger.info("日期间隔,建议入厂时间{}", adviseInDate1);
        }
        //里程间隔
        if (mileageInterval != 0 && dailyAverageMileage != null && dailyAverageMileage > 0) {
            Calendar c = Calendar.getInstance();
            c.setTime(inviteTime);
            int amount = (int) (mileageInterval / dailyAverageMileage);
            c.add(Calendar.DATE, amount);
            adviseInDate2 = c.getTime();
            logger.info("里程间隔,建议入厂时间{}", adviseInDate2);
        }
        if (vo.getOutMileage() == null) {
            logger.info("基准里程为null,设置初始里程为0{}", vo.getOutMileage());
            vo.setOutMileage(0d);
        }
        vo.setAdviseInMileage(vo.getOutMileage().intValue() + mileageInterval);
        logger.info("建议入厂里程{}", vo.getAdviseInMileage());
        //日期间隔为空取里程间隔
        if (dateInterval == 0) {
            return adviseInDate2;
        }
        //里程间隔为空或平均里程为空 取日期间隔
        if (mileageInterval == 0 || dailyAverageMileage == null || dailyAverageMileage <= 0) {
            return adviseInDate1;
        }
        //存在两条，取较小的建议进厂日期
        return adviseInDate1.compareTo(adviseInDate2) > 0 ? adviseInDate2 : adviseInDate1;
    }

    /**
     * 保存跟进记录
     *
     * @param dto
     * @return
     */
    @Override
    public int saveInviteVehicleRecord(InviteVehicleRecordDetailDTO dto) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        dto.setDealerCode(loginInfoDto.getOwnerCode());
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordMapper.selectById(dto.getInviteId());
        inviteVehicleRecordPO.setFollowStatus(dto.getStatus());
        //实际跟进日期
        inviteVehicleRecordPO.setActualFollowDate(new Date());
        inviteVehicleRecordPO.setSaId(String.valueOf(loginInfoDto.getUserId()));
        inviteVehicleRecordPO.setSaName(loginInfoDto.getUserName());
        inviteVehicleRecordPO.setContent(dto.getContent());
        inviteVehicleRecordPO.setLoseReason(dto.getLoseReason());

        //20210712 添加未使用AI原因以及线索异常状态
        if (dto.getNoAIreason() == null) {
            inviteVehicleRecordPO.setItemCode("");
        } else {
            inviteVehicleRecordPO.setItemCode(dto.getNoAIreason());
        }
        inviteVehicleRecordPO.setItemType(dto.getErrorStatus());

        //更新子线索
        InviteVehicleRecordPO up = new InviteVehicleRecordPO();
        if (inviteVehicleRecordPO.getFirstFollowDate() == null) {
            inviteVehicleRecordPO.setFirstFollowDate(inviteVehicleRecordPO.getActualFollowDate());
            up.setFirstFollowDate(inviteVehicleRecordPO.getActualFollowDate());
        }
        //继续跟进
        if (CommonConstants.FOLLOW_STATUS_IV.equals(dto.getStatus())) {
            inviteVehicleRecordPO.setPlanFollowDate(dto.getPlanDate());
            up.setPlanFollowDate(dto.getPlanDate());
        } else {
            inviteVehicleRecordPO.setPlanFollowDate(null);
            up.setPlanFollowDate(null);
        }
        //执行更新线索跟进状态
        inviteVehicleRecordMapper.updateById(inviteVehicleRecordPO);

        up.setFollowStatus(dto.getStatus());
        //实际跟进日期
        up.setActualFollowDate(new Date());
        up.setSaId(String.valueOf(loginInfoDto.getUserId()));
        up.setSaName(loginInfoDto.getUserName());
        LambdaUpdateWrapper<InviteVehicleRecordPO> updateWrapper = new UpdateWrapper().lambda();
        updateWrapper.eq(InviteVehicleRecordPO::getParentId, dto.getInviteId());
        inviteVehicleRecordMapper.update(up, updateWrapper);
        Long detailId = this.addInviteVehicleRecordDetail(dto);
        this.UpdateCallDetailsForDetailId(dto.getInviteId(), detailId);
        //增加判断是否当月AI 并更新二次更进数据
        int isCurrentAi = 0;
        CallDetailsPO callDetail = callDetailsMapper.selectIsCurrentMonth(dto.getInviteId());
        if (callDetail != null) {
            isCurrentAi = 1;
        }
        callDetailsMapper.updateTwiceFollow(dto.getInviteId(), isCurrentAi);

        this.maintenvehicleSaRef(inviteVehicleRecordPO.getVin(), inviteVehicleRecordPO.getDealerCode());
        //发送MQ
        //判断线索类型
        Integer invite = inviteVehicleRecordPO.getInviteType();
        String inviteType = invite == null ? null : invite.toString();
        if (InviteTypeEnum.containsCode(inviteType)) {
            Optional<VocInviteVehicleTaskRecordPo> inviteTaskRecordOptional = Optional.ofNullable(taskRecordService.selectIcmIdByRecordId(dto.getInviteId()));
            inviteTaskRecordOptional.ifPresent(inviteTaskRecord -> {
                inviteVehicleRecordPO.setIcmId(inviteTaskRecord.getIcmId());
                Optional.ofNullable(inviteTaskRecord.getVerifyStatus())
                        .ifPresent(verifyStatus -> inviteVehicleRecordPO.setVerifyStatus(verifyStatus.toString()));
                logger.info("saveInviteVehicleRecord,inviteVehicleRecordPO:{}", inviteVehicleRecordPO);
                this.publishEvent(Lists.newArrayList(inviteVehicleRecordPO));
            });
        }
        return 1;
    }

    public int checkAI(InviteVehicleRecordDetailDTO dto) {
        //判断当月是否有AI 没有提示选择未使用AI原因
        CallDetailsPO callDetail = getLastCallDetails(dto.getInviteId(),dto.getBatchNo());
        if (callDetail == null) {
            return 2;
        }
        //判断是否使用AI的逻辑是：从线索创建日期开始到建议入场日期月份结束前拨打的AI都算AI跟进
//        Calendar cal_1 = Calendar.getInstance();
//        cal_1.add(Calendar.MONTH, -1);
//        cal_1.set(Calendar.DAY_OF_MONTH, 1);
//        if (callDetail.getStartTime().before(cal_1.getTime())) {
//            return 2;
//        }
        return 1;
    }

    /**
     * 更新ai通话详情和跟进记录关系
     *
     * @param inviteId
     * @param detailId
     */
    private void UpdateCallDetailsForDetailId(Long inviteId, Long detailId) {

        List<Long> callRecordIds = callDetailsMapper.queryInviteCallDetails(inviteId);
        logger.info("待更新ai通话记录id列表：{}", callRecordIds);
        if (CollectionUtils.isEmpty(callRecordIds)){
            return;
        }
        callDetailsMapper.updateCallRecordsDetailId(detailId, callRecordIds);
    }

    /**
     * 保存预约单
     *
     * @param vo
     * @return
     */
    @Override
    public String saveBookingRecord(BookingCreateParamsVo vo) {
        logger.info("开始预约接口");
        String back = repairCommonClient.saveBookingRecord(vo);
        logger.info("预约接口返回,{}", back);
        if (back.contains("{")) {
            JSONObject jsonobject = JSONObject.parseObject(back);
            if (jsonobject.getInteger("resultCode") != 200) {
                String errMsg = jsonobject.getString("errMsg");
                if (errMsg.indexOf("错误原因:") != -1) {
                    errMsg = errMsg.substring(errMsg.indexOf("错误原因:"));
                }
                throw new DALException(errMsg);
            }
        }
        return back;
    }

    /**
     * 导出查询
     *
     * @param inviteVehicleRecordDTO
     * @return
     */
    @Override
    public List<Map> exportExcelinviteVehicleRecord(InviteVehicleRecordDTO inviteVehicleRecordDTO) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordDTO.transDtoToPo(InviteVehicleRecordPO.class);
        if (inviteVehicleRecordDTO.getIsself() != null && inviteVehicleRecordDTO.getIsself() == 1) {
            inviteVehicleRecordPO.setSaId(String.valueOf(loginInfoDto.getUserId()));
        }
        inviteVehicleRecordPO.setDealerCode(loginInfoDto.getOwnerCode());
        inviteVehicleRecordPO.setIsMain(1);
        return inviteVehicleRecordMapper.exportExcelinviteVehicleRecord(inviteVehicleRecordPO);
    }

    /**
     * 维护车辆和上次跟进人员关系
     *
     * @param vin
     * @param dealerCode
     */
    private void maintenvehicleSaRef(String vin, String dealerCode) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        LambdaQueryWrapper<InviteVehicleSaRefPO> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(InviteVehicleSaRefPO::getVin, vin);
        queryWrapper.eq(InviteVehicleSaRefPO::getDealerCode, dealerCode);
        InviteVehicleSaRefPO rs = inviteVehicleSaRefMapper.selectOne(queryWrapper);
        if (rs != null) {//更新
            rs.setSaId(String.valueOf(loginInfoDto.getUserId()));
            rs.setSaName(loginInfoDto.getUserCode());
            inviteVehicleSaRefMapper.updateById(rs);
        } else {//新增
            InviteVehicleSaRefPO po = new InviteVehicleSaRefPO();
            po.setVin(vin);
            po.setDealerCode(dealerCode);
            po.setSaId(String.valueOf(loginInfoDto.getUserId()));
            po.setSaName(loginInfoDto.getUserName());
            inviteVehicleSaRefMapper.insert(po);
        }
    }

    /**
     * 新增跟进记录
     *
     * @param dto
     */
    private Long addInviteVehicleRecordDetail(InviteVehicleRecordDetailDTO dto) {
        logger.info("addInviteVehicleRecordDetail,start");
        Long inviteId = dto.getInviteId();
        Integer inviteType = dto.getInviteType();
        String vin = dto.getVin();
        logger.info("addInviteVehicleRecordDetail,inviteId:{}, inviteType:{}, vin:{}", inviteId, inviteType, vin);

        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        InviteVehicleRecordDetailDTO recordDetail = new InviteVehicleRecordDetailDTO();
        recordDetail.setInviteType(inviteType);
        recordDetail.setVin(vin);
        recordDetail.setInviteId(inviteId);
        recordDetail.setContent(dto.getContent());
        recordDetail.setStatus(dto.getStatus());
        recordDetail.setPlanDate(dto.getPlanDate());//下次跟进日期
        recordDetail.setActualDate(new Date());//实际跟进日期
        recordDetail.setLoseReason(dto.getLoseReason());
        recordDetail.setFeedback(dto.getFeedback());
        recordDetail.setMode(dto.getMode());
        recordDetail.setNotFollowReason(dto.getNotFollowReason());
        recordDetail.setRemark(dto.getRemark());
        recordDetail.setOwnerCode(loginInfoDto.getOwnerCode());//经销商
        recordDetail.setSaId(String.valueOf(loginInfoDto.getUserId()));
        recordDetail.setSaName(loginInfoDto.getUserName());
        recordDetail.setDealerCode(loginInfoDto.getOwnerCode());
        Long id = inviteVehicleRecordDetailService.addInviteVehicleRecordDetail(recordDetail);
        logger.info("addInviteVehicleRecordDetail,id:{}", id);
        int num = vocInviteVehicleTaskRecordMapper.updateRecordNum(inviteId);
        logger.info("addInviteVehicleRecordDetail,num:{}", num);
        /**处理线索没有生成对应扩展表时的数据 ---start---*/
        //影响行数为零,表示没有扩展表
        if (num == 0) {
            doRecordNum(inviteId, inviteType, vin);
        }
        /**处理线索没有生成对应扩展表时的数据 ---end---*/
        return id;
    }

    private void doRecordNum(Long inviteId, Integer inviteType, String vin) {
        logger.info("doRecordNum,inviteId:{}, inviteType:{}, vin:{}", inviteId, inviteType, vin);
        //1.查询是否没有扩展表
        Integer flag = vocInviteVehicleTaskRecordMapper.selectTaskRecordByInviteId(inviteId);
        logger.info("doRecordNum,flag:{}", flag);
        List<InviteVehicleTaskPO> taskPoList = null;
        if (inviteId != null && flag != null && flag == 0) {
            //2.查询任务表中数据
            taskPoList = inviteVehicleTaskMapper.selectByInviteId(inviteId);
        }
        InviteVehicleTaskPO po = null;
        if (CollectionUtils.isNotEmpty(taskPoList)) {
            logger.info("doRecordNum,taskPoList:{}", taskPoList.size());
            po = taskPoList.get(0);
        }
        Long taskId = po == null ? null : po.getId();
        //3.封装数据添加到扩展表
        VocInviteVehicleTaskRecordPo recPo = new VocInviteVehicleTaskRecordPo();
        recPo.setTaskId(taskId);
        recPo.setRecordId(inviteId);
        logger.info("doRecordNum,taskId:{}", taskId);
        //4.跟进次数查询
        int recordNum = inviteVehicleRecordDetailService.selectNumByCount(inviteId);
        logger.info("doRecordNum,recordNum:{}", recordNum);
        recPo.setRecordNum(recordNum);
        recPo.setRecordType(RecordTypeEnum.POST_SALE.getIntCode());
        if (CommonConstants.INVITE_TYPE_VI.equals(inviteType)) {
            recPo.setLossType(CommonConstants.LOSS_TYPE_1);
        } else {
            recPo.setLossType(CommonConstants.LOSS_TYPE_0);
        }
        recPo.setVin(vin);
        recPo.setInviteType(inviteType);
        recPo.setCreatedBy("-9");
        vocInviteVehicleTaskRecordMapper.insert(recPo);

    }

    /**
     * 查询最新通话记录
     *
     * @param inviteId
     * @param batchNo
     * @return
     */
    private CallDetailsPO getLastCallDetails(Long inviteId, String batchNo) {
        return callDetailsMapper.selectLatest(inviteId,batchNo);
    }

    /**
     * 中台接口获取车辆信息
     *
     * @param vin
     * @return
     */
    public VehicleDTO getVehInfo(String vin) {
        VehicleDTO dto = businessPlatformService.listOwnerVehiclePage(vin);
        if (dto.getInvoiceEndDate() == null && dto.getInvoiceDate() != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(dto.getInvoiceDate());
            c.add(Calendar.MONTH, 33);
            c.add(Calendar.DATE, -1);
            dto.setInvoiceEndDate(c.getTime());
        } else if (dto.getInvoiceEndDate() != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(dto.getInvoiceEndDate());
            c.add(Calendar.MONTH, -3);
            dto.setInvoiceEndDate(c.getTime());
        }
        return dto;
    }


    /**
     * 设置保养套餐数据
     *
     * @param veh
     * @param dto
     */
    private void setMaintain(VehicleDTO veh, InviteVehicleRecordDTO dto) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        logger.info("查询保养套餐,ModelCode{},EngineNo{},Mileage{}", veh.getModelCode(), veh.getEngineNo(), veh.getMileage());
        SetMainFileParamsVO param = new SetMainFileParamsVO();
        param.setModelCode(veh.getModelCode());
        param.setEngineCode(veh.getVin().substring(5, 7));
        if (veh.getMileage() != null) {
            param.setInMileage(new BigDecimal(veh.getMileage()));
        }
        param.setDealerCode(loginInfoDto.getOwnerCode());
        List<SetMainFileVO> list = setMainFileMapper.querySetMainFileListNew(param.toMaps());

        if (CommonUtils.isNullOrEmpty(list)) {
            dto.setMaintainList(new ArrayList<SetMainFileVO>());
        } else {
            for (SetMainFileVO set : list) {
                List<TalkskillDTO> skillList = talkskillService.queryTalkskill(loginInfoDto.getOwnerCode(), set.getSetName());
                set.setTalkskill(skillList);
                //明细信息
                List<SetMainFileDetailVO> detailList = setMainFileMapper.querySetMainFileDetail(set.getId());
                set.setMainFileDetail(detailList);
            }
            dto.setMaintainList(list);
        }

    }

    /**
     * 产品接口 未实现
     *
     * @param dealerCode
     * @param vin
     * @return
     */
    private List<RecommendationDTO> queryRecommendation(String dealerCode, String vin) {
        List<RecommendationDTO> list = new ArrayList<RecommendationDTO>();
        RecommendationDTO dto = new RecommendationDTO();
        dto.setCode("12345");
        dto.setName("更换电池组");
        dto.setType("上次未修项目");
        dto.setCreatedAt(new Date());
        list.add(dto);
        dto = new RecommendationDTO();
        dto.setCode("23211");
        dto.setName("更换刹车片");
        dto.setType("上次未修项目");
        dto.setCreatedAt(new Date());
        list.add(dto);
        return list;
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param inviteVehicleRecordDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.InviteVehicleRecordDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<InviteVehicleRecordDTO> selectListBySql(InviteVehicleRecordDTO inviteVehicleRecordDTO) {
        if (inviteVehicleRecordDTO == null) {
            inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        }
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordDTO.transDtoToPo(InviteVehicleRecordPO.class);
        List<InviteVehicleRecordPO> list = inviteVehicleRecordMapper.selectListBySql(inviteVehicleRecordPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteVehicleRecordDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.InviteVehicleRecordDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public InviteVehicleRecordDTO getById(Long id) {
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordMapper.selectById(id);
        if (inviteVehicleRecordPO != null) {
            return inviteVehicleRecordPO.transPoToDto(InviteVehicleRecordDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param inviteVehicleRecordDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(InviteVehicleRecordDTO inviteVehicleRecordDTO) {
        //对对象进行赋值操作
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordDTO.transDtoToPo(InviteVehicleRecordPO.class);
        //执行插入
        //返回插入的值
        return inviteVehicleRecordMapper.insert(inviteVehicleRecordPO);
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                     主键ID
     * @param inviteVehicleRecordDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, InviteVehicleRecordDTO inviteVehicleRecordDTO) {
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordMapper.selectById(id);
        //对对象进行赋值操作
        inviteVehicleRecordDTO.transDtoToPo(inviteVehicleRecordPO);
        //执行更新
        return inviteVehicleRecordMapper.updateById(inviteVehicleRecordPO);
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = inviteVehicleRecordMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = inviteVehicleRecordMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 添加邀约记录
     *
     * @param inviteVehicleRecordDTO
     * @return
     */
    @Override
    public Long addInviteVehicleRecord(InviteVehicleRecordDTO inviteVehicleRecordDTO) {
        //对对象进行赋值操作
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordDTO.transDtoToPo(InviteVehicleRecordPO.class);
        //执行插入
        inviteVehicleRecordMapper.insert(inviteVehicleRecordPO);
        return inviteVehicleRecordPO.getId();
    }

    /**
     * 分页查询----保险跟进
     *
     * <AUTHOR>
     */
    @Override
    public IPage<InviteVehicleRecordDTO> selectFollowInsureRecord(Page page, String followChoiced,
                                                                  InviteVehicleRecordDTO inviteVehicleRecordDTO) {

        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (inviteVehicleRecordDTO == null) {
            inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        }
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordDTO.transDtoToPo(InviteVehicleRecordPO.class);
        inviteVehicleRecordPO.setDealerCode(loginInfoDto.getOwnerCode());
        List<Integer> status = new ArrayList<Integer>();
        List<Integer> orderStatus = new ArrayList<Integer>();
        if (followChoiced.equals("following")) {
            status.add(82401001);
            status.add(82401004);
            orderStatus.add(82411002);
        } else if (followChoiced.equals("followed")) {
            status.add(82401002);
            status.add(82401003);
            status.add(82401005);
            orderStatus.add(82411001);
            orderStatus.add(82411003);
            orderStatus.add(82411004);
        } else if (followChoiced.equals("followHistory")) {
            status.add(82401001);
            status.add(82401002);
            status.add(82401003);
            status.add(82401004);
            status.add(82401005);
            orderStatus.add(82411001);
            orderStatus.add(82411002);
            orderStatus.add(82411003);
            orderStatus.add(82411004);
        } else {
            throw new DALException("跟进状态获取出错了,请检查重试");
        }
        inviteVehicleRecordPO.setOrderStatusParam(orderStatus);
        inviteVehicleRecordPO.setFollowStatusParam(status);
        List<InviteVehicleRecordPO> list = inviteVehicleRecordMapper.selectFollowInsureRecord(page,
                inviteVehicleRecordPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteVehicleRecordDTO> result = list.stream().map(m -> m.transPoToDto(InviteVehicleRecordDTO.class)
            ).collect(Collectors.toList());
            Map<String, Boolean> cache = Maps.newHashMap();
            for (InviteVehicleRecordDTO recordDTO : result) {
                String key = StringUtils.join(Lists.newArrayList(recordDTO.getVin(), recordDTO.getDealerCode()), UNDERLINE);
                Boolean contains = cache.get(key);
                if (Objects.isNull(contains)) {
                    List<InsuranceBillDTO> insuranceBillList = inviteVehicleRecordMapper.selectInsuranceBill(recordDTO.getVin(), recordDTO.getDealerCode());
                    contains = !CommonUtils.isNullOrEmpty(insuranceBillList);
                }
                recordDTO.setIsInsureSuccess(contains ? 10041001 : 10041002);
            }
            page.setRecords(result);
            return page;
        }
    }

    /**
     * 保存跟进记录----保险跟进
     *
     * @param dto
     * <AUTHOR>
     */
    @Override
    public int saveFollowInsureRecord(InviteVehicleRecordDetailDTO dto) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordMapper.selectById(dto.getInviteId());
        inviteVehicleRecordPO.setFollowStatus(dto.getStatus());
        //继续跟进
        if (CommonConstants.FOLLOW_STATUS_IV.equals(dto.getStatus())) {
            inviteVehicleRecordPO.setPlanFollowDate(dto.getPlanDate());
        } else {
            inviteVehicleRecordPO.setPlanFollowDate(null);
        }
        if (inviteVehicleRecordPO.getFirstFollowDate() == null) {
            inviteVehicleRecordPO.setFirstFollowDate(new Date());
        }
        inviteVehicleRecordPO.setActualFollowDate(new Date());//实际跟进日期
        inviteVehicleRecordPO.setSaId(String.valueOf(loginInfoDto.getUserId()));
        inviteVehicleRecordPO.setSaName(loginInfoDto.getUserCode());
        //执行更新跟进状态
        inviteVehicleRecordMapper.updateById(inviteVehicleRecordPO);
        this.addInviteVehicleRecordDetail(dto);
        this.maintenvehicleSaRef(inviteVehicleRecordPO.getVin(), inviteVehicleRecordPO.getDealerCode());
        return 1;
    }

    /**
     * 保险跟进失败 -- 计划任务
     *
     * <AUTHOR>
     */
    @Override
    public void updateInsureFollowStatus() {

        List<InviteVehicleRecordPO> poList = inviteVehicleRecordMapper.selectInsuranceInvitePlan();
        if (!CommonUtils.isNullOrEmpty(poList)) {
            List<Long> idList = new ArrayList<Long>();
            List<Long> idList2 = new ArrayList<Long>();
            for (InviteVehicleRecordPO recordPO : poList) {
                idList.add(recordPO.getId());
                CheckRepairOrderDTO orderDTO = reportCommonClient.checkRepairOrderByVin(recordPO.getVin());
                if (null != orderDTO && null != orderDTO.getIsEnterFactory() && orderDTO.getIsEnterFactory() == 10041001) {
                    idList2.add(recordPO.getId());
                }
            }
            if (!CommonUtils.isNullOrEmpty(idList)) {
                inviteVehicleRecordMapper.updateInsureFollowStatus(idList, "99999");
            }
            /*if (rowsUpdate > 0) {
                logger.info("保险跟进失败计划任务执行成功，已修改了" + rowsUpdate + "条数据");
            } else {
                logger.info("保险跟进失败计划任务执行成功，没有符合条件的数据去修改");
            }*/
            if (!CommonUtils.isNullOrEmpty(idList2)) {
                inviteVehicleRecordMapper.insertInsureInviteTask(idList2, "99999");
            }
        }
    }

    /**
     * 导出查询 ---- 保险跟进
     *
     * @param inviteVehicleRecordDTO
     * <AUTHOR>
     */
    @Override
    public List<Map> exportExcelFollowInsure(String followChoiced, InviteVehicleRecordDTO inviteVehicleRecordDTO) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordDTO.transDtoToPo(InviteVehicleRecordPO.class);
        inviteVehicleRecordPO.setDealerCode(loginInfoDto.getOwnerCode());
        List<Integer> status = new ArrayList<Integer>();
        if (followChoiced.equals("following")) {
            status.add(82401001);
            status.add(82401004);
        } else if (followChoiced.equals("followed")) {
            status.add(82401002);
            status.add(82401003);
            status.add(82401005);
        } else if (followChoiced.equals("followHistory")) {
            status.add(82401001);
            status.add(82401002);
            status.add(82401003);
            status.add(82401004);
            status.add(82401005);
        } else {
            throw new DALException("跟进状态获取出错了,请检查重试");
        }
        inviteVehicleRecordPO.setFollowStatusParam(status);
        return inviteVehicleRecordMapper.exportExcelFollowInsure(inviteVehicleRecordPO);
    }

    /**
     * @param createDate
     * @return
     */
    @Override
    public List<InviteVehicleTaskPO> getWaitCloseRecord(String createDate) {
        return inviteVehicleTaskMapper.getWaitCloseRecord(createDate);
    }

    @Override
    public List<InviteVehicleRecordPO> findCluesToClose(String startDate, String endDate) {
        return inviteVehicleRecordMapper.findCluesToClose(startDate, endDate);
    }

    @Override
    public void closeCluesBulk(List<Long> list) {

    }

    /**
     * @param vin
     * @return
     */
    @Override
    public List<InviteVehicleRecordPO> getWaitCloseMaintainRecord(String vin) {

        return inviteVehicleRecordMapper.getWaitCloseMaintainRecord(vin);
    }

    /**
     * 查询待关闭的voc 事故线索
     *
     * @param createDate
     * @param vin
     * @return
     */
    @Override
    public List<InviteVehicleRecordPO> getWaitCloseVocAccidentRecord(String createDate, String vin) {
        return inviteVehicleRecordMapper.getWaitCloseVocAccidentRecord(createDate, vin);
    }

    /**
     * 查询待关闭的保修线索
     *
     * @param createDate
     * @return
     */
    @Override
    public List<InviteVehicleRecordPO> getWaitCloseGuaranteeRecord(String createDate) {
        return inviteVehicleRecordMapper.getWaitCloseGuaranteeRecord(createDate);
    }


    /**
     * 查询待关闭的保修线索
     *
     * @param vin
     * @return
     */
    @Override
    public List<InviteVehicleRecordPO> getWaitCloseGuaranteeRecordByVin(String vin) {
        return inviteVehicleRecordMapper.getWaitCloseGuaranteeRecordByVin(vin);
    }

    @Override
    public List<InviteVehicleRecordPO> getWaitCloseVulnerableRecord(String vin, String code, Integer type) {
        return inviteVehicleRecordMapper.getWaitCloseVulnerableRecord(vin, code, type);
    }

    /**
     * 分配经销商修改未下发的线索和未下发的任务
     *
     * @param dealerCode
     * @param lastDealerCode
     */
    @Override
    public int updateInviteByDealerCode(String vin, String dealerCode, String lastDealerCode) {
        int rows = inviteVehicleRecordMapper.updateInviteByDealerCode(vin, dealerCode, lastDealerCode);
        inviteVehicleTaskMapper.updateInviteTaskByDealerCode(vin, dealerCode, lastDealerCode);
        return rows;
    }

    /**
     * 查询待分配数量
     *
     * @return
     */
    @Override
    public Integer getNeedDistribute(List<Integer> leaveIds) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        return inviteVehicleRecordMapper.getNeedDistribute(loginInfoDto.getOwnerCode(), leaveIds);
    }

    /**
     * 更新预约单号
     *
     * @param bookingNo
     * @param inviteId
     */
    @Override
    public void saveBookingNoForRecord(String bookingNo, Long inviteId,String ownerCode,String vin) {
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordMapper.selectById(inviteId);
        //线索已关联预约单则不更新，没关联就更新或线索vin和预约登记vin不相等也不更新
        if (Objects.nonNull(inviteVehicleRecordPO) &&
                (Objects.nonNull(inviteVehicleRecordPO.getBookNo()) ||
                        !inviteVehicleRecordPO.getVin().equals(vin))) {
            logger.info("inviteId is {} not update invite info after generate an appointment",inviteId);
            return;
        }
        logger.info("start update invite info, inviteId is {},bookNo is {}",inviteId,inviteVehicleRecordPO.getBookNo());
        inviteVehicleRecordPO.setIsBook(1);
        inviteVehicleRecordPO.setBookNo(bookingNo);
        inviteVehicleRecordMapper.updateById(inviteVehicleRecordPO);
        VocInviteVehicleTaskRecordPo po = new VocInviteVehicleTaskRecordPo();
        LambdaQueryWrapper<VocInviteVehicleTaskRecordPo> qw = new LambdaQueryWrapper();
        qw.eq(VocInviteVehicleTaskRecordPo::getRecordId, inviteId);
        qw.isNull(VocInviteVehicleTaskRecordPo::getRecordAt);
        po.setRecordAt(DateUtils.parseDate(new Date(), DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
        vocInviteVehicleTaskRecordMapper.update(po, qw);
    }

    @Override
    public List<InviteVehicleRecordPO> getWaitCloseLostRecord(String dealerCode, String vin) {
        return inviteVehicleRecordMapper.getWaitCloseLostRecord(dealerCode, vin);
    }

    @Override
    public List<InviteVehicleRecordPO> getWaitCloseAlertRecord(String dealerCode, String vin) {
        return inviteVehicleRecordMapper.getWaitCloseAlertRecord(dealerCode, vin);
    }

    /**
     * 根据手机更新 oneId
     *
     * @param dto
     */
    @Override
    public void updateOneIdByMobile(CustomerInfoListReturnDTO dto) {
        if (StringUtils.isNullOrEmpty(dto.getMobile())) {
            return;
        }
        inviteVehicleRecordMapper.updateOneIdByMobile(dto);
    }


    /**
     * c端查询养修线索
     */
    @Override
    public IPage<InviteVehicleRecordDTO> getInviteVehicleRecordCPort(Page page, InviteVehicleRecordDTO inviteVehicleRecordDTO) {
        if (inviteVehicleRecordDTO == null) {
            inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        }
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordDTO.transDtoToPo(InviteVehicleRecordPO.class);
        inviteVehicleRecordPO.setDealerCode(inviteVehicleRecordDTO.getOwnerCode());

//        if (inviteVehicleRecordDTO.getIsself() != null && inviteVehicleRecordDTO.getIsself() == 1) {
//            inviteVehicleRecordPO.setSaId(String.valueOf(loginInfoDto.getUserId()));
//        }
        if (inviteVehicleRecordDTO.getIsNoDistribute() != null && inviteVehicleRecordDTO.getIsNoDistribute()) {
            inviteVehicleRecordPO.setIsNoDistribute(10041001);
        }
        if (inviteVehicleRecordDTO.getIsWaitDistribute() != null && inviteVehicleRecordDTO.getIsNoDistribute()) {
            inviteVehicleRecordPO.setIsWaitDistribute(10041001);
        }
        //查询主线索
        inviteVehicleRecordPO.setIsMain(1);
        List<InviteVehicleRecordPO> list = inviteVehicleRecordMapper.getInviteVehicleRecordCPort(page,
                inviteVehicleRecordPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteVehicleRecordDTO> result = list.stream().map(m -> m.transPoToDto(InviteVehicleRecordDTO.class)
            ).collect(Collectors.toList());
            page.setRecords(result);
            return page;
        }
    }

    @Override
    public List<InviteVehicleRecordPO> getWaitCloseRecordByVin(String vin) {
        LambdaQueryWrapper<InviteVehicleRecordPO> qw = new LambdaQueryWrapper<>();
        qw.eq(InviteVehicleRecordPO::getInviteType, CommonConstants.INVITE_TYPE_VI);
        qw.eq(InviteVehicleRecordPO::getIsDeleted, 0);
        qw.eq(InviteVehicleRecordPO::getOrderStatus, CommonConstants.ORDER_STATUS_II);
        qw.eq(InviteVehicleRecordPO::getVin, vin);
        return inviteVehicleRecordMapper.selectList(qw);
    }

    @Override
    public List<InviteVehicleRecordPO> selectByVin(String vin, boolean flag) {
        LambdaQueryWrapper<InviteVehicleRecordPO> qw = new LambdaQueryWrapper<>();
        if (flag) {
            qw.in(InviteVehicleRecordPO::getInviteType, CommonConstants.INVITE_TYPE_I, CommonConstants.INVITE_TYPE_II, CommonConstants.INVITE_TYPE_VI);
        } else {
            qw.in(InviteVehicleRecordPO::getInviteType, CommonConstants.INVITE_TYPE_I, CommonConstants.INVITE_TYPE_II, CommonConstants.INVITE_TYPE_VI, CommonConstants.INVITE_TYPE_XII);
        }
        qw.eq(InviteVehicleRecordPO::getIsDeleted, 0);
        qw.eq(InviteVehicleRecordPO::getOrderStatus, CommonConstants.ORDER_STATUS_II);
        qw.eq(InviteVehicleRecordPO::getVin, vin);
        return inviteVehicleRecordMapper.selectList(qw);
    }

    @Override
    public List<InviteVehicleRecordPO> selectTypeXIIByVin(String vin) {
        LambdaQueryWrapper<InviteVehicleRecordPO> qw = new LambdaQueryWrapper<>();
        qw.eq(InviteVehicleRecordPO::getInviteType, CommonConstants.INVITE_TYPE_XII);
        qw.eq(InviteVehicleRecordPO::getIsDeleted, 0);
        qw.eq(InviteVehicleRecordPO::getOrderStatus, CommonConstants.ORDER_STATUS_II);
        qw.eq(InviteVehicleRecordPO::getVin, vin);
        return inviteVehicleRecordMapper.selectList(qw);
    }

    @Override
    public int updateList(List<Long> upsr6) {
        update(upsr6);
        return upsr6.size();
    }

    @Override
    public List<InviteVehicleRecordPO> selectListByVin(String vin) {
        return inviteVehicleRecordMapper.selectListByVin(vin);
    }

    @Override
    public int updateAlertVocByVin(String vin) {
        return inviteVehicleRecordMapper.updateAlertVocByVin(vin);
    }


    /**
     * @param list
     */
    private void update(List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int numPerTimes = 200;
        if (list.size() <= numPerTimes) {
            batchRecordUpdate(list); //此处插入少于200条list
            logger.info(CommonConstants.LOG_S16);
        } else {
            int maxIndex = list.size();
            int maxTimes = maxIndex / numPerTimes;
            maxTimes += (maxIndex % numPerTimes) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimes) {
                int fromIndex = numPerTimes * currentTimes;
                int toIndex = fromIndex + numPerTimes;
                toIndex = toIndex > maxIndex ? maxIndex : toIndex;
                List<Long> subList = list.subList(fromIndex, toIndex);
                batchRecordUpdate(subList);//此处循环插入200条list
                logger.info(CommonConstants.LOG_S17, fromIndex, toIndex);
                currentTimes++;
            }
        }

    }

    /**
     * 批量修改
     *
     * @param list
     */
    private void batchRecordUpdate(List<Long> list) {
        inviteVehicleRecordMapper.updateList(list);
    }

    @Override
    public List<InviteVehicleRecordPO> selectVocLossByVin(String vin) {

        return inviteVehicleRecordMapper.selectVocLossByVin(vin);
    }

    @Override
    public List<InviteVehicleRecordPO> selectVocByVin(String vin) {
        return inviteVehicleRecordMapper.selectVocByVin(vin);
    }

    @Override
    public List<InviteVehicleRecordPO> selectTowListByVinAndCode(String vin, String code) {
        LambdaQueryWrapper<InviteVehicleRecordPO> qw = new LambdaQueryWrapper<>();
        qw.eq(InviteVehicleRecordPO::getVin, vin);
        qw.eq(InviteVehicleRecordPO::getDealerCode, code);
        qw.eq(InviteVehicleRecordPO::getIsDeleted, 0);
        qw.orderByDesc(InviteVehicleRecordPO::getId);
        qw.last("limit 1");
        return inviteVehicleRecordMapper.selectList(qw);
    }

    @Override
    public List<InviteVehicleRecordPO> selectLossByVin(String vin) {
        LambdaQueryWrapper<InviteVehicleRecordPO> qw = new LambdaQueryWrapper<>();
        qw.eq(InviteVehicleRecordPO::getVin, vin);
        qw.eq(InviteVehicleRecordPO::getIsDeleted, 0);
        qw.eq(InviteVehicleRecordPO::getInviteType, CommonConstants.INVITE_TYPE_VI);
        qw.eq(InviteVehicleRecordPO::getOrderStatus, CommonConstants.ORDER_STATUS_II);
        return inviteVehicleRecordMapper.selectList(qw);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LiteCrmClueResultDTO addClueByCrm(LiteCrmClueDTO dto) {
        logger.info("addClueByCrm,start,dto:{}", dto);
        //线索数据
        ClueDataDTO data = dto.getData();
        //保养灯线索信息
        MaintenanceLightInfoDTO maintenanceLightInfo = data.getMaintenanceLightInfo();
        if (Objects.isNull(maintenanceLightInfo)) {
            logger.info("maintenanceLightInfo is null");
            throw new ServiceBizException("maintenanceLightInfo is null");
        }
        //邀约类型
        String inviteType = maintenanceLightInfo.getInvitationType();
        if (StringUtils.isBlank(inviteType)) {
            logger.info("inviteType is null");
            throw new ServiceBizException("inviteType is null");
        }
        //车架号
        String vin = dto.getVehicleVin();
        //ICM线索ID
        Long icmId = dto.getId();
        //经销商信息
        List<DealerInfoDTO> dealerInfoList = data.getDealerInfo();
        logger.info("addClueByCrm,dealerInfoList:{}", dealerInfoList);
        if (CollectionUtils.isEmpty(dealerInfoList)) {
            logger.info("addClueByCrm,未获取到经销商信息");
            throw new ServiceBizException("经销商信息错误");
        }
        List<String> list = new ArrayList<>();
        IDistributedLock lock = DistributedLockUtil.getDistributedLock(
                RedisEnum.CLUE_DATA_SYNCHRO.getKey(icmId), RedisEnum.CLUE_DATA_SYNCHRO.getTimeoutMsecs());
        if (lock.acquire()) {//获取锁
            long startTime = new Date().getTime();
            try {
                //校验线索是否已存在
                int count = taskRecordService.selectCountByIcmId(icmId);
                if (count > 0) {
                    logger.info("addClueByCrm,重复添加:icmId:{}, count:{}", icmId, count);
                    return new LiteCrmClueResultDTO(Boolean.FALSE);
                }
                //线索下发经销商集合
                List<String> dealerCodes = dealerInfoList.stream().map(DealerInfoDTO::getDealerCode).distinct().collect(Collectors.toList());
                logger.info("addClueByCrm,dealerCodes:{}", dealerCodes);
                //白名单判断
                List<String> listCode = this.doWhitelistDetermination(dealerCodes, vin, Integer.parseInt(inviteType), true);
                if (CollectionUtils.isEmpty(listCode)) {
                    logger.info("addClueByCrm,listCode is Empty");
                    return new LiteCrmClueResultDTO(dealerCodes, Boolean.TRUE);
                }
                //获取非交集
                list = dealerCodes.stream()
                        .filter(code -> !listCode.contains(code))
                        .collect(Collectors.toList());
                logger.info("addClueByCrm,list:{}", list);
                //获取交集
                List<DealerInfoDTO> intersection = dealerInfoList.stream()
                        .filter(dealer -> listCode.contains(dealer.getDealerCode()))
                        .collect(Collectors.toList());
                logger.info("addClueByCrm,intersection:{}", intersection);
                for (DealerInfoDTO dealerInfo : intersection) {
                    this.addClueByCrmCreateLead(data, vin, icmId, dealerInfo);
                }
            } catch (Exception e) {
                logger.info("addClueByCrm,e:", e);
                throw new RuntimeException(e);
            } finally {
                try {
                    lock.release(); //释放
                    long endTime = new Date().getTime();
                    logger.info("addClueByCrm,lock:{}", endTime - startTime);
                } catch (Exception e) {
                    logger.error("addClueByCrm lock release e:", e);
                }
            }
        }
        logger.info("addClueByCrm,end");
        return new LiteCrmClueResultDTO(list, Boolean.TRUE);
    }

    private void addClueByCrmCreateLead(ClueDataDTO data, String vin, Long icmId, DealerInfoDTO dealerInfo) {
        //生成线索
        InviteVehicleRecordDTO recordDTO = new InviteVehicleRecordDTO();
        //封装参数
        this.getInviteVehicleRecordDTO(recordDTO, data, vin, dealerInfo);
        logger.info("addClueByCrm,recordDTO:{}", recordDTO);
        //添加线索数据
        Long recordId = this.addInviteVehicleRecord(recordDTO);
        logger.info("addClueByCrm,recordId:{}", recordId);
        Integer inviteType = recordDTO.getInviteType();
        String dealerCode = recordDTO.getDealerCode();
        Integer lossType = recordDTO.getLossType();
        Integer lossWarningType = recordDTO.getLossWarningType();
        logger.info("addClueByCrm,inviteType:{},dealerCode:{},lossWarningType:{}", inviteType, dealerCode, lossWarningType);
        //扩展表
        VocInviteVehicleTaskRecordPo recPo = new VocInviteVehicleTaskRecordPo();
        recPo.setRecordId(recordId);
        //线索类型
        recPo.setRecordType(recordDTO.getRecordType());
        //邀约类型
        recPo.setInviteType(inviteType);
        //流失类型
        recPo.setLossType(lossType);
        //流失预警类型
        recPo.setLossWarningType(lossWarningType);
        //日均里程
        recPo.setDailyMile(processDailyMile(recordDTO.getDailyMile()));
        //车架号
        recPo.setVin(vin);
        //icm线索Id
        recPo.setIcmId(icmId);
        //是否bev
        recPo.setBevFlag(recordDTO.getBevFlag());
        //动力类型
        recPo.setPowerType(recordDTO.getPowerType());
        //流失预警线索必传流失预警类型
        Optional.ofNullable(inviteType)
                .filter(InviteTypeEnum.LOST_WARNING.getIntCode()::equals)
                .ifPresent(type -> Objects.requireNonNull(lossWarningType, "lossWarningType is null"));
        //验证状态
        recPo.setVerifyStatus(VerifyTypeEnum.NOT_STARTED.getIntCode());
        //验证变更时间
        recPo.setVerifyTime(LocalDate.now());
        //返厂意向等级
        String returnIntentionLevel = data.getMaintenanceLightInfo().getRtnIntentionRating();
        recPo.setReturnIntentionLevel(processIntentionLevel(returnIntentionLevel));
        //添加扩展表数据
        taskRecordService.addRecord(recPo);
        //如果是流失线索,需要关闭对应的未完成的线索,流失类型:流失关闭,逾期关闭
        if (InviteTypeEnum.LOST_CUSTOMER.getIntCode().equals(inviteType)) {
            //进入关闭流程
            List<Integer> inviteList = Lists.newArrayList(InviteTypeEnum.FIRST_MAINTENANCE.getIntCode(),
                    InviteTypeEnum.REGULAR_MAINTENANCE.getIntCode(), InviteTypeEnum.LOST_WARNING.getIntCode());
            List<InviteVehicleRecordPO> poList = this.getOpenLeads(vin, dealerCode, inviteList);
            List<Long> ids = poList.stream().map(InviteVehicleRecordPO::getId).distinct().collect(Collectors.toList());
            //完成状态
            Objects.requireNonNull(lossType, "lossType is null");
            Integer orderStatus = lossType.equals(LossTypeEnum.CUSTOMER_LOST.getIntCode()) ?
                    CloseStatusEnum.LOST_CLOSED.getIntCode() : CloseStatusEnum.OVERDUE_CLOSED.getIntCode();
            logger.info("addClueByCrm,orderStatus:{}", orderStatus);
            this.closeLeadById(ids, orderStatus);
            //验证状态
            taskRecordService.updateVerifyStatusById(ids, VerifyTypeEnum.VERIFIED.getIntCode(), LocalDate.now());
            //发送MQ
            this.publishEvent(poList, VerifyTypeEnum.VERIFIED.getCode(), orderStatus.toString());
        }
    }

    private Integer processIntentionLevel(String returnIntentionLevel) {
        int defLevel = IntentionLevelEnum.THREE_STAR.getIntCode();
        int level = Optional.ofNullable(returnIntentionLevel)
                .map(str -> {
                    try {
                        return Integer.parseInt(str);
                    } catch (NumberFormatException e) {
                        return defLevel;
                    }
                }).orElse(defLevel);
        logger.info("processReturnIntentionLevel,ionLevel:{},level:{}", returnIntentionLevel, level);
        return level;
    }

    private String processDailyMile(String dailyMileString) {
        final String defMile = "0";
        String dailyMile = Optional.ofNullable(dailyMileString)
                .map(str -> {
                    try {
                        new BigDecimal(str);
                        return dailyMileString;
                    } catch (NumberFormatException | NullPointerException e) {
                        return defMile;
                    }
                })
                .orElse(defMile);
        logger.info("processDailyMile,dailyMileString:{},dailyMile:{}", dailyMileString, dailyMile);
        return dailyMile;
    }

    @Override
    public List<InviteVehicleRecordPO> getOpenLeads(String vin, String dealerCode, List<Integer> inviteList) {
        logger.info("getOpenLeads,vin:{},dealerCode:{},inviteList:{}", vin, dealerCode, inviteList);
        List<String> vinList = Lists.newArrayList(vin);
        return inviteVehicleRecordMapper.getOpenLeads(vinList, dealerCode, inviteList, null);
    }

    @Override
    public List<InviteVehicleRecordPO> getOpenLeads(List<String> vinList, List<Integer> inviteList, String createDate) {
        logger.info("getOpenLeads,vinList:{},inviteList:{}", vinList, inviteList);
        return Lists.partition(vinList,100).stream()
                .map(a->inviteVehicleRecordMapper.getOpenLeads(a,null,inviteList,createDate))
                        .flatMap(List::stream).collect(Collectors.toList());
    }

    @Override
    public void closeLeadById(List<Long> ids, Integer orderStatus) {
        logger.info("closeLeadById,vin:{},orderStatus:{}", ids, orderStatus);
        Lists.partition(ids, 500).forEach(id -> inviteVehicleRecordMapper.closeLeadById(id, orderStatus));
    }

    /**
     * 通过工单完成线索
     */
    @Override
    public void completeLeadByWorkOrder(String createDate) {
        logger.info("completeLeadByWorkOrder start:{}", createDate);
        //处理时间参数
        logger.info("准备获取锁");
        try {
            String completeClueKey = (String) redisClient.get(CacheKeyEnum.task_complete_clue_key.getKey());
            if (Objects.isNull(completeClueKey)) {
                redisClient.set(CacheKeyEnum.task_complete_clue_key.getKey(), "1", Math.toIntExact(CacheKeyEnum.task_complete_clue_key.getExpire()));
                //添加批量逻辑
                List<String> dateRangeList = new ArrayList<>();
                if (Objects.isNull(createDate)){
                    for (int i = 0; i <= 4; i++) {
                        Date dateBefore = DateUtil.offsetDay(DateUtil.parseDate(Utility.getDate()), -i);
                        dateRangeList.add(DateUtil.formatDate(dateBefore));
                    }
                }else{
                    dateRangeList=StrUtil.splitTrim(createDate, StrUtil.COMMA);
                }
                logger.info("date range list:{}",dateRangeList);
                dateRangeList.forEach(this::querySettledMaintenanceByTime);
            } else {
                logger.info("completeLeadByWorkOrder 存在运行中的工单完成线索线程任务");
            }
        } catch (Exception e) {
            logger.error("通过工单完成线索任务异常,error:",e);
        } finally {
            logger.info("通过工单完成线索任务 finally");
            redisClient.del(CacheKeyEnum.task_complete_clue_key.getKey());
        }
    }


    @Override
    public void querySettledMaintenanceByTime(String createDate) {
        logger.info("checkLeadCompletionByWorkOrder,start");
        if (createDate == null) {
            createDate = Utility.getDate();
        }
        logger.info("checkLeadCompletionByWorkOrder,createDate:{}", createDate);

        //查询保养工单的时间范围
        Integer offset = 1;
        CommonConfigDTO commonConfig = dmscloudServiceClient.getCommonConfig(CommonConstants.COMMON_CONFIG_COMPLETE_LEAD_TIME_FRAME);
        if (Objects.nonNull(commonConfig) && NumberUtil.isNumber(commonConfig.getConfigValue())) {
            offset = Integer.valueOf(commonConfig.getConfigValue());
        }
        String balanceTime = Utility.getBeforeDateSpecified(createDate, offset);
        logger.info("checkLeadCompletionByWorkOrder,balanceTime:{}", balanceTime);
        ClueParamVO vo = new ClueParamVO();
        vo.setBalanceTime(balanceTime);
        //查询保养工单
        List<RepairOrderVO> list = repairCommonClient.getSettledMaintenanceOrdersByTime(vo);
        if (CollectionUtils.isEmpty(list)) {
            logger.info("checkLeadCompletionByWorkOrder,CollectionUtils.isEmpty(list)");
            return;
        }
        List<String> vinList = new ArrayList<>();
        Map<String, RepairOrderVO> bevOrderMap = new HashMap<>();
        Map<String, RepairOrderVO> notBevOrderMap = new HashMap<>();
        this.fillVinAndOrderMap(list, vinList, bevOrderMap, notBevOrderMap);
        //查询需要完成关闭的线索
        List<Integer> inviteList = Lists.newArrayList(InviteTypeEnum.FIRST_MAINTENANCE.getIntCode(),
                InviteTypeEnum.REGULAR_MAINTENANCE.getIntCode(), InviteTypeEnum.LOST_CUSTOMER.getIntCode(),
                InviteTypeEnum.LOST_WARNING.getIntCode());
        List<InviteVehicleRecordPO> poListCode = this.getOpenLeads(vinList, inviteList, createDate);
        if (CollectionUtils.isEmpty(poListCode)) {
            logger.info("checkLeadCompletionByWorkOrder,CollectionUtils.isEmpty(poListCode)");
            return;
        }
        List<InviteVehicleRecordPO> poList = new ArrayList<>(poListCode.size());
        //白名单过滤
        poList = whiteListFilter(poListCode, poList);

        //填充线索修改数据
        List<InviteVehicleRecordPO> listUpPo = new ArrayList<>(poList.size());
        this.fillRecord(poList, bevOrderMap, notBevOrderMap, listUpPo);
        if (CollectionUtils.isEmpty(listUpPo)) {
            logger.info("checkLeadCompletionByWorkOrder,CollectionUtils.isEmpty(listUpPo)");
            return;
        }
        logger.info("checkLeadCompletionByWorkOrder,listUpPo:{}", listUpPo.size());
        //批量修改线索状态
        InviteVehicleRecordService proxy = ApplicationContextHelper.getBeanByType(InviteVehicleRecordService.class);
        proxy.addTransactionRecord(listUpPo);
        //发送MQ
        this.publishEvent(listUpPo, VerifyTypeEnum.PENDING_VERIFICATION.getCode());
        logger.info("checkLeadCompletionByWorkOrder,end");
    }

    /**
     * 保养工单对应的 vinList 和 orderMap
     */
    private void fillVinAndOrderMap(List<RepairOrderVO> list, List<String> vinList, Map<String, RepairOrderVO> bevOrderMap, Map<String, RepairOrderVO> notBevOrderMap) {
        for (RepairOrderVO order : list) {
            if (CollectionUtils.isEmpty(vinList) || !vinList.contains(order.getVin())) {
                vinList.add(order.getVin());
            }
            if (Objects.equals(order.getFunctionGroup(), FUNCTION_GROUP_2223)) {
                notBevOrderMap.put(order.getVin(), order);
            }
            if (Objects.equals(order.getFunctionGroup(), FUNCTION_GROUP_8724)) {
                bevOrderMap.put(order.getVin(), order);
            }
        }
    }

    /**
     * 白名单过滤
     */
    private List<InviteVehicleRecordPO> whiteListFilter(List<InviteVehicleRecordPO> poListCode, List<InviteVehicleRecordPO> poList) {
        List<String> listCode = getWhiteLists(CommonConstants.MOD_TYPE_91111011,CommonConstants.MOD_TYPE_91111015);
        logger.info("checkLeadCompletionByWorkOrder,listCode:{}", listCode);
        if (CollectionUtils.isNotEmpty(listCode)) {
            for (InviteVehicleRecordPO po : poListCode) {
                if (listCode.contains(po.getDealerCode())) {
                    poList.add(po);
                }
            }
        } else {
            poList = poListCode;
        }
        logger.info("checkLeadCompletionByWorkOrder,poList:{}", poList.size());
        return poList;
    }

    /**
     * 填充线索修改数据
     */
    private void fillRecord(List<InviteVehicleRecordPO> poList, Map<String, RepairOrderVO> bevOrderMap, Map<String, RepairOrderVO> notBevOrderMap, List<InviteVehicleRecordPO> listUpPo) {
        Long id;
        String dealerCode;
        InviteVehicleRecordPO upPo;
        RepairOrderVO orderVO;
        String vin;
        for (InviteVehicleRecordPO po : poList) {
            logger.info("fill record vin:{},{}",po.getVin(),po);
            vin = po.getVin();
            id = po.getId();
            dealerCode = po.getDealerCode();
            orderVO = Objects.equals(po.getBevFlag(), CommonConstants.BEV_LEAD) ? bevOrderMap.get(vin) : notBevOrderMap.get(vin);
            if (Objects.isNull(orderVO)) {
                logger.info("checkLeadCompletionByWorkOrder,Objects.isNull(orderVO)");
                continue;
            }
            if (Objects.isNull(dealerCode)) {
                logger.info("checkLeadCompletionByWorkOrder,Objects.isNull(dealerCode)");
                continue;
            }
            upPo = new InviteVehicleRecordPO();
            if (dealerCode.equals(orderVO.getDealerCode())) {
                logger.info("checkLeadCompletionByWorkOrder,自店完成,vin:{},id:{}", vin, id);
                upPo.setOrderStatus(CloseStatusEnum.SELF_COMPLETED.getIntCode());
            } else {
                logger.info("checkLeadCompletionByWorkOrder,他店完成,vin:{},id:{}", vin, id);
                upPo.setOrderStatus(CloseStatusEnum.OTHER_STORE_COMPLETED.getIntCode());
            }
            upPo.setOrderFinishDate(orderVO.getDeliveryDate());
            upPo.setRoNo(orderVO.getRoNo());
            upPo.setRoCreateDate(orderVO.getRoCreateDate());
            upPo.setRepairTypeCode(orderVO.getRepairTypeCode());
            upPo.setFinishDealerCode(orderVO.getDealerCode());
            upPo.setOutMileage(orderVO.getOutMileage());
            upPo.setId(id);
            upPo.setDealerCode(dealerCode);
            //非修改类参数
            upPo.setIcmId(po.getIcmId());
            upPo.setFollowStatus(po.getFollowStatus());
            upPo.setFunctionGroup(orderVO.getFunctionGroup());
            upPo.setSourceType(po.getSourceType());
            listUpPo.add(upPo);
        }
    }

    public List<FaultLightTopicDTO> convertRecordToDTO(List<InviteVehicleRecordPO> list, String verifyStatus, String orderStatus) {
        logger.info("convertRecordToDTO,start");
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<FaultLightTopicDTO> dtoList = new ArrayList<>(list.size());
        FaultLightTopicDTO dto;
        Date date = new Date();
        String followUpStatus;
        String bizStatus;
        Integer sourceType;
        for (InviteVehicleRecordPO record : list) {
            if (ObjectUtil.isEmpty(record)) {
                logger.info("convertRecordToDTO,ObjectUtil.isEmpty(record)");
                continue;
            }
            sourceType = record.getSourceType();
            if (ObjectUtil.isNotEmpty(sourceType) && !ObjectUtil.equal(sourceType, CommonConstants.CRM_LEAD)) {
                logger.info("convertRecordToDTO,continue,record:{}", record);
                continue;
            }
            followUpStatus = record.getFollowStatus() == null ? null : record.getFollowStatus().toString();
            bizStatus = record.getOrderStatus() == null ? null : record.getOrderStatus().toString();
            dto = new FaultLightTopicDTO();
            dto.setUpdateTime(date);
            dto.setId(record.getIcmId());
            dto.setFollowUpStatus(followUpStatus);
            dto.setBizStatus(orderStatus == null ? bizStatus : orderStatus);
            dto.setVerifyStatus(verifyStatus == null ? record.getVerifyStatus() : verifyStatus);
            dto.setLeadsType(LeadsTypeEnum.POST_LiGHT_LEADS.getCode());
            dto.setDealerCode(record.getDealerCode());
            dtoList.add(dto);
        }
        logger.info("convertRecordToDTO,end");
        return dtoList;
    }

    @Override
    public void addTransactionRecord(List<InviteVehicleRecordPO> listUpPo) {
        //更新线索
        Lists.partition(listUpPo, 500).forEach(this::completeBatchLeads);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void completeBatchLeads(List<InviteVehicleRecordPO> list) {
        //批量修改线索完成状态
        this.completeLeadsByIds(list);
        //批量更新线索日志
        this.completeBatchLeadsLog(list);
        //批量修改验证状态
        List<Long> listId = list.stream().map(InviteVehicleRecordPO::getId).distinct().collect(Collectors.toList());
        taskRecordService.updateVerifyStatusById(listId, VerifyTypeEnum.PENDING_VERIFICATION.getIntCode(), LocalDate.now());
    }

    private void completeLeadsByIds(List<InviteVehicleRecordPO> listUpPo) {
        //修改线索状态
        inviteVehicleRecordMapper.completeLeadsByIds(listUpPo);
        logger.info("completeLeadsByIds end,first param:{}", listUpPo.get(0));
    }


    /**
     * 批量记录线索变更日志
     */
    private void completeBatchLeadsLog(List<InviteVehicleRecordPO> recordList) {
        List<ClueCompleteLogPo> completeBatchLeadList = new ArrayList<>();
        for (InviteVehicleRecordPO record : recordList) {
            ClueCompleteLogPo completeLogPo = BeanUtil.copyProperties(record, ClueCompleteLogPo.class);
            completeLogPo.setCurrentStatus(CloseStatusEnum.INCOMPLETE.getIntCode())
                    .setCompleteStatus(record.getOrderStatus());
            completeBatchLeadList.add(completeLogPo);
        }
        clueCompleteLogService.saveBatch(completeBatchLeadList);
        logger.info("completeBatchLeadsLog end,first param:{}", recordList.get(0));
    }

    @Override
    public void closeOverdueLeads(String createDate) {
        logger.info("closeOverdueLeads,start");
        if (createDate == null) {
            createDate = Utility.getDate();
        }
        logger.info("closeOverdueLeads,createDate:{}", createDate);
        //计算时间,当前时间减三个月
        String endDate = Utility.getBeforeMonth(createDate, MONTH_3);
        String startDate = Utility.getBeforeMonth(endDate, MONTH_1);
        logger.info("closeOverdueLeads,startDate:{},endDate:{}", startDate, endDate);
        //查询需要关闭的首定保线索
        List<InviteVehicleRecordPO> listPo = findCluesToClose(startDate, endDate);
        if (CollectionUtils.isEmpty(listPo)) {
            logger.info("closeOverdueLeads,CollectionUtils.isEmpty(listPo)");
            return;
        }
        List<InviteVehicleRecordPO> list = new ArrayList<>(listPo.size());
        //查询白名单
        List<String> listCode = this.getWhiteList(CommonConstants.MOD_TYPE_91111011);
        List<String> listInCode = this.getWhiteList(CommonConstants.MOD_TYPE_91111015);
        logger.info("closeOverdueLeads,listCode:{}", listCode);
        logger.info("closeOverdueLeads,listInCode:{}", listInCode);
        if (CollectionUtils.isEmpty(listCode) && CollectionUtils.isEmpty(listInCode)) {
            //全网放开(过滤出CDP下发的线索)
            list = listPo.stream().filter(ObjectUtil::isNotEmpty)
                                  .filter(po -> Objects.equals(CommonConstants.CRM_LEAD, po.getSourceType()))
                                  .collect(Collectors.toList());
        } else {
            getListPo(listPo, list, listCode, listInCode);
        }
        List<Long> listId = list.stream().filter(ObjectUtil::isNotEmpty).map(InviteVehicleRecordPO::getId).distinct().collect(Collectors.toList());
        InviteVehicleRecordServiceImpl proxy = ApplicationContextHelper.getBeanByType(InviteVehicleRecordServiceImpl.class);
        proxy.closeLead(listId);
        //发送MQ
        this.publishEvent(list, VerifyTypeEnum.VERIFIED.getCode(), CloseStatusEnum.OVERDUE_CLOSED.getCode());
        logger.info("closeOverdueLeads,end");
    }

    private static void getListPo(List<InviteVehicleRecordPO> listPo, List<InviteVehicleRecordPO> list, List<String> listCode, List<String> listInCode) {
        Integer sourceType;
        for (InviteVehicleRecordPO po : listPo) {
            sourceType = po.getSourceType();
            if (listCode.contains(po.getDealerCode()) ||
                    (listInCode.contains(po.getDealerCode()) && Objects.equals(CommonConstants.CRM_LEAD, sourceType))) {
                list.add(po);
            }
        }
    }

    private static void getListClose(List<InviteVehicleRecordPO> listPo, List<InviteVehicleRecordPO> list, List<String> listCode) {
        for (InviteVehicleRecordPO po : listPo) {
            if (listCode.contains(po.getDealerCode())) {
                list.add(po);
            }
        }
    }

    private void publishEvent(List<InviteVehicleRecordPO> list) {
        publishEvent(list, null, null);
    }

    private void publishEvent(List<InviteVehicleRecordPO> list, String verifyStatus) {
        publishEvent(list, verifyStatus, null);
    }

    private void publishEvent(List<InviteVehicleRecordPO> list, String verifyStatus, String orderStatus) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Optional<List<FaultLightTopicDTO>> optionalListDTO = Optional.ofNullable(convertRecordToDTO(list, verifyStatus, orderStatus));
        optionalListDTO.ifPresent(listDTO -> eventPublisher.publishEvent(new LeadStatusChangeEvent(listDTO)));
    }

    @Transactional
    public void closeLead(List<Long> listId) {
        logger.info("closeLead,Transactional");
        //修改完成状态
        this.closeLeadById(listId, CloseStatusEnum.OVERDUE_CLOSED.getIntCode());
        //修改验证状态
        taskRecordService.updateVerifyStatusById(listId, VerifyTypeEnum.VERIFIED.getIntCode(), LocalDate.now());
    }

    private void getInviteVehicleRecordDTO(InviteVehicleRecordDTO recordDTO, ClueDataDTO data, String vin, DealerInfoDTO dealerInfo) {
        logger.info("getInviteVehicleRecordDTO,start");
        //经销商信息
        logger.info("getInviteVehicleRecordDTO,dealerInfo:{}", dealerInfo);
        Objects.requireNonNull(dealerInfo, "经销商信息错误");
        String dealerCode = dealerInfo.getDealerCode();
        recordDTO.setDealerCode(dealerCode);
        //车辆信息
        CarInfoDTO carInfo = data.getCarInfo();
        logger.info("getInviteVehicleRecordDTO,carInfo:{}", carInfo);
        if (Objects.nonNull(carInfo)) {
            recordDTO.setLicensePlateNum(carInfo.getLicencePlate());
            recordDTO.setModel(carInfo.getCarModelName());
            //日均
            recordDTO.setDailyMile(carInfo.getDailyMile());
        }

        //客户信息,NB自己查询,一年内次数最多的送修人,或者店端客户
        ContactInfoDTO contactInfo = this.getContactInfoDTO(vin);
        if (Objects.nonNull(contactInfo)) {
            recordDTO.setName(contactInfo.getCustomerName());
            recordDTO.setTel(contactInfo.getCustomerMobile());
            recordDTO.setSex(FaultGenderEnum.getNbCodeByNoCode(contactInfo.getGender()));
            //recordDTO.setAge();
        }
        //保养灯信息
        MaintenanceLightInfoDTO maintenanceLightInfo = data.getMaintenanceLightInfo();
        logger.info("getInviteVehicleRecordDTO,maintenanceLightInfo:{}", maintenanceLightInfo);
        if (Objects.nonNull(maintenanceLightInfo)) {
            //线索来源
            String sourceType = maintenanceLightInfo.getSourceType();
            //邀约类型
            String inviteType = maintenanceLightInfo.getInvitationType();
            //建议进厂时间
            Date adviceEnterFactory = maintenanceLightInfo.getAdviceEnterFactory();
            //流失类型
            String lossType = maintenanceLightInfo.getLossType();
            //流失预警类型
            String lossWarningType = maintenanceLightInfo.getLossWarningType();
            //是否bev
            this.fillBevFlag(recordDTO, maintenanceLightInfo, vin);
            recordDTO.setPowerType(maintenanceLightInfo.getPowerType());
            //校验类型
            examType(recordDTO, sourceType, inviteType, lossType, lossWarningType);
            //线索来源如果是保养灯线索,则建议进场时间等于线索下发时间加1天
            if (RecordTypeEnum.POST_ZERMATT_LEADS.getCode().equals(sourceType)) {
                LocalDate localDate = LocalDate.now().plusDays(1);
                adviceEnterFactory = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                logger.info("getInviteVehicleRecordDTO,adviceEnterFactory:{}", adviceEnterFactory);
            }
            Objects.requireNonNull(adviceEnterFactory, "建议进厂时间错误");
            recordDTO.setAdviseInDate(adviceEnterFactory);
            //查询上次保养时间
            com.yonyou.dmscus.customer.entity.dto.common.VehicleDTO vehicleDTO = vehicleService.selectDateByVin(vin);
            if (vehicleDTO != null) {
                Date lastInDate = InviteTypeEnum.FIRST_MAINTENANCE.getCode().equals(inviteType) ?
                        vehicleDTO.getLastMaintainDate() : vehicleDTO.getLastMaintenanceDate();
                recordDTO.setLastInDate(lastInDate);
            }
        }
        recordDTO.setSourceType(CommonConstants.CRM_LEAD);
        recordDTO.setIsMain(CommonConstants.MAINLINE_CABLE);
        recordDTO.setVin(vin);
        recordDTO.setOrderStatus(CommonConstants.ORDER_STATUS_II);
        recordDTO.setFollowStatus(CommonConstants.FOLLOW_STATUS_I);
        logger.info("getInviteVehicleRecordDTO,end,recordDTO:{}", recordDTO);
    }

    /**
     * 计算是否bev
     * <p>
     * 1、crm下发则取
     * 2、未下发则自动计算
     */
    private void fillBevFlag(InviteVehicleRecordDTO recordDTO, MaintenanceLightInfoDTO maintenanceLightInfo, String vin) {
        if (Objects.nonNull(maintenanceLightInfo.getPowerType())) {
            PowerTypeEnum.checkPowerType(maintenanceLightInfo.getPowerType());
            recordDTO.setBevFlag(Objects.equals(maintenanceLightInfo.getPowerType(), POWER_TYPE_BEV) ? 1 : 0);
            return;
        }
        String str1 = StrUtil.sub(vin, 5, 7);
        String str2 = StrUtil.sub(vin, 0, 3);
        if (StrUtil.isEmpty(vin) || StrUtil.isEmpty(str1) || StrUtil.isEmpty(str2)) {
            recordDTO.setBevFlag(0);
            return;
        }
        if ((Objects.equals(str2, "LPS") && CollUtil.newArrayList("EC", "EG").contains(str1))
                || CollUtil.newArrayList("ED", "EF").contains(str1)) {
            recordDTO.setBevFlag(1);
        } else {
            recordDTO.setBevFlag(0);
        }
    }


    /**
     * 保养灯查询客户信息(2023-11-23 弃用)
     *
     * @param vin
     * @param dealerCode
     * @return
     */
    @Nullable
    private ContactInfoDTO getContactInfoDTO(String vin, String dealerCode) {
        logger.info("getContactInfoDTO,查询客户信息vin:{},dealerCode:{}", vin, dealerCode);
        try {
            List<ContactInfoDTO> list = this.getCustomerProfile(dealerCode, vin);
            if (CollectionUtils.isNotEmpty(list)) {
                logger.info("getContactInfoDTO,list:{}", list.size());
                ContactInfoDTO dto = list.get(0);
                dto = dto == null ? this.getStoreOwner(dealerCode, vin) : dto;
                return dto;
            }
            return this.getStoreOwner(dealerCode, vin);
        } catch (Exception e) {
            logger.info("getContactInfoDTO,获取客户信息失败:", e);
            return null;
        }
    }

    /**
     * 保养灯查询客户信息(2023-11-23 启用)
     *
     * @param vin
     * @return
     */
    @Nullable
    private ContactInfoDTO getContactInfoDTO(String vin) {
        logger.info("getContactInfoDTO,vin:{}", vin);
        ContactInfoDTO contactInfoDTO = null;
        try {
            TmVehicleDTO tmVehicleDTO = businessPlatformService.getVehicleByVIN(vin);
            if (tmVehicleDTO == null || tmVehicleDTO.getCustomerId() == null) {
                logger.info("getContactInfoDTO,tmVehicleDTO == null || tmVehicleDTO.getCustomerId() == null");
                return null;
            }
            VehicleOwnerSelectByIdListDTO dto = new VehicleOwnerSelectByIdListDTO();
            dto.setList(Lists.newArrayList(tmVehicleDTO.getCustomerId()));
            ResponseDTO<List<VehicleOwnerDetailByIdListDTO>> responseDTO = midEndCustomerCenterClient.selectVehicleOwnerByOneIdList(dto);
            List<VehicleOwnerDetailByIdListDTO> list = null;
            if (responseDTO != null) {
                list = responseDTO.getData();
            }
            if (CollectionUtils.isNotEmpty(list)) {
                logger.info("getContactInfoDTO,list:{}", list.size());
                contactInfoDTO = new ContactInfoDTO();
                VehicleOwnerDetailByIdListDTO vehicleDto = list.get(list.size() - 1);
                contactInfoDTO.setCustomerName(vehicleDto.getName());
                contactInfoDTO.setCustomerMobile(vehicleDto.getMobile());
                contactInfoDTO.setGender(ObjectUtils.toString(vehicleDto.getGender(), null));
            }
        } catch (Exception e) {
            logger.error("getContactInfoDTO,Exception:", e);
            return null;
        }
        logger.info("getContactInfoDTO,contactInfoDTO:{}", contactInfoDTO);
        return contactInfoDTO;
    }

    private List<VehicleOwnerDetailByIdListDTO> selectVehicleOwnerByIdList(VehicleOwnerSelectByIdListDTO dto) {
        logger.info("selectVehicleOwnerByIdList,start");
        List<VehicleOwnerDetailByIdListDTO> dtoList;
        if (ObjectUtil.isEmpty(dto)) {
            logger.info("selectVehicleOwnerByIdList,ObjectUtil.isEmpty(dto)");
            return null;
        }
        List<Long> listLong = dto.getList();
        if (CollectionUtils.isEmpty(listLong)) {
            logger.info("selectVehicleOwnerByIdList,CollectionUtils.isEmpty(listLong)");
            return null;
        }
        logger.info("selectVehicleOwnerByIdList,listLong:{}", listLong.size());
        ResponseDTO<List<VehicleOwnerDetailByIdListDTO>> list;
        try {
            list = midEndCustomerCenterClient.selectVehicleOwnerByOneIdList(dto);
        } catch (Exception e) {
            logger.info("selectVehicleOwnerByIdList,e:{}", e);
            throw new RuntimeException("selectVehicleOwnerByIdList,fail");
        }
        dtoList = list.getData();
        logger.info("selectVehicleOwnerByIdList,end");
        return dtoList;
    }


    @Override
    public List<ContactInfoDTO> queryContactInfo(String dealerCode, String vin) {
        logger.info("getContactInfo,dealerCode:{},vin:{}", dealerCode, vin);
        List<ContactInfoDTO> list = new ArrayList<>();
        //送修人
        List<ContactInfoDTO> listProfile = this.getCustomerProfile(dealerCode, vin);
        if (CollectionUtils.isNotEmpty(listProfile)) {
            logger.info("getContactInfo,listProfile:{}", listProfile.size());
            list = listProfile;
        }
        //店端客户
        Optional.ofNullable(this.getStoreOwner(dealerCode, vin))
                .ifPresent(list::add);
        logger.info("getContactInfo,list:{}", list.size());
        return list;
    }

    @Override
    public void isValidationSuccessful() {
        logger.info("isValidationSuccessful,start");
        //维护历史线索的验证状态
        try {
            taskRecordService.markHistoryDataAsValid();
        } catch (Exception e) {
            logger.info("isValidationSuccessful,维护历史线索的验证状态,e:",e);
        }
        //查询完成时间满一个月且验证状态为待验证的线索
        List<InviteVehicleRecordPO> list = this.getExpiredPendingLeads();
        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            logger.info("isValidationSuccessful,CollectionUtils.isEmpty(list)");
            return;
        }
        logger.info("isValidationSuccessful,list:{}", list.size());
        //获取车架号集合
        List<String> listVin = list.stream().map(InviteVehicleRecordPO::getVin).distinct().collect(Collectors.toList());
        Map<String, List<InviteVehicleRecordPO>> mapVinIds = list.stream().filter(Objects::nonNull)
                .filter(po -> org.apache.commons.lang.StringUtils.isNotBlank(po.getVin()) && Objects.nonNull(po.getId()))
                .collect(Collectors.groupingBy(InviteVehicleRecordPO::getVin));
        logger.info("isValidationSuccessful,listVin:{}", listVin.size());
        Lists.partition(listVin,500).forEach(vines -> verifyEvidence(vines, mapVinIds));
        logger.info("isValidationSuccessful,end");
    }

    private void verifyEvidence(List<String> listVin, Map<String, List<InviteVehicleRecordPO>> mapVinIds) {
        OnlineOfflineDTO dto = new OnlineOfflineDTO();
        dto.setVehicle_vin(listVin);
        List<OnlineOfflineResultDTO> dtoList;
        List<InviteVehicleRecordPO> pos;
        int pageNum = 1;
        int pageSize = 100;
        do {
            dto.setPageNum(pageNum);
            dto.setPageSize(pageSize);
            logger.info("isValidationSuccessful,dto:{},pageNum:{}", dto, pageNum);
            dtoList = externalAPIService.onlineStatusQuery(dto);
            logger.info("isValidationSuccessful,uat环境日志,pro删除,dtoList:{}", dtoList);
            pos = taskRecordService.verifyEvidence(dtoList, mapVinIds);
            //发送MQ
            publishEvent(pos, VerifyTypeEnum.VERIFIED.getCode());
            pageNum += 1;
        } while (CollectionUtils.isNotEmpty(dtoList) && dtoList.size() == pageSize);
    }

    @Override
    public List<InviteVehicleRecordPO> getExpiredPendingLeads() {
        return inviteVehicleRecordMapper.getExpiredPendingLeads();
    }

    @Override
    public List<InviteClueResultDTO> selectInviteClue(Page<InviteClueResultDTO> page, InviteClueParamDTO dto) {
        return inviteVehicleRecordMapper.selectInviteClue(page, dto);
    }

    @Override
    public InviteClueResultDTO selectNewInviteClue(String vin,Integer leadsType, String dealerCode) {
        return inviteVehicleRecordMapper.selectNewInviteClue(vin, leadsType, dealerCode);
    }


    @Override
    public void batchInsertExtendedData() {
        logger.info("batchInsertExtendedData,start");
        List<VocInviteVehicleTaskRecordPo> listPo = new ArrayList<>();
        List<InviteClueResultDTO> list = inviteVehicleRecordMapper.findMissingExtendedDataByClue();
        if (!CollectionUtils.isEmpty(list)) {
            logger.info("batchInsertExtendedData,list:{}", list.size());
            VocInviteVehicleTaskRecordPo vocPo;
            //补偿扩展表数据
            for (InviteClueResultDTO resultDTO : list) {
                //扩展表
                vocPo = new VocInviteVehicleTaskRecordPo();
                vocPo.setRecordId(resultDTO.getId());
                vocPo.setRecordType(RecordTypeEnum.POST_SALE.getIntCode());
                vocPo.setVin(resultDTO.getVin());
                vocPo.setInviteType(resultDTO.getInviteType());
                listPo.add(vocPo);
            }
        }

        //批量新增
        if (CollectionUtils.isNotEmpty(listPo)) {
            logger.info("batchInsertExtendedData,listPo:{}", listPo.size());
            taskRecordService.addAll(listPo);
        }
        logger.info("batchInsertExtendedData,end");
    }

    @Override
    public List<InviteVehicleRecordPO> selectClueByVinAndInviteType(String vin, boolean flag) {
        logger.info("selectClueByVinAndInviteType,vin:{},flag:{}", vin, flag);
        List<InviteVehicleRecordPO> list = inviteVehicleRecordMapper.selectClueByVinAndInviteType(vin, flag);
        logger.info("selectClueByVinAndInviteType,num:{}", list.size());
        return list;
    }

    @Override
    public List<String> doWhitelistDetermination(List<String> dealerCodes, String vin, Integer inviteType, boolean flag) {
        logger.info("doWhitelistDetermination, dealerCodes:{}, vin:{}, inviteType:{}, flag:{}", dealerCodes, vin, inviteType, flag);
        // 查询白名单
        List<String> whiteList = getWhiteList(CommonConstants.MOD_TYPE_91111011);
        logger.info("doWhitelistDetermination, whiteList:{}", whiteList);
        List<String> inWhiteList = getWhiteList(CommonConstants.MOD_TYPE_91111015);
        logger.info("doWhitelistDetermination, inWhiteList:{}", inWhiteList);

        // 初始化逻辑处理
        List<String> listInWhite = doInWhitelistDeter(dealerCodes, vin, inviteType, flag);
        if (CollectionUtils.isEmpty(listInWhite)) {
            listInWhite = Collections.emptyList();
        }
        logger.info("doWhitelistDetermination, dealerCodes:{}", dealerCodes);
        logger.info("doWhitelistDetermination, listInWhite:{}", listInWhite);
        List<String> listWhite = null;
        // 保养灯百名单逻辑处理
        if(CollectionUtils.isNotEmpty(whiteList) && CollectionUtils.isNotEmpty(dealerCodes)){
            listWhite = doWhitelistDeter(dealerCodes, vin, inviteType, flag);
        }
        if (CollectionUtils.isEmpty(listWhite)) {
            listWhite = Collections.emptyList();
        }
        logger.info("doWhitelistDetermination, listWhite:{}", listWhite);
        //整合
        List<String> resultList = Stream.concat(listWhite.stream(), listInWhite.stream()).distinct().collect(Collectors.toList());
        logger.info("doWhitelistDetermination,end,resultList:{}", resultList);
        return resultList;
    }

    //保养灯百名单逻辑
    private List<String> doWhitelistDeter(List<String> dealerCodes, String vin, Integer inviteType, boolean flag) {
        logger.info("doWhitelistDeter, dealerCodes:{}, vin:{}, inviteType:{}, flag:{}", dealerCodes, vin, inviteType, flag);
        // 查询白名单
        List<String> whiteList = getWhiteList(CommonConstants.MOD_TYPE_91111011);
        if(CollectionUtils.isEmpty(whiteList)){
            logger.info("doWhitelistDeter, CollectionUtils.isEmpty(whiteList)");
            //全网开通拦截逻辑:
            //查询该vin在当前店最新的一条线索是否为cdp下发的,如果满足条件则正常下发,否则进行拦截
            return dealerCodes;
        }
        logger.info("doWhitelistDeter, whiteList:{}", whiteList);
        // 转换为HashSet加速判断元素是否存在
        Set<String> whiteListSet = new HashSet<>(whiteList);
        // 经销商集合
        List<String> codes;
        if (flag) {
            // true 表示白名单 > CDP下发的线索
            codes = dealerCodes.stream()
                    .filter(whiteListSet::contains)
                    .collect(Collectors.toList());
        } else {
            // false 表示黑名单 > NB自己下发的线索
            codes = dealerCodes.stream()
                    .filter(code -> !whiteListSet.contains(code))
                    .collect(Collectors.toList());
        }
        logger.info("doWhitelistDeter, codes:{}", codes);
        List<String> list = getCodes(vin, inviteType, flag, codes);
        logger.info("doWhitelistDeter, list:{}", list);
        return list;
    }

    //初始化逻辑
    private List<String> doInWhitelistDeter(List<String> dealerCodes, String vin, Integer inviteType, boolean flag) {
        logger.info("doInWhitelistDeter, dealerCodes:{}, vin:{}, inviteType:{}, flag:{}", dealerCodes, vin, inviteType, flag);
        // 查询白名单
        List<String> whiteList = getWhiteList(CommonConstants.MOD_TYPE_91111015);
        List<String> intersection;
        if(CollectionUtils.isEmpty(whiteList)){
            logger.info("doInWhitelistDeter.isEmpty(whiteList)");
            //全网开通
            intersection = dealerCodes;
        }else{
            intersection = new ArrayList<>(dealerCodes);
            //判断是否在白名单
            intersection.retainAll(whiteList);
            logger.info("doInWhitelistDeter, intersection:{}", intersection);
            //重置白名单,防止出现逻辑冲突
            dealerCodes.removeAll(whiteList);
            logger.info("doInWhitelistDeter,dealerCodes:{}",dealerCodes);
        }
        if(CollectionUtils.isEmpty(intersection)){
            logger.info("doInWhitelistDeter.isEmpty(intersection)");
            return null;
        }
        List<String> codes = getCodes(vin, inviteType, flag, intersection);
        if(CollectionUtils.isEmpty(codes)){
            logger.info("CollectionUtils.isEmpty(codes)");
            return null;
        }
        logger.info("CollectionUtils.isNotEmpty(codes),codes:{}", codes);
        //NB下发线索,首定保不许下发,流失预警,流失客户需要查询来源
        if (!flag && (InviteTypeEnum.FIRST_MAINTENANCE.getIntCode().equals(inviteType) ||
                InviteTypeEnum.REGULAR_MAINTENANCE.getIntCode().equals(inviteType))) {
            logger.info("doInWhitelistDeter,命中白名单,NB下发线索,首定保不再下发");
            return null;
        }
        List<String> list = enableGlobalLeadInterception(codes, vin, inviteType, flag);
        logger.info("doInWhitelistDeter,end,list:{}", list);
        return list;
    }

    private List<String> getCodes(String vin, Integer inviteType, boolean flag, List<String> codes) {
        //查询已有的未完成线索
        List<InviteVehicleRecordPO> listPo = this.selectClueByVinAndInviteType(vin, flag);
        logger.info("doWhitelistDeter,getCodes, listPo:{}", listPo);
        List<Integer> listType = Lists.newArrayList(InviteTypeEnum.FIRST_MAINTENANCE.getIntCode(),
                InviteTypeEnum.REGULAR_MAINTENANCE.getIntCode(), InviteTypeEnum.LOST_WARNING.getIntCode());
        if (InviteTypeEnum.LOST_CUSTOMER.getIntCode().equals(inviteType)) {
            logger.info("doWhitelistDeter,getCodes, inviteType:{}", inviteType);
            // 流失线索-自店的首定保,流失预警可以直接下发
            listPo.removeIf(po -> codes.contains(po.getDealerCode()) && listType.contains(po.getInviteType()));
        }
        List<String> list = listPo.stream().distinct().map(InviteVehicleRecordPO::getDealerCode).collect(Collectors.toList());
        //取交集
        List<String> distinctCodes = new ArrayList<>(codes);
        distinctCodes.removeAll(list);
        int size = list.size();
        logger.info("doWhitelistDeter,getCodes, distinctCodes:{}", distinctCodes);
        if(size > 1){
            //size大于1,表示不能再下发线索,返回null的数据
            return null;
        } else if (size == 0) {
            //size 等于0,表示可以正常下发线索
            return distinctCodes;
        } else {
            //size 等于1,表示下发了一条数据,还可再下发一条数据
            return judgeDealerCode(distinctCodes, vin);
        }
    }

    /**
     * 全网开通线索拦截
     */
    private List<String> enableGlobalLeadInterception(List<String> dealerCodes, String vin, Integer inviteType, boolean flag){
        logger.info("enableGlobalLeadInterception,dealerCodes:{},vin:{},inviteType:{},flag:{}", dealerCodes, vin, inviteType, flag);
        if(CollectionUtils.isEmpty(dealerCodes)){
            logger.info("enableGlobalLeadInterception,CollectionUtils.isEmpty(dealerCodes)");
            return dealerCodes;
        }
        InviteVehicleRecordPO po;
        Integer sourceType;
        List<String> codes = new ArrayList<>();
        //dealerCodes最多2条数据
        for (String dealerCode : dealerCodes) {
            //查询最新的一条线索
            po = fetchLatestAppointmentLead(
                    CollUtil.newArrayList(
                    InviteTypeEnum.FIRST_MAINTENANCE.getIntCode(),
                    InviteTypeEnum.REGULAR_MAINTENANCE.getIntCode(),
                    InviteTypeEnum.LOST_CUSTOMER.getIntCode(),
                    InviteTypeEnum.LOST_WARNING.getIntCode()),
                    CollUtil.newArrayList(CloseStatusEnum.INCOMPLETE.getIntCode()),
                    vin,
                    dealerCode);
            logger.info("enableGlobalLeadInterception,po:{}", po);
            if(flag){
                getCdpCodes(codes, po, dealerCode);
            }else{
                getNbCodes(codes, po, dealerCode);
            }
        }
        logger.info("enableGlobalLeadInterception, dealerCodes:{}", codes);
        return codes;
    }

    private void getCdpCodes(List<String> codes, InviteVehicleRecordPO po, String dealerCode){
        if(ObjectUtil.isEmpty(po)){
            logger.info("enableGlobalLeadInterception,ObjectUtil.isEmpty(po):{}", dealerCode);
            codes.add(dealerCode);
            return;
        }
        Integer sourceType = po.getSourceType();
        if (Objects.equals(CommonConstants.CRM_LEAD, sourceType)) {
            logger.info("enableGlobalLeadInterception, codes.add(dealerCode):{}", dealerCode);
            codes.add(dealerCode);
        }
    }

    private void getNbCodes(List<String> codes, InviteVehicleRecordPO po, String dealerCode){
        if(ObjectUtil.isEmpty(po)){
            logger.info("enableGlobalLeadInterception,ObjectUtil.isEmpty(po):{}", dealerCode);
            return;
        }
        Integer sourceType = po.getSourceType();
        //上一条未完成的线索不是CDP下发并且不是流失客户类型的线索,才允许下发流失客户线索
        if (!Objects.equals(CommonConstants.CRM_LEAD, sourceType) &&
                !InviteTypeEnum.LOST_CUSTOMER.getIntCode().equals(po.getInviteType())) {
            logger.info("enableGlobalLeadInterception, codes.add(dealerCode):{}", dealerCode);
            codes.add(dealerCode);
        }
    }

    private List<String> judgeDealerCode(List<String> distinctCodes, String vin){
        logger.info("judgeDealerCode,codes:{}",distinctCodes);
        if (CollectionUtils.isEmpty(distinctCodes)){
            logger.info("judgeDealerCode,CollectionUtils.isEmpty(distinctCodes)");
            return null;
        }
        logger.info("judgeDealerCode, distinctCodes:{}", distinctCodes);
        int size = distinctCodes.size();
        if (size == 1) {
            return distinctCodes;
        } else {
            return Lists.newArrayList(workOrderTimeChecker(distinctCodes, vin));
        }
    }

    private String workOrderTimeChecker(List<String> distinctCodes, String vin) {
        logger.info("workOrderTimeChecker,distinctCodes:{}, vin:{}", distinctCodes, vin);
        //查询工单返回时间最近的数据
        String code = repairCommonClient.workOrderTimeChecker(distinctCodes, vin);
        logger.info("workOrderTimeChecker,code:{}", code);
        //如果没有工单,下发第一家
        if (Objects.isNull(code)) {
            return distinctCodes.get(0);
        }
        return code;
    }

    //获取送修人
    private List<ContactInfoDTO> getCustomerProfile(String dealerCode, String vin) {
        logger.info("getCustomerProfile,dealerCode:{},vin:{}", dealerCode, vin);
        //查询工单获取送修人
        ClueParamVO vo = new ClueParamVO();
        vo.setDealerCode(dealerCode);
        vo.setVin(vin);
        List<RepairOrderVO> listVO = repairCommonClient.getSettledWorkOrdersByDealerAndVIN(vo);
        if (CollectionUtils.isEmpty(listVO)) {
            logger.info("getCustomerProfile,CollectionUtils.isEmpty(listVO)");
            return null;
        }
        logger.info("getCustomerProfile,listVO:{}", listVO.size());
        //获取最近的结算时间
        Date endTime = listVO.get(0).getBalanceTime();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(endTime);
        calendar.add(Calendar.YEAR, -1);
        Date startTime = calendar.getTime();
        logger.info("getCustomerProfile,endTime:{},startTime:{}", endTime, startTime);
        //获取一年内的工单
        List<RepairOrderVO> filteredList = listVO.stream()
                .filter(dto -> {
                    Date balanceTime = dto.getBalanceTime();
                    if (Objects.isNull(balanceTime)) {
                        return false;
                    }
                    boolean afterStartDate = balanceTime.after(startTime) || balanceTime.equals(startTime);
                    boolean beforeEndDate = balanceTime.before(endTime) || balanceTime.equals(endTime);
                    return afterStartDate && beforeEndDate;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredList)) {
            logger.info("getCustomerProfile,CollectionUtils.isEmpty(filteredList)");
            return null;
        }
        logger.info("getCustomerProfile,filteredList:{}", filteredList.size());
        //按照名称分组(名称,对象)
        List<ContactInfoDTO> list = new ArrayList<>(filteredList.stream()
                .collect(Collectors.groupingBy(RepairOrderVO::getDeliverer,
                        Collectors.collectingAndThen(Collectors.toList(), this::getContactInfo)))
                .values());
        //排序
        if (CollectionUtils.isNotEmpty(list)) {
            logger.info("getCustomerProfile,list:{}", list.size());
            //按次数排序
            list = list.stream()
                    .sorted(Comparator.comparingInt(ContactInfoDTO::getRepairNum).reversed()
                            .thenComparing(ContactInfoDTO::getBalanceTime, Comparator.reverseOrder()))
                    .collect(Collectors.toList());
        }
        return list;
    }

    private ContactInfoDTO getContactInfo(List<RepairOrderVO> list) {
        //取送修次数最大的数据,并且获取送修次数
        int count = list.size();
        RepairOrderVO latestRepairOrder = list.stream()
                .max(Comparator.comparing(RepairOrderVO::getBalanceTime)).orElse(null);
        Objects.requireNonNull(latestRepairOrder, "latestRepairOrder is null");
        ContactInfoDTO dto = new ContactInfoDTO();
        //送修人
        dto.setCustomerName(latestRepairOrder.getDeliverer());
        //送修性别
        dto.setGender(latestRepairOrder.getDelivererGender());
        //送修手机号
        dto.setCustomerMobile(latestRepairOrder.getDelivererMobile());
        //送修次数
        dto.setRepairNum(count);
        //维修时间
        dto.setBalanceTime(latestRepairOrder.getBalanceTime());
        //客户类型
        dto.setTypeCode(CustomerTypeEnum.REPAIR_SENDER.getCode());
        return dto;
    }

    //获取店端车主信息
    private ContactInfoDTO getStoreOwner(String dealerCode, String vin) {
        logger.info("getStoreOwner,dealerCode:{},vin:{}", dealerCode, vin);
        OwnerInfoResultsVo vo = repairCommonClient.getOwnerInfo(dealerCode, vin);
        logger.info("getStoreOwner,OwnerInfoResultsVo:{}", vo);
        if (Objects.isNull(vo)) {
            logger.info("getStoreOwner,Objects.isNull(vo)");
            return null;
        }
        ContactInfoDTO dto = new ContactInfoDTO();
        String customerMobile = Optional.ofNullable(vo.getMobileSecond())
                .orElse(vo.getMobileFirst());
        dto.setCustomerMobile(customerMobile);
        dto.setCustomerName(vo.getOwnerName());
        //客户类型
        dto.setTypeCode(CustomerTypeEnum.SHOP_CLIENT.getCode());
        Optional.ofNullable(vo.getGender()).ifPresent(gender -> dto.setGender(gender.toString()));
        logger.info("getStoreOwner,ContactInfoDTO:{}", dto);
        return dto;
    }

    //校验各种类型
    private void examType(InviteVehicleRecordDTO recordDTO, String sourceType, String inviteType, String lossType, String lossWarningType) {
        //校验线索类型
        if (!InviteTypeEnum.containsCode(inviteType)) {
            logger.error("getInviteVehicleRecordDTO,inviteType:{}", inviteType);
            throw new ServiceBizException("邀约类型错误,请确认");
        }
        if (!RecordTypeEnum.containsCode(sourceType)) {
            logger.error("getInviteVehicleRecordDTO,sourceType:{}", sourceType);
            throw new ServiceBizException("保养灯线索来源错误,请确认");
        }
        recordDTO.setRecordType(Integer.valueOf(sourceType));
        recordDTO.setInviteType(Integer.valueOf(inviteType));
        Optional.ofNullable(lossType)
                .filter(LossTypeEnum::containsCode)
                .map(Integer::valueOf)
                .ifPresent(recordDTO::setLossType);
        Optional.ofNullable(lossWarningType)
                .filter(LossWarTypeEnum::containsCode)
                .map(Integer::valueOf)
                .ifPresent(recordDTO::setLossWarningType);
    }

    @Override
    public List<String> getWhiteList(Integer modType) {
        logger.info("getWhiteList,modType:{}", modType);
        List<String> list = whitelistQueryService.selectWhiteListString(modType, 0);
        if (list == null) {
            list = Collections.emptyList();
        }
        logger.info("getWhiteList,list:{}", list);
        return list;
    }

    @Override
    public List<String> getWhiteLists(Integer... modTypes){
        List<String> whiteLists = new ArrayList<>();
        for (Integer modType : modTypes) {
            List<String> modTypeWhiteList = this.getWhiteList(modType);
            whiteLists.addAll(modTypeWhiteList);
        }
        return whiteLists;
    }

    @Override
    public List<InviteVehicleRecordPO> searchClues(List<String> list) {
        logger.info("searchClues,list:{}", list);
        List<InviteVehicleRecordPO> codes = inviteVehicleRecordMapper.searchClues(list);
        logger.info("searchClues,num:{}", codes.size());
        return codes;
    }

    @Override
    public void cdpUpdateReturnIntentionLevel() {
        //锁
        IDistributedLock distributedLock = DistributedLockUtil.getDistributedLock(CommonConstants.REDIS_CPD_KEY, 1000 * 60 * 3);
        if (!distributedLock.acquire()) {
            logger.info("cdpUpdateReturnIntentionLevel 获取锁失败");
            return;
        }
        try {
            //获取所有经销商code
            List<String> dealerCodeList = queryInviteVehicleRecordDealerCodeList();
            List<List<String>> dealerCodes = Lists.partition(dealerCodeList, 10);
            dealerCodes.stream().forEach(codes -> {
                //分批处理一次两千条 每小时大约处理50*60*60=180000
                Integer partialLimit = 500;
                Integer index = 1;
                logger.info("cdpUpdateReturnIntentionLevel 25号拉取cdp更新返厂意向登记codes:{}", codes);
                //获取总条数
                int count = queryUpdateReturnIntentionLevelCount(partialLimit, index, codes);
                //计算总页数
                int pages = count % partialLimit != 0 ? count / partialLimit + 1 : count / partialLimit;
                logger.info("cdpUpdateReturnIntentionLevel 总条数：{},总页数：{}", count, pages);
                for (int i = 0; i < pages; i++) {
                    logger.info("cdpUpdateReturnIntentionLevel 当前查询页:{}", pages);
                    IPage<InviteVehicleRecordPO> inviteVehicleRecordPOIPage = queryUpdateReturnIntentionLevelPage(partialLimit, index + i, codes);
                    Set<String> vinList = inviteVehicleRecordPOIPage.getRecords().stream().filter(e -> !StringUtils.isNullOrEmpty(e.getVin())).map(InviteVehicleRecordPO::getVin).collect(Collectors.toSet());
                    logger.info("IntentionLevel vins{}", vinList);
                    List<List<String>> vinLists = Lists.partition(new ArrayList<>(vinList), 50);
                    InviteVehicleRecordServiceImpl proxy = ApplicationContextHelper.getBeanByType(InviteVehicleRecordServiceImpl.class);
                    //todo 线程池开启数任务数根据处理的数据量vinLists.size 设置线程数
                    CompletableFuture<Boolean>[] futures = vinLists.stream()
                            .map(vins -> CompletableFuture.supplyAsync(() -> proxy.updateReturnIntentionLevel(vins, false), asyncUpdateReturnIntentionLevel))
                            .toArray(CompletableFuture[]::new);
                    CompletableFuture<Void> allOf = CompletableFuture.allOf(futures);
                    // 等待所有异步操作完成
                    allOf.join();
                }
            });
        } catch (Exception e) {
            logger.error("cdpUpdateReturnIntentionLevel 异常:{}",e);
        }finally {
            distributedLock.release(); //释放锁
        }
    }

    @Override
    public void doReturnIntentionLevel(List<String> vinList){
        updateReturnIntentionLevel(vinList, true);
    }

    @Override
    public int updateIsCreateInviteByVin(String vin) {
        return inviteVehicleTaskMapper.updateIsCreateInviteByVin(vin);
    }

    @Override
    public int updateDealerCodeByVin(String vin) {
        return inviteVehicleRecordMapper.updateDealerCodeByVin(vin);
    }


    @Transactional
    public Boolean updateReturnIntentionLevel(List<String> vins,boolean isRetry) {
        try {
            List<CdpTagTaskParameterDto> cdpTagTaskParameterDtos = cdpTagTaskService.getVins(vins);
            Map<String, List<CdpTagTaskParameterDto>> cdpTagTaskMap = cdpTagTaskParameterDtos.stream().filter(e -> !StringUtils.isNullOrEmpty(e.getReturnIntentionLevel())).collect(Collectors.groupingBy(CdpTagTaskParameterDto::getReturnIntentionLevel));
            cdpTagTaskMap.forEach((key, val) -> {
                setReturnIntentionLevelByVins(key, val);
            });
        }catch (Exception e){
            logger.error("updateReturnIntentionLevel更新cdp返厂意向异常:",e);
        }
        return true;
    }


    /**
     * 根据车架号修改返厂意向等级
     */
    private void setReturnIntentionLevelByVins(String returnIntentionLevel, List<CdpTagTaskParameterDto> cdpTagTaskParameterDtoList) {
        logger.info("setReturnIntentionLevelByVins key:{} val:{}",returnIntentionLevel,cdpTagTaskParameterDtoList);
        List<String> vinList = cdpTagTaskParameterDtoList.stream().map(CdpTagTaskParameterDto::getVin).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vinList)){
            return;
        }
        LambdaQueryWrapper<InviteVehicleRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InviteVehicleRecordPO::getIsDeleted, 0);
        queryWrapper.in(InviteVehicleRecordPO::getInviteType, CommonConstants.INVITE_TYPE_I, CommonConstants.INVITE_TYPE_II, CommonConstants.INVITE_TYPE_VI, CommonConstants.INVITE_TYPE_XII);
        queryWrapper.eq(InviteVehicleRecordPO::getOrderStatus, CommonConstants.ORDER_STATUS_II);
        queryWrapper.in(InviteVehicleRecordPO::getVin,vinList);
        List<Long> inviteVehicleRecordIdList = inviteVehicleRecordMapper.selectList(queryWrapper).stream().filter(e -> Objects.nonNull(e.getVin())).map(InviteVehicleRecordPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(inviteVehicleRecordIdList)){
            return;
        }
        UpdateWrapper<VocInviteVehicleTaskRecordPo> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(VocInviteVehicleTaskRecordPo::getReturnIntentionLevel, returnIntentionLevel)
                .in(VocInviteVehicleTaskRecordPo::getRecordId, inviteVehicleRecordIdList);
        vocInviteVehicleTaskRecordMapper.update(null,updateWrapper);
    }

    /**
     * 分页查询线索
     */
    private IPage queryUpdateReturnIntentionLevelPage(Integer partialLimit, Integer index,List<String> dealerCodeList){
        //分页对象
        IPage page= new Page();
        page.setSize(partialLimit);
        page.setCurrent(index);
        LambdaQueryWrapper<InviteVehicleRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InviteVehicleRecordPO::getIsDeleted, 0);
        queryWrapper.in(InviteVehicleRecordPO::getInviteType, CommonConstants.INVITE_TYPE_I, CommonConstants.INVITE_TYPE_II, CommonConstants.INVITE_TYPE_VI, CommonConstants.INVITE_TYPE_XII);
        queryWrapper.eq(InviteVehicleRecordPO::getOrderStatus, CommonConstants.ORDER_STATUS_II);
        queryWrapper.in(InviteVehicleRecordPO::getDealerCode,dealerCodeList);
        queryWrapper.orderByAsc(InviteVehicleRecordPO::getCreatedAt);
        IPage<InviteVehicleRecordPO> iPage = inviteVehicleRecordMapper.selectPage(page,queryWrapper);
        return iPage;
    }

    /**
     * 查询所有经销商code
     */
    private List<String> queryInviteVehicleRecordDealerCodeList(){
        //分页对象
        LambdaQueryWrapper<InviteVehicleRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(InviteVehicleRecordPO::getDealerCode);
        queryWrapper.eq(InviteVehicleRecordPO::getIsDeleted, 0);
        queryWrapper.in(InviteVehicleRecordPO::getInviteType, CommonConstants.INVITE_TYPE_I, CommonConstants.INVITE_TYPE_II, CommonConstants.INVITE_TYPE_VI, CommonConstants.INVITE_TYPE_XII);
        queryWrapper.eq(InviteVehicleRecordPO::getOrderStatus, CommonConstants.ORDER_STATUS_II);
        queryWrapper.groupBy(InviteVehicleRecordPO::getDealerCode);
        List<String> dealerCodes = inviteVehicleRecordMapper.selectList(queryWrapper).stream().filter(Objects::nonNull).map(InviteVehicleRecordPO::getDealerCode).collect(Collectors.toList());
        return dealerCodes;
    }

    /**
     * cdp返厂意向等级处理失败数据
     */
    private void retryUpdateReturnIntentionLevel(){
        //查询处理失败的车架号
        LambdaQueryWrapper<CdpTagTaskPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CdpTagTaskPo::getSinceType,CommonConstants.SINCE_TYPE_L);
        queryWrapper.le(CdpTagTaskPo::getRetryCount,3);
        queryWrapper.eq(CdpTagTaskPo::getTaskStatus,CommonConstants.CDP_TAG_FAILURE);
        List<CdpTagTaskPo> cdpTagTaskPos= cdpTagTaskMapper.selectList(queryWrapper);
        Set<String> vins = cdpTagTaskPos.stream().filter(e -> !StringUtils.isNullOrEmpty(e.getBizNo())).map(CdpTagTaskPo::getBizNo).collect(Collectors.toSet());
        logger.info("retryUpdateReturnIntentionLevel cdp返厂意向等级处理失败数据:{}",vins);
        List<List<String>> vinLists = Lists.partition(new ArrayList<>(vins), 50);
        vinLists.stream().forEach(v->{
            try {
                //处理cdp数据
                updateReturnIntentionLevel(v,true);
                //更新状态为成功
                cdpTagTaskMapper.updateCdpTagTaskSuccess(v);
            }catch (Exception e){
                logger.error("retryUpdateReturnIntentionLevel 重跑cdp数据失败:{}",e);
                //更新重跑次数和失败原因
                //todo 若 task_status=2:where条件:重试次数retry_count=2 and 重试状态task_status=2 set为-1 次数为3 根据biz_no去处理
                // 若 retry_count>=2 :retry_count++
                String essage = e.getMessage().length() > 200 ? e.getMessage().substring(200) : e.getMessage();
                cdpTagTaskMapper.updateTagTaskErrorByBizNo(v,essage);
            }

        });
    }


    /**
     * 查询线索总条数
     */
    private int queryUpdateReturnIntentionLevelCount(Integer partialLimit, Integer index,List<String> dealerCodeList){
        LambdaQueryWrapper<InviteVehicleRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InviteVehicleRecordPO::getIsDeleted, 0);
        queryWrapper.in(InviteVehicleRecordPO::getInviteType, CommonConstants.INVITE_TYPE_I, CommonConstants.INVITE_TYPE_II, CommonConstants.INVITE_TYPE_VI, CommonConstants.INVITE_TYPE_XII);
        queryWrapper.eq(InviteVehicleRecordPO::getOrderStatus, CommonConstants.ORDER_STATUS_II);
        queryWrapper.in(InviteVehicleRecordPO::getDealerCode,dealerCodeList);
        queryWrapper.orderByAsc(InviteVehicleRecordPO::getCreatedAt);
        int count = inviteVehicleRecordMapper.selectCount(queryWrapper);
        return count;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addWhiteList(String ownerCodes, Integer modType, Integer rosterType) {
        logger.info("addWhiteList,ownerCodes:{},modType:{},rosterType:{}", ownerCodes, modType, rosterType);
        List<String> list = Arrays.asList(ownerCodes.split(","));
        if (CollectionUtils.isEmpty(list)) {
            logger.info("addWhiteList,CollectionUtils.isEmpty(list)");
            return;
        }
        //删除缓存
        deleteRedisDataByKey();
        //修改验证状态
        updateValidationStatus(list);
        //添加白名单
        whitelistQueryService.addWhiteList(ownerCodes, modType, rosterType);
        //删除缓存
        deleteRedisDataByKey();
        logger.info("addWhiteList,end");
    }

    /**
     * 延时双删
     */
    private void deleteRedisDataByKey() {
        try {
            whitelistQueryService.deleteRedisDataByKey("whitelistCache::91111011-0");
        } catch (Exception e) {
            logger.info("deleteRedisDataByKey,Exception:", e);
        }
    }

    /**
     * 白名单修改验证状态
     */
    private void updateValidationStatus(List<String> list) {
        logger.info("updateValidationStatus,list:{}", list);
        //查询白名单线索
        List<InviteVehicleRecordPO> listPo = this.searchClues(list);
        if (CollectionUtils.isEmpty(listPo)) {
            logger.info("updateValidationStatus,CollectionUtils.isEmpty(listPo)");
            return;
        }
        Long id;
        Integer orderStatus;
        Boolean isFlag;
        Date orderFinishDate;
        List<Long> listStaId = new ArrayList<>();//未开始集合
        List<VocInviteVehicleTaskRecordPo> listVerId = new ArrayList<>();//待验证集合
        List<Long> listComId = new ArrayList<>(); //已验证集合
        List<Long> updateBevIdList = new ArrayList<>(); //需更新bev线索集合
        for (InviteVehicleRecordPO po : listPo) {
            logger.info("updateValidationStatus,po:{}", po);
            orderStatus = po.getOrderStatus();
            isFlag = po.getIsFlag();
            id = po.getId();
            orderFinishDate = po.getOrderFinishDate();
            if (CloseStatusEnum.INCOMPLETE.getIntCode().equals(orderStatus)) {//未完成的线索
                listStaId.add(po.getId());
            } else if (CloseStatusEnum.SELF_COMPLETED.getIntCode().equals(orderStatus) ||//自店完成的线索
                    CloseStatusEnum.OTHER_STORE_COMPLETED.getIntCode().equals(orderStatus)) {//他店完成的线索
                if (isFlag != null && isFlag) {//待验证
                    listVerId.add(potting(id, orderFinishDate));
                } else {//已验证
                    listComId.add(po.getId());
                }
            } else if (CloseStatusEnum.OVERDUE_CLOSED.getIntCode().equals(orderStatus)) {//逾期关闭的线索
                listComId.add(po.getId());
            }

            //bev线索更新
            if (Boolean.TRUE.equals(this.checkBevFlag(po))) {
                updateBevIdList.add(po.getId());
            }
        }
        //批量修改
        //未开始
        taskRecordService.updateVerifyStatusById(listStaId, VerifyTypeEnum.NOT_STARTED.getIntCode(), LocalDate.now());
        //待验证
        taskRecordService.updateVerifyStatusAllById(listVerId);
        //已验证
        taskRecordService.updateVerifyStatusById(listComId, VerifyTypeEnum.VERIFIED.getIntCode(), LocalDate.now());
        //更新bev线索
        taskRecordService.updateBevLeadList(updateBevIdList);
        logger.info("updateValidationStatus,end");
    }

    /**
     * 通过vin判断是否bev线索
     */
    private Boolean checkBevFlag(InviteVehicleRecordPO po) {
        if (Objects.isNull(po) || StringUtils.isBlank(po.getVin())) {
            return false;
        }
        String vin = po.getVin();
        String str1 = StrUtil.sub(vin, 5, 7);
        String str2 = StrUtil.sub(vin, 0, 3);
        if (StrUtil.isEmpty(vin) || StrUtil.isEmpty(str1) || StrUtil.isEmpty(str2)) {
            return false;
        }
        if ((Objects.equals(str2, "LPS") && CollUtil.newArrayList("EC", "EG").contains(str1))
                || CollUtil.newArrayList("ED", "EF").contains(str1)) {
            return true;
        } else {
            return false;
        }
    }

    private VocInviteVehicleTaskRecordPo potting(Long id, Date orderFinishDate) {
        VocInviteVehicleTaskRecordPo vocPo = new VocInviteVehicleTaskRecordPo();
        vocPo.setRecordId(id);
        vocPo.setVerifyStatus(VerifyTypeEnum.PENDING_VERIFICATION.getIntCode());
        vocPo.setVerifyTime(getVerifyDate(orderFinishDate));
        return vocPo;
    }

    private LocalDate getVerifyDate(Date date) {
        logger.info("getVerifyDate,date:{}", date);
        LocalDate localDate;
        try {
            localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        } catch (Exception e) {
            logger.info("getVerifyDate,Exception:", e);
            return LocalDate.now();
        }
        return localDate;
    }

    @Override
    public void updateAdviseInDate(List<CdpTagTaskParameterDto> list) {
        List<CdpTagTaskParameterDto> cdpList = list.stream()
                .filter(obj -> obj.getAdviseInDate() != null)
                .collect(Collectors.toList());
        if (cdpList != null & cdpList.size() > 0) {
            inviteVehicleRecordMapper.updateAdviseInDate(list);
        }
    }
    @Override
    public String queryBookingNo(BookingCreateParamsVo vo) {
        return repairCommonClient.queryBookingNo(vo.getOwnerCode(),vo.getVin());
    }
    /**
     * 查询最新的一条邀约线索
     */
    @Override
    public InviteVehicleRecordPO fetchLatestAppointmentLead(List<Integer> typeList, List<Integer> statusList, String vin, String dealerCode) {
        LambdaQueryWrapper<InviteVehicleRecordPO> wrapper = new LambdaQueryWrapper<InviteVehicleRecordPO>()
                .in(InviteVehicleRecordPO::getInviteType, typeList)
                .eq(InviteVehicleRecordPO::getVin, vin)
                .eq(InviteVehicleRecordPO::getDealerCode, dealerCode)
                .eq(InviteVehicleRecordPO::getIsDeleted, 0)
                .orderByDesc(InviteVehicleRecordPO::getId)
                .last("limit 1");
        if (CollectionUtils.isNotEmpty(statusList)) {
            wrapper.in(InviteVehicleRecordPO::getOrderStatus, statusList);
        }
        return inviteVehicleRecordMapper.selectOne(wrapper);
    }

    /**
     * 获取线索记录
     * @param ownerCode 经销商
     * @param vin vin
     * @param inviteType 线索类型
     * @return 返回对象
     */
    @Override
    public InviteVehicleRecordVo queryInviteVehicleByVin(String ownerCode, String vin, Integer inviteType) {
        if (org.apache.commons.lang3.StringUtils.isBlank(vin) || inviteType == null) {
            return null;
        }
        LambdaQueryWrapper<InviteVehicleRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(ownerCode)) {
            queryWrapper.eq(InviteVehicleRecordPO::getDealerCode, ownerCode);
        }
        queryWrapper.eq(InviteVehicleRecordPO::getInviteType, inviteType)
                    .eq(InviteVehicleRecordPO::getVin, vin)
                    .eq(InviteVehicleRecordPO::getOrderStatus,CommonConstants.ORDER_STATUS_II)
                            .orderByDesc(InviteVehicleRecordPO::getId).last("limit 1");
        InviteVehicleRecordPO inviteVehicleRecord = inviteVehicleRecordMapper.selectOne(queryWrapper);
        logger.info("queryInviteVehicleByVinInviteVehicleRecordVoResponse:{}", inviteVehicleRecord);
        InviteVehicleRecordVo inviteVehicleRecordVo = new InviteVehicleRecordVo();
        Optional.ofNullable(inviteVehicleRecord).ifPresent(s -> BeanUtils.copyProperties(s, inviteVehicleRecordVo));
        logger.info("queryInviteVehicleByVinInviteVehicleRecordVoResponse:{}", inviteVehicleRecordVo);
        return inviteVehicleRecordVo;
    }

}
