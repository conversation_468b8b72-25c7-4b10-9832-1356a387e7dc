package com.yonyou.dmscus.customer.service.invitationautocreate;

/**
 * 流失客户任务清洗
 */
public interface InvVehLossCleanTaskService {

    /**
     * 流失任务清洗-未下发的定保任务
     * @return
     */
    void doCleanLossTaskByDis();

    /**
     * 流失任务清洗-未完成的定保线索
     * @return
     */
    void doCleanLossTaskByUnf();

    /**
     * 流失任务清洗-逾期的定保线索
     * @return
     */
    void doCleanLossTaskBySli();

    /**
     * 流失任务新增时,没有生成扩展表
     */
    void addInviteVehicleTaskRecord(String startDate, String endDate);
}
