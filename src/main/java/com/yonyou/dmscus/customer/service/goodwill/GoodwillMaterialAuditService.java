package com.yonyou.dmscus.customer.service.goodwill;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMaterialAuditDTO;


                                                                                                    /**
 * <p>
 * 亲善预申请单亲善信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
public interface GoodwillMaterialAuditService  {
public IPage<GoodwillMaterialAuditDTO>selectPageBysql(Page page,GoodwillMaterialAuditDTO goodwillMaterialAuditDTO);
 public List<GoodwillMaterialAuditDTO>selectListBySql(GoodwillMaterialAuditDTO goodwillMaterialAuditDTO);
 public GoodwillMaterialAuditDTO getById(Long id);
 public int insert(GoodwillMaterialAuditDTO goodwillMaterialAuditDTO);
 public int update(Long id, GoodwillMaterialAuditDTO goodwillMaterialAuditDTO);
  public int deleteById(Long id);
   public int deleteBatchIds(String ids);
   
}
