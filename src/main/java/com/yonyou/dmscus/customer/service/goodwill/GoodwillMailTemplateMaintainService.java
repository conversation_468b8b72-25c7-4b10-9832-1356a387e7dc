package com.yonyou.dmscus.customer.service.goodwill;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMailTemplateMaintainDTO;


  /**
 * <p>
 * 亲善邮件模板维护 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-28
 */
public interface GoodwillMailTemplateMaintainService  {
	public IPage<GoodwillMailTemplateMaintainDTO>selectPageBysql(Page page,GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO);
	public List<GoodwillMailTemplateMaintainDTO>selectListBySql(GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO);
	public GoodwillMailTemplateMaintainDTO getById(Long id);
	public int insert(GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO);
	public int update(Long id, GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO);
	public int deleteById(Long id);
	public int deleteBatchIds(String ids);
	public int updateEmailMaintain(List<GoodwillMailTemplateMaintainDTO> goodwillMailTemplateMaintainDTO);

}
