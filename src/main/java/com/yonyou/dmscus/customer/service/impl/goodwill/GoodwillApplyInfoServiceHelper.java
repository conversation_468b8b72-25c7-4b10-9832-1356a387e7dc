package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;


@EnableAsync
@Component
public class GoodwillApplyInfoServiceHelper {

    @Lazy
    @Autowired
    private GoodwillApplyInfoService goodwillApplyInfoService;

    @Async("asyncThreadPool")
    public void sendEmail(GoodwillApplyInfoPO goodwillApplyInfoPo, int sendToFlag, String auditRole, Integer mailType){
        goodwillApplyInfoService.sendEmailAsync(goodwillApplyInfoPo,sendToFlag,auditRole,mailType);
    }
}
