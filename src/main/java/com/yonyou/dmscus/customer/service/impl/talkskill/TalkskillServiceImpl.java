package com.yonyou.dmscus.customer.service.impl.talkskill;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.talkskill.*;
import com.yonyou.dmscus.customer.dto.OrgInfoDTO;
import com.yonyou.dmscus.customer.dto.OrgSearchParams;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillDTO;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillKeywordDTO;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillKeywordLibDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.*;
import com.yonyou.dmscus.customer.service.CommonService;
import com.yonyou.dmscus.customer.service.talkskill.TalkskillService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
* <p>
* 话术 服务实现类
* </p>
*
* <AUTHOR>
* @since 2020-03-19
*/
@Service
public class TalkskillServiceImpl implements TalkskillService {
    private final Logger logger=LoggerFactory.getLogger(this.getClass());
    @Resource
    TalkskillMapper talkskillMapper;
    @Resource
    TalkskillKeywordMapper talkskillKeywordMapper;
    @Resource
    TalkskillKeywordLibMapper talkskillKeywordLibMapper;
    @Resource
    TalkskillTypeMapper talkskillTypeMapper;
    @Resource
    TalkskillTagMapper talkskillTagMapper;

    @Resource
    CommonService commonService;

    /**
    * 分页查询对应数据
    *
    * @param page 分页对象
    * @param talkskillDTO 查询条件
    * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscloud.finance.entity.dto.TalkskillDTO>
    * <AUTHOR>
    * @since 2018/7/22 0022
    */
    @Override
    public IPage<TalkskillDTO> selectPageBysql(Page page, TalkskillDTO talkskillDTO){
        if(talkskillDTO == null){
            talkskillDTO = new TalkskillDTO();
        }
        TalkskillPO talkskillPO = talkskillDTO.transDtoToPo(TalkskillPO.class);

        List<TalkskillPO> list = talkskillMapper.selectPageBySql(page,talkskillPO);

        if(CommonUtils.isNullOrEmpty(list)){

            page.setRecords(new ArrayList<>());

            return page;
        }else{

            List<TalkskillDTO>result = list.stream().map(m->m.transPoToDto(TalkskillDTO.class)).collect(Collectors.toList());

            page.setRecords(result);

            return page;
        }
    }

    /**
    * 根据查询条件返回结果集
    *
    * @param id 主键ID
    * @return com.yonyou.dmscloud.finance.entity.dto.TalkskillDTO
    * <AUTHOR>
    * @since 2018/7/22 0022
    */
    @Override
    public TalkskillDTO getById(Long id){
        TalkskillPO talkskillPO = talkskillMapper.selectById(id);
        if(talkskillPO!=null){
            return talkskillPO.transPoToDto(TalkskillDTO.class);
        }else{
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
    * 根据DTO 进行数据新增
    *
    * @param talkskillDTO 页面DTO
    * @return int
    * <AUTHOR>
    * @since 2018/7/21 0021
    */
    @Override
    public int insert(TalkskillDTO talkskillDTO){
        //对对象进行赋值操作
        TalkskillPO talkskillPO = talkskillDTO.transDtoToPo(TalkskillPO.class);
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if(loginInfoDto == null){
            throw new DALException("用户未登陆");
        }
        talkskillPO.setOrgId(loginInfoDto.getOrgId());
        /**
         * 执行插入
         */
        talkskillMapper.insert(talkskillPO);
        /**
         * 增加下发操作，给每个经销商增加一条话术,判断厂端还是店端
         */
        if(talkskillPO.getDataType() == CommonConstants.DATASOURCES_VCDC){
            OrgSearchParams orgSearchParams = new OrgSearchParams();
            orgSearchParams.setOrgType(15061003);
            Long id = talkskillPO.getTalkId();
            List<OrgInfoDTO>  list =   commonService.getOrgInfo(orgSearchParams);
            for(OrgInfoDTO item:list){
                talkskillPO.setIsVcdc(0);
                talkskillPO.setDealerCode(item.getOrgCode());
                talkskillPO.setVcdcId(id);
                talkskillPO.setIsDealerMod(0);
                talkskillMapper.insert(talkskillPO);
            }
        }

        /**
         * 处理关键词
         */
        for(String keyword:talkskillDTO.getKeywordList()){
            TalkskillKeywordDTO talkskillKeywordDTO = new TalkskillKeywordDTO();
            TalkskillKeywordPO talkskillKeywordPO = talkskillKeywordDTO.transDtoToPo(TalkskillKeywordPO.class);

            TalkskillKeywordLibDTO talkskillKeywordLibDTO = new TalkskillKeywordLibDTO();
            TalkskillKeywordLibPO talkskillKeywordLibPO = talkskillKeywordLibDTO.transDtoToPo(TalkskillKeywordLibPO.class);

            LambdaQueryWrapper<TalkskillKeywordLibPO> queryWrapper = new QueryWrapper().lambda();
            queryWrapper.eq(TalkskillKeywordLibPO::getKeyword,keyword);
            TalkskillKeywordLibPO obj = talkskillKeywordLibMapper.selectOne(queryWrapper);

            // 插入关系表
            talkskillKeywordPO.setTalkId(talkskillPO.getTalkId());
            talkskillKeywordPO.setKeyword(keyword);
            talkskillKeywordPO.setIsValid(CommonConstants.DICT_VALIDS_VALID);
            talkskillKeywordMapper.insert(talkskillKeywordPO);


            if(obj == null){
                //没有查到，关键词库新增一个
                talkskillKeywordLibPO.setKeyword(keyword);
                talkskillKeywordLibMapper.insert(talkskillKeywordLibPO);
            }
        }


        //返回插入的值
        return 1;
    }

    /**
    * 根据DTO 及ID 进行数据更新
    *
    * @param id 主键ID
    * @param talkskillDTO 页面DTO
    * @return int
    * <AUTHOR>
    * @since 2018/7/21 0021
    */
    @Override
    public int update(Long id, TalkskillDTO talkskillDTO){

        TalkskillPO talkskillPO = talkskillDTO.transDtoToPo(TalkskillPO.class);
        talkskillDTO.transDtoToPo(talkskillPO);
        /**
         * 修改话术,判断厂端还是店端，厂端判断经销商没有修改的同步修改
         */
        if(talkskillPO.getDataType() == CommonConstants.DATASOURCES_VCDC){
            TalkskillPO talkskillPO1 = new TalkskillPO();
            talkskillPO1.setIsDealerMod(0);
            talkskillPO1.setVcdcId(talkskillPO.getTalkId());
            List<TalkskillPO> modList = talkskillMapper.selectListBySql(talkskillPO1);

            for(TalkskillPO item:modList){
                item.setTitle(talkskillPO.getTitle());
                item.setTalkskill(talkskillPO.getTalkskill());
                item.setBeginDate(talkskillPO.getBeginDate());
                item.setEndDate(talkskillPO.getEndDate());
                item.setType(talkskillPO.getType());
                item.setTag1(talkskillPO.getTag1());
                item.setTag2(talkskillPO.getTag2());
                item.setKeyword1(talkskillPO.getKeyword1());
                item.setKeyword2(talkskillPO.getKeyword2());
                item.setKeyword3(talkskillPO.getKeyword3());
                item.setIsCompel(talkskillPO.getIsCompel());
                talkskillMapper.updateById(item);
            }

        }else {
            talkskillPO.setIsDealerMod(1);
        }

        talkskillMapper.updateById(talkskillPO);
        //处理关键词 --删除老的个数
        for(String keyword:talkskillDTO.getOldKeywordList()){
            TalkskillKeywordDTO talkskillKeywordDTO = new TalkskillKeywordDTO();
            TalkskillKeywordPO talkskillKeywordPO = talkskillKeywordDTO.transDtoToPo(TalkskillKeywordPO.class);
            talkskillKeywordPO.setKeyword(keyword);
            talkskillKeywordPO.setIsValid(CommonConstants.DICT_VALIDS_INVALID);
            LambdaQueryWrapper<TalkskillKeywordLibPO> queryWrapper = new QueryWrapper().lambda();
            queryWrapper.eq(TalkskillKeywordLibPO::getKeyword,keyword);
            TalkskillKeywordLibPO obj = talkskillKeywordLibMapper.selectOne(queryWrapper);
            talkskillKeywordPO.setKeyId(obj.getKeyId());
            talkskillKeywordMapper.updateById(talkskillKeywordPO);

        }

        //处理关键词
        for(String keyword:talkskillDTO.getKeywordList()){
            TalkskillKeywordDTO talkskillKeywordDTO = new TalkskillKeywordDTO();
            TalkskillKeywordPO talkskillKeywordPO = talkskillKeywordDTO.transDtoToPo(TalkskillKeywordPO.class);

            TalkskillKeywordLibDTO talkskillKeywordLibDTO = new TalkskillKeywordLibDTO();
            TalkskillKeywordLibPO talkskillKeywordLibPO = talkskillKeywordLibDTO.transDtoToPo(TalkskillKeywordLibPO.class);

            LambdaQueryWrapper<TalkskillKeywordLibPO> queryWrapper = new QueryWrapper().lambda();
            queryWrapper.eq(TalkskillKeywordLibPO::getKeyword,keyword);
            TalkskillKeywordLibPO obj = talkskillKeywordLibMapper.selectOne(queryWrapper);

            // 插入关系表
            talkskillKeywordPO.setTalkId(talkskillPO.getTalkId());
            talkskillKeywordPO.setKeyword(keyword);
            talkskillKeywordPO.setIsValid(CommonConstants.DICT_VALIDS_VALID);
            talkskillKeywordMapper.insert(talkskillKeywordPO);
            if(obj == null){
                //没有查到，关键词库新增一个
                talkskillKeywordLibPO.setKeyword(keyword);
                talkskillKeywordLibMapper.insert(talkskillKeywordLibPO);
            }
        }

        return 1;
    }
    /**
    * 启用禁用serivice
    */
    @Override
    public int updateSwitch(TalkskillDTO talkskillDTO) {
        /**
         * 判断是否厂端
         */
        if(talkskillDTO.getDataType() == CommonConstants.DATASOURCES_VCDC){
            TalkskillPO talkskillPO = talkskillDTO.transDtoToPo(TalkskillPO.class);
            /**
             * 暂时注释
             */
            // talkskillPO.setIsDealerMod(0);
            talkskillPO.setVcdcId(talkskillPO.getTalkId());
            List<TalkskillPO> modList = talkskillMapper.selectListBySql(talkskillPO);

            for(TalkskillPO item:modList){
                item.setIsEnable(talkskillPO.getIsEnable());
                talkskillMapper.updateById(item);
            }
        }

        return talkskillMapper.updataByIdEnable(talkskillDTO);
    }


    @Override
    public List<TalkskillDTO> queryTalkskill(String dealerCode, String type, String name) {
        List<TalkskillPO> list = talkskillMapper.queryTalkskill(dealerCode, type, name);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(TalkskillDTO.class)).collect(Collectors.toList());
        }
    }

    @Cacheable( value = "queryTalkskill", key = "#dealerCode+'-'+#keyword" )
    @Override
    public List<TalkskillDTO> queryTalkskill(String dealerCode,String keyword) {
        List<TalkskillPO> list = talkskillMapper.queryTalkskill1(dealerCode,keyword);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(TalkskillDTO.class)).collect(Collectors.toList());
        }
    }

    public List<TalkskillDTO> getQwimTalkskills(String dealerCode, List<String> tags) {
        logger.info("getQwimTalkskill start... dealerCode:{},tags:{}", dealerCode, tags);
        Objects.requireNonNull(dealerCode, "dealerCode isnull");
        if (CollectionUtils.isEmpty(tags)) {
            throw new ServiceBizException("getQwimTalkskill tag isnull");
        }

        List<TalkskillDTO> talkskills = new ArrayList<>();

        LambdaQueryWrapper<TalkskillTypePO> talkskillType = new LambdaQueryWrapper();
        talkskillType.eq(TalkskillTypePO::getTypeCode, CommonConstants.TALKSKILL_TYPE_CODE_QWIM);
        talkskillType.eq(TalkskillTypePO::getIsValid, CommonConstants.DICT_VALIDS_VALID);
        talkskillType.eq(TalkskillTypePO::getIsDeleted, 0);
        talkskillType.last(" limit 1");
        TalkskillTypePO talkskillTypePO = talkskillTypeMapper.selectOne(talkskillType);
        if (null == talkskillTypePO) {
            logger.info("getQwimTalkskill null == talkskillTypePO");
            return talkskills;
        }
        Long typeId = talkskillTypePO.getTypeId();
        logger.info("getQwimTalkskill typeId:{}", typeId);

        LambdaQueryWrapper<TalkskillTagPO> talkskillTag = new LambdaQueryWrapper();
        talkskillTag.in(TalkskillTagPO::getTagCode, tags);
        talkskillTag.eq(TalkskillTagPO::getIsValid, CommonConstants.DICT_VALIDS_VALID);
        talkskillTag.eq(TalkskillTagPO::getIsDeleted, 0);
        List<TalkskillTagPO> talkskillTags = talkskillTagMapper.selectList(talkskillTag);
        if (CollectionUtils.isEmpty(talkskillTags)) {
            logger.info("getQwimTalkskill talkskillTags isEmpty");
            return talkskills;
        }
        Map<String, String> tagMap = talkskillTags.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(obj -> obj.getTagId() + "",
                        TalkskillTagPO::getTagCode, (key1, key2) -> key1));
        Objects.requireNonNull("getQwimTalkskill tagMap isnull");

        LambdaQueryWrapper<TalkskillPO> talkskill = new LambdaQueryWrapper();
        talkskill.eq(TalkskillPO::getDealerCode, dealerCode);
        talkskill.eq(TalkskillPO::getType, typeId);
        talkskill.in(TalkskillPO::getTag1, tagMap.keySet());
        talkskill.eq(TalkskillPO::getIsEnable, CommonConstants.TALKSKILL_ENABLE_ENABLE);
        talkskill.eq(TalkskillPO::getIsDeleted, 0);
        talkskill.orderByDesc(TalkskillPO::getCreatedAt);
        List<TalkskillPO> pos = talkskillMapper.selectList(talkskill);
        if (CollectionUtils.isEmpty(pos)) {
            logger.info("getQwimTalkskill null == po");
            return talkskills;
        }

        TalkskillDTO talkskillDTO = null;
        for (TalkskillPO po : pos) {
            talkskillDTO = new TalkskillDTO();
            BeanUtils.copyProperties(po, talkskillDTO);
            talkskillDTO.setTypeCode(talkskillTypePO.getTypeCode());
            talkskillDTO.setTag1Code(tagMap.get(po.getTag1() + ""));
            talkskills.add(talkskillDTO);
        }

        return talkskills;
    }
}
