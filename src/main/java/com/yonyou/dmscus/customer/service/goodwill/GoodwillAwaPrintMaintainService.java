package com.yonyou.dmscus.customer.service.goodwill;

 
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditProcessDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAwaPrintMaintainDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAwaPrintMaintainPO;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


                                                                                                    /**
 * <p>
 * 亲善AWA打印模板维护 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-23
 */
public interface GoodwillAwaPrintMaintainService  {
	public IPage<GoodwillAwaPrintMaintainDTO>selectPageBysql(Page page,GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO);
	public List<GoodwillAwaPrintMaintainDTO>selectListBySql(GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO);
	public GoodwillAwaPrintMaintainDTO getById(Long id);
	public int insert(GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO);
	public int update(Long id, GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO);
	public int deleteById(Long id);
	public int deleteBatchIds(String ids);
	public List<Map>selectAwaPrintMaintainInfo(GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO);
	public int addOrEditAwaPrint(GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO);
   
}
