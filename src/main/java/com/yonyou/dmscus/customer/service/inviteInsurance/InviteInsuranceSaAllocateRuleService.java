package com.yonyou.dmscus.customer.service.inviteInsurance;


import com.yonyou.dmscus.customer.dto.InvitationRuleVcdcParamsVo;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.UserInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceSaAllocateRuleDTO;

import java.util.List;

/**
 * <p>
 * 续保SA分配规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
public interface InviteInsuranceSaAllocateRuleService {

    int saveInviteInsuranceSaAllocateRule(InviteInsuranceSaAllocateRuleDTO inviteInsuranceSaAllocateRuleDTO);

    InviteInsuranceSaAllocateRuleDTO getInviteInsuranceSaAllocateRuleDlr();

    List<InviteInsuranceSaAllocateRuleDTO> getInviteInsuranceSaAllocateRule(InvitationRuleVcdcParamsVo vo);

}
