package com.yonyou.dmscus.customer.service.complaint.impl.complaint;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintCcmClassificationMapper;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCcmClassificationPO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintCcmClassificationService;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCcmClassificationDTO;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 客诉CCM分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
@Service
public class ComplaintCcmClassificationServiceImpl implements ComplaintCcmClassificationService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    ComplaintCcmClassificationMapper complaintCcmClassificationMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                          分页对象
     * @param complaintCcmClassificationDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCcmClassificationDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<ComplaintCcmClassificationDTO> selectPageBysql(Page page, ComplaintCcmClassificationDTO complaintCcmClassificationDTO) {
        if (complaintCcmClassificationDTO == null) {
            complaintCcmClassificationDTO = new ComplaintCcmClassificationDTO();
        }
        ComplaintCcmClassificationPO complaintCcmClassificationPO = complaintCcmClassificationDTO.transDtoToPo(ComplaintCcmClassificationPO.class);

        List<ComplaintCcmClassificationPO> list = complaintCcmClassificationMapper.selectPageBySql(page, complaintCcmClassificationPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<ComplaintCcmClassificationDTO> result = list.stream().map(m -> m.transPoToDto(ComplaintCcmClassificationDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param complaintCcmClassificationDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCcmClassificationDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<ComplaintCcmClassificationDTO> selectListBySql(ComplaintCcmClassificationDTO complaintCcmClassificationDTO) {
        if (complaintCcmClassificationDTO == null) {
            complaintCcmClassificationDTO = new ComplaintCcmClassificationDTO();
        }
        ComplaintCcmClassificationPO complaintCcmClassificationPO = complaintCcmClassificationDTO.transDtoToPo(ComplaintCcmClassificationPO.class);
        List<ComplaintCcmClassificationPO> list = complaintCcmClassificationMapper.selectListBySql(complaintCcmClassificationPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(ComplaintCcmClassificationDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCcmClassificationDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public ComplaintCcmClassificationDTO getById(Long id) {
        ComplaintCcmClassificationPO complaintCcmClassificationPO = complaintCcmClassificationMapper.selectById(id);
        if (complaintCcmClassificationPO != null) {
            return complaintCcmClassificationPO.transPoToDto(ComplaintCcmClassificationDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param complaintCcmClassificationDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(ComplaintCcmClassificationDTO complaintCcmClassificationDTO) {
        //对对象进行赋值操作
        ComplaintCcmClassificationPO complaintCcmClassificationPO = complaintCcmClassificationDTO.transDtoToPo(ComplaintCcmClassificationPO.class);
        //执行插入
        int row = complaintCcmClassificationMapper.insert(complaintCcmClassificationPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                            主键ID
     * @param complaintCcmClassificationDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, ComplaintCcmClassificationDTO complaintCcmClassificationDTO) {
        ComplaintCcmClassificationPO complaintCcmClassificationPO = complaintCcmClassificationMapper.selectById(id);
        //对对象进行赋值操作
        complaintCcmClassificationDTO.transDtoToPo(complaintCcmClassificationPO);
        //执行更新
        int row = complaintCcmClassificationMapper.updateById(complaintCcmClassificationPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = complaintCcmClassificationMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

}
