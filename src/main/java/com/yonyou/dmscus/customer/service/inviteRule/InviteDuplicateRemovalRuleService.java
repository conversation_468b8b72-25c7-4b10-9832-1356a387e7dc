package com.yonyou.dmscus.customer.service.inviteRule;

import com.yonyou.dmscus.customer.entity.dto.inviteRule.InviteDuplicateRemovalRuleDTO;
import com.yonyou.dmscus.customer.service.common.IBaseService;

import java.util.List;


/**
 * <p>
 * 邀约去重规则表，本章表所存储的数据，应该都是一个默认值。 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-17
 */
public interface InviteDuplicateRemovalRuleService extends IBaseService<InviteDuplicateRemovalRuleDTO> {

    List<InviteDuplicateRemovalRuleDTO> selectListBySqlNew(InviteDuplicateRemovalRuleDTO t);

}
