package com.yonyou.dmscus.customer.service.vocAccidentInvitation;

import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.vocAccidentInvitation.InviteVehicleVocTaskDTO;
import com.yonyou.dmscus.customer.entity.dto.vocAccidentInvitation.TempInviteVehicleVocTaskDTO;
import com.yonyou.dmscus.customer.service.common.IBaseService;
import org.springframework.web.multipart.MultipartFile;


/**
 * <p>
 * 车辆特约店VOC事故邀约任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
public interface InviteVehicleVocTaskService extends IBaseService<InviteVehicleVocTaskDTO> {

    int saveVocInvitation(InviteVehicleVocTaskDTO inviteVehicleVocTaskDTO);

    /**
     * voc事故导入临时表
     * @param importFile
     * @return
     * @throws Exception
     */
    ImportTempResult<TempInviteVehicleVocTaskDTO> importTempVocInvitation(MultipartFile importFile) throws Exception;

    /**
     * 临时表数据保存到正式表
     * @return
     */
    int importVocInvitation();
}
