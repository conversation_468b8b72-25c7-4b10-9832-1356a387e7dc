package com.yonyou.dmscus.customer.service.goodwillTask;

public interface GoodwillTaskService {
	public int goodwillToRefuse();

	public int goodwillApplyTimeOutTask();

	public int goodwillMatrialAuditOneTask();

	public int goodwillMatrialAuditTwoTask();

	public int goodwillMatrialCommitOneTask();

	public int goodwillMatrialCommitTwoTask();

	public int goodwillSupplyMatrialCommitOneTask();

	public int goodwillSupplyMatrialCommitTwoTask();

	public int goodwillMatrialCommitTimeOutTask();

	public int goodwillInvoiceTimeOutTask();
}
