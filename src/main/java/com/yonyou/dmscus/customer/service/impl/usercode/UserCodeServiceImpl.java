package com.yonyou.dmscus.customer.service.impl.usercode;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.service.excel.ExcelRead;
import com.yonyou.dmscloud.framework.service.excel.ExcelReadCallBack;
import com.yonyou.dmscloud.framework.service.excel.impl.AbstractExcelReadCallBack;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.userCode.UserCodeInfoImportMapper;
import com.yonyou.dmscus.customer.dao.userCode.UserCodeInfoMapper;
import com.yonyou.dmscus.customer.dto.UserCodeVo;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.userCode.UserCodeInfoDto;
import com.yonyou.dmscus.customer.entity.dto.userCode.UserCodeInfoImportDto;
import com.yonyou.dmscus.customer.entity.dto.userCode.UserCodeInfoImportExcelDto;
import com.yonyou.dmscus.customer.entity.po.userCode.UserCodeInfoImportPo;
import com.yonyou.dmscus.customer.entity.po.userCode.UserCodeInfoPo;
import com.yonyou.dmscus.customer.service.usercode.UserCodeService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: UserCodeServiceImpl
 * @projectName server2
 * @description: TODO
 * @date 2022/7/1914:43
 */
@Service
public class UserCodeServiceImpl implements UserCodeService {
    private static final Logger logger = LoggerFactory.getLogger(UserCodeServiceImpl.class);

    @Autowired
    UserCodeInfoMapper userCodeInfoMapper ;
    @Autowired
    UserCodeInfoImportMapper userCodeInfoImportMapper ;


    @Resource
    ExcelRead<UserCodeInfoImportExcelDto> excelReadServiceIs;
    @Override
    public int getOneByUerCode(UserCodeVo vo) {
       int count=  userCodeInfoMapper.getCountByUserCode(vo.getUserCode());
        return count>0?1:0;
    }

    @Override
    public IPage<UserCodeInfoDto> selectPageBysql(Page page, UserCodeVo dto) {
        List<UserCodeInfoDto> list=userCodeInfoMapper.selectPageBySql(page,dto);
        page.setRecords(list);
        return page;
    }

    @Override
    public IPage<UserCodeInfoImportDto> selectErrorPage(Page<UserCodeInfoImportDto> page) {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if(StringUtils.isNullOrEmpty(loginInfoDto.getUserId())){
            throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
        }
        List<UserCodeInfoImportPo>  list=userCodeInfoImportMapper.selectErrorPage(page,loginInfoDto.getUserId());
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<UserCodeInfoImportDto> result = list.stream().map(m -> m.transPoToDto
                    (UserCodeInfoImportDto.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    @Override
    public ImportTempResult<UserCodeInfoImportDto> importUserCodeTemp(MultipartFile importFile) throws IOException {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if(com.yonyou.dmscloud.function.utils.common.StringUtils.isNullOrEmpty(loginInfoDto.getUserId())){
            throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
        }
        List<UserCodeInfoImportPo> addList=new ArrayList<UserCodeInfoImportPo>();

        //删除历史数据
        userCodeInfoImportMapper.deleteByCreatedBy(""+loginInfoDto.getUserId());

        excelReadServiceIs.analyzeExcelFirstSheet(importFile, new AbstractExcelReadCallBack<UserCodeInfoImportExcelDto>(
                UserCodeInfoImportExcelDto.class, new ExcelReadCallBack<UserCodeInfoImportExcelDto>() {

            private Integer seq = 1;
            @Override
            public void readRowCallBack(UserCodeInfoImportExcelDto dto, boolean b) {
                UserCodeInfoImportPo po=new UserCodeInfoImportPo();
                //校验excel数据
                if(validationData(dto,po)) {
                    po.setBelonging(dto.getBelonging());
                    po.setBelongingName(dto.getBelongingName());
                    po.setEmail(dto.getEmail());
                    po.setEmployeeName(dto.getEmployeeName());
                    po.setEmployeeNo(dto.getEmployeeNo());
                    po.setMobile(dto.getMobile());
                    po.setPosition(dto.getPosition());
                    po.setUserCode(dto.getUserCode());
                    po.setIsValid(getInt(dto.getIsValid()));
                    po.setIsError(0);
                }
                po.setLineNumber(++seq);
                addList.add(po);

            }
        }));
        if (!CommonUtils.isNullOrEmpty(addList)) {
            int listSize = addList.size();
            int toIndex = 100;
            for (int i = 0; i < addList.size(); i += toIndex) {
                if (i + toIndex > listSize) {        //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
                    toIndex = listSize - i;
                }
                List<UserCodeInfoImportPo> insertList = addList.subList(i, i + toIndex);
                userCodeInfoImportMapper.bulkInsert(insertList, loginInfoDto.getUserId());
            }
        }
        return this.checkTmpData();

    }

    private Integer getInt(String isValid) {
        if("是".equals(isValid)){
            return 10041001;
        }else {
            return  10041002;
        }
    }

    @Override
    public void batchInsert() {

        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //临时表导入正式表
        userCodeInfoMapper.batchInsert(loginInfoDto.getUserId());
        //删除临时表历史数据
        userCodeInfoImportMapper.deleteByCreatedBy(""+loginInfoDto.getUserId());

    }



    /**
     * 校验导入excel数据
     * @param dto
     * @param po
     * @return
     */
    private boolean validationData(UserCodeInfoImportExcelDto dto,UserCodeInfoImportPo po) {
        boolean isOk=true;
        if(!StringUtils.isNullOrEmpty(dto.getBelonging())){
            if(dto.getBelonging().length()>10){
                po.setErrorMsg("账号开通人员归属长度不能超过10");
                po.setIsError(1);
                isOk=false;
            }
        }
        if(!StringUtils.isNullOrEmpty(dto.getBelongingName())){
            if(dto.getBelongingName().length()>10){
                po.setErrorMsg("经销商代码/所属集团长度不能超过10");
                po.setIsError(1);
                isOk=false;
            }
        }
        if(!StringUtils.isNullOrEmpty(dto.getEmployeeName())){
            if(dto.getEmployeeName().length()>10){
                po.setErrorMsg("姓名长度不能超过10");
                po.setIsError(1);
                isOk=false;
            }
        }
        if(!StringUtils.isNullOrEmpty(dto.getEmployeeNo())){
            if(dto.getEmployeeNo().length()>10){
                po.setErrorMsg("员工编号长度不能超过10");
                po.setIsError(1);
                isOk=false;
            }
        }
        if(!StringUtils.isNullOrEmpty(dto.getPosition())){
            if(dto.getPosition().length()>10){
                po.setErrorMsg("岗位长度不能超过10");
                po.setIsError(1);
                isOk=false;
            }
        }

        if(!StringUtils.isNullOrEmpty(dto.getMobile())){
            if(dto.getMobile().length()>11){
                po.setErrorMsg("手机号码长度不能超过11");
                po.setIsError(1);
                isOk=false;
            }
        }
        if(!StringUtils.isNullOrEmpty(dto.getEmail())){
            if(dto.getEmail().length()>30){
                po.setErrorMsg("邮箱长度不能超过30");
                po.setIsError(1);
                isOk=false;
            }
        }

        if(StringUtils.isNullOrEmpty(dto.getUserCode()) || dto.getUserCode().length()>10) {
            po.setErrorMsg("newbie登录账号不能为空并且长度不能超过10");
            po.setIsError(1);
            isOk=false;
        }
        if(StringUtils.isNullOrEmpty(dto.getIsValid()) || !("是".equals(dto.getIsValid())||"否".equals(dto.getIsValid()))) {
            po.setErrorMsg("是否有BI报表权限不能为空并且必须为是或者否");
            po.setIsError(1);
            isOk=false;
        }
       int  count =  userCodeInfoMapper.selectCountByUserCode(dto.getUserCode());
       if(count>0){
           po.setErrorMsg("登录账号重复");
           po.setIsError(1);
           isOk=false;
       }

        return isOk;
    }

    /**
     * 校验更新临时表，返回校验结果对象
     *
     * @return
     * <AUTHOR>
     * @since 2020/03/19
     */
    private ImportTempResult<UserCodeInfoImportDto> checkTmpData() {
        ImportTempResult<UserCodeInfoImportDto> importResult = new ImportTempResult<UserCodeInfoImportDto>();
        // 获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //查询错误项
        List<UserCodeInfoImportDto> listError = userCodeInfoImportMapper.queryError(loginInfoDto.getUserId());
        if (!CommonUtils.isNullOrEmpty(listError)) {
            importResult.setErrorList(listError);
        } else {
            logger.info("未查到错误数据！");
        }
        // 查询成功项
        List<UserCodeInfoImportDto> listSuccess = userCodeInfoImportMapper.querySuccess(loginInfoDto.getUserId());
        if (!CommonUtils.isNullOrEmpty(listSuccess)) {
            importResult.setSuccessList(listSuccess);
        } else {
            //inviteVehicleDealerTaskImportMapper.deleteByCreatedBy(loginInfoDto.getUserId()+"");
            logger.info("未查到正确数据！");
        }
        // 查询正确数据数
        importResult.setSuccessCount(userCodeInfoImportMapper.querySucessCount(loginInfoDto.getUserId()));
        return importResult;
    }
}
