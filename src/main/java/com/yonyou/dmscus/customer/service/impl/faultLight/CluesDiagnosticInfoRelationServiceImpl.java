package com.yonyou.dmscus.customer.service.impl.faultLight;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.faultLight.CluesDiagnosticInfoRelationMapper;
import com.yonyou.dmscus.customer.dao.faultLight.TtFaultLightClueMapper;
import com.yonyou.dmscus.customer.entity.dto.faultLight.HandleDimCluesDto;
import com.yonyou.dmscus.customer.entity.dto.faultLight.SourceClueIdRelationDto;
import com.yonyou.dmscus.customer.entity.po.faultLight.CluesDiagnosticInfoRelationPO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightCluePO;
import com.yonyou.dmscus.customer.service.faultLight.CluesDiagnosticInfoRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CluesDiagnosticInfoRelationServiceImpl implements CluesDiagnosticInfoRelationService {
    @Resource
    private TtFaultLightClueMapper ttFaultLightClueMapper;
    @Resource
    private CluesDiagnosticInfoRelationMapper cluesDiagnosticInfoRelationMapper;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markIsDisplayDiagnosticInfo(HandleDimCluesDto handleDimCluesDto) {
        List<Long> relationPOList = handleDimCluesDto.getLeadsPreprocessingIds();
        Integer updateStatusResult = batchUpdateProcessingStatusByIds(relationPOList, CommonConstants.NUM_1);
        if (updateStatusResult > 0) {
            List<TtFaultLightCluePO> faultLightCluePOS = handleDimCluesDto.getCluePrimaryIdList().stream().map(id -> TtFaultLightCluePO.builder()
                    .id(id)
                    .troubleCode(String.valueOf(CommonConstants.NUM_1))
                    .build()).collect(Collectors.toList());
            log.info("start updating clue table data");
            Integer updateTroubleResult = ttFaultLightClueMapper.batchUpdateTroubleCodeBySourceClueId(faultLightCluePOS);
            if (updateTroubleResult > 0) {
                log.info("batch update trouble code success: {}", updateTroubleResult);
                batchUpdateProcessingStatusByIds(relationPOList, CommonConstants.NUM_2);
            } else {
                batchUpdateProcessingStatusByIds(relationPOList, CommonConstants.NUM_0);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HandleDimCluesDto recordNumberOfExecutions(List<CluesDiagnosticInfoRelationPO> relationPOList) {
        // 重试次数达到最大值
        List<Long> moreThanMaxRetryCountList = relationPOList.stream()
                .filter(v -> v.getNumberOfExecutions() > CommonConstants.NUM_4).map(CluesDiagnosticInfoRelationPO::getLeadsPreprocessingId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(moreThanMaxRetryCountList)) {
            batchUpdateProcessingStatusByIds(moreThanMaxRetryCountList, CommonConstants.NUM_3);
        }

        // 未处理的数据
        List<CluesDiagnosticInfoRelationPO> resultRelationPOList = relationPOList.stream().filter(v -> v.getNumberOfExecutions() <= CommonConstants.NUM_4).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resultRelationPOList)) {
            return null;
        }
        Integer result = batchUpdateNumberOfExecutionsByIds(resultRelationPOList);
        List<Long> sourceClueIds = resultRelationPOList.stream().map(CluesDiagnosticInfoRelationPO::getLeadsPreprocessingId).collect(Collectors.toList());
        List<SourceClueIdRelationDto> relationList = ttFaultLightClueMapper.queryRecordCountByIds(sourceClueIds);
        if (result > 0 && CollectionUtils.isNotEmpty(relationList)) {
            return assembleHandleDimCluesDto(relationList);
        }
        log.info("there is no data that needs to be updated");
        return null;
    }

    private HandleDimCluesDto assembleHandleDimCluesDto(List<SourceClueIdRelationDto> relationList) {
        List<Long> cluePrimaryIds = relationList.stream().map(SourceClueIdRelationDto::getCluePrimaryId).collect(Collectors.toList());
        List<Long> leadsPreprocessingIds = relationList.stream().map(SourceClueIdRelationDto::getSourceClueId).collect(Collectors.toList());
        HandleDimCluesDto handleDimCluesDto = new HandleDimCluesDto();
        handleDimCluesDto.setCluePrimaryIdList(cluePrimaryIds);
        handleDimCluesDto.setLeadsPreprocessingIds(leadsPreprocessingIds);
        return handleDimCluesDto;
    }

    @Override
    public List<CluesDiagnosticInfoRelationPO> queryDiagnosticInfoRelationList(int offset, int limit, String createdAt, Integer processingStatus) {
        return cluesDiagnosticInfoRelationMapper.queryDiagnosticInfoRelationList(offset, limit, processingStatus, createdAt);
    }

    @Override
    public Integer queryDiagnosticInfoRelationCount(String createdAt, Integer processingStatus) {
        return cluesDiagnosticInfoRelationMapper.queryDiagnosticInfoRelationCount(createdAt, processingStatus);
    }

    public Integer batchUpdateNumberOfExecutionsByIds(List<CluesDiagnosticInfoRelationPO> relationPOList) {
        relationPOList.forEach(v -> v.setNumberOfExecutions(v.getNumberOfExecutions() + 1));
        return cluesDiagnosticInfoRelationMapper.batchUpdateNumberOfExecutionsByIds(relationPOList);
    }

    public Integer batchUpdateProcessingStatusByIds(List<Long> relationPOList, Integer processingStatus) {
        List<CluesDiagnosticInfoRelationPO> relationPOS = relationPOList.stream().map(v -> {
            CluesDiagnosticInfoRelationPO relation = new CluesDiagnosticInfoRelationPO();
            relation.setLeadsPreprocessingId(v);
            relation.setProcessingStatus(processingStatus);
            return relation;
        }).collect(Collectors.toList());
        return cluesDiagnosticInfoRelationMapper.batchUpdateProcessingStatusByIds(relationPOS);
    }
}
