package com.yonyou.dmscus.customer.service.inviteRule;

import com.yonyou.dmscus.customer.entity.dto.inviteRule.InvitePartItemRuleDTO;
import com.yonyou.dmscus.customer.service.common.IBaseService;


/**
 * <p>
 * 邀约易损件和项目规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
public interface InvitePartItemRuleService extends IBaseService<InvitePartItemRuleDTO> {

    int savePartItemRule(InvitePartItemRuleDTO dto);
}
