/*
 * Copyright (C), 2020-2020, 上海用友汽车有限公司
 * FileName: MiddleGroundVehicleServiceImpl.java
 * Author:   caizhongming
 * Date:     2020年5月28日 上午11:55:57
 * Description: //模块目的、功能描述      
 * History: //修改记录
 * <author>      <time>      <version>    <desc>
 * 修改人姓名             修改时间            版本号                  描述
 */
package com.yonyou.dmscus.customer.service.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.middleInterface.OwnerVehicleVO;
import com.yonyou.dmscus.customer.middleInterface.ResponseDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 〈一句话功能简述〉<br> 
 * 〈功能详细描述〉
 * 中台车辆信息操作
 * <AUTHOR>
 * @see [相关类/方法]（可选）
 * @since [产品/模块版本] （可选）
 */
@Service
public class MiddleGroundVehicleServiceImpl implements IMiddleGroundVehicleService{
	
	/**日志对象*/
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

	/**接口调用成功的返回状态*/
	public static final String SUCCESS_CODE = "0";
	
	@Resource
	private RestTemplate directRestTemplate;

	@Autowired
	private MidUrlProperties midUrlProperties;
    
    /**
     * 功能描述: <br>
     * 〈功能详细描述〉
     * JSON数据格式请求中台接口
     * @param url 请求接口的地址
     * @param method 请求方法类型
     * @param reqParams 请求参数MAP
     * @return 执行后的结果
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public ResponseEntity<ResponseDTO> execRequest(String url, HttpMethod method, Map<String,Object> reqParams){
    	HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Map<String,Object>> httpEntity = new HttpEntity<Map<String,Object>>(reqParams, httpHeaders);
		ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(url, method, httpEntity,
				ResponseDTO.class);
		return responseEntity;
    }

	/**
	 * 中台新增车辆信息返回的结果
	 */
	@Override
	public ResponseDTO addMidVehicle(Map<String, Object> paramMap) {
		ResponseDTO response = new ResponseDTO();
		String url = null;
		Map<String,Object>  map = new HashMap<String,Object>();
		try {
			map.put("data", paramMap);
			logger.info("新增map={}",JSON.toJSONString(map));
			url = midUrlProperties.getMidEndVehicleCenter()+ midUrlProperties.getVehicle();
			ResponseEntity<ResponseDTO> responseEntity = this.execRequest(url, HttpMethod.POST, map);
			if(responseEntity.getBody()!=null) {
				ObjectMapper objectMapper = new ObjectMapper();
				//转换返回对象\\
				response = objectMapper.convertValue(responseEntity.getBody(), ResponseDTO.class);
			}else {
				throw new Exception("请求接口返回对象responseEntity为空");
			}
		} catch (Exception e) {
			logger.error("call middle 中台新增车辆接口请求地址:{},请求参数:{}，返回失败:{}",url,JSON.toJSONString(map),e);
			if (e != null) {
				response.setReturnCode("error");
				response.setReturnMessage(e.getMessage());
			}
		}
		return response;
	}

	/**
	 * 中台更新车辆信息返回的结果
	 */
	@Override
	public ResponseDTO updateMidVehicle(Map<String, Object> paramMap) {

		ResponseDTO response = new ResponseDTO();
		String url = null;
		Map<String,Object>  map = new HashMap<String,Object>();
		try {
			map.put("data", paramMap);
			logger.info("更新map={}",JSON.toJSONString(map));
			url = midUrlProperties.getMidEndVehicleCenter()+ midUrlProperties.getVehicle();
			ResponseEntity<ResponseDTO> responseEntity = this.execRequest(url, HttpMethod.PUT, map);
			if(responseEntity.getBody()!=null) {
				ObjectMapper objectMapper = new ObjectMapper();
				//转换返回对象
				response = objectMapper.convertValue(responseEntity.getBody(), ResponseDTO.class);
			}else {
				throw new Exception("请求接口返回对象responseEntity为空");
			}
		} catch (Exception e) {
			logger.error("call middle 中台更新车辆接口请求地址:{},请求参数:{}，返回失败:{}",url,JSON.toJSONString(map),e);
			if (e != null) {
				response.setReturnCode("error");
				response.setReturnMessage(e.getMessage());
			}
		}
		return response;
	}

	/**
	 * 校验传入的VIN那些VIN是不存在的。
	 */
	@Override
	public ResponseDTO<Map<String,Object>> getVinListCheckInfo(Map<String, Object> paramMap) {
		ResponseDTO<Map<String,Object>> response = new ResponseDTO<Map<String,Object>>();
		String url = null;
		Map<String,Object>  map = new HashMap<String,Object>();
		//定义返回的不存在的VIN码映射
		Map<String,Object> returnMap = new HashMap<String,Object>();
		try {
			map.put("data", paramMap);
			logger.info("验证VIN列表的map={}",JSON.toJSONString(map));
			url = midUrlProperties.getMidEndVehicleCenter()+midUrlProperties.getVinList();
			ResponseEntity<ResponseDTO> responseEntity = this.execRequest(url, HttpMethod.POST, map);
			if(responseEntity.getBody()!=null) {
				ObjectMapper objectMapper = new ObjectMapper();
				//忽略其中不存在的字段
				objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
				//转换返回对象
				ResponseDTO convertValue  = objectMapper.convertValue(responseEntity.getBody(), ResponseDTO.class);
				
				response.setReturnCode(convertValue.getReturnCode());
				response.setReturnMessage(convertValue.getReturnMessage());
				response.setCause(convertValue.getCause());
				
				JSONObject data = (JSONObject) JSONObject.toJSON(convertValue.getData());
				//判断所有vin是否正确
				boolean isAllExists = (boolean) data.get("isAllExists");
				returnMap.put("isAllExists", isAllExists);
				if (!isAllExists) {//不是所有VIN都是正确的
					JSONArray array = (JSONArray) data.get("notExistVinList");
					for (int i = 0; i < array.size(); i++) {
						String Vin = (String) array.get(i);
						returnMap.put(Vin, "no");
					}
				}
				//设置不存在的VIN
				response.setData(returnMap);
			}else {
				throw new Exception("请求接口返回对象responseEntity为空");
			}
		} catch (Exception e) {
			logger.error("call middle 中台验证VIN接口请求地址:{},请求参数:{}，返回失败:{}",url,JSON.toJSONString(map),e);
			if (e != null) {
				response.setReturnCode("error");
				response.setReturnMessage(e.getMessage());
			}
		}
		return response;
	}

	/**
	 * <AUTHOR>
	 * @date 2020年7月23日
	 * @param page
	 * @return
	 * (non-Javadoc)
	 */

	@Override
	public IPage<OwnerVehicleVO> queryVehicleInfo(Page page, Map map1) {

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String requestUrl = midUrlProperties.getMidEndVehicleCenter() + midUrlProperties.getListOwnerVehiclePage();
		Map<String, Object> map = new HashMap<String, Object>();
		// 查询对象参数转换MAP
		map.put("data", map1);
		map.put("page", page.getCurrent());
		map.put("pageSize", page.getSize());
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity httpEntity = new HttpEntity<>(map, httpHeaders);

		ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrl, HttpMethod.POST, httpEntity,
				ResponseDTO.class);
		if (responseEntity.getBody() != null) {
			if (SUCCESS_CODE.equals(responseEntity.getBody().getReturnCode())) {
				ObjectMapper objectMapper = new ObjectMapper();
				objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
				page = objectMapper.convertValue(responseEntity.getBody().getData(), Page.class);
				List<OwnerVehicleVO> list = objectMapper.convertValue(page.getRecords(),
						new TypeReference<List<OwnerVehicleVO>>() {
						});

				page.setRecords(list);
			}
		}
		return page;
	}
}
