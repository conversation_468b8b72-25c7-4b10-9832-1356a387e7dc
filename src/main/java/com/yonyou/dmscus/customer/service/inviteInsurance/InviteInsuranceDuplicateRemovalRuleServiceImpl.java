package com.yonyou.dmscus.customer.service.inviteInsurance;


import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceDuplicateRemovalRuleMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceDuplicateRemovalRuleDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceDuplicateRemovalRulePO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 续保去重规则表，本章表所存储的数据，应该都是一个默认值。 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@Service
public class InviteInsuranceDuplicateRemovalRuleServiceImpl implements InviteInsuranceDuplicateRemovalRuleService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteInsuranceDuplicateRemovalRuleMapper inviteInsuranceDuplicateRemovalRuleMapper;

    /**
     * 根据查询条件返回结果集
     *
     * @param inviteInsuranceDuplicateRemovalRuleDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.inviteRule.InviteDuplicateRemovalRuleDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<InviteInsuranceDuplicateRemovalRuleDTO> selectListBySql(InviteInsuranceDuplicateRemovalRuleDTO
                                                                                inviteInsuranceDuplicateRemovalRuleDTO) {
        if (inviteInsuranceDuplicateRemovalRuleDTO == null) {
            inviteInsuranceDuplicateRemovalRuleDTO = new InviteInsuranceDuplicateRemovalRuleDTO();
        }
        InviteInsuranceDuplicateRemovalRulePO inviteInsuranceDuplicateRemovalRulePO = inviteInsuranceDuplicateRemovalRuleDTO.transDtoToPo
                (InviteInsuranceDuplicateRemovalRulePO.class);
        List<InviteInsuranceDuplicateRemovalRulePO> list = inviteInsuranceDuplicateRemovalRuleMapper.selectListBySql
                (inviteInsuranceDuplicateRemovalRulePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteInsuranceDuplicateRemovalRuleDTO.class)).collect(Collectors
                    .toList());
        }
    }

    /**
     * 分页查询对应数据
     *
     * @param page                                   分页对象
     * @param inviteInsuranceDuplicateRemovalRuleDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.repair.entity.dto.tools.InviteInsuranceDuplicateRemovalRuleDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    /*@Override
    public IPage<InviteInsuranceDuplicateRemovalRuleDTO> selectPageBysql(Page page, InviteInsuranceDuplicateRemovalRuleDTO inviteInsuranceDuplicateRemovalRuleDTO) {
        if (inviteInsuranceDuplicateRemovalRuleDTO == null) {
            inviteInsuranceDuplicateRemovalRuleDTO = new InviteInsuranceDuplicateRemovalRuleDTO();
        }
        InviteInsuranceDuplicateRemovalRulePO inviteInsuranceDuplicateRemovalRulePO = inviteInsuranceDuplicateRemovalRuleDTO.transDtoToPo(InviteInsuranceDuplicateRemovalRulePO.class);

        List<InviteInsuranceDuplicateRemovalRulePO> list = inviteInsuranceDuplicateRemovalRuleMapper.selectPageBySql(page, inviteInsuranceDuplicateRemovalRulePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteInsuranceDuplicateRemovalRuleDTO> result = list.stream().map(m -> m.transPoToDto(InviteInsuranceDuplicateRemovalRuleDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }*/

}
