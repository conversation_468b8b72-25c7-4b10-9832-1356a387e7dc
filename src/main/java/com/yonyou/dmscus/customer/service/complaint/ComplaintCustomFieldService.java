package com.yonyou.dmscus.customer.service.complaint;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldDTO;

import java.util.List;

/**
 * <p>
 * 客户投诉自定义字段 用于是否查询条件及是否排序判断 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
public interface ComplaintCustomFieldService  {
    /**
     * 分页查询
     * @param page
     * @param complaintCustomFieldDTO
     * @return
     */
    IPage<ComplaintCustomFieldDTO> selectPageBysql(Page page, ComplaintCustomFieldDTO complaintCustomFieldDTO);

    /**
     * 集合查询
     * @param complaintCustomFieldDTO
     * @return
     */
    List<ComplaintCustomFieldDTO> selectListBySql(ComplaintCustomFieldDTO complaintCustomFieldDTO);

    /**
     * 通过id查询
     * @param id
     * @return
     */
    ComplaintCustomFieldDTO getById(Long id);

    /**
     * 新增
     * @param complaintCustomFieldDTO
     * @return
     */
    int insert(ComplaintCustomFieldDTO complaintCustomFieldDTO);

    /**
     * 更新
     * @param id
     * @param complaintCustomFieldDTO
     * @return
     */
    int update(Long id, ComplaintCustomFieldDTO complaintCustomFieldDTO);

    /**
     * 通过id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

}
