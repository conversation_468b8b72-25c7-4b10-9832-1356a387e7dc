package com.yonyou.dmscus.customer.service.goodwill;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFinalPartInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairInfoDTO;


                                                                                    /**
 * <p>
 * 亲善预申请最终解决方案配件信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
public interface GoodwillApplyFinalPartInfoService  {
	public IPage<GoodwillApplyFinalPartInfoDTO>selectPageBysql(Page page,GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO);
	public List<GoodwillApplyFinalPartInfoDTO>selectListBySql(GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO);
	public GoodwillApplyFinalPartInfoDTO getById(Long id);
	public int insert(GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO);
	public int update(Long id, GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO);
	public int deleteById(Long id);
	public int deleteBatchIds(String ids);
	public List<GoodwillApplyFinalPartInfoDTO> queryPartAmountInfo(Integer goodwillType,Long goodwillApplyId);

}
