package com.yonyou.dmscus.customer.service.inviteSaAllocateRule;


import com.yonyou.dmscus.customer.dto.InvitationRuleVcdcParamsVo;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.UserInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteSaAllocateRule.InviteSaAllocateRuleDTO;
import com.yonyou.dmscus.customer.service.common.IBaseService;

import java.util.List;


/**
 * <p>
 * SA分配规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
public interface InviteSaAllocateRuleService extends IBaseService<InviteSaAllocateRuleDTO> {

    int saveInviteSaAllocateRule(InviteSaAllocateRuleDTO inviteSaAllocateRuleDTO);

    List<UserInfoDTO> getUserInfo();

    InviteSaAllocateRuleDTO getInviteSaAllocateRuleDlr();

    List<InviteSaAllocateRuleDTO> getInviteSaAllocateRule(InvitationRuleVcdcParamsVo vo);
}
