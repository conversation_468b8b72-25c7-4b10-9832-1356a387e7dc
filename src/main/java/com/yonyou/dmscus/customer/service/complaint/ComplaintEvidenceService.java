package com.yonyou.dmscus.customer.service.complaint;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintEvidenceDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.InsertComplaitEvidenceDTO;

import java.util.List;

/**
 * <p>
 * 客户投诉证据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface ComplaintEvidenceService  {
    /**
     * 分页查询
     * @param page
     * @param complaintEvidenceDTO
     * @return
     */
    IPage<ComplaintEvidenceDTO> selectPageBysql(Page page, ComplaintEvidenceDTO complaintEvidenceDTO);

    /**
     * 集合查询
     * @param complaintEvidenceDTO
     * @return
     */
    List<ComplaintEvidenceDTO> selectListBySql(ComplaintEvidenceDTO complaintEvidenceDTO);

    /**
     * 通告id查询
     * @param id
     * @return
     */
    ComplaintEvidenceDTO getById(Long id);

    /**
     * 新增
     * @param complaintEvidenceDTO
     * @return
     */
    int insert(ComplaintEvidenceDTO complaintEvidenceDTO);

    /**
     * 跟新
     * @param id
     * @param complaintEvidenceDTO
     * @return
     */
    int update(Long id, ComplaintEvidenceDTO complaintEvidenceDTO);

    /**
     * 通过id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 新增证据
     * @param insertComplaitEvidenceDTO
     * @return
     */
    int insertComplaitEvidence(InsertComplaitEvidenceDTO insertComplaitEvidenceDTO);

    int deleteComplaitEvidence(List<ComplaintEvidenceDTO> complaintEvidenceDTOS);
}
