package com.yonyou.dmscus.customer.service.impl.talkskill;


import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.talkskill.TalkskillPageMapper;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillPagePO;
import com.yonyou.dmscus.customer.service.talkskill.TalkskillPageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;


/**
 * <p>
 * 话术使用页面 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-20
 */
@Service
public class TalkskillPageServiceImpl implements TalkskillPageService {
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        TalkskillPageMapper talkskillPageMapper;

        /**
         * 根据查询条件返回结果集
         *
         *
         * @return java.util.List<com.yonyou.dmscus.repair.entity.dto.TalkskillTypeDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<TalkskillPagePO> selectListBySql (){
            TalkskillPagePO talkskillPagePo = new TalkskillPagePO();
            List<TalkskillPagePO> list = talkskillPageMapper.selectListBySql(talkskillPagePo);
            if(CommonUtils.isNullOrEmpty(list)){
                return null;
            }else{
                return  list;
            }
        }

}
