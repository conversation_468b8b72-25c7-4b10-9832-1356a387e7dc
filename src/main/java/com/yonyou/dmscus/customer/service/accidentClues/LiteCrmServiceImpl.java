package com.yonyou.dmscus.customer.service.accidentClues;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.RedisConstants;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class LiteCrmServiceImpl implements LiteCrmService{
    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Value("${accidentClue.liteCrm.token.username:nb_accident}")
    private String userName;
    @Value("${accidentClue.liteCrm.token.password:nb123456}")
    private String password;
    @Value("${accidentClue.liteCrm.url.prefix:https://litecrm-uat.digitalvolvo.com}")
    private String prefix;
    @Value("${accidentClue.liteCrm.url.login:/auth/login}")
    private String login;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public String getCrmToken() throws ServiceBizException {
        logger.info("**********getCrmToken start**********");
        RBucket<String> bucket = redissonClient.getBucket(RedisConstants.KEY_AC_PUSH_LITE_CRM_TOKEN, new StringCodec("utf-8"));
        if (!ObjectUtils.isEmpty(bucket) && !ObjectUtils.isEmpty(bucket.get())){
            logger.info("缓存获取token结果：{}", bucket.get());
            return bucket.get();
        }
        return getTokenToCrm(bucket);
    }
    /**
     * 请求crm获取token
     * @return
     */
    public String getTokenToCrm(RBucket<String> bucket) {
        logger.info("getTokenToCrm start");
        RLock lock = redissonClient.getLock(RedisConstants.KEY_AC_PUSH_LITE_CRM_TOKEN_LOCK);
        try {
            if(!lock.tryLock()){
                logger.info("getTokenToCrm tryLock time out");
                return null;
            }
            if (!ObjectUtils.isEmpty(bucket) && !ObjectUtils.isEmpty(bucket.get())){
                logger.info("缓存获取token结果：{}", bucket.get());
                return bucket.get();
            }
            Map<String, String> accountMap = new HashMap<>();
            accountMap.put("username", userName);
            accountMap.put("password", password);
            HttpEntity<Object> httpEntity = new HttpEntity<>(accountMap, new HttpHeaders());
            String url = prefix + login;
            logger.info("get liteCrm token url: {}", url);
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<JSONObject> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, JSONObject.class);
            logger.info("liteCrm token 获取结果: {}", response);
            String token = response.getHeaders().get(HttpHeaders.AUTHORIZATION).get(0);
            logger.info("token: {}", token);
            if(StringUtils.isNotBlank(token)){
                bucket.set(token);
                bucket.expire(RedisConstants.EXPIRE_AC_PUSH_LITE_CRM_TOKEN, TimeUnit.MINUTES);
                logger.info("setRedis success");
                return token;
            }else{
                throw new ServiceBizException("liteCrm token get fail");
            }
        }catch (Exception e){
            logger.info("getTokenToCrmFail:", e);
            throw new ServiceBizException("系统繁忙,请稍后重试！");
        }finally {
            if(null != lock){
                try {
                    lock.unlock();
                }catch (Exception e){
                    logger.error("getTokenToCrmFail:", e);
                }
            }
        }
    }
}
