package com.yonyou.dmscus.customer.service.goodwill;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyMailHistoryDTO;

/**
 * <p>
 * 亲善预申请邮件发送记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
public interface GoodwillApplyMailHistoryService {

	IPage<GoodwillApplyMailHistoryDTO> selectPageBysql(Page page,
			GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO);

	List<GoodwillApplyMailHistoryDTO> selectListBySql(GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO);

	GoodwillApplyMailHistoryDTO getById(Long id);

	int insert(GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO);

	int update(Long id, GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO);

	int deleteById(Long id);

	int deleteBatchIds(String ids);

	int sendAgain(Long id);

}
