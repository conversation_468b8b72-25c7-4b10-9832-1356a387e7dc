package com.yonyou.dmscus.customer.service.inviteInsurance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.FullLeadsFollowDto;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InsuranceSaSllocateDlrDto;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleCustomerNumberDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.SaCustomerNumberDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleCustomerNumberPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO;
import com.yonyou.dmscus.customer.service.common.IBaseService;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 车辆邀约续保记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
public interface InviteInsuranceVehicleRecordService extends IBaseService<InviteInsuranceVehicleRecordDTO> {


        //保险跟进-邀约记录（----chensh）
        IPage<InviteInsuranceVehicleRecordDTO> selectFollowInsureRecord(Page page, InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO);

        int saveFollowInsureRecord(InviteInsuranceVehicleRecordDetailDTO dto);

        List<Map> exportExcelFollowInsure(InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO);

        //保险跟进失败 -- 计划任务
        void updateInsureFollowStatus();

        IPage<InviteInsuranceVehicleRecordDTO> getInviteInsuranceVehicleRecord(Page page, InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO);

        int saveInsuranceSaSllocate(InsuranceSaSllocateDlrDto saSllocateDlrDto);

        Integer getNeedDistribute(List<Integer> leaveIds);

        List<InviteInsuranceVehicleRecordDTO> getInviteInsuranceVehicleRecordInfo(String vin, Long id);
        
        String saveSaCustomerNumber(InviteInsuranceVehicleCustomerNumberDTO saCustomerNumberDTO);
        
        List<CallDetailsPO> callDetailList(Long insuranceDetailId);
        
        List<InviteInsuranceVehicleCustomerNumberPO> selectCusList(Long id);

        InviteInsuranceVehicleCustomerNumberPO selectInsuranceUserByVin(String vin);

        void updateRecordOrderStatus();

        IPage<InviteInsuranceVehicleRecordDTO> getInviteInsuranceVehicleRecordCPort(Page page, InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO);
        void updateRecordOrderStatusNew();

        String fixSaCustomerNumber(List<FullLeadsFollowDto> followList);
}
