package com.yonyou.dmscus.customer.service.complaint.impl.complaint;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.service.excel.ExcelRead;
import com.yonyou.dmscloud.framework.service.excel.ExcelReadCallBack;
import com.yonyou.dmscloud.framework.service.excel.impl.AbstractExcelReadCallBack;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscloud.function.utils.common.StringUtils;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintDealerCcmRefMapper;
import com.yonyou.dmscus.customer.dao.complaint.TeComplaintDealerCcmRefImportMapper;
import com.yonyou.dmscus.customer.dto.OrgSearchParams;
import com.yonyou.dmscus.customer.dto.ResponseDto;
import com.yonyou.dmscus.customer.dto.UserInfoOutDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.IsExistByCodeDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.*;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintDealerCcmRefPO;
import com.yonyou.dmscus.customer.entity.po.complaint.TeComplaintDealerCcmRefImportPO;
import com.yonyou.dmscus.customer.middleInterface.CompanyDetailByCodeDTO;
import com.yonyou.dmscus.customer.middleInterface.IsExistByCodeVO;
import com.yonyou.dmscus.customer.middleInterface.ResponseDTO;
import com.yonyou.dmscus.customer.middleInterface.ResponseListDTO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintDealerCcmRefService;
import com.yonyou.dmscus.customer.service.complaint.TeComplaintDealerCcmRefImportService;
import com.yonyou.dmscus.customer.service.impl.CommonServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;




     /**
 * <p>
 * 客户投诉进销商和CCM复杂人对应关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
@Service
public class ComplaintDealerCcmRefServiceImpl implements ComplaintDealerCcmRefService {
         /**
          * 日志说明
          */
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        ComplaintDealerCcmRefMapper complaintDealerCcmRefMapper;
         @Resource
         ExcelRead<CcmImportDTO> excelReadService;

         @Resource
         private RestTemplate directRestTemplate;

         @Autowired
         private  CommonServiceImpl commonService;

         @Autowired
         private MidUrlProperties midUrlProperties;

         @Autowired
         TeComplaintDealerCcmRefImportService teComplaintDealerCcmRefImportService;

         @Resource
         TeComplaintDealerCcmRefImportMapper teComplaintDealerCcmRefImportMapper;

        /**
         * 分页查询对应数据
         *
         * @param page 分页对象
         * @param complaintDealerCcmRefDTO 查询条件
         * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintDealerCcmRefDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public IPage<ComplaintDealerCcmRefDTO>selectPageBysql(Page page,ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO){
            if(complaintDealerCcmRefDTO ==null){
                complaintDealerCcmRefDTO =new ComplaintDealerCcmRefDTO();
            }
            ComplaintDealerCcmRefPO complaintDealerCcmRefPo =complaintDealerCcmRefDTO.transDtoToPo(ComplaintDealerCcmRefPO.class);

            List<ComplaintDealerCcmRefPO>list= complaintDealerCcmRefMapper.selectPageBySql(page,complaintDealerCcmRefPo);
            if(CommonUtils.isNullOrEmpty(list)){
                page.setRecords(new ArrayList<>());
                return page;
            }else{
                List<ComplaintDealerCcmRefDTO>result=list.stream().map(m->m.transPoToDto(ComplaintDealerCcmRefDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param complaintDealerCcmRefDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintDealerCcmRefDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<ComplaintDealerCcmRefDTO>selectListBySql(ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO){
            if(complaintDealerCcmRefDTO ==null){
                complaintDealerCcmRefDTO =new ComplaintDealerCcmRefDTO();
            }
            ComplaintDealerCcmRefPO complaintDealerCcmRefPo =complaintDealerCcmRefDTO.transDtoToPo(ComplaintDealerCcmRefPO.class);
            List<ComplaintDealerCcmRefPO>list= complaintDealerCcmRefMapper.selectListBySql(complaintDealerCcmRefPo);
            if(CommonUtils.isNullOrEmpty(list)){
                return new ArrayList<>();
            }else{
                return list.stream().map(m->m.transPoToDto(ComplaintDealerCcmRefDTO.class)).collect(Collectors.toList());
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintDealerCcmRefDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public ComplaintDealerCcmRefDTO getById(Long id){
            ComplaintDealerCcmRefPO complaintDealerCcmRefPo = complaintDealerCcmRefMapper.selectById(id);
            if(complaintDealerCcmRefPo!=null){
                return complaintDealerCcmRefPo.transPoToDto(ComplaintDealerCcmRefDTO.class);
            }else{
                throw new DALException("查询失败,没有符合条件的数据");
            }
        }

        /**
         * 根据DTO 进行数据新增
         *
         * @param complaintDealerCcmRefDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO){
            //对对象进行赋值操作
            ComplaintDealerCcmRefPO complaintDealerCcmRefPo = complaintDealerCcmRefDTO.transDtoToPo(ComplaintDealerCcmRefPO.class);
            //执行插入
            int row= complaintDealerCcmRefMapper.insert(complaintDealerCcmRefPo);
            //返回插入的值
            return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param complaintDealerCcmRefDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO){
            ComplaintDealerCcmRefPO complaintDealerCcmRefPo = complaintDealerCcmRefMapper.selectById(id);
            //对对象进行赋值操作
            complaintDealerCcmRefDTO.transDtoToPo(complaintDealerCcmRefPo);
            //执行更新
            int row= complaintDealerCcmRefMapper.updateById(complaintDealerCcmRefPo);
            return row;
        }

        /**
         * 根据ID 进行删除数据
         *
         * @param id
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteById(Long id){
            int deleteCount= complaintDealerCcmRefMapper.deleteById(id);
            logger.debug("删除数量：{}",deleteCount);
            if(deleteCount<=0){
                throw new DALException("删除失败,没有符合条件的数据");
            }else{
                return deleteCount;
            }
        }

         @Override
         public ImportTempResult<TeComplaintDealerCcmRefImportPO> importTempTools(MultipartFile importFile) {
             // 获取登录用户信息
             LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
             // 删除临时表数据 该登录用户的数据
             teComplaintDealerCcmRefImportMapper.deleteAll(loginInfoDto.getUserId());

             // 匿名内部类
             try {
                 excelReadService.analyzeExcelFirstSheet(importFile, new AbstractExcelReadCallBack<CcmImportDTO>(
                         CcmImportDTO.class, new ExcelReadCallBack<CcmImportDTO>() {

                     @Override
                     public void readRowCallBack(CcmImportDTO rowDto, boolean isValidateSucess) {
                         try {
                             if (isValidateSucess) {
                                 // 插入临时表
                                 this.importTempTools(rowDto);
                             }
                         } catch (Exception e) {
                             throw e;
                         }
                     }
                     private void importTempTools(CcmImportDTO rowDto) {
                         // 1. 保存导入信息
                         TeComplaintDealerCcmRefImportDTO importPo = new TeComplaintDealerCcmRefImportDTO();

                         importPo.setLineNumber(rowDto.getLineNumber());
                         importPo.setDealerCode(rowDto.getDealerCode());
                         importPo.setCcmMan(rowDto.getCcmMan());
                         teComplaintDealerCcmRefImportService.insert(importPo);
                     }
                 }));
             } catch (IOException e) {
                 e.printStackTrace();
             }
             ImportTempResult<TeComplaintDealerCcmRefImportPO> importResult = this.checkTmpData();
             return importResult;
         }

         @Override
         public int importTools() {
             // 获取登录用户信息
             LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
             List<TeComplaintDealerCcmRefImportPO> list = teComplaintDealerCcmRefImportMapper.querySuccess(loginInfoDto.getUserId());
             TeComplaintDealerCcmRefImportDTO returnDTO=new TeComplaintDealerCcmRefImportDTO();
             CcmImportDto1 ccmImportDto1=new CcmImportDto1();
             if(list.size() > 0) {
                 for(int i=0;i<list.size();i++) {
                     CompanyDetailByCodeDTO companyInfo = this.getCompanyInfo(list.get(i).getDealerCode());
                     SelectSmallManagerDTO selectSmallManagerDTO=new SelectSmallManagerDTO();
                     if(!StringUtils.isNullOrEmpty(companyInfo.getAfterSmallAreaId())){
                         selectSmallManagerDTO.setOrgId(companyInfo.getAfterSmallAreaId().intValue());
                         List<String> roleCode=new ArrayList<>();
                         roleCode.add("SHQYJL");
                         selectSmallManagerDTO.setRoleCode(roleCode);
                         List<SmallManagerDataDTO> smallManagerList=this.getSmallManager(selectSmallManagerDTO);
                         if(smallManagerList.size()!=0){
                             ccmImportDto1.setAfterSmallAreaName(smallManagerList.get(0).getEmployeeName());
                         }
                     }
                     returnDTO.setDealerCode(companyInfo.getCompanyCode());
                     returnDTO.setDealerName(companyInfo.getCompanyNameCn());
                     ccmImportDto1.setDealerName(companyInfo.getCompanyNameCn());
                     ccmImportDto1.setAfterBigAreaName(companyInfo.getAfterBigAreaName());
//                     ccmImportDto1.setAfterSmallAreaName(companyInfo.getAfterSmallAreaName());
                     ccmImportDto1.setGroupCompanyName(companyInfo.getGroupCompanyName());
                     ccmImportDto1.setAfterBigAreaId(companyInfo.getAfterBigAreaId());
                     ccmImportDto1.setAfterSmallAreaId(companyInfo.getAfterSmallAreaId());
                     OrgSearchParams orgSearchParams=new OrgSearchParams();
                     List<String> roleList=new ArrayList<>();
                     roleList.add("SHKS");
                     orgSearchParams.setRoleCode(roleList);
                     List<UserInfoOutDTO>CCMlist= commonService.getUserList(orgSearchParams);
                     for(int y=0;y<CCMlist.size();y++){
                         if(CCMlist.get(y).getEmployeeName().equals(list.get(i).getCcmMan())){
                             ccmImportDto1.setCcmManId(CCMlist.get(y).getUserId());
                         }
                     }
                     // 导入该用户的临时导入数据
                    ccmImportDto1.setId(list.get(i).getId());
                    ccmImportDto1.setUserId(loginInfoDto.getUserId());
                     complaintDealerCcmRefMapper.deleteAll(list.get(i).getDealerCode());
                     complaintDealerCcmRefMapper.importCcm(ccmImportDto1);
                 }

             }
             teComplaintDealerCcmRefImportMapper.deleteAll(loginInfoDto.getUserId());
             return 1;
         }

         @Override
         public boolean updateCcmAll(ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO) {
             return complaintDealerCcmRefMapper.updateCcmAll(complaintDealerCcmRefDTO);
         }

         /**
          * 校验更新临时表，返回校验结果对象
          *
          * @return
          * <AUTHOR>
          * @since 2020/03/19
          */
         private ImportTempResult<TeComplaintDealerCcmRefImportPO> checkTmpData() {
             ImportTempResult<TeComplaintDealerCcmRefImportPO> importResult = new ImportTempResult<TeComplaintDealerCcmRefImportPO>();
             // 获取登录用户信息
             LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
             List<TeComplaintDealerCcmRefImportPO> list = teComplaintDealerCcmRefImportMapper.selectAllCodes(loginInfoDto.getUserId());
             // 校验经销商代码是否存在
             IsExistByCodeDTO isExistByCodeDTO = new IsExistByCodeDTO();
             StringBuffer delearCodes = new StringBuffer();
             for (int j = 0; j < list.size(); j++) {
                 delearCodes.append(list.get(j).getDealerCode()+ ",");

             }
             IsExistByCodeVO rs = this.checkDealerCodes(delearCodes.toString());
             if (!rs.getIsAllExist()) {
                 List<String> errorList = rs.getNotExistCompanyCodeList();
                 teComplaintDealerCcmRefImportMapper.updateCheckDealerCodeExist(loginInfoDto.getUserId(), errorList);
             }
             // 查询错误项
             List<TeComplaintDealerCcmRefImportPO> listError = teComplaintDealerCcmRefImportMapper.queryError(loginInfoDto.getUserId());
             if (!CommonUtils.isNullOrEmpty(listError)) {
                 importResult.setErrorList(listError);
             } else {
                 System.out.println("未查到错误数据！");
             }
             // 查询成功项
             List<TeComplaintDealerCcmRefImportPO> listSuccess = teComplaintDealerCcmRefImportMapper.querySuccess(loginInfoDto.getUserId());
             if (!CommonUtils.isNullOrEmpty(listSuccess)) {
                 importResult.setSuccessList(listSuccess);
             } else {
                 teComplaintDealerCcmRefImportMapper.deleteAll(loginInfoDto.getUserId());
                 System.out.println("未查到正确数据！");
             }
             // 查询正确数据数
             importResult.setSuccessCount(teComplaintDealerCcmRefImportMapper.querySucessCount(loginInfoDto.getUserId()));
             return importResult;
         }

         /**
          * 根据导入的正确的经销商获取经销商详细信息
          * @return
          */
         public CompanyDetailByCodeDTO getCompanyInfo(String codes){

             String successCode = "0";
             String requestUrl = midUrlProperties.getMidEndOrgCenter()+midUrlProperties.getSelectByCompanyCode();
             IsExistByCodeDTO dto = new IsExistByCodeDTO();
             dto.setCompanyCode(codes);
             HttpHeaders httpHeaders = new HttpHeaders();
             httpHeaders.setContentType(MediaType.APPLICATION_JSON);
             HttpEntity<IsExistByCodeDTO> httpEntity = new HttpEntity<>(dto, httpHeaders);
             ResponseEntity<ResponseListDTO> responseEntity =directRestTemplate.exchange(requestUrl, HttpMethod.POST,httpEntity,
                     ResponseListDTO.class);
             if(responseEntity.getBody()!=null) {
                 if(successCode.equals(responseEntity.getBody().getReturnCode())) {
                     ObjectMapper objectMapper = new ObjectMapper();
                     objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                     List<CompanyDetailByCodeDTO> rs = objectMapper.convertValue(responseEntity.getBody().getData(),
                             new TypeReference<List<CompanyDetailByCodeDTO>>(){}) ;
                     return rs.get(0);
                 }else{
                     throw new DALException("获取经销商详细信息接口异常，请稍后再试");
                 }
             }
             return null;
         }
         /**
          * 返回错误的经销商代码 调用中台接口
          *
          * @param codes
          * @return
          */
         private IsExistByCodeVO checkDealerCodes(String codes) {
             String successCode = "0";
             String requestUrl = midUrlProperties.getMidEndOrgCenter()+ midUrlProperties.getIsExistByCode();
             IsExistByCodeDTO dto = new IsExistByCodeDTO();
             dto.setCompanyCode(codes);
             HttpHeaders httpHeaders = new HttpHeaders();
             httpHeaders.setContentType(MediaType.APPLICATION_JSON);
             HttpEntity<IsExistByCodeDTO> httpEntity = new HttpEntity<>(dto, httpHeaders);
             ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrl, HttpMethod.POST, httpEntity,
                     ResponseDTO.class);
             if (responseEntity.getBody() != null) {
                 if (successCode.equals(responseEntity.getBody().getReturnCode())) {
                     ObjectMapper objectMapper = new ObjectMapper();
                     objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                     return objectMapper.convertValue(responseEntity.getBody().getData(), IsExistByCodeVO.class);
                 } else {
                     throw new DALException("经销商代码校验接口异常，请稍后再试");
                 }
             }
             return null;
         }

         /**
          * 获取小区经理名字
          */
         public List<SmallManagerDataDTO> getSmallManager(SelectSmallManagerDTO selectSmallManagerDTO){

             String successCode = "0";
             String requestUrl = midUrlProperties.getMidEndAuthCenter()+midUrlProperties.getRoleOrgIdUser();
             Map<String, Object> data = new HashMap<>(16);
             data.put("data", selectSmallManagerDTO);
             JSONObject json = directRestTemplate.postForEntity(requestUrl, data, JSONObject.class).getBody();
             ResponseDto<List<SmallManagerDataDTO>> responseDto = JSONObject.parseObject(json.toJSONString(),
                     new com.alibaba.fastjson.TypeReference<ResponseDto<List<SmallManagerDataDTO>>>(SmallManagerDataDTO.class) {
                     });
             if (responseDto.getReturnCode().equals("Error1004")) {
                 throw new DALException("调用中台接口获取用户信息失败!");
             }
             if (responseDto.getData() != null && responseDto.getReturnCode().equals("0")) {
                 List<SmallManagerDataDTO> userList = responseDto.getData();
                 return userList;
             }
             return Collections.emptyList();
         }


     }
