package com.yonyou.dmscus.customer.service.impl.invitationautocreate;

import com.google.common.collect.Lists;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dao.invitationautocreate.InviteVehicleTaskMapper;
import com.yonyou.dmscus.customer.service.invitationautocreate.IncRepCleanDataService;
import com.yonyou.dmscus.customer.service.invitationautocreate.po.InviteVehiclePO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;


/**
 * 重复线索数据清洗
 */
@Slf4j
@Service
@RefreshScope
public class IncRepCleanDataServiceImpl implements IncRepCleanDataService {

    @Resource
    InviteVehicleTaskMapper inviteVehicleTaskMapper;
    @Resource
    InviteVehicleRecordMapper inviteVehicleRecordMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doTaskClean(List<InviteVehiclePO> cloTask, List<InviteVehiclePO> upTask) {
        log.info("doTaskClean,start");
        if (CollectionUtils.isNotEmpty(cloTask)) {
            log.info("doTaskClean,cloTask:{}", cloTask.size());
            Lists.partition(cloTask, 500).forEach(inviteVehicleTaskMapper::doProDataCloseTask);
        }
        if (CollectionUtils.isNotEmpty(upTask)) {
            log.info("doTaskClean,upTask:{}", upTask.size());
            Lists.partition(upTask, 500).forEach(inviteVehicleTaskMapper::doProDataUpdateTask);
        }
        log.info("doTaskClean,end");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doClueClean(List<InviteVehiclePO> cloClue, List<InviteVehiclePO> upClue) {
        log.info("doClueClean,start");
        if (CollectionUtils.isNotEmpty(cloClue)) {
            log.info("doClueClean,cloClue:{}", cloClue.size());
            //关闭主线索
            Lists.partition(cloClue, 500).forEach(inviteVehicleRecordMapper::doProDataCloseClue);
            //关闭子线索
            Lists.partition(cloClue, 500).forEach(inviteVehicleRecordMapper::doProDataCloseSubClue);
        }
        if (CollectionUtils.isNotEmpty(upClue)) {
            log.info("doClueClean,upClue:{}", upClue.size());
            Lists.partition(upClue, 500).forEach(inviteVehicleRecordMapper::doProDataUpdateClue);
        }
        log.info("doClueClean,end");
    }
}
