package com.yonyou.dmscus.customer.service.pushRecord;

import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.RemotePushSinceTypeEnum;
import com.yonyou.dmscus.customer.constants.RemotePushStatusEnum;
import com.yonyou.dmscus.customer.entity.po.pushRecord.RemotePushRecordPO;

/**
 * <AUTHOR>
 * @Date 2023/11/16 18:12
 * @Version 1.0
 */
public interface RemotePushRecordService {

    /**
     * 事故线索LiteCRM推送记录
     * @param reqParams 推送参数
     * @param respParams 响应结果
     * @param sinceType 推送场景
     * @param acId 线索信息
     * @param status 处理状态
     * @throws ServiceBizException exc
     */
    void accidentClueLiteCrmPushRecord(String reqParams, String respParams, Integer acId, RemotePushSinceTypeEnum sinceType, RemotePushStatusEnum status) throws ServiceBizException;


    /**
     * 更新事故线索补偿结果
     * @param record
     * @param resp
     * @param status
     * @throws ServiceBizException
     */
    void updateAcCompensateRecord(RemotePushRecordPO record, String resp, RemotePushStatusEnum status) throws ServiceBizException;
}
