package com.yonyou.dmscus.customer.service.talkskill;


import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillKeywordDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillKeywordPO;


import java.util.List;

/**
 * <p>
 * 话术关键词与话术关系 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-27
 */
public interface TalkskillKeywordService {
    List<TalkskillKeywordPO> selectListBySql(TalkskillKeywordDTO talkskillKeywordDTO);
    int insert(TalkskillKeywordDTO talkskillKeywordDTO);
    int update(Long id,TalkskillKeywordDTO talkskillKeywordDTO);
}
