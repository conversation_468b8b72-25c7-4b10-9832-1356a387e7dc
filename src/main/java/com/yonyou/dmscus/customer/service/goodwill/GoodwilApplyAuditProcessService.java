package com.yonyou.dmscus.customer.service.goodwill;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwilApplyAuditProcessDTO;

/**
 * <p>
 * 亲善审批流程表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-22
 */
public interface GoodwilApplyAuditProcessService {
	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwilApplyAuditProcessDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.bean.dto.GoodwilApplyAuditProcessDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public IPage<GoodwilApplyAuditProcessDTO> selectPageBysql(Page page,
			GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO);

	public List<GoodwilApplyAuditProcessDTO> selectListBySql(GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO);

	public GoodwilApplyAuditProcessDTO getById(Long id);

	public int insert(GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO);

	public int update(Long id, GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO);

	public int deleteById(Long id);

	public int deleteBatchIds(String ids);

	public List<Map> queryAuditProcess(Integer auditObject, Long id);

}
