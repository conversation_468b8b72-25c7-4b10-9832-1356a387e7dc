package com.yonyou.dmscus.customer.service.pushRecord;

import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.RemotePushSinceTypeEnum;
import com.yonyou.dmscus.customer.dao.pushRecord.RemotePushRecordMapper;
import com.yonyou.dmscus.customer.enums.RemotePushThreadSizeEnum;
import com.yonyou.dmscus.customer.service.accidentClues.CluePushService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023/12/15 18:52
 * @Version 1.0
 */
@Service
public class CompensateServiceImpl implements CompensateService{

    @Resource
    private RemotePushRecordMapper remotePushRecordMapper;
    @Resource
    private CluePushService cluePushService;

    Logger logger = LoggerFactory.getLogger(this.getClass());
    //每次查询条数限制数
    private static final int PAGE_SIZE = 1000;

    /**
     * 定时补偿
     * @throws ServiceBizException
     */
    @Override
    public void accidentCluePushCompensate() throws ServiceBizException {

        Integer totalCount = remotePushRecordMapper.countAcRemotePushCompensateList(RemotePushSinceTypeEnum.CLUE_INFO.getSinceType());
        if (totalCount == 0) {
            logger.info("未获取到需要补偿数据，结束补偿任务");
            return;
        }
        //根据待补偿数量获取异步线程数
        int threadSize = this.findThreadSize(totalCount);
        int limitSize = totalCount / threadSize;
        for (int i = 0; i < threadSize; i++) {
            int incrementI = i + 1;
            int limit = limitSize * i;
            int maxSize = limitSize * incrementI;
            this.compensate(limit, maxSize);
            logger.info("第 {} 批补偿结束", i);
        }
    }

    /**
     * 开始补偿
     * @param limit
     * @param maxSize
     * @throws ServiceBizException
     */
    private void compensate(int limit, int maxSize) throws ServiceBizException{

        int pageSize = PAGE_SIZE;
        int loopTimes = 0 == (maxSize - limit) / pageSize ? 1
                : (maxSize - limit) % pageSize == 0 ? (maxSize - limit) / pageSize
                : (maxSize - limit) / pageSize + 1;
        cluePushService.compensatePush(limit, maxSize, loopTimes, pageSize);
    }

    /**
     * 获取开启线程数量
     * @param totalCount
     * @return
     */
    private int findThreadSize(Integer totalCount){

        return totalCount < RemotePushThreadSizeEnum.THREAD_SIZE_LESS_THAN_50000.getCount() ? RemotePushThreadSizeEnum.THREAD_SIZE_LESS_THAN_50000.getThreadSize()
                : totalCount <= RemotePushThreadSizeEnum.THREAD_SIZE_LESS_THAN_100000.getCount() ? RemotePushThreadSizeEnum.THREAD_SIZE_LESS_THAN_100000.getThreadSize()
                : totalCount <= RemotePushThreadSizeEnum.THREAD_SIZE_GREATER_THAN_200000.getCount() ? RemotePushThreadSizeEnum.THREAD_SIZE_LESS_THAN_200000.getThreadSize()
                : RemotePushThreadSizeEnum.THREAD_SIZE_GREATER_THAN_200000.getThreadSize();
    }
}
