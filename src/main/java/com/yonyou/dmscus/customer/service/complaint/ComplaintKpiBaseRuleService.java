package com.yonyou.dmscus.customer.service.complaint;

    import com.baomidou.mybatisplus.core.metadata.IPage;
    import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
    import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleDTO;
    import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiSetDTO;
    import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiTest;
    import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRulePO;

    import java.util.List;


/**
 * <p>
 * 客户投诉KP基础规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
public interface ComplaintKpiBaseRuleService  {
    /**
     * 分页查询
     * @param page
     * @param complaintKpiBaseRuleDTO
     * @return
     */
    IPage<ComplaintKpiBaseRuleDTO>selectPageBysql(Page page,ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO);

    /**
     * 集合查询
     * @param complaintKpiBaseRuleDTO
     * @return
     */
     List<ComplaintKpiBaseRuleDTO> selectListBySql(ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO);

    /**
     * 通过id查询
     * @param id
     * @return
     */
    ComplaintKpiBaseRuleDTO getById(Long id);

    /**
     * 新增
     * @param complaintKpiBaseRuleDTO
     * @return
     */
    int insert(ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO);

    /**
     * 更新
     * @param id
     * @param complaintKpiBaseRuleDTO
     * @return
     */
    int update(Long id, ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO);

    /**
     * 通过id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 更新kpi
     * @param list
     * @return
     */

    int updateKpi(List<ComplaintKpiSetDTO> list);
}
