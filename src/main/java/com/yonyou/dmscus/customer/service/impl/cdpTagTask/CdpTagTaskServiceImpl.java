package com.yonyou.dmscus.customer.service.impl.cdpTagTask;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.configuration.CdpUrlProperties;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.voc.CdpTagTaskMapper;
import com.yonyou.dmscus.customer.dto.CdpTokenPortraitDto;
import com.yonyou.dmscus.customer.dto.cdp.CdpTagTaskParameterDto;
import com.yonyou.dmscus.customer.dto.cdp.TokenPortraitDto;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.CdpTagTaskPo;
import com.yonyou.dmscus.customer.enums.CacheKeyEnum;
import com.yonyou.dmscus.customer.service.cdpTagTask.qb.CdpTagTaskService;
import com.yonyou.dmscus.customer.service.common.common.CommonMethodService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocInviteVehicleTaskRecordService;
import com.yonyou.dmscus.customer.utils.RedisLockUtil.DistributedLockUtil;
import com.yonyou.dmscus.customer.utils.RedisLockUtil.IDistributedLock;
import com.yonyou.dmscus.customer.utils.Utills;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


import java.util.List;


/**
 * <AUTHOR>
 * @date 2020/11/10 0010
 */
@Service
public class CdpTagTaskServiceImpl implements CdpTagTaskService {

    Logger logger= LoggerFactory.getLogger(CdpTagTaskServiceImpl.class);

    @Resource(name = "cdpRestTemplate")
    private RestTemplate cdpRestTemplate;

    @Autowired
    InviteVehicleRecordService inviteVehicleRecordService;

    @Autowired
    CdpTagTaskMapper cdpTagTaskMapper;

    @Autowired
    VocInviteVehicleTaskRecordService vocInviteVehicleTaskRecordService;

    @Autowired
    CommonMethodService commonMethodService;

    @Autowired
    private CdpUrlProperties cdpUrlProperties;


    //保养灯-写入CDP标签同步任务数据
    @Override
    public void writesCdpTagTask(String startTime,String endTime) {
        logger.info("-------------writesCdpTagTask start------------");
        //处理时间参数
        startTime = StringUtils.isNotBlank(startTime) ? startTime : new SimpleDateFormat("yyyy-MM-dd").format(new Date())+" 00:00:00";
        endTime = StringUtils.isNotBlank(endTime) ? endTime : new SimpleDateFormat("yyyy-MM-dd").format(new Date())+" 23:59:59";
        logger.info("准备获取锁");
        //防重复
        IDistributedLock lock = DistributedLockUtil.getDistributedLock(
                CacheKeyEnum.cdp_tag_key.getKey("writesCdpTagTask"));
        if(!lock.antiDuplication()){
            logger.info("writesCdpTagTask,!lock.antiDuplication() 数据处理中");
            return;
        }
        ExecutorService service = null;
        try {
            List<String> whiteList = inviteVehicleRecordService.getWhiteLists(CommonConstants.MOD_TYPE_91111011,CommonConstants.MOD_TYPE_91111015);
            logger.info("checkLeadCompletionByWorkOrder,listCode:{}", whiteList);
            Integer countCdpTag = cdpTagTaskMapper.selectCountCdpTag(whiteList,startTime,endTime);
            Integer pageSize = cdpUrlProperties.getWritesLimit();
            logger.info("总行数totalRows={},每页数量pageSize={}", countCdpTag, pageSize);
            Page cdpTagTask = new Page();
            cdpTagTask.setSize(pageSize);
            cdpTagTask.setTotal(countCdpTag);
            // 获取总页数
            int page = Integer.parseInt(String.valueOf(cdpTagTask.getPages()));
            // 线程数量
            int threadSize = Utills.getThreadSize(countCdpTag);
            logger.info("线程数threadSize={}", threadSize);
            if (threadSize>0){
                service = Executors.newFixedThreadPool(threadSize);
                final CountDownLatch latch = new CountDownLatch(page);
                logger.info("开启闭锁={}，page={}", latch, page);
                // 依据页码创建任务数量
                for (int i = 1; i <= page; i++) {
                    final Page pagination = new Page(i, pageSize);
                    String finalStartTime = startTime;
                    String finalEndTime = endTime;
                    Runnable task = () -> {
                        try {
                            selectCdpTag(whiteList , pagination , finalStartTime, finalEndTime);
                        } catch (Exception e) {
                            logger.error(">>>多线程查询数据异常:{}", e);
                        } finally {
                            latch.countDown();
                        }
                    };
                    service.submit(task);
                }
                latch.await();
            }else {
                for (int i = 1; i <= page; i++) {
                    final Page pagination = new Page(i, pageSize);
                    selectCdpTag(whiteList , pagination ,startTime , endTime);
                }
            }
            logger.info("-------------writesCdpTagTask end------------");
        }catch (Exception e) {
            logger.error("disposeCdpTagTask，同步标签处理异常",e);
        } finally {
            if (service != null) {
                logger.info("写入CDP标签同步任务数据结束释放锁");
                service.shutdown();
            }
            logger.info("写入CDP标签同步任务数据结束释放锁");
            lock.release();
        }
    }

    private void selectCdpTag(List<String> whiteList, Page pagination,String startTime, String endTime) {
        IPage<String> pageResult = selectPageBysql(pagination, whiteList, startTime, endTime);
        if (pageResult != null) {
            List<String> vinList = pageResult.getRecords();
            insertCdpTagTask(vinList);
        }
    }

    @Transactional
    public void insertCdpTagTask(List<String> vinList) {
        cdpTagTaskMapper.insertCdpTagTask(vinList);
    }

    public IPage<String> selectPageBysql(Page page, List<String> whiteList,String startTime,String endTime) throws ServiceBizException {
        List<String> resList = cdpTagTaskMapper.selectInviteStatusPlan(page, whiteList,startTime,endTime);
        // 准备返回数据
        if (CommonUtils.isNullOrEmpty(resList)) {
            page.setRecords(Collections.emptyList());
        } else {
            page.setRecords(resList);
        }
        return page;
    }

    public IPage<String> selectTaskList(Page page, String startTime, String endTime) throws ServiceBizException {
        List<String> resList = cdpTagTaskMapper.selectTaskList(page,startTime,endTime);
        // 准备返回数据
        if (CommonUtils.isNullOrEmpty(resList)) {
            page.setRecords(Collections.emptyList());
        } else {
            page.setRecords(resList);
        }
        return page;
    }

    @Override
    public void disposeCdpTagTask(String startTime, String endTime) {
        logger.info("-------------disposeCdpTagTask start------------");
        //处理时间参数
        startTime = StringUtils.isNotBlank(startTime) ? startTime : new SimpleDateFormat("yyyy-MM-dd").format(new Date())+" 00:00:00";
        endTime = StringUtils.isNotBlank(endTime) ? endTime : new SimpleDateFormat("yyyy-MM-dd").format(new Date())+" 23:59:59";
        logger.info("获取token准备获取锁");
        //用ID做防重复点击
        IDistributedLock lock = DistributedLockUtil.getDistributedLock(
                CacheKeyEnum.cdp_tag_key.getKey("disposeCdpTagTask"));
        if(!lock.antiDuplication()){
            logger.info("disposeCdpTagTask,!lock.antiDuplication() 数据处理中");
            return;
        }
        ExecutorService service = null;
        try {
            Integer countCdpTag = cdpTagTaskMapper.selectTaskCount(startTime,endTime);
            Integer pageSize = cdpUrlProperties.getDisposeLimit();
            logger.info("总行数totalRows={},每页数量pageSize={}", countCdpTag, pageSize);
            Page cdpTagTask = new Page();
            cdpTagTask.setSize(pageSize);
            cdpTagTask.setTotal(countCdpTag);
            // 获取总页数
            int page = Integer.parseInt(String.valueOf(cdpTagTask.getPages()));
            // 线程数量
            int threadSize = Utills.getThreadSize(countCdpTag);
            if (threadSize>0){
                service = Executors.newFixedThreadPool(threadSize);
                final CountDownLatch latch = new CountDownLatch(page);
                logger.info("开启闭锁={}，page={}", latch, page);
                // 依据页码创建任务数量
                for (int i = 1; i <= page; i++) {
                    final Page pagination = new Page(i, pageSize);
                    String finalStartTime = startTime;
                    String finalEndTime = endTime;
                    Runnable task = () -> {
                        try {
                            updateDailyMileAdviseInDate(finalStartTime, finalEndTime, pagination);
                        } catch (Exception e) {
                            logger.error(">>>多线程查询数据异常:{}", e);
                        } finally {
                            latch.countDown();
                        }
                    };
                    service.submit(task);
                }
                latch.await();
            }else {
                for (int i = 1; i <= page; i++) {
                    final Page pagination = new Page(i, pageSize);
                    updateDailyMileAdviseInDate(startTime, endTime, pagination);
                }
            }
            logger.info("-------------disposeCdpTagTask end------------");
        }catch (Exception e) {
            logger.error("disposeCdpTagTask，同步标签处理异常",e);
        } finally {
            if (service != null) {
                logger.info("VINS批量查询车辆标签运行结束释放锁");
                service.shutdown();
            }if (lock != null) {
                logger.info("VINS批量查询车辆标签运行结束释放锁");
                lock.release();
            }
        }
    }

    private void updateDailyMileAdviseInDate(String startTime, String endTime, Page pagination) {
        IPage<String> pageResult = selectTaskList(pagination, startTime, endTime);
        if (pageResult != null && pageResult.getSize()>0) {
            List<String> vinList = pageResult.getRecords();
            if (CollectionUtils.isNotEmpty(vinList)) {
                List<CdpTagTaskParameterDto> vinsList = this.getVins(vinList);
                if (vinsList != null && vinsList.size() > 0) {
                    updateDailyMileAdviseInDate(vinsList);
                } else {
                    logger.info("VINS批量查询车辆标签为空,vinList.fisrt：{}", vinList.get(0));
                }
            }
        }
    }

    @Transactional
    public void updateDailyMileAdviseInDate(List<CdpTagTaskParameterDto> list) {
        try {
            vocInviteVehicleTaskRecordService.updateDailyMile(list);
            inviteVehicleRecordService.updateAdviseInDate(list);
            cdpTagTaskMapper.updateCdpTagTask(list);
        }catch (Exception e) {
            String essage = e.getMessage().length() > 200 ? e.getMessage().substring(200) : e.getMessage();
            logger.error("VINS批量查询车辆标签，原因：{}", essage);
            cdpTagTaskMapper.updateTagTaskError(list,essage);
        }
    }
    @Override
    @Retryable(value = {ServiceBizException.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000, multiplier = 5))
    public List<CdpTagTaskParameterDto> getVins(List<String> vins) {
        List<CdpTagTaskParameterDto> list = new ArrayList<>();
        long timestamp = System.currentTimeMillis();
        logger.info("getVin,批量查询车辆标签-timestamp:{}", timestamp);

        List<String> whiteList = inviteVehicleRecordService.getWhiteList(CommonConstants.MOD_TYPE_91111011);
        logger.info("getVin,checkLeadCompletionByWorkOrder,listCode:{}", whiteList);
        TokenPortraitDto tokenDto = this.queryCacheToken();
        if (ObjectUtils.isNotEmpty(tokenDto) && StringUtils.isNotEmpty(tokenDto.getToken())) {
            JSONObject param = new JSONObject();
            param.put("appid", cdpUrlProperties.getAppid());
            param.put("token", tokenDto.getToken());
            param.put("access_timestamp", String.valueOf(tokenDto.getTimestamp()));
            param.put("vins", vins);
            String url = cdpUrlProperties.getDomainUrl() + cdpUrlProperties.getVehicleLabelsUrl();
            logger.info("getVin,批量查询车辆标签-URL:{},vins.fisrt:{},vin.size:{}", url, vins.get(0), vins.size());
            String cdpResult = null;
            try {
                cdpResult = this.postForObject(url, param.toString());
            } catch (Exception e) {
                logger.error("getVin,批量查询车辆标签异常,请求CDP接口异常：", e);
                String message = e.getMessage().length() > 200 ? e.getMessage().substring(200) : e.getMessage();
                cdpTagTaskMapper.updateTagTask(vins, message);
                throw new ServiceBizException("getVin,批量查询车辆标签异常,请求CDP接口异常");
            }
            logger.info("getVin,批量查询车辆标签响应,vins.fisrt:{},vin.size:{}", vins.get(0), vins.size());
            if (StringUtils.isNotEmpty(cdpResult)) {
                JSONObject result = null;
                result = JSONObject.parseObject(cdpResult);
                Integer code = result.getInteger("code");
                if (code != 0) {
                    logger.info("getVin,result:{}", result);
                    throw new ServiceBizException("getVin,code返回异常");
                }
                JSONArray data = result.getJSONArray("data");
                if (ObjectUtils.isNotEmpty(data)) {
                    for (int i = 0; i < data.size(); i++) {
                        try {
                            CdpTagTaskParameterDto cdpTagTaskParameterDto = new CdpTagTaskParameterDto();
                            JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(data.get(i)));
                            if (ObjectUtils.isNotEmpty(jsonObject)) {
                                String vin = jsonObject.get("vin").toString();
                                cdpTagTaskParameterDto.setVin(vin);
                                cdpTagTaskParameterDto.setWhiteList(whiteList);
                                JSONArray tagList = jsonObject.getJSONArray("tagList");
                                tagList.stream()
                                        .map(tag -> JSON.toJSONString(tag))
                                        .map(JSONObject::parseObject)
                                        .forEach(tag -> {
                                            if (tag.getString("tagID").contains(cdpUrlProperties.getDailyMile()) && !StringUtils.isEmpty(tag.getString("tagValue"))) {
                                                cdpTagTaskParameterDto.setDailyMile(tag.getString("tagValue"));
                                            } else if (tag.getString("tagID").contains(cdpUrlProperties.getAdviseInDate()) && !StringUtils.isEmpty(tag.getString("tagValue"))) {
                                                cdpTagTaskParameterDto.setAdviseInDate(tag.getString("tagValue"));
                                            } else if (tag.getString("tagID").contains(cdpUrlProperties.getReturnIntentionLevel()) && !StringUtils.isEmpty(tag.getString("tagValue"))) {
                                                cdpTagTaskParameterDto.setReturnIntentionLevel(tag.getString("tagValue"));
                                            }
                                        });
                            }
                            list.add(cdpTagTaskParameterDto);
                        } catch (Exception e) {
                            logger.info("getVin,解析异常:", e);
                            String message = e.getMessage().length() > 200 ? e.getMessage().substring(200) : e.getMessage();
                            cdpTagTaskMapper.updateTagTask(vins, message);
                        }
                    }
                }
                return list;
            }
        }
        return list;
    }

    /**
     * 获取缓存 token
     *
     * @return
     */
    public TokenPortraitDto queryCacheToken() {
        CdpTokenPortraitDto cdpToken = commonMethodService.getCdpToken(null);
        TokenPortraitDto tokenPortraitDto = new TokenPortraitDto();
        tokenPortraitDto.setToken(cdpToken.getToken());
        tokenPortraitDto.setTimestamp(cdpToken.getAccessTimestamp());
        return tokenPortraitDto;
    }

    /**
     * 获取缓存 token
     *
     * @return
     */
    public TokenPortraitDto queryCacheToken(long timestamp) {
        IDistributedLock distributedLock = null;
        TokenPortraitDto token = new TokenPortraitDto();
        String key = CacheKeyEnum.cdp_token_key.getKey() + cdpUrlProperties.getAppid();
        logger.info("获取token准备获取锁");
        try {
            distributedLock = DistributedLockUtil.getDistributedLock(key, 1000 * 60 * 3, 1000 * 60 * 3);
            if (distributedLock.acquire()) {
                // 获取正在执行任务的线程
                Thread thread = Thread.currentThread();
                // 获取线程的名称
                String threadName = thread.getName();
                long id = thread.getId();
                logger.info("获取token已获取到锁,当前线程名称：{},线程id：{}",threadName,id);
                token = this.getCdpToken(timestamp);
            } else {
                logger.error("获取锁失败");
            }
        } catch (Exception e) {
            logger.error("获取锁失败"+e);
        }finally {
            if (distributedLock != null) {
                logger.info("VINS批量查询车辆标签运行结束释放锁");
                distributedLock.release();
            }
        }
        return token;
    }

    /**
     * 获取token
     *
     * @return
     */
    public TokenPortraitDto getCdpToken(long timestamp) {
        long startTime = System.currentTimeMillis();
        JSONObject param = new JSONObject();
        param.put("appid", cdpUrlProperties.getAppid());
        param.put("app_secret", cdpUrlProperties.getAppSecret());
        param.put("access_timestamp", timestamp);
        String url = cdpUrlProperties.getDomainUrl() + cdpUrlProperties.getTokenUrl();
        logger.info("获取CDP认证token-URL:{},请求参数：{}", url, param);
        String cdpResult = this.postForObject(url, param.toString());
        logger.info("获取CDP认证token-响应结果：{}", cdpResult);
        if (StringUtils.isEmpty(cdpResult)) {
            return new TokenPortraitDto();
        }
        JSONObject result = null;
        try {
            result = JSONObject.parseObject(cdpResult);
            logger.info("获取CDP认证token，result：{}",result);
            if (!ObjectUtils.isEmpty(result)){
                Integer code = result.getInteger("code");
                if (code == 0) {
                    JSONObject data = result.getJSONObject("data");
                    TokenPortraitDto tokenPortraitDto = new TokenPortraitDto();
                    tokenPortraitDto.setToken(data.getString("token"));
                    tokenPortraitDto.setTimestamp(timestamp);
                    long endTime = System.currentTimeMillis();
                    logger.info("获取token成功，接口耗时：{}",endTime - startTime);
                    return tokenPortraitDto;
                }else {
                    logger.info("获取CDP认证token-异常，code：{}",code);
                }
            }
        } catch (Exception e) {
            logger.error("获取CDP认证token-失败，error:",e);
        }
        return new TokenPortraitDto();
    }

    private String postForObject(String url, String param) {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        HttpEntity<String> formEntity = new HttpEntity<>(param, headers);
        return cdpRestTemplate.postForObject(url, formEntity, String.class);
    }


    @Override
    public void batchInsertHighlightFlagClueId(List<Long> clueId) {
        cdpTagTaskMapper.batchInsertHighlightFlagClueId(clueId);
    }

    @Override
    public Integer queryFaultLightClueTotal() {
        return cdpTagTaskMapper.queryFaultLightClueTotal();
    }

    @Override
    public List<CdpTagTaskPo> queryFaultLightClueId(Page page) {
        return cdpTagTaskMapper.queryFaultLightClueId(page);
    }

    @Override
    public void updateFaultLightClueById(List<Long> cdpTagTaskId) {
        cdpTagTaskMapper.updateFaultLightClueById(cdpTagTaskId);
    }

    @Override
    public void updateErrorTask(List<CdpTagTaskPo> cdpTagTaskPos) {
        cdpTagTaskMapper.updateErrorTask(cdpTagTaskPos);
    }
}
