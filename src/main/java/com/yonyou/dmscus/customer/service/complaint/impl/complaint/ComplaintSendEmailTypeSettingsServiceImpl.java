package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

    
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintSendEmailTypeSettingsPO;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintSendEmailTypeSettingsMapper;
import com.yonyou.dmscus.customer.service.complaint.ComplaintSendEmailTypeSettingsService;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailTypeSettingsDTO;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;




                                            /**
 * <p>
 * 客户投诉发送邮件类型设定 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
@Service
public class ComplaintSendEmailTypeSettingsServiceImpl implements ComplaintSendEmailTypeSettingsService {
        //日志对象
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        ComplaintSendEmailTypeSettingsMapper complaintSendEmailTypeSettingsMapper;

        /**
         * 分页查询对应数据
         *
         * @param page 分页对象
         * @param complaintSendEmailTypeSettingsDTO 查询条件
         * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailTypeSettingsDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public IPage<ComplaintSendEmailTypeSettingsDTO>selectPageBysql(Page page,ComplaintSendEmailTypeSettingsDTO complaintSendEmailTypeSettingsDTO){
            if(complaintSendEmailTypeSettingsDTO ==null){
                complaintSendEmailTypeSettingsDTO =new ComplaintSendEmailTypeSettingsDTO();
            }
            ComplaintSendEmailTypeSettingsPO complaintSendEmailTypeSettingsPo =complaintSendEmailTypeSettingsDTO.transDtoToPo(ComplaintSendEmailTypeSettingsPO.class);

            List<ComplaintSendEmailTypeSettingsPO>list= complaintSendEmailTypeSettingsMapper.selectPageBySql(page,complaintSendEmailTypeSettingsPo);
            if(CommonUtils.isNullOrEmpty(list)){
                page.setRecords(new ArrayList<>());
                return page;
            }else{
                List<ComplaintSendEmailTypeSettingsDTO>result=list.stream().map(m->m.transPoToDto(ComplaintSendEmailTypeSettingsDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param complaintSendEmailTypeSettingsDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailTypeSettingsDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<ComplaintSendEmailTypeSettingsDTO>selectListBySql(ComplaintSendEmailTypeSettingsDTO complaintSendEmailTypeSettingsDTO){
            if(complaintSendEmailTypeSettingsDTO ==null){
                complaintSendEmailTypeSettingsDTO =new ComplaintSendEmailTypeSettingsDTO();
            }
            ComplaintSendEmailTypeSettingsPO complaintSendEmailTypeSettingsPo =complaintSendEmailTypeSettingsDTO.transDtoToPo(ComplaintSendEmailTypeSettingsPO.class);
            List<ComplaintSendEmailTypeSettingsPO>list= complaintSendEmailTypeSettingsMapper.selectListBySql(complaintSendEmailTypeSettingsPo);
            if(CommonUtils.isNullOrEmpty(list)){
                return new ArrayList<>();
            }else{
                return list.stream().map(m->m.transPoToDto(ComplaintSendEmailTypeSettingsDTO.class)).collect(Collectors.toList());
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailTypeSettingsDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public ComplaintSendEmailTypeSettingsDTO getById(Long id){
            ComplaintSendEmailTypeSettingsPO complaintSendEmailTypeSettingsPo = complaintSendEmailTypeSettingsMapper.selectById(id);
            if(complaintSendEmailTypeSettingsPo!=null){
                return complaintSendEmailTypeSettingsPo.transPoToDto(ComplaintSendEmailTypeSettingsDTO.class);
            }else{
                throw new DALException("查询失败,没有符合条件的数据");
            }
        }

        /**
         * 根据DTO 进行数据新增
         *
         * @param complaintSendEmailTypeSettingsDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(ComplaintSendEmailTypeSettingsDTO complaintSendEmailTypeSettingsDTO){
            //对对象进行赋值操作
            ComplaintSendEmailTypeSettingsPO complaintSendEmailTypeSettingsPo = complaintSendEmailTypeSettingsDTO.transDtoToPo(ComplaintSendEmailTypeSettingsPO.class);
            //执行插入
            int row= complaintSendEmailTypeSettingsMapper.insert(complaintSendEmailTypeSettingsPo);
            //返回插入的值
            return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param complaintSendEmailTypeSettingsDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, ComplaintSendEmailTypeSettingsDTO complaintSendEmailTypeSettingsDTO){
            ComplaintSendEmailTypeSettingsPO complaintSendEmailTypeSettingsPo = complaintSendEmailTypeSettingsMapper.selectById(id);
            //对对象进行赋值操作
            complaintSendEmailTypeSettingsDTO.transDtoToPo(complaintSendEmailTypeSettingsPo);
            //执行更新
            int row= complaintSendEmailTypeSettingsMapper.updateById(complaintSendEmailTypeSettingsPo);
            return row;
        }

        /**
         * 根据ID 进行删除数据
         *
         * @param id
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteById(Long id){
            int deleteCount= complaintSendEmailTypeSettingsMapper.deleteById(id);
            logger.debug("删除数量：{}",deleteCount);
            if(deleteCount<=0){
                throw new DALException("删除失败,没有符合条件的数据");
            }else{
                return deleteCount;
            }
        }

}
