package com.yonyou.dmscus.customer.service.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.common.AjaxResponse;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.OwnerVehicleVO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillFirstPageDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillNoticeInvoiceInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 亲善预申请单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
public interface GoodwillApplyInfoService {
	public IPage<GoodwillApplyInfoDTO> selectPageBysql(Page page, GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public IPage<GoodwillApplyInfoDTO> getOemTodoByPage(Page page, GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public IPage<GoodwillApplyInfoDTO> getOemTodoListByPage(Page page, GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public IPage<GoodwillApplyInfoDTO> getOemSearchByPage(Page page, GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public IPage<GoodwillApplyInfoDTO> querySupportApplyDealerSearchInfo(Page page,
			GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public List<GoodwillApplyInfoDTO> selectListBySql(GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public GoodwillApplyInfoDTO getById(Long id);

	public int insert(GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public int update(Long id, GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public int deleteById(Long id);

	public int deleteBatchIds(String ids);

	public IPage<List> queryComplaintInfo(Page page, Map map);

	public IPage<OwnerVehicleVO> queryVehicleInfo(Page page, Map map);

	public IPage<List> queryPartDetailInfo(Page page, Map map);

	public IPage<List> queryPartInfo(Page page, Map map);

	public IPage<List> queryRepairOrderInfo(Page page, Map map);

	// 提交预申请单
	public int commitSupportApplyInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	String getBillNo(String ts, String dearCold);

	public Map queryApplyByDealerCode(String dealerCode, Integer year);

	// 亲善厂端 待办事项查询
	public IPage<GoodwillApplyInfoDTO> selectOemTodoPageBysql(Page page, GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	// 审核通过
	public int passApplyGoodwillInfo(Long id, GoodwillAuditInfoDTO goodwillAuditInfoDTO);

	// 审核驳回
	public int passFailApplyGoodwillInfo(Long id, GoodwillAuditInfoDTO goodwillAuditInfoDTO);

	List<Map> queryReturnTo(Long id, Integer goodwillStatus);

	List<GoodwillApplyInfoPO> queryApplyHistory(String vin, String applyNo);

	// 已审核亲善单
	public IPage<GoodwillApplyInfoDTO> selectCheckedGoodWill(Page page, GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	// 已审核亲善单
	public List<GoodwillApplyInfoDTO> exportCheckedApplyInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	// 经销商待办事项
	public IPage<GoodwillApplyInfoDTO> getDealerTodo(Page page, GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	// 提交预申请单
	public int commitDealerSupportApplyInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	/**
	 * 保存亲善预申请单信息
	 *
	 * @param goodwillApplyInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-06
	 */
	public int editSupportApplyInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	/**
	 * 提交亲善预申请单信息
	 *
	 * @param goodwillApplyInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-23
	 */
	public int commitCcmqSupportApplyInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public int editNoticeInvoiceInfo(GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto);

	public GoodwillNoticeInvoiceInfoDTO queryPaymentInfoByGoodwillApplyId(Long goodwillApplyId);

	public int saveInvoice(GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto);

	public int commitInvoice(GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto);

	public List<Map> queryInvoiceInfo(Long goodwillApplyId);

	public List<Map> queryOemInvoiceInfo(Long goodwillApplyId);

	public int saveInvoiceConfirm(GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto);

	public int refuseApplyGoodwillInfo(Long id, GoodwillAuditInfoDTO goodwillAuditInfoDTO);

	public int restartApplyGoodwillInfo(Long id, GoodwillAuditInfoDTO goodwillAuditInfoDTO);

	public IPage<GoodwillApplyInfoDTO> querySupportApplyAuditInfo(Page page, GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public int noNeedApplyGoodwillInfo(Long id, GoodwillAuditInfoDTO goodwillAuditInfoDTO);

	public IPage<List> queryNoticeInvoiceInfo(Page page, Long goodwillApplyId);

	public Map queryPrintInfo(Long noticeInvoiceId);

	public List<GoodwillApplyInfoDTO> exportSupportApplyOemSearchInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public List<GoodwillApplyInfoDTO> exportSupportApplyDealerSearchInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public void sendEmail(GoodwillApplyInfoPO goodwillApplyInfoPo, int sendToFlag, String auditRole, Integer mailType);

	public void sendEmailAsync(GoodwillApplyInfoPO goodwillApplyInfoPo, int sendToFlag, String auditRole, Integer mailType);

	public List<GoodwillApplyInfoDTO> exportSupportApplyAuditInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public Map getGoodwillFirstPage(GoodwillFirstPageDTO goodwillFirstPageDTO);

	/**
	 * 主要用于迁移数据产生亲善审批流程
	 * 根据申请单编号列表产生亲善审核流程
	 * add by czm 20210721
	 * @param applyNoList  亲善预申请单号列表
	 * @return 是否执行完毕
	 */
	AjaxResponse dealGoodwillFlowData(List<String> applyNoList);
}
