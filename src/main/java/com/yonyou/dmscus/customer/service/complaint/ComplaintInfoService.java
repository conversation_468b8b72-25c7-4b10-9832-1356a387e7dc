package com.yonyou.dmscus.customer.service.complaint;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.yonyou.dmscus.customer.entity.dto.complaint.*;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfMorePO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoPO;


import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 客户投诉信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface ComplaintInfoService  {
    /**
     * 分页查询
     * @param page
     * @param complaintInfoDTO
     * @return
     */
    IPage<ComplaintInfoDTO> selectPageBysql(Page page, ComplaintInfoDTO complaintInfoDTO);

    /**
     * 集合查询
     * @param complaintInfoDTO
     * @return
     */
    List<ComplaintInfoDTO> selectListBySql(ComplaintInfoDTO complaintInfoDTO);

    /**
     * 通过id查询
     * @param id
     * @return
     */
    ComplaintInfoDTO getById(Long id);

    /**
     * 新增
     * @param complaintInfoDTO
     * @return
     */
    int insert(ComplaintInfoDTO complaintInfoDTO);

    /**
     * 更新
     * @param id
     * @param complaintInfoDTO
     * @return
     */
    int update(Long id, ComplaintInfoDTO complaintInfoDTO);

    /**
     * 通过id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 获取客诉单号
     * @param ts
     * @param dearCold
     * @return
     */
    String getBillNo(String ts, String dearCold);

    /**
     * 新增
     * @param complaintmoreDTO
     * @return
     */
    int insertComplaint(ComplaintmoreDTO complaintmoreDTO);

    /**
     * 新增结案
     * @param complaintCustomFieldTestDTO
     * @param flag
     * @return
     */
    int insertClose(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO, String flag);

    /**
     * 分页查询
     * @param page
     * @param complaintInfMoreDTO
     * @return
     * @throws ParseException
     */
    IPage<ComplaintInfMoreDTO> selectCusByDeal(Page page, ComplaintInfMoreDTO complaintInfMoreDTO,String user) throws ParseException;

    /**
     * 导出查询
     * @param complaintInfMoreDTO
     * @return
     * @throws ParseException
     */
    List<Map> selectCusByDealAll(ComplaintInfMoreDTO complaintInfMoreDTO, String user) throws ParseException;

    /**
     * 区域经理结案
     * @param complaintCustomFieldTestDTO
     * @param agree,revisit
     * @return
     */
    int insertRegionClose(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO, String agree,String revisit);

    /**
     * 通过vin号查询亲善历史
     * @param page
     * @return
     */
    IPage<GoodwillApplyInfoDTO>selectGoodWill(Page page,GoodwillApplyInfoDTO goodwillApplyInfoDTO);

    void IssuedUpdataData(ComplaintFollowDTO complaintFollowDTO, String CreaterOrg,  String JobOrderStatus);

    ComplaintInfMorePO selectCusDetailById(ComplaintInfMoreDTO complaintInfMoreDTO);

    void getHoilday();

//    void IssuedCloseData()

    String complaintInfoSync(ComplaintInfoDTO complaintInfoDTO);
}
