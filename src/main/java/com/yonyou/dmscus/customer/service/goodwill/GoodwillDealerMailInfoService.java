package com.yonyou.dmscus.customer.service.goodwill;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillDealerMailInfoDTO;


                                                        /**
 * <p>
 * 亲善邮箱维护-经销商信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
public interface GoodwillDealerMailInfoService  {
	public IPage<GoodwillDealerMailInfoDTO>selectPageBysql(Page page,GoodwillDealerMailInfoDTO goodwillDealerMailInfoDTO);
	public IPage<List>selectVcdcPageBysql(Page page,String role);

	public List<GoodwillDealerMailInfoDTO>selectListBySql(GoodwillDealerMailInfoDTO goodwillDealerMailInfoDTO);
	public GoodwillDealerMailInfoDTO getById(Long id);
	public int insert(GoodwillDealerMailInfoDTO goodwillDealerMailInfoDTO);
	public int update(Long id, GoodwillDealerMailInfoDTO goodwillDealerMailInfoDTO);
	public int deleteById(Long id);
	public int deleteBatchIds(String ids);
	//查询集团下拉框
	public List<Map> blocSelect();
	//查询区域-区域经理下拉框
	public List<Map> areaManageSelect();
   
}
