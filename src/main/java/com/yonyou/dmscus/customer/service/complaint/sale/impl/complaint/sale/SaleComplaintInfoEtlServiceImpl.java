package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintInfoEtlMapper;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoEtlPO;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintInfoEtlService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoEtlDTO;

import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 销售客户投诉信息表-etl 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-22
 */
@Service
public class SaleComplaintInfoEtlServiceImpl implements SaleComplaintInfoEtlService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    SaleComplaintInfoEtlMapper saleComplaintInfoEtlMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                    分页对象
     * @param saleComplaintInfoEtlDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoEtlDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<SaleComplaintInfoEtlDTO> selectPageBysql(Page page, SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO) {
        if (saleComplaintInfoEtlDTO == null) {
            saleComplaintInfoEtlDTO = new SaleComplaintInfoEtlDTO();
        }
        SaleComplaintInfoEtlPO saleComplaintInfoEtlPO = saleComplaintInfoEtlDTO.transDtoToPo(SaleComplaintInfoEtlPO.class);

        List<SaleComplaintInfoEtlPO> list = saleComplaintInfoEtlMapper.selectPageBySql(page, saleComplaintInfoEtlPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<SaleComplaintInfoEtlDTO> result = list.stream().map(m -> m.transPoToDto(SaleComplaintInfoEtlDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param saleComplaintInfoEtlDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoEtlDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<SaleComplaintInfoEtlDTO> selectListBySql(SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO) {
        if (saleComplaintInfoEtlDTO == null) {
            saleComplaintInfoEtlDTO = new SaleComplaintInfoEtlDTO();
        }
        SaleComplaintInfoEtlPO saleComplaintInfoEtlPO = saleComplaintInfoEtlDTO.transDtoToPo(SaleComplaintInfoEtlPO.class);
        List<SaleComplaintInfoEtlPO> list = saleComplaintInfoEtlMapper.selectListBySql(saleComplaintInfoEtlPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(SaleComplaintInfoEtlDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoEtlDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public SaleComplaintInfoEtlDTO getById(Long id) {
        SaleComplaintInfoEtlPO saleComplaintInfoEtlPO = saleComplaintInfoEtlMapper.selectById(id);
        if (saleComplaintInfoEtlPO != null) {
            return saleComplaintInfoEtlPO.transPoToDto(SaleComplaintInfoEtlDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据" );
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param saleComplaintInfoEtlDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO) {
        //对对象进行赋值操作
        SaleComplaintInfoEtlPO saleComplaintInfoEtlPO = saleComplaintInfoEtlDTO.transDtoToPo(SaleComplaintInfoEtlPO.class);
        //执行插入
        int row = saleComplaintInfoEtlMapper.insert(saleComplaintInfoEtlPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                      主键ID
     * @param saleComplaintInfoEtlDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO) {
        SaleComplaintInfoEtlPO saleComplaintInfoEtlPO = saleComplaintInfoEtlMapper.selectById(id);
        //对对象进行赋值操作
        saleComplaintInfoEtlDTO.transDtoToPo(saleComplaintInfoEtlPO);
        //执行更新
        int row = saleComplaintInfoEtlMapper.updateById(saleComplaintInfoEtlPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = saleComplaintInfoEtlMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据" );
        } else {
            return deleteCount;
        }
    }

    @Override
    public List<Map> exportSaleComplaintHistory(SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO) {
        List<Map> list=saleComplaintInfoEtlMapper.exportSaleComplaintHistory(saleComplaintInfoEtlDTO);
        return list;
    }

}
