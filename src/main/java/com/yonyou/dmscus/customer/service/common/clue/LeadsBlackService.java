package com.yonyou.dmscus.customer.service.common.clue;

import com.yonyou.dmscus.customer.entity.po.common.LeadsBlackPO;
import com.yonyou.dmscus.customer.entity.po.common.LeadsBlackRecordPO;

public interface LeadsBlackService {
    /**添加*/
    void addLeadsBlack(LeadsBlackPO po);
    /**判断线索是否黑名单*/
    Long selectBlackByVinAndType(String vin, String leadsType);
    /**新增黑名单拦截记录*/
    int addLeadsBlackRecord(LeadsBlackRecordPO po);
}