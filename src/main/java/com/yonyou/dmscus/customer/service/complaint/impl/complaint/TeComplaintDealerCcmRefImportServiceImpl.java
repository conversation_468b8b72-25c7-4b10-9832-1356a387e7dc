package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

    
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.po.complaint.TeComplaintDealerCcmRefImportPO;
import com.yonyou.dmscus.customer.dao.complaint.TeComplaintDealerCcmRefImportMapper;
import com.yonyou.dmscus.customer.service.complaint.TeComplaintDealerCcmRefImportService;

import com.yonyou.dmscus.customer.entity.dto.complaint.TeComplaintDealerCcmRefImportDTO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;




                                                                    /**
 * <p>
 * 客户投诉进销商和CCM复杂人对应关系导入表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Service
public class TeComplaintDealerCcmRefImportServiceImpl implements TeComplaintDealerCcmRefImportService {
           /**
            * 日志对象
          */
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        TeComplaintDealerCcmRefImportMapper teComplaintDealerCcmRefImportMapper;

        /**
         * 分页查询对应数据
         *
         * @param page 分页对象
         * @param teComplaintDealerCcmRefImportDTO 查询条件
         * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.TeComplaintDealerCcmRefImportDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public IPage<TeComplaintDealerCcmRefImportDTO>selectPageBysql(Page page,TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO){
            if(teComplaintDealerCcmRefImportDTO ==null){
                teComplaintDealerCcmRefImportDTO =new TeComplaintDealerCcmRefImportDTO();
            }
            TeComplaintDealerCcmRefImportPO teComplaintDealerCcmRefImportPo =teComplaintDealerCcmRefImportDTO.transDtoToPo(TeComplaintDealerCcmRefImportPO.class);

            List<TeComplaintDealerCcmRefImportPO>list= teComplaintDealerCcmRefImportMapper.selectPageBySql(page,teComplaintDealerCcmRefImportPo);
            if(CommonUtils.isNullOrEmpty(list)){
                page.setRecords(new ArrayList<>());
                return page;
            }else{
                List<TeComplaintDealerCcmRefImportDTO>result=list.stream().map(m->m.transPoToDto(TeComplaintDealerCcmRefImportDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param teComplaintDealerCcmRefImportDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.TeComplaintDealerCcmRefImportDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<TeComplaintDealerCcmRefImportDTO>selectListBySql(TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO){
            if(teComplaintDealerCcmRefImportDTO ==null){
                teComplaintDealerCcmRefImportDTO =new TeComplaintDealerCcmRefImportDTO();
            }
            TeComplaintDealerCcmRefImportPO teComplaintDealerCcmRefImportPo =teComplaintDealerCcmRefImportDTO.transDtoToPo(TeComplaintDealerCcmRefImportPO.class);
            List<TeComplaintDealerCcmRefImportPO>list= teComplaintDealerCcmRefImportMapper.selectListBySql(teComplaintDealerCcmRefImportPo);
            if(CommonUtils.isNullOrEmpty(list)){
                return new ArrayList<>();
            }else{
                return list.stream().map(m->m.transPoToDto(TeComplaintDealerCcmRefImportDTO.class)).collect(Collectors.toList());
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.customer.entity.dto.complaint.TeComplaintDealerCcmRefImportDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public TeComplaintDealerCcmRefImportDTO getById(Long id){
            TeComplaintDealerCcmRefImportPO teComplaintDealerCcmRefImportPo = teComplaintDealerCcmRefImportMapper.selectById(id);
            if(teComplaintDealerCcmRefImportPo!=null){
                return teComplaintDealerCcmRefImportPo.transPoToDto(TeComplaintDealerCcmRefImportDTO.class);
            }else{
                throw new DALException("查询失败,没有符合条件的数据");
            }
        }

        /**
         * 根据DTO 进行数据新增
         *
         * @param teComplaintDealerCcmRefImportDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO){
            //对对象进行赋值操作
            TeComplaintDealerCcmRefImportPO teComplaintDealerCcmRefImportPo = teComplaintDealerCcmRefImportDTO.transDtoToPo(TeComplaintDealerCcmRefImportPO.class);
            //执行插入
            int row= teComplaintDealerCcmRefImportMapper.insert(teComplaintDealerCcmRefImportPo);
            //返回插入的值
            return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param teComplaintDealerCcmRefImportDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO){
            TeComplaintDealerCcmRefImportPO teComplaintDealerCcmRefImportPo = teComplaintDealerCcmRefImportMapper.selectById(id);
            //对对象进行赋值操作
            teComplaintDealerCcmRefImportDTO.transDtoToPo(teComplaintDealerCcmRefImportPo);
            //执行更新
            int row= teComplaintDealerCcmRefImportMapper.updateById(teComplaintDealerCcmRefImportPo);
            return row;
        }

        /**
         * 根据ID 进行删除数据
         *
         * @param id
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteById(Long id){
            int deleteCount= teComplaintDealerCcmRefImportMapper.deleteById(id);
            logger.debug("删除数量：{}",deleteCount);
            if(deleteCount<=0){
                throw new DALException("删除失败,没有符合条件的数据");
            }else{
                return deleteCount;
            }
        }


  }
