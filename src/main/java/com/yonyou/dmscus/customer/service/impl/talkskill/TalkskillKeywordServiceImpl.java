package com.yonyou.dmscus.customer.service.impl.talkskill;

import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillKeywordPO;
import com.yonyou.dmscus.customer.dao.talkskill.TalkskillKeywordMapper;
import com.yonyou.dmscus.customer.service.talkskill.TalkskillKeywordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillKeywordDTO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import javax.annotation.Resource;




/**
 * <p>
 * 话术关键词与话术关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-27
 */
@Service
public class TalkskillKeywordServiceImpl extends ServiceImpl<TalkskillKeywordMapper, TalkskillKeywordPO>implements TalkskillKeywordService {
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        TalkskillKeywordMapper talkskillKeywordMapper;

        /**
         * 根据查询条件返回结果集
         *
         * @param talkskillKeywordDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.TalkskillKeywordDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<TalkskillKeywordPO> selectListBySql(TalkskillKeywordDTO talkskillKeywordDTO){
            if(talkskillKeywordDTO ==null){
                talkskillKeywordDTO =new TalkskillKeywordDTO();
            }
            TalkskillKeywordPO talkskillKeywordPo =talkskillKeywordDTO.transDtoToPo(TalkskillKeywordPO.class);
            List<TalkskillKeywordPO> list= talkskillKeywordMapper.selectListBySql(talkskillKeywordPo);
            if(CommonUtils.isNullOrEmpty(list)){
                return null;
            }else{
                return list;
            }
        }



        /**
         * 根据DTO 进行数据新增
         *
         * @param talkskillKeywordDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(TalkskillKeywordDTO talkskillKeywordDTO){
            //对对象进行赋值操作
            TalkskillKeywordPO talkskillKeywordPo = talkskillKeywordDTO.transDtoToPo(TalkskillKeywordPO.class);
            //执行插入
            int row= talkskillKeywordMapper.insert(talkskillKeywordPo);
            //返回插入的值
            return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param talkskillKeywordDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, TalkskillKeywordDTO talkskillKeywordDTO){
            TalkskillKeywordPO talkskillKeywordPo = talkskillKeywordMapper.selectById(id);
                //对对象进行赋值操作
            talkskillKeywordDTO.transDtoToPo(talkskillKeywordPo);
            //执行更新
            int row= talkskillKeywordMapper.updateById(talkskillKeywordPo);
            return row;
        }

}
