package com.yonyou.dmscus.customer.service.inviteInsurance;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceRuleDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceRuleVcdcParamsVo;

import java.util.List;

/**
 * <p>
 * 邀约续保规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
public interface InviteInsuranceRuleService {

    List<InviteInsuranceRuleDTO> getInviteInsuranceRuleDlr(InviteInsuranceRuleDTO inviteInsuranceRuleDTO);

    int saveInviteInsuranceRuleDlr(InviteInsuranceRuleDTO dto);

    IPage<InviteInsuranceRuleDTO> getInviteInsuranceRuleVcdc(InviteInsuranceRuleVcdcParamsVo vo);

    int saveInviteInsuranceRule(InviteInsuranceRuleDTO dto);

    List<InviteInsuranceRuleDTO> selectListBySql(InviteInsuranceRuleDTO inviteInsuranceRuleDTO);


}
