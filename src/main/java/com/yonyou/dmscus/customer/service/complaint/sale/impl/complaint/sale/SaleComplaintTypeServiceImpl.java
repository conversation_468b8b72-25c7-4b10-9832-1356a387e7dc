package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintTypePO;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintTypeMapper;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintTypeService;

import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintTypeDTO;

import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 销售投诉类型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-10
 */
@Service
public class SaleComplaintTypeServiceImpl implements SaleComplaintTypeService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    SaleComplaintTypeMapper saleComplaintTypeMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                 分页对象
     * @param saleComplaintTypeDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintTypeDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<SaleComplaintTypeDTO> selectPageBysql(Page page, SaleComplaintTypeDTO saleComplaintTypeDTO) {
        if (saleComplaintTypeDTO == null) {
            saleComplaintTypeDTO = new SaleComplaintTypeDTO();
        }
        SaleComplaintTypePO saleComplaintTypePO = saleComplaintTypeDTO.transDtoToPo(SaleComplaintTypePO.class);

        List<SaleComplaintTypePO> list = saleComplaintTypeMapper.selectPageBySql(page, saleComplaintTypePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<SaleComplaintTypeDTO> result = list.stream().map(m -> m.transPoToDto(SaleComplaintTypeDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param saleComplaintTypeDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintTypeDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<SaleComplaintTypeDTO> selectListBySql(SaleComplaintTypeDTO saleComplaintTypeDTO) {
        if (saleComplaintTypeDTO == null) {
            saleComplaintTypeDTO = new SaleComplaintTypeDTO();
        }
        SaleComplaintTypePO saleComplaintTypePO = saleComplaintTypeDTO.transDtoToPo(SaleComplaintTypePO.class);
        List<SaleComplaintTypePO> list = saleComplaintTypeMapper.selectListBySql(saleComplaintTypePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(SaleComplaintTypeDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintTypeDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public SaleComplaintTypeDTO getById(Long id) {
        SaleComplaintTypePO saleComplaintTypePO = saleComplaintTypeMapper.selectById(id);
        if (saleComplaintTypePO != null) {
            return saleComplaintTypePO.transPoToDto(SaleComplaintTypeDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param saleComplaintTypeDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(SaleComplaintTypeDTO saleComplaintTypeDTO) {
        //对对象进行赋值操作
        SaleComplaintTypePO saleComplaintTypePO = saleComplaintTypeDTO.transDtoToPo(SaleComplaintTypePO.class);
        //执行插入
        int row = saleComplaintTypeMapper.insert(saleComplaintTypePO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                   主键ID
     * @param saleComplaintTypeDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, SaleComplaintTypeDTO saleComplaintTypeDTO) {
        SaleComplaintTypePO saleComplaintTypePO = saleComplaintTypeMapper.selectById(id);
        //对对象进行赋值操作
        saleComplaintTypeDTO.transDtoToPo(saleComplaintTypePO);
        //执行更新
        int row = saleComplaintTypeMapper.updateById(saleComplaintTypePO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = saleComplaintTypeMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    @Override
    public List<SaleComplaintTypeDTO> selectList(SaleComplaintTypeDTO saleComplaintTypeDTO) {
        if (saleComplaintTypeDTO == null) {
            saleComplaintTypeDTO = new SaleComplaintTypeDTO();
        }
        SaleComplaintTypePO saleComplaintTypePO = saleComplaintTypeDTO.transDtoToPo(SaleComplaintTypePO.class);
        List<SaleComplaintTypePO> list = saleComplaintTypeMapper.selectListFW(saleComplaintTypePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(SaleComplaintTypeDTO.class)).collect(Collectors.toList());
        }
    }
    @Override
    public List<SaleComplaintTypeDTO> selectListNotFw(SaleComplaintTypeDTO saleComplaintTypeDTO) {
        if (saleComplaintTypeDTO == null) {
            saleComplaintTypeDTO = new SaleComplaintTypeDTO();
        }
        SaleComplaintTypePO saleComplaintTypePO = saleComplaintTypeDTO.transDtoToPo(SaleComplaintTypePO.class);
        List<SaleComplaintTypePO> list = saleComplaintTypeMapper.selectListNotFw(saleComplaintTypePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(SaleComplaintTypeDTO.class)).collect(Collectors.toList());
        }
    }

}
