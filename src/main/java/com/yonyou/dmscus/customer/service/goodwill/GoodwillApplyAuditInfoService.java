package com.yonyou.dmscus.customer.service.goodwill;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditInfoDTO;

/**
 * <p>
 * 亲善审计表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-24
 */
public interface GoodwillApplyAuditInfoService {
	public IPage<GoodwillApplyAuditInfoDTO> selectPageBysql(Page page,
			GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO);

	public List<GoodwillApplyAuditInfoDTO> selectListBySql(GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO);

	public GoodwillApplyAuditInfoDTO getById(Long id);

	public int insert(GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO);

	public int update(Long id, GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO);

	public int deleteById(Long id);

	public int deleteBatchIds(String ids);

	public int auditApplyGoodwillInfo(GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDto);

	public List<GoodwillApplyAuditDetailDTO> queryAuditDetailInfo(Long auditId);

}
