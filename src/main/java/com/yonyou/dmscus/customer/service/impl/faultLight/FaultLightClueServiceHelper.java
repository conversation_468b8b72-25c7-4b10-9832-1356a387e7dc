package com.yonyou.dmscus.customer.service.impl.faultLight;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dto.EmailInfoDto;
import com.yonyou.dmscus.customer.dto.SmsPushDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.TmVehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.ClueNotifyDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.QueryRoleUserByCompanyCodeDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.UserInfoOutDTO;
import com.yonyou.dmscus.customer.service.CommonService;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.middleground.BasicdataCenterService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description 故障灯线索通知帮助类
 *
 * <AUTHOR>
 * @date 2023/7/19 19:26
 */
@Slf4j
@EnableAsync
@Component
public class FaultLightClueServiceHelper {
    private static final String UNKNOWN = "无";
    private static final DateTimeFormatter abbFormatter = DateTimeFormatter.ofPattern("yyMMdd");
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String GET = "get";
    private static final String COMMENTS = "comments";

    @Value("${volvo.email.publicFrom:<EMAIL>}")
    private String publicFrom;
    @Value("${fault.notify.subject:故障灯邀约线索提醒}")
    private String subject;
    @Value("${fault.notify.emailText:尊敬的经销商您好：<br>" +
            "&nbsp; &nbsp; 车牌：%s，VIN:%s，车辆于%s在%s发生了%s；于%s经400热线联络确认本店为意向经销商请尽快跟进，400热线备注为：%s。详细信息可登录NEWBIE查看。经销商代码：%s。<br>" +
            "&nbsp; &nbsp; 为确保故障灯提示的故障信息会被及时跟进，烦请及时联系上述联系人并关注后续状态<br>" +
            "&nbsp; &nbsp; 感谢<br>}")
    private String emailText;
    @Value("${fault.notify.role:YYZY,FWJL}")
    private String[] roleCodeArray;
    @Value("${fault.notify.templateCode:60656f6b-239e-4cd1-b075-b518e451d6f3}")
    private String templateCode;

    @Autowired
    private CommonService commonService;
    @Resource
    private BasicdataCenterService basicdataCenterService;
    @Autowired
    private BusinessPlatformService businessPlatformService;

    private static void convertDate(ClueNotifyDTO dto) {
        //线索生成时间
        String clueGenTime = getDateParam(dto.getClueGenTimeDate());
        log.info("convertDate,clueGenTime:{}", clueGenTime);
        dto.setClueGenTime(clueGenTime);
        //线索下发时间
        String clueDisTime = getDateParam(dto.getClueDisTimeDate());
        log.info("convertDate,clueDisTime:{}", clueDisTime);
        dto.setClueDisTime(clueDisTime);
    }

    private static String getDateParam(Date date) {
        log.info("getDateParam,date:{}", date);
        if (date == null) {
            return UNKNOWN;
        }
        try {
            Instant instant = date.toInstant();
            ZoneId zone = ZoneId.systemDefault();
            LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
            return formatter.format(localDateTime);
        } catch (Exception e) {
            log.info("getDateParam,Exception:{}", e);
            return UNKNOWN;
        }
    }

    private static String getSubjectTime() {
        LocalDate nowDate = LocalDate.now();
        return nowDate.format(abbFormatter);
    }

    private static void setObject(ClueNotifyDTO object) {
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            try {
                String name = field.getName();
                String str = name.substring(0, 1).toUpperCase();
                String get = GET + str + field.getName().substring(1);
                Method method = object.getClass().getMethod(get);
                if (!field.getType().equals(String.class)) {
                    continue;
                }
                Object ob = method.invoke(object);
                if (StringUtils.isNullOrEmpty(ob)) {
                    field.setAccessible(true);
                    field.set(object, UNKNOWN);
                } else if (COMMENTS.equals(name)) {
                    field.setAccessible(true);
                    field.set(object, StrUtil.maxLength(ob.toString(), CommonConstants.NOTIFY_LENGTH));
                }
            } catch (Exception e) {
                log.info("setObject:{}", e);
            }
        }
    }

    @Async
    public void doClueNotify(ClueNotifyDTO dto) {
        log.info("clueNotify,start");
        if (dto == null) {
            log.info("clueNotify,dto == null");
            return;
        }
        log.info("clueNotify,dto:{}", JSON.toJSONString(dto));
        /**查询数据*/
        String vin = dto.getVehicleVin();
        //查询发送信息车架号,线索生成时间,线索下发时间,400备注,经销商代码
        TmVehicleDTO vehicleDTO = businessPlatformService.getVehicleByVIN(vin);
        if (ObjectUtils.isNotEmpty(vehicleDTO)) {
            String plateNumber = vehicleDTO.getPlateNumber();
            log.info("clueNotify,plateNumber:{}", plateNumber);
            dto.setPlateNumber(plateNumber);
        }
        //查询收件人信息
        QueryRoleUserByCompanyCodeDTO codeDto = new QueryRoleUserByCompanyCodeDTO();
        //角色code:  邀约专员: YYZY   服务经理: FWJL
        codeDto.setRoleCode(this.getRoleCode());
        codeDto.setCompanyCode(dto.getDealerCode());
        //添加在职状态
        codeDto.setIsOnjob(CommonConstants.ON_THE_JOB);
        List<UserInfoOutDTO> listDto = basicdataCenterService.queryRoleUserByCompanyCode(codeDto);
        if (CollectionUtils.isEmpty(listDto)) {
            log.info("clueNotify,CollectionUtils.isEmpty(listDto)");
            return;
        }
        log.info("clueNotify,listDto:{}", listDto.size());
        //摘除离职人员
        List<UserInfoOutDTO> list = listDto.stream().filter(a -> Objects.nonNull(a) && !CommonConstants.RESIGNATION.equals(a.getIsOnjob())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(list)){
            log.info("clueNotify,CollectionUtils.isEmpty(list)");
            return;
        }
        log.info("clueNotify,list:{}", list.size());
        //转换时间
        convertDate(dto);
        //处理空值/限制外呼备注长度
        setObject(dto);
        //发送邮件
        List<String> emailList = list.stream().filter(a -> !StringUtils.isNullOrEmpty(a.getEmail()))
                .map(UserInfoOutDTO::getEmail).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(emailList)) {
            log.info("clueNotify,emailList:{}", emailList.size());
            try {
                clueEmailsNotify(dto, emailList);
            } catch (Exception e) {
                log.info("clueEmailsNotify,clueEmailsNotify,Exception:{}", e);
            }
        }
        //发送短信
        List<String> phoneList = list.stream().filter(a -> !StringUtils.isNullOrEmpty(a.getPhone()))
                .map(UserInfoOutDTO::getPhone).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(phoneList)) {
            log.info("clueNotify,phoneList:{}", phoneList.size());
            try {
                cluePhoneNotify(dto, phoneList);
            } catch (Exception e) {
                log.info("clueNotify,cluePhoneNotify,Exception:{}", e);
            }
        }
        log.info("clueNotify,end");
    }

    private void clueEmailsNotify(ClueNotifyDTO dto, List<String> emailList) {
        log.info("clueEmailsNotify,start");
        EmailInfoDto emailInfoDto = new EmailInfoDto();
        emailInfoDto.setFrom(publicFrom);
        emailInfoDto.setSubject(subject + getSubjectTime());
        emailInfoDto.setText(this.getEmailsText(dto));
        emailInfoDto.setTo(emailList.stream().toArray(String[]::new));
        commonService.sendMail(emailInfoDto);
        log.info("clueEmailsNotify,end");
    }

    private void cluePhoneNotify(ClueNotifyDTO dto, List<String> phoneList) {
        log.info("cluePhoneNotify,start");
        SmsPushDTO smsPushDTO = new SmsPushDTO();
        smsPushDTO.setMobiles(String.join(",", phoneList));
        smsPushDTO.setTemplateId(templateCode);
        smsPushDTO.setParamMap(dto);
        commonService.sendMessage(smsPushDTO);
        log.info("cluePhoneNotify,end");
    }

    private List getRoleCode() {
        ArrayList<String> list = new ArrayList<>(roleCodeArray.length);
        Collections.addAll(list, roleCodeArray);
        return list;
    }

    private String getEmailsText(ClueNotifyDTO dto) {
        log.info("clueNotify,getEmailsText:{}", JSON.toJSONString(dto));
        //赋值
        return String.format(emailText, dto.getPlateNumber(), dto.getVehicleVin(), dto.getFaultCityName(),
                dto.getClueGenTime(), dto.getWarningName(), dto.getClueDisTime(), dto.getComments(), dto.getDealerCode());
    }
}
