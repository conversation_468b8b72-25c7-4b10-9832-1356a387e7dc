package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyRepairPartInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairPartInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyRepairPartInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyRepairPartInfoService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 亲善预申请子表——维修零配件信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
@Service
public class GoodwillApplyRepairPartInfoServiceImpl
		extends ServiceImpl<GoodwillApplyRepairPartInfoMapper, GoodwillApplyRepairPartInfoPO>
		implements GoodwillApplyRepairPartInfoService {
	/** 日志对象 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillApplyRepairPartInfoMapper goodwillApplyRepairPartInfoMapper;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillApplyRepairPartInfoDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairPartInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillApplyRepairPartInfoDTO> selectPageBysql(Page page,
			GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO) {
		if (goodwillApplyRepairPartInfoDTO == null) {
			goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
		}
		GoodwillApplyRepairPartInfoPO goodwillApplyRepairPartInfoPo = goodwillApplyRepairPartInfoDTO
				.transDtoToPo(GoodwillApplyRepairPartInfoPO.class);

		List<GoodwillApplyRepairPartInfoPO> list = goodwillApplyRepairPartInfoMapper.selectPageBySql(page,
				goodwillApplyRepairPartInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillApplyRepairPartInfoDTO> result = list.stream()
					.map(m -> m.transPoToDto(GoodwillApplyRepairPartInfoDTO.class)).collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillApplyRepairPartInfoDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairPartInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillApplyRepairPartInfoDTO> selectListBySql(
			GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO) {
		if (goodwillApplyRepairPartInfoDTO == null) {
			goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
		}
		GoodwillApplyRepairPartInfoPO goodwillApplyRepairPartInfoPo = goodwillApplyRepairPartInfoDTO
				.transDtoToPo(GoodwillApplyRepairPartInfoPO.class);
		List<GoodwillApplyRepairPartInfoPO> list = goodwillApplyRepairPartInfoMapper
				.selectListBySql(goodwillApplyRepairPartInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillApplyRepairPartInfoDTO.class))
					.collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairPartInfoDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillApplyRepairPartInfoDTO getById(Long id) {
		GoodwillApplyRepairPartInfoPO goodwillApplyRepairPartInfoPo = goodwillApplyRepairPartInfoMapper.selectById(id);
		if (goodwillApplyRepairPartInfoPo != null) {
			return goodwillApplyRepairPartInfoPo.transPoToDto(GoodwillApplyRepairPartInfoDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillApplyRepairPartInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO) {
		// 对对象进行赋值操作
		GoodwillApplyRepairPartInfoPO goodwillApplyRepairPartInfoPo = goodwillApplyRepairPartInfoDTO
				.transDtoToPo(GoodwillApplyRepairPartInfoPO.class);
		// 执行插入
		int row = goodwillApplyRepairPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillApplyRepairPartInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO) {
		GoodwillApplyRepairPartInfoPO goodwillApplyRepairPartInfoPo = goodwillApplyRepairPartInfoMapper.selectById(id);
		// 对对象进行赋值操作
		goodwillApplyRepairPartInfoDTO.transDtoToPo(goodwillApplyRepairPartInfoPo);
		// 执行更新
		int row = goodwillApplyRepairPartInfoMapper.updateById(goodwillApplyRepairPartInfoPo);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillApplyRepairPartInfoMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillApplyRepairPartInfoMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据申请单id查询零配件信息
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2020/5/11
	 */
	@Override
	public List<GoodwillApplyRepairPartInfoDTO> getSupportApplyPartInfoById(Long goodwillApplyId) {
		List<GoodwillApplyRepairPartInfoDTO> list = goodwillApplyRepairPartInfoMapper
				.getSupportApplyPartInfoById(goodwillApplyId);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list;
		}
	}
}
