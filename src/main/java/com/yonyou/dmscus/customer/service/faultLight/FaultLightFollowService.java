package com.yonyou.dmscus.customer.service.faultLight;

import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightFollowRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightFollowSubDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightParamDTO;

import java.util.List;

public interface FaultLightFollowService {

    /**
     * 跟进页面参数查询
     */
    FaultLightFollowDTO queryFaultLightFollow(Long id);
    /**
     * 跟进状态下拉框
     */
    List<FaultLightParamDTO> queryFollowSpinner(Long id);
    /**
     * 跟进线索提交
     */
    void doFollowSub(FaultLightFollowSubDTO subDTO);
    /**
     * 线索跟进记录查询
     */
    List<FaultLightFollowRecordDTO> queryFollowRecord(FaultLightFollowRecordDTO dto);
}
