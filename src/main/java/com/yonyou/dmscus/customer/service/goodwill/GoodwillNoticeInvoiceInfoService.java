package com.yonyou.dmscus.customer.service.goodwill;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillNoticeInvoiceInfoDTO;

/**
 * <p>
 * 亲善管理通知开票信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-17
 */
public interface GoodwillNoticeInvoiceInfoService {
	public IPage<GoodwillNoticeInvoiceInfoDTO> selectPageBysql(Page page,
			GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO);

	public List<GoodwillNoticeInvoiceInfoDTO> selectListBySql(
			GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO);

	public GoodwillNoticeInvoiceInfoDTO getById(Long id);

	public int insert(GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO);

	public int update(Long id, GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO);

	public int deleteById(Long id);

	public int deleteBatchIds(String ids);

}
