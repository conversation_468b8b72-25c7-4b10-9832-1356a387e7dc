package com.yonyou.dmscus.customer.service.oss;


import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusRecordPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataRecordPo;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: OssService
 * @projectName dmscus.customer
 * @date 2022/11/116:56
 */
public interface OssService {
    void downLoadVocFunctionalStatus(String dateTime);

    void downLoadVocwarningdaily(String dateTime);

    void run(String dateTime);
    void run();

    int issuedTo(List<VocWarningDataRecordPo> list, String dateTime, Map<String, String> mapx);

    int issuedTox(List<VocFunctionalStatusRecordPO> list, Map<String, String> mapx, String dateTime);

    void getcontent(String dateTime);

    void vocFunctionalStatusDataClean(String dateTime);

    void vocwarningdailyDataClean(String dateTime);

    void vocdata(String dateTime);

    int issuedToinit(List<VocWarningDataRecordPo> list, String dateTime, Map<String, String> mapx);

    void checkVocAlert(String dateTime);
    void checkVocAlert();
}
