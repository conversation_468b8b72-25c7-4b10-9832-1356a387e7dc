package com.yonyou.dmscus.customer.service.accidentClues;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.dmscloud.framework.domains.dto.basedata.ResponseDTO;
import com.yonyou.dmscloud.framework.service.excel.ExcelExportColumn;
import com.yonyou.dmscus.customer.bean.dto.DownloadDTO;
import com.yonyou.dmscus.customer.configuration.InnerUrlProperties;
import com.yonyou.dmscus.customer.configuration.SalesUrlProperties;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesExportDTO;
import com.yonyou.dmscus.customer.util.common.VolvoHttpUtils;

import cn.hutool.core.date.DateUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 事故线索
 * <AUTHOR>
 * @date 2020/11/16 0016
 */
@Service
public class AccidentCluesOssServiceImpl implements AccidentCluesOssService {
    /**日志对象*/
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    VolvoHttpUtils volvoHttpUtils;

    @Resource
    private RestTemplate directRestTemplate;

    @Autowired
    private SalesUrlProperties salesUrlProperties;

    @Autowired
    private InnerUrlProperties innerUrlProperties;
    @Value("accidentClue.test.export:1")
    private String exportParam;

    @Override
    public void exportExcelAccidentExcel(AccidentCluesExportDTO dto) {
        logger.info("事故线索导出开始===================================》:{}", JSON.toJSONString(dto));
        try {
            String url=salesUrlProperties.getDownloadService()+salesUrlProperties.getExportExcelUrl();
            List<ExcelExportColumn> exportColumnList = getExcelColumn(dto);
            DownloadDTO downloadDTO = new DownloadDTO();
            downloadDTO.setQueryParams(dto.toMaps());
            downloadDTO.setExcelName(Objects.equals(exportParam,"1")?exportParam:"事故线索报表导出.xlsx");
            downloadDTO.setSheetName("事故线索");
            downloadDTO.setServiceUrl(innerUrlProperties.getDownloadAccidentReturn());
            downloadDTO.setExcelExportColumnList(exportColumnList);
            exportColumnList(url,downloadDTO.toMaps());
        }catch (Exception e){
            logger.info("事故线索导出：出现异常");
            e.printStackTrace();
        }
    }


    public ResponseDTO exportColumnList(String url, Map param){
        //RestTemplate restTemplate = new RestTemplate();
        ResponseDTO response = new ResponseDTO();
        HttpHeaders headers =volvoHttpUtils.getHeaders();
        try {
            HttpEntity<Map<String,Object>> httpEntity = new HttpEntity<Map<String,Object>>(param, headers);
            ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(url, HttpMethod.POST, httpEntity,
                    ResponseDTO.class);

            if(responseEntity.getBody()!=null) {
                ObjectMapper objectMapper = new ObjectMapper();
                //转换返回对象
                response = objectMapper.convertValue(responseEntity.getBody(), ResponseDTO.class);
            }else {
                throw new Exception("请求接口返回对象responseEntity为空");
            }
        } catch (Exception e) {
            logger.error("download-service接口请求地址:{},请求参数:{}，返回失败:{}",e);
            if (e != null) {
                response.setReturnCode("error");
                response.setReturnMessage(e.getMessage());
            }
        }
        return response;
    }

    //轮胎电瓶刹车片执行率明细导出
    public List<ExcelExportColumn> getExcelColumn(AccidentCluesExportDTO dto) {
        List<ExcelExportColumn> exportColumnList = new ArrayList<ExcelExportColumn>();
        if("vcdc".equals(dto.getSource())) {
            exportColumnList.add(new ExcelExportColumn("afterBigAreaName", "大区"));
            exportColumnList.add(new ExcelExportColumn("afterSmallAreaName", "小区"));
            exportColumnList.add(new ExcelExportColumn("provinceName", "省份"));
            exportColumnList.add(new ExcelExportColumn("cityName", "城市"));
            exportColumnList.add(new ExcelExportColumn("dealerCode", "经销商"));
            exportColumnList.add(new ExcelExportColumn("cluesResource", "线索来源"));
            exportColumnList.add(new ExcelExportColumn("insuranceSource", "来源渠道"));
        }else{
            exportColumnList.add(new ExcelExportColumn("cluesResource", "线索来源"));
            exportColumnList.add(new ExcelExportColumn("insuranceSource", "来源渠道"));
            exportColumnList.add(new ExcelExportColumn("channelType", "渠道标签"));
        }
        exportColumnList.add(new ExcelExportColumn("registNo", "报案号"));
        exportColumnList.add(new ExcelExportColumn("vin", "VIN"));
        exportColumnList.add(new ExcelExportColumn("license", "车牌号"));
        exportColumnList.add(new ExcelExportColumn("modelName", "车型"));
        exportColumnList.add(new ExcelExportColumn("ownerName", "车主姓名"));
        //exportColumnList.add(new ExcelExportColumn("ownerMobile", "车主手机"));
        exportColumnList.add(new ExcelExportColumn("contacts", "报案人"));
       // exportColumnList.add(new ExcelExportColumn("contactsPhone", "报案人手机号"));
        exportColumnList.add(new ExcelExportColumn("accidentDate", "出险时间"));
        exportColumnList.add(new ExcelExportColumn("accidentReason", "出险内容"));
        exportColumnList.add(new ExcelExportColumn("accidentAddress", "出险地点"));
        exportColumnList.add(new ExcelExportColumn("reportDate", "报案时间"));
        exportColumnList.add(new ExcelExportColumn("createdAt", "线索创建/推送时间"));
        exportColumnList.add(new ExcelExportColumn("firstFollowTime", "首次跟进时间"));
        exportColumnList.add(new ExcelExportColumn("lastFollowTime", "二次跟进时间"));
        exportColumnList.add(new ExcelExportColumn("nextFollowTime", "下次跟进时间"));
        exportColumnList.add(new ExcelExportColumn("followCount", "跟进次数"));
        exportColumnList.add(new ExcelExportColumn("followStatus", "跟进状态"));
        exportColumnList.add(new ExcelExportColumn("followText", "跟进内容"));
        exportColumnList.add(new ExcelExportColumn("followFailWhy", "跟进失败原因"));
        exportColumnList.add(new ExcelExportColumn("followPeopleName", "跟进人员"));
        exportColumnList.add(new ExcelExportColumn("insuranceCompanyName", "保险公司名称"));
        exportColumnList.add(new ExcelExportColumn("clientType", "送返修标识"));
        exportColumnList.add(new ExcelExportColumn("repeatLead", "是否重复线索"));
        exportColumnList.add(new ExcelExportColumn("bookingOrderNo", "预约单号"));
        exportColumnList.add(new ExcelExportColumn("bookingComeTime", "预约到店时间"));
        exportColumnList.add(new ExcelExportColumn("intoRoNo", "进厂工单号"));
        exportColumnList.add(new ExcelExportColumn("cluesStatus", "工单状态"));
        exportColumnList.add(new ExcelExportColumn("intoDealerDate", "进厂时间"));
        exportColumnList.add(new ExcelExportColumn("intoDealerCode", "进厂经销商"));
        exportColumnList.add(new ExcelExportColumn("repairSituation", "留修情况"));
        exportColumnList.add(new ExcelExportColumn("repairTypeCode", "维修类型"));
        exportColumnList.add(new ExcelExportColumn("repairAmount", "维修总金额"));
        exportColumnList.add(new ExcelExportColumn("repairPartAmount", "维修配件金额"));
        exportColumnList.add(new ExcelExportColumn("labourAmount", "维修工时金额"));
        return exportColumnList;
    }

}
