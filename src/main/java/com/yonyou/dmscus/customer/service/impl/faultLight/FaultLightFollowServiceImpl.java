package com.yonyou.dmscus.customer.service.impl.faultLight;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.constants.RedisEnum;
import com.yonyou.dmscus.customer.constants.faultLight.*;
import com.yonyou.dmscus.customer.dao.faultLight.*;
import com.yonyou.dmscus.customer.dto.BookingOrderInfoVO;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.dto.faultLight.FaultLightBookingRecordDto;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentClueContact;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.TmVehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightFollowRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightFollowSubDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightParamDTO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightCluePO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightFollowRecordPO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightInvitationPO;
import com.yonyou.dmscus.customer.feign.RepairCommonClient;
import com.yonyou.dmscus.customer.feign.vo.PrintDataVo;
import com.yonyou.dmscus.customer.feign.vo.PrintParamVo;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightFollowService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightInvitationService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightService;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import com.yonyou.dmscus.customer.utils.DateCalUtil;
import com.yonyou.dmscus.customer.utils.RedisLockUtil.DistributedLockUtil;
import com.yonyou.dmscus.customer.utils.RedisLockUtil.IDistributedLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;


@Slf4j
@Service
public class FaultLightFollowServiceImpl implements FaultLightFollowService {

    private static final String PAYMENT_OBJECT_CODE = "999999999999";
    @Resource
    private TtFaultLightFollowRecordMapper ttFaultLightFollowRecordMapper;
    @Resource
    private TtFaultLightInvitationMapper ttFaultLightInvitationMapper;
    @Resource
    private TtFaultLightClueMapper ttFaultLightClueMapper;
    @Autowired
    private RepairCommonClient repairCommonClient;
    @Autowired
    private BusinessPlatformService businessPlatformService;
    @Autowired
    private FaultLightService faultLightService;

    @Autowired
    private FaultLightInvitationService faultLightInvitationService;

    @Resource
    private TtFaultCallDetailsMapper ttFaultCallDetailsMapper;

    @Value("${faultLight.mostDay}")
    private  int mostDay;

    @Value("${faultLight.leastDay}")
    private  int leastDay;

    private  Integer maxLength = 500;
    /**
     * 跟进页面参数查询
     * @param id    Long
     * @return      FaultLightFollowDTO
     */
    @Override
    public FaultLightFollowDTO queryFaultLightFollow(Long id) {

        log.info("queryFaultLightFollow,start:{}", id);
        //查询线索信息
        FaultLightFollowDTO cluePo = ttFaultLightClueMapper.selectFaultLightFollow(id);
        if(cluePo == null){
            log.info("queryFaultLightFollow,cluePo == null");
            return null;
        }
        if (StringUtils.isNotEmpty(cluePo.getDailyMile())) {
            cluePo.setDisplayMile(cluePo.getDailyMile());
        } else {
            cluePo.setDisplayMile(cluePo.getDailyAverageMileage());
        }
        //限制外呼备注长度50
        String notifyComments = StrUtil.maxLength(cluePo.getComments(), CommonConstants.NOTIFY_LENGTH);
        cluePo.setComments(notifyComments);
        /**性别*/
        String gender = cluePo.getGender();
        gender = FaultGenderEnum.getNameByCode(gender);
        gender = gender == null ? FaultGenderEnum.GENDER_UNKNOWN.getName() : gender;
        cluePo.setGender(gender);
        //查询车辆信息
        TmVehicleDTO vehicleDTO = businessPlatformService.getVehicleByVIN(cluePo.getVin());
        if (ObjectUtils.isNotEmpty(vehicleDTO)) {
            cluePo.setPlateNumber(vehicleDTO.getPlateNumber());
            cluePo.setModel(vehicleDTO.getModelName());
        }
        log.info("queryFaultLightFollow,end:{}", JSON.toJSONString(cluePo));

        updateForecastTime(cluePo);

        return cluePo;
    }

    private void updateForecastTime(FaultLightFollowDTO cluePo) {
        String bookingOrderNo = cluePo.getBookingOrderNo();
        String dealerCode = cluePo.getDealerCode();
        if (StringUtils.isEmpty(bookingOrderNo) && StringUtils.isEmpty(dealerCode)){
            return;
        }
        List<BookingOrderInfoVO> bookingCreateParamsVo = repairCommonClient.queryBookingOrder(Collections.singletonList(new BookingOrderInfoVO(bookingOrderNo,dealerCode)));
        if ( ObjectUtils.isNotEmpty(bookingCreateParamsVo) && ObjectUtils.isNotEmpty(bookingCreateParamsVo.get(0))){
            BookingOrderInfoVO bookingOrderInfoVO = bookingCreateParamsVo.get(0);
            Date bookingComeTime = bookingOrderInfoVO.getBookingComeTime();
            cluePo.setForecastTime(bookingComeTime);
            List<FaultLightBookingRecordDto> faultLightBookingRecordDTOS = new ArrayList<>();
            faultLightBookingRecordDTOS.add(FaultLightBookingRecordDto.builder().icmId(cluePo.getIcmId()).forecastTime(bookingComeTime).build());
            ttFaultLightClueMapper.updateForecastTime(faultLightBookingRecordDTOS);
        }
    }

    /**
     * 跟进状态下拉框
     * @param id    Long
     * @return      FaultLightParamDTO
     */
    @Override
    public List<FaultLightParamDTO> queryFollowSpinner(Long id) {

        log.info("queryFollowSpinner,start:{}", id);
        if(ObjectUtils.isEmpty(id)){
            log.info("ObjectUtils.isEmpty(id)");
            throw new ServiceBizException("查询失败");
        }
        //查询当前状态
        TtFaultLightCluePO po = ttFaultLightClueMapper.selectById(id);
        if (ObjectUtils.isEmpty(po)) {
            log.info("ObjectUtils.isEmpty(po)");
            throw new ServiceBizException("查询失败");
        }
        List<FaultLightParamDTO> list = new ArrayList<>();
        //线索状态
        Integer clueStatus = po.getClueStatus();
        //跟进状态
        Integer followStatus = po.getFollowStatus();
        log.info("queryFollowSpinner, clueStatus : {}, followStatus : {}", clueStatus, followStatus);
        //赋值
        if (FaultFolOpeStateEnum.NO_ASSOCIATED_WORK_ORDER.getCode().equals(followStatus)) {
            list.add(new FaultLightParamDTO(FaultFolOpeStateEnum.NO_ASSOCIATED_WORK_ORDER.getCode(),
                    FaultFolOpeStateEnum.NO_ASSOCIATED_WORK_ORDER.getName()));
        }else if (FaultFollowStateEnum.APPOINTMENT_SUCCESS.getCode().equals(followStatus)) {
            list.add(new FaultLightParamDTO(FaultFolOpeStateEnum.APPOINTMENT_SUCCESS.getCode(),
                    FaultFolOpeStateEnum.APPOINTMENT_SUCCESS.getName()));
            list.add(new FaultLightParamDTO(FaultFolOpeStateEnum.ENTERED_STORE.getCode(),
                    FaultFolOpeStateEnum.ENTERED_STORE.getName()));
        }else{
            for (FaultFolOpeStateEnum value : FaultFolOpeStateEnum.values()) {
                if (clueStatus.equals(value.getParentCode()) && value.getState()!=0  && !Objects.equals(value.getCode(), FaultFolOpeStateEnum.APPOINTMENT_SUCCESS.getCode())) {
                    list.add(new FaultLightParamDTO(value.getCode(), value.getName()));
                }
            }
        }
        log.info("queryFollowSpinner,end");
        return list;
    }

    /**
     * 跟进线索提交
     * @param subDTO    FaultLightFollowSubDTO
     * @return          int
     * @throws Exception    Exception
     */
    @Override
    @Transactional
    public void doFollowSub(FaultLightFollowSubDTO subDTO){
        log.info("doFollowSub,start{}", JSON.toJSONString(subDTO));
        Long id = subDTO.getId();
        if (ObjectUtils.isEmpty(id)) {
            log.info("doFollowSub,ObjectUtils.isEmpty(id)");
            throw new ServiceBizException("请求失败");
        }
        //用ID做防重复点击
        IDistributedLock lock = DistributedLockUtil.getDistributedLock(
                RedisEnum.DO_FOLLOW_SUB.getKey(id), RedisEnum.CLUE_DATA_SYNCHRO.getTimeoutMsecs());
        if(!lock.antiDuplication()){
            log.info("doFollowSub,!lock.antiDuplication()");
            throw new ServiceBizException("请勿重复提交请求");
        }
        TtFaultLightCluePO cluePO;
        try {
            //查询线索
            TtFaultLightCluePO po = ttFaultLightClueMapper.selectById(id);
            if (ObjectUtils.isEmpty(po)) {
                log.info("doFollowSub,ObjectUtils.isEmpty(po)");
                throw new ServiceBizException("请求失败");
            }
            //线索状态
            Integer clueStatus = po.getClueStatus();
            if (FaultClueStateEnum.WAITING_FOR_APPOINTMENT.getCode().equals(clueStatus)) {
                //待预约
                cluePO = pendingAppointment(subDTO, po);
            } else if (FaultClueStateEnum.WAITING_ENTER_STORE.getCode().equals(clueStatus)) {
                //待进店
                cluePO = storeEntered(subDTO, po);
            } else if (FaultClueStateEnum.ENTERED_STORE.getCode().equals(clueStatus)) {
                //已进店
                cluePO = enteredStore(subDTO, po);
            } else {
                log.info("doFollowSub,跟进流程已完成clueStatus:{}", clueStatus);
                throw new ServiceBizException("跟进流程已完成");
            }
            //MQ状态同步
            faultLightService.pushMessage(cluePO.getIcmId(), String.valueOf(cluePO.getFollowStatus()), String.valueOf(cluePO.getClueStatus()), new Date());
        } catch (Exception e) {
            log.error("doFollowSub,Exception:{}", e);
            throw new ServiceBizException(e.getMessage());
        } finally {
            log.error("doFollowSub,release,end");
            lock.release();
        }
    }

    /**
     * 线索跟进记录查询
     * @param dto   FaultLightFollowRecordDTO
     * @return      FaultLightFollowRecordDTO
     */
    @Override
    public List<FaultLightFollowRecordDTO> queryFollowRecord(FaultLightFollowRecordDTO dto) {

        List<FaultLightFollowRecordDTO> faultLightFollowRecordDTOs = ttFaultLightFollowRecordMapper.queryFollowRecord(dto);

        faultLightFollowRecordDTOs.forEach(tf -> {
            tf.setClueDisTimeStr(DateUtils.dateFormat(tf.getClueDisTime(), DateUtils.DATE_FORMAT));
            tf.setFollowTimeStr(DateUtils.dateFormat(tf.getFollowTime(), DateUtils.DATE_FORMAT));

            tf.setFollowStatusStr(FaultFollowStateEnum.getNameByCode(tf.getFollowStatus()));
            tf.setClueStatusStr(FaultClueStateEnum.getNameByCode(tf.getClueStatus()));
        });

        return faultLightFollowRecordDTOs;
    }

    private TtFaultLightCluePO pendingAppointment(FaultLightFollowSubDTO subDTO, TtFaultLightCluePO po) throws Exception {
        log.info("doFollowSub,pendingAppointment,start");
        TtFaultLightCluePO uePO = new TtFaultLightCluePO();
        TtFaultLightInvitationPO onPO = new TtFaultLightInvitationPO();
        //查询故障灯预约记录
        TtFaultLightInvitationPO faultLightInvitationPO =faultLightInvitationService.queryFaultLightInvitationByClurId(po.getId());
        //线索ID
        long id = po.getId();
        //邀约时间
        Date inviteTime = po.getInviteTime();
        log.info("doFollowSub,inviteTime:{}", inviteTime);
        //线索状态
        Integer clueStatus = null;
        //操作状态
        Integer followStatus = subDTO.getFollowStatus();
        //预约时间
        Date forecastTime = subDTO.getForecastTime();
        //二次预约时间
        Date reInviteTime = subDTO.getReInviteTime();
        //邀约结果
        Integer inviteResult = null;
        //邀约人
        LoginInfoDto loginInfo = FrameworkUtil.getLoginInfo();
        String inviteName = loginInfo.getUserName();
        String saId = loginInfo.getUserId().toString();
        log.info("doFollowSub,inviteName:{}", inviteName);
        //当前时间
        Date data = new Date();
        //判断是否第一次跟进
        if (ObjectUtils.isEmpty(inviteTime)) {
            inviteTime = data;
            //邀约响应时间
            Long equationTime = DateCalUtil.calculateTime(po.getClueDisTime(), data);
            BigDecimal b = new BigDecimal((double)equationTime/60);
            Double hour = b.setScale(1,BigDecimal.ROUND_DOWN).doubleValue();
            hour = hour <= 0 ? 0.1 : hour;
            log.info("doFollowSub,hour:{}", hour);
            String inviteResTime = hour.toString();

            //邀约响应是否超时 0.无 1.没有超时 2.超时
            Integer inviteOvertime = equationTime >= CommonConstants.ONE_HUNDRED_TWENTY ? 2 : 1;
            //线索表
            uePO.setInviteTime(inviteTime);
            uePO.setInviteOvertime(inviteOvertime);
            //预约表
            onPO.setInviteTime(inviteTime);
            onPO.setInviteResTime(inviteResTime);
            onPO.setInviteOvertime(inviteOvertime);
        }
        /*校验操作状态*/
        //操作状态
        List<Integer> failedList = FaultFolOpeStateEnum.getFailed();
        if (failedList.contains(followStatus)) {
            /*预约失败*/
            inviteResult = 1;
            clueStatus = FaultClueStateEnum.CLOSE_CLUES.getCode();
            //线索关闭时间
            uePO.setClueCloTime(data);
            if (FaultFolOpeStateEnum.APPOINTMENT_FAILED_OTHER.getCode().equals(followStatus)) {
                if (StringUtils.isEmpty(subDTO.getFailureReason())){
                    log.info("reservationFailure,FailureReason:{}", subDTO.getFailureReason());
                    throw new ServiceBizException("请输入预约失败备注");
                }
                if (subDTO.getFailureReason().length()>maxLength){
                    log.info("reservationFailure,FailureReason:{}", subDTO.getFailureReason());
                    throw new ServiceBizException("预约失败备注过长");
                }
                onPO.setFailureReason(subDTO.getFailureReason());
            }
            uePO.setHighlightFlag(Integer.valueOf(StateEscapeEnum.STATE_ONE.getCode()));
        } else if (FaultFolOpeStateEnum.WAITING_ENTER_STORE.getCode().equals(followStatus)) {
            /*预约成功*/
            if (ObjectUtils.isEmpty(forecastTime)) {
                log.info("doFollowSub,forecastTime:{}", forecastTime);
                throw new ServiceBizException("请输入预约时间");
            } else if (checkForecastTime(inviteTime, forecastTime)) {
                log.info("doFollowSub,inviteTime:{},forecastTime:{}", inviteTime, forecastTime);
                throw new ServiceBizException("预约时间输入范围不正确");
            }
            inviteResult = 2;
            clueStatus = FaultClueStateEnum.WAITING_ENTER_STORE.getCode();
            followStatus = FaultFollowStateEnum.WAITING_ENTER_STORE.getCode();
            uePO.setHighlightFlag(Integer.valueOf(StateEscapeEnum.STATE_ONE.getCode()));
        }else if (FaultFolOpeStateEnum.SECOND_APPOINTMENT.getCode().equals(followStatus)){
            /*二次预约*/
            if (ObjectUtils.isEmpty(reInviteTime)){
                log.info("secondAppointment,reinviteTime:{}", reInviteTime);
                throw new ServiceBizException("请输入二次预约时间");
            }else if (checkForecastTime(inviteTime,reInviteTime)){
                log.info("secondAppointment,inviteTime:{},forecastTime:{}", inviteTime, reInviteTime);
                throw new ServiceBizException("二次预约时间输入范围不正确");
            }
            /*二次预约时间*/
            onPO.setReInviteTime(reInviteTime);
            //判断是否取消高亮
            if (!DateUtils.comDayto(reInviteTime)){
                uePO.setHighlightFlag(Integer.valueOf(StateEscapeEnum.STATE_ONE.getCode()));
            }else {
                //判断高亮
                uePO.setHighlightFlag(Integer.valueOf(StateEscapeEnum.STATE_TWO.getCode()));

            }

        }
        //修改线索 线索状态.跟进状态.邀约时间.预约进店时间
        uePO.setId(id);
        uePO.setIcmId(po.getIcmId());
        uePO.setClueStatus(clueStatus);
        uePO.setFollowStatus(followStatus);
        uePO.setForecastTime(forecastTime);
        uePO.setAfClueStatus(po.getClueStatus());

        int num = ttFaultLightClueMapper.updateFaultLightClueById(uePO);
        //预约记录表
        onPO.setClueId(id);
        onPO.setInviteResult(inviteResult);
        onPO.setForecastTime(forecastTime);
        onPO.setInviteName(inviteName);
        onPO.setSaId(saId);
        num += ttFaultLightInvitationMapper.updateByClueId(onPO);
        //跟进记录表
        followStatus = followStatus == null ? po.getFollowStatus() : followStatus;
        TtFaultLightFollowRecordPO rdPO = TtFaultLightFollowRecordPO.builder()
                .clueId(id)
                .faultId(po.getFaultId())
                .faultCityName(po.getFaultCityName())
                .clueDisTime(po.getClueDisTime())
                .followTime(data)
                .clueStatus(clueStatus)
                .followStatus(followStatus)
                .followName(inviteName)
                .forecastTime(forecastTime)
                .build();
        num += ttFaultLightFollowRecordMapper.insert(rdPO);
        ttFaultCallDetailsMapper.updateFaultLightDetailId(rdPO.getClueId(),rdPO.getId());
        log.info("doFollowSub,pendingAppointment,end,num:{}", num);
        return uePO;
    }

    private Long determineTimeoutOccurred(Date clueDisDate, Date date) throws Exception {
        log.info("determineTimeoutOccurred,clueDisDate:{},date:{}", clueDisDate, date);
        int clueDisTime = DateUtils.twentyFour(clueDisDate);
        int date1 = DateUtils.twentyFour(new Date());
        if (ObjectUtils.isEmpty(date)) {
            date = new Date();
        }
        Long equationTime = 0L;
        if (clueDisTime >= 9 && clueDisTime < 18 && date1 > 9 && date1 < 20) {
            /*线索下发时间大于9点，小于18点,且当前时间大于9点，小于20点，且超过两小时*/
            equationTime = DateUtils.getDateAfter(clueDisDate, date);
        } else if (clueDisTime >= 18 && clueDisTime < 20) {
            /*线索下发时间大于18点小于20点*/
            //线索下发时间+14小时
            String clueDisTimeT = DateUtils.getHourAfter(clueDisDate, 14);
            equationTime = DateUtils.getDateAfter(DateUtils.parse(clueDisTimeT, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS), date);
        } else if (clueDisTime >= 20 || clueDisTime < 9) {
            /*线索下发时间大于20点小于第二天9点*/
            int day = clueDisTime > 20 ? 1 : 0;
            String nextDay = DateUtils.parseDate(clueDisDate, day) + " 09:00:00";
            equationTime = DateUtils.getDateAfter(DateUtils.parse(nextDay, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS), date);
        }
        log.info("determineTimeoutOccurred,equationTime:{}", equationTime);
        return equationTime;
    }

    private TtFaultLightCluePO enteredStore(FaultLightFollowSubDTO subDTO, TtFaultLightCluePO po) {
        log.info("doFollowSub,enteredStore,start");
        TtFaultLightCluePO uePO = new TtFaultLightCluePO();
        TtFaultLightInvitationPO onPO = new TtFaultLightInvitationPO();
        //线索ID
        long id = po.getId();
        //当前时间
        Date data = new Date();
        //预约时间
        Date forecastTime = subDTO.getForecastTime();
        //线索状态
        Integer clueStatus = po.getClueStatus();
        //跟进状态
        Integer followState = po.getFollowStatus();
        //操作状态
        Integer followStatus = subDTO.getFollowStatus();
        followStatus = FaultFolOpeStateEnum.ENTERED_STORE_NOW.getCode().equals(followStatus) ?
                FaultFolOpeStateEnum.ENTERED_STORE.getCode() : followStatus;
        //邀约人
        LoginInfoDto loginInfo = FrameworkUtil.getLoginInfo();
        String inviteName = loginInfo.getUserName();
        String saId = loginInfo.getUserId().toString();
        //code
        String dealerCode = po.getDealerCode();
        //vin
        String vin = po.getVin();
        //工单号
        String afRoNo = po.getRoNo();
        //工单号
        String roNo = subDTO.getRoNo();
        if (ObjectUtils.isEmpty(roNo)) {
            log.info("doFollowSub,roNo:{}", roNo);
            throw new ServiceBizException("请输入工单号");
        }
        VehicleOwnerVO vo = null;
        List<VehicleOwnerVO> listVo;
        if (FaultFolOpeStateEnum.NO_ASSOCIATED_WORK_ORDER.getCode().equals(followState) &&
                !FaultFolOpeStateEnum.NO_ASSOCIATED_WORK_ORDER.getCode().equals(followStatus)) {
            log.info("doFollowSub,followState:{}, followStatus:{}", followState, followStatus);
            throw new ServiceBizException("跟进状态错误");
        }else if (FaultFolOpeStateEnum.NO_ASSOCIATED_WORK_ORDER.getCode().equals(followStatus)) {
            subDTO.setFollowStatus(FaultFolOpeStateEnum.ENTERED_STORE.getCode());
            return storeEntered(subDTO, po);
        }
        //工单号修改
        if (!roNo.equals(afRoNo)) {
            vo = this.queryRepairOrder(dealerCode, vin, roNo);
            log.info("doFollowSub,工单vo:{}", vo);
            if (ObjectUtils.isEmpty(vo)) {
                throw new ServiceBizException("工单号有误");
            }
            uePO.setRoStartTime(vo.getCreatedAt());
            uePO.setRoType(vo.getOrType());
            uePO.setRoNo(roNo);
        }
        //操作状态
        if (FaultFolOpeStateEnum.ENTERED_MISSING_PARTS.getCode().equals(followStatus)) {
            /*缺零件*/
            clueStatus = FaultClueStateEnum.ENTERED_STORE.getCode();
            followStatus = FaultFollowStateEnum.ENTERED_MISSING_PARTS.getCode();
            uePO.setMissParts(FaultClueWheResEnum.WHE_RES_TWO.getCode());
        } else if (FaultFolOpeStateEnum.NOT_REPAIR.getCode().equals(followStatus)) {
            /*车主不修*/
            clueStatus = FaultClueStateEnum.CLOSE_CLUES.getCode();
            followStatus = FaultFollowStateEnum.NOT_REPAIR.getCode();
            //关闭时间
            uePO.setClueCloTime(data);
            //不修
            uePO.setNoRepair(FaultClueWheResEnum.WHE_RES_TWO.getCode());
        } else if (FaultFolOpeStateEnum.ALREADY_COMPLETED.getCode().equals(followStatus)) {
            /*已交车*/
            clueStatus = FaultClueStateEnum.WAITING_FOR_VERIFICATION.getCode();
            followStatus = FaultFollowStateEnum.WAITING_FOR_VERIFICATION.getCode();
            listVo = repairCommonClient.queryRepairOrder(dealerCode, vin, roNo);
            if (CollectionUtils.isEmpty(listVo)) {
                log.info("CollectionUtils.isEmpty(vo)");
                throw new ServiceBizException("工单号有误");
            }
            log.info("doFollowSub,工单,listVo:{}", listVo.size());
            vo = listVo.get(0);
            log.info("doFollowSub,工单,vo:{}", vo);
            //判断工单是否已交车80671008
            String roStatus = vo.getDeliveryTag();
            if(!"80011001".equals(roStatus)){
                log.info("doFollowSub,!80011001.equals(roStatus)");
                throw new ServiceBizException("工单未交车");
            }
            //维修完成时间
            uePO.setRepairComTime(data);
            //工单结算时间
            uePO.setRoEndTime(vo.getForBalanceTime());
            //工单金额
            PrintParamVo paramsVo = new PrintParamVo();
            paramsVo.setOwnerCode(dealerCode);
            paramsVo.setPaySub(vo.getSubObbAmount());
            paramsVo.setPayYz(vo.getYzAmaount());
            paramsVo.setPrintType("BO");
            paramsVo.setPayObj(vo.getPaymentObjectCode());
            paramsVo.setPrintRegion("标准");
            paramsVo.setRepairGroupNoList(Arrays.asList(vo.getRoNo()));
            List<String> listNo = new ArrayList<>();
            String balanceNo;
            for (VehicleOwnerVO ownerVO : listVo) {
                balanceNo = ownerVO.getBalanceNo();
                if (!PAYMENT_OBJECT_CODE.equals(balanceNo)) {
                    listNo.add(balanceNo);
                }
            }
            paramsVo.setBalanceGroupNoList(listNo);
            PrintDataVo printDataVo = repairCommonClient.balancePrintData(paramsVo);
            if (ObjectUtils.isEmpty(printDataVo)) {
                log.info("doFollowSub,ObjectUtils.isEmpty(printDataVo)");
                throw new ServiceBizException("工单号有误");
            }
            String amountForDiscount = printDataVo.getAmountForDiscount();
            log.info("doFollowSub,printDataVo:{}", amountForDiscount);
            uePO.setRoAmount(amountForDiscount);
        }

        //修改线索 线索状态.跟进状态.
        uePO.setId(id);
        uePO.setIcmId(po.getIcmId());
        uePO.setClueStatus(clueStatus);
        uePO.setFollowStatus(followStatus);
        uePO.setAfClueStatus(po.getClueStatus());
        uePO.setHighlightFlag(Integer.valueOf(StateEscapeEnum.STATE_ONE.getCode()));
        int num = ttFaultLightClueMapper.updateFaultLightClueById(uePO);
        onPO.setClueId(id);
        onPO.setInviteName(inviteName);
        onPO.setSaId(saId);
        num += ttFaultLightInvitationMapper.updateByClueId(onPO);
        //跟进记录表
        TtFaultLightFollowRecordPO rdPO = TtFaultLightFollowRecordPO.builder()
                .clueId(id)
                .faultId(po.getFaultId())
                .faultCityName(po.getFaultCityName())
                .clueDisTime(po.getClueDisTime())
                .followTime(new Date())
                .clueStatus(clueStatus)
                .followStatus(followStatus)
                .followName(inviteName)
                .forecastTime(forecastTime)
                .roNo(roNo)
                .build();
        num += ttFaultLightFollowRecordMapper.insert(rdPO);
        ttFaultCallDetailsMapper.updateFaultLightDetailId(rdPO.getClueId(),rdPO.getId());
        log.info("doFollowSub,enteredStore,end,num:{}", num);
        return uePO;
    }

    private TtFaultLightCluePO storeEntered(FaultLightFollowSubDTO subDTO, TtFaultLightCluePO po) {
        log.info("doFollowSub,storeEntered,start");
        TtFaultLightCluePO uePO = new TtFaultLightCluePO();
        TtFaultLightInvitationPO onPO = new TtFaultLightInvitationPO();
        //线索ID
        long id = po.getId();
        //预约时间
        Date forecastTime = subDTO.getForecastTime();
        //邀约时间
        Date inviteTime = po.getInviteTime();
        //当前时间
        Date data = new Date();
        //线索状态
        Integer clueStatus = po.getClueStatus();
        //操作状态
        Integer followStatus = subDTO.getFollowStatus();
        followStatus = FaultFolOpeStateEnum.WAITING_FOR_APPOINTMENT.getCode().equals(followStatus) ?
                FaultFolOpeStateEnum.WAITING_ENTER_STORE.getCode() : followStatus;
        //邀约人
        LoginInfoDto loginInfo = FrameworkUtil.getLoginInfo();
        String inviteName = loginInfo.getUserName();
        String saId = loginInfo.getUserId().toString();
        //工单号
        String roNo = subDTO.getRoNo();
        //操作状态
        if (FaultFolOpeStateEnum.ENTERED_STORE.getCode().equals(followStatus)) {
            if (ObjectUtils.isEmpty(roNo)) {
                log.info("doFollowSub,roNo:{}", roNo);
                throw new ServiceBizException("请输入工单号");
            }
            VehicleOwnerVO vo = this.queryRepairOrder(po.getDealerCode(), po.getVin(), roNo);
            if (ObjectUtils.isEmpty(vo)) {
                log.info("doFollowSub,ObjectUtils.isEmpty(vo)");
                throw new ServiceBizException("工单号有误");
            }
            if(FaultClueStateEnum.WAITING_ENTER_STORE.getCode().equals(clueStatus)){
                //成功进店后的参数更新(之前是待进店状态,判断是正常进店)
                onPO.setIntoTime(data);
                int intoOnTime = faultLightService.getIntoOnTime(data, forecastTime);
                onPO.setIntoOnTime(intoOnTime);
                onPO.setNoInto(FaultClueWheResEnum.WHE_RES_ONE.getCode());
            }
            clueStatus = FaultClueStateEnum.ENTERED_STORE.getCode();
            log.info("doFollowSub,工单vo:{}", vo);
            uePO.setRoStartTime(vo.getCreatedAt());
            uePO.setRoType(vo.getOrType());
            uePO.setClueStatus(clueStatus);
            uePO.setFollowStatus(followStatus);
        }
        //修改线索 线索状态.跟进状态.
        //预约时间校验
        if (checkForecastTime(inviteTime, forecastTime)) {
            log.info("doFollowSub,inviteTime:{},forecastTime:{}", inviteTime, forecastTime);
            throw new ServiceBizException("预约时间输入范围不正确");
        }
        uePO.setForecastTime(forecastTime);
        uePO.setId(id);
        uePO.setIcmId(po.getIcmId());
        uePO.setRoNo(roNo);
        uePO.setHighlightFlag(Integer.valueOf(StateEscapeEnum.STATE_ONE.getCode()));
        uePO.setAfClueStatus(po.getClueStatus());
        int num = ttFaultLightClueMapper.updateFaultLightClueById(uePO);
        //预约记录表
        onPO.setClueId(id);
        onPO.setInviteName(inviteName);
        onPO.setSaId(saId);
        onPO.setForecastTime(forecastTime);
        num += ttFaultLightInvitationMapper.updateByClueId(onPO);
        //跟进记录表
        TtFaultLightFollowRecordPO rdPO = TtFaultLightFollowRecordPO.builder()
                .clueId(id)
                .faultId(po.getFaultId())
                .faultCityName(po.getFaultCityName())
                .clueDisTime(po.getClueDisTime())
                .followTime(new Date())
                .clueStatus(clueStatus)
                .followStatus(followStatus)
                .followName(inviteName)
                .forecastTime(forecastTime)
                .roNo(roNo)
                .build();
        num += ttFaultLightFollowRecordMapper.insert(rdPO);
        ttFaultCallDetailsMapper.updateFaultLightDetailId(rdPO.getClueId(),rdPO.getId());
        log.info("doFollowSub,storeEntered,end,num:{}", num);
        return uePO;
    }



    private  boolean checkForecastTime(Date inviteTime, Date forecastTime) {
        if(ObjectUtils.isEmpty(inviteTime) || ObjectUtils.isEmpty(forecastTime)){
            log.info("ObjectUtils.isEmpty(inviteTime) || ObjectUtils.isEmpty(forecastTime)");
            log.info("inviteTime:{}, forecastTime:{}", inviteTime, forecastTime);
            return Boolean.FALSE;
        }
        int day = DateUtils.daysDateBetween(inviteTime, forecastTime);
        if(day > mostDay || day < leastDay){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private VehicleOwnerVO queryRepairOrder(String dealerCode, String vin, String roNo){
        List<VehicleOwnerVO> list = repairCommonClient.queryRepairOrder(dealerCode, vin, roNo);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }
}
