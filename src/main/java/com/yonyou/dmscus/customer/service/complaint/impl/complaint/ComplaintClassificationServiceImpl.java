package com.yonyou.dmscus.customer.service.complaint.impl.complaint;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintClassificationPO;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintClassificationMapper;
import com.yonyou.dmscus.customer.service.complaint.ComplaintClassificationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintClassificationDTO;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 客诉工单分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-13
 */
@Service
public class ComplaintClassificationServiceImpl implements ComplaintClassificationService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    ComplaintClassificationMapper complaintClassificationMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                       分页对象
     * @param complaintClassificationDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintClassificationDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<ComplaintClassificationDTO> selectPageBysql(Page page, ComplaintClassificationDTO complaintClassificationDTO) {
        if (complaintClassificationDTO == null) {
            complaintClassificationDTO = new ComplaintClassificationDTO();
        }
        ComplaintClassificationPO complaintClassificationPO = complaintClassificationDTO.transDtoToPo(ComplaintClassificationPO.class);

        List<ComplaintClassificationPO> list = complaintClassificationMapper.selectPageBySql(page, complaintClassificationPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<ComplaintClassificationDTO> result = list.stream().map(m -> m.transPoToDto(ComplaintClassificationDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param complaintClassificationDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintClassificationDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<ComplaintClassificationDTO> selectListBySql(ComplaintClassificationDTO complaintClassificationDTO) {
        if (complaintClassificationDTO == null) {
            complaintClassificationDTO = new ComplaintClassificationDTO();
        }
        ComplaintClassificationPO complaintClassificationPO = complaintClassificationDTO.transDtoToPo(ComplaintClassificationPO.class);
        List<ComplaintClassificationPO> list = complaintClassificationMapper.selectListBySql(complaintClassificationPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(ComplaintClassificationDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintClassificationDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public ComplaintClassificationDTO getById(Long id) {
        ComplaintClassificationPO complaintClassificationPO = complaintClassificationMapper.selectById(id);
        if (complaintClassificationPO != null) {
            return complaintClassificationPO.transPoToDto(ComplaintClassificationDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param complaintClassificationDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(ComplaintClassificationDTO complaintClassificationDTO) {
        //对对象进行赋值操作
        ComplaintClassificationPO complaintClassificationPO = complaintClassificationDTO.transDtoToPo(ComplaintClassificationPO.class);
        //执行插入
        int row = complaintClassificationMapper.insert(complaintClassificationPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                         主键ID
     * @param complaintClassificationDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, ComplaintClassificationDTO complaintClassificationDTO) {
        ComplaintClassificationPO complaintClassificationPO = complaintClassificationMapper.selectById(id);
        //对对象进行赋值操作
        complaintClassificationDTO.transDtoToPo(complaintClassificationPO);
        //执行更新
        int row = complaintClassificationMapper.updateById(complaintClassificationPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = complaintClassificationMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = complaintClassificationMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    @Override
    public List<ComplaintClassificationDTO> selectComplaintCategory(ComplaintClassificationDTO complaintClassificationDTO ) {
        return complaintClassificationMapper.selectComplaintCategory(complaintClassificationDTO);
    }

    @Override
    public List<ComplaintClassificationPO> getByIds(List<Long> ids) {
        return complaintClassificationMapper.getByIds(ids);
    }


}
