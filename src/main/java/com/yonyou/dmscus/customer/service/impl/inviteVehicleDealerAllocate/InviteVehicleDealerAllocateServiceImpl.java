package com.yonyou.dmscus.customer.service.impl.inviteVehicleDealerAllocate;



import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.common.AjaxResponse;
import com.yonyou.dmscus.customer.dao.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryMapper;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.*;
import com.yonyou.dmscus.customer.entity.dto.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryDTO;
import com.yonyou.dmscus.customer.entity.po.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryPO;
import com.yonyou.dmscus.customer.entity.po.inviteVehicleDealerAllocate.InviteVehicleDealerAllocatePO;
import com.yonyou.dmscus.customer.dao.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateMapper;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateService;
import com.yonyou.dmscus.customer.entity.dto.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateDTO;
import com.yonyou.dmscus.customer.util.common.BeanUtils;
import com.yonyou.dmscus.customer.util.common.ImportExcelUtil;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import io.swagger.models.auth.In;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 车店分配表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@Service
public class InviteVehicleDealerAllocateServiceImpl implements InviteVehicleDealerAllocateService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteVehicleDealerAllocateMapper inviteVehicleDealerAllocateMapper;
    @Resource
    InviteVehicleDealerAllocateHistoryMapper inviteVehicleDealerAllocateHistoryMapper;
    @Autowired
    BusinessPlatformService businessPlatformService;
    @Autowired
    InviteVehicleRecordService inviteVehicleRecordService;
    @Autowired
    ReportCommonClient reportCommonClient;

    /**
     * 分页查询对应数据
     *
     * @param page                           分页对象
     * @param inviteVehicleDealerAllocateDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto
                       *       .       InviteVehicleDealerAllocateDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<InviteVehicleDealerAllocateDTO> selectPageBysql(Page page, InviteVehicleDealerAllocateDTO
            inviteVehicleDealerAllocateDTO) {
        if (inviteVehicleDealerAllocateDTO == null) {
            inviteVehicleDealerAllocateDTO = new InviteVehicleDealerAllocateDTO();
        }
        InviteVehicleDealerAllocatePO inviteVehicleDealerAllocatePO = inviteVehicleDealerAllocateDTO.transDtoToPo
                (InviteVehicleDealerAllocatePO.class);

        List<InviteVehicleDealerAllocatePO> list = inviteVehicleDealerAllocateMapper.selectPageBySql(page,
                inviteVehicleDealerAllocatePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteVehicleDealerAllocateDTO> result = list.stream().map(m -> m.transPoToDto
                    (InviteVehicleDealerAllocateDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param inviteVehicleDealerAllocateDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<InviteVehicleDealerAllocateDTO> selectListBySql(InviteVehicleDealerAllocateDTO
                                                                        inviteVehicleDealerAllocateDTO) {
        if (inviteVehicleDealerAllocateDTO == null) {
            inviteVehicleDealerAllocateDTO = new InviteVehicleDealerAllocateDTO();
        }
        InviteVehicleDealerAllocatePO inviteVehicleDealerAllocatePO = inviteVehicleDealerAllocateDTO.transDtoToPo
                (InviteVehicleDealerAllocatePO.class);
        List<InviteVehicleDealerAllocatePO> list = inviteVehicleDealerAllocateMapper.selectListBySql
                (inviteVehicleDealerAllocatePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteVehicleDealerAllocateDTO.class)).collect(Collectors
                    .toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public InviteVehicleDealerAllocateDTO getById(Long id) {
        InviteVehicleDealerAllocatePO inviteVehicleDealerAllocatePO = inviteVehicleDealerAllocateMapper.selectById(id);
        if (inviteVehicleDealerAllocatePO != null) {
            return inviteVehicleDealerAllocatePO.transPoToDto(InviteVehicleDealerAllocateDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param inviteVehicleDealerAllocateDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(InviteVehicleDealerAllocateDTO inviteVehicleDealerAllocateDTO) {
        //对对象进行赋值操作
        InviteVehicleDealerAllocatePO inviteVehicleDealerAllocatePO = inviteVehicleDealerAllocateDTO.transDtoToPo
                (InviteVehicleDealerAllocatePO.class);
        //执行插入
        int row = inviteVehicleDealerAllocateMapper.insert(inviteVehicleDealerAllocatePO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                             主键ID
     * @param inviteVehicleDealerAllocateDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, InviteVehicleDealerAllocateDTO inviteVehicleDealerAllocateDTO) {
        InviteVehicleDealerAllocatePO inviteVehicleDealerAllocatePO = inviteVehicleDealerAllocateMapper.selectById(id);
        //对对象进行赋值操作
        inviteVehicleDealerAllocateDTO.transDtoToPo(inviteVehicleDealerAllocatePO);
        //执行更新
        int row = inviteVehicleDealerAllocateMapper.updateById(inviteVehicleDealerAllocatePO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = inviteVehicleDealerAllocateMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = inviteVehicleDealerAllocateMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 查询经销商信息
     * @param dealerCode
     * @return
     */
    @Override
    public List<CompanyDetailDTO> queryDealer(String dealerCode) {
        return businessPlatformService.getDealer(null,null,dealerCode,null);
    }

    /**
     * 分配车主
     * @param dto
     * @return
     */
    @Override
    public int allocation(InviteVehicleDealerAllocateDTO dto) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        int sum = 0;
        HashMap<String,Integer> map = new HashMap();
        for (InviteVehicleDealerAllocateHistoryDTO item:dto.getVehicleList()) {
            int rows = this.updateAllocation(item.getVin(),item.getLicensePlateNum(),item.getName(),dto.getDealerCode(),
                    dto.getDealerName(),item.getDealerCode(),item.getDealerName(),loginInfoDto);
            sum+=rows;
        }
        if(sum!=0){
            reportCommonClient.sendMessageByRole("邀约管理","店内新分配线索"+sum+"条,请及时前往【邀约管理-邀约任务分配】进行分配。"
                    ,dto.getDealerCode(),"KFJL");
        }
        return 1;
    }

    /**
     * 分配车主
     * @param vin
     * @param licensePlateNum
     * @param name
     * @param dealerCode
     * @param dealerName
     * @param lastDealerCode
     * @param lastDealerName
     * @param loginInfoDto
     */
    private int updateAllocation(String vin,String licensePlateNum,String name,String dealerCode,String dealerName,
                                  String lastDealerCode,String lastDealerName,LoginInfoDto loginInfoDto){
        LambdaQueryWrapper<InviteVehicleDealerAllocatePO> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(InviteVehicleDealerAllocatePO::getVin,vin);
        InviteVehicleDealerAllocatePO po = inviteVehicleDealerAllocateMapper.selectOne(queryWrapper);
        if(po==null){
            po=new InviteVehicleDealerAllocatePO();
            po.setVin(vin);
            po.setDealerCode(dealerCode);
            po.setDealerName(dealerName);
            po.setCreatedBy(String.valueOf(loginInfoDto.getUserId()));
            inviteVehicleDealerAllocateMapper.insertAllocate(po);
        }else{
            po.setDealerCode(dealerCode);
            po.setDealerName(dealerName);
            po.setUpdatedAt(new Date());
            po.setUpdatedBy(String.valueOf(loginInfoDto.getUserId()));
            inviteVehicleDealerAllocateMapper.updateAllocateById(po);
        }
        //记录分配历史
        InviteVehicleDealerAllocateHistoryPO history = new InviteVehicleDealerAllocateHistoryPO();
        history.setVin(vin);
        history.setLicensePlateNum(licensePlateNum);
        history.setName(name);
        history.setDealerCode(dealerCode);
        history.setDealerName(dealerName);
        history.setLastDealerCode(lastDealerCode);
        history.setLastDealerName(lastDealerName);
        po.setCreatedBy(String.valueOf(loginInfoDto.getUserId()));
        //操作人姓名
        history.setOperatorName(loginInfoDto.getUserCode());
        inviteVehicleDealerAllocateHistoryMapper.insertAllocateHistory(history);
        int rows = inviteVehicleRecordService.updateInviteByDealerCode(vin,dealerCode,lastDealerCode);
        return rows;
    }


    @Override
    public IPage<OwnerVehicleVO> getVehicle(String license, String vin, String name, String dealerName, String
            dealerCode, Long currentPage, Long pageSize) {
        PageRequestDTO<OwnerVehicleDTO> dto = new PageRequestDTO<OwnerVehicleDTO>();
        OwnerVehicleDTO veh = new OwnerVehicleDTO();
        veh.setVin(vin);
        veh.setPlateNumber(license);
        veh.setName(name);
        veh.setCompanyCode(dealerCode);
        veh.setCompanyNameCn(dealerName);
        dto.setData(veh);
        dto.setPage(currentPage);
        dto.setPageSize(pageSize);
        IPage<OwnerVehicleVO> page = businessPlatformService.getVehicle(dto);
        HashMap<String,OwnerVehicleVO> set = new HashMap<String,OwnerVehicleVO>();
        if(page.getRecords().size()>0){
            for (OwnerVehicleVO item :page.getRecords()){
                set.put(item.getVin(),item);
            }
            List<TmVehicleDTO> list =  inviteVehicleDealerAllocateMapper.getVehicleBySql(set.keySet());
            for (TmVehicleDTO rs:list) {
                OwnerVehicleVO back = set.get(rs.getVin());
                back.setDealerCode(rs.getDealerCode());
                back.setDealerName(rs.getDealerName());
            }
            List<OwnerVehicleVO> records = new ArrayList<OwnerVehicleVO>();
            for (OwnerVehicleVO item :set.values()) {
                records.add(item);
            }
            page.setRecords(records);
        }
        return page;
    }

    @Override
    public AjaxResponse importUpload(MultipartFile importFile) {
        AjaxResponse ajaxResponse = new AjaxResponse();
        StopWatch sw = new StopWatch();
        try {
            logger.info("=1=importUpload 进入===");
            sw.start();
            // excel数据转换列表
            List<Map<Integer, String>> dataList = ImportExcelUtil.getImportExcelData(importFile.getInputStream(),
                    importFile.getOriginalFilename());
            if (CollectionUtils.isEmpty(dataList)) {
                ajaxResponse.setFail("5001", "导入的Excel数据不能为空");
                return ajaxResponse;
            }
            logger.info("=2=importUpload 文件已经转成dataList===");
            // 调用导入数据接口
            ajaxResponse = this.importExcelData(null,
                    null, dataList);

        } catch (Throwable e) {
            logger.error("call InviteVehicleDealerAllocateController.importUpload() method error msg:{}", e);
            ajaxResponse.setResult(AjaxResponse.FAILD);
            ajaxResponse.setMsg("文件解析失败!");
            ajaxResponse.addObject("msgDetail", BeanUtils.getExceptionMsg(e));
        }finally {
            sw.stop();
        }
        if(sw != null) {
            ajaxResponse.addObject("execTime", sw.getTotalTimeSeconds()+"秒");
        }
        return ajaxResponse;
    }


    public AjaxResponse importExcelData(Long operationId, String operationName,
                                        List<Map<Integer, String>> importDataList) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        AjaxResponse ajaxResponse = new AjaxResponse();
        logger.info("=3=vocMileageDataImport-importExcelData excel数据放入对象===");

        //插入数据库成功数量(安全)
        List<Integer> successCntList = new CopyOnWriteArrayList<Integer>();
        //线程插入数据
        this.addThreadimportExcelData(loginInfoDto,importDataList, successCntList);
        logger.info("=5=vocMileageDataImport 线程结束===");
        //成功总数
        long sum = successCntList.stream().reduce(Integer::sum).orElse(0);
        ajaxResponse.setSuccess("200", "操作成功");
        ajaxResponse.addObject("successCnt", sum);
        return ajaxResponse;
    }

    private void addThreadimportExcelData(LoginInfoDto loginInfoDto,final List<Map<Integer, String>> insertList,final
    List<Integer> successCntList) {
        // 线程数量
        int maxThreadSize = 5;
        int runsize = 5;
        if(insertList.size()<runsize){
            runsize=insertList.size();
        }
        ConcurrentHashMap<String,Integer> map = new ConcurrentHashMap();
        ThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(maxThreadSize);
        CountDownLatch countDownLatch = new CountDownLatch(runsize);
        for (Map<Integer, String> po:insertList) {
            ImportExcelDataThread thread = new ImportExcelDataThread(loginInfoDto,po,countDownLatch,
                    successCntList,map);
            executor.execute(thread);
        }
        try {
            countDownLatch.await();
            for (Map.Entry<String,Integer> msg:map.entrySet()) {
                if(msg.getValue()!=0){
                    reportCommonClient.sendMessageByRole("邀约管理","店内新分配线索"+msg.getValue()+"条,请及时前往【邀约管理-邀约任务分配】进行分配。"
                            ,msg.getKey(),"KFJL");
                }
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        executor.shutdown();
    }

    /**
     * 多线程处理
     */
    public class ImportExcelDataThread implements Runnable{
        private Map<Integer, String> po;
        private CountDownLatch countDownLatch;
        private List<Integer> successCntList;
        private LoginInfoDto loginInfoDto;
        private ConcurrentHashMap<String,Integer> map;
        public ImportExcelDataThread(LoginInfoDto loginInfoDto,Map<Integer, String> po,CountDownLatch countDownLatch,
                                                List<Integer> successCntList,ConcurrentHashMap<String,Integer> map) {
            super();
            this.po = po;
            this.countDownLatch = countDownLatch;
            this.successCntList = successCntList;
            this.loginInfoDto=loginInfoDto;
            this.map=map;
        }
        @Override
        public void run() {
            try {
                batchUpdateData(po,loginInfoDto,this.map);
                if(successCntList!=null){
                    successCntList.add(1);
                }
            } catch (Exception e) {
                logger.error(">>>多线程处理数据异常:{}", e);
            }finally{
                if(countDownLatch != null) {
                    countDownLatch.countDown();
                }
            }
        }
    }


    /**
     * 车店分配
     * @param po
     * @return
     */
    public int batchUpdateData(Map<Integer, String> po,LoginInfoDto loginInfoDto,ConcurrentHashMap<String,Integer> map){
        String vin= po.get(0);
        String lastDealerCode = po.get(1);
        String dealerCode = po.get(2);
        logger.info("车店分配,vin{},lastDealerCode{},dealerCode{}",vin,lastDealerCode,dealerCode);
        int rows =this.updateAllocation(vin,null,null,dealerCode,null,lastDealerCode,
                null,loginInfoDto);
        if(map.containsKey(dealerCode)){
            map.put(dealerCode,map.get(dealerCode)+rows);
        }else{
            map.put(dealerCode,rows);
        }
        return 1;
    }

}
