package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

    
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentTestDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAttachmentPO;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintAttachmentMapper;
import com.yonyou.dmscus.customer.service.complaint.ComplaintAttachmentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;




    /**
 * <p>
 * 客户投诉附件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Service
        public class ComplaintAttachmentServiceImpl implements ComplaintAttachmentService {
        /**
         * 日志说明
         */
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        ComplaintAttachmentMapper complaintAttachmentMapper;

        /**
         * 分页查询对应数据
         *
         * @param page 分页对象
         * @param complaintAttachmentDTO 查询条件
         * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public IPage<ComplaintAttachmentDTO>selectPageBysql(Page page,ComplaintAttachmentDTO complaintAttachmentDTO){
            if(complaintAttachmentDTO ==null){
                complaintAttachmentDTO =new ComplaintAttachmentDTO();
            }
            ComplaintAttachmentPO complaintAttachmentPo =complaintAttachmentDTO.transDtoToPo(ComplaintAttachmentPO.class);

            List<ComplaintAttachmentPO>list= complaintAttachmentMapper.selectPageBySql(page,complaintAttachmentPo);
            if(CommonUtils.isNullOrEmpty(list)){
                page.setRecords(new ArrayList<>());
                return page;
            }else{
                List<ComplaintAttachmentDTO>result=list.stream().map(m->m.transPoToDto(ComplaintAttachmentDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param complaintAttachmentDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<ComplaintAttachmentDTO>selectListBySql(ComplaintAttachmentDTO complaintAttachmentDTO){
            if(complaintAttachmentDTO ==null){
                complaintAttachmentDTO =new ComplaintAttachmentDTO();
            }
            ComplaintAttachmentPO complaintAttachmentPo =complaintAttachmentDTO.transDtoToPo(ComplaintAttachmentPO.class);
            List<ComplaintAttachmentPO>list= complaintAttachmentMapper.selectListBySql(complaintAttachmentPo);
            if(CommonUtils.isNullOrEmpty(list)){
                return new ArrayList<>();
            }else{
                return list.stream().map(m->m.transPoToDto(ComplaintAttachmentDTO.class)).collect(Collectors.toList());
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public ComplaintAttachmentDTO getById(Long id){
            ComplaintAttachmentPO complaintAttachmentPo = complaintAttachmentMapper.selectById(id);
            if(complaintAttachmentPo!=null){
                return complaintAttachmentPo.transPoToDto(ComplaintAttachmentDTO.class);
            }else{
                throw new DALException("查询失败,没有符合条件的数据");
            }
        }

        /**
         * 根据DTO 进行数据新增
         *
         * @param complaintAttachmentDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(ComplaintAttachmentDTO complaintAttachmentDTO){
            //对对象进行赋值操作
            ComplaintAttachmentPO complaintAttachmentPo = complaintAttachmentDTO.transDtoToPo(ComplaintAttachmentPO.class);
            //执行插入
            int row= complaintAttachmentMapper.insert(complaintAttachmentPo);
            //返回插入的值
            return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param complaintAttachmentDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, ComplaintAttachmentDTO complaintAttachmentDTO){
            ComplaintAttachmentPO complaintAttachmentPo = complaintAttachmentMapper.selectById(id);
            //对对象进行赋值操作
            complaintAttachmentDTO.transDtoToPo(complaintAttachmentPo);
            //执行更新
            int row= complaintAttachmentMapper.updateById(complaintAttachmentPo);
            return row;
        }

        /**
         * 根据ID 进行删除数据
         *
         * @param id
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteById(Long id){
            int deleteCount= complaintAttachmentMapper.deleteById(id);
            logger.debug("删除数量：{}",deleteCount);
            if(deleteCount<=0){
                throw new DALException("删除失败,没有符合条件的数据");
            }else{
                return deleteCount;
            }
        }

        @Override
        public IPage<ComplaintAttachmentDTO> selectPageBysql1(Page page, ComplaintAttachmentDTO complaintAttachmentDTO) {
            if(complaintAttachmentDTO ==null){
                complaintAttachmentDTO =new ComplaintAttachmentDTO();
            }
            ComplaintAttachmentPO complaintAttachmentPo =complaintAttachmentDTO.transDtoToPo(ComplaintAttachmentPO.class);

            List<ComplaintAttachmentPO>list= complaintAttachmentMapper.selectPageBySql1(page,complaintAttachmentPo);
            if(CommonUtils.isNullOrEmpty(list)){
                page.setRecords(new ArrayList<>());
                return page;
            }else{
                List<ComplaintAttachmentDTO>result=list.stream().map(m->m.transPoToDto(ComplaintAttachmentDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        @Override
        public List<ComplaintAttachmentTestDTO> selectListBySql1(ComplaintAttachmentDTO complaintAttachmentDTO) {
            if(complaintAttachmentDTO ==null){
                complaintAttachmentDTO =new ComplaintAttachmentDTO();
            }
            ComplaintAttachmentPO complaintAttachmentPo =complaintAttachmentDTO.transDtoToPo(ComplaintAttachmentPO.class);
            List<ComplaintAttachmentTestDTO>list= complaintAttachmentMapper.selectListBySql1(complaintAttachmentPo);
            if(CommonUtils.isNullOrEmpty(list)){
                return new ArrayList<>();
            }else{
                return list;
            }
        }

        /**
         * 删除附件
         *
         * @param id
         */
        @Override
        public void delete(Long id) {
            complaintAttachmentMapper.deleteAct(id);
        }


    }
