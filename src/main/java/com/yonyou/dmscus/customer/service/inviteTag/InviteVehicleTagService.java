package com.yonyou.dmscus.customer.service.inviteTag;

import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;

import com.yonyou.dmscus.customer.dto.InviteVehicleTagParamsDTO;
import com.yonyou.dmscus.customer.dto.RecVehicleTagDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.InviteClueParamDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.InviteClueResultDTO;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dto.InviteVehicleTagDTO;
import com.yonyou.dmscus.customer.dto.InviteVehicleTagOwnerDTO;

public interface InviteVehicleTagService  {

	/**
	 * 列表页查询
	 * @param page
	 * @param query
	 * @return
	 */
	IPage<InviteVehicleTagDTO> selectPageBysql(Page<InviteVehicleTagDTO> page, InviteVehicleTagParamsDTO query);
	
	/**
	 * 查询VIN绑定的标签
	 * @param vin
	 * @return
	 */
	InviteVehicleTagDTO getVinTag(String vin);


	long insertBatchInviteVehicleTag(Map<String, Set<String>> vinTagMap, Integer bindType);

	/**
	 * Excel标签导入
	 * @param multipartFile
	 * @return
	 */
	void importInviteTag(MultipartFile multipartFile);
	
	/**
	 * 导出
	 * @param response
	 * @param param
	 */
	void exportInviteTag(HttpServletResponse response, InviteVehicleTagParamsDTO param);

	void receiveVinTags(RecVehicleTagDTO recVehicleTagDTO);

	Map<String,String> getAllTag();

	boolean getLossTag(String vin,String dealerCode);

    List<InviteClueResultDTO> selectInviteClueTag(InviteClueParamDTO inviteClueParamDTO);
}
