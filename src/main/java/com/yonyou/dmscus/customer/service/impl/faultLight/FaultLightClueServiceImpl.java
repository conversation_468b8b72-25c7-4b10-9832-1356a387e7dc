package com.yonyou.dmscus.customer.service.impl.faultLight;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscloud.function.utils.common.StringUtils;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.constants.RedisEnum;
import com.yonyou.dmscus.customer.constants.faultLight.*;
import com.yonyou.dmscus.customer.dao.faultLight.*;
import com.yonyou.dmscus.customer.dto.CustomerInfoDto;
import com.yonyou.dmscus.customer.dto.faultLight.FaultLightBookingRecordDto;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.CompanyDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.*;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.CallDetailsDTO;
import com.yonyou.dmscus.customer.entity.po.faultLight.*;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.CdpTagTaskPo;
import com.yonyou.dmscus.customer.enums.CacheKeyEnum;
import com.yonyou.dmscus.customer.feign.RepairCommonClient;
import com.yonyou.dmscus.customer.service.cdpTagTask.qb.CdpTagTaskService;
import com.yonyou.dmscus.customer.service.clueMigrate.ITmClueMigrateTaskService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightClueService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightService;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import com.yonyou.dmscus.customer.utils.ClazzConverter;
import com.yonyou.dmscus.customer.utils.DateCalUtil;
import com.yonyou.dmscus.customer.utils.RedisLockUtil.DistributedLockUtil;
import com.yonyou.dmscus.customer.utils.RedisLockUtil.IDistributedLock;
import com.yonyou.dmscus.customer.utils.Utills;
import com.yonyou.dmscus.customer.utils.excel.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class FaultLightClueServiceImpl implements FaultLightClueService {

    private static final int MAX_LENGTH = 200;

    @Resource
    private TtFaultLightClueMapper ttFaultLightClueMapper;
    @Resource
    private TtFaultLightInvitationMapper ttFaultLightInvitationMapper;
    @Resource
    private TtFaultLightFollowRecordMapper ttFaultLightFollowRecordMapper;
    @Resource
    private TtFaultLightInfoMapper ttFaultLightInfoMapper;
    @Resource
    private TtFaultLightMqRecordMapper ttFaultLightMqRecordMapper;

    @Autowired
    private ITmClueMigrateTaskService clueMigrateTaskService;
    @Autowired
    private FaultLightService faultLightService;
    @Autowired
    private FaultLightClueServiceHelper faultLightClueServiceHelper;

    @Autowired
    private RepairCommonClient repairCommonClient;

    @Autowired
    private CdpTagTaskService cdpTagTaskService;


    private static void subComments(ClueDataSynchroDTO dto) {
        CallInfoDTO callInfo = dto.getCallInfo();
        if (ObjectUtils.isEmpty(callInfo)) {
            return;
        }
        String comments = callInfo.getComments();
        if (ObjectUtils.isNotEmpty(comments)) {
            String subComments = StrUtil.maxLength(comments, MAX_LENGTH);
            comments = subComments;
            callInfo.setComments(comments);
        }
    }

    private static void assignmentPo(CompanyDetailDTO comDTO, TtFaultLightCluePO po) {
        if (comDTO != null) {
            log.info("doClueDataSynchro,comDTO != null");
            po.setRegionId(comDTO.getAfterBigAreaId());                 /*大区id*/
            po.setRegionName(comDTO.getAfterBigAreaName());             /*大区名称*/
            po.setCellId(comDTO.getAfterSmallAreaId());                 /*小区id*/
            po.setCellName(comDTO.getAfterSmallAreaName());             /*小区名称*/
            po.setCityId(Long.valueOf(comDTO.getCityId()));             /*城市id*/
            po.setCityName(comDTO.getCityName());                       /*城市名称*/
            po.setGroupCompanyShortName(comDTO.getGroupCompanyName());  /*集团简称*/
        }
    }

    private static TtFaultLightCluePO getTtFaultLightCluePO(ClueDataSynchroDTO dto, Date date, TtFaultLightCluePO cluePO, String dealerCode, Integer clueStatus, Integer followStatus) {
        return TtFaultLightCluePO.builder()
                .id(cluePO.getId())
                .dealerCode(dealerCode)
                .dealerName(dto.getDealerName())
                .clueStatus(clueStatus)
                .followStatus(followStatus)
                .clueDisTime(date)
                .build();
    }

    private static String getComments(TtFaultLightCluePO cluePO, CallInfoDTO callInfo, TtFaultLightInvitationPO ionPO) throws Exception {
        String comments;
        log.info("doClueDataSynchro,callInfoDTO != null");
        /*联系完成时间*/
        String firstCallTime = callInfo.getCallEndTime();
        /*坐席姓名*/
        String firstSeatName = callInfo.getFirstCallSeat();
        /*通话结果*/
        String callResult = callInfo.getCallResult();
        /*外呼备注*/
        comments = callInfo.getComments();
        /*响应时间*/
        String contactResTime = null;
        /*是否超时*/
        Integer contactOvertime = null;
        if (!StringUtils.isNullOrEmpty(firstCallTime)) {
            //计算是否超时 联系完成时间 - 线索下发时间(胡仓下发crm线索时间)
            Long equationTime = DateCalUtil.calculateTime(cluePO.getClueGenTime(), DateUtils.parse(firstCallTime, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
            BigDecimal b = new BigDecimal((double) equationTime / 60);
            Double hour = b.setScale(1, BigDecimal.ROUND_DOWN).doubleValue();
            hour = hour <= 0 ? 0.1 : hour;
            log.info("doClueDataSynchro,hour:{}", hour);
            contactResTime = hour.toString();
            log.info("doClueDataSynchro,contactResTime:{}", contactResTime);
            contactOvertime = hour >= 4 ? 2 : 1;
            log.info("doClueDataSynchro,contactOvertime:{}", contactOvertime);
        }
        ionPO.setContactTime(DateUtils.parse(firstCallTime, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
        ionPO.setContactsName(firstSeatName);
        ionPO.setContactResTime(contactResTime);
        ionPO.setContactOvertime(contactOvertime);
        ionPO.setContactResult(callResult);
        ionPO.setComments(comments);
        return comments;
    }

    @Deprecated
    private static Long determineTimeoutOccurred(Date clueDisDate, Date date, int host) throws Exception {
        log.info("determineTimeoutOccurred,clueDisDate:{},date:{}", clueDisDate, date);
        int clueDisTime = DateUtils.twentyFour(clueDisDate);
        int date1 = DateUtils.twentyFour(new Date());
        if (ObjectUtils.isEmpty(date)) {
            date = new Date();
        }
        Long equationTime = 0L;
        if (clueDisTime >= 9 && clueDisTime < (20 - host) && date1 > 9 && date1 < 20) {
            /*线索下发时间大于9点，小于18点,且当前时间大于9点，小于20点，且超过两小时*/
            equationTime = DateUtils.getDateAfter(clueDisDate, date);
        } else if (clueDisTime >= (20 - host) && clueDisTime < 20) {
            /*线索下发时间大于18点小于20点*/
            //线索下发时间+14小时
            String clueDisTimeT = DateUtils.getHourAfter(clueDisDate, 14);
            equationTime = DateUtils.getDateAfter(DateUtils.parse(clueDisTimeT, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS), date);
        } else if (clueDisTime >= 20 || clueDisTime < 9) {
            /*线索下发时间大于20点小于第二天9点*/
            int day = clueDisTime > 20 ? 1 : 0;
            String nextDay = DateUtils.parseDate(clueDisDate, day) + " 09:00:00";
            equationTime = DateUtils.getDateAfter(DateUtils.parse(nextDay, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS), date);
        }
        log.info("determineTimeoutOccurred,equationTime:{}", equationTime);
        return equationTime;
    }

    /**
     * 线索,跟进状态联动下拉框
     *
     * @return FaultLightStateDTO
     */
    @Override
    public FaultLightStateDTO selectFaultLightState() {

        log.info("selectFaultLightState,start");
        List<FaultLightParamDTO> faultClueState = new ArrayList<>();
        for (FaultClueStateEnum value : FaultClueStateEnum.values()) {
            faultClueState.add(new FaultLightParamDTO(value.getCode(), value.getName()));
        }
        List<FaultLightParamDTO> faultFollowState = new ArrayList<>();
        for (FaultFollowStateEnum value : FaultFollowStateEnum.values()) {
            if (value.getState().equals(1)) {
                faultFollowState.add(new FaultLightParamDTO(value.getParentCode(), value.getCode(), value.getName()));
            }
        }
        log.info("selectFaultLightState,end,FaultClueState:{},FaultFollowState:{}",
                JSON.toJSONString(faultClueState), JSON.toJSONString(faultFollowState));
        return new FaultLightStateDTO(faultClueState, faultFollowState);
    }

    /**
     * 大小区,城市下拉框
     *
     * @return CityDropdownDoxDTO
     */
    @Override
    public List<CityDropdownDoxDTO> queryCityDropdownDox() {

        return ClazzConverter.converterClass(ttFaultLightClueMapper.queryCityDropdownDox(), CityDropdownDoxDTO.class);
    }

    /**
     * 线索同步接口
     *
     * @param dto ClueDataSynchroDTO
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean doClueDataSynchro(ClueDataSynchroDTO dto) {
        log.info("doClueDataSynchro,start,dto:{}", JSON.toJSONString(dto));
        long startTime = new Date().getTime();
        Long icmId = dto.getId();
        int num = 0;
        if (icmId == null) {
            throw new ServiceBizException("icmId不正确");
        }
        IDistributedLock lock = DistributedLockUtil.getDistributedLock(
                RedisEnum.CLUE_DATA_SYNCHRO.getKey(icmId), RedisEnum.CLUE_DATA_SYNCHRO.getTimeoutMsecs());
        if (lock.acquire()) {//获取锁
            try {
                //备注字段截取长度255,存200
                subComments(dto);
                //数据快照
                this.addClueCompensate(dto);
                //添加线索数据
                num = addClueDataSynchro(dto);
            } catch (Exception e) {
                log.error("doClueDataSynchro: ", e);
                throw new RuntimeException(e);
            } finally {
                lock.release(); //释放
                long endTime = new Date().getTime();
                log.info("doClueDataSynchro,lock,end:{}", endTime - startTime);
            }
        }
        return num > 0;
    }

    /**
     * 关联工单下拉框查询
     *
     * @param id long
     * @return String
     */
    @Override
    public List<String> selectRoNoSpinner(long id) {

        log.info("selectRoNoSpinner,id:{}", id);
        TtFaultLightCluePO po = ttFaultLightClueMapper.selectById(id);
        log.info("selectRoNoSpinner,po:{}", JSON.toJSONString(po));
        return repairCommonClient.queryRoNoSpinner(po.getDealerCode(), po.getVin(), po.getClueDisTime(), null);
    }

    @Override
    public IPage<ClueInfoQueryRespDTO> queryClueInfoList(Page<ClueInfoQueryRespDTO> page, ClueInfoQueryRequestDTO dto) {

        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //获取店端登录经销商CODE
        if (loginInfoDto == null || StringUtils.isBlank(loginInfoDto.getOwnerCode())) {
            throw new ServiceBizException("获取当前账户的信息为空,请重新登录!!!");
        } else {
            List<String> stringList = new ArrayList<>();
            stringList.add(loginInfoDto.getOwnerCode());
            dto.setDealerCode(stringList);
        }
        List<ClueInfoQueryRespDTO> clueInfoQueryRespDTOS =
                ttFaultLightClueMapper.queryClueInfoList(page, dto);

        List<Long> longs = new ArrayList<>();
        clueInfoQueryRespDTOS.forEach(ci -> {
            //转义
            ci.setClueStatusCn(FaultClueStateEnum.getNameByCode(ci.getClueStatus()));
            ci.setFollowStatusCn(FaultFollowStateEnum.getNameByCode(ci.getFollowStatus()));
            ci.setWheResCn(FaultClueWheResEnum.getWherEsMsg(ci.getWheRes()));
            try {
                //邀约时间为空
                if (ObjectUtils.isEmpty(ci.getInviteTime())) {
                    Long equationTime = DateCalUtil.calculateTime(ci.getClueDisTime(), new Date());
                    //判断是否超过两小时
                    if (equationTime >= CommonConstants.ONE_HUNDRED_TWENTY) {
                        ci.setInviteOvertime(StateEscapeEnum.STATE_TWO.getCode());
                        longs.add(ci.getId());
                    }
                }
            } catch (Exception e) {
                log.error("线索高亮处理异常", e);
            }
        });
        if (longs.size() > 0) {
            //修改超时标识
            ttFaultLightClueMapper.updateInviteOvertime(longs);
        }
        if (CommonUtils.isNullOrEmpty(clueInfoQueryRespDTOS)) {
            return page.setRecords(new ArrayList<>());
        }
        return page.setRecords(clueInfoQueryRespDTOS);
    }

    /**
     * 故障灯线索数据下载-店端
     *
     * @param dto      ClueInfoQueryRequestDTO
     * @param response HttpServletResponse
     */
    @Override
    public void clueInfoDerive(ClueInfoQueryRequestDTO dto, HttpServletResponse response) {

        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //获取登录后的经销商CODE
        if (loginInfoDto == null || StringUtils.isBlank(loginInfoDto.getOwnerCode())) {
            throw new ServiceBizException("获取当前账户的信息为空,请重新登录!!!");
        } else {
            List<String> stringList = new ArrayList<>();
            stringList.add(loginInfoDto.getOwnerCode());
            dto.setDealerCode(stringList);
        }

        List<ClueInfoQueryRespDTO> clueInfoQueryRespDTOS =
                ttFaultLightClueMapper.queryClueInfoList(null, dto);

        log.info("故障灯线索店端下载数据---:{}", JSONObject.toJSONString(clueInfoQueryRespDTOS));
        //转义
        stateEscape(clueInfoQueryRespDTOS);
        List<ClueInfoDeriveDTO> clueInfoDeriveDTOS = ClazzConverter.converterClass(clueInfoQueryRespDTOS, ClueInfoDeriveDTO.class);

        ExcelUtils.export(response, "线索集合数据", clueInfoDeriveDTOS, ClueInfoDeriveDTO.class);
    }

    public void stateEscape(List<ClueInfoQueryRespDTO> clueInfoQueryRespDTOS) {
        ClueInfoQueryRespDTO respDto;
        for (int i = 0; i < clueInfoQueryRespDTOS.size(); i++) {
            respDto = clueInfoQueryRespDTOS.get(i);
            respDto.setNumber(i + 1);
            //转义
            if (respDto.getClueStatus() != null) {
                respDto.setClueStatusCn(FaultClueStateEnum.getNameByCode(respDto.getClueStatus()));
            }
            if (respDto.getFollowStatus() != null) {
                respDto.setFollowStatusCn(FaultFollowStateEnum.getNameByCode(respDto.getFollowStatus()));
            }
            if (respDto.getWheRes() != null) {
                respDto.setWheResCn(FaultClueWheResEnum.getWherEsMsg(respDto.getWheRes()));
            }
            respDto.setIsDlr(StateEscapeEnum.getStateMsg(respDto.getIsDlr()));
            respDto.setIntoOnTime(StateEscapeEnum.getStateMsg(respDto.getIntoOnTime()));
            respDto.setNoInto(StateEscapeEnum.getStateMsg(respDto.getNoInto()));
            respDto.setSelfInto(StateEscapeEnum.getStateMsg(respDto.getSelfInto()));
            respDto.setLightsUp(StateEscapeEnum.getStateMsg(respDto.getLightsUp()));
            respDto.setMissParts(StateEscapeEnum.getStateMsg(respDto.getMissParts()));
            if (org.apache.commons.lang3.StringUtils.isNotBlank(respDto.getNoRepair())) {
                respDto.setNoRepair(respDto.getNoRepair().equals("2") ? "是" : null);
            }
            respDto.setContactOvertime(StateEscapeEnum.getStateMsg(respDto.getContactOvertime()));
            respDto.setInviteOvertime(StateEscapeEnum.getStateMsg(respDto.getInviteOvertime()));
            if (org.apache.commons.lang3.StringUtils.isNotBlank(respDto.getInviteResult())) {
                respDto.setInviteResult(respDto.getInviteResult().equals("1") ? "预约失败" : clueInfoQueryRespDTOS.get(i).getInviteResult().equals("2") ? "预约成功" : null);
            }
            if (ObjectUtils.isNotEmpty(respDto.getContactResult())) {
                respDto.setContactResult(CallStatusEnum.getNameByCode(respDto.getContactResult()));
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(respDto.getTroubleCode())) {
                respDto.setTroubleCode("0".equals(respDto.getTroubleCode()) ? "否" : "是");
            }
        }
    }

    /**
     * 故障灯线索集合查询-厂端
     *
     * @param page Page
     * @param dto  ClueInfoQueryRequestDTO
     * @return ClueInfoQueryRespDTO
     */
    @Override
    public IPage<ClueInfoQueryRespDTO> factoryQueryClueInfoList(Page<ClueInfoQueryRespDTO> page, ClueInfoQueryRequestDTO dto) {

        List<ClueInfoQueryRespDTO> clueInfoQueryRespDTOS =
                ttFaultLightClueMapper.queryClueInfoList(page, dto);

        if (CommonUtils.isNullOrEmpty(clueInfoQueryRespDTOS)) {
            return page.setRecords(new ArrayList<>());
        }
        clueInfoQueryRespDTOS.forEach(ci -> {
            ci.setClueStatusCn(FaultClueStateEnum.getNameByCode(ci.getClueStatus()));
            ci.setFollowStatusCn(FaultFollowStateEnum.getNameByCode(ci.getFollowStatus()));
            ci.setWheResCn(FaultClueWheResEnum.getWherEsMsg(ci.getWheRes()));
        });
        return page.setRecords(clueInfoQueryRespDTOS);
    }

    /**
     * 故障灯线索数据下载-厂端
     *
     * @param dto      ClueInfoQueryRequestDTO
     * @param response HttpServletResponse
     */
    @Override
    public void factoryClueInfoDerive(ClueInfoQueryRequestDTO dto, HttpServletResponse response) {

        List<ClueInfoQueryRespDTO> clueInfoQueryRespDTOS =
                ttFaultLightClueMapper.queryClueInfoList(null, dto);
        //转义
        stateEscape(clueInfoQueryRespDTOS);
        ExcelUtils.export(response, "厂端-线索集合数据", clueInfoQueryRespDTOS, ClueInfoQueryRespDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateClueInfo(ClueInfoQueryRequestDTO clueInfoQueryRequestDTO) {
        log.info("updateClueInfo,start:{}", JSON.toJSONString(clueInfoQueryRequestDTO));
        TtFaultLightCluePO ttFaultLightCluePO =
                ClazzConverter.converterClass(clueInfoQueryRequestDTO, TtFaultLightCluePO.class);
        if (ttFaultLightCluePO == null) {
            throw new ServiceBizException("操作失败，请稍后再试！！！");
        }
        ttFaultLightCluePO.setClueStatus(FaultClueStateEnum.ALREADY_COMPLETED.getCode());
        ttFaultLightCluePO.setFollowStatus(FaultFollowStateEnum.ALREADY_COMPLETED.getCode());
        ttFaultLightCluePO.setWheRes(clueInfoQueryRequestDTO.getWheRes());
        /**线索完成时间*/
        ttFaultLightCluePO.setClueComTime(new Date());
        //修改线索表状态
        ttFaultLightClueMapper.updateClueInfo(FaultClueStateEnum.WAITING_FOR_CONFIRMATION.getCode(), ttFaultLightCluePO);
        //查询线索
        TtFaultLightCluePO cluePO = ttFaultLightClueMapper.selectById(clueInfoQueryRequestDTO.getId());
        //添加跟进记录
        TtFaultLightFollowRecordPO po = faultLightService.converClueToRecord(cluePO);
        ttFaultLightFollowRecordMapper.insert(po);
        //MQ消息推送
        log.info("updateClueInfo,故障灯关联工单MQ消息推送");
        faultLightService.pushMessage(cluePO.getIcmId(),
                FaultFollowStateEnum.ALREADY_COMPLETED.getCode().toString(),
                FaultClueStateEnum.ALREADY_COMPLETED.getCode().toString(),
                new Date());
        log.info("updateClueInfo,end");
    }

    /**
     * 线索录入补偿
     *
     * @param dto ClueDataSynchroDTO
     * @return int
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addClueCompensate(ClueDataSynchroDTO dto) {

        //判断库里是否存在数据
        TtFaultLightInfoPO ttFaultLightInfoPO = ttFaultLightInfoMapper.selectById(dto.getId());

        Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(dto), Map.class);

        if (dto.getWarningInfo() != null) {
            map.putAll(JSONObject.parseObject(JSONObject.toJSONString(dto.getWarningInfo()), Map.class));
        }
        if (dto.getContactInfo() != null) {
            map.putAll(JSONObject.parseObject(JSONObject.toJSONString(dto.getContactInfo()), Map.class));
        }
        if (dto.getCallInfo() != null) {
            map.putAll(JSONObject.parseObject(JSONObject.toJSONString(dto.getCallInfo()), Map.class));
        }
        //对象重组
        TtFaultLightInfoPO ttFaultLightInfoPO1 =
                ClazzConverter.converterClass(map, TtFaultLightInfoPO.class);
        //新增数据
        if (ttFaultLightInfoPO == null) {
            return ttFaultLightInfoMapper.insert(ttFaultLightInfoPO1);
        } else {
            //修改
            return ttFaultLightInfoMapper.updateById(ttFaultLightInfoPO1);
        }
    }

    @Override
    public void updateHighlightFlag() {
        log.info("updateHighlightFlag start");
        //用ID做防重复点击
        IDistributedLock lock = DistributedLockUtil.getDistributedLock(
                CacheKeyEnum.FAULT_LIGHT_UPDATE_KEY.getKey() + CacheKeyEnum.updateHighlightFlag.getKey());
        if (!lock.antiDuplication()) {
            log.info("doFollowSub,!lock.antiDuplication()");
            return;
        }
        //获取总条数
        Integer total = cdpTagTaskService.queryFaultLightClueTotal();
        Page page = new Page();
        page.setSize(CommonConstants.FAULT_LIGHT_SIZE);
        page.setTotal(total);
        Integer pages = Integer.valueOf(String.valueOf(page.getPages()));
        Integer nThreads = Utills.getThreadSize(total);
        ExecutorService executorService = null;
        log.info("updateHighlightFlag,total:{},size:{},pages:{}", total, CommonConstants.FAULT_LIGHT_SIZE, pages);
        if (nThreads > 1) {
            final CountDownLatch latch = new CountDownLatch(pages);
            executorService = Executors.newFixedThreadPool(nThreads);
            for (int i = 0; i < pages; i++) {
                page.setCurrent(i);
                if (null != executorService) {
                    executorService.submit(() -> {
                        try {
                            this.queryFaultLightClueId(page);
                        } finally {
                            latch.countDown();
                        }
                    });
                }
            }
            try {
                latch.await();
            } catch (Exception e) {
                log.error("updateHighlightFlag:{}", "updateHighlightFlagError", e);
            }
        } else {
            for (int i = 0; i < pages; i++) {
                page.setCurrent(i);
                this.queryFaultLightClueId(page);
            }
        }
        if (null != executorService) {
            executorService.shutdown();
        }
        log.info("故障灯-更新高亮运行结束释放锁");
        lock.release();
        log.info("updateHighlightFlag end");
    }

    public void queryFaultLightClueId(Page page) {
        List<Long> clueId = new ArrayList<>();
        List<Long> cdpTagTaskId = new ArrayList<>();
        List<CdpTagTaskPo> cdpTagTaskPos = new ArrayList<>();
        try {
            /*查询二次预约超时数据*/
            cdpTagTaskPos = cdpTagTaskService.queryFaultLightClueId(page);
            log.info("updateHighlightFlag,cdpTagTaskPos:{}", JSONObject.toJSONString(cdpTagTaskPos));
            cdpTagTaskPos.forEach(j -> {
                clueId.add(Long.valueOf(j.getBizNo()));
                cdpTagTaskId.add(j.getId());
            });
            this.updateClueAndCdpTagTask(clueId, cdpTagTaskId);
        } catch (Exception e) {
            log.error("更新线索高亮失败:", e);
            String errorMessage = e.getMessage().substring(0, 200);
            cdpTagTaskPos.forEach(i -> {
                i.setErrorMessage(errorMessage);
                if (Objects.equals(i.getRetryCount(), CommonConstants.FAULT_LIGHT_RETRY_COUNT_TWO)) {
                    i.setRetryCount(CommonConstants.FAULT_LIGHT_RETRY_COUNT_THREE);
                    i.setTaskStatus(CommonConstants.FAULT_LIGHT_TASK_STATUS_MAX);
                } else {
                    i.setRetryCount(1 + i.getRetryCount());
                    i.setTaskStatus(CommonConstants.FAULT_LIGHT_TASK_STATUS_CLOSE);
                }
                i.setLastRetryTime(new Date());
            });
            cdpTagTaskService.updateErrorTask(cdpTagTaskPos);
        }

    }

    @Transactional
    public void updateClueAndCdpTagTask(List<Long> clueId, List<Long> cdpTagTaskId) {
        log.info("updateClueAndCdpTagTask,clueId:{},cdpTagTaskId:{}", clueId, cdpTagTaskId);
        if (CollectionUtils.isNotEmpty(clueId)) {
            updateHighlights(clueId);
        }
        if (CollectionUtils.isNotEmpty(cdpTagTaskId)) {
            cdpTagTaskService.updateFaultLightClueById(cdpTagTaskId);
        }
    }

    @Override
    public void updateHighlightFlagByClueDisTime() {
        ttFaultLightClueMapper.updateHighlightFlagByClueDisTime(FrameworkUtil.getLoginInfo().getOwnerCode());
    }

    @Override
    public CustomerInfoDto queryFaultLight(String vin, String ownerCode) {
        CustomerInfoDto customerInfoDto = new CustomerInfoDto();
        log.info("queryFaultLight vin:{},ownerCode:{}", vin, ownerCode);
        LambdaQueryWrapper<TtFaultLightCluePO> queryWrapper = new LambdaQueryWrapper<>();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(ownerCode)) {
            queryWrapper.eq(TtFaultLightCluePO::getDealerCode, ownerCode);
        }
        queryWrapper.eq(TtFaultLightCluePO::getVin, vin)
                .in(TtFaultLightCluePO::getClueStatus, FaultClueStateEnum.WAITING_FOR_APPOINTMENT.getCode(), FaultClueStateEnum.WAITING_ENTER_STORE.getCode(), FaultClueStateEnum.ENTERED_STORE.getCode())
                .orderByDesc(TtFaultLightCluePO::getId)
                .last("limit 1");
        TtFaultLightCluePO ttFaultLightCluePO = ttFaultLightClueMapper.selectOne(queryWrapper);
        if (Objects.isNull(ttFaultLightCluePO)){
            log.info("queryFaultLight ttFaultLightCluePO 查询为null");
            return null;
        }
        TtFaultLightInvitationPO ttFaultLightInvitationPO = ttFaultLightInvitationMapper.queryFaultLightInvitationByClurId(ttFaultLightCluePO.getId());
        if (Objects.isNull(ttFaultLightInvitationPO)){
            log.info("queryFaultLight ttFaultLightInvitationPO 查询为null");
            return null;
        }
        customerInfoDto.setMobile(ttFaultLightInvitationPO.getCusPhone());
        customerInfoDto.setCustomerName(ttFaultLightInvitationPO.getCusName());
        log.info("customerInfoDto :{}",customerInfoDto);
        return customerInfoDto;
    }

    @Override
    public void faultLightConsumerStatus(String t) {
        log.info("faultLightConsumerStatus-onMessage,t:{}", JSONObject.toJSONString(t));
        FaultLightTopicDTO faultLightTopicDTO =
                JSONObject.parseObject(t,FaultLightTopicDTO.class);
        log.info("onMessage,faultLightTopicDTO:{}",JSONObject.toJSONString(faultLightTopicDTO));
        //MQ消息录入
        try{
            TtFaultLightMqRecordPO ttFaultLightMqRecordPO = new TtFaultLightMqRecordPO();
            ttFaultLightMqRecordPO.setBizStatus(faultLightTopicDTO.getBizStatus());
            ttFaultLightMqRecordPO.setIcmId(faultLightTopicDTO.getId());
            ttFaultLightMqRecordPO.setUpdateTime(faultLightTopicDTO.getUpdateTime());
            ttFaultLightMqRecordPO.setFollowUpStatus(faultLightTopicDTO.getFollowUpStatus());
            ttFaultLightMqRecordMapper.insert(ttFaultLightMqRecordPO);
        }catch (Exception e){
            log.error("onMessage,MQ消息录入异常");
        }
        if(faultLightTopicDTO != null){
            Integer clueStatus = Integer.parseInt(faultLightTopicDTO.getBizStatus());
            Date updateTime = faultLightTopicDTO.getUpdateTime();
            TtFaultLightCluePO faultLightCluePO = ttFaultLightClueMapper.selectCountByIcmId(faultLightTopicDTO.getId());
            if(ObjectUtils.isEmpty(faultLightCluePO)){
                log.info("onMessage,ObjectUtils.isEmpty(faultLightCluePO)");
                return;
            }
            Integer afClueStatus = faultLightCluePO.getClueStatus();
            if(FaultClueStateEnum.CLOSE_CLUES.getCode().equals(afClueStatus)){
                log.info("onMessage,FaultClueStateEnum.CLOSE_CLUES.getCode().equals(afClueStatus)");
                return;
            }
            if(FaultClueStateEnum.CLOSE_CLUES.getCode().equals(clueStatus)){
                faultLightCluePO.setClueCloTime(updateTime == null ? new Date() : updateTime);
            }
            //跟新线索状态
            faultLightCluePO.setClueStatus(clueStatus);
            faultLightCluePO.setFollowStatus(Integer.parseInt(faultLightTopicDTO.getFollowUpStatus()));
            faultLightCluePO.setUpdatedAt(faultLightTopicDTO.getUpdateTime());
            faultLightCluePO.setAfClueStatus(afClueStatus);
            int num = ttFaultLightClueMapper.updateFaultLightClueById(faultLightCluePO);
            log.info("onMessage,num:{}", num);
            if (num == 0) {
                log.info("onMessage,num == 0,end");
                return;
            }
            //新增跟进记录
            TtFaultLightFollowRecordPO lightFollowRecordPO = faultLightService.converClueToRecord(faultLightCluePO);
            num += ttFaultLightFollowRecordMapper.insert(lightFollowRecordPO);
            log.info("onMessage,end:{}", num);
        }
    }

    public void updateHighlights(List<Long> secondAppointmentId) {
        //修改高亮标识
        ttFaultLightClueMapper.updateInviteOvertime(secondAppointmentId);
    }

    @Override
    public void unHighlight(CallDetailsDTO callDetailsDTO) {
        //查询线索id
        List<Long> ids = ttFaultLightClueMapper.queryIdByCallId(callDetailsDTO.getCallId());
        if (CollectionUtils.isNotEmpty(ids)) {
            cancelHighlights(ids);
        }
    }

    public void cancelHighlights(List<Long> ids) {
        //修改高亮标识
        ttFaultLightClueMapper.cancelHighlights(ids);
    }

    @Override
    public void queryHighlightFlag(String createDate, String endDate) {
        log.info("queryHighlightFlag,start");
        //做防重复点击
        IDistributedLock lock = DistributedLockUtil.getDistributedLock(
                CacheKeyEnum.FAULT_LIGHT_SYNC_KEY.getKey() + CacheKeyEnum.queryHighlightFlag.getKey());
        if (!lock.antiDuplication()) {
            log.info("doFollowSub,!lock.antiDuplication()");
            return;
        }
        /**传入时间为空时取当天*/
        if (StringUtils.isNullOrEmpty(createDate)) {
            createDate = DateUtils.queryBeginTime(createDate);
        }
        if (StringUtils.isNullOrEmpty(endDate)) {
            endDate = DateUtils.queryBeginTime(endDate);
        }
        log.info("queryHighlightFlag,createDate:{},endDate:{}", createDate, endDate);
        //获取总条数
        Integer total = ttFaultLightClueMapper.queryTotal(createDate, endDate);
        Page page = new Page();
        page.setSize(CommonConstants.FAULT_LIGHT_SIZE);
        page.setTotal(total);
        Integer pages = Integer.valueOf(String.valueOf(page.getPages()));
        log.info("queryHighlightFlag,total:{},size:{},current:{}", total, CommonConstants.FAULT_LIGHT_SIZE, pages);
        Integer nThreads = Utills.getThreadSize(total);
        log.info("queryHighlightFlag,nThreads:{}", nThreads);
        ExecutorService executorService = null;
        if (nThreads > 1) {
            final CountDownLatch latch = new CountDownLatch(pages);
            executorService = Executors.newFixedThreadPool(nThreads);
            for (int i = 0; i < pages; i++) {
                page.setCurrent(i);
                if (null != executorService) {
                    String finalEndDate = endDate;
                    String finalCreateDate = createDate;
                    executorService.submit(() -> {
                        try {
                            this.querySecondAppointmentId(page, finalCreateDate, finalEndDate);
                        } finally {
                            latch.countDown();
                        }
                    });
                }
            }
            try {
                latch.await();
            } catch (Exception e) {
                log.error("queryHighlightFlag:{}", e);
            }
        } else {
            for (int i = 0; i < pages; i++) {
                page.setCurrent(i);
                this.querySecondAppointmentId(page, createDate, endDate);
            }
        }
        if (null != executorService) {
            executorService.shutdown();
        }
        if (lock != null) {
            log.info("故障灯-获取高亮同步任务运行结束释放锁");
            lock.release();
        }
        log.info("queryHighlightFlag,end");
    }

    public void querySecondAppointmentId(Page page, String createDate, String endDate) {
        /*查询二次预约超时数据*/
        List<Long> secondAppointmentId = ttFaultLightClueMapper.querySecondAppointment(page, createDate, endDate);
        log.info("queryHighlightFlag,secondAppointmentId:{}", secondAppointmentId);
        batchInsertHighlightFlagClueId(secondAppointmentId);
    }

    @Transactional
    public void batchInsertHighlightFlagClueId(List<Long> secondAppointmentId) {
        log.info("batchInsertHighlightFlagClueId,secondAppointmentId:{}", secondAppointmentId);
        cdpTagTaskService.batchInsertHighlightFlagClueId(secondAppointmentId);
    }

    private int addClueDataSynchro(ClueDataSynchroDTO dto) throws Exception {
        log.info("doClueDataSynchro,start,dto:{}", JSON.toJSONString(dto));
        int num = 0;
        Long icmId = dto.getId();
        Date date = new Date();
        //查询
        TtFaultLightCluePO cluePO = ttFaultLightClueMapper.selectCountByIcmId(icmId);
        //待预约标识
        boolean flag = false;
        //400外呼备注
        String comments = null;
        TtFaultLightFollowRecordPO lightFollowRecordPO;
        log.info("doClueDataSynchro,cluePO:{}", JSON.toJSONString(cluePO));
        if (cluePO == null) { //新增
            log.info("doClueDataSynchro,add");
            TtFaultLightCluePO po = TtFaultLightCluePO.builder()
                    .icmId(icmId)
                    .vin(dto.getVehicleVin())
                    .clueStatus(FaultClueStateEnum.WAITING_FOR_CONTACT.getCode())
                    .followStatus(FaultFollowStateEnum.WAITING_FOR_CONTACT.getCode())
                    .clueGenTime(DateUtils.parse(dto.getLeadsReceiveTime(), DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS))
                    .build();
            /*故障报警警信息*/
            WarningInfoDTO warningInfo = dto.getWarningInfo();
            String warningCityId = warningInfo.getWarningCityId();
            String warningTime = warningInfo.getWarningTime();
            String warningId = warningInfo.getWarningId();
            if (warningInfo != null) {
                log.info("doClueDataSynchro,warningInfo != null");
                po.setFaultId(warningId == null ? null : Long.valueOf(warningId));
                po.setAlarmTime(warningTime == null ? null : DateUtils.parse(warningTime, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
                po.setFaultCityName(warningInfo.getWarningCityCN());
                po.setFaultCityId(warningCityId == null ? null : Long.valueOf(warningCityId));
            }
            log.info("doClueDataSynchro,add,po:{},{}", JSON.toJSONString(po), new Date().getTime());
            num = ttFaultLightClueMapper.insert(po);
            log.info("doClueDataSynchro,add,id:{}", num);
        } else if (FaultClueStateEnum.WAITING_FOR_CONTACT.getCode().equals(cluePO.getClueStatus())) { //更新
            log.info("doClueDataSynchro,alter");
            String dealerCode = dto.getDealerCode();
            /*客户信息*/
            ContactInfoDTO contactInfo = dto.getContactInfo();
            /*外呼信息*/
            CallInfoDTO callInfo = dto.getCallInfo();
            /*经销商信息*/
            CompanyDetailDTO comDTO = null;
            /*线索下发时间*/
            //new Date();
            /*线索状态*/
            Integer clueStatus;
            /*跟进状态*/
            Integer followStatus;
            /*是否为推荐dlr*/
            int isDlr = 0;
            /*是否推荐经销商*/
            Boolean recommendDealerFlag = dto.getRecommendDealerFlag();
            if (recommendDealerFlag == Boolean.TRUE) {
                isDlr = 2;
            } else if (recommendDealerFlag == Boolean.FALSE) {
                isDlr = 1;
            }
            if (!StringUtils.isNullOrEmpty(dealerCode)) {
                log.info("doClueDataSynchro,联系成功");
                //联系成功
                clueStatus = FaultClueStateEnum.WAITING_FOR_APPOINTMENT.getCode();
                followStatus = FaultFollowStateEnum.WAITING_FOR_APPOINTMENT.getCode();
                //查询经销商对于地区
                List<CompanyDetailDTO> listCode = clueMigrateTaskService.getCompanyInfoListByDealerCodeList(
                        Stream.of(dealerCode).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(listCode)) {
                    comDTO = listCode.get(0);
                    log.info("doClueDataSynchro,comDTO:{}", JSON.toJSONString(comDTO));
                }
                //待预约标识
                flag = true;
            } else {
                log.info("doClueDataSynchro,联系失败");
                //联系失败
                clueStatus = FaultClueStateEnum.CLOSE_CLUES.getCode();
                followStatus = FaultFollowStateEnum.CONTACT_FAILURE.getCode();
            }
            TtFaultLightCluePO po = getTtFaultLightCluePO(dto, date, cluePO, dealerCode, clueStatus, followStatus);
            assignmentPo(comDTO, po);
            log.info("doClueDataSynchro,po:{}", JSON.toJSONString(po));
            num = ttFaultLightClueMapper.updateById(po);
            log.info("doClueDataSynchro,num:{}", num);
            if (num > 0) {
                long clueId = cluePO.getId();
                TtFaultLightInvitationPO ionPO = TtFaultLightInvitationPO.builder()
                        .clueId(clueId)
                        .dlr(dealerCode)
                        .isDlr(isDlr)
                        .clueDisTime(date)
                        .build();
                if (callInfo != null) {
                    comments = getComments(cluePO, callInfo, ionPO);
                }
                if (contactInfo != null) {
                    log.info("doClueDataSynchro,contactInfo != null");
                    ionPO.setCusName(contactInfo.getCustomerName());
                    ionPO.setCusPhone(contactInfo.getCustomerMobile());
                    ionPO.setGender(contactInfo.getGender());
                }
                log.info("doClueDataSynchro,ionPO:{}", JSON.toJSONString(ionPO));
                //查询数据是add还是update
                Long count = ttFaultLightInvitationMapper.selectIdByClueId(clueId);
                if (count != null && count > 0) {
                    ttFaultLightInvitationMapper.updateByClueId(ionPO);
                } else {
                    num = ttFaultLightInvitationMapper.insert(ionPO);
                }
                log.info("doClueDataSynchro,num:{}", num);
            }
        }
        if (num > 0) {
            log.info("doClueDataSynchro,新增跟进记录");
            cluePO = ttFaultLightClueMapper.selectCountByIcmId(icmId);
            lightFollowRecordPO = ClazzConverter.converterClass(cluePO, TtFaultLightFollowRecordPO.class);
            if (lightFollowRecordPO != null) {
                lightFollowRecordPO.setClueId(cluePO.getId());
                lightFollowRecordPO.setFollowName("系统");
                lightFollowRecordPO.setFollowTime(date);
            }
            ttFaultLightFollowRecordMapper.batchInsert(Lists.newArrayList(lightFollowRecordPO));
        }
        log.info("doClueDataSynchro,flag:{}", flag);
        //待预约MQ发送,邮件短信通知
        doMess(dto, date, cluePO, flag, comments);
        log.info("doClueDataSynchro,end:{}", num);
        return num;
    }

    private void doMess(ClueDataSynchroDTO dto, Date date, TtFaultLightCluePO cluePO, boolean flag, String comments) {
        if (flag) {
            log.info("doMess,邮件短信通知");
            ClueNotifyDTO notifyDto = ClueNotifyDTO.builder()
                    .dealerCode(dto.getDealerCode())
                    .vehicleVin(cluePO.getVin())
                    .faultCityName(cluePO.getFaultCityName())
                    .clueGenTimeDate(cluePO.getClueGenTime())
                    .warningName(cluePO.getWarningName())
                    .clueDisTimeDate(date)
                    .comments(comments)
                    .build();
            faultLightClueServiceHelper.doClueNotify(notifyDto);
            log.info("doMess,待预约MQ发送");
            //MQ状态同步
            faultLightService.pushMessage(cluePO.getIcmId(),
                    String.valueOf(FaultFollowStateEnum.WAITING_FOR_APPOINTMENT.getCode()),
                    String.valueOf(FaultClueStateEnum.WAITING_FOR_APPOINTMENT.getCode()),
                    date);
        }
    }
}
