package com.yonyou.dmscus.customer.service.complaint;

    import com.baomidou.mybatisplus.core.metadata.IPage;
    import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailTemplateDTO;

    import java.util.List;


/**
 * <p>
 * 客户投诉发送邮件模板 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
public interface ComplaintSendEmailTemplateService {
    /**
     * 分页查询
     * @param page
     * @param complaintSendEmailTemplateDTO
     * @return
     */
      IPage<ComplaintSendEmailTemplateDTO> selectPageBysql(Page page, ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO);

    /**
     * 集合查询
     * @param complaintSendEmailTemplateDTO
     * @return
     */
      List<ComplaintSendEmailTemplateDTO> selectListBySql(ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO);

    /**
     * 通过id查询
     * @param id
     * @return
     */
    ComplaintSendEmailTemplateDTO getById(Long id);

    /**
     * 新增
     * @param complaintSendEmailTemplateDTO
     * @return
     */
    int insert(ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO);

    /**
     * 更新
     * @param id
     * @param complaintSendEmailTemplateDTO
     * @return
     */
    int update(Long id, ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO);

    /**
     * 通过id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 新增模板
     * @param complaintSendEmailTemplateDTO
     * @return
     */
    int insertEmailTemplate(ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO);

    /**
     * 查看邮件模板
     * @param userId
     * @return
     */
    ComplaintSendEmailTemplateDTO selectLastTemplate(long userId);
}
