package com.yonyou.dmscus.customer.service.impl.faultLight;

import com.yonyou.dmscus.customer.dao.faultLight.TtFaultLightInvitationMapper;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightInvitationPO;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightInvitationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class FaultLightInvitationServiceImpl implements FaultLightInvitationService {
    @Resource
    private TtFaultLightInvitationMapper ttFaultLightInvitationMapper;
    @Override
    public TtFaultLightInvitationPO queryFaultLightInvitationByClurId(Long id) {
        return ttFaultLightInvitationMapper.queryFaultLightInvitationByClurId(id);
    }
}
