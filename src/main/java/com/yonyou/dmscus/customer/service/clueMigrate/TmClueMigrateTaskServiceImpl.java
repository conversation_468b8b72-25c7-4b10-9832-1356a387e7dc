package com.yonyou.dmscus.customer.service.clueMigrate;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.service.excel.ExcelExportColumn;
import com.yonyou.dmscloud.framework.service.excel.ExcelGenerator;
import com.yonyou.dmscloud.framework.service.excel.ExcelRead;
import com.yonyou.dmscloud.framework.service.excel.impl.AbstractExcelReadCallBack;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.domains.dto.ImportResultDto;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.constants.ClueMigrateStatusEnum;
import com.yonyou.dmscus.customer.constants.ClueMigrateTypeEnum;
import com.yonyou.dmscus.customer.constants.DealerServiceStatusEnum;
import com.yonyou.dmscus.customer.constants.MigratedCdpEnum;
import com.yonyou.dmscus.customer.dao.clueMigrate.TmClueMigrateTaskMapper;
import com.yonyou.dmscus.customer.dao.dealermigrationrecord.DealerMigrationRecordMapper;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.dto.clueMigrate.*;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.CompanyDetailDTO;
import com.yonyou.dmscus.customer.entity.po.clueMigrate.TmClueMigrateTask;
import com.yonyou.dmscus.customer.entity.po.dealermigrationrecord.btnlog.DealerMigrationRecordPo;
import com.yonyou.dmscus.customer.entity.po.remote.TmCompany;
import com.yonyou.dmscus.customer.feign.CompanyClient;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.feign.VehicleInfoClient;
import com.yonyou.dmscus.customer.middleInterface.ResponseDTO;
import com.yonyou.dmscus.customer.utils.FunctionalEnumHelper;
import com.yonyou.dmscus.customer.vo.clueMigrate.ClueMigrateTaskVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yonyou.dmscus.customer.constants.CommonConstants.NUM_1;
import static com.yonyou.dmscus.customer.constants.CommonConstants.NUM_2;

/**
 * <p>
 * 线索迁移任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Service
public class TmClueMigrateTaskServiceImpl extends ServiceImpl<TmClueMigrateTaskMapper, TmClueMigrateTask> implements ITmClueMigrateTaskService {
    private static final Logger logger = LoggerFactory.getLogger(TmClueMigrateTaskServiceImpl.class);

    @Autowired
    private ExcelGenerator excelGenerator;

    @Autowired
    private ExcelRead<DealerToDealerTaskImportDTO> dealerToDealerTaskImportDTOExcelRead;

    @Autowired
    private ExcelRead<VinToDealerTaskImportDTO> vinToDealerTaskImportDTOExcelRead;
    @Autowired
    private ReportCommonClient reportCommonClient;

    @Autowired
    private VehicleInfoClient vehicleInfoClient;

    @Autowired
    private CompanyClient companyClient;

    @Resource
    private RestTemplate directRestTemplate;

    @Autowired
    private MidUrlProperties midUrlProperties;

    @Autowired
    private DealerMigrationRecordMapper dealerMigrationRecordMapper;



    @Transactional
    @Override
    public void create(ClueMigrateTaskAddDTO clueMigrateTaskAddDTO) {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        TmClueMigrateTask clueMigrateTask = new TmClueMigrateTask();
        clueMigrateTask.setSOwnerCode( clueMigrateTaskAddDTO.getSourceOwnerCode() );
        clueMigrateTask.setOwnerCode( clueMigrateTaskAddDTO.getOwnerCode() );
        clueMigrateTask.setSyncType( clueMigrateTaskAddDTO.getSyncType() );
        clueMigrateTask.setStartTime( LocalDateTime.now() );
        clueMigrateTask.setCreatedAt( LocalDateTime.now() );
        clueMigrateTask.setUpdatedAt( LocalDateTime.now() );
        clueMigrateTask.setVin(clueMigrateTaskAddDTO.getVin());
        clueMigrateTask.setOwnerParCode(clueMigrateTaskAddDTO.getOwnerParCode());
        clueMigrateTask.setSyncStatus(ClueMigrateStatusEnum.NOT_BEGIN.getCode());
        clueMigrateTask.setCreatedBy(loginInfoDto.getUserName());
        clueMigrateTask.setMigratedCdp(clueMigrateTaskAddDTO.getMigratedCdp());
        logger.info("TmClueMigrateTask：{}",loginInfoDto.getUserName());
        Map<String,CompanyDetailDTO>  map = this.getMapCompany();
        CompanyDetailDTO dto=   map.get(clueMigrateTaskAddDTO.getOwnerCode());
        if(dto!=null ){
            clueMigrateTask.setOwnerParCode(dto.getCompanyNameCn());
        }
        save( clueMigrateTask );
    }

    @Override
    public void edit(ClueMigrateTaskAddDTO clueMigrateTaskAddDTO) {

    }

    @Override
    public ClueMigrateTaskVO detail() {
        return null;
    }


    @Transactional
    @Override
    public void createDealerToDealerTask(ClueMigrateTaskAddDTO clueMigrateTaskAddDTO, CompanyDetailDTO companyDetailDTO) {
        logger.info("createDealerToDealerTask clueMigrateTaskAddDTO:{},companyDetailDTO:{}",clueMigrateTaskAddDTO,companyDetailDTO);
        String ownerCode = clueMigrateTaskAddDTO.getOwnerCode();
        if (StringUtils.isEmpty( ownerCode )) {
            throw new DALException( "目标经销商为空" );
        }

        if ( companyDetailDTO == null ) {
            logger.error( "未查询到该经销商信息 {}",  ownerCode );
            throw new DALException( "未查询到该经销商信息" + ownerCode );
        }
        // 判断目标经销商是否停业
        if ( isStopService( companyDetailDTO ) ) {
            logger.error( "该经销商已终止营业 {}",  ownerCode );
            throw new DALException( "该经销商已终止营业" + ownerCode );
        }

        String sourceOwnerCode = clueMigrateTaskAddDTO.getSourceOwnerCode();
        if ( StringUtils.isEmpty( sourceOwnerCode ) ) {
            throw new DALException( "原经销商为空" );
        }
        if ( ownerCode.equals( sourceOwnerCode ) ) {
            throw new DALException( "原经销商与目标经销商相同" );
        }
        // 判断原经销商与目标经销商是否存在
        List<String> dealerCodeList = new ArrayList<>();
        dealerCodeList.add( ownerCode );
        dealerCodeList.add( sourceOwnerCode );
        List<TmCompany> companyList = companyClient.selectListByCodeList( dealerCodeList );
        if ( CollectionUtils.isEmpty( companyList ) ) {
            throw new DALException( "原经销商与目标经销商不存在" );
        }
        else {
            List<String> dealerCodeResultList = companyList.stream().map( TmCompany::getCompanyCode ).collect(Collectors.toList());
            if ( !dealerCodeResultList.contains( sourceOwnerCode ) ) {
                throw new DALException( "原经销商不存在" );
            }
            if ( !dealerCodeResultList.contains( ownerCode ) ) {
                throw new DALException( "目标经销商不存在" );
            }
        }

        clueMigrateTaskAddDTO.setSyncType(ClueMigrateTypeEnum.DEALER_TO_DEALER.getCode());
        QueryWrapper queryWrapper=    new QueryWrapper<>();
        queryWrapper.eq("s_owner_code",clueMigrateTaskAddDTO.getSourceOwnerCode());
        queryWrapper.eq("owner_code",clueMigrateTaskAddDTO.getOwnerCode());
        queryWrapper.eq("sync_type",clueMigrateTaskAddDTO.getSyncType());
       int count = baseMapper.selectCount(queryWrapper);

       clueMigrateTaskAddDTO.setSyncType(ClueMigrateTypeEnum.DEALER_TO_DEALER.getCode());
       QueryWrapper queryWrapper1 = new QueryWrapper<>();
       queryWrapper1.eq("s_owner_code", clueMigrateTaskAddDTO.getSourceOwnerCode());
       queryWrapper1.eq("sync_type", 1);
       int count1 = baseMapper.selectCount(queryWrapper1);

       if(count>0 || count1 > 0){
        throw new DALException("经销商迁移数据重复");
       } else{
           create( clueMigrateTaskAddDTO );
       }
        logger.info("createDealerToDealerTask,end");

    }

    private Map<String ,CompanyDetailDTO> getMapCompany() {
        List<CompanyDetailDTO>  list= reportCommonClient.queryCompanyInfo();
        Map map = new HashMap();
        if(list!=null && list.size()>0){
            for (CompanyDetailDTO dto:list) {
                map.put(dto.getCompanyCode(),dto);
            }
        }
        return  map;
    }

    @Transactional
    @Override
    public void createVinToDealerTask(ClueMigrateTaskAddDTO clueMigrateTaskAddDTO, CompanyDetailDTO companyDetailDTO) {

        String ownerCode = clueMigrateTaskAddDTO.getOwnerCode();
        if (StringUtils.isEmpty( ownerCode )) {
            throw new DALException( "目标经销商为空" );
        }

        if ( companyDetailDTO == null ) {
            logger.error( "未查询到该经销商信息 {}",  ownerCode );
            throw new DALException( "未查询到该经销商信息" + ownerCode );
        }
        // 判断目标经销商是否停业
        if ( isStopService( companyDetailDTO ) ) {
            logger.error( "该经销商已终止营业 {}",  ownerCode );
            throw new DALException( "该经销商已终止营业" + ownerCode );
        }

        String sourceOwnerCode = clueMigrateTaskAddDTO.getSourceOwnerCode();
        if ( StringUtils.isEmpty( sourceOwnerCode ) ) {
            throw new DALException( "原经销商为空" );
        }
        if ( ownerCode.equals( sourceOwnerCode ) ) {
            throw new DALException( "原经销商与目标经销商相同" );
        }
        // 判断原经销商与目标经销商是否存在
        List<String> dealerCodeList = new ArrayList<>();
        dealerCodeList.add( ownerCode );
        dealerCodeList.add( sourceOwnerCode );
        List<TmCompany> companyList = companyClient.selectListByCodeList( dealerCodeList );
        if ( CollectionUtils.isEmpty( companyList ) ) {
            throw new DALException( "原经销商与目标经销商不存在" );
        }
        else {
            List<String> dealerCodeResultList = companyList.stream().map( TmCompany::getCompanyCode ).collect(Collectors.toList());
            if ( !dealerCodeResultList.contains( sourceOwnerCode ) ) {
                throw new DALException( "原经销商不存在" );
            }
            if ( !dealerCodeResultList.contains( ownerCode ) ) {
                throw new DALException( "目标经销商不存在" );
            }
        }

        String vin = clueMigrateTaskAddDTO.getVin();
        if ( StringUtils.isEmpty( vin ) ) {
            throw new DALException( "vin为空" );
        }

        // VIN控制17位
        if ( vin.length() != 17 ) {
            throw new DALException( "vin非法" );
        }

        // 判断vin是否存在
        VehicleOwnerVO vehicleOwnerVO = vehicleInfoClient.getVehicleVOByVin( vin );
        if ( vehicleOwnerVO == null ) {
            throw new DALException( "vin不存在" );
        }

        clueMigrateTaskAddDTO.setSyncType( ClueMigrateTypeEnum.VIN_TO_DEALER.getCode() );
        QueryWrapper queryWrapper=    new QueryWrapper<>();
        queryWrapper.eq("s_owner_code",clueMigrateTaskAddDTO.getSourceOwnerCode());
        queryWrapper.eq("owner_code",clueMigrateTaskAddDTO.getOwnerCode());
        queryWrapper.eq("vin",clueMigrateTaskAddDTO.getVin());
        queryWrapper.eq("sync_type",clueMigrateTaskAddDTO.getSyncType());

        int count = baseMapper.selectCount(queryWrapper);
        if(count>0){
            throw new DALException("经销商迁移数据重复");
        }else {
            create(clueMigrateTaskAddDTO);
        }
    }

    @Override
    public IPage<ClueMigrateTaskVO> getDealerToDealerMigrateTaskVOPageList(ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO) {
        if ( clueMigrateTaskQueryDTO == null ) clueMigrateTaskQueryDTO = new ClueMigrateTaskQueryDTO();
        clueMigrateTaskQueryDTO.setSyncType( ClueMigrateTypeEnum.DEALER_TO_DEALER.getCode() );
        return pageList( clueMigrateTaskQueryDTO );
    }

    @Override
    public IPage<ClueMigrateTaskVO> getVinToDealerMigrateTaskVOPageList(ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO) {
        if ( clueMigrateTaskQueryDTO == null ) clueMigrateTaskQueryDTO = new ClueMigrateTaskQueryDTO();
        clueMigrateTaskQueryDTO.setSyncType( ClueMigrateTypeEnum.VIN_TO_DEALER.getCode() );
        return pageList( clueMigrateTaskQueryDTO );
    }

    @Override
    public IPage<ClueMigrateTaskVO> pageList(ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO) {
        Page<ClueMigrateTaskVO> page = new Page<>( clueMigrateTaskQueryDTO.getCurrentPage(), clueMigrateTaskQueryDTO.getPageSize() );
        List<ClueMigrateTaskVO> list = baseMapper.pageList( page, clueMigrateTaskQueryDTO );
        decorateClueMigrateTaskVOList( list );
        page.setRecords( list );
        return page;
    }

    @Override
    public List<ClueMigrateTaskVO> getDealerToDealerMigrateTaskVOList(ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO) {
        if ( clueMigrateTaskQueryDTO == null ) clueMigrateTaskQueryDTO = new ClueMigrateTaskQueryDTO();
        clueMigrateTaskQueryDTO.setSyncType( ClueMigrateTypeEnum.DEALER_TO_DEALER.getCode() );
        return getClueMigrateTaskVOList( clueMigrateTaskQueryDTO );
    }

    @Override
    public List<ClueMigrateTaskVO> getVinToDealerMigrateTaskVOList(ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO) {
        if ( clueMigrateTaskQueryDTO == null ) clueMigrateTaskQueryDTO = new ClueMigrateTaskQueryDTO();
        clueMigrateTaskQueryDTO.setSyncType( ClueMigrateTypeEnum.VIN_TO_DEALER.getCode() );

        return getClueMigrateTaskVOList( clueMigrateTaskQueryDTO );
    }

    private void decorateClueMigrateTaskVOList(List<ClueMigrateTaskVO> clueMigrateTaskVOList) {
        if ( CollectionUtils.isNotEmpty( clueMigrateTaskVOList ) ) {

            // 去重
            Set<String> ownerCodeSet = clueMigrateTaskVOList.stream().map( ClueMigrateTaskVO::getOwnerCode ).collect(Collectors.toSet());
            List<String> ownerCodeList = new ArrayList<>(ownerCodeSet);
            List<TmCompany> companyList = companyClient.selectListByCodeList( ownerCodeList );

            Map<String, TmCompany> companyMap = null;
            if ( CollectionUtils.isNotEmpty( companyList ) ) {
                companyMap = companyList.stream().collect( Collectors.toMap( TmCompany::getCompanyCode, Function.identity() , ( o, n ) -> n ) );
            }

            for ( ClueMigrateTaskVO clueMigrateTaskVO : clueMigrateTaskVOList ) {
                String ownerCode = clueMigrateTaskVO.getOwnerCode();

                clueMigrateTaskVO.setSyncStatusName( FunctionalEnumHelper.getEnum(ClueMigrateStatusEnum.class, ClueMigrateStatusEnum::getCode, clueMigrateTaskVO.getSyncStatus() ).getName() );
                clueMigrateTaskVO.setSyncTypeName( FunctionalEnumHelper.getEnum(ClueMigrateTypeEnum.class, ClueMigrateTypeEnum::getCode, clueMigrateTaskVO.getSyncType() ).getName() );
                clueMigrateTaskVO.setMigratedCdpString(FunctionalEnumHelper.getEnum(MigratedCdpEnum.class, MigratedCdpEnum::getCode, clueMigrateTaskVO.getMigratedCdp()).getName());
                // start time
                LocalDateTime startTime = clueMigrateTaskVO.getStartTime();
                if ( startTime != null ) {
                    clueMigrateTaskVO.setStartTimeStr( startTime.format( DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss") ) );
                }
                // finish time
                LocalDateTime finishTime = clueMigrateTaskVO.getFinishTime();
                if ( finishTime != null ) {
                    clueMigrateTaskVO.setFinishTimeStr( finishTime.format( DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss") ) );
                }

                // dealer name
                clueMigrateTaskVO.setOwnerName( companyMap.get( ownerCode ).getCompanyNameCn() );
            }

        }
    }

    @Override
    public List<ClueMigrateTaskVO> getClueMigrateTaskVOList(ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO) {
        List<ClueMigrateTaskVO> list = baseMapper.getClueMigrateTaskVOList( clueMigrateTaskQueryDTO );
        decorateClueMigrateTaskVOList( list );
        return list;
    }

    @Override
    public void exportClueMigrateTaskList(ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO, HttpServletRequest request, HttpServletResponse response) {
    }

    @Override
    public void exportDealerToDealerClueMigrateTaskList(ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO, HttpServletRequest request, HttpServletResponse response) {
        List<ClueMigrateTaskVO> clueMigrateTaskVOList =
        getDealerToDealerMigrateTaskVOList( clueMigrateTaskQueryDTO );

        List<Map> dataMapList = new ArrayList<>();
        if ( CollectionUtils.isNotEmpty( clueMigrateTaskVOList ) ) {
            clueMigrateTaskVOList.forEach( clueMigrateTaskVO -> {
                ObjectMapper m = new ObjectMapper();
                Map dataMap = m.convertValue(clueMigrateTaskVO, Map.class);
                dataMapList.add( dataMap );
            } );
        }
        List<ExcelExportColumn> exportColumnList = new ArrayList<ExcelExportColumn>();
        exportColumnList.add(new ExcelExportColumn("sourceOwnerCode", "原经销商代码"));
        exportColumnList.add(new ExcelExportColumn("ownerCode", "经销商代码"));
        exportColumnList.add(new ExcelExportColumn("startTimeStr", "开始时间"));
        exportColumnList.add(new ExcelExportColumn("finishTimeStr", "结束时间"));
        exportColumnList.add(new ExcelExportColumn("syncStatusName", "同步状态"));
        exportColumnList.add(new ExcelExportColumn("migratedCdpString", "是否迁移CDP"));
        Map<String, List<Map>> excelData = new HashMap<>();
        excelData.put("clueMigrateTask", dataMapList);
        Map<String, List<ExcelExportColumn>> columnMap = new HashMap<String, List<ExcelExportColumn>>();
        columnMap.put("clueMigrateTask", exportColumnList);
        excelGenerator.generateExcelSheet(excelData, columnMap, "clueMigrateTask.xls", request, response);

    }

    @Override
    public void exportVinToDealerClueMigrateTaskList(ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO, HttpServletRequest request, HttpServletResponse response) {

        List<ClueMigrateTaskVO> clueMigrateTaskVOList = getVinToDealerMigrateTaskVOList( clueMigrateTaskQueryDTO );

        List<Map> dataMapList = new ArrayList<>();

        if ( CollectionUtils.isNotEmpty( clueMigrateTaskVOList ) ) {

            clueMigrateTaskVOList.forEach( clueMigrateTaskVO -> {
                ObjectMapper m = new ObjectMapper();
                Map dataMap = m.convertValue(clueMigrateTaskVO, Map.class);
                dataMapList.add( dataMap );
            } );
        }
        List<ExcelExportColumn> exportColumnList = new ArrayList<ExcelExportColumn>();
        exportColumnList.add(new ExcelExportColumn("vin", "vin"));
        exportColumnList.add(new ExcelExportColumn("ownerName", "所属经销商"));
        exportColumnList.add(new ExcelExportColumn("ownerCode", "经销商代码"));
        exportColumnList.add(new ExcelExportColumn("startTimeStr", "开始时间"));
        exportColumnList.add(new ExcelExportColumn("finishTimeStr", "结束时间"));
        exportColumnList.add(new ExcelExportColumn("syncStatusName", "同步状态"));
        Map<String, List<Map>> excelData = new HashMap<>();
        excelData.put("clueMigrateTask", dataMapList);
        Map<String, List<ExcelExportColumn>> columnMap = new HashMap<String, List<ExcelExportColumn>>();
        columnMap.put("clueMigrateTask", exportColumnList);
        excelGenerator.generateExcelSheet(excelData, columnMap, "clueMigrateTask.xls", request, response);

    }

    @Transactional
    @Override
    public ImportResultDto<DealerToDealerTaskImportDTO> importDealerToDealerClueMigrateTask(MultipartFile importFile) throws IOException {

        ImportResultDto<DealerToDealerTaskImportDTO> importResultDto = dealerToDealerTaskImportDTOExcelRead.analyzeExcelFirstSheet(importFile,
                new AbstractExcelReadCallBack<>(DealerToDealerTaskImportDTO.class, this::dealerToDealerTaskImportValidate));
        List<DealerToDealerTaskImportDTO> dealerToDealerTaskImportDTOList = importResultDto.getDataList();

        if (CollectionUtils.isNotEmpty( dealerToDealerTaskImportDTOList )) {

            // 目标经销商编码列表
            List<String> dealerCodeList =  dealerToDealerTaskImportDTOList.stream().distinct().map( DealerToDealerTaskImportDTO::getOwnerCode ).collect(Collectors.toList());

            Map<String, CompanyDetailDTO> dealerInfoMap = getCompanyInfoListByDealerCodeList(  dealerCodeList ).stream()
                    .collect( Collectors.toMap( CompanyDetailDTO::getCompanyCode, Function.identity() ) );

            dealerToDealerTaskImportDTOList.forEach( x -> {
                ClueMigrateTaskAddDTO clueMigrateTaskAddDTO = new ClueMigrateTaskAddDTO();
                if (StringUtils.isEmpty(x.getMigratedCdpString())) {
                    throw new DALException( "是否迁移CDP值不能为空");
                }
                Integer migratedCdpString = MigratedCdpEnum.getCodeByName(x.getMigratedCdpString());
                if (Objects.isNull(migratedCdpString)) {
                    throw new DALException( "是否迁移CDP取值只能为: 【是】或【否】");
                }
                clueMigrateTaskAddDTO.setMigratedCdp(MigratedCdpEnum.getCodeByName(x.getMigratedCdpString()));
                String ownerCode = x.getOwnerCode();

                CompanyDetailDTO companyDetailDTO = dealerInfoMap.get( ownerCode );
                if ( companyDetailDTO == null ) {
                    logger.error( "未查询到该经销商信息 {}",  ownerCode );
                    throw new DALException( "未查询到该经销商信息" + ownerCode );
                }
                // 判断目标经销商是否停业
                if ( isStopService( companyDetailDTO ) ) {
                    logger.error( "该经销商已终止营业 {}",  ownerCode );
                    throw new DALException( "该经销商已终止营业" + ownerCode );
                }
                clueMigrateTaskAddDTO.setOwnerCode( ownerCode );
                clueMigrateTaskAddDTO.setSourceOwnerCode( x.getSourceOwnerCode() );
                clueMigrateTaskAddDTO.setSyncType(ClueMigrateTypeEnum.DEALER_TO_DEALER.getCode());

                QueryWrapper queryWrapper1 = new QueryWrapper<>();
                queryWrapper1.eq("s_owner_code", clueMigrateTaskAddDTO.getSourceOwnerCode());
                queryWrapper1.eq("sync_type", 1);
                int count1 = baseMapper.selectCount(queryWrapper1);
                if (count1 > 0) {
                    throw new DALException("经销商迁移数据重复");
                }

                createDealerToDealerTask( clueMigrateTaskAddDTO, companyDetailDTO);
            } );
        }
        return null;
    }

    @Transactional
    @Override
    public ImportResultDto<VinToDealerTaskImportDTO> importVinToDealerClueMigrateTask(MultipartFile importFile) throws IOException {
        ImportResultDto<VinToDealerTaskImportDTO> importResultDto = vinToDealerTaskImportDTOExcelRead.analyzeExcelFirstSheet(importFile,
                new AbstractExcelReadCallBack<>(VinToDealerTaskImportDTO.class, this::vinToDealerTaskImportValidate));
        List<VinToDealerTaskImportDTO> vinToDealerTaskImportDTOList = importResultDto.getDataList();

        if (CollectionUtils.isNotEmpty( vinToDealerTaskImportDTOList )) {

            // 目标经销商编码列表
            List<String> dealerCodeList =  vinToDealerTaskImportDTOList.stream().distinct().map( VinToDealerTaskImportDTO::getOwnerCode ).collect(Collectors.toList());

            Map<String, CompanyDetailDTO> dealerInfoMap = getCompanyInfoListByDealerCodeList(  dealerCodeList ).stream()
                    .collect( Collectors.toMap( CompanyDetailDTO::getCompanyCode, Function.identity() ) );

            vinToDealerTaskImportDTOList.forEach( x -> {

                String ownerCode = x.getOwnerCode();

                CompanyDetailDTO companyDetailDTO = dealerInfoMap.get( ownerCode );


                ClueMigrateTaskAddDTO clueMigrateTaskAddDTO = new ClueMigrateTaskAddDTO();
                clueMigrateTaskAddDTO.setOwnerCode( ownerCode);
                clueMigrateTaskAddDTO.setVin( x.getVin() );
                clueMigrateTaskAddDTO.setSourceOwnerCode( x.getSourceOwnerCode() );

                createVinToDealerTask( clueMigrateTaskAddDTO, companyDetailDTO);
            } );
        }

        return null;
    }

    public void dealerToDealerTaskImportValidate(DealerToDealerTaskImportDTO dealerToDealerTaskImportDTO, boolean isValidateSuccess) {

    }

    public void vinToDealerTaskImportValidate(VinToDealerTaskImportDTO vinToDealerTaskImportDTO, boolean isValidateSuccess) {

    }

    @Override
    public List<CompanyDetailDTO> getCompanyListInfo(CompanyNewSelectDTO selectDTO) {

        String successCode = "0";
        List<CompanyDetailDTO> rs = new ArrayList<CompanyDetailDTO>();
        String requestUrl = midUrlProperties.getMidEndOrgCenter() + midUrlProperties.getCompanyInfo();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<CompanyNewSelectDTO> httpEntity = new HttpEntity<>(selectDTO, httpHeaders);
        ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrl, HttpMethod.POST, httpEntity,
                ResponseDTO.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            logger.info("提示信息：{}", "获取经销商详细信息接口异常，请稍后再试");
            throw new DALException("获取经销商详细信息接口异常，请稍后再试");
        }
        if (responseEntity.getBody() != null) {
            if (successCode.equals(responseEntity.getBody().getReturnCode())) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                rs = objectMapper.convertValue(responseEntity.getBody().getData(),
                        new TypeReference<List<CompanyDetailDTO>>() {
                        });
            } else {
                logger.info("提示信息：{}", "获取经销商详细信息接口异常，请稍后再试");
                throw new DALException("获取经销商详细信息接口异常，请稍后再试");
            }
        }
        return rs;
    }

    @Override
    public boolean isStopService(CompanyDetailDTO companyDetailDTO) {
        if ( companyDetailDTO == null ) {
            throw new DALException( "经销商信息为空" );
        }
        Integer dealerServiceStatus = companyDetailDTO.getStatus();
        return DealerServiceStatusEnum.STOP_SERVICE.getCode().equals(dealerServiceStatus);
    }

    @Override
    public boolean deleteById(Long id) {
        logger.info("deleteById,id:{}", id);
        if ( ObjectUtils.isEmpty( id ) ) {
            throw new DALException( "编号为空" );
        }

        LocalTime currentTime = LocalTime.now();
        LocalTime startTime = LocalTime.of(21, 30);
        LocalTime endTime = LocalTime.of(6, 0);
        logger.info("deleteById,currentTime:{}", currentTime);

        if(currentTime.isAfter(startTime) || currentTime.isBefore(endTime)) {
            throw new DALException( "在定时任务执行期间不能删除" );
        }

        TmClueMigrateTask tmClueMigrateTask = baseMapper.selectById(id);
        logger.info("deleteById,tmClueMigrateTask:{}", JSON.toJSONString(tmClueMigrateTask));
        if (tmClueMigrateTask.getSyncStatus() !=0){
            throw new DALException( "删除失败, 只可删除未执行数据" );
        }
        int delete = baseMapper.deleteById(id);
        if (delete != 1){
            throw new DALException( "删除失败" );
        }
        return false;
    }

    @Override
    public IPage<DealerMigrationRecordPo> leadsTransfer(int currentPage, int pageSize, Integer migrationType) {
        //分页对象
        IPage<DealerMigrationRecordPo> page= new Page<>();
        page.setSize(pageSize);
        page.setCurrent(currentPage);

        QueryWrapper<DealerMigrationRecordPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("migration_type", Objects.nonNull(migrationType) ? migrationType : NUM_1);
        return dealerMigrationRecordMapper.selectPage(page, queryWrapper);
    }

    @Override
    @Transactional
    public void cleanDealerToDealerTask() {
        logger.info("cleanDealerToDealerTask,start");
        QueryWrapper queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sync_type",1 );
        queryWrapper.in("sync_status",0,1 );
        queryWrapper.in("is_deleted",0 );
        queryWrapper.eq("migrated_cdp",1 );

        logger.info("cleanDealerToDealerTask,未执行与已完成数据");
        List<TmClueMigrateTask> list = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)){
            return;
        }
        logger.info("cleanDealerToDealerTask,list{},size:{}",list.get(0) , list.size());
        dealerMigrationRecordMapper.cleanDealerMigrationRecord();
        logger.info("数据表清洗完成");
        Map<String, String> values = list.stream()
                .collect(
                        LinkedHashMap::new, // 提供一个 LinkedHashMap 作为结果的容器
                        (map, vo) -> map.put(vo.getSOwnerCode(), vo.getOwnerCode()), // 提取 sourceOwnerCode 和 ownerCode 放入 Map
                        LinkedHashMap::putAll // 将中间结果合并到最终结果中
                );

        logger.info("cleanDealerToDealerTask,提取出经销商信息,size:{}", values.size());
        Map<String, String> map = resolveMapping(values);

        logger.info("cleanDealerToDealerTask,转化");
        List<DealerMigrationRecordPo> list1 = map.entrySet().stream()
                .map(entry -> {
                    DealerMigrationRecordPo dealerMigrationRecordPo = new DealerMigrationRecordPo();
                    dealerMigrationRecordPo.setOriginalDealerCode(entry.getKey());
                    dealerMigrationRecordPo.setTargetDealerCode(entry.getValue());
                    dealerMigrationRecordPo.setMigrationType(NUM_1);
                    return dealerMigrationRecordPo;
                })
                .collect(Collectors.toList());
        List<DealerMigrationRecordPo> list2 = map.entrySet().stream()
                .map(entry -> {
                    DealerMigrationRecordPo dealerMigrationRecordPo = new DealerMigrationRecordPo();
                    dealerMigrationRecordPo.setOriginalDealerCode(entry.getKey());
                    dealerMigrationRecordPo.setTargetDealerCode(entry.getValue());
                    dealerMigrationRecordPo.setMigrationType(NUM_2);
                    return dealerMigrationRecordPo;
                })
                .collect(Collectors.toList());
        list1.addAll(list2);
        logger.info("cleanDealerToDealerTask,inse,list1");
        dealerMigrationRecordMapper.insertList(list1);
        logger.info("cleanDealerToDealerTask,end,list1");
    }

    public static String getValue(Map<String, String> values, String key) {
        while (values.containsKey(key)) {
            String value = values.get(key);
            if (values.containsKey(value)) {
                key = value;
            } else {
                return value;
            }
        }
        return key;
    }

    private static Map<String, String> resolveMapping(Map<String, String> originalValues) {
        Map<String, String> resolvedValues = new HashMap<>();
        // 对于每个键，我们尝试找到其最终映射值
        for (String key : originalValues.keySet()) {
            Set<String> visitedKeys = new HashSet<>();
            String currentKey = key;
            boolean isInLoop = false;
            // 循环寻找最终值
            while (originalValues.containsKey(currentKey)) {
                if (visitedKeys.contains(currentKey)) {
                    // 检测到循环引用
                    isInLoop = true;
                    break;
                }
                visitedKeys.add(currentKey);
                currentKey = originalValues.get(currentKey);
            }

            if (!isInLoop) {
                // 如果未检测到循环引用，则为所有访问过的键设置最终映射值
                for (String visitedKey : visitedKeys) {
                    resolvedValues.put(visitedKey, currentKey);
                }
            } else {
                // 检测到循环引用，将循环内的元素值设置为它们自身
                for (String visitedKey : visitedKeys) {
                    resolvedValues.put(visitedKey, visitedKey);
                }
            }
        }
        return resolvedValues;
    }
    
    @Override
    public List<CompanyDetailDTO> getCompanyInfoListByDealerCodeList(List<String> dealerCodeList) {
        if ( CollectionUtils.isEmpty( dealerCodeList ) ) {
            throw new DALException( "经销商代码列表为空" );
        }
        CompanyNewSelectDTO companyNewSelectDTO = new CompanyNewSelectDTO();
        companyNewSelectDTO.setCompanyCodes( dealerCodeList );
        return getCompanyListInfo( companyNewSelectDTO );
    }

    @Override
    public CompanyDetailDTO getCompanyInfoByDealerCode(String dealerCode) {
        if ( StringUtils.isEmpty( dealerCode ) ) {
            throw new DALException( "经销商编号为空" );
        }
        List<String> dealerCodeList = new ArrayList<>();
        dealerCodeList.add(  dealerCode );
        try {
            return getCompanyInfoListByDealerCodeList( dealerCodeList ).get( 0 );
        } catch ( Exception e ) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void createDealerToDealerTask(ClueMigrateTaskAddDTO clueMigrateTaskAddDTO) {
        // 目标经销商
        String dealerCode = clueMigrateTaskAddDTO.getOwnerCode();
        if ( StringUtils.isEmpty( dealerCode ) ) {
            throw new DALException( "目标经销商为空" );
        }
        if (Objects.isNull(clueMigrateTaskAddDTO.getMigratedCdp())) {
            throw new DALException( "是否迁移CDP不能为空" );
        }
        createDealerToDealerTask( clueMigrateTaskAddDTO, getCompanyInfoByDealerCode( dealerCode ) );
    }

    @Override
    public void createVinToDealerTask(ClueMigrateTaskAddDTO clueMigrateTaskAddDTO) {
        // 目标经销商
        String dealerCode = clueMigrateTaskAddDTO.getOwnerCode();
        if ( StringUtils.isEmpty( dealerCode ) ) {
            throw new DALException( "目标经销商为空" );
        }
        createVinToDealerTask( clueMigrateTaskAddDTO, getCompanyInfoByDealerCode( dealerCode ) );
    }

}

