package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyFinalPartInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFinalPartInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyFinalPartInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyFinalPartInfoService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 亲善预申请最终解决方案配件信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
@Service
public class GoodwillApplyFinalPartInfoServiceImpl
		extends ServiceImpl<GoodwillApplyFinalPartInfoMapper, GoodwillApplyFinalPartInfoPO>
		implements GoodwillApplyFinalPartInfoService {
	/** 日志对象 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillApplyFinalPartInfoMapper goodwillApplyFinalPartInfoMapper;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillApplyFinalPartInfoDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFinalPartInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillApplyFinalPartInfoDTO> selectPageBysql(Page page,
			GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO) {
		if (goodwillApplyFinalPartInfoDTO == null) {
			goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
		}
		GoodwillApplyFinalPartInfoPO goodwillApplyFinalPartInfoPo = goodwillApplyFinalPartInfoDTO
				.transDtoToPo(GoodwillApplyFinalPartInfoPO.class);

		List<GoodwillApplyFinalPartInfoPO> list = goodwillApplyFinalPartInfoMapper.selectPageBySql(page,
				goodwillApplyFinalPartInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillApplyFinalPartInfoDTO> result = list.stream()
					.map(m -> m.transPoToDto(GoodwillApplyFinalPartInfoDTO.class)).collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillApplyFinalPartInfoDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFinalPartInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillApplyFinalPartInfoDTO> selectListBySql(
			GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO) {
		if (goodwillApplyFinalPartInfoDTO == null) {
			goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
		}
		GoodwillApplyFinalPartInfoPO goodwillApplyFinalPartInfoPo = goodwillApplyFinalPartInfoDTO
				.transDtoToPo(GoodwillApplyFinalPartInfoPO.class);
		List<GoodwillApplyFinalPartInfoPO> list = goodwillApplyFinalPartInfoMapper
				.selectListBySql(goodwillApplyFinalPartInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillApplyFinalPartInfoDTO.class))
					.collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFinalPartInfoDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillApplyFinalPartInfoDTO getById(Long id) {
		GoodwillApplyFinalPartInfoPO goodwillApplyFinalPartInfoPo = goodwillApplyFinalPartInfoMapper.selectById(id);
		if (goodwillApplyFinalPartInfoPo != null) {
			return goodwillApplyFinalPartInfoPo.transPoToDto(GoodwillApplyFinalPartInfoDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillApplyFinalPartInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO) {
		// 对对象进行赋值操作
		GoodwillApplyFinalPartInfoPO goodwillApplyFinalPartInfoPo = goodwillApplyFinalPartInfoDTO
				.transDtoToPo(GoodwillApplyFinalPartInfoPO.class);
		// 执行插入
		int row = goodwillApplyFinalPartInfoMapper.insert(goodwillApplyFinalPartInfoPo);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillApplyFinalPartInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO) {
		GoodwillApplyFinalPartInfoPO goodwillApplyFinalPartInfoPo = goodwillApplyFinalPartInfoMapper.selectById(id);
		// 对对象进行赋值操作
		goodwillApplyFinalPartInfoDTO.transDtoToPo(goodwillApplyFinalPartInfoPo);
		// 执行更新
		int row = goodwillApplyFinalPartInfoMapper.updateById(goodwillApplyFinalPartInfoPo);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillApplyFinalPartInfoMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillApplyFinalPartInfoMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据申请单id查询亲善金额及类型零配件信息
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2020/5/11
	 */
	@Override
	public List<GoodwillApplyFinalPartInfoDTO> queryPartAmountInfo(Integer goodwillType, Long goodwillApplyId) {
		List<GoodwillApplyFinalPartInfoDTO> list = goodwillApplyFinalPartInfoMapper.queryPartAmountInfo(goodwillType,
				goodwillApplyId);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list;
		}
	}

}
