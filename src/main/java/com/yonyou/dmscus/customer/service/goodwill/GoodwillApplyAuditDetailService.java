package com.yonyou.dmscus.customer.service.goodwill;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditDetailDTO;

/**
 * <p>
 * 亲善审计明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-24
 */
public interface GoodwillApplyAuditDetailService {
	public IPage<GoodwillApplyAuditDetailDTO> selectPageBysql(Page page,
			GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO);

	public List<GoodwillApplyAuditDetailDTO> selectListBySql(GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO);

	public GoodwillApplyAuditDetailDTO getById(Long id);

	public int insert(GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO);

	public int update(Long id, GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO);

	public int deleteById(Long id);

	public int deleteBatchIds(String ids);

}
