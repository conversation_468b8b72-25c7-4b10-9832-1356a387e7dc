package com.yonyou.dmscus.customer.service.inviteTag;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.framework.util.bean.ApplicationContextHelper;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.common.AjaxResponse;
import com.yonyou.dmscus.customer.common.BaseCheckDataVO;
import com.yonyou.dmscus.customer.dao.InviteTag.InviteTagMapper;
import com.yonyou.dmscus.customer.dao.InviteTag.InviteVehicleTagMapper;
import com.yonyou.dmscus.customer.dao.common.VehicleMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dto.InviteTagDto;
import com.yonyou.dmscus.customer.dto.InviteVehicleTagDTO;
import com.yonyou.dmscus.customer.dto.InviteVehicleTagParamsDTO;
import com.yonyou.dmscus.customer.dto.RecVehicleTagDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.TmVehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.InviteClueParamDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.InviteClueResultDTO;
import com.yonyou.dmscus.customer.entity.po.common.VehiclePO;
import com.yonyou.dmscus.customer.entity.po.inviteTag.InviteTagPO;
import com.yonyou.dmscus.customer.entity.po.inviteTag.InviteVehicleTagPO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.util.common.ImportExcelUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static com.yonyou.dmscus.customer.constants.CommonConstants.*;

@Service
public class InviteVehicleTagServiceImpl implements InviteVehicleTagService {

	private static final int TWO_THOUSAND_AND_FIFTEEN = 2015;
	private static final String END_TIME_CODE = " 23:59:59";
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    public static final String TAG_TITLE = "标签管理";
    public static final String VIN = "VIN";
    public static final String TAG = "标签";

	@Autowired
	private BusinessPlatformService businessPlatformService;
	@Resource
	private InviteVehicleTagMapper inviteVehicleTagMapper;
	@Resource
	private InviteTagMapper inviteTagMapper;
	@Resource
	private VehicleMapper vehicleMapper;
	@Resource
	private InviteVehicleRecordMapper inviteVehicleRecordMapper;

	@Override
	public IPage<InviteVehicleTagDTO> selectPageBysql(Page<InviteVehicleTagDTO> page,
													  InviteVehicleTagParamsDTO param) {
		String tagNames = param.getTagName();
		if (StringUtils.isNotBlank(tagNames) && tagNames.contains(",")) {
			param.setTagNameList(Arrays.asList(tagNames.split(",")));
		}
		List<InviteVehicleTagDTO> pageList = inviteVehicleTagMapper.selectByDtoForPage(page, param);
		this.transResTags(pageList);
		page.setRecords(pageList);
		return page;
	}
	
	@Override
	public InviteVehicleTagDTO getVinTag(String vin) {
		List<InviteTagDto> vinTags = inviteVehicleTagMapper.selectVinTagByVIN(vin);
		InviteVehicleTagDTO tagDTO = new InviteVehicleTagDTO();
		tagDTO.setVin(vin);
		tagDTO.setTagList(vinTags);
		if(CollectionUtils.isNotEmpty(vinTags)){
			tagDTO.setTagNameList(vinTags.stream().map(InviteTagDto::getName).distinct().collect(Collectors.toList()));
			tagDTO.setTagName(org.apache.commons.lang3.StringUtils.joinWith(",",tagDTO.getTagNameList()));
		}
		return tagDTO;
	}


	
	@Override
	@Transactional
	public long insertBatchInviteVehicleTag(Map<String, Set<String>> vinTagMap, Integer bindType) {

		Set<String> vins = vinTagMap.keySet();

		this.checkVin(vins);

		//1.删除已有导入标签
		this.deleteTagByVins(vins,bindType);

		List<InviteVehicleTagPO> tagPos = Lists.newArrayList();

		Map<String,Long> tagIdMap = Maps.newHashMap();

		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();

		String createBy = Objects.isNull(loginInfoDto) ? null : loginInfoDto.getUserId().toString();

		String appId = Objects.isNull(loginInfoDto) ? DEFAULT_APPID : loginInfoDto.getAppId();

		vinTagMap.forEach((vin,tags) -> {

			//初始化tag
			initTagId(tags,tagIdMap);

			tagPos.addAll(tags.stream().map(tag -> {
				InviteVehicleTagPO po = new InviteVehicleTagPO();
				po.setAppId(appId);
				po.setBindType(bindType);
				po.setVin(vin);
				po.setTagId(tagIdMap.get(tag));
				po.setCreatedBy(createBy);
				if(Objects.isNull(po.getTagId())){
					throw new ServiceBizException("获取标签失败！");
				}
				return po;
			}).collect(Collectors.toList()));
		});

		Lists.partition(tagPos,1000).forEach(inviteVehicleTagMapper::insertBatch);

  		return tagPos.size();
	}

	private void checkVin(Set<String> vins){
		Map<String,String> cache = Maps.newHashMap();
		List<String> errors = Lists.newArrayList();
		if(CollectionUtils.isNotEmpty(vins)){
			vins.forEach(vin -> {
				if(!cache.containsKey(vin)){
					LambdaQueryWrapper<VehiclePO> queryWrapper = new QueryWrapper().lambda();
					queryWrapper.eq(VehiclePO::getVin, vin);
					VehiclePO rs = vehicleMapper.selectOne(queryWrapper);
					if(Objects.isNull(rs)){
						errors.add(vin);
					}else{
						cache.put(vin,vin);
					}
				}
			});
		}
		if(!errors.isEmpty()){
			throw new ServiceBizException("vin校验失败,以下vin:["+JSON.toJSONString(errors)+"]不存在");
		}
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void initTagId(Set<String> tags,Map<String,Long> tagIdMap){
		//命中缓存
		Set<String> initTags = tags.stream().filter(tag -> !tagIdMap.containsKey(tag)).collect(Collectors.toSet());
		if(CollectionUtils.isNotEmpty(initTags)){
			//未命中缓存从数据库取
			List<InviteTagPO> inviteTagPOS = inviteTagMapper.selectInviteTagByName(initTags, TAG_GROUP_VIN);
			if(!inviteTagPOS.isEmpty()){
				inviteTagPOS.forEach(po -> tagIdMap.putIfAbsent(po.getName(),po.getId()));
			}
			//不在数据库中的
			initTags = tags.stream().filter(tag -> !tagIdMap.containsKey(tag)).collect(Collectors.toSet());
			if(CollectionUtils.isNotEmpty(initTags)){
				//新增以及加入缓存
				inviteTagMapper.insertBatchInviteTag(initTags,TAG_GROUP_VIN);
				inviteTagPOS = inviteTagMapper.selectInviteTagByName(initTags, TAG_GROUP_VIN);
				if(!inviteTagPOS.isEmpty()){
					inviteTagPOS.forEach(po -> tagIdMap.putIfAbsent(po.getName(),po.getId()));
				}
			}

		}
	}

	//根据vin和标签类型删除标签
	private void deleteTagByVins(Set<String> vins, Integer bindType){
		if(CollectionUtils.isNotEmpty(vins)){
			LambdaQueryWrapper<InviteVehicleTagPO> wrapper = Wrappers.lambdaQuery();
			wrapper.in(InviteVehicleTagPO::getVin, vins);
			wrapper.eq(InviteVehicleTagPO::getBindType,bindType);
			inviteVehicleTagMapper.delete(wrapper);
		}
	}
	
	@Override
	public void importInviteTag(MultipartFile multipartFile) {
		AjaxResponse ajaxResponse = new AjaxResponse();
		BaseCheckDataVO baseCheckDataVO = new BaseCheckDataVO();
		Map<String,Set<String>> vinTagMap = Maps.newHashMap();
        Set<String> tagNameList = new HashSet<>();
		 
        List<Map<Integer, String>> excelData = null;
		String fileName = multipartFile.getOriginalFilename();
		InputStream ins = null;
		try {
			ins = multipartFile.getInputStream();
			excelData = ImportExcelUtil.getImportExcelData(ins, fileName);
		} catch (IOException e) {
 			e.printStackTrace();
		} finally {
			try {
				if (ins != null) {
					ins.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		if (excelData != null) {
			this.checkInviteTagImportData(excelData, vinTagMap, tagNameList, baseCheckDataVO, ajaxResponse);
		}
	    //存在错误的信息返回
        if(ajaxResponse.isFail()){
			throw new ServiceBizException("文件解析失败,{}",JSON.toJSONString(baseCheckDataVO.getImportErrorList()));
        }
        if (!vinTagMap.isEmpty()) {
			InviteVehicleTagService proxy = ApplicationContextHelper.getBeanByType(InviteVehicleTagService.class);
        	if(Objects.isNull(proxy)){
				throw new ServiceBizException("获取代理服务失败！");
			}
			proxy.insertBatchInviteVehicleTag(vinTagMap, TAG_BINTTYPE_IMPORT);
        }
	}
	
	@Override
	public void exportInviteTag(HttpServletResponse response, InviteVehicleTagParamsDTO param) {
 		String tagNames = param.getTagName();
		if (StringUtils.isNotBlank(tagNames) && tagNames.contains(",")) {
			param.setTagNameList(Arrays.asList(tagNames.split(",")));
		}
		List<InviteVehicleTagDTO> dataList = inviteVehicleTagMapper.selectByDto(param);
		this.transResTags(dataList);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		String fileName = TAG_TITLE + sdf.format(new Date()) + ".xlsx";

		OutputStream ous = null;
		Workbook workbook = new XSSFWorkbook();
		try {
			response.reset();
			response.setContentType("application/msexcel;charset=utf-8");
			response.setHeader("Content-disposition", "attachment; filename=" + new String(fileName.getBytes(), "ISO8859-1"));
			ous = response.getOutputStream();
			//CellStyle titleStyle = workbook.createCellStyle();
			Sheet sheet = workbook.createSheet(TAG_TITLE);
			sheet.setColumnWidth(0, 21*256);
			sheet.setColumnWidth(1, 32*256);
			Row titleRow = sheet.createRow(0);
			titleRow.createCell(0).setCellValue(VIN);
			titleRow.createCell(1).setCellValue(TAG);
			
			for (int i = 0; i < dataList.size(); i++) {
				InviteVehicleTagDTO dto = dataList.get(i);
				Row row = sheet.createRow((i+1));
				row.createCell(0).setCellValue(dto.getVin());
				row.createCell(1).setCellValue(dto.getTagName());
			}
			workbook.write(ous);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("标签导入失败: {}", e.getMessage());
		} finally {
			try {
				if (ous != null) {
					ous.close();
				}
				workbook.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	private void transResTags(List<InviteVehicleTagDTO> dataList){
		if(CollectionUtils.isNotEmpty(dataList)){
			List<String> vins = dataList.stream().map(InviteVehicleTagDTO::getVin).collect(Collectors.toList());
			List<InviteVehicleTagDTO> tagDTOS = inviteVehicleTagMapper.selectByVins(vins);
			Map<String, String> tagMap = tagDTOS.stream().collect(Collectors.toMap(InviteVehicleTagDTO::getVin, InviteVehicleTagDTO::getTagName));
			dataList.forEach(e -> {
				if(tagMap.containsKey(e.getVin())){
					e.setTagName(tagMap.get(e.getVin()));
				}
			});
		}
	}

	//标签导入数据校验
    private void checkInviteTagImportData(List<Map<Integer, String>> importDataList,
										  Map<String,Set<String>> vinTagMap, Set<String> tagList,
    		BaseCheckDataVO checkDataVO, AjaxResponse ajaxResponse) {
		//第一行标题，从excel数据第二行开始
		int rowId = 2;
		for(Map<Integer, String> rowData : importDataList) {
			try {
				//设置某行数据
				checkDataVO.setRowData(rowData);
				//设置第几行数字
				checkDataVO.setRowId(rowId);
				//check
				String vin = rowData.get(0);
				String tagNames = rowData.get(1);
				if (StringUtils.isBlank(vin)) {
					checkDataVO.addError("vin字段为空！");
				}
				if (StringUtils.isBlank(tagNames)) {
					checkDataVO.addError("标签字段为空！");
				}
				if(checkDataVO.getErrorList().size() > 0) {
					break;
				}
				//兼容不通行同一个vin的情况。
				List<String> tagNameList = Arrays.asList(tagNames.replaceAll("，", ",").split(","));
				Set<String> tags  = vinTagMap.get(vin);
				if(Objects.isNull(tags)){
					tags = Sets.newHashSet();
					vinTagMap.put(vin,tags);
				}
				tags.addAll(tagNameList);
				rowId++; //下一行
			} catch (Exception e) {
				logger.error("InviteVehicleTagServiceImpl.checkInviteTagImportData("+JSON.toJSONString(rowData)+") EXCEL导入记录校验出错:{}",e);
				checkDataVO.addError("标签导入校验出错：" + e.getMessage());
			}
			//获取错误的验证信息
			List<String> errorMsgList = checkDataVO.getErrorList();
			if(CollectionUtils.isNotEmpty(errorMsgList)){
				ajaxResponse.setResult(AjaxResponse.FAILD);
			}
		}
    }

	@Override
	public void receiveVinTags(RecVehicleTagDTO recVehicleTagDTO) {
		List<InviteVehicleTagDTO> vehicleTags = recVehicleTagDTO.getVehicleTags();
		if(CollectionUtils.isNotEmpty(vehicleTags)){
			InviteVehicleTagService proxy = ApplicationContextHelper.getBeanByType(InviteVehicleTagService.class);
			if(Objects.isNull(proxy)){
				throw new ServiceBizException("获取代理服务失败！");
			}
			Map<String, Set<String>> vinTagMap = vehicleTags.stream().collect(Collectors.toMap(InviteVehicleTagDTO::getVin,e -> Sets.newHashSet(e.getTagNameList())));
			proxy.insertBatchInviteVehicleTag(vinTagMap, TAG_BINTTYPE_AGLOR);
		}
	}

	@Override
	public Map<String, String> getAllTag() {
		return inviteTagMapper.selectList(Wrappers.lambdaQuery()).stream().collect(Collectors.toMap(InviteTagPO::getName, InviteTagPO::getName));
	}

	@Override
	public boolean getLossTag(String vin, String dealerCode) {
		logger.info("getLossTag start vin :{}, dealerCode :{}", vin, dealerCode);
		boolean flag = false;
		//自店流失客户类型线索，状态为"未完成"或"逾期关闭"，建议进厂日期在2020年1月1日至当月最后一天
		//获取当月最后一天
		LocalDate lastDay = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
		String endTime = lastDay.toString() + END_TIME_CODE;
		logger.info("endTime:{}", endTime);
		int count = inviteVehicleRecordMapper.selectLossTag(vin, endTime);
		logger.info("getLossTag count:{}", count);
		//车是2015年及以后车款，红色高亮显示
		if (count > 0) {
			TmVehicleDTO tmVehicleDTO = businessPlatformService.getVehicleByVIN(vin);
			String configYear = tmVehicleDTO.getConfigYear();
			logger.info("getLossTag configYear:{}", configYear);
			int year = 0;
			try {
				year = Integer.parseInt(configYear);
			} catch (NumberFormatException e) {
				logger.info("年款转换失败 year:{}", configYear);
				return false;
			}
			if (year >= TWO_THOUSAND_AND_FIFTEEN) {
				logger.info("year >= 2015 year:{}", year);
				flag = true;
			}
		}
		logger.info("getLossTag end flag:{}", flag);
		return flag;
	}
	@Override
	public List<InviteClueResultDTO> selectInviteClueTag(InviteClueParamDTO inviteClueParamDTO) {
		logger.info("getLossInviteTag start inviteClueParamDTO :{}", inviteClueParamDTO);
		boolean flag = false;
		//自店流失客户类型线索，状态为"未完成"或"逾期关闭"，建议进厂日期在2020年1月1日至当月最后一天
		//获取当月最后一天
		LocalDate lastDay = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
		String endTime = lastDay.toString() + END_TIME_CODE;
		logger.info("endTime:{}", endTime);

		Page<InviteClueResultDTO> page = new Page<>(inviteClueParamDTO.getCurrentPage(), inviteClueParamDTO.getPageSize());

		List<InviteClueResultDTO> result = inviteVehicleRecordMapper.selectInviteClue(page,inviteClueParamDTO);

		return result;
	}
}
