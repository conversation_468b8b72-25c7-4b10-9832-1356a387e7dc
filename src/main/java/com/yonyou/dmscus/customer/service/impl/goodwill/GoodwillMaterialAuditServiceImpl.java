package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillMaterialAuditMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMaterialAuditDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMaterialAuditPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillMaterialAuditService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 亲善预申请单亲善信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class GoodwillMaterialAuditServiceImpl extends ServiceImpl<GoodwillMaterialAuditMapper, GoodwillMaterialAuditPO>
		implements GoodwillMaterialAuditService {
	/** 日志对象 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillMaterialAuditMapper goodwillMaterialAuditMapper;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillMaterialAuditDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMaterialAuditDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	@Transactional(readOnly = true)
	public IPage<GoodwillMaterialAuditDTO> selectPageBysql(Page page,
			GoodwillMaterialAuditDTO goodwillMaterialAuditDTO) {
		if (goodwillMaterialAuditDTO == null) {
			goodwillMaterialAuditDTO = new GoodwillMaterialAuditDTO();
		}
		GoodwillMaterialAuditPO goodwillMaterialAuditPo = goodwillMaterialAuditDTO
				.transDtoToPo(GoodwillMaterialAuditPO.class);

		List<GoodwillMaterialAuditPO> list = goodwillMaterialAuditMapper.selectPageBySql(page, goodwillMaterialAuditPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillMaterialAuditDTO> result = list.stream()
					.map(m -> m.transPoToDto(GoodwillMaterialAuditDTO.class)).collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillMaterialAuditDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMaterialAuditDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	@Transactional(readOnly = true)
	public List<GoodwillMaterialAuditDTO> selectListBySql(GoodwillMaterialAuditDTO goodwillMaterialAuditDTO) {
		if (goodwillMaterialAuditDTO == null) {
			goodwillMaterialAuditDTO = new GoodwillMaterialAuditDTO();
		}
		GoodwillMaterialAuditPO goodwillMaterialAuditPo = goodwillMaterialAuditDTO
				.transDtoToPo(GoodwillMaterialAuditPO.class);
		List<GoodwillMaterialAuditPO> list = goodwillMaterialAuditMapper.selectListBySql(goodwillMaterialAuditPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillMaterialAuditDTO.class)).collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMaterialAuditDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	@Transactional(readOnly = true)
	public GoodwillMaterialAuditDTO getById(Long id) {
		GoodwillMaterialAuditPO goodwillMaterialAuditPo = goodwillMaterialAuditMapper.selectById(id);
		if (goodwillMaterialAuditPo != null) {
			return goodwillMaterialAuditPo.transPoToDto(GoodwillMaterialAuditDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillMaterialAuditDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillMaterialAuditDTO goodwillMaterialAuditDTO) {
		// 对对象进行赋值操作
		GoodwillMaterialAuditPO goodwillMaterialAuditPo = goodwillMaterialAuditDTO
				.transDtoToPo(GoodwillMaterialAuditPO.class);
		// 执行插入
		int row = goodwillMaterialAuditMapper.insert(goodwillMaterialAuditPo);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillMaterialAuditDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillMaterialAuditDTO goodwillMaterialAuditDTO) {
		GoodwillMaterialAuditPO goodwillMaterialAuditPo = goodwillMaterialAuditMapper.selectById(id);
		// 对对象进行赋值操作
		goodwillMaterialAuditDTO.transDtoToPo(goodwillMaterialAuditPo);
		// 执行更新
		int row = goodwillMaterialAuditMapper.updateById(goodwillMaterialAuditPo);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillMaterialAuditMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillMaterialAuditMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}
}
