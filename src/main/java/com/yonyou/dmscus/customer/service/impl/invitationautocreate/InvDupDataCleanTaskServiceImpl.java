package com.yonyou.dmscus.customer.service.impl.invitationautocreate;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.feign.MidEndCustomerCenterClient;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.feign.vo.InvDataCleanVO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.dto.VehicleOwnerDetailByIdListDTO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.dto.VehicleOwnerSelectByIdListDTO;
import com.yonyou.dmscus.customer.service.invitationautocreate.IncRepCleanDataService;
import com.yonyou.dmscus.customer.service.invitationautocreate.InvDupDataCleanTaskService;
import com.yonyou.dmscus.customer.service.invitationautocreate.po.InviteVehiclePO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Period;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 重复线索数据清洗
 */
@Slf4j
@Service
@RefreshScope
public class InvDupDataCleanTaskServiceImpl implements InvDupDataCleanTaskService {

    @Autowired
    ReportCommonClient reportCommonClient;
    @Autowired
    MidEndCustomerCenterClient midEndCustomerCenterClient;

    @Autowired
    IncRepCleanDataService incRepCleanDataService;

    /**
     * 计算年龄
     *
     * @param birth
     * @return
     */
    public static String getDetailedAge(LocalDate birth) {
        if (birth == null) {
            return null;
        }
        LocalDate nowDate = LocalDate.now();
        //使用period就可以获得年份
        Period period = Period.between(birth, nowDate);
        int age = period.getYears();
        return ObjectUtils.toString(age, null);
    }

    @Override
    public void doIncRepTaskClean() {
        log.info("doIncRepTaskClean,start");
        /**查询未下发的重复任务集合*/
        List<InvDataCleanVO> list = reportCommonClient.queryIncRepTask();
        if (CollectionUtils.isEmpty(list)) {
            log.info("doIncRepTaskClean,CollectionUtils.isNotEmpty(list)");
            return;
        }
        this.incRepCleanData(list, Boolean.TRUE);
        log.info("doIncRepTaskClean,end");
    }

    @Override
    public void doIncRepClueClean() {
        log.info("doIncRepClueClean,start");
        /**查询未完成的重复线索集合*/
        List<InvDataCleanVO> list = reportCommonClient.queryIncClueTask();
        if (CollectionUtils.isEmpty(list)) {
            log.info("doIncRepClueClean,CollectionUtils.isNotEmpty(list)");
            return;
        }
        this.incRepCleanData(list, Boolean.FALSE);
        log.info("doIncRepClueClean,end");
    }

    private void incRepCleanData(List<InvDataCleanVO> list, boolean flag) {
        log.info("incRepCleanData,start,flag:{}", flag);
        /**查询对应的用户信息*/
        List<VehicleOwnerDetailByIdListDTO> pageOwner = getPageOwner(list);
        Map<Long, VehicleOwnerDetailByIdListDTO> idMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(pageOwner)) {
            log.info("incRepCleanData,flag:{},pageOwner:{}", flag, pageOwner.size());
            idMap = pageOwner.stream().collect(Collectors.toMap(VehicleOwnerDetailByIdListDTO::getOneId, Function.identity(), (key1, key2) -> key2));
        }
        log.info("incRepCleanData,flag:{},idMap:{}", flag, idMap.size());
        /**分组判断用户信息*/
        //按照vin, dealer_code, invite_type, advise_in_date分组-定义库数
        Function<InvDataCleanVO, List<Object>> compositeKey = person -> Arrays.asList(person.getVin(), person.getDealerCode(), person.getInviteType(), person.getAdviseInDate());
        //按照vin, dealer_code, invite_type, advise_in_date分组-分组
        Map<List<Object>, List<InvDataCleanVO>> groupMap = list.stream().collect(Collectors.groupingBy(compositeKey, Collectors.toList()));
        log.info("incRepCleanData,flag:{},groupMap:{}", flag, groupMap.size());
        List<InvDataCleanVO> groupList;
        List<InviteVehiclePO> cloList = new ArrayList<>();
        List<InviteVehiclePO> upList = new ArrayList<>();
        for (Map.Entry<List<Object>, List<InvDataCleanVO>> m : groupMap.entrySet()) {
            groupList = m.getValue();
            doProData(groupList, idMap, cloList, upList);
        }
        log.info("incRepCleanData,flag:{}", flag);
        /**批量修改*/
        if (flag) {
            log.info("incRepCleanData,doTaskClean");
            incRepCleanDataService.doTaskClean(cloList, upList);
        } else {
            log.info("incRepCleanData,doClueClean");
            incRepCleanDataService.doClueClean(cloList, upList);
        }
        log.info("incRepCleanData,end");
    }

    private void doProData(List<InvDataCleanVO> groupList, Map<Long, VehicleOwnerDetailByIdListDTO> idMap,
                           List<InviteVehiclePO> cloList, List<InviteVehiclePO> upList) {
        log.info("doProData,start");
        if (CollectionUtils.isEmpty(groupList)) {
            log.info("doProData,CollectionUtils.isEmpty(groupList)");
            return;
        }
        /**AI评分 - 获取评分最高的线索,1.分数一样取第一条,2.无分数返回null*/
        Optional<InvDataCleanVO> optionalVo = groupList.stream().filter(vo -> vo.getTotalScore() != null).max(Comparator.comparing(InvDataCleanVO::getTotalScore));
        /**获取跟进状态不为未跟进的对象*/
        //备选数据
        List<InvDataCleanVO> followList = groupList.stream().filter(vo -> !CommonConstants.FOLLOW_STATUS_I.equals(vo.getFollowStatus())).collect(Collectors.toList());
        InvDataCleanVO alternateVo = groupList.get(groupList.size() - 1);
        if(CollectionUtils.isNotEmpty(followList)){
            alternateVo = followList.get(followList.size() - 1);
        }
        //获取分数最高的数据,如没有取状态不为未跟进的/最后一条(取到的数据做修改,其余系统删除)
        InvDataCleanVO cleanVo = optionalVo.isPresent() ? optionalVo.get() : alternateVo;
        Long idUp = cleanVo.getId();
        log.info("doProData,idUp:{}", idUp);
        InviteVehiclePO po;
        Long customerId;
        VehicleOwnerDetailByIdListDTO dto;
        Long id;
        String name = null;
        String tel = null;
        String sex = null;
        String age = null;
        for (InvDataCleanVO vo : groupList) {
            id = vo.getId();
            customerId = vo.getCustomerId();
            if (idUp.equals(id)) {
                dto = idMap.get(customerId);
                if(ObjectUtil.isNotEmpty(dto)){
                    name = dto.getName();
                    tel = dto.getMobile();
                    sex = ObjectUtils.toString(dto.getGender(), null);
                    age = getDetailedAge(dto.getBirthday());
                }
                //修改用户信息
                po = new InviteVehiclePO(name,tel,sex,age,id);
                upList.add(po);
                continue;
            }
            //关闭重复线索
            po = new InviteVehiclePO(vo.getId());
            cloList.add(po);
        }
        log.info("doProData,end,upList:{},cloList:{}", upList.size(), cloList.size());
    }

    private List<VehicleOwnerDetailByIdListDTO> getPageOwner(List<InvDataCleanVO> list) {
        List<Long> ids = list.stream().map(InvDataCleanVO::getCustomerId).distinct().collect(Collectors.toList());
        log.info("getPageOwner,ids:{}", ids.size());
        List<List<Long>> partition = Lists.partition(ids, 500);
        VehicleOwnerSelectByIdListDTO dto;
        List<VehicleOwnerDetailByIdListDTO> listDto;
        List<VehicleOwnerDetailByIdListDTO> pageOwner = new ArrayList<>();
        for (List<Long> longs : partition) {
            dto = new VehicleOwnerSelectByIdListDTO();
            dto.setList(longs);
            listDto = selectVehicleOwnerByIdList(dto);
            if (CollectionUtils.isNotEmpty(listDto)) {
                pageOwner.addAll(listDto);
            }
        }
        log.info("getPageOwner,end:{}", pageOwner.size());
        return pageOwner;
    }

    private List<VehicleOwnerDetailByIdListDTO> selectVehicleOwnerByIdList(VehicleOwnerSelectByIdListDTO dto) {
        log.info("selectVehicleOwnerByIdList,start");
        List<VehicleOwnerDetailByIdListDTO> dtoList;
        if (ObjectUtil.isEmpty(dto)) {
            log.info("selectVehicleOwnerByIdList,ObjectUtil.isEmpty(dto)");
            return null;
        }
        List<Long> listLong = dto.getList();
        if (CollectionUtils.isEmpty(listLong)) {
            log.info("selectVehicleOwnerByIdList,CollectionUtils.isEmpty(listLong)");
            return null;
        }
        log.info("selectVehicleOwnerByIdList,listLong:{}", listLong.size());
        ResponseDTO<List<VehicleOwnerDetailByIdListDTO>> list;
        try {
            list = midEndCustomerCenterClient.selectVehicleOwnerByOneIdList(dto);
        } catch (Exception e) {
            log.info("selectVehicleOwnerByIdList,e:{}", e);
            throw new RuntimeException("selectVehicleOwnerByIdList,fail");
        }
        dtoList = list.getData();
        log.info("selectVehicleOwnerByIdList,end");
        return dtoList;
    }
}
