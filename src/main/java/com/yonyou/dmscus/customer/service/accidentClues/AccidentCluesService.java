package com.yonyou.dmscus.customer.service.accidentClues;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.dto.AccidentCluesUserDto;
import com.yonyou.dmscus.customer.entity.dto.GetDealerUserDataDTO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.*;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesFollowPO;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesImportPO;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesPO;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesSaNumberPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO;
import com.yonyou.dmscus.customer.entity.vo.AccidentClueDashBoardVO;
import com.yonyou.dmscus.customer.entity.vo.AccidentClueVo;
import com.yonyou.dmscus.customer.entity.vo.EmpByRoleCodeVO;
import com.yonyou.dmscus.customer.entity.vo.RepairCountVo;
import com.yonyou.dmscus.customer.entity.vo.accidentClues.AccidentClueFollowVo;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/16 0016
 */
public interface AccidentCluesService {

    int insert(AccidentCluesDTO dto);

    IPage<AccidentCluesPO> selectPageBysql(Page page, AccidentCluesDTO dto);

    int insertCluesFollow(AccidentCluesFollowDTO followDto);

    int insertttCluesAllot(AccidentCluesAllotDTO allotDto);

    AccidentCluesDTO selectById(Integer acId);

    IPage<AccidentCluesFollowPO> selectCluesFollowPageBysql(Page page, Integer acId);

    List<AccidentCluesUserDto> selectCusList(Integer acId);

    String saveWorkNumber(AccidentCluesSaNumberDTO saCustomerNumberDTO);

    List<CallDetailsPO> selectCallDetailByfollowId(Integer followId);

    List<Map> exportExcelAccident(AccidentCluesDTO dto);

    void exportExcelNew( AccidentCluesDTO dto );

    ImportTempResult<AccidentCluesImportPO> importTemp(MultipartFile importFile) throws Exception;

    void batchInsert();

    IPage<AccidentCluesImportPO> selectErrorPage(Page page);

    IPage<AccidentCluesImportPO> selectSuccessPage(Page page);

    AccidentCluesFollowPO selectMaxFollwByAcId(Integer acId);

    void updateTimeOut();

    void updateTimeOutClues();

    void accidentCluesAlert();

    /**
     * 事故线索修改
     * @param clue clue
     * @throws ServiceBizException exception
     */
    void updateAccidentClues(AccidentCluesDTO clue) throws ServiceBizException;

    /**
     * app 事故线索新增
     * @param clue clue
     * @throws ServiceBizException exception
     */
    Integer saveAccidentClues(AccidentCluesDTO clue) throws ServiceBizException;

    /**
     * 事故线索列表查询
     * @param params params
     * @return clues
     * @throws ServiceBizException exc
     */
    IPage<AccidentClueVO> getList(AccidentClueVO params) throws ServiceBizException;

    /**
     * 返回当前经销商未跟进线索数量+继续跟进线索数量
     */
    RepairCountVo count();

    /*
    * 根据图片得到事故线索信息
    * */

    List<EmpByRoleCodeVO> getDealerUser(GetDealerUserDataDTO getDealerUserDTO, boolean b) throws ServiceBizException;

    /*
    * 根据短信内容获取线索信息
    * */
    AccidentClueVo getInfoByContent(String param);

    /**
     * 跟进历史
     * @param acId id
     * @return list
     * @throws ServiceBizException ServiceBizException
     */
    List<AccidentCluesFollowDTO> getFollowList(Integer acId) throws ServiceBizException;

    /**
     * 线索提醒
     */
    void followRemind();

    /**
     * 预约未进厂提醒
     */
    void appointmentTimeOutRemind();

    /**
     * 接收推送线索
     * @param clueList
     * @throws ServiceBizException
     */
    void receiveClueList(List<AccidentCluesDTO> clueList) throws ServiceBizException;

    /**
     * 事故线索-预约单保存
     * @param dto
     * @return
     */
    BookingOrderReturnVo saveAppointmentOrder(AccidentCluesDTO dto) throws ServiceBizException;

    /**
     * 联系人修改
     * @param contact
     * @throws ServiceBizException
     */
    void updateContactInfo(AccidentClueContact contact) throws ServiceBizException;

    /**
     * 事故线索看板
     * @param dto
     * @return
     * @throws ServiceBizException
     */
    AccidentClueDashBoardVO queryAccidentClueDashboard(DashBoardQueryDTO dto) throws ServiceBizException;

    /**
     * 推送跟进状态到litecrm
     */
    void pushLiteCrmClueStatus(List<StatusChangePushDTO> pushInfoList);

    List<AccidentClueFollowVo> followCount(AccidentClueVO params);
}
