package com.yonyou.dmscus.customer.service.inviteInsurance;

/**
 * <p>
 * 车辆邀约续保任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
public interface InviteInsuranceVehicleTaskService {

    /**
     * 根据续保邀约任务创建邀约线索
     * @param createDate
     * @return
     */
    int createInviteInsuranceByTask(String ownerCode, String vin, String createDate);

    /**
     *  创建新车续保任务
     * @param createDate
     * @return
     */
    int insuranceTaskAutoCreate(String createDate);

}
