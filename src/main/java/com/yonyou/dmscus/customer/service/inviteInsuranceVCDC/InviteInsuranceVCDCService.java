package com.yonyou.dmscus.customer.service.inviteInsuranceVCDC;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/9 0009
 */
public interface InviteInsuranceVCDCService {

    IPage<InviteInsuranceVehicleRecordDTO> selectInsuranceVCDCPage(Page page, InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO);

    Map<String,Object> selectVehicleByVin(String vin);

    List<Map> exportExcel( InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO);
}
