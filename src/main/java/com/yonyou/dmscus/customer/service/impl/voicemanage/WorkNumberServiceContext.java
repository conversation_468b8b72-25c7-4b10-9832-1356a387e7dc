package com.yonyou.dmscus.customer.service.impl.voicemanage;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
public class WorkNumberServiceContext {


    @Autowired
    private List<AbstractWorkNumberSerivce> abstractWorkNumberSerivces;

    /**
     * 绑定
     */
    public Map<String, Object> bind(String operator, String extension, String mobilePhone) {

        for (AbstractWorkNumberSerivce abstractWorkNumberSerivce : abstractWorkNumberSerivces) {
            if (abstractWorkNumberSerivce.support(operator)) {
                return abstractWorkNumberSerivce.bind(extension, mobilePhone);
            }
        }
        return null;
    }

    /**
     * 查询绑定关系
     */
    public Map<String, Object> info(String operator, String workNumbe) {

        for (AbstractWorkNumberSerivce abstractWorkNumberSerivce : abstractWorkNumberSerivces) {
            if (abstractWorkNumberSerivce.support(operator)) {
                return abstractWorkNumberSerivce.info(workNumbe);
            }
        }
        return null;
    }

    /**
     * 解绑
     */
    public Map<String, Object> unBind(String operator, String workNumbe) {

        for (AbstractWorkNumberSerivce abstractWorkNumberSerivce : abstractWorkNumberSerivces) {
            if (abstractWorkNumberSerivce.support(operator)) {
                return abstractWorkNumberSerivce.unBind(workNumbe);
            }
        }
        return null;
    }

    /**
     * 呼叫登记
     */
    public Map<String, Object> register(String operator, String callId, String holderNumber, String workNumber,
                                        String customNumber, String userOrderId) {

        for (AbstractWorkNumberSerivce abstractWorkNumberSerivce : abstractWorkNumberSerivces) {
            if (abstractWorkNumberSerivce.support(operator)) {
                return abstractWorkNumberSerivce.register(callId, holderNumber, workNumber, customNumber, userOrderId);
            }
        }
        return null;
    }

    /**
     * 判断各经销商响应状态值是否正常
     */
    public boolean isSuccess(String operator, String methodType, Map<String, Object> result) {

        for (AbstractWorkNumberSerivce abstractWorkNumberSerivce : abstractWorkNumberSerivces) {
            if (abstractWorkNumberSerivce.support(operator)) {
                return abstractWorkNumberSerivce.isSuccess(methodType, result);
            }
        }
        return false;
    }

    /**
     * 组装并获取业务编号
     */
    public String getUserOrderId(String... params) {
        if (null == params || params.length < 1) {
            return UUID.randomUUID().toString().replace("-", "").toLowerCase();
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < params.length; i++) {
            String p = params[i];
            if (null == p) {
                p = "";
            }
            sb.append(p);
            if (i < params.length - 1) {
                sb.append("_");
            }
        }
        String userOrderId = sb.toString();
        if (userOrderId.length() > 128) {
            userOrderId = sb.substring(0, 128);
        }
        return userOrderId;
    }
}
