package com.yonyou.dmscus.customer.service.common.clue;


import com.yonyou.dmscus.customer.dao.common.ClueLogMapper;
import com.yonyou.dmscus.customer.entity.po.common.ClueLogPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/10/20 16:10
 */
@Service
@Slf4j
public class ClueLogServiceImpl implements ClueLogService {
    @Resource
    ClueLogMapper clueLogMapper;

    @Override
    public void add(ClueLogPO po) {
        clueLogMapper.insert(po);
    }
}
