package com.yonyou.dmscus.customer.service.impl.voicemanage;


import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceCustomerInfoDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceCustomerInfoPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.voicemanage.SaCustomerNumberMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.SaWorkNumberMapper;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.SaCustomerNumberDTO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaCustomerNumberPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaWorkNumberPO;
import com.yonyou.dmscus.customer.service.voicemanage.SaCustomerNumberService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.dmscus.customer.utils.ai.DccHttpHelper;
import com.yonyou.dmscus.customer.utils.ai.DccResponseUtil;


/**
 * <p>
 * SA呼叫登记 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Service
public class SaCustomerNumberServiceImpl implements SaCustomerNumberService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    SaCustomerNumberMapper saCustomerNumberMapper;
    @Resource
    SaWorkNumberMapper saWorkNumberMapper;

    @Autowired
    WorkNumberServiceContext workNumberServiceContext;

    @Value("${ai.telecom.common.url_mapping_register}")
    String URL_MAPPING_REGISTER;  //呼叫登记
    /**
     * 分页查询对应数据
     *
     * @param page                分页对象
     * @param saCustomerNumberDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto
                     *   . SaCustomerNumberDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<SaCustomerNumberDTO> selectPageBysql(Page page, SaCustomerNumberDTO saCustomerNumberDTO) {
        if (saCustomerNumberDTO == null) {
            saCustomerNumberDTO = new SaCustomerNumberDTO();
        }
        SaCustomerNumberPO saCustomerNumberPO = saCustomerNumberDTO.transDtoToPo(SaCustomerNumberPO.class);

        List<SaCustomerNumberPO> list = saCustomerNumberMapper.selectPageBySql(page, saCustomerNumberPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<SaCustomerNumberDTO> result = list.stream().map(m -> m.transPoToDto(SaCustomerNumberDTO.class))
                    .collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param saCustomerNumberDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.SaCustomerNumberDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<SaCustomerNumberDTO> selectListBySql(SaCustomerNumberDTO saCustomerNumberDTO) {
        if (saCustomerNumberDTO == null) {
            saCustomerNumberDTO = new SaCustomerNumberDTO();
        }
        SaCustomerNumberPO saCustomerNumberPO = saCustomerNumberDTO.transDtoToPo(SaCustomerNumberPO.class);
        List<SaCustomerNumberPO> list = saCustomerNumberMapper.selectListBySql(saCustomerNumberPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(SaCustomerNumberDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.SaCustomerNumberDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public SaCustomerNumberDTO getById(Long id) {
        SaCustomerNumberPO saCustomerNumberPO = saCustomerNumberMapper.selectById(id);
        if (saCustomerNumberPO != null) {
            return saCustomerNumberPO.transPoToDto(SaCustomerNumberDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param saCustomerNumberDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(SaCustomerNumberDTO saCustomerNumberDTO) {
        //对对象进行赋值操作
        SaCustomerNumberPO saCustomerNumberPO = saCustomerNumberDTO.transDtoToPo(SaCustomerNumberPO.class);
        //执行插入
        int row = saCustomerNumberMapper.insert(saCustomerNumberPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                  主键ID
     * @param saCustomerNumberDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, SaCustomerNumberDTO saCustomerNumberDTO) {
        SaCustomerNumberPO saCustomerNumberPO = saCustomerNumberMapper.selectById(id);
        //对对象进行赋值操作
        saCustomerNumberDTO.transDtoToPo(saCustomerNumberPO);
        //执行更新
        int row = saCustomerNumberMapper.updateById(saCustomerNumberPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = saCustomerNumberMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = saCustomerNumberMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 保存呼叫登记
     * @param saCustomerNumberDTO
     * @return
     */
    @Override
    public String saveSaCustomerNumber(SaCustomerNumberDTO saCustomerNumberDTO) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();

        //uuid
        String callId = Objects.isNull(saCustomerNumberDTO.getCallId()) ? this.getUUID32() : saCustomerNumberDTO.getCallId();

        //校验当前账号是否绑定AI语音工作号
        LambdaQueryWrapper<SaWorkNumberPO> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(SaWorkNumberPO::getDealerCode, loginInfoDto.getOwnerCode());
        queryWrapper.eq(SaWorkNumberPO::getSaId, saCustomerNumberDTO.getSaId());
        SaWorkNumberPO rs = saWorkNumberMapper.selectOne(queryWrapper);

        //校验联系方式格式是否正确 "^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(17[013678])|(18[0,5-9]))\\d{8}$"
        if(11 != saCustomerNumberDTO.getCusNumber().length()){
            throw new DALException("手机号应为11位数");
        }
        if(rs==null){
            throw new DALException("当前账号还未绑定AI语音工作号，不可使用!");
        }
        String operator = rs.getOperator();
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(operator, String.valueOf(CommonConstants.NEW_TELECOM_OPERATOR))) {
            return  operatorIf(saCustomerNumberDTO, callId, rs);
        }


        //调用电信接口进行呼叫登记操作(未实现)失败抛异常回滚


        SaCustomerNumberPO inPo = new SaCustomerNumberPO();
        inPo.setCallId(callId);
        inPo.setInviteId(saCustomerNumberDTO.getInviteId());
        inPo.setSaId(saCustomerNumberDTO.getSaId());
        inPo.setCusName(saCustomerNumberDTO.getCusName());
        inPo.setCusNumber(saCustomerNumberDTO.getCusNumber());
        inPo.setDealerCode(loginInfoDto.getOwnerCode());
        inPo.setSaName(rs.getSaName());
        inPo.setSaNumber(rs.getSaNumber());
        inPo.setWorkNumber(rs.getWorkNumber());
        inPo.setBatchNo(saCustomerNumberDTO.getBatchNo());
        saCustomerNumberMapper.insert(inPo);

        saveInviteInsurance(saCustomerNumberDTO, loginInfoDto);
        //电信 呼叫登记
        Map<String,Object> map =new HashMap<String,Object>();
        map.put("callId", callId);  //登记唯一标识
        map.put("holderNumber", rs.getSaNumber()); //服务顾问手机号
        map.put("workNumber", rs.getWorkNumber()); //工作号
        map.put("customNumber", saCustomerNumberDTO.getCusNumber()); //联系方式
        map.put("expireMinute", 30);  //默认写死30
        logger.info("呼叫登记："+map);
        String str=DccHttpHelper.httpPost(URL_MAPPING_REGISTER, map);
        DccResponseUtil response=JSONObject.parseObject(str,DccResponseUtil.class);
        if(!"0".equals(response.getCode())) {
        	throw new DALException(response.getMessage());
        }
        logger.info("呼叫登记结束："+str);
        //返回绑定的工作号
        return rs.getWorkNumber();
    }

    private void saveInviteInsurance(SaCustomerNumberDTO saCustomerNumberDTO, LoginInfoDto loginInfoDto) {
        List<InviteInsuranceCustomerInfoDTO> customerInfoDTOList = saCustomerNumberMapper.selectAllInsuranceCustomerInfo(saCustomerNumberDTO.getInviteId(),loginInfoDto.getOwnerCode());
        if(CommonUtils.isNullOrEmpty(customerInfoDTOList)){
            InviteInsuranceCustomerInfoPO customerInfoPO = new InviteInsuranceCustomerInfoPO();
            customerInfoPO.setInsuranceId(saCustomerNumberDTO.getInviteId());
            customerInfoPO.setDealerCode(loginInfoDto.getOwnerCode());
            customerInfoPO.setInsureName(saCustomerNumberDTO.getCusName());
            customerInfoPO.setInsureNumber(saCustomerNumberDTO.getCusNumber());
            saCustomerNumberMapper.insertPo(customerInfoPO);
        }else{
            boolean isExist = false;
            for(InviteInsuranceCustomerInfoDTO dto : customerInfoDTOList){
                if((saCustomerNumberDTO.getCusName().equals(dto.getInsureName()) || saCustomerNumberDTO.getCusName() == dto.getInsureName())
                        && (saCustomerNumberDTO.getCusNumber().equals(dto.getInsureNumber()) || saCustomerNumberDTO.getCusNumber() == dto.getInsureNumber())){
                    isExist = true;
                }
            }
            if(!isExist){
                InviteInsuranceCustomerInfoPO customerInfoPO = new InviteInsuranceCustomerInfoPO();
                customerInfoPO.setInsuranceId(saCustomerNumberDTO.getInviteId());
                customerInfoPO.setDealerCode(loginInfoDto.getOwnerCode());
                customerInfoPO.setInsureName(saCustomerNumberDTO.getCusName());
                customerInfoPO.setInsureNumber(saCustomerNumberDTO.getCusNumber());
                saCustomerNumberMapper.insertPo(customerInfoPO);
            }
        }
    }

    String operatorIf(SaCustomerNumberDTO saCustomerNumberDTO,String callId,SaWorkNumberPO rs) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        String   operator = String.valueOf(CommonConstants.NEW_TELECOM_AXB);
        String userOrderId = workNumberServiceContext.getUserOrderId("II", loginInfoDto.getOwnerCode(), saCustomerNumberDTO.getInviteId() + "", callId);
        logger.info("operatorIf II userOrderId:{}", userOrderId);
        Map<String, Object> registerResultMap = workNumberServiceContext.register(operator, callId,
                rs.getSaNumber(), rs.getWorkNumber(), saCustomerNumberDTO.getCusNumber(), userOrderId);
        boolean isSuccessOfRegister = workNumberServiceContext
                .isSuccess(operator, CommonConstants.WORK_NUMBER_MENTHODTYPE_REGISTER, registerResultMap);
        SaCustomerNumberPO inPo = new SaCustomerNumberPO();
        if (!isSuccessOfRegister) {
            String resMessage=registerResultMap.containsKey("message")?registerResultMap.get("message").toString():registerResultMap.get("msg").toString();
            throw new ServiceBizException(resMessage);
        } else {
            logger.info("当前操作供应商==={}，工作号===={}，bindId==={}", operator,
                    rs.getWorkNumber(), inPo.getCallId());
            inPo.setCallId(callId);
            inPo.setInviteId(saCustomerNumberDTO.getInviteId());
            inPo.setSaId(saCustomerNumberDTO.getSaId());
            inPo.setCusName(saCustomerNumberDTO.getCusName());
            inPo.setCusNumber(saCustomerNumberDTO.getCusNumber());
            inPo.setDealerCode(loginInfoDto.getOwnerCode());
            inPo.setSaName(rs.getSaName());
            inPo.setSaNumber(rs.getSaNumber());
            inPo.setWorkNumber(rs.getWorkNumber());
            inPo.setBatchNo(saCustomerNumberDTO.getBatchNo());
            logger.info("====呼叫登记保存信息SaCustomerNumberPO======{}", inPo);
            logger.info("====loginInfo========={}", loginInfoDto);
            saCustomerNumberMapper.insert(inPo);
        }
        return rs.getWorkNumber();
    }

    /**
     * 查询最近的绑定电话
     * @param inviteId
     * @return
     */
    @Override
    public SaCustomerNumberDTO getSaCustomerNumber(Long inviteId) {
        SaCustomerNumberPO po = saCustomerNumberMapper.getSaCustomerNumber(inviteId);
        if(po!=null){
            return po.transPoToDto(SaCustomerNumberDTO.class);
        }
        return null;
    }

    /**
     * 得到32位的uuid
     * @return
     */
    public  String getUUID32(){
        return UUID.randomUUID().toString().replace("-", "").toLowerCase();
    }

    @Override
    public List<InviteInsuranceCustomerInfoDTO> selectAllInsuranceCustomerInfo(Long insuranceId) {
        if(insuranceId != null){
            //获取登录用户信息
            LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
            return saCustomerNumberMapper.selectAllInsuranceCustomerInfo(insuranceId,loginInfoDto.getOwnerCode());
        }else{
            return null;
        }
    }

    @Override
    public void updateInsuranceCustomerInfo(InviteInsuranceCustomerInfoDTO infoDTO) {
        if(infoDTO != null){
            //获取登录用户信息
            LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
            saCustomerNumberMapper.updateInsuranceCustomerInfo(infoDTO,loginInfoDto.getUserId());
        }else{
            throw new DALException("请求参数为空，修改失败!");
        }
    }

    @Override
    public void deleteInsuranceCustomerInfo(Long tiicId) {
        if(tiicId != null){
            saCustomerNumberMapper.deleteInsuranceCustomerInfo(tiicId);
        }else{
            throw new DALException("请求参数为空，删除失败!");
        }
    }
}
