package com.yonyou.dmscus.customer.service.complaint;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentTestDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAttachmentPO;

import java.util.List;

/**
 * <p>
 * 客户投诉附件 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface ComplaintAttachmentService  {
    /**
     * 分页查询
     * @param page
     * @param complaintAttachmentDTO
     * @return
     */
    IPage<ComplaintAttachmentDTO> selectPageBysql(Page page, ComplaintAttachmentDTO complaintAttachmentDTO);

    /**
     * 集合查询
     * @param complaintAttachmentDTO
     * @return
     */
    List<ComplaintAttachmentDTO> selectListBySql(ComplaintAttachmentDTO complaintAttachmentDTO);

    /**
     * 通过id查询
     * @param id
     * @return
     */
    ComplaintAttachmentDTO getById(Long id);

    /**
     * 新增
     * @param complaintAttachmentDTO
     * @return
     */
    int insert(ComplaintAttachmentDTO complaintAttachmentDTO);

    /**
     * 更新
     * @param id
     * @param complaintAttachmentDTO
     * @return
     */
    int update(Long id, ComplaintAttachmentDTO complaintAttachmentDTO);

    /**
     * 通过id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 分页查询
     * @param page
     * @param complaintAttachmentDTO
     * @return
     */
    IPage<ComplaintAttachmentDTO> selectPageBysql1(Page page, ComplaintAttachmentDTO complaintAttachmentDTO);

    /**
     * 集合查询
     * @param complaintAttachmentDTO
     * @return
     */
    List<ComplaintAttachmentTestDTO> selectListBySql1(ComplaintAttachmentDTO complaintAttachmentDTO);

    /**
     * 删除附件
     * @param id
     */
    void delete(Long id);
}
