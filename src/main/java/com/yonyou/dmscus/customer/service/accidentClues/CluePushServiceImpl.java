package com.yonyou.dmscus.customer.service.accidentClues;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.constants.RedisConstants;
import com.yonyou.dmscus.customer.constants.RemotePushSinceTypeEnum;
import com.yonyou.dmscus.customer.constants.RemotePushStatusEnum;
import com.yonyou.dmscus.customer.dao.pushRecord.RemotePushRecordMapper;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesDTO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.ClueInfoPushDTO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.StatusChangePushDTO;
import com.yonyou.dmscus.customer.entity.po.pushRecord.RemotePushRecordPO;
import com.yonyou.dmscus.customer.feign.WhitelistQueryService;
import com.yonyou.dmscus.customer.rocketmq.AccidentCluePushLiteCrmMQTemplate;
import com.yonyou.dmscus.customer.service.pushRecord.RemotePushRecordService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

import org.apache.rocketmq.client.producer.SendResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.retry.RetryContext;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.retry.support.RetrySynchronizationManager;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2023/11/20 11:32
 * @Version 1.0
 */
@Service
@EnableAsync
public class CluePushServiceImpl implements CluePushService {

    @Resource
    private AccidentCluePushLiteCrmMQTemplate accidentCluePushLiteCrmMQTemplate;
    @Resource
    private RemotePushRecordService remotePushRecordService;
    @Resource
    private WhitelistCheckService whitelistCheckService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private RemotePushRecordMapper remotePushRecordMapper;


    @Value("${accidentClue.liteCrm.token.username:nb_accident}")
    private String userName;
    @Value("${accidentClue.liteCrm.token.password:nb123456}")
    private String password;
    @Value("${accidentClue.liteCrm.url.prefix:https://litecrm-uat.digitalvolvo.com}")
    private String prefix;
    @Value("${accidentClue.liteCrm.url.login:/auth/login}")
    private String login;
    @Value("${accidentClue.liteCrm.url.cluePush:/leads/api/v2/post/sale/collect}")
    private String cluePush;
    @Value("${accidentClue.liteCrm.rocketmq.topic:topic_after_sale_leads}")
    private String liteCrmPushTopic;

    @Resource
    private WhitelistQueryService whitelistQueryService;
    @Autowired
    private LiteCrmService liteCrmService;

    Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 推送事故线索
     * @param clueInfo
     * @throws ServiceBizException
     */
    @Override
    @Retryable(value = {AccidentCluePushFailException.class}, maxAttempts = 3, backoff = @Backoff(delay = 500))
    public void pushLiteCrmClueInfo(AccidentCluesDTO clueInfo) throws ServiceBizException {

//        if (!whitelistCheckService.checkWhitelist(clueInfo.getDealerCode())){
//            logger.info("非白名单经销商不推送线索");
//            return;
//        }
//
//        //黑名单
//        Boolean blacklistCheck = whitelistQueryService.checkWhitelist(clueInfo.getDealerCode(), CommonConstants.ACCIDENT_LEAD_TYPE, 1, "");
//        if (blacklistCheck) {
//            logger.info("push lite crm clue refused by blacklist,{}:{}", clueInfo.getAcId(), clueInfo.getVin());
//            return;
//        }

        logger.info("**********开始获取推送事故线索**********");
        ClueInfoPushDTO pushInfo = new ClueInfoPushDTO(clueInfo);
        if (!this.checkPushInfo(pushInfo)){
            logger.info("参数不满足推送条件，结束推送");
            return;
        }
        String pushParams = pushInfo.getPushParams(pushInfo, clueInfo);
        logger.info("事故线索推送参数 ===>> {}", pushParams);

        String url = prefix + cluePush;
        logger.info("push accident clue url: {}", url);
        RestTemplate restTemplate = new RestTemplate();
        RetryContext context = RetrySynchronizationManager.getContext();
        int retryCount = context.getRetryCount();
        logger.info("**********事故线索第 {} 次推送**********", retryCount);
        String returnMsg = null;
        try {
            HttpEntity<Object> httpEntity = new HttpEntity<>(pushParams, this.buildHeader());
            ResponseEntity<JSONObject> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, JSONObject.class);
            logger.info("事故线索推送结果: {}", response);
            Assert.isTrue(!ObjectUtils.isEmpty(response) && !ObjectUtils.isEmpty(response.getBody()), "服务调用异常！");
            returnMsg = response.getBody().toJSONString();
            Assert.isTrue(ObjectUtils.nullSafeEquals(response.getBody().get("code"), 0), "推送失败！");
            remotePushRecordService.accidentClueLiteCrmPushRecord(pushParams, returnMsg, clueInfo.getAcId(), RemotePushSinceTypeEnum.CLUE_INFO, RemotePushStatusEnum.STATUS_SUCCESS);
        } catch (Exception e) {
            remotePushRecordService.accidentClueLiteCrmPushRecord(pushParams, returnMsg, clueInfo.getAcId(), RemotePushSinceTypeEnum.CLUE_INFO, RemotePushStatusEnum.STATUS_FAIL);
            throw new AccidentCluePushFailException("事故线索信息消息推送失败，原因：" + e.getMessage());
        }
    }

    /**
     * 线索状态变更推送
     * @param pushInfo
     * @throws ServiceBizException
     */
    @Override
    @Retryable(value = {AccidentCluePushFailException.class}, maxAttempts = 3, backoff = @Backoff(delay = 500))
    public void pushLiteCrmClueStatus(StatusChangePushDTO pushInfo) throws ServiceBizException {
        RetryContext context = RetrySynchronizationManager.getContext();
        int retryCount = context.getRetryCount();
        logger.info("**********事故线索状态第 {} 次推送**********", retryCount);
        String body = JSON.toJSONString(pushInfo);
        SendResult result = null;
        String followUpStatus = pushInfo.getFollowUpStatus();
        if (StringUtils.isBlank(followUpStatus)) {
            followUpStatus = "0";
        }
        try {
            result = this.push(body);
            remotePushRecordService.accidentClueLiteCrmPushRecord(body, JSONObject.toJSONString(result), Integer.parseInt(pushInfo.getSourceClueId()), this.findRecordSinceType(Integer.parseInt(followUpStatus)), RemotePushStatusEnum.STATUS_SUCCESS);
        }catch (Throwable e){
            logger.info("事故线索状态变更推送失败,原因：{}", e.getMessage());
            remotePushRecordService.accidentClueLiteCrmPushRecord(body, JSONObject.toJSONString(result), Integer.parseInt(pushInfo.getSourceClueId()), this.findRecordSinceType(Integer.parseInt(followUpStatus)), RemotePushStatusEnum.STATUS_FAIL);
            throw new AccidentCluePushFailException(e.getMessage());
        }
    }

    /**
     * 定时任务补偿推送
     * @param limit
     * @param maxSize
     * @param loopTimes
     * @param pageSize
     * @throws ServiceBizException
     */
    @Async("acThreadPool")
    public void compensatePush(int limit, int maxSize, int loopTimes, int pageSize) throws ServiceBizException {

        logger.info("线程：{} 开始补偿数据", Thread.currentThread().getName());
        for (int i = 0; i < loopTimes; i++) {

            this.doCompensate(remotePushRecordMapper.queryAcRemotePushCompensateList(RemotePushSinceTypeEnum.CLUE_INFO.getSinceType(), limit, pageSize));
            //每次循环分页参数动态变化，最后一次页码
            limit = limit + pageSize;
            pageSize = loopTimes - i == 2 ? maxSize - limit : pageSize;
        }
    }



    /**
     * 补偿
     * @param compensateList
     * @throws ServiceBizException
     */
    private void doCompensate(List<RemotePushRecordPO> compensateList) throws ServiceBizException{

        if (CollectionUtils.isEmpty(compensateList)){
            logger.info("补偿列表数据为空，结束补偿");
            return;
        }
        for (RemotePushRecordPO record : compensateList) {
            if (ObjectUtils.nullSafeEquals(RemotePushSinceTypeEnum.CLUE_INFO.getSubSinceType(), record.getSubSinceType())){
                logger.info("补偿推送线索信息：{}", record);
                this.compensateClueInfo(record);
                continue;
            }
            logger.info("补偿推送线索状态：{}", record);
            SendResult result = null;
            try {
                result = this.push(record.getReqParams());
                Assert.isTrue(!ObjectUtils.isEmpty(result), "线索状态补偿推送失败！");
                remotePushRecordService.updateAcCompensateRecord(record, JSONObject.toJSONString(result), RemotePushStatusEnum.STATUS_SUCCESS);
            }catch (Exception e){
                logger.info("事故线索状态补偿推送失败！原因：{}", e.getMessage());
                remotePushRecordService.updateAcCompensateRecord(record, JSONObject.toJSONString(result), RemotePushStatusEnum.STATUS_FAIL);
            }
        }
    }

    /**
     * 获取推送枚举
     * @param followStatus
     * @return
     */
    private RemotePushSinceTypeEnum findRecordSinceType(Integer followStatus) {

        switch (followStatus){
            case CommonConstants.CLUE_FOLLOW_STATUS_UP:
                return RemotePushSinceTypeEnum.CLUE_STATUS_FOLLOW_UP;
            case CommonConstants.CLUE_FOLLOW_STATUS_SUCCESS:
                return RemotePushSinceTypeEnum.CLUE_STATUS_FOLLOW_SUCCESS;
            case CommonConstants.CLUE_FOLLOW_STATUS_FAIL:
                return RemotePushSinceTypeEnum.CLUE_STATUS_FOLLOW_FAIL;
            case CommonConstants.RENEWAL_INSURANCE_STATUS_SUCCES:
                return RemotePushSinceTypeEnum.RENEWAL_INSURANCE_STATUS_SUCCES;
            case CommonConstants.RENEWAL_INSURANCE_STATUS_FAIL:
                return RemotePushSinceTypeEnum.RENEWAL_INSURANCE_STATUS_FAIL;
            case CommonConstants.RENEWAL_INSURANCE_STATUS_KEEP:
                return RemotePushSinceTypeEnum.RENEWAL_INSURANCE_STATUS_KEEP;
            case CommonConstants.NUM_0:
                return RemotePushSinceTypeEnum.CLUE_STATUS_FOLLOW_NONE;
            default:
                return RemotePushSinceTypeEnum.CLUE_STATUS_FOLLOW_NOT_START;
        }
    }

    /**
     * mq推送线索状态
     * @param body
     * @return
     */
    private SendResult push(String body){

        SendResult sendResult = accidentCluePushLiteCrmMQTemplate.syncSend(liteCrmPushTopic, new Message<String>() {
            @Override
            public String getPayload() {
                return body;
            }
            @Override
            public MessageHeaders getHeaders() {
                HashMap<String, Object> headers = Maps.newHashMap();
                return new MessageHeaders(headers);
            }
        });
        logger.info("事故线索状态变更推送结果：{}", sendResult);

        return sendResult;
    }

    /**
     * 校验线索必填项
     * @param pushInfo
     * @return
     */
    private boolean checkPushInfo(ClueInfoPushDTO pushInfo){

        return pushInfo.checkFieldsNotNull(pushInfo);
    }



    /**
     * 构建事故线索推送请求头
     * @return
     */
    private HttpHeaders buildHeader() {

        String token = liteCrmService.getCrmToken();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
        httpHeaders.set(HttpHeaders.AUTHORIZATION, token);
        return httpHeaders;
    }

    /**
     * 补偿线索信息推送
     * @param record
     */
    private void compensateClueInfo(RemotePushRecordPO record){

        String url = prefix + cluePush;
        logger.info("事故线索定时补偿推送线索信息url: {}", url);
        String returnMsg = null;
        RestTemplate restTemplate = new RestTemplate();
        String pushParams = JSONObject.toJSONString(record.getReqParams());
        logger.info("线索信息补偿推送参数：{}", pushParams);
        HttpEntity<Object> httpEntity = new HttpEntity<>(pushParams, this.buildHeader());
        try {
            ResponseEntity<JSONObject> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, JSONObject.class);
            logger.info("事故线索补偿推送结果: {}", response);
            Assert.isTrue(!ObjectUtils.isEmpty(response) && !ObjectUtils.isEmpty(response.getBody()), "服务调用异常！");
            returnMsg = response.getBody().toJSONString();
            Assert.isTrue(ObjectUtils.nullSafeEquals(response.getBody().get("code"), 0), "推送失败！");
            remotePushRecordService.updateAcCompensateRecord(record, returnMsg, RemotePushStatusEnum.STATUS_SUCCESS);
        } catch (Exception e) {
            remotePushRecordService.updateAcCompensateRecord(record, returnMsg, RemotePushStatusEnum.STATUS_FAIL);
            throw new AccidentCluePushFailException("事故线索补偿推送失败，原因：" + e.getMessage());
        }
    }
}

class AccidentCluePushFailException extends ServiceBizException {

    public AccidentCluePushFailException(Exception e) {
        super(e);
    }

    public AccidentCluePushFailException(String msg) {
        super(msg);
    }

    public AccidentCluePushFailException(String msg, Serializable exceptionData) {
        super(msg, exceptionData);
    }

    public AccidentCluePushFailException(String msg, Exception e) {
        super(msg, e);
    }
}
