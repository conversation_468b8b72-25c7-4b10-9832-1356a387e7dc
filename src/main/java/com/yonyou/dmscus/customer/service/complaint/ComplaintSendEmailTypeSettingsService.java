package com.yonyou.dmscus.customer.service.complaint;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailTypeSettingsDTO;

import java.util.List;

/**
 * <p>
 * 客户投诉发送邮件类型设定 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
public interface ComplaintSendEmailTypeSettingsService  {
    /**
     * 分页查询
     * @param page
     * @param complaintSendEmailTypeSettingsDTO
     * @return
     */
    IPage<ComplaintSendEmailTypeSettingsDTO> selectPageBysql(Page page, ComplaintSendEmailTypeSettingsDTO complaintSendEmailTypeSettingsDTO);

    /**
     * 集合查询
     * @param complaintSendEmailTypeSettingsDTO
     * @return
     */
    List<ComplaintSendEmailTypeSettingsDTO> selectListBySql(ComplaintSendEmailTypeSettingsDTO complaintSendEmailTypeSettingsDTO);

    /**
     * 通过id查询
     * @param id
     * @return
     */
    ComplaintSendEmailTypeSettingsDTO getById(Long id);

    /**
     * 新增
     * @param complaintSendEmailTypeSettingsDTO
     * @return
     */
    int insert(ComplaintSendEmailTypeSettingsDTO complaintSendEmailTypeSettingsDTO);

    /**
     * 更新
     * @param id
     * @param complaintSendEmailTypeSettingsDTO
     * @return
     */
    int update(Long id, ComplaintSendEmailTypeSettingsDTO complaintSendEmailTypeSettingsDTO);

    /**
     * 通过id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

}
