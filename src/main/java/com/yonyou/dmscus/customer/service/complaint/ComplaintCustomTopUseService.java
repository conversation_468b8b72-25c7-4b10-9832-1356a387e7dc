package com.yonyou.dmscus.customer.service.complaint;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomTopUseDTO;

import java.util.List;

/**
 * <p>
 * 客户投诉自定义置顶使用表每个人不同对应不同 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
public interface ComplaintCustomTopUseService  {
    /**
     * 分页查询
     * @param page
     * @param complaintCustomTopUseDTO
     * @return
     */
    IPage<ComplaintCustomTopUseDTO> selectPageBysql(Page page, ComplaintCustomTopUseDTO complaintCustomTopUseDTO);

    /**
     * 集合查询
     * @param complaintCustomTopUseDTO
     * @return
     */
    List<ComplaintCustomTopUseDTO> selectListBySql(ComplaintCustomTopUseDTO complaintCustomTopUseDTO);

    /**
     * 通过id查询
     * @param id
     * @return
     */
    ComplaintCustomTopUseDTO getById(Long id);

    /**
     * 新增
     * @param complaintCustomTopUseDTO
     * @return
     */
    int insert(ComplaintCustomTopUseDTO complaintCustomTopUseDTO);

    /**
     *
     * 更新
     * @param id
     * @param complaintCustomTopUseDTO
     * @return
     */
    int update(Long id, ComplaintCustomTopUseDTO complaintCustomTopUseDTO);

    /**
     * 通过id查询
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 新增置顶
     * @param complaintCustomFieldTestDTO
     * @return
     */
    int insertTop(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO);
}
