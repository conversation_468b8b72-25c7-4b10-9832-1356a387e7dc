package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dto.LossDataRecordOss;
import com.yonyou.dmscus.customer.dto.LossDataRecordVo;
import com.yonyou.dmscus.customer.entity.dto.loss.LossDataRecordDto;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.LossDataRecordPo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: LossDataRecordService
 * @projectName dmscus.customer
 * @date 2022/12/1217:45
 */
public interface LossDataRecordService {
    int  save(LossDataRecordPo po );

    LossDataRecordPo selectByVin(String vin);

    int update(LossDataRecordPo po);

    void insertList(List<LossDataRecordPo> pos);

    IPage<LossDataRecordDto> selectPageBysql(Page<LossDataRecordVo> page, LossDataRecordVo lossDataRecordDto);

    List<Map> exportExcel(LossDataRecordVo dto);

    void exportExcelOss(LossDataRecordOss dto);

}
