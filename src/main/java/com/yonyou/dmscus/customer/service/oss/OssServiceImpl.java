package com.yonyou.dmscus.customer.service.oss;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.obs.services.ObsClient;
import com.obs.services.model.ObsObject;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.entity.dto.voc.VocParm;
import com.yonyou.dmscus.customer.entity.po.btnlog.BtnLogPO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusLogPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusRecordPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataLogPo;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataRecordPo;
import com.yonyou.dmscus.customer.enums.VocEnum;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.feign.WhitelistQueryService;
import com.yonyou.dmscus.customer.service.CommonService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.invitationautocreate.InviteVehicleTaskService;
import com.yonyou.dmscus.customer.service.vocbtnlog.BtnLogService;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocInviteVehicleTaskRecordService;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: OssServiceImpl
 * @projectName dmscus.customer
 * @description:
 * @date 2022/11/116:56
 */
@Service
public class OssServiceImpl implements OssService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    CommonService commonService;
    @Autowired
    BtnLogService btnLogService;
    @Autowired
    OssWorkService ossWorkService;

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    ReportCommonClient reportCommonClient;
    @Autowired
    InviteVehicleRecordService inviteVehicleRecordService;
    @Autowired
    InviteVehicleTaskService inviteVehicleTaskService;
    @Autowired
    VocInviteVehicleTaskRecordService vocInviteVehicleTaskRecordService;

    //voc oss keyid
    @Value("${voc.aliyun.oss.accessKeyId}")
    private String accessKeyId;

    @Value("${voc.aliyun.oss.accessKeySecret}")
    private String accessKeySecret;

    @Value("${voc.aliyun.oss.bucketName}")
    private String bucketName;

    @Value("${voc.aliyun.oss.endpoint}")
    private String endpoint;

    @Value("${voc.aliyun.oss.status}")
    private String key2;

    @Value("${voc.aliyun.oss.daily}")
    private String key;


    @Value("${voc.task.lossdate:-3}")
    private Integer lossDate;

    @Autowired
    private ApplicationEventPublisher eventPublisher;
    @Autowired
    WhitelistQueryService whitelistQueryService;

    //=================================================激活数据拉取===================================================//
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void downLoadVocFunctionalStatus(String data){

        logger.info("voc激活定时任务开始downLoadVocFunctionalStatus");
        long curTime1 = System.nanoTime();
        //T+2
        data = getDate(data, "voc激活定时任务开始获取数据时间:{}");
        //查询是否已经成功拉取数据
        if (this.selectLog(data, 1, 0)) {
            logger.info("voc激活定时任务结束获取数据时间:{}，已拉取", data);
            return;
        }
        //日志表
        BtnLogPO po = getBtnLogPO(data, 1, "激活文件开始执行");
        logger.info("voc激活定时任务开始获取数据时间:{}", data);
        String s2 = data + "/" + key2 + data + ".csv";
        //激活
        ObsClient obsClient = this.getObs();
        Boolean b = this.doesObjectExist(s2, obsClient);
        logger.info("voc激活定时任务开始判断OSS文件存不存在:{},{}", b, s2);
        if (!b) {
            //不存在5
            //发邮件
            //报错日志表
            po.setIsSc(2);
            po.setFileUrl(s2);
            po.setMess(data + "文件不存在");
            this.update(po);
            return;
        }
        //获取文件并转换对象
        ObsObject o = this.getObsObject(s2, obsClient);
        if (ObjectUtils.isEmpty(o)) {
            //发邮件
            //报错日志表
            po.setIsSc(1);
            po.setMess("oss获取数据返回为null");
            this.update(po);
            return;
        }

        List<VocFunctionalStatusLogPO> logPOSList = new ArrayList();
        // 使用缓冲流读取  设置编码格式
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(o.getObjectContent(), "utf-8"))) {
            // 读取csv文件
            CSVParser parser = getCsvRecords(reader);
            //遍历每一行数据
            List<CSVRecord> csvRecords = parser.getRecords();
            //第一行数据是字段名称   跳过
            if (CollectionUtils.isEmpty(csvRecords)) {
                return;
            }
            for (int i = 1; i < csvRecords.size(); i++) {
                CSVRecord record = csvRecords.get(i);
                //组装数据
                VocFunctionalStatusLogPO polog = new VocFunctionalStatusLogPO();
                this.assemble(record, polog);
                logPOSList.add(polog);
            }
            // 数据读取完成后，获取的流必须关闭，否则会造成连接泄漏，导致请求无连接可用，程序无法正常工作。
        } catch (Exception e) {
            //发邮件
            //报错日志表
            po.setIsSc(1);
             getMess(po,e);
            this.update(po);
            return;
        } finally {
            //关闭连接
            try {
                obsClient.close();
            } catch (IOException e) {
                logger.error("oss 关闭异常",e);
            }
        }

        if ( CollUtil.isEmpty(logPOSList)) {
            //发邮件
            //报错日志表

            po.setIsSc(2);
            po.setMess( CommonConstants.LOG_S24);
            this.update(po);
            return;
        } else  {
            //保存流水表
            try {
                ossWorkService.insertListLog(logPOSList);
            } catch (Exception e) {
                logger.info("voc激活定时任务开始保存数据报错:{},{}", b, s2);
                //报错日志表
                po.setIsSc(1);
                getMess(po, e);
                this.update(po);
                return;
            }
        }
        po.setIsSc(0);
        this.update(po);
        logger.info("voc激活定时任务结束耗时ns:{}", System.nanoTime() - curTime1);
    }

    private BtnLogPO getBtnLogPO(String data, int i2, String context) {
        BtnLogPO po = new BtnLogPO();
        po.setUpdateTime(data);
        po.setIsSc(1);
        po.setFileUrl(data);
        po.setDataType(i2);
        po.setMess(data + context);
        this.saveLog(po);
        return po;
    }

    private String getDate(String data, String s) {
        if (StringUtils.isEmpty(data)) {
            data = DateUtils.getBeforeDateStrParm(new Date(), 2, DateUtils.YYYYMMDD);
            logger.info(s, data);
        }
        return data;
    }

    private void getMess(BtnLogPO po, Exception e) {
        String mess = e.getMessage();
        if(mess.length()>90){
            po.setMess(mess.substring(0,90));
        }else{
            po.setMess(mess);
        }
    }
    @Override
    public void run() {
        run(null);
    }    //==============================开口=================================================//
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void run(String data) {
        String data1 = StringUtils.isEmpty(data) ? DateUtils.getBeforeDateStrParm(new Date(), 2, DateUtils.YYYYMMDD) : data;
        ExecutorService service = Executors.newFixedThreadPool(2);

        // 闭锁，等待所有任务执行完成
        final CountDownLatch latch = new CountDownLatch(2);
        logger.info("开启闭锁={}，page={}", latch, 2);
        // 依据页码创建任务数量
        Runnable task = () -> {
            try {
                downLoadVocFunctionalStatus(data1);
            } catch (Exception e) {
                logger.error(">>>voc激活异常:{}", e);
            } finally {
                latch.countDown();
            }
        };
        Runnable task2 = ()-> {
                try {
                    downLoadVocwarningdaily(data1);
                } catch (Exception e) {
                    logger.error(">>>voc亮灯异常:{}", e);
                } finally {
                    latch.countDown();
                }
        };
        shutService(service, latch, task, task2);
        logger.info("voc数据结束");
        //发实践（异步处理业务数据）
        this.getcontent(data1);
    }

    //=================================================亮灯数据拉取===================================================//
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void downLoadVocwarningdaily(String data){
        long curTime1 = System.nanoTime();
        logger.info("VOC亮灯定时任务开始downLoadVocwarningdaily");
        //T+2
        data = getDate(data, "VOC亮灯定时任务开始获取数据时间:{}");
        if (this.selectLog(data, 0, 0)) {
            logger.info("VOC亮灯定时任务结束获取数据时间:{}，已拉取", data);
            return;
        }
        //日志表
        BtnLogPO po = getBtnLogPO(data, 0, "激活文件开始执行");
        logger.info("VOC亮灯定时任务开始获取数据时间:{}", data);
        String s2 = data + "/" + key + data + ".csv";
        //亮灯_20221026.csv
        ObsClient oss = this.getObs();
        Boolean b = this.doesObjectExist(s2, oss);
        logger.info("VOC亮灯定时任务开始判断OSS文件存不存在:{},{}", b, s2);
        if (!b) {
            //不存在
            //发邮件
            //报错日志表
            po.setIsSc(2);
            po.setFileUrl(s2);
            po.setDataType(0);
            po.setMess(data + "文件不存在");
            this.update(po);
            return;
        }
        //获取文件并转换对象
        ObsObject o = this.getObsObject(s2, oss);
        if (ObjectUtils.isEmpty(o)) {
            //发邮件
            //报错日志表
            po.setIsSc(2);
            po.setFileUrl(s2);
            po.setDataType(0);
            po.setMess("oss获取数据返回为null");
            this.update(po);
            return;
        }

        List<VocWarningDataLogPo> logPOSList = new ArrayList();
        // 使用缓冲流读取  设置编码格式
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(o.getObjectContent(), "utf-8"))) {
            // 读取csv文件
            CSVParser parser = getCsvRecordss(reader);
            //遍历每一行数据
            List<CSVRecord> csvRecords = parser.getRecords();
            //第一行数据是字段名称   跳过
            if (CollectionUtils.isEmpty(csvRecords)) {
                return;
            }
            for (int i = 1; i < csvRecords.size(); i++) {
                CSVRecord record = csvRecords.get(i);
                //组装数据
                VocWarningDataLogPo polog = new VocWarningDataLogPo();
                this.assemble(record, polog);
                logPOSList.add(polog);
            }
            // 数据读取完成后，获取的流必须关闭，否则会造成连接泄漏，导致请求无连接可用，程序无法正常工作。
        } catch (Exception e) {
            //发邮件
            //报错日志表
            po.setIsSc(1);
            getMess(po,e);
            this.update(po);
            return;
        } finally {
            //关闭连接
            try {
                oss.close();
            } catch (IOException e) {
                logger.error("oss 关闭异常",e);
            }
        }

        if (sb1(logPOSList)  ) {
            //发邮件
            //报错日志表
            po.setIsSc(2);
            po.setMess( CommonConstants.LOG_S24);
            this.update(po);
            return;
        }
        if (logPOSList.isEmpty()) {
            //发邮件
            //报错日志表
            po.setIsSc(2);
            po.setMess( CommonConstants.LOG_S24);
            this.update(po);
            return;
        }
        if (CollUtil.isNotEmpty(logPOSList)) {
            //保存流水表
            try {
                ossWorkService.insertListWarningDataLog(logPOSList);
            } catch (Exception e) {
                logger.info("VOC亮灯定时任务开始保存数据报错:{},{}", b, s2);
                //报错日志表
                po.setIsSc(1);
                getMess(po,e);
                this.update(po);
                return;
            }
        }
        po.setIsSc(0);
        this.update(po);
        logger.info("VOC亮灯定时任务结束耗时ns:{}", System.nanoTime() - curTime1);
    }

    private boolean sb1(List<VocWarningDataLogPo> logPOSList) {
        return  logPOSList == null;
    }
//===========================================数据写库======================================================//
    /**
     * 从流水表拉取数据到业务表
     *
     * @param data
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void getcontent(String data) {
        String data1 = StringUtils.isEmpty(data) ? DateUtils.getBeforeDateStrParm(new Date(), 2, DateUtils.YYYYMMDD) : data;
        //解耦，从流水表拉取数据
        ExecutorService service = Executors.newFixedThreadPool(2);
        // 闭锁，等待所有任务执行完成
        final CountDownLatch latch = new CountDownLatch(2);
        logger.info("getcontent开启闭锁={}，page={}", latch, 2);
        // 依据页码创建任务数量
        Runnable task = ()-> {
                try {
                    vocFunctionalStatusDataClean(data1);
                } catch (Exception e) {
                    logger.error(">>>voc激活异常:{}", e);
                } finally {
                    latch.countDown();
                }
        };
        Runnable task2 = ()-> {
                try {
                    vocwarningdailyDataClean(data1);
                } catch (Exception e) {
                    logger.error(">>>voc亮灯异常:{}", e);
                } finally {
                    latch.countDown();
                }
        };
        shutService(service, latch, task, task2);
        logger.info("voc数据清洗结束");
        //调线索生成业务
        this.vocdata((DateUtils.dateToString(new Date(), DateUtils.YYYYMMDD)));
    }

    private void shutService(ExecutorService service, CountDownLatch latch, Runnable task, Runnable task2) {
        service.submit(task);
        service.submit(task2);
        try {
            latch.await();
        } catch (Exception e) {
            logger.info("{}", e);
        }
        if (sb(service)) {
            service.shutdown();
        }
    }

    private boolean sb(ExecutorService service) {
       return null != service;
    }


    //增量数据写库
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void vocFunctionalStatusDataClean(String data) {

        logger.info("voc激活增量数据写库定时任务开始vocFunctionalStatusDataClean:{}", data);
        //T+2
        data = getDateTime(data, "voc激活增量数据写库定时任务开始获取数据时间:{}");
        long curTime1 = System.nanoTime();
        BtnLogPO po = getBtnLogPO(data, 2, "激活文件写库开始执行");
        //
        /*if (this.selectLog(data, 2, 0)) {
            logger.info("voc激活增量数据写库定时任务结束获取数据时间:{}，已拉取", data);
            return;
        }*/
        //判断传入时间和数据库数据时间 如果数据库数据时间大于等于传入则不改，如果小于update ,如果不存在 则直接新增 。
        //分页查询流水表清洗完之后插入业务表
        //分页查询
        Integer returnCount = 0;
        Integer partitionSize = 2000;
        Integer start = 0;
        long startTimeMillis = System.currentTimeMillis();
        logger.info("voc激活增量数据写库定时任务,createDate:{},startTimeMillis:{}", data, startTimeMillis);
        List<VocFunctionalStatusLogPO> list = null;
        int issc = 0;
        while (true) {
            Integer begIndex = start * partitionSize;
            logger.info("voc激活增量数据写库定时任务,createDate:{},begIndex:{},partitionSize:{}", data, begIndex, partitionSize);
            this.update(po);
            list = this.selectVocFunctionalStatusLogLog(data, start * partitionSize, partitionSize);
            if (list == null || list.isEmpty()) {
                if (begIndex == 0) {
                    issc = 2;
                }
                logger.info("voc激活增量数据写库定时任务,执行完成结束");
                break;
            } else {
                List<VocFunctionalStatusRecordPO> insertList = new ArrayList(list.size());
                List<VocFunctionalStatusRecordPO> updateList = new ArrayList(list.size());
                Map<String, VocFunctionalStatusRecordPO> map = new HashMap<>(list.size());
                start++;
                logger.info("voc激活增量数据写库定时任务,start:{},size:{}", start, list.size());
                //判断viN在业务表存不存在存在修改，不存在新增
                // 存在时 判断vin的dt时间和业务表的跟新时间大小

                getVocfFunctionStatus(map, list);
                //查询结束后开始处理数据
                // list ==>map 根据时间保留最新的时间的数据
                // 加map 每一千查询一次业务表获取VIN 存到MAP，

                pinjieData(list, insertList, updateList, map);

                //保存流水表
                try {
                    insertVocFun(insertList, updateList);
                    returnCount += 1;
                    logger.info("voc激活增量数据写库定时任务,returnCount:{}", returnCount);
                } catch (Exception e) {
                    logger.info("voc激活增量数据写库定时任务结束保存报错:{}", e);
                    //报错日志表
                    po.setIsSc(1);
                    getMess(po,e);
                    this.update(po);
                    return;
                }

            }
        }
        po.setIsSc(issc);
        if (po.getIsSc() == 2) {
            po.setMess("激活文件写库数据为空");
        }
        this.update(po);
        logger.info("voc激活增量数据写库定时任务结束耗时ns:{}", System.nanoTime() - curTime1);
    }

    private void insertVocFun(List<VocFunctionalStatusRecordPO> insertList, List<VocFunctionalStatusRecordPO> updateList) {
        if (insertList != null && !insertList.isEmpty()) {
            ossWorkService.insertList(insertList);
        }
        if (updateList != null && !updateList.isEmpty()) {
            ossWorkService.updateRecord(updateList);
        }
    }

    private void pinjieData(List<VocFunctionalStatusLogPO> list, List<VocFunctionalStatusRecordPO> insertList, List<VocFunctionalStatusRecordPO> updateList, Map<String, VocFunctionalStatusRecordPO> map) {
        for (VocFunctionalStatusLogPO logPO : list) {
            if (map.containsKey(logPO.getVin())) {
                String dt = logPO.getDt();
                VocFunctionalStatusRecordPO recordPO = map.get(logPO.getVin());
                String updatetime = recordPO.getUpdateTime();
                logger.info("voc激活增量数据写库定时任务开始vin:{},dt:{},updatetime:{}", logPO.getVin(), dt, updatetime);
                //判断dt 是否在updatetime 之后
                updateVocFunctionalStatus(updateList, logPO, dt, recordPO, updatetime);
            } else {
                VocFunctionalStatusRecordPO recordPOIns = new VocFunctionalStatusRecordPO();
                recordPOIns.setVin(logPO.getVin());
                this.setVocFuntionalStatusPo(logPO, recordPOIns);
                insertList.add(recordPOIns);
            }
        }
    }

    private void updateVocFunctionalStatus(List<VocFunctionalStatusRecordPO> updateList, VocFunctionalStatusLogPO logPO, String dt, VocFunctionalStatusRecordPO recordPO, String updatetime) {
        if (dt.equals(updatetime)) {
            String ad = logPO.getActivatedState();
            Integer ac = recordPO.getActivatedState();
            //1,激活；0,未激活
            if (!ad.equals(ac == 0 ? "未激活" : "激活")) {
                this.setVocFuntionalStatusPo(logPO, recordPO);
                updateList.add(recordPO);
            }
        } else {
            if (DateUtils.compareTo(dt, updatetime, DateUtils.YYYYMMDD)) {
                this.setVocFuntionalStatusPo(logPO, recordPO);
                updateList.add(recordPO);
            }
        }
    }

    private void getVocfFunctionStatus(Map<String, VocFunctionalStatusRecordPO> map,  List<VocFunctionalStatusLogPO> list) {
        List<List<VocFunctionalStatusLogPO>> x = Lists.partition(list, 1000);
        for (List<VocFunctionalStatusLogPO> xx : x) {
            //1000一次查询已经保存的结果结果放入MAP
            List<VocFunctionalStatusRecordPO> selectList = ossWorkService.selectListStatusRecord(xx);
            if (selectList != null && !selectList.isEmpty()) {
                for (VocFunctionalStatusRecordPO pox : selectList) {
                    map.put(pox.getVin(), pox);
                }
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void vocwarningdailyDataClean(String data) {
        long curTime1 = System.nanoTime();
        logger.info("VOC亮灯增量数据写库定时任务开始vocwarningdailyDataCleandata:{}", data);
        //T+2
        data = getDateTime(data, "VOC亮灯增量数据写库定时任务开始获取数据时间:{}");
        BtnLogPO po = getBtnLogPO(data, 3, "亮灯文件写库开始执行");
        //
        /*if (this.selectLog(data, 3, 0)) {
            logger.info("VOC亮灯增量数据写库定时任务结束获取数据时间:{}，已拉取", data);
            return;
        }*/
        //分页查询流水表清洗完之后插入业务表
        //分页查询
        int issc = 0;
        Integer returnCount = 0;
        Integer partitionSize = 2000;
        Integer start = 0;
        long startTimeMillis = System.currentTimeMillis();
        logger.info("VOC亮灯增量数据写库定时任务,createDate:{},startTimeMillis:{}", data, startTimeMillis);
        List<VocWarningDataLogPo> list = null;
        while (true) {
            Integer begIndex = start * partitionSize;
            logger.info("VOC亮灯增量数据写库定时任务,createDate:{},begIndex:{},partitionSize:{}", data, begIndex, partitionSize);
            this.update(po);
            //判断传入时间和数据库数据时间 如果数据库数据时间大于等于传入则不改，如果小于update ,如果不存在 则直接新增 。
            list = this.selectWarningdailyLog(data, start * partitionSize, partitionSize);
            if (list != null && !list.isEmpty()) {
                start++;
                //新增数据
                List<VocWarningDataRecordPo> insertList = new ArrayList(list.size());
                //修改数据
                List<VocWarningDataRecordPo> updateList = new ArrayList(list.size());
                //数据库判断数据
                Map<String, VocWarningDataRecordPo> map = new HashMap<>(list.size());
                //判断viN在业务表存不存在存在修改，不存在新增
                // 存在时 判断vin的dt时间和业务表的跟新时间大小
                getVocWarn(list, map);
                //查询结束后开始处理数据
                // list ==>map 根据时间保留最新的时间的数据
                // 加map 每一千查询一次业务表获取VIN 存到MAP，
                getVocWarnDate(list, insertList, updateList, map);
                //保存流水表
                try {
                    saveVocWarn(insertList, updateList);
                    returnCount += 1;
                    logger.info("VOC亮灯增量数据写库定时任务,returnCount:{}", returnCount);
                } catch (Exception e) {
                    logger.info("VOC亮灯增量数据写库定时任务结束保存报错:{}", e);
                    //报错日志表
                    po.setIsSc(1);
                    getMess(po,e);
                    this.update(po);
                    return;
                }
            } else {
                if (begIndex == 0) {
                    issc = 2;
                }
                logger.info("VOC亮灯增量数据写库定时任务,执行完成结束");
                break;

            }

        }
        po.setIsSc(issc);
        if (po.getIsSc() == 2) {
            po.setMess("激活文件写库数据为空");
        }
        this.update(po);
        logger.info("VOC亮灯增量数据写库定时任务结束耗时ns:{}", System.nanoTime() - curTime1);
    }

    private void saveVocWarn(List<VocWarningDataRecordPo> insertList, List<VocWarningDataRecordPo> updateList) {
        if (insertList != null && !insertList.isEmpty()) {
            ossWorkService.insertVocWarningDataRecordList(insertList);
        }
        if (updateList != null && !updateList.isEmpty()) {
            ossWorkService.updateVocWarningDataRecordList(updateList);
        }
    }

    private String getDateTime(String data, String s) {
        String data1 = DateUtils.getBeforeDateStrParm(new Date(), 2, DateUtils.YYYYMMDD);
        if (StringUtils.isEmpty(data)) {
            data = data1;
            logger.info(s, data);
        }
        return data;
    }

    private void getVocWarnDate(List<VocWarningDataLogPo> list, List<VocWarningDataRecordPo> insertList, List<VocWarningDataRecordPo> updateList, Map<String, VocWarningDataRecordPo> map) {
        for (VocWarningDataLogPo logPO : list) {
            if (map.containsKey(logPO.getVin())) {
                String dt = logPO.getDt();
                VocWarningDataRecordPo recordPO = map.get(logPO.getVin());
                String updatetime = recordPO.getReportDate();
                logger.info("voc亮灯增量数据写库定时任务开始vin:{},dt:{},updatetime:{}", logPO.getVin(), dt, updatetime);
                //判断dt 是否在updatetime 之后
                updateVocWarn(updateList, logPO, dt, recordPO, updatetime);
            } else {
                VocWarningDataRecordPo recordPOIns = new VocWarningDataRecordPo();
                this.setWarningDataRecordPo(logPO, recordPOIns);
                insertList.add(recordPOIns);
            }
        }
    }

    private void updateVocWarn(List<VocWarningDataRecordPo> updateList, VocWarningDataLogPo logPO, String dt, VocWarningDataRecordPo recordPO, String updatetime) {
        if (dt.equals(updatetime)) {
            String ad = logPO.getStatusValue();
            Integer ac = recordPO.getStatusValue();
            //1,激活；0,未激活
            if (!ad.equals(VocEnum.getStatusName(ac))) {
                this.setWarningDataRecordPo(logPO, recordPO);
                updateList.add(recordPO);
            }
        } else {
            if (DateUtils.compareTo(dt, updatetime, DateUtils.YYYYMMDD)) {
                logger.info("休息休息：{}", JSONObject.toJSON(recordPO));
                this.setWarningDataRecordPo(logPO, recordPO);
                updateList.add(recordPO);
            }
        }
    }

    private void getVocWarn(List<VocWarningDataLogPo> list, Map<String, VocWarningDataRecordPo> map) {
        List<List<VocWarningDataLogPo>> x = Lists.partition(list, 1000);
        for (List<VocWarningDataLogPo> xx : x) {
            //1000一次查询已经保存的结果结果放入MAP
            List<VocWarningDataRecordPo> selectList = ossWorkService.selectListWarningDataRecord(xx);
            if (selectList != null && !selectList.isEmpty()) {
                for (VocWarningDataRecordPo pox : selectList) {
                    map.put(pox.getVin(), pox);
                }
            }
        }
    }

    private Map<String, String> getbmd() {
      List<String>  list =   whitelistQueryService.selectWhiteListString(CommonConstants.MODTYPE_91111002,0);
      if(CollUtil.isNotEmpty(list)){

        List<String>  vins =  reportCommonClient.selectVinByCode(list);
        if(CollUtil.isNotEmpty(vins)){
            return  vins.stream().collect(Collectors.toMap(item ->item,item->item,(item1, item2) -> item1));
        }
      }
      return  new HashMap<>(1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int issuedToinit(List<VocWarningDataRecordPo> list, String dateTime, Map<String, String> mapx) {
        //根据 亮灯数据 查询激活数据 ，如果找到则默认未激活，未激活则不下发线索
        Map<String, VocFunctionalStatusRecordPO> map = new HashMap<>(list.size());
        //判断viN在业务表存不存在存在修改，不存在新增
        // 存在时 判断vin的dt时间和业务表的跟新时间大小
        getIssue(list, map);
        //任务
        //首保
        List<InviteVehicleTaskPO> inst1 = new ArrayList<>(list.size());
        //定保
        List<InviteVehicleTaskPO> inst2 = new ArrayList<>(list.size());
        // 流失客户立即下发 3
        List<InviteVehicleTaskPO> inst6 = new ArrayList<>(list.size());
        //流失客户不立即下发  2
        List<InviteVehicleTaskPO> inst63 = new ArrayList<>(list.size());
        //流失预警
        List<InviteVehicleTaskPO> inst12 = new ArrayList<>(list.size());
        //18个月 提前1个月下发
        List<InviteVehicleTaskPO> inst66 = new ArrayList<>(list.size());
        //修改任务
        List<Long> upst6 = new ArrayList<>(list.size());
        //修改线索
        List<Long> upsr6 = new ArrayList<>(list.size());
        //修改线索
        List<Long> upsr12= new ArrayList<>(list.size());

        // 添加白名单设置  vocList
        for (VocWarningDataRecordPo po : list) {
            logger.info("issuedToinit vin:{}", po.getVin());
                if(CollUtil.isNotEmpty(mapx) && !mapx.containsKey(po.getVin())){
                    continue;
                }
               VocParm parm = new VocParm();
                parm.setPo(po);
                parm.setDateTime(dateTime);
            //1.激活数据有， 正常逻辑
            if (map.containsKey(po.getVin())) {
                VocFunctionalStatusRecordPO pox = map.get(po.getVin());
                if (pox.getActivatedState().intValue() == 1) {
                    logger.info("pox.getActivatedState()==1");

                        //初始化
                        grantClueInit(parm, inst1,   inst2,  inst6, inst12, inst66);

                }
                //未激活等登，不处理 go.go.go.....
            } else {
                    //初始化
                    grantClueInit(parm, inst1,    inst2,  inst6,  inst12, inst66);

            }
            upsr6.addAll(parm.getUpsr6());
            upst6.addAll(parm.getUpst6());
            upsr12.addAll(parm.getUpsr12());

        }
        saveList(inst1, inst2, inst63, inst12, inst66);
        saveListInit6(inst6);
        logger.info("inst1:{}",inst1.size());
        logger.info("inst2:{}",inst2.size());
        logger.info("inst6:{}",inst6.size());
        logger.info("inst63:{}",inst63.size());
        logger.info("inst12:{}",inst12.size());
        logger.info("inst66:{}",inst66.size());
        updateTask(upst6, upsr6, upsr12);
        logger.info("upsr6:{}",upsr6.size());
        logger.info("upst6:{}",upst6.size());
        logger.info("upsr12:{}",upsr12.size());
        int count = ossWorkService.updateWarningIsExecute(list);
        logger.info("updateIsExecute,count:{}", count);
        return 1;
    }

    private void getIssue(List<VocWarningDataRecordPo> list, Map<String, VocFunctionalStatusRecordPO> map) {
        List<String> gameIdList = list.stream().map(VocWarningDataRecordPo::getVin).collect(Collectors.toList());
        List<List<String>> x = Lists.partition(gameIdList, 1000);

        //1000一次查询已经保存的结果结果放入MAP(激活数据)
        getIssuedToInitVocFunc(map, x);
    }


    private void saveList(List<InviteVehicleTaskPO> inst1, List<InviteVehicleTaskPO> inst2,  List<InviteVehicleTaskPO> inst63, List<InviteVehicleTaskPO> inst12, List<InviteVehicleTaskPO> inst66) {
        // 批量修改后 根据新的任务生成线索
        insertTask(inst1, inst2,  inst12, inst66,inst63);

    }

    private void insertTask(List<InviteVehicleTaskPO> inst1, List<InviteVehicleTaskPO> inst2,  List<InviteVehicleTaskPO> inst12, List<InviteVehicleTaskPO> inst66, List<InviteVehicleTaskPO> inst63) {
        if (inst1 != null && !inst1.isEmpty()) {
            inviteVehicleTaskService.inser1List1(inst1);
        }
        if (inst2 != null && !inst2.isEmpty()) {
            inviteVehicleTaskService.inser1List2(inst2);
        }
        if (inst12 != null && !inst12.isEmpty()) {
            inviteVehicleTaskService.inser1List12(inst12);
        }
        if (inst66 != null && !inst66.isEmpty()) {
            inviteVehicleTaskService.inser1List66(inst66);
        }

        if(inst63!=null &&  !inst63.isEmpty()){
            inviteVehicleTaskService.inser1List63(inst63);
        }
    }

    private void saveListInit6(List<InviteVehicleTaskPO> inst6) {
        if (inst6 != null && !inst6.isEmpty()) {
            inviteVehicleTaskService.saveListInit6(inst6);
        }
    }
    private void saveList6(List<InviteVehicleTaskPO> inst6) {
        if (inst6 != null && !inst6.isEmpty()) {
            inviteVehicleTaskService.inser1List6(inst6);
        }
    }

    private void getIssuedToInitVocFunc(Map<String, VocFunctionalStatusRecordPO> map, List<List<String>> x) {
        for (List<String> xx : x) {
            List<VocFunctionalStatusRecordPO> selectList = ossWorkService.selectListByVins(xx);
            if (selectList != null && !selectList.isEmpty()) {
                for (VocFunctionalStatusRecordPO pox : selectList) {
                    map.put(pox.getVin(), pox);
                }
            }
        }
    }

    //数据初始化
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void vocdata(String dateTime) {
        long curTime1 = System.nanoTime();
        logger.info("VOC环保灯线索下发定时任务开始获取数据时间开始1:{}", dateTime);
        //获取 激活信息当天修改时间
        if (StringUtils.isEmpty(dateTime)) {
            dateTime = DateUtils.dateToString(new Date(), DateUtils.YYYYMMDD);
            logger.info("VOC环保灯线索下发定时任务开始获取数据时间2:{}", dateTime);
        }
        // 分页查询 减轻单次查询压力， 2000一次 。
        pageMLamp(dateTime);

        logger.info("VOC环保灯线索下发定时任务开始获取数据时间结束耗时:{}", System.nanoTime() - curTime1);

    }



    private boolean checkRepairOrder18(String vin, String datatime) {
        //查询最后一次工单
        VehicleOwnerVO vo = reportCommonClient.queryRepairOrderByVinAndCodeAndJL(vin, "");
        if (vo != null) {
            // 当前时间向前推18个月，与结算时间比较，如果在结算时间之前 返回true，其他返回false
            try {
                return DateUtils.check18(vo.getDeliveryDate(),datatime);
            } catch (ParseException e) {
                logger.info("checkRepairOrder18  ParseException {}", e);
                throw new IllegalArgumentException(e);
            }
        }
        return false;
    }

    // 1. 激活和亮灯都有 正常
    // 2.激活有，亮灯没有  等灯
    //3. 激活没有，亮灯有 ，默认激活
    private void pageMLamp(String dateTime) {
        Map<String ,String> map  = this.getbmd();
        //分页查询
        Integer returnCount = 0;
        Integer partitionSize = 2000;
        Integer start = 0;
        long startTimeMillis = System.currentTimeMillis();
        logger.info("VOC保养灯线索下发,createDate:{},startTimeMillis:{}", dateTime, startTimeMillis);
        List<VocWarningDataRecordPo> list1 = null;
        while (true) {
            Integer begIndex = start * partitionSize;
            logger.info("VOC保养灯线索下发,createDate:{},begIndex:{},partitionSize:{}", dateTime, begIndex, partitionSize);
            list1 = ossWorkService.selectVocWarningDataRecordPoByModfiyTime(dateTime, 0, partitionSize);
            if (list1 == null || list1.isEmpty()) {
                logger.info("---VOC保养灯线索下发,执行完成---");
                break;
            } else {
                start++;
                logger.info("---VOC保养灯线索下发,开始执行---,start:{},size:{}", start, list1.size());
                int i = issuedTo(list1, dateTime,map);
                returnCount += i;
                logger.info("VOC保养灯线索下发,returnCount:{}", returnCount);
            }
        }
        Integer returnCount1 = 0;
        Integer partitionSize1 = 2000;
        Integer start1 = 0;
        List<VocFunctionalStatusRecordPO> list = null;
        while (true) {
            Integer begIndex1 = start1 * partitionSize1;
            logger.info("VOC1保养灯线索下发,createDate:{},begIndex:{},partitionSize:{}", dateTime, begIndex1, partitionSize1);
            list = ossWorkService.selectVocFunctionalStatusRecordPOByModfiyTime(dateTime, 0, partitionSize1);
            if (list == null || list.isEmpty()) {
                logger.info("---VOC1保养灯线索下发,执行完成---");
                break;
            } else {
                start1++;
                logger.info("---VOC1保养灯线索下发,开始执行---,start1:{},size1:{}", start1, list.size());
                int i = issuedTox(list,map,dateTime);
                returnCount1 += i;
                logger.info("VOC1保养灯线索下发,returnCount:{}", returnCount1);
            }
        }

        long endTimeMillis = System.currentTimeMillis();
        long execTime = endTimeMillis - startTimeMillis;
        logger.info("VOC保养灯线索下发总分页数,returnCount:{},endTimeMillis:{},execTime:{}", returnCount, endTimeMillis, execTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int issuedTox(List<VocFunctionalStatusRecordPO> list,  Map<String, String> mapx,String dateTime) {
        //根据 亮灯数据 查询激活数据 ，如果找到则默认未激活，未激活则不下发线索
        Map<String, VocFunctionalStatusRecordPO> map = list.stream().filter(s->ossWorkService.selectCountVocWarningDataRecordPo(s.getVin(),dateTime)<1).collect(Collectors.toMap(VocFunctionalStatusRecordPO::getVin, item -> item));

        //判断viN在业务表存不存在存在修改，不存在新增
        // 存在时 判断vin的dt时间和业务表的跟新时间大小
        //修改任务
        List<Long> upst6 = new ArrayList<>(list.size());
        //修改线索
        List<Long> upsr6 = new ArrayList<>(list.size());
        //修改线索
        List<Long> upsr12= new ArrayList<>(list.size());
        List<InviteVehicleTaskPO> inst123 = new ArrayList<>(list.size());

        vocToNoVoc(map, upsr6, inst123,mapx);
        saveListNoVoc(inst123);
        updateTask(upst6, upsr6, upsr12);
        int count = ossWorkService.updateFunctionalIsExecute(list);
        logger.info("updateIsExecute,count:{}", count);
        return  1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int issuedTo(List<VocWarningDataRecordPo> list, String dateTime, Map<String, String> mapx) {
        //根据 亮灯数据 查询激活数据 ，如果找到则默认未激活，未激活则不下发线索
        Map<String, VocFunctionalStatusRecordPO> map = new HashMap<>(list.size());
        //判断viN在业务表存不存在存在修改，不存在新增
        // 存在时 判断vin的dt时间和业务表的跟新时间大小
        getIssue(list, map);
        //18个月 提前1个月下发 流失客户
        List<InviteVehicleTaskPO> inst66 = new ArrayList<>(list.size());
        //修改任务   流失任务
        List<Long> upst6 = new ArrayList<>(list.size());
        //修改线索
        List<Long> upsr6 = new ArrayList<>(list.size());
        //修改线索
        List<Long> upsr12= new ArrayList<>(list.size());
        //任务                     首保
        List<InviteVehicleTaskPO> inst1 = new ArrayList<>(list.size());
        //定保
        List<InviteVehicleTaskPO> inst2 = new ArrayList<>(list.size());
        //流失客户
        List<InviteVehicleTaskPO> inst6 = new ArrayList<>(list.size());
        //非4S店保养流失
        List<InviteVehicleTaskPO> inst63 = new ArrayList<>(list.size());
        //流失
        List<InviteVehicleTaskPO> inst12 = new ArrayList<>(list.size());
        //激活变未激活,首保定保 超过3个月
        List<InviteVehicleTaskPO> inst123 = new ArrayList<>(list.size());

       // 添加白名单设置  vocList
        for (VocWarningDataRecordPo po : list) {
            logger.info("issuedTo po{}", po.getVin());
            if(CollUtil.isNotEmpty(mapx) && !mapx.containsKey(po.getVin())){
                continue;
            }
            VocParm parm = new VocParm();
            parm.setDateTime(dateTime);
            parm.setPo(po);
            //1.激活数据有， 正常逻辑
            if (map.containsKey(po.getVin())) {
                VocFunctionalStatusRecordPO pox = map.get(po.getVin());
                if (pox.getActivatedState().intValue() == 1) {
                        //激活
                        grantClue(parm, inst1, inst2,    inst12,inst66,inst63,inst6);
                }else{
                    vocToNoVocx(pox,upsr6,inst123);
                }
            } else {
                    //2.激活数据没有 ，默认为激活
                    grantClue(parm, inst1, inst2,   inst12, inst66,inst63,inst6);
            }
            upsr6.addAll(parm.getUpsr6());
            upst6.addAll(parm.getUpst6());
            upsr12.addAll(parm.getUpsr12());

        }
        // 批量修改后 根据新的任务生成线索
        saveListInvoc(inst1, inst2,  inst63, inst12, inst66);
        saveListNoVoc(inst123);
        saveList6(inst6);
        updateTask(upst6, upsr6, upsr12);
        logger.info("inst1:{}",inst1.size());
        logger.info("inst2:{}",inst2.size());
        logger.info("inst6:{}",inst6.size());
        logger.info("inst63:{}",inst63.size());
        logger.info("inst12:{}",inst12.size());
        logger.info("inst123:{}",inst123.size());
        logger.info("inst66:{}",inst66.size());
        logger.info("upsr6:{}",upsr6.size());
        logger.info("upst6:{}",upst6.size());
        logger.info("upsr12:{}",upsr12.size());
        int count = ossWorkService.updateWarningIsExecute(list);
        logger.info("updateIsExecute,count:{}", count);
        return 1;
    }

    private void saveListNoVoc(List<InviteVehicleTaskPO> inst123) {
        if (inst123 != null && !inst123.isEmpty()) {
            inviteVehicleTaskService.inser123List12(inst123);
        }
    }

    private void vocToNoVoc(Map<String, VocFunctionalStatusRecordPO> map, List<Long> upsr6, List<InviteVehicleTaskPO> inst123,Map<String, String> mapx) {
        for (VocFunctionalStatusRecordPO value : map.values()) {
            if(CollUtil.isNotEmpty(mapx) && !mapx.containsKey(value.getVin())){
                continue;
            }
            if(value.getActivatedState().intValue()==0){
                vocNoVoc(upsr6, inst123, value);


            }
        }
    }
    private void vocToNoVocx(VocFunctionalStatusRecordPO value, List<Long> upsr6, List<InviteVehicleTaskPO> inst123) {
        vocNoVoc(upsr6, inst123, value);
    }

    private void vocNoVoc(List<Long> upsr6, List<InviteVehicleTaskPO> inst123, VocFunctionalStatusRecordPO value) {
        //1、如果voc生成首定保线索，从voc变非voc，关闭首定保线索位逾期关闭，生成非voc流失预警线索
            //查询上一个状态，如果是从激活变为未激活，则关闭voc首定保线索 生成流失预警
            //查询倒数第2个状态,
            int count  =  ossWorkService.selectVocFunctionalStatusLogByVin(value.getVin());
            if(count>0){
            //查询最后一次亮灯数据
          VocWarningDataRecordPo vocWarningDataRecordPo =   ossWorkService.selectVocWarningDataRecordPo(value.getVin());
          if(vocWarningDataRecordPo !=null){
              vocNoVocExc(upsr6, inst123, vocWarningDataRecordPo);
          }
        }
    }

    private void vocNoVocExc(List<Long> upsr6, List<InviteVehicleTaskPO> inst123, VocWarningDataRecordPo vocWarningDataRecordPo) {
        if(vocWarningDataRecordPo.getStatusValue().equals(CommonConstants.VOC_TIMEFORSERVICE_NUM) ||vocWarningDataRecordPo.getStatusValue().equals(CommonConstants.VOC_ALMOSTTIMEFORSERVICE_NUM)){

            List<InviteVehicleRecordPO> listx =  inviteVehicleRecordService.selectVocByVin(vocWarningDataRecordPo.getVin());
             if(CollUtil.isNotEmpty(listx)){
                 for (InviteVehicleRecordPO po: listx) {
                     //判断是否 超过3个月 是下发，不是等3个月
                     if(DateUtils.comto(po.getAdviseInDate(),3)){
                         upsr6.add(po.getId());
                         //流失预警
                         logger.info("xiafaliuc：流失预警新增：{},{},{}",vocWarningDataRecordPo.getVin(),vocWarningDataRecordPo.getStatusValue(),vocWarningDataRecordPo.getMileageKm());
                         createCustomerTaskVoc123(inviteVehicleTaskService.selectByInviteId(po.getId()),inst123);
                     }
                 }
             }
        }
        //2、如果voc生成流失预警线索，从voc变非voc，保持线索不变
        //3、如果voc生成流失线索，从voc变非voc，保持线索不变
    }

    private void saveListInvoc(List<InviteVehicleTaskPO> inst1, List<InviteVehicleTaskPO> inst2,  List<InviteVehicleTaskPO> inst63, List<InviteVehicleTaskPO> inst12, List<InviteVehicleTaskPO> inst66) {
        insertTask(inst1, inst2,  inst12, inst66,inst63);
    }

    private void updateTask( List<Long> upst6, List<Long> upsr6, List<Long> upsr12) {
        if(upst6!=null && !upst6.isEmpty()){
            inviteVehicleTaskService.updateList(upst6);
        }
        if(upsr6!=null && !upsr6.isEmpty()){
            inviteVehicleRecordService.updateList(upsr6);
        }
        if(upsr12!=null && !upsr12.isEmpty()){
            inviteVehicleRecordService.updateList(upsr12);
        }
    }

    private void grantClueInit(VocParm parm , List<InviteVehicleTaskPO> inst1,  List<InviteVehicleTaskPO> inst2, List<InviteVehicleTaskPO> inst6,  List<InviteVehicleTaskPO> inst12, List<InviteVehicleTaskPO> inst66){
        VocWarningDataRecordPo po =   parm.getPo();
        String dateTime =  parm.getDateTime();
        List<Long> upsr6 = parm.getUpsr6();
        List<Long> upst6 = parm.getUpst6();
        //拿7天最近一次的里程数据
        //实在没有，只要不是流失客户，发定保吧
        //其实要准确一点的话，如果不是流失客户，看系统里有没有过机滤工单，没有过就是首保
        String mileageKm = po.getMileageKm();
        Integer inm = getInteger(po, dateTime, mileageKm);
        //根据条件下发线索
        // true ： 结算时间在 18个月里 ，false  结算时间在18个月外
        if (this.checkRepairOrder18(po.getVin(),dateTime)) {
            logger.info("this.checkRepairOrder18(po.getVin(),dateTime):{},{}",po.getVin(),dateTime);
            //下发流失线索(流失客户)
            //新增流失客户任务下发线索， 关闭旧的流失客户线索或者任务。
            //关闭哪家就给哪家下发线索。
            getRepairOrder18(po, inst6, upsr6, upst6, inm);
            logger.info(CommonConstants.LOG_S22,upsr6.size());
            logger.info(CommonConstants.LOG_S23,upst6.size());
        } else {


            // 查询系统是否存在已下发未完成的线索
            //  没有 新逻辑下发 （保养灯+里程 下发线索）
            //首定保
            int  count = 0;
            count = getCountInitialization(po, count);
            logger.info("查询首定保未完成为0：{}",po.getVin());
            if(count==0){
                //流失预警存在
                //查询是否有流失预警
                //如果存在 3个月之外的 预警线索 则超时关闭. 根据新下发逻辑下发线索
                boolean flag = isFlag(po, upsr6);
                if (flag) {
                    logger.info("查询流失预警未完成为0");
                    xiafaInit(po, inm,inst1,inst2,inst12,inst66,inst6);
                }

            }
        }
    }

    private boolean isFlag(VocWarningDataRecordPo po, List<Long> upsr6) {
        boolean flag = false;
        int num = vocInviteVehicleTaskRecordService.selectLossWarningByVin(po.getVin());
        if(num != 0){
            List<Long> recordPOS = vocInviteVehicleTaskRecordService.selectTypeXIIByVin(po.getVin());
            if (CollectionUtils.isNotEmpty(recordPOS)) {
                flag = true;
                for (Long recordPO : recordPOS) {
                    upsr6.add(recordPO);
                }
            }
        }else{
            flag = true;
        }
        return flag;
    }

    private Integer getInteger(VocWarningDataRecordPo po, String dateTime, String mileageKm) {
        Integer inm;
        if (!isInteger(mileageKm)) {
            inm = ossWorkService.selectKmByVin(po.getVin(), dateTime, DateUtils.getBeforeDateStrParm(DateUtils.parseDateStrToDate(dateTime, DateUtils.YYYYMMDD), 7, DateUtils.YYYYMMDD));
            logger.info("!isInteger(mileageKm):{},{}", mileageKm,inm);
        } else {
            inm = Integer.valueOf(mileageKm);
        }
        return inm;
    }

    private int getCountInitialization(VocWarningDataRecordPo po, int count) {
        List<InviteVehicleRecordPO> list =  inviteVehicleRecordService.selectByVin(po.getVin(),true);
        if(CollUtil.isNotEmpty(list)){
            for (InviteVehicleRecordPO pox:list) {
                count =count+ vocInviteVehicleTaskRecordService.selectVocCountByVinT(po.getVin(),pox.getId());
                if(count>0){
                    break;
                }
            }

        }else{
            count = 0;
        }
        return count;
    }

    private int getCount(VocWarningDataRecordPo po, int count) {
        List<InviteVehicleRecordPO> list =  inviteVehicleRecordService.selectByVin(po.getVin(),false);
        if(CollUtil.isNotEmpty(list)){
            for (InviteVehicleRecordPO pox:list) {
                count =count+ vocInviteVehicleTaskRecordService.selectVocCountByVin(po.getVin(),pox.getId());
                if(count>0){
                    break;
                }
            }

        }else{
            count = 0;
        }
        return count;
    }

    private void getRepairOrder18(VocWarningDataRecordPo po, List<InviteVehicleTaskPO> inst6, List<Long> upsr6, List<Long> upst6, Integer inm) {
        List<InviteVehicleRecordPO> recordPOS = inviteVehicleRecordService.getWaitCloseRecordByVin(po.getVin());
        if (CollectionUtils.isNotEmpty(recordPOS)) {
            for (InviteVehicleRecordPO r : recordPOS) {
                upsr6.add(r.getId());
                InviteVehicleTaskPO t = inviteVehicleTaskService.selectByInviteId(r.getId());
                if(ObjectUtils.isNotEmpty(t)){
                    inst6.add(this.createCustomerLossTaskVoc6(t, inm));
                }
            }
        }
        List<InviteVehicleTaskPO> taskPOS = inviteVehicleTaskService.getWaitCloseRecordByVin(po.getVin());
        if (CollectionUtils.isNotEmpty(taskPOS)) {
            for (InviteVehicleTaskPO t : taskPOS) {
                upst6.add(t.getId());
                inst6.add(this.createCustomerLossTaskVoc6(t, inm));
            }
        }
    }


    private void grantClue(VocParm vocParm, List<InviteVehicleTaskPO> inst1, List<InviteVehicleTaskPO> inst2, List<InviteVehicleTaskPO> inst12,  List<InviteVehicleTaskPO> inst66, List<InviteVehicleTaskPO> inst63, List<InviteVehicleTaskPO> inst6) {
        String dateTime =  vocParm.getDateTime();
        VocWarningDataRecordPo po =  vocParm.getPo();
        //常规
        //拿7天最近一次的里程数据
        //实在没有，只要不是流失客户，发定保吧
        //其实要准确一点的话，如果不是流失客户，看系统里有没有过机滤工单，没有过就是首保
        String mileageKm = po.getMileageKm();
        Integer inm = 0;
        if (!isInteger(mileageKm)) {
            inm = ossWorkService.selectKmByVin(po.getVin(), dateTime, DateUtils.getBeforeDateStrParm(DateUtils.parseDateStrToDate(dateTime, DateUtils.YYYYMMDD), 7, DateUtils.YYYYMMDD));
            logger.info("!isInteger(mileageKm):{},{}",mileageKm,inm);
        } else {
            inm = Integer.valueOf(mileageKm);
        }
        vocParm.setInm(inm);

        //  AlmostTimeForService   首保
        //  TimeForService  定保
        //  TimeExceeded  流失预警
        //  Normal  流失客户

            // 查询系统是否存在已下发未完成的线索
            //  没有 新逻辑下发 （保养灯+里程 下发线索）
            //首定保
            int count =0;
            count =  getCount(po, count);
            if(count==0){
                xiafaliuc(vocParm,inst1,inst2,inst12,inst66,inst63,inst6);
            }
    }
    private void xiafaInit(VocWarningDataRecordPo po, Integer inm, List<InviteVehicleTaskPO> inst1, List<InviteVehicleTaskPO> inst2,  List<InviteVehicleTaskPO> inst12, List<InviteVehicleTaskPO> inst66, List<InviteVehicleTaskPO> inst6) {
        //新逻辑下发
        //1)如果保养灯为AlmostTimeForService-新增，里程大于8750，则为定保线索，反之首保；
        //2)如果保养灯为TimeForService-新增/改变，里程大于10000，则为定保线索，反之首保；
        //3)如果保养灯为TimeExceeded-新增/改变，则生成流失预警线索；
        //4)如果保养灯为Normal-消失，则生成流失客户线索。
        //VOC车生成首/定保线索的同时，生成+18个月的流失线索，提前一个月下发；
        Integer statusValue = po.getStatusValue();
        String statusChange = po.getStatusChange();
        if (CommonConstants.VOC_ALMOSTTIMEFORSERVICE.equals(getStatusValue(statusValue.intValue())) && CommonConstants.VOC_INSERT.equals(statusChange)
        ) {
            logger.info("AlmostTimeForService新增：{},{}",po.getVin(),po.getMileageKm());
            //查询机油没有，发首保 ，有发定保
            getTask(po, inm, inst1, inst2, inst66, 8750);
        }else

        if (CommonConstants.VOC_TIMEFORSERVICE.equals(getStatusValue(statusValue.intValue()))
                && (CommonConstants.VOC_INSERT.equals(statusChange) || CommonConstants.VOC_UPDATE.equals(statusChange))
        ) {
            logger.info("TimeForService：{},{}",po.getVin(),po.getMileageKm());
            getTask(po, inm, inst1, inst2, inst66, 10000);
        }

        else

        if (CommonConstants.VOC_TIMEEXCEEDED.equals(getStatusValue(statusValue.intValue()))
                && (CommonConstants.VOC_INSERT.equals(statusChange) || CommonConstants.VOC_UPDATE.equals(statusChange))) {
            //流失预警
            logger.info("TimeExceeded：{},{}",po.getVin(),po.getMileageKm());
            createCustomerTaskVoc12(po.getVin(),inst12);
        }else

        if (CommonConstants.VOC_NORMAL.equals(getStatusValue(statusValue.intValue()))
                && CommonConstants.VOC_FADED.equals(statusChange)) {
            //流失客户
            logger.info("Normal：{},{}",po.getVin(),po.getMileageKm());
            createCustomerLossTaskVoc6(inst6,po.getVin());
        }
    }

    private void getTask(VocWarningDataRecordPo po, Integer inm, List<InviteVehicleTaskPO> inst1, List<InviteVehicleTaskPO> inst2, List<InviteVehicleTaskPO> inst66, int i) {
        if (inm == null) {
            VehicleOwnerVO vo = reportCommonClient.queryRepairOrderByVinAndCodeAndJL(po.getVin(), "");
            if (vo != null) {
                logger.info("vocinm ==null(vo != null）定保新增：{},{},{}",po.getVin(),po.getStatusValue(),po.getMileageKm());
                //定保任务
                inm = vo.getOutMileage();
                createCustomerTaskVoc2(inm, inst2, inst66, po.getVin(), vo.getDeliveryDate());
            } else {
                logger.info("vocinm ==null(vo = null）首保新增：{},{},{}",po.getVin(),po.getStatusValue(),po.getMileageKm());
                //流失
                //首保任务
                createCustomerTaskVoc1(po.getVin(), inst1, inst66);
            }
        } else if (inm > i) {
            //定保任务
            logger.info("voc：inm > i定保新增：{},{},{},{},{}",po.getVin(),po.getStatusValue(),po.getMileageKm(),inm,i);
            try {
                createCustomerTaskVoc2(inm, inst2, inst66, po.getVin(), DateUtils.parse(po.getReportDate(),DateUtils.YYYYMMDD));
            } catch (Exception e) {
               logger.info("voc：inm > i定保新增 定保新增定时任务异常：{},{}",po.getVin(),e);
            }
        } else {
            //首保
            logger.info("voc：首保新增：{},{},{},{},{}",po.getVin(),po.getStatusValue(),po.getMileageKm(),inm,i);
            createCustomerTaskVoc1(po.getVin(), inst1, inst66);
        }
    }


    private void xiafaliuc(VocParm vocParm, List<InviteVehicleTaskPO> inst1, List<InviteVehicleTaskPO> inst2, List<InviteVehicleTaskPO> inst12, List<InviteVehicleTaskPO> inst66, List<InviteVehicleTaskPO> inst63,  List<InviteVehicleTaskPO> inst6) {
        VocWarningDataRecordPo po =  vocParm.getPo();
        
        Integer inm = vocParm.getInm();
        //新逻辑下发
        //1)如果保养灯为AlmostTimeForService-新增，里程大于8750，则为定保线索，反之首保；
        //2)如果保养灯为TimeForService-新增/改变，里程大于10000，则为定保线索，反之首保；
        //3)如果保养灯为TimeExceeded-新增/改变，则生成流失预警线索；
        //4)如果保养灯为Normal-消失，则生成流失客户线索。
        //VOC车生成首/定保线索的同时，生成+18个月的流失线索，提前一个月下发；
        Integer statusValue = po.getStatusValue();
        String statusChange = po.getStatusChange();
        List<Long> upsr12 =  vocParm.getUpsr12();
        List<Long> upsr6 = vocParm.getUpsr6();
        //查询是否有VOC线索流失客户未完成的如果有直接关闭生成新的流失客户线索
        List<InviteVehicleRecordPO> xx = inviteVehicleRecordService.selectVocLossByVin(po.getVin());
        if(CollUtil.isNotEmpty(xx)){
            logger.info("vocTaskPa：查询到上个状态为消失：{},{},{},{}",po.getVin(),po.getStatusValue(),po.getMileageKm(),inm);
            loss(inst6, upsr12, xx);
        }else{

            if (CommonConstants.VOC_ALMOSTTIMEFORSERVICE.equals(getStatusValue(statusValue.intValue())) && CommonConstants.VOC_INSERT.equals(statusChange)
            ) {
                logger.info("xiafaliuc：首保新增：{},{},{},{}",po.getVin(),po.getStatusValue(),po.getMileageKm(),inm);
                //vocTask(po, inm, inst1, inst66);
                getTask(po,inm, inst1, inst2, inst66, 8750);
            }else

            if (CommonConstants.VOC_TIMEFORSERVICE.equals(getStatusValue(statusValue.intValue()))
                    && (CommonConstants.VOC_INSERT.equals(statusChange) || CommonConstants.VOC_UPDATE.equals(statusChange))
            ) {
                logger.info("xiafaliuc：定保新增：{},{},{},{}",po.getVin(),po.getStatusValue(),po.getMileageKm(),inm);
                //vocTask1(po, inm,  inst2, inst66);
                getTask(po,inm, inst1, inst2, inst66, 10000);
            }
            else

            if (CommonConstants.VOC_TIMEEXCEEDED.equals(getStatusValue(statusValue.intValue()))
                    && (CommonConstants.VOC_INSERT.equals(statusChange) || CommonConstants.VOC_UPDATE.equals(statusChange))) {
                //流失预警
                logger.info("xiafaliuc：流失预警新增：{},{},{},{}",po.getVin(),po.getStatusValue(),po.getMileageKm(),inm);
                createCustomerTaskVoc122(inst12,po.getVin(),upsr12);
            }else

            if (CommonConstants.VOC_NORMAL.equals(getStatusValue(statusValue.intValue()))
                    && CommonConstants.VOC_FADED.equals(statusChange)) {
                //流失客户
                lossTask(inst63, po, upsr6);

                logger.info("xiafaliuc：流失客户新增：{},{},{},{}",po.getVin(),po.getStatusValue(),po.getMileageKm(),inm);
            }
        }
    }

    private void loss(List<InviteVehicleTaskPO> inst6, List<Long> upsr12, List<InviteVehicleRecordPO> xx) {
        for (InviteVehicleRecordPO recordPOx:xx) {
            upsr12.add(recordPOx.getId());
            //下发新的流失客户线索
            InviteVehicleTaskPO p1o=  this.createCustomerTaskVoc6(recordPOx);
            if(p1o!=null){
                inst6.add(p1o);
            }

        }
    }


    private void lossTask(List<InviteVehicleTaskPO> inst63, VocWarningDataRecordPo po, List<Long> upsr6) {
        //查询流失客户非VOC
        List<InviteVehicleRecordPO> list =   inviteVehicleRecordService.selectLossByVin(po.getVin());
        if(CollUtil.isNotEmpty(list)){
            for (InviteVehicleRecordPO recordPOx:list) {
                upsr6.add(recordPOx.getId());
                //下发新的流失客户线索
                createCustomerLossTaskVoc63v(inst63,po.getVin(),recordPOx.getDealerCode());

            }
        }else{
            createCustomerLossTaskVoc63(inst63,po.getVin());
        }
    }

    private void vocTask1(VocWarningDataRecordPo po, Integer inm,  List<InviteVehicleTaskPO> inst2, List<InviteVehicleTaskPO> inst66) {
        //查询机油没有，发首保 ，有发定保
        VehicleOwnerVO vo = reportCommonClient.queryRepairOrderByVinAndCodeAndJL(po.getVin(), "");
        Date date = null;
        try {
            date = DateUtils.parse(po.getReportDate(),DateUtils.YYYYMMDD);
        } catch (Exception e) {
            logger.info("vocTask1定保新增 定保新增定时任务异常：{},{},{}",po.getVin(),po.getId(),e);
        }
        if (inm == null) {
            if (vo != null) {
                //定保
                inm = vo.getOutMileage();
                logger.info("vocTask1：定保保新增：{},{},{},{}",po.getVin(),po.getStatusValue(),po.getMileageKm(),inm);
            }
            createCustomerTaskVoc2(inm, inst2, inst66, po.getVin(), date);
        } else {
            // 定保
            logger.info("vocTask1：定保新增：{},{},{},{}",po.getVin(),po.getStatusValue(),po.getMileageKm(),inm);
                createCustomerTaskVoc2(inm, inst2, inst66, po.getVin(), date);
        }
    }

    private void vocTask(VocWarningDataRecordPo po, Integer inm, List<InviteVehicleTaskPO> inst1, List<InviteVehicleTaskPO> inst66) {
        //查询机油没有，发首保 ，有发定保
            // 首保
            logger.info("vocTask：首保新增：{},{},{},{}",po.getVin(),po.getStatusValue(),po.getMileageKm(),inm);
            createCustomerTaskVoc1(po.getVin(), inst1, inst66);
    }

    private void createCustomerTaskVoc122(List<InviteVehicleTaskPO> inst12, String vin, List<Long> upsr12) {
       List<InviteVehicleRecordPO>  list  = inviteVehicleRecordService.selectListByVin(vin);
       if(list!=null  &&  !list.isEmpty()){
           for (InviteVehicleRecordPO po:list) {
               upsr12.add(po.getId());
               this.createCustomerTaskVoc12v(vin,inst12,po.getDealerCode());
           }
       }else{
           this.createCustomerTaskVoc12(vin,inst12);
       }

    }

    private void createCustomerTaskVoc1(String vin, List<InviteVehicleTaskPO> inst1, List<InviteVehicleTaskPO> inst66) {
         inviteVehicleTaskService.createCustomerTaskVoc1( vin,inst1,inst66);
    }

    private void createCustomerTaskVoc2(Integer inm, List<InviteVehicleTaskPO> inst2, List<InviteVehicleTaskPO> inst66, String vin, Date deliveryDate) {
         inviteVehicleTaskService.createCustomerTaskVoc2( inm,inst2,inst66,vin,deliveryDate);
    }

    //流失客户任务

    private InviteVehicleTaskPO createCustomerLossTaskVoc6(InviteVehicleTaskPO t, Integer inm) {
        return inviteVehicleTaskService.createCustomerLossTaskVoc(t, inm);
    }
    private InviteVehicleTaskPO createCustomerTaskVoc6(InviteVehicleRecordPO po) {
       return   inviteVehicleTaskService.createCustomerTaskVoc6( po);
    }
    //流失客户任务

    private void createCustomerLossTaskVoc6(List<InviteVehicleTaskPO> inst6, String vin) {
         inviteVehicleTaskService.createCustomerLossTaskVoc6(inst6, vin);
    }

    //流失客户任务

    private void createCustomerLossTaskVoc63(List<InviteVehicleTaskPO> inst63, String vin) {
        inviteVehicleTaskService.createCustomerLossTaskVoc63(inst63, vin);
    }
    //流失预警任务
    private void createCustomerTaskVoc12(String vin, List<InviteVehicleTaskPO> inst12) {
         inviteVehicleTaskService.createCustomerTaskVoc12(vin, inst12);
    }
    private void createCustomerTaskVoc12v(String vin, List<InviteVehicleTaskPO> inst12,String code) {
         inviteVehicleTaskService.createCustomerTaskVoc12v(vin, inst12,code);
    }
    //流失客户任务

    private void createCustomerLossTaskVoc63v(List<InviteVehicleTaskPO> inst63, String vin,String code) {
        inviteVehicleTaskService.createCustomerLossTaskVoc63v(inst63, vin,code);
    }

    @Override
    public void checkVocAlert() {
        checkVocAlert(null);
    }

    @Override
    public void checkVocAlert(String dateTime) {
        if(StringUtils.isEmpty(dateTime)){
            dateTime = DateUtils.getBeforeDateStrParm(new Date(),lossDate ,DateUtils.PATTERN_YYYY_MM_DD);
        }
        List<InviteVehicleRecordPO> pos = new ArrayList<>();
      List<InviteVehicleTaskPO> list =    inviteVehicleTaskService.selectListTaskVocByTime(dateTime);
        if(list!=null && ! list.isEmpty()){
             //查询工单3天前是否有机油工单
            for (InviteVehicleTaskPO po : list) {
                int count  =  reportCommonClient.selectRepairOrderByVin(po.getVin(),dateTime);
                if(count<1){
                    //1.关闭VOC流失预警线索
                      inviteVehicleRecordService.updateAlertVocByVin(po.getVin());
                    // 3.VOC首定保流失客户任务关闭
                    inviteVehicleTaskService.updateLossVocByVin(po.getVin());
                    // 2.下发VOC流失客户线索
                    pos.add(inviteVehicleTaskService.issued(po));

                }
            }
        }
        if(CollUtil.isNotEmpty(pos)){
            inviteVehicleTaskService.taskAllocationVocList(pos);
        }
    }
    //==================================== 工具、数据组装、日志表操作=========================================================================//


    private boolean doesObjectExist(String s2, ObsClient obsClient) {
        return obsClient.doesObjectExist(bucketName, s2);
    }

    private ObsClient getObs() {
        return new ObsClient(accessKeyId, accessKeySecret, endpoint);

    }


    public ObsObject getObsObject(String s2, ObsClient obsClient) {
        ObsObject o = obsClient.getObject(bucketName, s2);
        if (o != null) {
            return o;
        }
        return null;
    }

    private void saveLog(BtnLogPO po) {
        int save = btnLogService.insert(po);
        logger.info("日志保存save：{},{}", save, JSONObject.toJSON(po));
    }

    private void update(BtnLogPO po) {
        int update = btnLogService.update(po);
        logger.info("日志保存update：{},{}", update, JSONObject.toJSON(po));
    }

    private boolean selectLog(String dateTime, int type, int issc) {
        int count = btnLogService.selectCount(dateTime, type, issc);
        logger.info("日志查询btnLogService：{},{},{}", count, dateTime, type);
        return count > 0;
    }


    private CSVParser getCsvRecords(BufferedReader reader) throws IOException {
        return CSVFormat.DEFAULT.withHeader("vin", "dt", "activated_state", "subscription_startdate", "subscription_enddate").parse(reader);
    }

    private CSVParser getCsvRecordss(BufferedReader reader) throws IOException {
        return CSVFormat.DEFAULT.withHeader("vin", "dt", "status_name", "status_value", "status_change", "mileage_m", "mileage_km", "model_year", "model").parse(reader);
    }


    public void assemble(CSVRecord record, VocFunctionalStatusLogPO polog) {
        polog.setVin(record.get(0));
        polog.setDt(record.get(1));
        polog.setActivatedState(record.get(2));
        polog.setSubscriptionStartdate(record.get(3));
        polog.setSubscriptionEnddate(record.get(4));
    }

    public void assemble(CSVRecord record, VocFunctionalStatusRecordPO recordPO) {
        recordPO.setVin(record.get(0));
        recordPO.setUpdateTime(record.get(1));
        recordPO.setActivatedState("已激活".equals(record.get(2)) ? 1 : 0);
        recordPO.setSubscriptionStartdate(DateUtils.parseDateStrToDate(record.get(3), DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
        recordPO.setSubscriptionEnddate(DateUtils.parseDateStrToDate(record.get(4), DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
    }


    private void assemble(CSVRecord record, VocWarningDataLogPo polog) {
        polog.setVin(record.get(0));
        polog.setDt(record.get(1));
        polog.setStatusName(record.get(2));
        polog.setStatusValue(record.get(3));
        polog.setStatusChange(record.get(4));
        //  /N走新逻辑
        polog.setMileagem(record.get(5));
        //  /N走新逻辑
        polog.setMileageKm(record.get(6));
        polog.setModelYear(record.get(7));
        polog.setModel(record.get(8));
    }


    private List<VocFunctionalStatusLogPO> selectVocFunctionalStatusLogLog(String data, int begIndex, Integer endIndex) {
        return ossWorkService.selectVocFunctionalStatusLogLog(data, begIndex, endIndex);
    }

    private List<VocWarningDataLogPo> selectWarningdailyLog(String data, int begIndex, Integer endIndex) {
        return ossWorkService.selectWarningdailyLog(data, begIndex, endIndex);
    }

    private void setVocFuntionalStatusPo(VocFunctionalStatusLogPO logPO, VocFunctionalStatusRecordPO recordPO) {
        recordPO.setUpdateTime(logPO.getDt());
        recordPO.setActivatedState("未激活".equals(logPO.getActivatedState()) ? 0 : 1);
        recordPO.setSubscriptionStartdate(DateUtils.parseDateStrToDate(logPO.getSubscriptionStartdate(), DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
        recordPO.setSubscriptionEnddate(DateUtils.parseDateStrToDate(logPO.getSubscriptionEnddate(), DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
        recordPO.setModifyTime(DateUtils.dateToString(new Date(), DateUtils.YYYYMMDD));
    }

    private void setWarningDataRecordPo(VocWarningDataLogPo logPO, VocWarningDataRecordPo recordPOIns) {

        recordPOIns.setReportDate(logPO.getDt());
        recordPOIns.setModifyTime(DateUtils.dateToString(new Date(), DateUtils.YYYYMMDD));
        recordPOIns.setVin(logPO.getVin());
        recordPOIns.setStatusName(logPO.getStatusName());
        //转换
        recordPOIns.setStatusValue(getStatus(logPO.getStatusValue()));
        recordPOIns.setStatusChange(logPO.getStatusChange());
        recordPOIns.setMileage(isInteger(logPO.getMileagem()) ? String.valueOf(Math.round(Double.parseDouble(logPO.getMileagem()))) : logPO.getMileagem());
        //四舍五入
        recordPOIns.setMileageKm(isInteger(logPO.getMileageKm()) ? String.valueOf(Math.round(Double.parseDouble(logPO.getMileageKm()))) : logPO.getMileageKm());
        recordPOIns.setYearModel(logPO.getModelYear());
        recordPOIns.setModel(logPO.getModel());

    }

    private int getStatus(String statusValue) {
        return VocEnum.getStatusValues(statusValue);
    }

    private String getStatusValue(int statusName) {
        return VocEnum.getStatusName(statusName);
    }


    public static boolean isInteger(String str) {
        Pattern pattern = Pattern.compile("[0-9]*\\.?[0-9]+");
        Matcher isNum = pattern.matcher(str);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }

    /**
     * 创建流失预警线索
     *
     * @param vo InviteVehicleTaskPO
     * @param inst123
     */
    private void createCustomerTaskVoc123(InviteVehicleTaskPO vo, List<InviteVehicleTaskPO> inst123) {
        InviteVehicleTaskPO po = new InviteVehicleTaskPO();
        //基准日期+ 间隔月
        po.setAdviseInDate(DateUtils.getDate());
        po.setCreateInviteTime(DateUtils.getDate());
        po.setAdviseInMileage(vo.getAdviseInMileage());
        po.setCloseTimes(0);
        po.setIsCreateInvite(0);
        //基准日期为关闭任务的建议进厂日期
        po.setItemType(vo.getItemType());
        po.setInviteTime(vo.getAdviseInDate());
        po.setItemCode(vo.getItemCode());
        po.setInviteType(CommonConstants.INVITE_TYPE_XII);
        po.setRemindInterval(vo.getRemindInterval());
        po.setCloseTimes(vo.getCloseTimes() + 1);
        po.setLicensePlateNum(vo.getLicensePlateNum());
        po.setDealerCode(vo.getDealerCode());
        po.setName(vo.getName());
        po.setAge(vo.getAge());
        po.setModel(vo.getModel());
        po.setDailyMileage(vo.getDailyMileage());
        po.setSex(vo.getSex());
        po.setTel(vo.getTel());
        po.setItemName(vo.getItemName());
        po.setVin(vo.getVin());
        po.setDayInAdvance(vo.getDayInAdvance());
        inst123.add(po);
    }

}
