package com.yonyou.dmscus.customer.service.invitationautocreate.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InviteVehiclePO {

    /**
     * 邀约ID
     */
    private Long id;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车主姓名
     */
    private String name;

    /**
     * 车主电话
     */
    private String tel;

    /**
     * 邀约类型
     */
    private Integer inviteType;

    /**
     * 建议进厂日期
     */
    private Date adviseInDate;

    /**
     * 跟进状态
     */
    private Integer followStatus;

    /**
     * 线索完成状态
     */
    private Integer orderStatus;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 经销商代码
     */
    private String dealerCode;

    /**
     * 车主年龄
     */
    private String age;

    /**
     * 车主性别
     */
    private String sex;

    /**
     * 车型
     */
    private String model;


    public InviteVehiclePO(String name, String tel, String sex, String age, Long id) {
        super();
        this.name = name;
        this.tel = tel;
        this.sex = sex;
        this.age = age;
        this.id = id;
    }
    public InviteVehiclePO(Long id) {
        super();
        this.id = id;
    }
}
