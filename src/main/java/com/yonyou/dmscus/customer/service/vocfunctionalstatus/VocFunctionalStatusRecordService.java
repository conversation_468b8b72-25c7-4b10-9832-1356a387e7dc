package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusLogPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusRecordPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataRecordPo;

import java.util.List;

/**
 * <AUTHOR>
 * @title: VocFunctionalStatusRecordService
 * @projectName dmscus.customer
 * @date 2022/11/118:46
 */
public interface VocFunctionalStatusRecordService {
    int insertList(List<VocFunctionalStatusRecordPO> list);

    int updateList(List<VocFunctionalStatusRecordPO> updateList);

    List<VocFunctionalStatusRecordPO> selectListStatusRecord(List<VocFunctionalStatusLogPO>  x);

    List<VocFunctionalStatusRecordPO>  selectVocFunctionalStatusRecordByModfiyTime(String dateTime, Integer begIndex, Integer partitionSize);

    List<VocFunctionalStatusRecordPO> selectListByVins(List<String> xx);

    int selectByVin(String vin);

    int selectIsStatusByVin(String vin);

    int selectVocFunctionalStatusRecordByVin(String vin);

    List<VocFunctionalStatusRecordPO> selectVocFunctionalStatusRecordPOByModfiyTime(String dateTime, Integer begIndex1, Integer partitionSize1);

    int updateFunctionalIsExecute(List<VocFunctionalStatusRecordPO> list);
}
