package com.yonyou.dmscus.customer.service.inviteInsurance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordImportDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordImportPO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 车辆续保线索导入临时表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-19
 */
public interface InviteInsuranceVehicleRecordImportService extends IService<InviteInsuranceVehicleRecordImportPO> {

    ImportTempResult<InviteInsuranceVehicleRecordImportDTO> importInsuranceRecordTemp(MultipartFile importFile)
            throws Exception;

    void importInsuranceRecord() throws ServiceBizException;

    IPage<InviteInsuranceVehicleRecordImportDTO> selectImportSuccessInsuranceRecord(Page page)
            throws ServiceBizException;

    IPage<InviteInsuranceVehicleRecordImportDTO> selectImportErrorInsuranceRecord(Page page)
            throws ServiceBizException;

}
