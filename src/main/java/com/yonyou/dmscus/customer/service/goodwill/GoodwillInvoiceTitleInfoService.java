package com.yonyou.dmscus.customer.service.goodwill;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillInvoiceTitleInfoDTO;

/**
 * <p>
 * 亲善发票抬头信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-17
 */
public interface GoodwillInvoiceTitleInfoService {
	public IPage<GoodwillInvoiceTitleInfoDTO> selectPageBysql(Page page,
			GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO);

	public List<GoodwillInvoiceTitleInfoDTO> selectListBySql(GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO);

	public GoodwillInvoiceTitleInfoDTO getById(Long id);

	public int insert(GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO);

	public int update(Long id, GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO);

	public int deleteById(Long id);

	public int deleteBatchIds(String ids);

	public GoodwillInvoiceTitleInfoDTO queryInvoiceInfo(Integer invoiceTitle);

}
