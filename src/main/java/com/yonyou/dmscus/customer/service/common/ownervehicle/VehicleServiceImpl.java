package com.yonyou.dmscus.customer.service.common.ownervehicle;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.common.VehicleMapper;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.*;
import com.yonyou.dmscus.customer.entity.dto.common.VehicleDTO;
import com.yonyou.dmscus.customer.entity.po.common.VehiclePO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 车主车辆
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-21
 */
@Service
public class VehicleServiceImpl implements VehicleService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    VehicleMapper vehicleMapper;


    /**
     * 查询车主车辆
     * @param vin
     * @return
     */
    @Override
    public VehicleDTO getVehicleByVin(String vin) {
        VehiclePO po = vehicleMapper.getVehicleByVin(vin);
        if(po==null){
            return new VehicleDTO();
        }else{
            return po.transPoToDto(VehicleDTO.class);
        }
    }

    @Override
    public VehicleDTO selectDateByVin(String vin) {
        VehiclePO po = vehicleMapper.selectDateByVin(vin);
        if (Objects.isNull(po)) {
            return null;
        }
        return po.transPoToDto(VehicleDTO.class);
    }
}
