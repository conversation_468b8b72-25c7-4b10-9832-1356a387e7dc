package com.yonyou.dmscus.customer.service.middleground;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.dto.EmpQueryDto;
import com.yonyou.dmscus.customer.dto.UserOrgInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.OrgQueryDto;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.PageResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.*;
import com.yonyou.dmscus.customer.entity.vo.OrgVo;
import com.yonyou.dmscus.customer.feign.MidEndAuthCenterClient;
import com.yonyou.dmscus.customer.feign.MidEndBasicDataCenterClient;
import com.yonyou.dmscus.customer.feign.MidUserOrganizationClient;
import com.yonyou.dmscus.customer.feign.dto.AllModeDto;
import com.yonyou.dmscus.customer.util.common.VolvoHttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 中台接口
 */
@Service
public class BasicdataCenterServiceImpl implements BasicdataCenterService{

    /**日志对象*/
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**接口调用成功的返回状态*/
    public static final String SUCCESS_CODE = "0";

    @Resource
    private RestTemplate directRestTemplate;

    @Autowired
    private MidUrlProperties midUrlProperties;

    @Autowired
    private MidUserOrganizationClient organizationClient;

    @Autowired
    private MidEndAuthCenterClient authCenterClient;

    @Resource
    private MidEndBasicDataCenterClient midEndBasicDataCenterClient;

    @Autowired
    VolvoHttpUtils volvoHttpUtils;

    /**
     * 查询 所有车型
     * @param queryParams
     * @return
     * @throws ServiceBizException
     */
    @Override
    public List<ModelVO> queryAllModel(Map<String, Object> queryParams) throws ServiceBizException {
        RequestDTO<ModelVO> request = new RequestDTO<ModelVO>();
        request.setData(new ModelVO());
        String jsonStr = JSONObject.toJSONString(request);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<RequestDTO> httpEntity = new HttpEntity<>(request, httpHeaders);
        String url = midUrlProperties.getMidEndBasicdataCenter() + midUrlProperties.getModelAll();
        ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(url, HttpMethod.POST, httpEntity,
                ResponseDTO.class);
        if(responseEntity.getBody()!=null) {
            if(SUCCESS_CODE.equals(responseEntity.getBody().getReturnCode())) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                List<ModelVO> convertValue = objectMapper
                        .convertValue(responseEntity.getBody().getData(), new TypeReference<List<ModelVO>>(){});
                return convertValue;
            }else {
                throw new ServiceBizException("查询中台车型失败");
            }
        }
        return null;
    }



    /**
     * 通过经销商代码和角色代码查询员工列表
     * @return
     * @throws ServiceBizException
     *
     * public List<UserInfoOutDTO> QueryRoleUserByCompanyCode(Map<String, Object> queryParams) throws ServiceBizException {
     *         RequestDTO<QueryRoleUserByCompanyCodeDTO> request = new RequestDTO<QueryRoleUserByCompanyCodeDTO>();
     *         if (queryParams != null){
     *             for(String key : queryParams.keySet()){
     *                 QueryRoleUserByCompanyCodeDTO value = (QueryRoleUserByCompanyCodeDTO) queryParams.get(key);
     *                 request.setData(value);
     *             }
     *         }else{
     *             request.setData(new QueryRoleUserByCompanyCodeDTO());
     *         }
     */
    @Override
    public List<UserInfoOutDTO> queryRoleUserByCompanyCode(QueryRoleUserByCompanyCodeDTO dto) throws ServiceBizException {
        RequestDTO<QueryRoleUserByCompanyCodeDTO> request = new RequestDTO<QueryRoleUserByCompanyCodeDTO>();
        request.setData(dto);
        String jsonStr = JSONObject.toJSONString(request);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<RequestDTO> httpEntity = new HttpEntity<>(request, httpHeaders);
        String url = midUrlProperties.getMidEndAuthCenter() + midUrlProperties.getDealerUser();
        ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(url, HttpMethod.POST, httpEntity,
                ResponseDTO.class);
        if(responseEntity.getBody()!=null) {
            if(SUCCESS_CODE.equals(responseEntity.getBody().getReturnCode())) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                List<UserInfoOutDTO> convertValue = objectMapper
                        .convertValue(responseEntity.getBody().getData(), new TypeReference<List<UserInfoOutDTO>>(){});
                return convertValue;
            }else {
                throw new ServiceBizException("通过中台接口经销商代码和角色代码查询员工列表失败");
            }
        }
        return null;
    }

    /*
    * 根据经销商代码查询经销商信息
    * */
    @Override
    public OrgVo getDealerInfo(List<String> ownerCodeList) {
        if(CollectionUtils.isEmpty(ownerCodeList)){
            logger.error("getDealerInfo为空返回");
            return new OrgVo();
        }
        OrgQueryDto dto = new OrgQueryDto();
        dto.setOrgCodes(ownerCodeList);
        com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO<OrgVo> responseDTO = organizationClient.orgInfo(dto);
        if(responseDTO.Success()){
            logger.info("getDealerInfo:{}",responseDTO.getData());
            return responseDTO.getData();
        }
        logger.error("未查询到经销商代码");
        return new OrgVo();
    }

    /*
    * 查询该部门/组织 下面的组织id
    * */
    @Override
    public List<Long> getChildOrgIdByOrgId(String orgId) {
        if (StringUtils.isEmpty(orgId)) {
            logger.error("getChildOrgIdByOrgId为空返回");
            return Collections.emptyList();
        }
        com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO<String> orgs = organizationClient.getOrgs(orgId);
        if (orgs.Success()) {
            String data = orgs.getData();
            return Arrays.stream(data.split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        logger.error("未查询到组织id对应信息");
        return Collections.emptyList();
    }

    /*
    * 查询userId对应的用户 组织信息
    * */
    @Override
    public UserOrgInfoDTO getUserOrgInfo(String userId) {
        if(StringUtils.isEmpty(userId)){
            logger.error("getUserOrgInfo空返回");
            return new UserOrgInfoDTO();
        }
        com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO<UserOrgInfoDTO> userOrgInfoDTOResponseDTO = authCenterClient.userInfo(userId);
        if(userOrgInfoDTOResponseDTO.Success()){
            return userOrgInfoDTOResponseDTO.getData();
        }
        logger.error("未查询到用户{}对应的信息",userId);
        return new UserOrgInfoDTO();
    }

    /*
    * 查询某个组织下所有人
    * */
    @Override
    public List<UserOrgInfoDTO> getCompanyAllUser(Long orgId) {
        if(StringUtils.isEmpty(orgId)){
            return Collections.emptyList();
        }
        EmpQueryDto empQueryDto = new EmpQueryDto(orgId);
        com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO<PageResponseDTO<List<UserOrgInfoDTO>>> dto = authCenterClient.empInfo(empQueryDto);
        if(dto.Success()){
            PageResponseDTO<List<UserOrgInfoDTO>> data = dto.getData();
            logger.info("getCompanyAllUser:{}", data);
            List<UserOrgInfoDTO> records = data.getRecords();
            if(CollectionUtils.isEmpty(records)){
                return Collections.emptyList();
            }
            return records;
        }
        return Collections.emptyList();
    }

    /*
    * 根据userIdList查询对应orgId
    * */
    @Override
    public List<OrgVo> getOrgIdBy(List<Integer> userIdList) {
        EmpQueryDto empQueryDto = new EmpQueryDto(userIdList);
        com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO<List<OrgVo>> responseDTO = authCenterClient.empList(empQueryDto);
        if(responseDTO.Success()){
            logger.info("getDealerInfo:{}",responseDTO.getData());
            return responseDTO.getData();
        }
        return Collections.emptyList();
    }

    /*
     * 根据经销商代码查询所有员工
     * */
    @Override
    public List<UserOrgInfoDTO> empDealerInfo(String ownerCode) {
        if(StringUtils.isEmpty(ownerCode)){
            return Collections.emptyList();
        }
        com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO<List<UserOrgInfoDTO>> dto = authCenterClient.empDealerInfo(new EmpQueryDto(ownerCode));
        if(dto.Success()){
            logger.info("getDealerInfo:{}",dto.getData());
            return dto.getData();
        }
        return Collections.emptyList();
    }

    //获取车型名称, 使用注解缓存1天
    @Override
    @Cacheable(value = "allModeList", key = "'allModeList'", cacheManager = "cacheManager", unless = "#result == null")
    public List<AllModeDto> selectAllModeList() {
        try {
            com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO<List<AllModeDto>> res = midEndBasicDataCenterClient.allModeList(new EmpQueryDto());
            if(res == null){
                logger.info("selectAllModeList,res == null");
                return null;
            }
            List<AllModeDto> list = res.getData();
            if(CollectionUtils.isEmpty(list)){
                logger.info("selectAllModeList,list is empty");
                return null;
            }
            logger.info("selectAllModeList,list:{}", list.size());
            return list;
        } catch (Exception e) {
            logger.error("selectAllModeList,查询全量车型列表时发生异常", e);
            return null;
        }
    }

    @Override
    public <T> Object queryCommon(T baseVO, String host, String api) throws ServiceBizException {
        com.yonyou.dmscloud.framework.domains.dto.basedata.RequestDTO<T> request = new com.yonyou.dmscloud.framework.domains.dto.basedata.RequestDTO<>();
        RestTemplate restTemplate = new RestTemplate();
        request.setData(baseVO);
        HttpHeaders httpHeaders = volvoHttpUtils.getHeaders();
        HttpEntity<com.yonyou.dmscloud.framework.domains.dto.basedata.RequestDTO> httpEntity = new HttpEntity<>(request, httpHeaders);
        String url = host + api;
        ResponseEntity<com.yonyou.dmscloud.framework.domains.dto.basedata.ResponseDTO> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity,
                com.yonyou.dmscloud.framework.domains.dto.basedata.ResponseDTO.class);
        if (responseEntity.getBody() != null) {
            if (SUCCESS_CODE.equals(responseEntity.getBody().getReturnCode())) {
                return responseEntity.getBody().getData();
            }
        }
        return null;
    }


}
