package com.yonyou.dmscus.customer.service.impl.invitationautocreate;

import cn.hutool.core.util.ObjectUtil;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.invitationautocreate.DailyMileageLogMapper;
import com.yonyou.dmscus.customer.dto.RepairOrderVO;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.feign.RepairCommonClient;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.feign.vo.ClueParamVO;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.invitationautocreate.InviteVehicleDoTaskService;
import com.yonyou.dmscus.customer.service.invitationautocreate.InviteVehicleTaskService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.dmscus.customer.util.common.Utility;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * 车辆邀约任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-07
 */
@Service
@Slf4j
@RefreshScope
public class InviteVehicleDoTaskServiceImpl implements InviteVehicleDoTaskService {
    @Autowired
    RepairCommonClient repairCommonClient;
    @Autowired
    ReportCommonClient reportCommonClient;

    @Autowired
    InviteVehicleTaskService inviteVehicleTaskService;
    @Autowired
    InviteVehicleRecordService inviteVehicleRecordService;
    @Resource
    DailyMileageLogMapper dailyMileageLogMapper;


    @Override
    public void inviteAutoCreateTask(String createDate, String ownerCode, String orderNo) {
        log.info("inviteAutoCreateTask,start, ownerCode:{}, orderNo:{}", ownerCode, orderNo);
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        log.info("inviteAutoCreateTask,createDate:{}", createDate);
        //工单
        this.addTaskByRepairOrder(createDate, ownerCode, orderNo);
        //VOC事故关闭/工单进厂关闭保修到期线索
        inviteVehicleTaskService.closeClues(createDate);
        //关闭保修线索
        inviteVehicleTaskService.closeGuarantee(createDate);
        //易损件-轮胎
        inviteVehicleTaskService.createTyreTask(createDate);
        log.info("inviteAutoCreateTask,end");
    }

    @Override
    public void addTaskByRepairOrder(String createDate, String ownerCode, String orderNo) {
        log.info("addTaskByRepairOrder,start:{},createDate:{}, ownerCode:{}, orderNo:{}", createDate, ownerCode, orderNo);
        //查询1天内的工单
        List<VehicleOwnerVO> list = reportCommonClient.queryRegularMaintain(createDate, 1);
        if(CollectionUtils.isEmpty(list)){
            log.info("addTaskByRepairOrder,CollectionUtils.isEmpty(list)");
            return;
        }
        log.info("addTaskByRepairOrder,list:{}",list.size());
        //查询vin对应的车主信息
        inviteVehicleTaskService.batchQueryUserInfo(list);
        //查询保养工单
        String beforeDate = Utility.getBeforeDate(createDate);
        log.info("addTaskByRepairOrder,beforeDate:{}", beforeDate);
        List<String> codes = list.stream().map(VehicleOwnerVO::getDealerCode).distinct().collect(Collectors.toList());
        ClueParamVO paramsVo = new ClueParamVO();
        paramsVo.setCreateDate(beforeDate);
        paramsVo.setOwnerCode(codes);
        paramsVo.setRoNo(orderNo);
        List<RepairOrderVO> maiLList = repairCommonClient.queryMaiRepairOrder(paramsVo);
        if (CollectionUtils.isEmpty(maiLList)) {
            log.info("addTaskByRepairOrder,CollectionUtils.isEmpty(maiLList)");
            return;
        }
        log.info("addTaskByRepairOrder,maiLList:{}",maiLList.size());
        //白名查询
        List<String> listCode = inviteVehicleRecordService.getWhiteLists(CommonConstants.MOD_TYPE_91111011,CommonConstants.MOD_TYPE_91111015);
        log.info("addTaskByRepairOrder,listCode:{}", listCode);
        Map<String, RepairOrderVO> maiMap = maiLList.stream()
                .collect(Collectors.toMap(vo -> vo.getDealerCode() + vo.getRoNo(), Function.identity(), (key1, key2) -> key2));
        String vin = null;
        String dealerCode;
        String roNo;
        RepairOrderVO ro;
        for (VehicleOwnerVO vo : list) {
            try {
                dealerCode = vo.getDealerCode();
                roNo = vo.getRoNo();
                vin = vo.getVin();
                log.info("addTaskByRepairOrder,vin:{},dealerCode:{},roNo:{}", vin, dealerCode, roNo);
                ro = maiMap.get(dealerCode + roNo);
                //存在则表示是保养工单
                if (ObjectUtil.isNotEmpty(ro)) {
                    log.info("addTaskByRepairOrder,ObjectUtil.isNotEmpty(ro),vin:{},dealerCode:{},roNo:{}", vin, dealerCode, roNo);
                    //开始执行定保任务逻辑
                    inviteVehicleTaskService.doMaintainTask(vo, ro, listCode);
                }
            } catch (Exception e) {
                log.info("addTaskByRepairOrder,Exception:{}", e);
                dailyMileageLogMapper.SetErrorlog("工单创建邀约任务异常", e.getMessage(), e.getStackTrace()
                        .toString(), vin);
            }
        }
        log.info("addTaskByRepairOrder,end");
    }
}
