package com.yonyou.dmscus.customer.service.inviteInsurance;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscloud.function.utils.common.NumberUtil;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.faultLight.TtFaultCallDetailsMapper;
import com.yonyou.dmscus.customer.dao.faultLight.TtFaultCallRegisterMapper;
import com.yonyou.dmscus.customer.dao.inviteInsurance.*;
import com.yonyou.dmscus.customer.dao.voicemanage.CallDetailsMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.SaCustomerNumberMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.SaWorkNumberMapper;
import com.yonyou.dmscus.customer.dto.CheckRepairOrderDTO;
import com.yonyou.dmscus.customer.dto.RepairOrderVO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.UserInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.*;
import com.yonyou.dmscus.customer.entity.dto.middleground.QueryRoleUserByCompanyCodeDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.UserInfoOutDTO;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillDTO;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.SaCustomerNumberDTO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultCallDetailsPO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultCallRegisterPO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.*;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaCustomerNumberPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaWorkNumberPO;
import com.yonyou.dmscus.customer.enums.InvitationTypeEnum;
import com.yonyou.dmscus.customer.feign.DmscusRepairClient;
import com.yonyou.dmscus.customer.feign.RepairCommonClient;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.middleInterface.OwnerVehicleVO;
import com.yonyou.dmscus.customer.service.common.IMiddleGroundVehicleService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightService;
import com.yonyou.dmscus.customer.service.httplog.HttpLogAiService;
import com.yonyou.dmscus.customer.service.impl.voicemanage.WorkNumberServiceContext;
import com.yonyou.dmscus.customer.service.middleground.BasicdataCenterService;
import com.yonyou.dmscus.customer.service.talkskill.TalkskillService;
import com.yonyou.dmscus.customer.service.voicemanage.CallDetailsService;
import com.yonyou.dmscus.customer.service.voicemanage.SaCustomerNumberService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.dmscus.customer.utils.ai.DccHttpHelper;
import com.yonyou.dmscus.customer.utils.ai.DccResponseUtil;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;

import org.apache.ibatis.annotations.Param;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

import java.math.BigInteger;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * 车辆邀约续保记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@Service
public class InviteInsuranceVehicleRecordServiceImpl implements InviteInsuranceVehicleRecordService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteInsuranceVehicleRecordMapper inviteInsuranceVehicleRecordMapper;
    @Resource
    InviteInsuranceVehicleRecordDetailService inviteInsuranceVehicleRecordDetailService;
    @Resource
    InviteInsuranceVehicleSaRefMapper inviteInsuranceVehicleSaRefMapper;
    @Autowired
    TalkskillService talkskillService;
    @Resource
    InviteInsuranceVehicleTaskMapper inviteInsuranceVehicleTaskMapper;
    @Resource
    InviteInsuranceCustomerInfoMapper inviteInsuranceCustomerInfoMapper;

    @Resource
    CallDetailsMapper callDetailsMapper;

    @Resource
    CallDetailsService callDetailsService;

    @Autowired
    WorkNumberServiceContext workNumberServiceContext;

    @Autowired
    RepairCommonClient repairCommonClient;

    @Autowired
    ReportCommonClient reportCommonClient;

    @Resource
    InviteInsuranceVehicleCustomerNumberMapper inviteInsuranceVehicleCustomerNumberMapper;

    @Resource
    SaWorkNumberMapper saWorkNumberMapper;

    @Value("${ai.telecom.common.url_mapping_register}")
    String URL_MAPPING_REGISTER;  //呼叫登记

    @Resource
    IMiddleGroundVehicleService iMiddleGroundVehicleService;

    @Resource
    DmscusRepairClient dmscusRepairClient;
    @Autowired
    HttpLogAiService httpLogAiService;
    @Autowired
    SaCustomerNumberService saCustomerNumberService;
    @Autowired
    FaultLightService faultLightService;
    @Resource
    SaCustomerNumberMapper saCustomerNumberMapper;
    @Resource
    TtFaultCallRegisterMapper faultCallRegisterMapper;
    @Resource
    TtFaultCallDetailsMapper faultCallDetailsMapper;

    @Resource
    BasicdataCenterService basicdataCenterService;


    /**
     * 分页查询----保险跟进
     *
     * <AUTHOR>
     */
    @Override
    public IPage<InviteInsuranceVehicleRecordDTO> selectFollowInsureRecord(Page page, InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO) {

        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (inviteInsuranceVehicleRecordDTO == null) {
            inviteInsuranceVehicleRecordDTO = new InviteInsuranceVehicleRecordDTO();
        }
        InviteInsuranceVehicleRecordPO inviteInsuranceVehicleRecordPO = inviteInsuranceVehicleRecordDTO.transDtoToPo(InviteInsuranceVehicleRecordPO.class);
        inviteInsuranceVehicleRecordPO.setDealerCode(loginInfoDto.getOwnerCode());
        List<InviteInsuranceVehicleRecordPO> list = inviteInsuranceVehicleRecordMapper.selectFollowInsureRecord(page,
                inviteInsuranceVehicleRecordPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        }

        page.setRecords(setRecordDTOS(list));
        return page;
    }

    private List<InviteInsuranceVehicleRecordDTO> setRecordDTOS(List<InviteInsuranceVehicleRecordPO> list) {
        if (CommonUtils.isNullOrEmpty(list)) {
            return null;
        }
        // 优化3 循环查库改为一次查库
        List<InviteInsuranceVehicleRecordDTO> result = list.stream()
                .map(m -> m.transPoToDto(InviteInsuranceVehicleRecordDTO.class))
                .collect(Collectors.toList());
        // 收集id
        List<Long> ids = result.stream().map(InviteInsuranceVehicleRecordDTO::getId).collect(Collectors.toList());
        if (CommonUtils.isNullOrEmpty(ids)) {
            return result;
        }
        // 查询最近一次通话记录详情
        List<InviteInsuranceVehicleRecordPO> calls = inviteInsuranceVehicleRecordMapper.selectLatelyCalls(ids);
        if (CommonUtils.isNullOrEmpty(calls) && calls.size() > 0) {
            return result;
        }
        // 分组数据集
        Map<Long, List<InviteInsuranceVehicleRecordPO>> mapRecords = calls.stream().collect(Collectors.groupingBy(InviteInsuranceVehicleRecordPO::getInsuranceId));
        if (CommonUtils.isNullOrEmpty(mapRecords)) {
            return result;
        }
        // 组装数据
        for (InviteInsuranceVehicleRecordDTO res : result) {
            Optional.ofNullable(mapRecords.get(res.getId())).ifPresent(v -> {
                res.setScore(v.get(0).getScore());  //通话得分
                res.setCallTime(v.get(0).getCallTime());
                res.setCallLength(v.get(0).getCallLength());
                res.setCallDetailId(v.get(0).getCallDetailId());
            });
        }
        return result;
    }

    /**
     * 保存跟进记录----保险跟进
     *
     * @param dto
     * <AUTHOR>
     */
    @Override
    public int saveFollowInsureRecord(InviteInsuranceVehicleRecordDetailDTO dto) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        InviteInsuranceVehicleRecordPO inviteInsuranceVehicleRecordPO = inviteInsuranceVehicleRecordMapper.selectById(dto.getInviteId());
        inviteInsuranceVehicleRecordPO.setFollowStatus(dto.getStatus());
        //继续跟进
        if (CommonConstants.FOLLOW_STATUS_IV.equals(dto.getStatus())) {
            inviteInsuranceVehicleRecordPO.setPlanFollowDate(dto.getPlanDate());
        } else {
            inviteInsuranceVehicleRecordPO.setPlanFollowDate(null);
        }
        if (null == inviteInsuranceVehicleRecordPO.getFirstFollowDate()) {
            inviteInsuranceVehicleRecordPO.setFirstFollowDate(new Date());
        }
        if (null != dto.getAdviseInDate()) {
            inviteInsuranceVehicleRecordPO.setAdviseInDate(dto.getAdviseInDate());
            InviteInsuranceVehicleTaskPO inviteInsuranceVehicleTaskPO = inviteInsuranceVehicleTaskMapper.queryTaskByInviteId(dto.getId());
            if (inviteInsuranceVehicleTaskPO != null){
                inviteInsuranceVehicleTaskPO.setAdviseInDate(dto.getAdviseInDate());
                inviteInsuranceVehicleTaskMapper.updateById(inviteInsuranceVehicleTaskPO);
            }
        }
        inviteInsuranceVehicleRecordPO.setActualFollowDate(new Date());//实际跟进日期
        inviteInsuranceVehicleRecordPO.setSaId(String.valueOf(loginInfoDto.getUserId()));
        inviteInsuranceVehicleRecordPO.setSaName(loginInfoDto.getUserName());
        inviteInsuranceVehicleRecordPO.setContent(dto.getContent());
        inviteInsuranceVehicleRecordPO.setLoseReason(dto.getLoseReason());


        //执行更新跟进状态
        inviteInsuranceVehicleRecordMapper.updateById(inviteInsuranceVehicleRecordPO);
        Long insuranceDetailId = this.addInviteInsuranceVehicleRecordDetail(dto);
        //this.updateCallDetailsForDetailId(dto.getInviteId(),insuranceDetailId);
        this.maintenvehicleSaRef(inviteInsuranceVehicleRecordPO.getVin(), inviteInsuranceVehicleRecordPO.getDealerCode());
        //更新未关联的通话记录
        inviteInsuranceVehicleCustomerNumberMapper.updateInsuranceDetailId(dto.getInviteId(), insuranceDetailId);

        //经销商在线索跟进时跟进失败后，选择跟进失败原因【非自店有效续保线索】时，这条线索的状态变为“续保关闭”
        if(!StringUtils.isNullOrEmpty(dto.getLoseReason())){
            if(81851006==dto.getLoseReason()){
                inviteInsuranceVehicleRecordMapper.updateRecordStatusClose(inviteInsuranceVehicleRecordPO.getVin(),inviteInsuranceVehicleRecordPO.getDealerCode());
            }
        }
        return 1;
    }

    /**
     * 保险跟进失败 -- 计划任务
     *
     * <AUTHOR>
     */
    @Override
    public void updateInsureFollowStatus() {

        List<InviteInsuranceVehicleRecordPO> poList = inviteInsuranceVehicleRecordMapper.selectInsuranceInvitePlan();
        if (!CommonUtils.isNullOrEmpty(poList)){
            List<Long> idList = new ArrayList<Long>();
            List<Long> idList2 = new ArrayList<Long>();
            for(InviteInsuranceVehicleRecordPO recordPO : poList){
                idList.add(recordPO.getId());
                CheckRepairOrderDTO orderDTO = reportCommonClient.checkRepairOrderByVin(recordPO.getVin());
                if (null != orderDTO && null != orderDTO.getIsEnterFactory() && orderDTO.getIsEnterFactory() == 10041001){
                    idList2.add(recordPO.getId());
                }
            }
            if (!CommonUtils.isNullOrEmpty(idList)){
                inviteInsuranceVehicleRecordMapper.updateInsureFollowStatus(idList,"99999");
                inviteInsuranceVehicleRecordMapper.updateInsureFollowTaskStatus(idList,"99999");
            }
            /*if (rowsUpdate > 0) {
                logger.info("保险跟进失败计划任务执行成功，已修改了" + rowsUpdate + "条数据");
            } else {
                logger.info("保险跟进失败计划任务执行成功，没有符合条件的数据去修改");
            }*/
            if(!CommonUtils.isNullOrEmpty(idList2)){
                inviteInsuranceVehicleRecordMapper.insertInsureInviteTask(idList2,"99999");
            }
        }
    }

    /**
     * 导出查询 ---- 保险跟进
     *
     * @param inviteVehicleRecordDTO
     * <AUTHOR>
     */
    @Override
    public List<Map> exportExcelFollowInsure(InviteInsuranceVehicleRecordDTO inviteVehicleRecordDTO) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        InviteInsuranceVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordDTO.transDtoToPo(InviteInsuranceVehicleRecordPO.class);
        inviteVehicleRecordPO.setDealerCode(loginInfoDto.getOwnerCode());
        return inviteInsuranceVehicleRecordMapper.exportExcelFollowInsure(inviteVehicleRecordPO);
    }

    /**
     * 新增跟进记录
     *
     * @param dto
     */
    private Long addInviteInsuranceVehicleRecordDetail(InviteInsuranceVehicleRecordDetailDTO dto) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        InviteInsuranceVehicleRecordDetailDTO recordDetail = new InviteInsuranceVehicleRecordDetailDTO();
        recordDetail.setInviteId(dto.getInviteId());
        recordDetail.setContent(dto.getContent());
        recordDetail.setStatus(dto.getStatus());
        recordDetail.setPlanDate(dto.getPlanDate());//下次跟进日期
        recordDetail.setActualDate(new Date());//实际跟进日期
        recordDetail.setLoseReason(dto.getLoseReason());
        recordDetail.setFeedback(dto.getFeedback());
        recordDetail.setMode(dto.getMode());
        recordDetail.setNotFollowReason(dto.getNotFollowReason());
        recordDetail.setRemark(dto.getRemark());
        recordDetail.setOwnerCode(loginInfoDto.getOwnerCode());//经销商
        recordDetail.setSaId(String.valueOf(loginInfoDto.getUserId()));
        recordDetail.setSaName(loginInfoDto.getUserName());
        recordDetail.setDealerCode(loginInfoDto.getOwnerCode());
        recordDetail.setVin(dto.getVin());
        recordDetail.setAdviseInDate(dto.getAdviseInDate());
        recordDetail.setInviteType(82381003);
        Long id = inviteInsuranceVehicleRecordDetailService.addInviteInsuranceVehicleRecordDetail(recordDetail);
        return id;
    }

    /**
     * 更新ai通话详情和跟进记录关系
     * @param insuranceId
     * @param insuranceDetailId
     */
    /*private void updateCallDetailsForDetailId(Long insuranceId, Long insuranceDetailId) {
        callDetailsMapper.updateCallDetailsForDetailId(insuranceId,insuranceDetailId);
    }*/

    /**
     * 维护车辆和上次跟进人员关系
     *
     * @param vin
     * @param dealerCode
     */
    private void maintenvehicleSaRef(String vin, String dealerCode) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        LambdaQueryWrapper<InviteInsuranceVehicleSaRefPO> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(InviteInsuranceVehicleSaRefPO::getVin, vin);
        queryWrapper.eq(InviteInsuranceVehicleSaRefPO::getDealerCode, dealerCode);
        InviteInsuranceVehicleSaRefPO rs = inviteInsuranceVehicleSaRefMapper.selectOne(queryWrapper);
        if (rs != null) {//更新
            rs.setSaId(String.valueOf(loginInfoDto.getUserId()));
            rs.setSaName(loginInfoDto.getUserCode());
            inviteInsuranceVehicleSaRefMapper.updateById(rs);
        } else {//新增
            InviteInsuranceVehicleSaRefPO po = new InviteInsuranceVehicleSaRefPO();
            po.setVin(vin);
            po.setDealerCode(dealerCode);
            po.setSaId(String.valueOf(loginInfoDto.getUserId()));
            po.setSaName(loginInfoDto.getUserName());
            inviteInsuranceVehicleSaRefMapper.insert(po);
        }
    }

    /**
     * 查询邀约线索
     *
     * @param page
     * @param inviteInsuranceVehicleRecordDTO
     * @return
     */
    @Override
    public IPage<InviteInsuranceVehicleRecordDTO> getInviteInsuranceVehicleRecord(Page page,
                                                                                  InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (inviteInsuranceVehicleRecordDTO == null) {
            inviteInsuranceVehicleRecordDTO = new InviteInsuranceVehicleRecordDTO();
        }
        InviteInsuranceVehicleRecordPO inviteInsuranceVehicleRecordPO = inviteInsuranceVehicleRecordDTO.transDtoToPo(InviteInsuranceVehicleRecordPO.class);
        inviteInsuranceVehicleRecordPO.setDealerCode(loginInfoDto.getOwnerCode());
        if (inviteInsuranceVehicleRecordDTO.getIsself() != null && inviteInsuranceVehicleRecordDTO.getIsself() == 1) {
            inviteInsuranceVehicleRecordPO.setSaId(String.valueOf(loginInfoDto.getUserId()));
        }
        if (inviteInsuranceVehicleRecordDTO.getIsNoDistribute() != null && inviteInsuranceVehicleRecordDTO.getIsNoDistribute()) {
            inviteInsuranceVehicleRecordPO.setIsNoDistribute(10041001);
        }
        if (inviteInsuranceVehicleRecordDTO.getIsWaitDistribute() != null && inviteInsuranceVehicleRecordDTO.getIsNoDistribute()) {
            inviteInsuranceVehicleRecordPO.setIsWaitDistribute(10041001);
        }
        List<InviteInsuranceVehicleRecordPO> list = inviteInsuranceVehicleRecordMapper.selectInviteInsuranceVehicleRecord(page,
                inviteInsuranceVehicleRecordPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteInsuranceVehicleRecordDTO> result = list.stream().map(m -> m.transPoToDto(InviteInsuranceVehicleRecordDTO.class)
            ).collect(Collectors.toList());
            page.setRecords(result);
            return page;
        }
    }

    /**
     * 查询待分配数量
     *
     * @return
     */
    @Override
    public Integer getNeedDistribute(List<Integer> leaveIds) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        return inviteInsuranceVehicleRecordMapper.getNeedDistribute(loginInfoDto.getOwnerCode(), leaveIds);
    }

    @Override
    public List<InviteInsuranceVehicleRecordDTO> getInviteInsuranceVehicleRecordInfo(String vin, Long id) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        List<InviteInsuranceVehicleRecordPO> invs = inviteInsuranceVehicleRecordMapper.selectInsuranceVehicleRecordAndSubcues(id);
        if(CommonUtils.isNullOrEmpty(invs)){
            return null;
        }else{
            List<InviteInsuranceVehicleRecordDTO> list = invs.stream().map(m -> m.transPoToDto(InviteInsuranceVehicleRecordDTO.class)).collect
                    (Collectors.toList());
            for (InviteInsuranceVehicleRecordDTO dto : list) {
                if (dto.getInviteType() == 82381003) {
                    //续保
                    List<TalkskillDTO> skillList = talkskillService.queryTalkskill(loginInfoDto.getOwnerCode(), "邀约", "续保");
                    dto.setTalkskill(skillList);
                }
            }
            return list;
        }
    }

    /**
     * 邀约线索分配
     *
     * @param saSllocateDlrDto
     * @return
     */
    @Override
    public int saveInsuranceSaSllocate(InsuranceSaSllocateDlrDto saSllocateDlrDto) {
        List<InviteInsuranceVehicleRecordDTO> inviteInsuranceVehicleRecordList = saSllocateDlrDto.getInviteInsuranceVehicleRecordList();
        List<UserInfoDTO> saList = checkAndSetSA(saSllocateDlrDto);
        if(org.springframework.util.CollectionUtils.isEmpty(saList)){
            return 0;
        }
        int count = saList.size();
        if (CommonConstants.SASLLOCATEDLRDTO_RULE_TYPE_82121001.equals(saSllocateDlrDto.getRuleType())) {//平均分配
            if(!CommonUtils.isNullOrEmpty(inviteInsuranceVehicleRecordList)){
                for (int i = 0; i < inviteInsuranceVehicleRecordList.size(); i++) {
                    InviteInsuranceVehicleRecordDTO record = inviteInsuranceVehicleRecordList.get(i);
                    int index = i % count;
                    UserInfoDTO sa = saList.get(index);
                    record.setSaId(String.valueOf(sa.getId()));
                    record.setSaName(sa.getUsername());
                    this.update(record.getId(), record);
                }
            }
        } else if ( CommonConstants.SASLLOCATEDLRDTO_RULE_TYPE_82121002.equals(saSllocateDlrDto.getRuleType())) {//根据上次接待SA分配
            if(!CommonUtils.isNullOrEmpty(inviteInsuranceVehicleRecordList)){
                for (int i = 0; i < inviteInsuranceVehicleRecordList.size(); i++) {
                    InviteInsuranceVehicleRecordDTO record = inviteInsuranceVehicleRecordList.get(i);
                    record.setSaId(record.getLastSaId());
                    record.setSaName(record.getLastSaName());
                    this.update(record.getId(), record);
                }
            }
        } else if (CommonConstants.SASLLOCATEDLRDTO_RULE_TYPE_82121003.equals(saSllocateDlrDto.getRuleType())) {//以上次接待SA为主,平均分配为辅
            if(!CommonUtils.isNullOrEmpty(inviteInsuranceVehicleRecordList)){
                //每个人的平均值,超过平均值就不在给这个人分配任务
                double average = inviteInsuranceVehicleRecordList.size() * 1.0 / count;
                //记录每个任务的分配数量
                HashMap<String, Integer> map = new HashMap<String, Integer>();
                int hasCount = 0;//已经分配数量，除去上次接待SA分配
                for (int i = 0; i < inviteInsuranceVehicleRecordList.size(); i++) {
                    InviteInsuranceVehicleRecordDTO record = inviteInsuranceVehicleRecordList.get(i);
                    UserInfoDTO sa = null;
                    if (record.getLastSaId() != null) {
                        record.setSaId(record.getLastSaId());
                        record.setSaName(record.getLastSaName());
                        this.update(record.getId(), record);
                    } else {
                        int index = hasCount % count;
                        sa = saList.get(index);
                        record.setSaId(String.valueOf(sa.getId()));
                        record.setSaName(sa.getUsername());
                        this.update(record.getId(), record);
                        hasCount++;
                    }
                    Integer cur = map.get(record.getSaId());
                    if (cur == null) {
                        cur = Integer.valueOf(1);
                    } else {
                        cur = cur + 1;
                    }
                    map.put(record.getSaId(), cur);
                    if (record.getLastSaId() == null && cur >= average) {//如果不是上次接待SA分配，且分配完当前人已经比平均值大,
                        saList.remove(sa);
                        count = saList.size();//改变分配基数
                    }
                }
            }
        }
        return 1;
    }

    private List<UserInfoDTO> checkAndSetSA(InsuranceSaSllocateDlrDto saSllocateDlrDto) {
        List<UserInfoDTO> saList = saSllocateDlrDto.getSelectSa();
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(saList)){
            List<UserInfoOutDTO> roleUserList = getBXZY();
            if(roleUserList.isEmpty()){
                return Lists.newArrayList();
            }
            //进行人员过滤
            List<Long> userIds = roleUserList.stream().map(UserInfoOutDTO::getUserId).collect(Collectors.toList());
            return saList.stream().filter(e -> userIds.contains(e.getId())).collect(Collectors.toList());
        }else{
            return Lists.newArrayList();
        }
    }

    private List<UserInfoOutDTO> getBXZY() {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //校验sa有效性
        QueryRoleUserByCompanyCodeDTO codeDTO = new QueryRoleUserByCompanyCodeDTO();
        List<String> roleCode = new ArrayList<>();
        roleCode.add("BXZY");
        codeDTO.setCompanyCode(loginInfoDto.getOwnerCode());
        codeDTO.setRoleCode(roleCode);
        codeDTO.setIsOnjob(10081001);
        List<UserInfoOutDTO> roleUserList = basicdataCenterService.queryRoleUserByCompanyCode(codeDTO);
        return roleUserList;
    }

    @Override
    public IPage<InviteInsuranceVehicleRecordDTO> selectPageBysql(Page page, InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO) {
        return null;
    }

    @Override
    public List<InviteInsuranceVehicleRecordDTO> selectListBySql(InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO) {
        return null;
    }

    @Override
    public InviteInsuranceVehicleRecordDTO getById(Long id) {
        return null;
    }

    @Override
    public int insert(InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO) {
        return 0;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                     主键ID
     * @param inviteInsuranceVehicleRecordDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO) {
        InviteInsuranceVehicleRecordPO inviteInsuranceVehicleRecordPO = inviteInsuranceVehicleRecordMapper.selectById(id);
        //对对象进行赋值操作
        inviteInsuranceVehicleRecordDTO.transDtoToPo(inviteInsuranceVehicleRecordPO);
        //执行更新
        int row = inviteInsuranceVehicleRecordMapper.updateById(inviteInsuranceVehicleRecordPO);
        return row;
    }

    @Override
    public int deleteById(Long id) {
        return 0;
    }

    @Override
    public int deleteBatchIds(String ids) {
        return 0;
    }

    /**
                * 呼叫登记
     */
	@Override
	public String saveSaCustomerNumber(InviteInsuranceVehicleCustomerNumberDTO saCustomerNumberDTO) {
		//获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //uuid
        String callId= Objects.isNull(saCustomerNumberDTO.getCallId()) ? this.getUUID32() : saCustomerNumberDTO.getCallId();
        //校验当前账号是否绑定AI语音工作号
        LambdaQueryWrapper<SaWorkNumberPO> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(SaWorkNumberPO::getDealerCode, loginInfoDto.getOwnerCode());
        queryWrapper.eq(SaWorkNumberPO::getSaId, saCustomerNumberDTO.getSaId());
        SaWorkNumberPO rs = saWorkNumberMapper.selectOne(queryWrapper);
        if(rs==null){
            throw new DALException("当前账号还未绑定AI语音工作号，不可使用!");
        }
        //校验联系方式格式是否正确
        if(11 != saCustomerNumberDTO.getCusNumber().length()){
        	throw new DALException("手机号应为11位数");
        }

        String operator = rs.getOperator();
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(operator, String.valueOf(CommonConstants.NEW_TELECOM_OPERATOR))) {
            return  operatorIf(saCustomerNumberDTO, callId, rs);
        }


        InviteInsuranceVehicleCustomerNumberPO inPo = new InviteInsuranceVehicleCustomerNumberPO();
        inPo.setCallId(callId);
        inPo.setInsuranceId(saCustomerNumberDTO.getInsuranceId());
        inPo.setInsuranceDetailId(saCustomerNumberDTO.getInsuranceDetailId());
        inPo.setSaId(saCustomerNumberDTO.getSaId());
        inPo.setCusName(saCustomerNumberDTO.getCusName());
        inPo.setCusNumber(saCustomerNumberDTO.getCusNumber());
        inPo.setDealerCode(loginInfoDto.getOwnerCode());
        inPo.setSaName(rs.getSaName());
        inPo.setSaNumber(rs.getSaNumber());
        inPo.setWorkNumber(rs.getWorkNumber());
        inPo.setBatchNo(saCustomerNumberDTO.getBatchNo());
        inviteInsuranceVehicleCustomerNumberMapper.insert(inPo);

        extracted(saCustomerNumberDTO, loginInfoDto);

        //电信 呼叫登记
        Map<String,Object> map =new HashMap<String,Object>();
        map.put("callId", callId);  //登记唯一标识
        map.put("holderNumber", rs.getSaNumber()); //服务顾问手机号
        map.put("workNumber", rs.getWorkNumber()); //工作号
        map.put("customNumber", saCustomerNumberDTO.getCusNumber()); //联系方式
        map.put("expireMinute", 30);  //默认写死30
        logger.info("呼叫登记："+map);
        String str=DccHttpHelper.httpPost(URL_MAPPING_REGISTER, map);
        DccResponseUtil response=JSONObject.parseObject(str,DccResponseUtil.class);
        if(!"0".equals(response.getCode())) {
        	throw new DALException(response.getMessage());
        }
        logger.info("呼叫登记结束："+str);
        //返回绑定的工作号
        return rs.getWorkNumber();
	}

    private void extracted(InviteInsuranceVehicleCustomerNumberDTO saCustomerNumberDTO, LoginInfoDto loginInfoDto) {
        List<InviteInsuranceCustomerInfoDTO> customerInfoDTOList = inviteInsuranceCustomerInfoMapper.selectAllInsuranceCustomerInfo(saCustomerNumberDTO.getInsuranceId(), loginInfoDto.getOwnerCode());
        if(CommonUtils.isNullOrEmpty(customerInfoDTOList)){
            InviteInsuranceCustomerInfoPO customerInfoPO = new InviteInsuranceCustomerInfoPO();
            customerInfoPO.setInsuranceId(saCustomerNumberDTO.getInsuranceId());
            customerInfoPO.setDealerCode(loginInfoDto.getOwnerCode());
            customerInfoPO.setInsureName(saCustomerNumberDTO.getCusName());
            customerInfoPO.setInsureNumber(saCustomerNumberDTO.getCusNumber());
            inviteInsuranceCustomerInfoMapper.insert(customerInfoPO);
        }else{
            boolean isExist = false;
            for(InviteInsuranceCustomerInfoDTO dto : customerInfoDTOList){
                if((saCustomerNumberDTO.getCusName().equals(dto.getInsureName()) || saCustomerNumberDTO.getCusName() == dto.getInsureName())
                && (saCustomerNumberDTO.getCusNumber().equals(dto.getInsureNumber()) || saCustomerNumberDTO.getCusNumber() == dto.getInsureNumber())){
                    isExist = true;
                }
            }
            if(!isExist){
                InviteInsuranceCustomerInfoPO customerInfoPO = new InviteInsuranceCustomerInfoPO();
                customerInfoPO.setInsuranceId(saCustomerNumberDTO.getInsuranceId());
                customerInfoPO.setDealerCode(loginInfoDto.getOwnerCode());
                customerInfoPO.setInsureName(saCustomerNumberDTO.getCusName());
                customerInfoPO.setInsureNumber(saCustomerNumberDTO.getCusNumber());
                inviteInsuranceCustomerInfoMapper.insert(customerInfoPO);
            }
        }
    }

    /**
     * 得到32位的uuid
     * @return
     */
    public  String getUUID32(){
        return UUID.randomUUID().toString().replace("-", "").toLowerCase();
    }


    String operatorIf(InviteInsuranceVehicleCustomerNumberDTO saCustomerNumberDTO, String callId, SaWorkNumberPO rs) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        String   operator = String.valueOf(CommonConstants.NEW_TELECOM_AXB);
        String userOrderId = workNumberServiceContext.getUserOrderId("IR", loginInfoDto.getOwnerCode(), saCustomerNumberDTO.getInsuranceId() + "", callId);
        logger.info("operatorIf IR userOrderId:{}", userOrderId);
        Map<String, Object> registerResultMap = workNumberServiceContext.register(operator, callId,
                rs.getSaNumber(), rs.getWorkNumber(), saCustomerNumberDTO.getCusNumber(), userOrderId);
        boolean isSuccessOfRegister = workNumberServiceContext
                .isSuccess(operator, CommonConstants.WORK_NUMBER_MENTHODTYPE_REGISTER, registerResultMap);
        InviteInsuranceVehicleCustomerNumberPO inPo = new InviteInsuranceVehicleCustomerNumberPO();
        if (!isSuccessOfRegister) {
            String resMessage=registerResultMap.containsKey("message")?registerResultMap.get("message").toString():registerResultMap.get("msg").toString();
            throw new ServiceBizException(resMessage);
        } else {
            logger.info("当前操作供应商==={}，工作号===={}，bindId==={}", operator,
                    rs.getWorkNumber(), inPo.getCallId());
            inPo.setCallId(callId);
            inPo.setInsuranceId(saCustomerNumberDTO.getInsuranceId());
            inPo.setInsuranceDetailId(saCustomerNumberDTO.getInsuranceDetailId());
            inPo.setSaId(saCustomerNumberDTO.getSaId());
            inPo.setCusName(saCustomerNumberDTO.getCusName());
            inPo.setCusNumber(saCustomerNumberDTO.getCusNumber());
            inPo.setDealerCode(loginInfoDto.getOwnerCode());
            inPo.setSaName(rs.getSaName());
            inPo.setSaNumber(rs.getSaNumber());
            inPo.setWorkNumber(rs.getWorkNumber());
            inPo.setBatchNo(saCustomerNumberDTO.getBatchNo());
            logger.info("====呼叫登记保存信息InviteInsuranceVehicleCustomerNumberPO======{}", inPo);
            logger.info("====loginInfo========={}", loginInfoDto);
            inviteInsuranceVehicleCustomerNumberMapper.insert(inPo);
        }
        return rs.getWorkNumber();
    }


    /**
              * 查询通话记录
     */
	@Override
	public List<CallDetailsPO> callDetailList(Long insuranceDetailId) {
		return callDetailsMapper.getDetailsByInsuranceDetailId(insuranceDetailId);
	}

	/**
	 * 查询联系人
	 */
	@Override
	public List<InviteInsuranceVehicleCustomerNumberPO> selectCusList(Long id) {
		return inviteInsuranceVehicleCustomerNumberMapper.selectCusList(id);
	}

    /**
     * 查询联系人 --- 投保人+被保人
     * <AUTHOR>
     */
    @Override
    public InviteInsuranceVehicleCustomerNumberPO selectInsuranceUserByVin(String vin) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        return inviteInsuranceVehicleCustomerNumberMapper.selectInsuranceUserByVin(vin,loginInfoDto.getOwnerCode());
    }

    /**
     * 更新线索状态（流失客户的逻辑）
     * 线索的完成状态变成“流失客户”的逻辑：
     * 针对跟进状态为“未跟进”或“继续跟进”，线索完成状态为“未完成”时，且未在获取到易保保单或店内新增保单，当前时间已大于“续保到期时间+30天”的线索，将这批线索的状态修改为“流失客户”
     * 并在对应VIN 24个月内进厂过的经销商店内生成新的任务/线索
     *
     */
    @Override
    public void updateRecordOrderStatus() {
        httpLogAiService.saveHttpLog("更新线索状态（流失客户的逻辑）任务","","","POST","200","更新线索状态（流失客户的逻辑）任务开始","2222");
        try {
            logger.info("========续保的邀约线索到期后修改状态(开始)");
            //查询流失客户的线索
            List<InviteInsuranceVehicleRecordPO> list=inviteInsuranceVehicleRecordMapper.selectLossOfRecord();
            for(InviteInsuranceVehicleRecordPO po:list){
                logger.info("=======流失客户");
                //查询车主车辆信息
                OwnerVehicleVO ownerVehicleVO2 = this.getMidOwnerVehicleVOByVin(po.getVin());
                //24个月内进厂过的经销商店内生成新的任务
                List<String> listStr1 = this.selectRepairOrderByVin2(po.getVin(),po.getAdviseInDate());
                if(!CommonUtils.isNullOrEmpty(listStr1) && listStr1.size() > 0){
                    for(String s:listStr1){
                        logger.info("======24个月进厂VIN："+s);
                        insertInsuranceTaskByRepairNo(po,s,ownerVehicleVO2);
                    }
                }
            }
            logger.info("======更新线索流失客户");
            //更新线索未“流失客户”
            inviteInsuranceVehicleRecordMapper.updateRecordOrderStatus();
        }catch (Exception e){
            logger.info("========续保的邀约线索到期后修改状态(异常)");
            httpLogAiService.saveHttpLog("更新线索状态（流失客户的逻辑）任务","","","POST","500","更新线索状态（流失客户的逻辑）任务","2222");

            e.printStackTrace();
        }finally {
            logger.info("========续保的邀约线索到期后修改状态(结束)");
            httpLogAiService.saveHttpLog("更新线索状态（流失客户的逻辑）任务","","","POST","200","更新线索状态（流失客户的逻辑）任务结束","2222");

        }

    }

    /**
     * 更新线索状态（流失客户的逻辑）
     * 线索的完成状态变成“流失客户”的逻辑：
     * 针对跟进状态为“未跟进”或“继续跟进”，线索完成状态为“未完成”时，且未在获取到易保保单或店内新增保单，当前时间已大于“续保到期时间+30天”的线索，将这批线索的状态修改为“流失客户”
     * 并在对应VIN 24个月内进厂过的经销商店内生成新的任务/线索
     *
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRecordOrderStatusNew() {
        httpLogAiService.saveHttpLog("更新线索状态（流失客户的逻辑）任务","","","POST","200","更新线索状态（流失客户的逻辑）任务开始","2222");
        try {
            logger.info("========续保的邀约线索到期后修改状态(开始)");
            //查询流失客户的线索
            List<InviteInsuranceVehicleRecordPO> list=inviteInsuranceVehicleRecordMapper.selectLossOfRecord();
            List<Long> updateList = new ArrayList<>();
            for(InviteInsuranceVehicleRecordPO po:list){
                updateList.add(po.getId());
                logger.info("=======流失客户");
                //查询车主车辆信息
                OwnerVehicleVO ownerVehicleVO2 = this.getMidOwnerVehicleVOByVin(po.getVin());
                if(ownerVehicleVO2!=null){
                    //24个月内进厂过的经销商店内生成新的任务
                    List<String> listStr1 = this.selectRepairOrderByVin2(po.getVin(),po.getAdviseInDate());
                    if(!CommonUtils.isNullOrEmpty(listStr1) && listStr1.size() > 0){
                        for(String s:listStr1){
                            logger.info("======24个月进厂VIN："+s);
                            insertInsuranceTaskByRepairNoNew(po,s,ownerVehicleVO2);
                        }
                    }
                }
            }
            logger.info("======更新线索流失客户");
            //更新线索未“流失客户”
            if(updateList!= null  &&  updateList.size()>0){
                //分页修改
                updates(updateList);
            }
        }catch (Exception e){
            logger.info("========续保的邀约线索到期后修改状态(异常)");
            httpLogAiService.saveHttpLog("更新线索状态（流失客户的逻辑）任务","","","POST","500","更新线索状态（流失客户的逻辑）任务","2222");

            e.printStackTrace();
        }finally {
            logger.info("========续保的邀约线索到期后修改状态(结束)");
            httpLogAiService.saveHttpLog("更新线索状态（流失客户的逻辑）任务","","","POST","200","更新线索状态（流失客户的逻辑）任务结束","2222");

        }

    }

    /**
     * 补完呼叫登记
     * <p>
     * 附：
     * 1、本方法中字段命名 inviteId ，指代「线索ID」
     *
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String fixSaCustomerNumber(List<FullLeadsFollowDto> followList) {
        logger.info("fixSaCustomerNumber==========followList:{}", followList);
        if (org.springframework.util.CollectionUtils.isEmpty(followList)) {
            logger.info("fixSaCustomerNumber isEmpty followList");
            return null;
        }

        List<Long> yyIds = CollUtil.newArrayList(-1L);
        List<Long> xbIds = CollUtil.newArrayList(-1L);
        List<Long> gzdIds = CollUtil.newArrayList(-1L);
        List<Long> unCheckedInviteIdList = CollUtil.newArrayList(-1L);
        List<Long> unCheckedInsuranceIdList = CollUtil.newArrayList(-1L);
        List<Long> unCheckedFaultIdList = CollUtil.newArrayList(-1L);
        List<Long> checkedInviteIdList = CollUtil.newArrayList();
        List<Long> checkedInsuranceIdList = CollUtil.newArrayList();
        List<Long> checkedFaultIdList = CollUtil.newArrayList();
        List<FullLeadsFollowDto> checkedList = new ArrayList<>();
        // 收集线索id
        for (FullLeadsFollowDto dto : followList) {
            if (null == dto) {
                continue;
            }
            if (String.valueOf(CommonConstants.DICT_IS_YES).equals(dto.getSelectStatus() + "")) {
                checkedList.add(dto);
            }
            if (String.valueOf(InvitationTypeEnum.INVITE_TYPE_INSURANCE.getCode()).equals(dto.getInviteType() + "")) {
                // 续保
                xbIds.add(dto.getId());
                if (!Objects.equals(CommonConstants.DICT_IS_YES, dto.getSelectStatus())) {
                    unCheckedInsuranceIdList.add(dto.getId());
                } else {
                    checkedInsuranceIdList.add(dto.getId());
                }
            } else if (String.valueOf(InvitationTypeEnum.INVITE_TYPE_FAULT_LIGHT.getCode()).equals(dto.getInviteType() + "")) {
                // 故障灯
                gzdIds.add(dto.getId());
                if (!Objects.equals(CommonConstants.DICT_IS_YES, dto.getSelectStatus())) {
                    unCheckedFaultIdList.add(dto.getId());
                } else {
                    checkedFaultIdList.add(dto.getId());
                }
            } else {
                // 邀约
                yyIds.add(dto.getId());
                if (!Objects.equals(CommonConstants.DICT_IS_YES, dto.getSelectStatus())) {
                    unCheckedInviteIdList.add(dto.getId());
                } else {
                    checkedInviteIdList.add(dto.getId());
                }
            }
        }

        //清理未跟进线索的通话记录
        if (unCheckedInsuranceIdList.size() > 1) {
            inviteInsuranceVehicleCustomerNumberMapper.update(null,
                new LambdaUpdateWrapper<InviteInsuranceVehicleCustomerNumberPO>().in(InviteInsuranceVehicleCustomerNumberPO::getInsuranceId,
                        unCheckedInsuranceIdList)
                    .isNull(InviteInsuranceVehicleCustomerNumberPO::getInsuranceDetailId)
                    .set(InviteInsuranceVehicleCustomerNumberPO::getInsuranceDetailId, -1L));
        }
        if (unCheckedInviteIdList.size() > 1) {
            saCustomerNumberMapper.update(null, new LambdaUpdateWrapper<SaCustomerNumberPO>().in(SaCustomerNumberPO::getInviteId, unCheckedInviteIdList)
                .isNull(SaCustomerNumberPO::getDetailId)
                .set(SaCustomerNumberPO::getDetailId, -1L));
        }
        if (unCheckedFaultIdList.size() > 1) {
            faultCallRegisterMapper.update(null, new LambdaUpdateWrapper<TtFaultCallRegisterPO>().in(TtFaultCallRegisterPO::getInviteId, unCheckedFaultIdList)
                .isNull(TtFaultCallRegisterPO::getDetailId)
                .set(TtFaultCallRegisterPO::getDetailId, -1L));
        }

        //通话示例，用于通话记录复制，callId - call
        HashMap<String, SaCustomerNumberDTO> callExampleMap = new HashMap<>();
        //通话记录，callId - callList
        HashMap<String, List<SaCustomerNumberDTO>> callMap = new HashMap<>();
        //通话ID集合,去重
        ArrayList<String> callIdList = new ArrayList<>();
        List<String> existCheckCallList = new ArrayList<>();

        //通话
        List<SaCustomerNumberDTO> customerNumberList = callDetailsMapper.selectSaCustomerNumbers(yyIds, xbIds, gzdIds);
        if (CollectionUtils.isEmpty(customerNumberList)) {
            return null;
        }
        for (SaCustomerNumberDTO call : customerNumberList) {
            existCheckCallList.add(call.getSaType() + call.getCallId() + call.getInviteId());
            //无通话记录ID，无处理价值
            if (Objects.isNull(call) || StringUtils.isBlank(call.getCallId())) {
                continue;
            }
            if (Objects.isNull(callMap.get(call.getCallId()))) {
                callMap.put(call.getCallId(), CollUtil.newArrayList(call));
            } else {
                List<SaCustomerNumberDTO> change = callMap.get(call.getCallId());
                change.add(call);
                callMap.put(call.getCallId(), change);
            }
            if (Objects.isNull(callExampleMap.get(call.getCallId()))) {
                callExampleMap.put(call.getCallId(), call);
                callIdList.add(call.getCallId());
            }
        }
        if (CollUtil.isEmpty(callIdList)) {
            return null;
        }

        List<CallDetailsPO> detailList = callDetailsMapper.selectList(new LambdaQueryWrapper<CallDetailsPO>().in(CallDetailsPO::getCallId, callIdList));
        List<TtFaultCallDetailsPO> faultDetailList = faultCallDetailsMapper.selectList(
            new LambdaQueryWrapper<TtFaultCallDetailsPO>().in(TtFaultCallDetailsPO::getCallId, callIdList));
        Map<String, List<CallDetailsPO>> detailMap = new HashMap<>();
        Map<String, List<TtFaultCallDetailsPO>> faultDetailMap = new HashMap<>();

        if (CollUtil.isNotEmpty(detailList)) {
            detailMap = detailList.stream().collect(Collectors.groupingBy(CallDetailsPO::getCallId));
        }
        if (CollUtil.isNotEmpty(faultDetailList)) {
            faultDetailMap = faultDetailList.stream().collect(Collectors.groupingBy(TtFaultCallDetailsPO::getCallId));
        }


        //补通话明细
        for (String callId : callIdList) {
            if (detailMap.containsKey(callId) && !faultDetailMap.containsKey(callId)) {
                List<CallDetailsPO> exampleList = detailMap.get(callId);
                if (CollUtil.isNotEmpty(exampleList)) {
                    for (CallDetailsPO example : exampleList) {
                        TtFaultCallDetailsPO target = BeanUtil.copyProperties(example, TtFaultCallDetailsPO.class);
                        target.setId(null);
                        faultCallDetailsMapper.insert(target);
                    }
                }
            }
            if (!detailMap.containsKey(callId) && faultDetailMap.containsKey(callId)) {
                List<TtFaultCallDetailsPO> exampleList = faultDetailMap.get(callId);
                if (CollUtil.isNotEmpty(exampleList)) {
                    for (TtFaultCallDetailsPO example : exampleList) {
                        CallDetailsPO target = BeanUtil.copyProperties(example, CallDetailsPO.class);
                        target.setId(null);
                        callDetailsMapper.insert(target);
                    }
                }
            }
            SaCustomerNumberDTO example = callExampleMap.get(callId);
            example.setId(null);
            if (CollUtil.isNotEmpty(checkedInviteIdList)) {
                SaCustomerNumberPO target = null;
                for (Long inviteId : checkedInviteIdList) {
                    target = BeanUtil.copyProperties(example, SaCustomerNumberPO.class);
                    target.setInviteId(inviteId);
                    if (!existCheckCallList.contains("yy" + target.getCallId() + target.getInviteId())) {
                        saCustomerNumberMapper.insert(target);
                    }
                }
            }
            if (CollUtil.isNotEmpty(checkedInsuranceIdList)) {
                InviteInsuranceVehicleCustomerNumberPO target = null;
                for (Long inviteId : checkedInsuranceIdList) {
                    target = BeanUtil.copyProperties(example, InviteInsuranceVehicleCustomerNumberPO.class);
                    target.setInsuranceId(inviteId);
                    if (!existCheckCallList.contains("xb" + target.getCallId() + target.getInsuranceId())) {
                        inviteInsuranceVehicleCustomerNumberMapper.insert(target);
                    }
                }
            }
            if (CollUtil.isNotEmpty(checkedFaultIdList)) {
                TtFaultCallRegisterPO target = null;
                for (Long inviteId : checkedFaultIdList) {
                    target = BeanUtil.copyProperties(example, TtFaultCallRegisterPO.class);
                    target.setInviteId(inviteId);
                    if (!existCheckCallList.contains("gzd" + target.getCallId() + target.getInviteId())) {
                        faultCallRegisterMapper.insert(target);
                    }
                }
            }
        }
        return null;
    }




    private void updates(List<Long> updateList) {
        logger.info("updateRecordOrderStatusNew==========updateList.size:{}",updateList.size());
        int numPerTimes = 1000;
        if (updateList.size() <= numPerTimes) {
            logger.info("updateRecordOrderStatusNew批量修改流失数量少于1000条开始");
            batchUpdate(updateList); //此处插入少于1000条list
            logger.info("updateRecordOrderStatusNew批量修改流失数量少于1000条结束");
        } else {
            int maxIndex = updateList.size();
            int maxTimes = maxIndex / numPerTimes;
            maxTimes += (maxIndex % numPerTimes) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimes) {
                int fromIndex = numPerTimes * currentTimes;
                int toIndex = fromIndex + numPerTimes;
                toIndex = toIndex > maxIndex ? maxIndex : toIndex;
                logger.info("updateRecordOrderStatusNew批量修改流失从{}，到{}开始",fromIndex,toIndex);
                List<Long> subList = updateList.subList(fromIndex, toIndex);
                batchUpdate(subList);//此处循环插入500条list
                logger.info("updateRecordOrderStatusNew批量修改流失从{}，到{}结束",fromIndex,toIndex);
                currentTimes++;
            }
        }
    }

    private void batchUpdate(List<Long> updateList) {
        int size = inviteInsuranceVehicleRecordMapper.updateList(updateList);
        logger.info("==========inviteInsuranceVehicleRecordMapper.updateList:{}",size);
    }


    /**
     * c端查询续保线索
     */
    @Override
    public IPage<InviteInsuranceVehicleRecordDTO> getInviteInsuranceVehicleRecordCPort(Page page, InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO) {

        InviteInsuranceVehicleRecordPO inviteInsuranceVehicleRecordPO = inviteInsuranceVehicleRecordDTO.transDtoToPo(InviteInsuranceVehicleRecordPO.class);
        if (inviteInsuranceVehicleRecordDTO.getIsNoDistribute() != null && inviteInsuranceVehicleRecordDTO.getIsNoDistribute()) {
            inviteInsuranceVehicleRecordPO.setIsNoDistribute(10041001);
        }
        if (inviteInsuranceVehicleRecordDTO.getIsWaitDistribute() != null && inviteInsuranceVehicleRecordDTO.getIsNoDistribute()) {
            inviteInsuranceVehicleRecordPO.setIsWaitDistribute(10041001);
        }
        List<InviteInsuranceVehicleRecordPO> list = inviteInsuranceVehicleRecordMapper.selectInviteInsuranceVehicleRecordCPort(page,
                inviteInsuranceVehicleRecordPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteInsuranceVehicleRecordDTO> result = list.stream().map(m -> m.transPoToDto(InviteInsuranceVehicleRecordDTO.class)
            ).collect(Collectors.toList());
            page.setRecords(result);
            return page;
        }
    }



    /**
     * 新生成一条任务（24个月进厂）
     * @param dealerCode
     * @return
     */
    private void insertInsuranceTaskByRepairNo(InviteInsuranceVehicleRecordPO recordPO,
                                               String dealerCode,OwnerVehicleVO ownerVehicleVO2) {
        InviteInsuranceVehicleTaskDTO record = new InviteInsuranceVehicleTaskDTO();
        //续保规则
     //   InviteInsuranceRulePO insuranceRulePO = inviteInsuranceVehicleTaskMapper.getInvitationDlrRule(dealerCode, recordPO.getClueType(), 82041004);
        InviteInsuranceRulePO insuranceRulePO = inviteInsuranceVehicleTaskMapper.getInvitationDlrRule(dealerCode, recordPO.getClueType(), CommonConstants.INVITE_RULE_82041004);
        if (ownerVehicleVO2 != null) {
            record.setVin(ownerVehicleVO2.getVin());
            record.setLicensePlateNum(ownerVehicleVO2.getPlateNumber());
            record.setName(ownerVehicleVO2.getName());
            record.setTel(ownerVehicleVO2.getMobile());
            record.setItemType(CommonConstants.ITEM_TYPE_10041002);
          //  record.setItemType(10041002);
            record.setDataSources(5);
            record.setInsuranceType(dmscusRepairClient.selectInsuranceByVin(recordPO.getVin(),null).getInsuranceType()); //续保客户类型
            record.setInviteType(CommonConstants.INVITE_TYPE_82381003);
         //   record.setInviteType(82381003);
            record.setDayInAdvance(insuranceRulePO.getDayInAdvance());
            record.setRemindInterval(insuranceRulePO.getRemindInterval());
            record.setIsCreateInvite(0);
            record.setAdviseInDate(addOneYearDate(recordPO.getAdviseInDate())); //建议进厂日期 { +1 年}
            Date CreateInviteTime = dateUtil(record.getAdviseInDate(),insuranceRulePO);
            record.setCreateInviteTime(CreateInviteTime); //生成邀约时间
            //record.setFollowStatus(82401001);//未跟进
            record.setFollowStatus(CommonConstants.FOLLOW_STATUS_I);//未跟进
            record.setCloseInterval(insuranceRulePO.getCloseInterval());                               //超时关闭时间
            record.setOwnerCode(dealerCode);//经销商
            record.setDealerCode(dealerCode);
            record.setClueType(recordPO.getClueType());
            //对对象进行赋值操作
            InviteInsuranceVehicleTaskPO inviteInsuranceVehicleTaskPO = record.transDtoToPo(InviteInsuranceVehicleTaskPO.class);
            //执行插入
            inviteInsuranceVehicleTaskMapper.insert(inviteInsuranceVehicleTaskPO);
        }
    }
    /**
     * 新生成一条任务（24个月进厂）
     * @param dealerCode
     * @return
     */
    private void insertInsuranceTaskByRepairNoNew(InviteInsuranceVehicleRecordPO recordPO,
                                               String dealerCode,OwnerVehicleVO ownerVehicleVO2) {
        InviteInsuranceVehicleTaskDTO record = new InviteInsuranceVehicleTaskDTO();
        //续保规则
        //   InviteInsuranceRulePO insuranceRulePO = inviteInsuranceVehicleTaskMapper.getInvitationDlrRule(dealerCode, recordPO.getClueType(), 82041004);
        InviteInsuranceRulePO insuranceRulePO = inviteInsuranceVehicleTaskMapper.getInvitationDlrRule(dealerCode, recordPO.getClueType(), CommonConstants.INVITE_RULE_82041004);
            record.setVin(ownerVehicleVO2.getVin());
            record.setLicensePlateNum(ownerVehicleVO2.getPlateNumber());
            record.setName(ownerVehicleVO2.getName());
            record.setTel(ownerVehicleVO2.getMobile());
            record.setItemType(CommonConstants.ITEM_TYPE_10041002);
            //  record.setItemType(10041002);
            record.setDataSources(5);
            record.setInsuranceType(dmscusRepairClient.selectInsuranceByVin(recordPO.getVin(),null).getInsuranceType()); //续保客户类型
            record.setInviteType(CommonConstants.INVITE_TYPE_82381003);
            //   record.setInviteType(82381003);
            record.setDayInAdvance(insuranceRulePO.getDayInAdvance());
            record.setRemindInterval(insuranceRulePO.getRemindInterval());
            record.setIsCreateInvite(0);
            record.setAdviseInDate(addOneYearDate(recordPO.getAdviseInDate())); //建议进厂日期 { +1 年}
            Date CreateInviteTime = dateUtil(record.getAdviseInDate(),insuranceRulePO);
            record.setCreateInviteTime(CreateInviteTime); //生成邀约时间
            //record.setFollowStatus(82401001);//未跟进
            record.setFollowStatus(CommonConstants.FOLLOW_STATUS_I);//未跟进
            record.setCloseInterval(insuranceRulePO.getCloseInterval());                               //超时关闭时间
            record.setOwnerCode(dealerCode);//经销商
            record.setDealerCode(dealerCode);
            record.setClueType(recordPO.getClueType());
            //对对象进行赋值操作
            InviteInsuranceVehicleTaskPO inviteInsuranceVehicleTaskPO = record.transDtoToPo(InviteInsuranceVehicleTaskPO.class);
            //执行插入
            inviteInsuranceVehicleTaskMapper.insert(inviteInsuranceVehicleTaskPO);
    }
    private OwnerVehicleVO getMidOwnerVehicleVOByVin(String vin) {
        OwnerVehicleVO ownerVehicleVO = null;
        try {
            Page page = new Page(1, 1);
            Map map = new HashMap();
            map.put("vin", vin);
            IPage<OwnerVehicleVO> pageResult = iMiddleGroundVehicleService.queryVehicleInfo(page, map);
            if (pageResult != null && CollectionUtils.isNotEmpty(pageResult.getRecords())) {
                ownerVehicleVO = pageResult.getRecords().get(0);
            } else {
                ownerVehicleVO = null;
            }
        } catch (Exception e) {
            logger.error("call 根据[vin={}]中台车主车辆接口分页查询单条记录失败:{}", vin, e);
        }
        return ownerVehicleVO;
    }

    /**
     * 时间处理（24个月进厂）
     *
     * @return
     */
    private Date dateUtil(Date finishDate,InviteInsuranceRulePO insuranceRulePO){
        Calendar calendar1 = Calendar.getInstance(); //得到日历
        calendar1.setTime(finishDate);//把结束时间赋给日历
        if (null != insuranceRulePO && null != insuranceRulePO.getDayInAdvance()) {
            calendar1.add(Calendar.DATE, -insuranceRulePO.getDayInAdvance());  //根据规则提前跟进天数
        } else {
            calendar1.add(Calendar.DATE, -90);  //根据规则提前跟进天数
        }
        return calendar1.getTime();
    }



    /**
     * 根据vin查询 车辆24个月最近两次的进厂经销商
     *
     * @param vin
     * @param insuranceDate
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    public List<String> selectRepairOrderByVin2(String vin, Date insuranceDate){
        List<String> listStr = new ArrayList<String>();
        CheckRepairOrderDTO orderDTO = reportCommonClient.selectRepairOrderByVin2(vin,insuranceDate);
        if(null != orderDTO){
            logger.info("repair续保24个月进厂查询逻辑report返回值=========1：{}", orderDTO);
            for (RepairOrderVO dto : orderDTO.getOrderVOList()){
                listStr.add(dto.getOwnerCode());
            }
        }
        if(!CommonUtils.isNullOrEmpty(listStr) && listStr.size() > 0){
            return listStr.stream().distinct().collect(Collectors.toList());
        }else{
            return null;
        }

    }

    private Date addOneYearDate(Date insuranceDate){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(insuranceDate);
        calendar.add(Calendar.YEAR, +1);
        Date date = calendar.getTime();
        return date;
    }

}
