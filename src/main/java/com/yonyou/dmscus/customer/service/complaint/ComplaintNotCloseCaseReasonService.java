package com.yonyou.dmscus.customer.service.complaint;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonDTO;

import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ReasonsforFiveDto;


import java.util.List;

/**
 * <p>
 * 5日未结案原因 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface ComplaintNotCloseCaseReasonService  {
    /**
     * 分页查询
     * @param page
     * @param complaintNotCloseCaseReasonDTO
     * @return
     */
      IPage<ComplaintNotCloseCaseReasonDTO> selectPageBysql(Page page, ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO);

    /**
     * 集合查询
     * @param complaintNotCloseCaseReasonDTO
     * @return
     */
      List<ComplaintNotCloseCaseReasonDTO> selectListBySql(ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO);

    /**
     * 通过id查询
     * @param id
     * @return
     */
      ComplaintNotCloseCaseReasonDTO getById(Long id);

    /**
     * 新增
     * @param complaintNotCloseCaseReasonDTO
     * @return
     */
      int insert(ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO);

    /**
     * 更新
     * @param id
     * @param complaintNotCloseCaseReasonDTO
     * @return
     */
      int update(Long id, ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO);

    /**
     * 通过id删除
     * @param id
     * @return
     */
      int deleteById(Long id);

    /**
     * 新增
     * @param reasonsforFiveDto
     * @return
     */
    int insertReasonsforFive(ReasonsforFiveDto reasonsforFiveDto);

    /**
     * 分页查询
     * @param page
     * @param complaintNotCloseCaseReasonTestDTO
     * @return
     */
    IPage<ComplaintNotCloseCaseReasonTestDTO> selectPageBysql3(Page page, ComplaintNotCloseCaseReasonTestDTO complaintNotCloseCaseReasonTestDTO);
}
