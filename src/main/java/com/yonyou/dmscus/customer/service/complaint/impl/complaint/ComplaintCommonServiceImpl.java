package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.yonyou.dmscus.customer.dao.complaint.ComplaintInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintClassificationDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
@Component
public class ComplaintCommonServiceImpl {
    @Resource
    ComplaintInfoMapper complaintInfoMapper;
    /**
     * 客户要求
     */
    public  String getCusRequirement(String cusRequirementData){
        List<String> cusRequirementList = Arrays.asList(cusRequirementData.split(","));
        StringBuffer cusRequirement = new StringBuffer();
        for (int i = 0; i < cusRequirementList.size(); i++) {
            if (i == cusRequirementList.size() - 1) {
                switch (cusRequirementList.get(i)){
                    case "82021001":
                        cusRequirement.append("彻底修理");
                        break;
                    case "82021002":
                        cusRequirement.append("免费修理");
                        break;
                    case "82021003":
                        cusRequirement.append("优惠修理");
                        break;
                    case "82021004":
                        cusRequirement.append("退车");
                        break;
                    case "82021005":
                        cusRequirement.append("换车");
                        break;
                    case "82021006":
                        cusRequirement.append("提供代步车");
                        break;
                    case "82021007":
                        cusRequirement.append("补偿保养");
                        break;
                    case "82021008":
                        cusRequirement.append("代金券");
                        break;
                    case "82021009":
                        cusRequirement.append("现金");
                        break;
                    case "82021010":
                        cusRequirement.append("履行约定");
                        break;
                    case "82021011":
                        cusRequirement.append("道歉");
                        break;
                    case "82021012":
                        cusRequirement.append("其他赔偿");
                        break;
                    case "82021013":
                        cusRequirement.append("要求扩大维修");
                        break;
                    case "82021014":
                        cusRequirement.append("尽快提供配件");
                        break;
                    case "82021015":
                        cusRequirement.append("尽快提车（新车）");
                        break;
                    case "82021016":
                        cusRequirement.append("尽快提车（维修保养）");
                        break;
                    case "82021017":
                        cusRequirement.append("要求解释说明");
                        break;
                    case "82021018":
                        cusRequirement.append("其他要求");
                        break;
                }
            } else {
                switch (cusRequirementList.get(i)){
                    case "82021001":
                        cusRequirement.append("彻底修理"+",");
                        break;
                    case "82021002":
                        cusRequirement.append("免费修理"+",");
                        break;
                    case "82021003":
                        cusRequirement.append("优惠修理"+",");
                        break;
                    case "82021004":
                        cusRequirement.append("退车"+",");
                        break;
                    case "82021005":
                        cusRequirement.append("换车"+",");
                        break;
                    case "82021006":
                        cusRequirement.append("提供代步车"+",");
                        break;
                    case "82021007":
                        cusRequirement.append("补偿保养"+",");
                        break;
                    case "82021008":
                        cusRequirement.append("代金券"+",");
                        break;
                    case "82021009":
                        cusRequirement.append("现金"+",");
                        break;
                    case "82021010":
                        cusRequirement.append("履行约定"+",");
                        break;
                    case "82021011":
                        cusRequirement.append("道歉"+",");
                        break;
                    case "82021012":
                        cusRequirement.append("其他赔偿"+",");
                        break;
                    case "82021013":
                        cusRequirement.append("要求扩大维修"+",");
                        break;
                    case "82021014":
                        cusRequirement.append("尽快提供配件"+",");
                        break;
                    case "82021015":
                        cusRequirement.append("尽快提车（新车）"+",");
                        break;
                    case "82021016":
                        cusRequirement.append("尽快提车（维修保养）"+",");
                        break;
                    case "82021017":
                        cusRequirement.append("要求解释说明"+",");
                        break;
                    case "82021018":
                        cusRequirement.append("其他要求"+",");
                        break;
                }
            }
        }
        return  cusRequirement.toString();
    }

    /**
     * 赋值问题状态
     */
    public  String getProblemInf(String problemInfoData){
        String problemInfo="";

        switch (problemInfoData){
            case "82011001":
                problemInfo="噪音";
                break;
            case "82011002":
                problemInfo="机油消耗大";
                break;
            case "82011003":
                problemInfo="燃油消耗大";
                break;
            case "82011004":
                problemInfo="熄火";
                break;
            case "82011005":
                problemInfo="异响";
                break;
            case "82011006":
                problemInfo="捣缸";
                break;
            case "82011007":
                problemInfo="抖动";
                break;
            case "82011008":
                problemInfo="警告灯亮";
                break;
            case "82011009":
                problemInfo="动力不足";
                break;
            case "82011010":
                problemInfo="起火";
                break;
            case "82011011":
                problemInfo="无法启动";
                break;
            case "82011012":
                problemInfo="启动不良";
                break;
            case "82011013":
                problemInfo="漏油";
                break;
            case "82011014":
                problemInfo="其他";
                break;
        }

        return  problemInfo;
    }
    /**
     * 投诉来源状态赋值
     */
    public  String getSource(int sourceData){
        String source="";

        switch (sourceData){
            case 81971001:
                source="400热线";
                break;
            case 81971002:
                source="邮件投诉";
                break;
            case 81971003:
                source="电话投诉";
                break;
            case 81971004:
                source="微信调研";
                break;
            case 81971005:
                source="客服回访";
                break;
            case 81971006:
                source="来电投诉";
                break;
            case 81971007:
                source="官网投诉";
                break;
            case 81971008:
                source="售后CEM";
                break;
            case 81971009:
                source="媒体公关";
                break;
            case 81971010:
                source="媒体网站";
                break;
            case 81971011:
                source="沃世界平台";
                break;
            case 81971012:
                source="沃世界论坛";
                break;
            case 81971013:
                source="CVS";
                break;
            case 81971014:
                source="不满意案件";
                break;
            case 81971015:
                source="DPAC";
                break;
            case 81971016:
                source="经销商报备";
                break;
            case 81971017:
                source="CCM预警";
                break;
            case 81971018:
                source="经销商协助处理";
                break;
            case 81971019:
                source="工商质监";
                break;

        }

        return  source;
    }
    /**
     * 客诉单类别一级层赋值
     */
    public String getCategory(String Category1Data) {
        ComplaintClassificationDTO complaintClassificationDTO=new ComplaintClassificationDTO();
        complaintClassificationDTO.setId(Long.valueOf(Category1Data));
        List<ComplaintClassificationDTO> list=complaintInfoMapper.getCategory1(complaintClassificationDTO);
        String Category1= String.valueOf(list.get(0).getCateName());

        return Category1;
    }

    /**
     * 客诉单类别二级层赋值
     */
    public  String getCategory2(String category2Data){
        String category2="";

        switch (category2Data){
            case "82261001":
                category2="媒体公关售后案件";
                break;
            case "82261002":
                category2="媒体公关售前案件";
                break;
            case "82261003":
                category2="DPAC";
                break;
            case "82261004":
                category2="媒体网站";
                break;
            case "82261005":
                category2="售后案件-售后市场活动";
                break;
            case "82261006":
                category2="售后案件-车主俱乐部相关";
                break;
            case "82261007":
                category2="售后案件-附件";
                break;
            case "82261008":
                category2="售后案件-配件";
                break;
            case "82261009":
                category2="售后案件-保修";
                break;
            case "82261010":
                category2="售后案件-技术";
                break;
            case "82261011":
                category2="售后案件-区域管理";
                break;
            case "82261012":
                category2="沃世界";
                break;
            case "82261013":
                category2="售前案件";
                break;
            case "82261014":
                category2="IB投诉";
                break;
        }

        return  category2;
    }
    /**
     * 客诉单类别三级层赋值
     */
    public  String getCategory3(String category3Data){
        String category3="";

        switch (category3Data){
            case "82271001":
                category3="媒体来电反馈客户售后投诉";
                break;
            case "82271002":
                category3="媒体来电反馈客户销售投诉";
                break;
            case "82271003":
                category3="DPAC售后案件";
                break;
            case "82271004":
                category3="媒体网站售后案件";
                break;
            case "82271005":
                category3="售后案件-售后市场活动";
                break;
            case "82271006":
                category3="售后案件-车主俱乐部相关";
                break;
            case "82271007":
                category3="附件供应（到货时间不能满足客户需求）/价格/质量";
                break;
            case "82271008":
                category3="配件信息（件号/替代件号等问题）";
                break;
            case "82271009":
                category3="配件供应（到货时间不能满足客户需求）";
                break;
            case "82271010":
                category3="售后-技术-车身及内外饰";
                break;
            case "82271011":
                category3="售后-技术-转向系统";
                break;
            case "82271012":
                category3="售后-技术-传动系统";
                break;
            case "82271013":
                category3="售后-技术-动力总成";
                break;
            case "82271014":
                category3="售后-技术-底盘";
                break;
            case "82271015":
                category3="售后-技术-电器系统";
                break;
            case "82271016":
                category3="保修争议（客户在保内，经销商不给予保修，客户不认可不满）";
                break;
            case "82271017":
                category3="保修范围（客户已过保，要求保修）";
                break;
            case "82271018":
                category3="经销商管理其他";
                break;
            case "82271019":
                category3="经销商的服务收费";
                break;
            case "82271020":
                category3="经销商的服务态度";
                break;
            case "82271021":
                category3="沃世界平台售后案件";
                break;
            case "82271022":
                category3="沃世界售后案件";
                break;
            case "82271023":
                category3="直销车";
                break;
            case "82271024":
                category3="服务费用";
                break;
            case "82271025":
                category3="车辆资源";
                break;
            case "82271026":
                category3="沃世界微信绑定问题";
                break;
            case "82271027":
                category3="合格证问题（重大）";
                break;
            case "82271028":
                category3="欺诈销售（重大）";
                break;
            case "82271029":
                category3="合格证关单";
                break;
            case "82271030":
                category3="二手车";
                break;
            case "82271031":
                category3="大客户";
                break;
            case "82271032":
                category3="上牌问题";
                break;
            case "82271033":
                category3="大客户二手车";
                break;
            case "82271034":
                category3="展厅服务";
                break;
            case "82271035":
                category3="定（订）金问题";
                break;
            case "82271036":
                category3="其他问题";
                break;
            case "82271037":
                category3="订单优惠";
                break;
            case "82271038":
                category3="交车问题";
                break;
            case "82271039":
                category3="金融保险";
                break;
            case "82271040":
                category3="客服人员服务态度不佳";
                break;
            case "82271041":
                category3="客服人员专业知识不足/提供错误资讯";
                break;




        }

        return  category3;
    }



}
