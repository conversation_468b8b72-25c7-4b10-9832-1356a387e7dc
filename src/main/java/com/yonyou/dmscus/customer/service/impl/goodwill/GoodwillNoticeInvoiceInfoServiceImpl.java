package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillNoticeInvoiceInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillNoticeInvoiceInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillNoticeInvoiceInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillNoticeInvoiceInfoService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 亲善管理通知开票信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-17
 */
@Service
public class GoodwillNoticeInvoiceInfoServiceImpl
		extends ServiceImpl<GoodwillNoticeInvoiceInfoMapper, GoodwillNoticeInvoiceInfoPO>
		implements GoodwillNoticeInvoiceInfoService {
	// 日志对象
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillNoticeInvoiceInfoMapper goodwillNoticeInvoiceInfoMapper;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillNoticeInvoiceInfoDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillNoticeInvoiceInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillNoticeInvoiceInfoDTO> selectPageBysql(Page page,
			GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO) {
		if (goodwillNoticeInvoiceInfoDTO == null) {
			goodwillNoticeInvoiceInfoDTO = new GoodwillNoticeInvoiceInfoDTO();
		}
		GoodwillNoticeInvoiceInfoPO goodwillNoticeInvoiceInfoPO = goodwillNoticeInvoiceInfoDTO
				.transDtoToPo(GoodwillNoticeInvoiceInfoPO.class);

		List<GoodwillNoticeInvoiceInfoPO> list = goodwillNoticeInvoiceInfoMapper.selectPageBySql(page,
				goodwillNoticeInvoiceInfoPO);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillNoticeInvoiceInfoDTO> result = list.stream()
					.map(m -> m.transPoToDto(GoodwillNoticeInvoiceInfoDTO.class)).collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillNoticeInvoiceInfoDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillNoticeInvoiceInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillNoticeInvoiceInfoDTO> selectListBySql(
			GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO) {
		if (goodwillNoticeInvoiceInfoDTO == null) {
			goodwillNoticeInvoiceInfoDTO = new GoodwillNoticeInvoiceInfoDTO();
		}
		GoodwillNoticeInvoiceInfoPO goodwillNoticeInvoiceInfoPO = goodwillNoticeInvoiceInfoDTO
				.transDtoToPo(GoodwillNoticeInvoiceInfoPO.class);
		List<GoodwillNoticeInvoiceInfoPO> list = goodwillNoticeInvoiceInfoMapper
				.selectListBySql(goodwillNoticeInvoiceInfoPO);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillNoticeInvoiceInfoDTO.class))
					.collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillNoticeInvoiceInfoDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillNoticeInvoiceInfoDTO getById(Long id) {
		GoodwillNoticeInvoiceInfoPO goodwillNoticeInvoiceInfoPO = goodwillNoticeInvoiceInfoMapper.selectById(id);
		if (goodwillNoticeInvoiceInfoPO != null) {
			return goodwillNoticeInvoiceInfoPO.transPoToDto(GoodwillNoticeInvoiceInfoDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillNoticeInvoiceInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO) {
		// 对对象进行赋值操作
		GoodwillNoticeInvoiceInfoPO goodwillNoticeInvoiceInfoPO = goodwillNoticeInvoiceInfoDTO
				.transDtoToPo(GoodwillNoticeInvoiceInfoPO.class);
		// 执行插入
		int row = goodwillNoticeInvoiceInfoMapper.insert(goodwillNoticeInvoiceInfoPO);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillNoticeInvoiceInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO) {
		GoodwillNoticeInvoiceInfoPO goodwillNoticeInvoiceInfoPO = goodwillNoticeInvoiceInfoMapper.selectById(id);
		// 对对象进行赋值操作
		goodwillNoticeInvoiceInfoDTO.transDtoToPo(goodwillNoticeInvoiceInfoPO);
		// 执行更新
		int row = goodwillNoticeInvoiceInfoMapper.updateById(goodwillNoticeInvoiceInfoPO);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillNoticeInvoiceInfoMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillNoticeInvoiceInfoMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

}
