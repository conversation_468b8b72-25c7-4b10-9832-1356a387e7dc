package com.yonyou.dmscus.customer.service.complaint;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCcmClassificationDTO;

import java.util.List;

/**
 * <p>
 * 客诉CCM分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
public interface ComplaintCcmClassificationService {
    IPage<ComplaintCcmClassificationDTO> selectPageBysql(Page page, ComplaintCcmClassificationDTO complaintCcmClassificationDTO);
    List<ComplaintCcmClassificationDTO> selectListBySql(ComplaintCcmClassificationDTO complaintCcmClassificationDTO);
    ComplaintCcmClassificationDTO getById(Long id);
    int insert(ComplaintCcmClassificationDTO complaintCcmClassificationDTO);
    int update(Long id, ComplaintCcmClassificationDTO complaintCcmClassificationDTO);
    int deleteById(Long id);

}
