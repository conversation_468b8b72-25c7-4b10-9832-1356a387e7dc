package com.yonyou.dmscus.customer.service.invitationFollow;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dto.BookingCreateParamsVo;
import com.yonyou.dmscus.customer.dto.CustomerInfoListReturnDTO;
import com.yonyou.dmscus.customer.dto.cdp.CdpTagTaskParameterDto;
import com.yonyou.dmscus.customer.entity.dto.clue.ContactInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.InviteClueParamDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.InviteClueResultDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.LiteCrmClueDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.LiteCrmClueResultDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.RecommendationDTO;
import com.yonyou.dmscus.customer.entity.dto.saSllocateDlr.SaSllocateDlrDto;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO;
import com.yonyou.dmscus.customer.entity.vo.InviteVehicleRecordVo;
import com.yonyou.dmscus.customer.service.common.IBaseService;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 车辆邀约记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
public interface InviteVehicleRecordService extends IBaseService<InviteVehicleRecordDTO> {

    Long addInviteVehicleRecord (InviteVehicleRecordDTO inviteVehicleRecordDTO);

    IPage<InviteVehicleRecordDTO> getInviteVehicleRecord(Page page, InviteVehicleRecordDTO inviteVehicleRecordDTO);

    void updateIsAi();


    IPage<InviteVehicleRecordDTO> getInviteVehicleRecordForSaSllocate(Page<InviteVehicleRecordPO> page, InviteVehicleRecordDTO
            inviteVehicleRecordDTO);

    int saveSaSllocate(SaSllocateDlrDto saSllocateDlrDto);

    List<RecommendationDTO> getRecommendation(String vin);

    List<InviteVehicleRecordDTO> getInviteVehicleRecordInfo(String vin, Long id);

    //增加易损件提醒查询
    List<InviteVehicleRecordDTO> getInviteVehicleVulInfo(String vin);

    int saveInviteVehicleRecord(InviteVehicleRecordDetailDTO dto);

    int checkAI(InviteVehicleRecordDetailDTO dto);

   String  saveBookingRecord(BookingCreateParamsVo vo);
    /**
     * 导出查询
     * @param inviteVehicleRecordDTO
     * @return
     */
    List<Map> exportExcelinviteVehicleRecord(InviteVehicleRecordDTO inviteVehicleRecordDTO);
    //保险跟进-邀约记录（----chensh）
    IPage<InviteVehicleRecordDTO> selectFollowInsureRecord(Page page,String followChoiced, InviteVehicleRecordDTO inviteVehicleRecordDTO);

    int saveFollowInsureRecord(InviteVehicleRecordDetailDTO dto);

    List<Map> exportExcelFollowInsure(String followChoiced,InviteVehicleRecordDTO inviteVehicleRecordDTO);

    //保险跟进失败 -- 计划任务
    void updateInsureFollowStatus();

    List<InviteVehicleTaskPO> getWaitCloseRecord(String createDate);

    /**查询需要关闭的线索(支持补前1个月的数据)*/
    List<InviteVehicleRecordPO> findCluesToClose(String startDate,String endDate);
    /**批量关闭线索*/
    void closeCluesBulk(List<Long> list);
    /**
     * 邀约关闭首定保查询
     * @param vin
     * @return
     */
    List<InviteVehicleRecordPO> getWaitCloseMaintainRecord(String vin);

    List<InviteVehicleRecordPO> getWaitCloseVocAccidentRecord(String createDate, String vin);

    List<InviteVehicleRecordPO> getWaitCloseGuaranteeRecord(String createDate);

    List<InviteVehicleRecordPO> getWaitCloseGuaranteeRecordByVin(String vin);

    List<InviteVehicleRecordPO> getWaitCloseVulnerableRecord(String vin, String code,Integer type);

    int updateInviteByDealerCode(String vin,String dealerCode, String lastDealerCode);

    Integer getNeedDistribute(List<Integer> leaveIds);

    void saveBookingNoForRecord(String bookingNo, Long inviteId,String ownerCode,String vin);

    List<InviteVehicleRecordPO> getWaitCloseLostRecord(String dealerCode, String vin);

    List<InviteVehicleRecordPO> getWaitCloseAlertRecord(String dealerCode, String vin);
    void updateOneIdByMobile(CustomerInfoListReturnDTO dto);

    IPage<InviteVehicleRecordDTO> getInviteVehicleRecordCPort(Page page, InviteVehicleRecordDTO inviteVehicleRecordDTO);

    List<InviteVehicleRecordPO> getWaitCloseRecordByVin(String vin);

    List<InviteVehicleRecordPO> selectByVin(String vin,boolean flag);

    List<InviteVehicleRecordPO>  selectTypeXIIByVin(String vin);

    int updateList(List<Long> upsr6);

    List<InviteVehicleRecordPO> selectListByVin(String vin);

    int  updateAlertVocByVin(String vin);

    List<InviteVehicleRecordPO> selectVocLossByVin(String vin);

    List<InviteVehicleRecordPO> selectVocByVin(String vin);

    List<InviteVehicleRecordPO> selectTowListByVinAndCode(String vin, String code);

    List<InviteVehicleRecordPO> selectLossByVin(String vin);

    //---CDP接入邀约线索,首保,定保,流失预警,流失客户---
    /**crm下发线索到nb*/
    LiteCrmClueResultDTO addClueByCrm(LiteCrmClueDTO dto);

    /**查询未完成的线索: 首保,定保,流失预警*/
    List<InviteVehicleRecordPO> getOpenLeads(String vin, String dealerCode, List<Integer> inviteList);
    List<InviteVehicleRecordPO> getOpenLeads(List<String> vinList, List<Integer> inviteList, String createDate);
    /**通过ID关闭线索*/
    void closeLeadById(List<Long> ids, Integer orderStatus);

    /**通过工单判断线索是否完成*/
    void querySettledMaintenanceByTime(String createDate);
    void addTransactionRecord(List<InviteVehicleRecordPO> listUpPo);
   void completeBatchLeads(List<InviteVehicleRecordPO> list);

  /**逾期线索关闭*/
    void closeOverdueLeads(String createDate);
    /**查询客户信息*/
    List<ContactInfoDTO> queryContactInfo(String dealerCode, String vin);
    /**通过保养灯信息判断线索验证状态*/
    void isValidationSuccessful();
    /**获取已过期的待验证线索*/
    List<InviteVehicleRecordPO> getExpiredPendingLeads();
    /**邀约线索查询通用接口*/
    List<InviteClueResultDTO> selectInviteClue(Page<InviteClueResultDTO> page,InviteClueParamDTO dto);
    /**邀约线索查询通用接口*/
    InviteClueResultDTO selectNewInviteClue(String vin, Integer leadsType, String dealerCode);
    /**补充扩展表数据*/
    void batchInsertExtendedData();
    /**通过vin查询未完成的线索数量*/
    List<InviteVehicleRecordPO> selectClueByVinAndInviteType(String vin, boolean flag);
    /**白名单逻辑*/
    List<String> doWhitelistDetermination(List<String> dealerCodes, String vin, Integer inviteType, boolean flag);
    /**查询白名单*/
    List<String> getWhiteList(Integer modType);
    /**查询多个白名单*/
    List<String> getWhiteLists(Integer... modType);
    /**添加白名单*/
    void addWhiteList(String ownerCodes, Integer modType, Integer rosterType);
    /**查询白名单线索*/
    public List<InviteVehicleRecordPO> searchClues(List<String> list);
    /**每月25号拉取CDP更新返厂意向登记*/
    void cdpUpdateReturnIntentionLevel();

    void updateAdviseInDate(List<CdpTagTaskParameterDto> list);

    String queryBookingNo(BookingCreateParamsVo vo);
    /**查询最新的一条线索*/
    InviteVehicleRecordPO fetchLatestAppointmentLead(List<Integer> typeList, List<Integer> statusList, String vin, String dealerCode);

    void completeLeadByWorkOrder(String createDate);

    /**
     * 查询线索记录根据vin或者经销商
     * @param ownerCode 经销商
     * @param vin vin
     * @param inviteType 线索类型
     * @return 参数对象
     */
    InviteVehicleRecordVo queryInviteVehicleByVin(String ownerCode, String vin, Integer inviteType);

    void doReturnIntentionLevel(List<String> vinList);

    /**通过vin删除黑名单中待下发的任务*/
    int updateIsCreateInviteByVin(String vin);
    /**通过vin修改线索的经销商(黑名单隐藏线索用)*/
    int updateDealerCodeByVin(String vin);

}
