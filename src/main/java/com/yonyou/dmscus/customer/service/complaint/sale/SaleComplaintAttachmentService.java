package com.yonyou.dmscus.customer.service.complaint.sale;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintAttachmentDTO;

import java.util.List;

/**
 * <p>
 * 销售客户投诉附件 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-11
 */
public interface SaleComplaintAttachmentService {
    IPage<SaleComplaintAttachmentDTO> selectPageBysql(Page page, SaleComplaintAttachmentDTO saleComplaintAttachmentDTO);
    List<SaleComplaintAttachmentDTO> selectListBySql(SaleComplaintAttachmentDTO saleComplaintAttachmentDTO);
    SaleComplaintAttachmentDTO getById(Long id);
    int insert(SaleComplaintAttachmentDTO saleComplaintAttachmentDTO);
    int update(Long id, SaleComplaintAttachmentDTO saleComplaintAttachmentDTO);
    int deleteById(Long id);
    int deleteBatchIds(String ids);

    List<ComplaintAttachmentTestDTO> selectListBySql1(SaleComplaintAttachmentDTO saleComplaintAttachmentDTO);
}
