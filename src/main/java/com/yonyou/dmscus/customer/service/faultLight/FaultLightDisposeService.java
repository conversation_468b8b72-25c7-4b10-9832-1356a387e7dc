package com.yonyou.dmscus.customer.service.faultLight;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.faultLight.BatchUpdateDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.infoInherit.FaultLightInheritDTO;
import com.yonyou.dmscus.customer.feign.dto.AllModeDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface FaultLightDisposeService {

    /**
     * 分页查询--获取故障类别集合
     */
    IPage<FaultLightInheritDTO> selectPageTypeInfo(Page<FaultLightInheritDTO> page, FaultLightDTO dto);
    /**
     * 获取故障类别---对外暴露接口
     */
    FaultLightDTO getTypeInfo(FaultLightDTO dto);
    /**
     * 批量添加类别
     */
    int addTypeInfo(List<FaultLightDTO> dto);
    /**
     * 批量修改类别
     */
    int batchUpdate(BatchUpdateDTO batchUpdate);
    /**
     * 获取累计几次集合
     */
    List<String> cumulativeNumber();

    /**
     * 查询责任验证状态
     */
    boolean selectDutyStatusByIds(List<Long> list);

    /**
     * 导入类别
     */
    void importDispose(MultipartFile file);
}
