package com.yonyou.dmscus.customer.service.goodwill;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillDealerLinesMaintainDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.companySelectDTO;

/**
 * <p>
 * 经销商亲善额度维护 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
public interface GoodwillDealerLinesMaintainService {
	public IPage<GoodwillDealerLinesMaintainDTO> selectPageBysql(Page page,
			GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO);

	public List<GoodwillDealerLinesMaintainDTO> selectListBySql(
			GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO);

	public GoodwillDealerLinesMaintainDTO getById(Long id);

	public int insert(GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO);

	public int update(Long id, GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO);

	public int deleteById(Long id);

	public int deleteBatchIds(String ids);

	void deleteDealerLinesById(Long id) throws ServiceBizException;

	// 查询经销商信息
	public IPage<Map> getDealerInfo(Page page, companySelectDTO dto);

	public int addDealerLinesInfo(GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO);

	// 经销商亲善额度导出查询
	public List<Map> selectExportListBySql(Map<String, String> queryParam);
}
