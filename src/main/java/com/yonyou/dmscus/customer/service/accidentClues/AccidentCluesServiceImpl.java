package com.yonyou.dmscus.customer.service.accidentClues;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.domain.framework.RestResultResponse;
import com.yonyou.dmscloud.framework.domains.dto.basedata.ResponseDTO;
import com.yonyou.dmscloud.framework.service.excel.ExcelExportColumn;
import com.yonyou.dmscloud.framework.service.excel.ExcelRead;
import com.yonyou.dmscloud.framework.service.excel.ExcelReadCallBack;
import com.yonyou.dmscloud.framework.service.excel.impl.AbstractExcelReadCallBack;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscloud.function.utils.common.StringUtils;
import com.yonyou.dmscus.customer.configuration.InnerUrlProperties;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.configuration.SalesUrlProperties;
import com.yonyou.dmscus.customer.constants.AccidentCluesConstants;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.constants.clue.ACDashBoardQueryEnum;
import com.yonyou.dmscus.customer.constants.clue.AccidentClueAppPushEnum;
import com.yonyou.dmscus.customer.dao.accidentClues.*;
import com.yonyou.dmscus.customer.dao.voicemanage.CallDetailsMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.SaWorkNumberMapper;
import com.yonyou.dmscus.customer.dto.*;
import com.yonyou.dmscus.customer.enevt.AccidentClueInfoPushEvent;
import com.yonyou.dmscus.customer.enevt.AccidentClueStatusChangeEvent;
import com.yonyou.dmscus.customer.entity.VehicleQueryParamsDTO;
import com.yonyou.dmscus.customer.entity.dto.GetDealerUserDataDTO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.*;
import com.yonyou.dmscus.customer.entity.dto.clue.ContactInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.middleground.ModelVO;
import com.yonyou.dmscus.customer.entity.po.accidentClues.*;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaWorkNumberPO;
import com.yonyou.dmscus.customer.entity.vo.*;
import com.yonyou.dmscus.customer.entity.vo.accidentClues.AccidentClueFollowVo;
import com.yonyou.dmscus.customer.enums.AccidentCluesInsuranceSourceEnum;
import com.yonyou.dmscus.customer.enums.RangeEnum;
import com.yonyou.dmscus.customer.feign.CustomerRepairCommonClient;
import com.yonyou.dmscus.customer.feign.DmscloudServiceClient;
import com.yonyou.dmscus.customer.feign.MidEndVehicleCenterFeign;
import com.yonyou.dmscus.customer.feign.MidUserOrganizationClient;
import com.yonyou.dmscus.customer.feign.RepairCommonClient;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.feign.dto.CommonConfigDTO;
import com.yonyou.dmscus.customer.feign.dto.VehicleDto;
import com.yonyou.dmscus.customer.feign.dto.VehiclePageQueryDetailDto;
import com.yonyou.dmscus.customer.feign.dto.VehiclePageQueryDto;
import com.yonyou.dmscus.customer.feign.vo.PadVehiclePreviewResultVo;
import com.yonyou.dmscus.customer.feign.vo.PadVehiclePreviewVo;
import com.yonyou.dmscus.customer.middleInterface.CompanyDetailByCodeDTO;
import com.yonyou.dmscus.customer.ossexcel.DownloadDTO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.complaint.impl.complaint.ComplaintDealerCcmRefServiceImpl;
import com.yonyou.dmscus.customer.service.impl.voicemanage.WorkNumberServiceContext;
import com.yonyou.dmscus.customer.service.middleground.BasicdataCenterService;
import com.yonyou.dmscus.customer.service.parse.ParseService;
import com.yonyou.dmscus.customer.service.range.RangeService;
import com.yonyou.dmscus.customer.util.SensitiveUtils;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import com.yonyou.dmscus.customer.utils.ai.DccHttpHelper;
import com.yonyou.dmscus.customer.utils.ai.DccResponseUtil;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.yonyou.dmscus.customer.constants.AccidentCluesConstants.ACCIDENT_CLUES;
import static com.yonyou.dmscus.customer.constants.AccidentCluesConstants.FACTORY_BIZ_MANAGE;

/**
 * 事故线索
 *
 * <AUTHOR>
 * @date 2020/11/16 0016
 */
@Service
public class AccidentCluesServiceImpl implements AccidentCluesService {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    AccidentCluesMapper accidentCluesMapper;

    @Resource
    AccidentCluesExtMapper accidentCluesExtMapper;

    @Resource
    AccidentCluesExt2Mapper accidentCluesExt2Mapper;

    @Resource
    AccidentCluesFollowMapper accidentCluesFollowMapper;

    @Resource
    AccidentCluesAllotMapper accidentCluesAllotMapper;

    @Resource
    AccidentCluesSaNumberMapper accidentCluesSaNumberMapper;

    @Resource
    SaWorkNumberMapper saWorkNumberMapper;

    @Resource
    CallDetailsMapper callDetailsMapper;

    @Value("${ai.telecom.common.url_mapping_register}")
    String URL_MAPPING_REGISTER;  //呼叫登记

    @Resource
    AccidentCluesImportMapper accidentCluesImportMapper;

    @Resource
    ExcelRead<AccidentCluesImportDTO> excelReadServiceIs;

    @Resource
    BasicdataCenterService basicdataCenterService;

    @Resource
    ComplaintDealerCcmRefServiceImpl complaintDealerCcmRefService;

    @Resource
    CustomerRepairCommonClient customerRepairCommonClient;

    @Resource
    BusinessPlatformService businessPlatformService;

    @Resource
    ReportCommonClient reportCommonClient;

    @Resource
    private RestTemplate directRestTemplate;

    @Autowired
    private SalesUrlProperties salesUrlProperties;

    @Autowired
    private InnerUrlProperties innerUrlProperties;

    @Resource
    private MidUrlProperties midUrlProperties;

    @Resource
    private AccidentCluesContactsMapper accidentCluesContactsMapper;

    String msg = "success";

    @Autowired
    private RangeService rangeService;

    @Autowired
    private ParseService parseService;

    @Resource
    private AppPushService appPushService;

    @Autowired
    WorkNumberServiceContext workNumberServiceContext;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private RepairCommonClient repairCommonClient;

    @Resource
    private WhitelistCheckService whitelistCheckService;

    @Autowired
    private DmscloudServiceClient dmscloudServiceClient;

    @Resource
    private MidEndVehicleCenterFeign midEndVehicleCenterFeign;
    @Resource
    private MidUserOrganizationClient orgClient;

    @Value("${accidentClue.app.menuId:}")
    private Integer menuId;
    @Value("${accidentClue.expireDay:14}")
    private Integer expireDay;
    @Value("${accidentClue.limitDate:}")
    private String limitDate;

    private static final String INIT_WOW_MOM_VALUE = "0%";

    /**
     * 保存线索
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insert(AccidentCluesDTO dto) {
        logger.info("事故线索保存入参:{}",JSON.toJSONString(dto));
        String ownerCode = FrameworkUtil.getLoginInfo().getOwnerCode();


        //校验车
        this.checkVinMust(dto.getVin(),dto.getLicense(),false);

        //查询 VIN
        PadVehiclePreviewResultVo vehicle = dmscloudServiceClient.queryOwnerVehicleInterf(
            new PadVehiclePreviewVo().setLicense(dto.getLicense()));
        if (Objects.isNull(vehicle)){
            throw new ServiceBizException("车牌号不存在");
        }
        dto.setVin(vehicle.getVin());
        //校验重复线索
        this.checkVinCluesRepetition(dto.getVin());

        //获取经销商所属大、小区
        CompanyDetailByCodeDTO companyInfo = complaintDealerCcmRefService.getCompanyInfo(ownerCode);
        // dto.setCluesStatus(CommonConstants.CLUES_STATUS_UNFINISHED);
        dto.setFollowStatus(CommonConstants.FOLLOW_STATUS_WGJ);
        dto.setDealerCode(ownerCode);
        dto.setAfterBigAreaId(companyInfo.getAfterBigAreaId());
        dto.setAfterBigAreaName(companyInfo.getAfterBigAreaName());
        dto.setAfterSmallAreaId(companyInfo.getAfterSmallAreaId());
        dto.setAfterSmallAreaName(companyInfo.getAfterSmallAreaName());
        dto.setDealerName(companyInfo.getCompanyNameCn());
        AccidentCluesPO po = dto.transDtoToPo(AccidentCluesPO.class);
        po.setCreatedAt(new Date());
        po.setRecordVersion(1);
        //获取oneId
        logger.info("获取oneid");
        List<CustomerInfoCenterDTO> param = new ArrayList<>();
        CustomerInfoCenterDTO customerInfoCenterDto=new CustomerInfoCenterDTO();
        customerInfoCenterDto.setName(dto.getContacts());
        customerInfoCenterDto.setMobile(dto.getContactsPhone());
        param.add(customerInfoCenterDto);
        logger.info("入参:{}", JSON.toJSONString(param));
        List<CustomerInfoListReturnDTO> oneIdList = businessPlatformService.getOneId(param);
        logger.info("返回:{}", JSON.toJSONString(oneIdList));
        if(!CommonUtils.isNullOrEmpty(oneIdList) && oneIdList.size()>0) {
        	po.setOneId(Integer.parseInt(oneIdList.get(0).getId()+""));
        }
        int row = accidentCluesMapper.insert(po);
        //初始化附加表
        this.initExtInfo(po.getAcId(),po.getVin(),po.getLicense(), dto.getCluesResource());
        dto.setAcId(po.getAcId());
        dto.setContactList(Collections.singletonList(new AccidentClueContact(dto.getContacts(), dto.getContactsPhone(), CommonConstants.DICT_IS_NO)));
        applicationEventPublisher.publishEvent(new AccidentClueInfoPushEvent(Collections.singletonList(dto)));
        // Annotate in the new version of accident clues on 2024-05-17 delete App push
        return row;
    }

    /**
     * 分页查询
     */
    @Override
    public IPage<AccidentCluesPO> selectPageBysql(Page page, AccidentCluesDTO dto) {
        logger.info("事故线索厂端查询:{}", JSON.toJSONString(dto));
        LoginInfoDto loginInfo = FrameworkUtil.getLoginInfo();
        CommonConfigDTO configByKey = dmscloudServiceClient.getConfigByKey(FACTORY_BIZ_MANAGE, ACCIDENT_CLUES);
        logger.info("selectPageBysql loginInfo:{}", JSON.toJSONString(loginInfo));
        logger.info("selectPageBysql configByKey:{}", JSON.toJSONString(configByKey));
        if(Objects.nonNull(configByKey)){
            String configValue = configByKey.getConfigValue();
            String[] split = configValue.split(",");
            List<String> factoryBizManage = Arrays.asList(split);
            if(!factoryBizManage.contains(String.valueOf(loginInfo.getUserId()))){
                dto.setDataStatus(1);
            }else{
                dto.setDataStatus(0);
            }
        }
        if(!"vcdc".equals(dto.getSource())) {
    		dto.setDealerCode(loginInfo.getOwnerCode());
    	}
        if(!StringUtils.isNullOrEmpty(dto.getReportDateEnd())){
            dto.setReportDateEnd(DateUtils.getAfterDate(dto.getReportDateEnd(),1));
        }
        if (!StringUtils.isNullOrEmpty(dto.getNextFollowDateEnd())) {
            dto.setNextFollowDateEnd(DateUtils.getAfterDate(dto.getNextFollowDateEnd(), 1));
        }
        if (Objects.nonNull(dto.getCreatedDateEnd())) {
            dto.setCreatedDateEnd(DateUtils.getAfterDate(dto.getCreatedDateEnd(), 1));
        }
        if(!StringUtils.isNullOrEmpty(dto.getIsself()) && "10371001".equals(dto.getIsself())) {
        	dto.setFollowPeople(Integer.parseInt(loginInfo.getUserId()+""));
        }
        List<AccidentCluesPO> list= accidentCluesMapper.selectPageSql(page,dto.transDtoToPo(AccidentCluesPO.class),dto);
        if(CommonUtils.isNullOrEmpty(list)){
            page.setRecords(new ArrayList<>());
        } else {
            page.setRecords(list);
        }
        return page;
    }

    /**
     * 线索跟进
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertCluesFollow(AccidentCluesFollowDTO followDto) {
        logger.info("线索跟进:{}", JSON.toJSONString(followDto));
        LoginInfoDto loginInfo = FrameworkUtil.getLoginInfo();
        logger.info("insertCluesFollow loginInfo:{}",loginInfo);
        String ownerCode = loginInfo.getOwnerCode();
        AccidentCluesFollowPO followPo=followDto.transDtoToPo(AccidentCluesFollowPO.class);
        AccidentCluesPO accidentCluesPO= accidentCluesMapper.selectById(followPo.getAcId());
        if (ObjectUtils.isEmpty(accidentCluesPO)){
            throw new ServiceBizException("跟进线索不存在！");
        }
        //跟进失败原因如选择'其它'，跟进反馈变为必填
        if (Objects.equals(followPo.getFollowFailWhy(),AccidentCluesConstants.ACCIDENT_CLUES_FOLLOW_FAIL_WHY_RESTS) && StringUtils.isNullOrEmpty(followPo.getFollowText())){
            throw new ServiceBizException("跟进反馈不能为空");
        }
        Integer followStatus = accidentCluesPO.getFollowStatus();


        //填充跟进信息，主表冗余字段
        this.fillFollowInfo(followPo, followStatus);


        followDto.setDealerCode(ownerCode);
        AccidentCluesExtPo ext = accidentCluesExtMapper.selectOne(
                new LambdaQueryWrapper<AccidentCluesExtPo>().eq(AccidentCluesExtPo::getAcId, followPo.getAcId())
                        .eq(AccidentCluesExtPo::getIsDeleted, 0)
                        .last("limit 1"));
        //保存跟进记录
        /*跟进记录被兄弟线索同步*/
        this.saveFollowInfo(followPo, loginInfo);
        // 修改或新增
        this.saveContactsInformation(accidentCluesPO, ext, followDto);
        //更新主单数据
//        accidentCluesPO.setIntoDealerCode(followDto.getIntoDealerCode()); //进厂经销商
        accidentCluesPO.setIntoDealerDate(followDto.getIntoDealerDate()); //进厂时间
        accidentCluesPO.setIsInsured(followDto.getIsInsured()); //是否本店承保
        accidentCluesPO.setDoubleAccident(followDto.getDoubleAccident()); //单方、双方事故
        accidentCluesPO.setFollowStatus(followDto.getFollowStatus()); //跟进状态
        //如果工单状态是在修 或者 已提交结算，线索状态为已进厂
        if (CommonConstants.DICT_RO_STATUS_TYPE_ON_REPAIR.equals(followDto.getRoStatus() + "") ||
                CommonConstants.DICT_RO_STATUS_TYPE_FOR_BALANCE.equals(followDto.getRoStatus() + "")) {
            accidentCluesPO.setCluesStatus(CommonConstants.CLUES_STATUS_INTO);
            //工单状态是已结算，线索状态为 已完成
        } else if (CommonConstants.DICT_RO_STATUS_TYPE_BALANCED.equals(followDto.getRoStatus() + "")) {
            accidentCluesPO.setCluesStatus(CommonConstants.CLUES_STATUS_FINISHED);
        }
//        accidentCluesPO.setIntoDealerCode(followDto.getIntoDealerCode());
//        accidentCluesPO.setIntoDealerDate(followDto.getIntoDealerDate());
//        accidentCluesPO.setIntoRoNo(followDto.getRoNo());
        accidentCluesPO.setNextFollowDate(followDto.getNextFollowDate());
        accidentCluesPO.setIsAppointment(followDto.getIsAppointment());
        accidentCluesPO.setFollowCount((StringUtils.isNullOrEmpty(accidentCluesPO.getFollowCount()) ? 0 : accidentCluesPO.getFollowCount()) + 1);
        accidentCluesPO.setIsBruise(followDto.getIsBruise());
        //预约单号
        accidentCluesPO.setBookingOrderNo(followDto.getBookingOrderNo());
        //修改所有线索组的预约单号
        LambdaQueryWrapper<AccidentCluesExtPo> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(AccidentCluesExtPo::getAcId,accidentCluesPO.getAcId());
        queryWrapper.eq(AccidentCluesExtPo::getIsDeleted,0);
        queryWrapper.last("limit 1");
        AccidentCluesExtPo accidentCluesExtPo = accidentCluesExtMapper.selectOne(queryWrapper);
        logger.info("accidentCluesExtPo :{}",accidentCluesExtPo);
        if (Objects.nonNull(accidentCluesExtPo) && !StringUtils.isNullOrEmpty(accidentCluesExtPo.getRepeatSonLeadId())){
            String[] crmIds = accidentCluesExtPo.getRepeatSonLeadId().split(",");
            logger.info("crmIds:{}",crmIds);
            LambdaQueryWrapper<AccidentCluesExtPo> accidentCluesExtQueryWrapper=new LambdaQueryWrapper<>();
            accidentCluesExtQueryWrapper.in(AccidentCluesExtPo::getCrmId,Arrays.asList(crmIds));
            accidentCluesExtQueryWrapper.eq(AccidentCluesExtPo::getIsDeleted,0);
            List<AccidentCluesExtPo> accidentCluesExtPos = accidentCluesExtMapper.selectList(accidentCluesExtQueryWrapper);
            if (!CollectionUtils.isEmpty(accidentCluesExtPos)){
                List<Integer> acidList=accidentCluesExtPos.stream().map(AccidentCluesExtPo::getAcId).collect(Collectors.toList());
                logger.info("acidList:{}",acidList);
                //更新主表
                logger.info("accidentCluesExtQueryWrapper 更新主表");
                LambdaUpdateWrapper<AccidentCluesPO> accidentCluesUpdateWrapper = new LambdaUpdateWrapper<>();
                accidentCluesUpdateWrapper.set(AccidentCluesPO::getBookingOrderNo, followDto.getBookingOrderNo());
                accidentCluesUpdateWrapper.set(AccidentCluesPO::getFollowStatus, followDto.getFollowStatus());
                accidentCluesUpdateWrapper.set(AccidentCluesPO::getFollowCount, accidentCluesPO.getFollowCount());
                accidentCluesUpdateWrapper.set(AccidentCluesPO::getNextFollowDate, accidentCluesPO.getNextFollowDate());
                accidentCluesUpdateWrapper.set(AccidentCluesPO::getIsBruise, accidentCluesPO.getIsBruise());
                accidentCluesUpdateWrapper.in(AccidentCluesPO::getAcId, acidList);
                accidentCluesUpdateWrapper.eq(AccidentCluesPO::getIsDeleted, 0);
                accidentCluesMapper.update(null, accidentCluesUpdateWrapper);
                //更新拓展表
                logger.info("accidentCluesExtPoUpdateWrapper 更新拓展表");
                LambdaUpdateWrapper<AccidentCluesExtPo> accidentCluesExtPoUpdateWrapper = new LambdaUpdateWrapper<>();
                if (Objects.nonNull(ext)) {
                    accidentCluesExtPoUpdateWrapper.set(AccidentCluesExtPo::getFirstFollowTime, ext.getFirstFollowTime());
                    accidentCluesExtPoUpdateWrapper.set(AccidentCluesExtPo::getLastFollowTime, ext.getLastFollowTime());
                }
                accidentCluesExtPoUpdateWrapper.set(AccidentCluesExtPo::getFollowText, followDto.getFollowText());
                accidentCluesExtPoUpdateWrapper.set(AccidentCluesExtPo::getFollowFailWhy, followDto.getFollowFailWhy());
                accidentCluesExtPoUpdateWrapper.set(AccidentCluesExtPo::getOldFollowStatus, followStatus);
                accidentCluesExtPoUpdateWrapper.in(AccidentCluesExtPo::getAcId, acidList);
                accidentCluesExtPoUpdateWrapper.in(AccidentCluesExtPo::getIsDeleted, 0);
                accidentCluesExtMapper.update(null, accidentCluesExtPoUpdateWrapper);

            }

        }

        //跟进未分配线索自动分配跟进人
        this.autoAllot(accidentCluesPO, loginInfo);
        accidentCluesMapper.updateById(accidentCluesPO);
        logger.info("线索跟进结束==========");
        //查询未关联的通话记录，关联上本次的跟进记录
        accidentCluesSaNumberMapper.updateFollowId(followPo.getAcId(), followPo.getFollowId());

        if (Objects.isNull(ext)){
            logger.info("ext info not exist {}",followPo.getAcId());
        }else{
            this.publishEvent(1,accidentCluesPO.getAcId(),ext.getCrmId(),accidentCluesPO.getCluesStatus(),accidentCluesPO.getFollowStatus());
        }
        return followPo.getFollowId();
    }

    private void publishEvent(int row, Integer acId, Long crmId, Integer cluesStatus, Integer followStatus) {
        logger.info("publishEvent start row:{},crmId:{},cluesStatus:{},followStatus:{}", row, crmId, cluesStatus, followStatus);
        if (row < 1) {
            logger.info("publishEvent row < 1");
            return;
        }
        // 同步litecrm
        StatusChangePushDTO pushDTO = new StatusChangePushDTO();
        pushDTO.setLeadsType(CommonConstants.LEADS_TYPE_103); // 线索类型
        pushDTO.setSourceClueId(null == acId ? "0" : String.valueOf(acId)); // acId
        Optional.ofNullable(crmId).ifPresent(v -> {
            pushDTO.setId(String.valueOf(v)); // crmId
        });
        Optional.ofNullable(cluesStatus).ifPresent(v -> {
            pushDTO.setBizStatus(String.valueOf(v)); // 跟进状态
        });
        Optional.ofNullable(followStatus).ifPresent(v -> {
            pushDTO.setFollowUpStatus(String.valueOf(v)); // 跟进状态
        });
        applicationEventPublisher.publishEvent(new AccidentClueStatusChangeEvent(Collections.singletonList(pushDTO)));
        logger.info("publishEvent end");
    }

    /**
     * 保存跟进信息
     *
     */
    private void saveFollowInfo(AccidentCluesFollowPO followPo, LoginInfoDto loginInfo) {
        followPo.setFollowPeople(Integer.parseInt(loginInfo.getUserId() + ""));

        //处理跟进人名称
        logger.info("insertCluesFollow loginInfo.getUserName:{}",loginInfo.getUserName());
        if (StringUtils.isNullOrEmpty(loginInfo.getUserName())){
            UserOrgInfoDTO userOrgInfo = basicdataCenterService.getUserOrgInfo(String.valueOf(loginInfo.getUserId()));
            logger.info("insertCluesFollow userOrgInfo:{}",userOrgInfo);
            followPo.setFollowPeopleName(Objects.nonNull(userOrgInfo) ?userOrgInfo.getEmployeeName():"");
        }else {
            followPo.setFollowPeopleName(loginInfo.getUserName());
        }
        followPo.setRecordVersion(1);
        AccidentCluesExtPo ext = accidentCluesExtMapper.selectOne(
            new LambdaQueryWrapper<AccidentCluesExtPo>().eq(AccidentCluesExtPo::getAcId, followPo.getAcId())
                .eq(AccidentCluesExtPo::getIsDeleted, 0)
                .last("limit 1"));
        //无兄弟线索，只跟进自己
        if (Objects.isNull(ext) || StrUtil.isEmpty(ext.getRepeatSonLeadId()) || Objects.equals(StrUtil.toString(ext.getCrmId()), ext.getRepeatSonLeadId())) {
            accidentCluesFollowMapper.insert(followPo);
        } else {
            List<AccidentCluesExtPo> extList = accidentCluesExtMapper.selectList(
                new LambdaQueryWrapper<AccidentCluesExtPo>().eq(AccidentCluesExtPo::getRepeatSonLeadId, ext.getRepeatSonLeadId())
                    .eq(AccidentCluesExtPo::getIsDeleted, 0));
            AccidentCluesFollowPO followTemplate = new AccidentCluesFollowPO();
            extList.stream().map(AccidentCluesExtPo::getAcId).forEach(a -> {
                if (Objects.equals(a,followPo.getAcId())){
                    accidentCluesFollowMapper.insert(followPo);
                }else{
                    BeanUtil.copyProperties(followPo, followTemplate);
                    followTemplate.setAcId(a);
                    accidentCluesFollowMapper.insert(followTemplate);
                }
            });
        }
    }
/**
 * 修改主子线索：联系方式-手机号
 */
private void saveContactsInformation(AccidentCluesPO accidentCluesPO, AccidentCluesExtPo ext, AccidentCluesFollowDTO followDto) {
    if(StringUtils.isNullOrEmpty(followDto.getContactsInformation())){
        return;
    }
    AccidentCluesExt2Po accidentCluesExt2Po = accidentCluesExt2Mapper.selectOne(new LambdaQueryWrapper<AccidentCluesExt2Po>().eq(AccidentCluesExt2Po::getAcId, accidentCluesPO.getAcId()));
    //无兄弟线索，只修改自己
    if (Objects.isNull(ext) || StrUtil.isEmpty(ext.getRepeatSonLeadId()) || Objects.equals(StrUtil.toString(ext.getCrmId()), ext.getRepeatSonLeadId())) {
        if(Objects.isNull(accidentCluesExt2Po) ){
            accidentCluesExt2Po = AccidentCluesExt2Po.builder().acId(accidentCluesPO.getAcId()).contactsInformation(followDto.getContactsInformation()).build();
            accidentCluesExt2Mapper.insert(accidentCluesExt2Po);
        }else{
            accidentCluesExt2Po.setContactsInformation(followDto.getContactsInformation());
            accidentCluesExt2Mapper.updateById(accidentCluesExt2Po);
        }
    } else {
        List<AccidentCluesExtPo> extList = accidentCluesExtMapper.selectList(
                new LambdaQueryWrapper<AccidentCluesExtPo>().eq(AccidentCluesExtPo::getRepeatSonLeadId, ext.getRepeatSonLeadId())
                        .eq(AccidentCluesExtPo::getIsDeleted, 0));
        extList.stream().map(AccidentCluesExtPo::getAcId).forEach(a -> {
            AccidentCluesExt2Po accidentCluesExt2 = accidentCluesExt2Mapper.selectOne(new LambdaQueryWrapper<AccidentCluesExt2Po>().eq(AccidentCluesExt2Po::getAcId, a));
            if(Objects.isNull(accidentCluesExt2) ){
                accidentCluesExt2 = AccidentCluesExt2Po.builder().acId(a).contactsInformation(followDto.getContactsInformation()).build();
                accidentCluesExt2Mapper.insert(accidentCluesExt2);
            }else{
                accidentCluesExt2.setContactsInformation(followDto.getContactsInformation());
                accidentCluesExt2Mapper.updateById(accidentCluesExt2);
            }
        });
    }
}
    /**
     * 填充跟进时间、内容、失败原因
     * <p>
     * 首次跟进 or 二次跟进
     */
    private void fillFollowInfo(AccidentCluesFollowPO followPO, Integer followStatus) {
        AccidentCluesFollowPO existedFollow = accidentCluesFollowMapper.selectOne(
            new LambdaQueryWrapper<AccidentCluesFollowPO>().eq(AccidentCluesFollowPO::getAcId, followPO.getAcId())
                .eq(AccidentCluesFollowPO::getIsDeleted, 0)
                .last("limit 1"));

        //跟进内容
        LambdaUpdateWrapper<AccidentCluesExtPo> wrapper = new LambdaUpdateWrapper<AccidentCluesExtPo>().eq(AccidentCluesExtPo::getAcId, followPO.getAcId())
            .eq(AccidentCluesExtPo::getIsDeleted, 0)
            .set(AccidentCluesExtPo::getFollowText, followPO.getFollowText())
            .set(AccidentCluesExtPo::getFollowFailWhy, followPO.getFollowFailWhy())
            .set(AccidentCluesExtPo::getOldFollowStatus, followStatus);

        //首次跟进
        if (Objects.isNull(existedFollow)) {
            accidentCluesExtMapper.update(null, wrapper.set(AccidentCluesExtPo::getFirstFollowTime, DateUtil.date()));
        }
        //非首次跟进
        else {
            accidentCluesExtMapper.update(null, wrapper.set(AccidentCluesExtPo::getLastFollowTime, DateUtil.date()));
        }
    }



    /**
     * 跟进未分配线索自动分配跟进人
     * @param clue
     * @param loginInfo
     */
    private void autoAllot(AccidentCluesPO clue, LoginInfoDto loginInfo){

        if (!whitelistCheckService.checkWhitelist() || !ObjectUtils.isEmpty(clue.getFollowPeople())){
            logger.info("未命中白名单或跟进人员为空");
            return;
        }
        logger.info("跟进未分配线索自动分配跟进人");
        clue.setFollowPeople(Integer.parseInt(String.valueOf(loginInfo.getUserId())));
        clue.setFollowPeopleName(loginInfo.getUserName());
    }

    /**
     * 线索分配
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertttCluesAllot(AccidentCluesAllotDTO allotDto) {

        List<Integer> followPeoples = CollectionUtils.isEmpty(allotDto.getFollowUserList()) ? allotDto.getFollowPeoples() : allotDto.getFollowUserList().stream().map(AllotFollowUserInfoDTO::getUserId).collect(Collectors.toList());
        List<Integer> acIds=allotDto.getAcIds(); //线索数
        Map<Integer, List<AllotFollowUserInfoDTO>> userInfoMap = CollectionUtils.isEmpty(allotDto.getFollowUserList()) ? new HashMap<>() : allotDto.getFollowUserList().stream().collect(Collectors.groupingBy(AllotFollowUserInfoDTO::getUserId));
        if( followPeoples.size() > acIds.size()){
        	throw new ServiceBizException("跟进人员不能大于线索数量");
        }

        //分配线索（平均分配的原则，如果 线索 % 人！=0，则依次分配）
        Integer j = 0;
        Map<Long, List<AccidentCluesPO>> appPushMap = new HashMap<>(followPeoples.size());
        for (Integer i = 0; i < acIds.size(); i++) {
            for (Integer b = 0; b < followPeoples.size(); b++) {
                if (b.equals(j)) {
                    j++;
                    AccidentCluesPO clue = setAccidentCluesAllotPO(acIds.get(i), followPeoples.get(b), userInfoMap.get(followPeoples.get(b)), acIds.size() == 1);
                    logger.info("线索:" + acIds.get(i) + "，分配给人：" + followPeoples.get(b));
                    this.pushMap(appPushMap, clue, Long.parseLong(String.valueOf(followPeoples.get(b))));
                    if (j.equals(followPeoples.size())) {
                        j = 0;
                    }
                    break;
                }
            }
        }
        // Annotate in the new version of accident clues on 2024-05-17 delete App push
        logger.info("**************分配结束****************");
        return 1;
    }

    private void pushMap(Map<Long, List<AccidentCluesPO>> appPushMap, AccidentCluesPO clue, Long userId){

        List<AccidentCluesPO> clueList = appPushMap.get(userId);
        if (CollectionUtils.isEmpty(clueList)){
            clueList = new ArrayList<>();
            clueList.add(clue);
            appPushMap.put(userId, clueList);
            return;
        }
        clueList.add(clue);
        appPushMap.put(userId, clueList);
    }

    /**
     * 推送app消息通知
     * @param appPushMap
     * @param companyId
     * @throws ServiceBizException
     */
    private void appPush(Map<Long, List<AccidentCluesPO>> appPushMap, Long companyId) throws ServiceBizException{

        if (!whitelistCheckService.checkWhitelist()){
            logger.info("非白名单用户不推送消息，经销商id：{}", companyId);
            return;
        }
        appPushService.clueAllotAppPush(appPushMap, companyId, AccidentClueAppPushEnum.PUSH_TYPE_ALLOT);
    }

    /**
     * 根据id查询线索主单信息
     */
    @Override
    public AccidentCluesDTO selectById(Integer acId) {
        logger.info("selectById acId:{}",acId);
        AccidentCluesPO po = accidentCluesMapper.selectById(acId);
        AccidentCluesDTO clue = po.transPoToDto(AccidentCluesDTO.class);
        //查询拓展字段
        LambdaQueryWrapper<AccidentCluesExtPo> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(AccidentCluesExtPo::getAcId,acId);
        queryWrapper.eq(AccidentCluesExtPo::getIsDeleted,0);
        queryWrapper.last("limit 1");
        AccidentCluesExtPo accidentCluesExtPo = accidentCluesExtMapper.selectOne(queryWrapper);
        logger.info("selectById accidentCluesExtPo:{}",accidentCluesExtPo);
        if (Objects.nonNull(accidentCluesExtPo)){
            //翻译是否重复
            if (Objects.isNull(accidentCluesExtPo.getRepeatSonLeadId()) && Objects.isNull(accidentCluesExtPo.getCrmId())) {
                accidentCluesExtPo.setRepeatLead(10041001);
            } else if (Objects.equals(accidentCluesExtPo.getRepeatSonLeadId(), accidentCluesExtPo.getCrmId() + "")) {
                accidentCluesExtPo.setRepeatLead(10041002);
            } else {
                accidentCluesExtPo.setRepeatLead(10041001);
            }
            //来源渠道
            clue.setSourceChannel(accidentCluesExtPo.getSourceChannel());
            clue.setCrmId(accidentCluesExtPo.getCrmId());
            clue.setRegistNo(accidentCluesExtPo.getRegistNo());
            clue.setRepeatLead(accidentCluesExtPo.getRepeatLead());
            clue.setAccidentDate(accidentCluesExtPo.getAccidentDate());
            clue.setAccidentReason(accidentCluesExtPo.getAccidentReason());
            clue.setContactsName(accidentCluesExtPo.getContactsName());
            clue.setCallType(accidentCluesExtPo.getCallType());
            clue.setCallPoliceFlag(accidentCluesExtPo.getCallPoliceFlag());
            clue.setOwnerName(accidentCluesExtPo.getOwnerName());
            clue.setMobile(accidentCluesExtPo.getOwnerMobile());
            clue.setModelName(accidentCluesExtPo.getModelName());
        }
        AccidentCluesExt2Po accidentCluesExt2Po = accidentCluesExt2Mapper.selectOne(new LambdaQueryWrapper<AccidentCluesExt2Po>().eq(AccidentCluesExt2Po::getAcId, po.getAcId()));
        if(Objects.nonNull(accidentCluesExt2Po)){
            clue.setContactsInformation(accidentCluesExt2Po.getContactsInformation());
            clue.setVirtualPhoneFlag(accidentCluesExt2Po.getVirtualPhoneFlag());
            clue.setInsuranceSource(accidentCluesExt2Po.getInsuranceSource());
            // DVR线索，新增设备id(deviceId)、G-sensor曲线(gsensor)、DVR销售门店(dvrStore)
            clue.setDeviceId(accidentCluesExt2Po.getDeviceId());
            clue.setGsensor(accidentCluesExt2Po.getGsensor());
            clue.setDvrStore(accidentCluesExt2Po.getDvrStore());
        }

        AccidentCluesUserDto accidentCluesUserDto = accidentCluesSaNumberMapper.selectCusListB(acId);
        if(Objects.nonNull(accidentCluesUserDto)){
            //CDP车主
            if(org.apache.commons.lang3.StringUtils.isNotEmpty(accidentCluesUserDto.getCdpContactsPhone())){
                AccidentCluesUserDto userDto = new AccidentCluesUserDto();
                userDto.setContacts(accidentCluesUserDto.getCdpContacts());
                userDto.setContactsPhone(accidentCluesUserDto.getCdpContactsPhone());
                clue.setCdpUserInfos(Collections.singletonList(userDto));
            }
            //工单送修人
            if(org.apache.commons.lang3.StringUtils.isNotEmpty(accidentCluesUserDto.getOrderContactsInfo())){
                List<AccidentCluesUserDto> userDtos = Lists.newArrayList();
                List<ContactInfoDTO> contactInfoDTOS = JSON.parseArray(accidentCluesUserDto.getOrderContactsInfo(), ContactInfoDTO.class);
                contactInfoDTOS = contactInfoDTOS.stream().filter(e -> org.apache.commons.lang3.StringUtils.isNotEmpty(e.getCustomerMobile())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(contactInfoDTOS)){
                    int size = Math.min(contactInfoDTOS.size(), 3);
                    for(int i = 1 ; i <= size; i++){
                        ContactInfoDTO contactInfoDTO = contactInfoDTOS.get(i - 1);
                        AccidentCluesUserDto userDto = new AccidentCluesUserDto();
                        userDto.setContacts(contactInfoDTO.getCustomerName());
                        userDto.setContactsPhone(contactInfoDTO.getCustomerMobile());
                        userDtos.add(userDto);
                    }
                    clue.setOrderUserInfos(userDtos);
                }
            }
        }

        logger.info("selectById clue1:{}",clue);
        List<AccidentClueContact> contacts = accidentCluesContactsMapper.getAccidentClueContacts(clue.getDealerCode(), Collections.singletonList(Long.parseLong(String.valueOf(clue.getAcId()))), null);
        if (CollectionUtils.isEmpty(contacts)){
            return clue;
        }
        Map<Integer, List<AccidentClueContact>> contactsMap = contacts.stream().collect(Collectors.groupingBy(AccidentClueContact::getIsOwner));
        List<AccidentClueContact> ownerInfoList = contactsMap.get(CommonConstants.DICT_IS_YES);
        if (!CollectionUtils.isEmpty(ownerInfoList)){
            AccidentClueContact ownerInfo = ownerInfoList.get(0);
            clue.setOwnerName(ownerInfo.getContacts());
            clue.setMobile(ownerInfo.getContactsPhone());
        }
        clue.setContactList(contactsMap.get(CommonConstants.DICT_IS_NO));

        //无兄弟线索，只修改自己
        logger.info("selectById clue2:{}",clue);
        return clue;
    }

    /**
     * 根据acId查询跟进记录
     */
    @Override
    public IPage<AccidentCluesFollowPO> selectCluesFollowPageBysql(Page page, Integer acId) {
        AccidentCluesFollowPO followPO = new AccidentCluesFollowPO();
        followPO.setAcId(acId);
        List<AccidentCluesFollowPO> follows = accidentCluesFollowMapper.selectPageBySql(page, followPO);
        if (CommonUtils.isNullOrEmpty(follows)) {
            page.setRecords(new ArrayList<>());
        } else {
            page.setRecords(follows);
        }
        return page;
    }

    /**
     * 查询联系人
     */
    @Override
    public List<AccidentCluesUserDto> selectCusList(Integer acId) {
        AccidentCluesUserDto accidentCluesUserDto = accidentCluesSaNumberMapper.selectCusListB(acId);
        List<AccidentCluesUserDto> cluesUserDtos = Lists.newArrayList();
        //报案人
        if(org.apache.commons.lang3.StringUtils.isNotEmpty(accidentCluesUserDto.getContactsPhone())){
            AccidentCluesUserDto userDto = new AccidentCluesUserDto();
            userDto.setContactsTypeName("报案人");
            userDto.setContacts(accidentCluesUserDto.getContacts());
            userDto.setContactsPhone(accidentCluesUserDto.getContactsPhone());
            cluesUserDtos.add(userDto);
        }
        //车主
        if(org.apache.commons.lang3.StringUtils.isNotEmpty(accidentCluesUserDto.getOwnerMobile())){
            AccidentCluesUserDto userDto = new AccidentCluesUserDto();
            userDto.setContactsTypeName("车主");
            userDto.setContacts(accidentCluesUserDto.getOwnerName());
            userDto.setContactsPhone(accidentCluesUserDto.getOwnerMobile());
            cluesUserDtos.add(userDto);
        }
        //CDP车主
        if(org.apache.commons.lang3.StringUtils.isNotEmpty(accidentCluesUserDto.getCdpContactsPhone())){
            AccidentCluesUserDto userDto = new AccidentCluesUserDto();
            userDto.setContactsTypeName("CDP车主");
            userDto.setContacts(accidentCluesUserDto.getCdpContacts());
            userDto.setContactsPhone(accidentCluesUserDto.getCdpContactsPhone());
            cluesUserDtos.add(userDto);
        }
        //工单送修人
        if(org.apache.commons.lang3.StringUtils.isNotEmpty(accidentCluesUserDto.getOrderContactsInfo())){
            List<ContactInfoDTO> contactInfoDTOS = JSON.parseArray(accidentCluesUserDto.getOrderContactsInfo(), ContactInfoDTO.class);
            contactInfoDTOS = contactInfoDTOS.stream().filter(e -> org.apache.commons.lang3.StringUtils.isNotEmpty(e.getCustomerMobile())).collect(Collectors.toList());
            int size = Math.min(contactInfoDTOS.size(), 3);
            for(int i = 1 ; i <= size; i++){
                ContactInfoDTO contactInfoDTO = contactInfoDTOS.get(i - 1);
                AccidentCluesUserDto userDto = new AccidentCluesUserDto();
                userDto.setContactsTypeName("送修人"+i);
                userDto.setContacts(contactInfoDTO.getCustomerName());
                userDto.setContactsPhone(contactInfoDTO.getCustomerMobile());
                cluesUserDtos.add(userDto);
            }
        }
        return cluesUserDtos;
    }

    /**
     * 呼叫登记
     */
    @Override
    public String saveWorkNumber(AccidentCluesSaNumberDTO saCustomerNumberDTO) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //校验当前账号是否绑定AI语音工作号
        LambdaQueryWrapper<SaWorkNumberPO> queryWrapper = new QueryWrapper<SaWorkNumberPO>().lambda();
        queryWrapper.eq(SaWorkNumberPO::getDealerCode, loginInfoDto.getOwnerCode());
        queryWrapper.eq(SaWorkNumberPO::getSaId, saCustomerNumberDTO.getSaId());
        SaWorkNumberPO rs = saWorkNumberMapper.selectOne(queryWrapper);
        if (rs == null) {
            throw new DALException("当前账号还未绑定AI语音工作号，不可使用!");
        }
        //校验联系方式格式是否正确
        if (11 != saCustomerNumberDTO.getCusNumber().length()) {
            throw new DALException("手机号应为11位数");
        }
        logger.info("线索外呼入参:{}",JSON.toJSONString(saCustomerNumberDTO));
        //uuid
        String callId = this.getUUID32();
        String workNum = this.operatorIf(saCustomerNumberDTO, callId, rs);
        if (!ObjectUtils.isEmpty(workNum)){
            return workNum;
        }
        AccidentCluesSaNumberPO inPo = new AccidentCluesSaNumberPO();
        inPo.setCallId(callId);
        inPo.setAcId(saCustomerNumberDTO.getAcId());
        inPo.setFollowId(saCustomerNumberDTO.getFollowId());
        inPo.setSaId(saCustomerNumberDTO.getSaId());
        inPo.setCusName(saCustomerNumberDTO.getCusName());
        inPo.setCusNumber(saCustomerNumberDTO.getCusNumber());
        inPo.setDealerCode(loginInfoDto.getOwnerCode());
        inPo.setSaName(rs.getSaName());
        inPo.setSaNumber(rs.getSaNumber());
        inPo.setWorkNumber(rs.getWorkNumber());
        accidentCluesSaNumberMapper.insert(inPo);
        //电信 呼叫登记
        Map<String, Object> map = new HashMap<>();
        map.put("callId", callId);  //登记唯一标识
        map.put("holderNumber", rs.getSaNumber()); //服务顾问手机号
        map.put("workNumber", rs.getWorkNumber()); //工作号
        map.put("customNumber", saCustomerNumberDTO.getCusNumber()); //联系方式
        map.put("expireMinute", 30);  //默认写死30
        logger.info("呼叫登记：" + map);
        String str = DccHttpHelper.httpPost(URL_MAPPING_REGISTER, map);
        DccResponseUtil response = JSONObject.parseObject(str, DccResponseUtil.class);
        if (!"0".equals(response.getCode())) {
            throw new DALException(response.getMessage());
        }
        logger.info("呼叫登记结束：" + str);
        //返回绑定的工作号
        return rs.getWorkNumber();
    }

    /**
     * 新电信呼叫登记
     * @param saCustomerNumberDTO
     * @param callId
     * @param rs
     * @return
     */
    private String operatorIf(AccidentCluesSaNumberDTO saCustomerNumberDTO, String callId, SaWorkNumberPO rs) {

        if (!whitelistCheckService.checkWhitelist() || !org.apache.commons.lang.StringUtils.equalsIgnoreCase(rs.getOperator(), String.valueOf(CommonConstants.NEW_TELECOM_OPERATOR))){
            return null;
        }

        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        String   operator = String.valueOf(CommonConstants.NEW_TELECOM_AXB);
        Map<String, Object> registerResultMap = workNumberServiceContext.register(operator, callId,
                rs.getSaNumber(), rs.getWorkNumber(), saCustomerNumberDTO.getCusNumber());
        boolean isSuccessOfRegister = workNumberServiceContext
                .isSuccess(operator, CommonConstants.WORK_NUMBER_MENTHODTYPE_REGISTER, registerResultMap);
        AccidentCluesSaNumberPO inPo = new AccidentCluesSaNumberPO();
        if (!isSuccessOfRegister) {
            String resMessage=registerResultMap.containsKey("message")?registerResultMap.get("message").toString():registerResultMap.get("msg").toString();
            throw new ServiceBizException(resMessage);
        } else {
            logger.info("当前操作供应商==={}，工作号===={}，bindId==={}", operator,
                    rs.getWorkNumber(), inPo.getCallId());
            inPo.setCallId(callId);
            inPo.setAcId(saCustomerNumberDTO.getAcId());
            inPo.setFollowId(saCustomerNumberDTO.getFollowId());
            inPo.setSaId(saCustomerNumberDTO.getSaId());
            inPo.setCusName(saCustomerNumberDTO.getCusName());
            inPo.setCusNumber(saCustomerNumberDTO.getCusNumber());
            inPo.setDealerCode(loginInfoDto.getOwnerCode());
            inPo.setSaName(rs.getSaName());
            inPo.setSaNumber(rs.getSaNumber());
            inPo.setWorkNumber(rs.getWorkNumber());
            logger.info("====呼叫登记保存信息SaCustomerNumberPO======{}", inPo);
            logger.info("====loginInfo========={}", loginInfoDto);
            accidentCluesSaNumberMapper.insert(inPo);
        }
        return rs.getWorkNumber();
    }

    /**
     * 查询通话记录
     */
    @Override
    public List<CallDetailsPO> selectCallDetailByfollowId(Integer followId) {
        return callDetailsMapper.getAccidentCluesCallDetails(followId);
    }

    /**
     * 线索分配
     * @param acId
     * @param followPeopleId
     * @param contactInfo
     * @param flag
     * @return
     * @throws ServiceBizException
     */
    private AccidentCluesPO setAccidentCluesAllotPO(Integer acId, Integer followPeopleId, List<AllotFollowUserInfoDTO> contactInfo, boolean flag) throws ServiceBizException{

        //更新主单跟进人员
        AccidentCluesPO accidentCluesPO = accidentCluesMapper.selectById(acId);
        accidentCluesPO.setFollowPeople(followPeopleId);
        accidentCluesPO.setFollowPeopleName(CollectionUtils.isEmpty(contactInfo) ? accidentCluesPO.getFollowPeopleName() : contactInfo.get(0).getEmployeeName());
        accidentCluesMapper.updateById(accidentCluesPO);
        //仅能分配未进厂且跟进中线索
        this.checkAllot(accidentCluesPO, flag);
        //增加分配记录
        AccidentCluesAllotPO allotPo = new AccidentCluesAllotPO();
        allotPo.setAcId(acId);
        allotPo.setDealerCode(FrameworkUtil.getLoginInfo().getOwnerCode());
        allotPo.setFollowPeople(followPeopleId);
        allotPo.setAllotPersonnel(Integer.parseInt(FrameworkUtil.getLoginInfo().getUserId() + ""));
        allotPo.setAllotDate(new Date());
        allotPo.setAllotType(CommonConstants.NUM_1);
        accidentCluesAllotMapper.insert(allotPo);

        return accidentCluesPO;
    }

    /**
     * 分配校验，仅能分配未进厂且跟进中线索
     * @param clue
     * @param flag
     * @throws ServiceBizException
     */
    private void checkAllot(AccidentCluesPO clue, boolean flag) throws ServiceBizException{

        if (!whitelistCheckService.checkWhitelist()){
            return;
        }
        String errMsg = "仅能分配未进厂且跟进中线索";
        try {
            Assert.isTrue(!ObjectUtils.nullSafeEquals(clue.getFollowStatus(), CommonConstants.FOLLOW_STATUS_FINISHED)
                    && !ObjectUtils.nullSafeEquals(clue.getFollowStatus(), CommonConstants.FOLLOW_STATUS_FAIL), errMsg);
        }catch (Exception e){
            errMsg = flag ? "此线索不可分配" : "存在不可分配线索";
            throw new ServiceBizException(errMsg);
        }
    }

    public String getUUID32() {
        return UUID.randomUUID().toString().replace("-", "").toLowerCase();
    }

    /**
     * 导出
     */
    @Override
    public List<Map> exportExcelAccident(AccidentCluesDTO dto) {
        //非厂端
        if (!"vcdc".equals(dto.getSource())) {
            dto.setDealerCode(FrameworkUtil.getLoginInfo().getOwnerCode());
            if (!StringUtils.isNullOrEmpty(dto.getIsself()) && "10371001".equals(dto.getIsself())) {
                dto.setFollowPeople(Integer.parseInt(FrameworkUtil.getLoginInfo().getUserId() + ""));
            }
        }
        if (!StringUtils.isNullOrEmpty(dto.getReportDateEnd())) {
            dto.setReportDateEnd(DateUtils.getAfterDate(dto.getReportDateEnd(), 1));
        }
        if (!StringUtils.isNullOrEmpty(dto.getNextFollowDateEnd())) {
            dto.setNextFollowDateEnd(DateUtils.getAfterDate(dto.getNextFollowDateEnd(), 1));
        }
        if (Objects.nonNull(dto.getCreatedDateEnd())) {
            dto.setCreatedDateEnd(DateUtils.getAfterDate(dto.getCreatedDateEnd(), 1));
        }
        Page page = new Page<>(dto.getCurrentPage(), dto.getPageSize());
        logger.info("page-->" + JSON.toJSONString(page));
        List<Map> list = accidentCluesMapper.exportExcelAccident(page, dto.transDtoToPo(AccidentCluesPO.class));
        return list;
    }

    @Override
    public void exportExcelNew(AccidentCluesDTO dto) {

        logger.info("导出开始===================================》");
        try {
            String url = salesUrlProperties.getDownloadService() + salesUrlProperties.getExportExcelUrl();

            List<ExcelExportColumn> exportColumnList = new ArrayList<>();
            exportColumnList.add(new ExcelExportColumn("license", "车牌号"));
            exportColumnList.add(new ExcelExportColumn("contacts", "联系人"));
            exportColumnList.add(new ExcelExportColumn("report_date", "报案时间", "yyyy-MM-dd HH:mm:ss"));
            exportColumnList.add(new ExcelExportColumn("follow_status", "跟进状态"));
            exportColumnList.add(new ExcelExportColumn("clues_status", "线索状态"));
            exportColumnList.add(new ExcelExportColumn("into_dealer_code", "进厂经销商"));
            exportColumnList.add(new ExcelExportColumn("into_dealer_date", "进厂时间", "yyyy-MM-dd HH:mm:ss"));
            exportColumnList.add(new ExcelExportColumn("into_ro_no", "进厂工单号"));
            exportColumnList.add(new ExcelExportColumn("follow_people_name", "跟进人员"));
            exportColumnList.add(new ExcelExportColumn("next_follow_date", "下次跟进时间"));
            exportColumnList.add(new ExcelExportColumn("follow_count", "跟进次数"));
            exportColumnList.add(new ExcelExportColumn("insurance_company_name", "保险公司"));
            exportColumnList.add(new ExcelExportColumn("created_at", "线索创建时间", "yyyy-MM-dd HH:mm:ss"));
            exportColumnList.add(new ExcelExportColumn("is_insured", "是否本店承包"));
            exportColumnList.add(new ExcelExportColumn("double_accident", "单方/双方事故"));
            exportColumnList.add(new ExcelExportColumn("duty_division", "事故责任划分"));
            exportColumnList.add(new ExcelExportColumn("is_report", "是否现场报案"));
            exportColumnList.add(new ExcelExportColumn("is_trailer", "是否拖车服务"));
            exportColumnList.add(new ExcelExportColumn("follow_text", "跟进内容"));

            DownloadDTO downloadDTO = new DownloadDTO();
            downloadDTO.setQueryParams(dto.toMaps());
            downloadDTO.setExcelName("线索导出.xlsx");
            downloadDTO.setSheetName("线索");
            downloadDTO.setServiceUrl(innerUrlProperties.getAccidentCluesExport());
            downloadDTO.setExcelExportColumnList(exportColumnList);


            //RestTemplate restTemplate = new RestTemplate();
            ResponseDTO response = new ResponseDTO();
            HttpHeaders headers = new HttpHeaders();
            try {
                HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<Map<String, Object>>(downloadDTO.toMaps(), headers);
                ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(url, HttpMethod.POST, httpEntity,
                        ResponseDTO.class);

                if (responseEntity.getBody() != null) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    //转换返回对象
                    response = objectMapper.convertValue(responseEntity.getBody(), ResponseDTO.class);
                } else {
                    throw new Exception("请求接口返回对象responseEntity为空");
                }
            } catch (Exception e) {
                logger.error("download-service接口请求地址:{},请求参数:{}，返回失败:{}", e);
                if (e != null) {
                    response.setReturnCode("error");
                    response.setReturnMessage(e.getMessage());
                }
            }

//            generateData(url,downloadDTO.toMaps());
        } catch (Exception e) {
            logger.info("线索导出：出现异常");
            e.printStackTrace();
        }

    }

    /**
     * 导入
     */
    @Override
    public ImportTempResult<AccidentCluesImportPO> importTemp(MultipartFile importFile) throws Exception {
        msg = "success";

        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (com.yonyou.dmscloud.function.utils.common.StringUtils.isNullOrEmpty(loginInfoDto.getUserId())) {
            throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
        }
        List<AccidentCluesImportPO> addList = new ArrayList<>();

        Collection<String> vinList = Sets.newHashSet();
        //删除历史数据
        accidentCluesImportMapper.deleteByCreatedBy("" + loginInfoDto.getUserId());

        //车型
        List<ModelVO> modelVOList = basicdataCenterService.queryAllModel(new HashMap<>());
        List<String> modelCodeList = new ArrayList<String>();
        if (!CommonUtils.isNullOrEmpty(modelVOList)) {
            modelCodeList = modelVOList.stream().map(modelVo -> modelVo.getModelCode()).collect(Collectors.toList());
        }
        final List<String> modelCode = modelCodeList;


        //保险公司
        List<Map> list = customerRepairCommonClient.getInsuranceCompany(10041001);
        List<Object> insurance = new ArrayList<Object>();
        if (!CommonUtils.isNullOrEmpty(list)) {
            insurance = list.stream().map(map -> map.get("INSURATION_SHORT_NAME")).collect(Collectors.toList());
        }
        final List<Object> insuranceList = insurance;
        logger.info("保险公司===" + insuranceList);

//        final List<Object> insuranceList=new ArrayList<Object>();
//        final List<String> modelCode=new ArrayList<String>();
        excelReadServiceIs.analyzeExcelFirstSheet(importFile, new AbstractExcelReadCallBack<AccidentCluesImportDTO>(
                AccidentCluesImportDTO.class, new ExcelReadCallBack<AccidentCluesImportDTO>() {
            private Integer seq = 1;

            @Override
            public void readRowCallBack(AccidentCluesImportDTO dto, boolean b) {
                AccidentCluesImportPO po = new AccidentCluesImportPO();
                //校验excel数据
                Map<String, Object> result1 = validationData(dto, po, modelCode, insuranceList);
                // msg=result1.get("msg").toString();
                if ((Boolean) result1.get("isOk")) {
                    po.setDealerCode(loginInfoDto.getOwnerCode());
                    po.setLicense(dto.getLicense());
                    po.setModels(dto.getModels());

                    po.setContacts(dto.getContacts());
                    po.setContactsPhone(dto.getContactsPhone());
                    if ("保险公司短信推送".equals(dto.getCluesResource())) {
                        po.setCluesResource(CommonConstants.CLUES_RESOURCE_BXGSTS);
                    } else if ("客户来电".equals(dto.getCluesResource())) {
                        po.setCluesResource(CommonConstants.CLUES_RESOURCE_KHLD);
                    } else if ("外拓业务".equals(dto.getCluesResource())) {
                        po.setCluesResource(CommonConstants.CLUES_RESOURCE_WTYW);
                    } else if ("其他".equals(dto.getCluesResource())) {
                        po.setCluesResource(CommonConstants.CLUES_RESOURCE_QT);
                    }
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    simpleDateFormat.setLenient(false);
                    po.setReportDate(DateUtils.parseDateStrToDate(simpleDateFormat.format(dto.getReportDate()), "yyyy-MM-dd HH:mm:ss"));
                    po.setAccidentAddress(dto.getAccidentAddress());
                    po.setInsuranceCompanyName(dto.getInsuranceCompanyName());
                    po.setRemark(dto.getRemark());
                    po.setIsError(0);
                }
                po.setLineNumber(++seq);
                addList.add(po);

            }
        }));
        //if("success".equals(msg)) {
        if (!CommonUtils.isNullOrEmpty(addList)) {
            int listSize = addList.size();
            int toIndex = 100;
            for (int i = 0; i < addList.size(); i += toIndex) {
                if (i + toIndex > listSize) {        //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
                    toIndex = listSize - i;
                }
                List<AccidentCluesImportPO> insertList = addList.subList(i, i + toIndex);
                accidentCluesImportMapper.bulkInsert(insertList, loginInfoDto.getUserId());
            }
        }
        //}

        logger.info("msg========" + msg);
        return this.checkTmpData(vinList, msg);
    }

    /**
     * 校验导入excel数据
     *
     * @param dto
     * @param po
     * @return
     */
    private Map<String, Object> validationData(AccidentCluesImportDTO dto, AccidentCluesImportPO po, List<String> modelCodeList, List<Object> insuranceList) {
        boolean isOk = true;
        Map<String, Object> result = new HashMap<String, Object>();
        if (!StringUtils.isNullOrEmpty(dto.getInsuranceCompanyName())) {
            if (!CommonUtils.isNullOrEmpty(insuranceList)) {
                if (!insuranceList.contains(dto.getInsuranceCompanyName().trim().toString())) {
                    //msg=dto.getInsuranceCompanyName().trim()+"保险公司不存在，,请联系系统运维团队";
                    po.setErrorMsg(dto.getInsuranceCompanyName().trim() + "保险公司不存在，请联系系统运维团队");
                    po.setIsError(1);
                    isOk = false;
                    //throw new ServiceBizException(msg);
                }
            }
        }


        if (!StringUtils.isNullOrEmpty(dto.getModels())) {
            if (!CommonUtils.isNullOrEmpty(modelCodeList)) {
                if (!modelCodeList.contains(dto.getModels().trim())) {
                    po.setErrorMsg(dto.getModels() + "车型代码不存在");
                    po.setIsError(1);
                    isOk = false;
                }
            }

        }
        if (StringUtils.isNullOrEmpty(dto.getLicense())) {
            po.setErrorMsg("车牌号不能为空");
            po.setIsError(1);
            isOk = false;
        }
        if (StringUtils.isNullOrEmpty(dto.getContactsPhone())) {
            po.setErrorMsg("联系方式不能为空");
            po.setIsError(1);
            isOk = false;
        }
        if (StringUtils.isNullOrEmpty(dto.getCluesResource())) {
            po.setErrorMsg("线索来源不能为空");
            po.setIsError(1);
            isOk = false;
        }

        if (!"保险公司短信推送".equals(dto.getCluesResource()) && !"客户来电".equals(dto.getCluesResource())
                && !"外拓业务".equals(dto.getCluesResource()) && !"其他".equals(dto.getCluesResource())
        ) {
            po.setErrorMsg("线索来源只能是'电话，短信，外拓业务，其他'");
            po.setIsError(1);
            isOk = false;
        }
        if (StringUtils.isNullOrEmpty(dto.getInsuranceCompanyName())) {
            po.setErrorMsg("保险公司不能为空");
            po.setIsError(1);
            isOk = false;
        }
        if (StringUtils.isNullOrEmpty(dto.getReportDate())) {
            po.setErrorMsg("报案时间不能为空或者格式不正确（yyyy-MM-dd HH:mm:ss）");
            po.setIsError(1);
            isOk = false;
        }
        result.put("isOk", isOk);
        result.put("msg", msg);
        return result;
    }

    /**
     * 校验更新临时表，返回校验结果对象
     *
     * @return
     * <AUTHOR>
     * @since 2020/03/19
     */
    private ImportTempResult<AccidentCluesImportPO> checkTmpData(Collection<String> vinList, String msg) {
        ImportTempResult<AccidentCluesImportPO> importResult = new ImportTempResult<AccidentCluesImportPO>();
        // 获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        accidentCluesImportMapper.updateError(loginInfoDto.getOwnerCode());

        //查询错误项
        List<AccidentCluesImportPO> listError = accidentCluesImportMapper.queryError(loginInfoDto.getUserId());
        if (!CommonUtils.isNullOrEmpty(listError)) {
            importResult.setErrorList(listError);
        } else {
            logger.info("未查到错误数据！");
        }
        // 查询成功项
        List<AccidentCluesImportPO> listSuccess = accidentCluesImportMapper.querySuccess(loginInfoDto.getUserId());
        if (!CommonUtils.isNullOrEmpty(listSuccess)) {
            importResult.setSuccessList(listSuccess);
        } else {
            logger.info("未查到正确数据！");
        }
        if (!"success".equals(msg)) {
            importResult.setErrorMsg(msg);
        } else {
            importResult.setErrorMsg("success");
        }
        // 查询正确数据数
        importResult.setSuccessCount(accidentCluesImportMapper.querySucessCount(loginInfoDto.getUserId()));

        return importResult;
    }


    /**
     * 批量插入
     */
    @Override
    public void batchInsert() {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();

        //主单
        accidentCluesMapper.batchInsert(loginInfoDto.getUserId());

        //获取oneId
        logger.info("获取oneid");
        List<CustomerInfoCenterDTO> param = accidentCluesImportMapper.selectPhone(loginInfoDto.getUserId());
        ;
        logger.info("入参：" + JSON.toJSONString(param).toString());
        List<CustomerInfoListReturnDTO> oneIdList = businessPlatformService.getOneId(param);
        logger.info("返回：" + JSON.toJSONString(oneIdList).toString());
        accidentCluesMapper.updateOneId(oneIdList);

        this.initExtBatch();

        //删除历史数据
        accidentCluesImportMapper.deleteByCreatedBy("" + loginInfoDto.getUserId());

    }

    /**
     * 导入线索的 EXT 表数据更新
     *
     */
    private void initExtBatch() {
        HashMap<String, String> insuranceMap = this.insuranceMap();

        List<AccidentCluesDTO> clueList = accidentCluesMapper.selectClueWithoutExt();

        CompanyDetailByCodeDTO companyInfo = complaintDealerCcmRefService.getCompanyInfo(FrameworkUtil.getLoginInfo().getOwnerCode());

        if (CollUtil.isEmpty(clueList)) {
            return;
        }
        for (AccidentCluesDTO clue : clueList) {
            this.initExtInfo(clue,insuranceMap,companyInfo);
        }
    }

    /**
     * 保险公司 map
     * key：INSURATION_SHORT_NAME
     * value：INSURATION_CODE
     */
    private HashMap<String, String> insuranceMap() {
        List<Map> insuranceList = customerRepairCommonClient.getInsuranceCompany(10041001);
        HashMap<String, String> insuranceMap = new HashMap<>();
        if (CollUtil.isNotEmpty(insuranceList)) {
            for (Map insurance : insuranceList) {
                insuranceMap.put(StrUtil.toString(insurance.get("INSURATION_SHORT_NAME")), StrUtil.toString(insurance.get("INSURATION_CODE")));
            }
        }
        logger.info("insurance map:{}",insuranceMap);
        return insuranceMap;
    }

    /**
     * 初始化事故线索 ext & vin & 保险公司ID
     *
     */
    private void initExtInfo(AccidentCluesDTO clue, HashMap<String, String> insuranceMap, CompanyDetailByCodeDTO companyInfo) {
        logger.info("init accident clue ext info start :{}", clue);
        if (Objects.isNull(clue) || Objects.isNull(clue.getAcId()) || Objects.isNull(clue.getLicense())) {
            logger.info("init ext fail,miss param :{}", clue);
            return;
        }
        AccidentCluesExtPo ext = AccidentCluesExtPo.builder().acId(clue.getAcId()).sourceChannel("NewBie").channelType("NewBie").build();
        //车主
        PadVehiclePreviewResultVo owner = dmscloudServiceClient.queryOwnerVehicleInterf(new PadVehiclePreviewVo().setLicense(clue.getLicense()));
        logger.info("init ext info query owner vehicle info :{}", owner);
        if (Objects.nonNull(owner)) {
            ext.setOwnerMobile(owner.getMobile()).setOwnerName(owner.getOwnerName());
        }
        //填充主表信息，车辆 & 大小区 & 保险公司
        LambdaUpdateWrapper<AccidentCluesPO> wrapper = new LambdaUpdateWrapper<AccidentCluesPO>().set(AccidentCluesPO::getVin,
                Objects.isNull(owner) ? null : owner.getVin())
            .set(AccidentCluesPO::getInsuranceCompanyId, insuranceMap.get(clue.getInsuranceCompanyName()))
            .eq(AccidentCluesPO::getAcId, clue.getAcId())
            .eq(AccidentCluesPO::getIsDeleted, 0);
        //填充大小区
        if (Objects.nonNull(companyInfo)) {
            clue.setDealerName(companyInfo.getCompanyShortNameCn());
            if (Objects.isNull(clue.getAfterBigAreaId())) {
                wrapper.set(AccidentCluesPO::getAfterBigAreaId, companyInfo.getAfterBigAreaId());
            }
            if (Objects.isNull(clue.getAfterBigAreaName())) {
                wrapper.set(AccidentCluesPO::getAfterBigAreaName, companyInfo.getAfterBigAreaName());
            }
            if (Objects.isNull(clue.getAfterSmallAreaId())) {
                wrapper.set(AccidentCluesPO::getAfterSmallAreaId, companyInfo.getAfterSmallAreaId());
            }
            if (Objects.isNull(clue.getAfterSmallAreaName())) {
                wrapper.set(AccidentCluesPO::getAfterSmallAreaName, companyInfo.getAfterSmallAreaName());
            }
        }

        ResponseDTO<Page<VehicleDto>> res = midEndVehicleCenterFeign.queryVehiclePage(
            new VehiclePageQueryDto().setPage(1).setPageSize(10).setData(new VehiclePageQueryDetailDto().setPlateNumber(clue.getLicense())));
        if (Objects.isNull(res) || !Objects.equals(res.getReturnCode(), CommonConstants.ZERO) || Objects.isNull(res.getData()) || CollUtil.isEmpty(
            res.getData().getRecords())) {
            throw new ServiceBizException("查询车辆信息失败");
        }
        logger.info("vehicle info {}:{}", clue.getVin(), res.getData().getRecords());
        VehicleDto vehicle = res.getData().getRecords().get(0);
        if (Objects.nonNull(vehicle)) {
            ext.setModelName(vehicle.getModelName()).setModelYear(vehicle.getModelYear());
            wrapper.set(AccidentCluesPO::getVin, vehicle.getVin());
            clue.setVin(vehicle.getVin());
        }
        logger.info("init ext po :{}", ext);
        accidentCluesMapper.update(null, wrapper);
        accidentCluesExtMapper.insert(ext);

        //导入线索的上报
        applicationEventPublisher.publishEvent(new AccidentClueInfoPushEvent(Collections.singletonList(clue)));
    }

    /**
     * 查询失败项
     */
    @Override
    public IPage<AccidentCluesImportPO> selectErrorPage(Page page) {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (StringUtils.isNullOrEmpty(loginInfoDto.getUserId())) {
            throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
        }
        List<AccidentCluesImportPO> list = accidentCluesImportMapper.selectErrorPage(page, loginInfoDto.getUserId());
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            page.setRecords(list);
            return page;
        }
    }

    /**
     * 查询成功项
     */
    @Override
    public IPage<AccidentCluesImportPO> selectSuccessPage(Page page) {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (StringUtils.isNullOrEmpty(loginInfoDto.getUserId())) {
            throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
        }
        List<AccidentCluesImportPO> list = accidentCluesImportMapper.selectSuccessPage(page, loginInfoDto.getUserId());
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            page.setRecords(list);
            return page;
        }
    }

    /**
     * 查询最近的一个跟进记录
     */
    @Override
    public AccidentCluesFollowPO selectMaxFollwByAcId(Integer acId) {
        List<AccidentCluesFollowPO> list = accidentCluesFollowMapper.selectMaxFollwByAcId(acId);
        if (!CommonUtils.isNullOrEmpty(list) && list.size() > 0) {
            return list.get(0);
        }
        return new AccidentCluesFollowPO();
    }

    /**
     * 14超时更新
     */
    @Override
    public void updateTimeOut() {
        logger.info("======更新14天超时（开始）");
        accidentCluesMapper.updateTimeOut();
        logger.info("======更新14天超时（完成）");
    }

    @Override
    public void updateTimeOutClues() {

        logger.info("**********超时{}天取消定时任务开始执行**********",expireDay);
        accidentCluesMapper.updateTimeOutClues(expireDay);
        logger.info("**********超时取消定时任务执行结束**********");
    }

    /**
     * 消息提醒
     */
    @Override
    public void accidentCluesAlert() {
        logger.info("======事故线索消息提醒（开始）");

        logger.info("逾期未跟进=========>");
        //逾期未跟进 （线索状态是未进厂，跟进状态为继续跟进，且没有最新的跟进记录）
        //    查询逾期10分钟的线索
        List<AccidentCluesPO> timeoutNotFollowList= accidentCluesMapper.selectTimeoutNotFollow();
        for(AccidentCluesPO ac:timeoutNotFollowList) {
            String context="事故车辆【"+ac.getLicense()+"】，报案时间【"+DateUtils.formateDateToString(ac.getReportDate(),DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS)+"】，已超过上次设置的跟进时间，请及时跟进。";
            reportCommonClient.sendMessageByRoleNotRedirect("事故线索管理",context
                    ,ac.getDealerCode(),"SGWTZY");

        }


        logger.info("预约未进厂=========>");
        //预约未进厂 (线索状态是未进厂，跟进状态为跟进成功，客户进店时间，但未关联工单号)
        //查询预约时间 超时10分钟的
        List<AccidentCluesPO> appointmentNotIntoList= accidentCluesMapper.appointmentNotInto();
        for(AccidentCluesPO ac:appointmentNotIntoList) {
            String context="事故车辆【"+ac.getLicense()+"】还未进厂，已超过预约时间【"+DateUtils.formateDateToString(ac.getAppointmentIntoDate(),DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS)+"】，请及时跟进。";
            reportCommonClient.sendMessageByRoleNotRedirect("事故线索管理",context
                    ,ac.getDealerCode(),"SGWTZY");
        }


        logger.info("预约提前提醒========>");
        //查询将来的50-70分钟后有没有预约的
        List<AccidentCluesPO> nextAppintmentDataAccident= accidentCluesMapper.nextAppintmentDataAccident();
        for(AccidentCluesPO ac:nextAppintmentDataAccident) {
            String context="事故车辆【"+ac.getLicense()+"】还未进厂，离预约时间【"+DateUtils.formateDateToString(ac.getAppointmentIntoDate(),DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS)+"】还有1小时，请及时跟进。";
            reportCommonClient.sendMessageByRoleNotRedirect("事故线索管理",context
                    ,ac.getDealerCode(),"SGWTZY");
        }
        //更新提醒的
        accidentCluesMapper.updateRmind();

        logger.info("======事故线索消息提醒（结束）");
    }


    /**
     *返回当前经销商未跟进线索数量+继续跟进线索数量
     */
    @Override
    public RepairCountVo count() {
        if (!whitelistCheckService.checkWhitelist()){
            return new RepairCountVo(0L);
        }
        RepairCountVo vo=new RepairCountVo();
        LoginInfoDto loginInfo = FrameworkUtil.getLoginInfo();
        String ownerCode = loginInfo.getOwnerCode();
        Integer rangeId = rangeService.getRangeControlFromMid(menuId);
        List<Long> userList=new ArrayList<>();
        if(rangeId!=0){
            userList = rangeService.hasRangeAccidentClueList(rangeId,loginInfo);
        }
        long accidentClueCount = accidentCluesMapper.selectCountBySql(ownerCode, userList, limitDate);
        return vo.setAccidentClueCount(accidentClueCount);
    }

    /**
     * 新增事故线索
     * @param dto clue
     * @return
     * @throws ServiceBizException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveAccidentClues(AccidentCluesDTO dto) throws ServiceBizException {

        logger.info("事故线索新增参数===>>{}", JSONObject.toJSONString(dto));
        String ownerCode = FrameworkUtil.getLoginInfo().getOwnerCode();

        //查询经销商名
        com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO<List<CompanyDetailInfoDTO>> res = orgClient.selectByCompanyCode(
            new CompanyParamDTO().setCompanyCode(ownerCode));
        if (Objects.equals(res.getReturnCode(), CommonConstants.RETURN_CODE_0) && CollUtil.isNotEmpty(res.getData())) {
            CompanyDetailInfoDTO dealer = res.getData().get(0);
            dto.setDealerName(Objects.isNull(dealer) ? null : dealer.getCompanyNameCn());
        }

        String userId = String.valueOf(FrameworkUtil.getLoginInfo().getUserId());
        Integer acId = null;
        //短信识别自动生成线索逻辑
        //场景1、短信读取，短信内容放入dto.getParam();场景2、手动创建线索
        try {
            if (!ObjectUtils.isEmpty(dto.getParam())){
                logger.info("开始短信模版线索创建，ownerCode：{}, userId:{}", ownerCode, userId);
                acId = this.saveAutoRecognize(dto.getParam(), ownerCode, userId);

            }else {
                //保存车主信息
                if (!ObjectUtils.isEmpty(dto.getMobile())){
                    dto.getContactList().add(new AccidentClueContact(dto.getOwnerName(), dto.getMobile(), CommonConstants.DICT_IS_YES));
                }
                this.checkContactsMobile(dto.getContactList());
                //this.checkClueIsExists(dto);
                this.checkVinCluesRepetition(dto.getVin());
                this.checkVinMust(dto.getVin(),dto.getLicense(),dto.isSourceApp());
                dto.setDealerCode(ownerCode);
                dto.setBookingOrderNo("");
                AccidentCluesPO po = dto.transDtoToPo(AccidentCluesPO.class);
                this.getDealerInfo(po);
                po.setRecordVersion(1);
                po.setCreatedBy(userId);
                po.setCreatedAt(new Date());
                accidentCluesMapper.insert(po);
                //初始化附加表
                this.initExtInfo(po.getAcId(),po.getVin(),po.getLicense(), dto.getCluesResource());
                this.saveAccidentClueContacts(dto.getContactList(), po);
                dto.setAcId(po.getAcId());
                applicationEventPublisher.publishEvent(new AccidentClueInfoPushEvent(Collections.singletonList(dto)));
                // Annotate in the new version of accident clues on 2024-05-17 delete App push
                acId = po.getAcId();
            }
        }catch (Exception e){
            logger.error("创建事故线索失败，userId:{}, 车牌：{}, 报案日期：{},error:{}", userId, dto.getLicense(), dto.getReportDate(), e);
            throw new ServiceBizException(e.getMessage());
        }

            return acId;
    }

    /**
     * 初始化事故线索拓展信息
     */
    private void initExtInfo(Integer acId, String vin,String license, Integer cluesResource) {
        logger.info("init accident clue ext info start :{}",license);
        if (Objects.isNull(acId) || Objects.isNull(license)) {
            logger.info("init ext fail,miss param :{},{}", acId, license);
            return;
        }
        AccidentCluesExtPo ext = AccidentCluesExtPo.builder().acId(acId).sourceChannel("NewBie").channelType("NewBie").build();
        //车主
        PadVehiclePreviewResultVo owner = dmscloudServiceClient.queryOwnerVehicleInterf(new PadVehiclePreviewVo().setLicense(license));
        if (Objects.nonNull(owner)) {
            ext.setOwnerMobile(owner.getMobile()).setOwnerName(owner.getOwnerName());
        }
        //车辆
        if (StrUtil.isEmpty(vin)){
            accidentCluesMapper.update(null,
                new LambdaUpdateWrapper<AccidentCluesPO>().set(AccidentCluesPO::getVin,Objects.isNull(owner)?null:owner.getVin()).eq(AccidentCluesPO::getAcId,acId).eq(AccidentCluesPO::getIsDeleted,
                    0));
        }

        ResponseDTO<Page<VehicleDto>> res = midEndVehicleCenterFeign.queryVehiclePage(
            new VehiclePageQueryDto().setPage(1).setPageSize(10).setData(new VehiclePageQueryDetailDto().setPlateNumber(license)));
        if (Objects.isNull(res) || !Objects.equals(res.getReturnCode(), CommonConstants.ZERO)||Objects.isNull(res.getData())||CollUtil.isEmpty(res.getData().getRecords())) {
            throw new ServiceBizException("查询车辆信息失败");
        }
        logger.info("vehicle info {}:{}", vin, res.getData().getRecords());
        VehicleDto vehicle = res.getData().getRecords().get(0);
        if (Objects.nonNull(vehicle)) {
            ext.setModelName(vehicle.getModelName()).setModelYear(vehicle.getModelYear());
        }
        logger.info("init ext po :{}", ext);
        accidentCluesExtMapper.insert(ext);
        AccidentCluesExt2Po build = AccidentCluesExt2Po.builder().acId(acId)
                .insuranceSource(AccidentCluesInsuranceSourceEnum.fromChineseName(Objects.isNull(cluesResource) ? AccidentCluesInsuranceSourceEnum.OTHER_WAY.getCode() : cluesResource))
                .build();
        accidentCluesExt2Mapper.insert(build);
    }

    /**
     * 短息识别新增逻辑
     * @param param
     * @param ownerCode
     * @param userId
     * @return
     * @throws ServiceBizException
     */
    private Integer saveAutoRecognize(String param, String ownerCode, String userId) throws ServiceBizException{

        AccidentClueVo clueVo = this.getInfoByContent(param);
        logger.info("事故线索识别结果===>>{}", JSONObject.toJSONString(clueVo));
        if (ObjectUtils.isEmpty(clueVo.getLicense())){
            logger.error("车牌信息识别异常！userId:{}, ownerCode:{}", userId, ownerCode);
            throw new ServiceBizException("车牌信息识别异常！");
        }

        Integer count = accidentCluesMapper.checkClueIfExists(clueVo.getLicense(), ownerCode, clueVo.getReportDate());
        if (count != 0){
            throw new ServiceBizException("车牌号【" + clueVo.getLicense() + "】在" + DateUtils.formateDateToString(clueVo.getReportDate(), DateUtils.PATTERN_YYYY_MM_DD) + "（报案时间）已创建过事故线索单据，不能重复创建！");
        }
        AccidentCluesPO po = this.fillCluePo(ownerCode, userId, clueVo);
        accidentCluesMapper.insert(po);
        //初始化附加表
        this.initExtInfo(po.getAcId(),po.getVin(),po.getLicense(), null);
        accidentCluesContactsMapper.saveBatch(ownerCode, Long.parseLong(String.valueOf(po.getAcId())), clueVo.getContactList());

        return po.getAcId();
    }

    /**
     * 填充线索信息
     * @param ownerCode
     * @param userId
     * @param clueVo
     * @return
     */
    private AccidentCluesPO fillCluePo(String ownerCode, String userId, AccidentClueVo clueVo) {

        AccidentCluesPO po = new AccidentCluesPO();
        po.setDealerCode(ownerCode);
        po.setLicense(clueVo.getLicense());
        po.setCluesResource(CommonConstants.CLUES_RESOURCE_BXGSTS);
        po.setAccidentAddress(clueVo.getAccidentAddress());
        po.setReportDate(clueVo.getReportDate());
        po.setInsuranceCompanyId(clueVo.getInsuranceCompanyId());
        po.setInsuranceCompanyName(clueVo.getInsuranceCompanyName());
        po.setCreatedBy(userId);
        this.getDealerInfo(po);

        return po;
    }

    /**
     * 校验线索是否已存在
     * @param dto
     * @throws ServiceBizException
     */
    private void checkClueIsExists(AccidentCluesDTO dto) throws ServiceBizException{

        Integer count = accidentCluesMapper.getCountByLicense(dto.getLicense(), FrameworkUtil.getLoginInfo().getOwnerCode(), dto.getReportDate());
        if (count > 0) {
            throw new ServiceBizException("车牌号【" + dto.getLicense() + "】在" + DateUtils.formateDateToString(dto.getReportDate(), DateUtils.PATTERN_YYYY_MM_DD) + "（报案时间）已创建过事故线索单据，不能重复创建！");
        }
    }

    /**
     * 获取经销商所属大、小区
     * @param po
     * @throws ServiceBizException
     */
    private void getDealerInfo(AccidentCluesPO po) throws ServiceBizException{

        CompanyDetailByCodeDTO companyInfo = complaintDealerCcmRefService.getCompanyInfo(po.getDealerCode());
        // po.setCluesStatus(CommonConstants.CLUES_STATUS_UNFINISHED);
        po.setFollowStatus(CommonConstants.FOLLOW_STATUS_WGJ);
        po.setDealerCode(FrameworkUtil.getLoginInfo().getOwnerCode());
        po.setAfterBigAreaId(companyInfo.getAfterBigAreaId());
        po.setAfterBigAreaName(companyInfo.getAfterBigAreaName());
        po.setAfterSmallAreaId(companyInfo.getAfterSmallAreaId());
        po.setAfterSmallAreaName(companyInfo.getAfterSmallAreaName());
    }

    /**
     * 校验手机号格式
     * @param contacts
     * @throws ServiceBizException
     */
    private void checkContactsMobile(List<AccidentClueContact> contacts) throws ServiceBizException{

        if (CollectionUtils.isEmpty(contacts)){
            logger.info("联系人为空不校验");
            return;
        }
        contacts.forEach(con -> {
            if (!ObjectUtils.isEmpty(con.getIsOwner())
                    && CommonConstants.DICT_IS_YES != con.getIsOwner()
                    && !com.yonyou.dmscus.customer.util.common.StringUtils.isMobile(con.getContactsPhone())){
                logger.info("当前手机号：{} 格式不正确", con.getContactsPhone());
                throw new ServiceBizException("请输入正确手机号");
            }
        });
    }

    /**
     * 保存联系人信息
     * @param contactList
     * @param po
     * @throws ServiceBizException
     */
    private void saveAccidentClueContacts(List<AccidentClueContact> contactList, AccidentCluesPO po) throws ServiceBizException{

        if (CollectionUtils.isEmpty(contactList)){
            logger.info("联系人列表为空！");
            return;
        }
        List<CustomerInfoListReturnDTO> oneIdList = this.syncClueContacts(contactList);
        if (CollectionUtils.isEmpty(oneIdList)){
            logger.info("调用中台接口返回为空，线索id:{}, 手机号：{}", contactList.get(0).getAcId(), contactList.get(0).getContactsPhone());
            return;
        }
        Map<String, List<CustomerInfoListReturnDTO>> oneIdGroup = oneIdList.stream().collect(Collectors.groupingBy(CustomerInfoListReturnDTO::getMobile));
        contactList.forEach(contact -> contact.setOneId(oneIdGroup.get(contact.getContactsPhone()).get(0).getId()));
        this.updateClueInfoContact(contactList, po);
        accidentCluesContactsMapper.saveBatch(po.getDealerCode(), Long.parseLong(String.valueOf(po.getAcId())), contactList);
    }

    /**
     * 更新联系人
     * @param contactList
     * @param po
     * @throws ServiceBizException
     */
    private void updateClueInfoContact(List<AccidentClueContact> contactList, AccidentCluesPO po) throws ServiceBizException{

        //只有车主无联系人，不更新线索信息
        if (contactList.stream().noneMatch(con -> ObjectUtils.nullSafeEquals(con.getIsOwner(), CommonConstants.DICT_IS_NO))){
            return;
        }
        po.setContacts(contactList.get(0).getContacts());
        po.setContactsPhone(contactList.get(0).getContactsPhone());
        po.setOneId(ObjectUtils.isEmpty(contactList.get(0).getOneId()) ? 0 : Integer.parseInt(String.valueOf(contactList.get(0).getOneId())));
        accidentCluesMapper.updateById(po);
    }

    /**
     * 同步中台客户信息
     * @param contacts
     * @return
     */
    private List<CustomerInfoListReturnDTO> syncClueContacts(List<AccidentClueContact> contacts) {

        if (CollectionUtils.isEmpty(contacts)){
            return new ArrayList<>();
        }
        List<CustomerInfoCenterDTO> params = new ArrayList<>();
        contacts.forEach(contact -> params.add(new CustomerInfoCenterDTO(contact.getContacts(), contact.getContactsPhone())));
        logger.info("入参:{}", JSON.toJSONString(params));
        List<CustomerInfoListReturnDTO> oneIdList = businessPlatformService.getOneId(params);
        logger.info("返回:{}", JSON.toJSONString(oneIdList));
        Assert.isTrue(!CollectionUtils.isEmpty(oneIdList), "同步失败！");

        return oneIdList;
    }

    /**
     * 更新线索信息
     * @param clue clue
     * @throws ServiceBizException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAccidentClues(AccidentCluesDTO clue) throws ServiceBizException {

        logger.info("线索修改参数：{}", clue);
        try{
            if (ObjectUtils.isEmpty(clue) || ObjectUtils.isEmpty(clue.getAcId())) {
                logger.info("线索更新参数或id为空");
                throw new ServiceBizException("参数异常！");
            }
            this.checkContactsMobile(clue.getContactList());
            AccidentCluesPO cluePo = accidentCluesMapper.selectById(clue.getAcId());
            if (ObjectUtils.isEmpty(cluePo)){
                logger.info("线索不存在");
                throw new ServiceBizException("数据异常，当前线索不存在！");
            }
            if (!ObjectUtils.isEmpty(clue.getMobile())){
                logger.info("车主手机号不为空，添加车主信息");
                clue.getContactList().add(new AccidentClueContact(clue.getOwnerName(), clue.getMobile(), CommonConstants.DICT_IS_YES));
            }
            this.updateClueContacts(clue, cluePo);
            Integer originFollowUser = cluePo.getFollowPeople();
            this.fillUpdatePo(clue, cluePo);
            accidentCluesMapper.updateById(cluePo);
            applicationEventPublisher.publishEvent(new AccidentClueInfoPushEvent(Collections.singletonList(clue)));
            if (!ObjectUtils.nullSafeEquals(clue.getFollowPeople(), originFollowUser)){
                if (null != clue.getFollowPeople()) {
                    cluePo.setFollowPeople(clue.getFollowPeople());
                    Map<Long, List<AccidentCluesPO>> pushMap = new HashMap<>(CommonConstants.NUM_5);
                    pushMap.put(Long.parseLong(String.valueOf(clue.getFollowPeople())), Collections.singletonList(cluePo));
                    // Annotate in the new version of accident clues on 2024-05-17 delete App push
                }

            }
        }catch (Exception e){

            logger.error("更新线索异常，线索id：{}, error：{}", clue.getAcId(), e);
            throw new ServiceBizException(e.getMessage());
        }
    }

    /**
     * 填充更新内容
     * @param clue
     * @param cluePo
     */
    private void fillUpdatePo(AccidentCluesDTO clue, AccidentCluesPO cluePo){

        cluePo.setLicense(ObjectUtils.isEmpty(clue.getLicense()) ? cluePo.getLicense() : clue.getLicense());
        cluePo.setContacts(ObjectUtils.isEmpty(clue.getContacts()) ? cluePo.getContacts() : clue.getContacts());
        cluePo.setContactsPhone(ObjectUtils.isEmpty(clue.getContactsPhone()) ? cluePo.getContactsPhone() : clue.getContactsPhone());
        cluePo.setReportDate(ObjectUtils.isEmpty(clue.getReportDate()) ? cluePo.getReportDate() : clue.getReportDate());
        cluePo.setFollowPeople(ObjectUtils.isEmpty(clue.getFollowPeople()) ? cluePo.getFollowPeople() : clue.getFollowPeople());
        cluePo.setFollowPeopleName(ObjectUtils.isEmpty(clue.getFollowPeopleName()) ? cluePo.getFollowPeopleName() : clue.getFollowPeopleName());
        cluePo.setFollowStatus(ObjectUtils.isEmpty(clue.getFollowStatus()) ? cluePo.getFollowStatus() : clue.getFollowStatus());
        cluePo.setLicense(ObjectUtils.isEmpty(clue.getLicense()) ? cluePo.getLicense() : clue.getLicense());
        cluePo.setVin(ObjectUtils.isEmpty(clue.getVin()) ? cluePo.getVin() : clue.getVin());
        cluePo.setModelsId(ObjectUtils.isEmpty(clue.getModelsId()) ? cluePo.getModelsId() : clue.getModelsId());
        cluePo.setCluesResource(ObjectUtils.isEmpty(clue.getCluesResource()) ? cluePo.getCluesResource() : clue.getCluesResource());
        cluePo.setOutsideAmount(ObjectUtils.isEmpty(clue.getOutsideAmount()) ? cluePo.getOutsideAmount() : clue.getOutsideAmount());
        cluePo.setReportDate(ObjectUtils.isEmpty(clue.getReportDate()) ? cluePo.getReportDate() : clue.getReportDate());
        cluePo.setAccidentAddress(ObjectUtils.isEmpty(clue.getAccidentAddress()) ? cluePo.getAccidentAddress() : clue.getAccidentAddress());
        cluePo.setCluesType(ObjectUtils.isEmpty(clue.getCluesType()) ? cluePo.getCluesType() : clue.getCluesType());
        cluePo.setIsBruise(ObjectUtils.isEmpty(clue.getIsBruise()) ? cluePo.getIsBruise() : clue.getIsBruise());
        cluePo.setRemark(ObjectUtils.isEmpty(clue.getRemark()) ? cluePo.getRemark() : clue.getRemark());
        cluePo.setAccidentType(ObjectUtils.isEmpty(clue.getAccidentType()) ? cluePo.getAccidentType() : clue.getAccidentType());
        cluePo.setPictures(ObjectUtils.isEmpty(clue.getPictures()) ? cluePo.getPictures() : clue.getPictures());
        cluePo.setBookingOrderNo(ObjectUtils.isEmpty(clue.getBookingOrderNo()) ? cluePo.getBookingOrderNo() : clue.getBookingOrderNo());
        clue.setCluesStatus(cluePo.getCluesStatus());
        if (!ObjectUtils.isEmpty(clue.getInsuranceCompanyId())){
            cluePo.setInsuranceCompanyId(clue.getInsuranceCompanyId());
            cluePo.setInsuranceCompanyName(clue.getInsuranceCompanyName());
        }
    }

    /**
     * 更新联系人
     * @param dto
     * @param cluePo
     * @throws ServiceBizException
     */
    private void updateClueContacts(AccidentCluesDTO dto, AccidentCluesPO cluePo) throws ServiceBizException{

        if (CollectionUtils.isEmpty(dto.getContactList())){
            return;
        }

        accidentCluesContactsMapper.delete(new LambdaQueryWrapper<AccidentCluesContactsPO>().eq(AccidentCluesContactsPO::getAcId, cluePo.getAcId()));
        this.saveAccidentClueContacts(dto.getContactList(), cluePo);
    }

    @Override
    public List<EmpByRoleCodeVO> getDealerUser(GetDealerUserDataDTO getDealerUserDTO, boolean b) throws ServiceBizException{

        if (ObjectUtils.isEmpty(getDealerUserDTO)){
            throw new ServiceBizException("参数异常");
        }
        EmpByRoleCodeVO param = new EmpByRoleCodeVO();
        LoginInfoDto loginInfo = FrameworkUtil.getLoginInfo();
        logger.info("getDealerUserDTO:{}",getDealerUserDTO);
        if(getDealerUserDTO!=null && getDealerUserDTO.getData() != null){
            if(getDealerUserDTO.getData().getIsOnjob() != null) {
                param.setIsOnjob(getDealerUserDTO.getData().getIsOnjob());
            }
            if(CollUtil.isNotEmpty(getDealerUserDTO.getData().getRoleCode())) {
                param.setRoleCode(getDealerUserDTO.getData().getRoleCode());
            }
            if(StrUtil.isNotEmpty(getDealerUserDTO.getData().getEmployeeName())){
                param.setEmployeeName(getDealerUserDTO.getData().getEmployeeName());
            }
        }
        if (getDealerUserDTO!=null && getDealerUserDTO.getData() != null && StrUtil.isNotEmpty(getDealerUserDTO.getData().getCompanyCode())){
            param.setCompanyCode(getDealerUserDTO.getData().getCompanyCode());
        }else {
            param.setCompanyCode(loginInfo.getOwnerCode());
        }
        logger.info("调用中台接口入参:{} {}",param,b);
        List<EmpByRoleCodeVO> responseDealerUserData = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false)
                .convertValue(basicdataCenterService.queryCommon(param,midUrlProperties.getMidEndAuthCenter(), midUrlProperties.getEmpRoleCodeUrl()), new TypeReference<List<EmpByRoleCodeVO>>() {});
        if(CollUtil.isEmpty(responseDealerUserData)){
            return Collections.emptyList();
        }
        if(b){
            responseDealerUserData.forEach(objOrgUser->{
                objOrgUser.setIdcardNumber(SensitiveUtils.idCard(objOrgUser.getIdcardNumber()));
                objOrgUser.setPhone(SensitiveUtils.mobile(objOrgUser.getPhone()));
                objOrgUser.setEmail(SensitiveUtils.email(objOrgUser.getEmail()));
            });
        }

        return this.refreshDealerUserList(responseDealerUserData, loginInfo, getDealerUserDTO.getIsReport());
    }

    /**
     * 根据权限配置筛选返回数据
     * @param responseDealerUserData
     * @param loginInfo
     * @param isReport
     */
    private List<EmpByRoleCodeVO> refreshDealerUserList(List<EmpByRoleCodeVO> responseDealerUserData, LoginInfoDto loginInfo, Boolean isReport){

        if (!ObjectUtils.isEmpty(isReport) && isReport){
            return responseDealerUserData;
        }

        List<EmpByRoleCodeVO> codeVOS = rangeService.hasRangeClueAllotPeople(responseDealerUserData, loginInfo);
        Set<Long> userSet = codeVOS.stream().map(EmpByRoleCodeVO::getUserId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<Map<String, Object>> userAllotCount = accidentCluesMapper.getUserAllotCount(userSet, loginInfo.getOwnerCode());
        for (EmpByRoleCodeVO codeVO : codeVOS) {
            Long userId = codeVO.getUserId();
            if(userId==null){
                continue;
            }
            Optional<Map<String, Object>> first = userAllotCount.stream().filter(e -> userId.equals(Long.parseLong(e.get("follow_people").toString()))).findFirst();
            if(first.isPresent()){
                Map<String, Object> map = first.get();
                codeVO.setNowClueCount(Integer.valueOf(map.get("ct").toString()));
            }
        }

        return codeVOS;
    }

    @Override
    public AccidentClueVo getInfoByContent(String param) {
        String s = param.replaceAll(" ", "")
                .replaceAll("\n", "")
                .replaceAll("：", ":")
                .replaceAll("，", ",");
        return parseService.parseContent(s);
    }


    /**
     * 获取线索列表
     * @param params params
     * @return
     * @throws ServiceBizException
     */
    @Override
    public IPage<AccidentClueVO> getList(AccidentClueVO params) throws ServiceBizException {
        logger.info("accident clue app page query getList:{}",params);
        LoginInfoDto loginInfo = FrameworkUtil.getLoginInfo();
        String ownerCode = loginInfo.getOwnerCode();
        params.setDealerCode(ownerCode);
        this.fillUserRange(params, loginInfo);
        List<Integer> contactAcIds = this.findContactAcIds(params.getSubParam());
        List<Integer> followStatusList = params.getFollowStatusList();
        String orderByStr = CommonConstants.ORDER_BY_STR;
        if(CollectionUtils.isNotEmpty(followStatusList)) {
            Integer followStatus = params.getFollowStatusList().get(0);
            if(followStatus == CommonConstants.NOT_FOLLOW
                    || followStatus == CommonConstants.FOLLOW_TIME_OUT
                    || followStatus == CommonConstants.ALL_FOLLOW_STATUS) {
                orderByStr = "tt.created_at desc";
            }else if(followStatus == CommonConstants.KEEP_ON_FOLLOW) {
                orderByStr = "tt.next_follow_date asc";
            }
            if(followStatus == CommonConstants.ALL_FOLLOW_STATUS) {
                // 全部
                params.setFollowStatusList(null);
            }
        }
        Page page = new Page<>(params.getPageNum(), params.getLimit());
        IPage<AccidentClueVO> cluePage = accidentCluesMapper.getList(page, params, params.getUserList(), contactAcIds, limitDate, orderByStr);
        if (CollectionUtils.isEmpty(cluePage.getRecords())){
            return cluePage;
        }

        List<Long> acIdList = cluePage.getRecords().stream().map(AccidentClueVO::getAcId).collect(Collectors.toList());
        List<AccidentClueContact> contacts = accidentCluesContactsMapper.getAccidentClueContacts(ownerCode, acIdList, CommonConstants.DICT_IS_NO);
        if (CollectionUtils.isEmpty(contacts)){
            return cluePage;
        }

        Map<Long, List<AccidentClueContact>> contactsGroup = contacts.stream().collect(Collectors.groupingBy(AccidentClueContact::getAcId));
        cluePage.getRecords().forEach(clue -> clue.setContactList(contactsGroup.get(clue.getAcId())));
        return cluePage;
    }

    /**
     * 联系人匹配线索id结果
     * @param param
     * @return
     */
    private List<Integer> findContactAcIds(String param){

        if (ObjectUtils.isEmpty(param)){
            return new ArrayList<>();
        }
        return accidentCluesContactsMapper.queryAcIdByContactInfo(param);
    }

    /**
     * 填充数据访问范围权限列表
     * @param params
     * @param loginInfo
     * @throws ServiceBizException
     */
    private void fillUserRange(AccidentClueVO params, LoginInfoDto loginInfo) throws ServiceBizException{

        Integer rangeId = rangeService.getRangeControlFromMid(menuId);
        if(rangeId!=0){
            params.setUserList(rangeService.hasRangeAccidentClueList(rangeId,loginInfo));
        }
    }

    /**
     * 跟进历史
     * @param acId id
     * @return
     * @throws ServiceBizException
     */
    @Override
    public List<AccidentCluesFollowDTO> getFollowList(Integer acId) throws ServiceBizException {

        return accidentCluesFollowMapper.getFollowList(acId);
    }

    /**
     * 跟进提醒
     */
    @Override
    public void followRemind() {

        //跟进逾期十分钟提醒
        List<AccidentCluesPO> notFollowList = accidentCluesMapper.selectTimeoutNotFollow();
        logger.info("逾期跟进提醒列表 ===>> {}", notFollowList);
        appPushService.followRemindAppPush(notFollowList, null, AccidentClueAppPushEnum.PUSH_TYPE_TIMEOUT_REMIND);
        if (!CollectionUtils.isEmpty(notFollowList)){
            accidentCluesMapper.updateClueRemind(notFollowList.stream().map(AccidentCluesPO::getAcId).collect(Collectors.toList()));
        }

        //跟进提前30分钟提醒
        List<AccidentCluesPO> advanceFollowList = accidentCluesMapper.advanceFollowList();
        logger.info("跟进提前30分钟提醒列表 ===>> {}", advanceFollowList);
        appPushService.followRemindAppPush(advanceFollowList, null, AccidentClueAppPushEnum.PUSH_TYPE_BEFORE_FOLLOW);
    }

    /**
     * 超时未进厂提醒
     */
    @Override
    public void appointmentTimeOutRemind() {

        List<AccidentCluesPO> appointmentTimeOutList = accidentCluesMapper.getAppointmentTimeOutList();
        logger.info("预约超时未进厂提醒列表 ===>> {}", appointmentTimeOutList);
        appPushService.followRemindAppPush(appointmentTimeOutList, null, AccidentClueAppPushEnum.PUSH_TYPE_NOT_COMING);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiveClueList(List<AccidentCluesDTO> clueList) throws ServiceBizException {

        logger.info("接收推送线索列表 ===>> {}", JSONObject.toJSONString(clueList));
        if (CollectionUtils.isEmpty(clueList)){
            return;
        }
        for (AccidentCluesDTO clue : clueList) {
            try {
                Assert.isTrue(!ObjectUtils.isEmpty(clue.getLicense()), "车牌号为空！");
                Integer count = accidentCluesMapper.getCountByLicense(clue.getLicense(), clue.getDealerCode(), clue.getReportDate());
                Assert.isTrue(count == 0, "接收推送线索已存在");
            }catch (Exception e){
                logger.info("线索：{}保存失败，原因：{}", clue, e.getMessage());
                continue;
            }
            AccidentCluesPO po = clue.transDtoToPo(AccidentCluesPO.class);
            po.setCluesStatus(CommonConstants.CLUES_STATUS_UNFINISHED);
            po.setFollowStatus(CommonConstants.FOLLOW_STATUS_WGJ);
            accidentCluesMapper.insert(po);
            this.saveAccidentClueContacts(clue.getContactList(), po);
        }
    }

    /**
     * 事故线索-预约单保存
     *
     * @param dto
     * @return
     */
    @Override
    public BookingOrderReturnVo saveAppointmentOrder(AccidentCluesDTO dto) throws ServiceBizException{

        BookingOrderParamsVo paramsVo = getBookingOrderParamsVo(dto);
        logger.info("事故线索-预约单保存,入参：【{}】", JSONUtil.toJsonStr(paramsVo));

        RestResultResponse<BookingOrderReturnVo> response = null;
        try {
            response = repairCommonClient.saveBookingPart(paramsVo);
            logger.info("预约单创建结果===>>{}", response.toString());
            Assert.isTrue(!ObjectUtils.isEmpty(response), "预约单创建失败！");
            Assert.isTrue(ObjectUtils.nullSafeEquals(response.getResultCode(), HttpStatus.OK.value()), response.getErrMsg());
            return response.getData();
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            throw new ServiceBizException(e.getMessage());
        }
    }

    private BookingOrderParamsVo getBookingOrderParamsVo(AccidentCluesDTO dto) {

        LoginInfoDto loginInfo = FrameworkUtil.getLoginInfo();
        String ownerCode = loginInfo.getOwnerCode();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        BookingOrderParamsVo paramsVo = new BookingOrderParamsVo();
        paramsVo.setIsAccidentClues(CommonConstants.DICT_IS_YES);
        paramsVo.setAcId(dto.getAcId());
        paramsVo.setBookingComeTime(null == dto.getIntoDealerDate() ? null : sdf.format(dto.getIntoDealerDate()));  // 预约进厂时间
        paramsVo.setContactorName(dto.getContacts());  // 预约人
        paramsVo.setContactorMobile(dto.getContactsPhone());  // 预约人手机
        paramsVo.setContactorPhone(dto.getContactsPhone());   // 预约人电话
        paramsVo.setBookingSource(dto.getBookingSource());    // 预约来源
        paramsVo.setServiceAdvisor(dto.getServiceAdvisor());  // 服务顾问
        paramsVo.setCustomerDesc(dto.getCustomerDesc());  // 客户需求
        paramsVo.setLicense(dto.getLicense());  // 车牌
        paramsVo.setVin(dto.getVin());   // VIN
        paramsVo.setRepairCategoryCode(CommonConstants.REPAIR_CATEGORY_ACCIDENT);  // 预约维修类型
        paramsVo.setBookingTypeCode(CommonConstants.BOOKING_TYPE_PASSIVELY);  // 预约类别
        paramsVo.setBookingOrderNo(dto.getBookingOrderNo()); // 预约单号
        paramsVo.setAcceptBookingDate(sdf.format(new Date()));  // 预约登记时间
        // paramsVo.setOwnerOneId();  // 车主oneId

        // 查询车辆相相关信息
        VehicleQueryParamsDTO vehicleQueryParams = new VehicleQueryParamsDTO();
        vehicleQueryParams.setLicense(dto.getLicense());
        vehicleQueryParams.setVin(dto.getVin());
        List<VehicleBasicInfoVo> vehicleList = repairCommonClient.queryVehicleBasicInfoList(vehicleQueryParams);
        if(CollectionUtils.isEmpty(vehicleList)){
            throw new ServiceBizException("车辆未维护,请在Newbie新增");
        }
        logger.info("通过车牌号：{} vin:{} 查询到的车辆信息：【{}】", dto.getLicense(), dto.getVin(), JSONUtil.toJsonStr(vehicleList));
        VehicleBasicInfoVo target = vehicleList.get(0);
        paramsVo.setBrand(target.getBrand());  // 品牌
        paramsVo.setSeries(target.getSeries());  // 车系
        paramsVo.setModel(target.getModel());  // 车型
        paramsVo.setConfig(target.getApackage());  // 配置
        paramsVo.setYearModel(target.getYearModel());   // 年款
        paramsVo.setInMileage(target.getMileage());  // 进厂行驶里程
        paramsVo.setModelCode(target.getModelCode());  // 车型代码

        AccidentCluesContactsPO accidentCluesContactsPO = getAccidentCluesContacts(dto.getAcId(), dto.getContactsPhone(), null, CommonConstants.DICT_IS_NO);
        if (null != accidentCluesContactsPO) {
            paramsVo.setOneId(null == accidentCluesContactsPO.getOneId() ? null : accidentCluesContactsPO.getOneId().intValue());  // 联系人oneId
        }
        AccidentCluesContactsPO vehicleOwner = getAccidentCluesContacts(dto.getAcId(), null, ownerCode, CommonConstants.DICT_IS_YES);
        if (null != vehicleOwner) {
            paramsVo.setOwnerName(vehicleOwner.getContacts());  // 车主姓名
        }

        return paramsVo;
    }

    /**
     * 获取车辆联系人或车主信息
     * @param acId  事故线索id
     * @param contactsPhone  联系人电话
     * @param dealerCode  经销商code
     * @param isOwner 是否车主  10041001-是   10041002-否
     * @return
     */
    private AccidentCluesContactsPO getAccidentCluesContacts(Integer acId, String contactsPhone, String dealerCode, Integer isOwner) {

        QueryWrapper<AccidentCluesContactsPO> queryWrapper = new QueryWrapper();
        if (null != acId) {
            queryWrapper.lambda().eq(AccidentCluesContactsPO::getAcId, acId);
        }

        if (org.apache.commons.lang.StringUtils.isNotBlank(contactsPhone)) {
            queryWrapper.lambda().eq(AccidentCluesContactsPO::getContactsPhone, contactsPhone);
        }

        if (org.apache.commons.lang.StringUtils.isNotBlank(dealerCode)) {
            queryWrapper.lambda().eq(AccidentCluesContactsPO::getDealerCode, dealerCode);
        }

        if (null != isOwner) {
            queryWrapper.lambda().eq(AccidentCluesContactsPO::getIsOwner, isOwner);
        }

        AccidentCluesContactsPO obj = accidentCluesContactsMapper.selectOne(queryWrapper);

        return obj;

    }




    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateContactInfo(AccidentClueContact contact) throws ServiceBizException {

        if (ObjectUtils.isEmpty(contact) || ObjectUtils.isEmpty(contact.getAcId())){
            throw new ServiceBizException("参数异常！");
        }
        this.checkContactsMobile(ObjectUtils.isEmpty(contact.getContactsPhone()) ? new ArrayList<>() : Collections.singletonList(contact));
        AccidentCluesPO cluePo = accidentCluesMapper.selectOne(new LambdaQueryWrapper<AccidentCluesPO>().eq(AccidentCluesPO::getAcId, contact.getAcId()));
        if (ObjectUtils.isEmpty(cluePo)){
            throw new ServiceBizException("线索不存在！");
        }
        AccidentCluesContactsPO contactPo = accidentCluesContactsMapper.selectOne(new LambdaQueryWrapper<AccidentCluesContactsPO>()
                .eq(AccidentCluesContactsPO::getId, contact.getId()));
        if (!ObjectUtils.isEmpty(contactPo)){
            contact.setOriginalContacts(contactPo.getContacts());
            contact.setOriginalContactsPhone(contactPo.getContactsPhone());
            contactPo.setContacts(ObjectUtils.isEmpty(contact.getContacts()) ? contactPo.getContacts() : contact.getContacts());
            contactPo.setContactsPhone(ObjectUtils.isEmpty(contact.getContactsPhone()) ? contactPo.getContactsPhone() : contact.getContactsPhone());
            accidentCluesContactsMapper.updateById(contactPo);
        }else {
            accidentCluesContactsMapper.saveBatch(FrameworkUtil.getLoginInfo().getOwnerCode(), contact.getAcId(), Collections.singletonList(contact));
        }
        this.syncClueInfoContact(contact, cluePo);
    }

    /**
     * 同步线索主单联系人信息
     * @param contact
     * @param cluePo
     */
    private void syncClueInfoContact(AccidentClueContact contact, AccidentCluesPO cluePo) {

        if (!ObjectUtils.nullSafeEquals(contact.getOriginalContacts(), cluePo.getContacts()) || !ObjectUtils.nullSafeEquals(contact.getOriginalContactsPhone(), cluePo.getContactsPhone())){
            return;
        }
        cluePo.setContacts(ObjectUtils.isEmpty(contact.getContacts()) ? cluePo.getContacts() : contact.getContacts());
        cluePo.setContactsPhone(ObjectUtils.isEmpty(contact.getContactsPhone()) ? cluePo.getContactsPhone() : contact.getContactsPhone());
        accidentCluesMapper.updateById(cluePo);
    }

    @Override
    public AccidentClueDashBoardVO queryAccidentClueDashboard(DashBoardQueryDTO dto) throws ServiceBizException {

        logger.info("事故线索看板查询参数：{}", dto);
        if (ObjectUtils.isEmpty(dto)){
            return new AccidentClueDashBoardVO();
        }
        Date begin;
        Date end;
        Long userId = FrameworkUtil.getLoginInfo().getUserId();
        String ownerCode = FrameworkUtil.getLoginInfo().getOwnerCode();
        try {
            begin = DateUtils.parse(dto.getBeginDate(), DateUtils.PATTERN_YYYY_MM_DD);
            end = DateUtils.parse(dto.getEndDate(), DateUtils.PATTERN_YYYY_MM_DD);
        }catch (Exception e){
            throw new ServiceBizException("日期格式异常！");
        }
        AccidentClueDashBoardVO result = new AccidentClueDashBoardVO();
        Integer rangeId = rangeService.getRangeControlFromMid(menuId);
        //本人，个人看板
        if(RangeEnum.RANGE_OWNER.getRangeCode().equals(rangeId)){
            result = this.findOwnerDashBoard(ownerCode, userId, dto, begin, end);
        }
        //所有，门店看板
        if (RangeEnum.RANGE_ALL.getRangeCode().equals(rangeId) || 0 == rangeId){
            result = this.findDealerDashBoard(ownerCode, dto, begin, end);
        }

        logger.info("看板查询返回结果===>>{}", result);
        return result;
    }

    /**
     * 获取门店看板
     * @param ownerCode
     * @param dto
     * @param begin
     * @param end
     * @return
     * @throws ServiceBizException
     */
    private AccidentClueDashBoardVO findDealerDashBoard(String ownerCode, DashBoardQueryDTO dto, Date begin, Date end) throws ServiceBizException{

        AccidentClueDashBoardVO dashBoardInfo = accidentCluesMapper.queryDealerDashBoardInfo(ownerCode, dto.getBeginDate(), dto.getEndDate());
        dashBoardInfo = this.fillDashBoard(dashBoardInfo);
        //获取周、月环比
        this.queryDealerWowAndMom(dashBoardInfo, dto, begin, end, ownerCode);

        //根据差值<180天返回柱状图纬度
        long diffDay = DateUtil.betweenDay(begin, end, true);
        List<HistogramDataVO> detailList;
        if (ACDashBoardQueryEnum.QUERY_TYPE_USER.getType().equals(dto.getQueryType())){
            detailList = accidentCluesMapper.queryDashBoardUserDetail(ownerCode, dto.getBeginDate(), dto.getEndDate());
            this.refreshDetailList(dashBoardInfo, detailList);
            return dashBoardInfo;
        }
        if (diffDay < CommonConstants.NUM_180){
            detailList = accidentCluesMapper.queryDealerDashBoardDayDetail(ownerCode, dto.getBeginDate(), dto.getEndDate());
        }else {
            detailList = accidentCluesMapper.queryDealerDashBoardMonthDetail(ownerCode, dto.getBeginDate(), dto.getEndDate());
        }
        //处理看板数据返回给前端使用
        this.refreshDetailList(dashBoardInfo, detailList);

        return dashBoardInfo;
    }

    /**
     * 获取个人看板
     * @param ownerCode
     * @param userId
     * @param dto
     * @param begin
     * @param end
     * @return
     * @throws ServiceBizException
     */
    private AccidentClueDashBoardVO findOwnerDashBoard(String ownerCode, Long userId, DashBoardQueryDTO dto, Date begin, Date end) throws ServiceBizException{

        AccidentClueDashBoardVO dashBoardInfo = accidentCluesMapper.queryDashBoardInfo(ownerCode, userId, dto.getBeginDate(), dto.getEndDate());
        dashBoardInfo = this.fillDashBoard(dashBoardInfo);
        //获取周、月环比
        this.queryWowAndMom(dashBoardInfo, dto, begin, end, ownerCode, userId);

        //根据差值<180天返回柱状图纬度
        long diffDay = DateUtil.betweenDay(begin, end, true);
        List<HistogramDataVO> detailList;
        if (diffDay < CommonConstants.NUM_180){
            detailList = accidentCluesMapper.queryDashBoardDayDetail(ownerCode, userId,  dto.getBeginDate(), dto.getEndDate());
        }else {
            detailList = accidentCluesMapper.queryDashBoardMonthDetail(ownerCode, userId,  dto.getBeginDate(), dto.getEndDate());
        }
        //处理看板数据返回给前端使用
        this.refreshDetailList(dashBoardInfo, detailList);

        return dashBoardInfo;
    }

    /**
     * 获取门店周、月环比
     * @param dashBoardInfo
     * @param dto
     * @param beginDate
     * @param endDate
     * @param ownerCode
     * @throws ServiceBizException
     */
    private void queryDealerWowAndMom(AccidentClueDashBoardVO dashBoardInfo, DashBoardQueryDTO dto, Date beginDate, Date endDate, String ownerCode) throws ServiceBizException{

        AccidentClueDashBoardVO dashBoardBeforeWeek = null;
        AccidentClueDashBoardVO dashBoardBeforeMonth = null;
        //周环比、月环比
        if (this.checkDateBelongWeekOrMonth(beginDate, endDate, true)){
            dashBoardInfo.setFollowUpWow(INIT_WOW_MOM_VALUE).setNotFollowWow(INIT_WOW_MOM_VALUE).setSuccessFollowWow(INIT_WOW_MOM_VALUE).setFollowFailWow(INIT_WOW_MOM_VALUE);
            dashBoardBeforeWeek = accidentCluesMapper.queryDealerDashBoardInfo(ownerCode, this.findDateBeforeWeekOrMonth(beginDate, Calendar.WEEK_OF_MONTH), this.findDateBeforeWeekOrMonth(endDate, Calendar.WEEK_OF_MONTH));
        }
        //月环比
        if (this.checkDateBelongWeekOrMonth(beginDate, endDate, false)){
            dashBoardInfo.setFollowUpMom(INIT_WOW_MOM_VALUE).setNotFollowMom(INIT_WOW_MOM_VALUE).setSuccessFollowMom(INIT_WOW_MOM_VALUE).setFollowFailMom(INIT_WOW_MOM_VALUE);
            dashBoardBeforeMonth = accidentCluesMapper.queryDealerDashBoardInfo(ownerCode, this.findDateBeforeWeekOrMonth(beginDate, Calendar.MONDAY), this.findDateBeforeWeekOrMonth(endDate, Calendar.MONDAY));
        }
        this.fillWowAndMom(dashBoardInfo, dashBoardBeforeWeek, dashBoardBeforeMonth);
    }

    /**
     * 处理看板数据
     * @param dashBoardInfo
     * @return
     */
    private AccidentClueDashBoardVO fillDashBoard(AccidentClueDashBoardVO dashBoardInfo){

        if (!ObjectUtils.isEmpty(dashBoardInfo)){
            BigDecimal totalCount = new BigDecimal(String.valueOf(dashBoardInfo.getTotalCount()));
            dashBoardInfo.setNotFollowRate(0 == dashBoardInfo.getNotFollowCount()
                    ? BigDecimal.ZERO : new BigDecimal(String.valueOf(dashBoardInfo.getNotFollowCount())).divide(totalCount, 4, RoundingMode.HALF_UP));
            dashBoardInfo.setFollowUpRate(0 == dashBoardInfo.getFollowUpCount()
                    ? BigDecimal.ZERO : new BigDecimal(String.valueOf(dashBoardInfo.getFollowUpCount())).divide(totalCount, 4, RoundingMode.HALF_UP));
            dashBoardInfo.setSuccessFollowRate(0 == dashBoardInfo.getSuccessFollowCount()
                    ? BigDecimal.ZERO : new BigDecimal(String.valueOf(dashBoardInfo.getSuccessFollowCount())).divide(totalCount, 4, RoundingMode.HALF_UP));
            dashBoardInfo.setFollowFailRate(0 == dashBoardInfo.getFollowFailCount()
                    ? BigDecimal.ZERO : new BigDecimal(String.valueOf(dashBoardInfo.getFollowFailCount())).divide(totalCount, 4, RoundingMode.HALF_UP));
            return dashBoardInfo;
        }
        return AccidentClueDashBoardVO.buildEmptyVo();
    }

    /**
     * 处理看板柱状图数据
     * @param dashBoardInfo
     * @param detailList
     */
    private void refreshDetailList(AccidentClueDashBoardVO dashBoardInfo, List<HistogramDataVO> detailList){

        this.fillDashBoardHistogramDataVo(dashBoardInfo, detailList);
        if (CollectionUtils.isEmpty(detailList)){
            return;
        }

        BigDecimal rate = BigDecimal.TEN.multiply(BigDecimal.TEN);
        detailList.forEach(detail -> {
            BigDecimal unInsure = new BigDecimal(String.valueOf(detail.getUnInsuranceCount()));
            BigDecimal unInsuranceRepair = new BigDecimal(String.valueOf(detail.getUnInsuranceRepairCount()));
            BigDecimal insure = new BigDecimal(String.valueOf(detail.getIsInsuranceCount()));
            BigDecimal insuranceRepair = new BigDecimal(String.valueOf(detail.getIsInsuranceRepairCount()));
            dashBoardInfo.getHistogramDateList().add(detail.getHistogramDate());
            dashBoardInfo.getFollowPeopleNameList().add(detail.getFollowPeopleName());
            dashBoardInfo.getSuccessCountList().add(detail.getSuccessCount());
            dashBoardInfo.getFailCountList().add(detail.getFailCount());
            dashBoardInfo.getRepairRateList().add(BigDecimal.ZERO.compareTo(unInsure) == 0
                    ? BigDecimal.ZERO : unInsuranceRepair.divide(unInsure, 4, RoundingMode.HALF_UP).multiply(rate).setScale(2, RoundingMode.HALF_UP));
            dashBoardInfo.getBackRepairRateList().add(BigDecimal.ZERO.compareTo(insure) == 0
                    ? BigDecimal.ZERO : insuranceRepair.divide(insure, 4, RoundingMode.HALF_UP).multiply(rate).setScale(2, RoundingMode.HALF_UP));
        });
    }

    /**
     * 填充柱状图列
     * @param dashBoardInfo
     * @param detailList
     */
    private void fillDashBoardHistogramDataVo(AccidentClueDashBoardVO dashBoardInfo, List<HistogramDataVO> detailList){

        int capacity = CollectionUtils.isEmpty(detailList) ? CommonConstants.NUM_1 : detailList.size();
        dashBoardInfo.initHistogramData(dashBoardInfo, capacity);
    }

    /**
     * 计算个人周环比、月环比
     * @param dashBoardInfo
     * @param dto
     * @param beginDate
     * @param endDate
     * @param ownerCode
     * @param userId
     * @throws ServiceBizException
     */
    private void queryWowAndMom(AccidentClueDashBoardVO dashBoardInfo, DashBoardQueryDTO dto, Date beginDate, Date endDate, String ownerCode, Long userId) throws ServiceBizException{

        AccidentClueDashBoardVO dashBoardBeforeWeek = null;
        AccidentClueDashBoardVO dashBoardBeforeMonth = null;

        //周环比、月环比
        if (this.checkDateBelongWeekOrMonth(beginDate, endDate, true)){
            dashBoardInfo.setFollowUpWow(INIT_WOW_MOM_VALUE).setNotFollowWow(INIT_WOW_MOM_VALUE).setSuccessFollowWow(INIT_WOW_MOM_VALUE).setFollowFailWow(INIT_WOW_MOM_VALUE);
            dashBoardBeforeWeek = accidentCluesMapper.queryDashBoardInfo(ownerCode, userId, this.findDateBeforeWeekOrMonth(beginDate, Calendar.WEEK_OF_MONTH), this.findDateBeforeWeekOrMonth(endDate, Calendar.WEEK_OF_MONTH));
        }
        //月环比
        if (this.checkDateBelongWeekOrMonth(beginDate, endDate, false)){
            dashBoardInfo.setFollowUpMom(INIT_WOW_MOM_VALUE).setNotFollowMom(INIT_WOW_MOM_VALUE).setSuccessFollowMom(INIT_WOW_MOM_VALUE).setFollowFailMom(INIT_WOW_MOM_VALUE);
            dashBoardBeforeMonth = accidentCluesMapper.queryDashBoardInfo(ownerCode, userId, this.findDateBeforeWeekOrMonth(beginDate, Calendar.MONDAY), this.findDateBeforeWeekOrMonth(endDate, Calendar.MONDAY));
        }
        this.fillWowAndMom(dashBoardInfo, dashBoardBeforeWeek, dashBoardBeforeMonth);
    }

    /**
     * 填充周、月环比值
     * @param dashBoardInfo
     * @param dashBoardBeforeWeek
     * @param dashBoardBeforeMonth
     */
    private void fillWowAndMom(AccidentClueDashBoardVO dashBoardInfo, AccidentClueDashBoardVO dashBoardBeforeWeek, AccidentClueDashBoardVO dashBoardBeforeMonth){

        if (!ObjectUtils.isEmpty(dashBoardBeforeWeek)){
            dashBoardInfo.setNotFollowWow(this.calculatePercent(dashBoardInfo.getNotFollowCount() - dashBoardBeforeWeek.getNotFollowCount(), dashBoardBeforeWeek.getNotFollowCount()));
            dashBoardInfo.setFollowUpWow(this.calculatePercent(dashBoardInfo.getFollowUpCount() - dashBoardBeforeWeek.getFollowUpCount(), dashBoardBeforeWeek.getFollowUpCount()));
            dashBoardInfo.setSuccessFollowWow(this.calculatePercent(dashBoardInfo.getSuccessFollowCount() - dashBoardBeforeWeek.getSuccessFollowCount(), dashBoardBeforeWeek.getSuccessFollowCount()));
            dashBoardInfo.setFollowFailWow(this.calculatePercent(dashBoardInfo.getFollowFailCount() - dashBoardBeforeWeek.getFollowFailCount(), dashBoardBeforeWeek.getFollowFailCount()));
        }
        if (!ObjectUtils.isEmpty(dashBoardBeforeMonth)){
            dashBoardInfo.setNotFollowMom(this.calculatePercent(dashBoardInfo.getNotFollowCount() - dashBoardBeforeMonth.getNotFollowCount(), dashBoardBeforeMonth.getNotFollowCount()));
            dashBoardInfo.setFollowUpMom(this.calculatePercent(dashBoardInfo.getFollowUpCount() - dashBoardBeforeMonth.getFollowUpCount(), dashBoardBeforeMonth.getFollowUpCount()));
            dashBoardInfo.setSuccessFollowMom(this.calculatePercent(dashBoardInfo.getSuccessFollowCount() - dashBoardBeforeMonth.getSuccessFollowCount(), dashBoardBeforeMonth.getSuccessFollowCount()));
            dashBoardInfo.setFollowFailMom(this.calculatePercent(dashBoardInfo.getFollowFailCount() - dashBoardBeforeMonth.getFollowFailCount(), dashBoardBeforeMonth.getFollowFailCount()));
        }
    }

    /**
     * 获取两数百分比
     * @param divisor
     * @param dividend
     * @return
     */
    private String calculatePercent(Integer divisor, Integer dividend){

        BigDecimal multiplyValue = BigDecimal.TEN.multiply(BigDecimal.TEN);
        BigDecimal divisorValue = new BigDecimal(String.valueOf(divisor));
        BigDecimal dividendValue = new BigDecimal(String.valueOf(dividend));
        if (BigDecimal.ZERO.compareTo(dividendValue) == 0){
            return INIT_WOW_MOM_VALUE;
        }
        BigDecimal divide = divisorValue.divide(dividendValue, 2, RoundingMode.HALF_UP);
        BigDecimal resultValue = divide.multiply(multiplyValue);
        int result = resultValue.intValue();
        return result + "%";
    }

    /**
     * 获取前x天日期
     * @param date
     * @param field
     * @return
     * @throws ServiceBizException
     */
    private String findDateBeforeWeekOrMonth(Date date, int field) throws ServiceBizException{

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(field, -1);

        return DateUtils.formatDate(calendar.getTime(), DateUtils.PATTERN_YYYY_MM_DD);
    }

    /**
     * 校验日期是否属于当前周、当前月
     * @param beginDate
     * @param endDate
     * @param isWeek
     * @return
     */
    private Boolean checkDateBelongWeekOrMonth(Date beginDate, Date endDate, boolean isWeek){

        Calendar calendar = Calendar.getInstance();
        if (isWeek){
            calendar.setTime(beginDate);
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            int beginDateWeek = calendar.get(Calendar.WEEK_OF_YEAR);
            calendar.setTime(endDate);
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            int endDateWeek = calendar.get(Calendar.WEEK_OF_YEAR);
            return beginDateWeek == endDateWeek;
        }
        calendar.setTime(beginDate);
        int beginDateMonth = calendar.get(Calendar.MONTH);
        calendar.setTime(endDate);
        int endDateMonth = calendar.get(Calendar.MONTH);

        return beginDateMonth == endDateMonth;
    }



    /**
     * 1.本店内48H内有相同VIN的线索即不可重复创建。
     * 如重复，则提示：48H内此车辆已有重复线索。
     */
    private void checkVinCluesRepetition(String vin){
        logger.info("checkVinCluesRepetition vin:{},ownerCode:{}",vin,FrameworkUtil.getLoginInfo().getOwnerCode());
        if (StringUtils.isNullOrEmpty(vin)){
            logger.info("checkVinCluesRepetition vin isNullOrEmpty");
            return;
        }
        //当前时间+-48小时
        String hoursAgo = LocalDateTime.now().minusHours(48).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String plusAgo = LocalDateTime.now().plusHours(48).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        logger.info("checkVinCluesRepetition hoursAgo:{}",hoursAgo);
        LambdaQueryWrapper<AccidentCluesPO> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(AccidentCluesPO::getDealerCode,FrameworkUtil.getLoginInfo().getOwnerCode());
        queryWrapper.eq(AccidentCluesPO::getVin,vin);
        queryWrapper.eq(AccidentCluesPO::getIsDeleted,"0");
        queryWrapper.ge(AccidentCluesPO::getCreatedAt,hoursAgo);
        queryWrapper.le(AccidentCluesPO::getCreatedAt,plusAgo);
        Integer count = accidentCluesMapper.selectCount(queryWrapper);
        logger.info("checkVinCluesRepetition count:{}",count);
        if (count>0){
            throw new ServiceBizException("48H内此车辆已有重复线索");
        }
    }

    /**
     * vin必填项
     * 1.如果是app则校验vin是否为空
     * 2.如果是pc端则校验车牌号进行VIN获取，如果获取不到则也视为无VIN拦截
     */
    private void checkVinMust(String vin, String license,boolean sourceApp){
        logger.info("checkVinMust vin:{},license:{},sourceApp:{}",vin,license,sourceApp);
        if (sourceApp){
            logger.info("checkVinMust app渠道");
            if (StringUtils.isNullOrEmpty(vin)){
                throw new ServiceBizException("车架号不能为空");
            }
        }else {
            logger.info("checkVinMust pc渠道");
            if (StringUtils.isNullOrEmpty(license)){
                throw new ServiceBizException("车牌号不能为空");
            }
            PadVehiclePreviewVo padVehiclePreviewVo=new PadVehiclePreviewVo();
            padVehiclePreviewVo.setLicense(license);
            logger.info("checkVinMust padVehiclePreviewVo {}",padVehiclePreviewVo);
            PadVehiclePreviewResultVo padVehiclePreviewResultVo = dmscloudServiceClient.queryOwnerVehicleInterf(padVehiclePreviewVo);
            logger.info("checkVinMust padVehiclePreviewResultVo:{}",padVehiclePreviewResultVo);
            if (Objects.isNull(padVehiclePreviewResultVo) || StringUtils.isNullOrEmpty(padVehiclePreviewResultVo.getVin())){
                throw new ServiceBizException("系统暂无此车辆，请先维护");
            }
        }
    }

    /**
     * 推送状态到litecrm
     */
    @Override
    public void pushLiteCrmClueStatus(List<StatusChangePushDTO> pushInfoList) {
        logger.info("pushLiteCrmClueStatus start");
        if (CollectionUtils.isEmpty(pushInfoList)) {
            logger.info("pushLiteCrmClueStatus pushInfoList isEmpty");
            return;
        }
        // 发起事件推送litecrm
        applicationEventPublisher.publishEvent(new AccidentClueStatusChangeEvent(pushInfoList));
    }

    @Override
    public List<AccidentClueFollowVo> followCount(AccidentClueVO params) {
        logger.info("accident clue follow count param:{}",params);
        LoginInfoDto loginInfo = FrameworkUtil.getLoginInfo();
        String ownerCode = loginInfo.getOwnerCode();
        params.setDealerCode(ownerCode);
        this.fillUserRange(params, loginInfo);
        List<Integer> contactAcIds = this.findContactAcIds(params.getSubParam());
        List<Integer> followStatusList = params.getFollowStatusList();
        if (CollectionUtils.isNotEmpty(followStatusList)) {
            Integer followStatus = params.getFollowStatusList().get(0);
            if (followStatus == CommonConstants.ALL_FOLLOW_STATUS) {
                // 全部
                params.setFollowStatusList(null);
            }
        }
        AccidentClueFollowVo accidentClueFollowVo = accidentCluesMapper.followCount(params, contactAcIds, limitDate);
        if (Objects.isNull(accidentClueFollowVo)){
            accidentClueFollowVo=new AccidentClueFollowVo().setNotFollow(0).setKeepOnFollow(0).setFollowTimeOut(0).setTotal(0);
        }
        return CollUtil.newArrayList(new AccidentClueFollowVo().setFollowStatus(83531001).setCount(accidentClueFollowVo.getNotFollow()),
            new AccidentClueFollowVo().setFollowStatus(83531002).setCount(accidentClueFollowVo.getKeepOnFollow()),
            new AccidentClueFollowVo().setFollowStatus(83531005).setCount(accidentClueFollowVo.getFollowTimeOut()),
            new AccidentClueFollowVo().setFollowStatus(83531000).setCount(accidentClueFollowVo.getTotal()));
    }
}
