package com.yonyou.dmscus.customer.service.impl.maintaininfo;

import com.google.common.collect.Lists;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.maintaininfo.InviteMaintainInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.maintaininfo.MaintainInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.maintaininfo.RecMaintainInfoDTO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRulePO;
import com.yonyou.dmscus.customer.entity.po.maintaininfo.InviteMaintainInfoPO;
import com.yonyou.dmscus.customer.service.inviteRule.InviteRuleService;
import com.yonyou.dmscus.customer.service.maintaininfo.InviteMaintainInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * description: add a description
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class InviteMaintainInfoServiceImpl implements InviteMaintainInfoService {
    public static final int SIZE = 1_000;
    public static final String DEALER_CODE = "VCDC";
    @Autowired
    InviteRuleService inviteRuleService;
    @Resource
    private InviteMaintainInfoMapper inviteMaintainInfoMapper;

    private static LocalDateTime dateToLocalDateTime(Date date) {
        if (ObjectUtils.isNotEmpty(date)) {
            return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        }
        return null;
    }

    private static List<InviteMaintainInfoPO> getInfoPos(List<MaintainInfoDTO> list, Double mileage, Integer month) {
        List<InviteMaintainInfoPO> infoPos = Lists.newArrayList();
        InviteMaintainInfoPO po;
        for (MaintainInfoDTO infoDto : list) {
            po = InviteMaintainInfoPO.builder()
                    .ownerCode(infoDto.getOwnerCode())
                    .roNo(infoDto.getRoNo())
                    .vin(infoDto.getVin())
                    .maintainMileage(infoDto.getMaintainMileage())
                    .maintainTime(dateToLocalDateTime(infoDto.getMaintainTime()))
                    .beforeMaintainMileage(infoDto.getBeforeMaintainMileage())
                    .beforeMaintainTime(dateToLocalDateTime(infoDto.getBeforeMaintainTime()))
                    .diffMileage(infoDto.getDiffMileage())
                    .diffMonth(infoDto.getDiffMonth())
                    .datumMileage(mileage)
                    .datumMonth(month)
                    .advanceMileage(infoDto.getDiffMileage() - mileage)
                    .advanceMonth(infoDto.getDiffMonth() - month)
                    .build();
            infoPos.add(po);
        }
        return infoPos;
    }

    @Override
    public void insertMaintainInfo(RecMaintainInfoDTO dto) {
        List<MaintainInfoDTO> list = dto.getList();
        if (CollectionUtils.isNotEmpty(list)) {
            Double mileage = null;
            Integer month = null;
            List<InviteRulePO> rules = inviteRuleService.getRegularMaintainRule(DEALER_CODE);
            if (CollectionUtils.isEmpty(rules)) {
                log.info("规则查询不存在");
                return;
            }
            for (InviteRulePO rule : rules) {
                if (CommonConstants.INVITE_RULE_III.equals(rule.getInviteRule())) {
                    month = rule.getRuleValue();
                } else {
                    mileage = rule.getRuleValue().doubleValue();
                }
            }
            List<InviteMaintainInfoPO> infoPos = getInfoPos(list, mileage, month);
            Lists.partition(infoPos, SIZE).forEach(inviteMaintainInfoMapper::insertInviteMaintainInfo);
        }
    }

    @Override
    public InviteMaintainInfoPO selectMainInfoByVin(String vin) {
        return inviteMaintainInfoMapper.selectMainInfoByVin(vin);
    }
}
