package com.yonyou.dmscus.customer.service.oss;

import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusLogPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusRecordPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataLogPo;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataRecordPo;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocFunctionalStatusLogService;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocFunctionalStatusRecordService;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocWarningDataLogService;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocWarningDataRecordService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @title: OssWorkServiceImpl
 * @projectName dmscus.customer
 * @description:
 * @date 2022/11/316:26
 */
@Service
public class OssWorkServiceImpl   implements OssWorkService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    VocFunctionalStatusLogService logService;
    @Autowired
    VocFunctionalStatusRecordService recordService;
    @Autowired
    VocWarningDataLogService warningDataLogSrevice;
    @Autowired
    VocWarningDataRecordService warningDataRecordService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertListLog(List<VocFunctionalStatusLogPO> logPOSList) {
        logger.info("voc激活定时任务保存数据VocFunctionalStatusLogPO:insertListLog开始");
        int   size =  logService.insertList(logPOSList);
        logger.info("voc激活定时任务保存数据VocFunctionalStatusLogPO:insertListLog结束size：{}",size);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertList(List<VocFunctionalStatusRecordPO> list) {
        logger.info("voc激活定时任务保存数据VocFunctionalStatusRecordPO:insertList开始");
        int   size =  recordService.insertList(list);
        logger.info("voc激活定时任务保存数据VocFunctionalStatusRecordPO:insertList结束size：{}",size);
    }

    @Override
    public void insertListWarningDataLog(List<VocWarningDataLogPo> logPOSList) {
        logger.info("voc亮灯定时任务保存数据VocWarningDataLogPo:insertListWarningDataLog开始");
        int   size =  warningDataLogSrevice.insertList(logPOSList);
        logger.info("voc亮灯定时任务保存数据VocWarningDataLogPo:insertListWarningDataLog结束size：{}",size);
    }

    @Override
    public List<VocFunctionalStatusLogPO> selectVocFunctionalStatusLogLog(String data, int begIndex, Integer endIndex) {

        return logService.selectListBydt(data,begIndex,endIndex);
    }


    @Override
    public void updateRecord(List<VocFunctionalStatusRecordPO> updateList) {
        logger.info("voc激活定时任务修改数据VocFunctionalStatusRecordPO：updateRecord开始");
        int   size = recordService.updateList(updateList);
        logger.info("voc激活定时任务修改数据VocFunctionalStatusRecordPO：updateRecord结束size：{}",size);
    }

    @Override
    public List<VocFunctionalStatusRecordPO> selectListStatusRecord(List<VocFunctionalStatusLogPO> x) {
        logger.info("voc激活定时任务查询数据selectListStatusRecord开始");
        List<VocFunctionalStatusRecordPO> list= recordService.selectListStatusRecord(x);
        logger.info("voc激活定时任务查询数据selectListStatusRecord结束");
        return list;
    }

    @Override
    public List<VocWarningDataLogPo> selectWarningdailyLog(String data, int begIndex, Integer endIndex) {
        return warningDataLogSrevice.selectListBydt(data,begIndex,endIndex);
    }

    @Override
    public List<VocWarningDataRecordPo> selectListWarningDataRecord(List<VocWarningDataLogPo> x) {
        logger.info("voc亮灯定时任务查询数据VocWarningDataRecordPo:selectListWarningDataRecord开始");
        List<VocWarningDataRecordPo> list= warningDataRecordService.selectListWarningDataRecord(x);
        logger.info("voc亮灯定时任务查询数据VocWarningDataRecordPo:selectListWarningDataRecord结束");
        return list;
    }

    @Override
    public void insertVocWarningDataRecordList(List<VocWarningDataRecordPo> insertList) {
        logger.info("voc亮灯定时任务保存数据VocWarningDataRecordPo:insertVocWarningDataRecordList开始");
        int   size = warningDataRecordService.insertList(insertList);
        logger.info("voc亮灯定时任务保存数据VocWarningDataRecordPo:insertVocWarningDataRecordList结束size：{}",size);
    }

    @Override
    public void updateVocWarningDataRecordList(List<VocWarningDataRecordPo> updateList) {
        logger.info("voc亮灯定时任务修改数据VocWarningDataRecordPo:updateVocWarningDataRecordList开始");
        int   size =warningDataRecordService.updateList(updateList);
        logger.info("voc亮灯定时任务修改数据VocWarningDataRecordPo：updateVocWarningDataRecordList结束size：{}",size);
    }

    @Override
    public List<VocFunctionalStatusRecordPO> selectVocFunctionalStatusRecordByModfiyTime(String dateTime, Integer begIndex, Integer partitionSize) {
        logger.info("查询voc激活需要处理的数据定时任务查询VocFunctionalStatusRecordPO：selectVocFunctionalStatusRecordByModfiyTime开始:{},{},{}",dateTime,begIndex,partitionSize);
         List<VocFunctionalStatusRecordPO> list = recordService.selectVocFunctionalStatusRecordByModfiyTime(dateTime,begIndex,partitionSize);
        logger.info("查询voc激活需要处理的数据定时任务查询VocFunctionalStatusRecordPO：selectVocFunctionalStatusRecordByModfiyTime结束:{},{},{}",dateTime,begIndex,partitionSize);
        return  list ;
    }

    @Override
    public List<VocWarningDataRecordPo> selectVocWarningDataRecordPoByModfiyTime(String dateTime, Integer begIndex, Integer partitionSize) {
        logger.info("查询voc亮灯需要处理的数据定时任务查询VocWarningDataRecordPo：selectVocWarningDataRecordPoByModfiyTime开始:{},{},{}",dateTime,begIndex,partitionSize);
        List<VocWarningDataRecordPo> list = warningDataRecordService.selectVocWarningDataRecordPoByModfiyTime(dateTime,begIndex,partitionSize);
        logger.info("查询voc亮灯需要处理的数据定时任务查询VocWarningDataRecordPo：selectVocWarningDataRecordPoByModfiyTime结束:{},{},{}",dateTime,begIndex,partitionSize);
        return  list ;
    }

    @Override
    public int updateWarningIsExecute(List<VocWarningDataRecordPo> list){
        return warningDataRecordService.updateWarningIsExecute(list);
    }

    @Override
    public List<VocFunctionalStatusRecordPO> selectListByVins(List<String> xx) {
        logger.info("查询voc激活需要处理的数据定时任务查询VocFunctionalStatusRecordPO：selectListByVins开始");
        List<VocFunctionalStatusRecordPO> list = recordService.selectListByVins(xx);
        logger.info("查询voc激活需要处理的数据定时任务查询VocFunctionalStatusRecordPO：selectListByVins结束");
        return  list ;
    }

    @Override
    public Integer selectKmByVin(String vin, String datetime, String endtime) {
        logger.info("selectKmByVin vin:{},datetime:{},endtime:{}", vin, datetime, endtime);
       String  km= warningDataRecordService.selectKmByVin(vin,datetime,endtime);
        logger.info("selectKmByVin km:{}", km);
       if(!StringUtils.isNullOrEmpty(km)) return Double.valueOf(km).intValue();
       return  null;
    }

    @Override
    public int selectCountWarn(String vin) {
        return warningDataLogSrevice.selectCount(vin);
    }

    @Override
    public int selectWarningdailyReodeByVin(String vin) {
        return warningDataRecordService.selectWarningdailyReodeByVin(vin);
    }

    @Override
    public int selectVocFunctionalStatusRecordByVin(String vin) {
        return recordService.selectVocFunctionalStatusRecordByVin(vin);
    }

    @Override
    public VocWarningDataRecordPo selectVocWarningDataRecordPo(String vin) {
        return warningDataRecordService.selectVocWarningDataRecordPo(vin);
    }

    @Override
    public List<VocFunctionalStatusRecordPO> selectVocFunctionalStatusRecordPOByModfiyTime(String dateTime, Integer begIndex1, Integer partitionSize1) {

        return recordService.selectVocFunctionalStatusRecordPOByModfiyTime(dateTime,begIndex1,partitionSize1);
    }

    @Override
    public int updateFunctionalIsExecute(List<VocFunctionalStatusRecordPO> list){
        return recordService.updateFunctionalIsExecute(list);
    }

    @Override
    public int selectCountVocWarningDataRecordPo(String vin, String dateTime) {
        return warningDataRecordService.selectCountByVinAndModfiy(vin,dateTime);
    }

    @Override
    public int selectVocFunctionalStatusLogByVin(String vin) {
        return logService.selectVocFunctionalStatusLogByVin(vin);
    }

    @Override
    public int selectWarningdailyLogByVin(String vin) {
        return warningDataLogSrevice.selectWarningdailyLogByVin(vin);
    }
}
