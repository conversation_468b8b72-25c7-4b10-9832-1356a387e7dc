package com.yonyou.dmscus.customer.service.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dto.CompanyInfoDTO;
import com.yonyou.dmscus.customer.dto.ComplaintInfMoreVo;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintmoreDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.v51dk.V51dkComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoPO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintMaintainPO;
import com.yonyou.dmscus.customer.entity.vo.V51dkComplaintInfMoreVO;

import java.text.ParseException;
import java.util.List;


/**
 * <p>
 * 销售客户投诉信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-10
 */
public interface SaleComplaintInfoService {
    IPage<SaleComplaintInfoDTO> selectPageBysql(Page page, SaleComplaintInfoDTO saleComplaintInfoDTO);
    List<SaleComplaintInfoDTO> selectListBySql(SaleComplaintInfoDTO saleComplaintInfoDTO);
    SaleComplaintInfoDTO getById(Long id);
    int insert(SaleComplaintInfoDTO saleComplaintInfoDTO);
    int update(Long id, SaleComplaintInfoDTO saleComplaintInfoDTO);
    int deleteById(Long id);

    int insertComplaint(ComplaintmoreDTO complaintmoreDTO);

    IPage<ComplaintInfMoreDTO> selectCusByDeal(Page page, ComplaintInfMoreDTO complaintInfMoreDTO, String user);

    int insertClose(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO, String flag);

    int insertRegionClose(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO);

    int insertSaleHQClose(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO);

    int insertSaleRestart(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO);

    List<ComplaintInfMoreDTO> selectCusByDealAll(ComplaintInfMoreDTO complaintInfMoreDTO, String user)throws ParseException;
    void IssuedUpdataData(SaleComplaintFollowDTO saleComplaintFollowDTO, String CreaterOrg, String JobOrderStatus);

    ComplaintInfMoreVo selectSaleCusDetailById(ComplaintInfMoreDTO complaintInfMoreDTO);

    Page<V51dkComplaintInfMoreVO> select51dkCusByDeal(Page page, V51dkComplaintInfMoreDTO complaintInfMoreDTO);

    /**
     * 店端客诉，获取处理经销商下拉框数据
     * @return
     */
    List<CompanyInfoDTO> queryCompanyInfoList();

    Integer updateSaleComplaint(ComplaintInfMoreDTO complaintCustomFieldTestDTO);

    int updateSaleComplaint(SaleComplaintInfoDTO saleComplaintInfoDTO, boolean workStatusFlag, boolean workOrderNatureFlag, boolean closeCaseStatusFlag, SaleComplaintInfoPO saleComplaintInfoPO, SaleComplaintMaintainPO saleComplaintMaintainPO);
}
