package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

    
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;

import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiSetDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiTest;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRuleUsePO;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintKpiBaseRuleUseMapper;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRuleUseTestPO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintKpiBaseRuleUseService;

import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;




    /**
 * <p>
 * 客户投诉KP基础规则使用表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
@Service
public class ComplaintKpiBaseRuleUseServiceImpl implements ComplaintKpiBaseRuleUseService {
        /**
         * 日志说明
         */
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        ComplaintKpiBaseRuleUseMapper complaintKpiBaseRuleUseMapper;

        /**
         * 分页查询对应数据
         *
         * @param page 分页对象
         * @param complaintKpiBaseRuleUseDTO 查询条件
         * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public IPage<ComplaintKpiBaseRuleUseDTO>selectPageBysql(Page page,ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO){
            if(complaintKpiBaseRuleUseDTO ==null){
                complaintKpiBaseRuleUseDTO =new ComplaintKpiBaseRuleUseDTO();
            }
            ComplaintKpiBaseRuleUsePO complaintKpiBaseRuleUsePo =complaintKpiBaseRuleUseDTO.transDtoToPo(ComplaintKpiBaseRuleUsePO.class);

            List<ComplaintKpiBaseRuleUsePO>list= complaintKpiBaseRuleUseMapper.selectPageBySql(page,complaintKpiBaseRuleUsePo);
            if(CommonUtils.isNullOrEmpty(list)){
                page.setRecords(new ArrayList<>());
                return page;
            }else{
                List<ComplaintKpiBaseRuleUseDTO>result=list.stream().map(m->m.transPoToDto(ComplaintKpiBaseRuleUseDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param complaintKpiBaseRuleUseDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<ComplaintKpiBaseRuleUseDTO>selectListBySql(ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO){
            if(complaintKpiBaseRuleUseDTO ==null){
                complaintKpiBaseRuleUseDTO =new ComplaintKpiBaseRuleUseDTO();
            }
            ComplaintKpiBaseRuleUsePO complaintKpiBaseRuleUsePo =complaintKpiBaseRuleUseDTO.transDtoToPo(ComplaintKpiBaseRuleUsePO.class);
            List<ComplaintKpiBaseRuleUsePO>list= complaintKpiBaseRuleUseMapper.selectListBySql(complaintKpiBaseRuleUsePo);
            if(CommonUtils.isNullOrEmpty(list)){
                return new ArrayList<>();
            }else{
                return list.stream().map(m->m.transPoToDto(ComplaintKpiBaseRuleUseDTO.class)).collect(Collectors.toList());
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public ComplaintKpiBaseRuleUseDTO getById(Long id){
            ComplaintKpiBaseRuleUsePO complaintKpiBaseRuleUsePo = complaintKpiBaseRuleUseMapper.selectById(id);
            if(complaintKpiBaseRuleUsePo!=null){
                return complaintKpiBaseRuleUsePo.transPoToDto(ComplaintKpiBaseRuleUseDTO.class);
            }else{
                throw new DALException("查询失败,没有符合条件的数据");
            }
        }

        /**
         * 根据DTO 进行数据新增
         *
         * @param complaintKpiBaseRuleUseDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO){
            //对对象进行赋值操作
            ComplaintKpiBaseRuleUsePO complaintKpiBaseRuleUsePo = complaintKpiBaseRuleUseDTO.transDtoToPo(ComplaintKpiBaseRuleUsePO.class);
            //执行插入
            int row= complaintKpiBaseRuleUseMapper.insert(complaintKpiBaseRuleUsePo);
            //返回插入的值
            return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param complaintKpiBaseRuleUseDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO){
            ComplaintKpiBaseRuleUsePO complaintKpiBaseRuleUsePo = complaintKpiBaseRuleUseMapper.selectById(id);
            //对对象进行赋值操作
            complaintKpiBaseRuleUseDTO.transDtoToPo(complaintKpiBaseRuleUsePo);
            //执行更新
            int row= complaintKpiBaseRuleUseMapper.updateById(complaintKpiBaseRuleUsePo);
            return row;
        }

        /**
         * 根据ID 进行删除数据
         *
         * @param id
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteById(Long id){
            int deleteCount= complaintKpiBaseRuleUseMapper.deleteById(id);
            logger.debug("删除数量：{}",deleteCount);
            if(deleteCount<=0){
                throw new DALException("删除失败,没有符合条件的数据");
            }else{
                return deleteCount;
            }
        }

        @Override
        public IPage<ComplaintKpiBaseRuleUseTestDTO> selectPageBysql1(Page page, ComplaintKpiBaseRuleUseTestDTO complaintKpiBaseRuleUseTestDTO) {

            if(complaintKpiBaseRuleUseTestDTO ==null){
                complaintKpiBaseRuleUseTestDTO =new ComplaintKpiBaseRuleUseTestDTO();
            }
            ComplaintKpiBaseRuleUseTestPO complaintKpiBaseRuleUseTestPo =complaintKpiBaseRuleUseTestDTO.transDtoToPo(ComplaintKpiBaseRuleUseTestPO.class);

            List<ComplaintKpiBaseRuleUseTestPO>list= complaintKpiBaseRuleUseMapper.selectPageBySql1(page,complaintKpiBaseRuleUseTestPo);
            if(CommonUtils.isNullOrEmpty(list)){
                page.setRecords(new ArrayList<>());
                return page;
            }else{
                List<ComplaintKpiBaseRuleUseTestDTO>result=list.stream().map(m->m.transPoToDto(ComplaintKpiBaseRuleUseTestDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        @Override
        public List<ComplaintKpiBaseRuleUseTestDTO> selectListBySql1(ComplaintKpiBaseRuleUseTestDTO complaintKpiBaseRuleUseTestDTO) {
            if(complaintKpiBaseRuleUseTestDTO ==null){
                complaintKpiBaseRuleUseTestDTO =new ComplaintKpiBaseRuleUseTestDTO();
            }
            ComplaintKpiBaseRuleUseTestPO complaintKpiBaseRuleUseTestPo =complaintKpiBaseRuleUseTestDTO.transDtoToPo(ComplaintKpiBaseRuleUseTestPO.class);
            List<ComplaintKpiBaseRuleUseTestPO>list= complaintKpiBaseRuleUseMapper.selectListBySql1(complaintKpiBaseRuleUseTestPo);
            if(CommonUtils.isNullOrEmpty(list)){
                return new ArrayList<>();
            }else{
                return list.stream().map(m->m.transPoToDto(ComplaintKpiBaseRuleUseTestDTO.class)).collect(Collectors.toList());
            }
        }

        @Override
        public int updateWarnValue(List<ComplaintKpiTest> list) {
            int row=0;
            for (int i=0;i<list.size();i++){
                ComplaintKpiTest complaintKpiTest=list.get(i);
                long id=complaintKpiTest.getId();
                ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO=new ComplaintKpiBaseRuleUseDTO();
                complaintKpiBaseRuleUseDTO.setDealerCode(FrameworkUtil.getLoginInfo().getOwnerCode());
                complaintKpiBaseRuleUseDTO.setRuleId(id);
                complaintKpiBaseRuleUseDTO.setUser(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
                List<ComplaintKpiBaseRuleUseDTO> all=selectListBySql(complaintKpiBaseRuleUseDTO);
                complaintKpiBaseRuleUseDTO.setWarnValue(complaintKpiTest.getWarnValue());
                if(all.size()==0){
                    row=insert(complaintKpiBaseRuleUseDTO);
                }else {
                    long userId=all.get(0).getId();
                    row= update(userId,complaintKpiBaseRuleUseDTO);
                }
            }
            return row;
        }
}
