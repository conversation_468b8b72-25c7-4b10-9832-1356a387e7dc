package com.yonyou.dmscus.customer.service.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ReasonsforFiveDto;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintNotCloseCaseReasonDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintNotCloseCaseReasonTestPO;

import java.util.List;


/**
 * <p>
 * 5日未结案原因 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
public interface SaleComplaintNotCloseCaseReasonService {
    IPage<SaleComplaintNotCloseCaseReasonDTO> selectPageBysql(Page page, SaleComplaintNotCloseCaseReasonDTO saleComplaintNotCloseCaseReasonDTO);
    List<SaleComplaintNotCloseCaseReasonDTO> selectListBySql(SaleComplaintNotCloseCaseReasonDTO saleComplaintNotCloseCaseReasonDTO);
    SaleComplaintNotCloseCaseReasonDTO getById(Long id);
    int insert(SaleComplaintNotCloseCaseReasonDTO saleComplaintNotCloseCaseReasonDTO);
    int update(Long id, SaleComplaintNotCloseCaseReasonDTO saleComplaintNotCloseCaseReasonDTO);
    int deleteById(Long id);
    int deleteBatchIds(String ids);

    IPage<ComplaintNotCloseCaseReasonTestDTO> selectPageBysql3(Page page, ComplaintNotCloseCaseReasonTestDTO complaintNotCloseCaseReasonTestDTO);

    int insertReasonsforFive(ReasonsforFiveDto reasonsforFiveDto);
}
