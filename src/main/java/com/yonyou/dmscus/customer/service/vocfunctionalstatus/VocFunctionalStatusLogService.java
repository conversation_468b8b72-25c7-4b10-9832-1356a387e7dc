package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusLogPO;

import java.util.List;

/**
 * <AUTHOR>
 * @title: VocFunctionalStatusLogService
 * @projectName dmscus.customer
 * @description: TODO
 * @date 2022/11/118:46
 */
public interface VocFunctionalStatusLogService   {
    int  insertList(List<VocFunctionalStatusLogPO> logPOSList);

    List<VocFunctionalStatusLogPO> selectListBydt(String data, int begIndex, Integer endIndex);

    int selectVocFunctionalStatusLogByVin(String vin);
}
