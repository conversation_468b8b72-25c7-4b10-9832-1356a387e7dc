package com.yonyou.dmscus.customer.service.oss;

import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusLogPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusRecordPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataLogPo;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataRecordPo;

import java.util.List;

/**
 * <AUTHOR>
 * @title: WorkService
 * @projectName dmscus.customer
 * @description: TODO
 * @date 2022/11/316:26
 */
public interface OssWorkService {
    void insertListLog(List<VocFunctionalStatusLogPO> logPOSList);
    void insertList(List<VocFunctionalStatusRecordPO> list);

    void insertListWarningDataLog(List<VocWarningDataLogPo> logPOSList);

    List<VocFunctionalStatusLogPO> selectVocFunctionalStatusLogLog(String data, int begIndex, Integer endIndex);


    void updateRecord(List<VocFunctionalStatusRecordPO> updateList);

    List<VocFunctionalStatusRecordPO> selectListStatusRecord(List<VocFunctionalStatusLogPO>  x);

    List<VocWarningDataLogPo> selectWarningdailyLog(String data, int begIndex, Integer endIndex);

    List<VocWarningDataRecordPo> selectListWarningDataRecord(List<VocWarningDataLogPo> x);

    void insertVocWarningDataRecordList(List<VocWarningDataRecordPo> insertList);

    void updateVocWarningDataRecordList(List<VocWarningDataRecordPo> updateList);

    List<VocFunctionalStatusRecordPO> selectVocFunctionalStatusRecordByModfiyTime(String dateTime, Integer begIndex, Integer partitionSize);

    List<VocWarningDataRecordPo> selectVocWarningDataRecordPoByModfiyTime(String dateTime, Integer begIndex, Integer partitionSize);

    int updateWarningIsExecute(List<VocWarningDataRecordPo> list);

    List<VocFunctionalStatusRecordPO> selectListByVins(List<String> xx);

    Integer selectKmByVin(String poVin, String datime, String endtime);

    int selectCountWarn(String vin);

    int selectWarningdailyReodeByVin(String vin);

    int selectVocFunctionalStatusRecordByVin(String vin);

    VocWarningDataRecordPo selectVocWarningDataRecordPo(String vin);

    List<VocFunctionalStatusRecordPO> selectVocFunctionalStatusRecordPOByModfiyTime(String dateTime, Integer begIndex1, Integer partitionSize1);

    int updateFunctionalIsExecute(List<VocFunctionalStatusRecordPO> list);

    int selectCountVocWarningDataRecordPo(String vin, String dateTime);

    int selectVocFunctionalStatusLogByVin(String vin);

    int selectWarningdailyLogByVin(String vin);
}
