package com.yonyou.dmscus.customer.service.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiSetDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintKpiBaseRulePO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintKpiBaseRuleDTO;

import java.util.List;


/**
 * <p>
 * 销售客户投诉KP基础规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-21
 */
public interface SaleComplaintKpiBaseRuleService {
    IPage<SaleComplaintKpiBaseRuleDTO> selectPageBysql(Page page, SaleComplaintKpiBaseRuleDTO saleComplaintKpiBaseRuleDTO);
    List<SaleComplaintKpiBaseRuleDTO> selectListBySql(SaleComplaintKpiBaseRuleDTO saleComplaintKpiBaseRuleDTO);
    SaleComplaintKpiBaseRuleDTO getById(Long id);
    int insert(SaleComplaintKpiBaseRuleDTO saleComplaintKpiBaseRuleDTO);
    int update(Long id, SaleComplaintKpiBaseRuleDTO saleComplaintKpiBaseRuleDTO);
    int deleteById(Long id);
    int deleteBatchIds(String ids);

    int updateKpi(List<ComplaintKpiSetDTO> list);
}
