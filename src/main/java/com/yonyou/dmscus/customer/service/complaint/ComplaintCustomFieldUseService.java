package com.yonyou.dmscus.customer.service.complaint;

    import com.baomidou.mybatisplus.core.metadata.IPage;
    import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

    import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
    import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldUseDTO;

    import java.util.List;
    import java.util.Map;


/**
 * <p>
 * 客户投诉自定义字段使用表 每个人不同对应不同 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
public interface ComplaintCustomFieldUseService{
    /**
     * 分页查询
     * @param page
     * @param complaintCustomFieldUseDTO
     * @return
     */
       IPage<ComplaintCustomFieldUseDTO> selectPageBysql(Page page, ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO);

    /**
     * 集合查询
     * @param complaintCustomFieldUseDTO
     * @return
     */
       List<ComplaintCustomFieldUseDTO> selectListBySql(ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO);

    /**
     * 通过id查询
     * @param id
     * @return
     */
       ComplaintCustomFieldUseDTO getById(Long id);

    /**
     * 新增
     * @param complaintCustomFieldUseDTO
     * @return
     */
    int insert(ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO);

    /**
     * 更新
     * @param id
     * @param complaintCustomFieldUseDTO
     * @return
     */
    int update(Long id, ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO);

    /**
     * 通过id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 新增
     * @param complaintCustomFieldTestDTO
     * @return
     */
    int insertFied(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO);

    /**
     * 新增置顶
     * @param sortList
     * @return
     */
    int insertSort(List<ComplaintCustomFieldUseDTO> sortList);

    /**
     * 重置
     * @return
     */
   int  resetFied();
}
