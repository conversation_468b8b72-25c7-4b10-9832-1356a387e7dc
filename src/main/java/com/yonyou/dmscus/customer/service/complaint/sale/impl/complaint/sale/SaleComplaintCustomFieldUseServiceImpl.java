package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldUseDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintCustomFieldUsePO;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintCustomFieldUseMapper;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintCustomFieldUseService;

import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintCustomFieldUseDTO;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 客户投诉自定义字段使用表 每个人不同对应不同 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
@Service
public class SaleComplaintCustomFieldUseServiceImpl implements SaleComplaintCustomFieldUseService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    SaleComplaintCustomFieldUseMapper saleComplaintCustomFieldUseMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                           分页对象
     * @param saleComplaintCustomFieldUseDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintCustomFieldUseDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<SaleComplaintCustomFieldUseDTO> selectPageBysql(Page page, SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO) {
        if (saleComplaintCustomFieldUseDTO == null) {
            saleComplaintCustomFieldUseDTO = new SaleComplaintCustomFieldUseDTO();
        }
        SaleComplaintCustomFieldUsePO saleComplaintCustomFieldUsePO = saleComplaintCustomFieldUseDTO.transDtoToPo(SaleComplaintCustomFieldUsePO.class);

        List<SaleComplaintCustomFieldUsePO> list = saleComplaintCustomFieldUseMapper.selectPageBySql(page, saleComplaintCustomFieldUsePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<SaleComplaintCustomFieldUseDTO> result = list.stream().map(m -> m.transPoToDto(SaleComplaintCustomFieldUseDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param saleComplaintCustomFieldUseDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintCustomFieldUseDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<SaleComplaintCustomFieldUseDTO> selectListBySql(SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO) {
        if (saleComplaintCustomFieldUseDTO == null) {
            saleComplaintCustomFieldUseDTO = new SaleComplaintCustomFieldUseDTO();
        }
        SaleComplaintCustomFieldUsePO saleComplaintCustomFieldUsePO = saleComplaintCustomFieldUseDTO.transDtoToPo(SaleComplaintCustomFieldUsePO.class);
        List<SaleComplaintCustomFieldUsePO> list = saleComplaintCustomFieldUseMapper.selectListBySql(saleComplaintCustomFieldUsePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(SaleComplaintCustomFieldUseDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintCustomFieldUseDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public SaleComplaintCustomFieldUseDTO getById(Long id) {
        SaleComplaintCustomFieldUsePO saleComplaintCustomFieldUsePO = saleComplaintCustomFieldUseMapper.selectById(id);
        if (saleComplaintCustomFieldUsePO != null) {
            return saleComplaintCustomFieldUsePO.transPoToDto(SaleComplaintCustomFieldUseDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据" );
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param saleComplaintCustomFieldUseDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO) {
        //对对象进行赋值操作
        SaleComplaintCustomFieldUsePO saleComplaintCustomFieldUsePO = saleComplaintCustomFieldUseDTO.transDtoToPo(SaleComplaintCustomFieldUsePO.class);
        //执行插入
        int row = saleComplaintCustomFieldUseMapper.insert(saleComplaintCustomFieldUsePO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                             主键ID
     * @param saleComplaintCustomFieldUseDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO) {
        SaleComplaintCustomFieldUsePO saleComplaintCustomFieldUsePO = saleComplaintCustomFieldUseMapper.selectById(id);
        //对对象进行赋值操作
        saleComplaintCustomFieldUseDTO.transDtoToPo(saleComplaintCustomFieldUsePO);
        //执行更新
        int row = saleComplaintCustomFieldUseMapper.updateById(saleComplaintCustomFieldUsePO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = saleComplaintCustomFieldUseMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据" );
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = saleComplaintCustomFieldUseMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据" );
        } else {
            return deleteCount;
        }
    }

    @Override
    public int insertFied(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO) {
        SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO=new SaleComplaintCustomFieldUseDTO();
        Long userId=FrameworkUtil.getLoginInfo().getUserId();
        //5日未结案原因是否填写
        List<SaleComplaintCustomFieldUsePO> smallClass2List1=saleComplaintCustomFieldUseMapper.querysmallClass2(userId);
        saleComplaintCustomFieldUseDTO=new SaleComplaintCustomFieldUseDTO();
        saleComplaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
        if (complaintCustomFieldTestDTO.getSmallClass1()==true){
            saleComplaintCustomFieldUseDTO.setFieldName("small_class");
            saleComplaintCustomFieldUseDTO.setIsQuery(true);
            if(smallClass2List1.size()!=0){
                long smallClassId=smallClass2List1.get(0).getId();
                update(smallClassId,saleComplaintCustomFieldUseDTO);
            }else {
                insert(saleComplaintCustomFieldUseDTO);
            }

        }else {
            if(smallClass2List1.size()!=0){
                long smallClassId=smallClass2List1.get(0).getId();
                saleComplaintCustomFieldUseDTO.setIsQuery(false);
                update(smallClassId,saleComplaintCustomFieldUseDTO);
            }
        }

        //客诉单类别一阶层是否填写
        List<SaleComplaintCustomFieldUsePO> category1List1=saleComplaintCustomFieldUseMapper.queryCategory1(userId);
        saleComplaintCustomFieldUseDTO=new SaleComplaintCustomFieldUseDTO();
        saleComplaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
        if(complaintCustomFieldTestDTO.getCategory11()==true){
            saleComplaintCustomFieldUseDTO.setFieldName("category1");
            saleComplaintCustomFieldUseDTO.setIsQuery(true);
            if(category1List1.size()!=0){
                long category1Id=category1List1.get(0).getId();
                update(category1Id,saleComplaintCustomFieldUseDTO);
            }else {
                insert(saleComplaintCustomFieldUseDTO);
            }

        }else {
            if(category1List1.size()!=0){
                long category1Id=category1List1.get(0).getId();
                saleComplaintCustomFieldUseDTO.setIsQuery(false);
                update(category1Id,saleComplaintCustomFieldUseDTO);
            }
        }

        //客诉单类别二阶层是否填写
        List<SaleComplaintCustomFieldUsePO> category2List1=saleComplaintCustomFieldUseMapper.queryCategory2(userId);
        saleComplaintCustomFieldUseDTO=new SaleComplaintCustomFieldUseDTO();
        saleComplaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
        if(complaintCustomFieldTestDTO.getCategory21()==true){
            saleComplaintCustomFieldUseDTO.setFieldName("category2");
            saleComplaintCustomFieldUseDTO.setIsQuery(true);
            if(category2List1.size()!=0){
                long category2Id=category2List1.get(0).getId();
                update(category2Id,saleComplaintCustomFieldUseDTO);
            }else {
                insert(saleComplaintCustomFieldUseDTO);
            }

        }else {
            if(category2List1.size()!=0){
                long category2Id=category2List1.get(0).getId();
                saleComplaintCustomFieldUseDTO.setIsQuery(false);
                update(category2Id,saleComplaintCustomFieldUseDTO);
            }
        }

        //客诉单类别三阶层是否填写
        List<SaleComplaintCustomFieldUsePO> category3List1=saleComplaintCustomFieldUseMapper.queryCategory3(userId);
        saleComplaintCustomFieldUseDTO=new SaleComplaintCustomFieldUseDTO();
        saleComplaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
        if(complaintCustomFieldTestDTO.getCategory31()==true){
            saleComplaintCustomFieldUseDTO.setFieldName("category3");
            saleComplaintCustomFieldUseDTO.setIsQuery(true);
            if(category3List1.size()!=0){
                long category3Id=category3List1.get(0).getId();
                update(category3Id,saleComplaintCustomFieldUseDTO);
            }else {
                insert(saleComplaintCustomFieldUseDTO);
            }
        }else {
            if(category3List1.size()!=0){
                long category3Id=category3List1.get(0).getId();
                saleComplaintCustomFieldUseDTO.setIsQuery(false);
                update(category3Id,saleComplaintCustomFieldUseDTO);
            }
        }

        return 1;
    }

    @Override
    public int insertSort(List<SaleComplaintCustomFieldUseDTO> sortList) {
        Long userId=FrameworkUtil.getLoginInfo().getUserId();
        saleComplaintCustomFieldUseMapper.deletesort(userId);
        for (int i=0;i<sortList.size();i++){
            SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO= new SaleComplaintCustomFieldUseDTO();
            String  fieldName=sortList.get(i).getFieldName();
            saleComplaintCustomFieldUseDTO.setFieldName(fieldName);
            String sortType=sortList.get(i).getSortType();
            saleComplaintCustomFieldUseDTO.setSortType(sortType);
            String tableName="";
            if(!StringUtils.isNullOrEmpty(fieldName))
            switch (fieldName){
                case "work_order_nature" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("work_order_nature");
                    break;
                case "work_order_classification" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("work_order_classification");
                    break;

                case "complaint_id" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("complaint_id");
                    break;
                case "call_time" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("call_time");
                    break;

                case "region" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("region");
                    break;
                case "region_manager" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("region_manager");
                    break;
                case "dealer_code" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("dealer_code");
                    break;
                case "dealer_name" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("dealer_name");
                    break;
                case "bloc" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("bloc");
                    break;
                case "subject" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("subject");
                    break;
                case "source" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("source");
                    break;
                case "type" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("type");
                    break;
                case "work_order_status" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("work_order_status");
                    break;
                case "close_case_status" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("close_case_status");
                    break;
                case "call_name" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("call_name");
                    break;
                case "call_tel" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("call_tel");
                    break;
                case "model" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("model");
                    break;
                case "license_plate_num" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("license_plate_num");
                    break;
                case "vin" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("vin");
                    break;
                case "newest_restart_time" :
                    tableName="tt_sale_complaint_info";
                    saleComplaintCustomFieldUseDTO.setTableName(tableName);
                    saleComplaintCustomFieldUseDTO.setFieldDescribe("newest_restart_time");
                    break;
                default :



            }
            saleComplaintCustomFieldUseDTO.setIsSort(1);
            saleComplaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
            SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDto=new SaleComplaintCustomFieldUseDTO();
            saleComplaintCustomFieldUseDto.setUserId(userId);
            saleComplaintCustomFieldUseDto.setFieldName(fieldName);
            saleComplaintCustomFieldUseDto.setTableName(tableName);
            List<SaleComplaintCustomFieldUsePO> sortlist=saleComplaintCustomFieldUseMapper.querysort(saleComplaintCustomFieldUseDto);
            if(sortlist.size()!=0){
                long sortId=sortlist.get(0).getId();
                update(sortId,saleComplaintCustomFieldUseDTO);
            }else {
                insert(saleComplaintCustomFieldUseDTO);
            }
        }

        return 1;
    }

    /**
     * 自定义重置
     */
    @Override
    public int resetFied() {
        long userId=FrameworkUtil.getLoginInfo().getUserId();
        return   saleComplaintCustomFieldUseMapper.resetFied(userId);
    }
}
