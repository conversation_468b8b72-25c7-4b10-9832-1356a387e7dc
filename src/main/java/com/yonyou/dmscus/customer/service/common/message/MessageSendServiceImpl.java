package com.yonyou.dmscus.customer.service.common.message;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.DateUtil;
import com.yonyou.dmscloud.function.utils.common.StringUtils;
import com.yonyou.dmscloud.function.utils.security.MD5Util;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.common.QueryUserPositionDTO;
import com.yonyou.dmscus.customer.entity.dto.common.UserPositionOutDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.AppPushDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.RequestDTO;
import com.yonyou.dmscus.customer.feign.MidEndAuthCenterClient;
import com.yonyou.dmscus.customer.rocketmq.AppPushRocketMQTemplate;
import com.yonyou.dmscus.customer.util.HeaderUtil;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2023/10/30 13:43
 * @Version 1.0
 */
@Service
public class MessageSendServiceImpl implements MessageSendService {

    private static final Logger logger = LoggerFactory.getLogger(MessageSendServiceImpl.class);

    @Resource
    private AppPushRocketMQTemplate appPushRocketMQTemplate;
    @Resource
    private MidEndAuthCenterClient midEndAuthCenterClient;

    @Value("${accidentClue.appPush.rocketmq.topic:}")
    private String topic;

    @Value("${volvo.email.appId:3a1ef6bc-60f1-430b-ac96-b3a60ee70bb3}")
    public String volvoAppId;

    @Value("${accidentClue.appPush.rocketmq.tag:}")
    private String messageSendAppTag;

    @Value("${accidentClue.appPush.rocketmq.event:}")
    private String messageSendAppEvent;

    @Override
    public String messageSendApp(AppPushDTO appPushDTO) {

        String[] empIds = appPushDTO.getTargetCodes();
        if (!StringUtils.isNullOrEmpty(empIds)) {
            Stream<String> stream = Arrays.stream(empIds);
            List<String> list = stream.distinct().collect(Collectors.toList());
            empIds = list.toArray(new String[1]);
        }
        appPushDTO.setTargetCodes(empIds);
        appPushDTO.setMode("notify");
        appPushDTO.setTargetType("ALIAS");
        if (StringUtils.isNullOrEmpty(appPushDTO.getPriority())) {
            appPushDTO.setPriority(CommonConstants.GENERAL_LEVEL);
        }
        //HttpHeaders headers = formatHttpHeaders(volvoHttpUtils.getHeaders());
        String key = MD5Util.encrypte(DateUtil.formatDateByFormat(new Date(), DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS), JSON.toJSONString(appPushDTO));
        logger.info("messageSendApp send message key:{}", key);
        appPushDTO.setAppId(volvoAppId);
        appPushDTO.setEvent(messageSendAppEvent);
        logger.info("消息推送数据:{}", appPushDTO);
        Map<String, Object> resMap = Maps.newHashMap();
        resMap.put("code", "200");
        try {
            this.push(this.getMqDestination(topic, messageSendAppTag), key, appPushDTO);
        } catch (Exception e) {
            logger.error("推送消息失败", e);
            resMap.put("code", "100");
        }
        return JSON.toJSONString(resMap);
    }

    private void push(String mqDestination, String keys, AppPushDTO appPushDTO) {
        try {
            appPushRocketMQTemplate.asyncSend(mqDestination, new Message<String>() {
                @Override
                public String getPayload() {
                    return JSON.toJSONString(appPushDTO);
                }

                @Override
                public MessageHeaders getHeaders() {
                    HashMap<String, Object> headers = Maps.newHashMap();
                    headers.put(RocketMQHeaders.KEYS, keys);
                    return new MessageHeaders(headers);
                }
            }, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    logger.info("消息投递成功,result:{}", JSON.toJSONString(sendResult));
                }

                @Override
                public void onException(Throwable throwable) {
                    logger.error("消息投递失败", throwable);
                }
            });
        } catch (Exception e) {
            logger.error("推送消息失败", e);
            throw new ServiceBizException("推送消息失败");
        }
        logger.info("推送消息成功");
    }

    private HttpHeaders formatHttpHeaders(HttpHeaders headers) {
        if (null != headers) {
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (Objects.nonNull(requestAttributes)) {
                HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
                Enumeration<String> headerNames = request.getHeaderNames();
                while (headerNames.hasMoreElements()) {
                    String headerName = headerNames.nextElement();
                    if ("appId".equals(headerName)) {
                        continue;
                    }
                    headers.add(headerName, request.getHeader(headerName));
                }
            } else {
                Map<String, String> headerMap = HeaderUtil.getAll();
                Optional.ofNullable(headerMap).ifPresent(h -> {
                    h.forEach((k, v) -> {
                        if ("appId".equals(k)) {
                            return;
                        }
                        headers.add(k, v);
                    });
                });
            }
            headers.remove("appId");
            headers.put("appId", Collections.singletonList(volvoAppId));
        }
        return headers;

    }

    private String getMqDestination(String topic, String tag) {
        return org.apache.commons.lang3.StringUtils.join(Lists.newArrayList(topic, tag), ":");
    }

    @Override
    public List<String> queryEmpId(QueryUserPositionDTO empQueryDto) {
        List<String> aliases = null;
        List<UserPositionOutDTO> empVos = this.getUserPositionInfoList(empQueryDto);
        if (!CollectionUtils.isEmpty(empVos)) {
            aliases = new LinkedList<>();
            for (UserPositionOutDTO dto : empVos) {
                aliases.add("E" + dto.getEmpId());
            }
        }
        logger.info("查询员工返回数据List:{}", aliases);
        return aliases;
    }

    @Override
    public List<UserPositionOutDTO> getUserPositionInfoList(QueryUserPositionDTO empQueryDto) {

        if (CollectionUtils.isEmpty(empQueryDto.getUserIds()) && CollectionUtils.isEmpty(empQueryDto.getRoleCodes())){
            return new ArrayList<>();
        }
        RequestDTO<QueryUserPositionDTO> request = new RequestDTO<>();
        request.setData(empQueryDto);
        logger.info("查询员工QueryUserPositionDTO:{}", request);
//        HttpEntity<RequestDTO> httpEntity = new HttpEntity<>(request, volvoHttpUtils.getHeaders());
//        String url = midUrlProperties.getMidEndAuthCenter() + midUrlProperties.getVolvoMidEmpPosition();
//        ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(url, HttpMethod.POST, httpEntity, ResponseDTO.class);
//        ObjectMapper objectMapper = new ObjectMapper();
//        List<UserPositionOutDTO> empVos = objectMapper.convertValue(responseEntity.getBody().getData(),
//                new TypeReference<List<UserPositionOutDTO>>() {
//                });
        ResponseDTO<List<UserPositionOutDTO>> response = midEndAuthCenterClient.getEmpInfoList(request);
        List<UserPositionOutDTO> empVos = response.getData();
        logger.info("查询empVos数据:{}", empVos);

        return CollectionUtils.isEmpty(empVos) ? new ArrayList<>() : empVos.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(UserPositionOutDTO::getUserId))),
                ArrayList::new));
    }
}
