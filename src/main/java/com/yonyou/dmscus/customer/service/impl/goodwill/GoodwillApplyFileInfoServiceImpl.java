package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyFileInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFileInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyFileInfoPO;
import com.yonyou.dmscus.customer.enums.GoodwillFileTypeEnum;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyFileInfoService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 亲善预申请上传附件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
@Service
public class GoodwillApplyFileInfoServiceImpl extends ServiceImpl<GoodwillApplyFileInfoMapper, GoodwillApplyFileInfoPO>
		implements GoodwillApplyFileInfoService {
	/** 日志对象 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillApplyFileInfoMapper goodwillApplyFileInfoMapper;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillApplyFileInfoDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFileInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillApplyFileInfoDTO> selectPageBysql(Page page,
			GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO) {
		if (goodwillApplyFileInfoDTO == null) {
			goodwillApplyFileInfoDTO = new GoodwillApplyFileInfoDTO();
		}
		GoodwillApplyFileInfoPO goodwillApplyFileInfoPo = goodwillApplyFileInfoDTO
				.transDtoToPo(GoodwillApplyFileInfoPO.class);

		List<GoodwillApplyFileInfoPO> list = goodwillApplyFileInfoMapper.selectPageBySql(page, goodwillApplyFileInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillApplyFileInfoDTO> result = list.stream()
					.map(m -> m.transPoToDto(GoodwillApplyFileInfoDTO.class)).collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillApplyFileInfoDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFileInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillApplyFileInfoDTO> selectListBySql(GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO) {
		if (goodwillApplyFileInfoDTO == null) {
			goodwillApplyFileInfoDTO = new GoodwillApplyFileInfoDTO();
		}
		GoodwillApplyFileInfoPO goodwillApplyFileInfoPo = goodwillApplyFileInfoDTO
				.transDtoToPo(GoodwillApplyFileInfoPO.class);
		List<GoodwillApplyFileInfoPO> list = goodwillApplyFileInfoMapper.selectListBySql(goodwillApplyFileInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillApplyFileInfoDTO.class)).collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFileInfoDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillApplyFileInfoDTO getById(Long id) {
		GoodwillApplyFileInfoPO goodwillApplyFileInfoPo = goodwillApplyFileInfoMapper.selectById(id);
		if (goodwillApplyFileInfoPo != null) {
			return goodwillApplyFileInfoPo.transPoToDto(GoodwillApplyFileInfoDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillApplyFileInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO) {
		// 对对象进行赋值操作
		GoodwillApplyFileInfoPO goodwillApplyFileInfoPo = goodwillApplyFileInfoDTO
				.transDtoToPo(GoodwillApplyFileInfoPO.class);
		// 执行插入
		int row = goodwillApplyFileInfoMapper.insert(goodwillApplyFileInfoPo);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillApplyFileInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO) {
		GoodwillApplyFileInfoPO goodwillApplyFileInfoPo = goodwillApplyFileInfoMapper.selectById(id);
		// 对对象进行赋值操作
		goodwillApplyFileInfoDTO.transDtoToPo(goodwillApplyFileInfoPo);
		// 执行更新
		int row = goodwillApplyFileInfoMapper.updateById(goodwillApplyFileInfoPo);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public void deleteById(Long id) throws ServiceBizException {
		if (StringUtils.isNullOrEmpty(id)) {
			throw new ServiceBizException("删除失败");
		}
		GoodwillApplyFileInfoPO po = goodwillApplyFileInfoMapper.selectById(id);
		if (!StringUtils.isNullOrEmpty(po)) {
			// po.setIsValid(Integer.valueOf(CommonConstants.DICT_IS_NO));
			po.setIsDeleted(true);
			goodwillApplyFileInfoMapper.updateById(po);
		}

	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillApplyFileInfoMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	@Override
	public List<GoodwillApplyFileInfoDTO> selectListBySql1(GoodwillApplyFileInfoDTO goodwillApplyFileInfoDto) {
		if (goodwillApplyFileInfoDto == null) {
			goodwillApplyFileInfoDto = new GoodwillApplyFileInfoDTO();
		}
		GoodwillApplyFileInfoPO complaintAttachmentPo = goodwillApplyFileInfoDto
				.transDtoToPo(GoodwillApplyFileInfoPO.class);
		List<GoodwillApplyFileInfoPO> list = goodwillApplyFileInfoMapper.selectListBySql1(complaintAttachmentPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			List<GoodwillApplyFileInfoDTO> targetList = list.stream().map(m -> m.transPoToDto(GoodwillApplyFileInfoDTO.class)).collect(Collectors.toList());
			//设置下载文件名称 add czm 20210715
			this.setDownLoadFileNameData(targetList);
			return targetList;
		}
	}

	/**
	 * 动态设置下载文件名称 add czm 20210715
	 */
	private void setDownLoadFileNameData(List<GoodwillApplyFileInfoDTO> targetList) {
		if(CollectionUtils.isNotEmpty(targetList)){
			int num = 0;
			for(GoodwillApplyFileInfoDTO fileDto:targetList){
				num++;
				//获取文件类型中文名称
				String fileTypeName = fileDto.getFileType()+"";
				if(fileDto.getFileType() != null){
					GoodwillFileTypeEnum goodwillFileTypeEnum =	GoodwillFileTypeEnum.fromCode(fileDto.getFileType());
					if(goodwillFileTypeEnum != null){
						fileTypeName = goodwillFileTypeEnum.display();
					}
				}
				//拼接下载的文件名(亲善申请单号-文件类型-序号-文件名)
				String downloadFileName = fileDto.getApplyNo()+"-"+fileTypeName+"-"+num+"-"+fileDto.getFileName();
				//设置下载文件名称
				fileDto.setDownloadFileName(downloadFileName);
				//临时借用设置附件名称
				fileDto.setFileName(downloadFileName);
			}
		}
	}

	@Override
	public List<GoodwillApplyFileInfoDTO> selectMaterialUploadList(GoodwillApplyFileInfoDTO goodwillApplyFileInfoDto) {
		if (goodwillApplyFileInfoDto == null) {
			goodwillApplyFileInfoDto = new GoodwillApplyFileInfoDTO();
		}
		GoodwillApplyFileInfoPO goodwillApplyFileInfoPo = goodwillApplyFileInfoDto
				.transDtoToPo(GoodwillApplyFileInfoPO.class);
		List<GoodwillApplyFileInfoPO> list = goodwillApplyFileInfoMapper
				.selectMaterialUploadList(goodwillApplyFileInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			List<GoodwillApplyFileInfoDTO> targetList = Lists.newArrayList();
			List<GoodwillApplyFileInfoDTO> srcList = list.stream().map(m -> m.transPoToDto(GoodwillApplyFileInfoDTO.class)).collect(Collectors.toList());
			//通过文件类型分组
			Map<Integer,List<GoodwillApplyFileInfoDTO>> gwFileMap = new LinkedHashMap<>();
			//通过文件类型分组
			srcList.stream().collect(Collectors.groupingBy(GoodwillApplyFileInfoDTO::getFileType,Collectors.toList())).forEach(gwFileMap::put);
			Set<Integer> fileTypeList = gwFileMap.keySet();
			if(CollectionUtils.isNotEmpty(fileTypeList)){
				for(Integer fileType : fileTypeList){
					List<GoodwillApplyFileInfoDTO> tmpList = gwFileMap.get(fileType);
					//设置下载文件名称 add czm 20210720
					this.setDownLoadFileNameData(tmpList);
					//加入分组集合
					targetList.addAll(tmpList);
				}
			}
			return targetList;
		}
	}
}
