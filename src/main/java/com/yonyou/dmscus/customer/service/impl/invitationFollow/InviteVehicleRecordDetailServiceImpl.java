package com.yonyou.dmscus.customer.service.impl.invitationFollow;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordDetailMapper;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordDetailPO;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordDetailService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 车辆邀约记录明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
@Service
public class InviteVehicleRecordDetailServiceImpl implements InviteVehicleRecordDetailService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteVehicleRecordDetailMapper inviteVehicleRecordDetailMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                         分页对象
     * @param inviteVehicleRecordDetailDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto
                     *   . InviteVehicleRecordDetailDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<InviteVehicleRecordDetailDTO> selectPageBysql(Page page, InviteVehicleRecordDetailDTO
            inviteVehicleRecordDetailDTO) {
        if (inviteVehicleRecordDetailDTO == null) {
            inviteVehicleRecordDetailDTO = new InviteVehicleRecordDetailDTO();
        }
        InviteVehicleRecordDetailPO inviteVehicleRecordDetailPO = inviteVehicleRecordDetailDTO.transDtoToPo
                (InviteVehicleRecordDetailPO.class);

        List<InviteVehicleRecordDetailPO> list = inviteVehicleRecordDetailMapper.selectPageBySql(page,
                inviteVehicleRecordDetailPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteVehicleRecordDetailDTO> result = list.stream().map(m -> m.transPoToDto
                    (InviteVehicleRecordDetailDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param inviteVehicleRecordDetailDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.InviteVehicleRecordDetailDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<InviteVehicleRecordDetailDTO> selectListBySql(InviteVehicleRecordDetailDTO
                                                                          inviteVehicleRecordDetailDTO) {
        if (inviteVehicleRecordDetailDTO == null) {
            inviteVehicleRecordDetailDTO = new InviteVehicleRecordDetailDTO();
        }
        InviteVehicleRecordDetailPO inviteVehicleRecordDetailPO = inviteVehicleRecordDetailDTO.transDtoToPo
                (InviteVehicleRecordDetailPO.class);
        List<InviteVehicleRecordDetailPO> list = inviteVehicleRecordDetailMapper.selectListBySql
                (inviteVehicleRecordDetailPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteVehicleRecordDetailDTO.class)).collect(Collectors
                    .toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.InviteVehicleRecordDetailDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public InviteVehicleRecordDetailDTO getById(Long id) {
        InviteVehicleRecordDetailPO inviteVehicleRecordDetailPO = inviteVehicleRecordDetailMapper.selectById(id);
        if (inviteVehicleRecordDetailPO != null) {
            return inviteVehicleRecordDetailPO.transPoToDto(InviteVehicleRecordDetailDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param inviteVehicleRecordDetailDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(InviteVehicleRecordDetailDTO inviteVehicleRecordDetailDTO) {
        //对对象进行赋值操作
        InviteVehicleRecordDetailPO inviteVehicleRecordDetailPO = inviteVehicleRecordDetailDTO.transDtoToPo
                (InviteVehicleRecordDetailPO.class);
        //执行插入
        int row = inviteVehicleRecordDetailMapper.insert(inviteVehicleRecordDetailPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                           主键ID
     * @param inviteVehicleRecordDetailDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, InviteVehicleRecordDetailDTO inviteVehicleRecordDetailDTO) {
        InviteVehicleRecordDetailPO inviteVehicleRecordDetailPO = inviteVehicleRecordDetailMapper.selectById(id);
        //对对象进行赋值操作
        inviteVehicleRecordDetailDTO.transDtoToPo(inviteVehicleRecordDetailPO);
        //执行更新
        int row = inviteVehicleRecordDetailMapper.updateById(inviteVehicleRecordDetailPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = inviteVehicleRecordDetailMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = inviteVehicleRecordDetailMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 添加邀约记录明细
     * @param inviteVehicleRecordDetailDTO
     * @return
     */
    @Override
    public Long addInviteVehicleRecordDetail(InviteVehicleRecordDetailDTO inviteVehicleRecordDetailDTO) {
        //对对象进行赋值操作
        InviteVehicleRecordDetailPO inviteVehicleRecordDetailPO = inviteVehicleRecordDetailDTO.transDtoToPo
                (InviteVehicleRecordDetailPO.class);
        //执行插入
        inviteVehicleRecordDetailMapper.insert(inviteVehicleRecordDetailPO);
        return inviteVehicleRecordDetailPO.getId();
    }

    /**
     * 按条件更新邀约记录明细
     * @param recordDetail
     */
    @Override
    public void updateInviteVehicleRecordDetail(InviteVehicleRecordDetailDTO recordDetail) {
        InviteVehicleRecordDetailPO inviteVehicleRecordDetailPO = recordDetail.transDtoToPo
                (InviteVehicleRecordDetailPO.class);
        LambdaUpdateWrapper<InviteVehicleRecordDetailPO> updateWrapper = new UpdateWrapper().lambda();
        updateWrapper.eq(InviteVehicleRecordDetailPO::getInviteId,recordDetail.getInviteId());
        updateWrapper.eq(InviteVehicleRecordDetailPO::getSaId,recordDetail.getSaId());
        updateWrapper.eq(InviteVehicleRecordDetailPO::getSaName,recordDetail.getSaName());
        inviteVehicleRecordDetailMapper.update(inviteVehicleRecordDetailPO,updateWrapper);
    }

    @Override
    public int selectNumByCount(Long inviteId) {
        return inviteVehicleRecordDetailMapper.selectNumByCount(inviteId);
    }
}
