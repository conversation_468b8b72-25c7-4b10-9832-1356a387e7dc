package com.yonyou.dmscus.customer.service.inviteSaAllocateRule;


import com.yonyou.dmscus.customer.entity.dto.inviteSaAllocateRule.InviteSaAllocateRuleDetailDTO;
import com.yonyou.dmscus.customer.service.common.IBaseService;

import java.util.List;


/**
 * <p>
 * SA分配规则明细表,通过经销商CODE与invite_sa_allocate_rule关联，用于经销商SA分配中平均分配 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
public interface InviteSaAllocateRuleDetailService extends IBaseService<InviteSaAllocateRuleDetailDTO> {

    List<InviteSaAllocateRuleDetailDTO> getSaAllocateRule(String dealerCode);

    int saveSaAllocateRuleDetail(List<InviteSaAllocateRuleDetailDTO> list);
}
