package com.yonyou.dmscus.customer.service.faultLight;

import com.yonyou.dmscus.customer.entity.dto.faultLight.*;
import com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.info.DiagnosisRecord;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.SaCustomerNumberDTO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightCluePO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightFollowRecordPO;
import com.yonyou.dmscus.customer.feign.dto.WarningDto;

import java.util.Date;
import java.util.List;

public interface FaultLightService {

    String saveSaCustomerNumber(SaCustomerNumberDTO saCustomerNumberDTO);

    List<TtFaultCallDetailsDTO> queryCallDetails(TtFaultCallDetailsDTO dto);

    void faultLightOrderCorrelation();

    void faultLightOrderCorrelationV4();

    /**
     * 预约单取消时关联线索关闭
     */
    void closeClueOnCancelledBookingOrder();

    List<RecordsDTO> queryCluesWarningInfo(WarningDto params);

    //待验证线索判定
    void doPendingVerificationClue();

    List<TtFaultLightClueDTO> queryDealerList(TtFaultLightClueDTO dto);

    //批量推送MQ
    void pushMessage(Long id, String followUpStatus, String bizStatus, Date updateTime);

    TtFaultLightFollowRecordPO converClueToRecord(TtFaultLightCluePO ttFaultLightCluePO);

    /**判断是否按时进店*/
    int getIntoOnTime(Date date, Date forecastTime);

    void faultLightStatusChange();

    void faultLightStatusRenovate();

    int submitSettlementRemind(String dealerCode, String vin);

    List<DiagnosisRecord> queryCluesDiagnosticInfo(Long cluesId, String cluesType);

    void handleDimClues(int pageSize);

    void updateForecastTime();

    void faultLightOrderCorrelationV5();
    /**Dtc诊断信息名称映射*/
    List<DtcCodeDTO> selectNameByEcuDtc(List<String> ecuDtc);
}
