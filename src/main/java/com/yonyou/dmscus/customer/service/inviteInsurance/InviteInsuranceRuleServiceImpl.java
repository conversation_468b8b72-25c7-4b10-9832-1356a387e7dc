package com.yonyou.dmscus.customer.service.inviteInsurance;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscloud.function.utils.common.StringUtils;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceRuleMapper;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleTaskMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceRuleDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceRuleVcdcParamsVo;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceRulePO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 邀约续保规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@Service
public class InviteInsuranceRuleServiceImpl implements InviteInsuranceRuleService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteInsuranceRuleMapper inviteInsuranceRuleMapper;
    @Autowired
    BusinessPlatformService businessPlatformService;
    @Resource
    InviteInsuranceVehicleTaskMapper inviteInsuranceVehicleTaskMapper;

    @Override
    public List<InviteInsuranceRuleDTO> getInviteInsuranceRuleDlr(InviteInsuranceRuleDTO inviteInsuranceRuleDTO) {
        if(inviteInsuranceRuleDTO==null) {
            inviteInsuranceRuleDTO=new InviteInsuranceRuleDTO();
        }
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        inviteInsuranceRuleDTO.setDealerCode(loginInfoDto.getOwnerCode());
        InviteInsuranceRulePO po = inviteInsuranceRuleDTO.transDtoToPo(InviteInsuranceRulePO.class);
        List<InviteInsuranceRulePO> list = inviteInsuranceRuleMapper.getInviteInsuranceRuleDlr(po);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteInsuranceRuleDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 保存邀约规则 -- 店端
     *
     * @param dto
     * @return
     */
    @Override
    public int saveInviteInsuranceRuleDlr(InviteInsuranceRuleDTO dto) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        InviteInsuranceRulePO po = dto.transDtoToPo(InviteInsuranceRulePO.class);
        if(StringUtils.isBlank(loginInfoDto.getOwnerCode())){
            throw new DALException("获取当前账户的信息为空,请重新登录!");
        }else{
            if(dto.getInviteType() == 1){
                InviteInsuranceRulePO clivtaRulePo = inviteInsuranceRuleMapper.getClivtaInviteInsuranceRuleExist(loginInfoDto.getOwnerCode());
                if(clivtaRulePo != null){
                    clivtaRulePo.setDayInAdvance(dto.getDayInAdvance());
                    clivtaRulePo.setCloseInterval(dto.getCloseInterval());
                    clivtaRulePo.setIsUse(dto.getIsUse());
                    int row1 =  inviteInsuranceRuleMapper.updateById(clivtaRulePo);
                    if (row1 > 0 && dto.getIsUse() == 1){
                        logger.info("店端修改续保规则，更新续保线索数据1111====");
                        inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleTaskDlr(loginInfoDto.getUserId(),clivtaRulePo);
                        inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleRecordDlr(loginInfoDto.getUserId(),clivtaRulePo);
                    }
                }else{
                    po.setDealerCode(loginInfoDto.getOwnerCode());
                    int row2 = inviteInsuranceRuleMapper.insert(po);
                    if (row2 > 0 && dto.getIsUse() == 1){
                        logger.info("店端修改续保规则，更新续保线索数据22222====");
                        inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleTaskDlr(loginInfoDto.getUserId(),po);
                        inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleRecordDlr(loginInfoDto.getUserId(),po);
                    }
                }
            }else{
                InviteInsuranceRulePO viRulePo = inviteInsuranceRuleMapper.getViInviteInsuranceRuleExist(loginInfoDto.getOwnerCode());
                if(viRulePo != null){
                    viRulePo.setDayInAdvance(dto.getDayInAdvance());
                    viRulePo.setCloseInterval(dto.getCloseInterval());
                    viRulePo.setIsUse(dto.getIsUse());
                    int row1 =  inviteInsuranceRuleMapper.updateById(viRulePo);
                    if (row1 > 0 && dto.getIsUse() == 1){
                        logger.info("店端修改续保规则，更新续保线索数据33333====");
                        inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleTaskDlr(loginInfoDto.getUserId(),viRulePo);
                        inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleRecordDlr(loginInfoDto.getUserId(),viRulePo);
                    }
                }else{
                    po.setDealerCode(loginInfoDto.getOwnerCode());
                    int row2 = inviteInsuranceRuleMapper.insert(po);
                    if (row2 > 0 && dto.getIsUse() == 1){
                        logger.info("店端修改续保规则，更新续保线索数据44444====");
                        inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleTaskDlr(loginInfoDto.getUserId(),po);
                        inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleRecordDlr(loginInfoDto.getUserId(),po);
                    }
                }
            }
        }
        return 1;
    }

    /**
     * @param vo
     * @return
     */
    @Override
    public IPage<InviteInsuranceRuleDTO> getInviteInsuranceRuleVcdc(InviteInsuranceRuleVcdcParamsVo vo) {
        List<InviteInsuranceRuleDTO> rs = new ArrayList<InviteInsuranceRuleDTO>();
        if (vo == null) {
            vo = new InviteInsuranceRuleVcdcParamsVo();
        }
        String areaId = null;
        if(null != vo.getAreaId()){
            areaId=vo.getAreaId();
        }else if(null != vo.getAreaManageId()){
            areaId=vo.getAreaManageId();
        }
        List<String> codes = businessPlatformService.getDealercodes(areaId,vo.getLargeAreaId(),vo.getDealerCode(),vo.getDealerName());
        for (String dealerCode : codes) {
            List<InviteInsuranceRuleDTO> list = inviteInsuranceRuleMapper.getInviteInsuranceRuleVcdc(dealerCode).stream().map(m -> m
                    .transPoToDto(InviteInsuranceRuleDTO.class)).collect(Collectors.toList());
            rs.addAll(list);
        }
        Page page = new Page(vo.getCurrentPage(), vo.getPageSize(), rs.size(), false);
        List<InviteInsuranceRuleDTO> beanPage = new ArrayList<InviteInsuranceRuleDTO>();
        int currIdx = (vo.getCurrentPage() > 1 ? (vo.getCurrentPage() -1) * vo.getPageSize() : 0);
        for (int i = 0; i < vo.getPageSize() && i < rs.size() - currIdx; i++) {
            InviteInsuranceRuleDTO ruleDTO = rs.get(currIdx + i);
            beanPage.add(ruleDTO);
        }
        page.setRecords(beanPage);
        return page;
    }

    /**
     * 保存邀约规则 -- 厂端
     *
     * @param dto
     * @return
     */
    @Override
    public int saveInviteInsuranceRule(InviteInsuranceRuleDTO dto) {

        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (null == loginInfoDto.getUserId()){
            throw new DALException("获取当前账户的信息为空,请重新登录!");
        }
        InviteInsuranceRulePO po = dto.transDtoToPo(InviteInsuranceRulePO.class);
        if(dto.getInviteType() == 1){
            InviteInsuranceRulePO clivtaRulePo = inviteInsuranceRuleMapper.getClivtaInviteInsuranceRuleExist("VCDC");
            if(clivtaRulePo != null){
                clivtaRulePo.setDayInAdvance(dto.getDayInAdvance());
                clivtaRulePo.setCloseInterval(dto.getCloseInterval());
                clivtaRulePo.setIsUse(dto.getIsUse());
                int rowUp = inviteInsuranceRuleMapper.updateById(clivtaRulePo);
                if(rowUp > 0){
                    List<InviteInsuranceRulePO> poList = inviteInsuranceRuleMapper.getClivtaInviteInsuranceRuleCount("VCDC");
                    if(CommonUtils.isNullOrEmpty(poList)){
                        logger.info("厂端修改续保规则，更新续保线索数据1111====");
                        inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleTask(loginInfoDto.getUserId(),clivtaRulePo);
                        inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleRecord(loginInfoDto.getUserId(),clivtaRulePo);
                    }else{
                        int rowOther = inviteInsuranceRuleMapper.updateAllClivtaInsuranceRule(loginInfoDto.getUserId(),clivtaRulePo);
                        if(rowOther > 0  && dto.getIsUse() == 1){
                            logger.info("厂端修改续保规则，更新续保线索数据2222====");
                            inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleTask(loginInfoDto.getUserId(),clivtaRulePo);
                            inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleRecord(loginInfoDto.getUserId(),clivtaRulePo);
                        }
                    }
                }
            }else{
                po.setDealerCode("VCDC");
                int rowIn = inviteInsuranceRuleMapper.insert(po);
                if(rowIn > 0){
                    List<InviteInsuranceRulePO> poList = inviteInsuranceRuleMapper.getClivtaInviteInsuranceRuleCount("VCDC");
                    if(CommonUtils.isNullOrEmpty(poList)){
                        logger.info("厂端修改续保规则，更新续保线索数据3333====");
                        inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleTask(loginInfoDto.getUserId(),po);
                        inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleRecord(loginInfoDto.getUserId(),po);
                    }else{
                        int rowOther = inviteInsuranceRuleMapper.updateAllClivtaInsuranceRule(loginInfoDto.getUserId(),po);
                        if(rowOther > 0 && dto.getIsUse() == 1){
                            logger.info("厂端修改续保规则，更新续保线索数据4444====");
                            inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleTask(loginInfoDto.getUserId(),po);
                            inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleRecord(loginInfoDto.getUserId(),po);
                        }
                    }
                }
            }
        }else{
            InviteInsuranceRulePO viRulePo = inviteInsuranceRuleMapper.getViInviteInsuranceRuleExist("VCDC");
            if(viRulePo != null){
                viRulePo.setDayInAdvance(dto.getDayInAdvance());
                viRulePo.setCloseInterval(dto.getCloseInterval());
                viRulePo.setIsUse(dto.getIsUse());
                int rowUp = inviteInsuranceRuleMapper.updateById(viRulePo);
                if(rowUp > 0){
                    List<InviteInsuranceRulePO> poList = inviteInsuranceRuleMapper.getViInviteInsuranceRuleCount("VCDC");
                    if(CommonUtils.isNullOrEmpty(poList)){
                        logger.info("厂端修改续保规则，更新续保线索数据1111====");
                        inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleTask(loginInfoDto.getUserId(),viRulePo);
                        inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleRecord(loginInfoDto.getUserId(),viRulePo);
                    }else{
                        int rowOther = inviteInsuranceRuleMapper.updateAllViInsuranceRule(loginInfoDto.getUserId(),viRulePo);
                        if(rowOther > 0  && dto.getIsUse() == 1){
                            logger.info("厂端修改续保规则，更新续保线索数据2222====");
                            inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleTask(loginInfoDto.getUserId(),viRulePo);
                            inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleRecord(loginInfoDto.getUserId(),viRulePo);
                        }
                    }
                }
            }else{
                po.setDealerCode("VCDC");
                int rowIn = inviteInsuranceRuleMapper.insert(po);
                if(rowIn > 0){
                    List<InviteInsuranceRulePO> poList = inviteInsuranceRuleMapper.getViInviteInsuranceRuleCount("VCDC");
                    if(CommonUtils.isNullOrEmpty(poList)){
                        logger.info("厂端修改续保规则，更新续保线索数据3333====");
                        inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleTask(loginInfoDto.getUserId(),po);
                        inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleRecord(loginInfoDto.getUserId(),po);
                    }else{
                        int rowOther = inviteInsuranceRuleMapper.updateAllViInsuranceRule(loginInfoDto.getUserId(),po);
                        if(rowOther > 0 && dto.getIsUse() == 1){
                            logger.info("厂端修改续保规则，更新续保线索数据4444====");
                            inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleTask(loginInfoDto.getUserId(),po);
                            inviteInsuranceVehicleTaskMapper.updateInsuranceVehicleRecord(loginInfoDto.getUserId(),po);
                        }
                    }
                }
            }
        }
        return 1;
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param inviteInsuranceRuleDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.part.entity.dto.InviteRuleDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<InviteInsuranceRuleDTO> selectListBySql(InviteInsuranceRuleDTO inviteInsuranceRuleDTO) {
        if (inviteInsuranceRuleDTO == null) {
            inviteInsuranceRuleDTO = new InviteInsuranceRuleDTO();
        }
        InviteInsuranceRulePO inviteInsuranceRulePO = inviteInsuranceRuleDTO.transDtoToPo(InviteInsuranceRulePO.class);
        List<InviteInsuranceRulePO> list = inviteInsuranceRuleMapper.selectListBySqlVcdc(inviteInsuranceRulePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteInsuranceRuleDTO.class)).collect(Collectors.toList());
        }
    }

}
