package com.yonyou.dmscus.customer.service.voicemanage;


import com.yonyou.dmscus.customer.entity.dto.voicemanage.CallVoiceDTO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallVoicePO;
import com.yonyou.dmscus.customer.service.common.IBaseService;

import java.util.List;


/**
 * <p>
 * 通话录音 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
public interface CallVoiceService extends IBaseService<CallVoiceDTO> {

	void getCallVoiceByAi();
	
	void sendDSCCallVoice();

	void insertList(List<CallVoicePO> callVoicePOList);
}
