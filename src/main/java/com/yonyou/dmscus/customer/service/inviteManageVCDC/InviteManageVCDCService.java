package com.yonyou.dmscus.customer.service.inviteManageVCDC;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dto.InvitationDeleteParamDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleDto;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteManageVCDC.AllocateDealerDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 车辆邀约记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */

public interface InviteManageVCDCService {

    IPage<InviteVehicleDto> getInviteVehicleRecord(Page page, InviteVehicleRecordDTO inviteVehicleRecordDTO, List<String> nameList);


    int allocateDealer(AllocateDealerDTO dto);

    List<Map> exportExcelinviteVehicleRecord(InviteVehicleRecordDTO inviteVehicleRecordDTO);

    InviteVehicleRecordDTO getInviteVehicleRecordInfo(String vin, Long id);

    /**
     * 店端查询跟进记录
     * @param vin
     * @param id
     * @return
     */
    List<InviteVehicleRecordDetailDTO> getInviteVehicleRecordInfoDlr(String vin, Long id, String ownerCode);

    void exportExcel(InviteVehicleRecordDTO dto, HttpServletResponse response, List<String> inviteName) throws Exception;

    IPage<InviteVehicleRecordDetailDTO> getInviteVehicleRecordInfoHistoryDlr(Page page,String vin, String ownerCode);

    /**
     * 邀约线索管理导出（下载中心）
     * @param inviteVehicleRecordDTO
     */
	void exportExcelByDown(InviteVehicleRecordDTO inviteVehicleRecordDTO);

	/**
	 * 下载中心回调接口
	 * @param inviteVehicleRecordDTO
	 * @param pSize 
	 * @param offset 
	 * @param page
	 * @return
	 */
	List<Map> downLoadExportExcel(InviteVehicleRecordDTO inviteVehicleRecordDTO, Integer offset, Integer pSize);

	void deleteSelfCreateInvitation(InvitationDeleteParamDTO param);
}
