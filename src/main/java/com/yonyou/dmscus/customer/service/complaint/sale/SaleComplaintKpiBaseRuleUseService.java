package com.yonyou.dmscus.customer.service.complaint.sale;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiTest;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintKpiBaseRuleUseDTO;

import java.util.List;

/**
 * <p>
 * 销售客户投诉KP基础规则使用表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-21
 */
public interface SaleComplaintKpiBaseRuleUseService  {
    IPage<SaleComplaintKpiBaseRuleUseDTO> selectPageBysql(Page page, SaleComplaintKpiBaseRuleUseDTO saleComplaintKpiBaseRuleUseDTO);
    List<SaleComplaintKpiBaseRuleUseDTO> selectListBySql(SaleComplaintKpiBaseRuleUseDTO saleComplaintKpiBaseRuleUseDTO);
    SaleComplaintKpiBaseRuleUseDTO getById(Long id);
    int insert(SaleComplaintKpiBaseRuleUseDTO saleComplaintKpiBaseRuleUseDTO);
    int update(Long id, SaleComplaintKpiBaseRuleUseDTO saleComplaintKpiBaseRuleUseDTO);
    int deleteById(Long id);
    int deleteBatchIds(String ids);

    List<ComplaintKpiBaseRuleUseTestDTO> selectListBySql1(ComplaintKpiBaseRuleUseTestDTO complaintKpiBaseRuleUserDTO);

    int updateWarnValue(List<ComplaintKpiTest> list);
}
