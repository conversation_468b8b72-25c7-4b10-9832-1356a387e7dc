package com.yonyou.dmscus.customer.service.impl.voicemanage;

import java.util.Map;

public  abstract class AbstractWorkNumberSerivce implements WorkNumberService {

    abstract Map<String, Object> register(String callId, String holderNumber, String workNumbe, String customNumber, String userOrderId);

    abstract Map<String, Object> info(String workNumbe);

    @Override
    public boolean support(String operator) {
        return false;
    }

    @Override
    public Map<String, Object> bind(String extension, String mobilePhone) {
        return null;
    }

    @Override
    public Map<String, Object> unBind(String extension) {
        return null;
    }

    @Override
    public boolean isSuccess(String methodType,Map<String, Object> result) {
        return false;
    }
}
