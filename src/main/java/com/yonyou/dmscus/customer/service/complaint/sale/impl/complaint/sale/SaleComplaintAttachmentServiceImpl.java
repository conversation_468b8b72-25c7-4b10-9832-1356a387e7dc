package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentTestDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAttachmentPO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintAttachmentPO;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintAttachmentMapper;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintAttachmentService;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintAttachmentDTO;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 销售客户投诉附件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-11
 */
@Service
public class SaleComplaintAttachmentServiceImpl implements SaleComplaintAttachmentService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    SaleComplaintAttachmentMapper saleComplaintAttachmentMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                       分页对象
     * @param saleComplaintAttachmentDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintAttachmentDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<SaleComplaintAttachmentDTO> selectPageBysql(Page page, SaleComplaintAttachmentDTO saleComplaintAttachmentDTO) {
        if (saleComplaintAttachmentDTO == null) {
            saleComplaintAttachmentDTO = new SaleComplaintAttachmentDTO();
        }
        SaleComplaintAttachmentPO saleComplaintAttachmentPO = saleComplaintAttachmentDTO.transDtoToPo(SaleComplaintAttachmentPO.class);

        List<SaleComplaintAttachmentPO> list = saleComplaintAttachmentMapper.selectPageBySql(page, saleComplaintAttachmentPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<SaleComplaintAttachmentDTO> result = list.stream().map(m -> m.transPoToDto(SaleComplaintAttachmentDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param saleComplaintAttachmentDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintAttachmentDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<SaleComplaintAttachmentDTO> selectListBySql(SaleComplaintAttachmentDTO saleComplaintAttachmentDTO) {
        if (saleComplaintAttachmentDTO == null) {
            saleComplaintAttachmentDTO = new SaleComplaintAttachmentDTO();
        }
        SaleComplaintAttachmentPO saleComplaintAttachmentPO = saleComplaintAttachmentDTO.transDtoToPo(SaleComplaintAttachmentPO.class);
        List<SaleComplaintAttachmentPO> list = saleComplaintAttachmentMapper.selectListBySql(saleComplaintAttachmentPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(SaleComplaintAttachmentDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintAttachmentDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public SaleComplaintAttachmentDTO getById(Long id) {
        SaleComplaintAttachmentPO saleComplaintAttachmentPO = saleComplaintAttachmentMapper.selectById(id);
        if (saleComplaintAttachmentPO != null) {
            return saleComplaintAttachmentPO.transPoToDto(SaleComplaintAttachmentDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据" );
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param saleComplaintAttachmentDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(SaleComplaintAttachmentDTO saleComplaintAttachmentDTO) {
        //对对象进行赋值操作
        SaleComplaintAttachmentPO saleComplaintAttachmentPO = saleComplaintAttachmentDTO.transDtoToPo(SaleComplaintAttachmentPO.class);
        //执行插入
        int row = saleComplaintAttachmentMapper.insert(saleComplaintAttachmentPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                         主键ID
     * @param saleComplaintAttachmentDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, SaleComplaintAttachmentDTO saleComplaintAttachmentDTO) {
        SaleComplaintAttachmentPO saleComplaintAttachmentPO = saleComplaintAttachmentMapper.selectById(id);
        //对对象进行赋值操作
        saleComplaintAttachmentDTO.transDtoToPo(saleComplaintAttachmentPO);
        //执行更新
        int row = saleComplaintAttachmentMapper.updateById(saleComplaintAttachmentPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = saleComplaintAttachmentMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据" );
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = saleComplaintAttachmentMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据" );
        } else {
            return deleteCount;
        }
    }

    @Override
    public List<ComplaintAttachmentTestDTO> selectListBySql1(SaleComplaintAttachmentDTO saleComplaintAttachmentDTO) {
        if(saleComplaintAttachmentDTO ==null){
            saleComplaintAttachmentDTO =new SaleComplaintAttachmentDTO();
        }
        SaleComplaintAttachmentPO saleComplaintAttachmentPO =saleComplaintAttachmentDTO.transDtoToPo(SaleComplaintAttachmentPO.class);
        List<ComplaintAttachmentTestDTO>list= saleComplaintAttachmentMapper.selectListBySql1(saleComplaintAttachmentDTO);
        if(CommonUtils.isNullOrEmpty(list)){
            return new ArrayList<>();
        }else{
            return list;
        }
    }
}
