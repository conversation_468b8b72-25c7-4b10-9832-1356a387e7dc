package com.yonyou.dmscus.customer.service.inviteInsurance;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.service.excel.ExcelRead;
import com.yonyou.dmscloud.framework.service.excel.ExcelReadCallBack;
import com.yonyou.dmscloud.framework.service.excel.impl.AbstractExcelReadCallBack;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscloud.function.utils.common.StringUtils;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceRuleMapper;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleRecordImportMapper;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleRecordMapper;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleTaskMapper;
import com.yonyou.dmscus.customer.dto.CheckRepairOrderDTO;
import com.yonyou.dmscus.customer.dto.QueryVehicleOwnerDataVo;
import com.yonyou.dmscus.customer.dto.RepairOrderVO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InsuranceRecordImport;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordImportDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleTaskDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.QueryRoleUserByCompanyCodeDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.UserInfoOutDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceRulePO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordImportPO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleTaskPO;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.middleInterface.OwnerVehicleVO;
import com.yonyou.dmscus.customer.service.common.IMiddleGroundVehicleService;
import com.yonyou.dmscus.customer.service.middleground.BasicdataCenterService;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;


/**
 * <p>
 * 车辆续保线索导入临时表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-19
 */
@Service
public class InviteInsuranceVehicleRecordImportServiceImpl extends ServiceImpl<InviteInsuranceVehicleRecordImportMapper,InviteInsuranceVehicleRecordImportPO> implements InviteInsuranceVehicleRecordImportService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteInsuranceVehicleRecordImportMapper inviteInsuranceVehicleRecordImportMapper;
    @Resource
    ExcelRead<InsuranceRecordImport> excelReadService;
    @Autowired
    ReportCommonClient reportCommonClient;
    @Resource
    BasicdataCenterService basicdataCenterService;
    @Resource
    InviteInsuranceRuleMapper inviteInsuranceRuleMapper;
    @Resource
    IMiddleGroundVehicleService iMiddleGroundVehicleService;
    @Resource
    InviteInsuranceVehicleTaskMapper inviteInsuranceVehicleTaskMapper;
    @Resource
    InviteInsuranceVehicleRecordMapper inviteInsuranceVehicleRecordMapper;

    /**
     * 读excel文件数据保存至临时表，返回校验结果 国产车保修清单
     *
     * @param importFile
     * @throws Exception
     * <AUTHOR>
     * @since 2020/3/30
     */
    @Override
    public ImportTempResult<InviteInsuranceVehicleRecordImportDTO> importInsuranceRecordTemp(MultipartFile importFile)
            throws Exception {
        // 获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (StringUtils.isNullOrEmpty(loginInfoDto.getUserId())) {
            throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
        }
        // 删除临时表数据 该登录用户的数据
        inviteInsuranceVehicleRecordImportMapper.deleteAll(loginInfoDto.getUserId());
        List<InviteInsuranceVehicleRecordImportPO> tmpDtoList = new ArrayList<InviteInsuranceVehicleRecordImportPO>();
        // 匿名内部类
        excelReadService.analyzeExcelFirstSheet(importFile, new AbstractExcelReadCallBack<InsuranceRecordImport>(
                InsuranceRecordImport.class, new ExcelReadCallBack<InsuranceRecordImport>() {
            private Integer seq = 1;

            @Override
            public void readRowCallBack(InsuranceRecordImport rowDto, boolean isValidateSucess) {
                try {
                    if (isValidateSucess) {
                        // 插入临时表
                        // 1. 保存导入信息
                        InviteInsuranceVehicleRecordImportPO detailPO = new InviteInsuranceVehicleRecordImportPO();
                        detailPO.setLineNumber(++seq);
                        detailPO.setDealerCode(loginInfoDto.getOwnerCode());
                        detailPO.setVin(rowDto.getVin());
                        if (rowDto.getViFinishDate() == null) {
                            detailPO.setViFinishDate(null);
                            detailPO.setIsError(0);
                        } else {
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                            simpleDateFormat.setLenient(false);
                            detailPO.setViFinishDate(simpleDateFormat.format(rowDto.getViFinishDate()));
                            detailPO.setIsError(0);
                        }
                        if (rowDto.getClivtaFinishDate() == null) {
                            detailPO.setClivtaFinishDate(null);
                            detailPO.setIsError(0);
                        } else {
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                            simpleDateFormat.setLenient(false);
                            detailPO.setClivtaFinishDate(simpleDateFormat.format(rowDto.getClivtaFinishDate()));
                            detailPO.setIsError(0);
                        }
                        detailPO.setSaId(rowDto.getInsureCustomerInfo());
                        detailPO.setInsuranceName(rowDto.getInsuranceName());
                        tmpDtoList.add(detailPO);
                    }
                } catch (Exception e) {
                    throw new ServiceBizException(e);
                }
            }
        }));
        if (!CommonUtils.isNullOrEmpty(tmpDtoList)) {
            int listSize = tmpDtoList.size();
            int toIndex = CommonConstants.INSERT_BATCH_LIMIT_COUNT;
            for (int i = 0; i < tmpDtoList.size(); i += toIndex) {
                if (i + toIndex > listSize) {        //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
                    toIndex = listSize - i;
                }
                List<InviteInsuranceVehicleRecordImportPO> insertList = tmpDtoList.subList(i, i + toIndex);
                inviteInsuranceVehicleRecordImportMapper.insertInsuranceImportData(insertList, loginInfoDto.getUserId());
            }
        }
        ImportTempResult<InviteInsuranceVehicleRecordImportDTO> importResult = this.checkTmpData();
        return importResult;
    }

    /**
     * 校验更新临时表，返回校验结果对象
     *
     * <AUTHOR>
     * @since 2020/03/19
     */
    private ImportTempResult<InviteInsuranceVehicleRecordImportDTO> checkTmpData() {
        ImportTempResult<InviteInsuranceVehicleRecordImportDTO> importResult = new ImportTempResult<>();
        // 获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (StringUtils.isNullOrEmpty(loginInfoDto.getUserId())) {
            throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
        }
        logger.info("导入续保线索 ==== 校验vin是否为空");
        // 校验vin是否为空
        inviteInsuranceVehicleRecordImportMapper.updateCheckVinEmpty(loginInfoDto.getUserId());
        // 校验商业险到期日期+商业险到期日期
        inviteInsuranceVehicleRecordImportMapper.updateCheckDateEmpty(loginInfoDto.getUserId());

        List<InviteInsuranceVehicleRecordImportPO> poList = inviteInsuranceVehicleRecordImportMapper.getSuccessData(loginInfoDto.getUserId());
        if (!CommonUtils.isNullOrEmpty(poList)) {
            List<String> vinList = new ArrayList<>();
            List<String> codeList = new ArrayList<>();
            for (InviteInsuranceVehicleRecordImportPO importPO : poList) {
                vinList.add(importPO.getVin());
                codeList.add(importPO.getSaId());
            }
            if (!CommonUtils.isNullOrEmpty(vinList)) {
                inviteInsuranceVehicleRecordImportMapper.deleteVehicleOwnerByUserId(loginInfoDto.getUserId());
                int listSize = vinList.size();
                int toIndex = 300;
                for (int i = 0; i < vinList.size(); i += toIndex) {
                    if (i + toIndex > listSize) {        //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
                        toIndex = listSize - i;
                    }
                    List<String> stringList = vinList.subList(i, i + toIndex);
                    QueryVehicleOwnerDataVo dataVo = reportCommonClient.selectVehicleOwnerInfoByList(stringList);
                    logger.info("导入续保线索 ==== 通过ADB查询车主车辆信息: {}", dataVo);
                    if (null != dataVo && !CommonUtils.isNullOrEmpty(dataVo.getVehicleOwnerInfoList())) {
                        inviteInsuranceVehicleRecordImportMapper.insertVehicleOwner(dataVo.getVehicleOwnerInfoList(), loginInfoDto.getUserId());
                    }
                }
            }
            codeList = codeList.stream().distinct().collect(Collectors.toList());
            if (!CommonUtils.isNullOrEmpty(codeList)) {
                //通过经销商代码和角色代码查询员工列表  -- 调中台接口
                QueryRoleUserByCompanyCodeDTO codeDTO = new QueryRoleUserByCompanyCodeDTO();
                List<String> roleCode = new ArrayList<>();
                roleCode.add("BXZY");
                codeDTO.setCompanyCode(loginInfoDto.getOwnerCode());
                codeDTO.setRoleCode(roleCode);
                codeDTO.setIsOnjob(10081001);
                List<UserInfoOutDTO> roleUserList = basicdataCenterService.queryRoleUserByCompanyCode(codeDTO);
                logger.info("导入续保线索 ==== 通过中台接口查询保险专员信息: {}", roleUserList);
                if (!CommonUtils.isNullOrEmpty(roleUserList)) {
                    inviteInsuranceVehicleRecordImportMapper.deleteRoleUserByUserId(loginInfoDto.getUserId());
                    inviteInsuranceVehicleRecordImportMapper.insertRoleUser(roleUserList, loginInfoDto.getUserId());
                }
            }
        }
        inviteInsuranceVehicleRecordImportMapper.updateExistVinOnMiddle(loginInfoDto.getUserId());
        inviteInsuranceVehicleRecordImportMapper.updateImportInsuranceRecord(loginInfoDto.getUserId());

        // 查询正确数据数
        importResult.setSuccessCount(inviteInsuranceVehicleRecordImportMapper.querySuccessCount(loginInfoDto.getUserId()));
        return importResult;
    }

    @SneakyThrows
    @Override
    public void importInsuranceRecord() throws ServiceBizException {

        // 获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (StringUtils.isNullOrEmpty(loginInfoDto.getUserId())) {
            throw new ServiceBizException("获取当前登录人信息失败，请重新登录!");
        }
        // 查询成功项
        List<InviteInsuranceVehicleRecordImportPO> listSuccess = inviteInsuranceVehicleRecordImportMapper.getSuccessData(loginInfoDto.getUserId());

        logger.info("续保线索临时表数据导入正式表开始 ===== {}", listSuccess);
        if (!CommonUtils.isNullOrEmpty(listSuccess)) {
            InviteInsuranceRulePO viRulePo = inviteInsuranceRuleMapper.getViInviteInsuranceRuleDlr(loginInfoDto.getOwnerCode());
            InviteInsuranceRulePO clvitaRulePo = inviteInsuranceRuleMapper.getClivtaInviteInsuranceRuleDlr(loginInfoDto.getOwnerCode());
            List<InviteInsuranceVehicleRecordImportPO> listVi = new ArrayList<>();
            List<InviteInsuranceVehicleRecordImportPO> listCli = new ArrayList<>();
            List<String> vinList = new ArrayList<>();
            for (InviteInsuranceVehicleRecordImportPO importPO : listSuccess) {
                if (null != importPO.getViFinishDate() && null == importPO.getClivtaFinishDate()) {//商业险
                    listVi.add(importPO);
                } else if (null == importPO.getViFinishDate() && null != importPO.getClivtaFinishDate()) {//交强险
                    listCli.add(importPO);
                } else if (null != importPO.getViFinishDate() && null != importPO.getClivtaFinishDate()) {//商业险+交强险
                    listVi.add(importPO);
                    listCli.add(importPO);
                } else {
                    System.out.println("商业险到期日期+交强险到期日期都为空");
                }
                vinList.add(importPO.getVin());
            }
            vinList = vinList.stream().distinct().collect(Collectors.toList());
            int viInsert = 0;
            int cliInsert = 0;

            //交强险
            if (!CommonUtils.isNullOrEmpty(listCli)) {
                Integer cliDay;
                if (null != clvitaRulePo && null != clvitaRulePo.getDayInAdvance()) {
                    cliDay = clvitaRulePo.getDayInAdvance();
                } else {
                    cliDay = 90;
                }

                List<InviteInsuranceVehicleRecordImportPO> insertList = new ArrayList<>();

                for (InviteInsuranceVehicleRecordImportPO recordImportPo : listCli) {
                    //时间转换为date做计算
                    Date clDate = this.stringOverDate(recordImportPo.getClivtaFinishDate());
                    //查询历史线索
                    List<InviteInsuranceVehicleRecordPO> inviteInsuranceVehicleRecord = this.getInviteInsuranceVehicleRecordPOS(recordImportPo);
                    if (!CollectionUtils.isEmpty(inviteInsuranceVehicleRecord)) {
                        logger.info("orderStatus日志查看{}", inviteInsuranceVehicleRecord.get(0).getOrderStatus());
                    }
                    List<InviteInsuranceVehicleRecordPO> inviteVehicleRecord = inviteInsuranceVehicleRecord.stream().filter(r -> Objects.equals(r.getOrderStatus(), CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681001) ||
                                    Objects.equals(r.getOrderStatus(), CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681002) ||
                                    Objects.equals(r.getOrderStatus(), CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681004))
                            .collect(toList());
                    logger.info("导入线索查询非关闭状态的线索{}", inviteVehicleRecord);
                    boolean flag = true;
                    for (InviteInsuranceVehicleRecordPO vehicleRecordPo : inviteVehicleRecord) {
                        long start = vehicleRecordPo.getAdviseInDate().getTime();
                        long end = vehicleRecordPo.getAdviseInDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().plusMonths(12L).toInstant(ZoneOffset.of("+8")).toEpochMilli();
                        assert clDate != null;
                        Boolean result = (clDate.getTime() >= start && clDate.getTime() < end);
                        logger.info("start{},end{},result{}", start, end, result);
                        if (result) {
                            flag = false;
                        }
                    }
                    //在记录表中没记录则新增
                    if (flag) {
                        insertList.add(recordImportPo);
                    }
                }

                //新增交强险线索
                if (!CollectionUtils.isEmpty(insertList)) {
                    cliInsert = inviteInsuranceVehicleRecordImportMapper.importClivtaInsuranceData(insertList, loginInfoDto.getUserId(), cliDay);
                }

            }
            //商业险
            if (!CommonUtils.isNullOrEmpty(listVi)) {
                Integer viDay;
                if (null != viRulePo && null != viRulePo.getDayInAdvance()) {
                    viDay = viRulePo.getDayInAdvance();
                } else {
                    viDay = 90;
                }

                List<InviteInsuranceVehicleRecordImportPO> insertList = new ArrayList<>();

                for (InviteInsuranceVehicleRecordImportPO recordImportPo : listVi) {
                    //时间转换为date做计算
                    Date viDate = this.stringOverDate(recordImportPo.getViFinishDate());
                    //查询历史线索
                    List<InviteInsuranceVehicleRecordPO> inviteInsuranceVehicleRecordPOS = this.getInviteInsuranceVehicleRecordPOS(recordImportPo);
                    List<InviteInsuranceVehicleRecordPO> recordArr = inviteInsuranceVehicleRecordPOS.stream().filter(e -> {
                            long start = e.getAdviseInDate().getTime();
                            long end = e.getAdviseInDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().plusMonths(12L).toInstant(ZoneOffset.of("+8")).toEpochMilli();
                            Boolean result = (viDate.getTime() >= start && viDate.getTime() < end);
                            logger.info("start{},end{},result{}", start, end, result);
                            return result;
                        }).collect(toList());
                    logger.info("recordArr---->" + JSONObject.toJSONString(recordArr));


                    if (recordArr.stream().filter(r -> r.getClueType() == 1).count() == recordArr.size()) {
                        List<InviteInsuranceVehicleRecordPO> list = recordArr.stream().filter(r -> Objects.equals(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681001, r.getOrderStatus()) ||
                                        Objects.equals(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681002, r.getOrderStatus()) ||
                                        Objects.equals(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681004, r.getOrderStatus()))
                                .sorted(Comparator.comparing(InviteInsuranceVehicleRecordPO::getAdviseInDate).reversed())
                                .collect(toList());
                        logger.info("导入商业险更改交强险数据---->" + JSONObject.toJSONString(list));
                        if (!CollectionUtils.isEmpty(list)) {
                            InviteInsuranceVehicleRecordPO entity = list.get(0);
                            //线索完成状态：已完成更改成未完成
                            this.updateRecordOrderStatus(viDay, viDate, entity);
                            logger.info("vin{}经销商{}更新12月内已下发交强险线索{}", entity.getVin(), entity.getDealerCode(), entity);
                        } else {
                            insertList.add(recordImportPo);
                        }
                    } else {
                        //如果已存在线索都是关闭的 新增
                        if (recordArr.size() == recordArr.stream().filter(r -> (Objects.equals(r.getOrderStatus(), CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681005) ||
                                Objects.equals(r.getOrderStatus(), CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681006))).count()) {
                            insertList.add(recordImportPo);
                            //如果商业险都是关闭的并且有交强险是未关闭状态  更新交强险线索
                        } else if ((recordArr.stream().filter(r -> r.getClueType() == 2).count() == recordArr.stream().filter(r -> ((Objects.equals(r.getOrderStatus(), CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681005) ||
                                Objects.equals(r.getOrderStatus(), CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681006)) && r.getClueType() == 2)).count()) && boof(recordArr)){
                                InviteInsuranceVehicleRecordPO entity = recordArr.stream().filter(r -> r.getClueType() == 1).findFirst().orElse(recordArr.get(0));
                                this.updateRecordOrderStatus(viDay, viDate, entity);
                                logger.info("商业险都是关闭---交强险是未关闭更新交强险{}", entity);
                        }
                    }

                }

                if (!CollectionUtils.isEmpty(insertList)) {
                    viInsert = inviteInsuranceVehicleRecordImportMapper.importViInsuranceData(insertList, loginInfoDto.getUserId(), viDay);
                }

            }

            if (viInsert > 0 || cliInsert > 0) { //根据投保单信息   修改续保线索：续保客户类型
                inviteInsuranceVehicleRecordImportMapper.updateInsuranceRecordData(vinList, loginInfoDto.getUserId(), loginInfoDto.getOwnerCode());
            }
        } else {
            System.out.println("文件读入成功后正确数据为空，请检查文件重试!");
        }
    }

    private boolean boof(List<InviteInsuranceVehicleRecordPO> recordArr) {
      return   recordArr.stream().anyMatch(r -> r.getClueType() == 1 && (Objects.equals(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681001, r.getOrderStatus()) ||
                Objects.equals(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681002, r.getOrderStatus()) ||
                Objects.equals(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681004, r.getOrderStatus())));
    }

    private void updateRecordOrderStatus(Integer viDay, Date viDate, InviteInsuranceVehicleRecordPO entity) {
        if (Objects.equals(entity.getOrderStatus(), CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681002)) {
            entity.setOrderStatus(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681001);
        }
        entity.setAdviseInDate(viDate);
        Calendar c = Calendar.getInstance();
        c.setTime(viDate);
        c.add(Calendar.DATE, -viDay);
        entity.setPlanFollowDate(c.getTime());
        entity.setClueType(2);
        entity.setFollowStatus(CommonConstants.FOLLOW_STATUS_I);
        inviteInsuranceVehicleRecordMapper.updateById(entity);
    }

    private Date stringOverDate(String recordImportPo) {
        DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        Date viDate = null;
        try{
            viDate = Date.from(LocalDateTime.parse(recordImportPo,dtf2).toInstant(ZoneOffset.of("+8")));
        } catch (Exception e){
          logger.info("stringOverDate:error-{}",e);
        }
        return viDate;
    }

    private List<InviteInsuranceVehicleRecordPO> getInviteInsuranceVehicleRecordPOS(InviteInsuranceVehicleRecordImportPO recordImportPo) {
        LambdaQueryWrapper<InviteInsuranceVehicleRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InviteInsuranceVehicleRecordPO::getDealerCode, recordImportPo.getDealerCode())
                .eq(InviteInsuranceVehicleRecordPO::getVin, recordImportPo.getVin())
                .eq(InviteInsuranceVehicleRecordPO::getIsDeleted, 0);
        return inviteInsuranceVehicleRecordMapper.selectList(queryWrapper);
    }


    /**
     * 24个月进厂逻辑 生成续保任务  -- 线索导入
     */
    private void insertInsureTaskImportData(String vin, String dealerCode, String finishDate1, Integer viClivta, InviteInsuranceVehicleRecordImportPO importPO) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date finishDate = simpleDateFormat.parse(finishDate1);
        List<String> listStr1 = this.selectRepairOrderByVin2(vin, finishDate, dealerCode, 1);
        if (CommonUtils.isNullOrEmpty(listStr1)) {
            return;
        } else {
            Date finishDate2 = null;
            for (String code : listStr1) {
                if (null != code && code.equals(dealerCode)) {
                    continue;
                } else {
                    finishDate2 = this.insertInsuranceTaskByRepairNo(finishDate, importPO, code, viClivta);
                }
            }
            if (null != finishDate2) {
                List<String> listStr2 = this.selectRepairOrderByVin2(vin, finishDate2, dealerCode, 2);
                List<String> listStr3 = listStr2.stream().filter(item -> !listStr1.contains(item)).collect(toList());
                if (!CommonUtils.isNullOrEmpty(listStr3)) {
                    for (String code : listStr3) {
                        this.insertInsuranceTaskByRepairNo(finishDate2, importPO, code, viClivta);
                    }
                }
            }
        }
    }


    /**
     * 新生成一条任务（24个月进厂）
     *
     * @return
     */
    private Date insertInsuranceTaskByRepairNo(Date viFinishDate, InviteInsuranceVehicleRecordImportPO importPO, String dealerCode,
                                               Integer viClivta) {
        logger.info("customer--24个月进厂逻辑 生成续保任务 ===== 线索导入的：{}", importPO);
        Date finishDate = null;
        Date beforeDate = null;
        InviteInsuranceRulePO insuranceRulePO = inviteInsuranceVehicleTaskMapper.getInvitationDlrRule(dealerCode, viClivta, 82041004);
        List<InviteInsuranceVehicleTaskPO> list = inviteInsuranceVehicleTaskMapper.queryInsuranceTaskRecordDesc(importPO.getVin(), viClivta);
        OwnerVehicleVO ownerVehicleVO = this.getMidOwnerVehicleVOByVin(importPO.getVin());
        if (CommonUtils.isNullOrEmpty(list)) {
            if (null != ownerVehicleVO.getInvoiceDate()) {
                finishDate = ownerVehicleVO.getInvoiceDate();
                beforeDate = dateUtil(ownerVehicleVO.getInvoiceDate(), insuranceRulePO);
            }
        } else {
            for (InviteInsuranceVehicleTaskPO taskPO : list) {
                if (dealerCode.equals(taskPO.getDealerCode()) || dealerCode == taskPO.getDealerCode()) {
                    return viFinishDate;
                }
            }
            if(list.get(0).getDataSources() < 4){
                if (null != list.get(0).getDataSources()) {
                    finishDate = list.get(0).getAdviseInDate();
                    beforeDate = dateUtil(list.get(0).getAdviseInDate(), insuranceRulePO);
                }
            }else{
                if (null != ownerVehicleVO.getInvoiceDate()) {
                    finishDate = ownerVehicleVO.getInvoiceDate();
                    beforeDate = dateUtil(ownerVehicleVO.getInvoiceDate(), insuranceRulePO);
                }
            }
        }
        InviteInsuranceVehicleTaskDTO record = new InviteInsuranceVehicleTaskDTO();
        record.setVin(importPO.getVin());
        record.setLicensePlateNum(importPO.getLicensePlateNum());
        record.setName(importPO.getName());
        record.setTel(importPO.getTel());
        record.setItemType(10041002);
        record.setDataSources(5);

        //record.setInsuranceType(81761002);

        record.setInviteType(82381003);
        record.setDayInAdvance(insuranceRulePO.getDayInAdvance());
        record.setRemindInterval(insuranceRulePO.getRemindInterval());
        record.setIsCreateInvite(0);
        record.setCreateInviteTime(beforeDate); //生成邀约时间
        record.setAdviseInDate(finishDate); //建议进厂日期
        record.setFollowStatus(82401001);//未跟进
        record.setCloseInterval(insuranceRulePO.getCloseInterval());                               //超时关闭时间
        record.setOwnerCode(dealerCode);//经销商
        record.setDealerCode(dealerCode);
        record.setClueType(viClivta);
        //对对象进行赋值操作
        InviteInsuranceVehicleTaskPO inviteInsuranceVehicleTaskPO = record.transDtoToPo(InviteInsuranceVehicleTaskPO.class);
        //执行插入
        inviteInsuranceVehicleTaskMapper.insert(inviteInsuranceVehicleTaskPO);
        this.createInviteByTask(inviteInsuranceVehicleTaskPO);
        return finishDate;
    }

    /**
     * 创建续保线索 -- 记录
     *
     * @param po
     * @return
     */
    public void createInviteByTask(InviteInsuranceVehicleTaskPO po) {
        logger.info("customer--实时将线索任务转为记录 ===== 线索导入的：{}", po);
        InviteInsuranceVehicleRecordPO record = new InviteInsuranceVehicleRecordPO();
        record.setDealerCode(po.getDealerCode());
        //VCDC下发邀约
        record.setSourceType(1);
        record.setIsMain(1);
        record.setVin(po.getVin());
        record.setLicensePlateNum(po.getLicensePlateNum());
        record.setName(po.getName());
        record.setTel(po.getTel());
        record.setAge(po.getAge());
        record.setSex(po.getSex());
        record.setModel(po.getModel());
        record.setInviteType(po.getInviteType());
        record.setAdviseInDate(po.getAdviseInDate());
        record.setItemType(po.getItemType());
        record.setItemCode(po.getItemCode());
        record.setItemName(po.getItemName());
        record.setLastChangeDate(po.getInviteTime());
        //未完成
        record.setOrderStatus(83681001);
        Calendar c = Calendar.getInstance();
        c.setTime(po.getAdviseInDate());
        if(null == po.getDayInAdvance()){
            c.add(Calendar.DATE, -90);
        }else{
            c.add(Calendar.DATE, -po.getDayInAdvance());
        }
        //计划跟进时间 = 建议进厂时间 -提前N天邀约
        record.setPlanFollowDate(c.getTime());
        //未跟进
        record.setFollowStatus(82401001);
        record.setInsuranceBillId(po.getInsuranceBillId());
        record.setInsuranceType(po.getInsuranceType());
        record.setClueType(po.getClueType());
        record.setDataSources(po.getDataSources());
        record.setCreatedAt(new Date());

        inviteInsuranceVehicleRecordMapper.insert(record);

        //更新邀约任务
        po.setIsCreateInvite(1);
        po.setInviteId(record.getId());
        inviteInsuranceVehicleTaskMapper.updateById(po);
    }

    /**
     * 根据vin查询 车辆24个月最近两次的进厂经销商
     *
     * @param vin
     * @param insuranceDate
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    public List<String> selectRepairOrderByVin2(String vin, Date insuranceDate, String dealerCode, Integer num) {
        List<String> listStr = new ArrayList<String>();
        if (num == 1) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(insuranceDate);
            calendar.add(Calendar.YEAR, -1);
            Date date = calendar.getTime();
            CheckRepairOrderDTO orderDTO = reportCommonClient.selectRepairOrderByVin2(vin, date);
            if (null != orderDTO) {
                logger.info("customer--通过report查询24个月进厂数据1 ===== 线索导入的：{}", orderDTO);
                if (CommonUtils.isNullOrEmpty(orderDTO.getOrderVOList())) {
                    listStr.add(dealerCode);
                } else {
                    for (RepairOrderVO dto : orderDTO.getOrderVOList()) {
                        listStr.add(dto.getOwnerCode());
                    }
                    listStr.add(dealerCode);
                }
            }
        } else {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(insuranceDate);
            calendar.add(Calendar.DATE, -90);
            Date date = calendar.getTime();
            CheckRepairOrderDTO orderDTO = reportCommonClient.selectRepairOrderByVin2(vin, date);
            if (null != orderDTO) {
                logger.info("customer--通过report查询24个月进厂数据2 ===== 线索导入的：{}", orderDTO);
                if (CommonUtils.isNullOrEmpty(orderDTO.getOrderVOList())) {
                    listStr.add(dealerCode);
                } else {
                    for (RepairOrderVO dto : orderDTO.getOrderVOList()) {
                        listStr.add(dto.getOwnerCode());
                    }
                    listStr.add(dealerCode);
                }
            }
        }
        return listStr.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public IPage<InviteInsuranceVehicleRecordImportDTO> selectImportSuccessInsuranceRecord(Page page) throws ServiceBizException {
        // 获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (StringUtils.isNullOrEmpty(loginInfoDto.getUserId())) {
            throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
        }
        List<InviteInsuranceVehicleRecordImportDTO> list = inviteInsuranceVehicleRecordImportMapper.selectImportSuccessInsuranceRecord(page, loginInfoDto.getUserId());
        page.setRecords(list);
        return page;
    }

    @Override
    public IPage<InviteInsuranceVehicleRecordImportDTO> selectImportErrorInsuranceRecord(Page page) throws ServiceBizException {
        // 获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (StringUtils.isNullOrEmpty(loginInfoDto.getUserId())) {
            throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
        }
        List<InviteInsuranceVehicleRecordImportDTO> list = inviteInsuranceVehicleRecordImportMapper.selectImportErrorInsuranceRecord(page, loginInfoDto.getUserId());
        page.setRecords(list);
        return page;
    }

    /**
     * 时间处理（24个月进厂）
     *
     * @return
     */
    private Date dateUtil(Date finishDate, InviteInsuranceRulePO insuranceRulePO) {


        Calendar calendar1 = Calendar.getInstance(); //得到日历
        calendar1.setTime(finishDate);//把结束时间赋给日历
        if (null != insuranceRulePO && null != insuranceRulePO.getDayInAdvance()) {
            calendar1.add(Calendar.DATE, -insuranceRulePO.getDayInAdvance());  //根据规则提前跟进天数
        } else {
            calendar1.add(Calendar.DATE, -90);  //根据规则提前跟进天数
        }
        return calendar1.getTime();
    }

    private OwnerVehicleVO getMidOwnerVehicleVOByVin(String vin) {
        OwnerVehicleVO ownerVehicleVO = null;
        try {
            Page page = new Page(1, 1);
            Map map = new HashMap();
            map.put("vin", vin);
            IPage<OwnerVehicleVO> pageResult = iMiddleGroundVehicleService.queryVehicleInfo(page, map);
            if (pageResult != null && CollectionUtils.isNotEmpty(pageResult.getRecords())) {
                ownerVehicleVO = pageResult.getRecords().get(0);
            } else {
                ownerVehicleVO = null;
                //throw new ServiceBizException("根据当前VIN： " + vin + "调用中台车主车辆接口查询数据失败");
            }
        } catch (Exception e) {
            logger.error("call 根据[vin={}]中台车主车辆接口分页查询单条记录失败:{}", vin, e);
        }
        return ownerVehicleVO;
    }
}


