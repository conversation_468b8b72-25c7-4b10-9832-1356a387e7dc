package com.yonyou.dmscus.customer.service.accidentClues;

import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesDTO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.StatusChangePushDTO;

/**
 * <AUTHOR>
 * @Date 2023/11/20 11:32
 * @Version 1.0
 */
public interface CluePushService {

    /**
     * 线索新增&修改推送LiteCRM
     * @param clueInfo
     * @throws ServiceBizException
     */
    void pushLiteCrmClueInfo(AccidentCluesDTO clueInfo) throws ServiceBizException;

    /**
     * 线索状态变更推送LiteCRM
     * @param pushInfo
     * @throws ServiceBizException
     */
    void pushLiteCrmClueStatus(StatusChangePushDTO pushInfo) throws ServiceBizException;

    /**
     * 定时任务补偿推送
     * @param limit
     * @param maxSize
     * @param loopTimes
     * @param pageSize
     * @throws ServiceBizException
     */
    void compensatePush(int limit, int maxSize, int loopTimes, int pageSize) throws ServiceBizException;
}
