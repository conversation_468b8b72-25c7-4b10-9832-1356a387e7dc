package com.yonyou.dmscus.customer.service.common.message;

import com.yonyou.dmscus.customer.entity.dto.common.QueryUserPositionDTO;
import com.yonyou.dmscus.customer.entity.dto.common.UserPositionOutDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.AppPushDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/30 13:43
 * @Version 1.0
 */
public interface MessageSendService {

    /**
     * 推送消息给中台发送到APP
     * @param appPushDTO params
     * @return result
     */
    String messageSendApp(AppPushDTO appPushDTO);

    /**
     * 通过人员条件查询人员相关信息(empId)
     * @param empQueryDto params
     * @return result
     */
    List<String> queryEmpId(QueryUserPositionDTO empQueryDto);

    /**
     * 获取员工信息列表
     * @param empQueryDto params
     * @return res
     */
    List<UserPositionOutDTO> getUserPositionInfoList(QueryUserPositionDTO empQueryDto);
}
