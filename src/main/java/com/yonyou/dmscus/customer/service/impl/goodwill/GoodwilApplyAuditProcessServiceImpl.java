package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwilApplyAuditProcessMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwilApplyAuditProcessDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwilApplyAuditProcessPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwilApplyAuditProcessService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 亲善审批流程表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-22
 */
@Service
public class GoodwilApplyAuditProcessServiceImpl
		extends ServiceImpl<GoodwilApplyAuditProcessMapper, GoodwilApplyAuditProcessPO>
		implements GoodwilApplyAuditProcessService {
	/** 日志对象 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwilApplyAuditProcessMapper goodwilApplyAuditProcessMapper;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwilApplyAuditProcessDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.bean.dto.GoodwilApplyAuditProcessDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwilApplyAuditProcessDTO> selectPageBysql(Page page,
			GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO) {
		if (goodwilApplyAuditProcessDTO == null) {
			goodwilApplyAuditProcessDTO = new GoodwilApplyAuditProcessDTO();
		}
		GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo = goodwilApplyAuditProcessDTO
				.transDtoToPo(GoodwilApplyAuditProcessPO.class);

		List<GoodwilApplyAuditProcessPO> list = goodwilApplyAuditProcessMapper.selectPageBySql(page,
				goodwilApplyAuditProcessPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwilApplyAuditProcessDTO> result = list.stream()
					.map(m -> m.transPoToDto(GoodwilApplyAuditProcessDTO.class)).collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwilApplyAuditProcessDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.bean.dto.GoodwilApplyAuditProcessDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwilApplyAuditProcessDTO> selectListBySql(GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO) {
		if (goodwilApplyAuditProcessDTO == null) {
			goodwilApplyAuditProcessDTO = new GoodwilApplyAuditProcessDTO();
		}
		GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo = goodwilApplyAuditProcessDTO
				.transDtoToPo(GoodwilApplyAuditProcessPO.class);
		List<GoodwilApplyAuditProcessPO> list = goodwilApplyAuditProcessMapper
				.selectListBySql(goodwilApplyAuditProcessPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwilApplyAuditProcessDTO.class))
					.collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.bean.dto.GoodwilApplyAuditProcessDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwilApplyAuditProcessDTO getById(Long id) {
		GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo = goodwilApplyAuditProcessMapper.selectById(id);
		if (goodwilApplyAuditProcessPo != null) {
			return goodwilApplyAuditProcessPo.transPoToDto(GoodwilApplyAuditProcessDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwilApplyAuditProcessDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO) {
		// 对对象进行赋值操作
		GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo = goodwilApplyAuditProcessDTO
				.transDtoToPo(GoodwilApplyAuditProcessPO.class);
		// 执行插入
		int row = goodwilApplyAuditProcessMapper.insert(goodwilApplyAuditProcessPo);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwilApplyAuditProcessDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO) {
		GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo = goodwilApplyAuditProcessMapper.selectById(id);
		// 对对象进行赋值操作
		goodwilApplyAuditProcessDTO.transDtoToPo(goodwilApplyAuditProcessPo);
		// 执行更新
		int row = goodwilApplyAuditProcessMapper.updateById(goodwilApplyAuditProcessPo);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwilApplyAuditProcessMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwilApplyAuditProcessMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据审核对象和亲善单ID查询审批流程
	 *
	 * @param id,auditObject
	 * @return List<GoodwilApplyAuditProcessDTO>
	 * <AUTHOR>
	 * @since 2020/6/6
	 */
	@Override
	public List<Map> queryAuditProcess(Integer auditObject, Long id) {
		List<Map> list = goodwilApplyAuditProcessMapper.queryAuditProcess(auditObject, id);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list;
		}

	}

}
