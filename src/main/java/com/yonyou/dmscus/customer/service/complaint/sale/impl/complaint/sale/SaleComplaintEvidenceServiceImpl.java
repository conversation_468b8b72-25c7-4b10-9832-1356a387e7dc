package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;

    
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintEvidencePO;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintEvidenceMapper;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintEvidenceService;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintEvidenceDTO;

import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;




                                                            /**
 * <p>
 * 客户测试表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
@Service
public class SaleComplaintEvidenceServiceImpl implements SaleComplaintEvidenceService {
        //日志对象
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        SaleComplaintEvidenceMapper saleComplaintEvidenceMapper;

        /**
         * 分页查询对应数据
         *
         * @param page 分页对象
         * @param saleComplaintEvidenceDTO 查询条件
         * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintEvidenceDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public IPage<SaleComplaintEvidenceDTO>selectPageBysql(Page page,SaleComplaintEvidenceDTO saleComplaintEvidenceDTO){
            if(saleComplaintEvidenceDTO ==null){
                saleComplaintEvidenceDTO =new SaleComplaintEvidenceDTO();
            }
            SaleComplaintEvidencePO saleComplaintEvidencePO =saleComplaintEvidenceDTO.transDtoToPo(SaleComplaintEvidencePO.class);

            List<SaleComplaintEvidencePO>list= saleComplaintEvidenceMapper.selectPageBySql(page,saleComplaintEvidencePO);
            if(CommonUtils.isNullOrEmpty(list)){
                page.setRecords(new ArrayList<>());
                return page;
            }else{
                List<SaleComplaintEvidenceDTO>result=list.stream().map(m->m.transPoToDto(SaleComplaintEvidenceDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param saleComplaintEvidenceDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintEvidenceDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<SaleComplaintEvidenceDTO>selectListBySql(SaleComplaintEvidenceDTO saleComplaintEvidenceDTO){
            if(saleComplaintEvidenceDTO ==null){
                saleComplaintEvidenceDTO =new SaleComplaintEvidenceDTO();
            }
            SaleComplaintEvidencePO saleComplaintEvidencePO =saleComplaintEvidenceDTO.transDtoToPo(SaleComplaintEvidencePO.class);
            List<SaleComplaintEvidencePO>list= saleComplaintEvidenceMapper.selectListBySql(saleComplaintEvidencePO);
            if(CommonUtils.isNullOrEmpty(list)){
                return new ArrayList<>();
            }else{
                return list.stream().map(m->m.transPoToDto(SaleComplaintEvidenceDTO.class)).collect(Collectors.toList());
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintEvidenceDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public SaleComplaintEvidenceDTO getById(Long id){
            SaleComplaintEvidencePO saleComplaintEvidencePO = saleComplaintEvidenceMapper.selectById(id);
            if(saleComplaintEvidencePO!=null){
                return saleComplaintEvidencePO.transPoToDto(SaleComplaintEvidenceDTO.class);
            }else{
                throw new DALException("查询失败,没有符合条件的数据");
            }
        }

        /**
         * 根据DTO 进行数据新增
         *
         * @param saleComplaintEvidenceDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(SaleComplaintEvidenceDTO saleComplaintEvidenceDTO){
            //对对象进行赋值操作
            SaleComplaintEvidencePO saleComplaintEvidencePO = saleComplaintEvidenceDTO.transDtoToPo(SaleComplaintEvidencePO.class);
            //执行插入
            int row= saleComplaintEvidenceMapper.insert(saleComplaintEvidencePO);
            //返回插入的值
            return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param saleComplaintEvidenceDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, SaleComplaintEvidenceDTO saleComplaintEvidenceDTO){
            SaleComplaintEvidencePO saleComplaintEvidencePO = saleComplaintEvidenceMapper.selectById(id);
            //对对象进行赋值操作
            saleComplaintEvidenceDTO.transDtoToPo(saleComplaintEvidencePO);
            //执行更新
            int row= saleComplaintEvidenceMapper.updateById(saleComplaintEvidencePO);
            return row;
        }

        /**
         * 根据ID 进行删除数据
         *
         * @param id
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteById(Long id){
            int deleteCount= saleComplaintEvidenceMapper.deleteById(id);
            logger.debug("删除数量：{}",deleteCount);
            if(deleteCount<=0){
                throw new DALException("删除失败,没有符合条件的数据");
            }else{
                return deleteCount;
            }
        }

        /**
         * 根据ids 进行删除
         *
         * @param ids
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteBatchIds(String ids){
            List<Long>longList= StringUtils.convertStrToArray(ids,",", Long.class);
            int deleteCount= saleComplaintEvidenceMapper.deleteBatchIds(longList);
            logger.debug("删除数量：{}",deleteCount);
            if(deleteCount!=longList.size()){
                throw new DALException("删除失败,没有符合条件的数据");
            }else{
                return deleteCount;
            }
        }
}
