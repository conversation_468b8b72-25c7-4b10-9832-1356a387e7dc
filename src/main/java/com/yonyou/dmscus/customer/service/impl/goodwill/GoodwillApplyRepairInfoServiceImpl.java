package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyRepairInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyRepairInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyRepairInfoService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 亲善预约申请维修记录子表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class GoodwillApplyRepairInfoServiceImpl
		extends ServiceImpl<GoodwillApplyRepairInfoMapper, GoodwillApplyRepairInfoPO>
		implements GoodwillApplyRepairInfoService {
	/** 日志对象 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillApplyRepairInfoMapper goodwillApplyRepairInfoMapper;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillApplyRepairInfoDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	@Transactional(readOnly = true)
	public IPage<GoodwillApplyRepairInfoDTO> selectPageBysql(Page page,
			GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO) {
		if (goodwillApplyRepairInfoDTO == null) {
			goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
		}
		GoodwillApplyRepairInfoPO goodwillApplyRepairInfoPo = goodwillApplyRepairInfoDTO
				.transDtoToPo(GoodwillApplyRepairInfoPO.class);

		List<GoodwillApplyRepairInfoPO> list = goodwillApplyRepairInfoMapper.selectPageBySql(page,
				goodwillApplyRepairInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillApplyRepairInfoDTO> result = list.stream()
					.map(m -> m.transPoToDto(GoodwillApplyRepairInfoDTO.class)).collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillApplyRepairInfoDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	@Transactional(readOnly = true)
	public List<GoodwillApplyRepairInfoDTO> selectListBySql(GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO) {
		if (goodwillApplyRepairInfoDTO == null) {
			goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
		}
		GoodwillApplyRepairInfoPO goodwillApplyRepairInfoPo = goodwillApplyRepairInfoDTO
				.transDtoToPo(GoodwillApplyRepairInfoPO.class);
		List<GoodwillApplyRepairInfoPO> list = goodwillApplyRepairInfoMapper.selectListBySql(goodwillApplyRepairInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillApplyRepairInfoDTO.class))
					.collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairInfoDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	@Transactional(readOnly = true)
	public GoodwillApplyRepairInfoDTO getById(Long id) {
		GoodwillApplyRepairInfoPO goodwillApplyRepairInfoPo = goodwillApplyRepairInfoMapper.selectById(id);
		if (goodwillApplyRepairInfoPo != null) {
			return goodwillApplyRepairInfoPo.transPoToDto(GoodwillApplyRepairInfoDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillApplyRepairInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO) {
		// 对对象进行赋值操作
		GoodwillApplyRepairInfoPO goodwillApplyRepairInfoPo = goodwillApplyRepairInfoDTO
				.transDtoToPo(GoodwillApplyRepairInfoPO.class);
		// 执行插入
		int row = goodwillApplyRepairInfoMapper.insert(goodwillApplyRepairInfoPo);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillApplyRepairInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO) {
		GoodwillApplyRepairInfoPO goodwillApplyRepairInfoPo = goodwillApplyRepairInfoMapper.selectById(id);
		// 对对象进行赋值操作
		goodwillApplyRepairInfoDTO.transDtoToPo(goodwillApplyRepairInfoPo);
		// 执行更新
		int row = goodwillApplyRepairInfoMapper.updateById(goodwillApplyRepairInfoPo);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillApplyRepairInfoMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillApplyRepairInfoMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据申请单id查询维修记录
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2020/5/11
	 */
	@Override
	public List<GoodwillApplyRepairInfoDTO> getSupportApplyRepairInfoById(Long goodwillApplyId) {
		List<GoodwillApplyRepairInfoDTO> list = goodwillApplyRepairInfoMapper
				.getSupportApplyRepairInfoById(goodwillApplyId);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list;
		}
	}
}
