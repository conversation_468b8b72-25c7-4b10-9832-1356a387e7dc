package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

    
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;


import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomTopUsePO;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintCustomTopUseMapper;
import com.yonyou.dmscus.customer.service.complaint.ComplaintCustomTopUseService;

import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomTopUseDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;




                                                    /**
 * <p>
 * 客户投诉自定义置顶使用表每个人不同对应不同 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
@Service
        public class ComplaintCustomTopUseServiceImpl implements ComplaintCustomTopUseService {
         /**
         * 日志说明
         */
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        ComplaintCustomTopUseMapper complaintCustomTopUseMapper;

        /**
         * 分页查询对应数据
         *
         * @param page 分页对象
         * @param complaintCustomTopUseDTO 查询条件
         * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomTopUseDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public IPage<ComplaintCustomTopUseDTO>selectPageBysql(Page page,ComplaintCustomTopUseDTO complaintCustomTopUseDTO){
            if(complaintCustomTopUseDTO ==null){
                complaintCustomTopUseDTO =new ComplaintCustomTopUseDTO();
            }
            ComplaintCustomTopUsePO complaintCustomTopUsePo =complaintCustomTopUseDTO.transDtoToPo(ComplaintCustomTopUsePO.class);

            List<ComplaintCustomTopUsePO>list= complaintCustomTopUseMapper.selectPageBySql(page,complaintCustomTopUsePo);
            if(CommonUtils.isNullOrEmpty(list)){
                page.setRecords(new ArrayList<>());
                return page;
            }else{
                List<ComplaintCustomTopUseDTO>result=list.stream().map(m->m.transPoToDto(ComplaintCustomTopUseDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param complaintCustomTopUseDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomTopUseDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<ComplaintCustomTopUseDTO>selectListBySql(ComplaintCustomTopUseDTO complaintCustomTopUseDTO){
            if(complaintCustomTopUseDTO ==null){
                complaintCustomTopUseDTO =new ComplaintCustomTopUseDTO();
            }
            ComplaintCustomTopUsePO complaintCustomTopUsePo =complaintCustomTopUseDTO.transDtoToPo(ComplaintCustomTopUsePO.class);
            List<ComplaintCustomTopUsePO>list= complaintCustomTopUseMapper.selectListBySql(complaintCustomTopUsePo);
            if(CommonUtils.isNullOrEmpty(list)){
                return new ArrayList<>();
            }else{
                return list.stream().map(m->m.transPoToDto(ComplaintCustomTopUseDTO.class)).collect(Collectors.toList());
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomTopUseDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public ComplaintCustomTopUseDTO getById(Long id){
            ComplaintCustomTopUsePO complaintCustomTopUsePo = complaintCustomTopUseMapper.selectById(id);
            if(complaintCustomTopUsePo!=null){
                return complaintCustomTopUsePo.transPoToDto(ComplaintCustomTopUseDTO.class);
            }else{
                throw new DALException("查询失败,没有符合条件的数据");
            }
        }

        /**
         * 根据DTO 进行数据新增
         *
         * @param complaintCustomTopUseDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(ComplaintCustomTopUseDTO complaintCustomTopUseDTO){
            //对对象进行赋值操作
            ComplaintCustomTopUsePO complaintCustomTopUsePo = complaintCustomTopUseDTO.transDtoToPo(ComplaintCustomTopUsePO.class);
            //执行插入
            int row= complaintCustomTopUseMapper.insert(complaintCustomTopUsePo);
            //返回插入的值
            return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param complaintCustomTopUseDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, ComplaintCustomTopUseDTO complaintCustomTopUseDTO){
            ComplaintCustomTopUsePO complaintCustomTopUsePo = complaintCustomTopUseMapper.selectById(id);
            //对对象进行赋值操作
            complaintCustomTopUseDTO.transDtoToPo(complaintCustomTopUsePo);
            //执行更新
            int row= complaintCustomTopUseMapper.updateById(complaintCustomTopUsePo);
            return row;
        }

        /**
         * 根据ID 进行删除数据
         *
         * @param id
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteById(Long id){
            int deleteCount= complaintCustomTopUseMapper.deleteById(id);
            logger.debug("删除数量：{}",deleteCount);
            if(deleteCount<=0){
                throw new DALException("删除失败,没有符合条件的数据");
            }else{
                return deleteCount;
            }
        }

        /**
         * 新增置顶条件
         * @param complaintCustomFieldTestDTO
         * @return
          */
        @Override
        public int insertTop(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO) {
            long userId= FrameworkUtil.getLoginInfo().getUserId();
            ComplaintCustomTopUseDTO complaintCustomTopUseDTO=new ComplaintCustomTopUseDTO();
            String planFollowTime=complaintCustomFieldTestDTO.getPlanFollowTime();
            String ccmIsRead=complaintCustomFieldTestDTO.getCcmIsRead();
            String importanceLevel=complaintCustomFieldTestDTO.getImportanceLevel();
            String callTime=complaintCustomFieldTestDTO.getCallTime();
            String isFinish=complaintCustomFieldTestDTO.getIsFinish();
            Map<String,String> planFollowTimeMap=new HashMap<String,String> (16);
            Map<String,String>  ccmIsReadMap=new HashMap<String,String> (16);
            Map<String,String>  importanceLevelMap=new HashMap<String,String> (16);
            Map<String,String>  callTimeMap=new HashMap<String,String> (16);
            Map<String,String>  isFinishMap=new HashMap<String,String> (16);
            planFollowTimeMap.put("Type","planFollowTime");
            planFollowTimeMap.put("isValid",planFollowTime);
            ccmIsReadMap.put("Type","ccmIsRead");
            ccmIsReadMap.put("isValid",ccmIsRead);
            importanceLevelMap.put("Type","importanceLevel");
            importanceLevelMap.put("isValid",importanceLevel);
            callTimeMap.put("Type","callTime");
            callTimeMap.put("isValid",callTime);
            isFinishMap.put("Type","isFinish");
            isFinishMap.put("isValid",isFinish);
            List<Map> toplist=new ArrayList<>();
            toplist.add(0,planFollowTimeMap);
            toplist.add(1,ccmIsReadMap);
            toplist.add(2,importanceLevelMap);
            toplist.add(3,callTimeMap);
            toplist.add(4,isFinishMap);
            for (int i=0;i<toplist.size();i++){
                complaintCustomTopUseDTO.setType((String) toplist.get(i).get("Type"));
                String flag="false";
                if(toplist.get(i).get("isValid").equals(flag)){
                    complaintCustomTopUseDTO.setIsValid(10041002);
                }else {
                    complaintCustomTopUseDTO.setIsValid(10041001);
                }
                complaintCustomTopUseDTO.setUserId(userId);
                List<ComplaintCustomTopUsePO> queryTop=complaintCustomTopUseMapper.queryTop(complaintCustomTopUseDTO);

                if(queryTop.size()!=0){
                    long id=queryTop.get(0).getId();
                        update(id,complaintCustomTopUseDTO);
                }else {
                    insert(complaintCustomTopUseDTO);
                }
            }
            return 1;
         }

}
