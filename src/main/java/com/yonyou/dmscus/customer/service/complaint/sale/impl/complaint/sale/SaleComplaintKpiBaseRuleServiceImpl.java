package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiSetDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintKpiBaseRulePO;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintKpiBaseRuleMapper;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintKpiBaseRuleService;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintKpiBaseRuleDTO;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 销售客户投诉KP基础规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-21
 */
@Service
public class SaleComplaintKpiBaseRuleServiceImpl implements SaleComplaintKpiBaseRuleService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    SaleComplaintKpiBaseRuleMapper saleComplaintKpiBaseRuleMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                        分页对象
     * @param saleComplaintKpiBaseRuleDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintKpiBaseRuleDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<SaleComplaintKpiBaseRuleDTO> selectPageBysql(Page page, SaleComplaintKpiBaseRuleDTO saleComplaintKpiBaseRuleDTO) {
        if (saleComplaintKpiBaseRuleDTO == null) {
            saleComplaintKpiBaseRuleDTO = new SaleComplaintKpiBaseRuleDTO();
        }
        SaleComplaintKpiBaseRulePO saleComplaintKpiBaseRulePO = saleComplaintKpiBaseRuleDTO.transDtoToPo(SaleComplaintKpiBaseRulePO.class);

        List<SaleComplaintKpiBaseRulePO> list = saleComplaintKpiBaseRuleMapper.selectPageBySql(page, saleComplaintKpiBaseRulePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<SaleComplaintKpiBaseRuleDTO> result = list.stream().map(m -> m.transPoToDto(SaleComplaintKpiBaseRuleDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param saleComplaintKpiBaseRuleDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintKpiBaseRuleDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<SaleComplaintKpiBaseRuleDTO> selectListBySql(SaleComplaintKpiBaseRuleDTO saleComplaintKpiBaseRuleDTO) {
        if (saleComplaintKpiBaseRuleDTO == null) {
            saleComplaintKpiBaseRuleDTO = new SaleComplaintKpiBaseRuleDTO();
        }
        SaleComplaintKpiBaseRulePO saleComplaintKpiBaseRulePO = saleComplaintKpiBaseRuleDTO.transDtoToPo(SaleComplaintKpiBaseRulePO.class);
        List<SaleComplaintKpiBaseRulePO> list = saleComplaintKpiBaseRuleMapper.selectListBySql(saleComplaintKpiBaseRulePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(SaleComplaintKpiBaseRuleDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintKpiBaseRuleDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public SaleComplaintKpiBaseRuleDTO getById(Long id) {
        SaleComplaintKpiBaseRulePO saleComplaintKpiBaseRulePO = saleComplaintKpiBaseRuleMapper.selectById(id);
        if (saleComplaintKpiBaseRulePO != null) {
            return saleComplaintKpiBaseRulePO.transPoToDto(SaleComplaintKpiBaseRuleDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据" );
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param saleComplaintKpiBaseRuleDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(SaleComplaintKpiBaseRuleDTO saleComplaintKpiBaseRuleDTO) {
        //对对象进行赋值操作
        SaleComplaintKpiBaseRulePO saleComplaintKpiBaseRulePO = saleComplaintKpiBaseRuleDTO.transDtoToPo(SaleComplaintKpiBaseRulePO.class);
        //执行插入
        int row = saleComplaintKpiBaseRuleMapper.insert(saleComplaintKpiBaseRulePO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                          主键ID
     * @param saleComplaintKpiBaseRuleDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, SaleComplaintKpiBaseRuleDTO saleComplaintKpiBaseRuleDTO) {
        SaleComplaintKpiBaseRulePO saleComplaintKpiBaseRulePO = saleComplaintKpiBaseRuleMapper.selectById(id);
        //对对象进行赋值操作
        saleComplaintKpiBaseRuleDTO.transDtoToPo(saleComplaintKpiBaseRulePO);
        //执行更新
        int row = saleComplaintKpiBaseRuleMapper.updateById(saleComplaintKpiBaseRulePO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = saleComplaintKpiBaseRuleMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据" );
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = saleComplaintKpiBaseRuleMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据" );
        } else {
            return deleteCount;
        }
    }

    @Override
    public int updateKpi(List<ComplaintKpiSetDTO> list) {
        int row = 0;
        for (int i = 0; i < list.size(); i++) {
            ComplaintKpiSetDTO complaintKpiSetDto = list.get(i);
            SaleComplaintKpiBaseRuleDTO saleComplaintKpiBaseRuleDTO = new SaleComplaintKpiBaseRuleDTO();
            saleComplaintKpiBaseRuleDTO.setIndexValue(complaintKpiSetDto.getIndexValue());
            if (complaintKpiSetDto.getScore() == null || (complaintKpiSetDto.getScore().toString()).equals("" )) {
                saleComplaintKpiBaseRuleDTO.setScore(0);
            } else {
                saleComplaintKpiBaseRuleDTO.setScore(complaintKpiSetDto.getScore());
            }

            if (complaintKpiSetDto.getIsValid() == false) {
                saleComplaintKpiBaseRuleDTO.setIsValid(0);
            } else {
                saleComplaintKpiBaseRuleDTO.setIsValid(1);
            }

            long id = complaintKpiSetDto.getId();
            saleComplaintKpiBaseRuleDTO.setKpi(complaintKpiSetDto.getKpi());
            saleComplaintKpiBaseRuleDTO.setCreatedBy(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
            row = saleComplaintKpiBaseRuleMapper.updateKpi(saleComplaintKpiBaseRuleDTO);
        }
        return row;
    }

}
