package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

    
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.dto.complaint.*;
import com.yonyou.dmscus.customer.entity.dto.middleground.AppPushDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistDepartmentPO;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintAssistDepartmentMapper;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistPO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintAssistDepartmentService;

import com.yonyou.dmscus.customer.service.impl.CommonServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;




     /**
 * <p>
 * 协助部门 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Service
public class ComplaintAssistDepartmentServiceImpl implements ComplaintAssistDepartmentService {
         /**
          * 日志说明
          */
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        ComplaintAssistDepartmentMapper complaintAssistDepartmentMapper;
        @Autowired
         CommonServiceImpl commonService;

        /**
         * 分页查询对应数据
         *
         * @param page 分页对象
         * @param complaintAssistDepartmentDTO 查询条件
         * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAssistDepartmentDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public IPage<ComplaintAssistDepartmentDTO>selectPageBysql(Page page,ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO){
			if(complaintAssistDepartmentDTO ==null){
				complaintAssistDepartmentDTO =new ComplaintAssistDepartmentDTO();
			}
			ComplaintAssistDepartmentPO complaintAssistDepartmentPO =complaintAssistDepartmentDTO.transDtoToPo(ComplaintAssistDepartmentPO.class);

			List<ComplaintAssistDepartmentPO>list= complaintAssistDepartmentMapper.selectPageBySql(page,complaintAssistDepartmentPO);
			if(CommonUtils.isNullOrEmpty(list)){
				page.setRecords(new ArrayList<>());
				return page;
			}else{
				List<ComplaintAssistDepartmentDTO>result=list.stream().map(m->m.transPoToDto(ComplaintAssistDepartmentDTO.class)).collect(Collectors.toList());

				page.setRecords(result);
				return page;
			}
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param complaintAssistDepartmentDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAssistDepartmentDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<ComplaintAssistDepartmentDTO>selectListBySql(ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO){
			if(complaintAssistDepartmentDTO ==null){
				complaintAssistDepartmentDTO =new ComplaintAssistDepartmentDTO();
			}
				ComplaintAssistDepartmentPO complaintAssistDepartmentPO =complaintAssistDepartmentDTO.transDtoToPo(ComplaintAssistDepartmentPO.class);
			List<ComplaintAssistDepartmentPO>list= complaintAssistDepartmentMapper.selectListBySql(complaintAssistDepartmentPO);
			if(CommonUtils.isNullOrEmpty(list)){
				return new ArrayList<>();
			}else{
				return list.stream().map(m->m.transPoToDto(ComplaintAssistDepartmentDTO.class)).collect(Collectors.toList());
			}
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAssistDepartmentDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public ComplaintAssistDepartmentDTO getById(Long id){
		ComplaintAssistDepartmentPO complaintAssistDepartmentPO = complaintAssistDepartmentMapper.selectById(id);
        if(complaintAssistDepartmentPO!=null){
        return complaintAssistDepartmentPO.transPoToDto(ComplaintAssistDepartmentDTO.class);
        }else{
        throw new DALException("查询失败,没有符合条件的数据");
        }
        }

        /**
         * 根据DTO 进行数据新增
         *
         * @param complaintAssistDepartmentDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO){
        //对对象进行赋值操作
    ComplaintAssistDepartmentPO complaintAssistDepartmentPO = complaintAssistDepartmentDTO.transDtoToPo(ComplaintAssistDepartmentPO.class);
        //执行插入
        int row= complaintAssistDepartmentMapper.insert(complaintAssistDepartmentPO);
        //返回插入的值
        return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param complaintAssistDepartmentDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO){
            ComplaintAssistDepartmentPO complaintAssistDepartmentPO = complaintAssistDepartmentMapper.selectById(id);
            //对对象进行赋值操作
            complaintAssistDepartmentDTO.transDtoToPo(complaintAssistDepartmentPO);
            //执行更新
            int row= complaintAssistDepartmentMapper.updateById(complaintAssistDepartmentPO);
            return row;
        }

        /**
         * 根据ID 进行删除数据
         *
         * @param id
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteById(Long id){
            int deleteCount= complaintAssistDepartmentMapper.deleteById(id);
            logger.debug("删除数量：{}",deleteCount);
            if(deleteCount<=0){
                throw new DALException("删除失败,没有符合条件的数据");
            }else{
                return deleteCount;
            }
        }

         @Override
         public List<ComplaintAssistDTO> selectAssistByid(ComplaintAssistDTO complaintAssistDTO) {
             if(complaintAssistDTO ==null){
                 complaintAssistDTO =new ComplaintAssistDTO();
             }
             ComplaintAssistPO complaintAssistPO =complaintAssistDTO.transDtoToPo(ComplaintAssistPO.class);
             List<ComplaintAssistPO>list= complaintAssistDepartmentMapper.selectAssistByid(complaintAssistPO);
             if(CommonUtils.isNullOrEmpty(list)){
                 return new ArrayList<>();
             }else{
                 return list.stream().map(m->m.transPoToDto(ComplaintAssistDTO.class)).collect(Collectors.toList());
             }
         }

         @Override
         public void updatestatus(long id) {
             complaintAssistDepartmentMapper.updatestatus(id);
         }

         @Override
         public void updatestatus1(long id) {
             complaintAssistDepartmentMapper.updatestatus1(id);
         }

         /**
          * 查询数据
          *
          * @param complaintAssistDTO
          * @return
          */
         @Override
         public List<ComplaintAssistDTO> selectAssistValidByid(ComplaintAssistDTO complaintAssistDTO) {
             if(complaintAssistDTO ==null){
                 complaintAssistDTO =new ComplaintAssistDTO();
             }
             ComplaintAssistPO complaintAssistPO =complaintAssistDTO.transDtoToPo(ComplaintAssistPO.class);
             List<ComplaintAssistPO>list= complaintAssistDepartmentMapper.selectAssistValidByid(complaintAssistPO);
             if(CommonUtils.isNullOrEmpty(list)){
                 return new ArrayList<>();
             }else{
                 return list.stream().map(m->m.transPoToDto(ComplaintAssistDTO.class)).collect(Collectors.toList());
             }
         }

         /**
          * 查询是否被分配过
          *
          * @param complaintAssistDTO
          * @return
          */
         @Override
         public List<ComplaintAssistDepartmentDTO> selectAssistList(ComplaintAssistDTO complaintAssistDTO) {
             return complaintAssistDepartmentMapper.selectAssistList(complaintAssistDTO);
         }

         @Override
         public void setAssistDepartmentNotEidt(long id) {
             complaintAssistDepartmentMapper.setAssistDepartmentNotEidt(id);

         }

         @Override
         public void pushMessage(ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO) {
             GetDepUserDTO getDepUserDTO=new GetDepUserDTO();
             getDepUserDTO.setOrgId(Integer.valueOf(complaintAssistDepartmentDTO.getAssistDepartment()));
             List<DepUserDataDTO> depUserDataDTOList=commonService.getDepUser(getDepUserDTO);
             List<Integer> idList = depUserDataDTOList.stream().map(DepUserDataDTO::getUserId).collect(Collectors.toList());
             AppPushDTO appPushDTO=new AppPushDTO();
             appPushDTO.setUserIds(idList);
             appPushDTO.setPriority(33081001L);
             ComplaintInfoDTO complaintInfoDTO=complaintAssistDepartmentMapper.selectNo(complaintAssistDepartmentDTO.getComplaintInfoId());
             String hopeReplyTime="";
             switch (complaintAssistDepartmentDTO.getHopeReplyTime()){
                 case 82701001:
                     hopeReplyTime="2小时";
                     break;
                 case 82701002:
                     hopeReplyTime="4小时";
                     break;
                 case 82701003:
                     hopeReplyTime="8小时";
                     break;
                 case 82701004:
                     hopeReplyTime="24小时";
                     break;
                 default :
                     hopeReplyTime="尽快回复";
             }
             appPushDTO.setTitle("客诉单处理提醒");
             JSONObject json = new JSONObject();
             //向json中添加数据
             json.put("complaintNo",complaintInfoDTO.getComplaintId());
             json.put("hopeReplyTime",hopeReplyTime);

             //转换为字符串
             String jsonStr = json.toString();
             appPushDTO.setJson(jsonStr);
             appPushDTO.setContent("您有需要处理的客诉单:"
                                    +"客诉单号:"+complaintInfoDTO.getComplaintId()+",希望回复时间:"+hopeReplyTime
                                    +"!点击案件进行处理");
             commonService.messageSendApp(appPushDTO);


         }

         @Override
         public IPage<ComplaintAssistDTO> selectAssist(Page page, ComplaintAssistDTO complaintAssistDTO) {
             if(complaintAssistDTO ==null){
                 complaintAssistDTO =new ComplaintAssistDTO();
             }
             ComplaintAssistPO complaintAssistPO =complaintAssistDTO.transDtoToPo(ComplaintAssistPO.class);

             List<ComplaintAssistPO>list= complaintAssistDepartmentMapper.selectAssist(page,complaintAssistPO);
             if(CommonUtils.isNullOrEmpty(list)){
                 page.setRecords(new ArrayList<>());
                 return page;
             }else{
                 List<ComplaintAssistDTO>result=list.stream().map(m->m.transPoToDto(ComplaintAssistDTO.class)).collect(Collectors.toList());

                 page.setRecords(result);
                 return page;
             }
         }


}
