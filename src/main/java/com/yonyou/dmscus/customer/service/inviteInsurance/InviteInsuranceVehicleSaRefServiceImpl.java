package com.yonyou.dmscus.customer.service.inviteInsurance;

import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleSaRefMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * <p>
 * 续保车辆跟进人员对应关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@Service
public class InviteInsuranceVehicleSaRefServiceImpl implements InviteInsuranceVehicleSaRefService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteInsuranceVehicleSaRefMapper inviteInsuranceVehicleSaRefMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                           分页对象
     * @param inviteInsuranceVehicleSaRefDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.repair.entity.dto.tools.InviteInsuranceVehicleSaRefDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    /*@Override
    public IPage<InviteInsuranceVehicleSaRefDTO> selectPageBysql(Page page, InviteInsuranceVehicleSaRefDTO inviteInsuranceVehicleSaRefDTO) {
        if (inviteInsuranceVehicleSaRefDTO == null) {
            inviteInsuranceVehicleSaRefDTO = new InviteInsuranceVehicleSaRefDTO();
        }
        InviteInsuranceVehicleSaRefPO inviteInsuranceVehicleSaRefPO = inviteInsuranceVehicleSaRefDTO.transDtoToPo(InviteInsuranceVehicleSaRefPO.class);

        List<InviteInsuranceVehicleSaRefPO> list = inviteInsuranceVehicleSaRefMapper.selectPageBySql(page, inviteInsuranceVehicleSaRefPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteInsuranceVehicleSaRefDTO> result = list.stream().map(m -> m.transPoToDto(InviteInsuranceVehicleSaRefDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }*/

}
