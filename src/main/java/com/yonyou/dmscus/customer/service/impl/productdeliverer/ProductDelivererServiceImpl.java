package com.yonyou.dmscus.customer.service.impl.productdeliverer;



import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscus.customer.dao.productdeliverer.ProductDelivererMapper;
import com.yonyou.dmscus.customer.entity.dto.productdeliverer.ProductDelivererDTO;
import com.yonyou.dmscus.customer.entity.po.productdeliverer.ProductDelivererPO;
import com.yonyou.dmscus.customer.service.productdeliverer.ProductDelivererService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import javax.annotation.Resource;


/**
 * <p>
 * 主单经销商送修人信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-02
 */
@Service
public class ProductDelivererServiceImpl implements ProductDelivererService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    ProductDelivererMapper productDelivererMapper;


    /**
     * 查询上次工单送修人信息
     * @param vin
     * @return
     */
    @Override
    public ProductDelivererDTO queryProductDeliverer(String vin) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        ProductDelivererPO po = productDelivererMapper.queryProductDeliverer(vin,loginInfoDto.getOwnerCode());
        if(po!=null){
            return po.transPoToDto(ProductDelivererDTO.class);
        }
        return null;
    }
}
