package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yonyou.dmscus.customer.dao.voc.VocFunctionalStatusLogMapper;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusLogPO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @title: VocFunctionalStatusLogServiceImpl
 * @projectName dmscus.customer
 * @date 2022/11/118:48
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class VocFunctionalStatusLogServiceImpl   implements VocFunctionalStatusLogService  {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    VocFunctionalStatusLogMapper vocFunctionalStatusLogMapper;

    @Override
    public int insertList(List<VocFunctionalStatusLogPO> logPOSList) {
           addList(logPOSList);
        return logPOSList.size();
    }

    @Override
    public List<VocFunctionalStatusLogPO> selectListBydt(String data, int begIndex, Integer endIndex) {
       QueryWrapper qw =   new QueryWrapper<VocFunctionalStatusLogPO>();
       qw.eq("dt",data);
       qw.eq("is_deleted",0);
        qw.orderByDesc("id");
        qw.last(" limit "+begIndex+","+endIndex);
        return vocFunctionalStatusLogMapper.selectList(qw);
    }

    @Override
    public int selectVocFunctionalStatusLogByVin(String vin) {
        LambdaQueryWrapper<VocFunctionalStatusLogPO> qw = new LambdaQueryWrapper<>();
        qw.eq(VocFunctionalStatusLogPO::getIsDeleted,0);
        qw.eq(VocFunctionalStatusLogPO::getVin,vin);
        qw.orderByDesc(VocFunctionalStatusLogPO::getId);
        qw.last("limit 2");
        List<VocFunctionalStatusLogPO> list = vocFunctionalStatusLogMapper.selectList(qw);
        if (CollUtil.isNotEmpty(list) &&  list.size()==2) {
            if("未激活".equals(list.get(0).getActivatedState())&& "已激活".equals(list.get(1).getActivatedState())){
                return  1 ;
            }
            return  0;
        }else{
             return  0 ;
        }
    }

    /**
     *
     *  @param list
     */
    private void addList(List<VocFunctionalStatusLogPO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int numPerTimes = 200;
        if (list.size() <= numPerTimes) {
            batchLogAdd(list); //此处插入少于200条list
            logger.info("voc激活定时任务保存数据VocFunctionalStatusLogPO成功，数据小于200");
        } else {
            int maxIndex = list.size();
            int maxTimes = maxIndex / numPerTimes;
            maxTimes += (maxIndex % numPerTimes) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimes) {
                int fromIndex = numPerTimes * currentTimes;
                int toIndex = fromIndex + numPerTimes;
                toIndex = toIndex > maxIndex ? maxIndex : toIndex;
                List<VocFunctionalStatusLogPO> subList = list.subList(fromIndex, toIndex);
                batchLogAdd(subList);//此处循环插入200条list
                logger.info("voc激活定时任务保存数据VocFunctionalStatusLogPO成功，数据大于200{},{}",fromIndex,toIndex);
                currentTimes++;
            }
        }

    }

    /**
     *批量新增
     *  @param list
     */
    private void batchLogAdd(List<VocFunctionalStatusLogPO> list) {
        vocFunctionalStatusLogMapper.insertList(list);
    }
}
