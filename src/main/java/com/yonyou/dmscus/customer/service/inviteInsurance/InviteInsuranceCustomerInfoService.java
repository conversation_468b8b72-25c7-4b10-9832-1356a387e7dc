package com.yonyou.dmscus.customer.service.inviteInsurance;


import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceCustomerInfoDTO;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <p>
 * 续保呼叫登记自建联系人 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-15
 */
public interface InviteInsuranceCustomerInfoService {

    List<InviteInsuranceCustomerInfoDTO> selectAllInsuranceCustomerInfo(Long insuranceId);

    void updateInsuranceCustomerInfo(InviteInsuranceCustomerInfoDTO infoDTO);

    void deleteInsuranceCustomerInfo(Long tiicId);
}
