package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiTest;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRuleUseTestPO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintKpiBaseRuleUsePO;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintKpiBaseRuleUseMapper;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintKpiBaseRuleUseService;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintKpiBaseRuleUseDTO;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 销售客户投诉KP基础规则使用表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-21
 */
@Service
public class SaleComplaintKpiBaseRuleUseServiceImpl implements SaleComplaintKpiBaseRuleUseService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    SaleComplaintKpiBaseRuleUseMapper saleComplaintKpiBaseRuleUseMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                           分页对象
     * @param saleComplaintKpiBaseRuleUseDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintKpiBaseRuleUseDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<SaleComplaintKpiBaseRuleUseDTO> selectPageBysql(Page page, SaleComplaintKpiBaseRuleUseDTO saleComplaintKpiBaseRuleUseDTO) {
        if (saleComplaintKpiBaseRuleUseDTO == null) {
            saleComplaintKpiBaseRuleUseDTO = new SaleComplaintKpiBaseRuleUseDTO();
        }
        SaleComplaintKpiBaseRuleUsePO saleComplaintKpiBaseRuleUsePO = saleComplaintKpiBaseRuleUseDTO.transDtoToPo(SaleComplaintKpiBaseRuleUsePO.class);

        List<SaleComplaintKpiBaseRuleUsePO> list = saleComplaintKpiBaseRuleUseMapper.selectPageBySql(page, saleComplaintKpiBaseRuleUsePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<SaleComplaintKpiBaseRuleUseDTO> result = list.stream().map(m -> m.transPoToDto(SaleComplaintKpiBaseRuleUseDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param saleComplaintKpiBaseRuleUseDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintKpiBaseRuleUseDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<SaleComplaintKpiBaseRuleUseDTO> selectListBySql(SaleComplaintKpiBaseRuleUseDTO saleComplaintKpiBaseRuleUseDTO) {
        if (saleComplaintKpiBaseRuleUseDTO == null) {
            saleComplaintKpiBaseRuleUseDTO = new SaleComplaintKpiBaseRuleUseDTO();
        }
        SaleComplaintKpiBaseRuleUsePO saleComplaintKpiBaseRuleUsePO = saleComplaintKpiBaseRuleUseDTO.transDtoToPo(SaleComplaintKpiBaseRuleUsePO.class);
        List<SaleComplaintKpiBaseRuleUsePO> list = saleComplaintKpiBaseRuleUseMapper.selectListBySql(saleComplaintKpiBaseRuleUsePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(SaleComplaintKpiBaseRuleUseDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintKpiBaseRuleUseDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public SaleComplaintKpiBaseRuleUseDTO getById(Long id) {
        SaleComplaintKpiBaseRuleUsePO saleComplaintKpiBaseRuleUsePO = saleComplaintKpiBaseRuleUseMapper.selectById(id);
        if (saleComplaintKpiBaseRuleUsePO != null) {
            return saleComplaintKpiBaseRuleUsePO.transPoToDto(SaleComplaintKpiBaseRuleUseDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据" );
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param saleComplaintKpiBaseRuleUseDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(SaleComplaintKpiBaseRuleUseDTO saleComplaintKpiBaseRuleUseDTO) {
        //对对象进行赋值操作
        SaleComplaintKpiBaseRuleUsePO saleComplaintKpiBaseRuleUsePO = saleComplaintKpiBaseRuleUseDTO.transDtoToPo(SaleComplaintKpiBaseRuleUsePO.class);
        //执行插入
        int row = saleComplaintKpiBaseRuleUseMapper.insert(saleComplaintKpiBaseRuleUsePO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                             主键ID
     * @param saleComplaintKpiBaseRuleUseDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, SaleComplaintKpiBaseRuleUseDTO saleComplaintKpiBaseRuleUseDTO) {
        SaleComplaintKpiBaseRuleUsePO saleComplaintKpiBaseRuleUsePO = saleComplaintKpiBaseRuleUseMapper.selectById(id);
        //对对象进行赋值操作
        saleComplaintKpiBaseRuleUseDTO.transDtoToPo(saleComplaintKpiBaseRuleUsePO);
        //执行更新
        int row = saleComplaintKpiBaseRuleUseMapper.updateById(saleComplaintKpiBaseRuleUsePO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = saleComplaintKpiBaseRuleUseMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据" );
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = saleComplaintKpiBaseRuleUseMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据" );
        } else {
            return deleteCount;
        }
    }

    @Override
    public List<ComplaintKpiBaseRuleUseTestDTO> selectListBySql1(ComplaintKpiBaseRuleUseTestDTO complaintKpiBaseRuleUseTestDTO) {
        if (complaintKpiBaseRuleUseTestDTO == null) {
            complaintKpiBaseRuleUseTestDTO = new ComplaintKpiBaseRuleUseTestDTO();
        }
        ComplaintKpiBaseRuleUseTestPO complaintKpiBaseRuleUseTestPo = complaintKpiBaseRuleUseTestDTO.transDtoToPo(ComplaintKpiBaseRuleUseTestPO.class);
        List<ComplaintKpiBaseRuleUseTestPO> list = saleComplaintKpiBaseRuleUseMapper.selectListBySql1(complaintKpiBaseRuleUseTestPo);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(ComplaintKpiBaseRuleUseTestDTO.class)).collect(Collectors.toList());
        }
    }
    @Override
    public int updateWarnValue(List<ComplaintKpiTest> list) {
        int row=0;
        for (int i=0;i<list.size();i++){
            ComplaintKpiTest complaintKpiTest=list.get(i);
            long id=complaintKpiTest.getId();
            SaleComplaintKpiBaseRuleUseDTO saleComplaintKpiBaseRuleUseDTO=new SaleComplaintKpiBaseRuleUseDTO();
            saleComplaintKpiBaseRuleUseDTO.setDealerCode(FrameworkUtil.getLoginInfo().getOwnerCode());
            saleComplaintKpiBaseRuleUseDTO.setRuleId(id);
            saleComplaintKpiBaseRuleUseDTO.setUser(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
            List<SaleComplaintKpiBaseRuleUseDTO> all=selectListBySql(saleComplaintKpiBaseRuleUseDTO);
            saleComplaintKpiBaseRuleUseDTO.setWarnValue(complaintKpiTest.getWarnValue());
            if(all.size()==0){
                row=insert(saleComplaintKpiBaseRuleUseDTO);
            }else {
                long userId=all.get(0).getId();
                row= update(userId,saleComplaintKpiBaseRuleUseDTO);
            }


        }
        return row;
    }
}
