package com.yonyou.dmscus.customer.service.vocbtnlog;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yonyou.dmscus.customer.dao.btnLog.BtnLogMapper;
import com.yonyou.dmscus.customer.entity.po.btnlog.BtnLogPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @title: BtnLogServiceImpl
 * @projectName dmscus.customer
 * @description: TODO
 * @date 2022/11/117:44
 */
@Service
public class BtnLogServiceImpl implements  BtnLogService {
    @Autowired
    BtnLogMapper   btnLogMapper;

    @Override
    public int insert(BtnLogPO po) {
        return btnLogMapper.insert(po);
    }

    @Override
    public int update(BtnLogPO po) {
        return btnLogMapper.updateById(po);
    }

    @Override
    public int selectCount(String dateTime, int type,int issc) {
       QueryWrapper qw =  new QueryWrapper<BtnLogPO>();
        qw.eq("update_time",dateTime);
        qw.eq("data_type", type);
        qw.eq("is_sc",issc);
        qw.eq("is_deleted",0);
        return btnLogMapper.selectCount(qw);
    }
}
