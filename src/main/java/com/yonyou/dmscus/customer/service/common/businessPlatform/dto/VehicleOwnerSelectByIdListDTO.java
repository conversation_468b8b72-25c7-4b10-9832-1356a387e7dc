package com.yonyou.dmscus.customer.service.common.businessPlatform.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <p>
 * 车主表
 * </p>
 *
 * <AUTHOR> @since 2020-04-20
 */
@ApiModel(value="ONEID集合查询车主信息集合对象", description="ONEID集合查询车主信息集合")
public class VehicleOwnerSelectByIdListDTO {

    @ApiModelProperty(value = "车主信息ID集合", name = "list", required = false)
    private List<Long> list; 

	public List<Long> getList() {
		return list;
	}

	public void setList(List<Long> list) {
		this.list = list;
	}

	@Override
    public String toString() {
        return "TmVehicleOwner{" +
        "list=" + list +
        "}";
    }
}
