package com.yonyou.dmscus.customer.service.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintTypeDTO;

import java.util.List;


/**
 * <p>
 * 销售投诉类型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-10
 */
public interface SaleComplaintTypeService {
    IPage<SaleComplaintTypeDTO> selectPageBysql(Page page, SaleComplaintTypeDTO saleComplaintTypeDTO);
    List<SaleComplaintTypeDTO> selectListBySql(SaleComplaintTypeDTO saleComplaintTypeDTO);
    SaleComplaintTypeDTO getById(Long id);
    int insert(SaleComplaintTypeDTO saleComplaintTypeDTO);
    int update(Long id, SaleComplaintTypeDTO saleComplaintTypeDTO);
    int deleteById(Long id);

    List<SaleComplaintTypeDTO> selectList(SaleComplaintTypeDTO saleComplaintTypeDTO);

    List<SaleComplaintTypeDTO> selectListNotFw(SaleComplaintTypeDTO saleComplaintTypeDTO);
}
