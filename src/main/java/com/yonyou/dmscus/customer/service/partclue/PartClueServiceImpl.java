package com.yonyou.dmscus.customer.service.partclue;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.constants.RedisEnum;
import com.yonyou.dmscus.customer.constants.clue.LeadsTypeEnum;
import com.yonyou.dmscus.customer.constants.lnvitation.CloseStatusEnum;
import com.yonyou.dmscus.customer.constants.lnvitation.InviteTypeEnum;
import com.yonyou.dmscus.customer.constants.lnvitation.VerifyTypeEnum;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordDetailMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dao.voc.VocInviteVehicleTaskRecordMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.CallDetailsMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.CallVoiceMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.SaCustomerNumberMapper;
import com.yonyou.dmscus.customer.dto.RepairOrderVO;
import com.yonyou.dmscus.customer.enevt.LeadStatusChangeEvent;
import com.yonyou.dmscus.customer.entity.dto.clue.*;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightTopicDTO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordDetailPO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocInviteVehicleTaskRecordPo;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallVoicePO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaCustomerNumberPO;
import com.yonyou.dmscus.customer.feign.RepairCommonClient;
import com.yonyou.dmscus.customer.service.voicemanage.CallDetailsService;
import com.yonyou.dmscus.customer.service.voicemanage.CallVoiceService;
import com.yonyou.dmscus.customer.utils.RedisLockUtil.DistributedLockUtil;
import com.yonyou.dmscus.customer.utils.RedisLockUtil.IDistributedLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class PartClueServiceImpl implements PartClueService {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private InviteVehicleRecordMapper inviteVehicleRecordMapper;
	@Autowired
	private VocInviteVehicleTaskRecordMapper vocInviteVehicleTaskRecordMapper;
	@Autowired
	private RepairCommonClient repairCommonClient;
	@Autowired
	private SaCustomerNumberMapper saCustomerNumberMapper;
	@Autowired
	private CallDetailsMapper callDetailsMapper;
	@Autowired
	private CallDetailsService callDetailsService;
	@Autowired
	private CallVoiceMapper callVoiceMapper;
	@Autowired
	private CallVoiceService callVoiceService;
	@Autowired
	private InviteVehicleRecordDetailMapper inviteVehicleRecordDetailMapper;
	@Autowired
	private ApplicationEventPublisher eventPublisher;

	public static final String LITE_CRM = "LiteCrm";

	@Transactional
	public boolean doClueDataSynchro(LiteCrmClueDTO dto) {
		logger.info("doClueDataSynchro start... dto:{}", dto);
		Objects.requireNonNull(dto, "doClueDataSynchro null dto");
		Long icmId = dto.getId();
		Objects.requireNonNull(icmId, "doClueDataSynchro null icmId");
		IDistributedLock lock = DistributedLockUtil.getDistributedLock(
				RedisEnum.CLUE_DATA_SYNCHRO.getKey(icmId),
				RedisEnum.CLUE_DATA_SYNCHRO.getTimeoutMsecs());
		try {
			// 获取锁
			if (lock.acquire()) {
				LambdaQueryWrapper<VocInviteVehicleTaskRecordPo> queryWrapper = new LambdaQueryWrapper();
				queryWrapper.eq(VocInviteVehicleTaskRecordPo::getIcmId, icmId);
				queryWrapper.eq(VocInviteVehicleTaskRecordPo::getInviteType, InviteTypeEnum.PART_CLUE.getIntCode());
				queryWrapper.eq(VocInviteVehicleTaskRecordPo::getIsDeleted, 0);
				List<VocInviteVehicleTaskRecordPo> pos = vocInviteVehicleTaskRecordMapper.selectList(queryWrapper);
				if (!CollectionUtils.isEmpty(pos)) {
					logger.info("doClueDataSynchro !isEmpty pos");
					throw new ServiceBizException("请勿重复下发线索");
				}
				// 添加线索数据
				doClueDataSynchro_(dto);
			}
		} catch (Exception e) {
			logger.error("doClueDataSynchro: {}", e);
			throw new ServiceBizException(e.getMessage());
		} finally {
			if (null != lock) {
				try {
					// 释放锁
					lock.release();
				} catch (Exception e) {
					logger.error("doClueDataSynchro lock release e:{}", e);
				}
			}
		}

		logger.info("doClueDataSynchro end...");
		return true;
	}

	/**
	 * 下发线索
	 */
	public int doClueDataSynchro_(LiteCrmClueDTO dto) {
		logger.info("doClueDataSynchro start...  dto:{}", dto);
		Objects.requireNonNull(dto, "doClueDataSynchro null dto");
		ClueDataDTO data = dto.getData();
		Objects.requireNonNull(data, "doClueDataSynchro null data");
		ZeroAttInfoDTO zeroAttInfo = data.getZeroAttInfo();
		Objects.requireNonNull(zeroAttInfo, "doClueDataSynchro null zeroAttInfo");

		Long id = dto.getId();
		String vin = dto.getVehicleVin();
		logger.info("doClueDataSynchro id:{},vin:{}", id, vin);
		Objects.requireNonNull(id, "doClueDataSynchro null id");
		Objects.requireNonNull(vin, "doClueDataSynchro null vin");

		String vipId = zeroAttInfo.getVipId();
		String ticketId = zeroAttInfo.getTicketId();
		String ticketTemplateId = zeroAttInfo.getTicketTemplateId();
		String ticketCode = zeroAttInfo.getTicketCode();
		String ticketName = zeroAttInfo.getTicketName();
		String orderId = zeroAttInfo.getOrderId();
		logger.info("doClueDataSynchro vipId:{},ticketId:{},ticketTemplateId:{},ticketCode:{},ticketName:{},orderId:{}",
				vipId, ticketId, ticketTemplateId, ticketCode, ticketName, orderId);
		Objects.requireNonNull(ticketCode, "doClueDataSynchro null ticketCode");
		Objects.requireNonNull(ticketName, "doClueDataSynchro null ticketName");

		List<DealerInfoDTO> dealerInfo = data.getDealerInfo();
		if (CollectionUtils.isEmpty(dealerInfo)) {
			throw new ServiceBizException("doClueDataSynchro isEmpty dealerInfo");
		}
		DealerInfoDTO dealerInfoDTO = dealerInfo.get(0);
		Objects.requireNonNull(dealerInfoDTO, "doClueDataSynchro null dealerInfoDTO");
		String dealerCode = dealerInfoDTO.getDealerCode();
		logger.info("doClueDataSynchro dealerCode:{}", dealerCode);
		Objects.requireNonNull(dealerCode, "doClueDataSynchro dealerCode is null");

		CarInfoDTO carInfo = data.getCarInfo();
		Objects.requireNonNull(carInfo, "doClueDataSynchro null carInfo");

		List<ContactInfoDTO> contactInfo = data.getContactInfo();
		if (CollectionUtils.isEmpty(contactInfo)) {
			throw new ServiceBizException("doClueDataSynchro isEmpty contactInfo");
		}
		ContactInfoDTO contactInfoDTO = contactInfo.get(0);
		Objects.requireNonNull(contactInfoDTO, "doClueDataSynchro null contactInfoDTO");
		String customerName = contactInfoDTO.getCustomerName();
		String customerMobile = contactInfoDTO.getCustomerMobile();
		Objects.requireNonNull(customerName, "doClueDataSynchro null customerName");
		Objects.requireNonNull(customerMobile, "doClueDataSynchro null customerMobile");

		Date now = new Date();
		InviteVehicleRecordPO record = new InviteVehicleRecordPO();
		record.setDealerCode(dealerCode);
		record.setIsMain(1); // 主线索
		record.setVin(vin);
		record.setLicensePlateNum(carInfo.getLicencePlate());
		record.setName(customerName);
		record.setTel(customerMobile);
		record.setInviteType(InviteTypeEnum.PART_CLUE.getIntCode()); // 邀约类型
		record.setOrderStatus(CloseStatusEnum.INCOMPLETE.getIntCode()); // 线索状态 - 未完成
		record.setFollowStatus(CommonConstants.FOLLOW_STATUS_I); // 跟进状态
		record.setOwnerCode(dealerCode); // 经销商
		record.setIsDeleted(false);
		record.setCreatedAt(now);
		record.setCreatedBy(LITE_CRM);
		record.setUpdatedAt(now);
		record.setUpdatedBy(LITE_CRM);
		record.setAdviseInDate(now);
		//record.setScore(0); // AI 得分默认为0
		inviteVehicleRecordMapper.insert(record);
		Long poId = record.getId();
		logger.info("doClueDataSynchro poId:{}", poId);
		Objects.requireNonNull(poId, "doClueDataSynchro null poId");

		VocInviteVehicleTaskRecordPo vocrecord = new VocInviteVehicleTaskRecordPo();
		vocrecord.setRecordId(poId);
		vocrecord.setIcmId(id);
		vocrecord.setVin(vin);
		vocrecord.setInviteType(InviteTypeEnum.PART_CLUE.getIntCode());
		Optional.ofNullable(ticketTemplateId).ifPresent(v -> {
			vocrecord.setCouponId(Long.parseLong(v));
		});
		vocrecord.setCouponCode(ticketCode);
		vocrecord.setCouponName(ticketName);
		Optional.ofNullable(ticketId).ifPresent(v -> {
			vocrecord.setDetailId(Long.parseLong(v));
		});
		vocrecord.setIsDeleted(0);
		vocrecord.setCreatedAt(now);
		vocrecord.setCreatedBy(LITE_CRM);
		vocrecord.setUpdatedAt(now);
		vocrecord.setUpdatedBy(LITE_CRM);
		vocrecord.setOrderId(orderId);
		int row = vocInviteVehicleTaskRecordMapper.insert(vocrecord);
		logger.info("doClueDataSynchro end... vocpoId:{}", vocrecord.getId());
		//零附件线索关闭优化
		try {
			partClueCloseOptimize(dto,dealerCode,ticketId,poId);
		}catch (Exception e){
			logger.error("partClueCloseOptimize 零附件线索关闭优化异常",e);
		}
		return row;
	}

	/**
	 * 更改线索状态
	 */
	@Transactional
	public boolean updateClueStatus(LiteCrmClueDTO dto) {
		logger.info("updateClueStatus start...  dto:{}", dto);
		Objects.requireNonNull(dto, "updateClueStatus null dto");
		Long icmId = dto.getId();
		String orderStatus = dto.getClueStatus();
		String dataUpdateDate = dto.getDataUpdateDate();
		Objects.requireNonNull(icmId, "updateClueStatus null icmId");
		Objects.requireNonNull(orderStatus, "updateClueStatus null orderStatus");
		Objects.requireNonNull(dataUpdateDate, "updateClueStatus null dataUpdateDate");

		LambdaQueryWrapper<VocInviteVehicleTaskRecordPo> queryWrapper = new LambdaQueryWrapper();
		queryWrapper.eq(VocInviteVehicleTaskRecordPo::getIcmId, icmId);
		queryWrapper.eq(VocInviteVehicleTaskRecordPo::getInviteType, InviteTypeEnum.PART_CLUE.getIntCode());
		queryWrapper.eq(VocInviteVehicleTaskRecordPo::getIsDeleted, 0);
		queryWrapper.select(VocInviteVehicleTaskRecordPo::getId, VocInviteVehicleTaskRecordPo::getRecordId);
		List<VocInviteVehicleTaskRecordPo> vocpos = vocInviteVehicleTaskRecordMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(vocpos)) {
			logger.info("updateClueStatus isEmpty vocpos");
			throw new ServiceBizException("未查询到线索");
		}

		// 如果是核销成功则 verificationDealerCode、verificationOrder 为必填项
		RepairOrderVO repairOrderVO = null;
		ZeroAttInfoDTO zeroAttInfo = null;
		if (Arrays.asList(CloseStatusEnum.SELF_COMPLETED.getCode(),
				CloseStatusEnum.OTHER_STORE_COMPLETED.getCode())
				.contains(orderStatus))  {
			logger.info("updateClueStatus orderStatus is SELF_COMPLETED or OTHER_STORE_COMPLETED");
			ClueDataDTO data = dto.getData();
			Objects.requireNonNull(data, "updateClueStatus null data");

			zeroAttInfo = data.getZeroAttInfo();
			Objects.requireNonNull(zeroAttInfo, "updateClueStatus null zeroAttInfo");

			String verificationDealerCode = zeroAttInfo.getVerificationDealerCode();
			String verificationOrder = zeroAttInfo.getVerificationOrder();
			String verificationDate = zeroAttInfo.getVerificationDate();
			logger.info("updateClueStatus verificationDealerCode:{},verificationOrder:{},verificationDate:{}",
					verificationDealerCode, verificationOrder, verificationDate);
			Objects.requireNonNull(verificationDealerCode, "updateClueStatus verificationDealerCode is null");
			Objects.requireNonNull(verificationOrder, "updateClueStatus verificationOrder is null");

			// 根据经销商和工单查询开单时间
			repairOrderVO = repairCommonClient.queryRepairOrder(verificationDealerCode, verificationOrder);
			logger.info("updateClueStatus repairOrderVO:{}", repairOrderVO);
		}

		updateClueStatus(vocpos, dto, zeroAttInfo, repairOrderVO);

		logger.info("updateClueStatus end...");
		return true;
	}

	private void updateClueStatus(List<VocInviteVehicleTaskRecordPo> vocpos,
								  LiteCrmClueDTO dto,
								  ZeroAttInfoDTO zeroAttInfo,
								  RepairOrderVO repairOrderVO) {
		LambdaUpdateWrapper<VocInviteVehicleTaskRecordPo> vocRecordWrapper = null;
		LambdaUpdateWrapper<InviteVehicleRecordPO> recordWrapper = null;
		Date date = new Date();
		logger.info("updateClueStatus2 start...");
		for (VocInviteVehicleTaskRecordPo vocpo : vocpos) {
			if (null == vocpo) {
				continue;
			}
			vocRecordWrapper = new LambdaUpdateWrapper();
			vocRecordWrapper.eq(VocInviteVehicleTaskRecordPo::getId, vocpo.getId());
			vocRecordWrapper.eq(VocInviteVehicleTaskRecordPo::getInviteType, InviteTypeEnum.PART_CLUE.getIntCode());
			vocRecordWrapper.eq(VocInviteVehicleTaskRecordPo::getIsDeleted, 0);
			vocRecordWrapper.set(VocInviteVehicleTaskRecordPo::getUpdatedAt, date);

			if (null != zeroAttInfo) {
				logger.info("updateClueStatus null != zeroAttInfo");
				vocRecordWrapper.set(VocInviteVehicleTaskRecordPo::getExchangeOwnerCode, zeroAttInfo.getVerificationDealerCode());
				vocRecordWrapper.set(VocInviteVehicleTaskRecordPo::getExchangeRoNo, zeroAttInfo.getVerificationOrder());
				vocRecordWrapper.set(VocInviteVehicleTaskRecordPo::getCouponStateTime, zeroAttInfo.getVerificationDate());
			}

			// 如果是未完成状态需要抹掉核销信息
			String clueStatus = dto.getClueStatus();
			if (Objects.equals(clueStatus, CloseStatusEnum.INCOMPLETE.getCode())) {
				logger.info("updateClueStatus clueStatus = INCOMPLETE");
				vocRecordWrapper.set(VocInviteVehicleTaskRecordPo::getExchangeOwnerCode, null);
				vocRecordWrapper.set(VocInviteVehicleTaskRecordPo::getExchangeRoNo, null);
				vocRecordWrapper.set(VocInviteVehicleTaskRecordPo::getCouponStateTime, null);
			}

			int row = vocInviteVehicleTaskRecordMapper.update(null, vocRecordWrapper);
			logger.info("updateClueStatus inviteVehicleRecord update row:{}", row);

			recordWrapper = new LambdaUpdateWrapper();
			recordWrapper.eq(InviteVehicleRecordPO::getId, vocpo.getRecordId());
			recordWrapper.eq(InviteVehicleRecordPO::getInviteType, InviteTypeEnum.PART_CLUE.getIntCode());
			recordWrapper.eq(InviteVehicleRecordPO::getIsDeleted, 0);
			recordWrapper.set(InviteVehicleRecordPO::getOrderStatus, dto.getClueStatus());
			recordWrapper.set(InviteVehicleRecordPO::getOrderFinishDate, dto.getDataUpdateDate());
			recordWrapper.set(InviteVehicleRecordPO::getUpdatedAt, date);

			// 存在核销经销商
			if (null != zeroAttInfo) {
				logger.info("updateClueStatus null != zeroAttInfo");
				recordWrapper.set(InviteVehicleRecordPO::getFinishDealerCode, zeroAttInfo.getVerificationDealerCode());
			}

			// 存在工单信息
			if (null != repairOrderVO) {
				logger.info("updateClueStatus null != repairOrderVO");
				recordWrapper.set(InviteVehicleRecordPO::getRoNo, repairOrderVO.getRoNo());
				recordWrapper.set(InviteVehicleRecordPO::getRoCreateDate, repairOrderVO.getRoCreateDate());
				recordWrapper.set(InviteVehicleRecordPO::getRepairTypeCode, repairOrderVO.getRepairTypeCode());
				recordWrapper.set(InviteVehicleRecordPO::getOutMileage, repairOrderVO.getOutMileage());
			}

			// 如果是未完成状态需要抹掉核销信息
			if (Objects.equals(clueStatus, CloseStatusEnum.INCOMPLETE.getCode())) {
				logger.info("updateClueStatus clueStatus = INCOMPLETE");
				recordWrapper.set(InviteVehicleRecordPO::getOrderFinishDate, null);
				recordWrapper.set(InviteVehicleRecordPO::getFinishDealerCode, null);
				recordWrapper.set(InviteVehicleRecordPO::getRoNo, null);
				recordWrapper.set(InviteVehicleRecordPO::getRoCreateDate, null);
				recordWrapper.set(InviteVehicleRecordPO::getRepairTypeCode, null);
				recordWrapper.set(InviteVehicleRecordPO::getOutMileage, null);
			}

			row = inviteVehicleRecordMapper.update(null, recordWrapper);
			logger.info("updateClueStatus inviteVehicleRecord update row:{}", row);
		}
		logger.info("updateClueStatus2 end...");
	}

	/**
	 *当零附件线索下发的时候，如果存在重复领用id则把历史线索信息关闭，同时产生新的线索信息
	 */
	public void partClueCloseOptimize(LiteCrmClueDTO dto,String dealerCode,String detailId,long newId) {
		if (StringUtils.isEmpty(detailId)){
			logger.info("partClueCloseOptimize StringUtils.isEmpty(detailId)");
			return;
		}

		logger.info("partClueCloseOptimize LiteCrmClueDTO dto,String dealerCode,String detailId,long newId,:{},{},{},{}",dto,dealerCode,detailId,newId);
		InviteVehicleRecordPO inviteVehicleRecordPO = vocInviteVehicleTaskRecordMapper.queryTtInviteVehicleRecordByOrderId(dealerCode, detailId);
		//线索状态未完成则把此线索进行关闭
		if (Objects.nonNull(inviteVehicleRecordPO) && newId != inviteVehicleRecordPO.getId()){
			//不等于当前插入的id
			int row=0;
			if (inviteVehicleRecordPO.getOrderStatus().toString().equals(CloseStatusEnum.INCOMPLETE.getCode())){
				logger.info("partClueCloseOptimize 线索状态未完成则把此线索进行关闭:{}",inviteVehicleRecordPO);
				row = inviteVehicleRecordMapper.closeLeadById(Arrays.asList(inviteVehicleRecordPO.getId()), CloseStatusEnum.OVERDUE_CLOSED.getIntCode());
				logger.info("inviteVehicleRecordMapper.closeLeadById row :{}",row);
			}
			//赋值旧线索id
			logger.info("closeLeadById , partClueCloseOptimize 赋值旧线索id:{}", inviteVehicleRecordPO.getId());
			LambdaUpdateWrapper<VocInviteVehicleTaskRecordPo> updateWrapper = new UpdateWrapper().lambda();
			updateWrapper.eq(VocInviteVehicleTaskRecordPo::getRecordId, newId);
			updateWrapper.set(VocInviteVehicleTaskRecordPo::getReplaceRecordId,inviteVehicleRecordPO.getId());
			vocInviteVehicleTaskRecordMapper.update(null,updateWrapper);

			//需要把关闭的线索的
			// 1.跟进记录（如有）
			// 2.sa呼叫登记（如有）
			// 3.通话详情（如有）
			// 4.通话录音（如有）进行copay到新线索：

			//跟进记录 tt_invite_vehicle_record_detail
			List<InviteVehicleRecordDetailPO> inviteVehicleRecordInfoList = inviteVehicleRecordDetailMapper.getInviteVehicleRecordInfo(inviteVehicleRecordPO.getId());
			logger.info("inviteVehicleRecordInfoList :{}",inviteVehicleRecordInfoList);
			if (!CollectionUtils.isEmpty(inviteVehicleRecordInfoList)){
				for (InviteVehicleRecordDetailPO inviteVehicleRecordDetailPO:inviteVehicleRecordInfoList){
					inviteVehicleRecordDetailPO.setInviteId(newId);
					inviteVehicleRecordDetailPO.setDealerCode(dealerCode);
					inviteVehicleRecordDetailPO.setVin(dto.getVehicleVin());
					logger.info("inviteVehicleRecordInfoList insert po:{}",inviteVehicleRecordDetailPO);
					inviteVehicleRecordDetailMapper.insert(inviteVehicleRecordDetailPO);
				}
			}

			//呼叫登记 tt_sa_customer_number
			LambdaQueryWrapper<SaCustomerNumberPO> queryWrapper = new LambdaQueryWrapper();
			queryWrapper.eq(SaCustomerNumberPO::getInviteId, inviteVehicleRecordPO.getId());
			List<SaCustomerNumberPO> saCustomerNumberList = saCustomerNumberMapper.selectList(queryWrapper);
			logger.info("saCustomerNumberList :{}", saCustomerNumberList);
			if (!CollectionUtils.isEmpty(saCustomerNumberList)) {
				for (SaCustomerNumberPO saCustomerNumber : saCustomerNumberList) {
					//生成新的callId
					String newCallId = UUID.randomUUID().toString();
					//旧的callId
					String oldCallId = saCustomerNumber.getCallId();
					logger.info("生成新的callId:{},旧的callId:{}", newCallId, oldCallId);
					saCustomerNumber.setInviteId(newId);
					saCustomerNumber.setCallId(newCallId);
					saCustomerNumberMapper.insert(saCustomerNumber);
					//通话详情 tt_call_details
					logger.info("通话详情id callId:{}", oldCallId);
					if (StringUtils.isEmpty(oldCallId)) {
						logger.info("StringUtils.isEmpty oldCallId ");
						continue;
					}

					LambdaQueryWrapper<CallDetailsPO> callDetailsQueryWrapper = new LambdaQueryWrapper<>();
					callDetailsQueryWrapper.eq(CallDetailsPO::getCallId, oldCallId);
					List<CallDetailsPO> callDetailsPOS = callDetailsMapper.selectList(callDetailsQueryWrapper);
					logger.info("callDetailsPOS:{}", callDetailsPOS);
					if (CollectionUtils.isEmpty(callDetailsPOS)) {
						logger.info("CollectionUtils.isEmpty(callDetailsPOS)");
						continue;
					}
					List<CallDetailsPO> collectCallDetailsPO = callDetailsPOS.stream().map(e -> {
						e.setCallId(newCallId);
						return e;
					}).collect(Collectors.toList());
					callDetailsService.insertList(collectCallDetailsPO);
					for (CallDetailsPO callDetailsPO:callDetailsPOS) {
						//通话录音 tt_call_voice
						LambdaQueryWrapper<CallVoicePO> callVoiceQueryWrapper = new LambdaQueryWrapper<>();
						callVoiceQueryWrapper.eq(CallVoicePO::getCallId, oldCallId);
						callVoiceQueryWrapper.eq(CallVoicePO::getSessionId, callDetailsPO.getSessionId());
						List<CallVoicePO> callVoicePOS = callVoiceMapper.selectList(callVoiceQueryWrapper);
						logger.info("callVoicePOS:{}", callVoicePOS);
						if (!CollectionUtils.isEmpty(callVoicePOS)) {
							List<CallVoicePO> collectCallVoicePO = callVoicePOS.stream().map((e) -> {
								e.setCallId(newCallId);
								return e;
							}).collect(Collectors.toList());
							callVoiceService.insertList(collectCallVoicePO);
						}
					}

				}
			}

			//发送MQ
			if (row > 0) {
				logger.info("partClueCloseOptimize row > 0");
				publishEvent(inviteVehicleRecordPO);
			}
		}else {
			logger.info("partClueCloseOptimize 不存在未关闭的线索");
		}
	}

	private void publishEvent(InviteVehicleRecordPO recordPO) {
		logger.info("partClueCloseOptimize publishEvent");
		FaultLightTopicDTO faultLightTopicDTO = new FaultLightTopicDTO();
		faultLightTopicDTO.setUpdateTime(new Date());
		faultLightTopicDTO.setId(recordPO.getIcmId());
		faultLightTopicDTO.setBizStatus(String.valueOf(CloseStatusEnum.OVERDUE_CLOSED.getIntCode()));
		faultLightTopicDTO.setLeadsType(LeadsTypeEnum.POST_ZERMATT_LEADS.getCode());
		faultLightTopicDTO.setDealerCode(recordPO.getDealerCode());
		List<FaultLightTopicDTO> list = new ArrayList<>();
		list.add(faultLightTopicDTO);
		logger.info("partClueCloseOptimize LeadStatusChangeEvent:{}", faultLightTopicDTO);
		eventPublisher.publishEvent(new LeadStatusChangeEvent(list));
	}
}
