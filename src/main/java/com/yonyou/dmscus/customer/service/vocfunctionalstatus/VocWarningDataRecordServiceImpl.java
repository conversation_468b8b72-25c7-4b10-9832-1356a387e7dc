package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.voc.VocWarningDataRecordMapper;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataLogPo;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataRecordPo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @title: VocWarningDataRecordServiceImpl
 * @projectName dmscus.customer
 * @description:
 * @date 2022/11/118:47
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class VocWarningDataRecordServiceImpl implements  VocWarningDataRecordService {
    Logger logger= LoggerFactory.getLogger(this.getClass());

    @Autowired
    VocWarningDataRecordMapper  vocWarningDataRecordMapper;

    @Override
    public List<VocWarningDataRecordPo> selectListWarningDataRecord(List<VocWarningDataLogPo> x) {
        return vocWarningDataRecordMapper.selectListWarningDataRecord(x);
    }

    @Override
    public int insertList(List<VocWarningDataRecordPo> insertList) {
        addList(insertList);
        return insertList.size();
    }

    @Override
    public int updateList(List<VocWarningDataRecordPo> updateList) {
        update(updateList);
        return updateList.size();
    }

    @Override
    public int updateWarningIsExecute(List<VocWarningDataRecordPo> list){
        if (CollectionUtils.isNotEmpty(list)){
            return vocWarningDataRecordMapper.updateWarningIsExecute(list);
        }
        return 0;
    }

    @Override
    public List<VocWarningDataRecordPo> selectVocWarningDataRecordPoByModfiyTime(String dateTime, Integer begIndex, Integer partitionSize) {
       QueryWrapper<VocWarningDataRecordPo>  qw =  new QueryWrapper<>();
        qw.eq("modify_time",dateTime);
        qw.eq("is_execute",0);
        qw.eq("is_deleted",0);
        qw.orderByDesc("id");
        qw.last(" limit "+begIndex+","+partitionSize);
        return vocWarningDataRecordMapper.selectList(qw);
    }

    @Override
    public String selectKmByVin(String vin, String datetime, String endtime) {

        return vocWarningDataRecordMapper.selectOneByVinAndDate(vin,datetime,endtime);
    }

    @Override
    public int selectByVin(String vin) {
        QueryWrapper<VocWarningDataRecordPo>  qw =  new QueryWrapper<>();
        qw.eq("is_deleted",0);
        qw.eq("vin",vin);
        return vocWarningDataRecordMapper.selectCount(qw);
    }

    @Override
    public int selectWarningdailyReodeByVin(String vin) {
        LambdaQueryWrapper<VocWarningDataRecordPo> qw = new LambdaQueryWrapper<>();
        qw.eq(VocWarningDataRecordPo::getIsDeleted ,"0");
        qw.eq(VocWarningDataRecordPo::getVin,vin);
        qw.orderByDesc(VocWarningDataRecordPo::getId);
        qw.last("limit 2");
        List<VocWarningDataRecordPo>    list = vocWarningDataRecordMapper.selectList(qw);
        if(CollUtil.isNotEmpty(list)&&  list.size()>1){
            VocWarningDataRecordPo pox =  list.get(1);
             if(  pox.getStatusValue().equals(CommonConstants.VOC_NORMAL_NUM ) ) {
                 return 1 ;
             }else{
                 return  0 ;
             }
        }
        return 0;
    }


    /**
     * @param list
     */
    private void addList(List<VocWarningDataRecordPo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        int numPerTimesw = 200;
        if (list.size() <= numPerTimesw) {
            batchLogAdd(list); //此处插入少于200条list
            logger.info("voc亮灯定时任务保存数据VocWarningDataRecordPo成功，数据小于200");
        } else {

            int maxIndexv = list.size();
            int maxTimesv = maxIndexv / numPerTimesw;
            maxTimesv += (maxIndexv % numPerTimesw) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimesv) {
                int fromIndex = numPerTimesw * currentTimes;
                int toIndex = fromIndex + numPerTimesw;
                toIndex = toIndex > maxIndexv ? maxIndexv : toIndex;
                List<VocWarningDataRecordPo> subList = list.subList(fromIndex, toIndex);
                batchLogAdd(subList);//此处循环插入200条list
                logger.info("voc亮灯定时任务保存数据VocWarningDataRecordPo成功，数据大于200{},{}", fromIndex, toIndex);
                currentTimes++;
            }
        }

    }

    /**
     * 批量新增
     *
     * @param list
     */
    private void batchLogAdd(List<VocWarningDataRecordPo> list) {
         vocWarningDataRecordMapper.insertList(list);
    }

    /**
     * @param list
     */
    private void update(List<VocWarningDataRecordPo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int numPerTimesww = 200;
        if (list.size() <= numPerTimesww) {
            batchRecordUpdate(list); //此处插入少于200条list
            logger.info("voc亮灯定时任务修改数据VocWarningDataRecordPo成功，数据小于200");
        } else {
            int maxIndexvw = list.size();
            int maxTimesv = maxIndexvw / numPerTimesww;
            maxTimesv += (maxIndexvw % numPerTimesww) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimesv) {
                int fromIndex = numPerTimesww * currentTimes;
                int toIndex = fromIndex + numPerTimesww;
                toIndex = toIndex > maxIndexvw ? maxIndexvw : toIndex;
                List<VocWarningDataRecordPo> subList = list.subList(fromIndex, toIndex);
                batchRecordUpdate(subList);//此处循环插入200条list
                logger.info("voc亮灯定时任务修改数据VocWarningDataRecordPo成功，数据大于200{},{}", fromIndex, toIndex);
                currentTimes++;
            }
        }

    }

    /**
     * 批量修改
     *
     * @param list
     */
    private void batchRecordUpdate(List<VocWarningDataRecordPo> list) {
         vocWarningDataRecordMapper.updateList(list);
    }
    @Override
    public VocWarningDataRecordPo selectVocWarningDataRecordPo(String vin) {
        LambdaQueryWrapper<VocWarningDataRecordPo> qw = new LambdaQueryWrapper<>();
        qw.eq(VocWarningDataRecordPo::getIsDeleted ,"0");
        qw.eq(VocWarningDataRecordPo::getVin,vin);
        qw.orderByDesc(VocWarningDataRecordPo::getId);
        qw.last("limit 1");
        List<VocWarningDataRecordPo>    list = vocWarningDataRecordMapper.selectList(qw);
        if(CollUtil.isNotEmpty(list))
            return list.get(0);
        return  null;
    }

    @Override
    public int selectCountByVinAndModfiy(String vin, String dateTime) {
        LambdaQueryWrapper<VocWarningDataRecordPo> qw = new LambdaQueryWrapper<>();
        qw.eq(VocWarningDataRecordPo::getIsDeleted ,"0");
        qw.eq(VocWarningDataRecordPo::getVin,vin);
        qw.eq(VocWarningDataRecordPo::getModifyTime,dateTime);
        return  vocWarningDataRecordMapper.selectCount(qw);
    }

}
