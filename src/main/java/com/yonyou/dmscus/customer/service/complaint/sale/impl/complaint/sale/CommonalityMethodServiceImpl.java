package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintInfoMapper;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyInfoMapper;
import com.yonyou.dmscus.customer.dto.*;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.feign.MidEndAuthCenterClient;
import com.yonyou.dmscus.customer.feign.MidUserOrganizationClient;
import com.yonyou.dmscus.customer.middleInterface.RequestDTO;
import com.yonyou.dmscus.customer.service.complaint.sale.CommonalityMethodService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@AllArgsConstructor
@Service
public class CommonalityMethodServiceImpl implements CommonalityMethodService {

    @Resource
    private ComplaintInfoMapper complaintInfoMapper;

    @Resource
    private GoodwillApplyInfoMapper goodwillApplyInfoMapper;

    @Resource
    private MidUserOrganizationClient midUserOrganizationClient;

    @Resource
    private MidEndAuthCenterClient midEndAuthCenterClient;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void regionInformationRefresh(List<String> dealerCodes) {
        List<String> result = null;
        if (CollectionUtils.isEmpty(dealerCodes)){
            List<String> dealerList = complaintInfoMapper.queryDealer();
            List<String> dealerSaleList = complaintInfoMapper.querySaleDealer();
            List<String> dealerList1 = goodwillApplyInfoMapper.queryDealer();
            dealerList.addAll(dealerSaleList);
            dealerList.addAll(dealerList1);
            // 使用Stream API去重
            List<String> distinctDealerList = dealerList.stream()
                    .distinct()
                    .collect(Collectors.toList());
            logger.info("CommonalityMethodServiceImpl.regionInformationRefresh 待刷新区域信息经销商 distinctDealerList：【{}】", JSON.toJSONString(distinctDealerList));
            int chunkSize = 100;
            result = distinctDealerList.stream()
                    // 分组依据：元素的索引除以chunkSize的商
                    .collect(Collectors.groupingBy(it -> dealerList.indexOf(it) / chunkSize))
                    // 将每个分组转换为逗号连接的字符串
                    .values().stream()
                    .map(group -> String.join(",", group))
                    .collect(Collectors.toList());
        }else {
            logger.info("regionInformationRefresh.dealerCodes:{}", dealerCodes);
            result = dealerCodes;
        }

        List<CompanyDetailInfoDTO> companyDetailInfoDTOList = new ArrayList<>();
        logger.info("CommonalityMethodServiceImpl.regionInformationRefresh 分组后经销商 result：【{}】", JSON.toJSONString(result));

        result.forEach(
                s -> {
                    // 查询母店自身信息
                    CompanyParamDTO parentParamDto = new CompanyParamDTO();
                    parentParamDto.setCompanyCode(s);
                    logger.info("SaleComplaintInfoServiceImpl.queryCompanyInfoList 子店查询母店信息 入参parentParamDto：【{}】", JSON.toJSONString(parentParamDto));
                    ResponseDTO<List<CompanyDetailInfoDTO>> parentResponseDTO = midUserOrganizationClient.selectByCompanyCode(parentParamDto);
                    logger.info("SaleComplaintInfoServiceImpl.queryCompanyInfoList 子店查询母店信息 入参parentParamDto：【{}】 出参parentResponseDTO:【{}】", JSON.toJSONString(parentParamDto), JSON.toJSONString(parentResponseDTO));
                    if (parentResponseDTO.Success()) {
                        List<CompanyDetailInfoDTO> parentCompanyDetailInfoDTOList = parentResponseDTO.getData();
                        companyDetailInfoDTOList.addAll(parentCompanyDetailInfoDTOList);
                    }
                }
        );

        if (CollectionUtils.isEmpty(companyDetailInfoDTOList)){
            return;
        }

        List<CompanyDetailInfoDTO> companyDetailInfoDTOS = companyDetailInfoDTOList.stream()
                .filter(v -> (ObjectUtils.isNotEmpty(v.getAfterBigAreaId()) || ObjectUtils.isNotEmpty(v.getAfterBigAreaName()) || ObjectUtils.isNotEmpty(v.getAfterSmallAreaId()) || ObjectUtils.isNotEmpty(v.getAfterSmallAreaName()))
                    && (ObjectUtils.isNotEmpty(v.getSaleBigAreaId()) || ObjectUtils.isNotEmpty(v.getSaleBigAreaName()) || ObjectUtils.isNotEmpty(v.getSaleSmallAreaId()) || ObjectUtils.isNotEmpty(v.getSaleSmallAreaName()))
                )
                .collect(Collectors.toList());
        logger.info("CommonalityMethodServiceImpl.companyDetailInfoDTOList 经销商信息去除空数据 companyDetailInfoDTOS：【{}】", JSON.toJSONString(companyDetailInfoDTOS));

        if (CollectionUtils.isEmpty(companyDetailInfoDTOS)){
            return;
        }

        QueryUserByOrgTypeDTO queryUserByOrgTypeDTO=new QueryUserByOrgTypeDTO();
        queryUserByOrgTypeDTO.setRoleCode(CommonConstants.SHQYJL_ROLECODE );
        RequestDTO<QueryUserByOrgTypeDTO> queryUserByOrgTypeRequestDTO= new RequestDTO<>();
        queryUserByOrgTypeRequestDTO.setData(queryUserByOrgTypeDTO);
        ResponseDTO<List<UserOrgInfoDTO>> listResponseDTO = midEndAuthCenterClient.queryUserByOrgType(queryUserByOrgTypeRequestDTO);
        List<UserOrgInfoDTO> userOrgInfoDTO = listResponseDTO.getData();
        logger.info("CommonalityMethodServiceImpl.companyDetailInfoDTOList 售后区域经理信息 userList：【{}】", JSON.toJSONString(userOrgInfoDTO));

        if (!CollectionUtils.isEmpty(userOrgInfoDTO)){
            Map<Long, String> userIdToUsernameMap = userOrgInfoDTO.stream()
                    .filter(Objects::nonNull)  // 过滤掉列表中的null元素
                    .filter(dto -> dto.getOrgid() != null && dto.getUsername() != null) // 过滤掉 OrgId 或 UserName 为空的对象
                    .collect(Collectors.toMap(
                            UserOrgInfoDTO::getOrgid,  // 作为Map的键
                            UserOrgInfoDTO::getUsername,  // 作为Map的值
                            (existing, replacement) -> existing  // 如果键重复，保留第一个出现的值
                    ));

            logger.info("CommonalityMethodServiceImpl.companyDetailInfoDTOList 售后区域经理信息 userList：【{}】", JSON.toJSONString(userIdToUsernameMap));

            companyDetailInfoDTOS.forEach(companyDetailInfoDTO -> {
                Long afterSmallAreaId = companyDetailInfoDTO.getAfterSmallAreaId();
                if (userIdToUsernameMap.containsKey(afterSmallAreaId)) {
                    companyDetailInfoDTO.setRegionManager(userIdToUsernameMap.get(afterSmallAreaId));
                }
            });
        }

        queryUserByOrgTypeDTO.setRoleCode(CommonConstants.XSQYJL_ROLECODE );
        queryUserByOrgTypeRequestDTO.setData(queryUserByOrgTypeDTO);
        ResponseDTO<List<UserOrgInfoDTO>> listResponseDTO1 = midEndAuthCenterClient.queryUserByOrgType(queryUserByOrgTypeRequestDTO);
        List<UserOrgInfoDTO> userOrgInfoDTO1 = listResponseDTO1.getData();
        logger.info("CommonalityMethodServiceImpl.companyDetailInfoDTOList 销售区域经理信息 userList：【{}】", JSON.toJSONString(userOrgInfoDTO1));

        if (!CollectionUtils.isEmpty(userOrgInfoDTO1)){
            Map<Long, String> userIdToUsernameMap = userOrgInfoDTO1.stream()
                    .filter(Objects::nonNull)  // 过滤掉列表中的null元素
                    .filter(dto -> dto.getOrgid() != null && dto.getUsername() != null) // 过滤掉 OrgId 或 UserName 为空的对象
                    .collect(Collectors.toMap(
                            UserOrgInfoDTO::getOrgid,  // 作为Map的键
                            UserOrgInfoDTO::getUsername,  // 作为Map的值
                            (existing, replacement) -> existing  // 如果键重复，保留第一个出现的值
                    ));

            logger.info("CommonalityMethodServiceImpl.companyDetailInfoDTOList 销售区域经理信息 userList：【{}】", JSON.toJSONString(userIdToUsernameMap));

            companyDetailInfoDTOS.forEach(companyDetailInfoDTO -> {
                Long saleSmallAreaId = companyDetailInfoDTO.getSaleSmallAreaId();
                if (userIdToUsernameMap.containsKey(saleSmallAreaId)) {
                    companyDetailInfoDTO.setSaleRegionManager(userIdToUsernameMap.get(saleSmallAreaId));
                }
            });
        }

        if (CollectionUtils.isEmpty(companyDetailInfoDTOS)){
            return;
        }

        goodwillApplyInfoMapper.regionInformationRefresh(companyDetailInfoDTOS);
        complaintInfoMapper.regionInformationRefresh(companyDetailInfoDTOS);
        complaintInfoMapper.buyRegionInformationRefresh(companyDetailInfoDTOS);
        complaintInfoMapper.saleRegionInformationRefresh(companyDetailInfoDTOS);
        complaintInfoMapper.buySaleRegionInformationRefresh(companyDetailInfoDTOS);
        complaintInfoMapper.ccmRegionInformationRefresh(companyDetailInfoDTOS);

    }

}
