package com.yonyou.dmscus.customer.service.impl.talkskill;

    
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.talkskill.TalkskillTypeMapper;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillTypeDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillTypePO;
import com.yonyou.dmscus.customer.service.talkskill.TalkskillTypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


/**
 * <p>
 * 话术业务分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-20
 */
@Service
public class TalkskillTypeServiceImpl implements TalkskillTypeService {
        /**
         * 日志对象
         */
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        TalkskillTypeMapper talkskillTypeMapper;

        /**
         * 根据查询条件返回结果集
         *
         * @param talkskillTypeDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.repair.entity.dto.TalkskillTypeDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<TalkskillTypePO> selectListBySql (TalkskillTypeDTO talkskillTypeDTO){
            if(talkskillTypeDTO ==null){
                talkskillTypeDTO =new TalkskillTypeDTO();
            }
            TalkskillTypePO talkskillTypePo =talkskillTypeDTO.transDtoToPo(TalkskillTypePO.class);
            List<TalkskillTypePO> list = talkskillTypeMapper.selectListBySql(talkskillTypePo);
            if(CommonUtils.isNullOrEmpty(list)){
                return null;
            }else{
                return  list;
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.repair.entity.dto.TalkskillTypeDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public TalkskillTypeDTO getById(Long id){
            TalkskillTypePO talkskillTypePo = talkskillTypeMapper.selectById(id);
            if(talkskillTypePo!=null){
                return talkskillTypePo.transPoToDto(TalkskillTypeDTO.class);
            }else{
                throw new DALException("查询失败,没有符合条件的数据");
            }
        }
        /**
         * 根据DTO 进行数据新增--逐条
         *
         * @param talkskillTypeDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(TalkskillTypeDTO talkskillTypeDTO){
            TalkskillTypePO talkskillTypePo = talkskillTypeDTO.transDtoToPo(TalkskillTypePO.class);
            return talkskillTypeMapper.insert(talkskillTypePo);
        }
        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param talkskillTypeDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, TalkskillTypeDTO talkskillTypeDTO){
            TalkskillTypePO talkskillTypePo = talkskillTypeMapper.selectById(id);
            talkskillTypeDTO.transDtoToPo(talkskillTypePo);
            return talkskillTypeMapper.updateById(talkskillTypePo);
        }
        /**
         * 根据DTO 及ID 进行数据更新
         *
         *
         * @param talkskillTypeDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int updateList(List<TalkskillTypeDTO> talkskillTypeDTO){
            for(int i=0;i<talkskillTypeDTO.size();i++){
                TalkskillTypePO talkskillTypePo = talkskillTypeDTO.get(i).transDtoToPo(TalkskillTypePO.class);;
                //对对象进行赋值操作
                if(talkskillTypePo.getTypeId() == null){
                    //执行更新
                    if (Objects.equals(talkskillTypePo.getTypeCode(), CommonConstants.TALKSKILL_TYPE_CODE_QWIM)) {
                        throw new ServiceBizException(CommonConstants.TALKSKILL_TYPE_CODE_QWIM + " 为定制化类型，请勿重复添加！");
                    }
                    talkskillTypeMapper.insert(talkskillTypePo);
                }else {
                    //执行更新
                    talkskillTypeMapper.updateById(talkskillTypePo);
                }

            }
            return 1;
        }



}
