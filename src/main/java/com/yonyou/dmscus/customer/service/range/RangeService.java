package com.yonyou.dmscus.customer.service.range;

import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesPO;
import com.yonyou.dmscus.customer.entity.vo.EmpByRoleCodeVO;

import java.util.List;

public interface RangeService {

    /*
     * 查询菜单如何设置的
     * */
    Integer getRangeControlFromMid(Integer menuId);


    /*
    * 判断事故线索是否有权限跟进/查看
    * */
    boolean hasRangeAccidentClue(Integer rangeControlFromMid, AccidentCluesPO po, LoginInfoDto infoDto);

    /*
    * 判断事故线索是否有权限分配
    * */
    boolean hasRangeClueAllot(Integer rangeId, List<Integer> followPeoples, LoginInfoDto loginInfo);

    /*
     * 判断事故线索是否有权限分配
     * */
    List<EmpByRoleCodeVO> hasRangeClueAllotPeople( List<EmpByRoleCodeVO> responseDealerUserData, LoginInfoDto loginInfo);

    /*
    * 查询当前用户可以查询的线索
    * */
    List<Long> hasRangeAccidentClueList(Integer rangeId, LoginInfoDto loginInfo);
}
