package com.yonyou.dmscus.customer.service.inviteVehicleDealerAllocate;


import com.yonyou.dmscus.customer.entity.dto.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryDTO;
import com.yonyou.dmscus.customer.service.common.IBaseService;

import java.util.List;


/**
 * <p>
 * 车店分配历史表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface InviteVehicleDealerAllocateHistoryService extends IBaseService<InviteVehicleDealerAllocateHistoryDTO> {

    List<InviteVehicleDealerAllocateHistoryDTO> getAllocationHistory(String vin);
}
