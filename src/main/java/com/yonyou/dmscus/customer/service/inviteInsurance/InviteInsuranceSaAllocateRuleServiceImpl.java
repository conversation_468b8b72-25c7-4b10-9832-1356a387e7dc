package com.yonyou.dmscus.customer.service.inviteInsurance;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceSaAllocateRuleMapper;
import com.yonyou.dmscus.customer.dto.InvitationRuleVcdcParamsVo;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.UserInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceSaAllocateRuleDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceSaAllocateRulePO;
import com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule.InviteSaAllocateRulePO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 续保SA分配规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@Service
public class InviteInsuranceSaAllocateRuleServiceImpl implements InviteInsuranceSaAllocateRuleService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteInsuranceSaAllocateRuleMapper inviteInsuranceSaAllocateRuleMapper;
    @Autowired
    BusinessPlatformService businessPlatformService;

        /**
         * 保存分配规则
         *
         * @param inviteInsuranceSaAllocateRuleDTO
         * @return
         */
        @Override
        public int saveInviteInsuranceSaAllocateRule(InviteInsuranceSaAllocateRuleDTO inviteInsuranceSaAllocateRuleDTO) {
            InviteInsuranceSaAllocateRulePO inviteInsuranceSaAllocateRulePO = inviteInsuranceSaAllocateRuleMapper.selectById(inviteInsuranceSaAllocateRuleDTO
                    .getId());
            //对对象进行赋值操作
            inviteInsuranceSaAllocateRuleDTO.transDtoToPo(inviteInsuranceSaAllocateRulePO);
            return inviteInsuranceSaAllocateRuleMapper.updateById(inviteInsuranceSaAllocateRulePO);
        }


        @Override
        public InviteInsuranceSaAllocateRuleDTO getInviteInsuranceSaAllocateRuleDlr() {
            //获取登录用户信息
            LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
            LambdaQueryWrapper<InviteInsuranceSaAllocateRulePO> queryWrapper = new QueryWrapper().lambda();
            queryWrapper.eq(InviteInsuranceSaAllocateRulePO::getDealerCode, loginInfoDto.getOwnerCode());
            InviteInsuranceSaAllocateRulePO po = inviteInsuranceSaAllocateRuleMapper.selectOne(queryWrapper);
            if (po != null) {
                return po.transPoToDto(InviteInsuranceSaAllocateRuleDTO.class);
            }else{
                return null;
            }
        }

        @Override
        public List<InviteInsuranceSaAllocateRuleDTO> getInviteInsuranceSaAllocateRule(InvitationRuleVcdcParamsVo vo) {
            String areaId = null;
            if(vo.getAreaId()!=null){
                areaId=vo.getAreaId();
            }else if(vo.getAreaManageId()!=null){
                areaId=vo.getAreaManageId();
            }
            List<String> codes = businessPlatformService.getDealercodes(areaId,vo.getLargeAreaId(),vo.getDealerCode(),vo.getDealerName());
            List<InviteInsuranceSaAllocateRulePO> list = inviteInsuranceSaAllocateRuleMapper.getInviteInsuranceSaAllocateRule(codes);
            if (CommonUtils.isNullOrEmpty(list)) {
                return new ArrayList<>();
            } else {
                return list.stream().map(m -> m.transPoToDto(InviteInsuranceSaAllocateRuleDTO.class)).collect(Collectors.toList());
            }
        }

}
