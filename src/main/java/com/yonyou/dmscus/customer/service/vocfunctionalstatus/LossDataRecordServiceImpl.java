package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.domains.dto.basedata.ResponseDTO;
import com.yonyou.dmscloud.framework.service.excel.ExcelExportColumn;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscus.customer.bean.dto.DownloadDTO;
import com.yonyou.dmscus.customer.dao.voc.LossDataRecordMapper;
import com.yonyou.dmscus.customer.dto.LossDataRecordOss;
import com.yonyou.dmscus.customer.dto.LossDataRecordVo;
import com.yonyou.dmscus.customer.entity.dto.loss.LossDataRecordDto;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.LossDataRecordPo;
import com.yonyou.dmscus.customer.util.common.VolvoHttpUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: LossDataRecordServiceImpl
 * @projectName dmscus.customer
 * @date 2022/12/1217:45
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class LossDataRecordServiceImpl implements  LossDataRecordService {

    /**
     * 日志对象
     */
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    public static final String DOWNLOAD_RETURN = "http://dmscus-customer/loss/exportExcel/oss";

    @Autowired
    VolvoHttpUtils volvoHttpUtils;

    @Autowired
    RestTemplate restTemplate;
    @Autowired
    private LossDataRecordMapper lossDataRecordMapper;
    //日志对象
    public int save(LossDataRecordPo po) {
        return lossDataRecordMapper.insert(po);
    }

    @Override
    public LossDataRecordPo selectByVin(String vin) {
       LambdaQueryWrapper<LossDataRecordPo> queryWrapper =  new LambdaQueryWrapper<>();
        queryWrapper.eq(LossDataRecordPo::getVin,vin)
                    .eq(LossDataRecordPo::getIsDeleted, 0)
                    .orderByDesc(LossDataRecordPo::getCreatedAt)
                    .last("limit 1");
        return lossDataRecordMapper.selectOne(queryWrapper);
    }

    @Override
    public int update(LossDataRecordPo po) {
        return lossDataRecordMapper.updateById(po);
    }

    @Override
    public void insertList(List<LossDataRecordPo> pos) {
        addLists(pos);
    }

    @Override
    public IPage<LossDataRecordDto> selectPageBysql(Page<LossDataRecordVo> page, LossDataRecordVo lossDataRecordDto) {
            if(lossDataRecordDto.getIndexFactory()==0){
                //获取登录用户信息
                LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
                lossDataRecordDto.setDealerCode(loginInfoDto.getOwnerCode());
            }
            return  lossDataRecordMapper.selectPageList(page,lossDataRecordDto);
    }

    @Override
    public List<Map> exportExcel(LossDataRecordVo dto) {
        Page page = new Page<>(dto.getCurrentPage(),dto.getPageSize());
        return  lossDataRecordMapper.exportExcel(page,dto);
    }





    @Override
    public void exportExcelOss(LossDataRecordOss dto) {
        logger.info("流失客户申请导出开始===================================》");
        try {
            if(dto.getIndexFactory()==0){
                //获取登录用户信息
                LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
                dto.setDealerCode(loginInfoDto.getOwnerCode());
            }
            String url="http://download-service/download/exportExcel";
            List<ExcelExportColumn> exportColumnList = getExcelColumn();
            DownloadDTO downloadDTO = new DownloadDTO();
            downloadDTO.setQueryParams(dto.toMaps());
            downloadDTO.setExcelName("流失客户.xlsx");
            downloadDTO.setSheetName("流失客户");
            downloadDTO.setServiceUrl(DOWNLOAD_RETURN);
            downloadDTO.setExcelExportColumnList(exportColumnList);
            exportColumnList(url,downloadDTO.toMaps());
        }catch (Exception e){
            logger.info("流失客户上传导出：出现异常:e{}",e);
        }
    }



    //列
    private List<ExcelExportColumn> getExcelColumn() {
        List<ExcelExportColumn> exportColumnList = new ArrayList<>();
        exportColumnList.add(new ExcelExportColumn("vin", "VIN"));
        exportColumnList.add(new ExcelExportColumn("dealerCode", "经销商代码"));
        exportColumnList.add(new ExcelExportColumn("orderAt", "最后一次保养时间"));
        exportColumnList.add(new ExcelExportColumn("lastDealerCode", "最后一次保养经销商"));
        exportColumnList.add(new ExcelExportColumn("recordAt", "流失线索下发时间"));
        exportColumnList.add(new ExcelExportColumn("invoiceDate", "开票时间"));
        exportColumnList.add(new ExcelExportColumn("isActive", "是否激活"));
        exportColumnList.add(new ExcelExportColumn("activeDealerCode", "激活经销商"));
        exportColumnList.add(new ExcelExportColumn("activeAt", "激活时间"));
        exportColumnList.add(new ExcelExportColumn("ownerName", "客户姓名"));
        exportColumnList.add(new ExcelExportColumn("mobile", "手机"));

        return exportColumnList;
    }

    public ResponseDTO exportColumnList(String url, Map param){
        ResponseDTO response = new ResponseDTO();
        HttpHeaders headers =volvoHttpUtils.getHeaders();
        try {
            HttpEntity<Map<String,Object>> httpEntity = new HttpEntity<>(param, headers);
            ResponseEntity<ResponseDTO> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity,
                    ResponseDTO.class);

            if(responseEntity.getBody()!=null) {
                ObjectMapper objectMapper = new ObjectMapper();
                //转换返回对象
                response = objectMapper.convertValue(responseEntity.getBody(), ResponseDTO.class);
            }else {
                response.setReturnCode("error");
                response.setReturnMessage("请求接口返回对象responseEntity为空");
            }
        } catch (Exception e) {
            logger.error("download-service接口请求地址:{},请求参数:{}，返回失败:{}",e);
            if (fool(e)) {
                response.setReturnCode("error");
                response.setReturnMessage(e.getMessage());
            }
        }
        return response;
    }

    private  boolean  fool(Exception e){
        return  e != null;
    }

    /**
     *
     *  @param list
     */
    private void addLists(List<LossDataRecordPo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int numPerTimes = 200;
        if (list.size() <= numPerTimes) {
            batchLogAdds(list); //此处插入少于200条list
            logger.info("初始化客户流失成功，数据小于200");
        } else {
            int maxIndexx = list.size();
            int maxTimes = maxIndexx / numPerTimes;
            maxTimes += (maxIndexx % numPerTimes) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimes) {
                int fromIndex = numPerTimes * currentTimes;
                int toIndex = fromIndex + numPerTimes;
                toIndex = toIndex > maxIndexx ? maxIndexx : toIndex;
                List<LossDataRecordPo> subList = list.subList(fromIndex, toIndex);
                batchLogAdds(subList);//此处循环插入200条list
                logger.info("初始化客户流失成功，数据大于200{},{}",fromIndex,toIndex);
                currentTimes++;
            }
        }

    }

    /**
     *批量新增
     *  @param list
     */
    private void batchLogAdds(List<LossDataRecordPo> list) {
        lossDataRecordMapper.insertList(list);
    }
}
