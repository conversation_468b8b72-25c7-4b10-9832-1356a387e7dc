package com.yonyou.dmscus.customer.service.impl.voicemanage;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.voicemanage.SaWorkNumberMapper;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.SaWorkNumberDTO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaWorkNumberPO;
import com.yonyou.dmscus.customer.service.voicemanage.SaWorkNumberService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.dmscus.customer.utils.ai.CallRegisterChild;
import com.yonyou.dmscus.customer.utils.ai.DccHttpHelper;
import com.yonyou.dmscus.customer.utils.ai.DccResponseUtil;


/**
 * <p>
 * AI语音工作号管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Service
public class SaWorkNumberServiceImpl implements SaWorkNumberService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    SaWorkNumberMapper saWorkNumberMapper;
    
    @Value("${ai.telecom.common.url_mapping_workbind}")
    String URL_MAPPING_WORKBIND;  //工作号绑定
    
    @Value("${ai.telecom.common.url_mapping_workbind}")
    String URL_MAPPING_UNWORKBIND; //工作号解绑

    /**
     * 分页查询对应数据
     *
     * @param page            分页对象
     * @param saWorkNumberDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.SaWorkNumberDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<SaWorkNumberDTO> selectPageBysql(Page page, SaWorkNumberDTO saWorkNumberDTO) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (saWorkNumberDTO == null) {
            saWorkNumberDTO = new SaWorkNumberDTO();
        }
        SaWorkNumberPO saWorkNumberPO = saWorkNumberDTO.transDtoToPo(SaWorkNumberPO.class);
        //只查询登录经销商
        saWorkNumberPO.setDealerCode(loginInfoDto.getOwnerCode());
        List<SaWorkNumberPO> list = saWorkNumberMapper.selectPageBySql(page, saWorkNumberPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<SaWorkNumberDTO> result = list.stream().map(m -> m.transPoToDto(SaWorkNumberDTO.class)).collect
                    (Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param saWorkNumberDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.SaWorkNumberDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<SaWorkNumberDTO> selectListBySql(SaWorkNumberDTO saWorkNumberDTO) {
        if (saWorkNumberDTO == null) {
            saWorkNumberDTO = new SaWorkNumberDTO();
        }
        SaWorkNumberPO saWorkNumberPO = saWorkNumberDTO.transDtoToPo(SaWorkNumberPO.class);
        List<SaWorkNumberPO> list = saWorkNumberMapper.selectListBySql(saWorkNumberPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(SaWorkNumberDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.SaWorkNumberDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public SaWorkNumberDTO getById(Long id) {
        SaWorkNumberPO saWorkNumberPO = saWorkNumberMapper.selectById(id);
        if (saWorkNumberPO != null) {
            return saWorkNumberPO.transPoToDto(SaWorkNumberDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param saWorkNumberDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(SaWorkNumberDTO saWorkNumberDTO) {
        //对对象进行赋值操作
        SaWorkNumberPO saWorkNumberPO = saWorkNumberDTO.transDtoToPo(SaWorkNumberPO.class);
        //执行插入
        int row = saWorkNumberMapper.insert(saWorkNumberPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id              主键ID
     * @param saWorkNumberDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, SaWorkNumberDTO saWorkNumberDTO) {
        SaWorkNumberPO saWorkNumberPO = saWorkNumberMapper.selectById(id);
        //对对象进行赋值操作
        saWorkNumberDTO.transDtoToPo(saWorkNumberPO);
        //执行更新
        int row = saWorkNumberMapper.updateById(saWorkNumberPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = saWorkNumberMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = saWorkNumberMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 保存AI语音工作号
     * @param saWorkNumberDTO
     * @return
     */
    @Override
    public int saveSaWorkNumber(SaWorkNumberDTO saWorkNumberDTO) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //对对象进行赋值操作
        SaWorkNumberPO saWorkNumberPO = saWorkNumberDTO.transDtoToPo(SaWorkNumberPO.class);
        saWorkNumberPO.setDealerCode(loginInfoDto.getOwnerCode());
        //校验跟进人员是否已存在
        if(this.hasExistSa(saWorkNumberPO)){
            throw new DALException("跟进人员已存在!");
        }
        if(saWorkNumberDTO.getId()==null){
            //校验输入的跟进人员手机号是否已绑定AI语音工作号
            if(this.checkSaNumber(saWorkNumberPO)){
                throw new DALException("当前手机号已绑定AI语音工作号,若需更新请先解绑后再重新绑定！");
            }
            //校验输入的AI语音工作号是否已有绑定记录
            if(this.checkWorkNumber(saWorkNumberPO)){
                throw new DALException("当前AI语音工作号已绑定其它手机号,若需更新请先解绑后再重新绑定！");
            }

            //执行插入
            saWorkNumberMapper.insert(saWorkNumberPO);
            //电信接口进行工作号绑定(未实现)失败抛异常回滚
            Map<String,Object> map=new HashMap<String,Object>();
            map.put("holderNumber", saWorkNumberDTO.getSaNumber());
            map.put("workNumber", saWorkNumberDTO.getWorkNumber());
            logger.info("AX 关系绑定:"+map);
            String str= DccHttpHelper.httpPost(URL_MAPPING_WORKBIND, map);
            DccResponseUtil response=JSONObject.parseObject(str,DccResponseUtil.class);
            if(!"0".equals(response.getCode())) {
            	throw new DALException(response.getMessage());
            }
            logger.info("AX 关系绑定结束:"+str);
        }else{
            saWorkNumberPO = saWorkNumberMapper.selectById(saWorkNumberDTO.getId());
            //对对象进行赋值操作
            saWorkNumberPO.setSaId(saWorkNumberDTO.getSaId());
            saWorkNumberPO.setSaName(saWorkNumberDTO.getSaName());
            //执行更新
            saWorkNumberMapper.updateById(saWorkNumberPO);
        }
        return 1;
    }

    /**
     * 解绑
     * @param id
     * @return
     */
    @Override
    public int noBinding(Long id) {
    	SaWorkNumberPO saWorkNumberPO=saWorkNumberMapper.selectById(id);
        //删除工作号绑定关系
        this.deleteById(id);
        //调用电信接口进行AI语音工作号解绑(未实现)失败抛异常回滚
        Map<String,Object> map=new HashMap<String,Object>();
        map.put("workNumber", saWorkNumberPO.getWorkNumber());
        logger.info("AX 关系解绑:"+map);
        String str=DccHttpHelper.httpPost(URL_MAPPING_UNWORKBIND, map);
        DccResponseUtil response=JSONObject.parseObject(str,DccResponseUtil.class);
        if(!"0".equals(response.getCode())) {
        	throw new DALException(response.getMessage());
        }
        logger.info("AX 关系解绑结束:"+str);
        return 1;
    }

    /**
     * 校验输入的AI语音工作号是否已有绑定记录
     * @param saWorkNumberPO
     * @return
     */
    private boolean checkWorkNumber(SaWorkNumberPO saWorkNumberPO) {
        Integer rs = saWorkNumberMapper.checkWorkNumber(saWorkNumberPO);
        if(rs!=null){
            return true;
        }
        return false;
    }

    /**
     * 校验输入的跟进人员手机号是否已绑定AI语音工作号
     * @param saWorkNumberPO
     * @return
     */
    private boolean checkSaNumber(SaWorkNumberPO saWorkNumberPO) {
        Integer rs = saWorkNumberMapper.checkSaNumber(saWorkNumberPO);
        if(rs!=null){
            return true;
        }
        return false;
    }

    /**
     * 校验跟进人员是否已存在
     * @param saWorkNumberPO
     * @return
     */
    private boolean hasExistSa(SaWorkNumberPO saWorkNumberPO) {
        Integer rs = saWorkNumberMapper.hasExistSa(saWorkNumberPO);
        if(rs!=null){
            return true;
        }
        return false;
    }
    
}
