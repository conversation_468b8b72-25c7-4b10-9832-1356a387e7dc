package com.yonyou.dmscus.customer.service.impl.faultLight;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.faultLight.FaultLightDisposeMapper;
import com.yonyou.dmscus.customer.entity.dto.faultLight.BatchUpdateDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightDTO;
import com.yonyou.dmscus.customer.entity.excel.faultLight.FaultLightExcel;
import com.yonyou.dmscus.customer.entity.po.faultLight.FaultLightPO;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightDisposeService;
import com.yonyou.dmscus.customer.utils.ClazzConverter;
import com.yonyou.dmscus.customer.utils.excel.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
@RefreshScope
public class FaultLightDisposeServiceImpl implements FaultLightDisposeService {

    @Resource
    private FaultLightDisposeMapper faultLightDisposeMapper;

    @Value("${fault.cumulative.number:1,2,3,4,5}")
    private String number;


    /**
     * 故障灯类别分页查询
     *
     * @param page          Page
     * @param dto           FaultLightDTO
     * @return IPage
     */
    @Override
    public IPage<FaultLightDTO> selectPageTypeInfo(Page<FaultLightDTO> page, FaultLightDTO dto) {

        FaultLightPO faultLightPO = ClazzConverter.converterClass(dto, FaultLightPO.class);

        List<FaultLightPO> faultLightPOS = faultLightDisposeMapper.selectPageTypeInfo(page, faultLightPO);

        if (CommonUtils.isNullOrEmpty(faultLightPOS)) {
            return page.setRecords(new ArrayList<>());
        }
        return page.setRecords(ClazzConverter.converterClass(faultLightPOS, FaultLightDTO.class));
    }

    /**
     * 故障类别查询----对外暴露接口
     *
     * @param dto   FaultLightDTO
     * @return      FaultLightDTO
     */
    @Override
    public FaultLightDTO getTypeInfo(FaultLightDTO dto) {

        FaultLightPO faultLightPO = ClazzConverter.converterClass(dto, FaultLightPO.class);

        FaultLightPO faultLightPO1 = faultLightDisposeMapper.selectTypeInfo(faultLightPO);

        return ClazzConverter.converterClass(faultLightPO1, FaultLightDTO.class);
    }

    /**
     * 批量添加类别
     * @param dto       FaultLightDTO
     * @return          INT
     */
    @Override
    public int addTypeInfo(List<FaultLightDTO> dto) {

        dto.removeIf(item -> item.getId() == null);

        return faultLightDisposeMapper.addTypeInfo(ClazzConverter.converterClass(dto, FaultLightPO.class));
    }

    /**
     * 批量修改类别
     * @param batchUpdate   BatchUpdateDTO
     * @return              int
     */
    @Override
    public int batchUpdate(BatchUpdateDTO batchUpdate) {

        return faultLightDisposeMapper.batchUpdate(batchUpdate, batchUpdate.getId());
    }

    /**
     * 配置下拉选项1、2、3、4、5
     * @return List
     */
    @Override
    public List<String> cumulativeNumber() {

        String[] str = number.split(",");

        return Arrays.asList(str);
    }

    @Override
    public boolean selectDutyStatusByIds(List<Long> list) {
        int num = faultLightDisposeMapper.selectDutyStatusByIds(list);
        return num > 0;
    }

    @Override
    public void importDispose(MultipartFile file) {
        log.info("importDispose,start");
        List<FaultLightExcel> list;
        try {
            list = ExcelUtils.readMultipartFile(file, FaultLightExcel.class);
        } catch (Exception e) {
            log.info("importDispose,Exception:{}", e);
            throw new ServiceBizException(e);
        }
        log.info("importDispose,list:{}", list.size());
        List<FaultLightPO> pos = ClazzConverter.converterClass(list, FaultLightPO.class);
        Lists.partition(pos,1000).forEach(faultLightDisposeMapper::addTypeInfo);
        log.info("importDispose,end");
    }
}
