package com.yonyou.dmscus.customer.service.impl.faultLight;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.faultLight.FaultLightDisposeMapper;
import com.yonyou.dmscus.customer.dto.EmpQueryDto;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.BatchUpdateDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.infoInherit.FaultLightInheritDTO;
import com.yonyou.dmscus.customer.entity.excel.faultLight.FaultLightExcel;
import com.yonyou.dmscus.customer.entity.po.faultLight.FaultLightPO;
import com.yonyou.dmscus.customer.feign.MidEndBasicDataCenterClient;
import com.yonyou.dmscus.customer.feign.dto.AllModeDto;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightDisposeService;
import com.yonyou.dmscus.customer.service.middleground.BasicdataCenterService;
import com.yonyou.dmscus.customer.utils.ClazzConverter;
import com.yonyou.dmscus.customer.utils.excel.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
@RefreshScope
public class FaultLightDisposeServiceImpl implements FaultLightDisposeService {
    @Resource
    private BasicdataCenterService basicdataCenterService;
    @Resource
    private FaultLightDisposeMapper faultLightDisposeMapper;

    @Value("${fault.cumulative.number:1,2,3,4,5}")
    private String number;


    /**
     * 故障灯类别分页查询
     *
     * @param page          Page
     * @param dto           FaultLightDTO
     * @return IPage
     */
    @Override
    public IPage<FaultLightInheritDTO> selectPageTypeInfo(Page<FaultLightInheritDTO> page, FaultLightDTO dto) {
        log.info("selectPageTypeInfo,dto:{}", JSON.toJSONString(dto));
        FaultLightPO faultLightPO = ClazzConverter.converterClass(dto, FaultLightPO.class);

        List<FaultLightPO> faultLightPOS = faultLightDisposeMapper.selectPageTypeInfo(page, faultLightPO);

        if (CommonUtils.isNullOrEmpty(faultLightPOS)) {
            return page.setRecords(Collections.emptyList());
        }
        //获取车型ID-名称映射
        Map<String, String> modelCodeToNameMap = getModelIdToNameMap();
        //PO转DTO并处理车型名称
        List<FaultLightInheritDTO> result = getFaultLightInheritDTOS(faultLightPOS, modelCodeToNameMap);
        return page.setRecords(result);
    }

    /**
     * 获取车型ID到车型名称的映射
     */
    private Map<String, String> getModelIdToNameMap() {
        // 调用基础数据服务获取全量车型
        List<AllModeDto> allModeList = basicdataCenterService.selectAllModeList();
        Map<String, String> modelIdToNameMap = new HashMap<>(allModeList.size());
        if (CollectionUtils.isEmpty(allModeList)) {
            log.warn("getModelIdToNameMap,获取全量车型列表为空，后续车型ID无法匹配名称");
            return modelIdToNameMap;
        }
        // 遍历封装映射关系
        for (AllModeDto modeDto : allModeList) {
            // 避免modeDto.getId()为null导致的NPE
            if (StringUtils.isEmpty(modeDto.getId())) {
                log.warn("getModelIdToNameMap,存在车型ID为空的异常数据，车型信息：{}", modeDto);
                continue;
            }
            modelIdToNameMap.put(modeDto.getId(), modeDto.getModelName());
        }
        return modelIdToNameMap;
    }

    private List<FaultLightInheritDTO> getFaultLightInheritDTOS(List<FaultLightPO> faultLightPOS, Map<String, String> modelCodeToNameMap) {
        List<FaultLightInheritDTO> result = new ArrayList<>(faultLightPOS.size());
        FaultLightInheritDTO resultDTO;
        // 遍历PO列表，转换车型ID为名称
        for (FaultLightPO po : faultLightPOS) {
            if (po == null) {
                log.warn("getFaultLightInheritDTOS,车型故障灯PO列表中存在null元素，已跳过");
                continue;
            }
            resultDTO = ClazzConverter.converterClass(po, FaultLightInheritDTO.class);
            if(resultDTO == null){
                log.info("getFaultLightInheritDTOS,resultDTO is null");
                continue;
            }
            // 处理车型名称
            String vehicleModels = resolveVehicleModelNames(po.getVehicleModelIds(), modelCodeToNameMap);
            resultDTO.setVehicleModels(vehicleModels);
            result.add(resultDTO);
        }
        return result;
    }

    /**
     * 根据车型ID解析车型名称
     */
    private String resolveVehicleModelNames(String vehicleModelIds, Map<String, String> modelIdToNameMap) {
        // 场景1：ID为空或为"All"，直接返回"All"
        if (StringUtils.isEmpty(vehicleModelIds) || "All".equals(vehicleModelIds.trim())) {
            return "All";
        }
        // 场景2：映射表为空，无法匹配，返回原始ID（避免丢失数据）
        if (modelIdToNameMap.isEmpty()) {
            log.info("resolveVehicleModelNames,车型ID-名称映射表为空，返回原始车型ID：{}", vehicleModelIds);
            return vehicleModelIds;
        }
        // 场景3：正常解析ID为名称
        String[] modelIdArray = vehicleModelIds.split(",");
        // 初始化列表时指定容量，性能优化
        List<String> modelNameList = new ArrayList<>(modelIdArray.length);
        for (String modelId : modelIdArray) {
            // 去除空格
            String trimmedId = modelId.trim();
            // 匹配名称，无匹配则显示"未知车型(原始ID)"
            String modelName = modelIdToNameMap.getOrDefault(trimmedId, "未知车型(" + trimmedId + ")");
            modelNameList.add(modelName);
        }
        // 拼接名称为字符串（用逗号分隔，与ID格式一致）
        return String.join(",", modelNameList);
    }

    /**
     * 故障类别查询----对外暴露接口
     *
     * @param dto   FaultLightDTO
     * @return      FaultLightDTO
     */
    @Override
    public FaultLightDTO getTypeInfo(FaultLightDTO dto) {

        FaultLightPO faultLightPO = ClazzConverter.converterClass(dto, FaultLightPO.class);

        FaultLightPO faultLightPO1 = faultLightDisposeMapper.selectTypeInfo(faultLightPO);

        return ClazzConverter.converterClass(faultLightPO1, FaultLightDTO.class);
    }

    /**
     * 批量添加类别
     * @param dto       FaultLightDTO
     * @return          INT
     */
    @Override
    public int addTypeInfo(List<FaultLightDTO> dto) {

        dto.removeIf(item -> item.getId() == null);

        return faultLightDisposeMapper.addTypeInfo(ClazzConverter.converterClass(dto, FaultLightPO.class));
    }

    /**
     * 批量修改类别
     * @param batchUpdate   BatchUpdateDTO
     * @return              int
     */
    @Override
    public int batchUpdate(BatchUpdateDTO batchUpdate) {

        return faultLightDisposeMapper.batchUpdate(batchUpdate, batchUpdate.getId());
    }

    /**
     * 配置下拉选项1、2、3、4、5
     * @return List
     */
    @Override
    public List<String> cumulativeNumber() {

        String[] str = number.split(",");

        return Arrays.asList(str);
    }

    @Override
    public boolean selectDutyStatusByIds(List<Long> list) {
        int num = faultLightDisposeMapper.selectDutyStatusByIds(list);
        return num > 0;
    }

    @Override
    public void importDispose(MultipartFile file) {
        log.info("importDispose,start");
        List<FaultLightExcel> list;
        try {
            list = ExcelUtils.readMultipartFile(file, FaultLightExcel.class);
        } catch (Exception e) {
            log.info("importDispose,Exception:{}", e);
            throw new ServiceBizException(e);
        }
        log.info("importDispose,list:{}", list.size());
        List<FaultLightPO> pos = ClazzConverter.converterClass(list, FaultLightPO.class);
        Lists.partition(pos,1000).forEach(faultLightDisposeMapper::addTypeInfo);
        log.info("importDispose,end");
    }
}
