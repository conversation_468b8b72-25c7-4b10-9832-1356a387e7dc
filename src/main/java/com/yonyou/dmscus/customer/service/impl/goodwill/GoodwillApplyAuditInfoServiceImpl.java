package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyAuditDetailLogMapper;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyAuditDetailMapper;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyAuditInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditDetailLogPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditDetailPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyAuditInfoService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 亲善审计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-24
 */
@Service
public class GoodwillApplyAuditInfoServiceImpl extends
		ServiceImpl<GoodwillApplyAuditInfoMapper, GoodwillApplyAuditInfoPO> implements GoodwillApplyAuditInfoService {
	// 日志对象
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillApplyAuditInfoMapper goodwillApplyAuditInfoMapper;
	@Resource
	GoodwillApplyAuditDetailMapper goodwillApplyAuditDetailMapper;
	@Resource
	GoodwillApplyAuditDetailLogMapper goodwillApplyAuditDetailLogMapper;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillApplyAuditInfoDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillApplyAuditInfoDTO> selectPageBysql(Page page,
			GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO) {
		if (goodwillApplyAuditInfoDTO == null) {
			goodwillApplyAuditInfoDTO = new GoodwillApplyAuditInfoDTO();
		}
		GoodwillApplyAuditInfoPO goodwillApplyAuditInfoPO = goodwillApplyAuditInfoDTO
				.transDtoToPo(GoodwillApplyAuditInfoPO.class);

		List<GoodwillApplyAuditInfoPO> list = goodwillApplyAuditInfoMapper.selectPageBySql(page,
				goodwillApplyAuditInfoPO);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillApplyAuditInfoDTO> result = list.stream()
					.map(m -> m.transPoToDto(GoodwillApplyAuditInfoDTO.class)).collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillApplyAuditInfoDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillApplyAuditInfoDTO> selectListBySql(GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO) {
		if (goodwillApplyAuditInfoDTO == null) {
			goodwillApplyAuditInfoDTO = new GoodwillApplyAuditInfoDTO();
		}
		GoodwillApplyAuditInfoPO goodwillApplyAuditInfoPO = goodwillApplyAuditInfoDTO
				.transDtoToPo(GoodwillApplyAuditInfoPO.class);
		List<GoodwillApplyAuditInfoPO> list = goodwillApplyAuditInfoMapper.selectListBySql(goodwillApplyAuditInfoPO);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillApplyAuditInfoDTO.class)).collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditInfoDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillApplyAuditInfoDTO getById(Long id) {
		GoodwillApplyAuditInfoPO goodwillApplyAuditInfoPO = goodwillApplyAuditInfoMapper.selectById(id);
		if (goodwillApplyAuditInfoPO != null) {
			return goodwillApplyAuditInfoPO.transPoToDto(GoodwillApplyAuditInfoDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillApplyAuditInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO) {
		// 对对象进行赋值操作
		GoodwillApplyAuditInfoPO goodwillApplyAuditInfoPO = goodwillApplyAuditInfoDTO
				.transDtoToPo(GoodwillApplyAuditInfoPO.class);
		// 执行插入
		int row = goodwillApplyAuditInfoMapper.insert(goodwillApplyAuditInfoPO);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillApplyAuditInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO) {
		GoodwillApplyAuditInfoPO goodwillApplyAuditInfoPO = goodwillApplyAuditInfoMapper.selectById(id);
		// 对对象进行赋值操作
		goodwillApplyAuditInfoDTO.transDtoToPo(goodwillApplyAuditInfoPO);
		// 执行更新
		int row = goodwillApplyAuditInfoMapper.updateById(goodwillApplyAuditInfoPO);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillApplyAuditInfoMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillApplyAuditInfoMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 保存审计信息
	 *
	 * @param GoodwillApplyAuditInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-06
	 */
	@Override
	public int auditApplyGoodwillInfo(GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDto) {
		int row = 0;
		if (goodwillApplyAuditInfoDto.getId() == null) {
			// 对对象进行赋值操作
			GoodwillApplyAuditInfoPO goodwillApplyAuditInfoPO = goodwillApplyAuditInfoDto
					.transDtoToPo(GoodwillApplyAuditInfoPO.class);
			goodwillApplyAuditInfoPO.setIsAudit(CommonConstants.DICT_IS_YES);
			goodwillApplyAuditInfoPO.setAuditTime(new Date());
			// 执行插入
			row = goodwillApplyAuditInfoMapper.insert(goodwillApplyAuditInfoPO);
			GoodwillApplyAuditInfoPO po = goodwillApplyAuditInfoMapper
					.selectByGoodwillId(goodwillApplyAuditInfoPO.getGoodwillApplyId());
			goodwillApplyAuditInfoDto.setId(po.getId());
		} else {
			GoodwillApplyAuditInfoPO goodwillApplyAuditInfoPO = goodwillApplyAuditInfoMapper
					.selectById(goodwillApplyAuditInfoDto.getId());
			// 对对象进行赋值操作
			goodwillApplyAuditInfoDto.transDtoToPo(goodwillApplyAuditInfoPO);
			// 执行更新
			row = goodwillApplyAuditInfoMapper.updateById(goodwillApplyAuditInfoPO);

		}

		// 插入明细
		if (goodwillApplyAuditInfoDto.getGoodwillApplyAuditDetailDto() != null) {
			Map<String, Object> columnMap = new HashMap(16);
			columnMap.put("audit_id", goodwillApplyAuditInfoDto.getId());
			goodwillApplyAuditDetailMapper.deleteByMap(columnMap);
			for (GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDto : goodwillApplyAuditInfoDto
					.getGoodwillApplyAuditDetailDto()) {
				// 对对象进行赋值操作
				GoodwillApplyAuditDetailPO goodwillApplyAuditDetailPO = goodwillApplyAuditDetailDto
						.transDtoToPo(GoodwillApplyAuditDetailPO.class);
				goodwillApplyAuditDetailPO.setAuditId(goodwillApplyAuditInfoDto.getId());
				// 执行插入
				goodwillApplyAuditDetailMapper.insert(goodwillApplyAuditDetailPO);

				// 日志表插入数据
				// 对对象进行赋值操作
				GoodwillApplyAuditDetailLogPO goodwillApplyAuditDetailLogPo = goodwillApplyAuditDetailDto
						.transDtoToPo(GoodwillApplyAuditDetailLogPO.class);
				goodwillApplyAuditDetailLogPo.setAuditId(goodwillApplyAuditInfoDto.getId());
				// 执行插入
				goodwillApplyAuditDetailLogMapper.insert(goodwillApplyAuditDetailLogPo);

			}
		}

		return row;
	}

	/**
	 * 根据主单ID查询明细
	 *
	 * @param id
	 *            数据主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditDetailDTO
	 * <AUTHOR>
	 * @since 2020-06-24
	 */
	@Override
	public List<GoodwillApplyAuditDetailDTO> queryAuditDetailInfo(Long auditId) {
		List<GoodwillApplyAuditDetailDTO> list = goodwillApplyAuditDetailMapper.queryAuditDetailInfo(auditId);
		if (!CommonUtils.isNullOrEmpty(list)) {
			return list;
		} else {
			return new ArrayList<>();
		}

	}
}
