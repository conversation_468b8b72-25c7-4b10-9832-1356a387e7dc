package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillInvoiceRecordMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillInvoiceRecordDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillInvoiceRecordPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillInvoiceRecordService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 亲善管理录入发票信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-19
 */
@Service
public class GoodwillInvoiceRecordServiceImpl extends ServiceImpl<GoodwillInvoiceRecordMapper, GoodwillInvoiceRecordPO>
		implements GoodwillInvoiceRecordService {
	// 日志对象
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillInvoiceRecordMapper goodwillInvoiceRecordMapper;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillInvoiceRecordDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillInvoiceRecordDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillInvoiceRecordDTO> selectPageBysql(Page page,
			GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO) {
		if (goodwillInvoiceRecordDTO == null) {
			goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
		}
		GoodwillInvoiceRecordPO goodwillInvoiceRecordPO = goodwillInvoiceRecordDTO
				.transDtoToPo(GoodwillInvoiceRecordPO.class);

		List<GoodwillInvoiceRecordPO> list = goodwillInvoiceRecordMapper.selectPageBySql(page, goodwillInvoiceRecordPO);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillInvoiceRecordDTO> result = list.stream()
					.map(m -> m.transPoToDto(GoodwillInvoiceRecordDTO.class)).collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillInvoiceRecordDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillInvoiceRecordDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillInvoiceRecordDTO> selectListBySql(GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO) {
		if (goodwillInvoiceRecordDTO == null) {
			goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
		}
		GoodwillInvoiceRecordPO goodwillInvoiceRecordPO = goodwillInvoiceRecordDTO
				.transDtoToPo(GoodwillInvoiceRecordPO.class);
		List<GoodwillInvoiceRecordPO> list = goodwillInvoiceRecordMapper.selectListBySql(goodwillInvoiceRecordPO);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillInvoiceRecordDTO.class)).collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillInvoiceRecordDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillInvoiceRecordDTO getById(Long id) {
		GoodwillInvoiceRecordPO goodwillInvoiceRecordPO = goodwillInvoiceRecordMapper.selectById(id);
		if (goodwillInvoiceRecordPO != null) {
			return goodwillInvoiceRecordPO.transPoToDto(GoodwillInvoiceRecordDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillInvoiceRecordDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO) {
		// 对对象进行赋值操作
		GoodwillInvoiceRecordPO goodwillInvoiceRecordPO = goodwillInvoiceRecordDTO
				.transDtoToPo(GoodwillInvoiceRecordPO.class);
		// 执行插入
		int row = goodwillInvoiceRecordMapper.insert(goodwillInvoiceRecordPO);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillInvoiceRecordDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO) {
		GoodwillInvoiceRecordPO goodwillInvoiceRecordPO = goodwillInvoiceRecordMapper.selectById(id);
		// 对对象进行赋值操作
		goodwillInvoiceRecordDTO.transDtoToPo(goodwillInvoiceRecordPO);
		// 执行更新
		int row = goodwillInvoiceRecordMapper.updateById(goodwillInvoiceRecordPO);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillInvoiceRecordMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillInvoiceRecordMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}
}
