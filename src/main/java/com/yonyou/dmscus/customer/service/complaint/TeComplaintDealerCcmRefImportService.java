package com.yonyou.dmscus.customer.service.complaint;

    import com.baomidou.mybatisplus.core.metadata.IPage;
    import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.TeComplaintDealerCcmRefImportDTO;

    import java.util.List;


/**
 * <p>
 * 客户投诉进销商和CCM复杂人对应关系导入表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public interface TeComplaintDealerCcmRefImportService  {
    /**
     * 分页查询
     * @param page
     * @param teComplaintDealerCcmRefImportDTO
     * @return
     */
       IPage<TeComplaintDealerCcmRefImportDTO> selectPageBysql(Page page, TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO);

    /**
     * 集合查询
     * @param teComplaintDealerCcmRefImportDTO
     * @return
     */
       List<TeComplaintDealerCcmRefImportDTO> selectListBySql(TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO);

    /**
     * 通过id查询
     * @param id
     * @return
     */
    TeComplaintDealerCcmRefImportDTO getById(Long id);

    /**
     * 新增
     * @param teComplaintDealerCcmRefImportDTO
     * @return
     */
    int insert(TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO);

    /**
     *
     * 更新
     * @param id
     * @param teComplaintDealerCcmRefImportDTO
     * @return
     */
    int update(Long id, TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO);

    /**
     *
     * 通过id删除
     * @param id
     * @return
     */
    int deleteById(Long id);


}
