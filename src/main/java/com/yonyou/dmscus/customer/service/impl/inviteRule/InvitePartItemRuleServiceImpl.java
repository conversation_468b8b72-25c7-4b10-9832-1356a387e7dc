package com.yonyou.dmscus.customer.service.impl.inviteRule;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.inviteRule.InvitePartItemRuleChangedRecordMapper;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InvitePartItemRuleChangedRecordPO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InvitePartItemRulePO;
import com.yonyou.dmscus.customer.dao.inviteRule.InvitePartItemRuleMapper;
import com.yonyou.dmscus.customer.service.inviteRule.InvitePartItemRuleService;
import com.yonyou.dmscus.customer.entity.dto.inviteRule.InvitePartItemRuleDTO;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 邀约易损件和项目规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
@Service
public class InvitePartItemRuleServiceImpl implements InvitePartItemRuleService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InvitePartItemRuleMapper invitePartItemRuleMapper;
    @Resource
    InvitePartItemRuleChangedRecordMapper invitePartItemRuleChangedRecordMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                  分页对象
     * @param invitePartItemRuleDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto
                     *   . InvitePartItemRuleDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<InvitePartItemRuleDTO> selectPageBysql(Page page, InvitePartItemRuleDTO invitePartItemRuleDTO) {
        if (invitePartItemRuleDTO == null) {
            invitePartItemRuleDTO = new InvitePartItemRuleDTO();
        }
        InvitePartItemRulePO invitePartItemRulePO = invitePartItemRuleDTO.transDtoToPo(InvitePartItemRulePO.class);

        List<InvitePartItemRulePO> list = invitePartItemRuleMapper.selectPageBySql(page, invitePartItemRulePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InvitePartItemRuleDTO> result = list.stream().map(m -> m.transPoToDto(InvitePartItemRuleDTO.class))
                    .collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param invitePartItemRuleDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.inviteRule.InvitePartItemRuleDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<InvitePartItemRuleDTO> selectListBySql(InvitePartItemRuleDTO invitePartItemRuleDTO) {
        if (invitePartItemRuleDTO == null) {
            invitePartItemRuleDTO = new InvitePartItemRuleDTO();
        }
        InvitePartItemRulePO invitePartItemRulePO = invitePartItemRuleDTO.transDtoToPo(InvitePartItemRulePO.class);
        List<InvitePartItemRulePO> list = invitePartItemRuleMapper.selectListBySql(invitePartItemRulePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InvitePartItemRuleDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.inviteRule.InvitePartItemRuleDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public InvitePartItemRuleDTO getById(Long id) {
        InvitePartItemRulePO invitePartItemRulePO = invitePartItemRuleMapper.selectById(id);
        if (invitePartItemRulePO != null) {
            return invitePartItemRulePO.transPoToDto(InvitePartItemRuleDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param invitePartItemRuleDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(InvitePartItemRuleDTO invitePartItemRuleDTO) {
        //对对象进行赋值操作
        InvitePartItemRulePO invitePartItemRulePO = invitePartItemRuleDTO.transDtoToPo(InvitePartItemRulePO.class);
        //执行插入
        int row = invitePartItemRuleMapper.insert(invitePartItemRulePO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                    主键ID
     * @param invitePartItemRuleDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, InvitePartItemRuleDTO invitePartItemRuleDTO) {
        InvitePartItemRulePO invitePartItemRulePO = invitePartItemRuleMapper.selectById(id);
        //对对象进行赋值操作
        invitePartItemRuleDTO.transDtoToPo(invitePartItemRulePO);
        //执行更新
        int row = invitePartItemRuleMapper.updateById(invitePartItemRulePO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = invitePartItemRuleMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = invitePartItemRuleMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }


    /**
     * 保存规则
     * @param dto
     * @return
     */
    @Override
    public int savePartItemRule(InvitePartItemRuleDTO dto) {
            InvitePartItemRulePO po = dto.transDtoToPo(InvitePartItemRulePO.class);
            if(!checkRepeat(po)){
                throw new DALException("零件编号或项目编号重复,无法保存!");
            }
        if (po.getId()==null) {
                invitePartItemRuleMapper.insert(po);
            }else{
            InvitePartItemRulePO  rs =invitePartItemRuleMapper.selectById(dto.getId());
            this.saveInviteRuleChangedLog(rs,dto);
            invitePartItemRuleMapper.updateById(po);
            }
        return 1;
    }

    /**
     * 比较变更数据，保存变更记录
     * @param inviteRulePO
     * @param dto
     */
    private void saveInviteRuleChangedLog(InvitePartItemRulePO inviteRulePO, InvitePartItemRuleDTO dto) {
        InvitePartItemRuleChangedRecordPO insertPo = new InvitePartItemRuleChangedRecordPO();
        insertPo.setDealerCode(inviteRulePO.getDealerCode());
        insertPo.setType(inviteRulePO.getType());
        insertPo.setCode(inviteRulePO.getCode());
        insertPo.setName(inviteRulePO.getName());
        insertPo.setLastRemindInterval(inviteRulePO.getRemindInterval());
        insertPo.setLastMileageInterval(inviteRulePO.getMileageInterval());
        insertPo.setLastDateInterval(inviteRulePO.getDateInterval());
        insertPo.setLastVin(inviteRulePO.getVin());
        insertPo.setLastModelCode(inviteRulePO.getModelCode());
        insertPo.setLastModelYear(inviteRulePO.getModelYear());
        insertPo.setLastEngineCode(inviteRulePO.getEngineCode());
        insertPo.setLastGearboxCode(inviteRulePO.getGearboxCode());
        insertPo.setLastRuleRelationship(inviteRulePO.getRuleRelationship());
        insertPo.setLastIsUse(inviteRulePO.getIsUse());
        insertPo.setRemindInterval(dto.getRemindInterval());
        insertPo.setMileageInterval(dto.getMileageInterval());
        insertPo.setDateInterval(dto.getDateInterval());
        insertPo.setVin(dto.getVin());
        insertPo.setModelCode(dto.getModelCode());
        insertPo.setModelYear(dto.getModelYear());
        insertPo.setEngineCode(dto.getEngineCode());
        insertPo.setGearboxCode(dto.getGearboxCode());
        insertPo.setRuleRelationship(dto.getRuleRelationship());
        insertPo.setIsUse(dto.getIsUse());
        insertPo.setUpdateIsExecute(this.checkHasChanged(insertPo));
        invitePartItemRuleChangedRecordMapper.insert(insertPo);
    }

    /**
     * 检查是否影响邀约任务和线索有改变
     * @param insertPo
     * @return
     */
    private Boolean checkHasChanged(InvitePartItemRuleChangedRecordPO insertPo) {
        if((insertPo.getLastMileageInterval()!=null&&insertPo.getMileageInterval()!=null
                &&insertPo.getLastMileageInterval().equals(insertPo.getMileageInterval()))
                ||(insertPo.getLastMileageInterval()==null&&insertPo.getMileageInterval()==null)){
        }else{
            invitePartItemRuleChangedRecordMapper.updateIsExecute(insertPo.getType(),insertPo.getCode());
            return false;
        }
        if((insertPo.getLastDateInterval()!=null&&insertPo.getDateInterval()!=null
                &&insertPo.getLastDateInterval().equals(insertPo.getDateInterval()))
                ||(insertPo.getLastDateInterval()==null&&insertPo.getDateInterval()==null)){
        }else{
            invitePartItemRuleChangedRecordMapper.updateIsExecute(insertPo.getType(),insertPo.getCode());
            return false;
        }
        insertPo.setRemark("影响数据未改变");
        return true;
    }

    /**
     * 检查是否重复
     * @param po
     * @return
     */
    private Boolean checkRepeat(InvitePartItemRulePO po){
        Integer rs = invitePartItemRuleMapper.checkRepeat(po.getId(),po.getCode(),po.getType());
        if(rs!=null){
            return  false;
        }
        return  true;
    }
}
