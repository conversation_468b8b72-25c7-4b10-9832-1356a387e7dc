package com.yonyou.dmscus.customer.service.accidentClues;

import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.clue.AccidentClueAppPushEnum;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesPO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/11/1 10:59
 * @Version 1.0
 */
public interface AppPushService {

    /**
     * 分配提醒
     * @param appPushMap
     * @param companyId
     * @param pushEnum
     * @throws ServiceBizException
     */
    void clueAllotAppPush(Map<Long, List<AccidentCluesPO>> appPushMap, Long companyId, AccidentClueAppPushEnum pushEnum) throws ServiceBizException;

    /**
     * 跟进提醒
     * @param pushList
     * @param companyId
     * @param pushEnum
     * @throws ServiceBizException
     */
    void followRemindAppPush(List<AccidentCluesPO> pushList, Long companyId, AccidentClueAppPushEnum pushEnum) throws ServiceBizException;

}
