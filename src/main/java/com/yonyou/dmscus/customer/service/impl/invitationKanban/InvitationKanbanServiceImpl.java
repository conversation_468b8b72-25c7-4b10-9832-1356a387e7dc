package com.yonyou.dmscus.customer.service.impl.invitationKanban;




import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.utils.common.StringUtils;
import com.yonyou.dmscus.customer.dao.invitationKanban.InvitationKanbanMapper;
import com.yonyou.dmscus.customer.entity.dto.invitationKanban.InvitationKanbanInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationKanban.InvitationKanbanQueryDTO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.invitationKanban.InvitationKanbanService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;


/**
 * <p>
 * 车辆邀约记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
@Service
public class InvitationKanbanServiceImpl implements InvitationKanbanService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InvitationKanbanMapper invitationKanbanMapper;

    @Autowired
    BusinessPlatformService businessPlatformService;


    @Override
    public List<InvitationKanbanInfoDTO> InvitationKanbanService(InvitationKanbanQueryDTO query) {
        //条件存在经销商
        if(!StringUtils.isNullOrEmpty(query.getDealerCode())){
            return invitationKanbanMapper.getInvitationKanbanInfoByDealerCode(query);
        }
        else if(!StringUtils.isNullOrEmpty(query.getAreaId())){
            List<String> codes = businessPlatformService.getDealercodes(query.getAreaId(),null,null,null);
            if(codes.size()==0){
                return new ArrayList<InvitationKanbanInfoDTO>();
            }else{
                query.setDealers(codes);
                return invitationKanbanMapper.getInvitationKanbanInfoByDealers(query);
            }

        }else if(!StringUtils.isNullOrEmpty(query.getLargeAreaId())){
            List<String> codes = businessPlatformService.getDealercodes(null,query.getLargeAreaId(),null,null);
            if(codes.size()==0){
                return new ArrayList<InvitationKanbanInfoDTO>();
            }else{
                query.setDealers(codes);
                return invitationKanbanMapper.getInvitationKanbanInfoByDealers(query);
            }
        }else{
            return invitationKanbanMapper.getInvitationKanbanInfo(query);
        }
    }

    /**
     * 经销商查询邀约看板
     * @param query
     * @return
     */
    @Override
    public List<InvitationKanbanInfoDTO> InvitationKanbanDlrService(InvitationKanbanQueryDTO query) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        query.setDealerCode(loginInfoDto.getOwnerCode());
        return invitationKanbanMapper.getInvitationKanbanInfoByDealerCode(query);
    }

}
