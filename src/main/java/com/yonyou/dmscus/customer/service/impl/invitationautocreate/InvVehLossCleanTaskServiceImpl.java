package com.yonyou.dmscus.customer.service.impl.invitationautocreate;


import com.google.common.collect.Lists;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dao.invitationautocreate.InviteVehicleTaskMapper;
import com.yonyou.dmscus.customer.dao.inviteRule.InviteRuleMapper;
import com.yonyou.dmscus.customer.dao.voc.VocInviteVehicleTaskRecordMapper;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.InvVehLossCleanTaskPO;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRulePO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocInviteVehicleTaskRecordPo;
import com.yonyou.dmscus.customer.service.invitationautocreate.InvVehLossCleanTaskService;
import com.yonyou.dmscus.customer.service.invitationautocreate.InviteVehicleTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 流失客户任务清洗
 */
@Service
@Slf4j
@RefreshScope
public class InvVehLossCleanTaskServiceImpl implements InvVehLossCleanTaskService {

    @Resource
    InviteVehicleRecordMapper inviteVehicleRecordMapper;
    @Resource
    private InviteVehicleTaskMapper inviteVehicleTaskMapper;
    @Resource
    private InviteRuleMapper inviteRuleMapper;
    @Resource
    private VocInviteVehicleTaskRecordMapper vocInviteVehicleTaskRecordMapper;

    @Autowired
    private InviteVehicleTaskService inviteVehicleTaskService;

    @Override
    public void doCleanLossTaskByDis() {
        log.info("doCleanLossTaskByDis,start");
        long startTime = new Date().getTime();
        /**1.分批查询所有未完成定保任务*/
        Integer startNum;
        Integer endNum;
        int size = 10000;
        int page = 1;
        long startDate;
        long endDate;
        /**未完成定保任务集合*/
        List<InvVehLossCleanTaskPO> listPo;
        while (true) {
            startNum = (page - 1) * size;
            endNum = size;
            log.info("doCleanLossTaskByDis,page:{}, startNum:{}, endNum:{}", page, startNum, endNum);
            startDate = new Date().getTime();
            log.info("doCleanLossTaskByDis,page:{}, startDate:{}", page, startDate);
            //分批查询所有未完成定保任务
            listPo = inviteVehicleTaskMapper.selectFixedInsuranceTask(startNum, endNum);
            if (CollectionUtils.isEmpty(listPo)) {
                log.info("doCleanLossTaskByDis,CollectionUtils.isEmpty(listPo),page:{}", page);
                break;
            }
            doLossData(listPo, page);
            endDate = new Date().getTime();
            log.info("doCleanLossTaskByDis,page:{}, endDate:{}, endTime:{}, takeUpTime:{}", page, endDate, endDate - startDate);
            page += 1;
        }
        long endTime = new Date().getTime();
        log.info("doCleanLossTaskByDis,end, startTime:{}, endTime:{}, takeUpTime:{}", startTime, endTime, endTime - startTime);
    }

    @Override
    public void doCleanLossTaskByUnf() {
        log.info("doCleanLossTaskByUnf,start");
        doCleanLossTaskByAll(CommonConstants.ORDER_STATUS_II);
        log.info("doCleanLossTaskByUnf,end");
    }

    @Override
    public void doCleanLossTaskBySli() {
        log.info("doCleanLossTaskBySli,start");
        doCleanLossTaskByAll(CommonConstants.ORDER_STATUS_III);
        log.info("doCleanLossTaskBySli,end");
    }

    @Override
    public void addInviteVehicleTaskRecord(String startDate, String endDate) {
        log.info("addInviteVehicleTaskRecord,start,startDate:{}, endDate:{}", startDate, endDate);
        //查询流失任务没有生成对应扩展表的数据
        long startTime = new Date().getTime();
        log.info("addInviteVehicleTaskRecord,startTime:{}", startTime);
        List<InvVehLossCleanTaskPO> listPo = inviteVehicleTaskMapper.selectDeletionLossTask(startDate, endDate);
        long endTime = new Date().getTime();
        log.info("addInviteVehicleTaskRecord,endTime:{}, takeUpTime:{}", endTime, endTime - startTime);
        if(CollectionUtils.isEmpty(listPo)){
            log.info("addInviteVehicleTaskRecord,CollectionUtils.isEmpty(listPo)");
            return;
        }
        log.info("addInviteVehicleTaskRecord,listPo:{}", listPo.size());
        List<VocInviteVehicleTaskRecordPo> list = new ArrayList<>();
        VocInviteVehicleTaskRecordPo vocPo;
        for (InvVehLossCleanTaskPO po : listPo) {
            //扩展表
            vocPo = new VocInviteVehicleTaskRecordPo();
            vocPo.setTaskId(po.getId());
            vocPo.setRecordType(1);
            vocPo.setLossType(CommonConstants.LOSS_TYPE_1);
            vocPo.setVin(po.getVin());
            vocPo.setInviteType(CommonConstants.INVITE_TYPE_VI);
            vocPo.setCreatedBy("-9");
            list.add(vocPo);
        }
        if(CollectionUtils.isNotEmpty(list)){
            log.info("addInviteVehicleTaskRecord,list:{}",list.size());
            Lists.partition(list,500).forEach(vocInviteVehicleTaskRecordMapper::insertList);
        }
        log.info("addInviteVehicleTaskRecord,end");
    }

    private void doCleanLossTaskByAll(int orderStatus) {
        //思路:按经销商拆分查询
        log.info("doCleanLossTaskByAll,start, orderStatus:{}", orderStatus);
        //查询线索的所有经销商
        List<String> list = inviteVehicleRecordMapper.selectDealerCodeGroupBy();
        if (CollectionUtils.isEmpty(list)) {
            log.info("doCleanLossTaskByAll, CollectionUtils.isEmpty(list),end");
            return;
        }
        log.info("doCleanLossTaskByAll,list:{}", list.size());
        /**最新的定保是未完成的线索对应的任务集合*/
        List<InvVehLossCleanTaskPO> listPo;
        int page = 1;
        for (String dealerCode : list) {
            log.info("doCleanLossTaskByAll,page:{}, dealerCode:{}", page, dealerCode);
            //按经销商查询未完成/逾期的定保线索
            listPo = inviteVehicleTaskMapper.selectIncompleteTask(dealerCode, orderStatus);
            if (CollectionUtils.isEmpty(listPo)) {
                log.info("doCleanLossTaskByAll, CollectionUtils.isEmpty(listPo), dealerCode:{}", dealerCode);
                continue;
            }
            doLossData(listPo, page);
            page += 1;
        }
        log.info("doCleanLossTaskByAll,end");
    }

    private void doLossData(List<InvVehLossCleanTaskPO> listPo, int page) {
        log.info("doLossData,start,page:{}", page);
        List<String> listVin = listPo.stream().map(InvVehLossCleanTaskPO::getVin).distinct().collect(Collectors.toList());
        //查询对应的流失任务
        List<InvVehLossCleanTaskPO> listLoss = inviteVehicleTaskMapper.selectLossTask(listVin);
        //分组
        Map<String, List<InvVehLossCleanTaskPO>> mapLoss = listLoss.stream().collect(Collectors.groupingBy(InvVehLossCleanTaskPO::getVin));
        //判断
        /**批量删除集合*/
        List<Long> delList = new ArrayList<>();
        /**批量新增集合*/
        List<InviteVehicleTaskPO> addList = new ArrayList<>();
        /**批量修改集合*/
        List<InviteVehicleTaskPO> upList = new ArrayList<>();

        /**车架号*/
        String vin;
        /**经销商*/
        String dealerCode;
        /**流失任务集合*/
        List<InvVehLossCleanTaskPO> loss;
        List<InvVehLossCleanTaskPO> lossDealer = null;
        Map<String, List<InvVehLossCleanTaskPO>> mapDealer;
        for (InvVehLossCleanTaskPO a : listPo) {
            /**流失任务标识*/
            int count = 0;
            vin = a.getVin();
            dealerCode = a.getDealerCode();
            log.info("doLossData, vin:{}, dealerCode:{}", vin, dealerCode);
            loss = mapLoss.get(vin);
            if (CollectionUtils.isNotEmpty(loss)) {
                mapDealer = loss.stream().collect(Collectors.groupingBy(InvVehLossCleanTaskPO::getDealerCode));
                lossDealer = mapDealer.get(dealerCode);
                count = lossDealer == null ? 0 : lossDealer.size();
            }
            log.info("doLossData, vin:{}, dealerCode:{}, count:{}", vin, dealerCode, count);
            if (count == 0) {
                //封装数据-新增
                addList.add(getAddPo(a));
            } else if (count == 1) {
                //封装数据-修改
                upList.add(getUpPo(a, lossDealer.get(0).getId()));
            } else if (count > 1) {
                //封装数据-系统删除
                getDelList(delList, lossDealer);
                //封装数据-新增
                addList.add(getAddPo(a));
            }
        }
        //批量执行-走事务
        log.info("doLossData, page:{}, addList:{}, delList:{}, upList:{}", page, addList.size(), delList.size(), upList.size());
        inviteVehicleTaskService.doRepairLossTask(page, addList, delList, upList);
        log.info("doLossData,end, page:{}", page);
    }

    //增
    private InviteVehicleTaskPO getAddPo(InvVehLossCleanTaskPO po) {
        String dealerCode = po.getDealerCode();
        String vin = po.getVin();
        Date inviteTime = po.getInviteTime();
        log.info("doLossData,getAddPo vin:{}, dealerCode:{}", vin, dealerCode);
        //查询客户流失间隔规则
        InviteRulePO rule = inviteRuleMapper.getInvitationDlrRule(dealerCode, CommonConstants.INVITE_TYPE_CUS_LOSS, CommonConstants.INVITE_RULE_V);
        Date adviseInDate = getMonthTime(inviteTime, rule.getRuleValue());
        Date createInviteTime = getInviteTime(adviseInDate);
        InviteVehicleTaskPO taskPo = new InviteVehicleTaskPO();
        taskPo.setVin(po.getVin());
        taskPo.setLicensePlateNum(po.getLicensePlateNum());
        taskPo.setDealerCode(dealerCode);
        taskPo.setName(po.getName());
        taskPo.setTel(po.getTel());
        taskPo.setAge(po.getAge());
        taskPo.setSex(po.getSex());
        taskPo.setModel(po.getModel());
        taskPo.setDailyMileage(po.getDailyMileage());
        taskPo.setAdviseInDate(adviseInDate);
        taskPo.setInviteType(CommonConstants.INVITE_TYPE_VI);
        taskPo.setRemindInterval(po.getRemindInterval());
        //下发时间
        taskPo.setCreateInviteTime(createInviteTime);
        taskPo.setInviteTime(inviteTime);
        taskPo.setCloseInterval(po.getCloseInterval());
        taskPo.setAdviseInMileage(po.getAdviseInMileage());
        taskPo.setOutMileage(po.getOutMileage());
        log.info("doLossData,getAddPo vin:{}, dealerCode:{}, adviseInDate:{}, inviteTime:{}", vin, dealerCode, adviseInDate, inviteTime);
        return taskPo;
    }

    //删
    private void getDelList(List<Long> delList, List<InvVehLossCleanTaskPO> loss) {
        for (InvVehLossCleanTaskPO po : loss) {
            log.info("doLossData,getDelPo vin:{}, dealerCode:{}", po.getVin(), po.getDealerCode());
            delList.add(po.getId());
        }
    }

    //改
    private InviteVehicleTaskPO getUpPo(InvVehLossCleanTaskPO po, long id) {
        String dealerCode = po.getDealerCode();
        String vin = po.getVin();
        Date inviteTime = po.getInviteTime();
        log.info("doLossData,getUpPo vin:{}, dealerCode:{}, id:{}", vin, dealerCode, id);
        //查询客户流失间隔规则
        InviteRulePO rule = inviteRuleMapper.getInvitationDlrRule(dealerCode, CommonConstants.INVITE_TYPE_CUS_LOSS, CommonConstants.INVITE_RULE_V);
        Date adviseInDate = getMonthTime(inviteTime, rule.getRuleValue());
        Date createInviteTime = getInviteTime(adviseInDate);
        InviteVehicleTaskPO taskPo = new InviteVehicleTaskPO();
        taskPo.setId(id);
        taskPo.setAdviseInDate(adviseInDate);
        taskPo.setCreateInviteTime(createInviteTime);
        taskPo.setInviteTime(inviteTime);
        log.info("doLossData,getUpPo vin:{}, dealerCode:{}, adviseInDate:{}, inviteTime:{}", vin, dealerCode, adviseInDate, inviteTime);
        return taskPo;
    }

    /**
     * 计算建议进场日期
     */
    private Date getMonthTime(Date time, int amount) {
        if (time == null) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(time);
        c.add(Calendar.MONTH, amount);
        return c.getTime();
    }

    /**
     * 计算下发时间
     */
    private Date getInviteTime(Date time) {
        if (time == null) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(time);
        //下发时间 = 建议进场时间 - amount个月
        //c.add(Calendar.MONTH, amount);
        //下发时间取当月1号
        c.set(Calendar.DAY_OF_MONTH, 1);
        return c.getTime();
    }
}















