package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

    
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;


import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintCustomFieldUseMapper;
import com.yonyou.dmscus.customer.service.complaint.ComplaintCustomFieldUseService;

import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldUseDTO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.List;
import java.util.ArrayList;

import java.util.stream.Collectors;
import javax.annotation.Resource;




    /**
 * <p>
 * 客户投诉自定义字段使用表 每个人不同对应不同 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
@Service
        public class ComplaintCustomFieldUseServiceImpl implements ComplaintCustomFieldUseService {
        /**
         * 日志对象
         */
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        ComplaintCustomFieldUseMapper complaintCustomFieldUseMapper;

        /**
         * 分页查询对应数据
         *
         * @param page 分页对象
         * @param complaintCustomFieldUseDTO 查询条件
         * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldUseDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public IPage<ComplaintCustomFieldUseDTO>selectPageBysql(Page page,ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO){
            if(complaintCustomFieldUseDTO ==null){
                complaintCustomFieldUseDTO =new ComplaintCustomFieldUseDTO();
            }
            ComplaintCustomFieldUsePO complaintCustomFieldUsePo =complaintCustomFieldUseDTO.transDtoToPo(ComplaintCustomFieldUsePO.class);

            List<ComplaintCustomFieldUsePO>list= complaintCustomFieldUseMapper.selectPageBySql(page,complaintCustomFieldUsePo);
            if(CommonUtils.isNullOrEmpty(list)){
                page.setRecords(new ArrayList<>());
                return page;
            }else{
                List<ComplaintCustomFieldUseDTO>result=list.stream().map(m->m.transPoToDto(ComplaintCustomFieldUseDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param complaintCustomFieldUseDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldUseDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<ComplaintCustomFieldUseDTO>selectListBySql(ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO){
            if(complaintCustomFieldUseDTO ==null){
                complaintCustomFieldUseDTO =new ComplaintCustomFieldUseDTO();
            }
            ComplaintCustomFieldUsePO complaintCustomFieldUsePo =complaintCustomFieldUseDTO.transDtoToPo(ComplaintCustomFieldUsePO.class);
            List<ComplaintCustomFieldUsePO>list= complaintCustomFieldUseMapper.selectListBySql(complaintCustomFieldUsePo);
            if(CommonUtils.isNullOrEmpty(list)){
                return new ArrayList<>();
            }else{
                return list.stream().map(m->m.transPoToDto(ComplaintCustomFieldUseDTO.class)).collect(Collectors.toList());
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldUseDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public ComplaintCustomFieldUseDTO getById(Long id){
            ComplaintCustomFieldUsePO complaintCustomFieldUsePo = complaintCustomFieldUseMapper.selectById(id);
            if(complaintCustomFieldUsePo!=null){
                return complaintCustomFieldUsePo.transPoToDto(ComplaintCustomFieldUseDTO.class);
            }else{
                throw new DALException("查询失败,没有符合条件的数据");
            }
        }

        /**
         * 根据DTO 进行数据新增
         *
         * @param complaintCustomFieldUseDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO){
            //对对象进行赋值操作
            ComplaintCustomFieldUsePO complaintCustomFieldUsePo = complaintCustomFieldUseDTO.transDtoToPo(ComplaintCustomFieldUsePO.class);
            //执行插入
            int row= complaintCustomFieldUseMapper.insert(complaintCustomFieldUsePo);
            //返回插入的值
            return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param complaintCustomFieldUseDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO){
            ComplaintCustomFieldUsePO complaintCustomFieldUsePo = complaintCustomFieldUseMapper.selectById(id);
            //对对象进行赋值操作
            complaintCustomFieldUseDTO.transDtoToPo(complaintCustomFieldUsePo);
            //执行更新
            int row= complaintCustomFieldUseMapper.updateById(complaintCustomFieldUsePo);
            return row;
        }

        /**
         * 根据ID 进行删除数据
         *
         * @param id
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteById(Long id){
            int deleteCount= complaintCustomFieldUseMapper.deleteById(id);
            logger.debug("删除数量：{}",deleteCount);
            if(deleteCount<=0){
                throw new DALException("删除失败,没有符合条件的数据");
            }else{
                return deleteCount;
            }
        }

        /**
         * 新增自定义查询条件
         * @param complaintCustomFieldTestDTO
         * @return
         */
        @Override
        public int insertFied(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO) {
            Long userId=FrameworkUtil.getLoginInfo().getUserId();
            ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO=new ComplaintCustomFieldUseDTO();
            //CCM跟进状态是否填写
            List<ComplaintCustomFieldUsePO> followStatus1=complaintCustomFieldUseMapper.queryfollowStatus(userId);
            complaintCustomFieldUseDTO=new ComplaintCustomFieldUseDTO();
            complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
            if(complaintCustomFieldTestDTO.getFollowStatus1()==true){
                complaintCustomFieldUseDTO.setFieldName("follow_status");
                    complaintCustomFieldUseDTO.setIsQuery(true);
                    if (followStatus1.size()!=0){
                        long followStatusId=followStatus1.get(0).getId();
                        update(followStatusId,complaintCustomFieldUseDTO);
                    }else {
                        insert(complaintCustomFieldUseDTO);
                    }

            }else {
                if (followStatus1.size()!=0) {
                    long followStatusId=followStatus1.get(0).getId();
                    complaintCustomFieldUseDTO.setIsQuery(false);
                    update(followStatusId, complaintCustomFieldUseDTO);
                }
            }


            //CCM主要原因是否填写
            List<ComplaintCustomFieldUsePO> ccmMainReasonList1=complaintCustomFieldUseMapper.queryCcMainReason(userId);
            complaintCustomFieldUseDTO=new ComplaintCustomFieldUseDTO();
            complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
            if(complaintCustomFieldTestDTO.getCcMainReason1()==true){
                complaintCustomFieldUseDTO.setFieldName("cc_main_reason");
                    complaintCustomFieldUseDTO.setIsQuery(true);
                    if (ccmMainReasonList1.size()!=0){
                        long cmMainReasonId=ccmMainReasonList1.get(0).getId();
                        update(cmMainReasonId,complaintCustomFieldUseDTO);
                    }else {
                        insert(complaintCustomFieldUseDTO);
                    }
            }else {
                if (ccmMainReasonList1.size()!=0) {
                    long cmMainReasonId=ccmMainReasonList1.get(0).getId();
                    complaintCustomFieldUseDTO.setIsQuery(false);
                    update(cmMainReasonId, complaintCustomFieldUseDTO);
                }
            }
            //CCM解决方案是否填写
            List<ComplaintCustomFieldUsePO> ccResultList1=complaintCustomFieldUseMapper.queryccResult(userId);
            complaintCustomFieldUseDTO=new ComplaintCustomFieldUseDTO();
            complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
            if(complaintCustomFieldTestDTO.getCcResult1()==true){
                complaintCustomFieldUseDTO.setFieldName("cc_result");
                    complaintCustomFieldUseDTO.setIsQuery(true);
                    if (ccResultList1.size()!=0){
                        long ccResultId=ccResultList1.get(0).getId();
                        update(ccResultId,complaintCustomFieldUseDTO);
                    }else {
                        insert(complaintCustomFieldUseDTO);
                    }

            }else {
                if (ccResultList1.size()!=0) {
                    long ccResultId=ccResultList1.get(0).getId();
                    complaintCustomFieldUseDTO.setIsQuery(false);
                    update(ccResultId, complaintCustomFieldUseDTO);
                }
            }
            //是否协助处理是否填写
            List<ComplaintCustomFieldUsePO> isValid1List=complaintCustomFieldUseMapper.queryisValid(userId);
            complaintCustomFieldUseDTO=new ComplaintCustomFieldUseDTO();
            complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
            if(complaintCustomFieldTestDTO.getIsValid11()==true){
                complaintCustomFieldUseDTO.setFieldName("is_valid");
                complaintCustomFieldUseDTO.setIsQuery(true);
                if (isValid1List.size()!=0){
                    long isValidId=isValid1List.get(0).getId();
                    update(isValidId,complaintCustomFieldUseDTO);
                }else {
                    insert(complaintCustomFieldUseDTO);
                }

            }else {
                if (isValid1List.size()!=0) {
                    long isValidId=isValid1List.get(0).getId();
                    complaintCustomFieldUseDTO.setIsQuery(false);
                    update(isValidId, complaintCustomFieldUseDTO);
                }
            }


            //CCM部位是否填写
            List<ComplaintCustomFieldUsePO> ccmPartAllList=complaintCustomFieldUseMapper.queryCcmPart(userId);
            complaintCustomFieldUseDTO=new ComplaintCustomFieldUseDTO();
            complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
            if(complaintCustomFieldTestDTO.getCcmPart1()==true){
                complaintCustomFieldUseDTO.setFieldName("ccm_part");
                    complaintCustomFieldUseDTO.setIsQuery(true);
                    if (ccmPartAllList.size()!=0){
                        long ccmPartId=ccmPartAllList.get(0).getId();
                        update(ccmPartId,complaintCustomFieldUseDTO);
                    }else {
                        insert(complaintCustomFieldUseDTO);
                    }

            }else {
                if (ccmPartAllList.size()!=0) {
                    long ccmPartId=ccmPartAllList.get(0).getId();
                    complaintCustomFieldUseDTO.setIsQuery(false);
                    update(ccmPartId, complaintCustomFieldUseDTO);
                }
            }

            //CCM细分部位是否填写
            List<ComplaintCustomFieldUsePO> ccmSubdivisionPartList1=complaintCustomFieldUseMapper.queryCcmSubdivisionPart(userId);
            complaintCustomFieldUseDTO=new ComplaintCustomFieldUseDTO();
            complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
            if(complaintCustomFieldTestDTO.getCcmSubdivisionPart1()==true){
                complaintCustomFieldUseDTO.setFieldName("ccm_subdivision_part");
                    complaintCustomFieldUseDTO.setIsQuery(true);
                    if(ccmSubdivisionPartList1.size()!=0){
                        long ccmSubdivisionPartId=ccmSubdivisionPartList1.get(0).getId();
                        update(ccmSubdivisionPartId,complaintCustomFieldUseDTO);
                    }else {
                        insert(complaintCustomFieldUseDTO);
                    }
            }else {
                if(ccmSubdivisionPartList1.size()!=0){
                    long ccmSubdivisionPartId=ccmSubdivisionPartList1.get(0).getId();
                    complaintCustomFieldUseDTO.setIsQuery(false);
                    update(ccmSubdivisionPartId,complaintCustomFieldUseDTO);
                }
            }

            //5日未结案原因是否填写
            List<ComplaintCustomFieldUsePO> smallClass2List1=complaintCustomFieldUseMapper.querysmallClass2(userId);
            complaintCustomFieldUseDTO=new ComplaintCustomFieldUseDTO();
            complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
            if (complaintCustomFieldTestDTO.getSmallClass1()==true){
                complaintCustomFieldUseDTO.setFieldName("small_class");
                    complaintCustomFieldUseDTO.setIsQuery(true);
                    if(smallClass2List1.size()!=0){
                        long smallClassId=smallClass2List1.get(0).getId();
                        update(smallClassId,complaintCustomFieldUseDTO);
                    }else {
                        insert(complaintCustomFieldUseDTO);
                    }

            }else {
                if(smallClass2List1.size()!=0){
                    long smallClassId=smallClass2List1.get(0).getId();
                    complaintCustomFieldUseDTO.setIsQuery(false);
                    update(smallClassId,complaintCustomFieldUseDTO);
                }
            }

            //客诉单类别一阶层是否填写
            List<ComplaintCustomFieldUsePO> category1List1=complaintCustomFieldUseMapper.queryCategory1(userId);
            complaintCustomFieldUseDTO=new ComplaintCustomFieldUseDTO();
            complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
            if(complaintCustomFieldTestDTO.getCategory11()==true){
                complaintCustomFieldUseDTO.setFieldName("category1");
                    complaintCustomFieldUseDTO.setIsQuery(true);
                    if(category1List1.size()!=0){
                        long category1Id=category1List1.get(0).getId();
                        update(category1Id,complaintCustomFieldUseDTO);
                    }else {
                        insert(complaintCustomFieldUseDTO);
                    }

            }else {
                if(category1List1.size()!=0){
                    long category1Id=category1List1.get(0).getId();
                    complaintCustomFieldUseDTO.setIsQuery(false);
                    update(category1Id,complaintCustomFieldUseDTO);
                }
            }

            //客诉单类别二阶层是否填写
            List<ComplaintCustomFieldUsePO> category2List1=complaintCustomFieldUseMapper.queryCategory2(userId);
            complaintCustomFieldUseDTO=new ComplaintCustomFieldUseDTO();
            complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
            if(complaintCustomFieldTestDTO.getCategory21()==true){
                complaintCustomFieldUseDTO.setFieldName("category2");
                    complaintCustomFieldUseDTO.setIsQuery(true);
                    if(category2List1.size()!=0){
                        long category2Id=category2List1.get(0).getId();
                        update(category2Id,complaintCustomFieldUseDTO);
                    }else {
                        insert(complaintCustomFieldUseDTO);
                    }

            }else {
                if(category2List1.size()!=0){
                    long category2Id=category2List1.get(0).getId();
                    complaintCustomFieldUseDTO.setIsQuery(false);
                    update(category2Id,complaintCustomFieldUseDTO);
                }
            }

            //客诉单类别三阶层是否填写
            List<ComplaintCustomFieldUsePO> category3List1=complaintCustomFieldUseMapper.queryCategory3(userId);
            complaintCustomFieldUseDTO=new ComplaintCustomFieldUseDTO();
            complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
            if(complaintCustomFieldTestDTO.getCategory31()==true){
                complaintCustomFieldUseDTO.setFieldName("category3");
                    complaintCustomFieldUseDTO.setIsQuery(true);
                    if(category3List1.size()!=0){
                        long category3Id=category3List1.get(0).getId();
                        update(category3Id,complaintCustomFieldUseDTO);
                    }else {
                        insert(complaintCustomFieldUseDTO);
                    }
            }else {
                if(category3List1.size()!=0){
                    long category3Id=category3List1.get(0).getId();
                    complaintCustomFieldUseDTO.setIsQuery(false);
                    update(category3Id,complaintCustomFieldUseDTO);
                }
            }

            //CCM分类一是否填写
            List<ComplaintCustomFieldUsePO> classification1List1=complaintCustomFieldUseMapper.queryClassification1(userId);
            complaintCustomFieldUseDTO=new ComplaintCustomFieldUseDTO();
            complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
            if(complaintCustomFieldTestDTO.getClassification111()==true){
                complaintCustomFieldUseDTO.setFieldName("classification11");
                    complaintCustomFieldUseDTO.setIsQuery(true);
                    if(classification1List1.size()!=0){
                        long classification1Id=classification1List1.get(0).getId();
                        update(classification1Id,complaintCustomFieldUseDTO);
                    }else {
                        insert(complaintCustomFieldUseDTO);
                    }
            }else {
                if(classification1List1.size()!=0){
                    long classification1Id=classification1List1.get(0).getId();
                    complaintCustomFieldUseDTO.setIsQuery(false);
                    update(classification1Id,complaintCustomFieldUseDTO);
                }
            }

            //CCM分类二是否填写
            List<ComplaintCustomFieldUsePO> classification2List1=complaintCustomFieldUseMapper.queryClassification2(userId);
            complaintCustomFieldUseDTO=new ComplaintCustomFieldUseDTO();
            complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
            if(complaintCustomFieldTestDTO.getClassification211()==true){
                complaintCustomFieldUseDTO.setFieldName("classification21");
                    complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
                    complaintCustomFieldUseDTO.setIsQuery(true);
                    if(classification2List1.size()!=0){
                        long classification2Id=classification2List1.get(0).getId();
                        update(classification2Id,complaintCustomFieldUseDTO);
                    }else {
                        insert(complaintCustomFieldUseDTO);
                    }
            }else {
                if(classification2List1.size()!=0){
                    long classification2Id=classification2List1.get(0).getId();
                    complaintCustomFieldUseDTO.setIsQuery(false);
                    update(classification2Id,complaintCustomFieldUseDTO);
                }
            }

            //CCM分类三是否填写
            List<ComplaintCustomFieldUsePO> classification3List1=complaintCustomFieldUseMapper.queryClassification3(userId);
            complaintCustomFieldUseDTO=new ComplaintCustomFieldUseDTO();

            if(complaintCustomFieldTestDTO.getClassification311()==true){

                complaintCustomFieldUseDTO.setFieldName("classification31");
                complaintCustomFieldUseDTO.setIsQuery(true);
                complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
                    if(classification3List1.size()!=0){
                        long classification3Id=classification3List1.get(0).getId();
                        update(classification3Id,complaintCustomFieldUseDTO);
                    }else {
                        insert(complaintCustomFieldUseDTO);
                    }
            }else {
                if(classification3List1.size()!=0){
                    long classification3Id=classification3List1.get(0).getId();
                    complaintCustomFieldUseDTO.setIsQuery(false);
                    complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
                    update(classification3Id,complaintCustomFieldUseDTO);
                }
            }



            return 1;
        }

        /**
         * 新增排序
         * @param sortList
         * @return
         */
        @Override
        public int insertSort(List<ComplaintCustomFieldUseDTO> sortList) {
            Long userId=FrameworkUtil.getLoginInfo().getUserId();
            complaintCustomFieldUseMapper.deletesort(userId);
            for (int i=0;i<sortList.size();i++){
                ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO= new ComplaintCustomFieldUseDTO();
               String  fieldName=sortList.get(i).getFieldName();
               complaintCustomFieldUseDTO.setFieldName(fieldName);
               String sortType=sortList.get(i).getSortType();
               complaintCustomFieldUseDTO.setSortType(sortType);
                String tableName="";
               switch (fieldName){
                   case "ccm_man" :
                       tableName="tt_complaint_dealer_ccm_ref";
                        complaintCustomFieldUseDTO.setTableName(tableName);
                        complaintCustomFieldUseDTO.setFieldDescribe("ccm_man");
                       break;
                   case "region_manager" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("region_manager");
                       break;
                   case "type" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("type");
                       break;
                   case "work_order_nature" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("work_order_nature");
                       break;
                   case "complaint_id" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("complaint_id");
                       break;
                   case "call_time" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("call_time");
                       break;
                   case "fisrt_restart_dealer_fisrt_reply_time" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("fisrt_restart_dealer_fisrt_reply_time");
                       break;
                   case "subject" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("subject");
                       break;
                   case "work_order_status" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("work_order_status");
                       break;
                   case "dealer_code" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("dealer_code");
                       break;
                   case "dealer_name" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("dealer_name");
                       break;
                   case "bloc" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("bloc");
                       break;
                   case "region" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("region");
                       break;
                   case "source" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("source");
                       break;
                   case "importance_level" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("importance_level");
                       break;
                   case "call_name" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("call_name");
                       break;
                   case "call_tel" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("call_tel");
                       break;
                   case "model" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("model");
                       break;
                   case "model_year" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("model_year");
                       break;
                   case "config_name" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("config_name");
                       break;
                   case "license_plate_num" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("license_plate_num");
                       break;
                   case "is_close_case" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("is_close_case");
                       break;
                   case "data_sources" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("data_sources");
                       break;
                   case "close_case_status" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("close_case_status");
                       break;
                   case "close_case_time" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("close_case_time");
                       break;
                   case "dealer_fisrt_reply_time" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("dealer_fisrt_reply_time");
                       break;
                   case "is_revisit" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("is_revisit");
                       break;
                   case "vin" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("vin");
                       break;
                   case "newest_restart_time" :
                       tableName="tt_complaint_info";
                       complaintCustomFieldUseDTO.setTableName(tableName);
                       complaintCustomFieldUseDTO.setFieldDescribe("newest_restart_time");
                       break;
                       default :



               }
               complaintCustomFieldUseDTO.setIsSort(1);
               complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
               ComplaintCustomFieldUseDTO complaintCustomFieldUseDto=new ComplaintCustomFieldUseDTO();
                complaintCustomFieldUseDto.setUserId(userId);
                complaintCustomFieldUseDto.setFieldName(fieldName);
                complaintCustomFieldUseDto.setTableName(tableName);
                List<ComplaintCustomFieldUsePO> sortlist=complaintCustomFieldUseMapper.querysort(complaintCustomFieldUseDto);
               if(sortlist.size()!=0){
                   long sortId=sortlist.get(0).getId();
                   update(sortId,complaintCustomFieldUseDTO);
               }else {
                   insert(complaintCustomFieldUseDTO);
               }
               }

            return 1;
        }

        /**
         * 自定义重置
         */
        @Override
        public int resetFied() {
            long userId=FrameworkUtil.getLoginInfo().getUserId();
            return   complaintCustomFieldUseMapper.resetFied(userId);


        }


    }
