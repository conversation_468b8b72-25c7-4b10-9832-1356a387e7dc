package com.yonyou.dmscus.customer.service.inviteInsurance;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscloud.function.utils.common.StringUtils;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceSaAllocateRuleDetailMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceSaAllocateRuleDetailDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceSaAllocateRuleDetailPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 续保SA分配规则明细表,通过经销商CODE与invite_insurance_sa_allocate_rule关联，用于经销商SA分配中平均分配 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@Service
public class InviteInsuranceSaAllocateRuleDetailServiceImpl implements InviteInsuranceSaAllocateRuleDetailService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteInsuranceSaAllocateRuleDetailMapper inviteInsuranceSaAllocateRuleDetailMapper;

    @Override
    public List<InviteInsuranceSaAllocateRuleDetailDTO> getInsuranceSaAllocateRuleDetail(String dealerCode) {
        InviteInsuranceSaAllocateRuleDetailPO inviteInsuranceSaAllocateRuleDetailPO=new InviteInsuranceSaAllocateRuleDetailPO();
        if(dealerCode==null){
            //获取登录用户信息
            LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
            dealerCode=loginInfoDto.getOwnerCode();
        }
        inviteInsuranceSaAllocateRuleDetailPO.setDealerCode(dealerCode);
        List<InviteInsuranceSaAllocateRuleDetailPO> list = inviteInsuranceSaAllocateRuleDetailMapper.selectListBySql
                (inviteInsuranceSaAllocateRuleDetailPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteInsuranceSaAllocateRuleDetailDTO.class)).collect(Collectors
                    .toList());
        }
    }

    @Override
    public List<InviteInsuranceSaAllocateRuleDetailDTO> getInsuranceSaAllocateRuleDetailList(String dealerCodeList) {
        if (StringUtils.isBlank(dealerCodeList)) {
            throw new ServiceBizException("经销商code不能为空");
        }
        List<String> list = Arrays.asList(dealerCodeList.split(","));
        LambdaQueryWrapper<InviteInsuranceSaAllocateRuleDetailPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(InviteInsuranceSaAllocateRuleDetailPO::getDealerCode, list)
                        .eq(InviteInsuranceSaAllocateRuleDetailPO::getIsDeleted,0);
        List<InviteInsuranceSaAllocateRuleDetailPO> insuranceSaAllocateRuleDetailPOS = inviteInsuranceSaAllocateRuleDetailMapper.selectList(queryWrapper);
        logger.info("insuranceSaAllocateRuleDetailPOS------>{}", insuranceSaAllocateRuleDetailPOS);
        if (CommonUtils.isNullOrEmpty(insuranceSaAllocateRuleDetailPOS)) {
            return new ArrayList<>();
        } else {
            return insuranceSaAllocateRuleDetailPOS.stream().map(m -> m.transPoToDto(InviteInsuranceSaAllocateRuleDetailDTO.class))
                    .collect(Collectors.toList());
        }
    }

    @Override
    public int saveInsuranceSaAllocateRuleDetail(List<InviteInsuranceSaAllocateRuleDetailDTO> list) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        for (InviteInsuranceSaAllocateRuleDetailDTO dto : list) {
            InviteInsuranceSaAllocateRuleDetailPO po = dto.transDtoToPo(InviteInsuranceSaAllocateRuleDetailPO.class);
            //门店code
            po.setDealerCode(loginInfoDto.getOwnerCode());
            if(dto.getStatus().equals("A")){
                //新增
                inviteInsuranceSaAllocateRuleDetailMapper.insert(po);
            }else if(dto.getStatus().equals("D")){
                //删除
                inviteInsuranceSaAllocateRuleDetailMapper.deleteById(dto.getId());
            }else if(dto.getStatus().equals("U")){
                //更新
                inviteInsuranceSaAllocateRuleDetailMapper.updateById(po);
            }
        }
        return 1;
    }



}
