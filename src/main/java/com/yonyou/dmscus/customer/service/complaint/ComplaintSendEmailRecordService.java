package com.yonyou.dmscus.customer.service.complaint;

    import com.baomidou.mybatisplus.core.metadata.IPage;
    import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailRecordDTO;

    import java.util.List;


/**
 * <p>
 * 客户投诉发送邮件记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
public interface ComplaintSendEmailRecordService  {
    /**
     * 分页查询
     * @param page
     * @param complaintSendEmailRecordDTO
     * @return
     */
       IPage<ComplaintSendEmailRecordDTO> selectPageBysql(Page page, ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO);

    /**
     * 集合查询
     * @param complaintSendEmailRecordDTO
     * @return
     */
       List<ComplaintSendEmailRecordDTO> selectListBySql(ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO);

    /**
     * 通过id查询
     * @param id
     * @return
     */
    ComplaintSendEmailRecordDTO getById(Long id);

    /**
     * 新增
     * @param complaintSendEmailRecordDTO
     * @return
     */
    int insert(ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO);

    /**
     * 更新
     * @param id
     * @param complaintSendEmailRecordDTO
     * @return
     */
    int update(Long id, ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO);

    /**
     * 通过id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     *查询发件人最近使用过得邮箱
     * @param userId
     * @return
     */
    ComplaintSendEmailRecordDTO selectLastEmail(long userId);
    /**
     *查询发件人使用过得邮箱
     * @param userId
     * @return
     */
    List<ComplaintSendEmailRecordDTO> selectEmaillist(long userId);
}
