package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

    
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintSendEmailRecordPO;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintSendEmailRecordMapper;
import com.yonyou.dmscus.customer.service.complaint.ComplaintSendEmailRecordService;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailRecordDTO;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;




    /**
 * <p>
 * 客户投诉发送邮件记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
@Service
public class ComplaintSendEmailRecordServiceImpl implements ComplaintSendEmailRecordService {
        //日志对象
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        ComplaintSendEmailRecordMapper complaintSendEmailRecordMapper;

        /**
         * 分页查询对应数据
         *
         * @param page 分页对象
         * @param complaintSendEmailRecordDTO 查询条件
         * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailRecordDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public IPage<ComplaintSendEmailRecordDTO>selectPageBysql(Page page,ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO){
            if(complaintSendEmailRecordDTO ==null){
                complaintSendEmailRecordDTO =new ComplaintSendEmailRecordDTO();
            }
            ComplaintSendEmailRecordPO complaintSendEmailRecordPo =complaintSendEmailRecordDTO.transDtoToPo(ComplaintSendEmailRecordPO.class);

            List<ComplaintSendEmailRecordPO>list= complaintSendEmailRecordMapper.selectPageBySql(page,complaintSendEmailRecordPo);
            if(CommonUtils.isNullOrEmpty(list)){
                page.setRecords(new ArrayList<>());
                return page;
            }else{
                List<ComplaintSendEmailRecordDTO>result=list.stream().map(m->m.transPoToDto(ComplaintSendEmailRecordDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param complaintSendEmailRecordDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailRecordDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<ComplaintSendEmailRecordDTO>selectListBySql(ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO){
            if(complaintSendEmailRecordDTO ==null){
                complaintSendEmailRecordDTO =new ComplaintSendEmailRecordDTO();
            }
            ComplaintSendEmailRecordPO complaintSendEmailRecordPo =complaintSendEmailRecordDTO.transDtoToPo(ComplaintSendEmailRecordPO.class);
            List<ComplaintSendEmailRecordPO>list= complaintSendEmailRecordMapper.selectListBySql(complaintSendEmailRecordPo);
            if(CommonUtils.isNullOrEmpty(list)){
                return new ArrayList<>();
            }else{
                return list.stream().map(m->m.transPoToDto(ComplaintSendEmailRecordDTO.class)).collect(Collectors.toList());
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailRecordDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public ComplaintSendEmailRecordDTO getById(Long id){
            ComplaintSendEmailRecordPO complaintSendEmailRecordPo = complaintSendEmailRecordMapper.selectById(id);
            if(complaintSendEmailRecordPo!=null){
                return complaintSendEmailRecordPo.transPoToDto(ComplaintSendEmailRecordDTO.class);
            }else{
                throw new DALException("查询失败,没有符合条件的数据");
            }
        }

        /**
         * 根据DTO 进行数据新增
         *
         * @param complaintSendEmailRecordDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO){
            //对对象进行赋值操作
            ComplaintSendEmailRecordPO complaintSendEmailRecordPo = complaintSendEmailRecordDTO.transDtoToPo(ComplaintSendEmailRecordPO.class);
            //执行插入
            int row= complaintSendEmailRecordMapper.insert(complaintSendEmailRecordPo);
            //返回插入的值
            return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param complaintSendEmailRecordDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO){
            ComplaintSendEmailRecordPO complaintSendEmailRecordPo = complaintSendEmailRecordMapper.selectById(id);
            //对对象进行赋值操作
            complaintSendEmailRecordDTO.transDtoToPo(complaintSendEmailRecordPo);
            //执行更新
            int row= complaintSendEmailRecordMapper.updateById(complaintSendEmailRecordPo);
            return row;
        }

        /**
         * 根据ID 进行删除数据
         *
         * @param id
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteById(Long id){
            int deleteCount= complaintSendEmailRecordMapper.deleteById(id);
            logger.debug("删除数量：{}",deleteCount);
            if(deleteCount<=0){
                throw new DALException("删除失败,没有符合条件的数据");
            }else{
                return deleteCount;
            }
        }

        /**查询发件人最近使用过得邮箱
         * @param userId
         * @return
         */
        @Override
        public ComplaintSendEmailRecordDTO selectLastEmail(long userId) {
            ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO=complaintSendEmailRecordMapper.selectLastEmail(userId);
            return complaintSendEmailRecordDTO;
        }
        /**
         * 查询发件人使用过得邮箱
         * @param userId
         * @return
         */
        @Override
        public List<ComplaintSendEmailRecordDTO> selectEmaillist(long userId) {
            List<ComplaintSendEmailRecordDTO> list=complaintSendEmailRecordMapper.selectEmaillist(userId);
            return list;
        }

}
