package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yonyou.dmscus.customer.dao.voc.VocFunctionalStatusRecordMapper;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusLogPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusRecordPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataRecordPo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @title: VocFunctionalStatusRecordServiceImpl
 * @projectName dmscus.customer
 * @date 2022/11/118:48
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class VocFunctionalStatusRecordServiceImpl implements VocFunctionalStatusRecordService {
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    VocFunctionalStatusRecordMapper vocFunctionalStatusRecordMapper;

    @Override
    public int insertList(List<VocFunctionalStatusRecordPO> list) {
        addList(list);
        return list.size();
    }

    @Override
    public int updateList(List<VocFunctionalStatusRecordPO> updateList) {
        update(updateList);
        return updateList.size();
    }

    @Override
    public List<VocFunctionalStatusRecordPO> selectListStatusRecord(List<VocFunctionalStatusLogPO>  x) {
        return vocFunctionalStatusRecordMapper.selectListStatusRecord(x);
    }

    @Override
    public  List<VocFunctionalStatusRecordPO>  selectVocFunctionalStatusRecordByModfiyTime(String dateTime, Integer begIndex, Integer partitionSize) {
        QueryWrapper<VocFunctionalStatusRecordPO> qw =  new QueryWrapper<>();
        qw.eq("modify_time",dateTime);
        qw.eq("is_deleted",0);
        qw.orderByDesc("id");
        qw.last(" limit "+begIndex+","+partitionSize);
        return vocFunctionalStatusRecordMapper.selectList(qw);
    }

    @Override
    public List<VocFunctionalStatusRecordPO> selectListByVins(List<String> xx) {
        return vocFunctionalStatusRecordMapper.selectListByVins(xx);
    }

    @Override
    public int selectByVin(String vin) {
        QueryWrapper<VocFunctionalStatusRecordPO>  qw =  new QueryWrapper<>();
        qw.eq("is_deleted",0);
        qw.eq("vin",vin);
        qw.in("activated_state",1);
        return vocFunctionalStatusRecordMapper.selectCount(qw);
    }

    @Override
    public int selectIsStatusByVin(String vin) {
        LambdaQueryWrapper<VocFunctionalStatusRecordPO>  qw =  new LambdaQueryWrapper<>();
        qw.eq(VocFunctionalStatusRecordPO::getIsDeleted,0)
          .eq(VocFunctionalStatusRecordPO::getVin,vin)
           .orderByDesc(VocFunctionalStatusRecordPO::getId)
            .last("limit 1");
      VocFunctionalStatusRecordPO po =   vocFunctionalStatusRecordMapper.selectOne(qw);
      if(ObjectUtils.isNotEmpty(po)&& po.getActivatedState()==0){
              return 1 ;
      }
        return 0;
    }


    /**
     * @param list
     */
    private void addList(List<VocFunctionalStatusRecordPO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        int numPerTimes21 = 200;
        if (list.size() <= numPerTimes21) {
            batchAdd(list); //此处插入少于200条list
            logger.info("voc激活定时任务保存数据VocFunctionalStatusRecordPO成功，数据小于200");
        } else {
            int maxIndex2 = list.size();
            int maxTimes1 = maxIndex2 / numPerTimes21;
            maxTimes1 += (maxIndex2 % numPerTimes21) > 0 ? 1 : 0;
            int currentTimes1 = 0;
            while (currentTimes1 < maxTimes1) {
                int fromIndex1 = numPerTimes21 * currentTimes1;
                int toIndex1 = fromIndex1 + numPerTimes21;
                toIndex1 = toIndex1 > maxIndex2 ? maxIndex2 : toIndex1;
                List<VocFunctionalStatusRecordPO> subList = list.subList(fromIndex1, toIndex1);
                batchAdd(subList);//此处循环插入200条list
                logger.info("voc激活定时任务保存数据VocFunctionalStatusRecordPO成功，数据大于200{},{}", fromIndex1, toIndex1);
                currentTimes1++;
            }
        }

    }

    /**
     * 批量新增
     *
     * @param list
     */
    private void batchAdd(List<VocFunctionalStatusRecordPO> list) {
        vocFunctionalStatusRecordMapper.insertList(list);
    }

    /**
     * @param list
     */
    private void update(List<VocFunctionalStatusRecordPO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        int numPerTimes2 = 200;
        if (list.size() <= numPerTimes2) {
            batchRecordUpdate(list); //此处插入少于200条list
            logger.info("voc激活定时任务修改数据VocFunctionalStatusRecordPO成功，数据小于200");
        } else {
            int maxIndex2 = list.size();
            int maxTimes1 = maxIndex2 / numPerTimes2;
            maxTimes1 += (maxIndex2 % numPerTimes2) > 0 ? 1 : 0;
            int currentTimes1 = 0;
            while (currentTimes1 < maxTimes1) {
                int fromIndex1 = numPerTimes2 * currentTimes1;
                int toIndex1 = fromIndex1 + numPerTimes2;
                toIndex1 = toIndex1 > maxIndex2 ? maxIndex2 : toIndex1;
                List<VocFunctionalStatusRecordPO> subList = list.subList(fromIndex1, toIndex1);
                batchRecordUpdate(subList);//此处循环插入200条list
                logger.info("voc激活定时任务修改数据VocFunctionalStatusRecordPO成功，数据大于200{},{}", fromIndex1, toIndex1);
                currentTimes1++;
            }
        }

    }

    /**
     * 批量修改
     *
     * @param list
     */
    private void batchRecordUpdate(List<VocFunctionalStatusRecordPO> list) {
         vocFunctionalStatusRecordMapper.updateList(list);
    }

    @Override
    public int selectVocFunctionalStatusRecordByVin(String vin) {
        LambdaQueryWrapper<VocFunctionalStatusRecordPO> qw = new LambdaQueryWrapper<>();
        qw.eq(VocFunctionalStatusRecordPO::getIsDeleted,0);
        qw.eq(VocFunctionalStatusRecordPO::getVin,vin);
        qw.eq(VocFunctionalStatusRecordPO::getActivatedState,0);
        qw.orderByDesc(VocFunctionalStatusRecordPO::getId);
        qw.last("limit 1");
        return  vocFunctionalStatusRecordMapper.selectCount(qw);
    }

    @Override
    public List<VocFunctionalStatusRecordPO> selectVocFunctionalStatusRecordPOByModfiyTime(String dateTime, Integer begIndex1, Integer partitionSize1) {
        LambdaQueryWrapper<VocFunctionalStatusRecordPO>  qw = new LambdaQueryWrapper();
        qw.eq(VocFunctionalStatusRecordPO::getModifyTime,dateTime);
        qw.eq(VocFunctionalStatusRecordPO::getIsExecute,0);
        qw.eq(VocFunctionalStatusRecordPO::getIsDeleted,0);
        qw.eq(VocFunctionalStatusRecordPO::getActivatedState,0);
        qw.orderByDesc(VocFunctionalStatusRecordPO::getId);
        qw.last(" limit "+begIndex1+","+partitionSize1);
        return vocFunctionalStatusRecordMapper.selectList(qw);
    }

    @Override
    public int updateFunctionalIsExecute(List<VocFunctionalStatusRecordPO> list){
        if (CollectionUtils.isNotEmpty(list)){
            return vocFunctionalStatusRecordMapper.updateFunctionalIsExecute(list);
        }
        return 0;
    }
}
