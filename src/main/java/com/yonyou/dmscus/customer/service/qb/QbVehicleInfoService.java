package com.yonyou.dmscus.customer.service.qb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.qb.QbVehicleInfoDTO;
import com.yonyou.dmscus.customer.entity.po.qb.InviteQBVehicleImportPO;
import com.yonyou.dmscus.customer.service.common.IBaseService;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2020/11/10 0010
 */
public interface QbVehicleInfoService {

    public IPage<QbVehicleInfoDTO> selectPageBysql(Page page, QbVehicleInfoDTO qbVehicleInfoDTO);

    public int createdInviteVehicle(QbVehicleInfoDTO qbVehicleInfoDTO);
    
    int createQb(String list);

    ImportTempResult<InviteQBVehicleImportPO> importQBTemp(MultipartFile importFile) throws   Exception;

    int batchInsert();

    IPage<InviteQBVehicleImportPO> selectErrorPage(Page page);
}
