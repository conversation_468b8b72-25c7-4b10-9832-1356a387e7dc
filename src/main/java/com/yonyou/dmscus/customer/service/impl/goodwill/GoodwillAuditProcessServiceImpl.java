package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillAuditProcessMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditProcessDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAuditProcessPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillAuditProcessService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 亲善审批流程表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@Service
public class GoodwillAuditProcessServiceImpl extends ServiceImpl<GoodwillAuditProcessMapper, GoodwillAuditProcessPO>
		implements GoodwillAuditProcessService {
	/** 日志对象 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillAuditProcessMapper goodwillAuditProcessMapper;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillAuditProcessDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.GoodwillAuditProcessDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillAuditProcessDTO> selectPageBysql(Page page, GoodwillAuditProcessDTO goodwillAuditProcessDTO) {
		if (goodwillAuditProcessDTO == null) {
			goodwillAuditProcessDTO = new GoodwillAuditProcessDTO();
		}
		GoodwillAuditProcessPO goodwillAuditProcessPo = goodwillAuditProcessDTO
				.transDtoToPo(GoodwillAuditProcessPO.class);

		List<GoodwillAuditProcessPO> list = goodwillAuditProcessMapper.selectPageBySql(page, goodwillAuditProcessPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillAuditProcessDTO> result = list.stream().map(m -> m.transPoToDto(GoodwillAuditProcessDTO.class))
					.collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillAuditProcessDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.GoodwillAuditProcessDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillAuditProcessDTO> selectListBySql(GoodwillAuditProcessDTO goodwillAuditProcessDTO) {
		if (goodwillAuditProcessDTO == null) {
			goodwillAuditProcessDTO = new GoodwillAuditProcessDTO();
		}
		GoodwillAuditProcessPO goodwillAuditProcessPo = goodwillAuditProcessDTO
				.transDtoToPo(GoodwillAuditProcessPO.class);
		List<GoodwillAuditProcessPO> list = goodwillAuditProcessMapper.selectListBySql(goodwillAuditProcessPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillAuditProcessDTO.class)).collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillAuditProcessDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.GoodwillAuditProcessDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<Map> selectAuditProcessInfo(GoodwillAuditProcessDTO goodwillAuditProcessDTO) {
		if (goodwillAuditProcessDTO == null) {
			goodwillAuditProcessDTO = new GoodwillAuditProcessDTO();
		}
		GoodwillAuditProcessPO goodwillAuditProcessPo = goodwillAuditProcessDTO
				.transDtoToPo(GoodwillAuditProcessPO.class);
		List<Map> list = goodwillAuditProcessMapper.selectAuditProcessInfo(goodwillAuditProcessPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.GoodwillAuditProcessDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillAuditProcessDTO getById(Long id) {
		GoodwillAuditProcessPO goodwillAuditProcessPo = goodwillAuditProcessMapper.selectById(id);
		if (goodwillAuditProcessPo != null) {
			return goodwillAuditProcessPo.transPoToDto(GoodwillAuditProcessDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillAuditProcessDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillAuditProcessDTO goodwillAuditProcessDTO) {
		// 对对象进行赋值操作
		GoodwillAuditProcessPO goodwillAuditProcessPo = goodwillAuditProcessDTO
				.transDtoToPo(GoodwillAuditProcessPO.class);
		// 执行插入
		int row = goodwillAuditProcessMapper.insert(goodwillAuditProcessPo);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 *
	 * @param GoodwillAuditProcessDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020/4/21
	 */
	@Override
	public int insertRefuseBills(GoodwillAuditProcessDTO goodwillAuditProcessDTO) {

		if (goodwillAuditProcessDTO.getId() == null) {
			// 执行新增
			// 对对象进行赋值操作
			GoodwillAuditProcessPO goodwillAuditProcessPo = goodwillAuditProcessDTO
					.transDtoToPo(GoodwillAuditProcessPO.class);

			goodwillAuditProcessMapper.insert(goodwillAuditProcessPo);
		} else {
			// 执行更新
			GoodwillAuditProcessPO goodwillAuditProcessPo = goodwillAuditProcessMapper
					.selectById(goodwillAuditProcessDTO.getId());

			// 对对象进行赋值操作
			goodwillAuditProcessDTO.transDtoToPo(goodwillAuditProcessPo);
			goodwillAuditProcessMapper.updateById(goodwillAuditProcessPo);
		}

		return 1;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 *
	 * @param GoodwillAuditProcessDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020/4/21
	 */
	@Override
	public int insertAuditProcessInfo(List<GoodwillAuditProcessDTO> goodwillAuditProcessDTO) {
		if (!CommonUtils.isNullOrEmpty(goodwillAuditProcessDTO)) {
			for (GoodwillAuditProcessDTO goodwillAuditProcessDtos : goodwillAuditProcessDTO) {
				if (goodwillAuditProcessDtos.getId() == null) {
					// 执行新增
					// 对对象进行赋值操作
					GoodwillAuditProcessPO goodwillAuditProcessPo = goodwillAuditProcessDtos
							.transDtoToPo(GoodwillAuditProcessPO.class);

					goodwillAuditProcessMapper.insert(goodwillAuditProcessPo);
				} else {
					// 执行更新
					GoodwillAuditProcessPO goodwillAuditProcessPo = goodwillAuditProcessMapper
							.selectById(goodwillAuditProcessDtos.getId());

					// 对对象进行赋值操作
					goodwillAuditProcessDtos.transDtoToPo(goodwillAuditProcessPo);
					goodwillAuditProcessMapper.updateById(goodwillAuditProcessPo);
				}
			}
		}

		return 1;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillAuditProcessDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillAuditProcessDTO goodwillAuditProcessDTO) {
		GoodwillAuditProcessPO goodwillAuditProcessPo = goodwillAuditProcessMapper.selectById(id);
		// 对对象进行赋值操作
		goodwillAuditProcessDTO.transDtoToPo(goodwillAuditProcessPo);
		// 执行更新
		int row = goodwillAuditProcessMapper.updateById(goodwillAuditProcessPo);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillAuditProcessMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillAuditProcessMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据审批金额、审核对象，审核类型查询审批流程
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2020/5/19
	 */
	@Override
	public List<GoodwillAuditProcessDTO> selectListBydealGoodwillFlowData(Integer auditObject, Integer auditType, BigDecimal auditAmount) {
		List<GoodwillAuditProcessDTO> processList = new ArrayList<>();
		// 先查询审核类型和审核对象的流程
		List<GoodwillAuditProcessPO> list = goodwillAuditProcessMapper.selectList(auditObject, auditType);
		if (!CommonUtils.isNullOrEmpty(list)) {
			// 循环流程并和预申请单金额比较
			for (GoodwillAuditProcessPO goodwillAuditProcessPo : list) {
				GoodwillAuditProcessDTO dto = goodwillAuditProcessPo.transPoToDto(GoodwillAuditProcessDTO.class);
				// 判断最大审批金额是否为空：
				// 1为空：判断审核金额是否大于最小审批金额，大于直接新增
				// 2不为空：判断审核金额是否大于最大审批金额
				// 1大于：直接新增
				// 2不大于：判断最小取值关系
				// 1大于最小金额：直接新增
				// 2大于或等于最小金额：直接新增
				if (StringUtils.isNullOrEmpty(dto.getMaxAuditPrice())) {
					if (!StringUtils.isNullOrEmpty(dto.getMinAuditPrice())) {
						if (auditAmount.compareTo(dto.getMinAuditPrice()) == 1) {
							processList.add(dto);
							logger.info("dto.getMaxAuditPrice() is null :{},{}.compareTo({})==1", JSONObject.toJSON(dto),auditAmount,dto.getMinAuditPrice());
						}
					}
				} else {
					if (auditAmount.compareTo(dto.getMaxAuditPrice()) == 1) {
						processList.add(dto);
						logger.info("dto.getMaxAuditPrice() is not null :{},{}.compareTo({})==1", JSONObject.toJSON(dto),auditAmount,dto.getMaxAuditPrice());
					} else {
						if (!StringUtils.isNullOrEmpty(dto.getMinAuditPrice())) {
							if (!StringUtils.isNullOrEmpty(dto.getMinValueRelation())) {
								if (dto.getMinValueRelation() == 10011001) {
									if (auditAmount.compareTo(dto.getMinAuditPrice()) == 1) {
										processList.add(dto);
										logger.info("xxdto.getMaxAuditPrice() is  not  null :{},{}.compareTo({})==1", JSONObject.toJSON(dto),auditAmount,dto.getMinAuditPrice());
									}
								} else {
									if (auditAmount.compareTo(dto.getMinAuditPrice()) > -1) {
										processList.add(dto);
										logger.info("yydto.getMaxAuditPrice() is  not  null :{},{}.compareTo({})==1", JSONObject.toJSON(dto),auditAmount,dto.getMinAuditPrice());

									}
								}
							}
						}
					}
				}
			}
		}

		if (!CommonUtils.isNullOrEmpty(processList)) {
			return processList;
		} else {
			return null;
		}
	}

	/**
	 * 根据审批金额、审核对象，审核类型查询审批流程
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2020/5/19
	 */
	@Override
	public List<GoodwillAuditProcessDTO> selectList(Integer auditObject, Integer auditType, BigDecimal auditAmount, Long afterSmallAreaId, Long afterBigAreaId) {
		logger.info("亲善查询审核流程表参数：{}，{}，{}，{}，{}",auditObject,auditType,auditAmount,afterSmallAreaId,afterBigAreaId);
		List<GoodwillAuditProcessDTO> processList = new ArrayList<>();
		// 先查询审核类型和审核对象的流程
		List<GoodwillAuditProcessPO> list = goodwillAuditProcessMapper.selectList(auditObject, auditType);
		logger.info("亲善查询审核流程表结果：{}",JSONObject.toJSON(list));
		int falg = 0;
		if (afterSmallAreaId == null && afterBigAreaId !=null ) {
			falg = 1;
		}
		if (afterBigAreaId == null && afterSmallAreaId !=null ) {
			falg = 2;
		}
		if(afterBigAreaId == null && afterSmallAreaId ==null){
			falg = 3;
		}

		if (!CommonUtils.isNullOrEmpty(list)) {
			// 循环流程并和预申请单金额比较
			for (GoodwillAuditProcessPO goodwillAuditProcessPo : list) {
				GoodwillAuditProcessDTO dto = goodwillAuditProcessPo.transPoToDto(GoodwillAuditProcessDTO.class);
				// 判断最大审批金额是否为空：
				// 1为空：判断审核金额是否大于最小审批金额，大于直接新增
				// 2不为空：判断审核金额是否大于最大审批金额
				// 1大于：直接新增
				// 2不大于：判断最小取值关系
				// 1大于最小金额：直接新增
				// 2大于或等于最小金额：直接新增
				if (StringUtils.isNullOrEmpty(dto.getMaxAuditPrice())) {
					if (!StringUtils.isNullOrEmpty(dto.getMinAuditPrice())) {
						if (auditAmount.compareTo(dto.getMinAuditPrice()) == 1) {
							processList.add(dto);
							logger.info("dto.getMaxAuditPrice() is null :{},{}.compareTo({})==1", JSONObject.toJSON(dto), auditAmount, dto.getMinAuditPrice());
						}
					}
				} else {
					if (auditAmount.compareTo(dto.getMaxAuditPrice()) == 1) {
						processList.add(dto);
						logger.info("dto.getMaxAuditPrice() is not null :{},{}.compareTo({})==1", JSONObject.toJSON(dto), auditAmount, dto.getMaxAuditPrice());
					} else {
						if (!StringUtils.isNullOrEmpty(dto.getMinAuditPrice())) {
							if (!StringUtils.isNullOrEmpty(dto.getMinValueRelation())) {
								if (dto.getMinValueRelation() == 10011001) {
									if (auditAmount.compareTo(dto.getMinAuditPrice()) == 1) {
										processList.add(dto);
										logger.info("xxdto.getMaxAuditPrice() is  not  null :{},{}.compareTo({})==1", JSONObject.toJSON(dto), auditAmount, dto.getMinAuditPrice());
									}
								} else {
									if (auditAmount.compareTo(dto.getMinAuditPrice()) > -1) {
										processList.add(dto);
										logger.info("yydto.getMaxAuditPrice() is  not  null :{},{}.compareTo({})==1", JSONObject.toJSON(dto), auditAmount, dto.getMinAuditPrice());

									}
								}
							}
						}
					}
				}
			}
		}
		logger.info("亲善查询审核流程表结果processList.size({}),processList：{}",processList.size(),JSONObject.toJSON(processList));
		if (!CommonUtils.isNullOrEmpty(processList)) {
			//如果小区id 为空时，追加大区经理，如果大区id为空时 追加CCMQ
			if (falg == 1) {
				//没有小区经理
					Iterator<GoodwillAuditProcessDTO> iterator = processList.iterator();
					boolean apply =true;
					boolean material  =true;
					while (iterator.hasNext()) {
						GoodwillAuditProcessDTO dot= 	iterator.next();
						if("2".equals(dot.getAuditPosition()) && 0 == dot.getAuditType()){
							logger.info("1dot.getAuditObject()：{}",dot.getAuditObject());
							if(dot.getAuditObject()==0){
								apply =false;
							}
							if(dot.getAuditObject()==1){
								material =false;
							}
						}
						if ("1".equals(dot.getAuditPosition()) && 0 == dot.getAuditType()) {
							logger.info("1删除数据：{}",JSONObject.toJSON(dot));
							iterator.remove();
						}
					}
				logger.info("1亲善查询审核流程表结果processList.size({})",processList.size());
					for (GoodwillAuditProcessPO goodwillAuditProcessPo : list) {
						GoodwillAuditProcessDTO dto = goodwillAuditProcessPo.transPoToDto(GoodwillAuditProcessDTO.class);
						if ("2".equals(dto.getAuditPosition()) && 0==dto.getAuditType()) {
							if(dto.getAuditObject()==0&& apply){
								processList.add(dto);
							}
							if(dto.getAuditObject()==1&& material){
								processList.add(dto);
							}
						}
					}
				logger.info("1亲善查询审核流程表结果processList.size({})",processList.size());
			}else
			if (falg == 2) {
				//没有大区经理
				boolean apply =true;
				boolean material  =true;
				Iterator<GoodwillAuditProcessDTO> iterator = processList.iterator();
				while (iterator.hasNext()) {
					GoodwillAuditProcessDTO dot= 	iterator.next();
					if("3".equals(dot.getAuditPosition()) && 0 == dot.getAuditType()){
						logger.info("2dot.getAuditObject()：{}",dot.getAuditObject());
						if(dot.getAuditObject()==0){
							apply =false;
						}
						if(dot.getAuditObject()==1){
							material =false;
						}
					}
					if ("2".equals(dot.getAuditPosition()) && 0 == dot.getAuditType()) {
						logger.info("2删除数据：{}",JSONObject.toJSON(dot));
						iterator.remove();
					}

				}
				logger.info("2亲善查询审核流程表结果processList.size({})",processList.size());
				BigDecimal maax = new BigDecimal(0);
				Integer maxValueRelation = 0;
				for (GoodwillAuditProcessPO goodwillAuditProcessPo : list) {
						GoodwillAuditProcessDTO dto = goodwillAuditProcessPo.transPoToDto(GoodwillAuditProcessDTO.class);
						if(dto.getAuditObject()==0 && "1".equals(goodwillAuditProcessPo.getAuditPosition()) && 0==dto.getAuditType() ){
							maax= 	dto.getMaxAuditPrice();
							maxValueRelation =dto.getMinValueRelation();
					     }

						if ("3".equals(goodwillAuditProcessPo.getAuditPosition()) && 0==dto.getAuditType() ) {
							if(dto.getAuditObject()==0&& apply  && getCheck(auditAmount,maax,maxValueRelation)){
								processList.add(dto);
							}
							if(dto.getAuditObject()==1&& material){
								processList.add(dto);
							}
						}
					}
				logger.info("2亲善查询审核流程表结果processList.size({})",processList.size());
			}else

			if(falg ==3){
				Iterator<GoodwillAuditProcessDTO> iterator = processList.iterator();
				boolean apply =true;
				boolean material  =true;
				while (iterator.hasNext()) {
					GoodwillAuditProcessDTO dot= 	iterator.next();
					if("3".equals(dot.getAuditPosition()) && 0 == dot.getAuditType()){
						logger.info("3dot.getAuditObject()：{}",dot.getAuditObject());
						if(dot.getAuditObject()==0){
							apply =false;
						}
						if(dot.getAuditObject()==1){
							material =false;
						}
					}
					if (("1".equals(dot.getAuditPosition())|| "2".equals(dot.getAuditPosition())) && 0 == dot.getAuditType()) {
						logger.info("3删除数据：{}",JSONObject.toJSON(dot));
						iterator.remove();
					}

				}
				logger.info("3亲善查询审核流程表结果processList.size({})",processList.size());
				for (GoodwillAuditProcessPO goodwillAuditProcessPo : list) {
						GoodwillAuditProcessDTO dto = goodwillAuditProcessPo.transPoToDto(GoodwillAuditProcessDTO.class);
						if ("3".equals(goodwillAuditProcessPo.getAuditPosition())  && 0==dto.getAuditType()) {
							if(dto.getAuditObject()==0&& apply){
								processList.add(dto);
							}
							if(dto.getAuditObject()==1&& material){
								processList.add(dto);
							}
						}
				}
				logger.info("3亲善查询审核流程表结果processList.size({})",processList.size());
			}
			//数据排序 audit_object , audit_position
			if(processList!=null &&  processList.size()>0){
				List<GoodwillAuditProcessDTO>  re= 	getListSort(processList);
				logger.info("re:{}",JSONObject.toJSON(re));
				return  re ;
			}else{

				return processList;
			}

		} else {
			return null;
		}
	}

	private List<GoodwillAuditProcessDTO> getListSort(List<GoodwillAuditProcessDTO> processList) {
		List<GoodwillAuditProcessDTO> result = processList.stream()
				.sorted(Comparator.comparing((GoodwillAuditProcessDTO p) -> p.getAuditObject())
						.thenComparing(GoodwillAuditProcessDTO::getAuditPosition)).collect(Collectors.toList());
		return  result;

	}

	//更具金额和查询的范围比较是否需要添加下级审核权限10011002 《=，10011001《
	private boolean getCheck(BigDecimal auditAmount, BigDecimal dto, Integer maxValueRelation) {
		//设置为小于时，如果申请金额大于等于小区审核最大值需要添加总监审核
		if(maxValueRelation==10011001){
			if(auditAmount.compareTo(dto)>-1){
				return  true;
			}
		}
		//设置为小于等于时，如果申请金额大于小区审核最大值需要添加总监审核
		if(maxValueRelation==10011002){
			if(auditAmount.compareTo(dto)>0){
				return  true;
			}
		}
		return  false;
	}
}