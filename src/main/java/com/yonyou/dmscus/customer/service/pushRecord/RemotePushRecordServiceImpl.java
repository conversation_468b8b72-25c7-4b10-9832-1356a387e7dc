package com.yonyou.dmscus.customer.service.pushRecord;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.RemotePushSinceTypeEnum;
import com.yonyou.dmscus.customer.constants.RemotePushStatusEnum;
import com.yonyou.dmscus.customer.dao.pushRecord.RemotePushRecordMapper;
import com.yonyou.dmscus.customer.entity.po.pushRecord.RemotePushRecordPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/16 18:12
 * @Version 1.0
 */
@Service
public class RemotePushRecordServiceImpl implements RemotePushRecordService {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RemotePushRecordMapper remotePushRecordMapper;

    @Override
    public void accidentClueLiteCrmPushRecord(String reqParams, String respParams, Integer acId, RemotePushSinceTypeEnum sinceType, RemotePushStatusEnum status) throws ServiceBizException {

        logger.info("accident clue info push record: {}", reqParams);
        RemotePushRecordPO record = this.queryRecord(acId, sinceType);
        if (ObjectUtils.isEmpty(record)){
            this.saveRecord(reqParams, respParams, acId, sinceType, status);
            return;
        }
        record.setReqParams(reqParams).setRespContent(respParams).setTaskStatus(status.getStatus()).setUpdatedAt(new Date());
        remotePushRecordMapper.updateById(record);
    }

    /**
     * 更新事故线索补偿结果
     * @param record
     * @param resp
     * @param status
     * @throws ServiceBizException
     */
    @Override
    public void updateAcCompensateRecord(RemotePushRecordPO record, String resp, RemotePushStatusEnum status) throws ServiceBizException {

        record.setRespContent(resp)
                .setTaskStatus(status.getStatus())
                .setLastRetryTime(new Date())
                .setRetryCount(record.getRetryCount() + 1)
                .setUpdatedAt(new Date());
        remotePushRecordMapper.updateById(record);
    }

    /**
     * 保存事故线索推送记录
     * @param reqParams
     * @param respParams
     * @param acId
     * @param sinceType
     * @param status
     * @throws ServiceBizException
     */
    private void saveRecord(String reqParams, String respParams, Integer acId, RemotePushSinceTypeEnum sinceType, RemotePushStatusEnum status) throws ServiceBizException{

        RemotePushRecordPO record = new RemotePushRecordPO();
        record.setBizNo(String.valueOf(acId))
                .setSinceType(sinceType.getSinceType())
                .setSubSinceType(sinceType.getSubSinceType())
                .setReqParams(reqParams)
                .setRespContent(respParams)
                .setTaskStatus(status.getStatus());

        logger.info("事故线索推送记录：{}", record);
        remotePushRecordMapper.insert(record);
    }

    /**
     * 查询事故线索推送记录
     * @param acId
     * @param sinceType
     * @return
     */
    private RemotePushRecordPO queryRecord(Integer acId, RemotePushSinceTypeEnum sinceType){

        List<RemotePushRecordPO> records = remotePushRecordMapper.selectList(new LambdaQueryWrapper<RemotePushRecordPO>()
                .eq(RemotePushRecordPO::getBizNo, acId + "")
                .notIn(RemotePushRecordPO::getTaskStatus, Collections.singletonList(RemotePushStatusEnum.STATUS_SUCCESS.getStatus()))
                .eq(RemotePushRecordPO::getSinceType, sinceType.getSinceType())
                .eq(RemotePushRecordPO::getSubSinceType, sinceType.getSubSinceType()));
        if (CollectionUtils.isEmpty(records)){
            return null;
        }
        return records.get(0);
    }
}
