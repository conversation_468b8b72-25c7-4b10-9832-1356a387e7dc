package com.yonyou.dmscus.customer.service.common.common;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.dmscloud.framework.util.bean.ApplicationContextHelper;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.configuration.CdpUrlProperties;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dto.CdpTokenPortraitDto;
import com.yonyou.dmscus.customer.feign.CdpFeign;
import com.yonyou.dmscus.customer.feign.dto.CdpTokenDto;
import com.yonyou.dmscus.customer.feign.response.CdpResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryContext;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.retry.support.RetrySynchronizationManager;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/10/20 16:10
 */
@Service
@Slf4j
public class CommonMethodServiceImpl implements CommonMethodService {

    @Autowired
    private CdpUrlProperties cdpUrlProperties;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private CdpFeign cdpFegin;


    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public CdpTokenPortraitDto getCdpToken(String target) {
        long timestamp = System.currentTimeMillis();
        CdpTokenDto tokenDto = new CdpTokenDto();
        tokenDto.setAppid(cdpUrlProperties.getAppid());
        tokenDto.setApp_secret(cdpUrlProperties.getAppSecret());
        tokenDto.setAccess_timestamp(timestamp);
        logger.info("getCdpToken tokenDto:{}", tokenDto);

        RLock lock = null;
        try {
            logger.info("getCdpToken getLock start");
            RBucket<String> bucket = redissonClient.getBucket(CommonConstants.CDP_TOKEN, new StringCodec(CommonConstants.CDP_VALUE_UTF8));
            CdpTokenPortraitDto tokenPortraitDto = getRedisToken(bucket);
            if (null != tokenPortraitDto) {
                logger.info("getCdpToken tokenPortraitDto isNotNull");
                return tokenPortraitDto;
            }

            lock = redissonClient.getLock("cdp:lock:getAppTokenId:" + cdpUrlProperties.getAppid());
            // 获取锁
            if (!lock.tryLock()) {
                logger.info("getCdpToken getLock fail");
                return new CdpTokenPortraitDto();
            }
            logger.info("getCdpToken getLock success");
            return getCdpTokenDto(tokenDto, bucket);
        } finally {
            if (lock != null && lock.isHeldByCurrentThread()) {
                logger.info("getCdpToken getLock unlock");
                lock.unlock();
            }
        }
    }

    /**
     * 获取缓存
     */
    private CdpTokenPortraitDto getRedisToken(RBucket<String> bucket) {
        String s = bucket.get();
        logger.info("getCdpToken s:{}", s);
        if (StringUtils.isBlank(s)) {
            logger.info("getCdpToken s isBlank");
            return null;
        }
        return JSONObject.toJavaObject(JSON.parseObject(s), CdpTokenPortraitDto.class);
    }

    /**
     * 从缓存中获取token，如果获取不到则去查询cdp
     */
    public CdpTokenPortraitDto getCdpTokenDto(CdpTokenDto tokenDto, RBucket<String> bucket) {
        logger.info("getCdpToken start");
        Objects.requireNonNull(tokenDto, "getCdpToken tokenDto isNull");
        CdpTokenPortraitDto tokenPortraitDto = null;
        try {
            tokenPortraitDto = getRedisToken(bucket);
            if (null != tokenPortraitDto) {
                logger.info("getCdpToken tokenPortraitDto isNotNull");
                return tokenPortraitDto;
            }
            logger.info("getCdpToken redis get fail");

            // 代理调用，否则 Retryable 不生效
            CommonMethodServiceImpl proxy = ApplicationContextHelper.getBeanByType(CommonMethodServiceImpl.class);
            tokenPortraitDto = proxy.get(tokenDto);

            // 尝试缓存
            setRedisToken(bucket, tokenPortraitDto);
            return tokenPortraitDto;
        } catch (Exception e) {
            logger.error("getCdpToken e:{}", e);
            return new CdpTokenPortraitDto();
        }
    }

    /**
     * 获取cdptoken，如果获取失败则尝试重新获取
     */
    @Retryable(value = {ServiceBizException.class}, maxAttempts = 2, backoff = @Backoff(delay = 500))
    public CdpTokenPortraitDto get(CdpTokenDto tokenDto) {
        RetryContext context = RetrySynchronizationManager.getContext();
        int retryCount = context.getRetryCount();
        logger.info("postForObject retryCount:{}", retryCount);
        // 获取cdptoken
        CdpResponse<JSONObject> cdpResult = null;
        try {
            cdpResult = cdpFegin.postForObject(tokenDto);
        } catch (Exception e) {
            throw new ServiceBizException("postForObject e:{}", e);
        }

        logger.info("postForObject cdpResult:{}", cdpResult);
        if (null == cdpResult || cdpResult.isFail()) {
            throw new ServiceBizException("postForObject is error:");
        }
        // 获取data
        JSONObject data = cdpResult.getData();
        if (null == data) {
            throw new ServiceBizException("postForObject data isnull");
        }
        return JSONObject.toJavaObject(data, CdpTokenPortraitDto.class);
    }

    /**
     * 写入缓存
     */
    private void setRedisToken(RBucket<String> bucket, CdpTokenPortraitDto tokenPortraitDto) {
        String tokenStr = objectToJson(tokenPortraitDto);
        bucket.set(tokenStr, CommonConstants.CDP_TOKEN_TIME, TimeUnit.SECONDS);
    }

    public static <T> String objectToJson(T obj) {
        String jsonStr = null;
        if (obj != null) {
            ObjectMapper mapper = new ObjectMapper();
            try {
                jsonStr = mapper.writeValueAsString(obj);
            } catch (IOException e) {
                throw new ServiceBizException("json 转换失败", e);
            }
        }
        return jsonStr;
    }


}
