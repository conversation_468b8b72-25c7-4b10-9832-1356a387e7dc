package com.yonyou.dmscus.customer.service.inviteRule;

import java.util.List;

import com.yonyou.dmscus.customer.dto.InvitationRuleVcdcParamsVo;
import com.yonyou.dmscus.customer.entity.dto.inviteRule.InviteRuleDTO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRulePO;
import com.yonyou.dmscus.customer.service.common.IBaseService;


/**
 * <p>
 * 邀约规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-20
 */
public interface InviteRuleService extends IBaseService<InviteRuleDTO> {

    int saveInviteRule(InviteRuleDTO dto);

    List<InviteRuleDTO> getInvitationRuleDlr(InviteRuleDTO inviteRuleDTO);

    int saveInviteRuleDlr(InviteRuleDTO dto);

    List<InviteRuleDTO> getInvitationRuleVcdc(InvitationRuleVcdcParamsVo vo);

    List<InviteRulePO> getRegularMaintainRule(String dealerCode);
}
