package com.yonyou.dmscus.customer.service.accidentClues;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.constants.clue.AccidentClueAppPushEnum;
import com.yonyou.dmscus.customer.dto.SmsPushDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.common.QueryUserPositionDTO;
import com.yonyou.dmscus.customer.entity.dto.common.UserPositionOutDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.AppPushDTO;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesPO;
import com.yonyou.dmscus.customer.feign.MidEndAuthCenterClient;
import com.yonyou.dmscus.customer.middleInterface.UserInfoOutDTO;
import com.yonyou.dmscus.customer.service.CommonService;
import com.yonyou.dmscus.customer.service.common.message.MessageSendService;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/11/1 10:59
 * @Version 1.0
 */
@Service
@EnableAsync
public class AppPushServiceImpl implements AppPushService{

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${accidentClue.appPush.templateCode:}")
    public String templateCode;
    @Value("${accidentClue.appPush.title:}")
    public String title;
    @Value("${accidentClue.appPush.detailPath:}")
    public String detailPath;
    @Value("${accidentClue.limitDate:}")
    private String limitDate;
    @Value("${accidentClue.sms.allot.templateCode:}")
    public String allotTemplateCode;
    @Value("${accidentClue.sms.follow.templateCode:}")
    public String followTemplateCode;
    @Value("${accidentClue.sms.timeOut.templateCode:}")
    public String timeOutTemplateCode;
    @Value("${accidentClue.sms.notCom.templateCode:}")
    public String notComTemplateCode;
    @Resource
    private MessageSendService messageSendService;
    @Resource
    private CommonService commonService;
    @Resource
    private MidEndAuthCenterClient midEndAuthCenterClient;
    @Resource
    WhitelistCheckService whitelistCheckService;


    /**
     * 分配提醒
     * @param appPushMap
     * @param companyId
     * @param pushEnum
     * @throws ServiceBizException
     */
    @Override
    @Async("acThreadPool")
    public void clueAllotAppPush(Map<Long, List<AccidentCluesPO>> appPushMap, Long companyId, AccidentClueAppPushEnum pushEnum) throws ServiceBizException {

        logger.info("push enum: {}, push info: {}", pushEnum, appPushMap);
        if (ObjectUtils.isEmpty(appPushMap)){
            logger.info("消息推送参数为空");
            return;
        }
        List<AccidentCluesPO> clueListAll = appPushMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        List<AccidentCluesPO> clueList = this.refreshClueList(clueListAll);
        List<Long> userIds = new ArrayList<>(appPushMap.keySet());
        Map<Long, Long> userIdInfoMap = this.getUserIdInfo(userIds, companyId);
        this.send(clueList, userIdInfoMap, pushEnum);
        this.sendSms(clueList, pushEnum);
    }

    /**
     * 定时任务提醒
     * @param pushList
     * @param companyId
     * @param pushEnum
     * @throws ServiceBizException
     */
    @Override
    public void followRemindAppPush(List<AccidentCluesPO> pushList, Long companyId, AccidentClueAppPushEnum pushEnum) throws ServiceBizException {

        if (CollectionUtils.isEmpty(pushList)){
            return;
        }
        pushList = this.refreshClueList(pushList);
        List<Long> userIds = pushList.stream().map(clue -> Long.parseLong(String.valueOf(clue.getFollowPeople()))).collect(Collectors.toList());
        Map<Long, Long> userIdInfo = this.getUserIdInfo(userIds, companyId);
        this.sendByJob(pushList, userIdInfo, pushEnum);
        this.sendSmsByJob(pushList, pushEnum);
    }

    /**
     * 过滤创建日期在limitDate之前且跟进人不为空的线索
     * @param clueList
     * @return
     * @throws ServiceBizException
     */
    private List<AccidentCluesPO> refreshClueList(List<AccidentCluesPO> clueList) throws ServiceBizException {

        List<AccidentCluesPO> result = new ArrayList<>(clueList.size());
        for (AccidentCluesPO po : clueList) {
            try{
                if (!ObjectUtils.isEmpty(po.getCreatedAt())
                        && po.getCreatedAt().after(DateUtils.parse(limitDate, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS))
                        && !ObjectUtils.isEmpty(po.getFollowPeople())){
                    result.add(po);
                }
            }catch (Exception e){
                logger.info("线索刷新失败！");
            }

        }
        return result;
    }

    /**
     * 消息推送
     * @param clueList
     * @param userIdInfoMap
     * @param pushEnum
     * @throws ServiceBizException
     */
    private void send(List<AccidentCluesPO> clueList, Map<Long, Long> userIdInfoMap, AccidentClueAppPushEnum pushEnum) throws ServiceBizException{

        if (CollectionUtils.isEmpty(clueList) || CollectionUtils.isEmpty(userIdInfoMap)){
            logger.info("线索列表为空或用户信息为空");
            return;
        }
        for (AccidentCluesPO clue : clueList) {
            Long empId = userIdInfoMap.get(Long.parseLong(String.valueOf(ObjectUtils.isEmpty(clue.getFollowPeople()) ? 0 : clue.getFollowPeople())));
            if (ObjectUtils.isEmpty(empId)){
                continue;
            }
            logger.info("线索ID：{} 推送消息通知", clue.getAcId());
            messageSendService.messageSendApp(this.getPushParams(clue, empId, pushEnum));
        }
    }

    /**
     * 消息推送
     * @param clueList
     * @param userIdInfoMap
     * @param pushEnum
     * @throws ServiceBizException
     */
    private void sendByJob(List<AccidentCluesPO> clueList, Map<Long, Long> userIdInfoMap, AccidentClueAppPushEnum pushEnum) throws ServiceBizException{

        if (CollectionUtils.isEmpty(clueList) || CollectionUtils.isEmpty(userIdInfoMap)){
            logger.info("线索列表为空或用户信息为空");
            return;
        }
        for (AccidentCluesPO clue : clueList) {
            if (!whitelistCheckService.checkWhitelist(clue.getDealerCode())){
                logger.info("非白名单经销商线索不推消息");
                continue;
            }
            Long empId = userIdInfoMap.get(Long.parseLong(String.valueOf(ObjectUtils.isEmpty(clue.getFollowPeople()) ? 0 : clue.getFollowPeople())));
            if (ObjectUtils.isEmpty(empId)){
                continue;
            }
            logger.info("线索ID：{} 推送消息通知", clue.getAcId());
            messageSendService.messageSendApp(this.getPushParams(clue, empId, pushEnum));
        }
    }

    /**
     * 推送内容
     * @param clue
     * @param pushEnum
     * @return
     */
    private String getContent(AccidentCluesPO clue, AccidentClueAppPushEnum pushEnum){

        switch (pushEnum.getType()){
            case CommonConstants.PUSH_TYPE_ALLOT:
                return String.format(pushEnum.getMsg(), clue.getLicense());
            case CommonConstants.PUSH_TYPE_TIMEOUT_REMIND:
                return String.format(pushEnum.getMsg(), clue.getLicense(), DateUtils.formatDate(clue.getReportDate(), DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
            case CommonConstants.PUSH_TYPE_BEFORE_FOLLOW:
                return String.format(pushEnum.getMsg(), DateUtils.formatDate(clue.getNextFollowDate(), DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS), clue.getLicense());
            case CommonConstants.PUSH_TYPE_NOT_COMING:
                return String.format(pushEnum.getMsg(), clue.getLicense(), DateUtils.formatDate(clue.getAppointmentIntoDate(), DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS), clue.getLicense());
            default:
                return "";
        }
    }

    /**
     * 获取员工信息
     * @param userIds
     * @param companyId
     * @return
     * @throws ServiceBizException
     */
    private Map<Long,Long> getUserIdInfo(List<Long> userIds, Long companyId) throws ServiceBizException{

        QueryUserPositionDTO dto = new QueryUserPositionDTO();
        dto.setUserIds(userIds);
        dto.setCompanyIds(ObjectUtils.isEmpty(companyId) ? null : Collections.singletonList(companyId));
        List<UserPositionOutDTO> empInfoList = messageSendService.getUserPositionInfoList(dto);
        logger.info("获取员工empInfoList===>>{}", empInfoList);

        return empInfoList.stream().collect(Collectors.toMap(UserPositionOutDTO::getUserId, UserPositionOutDTO::getEmpId));
    }

    /**
     * 获取推送参数
     * @param clue
     * @param empId
     * @param pushEnum
     * @return
     */
    private AppPushDTO getPushParams(AccidentCluesPO clue, Long empId, AccidentClueAppPushEnum pushEnum){

        AppPushDTO pushParams = new AppPushDTO();
        pushParams.setTitle(title);
        pushParams.setTargetCodes(Collections.singletonList("E" + empId).toArray(new String[0]));
        pushParams.setContent(this.getContent(clue, pushEnum));

        Map<String, Map<String, String>> ext = new HashMap<>(CommonConstants.NUM_5);
        Map<String, String> params = new HashMap<>(CommonConstants.NUM_5);
        params.put("accidentDetailsUrl", detailPath + clue.getAcId());
        Map<String, String> template = new HashMap<>(CommonConstants.NUM_5);
        template.put("templateCode", templateCode);
        ext.put("params",params);
        ext.put("template",template);
        pushParams.setJson(JSONObject.toJSONString(ext));
        pushParams.setExt(ext);

        logger.info("********************push pushParams ===>> {}", pushParams);
        return pushParams;
    }

    /**
     * 发送短信
     * @param clueList
     * @param pushEnum
     * @throws ServiceBizException
     */
    private void sendSms(List<AccidentCluesPO> clueList, AccidentClueAppPushEnum pushEnum) throws ServiceBizException{

        List<UserInfoOutDTO> userInfoList = this.queryUserInfoList(clueList);
        if (CollectionUtils.isEmpty(userInfoList)){
            return;
        }

        Map<Long, List<UserInfoOutDTO>> userInfoListMap = userInfoList.stream().collect(Collectors.groupingBy(UserInfoOutDTO::getUserId));
        for (AccidentCluesPO clue : clueList) {
            List<UserInfoOutDTO> userInfo = userInfoListMap.get(Long.parseLong(String.valueOf(clue.getFollowPeople())));
            if (ObjectUtils.isEmpty(userInfo)) {
                logger.info("未找到对应跟进人员信息");
                continue;
            }
            SmsPushDTO smsPushDTO = new SmsPushDTO();
            smsPushDTO.setMobiles(userInfo.get(0).getPhone());
            this.buildSmsPushDto(clue, smsPushDTO, pushEnum);
            commonService.sendMessage(smsPushDTO);
            logger.info("事故线索消息通知发送短信结束");
        }
    }

    /**
     * 发送短信
     * @param clueList
     * @param pushEnum
     * @throws ServiceBizException
     */
    private void sendSmsByJob(List<AccidentCluesPO> clueList, AccidentClueAppPushEnum pushEnum) throws ServiceBizException{

        List<UserInfoOutDTO> userInfoList = this.queryUserInfoList(clueList);
        if (CollectionUtils.isEmpty(userInfoList)){
            return;
        }

        Map<Long, List<UserInfoOutDTO>> userInfoListMap = userInfoList.stream().collect(Collectors.groupingBy(UserInfoOutDTO::getUserId));
        for (AccidentCluesPO clue : clueList) {
            if (!whitelistCheckService.checkWhitelist(clue.getDealerCode())){
                logger.info("非白名单经销商线索不推消息");
                continue;
            }
            List<UserInfoOutDTO> userInfo = userInfoListMap.get(Long.parseLong(String.valueOf(clue.getFollowPeople())));
            if (ObjectUtils.isEmpty(userInfo)) {
                logger.info("未找到对应跟进人员信息");
                continue;
            }
            SmsPushDTO smsPushDTO = new SmsPushDTO();
            smsPushDTO.setMobiles(userInfo.get(0).getPhone());
            this.buildSmsPushDto(clue, smsPushDTO, pushEnum);
            commonService.sendMessage(smsPushDTO);
            logger.info("事故线索消息通知发送短信结束");
        }
    }

    /**
     * 批量获取用户信息
     * @param clueList
     * @return
     * @throws ServiceBizException
     */
    private List<UserInfoOutDTO> queryUserInfoList(List<AccidentCluesPO> clueList) throws ServiceBizException{

        List<String> userIds = clueList.stream().map(clue -> String.valueOf(clue.getFollowPeople())).collect(Collectors.toList());
        ResponseDTO<List<UserInfoOutDTO>> response = midEndAuthCenterClient.queryUserInfoList(userIds);
        if (ObjectUtils.isEmpty(response) || CollectionUtils.isEmpty(response.getData())){
            logger.info("用户信息获取结果为空！");
            return new ArrayList<>();
        }
        logger.info("获取用户信息结果列表===>>{}", response.getData());
        return response.getData();
    }

    /**
     * 构建短信推送参数
     * @param clue
     * @param smsPushDTO
     * @param pushEnum
     */
    private void buildSmsPushDto(AccidentCluesPO clue, SmsPushDTO smsPushDTO, AccidentClueAppPushEnum pushEnum) {

        Map<String, String> extMap = new HashMap<>(CommonConstants.NUM_5);
        switch (pushEnum.getType()) {
            case CommonConstants.PUSH_TYPE_ALLOT:
                extMap.put("license", clue.getLicense());
                smsPushDTO.setParamMap(extMap);
                smsPushDTO.setTemplateId(allotTemplateCode);
                break;
            case CommonConstants.PUSH_TYPE_TIMEOUT_REMIND:
                extMap.put("license", clue.getLicense());
                extMap.put("reportDate", DateUtils.formatDate(clue.getReportDate(), DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
                smsPushDTO.setParamMap(extMap);
                smsPushDTO.setTemplateId(timeOutTemplateCode);
                break;
            case CommonConstants.PUSH_TYPE_BEFORE_FOLLOW:
                extMap.put("license", clue.getLicense());
                extMap.put("nextFollowDate", DateUtils.formatDate(clue.getNextFollowDate(), DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
                smsPushDTO.setParamMap(extMap);
                smsPushDTO.setTemplateId(followTemplateCode);
                break;
            case CommonConstants.PUSH_TYPE_NOT_COMING:
                extMap.put("license", clue.getLicense());
                extMap.put("appointmentIntoDate", DateUtils.formatDate(clue.getAppointmentIntoDate(), DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
                smsPushDTO.setParamMap(extMap);
                smsPushDTO.setTemplateId(notComTemplateCode);
                break;
            default:
                break;
        }

        logger.info("短信推送参数===>>{}", smsPushDTO);
    }
}
