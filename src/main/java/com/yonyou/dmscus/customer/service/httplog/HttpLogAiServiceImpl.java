package com.yonyou.dmscus.customer.service.httplog;

import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscus.customer.dao.httpLog.HttpLogAiMapper;
import com.yonyou.dmscus.customer.entity.po.httpLog.HttpLogAiPO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/9/24 0024
 */
@Service
public class HttpLogAiServiceImpl implements  HttpLogAiService{

    @Resource
    HttpLogAiMapper httpLogAiMapper;
    /**
     * 保存
     * @param describe 接口描述
     * @param requestUrl 请求url
     * @param requestParam 请求参数
     * @param requestType 请求类型
     * @param responseCode 响应代码
     * @param responseMsg 响应结果
     */
    @Override
    public void saveHttpLog(String describe,String requestUrl,String requestParam,String requestType,String responseCode,String responseMsg,String SessionId) {
        HttpLogAiPO httpLogPO=new HttpLogAiPO();
        httpLogPO.setDescription(describe);
        httpLogPO.setRequestUrl(requestUrl);
        httpLogPO.setRequestParam(requestParam);
        httpLogPO.setRequestType(requestType);
        httpLogPO.setResponseCode(responseCode);
        httpLogPO.setResponseMsg(responseMsg);
        httpLogPO.setSessionId(SessionId);
        LoginInfoDto loginInfoDto=FrameworkUtil.getLoginInfo();
        if(loginInfoDto!=null){
            httpLogPO.setDealerCode(loginInfoDto.getOwnerCode());
        }
        httpLogAiMapper.insert(httpLogPO);
    }
}
