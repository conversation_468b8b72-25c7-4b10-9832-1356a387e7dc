package com.yonyou.dmscus.customer.service.common.businessPlatform;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yonyou.dmscus.customer.dto.CustomerInfoCenterDTO;
import com.yonyou.dmscus.customer.dto.CustomerInfoListReturnDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.*;
import com.yonyou.dmscus.customer.service.common.businessPlatform.dto.VehicleOwnerDetailByIdListDTO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.dto.VehicleOwnerSelectByIdListDTO;

import java.util.List;
import java.util.Map;

public interface BusinessPlatformService {

    // 中台大小区code 查询经销商
    List<String> getDealercodes(String areaId,String largeAreaId, String dealerCode,String dealerName);
    // 中台大小区code 查询经销商
    List<CompanyDetailDTO> getDealer(String areaId, String largeAreaId, String dealerCode,String dealerName);

    //中台车辆信息查询
    TmVehicleDTO getVehicleByVIN(String vin);

    //查询车辆
    IPage<OwnerVehicleVO> getVehicle(PageRequestDTO<OwnerVehicleDTO> dto);

    List<CustomerInfoListReturnDTO> getOneId(List<CustomerInfoCenterDTO> list);

    IsExistByCodeDTO checkDealerCodeExist(List<String> dealerCodes);

    CheckTmVehicleDTO checkVin(List<String> vins);


    /**
     * 中台获取车主车辆
     * @param vin
     * @return
     */
    VehicleDTO listOwnerVehiclePage(String vin);

    List<InviteVulDTO> getVulInfo(String vin);

    UserInfoNewDto getRoleList();
}