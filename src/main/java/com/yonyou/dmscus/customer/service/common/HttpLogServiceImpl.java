package com.yonyou.dmscus.customer.service.common;

import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscus.customer.bean.entity.HttpLogPO;
import com.yonyou.dmscus.customer.dao.common.HttpLogMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/9/24 0024
 */
@Service
public class HttpLogServiceImpl implements  HttpLogService{

    @Resource
    HttpLogMapper httpLogMapper;
    /**
     * 保存
     * @param describe 接口描述
     * @param requestUrl 请求url
     * @param requestParam 请求参数
     * @param requestType 请求类型
     * @param responseCode 响应代码
     * @param responseMsg 响应结果
     */
    @Override
    public void saveHttpLog(String describe,String requestUrl,String requestParam,String requestType,String responseCode,String responseMsg) {
        HttpLogPO httpLogPO=new HttpLogPO();
        httpLogPO.setDescription(describe);
        httpLogPO.setRequestUrl(requestUrl);
        httpLogPO.setRequestParam(requestParam);
        httpLogPO.setRequestType(requestType);
        httpLogPO.setResponseCode(responseCode);
        httpLogPO.setResponseMsg(responseMsg);
        LoginInfoDto loginInfoDto=FrameworkUtil.getLoginInfo();
        if(loginInfoDto!=null){
            httpLogPO.setDealerCode(loginInfoDto.getOwnerCode());
        }
        httpLogMapper.insert(httpLogPO);
    }
}
