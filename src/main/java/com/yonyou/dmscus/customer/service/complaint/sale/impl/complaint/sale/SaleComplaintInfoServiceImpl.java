package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.domain.framework.RestResultResponse;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.constants.WorkOrderNatureEnum;
import com.yonyou.dmscus.customer.constants.WorkOrderStatusEnum;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintAttachmentMapper;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintMaintainMapper;
import com.yonyou.dmscus.customer.dto.ComplaintInfMoreVo;
import com.yonyou.dmscus.customer.dto.GetModelNameDTO;
import com.yonyou.dmscus.customer.dto.OrgDetailDTO;
import com.yonyou.dmscus.customer.dto.*;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.*;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintCustomFieldUseDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintTypeDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sendComplaintData.CloseComplaintDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sendComplaintData.FollowInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sendComplaintData.NewComplaintDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.v51dk.V51dkComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfMorePO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintExtPO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoPO;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintInfoMapper;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintMaintainPO;
import com.yonyou.dmscus.customer.entity.vo.TmDateHolidayVo;
import com.yonyou.dmscus.customer.entity.vo.V51dkComplaintInfMoreVO;
import com.yonyou.dmscus.customer.feign.DictServiceClient;
import com.yonyou.dmscus.customer.feign.MidUserOrganizationClient;
import com.yonyou.dmscus.customer.feign.DmscloudServiceClient;
import com.yonyou.dmscus.customer.middleInterface.CompanyDetailByCodeDTO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintCustomTopUseService;
import com.yonyou.dmscus.customer.service.complaint.ComplaintInfoService;
import com.yonyou.dmscus.customer.service.complaint.impl.complaint.ComplaintCommonServiceImpl;
import com.yonyou.dmscus.customer.service.complaint.impl.complaint.ComplaintDealerCcmRefServiceImpl;
import com.yonyou.dmscus.customer.service.complaint.impl.complaint.ComplaintInfoServiceImpl;
import com.yonyou.dmscus.customer.service.complaint.sale.*;

import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoDTO;

import com.yonyou.dmscus.customer.service.impl.CommonServiceImpl;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import com.yonyou.dmscus.customer.util.common.HttpClient;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 销售客户投诉信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-10
 */
@Service
public class SaleComplaintInfoServiceImpl implements SaleComplaintInfoService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    SaleComplaintInfoMapper saleComplaintInfoMapper;

    @Resource
    SaleComplaintMaintainMapper saleComplaintMaintainMapper;

    @Autowired
    ComplaintDealerCcmRefServiceImpl complaintDealerCcmRefService;

    @Autowired
    SaleComplaintFollowService saleComplaintFollowService;
    @Resource
    SaleComplaintAttachmentMapper saleComplaintAttachmentMapper;

    @Autowired
    ComplaintCustomTopUseService complaintCustomTopUseService;

    @Autowired
    SaleComplaintCustomFieldUseService salecomplaintCustomFieldUseService;

    @Autowired
    ComplaintInfoServiceImpl complaintInfoServiceImpll;
    @Autowired
    CommonServiceImpl commonServiceImpl;
    @Autowired
    ComplaintCommonServiceImpl complaintCommonServiceImpl;
    @Autowired
    SaleComplaintTypeService saleComplaintTypeService;

    @Autowired
    SaleComplaintExtService saleComplaintExtService;

    @Value("${wedo.baseUrl}")
    private String baseUrl;

    @Value("${wedo.apiuid}")
    private String apiuid;

    @Value("${wedo.apipwd}")
    private String apipwd;

    @Resource
    private MidUrlProperties midUrlProperties;

    @Autowired
    ComplaintInfoService complaintInfoService;

    @Autowired
    private MidUserOrganizationClient midUserOrganizationClient;

    @Autowired
    private DmscloudServiceClient dmscloudServiceClient;

    @Autowired
    private DictServiceClient dictServiceClient;

    private DateTimeFormatter shortFmt = DateTimeFormatter.ofPattern(DateUtils.PATTERN_YYYY_MM_DD);


    /**
     * 分页查询对应数据
     *
     * @param page                 分页对象
     * @param saleComplaintInfoDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<SaleComplaintInfoDTO> selectPageBysql(Page page, SaleComplaintInfoDTO saleComplaintInfoDTO) {
        if (saleComplaintInfoDTO == null) {
            saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        }
        SaleComplaintInfoPO saleComplaintInfoPO = saleComplaintInfoDTO.transDtoToPo(SaleComplaintInfoPO.class);

        List<SaleComplaintInfoPO> list = saleComplaintInfoMapper.selectPageBySql(page, saleComplaintInfoPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<SaleComplaintInfoDTO> result = list.stream().map(m -> m.transPoToDto(SaleComplaintInfoDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param saleComplaintInfoDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<SaleComplaintInfoDTO> selectListBySql(SaleComplaintInfoDTO saleComplaintInfoDTO) {
        if (saleComplaintInfoDTO == null) {
            saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        }
        SaleComplaintInfoPO saleComplaintInfoPO = saleComplaintInfoDTO.transDtoToPo(SaleComplaintInfoPO.class);
        List<SaleComplaintInfoPO> list = saleComplaintInfoMapper.selectListBySql(saleComplaintInfoPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(SaleComplaintInfoDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public SaleComplaintInfoDTO getById(Long id) {
        SaleComplaintInfoPO saleComplaintInfoPO = saleComplaintInfoMapper.selectById(id);
        if (saleComplaintInfoPO != null) {
            return saleComplaintInfoPO.transPoToDto(SaleComplaintInfoDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param saleComplaintInfoDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(SaleComplaintInfoDTO saleComplaintInfoDTO) {
        //对对象进行赋值操作
        SaleComplaintInfoPO saleComplaintInfoPO = saleComplaintInfoDTO.transDtoToPo(SaleComplaintInfoPO.class);
        //执行插入
        int row = saleComplaintInfoMapper.insert(saleComplaintInfoPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                   主键ID
     * @param saleComplaintInfoDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, SaleComplaintInfoDTO saleComplaintInfoDTO) {
        SaleComplaintInfoPO saleComplaintInfoPO = saleComplaintInfoMapper.selectById(id);
        //对对象进行赋值操作
        saleComplaintInfoDTO.transDtoToPo(saleComplaintInfoPO);
        //执行更新
        int row = saleComplaintInfoMapper.updateById(saleComplaintInfoPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = saleComplaintInfoMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    @Override
    public int insertComplaint(ComplaintmoreDTO complaintmoreDTO) {
        SaleComplaintInfoDTO saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        saleComplaintInfoDTO.setDealerCode(FrameworkUtil.getLoginInfo().getOwnerCode());
        CompanyDetailByCodeDTO companyInfo = complaintDealerCcmRefService.getCompanyInfo(FrameworkUtil.getLoginInfo().getOwnerCode());
        saleComplaintInfoDTO.setRegion(companyInfo.getSaleBigAreaName());
        SelectSmallManagerDTO selectSmallManagerDTO = new SelectSmallManagerDTO();
        selectSmallManagerDTO.setOrgId(companyInfo.getSaleSmallAreaId().intValue());
        List<String> roleCode = new ArrayList<>();
        roleCode.add("XSQYJL");
        selectSmallManagerDTO.setRoleCode(roleCode);
        List<SmallManagerDataDTO> smallManagerList = complaintDealerCcmRefService.getSmallManager(selectSmallManagerDTO);
        saleComplaintInfoDTO.setWorkOrderNature(complaintmoreDTO.getWorkOrderNature());

        saleComplaintInfoDTO.setWorkOrderClassification(83621001);
        if (smallManagerList.size() != 0) {
            saleComplaintInfoDTO.setRegionManager(smallManagerList.get(0).getEmployeeName());
        }
        saleComplaintInfoDTO.setBloc(companyInfo.getGroupCompanyName());
        saleComplaintInfoDTO.setRegionId(companyInfo.getSaleBigAreaId());
        saleComplaintInfoDTO.setRegionManagerId(companyInfo.getSaleSmallAreaId());
        saleComplaintInfoDTO.setDealerName(companyInfo.getCompanyNameCn());
        saleComplaintInfoDTO.setBlocId(Long.valueOf(companyInfo.getGroupCompanyId()));
        saleComplaintInfoDTO.setComplaintId(complaintmoreDTO.getComplaintId());
        saleComplaintInfoDTO.setCallName(complaintmoreDTO.getCallName());
        saleComplaintInfoDTO.setCallTel(complaintmoreDTO.getCallTel());
        saleComplaintInfoDTO.setSex(complaintmoreDTO.getSex());
        saleComplaintInfoDTO.setCategory1(complaintmoreDTO.getCategory1());
        saleComplaintInfoDTO.setCategory2(complaintmoreDTO.getCategory2());
        saleComplaintInfoDTO.setCategory3(complaintmoreDTO.getCategory3());
        saleComplaintInfoDTO.setCallTime(new Date());
        saleComplaintInfoDTO.setSource(complaintmoreDTO.getSource());
        saleComplaintInfoDTO.setType(complaintmoreDTO.getSaleType());
        saleComplaintInfoDTO.setReport(false);
        saleComplaintInfoDTO.setIsCloseCase(10041002);
        saleComplaintInfoDTO.setImportanceLevel(complaintmoreDTO.getImportanceLevel());
        if (!StringUtils.isNullOrEmpty(complaintmoreDTO.getDepartment())) {
            StringBuffer department = new StringBuffer();
            List departmentList = complaintmoreDTO.getDepartment();
            for (int i = 0; i < departmentList.size(); i++) {
                if (i == departmentList.size() - 1) {
                    department.append(departmentList.get(i));
                } else {
                    department.append(departmentList.get(i) + ",");
                }
            }
            saleComplaintInfoDTO.setDepartment(department.toString());

        }

        StringBuffer part = new StringBuffer();
        List partList = complaintmoreDTO.getPart();
        for (int i = 0; i < partList.size(); i++) {
            if (i == partList.size() - 1) {
                part.append(partList.get(i));
            } else {
                part.append(partList.get(i) + ",");
            }
        }
        saleComplaintInfoDTO.setPart(part.toString());
        StringBuffer subdivisionPart = new StringBuffer();
        List subdivisionPartlist = complaintmoreDTO.getSubdivisionPart();
        for (int i = 0; i < subdivisionPartlist.size(); i++) {
            if (i == subdivisionPartlist.size() - 1) {
                subdivisionPart.append(subdivisionPartlist.get(i));
            } else {
                subdivisionPart.append(subdivisionPartlist.get(i) + ",");
            }
        }
        saleComplaintInfoDTO.setSubdivisionPart(subdivisionPart.toString());
        saleComplaintInfoDTO.setDataSources(82101001);
        //车辆信息赋值
        ComplaintInfoDTO complaintInfoDto1 = complaintmoreDTO.getFormPanelData1();
        saleComplaintInfoDTO.setLicensePlateNum(complaintInfoDto1.getLicensePlateNum());
        saleComplaintInfoDTO.setVin(complaintInfoDto1.getVin());
        saleComplaintInfoDTO.setName(complaintInfoDto1.getName());
        saleComplaintInfoDTO.setModel(complaintInfoDto1.getModel());
        saleComplaintInfoDTO.setModelYear(complaintInfoDto1.getModelYear());
        if (!StringUtils.isNullOrEmpty(complaintInfoDto1.getBuyTime())) {
            saleComplaintInfoDTO.setBuyTime(complaintInfoDto1.getBuyTime());
        }
        saleComplaintInfoDTO.setBuyDealerName(complaintInfoDto1.getBuyDealerName());
        if (!StringUtils.isNullOrEmpty(complaintInfoDto1.getMileage())) {
            saleComplaintInfoDTO.setMileage(complaintInfoDto1.getMileage());
        } else {
            saleComplaintInfoDTO.setMileage(0);
        }

        saleComplaintInfoDTO.setOwnerAddress(complaintmoreDTO.getOwnerCode());
        saleComplaintInfoDTO.setBuyDealerCode(complaintInfoDto1.getBuyDealerCode());
        if (!StringUtils.isNullOrEmpty(complaintInfoDto1.getBuyDealerCode())) {
            CompanyDetailByCodeDTO companyInfoBuy = complaintDealerCcmRefService.getCompanyInfo(complaintInfoDto1.getBuyDealerCode());
            saleComplaintInfoDTO.setBuyRegion(companyInfoBuy.getSaleBigAreaName());
            selectSmallManagerDTO = new SelectSmallManagerDTO();
            selectSmallManagerDTO.setOrgId(companyInfoBuy.getSaleSmallAreaId().intValue());
            selectSmallManagerDTO.setRoleCode(roleCode);
            smallManagerList = complaintDealerCcmRefService.getSmallManager(selectSmallManagerDTO);
            if (smallManagerList.size() != 0) {
                saleComplaintInfoDTO.setBuyRegionManager(smallManagerList.get(0).getEmployeeName());
            }
            saleComplaintInfoDTO.setBuyBloc(companyInfoBuy.getGroupCompanyName());
            saleComplaintInfoDTO.setBuyRegionId(companyInfoBuy.getSaleBigAreaId());
            saleComplaintInfoDTO.setBuyRegionManagerId(companyInfoBuy.getSaleSmallAreaId());
        }


        //投诉信息赋值
        ComplaintmoreDTO complaintInfoDto2 = complaintmoreDTO.getFormPanelData2();
        saleComplaintInfoDTO.setSubject(complaintInfoDto2.getSubject());
        saleComplaintInfoDTO.setProblem(complaintInfoDto2.getProblem());
        saleComplaintInfoDTO.setProblemInfo(complaintInfoDto2.getProblemInfo());
        StringBuffer cusRequirement = new StringBuffer();
        List cusRequirementList = complaintInfoDto2.getCusRequirement();
        for (int i = 0; i < cusRequirementList.size(); i++) {
            if (i == cusRequirementList.size() - 1) {
                cusRequirement.append(cusRequirementList.get(i));
            } else {
                cusRequirement.append(cusRequirementList.get(i) + ",");
            }
        }
        saleComplaintInfoDTO.setCusRequirement(cusRequirement.toString());
        ComplaintFollowDTO complaintFollowDto1 = complaintmoreDTO.getFormPanelData3();
        if (complaintFollowDto1.getFollowContent() != null) {
            saleComplaintInfoDTO.setDealerFisrtReplyTime(new Date());
            saleComplaintInfoDTO.setWorkOrderStatus(82451002);
        } else {
            saleComplaintInfoDTO.setWorkOrderStatus(82451001);
        }
        if (complaintmoreDTO.getWorkOrderNature() == 83611002) {
            saleComplaintInfoDTO.setCloseCaseStatus(82441004);
        }
        insert(saleComplaintInfoDTO);

        //经销商新建客诉单下发到400
        //DTO赋值
        NewComplaintDTO newComplaintDTO = this.setNewComplaintDto(saleComplaintInfoDTO);
        String Json = JSONObject.toJSONString(newComplaintDTO);
        String path = "/api/services/app/Order/DealerOrder";
        String http = baseUrl + path;
        String noncestr1 = UUID.randomUUID().toString().replace("-", "");
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String sgin = commonServiceImpl.GetCheckSign(path, apiuid, apipwd, noncestr1, timestamp);
        String describe = "下发给400新建销售客诉单";
        HttpClient.requestInterfaceJsonCus(describe, Json, http, sgin, apiuid, noncestr1, timestamp);
        //跟进信息赋值
        List<SaleComplaintInfoPO> queryid = saleComplaintInfoMapper.queryid(saleComplaintInfoDTO);
        long id = queryid.get(0).getId();
        //跟进信息赋值
        if (complaintFollowDto1.getFollowContent() != null) {
            SaleComplaintFollowDTO saleComplaintFollowDTO = new SaleComplaintFollowDTO();
            saleComplaintFollowDTO.setFollowContent(complaintFollowDto1.getFollowContent());
            saleComplaintFollowDTO.setComplaintInfoId(id);
            saleComplaintFollowDTO.setDealerNotPublish(false);
            saleComplaintFollowDTO.setCcmNotPublish(true);
            saleComplaintFollowDTO.setFollowTime(new Date());
            saleComplaintFollowDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
            saleComplaintFollowDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
            saleComplaintFollowService.insert(saleComplaintFollowDTO);
            IssuedUpdataData(saleComplaintFollowDTO, FrameworkUtil.getLoginInfo().getOwnerCode(), "跟进中");
        }
        AttcahmentUpdateDto attcahmentUpdateDto = new AttcahmentUpdateDto();
        attcahmentUpdateDto.setId(id);
        attcahmentUpdateDto.setNo(saleComplaintInfoDTO.getComplaintId());
        saleComplaintAttachmentMapper.updateAttachment(attcahmentUpdateDto);
        return 1;
    }

    /**
     * 给NewComPlaintDTO赋值
     *
     * @param saleComplaintInfoDTO
     * @return
     */
    public NewComplaintDTO setNewComplaintDto(SaleComplaintInfoDTO saleComplaintInfoDTO) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        NewComplaintDTO newComplaintDTO = new NewComplaintDTO();
        newComplaintDTO.setComplainID(saleComplaintInfoDTO.getComplaintId());
        //投诉类型赋值
        switch (saleComplaintInfoDTO.getType()) {
            case 10:
                newComplaintDTO.setComplainType("车辆配置相关");
                break;
            case 11:
                newComplaintDTO.setComplainType("销售人员专业水准");
                break;
            case 12:
                newComplaintDTO.setComplainType("交车时间");
                break;
            case 13:
                newComplaintDTO.setComplainType("手续办理");
                break;
            case 14:
                newComplaintDTO.setComplainType("交车时状况");
                break;
            case 15:
                newComplaintDTO.setComplainType("客户退订");
                break;
            case 16:
                newComplaintDTO.setComplainType("销售价格");
                break;
            case 17:
                newComplaintDTO.setComplainType("大客户相关");
                break;
            case 18:
                newComplaintDTO.setComplainType("二手车相关");
                break;
            case 19:
                newComplaintDTO.setComplainType("促销政策或活动");
                break;
            case 20:
                newComplaintDTO.setComplainType("金融相关");
                break;
            case 21:
                newComplaintDTO.setComplainType("保险相关");
                break;
            case 22:
                newComplaintDTO.setComplainType("上牌相关");
                break;
            case 23:
                newComplaintDTO.setComplainType("捆绑销售");
                break;
            case 24:
                newComplaintDTO.setComplainType("销售承若兑现");
                break;
            case 25:
                newComplaintDTO.setComplainType("销售服务");
                break;
            case 26:
                newComplaintDTO.setComplainType("其他");
                break;
        }
        //投诉来源赋值
        newComplaintDTO.setSource(complaintCommonServiceImpl.getSource(saleComplaintInfoDTO.getSource()));
        newComplaintDTO.setComplainDT(sf.format(saleComplaintInfoDTO.getCallTime()));
        newComplaintDTO.setComplainSource("经销商自建");
        //赋值工单状态
        newComplaintDTO.setJobOrderStatus(0);
        newComplaintDTO.setReplyContacts(saleComplaintInfoDTO.getCallName());
        newComplaintDTO.setReplyMobile(saleComplaintInfoDTO.getCallTel());
        newComplaintDTO.setRelatedLicense(saleComplaintInfoDTO.getLicensePlateNum());
        newComplaintDTO.setRelatedVin(saleComplaintInfoDTO.getVin());
        if (!StringUtils.isNullOrEmpty(saleComplaintInfoDTO.getModel())) {
            Integer id = Integer.valueOf(saleComplaintInfoDTO.getModel());
            GetModelNameDTO list = commonServiceImpl.getModelNameById(id);
            newComplaintDTO.setRelatedModel(list.getModelName());
        }
        newComplaintDTO.setMileage(saleComplaintInfoDTO.getMileage());
        newComplaintDTO.setRelatedVersion(saleComplaintInfoDTO.getModelYear());
        if (!StringUtils.isNullOrEmpty(saleComplaintInfoDTO.getBuyTime())) {
            newComplaintDTO.setBuyDT(sf.format(saleComplaintInfoDTO.getBuyTime()));
        }
        newComplaintDTO.setBuy_DlrCode(saleComplaintInfoDTO.getBuyDealerCode());
        newComplaintDTO.setBuy_DlrName(saleComplaintInfoDTO.getBuyDealerName());
        newComplaintDTO.setProc_DlrCode(saleComplaintInfoDTO.getDealerCode());
        newComplaintDTO.setProc_DlrName(saleComplaintInfoDTO.getDealerName());
        newComplaintDTO.setSubject(saleComplaintInfoDTO.getSubject());
        newComplaintDTO.setProblemDescr(saleComplaintInfoDTO.getProblem());
        newComplaintDTO.setCategoryProplem(complaintCommonServiceImpl.getProblemInf(saleComplaintInfoDTO.getProblemInfo()));
        newComplaintDTO.setCategory_1(complaintCommonServiceImpl.getCategory(saleComplaintInfoDTO.getCategory1()));
        newComplaintDTO.setCategory_2(complaintCommonServiceImpl.getCategory(saleComplaintInfoDTO.getCategory2()));
        newComplaintDTO.setCategory_3(complaintCommonServiceImpl.getCategory(saleComplaintInfoDTO.getCategory3()));
        newComplaintDTO.setCustomerClaims(complaintCommonServiceImpl.getCusRequirement(saleComplaintInfoDTO.getCusRequirement()));
        newComplaintDTO.setNature("销售");
        return newComplaintDTO;
    }

    /**
     * 下发给400跟进信息
     */
    @Override
    public void IssuedUpdataData(SaleComplaintFollowDTO saleComplaintFollowDTO, String CreaterOrg, String JobOrderStatus) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        UserRoleDTO userRoleDTO = commonServiceImpl.getUserRoleDTO();
        FollowInfoDTO followInfoDTO = new FollowInfoDTO();
        StringBuffer follower = new StringBuffer();
        List<UserRoleDataDTO> UserRoleDataDTO = userRoleDTO.getRoleList();
        if (UserRoleDataDTO != null && UserRoleDataDTO.size() != 0) {
            for (int i = 0; i < UserRoleDataDTO.size(); i++) {
                if (i == UserRoleDataDTO.size() - 1) {
                    follower.append(UserRoleDataDTO.get(i).getRoleName());
                } else {
                    follower.append(UserRoleDataDTO.get(i).getRoleName() + ",");
                }
            }
        }

        SaleComplaintInfoDTO saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        saleComplaintInfoDTO.setId(saleComplaintFollowDTO.getComplaintInfoId());
        List<SaleComplaintInfoPO> queryComplaintData = saleComplaintInfoMapper.queryComplaintData(saleComplaintInfoDTO);
        String id = queryComplaintData.get(0).getComplaintId();
        String source = "";
        if (!StringUtils.isNullOrEmpty(queryComplaintData.get(0).getDataSources())) {
            if (queryComplaintData.get(0).getDataSources() == 82101001) {
                source = "经销商自建";
            } else {
                source = "厂端";
            }
        }
        followInfoDTO.setComplainID(id);
        followInfoDTO.setFollContent(saleComplaintFollowDTO.getFollowContent());
        followInfoDTO.setJobOrderStatus(JobOrderStatus);
        followInfoDTO.setSource(source);
        followInfoDTO.setCreaterName(FrameworkUtil.getLoginInfo().getUserName());
        followInfoDTO.setFollower(follower.toString());
        followInfoDTO.setFollowDT(sf.format(saleComplaintFollowDTO.getFollowTime()));
        followInfoDTO.setCreaterOrg(CreaterOrg);
        String Json = JSONObject.toJSONString(followInfoDTO);
        String path = "/api/services/app/Order/OrderFollow";
        String http = baseUrl + path;
        String noncestr1 = UUID.randomUUID().toString().replace("-", "");
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String sgin = commonServiceImpl.GetCheckSign(path, apiuid, apipwd, noncestr1, timestamp);
        String describe = "下发给400跟进信息(销售)";
        HttpClient.requestInterfaceJsonCus(describe, Json, http, sgin, apiuid, noncestr1, timestamp);
    }

    @Override
    public ComplaintInfMoreVo selectSaleCusDetailById(ComplaintInfMoreDTO complaintInfMoreDTO) {
        return saleComplaintInfoMapper.selectSaleCusDetailById(complaintInfMoreDTO);
    }

    @Override
    public Page<V51dkComplaintInfMoreVO> select51dkCusByDeal(Page page, V51dkComplaintInfMoreDTO complaintInfMoreDTO) {
        if (!StringUtils.isBlank(complaintInfMoreDTO.getSort())) {
            complaintInfMoreDTO.setOwnerCode(FrameworkUtil.getLoginInfo().getOwnerCode());
        }
        Page<V51dkComplaintInfMoreVO> list = saleComplaintInfoMapper.select51dkCusByDeal(page, complaintInfMoreDTO);
        return list;
    }

    /**
     * 店端客诉，获取处理经销商下拉框数据
     *
     * @return
     */
    @Override
    public List<CompanyInfoDTO> queryCompanyInfoList() {

        List<CompanyInfoDTO> dealCompanyList = new ArrayList<>();
        LoginInfoDto loginInfo = FrameworkUtil.getLoginInfo();
        logger.info("SaleComplaintInfoServiceImpl.queryCompanyInfoList loginInfo:【{}】", JSON.toJSONString(loginInfo));
        String ownerCode = loginInfo.getOwnerCode();
        CompanyRequestDTO requestDTO = new CompanyRequestDTO();
        CompanyInfoParamDTO paramDTO = new CompanyInfoParamDTO();
        paramDTO.setOwnedCompanyName(ownerCode);
        requestDTO.setData(paramDTO);
        // 母店查询子店信息
        logger.info("SaleComplaintInfoServiceImpl.queryCompanyInfoList 母店查询子店信息 入参requestDTO：【{}】", JSON.toJSONString(requestDTO));
        ResponseDTO<List<CompanyInfoDTO>> listResponseDTO = midUserOrganizationClient.queryCompanyInfo(requestDTO);
        logger.info("SaleComplaintInfoServiceImpl.queryCompanyInfoList 母店查询子店信息 入参requestDTO：【{}】 出参listResponseDTO：【{}】", JSON.toJSONString(requestDTO), JSON.toJSONString(listResponseDTO));
        if (CommonConstants.RETURN_CODE_0.equals(listResponseDTO.getReturnCode())) {
            List<CompanyInfoDTO> companyInfoList = listResponseDTO.getData().stream().distinct().collect(Collectors.toList());
            dealCompanyList.addAll(companyInfoList);
        } else {
            logger.error("母店查询子店，调用中台查询接口异常");
            throw new ServiceBizException("母店查询子店，调用中台查询接口异常");
        }

        // 查询母店自身信息
        CompanyParamDTO parentParamDto = new CompanyParamDTO();
        parentParamDto.setCompanyCode(ownerCode);
        logger.info("SaleComplaintInfoServiceImpl.queryCompanyInfoList 子店查询母店信息 入参parentParamDto：【{}】", JSON.toJSONString(parentParamDto));
        ResponseDTO<List<CompanyDetailInfoDTO>> parentResponseDTO = midUserOrganizationClient.selectByCompanyCode(parentParamDto);
        logger.info("SaleComplaintInfoServiceImpl.queryCompanyInfoList 子店查询母店信息 入参parentParamDto：【{}】 出参parentResponseDTO:【{}】", JSON.toJSONString(parentParamDto), JSON.toJSONString(parentResponseDTO));
        if (CommonConstants.RETURN_CODE_0.equals(parentResponseDTO.getReturnCode()) && CollectionUtils.isNotEmpty(parentResponseDTO.getData())) {
            List<CompanyDetailInfoDTO> parentCompanyInfoList = parentResponseDTO.getData();
            CompanyDetailInfoDTO parentCompanyInfo = parentCompanyInfoList.get(0);
            CompanyInfoDTO companyInfo = new CompanyInfoDTO();
            companyInfo.setCompanyCode(parentCompanyInfo.getCompanyCode());
            companyInfo.setCompanyNameCn(parentCompanyInfo.getCompanyNameCn());
            companyInfo.setCompanyShortNameCn(parentCompanyInfo.getCompanyShortNameCn());
            dealCompanyList.add(companyInfo);
        } else {
            logger.error("子店查询母店，调用中台查询接口异常");
            throw new ServiceBizException("子店查询母店，调用中台查询接口异常");
        }

        dealCompanyList.stream().forEach(e -> {
            e.setCompanyNameEn(null);
            e.setCompanyShortNameEn(null);
            e.setDealerType(null);
            e.setIsSatelliteStore(null);
            e.setOwnedCompanyName(null);
        });

        List<CompanyInfoDTO> resultList = dealCompanyList.stream().distinct().collect(Collectors.toList());

        logger.info("SaleComplaintInfoServiceImpl.queryCompanyInfoList  resultList:【{}】 loginInfo：【{}】", JSON.toJSONString(resultList), JSON.toJSONString(loginInfo));

        return resultList;
    }


    /**
     * 下发给400结案信息
     */
    public void IssuedCloseData(SaleComplaintInfoDTO saleComplaintInfoDTO, String closeStatus) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        UserRoleDTO userRoleDTO = commonServiceImpl.getUserRoleDTO();
        CloseComplaintDTO closeComplaintDTO = new CloseComplaintDTO();
        StringBuffer follower = new StringBuffer();
        List<UserRoleDataDTO> UserRoleDataDTO = userRoleDTO.getRoleList();
        if (UserRoleDataDTO != null && UserRoleDataDTO.size() != 0) {
            for (int i = 0; i < UserRoleDataDTO.size(); i++) {
                if (i == UserRoleDataDTO.size() - 1) {
                    follower.append(UserRoleDataDTO.get(i).getRoleName());
                } else {
                    follower.append(UserRoleDataDTO.get(i).getRoleName() + ",");
                }
            }
        }
        List<SaleComplaintInfoPO> queryComplaintData = saleComplaintInfoMapper.queryComplaintData(saleComplaintInfoDTO);
        if (!StringUtils.isNullOrEmpty(queryComplaintData.get(0).getType())) {
            SaleComplaintTypeDTO saleComplaintTypeDTO = new SaleComplaintTypeDTO();
            saleComplaintTypeDTO.setId(Long.valueOf(queryComplaintData.get(0).getType()));
            List<SaleComplaintTypeDTO> saleComplaintTypeDTOList = saleComplaintTypeService.selectListBySql(saleComplaintTypeDTO);
            closeComplaintDTO.setComplainType(saleComplaintTypeDTOList.get(0).getTypeName());
        }
        if (!StringUtils.isNullOrEmpty(queryComplaintData.get(0).getIsSatisfied())) {

        }
        String id = queryComplaintData.get(0).getComplaintId();
        String dealerCloseDate = "";
        if (queryComplaintData.get(0).getApplyTime() != null) {
            dealerCloseDate = sf.format(queryComplaintData.get(0).getApplyTime());
        }
        String regionCloseDate = "";
        if (queryComplaintData.get(0).getRegionAuditTime() != null) {
            regionCloseDate = sf.format(queryComplaintData.get(0).getRegionAuditTime());
        }
        String CreaterOrg = "";
        String source = "";
        if (!StringUtils.isNullOrEmpty(queryComplaintData.get(0).getDataSources())) {
            if (queryComplaintData.get(0).getDataSources() == 82101001) {
                source = "经销商自建";
            } else {
                source = "400下发";
            }
        }
        if ("VOLVO".equals(FrameworkUtil.getLoginInfo().getOwnerCode())) {
            CreaterOrg = "厂端";
        } else {
            CreaterOrg = FrameworkUtil.getLoginInfo().getOwnerCode();
        }
        closeComplaintDTO.setComplainID(id);
        closeComplaintDTO.setCreaterOrg(CreaterOrg);
        closeComplaintDTO.setSource(source);
        closeComplaintDTO.setCaseStatus(closeStatus);
        //经销商申请结案时间

        if (StringUtils.isNullOrEmpty(dealerCloseDate)) {
            closeComplaintDTO.setCaseDoneApplyDT(sf.format(saleComplaintInfoDTO.getApplyTime()));
        } else {
            closeComplaintDTO.setCaseDoneApplyDT(dealerCloseDate);
        }
        //区域经理审核通过时间
        if (StringUtils.isNullOrEmpty(regionCloseDate)) {
            if (!StringUtils.isNullOrEmpty(saleComplaintInfoDTO.getRegionAuditTime())) {
                closeComplaintDTO.setRegionApplyDT(sf.format(saleComplaintInfoDTO.getRegionAuditTime()));
            }
        } else {
            closeComplaintDTO.setRegionApplyDT(regionCloseDate);
        }
        //总部审核
        if (closeStatus.equals("结案")) {
            closeComplaintDTO.setHeadApplyDT(sf.format(saleComplaintInfoDTO.getCloseCaseTime()));
            if (!StringUtils.isNullOrEmpty(queryComplaintData.get(0).getIsRevisit())) {
                switch (queryComplaintData.get(0).getIsRevisit()) {
                    case 10041001:
                        closeComplaintDTO.setCaseStatusFlag("是");
                        break;
                    case 10041002:
                        closeComplaintDTO.setCaseStatusFlag("否");
                        break;
                }
            }
        }
        if ("结案驳回".equals(closeStatus)) {
            closeComplaintDTO.setCaseStatusFlag("否");
        }
        if (!StringUtils.isNullOrEmpty(saleComplaintInfoDTO.getIsRevisit())) {
            switch (saleComplaintInfoDTO.getIsRevisit()) {
                case 10041001:
                    closeComplaintDTO.setVisitNeed("是");
                    break;
                case 10041002:
                    closeComplaintDTO.setVisitNeed("否");
                    break;
            }
        } else {
            closeComplaintDTO.setVisitNeed("否");
        }
        if (saleComplaintInfoDTO.getIsOpinion() != null) {
            switch (saleComplaintInfoDTO.getIsOpinion()) {
                case 10041001:
                    closeComplaintDTO.setRiskFlag("是");
                    break;
                case 10041002:
                    closeComplaintDTO.setRiskFlag("否");
                    break;
            }
        }
        closeComplaintDTO.setCreaterName(FrameworkUtil.getLoginInfo().getUserName());
        closeComplaintDTO.setCreater(follower.toString());
        closeComplaintDTO.setCreaterOrg(CreaterOrg);
        String Json = JSONObject.toJSONString(closeComplaintDTO);
        String path = "/api/services/app/Order/OrderCaseDone";
        String http = baseUrl + path;
        String noncestr1 = UUID.randomUUID().toString().replace("-", "");
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String sgin = commonServiceImpl.GetCheckSign(path, apiuid, apipwd, noncestr1, timestamp);
        String describe = "下发给400结案信息(销售)";
        HttpClient.requestInterfaceJsonCus(describe, Json, http, sgin, apiuid, noncestr1, timestamp);
    }


    @Override
    public IPage<ComplaintInfMoreDTO> selectCusByDeal(Page page, ComplaintInfMoreDTO complaintInfMoreDTO, String user) {
        logger.info("SaleComplaintInfoServiceImpl.selectCusByDeal parameters page:【{}】  complaintInfMoreDTO:【{}】  user:【{}】", JSON.toJSONString(page), JSON.toJSONString(complaintInfMoreDTO), user);
        ComplaintInfMoreDTO complaintInfMoreDto = null;
        try {
            logger.info("SaleComplaintInfoServiceImpl.selectCusByDeal before complaintInfMoreDTO:【{}】  user:{}", JSON.toJSONString(complaintInfMoreDTO), user);
            complaintInfMoreDto = setComplaintInfMoreDTO(complaintInfMoreDTO, user);
            logger.info("SaleComplaintInfoServiceImpl.selectCusByDeal after complaintInfMoreDto:【{}】  user:{}", JSON.toJSONString(complaintInfMoreDto), user);
        } catch (ParseException e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
        if (complaintInfMoreDto == null) {
            complaintInfMoreDto = new ComplaintInfMoreDTO();
        }

        IPage<ComplaintInfMoreDTO> complaintInfMoreDTOIPage = saleComplaintInfoMapper.selectCusByDeal(page, complaintInfMoreDto);
        List<ComplaintInfMoreDTO> pageRecords = complaintInfMoreDTOIPage.getRecords();
        if (CollectionUtils.isNotEmpty(pageRecords)) {

            Map<String, CompanyDetailInfoDTO> dealCompanyDetailMap = getDealCompanyDetailMap(pageRecords);

            //查询工作日
            logger.info("SaleComplaintInfoServiceImpl.selectCusByDeal 调用dict-service服务开始。。。。");
            RestResultResponse<List<TmDateHolidayVo>> workDayResult = dictServiceClient.findWorkDay();
            logger.info("SaleComplaintInfoServiceImpl.selectCusByDeal 工作日返回数据workDayResult：【{}】", JSONUtil.toJsonStr(workDayResult));
            Map<Integer, List<String>> workDayMap = new HashMap<>();
            if (CommonConstants.RESULT_CODE_200.equals(String.valueOf(workDayResult.getResultCode())) && CollectionUtils.isNotEmpty(workDayResult.getData())) {
                List<TmDateHolidayVo> workDayList = workDayResult.getData();
                Map<Integer, List<LocalDate>> integerListMap = workDayList.stream().map(TmDateHolidayVo::getTheTime).collect(Collectors.groupingBy(a -> a.getYear()));
                for(Integer key : integerListMap.keySet()){
                    List<LocalDate> localDateList = integerListMap.get(key);
                    if(CollectionUtils.isEmpty(localDateList)){
                        continue;
                    }
                    workDayMap.put(key, localDateList.stream().map(workDay -> workDay.format(shortFmt)).distinct().collect(Collectors.toList()));
                }
            } else {
                throw new ServiceBizException("查询工作日异常");
            }

            for (ComplaintInfMoreDTO target : pageRecords) {
                // 返回给页面的数据，给所属经销商赋值
                setOwnedCompanyInfo(target, dealCompanyDetailMap);

                // 计算结案目标天数
                calculationOfTargetDaysForCaseClosure(target, workDayMap);
            }

            complaintInfMoreDTOIPage.setRecords(pageRecords);
        }

        return complaintInfMoreDTOIPage;
    }

    /**
     * 计算结案目标天数
     * @param target  结果对象
     * @param workDayMap  工作日map集合，按年分组
     */
    private void calculationOfTargetDaysForCaseClosure(ComplaintInfMoreDTO target, Map<Integer, List<String>> workDayMap) {

        logger.info("SaleComplaintInfoServiceImpl.calculationOfTargetDaysForCaseClosure parameter target：【{}】", JSON.toJSONString(target));

        Date closeCaseTime = target.getCloseCaseTime(); // 结案时间
        Integer isCloseCase = target.getIsCloseCase();  // 是否结案  10041001-已结案   10041002-未结案
        if (CommonConstants.IS_CLOSE_CASE_YES.equals(isCloseCase)) {  // 已结案
            if (Objects.nonNull(closeCaseTime)) {
                LocalDate computationTime = dateConvertsLocalDateTime(closeCaseTime).toLocalDate();
                setTargetDaysForCaseClosure(target, workDayMap, computationTime);
            } else {
                target.setCloseCaseDays(null);  // 结案目标天数
            }
        } else if (CommonConstants.IS_CLOSE_CASE_NO.equals(isCloseCase)) {  // 未结案

            ZoneId chinaZone = ZoneId.of(CommonConstants.LOCALDATETIME_CHINA_ZONE);
            LocalDate computationTime = LocalDateTime.now(chinaZone).toLocalDate();
            setTargetDaysForCaseClosure(target, workDayMap, computationTime);
        }

    }

    /**
     * 计算结案目标天数
     * @param target 结果对象
     * @param workDayMap  工作日map集合，按年分组
     * @param computationTime  参与计算的时间
     */
    private void setTargetDaysForCaseClosure(ComplaintInfMoreDTO target, Map<Integer, List<String>> workDayMap, LocalDate computationTime) {

        logger.info("SaleComplaintInfoServiceImpl.setTargetDaysForCaseClosure parameter target:【{}】  computationTime:{} ", JSON.toJSONString(target), computationTime);

        // 下面的逻辑进行计算结案目标天数
        LocalDateTime caseCreateTime = target.getCaseCreateTime();  // 案件创建时间
        if (Objects.nonNull(caseCreateTime)) {
            Integer workOrderNature = target.getWorkOrderNature();  // 工单性质  2-投诉类  500-协助类  600-低满意度
            LocalDate lastDueDateForClosure = null;  // 最后应结案日期（是否五日结案的第五个工作日）
            // 计算最后应结案日期（是否五日结案的第五个工作日）
            if (WorkOrderNatureEnum.LOW_SATISFACTION.getCode().equals(workOrderNature)) {  // 低满意度
                lastDueDateForClosure = caseCreateTime.toLocalDate().plusDays(5L);  // 案件创建时间加5天  例如:2024-04-09 加5天，则为2024-04-14

            } else {
                // 第1步，判断案件创建时间是否为工作日
                // 第2步，根据案件创建时间是否为工作日， 计算最后应结案日期（是否五日结案的第五个工作日）
                Integer caseCreateTimeYear = caseCreateTime.getYear();
                List<String> workDayList = workDayMap.get(caseCreateTimeYear);
                List<String> nextYearWorkDayList = workDayMap.get(caseCreateTimeYear + 1);
                if (CollectionUtils.isNotEmpty(nextYearWorkDayList)) {
                    workDayList.addAll(nextYearWorkDayList);  // 这么做的目的是，防止有案件在下发在年末的最后一天（而且是非工作日）
                }

                LocalDate caseCreateTimeLocalDate = caseCreateTime.toLocalDate();
                String caseCreateTimeFmt = caseCreateTimeLocalDate.format(shortFmt);
                if (CollectionUtils.isNotEmpty(workDayList)) {

                    if (workDayList.contains(caseCreateTimeFmt)) {  // 案件创建日期为工作日
                        int index = workDayList.indexOf(caseCreateTimeFmt);
                        String lastDueDateForClosureStr = workDayList.get(index + 5);  // 最后应结案日期
                        lastDueDateForClosure = LocalDate.parse(lastDueDateForClosureStr, shortFmt);
                    } else {
                        int i = 0;
                        do {
                            caseCreateTimeLocalDate = caseCreateTimeLocalDate.plusDays(1L);
                            caseCreateTimeFmt = caseCreateTimeLocalDate.format(shortFmt);
                            if (i > 10) {  // 防止出现死循环
                                logger.info("SaleComplaintInfoServiceImpl.setTargetDaysForCaseClosure 中断循环 投诉案件complaintId:【{}】", target.getComplaintId());
                                break;
                            }
                            i++;
                        } while (!workDayList.contains(caseCreateTimeFmt));

                        int index = workDayList.indexOf(caseCreateTimeFmt);
                        String lastDueDateForClosureStr = workDayList.get(index + 4);  // 最后应结案日期
                        lastDueDateForClosure = LocalDate.parse(lastDueDateForClosureStr, shortFmt);

                    }
                } else {
                    throw new ServiceBizException("未获取到案件创建时间对应年份的工作日数据");
                }
            }

            long closeCaseDays = ChronoUnit.DAYS.between(computationTime, lastDueDateForClosure);  // 表示 lastDueDateForClosure 减去 computationTime 的结果
            target.setCloseCaseDays(Integer.parseInt(String.valueOf(closeCaseDays)));  // 结案目标天数

        } else {

            target.setCloseCaseDays(null);  // 结案目标天数
        }

    }


    /**
     * date 转换为 LocalDateTime
     * @param date
     * @return
     */
    private LocalDateTime dateConvertsLocalDateTime(Date date) {

        Instant instant = date.toInstant();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, java.time.ZoneId.systemDefault());
        return localDateTime;
    }

    /**
     * 填充所属经销商对应数据
     * @param target
     * @param dealCompanyDetailMap
     */
    private void setOwnedCompanyInfo(ComplaintInfMoreDTO target, Map<String, CompanyDetailInfoDTO> dealCompanyDetailMap) {
        String dealerCode = target.getDealerCode();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(dealerCode) && null != dealCompanyDetailMap) {
            CompanyDetailInfoDTO resultCompany = dealCompanyDetailMap.get(dealerCode);
            if (Objects.nonNull(resultCompany)) {
                target.setOwnedCompanyCode(resultCompany.getOwnedCompanyName());  // 所属经销商code
                target.setOwnedCompanyNameCn(resultCompany.getOwnedCompanyNameCn());  // 所属经销商全称
                target.setOwnedCompanyShortNameCn(resultCompany.getOwnedCompanyShortNameCn());  // 所属经销商简称
                target.setOwnedCompanyStatus(resultCompany.getOwnedCompanyStatus());  // 所属经销商营业状态
            }
        }
    }

    /**
     * 获取处理经销商数据 包括所属经销商code，所属经销商全称，所属经销商简称，所属经销商营业状态
     * @param pageRecords
     * @return
     */
    private Map<String, CompanyDetailInfoDTO> getDealCompanyDetailMap(List<ComplaintInfMoreDTO> pageRecords) {
        Map<String, CompanyDetailInfoDTO> dealCompanyDetailMap = null;
        List<String> dealDealerCodeList = pageRecords.stream().filter(e -> org.apache.commons.lang3.StringUtils.isNotBlank(e.getDealerCode())).map(ComplaintInfMoreDTO::getDealerCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(dealDealerCodeList)) {
            // 通过处理经销商，查询经销商code
            String companyCodeParam = String.join(CommonConstants.STR_COMMA, dealDealerCodeList);
            CompanyParamDTO paramDTO = new CompanyParamDTO();
            paramDTO.setCompanyCode(companyCodeParam);
            // 处理经销商信息
            logger.info("SaleComplaintInfoServiceImpl.getDealCompanyDetailMap 子店查询母店1 入参paramDTO：【{}】", JSON.toJSONString(paramDTO));
            ResponseDTO<List<CompanyDetailInfoDTO>> responseDTO = midUserOrganizationClient.selectByCompanyCode(paramDTO);
            logger.info("SaleComplaintInfoServiceImpl.getDealCompanyDetailMap 子店查询母店1 入参paramDTO：【{}】  出参responseDTO：【{}】", JSON.toJSONString(paramDTO), JSON.toJSONString(responseDTO));
            if (CommonConstants.RETURN_CODE_0.equals(responseDTO.getReturnCode()) && CollectionUtils.isNotEmpty(responseDTO.getData())) {
                // 处理经销商数据
                List<CompanyDetailInfoDTO> companyDetailInfoList = responseDTO.getData().stream().distinct().collect(Collectors.toList());
                // 所属经销商code
                List<String> ownedCompanyCodeList = companyDetailInfoList.stream().map(e -> org.apache.commons.lang3.StringUtils.isNotBlank(e.getOwnedCompanyName()) ? e.getOwnedCompanyName() : e.getCompanyCode()).distinct().collect(Collectors.toList());
                String ownedCompanyCodeParam = String.join(CommonConstants.STR_COMMA, ownedCompanyCodeList);
                CompanyParamDTO ownedCompanyParamDTO = new CompanyParamDTO();
                ownedCompanyParamDTO.setCompanyCode(ownedCompanyCodeParam);
                // 所属经销商信息
                logger.info("SaleComplaintInfoServiceImpl.getDealCompanyDetailMap 子店查询母店2 入参ownedCompanyParamDTO：【{}】", JSON.toJSONString(ownedCompanyParamDTO));
                ResponseDTO<List<CompanyDetailInfoDTO>> ownedCompanyResponseDTO = midUserOrganizationClient.selectByCompanyCode(ownedCompanyParamDTO);
                logger.info("SaleComplaintInfoServiceImpl.getDealCompanyDetailMap 子店查询母店2 入参ownedCompanyParamDTO：【{}】  出参ownedCompanyResponseDTO：", JSON.toJSONString(ownedCompanyParamDTO), JSON.toJSONString(ownedCompanyResponseDTO));
                if (CommonConstants.RETURN_CODE_0.equals(ownedCompanyResponseDTO.getReturnCode()) && CollectionUtils.isNotEmpty(ownedCompanyResponseDTO.getData())) {
                    // 所属经销商数据
                    List<CompanyDetailInfoDTO> ownedCompanyDetailList = ownedCompanyResponseDTO.getData();
                    Map<String, CompanyDetailInfoDTO> ownedCompanyDetailMap = ownedCompanyDetailList.stream().distinct().collect(Collectors.toMap(CompanyDetailInfoDTO::getCompanyCode, Function.identity(), (k1, k2) -> k2));
                    // 填充所属经销商信息
                    for (CompanyDetailInfoDTO dealCompany : companyDetailInfoList) {
                        String dealOwnedCompanyCode = org.apache.commons.lang3.StringUtils.isNotBlank(dealCompany.getOwnedCompanyName()) ? dealCompany.getOwnedCompanyName() : dealCompany.getCompanyCode(); // 处理经销商的所属经销商code
                        CompanyDetailInfoDTO ownedCompany = ownedCompanyDetailMap.get(dealOwnedCompanyCode); // 所属经销商信息
                        if (Objects.nonNull(ownedCompany)) {
                            dealCompany.setOwnedCompanyName(dealOwnedCompanyCode); // 处理经销商的所属经销商code
                            dealCompany.setOwnedCompanyNameCn(ownedCompany.getCompanyNameCn());  //  处理经销商的所属经销商全称
                            dealCompany.setOwnedCompanyShortNameCn(ownedCompany.getCompanyShortNameCn());  // 处理经销商的所属经销商简称
                            dealCompany.setOwnedCompanyStatus(ownedCompany.getStatus()); // 处理经销商的所属经销商营业状态
                        }
                    }

                    // 处理经销商Map 包含了所属经销商的code 所属经销商的全称 所属经销商的简称 所属经销商的营业状态
                    dealCompanyDetailMap = companyDetailInfoList.stream().distinct().collect(Collectors.toMap(CompanyDetailInfoDTO::getCompanyCode, Function.identity(), (k1, k2) -> k2));

                } else {
                    logger.error("SaleComplaintInfoServiceImpl.getDealCompanyDetailMap 子店查询母店2，调用中台查询接口异常");
                    throw new ServiceBizException("子店查询母店，调用中台查询接口异常");
                }

            } else {
                logger.error("SaleComplaintInfoServiceImpl.getDealCompanyDetailMap 子店查询母店1，调用中台查询接口异常");
                throw new ServiceBizException("子店查询母店，调用中台查询接口异常");
            }

        }

        return dealCompanyDetailMap;
    }


    /**
     * 赋值
     */
    public ComplaintInfMoreDTO setComplaintInfMoreDTO(ComplaintInfMoreDTO complaintInfMoreDTO, String user) throws ParseException {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (complaintInfMoreDTO.getSource1() != null) {
            List source1 = complaintInfMoreDTO.getSource1();
            StringBuffer source = new StringBuffer();
            for (int i = 0; i < source1.size(); i++) {

                if (i == source1.size() - 1) {
                    source.append(source1.get(i));
                } else {
                    source.append(source1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setSource(source.toString());
        }
        if (complaintInfMoreDTO.getType1() != null) {
            List type1 = complaintInfMoreDTO.getType1();
            StringBuffer type = new StringBuffer();
            for (int i = 0; i < type1.size(); i++) {
                if (i == type1.size() - 1) {
                    type.append(type1.get(i));
                } else {
                    type.append(type1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setType(type.toString());
        }
        if (complaintInfMoreDTO.getWorkOrderStatus1() != null) {
            List workOrderStatus1 = complaintInfMoreDTO.getWorkOrderStatus1();
            StringBuffer workOrderStatus = new StringBuffer();
            for (int i = 0; i < workOrderStatus1.size(); i++) {

                if (i == workOrderStatus1.size() - 1) {
                    workOrderStatus.append(workOrderStatus1.get(i));
                } else {
                    workOrderStatus.append(workOrderStatus1.get(i) + ",");
                }
            }
            if (!workOrderStatus.toString().equals("")) {
                complaintInfMoreDTO.setWorkOrderStatusData(workOrderStatus.toString());
            }
        }
        if (complaintInfMoreDTO.getImportanceLevel1() != null) {
            List importanceLevel1 = complaintInfMoreDTO.getImportanceLevel1();
            StringBuffer importanceLevel = new StringBuffer();
            for (int i = 0; i < importanceLevel1.size(); i++) {

                if (i == importanceLevel1.size() - 1) {
                    importanceLevel.append(importanceLevel1.get(i));
                } else {
                    importanceLevel.append(importanceLevel1.get(i) + ",");
                }
            }

            if (!importanceLevel.toString().equals("")) {
                complaintInfMoreDTO.setImportanceLevelData(importanceLevel.toString());
            }
        }
        if (complaintInfMoreDTO.getModel1() != null) {
            List model1 = complaintInfMoreDTO.getModel1();
            StringBuffer model = new StringBuffer();
            for (int i = 0; i < model1.size(); i++) {

                if (i == model1.size() - 1) {
                    model.append(model1.get(i));
                } else {
                    model.append(model1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setModel(model.toString());
        }
        //集团
//        if (complaintInfMoreDTO.getBloc1() != null) {
//            List bloc1 = complaintInfMoreDTO.getBloc1();
//            StringBuffer blocId = new StringBuffer();
//            for (int i = 0; i < bloc1.size(); i++) {
//
//                if (i == bloc1.size() - 1) {
//                    blocId.append(bloc1.get(i));
//                } else {
//                    blocId.append(bloc1.get(i) + ",");
//                }
//            }
//            complaintInfMoreDTO.setBlocId(blocId.toString());
//        }
        //经销商
//        if (complaintInfMoreDTO.getDealerCode() != null) {
//            String[] dealerCodeList = null;
//            dealerCodeList = complaintInfMoreDTO.getDealerCode().split(",");
//            StringBuffer dealerCode = new StringBuffer();
//            for (int i = 0; i < dealerCodeList.length; i++) {
//
//                if (i == dealerCodeList.length - 1) {
//                    dealerCode.append("\"" + dealerCodeList[i] + "\"");
//                } else {
//                    dealerCode.append("\"" + dealerCodeList[i] + "\"" + ",");
//                }
//            }
//            complaintInfMoreDTO.setDealerCode(dealerCode.toString());
//        }
        //区域
//        if (complaintInfMoreDTO.getRegion1() != null) {
//            List region1 = complaintInfMoreDTO.getRegion1();
//            StringBuffer regionId = new StringBuffer();
//            for (int i = 0; i < region1.size(); i++) {
//
//                if (i == region1.size() - 1) {
//                    regionId.append(region1.get(i));
//                } else {
//                    regionId.append(region1.get(i) + ",");
//                }
//            }
//            complaintInfMoreDTO.setRegionId(regionId.toString());
//        }
        //区域经理
        if (complaintInfMoreDTO.getRegionManager1() != null) {
            List regionManagerList = complaintInfMoreDTO.getRegionManager1();
            String regionManager = String.valueOf(regionManagerList.get(0));
            List<OrgDetailDTO> dealerList = commonServiceImpl.selectDealerCode(Integer.parseInt(regionManager));
            List<String> dealerCode = new ArrayList<>();
            for (int y = 0; y < dealerList.size(); y++) {
                dealerCode.add(dealerList.get(y).getCompanyCode());
            }
            List<String> dealerList1 = dealerCode.stream().distinct().collect(Collectors.toList());
//            StringBuffer dealCode = new StringBuffer();
            for (int i = 0; i < dealerList1.size(); i++) {
                if (dealerList1.get(i).equals("VOLVO")) {
                    dealerList1.remove(i);
                }
            }
//            for (int i = 0; i < dealerList1.size(); i++) {
//                if (i == dealerList1.size() - 1) {
//                    dealCode.append("\'" + dealerList1.get(i) + "\'");
//                } else {
//                    dealCode.append("\'" + dealerList1.get(i) + "\'" + ",");
//                }
//            }
            String dealCodeParam = String.join(CommonConstants.STR_COMMA, dealerList1);
            complaintInfMoreDTO.setDealerCode(dealCodeParam);
        }
        if (complaintInfMoreDTO.getCallTime1() != null && complaintInfMoreDTO.getCallTime1().length > 0) {

            complaintInfMoreDTO.setCallTime(sf.parse(complaintInfMoreDTO.getCallTime1()[0]));
            complaintInfMoreDTO.setCallTime2(sf.parse(complaintInfMoreDTO.getCallTime1()[1]));
        }
        if (complaintInfMoreDTO.getCallTime1() != null && complaintInfMoreDTO.getCallTime1().length > 0) {

            complaintInfMoreDTO.setCallTime(sf.parse(complaintInfMoreDTO.getCallTime1()[0]));
            complaintInfMoreDTO.setCallTime2(sf.parse(complaintInfMoreDTO.getCallTime1()[1]));
        }

        if (complaintInfMoreDTO.getActuallFollowTime2() != null && complaintInfMoreDTO.getActuallFollowTime2().length > 0) {
            complaintInfMoreDTO.setActuallFollowTime(sf.parse(complaintInfMoreDTO.getActuallFollowTime2()[0]));
            complaintInfMoreDTO.setActuallFollowTime1(sf.parse(complaintInfMoreDTO.getActuallFollowTime2()[1]));
        }
        if (complaintInfMoreDTO.getFisrtRestartTime1() != null && complaintInfMoreDTO.getFisrtRestartTime1().length > 0) {
            complaintInfMoreDTO.setFisrtRestartTime(sf.parse(complaintInfMoreDTO.getFisrtRestartTime1()[0]));
            complaintInfMoreDTO.setFisrtRestartTime2(sf.parse(complaintInfMoreDTO.getFisrtRestartTime1()[1]));
        }
        if (complaintInfMoreDTO.getPlanFollowTime() != null && complaintInfMoreDTO.getPlanFollowTime5().length > 0) {
            complaintInfMoreDTO.setPlanFollowTime1(sf.parse(complaintInfMoreDTO.getPlanFollowTime5()[0]));
            complaintInfMoreDTO.setPlanFollowTime2(sf.parse(complaintInfMoreDTO.getPlanFollowTime5()[1]));
        }
        SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO = new SaleComplaintCustomFieldUseDTO();
        saleComplaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
        saleComplaintCustomFieldUseDTO.setIsDeleted(0);
        saleComplaintCustomFieldUseDTO.setIsQuery(true);


        //自定义查询
//        if (complaintInfMoreDTO.getFollowStatus1() != null && complaintInfMoreDTO.getFollowStatus1().size() != 0) {
//            List followStatus1 = complaintInfMoreDTO.getFollowStatus1();
//            StringBuffer followStatus = new StringBuffer();
//            for (int i = 0; i < followStatus1.size(); i++) {
//
//                if (i == followStatus1.size() - 1) {
//                    followStatus.append(followStatus1.get(i));
//                } else {
//                    followStatus.append(followStatus1.get(i) + ",");
//                }
//            }
//            complaintInfMoreDTO.setFollowStatus(getSql(followStatus.toString(), "t.follow_status"));
//        }
//        if (complaintInfMoreDTO.getCcmPart1() != null && complaintInfMoreDTO.getCcmPart1().size() != 0) {
//            List ccmPart1 = complaintInfMoreDTO.getCcmPart1();
//            StringBuffer ccmPart = new StringBuffer();
//            for (int i = 0; i < ccmPart1.size(); i++) {
//
//                if (i == ccmPart1.size() - 1) {
//                    ccmPart.append(ccmPart1.get(i));
//                } else {
//                    ccmPart.append(ccmPart1.get(i) + ",");
//                }
//            }
//            complaintInfMoreDTO.setCcmPart(getSql(ccmPart.toString(), "t3.ccm_part"));
//        }
//        if (complaintInfMoreDTO.getCcmSubdivisionPart1() != null && complaintInfMoreDTO.getCcmSubdivisionPart1().size() != 0) {
//            List ccmSubdivisionPart1 = complaintInfMoreDTO.getCcmSubdivisionPart1();
//            StringBuffer ccmSubdivisionPart = new StringBuffer();
//            for (int i = 0; i < ccmSubdivisionPart1.size(); i++) {
//
//                if (i == ccmSubdivisionPart1.size() - 1) {
//                    ccmSubdivisionPart.append(ccmSubdivisionPart1.get(i));
//                } else {
//                    ccmSubdivisionPart.append(ccmSubdivisionPart1.get(i) + ",");
//                }
//            }
//            complaintInfMoreDTO.setCcmSubdivisionPart(getSql(ccmSubdivisionPart.toString(), "t3.ccm_subdivision_part"));
//        }
//        if (complaintInfMoreDTO.getCcMainReason1() != null && complaintInfMoreDTO.getCcMainReason1().size() != 0) {
//            List ccMainReason1 = complaintInfMoreDTO.getCcMainReason1();
//            StringBuffer ccMainReason = new StringBuffer();
//            for (int i = 0; i < ccMainReason1.size(); i++) {
//
//                if (i == ccMainReason1.size() - 1) {
//                    ccMainReason.append(ccMainReason1.get(i));
//                } else {
//                    ccMainReason.append(ccMainReason1.get(i) + ",");
//                }
//            }
//            complaintInfMoreDTO.setCcMainReason(getSql(ccMainReason.toString(), "t3.cc_main_reason"));
//        }
//        if (complaintInfMoreDTO.getCcResult1() != null && complaintInfMoreDTO.getCcResult1().size() != 0) {
//            List ccResult1 = complaintInfMoreDTO.getCcResult1();
//            StringBuffer ccResult = new StringBuffer();
//            for (int i = 0; i < ccResult1.size(); i++) {
//
//                if (i == ccResult1.size() - 1) {
//                    ccResult.append(ccResult1.get(i));
//                } else {
//                    ccResult.append(ccResult1.get(i) + ",");
//                }
//            }
//            complaintInfMoreDTO.setCcResult(getSql(ccResult.toString(), "t3.cc_result"));
//        }
        if (complaintInfMoreDTO.getCategory11() != null && complaintInfMoreDTO.getCategory11().size() != 0) {
            List category11 = complaintInfMoreDTO.getCategory11();
            StringBuffer category1 = new StringBuffer();
            for (int i = 0; i < category11.size(); i++) {

                if (i == category11.size() - 1) {
                    category1.append(category11.get(i));
                } else {
                    category1.append(category11.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setCategory1(getSql(category1.toString(), "t.category1"));
        }
        if (complaintInfMoreDTO.getCategory21() != null && complaintInfMoreDTO.getCategory21().size() != 0) {
            List category21 = complaintInfMoreDTO.getCategory21();
            StringBuffer category2 = new StringBuffer();
            for (int i = 0; i < category21.size(); i++) {

                if (i == category21.size() - 1) {
                    category2.append(category21.get(i));
                } else {
                    category2.append(category21.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setCategory2(getSql(category2.toString(), "t.category2"));
        }
        if (complaintInfMoreDTO.getCategory31() != null && complaintInfMoreDTO.getCategory31().size() != 0) {
            List category31 = complaintInfMoreDTO.getCategory31();
            StringBuffer category3 = new StringBuffer();
            for (int i = 0; i < category31.size(); i++) {

                if (i == category31.size() - 1) {
                    category3.append(category31.get(i));
                } else {
                    category3.append(category31.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setCategory3(getSql(category3.toString(), "t.category3"));
        }
//        if (complaintInfMoreDTO.getClassification12() != null && complaintInfMoreDTO.getClassification12().size() != 0) {
//            List classification12 = complaintInfMoreDTO.getClassification12();
//            StringBuffer classification1 = new StringBuffer();
//            for (int i = 0; i < classification12.size(); i++) {
//
//                if (i == classification12.size() - 1) {
//                    classification1.append(classification12.get(i));
//                } else {
//                    classification1.append(classification12.get(i) + ",");
//                }
//            }
//            complaintInfMoreDTO.setClassification11(getSql(classification1.toString(), "t3.classification1"));
//        }
//        if (complaintInfMoreDTO.getClassification22() != null && complaintInfMoreDTO.getClassification22().size() != 0) {
//            List classification22 = complaintInfMoreDTO.getClassification22();
//            StringBuffer classification2 = new StringBuffer();
//            for (int i = 0; i < classification22.size(); i++) {
//
//                if (i == classification22.size() - 1) {
//                    classification2.append(classification22.get(i));
//                } else {
//                    classification2.append(classification22.get(i) + ",");
//                }
//            }
//            complaintInfMoreDTO.setClassification21(getSql(classification2.toString(), "t3.classification2"));
//        }
//        if (complaintInfMoreDTO.getClassification3() != null) {
//            complaintInfMoreDTO.setClassification3(complaintInfMoreDTO.getClassification3());
//        }
        if (complaintInfMoreDTO.getSmallClass1() != null && complaintInfMoreDTO.getSmallClass1().size() != 0) {
            List smallClass1 = complaintInfMoreDTO.getSmallClass1();
            StringBuffer smallClass = new StringBuffer();
            for (int i = 0; i < smallClass1.size(); i++) {

                if (i == smallClass1.size() - 1) {
                    smallClass.append(smallClass1.get(i));
                } else {
                    smallClass.append(smallClass1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setSmallClass(getSql(smallClass.toString(), "t4.class_code"));
        }
        //自定义置顶
        ComplaintCustomTopUseDTO complaintCustomTopUseDTO = new ComplaintCustomTopUseDTO();
        complaintCustomTopUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
        complaintCustomTopUseDTO.setIsDeleted(false);
        complaintCustomTopUseDTO.setIsValid(10041001);
        List<ComplaintCustomTopUseDTO> toplist = complaintCustomTopUseService.selectListBySql(complaintCustomTopUseDTO);
        String sqltext1 = "";
        String sql = "";
        String sql1 = "";
        String sql2 = "";
        String dealer = "deal";
        String time = "3000/11/21 01:46:40";
        //sqltext1 = "order by  DATE_FORMAT(call_time,\"%Y-%m-%d\") desc ,work_order_nature,is_close_case desc,close_case_time desc";
        sqltext1 = "order by  DATE_FORMAT(call_time,\"%Y-%m-%d\") desc";
        if (toplist.size() != 0) {
            for (int i = 0; i < toplist.size(); i++) {
                String fieldName = toplist.get(i).getType();
                switch (fieldName) {
//                    case "planFollowTime":
//                        sqltext1 += "," + "IFNULL(nextPlanFollowTime,9999999999999) ";
//                        break;
                    case "ccmIsRead":
                        sqltext1 += "," + "ccm_is_read=1 desc ";
                        break;
                    case "importanceLevel":
                        sqltext1 += "," + "importance_level=82091001 DESC ";
                        break;
                    case "callTime":
                        sqltext1 += "," + "call_time and t.close_case_status!=82441005 and  t.close_case_status!=82441005  and t.is_close_case=10041002 DESC ";
                        break;
//                    case "isFinish":
//                        sqltext1 += "," + "t5.is_finish=10041001 desc ";
//                        break;
                    default:

                }
            }
            sql1 = sqltext1;

        }
        //自定义排序
        saleComplaintCustomFieldUseDTO = new SaleComplaintCustomFieldUseDTO();
        saleComplaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
        saleComplaintCustomFieldUseDTO.setIsDeleted(0);
        saleComplaintCustomFieldUseDTO.setIsSort(1);
        List<SaleComplaintCustomFieldUseDTO> sortUse = salecomplaintCustomFieldUseService.selectListBySql(saleComplaintCustomFieldUseDTO);
        String sqltext = "";
        if (sortUse.size() != 0) {
            for (int i = 0; i < sortUse.size(); i++) {
                String fieldName = sortUse.get(i).getFieldName();
                String value = sortUse.get(i).getSortType();
                if (StringUtils.isNullOrEmpty(fieldName))
                    sqltext += ",complaint_id +0 DESC";
                switch (fieldName) {
                    case "complaint_id":
                        sqltext += ",complaint_id +0" + " " + value;
                        break;
                    case "call_time":
                        sqltext += ",call_time" + " " + value;
                        break;
                    case "close_case_time":
                        sqltext += ",close_case_time" + " " + value;
                        break;
/*                    case "work_order_nature":
                        sqltext += "," + "work_order_nature" + " " + value;
                        break;
                    case "work_order_classification":
                        sqltext += "," + "work_order_classification" + " " + value;
                        break;
                    case "work_order_status":
                        sqltext += "," + "work_order_status" + " " + value;
                        break;
                    case "close_case_status":
                        sqltext += "," + "close_case_status" + " " + value;
                        break;
                    case "region":
                        sqltext += "," + "region" + " " + value;
                        break;
                    case "region_manager":
                        sqltext += "," + "region_manager" + " " + value;
                        break;
                    case "dealer_code":
                        sqltext += "," + "dealer_code" + " " + value;
                        break;
                    case "dealer_name":
                        sqltext += "," + "dealer_name" + " " + value;
                        break;
                    case "bloc":
                        sqltext += "," + "bloc" + " " + value;
                        break;
                    case "subject":
                        sqltext += "," + "subject" + " " + value;
                        break;
                    case "source":
                        sqltext += "," + "source" + " " + value;
                        break;
                    case "type":
                        sqltext += "," + "type" + " " + value;
                        break;
                    case "call_name":
                        sqltext += "," + "call_name" + " " + value;
                        break;
                    case "call_tel":
                        sqltext += "," + "call_tel" + " " + value;
                        break;
                    case "model":
                        sqltext += "," + "model" + " " + value;
                        break;
                    case "license_plate_num":
                        sqltext += "," + "license_plate_num" + " " + value;
                        break;
                    case "vin":
                        sqltext += "," + "vin" + " " + value;
                        break;
                    case "newest_restart_time":
                        sqltext += "," + "newest_restart_time" + " " + value;
                        break;*/
                    default:
                }

            }

        } else {
            sqltext += ",complaint_id +0 DESC";
        }
//        String dealerCode = FrameworkUtil.getLoginInfo().getOwnerCode();
//        String aa = "(t.dealer_code =" + "\"" + dealerCode + "\"" + " or EXISTS(SELECT 1 FROM tt_complaint_assist_department   t5 WHERE t.id=t5.complaint_info_id and  t5.assist_dealer_code <>t.dealer_code and t5.assist_dealer_code=" + "\"" + dealerCode + "\"" + "or t5.assist_department=" + "\"" + dealerCode + "\"" + "))";
        sql += sqltext1 + sqltext;
        complaintInfMoreDTO.setSql(sql);
        if (dealer.equals(user)) {  // 该逻辑是，店端登录进来，母店查询子店
            complaintInfMoreDTO.setIsAnonymous(10041002);

            List<String> companyCodeList = getDealerCompanyCodeList(complaintInfMoreDTO);
            String dealerCodeParam = CommonConstants.STR_UP_DOT + String.join(CommonConstants.STR_COMMA_UP_DOT, companyCodeList) + CommonConstants.STR_UP_DOT;
            complaintInfMoreDTO.setDealerCode(dealerCodeParam);
        } else {
            // 厂端登录
            complaintInfMoreDTO.setReport(true);

            List<String> oemCompanyCodeList = getOemCompanyCodeList(complaintInfMoreDTO);
            if (CollectionUtils.isEmpty(oemCompanyCodeList)) {
                complaintInfMoreDTO.setDealerCode(null);
            } else {
                String dealerCodeParam = CommonConstants.STR_UP_DOT + String.join(CommonConstants.STR_COMMA_UP_DOT, oemCompanyCodeList) + CommonConstants.STR_UP_DOT;
                complaintInfMoreDTO.setDealerCode(dealerCodeParam);
            }

        }
        return complaintInfMoreDTO;
    }

    /**
     * 厂端获取经销商code
     * @param complaintInfMoreDTO
     * @return
     */
    private List<String> getOemCompanyCodeList(ComplaintInfMoreDTO complaintInfMoreDTO) {

        logger.info("SaleComplaintInfoServiceImpl.getOemCompanyCodeList complaintInfMoreDTO：【{}】", JSON.toJSONString(complaintInfMoreDTO));

        // 两个筛选项 第一个所属经销商ownedCompanyCode  第二个处理经销商dealerCode
        List<String> oemCompanyCodeList = new ArrayList<>();
        if (org.apache.commons.lang3.StringUtils.isBlank(complaintInfMoreDTO.getOwnedCompanyCode()) && org.apache.commons.lang3.StringUtils.isNotBlank(complaintInfMoreDTO.getDealerCode())) {
            logger.info("SaleComplaintInfoServiceImpl.getOemCompanyCodeList ownedCompanyCode is blank and dealerCode is not blank");
            String[] dealerCodeArr = complaintInfMoreDTO.getDealerCode().split(CommonConstants.STR_COMMA);
            oemCompanyCodeList = Arrays.stream(dealerCodeArr).distinct().collect(Collectors.toList());
            logger.info("SaleComplaintInfoServiceImpl.getOemCompanyCodeList oemCompanyCodeList1：【{}】", JSON.toJSONString(oemCompanyCodeList));
            return oemCompanyCodeList;
        } else if (org.apache.commons.lang3.StringUtils.isNotBlank(complaintInfMoreDTO.getOwnedCompanyCode()) && org.apache.commons.lang3.StringUtils.isBlank(complaintInfMoreDTO.getDealerCode())) {
            logger.info("SaleComplaintInfoServiceImpl.getOemCompanyCodeList ownedCompanyCode is not blank and dealerCode is blank");
            oemCompanyCodeList = getParentAndChildrenCodeList(complaintInfMoreDTO);
            if (CollectionUtils.isEmpty(oemCompanyCodeList)) {
                oemCompanyCodeList.add(CommonConstants.STR_NULL);
            }
            logger.info("SaleComplaintInfoServiceImpl.getOemCompanyCodeList oemCompanyCodeList2：【{}】", JSON.toJSONString(oemCompanyCodeList));
            return oemCompanyCodeList;
        } else if (org.apache.commons.lang3.StringUtils.isNotBlank(complaintInfMoreDTO.getOwnedCompanyCode()) && org.apache.commons.lang3.StringUtils.isNotBlank(complaintInfMoreDTO.getDealerCode())) {  // 所属经销商和处理经销商都不为空
            logger.info("SaleComplaintInfoServiceImpl.getOemCompanyCodeList ownedCompanyCode is not blank and dealerCode is not blank");
            // 处理经销商code列表
            String[] dealerCodeArr = complaintInfMoreDTO.getDealerCode().split(CommonConstants.STR_COMMA);
            List<String> dealerCodeList = Arrays.stream(dealerCodeArr).distinct().collect(Collectors.toList());
            logger.info("SaleComplaintInfoServiceImpl.getOemCompanyCodeList dealerCodeList:【{}】", JSON.toJSONString(dealerCodeList));

            oemCompanyCodeList = getParentAndChildrenCodeList(complaintInfMoreDTO);
            logger.info("SaleComplaintInfoServiceImpl.getOemCompanyCodeList oemCompanyCodeList3：【{}】", JSON.toJSONString(oemCompanyCodeList));
            // 把所属经销商的code数据和处理经销商的code数据取交集
            List<String> intersectionCodeList = oemCompanyCodeList.stream().filter(dealerCodeList::contains).distinct().collect(Collectors.toList());
            logger.info("SaleComplaintInfoServiceImpl.getOemCompanyCodeList 取交集之后的经销商code列表intersectionCodeList：【{}】", JSON.toJSONString(intersectionCodeList));
            if (CollectionUtils.isEmpty(intersectionCodeList)) {
                intersectionCodeList.add(CommonConstants.STR_NULL);
            }
            return intersectionCodeList;
        }

        logger.info("SaleComplaintInfoServiceImpl.getOemCompanyCodeList ownedCompanyCode is blank and dealerCode is blank");

        return oemCompanyCodeList;

    }

    /**
     * 获取母店和子店的经销商code列表
     * @param complaintInfMoreDTO
     * @return
     */
    private List<String> getParentAndChildrenCodeList(ComplaintInfMoreDTO complaintInfMoreDTO) {
        logger.info("SaleComplaintInfoServiceImpl.getParentAndChildrenCodeList complaintInfMoreDTO:【{}】", JSON.toJSONString(complaintInfMoreDTO));
        List<String> oemCompanyCodeList = new ArrayList<>();
        String[] ownedCompanyCodeArr = complaintInfMoreDTO.getOwnedCompanyCode().split(CommonConstants.STR_COMMA);
        List<String> ownedCompanyCodeList = Arrays.stream(ownedCompanyCodeArr).distinct().collect(Collectors.toList()); // 所属经销商列表
        // 母店查询子店，查询所有数据
        CompanyRequestDTO requestDTO = new CompanyRequestDTO();
        CompanyInfoParamDTO paramDTO = new CompanyInfoParamDTO();
        requestDTO.setData(paramDTO);
        logger.info("查询所有的母店的子店数据 入参requestDTO：【{}】", JSON.toJSONString(requestDTO));
        ResponseDTO<List<CompanyInfoDTO>> responseDTO = midUserOrganizationClient.queryCompanyInfo(requestDTO);
        logger.info("查询所有的母店的子店数据 入参requestDTO：【{}】   出参responseDTO:【{}】", JSON.toJSONString(requestDTO), JSON.toJSONString(responseDTO));
        if (CommonConstants.RETURN_CODE_0.equals(responseDTO.getReturnCode()) && CollectionUtils.isNotEmpty(responseDTO.getData())) {
            List<CompanyInfoDTO> companyInfoList = responseDTO.getData();
            Map<String, List<CompanyInfoDTO>> companyInfoMap = companyInfoList.stream().filter(e -> org.apache.commons.lang3.StringUtils.isNotBlank(e.getOwnedCompanyName())).distinct().collect(Collectors.groupingBy(CompanyInfoDTO::getOwnedCompanyName));
            List<String> childrenCodeList = new ArrayList<>();
            List<String> parentCodeList = new ArrayList<>();
            for (String ownedCompanyCode : ownedCompanyCodeList) {
                List<CompanyInfoDTO> childrenCompanyInfoList = companyInfoMap.get(ownedCompanyCode);
                if (CollectionUtils.isNotEmpty(childrenCompanyInfoList)) {
                    List<String> tempCompanyCodeList = childrenCompanyInfoList.stream().filter(e -> org.apache.commons.lang3.StringUtils.isNotBlank(e.getCompanyCode())).distinct().map(CompanyInfoDTO::getCompanyCode).collect(Collectors.toList());  // 所属经销商的子店code列表
                    childrenCodeList.addAll(tempCompanyCodeList);
                    parentCodeList.add(ownedCompanyCode);
                }
            }
            oemCompanyCodeList.addAll(parentCodeList); // 所属经销商code列表
            oemCompanyCodeList.addAll(childrenCodeList); // // 所属经销商的子店code列表
            logger.info("厂端查询ownedCompanyCode is not blank oemCompanyCodeList before oemCompanyCodeList：【{}】", JSON.toJSONString(oemCompanyCodeList));
            oemCompanyCodeList = oemCompanyCodeList.stream().distinct().collect(Collectors.toList());
            logger.info("厂端查询ownedCompanyCode is not blank oemCompanyCodeList after oemCompanyCodeList：【{}】", JSON.toJSONString(oemCompanyCodeList));
            return oemCompanyCodeList;
        } else {
            logger.error("查询所有的母店的子店数据，调用中台查询接口异常");
            throw new ServiceBizException("查询所有的母店的子店数据，调用中台查询接口异常");
        }

    }


    /**
     * 获取处理经销商code
     * @param complaintInfMoreDTO
     * @return
     */
    private List<String> getDealerCompanyCodeList(ComplaintInfMoreDTO complaintInfMoreDTO) {
        logger.info("SaleComplaintInfoServiceImpl.getDealerCompanyCodeList complaintInfMoreDTO:【{}】", JSON.toJSONString(complaintInfMoreDTO));
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        logger.info("getDealerCompanyCodeList loginInfoDto:【{}】", JSON.toJSONString(loginInfoDto));
        String ownerCode = loginInfoDto.getOwnerCode();
        List<String> companyCodeList = new ArrayList<>();
        if (org.apache.commons.lang3.StringUtils.isBlank(complaintInfMoreDTO.getDealerCode())) {
            // 调用中台接口，通过母店查询子店
            CompanyRequestDTO requestDTO = new CompanyRequestDTO();
            CompanyInfoParamDTO paramDTO = new CompanyInfoParamDTO();
            paramDTO.setOwnedCompanyName(ownerCode);
            requestDTO.setData(paramDTO);
            logger.info("SaleComplaintInfoServiceImpl.getDealerCompanyCodeList setDealerCode 调用中台接口通过母店查询子店数据，入参requestDTO：【{}】", JSON.toJSONString(requestDTO));
            ResponseDTO<List<CompanyInfoDTO>> responseDTO = midUserOrganizationClient.queryCompanyInfo(requestDTO);
            logger.info("SaleComplaintInfoServiceImpl.getDealerCompanyCodeList setDealerCode 调用中台接口通过母店查询子店数据，入参requestDTO：【{}】  外层出参responseDTO：【{}】 内层出参：【{}】", JSON.toJSONString(requestDTO), JSON.toJSONString(responseDTO), JSON.toJSONString(responseDTO.getData()));
            if (CommonConstants.RETURN_CODE_0.equals(responseDTO.getReturnCode())) {
                List<String> childrenCompanyCodeList = new ArrayList<>();
                List<CompanyInfoDTO> childrenCompanyInfoList = responseDTO.getData();
                if (CollectionUtils.isNotEmpty(childrenCompanyInfoList)) {
                    childrenCompanyCodeList = childrenCompanyInfoList.stream().filter(e -> org.apache.commons.lang3.StringUtils.isNotBlank(e.getCompanyCode())).map(CompanyInfoDTO::getCompanyCode).distinct().collect(Collectors.toList());
                }
                childrenCompanyCodeList.add(ownerCode);
                companyCodeList = childrenCompanyCodeList.stream().distinct().collect(Collectors.toList());
            } else {
                logger.error("母店查询子店，调用中台查询接口异常");
                throw new ServiceBizException("母店查询子店，调用中台查询接口异常");
            }

        } else {
            String[] dealerCodeArr = complaintInfMoreDTO.getDealerCode().split(CommonConstants.STR_COMMA);
            companyCodeList = Arrays.stream(dealerCodeArr).distinct().collect(Collectors.toList());
        }

        return companyCodeList;
    }

    /**
     * sql拼接
     *
     * @param value
     * @param data
     * @return
     * @throws ServiceBizException
     */
    public String getSql(String value, String data) throws ServiceBizException {
        //sql拼接
        String[] split = value.split(",");
        StringBuffer res = new StringBuffer();
        for (int k = 0; k < split.length; k++) {
            if (k == split.length - 1) {
                res.append(data + " like '%" + split[k] + "%'");
            } else {
                res.append(data + " like '%" + split[k] + "%'" + " or ");
            }
        }
        return res.toString();
    }

    /**
     * 客诉经销商结案
     *
     * @param complaintCustomFieldTestDTO
     * @return
     */
    @Override
    public int insertClose(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO, String flag) {
        long id = complaintCustomFieldTestDTO.getComplaintInfoId();
        SaleComplaintInfoPO saleComplaintInfoPO = saleComplaintInfoMapper.selectById(id);
        if (Objects.nonNull(saleComplaintInfoPO) && String.valueOf(WorkOrderStatusEnum.SUBMISSION_FOR_CLOSURE.getCode()).equals(String.valueOf(saleComplaintInfoPO.getWorkOrderStatus()))) {
            logger.error("SaleComplaintInfoServiceImpl.insertClose 客诉工单已经提交结案id{}", id);
            throw new ServiceBizException("该客诉工单已经提交结案，不可重复操作！");
        }

        SaleComplaintInfoDTO saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        saleComplaintInfoDTO.setApplyTime(complaintCustomFieldTestDTO.getApplyTime());
        saleComplaintInfoDTO.setIsRevisit(complaintCustomFieldTestDTO.getIsRevisit());
        saleComplaintInfoDTO.setIsOpinion(complaintCustomFieldTestDTO.getIsOpinion());
        saleComplaintInfoDTO.setType(complaintCustomFieldTestDTO.getSaleType());
        saleComplaintInfoDTO.setCloseCaseRemark(complaintCustomFieldTestDTO.getCloseCaseRemark());
        SaleComplaintFollowDTO saleComplaintFollowDTO = new SaleComplaintFollowDTO();
        saleComplaintFollowDTO.setFollowContent(complaintCustomFieldTestDTO.getCloseCaseRemark());
        saleComplaintFollowDTO.setComplaintInfoId(id);
        saleComplaintFollowDTO.setDealerNotPublish(false);
        saleComplaintFollowDTO.setCcmNotPublish(true);
        saleComplaintFollowDTO.setFollowTime(new Date());
        saleComplaintFollowDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
        saleComplaintFollowDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
        saleComplaintFollowService.insert(saleComplaintFollowDTO);

        saleComplaintInfoDTO.setId(id);
        String code = "82441001";
        String closeStatus = "";
        //区分自建与下发
        if (!StringUtils.isNullOrEmpty(complaintCustomFieldTestDTO.getIsCloseSatisfied())) {
            if (complaintCustomFieldTestDTO.getIsCloseSatisfied().equals("10041001")) {
                saleComplaintInfoDTO.setCloseCaseStatus(83671005);
                saleComplaintInfoDTO.setWorkOrderStatus(82451004);
                saleComplaintInfoDTO.setIsCloseCase(10041001);
                saleComplaintInfoDTO.setCloseCaseTime(new Date());
                closeStatus = "结案";
                IssuedUpdataData(saleComplaintFollowDTO, FrameworkUtil.getLoginInfo().getOwnerCode(), "跟进完成");
            } else {
                saleComplaintInfoDTO.setCloseCaseStatus(83671006);
                saleComplaintInfoDTO.setWorkOrderStatus(82451004);
                saleComplaintInfoDTO.setIsCloseCase(10041001);
                saleComplaintInfoDTO.setCloseCaseTime(new Date());
                closeStatus = "结案";
                IssuedUpdataData(saleComplaintFollowDTO, FrameworkUtil.getLoginInfo().getOwnerCode(), "跟进完成");
            }
        } else {
            if (flag.equals(code)) {
                saleComplaintInfoDTO.setCloseCaseStatus(83671001);
                saleComplaintInfoDTO.setWorkOrderStatus(82451004);
                saleComplaintInfoDTO.setIsCloseCase(10041002);
                closeStatus = "申请结案";
                IssuedUpdataData(saleComplaintFollowDTO, FrameworkUtil.getLoginInfo().getOwnerCode(), "跟进中");
            } else {
                saleComplaintInfoDTO.setCloseCaseStatus(83671004);
                saleComplaintInfoDTO.setWorkOrderStatus(82451004);
                saleComplaintInfoDTO.setIsCloseCase(10041002);
                closeStatus = "申请结案";
                IssuedUpdataData(saleComplaintFollowDTO, FrameworkUtil.getLoginInfo().getOwnerCode(), "跟进中");
            }
        }

        int row = update(id, saleComplaintInfoDTO);
        IssuedCloseData(saleComplaintInfoDTO, closeStatus);
        return row;

    }

    /**
     * 客诉经销商结案(区域经理)
     *
     * @param complaintCustomFieldTestDTO
     * @return
     */
    @Override
    public int insertRegionClose(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO) {
        long id = complaintCustomFieldTestDTO.getComplaintInfoId();
        SaleComplaintInfoDTO saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        saleComplaintInfoDTO.setIsAgreeRegion(complaintCustomFieldTestDTO.getIsAgreeRegion());
        saleComplaintInfoDTO.setIsOpinion(complaintCustomFieldTestDTO.getIsOpinion());
        saleComplaintInfoDTO.setType(complaintCustomFieldTestDTO.getSaleType());
        saleComplaintInfoDTO.setRegionSatisfiedCase(complaintCustomFieldTestDTO.getRegionSatisfiedCase());
        saleComplaintInfoDTO.setId(id);
        if (complaintCustomFieldTestDTO.getRegionComments() != null) {
            saleComplaintInfoDTO.setRegionComments(complaintCustomFieldTestDTO.getRegionComments());
            SaleComplaintFollowDTO saleComplaintFollowDTO = new SaleComplaintFollowDTO();
            saleComplaintFollowDTO.setFollowContent(complaintCustomFieldTestDTO.getRegionComments());
            saleComplaintFollowDTO.setComplaintInfoId(id);
            saleComplaintFollowDTO.setDealerNotPublish(false);
            saleComplaintFollowDTO.setCcmNotPublish(true);
            saleComplaintFollowDTO.setFollowTime(new Date());
            saleComplaintFollowDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
            saleComplaintFollowDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
            IssuedUpdataData(saleComplaintFollowDTO, FrameworkUtil.getLoginInfo().getOwnerCode(), "提交结案");
            saleComplaintFollowService.insert(saleComplaintFollowDTO);
        }
        int code = 10041001;
        String msg = "";
        if (complaintCustomFieldTestDTO.getIsAgreeRegion() == code) {
            saleComplaintInfoDTO.setRegionAuditTime(new Date());
            saleComplaintInfoDTO.setCloseCaseStatus(83671002);
            saleComplaintInfoDTO.setWorkOrderStatus(82451004);
            saleComplaintInfoDTO.setIsCloseCase(10041002);
            msg = "申请结案";
        } else {
            saleComplaintInfoDTO.setCloseCaseStatus(83671003);
            saleComplaintInfoDTO.setWorkOrderStatus(82451002);
            saleComplaintInfoDTO.setIsCloseCase(10041002);
            msg = "结案驳回";
        }
        int row = update(id, saleComplaintInfoDTO);
        IssuedCloseData(saleComplaintInfoDTO, msg);
        return row;

    }

    @Override
    public int insertSaleHQClose(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO) {
        long id = complaintCustomFieldTestDTO.getComplaintInfoId();

        SaleComplaintInfoDTO saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        saleComplaintInfoDTO.setId(id);
        saleComplaintInfoDTO.setIsAgreeHeadquarters(complaintCustomFieldTestDTO.getIsAgreeHeadquarters());
        saleComplaintInfoDTO.setIsOpinion(complaintCustomFieldTestDTO.getIsOpinion());
        saleComplaintInfoDTO.setType(complaintCustomFieldTestDTO.getSaleType());
        saleComplaintInfoDTO.setHQSatisfiedCase(complaintCustomFieldTestDTO.getHQSatisfiedCase());
        if (complaintCustomFieldTestDTO.getHeadquartersComments() != null) {
            saleComplaintInfoDTO.setHeadquartersComments(complaintCustomFieldTestDTO.getHeadquartersComments());
            SaleComplaintFollowDTO saleComplaintFollowDTO = new SaleComplaintFollowDTO();
            saleComplaintFollowDTO.setFollowContent(complaintCustomFieldTestDTO.getHeadquartersComments());
            saleComplaintFollowDTO.setComplaintInfoId(id);
            saleComplaintFollowDTO.setDealerNotPublish(false);
            saleComplaintFollowDTO.setCcmNotPublish(true);
            saleComplaintFollowDTO.setFollowTime(new Date());
            saleComplaintFollowDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
            saleComplaintFollowDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
            IssuedUpdataData(saleComplaintFollowDTO, "厂端", "提交结案");
            saleComplaintFollowService.insert(saleComplaintFollowDTO);
        }
        int code = 10041001;
        if (complaintCustomFieldTestDTO.getIsAgreeHeadquarters() == code) {
            if (!StringUtils.isNullOrEmpty(saleComplaintInfoDTO.getHQSatisfiedCase()) && 10041001 == saleComplaintInfoDTO.getHQSatisfiedCase()) {
                saleComplaintInfoDTO.setCloseCaseStatus(83671005);
            } else {
                saleComplaintInfoDTO.setCloseCaseStatus(83671006);
            }
            saleComplaintInfoDTO.setCloseCaseTime(new Date());
            saleComplaintInfoDTO.setWorkOrderStatus(82451004);
            saleComplaintInfoDTO.setIsCloseCase(10041001);
            IssuedCloseData(saleComplaintInfoDTO, "结案");
        } else {
            saleComplaintInfoDTO.setCloseCaseStatus(83671003);
            saleComplaintInfoDTO.setWorkOrderStatus(82451002);
            saleComplaintInfoDTO.setIsCloseCase(10041002);
            IssuedCloseData(saleComplaintInfoDTO, "结案驳回");
        }

        return update(id, saleComplaintInfoDTO);
    }

    @Override
    public int insertSaleRestart(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO) {
        long id = complaintCustomFieldTestDTO.getComplaintInfoId();
        SaleComplaintInfoDTO saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        SaleComplaintFollowDTO saleComplaintFollowDTO = new SaleComplaintFollowDTO();
        saleComplaintFollowDTO.setComplaintInfoId(id);
        saleComplaintFollowDTO.setFollowTime(new Date());
        int code = 10041001;
        if (complaintCustomFieldTestDTO.getIsAgreeRestart() == code) {
            saleComplaintInfoDTO.setCloseCaseStatus(null);
            saleComplaintInfoDTO.setCloseCaseTime(null);
            saleComplaintInfoDTO.setRestartCloseCaseTime(null);
            saleComplaintInfoMapper.restart(id);
            saleComplaintInfoDTO.setWorkOrderStatus(82451003);
            saleComplaintInfoDTO.setIsCloseCase(10041002);
            List<SaleComplaintInfoPO> firstReStat = saleComplaintInfoMapper.queryDealerFirstReplyTime(id);
            if (firstReStat.get(0).getFisrtRestartTime() == null) {
                saleComplaintInfoDTO.setFisrtRestartTime(new Date());
            }
            saleComplaintInfoDTO.setNewestRestartTime(new Date());
            saleComplaintFollowDTO.setFollowContent("案件重启");
            IssuedUpdataData(saleComplaintFollowDTO, "厂端", "重启");
        } else {
            saleComplaintInfoDTO.setWorkOrderStatus(82451004);
            saleComplaintFollowDTO.setFollowContent("案件不重启");
            IssuedUpdataData(saleComplaintFollowDTO, "厂端", "提交结案");
        }

        return update(id, saleComplaintInfoDTO);
    }

    /**
     * 导出查询
     *
     * @param complaintInfMoreDTO
     * @return
     * @throws ParseException
     */
    @Override
    public List<ComplaintInfMoreDTO> selectCusByDealAll(ComplaintInfMoreDTO complaintInfMoreDTO, String user) throws ParseException {
        ComplaintInfMoreDTO complaintInfMoreDto = setComplaintInfMoreDTO(complaintInfMoreDTO, user);
        if (complaintInfMoreDto == null) {
            complaintInfMoreDto = new ComplaintInfMoreDTO();
        }
        ComplaintInfMorePO complaintInfMorePo = complaintInfMoreDto.transDtoToPo(ComplaintInfMorePO.class);
        List<ComplaintInfMorePO> list = saleComplaintInfoMapper.selectCusByDealAll(complaintInfMorePo);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(ComplaintInfMoreDTO.class)).collect(Collectors.toList());
        }
    }

    @Override
    public Integer updateSaleComplaint(ComplaintInfMoreDTO complaintInfMoreDTO) {

        if (ObjectUtils.isEmpty(complaintInfMoreDTO)) {
            throw new DALException("修改失败,当前修改数据为空,不能修改");
        }
        logger.info("updateSaleComplaint,complaintInfMoreDTO:{}", complaintInfMoreDTO);

        boolean workStatusFlag = false;
        boolean workOrderNatureFlag = false;
        boolean closeCaseStatusFlag = false;

        Long id = complaintInfMoreDTO.getId();
        if (ObjectUtils.isEmpty(id)) {
            throw new DALException("修改失败,当前修改数据主键为空,不能修改");
        }

        SaleComplaintInfoDTO saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        saleComplaintInfoDTO.setId(id);
        saleComplaintInfoDTO.setComplaintId(complaintInfMoreDTO.getComplaintId());
        List<SaleComplaintInfoPO> queryComplaintData = saleComplaintInfoMapper.queryComplaintData(saleComplaintInfoDTO);

        if (CollectionUtils.isEmpty(queryComplaintData)) {
            throw new DALException("修改失败,没有符合条件的数据");
        }

        SaleComplaintInfoPO saleComplaintInfoPO = queryComplaintData.get(0);
        logger.info("updateSaleComplaint,saleComplaintInfoPO:{}", saleComplaintInfoPO);

        if (ObjectUtils.isEmpty(saleComplaintInfoPO)) {
            throw new DALException("修改失败,没有符合条件的数据");
        }

        saleComplaintInfoDTO.setPlantDescription(complaintInfMoreDTO.getPlantDescription());

        if (!ObjectUtils.isEmpty(complaintInfMoreDTO.getWorkOrderStatus())) {
            logger.info("updateSaleComplaint,saleComplaintInfoPO.getWorkStatus:{},complaintInfMoreDTO.getWorkStatus:{}", saleComplaintInfoPO.getWorkStatus(), complaintInfMoreDTO.getWorkOrderStatus());
            saleComplaintInfoDTO.setWorkOrderStatus(complaintInfMoreDTO.getWorkOrderStatus());
            if(82451006==complaintInfMoreDTO.getWorkOrderStatus()){
                workStatusFlag = true;
            }
        }

        if (!ObjectUtils.isEmpty(complaintInfMoreDTO.getWorkOrderNature()) && !Objects.equals(saleComplaintInfoPO.getWorkOrderNature(), complaintInfMoreDTO.getWorkOrderNature())) {
            logger.info("updateSaleComplaint,saleComplaintInfoPO.getWorkOrderNature:{},complaintInfMoreDTO.getWorkOrderNature:{}", saleComplaintInfoPO.getWorkOrderNature(), complaintInfMoreDTO.getWorkOrderNature());
            saleComplaintInfoDTO.setWorkOrderNature(complaintInfMoreDTO.getWorkOrderNature());
            saleComplaintInfoDTO.setSource(81971018);
            workOrderNatureFlag = true;
        }

        if (!ObjectUtils.isEmpty(complaintInfMoreDTO.getCloseCaseStatus()) && (Objects.equals(complaintInfMoreDTO.getCloseCaseStatus(), 83671005) || Objects.equals(complaintInfMoreDTO.getCloseCaseStatus(), 83671006))) {
            logger.info("updateSaleComplaint,saleComplaintInfoPO.getCloseCaseStatus:{},complaintInfMoreDTO.getCloseCaseStatus:{}", saleComplaintInfoPO.getCloseCaseStatus(), complaintInfMoreDTO.getCloseCaseStatus());
            saleComplaintInfoDTO.setCloseCaseStatus(complaintInfMoreDTO.getCloseCaseStatus());
            saleComplaintInfoDTO.setCloseCaseTime(complaintInfMoreDTO.getCloseCaseTime());
            saleComplaintInfoDTO.setIsRevisit(complaintInfMoreDTO.getIsRevisit());
            saleComplaintInfoDTO.setIsCloseCase(10041001);
            closeCaseStatusFlag = true;
        }

        int row = updateAndInsert(id, saleComplaintInfoDTO, workStatusFlag, workOrderNatureFlag, closeCaseStatusFlag);

        return row;
    }

    private void IssuedUpdataData(SaleComplaintInfoDTO saleComplaintInfoDTO, boolean workStatusFlag, boolean workOrderNatureFlag, boolean closeCaseStatusFlag) {
        if (workOrderNatureFlag) {
            CloseComplaintDTO closeComplaintDTO = new CloseComplaintDTO();
            closeComplaintDTO.setComplainID(saleComplaintInfoDTO.getComplaintId());
            closeComplaintDTO.setNature("协助类");
            closeComplaintDTO.setScenctype("84001001");
            closeComplaintDTO.setFollContent(saleComplaintInfoDTO.getPlantDescription());
            closeComplaintDTO.setCreaterName(Objects.requireNonNull(FrameworkUtil.getLoginInfo()).getUserName());
            logger.info("IssuedUpdateData,workOrderNatureFlag,closeComplaintDTO:{}", closeComplaintDTO);
            IssuedUpdataData(closeComplaintDTO);
        }
        if (closeCaseStatusFlag) {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            CloseComplaintDTO closeComplaintDTO = new CloseComplaintDTO();
            closeComplaintDTO.setComplainID(saleComplaintInfoDTO.getComplaintId());
            closeComplaintDTO.setScenctype("84001008");
            if (!ObjectUtils.isEmpty(saleComplaintInfoDTO.getIsRevisit())) {
                closeComplaintDTO.setVistedNeed(10041001 == saleComplaintInfoDTO.getIsRevisit() ? "是" : "否");
            }
            if (!ObjectUtils.isEmpty(saleComplaintInfoDTO.getCloseCaseStatus())){
                switch (saleComplaintInfoDTO.getCloseCaseStatus()) {
                    case 83671005:
                        closeComplaintDTO.setCaseStatus("满意");
                        break;
                    case 83671006:
                        closeComplaintDTO.setCaseStatus("不满意");
                        break;
                }
            }
            closeComplaintDTO.setScenctype("84001008");
            if (!ObjectUtils.isEmpty(saleComplaintInfoDTO.getCloseCaseTime())) {
                closeComplaintDTO.setCaseDoneDT(sf.format(saleComplaintInfoDTO.getCloseCaseTime()));
            }
            closeComplaintDTO.setFollContent(saleComplaintInfoDTO.getPlantDescription());
            closeComplaintDTO.setCreaterName(Objects.requireNonNull(FrameworkUtil.getLoginInfo()).getUserName());
            logger.info("IssuedUpdateData,closeCaseStatusFlag,closeComplaintDTO:{}", closeComplaintDTO);
            IssuedUpdataData(closeComplaintDTO);
        }
        if (workStatusFlag) {
            CloseComplaintDTO closeComplaintDTO = new CloseComplaintDTO();
            closeComplaintDTO.setComplainID(saleComplaintInfoDTO.getComplaintId());
            closeComplaintDTO.setScenctype("84001006");
            closeComplaintDTO.setFollContent(saleComplaintInfoDTO.getPlantDescription());
            closeComplaintDTO.setCreaterName(Objects.requireNonNull(FrameworkUtil.getLoginInfo()).getUserName());
            logger.info("IssuedUpdateData,workStatusFlag,closeComplaintDTO:{}", closeComplaintDTO);
            IssuedUpdataData(closeComplaintDTO);
        }
    }


    /**
     * 下发给400结案信息
     */
    private void IssuedUpdataData(CloseComplaintDTO closeComplaintDTO) {
        String Json = JSONObject.toJSONString(closeComplaintDTO);
        String path = midUrlProperties.getUpdateSaleComplaint();
        String http = baseUrl + path;
        String noncestr1 = UUID.randomUUID().toString().replace("-", "");
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String sgin = commonServiceImpl.GetCheckSign(path, apiuid, apipwd, noncestr1, timestamp);
        String describe = "变更案件信息给400(销售)";
        HttpClient.requestInterfaceJsonCus(describe, Json, http, sgin, apiuid, noncestr1, timestamp);
    }

    private int updateAndInsert(Long id, SaleComplaintInfoDTO saleComplaintInfoDTO, boolean workStatusFlag, boolean workOrderNatureFlag, boolean closeCaseStatusFlag) {
        SaleComplaintInfoPO saleComplaintInfoPO = saleComplaintInfoMapper.selectById(id);
        SaleComplaintMaintainPO saleComplaintMaintainPO = new SaleComplaintMaintainPO();
        saleComplaintMaintainPO.setComplaintId(saleComplaintInfoPO.getComplaintId());
        saleComplaintMaintainPO.setWorkStatus(saleComplaintInfoPO.getWorkOrderStatus());
        saleComplaintMaintainPO.setWorkOrderNature(saleComplaintInfoPO.getWorkOrderNature());
        saleComplaintMaintainPO.setCloseCaseStatus(saleComplaintInfoPO.getCloseCaseStatus());
        saleComplaintMaintainPO.setCloseCaseTime(saleComplaintInfoPO.getCloseCaseTime());
        saleComplaintMaintainPO.setOperationalBy(Objects.requireNonNull(FrameworkUtil.getLoginInfo()).getUserName());
        saleComplaintMaintainPO.setOperationalStatus(0);
        saleComplaintMaintainPO.setOperationalTime(new Date());
        saleComplaintMaintainPO.setPlantDescription(saleComplaintInfoDTO.getPlantDescription());
        saleComplaintMaintainMapper.insert(saleComplaintMaintainPO);
        //对对象进行赋值操作
        saleComplaintInfoDTO.transDtoToPo(saleComplaintInfoPO);
        int row = updateSaleComplaint(saleComplaintInfoDTO, workStatusFlag, workOrderNatureFlag, closeCaseStatusFlag, saleComplaintInfoPO,saleComplaintMaintainPO);
        return row;
    }

    @Transactional
    @Override
    public int updateSaleComplaint(SaleComplaintInfoDTO saleComplaintInfoDTO, boolean workStatusFlag, boolean workOrderNatureFlag, boolean closeCaseStatusFlag, SaleComplaintInfoPO saleComplaintInfoPO, SaleComplaintMaintainPO saleComplaintMaintainPO) {
        //执行更新
        int row = saleComplaintInfoMapper.updateById(saleComplaintInfoPO);
        if (row > 0) {
            updateSaleComplaintExt(saleComplaintInfoDTO.getPlantDescription(), saleComplaintInfoPO);
            IssuedUpdataData(saleComplaintInfoDTO, workStatusFlag, workOrderNatureFlag, closeCaseStatusFlag);
            updateSaleComplaintMaintain(saleComplaintMaintainPO);
        }
        return row;
    }

    private void updateSaleComplaintMaintain(SaleComplaintMaintainPO saleComplaintMaintainPO) {
        SaleComplaintMaintainPO saleComplaintMaintainPO1 = saleComplaintMaintainMapper.selectById(saleComplaintMaintainPO.getId());
        if (ObjectUtils.isEmpty(saleComplaintMaintainPO1)) {
            return;
        }
        saleComplaintMaintainPO1.setOperationalStatus(1);
        saleComplaintMaintainMapper.updateById(saleComplaintMaintainPO1);
    }

    private void updateSaleComplaintExt(String plantDescription, SaleComplaintInfoPO saleComplaintInfoPO) {
        SaleComplaintExtPO saleComplaintExtPO = saleComplaintExtService.getSaleComplaintExtPO(saleComplaintInfoPO.getId());
        if (ObjectUtils.isEmpty(saleComplaintExtPO)) {
            return;
        }
        saleComplaintExtPO.setPlantDescription(plantDescription);
        saleComplaintExtService.updateSaleComplaintExtPO(saleComplaintExtPO);
    }

}
