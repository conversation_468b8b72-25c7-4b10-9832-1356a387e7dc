package com.yonyou.dmscus.customer.service.common;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface IBaseService<T> {
    IPage<T> selectPageBysql(Page page, T t);

    List<T> selectListBySql(T t);

    T getById(Long id);

    int insert(T t);

    int update(Long id, T t);

    int deleteById(Long id);

    int deleteBatchIds(String ids);
}