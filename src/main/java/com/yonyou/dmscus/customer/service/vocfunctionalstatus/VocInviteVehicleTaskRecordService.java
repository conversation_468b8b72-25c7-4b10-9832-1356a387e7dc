package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import com.yonyou.dmscus.customer.dto.OnlineOfflineResultDTO;
import com.yonyou.dmscus.customer.dto.cdp.CdpTagTaskParameterDto;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocInviteVehicleTaskRecordPo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: VocInviteVehicleTaskRecordService
 * @projectName dmscus.customer
 * @description: 
 * @date 2022/11/1814:45
 */
public interface VocInviteVehicleTaskRecordService {
    int  selectVocCountByVin(String vin, Long id);
    int  selectVocCountByVinT(String vin, Long id);
    List<Long> selectTypeXIIByVin(String vin);
    int selectLossWarningByVin(String vin);
    void setVocOrderTime(String orderTime, String vin, String code);

    void deleteVocOrderTime(String vin, String code);

    int addRecord(VocInviteVehicleTaskRecordPo recPo);

    int selectCountByIcmId(Long icmId);

    /**通过线索ID查icmId*/
    VocInviteVehicleTaskRecordPo selectIcmIdByRecordId(Long recordId);
    /**通过ID批量修改验证状态*/
    void updateVerifyStatusById(List<Long> list, Integer verifyStatus, LocalDate verifyTime);

    /**修改线索类型,流失类型字段所对应的字典表*/
    void updateDictionaryData();

    List<InviteVehicleRecordPO> verifyEvidence(List<OnlineOfflineResultDTO> dtoList, Map<String, List<InviteVehicleRecordPO>> mapVinIds);

    /**历史验证状态维护*/
    int markHistoryDataAsValid();

    //批量新增扩展表数据
    void addAll(List<VocInviteVehicleTaskRecordPo> list);

    /**批量修改*/
    void updateVerifyStatusAllById(List<VocInviteVehicleTaskRecordPo> list);

    void updateDailyMile(List<CdpTagTaskParameterDto> list);

    void updateBevLeadList(List<Long> updateBevIdList);
}
