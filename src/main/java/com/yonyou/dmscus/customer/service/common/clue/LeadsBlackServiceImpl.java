package com.yonyou.dmscus.customer.service.common.clue;


import com.yonyou.dmscus.customer.dao.common.LeadsBlackMapper;
import com.yonyou.dmscus.customer.dao.common.LeadsBlackRecordMapper;
import com.yonyou.dmscus.customer.entity.po.common.LeadsBlackPO;
import com.yonyou.dmscus.customer.entity.po.common.LeadsBlackRecordPO;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * description
 *
 * <AUTHOR>
 * @date 2024/12/05 16:10
 */
@Service
@Slf4j
public class LeadsBlackServiceImpl implements LeadsBlackService {
    @Resource
    LeadsBlackMapper leadsBlackMapper;
    @Resource
    LeadsBlackRecordMapper leadsBlackRecordMapper;

    @Autowired
    InviteVehicleRecordService inviteVehicleRecordService;

    @Override
    @Transactional
    public void addLeadsBlack(LeadsBlackPO po) {
        log.info("addLeadsBlack,po:{}", po);
        if(ObjectUtils.isEmpty(po)){
            log.info("addLeadsBlack,po is null");
            return;
        }
        String vin = po.getVin();
        if(StringUtils.isEmpty(vin)){
            log.info("addLeadsBlack,vin is null");
            return;
        }
        //邀约线索逻辑处理1.关闭待下发的任务 2.线索隐藏 3.添加数据
        //关闭待下发的任务
        int num = inviteVehicleRecordService.updateIsCreateInviteByVin(vin);
        log.info("addLeadsBlack,task:{}", num);
        //线索隐藏
        num = inviteVehicleRecordService.updateDealerCodeByVin(vin);
        log.info("addLeadsBlack,record:{}", num);
        //新增黑名单
        num = leadsBlackMapper.insert(po);
        log.info("addLeadsBlack,add:{}", num);
        log.info("addLeadsBlack,end");
    }

    @Override
    public Long selectBlackByVinAndType(String vin, String leadsType) {
        return leadsBlackMapper.selectBlackByVinAndType(vin, leadsType);
    }

    @Override
    public int addLeadsBlackRecord(LeadsBlackRecordPO po) {
        return leadsBlackRecordMapper.insert(po);
    }
}
