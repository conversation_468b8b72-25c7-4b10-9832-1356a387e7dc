package com.yonyou.dmscus.customer.service.faultLight;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dto.CustomerInfoDto;
import com.yonyou.dmscus.customer.dto.faultLight.FaultLightBookingRecordDto;
import com.yonyou.dmscus.customer.entity.dto.faultLight.*;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.CallDetailsDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface FaultLightClueService {

    /**
     * 线索,跟进状态联动下拉框
     */
    FaultLightStateDTO selectFaultLightState();
    /**
     * 大小区,城市下拉框
     */
    List<CityDropdownDoxDTO> queryCityDropdownDox();
    /**
     * 线索同步接口
     */
    boolean doClueDataSynchro(ClueDataSynchroDTO dto);
    /**
     * 关联工单下拉框查询
     */
    List<String> selectRoNoSpinner(long id);
    /**
     * 故障灯线索集合查询-店端
     */
    IPage<ClueInfoQueryRespDTO> queryClueInfoList(Page<ClueInfoQueryRespDTO> page, ClueInfoQueryRequestDTO dto);
    /**
     * 故障灯线索数据下载-店端
     */
    void clueInfoDerive(ClueInfoQueryRequestDTO dto, HttpServletResponse response);
    /**
     * 故障灯线索集合查询-厂端
     */
    IPage<ClueInfoQueryRespDTO> factoryQueryClueInfoList(Page<ClueInfoQueryRespDTO> page, ClueInfoQueryRequestDTO dto);
    /**
     * 故障灯线索数据下载-厂端
     */
    void factoryClueInfoDerive(ClueInfoQueryRequestDTO dto, HttpServletResponse response);
    /**
     * 线索数据修改-有责无责
     */
    void updateClueInfo(ClueInfoQueryRequestDTO clueInfoQueryRequestDTO);
    /**
     * 线索录入补偿
     */
    int addClueCompensate(ClueDataSynchroDTO dto);

    /**
     * 故障灯线索状态更新（是否超时、高亮）
     */
    void updateHighlightFlag();

    /**
     * 故障灯线索状态更新（是否超时、高亮）
     */
    void updateHighlights(List<Long> secondAppointmentId);

    /**
     *AI通话取消高亮
     */
    void unHighlight(CallDetailsDTO callDetailsDTO);

    /**
     *AI通话取消高亮
     */
    void cancelHighlights(List<Long> ids);

    /**
     * 获取故障灯高亮线索
     */
    void queryHighlightFlag(String createDate,String endDate);

    /**
     * 更新线索高亮及同步任务状态
     */
    void updateClueAndCdpTagTask(List<Long> clueId, List<Long> cdpTagTaskId);

    /**
     * 根据线索下发时间更新高亮状态
     */
    void updateHighlightFlagByClueDisTime();
    /**
     * 查询故障灯车主
     */
    CustomerInfoDto queryFaultLight(String vin,String ownerCode);
    /**
     * 故障灯消费crm线索状态
     */
    void faultLightConsumerStatus(String t);

}
