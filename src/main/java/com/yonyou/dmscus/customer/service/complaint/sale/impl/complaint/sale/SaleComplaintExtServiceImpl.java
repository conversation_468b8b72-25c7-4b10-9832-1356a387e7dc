package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintExtMapper;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintExtPO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 销售客诉扩展信息新增
 */
@Service
public class SaleComplaintExtServiceImpl implements SaleComplaintExtService {

    @Resource
    private SaleComplaintExtMapper saleComplaintExtMapper;

    @Override
    public int saveSaleComplaintExtPO(SaleComplaintExtPO saleComplaintExtPO) {
        return saleComplaintExtMapper.insert(saleComplaintExtPO);
    }

    @Override
    public SaleComplaintExtPO getSaleComplaintExtPO(long saleComplaintId) {
        LambdaQueryWrapper<SaleComplaintExtPO> queryWrapper = new LambdaQueryWrapper<SaleComplaintExtPO>()
                .eq(SaleComplaintExtPO::getSaleComplaintId, saleComplaintId).eq(SaleComplaintExtPO::getIsDeleted, 0);
        return saleComplaintExtMapper.selectOne(queryWrapper);
    }

    @Override
    public int updateSaleComplaintExtPO(SaleComplaintExtPO saleComplaintExtPO) {
        return saleComplaintExtMapper.updateById(saleComplaintExtPO);
    }
}
