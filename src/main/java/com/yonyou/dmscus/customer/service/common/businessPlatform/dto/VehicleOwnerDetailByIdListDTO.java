package com.yonyou.dmscus.customer.service.common.businessPlatform.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 车主表
 * </p>
 *
 * <AUTHOR> @since 2020-04-20
 */
@ApiModel(value="车主信息查询对象", description="车主信息ID集合查询")
public class VehicleOwnerDetailByIdListDTO {

    @ApiModelProperty(value = "车主编号")
    private Long id;

    @ApiModelProperty(value = "客户唯一ID")
    private Long oneId;

    @ApiModelProperty(value = "潜客姓名")
    private String name;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "联系人名称")
    private String contactName;

    @ApiModelProperty(value = "联系方式一")
    private String contactorMobile;

    @ApiModelProperty(value = "联系方式二 固定电话")
    private String contactorPhone;

    @ApiModelProperty(value = "QQ")
    private String qq;

    @ApiModelProperty(value = "微信号")
    private String weChat;

    @ApiModelProperty(value = "所属行业(互联网/IT/电子/通信:70301002.广告/传媒/文化/体育:70301002.金融:70301003.教育培训:70301004.制药/医疗:70301005.交通/物流/贸易/零售:70301006.专业服务:70301007.房地产/建筑:70301008.汽车:70301009.机械/制造:70301010.消费品:70301011.服务业:70301012.能源/化工/环保:70301013.政府/非盈利机构/其他:70301014)")
    private Integer industry;

    @ApiModelProperty(value = "企业性质(机关:70291001,事业:70291002,企业:70291003,运营:70291004,公检司法:70291005,其它:70291006)")
    private Integer enterpriseType;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "首次成交时间")
    private LocalDateTime bargainDate;

    @ApiModelProperty(value = "客户类型(个人:15231001,公司:15231002)")
    private Integer customerType;

    @ApiModelProperty(value = "性别(先生:10021001,女士:10021002,未知:10021003)")
    private Integer gender;

    @ApiModelProperty(value = "学历（小学:70271001,初中:70271002,高中/中专/技校:70271003,大专:70271004,本科:70271005,硕士及以上:70271006）")
    private Integer education;

    @ApiModelProperty(value = "职业(私营公司老板/自由职业者/个体户:15271001,公司高管:15271002,中层管理人员:15271003,销售人员/销售代表:15271004,私营公司职员/主管:15271005,家庭主妇/夫:15271006,退休:15271007,无工作:15271008,学生:15271009,技术工人:15271010,公务员/教师/警察:15271011,行政职员:15271012,专业人士:15271013,其他:15271014)")
    private Integer occupation;

    @ApiModelProperty(value = "生日")
    private LocalDate birthday;

    @ApiModelProperty(value = "证件类型(身份证:15081001,护照:15081002,军官证:15081003,士兵证:15081004,警察证:15081005,其他:15081006,机构代码:15081007)")
    private Integer ctCode;

    @ApiModelProperty(value = "证件号码")
    private String certificateNo;

    @ApiModelProperty(value = "邮箱")
    private String eMail;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "邮编")
    private String zipCode;

    @ApiModelProperty(value = "婚姻状况(已婚:10361001,未婚:10361002,离异:10361003)")
    private Integer maritalStatus;

    @ApiModelProperty(value = "省份ID(1000)")
    private Long province;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "城市ID(1000)")
    private Long city;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "区县ID(1000)")
    private Long district;

    @ApiModelProperty(value = "区县名称")
    private String districtName;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "建档日期")
    private LocalDateTime createdDate;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getOneId() {
		return oneId;
	}

	public void setOneId(Long oneId) {
		this.oneId = oneId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getContactorMobile() {
		return contactorMobile;
	}

	public void setContactorMobile(String contactorMobile) {
		this.contactorMobile = contactorMobile;
	}

	public String getContactorPhone() {
		return contactorPhone;
	}

	public void setContactorPhone(String contactorPhone) {
		this.contactorPhone = contactorPhone;
	}

	public String getQq() {
		return qq;
	}

	public void setQq(String qq) {
		this.qq = qq;
	}

	public String getWeChat() {
		return weChat;
	}

	public void setWeChat(String weChat) {
		this.weChat = weChat;
	}

	public Integer getIndustry() {
		return industry;
	}

	public void setIndustry(Integer industry) {
		this.industry = industry;
	}

	public Integer getEnterpriseType() {
		return enterpriseType;
	}

	public void setEnterpriseType(Integer enterpriseType) {
		this.enterpriseType = enterpriseType;
	}

	public LocalDateTime getBargainDate() {
		return bargainDate;
	}

	public void setBargainDate(LocalDateTime bargainDate) {
		this.bargainDate = bargainDate;
	}

	public Integer getCustomerType() {
		return customerType;
	}

	public void setCustomerType(Integer customerType) {
		this.customerType = customerType;
	}

	public Integer getGender() {
		return gender;
	}

	public void setGender(Integer gender) {
		this.gender = gender;
	}

	public Integer getEducation() {
		return education;
	}

	public void setEducation(Integer education) {
		this.education = education;
	}

	public Integer getOccupation() {
		return occupation;
	}

	public void setOccupation(Integer occupation) {
		this.occupation = occupation;
	}

	public LocalDate getBirthday() {
		return birthday;
	}

	public void setBirthday(LocalDate birthday) {
		this.birthday = birthday;
	}

	public Integer getCtCode() {
		return ctCode;
	}

	public void setCtCode(Integer ctCode) {
		this.ctCode = ctCode;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public String geteMail() {
		return eMail;
	}

	public void seteMail(String eMail) {
		this.eMail = eMail;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getZipCode() {
		return zipCode;
	}

	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}

	public Integer getMaritalStatus() {
		return maritalStatus;
	}

	public void setMaritalStatus(Integer maritalStatus) {
		this.maritalStatus = maritalStatus;
	}

	public Long getProvince() {
		return province;
	}

	public void setProvince(Long province) {
		this.province = province;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public Long getCity() {
		return city;
	}

	public void setCity(Long city) {
		this.city = city;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public Long getDistrict() {
		return district;
	}

	public void setDistrict(Long district) {
		this.district = district;
	}

	public String getDistrictName() {
		return districtName;
	}

	public void setDistrictName(String districtName) {
		this.districtName = districtName;
	}

	public LocalDateTime getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(LocalDateTime createdDate) {
		this.createdDate = createdDate;
	}

	@Override
    public String toString() {
        return "TtPontentialCustomer{" +
        "id=" + id +
        ", oneId=" + oneId +
        ", name=" + name +
        ", mobile=" + mobile +
        ", contactName=" + contactName +
        ", contactorMobile=" + contactorMobile +
        ", contactorPhone=" + contactorPhone +
        ", qq=" + qq +
        ", weChat=" + weChat +
        ", industry=" + industry +
        ", enterpriseType=" + enterpriseType +
        ", bargainDate=" + bargainDate +
        ", customerType=" + customerType +
        ", gender=" + gender +
        ", education=" + education +
        ", occupation=" + occupation +
        ", birthday=" + birthday +
        ", ctCode=" + ctCode +
        ", certificateNo=" + certificateNo +
        ", eMail=" + eMail +
        ", address=" + address +
        ", zipCode=" + zipCode +
        ", maritalStatus=" + maritalStatus +
        ", province=" + province +
        ", city=" + city +
        ", district=" + district +
        ", provinceName=" + provinceName +
        ", cityName=" + cityName +
        ", districtName=" + districtName +
        ", createdDate=" + createdDate +
        "}";
    }
}
