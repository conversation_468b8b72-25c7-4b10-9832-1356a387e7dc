package com.yonyou.dmscus.customer.service.impl.invitationFollow;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleSaRefMapper;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleSaRefDTO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleSaRefPO;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleSaRefService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 车辆跟进人员对应关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
@Service
public class InviteVehicleSaRefServiceImpl implements InviteVehicleSaRefService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteVehicleSaRefMapper inviteVehicleSaRefMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                  分页对象
     * @param inviteVehicleSaRefDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto
                     *   . InviteVehicleSaRefDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<InviteVehicleSaRefDTO> selectPageBysql(Page page, InviteVehicleSaRefDTO inviteVehicleSaRefDTO) {
        if (inviteVehicleSaRefDTO == null) {
            inviteVehicleSaRefDTO = new InviteVehicleSaRefDTO();
        }
        InviteVehicleSaRefPO inviteVehicleSaRefPO = inviteVehicleSaRefDTO.transDtoToPo(InviteVehicleSaRefPO.class);

        List<InviteVehicleSaRefPO> list = inviteVehicleSaRefMapper.selectPageBySql(page, inviteVehicleSaRefPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteVehicleSaRefDTO> result = list.stream().map(m -> m.transPoToDto(InviteVehicleSaRefDTO.class))
                    .collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param inviteVehicleSaRefDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.InviteVehicleSaRefDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<InviteVehicleSaRefDTO> selectListBySql(InviteVehicleSaRefDTO inviteVehicleSaRefDTO) {
        if (inviteVehicleSaRefDTO == null) {
            inviteVehicleSaRefDTO = new InviteVehicleSaRefDTO();
        }
        InviteVehicleSaRefPO inviteVehicleSaRefPO = inviteVehicleSaRefDTO.transDtoToPo(InviteVehicleSaRefPO.class);
        List<InviteVehicleSaRefPO> list = inviteVehicleSaRefMapper.selectListBySql(inviteVehicleSaRefPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteVehicleSaRefDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.InviteVehicleSaRefDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public InviteVehicleSaRefDTO getById(Long id) {
        InviteVehicleSaRefPO inviteVehicleSaRefPO = inviteVehicleSaRefMapper.selectById(id);
        if (inviteVehicleSaRefPO != null) {
            return inviteVehicleSaRefPO.transPoToDto(InviteVehicleSaRefDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param inviteVehicleSaRefDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(InviteVehicleSaRefDTO inviteVehicleSaRefDTO) {
        //对对象进行赋值操作
        InviteVehicleSaRefPO inviteVehicleSaRefPO = inviteVehicleSaRefDTO.transDtoToPo(InviteVehicleSaRefPO.class);
        //执行插入
        int row = inviteVehicleSaRefMapper.insert(inviteVehicleSaRefPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                    主键ID
     * @param inviteVehicleSaRefDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, InviteVehicleSaRefDTO inviteVehicleSaRefDTO) {
        InviteVehicleSaRefPO inviteVehicleSaRefPO = inviteVehicleSaRefMapper.selectById(id);
        //对对象进行赋值操作
        inviteVehicleSaRefDTO.transDtoToPo(inviteVehicleSaRefPO);
        //执行更新
        int row = inviteVehicleSaRefMapper.updateById(inviteVehicleSaRefPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = inviteVehicleSaRefMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = inviteVehicleSaRefMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }
}
