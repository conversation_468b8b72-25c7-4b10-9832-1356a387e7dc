package com.yonyou.dmscus.customer.service.talkskill;


import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillTypeDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillTypePO;

import java.util.List;

/**
 * <p>
 * 话术业务分类 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-20
 */
public interface TalkskillTypeService  {

    List<TalkskillTypePO> selectListBySql(TalkskillTypeDTO talkskillTypeDTO);

    TalkskillTypeDTO getById(Long id);
    int insert(TalkskillTypeDTO talkskillTypeDTO);
    int update(Long id, TalkskillTypeDTO talkskillTypeDTO);
    int updateList(List<TalkskillTypeDTO> talkskillTypeDTO);
}
