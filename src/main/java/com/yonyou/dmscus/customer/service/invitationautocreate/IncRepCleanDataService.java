package com.yonyou.dmscus.customer.service.invitationautocreate;


import com.yonyou.dmscus.customer.service.invitationautocreate.po.InviteVehiclePO;

import java.util.List;

/**
 * 重复线索数据清洗
 */
public interface IncRepCleanDataService {

    /**
     * 批量清洗重复任务
     */
    void doTaskClean(List<InviteVehiclePO> cloClue, List<InviteVehiclePO> upClue);

    /**
     * 批量清洗重复线索
     */
    void doClueClean(List<InviteVehiclePO> cloClue, List<InviteVehiclePO> upClue);
}
