package com.yonyou.dmscus.customer.service.complaint;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintClassificationDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintClassificationPO;

import java.util.List;

/**
 * <p>
 * 客诉工单分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-13
 */
public interface ComplaintClassificationService  {
    IPage<ComplaintClassificationDTO> selectPageBysql(Page page, ComplaintClassificationDTO complaintClassificationDTO);
    List<ComplaintClassificationDTO> selectListBySql(ComplaintClassificationDTO complaintClassificationDTO);
    ComplaintClassificationDTO getById(Long id);
    int insert(ComplaintClassificationDTO complaintClassificationDTO);
    int update(Long id, ComplaintClassificationDTO complaintClassificationDTO);
    int deleteById(Long id);
    int deleteBatchIds(String ids);

    List<ComplaintClassificationDTO> selectComplaintCategory(ComplaintClassificationDTO complaintClassificationDTO);

    List<ComplaintClassificationPO> getByIds(List<Long> ids);
}
