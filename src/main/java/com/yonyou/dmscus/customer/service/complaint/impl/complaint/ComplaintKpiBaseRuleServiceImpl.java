package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

    
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;


import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiSetDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRulePO;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintKpiBaseRuleMapper;

import com.yonyou.dmscus.customer.service.complaint.ComplaintKpiBaseRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleDTO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;




    /**
 * <p>
 * 客户投诉KP基础规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
@Service
public class ComplaintKpiBaseRuleServiceImpl implements ComplaintKpiBaseRuleService {
        /**
         * 日志说明
         */
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        ComplaintKpiBaseRuleMapper complaintKpiBaseRuleMapper;

        /**
         * 分页查询对应数据
         *
         * @param page 分页对象
         * @param complaintKpiBaseRuleDTO 查询条件
         * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public IPage<ComplaintKpiBaseRuleDTO>selectPageBysql(Page page,ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO){
            if(complaintKpiBaseRuleDTO ==null){
                complaintKpiBaseRuleDTO =new ComplaintKpiBaseRuleDTO();
            }
            ComplaintKpiBaseRulePO complaintKpiBaseRulePo =complaintKpiBaseRuleDTO.transDtoToPo(ComplaintKpiBaseRulePO.class);

            List<ComplaintKpiBaseRulePO>list= complaintKpiBaseRuleMapper.selectPageBySql(page,complaintKpiBaseRulePo);
            if(CommonUtils.isNullOrEmpty(list)){
                page.setRecords(new ArrayList<>());
                return page;
            }else{
                List<ComplaintKpiBaseRuleDTO>result=list.stream().map(m->m.transPoToDto(ComplaintKpiBaseRuleDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param complaintKpiBaseRuleDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<ComplaintKpiBaseRuleDTO>selectListBySql(ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO){
            if(complaintKpiBaseRuleDTO ==null){
                complaintKpiBaseRuleDTO =new ComplaintKpiBaseRuleDTO();
            }
            ComplaintKpiBaseRulePO complaintKpiBaseRulePo =complaintKpiBaseRuleDTO.transDtoToPo(ComplaintKpiBaseRulePO.class);
            List<ComplaintKpiBaseRulePO>list= complaintKpiBaseRuleMapper.selectListBySql(complaintKpiBaseRulePo);
            if(CommonUtils.isNullOrEmpty(list)){
                return new ArrayList<>();
            }else{
                return list.stream().map(m->m.transPoToDto(ComplaintKpiBaseRuleDTO.class)).collect(Collectors.toList());
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public ComplaintKpiBaseRuleDTO getById(Long id){
            ComplaintKpiBaseRulePO complaintKpiBaseRulePo = complaintKpiBaseRuleMapper.selectById(id);
            if(complaintKpiBaseRulePo!=null){
                return complaintKpiBaseRulePo.transPoToDto(ComplaintKpiBaseRuleDTO.class);
            }else{
                throw new DALException("查询失败,没有符合条件的数据");
            }
        }

        /**
         * 根据DTO 进行数据新增
         *
         * @param complaintKpiBaseRuleDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO){
            //对对象进行赋值操作
            ComplaintKpiBaseRulePO complaintKpiBaseRulePo = complaintKpiBaseRuleDTO.transDtoToPo(ComplaintKpiBaseRulePO.class);
            //执行插入
            int row= complaintKpiBaseRuleMapper.insert(complaintKpiBaseRulePo);
            //返回插入的值
            return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param complaintKpiBaseRuleDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO){
            ComplaintKpiBaseRulePO complaintKpiBaseRulePo = complaintKpiBaseRuleMapper.selectById(id);
            //对对象进行赋值操作
            complaintKpiBaseRuleDTO.transDtoToPo(complaintKpiBaseRulePo);
            //执行更新
            int row= complaintKpiBaseRuleMapper.updateById(complaintKpiBaseRulePo);
            return row;
        }

        /**
         * 根据ID 进行删除数据
         *
         * @param id
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteById(Long id){
            int deleteCount= complaintKpiBaseRuleMapper.deleteById(id);
            logger.debug("删除数量：{}",deleteCount);
            if(deleteCount<=0){
                throw new DALException("删除失败,没有符合条件的数据");
            }else{
                return deleteCount;
            }
        }

        @Override
        public int updateKpi(List<ComplaintKpiSetDTO> list) {
            int row=0;
            for (int i=0;i<list.size();i++){
                ComplaintKpiSetDTO complaintKpiSetDto=list.get(i);
                ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO=new ComplaintKpiBaseRuleDTO();
                complaintKpiBaseRuleDTO.setIndexValue(complaintKpiSetDto.getIndexValue());
                if(complaintKpiSetDto.getScore()==null||(complaintKpiSetDto.getScore().toString()).equals("")){
                    complaintKpiBaseRuleDTO.setScore(0);
                }else {
                    complaintKpiBaseRuleDTO.setScore(complaintKpiSetDto.getScore());
                }

                if(complaintKpiSetDto.getIsValid()==false){
                    complaintKpiBaseRuleDTO.setIsValid(0);
                }else {
                    complaintKpiBaseRuleDTO.setIsValid(1);
                }

                long id=complaintKpiSetDto.getId();
                complaintKpiBaseRuleDTO.setKpi(complaintKpiSetDto.getKpi());
                complaintKpiBaseRuleDTO.setCreatedBy(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
                row= complaintKpiBaseRuleMapper.updateKpi(complaintKpiBaseRuleDTO);
            }
            return row;
        }


}
