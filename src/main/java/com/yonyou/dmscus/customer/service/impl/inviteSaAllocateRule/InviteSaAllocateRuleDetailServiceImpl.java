package com.yonyou.dmscus.customer.service.impl.inviteSaAllocateRule;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule.InviteSaAllocateRuleDetailPO;
import com.yonyou.dmscus.customer.dao.inviteSaAllocateRule.InviteSaAllocateRuleDetailMapper;
import com.yonyou.dmscus.customer.service.inviteSaAllocateRule.InviteSaAllocateRuleDetailService;
import com.yonyou.dmscus.customer.entity.dto.inviteSaAllocateRule.InviteSaAllocateRuleDetailDTO;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * SA分配规则明细表,通过经销商CODE与invite_sa_allocate_rule关联，用于经销商SA分配中平均分配 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
@Service
public class InviteSaAllocateRuleDetailServiceImpl implements InviteSaAllocateRuleDetailService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteSaAllocateRuleDetailMapper inviteSaAllocateRuleDetailMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                          分页对象
     * @param inviteSaAllocateRuleDetailDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto
                     *   . InviteSaAllocateRuleDetailDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<InviteSaAllocateRuleDetailDTO> selectPageBysql(Page page, InviteSaAllocateRuleDetailDTO
            inviteSaAllocateRuleDetailDTO) {
        if (inviteSaAllocateRuleDetailDTO == null) {
            inviteSaAllocateRuleDetailDTO = new InviteSaAllocateRuleDetailDTO();
        }
        InviteSaAllocateRuleDetailPO inviteSaAllocateRuleDetailPO = inviteSaAllocateRuleDetailDTO.transDtoToPo
                (InviteSaAllocateRuleDetailPO.class);

        List<InviteSaAllocateRuleDetailPO> list = inviteSaAllocateRuleDetailMapper.selectPageBySql(page,
                inviteSaAllocateRuleDetailPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteSaAllocateRuleDetailDTO> result = list.stream().map(m -> m.transPoToDto
                    (InviteSaAllocateRuleDetailDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param inviteSaAllocateRuleDetailDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.inviteSaAllocateRule.InviteSaAllocateRuleDetailDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<InviteSaAllocateRuleDetailDTO> selectListBySql(InviteSaAllocateRuleDetailDTO
                                                                           inviteSaAllocateRuleDetailDTO) {
        if (inviteSaAllocateRuleDetailDTO == null) {
            inviteSaAllocateRuleDetailDTO = new InviteSaAllocateRuleDetailDTO();
        }
        InviteSaAllocateRuleDetailPO inviteSaAllocateRuleDetailPO = inviteSaAllocateRuleDetailDTO.transDtoToPo
                (InviteSaAllocateRuleDetailPO.class);
        List<InviteSaAllocateRuleDetailPO> list = inviteSaAllocateRuleDetailMapper.selectListBySql
                (inviteSaAllocateRuleDetailPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteSaAllocateRuleDetailDTO.class)).collect(Collectors
                    .toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.inviteSaAllocateRule.InviteSaAllocateRuleDetailDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public InviteSaAllocateRuleDetailDTO getById(Long id) {
        InviteSaAllocateRuleDetailPO inviteSaAllocateRuleDetailPO = inviteSaAllocateRuleDetailMapper.selectById(id);
        if (inviteSaAllocateRuleDetailPO != null) {
            return inviteSaAllocateRuleDetailPO.transPoToDto(InviteSaAllocateRuleDetailDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param inviteSaAllocateRuleDetailDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(InviteSaAllocateRuleDetailDTO inviteSaAllocateRuleDetailDTO) {
        //对对象进行赋值操作
        InviteSaAllocateRuleDetailPO inviteSaAllocateRuleDetailPO = inviteSaAllocateRuleDetailDTO.transDtoToPo
                (InviteSaAllocateRuleDetailPO.class);
        //执行插入
        int row = inviteSaAllocateRuleDetailMapper.insert(inviteSaAllocateRuleDetailPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                            主键ID
     * @param inviteSaAllocateRuleDetailDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, InviteSaAllocateRuleDetailDTO inviteSaAllocateRuleDetailDTO) {
        InviteSaAllocateRuleDetailPO inviteSaAllocateRuleDetailPO = inviteSaAllocateRuleDetailMapper.selectById(id);
        //对对象进行赋值操作
        inviteSaAllocateRuleDetailDTO.transDtoToPo(inviteSaAllocateRuleDetailPO);
        //执行更新
        int row = inviteSaAllocateRuleDetailMapper.updateById(inviteSaAllocateRuleDetailPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = inviteSaAllocateRuleDetailMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = inviteSaAllocateRuleDetailMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    @Override
    public List<InviteSaAllocateRuleDetailDTO> getSaAllocateRule(String dealerCode) {
        InviteSaAllocateRuleDetailPO inviteSaAllocateRuleDetailPO=new InviteSaAllocateRuleDetailPO();
        if(dealerCode==null){
            //获取登录用户信息
            LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
            dealerCode=loginInfoDto.getOwnerCode();
        }
        inviteSaAllocateRuleDetailPO.setDealerCode(dealerCode);
        inviteSaAllocateRuleDetailPO.setIsInsurance(10041001);
        List<InviteSaAllocateRuleDetailPO> list = inviteSaAllocateRuleDetailMapper.selectListBySql
                (inviteSaAllocateRuleDetailPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteSaAllocateRuleDetailDTO.class)).collect(Collectors
                    .toList());
        }
    }

    @Override
    public int saveSaAllocateRuleDetail(List<InviteSaAllocateRuleDetailDTO> list) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if(!CommonUtils.isNullOrEmpty(list)){
            for (InviteSaAllocateRuleDetailDTO dto : list) {
                InviteSaAllocateRuleDetailPO po = dto.transDtoToPo(InviteSaAllocateRuleDetailPO.class);
                //门店code
                po.setDealerCode(loginInfoDto.getOwnerCode());
                po.setIsInsurance(10041001);
                if(dto.getStatus().equals("A")){
                    //新增
                    inviteSaAllocateRuleDetailMapper.insert(po);
                }else if(dto.getStatus().equals("D")){
                    //删除
                    inviteSaAllocateRuleDetailMapper.deleteById(dto.getId());
                }else if(dto.getStatus().equals("U")){
                    //更新
                    inviteSaAllocateRuleDetailMapper.updateById(po);
                }
            }
        }
        return 1;
    }
}
