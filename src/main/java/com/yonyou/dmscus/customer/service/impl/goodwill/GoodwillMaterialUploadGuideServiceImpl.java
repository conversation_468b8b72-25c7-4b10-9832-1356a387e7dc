package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillMaterialUploadGuideMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMaterialUploadGuideDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMaterialUploadGuidePO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillMaterialUploadGuideService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 亲善材料上传指南 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
@Service
public class GoodwillMaterialUploadGuideServiceImpl
		extends ServiceImpl<GoodwillMaterialUploadGuideMapper, GoodwillMaterialUploadGuidePO>
		implements GoodwillMaterialUploadGuideService {
	/** 日志对象 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillMaterialUploadGuideMapper goodwillMaterialUploadGuideMapper;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillMaterialUploadGuideDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.GoodwillMaterialUploadGuideDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillMaterialUploadGuideDTO> selectPageBysql(Page page,
			GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO) {
		if (goodwillMaterialUploadGuideDTO == null) {
			goodwillMaterialUploadGuideDTO = new GoodwillMaterialUploadGuideDTO();
		}
		GoodwillMaterialUploadGuidePO goodwillMaterialUploadGuidePo = goodwillMaterialUploadGuideDTO
				.transDtoToPo(GoodwillMaterialUploadGuidePO.class);

		List<GoodwillMaterialUploadGuidePO> list = goodwillMaterialUploadGuideMapper.selectPageBySql(page,
				goodwillMaterialUploadGuidePo);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillMaterialUploadGuideDTO> result = list.stream()
					.map(m -> m.transPoToDto(GoodwillMaterialUploadGuideDTO.class)).collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillMaterialUploadGuideDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.GoodwillMaterialUploadGuideDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillMaterialUploadGuideDTO selecMaterialtInfo(
			GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO) {
		if (goodwillMaterialUploadGuideDTO == null) {
			goodwillMaterialUploadGuideDTO = new GoodwillMaterialUploadGuideDTO();
		}
		// GoodwillMaterialUploadGuidePO goodwillMaterialUploadGuidePo
		// =goodwillMaterialUploadGuideDTO.transDtoToPo(GoodwillMaterialUploadGuidePO.class);
		GoodwillMaterialUploadGuidePO list = goodwillMaterialUploadGuideMapper
				.selecMaterialtInfo(goodwillMaterialUploadGuideDTO);
		if (list != null) {
			return list.transPoToDto(GoodwillMaterialUploadGuideDTO.class);
		} else {
			return null;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.GoodwillMaterialUploadGuideDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillMaterialUploadGuideDTO getById(Long id) {
		GoodwillMaterialUploadGuidePO goodwillMaterialUploadGuidePo = goodwillMaterialUploadGuideMapper.selectById(id);
		if (goodwillMaterialUploadGuidePo != null) {
			return goodwillMaterialUploadGuidePo.transPoToDto(GoodwillMaterialUploadGuideDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillMaterialUploadGuideDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO) {
		// 对对象进行赋值操作
		GoodwillMaterialUploadGuidePO goodwillMaterialUploadGuidePo = goodwillMaterialUploadGuideDTO
				.transDtoToPo(GoodwillMaterialUploadGuidePO.class);
		// 执行插入
		int row = goodwillMaterialUploadGuideMapper.insert(goodwillMaterialUploadGuidePo);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillMaterialUploadGuideDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO) {
		GoodwillMaterialUploadGuidePO goodwillMaterialUploadGuidePo = goodwillMaterialUploadGuideMapper.selectById(id);
		// 对对象进行赋值操作
		goodwillMaterialUploadGuideDTO.transDtoToPo(goodwillMaterialUploadGuidePo);
		// 执行更新
		int row = goodwillMaterialUploadGuideMapper.updateById(goodwillMaterialUploadGuidePo);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillMaterialUploadGuideMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillMaterialUploadGuideMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 *
	 * @param talkskillTypeDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020/4/16
	 */
	@Override
	public int updateList(GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO) {

		if (goodwillMaterialUploadGuideDTO.getId() == null) {
			// 执行新增
			// 对对象进行赋值操作
			GoodwillMaterialUploadGuidePO goodwillMaterialUploadGuidePo = goodwillMaterialUploadGuideDTO
					.transDtoToPo(GoodwillMaterialUploadGuidePO.class);

			goodwillMaterialUploadGuideMapper.insert(goodwillMaterialUploadGuidePo);
		} else {
			// 执行更新
			GoodwillMaterialUploadGuidePO goodwillMaterialUploadGuidePo = goodwillMaterialUploadGuideMapper
					.selectById(goodwillMaterialUploadGuideDTO.getId());
			// 对对象进行赋值操作
			goodwillMaterialUploadGuideDTO.transDtoToPo(goodwillMaterialUploadGuidePo);
			goodwillMaterialUploadGuideMapper.updateById(goodwillMaterialUploadGuidePo);
		}

		return 1;
	}
}
