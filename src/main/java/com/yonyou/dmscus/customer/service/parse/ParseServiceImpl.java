package com.yonyou.dmscus.customer.service.parse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.constants.clue.DefaultMatchRuleEnum;
import com.yonyou.dmscus.customer.dao.Parse.ParseMapper;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentClueContact;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.OwnerVehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.OwnerVehicleVO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.PageRequestDTO;
import com.yonyou.dmscus.customer.entity.dto.parse.ParseDto;
import com.yonyou.dmscus.customer.entity.po.parse.ParseLogPo;
import com.yonyou.dmscus.customer.entity.po.parse.ParsePo;
import com.yonyou.dmscus.customer.entity.vo.AccidentClueVo;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ParseServiceImpl extends ServiceImpl<ParseMapper,ParsePo > implements ParseService {


    private final ParseConfigService parseConfigService;

    private final ParseLogService parseLogService;

    private final BusinessPlatformService businessPlatformService;

    public ParseServiceImpl(ParseConfigService parseConfigService, ParseLogService parseLogService, BusinessPlatformService businessPlatformService) {
        this.parseConfigService = parseConfigService;
        this.parseLogService = parseLogService;
        this.businessPlatformService = businessPlatformService;
    }

    @Override
    public AccidentClueVo parseContent(String param) {
        LoginInfoDto loginInfo = FrameworkUtil.getLoginInfo();
        List<ParseLogPo> poList=new ArrayList<>();
        List<ParseDto> parseDtoList=parseConfigService.getByParseIdList(null,0);
        if(CollectionUtils.isEmpty(parseDtoList)){
            log.error("未查询到解析项目对应的正则信息");
            return new AccidentClueVo();
        }
        Long resultId =null;
        boolean isFinished=false;
        for (ParseDto parseDto : parseDtoList) {
            String matchingRule = parseDto.getMatchingRule();
            Pattern compile = Pattern.compile(matchingRule);
            Matcher matcher = compile.matcher(param);
            while (matcher.find()){
                resultId=parseDto.getParseId();
                isFinished=true;
            }
        }
        List<ParseDto> parseIdList = parseConfigService.getByParseIdList(Collections.singletonList(resultId), 1);
        if(!isFinished){
            log.error("未匹配到文本对应保险公司");
            parseIdList= parseConfigService.getByParseIdList(Collections.singletonList(99999L), 1);
        }
        AccidentClueVo vo = this.getAccidentClueVo(param, poList, parseIdList,loginInfo);
        ParsePo parseId = this.getById(resultId);
        if(parseId!=null){
            vo.setInsuranceCompanyName(parseId.getApplyTo());
            vo.setInsuranceCompanyId(parseId.getCompanyCode());
        }else {
            log.info("没有查到解析模板id{}对应的保险公司信息",resultId);
        }
        parseLogService.saveLogInfo(poList);
        return vo;
    }


    /*
    * 根据解析规则拼装vo
    * */
    private AccidentClueVo getAccidentClueVo(String param, List<ParseLogPo> poList, List<ParseDto> parseDtoList, LoginInfoDto loginInfo) {
        AccidentClueVo vo = new AccidentClueVo();
        List<String> contactNameList = new ArrayList<>(CommonConstants.NUM_5);
        List<String> contactPhoneList = new ArrayList<>(CommonConstants.NUM_5);
        for (ParseDto parseDto : parseDtoList) {

            String matchingRule = this.getMatchRuleValue(parseDto);
            Pattern compile = Pattern.compile(matchingRule);
            Matcher matcher = compile.matcher(param);
            ParseLogPo parseLogPo = new ParseLogPo();
            this.setSaveParam(param, loginInfo, parseDto, parseLogPo);
            this.putParseValToVo(param, vo, parseDto, matcher, parseLogPo, contactNameList, contactPhoneList);
            poList.add(parseLogPo);
        }
        List<AccidentClueContact> contactList = this.getContactList(contactNameList, contactPhoneList);
        vo.setContactList(contactList);
        if(!StringUtils.isEmpty(vo.getLicense())){
            PageRequestDTO<OwnerVehicleDTO> dto = new PageRequestDTO<>();
            OwnerVehicleDTO vehicleDTO = new OwnerVehicleDTO();
            vehicleDTO.setPlateNumber(vo.getLicense());
            dto.setData(vehicleDTO);
            IPage<OwnerVehicleVO> vehicleVo = businessPlatformService.getVehicle(dto);
            log.info("根据车牌号{}查到的车辆信息:{}",vo.getLicense(),vehicleVo);
            if(vehicleVo!=null&&!CollectionUtils.isEmpty(vehicleVo.getRecords())){
                OwnerVehicleVO ownerVehicleVO = vehicleVo.getRecords().get(0);
                if(ownerVehicleVO!=null){
                    String mobile = ownerVehicleVO.getMobile();
                    String contactorMobile = ownerVehicleVO.getContactorMobile();
                    String contactorPhone = ownerVehicleVO.getContactorPhone();
                    String mobileNum = !StringUtils.isEmpty(mobile) ? mobile : (!StringUtils.isEmpty(contactorMobile) ? contactorMobile : contactorPhone);
                    if(!StringUtil.equals(mobileNum,vo.getContactsPhone())){
                        vo.setContactsPhone(mobileNum);
                    }
                    if(StringUtils.isEmpty(vo.getContacts())){
                        vo.setContacts(ownerVehicleVO.getContactName());
                    }
                }
            }
        }

        return vo;
    }

    private String getMatchRuleValue(ParseDto parseDto){

        return ObjectUtils.nullSafeEquals(parseDto.getMatchingRule(), DefaultMatchRuleEnum.MATCH_RULE_NAME_DEFAULT.getType())
                ? DefaultMatchRuleEnum.MATCH_RULE_NAME_DEFAULT.getValue()
                : ObjectUtils.nullSafeEquals(parseDto.getMatchingRule(), DefaultMatchRuleEnum.MATCH_RULE_NAME_SECRET.getType())
                ? DefaultMatchRuleEnum.MATCH_RULE_NAME_SECRET.getValue() : parseDto.getMatchingRule();
    }

    /*
    * 根据表中配置解析规则 解析数据到vo
    * */
    private void putParseValToVo(String param, AccidentClueVo vo, ParseDto parseDto, Matcher matcher, ParseLogPo parseLogPo, List<String> contactNameList, List<String> contactPhoneList) {
        while (matcher.find()){
            Object group = matcher.group();
            log.info("符合匹配的值:{}",group.toString());
            Class<AccidentClueVo> accidentClueVoClass = AccidentClueVo.class;
            try {
                Field field = accidentClueVoClass.getDeclaredField(parseDto.getFieldName());
                if(!StringUtils.isEmpty(group)){
                    field.setAccessible(true);
                    if(parseDto.getParseIndex()!=null&& parseDto.getParseIndex()==1){
                        try {
                            String result = matcher.group(1);
                            if(!StringUtils.isEmpty(result)){
                                this.setContactInfo(field, result, contactNameList, contactPhoneList);
                                group= result;
                            }
                        } catch (Exception e) {
                            log.error("当前字段无中间值");
                            parseLogPo.setParseText(e.getMessage().substring(0, Math.min(200, e.getMessage().length())));
                        }
                    }
                    if(field.getType().equals(Date.class)){
                        try {
                            SimpleDateFormat format = new SimpleDateFormat(parseDto.getRuleText());
                            group = format.parse(group.toString());
                        } catch (ParseException e) {
                            parseLogPo.setParseText(e.getMessage().substring(0, Math.min(200, e.getMessage().length())));
                        }
                    }
                    if(group instanceof String){
                        group=((String) group).trim();
                    }
                    field.set(vo,group);
                    parseLogPo.setParseText(String.valueOf(group));
                    param = param.replace(String.valueOf(group),"");
                    parseLogPo.setParseResult(true);
                }else {
                    log.info("没有解析到数据");
                }
            } catch (Exception  e) {
                log.error("没有找到对应属性",e);
                e.printStackTrace();
            }
        }
    }

    private List<AccidentClueContact> getContactList(List<String> contactNameList, List<String> contactPhoneList){

        log.info("识别到的姓名： {}", contactNameList);
        log.info("识别到的手机号： {}", contactPhoneList);
        List<AccidentClueContact> result = new ArrayList<>(CommonConstants.NUM_5);
        List<String> nameList = new ArrayList<>(new LinkedHashSet<>(contactNameList));
        List<String> phoneList = new ArrayList<>(new LinkedHashSet<>(contactPhoneList));
        for (int i = 0; i < Math.min(nameList.size(), phoneList.size()) ; i++) {
            result.add(new AccidentClueContact(nameList.get(i), phoneList.get(i), CommonConstants.DICT_IS_NO));
        }

        return result;
    }

    private void setContactInfo(Field field, String result, List<String> contactNameList, List<String> contactPhoneList){

        if ("contacts".equals(field.getName())){
            contactNameList.add(result);
        }
        if ("contactsPhone".equals(field.getName())){
            contactPhoneList.add(result);
        }
    }


    private void setSaveParam(String param, LoginInfoDto loginInfo, ParseDto parseDto, ParseLogPo parseLogPo) {
        parseLogPo.setParseId(parseDto.getParseId());
        parseLogPo.setConfigId(parseDto.getId());
        parseLogPo.setMsgText(param);
        parseLogPo.setParseResult(false);
        parseLogPo.setCreateSqlby(String.valueOf(loginInfo.getUserId()));
        parseLogPo.setUpdateSqlby(String.valueOf(loginInfo.getUserId()));
    }

    @Override
    public List<Long> getTextMatch(Long parseId) {
        LambdaQueryWrapper<ParsePo> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ParsePo::getId);
        List<ParsePo> list = list(wrapper);
        if(CollectionUtils.isEmpty(list)){
            log.error("未查询到对应解析数据:{}",parseId);
            return Collections.emptyList();
        }
        return list.stream().map(ParsePo::getId).collect(Collectors.toList());
    }
}
