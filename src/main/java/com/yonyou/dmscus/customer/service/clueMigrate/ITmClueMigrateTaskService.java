package com.yonyou.dmscus.customer.service.clueMigrate;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yonyou.dmscloud.function.domains.dto.ImportResultDto;
import com.yonyou.dmscus.customer.dto.clueMigrate.*;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.CompanyDetailDTO;
import com.yonyou.dmscus.customer.entity.po.clueMigrate.TmClueMigrateTask;
import com.yonyou.dmscus.customer.entity.po.dealermigrationrecord.btnlog.DealerMigrationRecordPo;
import com.yonyou.dmscus.customer.service.common.CommonService;
import com.yonyou.dmscus.customer.vo.clueMigrate.ClueMigrateTaskVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 线索迁移任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
public interface ITmClueMigrateTaskService extends CommonService<ClueMigrateTaskVO, ClueMigrateTaskAddDTO, ClueMigrateTaskQueryDTO, TmClueMigrateTask> {

    void createDealerToDealerTask( ClueMigrateTaskAddDTO clueMigrateTaskAddDTO );
    void createDealerToDealerTask( ClueMigrateTaskAddDTO clueMigrateTaskAddDTO, CompanyDetailDTO companyDetailDTO );

    void createVinToDealerTask( ClueMigrateTaskAddDTO clueMigrateTaskAddDTO );
    void createVinToDealerTask( ClueMigrateTaskAddDTO clueMigrateTaskAddDTO, CompanyDetailDTO companyDetailDTO );

    IPage<ClueMigrateTaskVO> getDealerToDealerMigrateTaskVOPageList( ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO );

    IPage<ClueMigrateTaskVO> getVinToDealerMigrateTaskVOPageList( ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO );

    List<ClueMigrateTaskVO> getDealerToDealerMigrateTaskVOList( ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO );

    List<ClueMigrateTaskVO> getVinToDealerMigrateTaskVOList( ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO );

    IPage<ClueMigrateTaskVO> pageList( ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO );

    List<ClueMigrateTaskVO> getClueMigrateTaskVOList( ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO );

    void exportDealerToDealerClueMigrateTaskList(ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO, HttpServletRequest request, HttpServletResponse response );

    void exportVinToDealerClueMigrateTaskList(ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO, HttpServletRequest request, HttpServletResponse response );

    void exportClueMigrateTaskList(ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO, HttpServletRequest request, HttpServletResponse response );


    ImportResultDto<DealerToDealerTaskImportDTO> importDealerToDealerClueMigrateTask(MultipartFile importFile ) throws IOException;

    ImportResultDto<VinToDealerTaskImportDTO> importVinToDealerClueMigrateTask(MultipartFile importFile ) throws IOException;

    /**
     * 从中台查询经销商信息
     * @param selectDTO
     * @return
     */
    List<CompanyDetailDTO> getCompanyListInfo(CompanyNewSelectDTO selectDTO);

    /**
     * 从中台查询经销商信息
     * @param dealerCodeList 经销商code列表
     * @return
     */
    List<CompanyDetailDTO> getCompanyInfoListByDealerCodeList(List<String> dealerCodeList );

    /**
     * 从中台查询经销商信息
     * @param dealerCode
     * @return
     */
    CompanyDetailDTO getCompanyInfoByDealerCode(String dealerCode );

    /**
     * 判断经销商是否停止营业
     *
     * @param companyDetailDTO@return
     */
    boolean isStopService( CompanyDetailDTO companyDetailDTO);

    boolean deleteById(Long id);

    IPage<DealerMigrationRecordPo> leadsTransfer(int currentPage, int pageSize,Integer migrationType);

    void cleanDealerToDealerTask();
}
