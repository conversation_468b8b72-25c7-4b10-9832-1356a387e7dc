package com.yonyou.dmscus.customer.service.impl.inviteVehicleDealerAllocate;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.po.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryPO;
import com.yonyou.dmscus.customer.dao.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryMapper;
import com.yonyou.dmscus.customer.service.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryService;
import com.yonyou.dmscus.customer.entity.dto.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryDTO;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 车店分配历史表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@Service
public class InviteVehicleDealerAllocateHistoryServiceImpl implements InviteVehicleDealerAllocateHistoryService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteVehicleDealerAllocateHistoryMapper inviteVehicleDealerAllocateHistoryMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                                  分页对象
     * @param inviteVehicleDealerAllocateHistoryDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto
                       *       .       InviteVehicleDealerAllocateHistoryDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<InviteVehicleDealerAllocateHistoryDTO> selectPageBysql(Page page,
                                                                        InviteVehicleDealerAllocateHistoryDTO
                                                                                inviteVehicleDealerAllocateHistoryDTO) {
        if (inviteVehicleDealerAllocateHistoryDTO == null) {
            inviteVehicleDealerAllocateHistoryDTO = new InviteVehicleDealerAllocateHistoryDTO();
        }
        InviteVehicleDealerAllocateHistoryPO inviteVehicleDealerAllocateHistoryPO =
                inviteVehicleDealerAllocateHistoryDTO.transDtoToPo(InviteVehicleDealerAllocateHistoryPO.class);

        List<InviteVehicleDealerAllocateHistoryPO> list = inviteVehicleDealerAllocateHistoryMapper.selectPageBySql
                (page, inviteVehicleDealerAllocateHistoryPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteVehicleDealerAllocateHistoryDTO> result = list.stream().map(m -> m.transPoToDto
                    (InviteVehicleDealerAllocateHistoryDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param inviteVehicleDealerAllocateHistoryDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<InviteVehicleDealerAllocateHistoryDTO> selectListBySql(InviteVehicleDealerAllocateHistoryDTO
                                                                               inviteVehicleDealerAllocateHistoryDTO) {
        if (inviteVehicleDealerAllocateHistoryDTO == null) {
            inviteVehicleDealerAllocateHistoryDTO = new InviteVehicleDealerAllocateHistoryDTO();
        }
        InviteVehicleDealerAllocateHistoryPO inviteVehicleDealerAllocateHistoryPO =
                inviteVehicleDealerAllocateHistoryDTO.transDtoToPo(InviteVehicleDealerAllocateHistoryPO.class);
        List<InviteVehicleDealerAllocateHistoryPO> list = inviteVehicleDealerAllocateHistoryMapper.selectListBySql
                (inviteVehicleDealerAllocateHistoryPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteVehicleDealerAllocateHistoryDTO.class)).collect
                    (Collectors
                            .toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public InviteVehicleDealerAllocateHistoryDTO getById(Long id) {
        InviteVehicleDealerAllocateHistoryPO inviteVehicleDealerAllocateHistoryPO =
                inviteVehicleDealerAllocateHistoryMapper.selectById(id);
        if (inviteVehicleDealerAllocateHistoryPO != null) {
            return inviteVehicleDealerAllocateHistoryPO.transPoToDto(InviteVehicleDealerAllocateHistoryDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param inviteVehicleDealerAllocateHistoryDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(InviteVehicleDealerAllocateHistoryDTO inviteVehicleDealerAllocateHistoryDTO) {
        //对对象进行赋值操作
        InviteVehicleDealerAllocateHistoryPO inviteVehicleDealerAllocateHistoryPO =
                inviteVehicleDealerAllocateHistoryDTO.transDtoToPo(InviteVehicleDealerAllocateHistoryPO.class);
        //执行插入
        int row = inviteVehicleDealerAllocateHistoryMapper.insert(inviteVehicleDealerAllocateHistoryPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                                    主键ID
     * @param inviteVehicleDealerAllocateHistoryDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, InviteVehicleDealerAllocateHistoryDTO inviteVehicleDealerAllocateHistoryDTO) {
        InviteVehicleDealerAllocateHistoryPO inviteVehicleDealerAllocateHistoryPO =
                inviteVehicleDealerAllocateHistoryMapper.selectById(id);
        //对对象进行赋值操作
        inviteVehicleDealerAllocateHistoryDTO.transDtoToPo(inviteVehicleDealerAllocateHistoryPO);
        //执行更新
        int row = inviteVehicleDealerAllocateHistoryMapper.updateById(inviteVehicleDealerAllocateHistoryPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = inviteVehicleDealerAllocateHistoryMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = inviteVehicleDealerAllocateHistoryMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 查询分配历史
     *
     * @param vin
     * @return
     */
    @Override
    public List<InviteVehicleDealerAllocateHistoryDTO> getAllocationHistory(String vin) {
        LambdaQueryWrapper<InviteVehicleDealerAllocateHistoryPO> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(InviteVehicleDealerAllocateHistoryPO::getVin, vin);
        List<InviteVehicleDealerAllocateHistoryPO> list = inviteVehicleDealerAllocateHistoryMapper.selectList
                (queryWrapper);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteVehicleDealerAllocateHistoryDTO.class)).collect
                    (Collectors
                            .toList());
        }
    }
}
