package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintInfoMapper;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoPO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintFollowPO;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintFollowMapper;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoPO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintInfoService;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintFollowService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintFollowDTO;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintInfoService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 销售客户投诉跟进表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-11
 */
@Service
public class SaleComplaintFollowServiceImpl implements SaleComplaintFollowService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    SaleComplaintFollowMapper saleComplaintFollowMapper;

    @Autowired
    SaleComplaintInfoService saleComplaintInfoService;

    @Resource
    SaleComplaintInfoMapper saleComplaintInfoMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                   分页对象
     * @param saleComplaintFollowDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintFollowDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<SaleComplaintFollowDTO> selectPageBysql(Page page, SaleComplaintFollowDTO saleComplaintFollowDTO) {
        if (saleComplaintFollowDTO == null) {
            saleComplaintFollowDTO = new SaleComplaintFollowDTO();
        }
        SaleComplaintFollowPO saleComplaintFollowPO = saleComplaintFollowDTO.transDtoToPo(SaleComplaintFollowPO.class);

        List<SaleComplaintFollowPO> list = saleComplaintFollowMapper.selectPageBySql(page, saleComplaintFollowPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<SaleComplaintFollowDTO> result = list.stream().map(m -> m.transPoToDto(SaleComplaintFollowDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param saleComplaintFollowDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintFollowDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<SaleComplaintFollowDTO> selectListBySql(String flag,SaleComplaintFollowDTO saleComplaintFollowDTO) {
        if (saleComplaintFollowDTO == null) {
            saleComplaintFollowDTO = new SaleComplaintFollowDTO();
        }
        SaleComplaintFollowPO saleComplaintFollowPO = saleComplaintFollowDTO.transDtoToPo(SaleComplaintFollowPO.class);
        List<SaleComplaintFollowPO> list = new ArrayList<>();

        String dealer="dealer";
        if(flag.equals(dealer)){
            list = saleComplaintFollowMapper.selectListByDealer(saleComplaintFollowPO);
        }else {
            list = saleComplaintFollowMapper.selectListByVcdc(saleComplaintFollowPO);
        }
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(SaleComplaintFollowDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintFollowDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public SaleComplaintFollowDTO getById(Long id) {
        SaleComplaintFollowPO saleComplaintFollowPO = saleComplaintFollowMapper.selectById(id);
        if (saleComplaintFollowPO != null) {
            return saleComplaintFollowPO.transPoToDto(SaleComplaintFollowDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param saleComplaintFollowDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(SaleComplaintFollowDTO saleComplaintFollowDTO) {
        //对对象进行赋值操作
        SaleComplaintFollowPO saleComplaintFollowPO = saleComplaintFollowDTO.transDtoToPo(SaleComplaintFollowPO.class);
        //执行插入
        int row = saleComplaintFollowMapper.insert(saleComplaintFollowPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                     主键ID
     * @param saleComplaintFollowDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, SaleComplaintFollowDTO saleComplaintFollowDTO) {
        SaleComplaintFollowPO saleComplaintFollowPO = saleComplaintFollowMapper.selectById(id);
        //对对象进行赋值操作
        saleComplaintFollowDTO.transDtoToPo(saleComplaintFollowPO);
        //执行更新
        int row = saleComplaintFollowMapper.updateById(saleComplaintFollowPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = saleComplaintFollowMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 新增经销商跟进内容
     *
     * @param complaintCustomFieldTestDto
     * @return
     */
    @Override
    public int insertcCus(ComplaintCustomFieldTestDTO complaintCustomFieldTestDto) throws ParseException {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        SaleComplaintFollowDTO saleComplaintFollowDTO = new SaleComplaintFollowDTO();
        if (!StringUtils.isNullOrEmpty(complaintCustomFieldTestDto.getPlanFollowTime())) {
            Date planFollowTime = sf.parse(complaintCustomFieldTestDto.getPlanFollowTime());
            saleComplaintFollowDTO.setPlanFollowTime(planFollowTime);
        }
        long complaintInfoId=complaintCustomFieldTestDto.getComplaintInfoId();
        //查询经销商首次回复时间是否存在
        SaleComplaintInfoDTO saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        List<SaleComplaintInfoPO> list=saleComplaintInfoMapper.queryDealerFirstReplyTime(complaintInfoId);
        if(list.get(0).getDealerFisrtReplyTime()==null){
            saleComplaintInfoDTO.setDealerFisrtReplyTime(new Date());
            saleComplaintInfoService.update(complaintInfoId,saleComplaintInfoDTO);

        }
        //查询案子是否重启
        if(list.get(0).getFisrtRestartTime()!=null&&list.get(0).getFisrtRestartTime()==null){
            saleComplaintInfoDTO.setFisrtRestartDealerFisrtReplyTime(new Date());
            saleComplaintInfoService.update(complaintInfoId,saleComplaintInfoDTO);
        }
        if(!StringUtils.isNullOrEmpty(complaintCustomFieldTestDto.getFollowContent())){
            saleComplaintInfoDTO.setWorkOrderStatus(82451002);
            saleComplaintInfoService.update(complaintInfoId,saleComplaintInfoDTO);
        }
        saleComplaintFollowDTO.setActuallFollowTime2(new Date());
        saleComplaintFollowDTO.setFollowTime(new Date());
        saleComplaintFollowDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
        saleComplaintFollowDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
        saleComplaintFollowDTO.setFollowContent(complaintCustomFieldTestDto.getFollowContent());
        saleComplaintFollowDTO.setComplaintInfoId(complaintInfoId);
        saleComplaintFollowDTO.setFollower(FrameworkUtil.getLoginInfo().getUserId().toString());
        saleComplaintFollowDTO.setDealerNotPublish(false);
        saleComplaintFollowDTO.setCcmNotPublish(true);
        saleComplaintInfoService.IssuedUpdataData(saleComplaintFollowDTO,FrameworkUtil.getLoginInfo().getOwnerCode(),"跟进中");
        return insert(saleComplaintFollowDTO);
    }

    @Override
    public int reportVeh(ComplaintCustomFieldTestDTO complaintCustomFieldTestDto) throws ParseException {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SaleComplaintFollowDTO saleComplaintFollowDTO = new SaleComplaintFollowDTO();
        if (complaintCustomFieldTestDto.getPlanFollowTime() != null) {
            Date planFollowTime = sf.parse(complaintCustomFieldTestDto.getPlanFollowTime());
            saleComplaintFollowDTO.setPlanFollowTime(planFollowTime);
        }
        long complaintInfoId=complaintCustomFieldTestDto.getComplaintInfoId();
        //查询经销商首次回复时间是否存在
        SaleComplaintInfoDTO saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        List<SaleComplaintInfoPO> list=saleComplaintInfoMapper.queryDealerFirstReplyTime(complaintInfoId);
        if(list.get(0).getDealerFisrtReplyTime()==null){
            saleComplaintInfoDTO.setDealerFisrtReplyTime(new Date());
            saleComplaintInfoService.update(complaintInfoId,saleComplaintInfoDTO);

        }
        SaleComplaintInfoPO saleComplaintInfopo = new SaleComplaintInfoPO();
        saleComplaintFollowDTO.setFollowTime(new Date());
        saleComplaintFollowDTO.setFollowContent(complaintCustomFieldTestDto.getFollowContent());

        saleComplaintFollowDTO.setComplaintInfoId(complaintCustomFieldTestDto.getComplaintInfoId());
        saleComplaintFollowDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
        saleComplaintFollowDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
        saleComplaintFollowDTO.setDealerNotPublish(true);
        saleComplaintFollowDTO.setCcmNotPublish(true);
        saleComplaintInfopo.setReport(true);
        if(!StringUtils.isNullOrEmpty(complaintCustomFieldTestDto.getFollowContent())){
            saleComplaintInfopo.setWorkOrderStatus(82451002);
        }
        saleComplaintInfoDTO.setId(complaintCustomFieldTestDto.getComplaintInfoId());
        LambdaQueryWrapper<SaleComplaintInfoPO> wrapper = new QueryWrapper<SaleComplaintInfoPO>().lambda();
        wrapper.eq(SaleComplaintInfoPO::getId, saleComplaintInfoDTO.getId());
        saleComplaintInfoMapper.update(saleComplaintInfopo, wrapper);
        saleComplaintInfoService.IssuedUpdataData(saleComplaintFollowDTO,FrameworkUtil.getLoginInfo().getOwnerCode(),"跟进中");
        return insert(saleComplaintFollowDTO);
    }
    /**
     * 区域经理跟进内容
     *
     * @param complaintCustomFieldTestDto
     * @return
     */
    @Override
    public int insertRegionCus(ComplaintCustomFieldTestDTO complaintCustomFieldTestDto)throws ParseException  {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        SaleComplaintFollowDTO saleComplaintFollowDTO = new SaleComplaintFollowDTO();
        if (!StringUtils.isNullOrEmpty(complaintCustomFieldTestDto.getPlanFollowTime())) {
            Date planFollowTime = sf.parse(complaintCustomFieldTestDto.getPlanFollowTime());
            saleComplaintFollowDTO.setPlanFollowTime(planFollowTime);
        }
        long complaintInfoId=complaintCustomFieldTestDto.getComplaintInfoId();
        saleComplaintFollowDTO.setActuallFollowTime2(new Date());
        saleComplaintFollowDTO.setFollowTime(new Date());
        saleComplaintFollowDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
        saleComplaintFollowDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
        saleComplaintFollowDTO.setFollowContent(complaintCustomFieldTestDto.getFollowContent());
        saleComplaintFollowDTO.setComplaintInfoId(complaintInfoId);
        saleComplaintFollowDTO.setFollower(FrameworkUtil.getLoginInfo().getUserId().toString());
        saleComplaintFollowDTO.setDealerNotPublish(false);
        saleComplaintFollowDTO.setCcmNotPublish(true);
        saleComplaintInfoService.IssuedUpdataData(saleComplaintFollowDTO,"厂端","跟进中");
        return insert(saleComplaintFollowDTO);
    }

    @Override
    public List<ComplaintInfMoreDTO> queryNextFollowing() {
        return saleComplaintFollowMapper.queryNextFollowing();
    }


}
