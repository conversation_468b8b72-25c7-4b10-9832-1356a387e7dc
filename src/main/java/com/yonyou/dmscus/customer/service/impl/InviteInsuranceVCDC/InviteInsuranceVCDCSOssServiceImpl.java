package com.yonyou.dmscus.customer.service.impl.InviteInsuranceVCDC;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.dmscloud.framework.domains.dto.basedata.ResponseDTO;
import com.yonyou.dmscloud.framework.service.excel.ExcelExportColumn;
import com.yonyou.dmscus.customer.bean.dto.DownloadDTO;
import com.yonyou.dmscus.customer.configuration.InnerUrlProperties;
import com.yonyou.dmscus.customer.configuration.SalesUrlProperties;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDTO;
import com.yonyou.dmscus.customer.service.inviteInsuranceVCDC.InviteInsuranceVCDCSOssService;
import com.yonyou.dmscus.customer.util.common.VolvoHttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class InviteInsuranceVCDCSOssServiceImpl implements InviteInsuranceVCDCSOssService {

    /**
     * 日志对象
     */
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    VolvoHttpUtils volvoHttpUtils;

    @Resource
    private RestTemplate directRestTemplate;

    @Autowired
    private SalesUrlProperties salesUrlProperties;

    @Autowired
    private InnerUrlProperties innerUrlProperties;

    @Override
    public void exportExcelOss(InviteInsuranceVehicleRecordDTO dto) {
        logger.info("保修申请导出开始===================================》");
        try {
            String url= salesUrlProperties.getDownloadService()+salesUrlProperties.getExportExcelUrl();
            List<ExcelExportColumn> exportColumnList = getExcelColumn();
            DownloadDTO downloadDTO = new DownloadDTO();
            downloadDTO.setQueryParams(dto.toMaps());
            downloadDTO.setExcelName("线索管理导出.xlsx");
            downloadDTO.setSheetName("线索管理");
            downloadDTO.setServiceUrl(innerUrlProperties.getInviteInsuranceVCDCExport());
            downloadDTO.setExcelExportColumnList(exportColumnList);
            exportColumnList(url,downloadDTO.toMaps());
        }catch (Exception e){
            logger.info("保修上传导出：出现异常");
            e.printStackTrace();
        }
    }

    //列
    private List<ExcelExportColumn> getExcelColumn() {

        List<ExcelExportColumn> exportColumnList = new ArrayList<ExcelExportColumn>();
        exportColumnList.add(new ExcelExportColumn("vin", "VIN"));
        exportColumnList.add(new ExcelExportColumn("licensePlateNum", "车牌号"));
        exportColumnList.add(new ExcelExportColumn("clueType", "线索类型"));
        exportColumnList.add(new ExcelExportColumn("insuranceType", "续保客户类型"));
        exportColumnList.add(new ExcelExportColumn("followStatusName", "跟进状态"));
        exportColumnList.add(new ExcelExportColumn("total_score", "AI得分"));
        exportColumnList.add(new ExcelExportColumn("start_time", "通话时间"));
        exportColumnList.add(new ExcelExportColumn("call_length", "通话时长(秒)"));
        exportColumnList.add(new ExcelExportColumn("adviseInDate", "续保到期日期"));
        exportColumnList.add(new ExcelExportColumn("planFollowDate", "计划跟进日期"));
        exportColumnList.add(new ExcelExportColumn("actualFollowDate", "实际跟进日期"));
        exportColumnList.add(new ExcelExportColumn("saName", "跟进人员"));
        exportColumnList.add(new ExcelExportColumn("orderStatus", "线索完成状态"));
        exportColumnList.add(new ExcelExportColumn("dealerCode", "经销商代码"));
        exportColumnList.add(new ExcelExportColumn("createdAt", "邀约创建日期"));
        exportColumnList.add(new ExcelExportColumn("content", "跟进内容"));
        exportColumnList.add(new ExcelExportColumn("loseReason", "失败原因"));
        return exportColumnList;
    }

    public ResponseDTO exportColumnList(String url, Map param){
        //RestTemplate restTemplate = new RestTemplate();
        ResponseDTO response = new ResponseDTO();
        HttpHeaders headers =volvoHttpUtils.getHeaders();
        try {
            HttpEntity<Map<String,Object>> httpEntity = new HttpEntity<Map<String,Object>>(param, headers);
            ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(url, HttpMethod.POST, httpEntity,
                    ResponseDTO.class);

            if(responseEntity.getBody()!=null) {
                ObjectMapper objectMapper = new ObjectMapper();
                //转换返回对象
                response = objectMapper.convertValue(responseEntity.getBody(), ResponseDTO.class);
            }else {
                throw new Exception("请求接口返回对象responseEntity为空");
            }
        } catch (Exception e) {
            logger.error("download-service接口请求地址:{},请求参数:{}，返回失败:{}",e);
            if (e != null) {
                response.setReturnCode("error");
                response.setReturnMessage(e.getMessage());
            }
        }
        return response;
    }
}
