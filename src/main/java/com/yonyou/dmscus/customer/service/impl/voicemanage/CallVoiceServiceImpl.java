package com.yonyou.dmscus.customer.service.impl.voicemanage;


import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyRepairInfoMapper;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyRepairInfoPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.souche.api.SoucheApiException;
import com.souche.api.SoucheClient;
import com.souche.api.SoucheRequest;
import com.souche.api.SoucheResponse;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.voicemanage.CallDetailsMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.CallVoiceMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.SaCustomerNumberMapper;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.CallVoiceDTO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallVoicePO;
import com.yonyou.dmscus.customer.service.voicemanage.CallVoiceService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.dmscus.customer.utils.ai.CallVoiceChild;
import com.yonyou.dmscus.customer.utils.ai.DccHttpHelper;
import com.yonyou.dmscus.customer.utils.ai.DccResponseUtil;
import com.yonyou.dmscus.customer.utils.souche.SoucheResponseUtil;


/**
 * <p>
 * 通话录音 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Service
public class CallVoiceServiceImpl extends ServiceImpl<CallVoiceMapper, CallVoicePO> implements CallVoiceService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    CallVoiceMapper callVoiceMapper;
    
    @Resource
    SaCustomerNumberMapper saCustomerNumberMapper;
    
    @Resource
    CallDetailsMapper callDetailsMapper;
    
    @Value("${ai.telecom.common.url_mapping_recording}")
    String URL_MAPPING_RECORDING; //录音文件拉取
    
    @Value("${souche.serverUrl}")
    String serverUrl;  //大搜车url
    
    @Value("${souche.appKey}")
    String appKey; //大搜车appKey
    
    @Value("${souche.appSecret}")
    String appSecret; //大搜车appSecret
    

    /**
     * 分页查询对应数据
     *
     * @param page         分页对象
     * @param callVoiceDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.CallVoiceDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<CallVoiceDTO> selectPageBysql(Page page, CallVoiceDTO callVoiceDTO) {
        if (callVoiceDTO == null) {
            callVoiceDTO = new CallVoiceDTO();
        }
        CallVoicePO callVoicePO = callVoiceDTO.transDtoToPo(CallVoicePO.class);

        List<CallVoicePO> list = callVoiceMapper.selectPageBySql(page, callVoicePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<CallVoiceDTO> result = list.stream().map(m -> m.transPoToDto(CallVoiceDTO.class)).collect(Collectors
                    .toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param callVoiceDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.CallVoiceDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<CallVoiceDTO> selectListBySql(CallVoiceDTO callVoiceDTO) {
        if (callVoiceDTO == null) {
            callVoiceDTO = new CallVoiceDTO();
        }
        CallVoicePO callVoicePO = callVoiceDTO.transDtoToPo(CallVoicePO.class);
        List<CallVoicePO> list = callVoiceMapper.selectListBySql(callVoicePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(CallVoiceDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.CallVoiceDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public CallVoiceDTO getById(Long id) {
        CallVoicePO callVoicePO = callVoiceMapper.selectById(id);
        if (callVoicePO != null) {
            return callVoicePO.transPoToDto(CallVoiceDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param callVoiceDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(CallVoiceDTO callVoiceDTO) {
        //对对象进行赋值操作
        CallVoicePO callVoicePO = callVoiceDTO.transDtoToPo(CallVoicePO.class);
        //执行插入
        int row = callVoiceMapper.insert(callVoicePO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id           主键ID
     * @param callVoiceDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, CallVoiceDTO callVoiceDTO) {
        CallVoicePO callVoicePO = callVoiceMapper.selectById(id);
        //对对象进行赋值操作
        callVoiceDTO.transDtoToPo(callVoicePO);
        //执行更新
        int row = callVoiceMapper.updateById(callVoicePO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = callVoiceMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = callVoiceMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
	 * 录音文件拉取
	 * (请求有通话详情，没有通话录音文件的数据)
	 */
	@Override
	public void getCallVoiceByAi() {
		logger.info("录音文件拉取（开始）...");
		//拉取需要请求的数据
		List<Map<String, Object>> list=callDetailsMapper.getNotVoiceCallDetails();
		List<CallVoiceChild> allDetail=new ArrayList<CallVoiceChild>();
		logger.info("param:"+list);
		for(Map<String,Object> obj:list) {
			String callId=(String)obj.get("call_id");
			Map<String,Object> map=new HashMap<String,Object>();
			map.put("callId", callId);
			//请求电信接口
			String str=DccHttpHelper.httpPost(URL_MAPPING_RECORDING,map);
			logger.info("callId:"+callId+";录音文件拉取"+str);
			//响应结果
			DccResponseUtil response=JSONObject.parseObject(str,DccResponseUtil.class);
			if("0".equals(response.getCode())) {
				//响应子元素
				List<CallVoiceChild> callVoiceChildList=(List<CallVoiceChild>)JSONObject.parseArray(response.getResult(),CallVoiceChild.class);
			    if(!CommonUtils.isNullOrEmpty(callVoiceChildList) && callVoiceChildList.size() > 0) {
			    	for(CallVoiceChild d:callVoiceChildList) {
				    	d.setCallId(callId);
				    	d.setOwnerCode(obj.get("owner_code")+"");
				    	d.setOwnerParCode(obj.get("owner_par_code")+"");
				    	d.setDealerCode(obj.get("dealer_code")+"");
				    }
				    allDetail.addAll(callVoiceChildList);
			    }
			}
		}
		if(!CommonUtils.isNullOrEmpty(allDetail) && allDetail.size() > 0) {
			//保存数据库
			callVoiceMapper.insertCallVoice(allDetail);
		}
		logger.info("录音文件拉取（结束）...");
		
	}

	/**
	 * 录音文件推送大搜车
	 * 
	 */
	@Override
	public void sendDSCCallVoice() {
		logger.info("录音文件推送大搜车（开始）。。。");
		List<Map<String,Object>>  callVoiceList=callVoiceMapper.selectSendCallVoice(CommonConstants.DICT_IS_NO);
		logger.info("共需要推送："+callVoiceList.size()+"条");
		int i=0;
		for(Map<String,Object> c: callVoiceList) {
			// 实例化 SDK 客户端
			SoucheClient soucheClient = new SoucheClient(serverUrl, appKey, appSecret);
			// 实例化请求类
			SoucheRequest request = new SoucheRequest();
			Map<String, Object> dataMap = new HashMap<String, Object>();
			//固定不变
	        dataMap.put("serviceId", "1");
	        dataMap.put("channelNumber", "2");
	        dataMap.put("direction", 1);//1: 呼出, 2:呼入
	        dataMap.put("inspects", "3");//质检类型
	        //不确定值(待后续确定了之后再补充)
	        dataMap.put("customerNumber", "");
	        dataMap.put("cityCode", "");
	        dataMap.put("provinceCode", "");
	        dataMap.put("shopCode", ""); 
	        dataMap.put("isFirstFollowUp", 1); //是否首次跟进拨打
	        
	        dataMap.put("sessionId",c.get("session_Id")); //sessionId
	        dataMap.put("url",c.get("voice_url")); //录音地址
	        dataMap.put("callTime",((Date)c.get("start_time")).getTime()); //通话开始时间（是否是必填值）
			// 设置请求参数 api {String}
			request.setApi("com.souche.voipvoice.voipVoiceController.insert.json");
			// 设置请求参数 data {Map<String, Object>}
			Map<String, Object> dataMap2 = new HashMap<String, Object>();
			String jsonData= JSON.toJSONString(dataMap);
			dataMap2.put("json", jsonData);
			request.setData(dataMap2);
			try {
				  // 发送请求
				  SoucheResponse response = soucheClient.execute(request);
				  if (response.isSuccess()) {
					    i++;
					    // 请求成功
					    String data = response.getData();
					    logger.info("请求成功："+data);
					    SoucheResponseUtil souche=JSONObject.parseObject(data,SoucheResponseUtil.class);
					    if("200".equals(souche.getCode())) {
					    	//请求成功 更新发送状态
					    	CallVoicePO callVoicePO=callVoiceMapper.selectById((Long)c.get("id"));
					    	callVoicePO.setIsSend(CommonConstants.DICT_IS_YES);
					    	callVoiceMapper.updateById(callVoicePO);
					    }
				  }
			} catch (SoucheApiException e) {
			  // 业务错误查看 errCode 和 errMessage 内容
			  int errCode = e.getErrCode();
			  String errMessage = e.getErrMessage();
			  logger.info("业务错误"+errCode + ": " + errMessage);
			  // 通信错误查看 message 内容
			  String errorMessage = e.getMessage();
			  logger.info(errorMessage);
			}
		}
		logger.info("实际推送"+i+"条");
		logger.info("录音文件推送大搜车（结束）。。。");
	}

    @Override
    public void insertList(List<CallVoicePO> callVoicePOList) {
        saveBatch(callVoicePOList,100);
    }


}
