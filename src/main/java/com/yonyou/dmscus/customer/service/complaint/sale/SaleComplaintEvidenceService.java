package com.yonyou.dmscus.customer.service.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintEvidencePO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintEvidenceDTO;

import java.util.List;


/**
 * <p>
 * 客户测试表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
public interface SaleComplaintEvidenceService {
    IPage<SaleComplaintEvidenceDTO> selectPageBysql(Page page, SaleComplaintEvidenceDTO saleComplaintEvidenceDTO);
    List<SaleComplaintEvidenceDTO> selectListBySql(SaleComplaintEvidenceDTO saleComplaintEvidenceDTO);
    SaleComplaintEvidenceDTO getById(Long id);
    int insert(SaleComplaintEvidenceDTO saleComplaintEvidenceDTO);
    int update(Long id, SaleComplaintEvidenceDTO saleComplaintEvidenceDTO);
    int deleteById(Long id);
    int deleteBatchIds(String ids);

}
