package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

    
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.dto.complaint.InsertComplaitEvidenceDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintEvidencePO;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintEvidenceMapper;
import com.yonyou.dmscus.customer.service.complaint.ComplaintEvidenceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintEvidenceDTO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;




    /**
 * <p>
 * 客户投诉证据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Service
public class ComplaintEvidenceServiceImpl implements ComplaintEvidenceService {
          /**
         * 日志说明
        */
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        ComplaintEvidenceMapper complaintEvidenceMapper;

        /**
         * 分页查询对应数据
         *
         * @param page 分页对象
         * @param complaintEvidenceDTO 查询条件
         * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintEvidenceDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public IPage<ComplaintEvidenceDTO>selectPageBysql(Page page,ComplaintEvidenceDTO complaintEvidenceDTO){
            if(complaintEvidenceDTO ==null){
                complaintEvidenceDTO =new ComplaintEvidenceDTO();
            }
            ComplaintEvidencePO complaintEvidencePo =complaintEvidenceDTO.transDtoToPo(ComplaintEvidencePO.class);

            List<ComplaintEvidencePO>list= complaintEvidenceMapper.selectPageBySql(page,complaintEvidencePo);
            if(CommonUtils.isNullOrEmpty(list)){
                page.setRecords(new ArrayList<>());
                return page;
            }else{
                List<ComplaintEvidenceDTO>result=list.stream().map(m->m.transPoToDto(ComplaintEvidenceDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param complaintEvidenceDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintEvidenceDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<ComplaintEvidenceDTO>selectListBySql(ComplaintEvidenceDTO complaintEvidenceDTO){
            if(complaintEvidenceDTO ==null){
                complaintEvidenceDTO =new ComplaintEvidenceDTO();
            }
            ComplaintEvidencePO complaintEvidencePo =complaintEvidenceDTO.transDtoToPo(ComplaintEvidencePO.class);
            List<ComplaintEvidencePO>list= complaintEvidenceMapper.selectListBySql(complaintEvidencePo);
            if(CommonUtils.isNullOrEmpty(list)){
                return new ArrayList<>();
            }else{
                return list.stream().map(m->m.transPoToDto(ComplaintEvidenceDTO.class)).collect(Collectors.toList());
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintEvidenceDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public ComplaintEvidenceDTO getById(Long id){
            ComplaintEvidencePO complaintEvidencePo = complaintEvidenceMapper.selectById(id);
            if(complaintEvidencePo!=null){
                return complaintEvidencePo.transPoToDto(ComplaintEvidenceDTO.class);
            }else{
                throw new DALException("查询失败,没有符合条件的数据");
            }
        }

        /**
         * 根据DTO 进行数据新增
         *
         * @param complaintEvidenceDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(ComplaintEvidenceDTO complaintEvidenceDTO){
            //对对象进行赋值操作
            ComplaintEvidencePO complaintEvidencePo = complaintEvidenceDTO.transDtoToPo(ComplaintEvidencePO.class);
            //执行插入
            int row= complaintEvidenceMapper.insert(complaintEvidencePo);
            //返回插入的值
            return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param complaintEvidenceDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, ComplaintEvidenceDTO complaintEvidenceDTO){
            ComplaintEvidencePO complaintEvidencePo = complaintEvidenceMapper.selectById(id);
            //对对象进行赋值操作
            complaintEvidenceDTO.transDtoToPo(complaintEvidencePo);
            //执行更新
            int row= complaintEvidenceMapper.updateById(complaintEvidencePo);
            return row;
        }

        /**
         * 根据ID 进行删除数据
         *
         * @param id
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteById(Long id){
            int deleteCount= complaintEvidenceMapper.deleteById(id);
            logger.debug("删除数量：{}",deleteCount);
            if(deleteCount<=0){
                throw new DALException("删除失败,没有符合条件的数据");
            }else{
                return deleteCount;
            }
        }

        /**
         * 新增证据
         *
         * @param insertComplaitEvidenceDTO
         * @return
         */
        @Override
        public int insertComplaitEvidence(InsertComplaitEvidenceDTO insertComplaitEvidenceDTO) {
            ComplaintEvidenceDTO complaintEvidenceDTO =new ComplaintEvidenceDTO();
            long id=insertComplaitEvidenceDTO.getComplaintInfoId();
            complaintEvidenceDTO.setComplaintInfoId(id);
            List<ComplaintEvidenceDTO> list=insertComplaitEvidenceDTO.getEvidenceList();
            int row=0;
            if(list.size()!=0){
                for (int i=0;i<list.size();i++){
                    complaintEvidenceDTO.setRoNo(list.get(i).getRoNo());
                    complaintEvidenceDTO.setOperator(FrameworkUtil.getLoginInfo().getUserName());
                    complaintEvidenceDTO.setObject(insertComplaitEvidenceDTO.getObject());
                    row=insert(complaintEvidenceDTO);
                }
            }

            return row;
        }

        @Override
        public int deleteComplaitEvidence(List<ComplaintEvidenceDTO> complaintEvidenceDTOS) {
            int row=0;
            if(complaintEvidenceDTOS.size()!=0){
                for (int i=0;i<complaintEvidenceDTOS.size();i++){
                   Long id=complaintEvidenceDTOS.get(i).getId();
                    row=deleteById(id);
                }
            }
            return row;
        }
    }
