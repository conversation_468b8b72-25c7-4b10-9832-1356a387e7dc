package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.yonyou.dmscus.customer.configuration.InnerUrlProperties;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.dto.DownloadDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.PageRequestDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.*;
import com.yonyou.dmscus.customer.feign.DmscloudServiceClient;
import com.yonyou.dmscus.customer.feign.DownloadClient;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.feign.dto.CommonConfigDTO;
import com.yonyou.dmscus.customer.ossexcel.ExcelExportColumn;
import com.yonyou.dmscus.customer.utils.ExcelUtil;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyInfoMapper;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillInvoiceRecordMapper;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillNoticeInvoiceInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillInvoiceRecordPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillNoticeInvoiceInfoPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillStampsPO;
import com.yonyou.dmscus.customer.middleInterface.RequestDTO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyInfoService;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillStampsService;
import com.yonyou.dmscus.customer.util.common.StringUtils;


/**
 * <p>
 * 亲善预申请单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
@Service
public class GoodwillStampsServiceImpl implements GoodwillStampsService {
	/** 日志对象 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Autowired
	GoodwillStampsService goodwillStampsService;
	@Autowired
	GoodwillApplyInfoMapper goodwillApplyInfoMapper;
	@Resource
	GoodwillNoticeInvoiceInfoMapper goodwillNoticeInvoiceInfoMapper;

	@Resource
	GoodwillInvoiceRecordMapper goodwillInvoiceRecordMapper;
	/** 接口调用成功的返回状态 */
	public static final String SUCCESS_CODE = "0";
	@Resource
	GoodwillApplyInfoService goodwillApplyInfoService;
	@Resource
	private RestTemplate directRestTemplate;
	@Autowired
	ReportCommonClient reportCommonClient;
	@Autowired
	private MidUrlProperties midUrlProperties;

	@Resource
	private DownloadClient downloadClient;
	@Autowired
	private DmscloudServiceClient dmscloudServiceClient;

    @Resource
    private InnerUrlProperties innerUrlProperties;

	private static final String COUPON_ID = "couponId";
	private static final int LIMIT_COUNT = 10;//循环查询次数上线设置为5，原因：目前数据只要1300条，目前5次是远远满足需求的
	private static final Long COUPON_PAGE = 1L;
	private static final Long COUPON_PAGE_SIZE = 500L;

	@Override
	public IPage<GoodwillStampsDTO> selectPageBysql(Page page, GoodwillStampsDTO goodwillStampsDTO) {
		List<GoodwillStampsPO> mpvList = goodwillApplyInfoMapper.queryRechargeInfo(page, goodwillStampsDTO);
		if (CommonUtils.isNullOrEmpty(mpvList)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillStampsDTO> result = mpvList.stream().map(m -> m.transPoToDto(GoodwillStampsDTO.class))
					.collect(Collectors.toList());
			StringBuffer buf = new StringBuffer(0);
			String couponId = "";
			Map<Long,BigDecimal> map = new HashMap<>();
			for (GoodwillStampsDTO dto : result) {
				if (!StringUtils.isNullOrEmpty(dto.getCouponId()) && "代金券".equals(dto.getResourceType())) {
					buf.append(dto.getCouponId() + ",");
					map.put(dto.getCouponId(),dto.getCostRate());
				}
			}
			logger.info("map========================={}",JSONObject.toJSON(map));
			if (buf.length() > 0) {
				couponId = buf.toString().substring(0, buf.toString().lastIndexOf(","));
			}
			if (!StringUtils.isNullOrEmpty(couponId)) {
				List<Map> list1 =  reportCommonClient.couponVerify(couponId);
				logger.info("reportCommonClient.couponVerify({})={}",couponId,JSONObject.toJSON(list1));
				Map<String,List<String>> mapMap = new HashMap<>();
				if(!CommonUtils.isNullOrEmpty(list1)){
					this.getCouponIdMap(list1,mapMap);
					logger.info("mapMap:{}",JSONObject.toJSON(mapMap));
				}

				//List<Map> interList = this.getLeftValue(StrUtil.splitToLong(couponId, ","));
				//Map map1 = new HashMap<>(16);
				for (GoodwillStampsDTO dtos : result) {
					if (!StringUtils.isNullOrEmpty(dtos.getCouponId())) {
					//	if (!CommonUtils.isNullOrEmpty(interList)) {
					//		for (Object object : interList) {
					//			JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
					//			map1 = jsonObject.toJavaObject(Map.class);
//								if (map1.get("couponId").toString().equals(dtos.getCouponId().toString())) {
////									if (!StringUtils.isNullOrEmpty(map1.get("leftValue"))) {
////										logger.info("map{}========================={}",dtos.getCouponId(),map.get(dtos.getCouponId()));
////										dtos.setLeftAmount(new BigDecimal(map1.get("leftValue").toString())
////												.multiply(new BigDecimal(0.01)).multiply(map.get(dtos.getCouponId())));
////										dtos.setUsedAmount(dtos.getRechargeAmount().subtract(dtos.getLeftAmount()));
//////										dtos.setUsedAmount(dtos.getRechargeAmount()
//////												.subtract(new BigDecimal(map1.get("leftValue").toString())
//////														.divide(new BigDecimal(100))));
////									}
////								}

							if (!StringUtils.isNullOrEmpty(mapMap.get(dtos.getCouponId().toString()))) {
								List<String> objectList = mapMap.get(dtos.getCouponId().toString());
								logger.info("objectList:{}",JSONObject.toJSON(objectList));
								for (String o : objectList) {
									if (dtos.getCostConsumeAmount() == null) {
										dtos.setUsedAmount(new BigDecimal(0).add(new BigDecimal(o).divide(new BigDecimal(100))));
										dtos.setCostConsumeAmount(new BigDecimal(0).add(new BigDecimal(o).divide(new BigDecimal(100)).multiply(map.get(dtos.getCouponId()))));
									} else {
										dtos.setUsedAmount(dtos.getUsedAmount().add(new BigDecimal(o).divide(new BigDecimal(100))));
										dtos.setCostConsumeAmount(dtos.getCostConsumeAmount().add(new BigDecimal(o).divide(new BigDecimal(100)).multiply(map.get(dtos.getCouponId()))));
									}
									logger.info("dtos.setCostConsumeAmount:{}",JSONObject.toJSON(dtos.getCostConsumeAmount()));
									logger.info("dtos.setUsedAmount:{}",JSONObject.toJSON(dtos.getUsedAmount()));
								}
								dtos.setUsedAmount(dtos.getUsedAmount().multiply(map.get(dtos.getCouponId())));
							}
								dtos.setLeftAmount(dtos.getRechargeAmount().subtract(dtos.getCostConsumeAmount()==null ?new BigDecimal(0):dtos.getCostConsumeAmount()));
								int  max = 	dtos.getLeftAmount().compareTo(new BigDecimal(0));
								if(max <0){
									dtos.setLeftAmount(new BigDecimal(0));
								}
					//		}
					//	}

					}
				}

			}
			page.setRecords(result);
			// page.setTotal(result.size());
			return page;
		}
	}

	@Override
	public List<GoodwillStampsDTO> selectPageByList(GoodwillStampsDTO goodwillStampsDTO) {
		// String json =
		// "{\"data\":[{\"applyNo\":\"1233534\",\"dealerCode\":\"43254523\",\"veOwnerName\":\"42134\",\"license\":\"41341\",\"vin\":\"23452\",\"noticeAmount\":\"25424\",\"resourceType\":\"52452\",\"noticeDate\":\"2020-05-23
		// 15:09:25\",\"rechargeDate\":\"2020-05-23
		// 15:09:25\",\"rechargeAmount\":\"234\",\"usedAmount\":\"23\",\"leftAmount\":\"435\",\"voucherRechargeAmount\":\"1243\",\"voucherNotAmount\":\"4123\"},{\"goodwillApplyId\":\"1233534\",\"dealerCode\":\"43254523\",\"veOwnerName\":\"42134\",\"license\":\"41341\",\"vin\":\"23452\",\"noticeAmount\":\"25424\",\"resourceType\":\"52452\",\"noticeDate\":\"2020-05-23
		// 15:09:25\",\"rechargeDate\":\"2020-05-23
		// 15:09:25\",\"rechargeAmount\":\"234\",\"usedAmount\":\"23\",\"leftAmount\":\"435\",\"voucherRechargeAmount\":\"1243\",\"voucherNotAmount\":\"4123\"},{\"goodwillApplyId\":\"1233534\",\"dealerCode\":\"43254523\",\"veOwnerName\":\"42134\",\"license\":\"41341\",\"vin\":\"23452\",\"noticeAmount\":\"25424\",\"resourceType\":\"52452\",\"noticeDate\":\"2020-05-23
		// 15:09:25\",\"rechargeDate\":\"2020-05-23
		// 15:09:25\",\"rechargeAmount\":\"234\",\"usedAmount\":\"23\",\"leftAmount\":\"435\",\"voucherRechargeAmount\":\"1243\",\"voucherNotAmount\":\"4123\"}]}";
		// JSONObject object = JSON.parseObject(json);
		// JSONArray array = (JSONArray) object.get("data");
		List<GoodwillStampsDTO> mpvList = goodwillApplyInfoMapper.queryRechargeExport(goodwillStampsDTO);
		if (CommonUtils.isNullOrEmpty(mpvList)) {
			return new ArrayList<>();
		} else {
			StringBuffer buf = new StringBuffer(0);
			String couponId = "";
			Map<Long,BigDecimal> map = new HashMap<>();
			for (GoodwillStampsDTO dto : mpvList) {
				if (!StringUtils.isNullOrEmpty(dto.getCouponId()) && "代金券".equals(dto.getResourceType())) {
					buf.append(dto.getCouponId() + ",");
					map.put(dto.getCouponId(),dto.getCostRate());
				}
			}
			if (buf.length() > 0) {
				couponId = buf.toString().substring(0, buf.toString().lastIndexOf(","));
			}
			if (!StringUtils.isNullOrEmpty(couponId)) {
				//List<Map> interList = this.getLeftValue(StrUtil.splitToLong(couponId, ","));
				//Map map1 = new HashMap<>(16);
				List<Map> list1 =  reportCommonClient.couponVerify(couponId);
				logger.info("reportCommonClient.couponVerify({})={}",couponId,JSONObject.toJSON(list1));
				Map<String,List<String>> mapMap = new HashMap<>();
				if(!CommonUtils.isNullOrEmpty(list1)){
					this.getCouponIdMap(list1,mapMap);
				}
				for (GoodwillStampsDTO dtos : mpvList) {
					if (!StringUtils.isNullOrEmpty(dtos.getCouponId())) {
						//if (!CommonUtils.isNullOrEmpty(interList)) {
						//	for (Object object : interList) {
							//	JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
						//		map1 = jsonObject.toJavaObject(Map.class);
						//		if (map1.get("couponId").toString().equals(dtos.getCouponId().toString())) {
						//			if (!StringUtils.isNullOrEmpty(map1.get("leftValue"))) {
//										dtos.setLeftAmount(new BigDecimal(map1.get("leftValue").toString())
//												.multiply(new BigDecimal(0.01)));
//										dtos.setUsedAmount(dtos.getRechargeAmount()
//												.subtract(new BigDecimal(map1.get("leftValue").toString())
//														.divide(new BigDecimal(100))));
						//				dtos.setLeftAmount(new BigDecimal(map1.get("leftValue").toString())
						//						.multiply(new BigDecimal(0.01)).multiply(map.get(dtos.getCouponId())));
						//				dtos.setUsedAmount(dtos.getRechargeAmount().subtract(dtos.getLeftAmount()));
						//			}
						//		}
						//	}
						//}
						if (!StringUtils.isNullOrEmpty(mapMap.get(dtos.getCouponId().toString()))) {
							List<String> objectList = mapMap.get(dtos.getCouponId().toString());
							logger.info("objectList:{}",JSONObject.toJSON(objectList));
							logger.info("map:{}",JSONObject.toJSON(map));
							for (String o : objectList) {
								if (dtos.getCostConsumeAmount() == null) {
									dtos.setUsedAmount(new BigDecimal(0).add(new BigDecimal(o).divide(new BigDecimal(100))));
									logger.info("map:key{},values:{}",dtos.getCouponId(),JSONObject.toJSON(map.get(dtos.getCouponId())));
									dtos.setCostConsumeAmount(new BigDecimal(0).add(new BigDecimal(o).divide(new BigDecimal(100)).multiply(map.get(dtos.getCouponId()))));
								} else {
									logger.info("map1:key{},values:{}",dtos.getCouponId(),JSONObject.toJSON(map.get(dtos.getCouponId())));
									dtos.setCostConsumeAmount(dtos.getCostConsumeAmount().add(new BigDecimal(o).divide(new BigDecimal(100)).multiply(map.get(dtos.getCouponId()))));
									dtos.setUsedAmount(dtos.getUsedAmount().add(new BigDecimal(o).divide(new BigDecimal(100))));
								}
							}
							dtos.setUsedAmount(dtos.getUsedAmount().multiply(map.get(dtos.getCouponId())));
						}
						dtos.setLeftAmount(dtos.getRechargeAmount().subtract(dtos.getCostConsumeAmount()==null ?new BigDecimal(0):dtos.getCostConsumeAmount()));
						int  max = 	dtos.getLeftAmount().compareTo(new BigDecimal(0));
						if(max <0){
							dtos.setLeftAmount(new BigDecimal(0));
						}

					}
				}

			}
			return mpvList;
		}
	}

	@Override
	public IPage<GoodwillStampsDTO> selectPageByConsume(Page page, GoodwillStampsDTO goodwillStampsDTO)
			throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		List<GoodwillStampsDTO> result = new ArrayList<>();

		if (!StringUtils.isNullOrEmpty(goodwillStampsDTO.getIsChargerd())) {
			String[] consumeId = this.getConsumeId(goodwillStampsDTO);
			logger.info("!StringUtils.isNullOrEmpty(goodwillStampsDTO.getIsChargerd()):{}",!StringUtils.isNullOrEmpty(goodwillStampsDTO.getIsChargerd()));
			logger.info("consumeId:{}",JSONObject.toJSON(consumeId));
			goodwillStampsDTO.setConsume(consumeId);
		} else {
			if (!StringUtils.isNullOrEmpty(goodwillStampsDTO.getInvoiceNumber())
					|| goodwillStampsDTO.getNoticeStartdAt() != null || goodwillStampsDTO.getNoticeEndAt() != null
					|| goodwillStampsDTO.getInvoiceStartdAt() != null || goodwillStampsDTO.getInvoiceEndAt() != null) {
				String[] consumeId = this.getConsumeId(goodwillStampsDTO);
				logger.info("!StringUtils.isNullOrEmpty(goodwillStampsDTO.getIsChargerd()):{}",!StringUtils.isNullOrEmpty(goodwillStampsDTO.getIsChargerd()));
				logger.info("consumeId:{}",JSONObject.toJSON(consumeId));
				goodwillStampsDTO.setConsume(consumeId);
			}
		}
		Long pageNum = COUPON_PAGE;
		Long pageSize = COUPON_PAGE_SIZE;
		Page<Map> pageList = this.getConsumeByPage(goodwillStampsDTO, pageNum, pageSize);
		List<Map> list = pageList.getRecords();
		// 添加循环查询和次数的上限
		while (pageList.getTotal() > pageSize * pageNum && pageNum<LIMIT_COUNT) {
			pageNum = pageNum+1;
			Page<Map> listPageTemp = this.getConsumeByPage(goodwillStampsDTO, pageNum, pageSize);
			list.addAll(listPageTemp.getRecords());
		}
		logger.info("getConsumeByPage 分页查询结果size：{}", list.size());
		if (!CommonUtils.isNullOrEmpty(list)) {
			// 解决
			List<String> consumeList = new ArrayList<>();
			List<String> couponIdList = new ArrayList<>();
			list.stream().filter(Objects::nonNull).forEach(obj->{
				Map map = JSON.parseObject(JSON.toJSONString(obj), Map.class);
				Optional.ofNullable(map.get("id")).ifPresent(o->consumeList.add(map.get("id").toString()));
				Optional.ofNullable(map.get(COUPON_ID)).ifPresent(o->couponIdList.add(map.get(COUPON_ID).toString()));
			});

			// 查询所有的goodwillNoticeInvoiceInfo Consume_id 不为空的数据
			List<String> ids = queryIdsByConsumeIds(consumeList);
			// 根据主键查询组合数据
			Map<String, List<GoodwillStampsPO>> map = new HashMap<>();
			if(!CollectionUtils.isEmpty(ids)) {
				map = queryConsumeInfoToMap(consumeList, ids, goodwillStampsDTO);
			}

			// 根据couponId集合查询对应的ApplyInfo对象集合并转换成key为couponId的Map结构
			Map<Long, GoodwillApplyInfoDTO> goodwillApplyInfoMap = new HashMap<>();
			if(!CollectionUtils.isEmpty(couponIdList)) {
				List<GoodwillApplyInfoDTO> goodwillApplyInfoList = goodwillApplyInfoMapper.findApplyInfobyCouponIds(couponIdList, goodwillStampsDTO);
				goodwillApplyInfoMap = goodwillApplyInfoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(GoodwillApplyInfoDTO::getCouponId, Function.identity(), (v1, v2)->v2));
			}

			for (Object object : list) {
				GoodwillStampsDTO goodwillStampsDto = new GoodwillStampsDTO();
				JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
				Map map1 = jsonObject.toJavaObject(Map.class);
				String consumes = map1.get("id").toString();
				goodwillStampsDTO.setConsumes(consumes);
				List<GoodwillStampsPO> mpvList = map.get(consumes);
				GoodwillApplyInfoDTO goodwillApplyInfoDTO = goodwillApplyInfoMap.get(Long.valueOf(map1.get(COUPON_ID).toString()));
				if (goodwillApplyInfoDTO != null) {
					goodwillStampsDto.setGoodwillApplyId(goodwillApplyInfoDTO.getId());
					goodwillStampsDto.setApplyNo(goodwillApplyInfoDTO.getApplyNo());
					goodwillStampsDto.setDealerCode(goodwillApplyInfoDTO.getDealerCode());
					goodwillStampsDto.setDealerName(goodwillApplyInfoDTO.getDealerName());
					goodwillStampsDto.setAreaManage(goodwillApplyInfoDTO.getAreaManage());
					goodwillStampsDto.setSmallArea(goodwillApplyInfoDTO.getSmallArea());
					goodwillStampsDto.setVin(goodwillApplyInfoDTO.getVin());
					goodwillStampsDto.setVeOwnerName(goodwillApplyInfoDTO.getCustomerName());
					goodwillStampsDto.setLicense(goodwillApplyInfoDTO.getLicense());
					goodwillStampsDto.setConsumeId(Long.valueOf(consumes));
					goodwillStampsDto.setRechargeAmount(goodwillApplyInfoDTO.getRechargeAmount());
					goodwillStampsDto.setInvoicedAmount(goodwillApplyInfoDTO.getNoticeInvoicePrice());
					if (!StringUtils.isNullOrEmpty(map1.get("operateDate"))) {
						goodwillStampsDto.setConsumeDate(sdf.parse(map1.get("operateDate").toString()));
					}
					if (!StringUtils.isNullOrEmpty(map1.get("verifyAmount"))) {
						goodwillStampsDto.setConsumeAmount(
								new BigDecimal(map1.get("verifyAmount").toString()).divide(new BigDecimal(100)));
						goodwillStampsDto.setCostConsumeAmount(
								goodwillStampsDto.getConsumeAmount().multiply(goodwillApplyInfoDTO.getCostRate()));
					}
					if (!StringUtils.isNullOrEmpty(map1.get("orderNo"))) {
						goodwillStampsDto.setRepairNumber(map1.get("orderNo").toString());
					}
					result.add(goodwillStampsDto);
				}
				if (!CommonUtils.isNullOrEmpty(mpvList)) {
					List<GoodwillStampsDTO> mpvList1 = mpvList.stream()
							.map(m -> m.transPoToDto(GoodwillStampsDTO.class)).collect(Collectors.toList());
					for (GoodwillStampsDTO dto : mpvList1) {
						if (goodwillApplyInfoDTO != null) {
							if (goodwillApplyInfoDTO.getId().equals(dto.getGoodwillApplyId())) {
								goodwillStampsDto.setNoticeInvoiceDate(dto.getNoticeDate());
								goodwillStampsDto.setNoticeInvoicePrice(dto.getNoticeInvoicePrice());
								goodwillStampsDto.setInvoiceTitle(dto.getInvoiceTitle());
								goodwillStampsDto.setInvoiceId(dto.getInvoiceId());
								goodwillStampsDto.setInvoiceNumber(dto.getInvoiceNumber());
								goodwillStampsDto.setInvoiceDate(dto.getInvoiceDate());
								goodwillStampsDto.setReceiveDate(dto.getReceiveDate());
								goodwillStampsDto.setCourierCompany(dto.getCourierCompany());
								goodwillStampsDto.setCourierNumber(dto.getCourierNumber());
								goodwillStampsDto.setDeliveryDate(dto.getDeliveryDate());

								// result.add(goodwillStampsDto);
							}
						}

					}
				}

			}
		}

		page.setTotal(result.size());
		page.setRecords(result.subList((int) ((page.getCurrent() - 1) * page.getSize()),
				(int) ((result.size() - (page.getCurrent() - 1) * page.getSize()) <= page.getSize() ? result.size()
						: (page.getCurrent() - 1) * page.getSize() + page.getSize())));
		return page;
	}

	/**
	 * 卡券核销接口-分页
	 * 页面DTO
	 */
	public Page<Map> getConsumeByPage(GoodwillStampsDTO goodwillStampsDTO,Long pageNum,Long pageSize) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Map dtos = new HashMap();
		dtos.put("kindness", 1);
		if (!StringUtils.isNullOrEmpty(goodwillStampsDTO.getConsumeStartdAt())) {
			dtos.put("operateDateBegin", sdf.format(goodwillStampsDTO.getConsumeStartdAt()).concat(" 00:00:00"));
		}
		if (!StringUtils.isNullOrEmpty(goodwillStampsDTO.getConsumeEndAt())) {
			dtos.put("operateDateEnd", sdf.format(goodwillStampsDTO.getConsumeEndAt()).concat(" 23:59:59"));
		}

		if (!StringUtils.isNullOrEmpty(goodwillStampsDTO.getIsChargerd())) {
			if (goodwillStampsDTO.getIsChargerd() == CommonConstants.DICT_IS_YES) {
				if (!StringUtils.isNullOrEmpty(goodwillStampsDTO.getConsume())) {
					dtos.put("verifyIds", goodwillStampsDTO.getConsume());
				}
			} else {
				if (!StringUtils.isNullOrEmpty(goodwillStampsDTO.getConsume())) {
					dtos.put("excludeVerifyIds", goodwillStampsDTO.getConsume());
				}
			}
		} else {
			if (!StringUtils.isNullOrEmpty(goodwillStampsDTO.getConsume())) {
				dtos.put("verifyIds", goodwillStampsDTO.getConsume());
			}
		}
		Page<Map> interList = getInterList(pageNum, pageSize, dtos);
		return interList;
	}

	@NotNull
	private Page<Map> getInterList(Long pageNum, Long pageSize, Map dtos) {
		PageRequestDTO<Map> requestDto = new PageRequestDTO<Map>();
		requestDto.setPage(pageNum);
		requestDto.setPageSize(pageSize);
		requestDto.setData(dtos);
		String requestUrls = midUrlProperties.getMidEndCouponCenter() + midUrlProperties.getTtCouponVerifyAllByPage();
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<PageRequestDTO> httpEntity = new HttpEntity<PageRequestDTO>(requestDto, httpHeaders);
		Page<Map> interList = new Page<>(requestDto.getPage(), requestDto.getPageSize());
		// 调用接口
		try {
			logger.info("z中台卡券核销查询入参:{}",JSONObject.toJSON(httpEntity));
			ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrls, HttpMethod.POST, httpEntity,
					ResponseDTO.class);
			if (responseEntity.getBody() != null) {
				if (SUCCESS_CODE.equals(responseEntity.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					interList = objectMapper.convertValue(responseEntity.getBody().getData(), Page.class);
					logger.info("z中台卡券核销查询出参Size:{}",interList.getSize());
				} else {
					logger.error("中台卡券核销查询报错：{}", responseEntity.getBody().getReturnMessage());
					throw new ServiceBizException("中台查询核销卡券接口失败");
				}
			} else {
				logger.error("中台核销卡券查询报错：{}", responseEntity.getBody().getReturnMessage());
				throw new ServiceBizException("中台查询核销卡券接口失败");
			}
		} catch (Exception e) {
			logger.error("中台核销卡券查询报错：{}", e.getMessage());
			throw new ServiceBizException("中台查询核销卡券接口失败");
		}
		return interList;
	}

	/**
	 * 卡券核销接口-分页
	 * 页面DTO
	 */
	public Map getConsumeByPage(String id) {
		logger.info("getConsumeByPage,id:{}",id);
		Map<String,Object> map = new HashMap();
		map.put("kindness", 1);
		map.put("id", id);
		Page<Map> interList = getInterList(1L, 1L, map);
		logger.info("getConsumeByPage,interList:{}",interList);
		if(CollectionUtils.isEmpty(interList.getRecords())){
			return null;
		}
		return interList.getRecords().get(0);
	}

	/**
	 * 根据主键id集合查询对应的ConsumeInfo
	 *  并根据consumeList 重新分组
	 * @param consumeList
	 * @param ids
	 * @return
	 */
	private Map<String, List<GoodwillStampsPO>> queryConsumeInfoToMap(List<String> consumeList, List<String> ids, GoodwillStampsDTO goodwillStampsDTO) {
		logger.info("ids: {}", ids);
		List<GoodwillStampsPO> goodwillStampsList = goodwillApplyInfoMapper.queryConsumeInfoByIds(ids, goodwillStampsDTO);
		Map<String, List<GoodwillStampsPO>> map = new HashMap<>();
		goodwillStampsList.forEach(obj->{
			String consumes = obj.getConsumes();
			if(org.apache.commons.lang3.StringUtils.isNotBlank(consumes)) {
				String[] split = consumes.split(",");
				List<String> asList =new ArrayList<>(Arrays.asList(split));
				asList.retainAll(consumeList);
				asList.stream().filter(Objects::nonNull).forEach(str->{
					List<GoodwillStampsPO> mapList = map.get(str);
					if(mapList==null) {
						mapList = new ArrayList<>();
					}
					mapList.add(obj);
					map.put(str, mapList);
				});
			}
		});
		return map;
	}

	/**
	 * 查询所有的goodwillNoticeInvoiceInfo Consume_id 不为空的数据
	 * 并根据consumeList 过滤出对应的主键idList
	 * @param consumeList
	 * @return
	 */
	private List<String> queryIdsByConsumeIds(List<String> consumeList) {
		if(CollectionUtils.isEmpty(consumeList)) {
			return new ArrayList<>();
		}
		List<GoodwillNoticeInvoiceInfoPO> consumeMapList = goodwillNoticeInvoiceInfoMapper.queryConsumeList();
		List<String> ids = new ArrayList<>();
		consumeMapList.forEach(obj->{
			String id = String.valueOf(obj.getId());
			String consumeId = obj.getConsumeId();
			if(org.apache.commons.lang3.StringUtils.isNotBlank(consumeId)) {
				String[] split = consumeId.split(",");
				List<String> asList =new ArrayList<>(Arrays.asList(split));
				asList.retainAll(consumeList);
				if(!asList.isEmpty()) {
					ids.add(id);
				}
			}
		});
		logger.info("ids: {}", ids);
		return ids;
	}

	@Override
	public void exportconGoodwillStamps(GoodwillStampsDTO goodwillStampsDTO) {

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

		List<ExcelExportColumn> exportColumnList = null;

		if (CommonConstants.DATATYPE_FACTORY == goodwillStampsDTO.getDataType()) {
			exportColumnList = ExcelUtil.getExcelExportColumnFromAnnotation(GoodwillStampsDTO.class);
            logger.info("厂端导出代金券使用明细字段：【{}】", org.apache.commons.collections4.CollectionUtils.isEmpty(exportColumnList) ? null : JSONUtil.toJsonStr(exportColumnList));
		} else if (CommonConstants.DATATYPE_STORE == goodwillStampsDTO.getDataType()) {
			exportColumnList = ExcelUtil.getExcelExportColumnFromAnnotation(GoodwillStampsStoreDTO.class);
            logger.info("店端导出代金券使用明细字段：【{}】", org.apache.commons.collections4.CollectionUtils.isEmpty(exportColumnList) ? null : JSONUtil.toJsonStr(exportColumnList));
		}


		Map<String, Object> paraMap = goodwillStampsDTO.toMaps();
		if (null != goodwillStampsDTO.getConsumeStartdAt()) {
			paraMap.put("consumeStartdAt", sdf.format(goodwillStampsDTO.getConsumeStartdAt()));
		}

		if (null != goodwillStampsDTO.getConsumeEndAt()) {
			paraMap.put("consumeEndAt", sdf.format(goodwillStampsDTO.getConsumeEndAt()));
		}

		if (null != goodwillStampsDTO.getInvoiceStartdAt()) {
			paraMap.put("invoiceStartdAt", sdf.format(goodwillStampsDTO.getInvoiceStartdAt()));
		}

		if (null != goodwillStampsDTO.getInvoiceEndAt()) {
			paraMap.put("invoiceEndAt", sdf.format(goodwillStampsDTO.getInvoiceEndAt()));
		}

		if (null != goodwillStampsDTO.getNoticeStartdAt()) {
			paraMap.put("noticeStartdAt", sdf.format(goodwillStampsDTO.getNoticeStartdAt()));
		}

		if (null != goodwillStampsDTO.getNoticeEndAt()) {
			paraMap.put("noticeEndAt", sdf.format(goodwillStampsDTO.getNoticeEndAt()));
		}

		DownloadDTO downloadDTO = new DownloadDTO();
		downloadDTO.setExcelExportColumnList(exportColumnList);
		downloadDTO.setQueryParams(paraMap);
		downloadDTO.setExcelName("代金券使用明细.xlsx");
		downloadDTO.setSheetName("代金券使用明细");
		downloadDTO.setServiceUrl(innerUrlProperties.getDownloadConGoodwillstampsUrl());
		logger.info("导出代金券使用明细，调用下载中心传入参数：【{}】", JSONUtil.toJsonStr(downloadDTO));
		downloadClient.downloadConGoodwillStamps(downloadDTO);

	}

    /**
	 * 下载导出代金券使用明细
	 * @param goodwillStampsDTO
	 * @return
	 * @throws ParseException
	 */
	@Override
	public List<GoodwillStampsDTO> downloadConGoodwillStamps(Page<GoodwillStampsDTO> page, GoodwillStampsDTO goodwillStampsDTO) throws ParseException {

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		List<GoodwillStampsDTO> result = new ArrayList<>();
		if (!StringUtils.isNullOrEmpty(goodwillStampsDTO.getIsChargerd())) {
			String[] consumeId = this.getConsumeId(goodwillStampsDTO);
			goodwillStampsDTO.setConsume(consumeId);
		} else {
			if (!StringUtils.isNullOrEmpty(goodwillStampsDTO.getInvoiceNumber())
					|| !StringUtils.isNullOrEmpty(goodwillStampsDTO.getInvoiceId())
					|| goodwillStampsDTO.getNoticeStartdAt() != null || goodwillStampsDTO.getNoticeEndAt() != null
					|| goodwillStampsDTO.getInvoiceStartdAt() != null || goodwillStampsDTO.getInvoiceEndAt() != null) {
				String[] consumeId = this.getConsumeId(goodwillStampsDTO);
				logger.info("consumeId----->:【{}】", consumeId);
				goodwillStampsDTO.setConsume(consumeId);
			}
		}

		Long pageNum = COUPON_PAGE;
		Long pageSize = COUPON_PAGE_SIZE;
		Page<Map> pageList = this.getConsumeByPage(goodwillStampsDTO, pageNum, pageSize);
		List<Map> list = pageList.getRecords();
		// 添加循环查询和次数的上限
		while (pageList.getTotal() > pageSize * pageNum && pageNum<LIMIT_COUNT) {
			pageNum = pageNum+1;
			Page<Map> listPageTemp = this.getConsumeByPage(goodwillStampsDTO, pageNum, pageSize);
			list.addAll(listPageTemp.getRecords());
		}
		logger.info("getConsumeByPage 分页查询结果size：{}", list.size());
		if (!CommonUtils.isNullOrEmpty(list)) {
			// 解决
			List<String> consumeList = new ArrayList<>();
			List<String> couponIdList = new ArrayList<>();
			list.stream().filter(Objects::nonNull).forEach(obj->{
				Map map = JSON.parseObject(JSON.toJSONString(obj), Map.class);
				Optional.ofNullable(map.get("id")).ifPresent(o->consumeList.add(map.get("id").toString()));
				Optional.ofNullable(map.get(COUPON_ID)).ifPresent(o->couponIdList.add(map.get(COUPON_ID).toString()));
			});

			// 查询所有的goodwillNoticeInvoiceInfo Consume_id 不为空的数据
			List<String> ids = queryIdsByConsumeIds(consumeList);
			// 根据主键查询组合数据
			Map<String, List<GoodwillStampsPO>> map = new HashMap<>();
			if(!CollectionUtils.isEmpty(ids)) {
				map = queryConsumeInfoToMap(consumeList, ids, goodwillStampsDTO);
			}

			// 根据couponId集合查询对应的ApplyInfo对象集合并转换成key为couponId的Map结构
			Map<Long, GoodwillApplyInfoDTO> goodwillApplyInfoMap = new HashMap<>();
			if(!CollectionUtils.isEmpty(couponIdList)) {
				List<GoodwillApplyInfoDTO> goodwillApplyInfoList = goodwillApplyInfoMapper.findApplyInfobyCouponIds(couponIdList, goodwillStampsDTO);
				logger.info("goodwillApplyInfoList.size:{}", goodwillApplyInfoList.size());
				goodwillApplyInfoMap = goodwillApplyInfoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(GoodwillApplyInfoDTO::getCouponId, Function.identity(), (v1, v2)->v2));
				logger.info("goodwillApplyInfoMap.size:{}", goodwillApplyInfoMap.size());
			}

			for (Object object : list) {
				GoodwillStampsDTO goodwillStampsDto = new GoodwillStampsDTO();
				JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
				Map map1 = jsonObject.toJavaObject(Map.class);
				String consumes = map1.get("id").toString();
				goodwillStampsDTO.setConsumes(consumes);
				List<GoodwillStampsPO> mpvList = map.get(consumes);
				GoodwillApplyInfoDTO goodwillApplyInfoDTO = goodwillApplyInfoMap.get(Long.valueOf(map1.get(COUPON_ID).toString()));
				if (goodwillApplyInfoDTO != null) {
					goodwillStampsDto.setGoodwillApplyId(goodwillApplyInfoDTO.getId());
					goodwillStampsDto.setApplyNo(goodwillApplyInfoDTO.getApplyNo());
					goodwillStampsDto.setDealerCode(goodwillApplyInfoDTO.getDealerCode());
					goodwillStampsDto.setDealerName(goodwillApplyInfoDTO.getDealerName());
					goodwillStampsDto.setAreaManage(goodwillApplyInfoDTO.getAreaManage());
					goodwillStampsDto.setSmallArea(goodwillApplyInfoDTO.getSmallArea());
					goodwillStampsDto.setVin(goodwillApplyInfoDTO.getVin());
					goodwillStampsDto.setVeOwnerName(goodwillApplyInfoDTO.getCustomerName());
					goodwillStampsDto.setLicense(goodwillApplyInfoDTO.getLicense());
					// goodwillStampsDto.setConsumeId(Long.valueOf(map1.get("id").toString()));
					goodwillStampsDto.setConsumeId(Long.valueOf(consumes));
					goodwillStampsDto.setRechargeAmount(goodwillApplyInfoDTO.getRechargeAmount());
					goodwillStampsDto.setInvoicedAmount(goodwillApplyInfoDTO.getNoticeInvoicePrice());
					if (!StringUtils.isNullOrEmpty(map1.get("operateDate"))) {
						goodwillStampsDto.setConsumeDate(sdf.parse(map1.get("operateDate").toString()));
					}
					if (!StringUtils.isNullOrEmpty(map1.get("verifyAmount"))) {
						goodwillStampsDto.setConsumeAmount(
								new BigDecimal(map1.get("verifyAmount").toString()).divide(new BigDecimal(100)));
						goodwillStampsDto.setCostConsumeAmount(
								goodwillStampsDto.getConsumeAmount().multiply(goodwillApplyInfoDTO.getCostRate()));
					}
					if (!StringUtils.isNullOrEmpty(map1.get("orderNo"))) {
						goodwillStampsDto.setRepairNumber(map1.get("orderNo").toString());
					}
					result.add(goodwillStampsDto);
				}
				if (!CommonUtils.isNullOrEmpty(mpvList)) {
					List<GoodwillStampsDTO> mpvList1 = mpvList.stream()
							.map(m -> m.transPoToDto(GoodwillStampsDTO.class)).collect(Collectors.toList());
					for (GoodwillStampsDTO dto : mpvList1) {
						if (goodwillApplyInfoDTO != null) {
							if (goodwillApplyInfoDTO.getId().equals(dto.getGoodwillApplyId() )) {
								goodwillStampsDto.setNoticeInvoiceDate(dto.getNoticeDate());
								goodwillStampsDto.setNoticeInvoicePrice(dto.getNoticeInvoicePrice());
								goodwillStampsDto.setInvoiceTitle(dto.getInvoiceTitle());
								goodwillStampsDto.setInvoiceId(dto.getInvoiceId());
								goodwillStampsDto.setInvoiceNumber(dto.getInvoiceNumber());
								goodwillStampsDto.setInvoiceDate(dto.getInvoiceDate());
								goodwillStampsDto.setReceiveDate(dto.getReceiveDate());
								goodwillStampsDto.setCourierCompany(dto.getCourierCompany());
								goodwillStampsDto.setCourierNumber(dto.getCourierNumber());
								goodwillStampsDto.setDeliveryDate(dto.getDeliveryDate());

								// result.add(goodwillStampsDto);
							}
						}
					}
				}

			}
		}

		if (null != page) {

			int beginIndex = Integer.parseInt(String.valueOf((page.getCurrent() - 1) * page.getSize()));
			int endIndex = Integer.parseInt(String.valueOf((result.size() - (page.getCurrent() - 1) * page.getSize()) <= page.getSize() ? result.size() : (page.getCurrent() - 1) * page.getSize() + page.getSize()));
            logger.info("page.getCurrent()--------->【{}】,page.getSize()-------->【{}】,beginIndex--------->【{}】,endIndex----------->【{}】", page.getCurrent(), page.getSize(), beginIndex, endIndex);
            if (beginIndex <= endIndex) {
                List<GoodwillStampsDTO> rList = result.subList(beginIndex, endIndex);
                logger.info("result--------->result.size【{}】,rList============>【{}】", result.size(), org.apache.commons.collections4.CollectionUtils.isEmpty(rList) ? null : JSONUtil.toJsonStr(rList));
                return rList;
            }

            logger.info("Collections.emptyList()下载导出数据查询完了");
            return Collections.emptyList();
        }

        return result;
	}

	/**
	 * 根据DTO 进行数据新增 通知开票
	 *
	 * @param goodwillNoticeInvoiceInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int editNoticeInvoiceInfo(GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto) {
		logger.info("editNoticeInvoiceInfo,goodwillNoticeInvoiceInfoDto:{}", goodwillNoticeInvoiceInfoDto);
		// 对对象进行赋值操作
		GoodwillNoticeInvoiceInfoPO goodwillNoticeInvoiceInfoPO = goodwillNoticeInvoiceInfoDto
				.transDtoToPo(GoodwillNoticeInvoiceInfoPO.class);
		// 查询判断是第几次开票
		Integer invoicedCount = goodwillApplyInfoMapper
				.queryNoticeInvoiceCount(goodwillNoticeInvoiceInfoDto.getGoodwillApplyId());
		// 录入发票信息插入开票id数据
		GoodwillInvoiceRecordPO goodwillInvoiceRecordPO = new GoodwillInvoiceRecordPO();

		GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoMapper
				.selectById(goodwillNoticeInvoiceInfoDto.getGoodwillApplyId());
		if(Objects.isNull(goodwillApplyInfoPo)){
			logger.info("editNoticeInvoiceInfo,end,goodwillApplyInfoPo is null");
			throw new ServiceBizException("数据错误,请检查");
		}
		//校验
		String consumeId = goodwillNoticeInvoiceInfoDto.getConsumeId();
		checkGoodwillApplyInfo(goodwillNoticeInvoiceInfoDto.getGoodwillApplyId(),
				goodwillNoticeInvoiceInfoDto.getNoticeInvoicePrice(),consumeId,goodwillApplyInfoPo.getCostRate()
		);
		// 对对象进行赋值操作
		if (goodwillNoticeInvoiceInfoDto.getNoticeInvoicePrice().compareTo(BigDecimal.ZERO) != 0) {
			logger.info("editNoticeInvoiceInfo,noticeInvoicePrice:{}", goodwillNoticeInvoiceInfoDto.getNoticeInvoicePrice());
			goodwillNoticeInvoiceInfoPO.setInvoiceId(
					goodwillNoticeInvoiceInfoDto.getInvoiceId().concat("-").concat(invoicedCount.toString()));
			goodwillNoticeInvoiceInfoPO.setNoticeInvoiceDate(new Date());
			goodwillNoticeInvoiceInfoPO.setInvoiceObject(1);
			goodwillNoticeInvoiceInfoPO.setIsCommit(CommonConstants.DICT_IS_NO);
			goodwillNoticeInvoiceInfoPO.setVoucherType(1);
			goodwillNoticeInvoiceInfoPO.setConsumeId(goodwillNoticeInvoiceInfoDto.getConsumeId());
			// 执行插入
			goodwillNoticeInvoiceInfoMapper.insert(goodwillNoticeInvoiceInfoPO);
			goodwillInvoiceRecordPO.setGoodwillApplyId(goodwillNoticeInvoiceInfoDto.getGoodwillApplyId());
			goodwillInvoiceRecordPO.setInvoiceId(
					goodwillNoticeInvoiceInfoDto.getInvoiceId().concat("-").concat(invoicedCount.toString()));
			// 执行插入
			goodwillInvoiceRecordMapper.insert(goodwillInvoiceRecordPO);
			// 调用中台接口
			goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_ENTRY_INVOICE);
			// goodwillApplyInfoPo.setInvoiceAmount(goodwillNoticeInvoiceInfoDto.getNoticeInvoicePrice());
			goodwillApplyInfoPo.setVoucherInvoiceAmount(goodwillNoticeInvoiceInfoDto.getNoticeInvoicePrice());
			// add by cl 20200807
			Integer mailType = 0;// 商务亲善代金券/积分开票通知
			if (!StringUtils.isNullOrEmpty(goodwillNoticeInvoiceInfoDto.getInvoiceTitle())) {
				if ("82061001".equals(goodwillNoticeInvoiceInfoDto.getInvoiceTitle())) {
					mailType = 82461018;// 确认亲善邮件类型 - 商务亲善代金券开票通知-VCDC进口车
				} else if ("82061002".equals(goodwillNoticeInvoiceInfoDto.getInvoiceTitle())) {
					mailType = 82461019;// 确认亲善邮件类型 - 商务亲善代金券开票通知-VCAP国产车
				} else {
					mailType = 82461020;// 确认亲善邮件类型 - 商务亲善代金券开票通知-其他
				}
			}

			int sendToFlag = 1; // 1,经销商 2，角色
			// 商务亲善代金券/积分开票通知
			goodwillApplyInfoService.sendEmail(goodwillApplyInfoPo, sendToFlag, null, mailType);
		}

		// 执行更新
		int row = goodwillApplyInfoMapper.updateById(goodwillApplyInfoPo);

		// 返回插入的值
		return row;
	}

	private void checkGoodwillApplyInfo(Long goodwillApplyId, BigDecimal noticeInvoicePrice, String consumeId, BigDecimal costRate){
		logger.info("checkGoodwillApplyInfo,goodwillApplyId:{}, noticeInvoicePrice:{}, consumeId:{},costRate:{}", goodwillApplyId, noticeInvoicePrice, consumeId, costRate);
		if(ObjectUtil.isEmpty(consumeId)){
			logger.info("checkGoodwillApplyInfo,consumeId is null");
			return;
		}
		//校验开关
		CommonConfigDTO commonConfigDTO = dmscloudServiceClient.getCommonConfig(CommonConstants.GOODWILL_APPLY_MODEL_ID_CONFIG_KEY);
		if(ObjectUtil.isNotEmpty(commonConfigDTO)){
			logger.info("checkGoodwillApplyInfo,commonConfigDTO is null");
			return;
		}
		List<GoodwillNoticeInvoiceInfoPO> listPo = goodwillNoticeInvoiceInfoMapper.queryNoticeInvoice(goodwillApplyId);
		if (CollectionUtils.isEmpty(listPo)) {
			logger.info("checkGoodwillApplyInfo,listPo is null");
			return;
		}
		//校验是否重复
		boolean flag = listPo.stream()
				.anyMatch(invoiceInfo -> consumeId.equals(invoiceInfo.getConsumeId()));
		if (flag){
			logger.info("checkGoodwillApplyInfo,end,consumeId :{}", consumeId);
			throw new ServiceBizException("开票通知金额重复提交,请检查");
		}
		logger.info("checkGoodwillApplyInfo,end");
	}

	@Override
	public IPage<List> queryInvoiceHistory(Page page, Long goodwillApplyId) {
		List<Map> result = goodwillApplyInfoMapper.queryInvoiceHistory(page, goodwillApplyId);
		Integer count = goodwillApplyInfoMapper.queryInvoiceHistoryCount(goodwillApplyId);
		page.setRecords(result);
		page.setTotal(count);
		return page;
	}

	/**
	 * 卡券充值接口
	 *
	 * @param goodwillNoticeInvoiceInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	public List<Map> getLeftValue(long[] ls) {
		String successCodes = "0";
		QueryCouponDetailInfoDTO dtos = new QueryCouponDetailInfoDTO();
		dtos.setCouponIds(ls);
		dtos.setKindness(1);
		RequestDTO<QueryCouponDetailInfoDTO> requestDto = new RequestDTO<QueryCouponDetailInfoDTO>();
		requestDto.setData(dtos);
		String requestUrls = midUrlProperties.getMidEndCouponCenter() + midUrlProperties.getAllEx();
		HttpHeaders httpHeaders = new HttpHeaders();
		logger.error("中台卡券查询参数：{}", JSON.toJSONString(requestDto));
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<RequestDTO> httpEntity = new HttpEntity<RequestDTO>(requestDto, httpHeaders);
		List<Map> interList = new ArrayList<>();
		Map map1 = new HashMap<>(16);
		// 调用接口
		try {
			ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrls, HttpMethod.POST, httpEntity,
					ResponseDTO.class);
			if (responseEntity.getBody() != null) {
				if (SUCCESS_CODE.equals(responseEntity.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					interList = objectMapper.convertValue(responseEntity.getBody().getData(), List.class);

				} else {
					logger.error("中台卡券查询报错：{}", responseEntity.getBody().getReturnMessage());
					throw new ServiceBizException("中台查询卡券接口失败");
				}
			} else {
				logger.error("中台卡券查询报错：{}", responseEntity.getBody().getReturnMessage());
				throw new ServiceBizException("中台查询卡券接口失败");
			}
		} catch (Exception e) {
			logger.error("中台卡券查询报错：{}", e.getMessage());
			throw new ServiceBizException("中台查询卡券接口失败");
		}
		return interList;
	}

	/**
	 * 卡券核销接口
	 *
	 * @param goodwillNoticeInvoiceInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	public List<Map> getConsume(GoodwillStampsDTO goodwillStampsDTO) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String successCodes = "0";
		Map dtos = new HashMap(16);
		dtos.put("kindness", 1);
		// dtos.setKindness(1);
		if (!StringUtils.isNullOrEmpty(goodwillStampsDTO.getConsumeStartdAt())) {
			dtos.put("operateDateBegin", sdf.format(goodwillStampsDTO.getConsumeStartdAt()).concat(" 00:00:00"));
		}
		if (!StringUtils.isNullOrEmpty(goodwillStampsDTO.getConsumeEndAt())) {
			dtos.put("operateDateEnd", sdf.format(goodwillStampsDTO.getConsumeEndAt()).concat(" 23:59:59"));
		}

		if (!StringUtils.isNullOrEmpty(goodwillStampsDTO.getIsChargerd())) {
			if (goodwillStampsDTO.getIsChargerd() == 10041001) {
				if (!StringUtils.isNullOrEmpty(goodwillStampsDTO.getConsume())) {
					dtos.put("verifyIds", goodwillStampsDTO.getConsume());
				}
			} else {
				if (!StringUtils.isNullOrEmpty(goodwillStampsDTO.getConsume())) {
					dtos.put("excludeVerifyIds", goodwillStampsDTO.getConsume());
				}
			}
		} else {
			if (!StringUtils.isNullOrEmpty(goodwillStampsDTO.getConsume())) {
				dtos.put("verifyIds", goodwillStampsDTO.getConsume());
			}
		}

		RequestDTO<Map> requestDto = new RequestDTO<Map>();
		requestDto.setData(dtos);
		String requestUrls = midUrlProperties.getMidEndCouponCenter() + midUrlProperties.getTtCouponVerifyAll();
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<RequestDTO> httpEntity = new HttpEntity<RequestDTO>(requestDto, httpHeaders);
		List<Map> interList = new ArrayList<>();
		// 调用接口
		try {
			logger.info("z中台卡券核销查询入参:{}",JSONObject.toJSON(httpEntity));
			ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrls, HttpMethod.POST, httpEntity,
					ResponseDTO.class);
			if (responseEntity.getBody() != null) {
				if (SUCCESS_CODE.equals(responseEntity.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					interList = objectMapper.convertValue(responseEntity.getBody().getData(), List.class);
					logger.info("z中台卡券核销查询出参:{}",JSONObject.toJSON(interList));
					// if (!CommonUtils.isNullOrEmpty(interList)) {
					// JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(interList));
					// interList = jsonObject.toJavaObject(List.class);
					// }
				} else {
					logger.error("中台卡券核销查询报错：{}", responseEntity.getBody().getReturnMessage());
					throw new ServiceBizException("中台查询核销卡券接口失败");
				}
			} else {
				logger.error("中台核销卡券查询报错：{}", responseEntity.getBody().getReturnMessage());
				throw new ServiceBizException("中台查询核销卡券接口失败");
			}
		} catch (Exception e) {
			logger.error("中台核销卡券查询报错：{}", e.getMessage());
			throw new ServiceBizException("中台查询核销卡券接口失败");
		}
		return interList;
	}

	public String[] getConsumeId(GoodwillStampsDTO goodwillStampsDTO) {
		String[] consumeId = null;
		List<String> list = goodwillApplyInfoMapper.getConsumeId(goodwillStampsDTO);
		if (!CollectionUtils.isEmpty(list)) {
			List<String> filteredList = list.stream()
					.filter(str -> str != null && !str.isEmpty()&&!str.equals("null"))
					.collect(Collectors.toList());
			String stringFromList = String.join(",",filteredList);
			consumeId = stringFromList.split(",");
		}
		return consumeId;

	}
	/**
	 * 转换数据
	 * @param list1
	 * @param mapMap
	 */
	private void getCouponIdMap(List<Map> list1, Map<String, List<String>> mapMap) {
		if(!CommonUtils.isNullOrEmpty(list1)){
			for (Object object :list1) {
				JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
				Map map1 = jsonObject.toJavaObject(Map.class);
				if(ObjectUtil.isNotEmpty(mapMap.get(map1.get("coupon_id").toString())) ){
					List<String> list = 	mapMap.get(map1.get("coupon_id").toString());
					list.add(map1.get("verify_amount")==null ? "0": map1.get("verify_amount").toString());
					mapMap.put(map1.get("coupon_id").toString(),list);
				}else{
					List<String> list = 	new ArrayList<String>();
					list.add(map1.get("verify_amount")==null ? "0": map1.get("verify_amount").toString());
					mapMap.put(map1.get("coupon_id").toString(),list);
				}
			}
		}
	}

}
