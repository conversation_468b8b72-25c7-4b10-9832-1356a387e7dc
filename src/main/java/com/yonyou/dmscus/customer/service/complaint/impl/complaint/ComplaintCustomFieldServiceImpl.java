package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

    
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldPO;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintCustomFieldMapper;
import com.yonyou.dmscus.customer.service.complaint.ComplaintCustomFieldService;

import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldDTO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;




                                                                /**
 * <p>
 * 客户投诉自定义字段 用于是否查询条件及是否排序判断 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
@Service
        public class ComplaintCustomFieldServiceImpl implements ComplaintCustomFieldService {
          /**
          * 日志说明
          */
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        ComplaintCustomFieldMapper complaintCustomFieldMapper;

        /**
         * 分页查询对应数据
         *
         * @param page 分页对象
         * @param complaintCustomFieldDTO 查询条件
         * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public IPage<ComplaintCustomFieldDTO>selectPageBysql(Page page,ComplaintCustomFieldDTO complaintCustomFieldDTO){
            if(complaintCustomFieldDTO ==null){
                complaintCustomFieldDTO =new ComplaintCustomFieldDTO();
            }
            ComplaintCustomFieldPO complaintCustomFieldPo =complaintCustomFieldDTO.transDtoToPo(ComplaintCustomFieldPO.class);

            List<ComplaintCustomFieldPO>list= complaintCustomFieldMapper.selectPageBySql(page,complaintCustomFieldPo);
            if(CommonUtils.isNullOrEmpty(list)){
                page.setRecords(new ArrayList<>());
                return page;
            }else{
                List<ComplaintCustomFieldDTO>result=list.stream().map(m->m.transPoToDto(ComplaintCustomFieldDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param complaintCustomFieldDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<ComplaintCustomFieldDTO>selectListBySql(ComplaintCustomFieldDTO complaintCustomFieldDTO){
            if(complaintCustomFieldDTO ==null){
                complaintCustomFieldDTO =new ComplaintCustomFieldDTO();
            }
            ComplaintCustomFieldPO complaintCustomFieldPo =complaintCustomFieldDTO.transDtoToPo(ComplaintCustomFieldPO.class);
            List<ComplaintCustomFieldPO>list= complaintCustomFieldMapper.selectListBySql(complaintCustomFieldPo);
            if(CommonUtils.isNullOrEmpty(list)){
                return new ArrayList<>();
            }else{
                return list.stream().map(m->m.transPoToDto(ComplaintCustomFieldDTO.class)).collect(Collectors.toList());
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public ComplaintCustomFieldDTO getById(Long id){
            ComplaintCustomFieldPO complaintCustomFieldPo = complaintCustomFieldMapper.selectById(id);
            if(complaintCustomFieldPo!=null){
                return complaintCustomFieldPo.transPoToDto(ComplaintCustomFieldDTO.class);
            }else{
                throw new DALException("查询失败,没有符合条件的数据");
            }
        }

        /**
         * 根据DTO 进行数据新增
         *
         * @param complaintCustomFieldDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(ComplaintCustomFieldDTO complaintCustomFieldDTO){
            //对对象进行赋值操作
            ComplaintCustomFieldPO complaintCustomFieldPo = complaintCustomFieldDTO.transDtoToPo(ComplaintCustomFieldPO.class);
            //执行插入
            int row= complaintCustomFieldMapper.insert(complaintCustomFieldPo);
            //返回插入的值
            return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param complaintCustomFieldDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, ComplaintCustomFieldDTO complaintCustomFieldDTO){
            ComplaintCustomFieldPO complaintCustomFieldPo = complaintCustomFieldMapper.selectById(id);
            //对对象进行赋值操作
            complaintCustomFieldDTO.transDtoToPo(complaintCustomFieldPo);
            //执行更新
            int row= complaintCustomFieldMapper.updateById(complaintCustomFieldPo);
            return row;
        }

        /**
         * 根据ID 进行删除数据
         *
         * @param id
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteById(Long id){
            int deleteCount= complaintCustomFieldMapper.deleteById(id);
            logger.debug("删除数量：{}",deleteCount);
            if(deleteCount<=0){
                throw new DALException("删除失败,没有符合条件的数据");
            }else{
                return deleteCount;
            }
        }

}
