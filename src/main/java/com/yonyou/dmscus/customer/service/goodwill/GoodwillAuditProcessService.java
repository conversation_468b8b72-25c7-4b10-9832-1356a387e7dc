package com.yonyou.dmscus.customer.service.goodwill;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditProcessDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAuditProcessPO;


                                                                        /**
 * <p>
 * 亲善审批流程表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface GoodwillAuditProcessService  {
	public IPage<GoodwillAuditProcessDTO>selectPageBysql(Page page,GoodwillAuditProcessDTO goodwillAuditProcessDTO);
	public List<GoodwillAuditProcessDTO>selectListBySql(GoodwillAuditProcessDTO goodwillAuditProcessDTO);
	public GoodwillAuditProcessDTO getById(Long id);
	public int insert(GoodwillAuditProcessDTO goodwillAuditProcessDTO);
	//拒单设置
	public int insertRefuseBills(GoodwillAuditProcessDTO goodwillAuditProcessDTO);
	//审批金额设置
	public int insertAuditProcessInfo(List<GoodwillAuditProcessDTO> goodwillAuditProcessDTO);

	public int update(Long id, GoodwillAuditProcessDTO goodwillAuditProcessDTO);
	public int deleteById(Long id);
	public int deleteBatchIds(String ids);
	public List<Map>selectAuditProcessInfo(GoodwillAuditProcessDTO goodwillAuditProcessDTO);
	 //根据审批金额、审核对象，审核类型查询审批流程
	public List<GoodwillAuditProcessDTO> selectListBydealGoodwillFlowData(Integer auditObject,Integer auditType,BigDecimal auditAmount);

	public List<GoodwillAuditProcessDTO> selectList(Integer auditObject, Integer auditType, BigDecimal applyAmount, Long afterSmallAreaId, Long afterBigAreaId);
}
