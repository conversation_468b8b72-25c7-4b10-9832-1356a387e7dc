package com.yonyou.dmscus.customer.service.complaint;

    import com.baomidou.mybatisplus.core.metadata.IPage;
    import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintDealerCcmRefDTO;
    import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
    import com.yonyou.dmscus.customer.entity.dto.complaint.TeComplaintDealerCcmRefImportDTO;
    import com.yonyou.dmscus.customer.entity.po.complaint.TeComplaintDealerCcmRefImportPO;
    import org.springframework.web.multipart.MultipartFile;

    import java.util.List;


/**
 * <p>
 * 客户投诉进销商和CCM复杂人对应关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
public interface ComplaintDealerCcmRefService  {
    /**
     * 分页查询
     * @param page
     * @param complaintDealerCcmRefDTO
     * @return
     */
        IPage<ComplaintDealerCcmRefDTO> selectPageBysql(Page page, ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO);

    /**
     * 集合查询
     * @param complaintDealerCcmRefDTO
     * @return
     */
    List<ComplaintDealerCcmRefDTO> selectListBySql(ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO);

    /**
     * 通过id查询
     * @param id
     * @return
     */
        ComplaintDealerCcmRefDTO getById(Long id);

    /**
     * 新增
     * @param complaintDealerCcmRefDTO
     * @return
     */
    int insert(ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO);

    /**
     * 更新
     * @param id
     * @param complaintDealerCcmRefDTO
     * @return
     */
        int update(Long id, ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO);

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 导入
     * @param importFile
     * @return
     */
    ImportTempResult<TeComplaintDealerCcmRefImportPO> importTempTools(MultipartFile importFile);

    /**
     * 导入正式表
     * @return
     */
    int importTools();

    /**
     * 更新
     * @param complaintDealerCcmRefDTO
     * @return
     */
    boolean updateCcmAll(ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO);
}
