package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillAuditInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAuditInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillAuditInfoService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 亲善审批记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
@Service
public class GoodwillAuditInfoServiceImpl extends ServiceImpl<GoodwillAuditInfoMapper, GoodwillAuditInfoPO>
		implements GoodwillAuditInfoService {
	/** 日志对象 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillAuditInfoMapper goodwillAuditInfoMapper;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillAuditInfoDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillAuditInfoDTO> selectPageBysql(Page page, GoodwillAuditInfoDTO goodwillAuditInfoDTO) {
		if (goodwillAuditInfoDTO == null) {
			goodwillAuditInfoDTO = new GoodwillAuditInfoDTO();
		}
		GoodwillAuditInfoPO goodwillAuditInfoPo = goodwillAuditInfoDTO.transDtoToPo(GoodwillAuditInfoPO.class);

		List<GoodwillAuditInfoPO> list = goodwillAuditInfoMapper.selectPageBySql(page, goodwillAuditInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillAuditInfoDTO> result = list.stream().map(m -> m.transPoToDto(GoodwillAuditInfoDTO.class))
					.collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillAuditInfoDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillAuditInfoDTO> selectListBySql(GoodwillAuditInfoDTO goodwillAuditInfoDTO) {
		if (goodwillAuditInfoDTO == null) {
			goodwillAuditInfoDTO = new GoodwillAuditInfoDTO();
		}
		GoodwillAuditInfoPO goodwillAuditInfoPo = goodwillAuditInfoDTO.transDtoToPo(GoodwillAuditInfoPO.class);
		List<GoodwillAuditInfoPO> list = goodwillAuditInfoMapper.selectListBySql(goodwillAuditInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillAuditInfoDTO.class)).collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditInfoDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillAuditInfoDTO getById(Long id) {
		GoodwillAuditInfoPO goodwillAuditInfoPo = goodwillAuditInfoMapper.selectById(id);
		if (goodwillAuditInfoPo != null) {
			return goodwillAuditInfoPo.transPoToDto(GoodwillAuditInfoDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillAuditInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillAuditInfoDTO goodwillAuditInfoDTO) {
		// 对对象进行赋值操作
		GoodwillAuditInfoPO goodwillAuditInfoPo = goodwillAuditInfoDTO.transDtoToPo(GoodwillAuditInfoPO.class);
		// 执行插入
		int row = goodwillAuditInfoMapper.insert(goodwillAuditInfoPo);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillAuditInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillAuditInfoDTO goodwillAuditInfoDTO) {
		GoodwillAuditInfoPO goodwillAuditInfoPo = goodwillAuditInfoMapper.selectById(id);
		// 对对象进行赋值操作
		goodwillAuditInfoDTO.transDtoToPo(goodwillAuditInfoPo);
		// 执行更新
		int row = goodwillAuditInfoMapper.updateById(goodwillAuditInfoPo);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillAuditInfoMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillAuditInfoMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据审核对象和亲善单ID查询审批历史
	 *
	 * @param id,auditObject
	 * @return List<GoodwilApplyAuditProcessDTO>
	 * <AUTHOR>
	 * @since 2020/6/6
	 */
	@Override
	public List<Map> queryApplyAuditHistoryInfo(Integer auditObject, Long id) {
		List<Map> list = goodwillAuditInfoMapper.queryApplyHistory(auditObject, id);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list;
		}

	}

	/**
	 * 根据审核对象和亲善单ID查询审批驳回历史
	 *
	 * @param id,auditObject
	 * @return List<GoodwilApplyAuditProcessDTO>
	 * <AUTHOR>
	 * @since 2020/6/6
	 */
	@Override
	public List<Map> queryReturnList(GoodwillAuditInfoDTO goodwillAuditInfoDto) {
		List<Map> list = goodwillAuditInfoMapper.queryReturnList(goodwillAuditInfoDto);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list;
		}

	}
}
