package com.yonyou.dmscus.customer.service.impl;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.dto.*;
import com.yonyou.dmscus.customer.entity.dto.complaint.DepUserDataDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.GetDepUserDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.UserRoleDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.AppPushDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.MessageResponseDTO;
import com.yonyou.dmscus.customer.middleInterface.ResponseListDTO;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.service.CommonService;

import javax.annotation.Resource;

@Service
public class CommonServiceImpl implements CommonService {

	/**
	 * 日志对象
	 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	/**
	 * 接口调用成功的返回状态
	 */
	public static final String  SUCCESS_CODE     = "0";
	public static final String STATUS_CODE      = "200";
	public static final String STATUS_FAIL_CODE = "400";

	@Resource
	private RestTemplate directRestTemplate;

	@Autowired
	private MidUrlProperties midUrlProperties;

	@Value("${volvo.email.appId}")
	private String appId;

	@Value("${volvo.email.goodwillAppId}")
	private String goodwillAppId;


	/**
	 * 查询所有用户
	 *
	 * @deprecated type 1 ，查询主机厂oem，2，查询经销商
	 */
	@Override
	public List<UserInfoOutDTO> getUserAllList(int type) {
		int DEALER_CODE = 2;
		String requestUrl = midUrlProperties.getMidEndAuthCenter() + midUrlProperties.getUsersOem();
		if (type == DEALER_CODE) {
			requestUrl = midUrlProperties.getMidEndAuthCenter() + midUrlProperties.getUsersDealer();
		}
		JSONObject json = directRestTemplate.getForEntity(requestUrl, JSONObject.class).getBody();
		ResponseDto<List<UserInfoOutDTO>> responseDto = JSONObject.parseObject(json.toJSONString(),
				new TypeReference<ResponseDto<List<UserInfoOutDTO>>>(UserInfoOutDTO.class) {
				});
		if (responseDto.getReturnCode().equals("Error1004")) {
			throw new DALException("调用中台接口获取用户信息失败!");
		}
		if (responseDto.getData() != null) {
			List<UserInfoOutDTO> userList = responseDto.getData();
			return userList;
		}
		return Collections.emptyList();
	}

	/**
	 * 根据组织id和角色查询用户
	 */
	@Override
	public List<UserInfoOutDTO> getUserList(OrgSearchParams orgSearchParams) {
		String requestUrl = midUrlProperties.getMidEndAuthCenter() + midUrlProperties.getRoleOrgIdUser();
		Map<String, OrgSearchParams> data = new HashMap<>(16);
		data.put("data", orgSearchParams);
		JSONObject json = directRestTemplate.postForEntity(requestUrl, data, JSONObject.class).getBody();
		logger.info(json.toJSONString(), "jsonjson");
		ResponseDto<List<UserInfoOutDTO>> responseDto = JSONObject.parseObject(json.toJSONString(),
				new TypeReference<ResponseDto<List<UserInfoOutDTO>>>(UserInfoOutDTO.class) {
				});
		if (responseDto.getReturnCode().equals("Error1004")) {
			throw new DALException("调用中台接口获取用户信息失败!");
		}
		if (responseDto.getData() != null) {
			List<UserInfoOutDTO> userList = responseDto.getData();
			return userList;
		}
		return Collections.emptyList();
	}

	@Override
	public List<OrgInfoDTO> getOrgInfo(OrgSearchParams orgSearchParams) {
		String requestUrl = midUrlProperties.getMidEndOrgCenter() + midUrlProperties.getSelectOrgInfo();
		JSONObject json = directRestTemplate.postForEntity(requestUrl, orgSearchParams, JSONObject.class).getBody();
		ResponseDto<List<OrgInfoDTO>> responseDto = JSONObject.parseObject(json.toJSONString(),
				new TypeReference<ResponseDto<List<OrgInfoDTO>>>(OrgInfoDTO.class) {
				});
		if (responseDto.getReturnCode().equals("Error1004")) {
			throw new DALException("调用中台接口获取用户信息失败!");
		}
		if (responseDto.getData() != null) {
			List<OrgInfoDTO> userList = responseDto.getData();
			return userList;
		}
		return Collections.emptyList();
	}

	@Override
	public String sendMail(EmailInfoDto emailInfoDto) {
		String requestUrl = midUrlProperties.getMidEndMessageCenter() + midUrlProperties.getPushV1emails();
		HttpHeaders requestHeaders = new HttpHeaders();
		logger.info("sendMail={}", JSON.toJSONString(emailInfoDto));
		requestHeaders.add("appId", appId);
		// 将请求头部和参数合成一个请求
		HttpEntity<EmailInfoDto> requestEntity = new HttpEntity<>(emailInfoDto, requestHeaders);
		ResponseDto responseDto = directRestTemplate.postForEntity(requestUrl, requestEntity, ResponseDto.class).getBody();
		return responseDto.getReturnCode();
	}

	@Override
	public String sendMessage(SmsPushDTO smsPushDTO) {
		String requestUrl = midUrlProperties.getMidEndMessageCenter()+ midUrlProperties.getPushV1smss();
		HttpHeaders requestHeaders = new HttpHeaders();
		logger.info("sendMessage={}", JSON.toJSONString(smsPushDTO));
		//将请求头部和参数合成一个请求
		HttpEntity<SmsPushDTO> requestEntity = new HttpEntity<>(smsPushDTO, requestHeaders);
		ResponseDto responseDto  = directRestTemplate.postForEntity(requestUrl,requestEntity, ResponseDto.class).getBody();
		String str =  responseDto.getReturnCode();
		return str;
	}

	// 亲善专用
	@Override
	public String sendGoodwillMail(EmailInfoDto emailInfoDto) {
		String requestUrl = midUrlProperties.getMidEndMessageCenter() + midUrlProperties.getPushV1emails();
		HttpHeaders requestHeaders = new HttpHeaders();
		logger.info("发送邮件={}", JSON.toJSONString(emailInfoDto));
		requestHeaders.add("appId", goodwillAppId);
		logger.info("亲善邮件APPID=", goodwillAppId);
		System.out.println("亲善邮件APPID="+goodwillAppId);
		// 将请求头部和参数合成一个请求
		HttpEntity<EmailInfoDto> requestEntity = new HttpEntity<>(emailInfoDto, requestHeaders);
		ResponseDto responseDto = directRestTemplate.postForEntity(requestUrl, requestEntity, ResponseDto.class).getBody();
		logger.info("亲善：{}",JSONObject.toJSON(responseDto));
		String str = responseDto.getReturnCode();
		return str;
	}

	/**
	 * 通过经销商代码和角色代码查询员工列表
	 */
	@Override
	public List<UserInfoOutDTO> getUserByRoleCodeList(QueryDealerUser queryDealerUser) {
		String requestUrl = midUrlProperties.getMidEndAuthCenter() + midUrlProperties.getDealerUser();
		Map<String, Object> data = new HashMap<>(16);
		data.put("data", queryDealerUser);
		JSONObject json = directRestTemplate.postForEntity(requestUrl, data, JSONObject.class).getBody();
		ResponseDto<List<UserInfoOutDTO>> responseDto = JSONObject.parseObject(json.toJSONString(),
				new TypeReference<ResponseDto<List<UserInfoOutDTO>>>(UserInfoOutDTO.class) {
				});
		if (responseDto.getReturnCode().equals("Error1004")) {
			throw new DALException("调用中台接口获取用户信息失败!");
		}
		if (responseDto.getData() != null && responseDto.getReturnCode().equals("0")) {
			List<UserInfoOutDTO> userList = responseDto.getData();
			return userList;
		}
		return Collections.emptyList();
	}

	/**
	 * 获取部门员工
	 */
	public List<DepUserDataDTO> getDepUser(GetDepUserDTO getDepUserDTO) {
		String requestUrl = midUrlProperties.getMidEndAuthCenter() + midUrlProperties.getRoleOrgIdUser();
		Map<String, Object> data = new HashMap<>(16);
		data.put("data", getDepUserDTO);
		JSONObject json = directRestTemplate.postForEntity(requestUrl, data, JSONObject.class).getBody();
		ResponseDto<List<DepUserDataDTO>> responseDto = JSONObject.parseObject(json.toJSONString(),
				new TypeReference<ResponseDto<List<DepUserDataDTO>>>(DepUserDataDTO.class) {
				});
		if (responseDto.getReturnCode().equals("Error1004")) {
			throw new DALException("调用中台接口获取用户信息失败!");
		}
		if (responseDto.getData() != null && responseDto.getReturnCode().equals("0")) {
			List<DepUserDataDTO> userList = responseDto.getData();
			return userList;
		}
		return Collections.emptyList();

	}

	/**
	 * 获取车型名称
	 */
	public List<GetModelNameDTO> getModelName(modelNameDTO modelNameDTO){
		String successCode = "0";
		String requestUrl = midUrlProperties.getMidEndBasicdataCenter()+midUrlProperties.getModelAll();
		Map<String, Object> data = new HashMap<>(16);
		data.put("data", modelNameDTO);
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Map> httpEntity = new HttpEntity<>(data, httpHeaders);
		ResponseEntity<ResponseListDTO> responseEntity =directRestTemplate.exchange(requestUrl, HttpMethod.POST,httpEntity,
				ResponseListDTO.class);
		if(responseEntity.getBody()!=null) {
			if(successCode.equals(responseEntity.getBody().getReturnCode())) {
				ObjectMapper objectMapper = new ObjectMapper();
				objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
				List<GetModelNameDTO> rs = objectMapper.convertValue(responseEntity.getBody().getData(),
						new com.fasterxml.jackson.core.type.TypeReference<List<GetModelNameDTO>>(){}) ;
				System.out.println(rs.get(0)+"==============测试");
				return rs;
			}else{
				throw new DALException("获取车型名称异常，请稍后再试");
			}
		}
		return null;
	}

	/**
	 * 获取车型名称
	 */
	public GetModelNameDTO getModelNameById(Integer id){
		String successCode = "0";
		String requestUrl = midUrlProperties.getMidEndBasicdataCenter()+midUrlProperties.getModelById()+"?id="+id;
		String.format(requestUrl, id);
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Map> httpEntity = new HttpEntity<>(null, httpHeaders);
		ResponseEntity<ResponseDto> responseEntity =directRestTemplate.exchange(requestUrl, HttpMethod.GET,httpEntity,
				ResponseDto.class);
		if(responseEntity.getBody()!=null) {
			if(successCode.equals(responseEntity.getBody().getReturnCode())) {
				ObjectMapper objectMapper = new ObjectMapper();
				objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
				GetModelNameDTO rs = objectMapper.convertValue(responseEntity.getBody().getData(),
						new com.fasterxml.jackson.core.type.TypeReference<GetModelNameDTO>(){}) ;
				System.out.println(rs+"==============测试");
				return rs;
			}else{
				throw new DALException("获取车型名称异常，请稍后再试");
			}
		}
		return null;
	}


	/**
	 * 消息推送APP
	 *
	 * @param appPushDTO
	 * @return (non - Javadoc)
	 * <AUTHOR>
	 * @date 2020年9月14日
	 */

	public String messageSendApp(AppPushDTO appPushDTO) {
		appPushDTO.setMode("notify");
		appPushDTO.setTargetType("ALIAS");

		//准备请求头信息
		HttpHeaders httpHeaders = new HttpHeaders();
		logger.info("------------------------>appId:{}", appId);
		httpHeaders.put("appId", Collections.singletonList(appId));
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<AppPushDTO> httpEntity = new HttpEntity<AppPushDTO>(appPushDTO, httpHeaders);
		//准备 url
		String url = midUrlProperties.getMidEndMessageCenter() + midUrlProperties.getPushV1Apps();
		logger.info("客诉推送={}", JSON.toJSONString(appPushDTO));
		//调用接口
		try {
			ResponseEntity<MessageResponseDTO> responseEntity = directRestTemplate.exchange(url, HttpMethod.POST, httpEntity,
					MessageResponseDTO.class);
			logger.info("------------------------>reponse.body:{}", responseEntity.getBody());
			if (responseEntity.getBody() != null) {
				if (SUCCESS_CODE.equals(responseEntity.getBody().getCode()) || STATUS_CODE.equals(responseEntity.getBody().getCode())) {
//                    logger.info("取送车推送成功：{}",responseEntity.getBody().getData());
					logger.info("客诉推送成功：{}", responseEntity.getBody());
					return responseEntity.getBody().getMsgId();
				} else {
					logger.error("客诉推送失败：{}", responseEntity.getBody().getError());
					throw new ServiceBizException("客诉推送失败");
				}
			} else {
				logger.error("客诉推送失败：{}", responseEntity.getBody().getError());
				throw new ServiceBizException("客诉推送失败");
			}
		} catch (Exception e) {
			logger.error("客诉推送失败：{}", e.getMessage());
			throw new ServiceBizException("客诉推送失败");
		}
	}
	/**
	 * 400获取签名
	 */
	public String GetCheckSign(String pathUrl, String clientID, String clientPwd, String noncestr,String timestamp)
	{
		pathUrl = pathUrl.toLowerCase();
		SortedMap<String, String> sortedParameters = new TreeMap<>();
		sortedParameters.put("apiuid",clientID);
		sortedParameters.put("apipwd",clientPwd );
		sortedParameters.put("noncestr",noncestr);
		sortedParameters.put("timestamp", timestamp);
		// 字串拼接
		StringBuilder sbSignContent = new StringBuilder();
		sbSignContent.append(pathUrl);
        sbSignContent.append("apipwd="+clientPwd+"apiuid="+clientID+"noncestr="+noncestr+"timestamp="+timestamp);

		return DigestUtils.md5DigestAsHex(sbSignContent.toString().getBytes());
	}
	/**
	 * 递归查询大区小区下的经销商
	 */
	public List<OrgDetailDTO> selectDealerCode(int codes){

		String successCode = "0";
		String requestUrl = midUrlProperties.getMidEndOrgCenter()+midUrlProperties.getDownOrgInfo()+codes;
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		JSONObject json = directRestTemplate.getForEntity(requestUrl, JSONObject.class).getBody();
		ResponseDto<List<OrgDetailDTO>> responseDto = JSONObject.parseObject(json.toJSONString(), new com.alibaba.fastjson.TypeReference<ResponseDto<List<OrgDetailDTO>>>(OrgDetailDTO.class) {
		});
		if(responseDto.getReturnCode().equals("Error1004")){
			throw new DALException("调用中台接口获取用户信息失败!");
		}
		if(responseDto.getData()!=null) {
			List<OrgDetailDTO> userList = responseDto.getData();
			return userList;
		}
		return Collections.emptyList();
	}


	public UserRoleDTO getUserRoleDTO() {
		String successCode = "0";
		String requestUrl = midUrlProperties.getMidEndAuthCenter()+midUrlProperties.getUserInfo();
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Map> httpEntity = new HttpEntity<>(httpHeaders);
		ResponseEntity<ResponseDto> responseEntity =directRestTemplate.exchange(requestUrl, HttpMethod.GET,httpEntity,
				ResponseDto.class);
		if(responseEntity.getBody()!=null) {
			if(successCode.equals(responseEntity.getBody().getReturnCode())) {
				ObjectMapper objectMapper = new ObjectMapper();
				objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
				UserRoleDTO rs = objectMapper.convertValue(responseEntity.getBody().getData(),
						new com.fasterxml.jackson.core.type.TypeReference<UserRoleDTO>(){}) ;
				System.out.println(rs+"==============测试");
				return rs;
			}else{
				throw new DALException("获取角色，请稍后再试");
			}
		}
		return null;
	}

	//获取周末和节假日
	public  Set<String> JJR(int year, int month) {
		//获取所有的周末
		Set<String> monthWekDay = getMonthWekDay(year, month);
		//http://timor.tech/api/holiday api文档地址
		Map jjr = getJjr(year, month+1);
		Integer code = (Integer) jjr.get("code");
		if(code  != 0){
			return monthWekDay;
		}
		Map<String,Map<String,Object>> holiday = (Map<String, Map<String,Object>>) jjr.get("holiday");
		Set<String> strings = holiday.keySet();
		for (String str: strings) {
			Map<String, Object> stringObjectMap = holiday.get(str);
			Integer wage = (Integer) stringObjectMap.get("wage");
			String date = (String) stringObjectMap.get("date");
			//筛选掉补班
			if(wage .equals( 1)){
				monthWekDay.remove(date);
			}else{
				monthWekDay.add(date);
			}
		}
		return monthWekDay;
	}
	//获取节假日不含周末
	public  Map getJjr(int year, int month) {
		String url = "http://timor.tech/api/holiday/year/"+year+"-"+month;
		OkHttpClient client = new OkHttpClient();
		Response response;
		//解密数据
		String rsa = null;
		Request request = new Request.Builder()
				.url(url)
				.get()
				.addHeader("Content-Type", "application/x-www-form-urlencoded")
				.build();
		try {
			response = client.newCall(request).execute();
			rsa = response.body().string();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return JSONObject.parseObject(rsa, Map.class);
	}

	//获取周末  月从0开始
	public  Set<String> getMonthWekDay(int year,int mouth){
		Set<String> dateList = new HashSet<>();
		SimpleDateFormat simdf = new SimpleDateFormat("yyyy-MM-dd");
		Calendar calendar = new GregorianCalendar(year, mouth , 1);
		Calendar endCalendar = new GregorianCalendar(year, mouth , 1);
		endCalendar.add(Calendar.MONTH,1);
		while (true) {
			int weekday=calendar.get(Calendar.DAY_OF_WEEK);
			if(weekday == 1 || weekday== 7){
				dateList.add(simdf.format(calendar.getTime()));
			}
			calendar.add(Calendar.DATE,1);
			if (calendar.getTimeInMillis() >= endCalendar.getTimeInMillis()){
				break;
			}
		}
		return dateList;
	}


}
