/*
 * Copyright (C), 2020-2020, 上海用友汽车有限公司
 * FileName: IMiddleGroundVehicleService.java
 * Author:   caizhongming
 * Date:     2020年5月28日 上午11:35:52
 * Description: //模块目的、功能描述      
 * History: //修改记录
 * <author>      <time>      <version>    <desc>
 * 修改人姓名             修改时间            版本号                  描述
 */
package com.yonyou.dmscus.customer.service.common;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.middleInterface.OwnerVehicleVO;
import com.yonyou.dmscus.customer.middleInterface.ResponseDTO;

import java.util.Map;

/**
 * 〈一句话功能简述〉<br> 
 * 〈功能详细描述〉
 *
 * <AUTHOR>
 * @see [相关类/方法]（可选）
 * @since [产品/模块版本] （可选）
 */
public interface IMiddleGroundVehicleService {

	/**
	 * 功能描述: <br>
	 * 〈功能详细描述〉
	 * 中台增加车辆信息接口调用
	 * @param map 传入车辆数据MAP
	 * @return
	 * @see [相关类/方法](可选)
	 * @since [产品/模块版本](可选)
	 */
	public ResponseDTO addMidVehicle(Map<String,Object> map);
	
	
	/**
	 * 功能描述: <br>
	 * 〈功能详细描述〉
	 * 中台更新车辆信息接口调用
	 * @param map 传入车辆数据MAP
	 * @return
	 * @see [相关类/方法](可选)
	 * @since [产品/模块版本](可选)
	 */
	public ResponseDTO updateMidVehicle(Map<String,Object> map);
	
	
	/**
	 * 功能描述: <br>
	 * 〈功能详细描述〉
	 * 获取vin码列表的验证情况数据
	 * map:key=vinList value=vinList数组vin码编码
	 * 返回不存在的VIN码Map映射 Map<String,Object> = Map<key="vin9999","no">
	 *    比如：LYVUE25D1LB569232,LYVUE25D5LB564518这两个VIN码是存在的。
	 * @see [相关类/方法](可选)
	 * @since [产品/模块版本](可选)
	 */
	public ResponseDTO<Map<String,Object>> getVinListCheckInfo(Map<String,Object> paramMap);
	
	
	/**
	 * 功能描述: <br>
	 * 〈功能详细描述〉
	 * 根据vin查找车主车型数据
	 * @return
	 * @see [相关类/方法](可选)
	 * @since [产品/模块版本](可选)
	 */
	public IPage<OwnerVehicleVO> queryVehicleInfo(Page page, Map map);
}
