package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Lists;
import com.yonyou.dmscloud.framework.util.bean.ApplicationContextHelper;
import com.yonyou.dmscus.customer.constants.lnvitation.VerifyTypeEnum;
import com.yonyou.dmscus.customer.dao.voc.LossDataRecordMapper;
import com.yonyou.dmscus.customer.dao.voc.VocInviteVehicleTaskRecordMapper;
import com.yonyou.dmscus.customer.dto.OnlineOfflineResultDTO;
import com.yonyou.dmscus.customer.dto.cdp.CdpTagTaskParameterDto;
import com.yonyou.dmscus.customer.entity.dto.clue.dictionary.DictionaryIdDTO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.LossDataRecordPo;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocInviteVehicleTaskRecordPo;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: VocInviteVehicleTaskRecordServiceImpl
 * @projectName dmscus.customer
 * @description:
 * @date 2022/11/1814:45
 */
@Service
public class VocInviteVehicleTaskRecordServiceImpl implements  VocInviteVehicleTaskRecordService {
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    LossDataRecordService  lossDataRecordService;
    @Autowired
    private VocInviteVehicleTaskRecordMapper vocInviteVehicleTaskRecordMapper;
    @Autowired
    private LossDataRecordMapper lossDataRecordMapper;
    @Autowired
    private InviteVehicleRecordService inviteVehicleRecordService;


    @Override
    public int selectVocCountByVin(String vin, Long id) {
        return  vocInviteVehicleTaskRecordMapper.selectVocCount(vin,id);
    }

    @Override
    public int  selectVocCountByVinT(String vin, Long id) {
        return  vocInviteVehicleTaskRecordMapper.selectVocCountT(vin,id);
    }


    @Override
    public List<Long> selectTypeXIIByVin(String vin) {
        return  vocInviteVehicleTaskRecordMapper.selectTypeXIIByVin(vin);
    }
    public int selectLossWarningByVin(String vin){
        return  vocInviteVehicleTaskRecordMapper.selectLossWarningByVin(vin);
    }
    @Override
    public void setVocOrderTime(String orderTime, String vin, String code) {
        logger.info("setVocOrderTime：vin{},orderTime{}", vin, orderTime);
        List<InviteVehicleRecordPO> list = inviteVehicleRecordService.selectTowListByVinAndCode(vin, code);
        if (CollUtil.isNotEmpty(list)) {
            InviteVehicleRecordPO po = list.get(0);
            logger.info("setVocOrderTime id：{}", po.getId());
            LambdaQueryWrapper<VocInviteVehicleTaskRecordPo> qw = new LambdaQueryWrapper<>();
            VocInviteVehicleTaskRecordPo pox = new VocInviteVehicleTaskRecordPo();

            try {
                Date time = DateUtils.parse(orderTime, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS);
                logger.info("setVocOrderTime time：{}", time);
                pox.setOrderAt(time);
                qw.eq(VocInviteVehicleTaskRecordPo::getRecordId, po.getId());
                vocInviteVehicleTaskRecordMapper.update(pox, qw);
                if (vin != null) {
                    logger.info("setVocOrderTime vin != null");
                    LossDataRecordPo recordPo = lossDataRecordService.selectByVin(vin);
                    if (ObjectUtils.isNotEmpty(recordPo)) {
                        logger.info("setVocOrderTime recordPo：{}", JSON.toJSONString(recordPo));
                        recordPo.setIsActive(1);
                        recordPo.setActiveAt(time);
                        recordPo.setActiveDealerCode(code);
                        lossDataRecordService.update(recordPo);
                        logger.info("setVocOrderTime 流失客户激活：{}", vin);
                    }
                }

            } catch (Exception e) {
                logger.info("保存异常：{},{}", vin, orderTime);
            }
            logger.info("setVocOrderTime end：vin{},orderTime{}", vin, orderTime);
        }
    }

    @Override
    public void deleteVocOrderTime(String vin, String code) {
        logger.info("deleteVocOrderTime：vin{}",vin);
        List<InviteVehicleRecordPO>  list  = inviteVehicleRecordService.selectTowListByVinAndCode(vin,code);
        if(CollUtil.isNotEmpty(list)){
            InviteVehicleRecordPO po =  list.get(0);
            logger.info("deleteVocOrderTime id：{}",po.getId());
            UpdateWrapper<VocInviteVehicleTaskRecordPo> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda()
                         .set(VocInviteVehicleTaskRecordPo::getOrderAt, null)
                         .eq(VocInviteVehicleTaskRecordPo::getRecordId, po.getId());
            vocInviteVehicleTaskRecordMapper.update(null,updateWrapper);
            logger.info("deleteVocOrderTime 结束：{}",vin);
        }
        if (vin != null) {
            LossDataRecordPo recordPo = lossDataRecordService.selectByVin(vin);
            logger.info("deleteVocOrderTime：recordPo{}",recordPo);
            if (ObjectUtils.isNotEmpty(recordPo)) {
                UpdateWrapper<LossDataRecordPo> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda()
                        .set(LossDataRecordPo::getIsActive, 0)
                        .set(LossDataRecordPo::getActiveAt, null)
                        .set(LossDataRecordPo::getActiveDealerCode, null)
                        .eq(LossDataRecordPo::getId, recordPo.getId());
                lossDataRecordMapper.update(null,updateWrapper);
                logger.info("deleteVocOrderTime is_active vin:{}", vin);
            }
        }
    }

    @Override
    public int addRecord(VocInviteVehicleTaskRecordPo recPo) {
        return vocInviteVehicleTaskRecordMapper.insert(recPo);
    }

    @Override
    public int selectCountByIcmId(Long icmId) {
        return vocInviteVehicleTaskRecordMapper.selectCountByIcmId(icmId);
    }

    @Override
    public VocInviteVehicleTaskRecordPo selectIcmIdByRecordId(Long recordId) {
        return vocInviteVehicleTaskRecordMapper.selectIcmIdByRecordId(recordId);
    }

    @Override
    public void updateVerifyStatusById(List<Long> list, Integer verifyStatus, LocalDate verifyTime) {
        if (CollectionUtils.isEmpty(list)) {
            logger.info("updateVerifyStatusById list is empty");
            return;
        }
        for (List<Long> longs : Lists.partition(list, 500)) {
            vocInviteVehicleTaskRecordMapper.updateVerifyStatusById(longs, verifyStatus, verifyTime);
        }
        logger.info("updateVerifyStatusById,first param:{}",list.get(0));
    }

    @Override
    public void updateVerifyStatusAllById(List<VocInviteVehicleTaskRecordPo> list){
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        Lists.partition(list, 500).forEach(vocInviteVehicleTaskRecordMapper::updateVerifyStatusAllById);
    }

    @Override
    public void updateDictionaryData() {
        logger.info("updateDictionaryData,start");
        int startPage = 0;
        int page = 100000;
        Long minId;
        Long maxId;
        while (Boolean.TRUE){
            int endPage = startPage + page;
            logger.info("updateDictionaryData,startPage:{},endPage:{}", startPage, endPage);
            //分页查询
            DictionaryIdDTO dto = vocInviteVehicleTaskRecordMapper.selectIdByLimit(startPage, endPage);
            if(Objects.isNull(dto)){
                return;
            }
            minId = dto.getMinId();
            maxId = dto.getMaxId();
            logger.info("updateDictionaryData,minId:{},maxId:{}", minId, maxId);
            if(Objects.isNull(minId) || Objects.isNull(maxId)){
                return;
            }
            startPage = endPage;
            //批量修改
            vocInviteVehicleTaskRecordMapper.updateTypeById(minId, maxId);
        }
        logger.info("updateDictionaryData,end");
    }

    @Override
    @Transactional
    public List<InviteVehicleRecordPO> verifyEvidence(List<OnlineOfflineResultDTO> dtoList, Map<String, List<InviteVehicleRecordPO>> mapVinIds) {
        if (CollectionUtils.isEmpty(dtoList)) {
            logger.info("verifyEvidence,CollectionUtils.isEmpty(dtoList)");
            return Collections.emptyList();
        }
        logger.info("verifyEvidence,dtoList:{}", dtoList.size());
        List<InviteVehicleRecordPO> poList = dtoList.stream()
                .filter(dto -> Objects.isNull(dto.getOnoff_status_tag()) ||
                        OnlineOfflineResultDTO.NO.equals(dto.getOnoff_status_tag()) ||
                        (OnlineOfflineResultDTO.YES.equals(dto.getLight_status_tag()) && compareTime(dto)))
                .flatMap(dto -> Optional.ofNullable(mapVinIds.get(dto.getVehicle_vin())).orElse(Collections.emptyList()).stream())
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(poList)){
            logger.info("verifyEvidence,CollectionUtils.isEmpty(poList)");
            return Collections.emptyList();
        }
        logger.info("verifyEvidence,poList:{}", poList.size());
        List<Long> upList  = poList.stream().map(InviteVehicleRecordPO::getId).distinct().collect(Collectors.toList());
        //修改数据
        this.updateVerifyStatusById(upList, VerifyTypeEnum.VERIFIED.getIntCode(), LocalDate.now());
        return poList;
    }

    @Override
    public int markHistoryDataAsValid(){
        logger.info("markHistoryDataAsValid,start");
        int count;
        count = vocInviteVehicleTaskRecordMapper.updateHistoryValidationStatusByOverdue();
        logger.info("markHistoryDataAsValid,count:{}", count);
        count += vocInviteVehicleTaskRecordMapper.updateHistoryValidationStatusByComplete();
        logger.info("markHistoryDataAsValid,count:{}", count);
        count += vocInviteVehicleTaskRecordMapper.updateHistoryValidationStatusByNoNull();
        logger.info("markHistoryDataAsValid,end,count:{}", count);
        return count;
    }

    @Override
    public void addAll(List<VocInviteVehicleTaskRecordPo> list) {
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(list)){
            logger.info("addAll,list:{}",list.size());
            Lists.partition(list,500).forEach(vocInviteVehicleTaskRecordMapper::insertList);
        }
    }

    public boolean compareTime(OnlineOfflineResultDTO dto) {
        String tag_upd_date = dto.getTag_upd_date();
        String warning_time = dto.getWarning_time();
        logger.info("compareTime,tag_upd_date:{},warning_time:{}", tag_upd_date, warning_time);
        if (Objects.isNull(tag_upd_date) || Objects.isNull(warning_time)) {
            logger.info("compareTime, tag_upd_date or warning_time is null");
            return false;
        }
        LocalDate localDate1;
        LocalDate localDate2;
        try {
            localDate1 = LocalDate.parse(tag_upd_date.substring(0, 10), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            localDate2 = LocalDate.parse(warning_time.substring(0, 10), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (DateTimeParseException e) {
            logger.error("compareTime, failed to parse date", e);
            return false;
        }
        boolean flag = localDate1.isAfter(localDate2);
        logger.info("compareTime: {}", flag);
        return flag;
    }

    @Override
    public void updateDailyMile(List<CdpTagTaskParameterDto> list) {
        List<CdpTagTaskParameterDto> cdpList = list.stream()
                .filter(obj -> obj.getDailyMile() != null)
                .collect(Collectors.toList());
        if (cdpList!=null&cdpList.size()>0) {
            vocInviteVehicleTaskRecordMapper.updateDailyMile(cdpList);
        }
    }



    /**
     * 更新线索是否bev状态
     *
     */
    @Override
    public void updateBevLeadList(List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (List<Long> idList : Lists.partition(list, 500)) {
            vocInviteVehicleTaskRecordMapper.updateBevLeadList(idList);
        }
    }

}
