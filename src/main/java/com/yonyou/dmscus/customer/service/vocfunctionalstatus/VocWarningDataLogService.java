package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataLogPo;

import java.util.List;

/**
 * <AUTHOR>
 * @title: VocWarningDataLogSrevice
 * @projectName dmscus.customer
 * @description: TODO
 * @date 2022/11/118:47
 */
public interface VocWarningDataLogService {
    int insertList(List<VocWarningDataLogPo> logPOSList);

    List<VocWarningDataLogPo> selectListBydt(String data, int begIndex, Integer endIndex);

    int selectCount(String vin);

    int selectWarningdailyLogByVin(String vin);
}
