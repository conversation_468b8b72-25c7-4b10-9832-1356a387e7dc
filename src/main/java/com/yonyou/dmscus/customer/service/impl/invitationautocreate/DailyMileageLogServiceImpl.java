package com.yonyou.dmscus.customer.service.impl.invitationautocreate;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.invitationautocreate.DailyMileageLogMapper;
import com.yonyou.dmscus.customer.entity.dto.invitationautocreate.DailyMileageLogDTO;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.DailyMileageLogPO;
import com.yonyou.dmscus.customer.service.invitationautocreate.DailyMileageLogService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 平均里程计算日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-13
 */
@Service
public class DailyMileageLogServiceImpl implements DailyMileageLogService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    DailyMileageLogMapper dailyMileageLogMapper;

    /**
     * 分页查询对应数据
     *
     * @param page               分页对象
     * @param dailyMileageLogDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto
                     *   . DailyMileageLogDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<DailyMileageLogDTO> selectPageBysql(Page page, DailyMileageLogDTO dailyMileageLogDTO) {
        if (dailyMileageLogDTO == null) {
            dailyMileageLogDTO = new DailyMileageLogDTO();
        }
        DailyMileageLogPO dailyMileageLogPO = dailyMileageLogDTO.transDtoToPo(DailyMileageLogPO.class);

        List<DailyMileageLogPO> list = dailyMileageLogMapper.selectPageBySql(page, dailyMileageLogPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<DailyMileageLogDTO> result = list.stream().map(m -> m.transPoToDto(DailyMileageLogDTO.class))
                    .collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param dailyMileageLogDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.DailyMileageLogDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<DailyMileageLogDTO> selectListBySql(DailyMileageLogDTO dailyMileageLogDTO) {
        if (dailyMileageLogDTO == null) {
            dailyMileageLogDTO = new DailyMileageLogDTO();
        }
        DailyMileageLogPO dailyMileageLogPO = dailyMileageLogDTO.transDtoToPo(DailyMileageLogPO.class);
        List<DailyMileageLogPO> list = dailyMileageLogMapper.selectListBySql(dailyMileageLogPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(DailyMileageLogDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.DailyMileageLogDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public DailyMileageLogDTO getById(Long id) {
        DailyMileageLogPO dailyMileageLogPO = dailyMileageLogMapper.selectById(id);
        if (dailyMileageLogPO != null) {
            return dailyMileageLogPO.transPoToDto(DailyMileageLogDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param dailyMileageLogDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(DailyMileageLogDTO dailyMileageLogDTO) {
        //对对象进行赋值操作
        DailyMileageLogPO dailyMileageLogPO = dailyMileageLogDTO.transDtoToPo(DailyMileageLogPO.class);
        //执行插入
        int row = dailyMileageLogMapper.insert(dailyMileageLogPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                 主键ID
     * @param dailyMileageLogDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, DailyMileageLogDTO dailyMileageLogDTO) {
        DailyMileageLogPO dailyMileageLogPO = dailyMileageLogMapper.selectById(id);
        //对对象进行赋值操作
        dailyMileageLogDTO.transDtoToPo(dailyMileageLogPO);
        //执行更新
        int row = dailyMileageLogMapper.updateById(dailyMileageLogPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = dailyMileageLogMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = dailyMileageLogMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }
}
