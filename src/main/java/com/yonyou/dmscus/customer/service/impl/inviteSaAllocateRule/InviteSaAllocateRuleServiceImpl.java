package com.yonyou.dmscus.customer.service.impl.inviteSaAllocateRule;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dto.InvitationRuleVcdcParamsVo;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.UserInfoDTO;
import com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule.InviteSaAllocateRulePO;
import com.yonyou.dmscus.customer.dao.inviteSaAllocateRule.InviteSaAllocateRuleMapper;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.inviteSaAllocateRule.InviteSaAllocateRuleService;
import com.yonyou.dmscus.customer.entity.dto.inviteSaAllocateRule.InviteSaAllocateRuleDTO;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * SA分配规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
@Service
public class InviteSaAllocateRuleServiceImpl implements InviteSaAllocateRuleService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteSaAllocateRuleMapper inviteSaAllocateRuleMapper;
    @Autowired
    BusinessPlatformService businessPlatformService;

    /**
     * 分页查询对应数据
     *
     * @param page                    分页对象
     * @param inviteSaAllocateRuleDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto
                       *       .       InviteSaAllocateRuleDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<InviteSaAllocateRuleDTO> selectPageBysql(Page page, InviteSaAllocateRuleDTO inviteSaAllocateRuleDTO) {
        if (inviteSaAllocateRuleDTO == null) {
            inviteSaAllocateRuleDTO = new InviteSaAllocateRuleDTO();
        }
        InviteSaAllocateRulePO inviteSaAllocateRulePO = inviteSaAllocateRuleDTO.transDtoToPo(InviteSaAllocateRulePO
                .class);

        List<InviteSaAllocateRulePO> list = inviteSaAllocateRuleMapper.selectPageBySql(page, inviteSaAllocateRulePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteSaAllocateRuleDTO> result = list.stream().map(m -> m.transPoToDto(InviteSaAllocateRuleDTO
                    .class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param inviteSaAllocateRuleDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.inviteSaAllocateRule.InviteSaAllocateRuleDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<InviteSaAllocateRuleDTO> selectListBySql(InviteSaAllocateRuleDTO inviteSaAllocateRuleDTO) {
        if (inviteSaAllocateRuleDTO == null) {
            inviteSaAllocateRuleDTO = new InviteSaAllocateRuleDTO();
        }
        InviteSaAllocateRulePO inviteSaAllocateRulePO = inviteSaAllocateRuleDTO.transDtoToPo(InviteSaAllocateRulePO
                .class);
        List<InviteSaAllocateRulePO> list = inviteSaAllocateRuleMapper.selectListBySql(inviteSaAllocateRulePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteSaAllocateRuleDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.inviteSaAllocateRule.InviteSaAllocateRuleDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public InviteSaAllocateRuleDTO getById(Long id) {
        InviteSaAllocateRulePO inviteSaAllocateRulePO = inviteSaAllocateRuleMapper.selectById(id);
        if (inviteSaAllocateRulePO != null) {
            return inviteSaAllocateRulePO.transPoToDto(InviteSaAllocateRuleDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param inviteSaAllocateRuleDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(InviteSaAllocateRuleDTO inviteSaAllocateRuleDTO) {
        //对对象进行赋值操作
        InviteSaAllocateRulePO inviteSaAllocateRulePO = inviteSaAllocateRuleDTO.transDtoToPo(InviteSaAllocateRulePO
                .class);
        //执行插入
        int row = inviteSaAllocateRuleMapper.insert(inviteSaAllocateRulePO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                      主键ID
     * @param inviteSaAllocateRuleDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, InviteSaAllocateRuleDTO inviteSaAllocateRuleDTO) {
        InviteSaAllocateRulePO inviteSaAllocateRulePO = inviteSaAllocateRuleMapper.selectById(id);
        //对对象进行赋值操作
        inviteSaAllocateRuleDTO.transDtoToPo(inviteSaAllocateRulePO);
        //执行更新
        int row = inviteSaAllocateRuleMapper.updateById(inviteSaAllocateRulePO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = inviteSaAllocateRuleMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = inviteSaAllocateRuleMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 保存分配规则
     *
     * @param inviteSaAllocateRuleDTO
     * @return
     */
    @Override
    public int saveInviteSaAllocateRule(InviteSaAllocateRuleDTO inviteSaAllocateRuleDTO) {
        InviteSaAllocateRulePO inviteSaAllocateRulePO = inviteSaAllocateRuleMapper.selectById(inviteSaAllocateRuleDTO
                .getId());
        //对对象进行赋值操作
        inviteSaAllocateRuleDTO.transDtoToPo(inviteSaAllocateRulePO);
        return inviteSaAllocateRuleMapper.updateById(inviteSaAllocateRulePO);
    }

    /**
     * 查询用户信息
     *
     * @return
     */
    @Override
    public List<UserInfoDTO> getUserInfo() {
        return this.getUserList();
    }

    @Override
    public InviteSaAllocateRuleDTO getInviteSaAllocateRuleDlr() {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        LambdaQueryWrapper<InviteSaAllocateRulePO> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(InviteSaAllocateRulePO::getDealerCode, loginInfoDto.getOwnerCode());
        InviteSaAllocateRulePO po = inviteSaAllocateRuleMapper.selectOne(queryWrapper);
        if (po != null) {
            return po.transPoToDto(InviteSaAllocateRuleDTO.class);
        }
        return null;
    }

    @Override
    public List<InviteSaAllocateRuleDTO> getInviteSaAllocateRule(InvitationRuleVcdcParamsVo vo) {
        String areaId = null;
        if(vo.getAreaId()!=null){
            areaId=vo.getAreaId();
        }else if(vo.getAreaManageId()!=null){
            areaId=vo.getAreaManageId();
        }
        List<String> codes = businessPlatformService.getDealercodes(areaId,vo.getLargeAreaId(),vo.getDealerCode(),vo.getDealerName());
        List<InviteSaAllocateRulePO> list = inviteSaAllocateRuleMapper.getInviteSaAllocateRule(codes);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteSaAllocateRuleDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 查询用户列表(中台接口未实现)
     *
     * @return
     */
    private List<UserInfoDTO> getUserList() {
        List<UserInfoDTO> list = new ArrayList<UserInfoDTO>();
        UserInfoDTO po = new UserInfoDTO();
        po.setId(1L);
        po.setAccount("F001");
        po.setUsername("李萍");
        list.add(po);
        po = new UserInfoDTO();
        po.setId(2L);
        po.setAccount("F002");
        po.setUsername("莫延");
        list.add(po);
        po = new UserInfoDTO();
        po.setId(3L);
        po.setAccount("F004");
        po.setUsername("张飞");
        list.add(po);
        po = new UserInfoDTO();
        po.setId(4L);
        po.setAccount("F008");
        po.setUsername("测试");
        list.add(po);
        po = new UserInfoDTO();
        po.setId(5L);
        po.setAccount("F011");
        po.setUsername("小票");
        list.add(po);
        return list;
    }
}
