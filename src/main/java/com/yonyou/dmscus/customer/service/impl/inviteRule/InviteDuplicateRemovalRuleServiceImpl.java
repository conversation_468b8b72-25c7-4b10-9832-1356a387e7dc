package com.yonyou.dmscus.customer.service.impl.inviteRule;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InviteDuplicateRemovalRulePO;
import com.yonyou.dmscus.customer.dao.inviteRule.InviteDuplicateRemovalRuleMapper;
import com.yonyou.dmscus.customer.service.inviteRule.InviteDuplicateRemovalRuleService;
import com.yonyou.dmscus.customer.entity.dto.inviteRule.InviteDuplicateRemovalRuleDTO;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 邀约去重规则表，本章表所存储的数据，应该都是一个默认值。 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-17
 */
@Service
public class InviteDuplicateRemovalRuleServiceImpl implements InviteDuplicateRemovalRuleService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteDuplicateRemovalRuleMapper inviteDuplicateRemovalRuleMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                          分页对象
     * @param inviteDuplicateRemovalRuleDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto
                       *       .       InviteDuplicateRemovalRuleDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<InviteDuplicateRemovalRuleDTO> selectPageBysql(Page page, InviteDuplicateRemovalRuleDTO
            inviteDuplicateRemovalRuleDTO) {
        if (inviteDuplicateRemovalRuleDTO == null) {
            inviteDuplicateRemovalRuleDTO = new InviteDuplicateRemovalRuleDTO();
        }
        InviteDuplicateRemovalRulePO inviteDuplicateRemovalRulePO = inviteDuplicateRemovalRuleDTO.transDtoToPo
                (InviteDuplicateRemovalRulePO.class);

        List<InviteDuplicateRemovalRulePO> list = inviteDuplicateRemovalRuleMapper.selectPageBySql(page,
                inviteDuplicateRemovalRulePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteDuplicateRemovalRuleDTO> result = list.stream().map(m -> m.transPoToDto
                    (InviteDuplicateRemovalRuleDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param inviteDuplicateRemovalRuleDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.inviteRule.InviteDuplicateRemovalRuleDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<InviteDuplicateRemovalRuleDTO> selectListBySql(InviteDuplicateRemovalRuleDTO
                                                                       inviteDuplicateRemovalRuleDTO) {
        if (inviteDuplicateRemovalRuleDTO == null) {
            inviteDuplicateRemovalRuleDTO = new InviteDuplicateRemovalRuleDTO();
        }
        InviteDuplicateRemovalRulePO inviteDuplicateRemovalRulePO = inviteDuplicateRemovalRuleDTO.transDtoToPo
                (InviteDuplicateRemovalRulePO.class);
        List<InviteDuplicateRemovalRulePO> list = inviteDuplicateRemovalRuleMapper.selectListBySql
                (inviteDuplicateRemovalRulePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteDuplicateRemovalRuleDTO.class)).collect(Collectors
                    .toList());
        }
    }

    @Cacheable( value = "InviteDuplicateRemovalRuleServiceImpl_selectListBySqlNew", key = "'InviteDuplicateRemovalRuleServiceImpl_selectListBySqlNew'")
    @Override
    public List<InviteDuplicateRemovalRuleDTO> selectListBySqlNew(InviteDuplicateRemovalRuleDTO t) {
        InviteDuplicateRemovalRulePO inviteDuplicateRemovalRulePO = t.transDtoToPo
                (InviteDuplicateRemovalRulePO.class);
        List<InviteDuplicateRemovalRulePO> list = inviteDuplicateRemovalRuleMapper.selectListBySql
                (inviteDuplicateRemovalRulePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteDuplicateRemovalRuleDTO.class)).collect(Collectors
                    .toList());
        }
    }


    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.inviteRule.InviteDuplicateRemovalRuleDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public InviteDuplicateRemovalRuleDTO getById(Long id) {
        InviteDuplicateRemovalRulePO inviteDuplicateRemovalRulePO = inviteDuplicateRemovalRuleMapper.selectById(id);
        if (inviteDuplicateRemovalRulePO != null) {
            return inviteDuplicateRemovalRulePO.transPoToDto(InviteDuplicateRemovalRuleDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param inviteDuplicateRemovalRuleDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(InviteDuplicateRemovalRuleDTO inviteDuplicateRemovalRuleDTO) {
        //对对象进行赋值操作
        InviteDuplicateRemovalRulePO inviteDuplicateRemovalRulePO = inviteDuplicateRemovalRuleDTO.transDtoToPo
                (InviteDuplicateRemovalRulePO.class);
        //执行插入
        int row = inviteDuplicateRemovalRuleMapper.insert(inviteDuplicateRemovalRulePO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                            主键ID
     * @param inviteDuplicateRemovalRuleDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, InviteDuplicateRemovalRuleDTO inviteDuplicateRemovalRuleDTO) {
        InviteDuplicateRemovalRulePO inviteDuplicateRemovalRulePO = inviteDuplicateRemovalRuleMapper.selectById(id);
        //对对象进行赋值操作
        inviteDuplicateRemovalRuleDTO.transDtoToPo(inviteDuplicateRemovalRulePO);
        //执行更新
        int row = inviteDuplicateRemovalRuleMapper.updateById(inviteDuplicateRemovalRulePO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = inviteDuplicateRemovalRuleMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = inviteDuplicateRemovalRuleMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }
}
