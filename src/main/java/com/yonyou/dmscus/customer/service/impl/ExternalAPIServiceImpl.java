package com.yonyou.dmscus.customer.service.impl;

import cn.hutool.core.codec.Base64Encoder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.dmscus.customer.dto.OnlineOfflineDTO;
import com.yonyou.dmscus.customer.dto.OnlineOfflineResultDTO;
import com.yonyou.dmscus.customer.dto.ResponseCdpDTO;
import com.yonyou.dmscus.customer.dto.UserInfoOutDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.RecordsDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.RetResultDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.WarningDTO;
import com.yonyou.dmscus.customer.service.ExternalAPIService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * description
 * <AUTHOR>
 * @date 2023/9/15 13:44
 */
@Slf4j
@Service
public class ExternalAPIServiceImpl implements ExternalAPIService {
    private static final String RECORDS = "records";
    @Value("${fault.cdp.baseurl:https://uat-cdp-ma.volvocars.com.cn}")
    private String baseUrl;
    @Value("${fault.cdp.serviceUrl:/failure-light-api/}")
    private String serviceUrl;
    @Value("${fault.cdp.pathUrl:dl/vehicle/online_offline}")
    private String pathUrl;
    @Value("${fault.cdp.ak:zokCgU8Usq50pwxNUja7KX9w}")
    private String ak;
    @Value("${fault.cdp.sk:34O0UGEqQfc4xqcUtseJmKjyCwVmMA}")
    private String as;
    @Override
    public List<OnlineOfflineResultDTO> onlineStatusQuery(OnlineOfflineDTO onlineOfflineDTO) {
        log.info("onlineStatusQuery,onlineOfflineDTO:{}", onlineOfflineDTO);
        String url = baseUrl + serviceUrl + pathUrl;
        log.info("onlineStatusQuery,url:{}", url);
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set(httpHeaders.AUTHORIZATION, "Basic " + Base64Encoder.encode(ak + ":" + as));
        HttpEntity<OnlineOfflineDTO> httpEntity = new HttpEntity<>(onlineOfflineDTO, httpHeaders);
        ResponseEntity<ResponseCdpDTO> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, ResponseCdpDTO.class);
        log.info("onlineStatusQuery,pro删除,responseEntity:{}", JSON.toJSONString(responseEntity));
        List<OnlineOfflineResultDTO> list = Optional.ofNullable(responseEntity)
                .map(ResponseEntity::getBody)
                .filter(dto -> dto != null && ResponseCdpDTO.SUCCESS.equals(dto.getRetCode()))
                .map(dto -> {
                    JSONObject retResult = dto.getRetResult();
                    if (retResult != null) {
                        JSONArray records = retResult.getJSONArray(RECORDS);
                        return JSON.parseArray(records.toJSONString(), OnlineOfflineResultDTO.class);
                    } else {
                        return null;
                    }
                })
                .orElse(null);
        log.info("onlineStatusQuery,end");
        return list;
    }
}
