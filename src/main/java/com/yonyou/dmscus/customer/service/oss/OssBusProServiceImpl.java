package com.yonyou.dmscus.customer.service.oss;

import cn.hutool.core.collection.CollUtil;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusRecordPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataRecordPo;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.feign.WhitelistQueryService;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: OssServiceImpl
 * @projectName dmscus.customer
 * @description:
 * @date 2022/11/116:56
 */
@Slf4j
@Service
public class OssBusProServiceImpl implements OssBusProService{
    @Autowired
    OssWorkService ossWorkService;
    @Autowired
    WhitelistQueryService whitelistQueryService;
    @Autowired
    ReportCommonClient reportCommonClient;
    @Autowired
    OssService ossService;


    @Override
    public void vocdatainit(String dateTime) {
        long curTime1 = System.nanoTime();
        log.info("VOC环保灯线索下发定时任务开始获取数据时间开始1:{}", dateTime);
        //获取 激活信息当天修改时间
        if (StringUtils.isEmpty(dateTime)) {
            dateTime = DateUtils.dateToString(new Date(), DateUtils.YYYYMMDD);
            log.info("VOC环保灯线索下发定时任务开始获取数据时间2:{}", dateTime);
        }
        // 分页查询 减轻单次查询压力， 2000一次 。
        //新增白名单功能、
        Map<String ,String> map  = this.getbmd();
        //分页查询
        long startTimeMillis = System.currentTimeMillis();
        Integer returnCount1 = 0;
        Integer partitionSize1 = 200;
        Integer start1 = 0;
        log.info("VOC保养灯线索下发,createDate:{},startTimeMillis:{}", dateTime, startTimeMillis);
        List<VocWarningDataRecordPo> list1 = null;
        while (true) {
            Integer begIndex = start1 * partitionSize1;
            log.info("VOC保养灯线索下发,createDate:{},begIndex:{},partitionSize:{}", dateTime, begIndex, partitionSize1);
            list1 = ossWorkService.selectVocWarningDataRecordPoByModfiyTime(dateTime, 0, partitionSize1);
            if (list1 == null || list1.isEmpty()) {
                log.info("---VOC保养灯线索下发,执行完成---");
                break;
            } else {
                start1++;
                log.info("---VOC保养灯线索下发,开始执行---,start:{},size:{}", start1, list1.size());
                int i = ossService.issuedToinit(list1, dateTime,map);
                returnCount1 += i;
                log.info("VOC保养灯线索下发,returnCount:{}", returnCount1);
            }
        }
        long endTimeMillis = System.currentTimeMillis();
        long execTime = endTimeMillis - startTimeMillis;
        log.info("VOC保养灯线索下发总分页数,returnCount:{},endTimeMillis:{},execTime:{}", returnCount1, endTimeMillis, execTime);


        log.info("VOC环保灯线索下发定时任务开始获取数据时间结束耗时:{}", System.nanoTime() - curTime1);

    }

    @Override
    public void run(String dateTime) {
        ossService.downLoadVocFunctionalStatus(dateTime);
        ossService.downLoadVocwarningdaily(dateTime);
        ossService.vocFunctionalStatusDataClean(dateTime);
        ossService.vocwarningdailyDataClean(dateTime);
        //获取 激活信息当天修改时间
        if (StringUtils.isEmpty(dateTime)) {
            dateTime = DateUtils.dateToString(new Date(), DateUtils.YYYYMMDD);
            log.info("dateTime:{}", dateTime);
        }
        Map<String ,String> map  = this.getbmd();
        //分页查询
        Integer returnCount = 0;
        Integer partitionSize = 200;
        Integer start = 0;
        long startTimeMillis = System.currentTimeMillis();
        log.info("VOC保养灯线索下发,createDate:{},startTimeMillis:{}", dateTime, startTimeMillis);
        List<VocWarningDataRecordPo> list1 = null;
        while (true) {
            Integer begIndex = start * partitionSize;
            log.info("VOC保养灯线索下发,createDate:{},begIndex:{},partitionSize:{}", dateTime, begIndex, partitionSize);
            list1 = ossWorkService.selectVocWarningDataRecordPoByModfiyTime(dateTime, 0, partitionSize);
            if (list1 == null || list1.isEmpty()) {
                log.info("---VOC保养灯线索下发,执行完成---");
                break;
            } else {
                start++;
                log.info("---VOC保养灯线索下发,开始执行---,start:{},size:{}", start, list1.size());
                int i = ossService.issuedTo(list1, dateTime,map);
                returnCount += i;
                log.info("VOC保养灯线索下发,returnCount:{}", returnCount);
            }
        }
        Integer returnCount1 = 0;
        Integer partitionSize1 = 200;
        Integer start1 = 0;
        List<VocFunctionalStatusRecordPO> list = null;
        while (true) {
            Integer begIndex1 = start1 * partitionSize1;
            log.info("VOC1保养灯线索下发,createDate:{},begIndex:{},partitionSize:{}", dateTime, begIndex1, partitionSize1);
            list = ossWorkService.selectVocFunctionalStatusRecordPOByModfiyTime(dateTime, 0, partitionSize1);
            if (list == null || list.isEmpty()) {
                log.info("---VOC1保养灯线索下发,执行完成---");
                break;
            } else {
                start1++;
                log.info("---VOC1保养灯线索下发,开始执行---,start1:{},size1:{}", start1, list.size());
                int i = ossService.issuedTox(list,map,dateTime);
                returnCount1 += i;
                log.info("VOC1保养灯线索下发,returnCount:{}", returnCount1);
            }
        }

        long endTimeMillis = System.currentTimeMillis();
        long execTime = endTimeMillis - startTimeMillis;
        log.info("VOC保养灯线索下发总分页数,returnCount:{},endTimeMillis:{},execTime:{}", returnCount, endTimeMillis, execTime);
    }

    private Map<String, String> getbmd() {
        List<String>  list =   whitelistQueryService.selectWhiteListString(CommonConstants.MODTYPE_91111002,0);
        if(CollUtil.isNotEmpty(list)){

            List<String>  vins =  reportCommonClient.selectVinByCode(list);
            if(CollUtil.isNotEmpty(vins)){
                return  vins.stream().collect(Collectors.toMap(item ->item,item->item,(item1, item2) -> item1));
            }
        }
        return  new HashMap<>(1);
    }
}
