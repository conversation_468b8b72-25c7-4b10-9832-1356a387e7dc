package com.yonyou.dmscus.customer.service.goodwill;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFileInfoDTO;

/**
 * <p>
 * 亲善预申请上传附件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
public interface GoodwillApplyFileInfoService {
	public IPage<GoodwillApplyFileInfoDTO> selectPageBysql(Page page,
			GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO);

	public List<GoodwillApplyFileInfoDTO> selectListBySql(GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO);

	public GoodwillApplyFileInfoDTO getById(Long id);

	public int insert(GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO);

	public int update(Long id, GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO);

	public void deleteById(Long id) throws ServiceBizException;

	public int deleteBatchIds(String ids);

	/**
	 * 集合查询
	 * 
	 * @param GoodwillApplyFileInfoDTO
	 * @return
	 */
	List<GoodwillApplyFileInfoDTO> selectListBySql1(GoodwillApplyFileInfoDTO goodwillApplyFileInfoDto);

	/**
	 * 查询材料上传
	 * 
	 * @param complaintAttachmentPo
	 * @return
	 */
	List<GoodwillApplyFileInfoDTO> selectMaterialUploadList(GoodwillApplyFileInfoDTO goodwillApplyFileInfoDto);

}
