package com.yonyou.dmscus.customer.service.talkskill;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillPO;

import java.util.List;

/**
 * <p>
 * 话术 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-19
 */
public interface TalkskillService {

    IPage<TalkskillDTO> selectPageBysql(Page<TalkskillPO> page, TalkskillDTO talkskillDTO);
    TalkskillDTO getById(Long id);
    int insert(TalkskillDTO talkskillDTO);
    int update(Long id, TalkskillDTO talkskillDTO);
    int updateSwitch(TalkskillDTO talkskillDTO);
    List<TalkskillDTO> queryTalkskill(String dealerCode,String type,String name);

    List<TalkskillDTO> queryTalkskill(String dealerCode, String setName);

    List<TalkskillDTO> getQwimTalkskills(String dealerCode, List<String> tags);
}
