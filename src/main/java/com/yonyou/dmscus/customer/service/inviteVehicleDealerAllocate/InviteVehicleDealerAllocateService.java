package com.yonyou.dmscus.customer.service.inviteVehicleDealerAllocate;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yonyou.dmscus.customer.common.AjaxResponse;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.*;
import com.yonyou.dmscus.customer.entity.dto.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateDTO;
import com.yonyou.dmscus.customer.service.common.IBaseService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * <p>
 * 车店分配表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface InviteVehicleDealerAllocateService extends IBaseService<InviteVehicleDealerAllocateDTO> {

    List<CompanyDetailDTO> queryDealer(String dealerCode);

    int allocation(InviteVehicleDealerAllocateDTO dto);

    IPage<OwnerVehicleVO> getVehicle(String license, String vin, String name, String dealerName, String dealerCode,
                                     Long currentPage,
                                     Long pageSize);

    AjaxResponse importUpload(MultipartFile importFile);
}
