package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.voc.VocWarningDataLogMapper;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataLogPo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @title: VocWarningDataLogSreviceImpl
 * @projectName dmscus.customer
 * @date 2022/11/118:47
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class VocWarningDataLogServiceImpl implements VocWarningDataLogService {
    Logger logger= LoggerFactory.getLogger(this.getClass());

    @Autowired
    VocWarningDataLogMapper  vocWarningDataLogMapper;

    @Override
    public int insertList(List<VocWarningDataLogPo> logPOSList) {
        addList(logPOSList);
        return logPOSList.size();
    }

    @Override
    public List<VocWarningDataLogPo> selectListBydt(String data, int begIndex, Integer endIndex) {
       LambdaQueryWrapper<VocWarningDataLogPo> qw =  new LambdaQueryWrapper<>();
       qw.eq(VocWarningDataLogPo::getDt,data);
        qw.eq(VocWarningDataLogPo::getIsDeleted,0);
        qw.eq(VocWarningDataLogPo::getStatusName,"ServiceWarningStatus");
        qw.orderByDesc(VocWarningDataLogPo::getId);
        qw.last(" limit "+begIndex+","+endIndex);
        return vocWarningDataLogMapper.selectList(qw);
    }

    @Override
    public int selectCount(String vin) {
        LambdaQueryWrapper<VocWarningDataLogPo> qw =  new LambdaQueryWrapper<>();
        qw.eq(VocWarningDataLogPo::getVin,vin);
        qw.eq(VocWarningDataLogPo::getIsDeleted,0);
        return vocWarningDataLogMapper.selectCount(qw);
    }

    @Override
    public int selectWarningdailyLogByVin(String vin) {
        LambdaQueryWrapper<VocWarningDataLogPo> qw = new LambdaQueryWrapper<>();
        qw.eq(VocWarningDataLogPo::getIsDeleted ,"0");
        qw.eq(VocWarningDataLogPo::getVin,vin);
        qw.orderByDesc(VocWarningDataLogPo::getId);
        qw.last("limit 2");
        List<VocWarningDataLogPo>    list = vocWarningDataLogMapper.selectList(qw);
        if(CollUtil.isNotEmpty(list)&&  list.size()>1){
            VocWarningDataLogPo pox =  list.get(1);
            if(  pox.getStatusValue().equals(CommonConstants.VOC_NORMAL ) ) {
                return 1 ;
            }else{
                return  0 ;
            }
        }
        return 0;
    }

    /**
     *
     *  @param list
     */
    private void addList(List<VocWarningDataLogPo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int numPerTimesx = 200;
        if (list.size() <= numPerTimesx) {
            batchLogAdd(list); //此处插入少于200条list
            logger.info("voc亮灯定时任务保存数据VocWarningDataLogPo成功，数据小于200");
        } else {
            int maxIndexc = list.size();
            int maxTimesx = maxIndexc / numPerTimesx;
            maxTimesx += (maxIndexc % numPerTimesx) > 0 ? 1 : 0;
            int currentTimes = 0;
            while (currentTimes < maxTimesx) {
                int fromIndex = numPerTimesx * currentTimes;
                int toIndex = fromIndex + numPerTimesx;
                toIndex = toIndex > maxIndexc ? maxIndexc : toIndex;
                List<VocWarningDataLogPo> subList = list.subList(fromIndex, toIndex);
                batchLogAdd(subList);//此处循环插入200条list
                logger.info("voc亮灯定时任务保存数据VocWarningDataLogPo成功，数据大于200{},{}",fromIndex,toIndex);
                currentTimes++;
            }
        }

    }

    /**
     *批量新增
     *  @param list
     */
    private void batchLogAdd(List<VocWarningDataLogPo> list) {
         vocWarningDataLogMapper.insertList(list);
    }
}
