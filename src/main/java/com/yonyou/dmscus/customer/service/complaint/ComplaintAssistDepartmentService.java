package com.yonyou.dmscus.customer.service.complaint;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAssistDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAssistDepartmentDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistDepartmentPO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistPO;

import java.util.List;

/**
 * <p>
 * 协助部门 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface ComplaintAssistDepartmentService  {
    /**
     * 分页查询
     * @param page
     * @param complaintAssistDepartmentDTO
     * @return
     */
    IPage<ComplaintAssistDepartmentDTO> selectPageBysql(Page page, ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO);

    /**
     * 集合查询
     * @param complaintAssistDepartmentDTO
     * @return
     */
    List<ComplaintAssistDepartmentDTO> selectListBySql(ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO);

    /**
     * 通过id查询
     * @param id
     * @return
     */
    ComplaintAssistDepartmentDTO getById(Long id);

    /**
     * 新增
     * @param complaintAssistDepartmentDTO
     * @return
     */
    int insert(ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO);

    /**
     * 更新
     * @param id
     * @param complaintAssistDepartmentDTO
     * @return
     */
    int update(Long id, ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO);

    /**
     * 通过id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 查询Assist
     * @param page
     * @param complaintAssistDTO
     * @return
     */
    IPage<ComplaintAssistDTO> selectAssist(Page<ComplaintAssistPO> page, ComplaintAssistDTO complaintAssistDTO);

    /**
     * 通过id查询Assist
     * @param complaintAssistDTO
     * @return
     */
    List<ComplaintAssistDTO> selectAssistByid(ComplaintAssistDTO complaintAssistDTO);

    /**
     * 更新
     * @param id
     */
    void updatestatus(long id);
    /**
     * 更新
     * @param id
     */
    void updatestatus1(long id);

    /**
     * 查询数据
     * @param complaintAssistDTO
     * @return
     */
    List<ComplaintAssistDTO> selectAssistValidByid(ComplaintAssistDTO complaintAssistDTO);

    /**
     * 查询是否被分配过
     * @param complaintAssistDTO
     * @return
     */
    List<ComplaintAssistDepartmentDTO> selectAssistList(ComplaintAssistDTO complaintAssistDTO);

    void setAssistDepartmentNotEidt(long id);

    void pushMessage(ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO);
}
