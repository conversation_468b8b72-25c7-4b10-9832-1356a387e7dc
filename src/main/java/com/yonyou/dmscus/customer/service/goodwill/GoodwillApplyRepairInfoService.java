package com.yonyou.dmscus.customer.service.goodwill;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairInfoDTO;


                                                                        /**
 * <p>
 * 亲善预约申请维修记录子表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
public interface GoodwillApplyRepairInfoService  {
	public IPage<GoodwillApplyRepairInfoDTO>selectPageBysql(Page page,GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO);
	public List<GoodwillApplyRepairInfoDTO>selectListBySql(GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO);
	public GoodwillApplyRepairInfoDTO getById(Long id);
	public int insert(GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO);
	public int update(Long id, GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO);
	public int deleteById(Long id);
	public int deleteBatchIds(String ids);
	public List<GoodwillApplyRepairInfoDTO> getSupportApplyRepairInfoById(Long goodwillApplyId);
}
