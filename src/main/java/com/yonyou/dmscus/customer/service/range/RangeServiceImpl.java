package com.yonyou.dmscus.customer.service.range;

import cn.hutool.core.collection.CollectionUtil;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.dto.RangeDto;
import com.yonyou.dmscus.customer.dto.UserOrgInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesPO;
import com.yonyou.dmscus.customer.entity.vo.EmpByRoleCodeVO;
import com.yonyou.dmscus.customer.entity.vo.OrgVo;
import com.yonyou.dmscus.customer.feign.MidEndAuthCenterClient;
import com.yonyou.dmscus.customer.service.middleground.BasicdataCenterService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RangeServiceImpl implements RangeService {

    private final MidEndAuthCenterClient authClient;
    private final BasicdataCenterService basicdataCenterService;

    public RangeServiceImpl(MidEndAuthCenterClient authClient, BasicdataCenterService basicdataCenterService) {
        this.authClient = authClient;
        this.basicdataCenterService = basicdataCenterService;
    }

    @Value("${accidentClue.app.menuId:}")
    private Integer menuId;

    @Override
    public Integer getRangeControlFromMid(Integer menuId) {
        if (menuId == null) {
            return 0;
        }
        ResponseDTO<RangeDto> permissionRangeByMenu = authClient.findPermissionRangeByMenu(String.valueOf(menuId));
        RangeDto data = permissionRangeByMenu.getData();
        if (data == null) {
            return 0;
        }
        Integer rangeCode = data.getRangeCode();
        return rangeCode == null ? 0 : rangeCode;
    }



    @Override
    public boolean hasRangeAccidentClue(Integer rangeId, AccidentCluesPO po, LoginInfoDto infoDto) {
        if (rangeId == null || po == null || infoDto == null
                ||infoDto.getOrgId()==null||infoDto.getOwnerCode()==null
                ||po.getFollowPeople()==null) {
            log.error("获取权限信息出错:{} {} {}",rangeId,po,infoDto);
            return false;
        }
        Integer followPeople = po.getFollowPeople();
        String ownerCode = po.getOwnerCode();
        Long orgId = infoDto.getOrgId();
        switch (rangeId) {
            //是本人
            case 10371001:
                if (followPeople == null) {
                    return true;
                }
                return StringUtils.isEquals(String.valueOf(followPeople), String.valueOf(infoDto.getUserId()));

            //本组织 orgId相同
            case 10371002:
                UserOrgInfoDTO userOrgInfo = basicdataCenterService.getUserOrgInfo(String.valueOf(followPeople));
                return orgId.equals(userOrgInfo.getOrgid());

            //本组织及下属 分配人所在组织orgId 以及下属orgId可继续操作
            case 10371003:
                UserOrgInfoDTO userOrgInfoDTO = basicdataCenterService.getUserOrgInfo(String.valueOf(followPeople));
                Long dtoOrgid = userOrgInfoDTO.getOrgid();
                if(dtoOrgid ==null){
                    log.error("本组织及下属 userOrgInfoDTO:{}",userOrgInfoDTO);
                    return false;
                }
                List<Long> childOrgIdByOrgId = basicdataCenterService.getChildOrgIdByOrgId(String.valueOf(dtoOrgid));
                return childOrgIdByOrgId.contains(orgId);

                //所有
            case 10371004:
                return StringUtils.isEquals(infoDto.getOwnerCode(), ownerCode);
            default:
                return true;
        }
    }

    @Override
    public boolean hasRangeClueAllot(Integer rangeId, List<Integer> followPeoples, LoginInfoDto loginInfo) {
        if(rangeId==null||followPeoples==null||loginInfo==null){
            log.error("hasRangeClueAllot:{} {} {}",rangeId,followPeoples,loginInfo);
            return false;
        }
        Long orgId = loginInfo.getOrgId();
        Long userId = loginInfo.getUserId();


        switch (rangeId) {
            //是本人
            case 10371001:
                Optional<Integer> first = followPeoples.stream().filter(Objects::nonNull).filter(e -> !e.equals(userId.intValue())).findFirst();
                if(first.isPresent()){
                    throw new ServiceBizException("所选跟进人只能是本人");
                }
                return true;

            //本组织 orgId相同
            case 10371002:
                //查询本组织有哪些人
                List<UserOrgInfoDTO> companyAllUser = basicdataCenterService.getCompanyAllUser(orgId);
                Set<Long> followPeople = companyAllUser.stream().map(UserOrgInfoDTO::getUserId).collect(Collectors.toSet());
                for (Integer people : followPeoples) {
                    if(people!=null&&!followPeople.contains((long)people)){
                        throw new ServiceBizException("所选人员非本组织人员!");
                    }
                }
            return true;
            //本组织及下属 分配人所在组织orgId 以及下属orgId可继续操作
            case 10371003:
                //选中的跟进人员是哪些组织/部门的 orgId
                List<OrgVo> followPeopleInfoList = basicdataCenterService.getOrgIdBy(followPeoples);
                //查询旗下有哪些组织 orgId
                List<Long> childOrgIdByOrgId = basicdataCenterService.getChildOrgIdByOrgId(String.valueOf(orgId));
                //加上本组织的orgId
                Set<Long> allOrgList=new HashSet<>(childOrgIdByOrgId.size());
                allOrgList.add(orgId);
                allOrgList.addAll(childOrgIdByOrgId);
                for (OrgVo orgVo : followPeopleInfoList) {
                    Long voOrgId = orgVo.getOrgId();
                    if(!allOrgList.contains(voOrgId)){
                        log.error("非本组织及下属人员:{}",orgVo);
                        throw new ServiceBizException(String.format("员工%s不在组织以及下属组织中",orgVo.getEmployeeName()));
                    }
                }
                return true;
                //所有
            case 10371004:
                return true;
            default:
                return  true;
        }

    }

    @Override
    public List<EmpByRoleCodeVO> hasRangeClueAllotPeople(List<EmpByRoleCodeVO> responseDealerUserData, LoginInfoDto loginInfo) {
        if(CollectionUtil.isEmpty(responseDealerUserData)||loginInfo==null||loginInfo.getOrgId()==null){
            log.error("hasRangeClueAllotPeople:{} {}",responseDealerUserData,loginInfo);
            return responseDealerUserData;
        }
        List<EmpByRoleCodeVO> result=new ArrayList<>();
        List<Integer> list = responseDealerUserData.stream().map(EmpByRoleCodeVO::getUserId).map(Long::intValue).distinct().collect(Collectors.toList());
        //1  查询菜单配置情况
        Integer rangeId = this.getRangeControlFromMid(menuId);
        //2  查看当前用户所属组织
        Long orgId = loginInfo.getOrgId();
        //3  查询待分配人员的组织情况
        List<OrgVo> orgVos = basicdataCenterService.getOrgIdBy(list);
        Map<Long, Long> orgMap = orgVos.stream().collect(Collectors.toMap(OrgVo::getUserId, OrgVo::getOrgId, (k1, k2) -> k1));
        //4  根据权限配置 筛选出 返回的 当前经销商符合职位的员工
        switch (rangeId) {
            //是本人
            case 10371001:
                for (EmpByRoleCodeVO responseDealerUserDatum : responseDealerUserData) {
                    Long userId = responseDealerUserDatum.getUserId();
                    if(userId==null){
                        continue;
                    }
                    if(userId.equals(loginInfo.getUserId())){
                        result.add(responseDealerUserDatum);
                    }
                }
                responseDealerUserData=result;break;
            //本组织 orgId相同
            case 10371002:
                for (EmpByRoleCodeVO responseDealerUserDatum : responseDealerUserData) {
                    Long userId = responseDealerUserDatum.getUserId();
                    if(userId==null||!orgMap.containsKey(userId)){
                        continue;
                    }
                    Long aLong = orgMap.get(userId);
                    if(orgId.equals(aLong)){
                        result.add(responseDealerUserDatum);
                    }
                }
                responseDealerUserData=result;break;
            //本组织及下属 分配人所在组织orgId 以及下属orgId可继续操作
            case 10371003:
                List<Long> childOrgIdByOrgId = basicdataCenterService.getChildOrgIdByOrgId(String.valueOf(orgId));
                List<Long> allOrgList=new ArrayList<>();
                allOrgList.add(orgId);
                allOrgList.addAll(childOrgIdByOrgId);
                for (EmpByRoleCodeVO responseDealerUserDatum : responseDealerUserData) {
                    Long userId = responseDealerUserDatum.getUserId();
                    if(userId==null||!orgMap.containsKey(userId)){
                        continue;
                    }
                    Long aLong = orgMap.get(userId);
                    if(allOrgList.contains(aLong)){
                        result.add(responseDealerUserDatum);
                    }
                }
                responseDealerUserData=result;break;
            //所有
            case 10371004:return responseDealerUserData;
        }
        return responseDealerUserData;
    }

    @Override
    public List<Long> hasRangeAccidentClueList(Integer rangeId, LoginInfoDto loginInfo) {
        if(rangeId==null||loginInfo==null||loginInfo.getUserId()==null
                ||loginInfo.getOrgId()==null||loginInfo.getOwnerCode()==null){
            return Collections.emptyList();
        }
        log.info("登录信息:{}--{}",loginInfo,rangeId);
        String ownerCode = loginInfo.getOwnerCode();
        Long userId = loginInfo.getUserId();
        Long orgId = loginInfo.getOrgId();
        List<Long> result=new ArrayList<>();
        switch (rangeId){
            case 10371001: result.add(userId);break;

            case 10371002:
                List<UserOrgInfoDTO> companyAllUser = basicdataCenterService.getCompanyAllUser(orgId);
                List<Long> allUserList = companyAllUser.stream().map(UserOrgInfoDTO::getUserId).distinct().collect(Collectors.toList());
                result.addAll(allUserList);
                result.add(userId);break;

            case 10371003:
                List<Long> childOrgIdList = basicdataCenterService.getChildOrgIdByOrgId(String.valueOf(orgId));
                List<UserOrgInfoDTO> userOrgInfoDTOS = basicdataCenterService.empDealerInfo(ownerCode);
                for (UserOrgInfoDTO userOrgInfoDTO : userOrgInfoDTOS) {
                    if(childOrgIdList.contains(userOrgInfoDTO.getOrgId())||orgId.equals(userOrgInfoDTO.getOrgId())){
                        result.add(userOrgInfoDTO.getUserId());
                    }
                }break;
            case 10371004: result=null;break;
        }
        log.info("hasRangeAccidentClueList:{}",result);
        return result;
    }
}
