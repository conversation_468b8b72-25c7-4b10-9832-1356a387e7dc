package com.yonyou.dmscus.customer.service.goodwill;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditInfoDTO;

/**
 * <p>
 * 亲善审批记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
public interface GoodwillAuditInfoService {
	public IPage<GoodwillAuditInfoDTO> selectPageBysql(Page page, GoodwillAuditInfoDTO goodwillAuditInfoDTO);

	public List<GoodwillAuditInfoDTO> selectListBySql(GoodwillAuditInfoDTO goodwillAuditInfoDTO);

	public GoodwillAuditInfoDTO getById(Long id);

	public int insert(GoodwillAuditInfoDTO goodwillAuditInfoDTO);

	public int update(Long id, GoodwillAuditInfoDTO goodwillAuditInfoDTO);

	public int deleteById(Long id);

	public int deleteBatchIds(String ids);

	public List<Map> queryApplyAuditHistoryInfo(Integer auditObject, Long id);

	public List<Map> queryReturnList(GoodwillAuditInfoDTO goodwillAuditInfoDto);

}
