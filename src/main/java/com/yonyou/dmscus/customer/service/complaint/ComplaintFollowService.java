package com.yonyou.dmscus.customer.service.complaint;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.CcmFollowInfoDto;

import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;

import java.text.ParseException;
import java.util.List;

/**
 * <p>
 * 客户投诉跟进表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface ComplaintFollowService {
    /**
     * 分页查询
     * @param page
     * @param complaintFollowDTO
     * @return
     */
    IPage<ComplaintFollowDTO> selectPageBysql(Page page, ComplaintFollowDTO complaintFollowDTO);

    /**
     * 集合查询
     * @param complaintFollowDTO
     * @return
     */
    List<ComplaintFollowDTO> selectListBySql(String flag, ComplaintFollowDTO complaintFollowDTO);

    /**
     * 通过id查询
     * @param id
     * @return
     */
    ComplaintFollowDTO getById(Long id);

    /**
     * 新增
     * @param complaintFollowDTO
     * @return
     */
    int insert(ComplaintFollowDTO complaintFollowDTO);

    /**
     * 更新
     * @param id
     * @param complaintFollowDTO
     * @return
     */
    int update(Long id, ComplaintFollowDTO complaintFollowDTO);

    /**
     * 通过id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 新增跟进任容
     * @param complaintCustomFieldTestDTO
     * @return
     * @throws ParseException
     */
    int insertcCus(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO) throws ParseException;

    /**
     * 上报车厂
     * @param complaintCustomFieldTestDTO
     * @return
     * @throws ParseException
     */
    int reportVeh(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO) throws ParseException;

    /**
     * CCM新增跟进内容
     * @param ccmFollowInfoDto
     * @return
     * @throws ParseException
     */
    int insertCcmCus(CcmFollowInfoDto ccmFollowInfoDto) throws ParseException;


    /**
     * 协助部门
     * @param complaintCustomFieldTestDTO
     * @return
     */
   int insertAssistCus(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO);

    /**
     * 新增
     * @param ccmFollowInfoDto
     * @return
     */
    int insertQuCus(CcmFollowInfoDto ccmFollowInfoDto);

    /**
     * 查询最新的案件
     * @param complaintFollowDTO
     * @return
     */
    List<ComplaintFollowDTO> queryNewCus(ComplaintFollowDTO complaintFollowDTO);

    /**
     * 区域经理跟进内容
     * @param complaintCustomFieldTestDTO
     * @return
     */
    int insertRegionCus(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO)throws ParseException ;

    /**
     * 协助经销商
     * @param complaintCustomFieldTestDTO
     * @return
     */
    int insertAssistDealer(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO);

    List<ComplaintInfMoreDTO> queryNextFollowing();
}
