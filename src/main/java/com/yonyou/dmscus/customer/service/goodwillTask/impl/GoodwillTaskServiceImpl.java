package com.yonyou.dmscus.customer.service.goodwillTask.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwilApplyAuditProcessMapper;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyInfoMapper;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyMailHistoryMapper;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillAuditInfoMapper;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillAuditProcessMapper;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillDealerMailInfoMapper;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillMailTemplateMaintainMapper;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillNoticeInvoiceInfoMapper;
import com.yonyou.dmscus.customer.dto.EmailInfoDto;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyMailHistoryDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwilApplyAuditProcessPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillDealerMailInfoPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMailTemplateMaintainPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillNoticeInvoiceInfoPO;
import com.yonyou.dmscus.customer.middleInterface.QueryRoleUserDTO;
import com.yonyou.dmscus.customer.middleInterface.RequestDTO;
import com.yonyou.dmscus.customer.middleInterface.UserInfoOutDTO;
import com.yonyou.dmscus.customer.service.CommonService;
import com.yonyou.dmscus.customer.service.goodwillTask.GoodwillTaskService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

@Service
public class GoodwillTaskServiceImpl implements GoodwillTaskService {
	/**
	 * 日志对象
	 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	/** 接口调用成功的返回状态 */
	public static final String SUCCESS_CODE = "0";
	@Resource
	GoodwillApplyInfoMapper goodwillApplyInfoMapper;
	@Resource
	GoodwillAuditProcessMapper goodwillAuditProcessMapper;
	@Resource
	GoodwillAuditInfoMapper goodwillAuditInfoMapper;
	@Resource
	GoodwillDealerMailInfoMapper goodwillDealerMailInfoMapper;
	@Resource
	GoodwillNoticeInvoiceInfoMapper goodwillNoticeInvoiceInfoMapper;
	@Resource
	GoodwillApplyMailHistoryMapper goodwillApplyMailHistoryMapper;
	@Resource
	GoodwillMailTemplateMaintainMapper goodwillMailTemplateMaintainMapper;
	@Resource
	GoodwilApplyAuditProcessMapper goodwilApplyAuditProcessMapper;
	@Autowired
	CommonService commonService;
	@Resource
	private RestTemplate directRestTemplate;
	@Autowired
	private MidUrlProperties midUrlProperties;

	@Override
	public int goodwillToRefuse() {
		// 查询自动拒单期限
		Map map = goodwillAuditProcessMapper.queryRefuseTime();

		if (map != null) {
			if (!StringUtils.isNullOrEmpty(map.get("month"))) {
				// 根据拒单期限查询亲善单
				List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper
						.queryRefuse(Integer.valueOf(map.get("month").toString()));
				if (!CommonUtils.isNullOrEmpty(list)) {
					for (GoodwillApplyInfoPO goodwillApplyInfoPo : list) {
						// 根绝审批流程表数据往审批记录表插入数据
						GoodwillAuditInfoDTO goodwillAuditInfoDto = new GoodwillAuditInfoDTO();
						if (goodwillApplyInfoPo.getGoodwillStatus() < CommonConstants.GOODWILL_STATUS_MATERIAL_UPLOAD) {
							goodwillAuditInfoDto.setAuditObject(0);
						} else if (goodwillApplyInfoPo
								.getGoodwillStatus() >= CommonConstants.GOODWILL_STATUS_MATERIAL_UPLOAD) {
							goodwillAuditInfoDto.setAuditObject(1);
						}

						goodwillAuditInfoDto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
						goodwillAuditInfoDto.setAuditRole("");
						goodwillAuditInfoDto.setAuditOpinion("材料提交超时,系统自动拒绝支持");
						goodwillAuditInfoDto.setAuditResult(82801004);
						goodwillAuditInfoDto.setAuditTime(new Date());
						// goodwillAuditInfoPo.setAuditName("999999");
						// goodwillAuditInfoPo.setAuditor(999999);

						// 修改亲善单状态
						// 对对象进行赋值操作
						goodwillApplyInfoPo.setLastGoodwillStatus(goodwillApplyInfoPo.getGoodwillStatus());
						goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_REFUSE_SUPPORT);
						// goodwillApplyInfoPo.setUpdatedPerson(loginInfoDto.getUserName());
						// 执行更新
						// add by xhy 20200821
						// 商务亲善不予支持通知：自动拒单，发送超时通知邮件给到经销商和CCMQ。（此拒单功能，开放至VCDC人员）
						Integer mailType = 82461023;// 确认亲善邮件类型
						int sendToFlag = 1; // 1,经销商 2，角色
						// 1，发送给经销商
						Integer status = this.sendEmail(goodwillApplyInfoPo, sendToFlag, null, mailType);
						// 2，发送给CCMQ
						// sendToFlag = 2;
						//拒绝支持逻辑改为不需要发送给CCMQ
						//Integer statuss = this.sendEmail(goodwillApplyInfoPo, 2, "CCMQ", mailType);
						if (status == 82771001 /*&& statuss == 82771001*/) {
							goodwillAuditInfoMapper.insertAuditInfo(goodwillAuditInfoDto);
							goodwillApplyInfoMapper.updateStatusById(goodwillApplyInfoPo);
						}
					}
				}

			}
		}

		return 0;
	}

	/**
	 * 商务亲善预申请审批超时提醒
	 * 
	 * @return
	 */
	@Override
	public int goodwillApplyTimeOutTask() {
		Integer mailType = 82461004;
		int sendToFlag = 2; // 1,经销商 2，角色 （默认：角色）
		Integer days = this.getDays(mailType);
		if (days != null && days != 0) {

			// 商务亲善预申请审批超时提醒
			List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.queryApplyTimeOut(days);
			if (!CommonUtils.isNullOrEmpty(list)) {
				// 查询此邮件类型需发送邮件角色
				GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainMapper
						.selectTemplateMaintain(mailType);
				if (goodwillMailTemplateMaintainPo != null) {
					for (GoodwillApplyInfoPO GoodwillApplyInfoPO : list) {
						int isSend = goodwillApplyMailHistoryMapper.queryIsSendEmail(GoodwillApplyInfoPO.getId(),
								mailType);
						if (StringUtils.isNullOrEmpty(isSend) || isSend < 1) {
							// 根据亲善id查询下一级审核角色
							List<GoodwilApplyAuditProcessPO> po = goodwilApplyAuditProcessMapper
									.selectByGoodwillApplyId(GoodwillApplyInfoPO.getId());
							if (!CommonUtils.isNullOrEmpty(po)) {
								for (GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo : po) {
									if (goodwillMailTemplateMaintainPo.getSendObject()
											.contains(goodwilApplyAuditProcessPo.getAuditRole())) {

										// 发送邮件给待审批的对象
										this.sendEmail(GoodwillApplyInfoPO, sendToFlag,
												goodwilApplyAuditProcessPo.getAuditRole(), mailType);
									}

								}
							}
						}
					}
				}

			}
		}
		// TODO Auto-generated method stub
		return 0;
	}

	/**
	 * 商务亲善材料审核超时提醒（2周）
	 * 
	 * @return
	 */
	@Override
	public int goodwillMatrialAuditOneTask() {
		Integer mailType = 82461008;
		int sendToFlag = 2; // 1,经销商 2，角色 （默认：角色）
		Integer days = this.getDays(mailType);
		if (days != null && days != 0) {
			// 商务亲善材料审核超时提醒（2周）
			List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.goodwillMatrialAuditTask(days, 1);
			if (!CommonUtils.isNullOrEmpty(list)) {
				// 查询此邮件类型需发送邮件角色
				GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainMapper
						.selectTemplateMaintain(mailType);
				if (goodwillMailTemplateMaintainPo != null) {
					for (GoodwillApplyInfoPO GoodwillApplyInfoPO : list) {
						int isSend = goodwillApplyMailHistoryMapper.queryIsSendEmail(GoodwillApplyInfoPO.getId(),
								mailType);
						if (StringUtils.isNullOrEmpty(isSend) || isSend < 1) {
							// 根据亲善id查询下一级审核角色
							List<GoodwilApplyAuditProcessPO> po = goodwilApplyAuditProcessMapper
									.selectByGoodwillApplyId(GoodwillApplyInfoPO.getId());
							if (!CommonUtils.isNullOrEmpty(po)) {
								for (GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo : po) {
									try {
										if (goodwillMailTemplateMaintainPo.getSendObject().contains(goodwilApplyAuditProcessPo.getAuditRole())) {
											// 发送邮件给待审批的对象
											this.sendEmail(GoodwillApplyInfoPO, sendToFlag, goodwilApplyAuditProcessPo.getAuditRole(), mailType);
										}
									}catch (Exception e) {
										logger.info(e.getMessage());
									}
								}
							}
						}
					}
				}

			}
		}
		// TODO Auto-generated method stub
		return 0;
	}

	/**
	 * 商务亲善材料审核超时提醒（1个月）
	 * 
	 * @return
	 */
	@Override
	public int goodwillMatrialAuditTwoTask() {

		Integer mailType = 82461009;
		int sendToFlag = 2; // 1,经销商 2，角色 （默认：角色）
		Integer days = this.getDays(mailType);
		if (days != null && days != 0) {
			// 商务亲善材料审核超时提醒（1个月）
			List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.goodwillMatrialAuditTask(days, 2);
			if (!CommonUtils.isNullOrEmpty(list)) {
				// 查询此邮件类型需发送邮件角色
				GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainMapper
						.selectTemplateMaintain(mailType);
				if (goodwillMailTemplateMaintainPo != null) {
					for (GoodwillApplyInfoPO GoodwillApplyInfoPO : list) {
						int isSend = goodwillApplyMailHistoryMapper.queryIsSendEmail(GoodwillApplyInfoPO.getId(),
								mailType);
						if (StringUtils.isNullOrEmpty(isSend) || isSend < 1) {
							// 根据亲善id查询下一级审核角色
							List<GoodwilApplyAuditProcessPO> po = goodwilApplyAuditProcessMapper
									.selectByGoodwillApplyId(GoodwillApplyInfoPO.getId());
							if (!CommonUtils.isNullOrEmpty(po)) {
								for (GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo : po) {
									try {
										if (goodwillMailTemplateMaintainPo.getSendObject().contains(goodwilApplyAuditProcessPo.getAuditRole())) {
											// 发送邮件给待审批的对象
											this.sendEmail(GoodwillApplyInfoPO, sendToFlag, goodwilApplyAuditProcessPo.getAuditRole(), mailType);
										}
									}catch (Exception e) {
										logger.info(e.getMessage());
									}
								}
							}
						}
					}
				}

			}
		}
		return 0;
	}

	/**
	 * 商务亲善材料提交超时提醒（1个月）
	 * 
	 * @return
	 */
	@Override
	public int goodwillMatrialCommitOneTask() {
		Integer mailType = 82461010;
		int sendToFlag = 2; // 1,经销商 2，角色 （默认：角色）
		Integer days = this.getDays(mailType);
		if (days != null && days != 0) {
			// 商务亲善材料提交超时提醒（1个月）
			List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.goodwillMatrialCommitTask(days, 1);
			if (!CommonUtils.isNullOrEmpty(list)) {
				// 查询此邮件类型需发送邮件角色
				GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainMapper
						.selectTemplateMaintain(mailType);
				if (goodwillMailTemplateMaintainPo != null) {
					for (GoodwillApplyInfoPO GoodwillApplyInfoPO : list) {
						int isSend = goodwillApplyMailHistoryMapper.queryIsSendEmail(GoodwillApplyInfoPO.getId(),
								mailType);
						if (StringUtils.isNullOrEmpty(isSend) || isSend < 1) {
							if (goodwillMailTemplateMaintainPo.getSendObject().contains("CCMQ")) {
								// 发送邮件给待审批的对象
								this.sendEmail(GoodwillApplyInfoPO, sendToFlag, "CCMQ", mailType);
							}
							this.sendEmail(GoodwillApplyInfoPO, 1, null, mailType);
						}
					}
				}
			}
		}
		return 0;
	}

	/**
	 * 商务亲善材料提交超时提醒（2个月）
	 * 
	 * @return
	 */
	@Override
	public int goodwillMatrialCommitTwoTask() {
		Integer mailType = 82461011;
		int sendToFlag = 2; // 1,经销商 2，角色 （默认：角色）
		Integer days = this.getDays(mailType);
		if (days != null && days != 0) {
			// 商务亲善材料提交超时提醒（2个月）
			List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.goodwillMatrialCommitTask(days, 2);
			if (!CommonUtils.isNullOrEmpty(list)) {
				// 查询此邮件类型需发送邮件角色
				GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainMapper
						.selectTemplateMaintain(mailType);
				if (goodwillMailTemplateMaintainPo != null) {
					for (GoodwillApplyInfoPO GoodwillApplyInfoPO : list) {
						int isSend = goodwillApplyMailHistoryMapper.queryIsSendEmail(GoodwillApplyInfoPO.getId(),
								mailType);
						if (StringUtils.isNullOrEmpty(isSend) || isSend < 1) {
							if (goodwillMailTemplateMaintainPo.getSendObject().contains("CCMQ")) {
								// 发送邮件给待审批的对象
								this.sendEmail(GoodwillApplyInfoPO, sendToFlag, "CCMQ", mailType);
							}
							this.sendEmail(GoodwillApplyInfoPO, 1, null, mailType);
						}
					}
				}
			}
		}
		return 0;
	}

	/**
	 * 商务亲善补充材料提交超时提醒（1个月）
	 * 
	 * @return
	 */
	@Override
	public int goodwillSupplyMatrialCommitOneTask() {
		Integer mailType = 82461012;
		int sendToFlag = 2; // 1,经销商 2，角色 （默认：角色）
		Integer days = this.getDays(mailType);
		if (days != null && days != 0) {
			// 商务亲善补充材料提交超时提醒（1个月）
			List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.goodwillSupplyMatrialCommitTask(days, 1);
			if (!CommonUtils.isNullOrEmpty(list)) {
				// 查询此邮件类型需发送邮件角色
				GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainMapper
						.selectTemplateMaintain(mailType);
				if (goodwillMailTemplateMaintainPo != null) {
					for (GoodwillApplyInfoPO GoodwillApplyInfoPO : list) {
						int isSend = goodwillApplyMailHistoryMapper.queryIsSendEmail(GoodwillApplyInfoPO.getId(),
								mailType);
						if (StringUtils.isNullOrEmpty(isSend) || isSend < 1) {
							if (goodwillMailTemplateMaintainPo.getSendObject().contains("CCMQ")) {
								// 发送邮件给待审批的对象
								this.sendEmail(GoodwillApplyInfoPO, sendToFlag, "CCMQ", mailType);
							}
							this.sendEmail(GoodwillApplyInfoPO, 1, null, mailType);
						}
					}
				}
			}
		}
		return 0;
	}

	/**
	 * 商务亲善补充材料提交超时提醒（2个月）
	 * 
	 * @return
	 */
	@Override
	public int goodwillSupplyMatrialCommitTwoTask() {
		Integer mailType = 82461013;
		int sendToFlag = 2; // 1,经销商 2，角色 （默认：角色）
		Integer days = this.getDays(mailType);
		if (days != null && days != 0) {
			// 商务亲善补充材料提交超时提醒（2个月）
			List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.goodwillSupplyMatrialCommitTask(days, 2);
			if (!CommonUtils.isNullOrEmpty(list)) {
				// 查询此邮件类型需发送邮件角色
				GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainMapper
						.selectTemplateMaintain(mailType);
				if (goodwillMailTemplateMaintainPo != null) {
					for (GoodwillApplyInfoPO GoodwillApplyInfoPO : list) {
						int isSend = goodwillApplyMailHistoryMapper.queryIsSendEmail(GoodwillApplyInfoPO.getId(),
								mailType);
						if (StringUtils.isNullOrEmpty(isSend) || isSend < 1) {
							if (goodwillMailTemplateMaintainPo.getSendObject().contains("CCMQ")) {
								// 发送邮件给待审批的对象
								this.sendEmail(GoodwillApplyInfoPO, sendToFlag, "CCMQ", mailType);
							}
							this.sendEmail(GoodwillApplyInfoPO, 1, null, mailType);
						}
					}
				}
			}
		}
		// TODO Auto-generated method stub
		return 0;
	}

	/**
	 * 商务亲善材料提交过期提醒
	 * 
	 * @return
	 */
	@Override
	public int goodwillMatrialCommitTimeOutTask() {
		Integer mailType = 82461025;
		int sendToFlag = 2; // 1,经销商 2，角色 （默认：角色）
		Integer days = this.getDays(mailType);
		if (days != null && days != 0) {
			// 商务亲善材料提交过期提醒
			List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.goodwillMatrialCommitTimeOutTask(days);
			if (!CommonUtils.isNullOrEmpty(list)) {
				// 查询此邮件类型需发送邮件角色
				GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainMapper
						.selectTemplateMaintain(mailType);
				if (goodwillMailTemplateMaintainPo != null) {
					for (GoodwillApplyInfoPO GoodwillApplyInfoPO : list) {
						int isSend = goodwillApplyMailHistoryMapper.queryIsSendEmail(GoodwillApplyInfoPO.getId(),
								mailType);
						if (StringUtils.isNullOrEmpty(isSend) || isSend < 1) {
							if (GoodwillApplyInfoPO.getAuditType() == 0) {
								// 发送邮件区域经理
								this.sendEmail(GoodwillApplyInfoPO, sendToFlag, "SHQYJL", mailType);
							} else if (GoodwillApplyInfoPO.getAuditType() == 1) {
								// 发送邮件给待审批的对象
								this.sendEmail(GoodwillApplyInfoPO, sendToFlag, "CCMGJJL", mailType);
							}
							this.sendEmail(GoodwillApplyInfoPO, sendToFlag, "CCMQ", mailType);
							this.sendEmail(GoodwillApplyInfoPO, 1, null, mailType);
						}
					}
				}

			}
		}
		// TODO Auto-generated method stub
		return 0;
	}

	/**
	 * 商务亲善开票超时通知
	 * 
	 * @return
	 */
	@Override
	public int goodwillInvoiceTimeOutTask() {
		Integer mailType = 82461021;
		int sendToFlag = 2; // 1,经销商 2，角色 （默认：角色）
		Integer days = this.getDays(mailType);
		if (days != null && days != 0) {
			// 商务亲善开票超时通知
			List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.goodwillInvoiceTimeOutTask(days);
			if (!CommonUtils.isNullOrEmpty(list)) {
				// 查询此邮件类型需发送邮件角色
				GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainMapper
						.selectTemplateMaintain(mailType);
				if (goodwillMailTemplateMaintainPo != null) {
					for (GoodwillApplyInfoPO GoodwillApplyInfoPO : list) {
						int isSend = goodwillApplyMailHistoryMapper.queryIsSendEmail(GoodwillApplyInfoPO.getId(),
								mailType);
						if (StringUtils.isNullOrEmpty(isSend) || isSend < 1) {
							if (goodwillMailTemplateMaintainPo.getSendObject().contains("CCMQ")) {
								// 发送邮件给待审批的对象
								this.sendEmail(GoodwillApplyInfoPO, sendToFlag, "CCMQ", mailType);
							}
							this.sendEmail(GoodwillApplyInfoPO, 1, null, mailType);
						}
					}
				}
			}
		}
		// TODO Auto-generated method stub
		return 0;
	}

	/**
	 * 发送邮件
	 *
	 * @param goodwillApplyInfoPo
	 *            亲善预申请单
	 * @param goodwillApplyMailHistoryDTO
	 *            邮件发送记录
	 * @param goodwillAuditInfoPO
	 *            审批记录表
	 * @param mailType
	 *            亲善邮件类型
	 */
	public Integer sendEmail(GoodwillApplyInfoPO goodwillApplyInfoPo, int sendToFlag, String auditRole,
			Integer mailType) {

		// 邮件发送记录
		GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO = new GoodwillApplyMailHistoryDTO();
		goodwillApplyMailHistoryDTO.setGoodwillApplyId(goodwillApplyInfoPo.getId());
		goodwillApplyMailHistoryDTO.setDealerCode(goodwillApplyInfoPo.getDealerCode());
		goodwillApplyMailHistoryDTO.setMailType(mailType);

		// 1,查询发送内容模板
		GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainMapper
				.selectTemplateMaintain(mailType);
		if (!StringUtils.isNullOrEmpty(goodwillMailTemplateMaintainPo)) {
			// 2，准备发送邮箱
			String fromEmail = CommonConstants.VOLVO_MAIL;

			// 3，准备收件人邮箱
			String[] list = null;
			if (sendToFlag == 1) {
				// 经销商
				list = getDealerEmailList(goodwillApplyInfoPo);
			} else if (sendToFlag == 2) {
				// 根据角色查询人员
				list = getEmailListByRole(goodwillApplyInfoPo, auditRole);
			}

			if (list != null && list.length > 0) {
				// 4，组装数据调用中台发送邮件
				EmailInfoDto emailInfoDto = new EmailInfoDto();

				emailInfoDto.setFrom(fromEmail);
				emailInfoDto.setTo(list);
				// EamilTemplate EamilTemplate = new EamilTemplate();
				// 解析邮件标题、内容
				String maileContent = parseMailTitleAndContent(goodwillApplyInfoPo,
						goodwillMailTemplateMaintainPo.getMailContent(), mailType);
				String mailTitle = parseMailTitleAndContent(goodwillApplyInfoPo,
						goodwillMailTemplateMaintainPo.getMailTitle(), mailType);
				emailInfoDto.setText(maileContent);
				emailInfoDto.setSubject(mailTitle);
				try {
					String status = commonService.sendGoodwillMail(emailInfoDto);
					// if (status.equals('0')) {
					if ("0".equals(status)) {
						logger.info("发送邮件成功！");
						// noticeSendEmailRecordDTO.setSendStatus(CommonConstants.IS_SENDMAIL_SUCCESS);
						goodwillApplyMailHistoryDTO.setSendStatus(82771001);
					} else {
						goodwillApplyMailHistoryDTO.setSendStatus(82771002);
						logger.info("发送邮件失败！");
					}

				} catch (Exception e) {
					logger.info(e.getMessage());
				} finally {
					// return noticeSendEmailRecordService.insert( noticeSendEmailRecordDTO);
				}
				goodwillApplyMailHistoryDTO.setReceiverMail(StringUtils.join(Arrays.asList(list), ","));
				goodwillApplyMailHistoryDTO.setSendBy(CommonConstants.VOLVO_MAIL);
				goodwillApplyMailHistoryDTO.setTitle(emailInfoDto.getSubject());
				goodwillApplyMailHistoryDTO.setContent(emailInfoDto.getText());
				goodwillApplyMailHistoryDTO.setSendTime(new Date());
				goodwillApplyMailHistoryMapper.insertMailHistory(goodwillApplyMailHistoryDTO);
			}
		}
		return goodwillApplyMailHistoryDTO.getSendStatus();
	}

	/**
	 * 解析 邮件标题 内容
	 * 
	 * @param mailContent
	 * @return
	 */
	private String parseMailTitleAndContent(GoodwillApplyInfoPO goodwillApplyInfoPo, String mail, Integer mailType) {
		String parseResult = null;
		if (StringUtils.isNullOrEmpty(mail)) {
			return parseResult;
		}

		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		// 1.准备数据
		// 【商务亲善申请ID】
		String applyNo = goodwillApplyInfoPo.getApplyNo();
		// 【经销商代码】
		String dealerCode = goodwillApplyInfoPo.getDealerCode();
		// 【经销商名称】
		String dealerName = goodwillApplyInfoPo.getDealerName();
		// 【车架号】
		String vin = goodwillApplyInfoPo.getVin();
		// 【申请金额】
		BigDecimal applyAmount = goodwillApplyInfoPo.getApplyAmount();
		// 【申请时间】
		String applyTimeStr = StringUtils.isNullOrEmpty(goodwillApplyInfoPo.getApplyTime()) ? ""
				: format.format(goodwillApplyInfoPo.getApplyTime());
		// 【亲善券消费明细】 --- 邮件类型“17商务亲善代金券/积分开票通知”，在发送邮件的变量参数里，需要增加消费明细
		if (mailType.intValue() == 82461016) {
			// TODO 中台接口（前台传过来消费记录（包括合并开票））
			// 亲善ID VIN 经销商 消费时间 消费金额 开票 ID 开票抬头 通知开票金额

		}
		// 【开票ID】
		String invocieId = "";
		// 【开票抬头】
		String invocieTitle = "";
		// 【通知开票金额】
		if (mailType.intValue() == 82461018 || mailType.intValue() == 82461019) {
			BigDecimal invocieAmount = goodwillApplyInfoPo.getVoucherInvoiceAmount();
			mail = mail.replaceAll("【通知开票金额】",
					(!StringUtils.isNullOrEmpty(invocieAmount)) ? (invocieAmount.doubleValue() + "") : "0");

		} else {
			BigDecimal invocieAmount = goodwillApplyInfoPo.getInvoiceAmount();
			mail = mail.replaceAll("【通知开票金额】",
					(!StringUtils.isNullOrEmpty(invocieAmount)) ? (invocieAmount.doubleValue() + "") : "0");

		}
		// 查询第一次开票记录
		GoodwillNoticeInvoiceInfoPO GoodwillNoticeInvoiceInfoPo = goodwillNoticeInvoiceInfoMapper
				.queryVoucherByGoodwillApplyId(goodwillApplyInfoPo.getId());

		if (!StringUtils.isNullOrEmpty(GoodwillNoticeInvoiceInfoPo) && GoodwillNoticeInvoiceInfoPo != null) {
			mail = mail.replaceAll("【代金券充值金额】",
					(!StringUtils.isNullOrEmpty(GoodwillNoticeInvoiceInfoPo.getVoucherRechargePrice()))
							? GoodwillNoticeInvoiceInfoPo.getVoucherRechargePrice().toString()
							: "0");

		}
		// 查询最近日期开票记录
		List<GoodwillNoticeInvoiceInfoPO> goodwillNoticeInvoiceInfoPOList = goodwillNoticeInvoiceInfoMapper
				.queryLastNoticeInoviceInfo(goodwillApplyInfoPo.getId());
		if (!CommonUtils.isNullOrEmpty(goodwillNoticeInvoiceInfoPOList)) {
			GoodwillNoticeInvoiceInfoPO po = goodwillNoticeInvoiceInfoPOList.get(0);
			invocieId = po.getInvoiceId();
			invocieTitle = po.getName();

		}

		//

		// 2.解析预留字段
		if (!StringUtils.isNullOrEmpty(applyNo)) {
			mail = mail.replaceAll("【商务亲善申请ID】", applyNo);
		} else {
			mail = mail.replaceAll("【商务亲善申请ID】", "");
		}
		if (!StringUtils.isNullOrEmpty(dealerCode)) {
			mail = mail.replaceAll("【经销商代码】", dealerCode);
		} else {
			mail = mail.replaceAll("【经销商名称】", "");
		}
		if (!StringUtils.isNullOrEmpty(dealerName)) {
			mail = mail.replaceAll("【经销商名称】", dealerName);
		} else {
			mail = mail.replaceAll("【经销商名称】", "");
		}
		if (!StringUtils.isNullOrEmpty(vin)) {
			mail = mail.replaceAll("【车架号】", vin);
		} else {
			mail = mail.replaceAll("【车架号】", "");
		}
		if (!StringUtils.isNullOrEmpty(applyAmount)) {
			mail = mail.replaceAll("【申请金额】", applyAmount + "");
		} else {
			mail = mail.replaceAll("【申请金额】", "");
		}
		if (!StringUtils.isNullOrEmpty(applyTimeStr)) {
			mail = mail.replaceAll("【申请时间】", applyTimeStr);
		} else {
			mail = mail.replaceAll("【申请时间】", "");
		}
		if (!StringUtils.isNullOrEmpty(invocieId)) {
			mail = mail.replaceAll("【开票ID】", invocieId);
		} else {
			mail = mail.replaceAll("【开票ID】", "");
		}
		if (!StringUtils.isNullOrEmpty(invocieTitle)) {
			mail = mail.replaceAll("【开票抬头】", invocieTitle);
		} else {
			mail = mail.replaceAll("【开票抬头】", "");
		}

		// mail = mail.replaceAll("【亲善券消费明细】",applyNo);

		parseResult = mail;
		return parseResult;
	}

	/**
	 * 查询角色对应的人员 邮箱
	 *
	 * @param goodwillApplyInfoPo
	 * @param auditRole
	 * @return
	 */
	private String[] getEmailListByRole(GoodwillApplyInfoPO goodwillApplyInfoPo, String auditRole) {
		String[] list = null;
		// 1，获取经销商 code
		String dealerCode = goodwillApplyInfoPo.getDealerCode();
		// 2,根据经销商code 查询 经销商详情的【售后大区ID】 【售后小区ID】
		if ("SHQYJL".equals(auditRole)) {// 售后区域经理
			Map map = getDealerInfo(dealerCode);
			if (!StringUtils.isNullOrEmpty(map.get("afterSmallAreaId"))) { // afterBigAreaId
				// 准备查询参数
				RequestDTO<QueryRoleUserDTO> requestDto = new RequestDTO<QueryRoleUserDTO>();
				QueryRoleUserDTO queryRoleUserDto = new QueryRoleUserDTO();
				queryRoleUserDto.setOrgId(Integer.getInteger(map.get("afterSmallAreaId").toString()));
				List<String> roleList = new ArrayList<String>();
				roleList.add(auditRole);
				queryRoleUserDto.setRoleCode(roleList);
				queryRoleUserDto.setOrgId(Integer.parseInt(map.get("afterSmallAreaId").toString()));
				requestDto.setData(queryRoleUserDto);
				logger.info("根据orgId及角色查询用户={}", JSON.toJSONString(requestDto));
				list = queryEmailFromAuthCenter(requestDto);
			} else {
				throw new DALException("请维护售后小区");
			}
		} else if ("SHDQJL".equals(auditRole) || "SHQYZJ".equals(auditRole)) { // 售后区域高级经理/区域总监
			// 查询 售后大区orgId
			Map map = getDealerInfo(dealerCode);
			if (!StringUtils.isNullOrEmpty(map.get("afterBigAreaId"))) { // afterBigAreaId
				// 准备查询参数
				RequestDTO<QueryRoleUserDTO> requestDto = new RequestDTO<QueryRoleUserDTO>();
				QueryRoleUserDTO queryRoleUserDto = new QueryRoleUserDTO();
				queryRoleUserDto.setOrgId(Integer.getInteger(map.get("afterBigAreaId").toString()));
				List<String> roleList = new ArrayList<String>();
				roleList.add(auditRole);
				queryRoleUserDto.setRoleCode(roleList);
				queryRoleUserDto.setOrgId(Integer.parseInt(map.get("afterBigAreaId").toString()));
				requestDto.setData(queryRoleUserDto);
				logger.info("根据orgId及角色查询用户={}", JSON.toJSONString(requestDto));
				list = queryEmailFromAuthCenter(requestDto);
			} else {
				throw new DALException("请维护售后大区");
			}
		} else { // VP /CEO /CCMGJJL/CCMZJ/OEM-CWJL/CFO /CCMZJ /CCMQ
			// 准备查询参数
			RequestDTO<QueryRoleUserDTO> requestDto = new RequestDTO<QueryRoleUserDTO>();
			QueryRoleUserDTO queryRoleUserDto = new QueryRoleUserDTO();
			List<String> roleList = new ArrayList<String>();
			roleList.add(auditRole);
			queryRoleUserDto.setRoleCode(roleList);
			requestDto.setData(queryRoleUserDto);
			logger.info("根据角色查询用户={}", JSON.toJSONString(requestDto));
			list = queryEmailFromAuthCenter(requestDto);
		}
		return list;
	}

	/**
	 * 根据 orgID,roleCode 查询 区域经理/售后区域高级经理/区域总监/ CCM高级经理/CCM总监/CCMQ/财务经理/VP/CFO/CEO
	 *
	 * @param requestDto
	 * @return
	 */
	private String[] queryEmailFromAuthCenter(RequestDTO<QueryRoleUserDTO> requestDto) {
		List<String> eMailList = new ArrayList<String>();
		String successCodes = "0";
		String requestUrls = midUrlProperties.getMidEndAuthCenter() + midUrlProperties.getRoleOrgIdUser();
		HttpHeaders httpHeaderss = new HttpHeaders();
		httpHeaderss.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<RequestDTO> httpEntitys = new HttpEntity<RequestDTO>(requestDto, httpHeaderss);
		String defaultEmail = CommonConstants.VOLVO_MAIL; // 当前登录人没有维护email时，给默认邮箱
		logger.info("查询角色地址={}", requestUrls);
		logger.info("查询角色邮箱={}", JSON.toJSONString(requestDto));
		try {
			ResponseEntity<ResponseDTO> responseEntitys = directRestTemplate.exchange(requestUrls, HttpMethod.POST,
					httpEntitys, ResponseDTO.class);
			if (responseEntitys.getBody() != null) {
				if (successCodes.equals(responseEntitys.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					// 忽略差异字段
					objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
					List<UserInfoOutDTO> userInfoOutDtoList = objectMapper.convertValue(
							responseEntitys.getBody().getData(), new TypeReference<List<UserInfoOutDTO>>() {
							});
					if (!CommonUtils.isNullOrEmpty(userInfoOutDtoList)) {
						eMailList = userInfoOutDtoList.stream().filter(item -> {
							return (!StringUtils.isNullOrEmpty(item.getEmail()));
						}).collect(Collectors.toList()) // 过滤邮箱为空的用户数据
								.stream().map(UserInfoOutDTO::getEmail).collect(Collectors.toList());// 再取出邮箱 组合成list
						if (!CommonUtils.isNullOrEmpty(eMailList)) {
							// 不为空
							return eMailList.toArray(new String[eMailList.size()]);
						} else {
							// TODO 这里为了测试默认用自己邮箱，后面注释掉，抛出异常提醒客户维护邮箱
							eMailList.add(defaultEmail);
							return eMailList.toArray(new String[eMailList.size()]);
							// throw new DALException("请维护用户【" + userInfoOutDtoList.get(0).getUsername() +
							// "】的邮箱");
						}

					} else {
						logger.error("员工信息查询接口：{}", requestDto.getData().getRoleCode().get(0));
						throw new DALException("没有查到响应的角色【" + requestDto.getData().getRoleCode().get(0) + "】的用户");
					}

				} else {
					logger.error("员工信息查询接口：{}", responseEntitys.getBody().getReturnMessage());
					throw new DALException("员工信息查询接口异常，请稍后再试");
				}
			}
		} catch (Exception e) {
			logger.error("员工信息查询接口：{}", e.getMessage());
			throw new DALException("员工信息查询接口异常，请稍后再试");
		}
		return null;
	}

	/**
	 * 获取经销商的售后大区ID，售后小区ID，区域总监 的 email
	 * 
	 * @param dealerCode
	 * @return
	 */
	private Map getDealerInfo(String dealerCode) {
		String successCodes = "0";
		String requestUrls = midUrlProperties.getMidEndOrgCenter() + midUrlProperties.getSelectCompanyByCompanyCode() + dealerCode;
		HttpHeaders httpHeaderss = new HttpHeaders();
		httpHeaderss.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Map> httpEntitys = new HttpEntity<>(httpHeaderss);
		Map map = new HashMap<>();
		logger.info("经销商信息接口={}", requestUrls);
		try {
			ResponseEntity<ResponseDTO> responseEntitys = directRestTemplate.exchange(requestUrls, HttpMethod.POST,
					httpEntitys, ResponseDTO.class);
			if (responseEntitys.getBody() != null) {
				if (successCodes.equals(responseEntitys.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					map = objectMapper.convertValue(responseEntitys.getBody().getData(), Map.class);
					return map;

				} else {
					logger.error("经销商信息接口：{}", responseEntitys.getBody().getReturnMessage());
					throw new DALException("经销商信息接口异常，请稍后再试");
				}
			}
		} catch (Exception e) {
			logger.error("经销商信息接口：{}", e.getMessage());
			throw new DALException("经销商信息接口异常，请稍后再试");
		}
		return map;
	}

	/**
	 * 查询经销商 邮箱
	 * 
	 * @param goodwillApplyInfoPo
	 * @return
	 */
	private String[] getDealerEmailList(GoodwillApplyInfoPO goodwillApplyInfoPo) {
		// 查询经销商邮箱信息
		String dealerCode = goodwillApplyInfoPo.getDealerCode();
		GoodwillDealerMailInfoPO goodwillDealerMailInfoPo = goodwillDealerMailInfoMapper.selectByDealerCode(dealerCode);
		String[] listMail = null;
		String[] list = null;
		String[] list1 = null;
		if (goodwillDealerMailInfoPo != null) {
			if (!StringUtils.isNullOrEmpty(goodwillDealerMailInfoPo.geteMail1())) {
				if (goodwillDealerMailInfoPo.geteMail1().indexOf(",") > 0) {
					list = goodwillDealerMailInfoPo.geteMail1().split(",");
				} else {
					list = new String[1];
					list[0] = goodwillDealerMailInfoPo.geteMail1();
				}
			}
			if (!StringUtils.isNullOrEmpty(goodwillDealerMailInfoPo.geteMail2())) {
				if (goodwillDealerMailInfoPo.geteMail2().indexOf(",") > 0) {
					list1 = goodwillDealerMailInfoPo.geteMail2().split(",");
				} else {
					list1 = new String[1];
					list1[0] = goodwillDealerMailInfoPo.geteMail2();
				}
			}

			listMail = (String[]) ArrayUtils.addAll(list, list1);
		}
		return listMail;
	}

	public Integer getDays(Integer mailType) {
		GoodwillMailTemplateMaintainPO po = goodwillMailTemplateMaintainMapper.selectTemplateMaintain(mailType);

		return po.getDays();

	}

}
