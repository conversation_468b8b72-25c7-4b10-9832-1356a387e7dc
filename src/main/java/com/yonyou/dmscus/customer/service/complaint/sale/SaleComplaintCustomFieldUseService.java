package com.yonyou.dmscus.customer.service.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintCustomFieldUseDTO;

import java.util.List;


/**
 * <p>
 * 客户投诉自定义字段使用表 每个人不同对应不同 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
public interface SaleComplaintCustomFieldUseService {
    IPage<SaleComplaintCustomFieldUseDTO> selectPageBysql(Page page, SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO);
    List<SaleComplaintCustomFieldUseDTO> selectListBySql(SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO);
    SaleComplaintCustomFieldUseDTO getById(Long id);
    int insert(SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO);
    int update(Long id, SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO);
    int deleteById(Long id);
    int deleteBatchIds(String ids);

    int insertFied(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO);

    int insertSort(List<SaleComplaintCustomFieldUseDTO> sortList);

    /**
     * 重置
     * @return
     */
    int  resetFied();
}
