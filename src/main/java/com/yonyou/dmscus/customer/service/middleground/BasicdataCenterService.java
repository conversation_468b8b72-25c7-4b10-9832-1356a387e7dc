package com.yonyou.dmscus.customer.service.middleground;

import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.dto.UserOrgInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.ModelVO;
import com.yonyou.dmscus.customer.entity.dto.middleground.QueryRoleUserByCompanyCodeDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.UserInfoOutDTO;
import com.yonyou.dmscus.customer.entity.vo.OrgVo;
import com.yonyou.dmscus.customer.feign.dto.AllModeDto;

import java.util.List;
import java.util.Map;

public interface BasicdataCenterService {

    List<ModelVO> queryAllModel(Map<String,Object> queryParams) throws ServiceBizException;

    List<UserInfoOutDTO> queryRoleUserByCompanyCode(QueryRoleUserByCompanyCodeDTO dto) throws ServiceBizException;

    /*
     * 根据经销商代码查询经销商信息
     * */
    OrgVo getDealerInfo(List<String> ownerCodeList);

    /*
     * 查询该部门/组织 下面的组织id
     * */
    List<Long> getChildOrgIdByOrgId(String orgId);


    /*
     * 查询userId对应的用户 组织信息
     * */
    UserOrgInfoDTO getUserOrgInfo(String userId);

    /*
     * 查询某个组织下所有人
     * */
    List<UserOrgInfoDTO> getCompanyAllUser(Long orgId);

    /*
    * 根据userIdList查询 批量查询orgId
    * */
    List<OrgVo> getOrgIdBy(List<Integer> userIdList);

    /*
    * 根据经销商代码查询所有员工
    * */
    List<UserOrgInfoDTO> empDealerInfo(String ownerCode);

    /**
     * 获取车型名称
     */
    List<AllModeDto> selectAllModeList();

    <T> Object queryCommon(T baseVO, String host, String api) throws ServiceBizException;


}
