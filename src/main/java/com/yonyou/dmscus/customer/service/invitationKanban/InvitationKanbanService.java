package com.yonyou.dmscus.customer.service.invitationKanban;


import com.yonyou.dmscus.customer.entity.dto.invitationKanban.InvitationKanbanInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationKanban.InvitationKanbanQueryDTO;

import java.util.List;


/**
 * <p>
 * 车辆邀约记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
public interface InvitationKanbanService {

    List<InvitationKanbanInfoDTO> InvitationKanbanService(InvitationKanbanQueryDTO query);

    List<InvitationKanbanInfoDTO> InvitationKanbanDlrService(InvitationKanbanQueryDTO query);
}
