package com.yonyou.dmscus.customer.service.inviteInsurance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscloud.function.utils.common.StringUtils;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleRecordDetailMapper;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordDetailPO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordDetailPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 车辆邀约续保记录明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@Service
public class InviteInsuranceVehicleRecordDetailServiceImpl implements InviteInsuranceVehicleRecordDetailService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteInsuranceVehicleRecordDetailMapper inviteInsuranceVehicleRecordDetailMapper;

    /**
     * 分页查询对应数据
     *
     * @param page                                  分页对象
     * @param inviteInsuranceVehicleRecordDetailDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.repair.entity.dto.tools.InviteInsuranceVehicleRecordDetailDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    /*@Override
    public IPage<InviteInsuranceVehicleRecordDetailDTO> selectPageBysql(Page page, InviteInsuranceVehicleRecordDetailDTO inviteInsuranceVehicleRecordDetailDTO) {
        if (inviteInsuranceVehicleRecordDetailDTO == null) {
            inviteInsuranceVehicleRecordDetailDTO = new InviteInsuranceVehicleRecordDetailDTO();
        }
        InviteInsuranceVehicleRecordDetailPO inviteInsuranceVehicleRecordDetailPO = inviteInsuranceVehicleRecordDetailDTO.transDtoToPo(InviteInsuranceVehicleRecordDetailPO.class);

        List<InviteInsuranceVehicleRecordDetailPO> list = inviteInsuranceVehicleRecordDetailMapper.selectPageBySql(page, inviteInsuranceVehicleRecordDetailPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteInsuranceVehicleRecordDetailDTO> result = list.stream().map(m -> m.transPoToDto(InviteInsuranceVehicleRecordDetailDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }*/
    /**
     * 添加邀约记录明细
     * @param inviteInsuranceVehicleRecordDetailDTO
     * @return
     */
    @Override
    public Long addInviteInsuranceVehicleRecordDetail(InviteInsuranceVehicleRecordDetailDTO inviteInsuranceVehicleRecordDetailDTO) {
        //对对象进行赋值操作
        InviteInsuranceVehicleRecordDetailPO inviteInsuranceVehicleRecordDetailPO = inviteInsuranceVehicleRecordDetailDTO.transDtoToPo
                (InviteInsuranceVehicleRecordDetailPO.class);
        //执行插入
        inviteInsuranceVehicleRecordDetailMapper.insert(inviteInsuranceVehicleRecordDetailPO);
        return inviteInsuranceVehicleRecordDetailPO.getId();
    }

    /**
     * 店端查询跟进记录
     * @param vin
     * @param id
     * @return
     */
    @Override
    public List<InviteInsuranceVehicleRecordDetailDTO> getInviteInsuranceVehicleRecordInfoDlr(String vin, Long id) {
        logger.info("getInviteInsuranceVehicleRecordInfoDlr, vin:{}, id:{}", vin, id);
        //获取登录用户信息
        //LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        List<InviteInsuranceVehicleRecordDetailPO> list = inviteInsuranceVehicleRecordDetailMapper.getInviteInsuranceVehicleRecordInfoDlr(id);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<InviteInsuranceVehicleRecordDetailDTO>();
        } else {
            List<InviteInsuranceVehicleRecordDetailDTO> result = list.stream().map(m -> m.transPoToDto
                    (InviteInsuranceVehicleRecordDetailDTO
                            .class)
            ).collect(Collectors.toList());
            return result;
        }
    }

    /**
     * 店端查询跟进记录明细历史
     * @param vin
     * @param dealerCode
     * @return
     */
    @Override
    public IPage<InviteInsuranceVehicleRecordDetailDTO> selectFollowInviteInsureDetailHistory(Page page, String vin, String dealerCode) {
        if(!StringUtils.isNullOrEmpty(vin) && !StringUtils.isNullOrEmpty(dealerCode)){
            List<InviteInsuranceVehicleRecordDetailPO> list = inviteInsuranceVehicleRecordDetailMapper.selectFollowInviteInsureDetailHistory(page, vin, dealerCode);
            if (CommonUtils.isNullOrEmpty(list)) {
                page.setRecords(new ArrayList<>());
                return page;
            } else {
                List<InviteInsuranceVehicleRecordDetailDTO> result = list.stream().map(m -> m.transPoToDto(InviteInsuranceVehicleRecordDetailDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }else{
            return null;
        }
    }

}
