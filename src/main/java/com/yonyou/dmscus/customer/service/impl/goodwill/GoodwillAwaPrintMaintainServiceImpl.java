package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillAwaPrintMaintainMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAwaPrintMaintainDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAwaPrintMaintainPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillAwaPrintMaintainService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 亲善AWA打印模板维护 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-23
 */
@Service
public class GoodwillAwaPrintMaintainServiceImpl
		extends ServiceImpl<GoodwillAwaPrintMaintainMapper, GoodwillAwaPrintMaintainPO>
		implements GoodwillAwaPrintMaintainService {
	/** 日志对象 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillAwaPrintMaintainMapper goodwillAwaPrintMaintainMapper;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillAwaPrintMaintainDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.GoodwillAwaPrintMaintainDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillAwaPrintMaintainDTO> selectPageBysql(Page page,
			GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO) {
		if (goodwillAwaPrintMaintainDTO == null) {
			goodwillAwaPrintMaintainDTO = new GoodwillAwaPrintMaintainDTO();
		}
		GoodwillAwaPrintMaintainPO goodwillAwaPrintMaintainPo = goodwillAwaPrintMaintainDTO
				.transDtoToPo(GoodwillAwaPrintMaintainPO.class);

		List<GoodwillAwaPrintMaintainPO> list = goodwillAwaPrintMaintainMapper.selectPageBySql(page,
				goodwillAwaPrintMaintainPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillAwaPrintMaintainDTO> result = list.stream()
					.map(m -> m.transPoToDto(GoodwillAwaPrintMaintainDTO.class)).collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillAwaPrintMaintainDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.GoodwillAwaPrintMaintainDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillAwaPrintMaintainDTO> selectListBySql(GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO) {
		if (goodwillAwaPrintMaintainDTO == null) {
			goodwillAwaPrintMaintainDTO = new GoodwillAwaPrintMaintainDTO();
		}
		GoodwillAwaPrintMaintainPO goodwillAwaPrintMaintainPo = goodwillAwaPrintMaintainDTO
				.transDtoToPo(GoodwillAwaPrintMaintainPO.class);
		List<GoodwillAwaPrintMaintainPO> list = goodwillAwaPrintMaintainMapper
				.selectListBySql(goodwillAwaPrintMaintainPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillAwaPrintMaintainDTO.class))
					.collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.GoodwillAwaPrintMaintainDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillAwaPrintMaintainDTO getById(Long id) {
		GoodwillAwaPrintMaintainPO goodwillAwaPrintMaintainPo = goodwillAwaPrintMaintainMapper.selectById(id);
		if (goodwillAwaPrintMaintainPo != null) {
			return goodwillAwaPrintMaintainPo.transPoToDto(GoodwillAwaPrintMaintainDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillAwaPrintMaintainDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO) {
		// 对对象进行赋值操作
		GoodwillAwaPrintMaintainPO goodwillAwaPrintMaintainPo = goodwillAwaPrintMaintainDTO
				.transDtoToPo(GoodwillAwaPrintMaintainPO.class);
		// 执行插入
		int row = goodwillAwaPrintMaintainMapper.insert(goodwillAwaPrintMaintainPo);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillAwaPrintMaintainDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO) {
		GoodwillAwaPrintMaintainPO goodwillAwaPrintMaintainPo = goodwillAwaPrintMaintainMapper.selectById(id);
		// 对对象进行赋值操作
		goodwillAwaPrintMaintainDTO.transDtoToPo(goodwillAwaPrintMaintainPo);
		// 执行更新
		int row = goodwillAwaPrintMaintainMapper.updateById(goodwillAwaPrintMaintainPo);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillAwaPrintMaintainMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillAwaPrintMaintainMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillAuditProcessDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.GoodwillAuditProcessDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<Map> selectAwaPrintMaintainInfo(GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO) {
		if (goodwillAwaPrintMaintainDTO == null) {
			goodwillAwaPrintMaintainDTO = new GoodwillAwaPrintMaintainDTO();
		}
		GoodwillAwaPrintMaintainPO goodwillAwaPrintMaintainPo = goodwillAwaPrintMaintainDTO
				.transDtoToPo(GoodwillAwaPrintMaintainPO.class);
		List<Map> list = goodwillAwaPrintMaintainMapper.selectAwaPrintMaintainInfo(goodwillAwaPrintMaintainPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list;
		}
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 *
	 * @param GoodwillAwaPrintMaintainDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020/4/21
	 */
	@Override
	public int addOrEditAwaPrint(GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO) {

		if (goodwillAwaPrintMaintainDTO.getId() == null) {
			// 执行新增
			// 对对象进行赋值操作
			GoodwillAwaPrintMaintainPO goodwillAwaPrintMaintainPo = goodwillAwaPrintMaintainDTO
					.transDtoToPo(GoodwillAwaPrintMaintainPO.class);

			goodwillAwaPrintMaintainMapper.insert(goodwillAwaPrintMaintainPo);
		} else {
			// 执行更新
			GoodwillAwaPrintMaintainPO goodwillAwaPrintMaintainPo = goodwillAwaPrintMaintainMapper
					.selectById(goodwillAwaPrintMaintainDTO.getId());

			// 对对象进行赋值操作
			goodwillAwaPrintMaintainDTO.transDtoToPo(goodwillAwaPrintMaintainPo);
			goodwillAwaPrintMaintainMapper.updateById(goodwillAwaPrintMaintainPo);
		}

		return 1;
	}
}
