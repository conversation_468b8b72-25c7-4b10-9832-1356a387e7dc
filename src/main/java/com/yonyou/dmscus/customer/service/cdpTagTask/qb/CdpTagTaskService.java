package com.yonyou.dmscus.customer.service.cdpTagTask.qb;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.CdpTagTaskPo;

import java.util.List;

import com.yonyou.dmscus.customer.dto.cdp.CdpTagTaskParameterDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/10 0010
 */
public interface CdpTagTaskService {

    void writesCdpTagTask(String startTime, String endTime);

    void disposeCdpTagTask(String startTime, String endTime);

    /**
     *批量插入故障灯高亮线索id
     */
    void batchInsertHighlightFlagClueId(List<Long> clueId);
    /**
     *查询故障灯线索总条数
     */
    Integer queryFaultLightClueTotal();
    /**
     *查询故障灯线索ID
     */
    List<CdpTagTaskPo> queryFaultLightClueId(Page page);
    /**
     *更新故障灯线索任务状态
     */
    void updateFaultLightClueById(List<Long> cdpTagTaskId);

    void updateErrorTask(List<CdpTagTaskPo> cdpTagTaskPos);
    List<CdpTagTaskParameterDto> getVins(List<String> vins);

}
