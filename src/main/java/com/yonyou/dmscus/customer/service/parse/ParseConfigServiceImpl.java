package com.yonyou.dmscus.customer.service.parse;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscus.customer.dao.Parse.ParseConfigMapper;
import com.yonyou.dmscus.customer.entity.dto.parse.ParseDto;
import com.yonyou.dmscus.customer.entity.po.parse.ParseConfigPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ParseConfigServiceImpl extends ServiceImpl<ParseConfigMapper, ParseConfigPo> implements ParseConfigService{


    /**
     * 根据parseId获取解析正则
      */
    @Override
    public List<ParseDto> getByParseIdList(List<Long> idList, int i) {
        LambdaQueryWrapper<ParseConfigPo> wrapper = new LambdaQueryWrapper<>();
        if(!CollectionUtils.isEmpty(idList)){
            wrapper.in(ParseConfigPo::getParseId,idList);
        }
        wrapper.eq(ParseConfigPo::getParseType,i);
        List<ParseConfigPo> list = list(wrapper);
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        return list.stream().map(e -> {
            ParseDto dto = new ParseDto();
            BeanUtil.copyProperties(e, dto, true);
            return dto;
        }).collect(Collectors.toList());
    }
}
