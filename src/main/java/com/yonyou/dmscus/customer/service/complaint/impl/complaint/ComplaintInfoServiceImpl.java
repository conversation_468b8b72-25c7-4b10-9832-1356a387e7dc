package com.yonyou.dmscus.customer.service.complaint.impl.complaint;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.dao.DAOUtil;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscloud.function.utils.common.DateUtil;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintAttachmentMapper;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintInfoExtendMapper;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintInfoMapper;
import com.yonyou.dmscus.customer.dto.EmailInfoDto;
import com.yonyou.dmscus.customer.dto.GetModelNameDTO;
import com.yonyou.dmscus.customer.entity.dto.common.HolidayDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.AttcahmentUpdateDto;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldUseDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomTopUseDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintmoreDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.SelectSmallManagerDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.SmallManagerDataDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.UserRoleDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.UserRoleDataDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sendComplaintData.CloseComplaintDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sendComplaintData.FollowInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sendComplaintData.NewComplaintDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.v51dk.V51dkComplaintInfoSyncDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintClassificationPO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfMorePO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoExtendPO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoPO;
import com.yonyou.dmscus.customer.enums.CacheKeyEnum;
import com.yonyou.dmscus.customer.feign.DmscloudServiceClient;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.feign.dto.CommonConfigDTO;
import com.yonyou.dmscus.customer.middleInterface.CompanyDetailByCodeDTO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintAttachmentService;
import com.yonyou.dmscus.customer.service.complaint.ComplaintClassificationService;
import com.yonyou.dmscus.customer.service.complaint.ComplaintCustomFieldUseService;
import com.yonyou.dmscus.customer.service.complaint.ComplaintCustomTopUseService;
import com.yonyou.dmscus.customer.service.complaint.ComplaintFollowService;
import com.yonyou.dmscus.customer.service.complaint.ComplaintInfoService;
import com.yonyou.dmscus.customer.service.impl.CommonServiceImpl;
import com.yonyou.dmscus.customer.util.common.HttpClient;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.yonyou.dmscus.customer.utils.RedisLockUtil.DistributedLockUtil;
import com.yonyou.dmscus.customer.utils.RedisLockUtil.IDistributedLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;


/**
 * <p>
 * 客户投诉信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Service
public class ComplaintInfoServiceImpl implements ComplaintInfoService {
    /**
     * 日志对象
     */
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    ComplaintInfoMapper complaintInfoMapper;
    @Autowired
    ComplaintFollowService complaintFollowService;
    @Autowired
    ComplaintCustomFieldUseService complaintCustomFieldUseService;
    @Autowired
    ComplaintCustomTopUseService complaintCustomTopUseService;
    @Autowired
    ComplaintAttachmentService complaintAttachmentService;
    @Resource
    ComplaintAttachmentMapper complaintAttachmentMapper;
    @Autowired
    ComplaintDealerCcmRefServiceImpl complaintDealerCcmRefService;
    @Autowired
    CommonServiceImpl commonServiceImpl;
    @Autowired
    ComplaintCommonServiceImpl complaintCommonServiceImpl;
    @Value("${wedo.baseUrl}")
    private String baseUrl;
    @Value("${wedo.apiuid}")
    private String apiuid;
    @Value("${wedo.apipwd}")
    private String apipwd;
    @Autowired
    ReportCommonClient reportCommonClient;
    @Resource
    ComplaintInfoExtendMapper complaintInfoExtendMapper;

    @Resource
    private MidUrlProperties midUrlProperties;

    @Resource
    private RestTemplate directRestTemplate;

    @Autowired
    private ComplaintClassificationService complaintClassificationService;

    @Autowired
    private DmscloudServiceClient dmscloudServiceClient;


    /**
     * 分页查询对应数据
     *
     * @param page             分页对象
     * @param complaintInfoDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfoDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<ComplaintInfoDTO> selectPageBysql(Page page, ComplaintInfoDTO complaintInfoDTO) {
        if (complaintInfoDTO == null) {
            complaintInfoDTO = new ComplaintInfoDTO();
        }
        ComplaintInfoPO complaintInfoPo = complaintInfoDTO.transDtoToPo(ComplaintInfoPO.class);

        List<ComplaintInfoPO> list = complaintInfoMapper.selectPageBySql(page, complaintInfoPo);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<ComplaintInfoDTO> result = list.stream().map(m -> m.transPoToDto(ComplaintInfoDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param complaintInfoDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfoDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<ComplaintInfoDTO> selectListBySql(ComplaintInfoDTO complaintInfoDTO) {
        if (complaintInfoDTO == null) {
            complaintInfoDTO = new ComplaintInfoDTO();
        }
        ComplaintInfoPO complaintInfoPo = complaintInfoDTO.transDtoToPo(ComplaintInfoPO.class);
        List<ComplaintInfoPO> list = complaintInfoMapper.selectListBySql(complaintInfoPo);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(ComplaintInfoDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfoDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public ComplaintInfoDTO getById(Long id) {
        ComplaintInfoPO complaintInfoPo = complaintInfoMapper.selectById(id);
        if (complaintInfoPo != null) {
            return complaintInfoPo.transPoToDto(ComplaintInfoDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param complaintInfoDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(ComplaintInfoDTO complaintInfoDTO) {
        //对对象进行赋值操作
        ComplaintInfoPO complaintInfoPo = complaintInfoDTO.transDtoToPo(ComplaintInfoPO.class);
        //执行插入
        int row = complaintInfoMapper.insert(complaintInfoPo);
        if (row > 0) {
            if (complaintInfoPo.getType().equals(81981001) || complaintInfoPo.getType().equals(81981004) ) {
                complaintInfoSync(complaintInfoDTO);
            }
            Long complaintInfoId=complaintInfoPo.getId();
            QueryWrapper<ComplaintInfoExtendPO> queryWrapper=new QueryWrapper<>();
            queryWrapper.eq("complaint_info_id",complaintInfoId);
            queryWrapper.eq("is_deleted",0);
            ComplaintInfoExtendPO complaintInfoExtendPO=complaintInfoExtendMapper.selectOne(queryWrapper);
            if(Objects.isNull(complaintInfoExtendPO)){
                ComplaintInfoExtendPO complaintInfoExtendPONew = new ComplaintInfoExtendPO();
                complaintInfoExtendPONew.setAfterSmallAreaName(complaintInfoDTO.getAfterSmallAreaName());
                complaintInfoExtendPONew.setComplaintInfoId(complaintInfoId);
                complaintInfoExtendPONew.setComplaintId(complaintInfoDTO.getComplaintId());
                complaintInfoExtendMapper.insert(complaintInfoExtendPONew);
            }else{
                complaintInfoExtendPO.setAfterSmallAreaName(complaintInfoDTO.getAfterSmallAreaName());
                complaintInfoExtendPO.setComplaintInfoId(complaintInfoId);
                complaintInfoExtendPO.setComplaintId(complaintInfoDTO.getComplaintId());
                complaintInfoExtendMapper.updateById(complaintInfoExtendPO);
            }

        }
        //返回插入的值
        return row;
    }

    @Override
    public String complaintInfoSync(ComplaintInfoDTO complaintInfoDTO) {
        int successCode = 0;
        String url = null;
        V51dkComplaintInfoSyncDTO syncDTO = new V51dkComplaintInfoSyncDTO();
        BeanUtils.copyProperties(complaintInfoDTO,syncDTO);
        syncDTO.setJobOrderStatus(String.valueOf(complaintInfoDTO.getWorkOrderStatus()));
        syncDTO.setNature(String.valueOf(complaintInfoDTO.getWorkOrderNature()));
        syncDTO.setCallTime(DateUtil.formatDateByFormat(complaintInfoDTO.getCallTime(),"yyyy-MM-dd HH:mm:ss"));
        if (!ObjectUtils.isEmpty(complaintInfoDTO.getBuyTime())) {
            syncDTO.setBuyTime(DateUtil.formatDateByFormat(complaintInfoDTO.getBuyTime(),"yyyy-MM-dd HH:mm:ss"));
        }

        List<Long> categoryIds = new ArrayList<>();
        categoryIds.add(Long.valueOf(complaintInfoDTO.getCategory1()));
        categoryIds.add(Long.valueOf(complaintInfoDTO.getCategory2()));
        categoryIds.add(Long.valueOf(complaintInfoDTO.getCategory3()));
        List<ComplaintClassificationPO> byIds = complaintClassificationService.getByIds(categoryIds);
        if (!CollectionUtils.isEmpty(byIds)) {
            Map<Long, String> idMap = byIds.stream().collect(Collectors.toMap(ComplaintClassificationPO::getId, ComplaintClassificationPO::getCateName));
            syncDTO.setCategory1(idMap.get(Long.valueOf(complaintInfoDTO.getCategory1())));
            syncDTO.setCategory2(idMap.get(Long.valueOf(complaintInfoDTO.getCategory2())));
            syncDTO.setCategory3(idMap.get(Long.valueOf(complaintInfoDTO.getCategory3())));
        }
        url = midUrlProperties.getMid51DKCenter()+midUrlProperties.getComplaintInfoSync();
        logger.info("推送中台客诉单信息开始，url：{}，入参：{}",url,JSON.toJSONString(syncDTO));
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<V51dkComplaintInfoSyncDTO> httpEntity = new HttpEntity<>(syncDTO, httpHeaders);
        ResponseEntity<JSONObject> responseEntity = directRestTemplate.exchange(url, HttpMethod.POST, httpEntity,
                JSONObject.class);
        if(responseEntity.getBody()!=null) {
            JSONObject body = responseEntity.getBody();
            logger.info("推送中台客诉单信息结束，返回结果：{}",body.toJSONString());
            if (!body.get("code").equals(successCode)) {
                throw new ServiceBizException(body.get("msg").toString());
            } else {
                return "成功";
            }
        } else {
            throw new ServiceBizException("调用中台推送客诉单信息失败");
        }
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id               主键ID
     * @param complaintInfoDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, ComplaintInfoDTO complaintInfoDTO) {
        ComplaintInfoPO complaintInfoPo = complaintInfoMapper.selectById(id);
        //首次结案时间不更新
        if(!StringUtils.isNullOrEmpty(complaintInfoPo.getFirstCloseCaseTime())){
            complaintInfoDTO.setFirstCloseCaseTime(null);
        }
        //首次重启时间不更新
        if(!StringUtils.isNullOrEmpty(complaintInfoPo.getFisrtRestartTime())){
            complaintInfoDTO.setFisrtRestartTime(null);
        }
        //首次重启结案时间不更新
        if(!StringUtils.isNullOrEmpty(complaintInfoPo.getFirstRestartCloseCaseTime())){
            complaintInfoDTO.setFirstRestartCloseCaseTime(null);
        }
        //对对象进行赋值操作
        complaintInfoDTO.transDtoToPo(complaintInfoPo);
        //执行更新
        int row = complaintInfoMapper.updateById(complaintInfoPo);

        //更新后获取最新的信息推送中台
        ComplaintInfoPO complaintInfoPO = complaintInfoMapper.selectById(id);
        ComplaintInfoDTO complaintInfoDTO1 = new ComplaintInfoDTO();
        BeanUtils.copyProperties(complaintInfoPO,complaintInfoDTO1);
        complaintInfoSync(complaintInfoDTO1);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = complaintInfoMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 获取投诉单号
     *
     * @param ts
     * @param dearCold
     */
    @Transactional
    @Override
    public String getBillNo(String ts, String dearCold) {
        return getBillNo1(ts, dearCold);
    }

    /**
     * 新建经销商投诉单
     *
     * @param complaintmoreDTO
     * @return
     */
    @Override
    public int insertComplaint(ComplaintmoreDTO complaintmoreDTO) {
        ComplaintInfoDTO complaintInfoDTO = new ComplaintInfoDTO();
        complaintInfoDTO.setDealerCode(FrameworkUtil.getLoginInfo().getOwnerCode());
        CompanyDetailByCodeDTO companyInfo = complaintDealerCcmRefService.getCompanyInfo(FrameworkUtil.getLoginInfo().getOwnerCode());
        complaintInfoDTO.setRegion(companyInfo.getAfterBigAreaName());
        complaintInfoDTO.setAfterSmallAreaName(companyInfo.getAfterSmallAreaName());

        SelectSmallManagerDTO selectSmallManagerDTO = new SelectSmallManagerDTO();
        selectSmallManagerDTO.setOrgId(companyInfo.getAfterSmallAreaId().intValue());
        List<String> roleCode = new ArrayList<>();
        roleCode.add("SHQYJL");
        selectSmallManagerDTO.setRoleCode(roleCode);
        List<SmallManagerDataDTO> smallManagerList = complaintDealerCcmRefService.getSmallManager(selectSmallManagerDTO);
        complaintInfoDTO.setWorkOrderNature(complaintmoreDTO.getWorkOrderNature());

        complaintInfoDTO.setWorkOrderClassification(83621001);
        if (smallManagerList.size() != 0) {
            complaintInfoDTO.setRegionManager(smallManagerList.get(0).getEmployeeName());
        }
        complaintInfoDTO.setBloc(companyInfo.getGroupCompanyName());
        complaintInfoDTO.setRegionId(companyInfo.getAfterBigAreaId());
        complaintInfoDTO.setRegionManagerId(companyInfo.getAfterSmallAreaId());
        complaintInfoDTO.setDealerName(companyInfo.getCompanyNameCn());
        complaintInfoDTO.setBlocId(Long.valueOf(companyInfo.getGroupCompanyId()));
        complaintInfoDTO.setComplaintId(complaintmoreDTO.getComplaintId());
        complaintInfoDTO.setCallName(complaintmoreDTO.getCallName());
        complaintInfoDTO.setCallTel(complaintmoreDTO.getCallTel());
        complaintInfoDTO.setSex(complaintmoreDTO.getSex());
        complaintInfoDTO.setCallTime(new Date());
        complaintInfoDTO.setSource(complaintmoreDTO.getSource());
        complaintInfoDTO.setType(complaintmoreDTO.getType());
        complaintInfoDTO.setCategory1(complaintmoreDTO.getCategory1());
        complaintInfoDTO.setCategory2(complaintmoreDTO.getCategory2());
        complaintInfoDTO.setCategory3(complaintmoreDTO.getCategory3());
        complaintInfoDTO.setReport(false);
        complaintInfoDTO.setIsCloseCase(10041002);
        complaintInfoDTO.setImportanceLevel(complaintmoreDTO.getImportanceLevel());
        if(!StringUtils.isNullOrEmpty(complaintmoreDTO.getDepartment())){
            StringBuffer department = new StringBuffer();
            List departmentList = complaintmoreDTO.getDepartment();
            for (int i = 0; i < departmentList.size(); i++) {
                if (i == departmentList.size() - 1) {
                    department.append(departmentList.get(i));
                } else {
                    department.append(departmentList.get(i) + ",");
                }
            }
            complaintInfoDTO.setDepartment(department.toString());
        }
        StringBuffer part = new StringBuffer();
        List partList = complaintmoreDTO.getPart();
        for (int i = 0; i < partList.size(); i++) {
            if (i == partList.size() - 1) {
                part.append(partList.get(i));
            } else {
                part.append(partList.get(i) + ",");
            }
        }
        complaintInfoDTO.setPart(part.toString());
        StringBuffer subdivisionPart = new StringBuffer();
        List subdivisionPartlist = complaintmoreDTO.getSubdivisionPart();
        for (int i = 0; i < subdivisionPartlist.size(); i++) {
            if (i == subdivisionPartlist.size() - 1) {
                subdivisionPart.append(subdivisionPartlist.get(i));
            } else {
                subdivisionPart.append(subdivisionPartlist.get(i) + ",");
            }
        }
        complaintInfoDTO.setSubdivisionPart(subdivisionPart.toString());
        complaintInfoDTO.setDataSources(82101001);
        //车辆信息赋值
        ComplaintInfoDTO complaintInfoDto1 = complaintmoreDTO.getFormPanelData1();
        complaintInfoDTO.setLicensePlateNum(complaintInfoDto1.getLicensePlateNum());
        complaintInfoDTO.setVin(complaintInfoDto1.getVin());
        complaintInfoDTO.setName(complaintInfoDto1.getName());
        complaintInfoDTO.setModel(complaintInfoDto1.getModel());
        complaintInfoDTO.setModelYear(complaintInfoDto1.getModelYear());
        if(!StringUtils.isNullOrEmpty(complaintInfoDto1.getBuyTime())){
            complaintInfoDTO.setBuyTime(complaintInfoDto1.getBuyTime());
        }
        complaintInfoDTO.setBuyDealerName(complaintInfoDto1.getBuyDealerName());
        complaintInfoDTO.setConfigName(complaintInfoDto1.getConfigName());
        complaintInfoDTO.setMileage(complaintInfoDto1.getMileage());
        complaintInfoDTO.setOwnerAddress(complaintmoreDTO.getOwnerCode());
        complaintInfoDTO.setBuyDealerCode(complaintInfoDto1.getBuyDealerCode());
        if (!StringUtils.isNullOrEmpty(complaintInfoDto1.getBuyDealerCode())) {
            CompanyDetailByCodeDTO companyInfoBuy = complaintDealerCcmRefService.getCompanyInfo(complaintInfoDto1.getBuyDealerCode());
            complaintInfoDTO.setBuyRegion(companyInfoBuy.getSaleBigAreaName());
            if(!StringUtils.isNullOrEmpty(companyInfoBuy.getSaleSmallAreaId())){
                roleCode.remove("SHQYJL");
                roleCode.add("XSXQJL");
                selectSmallManagerDTO = new SelectSmallManagerDTO();
                selectSmallManagerDTO.setOrgId(companyInfoBuy.getSaleSmallAreaId().intValue());
                selectSmallManagerDTO.setRoleCode(roleCode);
                smallManagerList = complaintDealerCcmRefService.getSmallManager(selectSmallManagerDTO);
                if (!CommonUtils.isNullOrEmpty(smallManagerList)) {
                    complaintInfoDTO.setBuyRegionManager(smallManagerList.get(0).getEmployeeName());
                }
            }
            complaintInfoDTO.setBuyBloc(companyInfoBuy.getGroupCompanyName());
            complaintInfoDTO.setBuyRegionId(companyInfoBuy.getSaleBigAreaId());
            complaintInfoDTO.setBuyRegionManagerId(companyInfoBuy.getSaleSmallAreaId());
            if (!StringUtils.isNullOrEmpty(complaintInfoDto1.getBuyDealerName())) {
                complaintInfoDTO.setBuyDealerName(complaintInfoDto1.getBuyDealerName());
            } else {
                complaintInfoDTO.setBuyDealerName(companyInfoBuy.getCompanyNameCn());
            }
        }
        //投诉信息赋值
        ComplaintmoreDTO complaintInfoDto2 = complaintmoreDTO.getFormPanelData2();
        complaintInfoDTO.setSubject(complaintInfoDto2.getSubject());
        complaintInfoDTO.setProblem(complaintInfoDto2.getProblem());
        complaintInfoDTO.setProblemInfo(complaintInfoDto2.getProblemInfo());
        StringBuffer cusRequirement = new StringBuffer();
        List cusRequirementList = complaintInfoDto2.getCusRequirement();
        for (int i = 0; i < cusRequirementList.size(); i++) {
            if (i == cusRequirementList.size() - 1) {
                cusRequirement.append(cusRequirementList.get(i));
            } else {
                cusRequirement.append(cusRequirementList.get(i) + ",");
            }
        }
        complaintInfoDTO.setCusRequirement(cusRequirement.toString());
        ComplaintFollowDTO complaintFollowDto1 = complaintmoreDTO.getFormPanelData3();
        if (complaintFollowDto1.getFollowContent() != null) {
            complaintInfoDTO.setDealerFisrtReplyTime(new Date());
            complaintInfoDTO.setWorkOrderStatus(82451002);
        } else {
            complaintInfoDTO.setWorkOrderStatus(82451001);
        }
        complaintInfoDTO.setCloseCaseStatus(82441004);
        complaintInfoDTO.setWorkOrderNature(complaintmoreDTO.getWorkOrderNature());
        insert(complaintInfoDTO);
        NewComplaintDTO newComplaintDTO = this.setNewComplaintDto(complaintInfoDTO);
        String Json = JSONObject.toJSONString(newComplaintDTO);
        String path = "/api/services/app/Order/DealerOrder";
        String http = baseUrl + path;
        String noncestr1 = UUID.randomUUID().toString().replace("-", "");
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String sgin = commonServiceImpl.GetCheckSign(path, apiuid, apipwd, noncestr1, timestamp);
        String describe = "下发给400新建售后客诉单";
        HttpClient.requestInterfaceJsonCus(describe, Json, http, sgin, apiuid, noncestr1, timestamp);
        //跟进信息赋值
        List<ComplaintInfoPO> queryid = complaintInfoMapper.queryid(complaintInfoDTO);
        long id = queryid.get(0).getId();
        //跟进信息赋值
        if (complaintFollowDto1.getFollowContent() != null) {
            ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
            complaintFollowDTO.setFollowContent(complaintFollowDto1.getFollowContent());
            complaintFollowDTO.setComplaintInfoId(id);
            complaintFollowDTO.setDealerNotPublish(false);
            complaintFollowDTO.setCcmNotPublish(true);
            complaintFollowDTO.setFollowTime(new Date());
            complaintFollowDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
            complaintFollowDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
            complaintFollowService.insert(complaintFollowDTO);
            IssuedUpdataData(complaintFollowDTO, FrameworkUtil.getLoginInfo().getOwnerCode(), "跟进中");
        }
        AttcahmentUpdateDto attcahmentUpdateDto = new AttcahmentUpdateDto();
        attcahmentUpdateDto.setId(id);
        attcahmentUpdateDto.setNo(complaintInfoDTO.getComplaintId());
        complaintAttachmentMapper.updateAttachment(attcahmentUpdateDto);
        return 1;
    }

    /**
     * 给NewComPlaintDTO赋值
     *
     * @param complaintInfoDTO
     * @return
     */
    public NewComplaintDTO setNewComplaintDto(ComplaintInfoDTO complaintInfoDTO) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        NewComplaintDTO newComplaintDTO = new NewComplaintDTO();
        newComplaintDTO.setComplainID(complaintInfoDTO.getComplaintId());
        //投诉类型赋值
        switch (complaintInfoDTO.getType()) {
            case 81981001:
                newComplaintDTO.setComplainType("产品质量");
                break;
            case 81981002:
                newComplaintDTO.setComplainType("销售");
                break;
            case 81981003:
                newComplaintDTO.setComplainType("服务");
                break;
            case 81981004:
                newComplaintDTO.setComplainType("重要");
                break;
        }
        newComplaintDTO.setSource(complaintCommonServiceImpl.getSource(complaintInfoDTO.getSource()));
        newComplaintDTO.setComplainDT(sf.format(complaintInfoDTO.getCallTime()));
        newComplaintDTO.setComplainSource("经销商自建");
        //赋值工单状态
        newComplaintDTO.setJobOrderStatus(0);
        newComplaintDTO.setReplyContacts(complaintInfoDTO.getCallName());
        newComplaintDTO.setReplyMobile(complaintInfoDTO.getCallTel());
        newComplaintDTO.setRelatedLicense(complaintInfoDTO.getLicensePlateNum());
        newComplaintDTO.setRelatedVin(complaintInfoDTO.getVin());
        Integer id = Integer.valueOf(complaintInfoDTO.getModel());
        GetModelNameDTO list = commonServiceImpl.getModelNameById(id);
        newComplaintDTO.setRelatedModel(list.getModelName());
        newComplaintDTO.setMileage(complaintInfoDTO.getMileage());
        newComplaintDTO.setRelatedVersion(complaintInfoDTO.getModelYear());
        newComplaintDTO.setBuyDT(sf.format(complaintInfoDTO.getBuyTime()));
        newComplaintDTO.setBuy_DlrCode(complaintInfoDTO.getBuyDealerCode());
        newComplaintDTO.setBuy_DlrName(complaintInfoDTO.getBuyDealerName());
        newComplaintDTO.setProc_DlrCode(complaintInfoDTO.getDealerCode());
        newComplaintDTO.setProc_DlrName(complaintInfoDTO.getDealerName());
        newComplaintDTO.setSubject(complaintInfoDTO.getSubject());
        newComplaintDTO.setProblemDescr(complaintInfoDTO.getProblem());
        newComplaintDTO.setCategoryProplem(complaintCommonServiceImpl.getProblemInf(complaintInfoDTO.getProblemInfo()));
        newComplaintDTO.setCategory_1(complaintCommonServiceImpl.getCategory(complaintInfoDTO.getCategory1()));
        newComplaintDTO.setCategory_2(complaintCommonServiceImpl.getCategory(complaintInfoDTO.getCategory2()));
        newComplaintDTO.setCategory_3(complaintCommonServiceImpl.getCategory(complaintInfoDTO.getCategory3()));
        newComplaintDTO.setCustomerClaims(complaintCommonServiceImpl.getCusRequirement(complaintInfoDTO.getCusRequirement()));
        newComplaintDTO.setNature("售后");
        return newComplaintDTO;
    }


    /**
     * 客诉经销商结案
     *
     * @param complaintCustomFieldTestDTO
     * @return
     */
    @Override
    public int insertClose(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO, String flag) {

        long id = complaintCustomFieldTestDTO.getComplaintInfoId();
        //防重复
        IDistributedLock lock = DistributedLockUtil.getDistributedLock(
                CacheKeyEnum.CUSTOMER_COMPLAINT_KEY.getKey(id));
        if(!lock.antiDuplication()){
            throw new DALException( "当前案件正在维护，请勿重复操作" );
        }
        int row = 0;
        try {
            ComplaintInfoDTO complaintInfoDTO = new ComplaintInfoDTO();
            complaintInfoDTO.setId(id);
            List basicReasonList = complaintCustomFieldTestDTO.getBasicReason();
            StringBuffer basicReason = new StringBuffer();
            if (basicReasonList != null) {
                for (int i = 0; i < basicReasonList.size(); i++) {
                    //当循环到最后一个的时候 就不添加逗号,
                    if (i == basicReasonList.size() - 1) {
                        basicReason.append(basicReasonList.get(i));
                    } else {
                        basicReason.append(basicReasonList.get(i) + ",");
                    }
                }
            }
            List<ComplaintInfoPO> queryComplaintData = complaintInfoMapper.queryComplaintData(complaintInfoDTO);
            int isRestart=10041002;
            //判断案子是否重启过
            if(!StringUtils.isNullOrEmpty(queryComplaintData.get(0).getFisrtRestartTime())){
                isRestart=10041001;
            }
            complaintInfoDTO.setBasicReason(basicReason.toString());
            complaintInfoDTO.setApplyTime(complaintCustomFieldTestDTO.getApplyTime());
            complaintInfoDTO.setTechMaintainPlan(complaintCustomFieldTestDTO.getTechMaintainPlan());
            if (complaintCustomFieldTestDTO.getRapportPlan() != null) {
                complaintInfoDTO.setRapportPlan(complaintCustomFieldTestDTO.getRapportPlan());
            }
            complaintInfoDTO.setId(id);
            String code = "82441001";
            if (flag.equals(code)) {
                complaintInfoDTO.setCloseCaseStatus(82441001);
                complaintInfoDTO.setIsCloseCase(10041002);
            } else {
                if(isRestart==10041002){
                    complaintInfoDTO.setFirstCloseCaseTime(new Date());
                }else if(StringUtils.isNullOrEmpty(queryComplaintData.get(0).getFirstRestartCloseCaseTime())&&isRestart==10041001) {
                    complaintInfoDTO.setRestartCloseCaseTime(new Date());
                    complaintInfoDTO.setFirstRestartCloseCaseTime(new Date());
                }else {
                    complaintInfoDTO.setRestartCloseCaseTime(new Date());
                }
                complaintInfoDTO.setCloseCaseStatus(82441005);
                complaintInfoDTO.setCloseCaseTime(complaintCustomFieldTestDTO.getApplyTime());
                complaintInfoDTO.setWorkOrderStatus(82451004);
                complaintInfoDTO.setIsCloseCase(10041001);
            }
            complaintInfoDTO.setIsSatisfied(complaintCustomFieldTestDTO.getIsSatisfied());
            complaintInfoDTO.setIsRepaired(complaintCustomFieldTestDTO.getIsRepaired());
            complaintInfoDTO.setApplyTime(new Date());
            complaintInfoDTO.setRisk(complaintCustomFieldTestDTO.getRisk());
            row=update(id, complaintInfoDTO);
            IssuedCloseData(complaintInfoDTO,basicReasonList);

        } catch (Exception e) {
            logger.info("客诉结案报错：",e);
            throw new ServiceBizException(e.getMessage());
        } finally {
            logger.info("客诉结案释放锁");
            lock.release();
        }
        return row;
    }


    /**
     * 分页查询
     *
     * @param page
     * @param complaintInfMoreDTO
     * @return
     * @throws ParseException
     */
    @Override
    public IPage<ComplaintInfMoreDTO> selectCusByDeal(Page page, ComplaintInfMoreDTO complaintInfMoreDTO, String user) throws ParseException {
        ComplaintInfMoreDTO complaintInfMoreDto = setComplaintInfMoreDTO(complaintInfMoreDTO, user);
        String model = null;
        if (complaintInfMoreDto == null) {
            complaintInfMoreDto = new ComplaintInfMoreDTO();
        }
        ComplaintInfMorePO complaintInfMorePo = complaintInfMoreDto.transDtoToPo(ComplaintInfMorePO.class);
        //获取em90配置参数
        CommonConfigDTO commonConfigDTO = dmscloudServiceClient.getCommonConfig(CommonConstants.EM90_MODEL_ID_CONFIG_KEY);
        if (Objects.nonNull(commonConfigDTO) && org.apache.commons.lang.StringUtils.isNotEmpty(commonConfigDTO.getConfigValue())){
            model = commonConfigDTO.getConfigValue();
        }
        List<ComplaintInfMorePO> list = complaintInfoMapper.selectCusByDeal(page, complaintInfMorePo,model);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<ComplaintInfMoreDTO> result = list.stream().map(m -> m.transPoToDto(ComplaintInfMoreDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 导出查询
     *
     * @param complaintInfMoreDTO
     * @return
     * @throws ParseException
     */
    @Override
    public List<Map> selectCusByDealAll(ComplaintInfMoreDTO complaintInfMoreDTO, String user) throws ParseException {
        ComplaintInfMoreDTO complaintInfMoreDto = setComplaintInfMoreDTO(complaintInfMoreDTO, user);
        if (complaintInfMoreDto == null) {
            complaintInfMoreDto = new ComplaintInfMoreDTO();
        }
        if(!StringUtils.isNullOrEmpty(user)&&user.equals("deal")){
            String dealerCode=FrameworkUtil.getLoginInfo().getOwnerCode();
            String aa = "(t.dealer_code =" + "\"" + dealerCode + "\"" + " or EXISTS(SELECT 1 FROM  ${schema.dms_manage}.tt_complaint_assist_department   t5 WHERE t.id=t5.complaint_info_id and  t5.assist_dealer_code <>t.dealer_code and t5.assist_dealer_code=" + "\"" + dealerCode + "\"" + "or t5.assist_department=" + "\"" + dealerCode + "\"" + "))";
            complaintInfMoreDto.setSql1(aa);
        }


       return reportCommonClient.exportComplaint(complaintInfMoreDto);
    }

    @Override
    public int insertRegionClose(ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO, String agree, String revisit) {
        logger.info("complaintCustomFieldTestDTO:{}",complaintCustomFieldTestDTO);

        long id = complaintCustomFieldTestDTO.getComplaintInfoId();

        //防重复
        IDistributedLock lock = DistributedLockUtil.getDistributedLock(
                CacheKeyEnum.CUSTOMER_COMPLAINT_KEY.getKey(id));
        if(!lock.antiDuplication()){
            throw new DALException( "当前案件正在维护，请勿重复操作" );
        }
        int update = 0;
        try {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            ComplaintInfoDTO complaintInfoDTO = new ComplaintInfoDTO();
            complaintInfoDTO.setId(id);
            List<ComplaintInfoPO> queryComplaintData = complaintInfoMapper.queryComplaintData(complaintInfoDTO);
            logger.info("queryComplaintData:{}",queryComplaintData);
            String complaintNo = queryComplaintData.get(0).getComplaintId();
            List basicReasonList = complaintCustomFieldTestDTO.getBasicReason();
            //当前时间
            Date nowtime = new Date();
            // 日历对象
            Calendar c = Calendar.getInstance();
            // 设置时间
            c.setTime(nowtime);
            // 设置毫秒值为0
            c.set(Calendar.MILLISECOND, 0);

            nowtime = c.getTime();
            if(!StringUtils.isNullOrEmpty(queryComplaintData.get(0).getCloseCaseTime())){
                nowtime = queryComplaintData.get(0).getCloseCaseTime();
            }
            int isRestart=10041002;
            //判断案子是否重启过
            if(!StringUtils.isNullOrEmpty(queryComplaintData.get(0).getFisrtRestartTime())){
                isRestart=10041001;
            }
            logger.info("queryComplaintData,isRestart:{}",isRestart);
            StringBuffer basicReason = new StringBuffer();
            if (basicReasonList != null) {
                for (int i = 0; i < basicReasonList.size(); i++) {
                    //当循环到最后一个的时候 就不添加逗号,
                    if (i == basicReasonList.size() - 1) {
                        basicReason.append(basicReasonList.get(i));
                    } else {
                        basicReason.append(basicReasonList.get(i) + ",");
                    }
                }
            }
            complaintInfoDTO.setIsAgree(complaintCustomFieldTestDTO.getIsAgree());
            complaintInfoDTO.setIsRevisit(complaintCustomFieldTestDTO.getIsRevisit());
            complaintInfoDTO.setIsRepaired(complaintCustomFieldTestDTO.getIsRepaired());
            complaintInfoDTO.setIsSatisfied(complaintCustomFieldTestDTO.getIsSatisfied());
            complaintInfoDTO.setBasicReason(basicReason.toString());

            complaintInfoDTO.setTechMaintainPlan(complaintCustomFieldTestDTO.getTechMaintainPlan());
            if (complaintCustomFieldTestDTO.getRapportPlan() != null) {
                complaintInfoDTO.setRapportPlan(complaintCustomFieldTestDTO.getRapportPlan());
            }
            String followConcent="";
            String isCloseText="";
            String isRevisitText="";
            String code = "82731002";
            if (agree.equals(code)) {
                complaintInfoDTO.setCloseCaseStatus(82441002);
                complaintInfoDTO.setIsCloseCase(10041002);
                isCloseText="不结案 ";
                isRevisitText="不回访 ";
            } else {
                String number = "10041001";
                complaintInfoDTO.setSubmitTime(nowtime);
                if (revisit.equals(number)) {
                    //回访
                    complaintInfoDTO.setCloseCaseStatus(82441003);
                    complaintInfoDTO.setIsCloseCase(10041002);
                    isCloseText="结案 ";
                    isRevisitText="回访 ";
                    if(isRestart==10041001){
                        complaintInfoDTO.setIsRestartRevisit(10041001);
                    }
                    IssuedCloseData(complaintInfoDTO,basicReasonList);
                } else {
                    isCloseText="结案 ";
                    isRevisitText="不回访 ";
                    if(isRestart==10041002){
                        complaintInfoDTO.setFirstCloseCaseTime(nowtime);
                    }else if(StringUtils.isNullOrEmpty(queryComplaintData.get(0).getFirstRestartCloseCaseTime())&&isRestart==10041001) {
                        complaintInfoDTO.setRestartCloseCaseTime(nowtime);
                        complaintInfoDTO.setFirstRestartCloseCaseTime(nowtime);
                    }else {
                        complaintInfoDTO.setRestartCloseCaseTime(nowtime);
                    }
                    //不回访直接结案
                    complaintInfoDTO.setCloseCaseStatus(82441005);
                    complaintInfoDTO.setWorkOrderStatus(82451004);
                    complaintInfoDTO.setCloseCaseTime(nowtime);
                    complaintInfoDTO.setIsCloseCase(10041001);
                    Date newDate=new Date();
                    Date dealerFisrtReplyTime = queryComplaintData.get(0).getDealerFisrtReplyTime();
                    Date callTime = queryComplaintData.get(0).getCallTime();
                    String closeDate=sf.format(complaintInfoDTO.getCloseCaseTime());
                    int ReplyTime=0;
                    if(!StringUtils.isNullOrEmpty(dealerFisrtReplyTime)){
                        ReplyTime = (int) (Math.abs(dealerFisrtReplyTime.getTime() - callTime.getTime()));
                    }
                    int CloseTime = (int) (Math.abs(newDate.getTime() - callTime.getTime()));
                    String ReplyFlag="否";
                    String closeFlag="否";
                    if(ReplyTime>=24){
                        ReplyFlag="是";
                    }
                    if(CloseTime>=120){
                        closeFlag="是";
                    }
                    System.out.println(ReplyTime/1000/60/60);
                    EmailInfoDto emailInfoDto = new EmailInfoDto();
                    emailInfoDto.setFrom("<EMAIL>");
                    String[] list = new String[64];
                    list = new String[1];
                    list[0] = "<EMAIL>";
                    emailInfoDto.setTo(list);
                    emailInfoDto.setSubject("客诉单结案不回访");
                    emailInfoDto.setText("投诉单号:"+complaintNo+",已结案,无需回访,是否24小时回访:"+ReplyFlag+"是否未5个工作日内结案:"+closeFlag+"结案时间为"+closeDate);
    //                commonServiceImpl.sendMail(emailInfoDto);
                    IssuedCloseData(complaintInfoDTO,basicReasonList);
                }
            }
            complaintInfoDTO.setRisk(complaintCustomFieldTestDTO.getRisk());
            if(complaintCustomFieldTestDTO.getIsSatisfied()==10041001){
                followConcent=isCloseText+" 满意 "+isRevisitText;
            }else {
                followConcent=isCloseText+" 不满意 "+isRevisitText;
            }
            if (!StringUtils.isNullOrEmpty(complaintCustomFieldTestDTO.getRegionalManagerComments())) {
                complaintInfoDTO.setRegionalManagerComments(complaintCustomFieldTestDTO.getRegionalManagerComments());
                followConcent+="//n"+complaintCustomFieldTestDTO.getRegionalManagerComments();
            }
            ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
            complaintFollowDTO.setActuallFollowTime2(nowtime);
            complaintFollowDTO.setFollowTime(nowtime);
            complaintFollowDTO.setFollowContent(followConcent);
            complaintFollowDTO.setComplaintInfoId(id);
            complaintFollowDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
            complaintFollowDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
            complaintFollowDTO.setIsCcmNotPublish(true);
            if(!StringUtils.isNullOrEmpty(complaintInfoDTO.getWorkOrderStatus())&&82451004==complaintInfoDTO.getWorkOrderStatus()){
                IssuedUpdataData(complaintFollowDTO, "厂端", "提交结案");
            }else {
                IssuedUpdataData(complaintFollowDTO, "厂端", "跟进中");
            }
            complaintFollowService.insert(complaintFollowDTO);
            update = update(id, complaintInfoDTO);
        } catch (Exception e) {
            logger.info("客诉结案报错：",e);
            throw new ServiceBizException(e.getMessage());
        } finally {
            logger.info("客诉结案释放锁");
            lock.release();
        }
        return update ;
    }

    /**
     * 通过vin号查询亲善历史
     *
     * @param
     * @return
     */
    @Override
    public IPage<GoodwillApplyInfoDTO> selectGoodWill(Page page, GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
        if (goodwillApplyInfoDTO == null) {
            goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        }

        List<GoodwillApplyInfoDTO> list = complaintInfoMapper.getByvin(page, goodwillApplyInfoDTO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<GoodwillApplyInfoDTO> result = list;

            page.setRecords(result);
            return page;
        }

    }

    /**
     * 下发给400跟进信息
     */
    @Override
    public void IssuedUpdataData(ComplaintFollowDTO complaintFollowDTO, String CreaterOrg, String JobOrderStatus) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        UserRoleDTO userRoleDTO = commonServiceImpl.getUserRoleDTO();
        FollowInfoDTO followInfoDTO = new FollowInfoDTO();
        StringBuffer follower = new StringBuffer();
        List<UserRoleDataDTO> UserRoleDataDTO = userRoleDTO.getRoleList();
        if (UserRoleDataDTO != null && UserRoleDataDTO.size() != 0) {
            for (int i = 0; i < UserRoleDataDTO.size(); i++) {
                if (i == UserRoleDataDTO.size() - 1) {
                    follower.append(UserRoleDataDTO.get(i).getRoleName());
                } else {
                    follower.append(UserRoleDataDTO.get(i).getRoleName() + ",");
                }
            }
        }
        ComplaintInfoDTO complaintInfoDTO = new ComplaintInfoDTO();
        complaintInfoDTO.setId(complaintFollowDTO.getComplaintInfoId());
        List<ComplaintInfoPO> queryComplaintData = complaintInfoMapper.queryComplaintData(complaintInfoDTO);
        String id = queryComplaintData.get(0).getComplaintId();
        String source = "";
        if (!StringUtils.isNullOrEmpty(queryComplaintData.get(0).getDataSources())) {
            if (queryComplaintData.get(0).getDataSources() == 82101001) {
                source = "经销商自建";
            } else {
                source = "400下发";
            }
        }
        followInfoDTO.setComplainID(id);
        followInfoDTO.setFollContent(complaintFollowDTO.getFollowContent());
        followInfoDTO.setJobOrderStatus(JobOrderStatus);
        followInfoDTO.setSource(source);
        followInfoDTO.setCreaterName(FrameworkUtil.getLoginInfo().getUserName());
        followInfoDTO.setFollower(follower.toString());
        followInfoDTO.setFollowDT(sf.format(complaintFollowDTO.getFollowTime()));
        followInfoDTO.setCreaterOrg(CreaterOrg);
        String Json = JSONObject.toJSONString(followInfoDTO);
        String path = "/api/services/app/Order/OrderFollow";
        String http = baseUrl + path;
        String noncestr1 = UUID.randomUUID().toString().replace("-", "");
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String sgin = commonServiceImpl.GetCheckSign(path, apiuid, apipwd, noncestr1, timestamp);
        String describe = "下发给400跟进信息(售后)";
        HttpClient.requestInterfaceJsonCus(describe, Json, http, sgin, apiuid, noncestr1, timestamp);
    }

    @Override
    public ComplaintInfMorePO selectCusDetailById(ComplaintInfMoreDTO complaintInfMoreDTO) {
        return complaintInfoMapper.selectCusDetailById(complaintInfMoreDTO);
    }

    /**
     * 获取节假日
     */
    @Override
    public void getHoilday() {
        Calendar cal = Calendar.getInstance();
        HolidayDTO holidayDTO=new HolidayDTO();
        int year = cal.get(Calendar.YEAR);
//        for(int year=2018;year<2022;year++){
        for (int i=0;i<12;i++){
            holidayDTO.setYear(year);
            holidayDTO.setMonth(i);
            holidayDTO.setHolidayDate(commonServiceImpl.JJR(year,i));
            //是否表中存在
            List<HolidayDTO> holidayDTOList=complaintInfoMapper.selectHoildayList(holidayDTO);
            if(CommonUtils.isNullOrEmpty(holidayDTOList)){
                complaintInfoMapper.insertHoliday(holidayDTO,holidayDTO.getHolidayDate());
            }
//            }
        }

    }

    /**
     * 下发给400结案信息
     */
//    @Override
    public void IssuedCloseData(ComplaintInfoDTO complaintInfoDTO,List<Integer> basicReasonList) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        UserRoleDTO userRoleDTO = commonServiceImpl.getUserRoleDTO();
        CloseComplaintDTO closeComplaintDTO = new CloseComplaintDTO();
        StringBuffer follower = new StringBuffer();
        List<UserRoleDataDTO> UserRoleDataDTO = userRoleDTO.getRoleList();
        if (UserRoleDataDTO != null && UserRoleDataDTO.size() != 0) {
            for (int i = 0; i < UserRoleDataDTO.size(); i++) {
                if (i == UserRoleDataDTO.size() - 1) {
                    follower.append(UserRoleDataDTO.get(i).getRoleName());
                } else {
                    follower.append(UserRoleDataDTO.get(i).getRoleName() + ",");
                }
            }
        }
        List<ComplaintInfoPO> queryComplaintData = complaintInfoMapper.queryComplaintData(complaintInfoDTO);

        String id = queryComplaintData.get(0).getComplaintId();
        String CreaterOrg = "";
        String source = "";
        String dealerCloseDate="";
        if(!StringUtils.isNullOrEmpty(queryComplaintData.get(0).getApplyTime())){
             dealerCloseDate = sf.format(queryComplaintData.get(0).getApplyTime());
        }
        if (!StringUtils.isNullOrEmpty(queryComplaintData.get(0).getDataSources())) {
            if (queryComplaintData.get(0).getDataSources() == 82101001) {
                source = "经销商自建";
            } else {
                source = "400下发";
            }
        }
        if("VOLVO".equals(FrameworkUtil.getLoginInfo().getOwnerCode())){
            CreaterOrg = "厂端";
        }else {
            CreaterOrg = FrameworkUtil.getLoginInfo().getOwnerCode();
        }
        closeComplaintDTO.setComplainID(id);
        closeComplaintDTO.setCreaterOrg(CreaterOrg);
        closeComplaintDTO.setSource(source);
        if (complaintInfoDTO.getIsCloseCase() == 10041001) {
            closeComplaintDTO.setCaseStatus("结案");
            if (!StringUtils.isNullOrEmpty(dealerCloseDate)) {
                closeComplaintDTO.setCaseDoneApplyDT(dealerCloseDate);
            } else if(!StringUtils.isNullOrEmpty(complaintInfoDTO.getCloseCaseTime())) {
                closeComplaintDTO.setCaseDoneApplyDT(sf.format(complaintInfoDTO.getCloseCaseTime()));
            }else {
                closeComplaintDTO.setCaseDoneApplyDT(sf.format(new Date()));
            }
            if(!StringUtils.isNullOrEmpty(complaintInfoDTO.getSubmitTime())){
                if(!StringUtils.isNullOrEmpty(complaintInfoDTO.getSubmitTime())){
                    closeComplaintDTO.setRegionApplyDT(sf.format(complaintInfoDTO.getSubmitTime()));
                }
            }
            closeComplaintDTO.setCaseDoneDT(sf.format(complaintInfoDTO.getCloseCaseTime()));
        } else {
            if (!StringUtils.isNullOrEmpty(dealerCloseDate)) {
                closeComplaintDTO.setCaseDoneApplyDT(dealerCloseDate);
            } else if(!StringUtils.isNullOrEmpty(complaintInfoDTO.getCloseCaseTime())) {
                closeComplaintDTO.setCaseDoneApplyDT(sf.format(complaintInfoDTO.getCloseCaseTime()));
            }else {
                closeComplaintDTO.setCaseDoneApplyDT(sf.format(new Date()));
            }
            if(!StringUtils.isNullOrEmpty(complaintInfoDTO.getSubmitTime())){
                closeComplaintDTO.setRegionApplyDT(sf.format(complaintInfoDTO.getSubmitTime()));
            }
            closeComplaintDTO.setCaseStatus("申请结案");
        }
        if (!StringUtils.isNullOrEmpty(complaintInfoDTO.getIsRevisit())) {
            switch (complaintInfoDTO.getIsRevisit()) {
                case 10041001:
                    closeComplaintDTO.setVisitNeed("是");
                    break;
                case 10041002:
                    closeComplaintDTO.setVisitNeed("否");
                    break;
            }
        }else {
            closeComplaintDTO.setVisitNeed("否");
        }
        StringBuffer basicReason=new StringBuffer();
        if (basicReasonList != null) {
            for (int i = 0; i < basicReasonList.size(); i++) {
                //当循环到最后一个的时候 就不添加逗号,
                if (i == basicReasonList.size() - 1) {
                    switch (basicReasonList.get(i)) {
                        case 82741001:
                            basicReason.append("产品质量");
                            break;
                        case 82741002:
                            basicReason.append("维修质量");
                            break;
                        case 82741003:
                            basicReason.append("服务");
                            break;
                    }
                } else {
                    switch (basicReasonList.get(i)) {
                        case 82741001:
                            basicReason.append("产品质量"+",");
                            break;
                        case 82741002:
                            basicReason.append("维修质量"+",");
                            break;
                        case 82741003:
                            basicReason.append("服务"+",");
                            break;
                    }
                }
            }
        }
        closeComplaintDTO.setBasicReason(basicReason.toString());
        if (!StringUtils.isNullOrEmpty(complaintInfoDTO.getIsRepaired())) {
            switch (complaintInfoDTO.getIsRepaired()) {
                case 83911001:
                    closeComplaintDTO.setRepairFlag("是");
                    break;
                case 83911002:
                    closeComplaintDTO.setRepairFlag("否");
                    break;
                case 83911003:
                    closeComplaintDTO.setRepairFlag("不涉及");
                    break;
                default:
                    closeComplaintDTO.setRepairFlag(null);
            }
        }
        closeComplaintDTO.setPotentialRisk(complaintInfoDTO.getRisk());
        closeComplaintDTO.setCreaterName(FrameworkUtil.getLoginInfo().getUserName());
        closeComplaintDTO.setCreater(follower.toString());
        closeComplaintDTO.setCreaterOrg(CreaterOrg);
        String Json = JSONObject.toJSONString(closeComplaintDTO);
        String path = "/api/services/app/Order/OrderCaseDone";
        String http = baseUrl + path;
        String noncestr1 = UUID.randomUUID().toString().replace("-", "");
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String sgin = commonServiceImpl.GetCheckSign(path, apiuid, apipwd, noncestr1, timestamp);
        String describe = "下发给400结案信息(售后)";
        HttpClient.requestInterfaceJsonCus(describe, Json, http, sgin, apiuid, noncestr1, timestamp);
    }


    /**
     * 调用获取单号存储过程
     *
     * @param type
     * @param dealerCode
     * @return
     * @throws ServiceBizException
     */
    public String getBillNo1(String type, String dealerCode) throws ServiceBizException {
        String billNo = "0";

        try {
            List<String> ins = new ArrayList();
            ins.add(dealerCode);
            ins.add(type);
            StringBuilder sb = new StringBuilder("select @e as errorCode,@r as billNo;");
            List<Object> params = new ArrayList<>();
            params.add(dealerCode);
            params.add(type);
            DAOUtil.execBatchPreparement("call P_TS_GETBILLNO(?,?,@e,@r);", params);
            List<Map> listout = DAOUtil.findAll(sb.toString(), false, (List) null, new boolean[0]);
            String errcode = ((Map) listout.get(0)).get("errorCode").toString();
            billNo = ((Map) listout.get(0)).get("billNo").toString();
            String error6 = "ERROR-6666";
            String error9 = "ERROR-9999";
            if (error6.equalsIgnoreCase(errcode)) {
                throw new ServiceBizException("已超出本日单号的上限!");
            } else if (error9.equalsIgnoreCase(errcode)) {
                throw new ServiceBizException("没有对应的单据类型!");
            } else {
                return billNo;
            }
        } catch (Exception var8) {
            logger.error("生成单号异常: {}", var8.getMessage());
            throw new ServiceBizException("生成单号异常", var8.getMessage());
        }
    }

    /**
     * sql拼接
     *
     * @param value
     * @param data
     * @return
     * @throws ServiceBizException
     */
    public String getSql(String value, String data) throws ServiceBizException {
        //sql拼接
        String[] split = value.split(",");
        StringBuffer res = new StringBuffer();
        for (int k = 0; k < split.length; k++) {
            if (k == split.length - 1) {
                res.append(data + " like '%" + split[k] + "%'");
            } else {
                res.append(data + " like '%" + split[k] + "%'" + " or ");
            }
        }
        return res.toString();
    }

    /**
     * 赋值
     */
    public ComplaintInfMoreDTO setComplaintInfMoreDTO(ComplaintInfMoreDTO complaintInfMoreDTO, String user) throws ParseException {
//        if (user != null) {
//            if (user.equals("SHDQJL") || user.equals("CCM") || user.equals("SHQYJL")|| user.equals("deal")) {
//                complaintInfMoreDTO.setIsCloseCase(10041002);
//                complaintInfMoreDTO.setInvalidCaseHidden(10041001);
//            }
//        }

        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (complaintInfMoreDTO.getSource1() != null) {
            List source1 = complaintInfMoreDTO.getSource1();
            StringBuffer source = new StringBuffer();
            for (int i = 0; i < source1.size(); i++) {

                if (i == source1.size() - 1) {
                    source.append(source1.get(i));
                } else {
                    source.append(source1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setSource(source.toString());
        }
        if (complaintInfMoreDTO.getType1() != null) {
            List type1 = complaintInfMoreDTO.getType1();
            StringBuffer type = new StringBuffer();
            for (int i = 0; i < type1.size(); i++) {

                if (i == type1.size() - 1) {
                    type.append(type1.get(i));
                } else {
                    type.append(type1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setType(type.toString());
        }
        if (complaintInfMoreDTO.getModel1() != null) {
            List model1 = complaintInfMoreDTO.getModel1();
            StringBuffer model = new StringBuffer();
            for (int i = 0; i < model1.size(); i++) {

                if (i == model1.size() - 1) {
                    model.append(model1.get(i));
                } else {
                    model.append(model1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setModel(model.toString());
        }
        //CCM负责人
        if (complaintInfMoreDTO.getCcmManId1() != null) {
            List ccmManId1 = complaintInfMoreDTO.getCcmManId1();
            StringBuffer ccmManId = new StringBuffer();
            for (int i = 0; i < ccmManId1.size(); i++) {

                if (i == ccmManId1.size() - 1) {
                    ccmManId.append(ccmManId1.get(i));
                } else {
                    ccmManId.append(ccmManId1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setCcmManId(ccmManId.toString());
        }
        //集团
        if (complaintInfMoreDTO.getBloc1() != null) {
            List bloc1 = complaintInfMoreDTO.getBloc1();
            StringBuffer blocId = new StringBuffer();
            for (int i = 0; i < bloc1.size(); i++) {

                if (i == bloc1.size() - 1) {
                    blocId.append(bloc1.get(i));
                } else {
                    blocId.append(bloc1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setBlocId(blocId.toString());
        }
        //经销商
        if (complaintInfMoreDTO.getDealerCode1() != null) {
            List dealerCode1 = complaintInfMoreDTO.getDealerCode1();
            StringBuffer dealerCode = new StringBuffer();
            for (int i = 0; i < dealerCode1.size(); i++) {

                if (i == dealerCode1.size() - 1) {
                    dealerCode.append("\"" + dealerCode1.get(i) + "\"");
                } else {
                    dealerCode.append("\"" + dealerCode1.get(i) + "\"" + ",");
                }
            }
            complaintInfMoreDTO.setDealerCode(dealerCode.toString());
        }
        //区域
        if (complaintInfMoreDTO.getRegion1() != null) {
            List region1 = complaintInfMoreDTO.getRegion1();
            StringBuffer regionId = new StringBuffer();
            for (int i = 0; i < region1.size(); i++) {

                if (i == region1.size() - 1) {
                    regionId.append(region1.get(i));
                } else {
                    regionId.append(region1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setRegionId(regionId.toString());
        }
        //区域经理
        if (complaintInfMoreDTO.getRegionManager1() != null) {
            List regionManager1 = complaintInfMoreDTO.getRegionManager1();
            StringBuffer regionManagerId = new StringBuffer();
            for (int i = 0; i < regionManager1.size(); i++) {

                if (i == regionManager1.size() - 1) {
                    regionManagerId.append("\"" + regionManager1.get(i) + "\"");
                } else {
                    regionManagerId.append("\"" + regionManager1.get(i) + "\"" + ",");
                }
            }
            complaintInfMoreDTO.setRegionManagerId(regionManagerId.toString());
        }
        //小区
        if (complaintInfMoreDTO.getAreaId1() != null) {
            List areaId1 = complaintInfMoreDTO.getAreaId1();
            StringBuffer areaId = new StringBuffer();
            for (int i = 0; i < areaId1.size(); i++) {

                if (i == areaId1.size() - 1) {
                    areaId.append(areaId1.get(i));
                } else {
                    areaId.append(areaId1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setAreaId(areaId.toString());
        }
        if (complaintInfMoreDTO.getCallTime1() != null && complaintInfMoreDTO.getCallTime1().length > 0) {
            complaintInfMoreDTO.setCallTime(sf.parse(complaintInfMoreDTO.getCallTime1()[0]));
            complaintInfMoreDTO.setCallTime2(sf.parse(complaintInfMoreDTO.getCallTime1()[1]));
        }
        if (complaintInfMoreDTO.getCloseCaseTime1() != null && complaintInfMoreDTO.getCloseCaseTime1().length > 0) {
            complaintInfMoreDTO.setCloseCaseTime(sf.parse(complaintInfMoreDTO.getCloseCaseTime1()[0]));
            complaintInfMoreDTO.setCloseCaseTime2(sf.parse(complaintInfMoreDTO.getCloseCaseTime1()[1]));
        }
        if (complaintInfMoreDTO.getFirstCloseCaseTime1() != null && complaintInfMoreDTO.getFirstCloseCaseTime1().length >= 2) {
            complaintInfMoreDTO.setFirstCloseCaseTime(sf.parse(complaintInfMoreDTO.getFirstCloseCaseTime1()[0]));
            complaintInfMoreDTO.setFirstCloseCaseTime2(sf.parse(complaintInfMoreDTO.getFirstCloseCaseTime1()[1]));
        }
        if (complaintInfMoreDTO.getFirstRestartCloseCaseTime1() != null && complaintInfMoreDTO.getFirstRestartCloseCaseTime1().length > 0) {
            complaintInfMoreDTO.setFirstRestartCloseCaseTime(sf.parse(complaintInfMoreDTO.getFirstRestartCloseCaseTime1()[0]));
            complaintInfMoreDTO.setFirstRestartCloseCaseTime2(sf.parse(complaintInfMoreDTO.getFirstRestartCloseCaseTime1()[1]));
        }
        if (complaintInfMoreDTO.getActuallFollowTime2() != null && complaintInfMoreDTO.getActuallFollowTime2().length >= 2) {
            complaintInfMoreDTO.setActuallFollowTime(sf.parse(complaintInfMoreDTO.getActuallFollowTime2()[0]));
            complaintInfMoreDTO.setActuallFollowTime1(sf.parse(complaintInfMoreDTO.getActuallFollowTime2()[1]));
        }
        if (complaintInfMoreDTO.getFisrtRestartTime1() != null && complaintInfMoreDTO.getFisrtRestartTime1().length > 0) {
            complaintInfMoreDTO.setFisrtRestartTime(sf.parse(complaintInfMoreDTO.getFisrtRestartTime1()[0]));
            complaintInfMoreDTO.setFisrtRestartTime2(sf.parse(complaintInfMoreDTO.getFisrtRestartTime1()[1]));
        }
        if (complaintInfMoreDTO.getPlanFollowTime() != null && complaintInfMoreDTO.getPlanFollowTime5().length > 0) {
            complaintInfMoreDTO.setPlanFollowTime1(sf.parse(complaintInfMoreDTO.getPlanFollowTime5()[0]));
            complaintInfMoreDTO.setPlanFollowTime2(sf.parse(complaintInfMoreDTO.getPlanFollowTime5()[1]));
        }
        ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO = new ComplaintCustomFieldUseDTO();
        complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
        complaintCustomFieldUseDTO.setIsDeleted(0);
        complaintCustomFieldUseDTO.setIsQuery(true);

        //自定义查询
        if (complaintInfMoreDTO.getFollowStatus1() != null && complaintInfMoreDTO.getFollowStatus1().size() != 0) {
            List followStatus1 = complaintInfMoreDTO.getFollowStatus1();
            StringBuffer followStatus = new StringBuffer();
            for (int i = 0; i < followStatus1.size(); i++) {

                if (i == followStatus1.size() - 1) {
                    followStatus.append(followStatus1.get(i));
                } else {
                    followStatus.append(followStatus1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setFollowStatus(getSql(followStatus.toString(), "t.follow_status"));
        }
        if (complaintInfMoreDTO.getCcmPart1() != null && complaintInfMoreDTO.getCcmPart1().size() != 0) {
            List ccmPart1 = complaintInfMoreDTO.getCcmPart1();
            StringBuffer ccmPart = new StringBuffer();
            for (int i = 0; i < ccmPart1.size(); i++) {

                if (i == ccmPart1.size() - 1) {
                    ccmPart.append(ccmPart1.get(i));
                } else {
                    ccmPart.append(ccmPart1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setCcmPart(getSql(ccmPart.toString(), "t3.ccm_part"));
        }
        if (complaintInfMoreDTO.getCcmSubdivisionPart1() != null && complaintInfMoreDTO.getCcmSubdivisionPart1().size() != 0) {
            List ccmSubdivisionPart1 = complaintInfMoreDTO.getCcmSubdivisionPart1();
            StringBuffer ccmSubdivisionPart = new StringBuffer();
            for (int i = 0; i < ccmSubdivisionPart1.size(); i++) {

                if (i == ccmSubdivisionPart1.size() - 1) {
                    ccmSubdivisionPart.append(ccmSubdivisionPart1.get(i));
                } else {
                    ccmSubdivisionPart.append(ccmSubdivisionPart1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setCcmSubdivisionPart(getSql(ccmSubdivisionPart.toString(), "t3.ccm_subdivision_part"));
        }
        if (complaintInfMoreDTO.getCcMainReason1() != null && complaintInfMoreDTO.getCcMainReason1().size() != 0) {
            List ccMainReason1 = complaintInfMoreDTO.getCcMainReason1();
            StringBuffer ccMainReason = new StringBuffer();
            for (int i = 0; i < ccMainReason1.size(); i++) {

                if (i == ccMainReason1.size() - 1) {
                    ccMainReason.append(ccMainReason1.get(i));
                } else {
                    ccMainReason.append(ccMainReason1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setCcMainReason(getSql(ccMainReason.toString(), "t3.cc_main_reason"));
        }
        if (complaintInfMoreDTO.getCcResult1() != null && complaintInfMoreDTO.getCcResult1().size() != 0) {
            List ccResult1 = complaintInfMoreDTO.getCcResult1();
            StringBuffer ccResult = new StringBuffer();
            for (int i = 0; i < ccResult1.size(); i++) {

                if (i == ccResult1.size() - 1) {
                    ccResult.append(ccResult1.get(i));
                } else {
                    ccResult.append(ccResult1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setCcResult(getSql(ccResult.toString(), "t3.cc_result"));
        }
        if (complaintInfMoreDTO.getCategory11() != null && complaintInfMoreDTO.getCategory11().size() != 0) {
            List category11 = complaintInfMoreDTO.getCategory11();
            StringBuffer category1 = new StringBuffer();
            for (int i = 0; i < category11.size(); i++) {

                if (i == category11.size() - 1) {
                    category1.append(category11.get(i));
                } else {
                    category1.append(category11.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setCategory1(getSql(category1.toString(), "t.category1"));
        }
        if (complaintInfMoreDTO.getCategory21() != null && complaintInfMoreDTO.getCategory21().size() != 0) {
            List category21 = complaintInfMoreDTO.getCategory21();
            StringBuffer category2 = new StringBuffer();
            for (int i = 0; i < category21.size(); i++) {

                if (i == category21.size() - 1) {
                    category2.append(category21.get(i));
                } else {
                    category2.append(category21.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setCategory2(getSql(category2.toString(), "t.category2"));
        }
        if (complaintInfMoreDTO.getCategory31() != null && complaintInfMoreDTO.getCategory31().size() != 0) {
            List category31 = complaintInfMoreDTO.getCategory31();
            StringBuffer category3 = new StringBuffer();
            for (int i = 0; i < category31.size(); i++) {

                if (i == category31.size() - 1) {
                    category3.append(category31.get(i));
                } else {
                    category3.append(category31.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setCategory3(getSql(category3.toString(), "t.category3"));
        }
        if (complaintInfMoreDTO.getClassification12() != null && complaintInfMoreDTO.getClassification12().size() != 0) {
            List classification12 = complaintInfMoreDTO.getClassification12();
            StringBuffer classification1 = new StringBuffer();
            for (int i = 0; i < classification12.size(); i++) {

                if (i == classification12.size() - 1) {
                    classification1.append(classification12.get(i));
                } else {
                    classification1.append(classification12.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setClassification11(getSql(classification1.toString(), "t3.classification1"));
        }
        if (complaintInfMoreDTO.getClassification22() != null && complaintInfMoreDTO.getClassification22().size() != 0) {
            List classification22 = complaintInfMoreDTO.getClassification22();
            StringBuffer classification2 = new StringBuffer();
            for (int i = 0; i < classification22.size(); i++) {

                if (i == classification22.size() - 1) {
                    classification2.append(classification22.get(i));
                } else {
                    classification2.append(classification22.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setClassification21(getSql(classification2.toString(), "t3.classification2"));
        }
        if (complaintInfMoreDTO.getClassification3() != null) {
            complaintInfMoreDTO.setClassification3(complaintInfMoreDTO.getClassification3());
        }
        if (complaintInfMoreDTO.getSmallClass1() != null && complaintInfMoreDTO.getSmallClass1().size() != 0) {
            List smallClass1 = complaintInfMoreDTO.getSmallClass1();
            StringBuffer smallClass = new StringBuffer();
            for (int i = 0; i < smallClass1.size(); i++) {

                if (i == smallClass1.size() - 1) {
                    smallClass.append(smallClass1.get(i));
                } else {
                    smallClass.append(smallClass1.get(i) + ",");
                }
            }
            complaintInfMoreDTO.setSmallClass(getSql(smallClass.toString(), "t4.small_class"));
        }
        //自定义置顶
        ComplaintCustomTopUseDTO complaintCustomTopUseDTO = new ComplaintCustomTopUseDTO();
        complaintCustomTopUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
        complaintCustomTopUseDTO.setIsDeleted(false);
        complaintCustomTopUseDTO.setIsValid(10041001);
        List<ComplaintCustomTopUseDTO> toplist = complaintCustomTopUseService.selectListBySql(complaintCustomTopUseDTO);
        String sqltext1 = "";
        String sql = "";
        String sql1 = "";
        String sql2 = "";
        String dealer = "deal";
        if (dealer.equals(user)) {
            complaintInfMoreDTO.setAssitdep(FrameworkUtil.getLoginInfo().getOwnerCode());
        }
        String assisDepartment = "assisDepartment";
        //协助部门先设施，等到中台完善部门
//        String department = "323";
        if (assisDepartment.equals(user)) {
            complaintInfMoreDTO.setAssisDepartment(complaintInfMoreDTO.getAssisDepartment());
        }

        String time = "3000/11/21 01:46:40";
        if (toplist.size() != 0) {
            sqltext1 = "order by ";
            for (int i = 0; i < toplist.size(); i++) {
                String fieldName = toplist.get(i).getType();
                switch (fieldName) {
                    case "planFollowTime":
                        sqltext1 += "," + "IFNULL(t3.plan_follow_time,9999999999999) ";
                        break;
                    case "ccmIsRead":
                        sqltext1 += "," + "t.ccm_is_read=1 desc ";
                        break;
                    case "importanceLevel":
                        sqltext1 += "," + "t.importance_level=82091001 DESC ";
                        break;
                    case "callTime":
                        sqltext1 += "," + "t.call_time and t.close_case_status!=82441005 and  t.close_case_status!=82441005  and t.is_close_case=10041002 DESC";
                        break;
                    case "isFinish":
                        sqltext1 += "," + "t5.is_finish=10041001 desc ";
                        break;
                    default:

                }
            }
            sql1 = sqltext1.replaceAll(",(.*)", "$1");

        }
        //自定义排序
        complaintCustomFieldUseDTO = new ComplaintCustomFieldUseDTO();
        complaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
        complaintCustomFieldUseDTO.setIsDeleted(0);
        complaintCustomFieldUseDTO.setIsSort(1);
        List<ComplaintCustomFieldUseDTO> sortUse = complaintCustomFieldUseService.selectListBySql(complaintCustomFieldUseDTO);
        String sqltext = "";
        if (sortUse.size() != 0) {
            if (toplist.size() != 0) {
                sqltext = ",";
            } else {
                sqltext = "order by ";
            }
            for (int i = 0; i < sortUse.size(); i++) {
                String fieldName = sortUse.get(i).getFieldName();
                String value = sortUse.get(i).getSortType();
                switch (fieldName) {
                    case "complaint_id":
                        sqltext += "t.complaint_id +0 " + "+" + value;
                        sql="order by md,ccs, t.complaint_id +0 " + value;
                        break;
                    case "call_time":
                        sqltext +=  "t.call_time " + " " + value;
                        sql="order by md,ccs, t.call_time " + value;
                        break;
                    case "fisrt_restart_dealer_fisrt_reply_time":
                        sqltext +=  "t.fisrt_restart_dealer_fisrt_reply_time " + " " + value;
                        sql="order by md,ccs, t.fisrt_restart_dealer_fisrt_reply_time " + value;
                        break;
                    case "close_case_time":
                        sqltext +=  "t.close_case_time " + " " + value;
                        sql="order by md,ccs, t.close_case_time " + value;
                        break;
                    case "dealer_fisrt_reply_time":
                        sqltext +=  "t.call_time " + " " + value;
                        sql="order by md,ccs, t.dealer_fisrt_reply_time " + value;
                        break;
                    case "call_newest_restart_timetime":
                        sqltext +=  "t.newest_restart_time " + " " + value;
                        sql="order by md,ccs, t.newest_restart_time " + value;
                        break;
                    case "first_close_case_time":
                        sqltext +=  "t.first_close_case_time " + " " + value;
                        sql="order by md,ccs, t.first_close_case_time " + value;
                        break;
                    case "fisrt_restart_time":
                        sqltext +=  "t.fisrt_restart_time " + " " + value;
                        sql="order by md,ccs, t.fisrt_restart_time " + value;
                        break;
                    case "first_restart_close_case_time":
                        sqltext +=  "t.first_restart_close_case_time " + " " + value;
                        sql="order by md,ccs, t.first_restart_close_case_time " + value;
                        break;
                    default:
                }

            }

        }else{
            sql="order by md,ccs, t.call_time  ";
        }
        String dealerCode = FrameworkUtil.getLoginInfo().getOwnerCode();
        String aa = "(t.dealer_code =" + "\"" + dealerCode + "\"" + " or EXISTS(SELECT 1 FROM tt_complaint_assist_department   t5 WHERE t.id=t5.complaint_info_id and  t5.assist_dealer_code <>t.dealer_code and t5.assist_dealer_code=" + "\"" + dealerCode + "\"" + "or t5.assist_department=" + "\"" + dealerCode + "\"" + "))";
//        sql += sql1 + sqltext.replaceAll(",(.*)", "$1");
//       sql="order by  t.call_time  ";
        complaintInfMoreDTO.setSql(sql);
        if (dealer.equals(user)) {
            complaintInfMoreDTO.setSql1(aa);
        } else {
            complaintInfMoreDTO.setReport(true);
        }

        return complaintInfMoreDTO;
    }


}
