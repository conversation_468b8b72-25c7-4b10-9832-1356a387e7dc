package com.yonyou.dmscus.customer.service.parse;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscus.customer.dao.Parse.ParseLogMapper;
import com.yonyou.dmscus.customer.entity.po.parse.ParseLogPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
public class ParseLogServiceImpl extends ServiceImpl<ParseLogMapper, ParseLogPo> implements ParseLogService {


    @Override
    @Async("asyncThreadPool")
    public void saveLogInfo(List<ParseLogPo> poList) {
        if(CollectionUtils.isEmpty(poList)){
            log.info("无解析日志信息需要保存");
            return;
        }
        saveBatch(poList);
    }
}
