package com.yonyou.dmscus.customer.service.impl.faultLight;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64Encoder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yonyou.cloud.common.beans.RestResultResponse;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.constants.faultLight.BookingOrderStatusEnum;
import com.yonyou.dmscus.customer.constants.faultLight.FaultClueStateEnum;
import com.yonyou.dmscus.customer.constants.faultLight.FaultClueWheResEnum;
import com.yonyou.dmscus.customer.constants.faultLight.FaultFollowStateEnum;
import com.yonyou.dmscus.customer.dao.faultLight.*;
import com.yonyou.dmscus.customer.dao.voicemanage.SaWorkNumberMapper;
import com.yonyou.dmscus.customer.dto.BookingOrderInfoVO;
import com.yonyou.dmscus.customer.dto.ResponseCdpDTO;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.dto.faultLight.FaultLightBookingRecordDto;
import com.yonyou.dmscus.customer.enevt.LeadStatusChangeEvent;
import com.yonyou.dmscus.customer.entity.dto.faultLight.*;
import com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.info.DiagnosisDtcInfo;
import com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.info.DiagnosisRecord;
import com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.info.DiagnosisRetResult;
import com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.info.DiagnosisSnapshotInfo;
import com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.info.DtcDiagnosisInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.info.OriginDtcDiagnosisResult;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.SaCustomerNumberDTO;
import com.yonyou.dmscus.customer.entity.po.faultLight.CluesDiagnosticInfoRelationPO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultCallRegisterPO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightCluePO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightFollowRecordPO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightInvitationPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaWorkNumberPO;
import com.yonyou.dmscus.customer.feign.DatainFeign;
import com.yonyou.dmscus.customer.feign.DomainMaintainLeadFeign;
import com.yonyou.dmscus.customer.feign.DomainMaintainOrdersClient;
import com.yonyou.dmscus.customer.feign.RepairCommonClient;
import com.yonyou.dmscus.customer.feign.dto.FaultRepairOrderDto;
import com.yonyou.dmscus.customer.feign.dto.WarningDto;
import com.yonyou.dmscus.customer.feign.vo.PrintDataVo;
import com.yonyou.dmscus.customer.feign.vo.PrintParamVo;
import com.yonyou.dmscus.customer.service.faultLight.CluesDiagnosticInfoRelationService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightDisposeService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightService;
import com.yonyou.dmscus.customer.service.impl.voicemanage.WorkNumberServiceContext;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.dmscus.customer.utils.ClazzConverter;
import com.yonyou.dmscus.customer.utils.HttpClientUtil;
import com.yonyou.dmscus.customer.utils.ai.DccHttpHelper;
import com.yonyou.dmscus.customer.utils.ai.DccResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
@RefreshScope
public class FaultLightServiceImpl implements FaultLightService {

    private static final int FORTY_EIGHT_HOURS = 48;

    private static final String PAYMENT_OBJECT_CODE = "************";
    private static final int THIRTY_DAY = 24*30;
    private static final String SUCCESS = "00000";
    public static final String AUTHORIZATION = "Authorization";
    public static final String DTC = "DTC";
    @Value("${ai.telecom.common.url_mapping_register}")
    String URL_MAPPING_REGISTER;  //呼叫登记
    @Autowired
    private RepairCommonClient repairCommonClient;
    @Autowired
    private DomainMaintainOrdersClient domainMaintainOrdersClient;

    @Resource
    private RocketMQTemplate rocketMQTemplate;
    @Autowired
    private WorkNumberServiceContext workNumberServiceContext;
    @Autowired
    private FaultLightDisposeService faultLightDisposeService;
    @Resource
    private TtFaultCallRegisterMapper ttFaultCallRegisterMapper;
    @Resource
    private SaWorkNumberMapper saWorkNumberMapper;
    @Resource
    private TtFaultCallDetailsMapper ttFaultCallDetailsMapper;
    @Resource
    private TtFaultLightClueMapper ttFaultLightClueMapper;
    @Resource
    private TtFaultLightInvitationMapper ttFaultLightInvitationMapper;
    @Resource
    private TtFaultLightFollowRecordMapper ttFaultLightFollowRecordMapper;
    @Resource
    private DtcCodeMapper dtcCodeMapper;

    @Autowired
    private ApplicationEventPublisher eventPublisher;
    @Resource
    private DomainMaintainLeadFeign domainMaintainLeadFeign;

    private DatainFeign datainFeign;
    @Resource
    private CluesDiagnosticInfoRelationService cluesDiagnosticInfoRelationService;

    @Value("${topic.faultLight:TOPIC_FAULT_LIGHT}")
    private String faultLightTopic;
    @Value("${fault.cdp.warning-url:https://uat-cdp-ma.volvocars.com.cn/failure-light-api/dl/vehicle/warning}")
    private String cdpFault;
    @Value("${fault.cdp.ak:zokCgU8Usq50pwxNUja7KX9w}")
    private String ak;
    @Value("${fault.cdp.sk:34O0UGEqQfc4xqcUtseJmKjyCwVmMA}")
    private String as;
    @Value("${hc.queryDtcDiagnosisInfo.url:http://uat-datain.digitalvolvo.com/failure-light-dtc/dl/vehicle/dtc/detail}")
    private String queryDtcDiagnosisInfoUrl;


    @Resource
    private MidUrlProperties midUrlProperties;
    /**
     * 计算年龄
     *
     * @param dateOfBirth
     * @return
     */
    public static int getAge(Date dateOfBirth) {
        int age = 0;
        Calendar born = Calendar.getInstance();
        Calendar now = Calendar.getInstance();
        if (dateOfBirth != null) {
            now.setTime(new Date());
            born.setTime(dateOfBirth);
            if (born.after(now)) {
                throw new IllegalArgumentException("年龄不能超过当前日期");
            }
            age = now.get(Calendar.YEAR) - born.get(Calendar.YEAR);
            int nowDayOfYear = now.get(Calendar.DAY_OF_YEAR);
            int bornDayOfYear = born.get(Calendar.DAY_OF_YEAR);
            System.out.println("nowDayOfYear:" + nowDayOfYear + " bornDayOfYear:" + bornDayOfYear);
            if (nowDayOfYear < bornDayOfYear) {
                age -= 1;
            }
        }
        return age;
    }

    /**
     * 计算年龄
     *
     * @param dateOfBirth
     * @return
     */
    public static int getAge(String dateOfBirth) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        try {
            date = sdf.parse(dateOfBirth);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return getAge(date);
    }


    @Override
    public String saveSaCustomerNumber(SaCustomerNumberDTO saCustomerNumberDTO) {
        log.info("saveSaCustomerNumber,start:{}", JSON.toJSONString(saCustomerNumberDTO));
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //uuid
        String callId = Objects.isNull(saCustomerNumberDTO.getCallId())
            ? UUID.randomUUID().toString().replace("-", "").toLowerCase()
            : saCustomerNumberDTO.getCallId();
        //校验当前账号是否绑定AI语音工作号
        LambdaQueryWrapper<SaWorkNumberPO> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(SaWorkNumberPO::getDealerCode, loginInfoDto.getOwnerCode());
        queryWrapper.eq(SaWorkNumberPO::getSaId, saCustomerNumberDTO.getSaId());
        SaWorkNumberPO rs = saWorkNumberMapper.selectOne(queryWrapper);
        if (rs == null) {
            throw new DALException("当前账号还未绑定AI语音工作号，不可使用!");
        }
        //校验联系方式格式是否正确
        if (11 != saCustomerNumberDTO.getCusNumber().length()) {
            throw new DALException("手机号应为11位数");
        }
        log.info("saveSaCustomerNumber,rs:{}", JSON.toJSONString(rs));
        String operator = rs.getOperator();
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(operator, String.valueOf(CommonConstants.NEW_TELECOM_OPERATOR))) {
            log.info("StringUtils.equalsIgnoreCase(operator, String.valueOf(CommonConstants.NEW_TELECOM_OPERATOR))");
            return operatorIf(saCustomerNumberDTO, callId, rs);
        }

        TtFaultCallRegisterPO ttFaultCallRegisterPO = new TtFaultCallRegisterPO();
        ttFaultCallRegisterPO.setCallId(callId);
        ttFaultCallRegisterPO.setInviteId(saCustomerNumberDTO.getInviteId());
        ttFaultCallRegisterPO.setSaId(saCustomerNumberDTO.getSaId());
        ttFaultCallRegisterPO.setCusName(saCustomerNumberDTO.getCusName());
        ttFaultCallRegisterPO.setCusNumber(saCustomerNumberDTO.getCusNumber());
        ttFaultCallRegisterPO.setDealerCode(loginInfoDto.getOwnerCode());
        ttFaultCallRegisterPO.setSaName(rs.getSaName());
        ttFaultCallRegisterPO.setSaNumber(rs.getSaNumber());
        ttFaultCallRegisterPO.setWorkNumber(rs.getWorkNumber());
        ttFaultCallRegisterPO.setBatchNo(saCustomerNumberDTO.getBatchNo());
        ttFaultCallRegisterMapper.insert(ttFaultCallRegisterPO);

        //电信 呼叫登记
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("callId", callId);  //登记唯一标识
        map.put("holderNumber", rs.getSaNumber()); //服务顾问手机号
        map.put("workNumber", rs.getWorkNumber()); //工作号
        map.put("customNumber", saCustomerNumberDTO.getCusNumber()); //联系方式
        map.put("expireMinute", 30);  //默认写死30
        log.info("呼叫登记：" + map);
        String str = DccHttpHelper.httpPost(URL_MAPPING_REGISTER, map);
        DccResponseUtil response = JSONObject.parseObject(str, DccResponseUtil.class);
        if (!"0".equals(response.getCode())) {
            throw new DALException(response.getMessage());
        }
        log.info("呼叫登记结束：{}", str);
        //返回绑定的工作号
        return rs.getWorkNumber();
    }

    @Override
    public List<TtFaultCallDetailsDTO> queryCallDetails(TtFaultCallDetailsDTO dto) {

        return ttFaultCallDetailsMapper.queryCallDetails(dto.getInviteId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void faultLightOrderCorrelation() {
        log.info("faultLightOrderCorrelation,故障灯关联工单start");
        //查询线索状态为待进店，且工单为空的数据  优化: 线索状态为:待进店/已进店,工单号为空。(查已进店数据是为了处理历史线索状态为已进店,跟进状态为未关联工单的数据)
        List<TtFaultLightCluePO> ttFaultLightCluePOS = ttFaultLightClueMapper.selectClueInfoList();
        //状态为已进店,没有关联预约单且工单已作废。(工单查询时分500一批次)
        List<TtFaultLightCluePO> voidedOrderList = selectVoidedOrderClue();
        List<TtFaultLightCluePO> list = getAllList(ttFaultLightCluePOS, voidedOrderList);
        doFaultLightOrderCorrelation(list);
    }

    /**状态为已进店,没有关联预约单且工单已作废*/
    private List<TtFaultLightCluePO> selectVoidedOrderClue(){
        log.info("selectVoidedOrderClue,start");
        //查询已进店有关联工单的线索
        List<TtFaultLightCluePO> ttFaultLightCluePOS = ttFaultLightClueMapper.selectClueArriveList();
        if(CollectionUtils.isEmpty(ttFaultLightCluePOS)){
            log.info("selectVoidedOrderClue,ttFaultLightCluePOS is null");
            return null;
        }
        List<FaultRepairOrderDto> list = convertRepairOrderDto(ttFaultLightCluePOS);
        //查询工单是否作废
        List<FaultRepairOrderDto> repairOrderDto = queryVoidedOrder(list);
        if(CollectionUtils.isEmpty(repairOrderDto)){
            log.info("selectVoidedOrderClue,repairOrderDto is null");
            return null;
        }
        //组合参数
        List<TtFaultLightCluePO> poList = filterMatching(ttFaultLightCluePOS, repairOrderDto);
        log.info("selectVoidedOrderClue,poList:{}",poList.size());
        if(CollectionUtils.isEmpty(poList)){
            log.info("selectVoidedOrderClue,poList is null");
            return null;
        }
        return poList;
    }

    private List<FaultRepairOrderDto> queryVoidedOrder(List<FaultRepairOrderDto> list){
        if (CollectionUtils.isEmpty(list)){
            log.info("queryVoidedOrder list is null");
            return Collections.emptyList();
        }
        log.info("queryVoidedOrder,list:{}", list.size());
        RestResultResponse<List<FaultRepairOrderDto>> response = null;
        try{
        response = domainMaintainOrdersClient.queryVoidedOrder(list);
            log.info("queryVoidedOrder,response:{}",JSON.toJSONString(response));
        }catch (Exception e) {
            log.error("queryVoidedOrder,error:",e);
            throw new ServiceBizException("queryVoidedOrder e:{}", e);
        }
        if(Objects.nonNull(response) && Objects.nonNull(response.getData())){
            log.info("queryVoidedOrder,data is not null");
            return response.getData();
        }
        return Collections.emptyList();
    }
    private FaultRepairOrderDto queryRoNoSpinner(String ownerCode,String vin,Date startTime,Date endTime){
        log.info("queryRoNoSpinner,ownerCode:{},vin:{},startTime:{},endTime:{}", ownerCode,vin,startTime,endTime);
        RestResultResponse<FaultRepairOrderDto> response = null;
        try{
            response = domainMaintainOrdersClient.queryRoNoSpinner(ownerCode,vin,startTime,endTime);
            log.info("queryRoNoSpinner,response:{}",JSON.toJSONString(response));
        }catch (Exception e) {
            log.error("queryRoNoSpinner,error:",e);
            throw new ServiceBizException("queryRoNoSpinner e:{}", e);
        }
        if(Objects.nonNull(response) && Objects.nonNull(response.getData())){
            log.info("queryRoNoSpinner,data is not null");
            return response.getData();
        }
        return null;
    }

    private List<TtFaultLightCluePO> filterMatching(List<TtFaultLightCluePO> faultLightClueList, List<FaultRepairOrderDto> repairOrderDtoList) {
        return faultLightClueList.stream()
                .filter(po -> repairOrderDtoList.stream()
                        .anyMatch(dto -> dto.getRoNo().equals(po.getRoNo()) &&
                                dto.getOwnerCode().equals(po.getDealerCode())))
                .collect(Collectors.toList());
    }

    private List<FaultRepairOrderDto> convertRepairOrderDto(List<TtFaultLightCluePO> faultLightClueList) {
        return faultLightClueList.stream()
                .map(po -> FaultRepairOrderDto.builder()
                        .roNo(po.getRoNo())
                        .ownerCode(po.getDealerCode())
                        .build()) // 使用 Builder 模式
                .collect(Collectors.toList());
    }

    private void doFaultLightOrderCorrelation(List<TtFaultLightCluePO> ttFaultLightCluePOS) {
        if (CollectionUtils.isEmpty(ttFaultLightCluePOS)) {
            log.info("CollectionUtils.isEmpty(ttFaultLightCluePOS)");
            return;
        }
        log.info("faultLightOrderCorrelation,未关联工单线索/工单作废线索:{}", ttFaultLightCluePOS.size());
        //当前时间
        Date date = new Date();
        //获取前一天
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, -1);
        Date frontDate = calendar.getTime();
        log.info("faultLightOrderCorrelation,date:{},frontDate:{}", date, frontDate);
        List<TtFaultLightCluePO> ttFaultLightCluePOList = new ArrayList<>();
        List<TtFaultLightFollowRecordPO> lightFollowRecordPOS = new ArrayList<>();
        List<TtFaultLightInvitationPO> lightInvitationList = new ArrayList<>();
        Integer clueStatus;//线索状态
        Integer followStatus;//跟进状态
        String vin;//vin
        String dealerCode;//经销商
        Date clueGenTime;//线索生成时间
        Date forecastTime;//预约进店时间
        Date appDate;//预约进店时间+5天
        TtFaultLightInvitationPO ttFaultLightInvitationPO;
        for (TtFaultLightCluePO tl : ttFaultLightCluePOS) {
            log.info("doFaultLightOrderCorrelation,ttFaultLightCluePOS:{}", tl);
            vin = tl.getVin();
            clueStatus = null;
            followStatus = null;
            dealerCode = tl.getDealerCode();
            clueGenTime = tl.getClueGenTime();
            forecastTime = tl.getForecastTime();
            if(Objects.isNull(forecastTime)){
                log.info("faultLightOrderCorrelation, forecastTime is null");
                continue;
            }
            appDate = DateUtils.parseDate(forecastTime, 15, DateUtils.PATTERN_YYYY_MM_DD_23_59_59);
            FaultRepairOrderDto orderDto;
            log.info("faultLightOrderCorrelation,vin:{},dealerCode:{},forecastTime:{},appDate:{}",
                    vin, dealerCode, DateUtils.dateToString(forecastTime, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS),
                    DateUtils.dateToString(appDate, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
            /*if (forecastTime.compareTo(date) >= 0) {
                log.info("faultLightOrderCorrelation, forecastTime.compareTo(date) >= 0");
                continue;
            }*/
            //查询十五天内的自店工单
            orderDto = queryRoNoSpinner(dealerCode, vin, clueGenTime, appDate);
            if (Objects.nonNull(orderDto)) {
                log.info("faultLightOrderCorrelation, 十五天内有自店工单");
                clueStatus = FaultClueStateEnum.ENTERED_STORE.getCode();
                followStatus = FaultFollowStateEnum.ENTERED_STORE.getCode();
                tl.setRoNo(orderDto.getRoNo());
                tl.setRoType(orderDto.getRepairTypeCode());
                tl.setRoStartTime(DateUtils.convertLocalDateTimeToDate(orderDto.getCreatedAt()));
                //修改预约记录表成功进店后的参数更新
                ttFaultLightInvitationPO = new TtFaultLightInvitationPO();
                ttFaultLightInvitationPO.setClueId(tl.getId());
                //进店时间去前一天
                ttFaultLightInvitationPO.setIntoTime(frontDate);
                ttFaultLightInvitationPO.setIntoOnTime(getIntoOnTime(frontDate, forecastTime));
                ttFaultLightInvitationPO.setNoInto(FaultClueWheResEnum.WHE_RES_ONE.getCode());
                lightInvitationList.add(ttFaultLightInvitationPO);
            } else if (appDate.compareTo(date) < 0) {
                log.info("faultLightOrderCorrelation,appDate.compareTo(date) < 0");
                //查全量
                orderDto = queryRoNoSpinner(null, vin, clueGenTime, appDate);
                if (Objects.nonNull(orderDto)) {
                    log.info("faultLightOrderCorrelation, 十五天内全量数据有工单");
                    clueStatus = FaultClueStateEnum.ENTERING_STORE_NATURALLY.getCode();
                    followStatus = FaultFollowStateEnum.ENTERING_STORE_NATURALLY.getCode();
                    ttFaultLightInvitationPO = new TtFaultLightInvitationPO();
                    ttFaultLightInvitationPO.setClueId(tl.getId());
                    ttFaultLightInvitationPO.setSelfInto(FaultClueWheResEnum.WHE_RES_TWO.getCode());
                    lightInvitationList.add(ttFaultLightInvitationPO);
                } else {
                    log.info("faultLightOrderCorrelation, 十五天内无工单");
                    clueStatus = FaultClueStateEnum.CLOSE_CLUES.getCode();
                    followStatus = FaultFollowStateEnum.OVERRUN_ENTERING_STORE.getCode();
                    tl.setClueCloTime(new Date());
                    //修改预约记录表状态
                    ttFaultLightInvitationPO = new TtFaultLightInvitationPO();
                    ttFaultLightInvitationPO.setClueId(tl.getId());
                    ttFaultLightInvitationPO.setNoInto(FaultClueWheResEnum.WHE_RES_TWO.getCode());
                    lightInvitationList.add(ttFaultLightInvitationPO);
                }
            }
            if (clueStatus != null && followStatus != null) {
                tl.setClueStatus(clueStatus);
                tl.setFollowStatus(followStatus);
                ttFaultLightCluePOList.add(tl);
                lightFollowRecordPOS.add(converClueToRecord(tl));
            }
        }
        //批量修改线索、跟进数据
        if (ttFaultLightCluePOList.size() > 0) {
            //线索
            Lists.partition(ttFaultLightCluePOList, 1000).forEach(ttFaultLightClueMapper::batchUpdate);//跟进
            Lists.partition(lightFollowRecordPOS, 1000).forEach(ttFaultLightFollowRecordMapper::batchInsert);
        }
        if (lightInvitationList.size() > 0) {
            //修改预约记录表状态
            Lists.partition(lightInvitationList, 1000).forEach(ttFaultLightInvitationMapper::updateNoIntoById);
        }

        log.info("faultLightOrderCorrelation,关联故障单-MQ-消息推送：{}", JSONObject.toJSONString(ttFaultLightCluePOList));
        //MQ消息推送
        ttFaultLightCluePOList.forEach(cp -> {
            log.info("faultLightOrderCorrelation,故障灯关联工单MQ消息推送");
            this.pushMessage(cp.getIcmId(), String.valueOf(cp.getFollowStatus()), String.valueOf(cp.getClueStatus()), new Date());
        });
    }

    private static List<TtFaultLightCluePO> getAllList(List<TtFaultLightCluePO> ttFaultLightCluePOS, List<TtFaultLightCluePO> voidedOrderList) {
        List<TtFaultLightCluePO> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(ttFaultLightCluePOS)){
            list.addAll(ttFaultLightCluePOS);
        }
        if(CollectionUtils.isNotEmpty(voidedOrderList)){
            list.addAll(voidedOrderList);
        }
        return list;
    }

    @Override
    public void faultLightOrderCorrelationV4() {
        log.info("faultLightOrderCorrelation 4.0 代客预约, 故障灯关联工单start");
        // 1、查询线索状态为待进店且有预约单的数据
        List<FaultLightClueDTO> faultLightCluePOs =
                ttFaultLightClueMapper.selectClueInfoListV4(FaultClueStateEnum.WAITING_ENTER_STORE.getCode());
        if (CollectionUtils.isEmpty(faultLightCluePOs)) {
            log.info("CollectionUtils.isEmpty(faultLightCluePOs)");
            return;
        }

        // 2、表tt_repair_order中查询对应的工单号
        List<TtFaultLightCluePO> updateTtFaultLightCluePOList = new ArrayList<>();
        List<TtFaultLightInvitationPO> lightInvitationList = new ArrayList<>();
        List<TtFaultLightFollowRecordPO> lightFollowRecordPOList = new ArrayList<>();
        for (FaultLightClueDTO faultLightClueDTO : faultLightCluePOs) {
            String bookingOrderNo = faultLightClueDTO.getBookingOrderNo();
            String dealerCode = faultLightClueDTO.getDealerCode();

            // 通过预约单号、经销商查询工单号
            BookingOrderInfoVO bookingOrderInfoVO = repairCommonClient.queryByBookingOrderNoAndDealerCode(bookingOrderNo, dealerCode);
            log.info("closeClueOnCancelledBookingOrder,bookingOrderInfoVO:{}",bookingOrderInfoVO);
            // 如果存在更新数据
            Optional.ofNullable(bookingOrderInfoVO).ifPresent(bookingOrderInfoVO1 -> {
                log.info("closeClueOnCancelledBookingOrder,bookingOrderInfoVO1:{}",bookingOrderInfoVO1);
                // 更新线索状态为已进店、跟进状态为已进店
                TtFaultLightCluePO ttFaultLightCluePO = new TtFaultLightCluePO();
                BeanUtil.copyProperties(faultLightClueDTO, ttFaultLightCluePO);
                ttFaultLightCluePO.setRoNo(bookingOrderInfoVO1.getRoNo());
                ttFaultLightCluePO.setRoType(bookingOrderInfoVO1.getRoType());
                ttFaultLightCluePO.setRoStartTime(bookingOrderInfoVO1.getCreatedAt());
                ttFaultLightCluePO.setClueStatus(FaultClueStateEnum.ENTERED_STORE.getCode());
                ttFaultLightCluePO.setFollowStatus(FaultFollowStateEnum.ENTERED_STORE.getCode());

                updateTtFaultLightCluePOList.add(ttFaultLightCluePO);

                // 修改预约记录表成功进店后的参数更新
                TtFaultLightInvitationPO ttFaultLightInvitationPO = new TtFaultLightInvitationPO();
                ttFaultLightInvitationPO.setClueId(ttFaultLightCluePO.getId());
                Date createdAt = bookingOrderInfoVO1.getCreatedAt();
                ttFaultLightInvitationPO.setIntoTime(createdAt);
                ttFaultLightInvitationPO.setIntoOnTime(getIntoOnTime(createdAt, ttFaultLightCluePO.getForecastTime()));
                ttFaultLightInvitationPO.setNoInto(FaultClueWheResEnum.WHE_RES_ONE.getCode());
                lightInvitationList.add(ttFaultLightInvitationPO);

                // 保存跟进记录详情表
                lightFollowRecordPOList.add(converClueToRecord(ttFaultLightCluePO));
            });

        }

        // 3、更新数据库
        if (!updateTtFaultLightCluePOList.isEmpty()) {
            Lists.partition(updateTtFaultLightCluePOList, 1000).forEach(ttFaultLightClueMapper::batchUpdate);//跟进
            Lists.partition(lightFollowRecordPOList, 1000).forEach(ttFaultLightFollowRecordMapper::batchInsert);
        }

        if (!lightInvitationList.isEmpty()) {
            // 修改预约记录表状态
            Lists.partition(lightInvitationList, 1000).forEach(ttFaultLightInvitationMapper::updateNoIntoById);
        }

        // 4、同步给crm
        updateTtFaultLightCluePOList.forEach(item -> {
            log.info("faultLightOrderCorrelation, 故障灯关联工单MQ消息推送");
            this.pushMessage(item.getIcmId(), String.valueOf(item.getFollowStatus()), String.valueOf(item.getClueStatus()), new Date());
        });
        log.info("faultLightOrderCorrelation 4.0 代客预约, 故障灯关联工单end");
    }

    @Override
    public void faultLightOrderCorrelationV5() {
        log.info("faultLightOrderCorrelationV5 代客预约, 故障灯关联工单补偿逻辑start");
        // 1、查询线索状态为已进店且有预约单，有工单的数据
        List<BookingOrderInfoVO> faultLightCluePOs =
                ttFaultLightClueMapper.selectClueInfoListV5(FaultClueStateEnum.ENTERED_STORE.getCode());
        if (CollectionUtils.isEmpty(faultLightCluePOs)) {
            log.info("故障灯关联工单补偿逻,faultLightCluePOs.isEmpty");
            return;
        }

        // 2、筛选需要调整工单的数据
        List<BookingOrderInfoVO> bookingOrderInfoVOS = repairCommonClient.queryByBookingOrderNoAndRoNo(faultLightCluePOs);
        if (CollectionUtils.isEmpty(bookingOrderInfoVOS)) {
            log.info("故障灯关联工单补偿逻,bookingOrderInfoVOS.isEmpty");
            return;
        }

        List<TtFaultLightCluePO> updateTtFaultLightCluePOList = new ArrayList<>();
        bookingOrderInfoVOS.forEach(bookingOrderInfoVO ->{
            log.info("bookingOrderInfoVO,bookingOrderInfoVO:{}",bookingOrderInfoVO);
            // 更新线索状态为已进店、跟进状态为已进店
            TtFaultLightCluePO ttFaultLightCluePO = new TtFaultLightCluePO();
            ttFaultLightCluePO.setRoNo(bookingOrderInfoVO.getRoNo());
            ttFaultLightCluePO.setRoType(bookingOrderInfoVO.getRoType());
            ttFaultLightCluePO.setRoStartTime(bookingOrderInfoVO.getCreatedAt());
            ttFaultLightCluePO.setId(bookingOrderInfoVO.getId());
            updateTtFaultLightCluePOList.add(ttFaultLightCluePO);
        });

        // 3、更新数据库
        if (!updateTtFaultLightCluePOList.isEmpty()) {
            Lists.partition(updateTtFaultLightCluePOList, 1000).forEach(ttFaultLightClueMapper::batchUpdate);//跟进
        }
        log.info("faultLightOrderCorrelationV5 代客预约, 故障灯关联工单补偿逻辑end");
    }

    @Override
    public List<DtcCodeDTO> selectNameByEcuDtc(List<String> ecuDtc) {
        return dtcCodeMapper.selectNameByEcuDtc(ecuDtc);
    }

    /**
     * 预约单取消时关联线索关闭
     */
    @Override
    public void closeClueOnCancelledBookingOrder() {
        log.info("closeClueOnCancelledBookingOrder E故障灯4.0-400代客预约, 预约单取消时关联线索关闭start");
        // 1、查询所有具有预约单号且尚未完成的线索
        // 完成的线索状态
        List<Integer> finishClueStatusList = Arrays.asList(FaultClueStateEnum.ALREADY_COMPLETED.getCode(),
                FaultClueStateEnum.CLOSE_CLUES.getCode(),
                FaultClueStateEnum.ENTERING_STORE_NATURALLY.getCode());
        List<FaultLightClueDTO> faultLightClueDTOs =
                ttFaultLightClueMapper.selectBookingClueInfoListWithoutStatus(finishClueStatusList);
        if (CollectionUtils.isEmpty(faultLightClueDTOs)) {
            log.info("CollectionUtils.isEmpty(faultLightClueDTOs)");
            return;
        }

        // 2、根据预约单号查询tt_booking_order表，检查该预约单的状态
        List<TtFaultLightCluePO> updateTtFaultLightCluePOList = new ArrayList<>();
        List<TtFaultLightFollowRecordPO> lightFollowRecordPOList = new ArrayList<>();
        for (FaultLightClueDTO faultLightClueDTO : faultLightClueDTOs) {
            String bookingOrderNo = faultLightClueDTO.getBookingOrderNo();
            String dealerCode = faultLightClueDTO.getDealerCode();

            // 通过预约单号查询工单号预约单状态
            Integer bookingOrderStatus = repairCommonClient.queryBookingOrderStatus(bookingOrderNo, dealerCode);
            log.info("closeClueOnCancelledBookingOrder,bookingOrderStatus:{}",bookingOrderStatus);
            // 如果存在更新数据
            Optional.ofNullable(bookingOrderStatus)
                    .filter(statusCode ->
                            BookingOrderStatusEnum.CANCEL_ENTER_FACTORY.getCode().equals(statusCode)
                            ||
                            BookingOrderStatusEnum.TIMEOUT_CANCEL.getCode().equals(statusCode))
                    .ifPresent(statusCode -> {
                TtFaultLightCluePO ttFaultLightCluePO = new TtFaultLightCluePO();
                BeanUtil.copyProperties(faultLightClueDTO, ttFaultLightCluePO);
                // 如果预约单状态是 取消进厂 或者 超时取消，则将线索状态更新为已关闭，跟新状态更新为 预约取消
                ttFaultLightCluePO.setClueStatus(FaultClueStateEnum.CLOSE_CLUES.getCode());
                ttFaultLightCluePO.setFollowStatus(FaultFollowStateEnum.BOOKING_CANCEL.getCode());
                updateTtFaultLightCluePOList.add(ttFaultLightCluePO);

                // 保存跟进记录详情表
                lightFollowRecordPOList.add(converClueToRecord(ttFaultLightCluePO));
            });
        }

        // 3、更新数据
        if (!updateTtFaultLightCluePOList.isEmpty()) {
            Lists.partition(updateTtFaultLightCluePOList, 1000).forEach(ttFaultLightClueMapper::batchUpdate);//跟进
            Lists.partition(lightFollowRecordPOList, 1000).forEach(ttFaultLightFollowRecordMapper::batchInsert);
        }

        // 4、同步给crm
        updateTtFaultLightCluePOList.forEach(item -> {
            log.info("faultLightOrderCorrelation, 故障灯关联工单MQ消息推送");
            this.pushMessage(item.getIcmId(), String.valueOf(item.getFollowStatus()), String.valueOf(item.getClueStatus()), new Date());
        });
        log.info("closeClueOnCancelledBookingOrder E故障灯4.0-400代客预约, 预约单取消时关联线索关闭end");
    }

    @Override
    public int getIntoOnTime(Date date, Date forecastTime){
        return DateUtils.daysDateBetween(date, forecastTime) != 0 ? 1 : 2;
    }

    @Override
    public void faultLightStatusChange() {

        log.info("faultLightStatusChange,start");
        //故障灯-线索自动进入待验证
        List<TtFaultLightCluePO> faultLightClueChange = getFaultLightClueChange();
        if (CollectionUtils.isEmpty(faultLightClueChange)) {
            log.info("faultLightStatusChange,end");
            return;
        }
        List<TtFaultLightCluePO> ttFaultLightCluePOS = repairCommonClient.repairOrderChecker(faultLightClueChange);
        log.info("ttFaultLightCluePOS:{}", JSON.toJSONString(ttFaultLightCluePOS));
        if (CollectionUtils.isEmpty(ttFaultLightCluePOS)) {
            log.info("faultLightStatusChange,end");
            return;
        }
        List<TtFaultLightCluePO> faultLightClueList = queryFaultLightClueList(ttFaultLightCluePOS);
        log.info("ttFaultLightCluePOS:{}", JSON.toJSONString(faultLightClueList));
        if (CollectionUtils.isEmpty(faultLightClueList)){
            return;
        }
        ttFaultLightClueMapper.batchUpdates(faultLightClueList);
        this.publishEvent(faultLightClueList);

    }

    private List<TtFaultLightCluePO> queryFaultLightClueList(List<TtFaultLightCluePO> faultLightClueChange) {
        List<TtFaultLightCluePO> faultLightClueList = new ArrayList<>();
        faultLightClueChange.forEach(tl -> {
            TtFaultLightCluePO ttFaultLightCluePO = submitSettlementRemind(tl);
            if (ObjectUtils.isNotEmpty(ttFaultLightCluePO)) {
                faultLightClueList.add(ttFaultLightCluePO);
            }
        });
        return faultLightClueList;
    }

    @Override
    public void faultLightStatusRenovate() {
        log.info("faultLightStatusRenovate,start");
        //故障灯-线索自动进入待验证
        List<TtFaultLightCluePO> ttFaultLightCluePOS = getFaultLightStatusRenovate();
        if (CollectionUtils.isEmpty(ttFaultLightCluePOS)) {
            log.info("ttFaultLightCluePOS,isnull");
            return;
        };
        List<TtFaultLightCluePO> faultLightClueList = queryFaultLightClueList(ttFaultLightCluePOS);
        ttFaultLightClueMapper.batchUpdateRoAmount(faultLightClueList);
        log.info("faultLightStatusRenovate,end");
    }

    @Override
    public void updateForecastTime() {
        log.info("updateForecastTime,start");
        //故障灯-线索自动进入待验证
        List<BookingOrderInfoVO> faultLightCluePOS = getUpdateForecastTime();
        if (CollectionUtils.isEmpty(faultLightCluePOS)) {
            log.info("faultLightCluePOS,isnull");
            return;
        };
        List<FaultLightBookingRecordDto> faultLightClueList = queryForecastTimeList(faultLightCluePOS);
        if (CollectionUtils.isNotEmpty(faultLightClueList)){
            ttFaultLightClueMapper.updateForecastTime(faultLightClueList);
        }
        log.info("updateForecastTime,end");
    }

    private List<FaultLightBookingRecordDto> queryForecastTimeList(List<BookingOrderInfoVO> faultLightCluePOS) {
        List<FaultLightBookingRecordDto> faultLightBookingRecordDTOS = new ArrayList<>();
        List<BookingOrderInfoVO> bookingCreateParamsVos = new ArrayList<>();
        updateForecastTime(faultLightCluePOS,bookingCreateParamsVos);

        log.info("将 bookingCreateParamsVos 转换为 Map<String, String>");
        // 将 bookingCreateParamsVos 转换为 Map<String, String>，键为 bookingOrderNo + dealerCode，值为 forecastTime
        Map<String, Date> bookingMap = bookingCreateParamsVos.stream()
                .collect(Collectors.toMap(
                        vo -> vo.getBookingOrderNo() + vo.getOwnerCode(),
                        BookingOrderInfoVO::getBookingComeTime
                ));

        log.info("遍历 faultLightCluePOS，并根据 bookingOrderNo 和 dealerCode 设置 forecastTime");
        // 遍历 faultLightCluePOS，并根据 bookingOrderNo 和 dealerCode 设置 forecastTime
        faultLightCluePOS.forEach(po -> {
            String key = po.getBookingOrderNo() + po.getOwnerCode();
            if (bookingMap.containsKey(key)) {
                faultLightBookingRecordDTOS.add(FaultLightBookingRecordDto.builder().icmId(po.getIcmId()).forecastTime(bookingMap.get(key)).build());
            }
        });

        return faultLightBookingRecordDTOS;
    }

    private void updateForecastTime(List<BookingOrderInfoVO> faultLightCluePOS, List<BookingOrderInfoVO> bookingCreateParamsVos) {
        // 按500个一组提取 bookingOrderNo 并进行分组
        log.info("按500个一组提取 bookingOrderNo 并进行分组");
        List<List<BookingOrderInfoVO>> groupedBookingOrderNos = IntStream.range(0, faultLightCluePOS.size())
                .boxed()
                .collect(Collectors.groupingBy(index -> index / 500))
                .values()
                .stream()
                .map(indices -> indices.stream()
                        .map(faultLightCluePOS::get)
                        .map(po -> {
                            BookingOrderInfoVO vo = new BookingOrderInfoVO(po.getBookingOrderNo(),po.getOwnerCode());
                            vo.setBookingOrderNo(po.getBookingOrderNo());
                            return vo;
                        })
                        .collect(Collectors.toList()))
                .collect(Collectors.toList());

        log.info("根据分组后groupedBookingOrderNos，查询预约单最新进厂时间，开始");
        groupedBookingOrderNos.forEach(tl -> {
            List<BookingOrderInfoVO> bookingCreateParamsVo = repairCommonClient.queryBookingOrder(tl);
            bookingCreateParamsVos.addAll(bookingCreateParamsVo);
        });
        log.info("根据分组后groupedBookingOrderNos，查询预约单最新进厂时间，结束");
    }

    private List<TtFaultLightCluePO> getFaultLightStatusRenovate() {
        return ttFaultLightClueMapper.getFaultLightStatusRenovate();
    }

    private List<BookingOrderInfoVO> getUpdateForecastTime() {
        return ttFaultLightClueMapper.getUpdateForecastTime();
    }

    TtFaultLightCluePO submitSettlementRemind(TtFaultLightCluePO uePO) {
        String dealerCode = uePO.getDealerCode();
        List<VehicleOwnerVO> listVo = repairCommonClient.queryRepairOrder(dealerCode, uePO.getVin(), uePO.getRoNo());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(listVo)) {
            log.info("CollectionUtils.isEmpty(vo),工单号有误");
            return null;
        }
        log.info("doFollowSub,工单,listVo:{}", listVo.size());
        VehicleOwnerVO vo = listVo.get(0);
        log.info("doFollowSub,工单,vo:{}", vo);
        //判断工单是否已交车80671008
        String roStatus = vo.getDeliveryTag();
        if(!"80011001".equals(roStatus)){
            log.info("doFollowSub,!80011001.equals(roStatus),工单未交车");
            return null;
        }
        //工单结算时间
        uePO.setRoEndTime(vo.getForBalanceTime());
        //工单金额
        PrintParamVo paramsVo = new PrintParamVo();
        paramsVo.setOwnerCode(dealerCode);
        paramsVo.setPaySub(vo.getSubObbAmount());
        paramsVo.setPayYz(vo.getYzAmaount());
        paramsVo.setPrintType("BO");
        paramsVo.setPayObj(vo.getPaymentObjectCode());
        paramsVo.setPrintRegion("标准");
        paramsVo.setRepairGroupNoList(Arrays.asList(vo.getRoNo()));
        List<String> listNo = new ArrayList<>();
        String balanceNo;
        for (VehicleOwnerVO ownerVO : listVo) {
            balanceNo = ownerVO.getBalanceNo();
            if (!PAYMENT_OBJECT_CODE.equals(balanceNo)) {
                listNo.add(balanceNo);
            }
        }
        paramsVo.setBalanceGroupNoList(listNo);
        PrintDataVo printDataVo = repairCommonClient.balancePrintData(paramsVo);
        if (ObjectUtils.isEmpty(printDataVo)) {
            log.info("doFollowSub,ObjectUtils.isEmpty(printDataVo),工单号有误");
            return null;
        }
        String amountForDiscount = printDataVo.getAmountForDiscount();
        log.info("doFollowSub,printDataVo:{}", amountForDiscount);
        uePO.setRoAmount(amountForDiscount);
        return uePO;
    }


    @Override
    public int submitSettlementRemind(String dealerCode, String vin) {
        int flag = 10041002;
        int i = ttFaultLightClueMapper.selectClueStatus(dealerCode,vin);
        if(i>0){
            flag = 10041001;
        }
        return flag;
    }

    @Override
    public List<DiagnosisRecord> queryCluesDiagnosticInfo(Long cluesId, String cluesType) {
        String result;
        Map<String, Object> params = new HashMap<>();
        params.put("leads_id", cluesId);
        params.put("leads_type", cluesType);
        try {
            log.info("hc invoker remote interface origin request params: {}", queryDtcDiagnosisInfoUrl + "-" + JSON.toJSONString(params));
            result = HttpClientUtil.doGet(queryDtcDiagnosisInfoUrl, params, null);
            if (StringUtils.isNullOrEmpty(result)) {
                throw new ServiceBizException("【湖仓】查询DTC诊断信息接口返回为空");
            }
            log.info("hc invoker remote interface origin result: {}", result);
        } catch (Exception e) {
            log.error("hc invoker remote interface query clues diagnostic info exception: ", e);
            throw new ServiceBizException("【湖仓】通过线索ID查询DTC诊断信息接口异常");
        }

        OriginDtcDiagnosisResult diagnosisResult = JSONObject.parseObject(result, OriginDtcDiagnosisResult.class);
        if (!ResponseCdpDTO.SUCCESS.equals(diagnosisResult.getRetCode())) {
            if ("10001".equals(diagnosisResult.getRetCode())) {
                throw new ServiceBizException("调用胡仓接口查询DTC诊断信息参数错误: " + diagnosisResult.getRetInfo());
            } else {
                throw new ServiceBizException("调用胡仓接口查询DTC诊断信息失败: " + diagnosisResult.getRetInfo());
            }
        }
        return parseDtcDiagnosisInfo(result);
    }

    @Override
    public void handleDimClues(int pageSize) {
        log.info("handle dim clues start");
        String createdAt = LocalDate.now().minusDays(CommonConstants.NUM_3).toString();
        Integer count = cluesDiagnosticInfoRelationService.queryDiagnosticInfoRelationCount(createdAt, CommonConstants.NUM_0);
        if(count == 0) {
            log.info("there is no data to process");
            return;
        }
        int pageCount = (int) Math.ceil((double) count / pageSize);
        List<List<CluesDiagnosticInfoRelationPO>> batchList = new ArrayList<>();
        for (int i = 0; i < pageCount; i++) {
            List<CluesDiagnosticInfoRelationPO> relationPOList = cluesDiagnosticInfoRelationService
                    .queryDiagnosticInfoRelationList(i * pageSize, pageSize, createdAt, CommonConstants.NUM_0);
            if (CollectionUtils.isEmpty(relationPOList)) {
                continue;
            }
            batchList.add(relationPOList);
        }
        if (!CollectionUtils.isEmpty(batchList)) {
            log.info("handle dim clues batch query relation list size: {}", batchList.size());
            log.info("handle dim clues batch query relation list first data: {}", JSON.toJSONString(batchList.get(0)));
            batchList.forEach(v -> {
                HandleDimCluesDto handleDimCluesDto = cluesDiagnosticInfoRelationService.recordNumberOfExecutions(v);
                if (!Objects.isNull(handleDimCluesDto)) {
                    log.info("mark is display diagnostic info clues cluePrimaryIdList size: {}", CollectionUtils.isNotEmpty(handleDimCluesDto.getCluePrimaryIdList()) ? handleDimCluesDto.getCluePrimaryIdList().size() : 0);
                    log.info("mark is display diagnostic info clues leadsPreprocessingIds size: {}", CollectionUtils.isNotEmpty(handleDimCluesDto.getLeadsPreprocessingIds()) ? handleDimCluesDto.getLeadsPreprocessingIds().size() : 0);
                    log.info("mark is display diagnostic info request params: {}", JSON.toJSONString(handleDimCluesDto));
                    cluesDiagnosticInfoRelationService.markIsDisplayDiagnosticInfo(handleDimCluesDto);
                }
            });
        }
        log.info("handle dim clues end");
    }

    @Override
    public List<RecordsDTO> queryCluesWarningInfo(WarningDto warningDto) {
        log.info("queryCluesWarningInfo,warningDto: {}", warningDto);
        List<RecordsDTO> recordsDTOS = selectWarningDTC(warningDto);
        log.info("queryCluesWarningInfo,result: {}", recordsDTOS);
        return recordsDTOS;
    }

    private List<TtFaultLightCluePO> getFaultLightClueChange() {
        return ttFaultLightClueMapper.getFaultLightClueChange();
    }

    private void publishEvent(List<TtFaultLightCluePO> list) {
        if(org.apache.commons.collections.CollectionUtils.isEmpty(list)){
            return;
        }
        Optional<List<FaultLightTopicDTO>> optionalListDTO = Optional.ofNullable(convertRecordToDTO(list));
        optionalListDTO.ifPresent(listDTO -> eventPublisher.publishEvent(new LeadStatusChangeEvent(listDTO)));
    }


    public List<FaultLightTopicDTO> convertRecordToDTO(List<TtFaultLightCluePO> list) {
        if(org.apache.commons.collections.CollectionUtils.isEmpty(list)){
            return null;
        }
        List<FaultLightTopicDTO> dtoList = new ArrayList<>(list.size());
        FaultLightTopicDTO dto;
        Date date = new Date();
        String followUpStatus;
        String bizStatus;
        for (TtFaultLightCluePO record : list) {
            followUpStatus = record.getFollowStatus() == null ? null : record.getFollowStatus().toString();
            bizStatus = record.getClueStatus() == null ? null : record.getClueStatus().toString();
            dto = new FaultLightTopicDTO();
            dto.setUpdateTime(date);
            dto.setId(record.getIcmId());
            dto.setFollowUpStatus(followUpStatus);
            dto.setBizStatus( bizStatus);
            dtoList.add(dto);
        }
        return dtoList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doPendingVerificationClue() {

        List<TtFaultLightCluePO> listUp = new ArrayList<>();
        List<TtFaultLightFollowRecordPO> listLf = new ArrayList<>();

        faultLightClueDimorDtc(listUp, listLf,"DIM");
        faultLightClueDimorDtc(listUp, listLf,"DTC");

        if (CollectionUtils.isNotEmpty(listUp)) {
            log.info("doPendingVerificationClue,listUp:{}", listUp.size());
            Lists.partition(listUp, 1000).forEach(ttFaultLightClueMapper::batchUpdate);
            Lists.partition(listLf, 1000).forEach(ttFaultLightFollowRecordMapper::batchInsert);
        }
        //MQ消息推送
        listUp.forEach(cp -> {
            log.info("doPendingVerificationClue,故障灯关联工单MQ消息推送");
            this.pushMessage(cp.getIcmId(), String.valueOf(cp.getFollowStatus()), String.valueOf(cp.getClueStatus()), new Date());
        });
        log.info("doPendingVerificationClue,end");
    }

    private void faultLightClueDimorDtc(List<TtFaultLightCluePO> listUp, List<TtFaultLightFollowRecordPO> listLf , String sourceType) {
        log.info("doPendingVerificationClue,faultLightClueDim,start");
        List<FaultLightOnlineOfflineDTO> poList = new ArrayList<>();
        //查询所有待验证线索  时间: start - 维修完成时间, end - 维修完成时间+30D
        int totalRows = ttFaultLightClueMapper.selectPoByClueStatusCount(FaultClueStateEnum.WAITING_FOR_VERIFICATION.getCode(),sourceType);
        int pageSize = 500;
        log.info("总行数totalRows={},每页数量pageSize={}", totalRows, pageSize);
        if (totalRows < 1) {
            log.info("doPendingVerificationClue,end");
            return ;
        }
        Page cdpTagTask = new Page();
        cdpTagTask.setSize(pageSize);
        cdpTagTask.setTotal(totalRows);
        int page = Integer.parseInt(String.valueOf(cdpTagTask.getPages()));
        log.info("总条数={}，page={}", totalRows, page);
        // 依据页码创建任务数量
        for (int i = 1; i <= page; i++) {
            final Page pagination = new Page(i, pageSize);
            List<FaultLightOnlineOfflineDTO> faultLightOnlineOfflineDTOS = ttFaultLightClueMapper.selectPoByClueStatusPage(pagination, FaultClueStateEnum.WAITING_FOR_VERIFICATION.getCode(),sourceType);
            poList.addAll(faultLightOnlineOfflineDTOS);
        }
        if(CollectionUtils.isEmpty(poList)){
            log.info("未从数据库中获取该车对应故障触发时间,vehicle_vin:{}", poList);
            return ;
        }
        log.info("doPendingVerificationClue,size:{}", poList.size());
        int count = 3;
        Map<String, List<FaultLightOnlineOfflineDTO>> map = poList.stream()
                .collect(Collectors.groupingBy(
                        FaultLightOnlineOfflineDTO::getVin,
                        LinkedHashMap::new, // 保持插入顺序
                        Collectors.collectingAndThen(Collectors.toList(), list -> list.subList(0, Math.min(list.size(), count)))
                ))
                .entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        log.info("doPendingVerificationClue,Map:{}", map);
        /**线索状态*/
        Integer clueStatus;
        /**跟进状态*/
        Integer followStatus;
        /**离店亮灯 0.无 1.是*/
        Integer lightsUp;
        for (Map.Entry<String, List<FaultLightOnlineOfflineDTO>> entry : map.entrySet()) {
            String vin = entry.getKey();
            log.info("doPendingVerificationClue,vin:{}", vin);
            List<FaultLightOnlineOfflineDTO> value = entry.getValue();
            int size = value.size();
            FaultLightOnlineOfflineDTO faultLightOnlineOfflineDTO = value.get(size - 1);
            log.info("doPendingVerificationClue,FaultLightOnlineOfflineDTO:{}", faultLightOnlineOfflineDTO);
            /**车辆VIN码*/
            TtFaultLightCluePO cluePO = converOnlineOffToClue(faultLightOnlineOfflineDTO);

            String vehicle_vin = faultLightOnlineOfflineDTO.getVin();
            log.info("doPendingVerificationClue,vin:{}", vehicle_vin);
            /**开始时间，故障触发时间*/
            Date repairComTime = faultLightOnlineOfflineDTO.getRepairComTime();
            String start_time = DateUtils.dateFormat(repairComTime, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS);
            /**结束时间，故障触发时间*/
            Date endTime = faultLightOnlineOfflineDTO.getSendTime();
            Date clueComTime = null;
            Date thirtyDay = DateUtils.getDateHourAfter(repairComTime, THIRTY_DAY);

            boolean b = new Date().compareTo(thirtyDay) > 0;

            //如果启动次数小于3次且工单完成到当前时间未到30天 就跳过状态变更
            if (!b && size< count) {
                continue;
            }

            //如果启动次数小于3次且工单完成到当前时间超过30天 则结束时间取第30天
            if(b && size< count){
                endTime = thirtyDay;
            }

            //判断是否有启动
            if (!StringUtils.isNullOrEmpty(endTime)) {
                /**完成时间**/
                String end_time = DateUtils.dateFormat(endTime, DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS);
                log.info("doPendingVerificationClue,start_time:{}, end_time:{}", start_time, end_time);
                //调用胡仓接口判断是否在指定时间内有故障信息
                boolean flag;
                try {
                    flag = getFaultInformation(vehicle_vin, start_time, end_time,sourceType);
                } catch (Exception e) {
                    log.info("doPendingVerificationClue,cluePO:{},doException:{}", faultLightOnlineOfflineDTO, e);
                    continue;
                }
                log.info("doPendingVerificationClue,flag:{}", flag);
                if (flag) {
                    log.info("doPendingVerificationClue,abnormal");
                    //有则状态转变待确认
                    clueStatus = FaultClueStateEnum.WAITING_FOR_CONFIRMATION.getCode();
                    followStatus = FaultFollowStateEnum.WAITING_FOR_CONFIRMATION.getCode();
                    lightsUp = FaultClueWheResEnum.WHE_RES_TWO.getCode();
                    /*有责无责状态添加一个中间状态*/
                    cluePO.setWheRes(FaultClueWheResEnum.WHE_RES_THREE.getCode());
                } else {
                    log.info("doPendingVerificationClue,new Date().compareTo(endTime) > 0");
                    //没有故障信息且到期则状态转变已完成
                    clueStatus = FaultClueStateEnum.ALREADY_COMPLETED.getCode();
                    followStatus = FaultFollowStateEnum.ALREADY_COMPLETED.getCode();
                    clueComTime = new Date();
                    lightsUp = FaultClueWheResEnum.WHE_RES_ONE.getCode();
                }
            }else if (b) {
                log.info("doPendingVerificationClue,new Date().compareTo(endTime) > 0");
                //没有故障信息且到期则状态转变已完成
                clueStatus = FaultClueStateEnum.ALREADY_COMPLETED.getCode();
                followStatus = FaultFollowStateEnum.ALREADY_COMPLETED.getCode();
                clueComTime = new Date();
                lightsUp = FaultClueWheResEnum.WHE_RES_ONE.getCode();
            } else {
                log.info("doPendingVerificationClue,continue");
                continue;
            }
            cluePO.setClueStatus(clueStatus);
            cluePO.setFollowStatus(followStatus);
            cluePO.setLightsUp(lightsUp);
            cluePO.setClueComTime(clueComTime);
            listUp.add(cluePO);
            listLf.add(converClueToRecord(cluePO));
        }
    }

    private boolean getFaultInformation(String vehicle_vin, String start_time, String end_time,String warning_type) {
        int pageNum = 1;
        int pageSize = 100;
        List<RecordsDTO> list;
        boolean flag = Boolean.FALSE;
        while (true) {
            if (DTC.equals(warning_type)){
                WarningDto warningDto = new WarningDto();
                warningDto.setWarning_type(DTC);
                warningDto.setPageNum(pageNum);
                warningDto.setPageSize(pageSize);
                warningDto.setVinList(Collections.singletonList(new WarningDto.vinListInfo(vehicle_vin, start_time, end_time)));
                list = selectWarningDTC(warningDto);
                if (CollectionUtils.isEmpty(list)) {
                    break;
                }
                flag = true;
                break;
            }else {
                list = selectWarning(vehicle_vin, start_time, end_time, pageNum, pageSize);

                if (CollectionUtils.isEmpty(list)) {
                    break;
                } else {
                    List<Long> ids = list.stream().map(recordsDTO -> Long.valueOf(recordsDTO.getWarning_id())).collect(Collectors.toList());
                    //去重
                    ids = ids.stream().distinct().collect(Collectors.toList());
                    //查询是否在白名单
                    flag = faultLightDisposeService.selectDutyStatusByIds(ids);
                    if (flag) {
                        break;
                    }
                    pageNum += 1;
                }
            }
        }
        return flag;
    }

    private List<RecordsDTO> selectWarning(String vehicle_vin, String start_time, String end_time, int pageNum, int pageSize) {
        log.info("getFaultInformation,vehicle_vin:{},start_time:{},end_time:{},pageNum:{},pageSize:{}",
                vehicle_vin, start_time, end_time, pageNum, pageSize);
        String requestUrl = cdpFault + "?vehicle_vin=%s&start_time=%s&end_time=%s&pageNum=%s&pageSize=%s";
        String url = String.format(requestUrl, vehicle_vin, start_time, end_time, pageNum, pageSize);
        log.info("getFaultInformation,url:{}", url);
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set(AUTHORIZATION, "Basic " + Base64Encoder.encode(ak + ":" + as));
        HttpEntity<Map> httpEntity = new HttpEntity<>(null, httpHeaders);
        ResponseEntity<WarningDTO> responseEntity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, WarningDTO.class);
        log.info("getFaultInformation,responseEntity:{}", responseEntity);
        List<RecordsDTO> records = null;
        //00000 成功
        if (ObjectUtils.isNotEmpty(responseEntity)) {
            WarningDTO warningDTO = responseEntity.getBody();
            if (ObjectUtils.isNotEmpty(warningDTO)) {
                String retCode = warningDTO.getRetCode();
                if (SUCCESS.equals(retCode)) {
                    RetResultDTO retResult = warningDTO.getRetResult();
                    records = retResult.getRecords();
                }
            }
        }
        log.info("responseEntity,end:{}");
        return records;
    }

    private List<RecordsDTO> selectWarningDTC(WarningDto warningDto) {
        log.info("selectWarningDTC,warningDto:{}",warningDto);
        String url = midUrlProperties.getDatain() + midUrlProperties.getWarning();;
        log.info("getFaultInformation,url:{}", url);
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set(AUTHORIZATION, "Basic " + Base64Encoder.encode(ak + ":" + as));
        HttpEntity<WarningDto> httpEntity = new HttpEntity<>(warningDto, httpHeaders);
        ResponseEntity<WarningDTO> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, WarningDTO.class);
        log.info("getFaultInformation,responseEntity:{}", responseEntity);
        List<RecordsDTO> records = null;
        //00000 成功
        if (ObjectUtils.isNotEmpty(responseEntity)) {
            WarningDTO warningDTO = responseEntity.getBody();
            if (ObjectUtils.isNotEmpty(warningDTO)) {
                String retCode = warningDTO.getRetCode();
                if (SUCCESS.equals(retCode)) {
                    RetResultDTO retResult = warningDTO.getRetResult();
                    records = retResult.getRecords();
                }
            }
        }
        log.info("responseEntity,end:{}");
        return records;
    }

    @Override
    public List<TtFaultLightClueDTO> queryDealerList(TtFaultLightClueDTO dto) {
        return ttFaultLightClueMapper.queryDealerList(dto);
    }

    @Override
    public void pushMessage(Long id, String followUpStatus, String bizStatus, Date updateTime) {
        log.info("pushMessage,start,id: {},followUpStatus: {},bizStatus: {},updateTime: {}", id, followUpStatus, bizStatus, updateTime);
        FaultLightTopicDTO dto = new FaultLightTopicDTO();
        dto.setBizStatus(bizStatus);
        dto.setFollowUpStatus(followUpStatus);
        dto.setId(id);
        dto.setUpdateTime(updateTime);
        this.push(dto);
    }

    @Override
    public TtFaultLightFollowRecordPO converClueToRecord(TtFaultLightCluePO ttFaultLightCluePO) {

        TtFaultLightFollowRecordPO lightFollowRecordPO =
                ClazzConverter.converterClass(ttFaultLightCluePO, TtFaultLightFollowRecordPO.class);
        if (lightFollowRecordPO != null) {
            lightFollowRecordPO.setClueId(ttFaultLightCluePO.getId());
            lightFollowRecordPO.setFollowName("系统");
            lightFollowRecordPO.setFollowTime(new Date());
        }
        return lightFollowRecordPO;
    }

    public TtFaultLightCluePO converOnlineOffToClue(FaultLightOnlineOfflineDTO faultLightOnlineOfflineDTO) {

        TtFaultLightCluePO ttFaultLightCluePO =
                ClazzConverter.converterClass(faultLightOnlineOfflineDTO, TtFaultLightCluePO.class);
        return ttFaultLightCluePO;
    }

    private void push(Object body) {
        try {
            rocketMQTemplate.asyncSend(faultLightTopic, new Message<String>() {
                @Override
                public String getPayload() {
                    return JSON.toJSONString(body);
                }

                @Override
                public MessageHeaders getHeaders() {
                    HashMap<String, Object> headers = Maps.newHashMap();
                    return new MessageHeaders(headers);
                }
            }, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("pushMessage,消息投递成功,result:{}", JSON.toJSONString(sendResult));
                }

                @Override
                public void onException(Throwable throwable) {
                    log.error("pushMessage,消息投递失败:{}", throwable);
                }
            });
        } catch (Exception e) {
            log.error("pushMessage,推送消息失败:{}", e);
        }
    }

    private String getMqDestination(String topic, String tag) {
        return org.apache.commons.lang3.StringUtils.join(Lists.newArrayList(topic, tag), ":");
    }

    private String operatorIf(SaCustomerNumberDTO saCustomerNumberDTO, String callId, SaWorkNumberPO rs) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        String operator = String.valueOf(CommonConstants.NEW_TELECOM_AXB);
        String userOrderId = workNumberServiceContext.getUserOrderId("FL", loginInfoDto.getOwnerCode(), saCustomerNumberDTO.getInviteId() + "", callId);
        log.info("operatorIf FL userOrderId:{}", userOrderId);
        Map<String, Object> registerResultMap = workNumberServiceContext.register(operator, callId,
                rs.getSaNumber(), rs.getWorkNumber(), saCustomerNumberDTO.getCusNumber(), userOrderId);
        boolean isSuccessOfRegister = workNumberServiceContext
                .isSuccess(operator, CommonConstants.WORK_NUMBER_MENTHODTYPE_REGISTER, registerResultMap);
        TtFaultCallRegisterPO ttFaultCallRegisterPO = new TtFaultCallRegisterPO();
        if (!isSuccessOfRegister) {
            String resMessage = registerResultMap.containsKey("message") ? registerResultMap.get("message").toString() : registerResultMap.get("msg").toString();
            throw new ServiceBizException(resMessage);
        } else {
            log.info("当前操作供应商==={}，工作号===={}，bindId==={}", operator,
                    rs.getWorkNumber(), ttFaultCallRegisterPO.getCallId());
            ttFaultCallRegisterPO.setCallId(callId);
            ttFaultCallRegisterPO.setSaId(saCustomerNumberDTO.getSaId());
            ttFaultCallRegisterPO.setInviteId(saCustomerNumberDTO.getInviteId());
            ttFaultCallRegisterPO.setCusName(saCustomerNumberDTO.getCusName());
            ttFaultCallRegisterPO.setCusNumber(saCustomerNumberDTO.getCusNumber());
            assert loginInfoDto != null;
            ttFaultCallRegisterPO.setDealerCode(loginInfoDto.getOwnerCode());
            ttFaultCallRegisterPO.setSaName(rs.getSaName());
            ttFaultCallRegisterPO.setSaNumber(rs.getSaNumber());
            ttFaultCallRegisterPO.setWorkNumber(rs.getWorkNumber());
            log.info("====呼叫登记保存信息InviteInsuranceVehicleCustomerNumberPO======{}", ttFaultCallRegisterPO);
            log.info("====loginInfo========={}", loginInfoDto);
            ttFaultCallRegisterMapper.insert(ttFaultCallRegisterPO);
        }
        return rs.getWorkNumber();
    }
    public List<DiagnosisRecord> parseDtcDiagnosisInfo(String result) {
        String replacedData = result.replace("/", " ");
        DtcDiagnosisInfoDTO info = JSONObject.parseObject(replacedData, DtcDiagnosisInfoDTO.class);
        DiagnosisRetResult retResult = info.getRetResult();
        if (Objects.isNull(retResult)) {
            return Collections.emptyList();
        }
        List<DiagnosisRecord> records = retResult.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        Map<String, String> dtcNameMap = queryDtcCodeList(records);
        records.forEach(v -> {
            Map<String, DiagnosisDtcInfo> dtcInfoMap = v.getDtcs();
            String ecu = v.getEcu();
            if (Objects.isNull(dtcInfoMap) || dtcInfoMap.isEmpty()) {
                return;
            }
            for (Map.Entry<String, DiagnosisDtcInfo> dtcInfoEntry : dtcInfoMap.entrySet()) {
                DiagnosisDtcInfo diagnosisDtcInfo = dtcInfoEntry.getValue();
                if (Objects.isNull(diagnosisDtcInfo)) {
                    diagnosisDtcInfo = new DiagnosisDtcInfo();
                }
                diagnosisDtcInfo.setDtcName(dtcNameMap.get(getDtcKey(ecu, dtcInfoEntry.getKey())));
                Map<String, DiagnosisSnapshotInfo> snapshots = diagnosisDtcInfo.getSnapshots();
                if (Objects.isNull(snapshots) || snapshots.isEmpty()) {
                    continue;
                }
                Map<String, String> snapshotsStrings = getSnapshotsStrings(snapshots);
                diagnosisDtcInfo.setSnapshotsStrings(snapshotsStrings);
                diagnosisDtcInfo.setSnapshots(null);

            }
        });
        return retResult.getRecords();
    }

    private String getDtcKey(String ecu, String dtc){
        return ecu + "-" + dtc;
    }

    private Map<String, String> getSnapshotsStrings(Map<String, DiagnosisSnapshotInfo> snapshots) {
        Map<String, String> snapshotsStrings = new HashMap<>();
        for (Map.Entry<String, DiagnosisSnapshotInfo> snapshotEntry : snapshots.entrySet()) {
            DiagnosisSnapshotInfo snapshotInfo = snapshotEntry.getValue();
            if (Objects.isNull(snapshotInfo)) {
                continue;
            }
            Map<String, String> strings = snapshotInfo.getStrings();
            snapshotsStrings.putAll(strings);
        }
        return snapshotsStrings;
    }

    private Map<String, String> queryDtcCodeList(List<DiagnosisRecord> records) {
        List<String> dtcList = new ArrayList<>();
        records.forEach(v -> {
            Map<String, DiagnosisDtcInfo> dtcMap = v.getDtcs();
            String ecu = v.getEcu();
            if (!Objects.isNull(dtcMap) && !dtcMap.isEmpty()) {
                dtcMap.keySet().stream()
                        .map(key -> getDtcKey(ecu, key))
                        .forEach(dtcList::add);
            }
        });
        log.info("queryDtcCodeList,dtcList:{}", dtcList);
        try {
            List<DtcCodeDTO> list = selectNameByEcuDtc(dtcList);
            if (CollectionUtils.isEmpty(list)){
                return Collections.emptyMap();
            }
            log.info("queryDtcCodeList,list:{}", list);
            return list.stream().filter(v -> Objects.nonNull(v) && !Objects.isNull(v.getDtcName()))
                    .collect(Collectors.toMap(DtcCodeDTO::getEcuDtc, DtcCodeDTO::getDtcName, (k1, k2) -> k2));
        } catch (Exception e) {
            log.error("queryDtcCodeList exception: ", e);
            return Collections.emptyMap();
        }
    }
}
