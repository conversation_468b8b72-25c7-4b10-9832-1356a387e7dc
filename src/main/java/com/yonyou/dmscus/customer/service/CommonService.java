package com.yonyou.dmscus.customer.service;

import java.util.List;

import com.yonyou.dmscus.customer.dto.*;

public interface CommonService {

	List<UserInfoOutDTO> getUserAllList(int type);

	List<UserInfoOutDTO> getUserList(OrgSearchParams orgSearchParams);

	String sendMail(EmailInfoDto emailInfoDto);

	String sendMessage(SmsPushDTO smsPushDTO);

	String sendGoodwillMail(EmailInfoDto emailInfoDto);

	List<OrgInfoDTO> getOrgInfo(OrgSearchParams orgSearchParams);

	List<UserInfoOutDTO> getUserByRoleCodeList(QueryDealerUser queryDealerUser);

}
