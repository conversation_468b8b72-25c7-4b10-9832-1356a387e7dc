package com.yonyou.dmscus.customer.service.impl.inviteManageVCDC;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.configuration.InnerUrlProperties;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.common.VehicleMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordDetailMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.CallDetailsMapper;
import com.yonyou.dmscus.customer.dto.DownloadDTO;
import com.yonyou.dmscus.customer.dto.InvitationDeleteParamDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.TmVehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleDto;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteManageVCDC.AllocateDealerDTO;
import com.yonyou.dmscus.customer.entity.po.common.VehiclePO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordDetailPO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.enums.InvitationTypeEnum;
import com.yonyou.dmscus.customer.feign.DmscloudServiceClient;
import com.yonyou.dmscus.customer.feign.DownloadClient;
import com.yonyou.dmscus.customer.feign.dto.CommonConfigDTO;
import com.yonyou.dmscus.customer.ossexcel.ExcelExportColumn;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.inviteManageVCDC.InviteManageVCDCService;
import com.yonyou.dmscus.customer.service.voicemanage.CallDetailsService;
import com.yonyou.dmscus.customer.util.common.ExcelGenerator;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.dmscus.customer.utils.DataConvertUtils;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class InviteManageVCDCServiceImpl implements InviteManageVCDCService {

    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteVehicleRecordMapper inviteVehicleRecordMapper;
    @Resource
    InviteVehicleRecordDetailMapper inviteVehicleRecordDetailMapper;
    @Autowired
    BusinessPlatformService businessPlatformService;
    @Resource
    VehicleMapper vehicleMapper;
    @Resource
    CallDetailsMapper callDetailsMapper;

    @Autowired
    CallDetailsService callDetailsService;
    
	@Resource
	private DownloadClient downloadClient;

    @Resource
    private InnerUrlProperties innerUrlProperties;
    
    @Autowired
    private DmscloudServiceClient dmscloudServiceClient;
    
    /**
     * @param page
     * @param inviteVehicleRecordDTO
     * @return
     */
    @Override
    public IPage<InviteVehicleDto> getInviteVehicleRecord(Page page, InviteVehicleRecordDTO
            inviteVehicleRecordDTO) {
        if (inviteVehicleRecordDTO == null) {
            inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        }
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordDTO.transDtoToPo(InviteVehicleRecordPO
                .class);
        if (!StringUtils.isNullOrEmpty(inviteVehicleRecordDTO.getAreaId()) ||
                !StringUtils.isNullOrEmpty(inviteVehicleRecordDTO.getLargeAreaId()) ||
                !StringUtils.isNullOrEmpty(inviteVehicleRecordDTO.getDealerCode())) {
            List<String> codes = businessPlatformService.getDealercodes(inviteVehicleRecordDTO.getAreaId(),
                    inviteVehicleRecordDTO.getLargeAreaId(), inviteVehicleRecordDTO.getDealerCode(), null);
            inviteVehicleRecordPO.setDealerCodes(codes);
            if (codes.size() == 0) {
                page.setRecords(new ArrayList<>());
                return page;
            }
        }
        inviteVehicleRecordPO.setIsMain(1);
        // 是否是零附件线索
        List<Integer> inviteTypeParam = inviteVehicleRecordPO.getInviteTypeParam();
        if (!org.springframework.util.CollectionUtils.isEmpty(inviteTypeParam)) {
            logger.info("!isEmpty inviteTypeParam");
            long count = inviteTypeParam.stream()
                    .filter(Objects::nonNull)
                    .filter(v -> Objects.equals(v.toString(), CommonConstants.INVITE_TYPE_XIII + ""))
                    .count();
            int size = inviteTypeParam.size();
            logger.info("size:{},count:{}", size, count);
            if (Objects.equals(size + "", count + "")) {
                logger.info("size = count");
                inviteVehicleRecordPO.setPartClue(true);
            }
        }
        List<InviteVehicleRecordDTO> recordList = inviteVehicleRecordMapper.selectInviteVehicleRecord(page,
                inviteVehicleRecordPO);
        // 2. 转换并处理属性
        List<InviteVehicleDto> resultList = new ArrayList<>(recordList.size());
        for (InviteVehicleRecordDTO recordDto : recordList) {
            InviteVehicleDto targetDto = new InviteVehicleDto();
            BeanUtils.copyProperties(recordDto, targetDto);
            Integer inviteType = targetDto.getInviteType();
            if (InvitationTypeEnum.OEM_CREATED.getCode().equals(inviteType)) {
                targetDto.setInviteName(targetDto.getItemName());
            }
            resultList.add(targetDto);
        }
        page.setRecords(resultList);
        return page;
    }


    /**
     * 重新分配经销商
     *
     * @param dto
     * @return
     */
    @Override
    public int allocateDealer(AllocateDealerDTO dto) {
        List<InviteVehicleRecordDTO> list = dto.getInviteRecordList();
        for (InviteVehicleRecordDTO item : list) {
            item.setDealerCode(dto.getDealerCode());
            InviteVehicleRecordPO po = item.transDtoToPo(InviteVehicleRecordPO.class);
            //更新经销商code
            inviteVehicleRecordMapper.updateById(po);
        }
        return 1;
    }

    /**
     * 导出查询
     *
     * @param inviteVehicleRecordDTO
     * @return
     */
    @Override
    public List<Map> exportExcelinviteVehicleRecord(InviteVehicleRecordDTO inviteVehicleRecordDTO) {
        if (inviteVehicleRecordDTO == null) {
            inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        }
        InviteVehicleRecordPO inviteVehicleRecordPO = inviteVehicleRecordDTO.transDtoToPo(InviteVehicleRecordPO
                .class);
        if (!StringUtils.isNullOrEmpty(inviteVehicleRecordDTO.getAreaId()) ||
                !StringUtils.isNullOrEmpty(inviteVehicleRecordDTO.getLargeAreaId()) ||
                !StringUtils.isNullOrEmpty(inviteVehicleRecordDTO.getDealerCode())) {
            List<String> codes = businessPlatformService.getDealercodes(inviteVehicleRecordDTO.getAreaId(),
                    inviteVehicleRecordDTO.getLargeAreaId(), inviteVehicleRecordDTO.getDealerCode(), null);
            inviteVehicleRecordPO.setDealerCodes(codes);
        }

        return inviteVehicleRecordMapper.exportExcelinviteVehicleRecord(
                inviteVehicleRecordPO);
    }

    /**
     * 查询邀约线索车辆信息和跟进明细
     *
     * @param vin
     * @param id
     * @return
     */
    @Override
    public InviteVehicleRecordDTO getInviteVehicleRecordInfo(String vin, Long id) {
        TmVehicleDTO dto = businessPlatformService.getVehicleByVIN(vin);
        VehiclePO veh = vehicleMapper.getVehicleByVin(vin);
        InviteVehicleRecordPO po = inviteVehicleRecordMapper.selectById(id);
        List<InviteVehicleRecordDetailPO> list = inviteVehicleRecordDetailMapper.getInviteVehicleRecordInfo(id);
        InviteVehicleRecordDTO rs = po.transPoToDto(InviteVehicleRecordDTO.class);
        if (CommonUtils.isNullOrEmpty(list)) {
            rs.setRecordDetailList(new ArrayList<InviteVehicleRecordDetailDTO>());
        } else {
            List<InviteVehicleRecordDetailDTO> result = list.stream().map(m -> m.transPoToDto
                    (InviteVehicleRecordDetailDTO
                            .class)
            ).collect(Collectors.toList());
            rs.setRecordDetailList(result);
        }
        if (dto != null) {
            //销售经销商
            rs.setDealerCode(dto.getDealerCode());
        }
        if (veh != null) {
            //日均里程
            rs.setDailyAverageMileage(veh.getDailyAverageMileage());
        }
        return rs;
    }

    /**
     * 店端查询跟进记录
     *
     * @param vin
     * @param id
     * @return
     */
    @Override
    public List<InviteVehicleRecordDetailDTO> getInviteVehicleRecordInfoDlr(String vin, Long id, String ownerCode) {
        List<InviteVehicleRecordDetailPO> list = inviteVehicleRecordDetailMapper.getInviteVehicleRecordInfoDlr(id, ownerCode);
        if (CommonUtils.isNullOrEmpty(list)) {
            return Collections.emptyList();
        } else {
            return list.stream().map(m -> m.transPoToDto
                    (InviteVehicleRecordDetailDTO.class)
            ).collect(Collectors.toList());
        }
    }


    @Override
    public IPage<InviteVehicleRecordDetailDTO> getInviteVehicleRecordInfoHistoryDlr(Page page, String vin, String ownerCode) {
        //获取登录用户信息
        List<InviteVehicleRecordDetailPO> list = inviteVehicleRecordDetailMapper.getInviteVehicleRecordInfoHistoryDlr
                (page, vin, ownerCode);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<InviteVehicleRecordDetailDTO>());
            return page;
        } else {
            List<InviteVehicleRecordDetailDTO> result = list.stream().map(m -> m.transPoToDto
                    (InviteVehicleRecordDetailDTO
                            .class)
            ).collect(Collectors.toList());
            page.setRecords(result);
            return page;
        }
    }

    @Override
    public void exportExcel(InviteVehicleRecordDTO dto, HttpServletResponse response) throws Exception {
        String fileName = "邀约跟进导出";
        String fn = java.net.URLEncoder.encode(fileName, "UTF-8");
        response.setCharacterEncoding("utf-8");

        OutputStream out;
        response.setContentType("Content-Type: application/vnd.ms-excel");
        response.setHeader("Content-disposition", "attachment; filename="
                + fn + ".xlsx");
        out = response.getOutputStream();

        //标题
        String[] titles = {"VIN", "车牌号", "邀约类型", "邀约名称", "返厂意向等级", "建议进厂日期", "上次进厂日期", "下次跟进日期", "实际跟进日期",
                "跟进人员", "跟进状态", "线索完成状态", "邀约经销商", "通话时间", "通话时长", "AI得分"
                , "上次SA", "线索完成时间", "完成经销商", "备注", "跟进内容", "失败原因", "未用AI原因", "线索异常状态",
                "是否二次跟进", "跟进记录次数", "首次AI跟进时间", "工单开单时间", "预约时间","线索类型","线索下发日期",
                "流失线索类型", "流失预警线索类型", "验证状态", "券码", "卡券名称","是否BEV"};//excel标题列表
        //对应字段
        String[] fields = {"vin", "licensePlateNum", "inviteTypeName", "inviteName", "returnIntentionLevel", "adviseInDate", "lastInDate", "planFollowDate",
                "actualFollowDate", "saName", "followStatusName", "orderStatusName", "dealerCode", "callTime",
                "callLength","totalScore", "lastSaName", "orderFinishDate","finishDealerCode","itemName", "content",
                "loseReason","itemCode","itemType","isAi","recordNum","aiAt","orderAt","recordAt","recordType",
                "createdAt","lossType", "lossWarningType", "verifyStatus","couponCode","couponName","bevFlag"};//导出到excel的属性列表
        Map<String, Object> paras = new HashMap<>();

        Map<String, DecimalFormat> numberFormats = null;//数字格式map
        Map<String, DateFormat> dateFormats = null;//日期格式ma
        ExcelGenerator<HashMap> excelGenerator = new ExcelGenerator<HashMap>(titles, fields, numberFormats,
                dateFormats);
        excelGenerator.initWorkbook(fileName, true);

        //查询数据（考虑到后台微服务的性能，目前最大导出条数支持：40W 条） 这里实现mybatis的 ResultHandler接口，逐条处理生成Excel的行，防止一次性生成大数量的List导致堆溢出
        if (dto == null) {
            dto = new InviteVehicleRecordDTO();
        }
        InviteVehicleRecordPO inviteVehicleRecordPO = dto.transDtoToPo(InviteVehicleRecordPO
                .class);
        if (!StringUtils.isNullOrEmpty(dto.getAreaId()) ||
                !StringUtils.isNullOrEmpty(dto.getLargeAreaId()) ||
                !StringUtils.isNullOrEmpty(dto.getDealerCode())) {
            List<String> codes = businessPlatformService.getDealercodes(dto.getAreaId(),
                    dto.getLargeAreaId(), dto.getDealerCode(), null);
            inviteVehicleRecordPO.setDealerCodes(codes);
        }
        inviteVehicleRecordPO.setIsMain(1);
        inviteVehicleRecordMapper.exportExcelinviteVehicle(inviteVehicleRecordPO, excelGenerator);

        Workbook wb = excelGenerator.getWorkbook();
        excelGenerator.writeAndClose(response, fileName, wb);
        if (wb instanceof SXSSFWorkbook) {
            ((SXSSFWorkbook) wb).dispose();
        }
    }


	@Override
	public void exportExcelByDown(InviteVehicleRecordDTO inviteVehicleRecordDTO) {
		
		List<ExcelExportColumn> exportColumnList = new ArrayList<>();
		exportColumnList.add(new ExcelExportColumn("vin","VIN"));
		exportColumnList.add(new ExcelExportColumn("licensePlateNum","车牌号"));
		exportColumnList.add(new ExcelExportColumn("inviteTypeName","邀约类型"));
		exportColumnList.add(new ExcelExportColumn("returnIntentionLevel","返厂意向等级"));
		exportColumnList.add(new ExcelExportColumn("adviseInDate","建议进厂日期"));
		exportColumnList.add(new ExcelExportColumn("lastInDate","上次进厂日期"));
		exportColumnList.add(new ExcelExportColumn("planFollowDate","下次跟进日期"));
		exportColumnList.add(new ExcelExportColumn("actualFollowDate","实际跟进日期"));
		exportColumnList.add(new ExcelExportColumn("saName","跟进人员"));
		exportColumnList.add(new ExcelExportColumn("followStatusName","跟进状态"));
		exportColumnList.add(new ExcelExportColumn("orderStatusName","线索完成状态"));
		exportColumnList.add(new ExcelExportColumn("dealerCode","邀约经销商"));
		exportColumnList.add(new ExcelExportColumn("callTime","通话时间"));
		exportColumnList.add(new ExcelExportColumn("callLength","通话时长"));
		exportColumnList.add(new ExcelExportColumn("totalScore","AI得分"));
		exportColumnList.add(new ExcelExportColumn("lastSaName","上次SA"));
		exportColumnList.add(new ExcelExportColumn("orderFinishDate","线索完成时间"));
		exportColumnList.add(new ExcelExportColumn("finishDealerCode","完成经销商"));
		exportColumnList.add(new ExcelExportColumn("itemName","备注"));
		exportColumnList.add(new ExcelExportColumn("content","跟进内容"));
		exportColumnList.add(new ExcelExportColumn("loseReason","失败原因"));
		exportColumnList.add(new ExcelExportColumn("itemCode","未用AI原因"));
		exportColumnList.add(new ExcelExportColumn("itemType","线索异常状态"));
		exportColumnList.add(new ExcelExportColumn("isAi","是否二次跟进"));
		exportColumnList.add(new ExcelExportColumn("recordNum","跟进记录次数"));
		exportColumnList.add(new ExcelExportColumn("aiAt","首次AI跟进时间"));
		exportColumnList.add(new ExcelExportColumn("orderAt","工单开单时间"));
		exportColumnList.add(new ExcelExportColumn("recordAt","预约时间"));
		exportColumnList.add(new ExcelExportColumn("recordType","线索类型"));
		exportColumnList.add(new ExcelExportColumn("createdAt","线索下发日期"));
		exportColumnList.add(new ExcelExportColumn("lossType","流失线索类型"));
		exportColumnList.add(new ExcelExportColumn("lossWarningType","流失预警线索类型"));
		exportColumnList.add(new ExcelExportColumn("verifyStatus","验证状态"));
		exportColumnList.add(new ExcelExportColumn("couponCode","券码"));
		exportColumnList.add(new ExcelExportColumn("couponName","卡券名称"));
		exportColumnList.add(new ExcelExportColumn("bevFlag","是否BEV"));
		
		DownloadDTO downloadDTO = new DownloadDTO();
		downloadDTO.setExcelExportColumnList(exportColumnList);
		
		Map<String, Object> maps = inviteVehicleRecordDTO.toMaps();
		
		Integer pSize = queryCommonConfig();
		inviteVehicleRecordDTO.getInviteType();
		maps.put("recordTypeParam", DataConvertUtils.integerListToString(inviteVehicleRecordDTO.getRecordTypeParam()));
		maps.put("inviteTypeParam", DataConvertUtils.integerListToString(inviteVehicleRecordDTO.getInviteTypeParam()));
		maps.put("followStatusParam", DataConvertUtils.integerListToString(inviteVehicleRecordDTO.getFollowStatusParam()));
		maps.put("orderStatusParam", DataConvertUtils.integerListToString(inviteVehicleRecordDTO.getOrderStatusParam()));
		maps.put("leaveIds", DataConvertUtils.integerListToString(inviteVehicleRecordDTO.getLeaveIds()));
		maps.put("pSize", pSize);
		downloadDTO.setQueryParams(maps);
		downloadDTO.setExcelName("邀约跟进导出.xlsx");
		downloadDTO.setSheetName("邀约跟进导出");
		downloadDTO.setServiceUrl(innerUrlProperties.getDownloadInviteManageVCDCUrl());
		logger.info("邀约跟进线索管理导出，调用下载中心传入参数：【{}】", JSONUtil.toJsonStr(downloadDTO));
		downloadClient.downloadConGoodwillStamps(downloadDTO);
		
	}
	
	private Integer queryCommonConfig() {
		try {				
			// 查询分页配置配置
			CommonConfigDTO commonConfig = dmscloudServiceClient.getConfigByKey("1_invitationLeadManagement", "exportDownloadCenterSwitchConfigure");
			logger.info("请求配置返回数据：{}", JSON.toJSONString(commonConfig));
			String configExt1 = commonConfig.getConfigExt1();
			if(!StringUtils.isNullOrEmpty(configExt1)) {
				return Integer.valueOf(configExt1);
			}
		} catch (Exception e) {
			logger.info("获取分页配置失败", e);
		}
		return 5000;
	}

	@Override
	public List<Map> downLoadExportExcel(InviteVehicleRecordDTO dto, Integer offset, Integer pSize) {
        if (dto == null) {
            dto = new InviteVehicleRecordDTO();
        }
        logger.info("邀约线索导出：{}", dto);
        InviteVehicleRecordPO inviteVehicleRecordPO = dto.transDtoToPo(InviteVehicleRecordPO
                .class);
        if (!StringUtils.isNullOrEmpty(dto.getAreaId()) ||
                !StringUtils.isNullOrEmpty(dto.getLargeAreaId()) ||
                !StringUtils.isNullOrEmpty(dto.getDealerCode())) {
            List<String> codes = businessPlatformService.getDealercodes(dto.getAreaId(),
                    dto.getLargeAreaId(), dto.getDealerCode(), null);
            inviteVehicleRecordPO.setDealerCodes(codes);
        }
        inviteVehicleRecordPO.setIsMain(1);
        List<Map> exportExcelinviteVehicle = inviteVehicleRecordMapper.exportExcelinviteVehicleDownlod(inviteVehicleRecordPO, offset, pSize);
        if(exportExcelinviteVehicle!=null) {
        	exportExcelinviteVehicle.stream().filter(Objects::nonNull).forEach(DataConvertUtils::mapDateFormat);
        	return exportExcelinviteVehicle;
        }
		return Collections.emptyList();
	}

    /**
     * 自建邀约线索删除功能
     */
    @Override
    public void deleteSelfCreateInvitation(InvitationDeleteParamDTO param) {
        //1. 前置校验、记录
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        logger.info("自建邀约线索删除功能入参:{}，当前操作用户:{}", JSON.toJSONString(param), JSON.toJSONString(loginInfoDto));
        if (ObjectUtil.isEmpty(param) || ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceBizException("参数不得为空");
        }
        InviteVehicleRecordPO po = inviteVehicleRecordMapper.selectById(param.getId());
        logger.info("自建邀约线索删除，PO查询结果:{}", JSON.toJSONString(po));
        if (ObjectUtil.isEmpty(po) || ObjectUtil.notEqual(po.getInviteType(), CommonConstants.INVITE_TYPE_X)) {
            throw new ServiceBizException("邀约线索，查询结果为空或非自建邀约线索");
        }
        //2. 更新删除
        po.setIsDeleted(true);
        inviteVehicleRecordMapper.updateById(po);
    }


}
