package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

    
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscloud.function.utils.common.StringUtils;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintAssistDepartmentMapper;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.*;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintFollowPO;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintFollowMapper;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoPO;
import com.yonyou.dmscus.customer.enums.CacheKeyEnum;
import com.yonyou.dmscus.customer.service.complaint.ComplaintAssistDepartmentService;
import com.yonyou.dmscus.customer.service.complaint.ComplaintFollowService;

import com.yonyou.dmscus.customer.service.complaint.ComplaintInfoService;
import com.yonyou.dmscus.customer.utils.RedisLockUtil.DistributedLockUtil;
import com.yonyou.dmscus.customer.utils.RedisLockUtil.IDistributedLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;




    /**
 * <p>
 * 客户投诉跟进表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Service
        public class ComplaintFollowServiceImpl implements ComplaintFollowService {
        /**
         * 日志说明
         */
        private final Logger logger = LoggerFactory.getLogger(this.getClass());
        @Resource
        ComplaintFollowMapper complaintFollowMapper;
        @Resource
        ComplaintInfoMapper complaintInfoMapper;
        @Resource
        ComplaintAssistDepartmentMapper complaintAssistDepartmentMapper;
        @Autowired
        ComplaintInfoService complaintInfoService;
        @Autowired
        ComplaintInfoServiceImpl complaintInfoServiceImpl;
        @Autowired
        ComplaintAssistDepartmentService complaintAssistDepartmentService;

        /**
         * 分页查询对应数据
         *
         * @param page               分页对象
         * @param complaintFollowDTO 查询条件
         * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintFollowDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public IPage<ComplaintFollowDTO> selectPageBysql(Page page, ComplaintFollowDTO complaintFollowDTO) {
            if (complaintFollowDTO == null) {
                complaintFollowDTO = new ComplaintFollowDTO();
            }
            ComplaintFollowPO complaintFollowPo = complaintFollowDTO.transDtoToPo(ComplaintFollowPO.class);

            List<ComplaintFollowPO> list = complaintFollowMapper.selectPageBySql(page, complaintFollowPo);
            if (CommonUtils.isNullOrEmpty(list)) {
                page.setRecords(new ArrayList<>());
                return page;
            } else {
                List<ComplaintFollowDTO> result = list.stream().map(m -> m.transPoToDto(ComplaintFollowDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param complaintFollowDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintFollowDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<ComplaintFollowDTO> selectListBySql(String flag,ComplaintFollowDTO complaintFollowDTO) {
            if (complaintFollowDTO == null) {
                complaintFollowDTO = new ComplaintFollowDTO();
            }
            ComplaintFollowPO complaintFollowPo = complaintFollowDTO.transDtoToPo(ComplaintFollowPO.class);
            List<ComplaintFollowPO> list=new ArrayList<>();

            String dealer="dealer";
            if(flag.equals(dealer)){
                list = complaintFollowMapper.selectListByDealer(complaintFollowPo);
            }else {
                list = complaintFollowMapper.selectListByVcdc(complaintFollowPo);
            }

            if (CommonUtils.isNullOrEmpty(list)) {
                return new ArrayList<>();
            } else {
                return list.stream().map(m -> m.transPoToDto(ComplaintFollowDTO.class)).collect(Collectors.toList());
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintFollowDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public ComplaintFollowDTO getById(Long id) {
            ComplaintFollowPO complaintFollowPo = complaintFollowMapper.selectById(id);
            if (complaintFollowPo != null) {
                return complaintFollowPo.transPoToDto(ComplaintFollowDTO.class);
            } else {
                throw new DALException("查询失败,没有符合条件的数据");
            }
        }

        /**
         * 根据DTO 进行数据新增
         *
         * @param complaintFollowDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(ComplaintFollowDTO complaintFollowDTO) {
            //对对象进行赋值操作
            ComplaintFollowPO complaintFollowPo = complaintFollowDTO.transDtoToPo(ComplaintFollowPO.class);
            //执行插入
            int row = complaintFollowMapper.insert(complaintFollowPo);
            //返回插入的值
            return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id                 主键ID
         * @param complaintFollowDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, ComplaintFollowDTO complaintFollowDTO) {
            ComplaintFollowPO complaintFollowPo = complaintFollowMapper.selectById(id);
            //对对象进行赋值操作
            complaintFollowDTO.transDtoToPo(complaintFollowPo);
            //执行更新
            int row = complaintFollowMapper.updateById(complaintFollowPo);
            return row;
        }

        /**
         * 根据ID 进行删除数据
         *
         * @param id
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteById(Long id) {
            int deleteCount = complaintFollowMapper.deleteById(id);
            logger.debug("删除数量：{}", deleteCount);
            if (deleteCount <= 0) {
                throw new DALException("删除失败,没有符合条件的数据");
            } else {
                return deleteCount;
            }
        }

        /**
         * 新增经销商跟进内容
         *
         * @param complaintCustomFieldTestDto
         * @return
         */
        @Override
        public int insertcCus(ComplaintCustomFieldTestDTO complaintCustomFieldTestDto) throws ParseException {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
            if (!StringUtils.isNullOrEmpty(complaintCustomFieldTestDto.getPlanFollowTime())) {
                Date planFollowTime = sf.parse(complaintCustomFieldTestDto.getPlanFollowTime());
                complaintFollowDTO.setPlanFollowTime(planFollowTime);
            }
            long complaintInfoId=complaintCustomFieldTestDto.getComplaintInfoId();

            //防重复
            IDistributedLock lock = DistributedLockUtil.getDistributedLock(
                    CacheKeyEnum.CUSTOMER_COMPLAINT_KEY.getKey(complaintInfoId));
            if(!lock.antiDuplication()){
                throw new DALException( "当前案件正在维护，请勿重复操作" );
            }

            try {
                //查询经销商首次回复时间是否存在
                ComplaintInfoDTO complaintInfoDTO = new ComplaintInfoDTO();
                List<ComplaintInfoPO> list=complaintInfoMapper.queryDealerFirstReplyTime(complaintInfoId);
                if(list.get(0).getDealerFisrtReplyTime()==null){
                    complaintInfoDTO.setDealerFisrtReplyTime(new Date());
                    complaintInfoService.update(complaintInfoId,complaintInfoDTO);

                }
                //查询案子是否重启
                if(82451003==list.get(0).getWorkOrderStatus()&&!StringUtils.isNullOrEmpty(list.get(0).getFisrtRestartTime())){
                    complaintInfoDTO.setFisrtRestartDealerFisrtReplyTime(new Date());
                    complaintInfoService.update(complaintInfoId,complaintInfoDTO);
                }
                if(complaintCustomFieldTestDto.getFollowContent()!=null){
                    complaintInfoDTO.setWorkOrderStatus(82451002);
                    complaintInfoService.update(complaintInfoId,complaintInfoDTO);
                }
                complaintInfoDTO.setWorkOrderStatus(82451002);
                complaintFollowDTO.setActuallFollowTime2(new Date());
                complaintFollowDTO.setFollowTime(new Date());
                complaintFollowDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
                complaintFollowDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName()+"("+FrameworkUtil.getLoginInfo().getOwnerCode()+")");
                complaintFollowDTO.setFollowContent(complaintCustomFieldTestDto.getFollowContent());
                complaintFollowDTO.setComplaintInfoId(complaintInfoId);
                complaintFollowDTO.setFollower(FrameworkUtil.getLoginInfo().getUserId().toString());
                complaintFollowDTO.setDealerNotPublish(false);
                complaintFollowDTO.setCcmNotPublish(true);
                complaintInfoServiceImpl.IssuedUpdataData(complaintFollowDTO,FrameworkUtil.getLoginInfo().getOwnerCode(),"跟进中");
                return insert(complaintFollowDTO);
            } catch (Exception e) {
                logger.info("客诉跟进报错：",e);
                throw new ServiceBizException(e.getMessage());
            } finally {
                logger.info("客诉跟进释放锁");
                lock.release();
            }
        }

        @Override
        public int reportVeh(ComplaintCustomFieldTestDTO complaintCustomFieldTestDto) throws ParseException {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
            if ( !StringUtils.isNullOrEmpty(complaintCustomFieldTestDto.getPlanFollowTime())) {
                Date planFollowTime = sf.parse(complaintCustomFieldTestDto.getPlanFollowTime());
                complaintFollowDTO.setPlanFollowTime(planFollowTime);
            }
            long complaintInfoId=complaintCustomFieldTestDto.getComplaintInfoId();
            //查询经销商首次回复时间是否存在
            ComplaintInfoDTO complaintInfoDTO = new ComplaintInfoDTO();
            List<ComplaintInfoPO> list=complaintInfoMapper.queryDealerFirstReplyTime(complaintInfoId);
            if(list.get(0).getDealerFisrtReplyTime()==null){
                complaintInfoDTO.setDealerFisrtReplyTime(new Date());
                complaintInfoService.update(complaintInfoId,complaintInfoDTO);

            }
            ComplaintInfoPO complaintInfopo = new ComplaintInfoPO();
            complaintFollowDTO.setFollowTime(new Date());
            complaintFollowDTO.setFollowContent(complaintCustomFieldTestDto.getFollowContent());

            complaintFollowDTO.setComplaintInfoId(complaintCustomFieldTestDto.getComplaintInfoId());
            complaintFollowDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
            complaintFollowDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName()+"("+FrameworkUtil.getLoginInfo().getOwnerCode()+")");
            complaintFollowDTO.setDealerNotPublish(true);
            complaintFollowDTO.setCcmNotPublish(true);
            complaintInfopo.setReport(true);
            if(complaintCustomFieldTestDto.getFollowContent()!=null){
                complaintInfopo.setWorkOrderStatus(82451002);
            }
            complaintInfoDTO.setId(complaintCustomFieldTestDto.getComplaintInfoId());
            LambdaQueryWrapper<ComplaintInfoPO> wrapper = new QueryWrapper<ComplaintInfoPO>().lambda();
            wrapper.eq(ComplaintInfoPO::getId, complaintInfoDTO.getId());
            complaintInfoMapper.update(complaintInfopo, wrapper);
            complaintInfoServiceImpl.IssuedUpdataData(complaintFollowDTO,FrameworkUtil.getLoginInfo().getOwnerCode(),"跟进中");
            return insert(complaintFollowDTO);
        }

        @Override
        public int insertCcmCus(CcmFollowInfoDto ccmFollowInfoDto) throws ParseException {
            //防重复
            IDistributedLock lock = DistributedLockUtil.getDistributedLock(
                    CacheKeyEnum.CUSTOMER_COMPLAINT_KEY.getKey(ccmFollowInfoDto.getComplaintInfoId()));
            if(!lock.antiDuplication()){
                throw new DALException( "当前案件正在维护，请勿重复操作" );
            }

            try {
                SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getPlanFollowTime())) {
                    Date planFollowTime = sf.parse(ccmFollowInfoDto.getPlanFollowTime());
                    complaintFollowDTO.setPlanFollowTime(planFollowTime);
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getAdvise())) {
                    complaintFollowDTO.setAdvise(ccmFollowInfoDto.getAdvise());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getCcMainReason())) {
                    List ccMainReasonlist = ccmFollowInfoDto.getCcMainReason();
                    StringBuffer ccMainReason = new StringBuffer();
                        for (int i = 0; i < ccMainReasonlist.size(); i++) {
                        if (i == ccMainReasonlist.size() - 1) {
                            ccMainReason.append(ccMainReasonlist.get(i));
                        } else {
                            ccMainReason.append(ccMainReasonlist.get(i)+",");
                        }
                    }
                    complaintFollowDTO.setCcMainReason(ccMainReason.toString());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getCcmPart())) {
                    List ccmPartlist = ccmFollowInfoDto.getCcmPart();
                    StringBuffer  ccmPart=new StringBuffer();
                    for (int i = 0; i < ccmPartlist.size(); i++) {
                        if (i == ccmPartlist.size() - 1) {
                            ccmPart.append(ccmPartlist.get(i));
                        } else {
                            ccmPart.append(ccmPartlist.get(i)+",");
                        }
                    }
                    complaintFollowDTO.setCcmPart(ccmPart.toString());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getCcmSubdivisionPart())) {
                    List ccmSubdivisionPartList = ccmFollowInfoDto.getCcmSubdivisionPart();
                    StringBuffer ccmSubdivisionPart = new StringBuffer();
                    for (int i = 0; i < ccmSubdivisionPartList.size(); i++) {
                        if (i == ccmSubdivisionPartList.size() - 1) {
                            ccmSubdivisionPart.append(ccmSubdivisionPartList.get(i));
                        } else {
                            ccmSubdivisionPart.append(ccmSubdivisionPartList.get(i)+",");
                        }
                    }
                    complaintFollowDTO.setCcmSubdivisionPart(ccmSubdivisionPart.toString());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getCcResult())) {
                    List ccResultList = ccmFollowInfoDto.getCcResult();
                    StringBuffer cResult = new StringBuffer();
                    for (int i = 0; i < ccResultList.size(); i++) {
                        if (i == ccResultList.size() - 1) {
                            cResult.append(ccResultList.get(i));
                        } else {
                            cResult.append(ccResultList.get(i)+",");
                        }
                    }
                    complaintFollowDTO.setCcResult(cResult.toString());
                }
                if ( !StringUtils.isNullOrEmpty(ccmFollowInfoDto.getCcmSubject())) {
                    complaintFollowDTO.setCcmSubject(ccmFollowInfoDto.getCcmSubject());
                }
                if ( !StringUtils.isNullOrEmpty(ccmFollowInfoDto.getClassification1())) {
                    complaintFollowDTO.setClassification1(ccmFollowInfoDto.getClassification1());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getClassification2())) {
                    complaintFollowDTO.setClassification2(ccmFollowInfoDto.getClassification2());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getClassification3())) {
                    complaintFollowDTO.setClassification3(ccmFollowInfoDto.getClassification3());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getClassification4())) {
                    complaintFollowDTO.setClassification4(ccmFollowInfoDto.getClassification4());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getClassification5())) {
                    complaintFollowDTO.setClassification5(ccmFollowInfoDto.getClassification5());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getClassification6())) {
                    complaintFollowDTO.setClassification6(ccmFollowInfoDto.getClassification6());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getFollowContent())) {
                    complaintFollowDTO.setFollowContent(ccmFollowInfoDto.getFollowContent());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getCcmNotPublish())) {
                    if (ccmFollowInfoDto.getCcmNotPublish() == 1) {
                        complaintFollowDTO.setDealerNotPublish(true);
                        complaintFollowDTO.setIsCcmNotPublish(true);
                    } else {
                        complaintFollowDTO.setDealerNotPublish(true);
                        complaintFollowDTO.setIsCcmNotPublish(false);
                    }
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getKeyword())) {
                    complaintFollowDTO.setKeyword(ccmFollowInfoDto.getKeyword());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getStatus())) {
                    complaintFollowDTO.setStatus(ccmFollowInfoDto.getStatus());
                }
                complaintFollowDTO.setCcm(true);

                complaintFollowDTO.setActuallFollowTime2(new Date());
                complaintFollowDTO.setFollowTime(new Date());
                complaintFollowDTO.setComplaintInfoId(ccmFollowInfoDto.getComplaintInfoId());
                complaintFollowDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
                complaintFollowDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
                ComplaintInfoDTO complaintInfoDTO=new ComplaintInfoDTO();
                complaintInfoDTO.setIsCcmIsRead(true);
                complaintInfoService.update(ccmFollowInfoDto.getComplaintInfoId(),complaintInfoDTO);
                if(!StringUtils.isNullOrEmpty(complaintFollowDTO.getFollowContent())){
                    complaintInfoService.IssuedUpdataData(complaintFollowDTO,"厂端","跟进中");
                }
                return insert(complaintFollowDTO);
            } catch (ParseException e) {
                logger.info("客诉跟进报错：",e);
                throw new ServiceBizException(e.getMessage());
            } finally {
                logger.info("客诉跟进释放锁");
                lock.release();
            }
        }

        @Override
        public int insertAssistCus(ComplaintCustomFieldTestDTO complaintCustomFieldTestDto) {
            //防重复
            IDistributedLock lock = DistributedLockUtil.getDistributedLock(
                    CacheKeyEnum.CUSTOMER_COMPLAINT_KEY.getKey(complaintCustomFieldTestDto.getComplaintInfoId()));
            if(!lock.antiDuplication()){
                throw new DALException( "当前案件正在维护，请勿重复操作" );
            }

            try {
                ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
                complaintFollowDTO.setFollowTime(new Date());
                complaintFollowDTO.setFollowContent(complaintCustomFieldTestDto.getFollowContent());
                complaintFollowDTO.setComplaintInfoId(complaintCustomFieldTestDto.getComplaintInfoId());
                complaintFollowDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
                complaintFollowDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
                complaintFollowDTO.setIsCcmNotPublish(true);
                complaintFollowDTO.setDealerNotPublish(true);
                //质量部填写
                complaintFollowDTO.setDataSources(3);
                Map<String, Object> queryParam = new HashMap<String, Object>(16);
                queryParam.put("id", complaintCustomFieldTestDto.getComplaintInfoId());
                //现在写死，等到接口完善
                queryParam.put("dem", complaintCustomFieldTestDto.getOrgId());
                String code = "10041001";
                if (complaintCustomFieldTestDto.getIsFinish().equals(code)) {
                    complaintAssistDepartmentMapper.updateIsfinish(queryParam);
                } else {
                    complaintAssistDepartmentMapper.updateIsfinish1(queryParam);
                }
                complaintInfoService.IssuedUpdataData(complaintFollowDTO,"厂端","跟进中");
                return insert(complaintFollowDTO);
            } catch (Exception e) {
                logger.info("客诉跟进报错：",e);
                throw new ServiceBizException(e.getMessage());
            } finally {
                logger.info("客诉跟进释放锁");
                lock.release();
            }
        }

        @Override
        public int insertQuCus(CcmFollowInfoDto ccmFollowInfoDto) {
            //防重复
            IDistributedLock lock = DistributedLockUtil.getDistributedLock(
                    CacheKeyEnum.CUSTOMER_COMPLAINT_KEY.getKey(ccmFollowInfoDto.getComplaintInfoId()));
            if(!lock.antiDuplication()){
                throw new DALException( "当前案件正在维护，请勿重复操作" );
            }

            try {
                ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getQualityClassification1())) {
                    complaintFollowDTO.setQualityClassification1(ccmFollowInfoDto.getQualityClassification1());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getQualityClassification2())) {
                    complaintFollowDTO.setQualityClassification2(ccmFollowInfoDto.getQualityClassification2());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getQualityClassification3())) {
                    complaintFollowDTO.setQualityClassification3(ccmFollowInfoDto.getQualityClassification3());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getQualityClassification4())) {
                    complaintFollowDTO.setQualityClassification4(ccmFollowInfoDto.getQualityClassification4());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getFaultClassification())) {
                    complaintFollowDTO.setFaultClassification(ccmFollowInfoDto.getFaultClassification());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getRemark1())) {
                    complaintFollowDTO.setRemark1(ccmFollowInfoDto.getRemark1());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getRemark2())) {
                    complaintFollowDTO.setRemark2(ccmFollowInfoDto.getRemark2());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getRemark3())) {
                    complaintFollowDTO.setRemark3(ccmFollowInfoDto.getRemark3());
                }
                if (!StringUtils.isNullOrEmpty(ccmFollowInfoDto.getRemark4())) {
                    complaintFollowDTO.setRemark4(ccmFollowInfoDto.getRemark4());
                }
                complaintFollowDTO.setComplaintInfoId(ccmFollowInfoDto.getComplaintInfoId());
                complaintFollowDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
                complaintFollowDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
                complaintFollowDTO.setIsCcmNotPublish(true);
                complaintFollowDTO.setDealerNotPublish(true);
                complaintFollowDTO.setFollowTime(new Date());
                complaintInfoService.IssuedUpdataData(complaintFollowDTO,"厂端","跟进中");
                return insert(complaintFollowDTO);
            } catch (Exception e) {
                logger.info("客诉跟进报错：",e);
                throw new ServiceBizException(e.getMessage());
            } finally {
                logger.info("客诉跟进释放锁");
                lock.release();
            }
        }

        @Override
        public List<ComplaintFollowDTO> queryNewCus(ComplaintFollowDTO complaintFollowDTO) {
            if (complaintFollowDTO == null) {
                complaintFollowDTO = new ComplaintFollowDTO();
            }
            ComplaintFollowPO complaintFollowPo = complaintFollowDTO.transDtoToPo(ComplaintFollowPO.class);
            List<ComplaintFollowPO> list = complaintFollowMapper.queryNewCus(complaintFollowPo);
            if (CommonUtils.isNullOrEmpty(list)) {
                return new ArrayList<>();
            } else {
                return list.stream().map(m -> m.transPoToDto(ComplaintFollowDTO.class)).collect(Collectors.toList());

            }
        }

        /**
         * 区域经理跟进内容
         *
         * @param complaintCustomFieldTestDto
         * @return
         */
        @Override
        public int insertRegionCus(ComplaintCustomFieldTestDTO complaintCustomFieldTestDto)throws ParseException  {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
            if (!StringUtils.isNullOrEmpty(complaintCustomFieldTestDto.getPlanFollowTime())) {
                Date planFollowTime = sf.parse(complaintCustomFieldTestDto.getPlanFollowTime());
                complaintFollowDTO.setPlanFollowTime(planFollowTime);
            }
            long complaintInfoId=complaintCustomFieldTestDto.getComplaintInfoId();
            //防重复
            IDistributedLock lock = DistributedLockUtil.getDistributedLock(
                    CacheKeyEnum.CUSTOMER_COMPLAINT_KEY.getKey(complaintInfoId));
            if(!lock.antiDuplication()){
                throw new DALException( "当前案件正在维护，请勿重复操作" );
            }

            try {
                complaintFollowDTO.setActuallFollowTime2(new Date());
                complaintFollowDTO.setFollowTime(new Date());
                complaintFollowDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
                complaintFollowDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
                complaintFollowDTO.setFollowContent(complaintCustomFieldTestDto.getFollowContent());
                complaintFollowDTO.setComplaintInfoId(complaintInfoId);
                complaintFollowDTO.setFollower(FrameworkUtil.getLoginInfo().getUserId().toString());
                complaintFollowDTO.setDealerNotPublish(false);
                complaintFollowDTO.setCcmNotPublish(true);
                complaintInfoService.IssuedUpdataData(complaintFollowDTO,"厂端","跟进中");
                return insert(complaintFollowDTO);
            } catch (Exception e) {
                logger.info("客诉跟进报错：",e);
                throw new ServiceBizException(e.getMessage());
            } finally {
                logger.info("客诉跟进释放锁");
                lock.release();
            }
        }

        /**
         * 协助经销商
         *
         * @param complaintCustomFieldTestDto
         * @return
         */
        @Override
        public int insertAssistDealer(ComplaintCustomFieldTestDTO complaintCustomFieldTestDto) {
            //防重复
            IDistributedLock lock = DistributedLockUtil.getDistributedLock(
                    CacheKeyEnum.CUSTOMER_COMPLAINT_KEY.getKey(complaintCustomFieldTestDto.getComplaintInfoId()));
            if(!lock.antiDuplication()){
                throw new DALException( "当前案件正在维护，请勿重复操作" );
            }

            try {
                ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
                complaintFollowDTO.setFollowTime(new Date());
                complaintFollowDTO.setFollowContent(complaintCustomFieldTestDto.getFollowContent());
                complaintFollowDTO.setComplaintInfoId(complaintCustomFieldTestDto.getComplaintInfoId());
                complaintFollowDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
                complaintFollowDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
                complaintFollowDTO.setIsCcmNotPublish(true);
                complaintFollowDTO.setDealerNotPublish(true);
                Map<String, Object> queryParam = new HashMap<String, Object>(16);
                queryParam.put("id", complaintCustomFieldTestDto.getComplaintInfoId());
                //现在写死，等到接口完善
                queryParam.put("dem", FrameworkUtil.getLoginInfo().getOwnerCode());
                String code = "10041001";
                if (complaintCustomFieldTestDto.getIsFinish().equals(code)) {
                    complaintAssistDepartmentMapper.updateIsfinishByDealer(queryParam);
                } else {
                    complaintAssistDepartmentMapper.updateIsfinishByDealer1(queryParam);
                }
                complaintInfoService.IssuedUpdataData(complaintFollowDTO,"厂端","跟进中");
                return insert(complaintFollowDTO);
            } catch (Exception e) {
                logger.info("客诉跟进报错：",e);
                throw new ServiceBizException(e.getMessage());
            } finally {
                logger.info("客诉跟进释放锁");
                lock.release();
            }
        }

        @Override
        public List<ComplaintInfMoreDTO> queryNextFollowing() {
           return complaintFollowMapper.queryNextFollowing();
        }
    }
