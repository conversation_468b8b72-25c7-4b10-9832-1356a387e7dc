package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyMailHistoryMapper;
import com.yonyou.dmscus.customer.dto.EmailInfoDto;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyMailHistoryDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyMailHistoryPO;
import com.yonyou.dmscus.customer.service.CommonService;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyMailHistoryService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 亲善预申请邮件发送记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
@Service
public class GoodwillApplyMailHistoryServiceImpl
		extends ServiceImpl<GoodwillApplyMailHistoryMapper, GoodwillApplyMailHistoryPO>
		implements GoodwillApplyMailHistoryService {
	/** 日志对象 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillApplyMailHistoryMapper goodwillApplyMailHistoryMapper;
	@Autowired
	CommonService commonService;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillApplyMailHistoryDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.repair.entity.dto.GoodwillApplyMailHistoryDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillApplyMailHistoryDTO> selectPageBysql(Page page,
			GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO) {
		if (goodwillApplyMailHistoryDTO == null) {
			goodwillApplyMailHistoryDTO = new GoodwillApplyMailHistoryDTO();
		}
		GoodwillApplyMailHistoryPO goodwillApplyMailHistoryPo = goodwillApplyMailHistoryDTO
				.transDtoToPo(GoodwillApplyMailHistoryPO.class);

		List<GoodwillApplyMailHistoryPO> list = goodwillApplyMailHistoryMapper.selectPageBySql(page,
				goodwillApplyMailHistoryPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillApplyMailHistoryDTO> result = list.stream()
					.map(m -> m.transPoToDto(GoodwillApplyMailHistoryDTO.class)).collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillApplyMailHistoryDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.repair.entity.dto.GoodwillApplyMailHistoryDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillApplyMailHistoryDTO> selectListBySql(GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO) {
		if (goodwillApplyMailHistoryDTO == null) {
			goodwillApplyMailHistoryDTO = new GoodwillApplyMailHistoryDTO();
		}
		GoodwillApplyMailHistoryPO goodwillApplyMailHistoryPo = goodwillApplyMailHistoryDTO
				.transDtoToPo(GoodwillApplyMailHistoryPO.class);
		List<GoodwillApplyMailHistoryPO> list = goodwillApplyMailHistoryMapper
				.selectListBySql(goodwillApplyMailHistoryPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillApplyMailHistoryDTO.class))
					.collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.repair.entity.dto.GoodwillApplyMailHistoryDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillApplyMailHistoryDTO getById(Long id) {
		GoodwillApplyMailHistoryPO goodwillApplyMailHistoryPo = goodwillApplyMailHistoryMapper.selectById(id);
		if (goodwillApplyMailHistoryPo != null) {
			return goodwillApplyMailHistoryPo.transPoToDto(GoodwillApplyMailHistoryDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillApplyMailHistoryDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO) {
		// 对对象进行赋值操作
		GoodwillApplyMailHistoryPO goodwillApplyMailHistoryPo = goodwillApplyMailHistoryDTO
				.transDtoToPo(GoodwillApplyMailHistoryPO.class);
		// 执行插入
		int row = goodwillApplyMailHistoryMapper.insert(goodwillApplyMailHistoryPo);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillApplyMailHistoryDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO) {
		GoodwillApplyMailHistoryPO goodwillApplyMailHistoryPo = goodwillApplyMailHistoryMapper.selectById(id);
		// 对对象进行赋值操作
		goodwillApplyMailHistoryDTO.transDtoToPo(goodwillApplyMailHistoryPo);
		// 执行更新
		int row = goodwillApplyMailHistoryMapper.updateById(goodwillApplyMailHistoryPo);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillApplyMailHistoryMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillApplyMailHistoryMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillApplyMailHistoryDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int sendAgain(Long id) {
		Integer code = 0;
		GoodwillApplyMailHistoryPO goodwillApplyMailHistoryPo = goodwillApplyMailHistoryMapper.selectById(id);
		// 4，组装数据调用中台发送邮件
		EmailInfoDto emailInfoDto = new EmailInfoDto();
		emailInfoDto.setFrom(goodwillApplyMailHistoryPo.getSendBy());
		String[] list = null;
		if (goodwillApplyMailHistoryPo.getReceiverMail().indexOf(",") > 0) {
			list = goodwillApplyMailHistoryPo.getReceiverMail().split(",");
		} else {
			list = new String[1];
			list[0] = goodwillApplyMailHistoryPo.getReceiverMail();
		}
		emailInfoDto.setTo(list);
		emailInfoDto.setText(goodwillApplyMailHistoryPo.getContent());
		emailInfoDto.setSubject(goodwillApplyMailHistoryPo.getTitle());
		try {
			String status = commonService.sendGoodwillMail(emailInfoDto);
			// if (status.equals('0')) {
			if ("0".equals(status)) {
				logger.info("发送邮件成功！");
				code = 200;
				// noticeSendEmailRecordDTO.setSendStatus(CommonConstants.IS_SENDMAIL_SUCCESS);
				goodwillApplyMailHistoryPo.setSendStatus(82771001);
			} else {
				goodwillApplyMailHistoryPo.setSendStatus(82771002);
				logger.info("发送邮件失败！");
			}

		} catch (Exception e) {
			logger.info(e.getMessage());
		} finally {
			// return noticeSendEmailRecordService.insert( noticeSendEmailRecordDTO);
		}

		// 对对象进行赋值操作
		// 执行更新
		goodwillApplyMailHistoryPo.setSendTime(new Date());
		int row = goodwillApplyMailHistoryMapper.updateById(goodwillApplyMailHistoryPo);
		return code;
	}
}
