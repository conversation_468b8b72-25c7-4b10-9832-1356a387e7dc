package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

    
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;

import com.yonyou.dmscloud.function.utils.common.StringUtils;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ReasonsforFiveDto;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintNotCloseCaseReasonPO;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintNotCloseCaseReasonMapper;

import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintNotCloseCaseReasonTestPO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintNotCloseCaseReasonService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonDTO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;




                                                                                    /**
 * <p>
 * 5日未结案原因 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Service
public class ComplaintNotCloseCaseReasonServiceImpl implements ComplaintNotCloseCaseReasonService {
          /**
          * 日志说明
         */
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        ComplaintNotCloseCaseReasonMapper complaintNotCloseCaseReasonMapper;

        /**
         * 分页查询对应数据
         *
         * @param page 分页对象
         * @param complaintNotCloseCaseReasonDTO 查询条件
         * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public IPage<ComplaintNotCloseCaseReasonDTO>selectPageBysql(Page page,ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO){
            if(complaintNotCloseCaseReasonDTO ==null){
                complaintNotCloseCaseReasonDTO =new ComplaintNotCloseCaseReasonDTO();
            }
            ComplaintNotCloseCaseReasonPO complaintNotCloseCaseReasonPo =complaintNotCloseCaseReasonDTO.transDtoToPo(ComplaintNotCloseCaseReasonPO.class);

            List<ComplaintNotCloseCaseReasonPO>list= complaintNotCloseCaseReasonMapper.selectPageBySql(page,complaintNotCloseCaseReasonPo);
            if(CommonUtils.isNullOrEmpty(list)){
                page.setRecords(new ArrayList<>());
                return page;
            }else{
                List<ComplaintNotCloseCaseReasonDTO>result=list.stream().map(m->m.transPoToDto(ComplaintNotCloseCaseReasonDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param complaintNotCloseCaseReasonDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<ComplaintNotCloseCaseReasonDTO>selectListBySql(ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO){
            if(complaintNotCloseCaseReasonDTO ==null){
                complaintNotCloseCaseReasonDTO =new ComplaintNotCloseCaseReasonDTO();
            }
            ComplaintNotCloseCaseReasonPO complaintNotCloseCaseReasonPo =complaintNotCloseCaseReasonDTO.transDtoToPo(ComplaintNotCloseCaseReasonPO.class);
            List<ComplaintNotCloseCaseReasonPO>list= complaintNotCloseCaseReasonMapper.selectListBySql(complaintNotCloseCaseReasonPo);
            if(CommonUtils.isNullOrEmpty(list)){
                return new ArrayList<>();
            }else{
                return list.stream().map(m->m.transPoToDto(ComplaintNotCloseCaseReasonDTO.class)).collect(Collectors.toList());
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public ComplaintNotCloseCaseReasonDTO getById(Long id){
            ComplaintNotCloseCaseReasonPO complaintNotCloseCaseReasonPo = complaintNotCloseCaseReasonMapper.selectById(id);
            if(complaintNotCloseCaseReasonPo!=null){
                return complaintNotCloseCaseReasonPo.transPoToDto(ComplaintNotCloseCaseReasonDTO.class);
            }else{
                throw new DALException("查询失败,没有符合条件的数据");
            }
        }

        /**
         * 根据DTO 进行数据新增
         *
         * @param complaintNotCloseCaseReasonDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO){
            //对对象进行赋值操作
            ComplaintNotCloseCaseReasonPO complaintNotCloseCaseReasonPo = complaintNotCloseCaseReasonDTO.transDtoToPo(ComplaintNotCloseCaseReasonPO.class);
            //执行插入
            int row= complaintNotCloseCaseReasonMapper.insert(complaintNotCloseCaseReasonPo);
            //返回插入的值
            return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param complaintNotCloseCaseReasonDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO){
            ComplaintNotCloseCaseReasonPO complaintNotCloseCaseReasonPo = complaintNotCloseCaseReasonMapper.selectById(id);
            //对对象进行赋值操作
            complaintNotCloseCaseReasonDTO.transDtoToPo(complaintNotCloseCaseReasonPo);
            //执行更新
            int row= complaintNotCloseCaseReasonMapper.updateById(complaintNotCloseCaseReasonPo);
            return row;
        }

        /**
         * 根据ID 进行删除数据
         *
         * @param id
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteById(Long id){
            int deleteCount= complaintNotCloseCaseReasonMapper.deleteById(id);
            logger.debug("删除数量：{}",deleteCount);
            if(deleteCount<=0){
                throw new DALException("删除失败,没有符合条件的数据");
            }else{
                return deleteCount;
            }
        }

         @Override
         public int insertReasonsforFive(ReasonsforFiveDto reasonsforFiveDto) {
             ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO=new ComplaintNotCloseCaseReasonDTO();
             Long id=reasonsforFiveDto.getComplaintInfoId();
             int resulttime=0;
             List<ComplaintNotCloseCaseReasonPO> followTimeList=complaintNotCloseCaseReasonMapper.selectfollowTime(id);
             if(followTimeList.size()!=0) {
                 ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasontestDTO=new ComplaintNotCloseCaseReasonDTO();
                 Date lastFollowTime = followTimeList.get(0).getFollowTime();
                 long id1=followTimeList.get(0).getId();
                 Calendar aCalendar = Calendar.getInstance();
                 aCalendar.setTime(lastFollowTime);
                 int day1 = aCalendar.get(Calendar.DAY_OF_YEAR);
                 aCalendar.setTime(new Date());
                 int day2 = aCalendar.get(Calendar.DAY_OF_YEAR);
                 resulttime = day2 - day1;
                 complaintNotCloseCaseReasontestDTO.setDuration(resulttime);
                 update(id1,complaintNotCloseCaseReasontestDTO);
             }
             int row=0;
             String smallClass="";
             String smallName="";
             if(reasonsforFiveDto.getReason().getCustomerReason()!=null&&reasonsforFiveDto.getReason().getCustomerReason()!=""){
                 String fiveReason=reasonsforFiveDto.getReason().getCustomerReason();
                 switch (fiveReason){
                     case "customerReason":
                         complaintNotCloseCaseReasonDTO.setComplaintInfoId(id);
                         complaintNotCloseCaseReasonDTO.setBigClass(82441001);
                         complaintNotCloseCaseReasonDTO.setBigClassName("客户原因");

                         if(reasonsforFiveDto.getReason().getCustomerReason1()!=null&&reasonsforFiveDto.getReason().getCustomerReason1()==true){
                             smallClass+="82491001,";
                             smallName+="等待客户进店,";
                         }
                         if(reasonsforFiveDto.getReason().getCustomerReason2()!=null&&reasonsforFiveDto.getReason().getCustomerReason2()==true){
                             smallClass+="82491002,";
                             smallName+="等待客户试车,";
                         }
                         if(reasonsforFiveDto.getReason().getCustomerReason3()!=null&&reasonsforFiveDto.getReason().getCustomerReason3()==true){
                             smallClass+="82491003,";
                             smallName+="客户拒绝车辆检查,";
                         }
                         if(reasonsforFiveDto.getReason().getCustomerReason4()!=null&&reasonsforFiveDto.getReason().getCustomerReason4()==true){
                             smallClass+="82491004,";
                             smallName+="客户不同意维修方案,";
                         }
                         if(reasonsforFiveDto.getReason().getCustomerReason5()!=null&&reasonsforFiveDto.getReason().getCustomerReason5()==true){
                             smallClass+="82491005,";
                             smallName+="客户对技术判断不认可,";
                         }
                         if(reasonsforFiveDto.getReason().getCustomerReason6()!=null&&reasonsforFiveDto.getReason().getCustomerReason6()==true){
                             smallClass+="82491006,";
                             smallName+="客户期待值高（赔偿要求高）,";
                         }
                         if(reasonsforFiveDto.getReason().getCustomerReason7()!=null&&reasonsforFiveDto.getReason().getCustomerReason7()==true){
                             complaintNotCloseCaseReasonDTO.setOther(reasonsforFiveDto.getReason().getInput1());
                         }else {
                             complaintNotCloseCaseReasonDTO.setOther("");
                         }
                         complaintNotCloseCaseReasonDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
                         complaintNotCloseCaseReasonDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
                         complaintNotCloseCaseReasonDTO.setFollowTime(new Date());
                         complaintNotCloseCaseReasonDTO.setSmallClass(smallClass);
                         complaintNotCloseCaseReasonDTO.setSmallClassName(smallName);
                         complaintNotCloseCaseReasonDTO.setDuration(0);
                         row= insert(complaintNotCloseCaseReasonDTO);
                         break;
                     case "dearReason":
                         complaintNotCloseCaseReasonDTO.setComplaintInfoId(id);
                         complaintNotCloseCaseReasonDTO.setBigClass(82441002);
                         complaintNotCloseCaseReasonDTO.setBigClassName("经销商原因");
                         if(reasonsforFiveDto.getReason().getDearReason1()!=null&&reasonsforFiveDto.getReason().getDearReason1()==true){
                             smallClass+="82501001,";
                             smallName+="等待经销商拖车/上门服务,";
                         }
                         if(reasonsforFiveDto.getReason().getDearReason2()!=null&&reasonsforFiveDto.getReason().getDearReason2()==true){
                             smallClass+="82501002,";
                             smallName+="等待经销商拆解车辆,";
                         }
                         if(reasonsforFiveDto.getReason().getDearReason3()!=null&&reasonsforFiveDto.getReason().getDearReason3()==true){
                             smallClass+="82501003,";
                             smallName+="等待经销商提报技术报告,";
                         }
                         if(reasonsforFiveDto.getReason().getDearReason4()!=null&&reasonsforFiveDto.getReason().getDearReason4()==true){
                             smallClass+="82501004,";
                             smallName+="等待经销商维修车辆,";
                         }
                         if(reasonsforFiveDto.getReason().getDearReason5()!=null&&reasonsforFiveDto.getReason().getDearReason5()==true){
                             smallClass+="82501005,";
                             smallName+="等待经销商提供代步车,";
                         }
                         if(reasonsforFiveDto.getReason().getDearReason6()!=null&&reasonsforFiveDto.getReason().getDearReason6()==true){
                             smallClass+="82501006,";
                             smallName+="等待经销商订购配件,";
                         }
                         if(reasonsforFiveDto.getReason().getDearReason7()!=null&&reasonsforFiveDto.getReason().getDearReason7()==true){
                             smallClass+="82501007,";
                             smallName+="等待经销商试车,";
                         }
                         if(reasonsforFiveDto.getReason().getDearReason8()!=null&&reasonsforFiveDto.getReason().getDearReason8()==true){
                             smallClass+="82501008,";
                             smallName+="等待经销商走保险定损流程,";
                         }
                         if(reasonsforFiveDto.getReason().getDearReason9()!=null&&reasonsforFiveDto.getReason().getDearReason9()==true){
                             complaintNotCloseCaseReasonDTO.setOther(reasonsforFiveDto.getReason().getInput2());
                         }else {
                             complaintNotCloseCaseReasonDTO.setOther("");
                         }
                         complaintNotCloseCaseReasonDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
                         complaintNotCloseCaseReasonDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
                         complaintNotCloseCaseReasonDTO.setFollowTime(new Date());
                         complaintNotCloseCaseReasonDTO.setSmallClass(smallClass);
                         complaintNotCloseCaseReasonDTO.setSmallClassName(smallName);
                         complaintNotCloseCaseReasonDTO.setDuration(0);
                         row= insert(complaintNotCloseCaseReasonDTO);
                         break;
                     case "vcdcReason":
                         complaintNotCloseCaseReasonDTO.setComplaintInfoId(id);
                         complaintNotCloseCaseReasonDTO.setBigClass(82441003);
                         complaintNotCloseCaseReasonDTO.setBigClassName("VCDC原因");
                         if(reasonsforFiveDto.getReason().getVcdcReason1()!=null&&reasonsforFiveDto.getReason().getVcdcReason1()==true){
                             smallClass+="82511001,";
                             smallName+="等待厂家技术方案,";
                         }
                         if(reasonsforFiveDto.getReason().getVcdcReason2()!=null&&reasonsforFiveDto.getReason().getVcdcReason2()==true){
                             smallClass+="82511002,";
                             smallName+="等待厂家配件,";
                         }
                         if(reasonsforFiveDto.getReason().getVcdcReason3()!=null&&reasonsforFiveDto.getReason().getVcdcReason3()==true){
                             smallClass+="82511003,";
                             smallName+="等待厂家法务回复,";
                         }
                         if(reasonsforFiveDto.getReason().getVcdcReason4()!=null&&reasonsforFiveDto.getReason().getVcdcReason4()==true){
                             smallClass+="82511004,";
                             smallName+="等待厂家公关回复,";
                         }
                         if(reasonsforFiveDto.getReason().getVcdcReason5()!=null&&reasonsforFiveDto.getReason().getVcdcReason5()==true){
                             smallClass+="82511005,";
                             smallName+="等待区域结案,";
                         }
                         if(reasonsforFiveDto.getReason().getVcdcReason6()!=null&&reasonsforFiveDto.getReason().getVcdcReason6()==true){
                             complaintNotCloseCaseReasonDTO.setOther(reasonsforFiveDto.getReason().getInput3());
                         }else {
                             complaintNotCloseCaseReasonDTO.setOther("");
                         }
                         complaintNotCloseCaseReasonDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
                         complaintNotCloseCaseReasonDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
                         complaintNotCloseCaseReasonDTO.setFollowTime(new Date());
                         complaintNotCloseCaseReasonDTO.setSmallClass(smallClass);
                         complaintNotCloseCaseReasonDTO.setSmallClassName(smallName);
                         complaintNotCloseCaseReasonDTO.setDuration(0);
                         row= insert(complaintNotCloseCaseReasonDTO);
                         break;
                     case "thirdReason":
                         complaintNotCloseCaseReasonDTO.setComplaintInfoId(id);
                         complaintNotCloseCaseReasonDTO.setBigClass(82441004);
                         complaintNotCloseCaseReasonDTO.setBigClassName("第三方原因");
                         if(reasonsforFiveDto.getReason().getThirdReason1()!=null&&reasonsforFiveDto.getReason().getThirdReason1()==true){
                             smallClass+="82521001,";
                             smallName+="等待法院结论,";
                         }
                         if(reasonsforFiveDto.getReason().getThirdReason2()!=null&&reasonsforFiveDto.getReason().getThirdReason2()==true){
                             smallClass+="82521002,";
                             smallName+="等待12315结论,";
                         }
                         if(reasonsforFiveDto.getReason().getThirdReason3()!=null&&reasonsforFiveDto.getReason().getThirdReason3()==true){
                             smallClass+="82521003,";
                             smallName+="等待质量总局结论,";
                         }
                         if(reasonsforFiveDto.getReason().getThirdReason4()!=null&&reasonsforFiveDto.getReason().getThirdReason4()==true){
                             smallClass+="82521004,";
                             smallName+="等待保险公司结论,";
                         }
                         if(reasonsforFiveDto.getReason().getThirdReason5()!=null&&reasonsforFiveDto.getReason().getThirdReason5()==true){
                             smallClass+="82521005,";
                             smallName+="等待消防结论,";
                         }
                         if(reasonsforFiveDto.getReason().getThirdReason6()!=null&&reasonsforFiveDto.getReason().getThirdReason6()==true){
                             complaintNotCloseCaseReasonDTO.setOther(reasonsforFiveDto.getReason().getInput5());
                         }else {
                             complaintNotCloseCaseReasonDTO.setOther("");
                         }
//                         if(!StringUtils.isNullOrEmpty(smallClass)){
//                             if(",".equals(smallClass.substring(smallClass.length()-1, smallClass.length()))){
//                                 smallClass= smallClass.substring(0, smallClass.length() -1);
//                             }
//                         }if(!StringUtils.isNullOrEmpty(smallName)){
//                             if(",".equals(smallName.substring(smallName.length()-1, smallName.length()))){
//                                 smallName= smallName.substring(0, smallName.length() -1);
//                             }
//                         }

                         complaintNotCloseCaseReasonDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
                         complaintNotCloseCaseReasonDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
                         complaintNotCloseCaseReasonDTO.setFollowTime(new Date());
                         complaintNotCloseCaseReasonDTO.setSmallClass(smallClass);
                         complaintNotCloseCaseReasonDTO.setSmallClassName(smallName);
                         complaintNotCloseCaseReasonDTO.setDuration(0);
                         row= insert(complaintNotCloseCaseReasonDTO);
                         break;
                         default:
                 }
             }
             return row;
          }

           @Override
           public IPage<ComplaintNotCloseCaseReasonTestDTO> selectPageBysql3(Page page, ComplaintNotCloseCaseReasonTestDTO complaintNotCloseCaseReasonTestDTO) {
               if(complaintNotCloseCaseReasonTestDTO ==null){
                   complaintNotCloseCaseReasonTestDTO =new ComplaintNotCloseCaseReasonTestDTO();
               }
               ComplaintNotCloseCaseReasonTestPO complaintNotCloseCaseReasonTestPo =complaintNotCloseCaseReasonTestDTO.transDtoToPo(ComplaintNotCloseCaseReasonTestPO.class);

               List<ComplaintNotCloseCaseReasonTestPO>list= complaintNotCloseCaseReasonMapper.selectPageBySql2(page,complaintNotCloseCaseReasonTestPo);
               if(CommonUtils.isNullOrEmpty(list)){
                   page.setRecords(new ArrayList<>());
                   return page;
               }else{
                   List<ComplaintNotCloseCaseReasonTestDTO>result=list.stream().map(m->m.transPoToDto(ComplaintNotCloseCaseReasonTestDTO.class)).collect(Collectors.toList());

                   page.setRecords(result);
                   return page;
               }
            }

             }
