package com.yonyou.dmscus.customer.service.common.ownervehicle;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.*;
import com.yonyou.dmscus.customer.entity.dto.common.VehicleDTO;

import java.util.List;

public interface VehicleService {


    VehicleDTO getVehicleByVin(String vin);

    /**通过vin查询*/
    VehicleDTO selectDateByVin(String vin);
}