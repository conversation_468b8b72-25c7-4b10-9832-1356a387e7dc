/*
 * Copyright (C), 2020-2020, 上海用友汽车有限公司
 * FileName: IMiddleGroundVehicleService.java
 * Author:   caizhongming
 * Date:     2020年5月28日 上午11:35:52
 * Description: //模块目的、功能描述      
 * History: //修改记录
 * <author>      <time>      <version>    <desc>
 * 修改人姓名             修改时间            版本号                  描述
 */
package com.yonyou.dmscus.customer.service.middleground;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.middleInterface.OwnerVehicleVO;

import java.util.Map;

/**
 * 〈一句话功能简述〉<br> 
 * 〈功能详细描述〉
 *
 * <AUTHOR>
 * @see [相关类/方法]（可选）
 * @since [产品/模块版本] （可选）
 */
public interface IMiddleGroundVehicleService {

	/**
	 * 功能描述: <br>
	 * 〈功能详细描述〉
	 * 根据vin查找车主车型数据
	 * @return
	 * @see [相关类/方法](可选)
	 * @since [产品/模块版本](可选)
	 */
	public IPage<OwnerVehicleVO> queryVehicleInfo(Page page, Map map);
}
