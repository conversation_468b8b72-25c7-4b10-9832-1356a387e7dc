package com.yonyou.dmscus.customer.service.impl.inviteRule;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.inviteRule.InviteRuleChangedRecordMapper;
import com.yonyou.dmscus.customer.dao.inviteRule.InviteRuleMapper;
import com.yonyou.dmscus.customer.dto.InvitationRuleVcdcParamsVo;
import com.yonyou.dmscus.customer.entity.dto.inviteRule.InviteRuleDTO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRuleChangedRecordPO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRulePO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.inviteRule.InviteRuleService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 邀约规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-17
 */
@Service
public class InviteRuleServiceImpl implements InviteRuleService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteRuleMapper inviteRuleMapper;
    @Resource
    InviteRuleChangedRecordMapper inviteRuleChangedRecordMapper;
    @Autowired
    BusinessPlatformService businessPlatformService;

    /**
     * 分页查询对应数据
     *
     * @param page          分页对象
     * @param inviteRuleDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.part.entity.dto.InviteRuleDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<InviteRuleDTO> selectPageBysql(Page page, InviteRuleDTO inviteRuleDTO) {
        if (inviteRuleDTO == null) {
            inviteRuleDTO = new InviteRuleDTO();
        }
        InviteRulePO inviteRulePO = inviteRuleDTO.transDtoToPo(InviteRulePO.class);

        List<InviteRulePO> list = inviteRuleMapper.selectPageBySql(page, inviteRulePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteRuleDTO> result = list.stream().map(m -> m.transPoToDto(InviteRuleDTO.class)).collect
                    (Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param inviteRuleDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.part.entity.dto.InviteRuleDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<InviteRuleDTO> selectListBySql(InviteRuleDTO inviteRuleDTO) {
        if (inviteRuleDTO == null) {
            inviteRuleDTO = new InviteRuleDTO();
        }
        InviteRulePO inviteRulePO = inviteRuleDTO.transDtoToPo(InviteRulePO.class);
        List<InviteRulePO> list = inviteRuleMapper.selectListBySql(inviteRulePO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteRuleDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.part.entity.dto.InviteRuleDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public InviteRuleDTO getById(Long id) {
        InviteRulePO inviteRulePO = inviteRuleMapper.selectById(id);
        if (inviteRulePO != null) {
            return inviteRulePO.transPoToDto(InviteRuleDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param inviteRuleDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(InviteRuleDTO inviteRuleDTO) {
        //对对象进行赋值操作
        InviteRulePO inviteRulePO = inviteRuleDTO.transDtoToPo(InviteRulePO.class);
        //执行插入
        int row = inviteRuleMapper.insert(inviteRulePO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id            主键ID
     * @param inviteRuleDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, InviteRuleDTO inviteRuleDTO) {
        InviteRulePO inviteRulePO = inviteRuleMapper.selectById(id);
        //对对象进行赋值操作
        inviteRuleDTO.transDtoToPo(inviteRulePO);
        //执行更新
        int row = inviteRuleMapper.updateById(inviteRulePO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = inviteRuleMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = inviteRuleMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }


    /**
     * 保存邀约规则
     *
     * @param dto
     * @return
     */
    @Override
    public int saveInviteRule(InviteRuleDTO dto) {

        if (dto.getId()==null) {
            InviteRulePO po = dto.transDtoToPo(InviteRulePO.class);
            if (!checkAdd(po)) {
                throw new DALException("新增邀约类型的规则已存在,无法新增!");
            }
            //厂端设置
            po.setDealerCode("VCDC");
            inviteRuleMapper.insert(po);
        } else{
            InviteRulePO inviteRulePO = inviteRuleMapper.selectById(dto.getId());
            //比较变更数据，保存变更记录
            this.saveInviteRuleChangedLog(inviteRulePO,dto);
            //对对象进行赋值操作
            dto.transDtoToPo(inviteRulePO);
            inviteRulePO.setInviteRule(null);
            inviteRulePO.setInviteType(null);
            //执行更新
            inviteRuleMapper.updateById(inviteRulePO);
        }
        return 1;
    }

    /**
     * 比较变更数据，保存变更记录
     * @param inviteRulePO
     * @param dto
     */
    private void saveInviteRuleChangedLog(InviteRulePO inviteRulePO, InviteRuleDTO dto) {
        InviteRuleChangedRecordPO insertPo = new InviteRuleChangedRecordPO();
        insertPo.setDealerCode(dto.getDealerCode());
        insertPo.setInviteType(inviteRulePO.getInviteType());
        insertPo.setInviteRule(inviteRulePO.getInviteRule());
        insertPo.setLastRuleValue(inviteRulePO.getRuleValue());
        insertPo.setLastRemindInterval(inviteRulePO.getRemindInterval());
        insertPo.setLastCloseInterval(inviteRulePO.getCloseInterval());
        insertPo.setLastIsUse(inviteRulePO.getIsUse());
        insertPo.setRuleValue(dto.getRuleValue());
        insertPo.setRemindInterval(dto.getRemindInterval());
        insertPo.setCloseInterval(dto.getCloseInterval());
        insertPo.setIsUse(dto.getIsUse());
        insertPo.setUpdateIsExecute(this.checkHasChanged(insertPo));
        inviteRuleChangedRecordMapper.insert(insertPo);
    }

    /**
     * 检查是否影响有邀约任务和线索改变
     * @param insertPo
     * @return
     */
    private Boolean checkHasChanged(InviteRuleChangedRecordPO insertPo) {
        if((insertPo.getLastRuleValue()!=null&&insertPo.getRuleValue()!=null
                &&insertPo.getLastRuleValue().equals(insertPo.getRuleValue()))
                ||(insertPo.getLastRuleValue()==null&&insertPo.getRuleValue()==null)){
        }else{
            if(insertPo.getInviteType().equals(CommonConstants.INVITE_TYPE_FIXED_WARRANTY)){
                inviteRuleChangedRecordMapper.updateIsExecute(insertPo.getDealerCode(),insertPo.getInviteType(),
                        null);
            }else{
                inviteRuleChangedRecordMapper.updateIsExecute(insertPo.getDealerCode(),insertPo.getInviteType(),
                        insertPo.getInviteRule());
            }

            return false;
        }
        if((insertPo.getLastCloseInterval()!=null&&insertPo.getCloseInterval()!=null
                &&insertPo.getLastCloseInterval().equals(insertPo.getCloseInterval()))
                ||(insertPo.getLastCloseInterval()==null&&insertPo.getCloseInterval()==null)){
        }else{
            if(insertPo.getInviteType().equals(CommonConstants.INVITE_TYPE_FIXED_WARRANTY)){
                inviteRuleChangedRecordMapper.updateIsExecute(insertPo.getDealerCode(),insertPo.getInviteType(),
                        null);
            }else{
                inviteRuleChangedRecordMapper.updateIsExecute(insertPo.getDealerCode(),insertPo.getInviteType(),
                        insertPo.getInviteRule());
            }
            return false;
        }
        if((insertPo.getLastRemindInterval()!=null&&insertPo.getRemindInterval()!=null
                &&insertPo.getLastRemindInterval().equals(insertPo.getRemindInterval()))
                ||(insertPo.getLastRemindInterval()==null&&insertPo.getRemindInterval()==null)){
        }else{
            if(insertPo.getInviteType().equals(CommonConstants.INVITE_TYPE_FIXED_WARRANTY)){
                inviteRuleChangedRecordMapper.updateIsExecute(insertPo.getDealerCode(),insertPo.getInviteType(),
                        null);
            }else{
                inviteRuleChangedRecordMapper.updateIsExecute(insertPo.getDealerCode(),insertPo.getInviteType(),
                        insertPo.getInviteRule());
            }
            return false;
        }
        insertPo.setRemark("影响数据未改变");
        return true;
    }


    @Override
    public List<InviteRuleDTO> getInvitationRuleDlr(InviteRuleDTO inviteRuleDTO) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        inviteRuleDTO.setDealerCode(loginInfoDto.getOwnerCode());
        InviteRulePO po = inviteRuleDTO.transDtoToPo(InviteRulePO.class);
        List<InviteRulePO> list = inviteRuleMapper.getInvitationRuleDlr(po);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteRuleDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 邀约设置(店端)
     *
     * @param dto
     * @return
     */
    @Override
    public int saveInviteRuleDlr(InviteRuleDTO dto) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        InviteRulePO inviteRulePO = inviteRuleMapper.selectById(dto.getId());
            if ("VCDC".equals(dto.getDealerCode())) {
                this.saveInviteRuleChangedLog(inviteRulePO,dto);
                dto.transDtoToPo(inviteRulePO);
                inviteRulePO.setId(null);
                inviteRulePO.setUpdatedBy(String.valueOf(loginInfoDto.getUserId()));
                inviteRulePO.setUpdatedAt(new Date());
                inviteRuleMapper.insert(inviteRulePO);

            } else {
                this.saveInviteRuleChangedLog(inviteRulePO,dto);
                dto.transDtoToPo(inviteRulePO);
                inviteRulePO.setUpdatedBy(String.valueOf(loginInfoDto.getUserId()));
                inviteRulePO.setUpdatedAt(new Date());
                inviteRuleMapper.updateById(inviteRulePO);
            }
        return 1;
    }

    /**
     * @param vo
     * @return
     */
    @Override
    public List<InviteRuleDTO> getInvitationRuleVcdc(InvitationRuleVcdcParamsVo vo) {
        List<InviteRuleDTO> rs = new ArrayList<InviteRuleDTO>();
        String areaId = null;
        if(vo.getAreaId()!=null){
            areaId=vo.getAreaId();
        }else if(vo.getAreaManageId()!=null){
            areaId=vo.getAreaManageId();
        }
        List<String> codes = businessPlatformService.getDealercodes(areaId,vo.getLargeAreaId(),vo.getDealerCode(),vo.getDealerName());
        for (String dealerCode : codes) {
            List<InviteRuleDTO> list = inviteRuleMapper.getInvitationRuleVcdc(dealerCode).stream().map(m -> m
                    .transPoToDto(InviteRuleDTO.class)).collect(Collectors.toList());
            rs.addAll(list);
        }
        return rs;
    }

    @Override
    public List<InviteRulePO> getRegularMaintainRule(String dealerCode) {
        return inviteRuleMapper.getRegularMaintainRule(dealerCode);
    }


    private Boolean checkAdd(InviteRulePO po) {
        Integer rs = inviteRuleMapper.checkAdd(po.getInviteType(), po.getInviteRule());
        if (rs != null) {
            return false;
        }
        return true;
    }

}


