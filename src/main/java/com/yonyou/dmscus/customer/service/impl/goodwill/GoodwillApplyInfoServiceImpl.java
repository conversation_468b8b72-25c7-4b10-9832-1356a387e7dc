package com.yonyou.dmscus.customer.service.impl.goodwill;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.cyxdms.common.utils.RedisUtil;
import com.yonyou.dmscloud.framework.dao.DAOUtil;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.common.AjaxResponse;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.goodwill.*;
import com.yonyou.dmscus.customer.dto.EmailInfoDto;
import com.yonyou.dmscus.customer.dto.QueryUserByOrgTypeDTO;
import com.yonyou.dmscus.customer.dto.UserOrgInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.IsExistByCodeDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.OwnerVehicleVO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.*;
import com.yonyou.dmscus.customer.entity.dto.goodwill.*;
import com.yonyou.dmscus.customer.entity.po.goodwill.*;
import com.yonyou.dmscus.customer.feign.*;
import com.yonyou.dmscus.customer.feign.dto.ListBindRelationDto;
import com.yonyou.dmscus.customer.feign.vo.ListBindRelationVo;
import com.yonyou.dmscus.customer.middleInterface.RequestDTO;
import com.yonyou.dmscus.customer.middleInterface.*;
import com.yonyou.dmscus.customer.service.CommonService;
import com.yonyou.dmscus.customer.service.common.HttpLogService;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyInfoService;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyMailHistoryService;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillAuditProcessService;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillStampsService;
import com.yonyou.dmscus.customer.util.common.BeanUtils;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.dmscus.customer.util.common.Utility;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestTemplate;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 亲善预申请单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
@Service
public class GoodwillApplyInfoServiceImpl extends ServiceImpl<GoodwillApplyInfoMapper, GoodwillApplyInfoPO>
		implements GoodwillApplyInfoService {
	/**
	 * 日志对象
	 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	/** 接口调用成功的返回状态 */
	public static final String SUCCESS_CODE = "0";
	@Autowired
	GoodwillAuditProcessService goodwillAuditProcessService;
	@Autowired
	GoodwillStampsService goodwillStampsService;
	@Autowired
	GoodwillApplyMailHistoryService goodwillApplyMailHistoryService;
	@Resource
	GoodwillApplyInfoMapper goodwillApplyInfoMapper;
	@Resource
	GoodwillMaterialAuditMapper goodwillMaterialAuditMapper;
	@Resource
	GoodwillApplyRepairInfoMapper goodwillApplyRepairInfoMapper;
	@Resource
	GoodwillApplyRepairPartInfoMapper goodwillApplyRepairPartInfoMapper;
	@Resource
	GoodwillApplyFinalPartInfoMapper goodwillApplyFinalPartInfoMapper;
	@Resource
	GoodwilApplyAuditProcessMapper goodwilApplyAuditProcessMapper;
	@Resource
	GoodwillAuditInfoMapper goodwillAuditInfoMapper;
	@Resource
	GoodwillDealerMailInfoMapper goodwillDealerMailInfoMapper;
	@Resource
	GoodwillApplyFileInfoMapper goodwillApplyFileInfoMapper;
	@Resource
	GoodwillNoticeInvoiceInfoMapper goodwillNoticeInvoiceInfoMapper;

	@Resource
	GoodwillInvoiceRecordMapper goodwillInvoiceRecordMapper;
	@Resource
	GoodwillMailTemplateMaintainMapper goodwillMailTemplateMaintainMapper;
	@Resource
	CouponMapper couponMapper;
	@Resource
	CouponUrStandardDealerMapper couponUrStandardDealerMapper;
	@Autowired
	BusinessPlatformService businessPlatformService;
	@Autowired
	private GoodwillApplyInfoServiceHelper goodwillApplyInfoServiceHelper;
	@Autowired
	CommonService commonService;
	@Resource
	private RestTemplate directRestTemplate;
	@Autowired
	private MidUrlProperties midUrlProperties;
	@Autowired
	private VehicleOwnershipClient vehicleOwnershipClient;

	/**日志服务*/
	@Resource
	private HttpLogService httpLogService;
	@Autowired
	ReportCommonClient reportCommonClient;
	@Autowired
	StringRedisTemplate redisTemplate;
	@Resource
	private WhitelistQueryService whitelistQueryService;

	@Autowired
	MidEndAuthCenterClient midEndAuthCenterClient;
	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillApplyInfoDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillApplyInfoDTO> selectPageBysql(Page page, GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		if (goodwillApplyInfoDTO == null) {
			goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		}
		// GoodwillApplyInfoPO goodwillApplyInfoPo =
		// goodwillApplyInfoDTO.transDtoToPo(GoodwillApplyInfoPO.class);

		List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.selectGoodwillApplyInfo(page, goodwillApplyInfoDTO);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillApplyInfoDTO> result = list.stream().map(m -> m.transPoToDto(GoodwillApplyInfoDTO.class))
					.collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillApplyInfoDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillApplyInfoDTO> getOemTodoByPage(Page page, GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		if (goodwillApplyInfoDTO == null) {
			goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		}
		// GoodwillApplyInfoPO goodwillApplyInfoPo =
		// goodwillApplyInfoDTO.transDtoToPo(GoodwillApplyInfoPO.class);

		if (!(goodwillApplyInfoDTO.getRoleList().length>0) && StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getRoleList1()) && !(goodwillApplyInfoDTO.getRoleList2().length>0)){
			page.setRecords(new ArrayList<>());
			return page;
		}

		List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.selectOemTodoGoodwillApplyInfo(page,
				goodwillApplyInfoDTO);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillApplyInfoDTO> result = list.stream().map(m -> m.transPoToDto(GoodwillApplyInfoDTO.class))
					.collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillApplyInfoDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillApplyInfoDTO> getOemTodoListByPage(Page page, GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		if (goodwillApplyInfoDTO == null) {
			goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		}
		// GoodwillApplyInfoPO goodwillApplyInfoPo =
		// goodwillApplyInfoDTO.transDtoToPo(GoodwillApplyInfoPO.class);

		List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.selectOemTodoListGoodwillApplyInfo(page,
				goodwillApplyInfoDTO);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillApplyInfoDTO> result = list.stream().map(m -> m.transPoToDto(GoodwillApplyInfoDTO.class))
					.collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillApplyInfoDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillApplyInfoDTO> getOemSearchByPage(Page page, GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		if (goodwillApplyInfoDTO == null) {
			goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		}
		// GoodwillApplyInfoPO goodwillApplyInfoPo =
		// goodwillApplyInfoDTO.transDtoToPo(GoodwillApplyInfoPO.class);
		List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.selectOemSearchGoodwillApplyInfo(page,
				goodwillApplyInfoDTO);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			Map<Long,BigDecimal> map = new HashMap<>();
			List<GoodwillApplyInfoDTO> result = list.stream().map(m -> m.transPoToDto(GoodwillApplyInfoDTO.class))
					.collect(Collectors.toList());
			StringBuffer buf = new StringBuffer(0);
			String couponId = "";
			for (GoodwillApplyInfoDTO dto : result) {
				if (!StringUtils.isNullOrEmpty(dto.getCouponId())) {
					buf.append(dto.getCouponId() + ",");
					map.put(dto.getCouponId(),dto.getCostRate());
					//TODO:xx
				}
			}
			if (buf.length() > 0) {
				couponId = buf.toString().substring(0, buf.toString().lastIndexOf(","));
			}
			if (!StringUtils.isNullOrEmpty(couponId)) {
				List<Map> list1 =  reportCommonClient.couponVerify(couponId);
				logger.info("reportCommonClient.couponVerify({})={}",couponId,JSONObject.toJSON(list1));
				Map<String,List<String>> mapMap = new HashMap<>();
				if(!CommonUtils.isNullOrEmpty(list1)){
					this.getCouponIdMap(list1,mapMap);
				}
				//List<Map> interList = this.getLeftValue(StrUtil.splitToLong(couponId, ","));
				//Map map1 = new HashMap<>(16);
				for (GoodwillApplyInfoDTO dtos : result) {
					if (!StringUtils.isNullOrEmpty(dtos.getCouponId())) {
						//	if (!CommonUtils.isNullOrEmpty(interList)) {
						//	for (Object object : interList) {
						//		JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
						//	map1 = jsonObject.toJavaObject(Map.class);
						//	if (map1.get("couponId").toString().equals(dtos.getCouponId().toString())) {
						//	if (!StringUtils.isNullOrEmpty(map1.get("leftValue"))) {
//										dtos.setLeftAmount(new BigDecimal(map1.get("leftValue").toString())
//												.multiply(new BigDecimal(0.01)));
//										dtos.setUsedAmount(dtos.getRechargeAmount()
//												.subtract(new BigDecimal(map1.get("leftValue").toString())
//														.divide(new BigDecimal(100))));
						//dtos.setLeftAmount(new BigDecimal(map1.get("leftValue").toString())
						//		.multiply(new BigDecimal(0.01)).multiply(map.get(dtos.getCouponId())));
						//}
						if (!StringUtils.isNullOrEmpty(mapMap.get(dtos.getCouponId().toString()))) {
							List<String> objectList = mapMap.get(dtos.getCouponId().toString());
							for (String o : objectList) {
								if (dtos.getCostConsumeAmount() == null) {
									dtos.setCostConsumeAmount(new BigDecimal(0).add(new BigDecimal(o).divide(new BigDecimal(100)).multiply(map.get(dtos.getCouponId()))));
									dtos.setUsedAmount(new BigDecimal(0).add(new BigDecimal(o).divide(new BigDecimal(100))));
								} else {
									dtos.setCostConsumeAmount(dtos.getCostConsumeAmount().add(new BigDecimal(o).divide(new BigDecimal(100)).multiply(map.get(dtos.getCouponId()))));
									dtos.setUsedAmount(dtos.getUsedAmount().add(new BigDecimal(o).divide(new BigDecimal(100))));
								}
							}

						}

						dtos.setLeftAmount(dtos.getRechargeAmount().subtract(dtos.getCostConsumeAmount()==null ?new BigDecimal(0):dtos.getCostConsumeAmount()));
						int  max = 	dtos.getLeftAmount().compareTo(new BigDecimal(0));
						if(max <0){
							dtos.setLeftAmount(new BigDecimal(0));
						}

						//			dtos.setUsedAmount(dtos.getRechargeAmount().subtract(dtos.getLeftAmount()==null?new BigDecimal(0):dtos.getLeftAmount()));
						//		}
						//	}
						//}

					}
				}

			}

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 卡券充值接口
	 *
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	public List<Map> getLeftValue(long[] ls) {
		String successCodes = "0";
		QueryCouponDetailInfoDTO dtos = new QueryCouponDetailInfoDTO();
		dtos.setCouponIds(ls);
		dtos.setKindness(1);
		RequestDTO<QueryCouponDetailInfoDTO> requestDto = new RequestDTO<QueryCouponDetailInfoDTO>();
		requestDto.setData(dtos);
		String requestUrls = midUrlProperties.getMidEndCouponCenter() + midUrlProperties.getAllEx();
		HttpHeaders httpHeaders = new HttpHeaders();
		logger.error("中台卡券查询参数：{}", JSON.toJSONString(requestDto));
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<RequestDTO> httpEntity = new HttpEntity<RequestDTO>(requestDto, httpHeaders);
		List<Map> interList = new ArrayList<>();
		Map map1 = new HashMap<>(16);
		// 调用接口
		try {
			ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrls, HttpMethod.POST, httpEntity,
					ResponseDTO.class);
			logger.info("中台卡券查询结果：{}", JSON.toJSONString(responseEntity));
			if (responseEntity.getBody() != null) {
				if (SUCCESS_CODE.equals(responseEntity.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					interList = objectMapper.convertValue(responseEntity.getBody().getData(), List.class);

				} else {
					logger.error("中台卡券查询报错：{}", responseEntity.getBody().getReturnMessage());
					throw new ServiceBizException("中台查询卡券接口失败");
				}
			} else {
				logger.error("中台卡券查询报错：{}", responseEntity.getBody().getReturnMessage());
				throw new ServiceBizException("中台查询卡券接口失败");
			}
		} catch (Exception e) {
			logger.error("中台卡券查询报错：{}", e.getMessage());
			throw new ServiceBizException("中台查询卡券接口失败");
		}
		return interList;
	}

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillApplyInfoDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillApplyInfoDTO> querySupportApplyDealerSearchInfo(Page page,
																		 GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		if (goodwillApplyInfoDTO == null) {
			goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		}
		// GoodwillApplyInfoPO goodwillApplyInfoPo =
		// goodwillApplyInfoDTO.transDtoToPo(GoodwillApplyInfoPO.class);

		List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.querySupportApplyDealerSearchInfo(page,
				goodwillApplyInfoDTO);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			Map<Long,BigDecimal> map = new HashMap<>();
			List<GoodwillApplyInfoDTO> result = list.stream().map(m -> m.transPoToDto(GoodwillApplyInfoDTO.class))
					.collect(Collectors.toList());
			StringBuffer buf = new StringBuffer(0);
			String couponId = "";
			for (GoodwillApplyInfoDTO dto : result) {
				if (!StringUtils.isNullOrEmpty(dto.getCouponId())) {
					buf.append(dto.getCouponId() + ",");
					map.put(dto.getCouponId(),dto.getCostRate());
					//TODO:xx
				}
			}
			if (buf.length() > 0) {
				couponId = buf.toString().substring(0, buf.toString().lastIndexOf(","));
			}
			if (!StringUtils.isNullOrEmpty(couponId)) {
				List<Map> list1 =  reportCommonClient.couponVerify(couponId);
				logger.info("reportCommonClient.couponVerify({})={}",couponId,JSONObject.toJSON(list1));
				Map<String,List<String>> mapMap = new HashMap<>();
				if(!CommonUtils.isNullOrEmpty(list1)){
					this.getCouponIdMap(list1,mapMap);
				}
				//List<Map> interList = this.getLeftValue(StrUtil.splitToLong(couponId, ","));
				//Map map1 = new HashMap<>(16);
				for (GoodwillApplyInfoDTO dtos : result) {
					if (!StringUtils.isNullOrEmpty(dtos.getCouponId())) {
						//	if (!CommonUtils.isNullOrEmpty(interList)) {
						//	for (Object object : interList) {
						//		JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
						//	map1 = jsonObject.toJavaObject(Map.class);
						//	if (map1.get("couponId").toString().equals(dtos.getCouponId().toString())) {
						//	if (!StringUtils.isNullOrEmpty(map1.get("leftValue"))) {
//										dtos.setLeftAmount(new BigDecimal(map1.get("leftValue").toString())
//												.multiply(new BigDecimal(0.01)));
//										dtos.setUsedAmount(dtos.getRechargeAmount()
//												.subtract(new BigDecimal(map1.get("leftValue").toString())
//														.divide(new BigDecimal(100))));
						//dtos.setLeftAmount(new BigDecimal(map1.get("leftValue").toString())
						//		.multiply(new BigDecimal(0.01)).multiply(map.get(dtos.getCouponId())));
						//}
						if (!StringUtils.isNullOrEmpty(mapMap.get(dtos.getCouponId().toString()))) {
							List<String> objectList = mapMap.get(dtos.getCouponId().toString());
							for (String o : objectList) {
								if (dtos.getCostConsumeAmount() == null) {
									dtos.setCostConsumeAmount(new BigDecimal(0).add(new BigDecimal(o).divide(new BigDecimal(100)).multiply(map.get(dtos.getCouponId()))));
									dtos.setUsedAmount(new BigDecimal(0).add(new BigDecimal(o).divide(new BigDecimal(100))));
								} else {
									dtos.setCostConsumeAmount(dtos.getCostConsumeAmount().add(new BigDecimal(o).divide(new BigDecimal(100)).multiply(map.get(dtos.getCouponId()))));
									dtos.setUsedAmount(dtos.getUsedAmount().add(new BigDecimal(o).divide(new BigDecimal(100))));
								}
							}

						}

						dtos.setLeftAmount(dtos.getRechargeAmount().subtract(dtos.getCostConsumeAmount()==null ?new BigDecimal(0):dtos.getCostConsumeAmount()));
						int  max = 	dtos.getLeftAmount().compareTo(new BigDecimal(0));
						if(max <0){
							dtos.setLeftAmount(new BigDecimal(0));
						}

						//			dtos.setUsedAmount(dtos.getRechargeAmount().subtract(dtos.getLeftAmount()==null?new BigDecimal(0):dtos.getLeftAmount()));
						//		}
						//	}
						//}

					}
				}

			}
			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillApplyInfoDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillApplyInfoDTO> selectListBySql(GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		if (goodwillApplyInfoDTO == null) {
			goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		}
		GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoDTO.transDtoToPo(GoodwillApplyInfoPO.class);
		List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.selectListBySql(goodwillApplyInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillApplyInfoDTO.class)).collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillApplyInfoDTO getById(Long id) {
		GoodwillApplyInfoDTO goodwillApplyInfoDTO = goodwillApplyInfoMapper.getByApplyId(id);
		if (goodwillApplyInfoDTO != null) {
			return goodwillApplyInfoDTO;
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillApplyInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@SuppressWarnings("rawtypes")
	@Override
	@Transactional
	public int insert(GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		logger.info("开始保存——————————————————————————————————————————————————————————————");
		// 获取登录用户信息
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		List<Integer> list = goodwillApplyInfoDTO.getCustomerPainVue();
		// 客户痛点数据处理
		if (!CommonUtils.isNullOrEmpty(list)) {
			StringBuilder result = new StringBuilder();
			boolean flag = false;
			for (Integer integer : list) {
				if (flag) {
					result.append(",");
				} else {
					flag = true;
				}
				result.append(integer);
			}
			goodwillApplyInfoDTO.setCustomerPain(result.toString());
		}

		if (StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getId())) {
			// 对对象进行赋值操作
			GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoDTO.transDtoToPo(GoodwillApplyInfoPO.class);
			goodwillApplyInfoPo.setGoodwillStatus(82551001);
			goodwillApplyInfoPo.setApplyPerson(loginInfoDto.getUserName());
			goodwillApplyInfoPo.setUpdatedPerson(loginInfoDto.getUserName());
			// 执行插入
			int row = goodwillApplyInfoMapper.insert(goodwillApplyInfoPo);
			// 维修记录插入
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getGoodwillApplyRepairInfoDTO())) {
				for (GoodwillApplyRepairInfoDTO repairInfoDto : goodwillApplyInfoDTO.getGoodwillApplyRepairInfoDTO()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					GoodwillApplyRepairInfoPO goodwillApplyRepairInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyRepairInfoPO.class);
					goodwillApplyRepairInfoMapper.insert(goodwillApplyRepairInfoPo);

				}
			}
			// 维修零配件插入
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getGoodwillApplyRepairPartInfoDTO())) {
				for (GoodwillApplyRepairPartInfoDTO repairInfoDto : goodwillApplyInfoDTO
						.getGoodwillApplyRepairPartInfoDTO()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					GoodwillApplyRepairPartInfoPO goodwillApplyRepairPartInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyRepairPartInfoPO.class);
					goodwillApplyRepairPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);

				}
			}
			// 亲善类型及金额-保养成本
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getMaintainCostTable())) {
				for (GoodwillApplyFinalPartInfoDTO repairInfoDto : goodwillApplyInfoDTO.getMaintainCostTable()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					repairInfoDto.setGoodwillType(0);
					GoodwillApplyFinalPartInfoPO goodwillApplyRepairPartInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyFinalPartInfoPO.class);
					goodwillApplyRepairPartInfoPo.setQuantity(new BigDecimal(repairInfoDto.getQuantity()));
					goodwillApplyRepairPartInfoPo.setUnitPriceTax(new BigDecimal(repairInfoDto.getUnitPriceTax()));
					goodwillApplyRepairPartInfoPo
							.setSupportProportion(new BigDecimal(repairInfoDto.getSupportProportion()));
					goodwillApplyFinalPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);

				}
			}
			// 亲善类型及金额-延保成本
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getExtendWarrantyCostTable())) {
				for (GoodwillApplyFinalPartInfoDTO repairInfoDto : goodwillApplyInfoDTO.getExtendWarrantyCostTable()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					repairInfoDto.setGoodwillType(1);
					GoodwillApplyFinalPartInfoPO goodwillApplyRepairPartInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyFinalPartInfoPO.class);
					goodwillApplyRepairPartInfoPo.setQuantity(new BigDecimal(repairInfoDto.getQuantity()));
					goodwillApplyRepairPartInfoPo.setUnitPriceTax(new BigDecimal(repairInfoDto.getUnitPriceTax()));
					goodwillApplyRepairPartInfoPo
							.setSupportProportion(new BigDecimal(repairInfoDto.getSupportProportion()));
					goodwillApplyFinalPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);

				}
			}
			// 亲善类型及金额-附件精品成本
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getAccessoryCostTable())) {
				for (GoodwillApplyFinalPartInfoDTO repairInfoDto : goodwillApplyInfoDTO.getAccessoryCostTable()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					repairInfoDto.setGoodwillType(2);
					GoodwillApplyFinalPartInfoPO goodwillApplyRepairPartInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyFinalPartInfoPO.class);
					goodwillApplyRepairPartInfoPo.setQuantity(new BigDecimal(repairInfoDto.getQuantity()));
					goodwillApplyRepairPartInfoPo.setUnitPriceTax(new BigDecimal(repairInfoDto.getUnitPriceTax()));
					goodwillApplyRepairPartInfoPo
							.setSupportProportion(new BigDecimal(repairInfoDto.getSupportProportion()));
					goodwillApplyFinalPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);

				}
			}
			// 亲善信息插入
			GoodwillMaterialAuditDTO goodwillMaterialAuditDTO = new GoodwillMaterialAuditDTO();
			goodwillMaterialAuditDTO.setGoodwillApplyId(goodwillApplyInfoPo.getId());
			goodwillMaterialAuditDTO.setCustomerBackground(goodwillApplyInfoDTO.getCustomerBackground());
			goodwillMaterialAuditDTO.setReasonAndDispose(goodwillApplyInfoDTO.getReasonAndDispose());
			goodwillMaterialAuditDTO.setRepairSolution(goodwillApplyInfoDTO.getRepairSolution());
			goodwillMaterialAuditDTO.setCustomerRequire(goodwillApplyInfoDTO.getCustomerRequire());
			goodwillMaterialAuditDTO.setPotentialRisk(goodwillApplyInfoDTO.getPotentialRisk());
			goodwillMaterialAuditDTO.setVrOrTjNo(goodwillApplyInfoDTO.getVrOrTjNo());
			goodwillMaterialAuditDTO
					.setBusinessGoodwillApplyDetail(goodwillApplyInfoDTO.getBusinessGoodwillApplyDetail());
			GoodwillMaterialAuditPO goodwillMaterialAuditPo = goodwillMaterialAuditDTO
					.transDtoToPo(GoodwillMaterialAuditPO.class);
			goodwillMaterialAuditMapper.insert(goodwillMaterialAuditPo);
			//TODO:获取经销商角色如果是浙江区，金额大于10000.00转大区总监
			CompanyDetailDTO getRole   = getRole(goodwillApplyInfoDTO.getDealerCode());
			logger.info("getRole：{}",JSONObject.toJSON(getRole));
			//大区
			Long    afterBigAreaId = getRole.getAfterBigAreaId();
			//小区
			Long    afterSmallAreaId = getRole.getAfterSmallAreaId();
			//TODO: 跟进角色查询经销商判断售后大小区是否有售后经理
			Map<String,Long> mapvalus = this.check(afterBigAreaId,afterSmallAreaId);
			// 获取该申请单申请流程
			List<GoodwillAuditProcessDTO> auditList = goodwillAuditProcessService.selectList(0,
					goodwillApplyInfoDTO.getAuditType(), goodwillApplyInfoDTO.getApplyAmount(),mapvalus.get("afterSmallAreaId"),mapvalus.get("afterBigAreaId"));
			if (!CommonUtils.isNullOrEmpty(auditList)) {
				for (GoodwillAuditProcessDTO goodwillAuditProcessDTO : auditList) {
					// 获取角色CODE
					String roles = this.queryRole(goodwillAuditProcessDTO.getAuditObject(),
							goodwillAuditProcessDTO.getAuditType(),
							Integer.valueOf(goodwillAuditProcessDTO.getAuditPosition()));
//					if(afterSmallAreaId ==null  &&  "SHQYJL".equals(roles)){
//						continue;
//					}
//					if(afterBigAreaId ==null  &&  "SHDQJL".equals(roles)){
//						continue;
//					}
					GoodwilApplyAuditProcessDTO dto = new GoodwilApplyAuditProcessDTO();
					dto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					dto.setAuditObject(goodwillAuditProcessDTO.getAuditObject());
					dto.setAuditRole(roles);
					dto.setAuditType(goodwillApplyInfoPo.getAuditType());
					dto.setAuditPosition(goodwillAuditProcessDTO.getAuditPosition());
					dto.setAuditSort(Integer.valueOf(goodwillAuditProcessDTO.getAuditPosition()));
					GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo = dto
							.transDtoToPo(GoodwilApplyAuditProcessPO.class);
					goodwilApplyAuditProcessMapper.insert(goodwilApplyAuditProcessPo);

				}
			}

		} else {
			GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoMapper.selectById(goodwillApplyInfoDTO.getId());

			// 对对象进行赋值操作
			goodwillApplyInfoDTO.transDtoToPo(goodwillApplyInfoPo);
			if (StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getVolvoIntegral())) {
				goodwillApplyInfoPo.setVolvoIntegral(new BigDecimal(0));
			}
			if (StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getVoucherCost())) {
				goodwillApplyInfoPo.setVoucherCost(new BigDecimal(0));
			}
			if (StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getWalkingCarPrice())) {
				goodwillApplyInfoPo.setWalkingCarPrice(new BigDecimal(0));
			}
			if (StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getReturnChangeCarPrice())) {
				goodwillApplyInfoPo.setReturnChangeCarPrice(new BigDecimal(0));
			}
			if (StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getOtherPrice())) {
				goodwillApplyInfoPo.setOtherPrice(new BigDecimal(0));
			}
			goodwillApplyInfoPo.setUpdatedPerson(loginInfoDto.getUserName());
			// 执行更新
			int row = goodwillApplyInfoMapper.updateById(goodwillApplyInfoPo);

			// 维修记录更新
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getGoodwillApplyRepairInfoDTO())) {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoPo.getId());
				goodwillApplyRepairInfoMapper.deleteByMap(columnMap);
				for (GoodwillApplyRepairInfoDTO repairInfoDto : goodwillApplyInfoDTO.getGoodwillApplyRepairInfoDTO()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					GoodwillApplyRepairInfoPO goodwillApplyRepairInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyRepairInfoPO.class);
					goodwillApplyRepairInfoMapper.insert(goodwillApplyRepairInfoPo);

				}
			} else {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoPo.getId());
				goodwillApplyRepairInfoMapper.deleteByMap(columnMap);
			}
			// 维修零配件插入
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getGoodwillApplyRepairPartInfoDTO())) {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoPo.getId());
				goodwillApplyRepairPartInfoMapper.deleteByMap(columnMap);
				for (GoodwillApplyRepairPartInfoDTO repairInfoDto : goodwillApplyInfoDTO
						.getGoodwillApplyRepairPartInfoDTO()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					GoodwillApplyRepairPartInfoPO goodwillApplyRepairPartInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyRepairPartInfoPO.class);
					goodwillApplyRepairPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);

				}
			} else {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoPo.getId());
				goodwillApplyRepairPartInfoMapper.deleteByMap(columnMap);

			}

			// 亲善类型及金额-保养成本
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getMaintainCostTable())) {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoPo.getId());
				columnMap.put("goodwill_type", 0);
				goodwillApplyFinalPartInfoMapper.deleteByMap(columnMap);
				for (GoodwillApplyFinalPartInfoDTO repairInfoDto : goodwillApplyInfoDTO.getMaintainCostTable()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					repairInfoDto.setGoodwillType(0);
					GoodwillApplyFinalPartInfoPO goodwillApplyRepairPartInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyFinalPartInfoPO.class);
					goodwillApplyRepairPartInfoPo.setQuantity(new BigDecimal(repairInfoDto.getQuantity()));
					goodwillApplyRepairPartInfoPo.setUnitPriceTax(new BigDecimal(repairInfoDto.getUnitPriceTax()));
					goodwillApplyRepairPartInfoPo
							.setSupportProportion(new BigDecimal(repairInfoDto.getSupportProportion()));
					goodwillApplyFinalPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);

				}
			} else if (CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getMaintainCostTable())
					&& goodwillApplyInfoDTO.getIsMaintainChange() == 1) {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoPo.getId());
				columnMap.put("goodwill_type", 0);
				goodwillApplyFinalPartInfoMapper.deleteByMap(columnMap);

			}

			// 亲善类型及金额-延保成本
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getExtendWarrantyCostTable())) {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoPo.getId());
				columnMap.put("goodwill_type", 1);
				goodwillApplyFinalPartInfoMapper.deleteByMap(columnMap);
				for (GoodwillApplyFinalPartInfoDTO repairInfoDto : goodwillApplyInfoDTO.getExtendWarrantyCostTable()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					repairInfoDto.setGoodwillType(1);
					GoodwillApplyFinalPartInfoPO goodwillApplyRepairPartInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyFinalPartInfoPO.class);
					goodwillApplyRepairPartInfoPo.setQuantity(new BigDecimal(repairInfoDto.getQuantity()));
					goodwillApplyRepairPartInfoPo.setUnitPriceTax(new BigDecimal(repairInfoDto.getUnitPriceTax()));
					goodwillApplyRepairPartInfoPo
							.setSupportProportion(new BigDecimal(repairInfoDto.getSupportProportion()));
					goodwillApplyFinalPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);

				}
			} else if (CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getExtendWarrantyCostTable())
					&& goodwillApplyInfoDTO.getIsExtendWarrantyChange() == 1) {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoPo.getId());
				columnMap.put("goodwill_type", 1);
				goodwillApplyFinalPartInfoMapper.deleteByMap(columnMap);

			}
			// 亲善类型及金额-附件精品成本
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getAccessoryCostTable())) {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoPo.getId());
				columnMap.put("goodwill_type", 2);
				goodwillApplyFinalPartInfoMapper.deleteByMap(columnMap);
				for (GoodwillApplyFinalPartInfoDTO repairInfoDto : goodwillApplyInfoDTO.getAccessoryCostTable()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					repairInfoDto.setGoodwillType(2);
					GoodwillApplyFinalPartInfoPO goodwillApplyRepairPartInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyFinalPartInfoPO.class);
					goodwillApplyRepairPartInfoPo.setQuantity(new BigDecimal(repairInfoDto.getQuantity()));
					goodwillApplyRepairPartInfoPo.setUnitPriceTax(new BigDecimal(repairInfoDto.getUnitPriceTax()));
					goodwillApplyRepairPartInfoPo
							.setSupportProportion(new BigDecimal(repairInfoDto.getSupportProportion()));
					goodwillApplyFinalPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);

				}
			} else if (CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getAccessoryCostTable())
					&& goodwillApplyInfoDTO.getIsAccessoryChange() == 1) {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoPo.getId());
				columnMap.put("goodwill_type", 2);
				goodwillApplyFinalPartInfoMapper.deleteByMap(columnMap);

			}

			// 亲善信息插入
			GoodwillMaterialAuditPO goodwillMaterialAuditPo = goodwillMaterialAuditMapper
					.selectById(goodwillApplyInfoDTO.getMaterialId());

			GoodwillMaterialAuditDTO goodwillMaterialAuditDTO = new GoodwillMaterialAuditDTO();
			goodwillMaterialAuditDTO.setGoodwillApplyId(goodwillApplyInfoPo.getId());
			goodwillMaterialAuditDTO.setCustomerBackground(goodwillApplyInfoDTO.getCustomerBackground());
			goodwillMaterialAuditDTO.setReasonAndDispose(goodwillApplyInfoDTO.getReasonAndDispose());
			goodwillMaterialAuditDTO.setRepairSolution(goodwillApplyInfoDTO.getRepairSolution());
			goodwillMaterialAuditDTO.setCustomerRequire(goodwillApplyInfoDTO.getCustomerRequire());
			goodwillMaterialAuditDTO.setPotentialRisk(goodwillApplyInfoDTO.getPotentialRisk());
			goodwillMaterialAuditDTO.setVrOrTjNo(goodwillApplyInfoDTO.getVrOrTjNo());
			goodwillMaterialAuditDTO
					.setBusinessGoodwillApplyDetail(goodwillApplyInfoDTO.getBusinessGoodwillApplyDetail());
			if (goodwillMaterialAuditPo == null) {
				goodwillMaterialAuditPo = goodwillMaterialAuditDTO.transDtoToPo(GoodwillMaterialAuditPO.class);
				goodwillMaterialAuditMapper.insert(goodwillMaterialAuditPo);
			} else {
				goodwillMaterialAuditDTO.transDtoToPo(goodwillMaterialAuditPo);
				goodwillMaterialAuditMapper.updateById(goodwillMaterialAuditPo);
			}
			//TODO:获取经销商角色如果是浙江区，金额大于10000.00转大区总监
			CompanyDetailDTO  getRole   = getRole(goodwillApplyInfoDTO.getDealerCode());
			logger.info("getRole：{}",JSONObject.toJSON(getRole));
			Long    afterBigAreaId = getRole.getAfterBigAreaId();
			Long    afterSmallAreaId = getRole.getAfterSmallAreaId();
			//TODO: 跟进角色查询经销商判断售后大小区是否有售后经理
			Map<String,Long> mapvalus = this.check(afterBigAreaId,afterSmallAreaId);
			// 获取该申请单申请流程
			List<GoodwillAuditProcessDTO> auditList = goodwillAuditProcessService.selectList(0,
					goodwillApplyInfoDTO.getAuditType(), goodwillApplyInfoDTO.getApplyAmount(),mapvalus.get("afterSmallAreaId"),mapvalus.get("afterBigAreaId"));
			Map<String, Object> columnMap = new HashMap(16);
			columnMap.put("goodwill_apply_id", goodwillApplyInfoPo.getId());
			// columnMap.put("audit_object", 0);
			// columnMap.put("audit_type", goodwillApplyInfoDTO.getAuditType());
			goodwilApplyAuditProcessMapper.deleteByMap(columnMap);
			if (!CommonUtils.isNullOrEmpty(auditList)) {
				for (GoodwillAuditProcessDTO goodwillAuditProcessDTO : auditList) {
					// 获取角色CODE
					String roles = this.queryRole(goodwillAuditProcessDTO.getAuditObject(),
							goodwillAuditProcessDTO.getAuditType(),
							Integer.valueOf(goodwillAuditProcessDTO.getAuditPosition()));
//					if(afterSmallAreaId ==null  &&  "SHQYJL".equals(roles)){
//						continue;
//					}
//					if(afterBigAreaId ==null  &&  "SHDQJL".equals(roles)){
//						continue;
//					}
					GoodwilApplyAuditProcessDTO dto = new GoodwilApplyAuditProcessDTO();
					dto.setAuditRole(roles);
					dto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					dto.setAuditObject(goodwillAuditProcessDTO.getAuditObject());
					dto.setAuditType(goodwillApplyInfoPo.getAuditType());
					dto.setAuditPosition(goodwillAuditProcessDTO.getAuditPosition());
					dto.setAuditSort(Integer.valueOf(goodwillAuditProcessDTO.getAuditPosition()));
					GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo = dto
							.transDtoToPo(GoodwilApplyAuditProcessPO.class);
					goodwilApplyAuditProcessMapper.insert(goodwilApplyAuditProcessPo);

				}
			}

		}
		// 返回插入的值
		return 1;
	}

	private CompanyDetailDTO getRole(String dealerCode) {
		//查询经销商信息
		List<CompanyDetailDTO> list =  businessPlatformService.getDealer(null,null ,dealerCode,null);
		if(list ==null || list.size()<1){
			throw new DALException("经销商信息异常，请稍后再试");
		}
		return 	list.get(0);
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillApplyInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoMapper.selectById(id);
		// 对对象进行赋值操作
		goodwillApplyInfoDTO.transDtoToPo(goodwillApplyInfoPo);
		goodwillApplyInfoPo.setUpdatedPerson(loginInfoDto.getUserName());
		// 执行更新
		int row = goodwillApplyInfoMapper.updateById(goodwillApplyInfoPo);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillApplyInfoMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillApplyInfoMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<List>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<List> queryComplaintInfo(Page page, Map map) {
		// 接口数据
		List<Map> result = goodwillApplyInfoMapper.queryComplaintInfo(page, map);
		Integer count = goodwillApplyInfoMapper.queryComplaintCountInfo(map);
		page.setRecords(result);
		page.setTotal(count);
		return page;
		// }
	}

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<List> * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@SuppressWarnings("unchecked")
	@Override
	public IPage<OwnerVehicleVO> queryVehicleInfo(Page page, Map map1) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String successCode = "0";
		String requestUrl = midUrlProperties.getMidEndVehicleCenter() + midUrlProperties.getListOwnerVehiclePage();
		Map<String, Object> map = new HashMap<String, Object>();
		// 查询对象参数转换MAP
		map.put("data", map1);
		map.put("page", page.getCurrent());
		map.put("pageSize", page.getSize());
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity httpEntity = new HttpEntity<>(map, httpHeaders);
		ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrl, HttpMethod.POST, httpEntity,
				ResponseDTO.class);
		if (responseEntity.getBody() != null) {
			if (successCode.equals(responseEntity.getBody().getReturnCode())) {
				ObjectMapper objectMapper = new ObjectMapper();
				objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
				page = objectMapper.convertValue(responseEntity.getBody().getData(), Page.class);
				List<OwnerVehicleVO> list = objectMapper.convertValue(page.getRecords(),
						new TypeReference<List<OwnerVehicleVO>>() {
						});
				if (!CommonUtils.isNullOrEmpty(list)) {
					for (OwnerVehicleVO ownerVehicleVo : list) {
						ownerVehicleVo.setMobile(null);
						if (StringUtils.isNullOrEmpty(ownerVehicleVo.getPlateNumber())) {
							ownerVehicleVo.setPlateNumber("无车牌");
						}
						// 根据VIN查询延保信息
						Map vinMap = goodwillApplyInfoMapper.queryExtendWarrantyByVin(ownerVehicleVo.getVin());
						if (!StringUtils.isNullOrEmpty(vinMap)) {
							ownerVehicleVo.setIsExtendWarranty(CommonConstants.DICT_IS_YES);
							if (!StringUtils.isNullOrEmpty(vinMap.get("product_name"))) {
								ownerVehicleVo.setExtendWarrantyName(vinMap.get("product_name").toString());
							}
							if (!StringUtils.isNullOrEmpty(vinMap.get("effective_date"))) {
								ownerVehicleVo.setExtendWarrantyStartDate(sdf.format(vinMap.get("effective_date")));
							}
							if (!StringUtils.isNullOrEmpty(vinMap.get("expire_date"))) {
								ownerVehicleVo.setExtendWarrantyEndDate(sdf.format(vinMap.get("expire_date")));
							}

						} else {
							ownerVehicleVo.setIsExtendWarranty(CommonConstants.DICT_IS_NO);
						}

						// 根据VIN查询最近送修人电话
//						Map delivererMobile = goodwillApplyInfoMapper.queryMobile(ownerVehicleVo.getVin());
//						if (!StringUtils.isNullOrEmpty(delivererMobile)) {
//							ownerVehicleVo.setMobile(delivererMobile.get("mobile") + "");
//						}

					}
				}

				page.setRecords(list);
				// return page;
			} else {
				// return null;
			}
		}
		return page;

		// 接口数据
		// List<Map> result = new ArrayList<>();
		// Map map = new HashMap(16);
		// map.put("vin", "LVYPD10A0KP084065");
		// map.put("license", "贵B-V8915");
		// map.put("customerName", "王二");
		// map.put("customerMobile", "13111111111");
		// map.put("mileage", "101111");
		// map.put("isExtendWarranty", "10041001");
		// map.put("extendWarrantyName", "保修2年");
		// map.put("extendWarrantyStartDate", "2020-01-01");
		// map.put("extendWarrantyEndDate", "2021-12-31");
		// map.put("model", "2020款");
		// map.put("buyCarDate", "2014-01-01");
		// map.put("warrantyStartDate", "2014-01-01");
		// result.add(map);
		// page.setRecords(result);
		// page.setTotal(result.size());
		// return page;
		// }
	}

	/**
	 * 查询亲善金额及分类配件信息——分页查询零件信息
	 *
	 * @param page
	 *            分页对象
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<List> * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@SuppressWarnings("rawtypes")
	@Override
	public IPage<List> queryPartDetailInfo(Page page, Map map) {
		// 税率
		String vat = Utility.getVat();
		map.put("rate", vat);
		//设置当前系统时间
		map.put("nowDate", DateUtils.formateDateToString(new Date(),DateUtils.PATTERN_YYYY_MM_DD));
		// 接口数据
		List<Map> result = goodwillApplyInfoMapper.queryPartDetailInfo(page, map);
		Integer count = goodwillApplyInfoMapper.queryPartDetailCountInfo(map);
		page.setRecords(result);
		page.setTotal(count);
		return page;
	}

	/**
	 * 查询零配件信息——分页查询配件信息
	 *
	 * @param page
	 *            分页对象
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<List> * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<List> queryPartInfo(Page page, Map map1) {
		// 接口数据
		List<Map> result = new ArrayList<>();
		Map map = new HashMap(16);
		map.put("repairNo", "da2131231");
		map.put("orderDate", "2020-05-11");
		map.put("repairType", 80681001);
		map.put("customerName", "王二");
		map.put("partNo", "L1101");
		map.put("partName", "轮胎");
		map.put("quantity", "12");
		map.put("orderDate", "2020-05-11");
		map.put("arrivalDate", "2020-05-21");
		result.add(map);
		page.setRecords(result);
		page.setTotal(result.size());
		return page;
	}

	/**
	 * 分页查询维修记录——分页查询工单信息
	 *
	 * @param page
	 *            分页对象
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<List> * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<List> queryRepairOrderInfo(Page page, Map map1) {
		// 接口数据
		List<Map> result = new ArrayList<>();
		Map map = new HashMap(16);
		map.put("roNo", "da2131231");
		map.put("customerName", "王二");
		map.put("repairTypeCode", 80681001);
		map.put("inMileage", "101111");
		map.put("roTroubleDesc", "左前轮破裂");
		map.put("roCreateDate", "2020-01-01");
		map.put("inDealerDate", "2021-12-31");
		map.put("deliveryDate", "2021-12-31");
		map.put("roStatus", 80491003);
		result.add(map);
		page.setRecords(result);
		page.setTotal(result.size());
		return page;
	}

	/**
	 * 提交预申请单
	 *
	 * @param goodwillApplyInfoDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@SuppressWarnings("rawtypes")
	@Override
	@Transactional
	public int commitSupportApplyInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		logger.info("开始保存——————————————————————————————————————————————————————————————");
		// 获取登录用户信息
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		List<Integer> list = goodwillApplyInfoDTO.getCustomerPainVue();
		// 客户痛点数据处理
		if (!CommonUtils.isNullOrEmpty(list)) {
			StringBuilder result = new StringBuilder();
			boolean flag = false;
			for (Integer integer : list) {
				if (flag) {
					result.append(",");
				} else {
					flag = true;
				}
				result.append(integer);
			}
			goodwillApplyInfoDTO.setCustomerPain(result.toString());
		}

		Integer mailType = 82461001;// 确认亲善邮件类型(商务亲善预申请等待审批)
		int sendToFlag = 2; // 1,经销商 2，角色 （默认：角色）

		if (StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getId())) {
			// 对对象进行赋值操作
			GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoDTO.transDtoToPo(GoodwillApplyInfoPO.class);
			goodwillApplyInfoPo.setGoodwillStatus(82551002);
			goodwillApplyInfoPo.setApplyPerson(loginInfoDto.getUserName());
			goodwillApplyInfoPo.setUpdatedPerson(loginInfoDto.getUserName());
			goodwillApplyInfoPo.setCommitTime(new Date());
			// 执行插入
			int row = goodwillApplyInfoMapper.insert(goodwillApplyInfoPo);
			// 维修记录插入
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getGoodwillApplyRepairInfoDTO())) {
				for (GoodwillApplyRepairInfoDTO repairInfoDto : goodwillApplyInfoDTO.getGoodwillApplyRepairInfoDTO()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					GoodwillApplyRepairInfoPO goodwillApplyRepairInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyRepairInfoPO.class);
					goodwillApplyRepairInfoMapper.insert(goodwillApplyRepairInfoPo);

				}
			}
			// 维修零配件插入
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getGoodwillApplyRepairPartInfoDTO())) {
				for (GoodwillApplyRepairPartInfoDTO repairInfoDto : goodwillApplyInfoDTO
						.getGoodwillApplyRepairPartInfoDTO()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					GoodwillApplyRepairPartInfoPO goodwillApplyRepairPartInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyRepairPartInfoPO.class);
					goodwillApplyRepairPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);

				}
			}
			// 亲善类型及金额-保养成本
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getMaintainCostTable())) {
				for (GoodwillApplyFinalPartInfoDTO repairInfoDto : goodwillApplyInfoDTO.getMaintainCostTable()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					repairInfoDto.setGoodwillType(0);
					GoodwillApplyFinalPartInfoPO goodwillApplyRepairPartInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyFinalPartInfoPO.class);
					goodwillApplyRepairPartInfoPo.setQuantity(new BigDecimal(repairInfoDto.getQuantity()));
					goodwillApplyRepairPartInfoPo.setUnitPriceTax(new BigDecimal(repairInfoDto.getUnitPriceTax()));
					goodwillApplyRepairPartInfoPo
							.setSupportProportion(new BigDecimal(repairInfoDto.getSupportProportion()));
					goodwillApplyFinalPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);

				}
			}
			// 亲善类型及金额-延保成本
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getExtendWarrantyCostTable())) {
				for (GoodwillApplyFinalPartInfoDTO repairInfoDto : goodwillApplyInfoDTO.getExtendWarrantyCostTable()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					repairInfoDto.setGoodwillType(1);
					GoodwillApplyFinalPartInfoPO goodwillApplyRepairPartInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyFinalPartInfoPO.class);
					goodwillApplyRepairPartInfoPo.setQuantity(new BigDecimal(repairInfoDto.getQuantity()));
					goodwillApplyRepairPartInfoPo.setUnitPriceTax(new BigDecimal(repairInfoDto.getUnitPriceTax()));
					goodwillApplyRepairPartInfoPo
							.setSupportProportion(new BigDecimal(repairInfoDto.getSupportProportion()));
					goodwillApplyFinalPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);

				}
			}
			// 亲善类型及金额-附件精品成本
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getAccessoryCostTable())) {
				for (GoodwillApplyFinalPartInfoDTO repairInfoDto : goodwillApplyInfoDTO.getAccessoryCostTable()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					repairInfoDto.setGoodwillType(2);
					GoodwillApplyFinalPartInfoPO goodwillApplyRepairPartInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyFinalPartInfoPO.class);
					goodwillApplyRepairPartInfoPo.setQuantity(new BigDecimal(repairInfoDto.getQuantity()));
					goodwillApplyRepairPartInfoPo.setUnitPriceTax(new BigDecimal(repairInfoDto.getUnitPriceTax()));
					goodwillApplyRepairPartInfoPo
							.setSupportProportion(new BigDecimal(repairInfoDto.getSupportProportion()));
					goodwillApplyFinalPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);

				}
			}
			// 亲善信息插入
			GoodwillMaterialAuditDTO goodwillMaterialAuditDTO = new GoodwillMaterialAuditDTO();
			goodwillMaterialAuditDTO.setGoodwillApplyId(goodwillApplyInfoPo.getId());
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getCustomerBackground())) {
				goodwillMaterialAuditDTO.setCustomerBackground(goodwillApplyInfoDTO.getCustomerBackground());
			}
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getReasonAndDispose())) {
				goodwillMaterialAuditDTO.setReasonAndDispose(goodwillApplyInfoDTO.getReasonAndDispose());
			}
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getRepairSolution())) {
				goodwillMaterialAuditDTO.setRepairSolution(goodwillApplyInfoDTO.getRepairSolution());
			}
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getCustomerRequire())) {
				goodwillMaterialAuditDTO.setCustomerRequire(goodwillApplyInfoDTO.getCustomerRequire());
			}
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getPotentialRisk())) {
				goodwillMaterialAuditDTO.setPotentialRisk(goodwillApplyInfoDTO.getPotentialRisk());
			}
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getVrOrTjNo())) {
				goodwillMaterialAuditDTO.setVrOrTjNo(goodwillApplyInfoDTO.getVrOrTjNo());
			}
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getBusinessGoodwillApplyDetail())) {
				goodwillMaterialAuditDTO
						.setBusinessGoodwillApplyDetail(goodwillApplyInfoDTO.getBusinessGoodwillApplyDetail());
			}
			GoodwillMaterialAuditPO goodwillMaterialAuditPo = goodwillMaterialAuditDTO
					.transDtoToPo(GoodwillMaterialAuditPO.class);
			goodwillMaterialAuditMapper.insert(goodwillMaterialAuditPo);
			//TODO:获取经销商角色如果是浙江区，金额大于10000.00转大区总监
			CompanyDetailDTO  getRole   = getRole(goodwillApplyInfoDTO.getDealerCode());
			logger.info("getRole：{}",JSONObject.toJSON(getRole));
			Long    afterBigAreaId = getRole.getAfterBigAreaId();
			Long    afterSmallAreaId = getRole.getAfterSmallAreaId();
			//TODO: 跟进角色查询经销商判断售后大小区是否有售后经理
			Map<String,Long>mapvalus =  this.check(afterBigAreaId,afterSmallAreaId);
			// 获取该申请单申请流程
			boolean num = false;
			List<GoodwillAuditProcessDTO> auditList = goodwillAuditProcessService.selectList(0,
					goodwillApplyInfoDTO.getAuditType(), goodwillApplyInfoDTO.getApplyAmount(),mapvalus.get("afterSmallAreaId"),mapvalus.get("afterBigAreaId"));
			logger.info("亲善查询审核流程表结果processList：{}",JSONObject.toJSON(auditList));
			if (!CommonUtils.isNullOrEmpty(auditList)) {
				for (GoodwillAuditProcessDTO goodwillAuditProcessDTO : auditList) {
					// 获取角色CODE
					String roles = this.queryRole(goodwillAuditProcessDTO.getAuditObject(),
							goodwillAuditProcessDTO.getAuditType(),
							Integer.valueOf(goodwillAuditProcessDTO.getAuditPosition()));
//					if(afterSmallAreaId ==null  &&  "SHQYJL".equals(roles)){
//						continue;
//					}
//					if(afterBigAreaId ==null  &&  "SHDQJL".equals(roles)){
//						continue;
//					}
					GoodwilApplyAuditProcessDTO dto = new GoodwilApplyAuditProcessDTO();
					dto.setGoodwillApplyId(goodwillApplyInfoPo.getId());
					dto.setAuditObject(goodwillAuditProcessDTO.getAuditObject());
					dto.setAuditType(goodwillApplyInfoPo.getAuditType());
					dto.setAuditRole(roles);
					dto.setAuditPosition(goodwillAuditProcessDTO.getAuditPosition());
					dto.setAuditSort(Integer.valueOf(goodwillAuditProcessDTO.getAuditPosition()));
					GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo = dto
							.transDtoToPo(GoodwilApplyAuditProcessPO.class);
					goodwilApplyAuditProcessMapper.insert(goodwilApplyAuditProcessPo);

					if (roles.equals("CFO")) {
						num = true;
					}

				}
			}

			if (num) {
				GoodwillApplyInfoPO goodwillApplyPo = goodwillApplyInfoMapper.selectById(goodwillApplyInfoPo.getId());
				// 对对象进行赋值操作
				goodwillApplyPo.setIsNeedTranslate(CommonConstants.DICT_IS_NO);
				;
				goodwillApplyPo.setUpdatedPerson(loginInfoDto.getUserName());
				// 执行更新
				int rows = goodwillApplyInfoMapper.updateById(goodwillApplyPo);
			}

			// 获取当前审核信息
			List<GoodwilApplyAuditProcessPO> po = goodwilApplyAuditProcessMapper
					.selectByGoodwillApplyId(goodwillApplyInfoPo.getId());
			if (!CommonUtils.isNullOrEmpty(po)) {
				for (GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo : po) {
					// 1.实时提醒-商务亲善预申请等待审批
					this.sendEmail(goodwillApplyInfoPo, sendToFlag, goodwilApplyAuditProcessPo.getAuditRole(),
							mailType);
				}
			}

		} else {
			GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoMapper.selectById(goodwillApplyInfoDTO.getId());
			// 对对象进行赋值操作
			goodwillApplyInfoDTO.transDtoToPo(goodwillApplyInfoPo);
			goodwillApplyInfoPo.setGoodwillStatus(82551002);
			if (StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getVolvoIntegral())) {
				goodwillApplyInfoPo.setVolvoIntegral(new BigDecimal(0));
			}
			if (StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getVoucherCost())) {
				goodwillApplyInfoPo.setVoucherCost(new BigDecimal(0));
			}
			if (StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getWalkingCarPrice())) {
				goodwillApplyInfoPo.setWalkingCarPrice(new BigDecimal(0));
			}
			if (StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getReturnChangeCarPrice())) {
				goodwillApplyInfoPo.setReturnChangeCarPrice(new BigDecimal(0));
			}
			if (StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getOtherPrice())) {
				goodwillApplyInfoPo.setOtherPrice(new BigDecimal(0));
			}

			// 维修记录更新
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getGoodwillApplyRepairInfoDTO())) {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoDTO.getId());
				goodwillApplyRepairInfoMapper.deleteByMap(columnMap);
				for (GoodwillApplyRepairInfoDTO repairInfoDto : goodwillApplyInfoDTO.getGoodwillApplyRepairInfoDTO()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoDTO.getId());
					GoodwillApplyRepairInfoPO goodwillApplyRepairInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyRepairInfoPO.class);
					goodwillApplyRepairInfoMapper.insert(goodwillApplyRepairInfoPo);

				}
			} else {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoDTO.getId());
				goodwillApplyRepairInfoMapper.deleteByMap(columnMap);
			}
			// 维修零配件插入
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getGoodwillApplyRepairPartInfoDTO())) {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoDTO.getId());
				goodwillApplyRepairPartInfoMapper.deleteByMap(columnMap);
				for (GoodwillApplyRepairPartInfoDTO repairInfoDto : goodwillApplyInfoDTO
						.getGoodwillApplyRepairPartInfoDTO()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoDTO.getId());
					GoodwillApplyRepairPartInfoPO goodwillApplyRepairPartInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyRepairPartInfoPO.class);
					goodwillApplyRepairPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);

				}
			} else {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoDTO.getId());
				goodwillApplyRepairPartInfoMapper.deleteByMap(columnMap);

			}

			// 亲善类型及金额-保养成本
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getMaintainCostTable())) {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoDTO.getId());
				columnMap.put("goodwill_type", 0);
				goodwillApplyFinalPartInfoMapper.deleteByMap(columnMap);
				for (GoodwillApplyFinalPartInfoDTO repairInfoDto : goodwillApplyInfoDTO.getMaintainCostTable()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoDTO.getId());
					repairInfoDto.setGoodwillType(0);
					GoodwillApplyFinalPartInfoPO goodwillApplyRepairPartInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyFinalPartInfoPO.class);
					goodwillApplyRepairPartInfoPo.setQuantity(new BigDecimal(repairInfoDto.getQuantity()));
					goodwillApplyRepairPartInfoPo.setUnitPriceTax(new BigDecimal(repairInfoDto.getUnitPriceTax()));
					goodwillApplyRepairPartInfoPo
							.setSupportProportion(new BigDecimal(repairInfoDto.getSupportProportion()));
					goodwillApplyFinalPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);

				}
			} else if (CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getMaintainCostTable())
					&& goodwillApplyInfoDTO.getIsMaintainChange() == 1) {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoDTO.getId());
				columnMap.put("goodwill_type", 0);
				goodwillApplyFinalPartInfoMapper.deleteByMap(columnMap);

			}

			// 亲善类型及金额-延保成本
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getExtendWarrantyCostTable())) {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoDTO.getId());
				columnMap.put("goodwill_type", 1);
				goodwillApplyFinalPartInfoMapper.deleteByMap(columnMap);
				for (GoodwillApplyFinalPartInfoDTO repairInfoDto : goodwillApplyInfoDTO.getExtendWarrantyCostTable()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoDTO.getId());
					repairInfoDto.setGoodwillType(1);
					GoodwillApplyFinalPartInfoPO goodwillApplyRepairPartInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyFinalPartInfoPO.class);
					goodwillApplyRepairPartInfoPo.setQuantity(new BigDecimal(repairInfoDto.getQuantity()));
					goodwillApplyRepairPartInfoPo.setUnitPriceTax(new BigDecimal(repairInfoDto.getUnitPriceTax()));
					goodwillApplyRepairPartInfoPo
							.setSupportProportion(new BigDecimal(repairInfoDto.getSupportProportion()));
					goodwillApplyFinalPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);

				}
			} else if (CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getExtendWarrantyCostTable())
					&& goodwillApplyInfoDTO.getIsExtendWarrantyChange() == 1) {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoDTO.getId());
				columnMap.put("goodwill_type", 1);
				goodwillApplyFinalPartInfoMapper.deleteByMap(columnMap);

			}
			// 亲善类型及金额-附件精品成本
			if (!CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getAccessoryCostTable())) {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoDTO.getId());
				columnMap.put("goodwill_type", 2);
				goodwillApplyFinalPartInfoMapper.deleteByMap(columnMap);
				for (GoodwillApplyFinalPartInfoDTO repairInfoDto : goodwillApplyInfoDTO.getAccessoryCostTable()) {
					repairInfoDto.setGoodwillApplyId(goodwillApplyInfoDTO.getId());
					repairInfoDto.setGoodwillType(2);
					GoodwillApplyFinalPartInfoPO goodwillApplyRepairPartInfoPo = repairInfoDto
							.transDtoToPo(GoodwillApplyFinalPartInfoPO.class);
					goodwillApplyRepairPartInfoPo.setQuantity(new BigDecimal(repairInfoDto.getQuantity()));
					goodwillApplyRepairPartInfoPo.setUnitPriceTax(new BigDecimal(repairInfoDto.getUnitPriceTax()));
					goodwillApplyRepairPartInfoPo
							.setSupportProportion(new BigDecimal(repairInfoDto.getSupportProportion()));
					goodwillApplyFinalPartInfoMapper.insert(goodwillApplyRepairPartInfoPo);

				}
			} else if (CommonUtils.isNullOrEmpty(goodwillApplyInfoDTO.getAccessoryCostTable())
					&& goodwillApplyInfoDTO.getIsAccessoryChange() == 1) {
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoDTO.getId());
				columnMap.put("goodwill_type", 2);
				goodwillApplyFinalPartInfoMapper.deleteByMap(columnMap);

			}

			// 亲善信息插入
			GoodwillMaterialAuditPO goodwillMaterialAuditPo = goodwillMaterialAuditMapper
					.selectById(goodwillApplyInfoDTO.getMaterialId());
			GoodwillMaterialAuditDTO goodwillMaterialAuditDTO = new GoodwillMaterialAuditDTO();
			goodwillMaterialAuditDTO.setGoodwillApplyId(goodwillApplyInfoDTO.getId());
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getCustomerBackground())) {
				goodwillMaterialAuditDTO.setCustomerBackground(goodwillApplyInfoDTO.getCustomerBackground());
			}
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getReasonAndDispose())) {
				goodwillMaterialAuditDTO.setReasonAndDispose(goodwillApplyInfoDTO.getReasonAndDispose());
			}
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getRepairSolution())) {
				goodwillMaterialAuditDTO.setRepairSolution(goodwillApplyInfoDTO.getRepairSolution());
			}
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getCustomerRequire())) {
				goodwillMaterialAuditDTO.setCustomerRequire(goodwillApplyInfoDTO.getCustomerRequire());
			}
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getPotentialRisk())) {
				goodwillMaterialAuditDTO.setPotentialRisk(goodwillApplyInfoDTO.getPotentialRisk());
			}
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getVrOrTjNo())) {
				goodwillMaterialAuditDTO.setVrOrTjNo(goodwillApplyInfoDTO.getVrOrTjNo());
			}
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getBusinessGoodwillApplyDetail())) {
				goodwillMaterialAuditDTO
						.setBusinessGoodwillApplyDetail(goodwillApplyInfoDTO.getBusinessGoodwillApplyDetail());
			}

			goodwillMaterialAuditDTO.transDtoToPo(goodwillMaterialAuditPo);
			goodwillMaterialAuditMapper.updateById(goodwillMaterialAuditPo);

			// 单据状态不为材料审核驳回，才会重置流程
			if (goodwillApplyInfoDTO.getGoodwillStatus() != CommonConstants.GOODWILL_STATUS_MATERIAL_RETURN) {
				// 获取该申请单申请流程
				//TODO:获取经销商角色如果是浙江区，金额大于10000.00转大区总监
				CompanyDetailDTO getRole   = getRole(goodwillApplyInfoDTO.getDealerCode());
				logger.info("getRole：{}",JSONObject.toJSON(getRole));
				logger.info("getRole：{}",JSONObject.toJSON(getRole));
				Long    afterBigAreaId = getRole.getAfterBigAreaId();
				Long    afterSmallAreaId = getRole.getAfterSmallAreaId();
				//TODO: 跟进角色查询经销商判断售后大小区是否有售后经理
				Map<String , Long>	mapvalus = this.check(afterBigAreaId,afterSmallAreaId);
				List<GoodwillAuditProcessDTO> auditList = goodwillAuditProcessService.selectList(0,
						goodwillApplyInfoDTO.getAuditType(), goodwillApplyInfoDTO.getApplyAmount(),mapvalus.get("afterSmallAreaId"),mapvalus.get("afterBigAreaId"));
				Map<String, Object> columnMap = new HashMap(16);
				columnMap.put("goodwill_apply_id", goodwillApplyInfoDTO.getId());
				// columnMap.put("audit_object", 0);
				// columnMap.put("audit_type", goodwillApplyInfoDTO.getAuditType());
				goodwilApplyAuditProcessMapper.deleteByMap(columnMap);
				if (!CommonUtils.isNullOrEmpty(auditList)) {
					for (GoodwillAuditProcessDTO goodwillAuditProcessDTO : auditList) {

						// 获取角色CODE
						String roles = this.queryRole(goodwillAuditProcessDTO.getAuditObject(),
								goodwillAuditProcessDTO.getAuditType(),
								Integer.valueOf(goodwillAuditProcessDTO.getAuditPosition()));
//						if(afterSmallAreaId ==null  &&  "SHQYJL".equals(roles)){
//							continue;
//						}
//						if(afterBigAreaId ==null  &&  "SHDQJL".equals(roles)){
//							continue;
//						}
						GoodwilApplyAuditProcessDTO dto = new GoodwilApplyAuditProcessDTO();
						dto.setGoodwillApplyId(goodwillApplyInfoDTO.getId());
						dto.setAuditObject(goodwillAuditProcessDTO.getAuditObject());
						dto.setAuditType(goodwillApplyInfoPo.getAuditType());
						dto.setAuditPosition(goodwillAuditProcessDTO.getAuditPosition());
						dto.setAuditRole(roles);
						dto.setAuditSort(Integer.valueOf(goodwillAuditProcessDTO.getAuditPosition()));
						GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo = dto
								.transDtoToPo(GoodwilApplyAuditProcessPO.class);
						goodwilApplyAuditProcessMapper.insert(goodwilApplyAuditProcessPo);

						if (roles.equals("CFO")) {
							goodwillApplyInfoPo.setIsNeedTranslate(CommonConstants.DICT_IS_NO);
						}
					}
				}
			}
			goodwillApplyInfoPo.setUpdatedPerson(loginInfoDto.getUserName());
			goodwillApplyInfoPo.setCommitTime(new Date());
			// 执行更新
			int row = goodwillApplyInfoMapper.updateById(goodwillApplyInfoPo);

			// 获取当前审核信息
			List<GoodwilApplyAuditProcessPO> po = goodwilApplyAuditProcessMapper
					.selectByGoodwillApplyId(goodwillApplyInfoPo.getId());
			if (!CommonUtils.isNullOrEmpty(po)) {
				for (GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo : po) {
					// 1.实时提醒-商务亲善预申请等待审批
					this.sendEmail(goodwillApplyInfoPo, sendToFlag, goodwilApplyAuditProcessPo.getAuditRole(),
							mailType);
				}
			}
		}

		// 返回插入的值
		return 1;
	}

	private Map<String,Long> check(Long afterBigAreaId, Long afterSmallAreaId) {
		Map<String,Long> mapvalues = new HashMap<>();
		mapvalues.put("afterBigAreaId",afterBigAreaId);
		mapvalues.put("afterSmallAreaId",afterSmallAreaId);
		//大区
		List<Map> listSHDQJL =  getSHDQJL();
		//小区
		List<Map> listSHQYJL =	getSHQYJL();
		boolean falg1 = false;
		if(!CommonUtils.isNullOrEmpty(listSHQYJL) && afterSmallAreaId !=null ){
			for (Map map:listSHQYJL) {
				if(map.get("orgid")!=null &&  map.get("orgid").toString().equals(afterSmallAreaId.toString())){
					falg1 = true;
					break;
				}

			}
		}
		boolean falg2 = false;
		if(!CommonUtils.isNullOrEmpty(listSHDQJL) && afterBigAreaId !=null ){
			for (Map map:listSHDQJL) {
				if(map.get("orgid")!=null &&  map.get("orgid").toString().equals(afterBigAreaId.toString())){
					falg2= true ;
					break;
				}
			}
		}
		logger.info("亲善获取大小区账号：{}",JSONObject.toJSON(mapvalues));
		if(!falg1){
			mapvalues.put("afterSmallAreaId",null);
		}
		if(!falg2){
			mapvalues.put("afterBigAreaId",null);
		}
		logger.info("end 亲善获取大小区账号：{}",JSONObject.toJSON(mapvalues));
		return mapvalues;
	}

	private List<Map> getSHQYJL() {
		String successCode = "0";
		String requestUrl = midUrlProperties.getMidEndAuthCenter() + midUrlProperties.getOrgRole();
		queryUserByOrgTypeDTO dto = new queryUserByOrgTypeDTO();
		Map map = new HashMap(16);
		map.put("roleCode", "SHQYJL");
		dto.setData(map);
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<queryUserByOrgTypeDTO> httpEntity = new HttpEntity<queryUserByOrgTypeDTO>(dto, httpHeaders);
		ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrl, HttpMethod.POST, httpEntity,
				ResponseDTO.class);
		// 接口数据
		if (responseEntity.getBody() != null) {
			if (successCode.equals(responseEntity.getBody().getReturnCode())) {
				ObjectMapper objectMapper = new ObjectMapper();
				logger.info("亲善调用中台获取小区经销商：{}",JSONObject.toJSON(responseEntity.getBody().getData()));
				return  objectMapper.convertValue(responseEntity.getBody().getData(), List.class);
			} else {
				throw new DALException("组织角色查询员工接口异常，请稍后再试");
			}
		}
		return null;
	}

	private List<Map> getSHDQJL() {
		String successCode = "0";
		String requestUrl = midUrlProperties.getMidEndAuthCenter() + midUrlProperties.getOrgRole();
		queryUserByOrgTypeDTO dto = new queryUserByOrgTypeDTO();
		Map map = new HashMap(16);
		map.put("roleCode", "SHDQJL");
		dto.setData(map);
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<queryUserByOrgTypeDTO> httpEntity = new HttpEntity<queryUserByOrgTypeDTO>(dto, httpHeaders);
		ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrl, HttpMethod.POST, httpEntity,
				ResponseDTO.class);
		// 接口数据
		if (responseEntity.getBody() != null) {
			if (successCode.equals(responseEntity.getBody().getReturnCode())) {
				ObjectMapper objectMapper = new ObjectMapper();
				logger.info("亲善调用中台获取大区经销商：{}",JSONObject.toJSON(responseEntity.getBody().getData()));
				return  objectMapper.convertValue(responseEntity.getBody().getData(), List.class);
			} else {
				throw new DALException("组织角色查询员工接口异常，请稍后再试");
			}
		}
		return null;
	}

	/**
	 * 获取亲善单号
	 *
	 * @param dearCold
	 */
	@Transactional
	@Override
	public String getBillNo(String gw, String dearCold) {
		return getBillNo1(gw, dearCold);
	}

	/**
	 * 调用获取单号存储过程
	 *
	 * @param type
	 * @param dealerCode
	 * @return
	 * @throws ServiceBizException
	 */
	public String getBillNo1(String type, String dealerCode) throws ServiceBizException {
		String billNo = "0";

		try {
			List<String> ins = new ArrayList();
			ins.add(dealerCode);
			ins.add(type);
			StringBuilder sb = new StringBuilder("select @e as errorCode,@r as billNo;");
			List<Object> params = new ArrayList<>();
			params.add(type);
			params.add(dealerCode);

			DAOUtil.execBatchPreparement("call P_GW_GETBILLNO(?,?,@e,@r);", params);
			List<Map> listout = DAOUtil.findAll(sb.toString(), false, (List) null, new boolean[0]);
			String errcode = ((Map) listout.get(0)).get("errorCode").toString();
			billNo = ((Map) listout.get(0)).get("billNo").toString();
			if ("ERROR-6666".equalsIgnoreCase(errcode)) {
				throw new ServiceBizException("已超出本日单号的上限!");
			} else if ("ERROR-9999".equalsIgnoreCase(errcode)) {
				throw new ServiceBizException("没有对应的单据类型!");
			} else {
				return billNo;
			}
		} catch (Exception var8) {
			logger.error("生成单号异常: {}", var8.getMessage());
			throw new ServiceBizException("生成单号异常", var8.getMessage());
		}
	}

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillApplyInfoDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillApplyInfoDTO> selectOemTodoPageBysql(Page page, GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		if (goodwillApplyInfoDTO == null) {
			goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		}
		GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoDTO.transDtoToPo(GoodwillApplyInfoPO.class);

		List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.selectPageBySql(page, goodwillApplyInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillApplyInfoDTO> result = list.stream().map(m -> m.transPoToDto(GoodwillApplyInfoDTO.class))
					.collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	@Override
	public IPage<GoodwillApplyInfoDTO> selectCheckedGoodWill(Page page, GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		if (goodwillApplyInfoDTO == null) {
			goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		}
		// 获取登录用户信息
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		goodwillApplyInfoDTO.setUserId(loginInfoDto.getUserId());
		GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoDTO.transDtoToPo(GoodwillApplyInfoPO.class);

		List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.selectCheckedGoodWill(page, goodwillApplyInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillApplyInfoDTO> result = list.stream().map(m -> m.transPoToDto(GoodwillApplyInfoDTO.class))
					.collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	@Override
	public List<GoodwillApplyInfoDTO> exportCheckedApplyInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		if (goodwillApplyInfoDTO == null) {
			goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		}
		// 获取登录用户信息
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		goodwillApplyInfoDTO.setUserId(loginInfoDto.getUserId());

		List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.exportCheckedApplyInfo(goodwillApplyInfoDTO);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillApplyInfoDTO.class)).collect(Collectors.toList());
		}
	}

	/**
	 * 审核通过
	 *
	 * @param id
	 *            主键ID
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@SuppressWarnings("unused")
	@Override
	public int passApplyGoodwillInfo(Long id, GoodwillAuditInfoDTO goodwillAuditInfoDTO) {
		logger.info("goodwillAuditInfoDTO,{}",JSONObject.toJSON(goodwillAuditInfoDTO));
		// 获取登录用户信息
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		// 更新主单的审核金额
		GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoMapper.selectById(id);
		Integer number = 0;

		// 对对象进行赋值操作
		if (goodwillApplyInfoPo.getGoodwillStatus() == CommonConstants.GOODWILL_STATUS_APPLY_RETURN) {
			goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_APPLY_AUDIT);
		}
		if (goodwillApplyInfoPo.getGoodwillStatus() == CommonConstants.GOODWILL_STATUS_MATERIAL_RETURN) {
			goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_MATERIAL_AUDIT);
		}
		goodwillApplyInfoPo.setAuditAmount(goodwillAuditInfoDTO.getAuditPrice());
		logger.info("settlementAmount:{}",goodwillAuditInfoDTO.getSettlementAmount());
		goodwillApplyInfoPo.setSettlementAmount(goodwillAuditInfoDTO.getSettlementAmount());
		//int mx=0;
		int mc = 1;
		// 按顺序获取审批流程表的该申请单还未审批的最新一条流程数据，并更新数据审核结果为审核通过
		List<GoodwilApplyAuditProcessPO> po = goodwilApplyAuditProcessMapper.selectByGoodwillApplyId(id);
		if (!CommonUtils.isNullOrEmpty(po)) {
			for (GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo : po) {
				//增加判断角色逻辑
				if(goodwilApplyAuditProcessPo.getAuditObject()!=null &&  goodwilApplyAuditProcessPo.getAuditObject().intValue()==0 ){
					mc =0;
					logger.info("xxxxxxxxxxxxxxxxxxxxxxxxx:{}",mc);
				}
//				if(!goodwilApplyAuditProcessPo.getAuditRole().equals(goodwillAuditInfoDTO.getRoleList())){
//					logger.info("xxxxxxxxxxxxxxxxxxxxxxxxx:{}","角色不正确");
//					if(goodwilApplyAuditProcessPo.getAuditObject()!=null &&  goodwilApplyAuditProcessPo.getAuditObject().intValue()==1){
//						mx=1;
//						logger.info("角色不正确：{}，{}",goodwilApplyAuditProcessPo.getAuditRole(),goodwillAuditInfoDTO.getRoleList());
//						break;
//					}
//				}
			//判断账号是否有修改的权限
                if(goodwilApplyAuditProcessPo.getAuditObject()!=null &&  goodwilApplyAuditProcessPo.getAuditObject().intValue()==1){
					logger.info("xxxxxxxxxxxxxxxxxxxxxxxxx:{}","角色不正确11");
                   mc= getRoles(goodwilApplyAuditProcessPo.getAuditRole());
                    if(mc== 1 ){
						logger.info("角色不正确：{}，{}",mc);
                        break;
                    }
                }
				if (goodwillApplyInfoPo.getGoodwillStatus() == CommonConstants.GOODWILL_STATUS_APPLY_AUDIT
						|| goodwillApplyInfoPo.getGoodwillStatus() == CommonConstants.GOODWILL_STATUS_APPLY_RETURN) {
					if (goodwillAuditInfoDTO.getRoleList() != null
							&& (goodwillAuditInfoDTO.getRoleList().equals("VP")
							|| goodwillAuditInfoDTO.getRoleList().equals("CCMQ"))
							&& goodwilApplyAuditProcessPo.getAuditRole().equals("VP")) {
						// 查询是否上传VP附件，没有上传提醒；
						Integer num = goodwillApplyFileInfoMapper.selectVpFile(goodwillApplyInfoPo.getApplyNo());
						if (num == 0) {
							throw new DALException("请上传VP附件");
						}
						// 查询是否上传VP和CEO附件，上传则审核直接通过
						Integer nums = goodwillApplyFileInfoMapper.selectCeoFile(goodwillApplyInfoPo.getApplyNo());
						if (num == 0 && nums != 0) {
							throw new DALException("请上传VP附件");
						} else if (num != 0 && nums != 0) {
							number = 1;
						}
					}
					if (goodwillAuditInfoDTO.getRoleList() != null
							&& (goodwillAuditInfoDTO.getRoleList().equals("CEO")
							|| goodwillAuditInfoDTO.getRoleList().equals("CCMQ"))
							&& goodwilApplyAuditProcessPo.getAuditRole().equals("CEO")) {
						// 查询是否上传CEO附件，没有上传则提醒
						Integer num = goodwillApplyFileInfoMapper.selectCeoFile(goodwillApplyInfoPo.getApplyNo());
						if (num == 0) {
							throw new DALException("请上传CEO附件");
						}

					}

				}
				boolean isMail=goodwillAuditInfoDTO.getRoleList().contains(CommonConstants.SHQYJL_ROLE_CODE) && goodwillAuditInfoDTO.getRoleList().contains(CommonConstants.SHDQJL_ROLE_CODE);
				logger.info("auditProcess role is {},and isMail is {}",goodwillAuditInfoDTO.getRoleList(),isMail);
				auditProcess(goodwilApplyAuditProcessPo, id, goodwillAuditInfoDTO, loginInfoDto, number, goodwillApplyInfoPo,!isMail);
				//当前角色同时拥有区域经理和高级区域经理,如果下个审批为高级区域经理则自动通过
				//  SHQYJL:售后区域经理 , SHDQJL :售后区域高级经理'
				if (isMail){
					logger.info("auditProcess同时为售后区域经理,售后区域高级经理");
					List<GoodwilApplyAuditProcessPO> goodwilApplyAuditProcess = goodwilApplyAuditProcessMapper.selectByGoodwillApplyId(id);
					logger.info("goodwilApplyAuditProcess审批:{}",goodwilApplyAuditProcess);
					if (CommonUtils.isNullOrEmpty(goodwilApplyAuditProcess)) {
						break;
					}
					if (CommonConstants.SHDQJL_ROLE_CODE.equals(goodwilApplyAuditProcess.get(0).getAuditRole())){
						//调用审批接口
						logger.info("auditProcess调用审批接口");
						auditProcess(goodwilApplyAuditProcess.get(0), id, goodwillAuditInfoDTO, loginInfoDto, number, goodwillApplyInfoPo,isMail);
					}
				}
			}
		}
		goodwillApplyInfoPo.setUpdatedPerson(loginInfoDto.getUserName());
		logger.info("更新xxxxxxxxxxxxxx：{},{}",mc);
		if( mc == 0){
			logger.info("goodwillApplyInfoPo,{}",JSONObject.toJSON(goodwillApplyInfoPo));
			int row = goodwillApplyInfoMapper.updateById(goodwillApplyInfoPo);
			return row;
		}else{
			return 1;
		}
	}
	private int getRoles( String roleList) {
		logger.info("xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx");
        UserInfoNewDto maps = 	businessPlatformService.getRoleList();
		httpLogService.saveHttpLog("亲善", "", roleList, "POST", "200", JSONObject.toJSONString(maps));
		logger.info("角色不正确：{}，{}",roleList,JSONObject.toJSONString(maps));
		if(maps!=null  ){
            List<RoleListDto> listDtos =  maps.getRoleList();
				if(listDtos !=null  &&  listDtos.size()>0){
                    logger.info("角色不正确：{}，{}",listDtos.toString());
                    for(int i=0;i<listDtos.size();i++) {
                        if(roleList.equals( listDtos.get(i).getRoleCode())){
							logger.info("roleList.equals( listDtos.get(i).getRoleCode()):{}",roleList.equals( listDtos.get(i).getRoleCode()));
                           return 0;
                        }

                    }
                }
		}
		return  1;
	}

	/**
	 * 审核驳回
	 *
	 * @param id
	 *            主键ID
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int passFailApplyGoodwillInfo(Long id, GoodwillAuditInfoDTO goodwillAuditInfoDTO) {
		// 审核驳回，主单数据不变；通过驳回角色信息和主单审核状态，确定驳回至审核流程中的哪一级
		// 然后将该等级后面的审核状态置为空。
		// 审核记录表插入一条驳回记录

		// 获取登录用户信息
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();

		Integer auditObject = null;
		Integer auditType = null;
		GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoMapper.selectById(id);

		Integer goodwillStatus = goodwillApplyInfoPo.getGoodwillStatus();
		Integer mailType = 0;// 确认亲善邮件类型
		int sendToFlag = 1; // 1,经销商 2，角色
		if (goodwillStatus == CommonConstants.GOODWILL_STATUS_APPLY_AUDIT
				|| goodwillStatus == CommonConstants.GOODWILL_STATUS_APPLY_RETURN) {
			auditObject = 0;
			goodwillApplyInfoPo.setGoodwillStatus(82551003);
			mailType = 82461002;// 商务亲善预审批被驳回
		}
		if (goodwillStatus == CommonConstants.GOODWILL_STATUS_MATERIAL_AUDIT
				|| goodwillStatus == CommonConstants.GOODWILL_STATUS_MATERIAL_RETURN) {
			if ("CEO".equals(goodwillAuditInfoDTO.getRoleList()) || "CFO".equals(goodwillAuditInfoDTO.getRoleList())) {
				if (!StringUtils.isNullOrEmpty(goodwillAuditInfoDTO.getReturnTo())
						&& !"CFO".equals(goodwillAuditInfoDTO.getReturnTo())) {
					goodwillApplyInfoPo.setIsNeedTranslate(CommonConstants.DICT_IS_NO);
				}
			}

			auditObject = 1;
			goodwillApplyInfoPo.setGoodwillStatus(82551006);
			mailType = 82461006;// 商务亲善材料审核被驳回
		}
		// 获取当前审核信息
		List<GoodwilApplyAuditProcessPO> po = goodwilApplyAuditProcessMapper.selectByGoodwillApplyId(id);
		if (!CommonUtils.isNullOrEmpty(po)) {
			for (GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo : po) {
				if ("0".equals(goodwillAuditInfoDTO.getReturnTo())) {
					// 如果驳回至经销商
					GoodwillAuditInfoPO goodwillAuditInfoPo = new GoodwillAuditInfoPO();
					goodwillAuditInfoPo.setGoodwillApplyId(id);
					goodwillAuditInfoPo.setAuditObject(goodwilApplyAuditProcessPo.getAuditObject());
					goodwillAuditInfoPo.setAuditType(goodwilApplyAuditProcessPo.getAuditType());
					goodwillAuditInfoPo.setAuditRole(goodwilApplyAuditProcessPo.getAuditRole());
					goodwillAuditInfoPo.setAuditOpinion(goodwillAuditInfoDTO.getAuditOpinion());
					goodwillAuditInfoPo.setAuditPrice(goodwillAuditInfoDTO.getAuditPrice());
					goodwillAuditInfoPo.setAuditResult(82801002);
					goodwillAuditInfoPo.setAuditTime(new Date());
					goodwillAuditInfoPo.setAuditName(loginInfoDto.getUserName());
					goodwillAuditInfoPo.setAuditor(loginInfoDto.getUserId());
					goodwillAuditInfoPo.setReturnTo(goodwillApplyInfoPo.getDealerCode());
					goodwillAuditInfoMapper.insert(goodwillAuditInfoPo);

					// 实时提醒- 商务亲善材料审核被驳回 （经销商）
					this.sendEmail(goodwillApplyInfoPo, sendToFlag, null, mailType);
				} else {
					// 根据驳回单位查询角色code
					GoodwilApplyAuditProcessPO wilApplyAuditProcessPo = goodwilApplyAuditProcessMapper
							.selectById(goodwillAuditInfoDTO.getReturnTo());
					// 根绝审批流程表数据往审批记录表插入数据
					GoodwillAuditInfoPO goodwillAuditInfoPo = new GoodwillAuditInfoPO();
					goodwillAuditInfoPo.setGoodwillApplyId(id);
					goodwillAuditInfoPo.setAuditObject(goodwilApplyAuditProcessPo.getAuditObject());
					goodwillAuditInfoPo.setAuditType(goodwilApplyAuditProcessPo.getAuditType());
					goodwillAuditInfoPo.setAuditRole(goodwilApplyAuditProcessPo.getAuditRole());
					goodwillAuditInfoPo.setAuditOpinion(goodwillAuditInfoDTO.getAuditOpinion());
					goodwillAuditInfoPo.setAuditPrice(goodwillAuditInfoDTO.getAuditPrice());
					goodwillAuditInfoPo.setAuditResult(82801002);
					goodwillAuditInfoPo.setAuditTime(new Date());
					goodwillAuditInfoPo.setAuditName(loginInfoDto.getUserName());
					goodwillAuditInfoPo.setAuditor(loginInfoDto.getUserId());
					goodwillAuditInfoPo.setReturnTo(wilApplyAuditProcessPo.getAuditRole());
					goodwillAuditInfoMapper.insert(goodwillAuditInfoPo);
					sendToFlag = 2;
					// 实时提醒- 商务亲善材料审核被驳回 （角色）
					this.sendEmail(goodwillApplyInfoPo, sendToFlag, wilApplyAuditProcessPo.getAuditRole(), mailType);
				}

			}
		}

		goodwilApplyAuditProcessMapper.updateProcess(id, Integer.valueOf(goodwillAuditInfoDTO.getReturnTo()),
				auditObject);
		goodwillApplyInfoPo.setUpdatedPerson(loginInfoDto.getUserName());
		// 执行更新
		int row = goodwillApplyInfoMapper.updateById(goodwillApplyInfoPo);

		// // 查询驳回单位的信息
		// if ("0".equals(goodwillAuditInfoDTO.getReturnTo())) {
		//
		// // 查询经销商邮箱信息
		// String dealerCode = goodwillApplyInfoPo.getDealerCode();
		// GoodwillDealerMailInfoPO goodwillDealerMailInfoPo =
		// goodwillDealerMailInfoMapper
		// .selectByDealerCode(dealerCode);
		// // 组装数据调用中台发送邮件
		// EmailInfoDto emailInfoDto = new EmailInfoDto();
		// String[] list;
		// if (goodwillDealerMailInfoPo != null) {
		// if (goodwillDealerMailInfoPo.geteMail1().indexOf(",") > 0) {
		// list = goodwillDealerMailInfoPo.geteMail1().split(",");
		// } else {
		// list = new String[1];
		// list[0] = goodwillDealerMailInfoPo.geteMail1();
		// }
		// emailInfoDto.setTo(list);
		// // EamilTemplate EamilTemplate = new EamilTemplate();
		// emailInfoDto.setText("你好！");
		// emailInfoDto.setSubject("打招呼");
		// try {
		// commonService.sendMail(emailInfoDto);
		// logger.info("发送邮件成功！");
		// //
		// noticeSendEmailRecordDTO.setSendStatus(CommonConstants.IS_SENDMAIL_SUCCESS);
		// goodwillApplyMailHistoryDTO.setSendStatus(82771001);
		// } catch (Exception e) {
		// goodwillApplyMailHistoryDTO.setSendStatus(82771002);
		// logger.info("发送邮件失败！");
		// logger.info(e.getMessage());
		// throw new DALException(e.getMessage());
		//
		// } finally {
		// // return noticeSendEmailRecordService.insert( noticeSendEmailRecordDTO);
		// }
		// //
		// goodwillApplyMailHistoryDTO.setReceiverMail(goodwillDealerMailInfoPo.geteMail1());
		// goodwillApplyMailHistoryDTO.setTitle(emailInfoDto.getSubject());
		// goodwillApplyMailHistoryDTO.setContent(emailInfoDto.getText());
		// goodwillApplyMailHistoryDTO.setSendTime(new Date());
		// goodwillApplyMailHistoryService.insert(goodwillApplyMailHistoryDTO);
		// }
		//
		// } else {
		// // 如果驳回到非经销商、查询车厂端人员信息
		// GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo =
		// goodwilApplyAuditProcessMapper
		// .selectById(Integer.valueOf(goodwillAuditInfoDTO.getReturnTo()));
		//
		// }

		return 1;
	}

	/**
	 * 根据经销商代码查询经销商详情
	 *
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	public Map queryDealer(String dealerCode) {
		Map map = new HashMap(16);
		String successCode = "0";
		String requestUrl = midUrlProperties.getMidEndOrgCenter()+midUrlProperties.getSelectByCompanyCode();
		IsExistByCodeDTO dto = new IsExistByCodeDTO();
		dto.setCompanyCode(dealerCode);
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<IsExistByCodeDTO> httpEntity = new HttpEntity<>(dto, httpHeaders);
		ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrl, HttpMethod.POST, httpEntity,
				ResponseDTO.class);
		List<Map> interList = new ArrayList<>();
		List<Map> resultList = new ArrayList<>();
		// 接口数据
		if (responseEntity.getBody() != null) {
			if (successCode.equals(responseEntity.getBody().getReturnCode())) {
				ObjectMapper objectMapper = new ObjectMapper();
				interList = objectMapper.convertValue(responseEntity.getBody().getData(), List.class);
				if (!CommonUtils.isNullOrEmpty(interList)) {
					for (Object object : interList) {
						JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
						map = jsonObject.toJavaObject(Map.class);
					}
				}

			} else {
				throw new DALException("根据经销商代码查询经销商详情接口异常，请稍后再试");
			}
		}
		return map;

	}

	/**
	 * 查询驳回单位下拉框
	 *
	 * @param
	 * @param
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public List<Map> queryReturnTo(Long id, Integer goodwillStatus) {
		Integer auditObject = null;
		if (goodwillStatus == CommonConstants.GOODWILL_STATUS_APPLY_AUDIT
				|| goodwillStatus == CommonConstants.GOODWILL_STATUS_APPLY_RETURN) {
			auditObject = 0;
		}
		if (goodwillStatus == CommonConstants.GOODWILL_STATUS_MATERIAL_AUDIT
				|| goodwillStatus == CommonConstants.GOODWILL_STATUS_MATERIAL_RETURN) {
			auditObject = 1;
		}
		List<Map> list = goodwilApplyAuditProcessMapper.queryReturnTo(id, auditObject);
		if (!CommonUtils.isNullOrEmpty(list)) {
			return list;
		} else {
			return new ArrayList<>();
		}

	}

	/**
	 * 根据VIN查询车辆亲善申请记录
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 * @since 2020-05-23
	 */
	@Override
	public List<GoodwillApplyInfoPO> queryApplyHistory(String vin, String applyNo) {
		List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.queryApplyHistory(vin, applyNo);
		if (!CommonUtils.isNullOrEmpty(list)) {
			return list;
		} else {
			return new ArrayList<>();
		}

	}

	/**
	 * 通过组织id和角色代码查询员工列表
	 *
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	public List<Map> queryDealer(Integer orgId, Integer orgType, Map roleCode) {

		String successCode = "0";
		String requestUrl = midUrlProperties.getMidEndAuthCenter() + midUrlProperties.getRoleInfo();
		Map map = new HashMap(16);
		map.put("orgId", orgId);
		map.put("orgType", orgType);
		map.put("roleCode", roleCode);
		Map data = new HashMap(16);
		data.put("data", map);
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Map> httpEntity = new HttpEntity<>(data, httpHeaders);
		ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrl, HttpMethod.POST, httpEntity,
				ResponseDTO.class);
		List<Map> interList = new ArrayList<>();
		List<Map> resultList = new ArrayList<>();
		// 接口数据
		if (responseEntity.getBody() != null) {
			if (successCode.equals(responseEntity.getBody().getReturnCode())) {
				ObjectMapper objectMapper = new ObjectMapper();
				interList = objectMapper.convertValue(responseEntity.getBody().getData(), List.class);
				if (!CommonUtils.isNullOrEmpty(interList)) {
					for (Object object : interList) {
						JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
						Map map1 = jsonObject.toJavaObject(Map.class);
						resultList.add(map1);
					}
				}

			} else {
				throw new DALException("根据经销商代码查询经销商详情接口异常，请稍后再试");
			}
		}
		return resultList;

	}

	/**
	 * 根据审批流程转译对应的角色
	 *
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	public String queryRole(Integer auditObject, Integer auditType, Integer position) {
		String roleCode = "";
		if (auditObject == 0) {
			if (auditType == 0) {
				if (position == 1) {
					roleCode = "SHQYJL";
				} else if (position == 2) {
					roleCode = "SHDQJL";
				} else if (position == 3) {
					roleCode = "SHQYZJ";
				} else if (position == 4) {
					roleCode = "VP";
				} else if (position == 5) {
					roleCode = "CEO";
				}
			} else if (auditType == 1) {
				if (position == 1) {
					roleCode = "CCMGJJL";
				} else if (position == 2) {
					roleCode = "CCMZJ";
				}
			}
		} else if (auditObject == 1) {
			if (auditType == 0) {
				if (position == 1) {
					roleCode = "SHQYJL";
				} else if (position == 2) {
					roleCode = "SHDQJL";
				} else if (position == 3) {
					roleCode = "CCMQ";
				} else if (position == 4) {
					roleCode = "SHQYZJ";
				} else if (position == 5) {
					roleCode = "VP";
				} else if (position == 6) {
					roleCode = "OEM-CWJL";
				} else if (position == 7) {
					roleCode = "CFO";
				} else if (position == 8) {
					roleCode = "CEO";
				}
			} else if (auditType == 1) {

				if (position == 1) {
					roleCode = "CCMGJJL";
				} else if (position == 2) {
					roleCode = "CCMQ";
				} else if (position == 3) {
					roleCode = "CCMZJ";
				}
			}
		}
		return roleCode;

	}

	/**
	 * 查询经销商年度申请信息
	 *
	 * @param dealerCode
	 * @param year
	 * @return Map
	 * <AUTHOR>
	 * @since 2020/6/6
	 */
	@Override
	public Map queryApplyByDealerCode(String dealerCode, Integer year) {
		Map map = goodwillApplyInfoMapper.queryApplyByDealerCode(dealerCode, year);
		if (CommonUtils.isNullOrEmpty(map)) {
			return new HashMap(16);
		} else {
			return map;
		}

	}

	/**
	 * 查询经销商待办事项列表
	 *
	 * @return Map
	 * <AUTHOR>
	 * @since 2020/6/6
	 */
	@Override
	public IPage<GoodwillApplyInfoDTO> getDealerTodo(Page page, GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		if (goodwillApplyInfoDTO == null) {
			goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		}
		// 获取登录用户信息
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		goodwillApplyInfoDTO.setUserId(loginInfoDto.getUserId());
		GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoDTO.transDtoToPo(GoodwillApplyInfoPO.class);

		List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.selectDealerTodoGoodwillApplyInfo(page,
				goodwillApplyInfoDTO);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillApplyInfoDTO> result = list.stream().map(m -> m.transPoToDto(GoodwillApplyInfoDTO.class))
					.collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 提交预申请单材料上传信息
	 *
	 * @param goodwillApplyInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-06-10
	 */
	@Override
	public int commitDealerSupportApplyInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		// 根据申请单ID查询是否上传了附件
		GoodwillApplyFileInfoPO goodwillApplyFileInfoPo = new GoodwillApplyFileInfoPO();
		goodwillApplyFileInfoPo.setApplyNo(goodwillApplyInfoDTO.getApplyNo());
		List<GoodwillApplyFileInfoPO> list = goodwillApplyFileInfoMapper
				.selectMaterialUploadList(goodwillApplyFileInfoPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			throw new DALException("请上传材料后再提交！");
		}
		GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoMapper.selectById(goodwillApplyInfoDTO.getId());
		goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_MATERIAL_AUDIT);
		// 对对象进行赋值操作
		// goodwillApplyInfoDTO.transDtoToPo(goodwillApplyInfoPo);
		if (goodwillApplyInfoPo.getMaterialCommitTime() == null) {
			goodwillApplyInfoPo.setMaterialCommitTime(new Date());
		}

		// 获取当前审核信息 - 发送邮件
		List<GoodwilApplyAuditProcessPO> po = goodwilApplyAuditProcessMapper
				.selectByGoodwillApplyId(goodwillApplyInfoPo.getId());
		if (!CommonUtils.isNullOrEmpty(po)) {
			for (GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo : po) {
				// 1.实时提醒-商务亲善材料审核提醒
				Integer mailType = 82461005;// 确认亲善邮件类型
				int sendToFlag = 2; // 1,经销商 2，角色

				this.sendEmail(goodwillApplyInfoPo, sendToFlag, goodwilApplyAuditProcessPo.getAuditRole(), mailType);
			}
		}
		goodwillApplyInfoPo.setUpdatedPerson(loginInfoDto.getUserName());
		// 执行更新
		int row = goodwillApplyInfoMapper.updateById(goodwillApplyInfoPo);
		return row;
	}

	/**
	 * 保存亲善预申请单信息
	 *
	 * @param goodwillApplyInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-06
	 */
	@Override
	public int editSupportApplyInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		// 亲善信息插入
		GoodwillMaterialAuditPO goodwillMaterialAuditPo = goodwillMaterialAuditMapper
				.selectById(goodwillApplyInfoDTO.getMaterialId());
		GoodwillMaterialAuditDTO goodwillMaterialAuditDTO = new GoodwillMaterialAuditDTO();
		// goodwillMaterialAuditDTO.setGoodwillApplyId(goodwillApplyInfoDTO.getId());
		if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getCustomerBackgroundEn())) {
			goodwillMaterialAuditDTO.setCustomerBackgroundEn(goodwillApplyInfoDTO.getCustomerBackgroundEn());
		}
		if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getReasonAndDisposeEn())) {
			goodwillMaterialAuditDTO.setReasonAndDisposeEn(goodwillApplyInfoDTO.getReasonAndDisposeEn());
		}
		if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getRepairSolutionEn())) {
			goodwillMaterialAuditDTO.setRepairSolutionEn(goodwillApplyInfoDTO.getRepairSolutionEn());
		}
		if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getCustomerRequireEn())) {
			goodwillMaterialAuditDTO.setCustomerRequireEn(goodwillApplyInfoDTO.getCustomerRequireEn());
		}
		if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getPotentialRiskEn())) {
			goodwillMaterialAuditDTO.setPotentialRiskEn(goodwillApplyInfoDTO.getPotentialRiskEn());
		}
		if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getVrOrTjNoEn())) {
			goodwillMaterialAuditDTO.setVrOrTjNoEn(goodwillApplyInfoDTO.getVrOrTjNoEn());
		}
		if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getBusinessGoodwillApplyDetailEn())) {
			goodwillMaterialAuditDTO
					.setBusinessGoodwillApplyDetailEn(goodwillApplyInfoDTO.getBusinessGoodwillApplyDetailEn());
		}

		goodwillMaterialAuditDTO.transDtoToPo(goodwillMaterialAuditPo);
		int row = goodwillMaterialAuditMapper.updateById(goodwillMaterialAuditPo);
		return row;
	}

	/**
	 * 提交亲善预申请单信息
	 *
	 * @param goodwillApplyInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-23
	 */
	@Override
	public int commitCcmqSupportApplyInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoMapper.selectById(goodwillApplyInfoDTO.getId());
		// String dealerCode = goodwillApplyInfoPo.getDealerCode();
		// GoodwillApplyInfoPO pos = goodwillApplyInfoPo;
		// 对对象进行赋值操作
		// goodwillApplyInfoDTO.transDtoToPo(goodwillApplyInfoPo);
		// goodwillApplyInfoPo.setDealerCode(dealerCode);
		goodwillApplyInfoPo.setIsNeedTranslate(CommonConstants.DICT_IS_NO);
		goodwillApplyInfoPo.setUpdatedPerson(loginInfoDto.getUserName());
		// 执行更新
		goodwillApplyInfoMapper.updateById(goodwillApplyInfoPo);
		// 亲善信息插入
		GoodwillMaterialAuditPO goodwillMaterialAuditPo = goodwillMaterialAuditMapper
				.selectById(goodwillApplyInfoDTO.getMaterialId());
		GoodwillMaterialAuditDTO goodwillMaterialAuditDTO = new GoodwillMaterialAuditDTO();
		// goodwillMaterialAuditDTO.setGoodwillApplyId(goodwillApplyInfoDTO.getId());
		if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getCustomerBackgroundEn())) {
			goodwillMaterialAuditDTO.setCustomerBackgroundEn(goodwillApplyInfoDTO.getCustomerBackgroundEn());
		}
		if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getReasonAndDisposeEn())) {
			goodwillMaterialAuditDTO.setReasonAndDisposeEn(goodwillApplyInfoDTO.getReasonAndDisposeEn());
		}
		if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getRepairSolutionEn())) {
			goodwillMaterialAuditDTO.setRepairSolutionEn(goodwillApplyInfoDTO.getRepairSolutionEn());
		}
		if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getCustomerRequireEn())) {
			goodwillMaterialAuditDTO.setCustomerRequireEn(goodwillApplyInfoDTO.getCustomerRequireEn());
		}
		if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getPotentialRiskEn())) {
			goodwillMaterialAuditDTO.setPotentialRiskEn(goodwillApplyInfoDTO.getPotentialRiskEn());
		}
		if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getVrOrTjNoEn())) {
			goodwillMaterialAuditDTO.setVrOrTjNoEn(goodwillApplyInfoDTO.getVrOrTjNoEn());
		}
		if (!StringUtils.isNullOrEmpty(goodwillApplyInfoDTO.getBusinessGoodwillApplyDetailEn())) {
			goodwillMaterialAuditDTO
					.setBusinessGoodwillApplyDetailEn(goodwillApplyInfoDTO.getBusinessGoodwillApplyDetailEn());
		}

		goodwillMaterialAuditDTO.transDtoToPo(goodwillMaterialAuditPo);
		int row = goodwillMaterialAuditMapper.updateById(goodwillMaterialAuditPo);

		// 获取当前审核信息 - 发送邮件
		List<GoodwilApplyAuditProcessPO> po = goodwilApplyAuditProcessMapper
				.selectByGoodwillApplyId(goodwillApplyInfoPo.getId());
		if (!CommonUtils.isNullOrEmpty(po)) {
			for (GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo : po) {
				// 1.实时提醒-商务亲善材料审核提醒
				Integer mailType = 82461005;// 确认亲善邮件类型
				int sendToFlag = 2; // 1,经销商 2，角色

				this.sendEmail(goodwillApplyInfoPo, sendToFlag, goodwilApplyAuditProcessPo.getAuditRole(), mailType);
			}
		}

		return row;
	}

	/**
	 * 根据DTO 进行数据新增 通知开票
	 *
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int editNoticeInvoiceInfo(GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto) {
		logger.info("editNoticeInvoiceInfo,goodwillNoticeInvoiceInfoDto:{}", goodwillNoticeInvoiceInfoDto);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		// 对对象进行赋值操作
		GoodwillNoticeInvoiceInfoPO goodwillNoticeInvoiceInfoPO = goodwillNoticeInvoiceInfoDto
				.transDtoToPo(GoodwillNoticeInvoiceInfoPO.class);

		// 录入发票信息插入开票id数据
		GoodwillInvoiceRecordPO goodwillInvoiceRecordPO = new GoodwillInvoiceRecordPO();

		GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoMapper
				.selectById(goodwillNoticeInvoiceInfoDto.getGoodwillApplyId());
		Integer mailType = 0;
		if (!StringUtils.isNullOrEmpty(goodwillNoticeInvoiceInfoDto.getInvoiceTitle())) {
			if ("82061001".equals(goodwillNoticeInvoiceInfoDto.getInvoiceTitle())) {
				mailType = 82461014;// 确认亲善邮件类型 - 商务亲善开票通知-VCDC进口车
			} else if ("82061002".equals(goodwillNoticeInvoiceInfoDto.getInvoiceTitle())) {
				mailType = 82461015;// 确认亲善邮件类型 - 商务亲善开票通知-VCAP国产车
			} else {
				mailType = 82461016;// 确认亲善邮件类型 - 商务亲善开票通知-其他
			}
		}
		int sendToFlag = 1; // 1,经销商 2，角色
		// 对对象进行赋值操作
		if (goodwillNoticeInvoiceInfoDto.getNoticeInvoicePrice().compareTo(BigDecimal.ZERO) != 0
				&& goodwillNoticeInvoiceInfoDto.getVoucherCouponFaceRechargePrice().compareTo(BigDecimal.ZERO) != 0
				&& goodwillNoticeInvoiceInfoDto.getVolvoCreditsRechargePrice().compareTo(BigDecimal.ZERO) != 0) {
			// 现金代金券积分都有
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoPo.getOwnerNo())) {
				goodwillNoticeInvoiceInfoPO.setNoticeInvoiceDate(new Date());
				goodwillNoticeInvoiceInfoPO.setInvoiceObject(0);
				goodwillNoticeInvoiceInfoPO.setIsCommit(CommonConstants.DICT_IS_NO);
				goodwillNoticeInvoiceInfoPO.setVoucherType(0);
				goodwillNoticeInvoiceInfoPO.setVoucherStatus(0);
				goodwillNoticeInvoiceInfoPO.setVoucherTime(new Date());
				goodwillNoticeInvoiceInfoPO.setCreditsStatus(0);
				goodwillNoticeInvoiceInfoPO.setCreditsTime(new Date());
				// 执行插入
				goodwillNoticeInvoiceInfoMapper.insert(goodwillNoticeInvoiceInfoPO);
				goodwillInvoiceRecordPO.setGoodwillApplyId(goodwillNoticeInvoiceInfoDto.getGoodwillApplyId());
				goodwillInvoiceRecordPO.setInvoiceId(goodwillNoticeInvoiceInfoDto.getInvoiceId());
				// 执行插入
				goodwillInvoiceRecordMapper.insert(goodwillInvoiceRecordPO);
				// 调用中台接口，发送亲善代金券信息

				CouponReturnVO responseDto = this.saveCoupon(goodwillApplyInfoPo, goodwillNoticeInvoiceInfoDto);
				if (!StringUtils.isNullOrEmpty(responseDto.getId())) {
					// 商务亲善开票通知
					this.sendEmail(goodwillApplyInfoPo, sendToFlag, goodwillApplyInfoPo.getDealerCode(), 82461017);
					this.saveCoupons(goodwillApplyInfoPo, goodwillNoticeInvoiceInfoDto, responseDto.getId());
				}
				goodwillApplyInfoPo.setCouponId(responseDto.getId());
				goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_ENTRY_OR_TOPUP);
				goodwillApplyInfoPo.setInvoiceAmount(goodwillNoticeInvoiceInfoDto.getNoticeInvoicePrice());

				// 商务亲善开票通知
				this.sendEmail(goodwillApplyInfoPo, sendToFlag, null, mailType);

				// TODO 代金券充值后，需要有 商务亲善代金券/积分开票通知 邮件
			} else {
				Long memberId = this.getMemberId(goodwillApplyInfoPo);
				if (memberId != 0) {
					goodwillApplyInfoPo.setOwnerNo(memberId.toString());
					goodwillNoticeInvoiceInfoPO.setNoticeInvoiceDate(new Date());
					goodwillNoticeInvoiceInfoPO.setInvoiceObject(0);
					goodwillNoticeInvoiceInfoPO.setIsCommit(CommonConstants.DICT_IS_NO);
					goodwillNoticeInvoiceInfoPO.setVoucherType(0);
					goodwillNoticeInvoiceInfoPO.setVoucherStatus(0);
					goodwillNoticeInvoiceInfoPO.setVoucherTime(new Date());
					goodwillNoticeInvoiceInfoPO.setCreditsStatus(0);
					goodwillNoticeInvoiceInfoPO.setCreditsTime(new Date());
					// 执行插入
					goodwillNoticeInvoiceInfoMapper.insert(goodwillNoticeInvoiceInfoPO);
					goodwillInvoiceRecordPO.setGoodwillApplyId(goodwillNoticeInvoiceInfoDto.getGoodwillApplyId());
					goodwillInvoiceRecordPO.setInvoiceId(goodwillNoticeInvoiceInfoDto.getInvoiceId());
					// 执行插入
					goodwillInvoiceRecordMapper.insert(goodwillInvoiceRecordPO);
					// 调用中台接口，发送亲善代金券信息

					CouponReturnVO responseDto = this.saveCoupon(goodwillApplyInfoPo, goodwillNoticeInvoiceInfoDto);
					if (!StringUtils.isNullOrEmpty(responseDto.getId())) {
						// 商务亲善开票通知
						this.sendEmail(goodwillApplyInfoPo, sendToFlag, goodwillApplyInfoPo.getDealerCode(), 82461017);
						this.saveCoupons(goodwillApplyInfoPo, goodwillNoticeInvoiceInfoDto, responseDto.getId());
					}
					goodwillApplyInfoPo.setCouponId(responseDto.getId());
					goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_ENTRY_OR_TOPUP);
					goodwillApplyInfoPo.setInvoiceAmount(goodwillNoticeInvoiceInfoDto.getNoticeInvoicePrice());

					// 商务亲善开票通知
					this.sendEmail(goodwillApplyInfoPo, sendToFlag, null, mailType);

					// TODO 代金券充值后，需要有 商务亲善代金券/积分开票通知 邮件
				} else {
					throw new ServiceBizException("未注册沃世界，请先注册");
				}
			}

		} else if (goodwillNoticeInvoiceInfoDto.getNoticeInvoicePrice().compareTo(BigDecimal.ZERO) != 0
				&& (goodwillNoticeInvoiceInfoDto.getVoucherCouponFaceRechargePrice().compareTo(BigDecimal.ZERO) != 0
				|| goodwillNoticeInvoiceInfoDto.getVolvoCreditsRechargePrice()
				.compareTo(BigDecimal.ZERO) != 0)) {
			// 有现金，代金券积分两者有一
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoPo.getOwnerNo())) {
				goodwillNoticeInvoiceInfoPO.setNoticeInvoiceDate(new Date());
				goodwillNoticeInvoiceInfoPO.setInvoiceObject(0);
				goodwillNoticeInvoiceInfoPO.setIsCommit(CommonConstants.DICT_IS_NO);
				goodwillNoticeInvoiceInfoPO.setVoucherType(0);
				// 调用中台接口，发送亲善代金券信息
				if (goodwillNoticeInvoiceInfoDto.getVoucherCouponFaceRechargePrice().compareTo(BigDecimal.ZERO) != 0
						&& goodwillNoticeInvoiceInfoDto.getVolvoCreditsRechargePrice()
						.compareTo(BigDecimal.ZERO) == 0) {
					goodwillNoticeInvoiceInfoPO.setVoucherStatus(0);
					goodwillNoticeInvoiceInfoPO.setVoucherTime(new Date());
				} else if (goodwillNoticeInvoiceInfoDto.getVoucherCouponFaceRechargePrice().compareTo(BigDecimal.ZERO) == 0
						&& goodwillNoticeInvoiceInfoDto.getVolvoCreditsRechargePrice()
						.compareTo(BigDecimal.ZERO) != 0) {
					goodwillNoticeInvoiceInfoPO.setCreditsStatus(0);
					goodwillNoticeInvoiceInfoPO.setCreditsTime(new Date());
				}
				// 执行插入
				goodwillNoticeInvoiceInfoMapper.insert(goodwillNoticeInvoiceInfoPO);
				goodwillInvoiceRecordPO.setGoodwillApplyId(goodwillNoticeInvoiceInfoDto.getGoodwillApplyId());
				goodwillInvoiceRecordPO.setInvoiceId(goodwillNoticeInvoiceInfoDto.getInvoiceId());
				// 执行插入
				goodwillInvoiceRecordMapper.insert(goodwillInvoiceRecordPO);
				if (goodwillNoticeInvoiceInfoDto.getVoucherCouponFaceRechargePrice().compareTo(BigDecimal.ZERO) != 0
						&& goodwillNoticeInvoiceInfoDto.getVolvoCreditsRechargePrice()
						.compareTo(BigDecimal.ZERO) == 0) {
					// 调用中台接口，发送亲善代金券信息
					CouponReturnVO responseDto = this.saveCoupon(goodwillApplyInfoPo, goodwillNoticeInvoiceInfoDto);
					if (!StringUtils.isNullOrEmpty(responseDto.getId())) {
						// 商务亲善开票通知
						this.sendEmail(goodwillApplyInfoPo, sendToFlag, goodwillApplyInfoPo.getDealerCode(), 82461017);
						this.saveCoupons(goodwillApplyInfoPo, goodwillNoticeInvoiceInfoDto, responseDto.getId());
					}
					goodwillApplyInfoPo.setCouponId(responseDto.getId());
				}
				goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_ENTRY_OR_TOPUP);
				goodwillApplyInfoPo.setInvoiceAmount(goodwillNoticeInvoiceInfoDto.getNoticeInvoicePrice());

				// 商务亲善开票通知
				this.sendEmail(goodwillApplyInfoPo, sendToFlag, null, mailType);
				// TODO 代金券充值后，需要有 商务亲善代金券/积分开票通知 邮件
			} else {
				Long memberId = this.getMemberId(goodwillApplyInfoPo);
				if (memberId != 0) {
					goodwillApplyInfoPo.setOwnerNo(memberId.toString());
					goodwillNoticeInvoiceInfoPO.setNoticeInvoiceDate(new Date());
					goodwillNoticeInvoiceInfoPO.setInvoiceObject(0);
					goodwillNoticeInvoiceInfoPO.setIsCommit(CommonConstants.DICT_IS_NO);
					goodwillNoticeInvoiceInfoPO.setVoucherType(0);
					// 调用中台接口，发送亲善代金券信息
					if (goodwillNoticeInvoiceInfoDto.getVoucherCouponFaceRechargePrice().compareTo(BigDecimal.ZERO) != 0
							&& goodwillNoticeInvoiceInfoDto.getVolvoCreditsRechargePrice()
							.compareTo(BigDecimal.ZERO) == 0) {
						goodwillNoticeInvoiceInfoPO.setVoucherStatus(0);
						goodwillNoticeInvoiceInfoPO.setVoucherTime(new Date());
					} else if (goodwillNoticeInvoiceInfoDto.getVoucherCouponFaceRechargePrice().compareTo(BigDecimal.ZERO) == 0
							&& goodwillNoticeInvoiceInfoDto.getVolvoCreditsRechargePrice()
							.compareTo(BigDecimal.ZERO) != 0) {
						goodwillNoticeInvoiceInfoPO.setCreditsStatus(0);
						goodwillNoticeInvoiceInfoPO.setCreditsTime(new Date());
					}
					// 执行插入
					goodwillNoticeInvoiceInfoMapper.insert(goodwillNoticeInvoiceInfoPO);
					goodwillInvoiceRecordPO.setGoodwillApplyId(goodwillNoticeInvoiceInfoDto.getGoodwillApplyId());
					goodwillInvoiceRecordPO.setInvoiceId(goodwillNoticeInvoiceInfoDto.getInvoiceId());
					// 执行插入
					goodwillInvoiceRecordMapper.insert(goodwillInvoiceRecordPO);
					// 调用中台接口，发送亲善代金券信息
					if (goodwillNoticeInvoiceInfoDto.getVoucherCouponFaceRechargePrice().compareTo(BigDecimal.ZERO) != 0
							&& goodwillNoticeInvoiceInfoDto.getVolvoCreditsRechargePrice()
							.compareTo(BigDecimal.ZERO) == 0) {
						// 调用中台接口，发送亲善代金券信息
						CouponReturnVO responseDto = this.saveCoupon(goodwillApplyInfoPo, goodwillNoticeInvoiceInfoDto);
						if (!StringUtils.isNullOrEmpty(responseDto.getId())) {
							// 商务亲善开票通知
							this.sendEmail(goodwillApplyInfoPo, sendToFlag, goodwillApplyInfoPo.getDealerCode(),
									82461017);
							this.saveCoupons(goodwillApplyInfoPo, goodwillNoticeInvoiceInfoDto, responseDto.getId());
						}
						goodwillApplyInfoPo.setCouponId(responseDto.getId());
					}
					goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_ENTRY_OR_TOPUP);
					goodwillApplyInfoPo.setInvoiceAmount(goodwillNoticeInvoiceInfoDto.getNoticeInvoicePrice());

					// 商务亲善开票通知
					this.sendEmail(goodwillApplyInfoPo, sendToFlag, null, mailType);
					// TODO 代金券充值后，需要有 商务亲善代金券/积分开票通知 邮件
				} else {
					throw new ServiceBizException("未注册沃世界，请先注册");
				}
			}

		} else if (goodwillNoticeInvoiceInfoDto.getNoticeInvoicePrice().compareTo(BigDecimal.ZERO) == 0
				&& goodwillNoticeInvoiceInfoDto.getVoucherCouponFaceRechargePrice().compareTo(BigDecimal.ZERO) != 0
				&& goodwillNoticeInvoiceInfoDto.getVolvoCreditsRechargePrice().compareTo(BigDecimal.ZERO) != 0) {
			// 没有现金，只有代金券和积分
			// 调用中台接口，发送亲善代金券信息
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoPo.getOwnerNo())) {

				// 调用中台接口，发送亲善代金券和积分信息
				goodwillNoticeInvoiceInfoPO.setNoticeInvoiceDate(new Date());
				goodwillNoticeInvoiceInfoPO.setInvoiceObject(0);
				goodwillNoticeInvoiceInfoPO.setIsCommit(CommonConstants.DICT_IS_NO);
				goodwillNoticeInvoiceInfoPO.setVoucherType(0);
				goodwillNoticeInvoiceInfoPO.setVoucherStatus(0);
				goodwillNoticeInvoiceInfoPO.setVoucherTime(new Date());
				goodwillNoticeInvoiceInfoPO.setCreditsStatus(0);
				goodwillNoticeInvoiceInfoPO.setCreditsTime(new Date());
				// 执行插入
				goodwillNoticeInvoiceInfoMapper.insert(goodwillNoticeInvoiceInfoPO);
				CouponReturnVO responseDto = this.saveCoupon(goodwillApplyInfoPo, goodwillNoticeInvoiceInfoDto);
				if (!StringUtils.isNullOrEmpty(responseDto.getId())) {
					// 商务亲善开票通知
					this.sendEmail(goodwillApplyInfoPo, sendToFlag, goodwillApplyInfoPo.getDealerCode(), 82461017);
					this.saveCoupons(goodwillApplyInfoPo, goodwillNoticeInvoiceInfoDto, responseDto.getId());
				}
				goodwillApplyInfoPo.setCouponId(responseDto.getId());

				goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_TOPUP);
				// TODO 代金券充值后，需要有 商务亲善代金券/积分开票通知 邮件
			} else {
				Long memberId = this.getMemberId(goodwillApplyInfoPo);
				if (memberId != 0) {
					goodwillApplyInfoPo.setOwnerNo(memberId.toString());

					// 调用中台接口，发送亲善代金券和积分信息
					goodwillNoticeInvoiceInfoPO.setNoticeInvoiceDate(new Date());
					goodwillNoticeInvoiceInfoPO.setInvoiceObject(0);
					goodwillNoticeInvoiceInfoPO.setIsCommit(CommonConstants.DICT_IS_NO);
					goodwillNoticeInvoiceInfoPO.setVoucherType(0);
					goodwillNoticeInvoiceInfoPO.setVoucherStatus(0);
					goodwillNoticeInvoiceInfoPO.setVoucherTime(new Date());
					goodwillNoticeInvoiceInfoPO.setCreditsStatus(0);
					goodwillNoticeInvoiceInfoPO.setCreditsTime(new Date());
					// 执行插入
					goodwillNoticeInvoiceInfoMapper.insert(goodwillNoticeInvoiceInfoPO);
					CouponReturnVO responseDto = this.saveCoupon(goodwillApplyInfoPo, goodwillNoticeInvoiceInfoDto);
					if (!StringUtils.isNullOrEmpty(responseDto.getId())) {
						// 商务亲善开票通知
						this.sendEmail(goodwillApplyInfoPo, sendToFlag, goodwillApplyInfoPo.getDealerCode(), 82461017);
						this.saveCoupons(goodwillApplyInfoPo, goodwillNoticeInvoiceInfoDto, responseDto.getId());
					}
					goodwillApplyInfoPo.setCouponId(responseDto.getId());
					goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_TOPUP);
					// TODO 代金券充值后，需要有 商务亲善代金券/积分开票通知 邮件
				} else {
					throw new ServiceBizException("未注册沃世界，请先注册");
				}
			}

		} else if (goodwillNoticeInvoiceInfoDto.getNoticeInvoicePrice().compareTo(BigDecimal.ZERO) == 0
				&& goodwillNoticeInvoiceInfoDto.getVoucherCouponFaceRechargePrice().compareTo(BigDecimal.ZERO) != 0
				&& goodwillNoticeInvoiceInfoDto.getVolvoCreditsRechargePrice().compareTo(BigDecimal.ZERO) == 0) {
			// 只有代金券
			// 调用中台接口，发送亲善代金券信息
			if (!StringUtils.isNullOrEmpty(goodwillApplyInfoPo.getOwnerNo())) {

				// 调用中台接口，发送亲善代金券信息
				goodwillNoticeInvoiceInfoPO.setNoticeInvoiceDate(new Date());
				goodwillNoticeInvoiceInfoPO.setInvoiceObject(0);
				goodwillNoticeInvoiceInfoPO.setIsCommit(CommonConstants.DICT_IS_NO);
				goodwillNoticeInvoiceInfoPO.setVoucherType(0);
				goodwillNoticeInvoiceInfoPO.setVoucherStatus(0);
				goodwillNoticeInvoiceInfoPO.setVoucherTime(new Date());
				// 执行插入
				goodwillNoticeInvoiceInfoMapper.insert(goodwillNoticeInvoiceInfoPO);

				// TODO 代金券充值后，需要有 商务亲善代金券/积分开票通知 邮件
				CouponReturnVO responseDto = this.saveCoupon(goodwillApplyInfoPo, goodwillNoticeInvoiceInfoDto);
				if (!StringUtils.isNullOrEmpty(responseDto.getId())) {
					// 商务亲善开票通知
					this.sendEmail(goodwillApplyInfoPo, sendToFlag, goodwillApplyInfoPo.getDealerCode(), 82461017);
					this.saveCoupons(goodwillApplyInfoPo, goodwillNoticeInvoiceInfoDto, responseDto.getId());
				}
				goodwillApplyInfoPo.setCouponId(responseDto.getId());
				goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_TOPUP);
			} else {
				Long memberId = this.getMemberId(goodwillApplyInfoPo);
				if (memberId != 0) {
					goodwillApplyInfoPo.setOwnerNo(memberId.toString());

					// 调用中台接口，发送亲善代金券信息
					goodwillNoticeInvoiceInfoPO.setNoticeInvoiceDate(new Date());
					goodwillNoticeInvoiceInfoPO.setInvoiceObject(0);
					goodwillNoticeInvoiceInfoPO.setIsCommit(CommonConstants.DICT_IS_NO);
					goodwillNoticeInvoiceInfoPO.setVoucherType(0);
					goodwillNoticeInvoiceInfoPO.setVoucherStatus(0);
					goodwillNoticeInvoiceInfoPO.setVoucherTime(new Date());
					// 执行插入
					goodwillNoticeInvoiceInfoMapper.insert(goodwillNoticeInvoiceInfoPO);

					// TODO 代金券充值后，需要有 商务亲善代金券/积分开票通知 邮件
					CouponReturnVO responseDto = this.saveCoupon(goodwillApplyInfoPo, goodwillNoticeInvoiceInfoDto);
					if (!StringUtils.isNullOrEmpty(responseDto.getId())) {
						// 商务亲善开票通知
						this.sendEmail(goodwillApplyInfoPo, sendToFlag, goodwillApplyInfoPo.getDealerCode(), 82461017);
						this.saveCoupons(goodwillApplyInfoPo, goodwillNoticeInvoiceInfoDto, responseDto.getId());
					}
					goodwillApplyInfoPo.setCouponId(responseDto.getId());
					goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_TOPUP);
				} else {
					throw new ServiceBizException("未注册沃世界，请先注册");
				}
			}

		} else if (goodwillNoticeInvoiceInfoDto.getNoticeInvoicePrice().compareTo(BigDecimal.ZERO) == 0
				&& goodwillNoticeInvoiceInfoDto.getVoucherCouponFaceRechargePrice().compareTo(BigDecimal.ZERO) == 0
				&& goodwillNoticeInvoiceInfoDto.getVolvoCreditsRechargePrice().compareTo(BigDecimal.ZERO) != 0) {
			// 只有积分
			// 调用中台接口，发送亲善积分信息
			goodwillNoticeInvoiceInfoPO.setNoticeInvoiceDate(new Date());
			goodwillNoticeInvoiceInfoPO.setInvoiceObject(0);
			goodwillNoticeInvoiceInfoPO.setIsCommit(CommonConstants.DICT_IS_NO);
			goodwillNoticeInvoiceInfoPO.setVoucherType(0);
			goodwillNoticeInvoiceInfoPO.setCreditsStatus(0);
			goodwillNoticeInvoiceInfoPO.setCreditsTime(new Date());
			// 执行插入
			goodwillNoticeInvoiceInfoMapper.insert(goodwillNoticeInvoiceInfoPO);

			// TODO 代金券充值后，需要有 商务亲善代金券/积分开票通知 邮件

			goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_TOPUP);
		} else if (goodwillNoticeInvoiceInfoDto.getNoticeInvoicePrice().compareTo(BigDecimal.ZERO) != 0
				&& goodwillNoticeInvoiceInfoDto.getVoucherCouponFaceRechargePrice().compareTo(BigDecimal.ZERO) == 0
				&& goodwillNoticeInvoiceInfoDto.getVolvoCreditsRechargePrice().compareTo(BigDecimal.ZERO) == 0) {
			// 只有现金
			goodwillNoticeInvoiceInfoPO.setNoticeInvoiceDate(new Date());
			goodwillNoticeInvoiceInfoPO.setInvoiceObject(0);
			goodwillNoticeInvoiceInfoPO.setIsCommit(CommonConstants.DICT_IS_NO);
			// 执行插入
			int is=	goodwillNoticeInvoiceInfoMapper.insert(goodwillNoticeInvoiceInfoPO);
			goodwillInvoiceRecordPO.setGoodwillApplyId(goodwillNoticeInvoiceInfoDto.getGoodwillApplyId());
			goodwillInvoiceRecordPO.setInvoiceId(goodwillNoticeInvoiceInfoDto.getInvoiceId());
			// 执行插入
			int  iss = goodwillInvoiceRecordMapper.insert(goodwillInvoiceRecordPO);
			goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_ENTRY_INVOICE);
			goodwillApplyInfoPo.setInvoiceAmount(goodwillNoticeInvoiceInfoDto.getNoticeInvoicePrice());

			// 商务亲善开票通知
			this.sendEmail(goodwillApplyInfoPo, sendToFlag, null, mailType);
		}
		goodwillApplyInfoPo.setUpdatedPerson(loginInfoDto.getUserName());
		// 执行更新
		int row = goodwillApplyInfoMapper.updateById(goodwillApplyInfoPo);

		// 返回插入的值
		return row;
	}

	private Long getMemberId(GoodwillApplyInfoPO goodwillApplyInfoPo) {
		logger.info("getOneId,goodwillApplyInfoPo:{}", goodwillApplyInfoPo);
		if (Objects.isNull(goodwillApplyInfoPo)) {
			return 0L;
		}
		String vin = goodwillApplyInfoPo.getVin();
		Long memberId = getMemberIdByVin(vin);
		logger.info("getOneId,memberId:{}", memberId);
		return memberId;
	}

	//作废
	private Long getOneIdByVin(String vin) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Long oneId = new Long(0);
		String successCode = "0";
		String requestUrl = midUrlProperties.getMidEndVehicleCenter() + midUrlProperties.getListOwnerVehiclePage();
		Map<String, Object> map = new HashMap<String, Object>();
		Map map1 = new HashMap<>(16);
		map1.put("vin", vin);
		// 查询对象参数转换MAP
		map.put("data", map1);
		map.put("page", 1);
		map.put("pageSize", 10);
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity httpEntity = new HttpEntity<>(map, httpHeaders);
		ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrl, HttpMethod.POST, httpEntity,
				ResponseDTO.class);
		logger.error("中台车主车辆查询参数：{}", map);

		if (responseEntity.getBody() != null) {
			if (successCode.equals(responseEntity.getBody().getReturnCode())) {
				ObjectMapper objectMapper = new ObjectMapper();
				objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
				logger.error("中台车主车辆查询结果：{}", responseEntity.getBody().getData());

				Map maps = objectMapper.convertValue(responseEntity.getBody().getData(), Map.class);
				if (maps != null) {
					List<Map> list = (List<Map>) maps.get("records");
					if (!CommonUtils.isNullOrEmpty(list)) {
						Map mapss = list.get(0);
						if (!StringUtils.isNullOrEmpty(mapss.get("oneId"))) {
							oneId = Long.valueOf(mapss.get("oneId").toString());
						}
					}
				}

			} else {
				logger.error("中台车主车辆查询报错：{}", responseEntity.getBody().getReturnMessage());
				throw new ServiceBizException("中台车主车辆查询报错");
			}
		}

		return oneId;

	}

	private Long getMemberIdByVin(String vin){
		long id = 0L;
		ListBindRelationDto dto = new ListBindRelationDto();
		dto.setVinCode(vin);
		dto.setIsOwner(1);
		logger.info("getMemberIdByVin,dto:{}",dto);
		ResponseDTO<List<ListBindRelationVo>> rDto = vehicleOwnershipClient.listBindRelation(dto);
		if(Objects.isNull(rDto) || Objects.isNull(rDto.getData())){
			logger.info("getMemberIdByVin,rDto is null || rDto.getData() is null");
			return id;
		}
		logger.info("getMemberIdByVin,rDto:{}",rDto);
		List<ListBindRelationVo> voList = rDto.getData();
		if(CollectionUtils.isEmpty(voList)){
			logger.info("getMemberIdByVin,CollectionUtils.isEmpty(voList)");
			return id;
		}
		ListBindRelationVo vo = voList.get(0);
		logger.info("getMemberIdByVin,vo:{}",vo);
		if(Objects.isNull(vo)){
			logger.info("getMemberIdByVin,vo is null");
			return id;
		}
		String memberId = vo.getMemberId();
		if(!StringUtils.isNullOrEmpty(memberId)){
			id = Long.parseLong(memberId);
			logger.info("getMemberIdByVin,id:{}",id);
		}
		return id;
	}

	public void saveCoupons(GoodwillApplyInfoPO goodwillApplyInfoPo,
							GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto, Long couponId) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		CouponPO couponPo = new CouponPO();
		couponPo.setAppId(loginInfoDto.getAppId());
		couponPo.setMidCouponId(couponId);
		couponPo.setIsGoodwill(CommonConstants.DICT_IS_YES);
		couponPo.setCouponPurpose(83171002);
		couponPo.setAvailableTotalCount(1);
		couponPo.setPurponseDesc("仅限购券经销商处使用");

		couponPo.setOwnerCode(loginInfoDto.getOwnerCode());
		couponPo.setCouponCode(goodwillApplyInfoPo.getApplyNo());// 编码
		couponPo.setCouponName("售后代金券");// 名称
		couponPo.setCouponStatus(82841003);// 状态
		couponPo.setCreatedBy(loginInfoDto.getUserId() + "");// 登录人
		couponPo.setCreatedByName(loginInfoDto.getUserName());// 登录人姓名
		couponPo.setValidityPeriodType(82851002);// 有效期类型
		couponPo.setCouponBeginDate(new Date());// 有效期开始日期
		Calendar c = Calendar.getInstance();
		c.add(Calendar.YEAR, +5);
		couponPo.setCouponEndDate(c.getTime());// 结束日期
		couponPo.setCouponInstructions(
				"1、 可用于购券经销商处原厂产品及服务的消费抵扣\n" +
						"2、 本代金券自发券日起有效期五年\n" +
						"3、 本代金券不设找零，不可提现");
		couponPo.setCouponDenomination(goodwillNoticeInvoiceInfoDto.getVoucherCouponFaceRechargePrice());// 面额 （这里单位是【元】
		couponPo.setStandardLimitDealerRequired(10041002);// 使用规则-标准规则-是否限定经销商（全部经销商10041001，部分经销商10041002） 这里应该是 部分经销商
		couponMapper.insert(couponPo);
		CouponUrStandardDealerPO po = new CouponUrStandardDealerPO();
		po.setAppId(loginInfoDto.getAppId());
		po.setOwnerCode(loginInfoDto.getOwnerCode());
		//取B端couponID
		po.setCouponId(couponPo.getId());
		po.setDealerCode(goodwillApplyInfoPo.getDealerCode());
		po.setDealerName(goodwillApplyInfoPo.getDealerName());
		po.setBigAreaId(goodwillApplyInfoPo.getAreaManageId().toString());
		po.setBigArea(goodwillApplyInfoPo.getAreaManage());
		po.setSmallAreaId(goodwillApplyInfoPo.getSmallAreaId().toString());
		po.setSmallArea(goodwillApplyInfoPo.getSmallArea());
		// po.setDealerAddress(dto.getAddressZh());
		couponUrStandardDealerMapper.insert(po);
	}

	/**
	 * 调用卡券中台接口
	 *
	 * @param
	 * @return
	 * @throws ServiceBizException
	 */
	public CouponReturnVO saveCoupon(GoodwillApplyInfoPO goodwillApplyInfoPo,
									 GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto) throws ServiceBizException {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		SaveCouponInfoDTO dto = new SaveCouponInfoDTO();
		dto.setCouponCode(goodwillApplyInfoPo.getApplyNo());
		dto.setCouponName("售后代金券");
		dto.setCouponType(31081004);
		dto.setAsList(31091002);
		dto.setIsOccupied(1);
		dto.setIsVolid(0);
		// dto.setCouponValue(goodwillNoticeInvoiceInfoDto.getVoucherRechargePrice().multiply(BigDecimal.valueOf(100)));
		dto.setServiceType(31111002);
		//dto.setCouponValue(goodwillNoticeInvoiceInfoDto.getVoucherRechargePrice().multiply(BigDecimal.valueOf(100)));
		dto.setCouponValue(goodwillNoticeInvoiceInfoDto.getVoucherCouponFaceRechargePrice().multiply(BigDecimal.valueOf(100)));
		dto.setPublishState(82841003);
		dto.setKindness(1);
		dto.setTermType(82851002);
		dto.setStartDate(sdf.format(new Date()));
		Calendar c = Calendar.getInstance();
		c.add(Calendar.YEAR, +5);
		dto.setEndDate(sdf.format(c.getTime()));
		UseRule useRule = new UseRule();
		List<String> list = new ArrayList<>();
		list.add(goodwillApplyInfoPo.getDealerCode());
		useRule.setLimitDealer(list);
		dto.setUseRule(useRule);
		dto.setTotalGet(1);
		dto.setCreator(loginInfoDto.getUserName());
		dto.setCreateBy(loginInfoDto.getUserId());
		dto.setUseScenes(83171002);
		dto.setCouponExplain("仅限购券经销商处使用");
		dto.setDenomination(
				"1、 可用于购券经销商处原厂产品及服务的消费抵扣\n" +
						"2、 本代金券自发券日起有效期五年\n" +
						"3、 本代金券不设找零，不可提现");
		// 调用中台接口，推送
		RequestDTO<SaveCouponInfoDTO> requestDto = new RequestDTO<SaveCouponInfoDTO>();
		requestDto.setData(dto);
		logger.info("创建卡券数据：{}", requestDto);
		CouponReturnVO responseDto = this.saveCouponToMidCouponCenter(requestDto);
		if (!StringUtils.isNullOrEmpty(responseDto.getId())) {
			SaveCouponDetailDTO dtos = new SaveCouponDetailDTO();
			dtos.setCouponId(responseDto.getId());
			dtos.setCouponSource(83241001);
			dtos.setGetDate(sdf.format(new Date()));
			String memberId =  goodwillApplyInfoPo.getOwnerNo();
			if (!StringUtils.isNullOrEmpty(memberId)) {
				dtos.setMemberId(Long.parseLong(memberId));
			}
			dtos.setTicketState(31061001);
			dtos.setVin(goodwillApplyInfoPo.getVin());
			// dtos.setLeftValue(0);
			// 调用中台接口，推送
			RequestDTO<SaveCouponDetailDTO> requestDtos = new RequestDTO<SaveCouponDetailDTO>();
			requestDtos.setData(dtos);
			logger.info("领用卡券数据：{}", requestDtos);
			Map responseDtos = this.getCouponDetail(requestDtos);
			// if (!StringUtils.isNullOrEmpty(responseDtos)) {
			// SaveCouponRechargeDTO dtoss = new SaveCouponRechargeDTO();
			// dtoss.setCouponId(responseDto.getId());
			//
			// String id = responseDtos.get("id").toString().substring(1,
			// responseDtos.get("id").toString().lastIndexOf("]"));
			//
			// dtoss.setCouponDetailId(Long.valueOf(id));
			// dtoss.setBeforeRechargeAmount(BigDecimal.ZERO);
			// dtoss.setAfterRechargeAmount(
			// goodwillNoticeInvoiceInfoDto.getVoucherRechargePrice().multiply(BigDecimal.valueOf(100)));
			// if (!StringUtils.isNullOrEmpty(goodwillApplyInfoPo.getOwnerNo())) {
			// dtoss.setCustomerId(Long.valueOf(goodwillApplyInfoPo.getOwnerNo()));
			// }
			// dtoss.setRechargeAmount(
			// goodwillNoticeInvoiceInfoDto.getVoucherRechargePrice().multiply(BigDecimal.valueOf(100)));
			// dtoss.setRechargeTime(sdf.format(new Date()));
			// // 调用中台接口，推送
			// RequestDTO<SaveCouponRechargeDTO> requestDtoss = new
			// RequestDTO<SaveCouponRechargeDTO>();
			// requestDtoss.setData(dtoss);
			// logger.info("准备卡券数据：{}", requestDtoss);
			// this.rechargeCouponDetail(requestDtoss);
			//
			// }

		}

		return responseDto;

	}

	/**
	 * 保存卡券到中台
	 *
	 * @param requestDto
	 * @return
	 * @throws ServiceBizException
	 */
	public CouponReturnVO saveCouponToMidCouponCenter(RequestDTO<SaveCouponInfoDTO> requestDto)
			throws ServiceBizException {
		// 准备请求头信息
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<RequestDTO> httpEntity = new HttpEntity<RequestDTO>(requestDto, httpHeaders);
		// 准备 url
		String requestUrls = midUrlProperties.getMidEndCouponCenter() + midUrlProperties.getTtCouponInfo();
		logger.info("新增卡券={}", JSON.toJSONString(requestDto));
		// 调用接口
		try {
			ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrls, HttpMethod.POST, httpEntity,
					ResponseDTO.class);
			if (responseEntity.getBody() != null) {
				if (SUCCESS_CODE.equals(responseEntity.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					CouponReturnVO convertValue = objectMapper.convertValue(responseEntity.getBody().getData(),
							CouponReturnVO.class);

					return convertValue;
				} else {
					logger.error("中台卡券保存报错：{}", responseEntity.getBody().getReturnMessage());
					throw new ServiceBizException("中台保存卡券接口失败");
				}
			} else {
				logger.error("中台卡券保存报错：{}", responseEntity.getBody().getReturnMessage());
				throw new ServiceBizException("中台保存卡券接口失败");
			}
		} catch (Exception e) {
			logger.error("中台卡券保存报错：{}", e.getMessage());
			throw new ServiceBizException("中台保存卡券接口失败");
		}

	}

	/**
	 * 中台卡券领用
	 *
	 * @param requestDto
	 * @return
	 * @throws ServiceBizException
	 */
	public Map getCouponDetail(RequestDTO<SaveCouponDetailDTO> requestDto) throws ServiceBizException {
		// 准备请求头信息
		// 准备请求头信息
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<RequestDTO> httpEntity = new HttpEntity<RequestDTO>(requestDto, httpHeaders);
		// 准备 url
		String requestUrls = midUrlProperties.getMidEndCouponCenter() + midUrlProperties.getTtCouponDetail();
		logger.info("getCouponDetail,领取卡券={}", JSON.toJSONString(requestDto));
		Map map = new HashMap<>(16);
		// 调用接口
		try {
			ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrls, HttpMethod.POST, httpEntity,
					ResponseDTO.class);
			logger.info("getCouponDetail,responseEntity:{}",JSON.toJSONString(responseEntity));
			if (responseEntity.getBody() != null) {
				if (SUCCESS_CODE.equals(responseEntity.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					// ResponseDTO convertValue =
					// objectMapper.convertValue(responseEntity.getBody(), ResponseDTO.class);
					map = objectMapper.convertValue(responseEntity.getBody().getData(), Map.class);
					return map;
				} else {
					logger.error("中台卡券领用报错：{}", responseEntity.getBody().getReturnMessage());
					throw new ServiceBizException("中台卡券领用接口失败");
				}
			} else {
				logger.error("中台卡券领用报错：{}", responseEntity.getBody().getReturnMessage());
				throw new ServiceBizException("中台卡券领用接口失败");
			}
		} catch (Exception e) {
			logger.error("中台卡券领用报错：{}", e.getMessage());
			throw new ServiceBizException("中台卡券领用接口失败");
		}

	}

	/**
	 * 中台卡券充值
	 *
	 * @param requestDto
	 * @return
	 * @throws ServiceBizException
	 */
	public Map rechargeCouponDetail(RequestDTO<SaveCouponRechargeDTO> requestDto) throws ServiceBizException {
		// 准备请求头信息
		// 准备请求头信息
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<RequestDTO> httpEntity = new HttpEntity<RequestDTO>(requestDto, httpHeaders);
		// 准备 url
		String requestUrls = midUrlProperties.getMidEndCouponCenter() + midUrlProperties.getCouponRecharge();
		logger.info("卡券充值={}", JSON.toJSONString(requestDto));
		Map map = new HashMap<>(16);
		// 调用接口
		try {
			ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrls, HttpMethod.POST, httpEntity,
					ResponseDTO.class);
			if (responseEntity.getBody() != null) {
				if (SUCCESS_CODE.equals(responseEntity.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					map = objectMapper.convertValue(responseEntity.getBody().getData(), Map.class);
					return map;
				} else {
					logger.error("中台卡券充值报错：{}", responseEntity.getBody().getReturnMessage());
					throw new ServiceBizException("中台卡券充值接口失败");
				}
			} else {
				logger.error("中台卡券充值报错：{}", responseEntity.getBody().getReturnMessage());
				throw new ServiceBizException("中台卡券充值接口失败");
			}
		} catch (Exception e) {
			logger.error("中台卡券充值报错：{}", e.getMessage());
			throw new ServiceBizException("中台卡券充值失败");
		}

	}

	/**
	 * 根据查询条件返回结果集
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillNoticeInvoiceInfoDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillNoticeInvoiceInfoDTO queryPaymentInfoByGoodwillApplyId(Long goodwillApplyId) {
		GoodwillNoticeInvoiceInfoPO goodwillNoticeInvoiceInfoPO = goodwillNoticeInvoiceInfoMapper
				.queryPaymentInfoByGoodwillApplyId(goodwillApplyId);
		if (goodwillNoticeInvoiceInfoPO != null) {
			return goodwillNoticeInvoiceInfoPO.transPoToDto(GoodwillNoticeInvoiceInfoDTO.class);
		} else {
			return new GoodwillNoticeInvoiceInfoDTO();
		}
	}

	/**
	 * 保存录入开票信息
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int saveInvoice(GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto) {
		int row = 0;
		if (goodwillNoticeInvoiceInfoDto != null) {
			if (!CommonUtils.isNullOrEmpty(goodwillNoticeInvoiceInfoDto.getGoodwillInvoiceRecordDTO())) {
				for (GoodwillInvoiceRecordDTO goodwillInvoiceRecordDto : goodwillNoticeInvoiceInfoDto
						.getGoodwillInvoiceRecordDTO()) {
					GoodwillInvoiceRecordPO goodwillInvoiceRecordPO = goodwillInvoiceRecordMapper
							.selectById(goodwillInvoiceRecordDto.getId());
					// 对对象进行赋值操作
					goodwillInvoiceRecordDto.transDtoToPo(goodwillInvoiceRecordPO);
					goodwillInvoiceRecordPO.setNoticeInvoiceId(goodwillInvoiceRecordDto.getNoticeInvoiceId());
					// 执行更新
					goodwillInvoiceRecordMapper.updateById(goodwillInvoiceRecordPO);
				}
			}
		}

		return row;
	}

	/**
	 * 提交录入开票信息
	 * 页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int commitInvoice(GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto) {
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		int row = 0;
		if (goodwillNoticeInvoiceInfoDto != null) {
			if (!CommonUtils.isNullOrEmpty(goodwillNoticeInvoiceInfoDto.getGoodwillInvoiceRecordDTO())) {

				for (GoodwillInvoiceRecordDTO goodwillInvoiceRecordDto : goodwillNoticeInvoiceInfoDto
						.getGoodwillInvoiceRecordDTO()) {
					GoodwillInvoiceRecordPO goodwillInvoiceRecordPO = goodwillInvoiceRecordMapper
							.selectById(goodwillInvoiceRecordDto.getId());
					// 对对象进行赋值操作
					goodwillInvoiceRecordDto.transDtoToPo(goodwillInvoiceRecordPO);
					goodwillInvoiceRecordPO.setNoticeInvoiceId(goodwillInvoiceRecordDto.getNoticeInvoiceId());
					// 执行更新
					goodwillInvoiceRecordMapper.updateById(goodwillInvoiceRecordPO);

					// // 更新通知开票表数据
					// GoodwillNoticeInvoiceInfoPO goodwillNoticeInvoiceInfoPO =
					// goodwillNoticeInvoiceInfoMapper
					// .selectById(goodwillNoticeInvoiceInfoDto.getId());
					// goodwillNoticeInvoiceInfoPO.setIsCommit(CommonConstants.DICT_IS_YES);
					// // 执行更新
					// goodwillNoticeInvoiceInfoMapper.updateById(goodwillNoticeInvoiceInfoPO);
					//
					goodwillNoticeInvoiceInfoMapper.updateStatus(goodwillInvoiceRecordDto.getInvoiceId());

				}
			}

			// 更新主单状态
			GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoMapper
					.selectById(goodwillNoticeInvoiceInfoDto.getGoodwillApplyId());
			if (goodwillApplyInfoPo.getGoodwillStatus() == CommonConstants.GOODWILL_STATUS_ENTRY_INVOICE) {
				goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_CONFIRM_INVOICE);
			}
			if (goodwillApplyInfoPo.getGoodwillStatus() == CommonConstants.GOODWILL_STATUS_ENTRY_OR_TOPUP) {
				goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_CONFIRM_OR_TOPUP);
			}
			goodwillApplyInfoPo.setUpdatedPerson(loginInfoDto.getUserName());
			goodwillApplyInfoMapper.updateById(goodwillApplyInfoPo);
		}
		return row;
	}

	/**
	 * 确认发票信息
	 * 页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int saveInvoiceConfirm(GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto) {
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		int row = 0;
		if (goodwillNoticeInvoiceInfoDto != null) {
			if (!CommonUtils.isNullOrEmpty(goodwillNoticeInvoiceInfoDto.getGoodwillInvoiceRecordDTO())) {

				for (GoodwillInvoiceRecordDTO goodwillInvoiceRecordDto : goodwillNoticeInvoiceInfoDto
						.getGoodwillInvoiceRecordDTO()) {
					GoodwillInvoiceRecordPO goodwillInvoiceRecordPO = goodwillInvoiceRecordMapper
							.selectById(goodwillInvoiceRecordDto.getId());
					// 对对象进行赋值操作
					goodwillInvoiceRecordDto.transDtoToPo(goodwillInvoiceRecordPO);
					// 执行更新
					goodwillInvoiceRecordMapper.updateById(goodwillInvoiceRecordPO);

					// 更新通知开票表数据
					GoodwillNoticeInvoiceInfoPO goodwillNoticeInvoiceInfoPO = goodwillNoticeInvoiceInfoMapper
							.selectById(goodwillInvoiceRecordDto.getNoticeInvoiceId());
					goodwillNoticeInvoiceInfoPO.setIsConfirm(CommonConstants.DICT_IS_YES);
					// 执行更新
					goodwillNoticeInvoiceInfoMapper.updateById(goodwillNoticeInvoiceInfoPO);

				}
			}

			// 更新主单状态
			GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoMapper
					.selectById(goodwillNoticeInvoiceInfoDto.getGoodwillApplyId());
			if (goodwillApplyInfoPo.getGoodwillStatus() == CommonConstants.GOODWILL_STATUS_CONFIRM_INVOICE) {
				goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_PAYMENTING);
			}
			if (goodwillApplyInfoPo.getGoodwillStatus() == CommonConstants.GOODWILL_STATUS_CONFIRM_OR_TOPUP) {
				goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_SPAYMENTING_OR_TOPUP);
			}
			goodwillApplyInfoPo.setUpdatedPerson(loginInfoDto.getUserName());
			goodwillApplyInfoMapper.updateById(goodwillApplyInfoPo);
		}
		return row;
	}

	@Override
	public List<Map> queryInvoiceInfo(Long applyId) {
		List<Map> list = goodwillInvoiceRecordMapper.queryInvoiceInfo(applyId);
		if (!CommonUtils.isNullOrEmpty(list)) {
			return list;
		} else {
			return new ArrayList<>();
		}

	}

	@Override
	public List<Map> queryOemInvoiceInfo(Long applyId) {
		List<Map> list = goodwillInvoiceRecordMapper.queryOemInvoiceInfo(applyId);
		if (!CommonUtils.isNullOrEmpty(list)) {
			return list;
		} else {
			return new ArrayList<>();
		}

	}

	/**
	 * 预申请单拒绝支持
	 *
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-23
	 */
	@Override
	public int refuseApplyGoodwillInfo(Long id, GoodwillAuditInfoDTO goodwillAuditInfoDTO) {
		// 获取登录用户信息
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		// 更新主单的审核金额
		GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoMapper.selectById(id);
		// 根绝审批流程表数据往审批记录表插入数据
		GoodwillAuditInfoPO goodwillAuditInfoPo = new GoodwillAuditInfoPO();
		if (goodwillApplyInfoPo.getGoodwillStatus() < CommonConstants.GOODWILL_STATUS_MATERIAL_UPLOAD) {
			goodwillAuditInfoPo.setAuditObject(0);
		} else if (goodwillApplyInfoPo.getGoodwillStatus() >= CommonConstants.GOODWILL_STATUS_MATERIAL_UPLOAD) {
			goodwillAuditInfoPo.setAuditObject(1);
		}

		goodwillAuditInfoPo.setGoodwillApplyId(id);
		goodwillAuditInfoPo.setAuditRole("");
		goodwillAuditInfoPo.setAuditOpinion(goodwillAuditInfoDTO.getAuditOpinion());
		goodwillAuditInfoPo.setAuditResult(82801004);
		goodwillAuditInfoPo.setAuditTime(new Date());
		goodwillAuditInfoPo.setAuditName(loginInfoDto.getUserName());
		goodwillAuditInfoPo.setAuditor(loginInfoDto.getUserId());
		goodwillAuditInfoMapper.insert(goodwillAuditInfoPo);

		// 对对象进行赋值操作
		goodwillApplyInfoPo.setLastGoodwillStatus(goodwillApplyInfoPo.getGoodwillStatus());
		goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_REFUSE_SUPPORT);
		goodwillApplyInfoPo.setUpdatedPerson(loginInfoDto.getUserName());
		// 执行更新
		int row = goodwillApplyInfoMapper.updateById(goodwillApplyInfoPo);

		// add by cl 20200807
		// 商务亲善不予支持通知：当区域或者CCM条线，选择了拒绝支持后，发送实时通知邮件给到经销商和CCMQ。（此拒单功能，开放至VCDC人员）
		Integer mailType = 82461022;// 确认亲善邮件类型
		int sendToFlag = 1; // 1,经销商 2，角色
		// 1，发送给经销商
		this.sendEmail(goodwillApplyInfoPo, sendToFlag, null, mailType);

		// 2，发送给CCMQ
		sendToFlag = 2;
		this.sendEmail(goodwillApplyInfoPo, sendToFlag, "CCMQ", mailType);

		return row;
	}

	/**
	 * 预申请单重启
	 *
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-23
	 */
	@Override
	public int restartApplyGoodwillInfo(Long id, GoodwillAuditInfoDTO goodwillAuditInfoDTO) {
		// 获取登录用户信息
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		// 更新主单的审核金额
		GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoMapper.selectById(id);
		// 根绝审批流程表数据往审批记录表插入数据
		GoodwillAuditInfoPO goodwillAuditInfoPo = new GoodwillAuditInfoPO();
		if (goodwillApplyInfoPo.getLastGoodwillStatus() < CommonConstants.GOODWILL_STATUS_MATERIAL_UPLOAD) {
			goodwillAuditInfoPo.setAuditObject(0);
		} else if (goodwillApplyInfoPo.getLastGoodwillStatus() >= CommonConstants.GOODWILL_STATUS_MATERIAL_UPLOAD) {
			goodwillAuditInfoPo.setAuditObject(1);
		}

		goodwillAuditInfoPo.setGoodwillApplyId(id);
		goodwillAuditInfoPo.setAuditRole("");
		goodwillAuditInfoPo.setAuditOpinion(goodwillAuditInfoDTO.getAuditOpinion());
		goodwillAuditInfoPo.setAuditResult(82801005);
		goodwillAuditInfoPo.setAuditTime(new Date());
		goodwillAuditInfoPo.setAuditName(loginInfoDto.getUserName());
		goodwillAuditInfoPo.setAuditor(loginInfoDto.getUserId());
		goodwillAuditInfoMapper.insert(goodwillAuditInfoPo);

		// 对对象进行赋值操作
		goodwillApplyInfoPo.setGoodwillStatus(goodwillApplyInfoPo.getLastGoodwillStatus());
		goodwillApplyInfoPo.setLastGoodwillStatus(CommonConstants.GOODWILL_STATUS_REFUSE_SUPPORT);
		goodwillApplyInfoPo.setUpdatedPerson(loginInfoDto.getUserName());
		// 执行更新
		int row = goodwillApplyInfoMapper.updateById(goodwillApplyInfoPo);
		return row;
	}

	/**
	 * 分页查询亲善审计数据
	 */
	@Override
	public IPage<GoodwillApplyInfoDTO> querySupportApplyAuditInfo(Page page,
																  GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		if (goodwillApplyInfoDTO == null) {
			goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		}
		// GoodwillApplyInfoPO goodwillApplyInfoPo =
		// goodwillApplyInfoDTO.transDtoToPo(GoodwillApplyInfoPO.class);

		List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.querySupportApplyAuditInfo(page, goodwillApplyInfoDTO);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			List<GoodwillApplyInfoDTO> result = list.stream().map(m -> m.transPoToDto(GoodwillApplyInfoDTO.class))
					.collect(Collectors.toList());

			page.setRecords(result);
			return page;
		}
	}

	/**
	 * 预申请单不需支持
	 *
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-23
	 */
	@Override
	public int noNeedApplyGoodwillInfo(Long id, GoodwillAuditInfoDTO goodwillAuditInfoDTO) {
		// 获取登录用户信息
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		// 更新主单的审核金额
		GoodwillApplyInfoPO goodwillApplyInfoPo = goodwillApplyInfoMapper.selectById(id);
		// 根绝审批流程表数据往审批记录表插入数据
		GoodwillAuditInfoPO goodwillAuditInfoPo = new GoodwillAuditInfoPO();
		if (goodwillApplyInfoPo.getGoodwillStatus() < CommonConstants.GOODWILL_STATUS_MATERIAL_UPLOAD) {
			goodwillAuditInfoPo.setAuditObject(0);
		} else if (goodwillApplyInfoPo.getGoodwillStatus() >= CommonConstants.GOODWILL_STATUS_MATERIAL_UPLOAD) {
			goodwillAuditInfoPo.setAuditObject(1);
		}

		goodwillAuditInfoPo.setGoodwillApplyId(id);
		goodwillAuditInfoPo.setAuditRole("");
		goodwillAuditInfoPo.setAuditOpinion(goodwillAuditInfoDTO.getAuditOpinion());
		goodwillAuditInfoPo.setAuditResult(82801003);
		goodwillAuditInfoPo.setAuditTime(new Date());
		goodwillAuditInfoPo.setAuditName(loginInfoDto.getUserName());
		goodwillAuditInfoPo.setAuditor(loginInfoDto.getUserId());
		goodwillAuditInfoMapper.insert(goodwillAuditInfoPo);

		// 对对象进行赋值操作
		goodwillApplyInfoPo.setLastGoodwillStatus(goodwillApplyInfoPo.getGoodwillStatus());
		goodwillApplyInfoPo.setGoodwillStatus(CommonConstants.GOODWILL_STATUS_NO_NEED_SUPPORT);
		goodwillApplyInfoPo.setUpdatedPerson(loginInfoDto.getUserName());
		// 执行更新
		int row = goodwillApplyInfoMapper.updateById(goodwillApplyInfoPo);

		// add by cl 20200807
		// 商务亲善不需要支持通知：当经销商选择“不需要支持”商务亲善申请后，发送实时通知邮件给到CCMQ。（此不予支持功能，开放至经销商）
		Integer mailType = 82461024;// 确认亲善邮件类型
		// 2，发送给CCMQ
		int sendToFlag = 2;
		this.sendEmail(goodwillApplyInfoPo, sendToFlag, "CCMQ", mailType);

		return row;
	}

	@Override
	public IPage<List> queryNoticeInvoiceInfo(Page page, Long goodwillApplyId) {
		List<Map> result = goodwillApplyInfoMapper.queryNoticeInvoiceInfo(page, goodwillApplyId);
		Integer count = goodwillApplyInfoMapper.queryNoticeInvoiceCount(goodwillApplyId);
		page.setRecords(result);
		page.setTotal(count);
		return page;
	}

	/**
	 * 查询打印AWA信息
	 *
	 * @param noticeInvoiceId
	 * @return Map
	 * <AUTHOR>
	 * @since 2020/7/13
	 */
	@Override
	public Map queryPrintInfo(Long noticeInvoiceId) {
		logger.info("queryPrintInfo,查询打印AWA信息,noticeInvoiceId:{}", noticeInvoiceId);
		Map map = goodwillApplyInfoMapper.queryPrintInfo(noticeInvoiceId);
		if (CommonUtils.isNullOrEmpty(map)) {
			return new HashMap(16);
		}
		logger.info("queryPrintInfo,查询打印AWA信息,map:{}", map);
		//判断是否存在区域经理
		Object auditName3 = map.get("auditName3");
		if (Objects.nonNull(auditName3)) {
			return map;
		}
		//查询CCMZJ角色
		Map mapCcm = goodwillApplyInfoMapper.queryPrintInfoByCcm(noticeInvoiceId);
		logger.info("queryPrintInfo,查询打印AWA信息,mapCcm:{}", mapCcm);
		if(Objects.isNull(mapCcm)){
			return map;
		}
		Object auditName = mapCcm.get("auditName");
		if(Objects.nonNull(auditName)){
			logger.info("queryPrintInfo,替换签名开始");
			map.put("auditName3", auditName);
			map.put("auditTime3", mapCcm.get("auditTime"));
		}
		return map;
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillApplyInfoDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillApplyInfoDTO> exportSupportApplyOemSearchInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		if (goodwillApplyInfoDTO == null) {
			goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		}
		List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.exportSupportApplyOemSearchInfo(goodwillApplyInfoDTO);

		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		}
		else {
			Map<Long,BigDecimal> map = new HashMap<>();
			StringBuffer buf = new StringBuffer(0);
			String couponId = "";
			List<String> couponIds = new ArrayList<>();
			for (GoodwillApplyInfoPO dto : list) {
				if (!StringUtils.isNullOrEmpty(dto.getCouponId())) {
					buf.append(dto.getCouponId() + ",");
					couponIds.add(String.valueOf(dto.getCouponId()));
					map.put(dto.getCouponId(),dto.getCostRate());
					//TODO:sd
				}
			}

			queryAuditInfo(goodwillApplyInfoDTO, list);


			if (buf.length() > 0) {
				couponId = buf.toString().substring(0, buf.toString().lastIndexOf(","));
			}
			logger.info("couponId:{}",couponId);
			if (!StringUtils.isNullOrEmpty(couponId)) {
//				List<Map> list1 =  reportCommonClient.couponVerify(couponId);
				logger.info("reportCommonClient.couponVerifyPost size is {}",couponIds.size());
				List<Map> list1 =  reportCommonClient.couponVerifyPost(couponIds);
				logger.info("reportCommonClient.couponVerify({})={}",couponId,JSONObject.toJSON(list1));
				Map<String,List<String>> mapMap = new HashMap<>();
				if(!CommonUtils.isNullOrEmpty(list1)){
					this.getCouponIdMap(list1,mapMap);
				}
				//List<Map> interList = this.getLeftValue(StrUtil.splitToLong(couponId, ","));
				//Map map1 = new HashMap<>(16);
				for (GoodwillApplyInfoPO dtos : list) {
					if (!StringUtils.isNullOrEmpty(dtos.getCouponId())) {
						//if (!CommonUtils.isNullOrEmpty(interList)) {
						//	for (Object object : interList) {
						//	JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
						//	map1 = jsonObject.toJavaObject(Map.class);
						//	if (map1.get("couponId").toString().equals(dtos.getCouponId().toString())) {
						//		if (!StringUtils.isNullOrEmpty(map1.get("leftValue"))) {
//										dtos.setLeftAmount(new BigDecimal(map1.get("leftValue").toString())
//												.multiply(new BigDecimal(0.01)));
//										dtos.setUsedAmount(dtos.getRechargeAmount()
//												.subtract(new BigDecimal(map1.get("leftValue").toString())
//														.divide(new BigDecimal(100))));
						//			dtos.setLeftAmount(new BigDecimal(map1.get("leftValue").toString())
						//					.multiply(new BigDecimal(0.01)).multiply(map.get(dtos.getCouponId())));
						//		}
						if (!StringUtils.isNullOrEmpty(mapMap.get(dtos.getCouponId().toString()))) {
							List<String> objectList =  mapMap.get(dtos.getCouponId().toString());
							BigDecimal bi = new BigDecimal(0);
							for (String o:objectList) {
								if (dtos.getCostConsumeAmount() == null) {
									dtos.setCostConsumeAmount(new BigDecimal(0).add(new BigDecimal(o).divide(new BigDecimal(100)).multiply(map.get(dtos.getCouponId()))));
									dtos.setUsedAmount(new BigDecimal(0).add(new BigDecimal(o).divide(new BigDecimal(100))));
								} else {
									dtos.setCostConsumeAmount(dtos.getCostConsumeAmount().add(new BigDecimal(o).divide(new BigDecimal(100)).multiply(map.get(dtos.getCouponId()))));
									dtos.setUsedAmount(dtos.getUsedAmount().add(new BigDecimal(o).divide(new BigDecimal(100))));
								}
							}

						}
						if(Objects.nonNull(dtos.getRechargeAmount())){
							dtos.setLeftAmount(dtos.getRechargeAmount().subtract(dtos.getCostConsumeAmount()==null ?new BigDecimal(0):dtos.getCostConsumeAmount()));
							int  max = 	dtos.getLeftAmount().compareTo(new BigDecimal(0));
							if(max <0){
								dtos.setLeftAmount(new BigDecimal(0));
							}
						}
						//dtos.setUsedAmount(dtos.getRechargeAmount().subtract(dtos.getLeftAmount()==null ? new BigDecimal(0):dtos.getLeftAmount()));
						//	}
						//}
						//}

					}
				}

			}


          // 请求中台接口，补全区域经理信息,放在缓存中 1天
			HashMap<Long,String>  areaManageInfo = RedisUtil.getOrSupply(redisTemplate, "AREA_MANAGE_INFO", this::getAreaManageInfo, new com.alibaba.fastjson.TypeReference<HashMap<Long,String>>() {
			}, TimeUnit.DAYS.toSeconds(1));
			logger.info("areaManageInfo:{}",areaManageInfo);
			list.stream().forEach(e->{
				if(StringUtils.isNullOrEmpty(e.getAuditName())){
					e.setAuditName(areaManageInfo.getOrDefault(Long.valueOf(e.getSmallAreaId().toString()),""));
				}
			});


			return list.stream().map(m -> m.transPoToDto(GoodwillApplyInfoDTO.class)).collect(Collectors.toList());
		}
	}

	private void queryAuditInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO, List<GoodwillApplyInfoPO> list) {

		try {
			List<GoodwillApplyInfoPO> listAudit = goodwillApplyInfoMapper.exportSupportApplyAuditListInfo(goodwillApplyInfoDTO);

			if (CollectionUtils.isEmpty(listAudit)){
				return;
			}
			logger.info("处理亲善申请查询导出审批角色,亲善案件条数：{},审批角色条数：{}",list.size(),listAudit.size());
			// 将 goodwillApplyInfoPOS 转换为 Map，以 ID 作为键
			Map<Long, String> idToAuditRoleMap = listAudit.stream()
					.collect(Collectors.toMap(GoodwillApplyInfoPO::getId, GoodwillApplyInfoPO::getAuditRole));

			// 更新 list 中的元素的 auditRole
			list.forEach(po -> po.setAuditRole(idToAuditRoleMap.get(po.getId())));
		} catch (Exception e) {
			logger.error("处理亲善申请查询导出审批角色失败：e",e);
		}

	}

	private HashMap<Long, String> getAreaManageInfo() {
		HashMap<Long,String> areaManageInfo=new HashMap<>();
		QueryUserByOrgTypeDTO queryUserByOrgTypeDTO=new QueryUserByOrgTypeDTO();
		queryUserByOrgTypeDTO.setOrgType(CommonConstants.SHQYJL_ORGTYPE);
		queryUserByOrgTypeDTO.setRoleCode(CommonConstants.SHQYJL_ROLECODE );
		RequestDTO<QueryUserByOrgTypeDTO>  queryUserByOrgTypeRequestDTO= new RequestDTO<>();
		queryUserByOrgTypeRequestDTO.setData(queryUserByOrgTypeDTO);
		ResponseDTO<List<UserOrgInfoDTO>> listResponseDTO = midEndAuthCenterClient.queryUserByOrgType(queryUserByOrgTypeRequestDTO);
		List<UserOrgInfoDTO> userOrgInfoDTO = listResponseDTO.getData();
		if(!CollectionUtils.isEmpty(userOrgInfoDTO)){
			userOrgInfoDTO.stream().forEach(e-> areaManageInfo.put(e.getOrgid(),e.getUsername()));
		}
		return areaManageInfo;
	}

	/**
	 * 转换数据
	 * @param list1
	 * @param mapMap
	 */
	private void getCouponIdMap(List<Map> list1, Map<String, List<String>> mapMap) {
		if(!CommonUtils.isNullOrEmpty(list1)){
			for (Object object :list1) {
				JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
				Map map1 = jsonObject.toJavaObject(Map.class);
				if(ObjectUtil.isNotEmpty(mapMap.get(map1.get("coupon_id").toString())) ){
					List<String> list = 	mapMap.get(map1.get("coupon_id").toString());
					list.add(map1.get("verify_amount")==null ? "0": map1.get("verify_amount").toString());
					mapMap.put(map1.get("coupon_id").toString(),list);
				}else{
					List<String> list = 	new ArrayList<String>();
					list.add(map1.get("verify_amount")==null ? "0": map1.get("verify_amount").toString());
					mapMap.put(map1.get("coupon_id").toString(),list);
				}
			}
		}
	}

	/**
	 * 卡券核销接口
	 *
	 * @param
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	public List<Map> getConsume(String[] ids) {
		Map dtos = new HashMap(16);
		dtos.put("kindness", 1);
		dtos.put("verifyIds", ids);
		RequestDTO<Map> requestDto = new RequestDTO<Map>();
		requestDto.setData(dtos);
		String requestUrls = midUrlProperties.getMidEndCouponCenter() + midUrlProperties.getTtCouponVerifyAll();
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<RequestDTO> httpEntity = new HttpEntity<RequestDTO>(requestDto, httpHeaders);
		List<Map> interList = new ArrayList<>();
		// 调用接口
		try {
			logger.info("中台卡券核销查询入参：{}", JSON.toJSONString(httpEntity));
			ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrls, HttpMethod.POST, httpEntity,
					ResponseDTO.class);
			logger.info("中台卡券核销查询结果：{}", JSON.toJSONString(responseEntity));
			if (responseEntity.getBody() != null) {
				if (SUCCESS_CODE.equals(responseEntity.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					interList = objectMapper.convertValue(responseEntity.getBody().getData(), List.class);
				} else {
					logger.error("中台卡券核销查询报错：{}", responseEntity.getBody().getReturnMessage());
					throw new ServiceBizException("中台查询核销卡券接口失败");
				}
			} else {
				logger.error("中台核销卡券查询报错：{}", responseEntity.getBody().getReturnMessage());
				throw new ServiceBizException("中台查询核销卡券接口失败");
			}
		} catch (Exception e) {
			logger.error("中台核销卡券查询报错：{}", e.getMessage());
			throw new ServiceBizException("中台查询核销卡券接口失败");
		}
		return interList;
	}
	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillApplyInfoDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillApplyInfoDTO> exportSupportApplyDealerSearchInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		if (goodwillApplyInfoDTO == null) {
			goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		}
		List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper
				.exportSupportApplyDealerSearchInfo(goodwillApplyInfoDTO);

		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			Map<Long,BigDecimal> map = new HashMap<>();
			StringBuffer buf = new StringBuffer(0);
			String couponId = "";
			List<String> couponIds = new ArrayList<>();
			for (GoodwillApplyInfoPO dto : list) {
				if (!StringUtils.isNullOrEmpty(dto.getCouponId())) {
					buf.append(dto.getCouponId() + ",");
					couponIds.add(String.valueOf(dto.getCouponId()));
					map.put(dto.getCouponId(),dto.getCostRate());
					//TODO:sd
				}
			}
			if (buf.length() > 0) {
				couponId = buf.toString().substring(0, buf.toString().lastIndexOf(","));
			}
			if (!StringUtils.isNullOrEmpty(couponId)) {
//				List<Map> list1 =  reportCommonClient.couponVerify(couponId);
				logger.info("reportCommonClient.couponVerifyPost size is {}",couponIds.size());
				List<Map> list1 =  reportCommonClient.couponVerifyPost(couponIds);
				logger.info("reportCommonClient.couponVerify({})={}",couponId,JSONObject.toJSON(list1));
				Map<String,List<String>> mapMap = new HashMap<>();
				if(!CommonUtils.isNullOrEmpty(list1)){
					this.getCouponIdMap(list1,mapMap);
				}
				//List<Map> interList = this.getLeftValue(StrUtil.splitToLong(couponId, ","));
				//Map map1 = new HashMap<>(16);
				for (GoodwillApplyInfoPO dtos : list) {
					if (!StringUtils.isNullOrEmpty(dtos.getCouponId())) {
						//if (!CommonUtils.isNullOrEmpty(interList)) {
						//	for (Object object : interList) {
						//	JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
						//	map1 = jsonObject.toJavaObject(Map.class);
						//	if (map1.get("couponId").toString().equals(dtos.getCouponId().toString())) {
						//		if (!StringUtils.isNullOrEmpty(map1.get("leftValue"))) {
//										dtos.setLeftAmount(new BigDecimal(map1.get("leftValue").toString())
//												.multiply(new BigDecimal(0.01)));
//										dtos.setUsedAmount(dtos.getRechargeAmount()
//												.subtract(new BigDecimal(map1.get("leftValue").toString())
//														.divide(new BigDecimal(100))));
						//			dtos.setLeftAmount(new BigDecimal(map1.get("leftValue").toString())
						//					.multiply(new BigDecimal(0.01)).multiply(map.get(dtos.getCouponId())));
						//		}
						if (!StringUtils.isNullOrEmpty(mapMap.get(dtos.getCouponId().toString()))) {
							List<String> objectList =  mapMap.get(dtos.getCouponId().toString());
							BigDecimal bi = new BigDecimal(0);
							for (String o:objectList) {
								if (dtos.getCostConsumeAmount() == null) {
									dtos.setCostConsumeAmount(new BigDecimal(0).add(new BigDecimal(o).divide(new BigDecimal(100)).multiply(map.get(dtos.getCouponId()))));
									dtos.setUsedAmount(new BigDecimal(0).add(new BigDecimal(o).divide(new BigDecimal(100))));
								} else {
									dtos.setCostConsumeAmount(dtos.getCostConsumeAmount().add(new BigDecimal(o).divide(new BigDecimal(100)).multiply(map.get(dtos.getCouponId()))));
									dtos.setUsedAmount(dtos.getUsedAmount().add(new BigDecimal(o).divide(new BigDecimal(100))));
								}
							}

						}
//						dtos.setLeftAmount(dtos.getRechargeAmount().subtract(dtos.getCostConsumeAmount()==null ?new BigDecimal(0):dtos.getCostConsumeAmount()));
//						int  max = 	dtos.getLeftAmount().compareTo(new BigDecimal(0));
//						if(max <0){
//							dtos.setLeftAmount(new BigDecimal(0));
//						}
						if(Objects.nonNull(dtos.getRechargeAmount())){
							dtos.setLeftAmount(dtos.getRechargeAmount().subtract(dtos.getCostConsumeAmount()==null ?new BigDecimal(0):dtos.getCostConsumeAmount()));
							int  max = 	dtos.getLeftAmount().compareTo(new BigDecimal(0));
							if(max <0){
								dtos.setLeftAmount(new BigDecimal(0));
							}
						}
						//dtos.setUsedAmount(dtos.getRechargeAmount().subtract(dtos.getLeftAmount()==null ? new BigDecimal(0):dtos.getLeftAmount()));
						//	}
						//}
						//}

					}
				}

			}
			return list.stream().map(m -> m.transPoToDto(GoodwillApplyInfoDTO.class)).collect(Collectors.toList());
		}
	}
	@Value("${volvo.email.goodwillAppId}")
	private String goodwillAppId;
	/**
	 * 发送邮件
	 *
	 * @param goodwillApplyInfoPo
	 *            亲善预申请单
	 *            审批记录表
	 * @param mailType
	 *            亲善邮件类型
	 */
	@Override
	public void sendEmail(GoodwillApplyInfoPO goodwillApplyInfoPo, int sendToFlag, String auditRole, Integer mailType) {
		goodwillApplyInfoServiceHelper.sendEmail(goodwillApplyInfoPo,sendToFlag,auditRole,mailType);
	}

	@Override
	public void sendEmailAsync(GoodwillApplyInfoPO goodwillApplyInfoPo, int sendToFlag, String auditRole, Integer mailType) {
		logger.info("goodwillAppId:{}",goodwillAppId);
		// 邮件发送记录
		GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO = new GoodwillApplyMailHistoryDTO();
		goodwillApplyMailHistoryDTO.setGoodwillApplyId(goodwillApplyInfoPo.getId());
		goodwillApplyMailHistoryDTO.setDealerCode(goodwillApplyInfoPo.getDealerCode());
		goodwillApplyMailHistoryDTO.setMailType(mailType);

		// 1,查询发送内容模板，判断是否需要发送邮件
		GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPo = goodwillMailTemplateMaintainMapper
				.selectTemplateMaintain(mailType);
		if (!StringUtils.isNullOrEmpty(goodwillMailTemplateMaintainPo)) {
			// 2，准备发送邮箱
			String fromEmail = CommonConstants.VOLVO_MAIL;

			// 3，准备收件人邮箱
			String[] list = null;
			if (sendToFlag == 1) {
				// 经销商
				list = getDealerEmailList(goodwillApplyInfoPo);
			} else if (sendToFlag == 2) {
				// 根据角色查询人员
				list = getEmailListByRole(goodwillApplyInfoPo, auditRole);
			}

			if (list != null && list.length > 0) {
				// 4，组装数据调用中台发送邮件
				EmailInfoDto emailInfoDto = new EmailInfoDto();

				emailInfoDto.setFrom(fromEmail);
				emailInfoDto.setTo(list);
				// EamilTemplate EamilTemplate = new EamilTemplate();
				// 解析邮件标题、内容
				String maileContent = parseMailTitleAndContent(goodwillApplyInfoPo,
						goodwillMailTemplateMaintainPo.getMailContent(), mailType);
				String mailTitle = parseMailTitleAndContent(goodwillApplyInfoPo,
						goodwillMailTemplateMaintainPo.getMailTitle(), mailType);
				emailInfoDto.setText(maileContent);
				emailInfoDto.setSubject(mailTitle);
				logger.info("发送邮件参数:{}",emailInfoDto);
				try {
					String status = commonService.sendGoodwillMail(emailInfoDto);
					// if (status.equals('0')) {
					if ("0".equals(status)) {
						logger.info("发送邮件成功！");
						// noticeSendEmailRecordDTO.setSendStatus(CommonConstants.IS_SENDMAIL_SUCCESS);
						goodwillApplyMailHistoryDTO.setSendStatus(82771001);
					} else {
						goodwillApplyMailHistoryDTO.setSendStatus(82771002);
						logger.info("发送邮件失败！");
					}

				} catch (Exception e) {
					logger.info(e.getMessage());
				} finally {
					// return noticeSendEmailRecordService.insert( noticeSendEmailRecordDTO);
				}
				goodwillApplyMailHistoryDTO.setReceiverMail(StringUtils.join(Arrays.asList(list), ","));
				goodwillApplyMailHistoryDTO.setSendBy(CommonConstants.VOLVO_MAIL);
				goodwillApplyMailHistoryDTO.setTitle(emailInfoDto.getSubject());
				goodwillApplyMailHistoryDTO.setContent(emailInfoDto.getText());
				goodwillApplyMailHistoryDTO.setSendTime(new Date());
				goodwillApplyMailHistoryService.insert(goodwillApplyMailHistoryDTO);
			}
		}

	}

	/**
	 * 解析 邮件标题 内容
	 *
	 * @return
	 */
	private String parseMailTitleAndContent(GoodwillApplyInfoPO goodwillApplyInfoPo, String mail, Integer mailType) {
		String parseResult = null;
		if (StringUtils.isNullOrEmpty(mail)) {
			return parseResult;
		}

		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		// 1.准备数据
		// 【商务亲善申请ID】
		String applyNo = goodwillApplyInfoPo.getApplyNo();
		// 【经销商代码】
		String dealerCode = goodwillApplyInfoPo.getDealerCode();
		// 【经销商名称】
		String dealerName = goodwillApplyInfoPo.getDealerName();
		// 【车架号】
		String vin = goodwillApplyInfoPo.getVin();
		// 【申请金额】
		BigDecimal applyAmount = goodwillApplyInfoPo.getApplyAmount();
		// 【申请时间】
		String applyTimeStr = StringUtils.isNullOrEmpty(goodwillApplyInfoPo.getApplyTime()) ? ""
				: format.format(goodwillApplyInfoPo.getApplyTime());

		// 【开票ID】
		String invocieId = "";
		// 【代金券充值金额】
		String voucherRechargePrice = "";
		// 【开票抬头】
		String invocieTitle = "";
		// 【通知开票金额】
		if (mailType.intValue() == 82461018 || mailType.intValue() == 82461019) {
			BigDecimal invocieAmount = goodwillApplyInfoPo.getVoucherInvoiceAmount();
			mail = mail.replaceAll("【通知开票金额】",
					(!StringUtils.isNullOrEmpty(invocieAmount)) ? (invocieAmount.doubleValue() + "") : "0");

		} else {
			BigDecimal invocieAmount = goodwillApplyInfoPo.getInvoiceAmount();
			mail = mail.replaceAll("【通知开票金额】",
					(!StringUtils.isNullOrEmpty(invocieAmount)) ? (invocieAmount.doubleValue() + "") : "0");

		}
		// 查询第一次开票记录
		GoodwillNoticeInvoiceInfoPO GoodwillNoticeInvoiceInfoPo = goodwillNoticeInvoiceInfoMapper
				.queryVoucherByGoodwillApplyId(goodwillApplyInfoPo.getId());

		if (!StringUtils.isNullOrEmpty(GoodwillNoticeInvoiceInfoPo) && GoodwillNoticeInvoiceInfoPo != null) {
			mail = mail.replaceAll("【代金券充值金额】",
					(!StringUtils.isNullOrEmpty(GoodwillNoticeInvoiceInfoPo.getVoucherCouponFaceRechargePrice()))
							? GoodwillNoticeInvoiceInfoPo.getVoucherCouponFaceRechargePrice()+""
							: "0");

			mail = mail.replaceAll("【代金券结算成本金额】",
					(!StringUtils.isNullOrEmpty(GoodwillNoticeInvoiceInfoPo.getVoucherRechargePrice()))
							? GoodwillNoticeInvoiceInfoPo.getVoucherRechargePrice()+""
							: "0");

		}
		// 查询最近日期开票记录
		List<GoodwillNoticeInvoiceInfoPO> goodwillNoticeInvoiceInfoPOList = goodwillNoticeInvoiceInfoMapper
				.queryLastNoticeInoviceInfo(goodwillApplyInfoPo.getId());
		if (!CommonUtils.isNullOrEmpty(goodwillNoticeInvoiceInfoPOList)) {
			GoodwillNoticeInvoiceInfoPO po = goodwillNoticeInvoiceInfoPOList.get(0);
			invocieId = po.getInvoiceId();
			invocieTitle = po.getName();

		}

		String consumeContent = "";
		// 【亲善券消费明细】 --- 邮件类型“17商务亲善代金券/积分开票通知”，在发送邮件的变量参数里，需要增加消费明细
		if (mailType.intValue() == 82461018 || mailType.intValue() == 82461019) {
			// TODO 中台接口（前台传过来消费记录（包括合并开票））
			// 亲善ID VIN 经销商 消费时间 消费金额 开票 ID 开票抬头 通知开票金额
			GoodwillStampsDTO goodwillStampsDTO = new GoodwillStampsDTO();
			goodwillStampsDTO.setInvoiceId(invocieId);
			try {
				// List<GoodwillStampsDTO> list = goodwillStampsService.exportconGoodwillStamps(goodwillStampsDTO);
				List<GoodwillStampsDTO> list = goodwillStampsService.downloadConGoodwillStamps(null, goodwillStampsDTO);
				if (!CommonUtils.isNullOrEmpty(list)) {
					consumeContent = this.getConsume(list);
				}
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}

		//

		// 2.解析预留字段
		if (!StringUtils.isNullOrEmpty(applyNo)) {
			mail = mail.replaceAll("【商务亲善申请ID】", applyNo);
		} else {
			mail = mail.replaceAll("【商务亲善申请ID】", "");
		}
		if (!StringUtils.isNullOrEmpty(dealerName)) {
			mail = mail.replaceAll("【经销商名称】", dealerName);
		} else {
			mail = mail.replaceAll("【经销商名称】", "");
		}
		if (!StringUtils.isNullOrEmpty(dealerCode)) {
			mail = mail.replaceAll("【经销商代码】", dealerCode);
		} else {
			mail = mail.replaceAll("【经销商代码】", "");
		}
		if (!StringUtils.isNullOrEmpty(vin)) {
			mail = mail.replaceAll("【车架号】", vin);
		} else {
			mail = mail.replaceAll("【车架号】", "");
		}
		if (!StringUtils.isNullOrEmpty(applyAmount)) {
			mail = mail.replaceAll("【申请金额】", applyAmount + "");
		} else {
			mail = mail.replaceAll("【申请金额】", "");
		}
		if (!StringUtils.isNullOrEmpty(applyTimeStr)) {
			mail = mail.replaceAll("【申请时间】", applyTimeStr);
		} else {
			mail = mail.replaceAll("【申请时间】", "");
		}
		if (!StringUtils.isNullOrEmpty(invocieId)) {
			mail = mail.replaceAll("【开票ID】", invocieId);
		} else {
			mail = mail.replaceAll("【开票ID】", "");
		}
		if (!StringUtils.isNullOrEmpty(invocieTitle)) {
			mail = mail.replaceAll("【开票抬头】", invocieTitle);
		} else {
			mail = mail.replaceAll("【开票抬头】", "");
		}
		if (!StringUtils.isNullOrEmpty(consumeContent)) {
			mail = mail.replaceAll("【亲善券消费明细】", consumeContent);
		} else {
			mail = mail.replaceAll("【亲善券消费明细】", "");
		}

		parseResult = mail;
		return parseResult;
	}

	/**
	 * 查询消费明细
	 *
	 * @return
	 */
	public String getConsume(List<GoodwillStampsDTO> list) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String table = "";
		for (GoodwillStampsDTO dto : list) {
			String invoiceId = "";
			String invoiceTitle = "";
			String noticeInvoicePrice = "";
			if (!StringUtils.isNullOrEmpty(dto.getInvoiceId())) {
				invoiceId = dto.getInvoiceId();
			}
			if (!StringUtils.isNullOrEmpty(dto.getInvoiceTitle())) {
				invoiceTitle = dto.getInvoiceTitle();
			}
			if (!StringUtils.isNullOrEmpty(dto.getNoticeInvoicePrice())) {
				noticeInvoicePrice = dto.getNoticeInvoicePrice().toString();
			}
			String tables = "<tr>" + "<td width='15%'><div align='center'>" + dto.getApplyNo() + "</div></td>"
					+ "    <td width='15%'><div align='center'>" + sdf.format(dto.getConsumeDate()) + "</div></td>"
					+ "    <td width='8%'><div align='center'>" + dto.getConsumeAmount() + "</div></td>"
					+ "    <td width='16%'><div align='center'>" + invoiceId + "</div></td>" + "  </tr>";
			table = table + tables;
		}

		String content = "<table style='width:60%;'><tbody>" + "<tr>"
				+ "    <td width='15%'><div align='center'>亲善单ID</div></td>"
				+ "    <td width='15%'><div align='center'>消费时间</div></td>"
				+ "    <td width='8%'><div align='center'>消费金额</div></td>"
				+ "    <td width='16%'><div align='center'>开票ID</div></td>" + "</tr>" + table + "<tbody></table>";

		return content;
	}

	/**
	 * 查询角色对应的人员 邮箱
	 *
	 * @param goodwillApplyInfoPo
	 * @param auditRole
	 * @return
	 */
	private String[] getEmailListByRole(GoodwillApplyInfoPO goodwillApplyInfoPo, String auditRole) {
		String[] list = null;
		// 1，获取经销商 code
		String dealerCode = goodwillApplyInfoPo.getDealerCode();
		// 2,根据经销商code 查询 经销商详情的【售后大区ID】 【售后小区ID】
		if ("SHQYJL".equals(auditRole)) {// 售后区域经理
			Map map = getDealerInfo(dealerCode);
			if (!StringUtils.isNullOrEmpty(map.get("afterSmallAreaId"))) { // afterBigAreaId
				// 准备查询参数
				RequestDTO<QueryRoleUserDTO> requestDto = new RequestDTO<QueryRoleUserDTO>();
				QueryRoleUserDTO queryRoleUserDto = new QueryRoleUserDTO();
				queryRoleUserDto.setOrgId(Integer.getInteger(map.get("afterSmallAreaId").toString()));
				List<String> roleList = new ArrayList<String>();
				roleList.add(auditRole);
				queryRoleUserDto.setRoleCode(roleList);
				queryRoleUserDto.setOrgId(Integer.parseInt(map.get("afterSmallAreaId").toString()));
				requestDto.setData(queryRoleUserDto);
				logger.info("根据orgId及角色查询用户={}", JSON.toJSONString(requestDto));
				list = queryEmailFromAuthCenter(requestDto);
			} else {
				throw new DALException("请维护售后小区");
			}
		} else if ("SHDQJL".equals(auditRole) || "SHQYZJ".equals(auditRole)) { // 售后区域高级经理/区域总监
			// 查询 售后大区orgId
			Map map = getDealerInfo(dealerCode);
			if (!StringUtils.isNullOrEmpty(map.get("afterBigAreaId"))) { // afterBigAreaId
				// 准备查询参数
				RequestDTO<QueryRoleUserDTO> requestDto = new RequestDTO<QueryRoleUserDTO>();
				QueryRoleUserDTO queryRoleUserDto = new QueryRoleUserDTO();
				queryRoleUserDto.setOrgId(Integer.getInteger(map.get("afterBigAreaId").toString()));
				List<String> roleList = new ArrayList<String>();
				roleList.add(auditRole);
				queryRoleUserDto.setRoleCode(roleList);
				queryRoleUserDto.setOrgId(Integer.parseInt(map.get("afterBigAreaId").toString()));
				requestDto.setData(queryRoleUserDto);
				logger.info("根据orgId及角色查询用户={}", JSON.toJSONString(requestDto));
				list = queryEmailFromAuthCenter(requestDto);
			} else {
				throw new DALException("请维护售后大区");
			}
		} else { // VP /CEO /CCMGJJL/CCMZJ/OEM-CWJL/CFO /CCMZJ /CCMQ
			// 准备查询参数
			RequestDTO<QueryRoleUserDTO> requestDto = new RequestDTO<QueryRoleUserDTO>();
			QueryRoleUserDTO queryRoleUserDto = new QueryRoleUserDTO();
			List<String> roleList = new ArrayList<String>();
			roleList.add(auditRole);
			queryRoleUserDto.setRoleCode(roleList);
			requestDto.setData(queryRoleUserDto);
			logger.info("根据角色查询用户={}", JSON.toJSONString(requestDto));
			list = queryEmailFromAuthCenter(requestDto);
		}
		return list;
	}

	/**
	 * 根据 orgID,roleCode 查询 区域经理/售后区域高级经理/区域总监/ CCM高级经理/CCM总监/CCMQ/财务经理/VP/CFO/CEO
	 *
	 * @param requestDto
	 * @return
	 */
	private String[] queryEmailFromAuthCenter(RequestDTO<QueryRoleUserDTO> requestDto) {
		List<String> eMailList = new ArrayList<String>();
		String successCodes = "0";
		String requestUrls = midUrlProperties.getMidEndAuthCenter() + midUrlProperties.getRoleOrgIdUser();
		HttpHeaders httpHeaderss = new HttpHeaders();
		httpHeaderss.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<RequestDTO> httpEntitys = new HttpEntity<RequestDTO>(requestDto, httpHeaderss);
		String defaultEmail = CommonConstants.VOLVO_MAIL; // 当前登录人没有维护email时，给默认邮箱
		try {
			ResponseEntity<ResponseDTO> responseEntitys = directRestTemplate.exchange(requestUrls, HttpMethod.POST,
					httpEntitys, ResponseDTO.class);
			logger.info("queryEmailFromAuthCenter查询结果：{}",JSONObject.toJSON(responseEntitys));
			if (responseEntitys.getBody() != null) {
				if (successCodes.equals(responseEntitys.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					// 忽略差异字段
					objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
					List<UserInfoOutDTO> userInfoOutDtoList = objectMapper.convertValue(
							responseEntitys.getBody().getData(), new TypeReference<List<UserInfoOutDTO>>() {
							});
					if (!CommonUtils.isNullOrEmpty(userInfoOutDtoList)) {
						eMailList = userInfoOutDtoList.stream().filter(item -> {
							return (!StringUtils.isNullOrEmpty(item.getEmail()));
						}).collect(Collectors.toList()) // 过滤邮箱为空的用户数据
								.stream().map(UserInfoOutDTO::getEmail).collect(Collectors.toList());// 再取出邮箱 组合成list
						if (!CommonUtils.isNullOrEmpty(eMailList)) {
							// 不为空
							return eMailList.toArray(new String[eMailList.size()]);
						} else {
							// TODO 这里为了测试默认用自己邮箱，后面注释掉，抛出异常提醒客户维护邮箱
							eMailList.add(defaultEmail);
							return eMailList.toArray(new String[eMailList.size()]);
							// throw new DALException("请维护用户【" + userInfoOutDtoList.get(0).getUsername() +
							// "】的邮箱");
						}

					} else {
						throw new DALException("没有查到响应的角色【" + requestDto.getData().getRoleCode().get(0) + "】的用户");
					}

				} else {
					throw new DALException("员工信息查询接口异常，请稍后再试");
				}
			}
		} catch (Exception e) {
			logger.info("queryEmailFromAuthCenter查询结果：{}",e.getMessage());
			throw new DALException("员工信息查询接口异常，请稍后再试");
		}
		return null;
	}

	/**
	 * 获取经销商的售后大区ID，售后小区ID，区域总监 的 email
	 *
	 * @param dealerCode
	 * @return
	 */
	private Map getDealerInfo(String dealerCode) {
		String successCodes = "0";
		String requestUrls = midUrlProperties.getMidEndOrgCenter() + midUrlProperties.getSelectCompanyByCompanyCode() + dealerCode;
		HttpHeaders httpHeaderss = new HttpHeaders();
		httpHeaderss.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Map> httpEntitys = new HttpEntity<>(httpHeaderss);
		Map map = new HashMap<>();
		try {
			ResponseEntity<ResponseDTO> responseEntitys = directRestTemplate.exchange(requestUrls, HttpMethod.POST,
					httpEntitys, ResponseDTO.class);
			if (responseEntitys.getBody() != null) {
				if (successCodes.equals(responseEntitys.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					map = objectMapper.convertValue(responseEntitys.getBody().getData(), Map.class);
					return map;

				} else {
					throw new DALException("经销商信息接口异常，请稍后再试");
				}
			}
		} catch (Exception e) {
			throw new DALException("经销商信息接口异常，请稍后再试");
		}
		return map;
	}

	/**
	 * 查询经销商 邮箱
	 *
	 * @param goodwillApplyInfoPo
	 * @return
	 */
	private String[] getDealerEmailList(GoodwillApplyInfoPO goodwillApplyInfoPo) {
		// 查询经销商邮箱信息
		String dealerCode = goodwillApplyInfoPo.getDealerCode();
		GoodwillDealerMailInfoPO goodwillDealerMailInfoPo = goodwillDealerMailInfoMapper.selectByDealerCode(dealerCode);
		String[] listMail = null;
		String[] list = null;
		String[] list1 = null;
		if (goodwillDealerMailInfoPo != null) {
			if (!StringUtils.isNullOrEmpty(goodwillDealerMailInfoPo.geteMail1())) {
				if (goodwillDealerMailInfoPo.geteMail1().indexOf(",") > 0) {
					list = goodwillDealerMailInfoPo.geteMail1().split(",");
				} else {
					list = new String[1];
					list[0] = goodwillDealerMailInfoPo.geteMail1();
				}
			}
			if (!StringUtils.isNullOrEmpty(goodwillDealerMailInfoPo.geteMail2())) {
				if (goodwillDealerMailInfoPo.geteMail2().indexOf(",") > 0) {
					list1 = goodwillDealerMailInfoPo.geteMail2().split(",");
				} else {
					list1 = new String[1];
					list1[0] = goodwillDealerMailInfoPo.geteMail2();
				}
			}

			listMail = (String[]) ArrayUtils.addAll(list, list1);
		}
		return listMail;
	}

	/**
	 * 查询当前登录人邮箱
	 *
	 * @return
	 */
	private String getFromEmail() {
		// 获取登录用户信息
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();

		String successCodes = "0";
		String requestUrls = midUrlProperties.getMidEndAuthCenter() + midUrlProperties.getEmp()+"?userId=" + loginInfoDto.getUserId();
		HttpHeaders httpHeaderss = new HttpHeaders();
		// Map map1 = new HashMap(16);
		// Long userId = loginInfoDto.getUserId();
		// map1.put("userId", userId);
		httpHeaderss.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Map> httpEntitys = new HttpEntity<>(httpHeaderss);
		String eMail = CommonConstants.VOLVO_MAIL; // TODO 当前登录人没有维护email时，给默认邮箱
		try {
			logger.info("员工信息查询requestUrls：{}",requestUrls);
			ResponseEntity<ResponseDTO> responseEntitys = directRestTemplate.exchange(requestUrls, HttpMethod.GET,
					httpEntitys, ResponseDTO.class);
			logger.info("员工信息查询结果：{}",JSONObject.toJSON(responseEntitys));
			Map map = new HashMap<>();
			if (responseEntitys.getBody() != null) {
				if (successCodes.equals(responseEntitys.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					map = objectMapper.convertValue(responseEntitys.getBody().getData(), Map.class);
					if (!CommonUtils.isNullOrEmpty(map)) {
						if (!StringUtils.isNullOrEmpty(map.get("eMail"))) {
							eMail = map.get("eMail").toString();
						}
					}

				} else {
					throw new DALException("员工信息查询接口异常，请稍后再试");
				}
			}
		} catch (Exception e) {
			logger.info("员工信息查询异常：{}",e.getMessage());
			throw new DALException("员工信息查询接口异常，请稍后再试");
		}
		return eMail;
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillApplyInfoDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillApplyInfoDTO> exportSupportApplyAuditInfo(GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		if (goodwillApplyInfoDTO == null) {
			goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		}
		List<GoodwillApplyInfoPO> list = goodwillApplyInfoMapper.exportSupportApplyAuditInfo(goodwillApplyInfoDTO);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillApplyInfoDTO.class)).collect(Collectors.toList());
		}
	}

	@Override
	public Map getGoodwillFirstPage(GoodwillFirstPageDTO goodwillFirstPageDTO) {
		Map map = goodwillApplyInfoMapper.getGoodwillFirstPage(goodwillFirstPageDTO);
		if (CommonUtils.isNullOrEmpty(map)) {
			return new HashMap(16);
		} else {
			return map;
		}
	}

	/**
	 * 主要用于迁移数据产生亲善审批流程
	 * 根据申请单编号列表产生亲善审核流程
	 * add by czm 20210721
	 * @param applyNoList 亲善预申请单号列表
	 * @return 是否执行完毕
	 */
	@Override
	public AjaxResponse dealGoodwillFlowData(List<String> applyNoList) {
		AjaxResponse ajaxResponse = new AjaxResponse();
		ajaxResponse.setMsg("默认处理成功");
		//定义返回信息
		String responseMsg = "";
		String requestParams = "";
		StopWatch sw = new StopWatch();
		try{
			sw.start();
			requestParams = JSON.toJSONString(applyNoList);
			//1.查询申请单数据
			Map<String, Object> queryParams = new HashMap<>();
			queryParams.put("applyNoList",applyNoList);
			List<GoodwillApplyInfoPO> goodwillPOList = goodwillApplyInfoMapper.getGoodwillApplyInfoDatas(queryParams);
			if(CollectionUtils.isNotEmpty(goodwillPOList)){
				for(GoodwillApplyInfoPO goodwillpo:goodwillPOList){
					//2.获取该申请单配置申请流程
					List<GoodwillAuditProcessDTO> auditList = goodwillAuditProcessService.selectListBydealGoodwillFlowData(null,
							goodwillpo.getAuditType(), goodwillpo.getApplyAmount());
					//3.根据申请单ID删除审批流程
					this.deleteGoodwillFlowByGoodwillId(goodwillpo.getId());

					//4.创建新的审核流程
					this.createGoodwillFlow(goodwillpo,auditList);

					//5.更新审批历史记录数据到审批流的审批时间
					this.updateGoodwillFlowAuditTime(goodwillpo.getId());
				}
			}

			sw.stop();
			ajaxResponse.setSuccess("200","亲善历史数据创建审批流程执行完毕");
			ajaxResponse.addObject("totalTime",sw.getTotalTimeSeconds()+"秒");
		}catch(Throwable e){
			logger.error("call this.dealGoodwillFlowData(applyNoList={}) method error msg:{}",requestParams, e);
			if (e != null) {
				ajaxResponse.setFail("4007","亲善审批流程产生失败："+e.getMessage());
				ajaxResponse.addObject("detailMsg", BeanUtils.getExceptionMsg(e));
			}
		}finally {
			if (sw.isRunning()) {
				sw.stop();
			}
			responseMsg = responseMsg + "@@@亲善历史数据处理后返回结果信息ajaxResponse="+ JSON.toJSONString(ajaxResponse);

			//记录日志
			httpLogService.saveHttpLog("2021亲善历史数据迁移创建审批流程手动任务","/dmscus.customer/goodwillApplyInfo/interf/dealgoodwillflowdata",requestParams,"POST","",responseMsg);
		}
		logger.info("亲善审批流程产生返回结果ajaxResponse={}", ajaxResponse);
		return ajaxResponse;
	}

	/**
	 * @param goodwillApplyId 预申请单ID
	 */
	private void updateGoodwillFlowAuditTime(Long goodwillApplyId) {
		goodwilApplyAuditProcessMapper.updateGoodwillFlowAuditTime(goodwillApplyId);
	}

	/**
	 * @param goodwillpo 亲善申请单数据
	 * @param auditList 亲善基本审核流程数据
	 */
	private void createGoodwillFlow(GoodwillApplyInfoPO goodwillpo, List<GoodwillAuditProcessDTO> auditList) {
		if(goodwillpo != null && CollectionUtils.isNotEmpty(auditList)){
			for (GoodwillAuditProcessDTO goodwillAuditProcessDTO : auditList) {
				// 获取亲善审批角色代码CODE
				String roles = this.queryRole(goodwillAuditProcessDTO.getAuditObject(),
						goodwillAuditProcessDTO.getAuditType(),
						Integer.valueOf(goodwillAuditProcessDTO.getAuditPosition()));
				GoodwilApplyAuditProcessDTO dto = new GoodwilApplyAuditProcessDTO();
				dto.setAuditRole(roles);
				//亲善主单ID
				dto.setGoodwillApplyId(goodwillpo.getId());
				//审核对象（0：亲善预审阶段  1：材料审核阶段）
				dto.setAuditObject(goodwillAuditProcessDTO.getAuditObject());
				//选用申请单中的audit_type 字段说明 0：区域审核  1：CCM审核 (插入数据取的是主单上的audit_type数据值)
				dto.setAuditType(goodwillpo.getAuditType());
				dto.setAuditPosition(goodwillAuditProcessDTO.getAuditPosition());
				dto.setAuditSort(Integer.valueOf(goodwillAuditProcessDTO.getAuditPosition()));
				//审核状态（默认审核通过）
				dto.setAuditStatus(82801001);
				//审核时间暂定为空
				GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo = dto
						.transDtoToPo(GoodwilApplyAuditProcessPO.class);
				//设置固定创建人（便于删除数据）
				goodwilApplyAuditProcessPo.setCreatedBy("hand");
				//亲善性质(82351001：特殊亲善    82351002：CCA自授权) CCA自授权亲善流程只有材料审核流程
				if("82351002".equals(String.valueOf(goodwillpo.getGoodwillNature()))){
					if("1".equals(String.valueOf(goodwilApplyAuditProcessPo.getAuditObject()))){//获取材料审核对象数据
						goodwilApplyAuditProcessMapper.insert(goodwilApplyAuditProcessPo);
					}
				}else{
					goodwilApplyAuditProcessMapper.insert(goodwilApplyAuditProcessPo);
				}
			}
		}
	}

	/**
	 * 根据亲善ID删除亲善审批流程
	 * @param goodwillApplyId 预申请单ID
	 */
	private void deleteGoodwillFlowByGoodwillId(Long goodwillApplyId) {
		Map<String, Object> columnMap = new HashMap(16);
		columnMap.put("goodwill_apply_id", goodwillApplyId);
		goodwilApplyAuditProcessMapper.deleteByMap(columnMap);
	}

	/**
	 * 审批状态修改,审批记录,邮件发送封装接口添加事务
	 */
	@Transactional
	public void auditProcess(GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPo,
							  Long id,
							  GoodwillAuditInfoDTO goodwillAuditInfoDTO,
							  LoginInfoDto loginInfoDto,
							  Integer number,
							  GoodwillApplyInfoPO goodwillApplyInfoPo,boolean isMail) {

		LambdaUpdateWrapper<GoodwilApplyAuditProcessPO> updateWrapper = new UpdateWrapper().lambda();
		updateWrapper.eq(GoodwilApplyAuditProcessPO::getId, goodwilApplyAuditProcessPo.getId());

		goodwilApplyAuditProcessPo.setAuditStatus(82801001);
		goodwilApplyAuditProcessPo.setAuditDate(new Date());

		goodwilApplyAuditProcessMapper.update(goodwilApplyAuditProcessPo, updateWrapper);

		// 根绝审批流程表数据往审批记录表插入数据
		GoodwillAuditInfoPO goodwillAuditInfoPo = new GoodwillAuditInfoPO();
		goodwillAuditInfoPo.setGoodwillApplyId(id);
		goodwillAuditInfoPo.setAuditObject(goodwilApplyAuditProcessPo.getAuditObject());
		goodwillAuditInfoPo.setAuditType(goodwilApplyAuditProcessPo.getAuditType());
		goodwillAuditInfoPo.setAuditRole(goodwilApplyAuditProcessPo.getAuditRole());
		goodwillAuditInfoPo.setAuditOpinion(goodwillAuditInfoDTO.getAuditOpinion());
		goodwillAuditInfoPo.setAuditPrice(goodwillAuditInfoDTO.getAuditPrice());
		goodwillAuditInfoPo.setAuditResult(82801001);
		goodwillAuditInfoPo.setAuditTime(new Date());
		goodwillAuditInfoPo.setAuditName(loginInfoDto.getUserName());
		goodwillAuditInfoPo.setAuditor(loginInfoDto.getUserId());
		goodwillAuditInfoMapper.insert(goodwillAuditInfoPo);

		GoodwilApplyAuditProcessPO pos = new GoodwilApplyAuditProcessPO();
		List<GoodwilApplyAuditProcessPO> poList = goodwilApplyAuditProcessMapper.selectByGoodwillApplyId(id);
		if (poList.size() > 0 && poList != null) {
			pos = poList.get(0);
		}
		// 邮件发送 add by cl 20200807
		Integer mailType = 0;// 确认亲善邮件类型
		int sendToFlag = 1; // 1,经销商 2，角色

		// 判断审批进程并更新主表
		if ((goodwilApplyAuditProcessPo.getApplyCounts() == 1 && goodwilApplyAuditProcessPo.getCounts() > 1)
				|| number == 1) {
			goodwillApplyInfoPo.setGoodwillStatus(82551004);// 材料待上传
			goodwillApplyInfoPo.setPassTime(new Date());
			if (number == 1) {
				goodwilApplyAuditProcessMapper.updateStatusById(id);
				// 根绝审批流程表数据往审批记录表插入数据
				GoodwillAuditInfoPO goodwillAuditInfoPo1 = new GoodwillAuditInfoPO();
				goodwillAuditInfoPo1.setGoodwillApplyId(id);
				goodwillAuditInfoPo1.setAuditObject(goodwilApplyAuditProcessPo.getAuditObject());
				goodwillAuditInfoPo1.setAuditType(goodwilApplyAuditProcessPo.getAuditType());
				goodwillAuditInfoPo1.setAuditRole("CEO");
				goodwillAuditInfoPo1.setAuditOpinion(goodwillAuditInfoDTO.getAuditOpinion());
				goodwillAuditInfoPo1.setAuditPrice(goodwillAuditInfoDTO.getAuditPrice());
				goodwillAuditInfoPo1.setAuditResult(82801001);
				goodwillAuditInfoPo1.setAuditTime(new Date());
				goodwillAuditInfoPo1.setAuditName(loginInfoDto.getUserName());
				goodwillAuditInfoPo1.setAuditor(loginInfoDto.getUserId());
				goodwillAuditInfoMapper.insert(goodwillAuditInfoPo1);

			}

			// 邮件-实时提醒-商务亲善预审批通过
			mailType = 82461003;
			this.sendEmail(goodwillApplyInfoPo, sendToFlag, null, mailType);
		}
		if (goodwilApplyAuditProcessPo.getCounts() == 1) {
			goodwillApplyInfoPo.setGoodwillStatus(82551007);// 待通知开票/充值
			goodwillApplyInfoPo.setMaterialPassTime(new Date());

			// 邮件-实时提醒-商务亲善材料审核通过
			mailType = 82461007;
			this.sendEmail(goodwillApplyInfoPo, sendToFlag, null, mailType);
		}

		// add by cl 继续审核
		if (goodwillApplyInfoPo.getGoodwillStatus() == CommonConstants.GOODWILL_STATUS_APPLY_AUDIT) {
			// 预申请单审核
			mailType = 82461001;
			sendToFlag = 2; // 角色
			logger.info("亲善pos:{}", JSONObject.toJSON(pos));
			if ("SHQYJL".equals(goodwilApplyAuditProcessPo.getAuditRole())
					|| "SHDQJL".equals(goodwilApplyAuditProcessPo.getAuditRole())

					|| "CCMGJJL".equals(goodwilApplyAuditProcessPo.getAuditRole())) {
				if(isMail){
					this.sendEmail(goodwillApplyInfoPo, sendToFlag, pos.getAuditRole(), mailType);
				}
			}
			if ("SHQYZJ".equals(goodwilApplyAuditProcessPo.getAuditRole())
					|| "CCMZJ".equals(goodwilApplyAuditProcessPo.getAuditRole())) {
				if(isMail){
					this.sendEmail(goodwillApplyInfoPo, sendToFlag, "CCMQ", mailType);
				}
			}

		}
		if (goodwillApplyInfoPo.getGoodwillStatus() == CommonConstants.GOODWILL_STATUS_MATERIAL_AUDIT) {
			// 材料审核
			mailType = 82461005;
			sendToFlag = 2; // 角色
			logger.info("亲善pos:{}", JSONObject.toJSON(pos));
			if(isMail){
				this.sendEmail(goodwillApplyInfoPo, sendToFlag, pos.getAuditRole(), mailType);
			}
//					if ("OEM-CWJL".equals(goodwilApplyAuditProcessPo.getAuditRole()) && pos != null) {
//						this.sendEmail(goodwillApplyInfoPo, sendToFlag, "CCMQ", mailType);
//					} else {
//						this.sendEmail(goodwillApplyInfoPo, sendToFlag, pos.getAuditRole(), mailType);
//					}

		}

	}
}

