package com.yonyou.dmscus.customer.service.impl.voicemanage;


import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.souche.api.SoucheApiException;
import com.souche.api.SoucheClient;
import com.souche.api.SoucheRequest;
import com.souche.api.SoucheResponse;
import com.yonyou.dmscloud.framework.util.redis.RedisClient;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscloud.function.utils.jsonSerializer.JSONUtil;
import com.yonyou.dmscus.customer.configuration.VocConfig;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.voicemanage.CallDetailsMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.CallScoreItemsMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.SaCustomerNumberMapper;
import com.yonyou.dmscus.customer.dto.voc.ObtainTokenParamDTO;
import com.yonyou.dmscus.customer.dto.voc.ObtainTokenResult;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.CallDetailsDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.CommonConfigPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO;
import com.yonyou.dmscus.customer.feign.VoiceOfCustomerFeignClient;
import com.yonyou.dmscus.customer.service.common.HttpLogService;
import com.yonyou.dmscus.customer.service.complaint.sale.ConfigCodeService;
import com.yonyou.dmscus.customer.service.voicemanage.CallDetailsService;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.dmscus.customer.utils.ai.*;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 通话详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Service
public class CallDetailsServiceImpl extends ServiceImpl<CallDetailsMapper, CallDetailsPO> implements CallDetailsService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    CallDetailsMapper callDetailsMapper;

    @Resource
    SaCustomerNumberMapper saCustomerNumberMapper;

    @Resource
    CallScoreItemsMapper callScoreItemsMapper;

    @Value("${ai.telecom.common.url_mapping_session}")
    String URL_MAPPING_SESSION; //话单拉取

    @Value("${souche.serverUrl}")
    String serverUrl;  //大搜车url

    @Value("${souche.appKey}")
    String appKey; //大搜车appKey

    @Value("${souche.appSecret}")
    String appSecret; //大搜车appSecret

    @Value("${souche.voiceUrl}")
    String voiceUrl;

    @Value("${souche.accessKey}")
    String accessKey;

    @Value("${souche.accessSecret}")
    String accessSecret;


    @Value("${zijie.serverUrl}")
    String zijieServerUrl;  //字节url
    @Value("${zijie.appKey}")
    String zijieAppKey; //字节appKey
    @Value("${zijie.voiceUrl}")
    String zijievoiceUrl;
    @Value("${zijie.user}")
    String zijievoiceUser;
    @Value("${zijie.token}")
    String zijievoiceToken;
    @Value("${zijie.oldtime}")
    String zijieoldtime;
    @Resource
    HttpLogService httpLogService;

    @Autowired
    private VoiceOfCustomerFeignClient voiceOfCustomerFeignClient;

    @Autowired
    private VocConfig vocConfig;

    @Autowired
    private RedisClient redisClient;

    @Resource
    private ConfigCodeService configCodeService;
    /**
     * 将map转换成url
     *
     * @param map
     * @return
     */
    public static String getUrlParamsByMap(Map<String, Object> map) {
        if (map == null) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            sb.append(entry.getKey() + "=" + entry.getValue());
            sb.append("&");
        }
        String s = sb.toString();
        if (s.endsWith("&")) {
            s = org.apache.commons.lang.StringUtils.substringBeforeLast(s, "&");
        }
        return s;
    }

    /**
     * 分页查询对应数据
     *
     * @param page           分页对象
     * @param callDetailsDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.CallDetailsDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<CallDetailsDTO> selectPageBysql(Page page, CallDetailsDTO callDetailsDTO) {
        if (callDetailsDTO == null) {
            callDetailsDTO = new CallDetailsDTO();
        }
        CallDetailsPO callDetailsPO = callDetailsDTO.transDtoToPo(CallDetailsPO.class);

        List<CallDetailsPO> list = callDetailsMapper.selectPageBySql(page, callDetailsPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<CallDetailsDTO> result = list.stream().map(m -> m.transPoToDto(CallDetailsDTO.class)).collect
                    (Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param callDetailsDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.CallDetailsDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<CallDetailsDTO> selectListBySql(CallDetailsDTO callDetailsDTO) {
        if (callDetailsDTO == null) {
            callDetailsDTO = new CallDetailsDTO();
        }
        CallDetailsPO callDetailsPO = callDetailsDTO.transDtoToPo(CallDetailsPO.class);
        List<CallDetailsPO> list = callDetailsMapper.selectListBySql(callDetailsPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(CallDetailsDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.CallDetailsDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public CallDetailsDTO getById(Long id) {
        CallDetailsPO callDetailsPO = callDetailsMapper.selectById(id);
        if (callDetailsPO != null) {
            return callDetailsPO.transPoToDto(CallDetailsDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param callDetailsDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(CallDetailsDTO callDetailsDTO) {
        //对对象进行赋值操作
        CallDetailsPO callDetailsPO = callDetailsDTO.transDtoToPo(CallDetailsPO.class);
        //执行插入
        int row = callDetailsMapper.insert(callDetailsPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id             主键ID
     * @param callDetailsDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, CallDetailsDTO callDetailsDTO) {
        CallDetailsPO callDetailsPO = callDetailsMapper.selectById(id);
        //对对象进行赋值操作
        callDetailsDTO.transDtoToPo(callDetailsPO);
        //执行更新
        int row = callDetailsMapper.updateById(callDetailsPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = callDetailsMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = callDetailsMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 查询通话记录
     *
     * @param detailId
     * @return
     */
    @Override
    public List<CallDetailsDTO> getCallDetails(String detailId) {
        List<CallDetailsPO> list = callDetailsMapper.getCallDetails(detailId);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(CallDetailsDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 接口获取通话记录
     * 定时任务
     * 1.一天一次（第二天凌晨拉取前一天所有登记过的的但是没有拉取过的登记记录）
     * 2.根据callId 拉取
     */
    @Override
    public void getCallDetailsByAi() {
        logger.info("话单拉取（开始）...");
        //定时任务拉取，
        List<Map<String, Object>> list = saCustomerNumberMapper.selectYesterdayAll();
        List<CallDetailsChild> allDetail = new ArrayList<CallDetailsChild>();
        logger.info("param:" + list);
        if (!CommonUtils.isNullOrEmpty(list) && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                String callId = (String) list.get(i).get("call_id");
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("callId", callId);
                //请求电信接口
                String str = DccHttpHelper.httpPost(URL_MAPPING_SESSION, map);
                //响应结果
                DccResponseUtil response = JSONObject.parseObject(str, DccResponseUtil.class);
                if ("0".equals(response.getCode())) {
                    //响应子元素
                    List<CallDetailsChild> callDetailsChildList = JSONObject.parseArray(response.getResult(), CallDetailsChild.class);
                    if (!CommonUtils.isNullOrEmpty(callDetailsChildList) && callDetailsChildList.size() > 0) {
                        for (CallDetailsChild d : callDetailsChildList) {
                            d.setCallId(callId);
                            d.setOwnerCode(list.get(i).get("owner_code") + "");
                            d.setOwnerParCode(list.get(i).get("owner_par_code") + "");
                            d.setDealerCode(list.get(i).get("dealer_code") + "");
                            try {
                                if (!StringUtils.isNullOrEmpty(d.getStartTime())) {
                                    d.setStartDate(new SimpleDateFormat("yyyyMMddHHmmss").parse(d.getStartTime() + ""));
                                }
                                if (!StringUtils.isNullOrEmpty(d.getEndTime())) {
                                    d.setEndDate(new SimpleDateFormat("yyyyMMddHHmmss").parse(d.getEndTime() + ""));
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }


                        }
                        allDetail.addAll(callDetailsChildList);
                    }

                }
                logger.info("callId:" + callId + ";话单拉取" + str);
            }
            //保存数据库
            if (!CommonUtils.isNullOrEmpty(allDetail) && allDetail.size() > 0) {
                callDetailsMapper.insertCallDetails(allDetail);
            }
        }


    }

    @Override
    public String sendSouche(String sessionId, String serviceId, Date startTime) {
        logger.info("请求大搜车获取总分---开始");
        String data = null;
        // 实例化 SDK 客户端
        SoucheClient soucheClient = new SoucheClient(serverUrl, appKey, appSecret);
        // 实例化请求类
        SoucheRequest request = new SoucheRequest();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        dataMap.put("sessionId", sessionId);
        dataMap.put("serviceId", serviceId);  //暂时不知道取什么值
        dataMap.put("callTime", startTime.getTime());
        logger.info("入参：" + dataMap);
        // 设置请求参数 api {String}
        request.setApi("com.souche.voipasr.analysisServiceController.getAiAnalysisScoreItems.json");
        request.setData(dataMap);
        try {
            // 发送请求
            SoucheResponse response = soucheClient.execute(request);
            if (response.isSuccess()) {
                // 请求成功
                data = response.getData();
                logger.info("获取总分：" + data);

            }
        } catch (SoucheApiException e) {
            // 业务错误查看 errCode 和 errMessage 内容
            int errCode = e.getErrCode();
            String errMessage = e.getErrMessage();
            logger.info(errCode + ": " + errMessage);
            // 通信错误查看 message 内容
            String errorMessage = e.getMessage();
            logger.info(errorMessage);

        }
        logger.info("请求大搜车获取总分---结束" + serviceId);
        return data;
    }

    /**
     * 获取录音播放的地址1
     */
    @Override
    public String getVoiceUrl1(Long id) {
        logger.info("voiceUrl:" + voiceUrl);
        StringBuilder url = new StringBuilder(voiceUrl);
        CallDetailsPO callDetailsPO = callDetailsMapper.selectById(id);
        Integer serviceId = 1;
        if (callDetailsPO.getCreatedAt().compareTo(DateUtils.parseDateStrToDate("2021-04-12 23:59:59", "yyyy-MM-dd HH:mm:ss")) > 0) {
            serviceId = 2;
        }
        url.append("?sessionId=" + callDetailsPO.getSessionId() + "&serviceId=" + serviceId + "&callTime=" + callDetailsPO.getStartTime().getTime() + "&originalToken=" + SoucheUtil.createToken(accessSecret, accessKey));
        logger.info("url:" + url);
        return url.toString();
    }

    /**
     * 获取录音播放的地址1
     */
    @Override
    public String getVoiceUrl(Long id) {

        Map<String, Object> map = callDetailsMapper.selectCallVoicePo(id);
        if (StringUtils.isNullOrEmpty(map.get("data_sources"))){
            throw new DALException("当前得分数据未返回，无法跳转");
        }
        String dataSources = map.get("data_sources").toString();
        if ("1".equals(dataSources)) {
            String recallVoiceUrl = queryConfigValue(CommonConstants.RECALL_VOICE_URL);
            if (Objects.equals(recallVoiceUrl,CommonConstants.BYTEDANCE)){
                return voiceZjUrl(id, map);
            }else if (Objects.equals(recallVoiceUrl,CommonConstants.VOC)){
                return voiceVocUrl(id, map ,CommonConstants.BYTEDANCE);
            }else {
                return voiceZjUrl(id, map);
            }
        } else if ("2".equals(dataSources)) {
            return voiceVocUrl(id, map , null);
        } else {
            throw new DALException("未知数据来源");
        }

    }


    public String queryConfigValue(String configValue) {
        CommonConfigPO configByKey = configCodeService.getConfigByKey(null, configValue);
        logger.info("config res:{}", configByKey);
        if (Objects.isNull(configByKey)) {
            logger.error("获取脱敏配置信息失败");
            return null;
        } else {
            if (StringUtils.isNullOrEmpty(configByKey.getConfigValue())) {
                return null;
            }
        }
        return configByKey.getConfigValue();
    }



    private String voiceZjUrl(Long id, Map<String, Object> map) {
        logger.info("zijievoiceUrl:" + zijievoiceUrl);
        StringBuilder url = new StringBuilder(zijievoiceUrl);
        //https://vcdc.yuyidata.com/
        // ?agent=volvo:service:4649711
        // &companyId=1003  固定
        // &customer=1720001367790612021111013583844471854945_1636523929
        // &date=2021-11-10
        // &userId=887699549555138560 固定
        // &token=eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiI1ZDViYTYzMy04MThjLTQyMzItOWY4MC05M2FlZjRhMjI4NTciLCJpYXQiOjE2MzI5MzIyMzksImV4cCI6MTYzMzUzNzAzOSwicmVhbE5hbWUiOiJ2b2x2byIsImNvbXBhbnlfaWQiOjEwMDMsImRlcHRfaWRzIjpbXSwiYWN0aW9uQ29kZSI6W10sInVzZXJUeXBlIjoxLCJ1c2VySWQiOjg4NzY5OTU0OTU1NTEzODU2MCwidXNlcm5hbWUiOiJ2b2x2byJ9.YXX51hS2PG_qUZsPe2KDJwO4xKAR_j-cX6LKgzGHPmUxLgrCNI5KixpiCKg99NsZ7vPntai_7V4ldxOs4513pA 固定
        //获取callid 和sessionid 查询 sa_id   拼接agent

        // String old = "2021-12-01 22:03:00";
        logger.info("zijieoldtime:{}", zijieoldtime);
        try {
            Date date = (Date) map.get("created_at");
            if (date.getTime() / 1000 < Long.valueOf(zijieoldtime)) {
                return this.getVoiceUrl1(id);
            }

        } catch (Exception e) {
            throw new DALException("未获取到录音地址");
        }
        logger.info("获取字节获取数据：{}", JSONObject.toJSON(map));
        url.append("?agent=volvo:service:");
        url.append(map.get("sa_id"));
        url.append("&companyId=1003");
        url.append("&customer=");
        url.append(map.get("session_Id"));
        url.append("_");
        Date created_at = (Date) map.get("start_time");
        try {
            url.append(created_at.getTime() / 1000);
        } catch (Exception e) {
            logger.info("获取字节url失败1：{}", e.getMessage());
            throw new DALException("字节拨打时间异常");
        }
        url.append("&date=");
        try {
            url.append(DateUtils.formatDate(created_at, DateUtils.PATTERN_YYYY_MM_DD));
        } catch (Exception e) {
            logger.info("获取字节url失败2：{}", e.getMessage());
            throw new DALException("字节拨打时间异常");
        }
        url.append("&userId=");
        url.append(zijievoiceUser);
        url.append("&token=");
        url.append(zijievoiceToken);
        logger.info("url:" + url);
        return url.toString();
    }

    private String voiceVocUrl(Long id, Map<String, Object> map, String bytedance) {
        String sessionId = map.get("session_Id").toString();
        logger.info("通话记录:{}", JSONUtil.objectToJson(map));
        logger.info("当前sessionId为:{}设置VOC地址", sessionId);
        String scorePageUrl = null;
        if (Objects.equals(bytedance, CommonConstants.BYTEDANCE)){
            scorePageUrl = queryConfigValue(CommonConstants.RECALL_VOICE_URL_HIS);
        }else {
            scorePageUrl = vocConfig.getScorePageUrl();
        }
        String str = "%s?sessionId=%s&token=%s";
        return String.format(str, scorePageUrl, sessionId, obtainToken());
    }

    @Override
    public String obtainToken() {
        if (Objects.isNull(redisClient.get(CommonConstants.VOC_TOKEN_KEY))) {
            setTokenToRedis();
        }
        return redisClient.get(CommonConstants.VOC_TOKEN_KEY).toString();
    }

    public void setTokenToRedis() {
        try {
            ObtainTokenParamDTO requestParamDTO = ObtainTokenParamDTO.builder().clientId(vocConfig.getClientId()).clientSecret(vocConfig.getClientSecret()).build();
            ObtainTokenResult obtainTokenResult = voiceOfCustomerFeignClient.getClientToke(requestParamDTO);
            logger.info("获取VOC系统token响应：{}", JSON.toJSONString(obtainTokenResult));
            if (!obtainTokenResult.isSuccess() || org.apache.commons.lang3.StringUtils.isBlank(obtainTokenResult.getToken())) {
                logger.info("获取VOC系统token失败：{}", obtainTokenResult);
            }
            String token = obtainTokenResult.getToken();
            Integer expire = obtainTokenResult.getTokenExpire();
            redisClient.set(CommonConstants.VOC_TOKEN_KEY, token, expire * 1000);
            logger.info("VOC系统token存入redis成功");
        } catch (Exception e) {
            logger.error("获取VOC系统token异常:{}", e.getMessage(), e);
        }
    }


    /**
     * 定时获取通话得分
     */
    @Override
    public void getCallTotalScore() {
        List<Map<String, Object>> result = callDetailsMapper.selectNotTotalScore();
        if (!CommonUtils.isNullOrEmpty(result) && result.size() > 0) {
            List<CallDetailsPO> updateList = new ArrayList<CallDetailsPO>(); //批量更新
            for (Map m : result) {
                if (!StringUtils.isNullOrEmpty(m.get("session_Id")) && !StringUtils.isNullOrEmpty(m.get("start_time"))) {
                    String serviceId = "1";
                    if (((Date) m.get("created_at")).compareTo(DateUtils.parseDateStrToDate("2021-04-12 23:59:59", "yyyy-MM-dd HH:mm:ss")) > 0) {
                        serviceId = "2";
                    }
                    String data = sendSouche(m.get("session_Id") + "", serviceId, (Date) m.get("start_time"));
                    SoucheEntity SoucheEntity = JSONObject.parseObject(data, SoucheEntity.class);
                    //添加，更新到数据库
                    if (SoucheEntity.getSuccess()) {
                        //添加，更新到数据库
                        CallDetailsPO a = new CallDetailsPO();
                        SoucheEntityChild child = JSONObject.parseObject(SoucheEntity.getData(), SoucheEntityChild.class);
                        a.setTotalScore(child.getTotalScore());
                        a.setId((Long) m.get("id"));
                        updateList.add(a);
                        //保存通话得分明细
                        callScoreItemsMapper.saveScoreItem(child.getAnalysisScoreItems());
                    }
                } else {
                    logger.info(m.get("session_Id") + "sessionid或者" + m.get("start_time") + "start_time为空");
                }

            }
            //批量更新数据（总分）
            if (!CommonUtils.isNullOrEmpty(updateList) && updateList.size() > 0) {
                callDetailsMapper.updateTotalScore(updateList);
            }
        }
    }

    /**
     * 定时获取通话得分
     */
    @Override
    public void getCallTotalScorezj() {
        List<Map<String, Object>> result = callDetailsMapper.selectNotTotalScore();
        if (!CommonUtils.isNullOrEmpty(result) && result.size() > 0) {
            List<CallDetailsPO> updateList = new ArrayList<CallDetailsPO>(); //批量更新
            for (Map m : result) {
                if (!StringUtils.isNullOrEmpty(m.get("session_Id")) && !StringUtils.isNullOrEmpty(m.get("start_time")) && !StringUtils.isNullOrEmpty(m.get("call_id"))) {
                    String serviceId = "5";
                    if (ObjectUtils.isEmpty(m.get("call_id"))) {
                        serviceId = "5";
                    } else {
                        List<Map<String, Object>> mapList = callDetailsMapper.selectInviteIdAndInvitetype(m.get("call_id").toString(), m.get("session_Id").toString());
                        if (mapList != null && mapList.size() > 0) {
                            Map<String, Object> map = mapList.get(0);
                            if (ObjectUtils.isNotEmpty(map) && !com.yonyou.dmscloud.function.utils.common.StringUtils.isNullOrEmpty(map.get("invite_id"))) {
                                if ("82381003".equals(map.get("invite_type") + "")) {
                                    serviceId = "5";//质检类型对应续保/回访
                                } else {
                                    serviceId = "4";//质检类型对应⾸定保；
                                }
                            } else {
                                serviceId = "5";//质检类型对应续保/回访
                            }
                        }

                    }
                    String data = sendSouchezj(m.get("session_Id") + "", serviceId, (Date) m.get("start_time"));
                    if (!StringUtils.isNullOrEmpty(data)) {
                        SoucheZJEntity entity = JSONObject.parseObject(data, SoucheZJEntity.class);
                        List<AnalysisScoreItemsZj> list = entity.getAnalysisScoreItems();
                        if (list != null && list.size() > 0) {
                            AnalysisScoreItemsZj en = list.get(0);
                            CallDetailsPO a = new CallDetailsPO();
                            a.setTotalScore(en.getTotalScore());
                            a.setId((Long) m.get("id"));
                            updateList.add(a);
                            //保存通话得分明细
                            callScoreItemsMapper.saveScoreZJItem(list);
                        }
                    }
                } else {
                    logger.info(m.get("session_Id") + "sessionid或者" + m.get("start_time") + "start_time为空");
                }

            }
            //批量更新数据（总分）
            if (!CommonUtils.isNullOrEmpty(updateList) && updateList.size() > 0) {
                callDetailsMapper.updateTotalScore(updateList);
            }
        }
    }

    @Override
    public String sendSouchezj(String sessionId, String serviceId, Date startTime) {
        logger.info("请求字节获取总分---开始");
        String responseCode = null;
        String responseMsg = null;
        String data = null;
        String serverUrl = zijieServerUrl + "/v1/dock-volvo/getAiAnalysisScoreItems";
        logger.info("字节url:{}", serverUrl);
        // 实例化请求类
        Map<String, Object> dataMap = new HashMap<String, Object>();
        dataMap.put("sessionId", sessionId);
        dataMap.put("serviceId", serviceId);  //暂时不知道取什么值
        dataMap.put("callTime", startTime.getTime());
        dataMap.put("appKey", zijieAppKey);
        logger.info("字节url:{}", serverUrl);
        // String jsonData = JSON.toJSONString(dataMap);
        String jsonData = getUrlParamsByMap(dataMap);
        logger.info("入参：" + jsonData);
        // 设置请求参数 api {String}
        try {
            // 发送请求
            HttpResponse httpResponse = HttpUtil.createGet(serverUrl + "?" + jsonData).execute();

            int status = httpResponse.getStatus();
            if (status == 200) {
                JSONObject jsonObject = JSON.parseObject(httpResponse.body());
                logger.info("字节返回:{}", jsonObject.toJSONString());
                responseMsg = "成功";
                int result = jsonObject.getIntValue("code");
                if (result == 200) {
                    data = jsonObject.getString("data");
                    logger.info("获取总分：" + data);
                }
            } else {
                responseCode = status + "";
                responseMsg = "调字节获取总分接口报错";
            }

        } catch (Exception e) {
            // 业务错误查看 errCode 和 errMessage 内容
            responseCode = "500";
            String errMessage = e.getMessage();
            logger.error(responseCode + ": " + errMessage);
            // 通信错误查看 message 内容
            responseMsg = e.getMessage();
        } finally {
            httpLogService.saveHttpLog("字节-获取总分", serverUrl, jsonData, "POST", responseCode, responseMsg);
        }
        logger.info("请求字节获取总分---结束" + sessionId);
        return data;
    }

    @Override
    public void insertList(List<CallDetailsPO> callDetailsPOList) {
        saveBatch(callDetailsPOList,100);
    }
}
