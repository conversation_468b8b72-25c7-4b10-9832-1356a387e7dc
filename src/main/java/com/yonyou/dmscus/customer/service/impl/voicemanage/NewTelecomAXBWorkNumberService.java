package com.yonyou.dmscus.customer.service.impl.voicemanage;

import com.alibaba.fastjson.JSON;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.jsonSerializer.JSONUtil;
import com.yonyou.dmscus.customer.configuration.NewTelecomConfig;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.entity.dto.common.RemoveWorkAxbInfo;
import com.yonyou.dmscus.customer.entity.dto.common.WorkAxbInfoBindDto;
import com.yonyou.dmscus.customer.utils.ai.NewTelecomHelper;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.*;

@Service
public class NewTelecomAXBWorkNumberService extends AbstractWorkNumberSerivce {
    private static final Logger logger = LoggerFactory.getLogger(NewTelecomAXBWorkNumberService.class);

    @Autowired
    private NewTelecomHelper newTelecomHelper;

    @Autowired
    private NewTelecomConfig newTelecomConfig;

    private static final String MWSSAGE = "message";
    private static final String RETURNCODE = "returnCode";
    private static final String MSG = "新电信绑定结果响应: {}";

    @Override
    public boolean support(String operator) {
        return StringUtils.equalsIgnoreCase(operator, String.valueOf(CommonConstants.NEW_TELECOM_AXB));
    }

    @Override
    Map<String, Object> info(String workNumbe) {
        String infoResult = null;
        try {
            HashMap<String, String> requestMap = new HashMap<>();
            requestMap.put("inCalledNum", workNumbe);
            String request = JSON.toJSONString(requestMap);
            String config = JSON.toJSONString(newTelecomConfig);
            logger.info("新电信查询绑定请求参数:{}", request);
            logger.info("新电信查询绑定请求配置:{}", config);
            infoResult = newTelecomHelper.httpPost(newTelecomConfig.getInfo(), JSON.toJSONString(requestMap));
            String result = JSON.toJSONString(infoResult);
            logger.info("新电信查询B绑定结果响应: {}", result);
        } catch (Exception e) {
            logger.error("新电信绑定异常:", e);
        }
        Map<String, Object> stringObjectMap = JSONUtil.jsonToMap(infoResult);
        if(MapUtils.isNotEmpty(stringObjectMap)&&stringObjectMap.containsKey("msg")){
            Object msg = stringObjectMap.get("msg");
            stringObjectMap.put(MWSSAGE,msg);
        }
        return stringObjectMap;
    }

    //@Override
    public Map<String, Object> bind(String extension, String mobilePhone, String callId, String userOrderId) {
        String bindResult = null;
        logger.info("\nAXB关系入参:{},\n{}", extension, mobilePhone);
        try {
            if (mobilePhone.contains(",")) {
                String[] split = mobilePhone.split(",");
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(new Date());
                calendar.add(Calendar.MINUTE,3);
                WorkAxbInfoBindDto workAxbInfo = WorkAxbInfoBindDto.builder()
                        .callNumA(split[0]).callNumB(split[1])
                        .callNumX(extension)
                        .userData(callId)
                        .effectStartTime(new Date())
                        .effectEndTime(calendar.getTime())
                        .oneHide("1")
                        .userOrderId(userOrderId)
                        .build();
                List<WorkAxbInfoBindDto> workAxbInfos = workAxbInfo.buildWorkAxbInfoList();
                String jsonString = JSON.toJSONString(workAxbInfos);
                String config = JSON.toJSONString(newTelecomConfig);
                logger.info("新电信绑定请求参数:{}", jsonString);
                logger.info("新电信绑定请求配置:{}", config);
                bindResult = newTelecomHelper.httpPost(newTelecomConfig.getBindB(), jsonString);
                String result = JSON.toJSONString(bindResult);
                logger.info(MSG, result);
            }
        } catch (Exception e) {
            logger.error("新电信绑定异常:", e);
        }
        Map<String, Object> stringObjectMap = JSONUtil.jsonToMap(bindResult);
        if(MapUtils.isNotEmpty(stringObjectMap)&&stringObjectMap.containsKey("msg")){
            Object msg = stringObjectMap.get("msg");
            stringObjectMap.put(MWSSAGE,msg);
        }
        return stringObjectMap;
    }


    @Override
    public Map<String, Object> unBind(String workNumbe) {
        String unBindResult = null;
        RemoveWorkAxbInfo removeWorkAxbInfo = RemoveWorkAxbInfo.builder().callNumA(workNumbe).build();
        try {
            List<RemoveWorkAxbInfo> removeWorkAxbInfos = removeWorkAxbInfo.buildWorkAxnInfoList();
            String remove = JSON.toJSONString(removeWorkAxbInfos);
            String config = JSON.toJSONString(newTelecomConfig);
            logger.info("新电信解绑请求参数:{}", remove);
            logger.info("新电信解绑请求配置:{}", config);
            unBindResult = newTelecomHelper.httpPost(newTelecomConfig.getUnbindB(), JSON.toJSONString(removeWorkAxbInfos));
            String result = JSON.toJSONString(unBindResult);
            logger.info(MSG, result);
        } catch (Exception e) {
            logger.error("新电信解绑异常:", e);
        }
        Map<String, Object> stringObjectMap = JSONUtil.jsonToMap(unBindResult);
        if(MapUtils.isNotEmpty(stringObjectMap)&&stringObjectMap.containsKey("msg")){
            Object msg = stringObjectMap.get("msg");
            stringObjectMap.put(MWSSAGE,msg);
        }
        return stringObjectMap;
    }

    @Override
    public boolean isSuccess(String methodType, Map<String, Object> result) {
        //新电信状态码为0时,表示请求成功，info接口响应判断totalCount 命中数是否大于0,大于0则视为存在绑定关系
        if (null != result && Objects.equals(String.valueOf(result.get(RETURNCODE)), "0")) {
            if (Objects.equals(methodType, CommonConstants.WORK_NUMBER_MENTHODTYPE_REGISTER)) {
                return true;
            }
            if (Objects.equals(methodType, CommonConstants.WORK_NUMBER_MENTHODTYPE_INFO)) {
                return Integer.parseInt(result.get("totalCount").toString()) > 0;
            }
        }
        return false;
    }

    @Override
    Map<String, Object> register(String callId, String holderNumber, String workNumbe, String customNumber, String userOrderId) {
        if (StringUtils.isEmpty(holderNumber) || StringUtils.isEmpty(customNumber)) {
            logger.error("holderNumber:{},customNumber:{}",holderNumber,customNumber);
            throw new ServiceBizException("呼叫号码与被呼叫号码不能为空");
        }
        HashMap<String, String> requestMap = new HashMap<>();
        requestMap.put("inCalledNum", workNumbe);
        String request = JSON.toJSONString(requestMap);
        String config = JSON.toJSONString(newTelecomConfig);
        logger.info("新电信查询绑定请求参数:{}", request);
        logger.info("新电信查询绑定请求配置:{}", config);
        String  infoResult;
        try {
            infoResult = newTelecomHelper.httpPost(newTelecomConfig.getInfo(), JSON.toJSONString(requestMap));
        } catch (Exception e) {
            logger.info("新电信查询绑定请求错误e:{}", e);
            throw new ServiceBizException("根据工作号查询绑定关系失败");
        }
        Map<String, Object> info = JSONUtil.jsonToMap(infoResult);
        if (MapUtils.isNotEmpty(info)) {
            infoPd(info);
        }
        String join = String.join(",", holderNumber, customNumber);
        return bind(workNumbe, join, callId, userOrderId);
    }

    void infoPd(Map<String, Object> info){
            if (info.containsKey(RETURNCODE) && "0".equals(info.get(RETURNCODE))) {
                Object resBody = info.get("resBody");
                if (resBody instanceof List<?>) {
                    List<Object> objects = (List<Object>)resBody;
                    for (Object object : objects) {
                        if (object != null) {
                            infoObj(object);
                        }
                    }

                }
            } else {
                throw new ServiceBizException("查询新电信历史绑定关系出错:{}", (Serializable) info.get("msg"));
            }
    }

    void infoObj(Object object){
        Map<String, Object> map = (Map) object;
        Object bindId = map.get("bindId");
        Object inCallerNumber = map.get("inCallerNumber");
        Object displayNumber = map.get("displayNumber");
        if (!com.yonyou.dmscus.customer.util.common.StringUtils.isNullOrEmpty(inCallerNumber)
                && !com.yonyou.dmscus.customer.util.common.StringUtils.isNullOrEmpty(displayNumber)
                && !com.yonyou.dmscus.customer.util.common.StringUtils.isNullOrEmpty(bindId))
        {
            RemoveWorkAxbInfo removeWorkAxbInfo = RemoveWorkAxbInfo.builder().bindId(bindId.toString()).build();
            try {
                List<RemoveWorkAxbInfo> removeWorkAxbInfos = removeWorkAxbInfo.buildWorkAxnInfoList();
                String request = JSON.toJSONString(removeWorkAxbInfos);
                String config = JSON.toJSONString(newTelecomConfig);
                logger.info("新电信解绑请求参数:{}", request);
                logger.info("新电信解绑请求配置:{}", config);
                String unBindResult = newTelecomHelper.httpPost(newTelecomConfig.getUnbindB(), JSON.toJSONString(removeWorkAxbInfos));
                logger.info(MSG, unBindResult);
            } catch (Exception e) {
                logger.error("新电信解绑异常:", e);
            }
        }
    }

}
