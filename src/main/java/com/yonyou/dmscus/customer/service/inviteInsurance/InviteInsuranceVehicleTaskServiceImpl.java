package com.yonyou.dmscus.customer.service.inviteInsurance;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.yonyou.cloud.common.beans.RestResultResponse;
import com.yonyou.dmscloud.framework.util.bean.ApplicationContextHelper;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.inviteInsurance.*;
import com.yonyou.dmscus.customer.dto.CheckRepairOrderDTO;
import com.yonyou.dmscus.customer.dto.RepairOrderVO;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.UserInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InsuranceSaSllocateDlrDto;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.*;
import com.yonyou.dmscus.customer.feign.DomainMaintainAuthFeign;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.service.httplog.HttpLogAiService;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.dmscus.customer.util.common.Utility;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.yonyou.dmscus.customer.constants.CommonConstants.RENEWAL_INSURANCE_WHITE;
import static com.yonyou.dmscus.customer.constants.CommonConstants.WECOM_ACCIDENT_ROSTER_TYPE_WHITE;
import static java.util.stream.Collectors.toList;


/**
 * <p>
 * 车辆邀约续保任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@Service
public class InviteInsuranceVehicleTaskServiceImpl implements InviteInsuranceVehicleTaskService {
    //日志对象
    private static final Logger logger = LoggerFactory.getLogger(InviteInsuranceVehicleTaskServiceImpl.class);
    @Resource
    InviteInsuranceVehicleTaskMapper inviteInsuranceVehicleTaskMapper;
    @Resource
    InviteInsuranceVehicleRecordMapper inviteInsuranceVehicleRecordMapper;
    @Resource
    InviteInsuranceSaAllocateRuleMapper inviteInsuranceSaAllocateRuleMapper;
    @Resource
    InviteInsuranceSaAllocateRuleDetailMapper inviteInsuranceSaAllocateRuleDetailMapper;
    @Autowired
    InviteInsuranceDuplicateRemovalRuleService inviteInsuranceDuplicateRemovalRuleService;
    @Autowired
    InviteInsuranceVehicleRecordService inviteInsuranceVehicleRecordService;
    @Autowired
    ReportCommonClient reportCommonClient;
    @Resource
    InviteInsuranceRuleMapper inviteInsuranceRuleMapper;
    @Autowired
    HttpLogAiService httpLogAiService;
    @Autowired
    private DomainMaintainAuthFeign domainMaintainAuthFeign;

    private Integer updateSize = 200; // 批次集合执行最大数
    private static Map<Integer, Integer> dataSourceLevel = new HashMap<>();

    static {

        dataSourceLevel.put(CommonConstants.DATA_SOURCE_NEW_REPAIR_ORDER_SA, 1); // SA手动填写的续保到期时间
        dataSourceLevel.put(CommonConstants.DATA_SOURCE_NEW_YB, 2); // 易保线索
        dataSourceLevel.put(CommonConstants.DATA_SOURCE_NEW_ENTER, 3); // 前台录入
        dataSourceLevel.put(CommonConstants.DATA_SOURCE_NEW_IMPORT, 4); // 底表手动导入的线索
        dataSourceLevel.put(CommonConstants.DATA_SOURCE_NEW_REPAIR_ORDER_24, 6); // 24个月进厂工单（开票时间拼接）

        dataSourceLevel.put(CommonConstants.DATA_SOURCE_REPAIR_ORDER_SA, 1); // SA手动填写的续保到期时间
        dataSourceLevel.put(CommonConstants.DATA_SOURCE_YB, 2); // 易保线索
        dataSourceLevel.put(CommonConstants.DATA_SOURCE_TB, 3); // 投保线索
        dataSourceLevel.put(CommonConstants.DATA_SOURCE_NEW_CAR, 5); // 新车
        dataSourceLevel.put(CommonConstants.DATA_SOURCE_REPAIR_ORDER_24, 6); // 24个月进厂工单（开票时间拼接）
    }

    /**
     * 根据邀约任务创建邀约线索
     *
     * @param createDate
     * @return
     */
    @Override
    public int createInviteInsuranceByTask(String ownerCode, String vin, String createDate) {
        logger.info("createInviteInsuranceByTask start...");
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        logger.info("createInviteInsuranceByTask ownerCode:{},vin:{},createDate:{}", ownerCode, vin, createDate);
        httpLogAiService.saveHttpLog("手动根据续保邀约任务创建邀约线索","",createDate,"POST","200","手动根据续保邀约任务创建邀约线索开始","3333");

        // 邀约去重规则
        // Integer type1 = null;
        // 查询去重规则
        /*List<InviteInsuranceDuplicateRemovalRuleDTO> list = inviteInsuranceDuplicateRemovalRuleService.selectListBySql(new
                InviteInsuranceDuplicateRemovalRuleDTO());
        for (InviteInsuranceDuplicateRemovalRuleDTO dto : list) {
            if (dto.getRuleType() == 1) {
                type1 = dto.getRule();
            }
        }*/

        this.createInsurance(ownerCode, vin, createDate);
        // 续保类型 自动分配
//        this.taskAllocation(createDate, true);
        httpLogAiService.saveHttpLog("手动根据续保邀约任务创建邀约线索","",createDate,"POST","200","手动根据续保邀约任务创建邀约线索结束","3333");

        logger.info("createInviteInsuranceByTask end...");
        return 1;
    }

    /**
     * 创建续保线索
     *
     * @param createDate
     */
    private void createInsurance(String ownerCode, String vin, String createDate) {
        logger.info("任务下发线索 createInsurance start...");
        List<InviteInsuranceVehicleTaskPO> list = inviteInsuranceVehicleTaskMapper.queryInsuranceTask(ownerCode, vin, createDate);
        if(!CollectionUtils.isEmpty(list)){
            Boolean enabled = getaWhitelistEnabled();
            logger.info("续保白名单开启状态:{}",enabled);
            if(Objects.isNull(enabled)){
                logger.info("未配置续保经销商白名单");
            }else{
                if(enabled){
                    logger.info("续保白名单启用");
                    RestResultResponse<List<String>> dealerResponse = domainMaintainAuthFeign.getWhitelistedDealers(RENEWAL_INSURANCE_WHITE,WECOM_ACCIDENT_ROSTER_TYPE_WHITE);
                    if(!dealerResponse.isSuccess()){
                        throw new ServiceBizException("获取续保白名单配置异常");
                    }
                    List<String> dealers = dealerResponse.getData();
                    if(CollectionUtils.isEmpty(dealers)){
                       logger.info("续保白名单启用,未配置白名单经销商");
                    }else{
                       logger.info("续保白名单启用,已开启白名单经销商列表:{}",dealers);
                        list = list.stream().filter(e -> !dealers.contains(e.getDealerCode())).collect(toList());
                        if(CollectionUtils.isEmpty(list)){
                            logger.info("续保白名单启用,过滤后线索任务为空");
                            return;
                        }
                    }
                }else{
                  logger.info("续保白名单全网放开,线索任务转线索关闭");
                  return;
                }
            }
            //先下发商业险,保证同一批次的数据不会存在两条线索
            list = list.stream().sorted(Comparator.comparing(InviteInsuranceVehicleTaskPO::getClueType).reversed()).collect(Collectors.toList());
            for (InviteInsuranceVehicleTaskPO po : list) {
                if (null == po) {
                    logger.info("任务下发线索 null == po ownerCode:{},vin:{}", po.getOwnerCode(), po.getVin());
                    continue;
                }
                // 创建邀约线索
                this.createInviteByTask(createDate, po);
            }
        }
        logger.info("任务下发线索 createInsurance end...");
    }

    private  Boolean getaWhitelistEnabled() {
        RestResultResponse<Boolean> response = domainMaintainAuthFeign.checkIfWhitelistEnabled(RENEWAL_INSURANCE_WHITE);
        if(!response.isSuccess()){
            throw new ServiceBizException("获取续保白名单信息异常");
        }
        return response.getData();
    }

    /**
     * 自动分配sa
     */
    private void taskAllocation(String createDate, Boolean isInsurance) {
        logger.info("taskAllocation start...");
        HashMap<String, List<InviteInsuranceVehicleRecordDTO>> map = new HashMap<>();
        List<InviteInsuranceVehicleRecordPO> list = null;
        if (isInsurance) {
            list = inviteInsuranceVehicleRecordMapper.queryWaitAllocationRecodeForInsurance(createDate);
        }
        if (!CommonUtils.isNullOrEmpty(list)) {
            logger.info("! isNullOrEmpty list");
            List<InviteInsuranceVehicleRecordDTO> result = list.stream().map(m -> m.transPoToDto(InviteInsuranceVehicleRecordDTO.class))
                    .collect(Collectors.toList());
            for (InviteInsuranceVehicleRecordDTO dto : result) {
                if (map.containsKey(dto.getDealerCode())) {
                    map.get(dto.getDealerCode()).add(dto);
                } else {
                    List<InviteInsuranceVehicleRecordDTO> array = new ArrayList<>();
                    array.add(dto);
                    map.put(dto.getDealerCode(), array);
                }
            }

            for (Map.Entry<String, List<InviteInsuranceVehicleRecordDTO>> entry : map.entrySet()) {
                this.allocation(entry.getKey(), entry.getValue(), isInsurance);
            }
        }
        logger.info("taskAllocation end...");
    }

    /**
     * 分配sa
     *
     * @param list
     */
    private void allocation(String dealerCode, List<InviteInsuranceVehicleRecordDTO> list, Boolean isInsurance) {
        logger.info("allocation start...");
        LambdaQueryWrapper<InviteInsuranceSaAllocateRulePO> qu = new QueryWrapper().lambda();
        qu.eq(InviteInsuranceSaAllocateRulePO::getDealerCode, dealerCode);
        qu.eq(InviteInsuranceSaAllocateRulePO::getIsDeleted, BigDecimal.ZERO);
        List<InviteInsuranceSaAllocateRulePO> po = inviteInsuranceSaAllocateRuleMapper.selectList(qu);
        //没有分配规则， 不自动分配
        if (CollectionUtils.isEmpty(po)) {
            logger.info("allocation 没有分配规则 不自动分配 dealerCode:{}", dealerCode);
            return;
        }
        LambdaQueryWrapper<InviteInsuranceSaAllocateRuleDetailPO> qde = new QueryWrapper().lambda();
        qde.eq(InviteInsuranceSaAllocateRuleDetailPO::getDealerCode, dealerCode);
        if (isInsurance) {
            qde.eq(InviteInsuranceSaAllocateRuleDetailPO::getIsInsurance,   CommonConstants.DICT_IS_YES);
           // qde.eq(InviteInsuranceSaAllocateRuleDetailPO::getIsInsurance,   10041001);
        }
        List<InviteInsuranceSaAllocateRuleDetailPO> users = inviteInsuranceSaAllocateRuleDetailMapper.selectList(qde);
        //没有可分配sa， 不自动分配
        if (users == null || users.size() == 0) {
            return;
        }
        List<UserInfoDTO> selectSa = new ArrayList<>();
        for (InviteInsuranceSaAllocateRuleDetailPO user : users) {
            UserInfoDTO u = new UserInfoDTO();
            u.setId(Long.parseLong(user.getSaId()));
            u.setUsername(user.getSaName());
            selectSa.add(u);
        }
        InsuranceSaSllocateDlrDto saSllocateDlrDto = new InsuranceSaSllocateDlrDto();
        // 如果有相同规则则获取最后那个规则
        po.stream()
                .filter(Objects::nonNull)
                .filter(v -> null != v.getCreatedAt())
                .sorted(Comparator.comparing(InviteInsuranceSaAllocateRulePO::getCreatedAt).reversed())
                .findFirst()
                .ifPresent(v -> {
            logger.info("allocation dealerCode:{},ruleType:{}", dealerCode, v.getRuleType());
            saSllocateDlrDto.setRuleType(v.getRuleType());
        });
        saSllocateDlrDto.setInviteInsuranceVehicleRecordList(list);
        saSllocateDlrDto.setSelectSa(selectSa);
        inviteInsuranceVehicleRecordService.saveInsuranceSaSllocate(saSllocateDlrDto);
    }

    /**
     * 创建邀约线索
     *
     * @param po
     * @return
     */
    public void createInviteByTask(String createDate, InviteInsuranceVehicleTaskPO po) {
        logger.info("任务下发线索 createInviteByTask start...");
        InviteInsuranceVehicleRecordPO record = new InviteInsuranceVehicleRecordPO();
        record.setDealerCode(po.getDealerCode());
        //VCDC下发邀约
        record.setSourceType(1);
        record.setIsMain(1);
        record.setVin(po.getVin());
        record.setLicensePlateNum(po.getLicensePlateNum());
        record.setName(po.getName());
        record.setTel(po.getTel());
        record.setAge(po.getAge());
        record.setSex(po.getSex());
        record.setModel(po.getModel());
        record.setInviteType(po.getInviteType());
        record.setAdviseInDate(po.getAdviseInDate());
        record.setItemType(po.getItemType());
        record.setItemCode(po.getItemCode());
        record.setItemName(po.getItemName());
        record.setLastChangeDate(po.getInviteTime());
        //未完成
        record.setOrderStatus(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681001);
        Calendar c = Calendar.getInstance();
        c.setTime(po.getAdviseInDate());
        if (null == po.getDayInAdvance()) {
            c.add(Calendar.DATE, -90);
        } else {
            c.add(Calendar.DATE, -po.getDayInAdvance());
        }
        //计划跟进时间 = 建议进厂时间 -提前N天邀约
        record.setPlanFollowDate(c.getTime());
        //未跟进
        record.setFollowStatus(CommonConstants.FOLLOW_STATUS_I);
        record.setInsuranceBillId(po.getInsuranceBillId());
        record.setInsuranceType(po.getInsuranceType());
        record.setClueType(po.getClueType());
        record.setDataSources(po.getDataSources());
        SimpleDateFormat simdate = new SimpleDateFormat(DateUtils.PATTERN_YYYY_MM_DD);
        try {
            record.setCreatedAt(simdate.parse(createDate));
        } catch (ParseException e) {
            logger.info("时间异常{}", createDate);
        }
        logger.info("任务下发线索 InviteInsuranceVehicleRecordPO:{}", record);

        // 任务下发线索，更新下发任务
        InviteInsuranceVehicleTaskServiceImpl proxy = ApplicationContextHelper.getBeanByType(InviteInsuranceVehicleTaskServiceImpl.class);
        proxy.createRecord(po, record);
    }

    public Date addYear(Date date, int n) {
        logger.info("任务下发线索 date:{},n:{}", date, n);
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.YEAR, n);
        Date destDay = c.getTime();
        logger.info("任务下发线索 addYear:{}", destDay);
        return destDay;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void createRecord(InviteInsuranceVehicleTaskPO taskPO, InviteInsuranceVehicleRecordPO recordPO) {
        logger.info("任务下发线索 createRecord start...");
        if (null == taskPO || null == recordPO) {
            logger.info("null = taskPO || null = recordPO");
            return;
        }

        // 查询续保到期时间 >= 续保到期时间 < 续保到期时间 + 1年
        // 状态为：未完成
        LambdaQueryWrapper<InviteInsuranceVehicleRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InviteInsuranceVehicleRecordPO::getDealerCode, recordPO.getDealerCode())
                .eq(InviteInsuranceVehicleRecordPO::getVin, recordPO.getVin())
                .eq(InviteInsuranceVehicleRecordPO::getIsDeleted, 0)
                .eq(InviteInsuranceVehicleRecordPO::getOrderStatus, CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681001)
                .eq(InviteInsuranceVehicleRecordPO::getFollowStatus, CommonConstants.FOLLOW_STATUS_I)
                .ge(InviteInsuranceVehicleRecordPO::getAdviseInDate, DateUtils.firstDayOfYear(recordPO.getAdviseInDate()))
                .le(InviteInsuranceVehicleRecordPO::getAdviseInDate, DateUtils.lastDayOfYear(recordPO.getAdviseInDate()));
        List<InviteInsuranceVehicleRecordPO> records = inviteInsuranceVehicleRecordMapper.selectList(queryWrapper);

        // >= 续保到期时间 < 续保到期时间 + 1年 没有线索
        if (CollectionUtils.isEmpty(records)) {
            logger.info("任务下发线索 isEmpty records 直接下发");
            createRecord_(taskPO, recordPO);
            return; // 结束下发逻辑
        }

        int createFlag = 0; // 是否下发线索
        Date date = new Date();
        InviteInsuranceVehicleRecordPO updateFlowPO = null;
        List<InviteInsuranceVehicleRecordPO> updateFlowPOS = new ArrayList<>();
        Integer taskDataSources = taskPO.getDataSources();
        logger.info("任务下发线索 存在records 尝试根据优先级下发 taskDataSources:{}", taskDataSources);
        for (InviteInsuranceVehicleRecordPO record : records) {
            if (null == record) {
                logger.info("任务下发线索 null == record");
                continue;
            }
            Integer recordDataSources = record.getDataSources();
            logger.info("任务下发线索 recordDataSources:{}", recordDataSources);
            if (null == recordDataSources) {
                logger.info("任务下发线索 null == recordDataSources 无法确定优先级");
                continue;
            }

            // 如果存在优先级大于已有线索则尝试关闭已有线索
            int taskLevel = Optional.ofNullable(dataSourceLevel.get(taskDataSources)).orElse(5);
            int recordLevel = Optional.ofNullable(dataSourceLevel.get(recordDataSources)).orElse(5);
            logger.info("任务下发线索 taskLevel:{},recordLevel:{}", taskLevel, recordLevel);
            if (recordLevel < taskLevel) {
                logger.info("任务下发线索 recordLevel < taskLevel");
                String invalidReason = "存在高优先级任务,此任务关闭";
                this.closeTask(taskPO,invalidReason);
                continue;
            }else if(recordLevel > taskLevel){
                logger.info("线索 recordLevel > taskLevel 线索关闭,recorId:{}",record.getId());
                this.closeRecord(record);
            }else{
                // 商业险/交强险，规则：商业 > 交强
                logger.info("任务下发线索 taskLevel == 2");
                int taskClueType = null == taskPO.getClueType() ? 0 : taskPO.getClueType();
                int recordClueType = null == record.getClueType() ? 0 : record.getClueType();
                logger.info("任务下发线索 taskClueType:{},recordClueType:{}", taskClueType, recordClueType);
                if(taskClueType < recordClueType){
                    logger.info("任务下发线索 taskClueType < recordClueType");
                    String invalidReason = "存在商业险任务,此任务关闭";
                    this.closeTask(taskPO,invalidReason);
                    continue;
                }else {
                    logger.info("线索 taskClueType > recordClueType 线索关闭,recorId:{}",record.getId());
                    this.closeRecord(record);
                }
            }
            updateFlowPO = new InviteInsuranceVehicleRecordPO();
            updateFlowPO.setId(record.getId());
            updateFlowPO.setOrderStatus(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681005);
            updateFlowPO.setUpdatedAt(date);
            updateFlowPO.setUpdatedBy("low level close");
            updateFlowPOS.add(updateFlowPO);
            createFlag++; // 因子+1
        }
        logger.info("任务下发线索 createFlag:{}", createFlag);

        // 只要是命中已有线索的优先级低于待下发的优先级就需要关掉，然后尝试下发新的线索
        if (CollectionUtils.isEmpty(updateFlowPOS)) {
            logger.info("任务下发线索 isEmpty updateFlowPOS 更新（关闭）线索");
            Lists.partition(updateFlowPOS, updateSize).forEach(inviteInsuranceVehicleRecordMapper::updates);
        }
        // 因子 = records size 说明当前待下发的线索为最高级别
        // 反之说明已有线索存在最高级别，当前线索无法下发
        if (createFlag >= records.size()) {
            logger.info("任务下发线索 createFlag == records.size 下发线索");
            createRecord_(taskPO, recordPO);
        }
        logger.info("任务下发线索 createRecord end...");
    }

    //关闭线索
    private void closeRecord(InviteInsuranceVehicleRecordPO record){
        record.setOrderStatus(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681006);
        inviteInsuranceVehicleRecordMapper.updateById(record);
    }

    private void closeTask(InviteInsuranceVehicleTaskPO taskPO,String invalidReason){
        taskPO.setIsCreateInvite(2);
        taskPO.setInvalidReason(invalidReason);
        taskPO.setInviteId(taskPO.getId());
        inviteInsuranceVehicleTaskMapper.updateById(taskPO);
    }

    private void createRecord_(InviteInsuranceVehicleTaskPO po, InviteInsuranceVehicleRecordPO record) {
        logger.info("任务下发线索 createRecord_ start...");
        int row = inviteInsuranceVehicleRecordMapper.insert(record);
        logger.info("任务下发线索 record insert row:{}", row);
        if (row < 1) {
            logger.info("任务下发线索 record insert row < 1");
            return ;

        }
        logger.info("任务下发线索 task update");
        po.setIsCreateInvite(1);
        po.setInviteId(record.getId());
        row = inviteInsuranceVehicleTaskMapper.updateById(po);
        logger.info("任务下发线索 createRecord_ end task update row:{}...", row);
    }

    private void getClueType(InviteInsuranceVehicleTaskPO po, InviteInsuranceVehicleRecordPO record, Calendar c, List<InviteInsuranceVehicleRecordPO> recordArr) {
        logger.info("任务下发线索 getClueType start...");
        if (2 == po.getClueType()) {
            logger.info("任务下发线索 2 == clueType");
            //新来一条商业险 如果12个月内都是交强险
            if (!CollectionUtils.isEmpty(recordArr) && recordArr.stream().filter(r -> Objects.equals(r.getClueType() + "", "1")).count() == recordArr.size()) {
                getClueTypex(po, record, c, recordArr);

            } else {
                getClueTypexx(po, record, c, recordArr);
            }
            po.setIsCreateInvite(2);
            inviteInsuranceVehicleTaskMapper.updateById(po);
            logger.info("任务下发线索 vin已下发线索不在下发线索{}", po);
        } else {
            logger.info("任务下发线索 2 != clueType");
            po.setIsCreateInvite(2);
            inviteInsuranceVehicleTaskMapper.updateById(po);
            //如果已存在交强险线索都是关闭的 新增
            if (!CollectionUtils.isEmpty(recordArr) &&
                    recordArr.size() == recordArr.stream().filter(r -> (Objects.equals(r.getOrderStatus(), CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681005) ||
                    Objects.equals(r.getOrderStatus(), CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681006))).count()) {
                inviteInsuranceVehicleRecordMapper.insert(record);
            }
            logger.info("任务下发线索 当前线索数据库中存在重复,不插入数据库" + record);
            logger.info("任务下发线索 把当前线索对应的任务，设置成不在生成线索" + po);
        }
        logger.info("getClueType end...");
    }

    private void getClueTypexx(InviteInsuranceVehicleTaskPO po, InviteInsuranceVehicleRecordPO record, Calendar c, List<InviteInsuranceVehicleRecordPO> recordArr) {
        //如果已存在保险线索都是关闭的 新增
        if (recordArr.size() == recordArr.stream().filter(r -> (Objects.equals(r.getOrderStatus(), CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681005) ||
                Objects.equals(r.getOrderStatus(), CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681006))).count()) {
            inviteInsuranceVehicleRecordMapper.insert(record);
            //如果商业险都是关闭的并且有交强险是未关闭状态  更新交强险线索
        } else if ((recordArr.stream().filter(r -> Objects.equals(r.getClueType() + "", "2")).count() == recordArr.stream().filter(r -> ((Objects.equals(r.getOrderStatus(), CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681005) ||
                Objects.equals(r.getOrderStatus(), CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681006)) && Objects.equals(r.getClueType() + "", "2"))).count()) && foolr(recordArr)){
                InviteInsuranceVehicleRecordPO entity = recordArr.stream().filter(r -> Objects.equals(r.getClueType() + "", "1")).findFirst().orElse(recordArr.get(0));
                logger.info("商业险都是关闭---交强险是未关闭更新交强险{}", entity);
                this.updateRecordOrderStatus(po, c, entity);
        }
    }

    private void getClueTypex(InviteInsuranceVehicleTaskPO po, InviteInsuranceVehicleRecordPO record, Calendar c, List<InviteInsuranceVehicleRecordPO> recordArr) {
        List<InviteInsuranceVehicleRecordPO> list = recordArr.stream().filter(r -> Objects.equals(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681001, r.getOrderStatus()) ||
                        Objects.equals(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681002, r.getOrderStatus()) ||
                        Objects.equals(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681004, r.getOrderStatus()))
                .sorted(Comparator.comparing(InviteInsuranceVehicleRecordPO::getAdviseInDate).reversed())
                .collect(toList());
        logger.info("商业险更改交强险数据---->" + JSONObject.toJSONString(list));
        if (!CollectionUtils.isEmpty(list)) {
            InviteInsuranceVehicleRecordPO entity = list.get(0);
            //线索完成状态：已完成更改成未完成
            this.updateRecordOrderStatus(po, c, entity);
            logger.info("vin{}经销商{}更新12月内已下发交强险线索{}", entity.getVin(), entity.getDealerCode(), entity);
        } else {
            inviteInsuranceVehicleRecordMapper.insert(record);
        }
    }

    private boolean foolr(List<InviteInsuranceVehicleRecordPO> recordArr) {
        return recordArr.stream().anyMatch(r -> Objects.equals(r.getClueType() + "", "1") && (Objects.equals(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681001, r.getOrderStatus()) ||
                Objects.equals(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681002, r.getOrderStatus()) ||
                Objects.equals(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681004, r.getOrderStatus())));
    }

    private void updateRecordOrderStatus(InviteInsuranceVehicleTaskPO po, Calendar c, InviteInsuranceVehicleRecordPO entity) {
        if (Objects.equals(entity.getOrderStatus(),CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681002)) {
            entity.setOrderStatus(CommonConstants.INVITEINSURANCEVEHICLERECORD_ORDER_STATUS_83681001);
        }
        entity.setAdviseInDate(po.getAdviseInDate());
        entity.setPlanFollowDate(c.getTime());
        entity.setClueType(2);
        entity.setFollowStatus(CommonConstants.FOLLOW_STATUS_I);
        inviteInsuranceVehicleRecordMapper.updateById(entity);
    }

    /**
     * 创建新车续保任务
     *
     * @param createDate
     * @return
     */
    @Override
    public int insuranceTaskAutoCreate(String createDate) {
        logger.info("新车续保计划任务========");
        if (StringUtils.isNullOrEmpty(createDate)) {
            //如果未传时间取当前时间
            createDate = Utility.getDate();
        }
        httpLogAiService.saveHttpLog("手动创建新车续保任务","",createDate,"POST","200","手动创建新车续保任务开始","1111");
        //保险任务创建
        this.insuranceTaskCreate(createDate);
        httpLogAiService.saveHttpLog("手动创建新车续保任务","",createDate,"POST","200","手动创建新车续保任务结束","1111");

        return 1;
    }

    /**
     * 创建新车续保任务
     *
     * @param createDate
     */
    private void insuranceTaskCreate(String createDate) {
        VehicleOwnerInsurancePO insurancePO = reportCommonClient.getNewVehicleForInsurance(createDate);
        logger.info("新车续保计划任务========"+insurancePO);
        if(insurancePO != null){
            List<VehicleOwnerVO> list = insurancePO.getVehicleOwnerVOList();
            if(!CommonUtils.isNullOrEmpty(list)){
                for (VehicleOwnerVO vo : list) {
                    try {
                        if(null != vo && null != vo.getDealerCode()){
                            if(Objects.isNull(vo.getInvoiceDate())){
                                logger.error("新车生产续保任务异常,开票事件为空,vin:"+vo.getVin());
                                continue;
                            }

                            InviteInsuranceRulePO viRulePo = inviteInsuranceRuleMapper.getViInviteInsuranceRuleDlr(vo.getDealerCode());
                            InviteInsuranceRulePO clvitaRulePo = inviteInsuranceRuleMapper.getClivtaInviteInsuranceRuleDlr(vo.getDealerCode());
                            //如果不存在续保规则
                            if(null == viRulePo || null == clvitaRulePo){
                                continue;
                            }
                            Calendar c = Calendar.getInstance();
                            //基准日期
                            vo.setLastMaintainDate(vo.getInvoiceDate());
                            c.setTime(vo.getInvoiceDate());
                            //开票日期+1年
                            c.add(Calendar.YEAR, 1);
                            vo.setAdviseInDate(c.getTime());
                            this.insertInsureTaskNewVehicleData1(vo,viRulePo);
                            this.insertInsureTaskNewVehicleData2(vo,clvitaRulePo);
                        }
                    } catch (Exception e) {
                        logger.error("新车续保计划任务失败,vin:"+vo.getVin(),e);
                    }
                }
            }
        }
    }

    /**
     * 根据vin查询 车辆24个月最近两次的进厂经销商
     *
     * @param vin
     * @param insuranceDate
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    public List<String> selectRepairOrderByVin2(String vin, Date insuranceDate, String dealerCode, Integer num){
        List<String> listStr = new ArrayList<String>();
        if(num == 1){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(insuranceDate);
            calendar.add(Calendar.YEAR, -1);
            Date date = calendar.getTime();
            CheckRepairOrderDTO orderDTO = reportCommonClient.selectRepairOrderByVin2(vin,date);
            if(null != orderDTO){
                logger.info("customer--通过report查询24个月进厂数据1 ===== 新车销售：{}", orderDTO);
                if(CommonUtils.isNullOrEmpty(orderDTO.getOrderVOList())){
                    listStr.add(dealerCode);
                }else{
                    for (RepairOrderVO dto : orderDTO.getOrderVOList()){
                        listStr.add(dto.getOwnerCode());
                    }
                    listStr.add(dealerCode);
                }
            }
        }else{
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(insuranceDate);
            calendar.add(Calendar.DATE, -90);
            Date date = calendar.getTime();
            CheckRepairOrderDTO orderDTO = reportCommonClient.selectRepairOrderByVin2(vin,date);
            if(null != orderDTO){
                logger.info("customer--通过report查询24个月进厂数据2 ===== 新车销售：{}", orderDTO);
                if(CommonUtils.isNullOrEmpty(orderDTO.getOrderVOList())){
                    listStr.add(dealerCode);
                }else{
                    for (RepairOrderVO dto : orderDTO.getOrderVOList()){
                        listStr.add(dto.getOwnerCode());
                    }
                    listStr.add(dealerCode);
                }
            }
        }
        return listStr.stream().distinct().collect(Collectors.toList());
    }

    /**
     *  24个月进厂逻辑 生成续保任务  -- 新车销售 -- 商业险线索
     */
    private void insertInsureTaskNewVehicleData1(VehicleOwnerVO vo,InviteInsuranceRulePO rulePo){
        List<String> listStr1 = this.selectRepairOrderByVin2(vo.getVin(),vo.getInvoiceDate(),vo.getDealerCode(),1);
        logger.info("customer--24个月进厂逻辑 生成续保任务 ===== 新车销售的-商业险：{}", listStr1.size());
        if(CommonUtils.isNullOrEmpty(listStr1)){
            this.createViInviteTask(vo,rulePo,vo.getDealerCode());
        }else{
            Boolean existAnother = false;
            for(String code : listStr1){
                if(null != code && code.equals(vo.getDealerCode())){
                    this.createViInviteTask(vo,rulePo,vo.getDealerCode());
                }else{
                    existAnother = true;
                    this.createViInviteTask(vo,rulePo,code);
                }
            }
            if(existAnother && null != vo.getInvoiceDate()){
                List<String> listStr2 = this.selectRepairOrderByVin2(vo.getVin(),vo.getInvoiceDate(),vo.getDealerCode(),2);
                List<String> listStr3 = listStr2.stream().filter(item -> !listStr1.contains(item)).collect(toList());
                if(!CommonUtils.isNullOrEmpty(listStr3)){
                    for(String code : listStr3){
                        this.createViInviteTask(vo,rulePo,code);
                    }
                }
            }
        }
    }

    /**
     *  24个月进厂逻辑 生成续保任务  -- 新车销售 -- 交强险线索
     */
    private void insertInsureTaskNewVehicleData2(VehicleOwnerVO vo,InviteInsuranceRulePO rulePo){
        List<String> listStr1 = this.selectRepairOrderByVin2(vo.getVin(),vo.getInvoiceDate(),vo.getDealerCode(),1);
        logger.info("customer--24个月进厂逻辑 生成续保任务 ===== 新车销售的-交强险：{}", listStr1.size());
        if(CommonUtils.isNullOrEmpty(listStr1)){
            this.createClivtaInviteTask(vo,rulePo,vo.getDealerCode());
        }else{
            Boolean existAnother = false;
            for(String code : listStr1){
                if(null != code && code.equals(vo.getDealerCode())){
                    this.createClivtaInviteTask(vo,rulePo,vo.getDealerCode());
                }else{
                    existAnother = true;
                    this.createClivtaInviteTask(vo,rulePo,code);
                }
            }
            if(existAnother && null != vo.getInvoiceDate()){
                List<String> listStr2 = this.selectRepairOrderByVin2(vo.getVin(),vo.getInvoiceDate(),vo.getDealerCode(),2);
                List<String> listStr3 = listStr2.stream().filter(item -> !listStr1.contains(item)).collect(toList());
                if(!CommonUtils.isNullOrEmpty(listStr3)){
                    for(String code : listStr3){
                        this.createClivtaInviteTask(vo,rulePo,code);
                    }
                }
            }
        }
    }

    /**
     * 创建交强险邀约任务
     *
     * @param vo
     */
    private void createClivtaInviteTask(VehicleOwnerVO vo,InviteInsuranceRulePO clvitaRulePo,String dealerCode) {
        InviteInsuranceVehicleTaskPO po = new InviteInsuranceVehicleTaskPO();
        po.setVin(vo.getVin());
        po.setLicensePlateNum(vo.getPlateNumber());
        po.setDealerCode(dealerCode);
        po.setName(vo.getName());
        po.setTel(vo.getMobile());
        po.setAge(vo.getAge());
        po.setSex(vo.getSex());
        po.setModel(vo.getModelId());
       // po.setInviteType(82381003);
        po.setInviteType(CommonConstants.INVITE_TYPE_82381003);
        po.setIsCreateInvite(0);
        po.setAdviseInDate(vo.getAdviseInDate());
        po.setInviteTime(vo.getLastMaintainDate());
        po.setDataSources(3); // 数据来源
        po.setDayInAdvance(clvitaRulePo.getDayInAdvance());
        po.setRemindInterval(clvitaRulePo.getRemindInterval());
        po.setCloseInterval(clvitaRulePo.getCloseInterval());

        Calendar c = Calendar.getInstance();
        c.setTime(po.getAdviseInDate());
        if (null != clvitaRulePo.getDayInAdvance()){
            c.add(Calendar.DATE, -clvitaRulePo.getDayInAdvance());
            po.setCreateInviteTime(c.getTime());
        }else{
            c.add(Calendar.DATE, -90);
            po.setCreateInviteTime(c.getTime());
        }
       // po.setItemType(10041002);
        po.setItemType(CommonConstants.TT_INVITE_INSURANCE_VEHICLE_TASK_ITEM_TYPE_10041002);
        po.setItemCode(vo.getItemCode());
        po.setClueType(1);
      //  po.setInsuranceType(81761002);
        po.setInsuranceType(CommonConstants.TT_INVITE_INSURANCE_VEHICLE_TASK_INSURANCE_TYPE_81761002);
        inviteInsuranceVehicleTaskMapper.insert(po);
    }

    /**
     * 创建商业险邀约任务
     *
     * @param vo
     */
    private void createViInviteTask(VehicleOwnerVO vo,InviteInsuranceRulePO viRulePo,String dealerCode) {
        InviteInsuranceVehicleTaskPO po = new InviteInsuranceVehicleTaskPO();
        po.setVin(vo.getVin());
        po.setLicensePlateNum(vo.getPlateNumber());
        po.setDealerCode(vo.getDealerCode());
        po.setName(vo.getName());
        po.setTel(vo.getMobile());
        po.setAge(vo.getAge());
        po.setSex(vo.getSex());
        po.setModel(vo.getModelId());
        //po.setInviteType(82381003);
        po.setInviteType(CommonConstants.INVITE_TYPE_82381003);
        po.setIsCreateInvite(0);
        po.setAdviseInDate(vo.getAdviseInDate());
        po.setInviteTime(vo.getLastMaintainDate());
        po.setDataSources(3); // 数据来源
        po.setDayInAdvance(viRulePo.getDayInAdvance());
        po.setRemindInterval(viRulePo.getRemindInterval());
        po.setCloseInterval(viRulePo.getCloseInterval());

        Calendar c = Calendar.getInstance();
        c.setTime(po.getAdviseInDate());
        if (null != viRulePo.getDayInAdvance()){
            c.add(Calendar.DATE, -viRulePo.getDayInAdvance());
            po.setCreateInviteTime(c.getTime());
        }else{
            c.add(Calendar.DATE, -90);
            po.setCreateInviteTime(c.getTime());
        }
       // po.setItemType(10041002);
        po.setItemType(CommonConstants.TT_INVITE_INSURANCE_VEHICLE_TASK_ITEM_TYPE_10041002);
        po.setItemCode(vo.getItemCode());
        po.setClueType(2);
       // po.setInsuranceType(81761002);
        po.setInsuranceType(CommonConstants.TT_INVITE_INSURANCE_VEHICLE_TASK_INSURANCE_TYPE_81761002);
        inviteInsuranceVehicleTaskMapper.insert(po);
    }
}