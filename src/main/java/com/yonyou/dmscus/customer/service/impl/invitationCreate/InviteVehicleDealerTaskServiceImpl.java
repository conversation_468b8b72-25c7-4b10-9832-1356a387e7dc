package com.yonyou.dmscus.customer.service.impl.invitationCreate;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.google.common.collect.Lists;
import com.yonyou.cloud.common.beans.RestResultResponse;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.voc.VocInviteVehicleTaskRecordMapper;
import com.yonyou.dmscus.customer.dto.*;
import com.yonyou.dmscus.customer.enevt.EM90MessageRemindEvent;
import com.yonyou.dmscus.customer.entity.dto.faultLight.ClueInfoDeriveDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationCreate.*;
import com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleVCDCTaskImportPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocInviteVehicleTaskRecordPo;
import com.yonyou.dmscus.customer.enums.InvitationTypeEnum;
import com.yonyou.dmscus.customer.feign.DomainMaintainAuthFeign;
import com.yonyou.dmscus.customer.utils.excel.ExcelUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.service.CommonNoService;
import com.yonyou.dmscloud.framework.service.excel.ExcelRead;
import com.yonyou.dmscloud.framework.service.excel.ExcelReadCallBack;
import com.yonyou.dmscloud.framework.service.excel.impl.AbstractExcelReadCallBack;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.invitationCreate.InviteVehicleDealerTaskImportMapper;
import com.yonyou.dmscus.customer.dao.invitationCreate.InviteVehicleDealerTaskMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskImportPO;
import com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskPO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.middleInterface.ResponseDTO;
import com.yonyou.dmscus.customer.service.common.IMiddleGroundVehicleService;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.invitationCreate.InviteVehicleDealerTaskService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import com.yonyou.dmscus.customer.util.common.StringUtils;

import static com.yonyou.dmscus.customer.constants.CommonConstants.CLUE_CLOSE_WHITE;
import static com.yonyou.dmscus.customer.constants.CommonConstants.WECOM_ACCIDENT_ROSTER_TYPE_WHITE;


/**
 * <p>
 * 车辆特约店自建邀约任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-29
 */
@Service
public class InviteVehicleDealerTaskServiceImpl implements InviteVehicleDealerTaskService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteVehicleDealerTaskMapper inviteVehicleDealerTaskMapper;
    @Autowired
    CommonNoService commonNoService;
    @Resource
    InviteVehicleRecordMapper inviteVehicleRecordMapper;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    InviteVehicleDealerTaskImportMapper inviteVehicleDealerTaskImportMapper;

    @Resource
    VocInviteVehicleTaskRecordMapper vocInviteVehicleTaskRecordMapper;

    @Resource
    ExcelRead<InviteVehicleDealerTaskImportDTO> excelReadServiceIs;

    @Resource
    ExcelRead<InviteVehicleVCDCTaskImportDTO> excelReadService;
    
    @Resource
    BusinessPlatformService businessPlatformService;
    
    @Resource
    InviteVehicleRecordService inviteVehicleRecordService;
    
    @Resource
    ReportCommonClient reportCommonClient;
    
    @Resource
    IMiddleGroundVehicleService iMiddleGroundVehicleService;

    @Resource
    DomainMaintainAuthFeign domainMaintainAuthFeign;

    /**
     * 分页查询对应数据
     *
     * @param page                       分页对象
     * @param inviteVehicleDealerTaskDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto
            * . InviteVehicleDealerTaskDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<InviteVehicleDealerTaskDTO> selectPageBysql(Page page, InviteVehicleDealerTaskDTO
            inviteVehicleDealerTaskDTO) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (inviteVehicleDealerTaskDTO == null) {
            inviteVehicleDealerTaskDTO = new InviteVehicleDealerTaskDTO();
        }
        InviteVehicleDealerTaskPO inviteVehicleDealerTaskPO = inviteVehicleDealerTaskDTO.transDtoToPo
                (InviteVehicleDealerTaskPO.class);
        inviteVehicleDealerTaskPO.setOwnerCode(loginInfoDto.getOwnerCode());
        List<InviteVehicleDealerTaskPO> list = inviteVehicleDealerTaskMapper.selectPageBySql(page,
                inviteVehicleDealerTaskPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteVehicleDealerTaskDTO> result = list.stream().map(m -> m.transPoToDto
                    (InviteVehicleDealerTaskDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param inviteVehicleDealerTaskDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.InviteVehicleDealerTaskDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<InviteVehicleDealerTaskDTO> selectListBySql(InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDTO) {
        if (inviteVehicleDealerTaskDTO == null) {
            inviteVehicleDealerTaskDTO = new InviteVehicleDealerTaskDTO();
        }
        InviteVehicleDealerTaskPO inviteVehicleDealerTaskPO = inviteVehicleDealerTaskDTO.transDtoToPo
                (InviteVehicleDealerTaskPO.class);
        List<InviteVehicleDealerTaskPO> list = inviteVehicleDealerTaskMapper.selectListBySql(inviteVehicleDealerTaskPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteVehicleDealerTaskDTO.class)).collect(Collectors.toList
                    ());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.InviteVehicleDealerTaskDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public InviteVehicleDealerTaskDTO getById(Long id) {
        InviteVehicleDealerTaskPO inviteVehicleDealerTaskPO = inviteVehicleDealerTaskMapper.selectById(id);
        if (inviteVehicleDealerTaskPO != null) {
            return inviteVehicleDealerTaskPO.transPoToDto(InviteVehicleDealerTaskDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param inviteVehicleDealerTaskDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDTO) {
        //对对象进行赋值操作
        InviteVehicleDealerTaskPO inviteVehicleDealerTaskPO = inviteVehicleDealerTaskDTO.transDtoToPo
                (InviteVehicleDealerTaskPO.class);
        //执行插入
        int row = inviteVehicleDealerTaskMapper.insert(inviteVehicleDealerTaskPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                         主键ID
     * @param inviteVehicleDealerTaskDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDTO) {
        InviteVehicleDealerTaskPO inviteVehicleDealerTaskPO = inviteVehicleDealerTaskMapper.selectById(id);
        //对对象进行赋值操作
        inviteVehicleDealerTaskDTO.transDtoToPo(inviteVehicleDealerTaskPO);
        //执行更新
        int row = inviteVehicleDealerTaskMapper.updateById(inviteVehicleDealerTaskPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = inviteVehicleDealerTaskMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = inviteVehicleDealerTaskMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }


    /**
     * 店端自建邀约
     * @param inviteVehicleDealerTaskDTO
     * @return
     */
    @Override
    public int saveInvitationDlr(InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDTO) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        InviteVehicleDealerTaskPO po = inviteVehicleDealerTaskDTO.transDtoToPo(InviteVehicleDealerTaskPO.class);
        String planNo = commonNoService.getBillNo("PL", loginInfoDto.getOwnerCode());
        po.setPlanNo(planNo);
        po.setDealerCode(loginInfoDto.getOwnerCode());
        po.setCreatedName(loginInfoDto.getUserCode());
        po.setOwnerCode(loginInfoDto.getOwnerCode());
        //经销商
        po.setDataSources(CommonConstants.DATA_SOURCES_DEALER);
        //店端自建
        po.setInviteType(CommonConstants.INVITE_TYPE_X);
        inviteVehicleDealerTaskMapper.insert(po);
        List<InviteVehicleDTO> vehList = inviteVehicleDealerTaskDTO.getVehList();
        for (int i = 0; i < vehList.size(); i++) {
            InviteVehicleDTO veh = vehList.get(i);
            this.addInviteVehicleRecord(po, veh);
        }
        return 1;
    }

    /**
     * 厂端自建邀约
     * @param inviteVehicleDealerTaskDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int saveInvitationVCDC(InviteVehicleDealerTaskParamsDTO inviteVehicleDealerTaskDTO) {
        logger.info("saveInvitationVCDC,inviteVehicleDealerTaskDTO:{}", JSON.toJSONString(inviteVehicleDealerTaskDTO));
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (loginInfoDto == null){
            logger.info("saveInvitationVCDC,loginInfoDto is null");
            return 0;
        }
        Long userId = loginInfoDto.getUserId();
        String inviteName = inviteVehicleDealerTaskDTO.getInviteName();
        Date overdueCloseDate = inviteVehicleDealerTaskDTO.getOverdueCloseDate();
        String dealerCode = inviteVehicleDealerTaskDTO.getDealerCode();
        Date adviseInDate = inviteVehicleDealerTaskDTO.getAdviseInDate();
        String createdBy = userId == null ? null : userId.toString();
        checkInvitationParams(inviteName, dealerCode, adviseInDate, overdueCloseDate);
        InviteVehicleDealerTaskPO po = inviteVehicleDealerTaskDTO.transDtoToPo(InviteVehicleDealerTaskPO.class);
        String planNo = "PL" + DateUtils.dateFormat(new Date(), "yyyyMMddHHmmss");
        logger.info("saveInvitationVCDC,planNo:{}", planNo);
        po.setPlanNo(planNo);
        po.setDealerCode(dealerCode);
        po.setOwnerCode(loginInfoDto.getOwnerCode());
        po.setCreatedName(loginInfoDto.getUserCode());
        //厂端
        po.setDataSources(CommonConstants.DATA_SOURCES_VCDC);
        //厂端自建
        po.setInviteType(CommonConstants.INVITE_TYPE_XI);
        inviteVehicleDealerTaskMapper.insert(po);
        List<InviteVehicleDTO> vehList = inviteVehicleDealerTaskDTO.getVehList();
        if (CollectionUtils.isEmpty(vehList)) {
            logger.info("saveInvitationVCDC,vehList is null");
            return 0;
        }
        logger.info("saveInvitationVCDC,vehList:{}, size:{}", JSON.toJSONString(vehList), vehList.size());
        //组装参数
        List<InviteVehicleVCDCTaskImportPO> importList = new ArrayList<>(vehList.size());
        InviteVehicleVCDCTaskImportPO importPo;
        for (InviteVehicleDTO veh : vehList) {
            importPo = new InviteVehicleVCDCTaskImportPO();
            importPo.setDealerCode(dealerCode);
            importPo.setVin(veh.getVin());
            importPo.setLicensePlateNum(veh.getLicensePlateNum());
            importPo.setName(veh.getName());
            importPo.setTel(veh.getTel());
            importPo.setAdviseInDate(adviseInDate);
            importPo.setCreatedBy(createdBy);
            importPo.setInviteName(inviteName);
            importPo.setOverdueCloseDate(overdueCloseDate);
            importList.add(importPo);
        }
        logger.info("saveInvitationVCDC,importList:{}", importList.size());
        //批量新增
        addInviteRecordTask(importList, planNo);
        logger.info("saveInvitationVCDC,end");
        return 1;
    }

    private void checkInvitationParams(String inviteName, String dealerCode, Date adviseInDate, Date overdueCloseDate) {
        //参数校验,邀约名称,,逾期关闭日期,
        if (StringUtils.isNullOrEmpty(inviteName)) {
            throw new ServiceBizException("邀约名称必填");
        }
        if (StringUtils.isNullOrEmpty(dealerCode)) {
            throw new ServiceBizException("下发经销商必填");
        }
        if (adviseInDate == null) {
            throw new ServiceBizException("建议进厂日期必填");
        }
        if (overdueCloseDate == null) {
            throw new ServiceBizException("逾期关闭日期必填");
        }else if (overdueCloseDate.before(new Date())) {
            throw new ServiceBizException("逾期关闭日期必须大于当前日期");
        }
    }

    /**
     * excel导入邀约线索
     * @param importFile
     * @throws Exception
     */
    @Override
    public ImportTempResult<InviteVehicleDealerTaskImportPO> importTemp(MultipartFile importFile) throws Exception {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if(com.yonyou.dmscloud.function.utils.common.StringUtils.isNullOrEmpty(loginInfoDto.getUserId())){
            throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
        }
        List<InviteVehicleDealerTaskImportPO> addList=new ArrayList<InviteVehicleDealerTaskImportPO>();
        
        Collection<String> vinList=Sets.newHashSet();
        //删除历史数据
        inviteVehicleDealerTaskImportMapper.deleteByCreatedBy(""+loginInfoDto.getUserId());

        excelReadServiceIs.analyzeExcelFirstSheet(importFile, new AbstractExcelReadCallBack<InviteVehicleDealerTaskImportDTO>(
                InviteVehicleDealerTaskImportDTO.class, new ExcelReadCallBack<InviteVehicleDealerTaskImportDTO>() {
            private Integer seq = 1;
            @Override
            public void readRowCallBack(InviteVehicleDealerTaskImportDTO dto, boolean b) {
        		InviteVehicleDealerTaskImportPO po=new InviteVehicleDealerTaskImportPO();
        		//校验excel数据
                if(validationData(dto,po)) {
                	 po.setDealerCode(loginInfoDto.getOwnerCode());
                     po.setInviteName(dto.getInviteName());
                     po.setFollowMode(dto.getFollowMode());
                     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                     simpleDateFormat.setLenient(false);
                     po.setAdviseInDate(DateUtils.parseDateStrToDate( simpleDateFormat.format(dto.getAdviseInDate()),"yyyy-MM-dd"));
                     po.setVin(dto.getVin());
                     po.setName(dto.getName());
                     po.setTel(dto.getTel());
                     po.setIsError(0);
                     po.setLicensePlateNum(dto.getLicensePlateNum());
                     vinList.add(dto.getVin());
                }
                po.setLineNumber(++seq);
                addList.add(po);
                
            }
        }));
        if (!CommonUtils.isNullOrEmpty(addList)) {
            int listSize = addList.size();
            int toIndex = 100;
            for (int i = 0; i < addList.size(); i += toIndex) {
                if (i + toIndex > listSize) {        //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
                    toIndex = listSize - i;
                }
                List<InviteVehicleDealerTaskImportPO> insertList = addList.subList(i, i + toIndex);
                inviteVehicleDealerTaskImportMapper.bulkInsert(insertList, loginInfoDto.getUserId());
            }
        }
        return this.checkTmpData(vinList);


    }

    @Override
    @Transactional
    public ImportTempResult<InviteVehicleVCDCTaskImportPO> importInviteVehicleVCDCTemp(MultipartFile importFile) throws Exception {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        ImportTempResult<InviteVehicleVCDCTaskImportPO> importResult = new ImportTempResult<>();
        List<InviteVehicleVCDCTaskImportPO> addList = new ArrayList<>();
        // 用于存储数据唯一性校验的Map，key为数据组合，value为第一次出现的行号
        Map<String, Integer> dataUniqueMap = new HashMap<>();
        if(loginInfoDto == null || loginInfoDto.getUserId() == null){
            logger.info("importInviteVehicleVCDCTemp,loginInfoDto is null or userId is null");
            return new ImportTempResult<>();
        }
        Long userId = loginInfoDto.getUserId();
        //开始时间
        long startTime = System.currentTimeMillis();
        //删除历史数据
        inviteVehicleDealerTaskImportMapper.deleteByCreatedBy(String.valueOf(userId));
        //结束时间
        long endTime = System.currentTimeMillis();
        logger.info("importInviteVehicleVCDCTemp,deleteByCreatedBy,use time:{}", endTime - startTime);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        startTime = System.currentTimeMillis();
        //解析excel
        getListByExcel(importFile, sdf, dataUniqueMap, addList);
        endTime = System.currentTimeMillis();
        logger.info("importInviteVehicleVCDCTemp,readRowCallBack,use time:{}", endTime - startTime);
        if(addList.size() > 10000){
            logger.info("importInviteVehicleVCDCTemp,addList size is 10000");
            importResult.setErrorMsg("您选择的文件超出1万行数据，文件读入失败!");
            return importResult;
        }
        startTime = System.currentTimeMillis();
        //校验vin码
        checkVCDCTmpData(addList);
        //添加数据到临时表
        if (!CommonUtils.isNullOrEmpty(addList)) {
            Lists.partition(addList, 2000).forEach(batchList -> {
                try {
                    inviteVehicleDealerTaskImportMapper.allInsert(batchList, userId);
                } catch (Exception e) {
                    logger.error("批量插入失败，批次大小: {}", batchList.size(), e);
                    throw new ServiceBizException("批量插入数据失败");
                }
            });
        }
        endTime = System.currentTimeMillis();
        logger.info("importInviteVehicleVCDCTemp,bulkInsert,use time:{}", endTime - startTime);
        //返回数据
        List<InviteVehicleVCDCTaskImportPO> errorList = addList.stream().filter(m -> m.getIsError() == 1).collect(Collectors.toList());
        List<InviteVehicleVCDCTaskImportPO> successList = addList.stream().filter(m -> m.getIsError() == 0).collect(Collectors.toList());
        importResult.setErrorList(errorList);
        importResult.setSuccessList(successList);
        importResult.setSuccessCount(CollectionUtils.isEmpty(successList) ? 0 : successList.size());
        return importResult;
    }

    private void getListByExcel(MultipartFile importFile, SimpleDateFormat sdf, Map<String, Integer> dataUniqueMap, List<InviteVehicleVCDCTaskImportPO> addList) throws IOException {
        excelReadService.analyzeExcelFirstSheet(importFile, new AbstractExcelReadCallBack<>(
                InviteVehicleVCDCTaskImportDTO.class, new ExcelReadCallBack<InviteVehicleVCDCTaskImportDTO>() {
            private Integer seq = 1;
            @Override
            public void readRowCallBack(InviteVehicleVCDCTaskImportDTO dto, boolean b) {
                InviteVehicleVCDCTaskImportPO po = new InviteVehicleVCDCTaskImportPO();
                //拼接key,做重复对比
                String dataKey = buildDataUniqueKey(dto, sdf);
                //校验excel数据
                if (validationVCDCData(dto, po, dataUniqueMap, dataKey, seq)) {
                    po.setIsError(0);
                }
                po.setInviteName(dto.getInviteName());
                sdf.setLenient(false);
                po.setAdviseInDate(DateUtils.parseDateStrToDate(sdf.format(dto.getAdviseInDate()), "yyyy-MM-dd"));
                po.setVin(dto.getVin());
                po.setDealerCode(dto.getDealerCode());
                po.setName(dto.getName());
                po.setTel(dto.getTel());
                po.setLicensePlateNum(dto.getLicensePlateNum());
                po.setLineNumber(++seq);
                addList.add(po);
            }
        }));
        dataUniqueMap.clear();
    }

    /**
     * 构建数据唯一性校验的key
     * 包含：邀约名称、经销商代码、建议进厂日期、VIN、车牌号、客户名称、联系方式
     * @param dto
     * @return
     */
    private String buildDataUniqueKey(InviteVehicleVCDCTaskImportDTO dto, SimpleDateFormat sdf) {
        StringBuilder keyBuilder = new StringBuilder();
        // 邀约名称
        keyBuilder.append(dto.getInviteName() != null ? dto.getInviteName().trim() : "").append("|");
        // 经销商代码
        keyBuilder.append(dto.getDealerCode() != null ? dto.getDealerCode().trim() : "").append("|");
        // 建议进厂日期
        if (dto.getAdviseInDate() != null) {
            keyBuilder.append(sdf.format(dto.getAdviseInDate()));
        }
        keyBuilder.append("|");
        // VIN
        keyBuilder.append(dto.getVin() != null ? dto.getVin().trim() : "").append("|");
        // 车牌号
        keyBuilder.append(dto.getLicensePlateNum() != null ? dto.getLicensePlateNum().trim() : "").append("|");
        // 客户名称
        keyBuilder.append(dto.getName() != null ? dto.getName().trim() : "").append("|");
        // 联系方式
        keyBuilder.append(dto.getTel() != null ? dto.getTel().trim() : "");
        // 逾期日期
        if (dto.getOverdueCloseDate() != null) {
            keyBuilder.append(sdf.format(dto.getOverdueCloseDate()));
        }
        keyBuilder.append("|");
        return keyBuilder.toString();
    }

    private void checkVCDCTmpData(List<InviteVehicleVCDCTaskImportPO> addList) {
        long startTime = System.currentTimeMillis();
        List<String> vinList = addList.stream()
                .filter(Objects::nonNull)
                .filter(po -> Objects.nonNull(po.getVin()) && !po.getVin().trim().isEmpty())
                .map(InviteVehicleVCDCTaskImportPO::getVin)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vinList)) {
            logger.info("checkVCDCTmpData,vinList isEmpty");
            return;
        }
        logger.info("checkVCDCTmpData, 调用中台地址查询VIN是否存在（开始),vinList:{}", vinList.size());
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("vinList", vinList);
        logger.info("checkVCDCTmpData,size:{}", vinList.size());
        ResponseDTO<Map<String, Object>> response = iMiddleGroundVehicleService.getVinListCheckInfo(paramMap);
        Map<String, Object> notExistVinMap = response.getData();
        List<String> list = null;
        if (notExistVinMap != null && CollectionUtils.isNotEmpty(notExistVinMap.keySet())) {
            list = new ArrayList<>(notExistVinMap.keySet());
            logger.info("notExistVinMap is not null and not empty,size:{}",list.size());
        }
        if(CommonUtils.isNullOrEmpty(list)){
            logger.info("checkVCDCTmpData,list is empty");
            return;
        }
        for (InviteVehicleVCDCTaskImportPO po : addList) {
            if(list.contains(po.getVin())){
                po.appendErrorMsg("VIN不存在");
            }
        }
        long endTime = System.currentTimeMillis();
        logger.info("checkVCDCTmpData,use time:{}", endTime - startTime);
    }


    /**
     * 校验导入excel数据
     * @param dto
     * @param po
     * @return
     */
    private boolean validationVCDCData(InviteVehicleVCDCTaskImportDTO dto,InviteVehicleVCDCTaskImportPO po, Map<String, Integer> dataUniqueMap, String dataKey, Integer seq) {
        boolean isOk=true;
        if(StringUtils.isNullOrEmpty(dto.getDealerCode())) {
            po.appendErrorMsg("经销商代码不能为空");
            isOk=false;
        }
        if(StringUtils.isNullOrEmpty(dto.getInviteName())||dto.getInviteName().length()>20) {
            po.appendErrorMsg("邀约名称不能为空并且长度不能超过20");
            isOk=false;
        }
        if(StringUtils.isNullOrEmpty(dto.getAdviseInDate())) {
            po.appendErrorMsg("建议进厂日期格式错误,正确格式 yyyy-MM-dd或yyyy/MM/dd");
            isOk=false;
        }
        if(StringUtils.isNullOrEmpty(dto.getVin()) || dto.getVin().length()>17) {
            po.appendErrorMsg("VIN不能为空并且长度不能超过17");
            isOk=false;
        }
        if(StringUtils.isNullOrEmpty(dto.getName()) || dto.getName().length()>50) {
            po.appendErrorMsg("客户姓名不能为空并且长度不能超过50");
            isOk=false;
        }
        if(StringUtils.isNullOrEmpty(dto.getTel()) || dto.getTel().length()>20) {
            po.appendErrorMsg("联系方式不能为空并且长度不能超过20");
            isOk=false;
        }
        if(!StringUtils.isNullOrEmpty(dto.getLicensePlateNum()) && dto.getLicensePlateNum().length() > 15) {
            po.appendErrorMsg("车牌号长度不能超过15");
            isOk=false;
        }
        if(StringUtils.isNullOrEmpty(dto.getOverdueCloseDate())) {
            po.appendErrorMsg("未输入逾期关闭日期");
            isOk=false;
        }else if(dto.getOverdueCloseDate().before(new Date())) {
            po.appendErrorMsg("逾期关闭日期必须大于当前日期");
            isOk=false;
        }
        // 校验数据重复性
        if (dataUniqueMap.containsKey(dataKey)) {
            Integer firstLineNumber = dataUniqueMap.get(dataKey);
            po.appendErrorMsg(String.format("第%d行数据与第%d行数据完全重复", seq, firstLineNumber));
        } else {
            dataUniqueMap.put(dataKey, seq);
        }
        return isOk;
    }

    /**
                * 校验导入excel数据
     * @param dto
     * @param po
     * @return
     */
    private boolean validationData(InviteVehicleDealerTaskImportDTO dto,InviteVehicleDealerTaskImportPO po) {
    	boolean isOk=true;
    	if(StringUtils.isNullOrEmpty(dto.getInviteName())||dto.getInviteName().length()>20) {
    		po.setErrorMsg("邀约名称不能为空并且长度不能超过20");
    		po.setIsError(1);
    		isOk=false;
    	}
    	if(StringUtils.isNullOrEmpty(dto.getFollowMode())) {
    		po.setErrorMsg("跟进方式不能为空");
    		po.setIsError(1);
    		isOk=false;
    	}
    	if(!"电话".equals(dto.getFollowMode()) && !"短信".equals(dto.getFollowMode())
    			&& !"沃世界".equals(dto.getFollowMode())&& !"微信".equals(dto.getFollowMode())	
    			&& !"问卷".equals(dto.getFollowMode())&& !"邮件".equals(dto.getFollowMode())
    			&& !"QQ".equals(dto.getFollowMode())&& !"其他".equals(dto.getFollowMode())
    			) {
    		po.setErrorMsg("跟进方式只能是'电话，短信，沃世界，微信，问卷，邮件，QQ，其他'");
    		po.setIsError(1);
    		isOk=false;
    	}
    	if(StringUtils.isNullOrEmpty(dto.getAdviseInDate())) {
    		po.setErrorMsg("建议进厂日期格式错误,正确格式 yyyy-MM-dd或yyyy/MM/dd");
    		po.setIsError(1);
    		isOk=false;
    	}
    	if(StringUtils.isNullOrEmpty(dto.getVin()) || dto.getVin().length()>17) {
    		po.setErrorMsg("VIN不能为空并且长度不能超过17");
    		po.setIsError(1);
    		isOk=false;
    	}
    	if(StringUtils.isNullOrEmpty(dto.getName()) || dto.getName().length()>50) {
    		po.setErrorMsg("客户姓名不能为空并且长度不能超过50");
    		po.setIsError(1);
    		isOk=false;
    	}
    	if(StringUtils.isNullOrEmpty(dto.getTel()) || dto.getTel().length()>20) {
    		po.setErrorMsg("联系方式不能为空并且长度不能超过20");
    		po.setIsError(1);
    		isOk=false;
    	}
    	if(!StringUtils.isNullOrEmpty(dto.getLicensePlateNum()) && dto.getLicensePlateNum().length() > 15) {
    		po.setErrorMsg("车牌号长度不能超过15");
    		po.setIsError(1);
    		isOk=false;
    	}
    	return isOk;
    }

   
    /**
     * 导入
     */
    @Override
    public void batchInsert() {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //不生成单号
        //String planNo = commonNoService.getBillNo("PL", loginInfoDto.getOwnerCode());
        String planNo ="";
                //主单
        inviteVehicleDealerTaskMapper.insertInvitationDlr(planNo,loginInfoDto.getUserCode(),loginInfoDto.getUserId());
        //明细
        inviteVehicleRecordMapper.batchInsert(loginInfoDto.getUserId());
        
        //调取中台接口获取oneid的数据
        List<InviteVehicleDealerTaskImportPO> listSuccess = inviteVehicleDealerTaskImportMapper.querySuccess(loginInfoDto.getUserId());
        List<CustomerInfoCenterDTO> param = new ArrayList<>();
	    if(!CommonUtils.isNullOrEmpty(listSuccess) && listSuccess.size() > 0) {
	    	for(InviteVehicleDealerTaskImportPO po:listSuccess) {
	    		CustomerInfoCenterDTO cus = new CustomerInfoCenterDTO();
	    		cus.setMobile(po.getTel());
	    		cus.setName(po.getName());
	    		param.add(cus);
	    	}
            if(!CommonUtils.isNullOrEmpty(param) && param.size() > 0) {
                List<CustomerInfoListReturnDTO> oneIdList = businessPlatformService.getOneId(param);
                for (CustomerInfoListReturnDTO dto:oneIdList) {
                    inviteVehicleRecordService.updateOneIdByMobile(dto);
                }
            }
	    }
        //删除历史数据
        inviteVehicleDealerTaskImportMapper.deleteByCreatedBy(""+loginInfoDto.getUserId());
    }

    /**
     * 厂端自建导入
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchVCDCInsert() {
        logger.info("batchVCDCInsert,start");
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if(loginInfoDto == null || loginInfoDto.getUserCode() == null || loginInfoDto.getUserId() == null){
            logger.info("batchVCDCInsert,loginInfoDto is null or userCode is null or userId is null");
            return;
        }
        String userCode = loginInfoDto.getUserCode();
        // 此问题已存在2年，生产的数据由运维手动导入，导入的sql并没有planNo（生产数据也存在空），故此可以用PL前缀+时间戳
        String planNo = "PL" + DateUtils.dateFormat(new Date(), "yyyyMMddHHmmss");
        logger.info("batchVCDCInsert,planNo:{}", planNo);
        //主单
        inviteVehicleDealerTaskMapper.insertInvitationVCDC(planNo,loginInfoDto.getUserCode(),loginInfoDto.getUserId()
                ,loginInfoDto.getOwnerCode());
        //查询出表的数据
        List<InviteVehicleVCDCTaskImportPO> importList = inviteVehicleDealerTaskImportMapper.selectByCreatedBy(loginInfoDto.getUserId());
        if (CollectionUtils.isEmpty(importList)) {
            logger.info("batchVCDCInsert,importList is null");
            return;
        }
        addInviteRecordTask(importList, planNo);
        //删除历史数据
        inviteVehicleDealerTaskImportMapper.deleteByCreatedBy(String.valueOf(loginInfoDto.getUserId()));
        logger.info("batchVCDCInsert,end");
    }

    private void addInviteRecordTask(List<InviteVehicleVCDCTaskImportPO> importList, String planNo) {
        long startTime = System.currentTimeMillis();
        List<InviteVehicleRecordPO> mainRecordList = new ArrayList<>(importList.size());
        InviteVehicleRecordPO mainRecord;
        for (InviteVehicleVCDCTaskImportPO importDO : importList) {
            mainRecord = new InviteVehicleRecordPO();
            mainRecord.setAppId("volvo");
            mainRecord.setOwnerCode(importDO.getDealerCode());
            mainRecord.setSourceType(2);
            mainRecord.setVin(importDO.getVin());
            mainRecord.setLicensePlateNum(importDO.getLicensePlateNum());
            mainRecord.setName(importDO.getName());
            mainRecord.setTel(importDO.getTel());
            mainRecord.setInviteType(InvitationTypeEnum.OEM_CREATED.getCode());
            mainRecord.setAdviseInDate(importDO.getAdviseInDate());
            mainRecord.setFollowStatus(CommonConstants.FOLLOW_STATUS_I);
            mainRecord.setOrderStatus(CommonConstants.ORDER_STATUS_II);
            mainRecord.setCreatedBy(importDO.getCreatedBy());
            mainRecord.setCreatedAt(new Date());
            mainRecord.setRecordVersion(1);
            mainRecord.setDealerCode(importDO.getDealerCode());
            mainRecord.setItemName(importDO.getInviteName());
            mainRecordList.add(mainRecord);
        }
        inviteVehicleRecordMapper.batchInsertMainRecord(mainRecordList);
        List<VocInviteVehicleTaskRecordPo> extRecordList = new ArrayList<>(mainRecordList.size());
        InviteVehicleRecordPO mrd;
        InviteVehicleVCDCTaskImportPO importDO;
        VocInviteVehicleTaskRecordPo extRecord;
        for (int i = 0; i < mainRecordList.size(); i++) {
            mrd = mainRecordList.get(i);
            importDO = importList.get(i);
            extRecord = new VocInviteVehicleTaskRecordPo();
            extRecord.setVin(mrd.getVin());
            extRecord.setRecordId(mrd.getId());
            extRecord.setInviteType(InvitationTypeEnum.OEM_CREATED.getCode());
            extRecord.setCreatedBy(importDO.getCreatedBy());
            extRecord.setOverdueCloseDate(importDO.getOverdueCloseDate());
            extRecord.setPlanNo(planNo);
            extRecordList.add(extRecord);
        }
        vocInviteVehicleTaskRecordMapper.batchInsertExtRecord(extRecordList);
        long endTime = System.currentTimeMillis();
        logger.info("batchVCDCInsert,use time:{}", endTime - startTime);
        //新增邀约名称
        List<String> listName = importList.stream().map(InviteVehicleVCDCTaskImportPO::getInviteName).distinct().collect(Collectors.toList());
        logger.info("batchVCDCInsert,listName:{}", listName.size());
        inviteVehicleDealerTaskMapper.batchInsertInviteName(listName);
    }


    /**
     * 校验更新临时表，返回校验结果对象
     *
     * @return
     * <AUTHOR>
     * @since 2020/03/19
     */
    private ImportTempResult<InviteVehicleDealerTaskImportPO> checkTmpData(Collection<String> vinList) {
        ImportTempResult<InviteVehicleDealerTaskImportPO> importResult = new ImportTempResult<InviteVehicleDealerTaskImportPO>();
        // 获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //校验vin是否存在
//        List<Integer>  list=reportCommonClient.selectNotVehicleByVin(Integer.parseInt(loginInfoDto.getUserId()+""));
//        logger.info("查询VIN返回数据接口开始。。。。。。。。。");
//        if(!CommonUtils.isNullOrEmpty(list) && list.size() > 0) {
//        	for(int i=0;i<list.size();i++) {
//             	logger.info("查询VIN返回数据。。。。。。。。。："+list.get(i));
//             	
//            }
//        	inviteVehicleDealerTaskImportMapper.updateErrorById(list);
//        }
        //中台验证车架号的数据准备
        if(!CommonUtils.isNullOrEmpty(vinList)) {
        	logger.info("===调用中台地址查询VIN是否存在（开始）====");
        	Map<String, Object> paramMap = new HashMap<String,Object>();
            paramMap.put("vinList", vinList);
            ResponseDTO<Map<String, Object>> response=iMiddleGroundVehicleService.getVinListCheckInfo(paramMap);
            Map<String,Object> notExistVinMap = response.getData();
            if(notExistVinMap != null && CollectionUtils.isNotEmpty(notExistVinMap.keySet())) {
            	logger.info("返回1："+JSON.toJSONString(response));
            	Map<String,Object> result= response.getData();
            	List<String> list=new ArrayList<String>();
            	if(!CommonUtils.isNullOrEmpty(result) && result.size()>0) {
            		list=getKeyListBySet(result.keySet());
            	}
            	if(!CommonUtils.isNullOrEmpty(list) && list.size() > 0) {
      			   inviteVehicleDealerTaskImportMapper.updateErrorById(loginInfoDto.getUserId(),list);
        		}
//            	if(notExistVinMap.get("notExistVinList")!=null) {
//            		logger.info("返回2："+JSON.toJSONString(notExistVinMap.get("notExistVinList")));
//            		List<Long> list=objToList(notExistVinMap.get("notExistVinList"));
//            		
//            	};
            	
            }
            
            
            logger.info("===调用中台地址查询VIN是否存在（结束）====");
        }
        
        
       
        
       
        
        
        //查询错误项
        List<InviteVehicleDealerTaskImportPO> listError = inviteVehicleDealerTaskImportMapper.queryError(loginInfoDto.getUserId());
        if (!CommonUtils.isNullOrEmpty(listError)) {
            importResult.setErrorList(listError);
        } else {
            logger.info("未查到错误数据！");
        }
        // 查询成功项
        List<InviteVehicleDealerTaskImportPO> listSuccess = inviteVehicleDealerTaskImportMapper.querySuccess(loginInfoDto.getUserId());
        if (!CommonUtils.isNullOrEmpty(listSuccess)) {
            importResult.setSuccessList(listSuccess);
        } else {
            //inviteVehicleDealerTaskImportMapper.deleteByCreatedBy(loginInfoDto.getUserId()+"");
            logger.info("未查到正确数据！");
        }
        // 查询正确数据数
        importResult.setSuccessCount(inviteVehicleDealerTaskImportMapper.querySucessCount(loginInfoDto.getUserId()));
        return importResult;
    }

	
    /**
	 * 根据key的set返回key的list
	 * 
	 * @param set
	 * @return
	 */
	public  List<String> getKeyListBySet(Set<String> set) {
		List<String> keyList = new ArrayList<String>();
		keyList.addAll(set);
		return keyList;
	}
    
 
	
    private   List<Long> objToList(Object obj){
        List<Long> result = new ArrayList<>();
        if (obj instanceof ArrayList<?>) {
            for (Object o : (List<?>) obj) {
                result.add(Long.parseLong(String.class.cast(o)) );
            }
        }
        result.forEach(System.out::println); // 输出：1 ab
        return result;
	}

    /**
     * 新增邀约线索
     *
     * @param po
     * @return
     */
    private void addInviteVehicleRecord(InviteVehicleDealerTaskPO po, InviteVehicleDTO veh) {
        InviteVehicleRecordDTO record = new InviteVehicleRecordDTO();
        //主线索
        record.setIsMain(1);
        //自建邀约
        record.setSourceType(2);
        record.setVin(veh.getVin());
        record.setLicensePlateNum(veh.getLicensePlateNum());
        record.setName(veh.getName());
        record.setTel(veh.getTel());
        record.setInviteType(po.getInviteType());
        record.setAdviseInDate(po.getAdviseInDate());
        //未跟进
        record.setFollowStatus(82401001);
        //未完成
        record.setOrderStatus(82411002);
        record.setSaId(veh.getSaId());
        record.setSaName(veh.getSaName());
        record.setDealerCode(po.getDealerCode());
        record.setDataSources(po.getDataSources());
        //对对象进行赋值操作
        InviteVehicleRecordPO inviteVehicleRecordPO = record.transDtoToPo(InviteVehicleRecordPO.class);
        //执行插入
        inviteVehicleRecordMapper.insert(inviteVehicleRecordPO);
        po.setInviteType(inviteVehicleRecordPO.getInviteType());
        po.setInviteId(inviteVehicleRecordPO.getId());
    }

	@Override
	public IPage<InviteVehicleDealerImportDTO> selectErrorPage(Page page) {
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		if(StringUtils.isNullOrEmpty(loginInfoDto.getUserId())){
			throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
		}
		List<InviteVehicleDealerTaskImportPO>  list=inviteVehicleDealerTaskImportMapper.selectErrorPage(page,loginInfoDto.getUserId());
		if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteVehicleDealerImportDTO> result = list.stream().map(m -> m.transPoToDto
                    (InviteVehicleDealerImportDTO.class)).collect(Collectors.toList());

            page.setRecords(list);
            return page;
        }
	}

	@Override
	public IPage<InviteVehicleDealerImportDTO> selectSuccessPage(Page page) {
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		if(StringUtils.isNullOrEmpty(loginInfoDto.getUserId())){
			throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
		}
		List<InviteVehicleDealerTaskImportPO>  list=inviteVehicleDealerTaskImportMapper.selectSuccessPage(page,loginInfoDto.getUserId());
		if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteVehicleDealerImportDTO> result = list.stream().map(m -> m.transPoToDto
                    (InviteVehicleDealerImportDTO.class)).collect(Collectors.toList());

            page.setRecords(list);
            return page;
        }
	}

    @Override
    public List<String> selectInviteName(String inviteName) {
        logger.info("selectInviteName,inviteName:{}", inviteName);
        List<String> list = inviteVehicleDealerTaskMapper.selectInviteName(inviteName);
        if (CollectionUtils.isEmpty(list)){
            logger.info("selectInviteName,end,list is null");
            return Collections.emptyList();
        }
        logger.info("selectInviteName,end,size:{}", list.size());
        return list;
    }

    @Override
    public Boolean cluesClose(List<Long> ids) {
        logger.info("cluesClose,id:{}", ids);
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (loginInfoDto == null){
            logger.info("cluesClose,end,loginInfoDto is null");
            return false;
        }
        String ownerCode = loginInfoDto.getOwnerCode();
        logger.info("cluesClose,ownerCode:{}", ownerCode);
        if (ownerCode == null){
            logger.info("cluesClose,end,ownerCode is null");
            return false;
        }
        //白名单校验
        RestResultResponse<Boolean> res = domainMaintainAuthFeign.checkWhitelist(ownerCode,CLUE_CLOSE_WHITE,WECOM_ACCIDENT_ROSTER_TYPE_WHITE,"", null);
        logger.info("cluesClose,res:{}", JSON.toJSONString(res));
        Boolean flag = false;
        if(res != null && res.getData() != null){
            flag = res.getData();
        }
        if(!flag){
            logger.info("cluesClose,end,flag is false");
            return false;
        }
        //手动关闭线索
        int num = inviteVehicleRecordMapper.closeClueById(ids);
        logger.info("cluesClose,end,num:{}", num);
        return num == 1;
    }

    @Override
    public IPage<queryImportDetailsDto> queryImportDetails(InviteTaskQueryParamsDto dto) {
        logger.info("queryImportDetails,dto:{}", dto);
        Page<queryImportDetailsDto> page = new Page<>(dto.getCurrentPage(), dto.getPageSize());
        List<queryImportDetailsDto> list = inviteVehicleRecordMapper.queryImportDetails(page, dto);
        page.setRecords(list);
        logger.info("queryImportDetails,end");
        return page;
    }

    @Override
    public void importDetails(InviteTaskQueryParamsDto dto, HttpServletResponse response) {
        logger.info("importDetails,dto:{}",dto);
        List<queryImportDetailsDto> list = inviteVehicleRecordMapper.queryImportDetails(null,dto);
        ExcelUtils.export(response, "厂端自建线索导入明细", list, queryImportDetailsDto.class);
    }


}
