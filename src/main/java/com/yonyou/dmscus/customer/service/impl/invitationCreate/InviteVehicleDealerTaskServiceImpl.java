package com.yonyou.dmscus.customer.service.impl.invitationCreate;


import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.google.common.collect.Lists;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dto.*;
import com.yonyou.dmscus.customer.enevt.EM90MessageRemindEvent;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.service.CommonNoService;
import com.yonyou.dmscloud.framework.service.excel.ExcelRead;
import com.yonyou.dmscloud.framework.service.excel.ExcelReadCallBack;
import com.yonyou.dmscloud.framework.service.excel.impl.AbstractExcelReadCallBack;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.invitationCreate.InviteVehicleDealerTaskImportMapper;
import com.yonyou.dmscus.customer.dao.invitationCreate.InviteVehicleDealerTaskMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.invitationCreate.InviteVehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationCreate.InviteVehicleDealerImportDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationCreate.InviteVehicleDealerTaskDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskImportPO;
import com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskPO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.middleInterface.ResponseDTO;
import com.yonyou.dmscus.customer.service.common.IMiddleGroundVehicleService;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.invitationCreate.InviteVehicleDealerTaskService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.util.common.DateUtils;
import com.yonyou.dmscus.customer.util.common.StringUtils;


/**
 * <p>
 * 车辆特约店自建邀约任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-29
 */
@Service
public class InviteVehicleDealerTaskServiceImpl implements InviteVehicleDealerTaskService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InviteVehicleDealerTaskMapper inviteVehicleDealerTaskMapper;
    @Autowired
    CommonNoService commonNoService;
    @Resource
    InviteVehicleRecordMapper inviteVehicleRecordMapper;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    InviteVehicleDealerTaskImportMapper inviteVehicleDealerTaskImportMapper;

    @Resource
    ExcelRead<InviteVehicleDealerTaskImportDTO> excelReadServiceIs;

    @Resource
    ExcelRead<InviteVehicleVCDCTaskImportDTO> excelReadService;
    
    @Resource
    BusinessPlatformService businessPlatformService;
    
    @Resource
    InviteVehicleRecordService inviteVehicleRecordService;
    
    @Resource
    ReportCommonClient reportCommonClient;
    
    @Resource
    IMiddleGroundVehicleService iMiddleGroundVehicleService;

    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    /**
     * 分页查询对应数据
     *
     * @param page                       分页对象
     * @param inviteVehicleDealerTaskDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto
            * . InviteVehicleDealerTaskDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<InviteVehicleDealerTaskDTO> selectPageBysql(Page page, InviteVehicleDealerTaskDTO
            inviteVehicleDealerTaskDTO) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if (inviteVehicleDealerTaskDTO == null) {
            inviteVehicleDealerTaskDTO = new InviteVehicleDealerTaskDTO();
        }
        InviteVehicleDealerTaskPO inviteVehicleDealerTaskPO = inviteVehicleDealerTaskDTO.transDtoToPo
                (InviteVehicleDealerTaskPO.class);
        inviteVehicleDealerTaskPO.setOwnerCode(loginInfoDto.getOwnerCode());
        List<InviteVehicleDealerTaskPO> list = inviteVehicleDealerTaskMapper.selectPageBySql(page,
                inviteVehicleDealerTaskPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteVehicleDealerTaskDTO> result = list.stream().map(m -> m.transPoToDto
                    (InviteVehicleDealerTaskDTO.class)).collect(Collectors.toList());

            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param inviteVehicleDealerTaskDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.InviteVehicleDealerTaskDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<InviteVehicleDealerTaskDTO> selectListBySql(InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDTO) {
        if (inviteVehicleDealerTaskDTO == null) {
            inviteVehicleDealerTaskDTO = new InviteVehicleDealerTaskDTO();
        }
        InviteVehicleDealerTaskPO inviteVehicleDealerTaskPO = inviteVehicleDealerTaskDTO.transDtoToPo
                (InviteVehicleDealerTaskPO.class);
        List<InviteVehicleDealerTaskPO> list = inviteVehicleDealerTaskMapper.selectListBySql(inviteVehicleDealerTaskPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteVehicleDealerTaskDTO.class)).collect(Collectors.toList
                    ());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.InviteVehicleDealerTaskDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public InviteVehicleDealerTaskDTO getById(Long id) {
        InviteVehicleDealerTaskPO inviteVehicleDealerTaskPO = inviteVehicleDealerTaskMapper.selectById(id);
        if (inviteVehicleDealerTaskPO != null) {
            return inviteVehicleDealerTaskPO.transPoToDto(InviteVehicleDealerTaskDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param inviteVehicleDealerTaskDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDTO) {
        //对对象进行赋值操作
        InviteVehicleDealerTaskPO inviteVehicleDealerTaskPO = inviteVehicleDealerTaskDTO.transDtoToPo
                (InviteVehicleDealerTaskPO.class);
        //执行插入
        int row = inviteVehicleDealerTaskMapper.insert(inviteVehicleDealerTaskPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                         主键ID
     * @param inviteVehicleDealerTaskDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDTO) {
        InviteVehicleDealerTaskPO inviteVehicleDealerTaskPO = inviteVehicleDealerTaskMapper.selectById(id);
        //对对象进行赋值操作
        inviteVehicleDealerTaskDTO.transDtoToPo(inviteVehicleDealerTaskPO);
        //执行更新
        int row = inviteVehicleDealerTaskMapper.updateById(inviteVehicleDealerTaskPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = inviteVehicleDealerTaskMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = inviteVehicleDealerTaskMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }


    /**
     * 店端自建邀约
     * @param inviteVehicleDealerTaskDTO
     * @return
     */
    @Override
    public int saveInvitationDlr(InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDTO) {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        InviteVehicleDealerTaskPO po = inviteVehicleDealerTaskDTO.transDtoToPo(InviteVehicleDealerTaskPO.class);
        String planNo = commonNoService.getBillNo("PL", loginInfoDto.getOwnerCode());
        po.setPlanNo(planNo);
        po.setDealerCode(loginInfoDto.getOwnerCode());
        po.setCreatedName(loginInfoDto.getUserCode());
        po.setOwnerCode(loginInfoDto.getOwnerCode());
        //经销商
        po.setDataSources(CommonConstants.DATA_SOURCES_DEALER);
        //店端自建
        po.setInviteType(CommonConstants.INVITE_TYPE_X);
        inviteVehicleDealerTaskMapper.insert(po);
        List<InviteVehicleDTO> vehList = inviteVehicleDealerTaskDTO.getVehList();
        for (int i = 0; i < vehList.size(); i++) {
            InviteVehicleDTO veh = vehList.get(i);
            this.addInviteVehicleRecord(po, veh);
        }
        return 1;
    }

    /**
     * 厂端自建邀约
     * @param inviteVehicleDealerTaskDTO
     * @return
     */
    @Override
    public int saveInvitationVCDC(InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDTO) {
        logger.info("saveInvitationVCDC,inviteVehicleDealerTaskDTO:{}", JSON.toJSONString(inviteVehicleDealerTaskDTO));
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        InviteVehicleDealerTaskPO po = inviteVehicleDealerTaskDTO.transDtoToPo(InviteVehicleDealerTaskPO.class);
        String planNo = "PL" + DateUtils.dateFormat(new Date(), "yyyyMMddHHmmss");
        logger.info("planNo:{}", planNo);
        po.setPlanNo(planNo);
        po.setDealerCode(inviteVehicleDealerTaskDTO.getDealerCode());
        po.setOwnerCode(loginInfoDto.getOwnerCode());
        po.setCreatedName(loginInfoDto.getUserCode());
        //厂端
        po.setDataSources(CommonConstants.DATA_SOURCES_VCDC);
        //厂端自建
        po.setInviteType(CommonConstants.INVITE_TYPE_XI);
        inviteVehicleDealerTaskMapper.insert(po);
        List<InviteVehicleDTO> vehList = inviteVehicleDealerTaskDTO.getVehList();
        for (int i = 0; i < vehList.size(); i++) {
            InviteVehicleDTO veh = vehList.get(i);
            this.addInviteVehicleRecord(po, veh);
        }
        inviteVehicleDealerTaskDTO.setInviteType(po.getInviteType());
        inviteVehicleDealerTaskDTO.setInviteId(po.getInviteId());
        logger.info("saveInvitationVCDC,add,inviteVehicleDealerTaskDTO:{}", JSON.toJSONString(inviteVehicleDealerTaskDTO));
        //EM90 message remind on 2024-02-22
        applicationEventPublisher.publishEvent(new EM90MessageRemindEvent(new EM90MessageRemindDto(inviteVehicleDealerTaskDTO,null,null)));

        return 1;
    }

    /**
     * excel导入邀约线索
     * @param importFile
     * @throws Exception
     */
    @Override
    public ImportTempResult<InviteVehicleDealerTaskImportPO> importTemp(MultipartFile importFile) throws Exception {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if(com.yonyou.dmscloud.function.utils.common.StringUtils.isNullOrEmpty(loginInfoDto.getUserId())){
            throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
        }
        List<InviteVehicleDealerTaskImportPO> addList=new ArrayList<InviteVehicleDealerTaskImportPO>();
        
        Collection<String> vinList=Sets.newHashSet();
        //删除历史数据
        inviteVehicleDealerTaskImportMapper.deleteByCreatedBy(""+loginInfoDto.getUserId());

        excelReadServiceIs.analyzeExcelFirstSheet(importFile, new AbstractExcelReadCallBack<InviteVehicleDealerTaskImportDTO>(
                InviteVehicleDealerTaskImportDTO.class, new ExcelReadCallBack<InviteVehicleDealerTaskImportDTO>() {
            private Integer seq = 1;
            @Override
            public void readRowCallBack(InviteVehicleDealerTaskImportDTO dto, boolean b) {
        		InviteVehicleDealerTaskImportPO po=new InviteVehicleDealerTaskImportPO();
        		//校验excel数据
                if(validationData(dto,po)) {
                	 po.setDealerCode(loginInfoDto.getOwnerCode());
                     po.setInviteName(dto.getInviteName());
                     po.setFollowMode(dto.getFollowMode());
                     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                     simpleDateFormat.setLenient(false);
                     po.setAdviseInDate(DateUtils.parseDateStrToDate( simpleDateFormat.format(dto.getAdviseInDate()),"yyyy-MM-dd"));
                     po.setVin(dto.getVin());
                     po.setName(dto.getName());
                     po.setTel(dto.getTel());
                     po.setIsError(0);
                     po.setLicensePlateNum(dto.getLicensePlateNum());
                     vinList.add(dto.getVin());
                }
                po.setLineNumber(++seq);
                addList.add(po);
                
            }
        }));
        if (!CommonUtils.isNullOrEmpty(addList)) {
            int listSize = addList.size();
            int toIndex = 100;
            for (int i = 0; i < addList.size(); i += toIndex) {
                if (i + toIndex > listSize) {        //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
                    toIndex = listSize - i;
                }
                List<InviteVehicleDealerTaskImportPO> insertList = addList.subList(i, i + toIndex);
                inviteVehicleDealerTaskImportMapper.bulkInsert(insertList, loginInfoDto.getUserId());
            }
        }
        return this.checkTmpData(vinList);


    }

    @Override
    @Transactional
    public ImportTempResult<InviteVehicleDealerTaskImportPO> importInviteVehicleVCDCTemp(MultipartFile importFile) throws Exception {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        List<InviteVehicleDealerTaskImportPO> addList = new ArrayList<InviteVehicleDealerTaskImportPO>();
        if(loginInfoDto == null || loginInfoDto.getUserId() == null){
            logger.info("importInviteVehicleVCDCTemp,loginInfoDto is null or userId is null");
            return new ImportTempResult<>();
        }
        Collection<String> vinList = Sets.newHashSet();
        Collection<String> dealerList = Sets.newHashSet();
        //开始时间
        long startTime = System.currentTimeMillis();
        //删除历史数据
        inviteVehicleDealerTaskImportMapper.deleteByCreatedBy(String.valueOf(loginInfoDto.getUserId()));
        //结束时间
        long endTime = System.currentTimeMillis();
        logger.info("importInviteVehicleVCDCTemp,deleteByCreatedBy,use time:{}", endTime - startTime);

        startTime = System.currentTimeMillis();
        excelReadService.analyzeExcelFirstSheet(importFile, new AbstractExcelReadCallBack<InviteVehicleVCDCTaskImportDTO>(
                InviteVehicleVCDCTaskImportDTO.class, new ExcelReadCallBack<InviteVehicleVCDCTaskImportDTO>() {
            private Integer seq = 1;

            @Override
            public void readRowCallBack(InviteVehicleVCDCTaskImportDTO dto, boolean b) {
                InviteVehicleDealerTaskImportPO po = new InviteVehicleDealerTaskImportPO();
                //校验excel数据
                if (validationVCDCData(dto, po)) {
                    po.setInviteName(dto.getInviteName());
                  //  SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                  //  simpleDateFormat.setLenient(false);
                  //  po.setAdviseInDate(DateUtils.parseDateStrToDate(simpleDateFormat.format(dto.getAdviseInDate()), "yyyy-MM-dd"));
                    String dateStr = DATE_FORMAT.get().format(dto.getAdviseInDate());
                    po.setAdviseInDate(DateUtils.parseDateStrToDate(dateStr, "yyyy-MM-dd"));
                    po.setVin(dto.getVin());
                    po.setDealerCode(dto.getDealerCode());
                    po.setName(dto.getName());
                    po.setTel(dto.getTel());
                    po.setIsError(0);
                    po.setLicensePlateNum(dto.getLicensePlateNum());
                    vinList.add(dto.getVin());
                    dealerList.add(dto.getDealerCode());
                }
                po.setLineNumber(++seq);
                addList.add(po);

            }
        }));
        endTime = System.currentTimeMillis();
        logger.info("importInviteVehicleVCDCTemp,readRowCallBack,use time:{}", endTime - startTime);

        startTime = System.currentTimeMillis();
        if (!CommonUtils.isNullOrEmpty(addList)) {
            Lists.partition(addList, 2000).forEach(batchList -> {
                try {
                    inviteVehicleDealerTaskImportMapper.bulkInsert(batchList, loginInfoDto.getUserId());
                } catch (Exception e) {
                    logger.error("批量插入失败，批次大小: {}", batchList.size(), e);
                    throw new ServiceBizException("批量插入数据失败");
                }
            });
            /*int listSize = addList.size();
            int toIndex = 2000;
            for (int i = 0; i < addList.size(); i += toIndex) {
                if (i + toIndex > listSize) {
                    toIndex = listSize - i;
                }
                List<InviteVehicleDealerTaskImportPO> insertList = addList.subList(i, i + toIndex);
                inviteVehicleDealerTaskImportMapper.bulkInsert(insertList, loginInfoDto.getUserId());
                for (InviteVehicleDealerTaskImportPO inviteVehicleDealerTaskImportPO : insertList) {
                    InviteVehicleDealerTaskImportDto inviteVehicleDealerTaskImportDto = new InviteVehicleDealerTaskImportDto();
                    BeanUtils.copyProperties(inviteVehicleDealerTaskImportPO, inviteVehicleDealerTaskImportDto);
                    applicationEventPublisher.publishEvent(new EM90MessageRemindEvent(new EM90MessageRemindDto(null,inviteVehicleDealerTaskImportDto,null)));
                }
            }*/
        }
        endTime = System.currentTimeMillis();
        logger.info("importInviteVehicleVCDCTemp,bulkInsert,use time:{}", endTime - startTime);
        return this.checkVCDCTmpData(vinList, dealerList);
    }

    private ImportTempResult<InviteVehicleDealerTaskImportPO> checkVCDCTmpData(Collection<String> vinList, Collection<String> dealerList) {
        ImportTempResult<InviteVehicleDealerTaskImportPO> importResult = new ImportTempResult<InviteVehicleDealerTaskImportPO>();
        // 获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if(loginInfoDto == null || loginInfoDto.getUserId() == null){
            logger.info("checkVCDCTmpData,loginInfoDto is null or userId is null");
            return importResult;
        }
        Long userId = loginInfoDto.getUserId();

        long startTime = System.currentTimeMillis();
        if(!CommonUtils.isNullOrEmpty(vinList)) {
            logger.info("checkVCDCTmpData, 调用中台地址查询VIN是否存在（开始),vinList:{}", vinList.size());
            Map<String, Object> paramMap = new HashMap<String,Object>();
            paramMap.put("vinList", vinList);
            logger.info("checkVCDCTmpData,size:{}", vinList.size());
            ResponseDTO<Map<String, Object>> response=iMiddleGroundVehicleService.getVinListCheckInfo(paramMap);
            Map<String,Object> notExistVinMap = response.getData();
            if(notExistVinMap != null && CollectionUtils.isNotEmpty(notExistVinMap.keySet())) {
                Map<String,Object> result= response.getData();
                List<String> list=new ArrayList<String>();
                if(!CommonUtils.isNullOrEmpty(result) && !result.isEmpty()) {
                    list=getKeyListBySet(result.keySet());
                }
                if(!CommonUtils.isNullOrEmpty(list) && !list.isEmpty()) {
                    logger.info("checkVCDCTmpData, list：{}", list.size());
                    inviteVehicleDealerTaskImportMapper.updateErrorById(userId,list);
                }
            }
        }
        long endTime = System.currentTimeMillis();
        logger.info("checkVCDCTmpData,use time:{}", endTime - startTime);
        //查询错误项
        List<InviteVehicleDealerTaskImportPO> listError = inviteVehicleDealerTaskImportMapper.queryError(loginInfoDto.getUserId());
        if (!CommonUtils.isNullOrEmpty(listError)) {
            importResult.setErrorList(listError);
        } else {
            logger.info("checkVCDCTmpData,未查到错误数据！");
        }
        // 查询成功项
        List<InviteVehicleDealerTaskImportPO> listSuccess = inviteVehicleDealerTaskImportMapper.querySuccess(loginInfoDto.getUserId());
        if (!CommonUtils.isNullOrEmpty(listSuccess)) {
            importResult.setSuccessList(listSuccess);
        } else {
            logger.info("checkVCDCTmpData,未查到正确数据！");
        }
        // 查询正确数据数
        importResult.setSuccessCount(inviteVehicleDealerTaskImportMapper.querySucessCount(loginInfoDto.getUserId()));

        //EM90 message remind on 2024-02-22
        return importResult;
    }


    /**
     * 校验导入excel数据
     * @param dto
     * @param po
     * @return
     */
    private boolean validationVCDCData(InviteVehicleVCDCTaskImportDTO dto,InviteVehicleDealerTaskImportPO po) {
        boolean isOk=true;
        if(StringUtils.isNullOrEmpty(dto.getDealerCode())) {
            po.setErrorMsg("经销商代码不能为空");
            po.setIsError(1);
            isOk=false;
        }
        if(StringUtils.isNullOrEmpty(dto.getInviteName())||dto.getInviteName().length()>20) {
            po.setErrorMsg("邀约名称不能为空并且长度不能超过20");
            po.setIsError(1);
            isOk=false;
        }
        if(StringUtils.isNullOrEmpty(dto.getAdviseInDate())) {
            po.setErrorMsg("建议进厂日期格式错误,正确格式 yyyy-MM-dd或yyyy/MM/dd");
            po.setIsError(1);
            isOk=false;
        }
        if(StringUtils.isNullOrEmpty(dto.getVin()) || dto.getVin().length()>17) {
            po.setErrorMsg("VIN不能为空并且长度不能超过17");
            po.setIsError(1);
            isOk=false;
        }
        if(StringUtils.isNullOrEmpty(dto.getName()) || dto.getName().length()>50) {
            po.setErrorMsg("客户姓名不能为空并且长度不能超过50");
            po.setIsError(1);
            isOk=false;
        }
        if(StringUtils.isNullOrEmpty(dto.getTel()) || dto.getTel().length()>20) {
            po.setErrorMsg("联系方式不能为空并且长度不能超过20");
            po.setIsError(1);
            isOk=false;
        }
        if(!StringUtils.isNullOrEmpty(dto.getLicensePlateNum()) && dto.getLicensePlateNum().length() > 15) {
            po.setErrorMsg("车牌号长度不能超过15");
            po.setIsError(1);
            isOk=false;
        }
        return isOk;
    }

    /**
                * 校验导入excel数据
     * @param dto
     * @param po
     * @return
     */
    private boolean validationData(InviteVehicleDealerTaskImportDTO dto,InviteVehicleDealerTaskImportPO po) {
    	boolean isOk=true;
    	if(StringUtils.isNullOrEmpty(dto.getInviteName())||dto.getInviteName().length()>20) {
    		po.setErrorMsg("邀约名称不能为空并且长度不能超过20");
    		po.setIsError(1);
    		isOk=false;
    	}
    	if(StringUtils.isNullOrEmpty(dto.getFollowMode())) {
    		po.setErrorMsg("跟进方式不能为空");
    		po.setIsError(1);
    		isOk=false;
    	}
    	if(!"电话".equals(dto.getFollowMode()) && !"短信".equals(dto.getFollowMode())
    			&& !"沃世界".equals(dto.getFollowMode())&& !"微信".equals(dto.getFollowMode())	
    			&& !"问卷".equals(dto.getFollowMode())&& !"邮件".equals(dto.getFollowMode())
    			&& !"QQ".equals(dto.getFollowMode())&& !"其他".equals(dto.getFollowMode())
    			) {
    		po.setErrorMsg("跟进方式只能是'电话，短信，沃世界，微信，问卷，邮件，QQ，其他'");
    		po.setIsError(1);
    		isOk=false;
    	}
    	if(StringUtils.isNullOrEmpty(dto.getAdviseInDate())) {
    		po.setErrorMsg("建议进厂日期格式错误,正确格式 yyyy-MM-dd或yyyy/MM/dd");
    		po.setIsError(1);
    		isOk=false;
    	}
    	if(StringUtils.isNullOrEmpty(dto.getVin()) || dto.getVin().length()>17) {
    		po.setErrorMsg("VIN不能为空并且长度不能超过17");
    		po.setIsError(1);
    		isOk=false;
    	}
    	if(StringUtils.isNullOrEmpty(dto.getName()) || dto.getName().length()>50) {
    		po.setErrorMsg("客户姓名不能为空并且长度不能超过50");
    		po.setIsError(1);
    		isOk=false;
    	}
    	if(StringUtils.isNullOrEmpty(dto.getTel()) || dto.getTel().length()>20) {
    		po.setErrorMsg("联系方式不能为空并且长度不能超过20");
    		po.setIsError(1);
    		isOk=false;
    	}
    	if(!StringUtils.isNullOrEmpty(dto.getLicensePlateNum()) && dto.getLicensePlateNum().length() > 15) {
    		po.setErrorMsg("车牌号长度不能超过15");
    		po.setIsError(1);
    		isOk=false;
    	}
    	return isOk;
    }

   
    /**
     * 导入
     */
    @Override
    public void batchInsert() {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //不生成单号
        //String planNo = commonNoService.getBillNo("PL", loginInfoDto.getOwnerCode());
        String planNo ="";
                //主单
        inviteVehicleDealerTaskMapper.insertInvitationDlr(planNo,loginInfoDto.getUserCode(),loginInfoDto.getUserId());
        //明细
        inviteVehicleRecordMapper.batchInsert(loginInfoDto.getUserId());
        
        //调取中台接口获取oneid的数据
        List<InviteVehicleDealerTaskImportPO> listSuccess = inviteVehicleDealerTaskImportMapper.querySuccess(loginInfoDto.getUserId());
        List<CustomerInfoCenterDTO> param = new ArrayList<>();
	    if(!CommonUtils.isNullOrEmpty(listSuccess) && listSuccess.size() > 0) {
	    	for(InviteVehicleDealerTaskImportPO po:listSuccess) {
	    		CustomerInfoCenterDTO cus = new CustomerInfoCenterDTO();
	    		cus.setMobile(po.getTel());
	    		cus.setName(po.getName());
	    		param.add(cus);
	    	}
            if(!CommonUtils.isNullOrEmpty(param) && param.size() > 0) {
                List<CustomerInfoListReturnDTO> oneIdList = businessPlatformService.getOneId(param);
                for (CustomerInfoListReturnDTO dto:oneIdList) {
                    inviteVehicleRecordService.updateOneIdByMobile(dto);
                }
            }
	    }
        //删除历史数据
        inviteVehicleDealerTaskImportMapper.deleteByCreatedBy(""+loginInfoDto.getUserId());
    }

    /**
     * 厂端自建导入
     */
    @Override
    @Transactional
    public void batchVCDCInsert() {
        logger.info("batchVCDCInsert,start");
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if(loginInfoDto == null || loginInfoDto.getUserCode() == null){
            logger.info("batchVCDCInsert,loginInfoDto is null or userCode is null");
            return;
        }
        // 此问题已存在2年，生产的数据由运维手动导入，导入的sql并没有planNo（生产数据也存在空），故此可以用PL前缀+时间戳
        String planNo = "PL" + DateUtils.dateFormat(new Date(), "yyyyMMddHHmmss");
        logger.info("batchVCDCInsert,planNo:{}", planNo);
        long startTime = System.currentTimeMillis();
        //主单
        inviteVehicleDealerTaskMapper.insertInvitationVCDC(planNo,loginInfoDto.getUserCode(),loginInfoDto.getUserId()
                ,loginInfoDto.getOwnerCode());
        //明细
        inviteVehicleRecordMapper.batchVCDCInsert(loginInfoDto.getUserId());
        long endTime = System.currentTimeMillis();
        logger.info("batchVCDCInsert,use time:{}", endTime - startTime);
        //删除历史数据
        inviteVehicleDealerTaskImportMapper.deleteByCreatedBy(String.valueOf(loginInfoDto.getUserId()));
        logger.info("batchVCDCInsert,end");
    }



    /**
     * 校验更新临时表，返回校验结果对象
     *
     * @return
     * <AUTHOR>
     * @since 2020/03/19
     */
    private ImportTempResult<InviteVehicleDealerTaskImportPO> checkTmpData(Collection<String> vinList) {
        ImportTempResult<InviteVehicleDealerTaskImportPO> importResult = new ImportTempResult<InviteVehicleDealerTaskImportPO>();
        // 获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //校验vin是否存在
//        List<Integer>  list=reportCommonClient.selectNotVehicleByVin(Integer.parseInt(loginInfoDto.getUserId()+""));
//        logger.info("查询VIN返回数据接口开始。。。。。。。。。");
//        if(!CommonUtils.isNullOrEmpty(list) && list.size() > 0) {
//        	for(int i=0;i<list.size();i++) {
//             	logger.info("查询VIN返回数据。。。。。。。。。："+list.get(i));
//             	
//            }
//        	inviteVehicleDealerTaskImportMapper.updateErrorById(list);
//        }
        //中台验证车架号的数据准备
        if(!CommonUtils.isNullOrEmpty(vinList)) {
        	logger.info("===调用中台地址查询VIN是否存在（开始）====");
        	Map<String, Object> paramMap = new HashMap<String,Object>();
            paramMap.put("vinList", vinList);
            ResponseDTO<Map<String, Object>> response=iMiddleGroundVehicleService.getVinListCheckInfo(paramMap);
            Map<String,Object> notExistVinMap = response.getData();
            if(notExistVinMap != null && CollectionUtils.isNotEmpty(notExistVinMap.keySet())) {
            	logger.info("返回1："+JSON.toJSONString(response));
            	Map<String,Object> result= response.getData();
            	List<String> list=new ArrayList<String>();
            	if(!CommonUtils.isNullOrEmpty(result) && result.size()>0) {
            		list=getKeyListBySet(result.keySet());
            	}
            	if(!CommonUtils.isNullOrEmpty(list) && list.size() > 0) {
      			   inviteVehicleDealerTaskImportMapper.updateErrorById(loginInfoDto.getUserId(),list);
        		}
//            	if(notExistVinMap.get("notExistVinList")!=null) {
//            		logger.info("返回2："+JSON.toJSONString(notExistVinMap.get("notExistVinList")));
//            		List<Long> list=objToList(notExistVinMap.get("notExistVinList"));
//            		
//            	};
            	
            }
            
            
            logger.info("===调用中台地址查询VIN是否存在（结束）====");
        }
        
        
       
        
       
        
        
        //查询错误项
        List<InviteVehicleDealerTaskImportPO> listError = inviteVehicleDealerTaskImportMapper.queryError(loginInfoDto.getUserId());
        if (!CommonUtils.isNullOrEmpty(listError)) {
            importResult.setErrorList(listError);
        } else {
            logger.info("未查到错误数据！");
        }
        // 查询成功项
        List<InviteVehicleDealerTaskImportPO> listSuccess = inviteVehicleDealerTaskImportMapper.querySuccess(loginInfoDto.getUserId());
        if (!CommonUtils.isNullOrEmpty(listSuccess)) {
            importResult.setSuccessList(listSuccess);
        } else {
            //inviteVehicleDealerTaskImportMapper.deleteByCreatedBy(loginInfoDto.getUserId()+"");
            logger.info("未查到正确数据！");
        }
        // 查询正确数据数
        importResult.setSuccessCount(inviteVehicleDealerTaskImportMapper.querySucessCount(loginInfoDto.getUserId()));
        return importResult;
    }

	
    /**
	 * 根据key的set返回key的list
	 * 
	 * @param set
	 * @return
	 */
	public  List<String> getKeyListBySet(Set<String> set) {
		List<String> keyList = new ArrayList<String>();
		keyList.addAll(set);
		return keyList;
	}
    
 
	
    private   List<Long> objToList(Object obj){
        List<Long> result = new ArrayList<>();
        if (obj instanceof ArrayList<?>) {
            for (Object o : (List<?>) obj) {
                result.add(Long.parseLong(String.class.cast(o)) );
            }
        }
        result.forEach(System.out::println); // 输出：1 ab
        return result;
	}

    /**
     * 新增邀约线索
     *
     * @param po
     * @return
     */
    private void addInviteVehicleRecord(InviteVehicleDealerTaskPO po, InviteVehicleDTO veh) {
        InviteVehicleRecordDTO record = new InviteVehicleRecordDTO();
        //主线索
        record.setIsMain(1);
        //自建邀约
        record.setSourceType(2);
        record.setVin(veh.getVin());
        record.setLicensePlateNum(veh.getLicensePlateNum());
        record.setName(veh.getName());
        record.setTel(veh.getTel());
        record.setInviteType(po.getInviteType());
        record.setAdviseInDate(po.getAdviseInDate());
        //未跟进
        record.setFollowStatus(82401001);
        //未完成
        record.setOrderStatus(82411002);
        record.setSaId(veh.getSaId());
        record.setSaName(veh.getSaName());
        record.setDealerCode(po.getDealerCode());
        record.setDataSources(po.getDataSources());
        //对对象进行赋值操作
        InviteVehicleRecordPO inviteVehicleRecordPO = record.transDtoToPo(InviteVehicleRecordPO.class);
        //执行插入
        inviteVehicleRecordMapper.insert(inviteVehicleRecordPO);
        po.setInviteType(inviteVehicleRecordPO.getInviteType());
        po.setInviteId(inviteVehicleRecordPO.getId());
    }

	@Override
	public IPage<InviteVehicleDealerImportDTO> selectErrorPage(Page page) {
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		if(StringUtils.isNullOrEmpty(loginInfoDto.getUserId())){
			throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
		}
		List<InviteVehicleDealerTaskImportPO>  list=inviteVehicleDealerTaskImportMapper.selectErrorPage(page,loginInfoDto.getUserId());
		if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteVehicleDealerImportDTO> result = list.stream().map(m -> m.transPoToDto
                    (InviteVehicleDealerImportDTO.class)).collect(Collectors.toList());

            page.setRecords(list);
            return page;
        }
	}

	@Override
	public IPage<InviteVehicleDealerImportDTO> selectSuccessPage(Page page) {
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		if(StringUtils.isNullOrEmpty(loginInfoDto.getUserId())){
			throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
		}
		List<InviteVehicleDealerTaskImportPO>  list=inviteVehicleDealerTaskImportMapper.selectSuccessPage(page,loginInfoDto.getUserId());
		if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteVehicleDealerImportDTO> result = list.stream().map(m -> m.transPoToDto
                    (InviteVehicleDealerImportDTO.class)).collect(Collectors.toList());

            page.setRecords(list);
            return page;
        }
	}

	
}
