package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

    
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintSendEmailTemplatePO;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintSendEmailTemplateMapper;
import com.yonyou.dmscus.customer.service.complaint.ComplaintSendEmailTemplateService;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailTemplateDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;




   /**
 * <p>
 * 客户投诉发送邮件模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
@Service
public class ComplaintSendEmailTemplateServiceImpl implements ComplaintSendEmailTemplateService {
        //日志对象
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        ComplaintSendEmailTemplateMapper complaintSendEmailTemplateMapper;

        /**
         * 分页查询对应数据
         *
         * @param page 分页对象
         * @param complaintSendEmailTemplateDTO 查询条件
         * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailTemplateDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public IPage<ComplaintSendEmailTemplateDTO>selectPageBysql(Page page,ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO){
            if(complaintSendEmailTemplateDTO ==null){
                complaintSendEmailTemplateDTO =new ComplaintSendEmailTemplateDTO();
            }
            ComplaintSendEmailTemplatePO complaintSendEmailTemplatePo =complaintSendEmailTemplateDTO.transDtoToPo(ComplaintSendEmailTemplatePO.class);

            List<ComplaintSendEmailTemplatePO>list= complaintSendEmailTemplateMapper.selectPageBySql(page,complaintSendEmailTemplatePo);
            if(CommonUtils.isNullOrEmpty(list)){
                page.setRecords(new ArrayList<>());
                return page;
            }else{
                List<ComplaintSendEmailTemplateDTO>result=list.stream().map(m->m.transPoToDto(ComplaintSendEmailTemplateDTO.class)).collect(Collectors.toList());

                page.setRecords(result);
                return page;
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param complaintSendEmailTemplateDTO 查询条件
         * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailTemplateDTO>
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public List<ComplaintSendEmailTemplateDTO>selectListBySql(ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO){
            if(complaintSendEmailTemplateDTO ==null){
                complaintSendEmailTemplateDTO =new ComplaintSendEmailTemplateDTO();
            }
            ComplaintSendEmailTemplatePO complaintSendEmailTemplatePo =complaintSendEmailTemplateDTO.transDtoToPo(ComplaintSendEmailTemplatePO.class);
            List<ComplaintSendEmailTemplatePO>list= complaintSendEmailTemplateMapper.selectListBySql(complaintSendEmailTemplatePo);
            if(CommonUtils.isNullOrEmpty(list)){
                return new ArrayList<>();
            }else{
                return list.stream().map(m->m.transPoToDto(ComplaintSendEmailTemplateDTO.class)).collect(Collectors.toList());
            }
        }

        /**
         * 根据查询条件返回结果集
         *
         * @param id 主键ID
         * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailTemplateDTO
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public ComplaintSendEmailTemplateDTO getById(Long id){
            ComplaintSendEmailTemplatePO complaintSendEmailTemplatePo = complaintSendEmailTemplateMapper.selectById(id);
            if(complaintSendEmailTemplatePo!=null){
                return complaintSendEmailTemplatePo.transPoToDto(ComplaintSendEmailTemplateDTO.class);
            }else{
                throw new DALException("查询失败,没有符合条件的数据");
            }
        }

        /**
         * 根据DTO 进行数据新增
         *
         * @param complaintSendEmailTemplateDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO){
            //对对象进行赋值操作
            ComplaintSendEmailTemplatePO complaintSendEmailTemplatePo = complaintSendEmailTemplateDTO.transDtoToPo(ComplaintSendEmailTemplatePO.class);
            //执行插入
            int row= complaintSendEmailTemplateMapper.insert(complaintSendEmailTemplatePo);
            //返回插入的值
            return row;
        }

        /**
         * 根据DTO 及ID 进行数据更新
         *
         * @param id 主键ID
         * @param complaintSendEmailTemplateDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int update(Long id, ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO){
            ComplaintSendEmailTemplatePO complaintSendEmailTemplatePo = complaintSendEmailTemplateMapper.selectById(id);
            //对对象进行赋值操作
            complaintSendEmailTemplateDTO.transDtoToPo(complaintSendEmailTemplatePo);
            //执行更新
            int row= complaintSendEmailTemplateMapper.updateById(complaintSendEmailTemplatePo);
            return row;
        }

        /**
         * 根据ID 进行删除数据
         *
         * @param id
         * @return int
         * <AUTHOR>
         * @since 2018/8/25 0025
         */
        @Override
        public int deleteById(Long id){
            int deleteCount= complaintSendEmailTemplateMapper.deleteById(id);
            logger.debug("删除数量：{}",deleteCount);
            if(deleteCount<=0){
                throw new DALException("删除失败,没有符合条件的数据");
            }else{
                return deleteCount;
            }
        }

        /**
        * 新增模板
        *
        * @param complaintSendEmailTemplateDTO
        * @return
        */
       @Override
       public int insertEmailTemplate(ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO) {
           int row =0;
           long userId= FrameworkUtil.getLoginInfo().getUserId();
           List<ComplaintSendEmailTemplateDTO> list=complaintSendEmailTemplateMapper.queryEmailTemplate(userId);
           if(list.size()!=0){
               long id=list.get(0).getId();
              row= update(id,complaintSendEmailTemplateDTO);
           }else {
               row=insert(complaintSendEmailTemplateDTO);
           }
           return row;
       }

       /**
        * 查看邮件模板
        *
        * @param userId
        * @return
        */
       @Override
       public ComplaintSendEmailTemplateDTO selectLastTemplate(long userId) {
           ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO = complaintSendEmailTemplateMapper.selectLastTemplate(userId);
           return complaintSendEmailTemplateDTO;

       }
   }
