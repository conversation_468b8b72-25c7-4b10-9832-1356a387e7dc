package com.yonyou.dmscus.customer.service.impl.talkskill;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.talkskill.TalkskillKeywordLibMapper;
import com.yonyou.dmscus.customer.dao.talkskill.TalkskillKeywordMapper;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillKeywordLibDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillKeywordLibPO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillKeywordPO;
import com.yonyou.dmscus.customer.service.talkskill.TalkskillKeywordLibService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 话术关键词 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-27
 */
@Service
public class TalkskillKeywordLibServiceImpl extends ServiceImpl<TalkskillKeywordLibMapper, TalkskillKeywordLibPO> implements TalkskillKeywordLibService {
        private final Logger logger=LoggerFactory.getLogger(this.getClass());
        @Resource
        TalkskillKeywordLibMapper talkskillKeywordLibMapper;
        @Resource
        TalkskillKeywordMapper talkskillKeywordMapper;

        /**
         * 根据DTO 进行数据新增
         *
         * @param talkskill
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public List<TalkskillKeywordLibPO> selectListBySql(String talkskill){
            TalkskillKeywordLibPO talkskillKeywordLibPo = new TalkskillKeywordLibPO();
            List<TalkskillKeywordLibPO> list = talkskillKeywordLibMapper.selectListBySql(talkskillKeywordLibPo);
            List<TalkskillKeywordLibPO> hasList = new ArrayList<>();

            TalkskillKeywordPO talkskillKeywordPo = new TalkskillKeywordPO();
            /**
            *先查询是否存在，如果存在查询使用频率，最多查询三个
             */
            for(int i=0;i<list.size();i++){
                TalkskillKeywordLibPO item  = list.get(i);
                if(talkskill.indexOf(item.getKeyword()) > -1 ){
                    talkskillKeywordPo.setIsValid(CommonConstants.DICT_VALIDS_VALID);
                    talkskillKeywordPo.setKeyword(item.getKeyword());
                    //查询存在个数
                    int num =  talkskillKeywordMapper.countNum(talkskillKeywordPo);
                    item.setNum(num);
                    hasList.add(item);
                }
            }
            return hasList;
        }
        /**
         * 根据DTO 进行数据新增
         *
         * @param talkskillKeywordLibDTO 页面DTO
         * @return int
         * <AUTHOR>
         * @since 2018/7/21 0021
         */
        @Override
        public int insert(TalkskillKeywordLibDTO talkskillKeywordLibDTO){
            //对对象进行赋值操作
            TalkskillKeywordLibPO talkskillKeywordLibPo = talkskillKeywordLibDTO.transDtoToPo(TalkskillKeywordLibPO.class);
            return talkskillKeywordLibMapper.insert(talkskillKeywordLibPo);
        }

}
