package com.yonyou.dmscus.customer.service.voicemanage;


import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceCustomerInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.SaCustomerNumberDTO;
import com.yonyou.dmscus.customer.service.common.IBaseService;

import java.util.List;


/**
 * <p>
 * SA呼叫登记 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
public interface SaCustomerNumberService extends IBaseService<SaCustomerNumberDTO> {

    String saveSaCustomerNumber(SaCustomerNumberDTO saCustomerNumberDTO);

    SaCustomerNumberDTO getSaCustomerNumber(Long inviteId);

    List<InviteInsuranceCustomerInfoDTO> selectAllInsuranceCustomerInfo(Long insuranceId);

    void updateInsuranceCustomerInfo(InviteInsuranceCustomerInfoDTO infoDTO);

    void deleteInsuranceCustomerInfo(Long tiicId);
}
