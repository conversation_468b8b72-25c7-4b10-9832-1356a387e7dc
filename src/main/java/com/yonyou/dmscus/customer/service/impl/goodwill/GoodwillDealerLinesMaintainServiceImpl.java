package com.yonyou.dmscus.customer.service.impl.goodwill;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillDealerLinesMaintainMapper;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.IsExistByCodeDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.DealerInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillDealerLinesMaintainDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.companySelectDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.queryUserByOrgTypeDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillDealerLinesMaintainPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillDealerLinesMaintainService;
import com.yonyou.dmscus.customer.util.common.StringUtils;

/**
 * <p>
 * 经销商亲善额度维护 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
@Service
public class GoodwillDealerLinesMaintainServiceImpl
		extends ServiceImpl<GoodwillDealerLinesMaintainMapper, GoodwillDealerLinesMaintainPO>
		implements GoodwillDealerLinesMaintainService {
	/** 日志对象 */
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	GoodwillDealerLinesMaintainMapper goodwillDealerLinesMaintainMapper;
	@Autowired
	HttpServletRequest request;

	@Resource
	private RestTemplate directRestTemplate;

	@Autowired
	private MidUrlProperties midUrlProperties;

	/**
	 * 分页查询对应数据
	 *
	 * @param page
	 *            分页对象
	 * @param goodwillDealerLinesMaintainDTO
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillDealerLinesMaintainDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public IPage<GoodwillDealerLinesMaintainDTO> selectPageBysql(Page page,
			GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO) {
		if (goodwillDealerLinesMaintainDTO == null) {
			goodwillDealerLinesMaintainDTO = new GoodwillDealerLinesMaintainDTO();
		}
		// GoodwillDealerLinesMaintainPO goodwillDealerLinesMaintainPO
		// =goodwillDealerLinesMaintainDTO.transDtoToPo(GoodwillDealerLinesMaintainPO.class);
		// 查询所选区域经销商
		if (!StringUtils.isNullOrEmpty(goodwillDealerLinesMaintainDTO.getAreaManage())) {
			String dealerCode = "";
			String successCodes = "0";
			String requestUrls = midUrlProperties.getMidEndOrgCenter()+midUrlProperties.getSelectCompanyInfo();
			companySelectDTO dtos = new companySelectDTO();
			dtos.setAfterSmallArea(goodwillDealerLinesMaintainDTO.getAreaManage() + "");
			dtos.setCompanyType("15061003");
			HttpHeaders httpHeaderss = new HttpHeaders();
			httpHeaderss.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity<companySelectDTO> httpEntitys = new HttpEntity<>(dtos, httpHeaderss);
			ResponseEntity<ResponseDTO> responseEntitys = directRestTemplate.exchange(requestUrls, HttpMethod.POST,
					httpEntitys, ResponseDTO.class);
			List<DealerInfoDTO> dealerList = new ArrayList<>();

			if (responseEntitys.getBody() != null) {
				if (successCodes.equals(responseEntitys.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					dealerList = objectMapper.convertValue(responseEntitys.getBody().getData(), List.class);
					if (!CommonUtils.isNullOrEmpty(dealerList)) {
						StringBuffer buf = new StringBuffer();
						for (Object object : dealerList) {
							JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
							DealerInfoDTO dealerDto = jsonObject.toJavaObject(DealerInfoDTO.class);
							buf.append("'" + dealerDto.getCompanyCode() + "',");

						}
						dealerCode = buf.toString().substring(0, buf.toString().lastIndexOf(","));
					}

				} else {
					throw new DALException("经销商多条件查询接口异常，请稍后再试");
				}
			}
			goodwillDealerLinesMaintainDTO.setAreaManages(dealerCode);

		}

		List<GoodwillDealerLinesMaintainPO> list = goodwillDealerLinesMaintainMapper.selectPageBySql(page,
				goodwillDealerLinesMaintainDTO);
		Integer count = goodwillDealerLinesMaintainMapper.selectPageCountBySql(goodwillDealerLinesMaintainDTO);
		System.err.println(count);
		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			page.setTotal(count);
			return page;
		} else {
			List<GoodwillDealerLinesMaintainDTO> result = list.stream()
					.map(m -> m.transPoToDto(GoodwillDealerLinesMaintainDTO.class)).collect(Collectors.toList());
			String dealerCodes = "";
			StringBuffer buf = new StringBuffer();
			for (GoodwillDealerLinesMaintainDTO dto : result) {
				buf.append("" + dto.getDealerCode() + ",");
			}
			dealerCodes = buf.toString().substring(0, buf.toString().lastIndexOf(","));
			String successCode = "0";
			String requestUrl = midUrlProperties.getMidEndOrgCenter()+midUrlProperties.getSelectByCompanyCode();
			IsExistByCodeDTO dto = new IsExistByCodeDTO();
			dto.setCompanyCode(dealerCodes);
			HttpHeaders httpHeaders = new HttpHeaders();
			httpHeaders.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity<IsExistByCodeDTO> httpEntity = new HttpEntity<>(dto, httpHeaders);
			ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrl, HttpMethod.POST, httpEntity,
					ResponseDTO.class);
			List<Map> interList = new ArrayList<>();
			List<Map> resultList = new ArrayList<>();
			// 接口数据
			if (responseEntity.getBody() != null) {
				if (successCode.equals(responseEntity.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					interList = objectMapper.convertValue(responseEntity.getBody().getData(), List.class);
					if (!CommonUtils.isNullOrEmpty(interList)) {
						for (Object object : interList) {
							JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
							Map map1 = jsonObject.toJavaObject(Map.class);
							for (GoodwillDealerLinesMaintainDTO dealerMailInfoDTO : result) {
								if (dealerMailInfoDTO.getDealerCode().equals(map1.get("companyCode"))) {
									if (!StringUtils.isNullOrEmpty(map1.get("afterSmallAreaId"))) {
										dealerMailInfoDTO
												.setAreaManage(Integer.parseInt(map1.get("afterSmallAreaId")+""));
									}
									if (!StringUtils.isNullOrEmpty(map1.get("groupCompanyName"))) {
										dealerMailInfoDTO.setBloc(map1.get("groupCompanyName").toString());
									}
									if (!StringUtils.isNullOrEmpty(map1.get("companyNameCn"))) {
										dealerMailInfoDTO.setDealerName(map1.get("companyNameCn").toString());
									}
									if (!StringUtils.isNullOrEmpty(map1.get("provinceId"))) {
										dealerMailInfoDTO
												.setProvince(Integer.valueOf(map1.get("provinceId").toString()));
									}
									if (!StringUtils.isNullOrEmpty(map1.get("provinceName"))) {
										dealerMailInfoDTO.setProvinceName(map1.get("provinceName").toString());
									}

								}
							}

						}
					}

				} else {
					throw new DALException("根据经销商代码查询经销商详情接口异常，请稍后再试");
				}
			}
			page.setRecords(result);
			page.setTotal(count);
			return page;
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param goodwillDealerLinesMaintainDTO
	 *            查询条件
	 * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillDealerLinesMaintainDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public List<GoodwillDealerLinesMaintainDTO> selectListBySql(
			GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO) {
		if (goodwillDealerLinesMaintainDTO == null) {
			goodwillDealerLinesMaintainDTO = new GoodwillDealerLinesMaintainDTO();
		}
		GoodwillDealerLinesMaintainPO goodwillDealerLinesMaintainPo = goodwillDealerLinesMaintainDTO
				.transDtoToPo(GoodwillDealerLinesMaintainPO.class);
		List<GoodwillDealerLinesMaintainPO> list = goodwillDealerLinesMaintainMapper
				.selectListBySql(goodwillDealerLinesMaintainPo);
		if (CommonUtils.isNullOrEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().map(m -> m.transPoToDto(GoodwillDealerLinesMaintainDTO.class))
					.collect(Collectors.toList());
		}
	}

	/**
	 * 根据查询条件返回结果集
	 *
	 * @param id
	 *            主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillDealerLinesMaintainDTO
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public GoodwillDealerLinesMaintainDTO getById(Long id) {
		GoodwillDealerLinesMaintainPO goodwillDealerLinesMaintainPo = goodwillDealerLinesMaintainMapper.selectById(id);
		if (goodwillDealerLinesMaintainPo != null) {
			return goodwillDealerLinesMaintainPo.transPoToDto(GoodwillDealerLinesMaintainDTO.class);
		} else {
			throw new DALException("查询失败,没有符合条件的数据");
		}
	}

	/**
	 * 根据DTO 进行数据新增
	 *
	 * @param goodwillDealerLinesMaintainDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int insert(GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO) {
		// 对对象进行赋值操作
		GoodwillDealerLinesMaintainPO goodwillDealerLinesMaintainPo = goodwillDealerLinesMaintainDTO
				.transDtoToPo(GoodwillDealerLinesMaintainPO.class);
		// 执行插入
		int row = goodwillDealerLinesMaintainMapper.insert(goodwillDealerLinesMaintainPo);
		// 返回插入的值
		return row;
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 * @param id
	 *            主键ID
	 * @param goodwillDealerLinesMaintainDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2018/7/21 0021
	 */
	@Override
	public int update(Long id, GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO) {
		GoodwillDealerLinesMaintainPO goodwillDealerLinesMaintainPo = goodwillDealerLinesMaintainMapper.selectById(id);
		// 对对象进行赋值操作
		goodwillDealerLinesMaintainDTO.transDtoToPo(goodwillDealerLinesMaintainPo);
		// 执行更新
		int row = goodwillDealerLinesMaintainMapper.updateById(goodwillDealerLinesMaintainPo);
		return row;
	}

	/**
	 * 根据ID 进行删除数据
	 *
	 * @param id
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteById(Long id) {
		int deleteCount = goodwillDealerLinesMaintainMapper.deleteById(id);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount <= 0) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 根据ids 进行删除
	 *
	 * @param ids
	 * @return int
	 * <AUTHOR>
	 * @since 2018/8/25 0025
	 */
	@Override
	public int deleteBatchIds(String ids) {
		List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
		int deleteCount = goodwillDealerLinesMaintainMapper.deleteBatchIds(longList);
		logger.debug("删除数量：{}", deleteCount);
		if (deleteCount != longList.size()) {
			throw new DALException("删除失败,没有符合条件的数据");
		} else {
			return deleteCount;
		}
	}

	/**
	 * 分页查询经销商信息数据
	 *
	 * @param page
	 *            分页对象
	 * @param dealerCode
	 *            查询条件
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillDealerLinesMaintainDTO>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@SuppressWarnings("unchecked")
	@Override
	public IPage<Map> getDealerInfo(Page page, companySelectDTO dto) {
		String successCode = "0";
		String requestUrl = midUrlProperties.getMidEndOrgCenter()+midUrlProperties.getSelectCompanyInfo();
		dto.setCompanyType("15061003");
		// RequestDTO<companySelectDTO> requestDto = new RequestDTO<>();
		// requestDto.setData(dto);
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<companySelectDTO> httpEntity = new HttpEntity<>(dto, httpHeaders);
		ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrl, HttpMethod.POST, httpEntity,
				ResponseDTO.class);
		List<DealerInfoDTO> dealerList = new ArrayList<>();
		List<Map> list = new ArrayList<>();
		if (responseEntity.getBody() != null) {
			if (successCode.equals(responseEntity.getBody().getReturnCode())) {
				ObjectMapper objectMapper = new ObjectMapper();
				dealerList = objectMapper.convertValue(responseEntity.getBody().getData(), List.class);
				if (!CommonUtils.isNullOrEmpty(dealerList)) {
					for (Object object : dealerList) {
						JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
						DealerInfoDTO dealerDto = jsonObject.toJavaObject(DealerInfoDTO.class);

						Map map = new HashMap(16);
						map.put("dealerCode", dealerDto.getCompanyCode());
						map.put("dealerName", dealerDto.getCompanyNameCn());
						map.put("bloc", dealerDto.getGroupCompanyName());
						map.put("blocId", dealerDto.getGroupCompanyId());
						map.put("orgCode", dealerDto.getOrgCode());
						map.put("orgName", dealerDto.getOrgName());
						map.put("areaManageId", dealerDto.getAfterBigAreaId());
						map.put("areaManage", dealerDto.getAfterBigAreaName());
						map.put("smallAreaId", dealerDto.getAfterSmallAreaId());
						map.put("smallArea", dealerDto.getAfterSmallAreaName());
						map.put("dealerType", dealerDto.getDealerType());
						map.put("provinceId", dealerDto.getProvinceId());
						map.put("provinceName", dealerDto.getProvinceName());
						map.put("cityId", dealerDto.getCityId());
						map.put("cityName", dealerDto.getCityName());
						map.put("address", dealerDto.getAddressZh());
						list.add(map);
					}
				}

			} else {
				throw new DALException("经销商查询接口异常，请稍后再试");
			}
		}

		if (CommonUtils.isNullOrEmpty(list)) {
			page.setRecords(new ArrayList<>());
			return page;
		} else {
			page.setTotal(list.size());
			page.setRecords(list.subList((int) ((page.getCurrent() - 1) * page.getSize()),
					(int) ((list.size() - (page.getCurrent() - 1) * page.getSize()) <= page.getSize() ? list.size()
							: (page.getCurrent() - 1) * page.getSize() + page.getSize())));
			return page;
		}
	}

	/**
	 * 根据DTO 及ID 进行数据更新
	 *
	 *
	 * @param GoodwillDealerLinesMaintainDTO
	 *            页面DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020/4/21
	 */
	@Override
	public int addDealerLinesInfo(GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO) {

		if (goodwillDealerLinesMaintainDTO.getId() == null) {
			// 执行新增
			// 对对象进行赋值操作
			GoodwillDealerLinesMaintainPO goodwillDealerLinesMaintainPo = goodwillDealerLinesMaintainDTO
					.transDtoToPo(GoodwillDealerLinesMaintainPO.class);
			goodwillDealerLinesMaintainPo.setFoundDate(new Date());
			goodwillDealerLinesMaintainMapper.insert(goodwillDealerLinesMaintainPo);
		} else {
			// 执行更新
			GoodwillDealerLinesMaintainPO goodwillDealerLinesMaintainPo = goodwillDealerLinesMaintainMapper
					.selectById(goodwillDealerLinesMaintainDTO.getId());

			// 对对象进行赋值操作
			goodwillDealerLinesMaintainDTO.transDtoToPo(goodwillDealerLinesMaintainPo);
			goodwillDealerLinesMaintainMapper.updateById(goodwillDealerLinesMaintainPo);
		}

		return 1;
	}

	/**
	 * 查询导出数据
	 *
	 *
	 * @param params
	 * @return List<Map>
	 * <AUTHOR>
	 * @since 2020/4/27
	 */
	@Override
	public List<Map> selectExportListBySql(Map<String, String> queryParam) {

		// 查询所选区域经销商
		if (!StringUtils.isNullOrEmpty(queryParam.get("areaManage"))) {
			String dealerCode = "";
			String successCodes = "0";
			String requestUrls = midUrlProperties.getMidEndOrgCenter()+midUrlProperties.getSelectCompanyInfo();
			companySelectDTO dtos = new companySelectDTO();
			dtos.setAfterSmallArea(queryParam.get("areaManage"));
			dtos.setCompanyType("15061003");
			HttpHeaders httpHeaderss = new HttpHeaders();
			httpHeaderss.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity<companySelectDTO> httpEntitys = new HttpEntity<>(dtos, httpHeaderss);
			ResponseEntity<ResponseDTO> responseEntitys = directRestTemplate.exchange(requestUrls, HttpMethod.POST,
					httpEntitys, ResponseDTO.class);
			List<DealerInfoDTO> dealerList = new ArrayList<>();

			if (responseEntitys.getBody() != null) {
				if (successCodes.equals(responseEntitys.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					dealerList = objectMapper.convertValue(responseEntitys.getBody().getData(), List.class);
					if (!CommonUtils.isNullOrEmpty(dealerList)) {
						StringBuffer buf = new StringBuffer();
						for (Object object : dealerList) {
							JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
							DealerInfoDTO dealerDto = jsonObject.toJavaObject(DealerInfoDTO.class);
							buf.append("'" + dealerDto.getCompanyCode() + "',");

						}
						dealerCode = buf.toString().substring(0, buf.toString().lastIndexOf(","));
					}

				} else {
					throw new DALException("经销商多条件查询接口异常，请稍后再试");
				}
			}
			queryParam.put("areaManages", dealerCode);

		}
		List<Map> list = goodwillDealerLinesMaintainMapper.selectExportListBySql(queryParam);
		if (!CommonUtils.isNullOrEmpty(list)) {
			String dealerCodes = "";
			StringBuffer buf = new StringBuffer();
			Integer areaId = 0;
			for (Map map : list) {
				buf.append("" + map.get("dealer_code") + ",");
			}
			dealerCodes = buf.toString().substring(0, buf.toString().lastIndexOf(","));

			String successCode = "0";
			String requestUrl = midUrlProperties.getMidEndOrgCenter()+midUrlProperties.getSelectByCompanyCode();
			IsExistByCodeDTO dto = new IsExistByCodeDTO();
			dto.setCompanyCode(dealerCodes);
			HttpHeaders httpHeaders = new HttpHeaders();
			httpHeaders.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity<IsExistByCodeDTO> httpEntity = new HttpEntity<>(dto, httpHeaders);
			ResponseEntity<ResponseDTO> responseEntity = directRestTemplate.exchange(requestUrl, HttpMethod.POST, httpEntity,
					ResponseDTO.class);
			List<Map> interList = new ArrayList<>();
			List<Map> resultList = new ArrayList<>();
			// 接口数据
			if (responseEntity.getBody() != null) {
				if (successCode.equals(responseEntity.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					interList = objectMapper.convertValue(responseEntity.getBody().getData(), List.class);
					if (!CommonUtils.isNullOrEmpty(interList)) {
						for (Object object : interList) {
							JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
							Map map1 = jsonObject.toJavaObject(Map.class);
							for (Map dealerMailInfoDTO : list) {
								if ((dealerMailInfoDTO.get("dealer_code")+"").equals(map1.get("companyCode")+"")) {
									if (!StringUtils.isNullOrEmpty(map1.get("afterSmallAreaId"))) {
										areaId = Integer.valueOf(map1.get("afterSmallAreaId") + "");
										dealerMailInfoDTO.put("areaId", areaId);
									}
									if (!StringUtils.isNullOrEmpty(map1.get("groupCompanyName"))) {
										dealerMailInfoDTO.put("bloc", map1.get("groupCompanyName").toString());
									}
									if (!StringUtils.isNullOrEmpty(map1.get("companyNameCn"))) {
										dealerMailInfoDTO.put("dealerName", map1.get("companyNameCn").toString());
									}
									if (!StringUtils.isNullOrEmpty(map1.get("provinceName"))) {
										dealerMailInfoDTO.put("provinceName", map1.get("provinceName").toString());
									}
									if (!StringUtils.isNullOrEmpty(map1.get("provinceId"))) {
										dealerMailInfoDTO.put("provinceId", map1.get("provinceId").toString());
									}

								}
							}

						}
					}

				} else {
					throw new DALException("根据经销商代码查询经销商详情接口异常，请稍后再试");
				}
			}
			// 查询区域-区域经理
			String requestUrls = midUrlProperties.getMidEndAuthCenter() + midUrlProperties.getOrgRole();
			queryUserByOrgTypeDTO dtos = new queryUserByOrgTypeDTO();
			Map maps = new HashMap(16);
			maps.put("orgType", 15061008);
			maps.put("roleCode", "SHQYJL");
			dtos.setData(maps);
			HttpHeaders httpHeaderss = new HttpHeaders();
			httpHeaders.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity<queryUserByOrgTypeDTO> httpEntitys = new HttpEntity<>(dtos, httpHeaderss);
			ResponseEntity<ResponseDTO> responseEntitys = directRestTemplate.exchange(requestUrls, HttpMethod.POST,
					httpEntitys, ResponseDTO.class);
			List<Map> interLists = new ArrayList<>();
			List<Map> resultLists = new ArrayList<>();
			// 接口数据
			if (responseEntitys.getBody() != null) {
				if (successCode.equals(responseEntitys.getBody().getReturnCode())) {
					ObjectMapper objectMapper = new ObjectMapper();
					interList = objectMapper.convertValue(responseEntitys.getBody().getData(), List.class);
					if (!CommonUtils.isNullOrEmpty(interList)) {
						for (Object object : interList) {
							JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
							Map map1 = jsonObject.toJavaObject(Map.class);
							if (!StringUtils.isNullOrEmpty(map1.get("orgid"))) {
								//if (areaId.equals(Integer.valueOf(map1.get("orgid").toString()))) {
									for (Map dealerMailInfoDTO : list) {
										if((dealerMailInfoDTO.get("areaId")+"").equals(map1.get("orgid").toString())) {
											dealerMailInfoDTO.put("areaManage",
													map1.get("orgName") + "-" + map1.get("username"));
										}
									}
								//}
							}

						}
					}

				} else {
					throw new DALException("组织角色查询员工接口异常，请稍后再试");
				}
			}

		}

		// TODO Auto-generated method stub
		return list;
	}

	/**
	 * 根据id删除
	 * 
	 * @param id
	 */
	@Override
	public void deleteDealerLinesById(Long id) throws ServiceBizException {
		if (StringUtils.isNullOrEmpty(id)) {
			throw new ServiceBizException("删除失败");
		}
		GoodwillDealerLinesMaintainPO po = goodwillDealerLinesMaintainMapper.selectById(id);
		if (!StringUtils.isNullOrEmpty(po)) {
			// po.setIsValid(Integer.valueOf(CommonConstants.DICT_IS_NO));
			po.setIsDeleted(true);
			goodwillDealerLinesMaintainMapper.updateById(po);
		}

	}
	
	

}
