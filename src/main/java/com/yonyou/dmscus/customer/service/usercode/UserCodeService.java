package com.yonyou.dmscus.customer.service.usercode;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dto.UserCodeVo;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.userCode.UserCodeInfoDto;
import com.yonyou.dmscus.customer.entity.dto.userCode.UserCodeInfoImportDto;
import com.yonyou.dmscus.customer.entity.dto.userCode.UserCodeInfoImportExcelDto;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @title: UserCodeService
 * @projectName server2
 * @description: TODO
 * @date 2022/7/1914:43
 */
public interface UserCodeService {
    //判断用户有没有权限
    int getOneByUerCode(UserCodeVo vo);
    //分页查询
    IPage<UserCodeInfoDto> selectPageBysql(Page page, UserCodeVo dto);
    //错误信息
    IPage<UserCodeInfoImportDto> selectErrorPage(Page<UserCodeInfoImportDto> page);
    //导入临时表
    ImportTempResult<UserCodeInfoImportDto> importUserCodeTemp(MultipartFile importFile) throws IOException;
    //保存临时数据
    void batchInsert();
}
