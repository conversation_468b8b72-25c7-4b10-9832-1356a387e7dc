package com.yonyou.dmscus.customer.service.impl.vocAccidentInvitation;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.service.excel.ExcelRead;
import com.yonyou.dmscloud.framework.service.excel.ExcelReadCallBack;
import com.yonyou.dmscloud.framework.service.excel.impl.AbstractExcelReadCallBack;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.UtilException;
import com.yonyou.dmscloud.function.utils.common.CommonUtils;
import com.yonyou.dmscus.customer.dao.vocAccidentInvitation.InviteVehicleVocTaskMapper;
import com.yonyou.dmscus.customer.dao.vocAccidentInvitation.TempInviteVehicleVocTaskMapper;
import com.yonyou.dmscus.customer.dto.CustomerInfoCenterDTO;
import com.yonyou.dmscus.customer.dto.CustomerInfoListReturnDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.CheckTmVehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.IsExistByCodeDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.vocAccidentInvitation.InviteVehicleVocTaskDTO;
import com.yonyou.dmscus.customer.entity.dto.vocAccidentInvitation.InviteVehicleVocTaskImportDTO;
import com.yonyou.dmscus.customer.entity.dto.vocAccidentInvitation.TempInviteVehicleVocTaskDTO;
import com.yonyou.dmscus.customer.entity.po.vocAccidentInvitation.InviteVehicleVocTaskPO;
import com.yonyou.dmscus.customer.entity.po.vocAccidentInvitation.TempInviteVehicleVocTaskPO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordDetailService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.vocAccidentInvitation.InviteVehicleVocTaskService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;


/**
 * <p>
 * 车辆特约店VOC事故邀约任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
@Service
public class InviteVehicleVocTaskServiceImpl implements InviteVehicleVocTaskService {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private List<InviteVehicleVocTaskImportDTO> importList;
    @Resource
    InviteVehicleVocTaskMapper inviteVehicleVocTaskMapper;
    @Resource
    TempInviteVehicleVocTaskMapper tempInviteVehicleVocTaskMapper;
    @Autowired
    InviteVehicleRecordService inviteVehicleRecordService;
    @Autowired
    InviteVehicleRecordDetailService inviteVehicleRecordDetailService;
    @Autowired
    ExcelRead<InviteVehicleVocTaskImportDTO> excelReadService;
    @Autowired
    BusinessPlatformService businessPlatformService;


    /**
     * 分页查询对应数据
     *
     * @param page                    分页对象
     * @param inviteVehicleVocTaskDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yonyou.dmscus.customer.entity.dto
                       *       .
                       *       InviteVehicleVocTaskDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public IPage<InviteVehicleVocTaskDTO> selectPageBysql(Page page, InviteVehicleVocTaskDTO inviteVehicleVocTaskDTO) {
        if (inviteVehicleVocTaskDTO == null) {
            inviteVehicleVocTaskDTO = new InviteVehicleVocTaskDTO();
        }
        InviteVehicleVocTaskPO inviteVehicleVocTaskPO = inviteVehicleVocTaskDTO.transDtoToPo(InviteVehicleVocTaskPO
                .class);
        if (!StringUtils.isNullOrEmpty(inviteVehicleVocTaskDTO.getAreaId()) ||
                !StringUtils.isNullOrEmpty(inviteVehicleVocTaskDTO.getLargeAreaId()) ||
                !StringUtils.isNullOrEmpty(inviteVehicleVocTaskDTO.getDealerCode())) {
            List<String> codes = businessPlatformService.getDealercodes(inviteVehicleVocTaskDTO.getAreaId(),
                    inviteVehicleVocTaskDTO.getLargeAreaId(), inviteVehicleVocTaskDTO.getDealerCode(),null);
            inviteVehicleVocTaskPO.setDealerCodes(codes);
        }
        List<InviteVehicleVocTaskPO> list = inviteVehicleVocTaskMapper.selectPageBySql(page, inviteVehicleVocTaskPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            page.setRecords(new ArrayList<>());
            return page;
        } else {
            List<InviteVehicleVocTaskDTO> result = list.stream().map(m -> m.transPoToDto(InviteVehicleVocTaskDTO
                    .class)).collect(Collectors.toList());
            page.setRecords(result);
            return page;
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param inviteVehicleVocTaskDTO 查询条件
     * @return java.util.List<com.yonyou.dmscus.customer.entity.dto.InviteVehicleVocTaskDTO>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public List<InviteVehicleVocTaskDTO> selectListBySql(InviteVehicleVocTaskDTO inviteVehicleVocTaskDTO) {
        if (inviteVehicleVocTaskDTO == null) {
            inviteVehicleVocTaskDTO = new InviteVehicleVocTaskDTO();
        }
        InviteVehicleVocTaskPO inviteVehicleVocTaskPO = inviteVehicleVocTaskDTO.transDtoToPo(InviteVehicleVocTaskPO
                .class);
        List<String> codes = businessPlatformService.getDealercodes(inviteVehicleVocTaskDTO.getAreaId(),
                inviteVehicleVocTaskDTO.getLargeAreaId(), inviteVehicleVocTaskDTO.getDealerCode(),null);
        inviteVehicleVocTaskPO.setDealerCodes(codes);
        List<InviteVehicleVocTaskPO> list = inviteVehicleVocTaskMapper.selectListBySql(inviteVehicleVocTaskPO);
        if (CommonUtils.isNullOrEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(m -> m.transPoToDto(InviteVehicleVocTaskDTO.class)).collect(Collectors.toList());
        }
    }

    /**
     * 根据查询条件返回结果集
     *
     * @param id 主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.InviteVehicleVocTaskDTO
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public InviteVehicleVocTaskDTO getById(Long id) {
        InviteVehicleVocTaskPO inviteVehicleVocTaskPO = inviteVehicleVocTaskMapper.selectById(id);
        if (inviteVehicleVocTaskPO != null) {
            return inviteVehicleVocTaskPO.transPoToDto(InviteVehicleVocTaskDTO.class);
        } else {
            throw new DALException("查询失败,没有符合条件的数据");
        }
    }

    /**
     * 根据DTO 进行数据新增
     *
     * @param inviteVehicleVocTaskDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int insert(InviteVehicleVocTaskDTO inviteVehicleVocTaskDTO) {
        //对对象进行赋值操作
        InviteVehicleVocTaskPO inviteVehicleVocTaskPO = inviteVehicleVocTaskDTO.transDtoToPo(InviteVehicleVocTaskPO
                .class);
        //执行插入
        int row = inviteVehicleVocTaskMapper.insert(inviteVehicleVocTaskPO);
        //返回插入的值
        return row;
    }

    /**
     * 根据DTO 及ID 进行数据更新
     *
     * @param id                      主键ID
     * @param inviteVehicleVocTaskDTO 页面DTO
     * @return int
     * <AUTHOR>
     * @since 2018/7/21 0021
     */
    @Override
    public int update(Long id, InviteVehicleVocTaskDTO inviteVehicleVocTaskDTO) {
        InviteVehicleVocTaskPO inviteVehicleVocTaskPO = inviteVehicleVocTaskMapper.selectById(id);
        //对对象进行赋值操作
        inviteVehicleVocTaskDTO.transDtoToPo(inviteVehicleVocTaskPO);
        //执行更新
        int row = inviteVehicleVocTaskMapper.updateById(inviteVehicleVocTaskPO);
        return row;
    }

    /**
     * 根据ID 进行删除数据
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteById(Long id) {
        int deleteCount = inviteVehicleVocTaskMapper.deleteById(id);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount <= 0) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 根据ids 进行删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @since 2018/8/25 0025
     */
    @Override
    public int deleteBatchIds(String ids) {
        List<Long> longList = StringUtils.convertStrToArray(ids, ",", Long.class);
        int deleteCount = inviteVehicleVocTaskMapper.deleteBatchIds(longList);
        logger.debug("删除数量：{}", deleteCount);
        if (deleteCount != longList.size()) {
            throw new DALException("删除失败,没有符合条件的数据");
        } else {
            return deleteCount;
        }
    }

    /**
     * 保存voc事故邀约
     *
     * @param inviteVehicleVocTaskDTO
     * @return
     */
    @Override
    public int saveVocInvitation(InviteVehicleVocTaskDTO inviteVehicleVocTaskDTO) {
        InviteVehicleVocTaskPO po = inviteVehicleVocTaskDTO.transDtoToPo(InviteVehicleVocTaskPO.class);
        if (po.getId() == null) {
            //新增
            List<String> dealerCodes = new ArrayList<String>();
            dealerCodes.add(po.getDealerCode());
            IsExistByCodeDTO dto = businessPlatformService.checkDealerCodeExist(dealerCodes);//检查经销商代码是否存在
            if (!dto.getIsAllExist()) {
                String errorcodes = StringUtils.listToString(dto.getNotExistCompanyCodeList(), ',');
                throw new DALException("经销商代码:" + errorcodes + "不存在，请检查经销商代码");
            }
            List<String> vins = new ArrayList<String>();
            vins.add(po.getVin());
            CheckTmVehicleDTO vinExist = businessPlatformService.checkVin(vins);//检查经销商代码是否存在
            if (!vinExist.getIsAllExists()){
                String errorcodes = StringUtils.listToString(vinExist.getNotExistVinList(), ',');
                throw new DALException("vin:" + errorcodes + "不存在，请检查车架号");
            }
            Long id = this.addInviteVehicleRecord(po);
            //线索id
            po.setInviteId(id);
            this.addInviteVehicleRecordDetail(po);
            //已下发
            po.setIsCreateInvite(1);
            inviteVehicleVocTaskMapper.insert(po);
            List<CustomerInfoCenterDTO> param = new ArrayList<>();
            CustomerInfoCenterDTO cus = new CustomerInfoCenterDTO();
            cus.setMobile(po.getTel());
            cus.setName(po.getName());
            param.add(cus);
            //中台获取oneId
            List<CustomerInfoListReturnDTO> oneIdList = businessPlatformService.getOneId(param);
            if(oneIdList!=null&&oneIdList.size()==1){
                if(oneIdList.get(0).getRecord()==0){
                    throw new DALException(oneIdList.get(0).getMessage());
                }
                inviteVehicleRecordService.updateOneIdByMobile(oneIdList.get(0));
            }
        } else {
            //更新
            InviteVehicleVocTaskPO inviteVehicleVocTaskPO = inviteVehicleVocTaskMapper.selectById(po.getId());
            //对对象进行赋值操作
            inviteVehicleVocTaskDTO.transDtoToPo(inviteVehicleVocTaskPO);
            List<String> dealerCodes = new ArrayList<String>();
            dealerCodes.add(inviteVehicleVocTaskPO.getDealerCode());
            //检查经销商代码是否存在
            IsExistByCodeDTO dto = businessPlatformService.checkDealerCodeExist(dealerCodes);
            if (!dto.getIsAllExist()) {
                String errorcodes = StringUtils.listToString(dto.getNotExistCompanyCodeList(), ',');
                throw new DALException("经销商代码:" + errorcodes + "不存在，请检查经销商代码");
            }
            //执行更新
            inviteVehicleVocTaskMapper.updateById(inviteVehicleVocTaskPO);
            this.updateInviteVehicleRecord(inviteVehicleVocTaskPO);
            this.updateInviteVehicleRecordDetail(inviteVehicleVocTaskPO);
        }
        return 1;
    }

    /**
     * voc事故导入临时表
     *
     * @param importFile
     * @return
     */
    @Override
    public ImportTempResult<TempInviteVehicleVocTaskDTO> importTempVocInvitation(MultipartFile importFile) throws
            Exception {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //删除临时表数据 该登录用户的数据
        tempInviteVehicleVocTaskMapper.deleteAll(loginInfoDto.getUserId());
        try {
            //匿名内部类
            excelReadService.analyzeExcelFirstSheet(importFile, new
                    AbstractExcelReadCallBack<InviteVehicleVocTaskImportDTO>

                    (InviteVehicleVocTaskImportDTO.class, new ExcelReadCallBack<InviteVehicleVocTaskImportDTO>() {

                        @Override
                        public void readRowCallBack(InviteVehicleVocTaskImportDTO rowDto, boolean isValidateSucess) {
                            try {
                                this.importTempInviteVehicleVocTask(rowDto);
                            } catch (Exception e) {
                                throw new DALException(e.getMessage());
                            }
                        }
                        /**
                         * 插入临时表
                         *
                         * @param rowDto
                         */
                        private void importTempInviteVehicleVocTask(InviteVehicleVocTaskImportDTO rowDto) {
                            //1. 保存导入信息
                            TempInviteVehicleVocTaskPO dataPo = new TempInviteVehicleVocTaskPO();
                            dataPo.setLineNumber(rowDto.getRowNO());
                            dataPo.setVin(rowDto.getVin());
                            dataPo.setLicensePlateNum(rowDto.getLicensePlateNum());
                            dataPo.setDealerCode(rowDto.getDealerCode());
                            dataPo.setName(rowDto.getName());
                            dataPo.setTel(rowDto.getTel());
                            dataPo.setContactSituation(rowDto.getContactSituation());
                            dataPo.setAccidentNo(rowDto.getAccidentNo());
                            dataPo.setAccidentDetail(rowDto.getAccidentDetail());
                            dataPo.setRemark(rowDto.getRemark());
                            //日期格式错误
                            if(rowDto.getContactDate()==null){
                                String date = rowDto.getErrorMsg();
                                String contactDate=date.substring(date.indexOf('"')+1,date.lastIndexOf('"'));
                                dataPo.setContactDate(contactDate);
                                dataPo.setIsError(true);
                                dataPo.setErrorMsg("日期格式错误,正确格式 yyyy-MM-dd HH:mm:ss或yyyy/MM/dd HH:mm:ss");
                            }else{
                                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                dataPo.setContactDate(simpleDateFormat.format(rowDto.getContactDate()));
                                dataPo.setIsError(false);
                            }

                            tempInviteVehicleVocTaskMapper.insert(dataPo);
                        }
                    }));
        } catch (UtilException e) {
            throw new DALException("模板有误,请重新下载模板!");
        }
        ImportTempResult<TempInviteVehicleVocTaskDTO> importResult = this.checkTmpData();
        return importResult;
    }

    @Override
    public int importVocInvitation() {
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        List<TempInviteVehicleVocTaskPO> list = tempInviteVehicleVocTaskMapper.querywaitImport(loginInfoDto.getUserId ());
        List<CustomerInfoCenterDTO> param = new ArrayList<>();
        for (TempInviteVehicleVocTaskPO rowDto : list) {
            InviteVehicleVocTaskPO po = new InviteVehicleVocTaskPO();
            po.setVin(rowDto.getVin());
            po.setLicensePlateNum(rowDto.getLicensePlateNum());
            po.setDealerCode(rowDto.getDealerCode());
            po.setContactDate(rowDto.getContactDateValue());
            po.setName(rowDto.getName());
            po.setTel(rowDto.getTel());
            if ("接通".equals(rowDto.getContactSituation())) {
                //接通
                po.setContactSituation(82421001);

            } else if ("未接通".equals(rowDto.getContactSituation())) {
                //未接通
                po.setContactSituation(82421002);
            }
            po.setAccidentNo(rowDto.getAccidentNo());
            po.setAccidentDetail(rowDto.getAccidentDetail());
            po.setRemark(rowDto.getRemark());
            Long id = this.addInviteVehicleRecord(po);
            //线索id
            po.setInviteId(id);
            this.addInviteVehicleRecordDetail(po);
            //已下发
            po.setIsCreateInvite(1);
            inviteVehicleVocTaskMapper.insert(po);
            CustomerInfoCenterDTO cus = new CustomerInfoCenterDTO();
            cus.setMobile(po.getTel());
            cus.setName(po.getName());
            param.add(cus);
        }
        //中台获取oneId
        List<CustomerInfoListReturnDTO> oneIdList = businessPlatformService.getOneId(param);
        for (CustomerInfoListReturnDTO dto:oneIdList) {
            if(dto.getRecord()==0){
                throw new DALException(dto.getMessage());
            }
            inviteVehicleRecordService.updateOneIdByMobile(dto);
        }
        return 1;
    }


    /**
     * 校验更新临时表，返回校验结果对象
     *
     * @return
     * <AUTHOR>
     * @since 2020/03/19
     */
    private ImportTempResult<TempInviteVehicleVocTaskDTO> checkTmpData() {
        ImportTempResult<TempInviteVehicleVocTaskDTO> importResult = new
                ImportTempResult<TempInviteVehicleVocTaskDTO>();
        //获取登录用户信息
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        //校验经销商是否在经销商列表中存在
        this.checkDealerCodeExist(loginInfoDto.getUserId());
        //校验vin是否在车架号列表中存在
        this.checkVinExist(loginInfoDto.getUserId());
        //校验是否存在相同vin
        tempInviteVehicleVocTaskMapper.updateCheckVinRepeat(loginInfoDto.getUserId());
        //校验车牌号错误
        tempInviteVehicleVocTaskMapper.updateCheckLicense(loginInfoDto.getUserId());
        //校验客户姓名
        tempInviteVehicleVocTaskMapper.updateCheckName(loginInfoDto.getUserId());
        //校验电话格式
        tempInviteVehicleVocTaskMapper.updateCheckTel(loginInfoDto.getUserId());
        //校验客户通话情况
        tempInviteVehicleVocTaskMapper.updateCheckContactSituation(loginInfoDto.getUserId());
        //校验voc事故号
        tempInviteVehicleVocTaskMapper.updateCheckAccidentNo(loginInfoDto.getUserId());
        //校验voc事故说明
        tempInviteVehicleVocTaskMapper.updateCheckAccidentDetail(loginInfoDto.getUserId());
        //校验voc备注
        tempInviteVehicleVocTaskMapper.updateCheckRemark(loginInfoDto.getUserId());
        //查询错误项
        List<TempInviteVehicleVocTaskPO> list = tempInviteVehicleVocTaskMapper.queryError(loginInfoDto.getUserId());
        if (CommonUtils.isNullOrEmpty(list)) {
            importResult.setErrorList(new ArrayList<TempInviteVehicleVocTaskDTO>());
        } else {
            importResult.setErrorList(list.stream().map(m -> m.transPoToDto(TempInviteVehicleVocTaskDTO.class))
                    .collect(Collectors.toList()));
        }
        //查询正确数据数
        importResult.setSuccessCount(tempInviteVehicleVocTaskMapper.querySucessCount(loginInfoDto.getUserId()));
        return importResult;
    }


    /**
     * 校验经销商是否在经销商列表中存在
     *
     * @param userId
     */
    private void checkDealerCodeExist(Long userId) {
        List<Map> codeList = tempInviteVehicleVocTaskMapper.getDealerCodes(userId);
        List<String> list = new ArrayList<String>();
        for (Map item : codeList) {
            if (item != null && !StringUtils.isNullOrEmpty(StringUtils.valueOf(item.get("dealer_code")))) {
                list.add(StringUtils.valueOf(item.get("dealer_code")));
            }
        }
        IsExistByCodeDTO rs = businessPlatformService.checkDealerCodeExist(list);
        if (!rs.getIsAllExist()) {
            List<String> errorList = rs.getNotExistCompanyCodeList();
            tempInviteVehicleVocTaskMapper.updateErrorDealerCode(userId, errorList);
        }
        tempInviteVehicleVocTaskMapper.updateEmptyDealerCode(userId);
    }



    /**
     * 校验经销商是否在经销商列表中存在
     *
     * @param userId
     */
    private void checkVinExist(Long userId) {
        List<Map> codeList = tempInviteVehicleVocTaskMapper.getDealerCodes(userId);
        List<String> list = new ArrayList<String>();
        for (Map item : codeList) {
            if (item != null && !StringUtils.isNullOrEmpty(StringUtils.valueOf(item.get("vin")))) {
                list.add(StringUtils.valueOf(item.get("vin")));
            }
        }
        CheckTmVehicleDTO rs = businessPlatformService.checkVin(list);
        if (!rs.getIsAllExists()) {
            List<String> errorList = rs.getNotExistVinList();
            tempInviteVehicleVocTaskMapper.updateErrorVin(userId, errorList);
        }
        tempInviteVehicleVocTaskMapper.updateEmptyDealerCode(userId);
    }


//    /**
//     * 导入voc事故邀约
//     */
//    private void importInviteVehicleVocTask() {
//        List<String> vins = new ArrayList<String>();
//        List<String> dealerCodes = new ArrayList<String>();
//        for (InviteVehicleVocTaskImportDTO rowDto : this.importList) {
//            if (rowDto.getVin() == null || "".equals(rowDto.getVin())) {
//                throw new DALException("VIN不能为空，请检查数据");
//            }
//            vins.add(rowDto.getVin());
//            if(rowDto.getDealerCode() == null || "".equals(rowDto.getDealerCode())) {
//                throw new DALException("经销商代码不能为空，请检查数据");
//            }
//            dealerCodes.add(rowDto.getDealerCode());
//        }
//        CheckTmVehicleDTO check = businessPlatformService.checkVin(vins);
//        if (!check.getIsAllExists()) {
//            String codes = StringUtils.listToString(check.getNotExistVinList(), ',');
//            throw new DALException("VIN:" + codes + "不存在，请检查VIN");
//        }
//        IsExistByCodeDTO dto = businessPlatformService.checkDealerCodeExist(dealerCodes);
//        if (!dto.getIsAllExist()) {
//            String errorcodes = StringUtils.listToString(dto.getNotExistCompanyCodeList(), ',');
//            throw new DALException("经销商代码:" + errorcodes + "不存在，请检查经销商代码");
//        }
//        long seq = 0;
//        for (InviteVehicleVocTaskImportDTO rowDto : this.importList) {
//            seq++;
//            InviteVehicleVocTaskPO po = new InviteVehicleVocTaskPO();
//            po.setVin(rowDto.getVin());
//            if(rowDto.getLicensePlateNum() == null || "".equals(rowDto.getLicensePlateNum())) {
//                throw new DALException("行号" + seq + ",车牌号不能为空，请检查数据");
//            }
//            if(rowDto.getLicensePlateNum()!=null&&rowDto.getLicensePlateNum().length()>20){
//                throw new DALException("行号" + seq + ",车牌号过长，请检查数据");
//            }
//            po.setLicensePlateNum(rowDto.getLicensePlateNum());
//            po.setDealerCode(rowDto.getDealerCode());
//            if (rowDto.getContactDate() == null) {
//                throw new DALException("行号" + seq + ",日期格式错误，请检查数据");
//            }
//            po.setContactDate(rowDto.getContactDate());
//            if(rowDto.getName() == null || "".equals(rowDto.getName())) {
//                throw new DALException("行号" + seq + ",客户姓名不能为空，请检查数据");
//            }
//            if(rowDto.getName()!=null&&rowDto.getName().length()>20){
//                throw new DALException("行号" + seq + ",客户姓名过长，请检查数据");
//            }
//            po.setName(rowDto.getName());
//            if(rowDto.getTel() == null || "".equals(rowDto.getTel())) {
//                throw new DALException("行号" + seq + ",电话号码不能为空，请检查数据");
//            }
//            if(rowDto.getTel()!=null&&rowDto.getTel().length()>20){
//                throw new DALException("行号" + seq + ",电话号码过长，请检查数据");
//            }
//            po.setTel(rowDto.getTel());
//            if ("接通".equals(rowDto.getContactSituation())) {
//                po.setContactSituation(82421001);//接通
//
//            } else if ("未接通".equals(rowDto.getContactSituation())) {
//                po.setContactSituation(82421002);//未接通
//            } else {
//                throw new DALException("行号" + seq + ",与客户通话情况填写错误，请检查数据");
//            }
//            if(rowDto.getAccidentNo()!=null&&rowDto.getAccidentNo().length()>30){
//                throw new DALException("行号" + seq + ",VOC事故号过长，请检查数据");
//            }
//            po.setAccidentNo(rowDto.getAccidentNo());
//            if(rowDto.getAccidentDetail()!=null&&rowDto.getAccidentDetail().length()>300){
//                throw new DALException("行号" + seq + ",VOC事故说明过长，请检查数据");
//            }
//            po.setAccidentDetail(rowDto.getAccidentDetail());
//            if(rowDto.getRemark()!=null&&rowDto.getRemark().length()>300){
//                throw new DALException("行号" + seq + ",备注过长，请检查数据");
//            }
//            po.setRemark(rowDto.getRemark());
//            Long id = this.addInviteVehicleRecord(po);
//            po.setInviteId(id);//线索id
//            this.addInviteVehicleRecordDetail(po);
//            po.setIsCreateInvite(1);//已下发
//            inviteVehicleVocTaskMapper.insert(po);
//
//        }
//    }


    /**
     * 新增邀约线索
     *
     * @param po
     * @return
     */
    private Long addInviteVehicleRecord(InviteVehicleVocTaskPO po) {
        InviteVehicleRecordDTO record = new InviteVehicleRecordDTO();
        record.setDealerCode(po.getDealerCode());
        //主线索
        record.setIsMain(1);
        //VOC事故邀约
        record.setSourceType(3);
        record.setVin(po.getVin());
        record.setLicensePlateNum(po.getLicensePlateNum());
        record.setName(po.getName());
        record.setTel(po.getTel());
        //voc邀约
        record.setInviteType(82381004);
        //VOC事故车联系经销商时间
        record.setAdviseInDate(po.getContactDate());
        Calendar c = Calendar.getInstance();
        c.setTime(po.getContactDate());
        //加3分钟
        c.add(Calendar.MINUTE, 3);
        record.setNewAdviseInDate(c.getTime());
        record.setPlanFollowDate(c.getTime());
        //继续跟进
        record.setFollowStatus(82401004);
        //未完成
        record.setOrderStatus(82411002);
        record.setSaId("VOC");
        record.setSaName("VOC");
        //经销商
        record.setOwnerCode(po.getDealerCode());
        return inviteVehicleRecordService.addInviteVehicleRecord(record);
    }

    /**
     * 更新邀约线索
     *
     * @param po
     */
    private void updateInviteVehicleRecord(InviteVehicleVocTaskPO po) {
        InviteVehicleRecordDTO record = new InviteVehicleRecordDTO();
        record.setId(po.getInviteId());//邀约线索id
        record.setName(po.getName());//客户姓名
        record.setTel(po.getTel());//电话
        record.setLicensePlateNum(po.getLicensePlateNum());//车牌
        record.setOwnerCode(po.getDealerCode());//经销商
        record.setDealerCode(po.getDealerCode());
        record.setAdviseInDate(po.getContactDate());
        Calendar c = Calendar.getInstance();
        c.setTime(po.getContactDate());
        c.add(Calendar.MINUTE, 3);//加3分钟
        record.setNewAdviseInDate(c.getTime());
        record.setPlanFollowDate(c.getTime());
        inviteVehicleRecordService.update(record.getId(), record);
    }


    /**
     * 新增邀约线索跟进记录
     *
     * @param po
     */
    private void addInviteVehicleRecordDetail(InviteVehicleVocTaskPO po) {
        InviteVehicleRecordDetailDTO recordDetail = new InviteVehicleRecordDetailDTO();
        recordDetail.setInviteId(po.getInviteId());
        recordDetail.setContent("voc录入。" + po.getAccidentDetail());
        //继续跟进
        recordDetail.setStatus(82401004);
        Calendar c = Calendar.getInstance();
        c.setTime(po.getContactDate());
        //加3分钟
        c.add(Calendar.MINUTE, 3);
        //下次跟进日期
        recordDetail.setPlanDate(c.getTime());
        //实际跟进日期
        recordDetail.setActualDate(po.getContactDate());
        //电话
        recordDetail.setMode(82391001);
        //经销商
        recordDetail.setOwnerCode(po.getDealerCode());
        recordDetail.setDealerCode(po.getDealerCode());
        recordDetail.setSaId("VOC");
        recordDetail.setSaName("VOC");
        inviteVehicleRecordDetailService.addInviteVehicleRecordDetail(recordDetail);
    }

    /**
     * 更新邀约线索跟进记录
     *
     * @param po
     */
    private void updateInviteVehicleRecordDetail(InviteVehicleVocTaskPO po) {
        InviteVehicleRecordDetailDTO recordDetail = new InviteVehicleRecordDetailDTO();
        recordDetail.setInviteId(po.getInviteId());
        recordDetail.setContent("voc录入。" + po.getAccidentDetail());
        Calendar c = Calendar.getInstance();
        c.setTime(po.getContactDate());
        c.add(Calendar.MINUTE, 3);//加3分钟
        recordDetail.setPlanDate(c.getTime());//下次跟进日期
        recordDetail.setActualDate(po.getContactDate());//实际跟进日期
        recordDetail.setOwnerCode(po.getDealerCode());//经销商
        recordDetail.setDealerCode(po.getDealerCode());
        recordDetail.setSaId("VOC");
        recordDetail.setSaName("VOC");
        inviteVehicleRecordDetailService.updateInviteVehicleRecordDetail(recordDetail);
    }

}
