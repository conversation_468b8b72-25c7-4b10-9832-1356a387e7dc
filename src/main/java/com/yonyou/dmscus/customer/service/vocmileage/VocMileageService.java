
/** 
*Copyright 2020 Yonyou Corporation Ltd. All Rights Reserved.
* This software is published under the terms of the Yonyou Software
* License version 1.0, a copy of which has been included with this
* distribution in the LICENSE.txt file.
*
* @Project Name : dmscus.customer
*
* @File name : VocMileageService.java
*
* <AUTHOR> caizhonming
*
* @Date : 2020年7月8日
*
*/

package com.yonyou.dmscus.customer.service.vocmileage;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.common.AjaxResponse;
import com.yonyou.dmscus.customer.entity.dto.vocmileage.VocMileageDTO;

/**
 * VOC里程管理服务类
 * 
 * <AUTHOR>
 * @date 2020年7月8日
 */

public interface VocMileageService {

    /**
     * 分页查询VOC里程
     * 
     * <AUTHOR>
     * @date 2020年7月8日
     * @param page
     * @param queryVocMileageDTO
     * @return
     * @throws ServiceBizException
     */

    public IPage<VocMileageDTO> queryVocMileagePageInfo(Page page,
                                                        VocMileageDTO queryVocMileageDTO) throws ServiceBizException;

    /**
     * 根据查询参数查询VOC里程
     * 
     * <AUTHOR>
     * @date 2020年7月8日
     * @param queryVocMileageDTO
     * @return
     * @throws ServiceBizException
     */

    public List<VocMileageDTO> findVocMileageByParams(VocMileageDTO queryVocMileageDTO) throws ServiceBizException;

    /**
     * 功能描述: <br>
     * 〈功能详细描述〉 通过获取文件Excel中的数据，导入数据库
     * 
     * @param operationId
     * @param operationName
     * @param importDataList
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public AjaxResponse importExcelData(Long operationId, String operationName,
                                        List<Map<Integer, String>> importDataList);

    /**
     * 批量删除数据
     * <AUTHOR>
     * @date 2020年7月8日
     * @param ids
     * @return
     */

    public int deleteBatchIds(String ids);
    
    
    
    /**
    * 批量导入数据测试
    * <AUTHOR>
    * @date 2020年7月16日
    * @param importDataList
    * @return
    */
    	
    public AjaxResponse importExcelData2(List<String[]> importDataList);
}
