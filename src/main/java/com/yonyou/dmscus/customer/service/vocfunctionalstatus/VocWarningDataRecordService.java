package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataLogPo;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataRecordPo;

import java.util.List;

/**
 * <AUTHOR>
 * @title: VocWarningDataRecordService
 * @projectName dmscus.customer
 * @description:
 * @date 2022/11/118:47
 */
public interface VocWarningDataRecordService {
    List<VocWarningDataRecordPo> selectListWarningDataRecord(List<VocWarningDataLogPo> x);

    int insertList(List<VocWarningDataRecordPo> insertList);

    int updateList(List<VocWarningDataRecordPo> updateList);

    int updateWarningIsExecute(List<VocWarningDataRecordPo> list);

    List<VocWarningDataRecordPo> selectVocWarningDataRecordPoByModfiyTime(String dateTime, Integer begIndex, Integer partitionSize);

    String selectKmByVin(String vin, String datetime, String endtime);

    int selectByVin(String vin);

    int selectWarningdailyReodeByVin(String vin);

    VocWarningDataRecordPo selectVocWarningDataRecordPo(String vin);

    int selectCountByVinAndModfiy(String vin, String dateTime);
}
