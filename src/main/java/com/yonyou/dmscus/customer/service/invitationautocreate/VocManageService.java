package com.yonyou.dmscus.customer.service.invitationautocreate;

import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.dto.VocMileageVO;

import java.time.LocalDate;
import java.util.List;


/**
 * <p>
 * 平均里程计算日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-13
 */
public interface VocManageService {

    int partitionGetAllVocVeh(String createDate, Integer page, Integer number, Integer partitionSize);

    int partitionGetAllVocVehOld(String createDate, Integer page, Integer number, Integer partitionSize );

    int pushpartitionGetAllVocVeh(String createDate);

    int computeDaMiForVocWithDateRange(LocalDate startDate, LocalDate endDate );

    int updateVoc(String createDate);

    //更新首保
    void updateFirstMaintainTask(String nextMonthDay, VehicleOwnerVO vo, String vin, Double dailyAverageMileage);

    void updateFirstMaintainTask(String nextMonthDay, List<VocMileageVO> list, String sql);

    void updateMaintainTask(String nextMonthDay, VehicleOwnerVO vo, String vin, Double dailyAverageMileage);

    void updateMaintainTask(String nextMonthDay, List<VocMileageVO> list, String sql);

    void updateMaintainTaskWithDateRange(LocalDate startDate, LocalDate endDate, VehicleOwnerVO vo, String vin, Double dailyAverageMileage);

    void updateVulnerableTask(String nextMonthDay, VehicleOwnerVO vo, String vin, Double dailyAverageMileage);

    void updateVulnerableTaskForRuleChanged(String nextMonthDay, Integer type, String code, Integer
            lastMileageInterval, Long id);


    /**
     * 定保规则修改
     *
     * @param nextMonthDay
     * @param dealerCode
     * @param ruleValue
     * @param closeInterval
     * @param remindInterval
     */
    void updateMaintainTaskForRuleChanged(String nextMonthDay, String dealerCode, Integer ruleValue, Integer
            closeInterval, Integer remindInterval, Integer inviteRule, Integer lastRuleValue, Long id);


    /**
     * 首保规则修改
     *
     * @param nextMonthDay
     * @param dealerCode
     * @param ruleValue
     * @param closeInterval
     * @param remindInterval
     */
    void updateFirstMaintainTaskForRuleChanged(String nextMonthDay, Integer inviteRule, String dealerCode, Integer
            ruleValue, Integer closeInterval, Integer remindInterval, Long id);

    /**
     * 客户流失规则修改
     *
     * @param nextMonthDay
     */
    void updateCustomerLossTaskForRuleChanged(String nextMonthDay, String dealerCode, Integer ruleValue, Integer
            closeInterval, Integer remindInterval, Long id);

    /**
     * 保修规则修改
     *
     * @param nextMonthDay
     */
    void updateGuaranteeTaskForRuleChanged(String nextMonthDay, String dealerCode, Integer ruleValue, Integer
            closeInterval, Integer remindInterval, Long id);



    int pushComputeDailyAverageMileageRecord(String startDate, String endDate);

    int pushComputeDailyAverageMileageTask(String startDate, String endDate);
}
