package com.yonyou.dmscus.customer.service.invitationFollow;


import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.service.common.IBaseService;
import io.swagger.models.auth.In;


/**
 * <p>
 * 车辆邀约记录明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
public interface InviteVehicleRecordDetailService extends IBaseService<InviteVehicleRecordDetailDTO> {

    Long addInviteVehicleRecordDetail (InviteVehicleRecordDetailDTO inviteVehicleRecordDetailDTO);

    void updateInviteVehicleRecordDetail(InviteVehicleRecordDetailDTO recordDetail);

    //查询跟进次数
    int selectNumByCount(Long inviteId);
}
