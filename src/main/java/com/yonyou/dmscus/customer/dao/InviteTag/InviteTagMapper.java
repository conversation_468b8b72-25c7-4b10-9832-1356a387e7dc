package com.yonyou.dmscus.customer.dao.InviteTag;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yonyou.dmscus.customer.entity.po.inviteTag.InviteTagPO;

public interface InviteTagMapper extends BaseMapper<InviteTagPO> {
	
	List<InviteTagPO> selectInviteTagByName(@Param("tagNameList") Set<String> tagNames,@Param("topic") String topic);
	
	Integer insertBatchInviteTag(@Param("tagNameList") Set<String> tagNames, @Param("topic") String topic);


    
}
