package com.yonyou.dmscus.customer.dao.goodwill;

    
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillDealerLinesMaintainDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillDealerLinesMaintainPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillDealerMailInfoPO;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 * 经销商亲善额度维护 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
public interface GoodwillDealerLinesMaintainMapper extends IMapper<GoodwillDealerLinesMaintainPO> {
	List<GoodwillDealerLinesMaintainPO> selectPageBySql(Page page,@Param("params") GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO);
	int selectPageCountBySql(@Param("params") GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO );	
	
	//经销商亲善额度导出查询
		public List<Map> selectExportListBySql(@Param("params")Map<String, String> queryParam);
}
