package com.yonyou.dmscus.customer.dao.complaint;

import java.util.List;
import java.util.Set;

import com.yonyou.dmscus.customer.dto.CompanyDetailInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.common.HolidayDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintClassificationDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfMorePO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoPO;

/**
 * <p>
 * 客户投诉信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface ComplaintInfoMapper extends IMapper<ComplaintInfoPO> {
	/**
	 * 通过id查询
	 *
	 * @param complaintInfoDTO
	 * @return
	 */
	List queryid(@Param("params") ComplaintInfoDTO complaintInfoDTO);

	/**
	 * 分页查询
	 *
	 * @param var1
	 * @param complaintInfMorePo
	 * @return
	 */
	List<ComplaintInfMorePO> selectCusByDeal(Page var1, @Param("params") ComplaintInfMorePO complaintInfMorePo,@Param("model") String model);

	/**
	 * 导出查询
	 *
	 * @param complaintInfMorePo
	 * @return
	 */
	List<ComplaintInfMorePO> selectCusByDealAll(@Param("params") ComplaintInfMorePO complaintInfMorePo);

	/**
	 * 通过vin号查询亲善历史
	 *
	 * @param
	 * @return
	 */
	List<GoodwillApplyInfoDTO> getByvin(Page var1, @Param("params") GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	/**
	 * 查询经销商首次回复时间
	 *
	 * @param complaintInfoId
	 * @return
	 */
	List<ComplaintInfoPO> queryDealerFirstReplyTime(long complaintInfoId);

	List<ComplaintInfoPO> queryComplaintData( @Param("params") ComplaintInfoDTO complaintInfoDTO);

    List<ComplaintClassificationDTO> getCategory1(@Param("params")ComplaintClassificationDTO complaintClassificationDTO);

	ComplaintInfMorePO selectCusDetailById(@Param("params")ComplaintInfMoreDTO complaintInfMoreDTO);

    List<HolidayDTO> selectHoildayList(@Param("params")HolidayDTO holidayDTO);

	void insertHoliday(@Param("params")HolidayDTO holidayDTO,@Param("hoildayList") Set<String> holidayDate);

    List<String> queryDealer();

    List<String> querySaleDealer();

	void regionInformationRefresh(@Param("params")List<CompanyDetailInfoDTO> companyDetailInfoDTOList);

	void buyRegionInformationRefresh(@Param("params")List<CompanyDetailInfoDTO> companyDetailInfoDTOS);

	void saleRegionInformationRefresh(@Param("params")List<CompanyDetailInfoDTO> companyDetailInfoDTOS);

	void buySaleRegionInformationRefresh(@Param("params")List<CompanyDetailInfoDTO> companyDetailInfoDTOS);

	void ccmRegionInformationRefresh(@Param("params")List<CompanyDetailInfoDTO> companyDetailInfoDTOS);

}
