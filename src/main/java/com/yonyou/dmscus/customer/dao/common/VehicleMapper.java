package com.yonyou.dmscus.customer.dao.common;


import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.dto.VocMileageVO;
import com.yonyou.dmscus.customer.entity.po.common.VehiclePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 车辆资料 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-08
 */
public interface VehicleMapper extends IMapper<VehiclePO> {

    VehiclePO getVehicleByVin(String vin);

    VehiclePO selectDateByVin(String vin);

    void insertVoc(@Param("params")VehiclePO insert);
    //批量添加
    int insertVocAll(@Param("params")List<VehiclePO> params);

    void updateVocById(@Param("params")VehiclePO rs);

    //批量更新
    int updateVocByIdAll(@Param("params")List<VehiclePO> params);

    List<VehiclePO> getAllVeh();

    List<VocMileageVO> getMileage(@Param("vin")String vin);

    void updateDailyAverageMileageById(@Param("params")VehiclePO po);
}
