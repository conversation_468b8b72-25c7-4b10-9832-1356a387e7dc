package com.yonyou.dmscus.customer.dao.accidentClues;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.dto.AccidentCluesUserDto;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesSaNumberPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/19 0019
 */
public interface AccidentCluesSaNumberMapper extends IMapper<AccidentCluesSaNumberPO> {

    public List<AccidentCluesSaNumberPO> selectCusList(@Param("acId") Integer acId);

    public AccidentCluesUserDto selectCusListB(@Param("acId") Integer acId);

    void updateFollowId(@Param("acId") Integer acId,@Param("followId") Integer followId);
}
