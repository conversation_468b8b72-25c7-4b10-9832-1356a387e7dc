package com.yonyou.dmscus.customer.dao.goodwill;

    
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyRepairInfoPO;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 亲善预约申请维修记录子表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
public interface GoodwillApplyRepairInfoMapper extends IMapper<GoodwillApplyRepairInfoPO> {
	public List<GoodwillApplyRepairInfoDTO> getSupportApplyRepairInfoById(Long goodwillApplyId);
}
