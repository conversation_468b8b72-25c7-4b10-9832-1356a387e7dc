package com.yonyou.dmscus.customer.dao.faultLight;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.faultLight.RvdcVehOnlineOfflineChangeRecordPO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightCluePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RvdcVehOnlineOfflineChangeRecordMapper extends IMapper<RvdcVehOnlineOfflineChangeRecordPO> {

    List<RvdcVehOnlineOfflineChangeRecordPO> selectEndTime(@Param("vin")String vin, @Param("startTime")String startTime);

    List<RvdcVehOnlineOfflineChangeRecordPO> selectEndTime(@Param("poList")List<TtFaultLightCluePO> poList);
}
