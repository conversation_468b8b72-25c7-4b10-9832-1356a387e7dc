package com.yonyou.dmscus.customer.dao.faultLight;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.faultLight.BatchUpdateDTO;
import com.yonyou.dmscus.customer.entity.po.faultLight.FaultLightPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FaultLightDisposeMapper extends IMapper<FaultLightPO> {

    int batchUpdate(@Param("params")BatchUpdateDTO batchUpdate,@Param("listItem")List<Long> listItem);

    FaultLightPO selectTypeInfo(@Param("params")FaultLightPO faultLightPO);

    List<FaultLightPO> selectPageTypeInfo(Page page,@Param("params")FaultLightPO faultLightPO);

    int addTypeInfo(@Param("params")List<FaultLightPO> params);

    int selectDutyStatusByIds(@Param("list")List<Long> list);
}
