package com.yonyou.dmscus.customer.dao.complaint.sale;

    
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintKpiBaseRuleDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintKpiBaseRulePO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 销售客户投诉KP基础规则 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-21
 */
public interface SaleComplaintKpiBaseRuleMapper extends IMapper<SaleComplaintKpiBaseRulePO> {

    int updateKpi(@Param("params")SaleComplaintKpiBaseRuleDTO saleComplaintKpiBaseRuleDTO);
}
