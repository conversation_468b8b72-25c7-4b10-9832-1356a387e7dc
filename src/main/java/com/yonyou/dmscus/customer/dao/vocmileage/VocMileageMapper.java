package com.yonyou.dmscus.customer.dao.vocmileage;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.vocmileage.VocMileageDTO;
import com.yonyou.dmscus.customer.entity.po.vocmileage.VocMileagePO;

/**
 * <p>
 * VOC里程基础表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-08
 */
public interface VocMileageMapper extends IMapper<VocMileagePO> {

    /**
     * 功能描述: <br>
     * 〈功能详细描述〉 VOC里程分页查询信息
     * 
     * @param page
     * @param queryParams
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    List<VocMileageDTO> queryVocMileagePageInfo(Page page, @Param("params") Map<String, Object> queryParams);

    /**
     * 功能描述: <br>
     * 〈功能详细描述〉 查询VOC里程信息
     * 
     * @param queryParams 查询参数
     * @return 反结算单列表
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    List<VocMileageDTO> selectListBySql(@Param("params") Map<String, Object> queryParams);
    
    
    /**
           * 批量插入VOC里程
    * <AUTHOR>
    * @date 2020年7月8日
    * @param insertList
    */ 	
    public int insertBatchData(@Param("insertList") List<VocMileagePO> insertList);

    public List<VocMileagePO> getAllVocVeh();

    VocMileagePO getVocMileageLast(@Param("vin")String vin);

    VocMileagePO getVocMileageByInterval(@Param("vin")String vin,@Param("getTime") Date getTime,@Param("mileageKm")
            Integer mileageKm,@Param("interval") int interval);

    List<VocMileagePO> getVocMileage(@Param("createDate")String createDate);
}
