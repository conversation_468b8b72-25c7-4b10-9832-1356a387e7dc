package com.yonyou.dmscus.customer.dao.goodwill;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwilApplyAuditProcessPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 亲善审批流程表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-22
 */
public interface GoodwilApplyAuditProcessMapper extends IMapper<GoodwilApplyAuditProcessPO> {
	public List<GoodwilApplyAuditProcessPO> selectByGoodwillApplyId(Long goodwillApplyId);

	public List<Map> queryReturnTo(@Param("goodwillApplyId") Long goodwillApplyId,
			@Param("auditObject") Integer auditObject);

	int updateProcess(@Param("goodwillApplyId") Long goodwillApplyId, @Param("id") Integer id,
			@Param("auditObject") Integer auditObject);

	public List<Map> queryAuditProcess(@Param("auditObject") Integer auditObject,
			@Param("goodwillApplyId") Long goodwillApplyId);

	public GoodwilApplyAuditProcessPO selectNextAudit(Long goodwillApplyId);

	int updateStatusById(@Param("goodwillApplyId") Long goodwillApplyId);

	// 迁移历史数据：根据亲善申请单ID更新流程审批时间 add 20210721
	public int updateGoodwillFlowAuditTime(@Param("goodwillApplyId") Long goodwillApplyId);
}
