package com.yonyou.dmscus.customer.dao.faultLight;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightFollowRecordDTO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightFollowRecordPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TtFaultLightFollowRecordMapper extends IMapper<TtFaultLightFollowRecordPO> {

    List<FaultLightFollowRecordDTO> queryFollowRecord(@Param("params")FaultLightFollowRecordDTO  params);

    void updateFollowRecord(@Param("clueState")Integer clueState,@Param("params")TtFaultLightFollowRecordPO params);

    void batchInsert(@Param("params")List<TtFaultLightFollowRecordPO> params);
}
