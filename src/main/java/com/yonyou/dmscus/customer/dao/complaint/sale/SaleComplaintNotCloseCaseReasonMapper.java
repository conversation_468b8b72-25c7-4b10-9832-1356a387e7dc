package com.yonyou.dmscus.customer.dao.complaint.sale;

    
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintNotCloseCaseReasonPO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintNotCloseCaseReasonTestPO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintNotCloseCaseReasonPO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 5日未结案原因 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
public interface SaleComplaintNotCloseCaseReasonMapper extends IMapper<SaleComplaintNotCloseCaseReasonPO> {

    List<ComplaintNotCloseCaseReasonTestPO> selectPageBySql2(Page page, @Param("params")ComplaintNotCloseCaseReasonTestPO complaintNotCloseCaseReasonTestPo);

    List<SaleComplaintNotCloseCaseReasonPO> selectfollowTime(Long id);
}
