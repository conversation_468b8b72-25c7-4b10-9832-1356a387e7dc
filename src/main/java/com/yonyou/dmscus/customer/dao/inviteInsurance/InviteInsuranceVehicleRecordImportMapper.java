package com.yonyou.dmscus.customer.dao.inviteInsurance;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleOwnerInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordImportDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.UserInfoOutDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordImportPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 车辆续保线索导入临时表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-19
 */
public interface InviteInsuranceVehicleRecordImportMapper extends IMapper<InviteInsuranceVehicleRecordImportPO> {

    void deleteAll(@Param("userId")Long userId);// 删除临时表数据

    void insertInsuranceImportData(@Param("tmpDtoList") List<InviteInsuranceVehicleRecordImportPO> tmpDtoList, @Param("userId")Long userId);

    void updateCheckVinEmpty(@Param("userId")Long userId);

    void updateCheckDateEmpty(@Param("userId")Long userId);

    List<InviteInsuranceVehicleRecordImportPO> getSuccessData(@Param("userId")Long userId);

    void deleteVehicleOwnerByUserId(@Param("userId")Long userId);// 删除临时表数据

    void insertVehicleOwner(@Param("vinList") List<InviteInsuranceVehicleOwnerInfoDTO> vinList, @Param("userId")Long userId);

    void deleteRoleUserByUserId(@Param("userId")Long userId);// 删除临时表数据

    void insertRoleUser(@Param("userList") List<UserInfoOutDTO> vinList, @Param("userId")Long userId);

    void updateExistVinOnMiddle(@Param("userId")Long userId);

    void updateImportInsuranceRecord(@Param("userId")Long userId);

    int querySuccessCount(@Param("userId")Long userId);

    List<InviteInsuranceVehicleRecordImportDTO> selectImportSuccessInsuranceRecord(Page page, @Param("userId")Long userId);

    List<InviteInsuranceVehicleRecordImportDTO> selectImportErrorInsuranceRecord(Page page, @Param("userId")Long userId);

    void updateBeforeViInsuranceRecord(@Param("viList") List<InviteInsuranceVehicleRecordImportPO> viList,@Param("userId")Long userId);

    void updateBeforeClivtaInsuranceRecord(@Param("clivtaList") List<InviteInsuranceVehicleRecordImportPO> clivtaList,@Param("userId")Long userId);

    int importViInsuranceData(@Param("viList") List<InviteInsuranceVehicleRecordImportPO> viList,
                               @Param("userId")Long userId,@Param("viDay")Integer viDay);

    int importClivtaInsuranceData(@Param("clivtaList") List<InviteInsuranceVehicleRecordImportPO> clivtaList,
                                   @Param("userId")Long userId,@Param("cliDay")Integer cliDay);

    void updateInsuranceRecordData(@Param("vinList") List<String> vinList, @Param("userId")Long userId,
                                   @Param("dealerCode")String dealerCode);
}
