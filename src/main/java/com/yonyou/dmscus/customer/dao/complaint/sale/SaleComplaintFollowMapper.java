package com.yonyou.dmscus.customer.dao.complaint.sale;

    
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintFollowPO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 销售客户投诉跟进表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-11
 */
public interface SaleComplaintFollowMapper extends IMapper<SaleComplaintFollowPO> {

    List<SaleComplaintFollowPO> selectListByDealer(@Param("params")SaleComplaintFollowPO saleComplaintFollowPO);

    List<SaleComplaintFollowPO> selectListByVcdc(@Param("params")SaleComplaintFollowPO saleComplaintFollowPO);

    List<ComplaintInfMoreDTO> queryNextFollowing();
}
