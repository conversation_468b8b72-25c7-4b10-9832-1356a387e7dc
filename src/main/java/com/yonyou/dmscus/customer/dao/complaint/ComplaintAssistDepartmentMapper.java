package com.yonyou.dmscus.customer.dao.complaint;

    
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.*;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistDepartmentPO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 协助部门 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface ComplaintAssistDepartmentMapper extends IMapper<ComplaintAssistDepartmentPO> {
    /**
     * 查询Assist
     * @param var1
     * @param complaintAssistPo
     * @return
     */
    List<ComplaintAssistPO> selectAssist(Page var1, @Param("params")ComplaintAssistPO complaintAssistPo);

    /**
     * 通过id查询
     * @param complaintAssistPo
     * @return
     */
    List<ComplaintAssistPO> selectAssistByid( @Param("params")ComplaintAssistPO complaintAssistPo);

    /**
     * 更新
     * @param id
     */
    void updatestatus(long id);
    /**
     * 更新
     * @param id
     */
    void updatestatus1(long id);
    /**
     * 更新
     * @param queryParam
     */
    void updateIsfinish(Map<String, Object> queryParam);
    /**
     * 更新
     * @param queryParam
     */
    void updateIsfinish1(Map<String, Object> queryParam);

    /**
     * 查询是否被分配到客诉单（质量部）
     * @param complaintAssistPO
     * @return
     */
    List<ComplaintAssistPO> selectAssistValidByid( @Param("params")ComplaintAssistPO complaintAssistPO);
    /**
     * 更新
     * @param queryParam
     */
    void updateIsfinishByDealer(Map<String, Object> queryParam);
    /**
     * 更新
     * @param queryParam
     */
    void updateIsfinishByDealer1(Map<String, Object> queryParam);

    /**
     * 查询是否被分配过
     * @param complaintAssistDTO
     * @return
     */
    List<ComplaintAssistDepartmentDTO> selectAssistList( @Param("params") ComplaintAssistDTO complaintAssistDTO);

    void setAssistDepartmentNotEidt(long id);


    ComplaintInfoDTO  selectNo(Long complaintInfoId);

}
