package com.yonyou.dmscus.customer.dao.complaint.sale;

    
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.AttcahmentUpdateDto;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintAttachmentDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfMorePO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintAttachmentPO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 销售客户投诉附件 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-11
 */
public interface SaleComplaintAttachmentMapper extends IMapper<SaleComplaintAttachmentPO> {

    List<ComplaintAttachmentTestDTO> selectListBySql1(@Param("params") SaleComplaintAttachmentDTO saleComplaintAttachmentDTO);

    void updateAttachment ( @Param("params")AttcahmentUpdateDto attcahmentUpdateDto);


}
