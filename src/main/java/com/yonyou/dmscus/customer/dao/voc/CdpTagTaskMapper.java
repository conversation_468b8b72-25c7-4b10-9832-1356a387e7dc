package com.yonyou.dmscus.customer.dao.voc;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.dto.cdp.CdpTagTaskParameterDto;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.CdpTagTaskPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 */
public interface CdpTagTaskMapper extends IMapper<CdpTagTaskPo> {

    List<String> selectTaskList(Page page, @Param("startTime")String startTime, @Param("endTime")String endTime);

    Integer selectCountCdpTag(@Param("whiteList") List<String> whiteList, @Param("startTime")String startTime, @Param("endTime")String endTime);
    Integer selectTaskCount(@Param("startTime")String startTime, @Param("endTime")String endTime);

    void insertCdpTagTask(@Param("vinList") List<String> vinList);

    void updateTagTask(@Param("list") List<String> list,@Param ("message") String message);

    void updateTagTaskError(@Param("vinList") List<CdpTagTaskParameterDto> vinList,@Param ("message") String message);

    List<String> selectInviteStatusPlan(Page page,@Param("whiteList") List<String> whiteList, @Param("startTime")String startTime, @Param("endTime")String endTime);
    /**
     * 批量插入故障灯高亮线索id
     */
    void batchInsertHighlightFlagClueId(@Param("clueId") List<Long> clueId);

    Integer queryFaultLightClueTotal();

    List<CdpTagTaskPo> queryFaultLightClueId(Page page);

    void updateFaultLightClueById(@Param("listItem") List<Long> listItem);

    void updateErrorTask(@Param("listItem") List<CdpTagTaskPo> cdpTagTaskPos);

    void updateCdpTagTask(@Param("vinList") List<CdpTagTaskParameterDto> vinList);

    void insertCdpTagTaskError(@Param("vinList") List<String> vinList,@Param("sinceType") int sinceType,@Param ("message") String message);

    void updateCdpTagTaskSuccess(@Param("list") List<String> list);

    void updateTagTaskErrorByBizNo(@Param("list") List<String> list,@Param ("message") String message);

}
