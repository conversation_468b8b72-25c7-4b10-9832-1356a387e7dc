package com.yonyou.dmscus.customer.dao.inviteInsurance;


import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceCustomerInfoDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceCustomerInfoPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 续保呼叫登记自建联系人 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-15
 */
public interface InviteInsuranceCustomerInfoMapper extends IMapper<InviteInsuranceCustomerInfoPO> {

    List<InviteInsuranceCustomerInfoDTO> selectAllInsuranceCustomerInfo(@Param("insuranceId")Long insuranceId,@Param("dealerCode")String dealerCode);

    void updateInsuranceCustomerInfo(@Param("params")InviteInsuranceCustomerInfoDTO infoDTO,@Param("userId")Long userId);

    void deleteInsuranceCustomerInfo(@Param("tiicId")Long tiicId);
}
