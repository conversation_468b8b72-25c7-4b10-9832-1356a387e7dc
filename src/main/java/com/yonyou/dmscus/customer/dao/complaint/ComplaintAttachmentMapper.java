package com.yonyou.dmscus.customer.dao.complaint;

    
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.AttcahmentUpdateDto;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentTestDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAttachmentPO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客户投诉附件 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface ComplaintAttachmentMapper extends IMapper<ComplaintAttachmentPO> {
    /**
     * 分页查询
     * @param var1
     * @param complaintAttachmentPo
     * @return
     */
    List<ComplaintAttachmentPO> selectPageBySql1(Page var1, @Param("params")ComplaintAttachmentPO complaintAttachmentPo);

    /**
     * 集合查询
     * @param complaintAttachmentPo
     * @return
     */
    List<ComplaintAttachmentTestDTO> selectListBySql1(@Param("params") ComplaintAttachmentPO complaintAttachmentPo);

    /**
     * 更新
     * @param attcahmentUpdateDto
     */
    void updateAttachment( @Param("params") AttcahmentUpdateDto attcahmentUpdateDto);

    /**
     * 删除
     * @param id
     */
    void deleteAct(Long id);
}
