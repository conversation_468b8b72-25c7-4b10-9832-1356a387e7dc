package com.yonyou.dmscus.customer.dao.accidentClues;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentClueContact;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesContactsPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/25 16:04
 * @Version 1.0
 */
public interface AccidentCluesContactsMapper extends IMapper<AccidentCluesContactsPO> {

    /**
     * 批量保存联系人
     * @param dealerCode
     * @param acId
     * @param contactList
     */
    void saveBatch(@Param("dealerCode")String dealerCode, @Param("acId")Long acId, @Param("contactList") List<AccidentClueContact> contactList);

    /**
     * 获取联系人信息列表
     * @param dealerCode
     * @param acIdList
     * @param isOwner
     * @return
     */
    List<AccidentClueContact> getAccidentClueContacts(@Param("dealerCode")String dealerCode, @Param("acIdList")List<Long> acIdList, @Param("isOwner")Integer isOwner);

    /**
     * 查询联系人对应线索列表
     * @param param
     * @return
     */
    List<Integer> queryAcIdByContactInfo(@Param("param")String param);
}
