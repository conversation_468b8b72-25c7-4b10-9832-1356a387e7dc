package com.yonyou.dmscus.customer.dao.accidentClues;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.dto.CustomerInfoListReturnDTO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentClueVO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesDTO;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesPO;
import com.yonyou.dmscus.customer.entity.vo.AccidentClueDashBoardVO;
import com.yonyou.dmscus.customer.entity.vo.HistogramDataVO;
import com.yonyou.dmscus.customer.entity.vo.accidentClues.AccidentClueFollowVo;

import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/11/16 0016
 */
public interface AccidentCluesMapper extends IMapper<AccidentCluesPO> {

	List<Map> exportExcelAccident(@Param("page") Page page, @Param("params")AccidentCluesPO po);

	void batchInsert(@Param("userId") Long userId);

	Integer getCountByLicense(@Param("license") String license,@Param("dealerCode") String dealerCode,@Param("reportDate") Date reportDate);

	void updateOneId(@Param("oneIdList")List<CustomerInfoListReturnDTO> oneIdList);

	void updateTimeOut();

	void updateTimeOutClues(@Param("expireDay")Integer expireDay);

	//逾期未跟进
	List<AccidentCluesPO> selectTimeoutNotFollow();

	//预约未进店
	List<AccidentCluesPO> appointmentNotInto();

	//预约提前提醒
	List<AccidentCluesPO> nextAppintmentDataAccident();

	void updateRmind();

	/**
	 * 线索列表查询
	 * @param page
	 * @param params
	 * @return
	 */
	IPage<AccidentClueVO> getList(@Param("page") Page page, @Param("params")AccidentClueVO params, @Param("userList")List<Long> userList, @Param("contactAcIds")List<Integer> contactAcIds, @Param("limitDate")String limitDate, @Param("orderByStr")String orderByStr);

	AccidentClueFollowVo followCount(@Param("params") AccidentClueVO params, @Param("contactAcIds") List<Integer> contactAcIds, @Param("limitDate") String limitDate);


	long selectCountBySql(@Param("ownerCode") String ownerCode, @RequestParam("userList") List<Long> userList, @Param("limitDate")String limitDate);

	List<AccidentCluesPO> selectPageSql(Page page, @Param("params")AccidentCluesPO transDtoToPo, @Param("dto") AccidentCluesDTO dto);

	/*
	* 查询用户被分配了多少线索
	* */
    List<Map<String, Object>> getUserAllotCount(@Param("userSet")Set<Long> userSet,@Param("ownerCode") String ownerCode);

	/**
	 * 提前三十分钟提醒列表
	 * @return
	 */
	List<AccidentCluesPO> advanceFollowList();

	/**
	 * 获取超时未进厂线索
	 * @return
	 */
	List<AccidentCluesPO> getAppointmentTimeOutList();

	/**
	 * 更新已提醒线索
	 * @param ids
	 */
	void updateClueRemind(@Param("ids")List<Integer> ids);

	/**
	 * 线索重复校验
	 * @param license
	 * @param dealerCode
	 * @param reportDate
	 * @return
	 */
	Integer checkClueIfExists(@Param("license") String license,@Param("dealerCode") String dealerCode,@Param("reportDate") Date reportDate);

	/**
	 * 看板信息查询
	 * @param param
	 * @return
	 */
	AccidentClueDashBoardVO queryDashBoardInfo(@Param("dealerCode")String dealerCode, @Param("followUser")Long followUser, @Param("beginDate")String beginDate, @Param("endDate")String endDate);

	/**
	 * 看板柱状图
	 * @param dealerCode
	 * @param followUser
	 * @param beginDate
	 * @param endDate
	 * @return
	 */
	List<HistogramDataVO> queryDashBoardDayDetail(@Param("dealerCode")String dealerCode, @Param("followUser")Long followUser, @Param("beginDate")String beginDate, @Param("endDate")String endDate);

	/**
	 * 看板柱状图
	 * @param dealerCode
	 * @param followUser
	 * @param beginDate
	 * @param endDate
	 * @return
	 */
	List<HistogramDataVO> queryDashBoardMonthDetail(@Param("dealerCode")String dealerCode, @Param("followUser")Long followUser, @Param("beginDate")String beginDate, @Param("endDate")String endDate);

	/**
	 * 门店看板信息查询
	 * @param dealerCode
	 * @param beginDate
	 * @param endDate
	 * @return
	 */
	AccidentClueDashBoardVO queryDealerDashBoardInfo(@Param("dealerCode")String dealerCode, @Param("beginDate")String beginDate, @Param("endDate")String endDate);

	/**
	 * 看板柱状图
	 * @param dealerCode
	 * @param beginDate
	 * @param endDate
	 * @return
	 */
	List<HistogramDataVO> queryDashBoardUserDetail(@Param("dealerCode")String dealerCode, @Param("beginDate")String beginDate, @Param("endDate")String endDate);

	/**
	 * 看板柱状图
	 * @param dealerCode
	 * @param beginDate
	 * @param endDate
	 * @return
	 */
	List<HistogramDataVO> queryDealerDashBoardDayDetail(@Param("dealerCode")String dealerCode, @Param("beginDate")String beginDate, @Param("endDate")String endDate);

	/**
	 * 看板柱状图
	 * @param dealerCode
	 * @param beginDate
	 * @param endDate
	 * @return
	 */
	List<HistogramDataVO> queryDealerDashBoardMonthDetail(@Param("dealerCode")String dealerCode, @Param("beginDate")String beginDate, @Param("endDate")String endDate);

	//修改预约单号
	Integer updateBookingOrderNo(@Param("crmList")List<String> crmList,@Param("bookingOrderNo")String bookingOrderNo,@Param("followStatus")Integer followStatus);

    List<AccidentCluesDTO> selectClueWithoutExt();

}
