package com.yonyou.dmscus.customer.dao.invitationKanban;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.invitationKanban.InvitationKanbanInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationKanban.InvitationKanbanQueryDTO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 车辆邀约记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
public interface InvitationKanbanMapper  {

    List<InvitationKanbanInfoDTO> getInvitationKanbanInfoByDealerCode(@Param("params")InvitationKanbanQueryDTO query);

    List<InvitationKanbanInfoDTO> getInvitationKanbanInfoByDealers(@Param("params") InvitationKanbanQueryDTO query);

    List<InvitationKanbanInfoDTO> getInvitationKanbanInfo(@Param("params")InvitationKanbanQueryDTO query);
}
