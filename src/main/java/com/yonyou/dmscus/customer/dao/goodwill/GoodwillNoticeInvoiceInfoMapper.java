package com.yonyou.dmscus.customer.dao.goodwill;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillNoticeInvoiceInfoPO;

/**
 * <p>
 * 亲善管理通知开票信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-17
 */
public interface GoodwillNoticeInvoiceInfoMapper extends IMapper<GoodwillNoticeInvoiceInfoPO> {
	public GoodwillNoticeInvoiceInfoPO queryPaymentInfoByGoodwillApplyId(
			@Param("goodwillApplyId") Long goodwillApplyId);

	public int updateStatus(@Param("invoiceId") String invoiceId);

	List<GoodwillNoticeInvoiceInfoPO> queryLastNoticeInoviceInfo(@Param("goodwillApplyId") Long goodwillApplyId);

	public GoodwillNoticeInvoiceInfoPO queryVoucherByGoodwillApplyId(@Param("goodwillApplyId") Long goodwillApplyId);

	/**
	 * 查询 consume_id非空的所有数据
	 * @return
	 */
	public List<GoodwillNoticeInvoiceInfoPO> queryConsumeList();

	public List<GoodwillNoticeInvoiceInfoPO> queryNoticeInvoice(@Param("goodwillApplyId")  Long goodwillApplyId);


}
