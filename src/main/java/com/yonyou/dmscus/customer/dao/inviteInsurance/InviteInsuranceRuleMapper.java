package com.yonyou.dmscus.customer.dao.inviteInsurance;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceRulePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 邀约续保规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
public interface InviteInsuranceRuleMapper extends IMapper<InviteInsuranceRulePO> {

    List<InviteInsuranceRulePO> getInviteInsuranceRuleDlr(@Param("params") InviteInsuranceRulePO po);

    InviteInsuranceRulePO getViInviteInsuranceRuleExist(@Param("dealerCode")String dealerCode);

    InviteInsuranceRulePO getClivtaInviteInsuranceRuleExist(@Param("dealerCode")String dealerCode);

    List<InviteInsuranceRulePO> getViInviteInsuranceRuleCount(@Param("dealerCode")String dealerCode);

    List<InviteInsuranceRulePO> getClivtaInviteInsuranceRuleCount(@Param("dealerCode")String dealerCode);

    List<InviteInsuranceRulePO> getInviteInsuranceRuleVcdc(@Param("dealerCode")String dealerCode);

    List<InviteInsuranceRulePO> selectListBySqlVcdc(@Param("params") InviteInsuranceRulePO rulePO);

    int updateAllViInsuranceRule(@Param("userId")Long userId,@Param("params") InviteInsuranceRulePO rulePO);

    int updateAllClivtaInsuranceRule(@Param("userId")Long userId,@Param("params") InviteInsuranceRulePO rulePO);

    InviteInsuranceRulePO getViInviteInsuranceRuleDlr(@Param("dealerCode")String dealerCode);

    InviteInsuranceRulePO getClivtaInviteInsuranceRuleDlr(@Param("dealerCode")String dealerCode);

}
