package com.yonyou.dmscus.customer.dao.goodwill;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillInvoiceTitleInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillInvoiceTitleInfoPO;

/**
 * <p>
 * 亲善发票抬头信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-17
 */
public interface GoodwillInvoiceTitleInfoMapper extends IMapper<GoodwillInvoiceTitleInfoPO> {
	@SuppressWarnings("rawtypes")
	List<GoodwillInvoiceTitleInfoPO> selectPageBySql(Page page,
			@Param("params") GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO);

	// 根据开票抬头查询发票信息
	int queryGoodwillInvoiceTitleInfoByInvoiceTitle(@Param("invoiceTitle") Integer invoiceTitle, @Param("id") Long id);

	public GoodwillInvoiceTitleInfoPO queryInvoiceInfo(@Param("invoiceTitle") Integer invoiceTitle);

}
