package com.yonyou.dmscus.customer.dao.userCode;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.dto.UserCodeVo;
import com.yonyou.dmscus.customer.entity.dto.userCode.UserCodeInfoDto;
import com.yonyou.dmscus.customer.entity.dto.userCode.UserCodeInfoImportDto;
import com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskImportPO;
import com.yonyou.dmscus.customer.entity.po.userCode.UserCodeInfoImportPo;
import com.yonyou.dmscus.customer.entity.po.userCode.UserCodeInfoPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 话术关键词与话术关系 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-27
 */
public interface UserCodeInfoImportMapper extends IMapper<UserCodeInfoImportPo> {


    List<UserCodeInfoImportPo> selectErrorPage(Page<UserCodeInfoImportDto> page, @Param("userId") Long userId);

    void deleteByCreatedBy(@Param("userId")String userId);

    void bulkInsert(@Param("addList")List<UserCodeInfoImportPo> insertList,@Param("userId") Long userId);

    List<UserCodeInfoImportDto> queryError(@Param("userId")Long userId);

    List<UserCodeInfoImportDto> querySuccess(@Param("userId")Long userId);

    int querySucessCount(@Param("userId")Long userId);
}
