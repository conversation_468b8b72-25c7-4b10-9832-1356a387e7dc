package com.yonyou.dmscus.customer.dao.goodwill;

    
import java.util.List;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairPartInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyRepairPartInfoPO;

/**
 * <p>
 * 亲善预申请子表——维修零配件信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
public interface GoodwillApplyRepairPartInfoMapper extends IMapper<GoodwillApplyRepairPartInfoPO> {
	public List<GoodwillApplyRepairPartInfoDTO> getSupportApplyPartInfoById(Long goodwillApplyId);
}
