package com.yonyou.dmscus.customer.dao.voicemanage;


import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaWorkNumberPO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * AI语音工作号管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
public interface SaWorkNumberMapper extends IMapper<SaWorkNumberPO> {

    /**
     * 校验输入的AI语音工作号是否已有绑定记录
     * @param saWorkNumberPO
     * @return
     */
    Integer checkWorkNumber(@Param("params")SaWorkNumberPO saWorkNumberPO);

    /**
     * 校验输入的跟进人员手机号是否已绑定AI语音工作号
     * @param saWorkNumberPO
     * @return
     */
    Integer checkSaNumber(@Param("params")SaWorkNumberPO saWorkNumberPO);

    /**
     * 校验跟进人员是否已存在
     * @param saWorkNumberPO
     * @return
     */
    Integer hasExistSa(@Param("params")SaWorkNumberPO saWorkNumberPO);
}
