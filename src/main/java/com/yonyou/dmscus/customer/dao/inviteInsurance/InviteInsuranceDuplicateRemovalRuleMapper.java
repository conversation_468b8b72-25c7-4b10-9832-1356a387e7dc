package com.yonyou.dmscus.customer.dao.inviteInsurance;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceDuplicateRemovalRulePO;

/**
 * <p>
 * 续保去重规则表，本章表所存储的数据，应该都是一个默认值。 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
public interface InviteInsuranceDuplicateRemovalRuleMapper extends IMapper<InviteInsuranceDuplicateRemovalRulePO> {

}
