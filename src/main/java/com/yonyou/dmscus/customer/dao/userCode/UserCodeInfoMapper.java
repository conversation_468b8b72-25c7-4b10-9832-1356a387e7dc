package com.yonyou.dmscus.customer.dao.userCode;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.dto.UserCodeVo;
import com.yonyou.dmscus.customer.entity.dto.qb.QbVehicleInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.userCode.UserCodeInfoDto;
import com.yonyou.dmscus.customer.entity.dto.userCode.UserCodeInfoImportDto;
import com.yonyou.dmscus.customer.entity.po.userCode.UserCodeInfoImportPo;
import com.yonyou.dmscus.customer.entity.po.userCode.UserCodeInfoPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 话术关键词与话术关系 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-27
 */
public interface UserCodeInfoMapper extends IMapper<UserCodeInfoPo> {

    Integer getCountByUserCode(@Param("userCode") String userCode);
    List<UserCodeInfoDto> selectPageBySql(Page page, @Param("params") UserCodeVo vo);

    void batchInsert(@Param("userId") Long userId);

    Integer selectCountByUserCode(@Param("userCode")String userCode);
}
