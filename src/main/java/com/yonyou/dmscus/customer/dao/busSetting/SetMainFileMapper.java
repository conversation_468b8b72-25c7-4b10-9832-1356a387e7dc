package com.yonyou.dmscus.customer.dao.busSetting;


import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.busSetting.SetMainFileDetailVO;
import com.yonyou.dmscus.customer.entity.dto.busSetting.SetMainFileVO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.VehicleDTO;
import com.yonyou.dmscus.customer.entity.po.busSetting.SetMainFilePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * 套餐主档 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
public interface SetMainFileMapper extends IMapper<SetMainFilePO> {


    List<SetMainFileVO> querySetMainFileListNew(
            @Param("params") Map<String, Object> toMaps);

    List<SetMainFileDetailVO> querySetMainFileDetail(@Param("id")Long id);

}
