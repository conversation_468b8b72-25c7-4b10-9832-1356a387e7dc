package com.yonyou.dmscus.customer.dao.goodwill;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAwaPrintMaintainPO;

/**
 * <p>
 * 亲善AWA打印模板维护 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-23
 */
public interface GoodwillAwaPrintMaintainMapper extends IMapper<GoodwillAwaPrintMaintainPO> {
	List<Map> selectAwaPrintMaintainInfo(@Param("params") GoodwillAwaPrintMaintainPO goodwillAwaPrintMaintainPo);

}
