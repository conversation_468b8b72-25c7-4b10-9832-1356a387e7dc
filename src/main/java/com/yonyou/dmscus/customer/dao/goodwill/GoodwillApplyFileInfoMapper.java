package com.yonyou.dmscus.customer.dao.goodwill;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyFileInfoPO;

/**
 * <p>
 * 亲善预申请上传附件表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
public interface GoodwillApplyFileInfoMapper extends IMapper<GoodwillApplyFileInfoPO> {
	/**
	 * 集合查询
	 * 
	 * @param complaintAttachmentPo
	 * @return
	 */
	List<GoodwillApplyFileInfoPO> selectListBySql1(@Param("params") GoodwillApplyFileInfoPO goodwillApplyFileInfoPo);

	/**
	 * 查询材料上传
	 * 
	 * @param complaintAttachmentPo
	 * @return
	 */
	List<GoodwillApplyFileInfoPO> selectMaterialUploadList(
			@Param("params") GoodwillApplyFileInfoPO goodwillApplyFileInfoPo);

	/**
	 * 查询ceo附件上传
	 * 
	 * @return
	 */
	int selectCeoFile(@Param("applyNo") String applyNo);

	/**
	 * 查询vp附件上传
	 * 
	 * @return
	 */
	int selectVpFile(@Param("applyNo") String applyNo);

	/**
	 * 查询vp和CEO附件上传
	 * 
	 * @return
	 */
	int selectVpAndCeoFile(@Param("applyNo") String applyNo);
}
