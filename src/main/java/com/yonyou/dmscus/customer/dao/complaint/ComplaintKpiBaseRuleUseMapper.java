package com.yonyou.dmscus.customer.dao.complaint;

    
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRuleUsePO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;

import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRuleUseTestPO;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * <p>
 * 客户投诉KP基础规则使用表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
public interface ComplaintKpiBaseRuleUseMapper extends IMapper<ComplaintKpiBaseRuleUsePO> {
    /**
     * 分页查询
     * @param var1
     * @param complaintKpiBaseRuleUseTestPo
     * @return
     */
    List<ComplaintKpiBaseRuleUseTestPO> selectPageBySql1(Page var1, @Param("params") ComplaintKpiBaseRuleUseTestPO complaintKpiBaseRuleUseTestPo);

    /**
     * 查询list集合
     * @param complaintKpiBaseRuleUseTestPo
     * @return
     */
    List<ComplaintKpiBaseRuleUseTestPO> selectListBySql1(@Param("params")ComplaintKpiBaseRuleUseTestPO complaintKpiBaseRuleUseTestPo) ;
}
