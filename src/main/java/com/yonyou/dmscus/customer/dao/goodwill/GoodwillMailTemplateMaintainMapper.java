package com.yonyou.dmscus.customer.dao.goodwill;

import org.apache.ibatis.annotations.Param;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMailTemplateMaintainPO;

/**
 * <p>
 * 亲善邮件模板维护 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-28
 */
public interface GoodwillMailTemplateMaintainMapper extends IMapper<GoodwillMailTemplateMaintainPO> {
	GoodwillMailTemplateMaintainPO selectTemplateMaintain(@Param("mailType") Integer mailType);
}
