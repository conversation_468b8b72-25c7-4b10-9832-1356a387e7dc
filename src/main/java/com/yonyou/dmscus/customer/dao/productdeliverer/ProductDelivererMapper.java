package com.yonyou.dmscus.customer.dao.productdeliverer;


import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.productdeliverer.ProductDelivererPO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 主单经销商送修人信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-02
 */
public interface ProductDelivererMapper extends IMapper<ProductDelivererPO> {

    ProductDelivererPO queryProductDeliverer(@Param("vin")String vin,@Param("dealerCode") String dealerCode);
}
