package com.yonyou.dmscus.customer.dao.inviteInsurance;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordDetailPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 车辆邀约续保记录明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
public interface InviteInsuranceVehicleRecordDetailMapper extends IMapper<InviteInsuranceVehicleRecordDetailPO> {

    List<InviteInsuranceVehicleRecordDetailPO> getInviteInsuranceVehicleRecordInfoDlr(@Param("id") Long id);

    List<InviteInsuranceVehicleRecordDetailPO> selectFollowInviteInsureDetailHistory(Page page, @Param("vin") String vin, @Param("dealerCode") String
            dealerCode);

}
