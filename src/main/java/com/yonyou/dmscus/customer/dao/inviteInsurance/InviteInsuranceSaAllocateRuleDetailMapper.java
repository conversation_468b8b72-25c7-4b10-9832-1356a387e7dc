package com.yonyou.dmscus.customer.dao.inviteInsurance;


import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceSaAllocateRuleDetailPO;

/**
 * <p>
 * 续保SA分配规则明细表,通过经销商CODE与invite_insurance_sa_allocate_rule关联，用于经销商SA分配中平均分配 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
public interface InviteInsuranceSaAllocateRuleDetailMapper extends IMapper<InviteInsuranceSaAllocateRuleDetailPO> {

}
