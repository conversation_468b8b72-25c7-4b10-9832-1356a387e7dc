package com.yonyou.dmscus.customer.dao.complaint;

    
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRulePO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客户投诉KP基础规则 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
public interface ComplaintKpiBaseRuleMapper extends IMapper<ComplaintKpiBaseRulePO> {

    /**
     * 更新kpi
     * @param complaintKpiBaseRuleDTO
     * @return
     */
    int updateKpi( @Param("params") ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO);
}
