package com.yonyou.dmscus.customer.dao.pushRecord;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.pushRecord.RemotePushRecordPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/16 18:17
 * @Version 1.0
 */
public interface RemotePushRecordMapper extends IMapper<RemotePushRecordPO> {

    /**
     * 查询待补偿数
     * @param sinceType
     * @return
     */
    Integer countAcRemotePushCompensateList(@Param("sinceType")Integer sinceType);

    /**
     * 查询待补偿数据
     * @param sinceType
     * @param limit
     * @param offset
     * @return
     */
    List<RemotePushRecordPO> queryAcRemotePushCompensateList(@Param("sinceType")Integer sinceType, @Param("limit")Integer limit, @Param("offset")Integer offset);
}
