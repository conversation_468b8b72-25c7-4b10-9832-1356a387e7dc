package com.yonyou.dmscus.customer.dao.inviteRule;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InvitePartItemRulePO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;

/**
 * <p>
 * 邀约易损件和项目规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
public interface InvitePartItemRuleMapper extends BaseMapper<InvitePartItemRulePO> {

    Integer checkRepeat(@Param("id")Long id,@Param("code")String code,@Param("type") Integer type);

    List<InvitePartItemRulePO> selectPageBySql(Page var1, @Param("params") InvitePartItemRulePO var2);

    @Cacheable( value = "InvitePartItemRuleMapper_selectListBySql", key = "'InvitePartItemRuleMapper_selectListBySql_' + #var1")
    List<InvitePartItemRulePO> selectListBySql(@Param("params") InvitePartItemRulePO var1);
}
