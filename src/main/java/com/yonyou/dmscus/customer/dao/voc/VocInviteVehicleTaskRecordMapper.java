package com.yonyou.dmscus.customer.dao.voc;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.dto.cdp.CdpTagTaskParameterDto;
import com.yonyou.dmscus.customer.entity.dto.clue.dictionary.DictionaryIdDTO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocInviteVehicleTaskRecordPo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @title: VocInviteVehicleTaskRecordMapper
 * @projectName dmscus.customer
 * @description:
 * @date 2022/11/1814:45
 */
public interface VocInviteVehicleTaskRecordMapper extends IMapper<VocInviteVehicleTaskRecordPo> {
    Integer selectVocCount(@Param("vin") String vin, @Param("id") Long id);
    Integer selectVocCountT(@Param("vin") String vin, @Param("id") Long id);
    List<Long> selectTypeXIIByVin(@Param("vin") String vin);

    Integer selectLossWarningByVin(@Param("vin") String vin);

    Integer selectTaskRecordByInviteId(@Param("recordId") Long recordId);

    int insertList(@Param("list") List<VocInviteVehicleTaskRecordPo> list);

    int updateByPo(@Param("taskId") Long taskId, @Param("recordId") Long recordId);

    int updateRecordNum(@Param("recordId")Long inviteId);

    int selectCountByIcmId(@Param("icmId")Long icmId);

    VocInviteVehicleTaskRecordPo selectIcmIdByRecordId(@Param("recordId")Long recordId);

    /**通过线索ID修改验证状态*/
    int updateVerifyStatusById(@Param("list")List<Long> list, @Param("verifyStatus")Integer verifyStatus, @Param("verifyTime")LocalDate verifyTime);

    /**维护历史数据验证状态-逾期状态*/
    int updateHistoryValidationStatusByOverdue();
    /**维护历史数据验证状态-完成状态(完成时间为null)*/
    int updateHistoryValidationStatusByComplete();
    /**维护历史数据验证状态-完成状态(完成时间不为null)*/
    int updateHistoryValidationStatusByNoNull();

    /**修改线索类型,流失类型字段所对应的字典表start*/
    /**分页查询*/
    DictionaryIdDTO selectIdByLimit(@Param("startPage")int startPage, @Param("endPage")int endPage);
    /**修改语句*/
    void updateTypeById(@Param("minId")Long minId, @Param("maxId")Long maxId);
    /**修改线索类型,流失类型字段所对应的字典表end*/

    /**批量修改*/
    void updateVerifyStatusAllById(@Param("list") List<VocInviteVehicleTaskRecordPo> list);


    void updateDailyMile(@Param("vinList") List<CdpTagTaskParameterDto> vinList);


    void updateBevLeadList(@Param("idList") List<Long> idList);

    /**
     * 根据经销商+订单id 查询是否存在未完成的线索
     * @param dealerCode
     * @param detailId
     * @return
     */
    InviteVehicleRecordPO queryTtInviteVehicleRecordByOrderId(@Param("dealerCode") String dealerCode, @Param("detailId") String detailId);

    /**
     * 厂端自建线索扩展数据
     */
    void batchInsertExtRecord(@Param("list") List<VocInviteVehicleTaskRecordPo> list);
}
