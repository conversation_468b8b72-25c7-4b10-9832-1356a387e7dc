package com.yonyou.dmscus.customer.dao.inviteInsurance;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceSaAllocateRulePO;
import com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule.InviteSaAllocateRulePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 续保SA分配规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
public interface InviteInsuranceSaAllocateRuleMapper extends IMapper<InviteInsuranceSaAllocateRulePO> {

    List<InviteInsuranceSaAllocateRulePO> getInviteInsuranceSaAllocateRule(@Param("dealerCodes")List<String> dealerCodes);

}
