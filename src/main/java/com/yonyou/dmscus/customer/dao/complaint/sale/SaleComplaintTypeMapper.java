package com.yonyou.dmscus.customer.dao.complaint.sale;

    
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintTypePO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 销售投诉类型 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-10
 */
public interface SaleComplaintTypeMapper extends IMapper<SaleComplaintTypePO> {

    List<SaleComplaintTypePO> selectListFW (@Param("params") SaleComplaintTypePO saleComplaintTypePO);

    List<SaleComplaintTypePO> selectListNotFw(@Param("params")SaleComplaintTypePO saleComplaintTypePO);
}
