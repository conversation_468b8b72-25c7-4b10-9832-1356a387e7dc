package com.yonyou.dmscus.customer.dao.complaint;

    
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintFollowPO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客户投诉跟进表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface ComplaintFollowMapper extends IMapper<ComplaintFollowPO> {
    /**
     * 查询最新一条更近记录
     * @param complaintFollowPo
     * @return
     */
    List<ComplaintFollowPO> queryNewCus(ComplaintFollowPO complaintFollowPo);

    /**
     * 店端查询
     * @param complaintFollowPo
     * @return
     */
    List<ComplaintFollowPO> selectListByDealer(@Param("params")ComplaintFollowPO complaintFollowPo);

    /**
     * 厂端查询
     * @param complaintFollowPo
     * @return
     */
    List<ComplaintFollowPO> selectListByVcdc(@Param("params")ComplaintFollowPO complaintFollowPo);

    List<ComplaintInfMoreDTO> queryNextFollowing();
}
