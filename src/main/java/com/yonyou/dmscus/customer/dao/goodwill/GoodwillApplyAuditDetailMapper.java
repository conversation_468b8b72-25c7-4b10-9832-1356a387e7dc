package com.yonyou.dmscus.customer.dao.goodwill;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditDetailDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditDetailPO;

/**
 * <p>
 * 亲善审计明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-24
 */
public interface GoodwillApplyAuditDetailMapper extends IMapper<GoodwillApplyAuditDetailPO> {
	public List<GoodwillApplyAuditDetailDTO> queryAuditDetailInfo(@Param("auditId") Long auditId);
}
