package com.yonyou.dmscus.customer.dao.invitationautocreate;


import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.DailyMileageLogPO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 平均里程计算日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-13
 */
public interface DailyMileageLogMapper extends IMapper<DailyMileageLogPO> {

    void SetErrorlog(@Param("type") String type,@Param("errorMsg") String errorMsg,@Param("errorStackMsg")
            String errorStackMsg,@Param("vin") String vin);

    void insertDailyMileageLog(@Param("params") DailyMileageLogPO log);
}
