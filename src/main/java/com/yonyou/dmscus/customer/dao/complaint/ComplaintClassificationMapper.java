package com.yonyou.dmscus.customer.dao.complaint;

    
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintClassificationDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintClassificationPO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客诉工单分类表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-13
 */
public interface ComplaintClassificationMapper extends IMapper<ComplaintClassificationPO> {

    List<ComplaintClassificationDTO> selectComplaintCategory( @Param("params") ComplaintClassificationDTO  complaintClassificationDTO);

    List<ComplaintClassificationPO> getByIds(@Param("ids")List<Long> ids);

}
