package com.yonyou.dmscus.customer.dao.voicemanage;


import java.util.List;
import java.util.Map;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceCustomerInfoDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceCustomerInfoPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaCustomerNumberPO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * SA呼叫登记 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
public interface SaCustomerNumberMapper extends IMapper<SaCustomerNumberPO> {
	
	List<Map<String,Object>> selectYesterdayAll();

    SaCustomerNumberPO getSaCustomerNumber(Long inviteId);

    List<InviteInsuranceCustomerInfoDTO> selectAllInsuranceCustomerInfo(@Param("insuranceId")Long insuranceId, @Param("dealerCode")String dealerCode);

    void updateInsuranceCustomerInfo(@Param("params")InviteInsuranceCustomerInfoDTO infoDTO,@Param("userId")Long userId);

    void deleteInsuranceCustomerInfo(@Param("tiicId")Long tiicId);

    void insertPo(@Param("params")InviteInsuranceCustomerInfoPO po);
}
