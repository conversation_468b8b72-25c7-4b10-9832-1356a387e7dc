package com.yonyou.dmscus.customer.dao.accidentClues;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dto.CustomerInfoCenterDTO;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesImportPO;

public interface AccidentCluesImportMapper {

	
	/**
     * 根据创建人删除相应数据
     * @param createdBy
     */
    void deleteByCreatedBy(@Param("createdBy") String createdBy);

    void bulkInsert(@Param("addList")List<AccidentCluesImportPO> addList,@Param("userId") Long userId);

    List<AccidentCluesImportPO>   queryError(@Param("userId") Long userId);

    List<AccidentCluesImportPO> querySuccess(@Param("userId") Long userId);

    Integer querySucessCount(@Param("userId") Long userId);
    
    List<AccidentCluesImportPO> selectErrorPage(Page page,@Param("userId") Long userId);
    
    List<AccidentCluesImportPO> selectSuccessPage(Page page,@Param("userId") Long userId);
    
    void updateErrorById(@Param("userId")Long userId,@Param("list")List<String> list);
    
    
    void updateError(@Param("dealerCode") String dealerCode);
    
	List<CustomerInfoCenterDTO> selectPhone(@Param("userId") Long userId);

}
