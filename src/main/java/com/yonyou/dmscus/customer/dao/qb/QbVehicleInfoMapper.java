package com.yonyou.dmscus.customer.dao.qb;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.qb.QbVehicleInfoDTO;
import com.yonyou.dmscus.customer.entity.po.qb.QbVehicleInfoPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/10 0010
 */
public interface QbVehicleInfoMapper extends IMapper<QbVehicleInfoPO> {

    List<QbVehicleInfoDTO> selectPageBySql(Page page, @Param("params") QbVehicleInfoDTO qbVehicleInfoDTO);
}
