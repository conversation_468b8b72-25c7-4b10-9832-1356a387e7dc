package com.yonyou.dmscus.customer.dao.goodwill;

    
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFinalPartInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyFinalPartInfoPO;

/**
 * <p>
 * 亲善预申请最终解决方案配件信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
public interface GoodwillApplyFinalPartInfoMapper extends IMapper<GoodwillApplyFinalPartInfoPO> {
	public List<GoodwillApplyFinalPartInfoDTO> queryPartAmountInfo(@Param("goodwillType")Integer goodwillType,@Param("goodwillApplyId")Long goodwillApplyId);

}
