package com.yonyou.dmscus.customer.dao.goodwill;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAuditInfoPO;

/**
 * <p>
 * 亲善审批记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
public interface GoodwillAuditInfoMapper extends IMapper<GoodwillAuditInfoPO> {
	public List<Map> queryApplyHistory(@Param("auditObject") Integer auditObject,
			@Param("goodwillApplyId") Long goodwillApplyId);

	public List<Map> queryReturnList(@Param("params") GoodwillAuditInfoDTO goodwillAuditInfoDto);

	int insertAuditInfo(@Param("params") GoodwillAuditInfoDTO goodwillAuditInfoDto);

}
