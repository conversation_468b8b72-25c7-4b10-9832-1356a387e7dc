package com.yonyou.dmscus.customer.dao.voicemanage;


import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.SaCustomerNumberDTO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO;
import com.yonyou.dmscus.customer.utils.ai.CallDetailsChild;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 通话详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
public interface CallDetailsMapper extends IMapper<CallDetailsPO> {

    /**
     * 查询通话记录
     * @param inviteId
     * @return
     */
    List<CallDetailsPO> getCallDetails(@Param("detailId") String detailId);
    
    void insertCallDetails(@Param("allDetail") List<CallDetailsChild> allDetail);
    
    void updateTotalScore(@Param("updateList") List<CallDetailsPO> updateList);
    
    CallDetailsPO selectLatest(@Param("id") Long id, @Param("batchNo") String batchNo);

    CallDetailsPO selectIsCurrentMonth(@Param("id") Long id);

    /**
     * 获取没有拉取过通话录音的通话记录
     * @return
     */
    List<Map<String, Object>> getNotVoiceCallDetails();

    /**
     * 获取没有拉取过得分的通话记录
     * @return
     */
    List<Map<String, Object>> selectNotTotalScore();

    /**
     *
     * @param inviteId
     * @param detailId
     */
    void updateCallDetailsForDetailId(@Param("inviteId")Long inviteId,@Param("detailId") Long detailId);

    /**
     * 待更新ai通话记录
     * @param inviteId
     */
    List<Long> queryInviteCallDetails(@Param("inviteId")Long inviteId);

    /**
     * 更新ai通话记录
     * @param detailId
     * @param ids
     */
    void updateCallRecordsDetailId(@Param("detailId") Long detailId, @Param("ids")List<Long> ids);


    /**
     *
     * @param inviteId
     * @param isAi
     */
    void updateTwiceFollow(@Param("inviteId")Long inviteId,@Param("isAi") int isAi);

    /**
     * 查询事故线索通话记录
     * @param followId 跟进id
     * @return
     */
    List<CallDetailsPO> getAccidentCluesCallDetails(@Param("followId") Integer followId);
    
    
    /**
            * 查询保险跟进通话记录
     */
    List<CallDetailsPO> getDetailsByInsuranceDetailId(@Param("insuranceDetailId") Long insuranceDetailId);


    List<Map<String,Object>> selectInviteIdAndInvitetype(@Param("callId")String callId,@Param("sessionId")String sessionId);

    Map selectCallVoicePo(@Param("id") Long id);

    List<SaCustomerNumberDTO> selectSaCustomerNumber(String batchNo);

    List<SaCustomerNumberDTO> selectSaCustomerNumbers(@Param("yyIds") List<Long> yyIds, @Param("xbIds") List<Long> xbIds, @Param("gzdIds") List<Long> gzdIds);
}
