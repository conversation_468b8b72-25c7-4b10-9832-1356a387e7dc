package com.yonyou.dmscus.customer.dao.invitationFollow;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordDetailPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 车辆邀约记录明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
public interface InviteVehicleRecordDetailMapper extends IMapper<InviteVehicleRecordDetailPO> {

    List<InviteVehicleRecordDetailPO> getInviteVehicleRecordInfo(@Param("id") Long id);

    List<InviteVehicleRecordDetailPO> getInviteVehicleRecordInfoDlr(@Param("id") Long id, @Param("dealerCode") String
            dealerCode);

    List<InviteVehicleRecordDetailPO> getInviteVehicleRecordInfoHistoryDlr(
            Page page, @Param("vin") String vin, @Param("dealerCode") String dealerCode);


    void insertCopyRecordDetail(@Param("newMainId") Long newMainId, @Param("id") Long id);

    int selectNumByCount(@Param("inviteId") Long inviteId);
}
