package com.yonyou.dmscus.customer.dao.faultLight;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightInvitationPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TtFaultLightInvitationMapper extends IMapper<TtFaultLightInvitationPO> {
    //修改
    int updateByClueId(TtFaultLightInvitationPO po);

    Long selectIdByClueId(@Param("clueId")Long clueId);

    int updateNoIntoById(@Param("params") List<TtFaultLightInvitationPO> params);

    TtFaultLightInvitationPO queryFaultLightInvitationByClurId(@Param("clueId") Long id);
}
