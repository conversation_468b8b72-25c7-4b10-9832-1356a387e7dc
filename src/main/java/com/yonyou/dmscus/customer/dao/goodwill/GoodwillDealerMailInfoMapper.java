package com.yonyou.dmscus.customer.dao.goodwill;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillDealerMailInfoPO;

/**
 * <p>
 * 亲善邮箱维护-经销商信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
public interface GoodwillDealerMailInfoMapper extends IMapper<GoodwillDealerMailInfoPO> {
	List<GoodwillDealerMailInfoPO> selectPageBySql(Page page,
			@Param("params") GoodwillDealerMailInfoPO goodwillDealerMailInfoPo);

	int selectPageCountBySql(@Param("params") GoodwillDealerMailInfoPO goodwillDealerMailInfoPo);

	GoodwillDealerMailInfoPO selectByDealerCode(String dealerCode);

	// 根据开票抬头查询发票信息
	int queryDealerIndoByDealerCode(@Param("dealerCode") String dealerCode, @Param("id") Long id);
}
