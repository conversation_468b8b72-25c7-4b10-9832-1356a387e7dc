package com.yonyou.dmscus.customer.dao.invitationCreate;

    
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskPO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 车辆特约店自建邀约任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-29
 */
public interface InviteVehicleDealerTaskMapper extends IMapper<InviteVehicleDealerTaskPO> {


    void insertInvitationDlr(@Param("planNo") String planNo,@Param("userCode") String userCode,@Param("userId") Long userId);


    void insertInvitationVCDC(@Param("planNo") String planNo,@Param("userCode") String userCode,@Param("userId") Long
            userId,@Param("ownerCode")String ownerCode);
}
