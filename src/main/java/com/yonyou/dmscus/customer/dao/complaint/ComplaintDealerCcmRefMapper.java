package com.yonyou.dmscus.customer.dao.complaint;

    
import com.yonyou.dmscus.customer.entity.dto.complaint.CcmImportDto1;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintDealerCcmRefDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.TeComplaintDealerCcmRefImportDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintDealerCcmRefPO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.complaint.TeComplaintDealerCcmRefImportPO;
import feign.Param;

import java.util.List;

/**
 * <p>
 * 客户投诉进销商和CCM复杂人对应关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
public interface ComplaintDealerCcmRefMapper extends IMapper<ComplaintDealerCcmRefPO> {

    /**
     * 新增
     * @param ccmImportDto1
     */
    void importCcm (@org.apache.ibatis.annotations.Param("params") CcmImportDto1 ccmImportDto1);

    /**
     * 更新
     * @param complaintDealerCcmRefDTO
     * @return
     */
    boolean updateCcmAll( @org.apache.ibatis.annotations.Param("params") ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO);

    /**
     * 删除
     * @param dealerCode
     */
    void deleteAll( String dealerCode);
}
