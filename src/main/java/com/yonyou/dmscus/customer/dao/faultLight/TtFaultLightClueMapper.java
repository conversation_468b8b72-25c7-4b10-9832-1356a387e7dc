package com.yonyou.dmscus.customer.dao.faultLight;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.dto.BookingOrderInfoVO;
import com.yonyou.dmscus.customer.dto.faultLight.FaultLightBookingRecordDto;
import com.yonyou.dmscus.customer.entity.dto.faultLight.*;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightCluePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TtFaultLightClueMapper extends IMapper<TtFaultLightCluePO> {

    TtFaultLightCluePO selectCountByIcmId(@Param("icmId") Long icmId);

    List<ClueInfoQueryRespDTO> queryClueInfoList(Page page, @Param("params")ClueInfoQueryRequestDTO params);
    /**更新高亮表示*/
    void updateInviteOvertime(@Param("listItem") List<Long> listItem);

    int updateFaultLightClueById(TtFaultLightCluePO po);

    /**查询大区,小区,城市*/
    List<TtFaultLightCluePO> queryCityDropdownDox();

    /**跟进信息页面查询*/
    FaultLightFollowDTO selectFaultLightFollow(@Param("id") long id);

    void updateClueInfo(@Param("clueState")Integer clueState,@Param("params")TtFaultLightCluePO params);

    List<TtFaultLightCluePO> selectClueInfoList();

    List<TtFaultLightCluePO> selectClueArriveList();

    List<FaultLightClueDTO> selectClueInfoListV4(Integer code);

    /**
     * 查询预约单号不为空的线索
     *
     * @param finishClueStatusList 排除的状态列表
     * @return 预约单号不为空的线索
     */
    List<FaultLightClueDTO> selectBookingClueInfoListWithoutStatus(@Param("finishClueStatusList") List<Integer> finishClueStatusList);

    boolean batchUpdate(@Param("params")List<TtFaultLightCluePO> params);

    boolean batchUpdates(@Param("params")List<TtFaultLightCluePO> params);

    List<TtFaultLightClueDTO> queryDealerList(@Param("params")TtFaultLightClueDTO dto);

    /**查询待验证线索*/
    List<TtFaultLightCluePO> selectPoByClueStatus(@Param("clueStatus") Integer clueStatus);

    List<FaultLightOnlineOfflineDTO> selectPoByClueStatusPage(Page page, @Param("clueStatus") Integer clueStatus, @Param("sourceType") String sourceType);

    int selectPoByClueStatusCount(@Param("clueStatus") Integer clueStatus, @Param("sourceType") String sourceType);

    List<TtFaultLightCluePO> getFaultLightClueChange();

    int selectClueStatus(String dealerCode, String vin);
    /*查询待预约超时数据*/
    List<Long> queryPendingAppointment();

    /**查询二次预约为高亮*/
    List<Long> querySecondAppointment(Page page,@Param("createDate") String createDate,@Param("endDate") String endDate);
    /**AI通话查询线索id*/
    List<Long> queryIdByCallId(@Param("callId") String callId);
    /**取消高亮*/
    void cancelHighlights(@Param("ids") List<Long> ids);
    /**
     * 查询二次预约为高亮得总条数
     */
    Integer queryTotal(@Param("createDate") String createDate,@Param("endDate") String endDate);

    /**
     * 根据线索下发时间更新高亮状态
     */
    void updateHighlightFlagByClueDisTime(@Param("dealerCode") String createDate);

    List<TtFaultLightCluePO> getFaultLightStatusRenovate();

    void batchUpdateRoAmount(@Param("params")List<TtFaultLightCluePO> faultLightClueList);

    /**
     * 通过胡仓线索ID批量更新是否展示DTC诊断信息字段
     * @param faultLightCluePOS
     * @return
     */
    Integer batchUpdateTroubleCodeBySourceClueId(@Param("params") List<TtFaultLightCluePO> faultLightCluePOS);

    List<SourceClueIdRelationDto> queryRecordCountByIds(@Param("params") List<Long> sourceClueIds);

    void updateForecastTime(@Param("params") List<FaultLightBookingRecordDto> faultLightBookingRecordDtos);

    List<BookingOrderInfoVO> getUpdateForecastTime();

    List<BookingOrderInfoVO> selectClueInfoListV5(Integer code);
}
