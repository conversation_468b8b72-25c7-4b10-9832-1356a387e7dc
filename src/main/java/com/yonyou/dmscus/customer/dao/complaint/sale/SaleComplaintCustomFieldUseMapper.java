package com.yonyou.dmscus.customer.dao.complaint.sale;

    
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintCustomFieldUseDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintCustomFieldUsePO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客户投诉自定义字段使用表 每个人不同对应不同 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
public interface SaleComplaintCustomFieldUseMapper extends IMapper<SaleComplaintCustomFieldUsePO> {

    List<SaleComplaintCustomFieldUsePO> querysmallClass2(Long userId);

    List<SaleComplaintCustomFieldUsePO> queryCategory1(Long userId);

    List<SaleComplaintCustomFieldUsePO> queryCategory2(Long userId);

    List<SaleComplaintCustomFieldUsePO> queryCategory3(Long userId);

    void deletesort(Long userId);

    List<SaleComplaintCustomFieldUsePO> querysort(@Param("params")SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDto);

    int resetFied(long userId);
}
