package com.yonyou.dmscus.customer.dao.clueMigrate;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dto.clueMigrate.ClueMigrateTaskQueryDTO;
import com.yonyou.dmscus.customer.entity.po.clueMigrate.TmClueMigrateTask;
import com.yonyou.dmscus.customer.vo.clueMigrate.ClueMigrateTaskVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 线索迁移任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
public interface TmClueMigrateTaskMapper extends BaseMapper<TmClueMigrateTask> {

    List<ClueMigrateTaskVO> pageList(Page<ClueMigrateTaskVO> page, @Param( "clueMigrateTaskQueryDTO" ) ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO);

    List<ClueMigrateTaskVO> getClueMigrateTaskVOList( @Param( "clueMigrateTaskQueryDTO" )   ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO );

    Integer sqlTest();

}
