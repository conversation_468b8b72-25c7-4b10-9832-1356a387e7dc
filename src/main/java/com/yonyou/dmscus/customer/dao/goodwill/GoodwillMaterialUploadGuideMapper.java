package com.yonyou.dmscus.customer.dao.goodwill;

    
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMaterialUploadGuideDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMaterialUploadGuidePO;

/**
 * <p>
 * 亲善材料上传指南 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
public interface GoodwillMaterialUploadGuideMapper extends IMapper<GoodwillMaterialUploadGuidePO> {
	@SuppressWarnings("rawtypes")
	List<GoodwillMaterialUploadGuidePO> selectPageBySql(Page page,@Param("params") GoodwillMaterialUploadGuideDTO goodwillInvoiceTitleInfoDTO);
	GoodwillMaterialUploadGuidePO selecMaterialtInfo(@Param("params") GoodwillMaterialUploadGuideDTO goodwillInvoiceTitleInfoDTO);
}
