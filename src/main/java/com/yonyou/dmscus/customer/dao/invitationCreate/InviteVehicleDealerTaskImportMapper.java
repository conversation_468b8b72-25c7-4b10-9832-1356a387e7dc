package com.yonyou.dmscus.customer.dao.invitationCreate;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskImportPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/3 0003
 */
public interface InviteVehicleDealerTaskImportMapper {

    /**
     * 根据创建人删除相应数据
     * @param createdBy
     */
    void deleteByCreatedBy(@Param("createdBy") String createdBy);

    void bulkInsert(@Param("addList")List<InviteVehicleDealerTaskImportPO> addList,@Param("userId") Long userId);

    List<InviteVehicleDealerTaskImportPO>   queryError(@Param("userId") Long userId);

    List<InviteVehicleDealerTaskImportPO> querySuccess(@Param("userId") Long userId);

    Integer querySucessCount(@Param("userId") Long userId);
    
    List<InviteVehicleDealerTaskImportPO> selectErrorPage(Page page,@Param("userId") Long userId);
    
    List<InviteVehicleDealerTaskImportPO> selectSuccessPage(Page page,@Param("userId") Long userId);
    
    void updateErrorById(@Param("userId")Long userId,@Param("list")List<String> list);

}
