package com.yonyou.dmscus.customer.dao.accidentClues;

import java.util.List;

import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesFollowDTO;
import org.apache.ibatis.annotations.Param;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesFollowPO;

/**
 * <AUTHOR>
 * @date 2020/11/16 0016
 */
public interface AccidentCluesFollowMapper extends IMapper<AccidentCluesFollowPO> {


	List<AccidentCluesFollowPO> selectMaxFollwByAcId(@Param("acId") Integer acId);

	List<AccidentCluesFollowDTO> getFollowList(@Param("acId") Integer acId);
}
