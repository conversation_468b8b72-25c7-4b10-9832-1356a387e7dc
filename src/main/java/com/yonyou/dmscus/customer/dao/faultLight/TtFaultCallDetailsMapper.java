package com.yonyou.dmscus.customer.dao.faultLight;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.faultLight.TtFaultCallDetailsDTO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultCallDetailsPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TtFaultCallDetailsMapper extends IMapper<TtFaultCallDetailsPO> {

    List<TtFaultCallDetailsDTO> queryCallDetails(@Param("inviteId") String inviteId);

    /**
     * 更新故障灯通话记录
     *
     * @param inviteId 线索id
     * @param detailId 跟进记录id
     */
    void updateFaultLightDetailId(@Param("inviteId") Long inviteId, @Param("detailId") Long detailId);
}
