package com.yonyou.dmscus.customer.dao.inviteInsurance;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.dto.DealerTransformationRecordPO;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <AUTHOR>
 * @title: DealerTransformationRecordMapper
 * @projectName server2
 * @description: TODO
 * @date 2022/4/1516:48
 */
public interface DealerTransformationRecordMapper extends IMapper<DealerTransformationRecordPO> {
    DealerTransformationRecordPO selectOneBydealerCode(@Param("dealerCode") String dealerCode);
}
