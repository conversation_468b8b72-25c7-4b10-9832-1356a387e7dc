package com.yonyou.dmscus.customer.dao.voc;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataLogPo;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataRecordPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/16 0016
 */
public interface VocWarningDataRecordMapper extends IMapper<VocWarningDataRecordPo> {
    List<VocWarningDataRecordPo> selectListWarningDataRecord(@Param("selectList") List<VocWarningDataLogPo> x);

    int insertList(@Param("list") List<VocWarningDataRecordPo> insertList);

    int updateList(@Param("updateList")List<VocWarningDataRecordPo> updateList);

    int updateWarningIsExecute(@Param("updateList")List<VocWarningDataRecordPo> list);

    String selectOneByVinAndDate(@Param("vin") String vin, @Param("datetime") String datetime, @Param("endtime") String endtime);
}
