package com.yonyou.dmscus.customer.dao.complaint;

    
import com.yonyou.dmscus.customer.entity.dto.complaint.TeComplaintDealerCcmRefImportDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.TeComplaintDealerCcmRefImportPO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;

import java.util.List;

/**
 * <p>
 * 客户投诉进销商和CCM复杂人对应关系导入表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public interface TeComplaintDealerCcmRefImportMapper extends IMapper<TeComplaintDealerCcmRefImportPO> {
    /**
     * 集合查询
     * @param userId
     * @return
     */
    List<TeComplaintDealerCcmRefImportPO> querySuccess(Long userId);

    /**
     * 查询数量
     * @param userId
     * @return
     */
    int querySucessCount(Long userId);
    /**
     * 查询
     * @param userId
     * @return
     */
    List<TeComplaintDealerCcmRefImportPO> selectAllCodes(Long userId);

    /**
     * 更新
     * @param userId
     * @param errorList
     */
    void updateCheckDealerCodeExist(@org.apache.ibatis.annotations.Param("userId")Long userId, @org.apache.ibatis.annotations.Param("errorList")List<String> errorList);

    /**
     * 查询
     * @param userId
     * @return
     */
    List<TeComplaintDealerCcmRefImportPO> queryError(Long userId);


    void deleteAll(Long userId);
}
