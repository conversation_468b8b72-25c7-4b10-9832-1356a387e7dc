package com.yonyou.dmscus.customer.dao.inviteVehicleDealerAllocate;

    
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.TmVehicleDTO;
import com.yonyou.dmscus.customer.entity.po.inviteVehicleDealerAllocate.InviteVehicleDealerAllocatePO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 车店分配表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface InviteVehicleDealerAllocateMapper extends IMapper<InviteVehicleDealerAllocatePO> {

    List<TmVehicleDTO> getVehicleBySql(@Param("strings") Set<String> strings);

    void insertAllocate(@Param("params")InviteVehicleDealerAllocatePO po);

    void updateAllocateById(@Param("params")InviteVehicleDealerAllocatePO po);
}
