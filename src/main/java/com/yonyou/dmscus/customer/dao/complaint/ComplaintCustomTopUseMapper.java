package com.yonyou.dmscus.customer.dao.complaint;

    
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomTopUseDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomTopUsePO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客户投诉自定义置顶使用表每个人不同对应不同 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
public interface ComplaintCustomTopUseMapper extends IMapper<ComplaintCustomTopUsePO> {
    /**
     *查询置顶
     * @param complaintCustomTopUseDTO
     * @return list
     */
    List queryTop(@Param("params") ComplaintCustomTopUseDTO complaintCustomTopUseDTO);
}
