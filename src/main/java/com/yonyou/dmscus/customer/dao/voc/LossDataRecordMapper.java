package com.yonyou.dmscus.customer.dao.voc;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.dto.LossDataRecordVo;
import com.yonyou.dmscus.customer.entity.dto.loss.LossDataRecordDto;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.LossDataRecordPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 */
public interface LossDataRecordMapper extends IMapper<LossDataRecordPo> {
    int insertList(@Param("list") List<LossDataRecordPo> list);
    IPage<LossDataRecordDto> selectPageList(Page page, @Param("params") LossDataRecordVo lossDataRecordDto);

    List<Map> exportExcel(Page page, @Param("params")LossDataRecordVo dto);
}
