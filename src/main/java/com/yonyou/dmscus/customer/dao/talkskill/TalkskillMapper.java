package com.yonyou.dmscus.customer.dao.talkskill;

    
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillPO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 话术 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-19
 */
public interface TalkskillMapper extends IMapper<TalkskillPO> {

      int updataByIdEnable(TalkskillDTO talkskillDTO);

    List<TalkskillPO> queryTalkskill(@Param("dealerCode")String dealerCode,@Param("type") String type,@Param("name")  String name);

    /**
     * 查询满足关键字匹配的话术
     * @param dealerCode
     * @param keyword
     * @return
     */
    List<TalkskillPO> queryTalkskill1(@Param("dealerCode") String dealerCode,@Param("keyword") String keyword);
}
