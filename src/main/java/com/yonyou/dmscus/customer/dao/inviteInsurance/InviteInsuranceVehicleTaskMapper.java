package com.yonyou.dmscus.customer.dao.inviteInsurance;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceRulePO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleTaskPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 车辆邀约续保任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
public interface InviteInsuranceVehicleTaskMapper extends IMapper<InviteInsuranceVehicleTaskPO> {

    List<InviteInsuranceVehicleTaskPO> queryInsuranceTask(@Param("ownerCode")String ownerCode,
                                                          @Param("vin")String vin,
                                                          @Param("createDate")String createDate);

    InviteInsuranceVehicleTaskPO queryTaskByInviteId(@Param("inviteId")Long inviteId);

    int updateInsuranceVehicleTask(@Param("userId")Long userId,@Param("params") InviteInsuranceRulePO rulePO);

    int updateInsuranceVehicleRecord(@Param("userId")Long userId,@Param("params") InviteInsuranceRulePO rulePO);

    int updateInsuranceVehicleTaskDlr(@Param("userId")Long userId,@Param("params") InviteInsuranceRulePO rulePO);

    int updateInsuranceVehicleRecordDlr(@Param("userId")Long userId,@Param("params") InviteInsuranceRulePO rulePO);

    InviteInsuranceRulePO getInvitationDlrRule(@Param("dealerCode") String dealerCode, @Param("inviteType") Integer
            inviteType, @Param("inviteRule") Integer inviteRule);

    List<InviteInsuranceVehicleTaskPO> queryInsuranceTaskRecordDesc(@Param("vin")String vin, @Param("viClivta")Integer viClivta);

}
