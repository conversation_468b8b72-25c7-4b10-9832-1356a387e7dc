package com.yonyou.dmscus.customer.dao.voc;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusLogPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusRecordPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataRecordPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/16 0016
 */
public interface VocFunctionalStatusRecordMapper extends IMapper<VocFunctionalStatusRecordPO> {
    int insertList(@Param("list")List<VocFunctionalStatusRecordPO> list);
    int updateList(@Param("updateList")List<VocFunctionalStatusRecordPO> list);

    int updateFunctionalIsExecute(@Param("updateList")List<VocFunctionalStatusRecordPO> list);

    List<VocFunctionalStatusRecordPO> selectListStatusRecord(@Param("selectList") List<VocFunctionalStatusLogPO>  x);

    List<VocFunctionalStatusRecordPO> selectListByVins(@Param("selectList")List<String> xx);
}
