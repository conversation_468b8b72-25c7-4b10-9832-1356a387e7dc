package com.yonyou.dmscus.customer.dao.inviteInsurance;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InsuranceBillDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 车辆邀约续保记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
public interface InviteInsuranceVehicleRecordMapper extends IMapper<InviteInsuranceVehicleRecordPO> {

    //保险跟进查询邀约记录----chensh
    List<InviteInsuranceVehicleRecordPO> selectFollowInsureRecord(Page page, @Param("params") InviteInsuranceVehicleRecordPO po);
    //导出查询--保险跟进--chensh
    List<Map> exportExcelFollowInsure(@Param("params") InviteInsuranceVehicleRecordPO inviteInsuranceVehicleRecordPO);
    //
    List<InviteInsuranceVehicleRecordPO> selectInsuranceInvitePlan();
    //保险跟进失败 关闭线索 -- 计划任务
    int updateInsureFollowStatus(@Param("idList")List<Long> idList,@Param("userId")String userId);
    int updateInsureFollowTaskStatus(@Param("idList")List<Long> idList,@Param("userId")String userId);
    //保险跟进失败  车辆24个月内有过进厂  生成新的任务 -- 计划任务
    int insertInsureInviteTask(@Param("idList")List<Long> idList,@Param("userId")String userId);
    //根据VIN判断客户是否投保成功
    List<InsuranceBillDTO> selectInsuranceBill(@Param("vin") String vin, @Param("dealerCode") String dealerCode);

    List<InviteInsuranceVehicleRecordPO> selectInviteInsuranceVehicleRecord(Page page, @Param("params") InviteInsuranceVehicleRecordPO po);

    Integer getNeedDistribute(@Param("dealerCode")String dealerCode,@Param("leaveIds")List<Integer> leaveIds);

    /**
     * 查询自动任务创建的未分配线索 续保 类型
     * @param createDate
     * @return
     */
    List<InviteInsuranceVehicleRecordPO> queryWaitAllocationRecodeForInsurance(@Param("createDate")String createDate);

    List<InviteInsuranceVehicleRecordPO> selectInsuranceVehicleRecordAndSubcues(@Param("id")Long id);

    List<InviteInsuranceVehicleRecordPO> selectFollowInsureRecordVCDC(Page page, @Param("params") InviteInsuranceVehicleRecordPO po);

    List<Map> exportExcel(Page page,@Param("params") InviteInsuranceVehicleRecordPO po);

    List<InviteInsuranceVehicleRecordPO> selectLatelyCall(@Param("insuranceId") Long id);

    List<InviteInsuranceVehicleRecordPO> selectLatelyCalls(@Param("insuranceIds") List<Long> ids);

    List<InviteInsuranceVehicleRecordPO> selectLossOfRecord();

    void updateRecordOrderStatus();

    void updateRecordStatusClose(@Param("vin") String vin,@Param("dealerCode") String dealerCode);

    List<InviteInsuranceVehicleRecordPO> selectInviteInsuranceVehicleRecordCPort(Page page, @Param("params") InviteInsuranceVehicleRecordPO po);


    int updateList(@Param("updateList")List<Long> updateList);

    void updates(@Param("pos") List<InviteInsuranceVehicleRecordPO> pos);
}
