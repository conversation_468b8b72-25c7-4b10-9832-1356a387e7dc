package com.yonyou.dmscus.customer.dao.inviteRule;

    
import com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRulePO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;

/**
 * <p>
 * 邀约规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-17
 */
public interface InviteRuleMapper extends IMapper<InviteRulePO> {

    Integer checkAdd(@Param("inviteType") Integer inviteType,@Param("inviteRule") Integer inviteRule);

    List<InviteRulePO> getInvitationRuleDlr(@Param("params") InviteRulePO po);

    @Cacheable( value = "getInvitationDlrRule", key = "#dealerCode+'-'+#inviteType+'-'+#inviteRule" )
    InviteRulePO getInvitationDlrRule(@Param("dealerCode") String dealerCode,@Param("inviteType") Integer
            inviteType,@Param("inviteRule") Integer inviteRule);

    List<InviteRulePO> getInvitationRuleVcdc(@Param("dealerCode")String dealerCode);

    List<InviteRulePO> getRegularMaintainRule(@Param("dealerCode")String dealerCode);
}
