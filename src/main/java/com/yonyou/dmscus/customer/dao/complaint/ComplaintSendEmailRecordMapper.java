package com.yonyou.dmscus.customer.dao.complaint;

    
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailRecordDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintSendEmailRecordPO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;

import java.util.List;

/**
 * <p>
 * 客户投诉发送邮件记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
public interface ComplaintSendEmailRecordMapper extends IMapper<ComplaintSendEmailRecordPO> {
    /**
     * 查询发件人最近使用过得邮箱
     * @param userId
     * @return
     */
    ComplaintSendEmailRecordDTO selectLastEmail(long userId);
    /**
     * 查询发件人使用过得邮箱
     * @param userId
     * @return
     */
    List<ComplaintSendEmailRecordDTO> selectEmaillist(long userId);
}
