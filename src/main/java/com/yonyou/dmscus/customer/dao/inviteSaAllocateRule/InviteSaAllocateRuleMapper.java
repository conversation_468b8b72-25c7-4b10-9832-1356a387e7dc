package com.yonyou.dmscus.customer.dao.inviteSaAllocateRule;

    
import com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule.InviteSaAllocateRulePO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * SA分配规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
public interface InviteSaAllocateRuleMapper extends IMapper<InviteSaAllocateRulePO> {

    List<InviteSaAllocateRulePO> getInviteSaAllocateRule(@Param("dealerCodes")List<String> dealerCodes);
}
