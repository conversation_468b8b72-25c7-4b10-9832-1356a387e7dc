package com.yonyou.dmscus.customer.dao.faultLight;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.faultLight.CluesDiagnosticInfoRelationPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CluesDiagnosticInfoRelationMapper extends IMapper<CluesDiagnosticInfoRelationPO> {
    List<CluesDiagnosticInfoRelationPO> queryDiagnosticInfoRelationList(@Param("offset") int offset, @Param("limit") int limit, @Param("processingStatus") Integer processingStatus, @Param("createdAt") String createdAt);
    Integer batchUpdateProcessingStatusByIds(@Param("params") List<CluesDiagnosticInfoRelationPO> relationPOList);
    Integer batchUpdateNumberOfExecutionsByIds(@Param("params") List<CluesDiagnosticInfoRelationPO> relationPOList);
    Integer queryDiagnosticInfoRelationCount(String createdAt, Integer processingStatus);
}