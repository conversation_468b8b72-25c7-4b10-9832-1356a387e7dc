package com.yonyou.dmscus.customer.dao.inviteRule;


import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InvitePartItemRuleChangedRecordPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 邀约易损件和项目规则变更记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-18
 */
public interface InvitePartItemRuleChangedRecordMapper extends IMapper<InvitePartItemRuleChangedRecordPO> {

    List<InvitePartItemRuleChangedRecordPO> getUpdateNoExecute();

    void updateIsExecute(@Param("type")Integer type,@Param("code") String code);
}
