package com.yonyou.dmscus.customer.dao.complaint.sale;

    
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRuleUseTestPO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintKpiBaseRuleUsePO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 销售客户投诉KP基础规则使用表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-21
 */
public interface SaleComplaintKpiBaseRuleUseMapper extends IMapper<SaleComplaintKpiBaseRuleUsePO> {

    List<ComplaintKpiBaseRuleUseTestPO> selectListBySql1(@Param("params")ComplaintKpiBaseRuleUseTestPO complaintKpiBaseRuleUseTestPo);
}
