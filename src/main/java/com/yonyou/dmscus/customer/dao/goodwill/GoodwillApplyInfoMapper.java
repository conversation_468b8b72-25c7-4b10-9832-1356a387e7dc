package com.yonyou.dmscus.customer.dao.goodwill;

import java.util.List;
import java.util.Map;

import com.yonyou.dmscus.customer.dto.CompanyDetailInfoDTO;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillFirstPageDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillStampsDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillStampsPO;

/**
 * <p>
 * 亲善预申请单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
public interface GoodwillApplyInfoMapper extends IMapper<GoodwillApplyInfoPO> {
	public List<Map> queryPartDetailInfo(Page page, @Param("params") Map map);

	public Integer queryPartDetailCountInfo(@Param("params") Map map);

	public GoodwillApplyInfoDTO getByApplyId(Long id);

	public List<Map> queryComplaintInfo(Page page, @Param("params") Map map);

	public Integer queryComplaintCountInfo(@Param("params") Map map);

	public List<GoodwillApplyInfoPO> selectCheckedGoodWill(Page page,
			@Param("params") GoodwillApplyInfoPO goodwillApplyInfoPo);

	public List<GoodwillApplyInfoPO> queryApplyHistory(@Param("vin") String vin, @Param("applyNo") String applyNo);

	public Map queryApplyByDealerCode(@Param("dealerCode") String dealerCode, @Param("year") Integer year);

	public List<GoodwillApplyInfoPO> selectGoodwillApplyInfo(Page page,
			@Param("params") GoodwillApplyInfoDTO goodwillApplyInfoDto);

	public List<GoodwillApplyInfoPO> selectOemTodoGoodwillApplyInfo(@SuppressWarnings("rawtypes") Page page,
			@Param("params") GoodwillApplyInfoDTO goodwillApplyInfoDto);

	public List<GoodwillApplyInfoPO> selectOemTodoListGoodwillApplyInfo(@SuppressWarnings("rawtypes") Page page,
			@Param("params") GoodwillApplyInfoDTO goodwillApplyInfoDto);

	public List<GoodwillApplyInfoPO> selectDealerTodoGoodwillApplyInfo(Page page,
			@Param("params") GoodwillApplyInfoDTO goodwillApplyInfoDto);

	public List<GoodwillApplyInfoPO> selectOemSearchGoodwillApplyInfo(Page page,
			@Param("params") GoodwillApplyInfoDTO goodwillApplyInfoDto);

	public List<GoodwillApplyInfoPO> querySupportApplyDealerSearchInfo(Page page,
			@Param("params") GoodwillApplyInfoDTO goodwillApplyInfoDto);

	public List<GoodwillApplyInfoPO> querySupportApplyAuditInfo(Page page,
			@Param("params") GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public List<Map> queryNoticeInvoiceInfo(Page page, @Param("goodwillApplyId") Long goodwillApplyId);

	public Integer queryNoticeInvoiceCount(Long GoodwillApplyId);

	public Map queryPrintInfo(@Param("noticeInvoiceId") Long noticeInvoiceId);

	//查询CCMZJ角色
	public Map queryPrintInfoByCcm(@Param("noticeInvoiceId") Long noticeInvoiceId);

	public List<GoodwillStampsPO> queryRechargeInfo(Page page, @Param("params") GoodwillStampsDTO goodwillStampsDTO);

	public List<GoodwillStampsDTO> queryRechargeExport(@Param("params") GoodwillStampsDTO goodwillStampsDTO);

	public Map queryExtendWarrantyByVin(@Param("vin") String vin);

	public List<Map> queryInvoiceHistory(Page page, @Param("goodwillApplyId") Long goodwillApplyId);

	public Integer queryInvoiceHistoryCount(Long GoodwillApplyId);

	public List<GoodwillApplyInfoPO> exportSupportApplyOemSearchInfo(
			@Param("params") GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public List<GoodwillApplyInfoPO> exportSupportApplyAuditListInfo(
			@Param("params") GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public List<GoodwillApplyInfoPO> exportSupportApplyDealerSearchInfo(
			@Param("params") GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public List<GoodwillApplyInfoPO> exportCheckedApplyInfo(@Param("params") GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public List<GoodwillApplyInfoPO> exportSupportApplyAuditInfo(
			@Param("params") GoodwillApplyInfoDTO goodwillApplyInfoDTO);

	public List<GoodwillStampsPO> queryConsumeInfo(@Param("params") GoodwillStampsDTO goodwillStampsDTO);

	public List<GoodwillStampsPO> queryCouponId(@Param("params") GoodwillStampsDTO goodwillStampsDTO);

	public GoodwillApplyInfoDTO findbyCouponId(@Param("couponId") Long couponId,
			@Param("params") GoodwillStampsDTO goodwillStampsDTO);

	public List<String> getConsumeId(@Param("params") GoodwillStampsDTO goodwillStampsDTO);

	public List<GoodwillApplyInfoPO> queryRefuse(@Param("month") Integer month);

	int updateStatusById(@Param("params") GoodwillApplyInfoPO goodwillApplyInfoPO);

	// 查询商务亲善预申请审批超时提醒
	public List<GoodwillApplyInfoPO> queryApplyTimeOut(@Param("days") Integer days);

	// 商务亲善材料审核超时提醒
	public List<GoodwillApplyInfoPO> goodwillMatrialAuditTask(@Param("days") Integer days, @Param("type") Integer type);

	// 商务亲善材料提交超时提醒
	public List<GoodwillApplyInfoPO> goodwillMatrialCommitTask(@Param("days") Integer days,
			@Param("type") Integer type);

	// 商务亲善补充材料提交超时提醒
	public List<GoodwillApplyInfoPO> goodwillSupplyMatrialCommitTask(@Param("days") Integer days,
			@Param("type") Integer type);

	// 商务亲善材料提交过期提醒
	public List<GoodwillApplyInfoPO> goodwillMatrialCommitTimeOutTask(@Param("days") Integer days);

	// 商务亲善开票超时通知
	public List<GoodwillApplyInfoPO> goodwillInvoiceTimeOutTask(@Param("days") Integer days);

	// 根据VIN查询送修人
	public Map queryMobile(@Param("vin") String vin);

	public Map getGoodwillFirstPage(@Param("params") GoodwillFirstPageDTO goodwillFirstPageDTO);


	/**
	 * 通过查询参数查询亲善申请单数据
	 * @param queryParams 查询参数
	 * @return 亲善申请单数据
	 */
	public List<GoodwillApplyInfoPO> getGoodwillApplyInfoDatas(@Param("params") Map<String, Object> queryParams);

	/**
	 * 根据goodwillNoticeInvoiceInfoMapper 表 主键id列表查询数据list
	 * @param ids
	 * @param goodwillStampsDTO 
	 * @return
	 */
	public List<GoodwillStampsPO> queryConsumeInfoByIds(@Param("ids") List<String> ids, @Param("params") GoodwillStampsDTO goodwillStampsDTO);

	/**
	 * 根据CouponIds 批量查询ApplyInfo
	 * @param couponIdList 
	 * @return
	 */
	public List<GoodwillApplyInfoDTO> findApplyInfobyCouponIds(@Param("couponIdList") List<String> couponIdList, @Param("params") GoodwillStampsDTO goodwillStampsDTO);

    List<String> queryDealer();

	void regionInformationRefresh(@Param("params") List<CompanyDetailInfoDTO> companyDetailInfoDTOList);
}
