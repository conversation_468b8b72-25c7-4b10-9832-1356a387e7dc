package com.yonyou.dmscus.customer.dao.inviteRule;

    

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRuleChangedRecordPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 邀约规则变更记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-18
 */
public interface InviteRuleChangedRecordMapper extends IMapper<InviteRuleChangedRecordPO> {

    List<InviteRuleChangedRecordPO> getUpdateNoExecute(@Param("inviteType") Integer inviteType);

    void updateIsExecute(@Param("dealerCode")String dealerCode,@Param("inviteType") Integer inviteType,
                         @Param("inviteRule") Integer inviteRule);
}
