package com.yonyou.dmscus.customer.dao.goodwill;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditProcessDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAuditProcessPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 亲善审批流程表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface GoodwillAuditProcessMapper extends IMapper<GoodwillAuditProcessPO> {
	@SuppressWarnings("rawtypes")
	List<GoodwillAuditProcessPO> selectPageBySql(Page page,
			@Param("params") GoodwillAuditProcessDTO goodwillInvoiceTitleInfoDTO);

	List<Map> selectAuditProcessInfo(@Param("params") GoodwillAuditProcessPO goodwillInvoiceTitleInfoPo);

	// 根据审核对象，审核类型查询审批流程
	public List<GoodwillAuditProcessPO> selectList(@Param("auditObject") Integer auditObject,
			@Param("auditType") Integer auditType);

	// 查询拒单期限
	public Map queryRefuseTime();

}
