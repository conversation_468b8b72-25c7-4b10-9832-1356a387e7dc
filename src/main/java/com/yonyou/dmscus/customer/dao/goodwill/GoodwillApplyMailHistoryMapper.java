package com.yonyou.dmscus.customer.dao.goodwill;

import org.apache.ibatis.annotations.Param;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyMailHistoryDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyMailHistoryPO;

/**
 * <p>
 * 亲善预申请邮件发送记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
public interface GoodwillApplyMailHistoryMapper extends IMapper<GoodwillApplyMailHistoryPO> {
	int insertMailHistory(@Param("params") GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO);

	int queryIsSendEmail(@Param("goodwillApplyId") Long goodwillApplyId, @Param("mailType") Integer mailType);
}
