package com.yonyou.dmscus.customer.dao.complaint.sale;


import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoEtlDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoEtlPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 销售客户投诉信息表-etl Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-22
 */
public interface SaleComplaintInfoEtlMapper extends IMapper<SaleComplaintInfoEtlPO> {

    List<Map> exportSaleComplaintHistory(@Param("params")SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO);
}
