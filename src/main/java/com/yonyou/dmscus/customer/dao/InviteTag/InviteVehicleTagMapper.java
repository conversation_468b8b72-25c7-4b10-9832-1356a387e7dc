package com.yonyou.dmscus.customer.dao.InviteTag;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dto.InviteTagDto;
import com.yonyou.dmscus.customer.dto.InviteVehicleTagDTO;
import com.yonyou.dmscus.customer.dto.InviteVehicleTagParamsDTO;
import com.yonyou.dmscus.customer.entity.po.inviteTag.InviteVehicleTagPO;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface InviteVehicleTagMapper extends BaseMapper<InviteVehicleTagPO> {

 	List<InviteVehicleTagDTO> selectByDtoForPage(Page page, @Param("param") InviteVehicleTagParamsDTO param);

 	List<InviteVehicleTagDTO> selectByDto(@Param("param") InviteVehicleTagParamsDTO param);
 	
 	List<InviteTagDto> selectVinTagByVIN(@Param("vin") String vin);
	
	Integer insertBatch(@Param("tagPos") List<InviteVehicleTagPO> tagPos);

	List<InviteVehicleTagDTO> selectByVins(@Param("vins") List<String> vins);

}
