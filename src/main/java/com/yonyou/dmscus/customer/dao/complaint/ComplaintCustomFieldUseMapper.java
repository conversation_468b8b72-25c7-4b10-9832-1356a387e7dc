package com.yonyou.dmscus.customer.dao.complaint;

    
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldUseDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 客户投诉自定义字段使用表 每个人不同对应不同 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
public interface ComplaintCustomFieldUseMapper extends IMapper<ComplaintCustomFieldUsePO> {
    /**
     * 查询CcmPart
     * @param userId
     * @return
     */
    List<ComplaintCustomFieldUsePO>  queryCcmPart(Long userId);
    /**
     * 查询CcmSubdivisionPart
     * @param userId
     * @return
     */
    List<ComplaintCustomFieldUsePO> queryCcmSubdivisionPart(Long userId);

    /**
     * 查询followStatus
     * @param userId
     * @return
     */
    List<ComplaintCustomFieldUsePO> queryfollowStatus(Long userId);
    /**
     * 查询CcMainReason
     * @param userId
     * @return
     */
    List<ComplaintCustomFieldUsePO> queryCcMainReason(Long userId);
    /**
     * 查询ccResult
     * @param userId
     * @return
     */
    List<ComplaintCustomFieldUsePO> queryccResult(Long userId);
    /**
     * 查询Category1
     * @param userId
     * @return
     */
    List<ComplaintCustomFieldUsePO> queryCategory1(Long userId);
    /**
     * 查询Category2
     * @param userId
     * @return
     */
    List<ComplaintCustomFieldUsePO> queryCategory2(Long userId);
    /**
     * 查询Category3
     * @param userId
     * @return
     */
    List<ComplaintCustomFieldUsePO> queryCategory3(Long userId);

    /**
     * Classification1
     * @param userId
     * @return
     */
    List<ComplaintCustomFieldUsePO> queryClassification1(Long userId);

    /**
     * 查询Classification2
     * @param userId
     * @return
     */
    List<ComplaintCustomFieldUsePO> queryClassification2(Long userId);

    /**
     * 查询sort
     * @param complaintCustomFieldUseDTO
     * @return
     */
    List<ComplaintCustomFieldUsePO> querysort(@Param("params") ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO);

    /**
     * 删除sort
     * @param userId
     */
    void deletesort(Long userId);

    /**
     * 查询smallClass2
     * @param userId
     * @return
     */
    List<ComplaintCustomFieldUsePO> querysmallClass2(Long userId);

    /**
     * 重置
     * @param userId
     * @return
     */
    int resetFied(long userId);

    /**
     * 查询Classification3
     * @param userId
     * @return
     */
    List<ComplaintCustomFieldUsePO> queryClassification3(Long userId);

    /**
     * 更新Query
     * @param complaintCustomFieldUseDTO
     */
    void updateQuery( @Param("params") ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO);

    List<ComplaintCustomFieldUsePO> queryisValid(Long userId);
}
