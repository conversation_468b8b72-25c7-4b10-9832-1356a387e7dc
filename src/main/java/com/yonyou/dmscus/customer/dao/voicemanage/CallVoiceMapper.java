package com.yonyou.dmscus.customer.dao.voicemanage;


import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallVoicePO;
import com.yonyou.dmscus.customer.utils.ai.CallVoiceChild;

/**
 * <p>
 * 通话录音 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
public interface CallVoiceMapper extends IMapper<CallVoicePO> {
	
	void insertCallVoice(@Param("allDetail") List<CallVoiceChild> allDetail);
	
	
	List<Map<String,Object>> selectSendCallVoice(@Param("isSend") Integer isSend);

}
