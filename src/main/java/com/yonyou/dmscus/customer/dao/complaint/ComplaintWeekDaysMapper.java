package com.yonyou.dmscus.customer.dao.complaint;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.complaint.HolidayPO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * <p>
 * 节假日查询接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-30
 */
public interface ComplaintWeekDaysMapper extends IMapper<HolidayPO> {

    List<HolidayPO> selectWeekDays(@Param("nowtime") Date nowtime, @Param("calltime")Date calltime);

    HolidayPO selectOneDays(@Param("time") Date time);
}
