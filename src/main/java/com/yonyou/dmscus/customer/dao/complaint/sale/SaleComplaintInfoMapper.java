package com.yonyou.dmscus.customer.dao.complaint.sale;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dto.ComplaintInfMoreVo;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.v51dk.V51dkComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfMorePO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoPO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.vo.V51dkComplaintInfMoreVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 销售客户投诉信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-10
 */
public interface SaleComplaintInfoMapper extends IMapper<SaleComplaintInfoPO> {

    List<SaleComplaintInfoPO> queryid(@Param("params")SaleComplaintInfoDTO saleComplaintInfoDTO);

    IPage<ComplaintInfMoreDTO> selectCusByDeal(Page page, @Param("params") ComplaintInfMoreDTO complaintInfMoreDTO);

    List<SaleComplaintInfoPO> queryDealerFirstReplyTime(long complaintInfoId);

    void restart(long id);

    List<ComplaintInfMorePO> selectCusByDealAll(@Param("params")ComplaintInfMorePO complaintInfMorePo);

    List<SaleComplaintInfoPO> queryComplaintData(@Param("params") SaleComplaintInfoDTO saleComplaintInfoDTO);

    ComplaintInfMoreVo selectSaleCusDetailById(@Param("params")ComplaintInfMoreDTO complaintInfMoreDTO);

    Page<V51dkComplaintInfMoreVO> select51dkCusByDeal(@Param("page") Page page, @Param("params") V51dkComplaintInfMoreDTO dto);
}
