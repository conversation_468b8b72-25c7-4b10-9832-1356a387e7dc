package com.yonyou.dmscus.customer.dao.goodwill;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillInvoiceRecordPO;

/**
 * <p>
 * 亲善管理录入发票信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-19
 */
public interface GoodwillInvoiceRecordMapper extends IMapper<GoodwillInvoiceRecordPO> {
	public List<Map> queryInvoiceInfo(@Param("applyId") Long applyId);

	public List<Map> queryOemInvoiceInfo(@Param("applyId") Long applyId);
}
