package com.yonyou.dmscus.customer.dao.vocAccidentInvitation;

    

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.vocAccidentInvitation.TempInviteVehicleVocTaskPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 车辆特约店VOC事故邀约任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
public interface TempInviteVehicleVocTaskMapper extends IMapper<TempInviteVehicleVocTaskPO> {

    /**
     * 查询经销商code
     * @param userId
     * @return
     */
    List<Map> getDealerCodes(@Param("userId") Long userId);

    /**
     * 查询vin
     * @param userId
     * @return
     */
    List<Map> getVins(@Param("userId") Long userId);

    /**
     * 更新错误经销商code
     * @param userId
     * @param errorList
     */
    void updateErrorDealerCode(@Param("userId") Long userId,@Param("errorList") List<String> errorList);



    /**
     * 更新错误经销商code
     * @param userId
     * @param errorList
     */
    void updateErrorVin(@Param("userId") Long userId,@Param("errorList") List<String> errorList);

    /**
     * 更新经销商null数据
     * @param userId
     */
    void updateEmptyDealerCode(@Param("userId")Long userId);


    /**
     * 更新vin null数据
     * @param userId
     */
    void updateEmptyVin(@Param("userId")Long userId);

    /**
     * 校验是否vin错误
     * @param userId
     */
    void updateCheckVinError(@Param("userId")Long userId);

    /**
     * 校验是否存在相同vin
     * @param userId
     */
    void updateCheckVinRepeat(@Param("userId") Long userId);

    /**
     * 校验车牌号错误
     * @param userId
     */
    void updateCheckLicense(@Param("userId") Long userId);



    /**
     * 校验客户姓名
     * @param userId
     */
    void updateCheckName(@Param("userId")Long userId);

    /**
     * 校验电话格式
     * @param userId
     */
    void updateCheckTel(@Param("userId")Long userId);

    /**
     * 校验客户通话情况
     * @param userId
     */
    void updateCheckContactSituation(@Param("userId")Long userId);

    /**
     * 校验voc事故号
     * @param userId
     */
    void updateCheckAccidentNo(@Param("userId")Long userId);

    /**
     * 校验voc事故说明
     * @param userId
     */
    void updateCheckAccidentDetail(@Param("userId")Long userId);

    /**
     * 校验voc备注
     * @param userId
     */
    void updateCheckRemark(@Param("userId")Long userId);


    /**
     * /查询错误项
     * @param userId
     * @return
     */
    List<TempInviteVehicleVocTaskPO> queryError(@Param("userId")Long userId);

    /**
     * 查询正确数据数
     * @param userId
     * @return
     */
    int querySucessCount(@Param("userId")Long userId);

    void deleteAll(@Param("userId")Long userId);

    List<TempInviteVehicleVocTaskPO> querywaitImport(@Param("userId")Long userId);
}


