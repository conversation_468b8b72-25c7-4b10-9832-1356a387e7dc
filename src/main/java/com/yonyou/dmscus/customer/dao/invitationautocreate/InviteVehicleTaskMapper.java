package com.yonyou.dmscus.customer.dao.invitationautocreate;


import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.InvVehLossCleanTaskPO;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocInviteVehicleTaskRecordPo;
import io.swagger.models.auth.In;
import com.yonyou.dmscus.customer.service.invitationautocreate.po.InviteVehiclePO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 车辆邀约任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-07
 */
public interface InviteVehicleTaskMapper extends IMapper<InviteVehicleTaskPO> {

    List<InviteVehicleTaskPO> queryQbTask(@Param("createDate") String createDate);

    List<InviteVehicleTaskPO> queryMaintainTask(@Param("createDate") String createDate);

    List<InviteVehicleTaskPO> queryVulnerableTask(@Param("createDate") String createDate);

    List<InviteVehicleTaskPO> queryRegularMaintainTask(@Param("vin") String vin);

    List<InviteVehicleTaskPO> queryCustomerLossTask(@Param("vin") String vin, @Param("dealerCode") String dealerCode);

    List<InviteVehicleTaskPO> queryInsuranceTask(@Param("createDate") String createDate);

    List<InviteVehicleTaskPO> queryGuaranteeTask(@Param("createDate") String createDate);

    List<InviteVehicleTaskPO> queryCustomerLoss(@Param("createDate") String createDate);

    InviteVehicleTaskPO queryFirstMaintainTask(@Param("vin") String vin);

    List<InviteVehicleTaskPO> getWaitCloseRecord(@Param("createDate") String createDate);

    void updateInviteTaskByDealerCode(@Param("vin") String vin, @Param("dealerCode") String dealerCode, @Param
            ("lastDealerCode") String lastDealerCode);

    void updateTaskCloseById(@Param("dealerCode") String dealerCode, @Param("inviteType") Integer inviteType, @Param
            ("vin")
            String vin);
    void newUpdateTaskCloseById(@Param("dealerCode") String dealerCode, @Param("inviteType") Integer inviteType, @Param
            ("vin")
            String vin);


    void updateTaskPartCloseById(@Param("vin") String vin, @Param("itemType") Integer itemType, @Param("itemCode")
            String itemCode);
    //获取首保
    List<InviteVehicleTaskPO> getFirstMaintainTask(@Param("nextMonthDay") String nextMonthDay, @Param("vin") String vin);
    List<InviteVehicleTaskPO> getFirstMaintainTasks(@Param("nextMonthDay") String nextMonthDay, @Param("list") List<String> list);
    List<InviteVehicleTaskPO> getMaintainTask(@Param("nextMonthDay") String nextMonthDay, @Param("vin") String vin);
    List<InviteVehicleTaskPO> getMaintainTasks(@Param("nextMonthDay") String nextMonthDay, @Param("list") List<String> list);
    List<InviteVehicleTaskPO> getMaintainTaskAll(@Param("nextMonthDay") String nextMonthDay, @Param("list") List<String> list);

    List<InviteVehicleTaskPO> getMaintainTaskWithDateRange(
            @Param( "startDateTime" ) LocalDateTime startDateTime,
            @Param( "endDateTime" ) LocalDateTime endDateTime,
            @Param("vin") String vin);

    List<InviteVehicleTaskPO> getVulnerableTask(@Param("nextMonthDay") String nextMonthDay, @Param("vin") String vin);

    List<InviteVehicleTaskPO> queryByFuseRule();


    void updateFordailyAverageMileageById(@Param("params") InviteVehicleTaskPO po);
    void updateFordailyAverageMileageByIds(@Param("params") List<InviteVehicleTaskPO> params);

    List<InviteVehicleTaskPO> getVulnerableTaskForRuleChanged(@Param("nextMonthDay") String nextMonthDay, @Param
            ("type") Integer type, @Param("code") String code,@Param("changedId") Long changedId);

    List<InviteVehicleTaskPO> getFirstMaintainTaskForRuleChanged(@Param("nextMonthDay") String nextMonthDay, @Param
            ("dealerCode") String dealerCode, @Param("changedId") Long changedId);


    void updateCloseInterval(@Param("ruleType") Integer ruleType, @Param("inviteType") Integer inviteType,
                             @Param("dealerCode") String dealerCode, @Param("closeInterval") Integer closeInterval,
                             @Param("remindInterval") Integer remindInterval);

    void updateAdviseInMileage(@Param("ruleType") Integer ruleType,@Param("inviteRule")Integer inviteRule,
                               @Param("inviteType")  Integer  inviteType, @Param("dealerCode") String dealerCode,
                               @Param("ruleValue") Integer  ruleValue,@Param("changedId")Long changedId);


    void updateAdviseInMileageHasDown(@Param("ruleType") Integer ruleType,@Param("inviteRule")Integer inviteRule,
                                      @Param("inviteType")  Integer  inviteType, @Param("dealerCode") String dealerCode,
                                      @Param("ruleValue") Integer  ruleValue,@Param("changedId")Long changedId);

    /**重复数据清洗-关闭线索*/
    void doProDataCloseTask(@Param("params")List<InviteVehiclePO> params);

    /**重复数据清洗-修改线索*/
    void doProDataUpdateTask(@Param("params")List<InviteVehiclePO> params);

    List<InviteVehicleTaskPO> getCustomerLossTaskForRuleChanged(@Param("nextMonthDay") String nextMonthDay, @Param
            ("dealerCode") String dealerCode, @Param("changedId") Long changedId);


    List<InviteVehicleTaskPO> getGuaranteeTaskForRuleChanged(@Param("nextMonthDay") String nextMonthDay, @Param
            ("dealerCode") String dealerCode, @Param("changedId") Long changedId);


    List<InviteVehicleTaskPO> getMaintainTaskForRuleChanged(@Param("nextMonthDay") String nextMonthDay, @Param
            ("dealerCode") String dealerCode, @Param("changedId") Long changedId);

    void updateCloseIntervalHasDown(@Param("ruleType") Integer ruleType, @Param("inviteType") Integer inviteType,
                                    @Param("dealerCode") String dealerCode, @Param("closeInterval") Integer
                                            closeInterval,
                                    @Param("remindInterval") Integer remindInterval);

    Integer countWaitUpdateNoExecute(@Param("nextMonthDay") String nextMonthDay, @Param
            ("dealerCode") String dealerCode, @Param("changedId") Long changedId, @Param("inviteType") Integer
            inviteType);

    Integer countVulnerableTaskForRuleChanged(@Param("nextMonthDay") String nextMonthDay, @Param
            ("type") Integer type, @Param("code") String code,@Param("changedId") Long changedId);


    List<InviteVehicleTaskPO> selectListByVinAndInviteTypeAndDealerCodeOrderById(@Param("vin")String vin, @Param("inviteType")Integer inviteType,@Param("dealerCode") String dealerCode);

    List<InviteVehicleTaskPO> selectAllByVinAndInviteTypeAndDealerCode(@Param("dealerCode") String dealerCode,@Param("vin") String vin, @Param("inviteType")Integer inviteType);

    List<InviteVehicleTaskPO> selectAllByVinAndDealerCode(@Param("dealerCode")String dealerCode, @Param("vin")String vin);

    List<InviteVehicleTaskPO> getTaskByAdviseInDate(@Param("startDate") String startDate,@Param("endDate")  String endDate);

    List<InviteVehicleTaskPO> selectByInviteId(@Param("inviteId") Long inviteId);

    int updateList( @Param("updateList")List<Long> list);

    int insertList(@Param("list")List<InviteVehicleTaskPO> list);

    void updateRecordIdListById(@Param("updateList")List<VocInviteVehicleTaskRecordPo> list);

    List<InviteVehicleTaskPO> selectListTaskVocByTime(@Param("dateTime")String dateTime);

    int updateLossVocByVin(@Param("vin")String vin);

    /**分批查询未下发的定保任务*/
    List<InvVehLossCleanTaskPO> selectFixedInsuranceTask(@Param("startNum")int startNum, @Param("endNum")int endNum);

    /**查询没有生成扩扩展的流失任务*/
    List<InvVehLossCleanTaskPO> selectDeletionLossTask(@Param("startDate")String startDate, @Param("endDate")String endDate);

    /**查询下个月未下发的流失任务*/
    List<InvVehLossCleanTaskPO> selectLossTask(@Param("listVin")List<String> listVin);

    /**查询最新的定保是未完成或者逾期的线索对应的任务*/
    List<InvVehLossCleanTaskPO> selectIncompleteTask(@Param("dealerCode")String dealerCode, Integer orderStatus);

    /**批量新增*/
    int insertLossAddList(@Param("list")List<InviteVehicleTaskPO> list);

    /**批量修改*/
    int updateLossUpList( @Param("list")List<InviteVehicleTaskPO> list);

    /**批量系统删除*/
    int updateLossDelList( @Param("list")List<Long> list);

    /**通过vin关闭待下发线索任务*/
    int updateIsCreateInviteByVin(@Param("vin")String vin);
}
