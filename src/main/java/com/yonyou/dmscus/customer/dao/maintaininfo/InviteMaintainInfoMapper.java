package com.yonyou.dmscus.customer.dao.maintaininfo;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.maintaininfo.InviteMaintainInfoPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * description: add a description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/12/07 17:21:55
 */


public interface InviteMaintainInfoMapper extends IMapper<InviteMaintainInfoPO> {

    //批量添加数据
    int insertInviteMaintainInfo(@Param("infoPos") List<InviteMaintainInfoPO> infoPos);

    //通过VIN查询最新的保养记录
    InviteMaintainInfoPO selectMainInfoByVin(@Param("vin") String vin);
}
