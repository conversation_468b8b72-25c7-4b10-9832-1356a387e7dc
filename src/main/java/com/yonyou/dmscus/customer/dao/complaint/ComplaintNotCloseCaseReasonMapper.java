package com.yonyou.dmscus.customer.dao.complaint;

    
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintNotCloseCaseReasonPO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;

import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintNotCloseCaseReasonTestPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 5日未结案原因 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface ComplaintNotCloseCaseReasonMapper extends IMapper<ComplaintNotCloseCaseReasonPO> {
    /**
     * 查询followTime
     * @param id
     * @return
     */
    List<ComplaintNotCloseCaseReasonPO> selectfollowTime(Long id);

    /**
     * 分页查询
     * @param page
     * @param complaintNotCloseCaseReasonTestPo
     * @return
     */
    List<ComplaintNotCloseCaseReasonTestPO> selectPageBySql2(Page page, @Param("params") ComplaintNotCloseCaseReasonTestPO complaintNotCloseCaseReasonTestPo);
}
