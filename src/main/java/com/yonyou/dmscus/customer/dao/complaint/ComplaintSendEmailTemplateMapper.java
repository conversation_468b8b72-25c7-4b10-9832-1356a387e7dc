package com.yonyou.dmscus.customer.dao.complaint;

    
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailTemplateDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintSendEmailTemplatePO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;

import java.util.List;

/**
 * <p>
 * 客户投诉发送邮件模板 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
public interface ComplaintSendEmailTemplateMapper extends IMapper<ComplaintSendEmailTemplatePO> {
    /**
     * 查看模板
     * @param userId
     * @return
     */
    List<ComplaintSendEmailTemplateDTO> queryEmailTemplate(long userId);
    /**
     * 查看模板
     * @param userId
     * @return
     */
    ComplaintSendEmailTemplateDTO selectLastTemplate(long userId);
}
