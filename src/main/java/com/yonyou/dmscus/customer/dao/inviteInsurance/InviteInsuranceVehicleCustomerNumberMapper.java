package com.yonyou.dmscus.customer.dao.inviteInsurance;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleCustomerNumberPO;

public interface InviteInsuranceVehicleCustomerNumberMapper extends IMapper<InviteInsuranceVehicleCustomerNumberPO> {
	
	void updateInsuranceDetailId(@Param("insuranceId") Long insuranceId,@Param("insuranceDetailId") Long insuranceDetailId);

	List<InviteInsuranceVehicleCustomerNumberPO> selectCusList(@Param("id") Long id);

	InviteInsuranceVehicleCustomerNumberPO selectInsuranceUserByVin(@Param("vin") String vin,@Param("dealerCode") String dealerCode);
}
