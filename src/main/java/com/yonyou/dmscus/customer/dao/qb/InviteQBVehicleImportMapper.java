package com.yonyou.dmscus.customer.dao.qb;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.po.qb.InviteQBVehicleImportPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/3 0003
 */
public interface InviteQBVehicleImportMapper {

    List<Long> selectMatchID(@Param("userId") Long userId);

    void deleteByCreatedBy(@Param("createdBy") String createdBy);

    void bulkInsert(@Param("addList") List<InviteQBVehicleImportPO> addList, @Param("userId") Long userId);

    List<InviteQBVehicleImportPO>   queryError(@Param("userId") Long userId);

    List<InviteQBVehicleImportPO> querySuccess(@Param("userId") Long userId);

    Integer querySucessCount(@Param("userId") Long userId);

    List<InviteQBVehicleImportPO> selectErrorPage(Page page, @Param("userId") Long userId);

    List<InviteQBVehicleImportPO> selectSuccessPage(Page page, @Param("userId") Long userId);

    void updateErrorById(@Param("userId") Long userId, @Param("list") List<String> list);

}
