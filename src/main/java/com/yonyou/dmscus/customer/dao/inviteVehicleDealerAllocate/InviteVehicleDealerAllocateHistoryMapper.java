package com.yonyou.dmscus.customer.dao.inviteVehicleDealerAllocate;

    
import com.yonyou.dmscus.customer.entity.po.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryPO;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 车店分配历史表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface InviteVehicleDealerAllocateHistoryMapper extends IMapper<InviteVehicleDealerAllocateHistoryPO> {

    void insertAllocateHistory(@Param("params")InviteVehicleDealerAllocateHistoryPO history);
}
