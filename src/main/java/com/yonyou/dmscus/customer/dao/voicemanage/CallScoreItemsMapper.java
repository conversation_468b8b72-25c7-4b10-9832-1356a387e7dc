package com.yonyou.dmscus.customer.dao.voicemanage;

import com.yonyou.dmscus.customer.utils.ai.AnalysisScoreItems;
import com.yonyou.dmscus.customer.utils.ai.AnalysisScoreItemsZj;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/25 0025
 */
public interface CallScoreItemsMapper {

    //批量插入
    void saveScoreItem(@Param("scoreItem") List<AnalysisScoreItems> scoreItem);

    void saveScoreZJItem(@Param("scoreItem") List<AnalysisScoreItemsZj> list);
}
