package com.yonyou.dmscus.customer.dao.invitationFollow;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.base.dao.IMapper;
import com.yonyou.dmscus.customer.dto.CustomerInfoListReturnDTO;
import com.yonyou.dmscus.customer.dto.VehicleInviteVO;
import com.yonyou.dmscus.customer.dto.cdp.CdpTagTaskParameterDto;
import com.yonyou.dmscus.customer.entity.dto.clue.InviteClueParamDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.InviteClueResultDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.dictionary.DictionaryIdDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleDto;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InsuranceBillDTO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.service.invitationautocreate.po.InviteVehiclePO;
import com.yonyou.dmscus.customer.util.common.ExcelGenerator;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.mapping.ResultSetType;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 车辆邀约记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
public interface InviteVehicleRecordMapper extends IMapper<InviteVehicleRecordPO> {

    List<InviteVehicleRecordDTO> selectInviteVehicleRecord(Page page, @Param("params") InviteVehicleRecordPO po);

    List<InviteVehicleRecordPO> getInviteVehicleRecordForSaSllocate(Page page,@Param("params") InviteVehicleRecordPO inviteVehicleRecordPO);

    List<InviteVehicleRecordPO> selectVehicleRecordAndSubcues(@Param("id")Long id);

    Integer getHasExtendedWarrantBugRecord(@Param("vin")String vin);
    Integer updateIsAi();

    /**
     * 导出查询
     * @param inviteVehicleRecordPO
     * @return
     */
    List<Map> exportExcelinviteVehicleRecord(@Param("params") InviteVehicleRecordPO inviteVehicleRecordPO);

    //保险跟进查询邀约记录----chensh
    List<InviteVehicleRecordPO> selectFollowInsureRecord(Page page, @Param("params") InviteVehicleRecordPO po);
    //导出查询--保险跟进--chensh
    List<Map> exportExcelFollowInsure(@Param("params") InviteVehicleRecordPO inviteVehicleRecordPO);
    //
    List<InviteVehicleRecordPO> selectInsuranceInvitePlan();
    //保险跟进失败 关闭线索 -- 计划任务
    int updateInsureFollowStatus(@Param("idList")List<Long> idList,@Param("userId")String userId);
    //保险跟进失败  车辆24个月内有过进厂  生成新的任务 -- 计划任务
    int insertInsureInviteTask(@Param("idList")List<Long> idList,@Param("userId")String userId);
    //根据VIN判断客户是否投保成功
    List<InsuranceBillDTO> selectInsuranceBill(@Param("vin") String vin, @Param("dealerCode") String dealerCode);

    /**
     * 查询范围内 未结束跟进的线索
     * @param dealerCode
     * @param adviseInDate
     * @param vin
     * @param mergeRule
     * @return
     */
    List<InviteVehicleRecordPO> queryInviteRecordForTask(@Param("dealerCode") String dealerCode, @Param("adviseInDate")
            Date adviseInDate, @Param("vin")  String vin, @Param("mergeRule") Integer mergeRule,@Param("id")Long id);


    /**
     * 查询自动任务创建的未分配线索 定首保及其它类型
     * @param createDate
     * @return
     */
    List<InviteVehicleRecordPO> queryWaitAllocationRecodeForOther(@Param("createDate")String createDate);

    /**
     * 查询自动任务创建的未分配线索 定首保及其它类型
     * @param vin
     * @return
     */
    List<InviteVehicleRecordPO> queryWaitAllocationRecodeForVin(@Param("vin")String vin);

    /**
     * 查询自动任务创建的未分配线索 续保 类型
     * @param createDate
     * @return
     */
    List<InviteVehicleRecordPO> queryWaitAllocationRecodeForInsurance(@Param("createDate")String createDate);

    /**
     * 查询子线索
     * @param id
     * @return
     */
    List<InviteVehicleRecordPO> queryInviteSubtextByid(@Param("id")Long id);

    void updateMainInvite(@Param("id")Long id);

    void updateOrderStatusById(@Param("id")Long id, @Param("orderStatus")Integer orderStatus);

    void updateSubtextInvite(@Param("mainId")Long mainId,@Param("id")Long id);

    List<InviteVehicleRecordPO> getWaitCloseMaintainRecord(@Param("vin")String vin);

    List<InviteVehicleRecordPO> getWaitCloseVocAccidentRecord(@Param("createDate")String createDate,@Param("vin") String vin);

    List<InviteVehicleRecordPO> getWaitCloseVulnerableRecord(@Param("vin")String vin,@Param("code") String code,
                                                             @Param("type")Integer type);

    /**
     * 更新未完成的
     * @param dealerCode
     * @param lastDealerCode
     */
    int updateInviteByDealerCode(@Param("vin")String vin,@Param("dealerCode")String dealerCode,@Param
            ("lastDealerCode") String lastDealerCode);

    Integer getNeedDistribute(@Param("dealerCode")String dealerCode,@Param("leaveIds")List<Integer> leaveIds);

    List<InviteVehicleRecordPO> getWaitCloseLostRecord(@Param("dealerCode")String dealerCode,@Param("vin") String vin);

    List<InviteVehicleRecordPO> getWaitCloseAlertRecord(@Param("dealerCode")String dealerCode,@Param("vin") String vin);

    InviteVehicleRecordPO getLatestError(@Param("dealerCode")String dealerCode,@Param("vin") String vin);

    void batchInsert(@Param("userId") Long userId);

    void batchVCDCInsert(@Param("userId") Long userId);

    void updateOneIdByMobile(@Param("params")CustomerInfoListReturnDTO dto);

    void batchUpdateOneIdByMobile(@Param("list")List<CustomerInfoListReturnDTO> list);

    void updateFordailyAverageMileageById(@Param("params")InviteVehicleRecordDTO dto);

    void updateFordailyAverageMileageByIds(@Param("params")List<InviteVehicleRecordDTO> params);

    /**重复数据清洗-关闭线索*/
    void doProDataCloseClue(@Param("params")List<InviteVehiclePO> params);

    /**重复数据清洗-关闭子线索*/
    void doProDataCloseSubClue(@Param("params")List<InviteVehiclePO> params);

    /**重复数据清洗-修改线索*/
    void doProDataUpdateClue(@Param("params")List<InviteVehiclePO> params);

    /**
     * 查询待关闭的保修线索
     * @param createDate
     * @return
     */
    List<InviteVehicleRecordPO> getWaitCloseGuaranteeRecord(String createDate);

    List<InviteVehicleRecordPO> getWaitCloseGuaranteeRecordByVin(String vin);

    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = 1000)
    @ResultType(HashMap.class)
    void exportExcelinviteVehicle(@Param("params")InviteVehicleRecordPO inviteVehicleRecordPO, ExcelGenerator<HashMap> excelGenerator);

    List<Map> exportExcelinviteVehicleDownlod(@Param("params")InviteVehicleRecordPO inviteVehicleRecordPO, @Param("offset") Integer offset,@Param("pSize") Integer pSize);
    
    void createTwiceFollow();

    List<InviteVehicleRecordPO> getInviteVehicleRecordCPort(Page page, @Param("params") InviteVehicleRecordPO po);

    List<InviteVehicleRecordPO> getRecordByAdviseInDate(@Param("startDate")String startDate, @Param("endDate")String endDate);

    int updateList(@Param("updateList")List<Long> list);

    int insertList(@Param("list")List<InviteVehicleRecordPO> list);

    List<InviteVehicleRecordPO> selectListByVin(@Param("vin")String vin);

    int updateAlertVocByVin(@Param("vin")String vin);

    List<InviteVehicleRecordPO> selectVocLossByVin(@Param("vin")String vin);

    List<InviteVehicleRecordPO> selectVocByVin(@Param("vin")String vin);

    //自店流失客户类型线索，状态为"未完成"或"逾期关闭"，建议进厂日期在2020年1月1日及以后
    int selectLossTag(@Param("vin")String vin, @Param("endTime")String endTime);

    /**通过vin查询未完成的线索数量*/
    List<InviteVehicleRecordPO> selectClueByVinAndInviteType(@Param("vin")String vin, @Param("flag") boolean flag);

    /**查询白名单线索*/
    List<InviteVehicleRecordPO> searchClues(@Param("list") List<String> list);

    /**查询所有经销商*/
    List<String> selectDealerCodeGroupBy();

    /**通过车架号,经销商查询未完成的线索: 首保,定保,流失预警*/
    List<InviteVehicleRecordPO> getOpenLeads(@Param("vinList") List<String> vinList,
                                             @Param("dealerCode") String dealerCode,
                                             @Param("inviteList") List<Integer> inviteList,
                                             @Param("createDate") String createDate);

    /**通过ID关闭线索*/
    int closeLeadById(@Param("ids")List<Long> ids, @Param("orderStatus")Integer orderStatus);

    /**通过ID批量完成线索*/
    int completeLeadsByIds(@Param("listUpPo")List<InviteVehicleRecordPO> listUpPo);

    /**查询待关闭线索*/
    List<InviteVehicleRecordPO> findCluesToClose(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**查询待验证邀约线索*/
    List<InviteVehicleRecordPO> getExpiredPendingLeads();
    /**邀约线索查询通用接口*/
    List<InviteClueResultDTO> selectInviteClue(Page page, @Param("params") InviteClueParamDTO params);
    InviteClueResultDTO selectNewInviteClue(@Param("vin") String vin, @Param("leadsType") Integer leadsType,  @Param("dealerCode") String dealerCode);
    /**补充扩展表数据start*/
    /**分页查询*/
    DictionaryIdDTO selectIdByLimit(@Param("startPage")int startPage, @Param("endPage")int endPage);
    /**通过线索表查询在扩展表没有的数据*/
    List<InviteClueResultDTO> findMissingExtendedDataByClue();
    /**补充扩展表数据end*/

    int updateAdviseInDate(@Param("vinList") List<CdpTagTaskParameterDto> vinList);

    /**初始化线索验证状态*/
    int updateVerifyStatus();
    /**通过vin修改线索的经销商(黑名单隐藏线索用)*/
    int updateDealerCodeByVin(@Param("vin")String vin);

}
