package com.yonyou.dmscus.customer.enevt.listener;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.dmscus.customer.enevt.AccidentClueInfoPushEvent;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesDTO;
import com.yonyou.dmscus.customer.service.accidentClues.CluePushService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/16 15:26
 * @Version 1.0
 */
@Component
@EnableAsync
public class AccidentClueInfoPushListener {

    @Resource
    private CluePushService cluePushService;

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @EventListener
    @Async("acThreadPool")
    public void pushMessage(AccidentClueInfoPushEvent event){

        List<AccidentCluesDTO> pushInfoList = event.getPushInfoList();
        logger.info("accident clue info event listener massage: {}", JSONObject.toJSONString(pushInfoList));

        for (AccidentCluesDTO pushInfo : pushInfoList) {
            logger.info("push info: {}", JSONObject.toJSONString(pushInfo));
            cluePushService.pushLiteCrmClueInfo(pushInfo);
        }
    }
}
