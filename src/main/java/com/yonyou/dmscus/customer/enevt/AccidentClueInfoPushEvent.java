package com.yonyou.dmscus.customer.enevt;

import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesDTO;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/17 15:58
 * @Version 1.0
 */
@Getter
public class AccidentClueInfoPushEvent extends ApplicationEvent {

    private final List<AccidentCluesDTO> pushInfoList;

    public AccidentClueInfoPushEvent(List<AccidentCluesDTO> pushInfoList) {
        super(pushInfoList);
        this.pushInfoList = pushInfoList;
    }
}
