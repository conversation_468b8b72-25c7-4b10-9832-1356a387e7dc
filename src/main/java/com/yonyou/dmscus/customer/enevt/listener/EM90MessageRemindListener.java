package com.yonyou.dmscus.customer.enevt.listener;

import com.yonyou.dmscus.customer.dto.EM90MessageRemindDto;
import com.yonyou.dmscus.customer.enevt.EM90MessageRemindEvent;
import com.yonyou.dmscus.customer.feign.ApplicationAfterSalesManagementClient;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
//@EnableAsync
public class EM90MessageRemindListener {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ApplicationAfterSalesManagementClient applicationAfterSalesManagementClient;

    @EventListener
    //@Async("acThreadPool")
    public void pushMessage(@NotNull EM90MessageRemindEvent event) {
        try {
            EM90MessageRemindDto em90MessageRemindDto = event.getEm90MessageRemindDto();
            logger.info("accident clue info event listener massage: {}", em90MessageRemindDto);
            applicationAfterSalesManagementClient.clueReminder(em90MessageRemindDto);
        } catch (Exception e) {
            logger.error("{}", e);
        }
    }
}
