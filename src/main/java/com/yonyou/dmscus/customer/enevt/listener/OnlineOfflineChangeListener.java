package com.yonyou.dmscus.customer.enevt.listener;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.dmscus.customer.dao.faultLight.RvdcVehOnlineOfflineChangeRecordMapper;
import com.yonyou.dmscus.customer.entity.po.faultLight.RvdcVehOnlineOfflineChangeRecordPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * description 线索状态监听
 * <AUTHOR>
 * @date 2023/9/5 16:29
 */

@Slf4j
@Component
public class OnlineOfflineChangeListener {

    private final Logger logger = LoggerFactory.getLogger(OnlineOfflineChangeListener.class);

    @Resource
    private RvdcVehOnlineOfflineChangeRecordMapper rvdcVehOnlineOfflineChangeRecordMapper;

    @KafkaListener(topics = "${topic.faultLightOnOff.topic}", groupId = "${topic.faultLightOnOff.group}")
    public void listen(String message) {
        logger.info("Received message: " + message);
        if (StringUtils.isNotEmpty(message)) {
            JSONObject result = null;
            try {
                result = JSONObject.parseObject(message);
                RvdcVehOnlineOfflineChangeRecordPO rvdcVehOnlineOfflineChangeRecordPO = JSONObject.parseObject(result.toString(), RvdcVehOnlineOfflineChangeRecordPO.class);
                log.info("VIN上下架数据落库，rvdcVehOnlineOfflineChangeRecordPO:{}" , rvdcVehOnlineOfflineChangeRecordPO);
                int insert = rvdcVehOnlineOfflineChangeRecordMapper.insert(rvdcVehOnlineOfflineChangeRecordPO);
            } catch (Exception e) {
                log.error("VIN上下架数据落库异常，message:{}" , message);
            }
        }
    }
}
