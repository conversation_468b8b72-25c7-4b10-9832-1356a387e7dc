package com.yonyou.dmscus.customer.enevt.listener;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.dmscus.customer.enevt.AccidentClueStatusChangeEvent;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.StatusChangePushDTO;
import com.yonyou.dmscus.customer.service.accidentClues.CluePushService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/16 15:26
 * @Version 1.0
 */
@Component
@EnableAsync
public class AccidentClueStatusChangeListener {

    @Resource
    private CluePushService cluePushService;

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @EventListener
    @Async("acThreadPool")
    public void pushMessage(AccidentClueStatusChangeEvent event){

        logger.info("accident clue event listener massage: {}", JSONObject.toJSONString(event.getPushInfoList()));

        List<StatusChangePushDTO> pushInfoList = event.getPushInfoList();
        for (StatusChangePushDTO pushInfo : pushInfoList) {
            logger.info("push info: {}", JSONObject.toJSONString(pushInfo));
            cluePushService.pushLiteCrmClueStatus(pushInfo);
        }
    }
}
