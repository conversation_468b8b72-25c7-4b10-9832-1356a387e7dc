package com.yonyou.dmscus.customer.enevt;

import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightTopicDTO;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * description 线索状态变更时间
 * <AUTHOR>
 * @date 2023/9/5 15:44
 */
public class LeadStatusChangeEvent extends ApplicationEvent {

    @Getter
    private List<FaultLightTopicDTO> list;

    public LeadStatusChangeEvent(List<FaultLightTopicDTO> list) {
        super(list);
        this.list = list;
    }
}
