package com.yonyou.dmscus.customer.enevt.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yonyou.dmscus.customer.enevt.LeadStatusChangeEvent;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightTopicDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * description 线索状态监听
 * <AUTHOR>
 * @date 2023/9/5 16:29
 */
@Component
@Slf4j
public class LeadStatusChangeListener {
    @Resource
    private RocketMQTemplate rocketMQTemplate;
    @Value("${topic.faultLight:TOPIC_FAULT_LIGHT}")
    private String faultLightTopic;

    @Async
    @EventListener
    public void sendMessage(LeadStatusChangeEvent event) {
        List<FaultLightTopicDTO> list = event.getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (FaultLightTopicDTO dto : list) {
            log.info("clue,sendMessage:{}", dto);
            if (Objects.isNull(dto)) {
                continue;
            }
            push(dto);
        }
    }

    private void push(Object body) {
        try {
            rocketMQTemplate.asyncSend(faultLightTopic, new Message<String>() {
                @Override
                public String getPayload() {
                    return JSON.toJSONString(body);
                }

                @Override
                public MessageHeaders getHeaders() {
                    HashMap<String, Object> headers = Maps.newHashMap();
                    return new MessageHeaders(headers);
                }
            }, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("pushMessage,消息投递成功,result:{}", JSON.toJSONString(sendResult));
                }

                @Override
                public void onException(Throwable throwable) {
                    log.error("pushMessage,消息投递失败:{}", throwable);
                }
            });
        } catch (Exception e) {
            log.error("pushMessage,推送消息失败:{}", e);
        }
    }
}
