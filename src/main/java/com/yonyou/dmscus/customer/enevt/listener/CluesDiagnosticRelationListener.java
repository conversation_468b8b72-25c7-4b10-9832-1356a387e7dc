package com.yonyou.dmscus.customer.enevt.listener;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.faultLight.CluesDiagnosticInfoRelationMapper;
import com.yonyou.dmscus.customer.entity.po.faultLight.CluesDiagnosticInfoRelationPO;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 通过kafka获取线索与诊断信息的关联关系
 */
@Component
public class CluesDiagnosticRelationListener {
    private final Logger logger = LoggerFactory.getLogger(CluesDiagnosticRelationListener.class);
    private static final Long BASE_RETRY_TIME = 60 * 1000L;
    private final Map<Integer, Integer> exceptionMessageMap = new ConcurrentHashMap<>();
    private static final Integer RETRY_COUNT = 4;
    @Resource
    private CluesDiagnosticInfoRelationMapper cluesDiagnosticInfoRelationMapper;
    @KafkaListener(topics = "${topic.cluesDiagnosticRelation.topic:topic_failind_dtc_dim_info_bind_rela}"
            , groupId = "${topic.cluesDiagnosticRelation.group:topic_group_failind_dtc_dim_info_bind_rela_newbie}"
            , containerFactory = "dtcDimRelContainerFactory")
    public void consumerRelationMessage(ConsumerRecord<?, String> record, Acknowledgment acknowledgment) {
        logger.info("dtc dim bind relation consumer message: {}", record);
        String message = record.value();
        if (StringUtils.isNullOrEmpty(message)) {
            acknowledgment.acknowledge();
            return;
        }
        try {
            JSONObject jsonObject = JSONObject.parseObject(message);
            long leadsPreprocessingId = jsonObject.getLongValue("leads_preprocessing_id");
            if (leadsPreprocessingId == 0L) {
                logger.info("consumer clues diagnostic info relation data illegality: {}", message);
                acknowledgment.acknowledge();
            } else {
                Boolean existFlag = messageRepeatabilityDetection(leadsPreprocessingId);
                if (Boolean.TRUE.equals(existFlag)) {
                    acknowledgment.acknowledge();
                    return;
                }
                String ctUpload = jsonObject.getString("ct_upload");
                String etlTime = jsonObject.getString("_etl_time");
                CluesDiagnosticInfoRelationPO relationPO = CluesDiagnosticInfoRelationPO.builder()
                        .leadsPreprocessingId(leadsPreprocessingId)
                        .ctUpload(ctUpload)
                        .etlTime(etlTime)
                        .build();
                int result = cluesDiagnosticInfoRelationMapper.insert(relationPO);
                if (result < 1) {
                    logger.error("insert clues diagnostic info relation to database failure: {}", message);
                    handleExceptionByRetries(message, acknowledgment);
                }
                acknowledgment.acknowledge();
            }
        } catch (Exception e) {
            logger.error("consumer clues diagnostic info relation exception: ", e);
            handleExceptionByRetries(message, acknowledgment);
        }
    }
    private void handleExceptionByRetries(String message, Acknowledgment acknowledgment) {
        int messageHashCode = message.hashCode();
        Integer retryCount = exceptionMessageMap.get(messageHashCode);
        if (Objects.isNull(retryCount)) {
            exceptionMessageMap.put(messageHashCode, CommonConstants.NUM_1);
            assert acknowledgment != null;
            acknowledgment.nack(BASE_RETRY_TIME);
        } else {
            exceptionMessageMap.put(messageHashCode, ++retryCount);
            if (retryCount > RETRY_COUNT) {
                logger.error("exception still thrown after retry, message: {}", message);
                exceptionMessageMap.remove(messageHashCode);
                acknowledgment.acknowledge();
            } else {
                acknowledgment.nack(retryCount * BASE_RETRY_TIME * 2);
            }
        }
    }
    private Boolean messageRepeatabilityDetection(long leadsPreprocessingId) {
        LambdaQueryWrapper<CluesDiagnosticInfoRelationPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CluesDiagnosticInfoRelationPO::getIsDeleted, CommonConstants.NUM_0);
        queryWrapper.eq(CluesDiagnosticInfoRelationPO::getLeadsPreprocessingId, leadsPreprocessingId);
        List<CluesDiagnosticInfoRelationPO> relationPOS = cluesDiagnosticInfoRelationMapper.selectList(queryWrapper);
        return CollectionUtils.isNotEmpty(relationPOS) ? Boolean.TRUE : Boolean.FALSE;
    }
}
