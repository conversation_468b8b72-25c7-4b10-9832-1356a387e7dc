package com.yonyou.dmscus.customer.enevt.listener;

import com.yonyou.dmscus.customer.enevt.VocEvent;
import com.yonyou.dmscus.customer.service.oss.OssService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


/**
 * 监听事件
 */
@Component
@Slf4j
public class VocEventListener {

    @Autowired
    private OssService ossService;

    //数据从流水到信息
    @Async
    @EventListener
    public void setVoc(VocEvent voc) {
        try {
            ossService.getcontent(voc.getUpdateTime());
        } finally {

        }
    }
}
