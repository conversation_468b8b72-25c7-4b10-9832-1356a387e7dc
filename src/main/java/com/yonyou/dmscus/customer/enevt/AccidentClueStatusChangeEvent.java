package com.yonyou.dmscus.customer.enevt;

import com.yonyou.dmscus.customer.entity.dto.accidentClues.StatusChangePushDTO;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/16 15:19
 * @Version 1.0
 */
@Getter
public class AccidentClueStatusChangeEvent extends ApplicationEvent {

    private final List<StatusChangePushDTO> pushInfoList;

    public AccidentClueStatusChangeEvent(List<StatusChangePushDTO> pushInfoList) {
        super(pushInfoList);
        this.pushInfoList = pushInfoList;
    }
}
