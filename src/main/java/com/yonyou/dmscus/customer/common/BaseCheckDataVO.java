package com.yonyou.dmscus.customer.common;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yonyou.dmscloud.function.domains.dto.DataImportDto;

public class BaseCheckDataVO {
	
    private List<String> errorList = new ArrayList<String>();

    //excel的一行数据
    private Map<Integer, String> rowData = Maps.newHashMap();
    
    //导入返回的错误数据列表
    private List<DataImportDto> importErrorList = Lists.newArrayList();
    
    //第几行
    private int rowId;

	public List<String> getErrorList() {
        return errorList;
    }

    public void setErrorList(List<String> errorList) {
        this.errorList = errorList;
    }
    

    public Map<Integer, String> getRowData() {
        return rowData;
    }

    public void setRowData(Map<Integer, String> rowData) {
        this.rowData = rowData;
    }
    
    
    public void addError(String errorMsg) {
        this.errorList.add("</br> 第" + rowId + "行:" + errorMsg);
		
		DataImportDto tmp = new DataImportDto(); tmp.setRowNO(rowId);
		tmp.setErrorMsg(errorMsg); this.importErrorList.add(tmp);
		 
    }
    
    /**
     * 
     * 功能描述: <br>
     * 得到当前行某列的数据
     *
     * @param column 列
     * @return
     */
    public String getColumnValue(Integer column) {
        if(rowData != null) {
            String columnVal = rowData.get(column);
            if(columnVal != null){
                columnVal = columnVal.trim();
            }
            return columnVal;
        }
        
        return null;
    }

    public int getRowId() {
        return rowId;
    }

    public void setRowId(int rowId) {
        this.rowId = rowId;
    }

	public List<DataImportDto> getImportErrorList() {
		return importErrorList;
	}

	public void setImportErrorList(List<DataImportDto> importErrorList) {
		this.importErrorList = importErrorList;
	}
}
