package com.yonyou.dmscus.customer.common;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 
 * 〈一句话功能简述〉<br> 
 * 通用的rest api返回对象，用在controller返回给前端。
 *
 * @see [相关类/方法]（可选）
 * @since [产品/模块版本] （可选）
 */
public class AjaxResponse {
    private final Logger logger = LoggerFactory.getLogger(AjaxResponse.class);

    
    /**
     * 请求失败常量
     */
    public static final int FAILD = 0;
    
    /**
     * 请求成功常量
     */
    public static final int SUCCESS = 1;

    
    /**
     * 返回结果 0表示失败 1表示成功
     */
    private int result;
    
    /**
     * 业务代码：比如失败原因代码，处理成功后的不同状态等。
     */
    private String code="";
    
    /**
     * 返回提示信息，比如失败原因描述，处理成功后的不同状态描述等。
     * 
     */
    private String msg="";
    
    
    /** 返回多个对象 */
    private final Map<String, Object> response = new HashMap<String, Object>();
    
  
    /**
     * 构造函数，默认设置状态为： 1-成功
     */
    public AjaxResponse() {
        result = SUCCESS;
    }
    
    /**
     * 
     * 功能描述: <br>
     * 设置result等于1-成功，并设置code和msg.
     *
     * @param code
     * @param msg
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public void setSuccess(String code,String msg) {
        this.code = code;
        this.msg = msg;
        this.result = SUCCESS;
    }   
    
    /**
     * 
     * 功能描述: <br>
     * 设置result等于0-失败，并设置code和msg.
     *
     * @param msg 失败原因
     * @param code 失败原因代码
     */
    public void setFail(String code,String msg) {
        this.code = code;
        this.msg = msg;       
        this.result = FAILD;
    }   
    
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    
    /**
     * @return result 调用结果
     */
    public int getResult() {
        return result;
    }
    /**
     * @param result 调用结果
     */
    public void setResult(int result) {
        this.result = result;
    }
    /**
     * @return msg 提示信息
     */
    public String getMsg() {
        return msg;
    }
    /**
     * @param msg 提示信息
     */
    public void setMsg(String msg) {
        this.msg = msg;
    }
    
    /**
     * 功能描述: <br>
     * 把obj放入response,
     *
     * @param key
     * @param obj
     */
    public void addObject(String key, Object obj) {
        response.put(key, obj);
    }
    
    /**
     * 
     * 功能描述: <br>
     * 把obj放入response, 如果obj为空，就放人缺省的defaultValue。
     *
     * @param key key
     * @param obj 真实值
     * @param defaultValue 缺省值
     */
    public void addObject(String key, Object obj, Object defaultValue) {
        if(obj != null ) {
            response.put(key, obj);
        } else {
            response.put(key, defaultValue);
        }
    }
    
    /**
     * 
     * 功能描述: <br>
     * 把obj放入response, 如果obj为空，就放人缺省的类型。
     *
     * @param key key
     * @param obj 真实值
     * @param defaultClazz 缺省的类型
     */
    public <T> void addObject(String key, Object obj, Class<T> defaultClazz) {
        if(obj != null ) {
            response.put(key, obj);
        } else {
            try {
                response.put(key, defaultClazz.newInstance());
            } catch (Exception e) {
                logger.error("addObject with defaultClazz error. key="+key, e);
            }
        }
    }
    
    public Object getObject(String key) {
        return response.get(key);
    }
    
    public Map<String, Object> getResponse() {
		return response;
	}

	/**
     * 
     * 功能描述: <br>
     * 设置result等于1-成功，并设置code和msg.
     *
     * @param code
     * @param msg
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static AjaxResponse success(String code,String msg) {
        AjaxResponse ajaxResponse = new AjaxResponse();        
        ajaxResponse.setSuccess(code, msg);
        
        return ajaxResponse;
    }   
    
    /**
     * 
     * 功能描述: <br>
     * 设置result等于0-失败，并设置code和msg.
     *
     * @param msg 失败原因
     * @param code 失败原因代码
     */
    public static AjaxResponse fail(String code,String msg) {
        AjaxResponse ajaxResponse = new AjaxResponse();        
        ajaxResponse.setFail(code, msg);       
        return ajaxResponse;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
    
    @JsonIgnore
    public boolean isSuccess() {
        return result == SUCCESS;
    }
    
    @JsonIgnore
    public boolean isFail() {
        return result == FAILD;
    }
    
}
