package com.yonyou.dmscus.customer.configuration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

@Slf4j
public class SwaggerFilter implements Filter {

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		HttpServletRequest httpRequest = (HttpServletRequest) request;
		// 1. 判断是否是目标接口
		if ("/v2/api-docs".equals(httpRequest.getRequestURI())) {
			log.info("doFilter v2 api docs start");
			// 这里可以修改 Swagger 的 JSON 输出
			String str = "";
			ClassPathResource resource = new ClassPathResource("swagger.json");
			try (InputStream inputStream = resource.getInputStream()) {
				str = StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);
				log.info("doFilter v2 api-docs str get end");
			}
			response.setCharacterEncoding("UTF-8");
			response.setContentType("application/json; charset=utf-8");
			response.getOutputStream().write(str.getBytes());
			log.info("doFilter v2 api docs write end");
		} else {
			chain.doFilter(request, response);
		}
	}
}