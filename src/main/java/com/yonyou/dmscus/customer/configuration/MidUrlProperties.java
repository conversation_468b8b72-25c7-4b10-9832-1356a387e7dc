package com.yonyou.dmscus.customer.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Data
@Component
@ConfigurationProperties(prefix = "access.mid.url")
public class MidUrlProperties extends BaseUrlProperties {

    private String midEndAuthCenter;

    private String midEndVehicleCenter;

    private String midEndOrgCenter;

    private String midEndBasicdataCenter;

    private String midEndCouponCenter;

    private String midEndCustomerCenter;

    private String midEndMessageCenter;

    private String mid51DKCenter;

    private String datain;


/**------------------------------------------mid-end-auth-center-----------------------------------------------------------*/
    private String usersOem = "/users/oem";

    private String usersDealer = "/users/dealer";

    private String roleOrgIdUser = "/role/orgId/user";

    private String dealerUser = "/role/dealer/user";

    private String userInfo = "/user/info";

    private String userInfoNew = "/user/info/new";

    private String orgRole = "/users/org/role";

    private String roleInfo = "/role/info";

    private String emp = "/emp/emp";

    //中台 通过经销商代码和角色代码查询员工列表
    private String empRoleCodeUrl = "/role/dealer/user";

    /**
     * 员工及职位信息查询
     */
    private String volvoMidEmpPosition = "/emp/position";


    /**----------------------------------------------mid-end-vehicle-center-------------------------------------------------------*/
    private String vehicleVin = "/vehicle/vin/";

    private String listOwnerVehiclePage = "/vehicle/listOwnerVehiclePage";

    private String vinList = "/vehicle/vinList";

    private String vehicle = "/vehicle";

/**----------------------------------------------mid-end-org-center-------------------------------------------------------*/
    private String selectOrgInfo = "/tmOrg/selectOrgInfo";

    private String downOrgInfo = "/tmOrg/downOrgInfo/";

    private String selectCompanyInfo = "/org/company/selectCompanyInfo";

    private String selectDealerByGroupCode = "/org/companyGroup/selectDealerByGroupCode";

    private String selectByCompanyCode = "/org/company/selectByCompanyCode";

    private String isExistByCode = "/org/company/IsExistByCode";

    private String companyInfo = "/org/company/companyInfo";

    private String selectCompanyByCompanyCode = "/org/company/selectCompanyByCompanyCode/";

/**---------------------------------------------mid-end-basicdata-center--------------------------------------------------------*/
    private String modelAll = "/basicdata/model/list";

    private String modelById = "/basicdata/model/getModelById";
/**-----------------------------------------------mid-end-coupon-center------------------------------------------------------*/
    private String allEx = "/coupon/tt-coupon-detail/allEx";

    private String ttCouponInfo  = "/coupon/tt-coupon-info";

    private String ttCouponDetail = "/coupon/tt-coupon-detail";

    private String couponRecharge = "/couponRecharge";

    private String ttCouponVerifyAll  = "/coupon/tt-coupon-verify/all";

    private String ttCouponVerifyAllByPage  = "/coupon/tt-coupon-verify/pageCouponVerifyCouponInfo";

/**------------------------------------------mid-end-customer-center-----------------------------------------------------------*/
    private String saveList = "/customer/customer-info/saveList";

    private String oneIdList = "/customer/vehicle-owner/selectVehicleOwnerByOneIdList";

/**----------------------------------------------mid-end-message-center-------------------------------------------------------*/
    private String pushV1emails = "/push/v1/emails";

    private String pushV1Apps = "/push/v1/apps";

    private String pushV1smss = "/push/v1/smss";

    /**----------------------------------------------mid-end-dict-center-------------------------------------------------------*/

    /**
     * 51DK中台 客诉同步接口
     */
    private String complaintInfoSync = "/qualityReport/synchronizeCustomerLitigation";

    /**----------------------------------------------4IN1------------------------------------------------------*/

    /**
     * 51DK中台 客诉同步接口
     */
    private String updateSaleComplaint = "/api/services/app/Order/updateSaleComplaint";

    /**------------------------------------------datain-----------------------------------------------------------*/

    /**
     * 51DK中台 客诉同步接口
     */
    private String warning = "/failure-light-api/dl/vehicle/warning";

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof MidUrlProperties)) return false;
        if (!super.equals(o)) return false;
        MidUrlProperties that = (MidUrlProperties) o;
        return Objects.equals(getMidEndAuthCenter(), that.getMidEndAuthCenter()) && Objects.equals(getMidEndVehicleCenter(), that.getMidEndVehicleCenter()) && Objects.equals(getMidEndOrgCenter(), that.getMidEndOrgCenter()) && Objects.equals(getMidEndBasicdataCenter(), that.getMidEndBasicdataCenter()) && Objects.equals(getMidEndCouponCenter(), that.getMidEndCouponCenter()) && Objects.equals(getMidEndCustomerCenter(), that.getMidEndCustomerCenter()) && Objects.equals(getMidEndMessageCenter(), that.getMidEndMessageCenter()) && Objects.equals(getUsersOem(), that.getUsersOem()) && Objects.equals(getUsersDealer(), that.getUsersDealer()) && Objects.equals(getRoleOrgIdUser(), that.getRoleOrgIdUser()) && Objects.equals(getDealerUser(), that.getDealerUser()) && Objects.equals(getUserInfo(), that.getUserInfo()) && Objects.equals(getUserInfoNew(), that.getUserInfoNew()) && Objects.equals(getOrgRole(), that.getOrgRole()) && Objects.equals(getRoleInfo(), that.getRoleInfo()) && Objects.equals(getEmp(), that.getEmp()) && Objects.equals(getVehicleVin(), that.getVehicleVin()) && Objects.equals(getListOwnerVehiclePage(), that.getListOwnerVehiclePage()) && Objects.equals(getVinList(), that.getVinList()) && Objects.equals(getVehicle(), that.getVehicle()) && Objects.equals(getSelectOrgInfo(), that.getSelectOrgInfo()) && Objects.equals(getDownOrgInfo(), that.getDownOrgInfo()) && Objects.equals(getSelectCompanyInfo(), that.getSelectCompanyInfo()) && Objects.equals(getSelectDealerByGroupCode(), that.getSelectDealerByGroupCode()) && Objects.equals(getSelectByCompanyCode(), that.getSelectByCompanyCode()) && Objects.equals(getIsExistByCode(), that.getIsExistByCode()) && Objects.equals(getCompanyInfo(), that.getCompanyInfo()) && Objects.equals(getSelectCompanyByCompanyCode(), that.getSelectCompanyByCompanyCode()) && Objects.equals(getModelAll(), that.getModelAll()) && Objects.equals(getAllEx(), that.getAllEx()) && Objects.equals(getTtCouponInfo(), that.getTtCouponInfo()) && Objects.equals(getTtCouponDetail(), that.getTtCouponDetail()) && Objects.equals(getCouponRecharge(), that.getCouponRecharge()) && Objects.equals(getTtCouponVerifyAll(), that.getTtCouponVerifyAll()) && Objects.equals(getSaveList(), that.getSaveList()) && Objects.equals(getPushV1emails(), that.getPushV1emails()) && Objects.equals(getPushV1Apps(), that.getPushV1Apps());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getMidEndAuthCenter(), getMidEndVehicleCenter(), getMidEndOrgCenter(), getMidEndBasicdataCenter(), getMidEndCouponCenter(), getMidEndCustomerCenter(), getMidEndMessageCenter(), getUsersOem(), getUsersDealer(), getRoleOrgIdUser(), getDealerUser(), getUserInfo(), getUserInfoNew(), getOrgRole(), getRoleInfo(), getEmp(), getVehicleVin(), getListOwnerVehiclePage(), getVinList(), getVehicle(), getSelectOrgInfo(), getDownOrgInfo(), getSelectCompanyInfo(), getSelectDealerByGroupCode(), getSelectByCompanyCode(), getIsExistByCode(), getCompanyInfo(), getSelectCompanyByCompanyCode(), getModelAll(), getAllEx(), getTtCouponInfo(), getTtCouponDetail(), getCouponRecharge(), getTtCouponVerifyAll(), getSaveList(), getPushV1emails(), getPushV1Apps());
    }
}
