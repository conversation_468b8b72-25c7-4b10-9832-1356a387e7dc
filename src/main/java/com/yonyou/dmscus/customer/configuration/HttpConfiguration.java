package com.yonyou.dmscus.customer.configuration;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.ssl.SSLContexts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
public class HttpConfiguration {

    @Autowired
    private TokenInterceptor tokenInterceptor;

    /**
     * RestTemplate 支持 https
     *
     * @return RestTemplate
     */
    @LoadBalanced
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate(generateHttpsRequestFactory(true));

        restTemplate.setInterceptors(Collections.singletonList(tokenInterceptor));
        // 设置Convert参数
        setConvert(restTemplate);

        return restTemplate;
    }

    /**
     * RestTemplate 支持 https ， 并且在接受到返回后不会跟随302跳转
     *
     * @return RestTemplate
     */
    @LoadBalanced
    @Bean
    public RestTemplate noRedirectRestTemplate() {
        RestTemplate restTemplate = new RestTemplate(generateHttpsRequestFactory(false));

        // 设置Convert参数
        setConvert(restTemplate);

        return restTemplate;
    }

    /**
     * 直连请求，无客户端负载 LoadBalanced
     *
     * @return RestTemplate
     */
    @Bean
    public RestTemplate directRestTemplate() {
        RestTemplate restTemplate = new RestTemplate(generateHttpsRequestFactory(true));
        restTemplate.setInterceptors(Collections.singletonList(tokenInterceptor));
        setConvert(restTemplate);
        return restTemplate;
    }

    /**
     * 创建SSL HTTP工厂
     *
     * @return SSL HTTP 工厂
     */
    private HttpComponentsClientHttpRequestFactory generateHttpsRequestFactory(boolean redirectable) {
        try {
            TrustStrategy acceptingTrustStrategy = (x509Certificates, authType) -> true;
            SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(null, acceptingTrustStrategy).build();
            SSLConnectionSocketFactory connectionSocketFactory =
                    new SSLConnectionSocketFactory(sslContext, new NoopHostnameVerifier());

            HttpClientBuilder httpClientBuilder = HttpClientBuilder
                    .create()
                    .setSSLSocketFactory(connectionSocketFactory);
            if (!redirectable) {
                httpClientBuilder.disableRedirectHandling();
            }
            CloseableHttpClient httpClient = httpClientBuilder.build();

            HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
            factory.setHttpClient(httpClient);
            factory.setConnectTimeout(10 * 1000);
            factory.setReadTimeout(120 * 1000);
            return factory;
        } catch (Exception e) {
            throw new RuntimeException("创建HttpsRestTemplate失败", e);
        }
    }

    private void setConvert(RestTemplate restTemplate) {
        setJasksonConvert(restTemplate);

        // 处理中文乱码
        //restTemplate.getMessageConverters().add(new StringHttpMessageConverter(StandardCharsets.UTF_8));
    }

    private void setJasksonConvert(RestTemplate restTemplate) {
        boolean setJasksonConvertSuccess = false;

        // 设置 json 转换， 驼峰命名转json的下划线命名
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(new PropertyNamingStrategy());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        List<HttpMessageConverter<?>> httpMessageConverterList = restTemplate.getMessageConverters();

        for(int i =0; i<httpMessageConverterList.size(); i++) {
            if(httpMessageConverterList.get(i) instanceof  MappingJackson2HttpMessageConverter) {
                restTemplate.getMessageConverters().set(i, new MappingJackson2HttpMessageConverter(objectMapper));
                setJasksonConvertSuccess = true;
                break;
            }
        }

        if(!setJasksonConvertSuccess) {
            restTemplate.getMessageConverters().add(new MappingJackson2HttpMessageConverter(objectMapper));
        }
    }
}
