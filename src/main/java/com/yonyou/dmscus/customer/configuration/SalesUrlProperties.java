package com.yonyou.dmscus.customer.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Data
@Component
@ConfigurationProperties(prefix = "access.sales.url")
public class SalesUrlProperties extends BaseUrlProperties{

    private String downloadService;

    private String exportExcelUrl = "/download/exportExcel";

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SalesUrlProperties)) return false;
        if (!super.equals(o)) return false;
        SalesUrlProperties that = (SalesUrlProperties) o;
        return Objects.equals(getDownloadService(), that.getDownloadService()) && Objects.equals(getExportExcelUrl(), that.getExportExcelUrl());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getDownloadService(), getExportExcelUrl());
    }
}
