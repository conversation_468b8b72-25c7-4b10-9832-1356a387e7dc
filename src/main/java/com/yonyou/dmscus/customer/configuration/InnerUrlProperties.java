package com.yonyou.dmscus.customer.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Data
@Component
@ConfigurationProperties(prefix = "access.innter.url")
public class InnerUrlProperties extends BaseUrlProperties{

    private String baseUrl;

    private String inviteInsuranceVCDCExport = "http://dmscus-customer/inviteInsuranceVCDC/exportExcel/oss";

    private String accidentCluesExport = "http://dmscus-customer/accidentClues/exportExcelData";

    private String downloadAccidentReturn = "http://application-maintain-management/accidentClues/export/oss";

    private String downloadConGoodwillstampsUrl = "http://dmscus-customer/goodwillStamps/downLoadConGoodwillStamps";
    
    private String downloadInviteManageVCDCUrl = "http://dmscus-customer/invitationManageVCDC/downLoadExportExcel";

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof InnerUrlProperties)) return false;
        if (!super.equals(o)) return false;
        InnerUrlProperties that = (InnerUrlProperties) o;
        return Objects.equals(getBaseUrl(), that.getBaseUrl()) && Objects.equals(getInviteInsuranceVCDCExport(), that.getInviteInsuranceVCDCExport()) && Objects.equals(getAccidentCluesExport(), that.getAccidentCluesExport()) && Objects.equals(getDownloadAccidentReturn(), that.getDownloadAccidentReturn());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getBaseUrl(), getInviteInsuranceVCDCExport(), getAccidentCluesExport(), getDownloadAccidentReturn());
    }
}
