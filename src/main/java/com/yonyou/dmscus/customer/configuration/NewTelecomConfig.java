package com.yonyou.dmscus.customer.configuration;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@RefreshScope
@Component
@Configuration
@ConfigurationProperties(prefix = "newtelecom")
public class NewTelecomConfig {

    private String appKey;
    private String secret;
    private String basicUrl;
    private String authenticate;
    private String info;
    private String bind;
    private String unbind;
    private String bindB;
    private String unbindB;

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getBasicUrl() {
        return basicUrl;
    }

    public void setBasicUrl(String basicUrl) {
        this.basicUrl = basicUrl;
    }

    public String getBind() {
        return bind;
    }

    public void setBind(String bind) {
        this.bind = bind;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getUnbind() {
        return unbind;
    }

    public void setUnbind(String unbind) {
        this.unbind = unbind;
    }


    public String getAuthenticate() {
        return authenticate;
    }

    public void setAuthenticate(String authenticate) {
        this.authenticate = authenticate;
    }

    public String getBindB() {
        return bindB;
    }

    public void setBindB(String bindB) {
        this.bindB = bindB;
    }

    public String getUnbindB() {
        return unbindB;
    }

    public void setUnbindB(String unbindB) {
        this.unbindB = unbindB;
    }

    @Override
    public String toString() {
        return "NewTelecomConfig{" +
                "appKey='" + appKey + '\'' +
                ", secret='" + secret + '\'' +
                ", basicUrl='" + basicUrl + '\'' +
                ", authenticate='" + authenticate + '\'' +
                ", info='" + info + '\'' +
                ", bind='" + bind + '\'' +
                ", unbind='" + unbind + '\'' +
                ", bindB='" + bindB + '\'' +
                ", unbindB='" + unbindB + '\'' +
                '}';
    }

}
