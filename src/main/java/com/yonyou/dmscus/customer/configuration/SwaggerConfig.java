package com.yonyou.dmscus.customer.configuration;


import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

@Slf4j
@Configuration
public class SwaggerConfig {
	@Bean
	public Docket createRestApi() {
		// 创建 Docket 对象
		return new Docket(DocumentationType.SWAGGER_2) // 文档类型，使用 Swagger2
				.apiInfo(this.apiInfo()) // 设置 API 信息
				// 扫描 Controller 包路径，获得 API 接口
				.select()
				.apis(RequestHandlerSelectors.basePackage("com.yonyou")) // 修改为自己项目controller包目录
				.paths(PathSelectors.any())
				// 构建出 Docket 对象
				.build();
	}

	private ApiInfo apiInfo() {
		return new ApiInfoBuilder()
				.title("dmscloud-service") // 服务名称
				.description("老售后维修业务") // 服务描述
				.version("1.0.0") // 版本号
				.contact(new Contact("elvis", "elvis", "elvis")) // 联系人
				.build();
	}

	@Bean
	public FilterRegistrationBean<SwaggerFilter> swaggerFilterRegistration() {
		log.info("FilterRegistrationBean SwaggerFilter start");
		FilterRegistrationBean<SwaggerFilter> registration = new FilterRegistrationBean<>();
		registration.setFilter(new SwaggerFilter());
		registration.addUrlPatterns("/v2/api-docs"); // 确保路径匹配
		registration.setOrder(Ordered.HIGHEST_PRECEDENCE); // 提高优先级
		return registration;
	}
}