package com.yonyou.dmscus.customer.configuration;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

@Configuration
@Slf4j
public class RedissonAutoConfig {
	@Value("${spring.redis.host}")
	private String host;
	@Value("${spring.redis.password}")
	private String pwd;
	@Value("${spring.redis.port}")
	private Integer port;
	@Value("${spring.redis.database}")
	private Integer database;
	@Value("${redisson.watchdog:30000}")
	private Long watchDog;

	/**
	 * 单机模式自动装配
	 */
	@Bean
	@ConditionalOnProperty(name = "spring.redis.host")
	public RedissonClient redissonSingle() {
		Config config = new Config();
		SingleServerConfig serverConfig = config.useSingleServer()
				.setAddress("redis://" + host + ":" + port)
				.setTimeout(3000)
				.setConnectionPoolSize(64)
				.setConnectionMinimumIdleSize(32);
		config.setLockWatchdogTimeout(watchDog);

		if (StringUtils.isNotBlank(pwd)) {
			serverConfig.setPassword(pwd);
		}
		return Redisson.create(config);
	}

	/**
	 * 通过 RedisMessageListenerContainer 配置监听生效
	 **/
	@Primary
	@Bean
	RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory) {
		RedisMessageListenerContainer container = new RedisMessageListenerContainer();
		container.setConnectionFactory(connectionFactory);
		return container;
	}
}
