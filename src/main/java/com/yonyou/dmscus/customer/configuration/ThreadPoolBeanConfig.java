package com.yonyou.dmscus.customer.configuration;

import com.google.common.collect.Maps;
import com.yonyou.cyxdms.common.context.CustomerAnnotationConfigServletWebServerApplicationContext;
import com.yonyou.cyxdms.common.entity.MultiKey;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscus.customer.util.HeaderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@EnableAsync
public class ThreadPoolBeanConfig {

    public static final String ASYNC_THREADPOOL = "asyncthreadpool";
    public static final String UPDATE_RETURN_INTENTION_LEVEL = "updateReturnIntentionLevel";
    public static final String AC_THREAD_POOL = "acThreadPool";

    private Integer coreSize = Runtime.getRuntime().availableProcessors() + 1;
    private Integer maxSize = Runtime.getRuntime().availableProcessors() * 2;
    @Value("${framework.threadPool.capacity:500}")
    private Integer capacity;
    private int corePoolSize = coreSize;

    @Bean
    public Executor asyncThreadPool() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(corePoolSize);
        taskExecutor.setMaxPoolSize(maxSize);
        taskExecutor.setQueueCapacity(capacity);
        taskExecutor.setThreadNamePrefix(ASYNC_THREADPOOL);
        taskExecutor.setTaskDecorator(new AsyncTaskDecorator());
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        taskExecutor.initialize();
        return taskExecutor;
    }

    @Bean
    public Executor asyncUpdateReturnIntentionLevel() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(10);
        taskExecutor.setMaxPoolSize(20);
        taskExecutor.setQueueCapacity(capacity);
        taskExecutor.setThreadNamePrefix(UPDATE_RETURN_INTENTION_LEVEL);
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        taskExecutor.initialize();
        return taskExecutor;
    }

    @Bean
    public Executor acThreadPool() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(coreSize * 2);
        taskExecutor.setMaxPoolSize(maxSize * 2);
        taskExecutor.setQueueCapacity(capacity);
        taskExecutor.setThreadNamePrefix(AC_THREAD_POOL);
        taskExecutor.setTaskDecorator(new AsyncTaskDecorator());
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        taskExecutor.initialize();
        return taskExecutor;
    }
}

@Slf4j
class AsyncTaskDecorator implements TaskDecorator {


    public static final String LOGIN_INFO = "loginInfo";

    @Override
    public Runnable decorate(Runnable runnable) {
        try {
            //登录信息
            LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
            Map<MultiKey, Object> loginMap = Maps.newHashMap();
            loginMap.put(new MultiKey(new Object[]{LoginInfoDto.class}),loginInfoDto);
            loginMap.put(new MultiKey(new Object[]{LOGIN_INFO}),loginInfoDto);
            loginMap.put(new MultiKey(new Object[]{LOGIN_INFO,LoginInfoDto.class}),loginInfoDto);

            //设置线程attributes
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            RequestContextHolder.setRequestAttributes(attributes, true);

            //resuest为空情况下补偿header
            HttpServletRequest request = attributes.getRequest();
            Enumeration<String> headerNames = request.getHeaderNames();
            Map<String,String> header = Maps.newHashMap();
            while (headerNames.hasMoreElements()) {
                String key = headerNames.nextElement();
                header.put(key, request.getHeader(key));
            }


            return () -> {
                //传递登录信息
                try {
                    CustomerAnnotationConfigServletWebServerApplicationContext.set(loginMap);
                    HeaderUtil.set(header);
                    runnable.run();
                } finally {
                    //执行结束移除登录信息
                    CustomerAnnotationConfigServletWebServerApplicationContext.remove();
                    HeaderUtil.remove();
                }
            };
        } catch (Exception e) {
            return runnable;
        }
    }
}
