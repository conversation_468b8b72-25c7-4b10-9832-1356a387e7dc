package com.yonyou.dmscus.customer.configuration;

import com.yonyou.dmscus.customer.util.HeaderUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class TokenInterceptor implements ClientHttpRequestInterceptor {

    public static final String AUTHORIZATION = "Authorization";
    private Logger logger = LoggerFactory.getLogger(TokenInterceptor.class);

    @Autowired
    private HttpServletRequest httpServletRequest;

    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution)
            throws IOException {
        logger.info("----------------------------进入RestTemplate拦截器------------------------------");
        Optional.ofNullable(RequestContextHolder.getRequestAttributes()).ifPresent(e -> {
            logger.info("当前请求对象存在");
            HttpHeaders headers = request.getHeaders();
            List<String> authorizations = headers.get(AUTHORIZATION);
            logger.info("TokenInterceptor1 Authorization:{}",httpServletRequest.getHeader(AUTHORIZATION));
            if(CollectionUtils.isEmpty(authorizations)){
                headers.add(AUTHORIZATION, httpServletRequest.getHeader(AUTHORIZATION));
                logger.info("TokenInterceptor1 Authorization:{}",httpServletRequest.getHeader(AUTHORIZATION));
            }
        });
        return execution.execute(request, body);
    }
}
