package com.yonyou.dmscus.customer.configuration;

import feign.RequestInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

@Configuration
public class MidFeignConfig {

    private static final Logger logger = LoggerFactory.getLogger(MidFeignConfig.class);

    @Value("${dynamic.datasouce.tenantHeader}")
    private String tenant;

    public MidFeignConfig() {
    }

    @Bean
    public RequestInterceptor midRequestInterceptor() {
        return (requestTemplate) -> {
            ServletRequestAttributes attrs = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
            if (attrs != null) {
                HttpServletRequest request = attrs.getRequest();
                Enumeration<String> headerNames = request.getHeaderNames();
                if (headerNames != null) {
                    while(headerNames.hasMoreElements()) {
                        String name = headerNames.nextElement();
                        String value = request.getHeader(name);
                        logger.info("Feign request: {},value: {}", name, value);
                    }
                    String userId = request.getHeader("userId");
                    requestTemplate.header("userId", new String[]{userId});
                    String tenant1 = request.getHeader(this.tenant);
                    requestTemplate.header(this.tenant, new String[]{tenant1});
                    String authorization = request.getHeader("Authorization");
                    requestTemplate.header("Authorization", new String[]{authorization});
                    logger.info("Feign userId{},tenant1:{},authorization:{}", userId, tenant1, authorization);
                } else {
                    logger.error("MidFeignConfig获取请求头失败！");
                }
            }

        };
    }
}