
/** 
*Copyright 2020 Yonyou Corporation Ltd. All Rights Reserved.
* This software is published under the terms of the Yonyou Software
* License version 1.0, a copy of which has been included with this
* distribution in the LICENSE.txt file.
*
* @Project Name : dmscus.customer
*
* @File name : ExecutorConfig.java
*
* <AUTHOR> caizhonming
*
* @Date : 2020年7月11日
*
*/
	
package com.yonyou.dmscus.customer.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
* 线程池的配置
* <AUTHOR>
* @date 2020年7月11日
*/
@Configuration
public class ExecutorConfig {

    private static int CORE_POOL_SIZE = 10;
    private static int MAX_POOL_SIZE = 50;
    
    @Bean(name="taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor(){
         ThreadPoolTaskExecutor poolTaskExecutor = new  ThreadPoolTaskExecutor();  
         //线程池维护线程的最少数量  
         poolTaskExecutor.setCorePoolSize(CORE_POOL_SIZE);  
         //线程池维护线程的最大数量  
         poolTaskExecutor.setMaxPoolSize(MAX_POOL_SIZE);  
         //线程池所使用的缓冲队列  
         poolTaskExecutor.setQueueCapacity(200);  
         //线程池维护线程所允许的空闲时间  
         poolTaskExecutor.setKeepAliveSeconds(300);  
         poolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);  
         return poolTaskExecutor;  
    }
}
