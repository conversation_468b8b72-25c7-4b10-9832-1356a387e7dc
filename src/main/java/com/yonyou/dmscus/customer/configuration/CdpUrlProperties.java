package com.yonyou.dmscus.customer.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "cdp")
@Data
public class CdpUrlProperties {

    /**
     * 服务域名路径
     */
    private String domainUrl;

    /**
     * appid 应用id
     */
    private String appid;

    /**
     * limit 写入分页数量
     */
    private Integer writesLimit;

    /**
     * limit 写入分页数量
     */
    private Integer disposeLimit;

    /**
     * app_secret 秘钥
     */
    private String appSecret;

    /**
     * dailyMile 日均里程标签
     */
    private String dailyMile;

    /**
     * dailyMile 进厂里程标签
     */
    private String adviseInDate;

    /**
     * 车辆售后返厂概率评级标签
     */
    private String returnIntentionLevel;

    /**
     * CDP接口设置超时时间
     */
    private Integer timeout = 600000;


    /**
     * 获取token路径
     */
    private String tokenUrl = "/attr-tag/api/getAppTokenId";

    /**
     * VINS批量查询车辆标签
     */
    private String vehicleLabelsUrl = "/attr-tag/api/getVehicleLabels";


}
