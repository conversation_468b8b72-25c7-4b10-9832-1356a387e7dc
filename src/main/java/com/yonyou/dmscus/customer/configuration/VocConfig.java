package com.yonyou.dmscus.customer.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "voc")
@Data
public class VocConfig {

    /**
     * clientId
     */
    private String clientId;

    /**
     * clientSecret
     */
    private String clientSecret;

    /**
     * 域名地址
     */
    private String baseUrl;

    /**
     * VOCH5URL
     */
    private String scorePageUrl;
}
