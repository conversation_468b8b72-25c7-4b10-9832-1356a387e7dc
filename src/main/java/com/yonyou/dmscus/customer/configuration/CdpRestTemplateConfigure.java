package com.yonyou.dmscus.customer.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
@Configuration
public class CdpRestTemplateConfigure {

    @Autowired
    private CdpUrlProperties cdpUrlProperties;

    @Bean
    public HttpComponentsClientHttpRequestFactory cdpHttpRequestFactory() {
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(cdpUrlProperties.getTimeout());
        httpRequestFactory.setConnectTimeout(cdpUrlProperties.getTimeout());
        httpRequestFactory.setReadTimeout(cdpUrlProperties.getTimeout());
        return httpRequestFactory;
    }

    @Bean(name = "cdpRestTemplate")
    public RestTemplate cdpRestTemplate() {
        RestTemplate restTemplate = new RestTemplate(cdpHttpRequestFactory());
        //设置restemplate编码为utf-8  index – index of the element to replace
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        return restTemplate;
    }
}
