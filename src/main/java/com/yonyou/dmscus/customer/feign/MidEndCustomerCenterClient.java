package com.yonyou.dmscus.customer.feign;


import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.dto.VehicleOwnerDetailByIdListDTO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.dto.VehicleOwnerSelectByIdListDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


@FeignClient(value = "mid-end-customer-center", url = "${access.mid.url.midEndCustomerCenter}")
public interface MidEndCustomerCenterClient {

    @PostMapping(
            path = {"/customer/vehicle-owner/selectVehicleOwnerByOneIdList"}
    )
    ResponseDTO<List<VehicleOwnerDetailByIdListDTO>> selectVehicleOwnerByOneIdList(@RequestBody VehicleOwnerSelectByIdListDTO var1);


}