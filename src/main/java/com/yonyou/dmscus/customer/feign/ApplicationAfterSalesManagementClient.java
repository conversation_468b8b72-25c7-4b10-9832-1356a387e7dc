package com.yonyou.dmscus.customer.feign;

import com.yonyou.dmscloud.framework.FeignConfig;
import com.yonyou.dmscus.customer.dto.EM90MessageRemindDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Objects;

@FeignClient(value = "application-aftersales-management", configuration = FeignConfig.class)
public interface ApplicationAfterSalesManagementClient {

    /**
     * 厂端自建/零附件线索/保养线索提醒推送
     */
    @PostMapping(value = "/EM90/clueReminder/notToken")
    public Objects clueReminder(@RequestBody EM90MessageRemindDto dto);
}
