package com.yonyou.dmscus.customer.feign;

import com.yonyou.dmscloud.framework.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.Map;


/**
 * 协同报表库 feign 调用接口
 *
 * @date 2020-11-03
 */
@FeignClient(value = "cti-service",url = "${access.sales.url.ctiService}",configuration = FeignConfig.class)
public interface TechServiceClient {


    /**
     * 查询电信Token的Feign调用
     * @param
     * @return
     */
    @GetMapping("/phone/registerGetToken")
    Map<String, Object> registerGetToken();

}
