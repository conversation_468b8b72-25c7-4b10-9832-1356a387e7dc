package com.yonyou.dmscus.customer.feign;

import com.yonyou.dmscloud.framework.FeignConfig;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "dmscus-report", configuration = FeignConfig.class, contextId = "vehicle-info")
public interface VehicleInfoClient {

    @GetMapping( "/tm-vehicle/getVehicleVOByVin" )
    VehicleOwnerVO getVehicleVOByVin(@RequestParam String vin );

    @PostMapping( "/tm-vehicle/getVehicleVOListByVinList" )
    List<VehicleOwnerVO> getVehicleVOListByVinList( @RequestBody List<String> vinList );

}
