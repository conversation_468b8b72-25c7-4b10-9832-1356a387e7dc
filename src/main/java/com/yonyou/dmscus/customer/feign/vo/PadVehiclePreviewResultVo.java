package com.yonyou.dmscus.customer.feign.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @ClassName PadVehiclePreviewResultVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/5/18 14:26
 */
@Data
public class PadVehiclePreviewResultVo {

    @ApiModelProperty(value = "车主唯一标识号(车架号)",name = "vin")
    private String vin;

    @ApiModelProperty(value = "车牌号" , name = "license")
    private String license;

    @ApiModelProperty(value = "车主手机" , name = "mobile")
    private String mobile;

    @ApiModelProperty(value = "车主姓名" , name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "车型" , name = "model")
    private String model;

    @ApiModelProperty(value = "送车人姓名" , name = "deliverer")
    private String deliverer;

    @ApiModelProperty(value = "送车人手机" , name = "delivererMobile")
    private String delivererMobile;

    @ApiModelProperty(value = "预约单号" , name = "bookingOrderNo")
    private String bookingOrderNo;

    @ApiModelProperty(value = "预约来源" , name = "bookingSource")
    private String bookingSource;

    @ApiModelProperty(value = "车型名称" , name = "modelName")
    private String modelName;

    @ApiModelProperty(value = "车型code" , name = "modelCode")
    private String modelCode;

    @ApiModelProperty(value = "车主id" , name = "oneId")
    private Long oneId;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entryTime;

    @ApiModelProperty(value = "进厂时间类型  1:摄像头  2:人工录入",name = "entryTimeType")
    private String entryTimeType;
}
