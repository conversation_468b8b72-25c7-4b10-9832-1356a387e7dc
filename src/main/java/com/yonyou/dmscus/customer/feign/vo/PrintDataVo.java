package com.yonyou.dmscus.customer.feign.vo;


import com.yonyou.dmscus.customer.entity.dto.businessPlatform.CompanyDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("通用打印数据Vo")
@Data
public class PrintDataVo {

    // 经销商信息
    @ApiModelProperty(value = "经销商基本信息", name = "companyInfo")
    private CompanyDetailDTO companyInfo;

    // 传入参数
    @ApiModelProperty(value = "打印单-打印单地区", name = "printRegion")
    private String printRegion;


    // 车辆信息
    @ApiModelProperty(value = "车主姓名", name = "ownerName")
    private String ownerName;
    @ApiModelProperty(value = "车主手机号", name = "ownerMobile")
    private String ownerMobile;
    @ApiModelProperty(value = "车主性别", name = "gender")
    private String gender;
    @ApiModelProperty(value = "车牌号", name = "license")
    private String license;
    @ApiModelProperty(value = "车型", name = "model")
    private String model;
    @ApiModelProperty(value = "车辆识别号", name = "vin")
    private String vin;
    @ApiModelProperty(value = "发动机号", name = "engineNo")
    private String engineNo;
    @ApiModelProperty(value = "保修起始日期", name = "wrtBeginDate")
    private String wrtBeginDate;
    @ApiModelProperty(value = "进厂里程", name = "inMileage")
    private String inMileage;
    @ApiModelProperty(value = "送修人", name = "deliverer")
    private String deliverer;
    @ApiModelProperty(value = "送修人手机", name = "delivererMobile")
    private String delivererMobile;
    @ApiModelProperty(value = "完工时间", name = "completeTime")
    private String completeTime;
    @ApiModelProperty(value = "完工里程", name = "itmeEndMileage")
    private String itmeEndMileage;
    @ApiModelProperty(value = "行驶里程", name = "mileage")
    private String mileage;


    @ApiModelProperty(value = "收费对象", name = "payObj")
    private String payObj;

    // 费用相关
    @ApiModelProperty(value = "工时费用", name = "labourAmount")
    private String labourAmount;
    @ApiModelProperty(value = "工时合计", name = "labourTotal")
    private String labourTotal;
    @ApiModelProperty(value = "工时费用-折扣金额", name = "discountForLabourAmount")
    private String discountForLabourAmount;
    @ApiModelProperty(value = "工时费用-折扣后金额", name = "labourAmountAfterDiscount")
    private String labourAmountAfterDiscount;
    @ApiModelProperty(value = "零件费用", name = "partAmount")
    private String partAmount;
    @ApiModelProperty(value = "零件费用-折扣金额", name = "discountForPartAmount")
    private String discountForPartAmount;
    @ApiModelProperty(value = "零件费用-折扣后金额", name = "partAmountAfterDiscount")
    private String partAmountAfterDiscount;
    @ApiModelProperty(value = "附加费用", name = "addItemAmount")
    private String addItemAmount;
    @ApiModelProperty(value = "附加费用-折扣金额", name = "addItemAmount")
    private String discountForAddItemAmount;
    @ApiModelProperty(value = "附加项目-折扣后金额", name = "addItemAmountAfterDiscount")
    private String addItemAmountAfterDiscount;
    @ApiModelProperty(value = "总计费用（工时+配件+附加项目）-折扣", name = "amountForDiscount")
    private String amountForDiscount;
    @ApiModelProperty(value = "总计费用", name = "amount")
    private String amount;
    @ApiModelProperty(value = "折扣金额", name = "discountAmount")
    private String discountAmount;
    @ApiModelProperty(value = "优惠金额", name = "preferentialAmount")
    private String preferentialAmount;
    @ApiModelProperty(value = "收费对象-去零金额", name = "paySub")
    private String paySub;
    @ApiModelProperty(value = "收费对象-圆整金额", name = "payYz")
    private String payYz;
    @ApiModelProperty(value = "实际金额", name = "receiveAmount")
    private String receiveAmount;

    // 工单信息
    @ApiModelProperty(value = "工单号", name = "roNo")
    private String roNo;
    @ApiModelProperty(value = "工单创建时间", name = "roCreateDate")
    private String roCreateDate;
    @ApiModelProperty(value = "工单打印时间", name = "roPrintTime")
    private String roPrintTime;
    @ApiModelProperty(value = "工单备注", name = "roRemark")
    private String roRemark;
    @ApiModelProperty(value = "工单开工时间", name = "itemStartTime")
    private String itemStartTime;
    @ApiModelProperty(value = "维修类型", name = "repairType")
    private String repairType;
    @ApiModelProperty(value = "估价单号", name = "estimateNo")
    private String estimateNo;
    @ApiModelProperty(value = "估价单时间", name = "estimateTime")
    private String estimateTime;
    @ApiModelProperty(value = "预约单号",name = "bookingOrderNo")
    private String bookingOrderNo;
    @ApiModelProperty(value = "预约时间",name = "bookingTime")
    private String bookingTime;
    @ApiModelProperty(value = "预交车时间", name = "endTimeSupposed")
    private String endTimeSupposed;
    @ApiModelProperty(value = "销售日期", name = "salesDate")
    private String salesDate;
    @ApiModelProperty(value = "服务顾问", name = "serviceAdvisor")
    private String serviceAdvisor;
    @ApiModelProperty(value = "客户描述", name = "customerDesc")
    private String customerDesc;
    @ApiModelProperty(value = "是否延保工单", name = "isExtendInsurance")
    private String isExtendInsurance;
    @ApiModelProperty(value = "是否服务合同购买工单", name = "isServeContractBuyOrder")
    private String isServeContractBuyOrder;
    @ApiModelProperty(value = "是否保养活动工单", name = "isUpkeepActivityOrder")
    private String isUpkeepActivityOrder;
    @ApiModelProperty(value = "注意事项勾选框", name = "noticeCheck")
    private String noticeCheck;
    @ApiModelProperty(value = "是否要洗车", name = "isCanWash")
    private String isCanWash;


    // 结算单-结算通用相关字段
    @ApiModelProperty(value = "结算单号", name = "balanceNo")
    private String balanceNo;
    @ApiModelProperty(value = "结算员", name = "balanceHandler")
    private String balanceHandler;
    @ApiModelProperty(value = "本次未做项目", name = "notDoneProjectThisTime")
    private String notDoneProjectThisTime;
    @ApiModelProperty(value = "结算备注", name = "balanceRemark")
    private String balanceRemark;
    @ApiModelProperty(value = "结算时间", name = "balanceTime")
    private String balanceTime;
    @ApiModelProperty(value = "结算单打印时间", name = "balancePrintTime")
    private String balancePrintTime;
    @ApiModelProperty(value = "支付方式", name = "payType")
    private String payType;

    // 结算单-延保服务相关字段
    @ApiModelProperty(value = "下次保养日期", name = "nextMaintainDate")
    private String nextMaintainDate;
    @ApiModelProperty(value = "下次保养里程", name = "nextMaintainMileage")
    private String nextMaintainMileage;
    @ApiModelProperty(value = "延保类型(出险无忧和钥匙置换)", name = "extendType")
    private String extendType;
    @ApiModelProperty(value = "延保类型(大类)", name = "extendInsuranceBigType")
    private String extendInsuranceBigType;
    @ApiModelProperty(value = "延保类型(小类)", name = "extendInsuranceSmallType")
    private String extendInsuranceSmallType;
    @ApiModelProperty(value = "延保时长(月)", name = "延保时长(月)")
    private String extendInsuranceDuration;
    @ApiModelProperty(value = "延保服务范围", name = "延保服务范围")
    private String extendInsuranceRange;
    @ApiModelProperty(value = "延保服务名称", name = "延保服务名称")
    private String extendInsuranceName;
    @ApiModelProperty(value = "延保服务开始时间", name = "延保服务开始时间")
    private String extendInsuranceStartDate;
    @ApiModelProperty(value = "延保服务结束时间", name = "延保服务结束时间")
    private String extendInsuranceEndDate;

    // 结算单-使用服务合同相关字段
    @ApiModelProperty(value = "使用服务合同次数", name = "使用服务合同次数")
    private String useCountText;

    // 结算单-服务合同购买相关字段
    @ApiModelProperty(value = "保养类型", name = "保养类型")
    private String maintenanceType;
    @ApiModelProperty(value = "保养合同名称", name = "保养合同名称")
    private String maintenanceContract;

    // 预打印专用
    @ApiModelProperty(value = "打印单-打印类型(RO:工单 BO:结算单 YO:预约单 EO:估价单 EI:延保服务合同 US:使用服务合同打印 BS:服务合同购买 PBO:预打印)", name = "printType")
    private String printType;

    // 预检单专用
    @ApiModelProperty(value = "技师", name = "technician")
    private String technician;
    @ApiModelProperty(value = "进厂时间", name = "inTime")
    private String inTime;

    // 质检报告专用
    @ApiModelProperty(value = "质检单单号", name = "qcRoNo")
    private String qcRoNo;
    @ApiModelProperty(value = "质检单车主姓名", name = "qcOwnerName")
    private String qcOwnerName;
    @ApiModelProperty(value = "质检单车主手机", name = "qcOwnerMobile")
    private String qcOwnerMobile;
    @ApiModelProperty(value = "质检单车牌号", name = "qcLicense")
    private String qcLicense;
    @ApiModelProperty(value = "质检单vin", name = "qcVin")
    private String qcVin;
    @ApiModelProperty(value = "是否包含溯源件", name = "qcVin")
    private String isContainSource;
    @ApiModelProperty(value = "车辆症状",name = "vehicleSymptom")
    private String vehicleSymptom;

}
