package com.yonyou.dmscus.customer.feign;

import java.util.List;
import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.yonyou.dmscloud.framework.FeignConfig;

/**
 * 售后产品-customerRepair feign 调用接口
 * <AUTHOR>
 * @date 2020-06-16
 */
@FeignClient(value = "${dmscloud.customerRepair.feign.name}",configuration = FeignConfig.class)
public interface CustomerRepairCommonClient {

    /**
     * 查询 保险公司
     * @date 20200819
     * <AUTHOR>
     * @param vo
     * @return
     */
    @GetMapping("/basedata/insuranceCo/insurance/dicts")
    public List<Map> getInsuranceCompany(@RequestParam(value = "isValid") Integer isValid);




}
