package com.yonyou.dmscus.customer.feign;

import com.yonyou.dmscloud.framework.FeignConfig;
import com.yonyou.dmscus.customer.feign.dto.CommonConfigDTO;
import com.yonyou.dmscus.customer.feign.vo.PadVehiclePreviewResultVo;
import com.yonyou.dmscus.customer.feign.vo.PadVehiclePreviewVo;
import io.swagger.annotations.ApiOperation;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "${dmscloud.repair.feign.name}", configuration = FeignConfig.class, contextId = "dmscloud-service")
public interface DmscloudServiceClient {

    /**
     * 获取公共配置参数
     *
     */
    @GetMapping("/common/config")
    CommonConfigDTO getCommonConfig(@RequestParam(value = "configKey") String configKey);

    //根据vin或license查询车主信息
    @PostMapping(value = "/padVehiclePreviewApi/queryOwnerVehicle")
    PadVehiclePreviewResultVo queryOwnerVehicleInterf(@RequestBody PadVehiclePreviewVo padVehiclePreviewVo);
    
    @ApiOperation(value = "根据分组和KEY获取配置信息", notes = "根据分组和KEY获取配置信息")
    @GetMapping(value = "/common/config")
    CommonConfigDTO getConfigByKey(@RequestParam(value = "configKey") String configKey, @RequestParam(value = "groupType") String groupType);
}
