package com.yonyou.dmscus.customer.feign.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 胡仓 数据返回
 */
@Data
public class HcResponse<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 返回代码，TRUE表示成功，其他表示失败
     */
    private String retCode;

    /**
     * 返回描述
     */
    private String retInfo;

    /**
     * 返回数据
     */
    private T retResult;

    /**
     * 总记录数
     */
    private Integer total;

    /**
     * 总记录数
     */
    private Integer pages;

    /**
     * 总记录数
     */
    private Integer pageNum;

    /**
     * 总记录数
     */
    private Integer pageSize;

}
