package com.yonyou.dmscus.customer.feign;

import com.yonyou.dmscloud.framework.FeignConfig;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InsuranceBillDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/22 0022
 */
@FeignClient(value = "${dmscus.repair.feign.name}", configuration = FeignConfig.class)
public interface DmscusRepairClient {

    @GetMapping("/insuranceBill/selectInsuranceByVin")
    public InsuranceBillDTO selectInsuranceByVin(@RequestParam(value = "vin") String vin, @RequestParam(value = "viInsuranceDate") Date viInsuranceDate);

}
