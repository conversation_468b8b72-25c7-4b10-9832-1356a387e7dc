package com.yonyou.dmscus.customer.feign.vo;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 重复线索清洗专用对象-非业务功能可废弃
 */
@Data
public class InvDataCleanVO extends BaseDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 用户名称
     */
    private String name;

    /**
     * 建议进厂日期
     */
    private Date adviseInDate;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 经销商代码
     */
    private String dealerCode;

    /**
     * 邀约类型
     */
    private Integer inviteType;

    /**
     * customer_id
     */
    private Long customerId;

    /**
     * AI得分
     */
    private Integer totalScore;

    /**
     * 跟进状态
     */
    private Integer followStatus;
}
