package com.yonyou.dmscus.customer.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domains.dto.basedata.ResponseDTO;
import com.yonyou.dmscus.customer.feign.dto.VehicleDto;
import com.yonyou.dmscus.customer.feign.dto.VehiclePageQueryDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "mid-end-vehicle-center",url = "${access.mid.url.midEndVehicleCenter}")
public interface MidEndVehicleCenterFeign {

    //车辆相关信息(查询ByVIN)
    @GetMapping(path = "/vehicle/getVehicleByVin/{vin}", consumes = "application/json", produces = "application/json")
    ResponseDTO<VehicleDto> queryVehicleByVIN(@PathVariable("vin") String vin);

    /**
     * 车辆信息查询
     */
    @PostMapping("/vehicle/listOwnerVehiclePage")
    ResponseDTO<Page<VehicleDto>> queryVehiclePage(@RequestBody VehiclePageQueryDto query);
}
