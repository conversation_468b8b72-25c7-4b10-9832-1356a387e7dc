package com.yonyou.dmscus.customer.feign.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@Accessors(chain = true)
@ApiModel("维修工单信息")
public class FaultRepairOrderDto implements Serializable {
    @ApiModelProperty("经销商code")
    private String ownerCode;

    @ApiModelProperty("工单号")
    private String roNo;

    @ApiModelProperty("车架号")
    private String vin;

    @ApiModelProperty("维修类型（'G','保修'、'I','事故'、'M','保养'、'N','机电维修'、'P','PDS'、'S','零售'")
    private String repairTypeCode;

    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;
}
