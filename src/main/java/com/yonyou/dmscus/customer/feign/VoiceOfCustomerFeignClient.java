package com.yonyou.dmscus.customer.feign;


import com.yonyou.dmscus.customer.dto.voc.ObtainTokenParamDTO;
import com.yonyou.dmscus.customer.dto.voc.ObtainTokenResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(url = "${voc.baseUrl}", name = "VoiceOfCustomer")
public interface VoiceOfCustomerFeignClient {

    @PostMapping(value = {"/qc/Interfaces/GetClientToken"}, produces = MediaType.APPLICATION_JSON_VALUE)
    ObtainTokenResult getClientToke(@RequestBody ObtainTokenParamDTO requestParamDTO);

}
