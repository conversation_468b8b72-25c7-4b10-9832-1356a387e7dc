package com.yonyou.dmscus.customer.feign.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("故障信息统计")
public class WarningDto {
	private String warning_type;
	private List<vinListInfo> vinList;
	private int searchType;
	private Integer pageNum;
	private Integer pageSize;


	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	public static class vinListInfo {

		/**
		 * 车辆VIN码
		 */
		private String vehicle_vin;

		/**
		 * 开始时间，故障触发时间，格式：yyyy-MM-dd HH:mm:ss
		 */
		private String start_time;

		/**
		 * 结束时间，故障触发时间，格式：yyyy-MM-dd HH:mm:ss
		 */
		private String end_time;

		/**
		 * 	故障ID（如果有多个故障ID，可以使用逗号分隔）
		 */
		private String warning_id;

		public vinListInfo(String vehicleVin, String startTime, String endTime) {
			this.vehicle_vin = vehicleVin;
			this.start_time = startTime;
			this.end_time = endTime;
		}
	}

}
