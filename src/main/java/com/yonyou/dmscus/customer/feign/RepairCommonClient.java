package com.yonyou.dmscus.customer.feign;

import com.yonyou.dmscloud.framework.FeignConfig;
import com.yonyou.dmscloud.framework.domain.framework.RestResultResponse;
import com.yonyou.dmscus.customer.dto.BookingCreateParamsVo;
import com.yonyou.dmscus.customer.dto.BookingOrderInfoVO;
import com.yonyou.dmscus.customer.dto.RepairOrderVO;
import com.yonyou.dmscus.customer.dto.VehicleInviteVO;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.entity.VehicleQueryParamsDTO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.BookingOrderParamsVo;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.BookingOrderReturnVo;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.VehicleBasicInfoVo;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightCluePO;
import com.yonyou.dmscus.customer.feign.vo.ClueParamVO;
import com.yonyou.dmscus.customer.feign.vo.OwnerInfoResultsVo;
import com.yonyou.dmscus.customer.feign.vo.PrintDataVo;
import com.yonyou.dmscus.customer.feign.vo.PrintParamVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;


/**
 * 售后产品-维修模块 feign 调用接口
 *
 * <AUTHOR>
 * @date 2020-10-07
 */
@FeignClient(value = "${dmscloud.repair.feign.name}", configuration = FeignConfig.class)
public interface RepairCommonClient {

    @PostMapping("/customerCheckInterfaceApi/getAppointmentOperation/customerInterAspect")
    String saveBookingRecord(BookingCreateParamsVo vo);

    @GetMapping("/faultLightApi/queryRoNoSpinner")
    List<String> queryRoNoSpinner(@RequestParam(value = "ownerCode", required = false) String ownerCode,
                                  @RequestParam("vin") String vin,
                                  @RequestParam(value = "startTime", required = false)
                                  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
                                  Date startTime,
                                  @RequestParam(value = "endTime", required = false)
                                  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
                                  Date endTime);

    @GetMapping("/faultLightApi/queryRepairOrder")
    List<VehicleOwnerVO> queryRepairOrder(@RequestParam(value = "ownerCode")String ownerCode,
                                    @RequestParam(value = "vin")String vin,
                                    @RequestParam(value = "roNo")String roNo);


    /**
     * 根据预约单号和经销商code查询工单号
     */
    @GetMapping("/faultLightApi/queryByBookingOrderNoAndDealerCode")
    BookingOrderInfoVO queryByBookingOrderNoAndDealerCode(@RequestParam(value = "bookingOrderNo") String bookingOrderNo,
                                                          @RequestParam("dealerCode") String dealerCode);

    /**
     * 查询预约单状态
     *
     * @param bookingOrderNo 预约单号
     * @param dealerCode     经销商代码
     * @return 预约单状态
     */
    @GetMapping("/faultLightApi/queryBookingOrderStatus")
    Integer queryBookingOrderStatus(@RequestParam(value = "bookingOrderNo") String bookingOrderNo,
                                    @RequestParam("dealerCode") String dealerCode);

    /**
     * 查询预约单时间
     *
     * @return 预约单时间
     */
    @PostMapping("/faultLightApi/queryBookingOrder")
    List<BookingOrderInfoVO> queryBookingOrder(@RequestBody List<BookingOrderInfoVO> bookingOrderInfoVO);

    /**
     * 工单金额查询
     */
    @PostMapping("/printBalanceRo/balancePrintDataInner")
    PrintDataVo balancePrintData(@RequestBody PrintParamVo paramsVo);

    /**
     * 查询保养工单
     */
    @PostMapping("/faultLightApi/queryMaiRepairOrder")
    List<RepairOrderVO> queryMaiRepairOrder(@RequestBody ClueParamVO paramsVo);

    /**
     * 通过时间指定查询已经结算的保养工单
     */
    @PostMapping("/faultLightApi/settled")
    List<RepairOrderVO> getSettledMaintenanceOrdersByTime(@RequestBody ClueParamVO vo);

    /**
     * 通过经销商,车架号查询出所有非DPS,非零售且已结算的工单
     */
    @PostMapping("/faultLightApi/settledWorkOrders")
    List<RepairOrderVO> getSettledWorkOrdersByDealerAndVIN(@RequestBody ClueParamVO vo);

    /**
     * 获取车主信息
     */
    @ApiOperation(value = "获取车主信息",notes = "获取车主信息")
    @GetMapping("/collaborativeInterfaceApi/getOwnerInfo")
    OwnerInfoResultsVo getOwnerInfo(@RequestParam(value = "ownerCode")String ownerCode,
                                    @RequestParam(value = "vin")String vin);

    @GetMapping("/partClueApi/queryRepairOrder")
    RepairOrderVO queryRepairOrder(@RequestParam(value = "ownerCode") String ownerCode,
                                          @RequestParam(value = "roNo") String roNo);

    /**
     * 查询工单返回时间最近的经销商
     */
    @GetMapping("/faultLightApi/workOrderTimeChecker")
    String workOrderTimeChecker(@RequestParam("dealerCode")List<String> dealerCode,
                                       @RequestParam("vin") String vin);


    /**
     * 预约登记保存接口
     * @param paramsVo
     * @return
     */
    @PostMapping(value = "/BookingRegister/saveAppointmentOrder")
    RestResultResponse<BookingOrderReturnVo> saveBookingPart(@RequestBody BookingOrderParamsVo paramsVo);

    /**
     * 通过vin或车牌号 查询 车辆信息查询
     * @param dto
     * @return
     */
    @PostMapping("/customerCare/ownerVehicleManagement/queryVehicleBasicInfoList")
    List<VehicleBasicInfoVo> queryVehicleBasicInfoList(@RequestBody VehicleQueryParamsDTO dto);

    @GetMapping("/customerCheckInterfaceApi/queryBookingNo")
    String queryBookingNo(@RequestParam("ownerCode")String ownerCode,
            @RequestParam("vin") String vin);

    /**
     * 查询工单返回时间最近的经销商
     */
    @PostMapping("/faultLightApi/repairOrderChecker")
    List<TtFaultLightCluePO> repairOrderChecker(@RequestBody List<TtFaultLightCluePO> faultLightClueChange);

    @GetMapping("/order/repair/vehicleowner/getVehicleInvite")
     VehicleInviteVO getVehicleInvite(@RequestParam(value = "vin") String vin);

    /**
     * 查询工单返回时间最近的经销商
     */
    @PostMapping("/faultLightApi/queryByBookingOrderNoAndRoNo")
    List<BookingOrderInfoVO> queryByBookingOrderNoAndRoNo(@RequestBody List<BookingOrderInfoVO> faultLightCluePOs);
}
