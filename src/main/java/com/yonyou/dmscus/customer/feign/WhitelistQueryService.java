package com.yonyou.dmscus.customer.feign;

import com.yonyou.dmscloud.framework.FeignConfig;
import com.yonyou.dmscloud.framework.domain.framework.RestResultResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = "${dmscloud.repair.feign.name}",configuration = FeignConfig.class)
public interface WhitelistQueryService {
    /**
     * 白名单查询
     */
    @GetMapping("/whiteList/customerInterAspect")
    @Cacheable(cacheNames = "whitelistCache", key = "#modType+'-'+#rosterType")
    public List<String> selectWhiteListString(
            @RequestParam(value = "modType") Integer modType,
            @RequestParam(value = "rosterType") Integer rosterType);

    /**
     * 白名单查询
     */
    @GetMapping("/vehicleHealthCheck/selectWhitelistByOwnerCode/customerInterAspect")
    public boolean selectWhitelistByOwnerCode(@RequestParam(value = "ownerCode") String ownerCode,
                                              @RequestParam(value = "modType") Integer modType,
                                              @RequestParam(value = "rosterType") Integer rosterType);

    /**
     * 添加白名单
     */
    @RequestMapping(value = "/vehicleHealthCheck/whitelist", method = RequestMethod.POST)
    void addWhiteList(@RequestParam String ownerCodes,
                      @RequestParam Integer modType,
                      @RequestParam Integer rosterType);

    /**
     * 删除缓存
     */
    @RequestMapping(value = "/redisManage/deleteRedisDataByKey", method = RequestMethod.GET)
    String deleteRedisDataByKey(@RequestParam(value = "key")  String key);

    @ApiOperation(value = "校验白名单", notes = "校验白名单")
    @GetMapping(value = "/whitelist/checkWhitelist")
    Boolean checkWhitelist(@RequestParam(value = "ownerCode") String ownerCode, @RequestParam(value = "modType") Integer modType, @RequestParam(value = "rosterType") Integer rosterType, @RequestParam(value = "vin") String vin);

}
