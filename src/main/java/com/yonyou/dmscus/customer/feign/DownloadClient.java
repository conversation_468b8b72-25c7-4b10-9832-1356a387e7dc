package com.yonyou.dmscus.customer.feign;

import com.yonyou.cloud.common.beans.RestResultResponse;
import com.yonyou.dmscloud.framework.FeignHttpsConfig;
import com.yonyou.dmscus.customer.dto.DownloadDTO;
import com.yonyou.dmscus.customer.vo.DownloadCacheVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "download-service", configuration = {FeignHttpsConfig.class})
public interface DownloadClient {

    /**
     * feign调用
     *
     * @param downloadDTO downloadDTO
     * @return com.yonyou.cyx.framework.bean.dto.framework.RestResultResponse<java.lang.Void>
     * <AUTHOR>
     * @since 2020/12/18
     */
    @PostMapping(value = "/download/exportExcel")
    RestResultResponse<DownloadCacheVO> downloadConGoodwillStamps(@RequestBody DownloadDTO downloadDTO);
}
