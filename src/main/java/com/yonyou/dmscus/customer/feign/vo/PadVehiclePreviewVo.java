package com.yonyou.dmscus.customer.feign.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @ClassName PadVehiclePreviewVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/5/18 14:20
 */
@Data
@Accessors(chain = true)
@ApiModel("pad查询车主信息VO")
public class PadVehiclePreviewVo {

    @ApiModelProperty(value = "车主唯一标识号(车架号)",name = "vin")
    private String vin;

    @ApiModelProperty(value = "车牌号" , name = "license")
    private String license;

    @ApiModelProperty(value = "预约单号" , name = "bookingNo")
    private String bookingNo;

    @ApiModelProperty(value = "经销商" , name = "ownerCode")
    private String ownerCode;
}
