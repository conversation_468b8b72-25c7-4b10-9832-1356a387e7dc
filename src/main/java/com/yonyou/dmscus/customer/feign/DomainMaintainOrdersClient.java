package com.yonyou.dmscus.customer.feign;

import com.yonyou.cloud.common.beans.RestResultResponse;
import com.yonyou.dmscus.customer.feign.dto.FaultRepairOrderDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;

@FeignClient(value = "domain-maintain-orders")
public interface DomainMaintainOrdersClient {

    @GetMapping("/faultLightApi/v1/queryRoNoSpinner")
    RestResultResponse<FaultRepairOrderDto> queryRoNoSpinner(@RequestParam(value = "ownerCode", required = false) String ownerCode,
                                                             @RequestParam("vin") String vin,
                                                             @RequestParam(value = "startTime", required = false)
                                    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                                    Date startTime,
                                                             @RequestParam(value = "endTime", required = false)
                                    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                                    Date endTime);

    @PostMapping("/faultLightApi/v1/queryVoidedOrder")
    RestResultResponse<List<FaultRepairOrderDto>> queryVoidedOrder(@RequestBody List<FaultRepairOrderDto> list);
}
