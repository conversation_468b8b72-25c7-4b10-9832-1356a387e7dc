package com.yonyou.dmscus.customer.feign;

import com.yonyou.dmscloud.framework.FeignConfig;
import com.yonyou.dmscloud.framework.domain.framework.RestResultResponse;
import com.yonyou.dmscus.customer.entity.vo.TmDateHolidayVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;


@FeignClient(value = "dict-service",configuration = FeignConfig.class)
public interface DictServiceClient {
    /**
     * 查询工作日
     */
    @GetMapping("/date/findWorkDay")
    RestResultResponse<List<TmDateHolidayVo>> findWorkDay();
}
