package com.yonyou.dmscus.customer.feign;

import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.feign.dto.ListBindRelationDto;
import com.yonyou.dmscus.customer.feign.vo.ListBindRelationVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "vehicle-ownership-service", url = "${access.mid.url.vehicleOwnershipService}")
public interface VehicleOwnershipClient {

    /**
     * @param listBindRelationDto
     * @return
     */
    @PostMapping("/api/vehicle/listBindRelation")
    ResponseDTO<List<ListBindRelationVo>> listBindRelation(@RequestBody ListBindRelationDto listBindRelationDto);
}
