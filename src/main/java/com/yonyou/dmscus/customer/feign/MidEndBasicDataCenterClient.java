package com.yonyou.dmscus.customer.feign;


import com.yonyou.dmscloud.framework.FeignConfig;
import com.yonyou.dmscus.customer.dto.EmpQueryDto;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.feign.dto.AllModeDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@FeignClient(value = "mid-end-basicdata-center",url = "${access.mid.url.midEndBasicdataCenter}",configuration = FeignConfig.class)
public interface MidEndBasicDataCenterClient {

    @PostMapping(path = "/basicdata/model/list")
    ResponseDTO<List<AllModeDto>> allModeList(@RequestBody EmpQueryDto dto );

}
