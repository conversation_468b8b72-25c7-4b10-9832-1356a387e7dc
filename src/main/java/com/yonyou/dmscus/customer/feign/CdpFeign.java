package com.yonyou.dmscus.customer.feign;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.dmscus.customer.feign.dto.CdpTokenDto;
import com.yonyou.dmscus.customer.feign.response.CdpResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "CDP", url = "${cdp.domainUrl}")
public interface CdpFeign {
    /**
     * 功能描述：获取cdp TOKEN
     */
    @PostMapping(path = "/attr-tag/api/getAppTokenId", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    CdpResponse<JSONObject> postForObject(@RequestBody CdpTokenDto tokenDto);

}
