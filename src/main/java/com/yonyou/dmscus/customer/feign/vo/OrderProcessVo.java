package com.yonyou.dmscus.customer.feign.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;


@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrderProcessVo {
    @NotNull(message = "经销商代码不能为空")
    private String dealerCode;

    @NotNull(message = "车架号不能为空")
    private List<String> listVin;

}
