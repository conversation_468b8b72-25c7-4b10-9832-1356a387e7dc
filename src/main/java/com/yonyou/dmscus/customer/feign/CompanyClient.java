package com.yonyou.dmscus.customer.feign;

import com.yonyou.dmscloud.framework.FeignConfig;
import com.yonyou.dmscus.customer.entity.po.remote.TmCompany;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "dmscus-report", configuration = FeignConfig.class, contextId = "company-info")
public interface CompanyClient {


    @GetMapping( "/tm-company/selectAllList" )
    List<TmCompany> selectAllList();

    @PostMapping( "/tm-company/selectListByCodeList" )
    List<TmCompany> selectListByCodeList( @RequestBody List<String> codeList );

    @GetMapping( "/tm-company/selectListByOwnedCode" )
    List<TmCompany> selectListByOwnedCode( @RequestParam String ownedCode );

    @GetMapping( "/tm-company/selectSatelliteListByOwnedCode" )
    List<TmCompany> selectSatelliteListByOwnedCode( @RequestParam String ownedCode );

}
