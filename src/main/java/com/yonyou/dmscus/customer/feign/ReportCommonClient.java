package com.yonyou.dmscus.customer.feign;

import com.yonyou.dmscloud.framework.FeignConfig;
import com.yonyou.dmscus.customer.dto.CheckRepairOrderDTO;
import com.yonyou.dmscus.customer.dto.QueryVehicleOwnerDataVo;
import com.yonyou.dmscus.customer.dto.RepairOrderVO;
import com.yonyou.dmscus.customer.dto.RepairVo;
import com.yonyou.dmscus.customer.dto.VehicleInviteVO;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.dto.VocMileageVO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.CompanyDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.VehicleOwnerInsurancePO;
import com.yonyou.dmscus.customer.feign.vo.InvDataCleanVO;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * 协同报表库 feign 调用接口
 *
 * <AUTHOR>
 * @date 2020-07-07
 */
@FeignClient(value = "dmscus-report", configuration = FeignConfig.class)
public interface ReportCommonClient {


    @GetMapping("/inviteAutoCreate/queryFirstMaintain")
    public List<VehicleOwnerVO> queryFirstMaintain(
            @RequestParam(value = "createDate") String createDate);

    @GetMapping("/inviteAutoCreate/queryRegularMaintain")
    public List<VehicleOwnerVO> queryRegularMaintain(@RequestParam(value = "createDate") String createDate,
                                                     @RequestParam(value = "days") Integer days);

    @GetMapping("/inviteAutoCreate/queryRegularMaintainWithMon")
    public List<VehicleOwnerVO> queryRegularMaintainWithMon(@RequestParam(value = "createDate") String createDate,
                                                            @RequestParam(value = "begIndex") Integer begIndex,
                                                            @RequestParam(value = "endIndex") Integer endIndex);

    @GetMapping("/inviteAutoCreate/queryRegularMaintainForAverageMileage")
    public List<VehicleOwnerVO> queryRegularMaintainForAverageMileage(
            @RequestParam(value = "createDate") String createDate,
            @RequestParam(value = "days") Integer days);

    @GetMapping("/inviteAutoCreate/checkHasRepairOrder")
    public Integer checkHasRepairOrder(@RequestParam(value = "createDate") String createDate,
                                       @RequestParam(value = "vin") String vin,
                                       @RequestParam(value = "duplicateRule") Integer duplicateRule);


    @GetMapping("/inviteAutoCreate/getMileage")
    public List<VocMileageVO> getMileage(@RequestParam(value = "createDate") String createDate, @RequestParam(value =
            "vin")
            String vin);

    @GetMapping("/inviteAutoCreate/getAllMileage")
    public List<VocMileageVO> getAllMileage(@RequestParam(value =
            "vin")
    String vin);

    @GetMapping("/inviteAutoCreate/getMaintainRepairOrderLast")
    public RepairOrderVO getMaintainRepairOrderLast(@RequestParam(value = "dealerCode") String dealerCode, @RequestParam
            (value = "roNo") String roNo);

    @GetMapping("/inviteAutoCreate/getRepairOrderLast")
    public List<RepairOrderVO> getRepairOrderLast(@RequestParam(value = "vin") String vin);

    @GetMapping("/inviteAutoCreate/queryRepairOrder")
    public RepairOrderVO queryRepairOrderByRoNo(@RequestParam(value = "dealerCode") String dealerCode, @RequestParam
            (value =
                    "roNo") String roNo, @RequestParam(value = "type") Integer type, @RequestParam(value = "code")
                                                        String code);


    @GetMapping("/inviteAutoCreate/queryVehicleForTyre")
    public List<VehicleOwnerVO> queryVehicleForTyre(@RequestParam(value = "createDate") String createDate,
                                                    @RequestParam(value = "mileageInterval") Integer mileageInterval,
                                                    @RequestParam(value = "months") Integer months);

    @GetMapping("/inviteAutoCreate/queryVehileOwnerForInsuranceTask")
    public List<VehicleOwnerVO> queryVehileOwnerForInsuranceTask(@RequestParam(value = "createDate") String createDate);


    @GetMapping("/inviteAutoCreate/queryVehileOwnerForGuaranteeTask")
    List<VehicleOwnerVO> queryVehileOwnerForGuaranteeTask(@RequestParam(value = "createDate") String createDate);

    @GetMapping("/inviteAutoCreate/queryVehForCustomerLossTask")
    public List<VehicleOwnerVO> queryVehForCustomerLossTask(@RequestParam(value = "createDate") String createDate);

    @GetMapping("/inviteAutoCreate/queryCreateTaskForQb")
    public List<VehicleOwnerVO> queryCreateTaskForQb();

    @GetMapping("/inviteAutoCreate/getRepairOrderLastOne")
    public RepairOrderVO getRepairOrderLastOne(@RequestParam(value = "vin") String vin);

    @GetMapping("/inviteAutoCreate/getVehicleInfo")
    public VehicleOwnerVO getVehicleInfo(@RequestParam(value = "vin") String vin);

    @GetMapping("/inviteAutoCreate/getAllVocVeh")
    public List<VocMileageVO> getAllVocVeh();

    @GetMapping("/inviteAutoCreate/getAllVocVehWithMon")
    public List<VocMileageVO> getAllVocVehWithMon( @RequestParam(value = "createDate",required = false) String createDate,
                                                  @RequestParam(value = "begIndex") Integer begIndex,
                                                  @RequestParam(value = "endIndex") Integer endIndex);

    @GetMapping("/inviteAutoCreate/getVocMileageLast")
    public VocMileageVO getVocMileageLast(@RequestParam(value = "vin") String vin);

    @GetMapping("/inviteAutoCreate/getVocMileageByInterval")
    public VocMileageVO getVocMileageByInterval(@RequestParam(value = "vin") String vin,
                                                @RequestParam(value = "getTime") Date getTime,
                                                @RequestParam(value = "mileageKm") Integer mileageKm);

    @GetMapping("/inviteAutoCreate/getVocMileage")
    public List<VocMileageVO> getVocMileage(@RequestParam(value = "createDate") String createDate);


    /**
     * @param vin
     * @return
     * <AUTHOR>
     * @ 根据vin判断 车辆24个月内是否有过进厂
     */
    @PostMapping("/partsAccessories/selectRepairOrderByVin")
    public CheckRepairOrderDTO checkRepairOrderByVin(@RequestParam(value = "vin") String vin);

    /**
     * @param vin
     * @return
     * <AUTHOR>
     * @ 根据vin查询 车辆24个月最近两次的进厂经销商
     */
    @PostMapping("/partsAccessories/selectRepairOrderByVin2")
    public CheckRepairOrderDTO selectRepairOrderByVin2(@RequestParam(value = "vin") String vin,
                                                             @RequestParam(value = "insuranceDate") Date insuranceDate);

    /**
     * <AUTHOR>
     * @ 根据vin集合   查询车主车辆信息
     */
    @PostMapping("/toolsStockQuery/selectVehicleOwnerInfo")
    public QueryVehicleOwnerDataVo selectVehicleOwnerInfoByList(@RequestBody List<String> vinList);

    /**
     * 邀约线索导入的时候查询不存在的VIN
     */
    @GetMapping("/vehicleowner/notVehicleByVin")
    public List<Integer> selectNotVehicleByVin(@RequestParam(value = "userId") Integer userId);

    @GetMapping("/inviteAutoCreate/queryQbListData")
    public List<VehicleOwnerVO> queryQbListData(@RequestParam(value = "qbNumber", required = false) String qbNumber,
                                                @RequestParam(value = "isPerformed", required = false) Integer
                                                        isPerformed,
                                                @RequestParam(value = "isClosed", required = false) Integer isClosed,
                                                @RequestParam(value = "vin", required = false) String vin,
                                                @RequestParam(value = "dealerCode", required = false) String
                                                        dealerCode,@RequestParam(value = "ids",required = false)List<Long> ids);

    @PostMapping("/inviteAutoCreate/queryQbListDataPost")
    public List<VehicleOwnerVO> queryQbListDataPost(@RequestBody List<Long> ids);


    /**
     * 消息发送
     *
     * @param title
     * @param message
     * @param dealerCode
     * @param roleCode
     * @return
     */
    @GetMapping("/sendMessage/sendMessageByRole")
    public int sendMessageByRole(@RequestParam(value = "title") String title,
                                 @RequestParam(value = "message") String message,
                                 @RequestParam(value = "dealerCode") String dealerCode,
                                 @RequestParam(value = "roleCode") String roleCode);

    /**
     * 查询新车----生成续保任务
     * <AUTHOR>
     * @param createDate
     * @return
     */
    @GetMapping("/inviteAutoCreate/getNewVehicleForInsurance")
    public VehicleOwnerInsurancePO getNewVehicleForInsurance(@RequestParam(value = "createDate") String createDate);

    /**
     * 消息发送
     *
     * @param title
     * @param message
     * @param dealerCode
     * @param roleCode
     * @return
     */
    @GetMapping("/interf/accidentSendMessage")
    public int sendMessageByRoleNotRedirect(@RequestParam(value = "title") String title,
                                 @RequestParam(value = "message") String message,
                                 @RequestParam(value = "dealerCode") String dealerCode,
                                 @RequestParam(value = "roleCode") String roleCode);

    /**
     * 查询车辆保养信息
     * @param vin
     * @return
     */
    @GetMapping("/vehicleowner/getVehicleInvite")
    public VehicleInviteVO getVehicleInvite(@RequestParam(value = "vin") String vin);
    /**
     * 客诉导出
     * @param complaintInfMoreDTO
     * @return
     */
    @PostMapping("/exportComplaintController/exportComplaint")
    public List<Map> exportComplaint(@RequestBody ComplaintInfMoreDTO complaintInfMoreDTO);

    /**
     * 获取卡券核销信息
     * @param couponIds
     * @return
     */
    @GetMapping("/couponDetail/couponVerify")
    public List<Map> couponVerify(
            @RequestParam(value = "couponIds") String couponIds);

    @PostMapping("/couponDetail/couponVerifyPost")
    @ResponseBody
    public List<Map> couponVerifyPost(@RequestBody List<String> couponIds);

    @GetMapping("/inviteAutoCreate/getRepairOrderLastNew")
    public List<RepairOrderVO> getRepairOrderLastNew(@RequestParam(value = "vin") String vin);

    @GetMapping("/inviteAutoCreate/queryCompany")
    public int  queryCompany(@RequestParam(value = "companyCode") String companyCode);
    @GetMapping("/inviteAutoCreate/queryCompanyInfo")
    public List<CompanyDetailDTO>  queryCompanyInfo();

    /**
     * 查询车辆保养信息
     * @param vin
     * @return
     */
    @GetMapping("/vehicleowner/vehicleByVinAndCode")
    public VehicleOwnerVO vehicleByVinAndCode(@RequestParam("dearcode") String dearcode , @RequestParam("vin")String vin );
    @PostMapping("/inviteAutoCreate/queryRegularMaintainList")
    List<VehicleOwnerVO> queryRegularMaintainList(List<String> listX);
    @GetMapping("/inviteAutoCreate/queryRepairOrderByVinAndCode")
    VehicleOwnerVO queryRepairOrderByVinAndCode(@RequestParam("vin")String vin, @RequestParam("dealerCode")String dealerCode);
    @GetMapping("/inviteAutoCreate/queryRepairOrderByVinAndCodeList")
    Map<String, VehicleOwnerVO> queryRepairOrderByVinAndCodeList(@RequestParam("sql")String sql);

    @GetMapping("/inviteAutoCreate/queryRepairOrderByVinAndCodeAndJL")
    VehicleOwnerVO queryRepairOrderByVinAndCodeAndJL(@RequestParam("vin")String vin, @RequestParam("dealerCode")String dealerCode);

    @GetMapping("/inviteAutoCreate/queryRepairOrderByVinList")
    Map<String, VehicleOwnerVO> queryRepairOrderByVinList(@RequestParam("sql")String sql);

    @GetMapping("/inviteAutoCreate/queryVocByVin")
    VehicleOwnerVO queryVocByVin(@RequestParam("vin")String vin);
    @GetMapping("/inviteAutoCreate/queryVocByVinList")
    Map<String, VehicleOwnerVO> queryVocByVinList(@RequestParam("sql")String sql);


    @GetMapping("/inviteAutoCreate/queryFirstMaintainByVin")
    List<VehicleOwnerVO> queryFirstMaintainByVin(@RequestParam(value = "vin")  String vin);
    @GetMapping("/inviteAutoCreate/selectRepairOrderByVin")
    int selectRepairOrderByVin(@RequestParam(value = "vin") String vin, @RequestParam(value = "dateTime")String dateTime);
    @GetMapping("/inviteAutoCreate/queryRepairOrderByVinAndCodeLimitOne")
    RepairVo queryRepairOrderByVinAndCodeLimitOne(@RequestParam(value = "vin") String vin, @RequestParam(value = "dealerCode")String dealerCode);
    @GetMapping("/inviteAutoCreate/queryVinByRepair")
    List<RepairVo> queryVinByRepair(@RequestParam(value = "dateTime") String dateTime, @RequestParam(value = "list")List<String> list);
    @GetMapping("/inviteAutoCreate/selectVin")
    List<String> selectVin(@RequestParam("begIndex") Integer begIndex, @RequestParam("endIndex")Integer endIndex);
    @GetMapping("/inviteAutoCreate/queryRepairJL")
    List<RepairVo> queryRepairJL(@RequestParam("list")List<String> list2);
    @GetMapping("/inviteAutoCreate/selectVinByCode")
    List<String> selectVinByCode(@RequestParam("list")List<String> list);
    @GetMapping("/inviteAutoCreate/queryUserInfo")
    Integer queryUserInfo(@RequestParam("userId")String userId);

    @PostMapping("/inviteDataClean/queryIncRepTask")
    List<InvDataCleanVO> queryIncRepTask();
    @PostMapping("/inviteDataClean/queryIncClueTask")
    List<InvDataCleanVO> queryIncClueTask();
}
