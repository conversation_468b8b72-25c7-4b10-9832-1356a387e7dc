package com.yonyou.dmscus.customer.feign;

import com.yonyou.dmscloud.framework.FeignConfig;
import com.yonyou.dmscus.customer.dto.*;
import com.yonyou.dmscus.customer.entity.dto.OrgQueryDto;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.vo.OrgVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value ="mid-end-org-center",url = "${access.mid.url.midEndOrgCenter}",configuration = FeignConfig.class)
public interface MidUserOrganizationClient {

    @GetMapping(value = {"/tmOrg/getOrgs/{orgId}"},
            produces = {"application/json;charset=UTF-8"})
    ResponseDTO<String> getOrgs(@PathVariable("orgId") String orgId);

    @GetMapping(value = {"tmOrg/orgInfo"},
            produces = {"application/json;charset=UTF-8"})
    ResponseDTO<OrgVo> orgInfo(@RequestBody OrgQueryDto dto );

    /**
     * 查询经销商详情
     * @param dto
     * @return
     */
    @PostMapping(value = "/org/company/selectByCompanyCode", produces = "application/json;charset=UTF-8")
    ResponseDTO<List<CompanyDetailInfoDTO>> selectByCompanyCode(@RequestBody CompanyParamDTO dto);
    /**
     * 查询经销商详情
     * @param dto
     * @return
     */
    @PostMapping(value = "/company/info", produces = "application/json;charset=UTF-8")
    ResponseDTO<List<CompanyInfoDTO>> queryCompanyInfo(@RequestBody CompanyRequestDTO dto);

}
