package com.yonyou.dmscus.customer.feign;

import com.yonyou.cloud.common.beans.RestResultResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限服务
 */
@FeignClient(name = "domain-maintain-auth")
public interface DomainMaintainAuthFeign {

    @ApiOperation(value = " 查询白名单是否开启", notes = "查询白名单是否开启")
    @GetMapping(value = "/whitelist/checkWhitelist/enable/interf")
    RestResultResponse<Boolean> checkIfWhitelistEnabled(@RequestParam(value = "modType") Integer modType);

    @ApiOperation(value = "查询查询白名单经销商", notes = "查询查询白名单经销商")
    @GetMapping(value = "/whitelist/checkWhitelist/getWhitelistedDealers/interf")
    RestResultResponse<List<String>> getWhitelistedDealers(@RequestParam(value = "modType") Integer modType, @RequestParam(value = "rosterType") Integer rosterType);

    @GetMapping(value = "/whitelist/checkWhitelist")
    RestResultResponse<Boolean> checkWhitelist(@RequestParam(value = "ownerCode") String ownerCode,
                           @RequestParam(value = "modType") Integer modType,
                           @RequestParam(value = "rosterType") Integer rosterType,
                           @RequestParam(value = "vin") String vin ,
                           @RequestParam(value = "groupCode", required = false) String groupCode);
}
