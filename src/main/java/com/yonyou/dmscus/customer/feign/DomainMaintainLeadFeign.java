package com.yonyou.dmscus.customer.feign;

import com.yonyou.cloud.common.beans.RestResultResponse;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentClueCrmInfoMqDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 功能描述：养修线索Feign接口
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
@FeignClient(name = "domain-maintain-leads")
public interface DomainMaintainLeadFeign {


    /**
     * 事故线索更新上报后的crm信息
     *
     */
    @PostMapping("/accidentClues/crmInfo")
    RestResultResponse<Void> updateAccidentClueCrmInfo(List<AccidentClueCrmInfoMqDto> parseArray);

    /**
     * 批量查询DTC名称
     * @param dtcList
     * @return
     */
    @PostMapping("/dtcClues/batchQueryDtcNameByCode")
    RestResultResponse<Map<String, String>> batchQueryDtcNameByCode(@RequestBody List<String> dtcList, @RequestParam("category") Integer category);
}
