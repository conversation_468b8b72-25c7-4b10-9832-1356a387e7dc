package com.yonyou.dmscus.customer.feign;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.dmscus.customer.feign.dto.WarningDto;
import com.yonyou.dmscus.customer.feign.response.HcResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "DATAIN", url = "${access.mid.url.datain}")
public interface DatainFeign {
    /**
     * 功能描述：获取cdp TOKEN
     */
    @PostMapping(path = "/failure-light-api/dl/vehicle/warning", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    HcResponse<JSONObject> queryCluesWarningInfo(@RequestBody WarningDto warningDto);

}
