package com.yonyou.dmscus.customer.feign;


import com.yonyou.dmscloud.framework.FeignConfig;
import com.yonyou.dmscus.customer.dto.EmpQueryDto;
import com.yonyou.dmscus.customer.dto.QueryUserByOrgTypeDTO;
import com.yonyou.dmscus.customer.dto.RangeDto;
import com.yonyou.dmscus.customer.dto.UserOrgInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.PageResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.common.QueryUserPositionDTO;
import com.yonyou.dmscus.customer.entity.dto.common.UserPositionOutDTO;
import com.yonyou.dmscus.customer.entity.vo.OrgVo;
import com.yonyou.dmscus.customer.middleInterface.RequestDTO;
import com.yonyou.dmscus.customer.middleInterface.UserInfoOutDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@FeignClient(value = "mid-end-auth-center",url = "${access.mid.url.midEndAuthCenter}",configuration = FeignConfig.class)
public interface MidEndAuthCenterClient {

    @PostMapping(path = "/users/org/role", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseDTO<List<UserOrgInfoDTO>> queryUserByOrgType(@RequestBody RequestDTO<QueryUserByOrgTypeDTO> queryUserByOrgTypeDTO);

    @GetMapping(path = "/emp/info/emp")
    ResponseDTO<UserOrgInfoDTO> userInfo(@RequestParam("userId") String userId);

    @PostMapping(path = "/emp/info")
    ResponseDTO<PageResponseDTO<List<UserOrgInfoDTO>>> empInfo(@RequestBody EmpQueryDto dto );

    @PostMapping(path = "/emp/dealer/info")
    ResponseDTO<List<UserOrgInfoDTO>> empDealerInfo(@RequestBody EmpQueryDto dto );

    @PostMapping(path = "/emp/list")
    ResponseDTO<List<OrgVo>> empList(@RequestBody EmpQueryDto dto );

    @GetMapping(value = {"/permission/range/{menuId}"})
    ResponseDTO<RangeDto> findPermissionRangeByMenu(@PathVariable("menuId") String menuId);

    @PostMapping(value = {"/emp/position"})
    ResponseDTO<List<UserPositionOutDTO>> getEmpInfoList(@RequestBody com.yonyou.dmscus.customer.entity.dto.middleground.RequestDTO<QueryUserPositionDTO> params);

    @PostMapping(path = "/users/id/list")
    ResponseDTO<List<UserInfoOutDTO>> queryUserInfoList(@RequestBody List<String> userIds);
}
