package com.yonyou.dmscus.customer.feign.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("线索查工单专用入参")
@Data
public class ClueParamVO {
    @ApiModelProperty(value = "createDate",name = "时间")
    private String createDate;
    @ApiModelProperty(value = "roNo",name = "工单号")
    private String roNo;
    @ApiModelProperty(value = "ownerCode",name = "经销商")
    private List<String> ownerCode;
    @ApiModelProperty(value = "dealerCode",name = "经销商")
    private String dealerCode;
    @ApiModelProperty(value = "balanceTime",name = "结算时间")
    private String balanceTime;
    @ApiModelProperty(value = "vin",name = "车架号")
    private String vin;
}
