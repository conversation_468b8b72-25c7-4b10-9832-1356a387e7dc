package com.yonyou.dmscus.customer.feign.response;

import com.yonyou.dmscus.customer.constants.CommonConstants;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * cdp 数据返回
 */
@Data
public class CdpResponse<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 返回代码，TRUE表示成功，其他表示失败
     */
    private String code;

    /**
     * 返回代码，TRUE表示成功，其他表示失败
     */
    private Boolean success;

    /**
     * 返回描述
     */
    private String msg;

    /**
     * 返回数据
     */
    private T data;

    public boolean isSuccess() {
        return Objects.equals(this.getCode(), CommonConstants.SUCCESS_CODE);
    }

    public boolean isFail() {
        return !isSuccess();
    }
}
