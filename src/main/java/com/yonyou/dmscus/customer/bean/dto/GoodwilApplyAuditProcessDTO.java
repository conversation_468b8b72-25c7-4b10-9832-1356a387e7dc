package com.yonyou.dmscus.customer.bean.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 亲善审批流程表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-22
 */
    
public class GoodwilApplyAuditProcessDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

        
    /**
     * 系统ID
     */
                private String appId;
                
    /**
     * 所有者代码
     */
                private String ownerCode;
                
    /**
     * 所有者的父组织代码
     */
                private String ownerParCode;
                
    /**
     * 组织ID
     */
                private Integer orgId;
                
    /**
     * 主键id
     */
                private Long id;
                
    /**
     * 亲善单id
     */
                private Long goodwillApplyId;
                
    /**
     * 审核对象
     */
                private Integer auditObject;
                
    /**
     * 审批类型
     */
                private Integer auditType;
                
    /**
     * 审批职位
     */
                private String auditPosition;
                
    /**
     * 审批顺序
     */
                private Integer auditSort;
                
    /**
     * 审批状态
     */
                private Integer auditStatus;
                
    /**
     * 审批日期
     */
                @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    private LocalDateTime auditDate;
                
    /**
     * 是否有效
     */
                private Integer isValid;
                
    /**
     * 是否删除:1,删除；0,未删除
     */
                private Boolean isDeleted;
                
    /**
     * 创建时间
     */
                @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    private LocalDateTime createdAt;
                
    /**
     * 修改时间
     */
                @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    private LocalDateTime updatedAt;
            
    public GoodwilApplyAuditProcessDTO() {
        super();
    }

                                
    public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
            }
                                
    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
            }
                                
    public String getOwnerParCode(){
        return ownerParCode;
    }


    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
            }
                                
    public Integer getOrgId(){
        return orgId;
    }


    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
            }
                                            
    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
            }
                                
    public Long getGoodwillApplyId(){
        return goodwillApplyId;
    }


    public void  setGoodwillApplyId(Long goodwillApplyId) {
        this.goodwillApplyId = goodwillApplyId;
            }
                                
    public Integer getAuditObject(){
        return auditObject;
    }


    public void  setAuditObject(Integer auditObject) {
        this.auditObject = auditObject;
            }
                                
    public Integer getAuditType(){
        return auditType;
    }


    public void  setAuditType(Integer auditType) {
        this.auditType = auditType;
            }
                                
    public String getAuditPosition(){
        return auditPosition;
    }


    public void  setAuditPosition(String auditPosition) {
        this.auditPosition = auditPosition;
            }
                                
    public Integer getAuditSort(){
        return auditSort;
    }


    public void  setAuditSort(Integer auditSort) {
        this.auditSort = auditSort;
            }
                                
    public Integer getAuditStatus(){
        return auditStatus;
    }


    public void  setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
            }
                                
    public LocalDateTime getAuditDate(){
        return auditDate;
    }


    public void  setAuditDate(LocalDateTime auditDate) {
        this.auditDate = auditDate;
            }
                                
    public Integer getIsValid(){
        return isValid;
    }


    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
            }
                                
    public LocalDateTime getCreatedAt(){
        return createdAt;
    }


    public void  setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
            }
                                
    public LocalDateTime getUpdatedAt(){
        return updatedAt;
    }


    public void  setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
            }
    
    @Override
    public String toString() {
        return "GoodwilApplyAuditProcessDTO{" +
                                            "appId=" + appId +
                                                            ", ownerCode=" + ownerCode +
                                                            ", ownerParCode=" + ownerParCode +
                                                            ", orgId=" + orgId +
                                                            ", id=" + id +
                                                            ", goodwillApplyId=" + goodwillApplyId +
                                                            ", auditObject=" + auditObject +
                                                            ", auditType=" + auditType +
                                                            ", auditPosition=" + auditPosition +
                                                            ", auditSort=" + auditSort +
                                                            ", auditStatus=" + auditStatus +
                                                            ", auditDate=" + auditDate +
                                                            ", isValid=" + isValid +
                                                            ", isDeleted=" + isDeleted +
                                                            ", createdAt=" + createdAt +
                                                            ", updatedAt=" + updatedAt +
                                    "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
