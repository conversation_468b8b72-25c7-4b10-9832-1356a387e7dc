package com.yonyou.dmscus.customer.bean.entity;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

    import com.yonyou.dmscloud.framework.base.po.BasePO;
    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.extension.activerecord.Model;
    import com.baomidou.mybatisplus.annotation.Version;
    import com.baomidou.mybatisplus.annotation.TableId;
    import java.time.LocalDateTime;
    import com.baomidou.mybatisplus.annotation.TableField;
    
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 亲善审批流程表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-22
 */
@TableName("tt_goodwil_apply_audit_process")
public class GoodwilApplyAuditProcessPO extends BasePO<GoodwilApplyAuditProcessPO> {

    private static final long serialVersionUID=1L;
    
    /**
     * 系统ID
     */
    @TableField("APP_ID")
        private String appId;
    
    /**
     * 所有者代码
     */
    @TableField("OWNER_CODE")
        private String ownerCode;
    
    /**
     * 所有者的父组织代码
     */
    @TableField("OWNER_PAR_CODE")
        private String ownerParCode;
    
    /**
     * 组织ID
     */
    @TableField("ORG_ID")
        private Integer orgId;
        
    /**
     * 主键id
     */
            @TableId(value = "id", type = IdType.AUTO)
                        private Long id;
    
    /**
     * 亲善单id
     */
    @TableField("goodwill_apply_id")
        private Long goodwillApplyId;
    
    /**
     * 审核对象
     */
    @TableField("audit_object")
        private Integer auditObject;
    
    /**
     * 审批类型
     */
    @TableField("audit_type")
        private Integer auditType;
    
    /**
     * 审批职位
     */
    @TableField("audit_position")
        private String auditPosition;
    
    /**
     * 审批顺序
     */
    @TableField("audit_sort")
        private Integer auditSort;
    
    /**
     * 审批状态
     */
    @TableField("audit_status")
        private Integer auditStatus;
    
    /**
     * 审批日期
     */
    @TableField("audit_date")
        private LocalDateTime auditDate;
    
    /**
     * 是否有效
     */
    @TableField("is_valid")
        private Integer isValid;
    
    /**
     * 是否删除:1,删除；0,未删除
     */
    @TableField("is_deleted")
        private Boolean isDeleted;
    
    /**
     * 创建时间
     */
    @TableField("created_at")
        private Date createdAt;
    
    /**
     * 修改时间
     */
    @TableField("updated_at")
        private Date updatedAt;

    public GoodwilApplyAuditProcessPO(){
        super();
    }

                    
    public String getAppId(){
        return appId;
    }

        public void setAppId(String appId) {
            this.appId = appId;
            }
                    
    public String getOwnerCode(){
        return ownerCode;
    }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
            }
                    
    public String getOwnerParCode(){
        return ownerParCode;
    }

        public void setOwnerParCode(String ownerParCode) {
            this.ownerParCode = ownerParCode;
            }
                    
    public Integer getOrgId(){
        return orgId;
    }

        public void setOrgId(Integer orgId) {
            this.orgId = orgId;
            }
                    
    public Long getId(){
        return id;
    }

        public void setId(Long id) {
            this.id = id;
            }
                    
    public Long getGoodwillApplyId(){
        return goodwillApplyId;
    }

        public void setGoodwillApplyId(Long goodwillApplyId) {
            this.goodwillApplyId = goodwillApplyId;
            }
                    
    public Integer getAuditObject(){
        return auditObject;
    }

        public void setAuditObject(Integer auditObject) {
            this.auditObject = auditObject;
            }
                    
    public Integer getAuditType(){
        return auditType;
    }

        public void setAuditType(Integer auditType) {
            this.auditType = auditType;
            }
                    
    public String getAuditPosition(){
        return auditPosition;
    }

        public void setAuditPosition(String auditPosition) {
            this.auditPosition = auditPosition;
            }
                    
    public Integer getAuditSort(){
        return auditSort;
    }

        public void setAuditSort(Integer auditSort) {
            this.auditSort = auditSort;
            }
                    
    public Integer getAuditStatus(){
        return auditStatus;
    }

        public void setAuditStatus(Integer auditStatus) {
            this.auditStatus = auditStatus;
            }
                    
    public LocalDateTime getAuditDate(){
        return auditDate;
    }

        public void setAuditDate(LocalDateTime auditDate) {
            this.auditDate = auditDate;
            }
                    
    public Integer getIsValid(){
        return isValid;
    }

        public void setIsValid(Integer isValid) {
            this.isValid = isValid;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }

        public void setIsDeleted(Boolean isDeleted) {
            this.isDeleted = isDeleted;
            }
                    
    public Date getCreatedAt(){
        return createdAt;
    }

        public void setCreatedAt(Date createdAt) {
            this.createdAt = createdAt;
            }
                    
    public Date getUpdatedAt(){
        return updatedAt;
    }

        public void setUpdatedAt(Date updatedAt) {
            this.updatedAt = updatedAt;
            }
    
    @Override
    protected Serializable pkVal(){
            return this.id;
        }

@Override
    public String toString(){
        return"GoodwilApplyAuditProcessPO{" +
                            "appId=" + appId +
                                    ", ownerCode=" + ownerCode +
                                    ", ownerParCode=" + ownerParCode +
                                    ", orgId=" + orgId +
                                    ", id=" + id +
                                    ", goodwillApplyId=" + goodwillApplyId +
                                    ", auditObject=" + auditObject +
                                    ", auditType=" + auditType +
                                    ", auditPosition=" + auditPosition +
                                    ", auditSort=" + auditSort +
                                    ", auditStatus=" + auditStatus +
                                    ", auditDate=" + auditDate +
                                    ", isValid=" + isValid +
                                    ", isDeleted=" + isDeleted +
                                    ", createdAt=" + createdAt +
                                    ", updatedAt=" + updatedAt +
                    "}";
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"id");
    }

}
