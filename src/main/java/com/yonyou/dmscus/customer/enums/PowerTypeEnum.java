package com.yonyou.dmscus.customer.enums;

import com.yonyou.dmscloud.function.exception.ServiceBizException;
import lombok.Getter;

import java.util.Arrays;

@Getter
public enum PowerTypeEnum {

    FV(0,"燃油"),
    HEV(0,"混动"),
    BEV(1,"电动");

    private final Integer bevFlag;
    private final String desc;

    PowerTypeEnum(Integer bevFlag, String desc) {
        this.bevFlag = bevFlag;
        this.desc = desc;
    }

    public static void checkPowerType (String desc) {
        boolean exist = Arrays.stream(values()).anyMatch(powerTypeEnum -> powerTypeEnum.getDesc().equals(desc));
        if (!exist) {
            throw new ServiceBizException("power type not exist");
        }
    }


}
