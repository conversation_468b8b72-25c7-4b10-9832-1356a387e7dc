package com.yonyou.dmscus.customer.enums;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

@ApiModel("经销商营业状态枚举")
@Getter
public enum CompanyBusinessStatusEnum {

    PREPARATION_FOR_OPENING(16031001, "开业准备"),
    IN_BUSINESS(16031002, "营业中"),
    PAUSE_BUSINESS(16031003, "暂停营业"),
    DISCONTINUE_OPERATIONS(16031004, "终止运营"),
    BRIDGE_SALE(16031005, "过渡销售");

    @ApiModelProperty("营业状态码")
    private Integer code;
    @ApiModelProperty("营业状态描述")
    private String desc;

    CompanyBusinessStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getBusinessDesc(Integer businessCode) {

        if (null == businessCode) {
            return "";
        }

        for (CompanyBusinessStatusEnum source : CompanyBusinessStatusEnum.values()) {
            if (String.valueOf(source.getCode()).equals(String.valueOf(businessCode))) {
                return source.getDesc();
            }
        }

        return "";

    }
}
