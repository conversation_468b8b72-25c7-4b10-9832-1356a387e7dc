package com.yonyou.dmscus.customer.enums;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Description： 亲善功能文件类型枚举类
 * <p>
 * Author: caizmv
 * <p>
 * Date: Created in 2021/7/15 17:55
 * <p>
 * Company: 用友公司
 */
public enum GoodwillFileTypeEnum implements Displayable {

    /**
     * 预申请附件
     */
    PRE_APPLY(82821001, "预申请附件"),

    /**
     * 亲善成本统计
     */
    GOODWILL_COST_STATIS(82821002, "亲善成本统计"),

    /**
     * 亲善成本截图
     */
    GOODWILL_COST_IMAG(82821003, "亲善成本截图"),
    /**
     * 故障维修工单/环检单
     */
    TROUBLE_REPAIR_ORDER(82821004, "故障维修工单/环检单"),
    /**
     * 故障维修领料单
     */
    TROUBLE_REPAIR_MATERIAL(82821005, "故障维修领料单"),
    /**
     * 亲善安装工单/领料单
     */
    GOODWILL_INSTALL_MATERIAL(82821006, "亲善安装工单/领料单"),
    /**
     * 情况说明和解协议
     */
    QK_AGREEMENT(82821007, "情况说明和解协议"),
    /**
     * 退换车补充材料
     */
    RETURN_CAR_MATERIAL(82821008, "退换车补充材料"),
    /**
     * 管理层审核邮件-VP
     */
    MANAGER_AUDIT_VP(82821009, "管理层审核邮件-VP"),
    /**
     * 管理层审核邮件-CEO
     */
    MANAGER_AUDIT_CEO(82821010, "管理层审核邮件-CEO"),
    /**
     * 费用更新附件
     */
    AMOUNT_UPDATE(82821011, "费用更新附件"),
    /**
     * VCDC其他附件
     */
    VCDC_ATTACH(82821012, "VCDC其他附件"),
    /**
     * 客户身份证明
     */
    CUST_IDENTIFICATION(82821013, "客户身份证明"),
    /**
     * 其他附件
     */
    OTHER_ATTACH(82821014, "其他附件");

    /**
     * Implementing a fromString method on an enum type
     */
    private static Map<Integer, GoodwillFileTypeEnum> stringToEnum = new LinkedHashMap<Integer, GoodwillFileTypeEnum>();
    static {
        // Initialize map from constant name to enum constant
        for (GoodwillFileTypeEnum value : values()) {
            stringToEnum.put(value.code(), value);
        }
    }

    /**
     * 枚举代码
     */
    private Integer code;
    /**
     * 枚举显示
     */
    private String display;

    /**
     * 构造函数
     *
     * @param code    枚举代码
     * @param display 枚举显示
     */
    private GoodwillFileTypeEnum(Integer code, String display) {
        this.code = code;
        this.display = display;
    }

    @Override
    public Integer code() {
        return this.code;
    }

    @Override
    public String display() {
        return this.display;
    }

    // Returns WinStatus for string, or null if string is invalid
    public static GoodwillFileTypeEnum fromCode(Integer code) {
        return stringToEnum.get(code);
    }

    @Override
    public String toString() {
        return this.display;
    }

    /**
     * 功能描述: <br>
     * 〈功能详细描述〉 获取枚举值
     *
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static Map<Integer, GoodwillFileTypeEnum> getObjEnum() {
        return stringToEnum;
    }
}
