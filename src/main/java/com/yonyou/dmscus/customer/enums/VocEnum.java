package com.yonyou.dmscus.customer.enums;

public enum VocEnum {
    NORMAL("Normal",83981001),
    TIMEEXCEEDED("TimeExceeded",83981002),
    ALMOSTTIMEFORSERVICE("AlmostTimeForService",83981003),
    TIMEFORSERVICE("TimeForService",83981004);

    private  String statusName;
    private  int statusValue;


    VocEnum(String statusName,int statusValue) {
       this.statusName=statusName;
       this.statusValue=statusValue;
    }
    public String getStatusName() {
        return this.statusName;
    }
    public int getStatusValue() {
        return this.statusValue;
    }
    /**
     * 根据类型的名称，返回类型的枚举实例。
     *
     * @param statusName 类型名称
     */
    public static int getStatusValues(String statusName) {
        for (VocEnum type : VocEnum.values()) {
            if (type.getStatusName().equals(statusName)) {
                return type.statusValue;
            }
        }
        return 0;
    }



    /**
     * 根据类型的名称，返回类型的枚举实例。
     *
     * @param statusValue 类型
     */
    public static String getStatusName(int statusValue) {
        for (VocEnum type : VocEnum.values()) {
            if (type.getStatusValue()==statusValue) {
                return type.statusName;
            }
        }
        return null;
    }

}
