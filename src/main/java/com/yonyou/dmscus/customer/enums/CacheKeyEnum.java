package com.yonyou.dmscus.customer.enums;


import java.util.Objects;

/**
 * redis
 */
public enum CacheKeyEnum {

    /** CDP token缓存key 3分钟*/
    cdp_token_key("newbei:cdp:token:", 1000 * 60 * 3),
    cdp_tag_key("DTCC:DMSCUS_CUSTOMER:CDP_TAG:TASK_NYSC:", 1000 * 60 * 60),
    task_complete_clue_key("DTCC:DMSCUS_CUSTOMER:CDP_TAG:TASK_COMPLETE_CLUE :", 1000 * 60 * 30),
    task_complete_key("DTCC:DMSCUS_CUSTOMER:CDP_TAG:TASK_COMPLETE :", 1000 * 60 * 30),

    FAULT_LIGHT_SYNC_KEY("DTCC:DMSCUS_CUSTOMER:FAULT_LIGHT:SYNC_TASK:", 1000 * 60 * 30),

    FAULT_LIGHT_UPDATE_KEY("DTCC:DMSCUS_CUSTOMER:FAULT_LIGHT:UPDATE_TASK:", 1000 * 60 * 30),
    CUSTOMER_COMPLAINT_KEY("DTCC:DMSCUS_CUSTOMER:CUSTOMER_COMPLAINT:", 1000 * 60 * 30),
    CUSTOMER_GOOD_WILL_NOTICE_KEY("DTCC:DMSCUS_CUSTOMER:GOOD_WILL_NOTICE:", 1000 * 60 * 30),
    updateHighlightFlag("updateHighlightFlag"),
    queryHighlightFlag("queryHighlightFlag")
    ;
    private String key;

    private long expire;

    CacheKeyEnum(String key, long expire) {
        this.key = key;
        this.expire = expire;
    }

    CacheKeyEnum(String pk) {
        this.key = pk;
    }
    public String getKey() {
        return key;
    }
    public String getKey(Object key_) {
        return this.key + key_;
    }
    public long getExpire() {
        return expire;
    }

    public static long getExpireByName(String key) {
        for (CacheKeyEnum valueEnum : CacheKeyEnum.values()) {
            if (Objects.equals(key,valueEnum.getKey())) {
                return valueEnum.getExpire();
            }
        }
        return 1;
    }
}
