package com.yonyou.dmscus.customer.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/12/14 10:58
 * @Version 1.0
 */
@Getter
public enum RemotePushThreadSizeEnum {

    THREAD_SIZE_LESS_THAN_50000(50000, 1),
    THREAD_SIZE_LESS_THAN_100000(100000, 2),
    THREAD_SIZE_LESS_THAN_200000(200000, 4),
    THREAD_SIZE_GREATER_THAN_200000(200000, 8);

    private final Integer count;
    private final Integer threadSize;

    RemotePushThreadSizeEnum(Integer count, Integer threadSize) {
        this.count = count;
        this.threadSize = threadSize;
    }
}
