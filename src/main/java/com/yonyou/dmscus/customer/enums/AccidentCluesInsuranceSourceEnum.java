/*
 * Copyright (c) Volvo CAR Distribution (SHANGHAI) Co., Ltd. 2023. All rights reserved.
 */

package com.yonyou.dmscus.customer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 线索异常原因
 * 异常原因:10301-非沃尔沃车;10302-无经销商
 *
 */
@Getter
@AllArgsConstructor
public enum AccidentCluesInsuranceSourceEnum {
    OTHER_WAY(15211012, "其他"),
    CUSTOMER_CALL(15211015, "客户来电"),
    FOUR_HUNDRED(15211017, "400"),
    EXPANDING_BUSINESS_EXTERNALLY(15211016, "外拓业务"),
    INSURANCE_COMPANY_SMS_PUSH(15211014, "保险公司短信推送");

    /**
     * 初始化默认设置属性
     */
    private final Integer code;

    /**
     * 初始化默认设置属性
     */
    private final String value;

    public static String fromChineseName(Integer code) {
        for (AccidentCluesInsuranceSourceEnum number : values()) {
            if (number.getCode().equals(code)) {
                return number.getValue();
            }
        }
        return null; // 如果找不到匹配的中文名称，则返回null
    }
}
