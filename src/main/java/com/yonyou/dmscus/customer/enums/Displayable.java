package com.yonyou.dmscus.customer.enums;

/**
 * 枚举类型接口<br> 
 * 定义枚举类型接口
 * @since [商品视图与服务/version=1]
 */
public interface Displayable {
    
    /**
     * 
     * 功能描述: <br>
     * 获取枚举类型编码
     *
     * @return 类型编码
     * @since [商品视图与服务/version=1]
     */
    Integer code();
    
    /**
     * 
     * 功能描述: <br>
     * 获取枚举类型字符描述
     *
     * @return 类型字符描述
     * @since [商品视图与服务/version=1]
     */
    String display();
}
