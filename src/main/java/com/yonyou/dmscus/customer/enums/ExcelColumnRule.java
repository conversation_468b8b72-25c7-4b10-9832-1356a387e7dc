package com.yonyou.dmscus.customer.enums;

import java.math.BigDecimal;

public class ExcelColumnRule {

    private boolean isMust;
    
    private String codeType;
    
    private Integer maxLength;
    
    private Integer minLength;
    
    private BigDecimal minPrice;
    
    private BigDecimal maxPrice;
    
    //数量/数字
    private Long maxNum;
    
    private Long minNum;
    
    private ExcelColumnRule(Builder builder) {
        this.isMust = builder.isMust;
        this.codeType = builder.codeType;
        this.maxLength = builder.maxLength;
        this.minLength = builder.minLength;
        this.maxNum = builder.maxNum;
        this.minNum = builder.minNum;
        this.minPrice = builder.minPrice;
        this.maxPrice = builder.maxPrice;
    }
    
    public static class Builder {
        private boolean isMust;
        
        private String codeType;
        
        private Integer maxLength;
        
        private Integer minLength;
        
        private BigDecimal minPrice;
        
        private BigDecimal maxPrice;
        
        private Long maxNum;
        
        private Long minNum;
        
        public Builder isMust(boolean isMust) {
            this.isMust = isMust;
            return this;
        }
        
        public Builder codeType(String codeType) {
            this.codeType = codeType;
            return this;
        }
        
        public Builder maxLength(Integer maxLength) {
            this.maxLength = maxLength;
            return this;
        }
        
        public Builder minLength(Integer minLength) {
            this.minLength = minLength;
            return this;
        }
        
        public Builder minPrice(BigDecimal minPrice) {
        	this.minPrice = minPrice;
            return this;
		}
        
        public Builder maxPrice(BigDecimal maxPrice) {
        	this.maxPrice = maxPrice;
            return this;
		}
        
        public Builder maxNum(Long maxNum) {
            this.maxNum = maxNum;
            return this;
        }
        
        public Builder minNum(Long minNum) {
            this.minNum = minNum;
            return this;
        }
        
        public ExcelColumnRule build() {
            return new ExcelColumnRule(this);
        }
    }
    
    
    public boolean isMust() {
        return isMust;
    }
    public void setMust(boolean isMust) {
        this.isMust = isMust;
    }
    public String getCodeType() {
        return codeType;
    }
    public void setCodeType(String codeType) {
        this.codeType = codeType;
    }
    public Integer getMaxLength() {
        return maxLength;
    }
    public void setMaxLength(Integer maxLength) {
        this.maxLength = maxLength;
    }
    public Integer getMinLength() {
        return minLength;
    }
    public void setMinLength(Integer minLength) {
        this.minLength = minLength;
    }
	public BigDecimal getMinPrice() {
		return minPrice;
	}
	public void setMinPrice(BigDecimal minPrice) {
		this.minPrice = minPrice;
	}
	public BigDecimal getMaxPrice() {
		return maxPrice;
	}
	public void setMaxPrice(BigDecimal maxPrice) {
		this.maxPrice = maxPrice;
	}
	public Long getMaxNum() {
		return maxNum;
	}
	public void setMaxNum(Long maxNum) {
		this.maxNum = maxNum;
	}
	public Long getMinNum() {
		return minNum;
	}
	public void setMinNum(Long minNum) {
		this.minNum = minNum;
	}

    
}
