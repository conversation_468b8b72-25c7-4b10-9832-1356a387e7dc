
/** 
*Copyright 2020 Yonyou Corporation Ltd. All Rights Reserved.
* This software is published under the terms of the Yonyou Software
* License version 1.0, a copy of which has been included with this
* distribution in the LICENSE.txt file.
*
* @Project Name : dmscus.customer
*
* @File name : VocMileageAddEnum.java
*
* <AUTHOR> caizhonming
*
* @Date : 2020年7月8日
*
*/
	
package com.yonyou.dmscus.customer.enums;

import java.util.HashMap;
import java.util.Map;

/**
* VOC里程Excel对应列枚举类
* <AUTHOR>
* @date 2020年7月8日
*/

public enum VocMileageAddEnum implements ExcelColumnEnum{



    VIN(0, "VIN码", 
            new ExcelColumnRule.Builder()
            .isMust(true)
            .maxLength(18)
            .build()),
    MILEAGE_M(1, "里程(米)",
            new ExcelColumnRule.Builder()
            .isMust(true)
            .minNum(0L)
            .build()),
    GET_TIME(2, "获取里程时间",
              new ExcelColumnRule.Builder()
              .isMust(false)
              .build())
              ;
    /**
     * Implementing a fromString method on an enum type
     */
    public static Map<Integer, VocMileageAddEnum> stringToEnum = new HashMap<Integer, VocMileageAddEnum>();
    static {
        // Initialize map from constant name to enum constant
        for (VocMileageAddEnum value : values()) {
            stringToEnum.put(value.code, value);
        }
    }
   
    /**
     * 枚举代码
     */
    private Integer code;
    /**
     * 枚举显示
     */
    private String display;
    
    /**
            * 字段校验规则
     */
    private ExcelColumnRule rule;
    
    private VocMileageAddEnum(Integer code, String display, ExcelColumnRule rule) {
        this.code = code;
        this.display = display;
        this.rule = rule;
    }
    
    
    @Override
    public Integer code() {
        return this.code;
    }

    @Override
    public String display() {
         return this.display;
    }

    public ExcelColumnRule rule() {
        return this.rule;
    }
    
     // Returns WinStatus for string, or null if string is invalid
    public static VocMileageAddEnum fromCode(Integer code) {
        return stringToEnum.get(code);
    }

    @Override
    public String toString() {
        return this.display;
    }
}
