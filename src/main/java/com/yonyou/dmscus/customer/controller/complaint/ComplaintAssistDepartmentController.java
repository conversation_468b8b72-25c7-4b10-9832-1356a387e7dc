package com.yonyou.dmscus.customer.controller.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAssistDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAssistDepartmentDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistDepartmentPO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistPO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintAssistDepartmentService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-04-15
 */
@Api(value = "/complaintAssistDepartment", tags = {"ComplaintAssistDepartmentController"})
@RestController
@RequestMapping("/complaintAssistDepartment")
public class ComplaintAssistDepartmentController extends BaseController {

    @Autowired
    ComplaintAssistDepartmentService complaintAssistDepartmentService;

    /**
     * 分页查询数据
     *
     * @param appId                系统ID
     * @param ownerCode            所有者代码
     * @param ownerParCode         所有者的父组织代码
     * @param orgId                组织ID
     * @param id                   主键ID
     * @param complaintInfoId      投诉信息表主键ID
     * @param object               跟进对象 区域经理、经销商、CCM、客服中心
     * @param followTime           分配时间
     * @param follower             分配人
     * @param assistDepartment     协助部门
     * @param assistDepartmentName 协助部门名称
     * @param assistDealerCode     协助部门
     * @param assistDealerName     协助部门名称
     * @param hopeReplyTime        希望回复时间
     * @param assistExplain        需协助说明
     * @param isReply              是否回复
     * @param replyTime            回复时间
     * @param isFinish             是否完成
     * @param finishTime           完成时间
     * @param isRead               是否已读 1 已读 0 未读
     * @param dataSources          数据来源
     * @param isDeleted            是否删除
     * @param isValid              是否有效
     * @param createdAt            创建时间
     * @param updatedAt            更新时间
     * @param currentPage          页数
     * @param pageSize             分页大小
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "complaintInfoId", value = "投诉信息表主键ID"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "object", value = "跟进对象 区域经理、经销商、CCM、客服中心"),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "followTime", value = "分配时间"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "follower", value = "分配人"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "assistDepartment", value = "协助部门"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "assistDepartmentName", value = "协助部门名称"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "assistDealerCode", value = "协助部门"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "assistDealerName", value = "协助部门名称"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "hopeReplyTime", value = "希望回复时间"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "assistExplain", value = "需协助说明"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isReply", value = "是否回复"),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "replyTime", value = "回复时间"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isFinish", value = "是否完成"),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "finishTime", value = "完成时间"),
            @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isRead", value = "是否已读 1 已读 0 未读"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = "数据来源"),
            @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "createdAt", value = "创建时间"),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "updatedAt", value = "更新时间"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
    })
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
    @GetMapping
    public IPage<ComplaintAssistDepartmentDTO> getByPage(
            @RequestParam(value = "appId", required = false) String appId,
            @RequestParam(value = "ownerCode", required = false) String ownerCode,
            @RequestParam(value = "ownerParCode", required = false) String ownerParCode,
            @RequestParam(value = "orgId", required = false) Integer orgId,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "complaintInfoId", required = false) Long complaintInfoId,
            @RequestParam(value = "object", required = false) String object,
            @RequestParam(value = "followTime", required = false) Date followTime,
            @RequestParam(value = "follower", required = false) String follower,
            @RequestParam(value = "assistDepartment", required = false) String assistDepartment,
            @RequestParam(value = "assistDepartmentName", required = false) String assistDepartmentName,
            @RequestParam(value = "assistDealerCode", required = false) String assistDealerCode,
            @RequestParam(value = "assistDealerName", required = false) String assistDealerName,
            @RequestParam(value = "hopeReplyTime", required = false) Integer hopeReplyTime,
            @RequestParam(value = "assistExplain", required = false) String assistExplain,
            @RequestParam(value = "isReply", required = false) Integer isReply,
            @RequestParam(value = "replyTime", required = false) Date replyTime,
            @RequestParam(value = "isFinish", required = false) Integer isFinish,
            @RequestParam(value = "finishTime", required = false) Date finishTime,
            @RequestParam(value = "isRead", required = false) Boolean isRead,
            @RequestParam(value = "dataSources", required = false) Integer dataSources,
            @RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
            @RequestParam(value = "isValid", required = false) Integer isValid,
            @RequestParam(value = "createdAt", required = false) Date createdAt,
            @RequestParam(value = "updatedAt", required = false) Date updatedAt,
            @RequestParam("currentPage") int currentPage,
            @RequestParam("pageSize") int pageSize) {
        Page<ComplaintAssistDepartmentPO> page = new Page(currentPage, pageSize);

        ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO = new ComplaintAssistDepartmentDTO();
        complaintAssistDepartmentDTO.setAppId(appId);
        complaintAssistDepartmentDTO.setOwnerCode(ownerCode);
        complaintAssistDepartmentDTO.setOwnerParCode(ownerParCode);
        complaintAssistDepartmentDTO.setOrgId(orgId);
        complaintAssistDepartmentDTO.setId(id);
        complaintAssistDepartmentDTO.setComplaintInfoId(complaintInfoId);
        complaintAssistDepartmentDTO.setObject(object);
        complaintAssistDepartmentDTO.setFollowTime(followTime);
        complaintAssistDepartmentDTO.setFollower(follower);
        complaintAssistDepartmentDTO.setAssistDepartment(assistDepartment);
        complaintAssistDepartmentDTO.setAssistDepartmentName(assistDepartmentName);
        complaintAssistDepartmentDTO.setAssistDealerCode(assistDealerCode);
        complaintAssistDepartmentDTO.setAssistDealerName(assistDealerName);
        complaintAssistDepartmentDTO.setHopeReplyTime(hopeReplyTime);
        complaintAssistDepartmentDTO.setAssistExplain(assistExplain);
        complaintAssistDepartmentDTO.setIsReply(isReply);
        complaintAssistDepartmentDTO.setReplyTime(replyTime);
        complaintAssistDepartmentDTO.setIsFinish(isFinish);
        complaintAssistDepartmentDTO.setFinishTime(finishTime);
        complaintAssistDepartmentDTO.setIsRead(isRead);
        complaintAssistDepartmentDTO.setDataSources(dataSources);
        complaintAssistDepartmentDTO.setIsDeleted(isDeleted);
        complaintAssistDepartmentDTO.setIsValid(isValid);
        complaintAssistDepartmentDTO.setCreatedAt(createdAt);
        complaintAssistDepartmentDTO.setUpdatedAt(updatedAt);
        return complaintAssistDepartmentService.selectPageBysql(page, complaintAssistDepartmentDTO);
    }

    /**
     * 进行数据修改
     *
     * @param id 数据主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAssistDepartmentDTO
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
    })
    @ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
    @GetMapping(value = "/{id}")
    public ComplaintAssistDepartmentDTO getById(@PathVariable("id") Long id) {
        return complaintAssistDepartmentService.getById(id);
    }

    /**
     * 进行数据新增
     *
     * @param complaintAssistDepartmentDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintAssistDepartmentDTO", name = "complaintAssistDepartmentDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public int insert(@RequestBody ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO) {
        return complaintAssistDepartmentService.insert(complaintAssistDepartmentDTO);
    }

    /**
     * 进行数据修改
     *
     * @param id                           需要修改数据的ID
     * @param complaintAssistDepartmentDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintAssistDepartmentDTO", name = "complaintAssistDepartmentDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
    @PutMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.CREATED)
    public int update(@PathVariable("id") Long id, @RequestBody ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO) {
        return complaintAssistDepartmentService.update(id, complaintAssistDepartmentDTO);
    }

    /**
     * 根据id删除对象
     *
     * @param id 需要修改数据的ID
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
    })
    @ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public boolean deleteById(@PathVariable("id") Long id) {
        complaintAssistDepartmentService.deleteById(id);
        return true;
    }

    /**
     * 进行数据新增
     *
     * @param complaintAssistDepartmentDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-03-25
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintAssistDepartmentDTO", name = "complaintAssistDepartmentDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
    @RequestMapping(value = "/insertAssist", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertAssist(@RequestBody ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO) {
        complaintAssistDepartmentDTO.setFollower(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
        ComplaintAssistDTO complaintAssistDTO = new ComplaintAssistDTO();
        complaintAssistDepartmentDTO.setFollowerName(FrameworkUtil.getLoginInfo().getUserName());
        complaintAssistDepartmentDTO.setFollowTime(new Date());
        complaintAssistDepartmentDTO.setIsValid(10041001);
        long id = complaintAssistDepartmentDTO.getComplaintInfoId();
        int row = 0;
        if (complaintAssistDepartmentDTO.getAssistDealerCode() != null) {
            complaintAssistDepartmentService.updatestatus(id);
            complaintAssistDTO.setId(id);
            complaintAssistDTO.setAssistDealerCode(complaintAssistDepartmentDTO.getAssistDealerCode());
            List<ComplaintAssistDepartmentDTO> list = complaintAssistDepartmentService.selectAssistList(complaintAssistDTO);
            if (list.size() != 0) {
                row = complaintAssistDepartmentService.update(list.get(0).getId(), complaintAssistDepartmentDTO);

            } else {
                row = complaintAssistDepartmentService.insert(complaintAssistDepartmentDTO);
            }
        }
        if (complaintAssistDepartmentDTO.getAssistDepartment() != null) {
            complaintAssistDepartmentService.updatestatus1(id);
            complaintAssistDTO = new ComplaintAssistDTO();
            complaintAssistDTO.setId(id);
            complaintAssistDTO.setAssistDepartment(complaintAssistDepartmentDTO.getAssistDepartment());
            List<ComplaintAssistDepartmentDTO> list = complaintAssistDepartmentService.selectAssistList(complaintAssistDTO);
            if (list.size() != 0) {
                row = complaintAssistDepartmentService.update(list.get(0).getId(), complaintAssistDepartmentDTO);
                if(complaintAssistDepartmentDTO.getHopeReplyTime()!=null){
                    if(complaintAssistDepartmentDTO.getHopeReplyTime()!=82701004 & complaintAssistDepartmentDTO.getHopeReplyTime()!=82701005){
                        //推送消息
                        complaintAssistDepartmentService.pushMessage(complaintAssistDepartmentDTO);
                    }
                }

            } else {
                row = complaintAssistDepartmentService.insert(complaintAssistDepartmentDTO);
                if(complaintAssistDepartmentDTO.getHopeReplyTime()!=null){
                    if(complaintAssistDepartmentDTO.getHopeReplyTime()!=82701004 & complaintAssistDepartmentDTO.getHopeReplyTime()!=82701005){
                        //推送消息
                        complaintAssistDepartmentService.pushMessage(complaintAssistDepartmentDTO);
                    }
                }

            }
        }

        return row;
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "complaintInfoId", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @RequestMapping(value = "/selectAssist", method = RequestMethod.GET)
    public IPage<ComplaintAssistDTO> selectAssist(
            @RequestParam(value = "complaintInfoId", required = false) Long complaintInfoId,
            @RequestParam("currentPage") int currentPage,
            @RequestParam("pageSize") int pageSize) {
        Page<ComplaintAssistPO> page = new Page(currentPage, pageSize);
        ComplaintAssistDTO complaintAssistDTO = new ComplaintAssistDTO();
        complaintAssistDTO.setComplaintInfoId(complaintInfoId);
        return complaintAssistDepartmentService.selectAssist(page, complaintAssistDTO);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "complaintInfoId", value = "")
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @RequestMapping(value = "/selectAssistByid", method = RequestMethod.GET)
    public String selectAssistByid(
            @RequestParam(value = "complaintInfoId", required = false) Long complaintInfoId) {
        boolean res = true;
        ComplaintAssistDTO complaintAssistDTO = new ComplaintAssistDTO();
        complaintAssistDTO.setComplaintInfoId(complaintInfoId);
        List<ComplaintAssistDTO> code = complaintAssistDepartmentService.selectAssistByid(complaintAssistDTO);
        String dealerCode = code.get(0).getAssistDealerCode();
        if (dealerCode == null || !dealerCode.equals(FrameworkUtil.getLoginInfo().getOwnerCode())) {
            res = false;
        }
        return Boolean.toString(res);
    }

    /**
     * 质量部查询协助
     *
     * @param complaintInfoId
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "complaintInfoId", value = "")
    })
    @ApiOperation(value = "质量部查询协助", notes = "质量部查询协助", httpMethod = "GET")
    @RequestMapping(value = "/selectAssistValidByid", method = RequestMethod.GET)
    public String selectAssistValidByid(
            @RequestParam(value = "complaintInfoId", required = false) Long complaintInfoId) {
        ComplaintAssistDTO complaintAssistDTO = new ComplaintAssistDTO();
        complaintAssistDTO.setComplaintInfoId(complaintInfoId);
        complaintAssistDTO.setAssistDepartment("324");
        boolean flag = true;
        List<ComplaintAssistDTO> code = complaintAssistDepartmentService.selectAssistValidByid(complaintAssistDTO);
        if (code.size() == 0) {
            flag = false;
        }
        return Boolean.toString(flag);
    }


}