package com.yonyou.dmscus.customer.controller.goodwill;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillNoticeInvoiceInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillStampsDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO;
import com.yonyou.dmscus.customer.enums.CacheKeyEnum;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillStampsService;
import com.yonyou.dmscus.customer.utils.Utills;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2020-05-23
 */
@Api(value = "/goodwillStamps", tags = {"GoodwillStampsController"})
@RestController
@RequestMapping("/goodwillStamps")
public class GoodwillStampsController {

	private final Logger logger = LoggerFactory.getLogger(GoodwillStampsController.class);

	@Autowired
	GoodwillStampsService goodwillStampsService;
	@Autowired
	protected RedissonClient redissonClient;

	/**
	 * 分页查询数据 亲善券充值明细
	 *
	 * @throws ParseException
	 * @since 2020-04-20
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "group", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "smallAreaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "dataType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "veOwnerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isChargerd", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeStartdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "rechargeStartdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "rechargeEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询数据 亲善券充值明细", notes = "分页查询数据 亲善券充值明细", httpMethod = "GET")
	@GetMapping
	public IPage<GoodwillStampsDTO> getByPage(@RequestParam(value = "group", required = false) String group,
			@RequestParam(value = "areaManage", required = false) String areaManage,
			@RequestParam(value = "smallAreaManage", required = false) String smallAreaManage,
			@RequestParam(value = "auditName", required = false) String auditName,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "dataType", required = false) Integer dataType,
			@RequestParam(value = "goodwillApplyId", required = false) Long goodwillApplyId,
			@RequestParam(value = "veOwnerName", required = false) String veOwnerName,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "isChargerd", required = false) Integer isChargerd,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "noticeStartdAt", required = false) String noticeStartdAt,
			@RequestParam(value = "noticeEndAt", required = false) String noticeEndAt,
			@RequestParam(value = "rechargeStartdAt", required = false) String rechargeStartdAt,
			@RequestParam(value = "rechargeEndAt", required = false) String rechargeEndAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillStampsDTO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillStampsDTO goodwillStampsDTO = new GoodwillStampsDTO();
		goodwillStampsDTO.setGroup(group);
		goodwillStampsDTO.setAreaManage(areaManage);
		goodwillStampsDTO.setSmallArea(smallAreaManage);
		goodwillStampsDTO.setAuditName1(Utills.getList(auditName));
		goodwillStampsDTO.setDataType(dataType);
		goodwillStampsDTO.setDealerCode(dealerCode);
		goodwillStampsDTO.setGoodwillApplyId(goodwillApplyId);
		goodwillStampsDTO.setApplyNo(applyNo);
		goodwillStampsDTO.setVeOwnerName(veOwnerName);
		goodwillStampsDTO.setIsChargerd(isChargerd);
		goodwillStampsDTO.setLicense(license);
		goodwillStampsDTO.setVin(vin);

		if (noticeStartdAt != null && noticeStartdAt != "") {
			goodwillStampsDTO.setNoticeStartdAt(sdf.parse(noticeStartdAt));
		}
		if (noticeEndAt != null && noticeEndAt != "") {
			goodwillStampsDTO.setNoticeEndAt(sdf.parse(noticeEndAt));
		}
		if (rechargeStartdAt != null && rechargeStartdAt != "") {
			goodwillStampsDTO.setRechargeStartdAt(sdf.parse(rechargeStartdAt));
		}
		if (rechargeEndAt != null && rechargeEndAt != "") {
			goodwillStampsDTO.setRechargeEndAt(sdf.parse(rechargeEndAt));
		}
		return goodwillStampsService.selectPageBysql(page, goodwillStampsDTO);
	}

	/**
	 * 导出数据 亲善券充值明细
	 * 
	 * @throws ParseException
	 * @since 2020-04-20
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "group", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "smallAreaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "dataType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "veOwnerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isChargerd", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeStartdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "rechargeStartdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "rechargeEndAt", value = "")
	})
	@ApiOperation(value = "导出数据 亲善券充值明细", notes = "导出数据 亲善券充值明细", httpMethod = "GET")
	@RequestMapping(value = "/exportGoodwillStamps", method = RequestMethod.GET)
	public List<GoodwillStampsDTO> getByPageList(@RequestParam(value = "group", required = false) String group,
			@RequestParam(value = "areaManage", required = false) String areaManage,
			@RequestParam(value = "smallAreaManage", required = false) String smallAreaManage,
			@RequestParam(value = "auditName", required = false) String auditName,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "dataType", required = false) Integer dataType,
			@RequestParam(value = "goodwillApplyId", required = false) Long goodwillApplyId,
			@RequestParam(value = "veOwnerName", required = false) String veOwnerName,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "isChargerd", required = false) Integer isChargerd,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "noticeStartdAt", required = false) String noticeStartdAt,
			@RequestParam(value = "noticeEndAt", required = false) String noticeEndAt,
			@RequestParam(value = "rechargeStartdAt", required = false) String rechargeStartdAt,
			@RequestParam(value = "rechargeEndAt", required = false) String rechargeEndAt) throws ParseException {
		// Page<GoodwillStampsDTO> page = new Page<GoodwillStampsDTO>();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		GoodwillStampsDTO goodwillStampsDTO = new GoodwillStampsDTO();
		goodwillStampsDTO.setGroup(group);
		goodwillStampsDTO.setAreaManage(areaManage);
		goodwillStampsDTO.setSmallArea(smallAreaManage);
		goodwillStampsDTO.setAuditName1(Utills.getList(auditName));
		goodwillStampsDTO.setDataType(dataType);
		goodwillStampsDTO.setDealerCode(dealerCode);
		goodwillStampsDTO.setGoodwillApplyId(goodwillApplyId);
		goodwillStampsDTO.setApplyNo(applyNo);
		goodwillStampsDTO.setVeOwnerName(veOwnerName);
		goodwillStampsDTO.setIsChargerd(isChargerd);
		goodwillStampsDTO.setLicense(license);
		goodwillStampsDTO.setVin(vin);

		if (noticeStartdAt != null && noticeStartdAt != "") {
			goodwillStampsDTO.setNoticeStartdAt(sdf.parse(noticeStartdAt));
		}
		if (noticeEndAt != null && noticeEndAt != "") {
			goodwillStampsDTO.setNoticeEndAt(sdf.parse(noticeEndAt));
		}
		if (rechargeStartdAt != null && rechargeStartdAt != "") {
			goodwillStampsDTO.setRechargeStartdAt(sdf.parse(rechargeStartdAt));
		}
		if (rechargeEndAt != null && rechargeEndAt != "") {
			goodwillStampsDTO.setRechargeEndAt(sdf.parse(rechargeEndAt));
		}
		return goodwillStampsService.selectPageByList(goodwillStampsDTO);
	}

	/**
	 * 分页查询数据 亲善券消费明细
	 *
	 * @throws ParseException
	 * @since 2020-04-20
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "group", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "smallAreaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "veOwnerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isChargerd", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeStartdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceStartdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "consumeStartdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "consumeEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询数据 亲善券消费明细", notes = "分页查询数据 亲善券消费明细", httpMethod = "GET")
	@RequestMapping(value = "/getGoodwillStamps", method = RequestMethod.GET)
	public IPage<GoodwillStampsDTO> getGoodwillStamps(@RequestParam(value = "group", required = false) String group,
			@RequestParam(value = "areaManage", required = false) String areaManage,
			@RequestParam(value = "smallAreaManage", required = false) String smallAreaManage,
			@RequestParam(value = "auditName", required = false) String auditName,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "goodwillApplyId", required = false) Long goodwillApplyId,
			@RequestParam(value = "veOwnerName", required = false) String veOwnerName,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "isChargerd", required = false) Integer isChargerd,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "invoiceNo", required = false) String invoiceNo,
			@RequestParam(value = "noticeStartdAt", required = false) String noticeStartdAt,
			@RequestParam(value = "noticeEndAt", required = false) String noticeEndAt,
			@RequestParam(value = "invoiceStartdAt", required = false) String invoiceStartdAt,
			@RequestParam(value = "invoiceEndAt", required = false) String invoiceEndAt,
			@RequestParam(value = "consumeStartdAt", required = false) String consumeStartdAt,
			@RequestParam(value = "consumeEndAt", required = false) String consumeEndAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		if(StringUtils.isBlank(consumeStartdAt)||StringUtils.isBlank(consumeEndAt)) {
			throw new ServiceBizException("消费时间不能为空");
		}
		Page<GoodwillStampsDTO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillStampsDTO goodwillStampsDTO = new GoodwillStampsDTO();
		goodwillStampsDTO.setGroup(group);
		goodwillStampsDTO.setAreaManage(areaManage);
		goodwillStampsDTO.setSmallArea(smallAreaManage);
		goodwillStampsDTO.setAuditName1(Utills.getList(auditName));
		goodwillStampsDTO.setDealerCode(dealerCode);
		goodwillStampsDTO.setGoodwillApplyId(goodwillApplyId);
		goodwillStampsDTO.setApplyNo(applyNo);
		goodwillStampsDTO.setVeOwnerName(veOwnerName);
		goodwillStampsDTO.setIsChargerd(isChargerd);
		goodwillStampsDTO.setLicense(license);
		goodwillStampsDTO.setVin(vin);
		goodwillStampsDTO.setInvoiceNumber(invoiceNo);

		if (noticeStartdAt != null && noticeStartdAt != "") {
			goodwillStampsDTO.setNoticeStartdAt(sdf.parse(noticeStartdAt));
		}
		if (noticeEndAt != null && noticeEndAt != "") {
			goodwillStampsDTO.setNoticeEndAt(sdf.parse(noticeEndAt));
		}
		if (invoiceStartdAt != null && invoiceStartdAt != "") {
			goodwillStampsDTO.setInvoiceStartdAt(sdf.parse(invoiceStartdAt));
		}
		if (invoiceEndAt != null && invoiceEndAt != "") {
			goodwillStampsDTO.setInvoiceEndAt(sdf.parse(invoiceEndAt));
		}
		if (consumeStartdAt != null && consumeStartdAt != "") {
			goodwillStampsDTO.setConsumeStartdAt(sdf.parse(consumeStartdAt));
		}
		if (consumeEndAt != null && consumeEndAt != "") {
			goodwillStampsDTO.setConsumeEndAt(sdf.parse(consumeEndAt));
		}
		return goodwillStampsService.selectPageByConsume(page, goodwillStampsDTO);
	}

	/**
	 * 导出代金券使用明细（厂端）
	 *
	 * @throws ParseException
	 * @since 2020-04-20
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "group", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "smallAreaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "veOwnerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isChargerd", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceNumber", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeStartdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceStartdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "consumeStartdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "consumeEndAt", value = "")
	})
	@ApiOperation(value = "导出代金券使用明细（厂端）", notes = "导出代金券使用明细（厂端）", httpMethod = "GET")
	@RequestMapping(value = "/exportOemConGoodwillStamps", method = RequestMethod.GET)
	public void exportOemConGoodwillStamps(
			@RequestParam(value = "group", required = false) String group,
			@RequestParam(value = "areaManage", required = false) String areaManage,
			@RequestParam(value = "smallAreaManage", required = false) String smallAreaManage,
			@RequestParam(value = "auditName", required = false) String auditName,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "goodwillApplyId", required = false) Long goodwillApplyId,
			@RequestParam(value = "veOwnerName", required = false) String veOwnerName,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "isChargerd", required = false) Integer isChargerd,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "invoiceNumber", required = false) String invoiceNumber,
			@RequestParam(value = "noticeStartdAt", required = false) String noticeStartdAt,
			@RequestParam(value = "noticeEndAt", required = false) String noticeEndAt,
			@RequestParam(value = "invoiceStartdAt", required = false) String invoiceStartdAt,
			@RequestParam(value = "invoiceEndAt", required = false) String invoiceEndAt,
			@RequestParam(value = "consumeStartdAt", required = false) String consumeStartdAt,
			@RequestParam(value = "consumeEndAt", required = false) String consumeEndAt) throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillStampsDTO goodwillStampsDTO = new GoodwillStampsDTO();
		goodwillStampsDTO.setGroup(group);
		goodwillStampsDTO.setAreaManage(areaManage);
		goodwillStampsDTO.setSmallArea(smallAreaManage);
		goodwillStampsDTO.setAuditName1(Utills.getList(auditName));
		goodwillStampsDTO.setDealerCode(dealerCode);
		goodwillStampsDTO.setGoodwillApplyId(goodwillApplyId);
		goodwillStampsDTO.setApplyNo(applyNo);
		goodwillStampsDTO.setVeOwnerName(veOwnerName);
		goodwillStampsDTO.setIsChargerd(isChargerd);
		goodwillStampsDTO.setLicense(license);
		goodwillStampsDTO.setVin(vin);
		goodwillStampsDTO.setInvoiceNumber(invoiceNumber);
		if(StringUtils.isBlank(consumeStartdAt)||StringUtils.isBlank(consumeEndAt)) {
			throw new ServiceBizException("消费时间不能为空");
		}
		if (noticeStartdAt != null && noticeStartdAt != "") {
			goodwillStampsDTO.setNoticeStartdAt(sdf.parse(noticeStartdAt));
		}
		if (noticeEndAt != null && noticeEndAt != "") {
			goodwillStampsDTO.setNoticeEndAt(sdf.parse(noticeEndAt));
		}
		if (invoiceStartdAt != null && invoiceStartdAt != "") {
			goodwillStampsDTO.setInvoiceStartdAt(sdf.parse(invoiceStartdAt));
		}
		if (invoiceEndAt != null && invoiceEndAt != "") {
			goodwillStampsDTO.setInvoiceEndAt(sdf.parse(invoiceEndAt));
		}
		if (consumeStartdAt != null && consumeStartdAt != "") {
			goodwillStampsDTO.setConsumeStartdAt(sdf.parse(consumeStartdAt));
		}
		if (consumeEndAt != null && consumeEndAt != "") {
			goodwillStampsDTO.setConsumeEndAt(sdf.parse(consumeEndAt));
		}
		goodwillStampsDTO.setDataType(CommonConstants.DATATYPE_FACTORY);  // 表示厂端
		goodwillStampsService.exportconGoodwillStamps(goodwillStampsDTO);
	}

	/**
	 * 导出代金券使用明细（店端）
	 *
	 * @throws ParseException
	 * @since 2020-04-20
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "group", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "smallAreaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "veOwnerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isChargerd", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceNumber", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeStartdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceStartdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "consumeStartdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "consumeEndAt", value = "")
	})
	@ApiOperation(value = "导出代金券使用明细（店端）", notes = "导出代金券使用明细（店端）", httpMethod = "GET")
	@RequestMapping(value = "/exportDealerConGoodwillStamps", method = RequestMethod.GET)
	public void exportDealerConGoodwillStamps(
			@RequestParam(value = "group", required = false) String group,
			@RequestParam(value = "areaManage", required = false) String areaManage,
			@RequestParam(value = "smallAreaManage", required = false) String smallAreaManage,
			@RequestParam(value = "auditName", required = false) String auditName,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "goodwillApplyId", required = false) Long goodwillApplyId,
			@RequestParam(value = "veOwnerName", required = false) String veOwnerName,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "isChargerd", required = false) Integer isChargerd,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "invoiceNumber", required = false) String invoiceNumber,
			@RequestParam(value = "noticeStartdAt", required = false) String noticeStartdAt,
			@RequestParam(value = "noticeEndAt", required = false) String noticeEndAt,
			@RequestParam(value = "invoiceStartdAt", required = false) String invoiceStartdAt,
			@RequestParam(value = "invoiceEndAt", required = false) String invoiceEndAt,
			@RequestParam(value = "consumeStartdAt", required = false) String consumeStartdAt,
			@RequestParam(value = "consumeEndAt", required = false) String consumeEndAt) throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillStampsDTO goodwillStampsDTO = new GoodwillStampsDTO();
		goodwillStampsDTO.setGroup(group);
		goodwillStampsDTO.setAreaManage(areaManage);
		goodwillStampsDTO.setSmallArea(smallAreaManage);
		goodwillStampsDTO.setAuditName1(Utills.getList(auditName));
		goodwillStampsDTO.setDealerCode(dealerCode);
		goodwillStampsDTO.setGoodwillApplyId(goodwillApplyId);
		goodwillStampsDTO.setApplyNo(applyNo);
		goodwillStampsDTO.setVeOwnerName(veOwnerName);
		goodwillStampsDTO.setIsChargerd(isChargerd);
		goodwillStampsDTO.setLicense(license);
		goodwillStampsDTO.setVin(vin);
		goodwillStampsDTO.setInvoiceNumber(invoiceNumber);
		if(StringUtils.isBlank(consumeStartdAt)||StringUtils.isBlank(consumeEndAt)) {
			throw new ServiceBizException("消费时间不能为空");
		}
		if (noticeStartdAt != null && noticeStartdAt != "") {
			goodwillStampsDTO.setNoticeStartdAt(sdf.parse(noticeStartdAt));
		}
		if (noticeEndAt != null && noticeEndAt != "") {
			goodwillStampsDTO.setNoticeEndAt(sdf.parse(noticeEndAt));
		}
		if (invoiceStartdAt != null && invoiceStartdAt != "") {
			goodwillStampsDTO.setInvoiceStartdAt(sdf.parse(invoiceStartdAt));
		}
		if (invoiceEndAt != null && invoiceEndAt != "") {
			goodwillStampsDTO.setInvoiceEndAt(sdf.parse(invoiceEndAt));
		}
		if (consumeStartdAt != null && consumeStartdAt != "") {
			goodwillStampsDTO.setConsumeStartdAt(sdf.parse(consumeStartdAt));
		}
		if (consumeEndAt != null && consumeEndAt != "") {
			goodwillStampsDTO.setConsumeEndAt(sdf.parse(consumeEndAt));
		}
		goodwillStampsDTO.setDataType(CommonConstants.DATATYPE_STORE);  // 表示店端
		goodwillStampsService.exportconGoodwillStamps(goodwillStampsDTO);
	}

	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "group", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "smallAreaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "veOwnerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isChargerd", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceNumber", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeStartdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceStartdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "consumeStartdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "consumeEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "下载中心回调代金券使用明细", notes = "", httpMethod = "GET")
	@GetMapping(value = "/downLoadConGoodwillStamps")
	public List<GoodwillStampsDTO> downLoadConGoodwillStamps(@RequestParam(value = "group", required = false) String group,
															 @RequestParam(value = "areaManage", required = false) String areaManage,
															 @RequestParam(value = "smallAreaManage", required = false) String smallAreaManage,
															 @RequestParam(value = "auditName", required = false) String auditName,
															 @RequestParam(value = "dealerCode", required = false) String dealerCode,
															 @RequestParam(value = "goodwillApplyId", required = false) Long goodwillApplyId,
															 @RequestParam(value = "veOwnerName", required = false) String veOwnerName,
															 @RequestParam(value = "applyNo", required = false) String applyNo,
															 @RequestParam(value = "isChargerd", required = false) Integer isChargerd,
															 @RequestParam(value = "license", required = false) String license,
															 @RequestParam(value = "vin", required = false) String vin,
															 @RequestParam(value = "invoiceNumber", required = false) String invoiceNumber,
															 @RequestParam(value = "noticeStartdAt", required = false) String noticeStartdAt,
															 @RequestParam(value = "noticeEndAt", required = false) String noticeEndAt,
															 @RequestParam(value = "invoiceStartdAt", required = false) String invoiceStartdAt,
															 @RequestParam(value = "invoiceEndAt", required = false) String invoiceEndAt,
															 @RequestParam(value = "consumeStartdAt", required = false) String consumeStartdAt,
															 @RequestParam(value = "consumeEndAt", required = false) String consumeEndAt,
                                                             @RequestParam("currentPage") int currentPage,
                                                             @RequestParam("pageSize") int pageSize) throws ParseException {

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillStampsDTO goodwillStampsDTO = new GoodwillStampsDTO();
		goodwillStampsDTO.setGroup(group);
		goodwillStampsDTO.setAreaManage(areaManage);
		goodwillStampsDTO.setSmallArea(smallAreaManage);
		goodwillStampsDTO.setAuditName1(Utills.getList(auditName));
		goodwillStampsDTO.setDealerCode(dealerCode);
		goodwillStampsDTO.setGoodwillApplyId(goodwillApplyId);
		goodwillStampsDTO.setApplyNo(applyNo);
		goodwillStampsDTO.setVeOwnerName(veOwnerName);
		goodwillStampsDTO.setIsChargerd(isChargerd);
		goodwillStampsDTO.setLicense(license);
		goodwillStampsDTO.setVin(vin);
		goodwillStampsDTO.setInvoiceNumber(invoiceNumber);
		goodwillStampsDTO.setCurrentPage(currentPage);
		goodwillStampsDTO.setPageSize(pageSize);

		if(StringUtils.isBlank(consumeStartdAt)||StringUtils.isBlank(consumeEndAt)) {
			throw new ServiceBizException("消费时间不能为空");
		}
		if (noticeStartdAt != null && noticeStartdAt != "") {
			goodwillStampsDTO.setNoticeStartdAt(sdf.parse(noticeStartdAt));
		}
		if (noticeEndAt != null && noticeEndAt != "") {
			goodwillStampsDTO.setNoticeEndAt(sdf.parse(noticeEndAt));
		}
		if (invoiceStartdAt != null && invoiceStartdAt != "") {
			goodwillStampsDTO.setInvoiceStartdAt(sdf.parse(invoiceStartdAt));
		}
		if (invoiceEndAt != null && invoiceEndAt != "") {
			goodwillStampsDTO.setInvoiceEndAt(sdf.parse(invoiceEndAt));
		}
		if (consumeStartdAt != null && consumeStartdAt != "") {
			goodwillStampsDTO.setConsumeStartdAt(sdf.parse(consumeStartdAt));
		}
		if (consumeEndAt != null && consumeEndAt != "") {
			goodwillStampsDTO.setConsumeEndAt(sdf.parse(consumeEndAt));
		}

		logger.info("/goodwillStamps/downLoadConGoodwillStamps=======>入参goodwillStampsDTO:【{}】", JSONUtil.toJsonStr(goodwillStampsDTO));
        Page<GoodwillStampsDTO> page = new Page<>();
        page.setCurrent(currentPage);
		page.setSize(pageSize);
		return goodwillStampsService.downloadConGoodwillStamps(page, goodwillStampsDTO);

	}

	/**
	 * 保存亲善代金券通知开票信息
	 *
	 * @param goodwillNoticeInvoiceInfoDto
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillNoticeInvoiceInfoDTO", name = "goodwillNoticeInvoiceInfoDto", value = "", required = true)
	})
	@ApiOperation(value = "保存亲善代金券通知开票信息            需要保存的DTO", notes = "保存亲善代金券通知开票信息            需要保存的DTO", httpMethod = "POST")
	@PostMapping(value = "/editNoticeInvoiceInfo")
	@ResponseStatus(HttpStatus.CREATED)
	public int editNoticeInvoiceInfo(@RequestBody GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto) {
		if (StringUtils.isBlank(goodwillNoticeInvoiceInfoDto.getInvoiceId())) {
			throw new ServiceBizException("开票id不能为空");
		}
		String consumeId = goodwillNoticeInvoiceInfoDto.getConsumeId();
		String key = consumeId == null ? goodwillNoticeInvoiceInfoDto.getInvoiceId() : consumeId;
		logger.info("editNoticeInvoiceInfo,key:{}", consumeId);
		String lockName = CacheKeyEnum.CUSTOMER_GOOD_WILL_NOTICE_KEY.getKey(key);
		int waitTime = 10;
		int leaseTime = 30;
		TimeUnit timeUnit = TimeUnit.SECONDS;
		RLock lock = this.redissonClient.getLock(lockName);
		int row = 0;
		try {
			if (!lock.tryLock(waitTime, leaseTime, timeUnit)) {
				logger.error("获取锁失败,lockName:{}", lockName);
				throw new DALException("当前系统繁忙，请勿重复操作");
			}
			goodwillStampsService.editNoticeInvoiceInfo(goodwillNoticeInvoiceInfoDto);
		} catch (DALException e) {
			logger.error("editNoticeInvoiceInfo,重复提交", e);
			throw e;
		} catch (Exception e) {
			logger.error("editNoticeInvoiceInfo,Exception", e);
			throw new ServiceBizException(e.getMessage());
		} finally {
			logger.info("editNoticeInvoiceInfo,释放锁");
			if (lock.isLocked()) {
				lock.unlock();
			}
		}
		return row;
	}

	/**
	 * 分页查询维修记录——分页查询工单信息 接口数据
	 * 
	 * @param role
	 *            角色
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-04-22
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询维修记录——分页查询工单信息 接口数据             角色            页数            分页大小", notes = "分页查询维修记录——分页查询工单信息 接口数据             角色            页数            分页大小", httpMethod = "GET")
	@GetMapping(value = "/queryInvoiceHistory")
	public IPage<List> queryInvoiceHistory(
			@RequestParam(value = "goodwillApplyId", required = true) Long goodwillApplyId,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyInfoPO> page = new Page(currentPage, pageSize);
		List<Map> list = new ArrayList<>();
		return goodwillStampsService.queryInvoiceHistory(page, goodwillApplyId);
	}

}