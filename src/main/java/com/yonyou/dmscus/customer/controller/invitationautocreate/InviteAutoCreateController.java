package com.yonyou.dmscus.customer.controller.invitationautocreate;

import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.invitationautocreate.InviteVehicleDoTaskService;
import com.yonyou.dmscus.customer.service.invitationautocreate.InviteVehicleTaskService;
import com.yonyou.dmscus.customer.service.invitationautocreate.VocManageService;
import com.yonyou.dmscus.customer.service.oss.OssBusProService;
import com.yonyou.dmscus.customer.service.oss.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-04-29
 */
@Api(value = "/inviteAutoCreate", tags = {"InviteAutoCreateController"})
@RestController
@RequestMapping("/inviteAutoCreate")
public class InviteAutoCreateController {
    //日志对象
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    InviteVehicleTaskService inviteVehicleTaskService;
    @Autowired
    InviteVehicleDoTaskService inviteVehicleDoTaskService;
    @Autowired
    VocManageService vocManageService;
    @Autowired
    OssService ossService;
    @Autowired
    OssBusProService ossbUSproService;
    @Autowired
    InviteVehicleRecordService inviteVehicleRecordService;



    /**
     * 创建邀约任务
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "")
    })
    @ApiOperation(value = "创建邀约任务", notes = "创建邀约任务", httpMethod = "GET")
    @GetMapping("/createInviteTask1")
    public int inviteAutoCreate1(@RequestParam(value = "createDate", required = false) String createDate){
        logger.info("createInviteTask1:开始");
        return inviteVehicleTaskService.inviteAutoCreateTask1(createDate);
    }

    /**
     * 创建邀约任务
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "")
    })
    @ApiOperation(value = "创建邀约任务", notes = "创建邀约任务", httpMethod = "GET")
    @GetMapping("/createInviteTask2")
    public int inviteAutoCreate2(@RequestParam(value = "createDate", required = false) String createDate){
        logger.info("inviteAutoCreate2:开始");
        return inviteVehicleTaskService.inviteAutoCreateTask2(createDate);
    }

    /**
     * 创建邀约任务(优化)
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "roNo", value = "")
    })
    @ApiOperation(value = "创建邀约任务(优化)", notes = "创建邀约任务(优化)", httpMethod = "GET")
    @GetMapping("/addInviteTask")
    public void addInviteTask(@RequestParam(value = "createDate", required = false) String createDate,
                             @RequestParam(value = "ownerCode", required = false) String ownerCode,
                             @RequestParam(value = "roNo", required = false) String roNo){
        inviteVehicleDoTaskService.inviteAutoCreateTask(createDate, ownerCode, roNo);
    }

    /**
     * 创建邀约任务(数据补偿接口)
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "", required = true)
    })
    @ApiOperation(value = "创建邀约任务(数据补偿接口)", notes = "创建邀约任务(数据补偿接口)", httpMethod = "GET")
    @GetMapping("/inviteAutoCreateCompensate")
    public int inviteAutoCreateCompensate(@RequestParam("createDate") String createDate,
                                          @RequestParam("vin") String vin,
                                          @RequestParam("dealerCode") String dealerCode){
        return inviteVehicleTaskService.inviteAutoCreateTaskCompensate(createDate, vin, dealerCode);
    }

    /**
     * 关闭保修线索
     * @param createDate
     * @return
    @ApiOperation(value = "", notes = "", httpMethod = "")
    @GetMapping("/closeGuarantee")
    public int closeGuarantee(@RequestParam(value = "createDate", required = false) String createDate){
        return inviteVehicleTaskService.closeGuarantee(createDate);
    }
     */

    /**
     * 创建轮胎任务
     * @param createDate
     * @return
     @ApiOperation(value = "", notes = "", httpMethod = "")
    @GetMapping("/createTyreTask")
    public int createTyreTask(@RequestParam(value = "createDate", required = false) String createDate){
        return inviteVehicleTaskService.createTyreTask(createDate);
    }
     */


    /**
     * 初始化数据
     * @param createDate
     * @param days
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "days", value = "", required = true)
    })
    @ApiOperation(value = "初始化数据", notes = "初始化数据", httpMethod = "GET")
    @GetMapping("/initInviteInfo")
    public int initInviteInfo(@RequestParam(value = "createDate") String createDate,
                                 @RequestParam(value = "days") Integer days){
        logger.info("initInviteInfo:开始");
        return inviteVehicleTaskService.initInviteInfo(createDate,days);
    }


    /**
     * 创建QB邀约任务
     * @return
     */
    @ApiOperation(value = "创建QB邀约任务", notes = "创建QB邀约任务", httpMethod = "GET")
    @GetMapping("/inviteAutoCreateQb")
    public int inviteAutoCreateQb(){
        logger.info("inviteAutoCreateQb:开始");
        return inviteVehicleTaskService.inviteAutoCreateQb();
    }


    /**
     * 计算日均行驶里程
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "page", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "number", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "partitionSize", value = "")
    })
    @ApiOperation(value = "计算日均行驶里程", notes = "计算日均行驶里程", httpMethod = "GET")
    @GetMapping("/computeDailyAverageMileage")
    public int computeDailyAverageMileage(@RequestParam(value = "createDate", required = false) String createDate,
                                          @RequestParam(value = "page", required = false) Integer page,
                                          @RequestParam(value = "number", required = false) Integer number,
                                          @RequestParam(value = "partitionSize", required = false) Integer partitionSize){
        logger.info("computeDailyAverageMileage:开始");
        return inviteVehicleTaskService.partitionGetNotVocVeh(createDate,page,number,partitionSize);
    }

    /**
     * 计算日均行驶里程
     * @return
     */
    @ApiOperation(value = "计算日均行驶里程", notes = "计算日均行驶里程", httpMethod = "GET")
    @GetMapping("/computeDailyAverageMileageAll")
    public int computeDailyAverageMileageAll(){
        logger.info("computeDailyAverageMileageAll:开始");
        return inviteVehicleTaskService.computeDailyAverageMileageAll();
    }


    /**
     * 计算voc日均行驶里程/建议进场时间
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "page", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "number", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "partitionSize", value = "")
    })
    @ApiOperation(value = "计算voc日均行驶里程/建议进场时间", notes = "计算voc日均行驶里程/建议进场时间", httpMethod = "GET")
    @GetMapping("/computeDailyAverageMileageForVoc")
    public int computeDailyAverageMileageForVoc(@RequestParam(value = "createDate", required = false) String createDate,
                                                @RequestParam(value = "page", required = false) Integer page,
                                                @RequestParam(value = "number", required = false) Integer number,
                                                @RequestParam(value = "partitionSize", required = false) Integer partitionSize){
        logger.info("computeDailyAverageMileageForVoc:开始");
        return vocManageService.partitionGetAllVocVeh(createDate, page,number,partitionSize);
    }

    /**
     * 计算非voc日均行驶里程/建议进场时间
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "")
    })
    @ApiOperation(value = "计算非voc日均行驶里程/建议进场时间", notes = "计算非voc日均行驶里程/建议进场时间", httpMethod = "GET")
    @GetMapping("/computeDailyAverageMileageForNotVoc")
    public int computeDailyAverageMileageForNotVoc(@RequestParam(value = "createDate", required = false) String
                                                        createDate){
        logger.info("computeDailyAverageMileageForVoc:开始");
        return inviteVehicleTaskService.partitionGetNotVocVeh(createDate, null,null,null);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "LocalDate", name = "startDate", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "LocalDate", name = "endDate", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/computeDaMiForVocWithDateRange")
    public int computeDaMiForVocWithDateRange(
            @RequestParam @DateTimeFormat( iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat( iso = DateTimeFormat.ISO.DATE) LocalDate endDate
            ){
        logger.info("computeDaMiForVocWithDateRange:开始");
        return vocManageService.computeDaMiForVocWithDateRange( startDate, endDate);
    }

    /**
     * 根据邀约任务创建邀约线索
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "")
    })
    @ApiOperation(value = "根据邀约任务创建邀约线索", notes = "根据邀约任务创建邀约线索", httpMethod = "GET")
    @GetMapping("/createInviteByTask")
    public int createInviteByTask(@RequestParam(value = "createDate", required = false) String createDate){
        logger.info("createInviteByTask:开始");
        return inviteVehicleTaskService.createInviteByTask(createDate);
    }

    /**
     * 自动关闭线索，首保未下发任务
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "")
    })
    @ApiOperation(value = "自动关闭线索，首保未下发任务", notes = "自动关闭线索，首保未下发任务", httpMethod = "GET")
    @GetMapping("/closeInvite")
    public int closeInvite(@RequestParam(value = "createDate", required = false) String createDate){
        logger.info("closeInvite:开始");
        return inviteVehicleTaskService.closeInvite(createDate);
    }

    /**
     * 查询前一天voc数据
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "")
    })
    @ApiOperation(value = "查询前一天voc数据", notes = "查询前一天voc数据", httpMethod = "GET")
    @GetMapping("/updateVoc")
    public int updateVoc(@RequestParam(value = "createDate", required = false) String createDate){
        logger.info("updateVoc:开始");
        return vocManageService.updateVoc(createDate);
    }

    /**
     * 定保 保险丝规则处理
     * @return
     */
    @ApiOperation(value = "定保 保险丝规则处理", notes = "定保 保险丝规则处理", httpMethod = "GET")
    @GetMapping("/MaintainFuseRule")
    public int MaintainFuseRule(){
        return inviteVehicleTaskService.fuseRule();
    }

    /**
     * 线索完成，拆分，并合并其他线索
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "parentId", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isMain", value = "", required = true)
    })
    @ApiOperation(value = "线索完成，拆分，并合并其他线索", notes = "线索完成，拆分，并合并其他线索", httpMethod = "GET")
    @GetMapping("/mergeInvite")
    public int mergeInvite(@RequestParam(value = "id") Long id,
                           @RequestParam(value = "parentId",required = false) Long parentId,
                           @RequestParam(value = "isMain") Integer isMain){
        logger.info("mergeInvite:开始");
        InviteVehicleRecordPO po = new InviteVehicleRecordPO();
        po.setId(id);
        po.setParentId(parentId);
        po.setIsMain(isMain);
        inviteVehicleTaskService.mergeInvite(po);
        return 1;
    }


    /**
     * 跟据规则改变更新易损件邀约任务和线索
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "")
    })
    @ApiOperation(value = "跟据规则改变更新易损件邀约任务和线索", notes = "跟据规则改变更新易损件邀约任务和线索", httpMethod = "GET")
    @GetMapping("/updateInviteVulnerable")
    public int updateInviteVulnerable(@RequestParam(value = "createDate", required = false) String createDate) {
        logger.info("updateInviteVulnerable:开始");
        return inviteVehicleTaskService.updateInviteVulnerable(createDate);
    }


    /**
     * 跟据规则改变更新首保邀约任务和线索
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "")
    })
    @ApiOperation(value = "跟据规则改变更新首保邀约任务和线索", notes = "跟据规则改变更新首保邀约任务和线索", httpMethod = "GET")
    @GetMapping("/updateInviteFristMaintainByRuleChanged")
    public int updateInviteFristMaintainByRuleChanged(@RequestParam(value = "createDate", required = false) String createDate) {
        logger.info("updateInviteFristMaintainByRuleChanged:开始");
        return inviteVehicleTaskService.updateInviteFristMaintainByRuleChanged(createDate);
    }

    /**
     * 跟据规则改变更新定保邀约任务和线索
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "")
    })
    @ApiOperation(value = "跟据规则改变更新定保邀约任务和线索", notes = "跟据规则改变更新定保邀约任务和线索", httpMethod = "GET")
    @GetMapping("/updateInviteMaintainByRuleChanged")
    public int updateInviteMaintainByRuleChanged(@RequestParam(value = "createDate", required = false) String createDate) {
        logger.info("updateInviteMaintainByRuleChanged:开始");
        return inviteVehicleTaskService.updateInviteMaintainByRuleChanged(createDate);
    }

    /**
     * 跟据规则改变更新客户流失邀约任务和线索
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "")
    })
    @ApiOperation(value = "跟据规则改变更新客户流失邀约任务和线索", notes = "跟据规则改变更新客户流失邀约任务和线索", httpMethod = "GET")
    @GetMapping("/updateInviteCustomerLossByRuleChanged")
    public int updateInviteCustomerLossByRuleChanged(@RequestParam(value = "createDate", required = false) String createDate) {
        logger.info("updateInviteCustomerLossByRuleChanged:开始");
        return inviteVehicleTaskService.updateInviteCustomerLossByRuleChanged(createDate);
    }

    /**
     * 跟据规则改变更新保修邀约任务和线索
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "")
    })
    @ApiOperation(value = "跟据规则改变更新保修邀约任务和线索", notes = "跟据规则改变更新保修邀约任务和线索", httpMethod = "GET")
    @GetMapping("/updateInviteGuaranteeByRuleChanged")
    public int updateInviteGuaranteeByRuleChanged(@RequestParam(value = "createDate", required = false) String createDate) {
        logger.info("updateInviteGuaranteeByRuleChanged:开始");
        return inviteVehicleTaskService.updateInviteGuaranteeByRuleChanged(createDate);
    }


    /**
     * 每月月初生成上月二次跟进数据
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "")
    })
    @ApiOperation(value = "每月月初生成上月二次跟进数据", notes = "每月月初生成上月二次跟进数据", httpMethod = "GET")
    @GetMapping("/createTwiceFollow")
    public int createTwiceFollow(@RequestParam(value = "createDate", required = false) String createDate) {
        logger.info("createTwiceFollow:开始");
        return inviteVehicleTaskService.createTwiceFollow(createDate);
    }


    /**
     * 创建邀约任务
     * @param list
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<String>", name = "list", value = "", required = true)
    })
    @ApiOperation(value = "创建邀约任务", notes = "创建邀约任务", httpMethod = "POST")
    @PostMapping("/createInviteTask3")
    public int inviteAutoCreate3(@RequestBody List<String> list){
        logger.info("inviteAutoCreate3:开始");
        return inviteVehicleTaskService.inviteAutoCreateTaskList(list);
    }

    /**
     * 计算voc日均行驶里程
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "")
    })
    @ApiOperation(value = "计算voc日均行驶里程", notes = "计算voc日均行驶里程", httpMethod = "GET")
    @GetMapping("/pushComputeDailyAverageMileageForVoc")
    public int pushComputeDailyAverageMileageForVoc(@RequestParam(value = "createDate", required = false) String
                                                        createDate){
        logger.info("pushComputeDailyAverageMileageForVoc:开始");
        return vocManageService.pushpartitionGetAllVocVeh(createDate);
    }

    /**
     * 计算非voc日均行驶里程线索
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "startDate", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "endDate", value = "")
    })
    @ApiOperation(value = "计算非voc日均行驶里程线索", notes = "计算非voc日均行驶里程线索", httpMethod = "GET")
    @GetMapping("/pushComputeDailyAverageMileageRecord")
    public int pushComputeDailyAverageMileageRecord(@RequestParam(value = "startDate", required = false) String startDate,@RequestParam(value = "endDate", required = false) String endDate){
        logger.info("pushComputeDailyAverageMileageRecord:开始");
        return vocManageService.pushComputeDailyAverageMileageRecord(startDate,endDate);
    }

    /**
     * 计算非voc日均行驶里程任务
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "startDate", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "endDate", value = "")
    })
    @ApiOperation(value = "计算非voc日均行驶里程任务", notes = "计算非voc日均行驶里程任务", httpMethod = "GET")
    @GetMapping("/pushComputeDailyAverageMileageTask")
    public int pushComputeDailyAverageMileageTask(@RequestParam(value = "startDate", required = false) String startDate,@RequestParam(value = "endDate", required = false) String endDate){
        logger.info("pushComputeDailyAverageMileageTask:开始");
        return vocManageService.pushComputeDailyAverageMileageTask(startDate,endDate);
    }

    /**
     * 激活数据定时任务
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dateTime", value = "")
    })
    @ApiOperation(value = "定时任务获取激活数据", notes = "定时任务获取激活数据", httpMethod = "GET")
    @GetMapping("/getcontent")
    public void getcontent(@RequestParam(value = "dateTime", required = false) String dateTime){
        logger.info("getcontent:开始");
        ossService.getcontent(dateTime);
    }


    /**
     * 激活数据定时任务
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dateTime", value = "")
    })
    @ApiOperation(value = "定时任务获取激活数据", notes = "定时任务获取激活数据", httpMethod = "GET")
    @GetMapping("/downLoadVocFunctionalStatus")
    public void downLoadVocFunctionalStatus(@RequestParam(value = "dateTime", required = false) String dateTime){
        logger.info("downLoadVocFunctionalStatus:start");
        ossService.downLoadVocFunctionalStatus(dateTime);
        logger.info("downLoadVocFunctionalStatus:end");
    }
    /**
     * 激活数据定时任务
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dateTime", value = "")
    })
    @ApiOperation(value = "定时任务获取亮灯数据", notes = "定时任务获取亮灯数据", httpMethod = "GET")
    @GetMapping("/downLoadVocwarningdaily")
    public void downLoadVocwarningdaily(@RequestParam(value = "dateTime", required = false) String dateTime){
        logger.info("downLoadVocwarningdaily:start");
        ossService.downLoadVocwarningdaily(dateTime);
        logger.info("downLoadVocwarningdaily:end");
    }
    /**
     * 激活数据定时任务
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dateTime", value = "")
    })
    @ApiOperation(value = "定时任务获取激活数据", notes = "定时任务获取激活数据", httpMethod = "GET")
    @GetMapping("/vocFunctionalStatusDataClean")
    public void vocFunctionalStatusDataClean(@RequestParam(value = "dateTime", required = false) String dateTime){
        logger.info("vocFunctionalStatusDataClean:start");
        ossService.vocFunctionalStatusDataClean(dateTime);
        logger.info("vocFunctionalStatusDataClean:end");
    }
    /**
     * 激活数据定时任务
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dateTime", value = "")
    })
    @ApiOperation(value = "定时任务获取亮灯数据", notes = "定时任务获取亮灯数据", httpMethod = "GET")
    @GetMapping("/vocwarningdailyDataClean")
    public void vocwarningdailyDataClean(@RequestParam(value = "dateTime", required = false) String dateTime){
        logger.info("vocwarningdailyDataClean:start");
        ossService.vocwarningdailyDataClean(dateTime);
        logger.info("vocwarningdailyDataClean:end");
    }

    /**
     * 初始化数据执行一
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<String>", name = "list", value = "", required = true)
    })
    @ApiOperation(value = "初始化数据执行一", notes = "初始化数据执行一", httpMethod = "GET")
    @GetMapping("/initeVocStart")
    public void initeVocStart(@RequestBody List<String> list) {
        logger.info("initeVocStart:start");
        try {
            String dateTime;
            for (String str : list) {
                dateTime = str;
                logger.info("initeVocStart:dateTime {}", dateTime);
                ossService.downLoadVocFunctionalStatus(dateTime);
                ossService.downLoadVocwarningdaily(dateTime);
                ossService.vocFunctionalStatusDataClean(dateTime);
                ossService.vocwarningdailyDataClean(dateTime);
            }
        } catch (Exception e) {
            logger.info("initeVocStart:Exception {}", e);
            throw new IllegalArgumentException(e);
        }
        logger.info("initeVocStart:end");
    }

    /**
     * 初始化数据执行二
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dateTime", value = "")
    })
    @ApiOperation(value = "定时任务生成线索", notes = "定时任务生成线索", httpMethod = "GET")
    @GetMapping("/initeVoc")
    public void initeVoc(@RequestParam(value = "dateTime", required = false) String dateTime) {
        logger.info("initeVoc:start");
        try {
            ossbUSproService.vocdatainit(dateTime);
        } catch (Exception e) {
            logger.info("initeVoc:Exception {}", e);
            throw new IllegalArgumentException(e);
        }

        logger.info("initeVoc:end");
    }

    /**
     * 激活数据定时任务
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dateTime", value = "")
    })
    @ApiOperation(value = "定时任务获取激活数据", notes = "定时任务获取激活数据", httpMethod = "GET")
    @GetMapping("/getRun")
    public void run(@RequestParam(value = "dateTime", required = false) String dateTime){
        logger.info("run:start dateTime {}", dateTime);
        try {
            ossbUSproService.run(dateTime);
        } catch (Exception e) {
            logger.info("run:end Exception {}", e);
            throw new IllegalArgumentException(e);
        }
        logger.info("run:end");
    }

    /**
     * 数据常规
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dateTime", value = "")
    })
    @ApiOperation(value = "定时任务生成线索", notes = "定时任务生成线索", httpMethod = "GET")
    @GetMapping("/insvoc")
    public void insVoc(@RequestParam(value = "dateTime", required = false) String dateTime){
        logger.info("insVoc:开始");
        ossService.vocdata(dateTime);
    }

    /**
     * 数据常规
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dateTime", value = "")
    })
    @ApiOperation(value = "定时任务生成线索", notes = "定时任务生成流失客户线索", httpMethod = "GET")
    @GetMapping("/checkVocAlert")
    public void checkVocAlert(@RequestParam(value = "dateTime", required = false) String dateTime){
        logger.info("checkVocAlert:start");
        try {
            ossService.checkVocAlert(dateTime);
        } catch (Exception e) {
            logger.info("checkVocAlert:Exception {}",e);
            throw new IllegalArgumentException(e);
        }
        logger.info("checkVocAlert:end");
    }

    /**
     * 数据常规一次性
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dateTime", value = "")
    })
    @ApiOperation(value = "补数据", notes = "补数据", httpMethod = "GET")
    @GetMapping("/losspush")
    public void losspush(@RequestParam(value = "dateTime", required = false) String dateTime){
        logger.info("losspush:start");
        try {
            inviteVehicleTaskService.losspush(dateTime);
        } catch (Exception e) {
            logger.info("losspush:Exception e:{}",e);
            throw new IllegalArgumentException(e);
        }
        logger.info("losspush:end");
    }

    /**
     * 数据常规一次性
     * @return
     */
    @ApiOperation(value = "补数据", notes = "补数据", httpMethod = "GET")
    @GetMapping("/test")
    public void test(){
        inviteVehicleTaskService.taskAllocationVocList(inviteVehicleRecordService.selectByVin("YA1MV1239B1202126",true));
    }

}
