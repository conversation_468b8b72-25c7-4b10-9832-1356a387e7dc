package com.yonyou.dmscus.customer.controller.qb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.qb.QbVehicleInfoDTO;
import com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskPO;
import com.yonyou.dmscus.customer.entity.po.qb.InviteQBVehicleImportPO;
import com.yonyou.dmscus.customer.service.qb.QbVehicleInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/11/10 0010
 */

@Api(value = "/qbNumber", tags = {"QbNumberInfoController"})
@RestController
@RequestMapping("/qbNumber")
public class QbNumberInfoController {

    @Resource
    QbVehicleInfoService qbVehicleInfoService;

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "qbNumber", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isPerformed", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isClosed", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping
    public IPage<QbVehicleInfoDTO> selectPageBysql(
            @RequestParam(value="qbNumber",required = false) String qbNumber,
            @RequestParam(value="isPerformed",required = false) Integer isPerformed,
            @RequestParam(value="isClosed",required = false) Integer isClosed,
            @RequestParam(value="vin",required = false) String vin,
            @RequestParam(value="dealerCode",required = false) String dealerCode,
            @RequestParam("currentPage") int currentPage,
            @RequestParam("pageSize") int pageSize
         ){
        QbVehicleInfoDTO dto=new QbVehicleInfoDTO();
        dto.setQbNumber(qbNumber);
        dto.setIsPerformed(isPerformed);
        dto.setIsClosed(isClosed);
        dto.setVin(vin);
        dto.setDealerCode(dealerCode);
        Page<InviteVehicleDealerTaskPO> page = new Page(currentPage, pageSize);
       return qbVehicleInfoService.selectPageBysql(page,dto);
    }

    /**
                * 一键创建
     * @param dto
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "QbVehicleInfoDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "一键创建", notes = "一键创建", httpMethod = "POST")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public Integer createdInviteVehicle(@RequestBody QbVehicleInfoDTO dto){
        return  qbVehicleInfoService.createdInviteVehicle(dto);
    }
    
    /**
            * 创建线索
     *
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "", required = true)
    })
    @ApiOperation(value = "创建线索", notes = "创建线索", httpMethod = "POST")
    @PostMapping(value = "/create/{ids}")
    @ResponseStatus(HttpStatus.CREATED)
    public int createQb(@PathVariable("ids") String ids) {
    	return qbVehicleInfoService.createQb(ids);
    }


    /**
     * 导入QB线索临时表
     *
     * @param importFile
     * @return
     * @throws Exception
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "", required = true)
    })
    @ApiOperation(value = "导入QB线索临时表", notes = "导入QB线索临时表", httpMethod = "POST")
    @RequestMapping(value = "/importQBTemp", method = RequestMethod.POST)
    public ImportTempResult<InviteQBVehicleImportPO> importQBTemp(
            @RequestParam(value = "file") MultipartFile importFile) throws Exception {
        return  qbVehicleInfoService.importQBTemp(importFile);
    }

    /**
     * 查询导入错误
     * @param currentPage
     * @param pageSize
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "查询导入错误", notes = "查询导入错误", httpMethod = "GET")
    @GetMapping("/listErrorPage")
    public IPage<InviteQBVehicleImportPO> selectErrorPage(@RequestParam("currentPage") int currentPage,
                                                     @RequestParam("pageSize") int pageSize){
        Page<InviteQBVehicleImportPO> page=new Page(currentPage, pageSize);
        return qbVehicleInfoService.selectErrorPage(page);

    }

    /**
     * 临时表数据保存到正式表
     *
     * @param
     * @return
     */
    @ApiOperation(value = "临时表数据保存到正式表", notes = "临时表数据保存到正式表", httpMethod = "GET")
    @GetMapping(value = "/importTableByExcel")
    public int importIsLocalWRExcel() {
        int ret = qbVehicleInfoService.batchInsert();
        return ret;
    }

}
