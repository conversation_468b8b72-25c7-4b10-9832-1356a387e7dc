package com.yonyou.dmscus.customer.controller.invitationFollow;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dto.InvitationFollowParamsDTO;
import com.yonyou.dmscus.customer.dto.SaSllocateDlrParamsDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordPO;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.inviteInsurance.InviteInsuranceVehicleRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @since 2021-12-24
 */
@Api(value = "c端查询线索", tags = {"c端查询线索"})
@RestController
@RequestMapping("/invitationCPortFollow")
public class InvitationFollowCPortController {

    @Autowired
    InviteVehicleRecordService inviteVehicleRecordService;
    @Autowired
    InviteInsuranceVehicleRecordService inviteInsuranceVehicleRecordService;


    /**
     * c端查询养修线索列表
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-24
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InvitationFollowParamsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "c端查询养修线索列表", notes = "c端查询养修线索列表", httpMethod = "POST")
    @PostMapping("interf/list")
    public IPage<InviteVehicleRecordDTO> getInviteVehicleRecord(@RequestBody InvitationFollowParamsDTO dto) {
        Page<InviteVehicleRecordPO> page = new Page(dto.getCurrentPage(), dto.getPageSize());
        InviteVehicleRecordDTO inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        inviteVehicleRecordDTO.setInviteTypeParam(dto.getInviteType());
        inviteVehicleRecordDTO.setPlanFollowDateStart(dto.getPlanFollowDateStart());
        inviteVehicleRecordDTO.setPlanFollowDateEnd(dto.getPlanFollowDateEnd());
        inviteVehicleRecordDTO.setLicensePlateNum(dto.getLicensePlateNum());
        inviteVehicleRecordDTO.setVin(dto.getVin());
        inviteVehicleRecordDTO.setActualFollowDateStart(dto.getActualFollowDateStart());
        inviteVehicleRecordDTO.setActualFollowDateEnd(dto.getActualFollowDateEnd());
        inviteVehicleRecordDTO.setName(dto.getName());
        inviteVehicleRecordDTO.setIsBook(dto.getIsBook());
        inviteVehicleRecordDTO.setAdviseInDateStart(dto.getAdviseInDateStart());
        inviteVehicleRecordDTO.setAdviseInDateEnd(dto.getAdviseInDateEnd());
        inviteVehicleRecordDTO.setFollowStatusParam(dto.getFollowStatus());
        inviteVehicleRecordDTO.setOrderStatusParam(dto.getOrderStatusParam());
        inviteVehicleRecordDTO.setSaName(dto.getSaName());
        inviteVehicleRecordDTO.setCreatedAtStart(dto.getCreatedAtStart());
        inviteVehicleRecordDTO.setCreatedAtEnd(dto.getCreatedAtEnd());
        inviteVehicleRecordDTO.setIsself(dto.getIsself());
        inviteVehicleRecordDTO.setLeaveIds(dto.getLeaveIds());
        inviteVehicleRecordDTO.setSaId(dto.getSaId());
        inviteVehicleRecordDTO.setMonthTwice(dto.getMonthTwice());
        inviteVehicleRecordDTO.setOwnerCode(dto.getDealerCode());
        return inviteVehicleRecordService.getInviteVehicleRecordCPort(page, inviteVehicleRecordDTO);
    }




    /**
     * c端查询续保线索接口
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 20222-01-12
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "SaSllocateDlrParamsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "c端查询续保线索", notes = "c端查询续保线索", httpMethod = "POST")
    @PostMapping("interf/insurancelist")
    public IPage<InviteInsuranceVehicleRecordDTO> getInviteInsuranceVehicleRecord(@RequestBody SaSllocateDlrParamsDTO dto) {
        Page<InviteInsuranceVehicleRecordPO> page = new Page(dto.getCurrentPage(), dto.getPageSize());
        InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO = new InviteInsuranceVehicleRecordDTO();
        inviteInsuranceVehicleRecordDTO.setInviteTypeParam(dto.getInviteType());
        inviteInsuranceVehicleRecordDTO.setClueType(dto.getClueType());
        inviteInsuranceVehicleRecordDTO.setPlanFollowDateStart(dto.getPlanFollowDateStart());
        inviteInsuranceVehicleRecordDTO.setPlanFollowDateEnd(dto.getPlanFollowDateEnd());
        inviteInsuranceVehicleRecordDTO.setLicensePlateNum(dto.getLicensePlateNum());
        inviteInsuranceVehicleRecordDTO.setVin(dto.getVin());
        inviteInsuranceVehicleRecordDTO.setActualFollowDateStart(dto.getActualFollowDateStart());
        inviteInsuranceVehicleRecordDTO.setActualFollowDateEnd(dto.getActualFollowDateEnd());
        inviteInsuranceVehicleRecordDTO.setName(dto.getName());
        inviteInsuranceVehicleRecordDTO.setIsBook(dto.getIsBook());
        inviteInsuranceVehicleRecordDTO.setAdviseInDateStart(dto.getAdviseInDateStart());
        inviteInsuranceVehicleRecordDTO.setAdviseInDateEnd(dto.getAdviseInDateEnd());
        inviteInsuranceVehicleRecordDTO.setFollowStatusParam(dto.getFollowStatus());
        inviteInsuranceVehicleRecordDTO.setSaName(dto.getSaName());
        inviteInsuranceVehicleRecordDTO.setCreatedAtStart(dto.getCreatedAtStart());
        inviteInsuranceVehicleRecordDTO.setCreatedAtEnd(dto.getCreatedAtEnd());
        inviteInsuranceVehicleRecordDTO.setIsNoDistribute(dto.getIsNoDistribute());
        inviteInsuranceVehicleRecordDTO.setIsWaitDistribute(dto.getIsWaitDistribute());
        inviteInsuranceVehicleRecordDTO.setLeaveIds(dto.getLeaveIds());
        inviteInsuranceVehicleRecordDTO.setOrderStatus(dto.getOrderStatus());
        inviteInsuranceVehicleRecordDTO.setSaId(dto.getSaId());
        return inviteInsuranceVehicleRecordService.getInviteInsuranceVehicleRecordCPort(page, inviteInsuranceVehicleRecordDTO);
    }


}