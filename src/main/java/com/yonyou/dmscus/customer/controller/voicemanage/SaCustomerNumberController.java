package com.yonyou.dmscus.customer.controller.voicemanage;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.framework.RestResultResponse;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.SaCustomerNumberDTO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaCustomerNumberPO;
import com.yonyou.dmscus.customer.service.voicemanage.SaCustomerNumberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-06-16
 */
@Api(value = "/saCustomerNumber", tags = {"SaCustomerNumberController"})
@RestController
@RequestMapping("/saCustomerNumber")
public class SaCustomerNumberController {

    @Autowired
    SaCustomerNumberService saCustomerNumberService;

    /**
     * 分页查询数据
     *
     * @param appId        系统ID
     * @param ownerCode    所有者代码
     * @param ownerParCode 所有者的父组织代码
     * @param orgId        组织ID
     * @param id           主键ID
     * @param inviteId     邀约ID
     * @param callId       call_id
     * @param saId         服务顾问ID
     * @param cusName      客户名称
     * @param cusNumber    客户电话
     * @param dataSources  数据来源
     * @param isDeleted    是否删除
     * @param isValid      是否有效
     * @param createdAt    创建时间
     * @param updatedAt    更新时间
     * @param currentPage  页数
     * @param pageSize     分页大小
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-06-16
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "inviteId", value = "邀约ID"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "callId", value = "call_id"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "saId", value = "服务顾问ID"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "cusName", value = "客户名称"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "cusNumber", value = "客户电话"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = "数据来源"),
            @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "createdAt", value = "创建时间"),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "updatedAt", value = "更新时间"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
    })
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
    @GetMapping("/list")
    public IPage<SaCustomerNumberDTO> getByPage(
            @RequestParam(value = "appId", required = false) String appId,
            @RequestParam(value = "ownerCode", required = false) String ownerCode,
            @RequestParam(value = "ownerParCode", required = false) String ownerParCode,
            @RequestParam(value = "orgId", required = false) Integer orgId,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "inviteId", required = false) Long inviteId,
            @RequestParam(value = "callId", required = false) String callId,
            @RequestParam(value = "saId", required = false) String saId,
            @RequestParam(value = "cusName", required = false) String cusName,
            @RequestParam(value = "cusNumber", required = false) String cusNumber,
            @RequestParam(value = "dataSources", required = false) Integer dataSources,
            @RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
            @RequestParam(value = "isValid", required = false) Integer isValid,
            @RequestParam(value = "createdAt", required = false) Date createdAt,
            @RequestParam(value = "updatedAt", required = false) Date updatedAt,
            @RequestParam("currentPage") int currentPage,
            @RequestParam("pageSize") int pageSize) {
        Page<SaCustomerNumberPO> page = new Page(currentPage, pageSize);

        SaCustomerNumberDTO saCustomerNumberDTO = new SaCustomerNumberDTO();
        saCustomerNumberDTO.setAppId(appId);
        saCustomerNumberDTO.setOwnerCode(ownerCode);
        saCustomerNumberDTO.setOwnerParCode(ownerParCode);
        saCustomerNumberDTO.setOrgId(orgId);
        saCustomerNumberDTO.setId(id);
        saCustomerNumberDTO.setInviteId(inviteId);
        saCustomerNumberDTO.setCallId(callId);
        saCustomerNumberDTO.setSaId(saId);
        saCustomerNumberDTO.setCusName(cusName);
        saCustomerNumberDTO.setCusNumber(cusNumber);
        saCustomerNumberDTO.setDataSources(dataSources);
        saCustomerNumberDTO.setIsDeleted(isDeleted);
        saCustomerNumberDTO.setIsValid(isValid);
        saCustomerNumberDTO.setCreatedAt(createdAt);
        saCustomerNumberDTO.setUpdatedAt(updatedAt);
        return saCustomerNumberService.selectPageBysql(page, saCustomerNumberDTO);
    }


    /**
     * 保存呼叫登记
     * @param saCustomerNumberDTO
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "SaCustomerNumberDTO", name = "saCustomerNumberDTO", value = "", required = true)
    })
    @ApiOperation(value = "保存呼叫登记", notes = "保存呼叫登记", httpMethod = "POST")
    @PostMapping("/saveSaCustomerNumber")
    @ResponseStatus(HttpStatus.CREATED)
    public String saveSaCustomerNumber(@RequestBody SaCustomerNumberDTO saCustomerNumberDTO) {
        return saCustomerNumberService.saveSaCustomerNumber(saCustomerNumberDTO);
    }


    /**
     *查询ai语音登记信息
     * @param inviteId
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "inviteId", value = "", required = true)
    })
    @ApiOperation(value = "查询ai语音登记信息", notes = "查询ai语音登记信息", httpMethod = "GET")
    @GetMapping("/querySaCustomerNumber")
    public List<SaCustomerNumberDTO> querySaCustomerNumber(@RequestParam(value = "inviteId") Long inviteId){
        SaCustomerNumberDTO param = new SaCustomerNumberDTO();
        param.setInviteId(inviteId);
        return saCustomerNumberService.selectListBySql(param);
    }

    /**
     * 保存呼叫登记
     * @param saCustomerNumberDTO
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "SaCustomerNumberDTO", name = "saCustomerNumberDTO", value = "", required = true)
    })
    @ApiOperation(value = "保存呼叫登记", notes = "保存呼叫登记", httpMethod = "POST")
    @PostMapping("/fullLeadSaveSaCustomerNumber")
    @ResponseStatus(HttpStatus.CREATED)
    public RestResultResponse fullLeadSaveSaCustomerNumber(@RequestBody SaCustomerNumberDTO saCustomerNumberDTO) {

        return new RestResultResponse<String>().data(saCustomerNumberService.saveSaCustomerNumber(saCustomerNumberDTO));
    }
}
