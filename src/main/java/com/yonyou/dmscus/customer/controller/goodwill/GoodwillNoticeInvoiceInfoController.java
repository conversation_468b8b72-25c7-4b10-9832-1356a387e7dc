package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillNoticeInvoiceInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillNoticeInvoiceInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillNoticeInvoiceInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;

/**
 *
 * <AUTHOR>
 * @since 2020-06-17
 */
@Api(value = "/goodwillNoticeInvoiceInfo", tags = {"GoodwillNoticeInvoiceInfoController"})
@RestController
@RequestMapping("/goodwillNoticeInvoiceInfo")
public class GoodwillNoticeInvoiceInfoController {

	@Autowired
	GoodwillNoticeInvoiceInfoService goodwillNoticeInvoiceInfoService;

	/**
	 * 分页查询数据
	 *
	 * @param appId                        系统ID
	 * @param ownerCode                    所有者代码
	 * @param ownerParCode                 所有者的父组织代码
	 * @param orgId                        组织ID
	 * @param id                           主键id
	 * @param goodwillApplyId              亲善单id
	 * @param invoiceId                    开票id
	 * @param invoiceTitle                 开票抬头
	 * @param name                         名称
	 * @param taxpayerIdentificationNumber 纳税人识别号
	 * @param phone                        电话
	 * @param address                      地址
	 * @param openBank                     开户行
	 * @param account                      账号
	 * @param noticeInvoicePrice           通知开票金额
	 * @param voucherRechargePrice         代金券充值金额
	 * @param volvoCreditsRechargePrice    沃世界积分充值金额
	 * @param isValid                      是否有效
	 * @param isDeleted                    是否删除:1,删除；0,未删除
	 * @param createdAt                    创建时间
	 * @param updatedAt                    修改时间
	 * @param currentPage                  页数
	 * @param pageSize                     分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * @throws ParseException
	 * <AUTHOR>
	 * @since 2020-06-17
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键id"),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = "亲善单id"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceId", value = "开票id"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceTitle", value = "开票抬头"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "name", value = "名称"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "taxpayerIdentificationNumber", value = "纳税人识别号"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "phone", value = "电话"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "address", value = "地址"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "openBank", value = "开户行"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "account", value = "账号"),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "noticeInvoicePrice", value = "通知开票金额"),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "voucherRechargePrice", value = "代金券充值金额"),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "volvoCreditsRechargePrice", value = "沃世界积分充值金额"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除:1,删除；0,未删除"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = "创建时间"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = "修改时间"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
	})
	@ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
	@GetMapping
	public IPage<GoodwillNoticeInvoiceInfoDTO> getByPage(@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "goodwillApplyId", required = false) Long goodwillApplyId,
			@RequestParam(value = "invoiceId", required = false) String invoiceId,
			@RequestParam(value = "invoiceTitle", required = false) String invoiceTitle,
			@RequestParam(value = "name", required = false) String name,
			@RequestParam(value = "taxpayerIdentificationNumber", required = false) String taxpayerIdentificationNumber,
			@RequestParam(value = "phone", required = false) String phone,
			@RequestParam(value = "address", required = false) String address,
			@RequestParam(value = "openBank", required = false) String openBank,
			@RequestParam(value = "account", required = false) String account,
			@RequestParam(value = "noticeInvoicePrice", required = false) BigDecimal noticeInvoicePrice,
			@RequestParam(value = "voucherRechargePrice", required = false) BigDecimal voucherRechargePrice,
			@RequestParam(value = "volvoCreditsRechargePrice", required = false) BigDecimal volvoCreditsRechargePrice,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillNoticeInvoiceInfoPO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO = new GoodwillNoticeInvoiceInfoDTO();
		goodwillNoticeInvoiceInfoDTO.setAppId(appId);
		goodwillNoticeInvoiceInfoDTO.setOwnerCode(ownerCode);
		goodwillNoticeInvoiceInfoDTO.setOwnerParCode(ownerParCode);
		goodwillNoticeInvoiceInfoDTO.setOrgId(orgId);
		goodwillNoticeInvoiceInfoDTO.setId(id);
		goodwillNoticeInvoiceInfoDTO.setGoodwillApplyId(goodwillApplyId);
		goodwillNoticeInvoiceInfoDTO.setInvoiceId(invoiceId);
		goodwillNoticeInvoiceInfoDTO.setInvoiceTitle(invoiceTitle);
		goodwillNoticeInvoiceInfoDTO.setName(name);
		goodwillNoticeInvoiceInfoDTO.setTaxpayerIdentificationNumber(taxpayerIdentificationNumber);
		goodwillNoticeInvoiceInfoDTO.setPhone(phone);
		goodwillNoticeInvoiceInfoDTO.setAddress(address);
		goodwillNoticeInvoiceInfoDTO.setOpenBank(openBank);
		goodwillNoticeInvoiceInfoDTO.setAccount(account);
		goodwillNoticeInvoiceInfoDTO.setNoticeInvoicePrice(noticeInvoicePrice);
		goodwillNoticeInvoiceInfoDTO.setVoucherRechargePrice(voucherRechargePrice);
		goodwillNoticeInvoiceInfoDTO.setVolvoCreditsRechargePrice(volvoCreditsRechargePrice);
		goodwillNoticeInvoiceInfoDTO.setIsValid(isValid);
		goodwillNoticeInvoiceInfoDTO.setIsDeleted(isDeleted);
		if (createdAt != null) {
			goodwillNoticeInvoiceInfoDTO.setCreatedAt(sdf.parse(createdAt));
		}
		if (updatedAt != null) {
			goodwillNoticeInvoiceInfoDTO.setUpdatedAt(sdf.parse(updatedAt));
		}
		return goodwillNoticeInvoiceInfoService.selectPageBysql(page, goodwillNoticeInvoiceInfoDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id 数据主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillNoticeInvoiceInfoDTO
	 * <AUTHOR>
	 * @since 2020-06-17
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
	@GetMapping(value = "/{id}")
	public GoodwillNoticeInvoiceInfoDTO getById(@PathVariable("id") Long id) {
		return goodwillNoticeInvoiceInfoService.getById(id);
	}

	/**
	 * 进行数据新增
	 *
	 * @param goodwillNoticeInvoiceInfoDTO 需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-06-17
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillNoticeInvoiceInfoDTO", name = "goodwillNoticeInvoiceInfoDTO", value = "需要保存的DTO", required = true)
	})
	@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
	@PostMapping
	@ResponseStatus(HttpStatus.CREATED)
	public int insert(@RequestBody GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO) {
		return goodwillNoticeInvoiceInfoService.insert(goodwillNoticeInvoiceInfoDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id                           需要修改数据的ID
	 * @param goodwillNoticeInvoiceInfoDTO 需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-06-17
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillNoticeInvoiceInfoDTO", name = "goodwillNoticeInvoiceInfoDTO", value = "需要保存的DTO", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
	@PutMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.CREATED)
	public int update(@PathVariable("id") Long id,
			@RequestBody GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO) {
		return goodwillNoticeInvoiceInfoService.update(id, goodwillNoticeInvoiceInfoDTO);
	}

	/**
	 * 根据id删除对象
	 *
	 * @param id 需要修改数据的ID
	 * <AUTHOR>
	 * @since 2020-06-17
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
	})
	@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteById(@PathVariable("id") Long id) {
		goodwillNoticeInvoiceInfoService.deleteById(id);
		return true;
	}

	/**
	 * 根据IDs批量删除对象
	 *
	 * @param ids 需要修改数据的ID集合
	 * <AUTHOR>
	 * @since 2020-06-17
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "需要修改数据的ID集合", required = true)
	})
	@ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/batch/{ids}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteByIds(@PathVariable("ids") String ids) {
		goodwillNoticeInvoiceInfoService.deleteBatchIds(ids);
		return true;
	}

}