package com.yonyou.dmscus.customer.controller.complaint;

import com.yonyou.dmscus.customer.entity.po.complaint.CommonConfigPO;
import com.yonyou.dmscus.customer.service.complaint.sale.ConfigCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公共配置控制层
 */
@Api(value = "/commonConfig", tags = {"公共配置控制层"})
@AllArgsConstructor
@RestController
@RequestMapping("/commonConfig")
public class CommonConfigController {

    private ConfigCodeService configCodeService;
    /**
     * 根据对应的配置code获取所有的配置信息
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "configCode", value = "", required = true)
    })
    @ApiOperation(value = "根据对应的配置code获取所有的配置信息", notes = "根据对应的配置code获取所有的配置信息", httpMethod = "GET")
    @GetMapping(value = "/configCode")
    public List<CommonConfigPO> getByConfigCode(@RequestParam("configCode") String configCode) {
        return configCodeService.getByConfigCode(configCode);
    }
}
