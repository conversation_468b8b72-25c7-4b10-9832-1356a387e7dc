package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyRepairInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyRepairInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;






                                                                        /**
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
@Api(value = "/goodwillApplyRepairInfo", tags = {"GoodwillApplyRepairInfoController"})
@RestController
@RequestMapping("/goodwillApplyRepairInfo")
                public class GoodwillApplyRepairInfoController {
    
        @Autowired
        GoodwillApplyRepairInfoService goodwillApplyRepairInfoService;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码
        * @param ownerParCode 所有者的父组织代码
        * @param orgId 组织ID
        * @param id 维修记录表id
        * @param goodwillApplyId 亲善预申请表id
        * @param repairNo 工单号
        * @param intoFactoryDate 进厂日期
        * @param leaveFactoryDate 出厂日期
        * @param mileage 里程
        * @param vehicleFaultDetail 车辆故障详情
        * @param checkResult 检查结果
        * @param remark 备注
        * @param isValid 是否有效
        * @param isDeleted 是否删除:1,删除；0,未删除
        * @param createdAt 创建时间
        * @param updatedAt 修改时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @throws ParseException 
         * @since 2020-05-11
        */
                @ApiImplicitParams({
						@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
						@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
						@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
						@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
						@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "维修记录表id"),
						@ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = "亲善预申请表id"),
						@ApiImplicitParam(paramType = "query", dataType = "string", name = "repairNo", value = "工单号"),
						@ApiImplicitParam(paramType = "query", dataType = "string", name = "intoFactoryDate", value = "进厂日期"),
						@ApiImplicitParam(paramType = "query", dataType = "string", name = "leaveFactoryDate", value = "出厂日期"),
						@ApiImplicitParam(paramType = "query", dataType = "string", name = "mileage", value = "里程"),
						@ApiImplicitParam(paramType = "query", dataType = "string", name = "vehicleFaultDetail", value = "车辆故障详情"),
						@ApiImplicitParam(paramType = "query", dataType = "string", name = "checkResult", value = "检查结果"),
						@ApiImplicitParam(paramType = "query", dataType = "string", name = "remark", value = "备注"),
						@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
						@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除:1,删除；0,未删除"),
						@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = "创建时间"),
						@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = "修改时间"),
						@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
						@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
				})
				@ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
				@GetMapping
        public IPage<GoodwillApplyRepairInfoDTO>getByPage(
			@RequestParam(value="appId",required = false) String appId,
			@RequestParam(value="ownerCode",required = false) String ownerCode,
			@RequestParam(value="ownerParCode",required = false) String ownerParCode,
			@RequestParam(value="orgId",required = false) Integer orgId,
			@RequestParam(value="id",required = false) Long id,
			@RequestParam(value="goodwillApplyId",required = false) Long goodwillApplyId,
			@RequestParam(value="repairNo",required = false) String repairNo,
			@RequestParam(value="intoFactoryDate",required = false) String intoFactoryDate,
			@RequestParam(value="leaveFactoryDate",required = false) String leaveFactoryDate,
			@RequestParam(value="mileage",required = false) String mileage,
			@RequestParam(value="vehicleFaultDetail",required = false) String vehicleFaultDetail,
			@RequestParam(value="checkResult",required = false) String checkResult,
			@RequestParam(value="remark",required = false) String remark,
			@RequestParam(value="isValid",required = false) Integer isValid,
			@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
			@RequestParam(value="createdAt",required = false) String createdAt,
			@RequestParam(value="updatedAt",required = false) String updatedAt,
	        @RequestParam("currentPage")int currentPage,
	        @RequestParam("pageSize")int pageSize) throws ParseException{
                	Page<GoodwillApplyRepairInfoPO>page=new Page(currentPage,pageSize);
                	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                	GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO =new GoodwillApplyRepairInfoDTO();
		            goodwillApplyRepairInfoDTO.setAppId(appId);
		            goodwillApplyRepairInfoDTO.setOwnerCode(ownerCode);
		            goodwillApplyRepairInfoDTO.setOwnerParCode(ownerParCode);
		            goodwillApplyRepairInfoDTO.setOrgId(orgId);
		            goodwillApplyRepairInfoDTO.setId(id);
		            goodwillApplyRepairInfoDTO.setGoodwillApplyId(goodwillApplyId);
		            goodwillApplyRepairInfoDTO.setRepairNo(repairNo);
		            goodwillApplyRepairInfoDTO.setMileage(mileage);
		            goodwillApplyRepairInfoDTO.setVehicleFaultDetail(vehicleFaultDetail);
		            goodwillApplyRepairInfoDTO.setCheckResult(checkResult);
		            goodwillApplyRepairInfoDTO.setRemark(remark);
		            goodwillApplyRepairInfoDTO.setIsValid(isValid);
		            goodwillApplyRepairInfoDTO.setIsDeleted(isDeleted);
		            if(intoFactoryDate != null){
		            	goodwillApplyRepairInfoDTO.setIntoFactoryDate(sdf.parse(intoFactoryDate));
	                }
	                if(leaveFactoryDate != null){
	                	goodwillApplyRepairInfoDTO.setLeaveFactoryDate(sdf.parse(leaveFactoryDate));
	                }
	                if(createdAt != null){
	                	goodwillApplyRepairInfoDTO.setCreatedAt(sdf.parse(createdAt));
	                }
	                if(updatedAt != null){
	                	goodwillApplyRepairInfoDTO.setUpdatedAt(sdf.parse(updatedAt));
	                }
                return goodwillApplyRepairInfoService.selectPageBysql(page,goodwillApplyRepairInfoDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairInfoDTO
 * <AUTHOR>
 * @since 2020-05-11
 */
@ApiImplicitParams({
		@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public GoodwillApplyRepairInfoDTO getById(@PathVariable("id") Long id){
        return goodwillApplyRepairInfoService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param goodwillApplyRepairInfoDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-05-11
 */
@ApiImplicitParams({
		@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyRepairInfoDTO", name = "goodwillApplyRepairInfoDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO){
        return goodwillApplyRepairInfoService.insert( goodwillApplyRepairInfoDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param goodwillApplyRepairInfoDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-05-11
 */
@ApiImplicitParams({
		@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
		@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyRepairInfoDTO", name = "goodwillApplyRepairInfoDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO){
        return goodwillApplyRepairInfoService.update(id,goodwillApplyRepairInfoDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-05-11
 */
@ApiImplicitParams({
		@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    goodwillApplyRepairInfoService.deleteById(id);
        return true;
        }

        /**
         * 根据IDs批量删除对象
         *
         * @param ids 需要修改数据的ID集合
         * <AUTHOR>
         * @since 2020-05-11
         */
                        @ApiImplicitParams({
								@ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "需要修改数据的ID集合", required = true)
						})
						@ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
						@DeleteMapping(value = "/batch/{ids}")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        public boolean deleteByIds(@PathVariable("ids") String ids){
            goodwillApplyRepairInfoService.deleteBatchIds(ids);
                return true;
                }

}