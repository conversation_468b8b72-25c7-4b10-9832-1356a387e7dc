package com.yonyou.dmscus.customer.controller.inviteInsurance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dto.SaSllocateDlrParamsDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InsuranceSaSllocateDlrDto;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordPO;
import com.yonyou.dmscus.customer.service.inviteInsurance.InviteInsuranceSaAllocateRuleService;
import com.yonyou.dmscus.customer.service.inviteInsurance.InviteInsuranceVehicleRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2020-11-11
 */
@Api(value = "/insuranceSaSllocateDlr", tags = {"InsuranceSaSllocateDlrController"})
@RestController
@RequestMapping("/insuranceSaSllocateDlr")
public class InsuranceSaSllocateDlrController {

    @Autowired
    InviteInsuranceVehicleRecordService inviteInsuranceVehicleRecordService;
    @Autowired
    InviteInsuranceSaAllocateRuleService inviteInsuranceSaAllocateRuleService;

    /**
     * 分页查询数据
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-24
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "SaSllocateDlrParamsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "POST")
    @PostMapping("/list")
    public IPage<InviteInsuranceVehicleRecordDTO> getInviteInsuranceVehicleRecord(@RequestBody  SaSllocateDlrParamsDTO dto) {
        Page<InviteInsuranceVehicleRecordPO> page = new Page(dto.getCurrentPage(), dto.getPageSize());
        InviteInsuranceVehicleRecordDTO inviteInsuranceVehicleRecordDTO = new InviteInsuranceVehicleRecordDTO();
        inviteInsuranceVehicleRecordDTO.setInviteTypeParam(dto.getInviteType());
        inviteInsuranceVehicleRecordDTO.setClueType(dto.getClueType());
        inviteInsuranceVehicleRecordDTO.setPlanFollowDateStart(dto.getPlanFollowDateStart());
        inviteInsuranceVehicleRecordDTO.setPlanFollowDateEnd(dto.getPlanFollowDateEnd());
        inviteInsuranceVehicleRecordDTO.setLicensePlateNum(dto.getLicensePlateNum());
        inviteInsuranceVehicleRecordDTO.setVin(dto.getVin());
        inviteInsuranceVehicleRecordDTO.setActualFollowDateStart(dto.getActualFollowDateStart());
        inviteInsuranceVehicleRecordDTO.setActualFollowDateEnd(dto.getActualFollowDateEnd());
        inviteInsuranceVehicleRecordDTO.setName(dto.getName());
        inviteInsuranceVehicleRecordDTO.setIsBook(dto.getIsBook());
        inviteInsuranceVehicleRecordDTO.setAdviseInDateStart(dto.getAdviseInDateStart());
        inviteInsuranceVehicleRecordDTO.setAdviseInDateEnd(dto.getAdviseInDateEnd());
        inviteInsuranceVehicleRecordDTO.setFollowStatusParam(dto.getFollowStatus());
        inviteInsuranceVehicleRecordDTO.setSaName(dto.getSaName());
        inviteInsuranceVehicleRecordDTO.setCreatedAtStart(dto.getCreatedAtStart());
        inviteInsuranceVehicleRecordDTO.setCreatedAtEnd(dto.getCreatedAtEnd());
        inviteInsuranceVehicleRecordDTO.setIsNoDistribute(dto.getIsNoDistribute());
        inviteInsuranceVehicleRecordDTO.setIsWaitDistribute(dto.getIsWaitDistribute());
        inviteInsuranceVehicleRecordDTO.setLeaveIds(dto.getLeaveIds());
        inviteInsuranceVehicleRecordDTO.setOrderStatus(dto.getOrderStatus());
        inviteInsuranceVehicleRecordDTO.setSaId(dto.getSaId());
        inviteInsuranceVehicleRecordDTO.setInsuranceName(dto.getInsuranceName());
        inviteInsuranceVehicleRecordDTO.setInsuranceTypeList(dto.getInsuranceTypeList());
        return inviteInsuranceVehicleRecordService.getInviteInsuranceVehicleRecord(page, inviteInsuranceVehicleRecordDTO);
    }

    /**
     * 保存邀约线索分配
     * @param saSllocateDlrDto
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InsuranceSaSllocateDlrDto", name = "saSllocateDlrDto", value = "", required = true)
    })
    @ApiOperation(value = "保存邀约线索分配", notes = "保存邀约线索分配", httpMethod = "POST")
    @PostMapping("/saveInsuranceSaSllocate")
    public int saveInsuranceSaSllocate(@RequestBody InsuranceSaSllocateDlrDto saSllocateDlrDto) {
        return inviteInsuranceVehicleRecordService.saveInsuranceSaSllocate(saSllocateDlrDto);
    }



}