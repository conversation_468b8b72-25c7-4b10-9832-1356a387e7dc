package com.yonyou.dmscus.customer.controller.complaint.sale;

import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintEvidenceService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;






                                                            /**
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
@Api(value = "/saleComplaintEvidence", tags = {"SaleComplaintEvidenceController"})
@RestController
@RequestMapping("/saleComplaintEvidence")
                public class SaleComplaintEvidenceController extends BaseController {
    
        @Autowired
        SaleComplaintEvidenceService saleComplaintEvidenceService;


}