package com.yonyou.dmscus.customer.controller.voicemanage;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.SaWorkNumberDTO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaWorkNumberPO;
import com.yonyou.dmscus.customer.service.voicemanage.SaWorkNumberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @since 2020-06-16
 */
@Api(value = "/saWorkNumber", tags = {"SaWorkNumberController"})
@RestController
@RequestMapping("/saWorkNumber")
public class SaWorkNumberController {

    @Autowired
    SaWorkNumberService saWorkNumberService;

    /**
     * 分页查询数据
     *
     * @param saName      服务顾问姓名
     * @param saNumber    服务顾问手机号
     * @param workNumber  AI语音工作号
     * @param currentPage 页数
     * @param pageSize    分页大小
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-06-16
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "saName", value = "服务顾问姓名"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "saNumber", value = "服务顾问手机号"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "workNumber", value = "AI语音工作号"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
    })
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
    @GetMapping("/list")
    public IPage<SaWorkNumberDTO> getByPage(
            @RequestParam(value = "saName", required = false) String saName,
            @RequestParam(value = "saNumber", required = false) String saNumber,
            @RequestParam(value = "workNumber", required = false) String workNumber,
            @RequestParam("currentPage") int currentPage,
            @RequestParam("pageSize") int pageSize) {
        Page<SaWorkNumberPO> page = new Page(currentPage, pageSize);

        SaWorkNumberDTO saWorkNumberDTO = new SaWorkNumberDTO();
        saWorkNumberDTO.setSaName(saName);
        saWorkNumberDTO.setSaNumber(saNumber);
        saWorkNumberDTO.setWorkNumber(workNumber);
        return saWorkNumberService.selectPageBysql(page, saWorkNumberDTO);
    }

    /**
     * 进行数据修改
     *
     * @param id 数据主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.SaWorkNumberDTO
     * <AUTHOR>
     * @since 2020-06-16
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
    })
    @ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
    @GetMapping(value = "/{id}")
    public SaWorkNumberDTO getById(@PathVariable("id") Long id) {
        return saWorkNumberService.getById(id);
    }

    /**
     * 保存AI语音工作号
     *
     * @param saWorkNumberDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-06-16
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "SaWorkNumberDTO", name = "saWorkNumberDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "保存AI语音工作号", notes = "保存AI语音工作号", httpMethod = "POST")
    @PostMapping("/saveSaWorkNumber")
    @ResponseStatus(HttpStatus.CREATED)
    public int saveSaWorkNumber(@RequestBody SaWorkNumberDTO saWorkNumberDTO) {
        return saWorkNumberService.saveSaWorkNumber(saWorkNumberDTO);
    }


    /**
     * 解绑
     * @param id
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "解绑", notes = "解绑", httpMethod = "GET")
    @GetMapping("/noBinding")
    public int noBinding(@RequestParam(value = "id") Long id){
        return saWorkNumberService.noBinding(id);
    }




}