package com.yonyou.dmscus.customer.controller.accidentClues;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.framework.RestResultResponse;
import com.yonyou.dmscus.customer.dto.AccidentCluesUserDto;
import com.yonyou.dmscus.customer.entity.dto.GetDealerUserDataDTO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentClueContact;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentClueVO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesAllotDTO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesDTO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesExportDTO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesImportDTO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesSaNumberDTO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.BookingOrderReturnVo;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.DashBoardQueryDTO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.StatusChangePushDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesFollowPO;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesImportPO;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO;
import com.yonyou.dmscus.customer.entity.vo.AccidentClueDashBoardVO;
import com.yonyou.dmscus.customer.entity.vo.AccidentClueVo;
import com.yonyou.dmscus.customer.entity.vo.EmpByRoleCodeVO;
import com.yonyou.dmscus.customer.entity.vo.RepairCountVo;
import com.yonyou.dmscus.customer.entity.vo.accidentClues.AccidentClueFollowVo;
import com.yonyou.dmscus.customer.service.accidentClues.AccidentCluesOssService;
import com.yonyou.dmscus.customer.service.accidentClues.AccidentCluesService;
import com.yonyou.dmscus.customer.service.accidentClues.LiteCrmService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(value = "/accidentClues", tags = {"事故线索"})
@RestController
@RequestMapping("/accidentClues")
@Slf4j
public class AccidentCluesController extends BaseController {

    private Logger logger = LoggerFactory.getLogger(AccidentCluesController.class);

    @Resource
    AccidentCluesService accidentCluesService;

    @Resource
    AccidentCluesOssService accidentCluesOssService;
    @Autowired
    LiteCrmService liteCrmService;


    /**
     * 分页查询
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "分页查询", notes = "分页查询", httpMethod = "GET")
    @GetMapping
    public IPage<AccidentCluesPO> selectPageBysql(AccidentCluesDTO dto, @RequestParam("currentPage") Long currentPage, @RequestParam("pageSize") Long pageSize) {
        Page page = new Page(currentPage, pageSize);
        return accidentCluesService.selectPageBysql(page, dto);
    }

    /**
     * 保存线索
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AccidentCluesDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "保存线索", notes = "保存线索", httpMethod = "POST")
    @PostMapping
    public int insert(@RequestBody AccidentCluesDTO dto) {
        return accidentCluesService.insert(dto);
    }

    /**
     * 线索跟进
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AccidentCluesFollowDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "线索跟进", notes = "线索跟进", httpMethod = "POST")
    @PostMapping(value = "/follow")
    public int insertCluesFollow(@RequestBody AccidentCluesFollowDTO dto) {
        return accidentCluesService.insertCluesFollow(dto);
    }

    /**
     * 线索分配
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AccidentCluesAllotDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "线索分配", notes = "线索分配", httpMethod = "POST")
    @PostMapping(value = "/allot")
    public int insertttCluesAllot(@RequestBody AccidentCluesAllotDTO dto) {
        return accidentCluesService.insertttCluesAllot(dto);
    }

    /**
     * 根据id查询线索主单信息
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "int", name = "acId", value = "", required = true)
    })
    @ApiOperation(value = "根据id查询线索主单信息", notes = "根据id查询线索主单信息", httpMethod = "GET")
    @GetMapping(value = "/{acId}")
    public AccidentCluesDTO selectById(@PathVariable("acId") Integer acId) {
        return accidentCluesService.selectById(acId);
    }

    /**
     * 查询跟进历史
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "pageSize", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "acId", value = "", required = true)
    })
    @ApiOperation(value = "查询跟进历史", notes = "查询跟进历史", httpMethod = "GET")
    @GetMapping(value = "/followList")
    public IPage<AccidentCluesFollowPO> selectCluesFollowPageBysql(@RequestParam("currentPage") Long currentPage, @RequestParam("pageSize") Long pageSize, @RequestParam("acId") Integer acId) {
        Page page = new Page(currentPage, pageSize);
        return accidentCluesService.selectCluesFollowPageBysql(page, acId);
    }

    /**
     * 查询联系人
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "acId", value = "", required = true)
    })
    @ApiOperation(value = "查询联系人", notes = "查询联系人", httpMethod = "GET")
    @GetMapping(value = "/cusPhoneList")
    public List<AccidentCluesUserDto> selectCusList(@RequestParam("acId") Integer acId) {
        return accidentCluesService.selectCusList(acId);
    }


    /**
     * 呼叫登记
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AccidentCluesSaNumberDTO", name = "saCustomerNumberDTO", value = "", required = true)
    })
    @ApiOperation(value = "呼叫登记", notes = "呼叫登记", httpMethod = "POST")
    @PostMapping(value = "/saveCluesSaNumber")
    public RestResultResponse<String> saveWorkNumber(@RequestBody AccidentCluesSaNumberDTO saCustomerNumberDTO) {
        RestResultResponse<String> dto = new RestResultResponse<>();
        try {
            dto.setData(accidentCluesService.saveWorkNumber(saCustomerNumberDTO));
            dto.setResultCode(200);
            dto.setSuccess(true);
        } catch (Exception e) {
            dto.setErrMsg(e.getMessage());
            dto.setResultCode(500);
            dto.setSuccess(false);
            log.error("事故线索外呼登记报错",e);
        }
        return dto;
    }

    /**
     * 查询跟进通话记录
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "followId", value = "", required = true)
    })
    @ApiOperation(value = "查询跟进通话记录", notes = "查询跟进通话记录", httpMethod = "GET")
    @GetMapping(value = "/callDetailByFollowId")
    public List<CallDetailsPO> selectCallDetailByfollowId(@RequestParam("followId") Integer followId) {
        return accidentCluesService.selectCallDetailByfollowId(followId);
    }


    /**
     * 导出
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AccidentCluesExportDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "导出", notes = "导出", httpMethod = "POST")
    @RequestMapping(value = "/exportExcel", method = RequestMethod.POST)
    @ResponseBody
    public void exportExcel(@RequestBody AccidentCluesExportDTO dto,
                            HttpServletRequest request, HttpServletResponse response) throws Exception {
        accidentCluesOssService.exportExcelAccidentExcel(dto);
    }


    /**
     * 导出
     */
    @ApiOperation(value = "导出", notes = "导出", httpMethod = "GET")
    @RequestMapping(value = "/exportExcel/oss", method = RequestMethod.GET)
    public List<Map> exportExcelOss(AccidentCluesDTO dto) throws Exception {
        List<Map> data = accidentCluesService.exportExcelAccident(dto);
        return data;
    }


    /**
     * d导入邀约线索临时表
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "", required = true)
    })
    @ApiOperation(value = "导入邀约线索临时表", notes = "d导入邀约线索临时表", httpMethod = "POST")
    @RequestMapping(value = "/importTemp", method = RequestMethod.POST)
    public ImportTempResult<AccidentCluesImportPO> importWRIsLocalDetailTemp(
            @RequestParam(value = "file") MultipartFile importFile) throws Exception {
        return accidentCluesService.importTemp(importFile);
    }

    /**
     * 查询导入错误
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "查询导入错误", notes = "查询导入错误", httpMethod = "GET")
    @GetMapping("/listErrorPage")
    public IPage<AccidentCluesImportPO> selectErrorPage(@RequestParam("currentPage") int currentPage,
    		@RequestParam("pageSize") int pageSize){
    	Page<AccidentCluesImportDTO> page=new Page(currentPage, pageSize);
    	return accidentCluesService.selectErrorPage(page);

    }

	/**
	     * 查询导入正确
	*/
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "查询导入正确", notes = "查询导入正确", httpMethod = "GET")
    @GetMapping("/listSuccessPage")
	public IPage<AccidentCluesImportPO> selectSuccessPage(@RequestParam("currentPage") int currentPage,
			@RequestParam("pageSize") int pageSize){
		Page<AccidentCluesImportDTO> page=new Page(currentPage, pageSize);
		return accidentCluesService.selectSuccessPage(page);

	}

    /**
	 * 临时表数据保存到正式表
	 * <AUTHOR>
	 * @since 2020-03-30
	 */
	@ApiOperation(value = "临时表数据保存到正式表", notes = "临时表数据保存到正式表", httpMethod = "GET")
    @GetMapping(value = "/importTableByExcel")
    public int importIsLocalWRExcel() {
		accidentCluesService.batchInsert();
		return 1;
	}


	  /**
     * 查询跟进通话记录
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "acId", value = "", required = true)
    })
    @ApiOperation(value = "查询跟进通话记录", notes = "查询跟进通话记录", httpMethod = "GET")
    @GetMapping(value = "/selectMaxFollw")
    public AccidentCluesFollowPO selectMaxFollwByAcId(@RequestParam("acId") Integer acId) {
        return accidentCluesService.selectMaxFollwByAcId(acId);
    }

    /**
     * 14天更新超时
     */
    @ApiOperation(value = "14天更新超时", notes = "14天更新超时", httpMethod = "GET")
    @GetMapping(value = "/timeout/interAspect")
    public void updateTimeOut() {
        accidentCluesService.updateTimeOut();
    }

    /**
     * 消息提醒
     */
    @ApiOperation(value = "消息提醒", notes = "消息提醒", httpMethod = "GET")
    @GetMapping(value = "/alert/interAspect")
    public void accidentCluesAlert() {
        accidentCluesService.accidentCluesAlert();
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AccidentCluesDTO", name = "clue", value = "", required = true)
    })
    @ApiOperation(value = "修改事故线索", notes = "", httpMethod = "POST")
    @PostMapping(value = "/update")
    public void updateAccidentClues(@RequestBody AccidentCluesDTO clue) {
        logger.info("修改事故线索 acId:{} 入参AccidentCluesDTO clue：【{}】", clue.getAcId(), JSONUtil.toJsonStr(clue));
        accidentCluesService.updateAccidentClues(clue);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AccidentCluesDTO", name = "clue", value = "", required = true)
    })
    @ApiOperation(value = "修改事故线索", notes = "", httpMethod = "POST")
    @PostMapping(value = "/update/interf")
    public void updateAccidentCluesInterf(@RequestBody AccidentCluesDTO clue) {
        logger.info("修改事故线索/update/interf acId:{} 入参AccidentCluesDTO clue：【{}】", clue.getAcId(), JSONUtil.toJsonStr(clue));
        accidentCluesService.updateAccidentClues(clue);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AccidentCluesDTO", name = "clue", value = "", required = true)
    })
    @ApiOperation(value = "事故线索新增", notes = "", httpMethod = "POST")
    @PostMapping(value = "/save")
    public RestResultResponse saveAccidentClues(@RequestBody AccidentCluesDTO clue) {

        return new RestResultResponse<>().data(accidentCluesService.saveAccidentClues(clue));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AccidentClueVO", name = "params", value = "", required = true)
    })
    @ApiOperation(value = "获取事故线索列表", notes = "", httpMethod = "POST")
    @PostMapping(value = "/getList")
    public IPage<AccidentClueVO> getList(@RequestBody AccidentClueVO params,HttpServletRequest request) {
        return accidentCluesService.getList(params);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AccidentClueVO", name = "params", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping("follow/count")
    public List<AccidentClueFollowVo> followCount(@RequestBody AccidentClueVO params){
        return accidentCluesService.followCount(params);
    }

    /*
     * 返回当前经销商未跟进线索数量+继续跟进线索数量
     * */
    @ApiOperation(value = "返回app图标展示数量", notes = "返回当前经销商未跟进线索数量+继续跟进线索数量 */", httpMethod = "GET")
    @GetMapping("/count")
    public RepairCountVo count(HttpServletRequest request) {
        return accidentCluesService.count();
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AccidentCluesDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "根据短信得到事故线索信息", notes = "", httpMethod = "POST")
    @PostMapping("/getInfoByPic")
    public AccidentClueVo getInfoByPic(@RequestBody AccidentCluesDTO dto){
        if(StringUtils.isNullOrEmpty(dto.getParam())){
            return new AccidentClueVo();
        }
       return  accidentCluesService.getInfoByContent(dto.getParam());
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "GetDealerUserDataDTO", name = "getDealerUserDTO", value = "", required = true)
    })
    @ApiOperation(value = "查看员工", notes = "", httpMethod = "POST")
    @PostMapping(value="/dealer/user")
    public List<EmpByRoleCodeVO> getDealerUser(@RequestBody GetDealerUserDataDTO getDealerUserDTO){
        return accidentCluesService.getDealerUser(getDealerUserDTO,false);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "acId", value = "", required = true)
    })
    @ApiOperation(value = "跟进历史", notes = "", httpMethod = "GET")
    @GetMapping(value = "/getFollowList")
    public List<AccidentCluesFollowDTO> getFollowList(@RequestParam("acId") Integer acId) {

        return accidentCluesService.getFollowList(acId);
    }

    @ApiOperation(value = "跟进提醒", notes = "", httpMethod = "GET")
    @GetMapping(value = "/followRemind")
    public void followRemind() {

        accidentCluesService.followRemind();
    }

    @ApiOperation(value = "超时未进厂提醒", notes = "", httpMethod = "GET")
    @GetMapping(value = "/appointmentTimeOutRemind")
    public void appointmentTimeOutRemind() {

        accidentCluesService.appointmentTimeOutRemind();
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<AccidentCluesDTO>", name = "params", value = "", required = true)
    })
    @ApiOperation(value = "接收第三方推送事故线索", notes = "", httpMethod = "POST")
    @PostMapping(value = "/receiveClueList")
    public void receiveClueList(@RequestBody List<AccidentCluesDTO> params) {

        accidentCluesService.receiveClueList(params);
    }

    /**
     * 事故线索-预约单保存
     * @param dto
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AccidentCluesDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "事故线索-预约单保存", notes = "事故线索-预约单保存", httpMethod = "POST")
    @PostMapping(value = "/saveAppointmentOrder")
    public BookingOrderReturnVo saveAppointmentOrder(@RequestBody AccidentCluesDTO dto) {

        logger.info("dmscus-customer事故线索-预约单保存(/accidentClues/saveAppointmentOrder)，入参：【{}】", JSONUtil.toJsonStr(dto));
        return accidentCluesService.saveAppointmentOrder(dto);

    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AccidentClueContact", name = "contact", value = "", required = true)
    })
    @ApiOperation(value = "修改线索联系人", notes = "", httpMethod = "POST")
    @PostMapping(value = "/updateContactInfo")
    public void updateContactInfo(@RequestBody AccidentClueContact contact) {

        accidentCluesService.updateContactInfo(contact);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "DashBoardQueryDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "事故线索看板", notes = "", httpMethod = "POST")
    @PostMapping(value = "/getDashBoard")
    public AccidentClueDashBoardVO getDashBoard(@RequestBody DashBoardQueryDTO dto) {

        return accidentCluesService.queryAccidentClueDashboard(dto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<StatusChangePushDTO>", name = "pushInfoList", value = "", required = true)
    })
    @ApiOperation(value = "同步状态到litecrm", notes = "", httpMethod = "POST")
    @PostMapping(value = "/pushLiteCrmClueStatus/interf")
    public void pushLiteCrmClueStatus(@RequestBody List<StatusChangePushDTO> pushInfoList) {
        accidentCluesService.pushLiteCrmClueStatus(pushInfoList);
    }
    @ApiOperation(value = "获取liteCrmToken", notes = "", httpMethod = "POST")
    @PostMapping(value = "/getLiteCrmToken")
    public String getLiteCrmToken() {
        return liteCrmService.getCrmToken();
    }
}
