package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditDetailDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditDetailPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyAuditDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;

/**
 *
 * <AUTHOR>
 * @since 2020-06-24
 */
@Api(value = "/goodwillApplyAuditDetail", tags = {"GoodwillApplyAuditDetailController"})
@RestController
@RequestMapping("/goodwillApplyAuditDetail")
public class GoodwillApplyAuditDetailController {

	@Autowired
	GoodwillApplyAuditDetailService goodwillApplyAuditDetailService;

	/**
	 * 分页查询数据
	 *
	 * @param appId
	 *            系统ID
	 * @param ownerCode
	 *            所有者代码
	 * @param ownerParCode
	 *            所有者的父组织代码
	 * @param orgId
	 *            组织ID
	 * @param id
	 *            主键id
	 * @param auditId
	 *            审计表主键
	 * @param auditWay
	 *            审计方式
	 * @param auditTime
	 *            审计时间
	 * @param troubleSpots
	 *            问题点
	 * @param punishResult
	 *            处罚结果
	 * @param deductionsPrice
	 *            扣款金额
	 * @param isNotification
	 *            是否通报
	 * @param isValid
	 *            是否有效
	 * @param isDeleted
	 *            是否删除:1,删除；0,未删除
	 * @param createdAt
	 *            创建时间
	 * @param updatedAt
	 *            修改时间
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-06-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "auditId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditWay", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "troubleSpots", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "punishResult", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "deductionsPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isNotification", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询数据", httpMethod = "GET")
	@GetMapping
	public IPage<GoodwillApplyAuditDetailDTO> getByPage(@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "auditId", required = false) Long auditId,
			@RequestParam(value = "auditWay", required = false) Integer auditWay,
			@RequestParam(value = "auditTime", required = false) String auditTime,
			@RequestParam(value = "troubleSpots", required = false) String troubleSpots,
			@RequestParam(value = "punishResult", required = false) String punishResult,
			@RequestParam(value = "deductionsPrice", required = false) BigDecimal deductionsPrice,
			@RequestParam(value = "isNotification", required = false) Integer isNotification,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyAuditDetailPO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO = new GoodwillApplyAuditDetailDTO();
		goodwillApplyAuditDetailDTO.setAppId(appId);
		goodwillApplyAuditDetailDTO.setOwnerCode(ownerCode);
		goodwillApplyAuditDetailDTO.setOwnerParCode(ownerParCode);
		goodwillApplyAuditDetailDTO.setOrgId(orgId);
		goodwillApplyAuditDetailDTO.setId(id);
		goodwillApplyAuditDetailDTO.setAuditId(auditId);
		goodwillApplyAuditDetailDTO.setAuditWay(auditWay);
		goodwillApplyAuditDetailDTO.setTroubleSpots(troubleSpots);
		goodwillApplyAuditDetailDTO.setPunishResult(punishResult);
		goodwillApplyAuditDetailDTO.setDeductionsPrice(deductionsPrice);
		goodwillApplyAuditDetailDTO.setIsNotification(isNotification);
		goodwillApplyAuditDetailDTO.setIsValid(isValid);
		goodwillApplyAuditDetailDTO.setIsDeleted(isDeleted);
		if (auditTime != null) {
			goodwillApplyAuditDetailDTO.setAuditTime(sdf.parse(auditTime));
		}
		if (createdAt != null) {
			goodwillApplyAuditDetailDTO.setCreatedAt(sdf.parse(createdAt));
		}
		if (updatedAt != null) {
			goodwillApplyAuditDetailDTO.setUpdatedAt(sdf.parse(updatedAt));
		}
		return goodwillApplyAuditDetailService.selectPageBysql(page, goodwillApplyAuditDetailDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id
	 *            数据主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditDetailDTO
	 * <AUTHOR>
	 * @since 2020-06-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
	@GetMapping(value = "/{id}")
	public GoodwillApplyAuditDetailDTO getById(@PathVariable("id") Long id) {
		return goodwillApplyAuditDetailService.getById(id);
	}

	/**
	 * 进行数据新增
	 *
	 * @param goodwillApplyAuditDetailDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-06-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyAuditDetailDTO", name = "goodwillApplyAuditDetailDTO", value = "", required = true)
	})
	@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
	@PostMapping
	@ResponseStatus(HttpStatus.CREATED)
	public int insert(@RequestBody GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO) {
		return goodwillApplyAuditDetailService.insert(goodwillApplyAuditDetailDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id
	 *            需要修改数据的ID
	 * @param goodwillApplyAuditDetailDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-06-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true),
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyAuditDetailDTO", name = "goodwillApplyAuditDetailDTO", value = "", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
	@PutMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.CREATED)
	public int update(@PathVariable("id") Long id,
			@RequestBody GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO) {
		return goodwillApplyAuditDetailService.update(id, goodwillApplyAuditDetailDTO);
	}

	/**
	 * 根据id删除对象
	 *
	 * @param id
	 *            需要修改数据的ID
	 * <AUTHOR>
	 * @since 2020-06-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
	})
	@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteById(@PathVariable("id") Long id) {
		goodwillApplyAuditDetailService.deleteById(id);
		return true;
	}

	/**
	 * 根据IDs批量删除对象
	 *
	 * @param ids
	 *            需要修改数据的ID集合
	 * <AUTHOR>
	 * @since 2020-06-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "", required = true)
	})
	@ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/batch/{ids}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteByIds(@PathVariable("ids") String ids) {
		goodwillApplyAuditDetailService.deleteBatchIds(ids);
		return true;
	}

}