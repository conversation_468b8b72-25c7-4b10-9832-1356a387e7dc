package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscus.customer.entity.dto.goodwill.ApplyUploadFileDto;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFileInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyFileInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyFileInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
@Api(value = "/goodwillApplyFileInfo", tags = {"GoodwillApplyFileInfoController"})
@RestController
@RequestMapping("/goodwillApplyFileInfo")
public class GoodwillApplyFileInfoController {

	@Autowired
	GoodwillApplyFileInfoService goodwillApplyFileInfoService;

	/**
	 * 分页查询数据
	 *
	 * @param appId
	 *            系统ID
	 * @param ownerCode
	 *            所有者代码
	 * @param ownerParCode
	 *            所有者的父组织代码
	 * @param orgId
	 *            组织ID
	 * @param id
	 *            主键ID
	 * @param goodwillApplyId
	 *            亲善预申请单id
	 * @param fileType
	 *            附件类型
	 * @param uploadPerson
	 *            上传人
	 * @param uploadTime
	 *            上传时间
	 * @param fileName
	 *            附件名称
	 * @param url
	 *            url
	 * @param fileSize
	 *            文件大小
	 * @param isValid
	 *            是否有效
	 * @param isDeleted
	 *            是否删除:1,删除；0,未删除
	 * @param createdAt
	 *            创建时间
	 * @param updatedAt
	 *            修改时间
	 * @param applyNo
	 *            亲善单号
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @since 2020-06-05
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "byte[]", name = "goodwillApplyId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "fileType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "uploadPerson", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "Date", name = "uploadTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "fileName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "url", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "fileSize", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "Date", name = "createdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "Date", name = "updatedAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
	@GetMapping
	public IPage<GoodwillApplyFileInfoDTO> getByPage(@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "goodwillApplyId", required = false) byte[] goodwillApplyId,
			@RequestParam(value = "fileType", required = false) Integer fileType,
			@RequestParam(value = "uploadPerson", required = false) Long uploadPerson,
			@RequestParam(value = "uploadTime", required = false) Date uploadTime,
			@RequestParam(value = "fileName", required = false) String fileName,
			@RequestParam(value = "url", required = false) String url,
			@RequestParam(value = "fileSize", required = false) BigDecimal fileSize,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "createdAt", required = false) Date createdAt,
			@RequestParam(value = "updatedAt", required = false) Date updatedAt,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize) {
		Page<GoodwillApplyFileInfoPO> page = new Page(currentPage, pageSize);

		GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO = new GoodwillApplyFileInfoDTO();
		goodwillApplyFileInfoDTO.setAppId(appId);
		goodwillApplyFileInfoDTO.setOwnerCode(ownerCode);
		goodwillApplyFileInfoDTO.setOwnerParCode(ownerParCode);
		goodwillApplyFileInfoDTO.setOrgId(orgId);
		goodwillApplyFileInfoDTO.setId(id);
		goodwillApplyFileInfoDTO.setGoodwillApplyId(goodwillApplyId);
		goodwillApplyFileInfoDTO.setFileType(fileType);
		goodwillApplyFileInfoDTO.setUploadPerson(uploadPerson);
		goodwillApplyFileInfoDTO.setUploadTime(uploadTime);
		goodwillApplyFileInfoDTO.setFileName(fileName);
		goodwillApplyFileInfoDTO.setUrl(url);
		goodwillApplyFileInfoDTO.setFileSize(fileSize);
		goodwillApplyFileInfoDTO.setIsValid(isValid);
		goodwillApplyFileInfoDTO.setIsDeleted(isDeleted);
		goodwillApplyFileInfoDTO.setCreatedAt(createdAt);
		goodwillApplyFileInfoDTO.setUpdatedAt(updatedAt);
		goodwillApplyFileInfoDTO.setApplyNo(applyNo);
		return goodwillApplyFileInfoService.selectPageBysql(page, goodwillApplyFileInfoDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id
	 *            数据主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFileInfoDTO
	 * <AUTHOR>
	 * @since 2020-06-05
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
	@GetMapping(value = "/{id}")
	public GoodwillApplyFileInfoDTO getById(@PathVariable("id") Long id) {
		return goodwillApplyFileInfoService.getById(id);
	}

	/**
	 * 进行数据新增
	 *
	 * @param goodwillApplyFileInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-06-05
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyFileInfoDTO", name = "goodwillApplyFileInfoDTO", value = "", required = true)
	})
	@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
	@PostMapping
	@ResponseStatus(HttpStatus.CREATED)
	public int insert(@RequestBody GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO) {
		return goodwillApplyFileInfoService.insert(goodwillApplyFileInfoDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id
	 *            需要修改数据的ID
	 * @param goodwillApplyFileInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-06-05
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true),
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyFileInfoDTO", name = "goodwillApplyFileInfoDTO", value = "", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
	@PutMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.CREATED)
	public int update(@PathVariable("id") Long id, @RequestBody GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO) {
		return goodwillApplyFileInfoService.update(id, goodwillApplyFileInfoDTO);
	}

	/**
	 * 根据id删除对象
	 *
	 * @param id
	 *            需要修改数据的ID
	 * <AUTHOR>
	 * @since 2020-06-05
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
	})
	@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteById(@PathVariable("id") Long id) {
		goodwillApplyFileInfoService.deleteById(id);
		return true;
	}

	/**
	 * 根据IDs批量删除对象
	 *
	 * @param ids
	 *            需要修改数据的ID集合
	 * <AUTHOR>
	 * @since 2020-06-05
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "", required = true)
	})
	@ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/batch/{ids}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteByIds(@PathVariable("ids") String ids) {
		goodwillApplyFileInfoService.deleteBatchIds(ids);
		return true;
	}

	/**
	 * 进行数据新增
	 *  亲善附件上传需要保存的数据
	 * @return int
	 * <AUTHOR>
	 * @since 2020-06-5
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "ApplyUploadFileDto", name = "applyUploadFileDto", value = "", required = true)
	})
	@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
	@RequestMapping(value = "/insertApplyUploadFile", method = RequestMethod.POST)
	@ResponseStatus(HttpStatus.CREATED)
	public int insertAttachment(@RequestBody ApplyUploadFileDto applyUploadFileDto) {
		GoodwillApplyFileInfoDTO goodwillApplyFileInfoDto = new GoodwillApplyFileInfoDTO();
		goodwillApplyFileInfoDto.setFileName(applyUploadFileDto.getName());
		goodwillApplyFileInfoDto.setFileType(applyUploadFileDto.getFileType());
		goodwillApplyFileInfoDto.setApplyNo(applyUploadFileDto.getBaseDate().getOrderNo());
		goodwillApplyFileInfoDto.setFileSize(new BigDecimal(applyUploadFileDto.getBaseDate().getSize()));
		goodwillApplyFileInfoDto.setUrl(applyUploadFileDto.getUrl());
		goodwillApplyFileInfoDto.setOwnerCode(FrameworkUtil.getLoginInfo().getOwnerCode());
		return goodwillApplyFileInfoService.insert(goodwillApplyFileInfoDto);
	}

	/**
	 * 查看亲善单的附件
	 *
	 * @return List<GoodwillApplyFileInfoDTO>
	 * <AUTHOR>
	 * @since 2020-06-05
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "fileType", value = "")
	})
	@ApiOperation(value = "查看亲善单的附件", notes = "查看亲善单的附件", httpMethod = "GET")
	@RequestMapping(value = "/queryEnclosure", method = RequestMethod.GET)
	public List<GoodwillApplyFileInfoDTO> queryEnclosure(
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "fileType", required = false) Integer fileType) {
		GoodwillApplyFileInfoDTO goodwillApplyFileInfoDto = new GoodwillApplyFileInfoDTO();
		goodwillApplyFileInfoDto.setApplyNo(applyNo);
		goodwillApplyFileInfoDto.setFileType(fileType);
		return goodwillApplyFileInfoService.selectListBySql1(goodwillApplyFileInfoDto);
	}

	/**
	 * 根据id删除对象
	 *
	 * @param id
	 *            需要修改数据的ID
	 * <AUTHOR>
	 * @since 2020-04-15
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
	})
	@ApiOperation(value = "根据id逻辑删除数据", notes = "根据id删除对象", httpMethod = "POST")
	@PostMapping(value = "/deleteById/{id}")
	public void deleteByIds(@PathVariable("id") Long id) {
		goodwillApplyFileInfoService.deleteById(id);
	}

	/**
	 * 查看亲善单所有的材料附件
	 *
	 * @return List<GoodwillApplyFileInfoDTO>
	 * <AUTHOR>
	 * @since 2020-06-05
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = "")
	})
	@ApiOperation(value = "查看亲善单所有的材料附件", notes = "查看亲善单所有的材料附件", httpMethod = "GET")
	@RequestMapping(value = "/queryAllMaterialFile", method = RequestMethod.GET)
	public List<GoodwillApplyFileInfoDTO> queryAllMaterialFile(
			@RequestParam(value = "applyNo", required = false) String applyNo) {
		GoodwillApplyFileInfoDTO goodwillApplyFileInfoDto = new GoodwillApplyFileInfoDTO();
		goodwillApplyFileInfoDto.setApplyNo(applyNo);
		return goodwillApplyFileInfoService.selectMaterialUploadList(goodwillApplyFileInfoDto);
	}

}