package com.yonyou.dmscus.customer.controller.talkskill;

import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillTagDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillTagPO;
import com.yonyou.dmscus.customer.service.talkskill.TalkskillTagService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 *
 * <AUTHOR>
 * @since 2020-03-20
 */
@Api(value = "/talkskillTag", tags = {"TalkskillTagController"})
@RestController
@RequestMapping("/talkskillTag")
public class TalkskillTagController extends BaseController {
    
        @Autowired
        TalkskillTagService talkskillTagService;

        /**
         *  全量查询
         * <AUTHOR>
         * @since 2020-03-20
         */
        @ApiImplicitParams({
                @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = ""),
                @ApiImplicitParam(paramType = "query", dataType = "int", name = "level", value = ""),
                @ApiImplicitParam(paramType = "query", dataType = "int", name = "parentId", value = "")
        })
        @ApiOperation(value = "全量查询", notes = "全量查询", httpMethod = "GET")
        @GetMapping(value = "/list")
        public List<TalkskillTagPO> getList(
                @RequestParam(value="isValid",required = false) Integer isValid,
                @RequestParam(value="level",required = false ,defaultValue = "1") Integer level,
                @RequestParam(value="parentId",required = false ,defaultValue = "0") Integer parentId
        ){
            TalkskillTagDTO talkskillTagDTO = new TalkskillTagDTO();
            talkskillTagDTO.setLevel(level);
            talkskillTagDTO.setParentId(parentId);
            talkskillTagDTO.setIsValid(isValid);
            return talkskillTagService.selectListBySql(talkskillTagDTO);
        }

        /**
         * 进行数据修改
         *
         * @param id 数据主键ID
         * @return com.yonyou.dmscus.repair.entity.dto.TalkskillTagDTO
         * <AUTHOR>
         * @since 2020-03-20
         */
        @ApiImplicitParams({
                @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
        })
        @ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
        @GetMapping(value = "/{id}")
        public TalkskillTagDTO getById(@PathVariable("id") Long id){

            return talkskillTagService.getById(id);

        }

        /**
         * 进行数据新增--批量插入
         *
         * @param list<TalkskillTagDTO> 需要保存的DTO
         * @return int
         * <AUTHOR>
         * @since 2020-03-20
         */
        @ApiImplicitParams({
                @ApiImplicitParam(paramType = "body", dataType = "List<TalkskillTagDTO>", name = "list", value = "", required = true)
        })
        @ApiOperation(value = "进行数据新增--批量插入", notes = "进行数据新增--批量插入", httpMethod = "POST")
        @PostMapping
        @ResponseStatus(HttpStatus.CREATED)
        public int insert(@RequestBody List<TalkskillTagDTO> list){
            return talkskillTagService.updateList(list);
        }

        /**
         * 进行数据修改
         *
         * @param id 需要修改数据的ID
         * @param talkskillTagDTO 需要保存的DTO
         * @return int
         * <AUTHOR>
         * @since 2020-03-20
         */
        @ApiImplicitParams({
                @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
                @ApiImplicitParam(paramType = "body", dataType = "TalkskillTagDTO", name = "talkskillTagDTO", value = "需要保存的DTO", required = true)
        })
        @ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
        @PutMapping(value = "/{id}")
        @ResponseStatus(HttpStatus.CREATED)
        public int update(@PathVariable("id") Long id,@RequestBody TalkskillTagDTO talkskillTagDTO){
            return talkskillTagService.update(id,talkskillTagDTO);
        }

}