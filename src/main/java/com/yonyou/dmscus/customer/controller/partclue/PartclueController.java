package com.yonyou.dmscus.customer.controller.partclue;

import com.yonyou.dmscus.customer.entity.dto.clue.LiteCrmClueDTO;
import com.yonyou.dmscus.customer.service.partclue.PartClueService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 零附件线索
 */
@Api(value = "/partclue", tags = {"零附件线索"})
@Slf4j
@RestController
@RequestMapping("/partclue")
public class PartclueController {
    @Autowired
    private PartClueService partClueService;

    /**
     * 线索同步接口
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "LiteCrmClueDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "线索同步接口", notes = "线索同步接口", httpMethod = "POST")
    @PostMapping("/updateClueStatus")
    public boolean updateClueStatus(@RequestBody LiteCrmClueDTO dto) {
        return partClueService.updateClueStatus(dto);
    }
}
