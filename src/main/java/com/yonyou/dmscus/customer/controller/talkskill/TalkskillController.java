package com.yonyou.dmscus.customer.controller.talkskill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillPO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillPagePO;
import com.yonyou.dmscus.customer.service.talkskill.TalkskillPageService;
import com.yonyou.dmscus.customer.service.talkskill.TalkskillService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;

import org.springframework.web.bind.annotation.*;
import com.yonyou.f4.mvc.controller.BaseController;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;


/**
 *
 * <AUTHOR>
 * @since 2020-03-19
 */
@Api(value = "/talkskill", tags = {"TalkskillController"})
@RestController
@RequestMapping("/talkskill")
public class TalkskillController extends BaseController {

    @Autowired
    TalkskillService talkskillService;
    @Autowired
    TalkskillPageService talkskillPageService;
    /**
     * 分页查询数据
    */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "talkId", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "title", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "type", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "beginDate", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "endDate", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "tag1", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "tag2", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "talkskill", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "keyword1", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "keyword2", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "keyword3", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isCompel", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isEnable", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isVcdc", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isDeleted", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
    @GetMapping
    public IPage<TalkskillDTO> getByPage(
        @RequestParam(value="talkId",required = false) Long talkId,
        @RequestParam(value="dealerCode",required = false) String dealerCode,
        @RequestParam(value="title",required = false) String title,
        @RequestParam(value="type",required = false) String type,
        @RequestParam(value="beginDate",required = false) String beginDate,
        @RequestParam(value="endDate",required = false) String endDate,
        @RequestParam(value="tag1",required = false) Integer tag1,
        @RequestParam(value="tag2",required = false) Integer tag2,
        @RequestParam(value="talkskill",required = false) String talkskill,
        @RequestParam(value="keyword1",required = false) String keyword1,
        @RequestParam(value="keyword2",required = false) String keyword2,
        @RequestParam(value="keyword3",required = false) String keyword3,
        @RequestParam(value="isCompel",required = false) Integer isCompel,
        @RequestParam(value="isEnable",required = false) Integer isEnable,
        @RequestParam(value="isVcdc",required = false) Integer isVcdc,
        @RequestParam(value="dataSources",required = false) Integer dataSources,
        @RequestParam(value="isDeleted",required = false) Integer isDeleted,
        @RequestParam(value="isValid",required = false) Integer isValid,
        @RequestParam(value = "currentPage" ,defaultValue = "1" )int currentPage,
        @RequestParam(value = "pageSize" , defaultValue = "10" ) int pageSize
    ) throws ParseException {
        Page<TalkskillPO> page = new Page(currentPage,pageSize);

        TalkskillDTO talkskillDTO = new TalkskillDTO();
        talkskillDTO.setTalkId(talkId);
        talkskillDTO.setDealerCode(dealerCode);
        talkskillDTO.setTitle(title);
        talkskillDTO.setType(type);
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if(beginDate !=null){
            LocalDate localDate = LocalDate.parse(beginDate,fmt);
            talkskillDTO.setBeginDate(localDate);
        }
        if(endDate !=null){
            LocalDate localDate = LocalDate.parse(endDate,fmt);
            talkskillDTO.setEndDate(localDate);
        }
        talkskillDTO.setTag1(tag1);
        talkskillDTO.setTag2(tag2);
        talkskillDTO.setTalkskill(talkskill);
        talkskillDTO.setKeyword1(keyword1);
        talkskillDTO.setKeyword2(keyword2);
        talkskillDTO.setKeyword3(keyword3);
        talkskillDTO.setIsCompel(isCompel);
        talkskillDTO.setIsEnable(isEnable);
        talkskillDTO.setIsVcdc(isVcdc);
        talkskillDTO.setDataSources(dataSources);
        talkskillDTO.setIsDeleted(isDeleted);
        talkskillDTO.setIsValid(isValid);
        return talkskillService.selectPageBysql(page,talkskillDTO);
    }

    /**
     * 根据id查询
     *
     * @param id 数据主键ID
     * @return com.yonyou.dmscus.repari.entity.dto.TalkskillDTO
     * <AUTHOR>
     * @since 2020-03-19
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
    })
    @ApiOperation(value = "根据id查询", notes = "根据id查询", httpMethod = "GET")
    @GetMapping(value = "/{id}")
    public TalkskillDTO getById(@PathVariable("id") Long id){
            return talkskillService.getById(id);
    }

    /**
     * 进行数据新增
     *
     * @param talkskillDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-03-19
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "TalkskillDTO", name = "talkskillDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public int insert(@RequestBody TalkskillDTO talkskillDTO){

        return talkskillService.insert( talkskillDTO);
    }

    /**
     * 进行数据修改
     *
     * @param id 需要修改数据的ID
     * @param talkskillDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-03-19
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
            @ApiImplicitParam(paramType = "body", dataType = "TalkskillDTO", name = "talkskillDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
    @PutMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.CREATED)
    public int update(@PathVariable("id") Long id,@RequestBody TalkskillDTO talkskillDTO){
        return talkskillService.update(id,talkskillDTO);
    }
    /**
     * 根据id更新状态
     *
     * @param talkskillDTO 需要修改数据的ID
     * <AUTHOR>
     * @since 2020-03-19
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "TalkskillDTO", name = "talkskillDTO", value = "需要修改数据的ID", required = true)
    })
    @ApiOperation(value = "根据id更新状态", notes = "根据id更新状态", httpMethod = "PUT")
    @PutMapping(value = "/enable")
    @ResponseStatus(HttpStatus.CREATED)
    public int switchEnable(@RequestBody TalkskillDTO talkskillDTO){
        return talkskillService.updateSwitch(talkskillDTO);
    }
    /**
     * 查询使用页面
     *
     *
     * <AUTHOR>
     * @since 2020-03-19
     */
    @ApiOperation(value = "查询使用页面", notes = "查询使用页面", httpMethod = "GET")
    @GetMapping(value="/talkskillPage")
    public List<TalkskillPagePO> getPage(){
        return talkskillPageService.selectListBySql();
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "tag", value = "", required = true)
    })
    @ApiOperation(value = "查询qw-im 定制化标签", notes = "", httpMethod = "GET")
    @GetMapping(value = "/getQwimTalkskill")
    public TalkskillDTO getQwimTalkskill(
            @RequestParam(value = "dealerCode") String dealerCode,
            @RequestParam(value = "tag") String tag) {
        List<TalkskillDTO> qwimTalkskills = talkskillService.getQwimTalkskills(dealerCode, Arrays.asList(tag));
        return CollectionUtils.isEmpty(qwimTalkskills) ? null : qwimTalkskills.get(0);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "List<String>", name = "tags", value = "", required = true)
    })
    @ApiOperation(value = "批量 查询qw-im 定制化标签", notes = "", httpMethod = "GET")
    @GetMapping(value = "/getQwimTalkskills")
    public List<TalkskillDTO> getQwimTalkskills(
            @RequestParam(value = "dealerCode") String dealerCode,
            @RequestParam(value = "tags") List<String> tags) {
        return talkskillService.getQwimTalkskills(dealerCode, tags);
    }
}