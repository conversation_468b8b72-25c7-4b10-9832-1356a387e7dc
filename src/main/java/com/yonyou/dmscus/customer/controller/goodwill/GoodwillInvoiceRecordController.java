package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillInvoiceRecordDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillInvoiceRecordPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillInvoiceRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;

/**
 *
 * <AUTHOR>
 * @since 2020-06-19
 */
@Api(value = "/goodwillInvoiceRecord", tags = {"GoodwillInvoiceRecordController"})
@RestController
@RequestMapping("/goodwillInvoiceRecord")
public class GoodwillInvoiceRecordController {

	@Autowired
	GoodwillInvoiceRecordService goodwillInvoiceRecordService;

	/**
	 * 分页查询数据
	 *
	 * @param appId                系统ID
	 * @param ownerCode            所有者代码
	 * @param ownerParCode         所有者的父组织代码
	 * @param orgId                组织ID
	 * @param id                   主键ID
	 * @param goodwillApplyId      亲善单ID
	 * @param noticeInvoiceId      开票通知单ID
	 * @param invoiceNo            发票号
	 * @param invoicePrice         发票金额（含税）
	 * @param invoiceDate          开票日期
	 * @param invoiceType          发票类型
	 * @param expressCompany       快递公司
	 * @param expressNo            快递单号
	 * @param expressDate          快递日期
	 * @param receivedInvoiceDate  收到发票日期
	 * @param receivedInvoicePrice 收到发票金额
	 * @param invoiceId            开票id
	 * @param isValid              是否有效
	 * @param isDeleted            是否删除:1,删除；0,未删除
	 * @param createdAt            创建时间
	 * @param updatedAt            修改时间
	 * @param currentPage          页数
	 * @param pageSize             分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * @throws ParseException
	 * <AUTHOR>
	 * @since 2020-06-19
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = "亲善单ID"),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "noticeInvoiceId", value = "开票通知单ID"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceNo", value = "发票号"),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "invoicePrice", value = "发票金额（含税）"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceDate", value = "开票日期"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "invoiceType", value = "发票类型"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "expressCompany", value = "快递公司"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "expressNo", value = "快递单号"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "expressDate", value = "快递日期"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "receivedInvoiceDate", value = "收到发票日期"),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "receivedInvoicePrice", value = "收到发票金额"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceId", value = "开票id"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除:1,删除；0,未删除"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = "创建时间"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = "修改时间"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
	})
	@ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
	@GetMapping
	public IPage<GoodwillInvoiceRecordDTO> getByPage(@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "goodwillApplyId", required = false) Long goodwillApplyId,
			@RequestParam(value = "noticeInvoiceId", required = false) Long noticeInvoiceId,
			@RequestParam(value = "invoiceNo", required = false) String invoiceNo,
			@RequestParam(value = "invoicePrice", required = false) BigDecimal invoicePrice,
			@RequestParam(value = "invoiceDate", required = false) String invoiceDate,
			@RequestParam(value = "invoiceType", required = false) Integer invoiceType,
			@RequestParam(value = "expressCompany", required = false) String expressCompany,
			@RequestParam(value = "expressNo", required = false) String expressNo,
			@RequestParam(value = "expressDate", required = false) String expressDate,
			@RequestParam(value = "receivedInvoiceDate", required = false) String receivedInvoiceDate,
			@RequestParam(value = "receivedInvoicePrice", required = false) BigDecimal receivedInvoicePrice,
			@RequestParam(value = "invoiceId", required = false) String invoiceId,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillInvoiceRecordPO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
		goodwillInvoiceRecordDTO.setAppId(appId);
		goodwillInvoiceRecordDTO.setOwnerCode(ownerCode);
		goodwillInvoiceRecordDTO.setOwnerParCode(ownerParCode);
		goodwillInvoiceRecordDTO.setOrgId(orgId);
		goodwillInvoiceRecordDTO.setId(id);
		goodwillInvoiceRecordDTO.setGoodwillApplyId(goodwillApplyId);
		goodwillInvoiceRecordDTO.setNoticeInvoiceId(noticeInvoiceId);
		goodwillInvoiceRecordDTO.setInvoiceNo(invoiceNo);
		goodwillInvoiceRecordDTO.setInvoicePrice(invoicePrice);
		goodwillInvoiceRecordDTO.setInvoiceType(invoiceType);
		goodwillInvoiceRecordDTO.setExpressCompany(expressCompany);
		goodwillInvoiceRecordDTO.setExpressNo(expressNo);
		goodwillInvoiceRecordDTO.setReceivedInvoicePrice(receivedInvoicePrice);
		goodwillInvoiceRecordDTO.setInvoiceId(invoiceId);
		goodwillInvoiceRecordDTO.setIsValid(isValid);
		goodwillInvoiceRecordDTO.setIsDeleted(isDeleted);
		if (invoiceDate != null) {
			goodwillInvoiceRecordDTO.setInvoiceDate(sdf.parse(invoiceDate));
		}
		if (expressDate != null) {
			goodwillInvoiceRecordDTO.setExpressDate(sdf.parse(expressDate));
		}
		if (receivedInvoiceDate != null) {
			goodwillInvoiceRecordDTO.setReceivedInvoiceDate(sdf.parse(receivedInvoiceDate));
		}
		if (createdAt != null) {
			goodwillInvoiceRecordDTO.setCreatedAt(sdf.parse(createdAt));
		}
		if (updatedAt != null) {
			goodwillInvoiceRecordDTO.setUpdatedAt(sdf.parse(updatedAt));
		}
		return goodwillInvoiceRecordService.selectPageBysql(page, goodwillInvoiceRecordDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id 数据主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillInvoiceRecordDTO
	 * <AUTHOR>
	 * @since 2020-06-19
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
	@GetMapping(value = "/{id}")
	public GoodwillInvoiceRecordDTO getById(@PathVariable("id") Long id) {
		return goodwillInvoiceRecordService.getById(id);
	}

	/**
	 * 进行数据新增
	 *
	 * @param goodwillInvoiceRecordDTO 需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-06-19
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillInvoiceRecordDTO", name = "goodwillInvoiceRecordDTO", value = "需要保存的DTO", required = true)
	})
	@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
	@PostMapping
	@ResponseStatus(HttpStatus.CREATED)
	public int insert(@RequestBody GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO) {
		return goodwillInvoiceRecordService.insert(goodwillInvoiceRecordDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id                       需要修改数据的ID
	 * @param goodwillInvoiceRecordDTO 需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-06-19
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillInvoiceRecordDTO", name = "goodwillInvoiceRecordDTO", value = "需要保存的DTO", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
	@PutMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.CREATED)
	public int update(@PathVariable("id") Long id, @RequestBody GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO) {
		return goodwillInvoiceRecordService.update(id, goodwillInvoiceRecordDTO);
	}

	/**
	 * 根据id删除对象
	 *
	 * @param id 需要修改数据的ID
	 * <AUTHOR>
	 * @since 2020-06-19
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
	})
	@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteById(@PathVariable("id") Long id) {
		goodwillInvoiceRecordService.deleteById(id);
		return true;
	}

	/**
	 * 根据IDs批量删除对象
	 *
	 * @param ids 需要修改数据的ID集合
	 * <AUTHOR>
	 * @since 2020-06-19
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "需要修改数据的ID集合", required = true)
	})
	@ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/batch/{ids}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteByIds(@PathVariable("ids") String ids) {
		goodwillInvoiceRecordService.deleteBatchIds(ids);
		return true;
	}

}