package com.yonyou.dmscus.customer.controller.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCcmClassificationDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCcmClassificationPO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintCcmClassificationService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2021-06-04
 */
@Api(value = "/complaintCcmClassification", tags = {"ComplaintCcmClassificationController"})
@RestController
@RequestMapping("/complaintCcmClassification")
public class ComplaintCcmClassificationController extends BaseController {

    @Autowired
    ComplaintCcmClassificationService complaintCcmClassificationService;

    /**
     * 分页查询数据
     *
     * @param appId               系统ID
     * @param ownerCode           所有者代码
     * @param ownerParCode        所有者的父组织代码
     * @param orgId               组织ID
     * @param id                  主键ID
     * @param parentId            父级ID
     * @param classificationName  分类名称
     * @param classificationLevel
     * @param dataSources         数据来源
     * @param isDeleted           是否删除
     * @param isValid             是否有效
     * @param createdAt           创建时间
     * @param updatedAt           更新时间
     * @param currentPage         页数
     * @param pageSize            分页大小
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2021-06-04
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "parentId", value = "父级ID"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "classificationName", value = "分类名称"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "classificationLevel", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = "数据来源"),
            @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "createdAt", value = "创建时间"),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "updatedAt", value = "更新时间"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
    })
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
    @GetMapping
    public IPage<ComplaintCcmClassificationDTO> getByPage(
            @RequestParam(value = "appId", required = false) String appId,
            @RequestParam(value = "ownerCode", required = false) String ownerCode,
            @RequestParam(value = "ownerParCode", required = false) String ownerParCode,
            @RequestParam(value = "orgId", required = false) Integer orgId,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "parentId", required = false) Long parentId,
            @RequestParam(value = "classificationName", required = false) String classificationName,
            @RequestParam(value = "classificationLevel", required = false) String classificationLevel,
            @RequestParam(value = "dataSources", required = false) Integer dataSources,
            @RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
            @RequestParam(value = "isValid", required = false) Integer isValid,
            @RequestParam(value = "createdAt", required = false) Date createdAt,
            @RequestParam(value = "updatedAt", required = false) Date updatedAt,
            @RequestParam("currentPage") int currentPage,
            @RequestParam("pageSize") int pageSize) {
        Page<ComplaintCcmClassificationPO> page = new Page(currentPage, pageSize);

        ComplaintCcmClassificationDTO complaintCcmClassificationDTO = new ComplaintCcmClassificationDTO();
        complaintCcmClassificationDTO.setAppId(appId);
        complaintCcmClassificationDTO.setOwnerCode(ownerCode);
        complaintCcmClassificationDTO.setOwnerParCode(ownerParCode);
        complaintCcmClassificationDTO.setOrgId(orgId);
        complaintCcmClassificationDTO.setId(id);
        complaintCcmClassificationDTO.setParentId(parentId);
        complaintCcmClassificationDTO.setClassificationName(classificationName);
        complaintCcmClassificationDTO.setClassificationLevel(classificationLevel);
        complaintCcmClassificationDTO.setDataSources(dataSources);
        complaintCcmClassificationDTO.setIsDeleted(isDeleted);
        complaintCcmClassificationDTO.setIsValid(isValid);
        complaintCcmClassificationDTO.setCreatedAt(createdAt);
        complaintCcmClassificationDTO.setUpdatedAt(updatedAt);
        return complaintCcmClassificationService.selectPageBysql(page, complaintCcmClassificationDTO);
    }

    /**
     * 进行数据修改
     *
     * @param id 数据主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCcmClassificationDTO
     * <AUTHOR>
     * @since 2021-06-04
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
    })
    @ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
    @GetMapping(value = "/{id}")
    public ComplaintCcmClassificationDTO getById(@PathVariable("id") Long id) {
        return complaintCcmClassificationService.getById(id);
    }

    /**
     * 进行数据新增
     *
     * @param complaintCcmClassificationDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2021-06-04
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCcmClassificationDTO", name = "complaintCcmClassificationDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public int insert(@RequestBody ComplaintCcmClassificationDTO complaintCcmClassificationDTO) {
        return complaintCcmClassificationService.insert(complaintCcmClassificationDTO);
    }

    /**
     * 进行数据修改
     *
     * @param id                            需要修改数据的ID
     * @param complaintCcmClassificationDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2021-06-04
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCcmClassificationDTO", name = "complaintCcmClassificationDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
    @PutMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.CREATED)
    public int update(@PathVariable("id") Long id, @RequestBody ComplaintCcmClassificationDTO complaintCcmClassificationDTO) {
        return complaintCcmClassificationService.update(id, complaintCcmClassificationDTO);
    }

    /**
     * 根据id删除对象
     *
     * @param id 需要修改数据的ID
     * <AUTHOR>
     * @since 2021-06-04
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
    })
    @ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public boolean deleteById(@PathVariable("id") Long id) {
        complaintCcmClassificationService.deleteById(id);
        return true;
    }

    /**
     *
     * @param complaintCcmClassificationDTO
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCcmClassificationDTO", name = "complaintCcmClassificationDTO", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping(value = "/getList")
    public List<ComplaintCcmClassificationDTO> getList(@RequestBody ComplaintCcmClassificationDTO complaintCcmClassificationDTO){
        return complaintCcmClassificationService.selectListBySql(complaintCcmClassificationDTO);
    }


}