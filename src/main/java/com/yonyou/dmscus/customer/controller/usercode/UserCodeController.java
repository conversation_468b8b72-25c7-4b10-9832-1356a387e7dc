package com.yonyou.dmscus.customer.controller.usercode;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dto.UserCodeVo;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.userCode.UserCodeInfoDto;
import com.yonyou.dmscus.customer.entity.dto.userCode.UserCodeInfoImportDto;
import com.yonyou.dmscus.customer.service.usercode.UserCodeService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


/**
*
* <AUTHOR>
* @since 2020-05-22
*/
@RestController
@RequestMapping("/userauth")
@Api(value = "app用户权限认证服务")
public class UserCodeController {

    @Autowired
    UserCodeService userCodeService;


    /**
     *
     * @param vo
     * @return int
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "UserCodeVo", name = "vo", value = "", required = true)
    })
    @ApiOperation(value = "查询数据", notes = "", httpMethod = "POST")
    @PostMapping("/app")
    public int getUserCodeCount(@RequestBody UserCodeVo vo) {
        if(vo==null){
            throw  new DALException("查询参数为空");
        }
        if(StringUtils.isNullOrEmpty(vo.getUserCode())){
            throw  new DALException("查询参数为空");
        }
        return userCodeService.getOneByUerCode(vo);
    }

    /**
     * 分页查询
     * @param dto
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "UserCodeVo", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "分页查询", notes = "分页查询", httpMethod = "POST")
    @PostMapping("/page")
    public IPage<UserCodeInfoDto> selectPageBysql(@RequestBody UserCodeVo dto ){
        Page page = new Page(dto.getCurrentPage(), dto.getPageSize());
        return userCodeService.selectPageBysql(page,dto);
    }

    /**
     * d导入临时表
     *
     * @param importFile
     * @return
     * @throws Exception
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "", required = true)
    })
    @ApiOperation(value = "导入权限临时表", notes = "d导入临时表", httpMethod = "POST")
    @RequestMapping(value = "/importUserCodeTemp", method = RequestMethod.POST)
    public ImportTempResult<UserCodeInfoImportDto> importUserCodeTemp(
            @RequestParam(value = "file") MultipartFile importFile) throws Exception {
        return  userCodeService.importUserCodeTemp(importFile);
    }

    /**
     * 查询导入错误
     * @param currentPage
     * @param pageSize
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "查询导入错误", notes = "查询导入错误", httpMethod = "GET")
    @GetMapping("/listErrorPage")
    public IPage<UserCodeInfoImportDto> selectErrorPage(@RequestParam("currentPage") int currentPage,
                                                               @RequestParam("pageSize") int pageSize){
        Page<UserCodeInfoImportDto> page=new Page(currentPage, pageSize);
        return userCodeService.selectErrorPage(page);

    }


    /**
     * 临时表数据保存到正式表
     *
     * @param
     * @return
     * <AUTHOR>
     * @since 2020-03-30
     */
    @ApiOperation(value = "临时表数据保存到正式表", notes = "临时表数据保存到正式表", httpMethod = "GET")
    @GetMapping(value = "/importTableByExcel")
    public int importIsLocalWRExcel() {
        userCodeService.batchInsert();
        return 1;
    }




}