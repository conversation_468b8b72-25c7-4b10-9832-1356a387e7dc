package com.yonyou.dmscus.customer.controller.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dto.CompanyInfoDTO;
import com.yonyou.dmscus.customer.dto.ComplaintInfMoreVo;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintmoreDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintFollowDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoPO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoPO;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintInfoService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-12-10
 */
@Api(value = "/saleComplaintInfo", tags = {"SaleComplaintInfoController"})
@RestController
@RequestMapping("/saleComplaintInfo")
public class SaleComplaintInfoController extends BaseController {

    @Autowired
    SaleComplaintInfoService saleComplaintInfoService;

    /**
     * 新增经销商自建投诉单(销售)
     *
     * @param complaintmoreDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintmoreDTO", name = "complaintmoreDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "新增经销商自建投诉单(销售)", notes = "新增经销商自建投诉单(销售)", httpMethod = "POST")
    @RequestMapping(value = "/insertComplaint", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insert(@RequestBody ComplaintmoreDTO complaintmoreDTO){
        return saleComplaintInfoService.insertComplaint(complaintmoreDTO);
    }

    /**
     * 分页查询数据(销售)
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintInfMoreDTO", name = "complaintInfMoreDTO", value = "")
    })
    @ApiOperation(value = "分页查询数据(销售)", notes = "分页查询数据(销售)", httpMethod = "POST")
    @RequestMapping(value = "/selectCusByDeal", method = RequestMethod.POST)
    public IPage<ComplaintInfMoreDTO>selectCusByDeal(@RequestBody(required=false)  ComplaintInfMoreDTO complaintInfMoreDTO
    ) throws ParseException {
        Page<SaleComplaintInfoPO>page=new Page(complaintInfMoreDTO.getCurrentPage(),complaintInfMoreDTO.getPageSize());
        String user="deal";
        return saleComplaintInfoService.selectCusByDeal(page,complaintInfMoreDTO,user);
    }

    /**
     * 店端客诉，获取处理经销商下拉框数据
     * @return
     */
    @ApiOperation(value = "店端客诉，获取处理经销商下拉框数据", notes = "店端客诉，获取处理经销商下拉框数据", httpMethod = "GET")
    @GetMapping(value = "/queryDealCompanyList")
    public List<CompanyInfoDTO> queryCompanyInfoList() {

        return saleComplaintInfoService.queryCompanyInfoList();
    }

    /**
     * 客诉结案提交（销售400结案）
     *
     * @param complaintCustomFieldTestDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "complaintCustomFieldTestDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "客诉结案提交（销售400结案）", notes = "客诉结案提交（销售400结案）", httpMethod = "POST")
    @RequestMapping(value = "/insertClose", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertClose(@RequestBody ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO  ){
        String flag="82441005";
        return saleComplaintInfoService.insertClose(complaintCustomFieldTestDTO,flag);
    }
    /**
     * 客诉结案提交（销售区域结案）
     *
     * @param complaintCustomFieldTestDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "complaintCustomFieldTestDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "客诉结案提交（销售区域结案）", notes = "客诉结案提交（销售区域结案）", httpMethod = "POST")
    @RequestMapping(value = "/insertClose1", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertClose1(@RequestBody ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO  ){
        String flag="82441001";
        return saleComplaintInfoService.insertClose(complaintCustomFieldTestDTO,flag);
    }

    /**
     * 客诉结案提交（区域经理）
     *
     * @param complaintCustomFieldTestDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "complaintCustomFieldTestDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "客诉结案提交（区域经理）", notes = "客诉结案提交（区域经理）", httpMethod = "POST")
    @RequestMapping(value = "/insertRegionClose", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertRegionClose(@RequestBody ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO  ){
        return saleComplaintInfoService.insertRegionClose(complaintCustomFieldTestDTO);
    }

    /**
     * 客诉结案提交（总部）
     *
     * @param complaintCustomFieldTestDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "complaintCustomFieldTestDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "客诉结案提交（总部）", notes = "客诉结案提交（总部）", httpMethod = "POST")
    @RequestMapping(value = "/insertSaleHQClose", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertSaleHQClose(@RequestBody ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO  ){
        return saleComplaintInfoService.insertSaleHQClose(complaintCustomFieldTestDTO);
    }

    /**
     * 销售总部案件是否重启
     *
     * @param complaintCustomFieldTestDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "complaintCustomFieldTestDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "销售总部案件是否重启", notes = "销售总部案件是否重启", httpMethod = "POST")
    @RequestMapping(value = "/insertSaleRestart", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertSaleRestart(@RequestBody ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO  ){
        return saleComplaintInfoService.insertSaleRestart(complaintCustomFieldTestDTO);
    }

    /**
     * 自动重启客诉
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "自动重启客诉", notes = "自动重启客诉", httpMethod = "GET")
    @GetMapping(value = "/IssuedUpdataData")
    public void IssuedUpdataData(@RequestParam("id") Long id){
        SaleComplaintFollowDTO saleComplaintFollowDTO=new SaleComplaintFollowDTO();
        saleComplaintFollowDTO.setComplaintInfoId(id);
        saleComplaintFollowDTO.setFollowTime(new Date());
        saleComplaintFollowDTO.setFollowContent("案件重启");
        saleComplaintInfoService.IssuedUpdataData(saleComplaintFollowDTO,"厂端","重启");
    }

    /**
     *导出(店端)
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintInfMoreDTO", name = "complaintInfMoreDTO", value = "")
    })
    @ApiOperation(value = "导出(店端)", notes = "导出(店端)", httpMethod = "POST")
    @RequestMapping(value = "/selectCusByDealAll", method = RequestMethod.POST)
    public List<ComplaintInfMoreDTO> selectCusByDealAll(@RequestBody(required=false)  ComplaintInfMoreDTO complaintInfMoreDTO
    ) throws ParseException {
        String user="deal";
        return saleComplaintInfoService.selectCusByDealAll(complaintInfMoreDTO,user);
    }

    /**
     *导出(厂端)
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintInfMoreDTO", name = "complaintInfMoreDTO", value = "")
    })
    @ApiOperation(value = "导出(厂端)", notes = "导出(厂端)", httpMethod = "POST")
    @RequestMapping(value = "/selectCusByCcmAll", method = RequestMethod.POST)
    public List<ComplaintInfMoreDTO> selectCusByCcmAll(@RequestBody(required=false)  ComplaintInfMoreDTO complaintInfMoreDTO
    ) throws ParseException {
        String user=complaintInfMoreDTO.getRole();
        return saleComplaintInfoService.selectCusByDealAll(complaintInfMoreDTO,user);
    }

    /**
     *分页查询数据(店端)
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintInfMoreDTO", name = "complaintInfMoreDTO", value = "")
    })
    @ApiOperation(value = "分页查询数据(店端)", notes = "分页查询数据(店端)", httpMethod = "POST")
    @RequestMapping(value = "/selectCusByCcm", method = RequestMethod.POST)
    public IPage<ComplaintInfMoreDTO>selectCusByCcm(@RequestBody(required=false)  ComplaintInfMoreDTO complaintInfMoreDTO
    ) throws ParseException {
        Page<ComplaintInfoPO>page=new Page(complaintInfMoreDTO.getCurrentPage(),complaintInfMoreDTO.getPageSize());
        String user=complaintInfMoreDTO.getRole();
        return saleComplaintInfoService.selectCusByDeal(page,complaintInfMoreDTO,user);
    }

    /**
     *
     * 查询明细
     * @param complaintInfMoreDTO
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintInfMoreDTO", name = "complaintInfMoreDTO", value = "")
    })
    @ApiOperation(value = "查询明细", notes = "查询明细", httpMethod = "POST")
    @RequestMapping(value = "/selectSaleCusDetailById", method = RequestMethod.POST)
    public ComplaintInfMoreVo selectSaleCusDetailById(@RequestBody(required=false)  ComplaintInfMoreDTO complaintInfMoreDTO
    )  {
        return saleComplaintInfoService.selectSaleCusDetailById(complaintInfMoreDTO);
    }

    /**
     * 销售客诉单修改
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintInfMoreDTO", name = "complaintInfMoreDTO", value = "", required = true)
    })
    @ApiOperation(value = "销售客诉单修改", notes = "销售客诉单修改", httpMethod = "POST")
    @RequestMapping(value = "/updateSaleComplaint", method = RequestMethod.POST)
    public Integer updateSaleComplaint(@RequestBody ComplaintInfMoreDTO complaintInfMoreDTO
    )  {
        return saleComplaintInfoService.updateSaleComplaint(complaintInfMoreDTO);
    }



}