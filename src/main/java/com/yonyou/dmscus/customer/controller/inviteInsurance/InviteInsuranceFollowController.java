package com.yonyou.dmscus.customer.controller.inviteInsurance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.framework.RestResultResponse;
import com.yonyou.dmscloud.framework.service.excel.ExcelExportColumn;
import com.yonyou.dmscloud.framework.service.excel.ExcelGenerator;
import com.yonyou.dmscloud.function.utils.common.DateUtil;
import com.yonyou.dmscus.customer.dto.InsuranceFollowParamsDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.FullLeadsFollowDto;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleCustomerNumberDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleCustomerNumberPO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO;
import com.yonyou.dmscus.customer.service.inviteInsurance.InviteInsuranceVehicleRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @since 2020-11-10
 */
@Api(value = "/inviteInsuranceFollow", tags = {"InviteInsuranceFollowController"})
@RestController
@RequestMapping("/inviteInsuranceFollow")
public class InviteInsuranceFollowController {

    @Autowired
    InviteInsuranceVehicleRecordService inviteInsuranceVehicleRecordService;
    @Autowired
    ExcelGenerator excelGenerator;



    /**
     * 保险跟进----分页查询数据
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-06-17
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InsuranceFollowParamsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "保险跟进----分页查询数据", notes = "保险跟进----分页查询数据", httpMethod = "POST")
    @PostMapping("/selectFollowInsureRecord")
    public IPage<InviteInsuranceVehicleRecordDTO> selectFollowInsureRecord(
            @RequestBody InsuranceFollowParamsDTO dto) {
        Page<InviteInsuranceVehicleRecordPO> page = new Page(dto.getCurrentPage(), dto.getPageSize());
        InviteInsuranceVehicleRecordDTO insuranceVehicleRecordDTO = new InviteInsuranceVehicleRecordDTO();
        insuranceVehicleRecordDTO.setPlanFollowDateStart(dto.getPlanFollowDateStart());
        insuranceVehicleRecordDTO.setPlanFollowDateEnd(dto.getPlanFollowDateEnd());
        insuranceVehicleRecordDTO.setLicensePlateNum(dto.getLicensePlateNum());
        insuranceVehicleRecordDTO.setVin(dto.getVin());
        insuranceVehicleRecordDTO.setActualFollowDateStart(dto.getActualFollowDateStart());
        insuranceVehicleRecordDTO.setActualFollowDateEnd(dto.getActualFollowDateEnd());
        insuranceVehicleRecordDTO.setName(dto.getName());
        insuranceVehicleRecordDTO.setAdviseInDateStart(dto.getAdviseInDateStart());
        insuranceVehicleRecordDTO.setAdviseInDateEnd(dto.getAdviseInDateEnd());
        insuranceVehicleRecordDTO.setFollowStatusParam(dto.getFollowStatus());
        insuranceVehicleRecordDTO.setOrderStatus(dto.getOrderStatus());
        insuranceVehicleRecordDTO.setIsself(dto.getIsself());
        insuranceVehicleRecordDTO.setSaId(dto.getSaId());
        insuranceVehicleRecordDTO.setClueType(dto.getClueType());
        insuranceVehicleRecordDTO.setInsuranceType(dto.getInsuranceType());
        insuranceVehicleRecordDTO.setInsuranceName(dto.getInsuranceName());
        // add on 2024-07-03
        insuranceVehicleRecordDTO.setAdviseInDate(DateUtil.parseDate(dto.getAdviseInDate(), "yyyy-MM-dd"));
        insuranceVehicleRecordDTO.setInsuranceType(dto.getInsuranceType());
        insuranceVehicleRecordDTO.setInsuranceName(dto.getInsuranceName());
        insuranceVehicleRecordDTO.setFactoryInsuranceExpiryDate(dto.getFactoryInsuranceExpiryDate());
        insuranceVehicleRecordDTO.setSuggestedCareDate(dto.getSuggestedCareDate());
        insuranceVehicleRecordDTO.setClueDataSource(dto.getClueDataSource());
        insuranceVehicleRecordDTO.setClueIssuanceType(dto.getClueIssuanceType());
        insuranceVehicleRecordDTO.setHasInstoreUpgrade(dto.getHasInstoreUpgrade());
        insuranceVehicleRecordDTO.setHasInstoreModified(dto.getHasInstoreModified());
        insuranceVehicleRecordDTO.setFactoryInsuranceExpiryDateStart(dto.getFactoryInsuranceExpiryDateStart());
        insuranceVehicleRecordDTO.setFactoryInsuranceExpiryDateEnd(dto.getFactoryInsuranceExpiryDateEnd());
        insuranceVehicleRecordDTO.setSuggestedCareDateStart(dto.getSuggestedCareDateStart());
        insuranceVehicleRecordDTO.setSuggestedCareDateEnd(dto.getSuggestedCareDateEnd());
        return inviteInsuranceVehicleRecordService.selectFollowInsureRecord(page, insuranceVehicleRecordDTO);
    }

    /**
     * 保存跟进
     *
     * <AUTHOR>
     * @since 2020-06-17
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteInsuranceVehicleRecordDetailDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "保存跟进", notes = "保存跟进", httpMethod = "POST")
    @PostMapping("/saveFollowInsureRecord")
    public int saveFollowInsureRecord(@RequestBody InviteInsuranceVehicleRecordDetailDTO dto) {
        return inviteInsuranceVehicleRecordService.saveFollowInsureRecord(dto);
    }

    /**
     * 导出 ---- 保险跟进
     * @param request
     * @param response
     * @throws Exception
     * <AUTHOR>
     *
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InsuranceFollowParamsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "导出 ---- 保险跟进", notes = "导出 ---- 保险跟进", httpMethod = "POST")
    @RequestMapping(value = "/exportFollowInsureExcel", method = RequestMethod.POST)
    @ResponseBody
    public void exportFollowInsureExcel(
            @RequestBody InsuranceFollowParamsDTO dto,
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        InviteInsuranceVehicleRecordDTO insuranceVehicleRecordDTO = new InviteInsuranceVehicleRecordDTO();
        insuranceVehicleRecordDTO.setPlanFollowDateStart(dto.getPlanFollowDateStart());
        insuranceVehicleRecordDTO.setPlanFollowDateEnd(dto.getPlanFollowDateEnd());
        insuranceVehicleRecordDTO.setLicensePlateNum(dto.getLicensePlateNum());
        insuranceVehicleRecordDTO.setVin(dto.getVin());
        insuranceVehicleRecordDTO.setActualFollowDateStart(dto.getActualFollowDateStart());
        insuranceVehicleRecordDTO.setActualFollowDateEnd(dto.getActualFollowDateEnd());
        insuranceVehicleRecordDTO.setName(dto.getName());
        insuranceVehicleRecordDTO.setAdviseInDateStart(dto.getAdviseInDateStart());
        insuranceVehicleRecordDTO.setAdviseInDateEnd(dto.getAdviseInDateEnd());
        insuranceVehicleRecordDTO.setFollowStatusParam(dto.getFollowStatus());
        insuranceVehicleRecordDTO.setOrderStatus(dto.getOrderStatus());
        insuranceVehicleRecordDTO.setIsself(dto.getIsself());
        insuranceVehicleRecordDTO.setSaId(dto.getSaId());
        insuranceVehicleRecordDTO.setClueType(dto.getClueType());
        insuranceVehicleRecordDTO.setInsuranceType(dto.getInsuranceType());
        List<Map> inviteVehicleRecordList = inviteInsuranceVehicleRecordService.exportExcelFollowInsure
                (insuranceVehicleRecordDTO);
        List<ExcelExportColumn> exportColumnList = new ArrayList<ExcelExportColumn>();
        exportColumnList.add(new ExcelExportColumn("name", "客户姓名"));
        exportColumnList.add(new ExcelExportColumn("vin", "VIN"));
        exportColumnList.add(new ExcelExportColumn("licensePlateNum", "车牌号"));
        exportColumnList.add(new ExcelExportColumn("clueType", "线索类型"));
        exportColumnList.add(new ExcelExportColumn("insuranceType", "续保客户类型"));
        exportColumnList.add(new ExcelExportColumn("followStatusName", "跟进状态"));
        exportColumnList.add(new ExcelExportColumn("adviseInDate", "续保到期日期","yyyy-MM-dd"));
        //exportColumnList.add(new ExcelExportColumn("newAdviseInDate", "新建议进厂日期","yyyy-MM-dd HH:mm:ss"));
        //exportColumnList.add(new ExcelExportColumn("newestAdviseInDate", "最新进厂日期","yyyy-MM-dd HH:mm:ss"));
        //exportColumnList.add(new ExcelExportColumn("lastInDate", "上次跟进日期","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("planFollowDate", "计划跟进日期","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("actualFollowDate", "实际跟进日期","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("orderStatus", "线索完成状态"));
        exportColumnList.add(new ExcelExportColumn("saName", "跟进人员"));
        //exportColumnList.add(new ExcelExportColumn("isBook", "是否有预约单"));
        exportColumnList.add(new ExcelExportColumn("insuranceStatus", "投保单状态"));
        exportColumnList.add(new ExcelExportColumn("isJointGuarantee", "投保单是否联保"));
        exportColumnList.add(new ExcelExportColumn("content", "跟进内容"));
        exportColumnList.add(new ExcelExportColumn("loseReason", "失败原因"));
        exportColumnList.add(new ExcelExportColumn("followTotal", "跟进次数"));
        //exportColumnList.add(new ExcelExportColumn("lastSaName", "上次SA"));

        Map<String, List<Map>> excelData = new HashMap<String, List<Map>>(16);
        excelData.put("保险跟进", inviteVehicleRecordList);
        Map<String, List<ExcelExportColumn>> ColumnMap = new HashMap<String, List<ExcelExportColumn>>(16);
        ColumnMap.put("保险跟进", exportColumnList);
        excelGenerator.generateExcelSheet(excelData, ColumnMap, "保险跟进导出.xls", request, response);
    }


    /**
     * 保险跟进失败 -- 计划任务
     *
     * <AUTHOR>
     * @since 2020-08-31
     */
    @ApiOperation(value = "保险跟进失败 -- 计划任务", notes = "保险跟进失败 -- 计划任务", httpMethod = "GET")
    @GetMapping("/updateInsureFollowStatus")
    public void updateInsureFollowStatus() {
        //inviteInsuranceVehicleRecordService.updateInsureFollowStatus();
        inviteInsuranceVehicleRecordService.updateRecordOrderStatus();
       // inviteInsuranceVehicleRecordService.updateRecordOrderStatusNew();
    }

    /**
     * 查询未分配数量
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InsuranceFollowParamsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "查询未分配数量", notes = "查询未分配数量", httpMethod = "POST")
    @PostMapping("/getNeedDistribute")
    public Integer getNeedDistribute(@RequestBody InsuranceFollowParamsDTO dto){
        return inviteInsuranceVehicleRecordService.getNeedDistribute(dto.getLeaveIds());
    }

    /**
     * 查询邀约线索及子线索
     *
     * @param id
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "查询邀约线索及子线索", notes = "查询邀约线索及子线索", httpMethod = "GET")
    @GetMapping("/getInviteInsuranceVehicleRecordInfo")
    public List<InviteInsuranceVehicleRecordDTO> getInviteInsuranceVehicleRecordInfo(
            @RequestParam(value = "vin", required = true) String vin,
            @RequestParam(value = "id", required = true) Long id) {
        return inviteInsuranceVehicleRecordService.getInviteInsuranceVehicleRecordInfo(vin, id);
    }
    
    /**
     * 保存呼叫登记
     * @param saCustomerNumberDTO
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteInsuranceVehicleCustomerNumberDTO", name = "saCustomerNumberDTO", value = "", required = true)
    })
    @ApiOperation(value = "保存呼叫登记", notes = "保存呼叫登记", httpMethod = "POST")
    @PostMapping("/saveSaCustomerNumber")
    @ResponseStatus(HttpStatus.CREATED)
    public String saveSaCustomerNumber(@RequestBody InviteInsuranceVehicleCustomerNumberDTO saCustomerNumberDTO) {
        return inviteInsuranceVehicleRecordService.saveSaCustomerNumber(saCustomerNumberDTO);
    }

    /**
     * 补完呼叫登记
     *
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<FullLeadsFollowDto>", name = "followList", value = "", required = true)
    })
    @ApiOperation(value = "补完呼叫登记", notes = "补完呼叫登记", httpMethod = "POST")
    @PostMapping("fixSaCustomerNumber")
    public void fixSaCustomerNumer(@RequestBody List<FullLeadsFollowDto> followList){
        inviteInsuranceVehicleRecordService.fixSaCustomerNumber(followList);
    }
    
    /**
     * 查看通话详情
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "insuranceDetailId", value = "", required = true)
    })
    @ApiOperation(value = "查看通话详情", notes = "查看通话详情", httpMethod = "GET")
    @GetMapping("/callDetailList/{insuranceDetailId}")
    public List<CallDetailsPO> callDetailList(@PathVariable("insuranceDetailId") Long insuranceDetailId) {
        return inviteInsuranceVehicleRecordService.callDetailList(insuranceDetailId);
    }
    
    /**
     * 查看跟进人信息
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "查看跟进人信息", notes = "查看跟进人信息", httpMethod = "GET")
    @GetMapping("/cusList/{insuranceId}")
    public List<InviteInsuranceVehicleCustomerNumberPO> selectCusList(@PathVariable("insuranceId") Long id) {
        return inviteInsuranceVehicleRecordService.selectCusList(id);
    }

    /**
     * 查询邀约线索及子线索
     *
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "查询邀约线索及子线索", notes = "查询邀约线索及子线索", httpMethod = "GET")
    @GetMapping("/queryInsuranceUserByVin")
    public InviteInsuranceVehicleCustomerNumberPO queryInsuranceUserByVin(
            @RequestParam(value = "vin", required = true) String vin) {
        return inviteInsuranceVehicleRecordService.selectInsuranceUserByVin(vin);
    }

    /**
     * 保存呼叫登记
     * @param saCustomerNumberDTO
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteInsuranceVehicleCustomerNumberDTO", name = "saCustomerNumberDTO", value = "", required = true)
    })
    @ApiOperation(value = "保存呼叫登记", notes = "保存呼叫登记", httpMethod = "POST")
    @PostMapping("/fullLeadSaveSaCustomerNumber")
    @ResponseStatus(HttpStatus.CREATED)
    public RestResultResponse fullLeadSaveSaCustomerNumber(@RequestBody InviteInsuranceVehicleCustomerNumberDTO saCustomerNumberDTO) {

        return new RestResultResponse<>().data(inviteInsuranceVehicleRecordService.saveSaCustomerNumber(saCustomerNumberDTO));
    }
}