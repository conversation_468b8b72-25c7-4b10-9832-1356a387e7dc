package com.yonyou.dmscus.customer.controller.complaint.sale;

import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.AttachmentDto;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintAttachmentDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoPO;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintAttachmentService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-12-11
 */
@Api(value = "/saleComplaintAttachment", tags = {"SaleComplaintAttachmentController"})
@RestController
@RequestMapping("/saleComplaintAttachment" )
public class SaleComplaintAttachmentController extends BaseController {

    @Autowired
    SaleComplaintAttachmentService saleComplaintAttachmentService;

    @Resource
    SaleComplaintInfoMapper saleComplaintInfoMapper;

    /**
     * 进行数据新增
     *
     * @param attachmentDto 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-03-25
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AttachmentDto", name = "attachmentDto", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
    @RequestMapping(value = "/insertAttachment", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertAttachment(@RequestBody AttachmentDto attachmentDto){
        SaleComplaintAttachmentDTO saleComplaintAttachmentDTO = new SaleComplaintAttachmentDTO();
        saleComplaintAttachmentDTO.setAttachmentName(attachmentDto.getName());
        SaleComplaintInfoDTO saleComplaintInfoDTO=new SaleComplaintInfoDTO();
        saleComplaintInfoDTO.setComplaintId(attachmentDto.getBaseDate().getOrderNo());
        List<SaleComplaintInfoPO> queryid=saleComplaintInfoMapper.queryid(saleComplaintInfoDTO);
        if(queryid.size()!=0){
            saleComplaintAttachmentDTO.setComplaintInfoId(queryid.get(0).getId());
        }else {
            saleComplaintAttachmentDTO.setComplaintInfoId(0L);
        }


        saleComplaintAttachmentDTO.setComplaintNo(attachmentDto.getBaseDate().getOrderNo());
        saleComplaintAttachmentDTO.setSize(attachmentDto.getBaseDate().getSize());
        saleComplaintAttachmentDTO.setUrl(attachmentDto.getRes().getUrl());
        saleComplaintAttachmentDTO.setOwnerCode(FrameworkUtil.getLoginInfo().getOwnerCode());

        return saleComplaintAttachmentService.insert(saleComplaintAttachmentDTO);
    }

    /**
     * 查看经销商自建投诉单中的附件
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintNo", value = "")
    })
    @ApiOperation(value = "查看经销商自建投诉单中的附件", notes = "查看经销商自建投诉单中的附件", httpMethod = "GET")
    @RequestMapping(value = "/queryEnclosure", method = RequestMethod.GET)
    public List<ComplaintAttachmentTestDTO> queryEnclosure(
            @RequestParam(value = "complaintNo", required = false) String complaintNo) {
        SaleComplaintAttachmentDTO saleComplaintAttachmentDTO = new SaleComplaintAttachmentDTO();
        saleComplaintAttachmentDTO.setComplaintNo(complaintNo);

        return saleComplaintAttachmentService.selectListBySql1(saleComplaintAttachmentDTO);
    }

    /**
     * 根据id删除对象
     *
     * @param id 需要修改数据的ID
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
    })
    @ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
    @DeleteMapping(value = "/deleteById/{id}")
    @ResponseStatus(HttpStatus.CREATED)
    public boolean deleteById(@PathVariable("id") Long id) {
        SaleComplaintAttachmentDTO saleComplaintAttachmentDTO = new SaleComplaintAttachmentDTO();
        saleComplaintAttachmentDTO.setIsDeleted(true);
        saleComplaintAttachmentService.update(id, saleComplaintAttachmentDTO);
        return true;
    }


}