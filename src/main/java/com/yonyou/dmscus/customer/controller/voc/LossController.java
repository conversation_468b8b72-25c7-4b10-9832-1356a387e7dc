package com.yonyou.dmscus.customer.controller.voc;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dto.LossDataRecordOss;
import com.yonyou.dmscus.customer.dto.LossDataRecordVo;
import com.yonyou.dmscus.customer.entity.dto.loss.LossDataRecordDto;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.LossDataRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @since 2020-04-29
 */
@RestController
@RequestMapping("/loss")
@Api(value = "流失客户报表")
public class LossController {

    @Autowired
    LossDataRecordService lossDataRecordService;


    /**
     *
     * 分页查询数据
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @throws ParseException
     * @since 2020-05-22
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "LossDataRecordVo", name = "vo", value = "", required = true)
    })
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "POST")
    @PostMapping("/page")
    public IPage<LossDataRecordDto> getByPage(
            @RequestBody   LossDataRecordVo vo
           ) {
        Page<LossDataRecordVo> page=new Page(vo.getCurrentPage(),vo.getPageSize());
        return lossDataRecordService.selectPageBysql(page,vo);
    }

    //导出
    /**
     * 导出
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "LossDataRecordOss", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "导出", notes = "导出", httpMethod = "POST")
    @PostMapping("/exportExcel")
    public void exportExcel(@RequestBody LossDataRecordOss dto){
        lossDataRecordService.exportExcelOss(dto);

    }

    /**
     * 导出
     */
    @ApiOperation(value = "导出", notes = "导出", httpMethod = "GET")
    @GetMapping("/exportExcel/oss")
    public List<Map> exportExcelOss(LossDataRecordVo dto){
        return lossDataRecordService.exportExcel(dto);
    }


}
