package com.yonyou.dmscus.customer.controller.complaint;

import com.yonyou.dmscus.customer.entity.dto.complaint.AcceptComlaintData.AcceptNewComplaintDTO;
import com.yonyou.dmscus.customer.service.complaint.AcceptComplaintService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "/acceptComplaintData", tags = {"接受400下发的回访结果"})
@RestController
@RequestMapping("/acceptComplaintData")
public class AcceptComplaintDataController extends BaseController {


    @Autowired
    AcceptComplaintService acceptComplaintService;
    /**
     * 接受400下发客诉单
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AcceptNewComplaintDTO", name = "acceptNewComplaintDTO", value = "", required = true)
    })
    @ApiOperation(value = "接受400下发客诉单", notes = "接受400下发客诉单", httpMethod = "POST")
    @RequestMapping(value = "/acceptNewComplaint", method = RequestMethod.POST)
    public  int acceptNewComplaint(@RequestBody AcceptNewComplaintDTO acceptNewComplaintDTO){

         return acceptComplaintService.acceptNewComplaint(acceptNewComplaintDTO);
    }

    /**
     * 接受400下发的回访结果
     */



}
