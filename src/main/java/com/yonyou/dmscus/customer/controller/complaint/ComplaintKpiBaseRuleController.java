package com.yonyou.dmscus.customer.controller.complaint;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiSetDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRulePO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintKpiBaseRuleService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;


/**
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
@Api(value = "/complaintKpiBaseRule", tags = {"ComplaintKpiBaseRuleController"})
@RestController
@RequestMapping("/complaintKpiBaseRule")
                public class ComplaintKpiBaseRuleController extends BaseController {
    
        @Autowired
        ComplaintKpiBaseRuleService complaintKpiBaseRuleService;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码 
        * @param ownerParCode 所有者的父组织代码 
        * @param orgId 组织ID
        * @param id 主键ID
        * @param kpiName KPI指标名称
        * @param kpi KPI指标
        * @param indexValue 指标值
        * @param ruleName 规则名称
        * @param rule 规则
        * @param score 权重（分）
        * @param formula 指标计算公式
        * @param dataSources 数据来源 
        * @param isDeleted 是否删除
        * @param isValid 是否有效 
        * @param createdAt 创建时间
        * @param updatedAt 更新时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @since 2020-04-14
        */
                @ApiImplicitParams({
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "kpiName", value = "KPI指标名称"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "kpi", value = "KPI指标"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "indexValue", value = "指标值"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ruleName", value = "规则名称"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "rule", value = "规则"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "score", value = "权重（分）"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "formula", value = "指标计算公式"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = "数据来源"),
                        @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "createdAt", value = "创建时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "updatedAt", value = "更新时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
                })
                @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
                @GetMapping
        public IPage<ComplaintKpiBaseRuleDTO>getByPage(
@RequestParam(value="appId",required = false) String appId,
@RequestParam(value="ownerCode",required = false) String ownerCode,
@RequestParam(value="ownerParCode",required = false) String ownerParCode,
@RequestParam(value="orgId",required = false) Integer orgId,
@RequestParam(value="id",required = false) Long id,
@RequestParam(value="kpiName",required = false) String kpiName,
@RequestParam(value="kpi",required = false) Integer kpi,
@RequestParam(value="indexValue",required = false) String indexValue,
@RequestParam(value="ruleName",required = false) String ruleName,
@RequestParam(value="rule",required = false) Integer rule,
@RequestParam(value="score",required = false) Integer score,
@RequestParam(value="formula",required = false) String formula,
@RequestParam(value="dataSources",required = false) Integer dataSources,
@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
@RequestParam(value="isValid",required = false) Integer isValid,
@RequestParam(value="createdAt",required = false) Date createdAt,
@RequestParam(value="updatedAt",required = false) Date updatedAt,
        @RequestParam("currentPage")int currentPage,
        @RequestParam("pageSize")int pageSize){
        Page<ComplaintKpiBaseRulePO>page=new Page(currentPage,pageSize);

        ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO =new ComplaintKpiBaseRuleDTO();
                                            complaintKpiBaseRuleDTO.setAppId(appId);
                                            complaintKpiBaseRuleDTO.setOwnerCode(ownerCode);
                                            complaintKpiBaseRuleDTO.setOwnerParCode(ownerParCode);
                                            complaintKpiBaseRuleDTO.setOrgId(orgId);
                                            complaintKpiBaseRuleDTO.setId(id);
                                            complaintKpiBaseRuleDTO.setKpiName(kpiName);
                                            complaintKpiBaseRuleDTO.setKpi(kpi);
                                            complaintKpiBaseRuleDTO.setIndexValue(indexValue);
                                            complaintKpiBaseRuleDTO.setRuleName(ruleName);
                                            complaintKpiBaseRuleDTO.setRule(rule);
                                            complaintKpiBaseRuleDTO.setScore(score);
                                            complaintKpiBaseRuleDTO.setFormula(formula);
                                            complaintKpiBaseRuleDTO.setDataSources(dataSources);
                                            complaintKpiBaseRuleDTO.setIsDeleted(isDeleted);
                                            complaintKpiBaseRuleDTO.setIsValid(isValid);
                                            complaintKpiBaseRuleDTO.setCreatedAt(createdAt);
                                            complaintKpiBaseRuleDTO.setUpdatedAt(updatedAt);
                return complaintKpiBaseRuleService.selectPageBysql(page,complaintKpiBaseRuleDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleDTO
 * <AUTHOR>
 * @since 2020-04-14
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public ComplaintKpiBaseRuleDTO getById(@PathVariable("id") Long id){
        return complaintKpiBaseRuleService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param complaintKpiBaseRuleDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-14
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintKpiBaseRuleDTO", name = "complaintKpiBaseRuleDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO){
        return complaintKpiBaseRuleService.insert( complaintKpiBaseRuleDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param complaintKpiBaseRuleDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-14
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintKpiBaseRuleDTO", name = "complaintKpiBaseRuleDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO){
        return complaintKpiBaseRuleService.update(id,complaintKpiBaseRuleDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-04-14
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    complaintKpiBaseRuleService.deleteById(id);
        return true;
        }

    /**
     * 查询数据KPL指标
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-14
     */
    @ApiOperation(value = "查询数据KPL指标", notes = "查询数据KPL指标", httpMethod = "GET")
    @RequestMapping(value = "/selectKPI", method = RequestMethod.GET)
    public List<ComplaintKpiBaseRuleDTO> selectKpi(){
        ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO=new ComplaintKpiBaseRuleDTO();
        return complaintKpiBaseRuleService.selectListBySql(complaintKpiBaseRuleDTO);
    }

    /**
     * 管理员修改KPI
     *
     * @return
     * <AUTHOR>
     * @since 2020-04-14
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<ComplaintKpiSetDTO>", name = "list", value = "", required = true)
    })
    @ApiOperation(value = "管理员修改KPI", notes = "管理员修改KPI", httpMethod = "POST")
    @RequestMapping(value = "/updateKPI", method = RequestMethod.POST)
    public int updateKpi(@RequestBody List<ComplaintKpiSetDTO> list){

        return complaintKpiBaseRuleService.updateKpi(list);
    }


}