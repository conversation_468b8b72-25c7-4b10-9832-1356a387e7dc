package com.yonyou.dmscus.customer.controller.complaint;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.service.excel.ExcelExportColumn;
import com.yonyou.dmscloud.framework.service.excel.ExcelGenerator;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintmoreDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfMorePO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoPO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintInfoService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Api(value = "/complaintInfo", tags = {"ComplaintInfoController"})
@RestController
@RequestMapping("/complaintInfo")
                public class ComplaintInfoController extends BaseController {
    
        @Autowired
        ComplaintInfoService complaintInfoService;
    @Autowired
    ExcelGenerator excelGenerator;
    /**
     * 日志对象
     */
    private final Logger logger = LoggerFactory.getLogger(this.getClass());


/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfoDTO
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public ComplaintInfoDTO getById(@PathVariable("id") Long id){
        return complaintInfoService.getById(id);
        }

/**
 * 新增经销商自建投诉单
 *
 * @param complaintmoreDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintmoreDTO", name = "complaintmoreDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "新增经销商自建投诉单", notes = "新增经销商自建投诉单", httpMethod = "POST")
@RequestMapping(value = "/insertComplaint", method = RequestMethod.POST)
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody ComplaintmoreDTO complaintmoreDTO){
        return complaintInfoService.insertComplaint(complaintmoreDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param complaintInfoDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintInfoDTO", name = "complaintInfoDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody ComplaintInfoDTO complaintInfoDTO){
        return complaintInfoService.update(id,complaintInfoDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    complaintInfoService.deleteById(id);
        return true;
        }


    /**
     * 获取客诉单号
     *
     * @param
     * @return String
     * <AUTHOR>
     * @date 2019年9月17日
     */
    @ApiOperation(value = "获取客诉单号", notes = "获取客诉单号", httpMethod = "GET")
    @RequestMapping(value = "/getbillNo",method = RequestMethod.GET)
    public String queryBookingLimit() throws ServiceBizException {
        String dearCold= FrameworkUtil.getLoginInfo().getOwnerCode();
        return complaintInfoService.getBillNo("TS",dearCold);
    }
    /**
     * 客诉结案提交（店端）
     *
     * @param complaintCustomFieldTestDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "complaintCustomFieldTestDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "客诉结案提交（店端）", notes = "客诉结案提交（店端）", httpMethod = "POST")
    @RequestMapping(value = "/insertClose", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertClose(@RequestBody ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO  ){
        String flag="82441005";
        return complaintInfoService.insertClose(complaintCustomFieldTestDTO,flag);
    }
    /**
     * 客诉结案提交（400下发）
     *
     * @param complaintCustomFieldTestDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "complaintCustomFieldTestDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "客诉结案提交（400下发）", notes = "客诉结案提交（400下发）", httpMethod = "POST")
    @RequestMapping(value = "/insertClose1", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertClose1(@RequestBody ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO  ){
        String flag="82441001";
        return complaintInfoService.insertClose(complaintCustomFieldTestDTO,flag);
    }
    /**
     * 分页查询数据(店端)
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintInfMoreDTO", name = "complaintInfMoreDTO", value = "")
    })
    @ApiOperation(value = "分页查询数据(店端)", notes = "分页查询数据(店端)", httpMethod = "POST")
    @RequestMapping(value = "/selectCusByDeal", method = RequestMethod.POST)
    public IPage<ComplaintInfMoreDTO>selectCusByDeal(@RequestBody(required=false)  ComplaintInfMoreDTO complaintInfMoreDTO
            ) throws ParseException {
        Page<ComplaintInfoPO>page=new Page(complaintInfMoreDTO.getCurrentPage(),complaintInfMoreDTO.getPageSize());
        String user="deal";
        return complaintInfoService.selectCusByDeal(page,complaintInfMoreDTO,user);
    }
    /**
     *分页查询数据(CCM)
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintInfMoreDTO", name = "complaintInfMoreDTO", value = "")
    })
    @ApiOperation(value = "分页查询数据(CCM)", notes = "分页查询数据(CCM)", httpMethod = "POST")
    @RequestMapping(value = "/selectCusByCcm", method = RequestMethod.POST)
    public IPage<ComplaintInfMoreDTO>selectCusByCcm(@RequestBody(required=false)  ComplaintInfMoreDTO complaintInfMoreDTO
    ) throws ParseException {
        Page<ComplaintInfoPO>page=new Page(complaintInfMoreDTO.getCurrentPage(),complaintInfMoreDTO.getPageSize());
        //权限CCM
       String user=complaintInfMoreDTO.getRole();
        //权限区域经理
//        String user="region";
        //权限协助部门
//        String user="assisDepartment";
        return complaintInfoService.selectCusByDeal(page,complaintInfMoreDTO,user);
    }
    /**
     * 分页查询数据(店端)
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintInfMoreDTO", name = "complaintInfMoreDTO", value = "")
    })
    @ApiOperation(value = "分页查询数据(店端)", notes = "分页查询数据(店端)", httpMethod = "POST")
    @RequestMapping(value = "/selectCusByDealAll", method = RequestMethod.POST)
    public void selectCusByDealAll(@RequestBody(required=false)  ComplaintInfMoreDTO complaintInfMoreDTO,
                                                        HttpServletRequest request, HttpServletResponse response
    ) throws ParseException {
        String user="deal";
        //准备导出数据
        List<Map> groupDataList = complaintInfoService.selectCusByDealAll(complaintInfMoreDTO,user);
        //构建Excel相关对象
        List<ExcelExportColumn> exportColumnList = new ArrayList<ExcelExportColumn>();
        exportColumnList.add(new ExcelExportColumn("ccm_man", "CCM"));
        exportColumnList.add(new ExcelExportColumn("region", "区域"));
        exportColumnList.add(new ExcelExportColumn("region_manager", "区域经理"));
        exportColumnList.add(new ExcelExportColumn("ccm_is_read", "CCM是否已经跟进"));
        exportColumnList.add(new ExcelExportColumn("call_time", "投诉时间"));
        exportColumnList.add(new ExcelExportColumn("complaint_id", "投诉ID"));
        exportColumnList.add(new ExcelExportColumn("work_order_status", "工单状态"));
        exportColumnList.add(new ExcelExportColumn("newest_restart_time", "重启时间"));
        exportColumnList.add(new ExcelExportColumn("category3", "投诉分类三层级"));
        exportColumnList.add(new ExcelExportColumn("source", "投诉来源"));
        exportColumnList.add(new ExcelExportColumn("bloc", "处理经销商集团"));
        exportColumnList.add(new ExcelExportColumn("dealer_code", "处理经销商代码"));
        exportColumnList.add(new ExcelExportColumn("dealer_name", "处理经销商名称"));
        exportColumnList.add(new ExcelExportColumn("buy_bloc", "购车经销商集团"));
        exportColumnList.add(new ExcelExportColumn("buy_dealer_code", "购车经销商代码"));
        exportColumnList.add(new ExcelExportColumn("buy_dealer_name", "购车经销商名称"));
        exportColumnList.add(new ExcelExportColumn("model_name", "车型"));
        exportColumnList.add(new ExcelExportColumn("model_year", "年款"));
        exportColumnList.add(new ExcelExportColumn("vin", "VIN"));
        exportColumnList.add(new ExcelExportColumn("license_plate_num", "车牌"));
        exportColumnList.add(new ExcelExportColumn("call_name", "客户姓名"));
        exportColumnList.add(new ExcelExportColumn("call_tel", "来电手机"));
        exportColumnList.add(new ExcelExportColumn("buy_time", "购车日期"));
        exportColumnList.add(new ExcelExportColumn("mileage", "里程"));
        exportColumnList.add(new ExcelExportColumn("ccm_subject", "CCM主题"));
        exportColumnList.add(new ExcelExportColumn("big_class_name", "5日未结案原因(大类)"));
        exportColumnList.add(new ExcelExportColumn("small_class_name", "5日未结案原因(小类)"));
        exportColumnList.add(new ExcelExportColumn("subject", "主题"));
        exportColumnList.add(new ExcelExportColumn("keyword", "关键字"));
        exportColumnList.add(new ExcelExportColumn("type", "投诉类型"));
//        exportColumnList.add(new ExcelExportColumn("ccm_part", "CCM部位"));
//        exportColumnList.add(new ExcelExportColumn("ccm_subdivision_part", "CCM细分部位"));
//        exportColumnList.add(new ExcelExportColumn("cc_main_reason", "CCM主要原因"));
//        exportColumnList.add(new ExcelExportColumn("cc_result", "CCM解决结果"));
        exportColumnList.add(new ExcelExportColumn("classification1", "分类一"));
        exportColumnList.add(new ExcelExportColumn("classification2", "分类二"));
        exportColumnList.add(new ExcelExportColumn("classification3", "分类三"));
        exportColumnList.add(new ExcelExportColumn("classification4", "分类四"));
        exportColumnList.add(new ExcelExportColumn("classification5", "分类五"));
        exportColumnList.add(new ExcelExportColumn("classification6", "分类六"));
        exportColumnList.add(new ExcelExportColumn("problem", "问题描述"));
        exportColumnList.add(new ExcelExportColumn("follow_time", "CCM跟进时间"));
        exportColumnList.add(new ExcelExportColumn("ccm_follow_content", "CCM跟进详情"));
        exportColumnList.add(new ExcelExportColumn("status", "最新状态"));
        exportColumnList.add(new ExcelExportColumn("follow_content", "工单跟进"));
        exportColumnList.add(new ExcelExportColumn("dealer_fisrt_reply_time", "经销商首次回复时间"));
        exportColumnList.add(new ExcelExportColumn("close_case_time", "结案时间"));
        exportColumnList.add(new ExcelExportColumn("ccm_system_assisted_processing", "CCM系统协助处理"));
        exportColumnList.add(new ExcelExportColumn("again_complaint_number", "投诉次数"));
        exportColumnList.add(new ExcelExportColumn("revisit_content", "回访需求"));
        Map<String, List<Map>> excelData = new HashMap<String, List<Map>>(16);
        excelData.put("客诉导出", groupDataList);
        Map<String, List<ExcelExportColumn>> ColumnMap = new HashMap<String, List<ExcelExportColumn>>(16);
        ColumnMap.put("客诉导出", exportColumnList);
        excelGenerator.generateExcelSheet(excelData, ColumnMap, "客诉导出.xls", request, response);

    }
    /**
     * 分页查询数据(CCM)
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintInfMoreDTO", name = "complaintInfMoreDTO", value = "")
    })
    @ApiOperation(value = "分页查询数据(CCM)", notes = "分页查询数据(CCM)", httpMethod = "POST")
    @RequestMapping(value = "/selectCusByCcmAll", method = RequestMethod.POST)
    public void selectCusByCcmAll(@RequestBody(required=false)  ComplaintInfMoreDTO complaintInfMoreDTO,
                                                       HttpServletRequest request, HttpServletResponse response
    ) throws ParseException {
        //权限CCM
           String user=complaintInfMoreDTO.getRole();
        //准备导出数据
        List<Map> groupDataList = complaintInfoService.selectCusByDealAll(complaintInfMoreDTO,user);
               logger.info("groupDataList={}", JSON.toJSONString(groupDataList));
        //构建Excel相关对象
        List<ExcelExportColumn> exportColumnList = new ArrayList<ExcelExportColumn>();
        exportColumnList.add(new ExcelExportColumn("ccm_man", "CCM"));
        exportColumnList.add(new ExcelExportColumn("region", "区域"));
        exportColumnList.add(new ExcelExportColumn("region_manager", "区域经理"));
        exportColumnList.add(new ExcelExportColumn("ccm_is_read", "CCM是否已经跟进"));
        exportColumnList.add(new ExcelExportColumn("call_time", "投诉时间"));
        exportColumnList.add(new ExcelExportColumn("complaint_id", "投诉ID"));
        exportColumnList.add(new ExcelExportColumn("work_order_status", "工单状态"));
        exportColumnList.add(new ExcelExportColumn("newest_restart_time", "重启时间"));
        exportColumnList.add(new ExcelExportColumn("category3", "投诉分类三层级"));
        exportColumnList.add(new ExcelExportColumn("source", "投诉来源"));
        exportColumnList.add(new ExcelExportColumn("bloc", "处理经销商集团"));
        exportColumnList.add(new ExcelExportColumn("dealer_code", "处理经销商代码"));
        exportColumnList.add(new ExcelExportColumn("dealer_name", "处理经销商名称"));
        exportColumnList.add(new ExcelExportColumn("buy_bloc", "购车经销商集团"));
        exportColumnList.add(new ExcelExportColumn("buy_dealer_code", "购车经销商代码"));
        exportColumnList.add(new ExcelExportColumn("buy_dealer_name", "购车经销商名称"));
        exportColumnList.add(new ExcelExportColumn("model_name", "车型"));
        exportColumnList.add(new ExcelExportColumn("model_year", "年款"));
        exportColumnList.add(new ExcelExportColumn("vin", "VIN"));
        exportColumnList.add(new ExcelExportColumn("license_plate_num", "车牌"));
        exportColumnList.add(new ExcelExportColumn("call_name", "客户姓名"));
        exportColumnList.add(new ExcelExportColumn("call_tel", "来电手机"));
        exportColumnList.add(new ExcelExportColumn("buy_time", "购车日期"));
        exportColumnList.add(new ExcelExportColumn("mileage", "里程"));
        exportColumnList.add(new ExcelExportColumn("ccm_subject", "CCM主题"));
        exportColumnList.add(new ExcelExportColumn("big_class_name", "5日未结案原因(大类)"));
        exportColumnList.add(new ExcelExportColumn("small_class_name", "5日未结案原因(小类)"));
        exportColumnList.add(new ExcelExportColumn("subject", "主题"));
        exportColumnList.add(new ExcelExportColumn("keyword", "关键字"));
        exportColumnList.add(new ExcelExportColumn("type", "投诉类型"));
//        exportColumnList.add(new ExcelExportColumn("ccm_part", "CCM部位"));
//        exportColumnList.add(new ExcelExportColumn("ccm_subdivision_part", "CCM细分部位"));
//        exportColumnList.add(new ExcelExportColumn("cc_main_reason", "CCM主要原因"));
//        exportColumnList.add(new ExcelExportColumn("cc_result", "CCM解决结果"));
        exportColumnList.add(new ExcelExportColumn("classification1", "分类一"));
        exportColumnList.add(new ExcelExportColumn("classification2", "分类二"));
        exportColumnList.add(new ExcelExportColumn("classification3", "分类三"));
        exportColumnList.add(new ExcelExportColumn("classification4", "分类四"));
        exportColumnList.add(new ExcelExportColumn("classification5", "分类五"));
        exportColumnList.add(new ExcelExportColumn("classification6", "分类六"));
        exportColumnList.add(new ExcelExportColumn("problem", "问题描述"));
        exportColumnList.add(new ExcelExportColumn("follow_time", "CCM跟进时间"));
        exportColumnList.add(new ExcelExportColumn("ccm_follow_content", "CCM跟进详情"));
        exportColumnList.add(new ExcelExportColumn("status", "最新状态"));
        exportColumnList.add(new ExcelExportColumn("follow_content", "工单跟进"));
        exportColumnList.add(new ExcelExportColumn("dealer_fisrt_reply_time", "经销商首次回复时间"));
        exportColumnList.add(new ExcelExportColumn("close_case_time", "结案时间"));
        exportColumnList.add(new ExcelExportColumn("ccm_system_assisted_processing", "CCM系统协助处理"));
        exportColumnList.add(new ExcelExportColumn("again_complaint_number", "投诉次数"));
        exportColumnList.add(new ExcelExportColumn("revisit_content", "回访需求"));
        Map<String, List<Map>> excelData = new HashMap<String, List<Map>>(16);
        excelData.put("客诉导出", groupDataList);
        logger.info("excelData={}", JSON.toJSONString(excelData));
        Map<String, List<ExcelExportColumn>> ColumnMap = new HashMap<String, List<ExcelExportColumn>>(16);
        ColumnMap.put("客诉导出", exportColumnList);
        logger.info("ColumnMap={}", JSON.toJSONString(ColumnMap));

        excelGenerator.generateExcelSheet(excelData, ColumnMap, "客诉导出.xls", request, response);
    }
    /**
     * 分页查询数据(店端)
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "complaintInfoId", value = "")
    })
    @ApiOperation(value = "分页查询数据(店端)", notes = "分页查询数据(店端)", httpMethod = "GET")
    @RequestMapping(value = "/selectCusById", method = RequestMethod.GET)
    public List<ComplaintInfoDTO> selectCusById(@RequestParam(value="complaintInfoId",required = false) Long complaintInfoId
    )  {
        ComplaintInfoDTO complaintInfoDTO =new ComplaintInfoDTO();
        complaintInfoDTO.setId(complaintInfoId);
        return complaintInfoService.selectListBySql(complaintInfoDTO);
    }
    /**
     * 新增区域经理结案信息(已结案)
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "complaintCustomFieldTestDTO", value = "")
    })
    @ApiOperation(value = "新增区域经理结案信息(已结案)", notes = "新增区域经理结案信息(已结案)", httpMethod = "POST")
    @RequestMapping(value = "/insertRegionClose", method = RequestMethod.POST)
    public int insertRegionClose(@RequestBody(required=false)  ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO
    )  {
        String agree= String.valueOf(complaintCustomFieldTestDTO.getIsAgree());
        String revisit= String.valueOf(complaintCustomFieldTestDTO.getIsRevisit());
        return complaintInfoService.insertRegionClose(complaintCustomFieldTestDTO,agree,revisit);
    }

    /**
     * 查询亲善历史(VIN)
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "查询亲善历史(VIN)", notes = "查询亲善历史(VIN)", httpMethod = "GET")
    @RequestMapping(value = "/selectGoodWill", method = RequestMethod.GET)
    public IPage<GoodwillApplyInfoDTO> selectGoodWill(@RequestParam(value="vin") String  vin,
                                                      @RequestParam("currentPage")int currentPage,
                                                      @RequestParam("pageSize")int pageSize
    )  {
        Page<ComplaintInfoPO>page=new Page(currentPage,pageSize);
        GoodwillApplyInfoDTO goodwillApplyInfoDTO=new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setVin(vin);
        return complaintInfoService.selectGoodWill(page,goodwillApplyInfoDTO);
    }

    /**
     *
     * 查询明细
     * @param complaintInfMoreDTO
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintInfMoreDTO", name = "complaintInfMoreDTO", value = "")
    })
    @ApiOperation(value = "查询明细", notes = "查询明细", httpMethod = "POST")
    @RequestMapping(value = "/selectCusDetailById", method = RequestMethod.POST)
    public ComplaintInfMorePO selectCusDetailById(@RequestBody(required=false)  ComplaintInfMoreDTO complaintInfMoreDTO
    )  {
        return complaintInfoService.selectCusDetailById(complaintInfMoreDTO);
    }
    /**
     * 查询客诉信息 返回List
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintInfMoreDTO", name = "complaintInfMoreDTO", value = "")
    })
    @ApiOperation(value = "查询客诉信息 返回List", notes = "查询客诉信息 返回List", httpMethod = "POST")
    @RequestMapping(value = "/selectCusReturnList", method = RequestMethod.POST)
    public List<ComplaintInfoDTO> selectCusReturnList(@RequestBody(required=false)  ComplaintInfMoreDTO complaintInfMoreDTO
    )  {
        ComplaintInfoDTO complaintInfoDTO =new ComplaintInfoDTO();
        complaintInfoDTO.setVin(complaintInfMoreDTO.getVin());
        complaintInfoDTO.setDealerCode(complaintInfMoreDTO.getDealerCode());
        return complaintInfoService.selectListBySql(complaintInfoDTO);
    }

        /**
     * 获取节假日
     */
    @ApiOperation(value = "获取节假日", notes = "获取节假日", httpMethod = "GET")
    @GetMapping("/interf/getHoliday")
     public void getHoilday(){
        complaintInfoService.getHoilday();
    }









}