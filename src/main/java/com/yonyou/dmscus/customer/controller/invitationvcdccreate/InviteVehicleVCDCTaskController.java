package com.yonyou.dmscus.customer.controller.invitationvcdccreate;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.dto.ResponseDTO;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.invitationCreate.*;
import com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskPO;
import com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleVCDCTaskImportPO;
import com.yonyou.dmscus.customer.service.invitationCreate.InviteVehicleDealerTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @since 2020-04-29
 */
@Api(value = "/inviteVehicleVCDCTask", tags = {"InviteVehicleVCDCTaskController"})
@Slf4j
@RestController
@RequestMapping("/inviteVehicleVCDCTask")
public class InviteVehicleVCDCTaskController {

    @Autowired
    InviteVehicleDealerTaskService inviteVehicleDealerTaskService;
    @Autowired
    RedissonClient redissonClient;


    /**
     * 分页查询数据
     * @param inviteType
     * @param inviteName
     * @param createdName
     * @param createdAtStart
     * @param createdAtEnd
     * @param currentPage
     * @param pageSize
     * <AUTHOR>
     * @since 2020-04-29
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "inviteType", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "inviteName", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createdName", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAtStart", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAtEnd", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
    @GetMapping("/list")
    public IPage<InviteVehicleDealerTaskDTO> getByPage(
            @RequestParam(value = "inviteType", required = false) Integer inviteType,
            @RequestParam(value = "inviteName", required = false) String inviteName,
            @RequestParam(value = "createdName", required = false) String createdName,
            @RequestParam(value = "createdAtStart", required = false) String createdAtStart,
            @RequestParam(value = "createdAtEnd", required = false) String createdAtEnd,
            @RequestParam("currentPage") int currentPage,
            @RequestParam("pageSize") int pageSize) {
        Page<InviteVehicleDealerTaskPO> page = new Page(currentPage, pageSize);

        InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDTO = new InviteVehicleDealerTaskDTO();
        inviteVehicleDealerTaskDTO.setInviteType(inviteType);
        inviteVehicleDealerTaskDTO.setInviteName(inviteName);
        inviteVehicleDealerTaskDTO.setCreatedName(createdName);
        inviteVehicleDealerTaskDTO.setCreatedAtStart(createdAtStart);
        inviteVehicleDealerTaskDTO.setCreatedAtEnd(createdAtEnd);
        inviteVehicleDealerTaskDTO.setDataSources(CommonConstants.DATA_SOURCES_VCDC);
        return inviteVehicleDealerTaskService.selectPageBysql(page, inviteVehicleDealerTaskDTO);
    }



    /**
     * 保存自己邀约线索
     * @param inviteVehicleDealerTaskDTO
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteVehicleDealerTaskDTO", name = "inviteVehicleDealerTaskDTO", value = "", required = true)
    })
    @ApiOperation(value = "保存自己邀约线索", notes = "保存自己邀约线索", httpMethod = "POST")
    @PostMapping("/saveInvitationVCDC")
    public int saveInvitationDlr(@RequestBody InviteVehicleDealerTaskParamsDTO inviteVehicleDealerTaskDTO){
        return inviteVehicleDealerTaskService.saveInvitationVCDC(inviteVehicleDealerTaskDTO);
    }

    /**
     * d导入邀约线索临时表
     *
     * @param importFile
     * @return
     * @throws Exception
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "", required = true)
    })
    @ApiOperation(value = "导入邀约线索临时表", notes = "d导入邀约线索临时表", httpMethod = "POST")
    @RequestMapping(value = "/importInviteVehicleVCDCTemp", method = RequestMethod.POST)
    public ImportTempResult<InviteVehicleVCDCTaskImportPO> importInviteVehicleVCDCTemp(
            @RequestParam(value = "file") MultipartFile importFile) throws Exception {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if(loginInfoDto == null || loginInfoDto.getUserId() == null){
            log.info("importInviteVehicleVCDCTemp,loginInfoDto is null");
            throw new ServiceBizException("数据导入异常");
        }
        //使用userId加redis锁,防止重复点击
        Long userId = loginInfoDto.getUserId();
        String lockKey = "importInviteVehicleVCDCTemp::importInviteVehicleVCDCTemp::" + userId;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if(lock.tryLock(1, -1, TimeUnit.SECONDS)){
                return  inviteVehicleDealerTaskService.importInviteVehicleVCDCTemp(importFile);
            }else{
                log.info("importInviteVehicleVCDCTemp,fail,重复提交");
                throw new ServiceBizException("请耐心等待,勿重复提交");
            }
        }catch (ServiceBizException e) {
            log.error("importInviteVehicleVCDCTemp,ServiceBizException:", e);
            throw e;
        }
        catch (Exception e) {
            log.error("importInviteVehicleVCDCTemp,Exception:", e);
            throw new ServiceBizException("数据导入异常");
        } finally {
            if (lock != null && lock.isHeldByCurrentThread()) {
                log.info("importInviteVehicleVCDCTemp,释放锁,userId:{}", userId);
                lock.unlock();
            }
        }
    }
    
    /**
            * 查询导入错误
     * @param currentPage
     * @param pageSize
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "查询导入错误", notes = "查询导入错误", httpMethod = "GET")
    @GetMapping("/listErrorPage")
    public IPage<InviteVehicleDealerImportDTO> selectErrorPage(@RequestParam("currentPage") int currentPage, 
    		@RequestParam("pageSize") int pageSize){
    	Page<InviteVehicleDealerImportDTO> page=new Page(currentPage, pageSize);
    	return inviteVehicleDealerTaskService.selectErrorPage(page);
    	
    }
    
	/**
	     * 查询导入正确
	* @param currentPage
	* @param pageSize
	* @return
	*/
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "查询导入正确", notes = "查询导入正确", httpMethod = "GET")
    @GetMapping("/listSuccessPage")
	public IPage<InviteVehicleDealerImportDTO> selectSuccessPage(@RequestParam("currentPage") int currentPage, 
			@RequestParam("pageSize") int pageSize){
		Page<InviteVehicleDealerImportDTO> page=new Page(currentPage, pageSize);
		return inviteVehicleDealerTaskService.selectSuccessPage(page);
		
	}
    
    /**
	 * 临时表数据保存到正式表
	 * 
	 * @param
	 * @return
	 * <AUTHOR>
	 * @since 2020-03-30
	 */
	@ApiOperation(value = "临时表数据保存到正式表", notes = "临时表数据保存到正式表", httpMethod = "GET")
    @GetMapping(value = "/importTableByExcel")
    public int importIsLocalWRExcel() {
        LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
        if(loginInfoDto == null || loginInfoDto.getUserId() == null){
            log.info("importIsLocalWRExcel,loginInfoDto is null");
            return 1;
        }
        //使用userId加redis锁,防止重复点击
        Long userId = loginInfoDto.getUserId();
        String lockKey = "importTableByExcel::batchVCDCInsert::" + userId;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if(lock.tryLock(1, -1, TimeUnit.SECONDS)){
                inviteVehicleDealerTaskService.batchVCDCInsert();
            }else{
                log.info("importTableByExcel,fail,重复提交");
            }
        } catch (Exception e) {
            log.error("importTableByExcel,Exception:", e);
            throw new ServiceBizException("数据导入异常");
        } finally {
            if (lock != null && lock.isHeldByCurrentThread()) {
                log.info("importTableByExcel,释放锁,userId:{}", userId);
                lock.unlock();
            }
        }
        return 1;
	}


    @ApiOperation(value = "邀约名称查询", notes = "自建线索邀约名称查询", httpMethod = "GET")
    @GetMapping(value = "/v1/invite")
    public ResponseDTO<List<String>> queryInvite(@RequestParam(value="inviteName",required = false) String inviteName){
        List<String> list = inviteVehicleDealerTaskService.selectInviteName(inviteName);
        return ResponseDTO.successData(list);
    }

    @ApiOperation(value = "自建线索数据明细查询", notes = "自建线索数据明细查询", httpMethod = "POST")
    @PostMapping(value = "/v1/queryImportDetails")
    public ResponseDTO<IPage<queryImportDetailsDto>> queryImportDetails(@RequestBody InviteTaskQueryParamsDto dto){
        IPage<queryImportDetailsDto> page = inviteVehicleDealerTaskService.queryImportDetails(dto);
        return ResponseDTO.successData(page);
    }

    @ApiOperation(value = "自建线索数据导出", notes = "自建线索数据明细导出", httpMethod = "POST")
    @PostMapping(value = "/v1/importDetails")
    public ResponseDTO<Boolean> importDetails(@RequestBody InviteTaskQueryParamsDto dto, HttpServletResponse response){
        inviteVehicleDealerTaskService.importDetails(dto, response);
        if (!response.isCommitted()) {
            return ResponseDTO.success();
        }
        return null;
    }

    @ApiOperation(value = "店端手动关闭线索", notes = "店端手动关闭厂端自建线索", httpMethod = "POST")
    @PostMapping(value = "/v1/cluesClose")
    public ResponseDTO<Boolean> cluesClose(@RequestParam(value="ids")
                                           @NotEmpty
                                           @NotEmpty(message = "线索ID列表（ids）不能为空，请至少传入一个线索ID")
                                           List<Long> ids){
        Boolean flag = inviteVehicleDealerTaskService.cluesClose(ids);
        return ResponseDTO.successData(flag);
    }

}
