package com.yonyou.dmscus.customer.controller.inviteInsurance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.service.excel.ExcelGenerator;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.service.inviteInsurance.InviteInsuranceVehicleRecordDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-11-11
 */
@Api(value = "/inviteInsuranceManage", tags = {"导出"})
@RestController
@RequestMapping("/inviteInsuranceManage")
public class InviteInsuranceManageController {

    @Autowired
    InviteInsuranceVehicleRecordDetailService inviteInsuranceVehicleRecordDetailService;
    @Autowired
    ExcelGenerator excelGenerator;

    /**
     * 店端查询续保线索跟进明细
     * @param vin
     * @param id
     * @return
     */
    @ApiOperation(value = "店端查询续保线索跟进明细", notes = "店端查询续保线索跟进明细", httpMethod = "GET")
    @GetMapping("/getInviteInsuranceVehicleRecordInfoDlr")
    public List<InviteInsuranceVehicleRecordDetailDTO> getInviteInsuranceVehicleRecordInfoDlr(
            @RequestParam(value = "vin", required = true) String vin,
            @RequestParam(value = "id", required = true) Long id) {
        return inviteInsuranceVehicleRecordDetailService.getInviteInsuranceVehicleRecordInfoDlr(vin, id);
    }

    /**
     * 店端查询续保线索跟进明细历史
     * @param vin
     * @param dealerCode
     * @return
     */
    @ApiOperation(value = "店端查询续保线索跟进明细历史", notes = "店端查询续保线索跟进明细历史", httpMethod = "GET")
    @GetMapping("/selectFollowInviteInsureDetailHistory")
    public IPage<InviteInsuranceVehicleRecordDetailDTO> selectFollowInviteInsureDetailHistory(
            @RequestParam(value = "vin", required = false) String vin,
            @RequestParam(value = "dealerCode", required = false) String dealerCode,
            @RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize) {
        Page<InviteInsuranceVehicleRecordDetailDTO> page = new Page(currentPage, pageSize);
        return inviteInsuranceVehicleRecordDetailService.selectFollowInviteInsureDetailHistory(page, vin, dealerCode);
    }



    /**
     * 分页查询数据
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-24
     */
    /*
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "POST")
    @PostMapping("/list")
    public IPage<InviteVehicleRecordDTO> getInviteVehicleRecord(@RequestBody InvitationFollowParamsDTO dto) {
        Page<InviteVehicleRecordPO> page = new Page(dto.getCurrentPage(), dto.getPageSize());
        InviteVehicleRecordDTO inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        inviteVehicleRecordDTO.setLargeAreaId(dto.getLargeAreaId());
        inviteVehicleRecordDTO.setAreaId(dto.getAreaId());
        inviteVehicleRecordDTO.setDealerCode(dto.getDealerCode());
        inviteVehicleRecordDTO.setInviteTypeParam(dto.getInviteType());
        inviteVehicleRecordDTO.setPlanFollowDateStart(dto.getPlanFollowDateStart());
        inviteVehicleRecordDTO.setPlanFollowDateEnd(dto.getPlanFollowDateEnd());
        inviteVehicleRecordDTO.setLicensePlateNum(dto.getLicensePlateNum());
        inviteVehicleRecordDTO.setVin(dto.getVin());
        inviteVehicleRecordDTO.setActualFollowDateStart(dto.getActualFollowDateStart());
        inviteVehicleRecordDTO.setActualFollowDateEnd(dto.getActualFollowDateEnd());
        inviteVehicleRecordDTO.setName(dto.getName());
        inviteVehicleRecordDTO.setIsBook(dto.getIsBook());
        inviteVehicleRecordDTO.setAdviseInDateStart(dto.getAdviseInDateStart());
        inviteVehicleRecordDTO.setAdviseInDateEnd(dto.getAdviseInDateEnd());
        inviteVehicleRecordDTO.setFollowStatusParam(dto.getFollowStatus());
        inviteVehicleRecordDTO.setSaName(dto.getSaName());
        inviteVehicleRecordDTO.setCreatedAtStart(dto.getCreatedAtStart());
        inviteVehicleRecordDTO.setCreatedAtEnd(dto.getCreatedAtEnd());
        inviteVehicleRecordDTO.setOrderStatus(dto.getOrderStatus());
        return inviteManageVCDCService.getInviteVehicleRecord(page, inviteVehicleRecordDTO);
    }*/



    /**
     * 分配
     * @param dto
     * @return
     */
    /*
    @ApiOperation(value = "分配", notes = "分配", httpMethod = "POST")
    @PostMapping("/allocateDealer")
    @ResponseStatus(HttpStatus.CREATED)
    public int allocateDealer(@RequestBody AllocateDealerDTO dto){
        return inviteManageVCDCService.allocateDealer(dto);
    }*/


    /**
     * 导出
     * @param inviteType
     * @param planFollowDateStart
     * @param planFollowDateEnd
     * @param licensePlateNum
     * @param vin
     * @param actualFollowDateStart
     * @param actualFollowDateEnd
     * @param name
     * @param isBook
     * @param adviseInDateStart
     * @param adviseInDateEnd
     * @param followStatus
     * @param saName
     * @param createdAtStart
     * @param createdAtEnd
     * @param overdue
     * @param request
     * @param response
     * @throws Exception
     */
    /*
    @ApiOperation(value = "导出", notes = "导出", httpMethod = "GET")
    @RequestMapping(value = "/exportExcel", method = RequestMethod.GET)
    @ResponseBody
    public void exportExcel(
            @RequestParam(value = "largeAreaId", required = false) String largeAreaId,
            @RequestParam(value = "areaId", required = false) String areaId,
            @RequestParam(value = "dealerCode", required = false) String dealerCode,
            @RequestParam(value = "inviteType", required = false) List<Integer> inviteType,
            @RequestParam(value = "planFollowDateStart", required = false) String planFollowDateStart,
            @RequestParam(value = "planFollowDateEnd", required = false) String planFollowDateEnd,
            @RequestParam(value = "licensePlateNum", required = false) String licensePlateNum,
            @RequestParam(value = "vin", required = false) String vin,
            @RequestParam(value = "actualFollowDateStart", required = false) String actualFollowDateStart,
            @RequestParam(value = "actualFollowDateEnd", required = false) String actualFollowDateEnd,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "isBook", required = false) Integer isBook,
            @RequestParam(value = "adviseInDateStart", required = false) String adviseInDateStart,
            @RequestParam(value = "adviseInDateEnd", required = false) String adviseInDateEnd,
            @RequestParam(value = "followStatus", required = false) List<Integer> followStatus,
            @RequestParam(value = "saName", required = false) String saName,
            @RequestParam(value = "createdAtStart", required = false) String createdAtStart,
            @RequestParam(value = "createdAtEnd", required = false) String createdAtEnd,
            @RequestParam(value = "overdue", required = false) Integer overdue,
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        InviteVehicleRecordDTO inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        inviteVehicleRecordDTO.setLargeAreaId(largeAreaId);
        inviteVehicleRecordDTO.setAreaId(areaId);
        inviteVehicleRecordDTO.setDealerCode(dealerCode);
        inviteVehicleRecordDTO.setInviteTypeParam(inviteType);
        inviteVehicleRecordDTO.setPlanFollowDateStart(planFollowDateStart);
        inviteVehicleRecordDTO.setPlanFollowDateEnd(planFollowDateEnd);
        inviteVehicleRecordDTO.setLicensePlateNum(licensePlateNum);
        inviteVehicleRecordDTO.setVin(vin);
        inviteVehicleRecordDTO.setActualFollowDateStart(actualFollowDateStart);
        inviteVehicleRecordDTO.setActualFollowDateEnd(actualFollowDateEnd);
        inviteVehicleRecordDTO.setName(name);
        inviteVehicleRecordDTO.setIsBook(isBook);
        inviteVehicleRecordDTO.setAdviseInDateStart(adviseInDateStart);
        inviteVehicleRecordDTO.setAdviseInDateEnd(adviseInDateEnd);
        inviteVehicleRecordDTO.setFollowStatusParam(followStatus);
        inviteVehicleRecordDTO.setSaName(saName);
        inviteVehicleRecordDTO.setCreatedAtStart(createdAtStart);
        inviteVehicleRecordDTO.setCreatedAtEnd(createdAtEnd);
        List<Map> inviteVehicleRecordList = inviteManageVCDCService.exportExcelinviteVehicleRecord
                (inviteVehicleRecordDTO);
        List<ExcelExportColumn> exportColumnList = new ArrayList<ExcelExportColumn>();
        exportColumnList.add(new ExcelExportColumn("vin", "VIN"));
        exportColumnList.add(new ExcelExportColumn("licensePlateNum", "车牌号"));
        exportColumnList.add(new ExcelExportColumn("inviteTypeName", "邀约类型"));
        exportColumnList.add(new ExcelExportColumn("adviseInDate", "建议进厂日期","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("newAdviseInDate", "新建议进厂日期","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("lastInDate", "上次进厂日期","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("planFollowDate", "下次跟进日期","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("actualFollowDate", "实际跟进日期","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("saName", "跟进人员"));
        exportColumnList.add(new ExcelExportColumn("followStatusName", "跟进状态"));
        exportColumnList.add(new ExcelExportColumn("isBook", "是否有预约单"));
        exportColumnList.add(new ExcelExportColumn("orderStatusName", "线索完成状态"));
        exportColumnList.add(new ExcelExportColumn("dealerCode", "邀约经销商"));
        exportColumnList.add(new ExcelExportColumn("callTime", "通话时间","yyyy-MM-dd HH:mm:ss"));
        exportColumnList.add(new ExcelExportColumn("totalScore", "通话时长"));
        exportColumnList.add(new ExcelExportColumn("callTime", "AI得分"));
        //xportColumnList.add(new ExcelExportColumn("lastSaName", "上次SA"));
        exportColumnList.add(new ExcelExportColumn("orderFinishDate", "线索完成时间","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("content", "跟进内容"));
        exportColumnList.add(new ExcelExportColumn("loseReason", "失败原因"));

        Map<String, List<Map>> excelData = new HashMap<String, List<Map>>();
        Map<String, List<ExcelExportColumn>> ColumnMap = new HashMap<String, List<ExcelExportColumn>>();
        if(inviteVehicleRecordList.size()>=65535){
            int temppage = 0;
            int page = 50000;
            List<Map> list = new ArrayList<>();
            for(int i=0;i<inviteVehicleRecordList.size();i++){
                list.add(inviteVehicleRecordList.get(i));
                if(i!=0&&i%page==0){
                    temppage++;
                    excelData.put("邀约跟进"+temppage, list);
                    ColumnMap.put("邀约跟进"+temppage, exportColumnList);
                    list = new ArrayList<>();
                }
                else if(i==inviteVehicleRecordList.size()-1){
                    temppage++;
                    excelData.put("邀约跟进"+temppage, list);
                    ColumnMap.put("邀约跟进"+temppage, exportColumnList);
                }
            }
        }else{
            excelData.put("邀约跟进", inviteVehicleRecordList);
            ColumnMap.put("邀约跟进", exportColumnList);
        }
        excelGenerator.generateExcelSheet(excelData, ColumnMap, "邀约跟进导出.xlsx", request, response);
    }*/

    /**
     * 查询邀约线索车辆信息和跟进明细
     * @param vin
     * @param id
     * @return
     */
    /*
    @ApiOperation(value = "查询邀约线索车辆信息和跟进明细", notes = "查询邀约线索车辆信息和跟进明细", httpMethod = "GET")
    @GetMapping("/getInviteVehicleRecordInfo")
    public InviteVehicleRecordDTO getInviteVehicleRecordInfo(
            @RequestParam(value = "vin", required = true) String vin,
            @RequestParam(value = "id", required = true) Long id) {
        return inviteManageVCDCService.getInviteVehicleRecordInfo(vin, id);
    }*/


}