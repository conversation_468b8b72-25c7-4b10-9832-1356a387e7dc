package com.yonyou.dmscus.customer.controller.talkskill;


import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillKeywordDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillKeywordLibPO;
import com.yonyou.dmscus.customer.service.talkskill.TalkskillKeywordLibService;
import com.yonyou.dmscus.customer.service.talkskill.TalkskillKeywordService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 *
 * <AUTHOR>
 * @since 2020-03-27
 */
@Api(value = "/talkskillKeyword", tags = {"TalkskillKeywordController"})
@RestController
@RequestMapping("/talkskillKeyword")
public class TalkskillKeywordController extends BaseController {
        @Autowired
        TalkskillKeywordLibService talkskillKeywordLibService;
        @Autowired
        TalkskillKeywordService talkskillKeywordService;
        /**
         * 进行数据查询
         *
         * @param
         * @return int
         * <AUTHOR>
         * @since 2020-03-27
         */
         @ApiImplicitParams({
                 @ApiImplicitParam(paramType = "body", dataType = "string", name = "talkskill", value = "", required = true)
         })
         @ApiOperation(value = "进行数据查询", notes = "进行数据查询", httpMethod = "POST")
         @PostMapping(value = "/hasList")
         public List<TalkskillKeywordLibPO> getList(@RequestBody String talkskill){

             return  talkskillKeywordLibService.selectListBySql(talkskill);

         }


        /**
         * 进行数据新增
         *
         * @param talkskillKeywordDTO 需要保存的DTO
         * @return int
         * <AUTHOR>
         * @since 2020-03-27
         */
        @ApiImplicitParams({
                @ApiImplicitParam(paramType = "body", dataType = "TalkskillKeywordDTO", name = "talkskillKeywordDTO", value = "需要保存的DTO", required = true)
        })
        @ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
        @PostMapping
        @ResponseStatus(HttpStatus.CREATED)
        public int insert(@RequestBody TalkskillKeywordDTO talkskillKeywordDTO){
            return talkskillKeywordService.insert( talkskillKeywordDTO);
        }




}