package com.yonyou.dmscus.customer.controller.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintEvidenceDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.InsertComplaitEvidenceDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintEvidencePO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintEvidenceService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Api(value = "/complaintEvidence", tags = {"ComplaintEvidenceController"})
@RestController
@RequestMapping("/complaintEvidence")
                public class ComplaintEvidenceController extends BaseController {
    
        @Autowired
        ComplaintEvidenceService complaintEvidenceService;

        /**
         * 分页查询数据
         *
        * @param complaintInfoId 投诉信息表主键ID
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @since 2020-04-15
        */
   @ApiImplicitParams({
           @ApiImplicitParam(paramType = "query", dataType = "long", name = "complaintInfoId", value = "投诉信息表主键ID"),
           @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
           @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
   })
   @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
   @RequestMapping(value = "/selectComplaitEvidence", method = RequestMethod.GET)
        public IPage<ComplaintEvidenceDTO>getByPage(
@RequestParam(value="complaintInfoId",required = false) Long complaintInfoId,
        @RequestParam("currentPage")int currentPage,
        @RequestParam("pageSize")int pageSize){
        Page<ComplaintEvidencePO>page=new Page(currentPage,pageSize);

        ComplaintEvidenceDTO complaintEvidenceDTO =new ComplaintEvidenceDTO();
                                            complaintEvidenceDTO.setComplaintInfoId(complaintInfoId);
                return complaintEvidenceService.selectPageBysql(page,complaintEvidenceDTO);
        }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "complaintInfoId", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @RequestMapping(value = "/selectComplaitEvidenceByList", method = RequestMethod.GET)
    public List<ComplaintEvidenceDTO> selectComplaitEvidenceByList(
            @RequestParam(value="complaintInfoId") Long complaintInfoId){
        ComplaintEvidenceDTO complaintEvidenceDTO =new ComplaintEvidenceDTO();
        complaintEvidenceDTO.setComplaintInfoId(complaintInfoId);
        return complaintEvidenceService.selectListBySql(complaintEvidenceDTO);
    }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintEvidenceDTO
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public ComplaintEvidenceDTO getById(@PathVariable("id") Long id){
        return complaintEvidenceService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param insertComplaitEvidenceDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "InsertComplaitEvidenceDTO", name = "insertComplaitEvidenceDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@RequestMapping(value = "/insertComplaitEvidence", method = RequestMethod.POST)
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody InsertComplaitEvidenceDTO insertComplaitEvidenceDTO){
        return complaintEvidenceService.insertComplaitEvidence( insertComplaitEvidenceDTO);
        }

    /**
     * 进行数据新增
     *
     * @param List<ComplaintEvidenceDTO> 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<ComplaintEvidenceDTO>", name = "complaintEvidenceDTOS", value = "", required = true)
    })
    @ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
    @RequestMapping(value = "/deleteComplaitEvidence", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int deleteComplaitEvidence(@RequestBody List<ComplaintEvidenceDTO> complaintEvidenceDTOS){
        return complaintEvidenceService.deleteComplaitEvidence(complaintEvidenceDTOS);
    }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param complaintEvidenceDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintEvidenceDTO", name = "complaintEvidenceDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody ComplaintEvidenceDTO complaintEvidenceDTO){
        return complaintEvidenceService.update(id,complaintEvidenceDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    complaintEvidenceService.deleteById(id);
        return true;
        }



}