package com.yonyou.dmscus.customer.controller.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintClassificationDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintClassificationListDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintClassificationPO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintClassificationService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;


/**
 *
 * <AUTHOR>
 * @since 2021-01-13
 */
@Api(value = "/complaintClassification", tags = {"ComplaintClassificationController"})
@RestController
@RequestMapping("/complaintClassification")
                public class ComplaintClassificationController extends BaseController {
    
        @Autowired
        ComplaintClassificationService complaintClassificationService;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码 
        * @param ownerParCode 所有者的父组织代码 
        * @param orgId 组织ID
        * @param id 主键ID
        * @param parentId 父级ID
        * @param cateCode 分类编码
        * @param cateName 分类名称
        * @param cateLevel 分类层级 0为工单性质 1为投诉单类别一级层 2为投诉单类别二级层 3为投诉单类别三级层
        * @param cateStatus 分类状态 1为新增 2为作废 3为更新
        * @param dataSources 数据来源 
        * @param isDeleted 是否删除
        * @param isValid 是否有效 
        * @param createdAt 创建时间
        * @param updatedAt 更新时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @since 2021-01-13
        */
                @ApiImplicitParams({
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "parentId", value = "父级ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "cateCode", value = "分类编码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "cateName", value = "分类名称"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "cateLevel", value = "分类层级 0为工单性质 1为投诉单类别一级层 2为投诉单类别二级层 3为投诉单类别三级层"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "cateStatus", value = "分类状态 1为新增 2为作废 3为更新"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = "数据来源"),
                        @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "createdAt", value = "创建时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "updatedAt", value = "更新时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
                })
                @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
                @GetMapping
        public IPage<ComplaintClassificationDTO>getByPage(
@RequestParam(value="appId",required = false) String appId,
@RequestParam(value="ownerCode",required = false) String ownerCode,
@RequestParam(value="ownerParCode",required = false) String ownerParCode,
@RequestParam(value="orgId",required = false) Integer orgId,
@RequestParam(value="id",required = false) Long id,
@RequestParam(value="parentId",required = false) Long parentId,
@RequestParam(value="cateCode",required = false) String cateCode,
@RequestParam(value="cateName",required = false) String cateName,
@RequestParam(value="cateLevel",required = false) String cateLevel,
@RequestParam(value="cateStatus",required = false) String cateStatus,
@RequestParam(value="dataSources",required = false) Integer dataSources,
@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
@RequestParam(value="isValid",required = false) Integer isValid,
@RequestParam(value="createdAt",required = false) Date createdAt,
@RequestParam(value="updatedAt",required = false) Date updatedAt,
        @RequestParam("currentPage")int currentPage,
        @RequestParam("pageSize")int pageSize){
        Page<ComplaintClassificationPO>page=new Page(currentPage,pageSize);

        ComplaintClassificationDTO complaintClassificationDTO =new ComplaintClassificationDTO();
                                            complaintClassificationDTO.setAppId(appId);
                                            complaintClassificationDTO.setOwnerCode(ownerCode);
                                            complaintClassificationDTO.setOwnerParCode(ownerParCode);
                                            complaintClassificationDTO.setOrgId(orgId);
                                            complaintClassificationDTO.setId(id);
                                            complaintClassificationDTO.setParentId(parentId);
                                            complaintClassificationDTO.setCateCode(cateCode);
                                            complaintClassificationDTO.setCateName(cateName);
                                            complaintClassificationDTO.setCateLevel(cateLevel);
                                            complaintClassificationDTO.setCateStatus(cateStatus);
                                            complaintClassificationDTO.setDataSources(dataSources);
                                            complaintClassificationDTO.setIsDeleted(isDeleted);
                                            complaintClassificationDTO.setIsValid(isValid);
                                            complaintClassificationDTO.setCreatedAt(createdAt);
                                            complaintClassificationDTO.setUpdatedAt(updatedAt);
                return complaintClassificationService.selectPageBysql(page,complaintClassificationDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintClassificationDTO
 * <AUTHOR>
 * @since 2021-01-13
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public ComplaintClassificationDTO getById(@PathVariable("id") Long id){
        return complaintClassificationService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param complaintClassificationDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2021-01-13
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintClassificationDTO", name = "complaintClassificationDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody ComplaintClassificationDTO complaintClassificationDTO){
        return complaintClassificationService.insert( complaintClassificationDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param complaintClassificationDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2021-01-13
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintClassificationDTO", name = "complaintClassificationDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody ComplaintClassificationDTO complaintClassificationDTO){
        return complaintClassificationService.update(id,complaintClassificationDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2021-01-13
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    complaintClassificationService.deleteById(id);
        return true;
        }

    /**
     * 查询工单性质 ：0
     */
    @ApiOperation(value = "查询工单性质 ：0", notes = "查询工单性质 ：0", httpMethod = "GET")
    @GetMapping(value = "/selectComplaintNature")
    public List<ComplaintClassificationDTO> selectComplaintNature(){
        ComplaintClassificationDTO complaintClassificationDTO=new ComplaintClassificationDTO();
        complaintClassificationDTO.setCateLevel("0");
        return complaintClassificationService.selectListBySql(complaintClassificationDTO);
    }

    /**
     * 查询工单类别
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "parentId", value = "")
    })
    @ApiOperation(value = "查询工单类别", notes = "查询工单类别", httpMethod = "GET")
    @GetMapping(value = "/selectComplaintCategory")
    public List<ComplaintClassificationDTO> selectComplaintCategory(
            @RequestParam(value="parentId",required = false) String parentId
    ){
        ComplaintClassificationDTO complaintClassificationDTO=new ComplaintClassificationDTO();
        complaintClassificationDTO.setParentId(Long.valueOf(parentId));
        return complaintClassificationService.selectComplaintCategory(complaintClassificationDTO);
    }

    /**
     * 查询工单类别
     */
    @ApiOperation(value = "查询工单类别", notes = "查询工单类别", httpMethod = "GET")
    @GetMapping(value = "/selectAllComplaintCategory")
    public List<ComplaintClassificationDTO> selectAllComplaintCategory(
    ){
        ComplaintClassificationDTO complaintClassificationDTO=new ComplaintClassificationDTO();
        return complaintClassificationService.selectComplaintCategory(complaintClassificationDTO);
    }

    /**
     * 查询工单类别(List)
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintClassificationListDTO", name = "complaintClassificationListDTO", value = "", required = true)
    })
    @ApiOperation(value = "查询工单类别(List)", notes = "查询工单类别(List)", httpMethod = "POST")
    @PostMapping(value = "/selectComplaintCategoryList")
    public List<ComplaintClassificationDTO> selectComplaintCategory(
            @RequestBody ComplaintClassificationListDTO   complaintClassificationListDTO
    ){
        ComplaintClassificationDTO complaintClassificationDTO=new ComplaintClassificationDTO();
        if (complaintClassificationListDTO.getParentList() != null) {
            List parentList = complaintClassificationListDTO.getParentList();
            StringBuffer parentId = new StringBuffer();
            for (int i = 0; i < parentList.size(); i++) {

                if (i == parentList.size() - 1) {
                    parentId.append(parentList.get(i));
                } else {
                    parentId.append(parentList.get(i) + ",");
                }
            }
            complaintClassificationDTO.setParentId1(parentId.toString());
        }
        return complaintClassificationService.selectComplaintCategory(complaintClassificationDTO);
    }

}