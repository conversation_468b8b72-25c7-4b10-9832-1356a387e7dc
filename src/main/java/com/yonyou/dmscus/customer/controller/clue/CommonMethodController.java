package com.yonyou.dmscus.customer.controller.clue;

import com.yonyou.dmscus.customer.dto.CdpTokenPortraitDto;
import com.yonyou.dmscus.customer.service.common.common.CommonMethodService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * description crm线条同步到nb
 *
 * <AUTHOR>
 * @date 2023/8/23 15:19
 */

@Api(value = "/commonMethod", tags = {"crm线条同步到nb"})
@Slf4j
@RestController
@RequestMapping("/commonMethod")
public class CommonMethodController {

    @Autowired
    private CommonMethodService commonMethodService;

    @ApiOperation(value = "获取CDP的token", notes = "", httpMethod = "GET")
    @GetMapping("/getCdpToken")
    public CdpTokenPortraitDto getCdpToken() {
        return commonMethodService.getCdpToken(null);
    }
}
