package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMaterialAuditDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMaterialAuditPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillMaterialAuditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;

                                                                                                    /**
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
@Api(value = "/goodwillMaterialAudit", tags = {"GoodwillMaterialAuditController"})
@RestController
@RequestMapping("/goodwillMaterialAudit")
                public class GoodwillMaterialAuditController {
    
        @Autowired
        GoodwillMaterialAuditService goodwillMaterialAuditService;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码
        * @param ownerParCode 所有者的父组织代码
        * @param orgId 组织ID
        * @param id 主键id
        * @param goodwillApplyId 亲善单id
        * @param customerBackground 客户背景
        * @param customerBackgroundEn 客户背景英文
        * @param reasonAndDispose 投诉原因及处理经过
        * @param reasonAndDisposeEn 投诉原因及处理经过英文
        * @param repairSolution 维修解决方案
        * @param repairSolutionEn 维修解决方案英文
        * @param customerRequire 客户要求
        * @param customerRequireEn 客户要求英文
        * @param potentialRisk 潜在风险
        * @param potentialRiskEn 潜在风险英文
        * @param vrOrTjNoEn VR或TJ号英文
        * @param vrOrTjNo VR或TJ号
        * @param businessGoodwillApplyDetailEn 商务亲善申请详情英文
        * @param businessGoodwillApplyDetail 商务亲善申请详情
        * @param isValid 是否有效
        * @param isDeleted 是否删除:1,删除；0,未删除
        * @param createdAt 创建时间
        * @param updatedAt 修改时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @throws ParseException 
         * @since 2020-05-06
        */
      @ApiImplicitParams({
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
              @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
              @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键id"),
              @ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = "亲善单id"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "customerBackground", value = "客户背景"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "customerBackgroundEn", value = "客户背景英文"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "reasonAndDispose", value = "投诉原因及处理经过"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "reasonAndDisposeEn", value = "投诉原因及处理经过英文"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "repairSolution", value = "维修解决方案"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "repairSolutionEn", value = "维修解决方案英文"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "customerRequire", value = "客户要求"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "customerRequireEn", value = "客户要求英文"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "potentialRisk", value = "潜在风险"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "potentialRiskEn", value = "潜在风险英文"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "vrOrTjNoEn", value = "VR或TJ号英文"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "vrOrTjNo", value = "VR或TJ号"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "businessGoodwillApplyDetailEn", value = "商务亲善申请详情英文"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "businessGoodwillApplyDetail", value = "商务亲善申请详情"),
              @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
              @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除:1,删除；0,未删除"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = "创建时间"),
              @ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = "修改时间"),
              @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
              @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
      })
      @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
      @GetMapping
      public IPage<GoodwillMaterialAuditDTO>getByPage(
		@RequestParam(value="appId",required = false) String appId,
		@RequestParam(value="ownerCode",required = false) String ownerCode,
		@RequestParam(value="ownerParCode",required = false) String ownerParCode,
		@RequestParam(value="orgId",required = false) Integer orgId,
		@RequestParam(value="id",required = false) Long id,
		@RequestParam(value="goodwillApplyId",required = false) Long goodwillApplyId,
		@RequestParam(value="customerBackground",required = false) String customerBackground,
		@RequestParam(value="customerBackgroundEn",required = false) String customerBackgroundEn,
		@RequestParam(value="reasonAndDispose",required = false) String reasonAndDispose,
		@RequestParam(value="reasonAndDisposeEn",required = false) String reasonAndDisposeEn,
		@RequestParam(value="repairSolution",required = false) String repairSolution,
		@RequestParam(value="repairSolutionEn",required = false) String repairSolutionEn,
		@RequestParam(value="customerRequire",required = false) String customerRequire,
		@RequestParam(value="customerRequireEn",required = false) String customerRequireEn,
		@RequestParam(value="potentialRisk",required = false) String potentialRisk,
		@RequestParam(value="potentialRiskEn",required = false) String potentialRiskEn,
		@RequestParam(value="vrOrTjNoEn",required = false) String vrOrTjNoEn,
		@RequestParam(value="vrOrTjNo",required = false) String vrOrTjNo,
		@RequestParam(value="businessGoodwillApplyDetailEn",required = false) String businessGoodwillApplyDetailEn,
		@RequestParam(value="businessGoodwillApplyDetail",required = false) String businessGoodwillApplyDetail,
		@RequestParam(value="isValid",required = false) Integer isValid,
		@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
		@RequestParam(value="createdAt",required = false) String createdAt,
		@RequestParam(value="updatedAt",required = false) String updatedAt,
        @RequestParam("currentPage")int currentPage,
        @RequestParam("pageSize")int pageSize) throws ParseException{
	        Page<GoodwillMaterialAuditPO>page=new Page(currentPage,pageSize);
	        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	        GoodwillMaterialAuditDTO goodwillMaterialAuditDTO =new GoodwillMaterialAuditDTO();
            goodwillMaterialAuditDTO.setAppId(appId);
            goodwillMaterialAuditDTO.setOwnerCode(ownerCode);
            goodwillMaterialAuditDTO.setOwnerParCode(ownerParCode);
            goodwillMaterialAuditDTO.setOrgId(orgId);
            goodwillMaterialAuditDTO.setId(id);
            goodwillMaterialAuditDTO.setGoodwillApplyId(goodwillApplyId);
            goodwillMaterialAuditDTO.setCustomerBackground(customerBackground);
            goodwillMaterialAuditDTO.setCustomerBackgroundEn(customerBackgroundEn);
            goodwillMaterialAuditDTO.setReasonAndDispose(reasonAndDispose);
            goodwillMaterialAuditDTO.setReasonAndDisposeEn(reasonAndDisposeEn);
            goodwillMaterialAuditDTO.setRepairSolution(repairSolution);
            goodwillMaterialAuditDTO.setRepairSolutionEn(repairSolutionEn);
            goodwillMaterialAuditDTO.setCustomerRequire(customerRequire);
            goodwillMaterialAuditDTO.setCustomerRequireEn(customerRequireEn);
            goodwillMaterialAuditDTO.setPotentialRisk(potentialRisk);
            goodwillMaterialAuditDTO.setPotentialRiskEn(potentialRiskEn);
            goodwillMaterialAuditDTO.setVrOrTjNoEn(vrOrTjNoEn);
            goodwillMaterialAuditDTO.setVrOrTjNo(vrOrTjNo);
            goodwillMaterialAuditDTO.setBusinessGoodwillApplyDetailEn(businessGoodwillApplyDetailEn);
            goodwillMaterialAuditDTO.setBusinessGoodwillApplyDetail(businessGoodwillApplyDetail);
            goodwillMaterialAuditDTO.setIsValid(isValid);
            goodwillMaterialAuditDTO.setIsDeleted(isDeleted);
            if(createdAt != null){
            	goodwillMaterialAuditDTO.setCreatedAt(sdf.parse(createdAt));
            }
            if(updatedAt != null){
            	goodwillMaterialAuditDTO.setUpdatedAt(sdf.parse(updatedAt));
            }
                return goodwillMaterialAuditService.selectPageBysql(page,goodwillMaterialAuditDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMaterialAuditDTO
 * <AUTHOR>
 * @since 2020-05-06
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public GoodwillMaterialAuditDTO getById(@PathVariable("id") Long id){
        return goodwillMaterialAuditService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param goodwillMaterialAuditDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-05-06
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "GoodwillMaterialAuditDTO", name = "goodwillMaterialAuditDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody GoodwillMaterialAuditDTO goodwillMaterialAuditDTO){
        return goodwillMaterialAuditService.insert( goodwillMaterialAuditDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param goodwillMaterialAuditDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-05-06
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "GoodwillMaterialAuditDTO", name = "goodwillMaterialAuditDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody GoodwillMaterialAuditDTO goodwillMaterialAuditDTO){
        return goodwillMaterialAuditService.update(id,goodwillMaterialAuditDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-05-06
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    goodwillMaterialAuditService.deleteById(id);
        return true;
        }

        /**
         * 根据IDs批量删除对象
         *
         * @param ids 需要修改数据的ID集合
         * <AUTHOR>
         * @since 2020-05-06
         */
                        @ApiImplicitParams({
                                @ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "需要修改数据的ID集合", required = true)
                        })
                        @ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
                        @DeleteMapping(value = "/batch/{ids}")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        public boolean deleteByIds(@PathVariable("ids") String ids){
            goodwillMaterialAuditService.deleteBatchIds(ids);
                return true;
                }

}