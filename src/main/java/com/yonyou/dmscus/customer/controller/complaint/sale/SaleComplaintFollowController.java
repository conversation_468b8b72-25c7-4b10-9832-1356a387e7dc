package com.yonyou.dmscus.customer.controller.complaint.sale;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.AppPushDTO;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintFollowService;
import com.yonyou.dmscus.customer.service.impl.CommonServiceImpl;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-12-11
 */
@Api(value = "/saleComplaintFollow", tags = {"SaleComplaintFollowController"})
@RestController
@RequestMapping("/saleComplaintFollow")
public class SaleComplaintFollowController extends BaseController {

    @Autowired
    SaleComplaintFollowService saleComplaintFollowService;

    @Autowired
    CommonServiceImpl commonService;

    /**
     * 查询跟进信息（店端使用）
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-14
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "查询跟进信息（店端使用）", notes = "查询跟进信息（店端使用）", httpMethod = "GET")
    @GetMapping(value = "/queryCus/{id}")
    public List<SaleComplaintFollowDTO> queryCus(@PathVariable("id") Long id) {

        SaleComplaintFollowDTO saleComplaintFollowDTO = new SaleComplaintFollowDTO();
        saleComplaintFollowDTO.setComplaintInfoId(id);
        saleComplaintFollowDTO.setIsCcmNotPublish(true);
        String flag = "dealer";
        return saleComplaintFollowService.selectListBySql(flag, saleComplaintFollowDTO);
    }

    /**
     * 新增经销商跟进内容
     * *
     * @param ComplaintCustomFieldTestDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "ComplaintCustomFieldTestDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "新增经销商跟进内容 *", notes = "新增经销商跟进内容 *", httpMethod = "POST")
    @RequestMapping(value = "/insertCus", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertCus(@RequestBody ComplaintCustomFieldTestDTO ComplaintCustomFieldTestDTO) throws ParseException {
        return saleComplaintFollowService.insertcCus(ComplaintCustomFieldTestDTO);
    }

    /**
     * 新增经销商跟进内容(上报车厂)
     * *
     * @param ComplaintCustomFieldTestDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "ComplaintCustomFieldTestDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "新增经销商跟进内容(上报车厂) *", notes = "新增经销商跟进内容(上报车厂) *", httpMethod = "POST")
    @RequestMapping(value = "/reportVeh", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int reportVeh(@RequestBody ComplaintCustomFieldTestDTO ComplaintCustomFieldTestDTO) throws ParseException {
        return saleComplaintFollowService.reportVeh(ComplaintCustomFieldTestDTO);
    }

    /**
     * 新增区域经理跟进内容
     * *
     * @param ComplaintCustomFieldTestDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "ComplaintCustomFieldTestDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "新增区域经理跟进内容 *", notes = "新增区域经理跟进内容 *", httpMethod = "POST")
    @RequestMapping(value = "/insertRegionCus", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertRegionCus(@RequestBody ComplaintCustomFieldTestDTO ComplaintCustomFieldTestDTO) throws ParseException {
        return saleComplaintFollowService.insertRegionCus(ComplaintCustomFieldTestDTO);
    }
    /**
     * 提醒下次跟进时间
     */
    @ApiOperation(value = "提醒下次跟进时间", notes = "提醒下次跟进时间", httpMethod = "GET")
    @GetMapping(value = "/remindNextFollowing")
    public void remindNextFollowing(){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<ComplaintInfMoreDTO> queryNextFollowing=saleComplaintFollowService.queryNextFollowing();
        for(int i=0;i<queryNextFollowing.size();i++){

            AppPushDTO appPushDTO=new AppPushDTO();
            List<Integer> idList=new ArrayList<>();
            idList.add(Integer.valueOf(queryNextFollowing.get(i).getFollowName()));
            appPushDTO.setUserIds(idList);
            appPushDTO.setPriority(33081001L);
            appPushDTO.setTitle("销售客诉单跟进提醒");
            JSONObject json = new JSONObject();
            //向json中添加数据
            json.put("complaintNo",queryNextFollowing.get(i).getComplaintId());
            json.put("dealerCode",queryNextFollowing.get(i).getDealerCode());
            json.put("regionManager",queryNextFollowing.get(i).getRegionManager());
            json.put("subject",queryNextFollowing.get(i).getSubject());
            json.put("FollowTime",formatter.format(queryNextFollowing.get(i).getFollowTime()));
            //转换为字符串
            String jsonStr = json.toString();
            appPushDTO.setJson(jsonStr);
            appPushDTO.setContent("您有需要跟进的客诉单:"
                    +"客诉单号:"+queryNextFollowing.get(i).getComplaintId()+",处理经销商:"+queryNextFollowing.get(i).getDealerCode()
                    +",区域经理:"+queryNextFollowing.get(i).getRegionManager()+",主题:"+queryNextFollowing.get(i).getSubject()
                    +",跟进时间:"+ formatter.format(queryNextFollowing.get(i).getFollowTime())
                    +"!点击案件进行处理");
            commonService.messageSendApp(appPushDTO);
        }

    }


}
