package com.yonyou.dmscus.customer.controller.inviteInsurance;

import com.yonyou.dmscus.customer.service.inviteInsurance.InviteInsuranceVehicleTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @since 2020-11-15
 */
@Api(value = "/inviteInsuranceAutoCreate", tags = {"InviteInsuranceAutoCreateController"})
@RestController
@RequestMapping("/inviteInsuranceAutoCreate")
public class InviteInsuranceAutoCreateController {

    @Autowired
    InviteInsuranceVehicleTaskService inviteInsuranceVehicleTaskService;





    /**
     * 初始化数据
     * @param createDate
     * @param days
     * @return
     */
    /*
    @ApiOperation(value = "", notes = "", httpMethod = "")
    @GetMapping("/initInviteInfo")
    public int initInviteInfo(@RequestParam(value = "createDate") String createDate,
                                 @RequestParam(value = "days") Integer days){
        return inviteVehicleTaskService.initInviteInfo(createDate,days);
    }*/



    /**
     * 根据续保邀约任务创建邀约线索
     * @param createDate
     * @return
     *
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "")
    })
    @ApiOperation(value = "根据续保邀约任务创建邀约线索", notes = "根据续保邀约任务创建邀约线索", httpMethod = "GET")
    @GetMapping("/createInviteInsuranceByTask")
    public int createInviteInsuranceByTask(
            @RequestParam(value = "ownerCode", required = false) String ownerCode,
            @RequestParam(value = "vin", required = false) String vin,
            @RequestParam(value = "createDate", required = false) String createDate){
        return inviteInsuranceVehicleTaskService.createInviteInsuranceByTask(ownerCode, vin, createDate);
    }

    /**
     * 创建新车的续保任务
     * @param createDate
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "")
    })
    @ApiOperation(value = "创建新车的续保任务", notes = "创建新车的续保任务", httpMethod = "GET")
    @GetMapping("/createNewVehicleInsuranceTask")
    public int insuranceTaskAutoCreate(@RequestParam(value = "createDate", required = false) String createDate){
        return inviteInsuranceVehicleTaskService.insuranceTaskAutoCreate(createDate);
    }


}
