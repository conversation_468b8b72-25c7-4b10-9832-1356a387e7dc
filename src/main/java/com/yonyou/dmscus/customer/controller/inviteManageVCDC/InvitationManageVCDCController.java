package com.yonyou.dmscus.customer.controller.inviteManageVCDC;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.service.excel.ExcelGenerator;
import com.yonyou.dmscus.customer.dto.InvitationDeleteParamDTO;
import com.yonyou.dmscus.customer.dto.InvitationFollowParamsDTO;
import com.yonyou.dmscus.customer.dto.invitationFollow.InvitationFollowParamsDto;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleDto;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteManageVCDC.AllocateDealerDTO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.service.inviteManageVCDC.InviteManageVCDCService;
import com.yonyou.dmscus.customer.utils.DataConvertUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @since 2020-04-24
 */
@Api(value = "/invitationManageVCDC", tags = {"InvitationManageVCDCController"})
@RestController
@RequestMapping("/invitationManageVCDC")
@Slf4j
public class InvitationManageVCDCController {

    @Autowired
    InviteManageVCDCService inviteManageVCDCService;
    @Autowired
    ExcelGenerator excelGenerator;

    /**
     * 分页查询数据
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-24
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InvitationFollowParamsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "POST")
    @PostMapping("/list")
    public IPage<InviteVehicleDto> getInviteVehicleRecord(@RequestBody InvitationFollowParamsDto dto) {
        Page<InviteVehicleRecordPO> page = new Page(dto.getCurrentPage(), dto.getPageSize());
        InviteVehicleRecordDTO inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        inviteVehicleRecordDTO.setLargeAreaId(dto.getLargeAreaId());
        inviteVehicleRecordDTO.setAreaId(dto.getAreaId());
        inviteVehicleRecordDTO.setDealerCode(dto.getDealerCode());
        inviteVehicleRecordDTO.setInviteTypeParam(dto.getInviteType());
        inviteVehicleRecordDTO.setPlanFollowDateStart(dto.getPlanFollowDateStart());
        inviteVehicleRecordDTO.setPlanFollowDateEnd(dto.getPlanFollowDateEnd());
        inviteVehicleRecordDTO.setLicensePlateNum(dto.getLicensePlateNum());
        inviteVehicleRecordDTO.setVin(dto.getVin());
        inviteVehicleRecordDTO.setActualFollowDateStart(dto.getActualFollowDateStart());
        inviteVehicleRecordDTO.setActualFollowDateEnd(dto.getActualFollowDateEnd());
        inviteVehicleRecordDTO.setName(dto.getName());
        inviteVehicleRecordDTO.setIsBook(dto.getIsBook());
        inviteVehicleRecordDTO.setAdviseInDateStart(dto.getAdviseInDateStart());
        inviteVehicleRecordDTO.setAdviseInDateEnd(dto.getAdviseInDateEnd());
        inviteVehicleRecordDTO.setFollowStatusParam(dto.getFollowStatus());
        inviteVehicleRecordDTO.setSaName(dto.getSaName());
        inviteVehicleRecordDTO.setCreatedAtStart(dto.getCreatedAtStart());
        inviteVehicleRecordDTO.setCreatedAtEnd(dto.getCreatedAtEnd());
        inviteVehicleRecordDTO.setOrderStatusParam(dto.getOrderStatusParam());
        inviteVehicleRecordDTO.setMonthTwice(dto.getMonthTwice());
        inviteVehicleRecordDTO.setRecordType(dto.getRecordType());
        inviteVehicleRecordDTO.setLossType(dto.getLossType());
        inviteVehicleRecordDTO.setCouponCode(dto.getCouponCode());
        inviteVehicleRecordDTO.setCouponName(dto.getCouponName());
        inviteVehicleRecordDTO.setRecordTypeParam(dto.getRecordTypeParam());
        inviteVehicleRecordDTO.setLossWarningType(dto.getLossWarningType());
        inviteVehicleRecordDTO.setBevFlag(dto.getBevFlag());
        inviteVehicleRecordDTO.setReturnIntentionLevel(dto.getReturnIntentionLevel());
        return inviteManageVCDCService.getInviteVehicleRecord(page, inviteVehicleRecordDTO, dto.getInviteName());
    }


    /**
     * 分配
     *
     * @param dto
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AllocateDealerDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "分配", notes = "分配", httpMethod = "POST")
    @PostMapping("/allocateDealer")
    @ResponseStatus(HttpStatus.CREATED)
    public int allocateDealer(@RequestBody AllocateDealerDTO dto) {
        return inviteManageVCDCService.allocateDealer(dto);
    }


    /**
     * 导出
     * @param dto
     * @param request
     * @param response
     * @throws Exception
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InvitationFollowParamsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "导出", notes = "导出", httpMethod = "POST")
    @PostMapping("/exportExcel")
    public void exportExcel(@RequestBody InvitationFollowParamsDto dto,
                            HttpServletRequest request, HttpServletResponse response) throws Exception {
        InviteVehicleRecordDTO inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        inviteVehicleRecordDTO.setLargeAreaId(dto.getLargeAreaId());
        inviteVehicleRecordDTO.setAreaId(dto.getAreaId());
        inviteVehicleRecordDTO.setDealerCode(dto.getDealerCode());
        inviteVehicleRecordDTO.setInviteTypeParam(dto.getInviteType());
        inviteVehicleRecordDTO.setPlanFollowDateStart(dto.getPlanFollowDateStart());
        inviteVehicleRecordDTO.setPlanFollowDateEnd(dto.getPlanFollowDateEnd());
        inviteVehicleRecordDTO.setLicensePlateNum(dto.getLicensePlateNum());
        inviteVehicleRecordDTO.setVin(dto.getVin());
        inviteVehicleRecordDTO.setActualFollowDateStart(dto.getActualFollowDateStart());
        inviteVehicleRecordDTO.setActualFollowDateEnd(dto.getActualFollowDateEnd());
        inviteVehicleRecordDTO.setName(dto.getName());
        inviteVehicleRecordDTO.setIsBook(dto.getIsBook());
        inviteVehicleRecordDTO.setAdviseInDateStart(dto.getAdviseInDateStart());
        inviteVehicleRecordDTO.setAdviseInDateEnd(dto.getAdviseInDateEnd());
        inviteVehicleRecordDTO.setFollowStatusParam(dto.getFollowStatus());
        inviteVehicleRecordDTO.setSaName(dto.getSaName());
        inviteVehicleRecordDTO.setCreatedAtStart(dto.getCreatedAtStart());
        inviteVehicleRecordDTO.setCreatedAtEnd(dto.getCreatedAtEnd());
        inviteVehicleRecordDTO.setOrderStatusParam(dto.getOrderStatusParam());
        inviteVehicleRecordDTO.setMonthTwice(dto.getMonthTwice());
        inviteVehicleRecordDTO.setRecordType(dto.getRecordType());
        inviteVehicleRecordDTO.setCouponCode(dto.getCouponCode());
        inviteVehicleRecordDTO.setCouponName(dto.getCouponName());
        inviteVehicleRecordDTO.setRecordTypeParam(dto.getRecordTypeParam());
        inviteVehicleRecordDTO.setLossType(dto.getLossType());
        inviteVehicleRecordDTO.setLossWarningType(dto.getLossWarningType());
        inviteVehicleRecordDTO.setBevFlag(dto.getBevFlag());
        inviteVehicleRecordDTO.setReturnIntentionLevel(dto.getReturnIntentionLevel());
        //查询并导出 Excel
        inviteManageVCDCService.exportExcel(inviteVehicleRecordDTO, response, dto.getInviteName());
    }
    
    
    /**
     * 导出
     * @param dto
     * @param request
     * @param response
     * @throws Exception
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InvitationFollowParamsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "导出", notes = "导出", httpMethod = "POST")
    @PostMapping("/exportExcel/down")
    public void exportExcelByDown(@RequestBody InvitationFollowParamsDTO dto) throws Exception {
        InviteVehicleRecordDTO inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        inviteVehicleRecordDTO.setLargeAreaId(dto.getLargeAreaId());
        inviteVehicleRecordDTO.setAreaId(dto.getAreaId());
        inviteVehicleRecordDTO.setDealerCode(dto.getDealerCode());
        inviteVehicleRecordDTO.setInviteTypeParam(dto.getInviteType());
        inviteVehicleRecordDTO.setPlanFollowDateStart(dto.getPlanFollowDateStart());
        inviteVehicleRecordDTO.setPlanFollowDateEnd(dto.getPlanFollowDateEnd());
        inviteVehicleRecordDTO.setLicensePlateNum(dto.getLicensePlateNum());
        inviteVehicleRecordDTO.setVin(dto.getVin());
        inviteVehicleRecordDTO.setActualFollowDateStart(dto.getActualFollowDateStart());
        inviteVehicleRecordDTO.setActualFollowDateEnd(dto.getActualFollowDateEnd());
        inviteVehicleRecordDTO.setName(dto.getName());
        inviteVehicleRecordDTO.setIsBook(dto.getIsBook());
        inviteVehicleRecordDTO.setAdviseInDateStart(dto.getAdviseInDateStart());
        inviteVehicleRecordDTO.setAdviseInDateEnd(dto.getAdviseInDateEnd());
        inviteVehicleRecordDTO.setFollowStatusParam(dto.getFollowStatus());
        inviteVehicleRecordDTO.setSaName(dto.getSaName());
        inviteVehicleRecordDTO.setCreatedAtStart(dto.getCreatedAtStart());
        inviteVehicleRecordDTO.setCreatedAtEnd(dto.getCreatedAtEnd());
        inviteVehicleRecordDTO.setOrderStatusParam(dto.getOrderStatusParam());
        inviteVehicleRecordDTO.setMonthTwice(dto.getMonthTwice());
        inviteVehicleRecordDTO.setRecordType(dto.getRecordType());
        inviteVehicleRecordDTO.setCouponCode(dto.getCouponCode());
        inviteVehicleRecordDTO.setCouponName(dto.getCouponName());
        inviteVehicleRecordDTO.setRecordTypeParam(dto.getRecordTypeParam());
        inviteVehicleRecordDTO.setLossType(dto.getLossType());
        inviteVehicleRecordDTO.setLossWarningType(dto.getLossWarningType());
        inviteVehicleRecordDTO.setBevFlag(dto.getBevFlag());
        inviteVehicleRecordDTO.setReturnIntentionLevel(dto.getReturnIntentionLevel());
        //查询并导出 Excel
        inviteManageVCDCService.exportExcelByDown(inviteVehicleRecordDTO);
    }
    
    
    /**
     * 下载中心回调
     * @param dto
     * @param request
     * @param response
     * @throws Exception
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "Map<String, Object>", name = "queryParam", value = "", required = true)
    })
    @ApiOperation(value = "导出", notes = "下载中心回调", httpMethod = "GET")
    @GetMapping("/downLoadExportExcel")
    public List<Map> downLoadExportExcel(@RequestParam Map<String, Object> queryParam) {
    	queryParam.put("recordTypeParam", DataConvertUtils.stringToIntegerList(queryParam.get("recordTypeParam")));
    	queryParam.put("inviteTypeParam", DataConvertUtils.stringToIntegerList(queryParam.get("inviteTypeParam")));
    	queryParam.put("followStatusParam", DataConvertUtils.stringToIntegerList(queryParam.get("followStatusParam")));
    	queryParam.put("orderStatusParam", DataConvertUtils.stringToIntegerList(queryParam.get("orderStatusParam")));
    	queryParam.put("leaveIds", DataConvertUtils.stringToIntegerList(queryParam.get("leaveIds")));
    	InviteVehicleRecordDTO inviteVehicleRecordDTO = BeanUtil.mapToBean(queryParam, InviteVehicleRecordDTO.class, false);
    	
    	log.info("queryParam::{}", JSON.toJSONString(queryParam));
    	Integer currentPage = Integer.valueOf(queryParam.get("currentPage").toString());
    	Integer pSize = Integer.valueOf(queryParam.get("pSize").toString());
    	if(currentPage<1) {
    		currentPage=1;
    	}
		int offset = (currentPage-1)*pSize;
        //查询并导出 Excel
        return inviteManageVCDCService.downLoadExportExcel(inviteVehicleRecordDTO, offset, pSize);
    }


    /**
     * 查询邀约线索车辆信息和跟进明细
     *
     * @param vin
     * @param id
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "查询邀约线索车辆信息和跟进明细", notes = "查询邀约线索车辆信息和跟进明细", httpMethod = "GET")
    @GetMapping("/getInviteVehicleRecordInfo")
    public InviteVehicleRecordDTO getInviteVehicleRecordInfo(
            @RequestParam(value = "vin", required = true) String vin,
            @RequestParam(value = "id", required = true) Long id) {
        return inviteManageVCDCService.getInviteVehicleRecordInfo(vin, id);
    }

    /**
     * 查询邀约线索车辆信息和跟进明细
     *
     * @param vin
     * @param id
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "", required = true)
    })
    @ApiOperation(value = "查询邀约线索车辆信息和跟进明细", notes = "查询邀约线索车辆信息和跟进明细", httpMethod = "GET")
    @GetMapping("/getInviteVehicleRecordInfoDlr")
    public List<InviteVehicleRecordDetailDTO> getInviteVehicleRecordInfoDlr(
            @RequestParam(value = "vin", required = true) String vin,
            @RequestParam(value = "id", required = true) Long id,
            @RequestParam("ownerCode") String ownerCode) {
        return inviteManageVCDCService.getInviteVehicleRecordInfoDlr(vin, id, ownerCode);
    }

    /**
     * 查询邀约线索车辆信息和跟进历史
     *
     * @param vin
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "", required = true)
    })
    @ApiOperation(value = "查询邀约线索车辆信息和跟进历史", notes = "查询邀约线索车辆信息和跟进历史", httpMethod = "GET")
    @GetMapping("/getInviteVehicleRecordInfoHistoryDlr")
    public IPage<InviteVehicleRecordDetailDTO> getInviteVehicleRecordInfoHistoryDlr(
            @RequestParam("currentPage") int currentPage,
            @RequestParam("pageSize") int pageSize,
            @RequestParam(value = "vin") String vin,
            @RequestParam("ownerCode") String ownerCode) {
        Page<InviteVehicleRecordDetailDTO> page = new Page(currentPage, pageSize);
        return inviteManageVCDCService.getInviteVehicleRecordInfoHistoryDlr(page,vin,ownerCode);
    }

    /**
     * 功能描述：自建邀约线索删除功能
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InvitationDeleteParamDTO", name = "param", value = "", required = true)
    })
    @ApiOperation(value = "自建邀约线索删除功能", notes = "功能描述：自建邀约线索删除功能", httpMethod = "POST")
    @PostMapping("/oem/delete")
    public void deleteSelfCreateInvitation(@RequestBody InvitationDeleteParamDTO param) {
        inviteManageVCDCService.deleteSelfCreateInvitation(param);
    }

}