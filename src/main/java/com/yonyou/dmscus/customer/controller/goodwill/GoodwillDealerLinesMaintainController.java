package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.service.excel.ExcelExportColumn;
import com.yonyou.dmscloud.framework.service.excel.ExcelGenerator;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillDealerLinesMaintainDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.companySelectDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillDealerLinesMaintainPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillDealerLinesMaintainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
@Api(value = "/goodwillDealerLinesMaintain", tags = {"GoodwillDealerLinesMaintainController"})
@RestController
@RequestMapping("/goodwillDealerLinesMaintain")
public class GoodwillDealerLinesMaintainController {
	@Autowired
	GoodwillDealerLinesMaintainService goodwillDealerLinesMaintainService;
	@Autowired
	private ExcelGenerator excelGenerator;

	/**
	 * 分页查询数据
	 *
	 * @param appId          系统ID
	 * @param ownerCode      所有者代码
	 * @param ownerParCode   所有者的父组织代码
	 * @param orgId          组织ID
	 * @param id             主键id
	 * @param goodwillNature 亲善性质
	 * @param dealerCode     经销商代码
	 * @param validStartDate 有效开始时间
	 * @param validEndDate   有效结束时间
	 * @param yearlyBudget   年度预算
	 * @param foundDate      创建日期
	 * @param isValid        是否有效
	 * @param isDeleted      是否删除:1,删除；0,未删除
	 * @param createdAt      创建时间
	 * @param updatedAt      修改时间
	 * @param currentPage    页数
	 * @param pageSize       分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * @throws ParseException
	 * <AUTHOR>
	 * @since 2020-04-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键id"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillNature", value = "亲善性质"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "经销商代码"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "validStartDate", value = "有效开始时间"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "validEndDate", value = "有效结束时间"),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "yearlyBudget", value = "年度预算"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "foundDateStart", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "foundDateEnd", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除:1,删除；0,未删除"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = "创建时间"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = "修改时间"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
	})
	@ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
	@GetMapping
	public IPage<GoodwillDealerLinesMaintainDTO> getByPage(
			@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "goodwillNature", required = false) Integer goodwillNature,
			@RequestParam(value = "areaManage", required = false) Integer areaManage,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "validStartDate", required = false) String validStartDate,
			@RequestParam(value = "validEndDate", required = false) String validEndDate,
			@RequestParam(value = "yearlyBudget", required = false) BigDecimal yearlyBudget,
			@RequestParam(value = "foundDateStart", required = false) String foundDateStart,
			@RequestParam(value = "foundDateEnd", required = false) String foundDateEnd,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillDealerLinesMaintainPO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO = new GoodwillDealerLinesMaintainDTO();
		goodwillDealerLinesMaintainDTO.setAppId(appId);
		goodwillDealerLinesMaintainDTO.setOwnerCode(ownerCode);
		goodwillDealerLinesMaintainDTO.setOwnerParCode(ownerParCode);
		goodwillDealerLinesMaintainDTO.setOrgId(orgId);
		goodwillDealerLinesMaintainDTO.setId(id);
		goodwillDealerLinesMaintainDTO.setGoodwillNature(goodwillNature);
		goodwillDealerLinesMaintainDTO.setDealerCode(dealerCode);

		goodwillDealerLinesMaintainDTO.setYearlyBudget(yearlyBudget);

		goodwillDealerLinesMaintainDTO.setIsValid(isValid);
		goodwillDealerLinesMaintainDTO.setIsDeleted(isDeleted);

		goodwillDealerLinesMaintainDTO.setAreaManage(areaManage);
		if (validStartDate != null) {
			goodwillDealerLinesMaintainDTO.setValidStartDate(sdf.parse(validStartDate));
		}
		if (validEndDate != null) {
			goodwillDealerLinesMaintainDTO.setValidEndDate(sdf.parse(validEndDate));
		}
		if (foundDateStart != null) {
			goodwillDealerLinesMaintainDTO.setFoundDateStart(sdf.parse(foundDateStart));
		}
		if (foundDateEnd != null) {
			goodwillDealerLinesMaintainDTO.setFoundDateEnd(sdf.parse(foundDateEnd));
		}
		if (createdAt != null) {
			goodwillDealerLinesMaintainDTO.setCreatedAt(sdf.parse(createdAt));
		}
		if (updatedAt != null) {
			goodwillDealerLinesMaintainDTO.setUpdatedAt(sdf.parse(updatedAt));
		}
		return goodwillDealerLinesMaintainService.selectPageBysql(page, goodwillDealerLinesMaintainDTO);
	}

	/**
	 * 分页查询经销商数据
	 *
	 * @param dealerCode  经销商代码
	 * @param currentPage 页数
	 * @param pageSize    分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * @throws ParseException
	 * <AUTHOR>
	 * @since 2020-04-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "经销商代码"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "area", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "dealerType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "provinceId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "cityId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
	})
	@ApiOperation(value = "分页查询经销商数据", notes = "分页查询经销商数据", httpMethod = "GET")
	@GetMapping(value = "/dealerInfo")
	public IPage<Map> getDealerInfo(@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "area", required = false) String area,
			@RequestParam(value = "dealerType", required = false) Integer dealerType,
			@RequestParam(value = "provinceId", required = false) String provinceId,
			@RequestParam(value = "cityId", required = false) String cityId,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<Map> page = new Page(currentPage, pageSize);
		companySelectDTO dto = new companySelectDTO();
		dto.setCompanyCode(dealerCode);
		dto.setAfterBigArea(area);
		dto.setDealerType(dealerType);
		dto.setProvinceId(provinceId);
		dto.setCityId(cityId);

		return goodwillDealerLinesMaintainService.getDealerInfo(page, dto);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id 数据主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillDealerLinesMaintainDTO
	 * <AUTHOR>
	 * @since 2020-04-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
	@GetMapping(value = "/{id}")
	public GoodwillDealerLinesMaintainDTO getById(@PathVariable("id") Long id) {
		return goodwillDealerLinesMaintainService.getById(id);
	}

	/**
	 * 进行数据新增
	 *
	 * @param goodwillDealerLinesMaintainDTO 需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-04-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillDealerLinesMaintainDTO", name = "goodwillDealerLinesMaintainDTO", value = "需要保存的DTO", required = true)
	})
	@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
	@PostMapping
	@ResponseStatus(HttpStatus.CREATED)
	public int insert(@RequestBody GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO) {
		return goodwillDealerLinesMaintainService.insert(goodwillDealerLinesMaintainDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id                             需要修改数据的ID
	 * @param goodwillDealerLinesMaintainDTO 需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-04-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillDealerLinesMaintainDTO", name = "goodwillDealerLinesMaintainDTO", value = "需要保存的DTO", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
	@PutMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.CREATED)
	public int update(@PathVariable("id") Long id,
			@RequestBody GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO) {
		return goodwillDealerLinesMaintainService.update(id, goodwillDealerLinesMaintainDTO);
	}

	/**
	 * 根据id删除对象
	 *
	 * @param id 需要修改数据的ID
	 * <AUTHOR>
	 * @since 2020-04-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
	})
	@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteById(@PathVariable("id") Long id) {
		goodwillDealerLinesMaintainService.deleteById(id);
		return true;
	}

	/**
	 * 根据IDs批量删除对象
	 *
	 * @param ids 需要修改数据的ID集合
	 * <AUTHOR>
	 * @since 2020-04-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "需要修改数据的ID集合", required = true)
	})
	@ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/batch/{ids}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteByIds(@PathVariable("ids") String ids) {
		goodwillDealerLinesMaintainService.deleteBatchIds(ids);
		return true;
	}

	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
	})
	@ApiOperation(value = "根据id逻辑删除数据", notes = "", httpMethod = "POST")
	@PostMapping("/delete/{id}")
	public void deleteDealerLinesById(@PathVariable("id") Long id) {
		goodwillDealerLinesMaintainService.deleteDealerLinesById(id);
	}

	/**
	 * 进行数据新增
	 *
	 * @param GoodwillAwaPrintMaintainDTO 需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-04-20
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillDealerLinesMaintainDTO", name = "goodwillDealerLinesMaintainDTO", value = "", required = true)
	})
	@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
	@PostMapping(value = "/addDealerLinesInfo")
	@ResponseStatus(HttpStatus.CREATED)
	public int addDealerLinesInfo(@RequestBody GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO) {
		return goodwillDealerLinesMaintainService.addDealerLinesInfo(goodwillDealerLinesMaintainDTO);
	}

	/**
	 * 延保购买及赠送记录导出
	 *
	 * @param queryParam
	 * @throws ParseException
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "Map<String, String>", name = "queryParam", value = "", required = true)
	})
	@ApiOperation(value = "经销商亲善限额设置导出", notes = "经销商亲善限额设置导出", httpMethod = "GET")
	@RequestMapping(value = "/exportDealerLinesInfo/export", method = RequestMethod.GET)
	public void getExtendedPurchaseGiveInfoExport(@RequestParam Map<String, String> queryParam,
			HttpServletRequest request, HttpServletResponse response)
			throws ServiceBizException, SQLException, ParseException {

		List<Map> resultList = goodwillDealerLinesMaintainService.selectExportListBySql(queryParam);
		Map<String, List<Map>> excelData = new HashMap<String, List<Map>>(16);
		excelData.put("经销商亲善限额设置导出", resultList);
		List<ExcelExportColumn> exportColumnList = new ArrayList<ExcelExportColumn>();

		exportColumnList.add(new ExcelExportColumn("dealer_code", "经销商代码"));
		exportColumnList.add(new ExcelExportColumn("dealerName", "经销商名称"));
		exportColumnList.add(new ExcelExportColumn("areaManage", "区域-区域经理"));
		exportColumnList.add(new ExcelExportColumn("bloc", "集团"));
		exportColumnList.add(new ExcelExportColumn("provinceName", "省份"));
		exportColumnList.add(new ExcelExportColumn("goodwill_nature", "亲善性质"));
		exportColumnList.add(new ExcelExportColumn("yearly_budget", "年度预算(万)"));
		exportColumnList.add(new ExcelExportColumn("is_valid", "是否有效"));
		exportColumnList.add(new ExcelExportColumn("valid_start_date", "有效开始日期", "yyyy-MM-dd"));
		exportColumnList.add(new ExcelExportColumn("valid_end_date", "有效结束日期", "yyyy-MM-dd"));
		exportColumnList.add(new ExcelExportColumn("found_date", "创建日期", "yyyy-MM-dd"));
		excelGenerator.generateExcel(excelData, exportColumnList, "经销商亲善限额设置信息.xlsx", request, response);
	}

}