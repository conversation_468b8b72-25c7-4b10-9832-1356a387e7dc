package com.yonyou.dmscus.customer.controller.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.service.excel.ExcelExportColumn;
import com.yonyou.dmscloud.framework.service.excel.ExcelGenerator;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoEtlDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoEtlPO;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintInfoEtlService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @since 2021-07-22
 */
@Api(value = "/saleComplaintInfoEtl", tags = {"SaleComplaintInfoEtlController"})
@RestController
@RequestMapping("/saleComplaintInfoEtl" )
public class SaleComplaintInfoEtlController extends BaseController {

    @Autowired
    SaleComplaintInfoEtlService saleComplaintInfoEtlService;
    @Autowired
    ExcelGenerator excelGenerator;

    /**
     * 分页查询数据
     *
     * @param id                           主键ID
     * @param callTime                     投诉日期
     * @param complaintId                  投诉ID（投诉编号）可 用于和呼叫中心对接
     * @param workOrderNature              工单性质
     * @param category1                    投诉单类别一级层
     * @param category2                    投诉单类别二级层
     * @param category3                    投诉单类别三级层
     * @param buyDealerCode                购买经销商
     * @param buyDealerName                购买经销商
     * @param dealerCode                   处理经销商代码
     * @param dealerName                   处理经销商
     * @param customerType                 客户类型
     * @param callName                     来电客户姓名/投诉人姓名
     * @param callTel                      来电电话/投诉人电话
     * @param replyTel                     回复联系人手机1
     * @param replyTel2                    回复联系人手机2
     * @param vin                          车架号
     * @param name                         车主姓名
     * @param phone                        车主手机
     * @param model                        车型
     * @param licensePlateNum              车牌号
     * @param buyTime                      购车时间
     * @param lifeCycle                    生命周期
     * @param mileage                      里程
     * @param problem                      问题描述
     * @param workOrderStatus              工单状态
     * @param closeCaseStatus              结案状态（投诉单状态）
     * @param dealerFisrtReplyTime         经销商首次回复时间
     * @param closeCaseTime                结案时间
     * @param solveTime                    投诉解决时长
     * @param reviewer                     审核人
     * @param activitySource               活动来源
     * @param revisitDemand                回访需求
     * @param emailStatus                  邮件状态
     * @param is48hSendEmail               经销商是否在48H内推出邮件
     * @param type                         投诉类型
     * @param againCallTime                再次投诉时间
     * @param sms                          短信
     * @param effectiveComplaint           有效投诉
     * @param source                       投诉来源
     * @param noCloseReasonsClassification 未结案原因分类
     * @param lastFollowTime               最后跟进时间
     * @param lastComplaintTime            最后投诉时间
     * @param complaintNumber              总投诉次数
     * @param responsibilityDetermine      责任判定
     * @param subject                      投诉主题
     * @param classification1              分类1
     * @param classification2              分类2
     * @param classification3              分类3
     * @param classification4              分类4
     * @param classification5              分类5
     * @param classification6              分类6
     * @param informationSource            信息来源
     * @param keyword                      关键字
     * @param consultationAndSolution      咨询解决情况
     * @param solution                     解决方法
     * @param isFaq                        FAQ支持
     * @param emotionIndexCreation         情绪指数新建
     * @param sentimentIndexCurrent        情绪指数新建
     * @param ccmDeal                      CCM系统协助处理
     * @param firstRevisitTime             首次回访需求时间
     * @param restartTime                  重启日期
     * @param restartNumber                重启次数
     * @param secondRestartTime            第二次重启时间
     * @param secondeCloseCaseTime         第二次结案时间
     * @param caseLevel                    案件等级
     * @param is24hReply                   是否24H回复
     * @param is48hRevisit                 是否48H回访
     * @param is5dCloseCase                是否5日结案
     * @param regionalFeedback             区域反馈回访
     * @param regionalFeedbackTime         区域反馈回访日
     * @param noRevisitReason              不回访理由
     * @param noCloseReasons               未结案原因
     * @param serviceCommitment            服务承诺
     * @param firstReturnTime              首次回访时间
     * @param currentPage                  页数
     * @param pageSize                     分页大小
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2021-07-22
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "callTime", value = "投诉日期"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintId", value = "投诉ID（投诉编号）可 用于和呼叫中心对接"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "workOrderNature", value = "工单性质"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "category1", value = "投诉单类别一级层"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "category2", value = "投诉单类别二级层"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "category3", value = "投诉单类别三级层"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "buyDealerCode", value = "购买经销商"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "buyDealerName", value = "购买经销商"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "处理经销商代码"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerName", value = "处理经销商"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "customerType", value = "客户类型"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "callName", value = "来电客户姓名/投诉人姓名"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "callTel", value = "来电电话/投诉人电话"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "replyTel", value = "回复联系人手机1"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "replyTel2", value = "回复联系人手机2"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "车架号"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "name", value = "车主姓名"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "phone", value = "车主手机"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "model", value = "车型"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "licensePlateNum", value = "车牌号"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "buyTime", value = "购车时间"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "lifeCycle", value = "生命周期"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "mileage", value = "里程"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "problem", value = "问题描述"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "workOrderStatus", value = "工单状态"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "closeCaseStatus", value = "结案状态（投诉单状态）"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerFisrtReplyTime", value = "经销商首次回复时间"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "closeCaseTime", value = "结案时间"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "solveTime", value = "投诉解决时长"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "reviewer", value = "审核人"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "activitySource", value = "活动来源"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "revisitDemand", value = "回访需求"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "emailStatus", value = "邮件状态"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "is48hSendEmail", value = "经销商是否在48H内推出邮件"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "type", value = "投诉类型"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "againCallTime", value = "再次投诉时间"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "sms", value = "短信"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "effectiveComplaint", value = "有效投诉"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "source", value = "投诉来源"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "noCloseReasonsClassification", value = "未结案原因分类"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "lastFollowTime", value = "最后跟进时间"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "lastComplaintTime", value = "最后投诉时间"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintNumber", value = "总投诉次数"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "responsibilityDetermine", value = "责任判定"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "subject", value = "投诉主题"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "classification1", value = "分类1"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "classification2", value = "分类2"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "classification3", value = "分类3"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "classification4", value = "分类4"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "classification5", value = "分类5"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "classification6", value = "分类6"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "informationSource", value = "信息来源"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "keyword", value = "关键字"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "consultationAndSolution", value = "咨询解决情况"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "solution", value = "解决方法"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "isFaq", value = "FAQ支持"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "emotionIndexCreation", value = "情绪指数新建"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "sentimentIndexCurrent", value = "情绪指数新建"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ccmDeal", value = "CCM系统协助处理"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "firstRevisitTime", value = "首次回访需求时间"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "restartTime", value = "重启日期"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "restartNumber", value = "重启次数"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "secondRestartTime", value = "第二次重启时间"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "secondeCloseCaseTime", value = "第二次结案时间"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "caseLevel", value = "案件等级"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "is24hReply", value = "是否24H回复"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "is48hRevisit", value = "是否48H回访"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "is5dCloseCase", value = "是否5日结案"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "regionalFeedback", value = "区域反馈回访"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "regionalFeedbackTime", value = "区域反馈回访日"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "noRevisitReason", value = "不回访理由"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "noCloseReasons", value = "未结案原因"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "serviceCommitment", value = "服务承诺"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "firstReturnTime", value = "首次回访时间"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "startCallTime", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "endCallTime", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
    })
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
    @GetMapping
    public IPage<SaleComplaintInfoEtlDTO> getByPage(
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "callTime", required = false) String callTime,
            @RequestParam(value = "complaintId", required = false) String complaintId,
            @RequestParam(value = "workOrderNature", required = false) String workOrderNature,
            @RequestParam(value = "category1", required = false) String category1,
            @RequestParam(value = "category2", required = false) String category2,
            @RequestParam(value = "category3", required = false) String category3,
            @RequestParam(value = "buyDealerCode", required = false) String buyDealerCode,
            @RequestParam(value = "buyDealerName", required = false) String buyDealerName,
            @RequestParam(value = "dealerCode", required = false) String dealerCode,
            @RequestParam(value = "dealerName", required = false) String dealerName,
            @RequestParam(value = "customerType", required = false) String customerType,
            @RequestParam(value = "callName", required = false) String callName,
            @RequestParam(value = "callTel", required = false) String callTel,
            @RequestParam(value = "replyTel", required = false) String replyTel,
            @RequestParam(value = "replyTel2", required = false) String replyTel2,
            @RequestParam(value = "vin", required = false) String vin,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "phone", required = false) String phone,
            @RequestParam(value = "model", required = false) String model,
            @RequestParam(value = "licensePlateNum", required = false) String licensePlateNum,
            @RequestParam(value = "buyTime", required = false) String buyTime,
            @RequestParam(value = "lifeCycle", required = false) String lifeCycle,
            @RequestParam(value = "mileage", required = false) String mileage,
            @RequestParam(value = "problem", required = false) String problem,
            @RequestParam(value = "workOrderStatus", required = false) String workOrderStatus,
            @RequestParam(value = "closeCaseStatus", required = false) String closeCaseStatus,
            @RequestParam(value = "dealerFisrtReplyTime", required = false) String dealerFisrtReplyTime,
            @RequestParam(value = "closeCaseTime", required = false) String closeCaseTime,
            @RequestParam(value = "solveTime", required = false) String solveTime,
            @RequestParam(value = "reviewer", required = false) String reviewer,
            @RequestParam(value = "activitySource", required = false) String activitySource,
            @RequestParam(value = "revisitDemand", required = false) String revisitDemand,
            @RequestParam(value = "emailStatus", required = false) String emailStatus,
            @RequestParam(value = "is48hSendEmail", required = false) String is48hSendEmail,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "againCallTime", required = false) String againCallTime,
            @RequestParam(value = "sms", required = false) String sms,
            @RequestParam(value = "effectiveComplaint", required = false) String effectiveComplaint,
            @RequestParam(value = "source", required = false) String source,
            @RequestParam(value = "noCloseReasonsClassification", required = false) String noCloseReasonsClassification,
            @RequestParam(value = "lastFollowTime", required = false) String lastFollowTime,
            @RequestParam(value = "lastComplaintTime", required = false) String lastComplaintTime,
            @RequestParam(value = "complaintNumber", required = false) String complaintNumber,
            @RequestParam(value = "responsibilityDetermine", required = false) String responsibilityDetermine,
            @RequestParam(value = "subject", required = false) String subject,
            @RequestParam(value = "classification1", required = false) String classification1,
            @RequestParam(value = "classification2", required = false) String classification2,
            @RequestParam(value = "classification3", required = false) String classification3,
            @RequestParam(value = "classification4", required = false) String classification4,
            @RequestParam(value = "classification5", required = false) String classification5,
            @RequestParam(value = "classification6", required = false) String classification6,
            @RequestParam(value = "informationSource", required = false) String informationSource,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "consultationAndSolution", required = false) String consultationAndSolution,
            @RequestParam(value = "solution", required = false) String solution,
            @RequestParam(value = "isFaq", required = false) String isFaq,
            @RequestParam(value = "emotionIndexCreation", required = false) String emotionIndexCreation,
            @RequestParam(value = "sentimentIndexCurrent", required = false) String sentimentIndexCurrent,
            @RequestParam(value = "ccmDeal", required = false) String ccmDeal,
            @RequestParam(value = "firstRevisitTime", required = false) String firstRevisitTime,
            @RequestParam(value = "restartTime", required = false) String restartTime,
            @RequestParam(value = "restartNumber", required = false) String restartNumber,
            @RequestParam(value = "secondRestartTime", required = false) String secondRestartTime,
            @RequestParam(value = "secondeCloseCaseTime", required = false) String secondeCloseCaseTime,
            @RequestParam(value = "caseLevel", required = false) String caseLevel,
            @RequestParam(value = "is24hReply", required = false) String is24hReply,
            @RequestParam(value = "is48hRevisit", required = false) String is48hRevisit,
            @RequestParam(value = "is5dCloseCase", required = false) String is5dCloseCase,
            @RequestParam(value = "regionalFeedback", required = false) String regionalFeedback,
            @RequestParam(value = "regionalFeedbackTime", required = false) String regionalFeedbackTime,
            @RequestParam(value = "noRevisitReason", required = false) String noRevisitReason,
            @RequestParam(value = "noCloseReasons", required = false) String noCloseReasons,
            @RequestParam(value = "serviceCommitment", required = false) String serviceCommitment,
            @RequestParam(value = "firstReturnTime", required = false) String firstReturnTime,
            @RequestParam(value = "startCallTime", required = false) String startCallTime,
            @RequestParam(value = "endCallTime", required = false) String endCallTime,
            @RequestParam("currentPage" ) int currentPage,
            @RequestParam("pageSize" ) int pageSize) {
        Page<SaleComplaintInfoEtlPO> page = new Page(currentPage, pageSize);
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd" );
        Calendar calendar = new GregorianCalendar();
        SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO = new SaleComplaintInfoEtlDTO();
        if (!StringUtils.isNullOrEmpty(endCallTime)) {
            try {
                Date endcallTime1 = sf.parse(endCallTime);
                calendar.setTime(endcallTime1);
                calendar.add(Calendar.DATE, 1);
                endcallTime1 = calendar.getTime();
                saleComplaintInfoEtlDTO.setEndCallTime(sf.format(endcallTime1));

            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        saleComplaintInfoEtlDTO.setId(id);
        saleComplaintInfoEtlDTO.setCallTime(callTime);
        saleComplaintInfoEtlDTO.setComplaintId(complaintId);
        saleComplaintInfoEtlDTO.setWorkOrderNature(workOrderNature);
        saleComplaintInfoEtlDTO.setCategory1(category1);
        saleComplaintInfoEtlDTO.setCategory2(category2);
        saleComplaintInfoEtlDTO.setCategory3(category3);
        saleComplaintInfoEtlDTO.setBuyDealerCode(buyDealerCode);
        saleComplaintInfoEtlDTO.setBuyDealerName(buyDealerName);
        saleComplaintInfoEtlDTO.setDealerCode(dealerCode);
        saleComplaintInfoEtlDTO.setDealerName(dealerName);
        saleComplaintInfoEtlDTO.setCustomerType(customerType);
        saleComplaintInfoEtlDTO.setCallName(callName);
        saleComplaintInfoEtlDTO.setCallTel(callTel);
        saleComplaintInfoEtlDTO.setReplyTel(replyTel);
        saleComplaintInfoEtlDTO.setReplyTel2(replyTel2);
        saleComplaintInfoEtlDTO.setVin(vin);
        saleComplaintInfoEtlDTO.setName(name);
        saleComplaintInfoEtlDTO.setPhone(phone);
        saleComplaintInfoEtlDTO.setModel(model);
        saleComplaintInfoEtlDTO.setLicensePlateNum(licensePlateNum);
        saleComplaintInfoEtlDTO.setBuyTime(buyTime);
        saleComplaintInfoEtlDTO.setLifeCycle(lifeCycle);
        saleComplaintInfoEtlDTO.setMileage(mileage);
        saleComplaintInfoEtlDTO.setProblem(problem);
        saleComplaintInfoEtlDTO.setWorkOrderStatus(workOrderStatus);
        saleComplaintInfoEtlDTO.setCloseCaseStatus(closeCaseStatus);
        saleComplaintInfoEtlDTO.setDealerFisrtReplyTime(dealerFisrtReplyTime);
        saleComplaintInfoEtlDTO.setCloseCaseTime(closeCaseTime);
        saleComplaintInfoEtlDTO.setSolveTime(solveTime);
        saleComplaintInfoEtlDTO.setReviewer(reviewer);
        saleComplaintInfoEtlDTO.setActivitySource(activitySource);
        saleComplaintInfoEtlDTO.setRevisitDemand(revisitDemand);
        saleComplaintInfoEtlDTO.setEmailStatus(emailStatus);
        saleComplaintInfoEtlDTO.setIs48hSendEmail(is48hSendEmail);
        saleComplaintInfoEtlDTO.setType(type);
        saleComplaintInfoEtlDTO.setAgainCallTime(againCallTime);
        saleComplaintInfoEtlDTO.setSms(sms);
        saleComplaintInfoEtlDTO.setEffectiveComplaint(effectiveComplaint);
        saleComplaintInfoEtlDTO.setSource(source);
        saleComplaintInfoEtlDTO.setNoCloseReasonsClassification(noCloseReasonsClassification);
        saleComplaintInfoEtlDTO.setLastFollowTime(lastFollowTime);
        saleComplaintInfoEtlDTO.setLastComplaintTime(lastComplaintTime);
        saleComplaintInfoEtlDTO.setComplaintNumber(complaintNumber);
        saleComplaintInfoEtlDTO.setResponsibilityDetermine(responsibilityDetermine);
        saleComplaintInfoEtlDTO.setSubject(subject);
        saleComplaintInfoEtlDTO.setClassification1(classification1);
        saleComplaintInfoEtlDTO.setClassification2(classification2);
        saleComplaintInfoEtlDTO.setClassification3(classification3);
        saleComplaintInfoEtlDTO.setClassification4(classification4);
        saleComplaintInfoEtlDTO.setClassification5(classification5);
        saleComplaintInfoEtlDTO.setClassification6(classification6);
        saleComplaintInfoEtlDTO.setInformationSource(informationSource);
        saleComplaintInfoEtlDTO.setKeyword(keyword);
        saleComplaintInfoEtlDTO.setConsultationAndSolution(consultationAndSolution);
        saleComplaintInfoEtlDTO.setSolution(solution);
        saleComplaintInfoEtlDTO.setIsFaq(isFaq);
        saleComplaintInfoEtlDTO.setEmotionIndexCreation(emotionIndexCreation);
        saleComplaintInfoEtlDTO.setSentimentIndexCurrent(sentimentIndexCurrent);
        saleComplaintInfoEtlDTO.setCcmDeal(ccmDeal);
        saleComplaintInfoEtlDTO.setFirstRevisitTime(firstRevisitTime);
        saleComplaintInfoEtlDTO.setRestartTime(restartTime);
        saleComplaintInfoEtlDTO.setRestartNumber(restartNumber);
        saleComplaintInfoEtlDTO.setSecondRestartTime(secondRestartTime);
        saleComplaintInfoEtlDTO.setSecondeCloseCaseTime(secondeCloseCaseTime);
        saleComplaintInfoEtlDTO.setCaseLevel(caseLevel);
        saleComplaintInfoEtlDTO.setIs24hReply(is24hReply);
        saleComplaintInfoEtlDTO.setIs48hRevisit(is48hRevisit);
        saleComplaintInfoEtlDTO.setIs5dCloseCase(is5dCloseCase);
        saleComplaintInfoEtlDTO.setRegionalFeedback(regionalFeedback);
        saleComplaintInfoEtlDTO.setRegionalFeedbackTime(regionalFeedbackTime);
        saleComplaintInfoEtlDTO.setNoRevisitReason(noRevisitReason);
        saleComplaintInfoEtlDTO.setNoCloseReasons(noCloseReasons);
        saleComplaintInfoEtlDTO.setServiceCommitment(serviceCommitment);
        saleComplaintInfoEtlDTO.setFirstReturnTime(firstReturnTime);
        saleComplaintInfoEtlDTO.setStartCallTime(startCallTime);
        return saleComplaintInfoEtlService.selectPageBysql(page, saleComplaintInfoEtlDTO);
    }



    /**
     * 进行数据修改
     *
     * @param id 数据主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoEtlDTO
     * <AUTHOR>
     * @since 2021-07-22
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
    })
    @ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
    @GetMapping(value = "/{id}" )
    public SaleComplaintInfoEtlDTO getById(@PathVariable("id" ) Long id) {
        return saleComplaintInfoEtlService.getById(id);
    }

    /**
     * 进行数据新增
     *
     * @param saleComplaintInfoEtlDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2021-07-22
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "SaleComplaintInfoEtlDTO", name = "saleComplaintInfoEtlDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public int insert(@RequestBody SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO) {
        return saleComplaintInfoEtlService.insert(saleComplaintInfoEtlDTO);
    }

    /**
     * 进行数据修改
     *
     * @param id                      需要修改数据的ID
     * @param saleComplaintInfoEtlDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2021-07-22
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
            @ApiImplicitParam(paramType = "body", dataType = "SaleComplaintInfoEtlDTO", name = "saleComplaintInfoEtlDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
    @PutMapping(value = "/{id}" )
    @ResponseStatus(HttpStatus.CREATED)
    public int update(@PathVariable("id" ) Long id, @RequestBody SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO) {
        return saleComplaintInfoEtlService.update(id, saleComplaintInfoEtlDTO);
    }

    /**
     * 根据id删除对象
     *
     * @param id 需要修改数据的ID
     * <AUTHOR>
     * @since 2021-07-22
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
    })
    @ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
    @DeleteMapping(value = "/{id}" )
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public boolean deleteById(@PathVariable("id" ) Long id) {
        saleComplaintInfoEtlService.deleteById(id);
        return true;
    }

    /**
     * 分页查询数据(店端)
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "SaleComplaintInfoEtlDTO", name = "saleComplaintInfoEtlDTO", value = "")
    })
    @ApiOperation(value = "分页查询数据(店端)", notes = "分页查询数据(店端)", httpMethod = "POST")
    @RequestMapping(value = "/exportSaleComplaintHistory", method = RequestMethod.POST)
    public void selectCusByDealAll(@RequestBody(required=false) SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO,
                                   HttpServletRequest request, HttpServletResponse response
    ) throws ParseException {
        //准备导出数据
        List<Map> groupDataList = saleComplaintInfoEtlService.exportSaleComplaintHistory(saleComplaintInfoEtlDTO);
        //构建Excel相关对象
        List<ExcelExportColumn> exportColumnList = new ArrayList<ExcelExportColumn>();
        exportColumnList.add(new ExcelExportColumn("call_time", "投诉日期"));
        exportColumnList.add(new ExcelExportColumn("complaint_id", "投诉ID"));
        exportColumnList.add(new ExcelExportColumn("work_order_nature", "工单性质"));
        exportColumnList.add(new ExcelExportColumn("category1", "投诉单类别一级层"));
        exportColumnList.add(new ExcelExportColumn("category2", "投诉单类别二级层"));
        exportColumnList.add(new ExcelExportColumn("category3", "投诉单类别三级层"));
        exportColumnList.add(new ExcelExportColumn("buy_dealer_code", "购买经销商代码"));
        exportColumnList.add(new ExcelExportColumn("buy_dealer_name", "购买经销商名称"));
        exportColumnList.add(new ExcelExportColumn("dealer_code", "处理经销商代码"));
        exportColumnList.add(new ExcelExportColumn("dealer_name", "处理经销商名称"));
        exportColumnList.add(new ExcelExportColumn("customer_type", "客户类型"));
        exportColumnList.add(new ExcelExportColumn("call_name", "来电人姓名"));
        exportColumnList.add(new ExcelExportColumn("call_tel", "来电号码"));
        exportColumnList.add(new ExcelExportColumn("reply_tel", "回复联系电话1"));
        exportColumnList.add(new ExcelExportColumn("reply_tel2", "回复联系电话2"));
        exportColumnList.add(new ExcelExportColumn("vin", "车架号"));
        exportColumnList.add(new ExcelExportColumn("phone", "车主手机"));
        exportColumnList.add(new ExcelExportColumn("model", "车型款别"));
        exportColumnList.add(new ExcelExportColumn("license_plate_num", "车牌号"));
        exportColumnList.add(new ExcelExportColumn("buy_time", "购车日期"));
        exportColumnList.add(new ExcelExportColumn("life_cycle", "生命周期"));
        exportColumnList.add(new ExcelExportColumn("mileage", "里程数"));
        exportColumnList.add(new ExcelExportColumn("problem", "问题及跟进详情"));
        exportColumnList.add(new ExcelExportColumn("work_order_status", "工单状态"));
        exportColumnList.add(new ExcelExportColumn("close_case_status", "结案状态"));
        exportColumnList.add(new ExcelExportColumn("dealer_fisrt_reply_time", "经销商首次回复时间"));
        exportColumnList.add(new ExcelExportColumn("close_case_time", "结案时间"));
        exportColumnList.add(new ExcelExportColumn("solve_time", "投诉解决时长"));
        exportColumnList.add(new ExcelExportColumn("reviewer", "审核人"));
        exportColumnList.add(new ExcelExportColumn("created_by", "创建人"));
        exportColumnList.add(new ExcelExportColumn("activity_source", "活动来源"));
        exportColumnList.add(new ExcelExportColumn("revisit_demand", "回访需求"));
        exportColumnList.add(new ExcelExportColumn("email_status", "邮件状态"));
        exportColumnList.add(new ExcelExportColumn("is_48H_send_email", "经销商是否在48H内推出邮件"));
        exportColumnList.add(new ExcelExportColumn("type", "投诉类型"));
        exportColumnList.add(new ExcelExportColumn("again_call_time", "再次投诉时间"));
        exportColumnList.add(new ExcelExportColumn("SMS", "短信"));
        exportColumnList.add(new ExcelExportColumn("effective_complaint", "有效投诉"));
        exportColumnList.add(new ExcelExportColumn("source", "投诉来源"));
        exportColumnList.add(new ExcelExportColumn("no_close_reasons_classification", "未结案原因分类"));
        exportColumnList.add(new ExcelExportColumn("last_follow_time", "最后跟进时间"));
        exportColumnList.add(new ExcelExportColumn("last_complaint_time", "最后投诉日期"));
        exportColumnList.add(new ExcelExportColumn("complaint_number", "总投诉次数"));
        exportColumnList.add(new ExcelExportColumn("responsibility_determine", "责任判定"));
        exportColumnList.add(new ExcelExportColumn("subject", "投诉主题"));
        exportColumnList.add(new ExcelExportColumn("classification1", "分类一"));
        exportColumnList.add(new ExcelExportColumn("classification2", "分类二"));
        exportColumnList.add(new ExcelExportColumn("classification3", "分类三"));
        exportColumnList.add(new ExcelExportColumn("classification4", "分类四"));
        exportColumnList.add(new ExcelExportColumn("classification5", "分类五"));
        exportColumnList.add(new ExcelExportColumn("classification6", "分类六"));
        exportColumnList.add(new ExcelExportColumn("information_source", "信息来源"));
        exportColumnList.add(new ExcelExportColumn("keyword", "关键字"));
        exportColumnList.add(new ExcelExportColumn("consultation_and_solution", "咨询解决情况"));
        exportColumnList.add(new ExcelExportColumn("solution", "解决方法"));
        exportColumnList.add(new ExcelExportColumn("is_FAQ", "FAQ支持"));
        exportColumnList.add(new ExcelExportColumn("emotion_index_creation", "情绪指数新建"));
        exportColumnList.add(new ExcelExportColumn("sentiment_index_current", "情绪指数当前"));
        exportColumnList.add(new ExcelExportColumn("ccm_deal", "CCM系统协助处理"));
        exportColumnList.add(new ExcelExportColumn("first_revisit_time", "首次回访需求时间"));
        exportColumnList.add(new ExcelExportColumn("first_return_time", "首次回访时间"));
        exportColumnList.add(new ExcelExportColumn("restart_time", "重启时间"));
        exportColumnList.add(new ExcelExportColumn("restart_number", "重启次数"));
        exportColumnList.add(new ExcelExportColumn("second_restart_time", "第二次重启时间"));
        exportColumnList.add(new ExcelExportColumn("seconde_close_case_time", "第二次结案时间"));
        exportColumnList.add(new ExcelExportColumn("case_level", "案件等级"));
        exportColumnList.add(new ExcelExportColumn("is_24H_reply", "是否24H回复"));
        exportColumnList.add(new ExcelExportColumn("is_48H_revisit", "是否48H回访"));
        exportColumnList.add(new ExcelExportColumn("is_5D_close_case", "是否5日结案"));
        exportColumnList.add(new ExcelExportColumn("regional_feedback", "区域反馈回访"));
        exportColumnList.add(new ExcelExportColumn("regional_feedback_time", "区域反馈回访日"));
        exportColumnList.add(new ExcelExportColumn("no_revisit_reason", "不回访理由"));
        exportColumnList.add(new ExcelExportColumn("no_close_reasons", "未结案原因"));
        exportColumnList.add(new ExcelExportColumn("service_commitment", "服务承诺"));
        Map<String, List<Map>> excelData = new HashMap<String, List<Map>>(16);
        excelData.put("老ccm系统销售历史数据", groupDataList);
        Map<String, List<ExcelExportColumn>> ColumnMap = new HashMap<String, List<ExcelExportColumn>>(16);
        ColumnMap.put("老ccm系统销售历史数据", exportColumnList);
        excelGenerator.generateExcelSheet(excelData, ColumnMap, "老ccm系统销售历史数据.xls", request, response);

    }


}