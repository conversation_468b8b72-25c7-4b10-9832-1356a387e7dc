package com.yonyou.dmscus.customer.controller.saSllocateDlr;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.redis.RedisClient;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.dto.SaSllocateDlrParamsDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.UserInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.saSllocateDlr.SaSllocateDlrDto;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.inviteSaAllocateRule.InviteSaAllocateRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @since 2020-04-24
 */
@Api(value = "/saSllocateDlr", tags = {"SaSllocateDlrController"})
@RestController
@RequestMapping("/saSllocateDlr")
public class SaSllocateDlrController {

    @Autowired
    InviteVehicleRecordService inviteVehicleRecordService;
    @Autowired
    InviteSaAllocateRuleService inviteSaAllocateRuleService;
    @Autowired
    RedisClient redisClient;

    /**
     * 分页查询数据
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-24
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "SaSllocateDlrParamsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "POST")
    @PostMapping("/list")
    public IPage<InviteVehicleRecordDTO> getInviteVehicleRecord(@RequestBody  SaSllocateDlrParamsDTO dto) {
        Page<InviteVehicleRecordPO> page = new Page(dto.getCurrentPage(), dto.getPageSize());
        InviteVehicleRecordDTO inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        inviteVehicleRecordDTO.setInviteTypeParam(dto.getInviteType());
        inviteVehicleRecordDTO.setPlanFollowDateStart(dto.getPlanFollowDateStart());
        inviteVehicleRecordDTO.setPlanFollowDateEnd(dto.getPlanFollowDateEnd());
        inviteVehicleRecordDTO.setLicensePlateNum(dto.getLicensePlateNum());
        inviteVehicleRecordDTO.setVin(dto.getVin());
        inviteVehicleRecordDTO.setActualFollowDateStart(dto.getActualFollowDateStart());
        inviteVehicleRecordDTO.setActualFollowDateEnd(dto.getActualFollowDateEnd());
        inviteVehicleRecordDTO.setName(dto.getName());
        inviteVehicleRecordDTO.setIsBook(dto.getIsBook());
        inviteVehicleRecordDTO.setAdviseInDateStart(dto.getAdviseInDateStart());
        inviteVehicleRecordDTO.setAdviseInDateEnd(dto.getAdviseInDateEnd());
        inviteVehicleRecordDTO.setFollowStatusParam(dto.getFollowStatus());
        inviteVehicleRecordDTO.setSaName(dto.getSaName());
        inviteVehicleRecordDTO.setCreatedAtStart(dto.getCreatedAtStart());
        inviteVehicleRecordDTO.setCreatedAtEnd(dto.getCreatedAtEnd());
        inviteVehicleRecordDTO.setIsNoDistribute(dto.getIsNoDistribute());
        inviteVehicleRecordDTO.setIsWaitDistribute(dto.getIsWaitDistribute());
        inviteVehicleRecordDTO.setLeaveIds(dto.getLeaveIds());
        inviteVehicleRecordDTO.setOrderStatusParam(dto.getOrderStatusParam());
        inviteVehicleRecordDTO.setSaId(dto.getSaId());
        inviteVehicleRecordDTO.setReturnIntentionLevel(dto.getReturnIntentionLevel());
        return inviteVehicleRecordService.getInviteVehicleRecordForSaSllocate(page, inviteVehicleRecordDTO);
    }

    /**
     * 查询用户信息
     * @return
     */
    @ApiOperation(value = "查询用户信息", notes = "查询用户信息", httpMethod = "GET")
    @GetMapping("/getUserInfo")
    public List<UserInfoDTO> getUserInfo(){
        return inviteSaAllocateRuleService.getUserInfo();
    }

    /**
     * 保存邀约线索分配
     * @param saSllocateDlrDto
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "SaSllocateDlrDto", name = "saSllocateDlrDto", value = "", required = true)
    })
    @ApiOperation(value = "保存邀约线索分配", notes = "保存邀约线索分配", httpMethod = "POST")
    @PostMapping("/saveSaSllocate")
    public int saveSaSllocate(@RequestBody SaSllocateDlrDto saSllocateDlrDto) {
        if (Objects.nonNull(redisClient.get("createInviteByTask"))){
            throw new ServiceBizException("当前自动分配定时任务还未完成，暂不能手动分配。");
        }
        return inviteVehicleRecordService.saveSaSllocate(saSllocateDlrDto);
    }



}