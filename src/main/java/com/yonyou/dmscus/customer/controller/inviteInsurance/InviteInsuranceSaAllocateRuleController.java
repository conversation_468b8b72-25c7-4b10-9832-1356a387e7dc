package com.yonyou.dmscus.customer.controller.inviteInsurance;

import com.yonyou.dmscus.customer.dto.InvitationRuleVcdcParamsVo;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceSaAllocateRuleDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceSaAllocateRuleDetailDTO;
import com.yonyou.dmscus.customer.service.inviteInsurance.InviteInsuranceSaAllocateRuleDetailService;
import com.yonyou.dmscus.customer.service.inviteInsurance.InviteInsuranceSaAllocateRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-11-11
 */
@Api(value = "/inviteInsuranceSaAllocateRule", tags = {"InviteInsuranceSaAllocateRuleController"})
@RestController
@RequestMapping("/inviteInsuranceSaAllocateRule")
public class InviteInsuranceSaAllocateRuleController {

    @Autowired
    InviteInsuranceSaAllocateRuleService inviteInsuranceSaAllocateRuleService;
    @Autowired
    InviteInsuranceSaAllocateRuleDetailService inviteInsuranceSaAllocateRuleDetailService;

    /**
     * 查询数据 -- 厂端
     * @param vo
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InvitationRuleVcdcParamsVo", name = "vo", value = "", required = true)
    })
    @ApiOperation(value = "查询数据 -- 厂端", notes = "查询数据 -- 厂端", httpMethod = "POST")
    @PostMapping("/getInviteInsuranceSaAllocateRule")
    public List<InviteInsuranceSaAllocateRuleDTO> getInviteInsuranceSaAllocateRule(@RequestBody InvitationRuleVcdcParamsVo vo) {
        return inviteInsuranceSaAllocateRuleService.getInviteInsuranceSaAllocateRule(vo);
    }


    /**
     * 查询数据 -- 店端
     * @return
     */
    @ApiOperation(value = "查询数据 -- 店端", notes = "查询数据 -- 店端", httpMethod = "GET")
    @GetMapping("/getInviteInsuranceSaAllocateRuleDlr")
    public InviteInsuranceSaAllocateRuleDTO getInviteInsuranceSaAllocateRuleDlr() {
        return inviteInsuranceSaAllocateRuleService.getInviteInsuranceSaAllocateRuleDlr();
    }



    /**
     * 保存分配规则
     * @param inviteInsuranceSaAllocateRuleDTO
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteInsuranceSaAllocateRuleDTO", name = "inviteInsuranceSaAllocateRuleDTO", value = "", required = true)
    })
    @ApiOperation(value = "保存分配规则", notes = "保存分配规则", httpMethod = "POST")
    @PostMapping("/saveInviteInsuranceSaAllocateRule")
    @ResponseStatus(HttpStatus.CREATED)
    public int saveInviteInsuranceSaAllocateRule(@RequestBody InviteInsuranceSaAllocateRuleDTO inviteInsuranceSaAllocateRuleDTO) {
        return inviteInsuranceSaAllocateRuleService.saveInviteInsuranceSaAllocateRule(inviteInsuranceSaAllocateRuleDTO);
    }

    /**
     * 保存分配规则人员明细
     * @param list
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<InviteInsuranceSaAllocateRuleDetailDTO>", name = "list", value = "", required = true)
    })
    @ApiOperation(value = "保存分配规则人员明细", notes = "保存分配规则人员明细", httpMethod = "POST")
    @PostMapping("/saveInsuranceSaAllocateRuleDetail")
    public int saveInsuranceSaAllocateRuleDetail(@RequestBody List<InviteInsuranceSaAllocateRuleDetailDTO> list) {
        return inviteInsuranceSaAllocateRuleDetailService.saveInsuranceSaAllocateRuleDetail(list);
    }

    /**
     *查询分配规则人员明细
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "")
    })
    @ApiOperation(value = "查询分配规则人员明细", notes = "查询分配规则人员明细", httpMethod = "GET")
    @GetMapping("/getInsuranceSaAllocateRuleDetail")
    public List<InviteInsuranceSaAllocateRuleDetailDTO> getInsuranceSaAllocateRuleDetail(@RequestParam(value = "dealerCode", required =
            false) String dealerCode){
        return inviteInsuranceSaAllocateRuleDetailService.getInsuranceSaAllocateRuleDetail(dealerCode);
    }

    /**
     *批量查询分配规则人员明细
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCodeList", value = "")
    })
    @ApiOperation(value = "批量查询分配规则人员明细", notes = "批量查询分配规则人员明细", httpMethod = "GET")
    @GetMapping("/getInsuranceSaAllocateRuleDetailList")
    public List<InviteInsuranceSaAllocateRuleDetailDTO> getInsuranceSaAllocateRuleDetailList(@RequestParam(value = "dealerCodeList", required =
            false) String dealerCodeList){
        return inviteInsuranceSaAllocateRuleDetailService.getInsuranceSaAllocateRuleDetailList(dealerCodeList);
    }
}