package com.yonyou.dmscus.customer.controller.complaint.sale;

import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintTypeDTO;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintTypeService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-12-10
 */
@Api(value = "/saleComplaintType", tags = {"SaleComplaintTypeController"})
@RestController
@RequestMapping("/saleComplaintType")
public class SaleComplaintTypeController extends BaseController {

    @Autowired
    SaleComplaintTypeService saleComplaintTypeService;

    /**
     * 销售投诉类型查询
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "workOrderNature", value = "")
    })
    @ApiOperation(value = "销售投诉类型查询", notes = "销售投诉类型查询", httpMethod = "GET")
    @RequestMapping(value = "/selectSaleComType", method = RequestMethod.GET)
    public List<SaleComplaintTypeDTO> selectCusById(@RequestParam(value="workOrderNature",required = false) Integer workOrderNature
    )  {
        SaleComplaintTypeDTO saleComplaintTypeDTO=new SaleComplaintTypeDTO();
        if(!StringUtils.isNullOrEmpty(workOrderNature)&&600==workOrderNature){
            saleComplaintTypeDTO.setId(9L);
           return saleComplaintTypeService.selectList(saleComplaintTypeDTO);
        }else if (!StringUtils.isNullOrEmpty(workOrderNature)&&600!=workOrderNature){
            saleComplaintTypeDTO.setId(9L);
            return saleComplaintTypeService.selectListNotFw(saleComplaintTypeDTO);
        }else {
            return saleComplaintTypeService.selectListBySql(saleComplaintTypeDTO);
        }
    }



}