package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFinalPartInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyFinalPartInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyFinalPartInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;






                                                                                    /**
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
@Api(value = "/goodwillApplyFinalPartInfo", tags = {"GoodwillApplyFinalPartInfoController"})
@RestController
@RequestMapping("/goodwillApplyFinalPartInfo")
                public class GoodwillApplyFinalPartInfoController {
    
        @Autowired
        GoodwillApplyFinalPartInfoService goodwillApplyFinalPartInfoService;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码
        * @param ownerParCode 所有者的父组织代码
        * @param orgId 组织ID
        * @param id 主键id
        * @param goodwillApplyId 亲善单id
        * @param goodwillType 亲善类型
        * @param partCode 零件代码
        * @param partName 零件名称
        * @param unitPriceNoTax 单价（不含税）
        * @param rate 税率
        * @param unitPriceTax 单价（含税）
        * @param quantity 数量
        * @param amountTax 总价（含税）
        * @param supportProportion 支持比例
        * @param goodwillAmountTax 亲善金额（含税）
        * @param isValid 是否有效
        * @param isDeleted 是否删除:1,删除；0,未删除
        * @param createdAt 创建时间
        * @param updatedAt 修改时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @throws ParseException 
         * @since 2020-05-11
        */
                @ApiImplicitParams({
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键id"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = "亲善单id"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillType", value = "亲善类型"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "partCode", value = "零件代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "partName", value = "零件名称"),
                        @ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "unitPriceNoTax", value = "单价（不含税）"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "rate", value = "税率"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "unitPriceTax", value = "单价（含税）"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "quantity", value = "数量"),
                        @ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "amountTax", value = "总价（含税）"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "supportProportion", value = "支持比例"),
                        @ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "goodwillAmountTax", value = "亲善金额（含税）"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
                        @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除:1,删除；0,未删除"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = "创建时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = "修改时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
                })
                @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
                @GetMapping
        public IPage<GoodwillApplyFinalPartInfoDTO>getByPage(
		@RequestParam(value="appId",required = false) String appId,
		@RequestParam(value="ownerCode",required = false) String ownerCode,
		@RequestParam(value="ownerParCode",required = false) String ownerParCode,
		@RequestParam(value="orgId",required = false) Integer orgId,
		@RequestParam(value="id",required = false) Long id,
		@RequestParam(value="goodwillApplyId",required = false) Long goodwillApplyId,
		@RequestParam(value="goodwillType",required = false) Integer goodwillType,
		@RequestParam(value="partCode",required = false) String partCode,
		@RequestParam(value="partName",required = false) String partName,
		@RequestParam(value="unitPriceNoTax",required = false) BigDecimal unitPriceNoTax,
		@RequestParam(value="rate",required = false) String rate,
		@RequestParam(value="unitPriceTax",required = false) String unitPriceTax,
		@RequestParam(value="quantity",required = false) String quantity,
		@RequestParam(value="amountTax",required = false) BigDecimal amountTax,
		@RequestParam(value="supportProportion",required = false) String supportProportion,
		@RequestParam(value="goodwillAmountTax",required = false) BigDecimal goodwillAmountTax,
		@RequestParam(value="isValid",required = false) Integer isValid,
		@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
		@RequestParam(value="createdAt",required = false) String createdAt,
		@RequestParam(value="updatedAt",required = false) String updatedAt,
        @RequestParam("currentPage")int currentPage,
        @RequestParam("pageSize")int pageSize) throws ParseException{
                Page<GoodwillApplyFinalPartInfoPO>page=new Page(currentPage,pageSize);
        		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        		GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO =new GoodwillApplyFinalPartInfoDTO();
                goodwillApplyFinalPartInfoDTO.setAppId(appId);
                goodwillApplyFinalPartInfoDTO.setOwnerCode(ownerCode);
                goodwillApplyFinalPartInfoDTO.setOwnerParCode(ownerParCode);
                goodwillApplyFinalPartInfoDTO.setOrgId(orgId);
                goodwillApplyFinalPartInfoDTO.setId(id);
                goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(goodwillApplyId);
                goodwillApplyFinalPartInfoDTO.setGoodwillType(goodwillType);
                goodwillApplyFinalPartInfoDTO.setPartCode(partCode);
                goodwillApplyFinalPartInfoDTO.setPartName(partName);
                goodwillApplyFinalPartInfoDTO.setUnitPriceNoTax(unitPriceNoTax);
                goodwillApplyFinalPartInfoDTO.setRate(rate);
                goodwillApplyFinalPartInfoDTO.setUnitPriceTax(unitPriceTax);
                goodwillApplyFinalPartInfoDTO.setQuantity(quantity);
                goodwillApplyFinalPartInfoDTO.setAmountTax(amountTax);
                goodwillApplyFinalPartInfoDTO.setSupportProportion(supportProportion);
                goodwillApplyFinalPartInfoDTO.setGoodwillAmountTax(goodwillAmountTax);
                goodwillApplyFinalPartInfoDTO.setIsValid(isValid);
                goodwillApplyFinalPartInfoDTO.setIsDeleted(isDeleted);
                if(createdAt != null){
                	goodwillApplyFinalPartInfoDTO.setCreatedAt(sdf.parse(createdAt));
                }
                if(updatedAt != null){
                	goodwillApplyFinalPartInfoDTO.setUpdatedAt(sdf.parse(updatedAt));
                }
                return goodwillApplyFinalPartInfoService.selectPageBysql(page,goodwillApplyFinalPartInfoDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFinalPartInfoDTO
 * <AUTHOR>
 * @since 2020-05-11
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public GoodwillApplyFinalPartInfoDTO getById(@PathVariable("id") Long id){
        return goodwillApplyFinalPartInfoService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param goodwillApplyFinalPartInfoDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-05-11
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyFinalPartInfoDTO", name = "goodwillApplyFinalPartInfoDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO){
        return goodwillApplyFinalPartInfoService.insert( goodwillApplyFinalPartInfoDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param goodwillApplyFinalPartInfoDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-05-11
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyFinalPartInfoDTO", name = "goodwillApplyFinalPartInfoDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO){
        return goodwillApplyFinalPartInfoService.update(id,goodwillApplyFinalPartInfoDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-05-11
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    goodwillApplyFinalPartInfoService.deleteById(id);
        return true;
        }

        /**
         * 根据IDs批量删除对象
         *
         * @param ids 需要修改数据的ID集合
         * <AUTHOR>
         * @since 2020-05-11
         */
                        @ApiImplicitParams({
                                @ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "需要修改数据的ID集合", required = true)
                        })
                        @ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
                        @DeleteMapping(value = "/batch/{ids}")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        public boolean deleteByIds(@PathVariable("ids") String ids){
            goodwillApplyFinalPartInfoService.deleteBatchIds(ids);
                return true;
                }

}