package com.yonyou.dmscus.customer.controller.complaint;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.CcmFollowInfoDto;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.AppPushDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintFollowPO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintFollowService;
import com.yonyou.dmscus.customer.service.impl.CommonServiceImpl;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Api(value = "/complaintFollow", tags = {"ComplaintFollowController"})
@RestController
@RequestMapping("/complaintFollow")
                public class ComplaintFollowController extends BaseController {
    
        @Autowired
        ComplaintFollowService complaintFollowService;
    @Autowired
    CommonServiceImpl commonService;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码 
        * @param ownerParCode 所有者的父组织代码 
        * @param orgId 组织ID
        * @param id 主键ID
        * @param complaintInfoId 投诉信息表主键ID
        * @param object 跟进对象 区域经理、经销商、CCM、客服中心
        * @param followTime 填写时间
        * @param follower 填写人
        * @param followContent 跟进内容
        * @param ccmNotPublish CCM跟进内容是否全网发布 1 公开 0 不公开
        * @param ccmSubject CCM主题
        * @param status 最新状态
        * @param advise 防止再发建议
        * @param ccmPart CCM部位 多选用逗号分隔
        * @param ccmSubdivisionPart CCM细分部位 多选用逗号分隔
        * @param ccMainReason CCM主要原因 多选用逗号分隔
        * @param ccResult CC解决结果 多选用逗号分隔
        * @param keyword 关键字
        * @param classification1 分类1
        * @param classification2 分类2
        * @param classification3 分类3
        * @param classification4 分类4
        * @param classification5 分类5
        * @param classification6 分类6
        * @param planFollowTime 下次跟进时间
        * @param actuallFollowTime2 实际跟进时间
        * @param dataSources 数据来源 
        * @param isDeleted 是否删除
        * @param isValid 是否有效 
        * @param createdAt 创建时间
        * @param updatedAt 更新时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @since 2020-04-15
        */
                @ApiImplicitParams({
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "complaintInfoId", value = "投诉信息表主键ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "object", value = "跟进对象 区域经理、经销商、CCM、客服中心"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "followTime", value = "填写时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "follower", value = "填写人"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "followContent", value = "跟进内容"),
                        @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "ccmNotPublish", value = "CCM跟进内容是否全网发布 1 公开 0 不公开"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ccmSubject", value = "CCM主题"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "status", value = "最新状态"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "advise", value = "防止再发建议"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ccmPart", value = "CCM部位 多选用逗号分隔"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ccmSubdivisionPart", value = "CCM细分部位 多选用逗号分隔"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ccMainReason", value = "CCM主要原因 多选用逗号分隔"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ccResult", value = "CC解决结果 多选用逗号分隔"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "keyword", value = "关键字"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "classification1", value = "分类1"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "classification2", value = "分类2"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "classification3", value = "分类3"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "classification4", value = "分类4"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "classification5", value = "分类5"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "classification6", value = "分类6"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "planFollowTime", value = "下次跟进时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "actuallFollowTime2", value = "实际跟进时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = "数据来源"),
                        @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "createdAt", value = "创建时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "updatedAt", value = "更新时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
                })
                @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
                @GetMapping
        public IPage<ComplaintFollowDTO>getByPage(
@RequestParam(value="appId",required = false) String appId,
@RequestParam(value="ownerCode",required = false) String ownerCode,
@RequestParam(value="ownerParCode",required = false) String ownerParCode,
@RequestParam(value="orgId",required = false) Integer orgId,
@RequestParam(value="id",required = false) Long id,
@RequestParam(value="complaintInfoId",required = false) Long complaintInfoId,
@RequestParam(value="object",required = false) String object,
@RequestParam(value="followTime",required = false) Date followTime,
@RequestParam(value="follower",required = false) String follower,
@RequestParam(value="followContent",required = false) String followContent,
@RequestParam(value="ccmNotPublish",required = false) Boolean ccmNotPublish,
@RequestParam(value="ccmSubject",required = false) String ccmSubject,
@RequestParam(value="status",required = false) String status,
@RequestParam(value="advise",required = false) Integer advise,
@RequestParam(value="ccmPart",required = false) String ccmPart,
@RequestParam(value="ccmSubdivisionPart",required = false) String ccmSubdivisionPart,
@RequestParam(value="ccMainReason",required = false) String ccMainReason,
@RequestParam(value="ccResult",required = false) String ccResult,
@RequestParam(value="keyword",required = false) String keyword,
@RequestParam(value="classification1",required = false) Integer classification1,
@RequestParam(value="classification2",required = false) String classification2,
@RequestParam(value="classification3",required = false) String classification3,
@RequestParam(value="classification4",required = false) String classification4,
@RequestParam(value="classification5",required = false) String classification5,
@RequestParam(value="classification6",required = false) String classification6,
@RequestParam(value="planFollowTime",required = false) Date planFollowTime,
@RequestParam(value="actuallFollowTime2",required = false) Date actuallFollowTime2,
@RequestParam(value="dataSources",required = false) Integer dataSources,
@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
@RequestParam(value="isValid",required = false) Integer isValid,
@RequestParam(value="createdAt",required = false) Date createdAt,
@RequestParam(value="updatedAt",required = false) Date updatedAt,
        @RequestParam("currentPage")int currentPage,
        @RequestParam("pageSize")int pageSize){
        Page<ComplaintFollowPO>page=new Page(currentPage,pageSize);

        ComplaintFollowDTO complaintFollowDTO =new ComplaintFollowDTO();
                                            complaintFollowDTO.setAppId(appId);
                                            complaintFollowDTO.setOwnerCode(ownerCode);
                                            complaintFollowDTO.setOwnerParCode(ownerParCode);
                                            complaintFollowDTO.setOrgId(orgId);
                                            complaintFollowDTO.setId(id);
                                            complaintFollowDTO.setComplaintInfoId(complaintInfoId);
                                            complaintFollowDTO.setObject(object);
                                            complaintFollowDTO.setFollowTime(followTime);
                                            complaintFollowDTO.setFollower(follower);
                                            complaintFollowDTO.setFollowContent(followContent);
                                            complaintFollowDTO.setIsCcmNotPublish(ccmNotPublish);
                                            complaintFollowDTO.setCcmSubject(ccmSubject);
                                            complaintFollowDTO.setStatus(status);
                                            complaintFollowDTO.setAdvise(advise);
                                            complaintFollowDTO.setCcmPart(ccmPart);
                                            complaintFollowDTO.setCcmSubdivisionPart(ccmSubdivisionPart);
                                            complaintFollowDTO.setCcMainReason(ccMainReason);
                                            complaintFollowDTO.setCcResult(ccResult);
                                            complaintFollowDTO.setKeyword(keyword);
                                            complaintFollowDTO.setClassification1(classification1);
                                            complaintFollowDTO.setClassification2(classification2);
                                            complaintFollowDTO.setClassification3(classification3);
                                            complaintFollowDTO.setClassification4(classification4);
                                            complaintFollowDTO.setClassification5(classification5);
                                            complaintFollowDTO.setClassification6(classification6);
                                            complaintFollowDTO.setPlanFollowTime(planFollowTime);
                                            complaintFollowDTO.setActuallFollowTime2(actuallFollowTime2);
                                            complaintFollowDTO.setDataSources(dataSources);
                                            complaintFollowDTO.setIsDeleted(isDeleted);
                                            complaintFollowDTO.setIsValid(isValid);
                                            complaintFollowDTO.setCreatedAt(createdAt);
                                            complaintFollowDTO.setUpdatedAt(updatedAt);
                return complaintFollowService.selectPageBysql(page,complaintFollowDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintFollowDTO
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public ComplaintFollowDTO getById(@PathVariable("id") Long id){
        return complaintFollowService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param complaintFollowDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintFollowDTO", name = "complaintFollowDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody ComplaintFollowDTO complaintFollowDTO){
        return complaintFollowService.insert( complaintFollowDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param complaintFollowDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintFollowDTO", name = "complaintFollowDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody ComplaintFollowDTO complaintFollowDTO){
        return complaintFollowService.update(id,complaintFollowDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    complaintFollowService.deleteById(id);
        return true;
        }
    /**
     * 查询跟进信息（店端使用）
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-14
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "查询跟进信息（店端使用）", notes = "查询跟进信息（店端使用）", httpMethod = "GET")
    @GetMapping(value = "/queryCus/{id}")
    public List<ComplaintFollowDTO> queryCus(@PathVariable("id") Long id){

        ComplaintFollowDTO complaintFollowDTO =new ComplaintFollowDTO();
        complaintFollowDTO.setComplaintInfoId(id);
        complaintFollowDTO.setIsCcmNotPublish(true);
        String flag="dealer";
        return complaintFollowService.selectListBySql(flag,complaintFollowDTO);
    }

    /**
     * 新增经销商跟进内容
     * *
     * @param ComplaintCustomFieldTestDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "ComplaintCustomFieldTestDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "新增经销商跟进内容 *", notes = "新增经销商跟进内容 *", httpMethod = "POST")
    @RequestMapping(value = "/insertCus", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertCus(@RequestBody ComplaintCustomFieldTestDTO ComplaintCustomFieldTestDTO) throws ParseException {
        return complaintFollowService.insertcCus(ComplaintCustomFieldTestDTO);
    }
    /**
     * 新增区域经理跟进内容
     * *
     * @param ComplaintCustomFieldTestDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "ComplaintCustomFieldTestDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "新增区域经理跟进内容 *", notes = "新增区域经理跟进内容 *", httpMethod = "POST")
    @RequestMapping(value = "/insertRegionCus", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertRegionCus(@RequestBody ComplaintCustomFieldTestDTO ComplaintCustomFieldTestDTO) throws ParseException {
        return complaintFollowService.insertRegionCus(ComplaintCustomFieldTestDTO);
    }
    /**
     * 协助部门跟进内容
     * *
     * @param ComplaintCustomFieldTestDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "ComplaintCustomFieldTestDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "协助部门跟进内容 *", notes = "协助部门跟进内容 *", httpMethod = "POST")
    @RequestMapping(value = "/insertAssistCus", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertAssistCus(@RequestBody ComplaintCustomFieldTestDTO ComplaintCustomFieldTestDTO)  {
        return complaintFollowService.insertAssistCus(ComplaintCustomFieldTestDTO);
    }
    /**
     * 协助经销商跟进内容
     * *
     * @param ComplaintCustomFieldTestDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "ComplaintCustomFieldTestDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "协助经销商跟进内容 *", notes = "协助经销商跟进内容 *", httpMethod = "POST")
    @RequestMapping(value = "/insertAssistDealer", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertAssistDealer(@RequestBody ComplaintCustomFieldTestDTO ComplaintCustomFieldTestDTO)  {
        return complaintFollowService.insertAssistDealer(ComplaintCustomFieldTestDTO);
    }
    /**
     * 新增CCM跟进内容
     * *
     * @param ccmFollowInfoDto 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "CcmFollowInfoDto", name = "ccmFollowInfoDto", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "新增CCM跟进内容 *", notes = "新增CCM跟进内容 *", httpMethod = "POST")
    @RequestMapping(value = "/insertCCMCus", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertCcmCuse(@RequestBody CcmFollowInfoDto ccmFollowInfoDto) throws ParseException {
        return complaintFollowService.insertCcmCus(ccmFollowInfoDto);
    }
    /**
     * 新增质量部跟进内容
     * *
     * @param ccmFollowInfoDto 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "CcmFollowInfoDto", name = "ccmFollowInfoDto", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "新增质量部跟进内容 *", notes = "新增质量部跟进内容 *", httpMethod = "POST")
    @RequestMapping(value = "/insertQuCus", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertQuCus(@RequestBody CcmFollowInfoDto ccmFollowInfoDto) throws ParseException {
        return complaintFollowService.insertQuCus(ccmFollowInfoDto);
    }
    /**
     * 新增经销商跟进内容(上报车厂)
     * *
     * @param ComplaintCustomFieldTestDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "ComplaintCustomFieldTestDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "新增经销商跟进内容(上报车厂) *", notes = "新增经销商跟进内容(上报车厂) *", httpMethod = "POST")
    @RequestMapping(value = "/reportVeh", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int reportVeh(@RequestBody ComplaintCustomFieldTestDTO ComplaintCustomFieldTestDTO) throws ParseException {
        return complaintFollowService.reportVeh(ComplaintCustomFieldTestDTO);
    }

    /**
     * 查询跟进信息（厂端使用）
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-14
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "查询跟进信息（厂端使用）", notes = "查询跟进信息（厂端使用）", httpMethod = "GET")
    @GetMapping(value = "/queryCus1/{id}")
    public List<ComplaintFollowDTO> queryCus1(@PathVariable("id") Long id){
        ComplaintFollowDTO complaintFollowDTO =new ComplaintFollowDTO();
        complaintFollowDTO.setComplaintInfoId(id);
        String flag="vcdc";
        return complaintFollowService.selectListBySql(flag,complaintFollowDTO);
    }
    /**
     * 查询跟进信息（厂端使用）
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-14
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "查询跟进信息（厂端使用）", notes = "查询跟进信息（厂端使用）", httpMethod = "GET")
    @GetMapping(value = "/queryNewCus/{id}")
    public List<ComplaintFollowDTO> queryNewCus(@PathVariable("id") Long id){
        ComplaintFollowDTO complaintFollowDTO =new ComplaintFollowDTO();
        complaintFollowDTO.setComplaintInfoId(id);
        return complaintFollowService.queryNewCus(complaintFollowDTO);
    }
    /**
     * 提醒下次跟进时间
     */
    @ApiOperation(value = "提醒下次跟进时间", notes = "提醒下次跟进时间", httpMethod = "GET")
    @GetMapping(value = "/remindNextFollowing")
    public void remindNextFollowing(){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<ComplaintInfMoreDTO> queryNextFollowing=complaintFollowService.queryNextFollowing();
        for(int i=0;i<queryNextFollowing.size();i++){

            AppPushDTO appPushDTO=new AppPushDTO();
            List<Integer> idList=new ArrayList<>();
            idList.add(Integer.valueOf(queryNextFollowing.get(i).getFollowName()));
            appPushDTO.setUserIds(idList);
            appPushDTO.setPriority(33081001L);
            appPushDTO.setTitle("售后客诉单跟进提醒");
            JSONObject json = new JSONObject();
            //向json中添加数据
            json.put("complaintNo",queryNextFollowing.get(i).getComplaintId());
            json.put("dealerCode",queryNextFollowing.get(i).getDealerCode());
            json.put("regionManager",queryNextFollowing.get(i).getRegionManager());
            json.put("subject",queryNextFollowing.get(i).getSubject());
            json.put("FollowTime",formatter.format(queryNextFollowing.get(i).getFollowTime()));
            //转换为字符串
            String jsonStr = json.toString();
            appPushDTO.setJson(jsonStr);
            appPushDTO.setContent("您有需要跟进的客诉单:"
                    +"客诉单号:"+queryNextFollowing.get(i).getComplaintId()+",处理经销商:"+queryNextFollowing.get(i).getDealerCode()
                    +",区域经理:"+queryNextFollowing.get(i).getRegionManager()+",主题:"+queryNextFollowing.get(i).getSubject()
                    +",跟进时间:"+ formatter.format(queryNextFollowing.get(i).getFollowTime())
                    +"!点击案件进行处理");
            commonService.messageSendApp(appPushDTO);
        }

    }




}