package com.yonyou.dmscus.customer.controller.inviteRuleDlr;

import com.yonyou.dmscus.customer.entity.dto.inviteRule.InviteRuleDTO;
import com.yonyou.dmscus.customer.service.inviteRule.InviteDuplicateRemovalRuleService;
import com.yonyou.dmscus.customer.service.inviteRule.InvitePartItemRuleService;
import com.yonyou.dmscus.customer.service.inviteRule.InviteRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-04-17
 */
@RestController
@RequestMapping("/inviteRuleDlr")
@Api(value = "店端邀约规则设置")
public class InviteRuleDlrController {

    @Autowired
    InviteRuleService inviteRuleService;
    @Autowired
    InviteDuplicateRemovalRuleService inviteDuplicateRemovalRuleService;
    @Autowired
    InvitePartItemRuleService invitePartItemRuleService;

    /**
     * 查询数据
     *
     * @param appId          系统ID
     * @param ownerCode      所有者代码
     * @param ownerParCode   所有者的父组织代码
     * @param orgId          组织ID
     * @param id             主键ID
     * @param dealerCode     经销商代码（VCDC：代表厂端，经销商代码：代表各自经销商）
     * @param inviteType     邀约类型：首保、定保、保险、客户流失
     * @param inviteRule     邀约规则
     * @param dayInAdvance   提前N天邀约
     * @param remindInterval 再提醒间隔（月）
     * @param isUse          是否启用：1、启用，2、不启用
     * @param dataSources    数据来源
     * @param isDeleted      是否删除
     * @param isValid        是否有效
     * @param createdAt      创建时间
     * @param updatedAt      更新时间
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-17
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "经销商代码（VCDC：代表厂端，经销商代码：代表各自经销商）"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "inviteType", value = "邀约类型：首保、定保、保险、客户流失"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "inviteRule", value = "邀约规则"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "dayInAdvance", value = "提前N天邀约"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "remindInterval", value = "再提醒间隔（月）"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isUse", value = "是否启用：1、启用，2、不启用"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = "数据来源"),
            @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "createdAt", value = "创建时间"),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "updatedAt", value = "更新时间")
    })
    @ApiOperation(value = "查询数据", notes = "查询数据", httpMethod = "GET")
    @GetMapping("/getInvitationRule")
    public List<InviteRuleDTO> getByList(
            @RequestParam(value = "appId", required = false) String appId,
            @RequestParam(value = "ownerCode", required = false) String ownerCode,
            @RequestParam(value = "ownerParCode", required = false) String ownerParCode,
            @RequestParam(value = "orgId", required = false) Integer orgId,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "dealerCode", required = false) String dealerCode,
            @RequestParam(value = "inviteType", required = false) Integer inviteType,
            @RequestParam(value = "inviteRule", required = false) Integer inviteRule,
            @RequestParam(value = "dayInAdvance", required = false) Integer dayInAdvance,
            @RequestParam(value = "remindInterval", required = false) Integer remindInterval,
            @RequestParam(value = "isUse", required = false) Integer isUse,
            @RequestParam(value = "dataSources", required = false) Integer dataSources,
            @RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
            @RequestParam(value = "isValid", required = false) Integer isValid,
            @RequestParam(value = "createdAt", required = false) Date createdAt,
            @RequestParam(value = "updatedAt", required = false) Date updatedAt) {

        InviteRuleDTO inviteRuleDTO = new InviteRuleDTO();
        inviteRuleDTO.setAppId(appId);
        inviteRuleDTO.setOwnerCode(ownerCode);
        inviteRuleDTO.setOwnerParCode(ownerParCode);
        inviteRuleDTO.setOrgId(orgId);
        inviteRuleDTO.setId(id);
        inviteRuleDTO.setInviteType(inviteType);
        inviteRuleDTO.setInviteRule(inviteRule);
        inviteRuleDTO.setDayInAdvance(dayInAdvance);
        inviteRuleDTO.setRemindInterval(remindInterval);
        inviteRuleDTO.setIsUse(isUse);
        inviteRuleDTO.setDataSources(dataSources);
        inviteRuleDTO.setIsDeleted(isDeleted);
        inviteRuleDTO.setIsValid(isValid);
        inviteRuleDTO.setCreatedAt(createdAt);
        inviteRuleDTO.setUpdatedAt(updatedAt);

        //查询
        return inviteRuleService.getInvitationRuleDlr(inviteRuleDTO);
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteRuleDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "保存邀约规则", notes = "", httpMethod = "POST")
    @PostMapping(value = "/saveInviteRule")
    public int saveInviteRule(@RequestBody InviteRuleDTO dto) {
        return inviteRuleService.saveInviteRuleDlr(dto);
    }



}