package com.yonyou.dmscus.customer.controller.faultLight;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.framework.RestResultResponse;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.dto.CustomerInfoDto;
import com.yonyou.dmscloud.framework.domain.framework.RestResultResponse;
import com.yonyou.dmscus.customer.dto.faultLight.FaultLightBookingRecordDto;
import com.yonyou.dmscus.customer.entity.dto.faultLight.*;
import com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.info.DiagnosisRecord;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.CallDetailsDTO;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.SaCustomerNumberDTO;
import com.yonyou.dmscus.customer.feign.dto.WarningDto;
import com.yonyou.dmscus.customer.service.faultLight.CluesDiagnosticInfoRelationService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightClueService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightDisposeService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightFollowService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.CompletableFuture;


/**
 * 故障灯
 */
@Api(value = "/faultLight", tags = {"故障灯"})
@Slf4j
@RestController
@RequestMapping("/faultLight")
public class FaultLightController {

    @Autowired
    FaultLightService faultLightService;
    @Autowired
    FaultLightDisposeService faultLightDisposeService;
    @Autowired
    FaultLightClueService faultLightClueService;
    @Autowired
    FaultLightFollowService faultLightFollowService;
    @Autowired
    private CluesDiagnosticInfoRelationService cluesDiagnosticInfoRelationService;

    /**
     * 分页查询--获取故障类别集合
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "FaultLightDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "分页查询--获取故障类别集合", notes = "分页查询--获取故障类别集合", httpMethod = "POST")
    @PostMapping("/typeList")
    public IPage<FaultLightDTO> getTypeList(@RequestBody FaultLightDTO dto) {

        Page<FaultLightDTO> page = new Page(dto.getCurrentPage(), dto.getPageSize());

        return faultLightDisposeService.selectPageTypeInfo(page, dto);
    }

    /**
     * 获取故障类别---对外暴露接口
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "FaultLightDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "获取故障类别---对外暴露接口", notes = "获取故障类别---对外暴露接口", httpMethod = "POST")
    @PostMapping("/typeInfo")
    public FaultLightDTO getTypeInfo(@RequestBody FaultLightDTO dto) {

        return faultLightDisposeService.getTypeInfo(dto);
    }

    /**
     * 获取故障类别---对外暴露接口
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<FaultLightDTO>", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "获取故障类别---对外暴露接口", notes = "获取故障类别---对外暴露接口", httpMethod = "POST")
    @PostMapping("/addTypeInfo")
    public int addTypeInfo(@RequestBody List<FaultLightDTO> dto) {

        return faultLightDisposeService.addTypeInfo(dto);
    }

    /**
     * 批量修改类别
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "BatchUpdateDTO", name = "batchUpdate", value = "", required = true)
    })
    @ApiOperation(value = "批量修改类别", notes = "批量修改类别", httpMethod = "POST")
    @PostMapping("/batchUpdate")
    public int batchUpdate(@RequestBody BatchUpdateDTO batchUpdate) {

        return faultLightDisposeService.batchUpdate(batchUpdate);
    }

    /**
     * 获取累计几次集合
     */
    @ApiOperation(value = "获取累计几次集合", notes = "获取累计几次集合", httpMethod = "GET")
    @GetMapping("/cumulativeNumber")
    public List<String> cumulativeNumber() {

        return faultLightDisposeService.cumulativeNumber();
    }

    /**
     * 线索,跟进状态联动下拉框
     */
    @ApiOperation(value = "线索,跟进状态联动下拉框", notes = "线索,跟进状态联动下拉框", httpMethod = "POST")
    @PostMapping("/queryClueFollowSpinner")
    public FaultLightStateDTO selectFaultLightState() {
        return faultLightClueService.selectFaultLightState();
    }

    /**
     * 大小区,城市下拉框
     */
    @ApiOperation(value = "大小区,城市下拉框", notes = "大小区,城市下拉框", httpMethod = "GET")
    @GetMapping("/queryCitySpinner")
    public List<CityDropdownDoxDTO> queryCityDropdownDox() {
        return faultLightClueService.queryCityDropdownDox();
    }

    /**
     * 线索同步接口
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClueDataSynchroDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "线索同步接口", notes = "线索同步接口", httpMethod = "POST")
    @PostMapping("/clueDataSynchro")
    public boolean doClueDataSynchro(@RequestBody ClueDataSynchroDTO dto) {
        return faultLightClueService.doClueDataSynchro(dto);
    }

    /**
     * 关联工单下拉框查询
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "关联工单下拉框查询", notes = "关联工单下拉框查询", httpMethod = "GET")
    @GetMapping("/selectRoNoSpinner")
    public List<String> selectRoNoSpinner(@RequestParam(value = "id") Long id) {
        return faultLightClueService.selectRoNoSpinner(id);
    }

    /**
     * 故障灯线索集合查询-店端
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClueInfoQueryRequestDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "故障灯线索集合查询-店端", notes = "故障灯线索集合查询-店端", httpMethod = "POST")
    @PostMapping("/queryClueInfoList")
    public IPage<ClueInfoQueryRespDTO> queryClueInfoList(@RequestBody ClueInfoQueryRequestDTO dto) {
        Page<ClueInfoQueryRespDTO> page = new Page(dto.getCurrentPage(), dto.getPageSize());
        return faultLightClueService.queryClueInfoList(page, dto);
    }

    /**
     * 故障灯线索数据下载-店端
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClueInfoQueryRequestDTO", name = "clueInfoQueryRequestDTO", value = "", required = true)
    })
    @ApiOperation(value = "故障灯线索数据下载-店端", notes = "故障灯线索数据下载-店端", httpMethod = "POST")
    @PostMapping("/clueInfoDerive")
    public void clueInfoDerive(@RequestBody ClueInfoQueryRequestDTO clueInfoQueryRequestDTO, HttpServletResponse response) {
        faultLightClueService.clueInfoDerive(clueInfoQueryRequestDTO, response);
    }

    /**
     * 故障灯线索集合查询-厂端
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClueInfoQueryRequestDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "故障灯线索集合查询-厂端", notes = "故障灯线索集合查询-厂端", httpMethod = "POST")
    @PostMapping("/factory/queryClueInfoList")
    public IPage<ClueInfoQueryRespDTO> factoryQueryClueInfoList(@RequestBody ClueInfoQueryRequestDTO dto) {
        Page<ClueInfoQueryRespDTO> page = new Page(dto.getCurrentPage(), dto.getPageSize());
        return faultLightClueService.factoryQueryClueInfoList(page, dto);
    }

    /**
     * 故障灯线索数据下载-厂端
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClueInfoQueryRequestDTO", name = "clueInfoQueryRequestDTO", value = "", required = true)
    })
    @ApiOperation(value = "故障灯线索数据下载-厂端", notes = "故障灯线索数据下载-厂端", httpMethod = "POST")
    @PostMapping("/factory/clueInfoDerive")
    public void factoryClueInfoDerive(@RequestBody ClueInfoQueryRequestDTO clueInfoQueryRequestDTO, HttpServletResponse response) {
        faultLightClueService.factoryClueInfoDerive(clueInfoQueryRequestDTO, response);
    }

    /**
     * 线索数据修改-有责无责
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClueInfoQueryRequestDTO", name = "clueInfoQueryRequestDTO", value = "", required = true)
    })
    @ApiOperation(value = "线索数据修改-有责无责", notes = "线索数据修改-有责无责", httpMethod = "POST")
    @PostMapping("/factory/updateClueInfo")
    public void updateClueInfo(@RequestBody ClueInfoQueryRequestDTO clueInfoQueryRequestDTO) {
        faultLightClueService.updateClueInfo(clueInfoQueryRequestDTO);
    }

    /**
     * 线索录入补偿
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClueDataSynchroDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "线索录入补偿", notes = "线索录入补偿", httpMethod = "POST")
    @PostMapping("/addClueCompensate")
    public int addClueCompensate(@RequestBody ClueDataSynchroDTO dto) {

        return faultLightClueService.addClueCompensate(dto);
    }

    /**
     * 跟进页面参数查询
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "跟进页面参数查询", notes = "跟进页面参数查询", httpMethod = "GET")
    @GetMapping("/queryFollowInfo")
    public FaultLightFollowDTO queryFaultLightFollow(@RequestParam(value = "id") Long id) {
        return faultLightFollowService.queryFaultLightFollow(id);
    }

    /**
     * 跟进状态下拉框
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "跟进状态下拉框", notes = "跟进状态下拉框", httpMethod = "GET")
    @GetMapping("/queryFollowSpinner")
    public List<FaultLightParamDTO> queryFollowSpinner(@RequestParam(value = "id") Long id) {
        return faultLightFollowService.queryFollowSpinner(id);
    }

    /**
     * 跟进故障灯线索提交
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "FaultLightFollowSubDTO", name = "subDTO", value = "", required = true)
    })
    @ApiOperation(value = "跟进故障灯线索提交", notes = "跟进故障灯线索提交", httpMethod = "POST")
    @PostMapping("/alterFollowStatus")
    public void doFollowSub(@RequestBody FaultLightFollowSubDTO subDTO) {
        faultLightFollowService.doFollowSub(subDTO);
    }

    /**
     * 线索跟进记录查询
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "FaultLightFollowRecordDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "线索跟进记录查询", notes = "线索跟进记录查询", httpMethod = "POST")
    @PostMapping("/queryFollowRecord")
    public List<FaultLightFollowRecordDTO> queryFollowRecord(@RequestBody FaultLightFollowRecordDTO dto) {

        return faultLightFollowService.queryFollowRecord(dto);
    }

    /**
     * 故障灯-保存呼叫登记
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "SaCustomerNumberDTO", name = "saCustomerNumberDTO", value = "", required = true)
    })
    @ApiOperation(value = "故障灯-保存呼叫登记", notes = "故障灯-保存呼叫登记", httpMethod = "POST")
    @PostMapping("/saveSaCustomerNumber")
    @ResponseStatus(HttpStatus.CREATED)
    public String saveSaCustomerNumber(@RequestBody SaCustomerNumberDTO saCustomerNumberDTO) {
        return faultLightService.saveSaCustomerNumber(saCustomerNumberDTO);
    }

    /**
     * 呼叫详情查询
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "TtFaultCallDetailsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "呼叫详情查询", notes = "呼叫详情查询", httpMethod = "POST")
    @PostMapping("/queryCallDetails")
    public List<TtFaultCallDetailsDTO> queryCallDetails(@RequestBody TtFaultCallDetailsDTO dto) {

        return faultLightService.queryCallDetails(dto);
    }

    /**
     * 关联故障灯工单
     */
    @ApiOperation(value = "关联故障灯工单", notes = "关联故障灯工单", httpMethod = "POST")
    @PostMapping("/faultLightOrderCorrelation")
    public void faultLightOrderCorrelation() {

        faultLightService.faultLightOrderCorrelation();
    }

    /**
     * 关联故障灯工单 4.0 400代客预约
     */
    @PostMapping("/faultLightOrderCorrelation/v4")
    public void faultLightOrderCorrelationV4() {
        CompletableFuture.runAsync(() -> faultLightService.faultLightOrderCorrelationV4());
        CompletableFuture.runAsync(() -> faultLightService.faultLightOrderCorrelationV5());
    }

    /**
     * 关联故障灯工单 4.0 400代客预约 预约单取消时关联线索关闭
     */
    @PostMapping("/updateForecastTime")
    public void updateForecastTime() {
        faultLightService.updateForecastTime();
    }

    /**
     * 关联故障灯工单 4.0 400代客预约 预约单取消时关联线索关闭
     */
    @PostMapping("/closeClueOnCancelledBookingOrder")
    public void closeClueOnCancelledBookingOrder() {
        faultLightService.closeClueOnCancelledBookingOrder();
    }

    /**
     * 故障灯-线索自动进入待验证
     */
    @ApiOperation(value = "故障灯-线索自动进入待验证", notes = "故障灯-线索自动进入待验证", httpMethod = "POST")
    @PostMapping("/faultLightStatusChange")
    public void faultLightStatusChange() {

        faultLightService.faultLightStatusChange();
    }

    /**
     * 故障灯-修复接口
     */
    @ApiOperation(value = "故障灯-修复接口", notes = "故障灯-修复接口", httpMethod = "POST")
    @PostMapping("/faultLightStatusRenovate")
    public void faultLightStatusRenovate() {
        faultLightService.faultLightStatusRenovate();
    }

    /**
     * 工单结算时触发提醒
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "工单结算时触发提醒", notes = "工单结算时触发提醒", httpMethod = "GET")
    @GetMapping("/submitSettlementRemind")
    public int submitSettlementRemind(@RequestParam(value = "dealerCode", required = true) String dealerCode,
                                       @RequestParam(value = "vin", required = true) String vin) {
        return faultLightService.submitSettlementRemind(dealerCode,vin);
    }

    /**
     * 经销商模糊查询接口
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "TtFaultLightClueDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "经销商模糊查询接口", notes = "经销商模糊查询接口", httpMethod = "POST")
    @PostMapping("/queryDealerList")
    public List<TtFaultLightClueDTO> queryDealerList(@RequestBody TtFaultLightClueDTO dto) {

        return faultLightService.queryDealerList(dto);
    }

    /**
     * 48H待验证线索
     */
    @ApiOperation(value = "48H待验证线索", notes = "48H待验证线索", httpMethod = "POST")
    @PostMapping("/doPendingVerificationClue")
    public void queryDealerList() {
        faultLightService.doPendingVerificationClue();
    }

    /**
     * 故障类别导入
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "file", value = "", required = true)
    })
    @ApiOperation(value = "故障类别导入", notes = "故障类别导入", httpMethod = "POST")
    @PostMapping("/importDispose")
    public void importDispose(@RequestPart("file") MultipartFile file) {
        faultLightDisposeService.importDispose(file);
    }

    /**
     * 故障灯线索状态更新（是否超时、高亮）
     */
    @ApiOperation(value = "故障灯线索状态更新（是否超时、高亮）", notes = "故障灯线索状态更新（是否超时、高亮）", httpMethod = "POST")
    @PostMapping("/updateHighlightFlag")
    public void updateHighlightFlag() {
        faultLightClueService.updateHighlightFlag();
    }

    /**
     *AI通话取消高亮
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "CallDetailsDTO", name = "callDetailsDTO", value = "", required = true)
    })
    @ApiOperation(value = "AI通话取消高亮", notes = "AI通话取消高亮", httpMethod = "POST")
    @PostMapping("/unHighlight/interAspect")
    public void unHighlight(@RequestBody CallDetailsDTO callDetailsDTO) {
        faultLightClueService.unHighlight(callDetailsDTO);
    }

    /**
     * 故障灯-获取高亮并插入同步任务表
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "HighLightDTO", name = "highLightDTO", value = "", required = true)
    })
    @ApiOperation(value = "故障灯-获取高亮并插入同步任务表", notes = "故障灯-获取高亮并插入同步任务表", httpMethod = "POST")
    @PostMapping("/queryHighlightFlag")
    public void queryHighlightFlag(@RequestBody HighLightDTO highLightDTO) {
        faultLightClueService.queryHighlightFlag(highLightDTO.getCreateDate(), highLightDTO.getEndDate());
    }

    /**
     * 故障灯线索状态更新（是否超时、高亮）
     */
    @ApiOperation(value = "故障灯线索状态更新（是否超时、高亮）", notes = "故障灯线索状态更新（是否超时、高亮）", httpMethod = "POST")
    @PostMapping("/updateHighlightFlagByClueDisTime")
    public void updateHighlightFlagByClueDisTime() {
        faultLightClueService.updateHighlightFlagByClueDisTime();
    }

    /**
     * 查询故障灯车主
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "", required = true)
    })
    @ApiOperation(value = "查询故障灯车主", notes = "查询故障灯车主", httpMethod = "GET")
    @GetMapping("/queryFaultLight")
    public CustomerInfoDto queryFaultLight(@RequestParam("vin") String vin, @RequestParam("ownerCode")String ownerCode) {
        return faultLightClueService.queryFaultLight(vin,ownerCode);
    }


    /**
     * 故障灯-保存呼叫登记
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "SaCustomerNumberDTO", name = "saCustomerNumberDTO", value = "", required = true)
    })
    @ApiOperation(value = "故障灯-保存呼叫登记", notes = "故障灯-保存呼叫登记", httpMethod = "POST")
    @PostMapping("/fullLeadSaveSaCustomerNumber")
    @ResponseStatus(HttpStatus.CREATED)
    public RestResultResponse fullLeadSaveSaCustomerNumber(@RequestBody SaCustomerNumberDTO saCustomerNumberDTO) {

        return new RestResultResponse<String>().data(faultLightService.saveSaCustomerNumber(saCustomerNumberDTO));
    }

    /**
     * 故障灯-DTC诊断信息展示接口
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "cluesId", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "cluesType", value = "", required = true)
    })
    @ApiOperation(value = "故障灯-DTC诊断信息展示接口", notes = "故障灯-DTC诊断信息展示接口", httpMethod = "GET")
    @GetMapping("/queryCluesDiagnosticInfo")
    public RestResultResponse queryCluesDiagnosticInfo(@RequestParam("cluesId") Long cluesId, @RequestParam("cluesType") String cluesType) {
        if (Objects.isNull(cluesId)) {
            throw new ServiceBizException("线索ID不能为空");
        }
        if (Objects.isNull(cluesType)) {
            throw new ServiceBizException("线索类型不能为空");
        }
        return new RestResultResponse<List<DiagnosisRecord>>().data(faultLightService.queryCluesDiagnosticInfo(cluesId, cluesType));
    }

    /**
     * 故障灯-DTC诊断信息展示接口
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "WarningDto", name = "warningDto", value = "", required = true)
    })
    @ApiOperation(value = "故障灯-DTC诊断信息展示接口", notes = "故障灯-DTC诊断信息展示接口", httpMethod = "POST")
    @PostMapping("/queryCluesWarningInfo/interf")
    public List<RecordsDTO> queryCluesWarningInfo(@RequestBody WarningDto warningDto) {
        return faultLightService.queryCluesWarningInfo(warningDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/markIsDisplayDiagnosticInfo")
    public void markIsDisplayDiagnosticInfo(@RequestParam("pageSize") int pageSize) {
        faultLightService.handleDimClues(pageSize);
    }
    /**
     * 故障灯消费crm线索状态
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "msg", value = "", required = true)
    })
    @ApiOperation(value = "故障灯消费crm线索状态", notes = "故障灯消费crm线索状态", httpMethod = "POST")
    @PostMapping("/faultLightConsumerStatus/interf")
    public void faultLightConsumerStatus(@RequestParam("msg") String msg) {
        faultLightClueService.faultLightConsumerStatus(msg);
    }

}
