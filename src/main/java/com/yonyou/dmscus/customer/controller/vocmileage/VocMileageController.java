
/** 
*Copyright 2020 Yonyou Corporation Ltd. All Rights Reserved.
* This software is published under the terms of the Yonyou Software
* License version 1.0, a copy of which has been included with this
* distribution in the LICENSE.txt file.
*
* @Project Name : dmscus.customer
*
* @File name : VocMileageController.java
*
* <AUTHOR> caizhonming
*
* @Date : 2020年7月8日
*
*/
	
package com.yonyou.dmscus.customer.controller.vocmileage;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.common.AjaxResponse;
import com.yonyou.dmscus.customer.configuration.ExcelParser;
import com.yonyou.dmscus.customer.entity.dto.vocmileage.VocMileageDTO;
import com.yonyou.dmscus.customer.service.vocmileage.VocMileageService;
import com.yonyou.dmscus.customer.util.common.BeanUtils;
import com.yonyou.dmscus.customer.util.common.ImportExcelUtil;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
* VOC里程管理控制类
* <AUTHOR>
* @date 2020年7月8日
*/
@Api(value = "/vocmileage", tags = {"VOC里程管理控制类"})
@RestController
@RequestMapping("/vocmileage")
public class VocMileageController extends BaseController{

    /** 日志对象 */
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    /** VOC里程服务注入*/
    @Autowired
    private VocMileageService  vocMileageService;
    
    @Autowired
    private ExcelParser excelParser;
    
    /**
     * 功能描述: <br>
     * 〈功能详细描述〉 VOC里程分页查询接口
     * 
     * @param queryVocMileageDTO
     * @param currentPage
     * @param pageSize
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "分页查询", notes = "功能描述: <br> 〈功能详细描述〉 VOC里程分页查询接口", httpMethod = "GET")
    @GetMapping("/pageinfo")
    public IPage<VocMileageDTO> selectPageBysql(VocMileageDTO queryVocMileageDTO,
            @RequestParam("currentPage") Long currentPage, @RequestParam("pageSize") Long pageSize) {
        // 构建分页对象
        Page page = new Page(currentPage, pageSize);
        return vocMileageService.queryVocMileagePageInfo(page, queryVocMileageDTO);
    }
    
    
    /**
    * VOC里程EXCEL导入
    * <AUTHOR>
    * @date 2020年7月8日
    * @param importFile
    * @return
    */
    	
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "", required = true)
    })
    @ApiOperation(value = "VOC里程EXCEL导入", notes = "VOC里程EXCEL导入", httpMethod = "POST")
    @PostMapping("/vocmileagedataimport")
    public AjaxResponse vocMileageDataImport(@RequestParam(value = "file") MultipartFile importFile) {
        AjaxResponse ajaxResponse = new AjaxResponse();
        StopWatch sw = new StopWatch();
        try {
            logger.info("=1=vocMileageDataImport 进入===");
            sw.start();
            // excel数据转换列表
            List<Map<Integer, String>> dataList = ImportExcelUtil.getImportExcelData(importFile.getInputStream(),
                    importFile.getOriginalFilename());
            if (CollectionUtils.isEmpty(dataList)) {
                ajaxResponse.setFail("5001", "导入的Excel数据不能为空");
                return ajaxResponse;
            }
            logger.info("=2=vocMileageDataImport 文件已经转成dataList===");
            // 调用导入数据接口
            ajaxResponse = vocMileageService.importExcelData(null,
                    null, dataList);
            
        } catch (Throwable e) {
            logger.error("call VocMileageController.importExcelData() method error msg:{}", e);
            ajaxResponse.setResult(AjaxResponse.FAILD);
            ajaxResponse.setMsg("文件解析失败!");
            ajaxResponse.addObject("msgDetail", BeanUtils.getExceptionMsg(e));
        }finally {
            sw.stop();
        }
        if(sw != null) {
            ajaxResponse.addObject("execTime", sw.getTotalTimeSeconds()+"秒"); 
        }
        return ajaxResponse;
    }
    
    
    /**
          * 测试
    * <AUTHOR>
    * @date 2020年7月15日
    * @param importFile
    * @return
    */
    	
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "", required = true)
    })
    @ApiOperation(value = "测试", notes = "测试", httpMethod = "POST")
    @PostMapping("/vocmileagedataimport2")
    public AjaxResponse vocMileageDataImportNow(@RequestParam(value = "file") MultipartFile importFile) {
        AjaxResponse ajaxResponse = new AjaxResponse();
        StopWatch sw = new StopWatch();
        try {
            logger.info("=1=ExcelParser.parse 开始解析===");
            sw.start();
            ExcelParser parse = excelParser.parse(importFile.getInputStream());
            List<String[]> datas = parse.getDatas();
            sw.stop();
            logger.info("=2=ExcelParser.parse 解析后进入===耗时:{}秒",sw.getTotalTimeSeconds());
            sw.start();
            if (CollectionUtils.isEmpty(datas)) {
                ajaxResponse.setFail("5001", "导入的Excel数据不能为空");
                return ajaxResponse;
            }
            // 调用导入数据接口
            ajaxResponse = vocMileageService.importExcelData2(datas);
        } catch (Throwable e) {
            logger.error("call VocMileageController.importExcelData() method error msg:{}", e);
            ajaxResponse.setResult(AjaxResponse.FAILD);
            ajaxResponse.setMsg("文件解析失败!");
            ajaxResponse.addObject("msgDetail", BeanUtils.getExceptionMsg(e));
        }finally {
            sw.stop();
        }
        if(sw != null) {
            ajaxResponse.addObject("execTime", sw.getTotalTimeSeconds()+"秒"); 
        }
        return ajaxResponse;
    }
    
    /**
     * 根据IDs批量删除对象
     *
     * @param ids 需要修改数据的ID集合
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "需要修改数据的ID集合", required = true)
    })
    @ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
    @DeleteMapping(value = "/deletevocmileage/batch/{ids}")
    public Integer deleteByIds(@PathVariable("ids") String ids) {
        int row = vocMileageService.deleteBatchIds(ids);
        return row;
    }
}
