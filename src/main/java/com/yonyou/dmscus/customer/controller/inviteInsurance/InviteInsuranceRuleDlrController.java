package com.yonyou.dmscus.customer.controller.inviteInsurance;

import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceRuleDTO;
import com.yonyou.dmscus.customer.service.inviteInsurance.InviteInsuranceDuplicateRemovalRuleService;
import com.yonyou.dmscus.customer.service.inviteInsurance.InviteInsuranceRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-11-11
 */
@RestController
@RequestMapping("/inviteInsuranceRuleDlr")
@Api(value = "店端邀约规则设置")
public class InviteInsuranceRuleDlrController {

    @Autowired
    InviteInsuranceRuleService inviteInsuranceRuleService;
    @Autowired
    InviteInsuranceDuplicateRemovalRuleService inviteInsuranceDuplicateRemovalRuleService;

    /**
     * 查询数据
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-17
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteInsuranceRuleDTO", name = "inviteInsuranceRuleDTO", value = "")
    })
    @ApiOperation(value = "查询数据", notes = "查询数据", httpMethod = "POST")
    @PostMapping("/getInviteInsuranceRule")
    public List<InviteInsuranceRuleDTO> getByList(@RequestBody(required = false) InviteInsuranceRuleDTO inviteInsuranceRuleDTO) {

        //查询
        return inviteInsuranceRuleService.getInviteInsuranceRuleDlr(inviteInsuranceRuleDTO);
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteInsuranceRuleDTO", name = "dto", value = "")
    })
    @ApiOperation(value = "保存邀约规则", notes = "", httpMethod = "POST")
    @PostMapping(value = "/saveInviteInsuranceRule")
    public int saveInviteRule(@RequestBody(required = false) InviteInsuranceRuleDTO dto) {
        return inviteInsuranceRuleService.saveInviteInsuranceRuleDlr(dto);
    }



}