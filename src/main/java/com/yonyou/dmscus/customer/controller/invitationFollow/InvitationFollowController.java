package com.yonyou.dmscus.customer.controller.invitationFollow;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.service.excel.ExcelDataType;
import com.yonyou.dmscloud.framework.service.excel.ExcelExportColumn;
import com.yonyou.dmscloud.framework.service.excel.ExcelGenerator;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.dto.BookingCreateParamsVo;
import com.yonyou.dmscus.customer.dto.BookingRecordDetailDTO;
import com.yonyou.dmscus.customer.dto.InvitationFollowParamsDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.RecommendationDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceCustomerInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.SaCustomerNumberDTO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.vo.InviteVehicleRecordVo;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.voicemanage.SaCustomerNumberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @since 2020-04-24
 */
@Api(value = "/invitationFollow", tags = {"InvitationFollowController"})
@RestController
@RequestMapping("/invitationFollow")
public class InvitationFollowController {

    @Autowired
    InviteVehicleRecordService inviteVehicleRecordService;
    @Autowired
    ExcelGenerator excelGenerator;
    @Autowired
    SaCustomerNumberService saCustomerNumberService;

    /**
     * 分页查询数据
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-24
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InvitationFollowParamsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "POST")
    @PostMapping("/list")
    public IPage<InviteVehicleRecordDTO> getInviteVehicleRecord(@RequestBody InvitationFollowParamsDTO dto) {
        Page<InviteVehicleRecordPO> page = new Page(dto.getCurrentPage(), dto.getPageSize());
        InviteVehicleRecordDTO inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        inviteVehicleRecordDTO.setInviteTypeParam(dto.getInviteType());
        inviteVehicleRecordDTO.setPlanFollowDateStart(dto.getPlanFollowDateStart());
        inviteVehicleRecordDTO.setPlanFollowDateEnd(dto.getPlanFollowDateEnd());
        inviteVehicleRecordDTO.setLicensePlateNum(dto.getLicensePlateNum());
        inviteVehicleRecordDTO.setVin(dto.getVin());
        inviteVehicleRecordDTO.setActualFollowDateStart(dto.getActualFollowDateStart());
        inviteVehicleRecordDTO.setActualFollowDateEnd(dto.getActualFollowDateEnd());
        inviteVehicleRecordDTO.setName(dto.getName());
        inviteVehicleRecordDTO.setIsBook(dto.getIsBook());
        inviteVehicleRecordDTO.setAdviseInDateStart(dto.getAdviseInDateStart());
        inviteVehicleRecordDTO.setAdviseInDateEnd(dto.getAdviseInDateEnd());
        inviteVehicleRecordDTO.setFollowStatusParam(dto.getFollowStatus());
        inviteVehicleRecordDTO.setOrderStatusParam(dto.getOrderStatusParam());
        inviteVehicleRecordDTO.setSaName(dto.getSaName());
        inviteVehicleRecordDTO.setCreatedAtStart(dto.getCreatedAtStart());
        inviteVehicleRecordDTO.setCreatedAtEnd(dto.getCreatedAtEnd());
        inviteVehicleRecordDTO.setIsself(dto.getIsself());
        inviteVehicleRecordDTO.setLeaveIds(dto.getLeaveIds());
        inviteVehicleRecordDTO.setSaId(dto.getSaId());
        inviteVehicleRecordDTO.setMonthTwice(dto.getMonthTwice());
        inviteVehicleRecordDTO.setRecordType(dto.getRecordType());
        inviteVehicleRecordDTO.setLossType(dto.getLossType());
        inviteVehicleRecordDTO.setCouponCode(dto.getCouponCode());
        inviteVehicleRecordDTO.setCouponName(dto.getCouponName());
        inviteVehicleRecordDTO.setRecordTypeParam(dto.getRecordTypeParam());
        inviteVehicleRecordDTO.setLossWarningType(dto.getLossWarningType());
        inviteVehicleRecordDTO.setReturnIntentionLevel(dto.getReturnIntentionLevel());
        inviteVehicleRecordDTO.setBevFlag(dto.getBevFlag());
        return inviteVehicleRecordService.getInviteVehicleRecord(page, inviteVehicleRecordDTO);
    }

    /**
     * 定时任务改变二次跟进状态
     * @return
     */
    @ApiOperation(value = "定时任务改变二次跟进状态", notes = "定时任务改变二次跟进状态", httpMethod = "GET")
    @GetMapping("/updateIsAi")
    public void updateIsAi(){
        inviteVehicleRecordService.updateIsAi();
    }

    /**
     * 查询未分配数量
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InvitationFollowParamsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "查询未分配数量", notes = "查询未分配数量", httpMethod = "POST")
    @PostMapping("/getNeedDistribute")
    public Integer getNeedDistribute(@RequestBody InvitationFollowParamsDTO dto){
        return inviteVehicleRecordService.getNeedDistribute(dto.getLeaveIds());
    }



    /**
     * 查询维修建议
     *
     * @param vin
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "查询维修建议", notes = "查询维修建议", httpMethod = "GET")
    @GetMapping("/getRecommendation")
    public List<RecommendationDTO> getRecommendation(@RequestParam(value = "vin", required = true) String vin) {
        return inviteVehicleRecordService.getRecommendation(vin);
    }

    /**
     * 查询邀约线索及子线索
     *
     * @param id
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "查询邀约线索及子线索", notes = "查询邀约线索及子线索", httpMethod = "GET")
    @GetMapping("/getInviteVehicleRecordInfo")
    public List<InviteVehicleRecordDTO> getInviteVehicleRecordInfo(
            @RequestParam(value = "vin", required = true) String vin,
            @RequestParam(value = "id", required = true) Long id) {
        return inviteVehicleRecordService.getInviteVehicleRecordInfo(vin, id);
    }



    /**
     * 查询易损件信息
     *
     * @param vin
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "查询易损件信息", notes = "查询易损件信息", httpMethod = "GET")
    @GetMapping("/getInviteVehicleVulInfo")
    public List<InviteVehicleRecordDTO> getInviteVehicleVulInfo(
            @RequestParam(value = "vin", required = true) String vin) {
        return inviteVehicleRecordService.getInviteVehicleVulInfo(vin);
    }

    /**
     * 保存跟进
     *
     * @param dto
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteVehicleRecordDetailDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "保存跟进", notes = "保存跟进", httpMethod = "POST")
    @PostMapping("/saveInviteVehicleRecord")
    public int saveInviteVehicleRecord(@RequestBody InviteVehicleRecordDetailDTO dto) {
        return inviteVehicleRecordService.saveInviteVehicleRecord(dto);
    }

    /**
     * 判断是否建议入厂月份前有没有AI
     *
     * @param dto
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteVehicleRecordDetailDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "判断是否建议入厂月份前有没有AI", notes = "判断是否建议入厂月份前有没有AI", httpMethod = "POST")
    @PostMapping("/checkAI")
    public int checkAI(@RequestBody InviteVehicleRecordDetailDTO dto) {
        return inviteVehicleRecordService.checkAI(dto);
    }


    /**
     * 导出
     * @param dto
     * @param request
     * @param response
     * @throws Exception
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InvitationFollowParamsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "导出", notes = "导出", httpMethod = "POST")
    @RequestMapping(value = "/exportExcel", method = RequestMethod.POST)
    @ResponseBody
    public void exportExcel(@RequestBody InvitationFollowParamsDTO dto,
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        InviteVehicleRecordDTO inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        inviteVehicleRecordDTO.setInviteTypeParam(dto.getInviteType());
        inviteVehicleRecordDTO.setPlanFollowDateStart(dto.getPlanFollowDateStart());
        inviteVehicleRecordDTO.setPlanFollowDateEnd(dto.getPlanFollowDateEnd());
        inviteVehicleRecordDTO.setLicensePlateNum(dto.getLicensePlateNum());
        inviteVehicleRecordDTO.setVin(dto.getVin());
        inviteVehicleRecordDTO.setActualFollowDateStart(dto.getActualFollowDateStart());
        inviteVehicleRecordDTO.setActualFollowDateEnd(dto.getActualFollowDateEnd());
        inviteVehicleRecordDTO.setName(dto.getName());
        inviteVehicleRecordDTO.setIsBook(dto.getIsBook());
        inviteVehicleRecordDTO.setAdviseInDateStart(dto.getAdviseInDateStart());
        inviteVehicleRecordDTO.setAdviseInDateEnd(dto.getAdviseInDateEnd());
        inviteVehicleRecordDTO.setFollowStatusParam(dto.getFollowStatus());
        inviteVehicleRecordDTO.setOrderStatusParam(dto.getOrderStatusParam());
        inviteVehicleRecordDTO.setSaName(dto.getSaName());
        inviteVehicleRecordDTO.setCreatedAtStart(dto.getCreatedAtStart());
        inviteVehicleRecordDTO.setCreatedAtEnd(dto.getCreatedAtEnd());
        inviteVehicleRecordDTO.setIsself(dto.getIsself());
        inviteVehicleRecordDTO.setLeaveIds(dto.getLeaveIds());
        inviteVehicleRecordDTO.setSaId(dto.getSaId());
        List<Map> inviteVehicleRecordList = inviteVehicleRecordService.exportExcelinviteVehicleRecord
                (inviteVehicleRecordDTO);
                List<ExcelExportColumn> exportColumnList = new ArrayList<ExcelExportColumn>();
        exportColumnList.add(new ExcelExportColumn("vin", "VIN"));
        exportColumnList.add(new ExcelExportColumn("licensePlateNum", "车牌号"));
        exportColumnList.add(new ExcelExportColumn("name","客户姓名"));
        exportColumnList.add(new ExcelExportColumn("inviteTypeName", "邀约类型"));
        exportColumnList.add(new ExcelExportColumn("sonInviteType", "子邀约类型"));
        exportColumnList.add(new ExcelExportColumn("adviseInDate", "建议进厂日期","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("newAdviseInDate", "新建议进厂日期","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("lastInDate", "上次进厂日期","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("planFollowDate", "下次跟进日期","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("actualFollowDate", "实际跟进日期","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("saName", "跟进人员"));
        exportColumnList.add(new ExcelExportColumn("followStatusName", "跟进状态"));
        exportColumnList.add(new ExcelExportColumn("isBook", "是否有预约单"));
        exportColumnList.add(new ExcelExportColumn("orderStatusName", "线索完成状态"));
        //exportColumnList.add(new ExcelExportColumn("lastSaName", "上次SA"));
        exportColumnList.add(new ExcelExportColumn("createdAt","邀约创建日期","yyyy-MM-dd HH:mm:ss"));
        exportColumnList.add(new ExcelExportColumn("callTime", "通话时间","yyyy-MM-dd HH:mm:ss"));
        exportColumnList.add(new ExcelExportColumn("callLength", "通话时长"));
        exportColumnList.add(new ExcelExportColumn("totalScore", "AI得分"));
        exportColumnList.add(new ExcelExportColumn("orderFinishDate", "线索完成时间","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("content", "跟进内容"));
        exportColumnList.add(new ExcelExportColumn("loseReason", "失败原因", ExcelDataType.Dict));
        exportColumnList.add(new ExcelExportColumn("couponCode", "券码"));
        exportColumnList.add(new ExcelExportColumn("couponName", "卡券名称"));
        Map<String, List<Map>> excelData = new HashMap<String, List<Map>>(16);
        excelData.put("邀约跟进", inviteVehicleRecordList);
        Map<String, List<ExcelExportColumn>> ColumnMap = new HashMap<String, List<ExcelExportColumn>>(16);
        ColumnMap.put("邀约跟进", exportColumnList);
        excelGenerator.generateExcelSheet(excelData, ColumnMap, "邀约跟进导出.xls", request, response);
    }

    /**
     * 保险跟进----分页查询数据
     *
     * @param currentPage     页数
     * @param pageSize        分页大小
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-06-17
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "planFollowDateStart", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "planFollowDateEnd", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "actualFollowDateStart", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "actualFollowDateEnd", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "adviseInDateStart", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "adviseInDateEnd", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "licensePlateNum", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "name", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "saId", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "followChoiced", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
    })
    @ApiOperation(value = "保险跟进----分页查询数据", notes = "保险跟进----分页查询数据", httpMethod = "GET")
    @GetMapping("/selectFollowInsureRecord")
    public IPage<InviteVehicleRecordDTO> selectFollowInsureRecord(
            @RequestParam(value = "planFollowDateStart", required = false) String planFollowDateStart,
            @RequestParam(value = "planFollowDateEnd", required = false) String planFollowDateEnd,
            @RequestParam(value = "actualFollowDateStart", required = false) String actualFollowDateStart,
            @RequestParam(value = "actualFollowDateEnd", required = false) String actualFollowDateEnd,
            @RequestParam(value = "adviseInDateStart", required = false) String adviseInDateStart,
            @RequestParam(value = "adviseInDateEnd", required = false) String adviseInDateEnd,
            @RequestParam(value = "licensePlateNum", required = false) String licensePlateNum,
            @RequestParam(value = "vin", required = false) String vin,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "saId", required = false) String saId,
            @RequestParam(value = "followChoiced", required = false) String followChoiced,
            @RequestParam("currentPage") int currentPage,
            @RequestParam("pageSize") int pageSize) {
        Page<InviteVehicleRecordPO> page = new Page(currentPage, pageSize);
        InviteVehicleRecordDTO inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        inviteVehicleRecordDTO.setPlanFollowDateStart(planFollowDateStart);
        inviteVehicleRecordDTO.setPlanFollowDateEnd(planFollowDateEnd);
        inviteVehicleRecordDTO.setLicensePlateNum(licensePlateNum);
        inviteVehicleRecordDTO.setVin(vin);
        inviteVehicleRecordDTO.setActualFollowDateStart(actualFollowDateStart);
        inviteVehicleRecordDTO.setActualFollowDateEnd(actualFollowDateEnd);
        inviteVehicleRecordDTO.setName(name);
        inviteVehicleRecordDTO.setSaId(saId);
        inviteVehicleRecordDTO.setAdviseInDateStart(adviseInDateStart);
        inviteVehicleRecordDTO.setAdviseInDateEnd(adviseInDateEnd);
        return inviteVehicleRecordService.selectFollowInsureRecord(page, followChoiced, inviteVehicleRecordDTO);
    }

    /**
     * 保存跟进
     *
     * <AUTHOR>
     * @since 2020-06-17
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteVehicleRecordDetailDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "保存跟进", notes = "保存跟进", httpMethod = "POST")
    @PostMapping("/saveFollowInsureRecord")
    public int saveFollowInsureRecord(@RequestBody InviteVehicleRecordDetailDTO dto) {
        return inviteVehicleRecordService.saveFollowInsureRecord(dto);
    }

    /**
     * 导出 ---- 保险跟进
     * @param request
     * @param response
     * @throws Exception
     * <AUTHOR>
     *
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "planFollowDateStart", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "planFollowDateEnd", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "licensePlateNum", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "actualFollowDateStart", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "actualFollowDateEnd", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "name", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "followChoiced", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "saName", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "adviseInDateStart", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "adviseInDateEnd", value = "")
    })
    @ApiOperation(value = "导出 ---- 保险跟进", notes = "导出 ---- 保险跟进", httpMethod = "GET")
    @RequestMapping(value = "/exportFollowInsureExcel", method = RequestMethod.GET)
    @ResponseBody
    public void exportFollowInsureExcel(
            @RequestParam(value = "planFollowDateStart", required = false) String planFollowDateStart,
            @RequestParam(value = "planFollowDateEnd", required = false) String planFollowDateEnd,
            @RequestParam(value = "licensePlateNum", required = false) String licensePlateNum,
            @RequestParam(value = "vin", required = false) String vin,
            @RequestParam(value = "actualFollowDateStart", required = false) String actualFollowDateStart,
            @RequestParam(value = "actualFollowDateEnd", required = false) String actualFollowDateEnd,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "followChoiced", required = false) String followChoiced,
            @RequestParam(value = "saName", required = false) String saName,
            @RequestParam(value = "adviseInDateStart", required = false) String adviseInDateStart,
            @RequestParam(value = "adviseInDateEnd", required = false) String adviseInDateEnd,
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        InviteVehicleRecordDTO inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        inviteVehicleRecordDTO.setPlanFollowDateStart(planFollowDateStart);
        inviteVehicleRecordDTO.setPlanFollowDateEnd(planFollowDateEnd);
        inviteVehicleRecordDTO.setLicensePlateNum(licensePlateNum);
        inviteVehicleRecordDTO.setVin(vin);
        inviteVehicleRecordDTO.setActualFollowDateStart(actualFollowDateStart);
        inviteVehicleRecordDTO.setActualFollowDateEnd(actualFollowDateEnd);
        inviteVehicleRecordDTO.setName(name);
        inviteVehicleRecordDTO.setSaName(saName);
        inviteVehicleRecordDTO.setAdviseInDateStart(adviseInDateStart);
        inviteVehicleRecordDTO.setAdviseInDateEnd(adviseInDateEnd);
        List<Map> inviteVehicleRecordList = inviteVehicleRecordService.exportExcelFollowInsure
                (followChoiced,inviteVehicleRecordDTO);
        List<ExcelExportColumn> exportColumnList = new ArrayList<ExcelExportColumn>();
        exportColumnList.add(new ExcelExportColumn("name", "客户姓名"));
        exportColumnList.add(new ExcelExportColumn("vin", "VIN"));
        exportColumnList.add(new ExcelExportColumn("licensePlateNum", "车牌号"));
        exportColumnList.add(new ExcelExportColumn("inviteTypeName", "邀约类型"));
        exportColumnList.add(new ExcelExportColumn("adviseInDate", "续保到期时间","yyyy-MM-dd HH:mm:ss"));
        //exportColumnList.add(new ExcelExportColumn("newAdviseInDate", "新建议进厂日期","yyyy-MM-dd HH:mm:ss"));
        exportColumnList.add(new ExcelExportColumn("newestAdviseInDate", "最新进厂日期","yyyy-MM-dd HH:mm:ss"));
        exportColumnList.add(new ExcelExportColumn("lastInDate", "上次跟进日期","yyyy-MM-dd HH:mm:ss"));
        exportColumnList.add(new ExcelExportColumn("planFollowDate", "计划跟进日期","yyyy-MM-dd HH:mm:ss"));
        exportColumnList.add(new ExcelExportColumn("actualFollowDate", "实际跟进日期","yyyy-MM-dd HH:mm:ss"));
        exportColumnList.add(new ExcelExportColumn("saName", "跟进人员"));
        exportColumnList.add(new ExcelExportColumn("followStatusName", "跟进状态"));
        /*exportColumnList.add(new ExcelExportColumn("isBook", "是否有预约单"));
        exportColumnList.add(new ExcelExportColumn("orderStatusName", "线索完成状态"));*/
        exportColumnList.add(new ExcelExportColumn("insuranceStatus", "投保单状态"));
        exportColumnList.add(new ExcelExportColumn("isJointGuarantee", "投保单是否联保"));
        //exportColumnList.add(new ExcelExportColumn("lastSaName", "上次SA"));

        Map<String, List<Map>> excelData = new HashMap<String, List<Map>>(16);
        excelData.put("保险跟进", inviteVehicleRecordList);
        Map<String, List<ExcelExportColumn>> ColumnMap = new HashMap<String, List<ExcelExportColumn>>(16);
        ColumnMap.put("保险跟进", exportColumnList);
        excelGenerator.generateExcelSheet(excelData, ColumnMap, "保险跟进导出.xls", request, response);
    }

    /**
     * 保险跟进失败 -- 计划任务
     *
     * <AUTHOR>
     * @since 2020-08-31
     */
    @ApiOperation(value = "保险跟进失败 -- 计划任务", notes = "保险跟进失败 -- 计划任务", httpMethod = "POST")
    @PostMapping("/updateInsureFollowStatus")
    public void updateInsureFollowStatus() {
        inviteVehicleRecordService.updateInsureFollowStatus();
    }


    /**
     * 预约安排
     *
     * @param dto
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "BookingRecordDetailDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "预约安排", notes = "预约安排", httpMethod = "POST")
    @PostMapping("/saveBookingRecord")
    public String saveBookingRecord(@RequestBody BookingRecordDetailDTO dto) {
        //先保存预约
        InviteVehicleRecordDetailDTO record = new InviteVehicleRecordDetailDTO();
        record.setInviteId(dto.getInviteId());
        record.setContent(dto.getContent());
        record.setFeedback(dto.getFeedback());
        record.setStatus(dto.getStatus());
        record.setMode(dto.getMode());
        record.setLoseReason(dto.getLoseReason());
        record.setPlanDate(dto.getPlanDate());
        record.setRemark(dto.getRemark());
        record.setNoAIreason(dto.getNoAIreason());
        record.setErrorStatus(dto.getErrorStatus());
        record.setVin(dto.getVin());
        inviteVehicleRecordService.saveInviteVehicleRecord(record);

        BookingCreateParamsVo vo=new BookingCreateParamsVo();
        vo.setVin(dto.getVin());
        vo.setLicense(dto.getLicense());
        //查询最近次ai绑定电话，未找到绑定电话取车主电话
        SaCustomerNumberDTO cusNumber = saCustomerNumberService.getSaCustomerNumber(dto.getInviteId());
        if(cusNumber!=null){
            vo.setContactorPhone(cusNumber.getCusNumber());
            vo.setContactorName(cusNumber.getCusName());
        }else{
            vo.setContactorPhone(dto.getTel());
            vo.setContactorName(dto.getName());
        }
        vo.setOrderHandRepair(dto.getOrderHandrepair());
        vo.setOrderLabour(dto.getOrderlabour());
        vo.setOrderPart(dto.getOrderpart());
        vo.setOneId(dto.getOneId());
        vo.setOwnerCode(FrameworkUtil.getLoginInfo().getOwnerCode());
        vo.setOwnerOneId(dto.getOneId());
        vo.setOperationCode("add");
        vo.setBookingSource(80101007);
        vo.setBookingTypeCode("80551002");
        vo.setBookingOrderStatus(80671001);
        //80551001预约类型
        //REPAIR_TYPE_CODE: "N"维修类型
        //REPAIR_TYPE_NAME: "机电维修"
        //vo.setBookingComeTime(new Date());

        //不创建预约单
        //String bookingNo = inviteVehicleRecordService.saveBookingRecord(vo);
        // feign调用查询历史预约单用于回显
        String bookingNo=inviteVehicleRecordService.queryBookingNo(vo);
        // 线索更新优化方案：在cloud-service 异步feign调用，线索已关联预约单则无需更新，无关联需要更新线索
        // inviteVehicleRecordService.saveBookingNoForRecord(bookingNo,dto.getInviteId(),vo.getOwnerCode());
        return bookingNo;
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "insuranceId", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/selectInsuranceCustomerInfo/{insuranceId}")
    public List<InviteInsuranceCustomerInfoDTO> selectAllInsuranceCustomerInfo(@PathVariable("insuranceId") Long insuranceId) {
        return saCustomerNumberService.selectAllInsuranceCustomerInfo(insuranceId);
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteInsuranceCustomerInfoDTO", name = "infoDTO", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping("/updateInsuranceCustomerInfo")
    public void updateInsuranceCustomerInfo(@RequestBody InviteInsuranceCustomerInfoDTO infoDTO) {
        saCustomerNumberService.updateInsuranceCustomerInfo(infoDTO);
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "tiicId", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/deleteInsuranceCustomerInfo/{tiicId}")
    public void deleteInsuranceCustomerInfo(@PathVariable("tiicId") Long tiicId) {
        saCustomerNumberService.deleteInsuranceCustomerInfo(tiicId);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "bookingNo", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "inviteId", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/updateInviteVehicleRecord/interf")
    public void updateInviteVehicleRecord(@RequestParam(value = "bookingNo") String bookingNo,
            @RequestParam(value = "inviteId") Long inviteId,
            @RequestParam(value = "ownerCode") String ownerCode,@RequestParam(value = "vin") String vin) {
        inviteVehicleRecordService.saveBookingNoForRecord(bookingNo,inviteId,ownerCode,vin);
    }

    /**
     * 查询线索记录根据vin或者经销商
     * @param ownerCode 经销商
     * @param vin vin
     * @param inviteType 线索类型
     * @return 参数对象
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "经销商"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "vin", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "inviteType", value = "线索类型", required = true)
    })
    @ApiOperation(value = "查询线索记录根据vin或者经销商", notes = "查询线索记录根据vin或者经销商", httpMethod = "GET")
    @GetMapping("/queryInviteVehicleByVin")
   public InviteVehicleRecordVo queryInviteVehicleByVin(@RequestParam(value = "ownerCode", required = false) String ownerCode,
                                                        @RequestParam("vin") String vin,
                                                        @RequestParam("inviteType") Integer inviteType){
        if (inviteType == null || StringUtils.isBlank(vin)) {
            throw new ServiceBizException("参数错误！");
        }
        return inviteVehicleRecordService.queryInviteVehicleByVin(ownerCode, vin, inviteType);
   }

}