package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAuditInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillAuditInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;






                                                                                /**
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
@Api(value = "/goodwillAuditInfo", tags = {"GoodwillAuditInfoController"})
@RestController
@RequestMapping("/goodwillAuditInfo")
                public class GoodwillAuditInfoController {
    
        @Autowired
        GoodwillAuditInfoService goodwillAuditInfoService;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码
        * @param ownerParCode 所有者的父组织代码
        * @param orgId 组织ID
        * @param id 主键ID
        * @param goodwillApplyId 亲善单id
        * @param auditObject 审批对象：0,预申请;1,材料
        * @param auditType 审批类型：0.区域审核；1.CCM审核
        * @param auditRole 审核角色
        * @param auditor 审核人
        * @param auditTime 审核时间
        * @param auditResult 审核结果
        * @param auditOpinion 审核意见
        * @param auditPrice 审核金额
        * @param returnTo 驳回单位
        * @param isValid 是否有效
        * @param isDeleted 是否删除:1,删除；0,未删除
        * @param createdAt 创建时间
        * @param updatedAt 修改时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @throws ParseException 
         * @since 2020-05-29
        */
                @ApiImplicitParams({
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = "亲善单id"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "auditObject", value = "审批对象：0,预申请;1,材料"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "auditType", value = "审批类型：0.区域审核；1.CCM审核"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "auditRole", value = "审核角色"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "auditor", value = "审核人"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "auditTime", value = "审核时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "auditResult", value = "审核结果"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "auditOpinion", value = "审核意见"),
                        @ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "auditPrice", value = "审核金额"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "returnTo", value = "驳回单位"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
                        @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除:1,删除；0,未删除"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = "创建时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = "修改时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
                })
                @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
                @GetMapping
        public IPage<GoodwillAuditInfoDTO>getByPage(
		@RequestParam(value="appId",required = false) String appId,
		@RequestParam(value="ownerCode",required = false) String ownerCode,
		@RequestParam(value="ownerParCode",required = false) String ownerParCode,
		@RequestParam(value="orgId",required = false) Integer orgId,
		@RequestParam(value="id",required = false) Long id,
		@RequestParam(value="goodwillApplyId",required = false) Long goodwillApplyId,
		@RequestParam(value="auditObject",required = false) Integer auditObject,
		@RequestParam(value="auditType",required = false) Integer auditType,
		@RequestParam(value="auditRole",required = false) String auditRole,
		@RequestParam(value="auditor",required = false) Long auditor,
		@RequestParam(value="auditTime",required = false) String auditTime,
		@RequestParam(value="auditResult",required = false) Integer auditResult,
		@RequestParam(value="auditOpinion",required = false) String auditOpinion,
		@RequestParam(value="auditPrice",required = false) BigDecimal auditPrice,
		@RequestParam(value="returnTo",required = false) String returnTo,
		@RequestParam(value="isValid",required = false) Integer isValid,
		@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
		@RequestParam(value="createdAt",required = false) String createdAt,
		@RequestParam(value="updatedAt",required = false) String updatedAt,
        @RequestParam("currentPage")int currentPage,
        @RequestParam("pageSize")int pageSize) throws ParseException{
            	Page<GoodwillAuditInfoPO>page=new Page(currentPage,pageSize);
            	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            	GoodwillAuditInfoDTO goodwillAuditInfoDTO =new GoodwillAuditInfoDTO();
                goodwillAuditInfoDTO.setAppId(appId);
                goodwillAuditInfoDTO.setOwnerCode(ownerCode);
                goodwillAuditInfoDTO.setOwnerParCode(ownerParCode);
                goodwillAuditInfoDTO.setOrgId(orgId);
                goodwillAuditInfoDTO.setId(id);
                goodwillAuditInfoDTO.setGoodwillApplyId(goodwillApplyId);
                goodwillAuditInfoDTO.setAuditObject(auditObject);
                goodwillAuditInfoDTO.setAuditType(auditType);
                goodwillAuditInfoDTO.setAuditRole(auditRole);
                goodwillAuditInfoDTO.setAuditor(auditor);
                goodwillAuditInfoDTO.setAuditResult(auditResult);
                goodwillAuditInfoDTO.setAuditOpinion(auditOpinion);
                goodwillAuditInfoDTO.setAuditPrice(auditPrice);
                goodwillAuditInfoDTO.setReturnTo(returnTo);
                goodwillAuditInfoDTO.setIsValid(isValid);
                goodwillAuditInfoDTO.setIsDeleted(isDeleted);
                if(auditTime != null){
                	goodwillAuditInfoDTO.setAuditTime(sdf.parse(auditTime));
                }
                if(createdAt != null){
                	goodwillAuditInfoDTO.setCreatedAt(sdf.parse(createdAt));
                }
                if(updatedAt != null){
                	goodwillAuditInfoDTO.setUpdatedAt(sdf.parse(updatedAt));
                }
                
                return goodwillAuditInfoService.selectPageBysql(page,goodwillAuditInfoDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditInfoDTO
 * <AUTHOR>
 * @since 2020-05-29
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public GoodwillAuditInfoDTO getById(@PathVariable("id") Long id){
        return goodwillAuditInfoService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param goodwillAuditInfoDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-05-29
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "GoodwillAuditInfoDTO", name = "goodwillAuditInfoDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody GoodwillAuditInfoDTO goodwillAuditInfoDTO){
        return goodwillAuditInfoService.insert( goodwillAuditInfoDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param goodwillAuditInfoDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-05-29
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "GoodwillAuditInfoDTO", name = "goodwillAuditInfoDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody GoodwillAuditInfoDTO goodwillAuditInfoDTO){
        return goodwillAuditInfoService.update(id,goodwillAuditInfoDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-05-29
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    goodwillAuditInfoService.deleteById(id);
        return true;
        }

        /**
         * 根据IDs批量删除对象
         *
         * @param ids 需要修改数据的ID集合
         * <AUTHOR>
         * @since 2020-05-29
         */
                        @ApiImplicitParams({
                                @ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "需要修改数据的ID集合", required = true)
                        })
                        @ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
                        @DeleteMapping(value = "/batch/{ids}")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        public boolean deleteByIds(@PathVariable("ids") String ids){
            goodwillAuditInfoService.deleteBatchIds(ids);
                return true;
                }

}