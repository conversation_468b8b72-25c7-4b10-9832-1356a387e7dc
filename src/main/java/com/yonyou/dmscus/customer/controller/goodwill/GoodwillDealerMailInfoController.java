package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.utils.common.StringUtils;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillDealerMailInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillDealerMailInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillDealerMailInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
@Api(value = "/goodwillDealerMailInfo", tags = {"GoodwillDealerMailInfoController"})
@RestController
@RequestMapping("/goodwillDealerMailInfo")
public class GoodwillDealerMailInfoController {

	@Autowired
	GoodwillDealerMailInfoService goodwillDealerMailInfoService;

	/**
	 * 分页查询数据
	 *
	 * @param appId        系统ID
	 * @param ownerCode    所有者代码
	 * @param ownerParCode 所有者的父组织代码
	 * @param orgId        组织ID
	 * @param id           主键id
	 * @param dealerCode   经销商代码
	 * @param eMail1       邮箱1
	 * @param eMail2       邮箱2
	 * @param updateDate   更新日期
	 * @param isValid      是否有效
	 * @param isDeleted    是否删除:1,删除；0,未删除
	 * @param createdAt    创建时间
	 * @param updatedAt    修改时间
	 * @param currentPage  页数
	 * @param pageSize     分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * @throws ParseException
	 * <AUTHOR>
	 * @since 2020-04-22
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键id"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "经销商代码"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "bloc", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "eMail1", value = "邮箱1"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "eMail2", value = "邮箱2"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updateDate", value = "更新日期"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除:1,删除；0,未删除"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = "创建时间"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = "修改时间"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
	})
	@ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
	@GetMapping
	public IPage<GoodwillDealerMailInfoDTO> getByPage(@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "areaManage", required = false) String areaManage,
			@RequestParam(value = "bloc", required = false) String bloc,
			@RequestParam(value = "eMail1", required = false) String eMail1,
			@RequestParam(value = "eMail2", required = false) String eMail2,
			@RequestParam(value = "updateDate", required = false) String updateDate,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillDealerMailInfoPO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		GoodwillDealerMailInfoDTO goodwillDealerMailInfoDTO = new GoodwillDealerMailInfoDTO();
		goodwillDealerMailInfoDTO.setAppId(appId);
		goodwillDealerMailInfoDTO.setOwnerCode(ownerCode);
		goodwillDealerMailInfoDTO.setOwnerParCode(ownerParCode);
		goodwillDealerMailInfoDTO.setOrgId(orgId);
		goodwillDealerMailInfoDTO.setId(id);
		goodwillDealerMailInfoDTO.setDealerCode(dealerCode);
		goodwillDealerMailInfoDTO.seteMail1(eMail1);
		goodwillDealerMailInfoDTO.seteMail2(eMail2);
		goodwillDealerMailInfoDTO.setIsValid(isValid);
		goodwillDealerMailInfoDTO.setIsDeleted(isDeleted);
		if (!StringUtils.isNullOrEmpty(areaManage)) {
			goodwillDealerMailInfoDTO.setAreaManage(Integer.valueOf(areaManage));
		}
		if (!StringUtils.isNullOrEmpty(bloc)) {
			goodwillDealerMailInfoDTO.setBloc(bloc);
		}
		if (updateDate != null) {
			goodwillDealerMailInfoDTO.setCreatedAt(sdf.parse(updateDate));
		}
		if (createdAt != null) {
			goodwillDealerMailInfoDTO.setCreatedAt(sdf.parse(createdAt));
		}
		if (updatedAt != null) {
			goodwillDealerMailInfoDTO.setUpdatedAt(sdf.parse(updatedAt));
		}
		return goodwillDealerMailInfoService.selectPageBysql(page, goodwillDealerMailInfoDTO);
	}

	/**
	 * 分页查询VCDC数据
	 *
	 * @param role        角色
	 * @param currentPage 页数
	 * @param pageSize    分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * @throws ParseException
	 * <AUTHOR>
	 * @since 2020-04-22
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "role", value = "角色"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
	})
	@ApiOperation(value = "分页查询VCDC数据", notes = "分页查询VCDC数据", httpMethod = "GET")
	@GetMapping(value = "/list")
	public IPage<List> getVcdcByPage(@RequestParam(value = "role", required = false) String role,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillDealerMailInfoPO> page = new Page(currentPage, pageSize);
		return goodwillDealerMailInfoService.selectVcdcPageBysql(page, role);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id 数据主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.GoodwillDealerMailInfoDTO
	 * <AUTHOR>
	 * @since 2020-04-22
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
	@GetMapping(value = "/{id}")
	public GoodwillDealerMailInfoDTO getById(@PathVariable("id") Long id) {
		return goodwillDealerMailInfoService.getById(id);
	}

	/**
	 * 进行数据新增
	 *
	 * @param goodwillDealerMailInfoDTO 需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-04-22
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillDealerMailInfoDTO", name = "goodwillDealerMailInfoDTO", value = "需要保存的DTO", required = true)
	})
	@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
	@PostMapping
	@ResponseStatus(HttpStatus.CREATED)
	public int insert(@RequestBody GoodwillDealerMailInfoDTO goodwillDealerMailInfoDTO) {
		return goodwillDealerMailInfoService.insert(goodwillDealerMailInfoDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id                        需要修改数据的ID
	 * @param goodwillDealerMailInfoDTO 需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-04-22
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillDealerMailInfoDTO", name = "goodwillDealerMailInfoDTO", value = "需要保存的DTO", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
	@PutMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.CREATED)
	public int update(@PathVariable("id") Long id, @RequestBody GoodwillDealerMailInfoDTO goodwillDealerMailInfoDTO) {
		return goodwillDealerMailInfoService.update(id, goodwillDealerMailInfoDTO);
	}

	/**
	 * 根据id删除对象
	 *
	 * @param id 需要修改数据的ID
	 * <AUTHOR>
	 * @since 2020-04-22
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
	})
	@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteById(@PathVariable("id") Long id) {
		goodwillDealerMailInfoService.deleteById(id);
		return true;
	}

	/**
	 * 根据IDs批量删除对象
	 *
	 * @param ids 需要修改数据的ID集合
	 * <AUTHOR>
	 * @since 2020-04-22
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "需要修改数据的ID集合", required = true)
	})
	@ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/batch/{ids}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteByIds(@PathVariable("ids") String ids) {
		goodwillDealerMailInfoService.deleteBatchIds(ids);
		return true;
	}

	/**
	 * 
	 * 查询集团下拉框
	 * 
	 * @param
	 * <AUTHOR>
	 * @since 2020-04-22
	 */
	@ApiOperation(value = "查询集团下拉框", notes = "查询集团下拉框", httpMethod = "GET")
	@GetMapping(value = "/blocSelect")
	public List<Map> blocSelect() {

		return goodwillDealerMailInfoService.blocSelect();
	}

	/**
	 * 
	 * 查询区域-区域经理下拉框
	 * 
	 * @param
	 * <AUTHOR>
	 * @since 2020-04-22
	 */
	@ApiOperation(value = "查询区域-区域经理下拉框", notes = "查询区域-区域经理下拉框", httpMethod = "GET")
	@GetMapping(value = "/areaManageSelect")
	public List<Map> areaManageSelect() {

		return goodwillDealerMailInfoService.areaManageSelect();
	}

}