package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMailTemplateMaintainDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMailTemplateMaintainPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillMailTemplateMaintainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2020-04-28
 */
@Api(value = "/goodwillMailTemplateMaintain", tags = {"GoodwillMailTemplateMaintainController"})
@RestController
@RequestMapping("/goodwillMailTemplateMaintain")
public class GoodwillMailTemplateMaintainController {

	@Autowired
	GoodwillMailTemplateMaintainService goodwillMailTemplateMaintainService;

	/**
	 * 分页查询数据
	 *
	 * @param appId        系统ID
	 * @param ownerCode    所有者代码
	 * @param ownerParCode 所有者的父组织代码
	 * @param orgId        组织ID
	 * @param 主键id         主键id
	 * @param mailType     邮件类型
	 * @param sendType     发送类型
	 * @param isUse        是否启用
	 * @param sendObject   发送对象
	 * @param mailTitle    邮件标题
	 * @param mailContent  邮件内容
	 * @param isValid      是否有效
	 * @param isDeleted    是否删除:1,删除；0,未删除
	 * @param createdAt    创建时间
	 * @param updatedAt    修改时间
	 * @param currentPage  页数
	 * @param pageSize     分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * @throws ParseException
	 * <AUTHOR>
	 * @since 2020-04-28
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "mailType", value = "邮件类型"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "sendType", value = "发送类型"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isUse", value = "是否启用"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "mailTitle", value = "邮件标题"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "mailContent", value = "邮件内容"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除:1,删除；0,未删除"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = "创建时间"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = "修改时间"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
	})
	@ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
	@GetMapping
	public IPage<GoodwillMailTemplateMaintainDTO> getByPage(
			@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "mailType", required = false) Integer mailType,
			@RequestParam(value = "sendType", required = false) Integer sendType,
			@RequestParam(value = "isUse", required = false) Integer isUse,
			@RequestParam(value = "mailTitle", required = false) String mailTitle,
			@RequestParam(value = "mailContent", required = false) String mailContent,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillMailTemplateMaintainPO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO = new GoodwillMailTemplateMaintainDTO();
		goodwillMailTemplateMaintainDTO.setAppId(appId);
		goodwillMailTemplateMaintainDTO.setOwnerCode(ownerCode);
		goodwillMailTemplateMaintainDTO.setOwnerParCode(ownerParCode);
		goodwillMailTemplateMaintainDTO.setOrgId(orgId);
		goodwillMailTemplateMaintainDTO.setId(id);
		goodwillMailTemplateMaintainDTO.setMailType(mailType);
		goodwillMailTemplateMaintainDTO.setSendType(sendType);
		goodwillMailTemplateMaintainDTO.setIsUse(isUse);
		goodwillMailTemplateMaintainDTO.setMailTitle(mailTitle);
		goodwillMailTemplateMaintainDTO.setMailContent(mailContent);
		goodwillMailTemplateMaintainDTO.setIsValid(isValid);
		goodwillMailTemplateMaintainDTO.setIsDeleted(isDeleted);

		if (createdAt != null) {
			goodwillMailTemplateMaintainDTO.setCreatedAt(sdf.parse(createdAt));
		}
		if (updatedAt != null) {
			goodwillMailTemplateMaintainDTO.setUpdatedAt(sdf.parse(updatedAt));
		}
		return goodwillMailTemplateMaintainService.selectPageBysql(page, goodwillMailTemplateMaintainDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id 数据主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMailTemplateMaintainDTO
	 * <AUTHOR>
	 * @since 2020-04-28
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
	@GetMapping(value = "/{id}")
	public GoodwillMailTemplateMaintainDTO getById(@PathVariable("id") Long id) {
		return goodwillMailTemplateMaintainService.getById(id);
	}

	/**
	 * 进行数据新增
	 *
	 * @param goodwillMailTemplateMaintainDTO 需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-04-28
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillMailTemplateMaintainDTO", name = "goodwillMailTemplateMaintainDTO", value = "需要保存的DTO", required = true)
	})
	@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
	@PostMapping
	@ResponseStatus(HttpStatus.CREATED)
	public int insert(@RequestBody GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO) {
		return goodwillMailTemplateMaintainService.insert(goodwillMailTemplateMaintainDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id                              需要修改数据的ID
	 * @param goodwillMailTemplateMaintainDTO 需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-04-28
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillMailTemplateMaintainDTO", name = "goodwillMailTemplateMaintainDTO", value = "需要保存的DTO", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
	@PutMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.CREATED)
	public int update(@PathVariable("id") Long id,
			@RequestBody GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO) {
		return goodwillMailTemplateMaintainService.update(id, goodwillMailTemplateMaintainDTO);
	}

	/**
	 * 根据id删除对象
	 *
	 * @param id 需要修改数据的ID
	 * <AUTHOR>
	 * @since 2020-04-28
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
	})
	@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteById(@PathVariable("id") Long id) {
		goodwillMailTemplateMaintainService.deleteById(id);
		return true;
	}

	/**
	 * 根据IDs批量删除对象
	 *
	 * @param ids 需要修改数据的ID集合
	 * <AUTHOR>
	 * @since 2020-04-28
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "需要修改数据的ID集合", required = true)
	})
	@ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/batch/{ids}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteByIds(@PathVariable("ids") String ids) {
		goodwillMailTemplateMaintainService.deleteBatchIds(ids);
		return true;
	}

	/**
	 * 根据IDs批量删除对象
	 *
	 * @param ids 需要修改数据的ID集合
	 * <AUTHOR>
	 * @since 2020-04-28
	 */
	@ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "GET")
	@GetMapping(value = "queryEmailMaintainInfo")
	public List<GoodwillMailTemplateMaintainDTO> queryEmailMaintainInfo() {
		GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO = new GoodwillMailTemplateMaintainDTO();
		return goodwillMailTemplateMaintainService.selectListBySql(goodwillMailTemplateMaintainDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id                              需要修改数据的ID
	 * @param goodwillMailTemplateMaintainDTO 需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-04-28
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "List<GoodwillMailTemplateMaintainDTO>", name = "goodwillMailTemplateMaintainDTO", value = "需要保存的DTO", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "POST")
	@PostMapping(value = "/saveEmailMaintain")
	@ResponseStatus(HttpStatus.CREATED)
	public int updateEmailMaintain(@RequestBody List<GoodwillMailTemplateMaintainDTO> goodwillMailTemplateMaintainDTO) {
		return goodwillMailTemplateMaintainService.updateEmailMaintain(goodwillMailTemplateMaintainDTO);
	}

}