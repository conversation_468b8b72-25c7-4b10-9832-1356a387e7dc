package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyMailHistoryDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyMailHistoryPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyMailHistoryService;
import com.yonyou.dmscus.customer.utils.Utills;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-05-27
 */
@Api(value = "/goodwillApplyMailHistory", tags = {"GoodwillApplyMailHistoryController"})
@RestController
@RequestMapping("/goodwillApplyMailHistory")
public class GoodwillApplyMailHistoryController extends BaseController {

	@Autowired
	GoodwillApplyMailHistoryService goodwillApplyMailHistoryService;

	/**
	 * 分页查询数据
	 *
	 * @param goodwillApplyId
	 *            亲善单id
	 * @param dealerCode
	 *            经销商
	 * @param mailType
	 *            邮件类型
	 * @param sendStatus
	 *            发送状态
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @since 2020-05-27
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "blocId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "areaManageId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "smallAreaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "mailType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "sendStatus", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdStartAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询数据", httpMethod = "GET")
	@RequestMapping(value = "/queryEmailHistory", method = RequestMethod.GET)
	public IPage<GoodwillApplyMailHistoryDTO> getByPage(
			@RequestParam(value = "blocId", required = false) Integer blocId,
			@RequestParam(value = "areaManageId", required = false) Integer areaManageId,
			@RequestParam(value = "areaManage", required = false) String areaManage,
			@RequestParam(value = "smallAreaManage", required = false) String smallAreaManage,
			@RequestParam(value = "auditName", required = false) String auditName,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "mailType", required = false) Integer mailType,
			@RequestParam(value = "sendStatus", required = false) Integer sendStatus,
			@RequestParam(value = "createdStartAt", required = false) String createdStartAt,
			@RequestParam(value = "createdEndAt", required = false) String createdEndAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize) {
		Page<GoodwillApplyMailHistoryPO> page = new Page(currentPage, pageSize);

		GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO = new GoodwillApplyMailHistoryDTO();
		goodwillApplyMailHistoryDTO.setBlocId(blocId);
		goodwillApplyMailHistoryDTO.setAreaManage(areaManage);
		goodwillApplyMailHistoryDTO.setSmallArea(smallAreaManage);
		goodwillApplyMailHistoryDTO.setAuditName1(Utills.getList(auditName));
		goodwillApplyMailHistoryDTO.setApplyNo(applyNo);
		goodwillApplyMailHistoryDTO.setDealerCode(dealerCode);
		goodwillApplyMailHistoryDTO.setMailType(mailType);
		goodwillApplyMailHistoryDTO.setSendStatus(sendStatus);
		if (StringUtils.isNotBlank(createdStartAt)) {
			goodwillApplyMailHistoryDTO.setCreatedStartAt(createdStartAt);
		}
		if (StringUtils.isNotBlank(createdEndAt)) {
			goodwillApplyMailHistoryDTO.setCreatedEndAt(createdEndAt);
		}
		return goodwillApplyMailHistoryService.selectPageBysql(page, goodwillApplyMailHistoryDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id
	 *            数据主键ID
	 * @return com.yonyou.dmscus.repair.entity.dto.GoodwillApplyMailHistoryDTO
	 * <AUTHOR>
	 * @since 2020-05-27
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
	})
	@ApiOperation(value = "进行数据修改", httpMethod = "GET")
	@GetMapping(value = "/{id}")
	public GoodwillApplyMailHistoryDTO getById(@PathVariable("id") Long id) {
		return goodwillApplyMailHistoryService.getById(id);
	}

	/**
	 * 进行数据新增
	 *
	 * @param goodwillApplyMailHistoryDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-27
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyMailHistoryDTO", name = "goodwillApplyMailHistoryDTO", value = "", required = true)
	})
	@ApiOperation(value = "进行数据新增", httpMethod = "POST")
	@PostMapping
	@ResponseStatus(HttpStatus.CREATED)
	public int insert(@RequestBody GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO) {
		return goodwillApplyMailHistoryService.insert(goodwillApplyMailHistoryDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id
	 *            需要修改数据的ID
	 * @param goodwillApplyMailHistoryDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-27
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true),
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyMailHistoryDTO", name = "goodwillApplyMailHistoryDTO", value = "", required = true)
	})
	@ApiOperation(value = "进行数据修改", httpMethod = "PUT")
	@PutMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.CREATED)
	public int update(@PathVariable("id") Long id,
			@RequestBody GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO) {
		return goodwillApplyMailHistoryService.update(id, goodwillApplyMailHistoryDTO);
	}

	/**
	 * 根据id删除对象
	 *
	 * @param id
	 *            需要修改数据的ID
	 * <AUTHOR>
	 * @since 2020-05-27
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
	})
	@ApiOperation(value = "根据id删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteById(@PathVariable("id") Long id) {
		goodwillApplyMailHistoryService.deleteById(id);
		return true;
	}

	/**
	 * 根据IDs批量删除对象
	 *
	 * @param ids
	 *            需要修改数据的ID集合
	 * <AUTHOR>
	 * @since 2020-05-27
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "", required = true)
	})
	@ApiOperation(value = "根据IDs批量删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/batch/{ids}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteByIds(@PathVariable("ids") String ids) {
		goodwillApplyMailHistoryService.deleteBatchIds(ids);
		return true;
	}

	/**
	 * 再次发送邮件
	 *
	 * @param id
	 *            需要修改数据的ID
	 * @param goodwillApplyMailHistoryDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-08-10
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
	})
	@ApiOperation(value = "再次发送邮件", httpMethod = "PUT")
	@PutMapping(value = "/{id}/sendAgain")
	@ResponseStatus(HttpStatus.CREATED)
	public int sendAgain(@PathVariable("id") Long id) {
		return goodwillApplyMailHistoryService.sendAgain(id);
	}

	/**
	 * 分页查询数据
	 *
	 * @param goodwillApplyId
	 *            亲善单id
	 * @param dealerCode
	 *            经销商
	 * @param mailType
	 *            邮件类型
	 * @param sendStatus
	 *            发送状态
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @since 2020-05-27
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "blocId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "areaManageId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "smallAreaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "mailType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "sendStatus", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdStartAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdEndAt", value = "")
	})
	@ApiOperation(value = "分页查询数据", httpMethod = "GET")
	@RequestMapping(value = "/exportEmailHistory", method = RequestMethod.GET)
	public List<GoodwillApplyMailHistoryDTO> getByList(@RequestParam(value = "blocId", required = false) Integer blocId,
			@RequestParam(value = "areaManageId", required = false) Integer areaManageId,
			@RequestParam(value = "areaManage", required = false) String areaManage,
			@RequestParam(value = "smallAreaManage", required = false) String smallAreaManage,
			@RequestParam(value = "auditName", required = false) String auditName,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "mailType", required = false) Integer mailType,
			@RequestParam(value = "sendStatus", required = false) Integer sendStatus,
			@RequestParam(value = "createdStartAt", required = false) String createdStartAt,
			@RequestParam(value = "createdEndAt", required = false) String createdEndAt) {

		GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO = new GoodwillApplyMailHistoryDTO();
		goodwillApplyMailHistoryDTO.setBlocId(blocId);
		goodwillApplyMailHistoryDTO.setAreaManageId(areaManageId);
		goodwillApplyMailHistoryDTO.setAreaManage(areaManage);
		goodwillApplyMailHistoryDTO.setSmallArea(smallAreaManage);
		goodwillApplyMailHistoryDTO.setAuditName1(Utills.getList(auditName));
		goodwillApplyMailHistoryDTO.setApplyNo(applyNo);
		goodwillApplyMailHistoryDTO.setDealerCode(dealerCode);
		goodwillApplyMailHistoryDTO.setMailType(mailType);
		goodwillApplyMailHistoryDTO.setSendStatus(sendStatus);
		if (StringUtils.isNotBlank(createdStartAt)) {
			goodwillApplyMailHistoryDTO.setCreatedStartAt(createdStartAt);
		}
		if (StringUtils.isNotBlank(createdEndAt)) {
			goodwillApplyMailHistoryDTO.setCreatedEndAt(createdEndAt);
		}
		return goodwillApplyMailHistoryService.selectListBySql(goodwillApplyMailHistoryDTO);
	}

}