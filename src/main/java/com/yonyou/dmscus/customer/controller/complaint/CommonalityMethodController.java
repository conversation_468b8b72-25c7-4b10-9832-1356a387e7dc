package com.yonyou.dmscus.customer.controller.complaint;

import com.yonyou.dmscus.customer.service.complaint.sale.CommonalityMethodService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公共配置控制层
 */
@Api(value = "/common", tags = {"公共配置控制层"})
@AllArgsConstructor
@RestController
@RequestMapping("/common")
public class CommonalityMethodController {

    private CommonalityMethodService commonalityMethodService;

    /**
     * 区域数据刷新
     */
    @ApiOperation(value = "区域数据刷新", notes = "区域数据刷新", httpMethod = "GET")
    @GetMapping(value = "/regionInformationRefresh")
    public void regionInformationRefresh(@RequestParam("dealerCodes") List<String> dealerCodes) {
        commonalityMethodService.regionInformationRefresh(dealerCodes);
    }
}
