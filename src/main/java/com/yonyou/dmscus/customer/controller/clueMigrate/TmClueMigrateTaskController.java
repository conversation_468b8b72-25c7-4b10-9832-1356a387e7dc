package com.yonyou.dmscus.customer.controller.clueMigrate;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yonyou.dmscloud.function.domains.dto.ImportResultDto;
import com.yonyou.dmscus.customer.controller.common.CommonController;
import com.yonyou.dmscus.customer.dto.clueMigrate.ClueMigrateTaskAddDTO;
import com.yonyou.dmscus.customer.dto.clueMigrate.ClueMigrateTaskQueryDTO;
import com.yonyou.dmscus.customer.dto.clueMigrate.DealerToDealerTaskImportDTO;
import com.yonyou.dmscus.customer.dto.clueMigrate.VinToDealerTaskImportDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.CompanyDetailDTO;
import com.yonyou.dmscus.customer.entity.po.clueMigrate.TmClueMigrateTask;
import com.yonyou.dmscus.customer.entity.po.dealermigrationrecord.btnlog.DealerMigrationRecordPo;
import com.yonyou.dmscus.customer.service.clueMigrate.ITmClueMigrateTaskService;
import com.yonyou.dmscus.customer.vo.clueMigrate.ClueMigrateTaskVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 线索迁移任务表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Api(value = "/tm-clue-migrate-task", tags = {"线索迁移任务表 前端控制器"})
@RestController
@RequestMapping("/tm-clue-migrate-task")
public class TmClueMigrateTaskController extends CommonController<ClueMigrateTaskVO, ClueMigrateTaskAddDTO, ClueMigrateTaskQueryDTO, TmClueMigrateTask> {

    @Autowired
    private ITmClueMigrateTaskService clueMigrateTaskService;


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClueMigrateTaskAddDTO", name = "clueMigrateTaskAddDTO", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "PUT")
    @PutMapping( "/createDealerToDealerTask" )
    public @ResponseBody String createDealerToDealerTask( @RequestBody ClueMigrateTaskAddDTO clueMigrateTaskAddDTO) {
        clueMigrateTaskService.createDealerToDealerTask( clueMigrateTaskAddDTO );
        return "success";
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClueMigrateTaskAddDTO", name = "clueMigrateTaskAddDTO", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "PUT")
    @PutMapping( "/createVinToDealerTask" )
    public @ResponseBody String createVinToDealerTask( @RequestBody ClueMigrateTaskAddDTO clueMigrateTaskAddDTO) {
        clueMigrateTaskService.createVinToDealerTask( clueMigrateTaskAddDTO );
        return "success";
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClueMigrateTaskQueryDTO", name = "clueMigrateTaskQueryDTO", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping( "/getDealerToDealerMigrateTaskVOPageList" )
    @ResponseBody
    IPage<ClueMigrateTaskVO> getDealerToDealerMigrateTaskVOPageList(@RequestBody ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO) {
        return clueMigrateTaskService.getDealerToDealerMigrateTaskVOPageList(clueMigrateTaskQueryDTO );
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClueMigrateTaskQueryDTO", name = "clueMigrateTaskQueryDTO", value = "")
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping( "/getDealerToDealerMigrateTaskVOList" )
    @ResponseBody
    List<ClueMigrateTaskVO> getDealerToDealerMigrateTaskVOList(@RequestBody( required = false ) ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO) {
        return clueMigrateTaskService.getDealerToDealerMigrateTaskVOList(clueMigrateTaskQueryDTO );
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClueMigrateTaskQueryDTO", name = "clueMigrateTaskQueryDTO", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping( "/getVinToDealerMigrateTaskVOPageList" )
    @ResponseBody
    IPage<ClueMigrateTaskVO> getVinToDealerMigrateTaskVOPageList(@RequestBody ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO) {
        return clueMigrateTaskService.getVinToDealerMigrateTaskVOPageList(clueMigrateTaskQueryDTO );
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClueMigrateTaskQueryDTO", name = "clueMigrateTaskQueryDTO", value = "")
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping( "/getVinToDealerMigrateTaskVOList" )
    @ResponseBody
    List<ClueMigrateTaskVO> getVinToDealerMigrateTaskVOList(@RequestBody( required = false ) ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO) {
        return clueMigrateTaskService.getVinToDealerMigrateTaskVOList(clueMigrateTaskQueryDTO );
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClueMigrateTaskQueryDTO", name = "clueMigrateTaskQueryDTO", value = "")
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping(value = "/exportDealerToDealerClueMigrateTaskList" )
    public void exportDealerToDealerClueMigrateTaskList(
            @RequestBody( required=false)  ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO,
            HttpServletRequest request, HttpServletResponse response
    )  {
        clueMigrateTaskService.exportDealerToDealerClueMigrateTaskList( clueMigrateTaskQueryDTO, request, response );
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClueMigrateTaskQueryDTO", name = "clueMigrateTaskQueryDTO", value = "")
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping(value = "/exportVinToDealerClueMigrateTaskList" )
    public void exportVinToDealerClueMigrateTaskList(
            @RequestBody( required=false)  ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO,
            HttpServletRequest request, HttpServletResponse response
    )  {
        clueMigrateTaskService.exportVinToDealerClueMigrateTaskList( clueMigrateTaskQueryDTO, request, response );
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping("/importDealerToDealerClueMigrateTask")
    public ImportResultDto<DealerToDealerTaskImportDTO> importDealerToDealerClueMigrateTask(@RequestParam(value = "file") MultipartFile importFile) throws Exception {
        return clueMigrateTaskService.importDealerToDealerClueMigrateTask(importFile);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping("/importVinToDealerClueMigrateTask")
    public ImportResultDto<VinToDealerTaskImportDTO> importVinToDealerClueMigrateTask(@RequestParam(value = "file") MultipartFile importFile) throws Exception {
        return clueMigrateTaskService.importVinToDealerClueMigrateTask(importFile);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping( "/getDealerInfoByDealerCode" )
    CompanyDetailDTO getDealerInfoByDealerCode( @RequestParam String dealerCode ) {
        return clueMigrateTaskService.getCompanyInfoByDealerCode(  dealerCode );
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<String>", name = "dealerCodeList", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping( "/getCompanyListInfoByDealerCodeList" )
    List<CompanyDetailDTO> getCompanyListInfoByDealerCodeList(@RequestBody List<String> dealerCodeList ) {
        return clueMigrateTaskService.getCompanyInfoListByDealerCodeList(  dealerCodeList );
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "migrationType", value = "")
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping( "/leadsTransfer/interf" )
    @ResponseBody
    public IPage<DealerMigrationRecordPo> leadsTransfer(@RequestParam("currentPage")int currentPage,
                                                 @RequestParam("pageSize")int pageSize
            ,@RequestParam(value = "migrationType",required = false)Integer migrationType) {
        return clueMigrateTaskService.leadsTransfer(currentPage,pageSize, migrationType);
    }

    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping( "/cleanDealerToDealerTask/interf" )
    @ResponseBody
    public void cleanDealerToDealerTask() {
        clueMigrateTaskService.cleanDealerToDealerTask();
    }

    /**
     * 根据id删除对象
     *
     * @param id 需要修改数据的ID
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
    })
    @ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "GET")
    @GetMapping(value = "/deleteById/{id}")
    @ResponseStatus(HttpStatus.CREATED)
    public boolean deleteById(@PathVariable("id") Long id) {
        return clueMigrateTaskService.deleteById(id);
    }

}
