package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyAuditInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2020-06-24
 */
@Api(value = "/goodwillApplyAuditInfo", tags = {"GoodwillApplyAuditInfoController"})
@RestController
@RequestMapping("/goodwillApplyAuditInfo")
public class GoodwillApplyAuditInfoController {

	@Autowired
	GoodwillApplyAuditInfoService goodwillApplyAuditInfoService;

	/**
	 * 分页查询数据
	 *
	 * @param appId
	 *            系统ID
	 * @param ownerCode
	 *            所有者代码
	 * @param ownerParCode
	 *            所有者的父组织代码
	 * @param orgId
	 *            组织ID
	 * @param id
	 *            主键id
	 * @param goodwillApplyId
	 *            亲善单id
	 * @param isAudit
	 *            是否审计
	 * @param auditResult
	 *            审计结果
	 * @param noticeDealerTime
	 *            通知经销商时间
	 * @param isValid
	 *            是否有效
	 * @param isDeleted
	 *            是否删除:1,删除；0,未删除
	 * @param createdAt
	 *            创建时间
	 * @param updatedAt
	 *            修改时间
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-06-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isAudit", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditResult", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditWay", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeDealerTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
	@GetMapping
	public IPage<GoodwillApplyAuditInfoDTO> getByPage(@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "goodwillApplyId", required = false) Long goodwillApplyId,
			@RequestParam(value = "isAudit", required = false) Integer isAudit,
			@RequestParam(value = "auditResult", required = false) Integer auditResult,
			@RequestParam(value = "auditWay", required = false) String auditWay,
			@RequestParam(value = "noticeDealerTime", required = false) String noticeDealerTime,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyAuditInfoPO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO = new GoodwillApplyAuditInfoDTO();
		goodwillApplyAuditInfoDTO.setAppId(appId);
		goodwillApplyAuditInfoDTO.setOwnerCode(ownerCode);
		goodwillApplyAuditInfoDTO.setOwnerParCode(ownerParCode);
		goodwillApplyAuditInfoDTO.setOrgId(orgId);
		goodwillApplyAuditInfoDTO.setId(id);
		goodwillApplyAuditInfoDTO.setGoodwillApplyId(goodwillApplyId);
		goodwillApplyAuditInfoDTO.setIsAudit(isAudit);
		goodwillApplyAuditInfoDTO.setAuditResult(auditResult);
		goodwillApplyAuditInfoDTO.setAuditWay(auditWay);
		goodwillApplyAuditInfoDTO.setIsValid(isValid);
		goodwillApplyAuditInfoDTO.setIsDeleted(isDeleted);
		if (noticeDealerTime != null) {
			goodwillApplyAuditInfoDTO.setNoticeDealerTime(sdf.parse(noticeDealerTime));
		}
		if (createdAt != null) {
			goodwillApplyAuditInfoDTO.setCreatedAt(sdf.parse(createdAt));
		}
		if (updatedAt != null) {
			goodwillApplyAuditInfoDTO.setUpdatedAt(sdf.parse(updatedAt));
		}
		return goodwillApplyAuditInfoService.selectPageBysql(page, goodwillApplyAuditInfoDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id
	 *            数据主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditInfoDTO
	 * <AUTHOR>
	 * @since 2020-06-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
	@GetMapping(value = "/{id}")
	public GoodwillApplyAuditInfoDTO getById(@PathVariable("id") Long id) {
		return goodwillApplyAuditInfoService.getById(id);
	}

	/**
	 * 进行数据新增
	 *
	 * @param goodwillApplyAuditInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-06-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyAuditInfoDTO", name = "goodwillApplyAuditInfoDTO", value = "", required = true)
	})
	@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
	@PostMapping
	@ResponseStatus(HttpStatus.CREATED)
	public int insert(@RequestBody GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO) {
		return goodwillApplyAuditInfoService.insert(goodwillApplyAuditInfoDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id
	 *            需要修改数据的ID
	 * @param goodwillApplyAuditInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-06-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true),
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyAuditInfoDTO", name = "goodwillApplyAuditInfoDTO", value = "", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
	@PutMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.CREATED)
	public int update(@PathVariable("id") Long id, @RequestBody GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO) {
		return goodwillApplyAuditInfoService.update(id, goodwillApplyAuditInfoDTO);
	}

	/**
	 * 根据id删除对象
	 *
	 * @param id
	 *            需要修改数据的ID
	 * <AUTHOR>
	 * @since 2020-06-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
	})
	@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteById(@PathVariable("id") Long id) {
		goodwillApplyAuditInfoService.deleteById(id);
		return true;
	}

	/**
	 * 根据IDs批量删除对象
	 *
	 * @param ids
	 *            需要修改数据的ID集合
	 * <AUTHOR>
	 * @since 2020-06-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "", required = true)
	})
	@ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/batch/{ids}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteByIds(@PathVariable("ids") String ids) {
		goodwillApplyAuditInfoService.deleteBatchIds(ids);
		return true;
	}

	/**
	 * 保存审计信息
	 *
	 * @param GoodwillApplyAuditInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyAuditInfoDTO", name = "goodwillApplyAuditInfoDto", value = "", required = true)
	})
	@ApiOperation(value = "保存审计信息", notes = "保存审计信息", httpMethod = "POST")
	@PostMapping(value = "/auditApplyGoodwillInfo")
	@ResponseStatus(HttpStatus.CREATED)
	public int auditApplyGoodwillInfo(@RequestBody GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDto) {
		return goodwillApplyAuditInfoService.auditApplyGoodwillInfo(goodwillApplyAuditInfoDto);
		// return goodwillNoticeInvoiceInfoService.insert(goodwillNoticeInvoiceInfoDto);
	}

	/**
	 * 根据主单ID查询明细
	 *
	 * @param id
	 *            数据主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditDetailDTO
	 * <AUTHOR>
	 * @since 2020-06-24
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "auditId", value = "", required = true)
	})
	@ApiOperation(value = "根据主单ID查询明细", notes = "根据主单ID查询明细", httpMethod = "GET")
	@GetMapping(value = "/queryAuditDetailInfo/{auditId}")
	public List<GoodwillApplyAuditDetailDTO> queryAuditDetailInfo(@PathVariable("auditId") Long auditId) {
		return goodwillApplyAuditInfoService.queryAuditDetailInfo(auditId);
	}

}