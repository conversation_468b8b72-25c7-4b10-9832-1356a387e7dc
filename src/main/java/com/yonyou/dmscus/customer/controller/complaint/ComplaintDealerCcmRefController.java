package com.yonyou.dmscus.customer.controller.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintDealerCcmRefDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintDealerCcmRefPO;
import com.yonyou.dmscus.customer.entity.po.complaint.TeComplaintDealerCcmRefImportPO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintDealerCcmRefService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;


/**
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
@Api(value = "/complaintDealerCcmRef", tags = {"ComplaintDealerCcmRefController"})
@RestController
@RequestMapping("/complaintDealerCcmRef")
                public class ComplaintDealerCcmRefController extends BaseController {
    
        @Autowired
        ComplaintDealerCcmRefService complaintDealerCcmRefService;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码 
        * @param ownerParCode 所有者的父组织代码 
        * @param orgId 组织ID
        * @param id 主键ID
        * @param region 区域
        * @param regionManager 区域经理
        * @param bloc 集团
        * @param dealerCode 进销商代码
        * @param dealerName 进销商名称
        * @param ccmMan CCM负责人
        * @param ccmManId CCM负责人ID
        * @param dataSources 数据来源 
        * @param isDeleted 是否删除
        * @param isValid 是否有效 
        * @param createdAt 创建时间
        * @param updatedAt 更新时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @since 2020-04-22
        */
                @ApiImplicitParams({
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "region", value = "区域"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "regionManager", value = "区域经理"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "bloc", value = "集团"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "进销商代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerName", value = "进销商名称"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ccmMan", value = "CCM负责人"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ccmManId", value = "CCM负责人ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = "数据来源"),
                        @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "createdAt", value = "创建时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "updatedAt", value = "更新时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
                })
                @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
                @GetMapping
        public IPage<ComplaintDealerCcmRefDTO>getByPage(
@RequestParam(value="appId",required = false) String appId,
@RequestParam(value="ownerCode",required = false) String ownerCode,
@RequestParam(value="ownerParCode",required = false) String ownerParCode,
@RequestParam(value="orgId",required = false) Integer orgId,
@RequestParam(value="id",required = false) Long id,
@RequestParam(value="region",required = false) String region,
@RequestParam(value="regionManager",required = false) String regionManager,
@RequestParam(value="bloc",required = false) String bloc,
@RequestParam(value="dealerCode",required = false) String dealerCode,
@RequestParam(value="dealerName",required = false) String dealerName,
@RequestParam(value="ccmMan",required = false) String ccmMan,
@RequestParam(value="ccmManId",required = false) String ccmManId,
@RequestParam(value="dataSources",required = false) Integer dataSources,
@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
@RequestParam(value="isValid",required = false) Integer isValid,
@RequestParam(value="createdAt",required = false) Date createdAt,
@RequestParam(value="updatedAt",required = false) Date updatedAt,
        @RequestParam("currentPage")int currentPage,
        @RequestParam("pageSize")int pageSize){
        Page<ComplaintDealerCcmRefPO>page=new Page(currentPage,pageSize);

        ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO =new ComplaintDealerCcmRefDTO();
                                            complaintDealerCcmRefDTO.setAppId(appId);
                                            complaintDealerCcmRefDTO.setOwnerCode(ownerCode);
                                            complaintDealerCcmRefDTO.setOwnerParCode(ownerParCode);
                                            complaintDealerCcmRefDTO.setOrgId(orgId);
                                            complaintDealerCcmRefDTO.setId(id);
                                            complaintDealerCcmRefDTO.setRegion(region);
                                            complaintDealerCcmRefDTO.setRegionManager(regionManager);
                                            complaintDealerCcmRefDTO.setBloc(bloc);
                                            complaintDealerCcmRefDTO.setDealerCode(dealerCode);
                                            complaintDealerCcmRefDTO.setDealerName(dealerName);
                                            complaintDealerCcmRefDTO.setCcmMan(ccmMan);
                                            complaintDealerCcmRefDTO.setCcmManId(ccmManId);
                                            complaintDealerCcmRefDTO.setDataSources(dataSources);
                                            complaintDealerCcmRefDTO.setIsDeleted(isDeleted);
                                            complaintDealerCcmRefDTO.setIsValid(isValid);
                                            complaintDealerCcmRefDTO.setCreatedAt(createdAt);
                                            complaintDealerCcmRefDTO.setUpdatedAt(updatedAt);
                return complaintDealerCcmRefService.selectPageBysql(page,complaintDealerCcmRefDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintDealerCcmRefDTO
 * <AUTHOR>
 * @since 2020-04-22
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public ComplaintDealerCcmRefDTO getById(@PathVariable("id") Long id){
        return complaintDealerCcmRefService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param complaintDealerCcmRefDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-22
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintDealerCcmRefDTO", name = "complaintDealerCcmRefDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO){
        return complaintDealerCcmRefService.insert( complaintDealerCcmRefDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param complaintDealerCcmRefDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-22
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintDealerCcmRefDTO", name = "complaintDealerCcmRefDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO){
        return complaintDealerCcmRefService.update(id,complaintDealerCcmRefDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-04-22
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    complaintDealerCcmRefService.deleteById(id);
        return true;
        }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @RequestMapping(value = "/selectCcmDealer", method = RequestMethod.GET)
    public IPage<ComplaintDealerCcmRefDTO>selectCcmDealer(
            @RequestParam("currentPage")int currentPage,
            @RequestParam("pageSize")int pageSize){
        Page<ComplaintDealerCcmRefPO>page=new Page(currentPage,pageSize);

        ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO =new ComplaintDealerCcmRefDTO();
        complaintDealerCcmRefDTO.setIsDeleted(false);
        return complaintDealerCcmRefService.selectPageBysql(page,complaintDealerCcmRefDTO);
    }

    /**
     *客诉数据设置（厂端）编辑CCM和经销商对应关系
     * @param complaintDealerCcmRefDTO
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintDealerCcmRefDTO", name = "complaintDealerCcmRefDTO", value = "", required = true)
    })
    @ApiOperation(value = "客诉数据设置（厂端）编辑CCM和经销商对应关系", notes = "客诉数据设置（厂端）编辑CCM和经销商对应关系", httpMethod = "POST")
    @RequestMapping(value = "/updateCCM", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int updateCcm(@RequestBody ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO){
        long id=complaintDealerCcmRefDTO.getId();
        return complaintDealerCcmRefService.update(id,complaintDealerCcmRefDTO);
    }
    /**
     * 预导入临时表
     *
     * @param importFile
     * @return
     * @throws Exception
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "", required = true)
    })
    @ApiOperation(value = "预导入工具临时表", notes = "预导入临时表", httpMethod = "POST")
    @RequestMapping(value = "/importTempTools", method = RequestMethod.POST)
    @ResponseBody
    public ImportTempResult<TeComplaintDealerCcmRefImportPO> importTempTools(
            @RequestParam(value = "file") MultipartFile importFile) throws Exception {
        return complaintDealerCcmRefService.importTempTools(importFile);
    }
    /**
     * 临时表数据保存到正式表
     *
     * @param
     * @return
     * <AUTHOR>
     * @throws Exception
     * @since 2020-04-30
     */
    @ApiOperation(value = "临时表数据保存到正式表", notes = "临时表数据保存到正式表", httpMethod = "GET")
    @GetMapping(value = "importTools")
    public int importTools() throws Exception {

        return complaintDealerCcmRefService.importTools();
    }
    /**
     *客诉数据设置（厂端）批量编辑CCM和经销商对应关系
     * @param complaintDealerCcmRefDTO
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintDealerCcmRefDTO", name = "complaintDealerCcmRefDTO", value = "", required = true)
    })
    @ApiOperation(value = "客诉数据设置（厂端）批量编辑CCM和经销商对应关系", notes = "客诉数据设置（厂端）批量编辑CCM和经销商对应关系", httpMethod = "POST")
    @RequestMapping(value = "/updateCCMAll", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public boolean updateCcmAll(@RequestBody ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO){
        complaintDealerCcmRefDTO.setId(FrameworkUtil.getLoginInfo().getUserId());
        return complaintDealerCcmRefService.updateCcmAll(complaintDealerCcmRefDTO);
    }
    /**
     * 查询CCM与经销商的关系
     */
    @ApiOperation(value = "查询CCM与经销商的关系", notes = "查询CCM与经销商的关系", httpMethod = "GET")
    @GetMapping(value = "/selectCCM")
    public List<ComplaintDealerCcmRefDTO> selectCCM(){
        ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO=new ComplaintDealerCcmRefDTO();
        return complaintDealerCcmRefService.selectListBySql(complaintDealerCcmRefDTO);
    }



}