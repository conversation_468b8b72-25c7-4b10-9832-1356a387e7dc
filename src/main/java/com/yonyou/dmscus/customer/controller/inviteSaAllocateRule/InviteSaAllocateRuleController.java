package com.yonyou.dmscus.customer.controller.inviteSaAllocateRule;

import com.yonyou.dmscus.customer.dto.InvitationRuleVcdcParamsVo;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.UserInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteSaAllocateRule.InviteSaAllocateRuleDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteSaAllocateRule.InviteSaAllocateRuleDetailDTO;
import com.yonyou.dmscus.customer.service.inviteSaAllocateRule.InviteSaAllocateRuleDetailService;
import com.yonyou.dmscus.customer.service.inviteSaAllocateRule.InviteSaAllocateRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-04-18
 */
@Api(value = "/inviteSaAllocateRule", tags = {"InviteSaAllocateRuleController"})
@RestController
@RequestMapping("/inviteSaAllocateRule")
public class InviteSaAllocateRuleController {

    @Autowired
    InviteSaAllocateRuleService inviteSaAllocateRuleService;
    @Autowired
    InviteSaAllocateRuleDetailService inviteSaAllocateRuleDetailService;

    /**
     * 查询数据
     * @param vo
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InvitationRuleVcdcParamsVo", name = "vo", value = "", required = true)
    })
    @ApiOperation(value = "查询数据", notes = "查询数据", httpMethod = "POST")
    @PostMapping("/getInviteSaAllocateRule")
    public List<InviteSaAllocateRuleDTO> getInviteSaAllocateRule(@RequestBody InvitationRuleVcdcParamsVo vo) {
        return inviteSaAllocateRuleService.getInviteSaAllocateRule(vo);
    }


    /**
     * 查询数据
     * @return
     */
    @ApiOperation(value = "查询数据", notes = "查询数据", httpMethod = "GET")
    @GetMapping("/getInviteSaAllocateRuleDlr")
    public InviteSaAllocateRuleDTO getInviteSaAllocateRuleDlr() {
        return inviteSaAllocateRuleService.getInviteSaAllocateRuleDlr();
    }



    /**
     * 保存分配规则
     * @param inviteSaAllocateRuleDTO
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteSaAllocateRuleDTO", name = "inviteSaAllocateRuleDTO", value = "", required = true)
    })
    @ApiOperation(value = "保存分配规则", notes = "保存分配规则", httpMethod = "POST")
    @PostMapping("/saveInviteSaAllocateRule")
    @ResponseStatus(HttpStatus.CREATED)
    public int saveInviteSaAllocateRule(@RequestBody InviteSaAllocateRuleDTO inviteSaAllocateRuleDTO) {
        return inviteSaAllocateRuleService.saveInviteSaAllocateRule(inviteSaAllocateRuleDTO);
    }

    /**
     * 保存分配规则人员明细
     * @param list
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<InviteSaAllocateRuleDetailDTO>", name = "list", value = "", required = true)
    })
    @ApiOperation(value = "保存分配规则人员明细", notes = "保存分配规则人员明细", httpMethod = "POST")
    @PostMapping("/saveSaAllocateRuleDetail")
    public int saveSaAllocateRuleDetail(@RequestBody List<InviteSaAllocateRuleDetailDTO> list) {
        return inviteSaAllocateRuleDetailService.saveSaAllocateRuleDetail(list);
    }

    /**
     *查询分配规则人员明细
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "")
    })
    @ApiOperation(value = "查询分配规则人员明细", notes = "查询分配规则人员明细", httpMethod = "GET")
    @GetMapping("/getSaAllocateRuleDetail")
    public List<InviteSaAllocateRuleDetailDTO> getSaAllocateRule(@RequestParam(value = "dealerCode", required =
            false) String dealerCode){
        return inviteSaAllocateRuleDetailService.getSaAllocateRule(dealerCode);
    }

    /**
     * 查询用户信息
     * @return
     */
    @ApiOperation(value = "查询用户信息", notes = "查询用户信息", httpMethod = "GET")
    @GetMapping("/getUserInfo")
    public List<UserInfoDTO> getUserInfo(){
        return inviteSaAllocateRuleService.getUserInfo();
    }

}