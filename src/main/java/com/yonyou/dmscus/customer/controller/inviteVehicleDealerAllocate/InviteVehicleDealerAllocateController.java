package com.yonyou.dmscus.customer.controller.inviteVehicleDealerAllocate;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.common.AjaxResponse;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.CompanyDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.OwnerVehicleVO;
import com.yonyou.dmscus.customer.entity.dto.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryDTO;
import com.yonyou.dmscus.customer.entity.po.inviteVehicleDealerAllocate.InviteVehicleDealerAllocatePO;
import com.yonyou.dmscus.customer.service.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryService;
import com.yonyou.dmscus.customer.service.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-04-20
 */
@Api(value = "/inviteVehicleDealerAllocate", tags = {"InviteVehicleDealerAllocateController"})
@RestController
@RequestMapping("/inviteVehicleDealerAllocate")
public class InviteVehicleDealerAllocateController {

    @Autowired
    InviteVehicleDealerAllocateService inviteVehicleDealerAllocateService;
    @Autowired
    InviteVehicleDealerAllocateHistoryService inviteVehicleDealerAllocateHistoryService;

    /**
     * 分页查询数据
     *
     * @param appId        系统ID
     * @param ownerCode    所有者代码
     * @param ownerParCode 所有者的父组织代码
     * @param orgId        组织ID
     * @param id           主键ID
     * @param vin          车架号
     * @param dealerName   经销商名称
     * @param dealerCode   经销商代码
     * @param isInvited    是否邀约
     * @param dataSources  数据来源
     * @param isDeleted    是否删除
     * @param isValid      是否有效
     * @param createdAt    创建时间
     * @param updatedAt    更新时间
     * @param currentPage  页数
     * @param pageSize     分页大小
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-20
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "车架号"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerName", value = "经销商名称"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "经销商代码"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isInvited", value = "是否邀约"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = "数据来源"),
            @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "createdAt", value = "创建时间"),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "updatedAt", value = "更新时间"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
    })
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
    @GetMapping
    public IPage<InviteVehicleDealerAllocateDTO> getByPage(
            @RequestParam(value = "appId", required = false) String appId,
            @RequestParam(value = "ownerCode", required = false) String ownerCode,
            @RequestParam(value = "ownerParCode", required = false) String ownerParCode,
            @RequestParam(value = "orgId", required = false) Integer orgId,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "vin", required = false) String vin,
            @RequestParam(value = "dealerName", required = false) String dealerName,
            @RequestParam(value = "dealerCode", required = false) String dealerCode,
            @RequestParam(value = "isInvited", required = false) Integer isInvited,
            @RequestParam(value = "dataSources", required = false) Integer dataSources,
            @RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
            @RequestParam(value = "isValid", required = false) Integer isValid,
            @RequestParam(value = "createdAt", required = false) Date createdAt,
            @RequestParam(value = "updatedAt", required = false) Date updatedAt,
            @RequestParam("currentPage") int currentPage,
            @RequestParam("pageSize") int pageSize) {
        Page<InviteVehicleDealerAllocatePO> page = new Page(currentPage, pageSize);

        InviteVehicleDealerAllocateDTO inviteVehicleDealerAllocateDTO = new InviteVehicleDealerAllocateDTO();
        inviteVehicleDealerAllocateDTO.setAppId(appId);
        inviteVehicleDealerAllocateDTO.setOwnerCode(ownerCode);
        inviteVehicleDealerAllocateDTO.setOwnerParCode(ownerParCode);
        inviteVehicleDealerAllocateDTO.setOrgId(orgId);
        inviteVehicleDealerAllocateDTO.setId(id);
        inviteVehicleDealerAllocateDTO.setVin(vin);
        inviteVehicleDealerAllocateDTO.setDealerName(dealerName);
        inviteVehicleDealerAllocateDTO.setDealerCode(dealerCode);
        inviteVehicleDealerAllocateDTO.setIsInvited(isInvited);
        inviteVehicleDealerAllocateDTO.setDataSources(dataSources);
        inviteVehicleDealerAllocateDTO.setIsDeleted(isDeleted);
        inviteVehicleDealerAllocateDTO.setIsValid(isValid);
        inviteVehicleDealerAllocateDTO.setCreatedAt(createdAt);
        inviteVehicleDealerAllocateDTO.setUpdatedAt(updatedAt);
        return inviteVehicleDealerAllocateService.selectPageBysql(page, inviteVehicleDealerAllocateDTO);
    }


    /**
     * 查询分配历史
     *
     * @param vin 车架号
     * @return int
     * <AUTHOR>
     * @since 2020-04-20
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "车架号", required = true)
    })
    @ApiOperation(value = "查询分配历史", notes = "查询分配历史", httpMethod = "GET")
    @GetMapping("/getAllocationHistory")
    public List<InviteVehicleDealerAllocateHistoryDTO> getAllocationHistory(@RequestParam(value = "vin") String vin) {
        return inviteVehicleDealerAllocateHistoryService.getAllocationHistory(vin);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "name", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerName", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/getVehicle")
    public IPage<OwnerVehicleVO> getVehicle(@RequestParam(value = "plateNumber", required = false) String license,
                                            @RequestParam(value = "vin", required = false) String vin,
                                            @RequestParam(value = "name", required = false) String name,
                                            @RequestParam(value = "dealerName", required = false) String dealerName,
                                            @RequestParam(value = "dealerCode", required = false) String dealerCode,
                                            @RequestParam("currentPage") Long currentPage,
                                            @RequestParam("pageSize") Long pageSize){
        return inviteVehicleDealerAllocateService.getVehicle(license,vin,name,dealerName,dealerCode,currentPage,pageSize);

    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "")
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/queryDealer")
    public List<CompanyDetailDTO> queryDealer(@RequestParam(value = "dealerCode", required = false) String dealerCode){
        return inviteVehicleDealerAllocateService.queryDealer(dealerCode);

    }

    /**
     * 重新分配经销商，并记录分配历史
     * @param dto
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteVehicleDealerAllocateDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "重新分配经销商，并记录分配历史", notes = "重新分配经销商，并记录分配历史", httpMethod = "POST")
    @PostMapping("/allocation")
    public int allocation(@RequestBody InviteVehicleDealerAllocateDTO dto){
        return inviteVehicleDealerAllocateService.allocation(dto);
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping("/importUpload")
    public AjaxResponse importUpload(@RequestParam(value = "file") MultipartFile importFile) {
        return inviteVehicleDealerAllocateService.importUpload(importFile);
    }




}