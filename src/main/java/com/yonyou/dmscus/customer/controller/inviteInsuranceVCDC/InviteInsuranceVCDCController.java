package com.yonyou.dmscus.customer.controller.inviteInsuranceVCDC;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.service.excel.ExcelGenerator;
import com.yonyou.dmscus.customer.dto.InsuranceFollowParamsDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordPO;
import com.yonyou.dmscus.customer.service.inviteInsuranceVCDC.InviteInsuranceVCDCSOssService;
import com.yonyou.dmscus.customer.service.inviteInsuranceVCDC.InviteInsuranceVCDCService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 续保线索管理（VCDC）
 * <AUTHOR>
 * @date 2021/3/9 0009
 */
@Api(value = "/inviteInsuranceVCDC", tags = {"续保线索管理（VCDC）"})
@RestController
@RequestMapping("/inviteInsuranceVCDC")
public class InviteInsuranceVCDCController {

    @Resource
    InviteInsuranceVCDCService inviteInsuranceVCDCService;

    @Resource
    InviteInsuranceVCDCSOssService inviteInsuranceVCDCSOssService;

    @Resource
    ExcelGenerator excelGenerator;
    /**
     * 分页查询
     * @param dto
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InsuranceFollowParamsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "分页查询", notes = "分页查询", httpMethod = "POST")
    @PostMapping("/page")
    public IPage<InviteInsuranceVehicleRecordDTO> selectInsuranceVCDCPage(@RequestBody InsuranceFollowParamsDTO dto){
        Page<InviteInsuranceVehicleRecordPO> page = new Page(dto.getCurrentPage(), dto.getPageSize());
        InviteInsuranceVehicleRecordDTO insuranceVehicleRecordDTO= setSelectDto(dto);
        return inviteInsuranceVCDCService.selectInsuranceVCDCPage(page,insuranceVehicleRecordDTO);
    }


    /**
     * 根据VIN查询车辆基本信息
     * @param vin
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "根据VIN查询车辆基本信息", notes = "根据VIN查询车辆基本信息", httpMethod = "GET")
    @GetMapping("/getVehicleByVin")
    public Map<String,Object> selectVehicleByVin(@RequestParam(value = "vin") String vin){
        return inviteInsuranceVCDCService.selectVehicleByVin(vin);
    }

    /**
     * 导出
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InsuranceFollowParamsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "导出", notes = "导出", httpMethod = "POST")
    @PostMapping("/exportExcel")
    public void exportExcel(@RequestBody InsuranceFollowParamsDTO dto){
        /*List<ExcelExportColumn> exportColumnList = new ArrayList<ExcelExportColumn>();
        InviteInsuranceVehicleRecordDTO insuranceVehicleRecordDTO= setSelectDto(dto);
        List<Map> data=inviteInsuranceVCDCService.exportExcel(insuranceVehicleRecordDTO);

        exportColumnList.add(new ExcelExportColumn("vin", "VIN"));
        exportColumnList.add(new ExcelExportColumn("licensePlateNum", "车牌号"));
        exportColumnList.add(new ExcelExportColumn("clueType", "线索类型"));
        exportColumnList.add(new ExcelExportColumn("insuranceType", "续保客户类型"));
        exportColumnList.add(new ExcelExportColumn("followStatusName", "跟进状态"));
        exportColumnList.add(new ExcelExportColumn("total_score", "AI得分"));
        exportColumnList.add(new ExcelExportColumn("start_time", "通话时间"));
        exportColumnList.add(new ExcelExportColumn("call_length", "通话时长(秒)"));
        exportColumnList.add(new ExcelExportColumn("adviseInDate", "续保到期日期","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("planFollowDate", "计划跟进日期","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("actualFollowDate", "实际跟进日期","yyyy-MM-dd"));
        exportColumnList.add(new ExcelExportColumn("saName", "跟进人员"));
        exportColumnList.add(new ExcelExportColumn("orderStatus", "线索完成状态"));
        exportColumnList.add(new ExcelExportColumn("dealerCode", "经销商代码"));
        exportColumnList.add(new ExcelExportColumn("createdAt", "邀约创建日期"));
        exportColumnList.add(new ExcelExportColumn("content", "跟进内容"));
        exportColumnList.add(new ExcelExportColumn("loseReason", "失败原因"));

        Map<String, List<Map>> excelData = new HashMap<String, List<Map>>();
        excelData.put("线索管理", data);
        Map<String, List<ExcelExportColumn>> ColumnMap = new HashMap<String, List<ExcelExportColumn>>(16);
        ColumnMap.put("线索管理", exportColumnList);
        excelGenerator.generateExcelSheet(excelData, ColumnMap, "线索管理导出.xls", request, response);*/
        InviteInsuranceVehicleRecordDTO insuranceVehicleRecordDTO= setSelectDto(dto);
        inviteInsuranceVCDCSOssService.exportExcelOss(insuranceVehicleRecordDTO);

    }

    /**
     * 导出
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InsuranceFollowParamsDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "导出", notes = "导出", httpMethod = "POST")
    @PostMapping("/exportExcel/oss")
    public List<Map> exportExcelOss(@RequestBody InsuranceFollowParamsDTO dto){
        InviteInsuranceVehicleRecordDTO insuranceVehicleRecordDTO= setSelectDto(dto);
        return inviteInsuranceVCDCService.exportExcel(insuranceVehicleRecordDTO);
    }


    private InviteInsuranceVehicleRecordDTO setSelectDto(InsuranceFollowParamsDTO dto){
        InviteInsuranceVehicleRecordDTO insuranceVehicleRecordDTO = new InviteInsuranceVehicleRecordDTO();
        insuranceVehicleRecordDTO.setLargeAreaId(dto.getLargeAreaId()); //大区
        insuranceVehicleRecordDTO.setAreaId(dto.getAreaId()); //小区（区域经理）
        insuranceVehicleRecordDTO.setDealerCode(dto.getDealerCode()); //经销商代码
        insuranceVehicleRecordDTO.setLicensePlateNum(dto.getLicensePlateNum()); //车牌号
        insuranceVehicleRecordDTO.setAdviseInDateStart(dto.getAdviseInDateStart()); //续保到期日期
        insuranceVehicleRecordDTO.setAdviseInDateEnd(dto.getAdviseInDateEnd()); //续保到期日期(结束)
        insuranceVehicleRecordDTO.setVin(dto.getVin()); //vin
        insuranceVehicleRecordDTO.setActualFollowDateStart(dto.getActualFollowDateStart()); //实际跟进日期
        insuranceVehicleRecordDTO.setActualFollowDateEnd(dto.getActualFollowDateEnd());//实际跟进日期(结束)
        insuranceVehicleRecordDTO.setFollowStatusParam(dto.getFollowStatus()); //跟进状态
        insuranceVehicleRecordDTO.setOrderStatus(dto.getOrderStatus()); //线索完成状态
        insuranceVehicleRecordDTO.setName(dto.getName()); //客户姓名
       // insuranceVehicleRecordDTO.setSaId(dto.getSaId()); //跟进人员
//        insuranceVehicleRecordDTO.setInsuranceType(dto.getInsuranceType()); //续保客户类型
//        insuranceVehicleRecordDTO.setClueType(dto.getClueType());
        insuranceVehicleRecordDTO.setCreatedAtStart(dto.getInvitationDateStart()); //邀约创建日期
        insuranceVehicleRecordDTO.setCreatedAtEnd(dto.getCreatedAtEnd()); //邀约创建日期(结束)
        insuranceVehicleRecordDTO.setSaName(dto.getSaName()); //跟进人员
        return insuranceVehicleRecordDTO;
    }
}
