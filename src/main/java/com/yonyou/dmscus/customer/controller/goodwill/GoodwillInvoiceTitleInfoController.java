package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillInvoiceTitleInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillInvoiceTitleInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillInvoiceTitleInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;






                                                                    /**
 *
 * <AUTHOR>
 * @since 2020-04-17
 */
@Api(value = "/goodwillInvoiceTitleInfo", tags = {"GoodwillInvoiceTitleInfoController"})
@RestController
@RequestMapping("/goodwillInvoiceTitleInfo")
                public class GoodwillInvoiceTitleInfoController {
    
        @Autowired
        GoodwillInvoiceTitleInfoService goodwillInvoiceTitleInfoService;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码
        * @param ownerParCode 所有者的父组织代码
        * @param orgId 组织ID
        * @param id 主键id
        * @param invoiceTitle 开票抬头
        * @param name 名称
        * @param taxpayerIdentificationNumber 纳税人识别号
        * @param phone 电话
        * @param address 地址
        * @param openBank 开户行
        * @param account 账号
        * @param isValid 是否有效
        * @param isDeleted 是否删除:1,删除；0,未删除
        * @param createdAt 创建时间
        * @param updatedAt 修改时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @throws ParseException 
         * @since 2020-04-17
        */
        @ApiImplicitParams({
                @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
                @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
                @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
                @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
                @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键id"),
                @ApiImplicitParam(paramType = "query", dataType = "int", name = "invoiceTitle", value = "开票抬头"),
                @ApiImplicitParam(paramType = "query", dataType = "string", name = "name", value = "名称"),
                @ApiImplicitParam(paramType = "query", dataType = "string", name = "taxpayerIdentificationNumber", value = "纳税人识别号"),
                @ApiImplicitParam(paramType = "query", dataType = "string", name = "phone", value = "电话"),
                @ApiImplicitParam(paramType = "query", dataType = "string", name = "address", value = "地址"),
                @ApiImplicitParam(paramType = "query", dataType = "string", name = "openBank", value = "开户行"),
                @ApiImplicitParam(paramType = "query", dataType = "string", name = "account", value = "账号"),
                @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
                @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除:1,删除；0,未删除"),
                @ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = "创建时间"),
                @ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = "修改时间"),
                @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
                @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
        })
        @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
        @GetMapping
        public IPage<GoodwillInvoiceTitleInfoDTO>getByPage(
		@RequestParam(value="appId",required = false) String appId,
		@RequestParam(value="ownerCode",required = false) String ownerCode,
		@RequestParam(value="ownerParCode",required = false) String ownerParCode,
		@RequestParam(value="orgId",required = false) Integer orgId,
		@RequestParam(value="id",required = false) Long id,
		@RequestParam(value="invoiceTitle",required = false) Integer invoiceTitle,
		@RequestParam(value="name",required = false) String name,
		@RequestParam(value="taxpayerIdentificationNumber",required = false) String taxpayerIdentificationNumber,
		@RequestParam(value="phone",required = false) String phone,
		@RequestParam(value="address",required = false) String address,
		@RequestParam(value="openBank",required = false) String openBank,
		@RequestParam(value="account",required = false) String account,
		@RequestParam(value="isValid",required = false) Integer isValid,
		@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
		@RequestParam(value="createdAt",required = false) String createdAt,
		@RequestParam(value="updatedAt",required = false) String updatedAt,
        @RequestParam("currentPage")int currentPage,
        @RequestParam("pageSize")int pageSize) throws ParseException{
        Page<GoodwillInvoiceTitleInfoPO>page=new Page(currentPage,pageSize);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO =new GoodwillInvoiceTitleInfoDTO();
                goodwillInvoiceTitleInfoDTO.setAppId(appId);
                goodwillInvoiceTitleInfoDTO.setOwnerCode(ownerCode);
                goodwillInvoiceTitleInfoDTO.setOwnerParCode(ownerParCode);
                goodwillInvoiceTitleInfoDTO.setOrgId(orgId);
                goodwillInvoiceTitleInfoDTO.setId(id);
                goodwillInvoiceTitleInfoDTO.setInvoiceTitle(invoiceTitle);
                goodwillInvoiceTitleInfoDTO.setName(name);
                goodwillInvoiceTitleInfoDTO.setTaxpayerIdentificationNumber(taxpayerIdentificationNumber);
                goodwillInvoiceTitleInfoDTO.setPhone(phone);
                goodwillInvoiceTitleInfoDTO.setAddress(address);
                goodwillInvoiceTitleInfoDTO.setOpenBank(openBank);
                goodwillInvoiceTitleInfoDTO.setAccount(account);
                goodwillInvoiceTitleInfoDTO.setIsValid(isValid);
                goodwillInvoiceTitleInfoDTO.setIsDeleted(isDeleted);
                if(createdAt != null){
                	goodwillInvoiceTitleInfoDTO.setCreatedAt(sdf.parse(createdAt));
                }
                if(updatedAt != null){
                	goodwillInvoiceTitleInfoDTO.setUpdatedAt(sdf.parse(updatedAt));
                }
                
                return goodwillInvoiceTitleInfoService.selectPageBysql(page,goodwillInvoiceTitleInfoDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.GoodwillInvoiceTitleInfoDTO
 * <AUTHOR>
 * @since 2020-04-17
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public GoodwillInvoiceTitleInfoDTO getById(@PathVariable("id") Long id){
        return goodwillInvoiceTitleInfoService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param goodwillInvoiceTitleInfoDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-17
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "GoodwillInvoiceTitleInfoDTO", name = "goodwillInvoiceTitleInfoDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO){
        return goodwillInvoiceTitleInfoService.insert( goodwillInvoiceTitleInfoDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param goodwillInvoiceTitleInfoDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-17
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "GoodwillInvoiceTitleInfoDTO", name = "goodwillInvoiceTitleInfoDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO){
        return goodwillInvoiceTitleInfoService.update(id,goodwillInvoiceTitleInfoDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-04-17
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    goodwillInvoiceTitleInfoService.deleteById(id);
        return true;
        }

        /**
         * 根据IDs批量删除对象
         *
         * @param ids 需要修改数据的ID集合
         * <AUTHOR>
         * @since 2020-04-17
         */
        @ApiImplicitParams({
                @ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "需要修改数据的ID集合", required = true)
        })
        @ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
        @DeleteMapping(value = "/batch/{ids}")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        public boolean deleteByIds(@PathVariable("ids") String ids){
            goodwillInvoiceTitleInfoService.deleteBatchIds(ids);
                return true;
                }

}