package com.yonyou.dmscus.customer.controller.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ReasonsforFiveDto;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintNotCloseCaseReasonTestPO;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintNotCloseCaseReasonService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @since 2020-12-14
 */
@Api(value = "/saleComplaintNotCloseCaseReason", tags = {"SaleComplaintNotCloseCaseReasonController"})
@RestController
@RequestMapping("/saleComplaintNotCloseCaseReason")
public class SaleComplaintNotCloseCaseReasonController extends BaseController {

    @Autowired
    SaleComplaintNotCloseCaseReasonService saleComplaintNotCloseCaseReasonService;

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "complaintInfoId", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @RequestMapping(value = "/selectReasonsforFive", method = RequestMethod.GET)
    public IPage<ComplaintNotCloseCaseReasonTestDTO> selectReasonsforFive(
            @RequestParam(value = "complaintInfoId", required = false) Long complaintInfoId,
            @RequestParam("currentPage") int currentPage,
            @RequestParam("pageSize") int pageSize) {
        Page<ComplaintNotCloseCaseReasonTestPO> page = new Page(currentPage, pageSize);
        ComplaintNotCloseCaseReasonTestDTO complaintNotCloseCaseReasonTestDTO = new ComplaintNotCloseCaseReasonTestDTO();
        complaintNotCloseCaseReasonTestDTO.setComplaintInfoId(complaintInfoId);
        return saleComplaintNotCloseCaseReasonService.selectPageBysql3(page, complaintNotCloseCaseReasonTestDTO);
    }

    /**
     * 新增5日未结案原因（店端）
     *
     * @param reasonsforFiveDto 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ReasonsforFiveDto", name = "reasonsforFiveDto", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "新增5日未结案原因（店端）", notes = "新增5日未结案原因（店端）", httpMethod = "POST")
    @RequestMapping(value = "/insertReasonsforFive", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertReasonsforFive(@RequestBody ReasonsforFiveDto reasonsforFiveDto){
        return saleComplaintNotCloseCaseReasonService.insertReasonsforFive(reasonsforFiveDto);
    }


}