package com.yonyou.dmscus.customer.controller.productdeliverer;

import com.yonyou.dmscus.customer.entity.dto.productdeliverer.ProductDelivererDTO;
import com.yonyou.dmscus.customer.service.productdeliverer.ProductDelivererService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @since 2020-11-02
 */
@Api(value = "/productDeliverer", tags = {"ProductDelivererController"})
@RestController
@RequestMapping("/productDeliverer")
public class ProductDelivererController {

    @Autowired
    ProductDelivererService productDelivererService;

    /**
     *查询上次工单送修人信息
     * @param vin
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "查询上次工单送修人信息", notes = "查询上次工单送修人信息", httpMethod = "GET")
    @GetMapping("/queryProductDeliverer")
    public ProductDelivererDTO queryProductDeliverer(@RequestParam(value = "vin") String vin){
        return productDelivererService.queryProductDeliverer(vin);
    }


}