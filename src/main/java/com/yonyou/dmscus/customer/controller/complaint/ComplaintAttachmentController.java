package com.yonyou.dmscus.customer.controller.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.AttachmentDto;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfoDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAttachmentPO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoPO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintAttachmentService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


/**
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Api(value = "/complaintAttachment", tags = {"ComplaintAttachmentController"})
@RestController
@RequestMapping("/complaintAttachment")
                public class ComplaintAttachmentController extends BaseController {
    
        @Autowired
        ComplaintAttachmentService complaintAttachmentService;
    @Resource
    ComplaintInfoMapper complaintInfoMapper;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码 
        * @param ownerParCode 所有者的父组织代码 
        * @param orgId 组织ID
        * @param id 主键ID
        * @param complaintInfoId 投诉信息表主键ID
        * @param object 跟进对象 区域经理、经销商、CCM、客服中心
        * @param attachmentName 附件名称
        * @param url 附件存放地址
        * @param size 附件大小
        * @param dataSources 数据来源 
        * @param isDeleted 是否删除
        * @param isValid 是否有效 
        * @param createdAt 创建时间
        * @param updatedAt 更新时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @since 2020-04-15
        */
                @ApiImplicitParams({
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "complaintInfoId", value = "投诉信息表主键ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "object", value = "跟进对象 区域经理、经销商、CCM、客服中心"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "attachmentName", value = "附件名称"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "url", value = "附件存放地址"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "size", value = "附件大小"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = "数据来源"),
                        @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "createdAt", value = "创建时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "updatedAt", value = "更新时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
                })
                @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
                @GetMapping
        public IPage<ComplaintAttachmentDTO>getByPage(
@RequestParam(value="appId",required = false) String appId,
@RequestParam(value="ownerCode",required = false) String ownerCode,
@RequestParam(value="ownerParCode",required = false) String ownerParCode,
@RequestParam(value="orgId",required = false) Integer orgId,
@RequestParam(value="id",required = false) Long id,
@RequestParam(value="complaintInfoId",required = false) Long complaintInfoId,
@RequestParam(value="object",required = false) String object,
@RequestParam(value="attachmentName",required = false) String attachmentName,
@RequestParam(value="url",required = false) String url,
@RequestParam(value="size",required = false) Integer size,
@RequestParam(value="dataSources",required = false) Integer dataSources,
@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
@RequestParam(value="isValid",required = false) Integer isValid,
@RequestParam(value="createdAt",required = false) Date createdAt,
@RequestParam(value="updatedAt",required = false) Date updatedAt,
        @RequestParam("currentPage")int currentPage,
        @RequestParam("pageSize")int pageSize){
        Page<ComplaintAttachmentPO>page=new Page(currentPage,pageSize);

        ComplaintAttachmentDTO complaintAttachmentDTO =new ComplaintAttachmentDTO();
                                            complaintAttachmentDTO.setAppId(appId);
                                            complaintAttachmentDTO.setOwnerCode(ownerCode);
                                            complaintAttachmentDTO.setOwnerParCode(ownerParCode);
                                            complaintAttachmentDTO.setOrgId(orgId);
                                            complaintAttachmentDTO.setId(id);
                                            complaintAttachmentDTO.setComplaintInfoId(complaintInfoId);
                                            complaintAttachmentDTO.setObject(object);
                                            complaintAttachmentDTO.setAttachmentName(attachmentName);
                                            complaintAttachmentDTO.setUrl(url);
                                            complaintAttachmentDTO.setSize(size);
                                            complaintAttachmentDTO.setDataSources(dataSources);
                                            complaintAttachmentDTO.setIsDeleted(isDeleted);
                                            complaintAttachmentDTO.setIsValid(isValid);
                                            complaintAttachmentDTO.setCreatedAt(createdAt);
                                            complaintAttachmentDTO.setUpdatedAt(updatedAt);
                return complaintAttachmentService.selectPageBysql(page,complaintAttachmentDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentDTO
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public ComplaintAttachmentDTO getById(@PathVariable("id") Long id){
        return complaintAttachmentService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param complaintAttachmentDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintAttachmentDTO", name = "complaintAttachmentDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody ComplaintAttachmentDTO complaintAttachmentDTO){
        return complaintAttachmentService.insert( complaintAttachmentDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param complaintAttachmentDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintAttachmentDTO", name = "complaintAttachmentDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody ComplaintAttachmentDTO complaintAttachmentDTO){
        return complaintAttachmentService.update(id,complaintAttachmentDTO);
        }

    /**
     * 进行数据新增
     *
     * @param attachmentDto 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-03-25
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AttachmentDto", name = "attachmentDto", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
    @RequestMapping(value = "/insertAttachment", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertAttachment(@RequestBody AttachmentDto attachmentDto){
        ComplaintAttachmentDTO complaintAttachmentDTO=new ComplaintAttachmentDTO();
        complaintAttachmentDTO.setAttachmentName(attachmentDto.getName());
        ComplaintInfoDTO complaintInfoDTO=new ComplaintInfoDTO();
        complaintInfoDTO.setComplaintId(attachmentDto.getBaseDate().getOrderNo());
        List<ComplaintInfoPO> queryid=complaintInfoMapper.queryid(complaintInfoDTO);
        if(queryid.size()!=0){
            complaintAttachmentDTO.setComplaintInfoId(queryid.get(0).getId());
        }else {
            complaintAttachmentDTO.setComplaintInfoId(0L);
        }


        complaintAttachmentDTO.setComplaintNo(attachmentDto.getBaseDate().getOrderNo());
        complaintAttachmentDTO.setSize(attachmentDto.getBaseDate().getSize());
        complaintAttachmentDTO.setUrl(attachmentDto.getRes().getUrl());
        complaintAttachmentDTO.setOwnerCode(FrameworkUtil.getLoginInfo().getOwnerCode());

        return complaintAttachmentService.insert(complaintAttachmentDTO);
    }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/deleteById/{id}")
@ResponseStatus(HttpStatus.CREATED)
public boolean deleteById(@PathVariable("id") Long id){
    ComplaintAttachmentDTO complaintAttachmentDTO=new ComplaintAttachmentDTO();
    complaintAttachmentDTO.setIsDeleted(true);
    complaintAttachmentService.update(id,complaintAttachmentDTO);

        return true;
        } /**
     * 查看经销商自建投诉单中的附件
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintNo", value = "")
    })
    @ApiOperation(value = "查看经销商自建投诉单中的附件", notes = "查看经销商自建投诉单中的附件", httpMethod = "GET")
    @RequestMapping(value = "/queryEnclosure", method = RequestMethod.GET)
    public List<ComplaintAttachmentTestDTO> queryEnclosure(
            @RequestParam(value="complaintNo",required = false) String complaintNo){
        ComplaintAttachmentDTO complaintAttachmentDTO=new ComplaintAttachmentDTO();
        complaintAttachmentDTO.setComplaintNo(complaintNo);

        return complaintAttachmentService.selectListBySql1(complaintAttachmentDTO);
    }




}