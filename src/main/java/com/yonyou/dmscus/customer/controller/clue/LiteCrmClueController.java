package com.yonyou.dmscus.customer.controller.clue;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.biz.log.bean.LogPrintTemplate;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.constants.clue.LeadsTypeEnum;
import com.yonyou.dmscus.customer.dto.BizLogDto;
import com.yonyou.dmscus.customer.dto.EM90MessageRemindDto;
import com.yonyou.dmscus.customer.enevt.EM90MessageRemindEvent;
import com.yonyou.dmscus.customer.entity.dto.clue.ClueDataDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.ContactInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.InviteClueParamDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.InviteClueResultDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.LiteCrmClueDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.LiteCrmClueResultDTO;
import com.yonyou.dmscus.customer.entity.po.common.ClueLogPO;
import com.yonyou.dmscus.customer.entity.po.common.LeadsBlackPO;
import com.yonyou.dmscus.customer.entity.po.common.LeadsBlackRecordPO;
import com.yonyou.dmscus.customer.service.cdpTagTask.qb.CdpTagTaskService;
import com.yonyou.dmscus.customer.service.common.clue.ClueLogService;
import com.yonyou.dmscus.customer.service.common.clue.LeadsBlackService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.invitationautocreate.InviteVehicleTaskService;
import com.yonyou.dmscus.customer.service.partclue.PartClueService;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocInviteVehicleTaskRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * description crm线条同步到nb
 *
 * <AUTHOR>
 * @date 2023/8/23 15:19
 */
@Api(value = "/inviteClue", tags = {"crm线条同步到nb"})
@Slf4j
@RestController
@RequestMapping("/inviteClue")
public class LiteCrmClueController {
    @Autowired
    private InviteVehicleRecordService inviteVehicleRecordService;
    @Autowired
    private InviteVehicleTaskService inviteVehicleTaskService;
    @Autowired
    private PartClueService partClueService;
    @Autowired
    private VocInviteVehicleTaskRecordService taskRecordService;
    @Autowired
    private ClueLogService clueLogService;
    @Autowired
    private LeadsBlackService leadsBlackService;
    @Autowired
    private CdpTagTaskService cdpTagTaskService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "LiteCrmClueDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "CRM下发线索到NB", notes = "", httpMethod = "POST")
    @PostMapping("/clueDataSynchro")
    public LiteCrmClueResultDTO doClueDataSynchro(@RequestBody LiteCrmClueDTO dto) {

        BizLogDto bizLogDto = initLogPrintTemplate(dto);
        LogPrintTemplate logPrintTemplate=initLogPrintTemplate();
        log.info("doClueDataSynchro,dto:{}", dto);
        LiteCrmClueResultDTO resultDTO = new LiteCrmClueResultDTO();
        if (Objects.isNull(dto)){
            log.info("LiteCrmClueDTO is null");
            throw new ServiceBizException("LiteCrmClueDTO is null");
        }
        String vin = dto.getVehicleVin();
        if (StringUtils.isBlank(vin)){
            log.info("vin is null");
            throw new ServiceBizException("vin is null");
        }
        //线索数据
        ClueDataDTO data = dto.getData();
        if (Objects.isNull(data)){
            log.info("ClueDataDTO is null");
            throw new ServiceBizException("ClueDataDTO is null");
        }
        //icm线索id
        Long icmId = dto.getId();
        String leadsType = dto.getLeadsType();
        if (Objects.isNull(icmId)||StringUtils.isBlank(leadsType)) {
            log.info("icmId is null or leadsType is null");
            throw new ServiceBizException("icmId is null or leadsType is null");

        }
        //记录线索下发日志
        try {
            ClueLogPO po = new ClueLogPO();
            po.setIcmId(dto.getId());
            po.setRequestData(JSON.toJSONString(dto));
            clueLogService.add(po);
        } catch (Exception e) {
            log.info("doClueDataSynchro,clueLogService.add is error:", e);
        }
        //线索黑名单拦截
        Long count = leadsBlackService.selectBlackByVinAndType(vin, leadsType);
        log.info("doClueDataSynchro,count:{}", count);
        if (count != null && count > 0){
            log.info("doClueDataSynchro,count > 0");
            LeadsBlackRecordPO leadsBlackRecordPO = new LeadsBlackRecordPO();
            leadsBlackRecordPO.setIcmId(icmId.toString());
            leadsBlackRecordPO.setVin(vin);
            leadsBlackRecordPO.setLeadsData(JSON.toJSONString(dto));
            log.info("doClueDataSynchro,leadsBlackRecordPO:{}", leadsBlackRecordPO);
            //添加拦截记录
            int num = leadsBlackService.addLeadsBlackRecord(leadsBlackRecordPO);
            log.info("doClueDataSynchro,num:{}", num);
            //返回结果
            resultDTO.setFlag(Boolean.TRUE);
            return resultDTO;
        }

        // 生成线索
        if (LeadsTypeEnum.POST_ZERMATT_LEADS.getCode().equals(leadsType)) {
            log.info("doClueDataSynchro ZERMATT_LEADS");
            boolean flag;
            try {
                flag = partClueService.doClueDataSynchro(dto);
                log.info("doClueDataSynchro,flag:{}", flag);
            }catch (Exception e){
                bizLogDto.setErrorMsg(e.getMessage());
                logPrintTemplate.setStatus(0);
                log.error("业务指标监控异常",e);
            }finally {
                logPrintTemplate.setData(BeanUtil.beanToMap(bizLogDto));
                log.info("logPrintTemplate 业务指标监控:{}",logPrintTemplate);
            }
            //EM90 message remind on 2024-02-22
            applicationEventPublisher.publishEvent(new EM90MessageRemindEvent(new EM90MessageRemindDto(null,null,dto)));
        } else if (LeadsTypeEnum.POST_LiGHT_LEADS.getCode().equals(leadsType)) {
            log.info("doClueDataSynchro LiGHT_LEADS");
            try {
                resultDTO = inviteVehicleRecordService.addClueByCrm(dto);
            }catch (Exception e){
                bizLogDto.setErrorMsg(e.getMessage());
                logPrintTemplate.setStatus(0);
                log.error("业务指标监控异常",e);
            }finally {
                logPrintTemplate.setData(BeanUtil.beanToMap(bizLogDto));
                log.info("logPrintTemplate 业务指标监控:{}",logPrintTemplate);
            }

            boolean flag = resultDTO.getFlag() == null ? Boolean.FALSE : resultDTO.getFlag();
            log.info("doClueDataSynchro,flag:{}", flag);

        } else {
            log.info("doClueDataSynchro 未获取到合法的线索类型");
            throw new ServiceBizException("未获取到合法的线索类型");
        }
        log.info("doClueDataSynchro,end");
        return resultDTO;
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "", required = true)
    })
    @ApiOperation(value = "通过工单完成线索,接口调试", notes = "", httpMethod = "GET")
    @GetMapping("/completion")
    public void checkLeadCompletionByWorkOrder(@RequestParam String createDate) {
        log.info("checkLeadCompletionByWorkOrder,createDate:{}", createDate);
        inviteVehicleRecordService.completeLeadByWorkOrder(createDate);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createDate", value = "", required = true)
    })
    @ApiOperation(value = "逾期接口关闭,接口调试", notes = "", httpMethod = "GET")
    @GetMapping("/expireLeads")
    public void terminateOverdueLeads(@RequestParam String createDate) {
        log.info("terminateOverdueLeads,createDate:{}", createDate);
        inviteVehicleRecordService.closeOverdueLeads(createDate);
    }

    @ApiOperation(value = "通过保养灯信息判断线索验证状态", notes = "", httpMethod = "POST")
    @PostMapping("/validateMaintenanceLight")
    public void validateMaintenanceLight() {
        log.info("validateMaintenanceLight");
        inviteVehicleRecordService.isValidationSuccessful();
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "startTime", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "endTime", value = "", required = true)
    })
    @ApiOperation(value = "保养灯-写入CDP标签同步任务数据", notes = "", httpMethod = "GET")
    @GetMapping("/writesCdpTagTask")
    public void writesCdpTagTask(@RequestParam String startTime,@RequestParam String endTime) {
        log.info("writesCdpTagTask");
        cdpTagTaskService.writesCdpTagTask(startTime,endTime);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "startTime", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "endTime", value = "", required = true)
    })
    @ApiOperation(value = "保养灯-写入CDP标签同步任务数据", notes = "", httpMethod = "GET")
    @GetMapping("/disposeCdpTagTask")
    public void disposeCdpTagTask(@RequestParam String startTime,@RequestParam String endTime) {
        log.info("disposeCdpTagTask");
        cdpTagTaskService.disposeCdpTagTask(startTime,endTime);
    }

    @ApiOperation(value = "刷线索字典数据,初始化数据使用", notes = "", httpMethod = "GET")
    @GetMapping("/refreshLeadDictionaryData")
    public void syncLeadDictionaryData() {
        log.info("syncLeadDictionaryData");
        taskRecordService.updateDictionaryData();
    }

    @ApiOperation(value = "补偿扩展表数据,初始化数据使用", notes = "", httpMethod = "GET")
    @GetMapping("/populateExtendedTable")
    public void compensateExtendedData() {
        log.info("compensateExtendedData");
        inviteVehicleRecordService.batchInsertExtendedData();
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCodes", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "modType", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "rosterType", value = "", required = true)
    })
    @ApiOperation(value = "添加白名单", notes = "", httpMethod = "GET")
    @GetMapping("/configurationWhiteList")
    public void configurationWhiteList(@RequestParam String ownerCodes,
                                       @RequestParam Integer modType,
                                       @RequestParam Integer rosterType) {
        log.info("configurationWhiteList");
        inviteVehicleRecordService.addWhiteList(ownerCodes,modType,rosterType);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "dealerCode", value = "", required = true),
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "查询客户信息", notes = "", httpMethod = "GET")
    @GetMapping("/customers/{dealerCode}/{vin}")
    public List<ContactInfoDTO> getCustomerInfoById(@PathVariable("dealerCode") String dealerCode, @PathVariable("vin") String vin) {
        return inviteVehicleRecordService.queryContactInfo(dealerCode, vin);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<InviteClueParamDTO>", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "邀约线索查询", notes = "", httpMethod = "POST")
    @PostMapping("/searchClues")
    public List<IPage<InviteClueResultDTO>> batchRetrieveClues(@NotNull @RequestBody List<InviteClueParamDTO> dto) {
        //查询白名单
        List<String> whiteList = inviteVehicleRecordService.getWhiteList(CommonConstants.MOD_TYPE_91112011);
        return dto.stream().filter(Objects::nonNull).map(param -> retrieveClues(param, whiteList)).collect(Collectors.toList());
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "leadsType", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "")
    })
    @ApiOperation(value = "邀约线索查询", notes = "", httpMethod = "GET")
    @GetMapping("/searchClues/latest")
    public InviteClueResultDTO batchRetrieveClues(@RequestParam(value = "vin") String vin,
                                                  @RequestParam(value = "leadsType") Integer leadsType,
                                                  @RequestParam(value = "dealerCode", required = false) String dealerCode) {
        return inviteVehicleRecordService.selectNewInviteClue(vin, leadsType, dealerCode);
    }

    @NotNull
    private IPage<InviteClueResultDTO> retrieveClues(InviteClueParamDTO dto, List<String> whiteList) {
        log.info("retrieveClues,dto:{}", dto);
        List<String> vin = dto.getVin();
        // 参数校验
        if (dto.getCurrentPage() < InviteClueParamDTO.DEFAULT_CURRENT_PAGE) {
            throw new ServiceBizException("currentPage must be greater than or equal to 1");
        }
        if (CollectionUtils.isEmpty(vin)) {
            throw new ServiceBizException("vin list is null or empty");
        }
        Page<InviteClueResultDTO> page = new Page<>(dto.getCurrentPage(), dto.getPageSize());
        //白名单
        if (!CollectionUtils.isEmpty(whiteList)) {
            log.info("retrieveClues,!CollectionUtils.isEmpty(whiteList)");
            page.setRecords(Collections.emptyList());
            return page;
        }
        List<InviteClueResultDTO> result = inviteVehicleRecordService.selectInviteClue(page, dto);
        page.setRecords(CollectionUtils.isEmpty(result) ? Collections.emptyList() : result);
        return page;
    }

    @ApiOperation(value = "拉取CDP更新返厂意向登记", notes = "", httpMethod = "GET")
    @GetMapping("/cdpUpdateReturnIntentionLevel")
    public void cdpUpdateReturnIntentionLevel(){
        inviteVehicleRecordService.cdpUpdateReturnIntentionLevel();
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<String>", name = "vinList", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping("/doReturnIntentionLevel")
    public void doReturnIntentionLevel(@NotNull @RequestBody List<String> vinList){
        log.info("手动拉取CDP更新返厂意向登记:{}", vinList);
        inviteVehicleRecordService.doReturnIntentionLevel(vinList);
    }


    /**
     * 初始化线索下发监控对象
     */
    private BizLogDto initLogPrintTemplate(LiteCrmClueDTO dto){
        BizLogDto bizLogDto=new BizLogDto();
        try {
            if (Objects.isNull(dto) || Objects.isNull(dto.getData()) || Objects.isNull(dto.getData().getMaintenanceLightInfo())){
                bizLogDto.setErrorMsg("LiteCrmClueDTO is null");
                return bizLogDto;
            }
            bizLogDto.setAppServiceName("dmscus-customer");
            bizLogDto.setLeadsType(dto.getLeadsType());
            bizLogDto.setInviteType(dto.getData().getMaintenanceLightInfo().getInvitationType());
        }catch (Exception e){
            log.error("初始化线索下发监控对象异常",e);
        }
        return bizLogDto;
    }
    /**
     * 初始化线索下发监控对象
     */
    private LogPrintTemplate initLogPrintTemplate(){
        LogPrintTemplate logPrintTemplate =new LogPrintTemplate();
        logPrintTemplate.setAppServiceName("dmscus-customer");
        logPrintTemplate.setTraceId(UUID.randomUUID().toString());
        return logPrintTemplate;
    }

    @PostMapping("/addLeadsBlack")
    @ApiOperation(value = "邀约线索新增黑名单内")
    public void addLeadsBlack(@NotNull @RequestBody LeadsBlackPO po) {
        log.info("addLeadsBlack");
        String vin = po.getVin();
        String leadsType = po.getLeadsType();
        if(StringUtils.isBlank(vin) || StringUtils.isBlank(leadsType)){
            log.error("addLeadsBlack,vin or leadsType is null");
            return;
        }
        leadsBlackService.addLeadsBlack(po);
    }
}
