package com.yonyou.dmscus.customer.controller.talkskill;

import com.alibaba.fastjson.JSON;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillTypeDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillTypePO;
import com.yonyou.dmscus.customer.service.talkskill.TalkskillTypeService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 *
 * <AUTHOR>
 * @since 2020-03-20
 */
@Api(value = "/talkskillType", tags = {"TalkskillTypeController"})
@RestController
@RequestMapping("/talkskillType")
public class TalkskillTypeController extends BaseController {
    
        @Autowired
        TalkskillTypeService talkskillTypeService;

        /**
         *  全量查询
         * <AUTHOR>
         * @since 2020-03-20
         */
        @ApiImplicitParams({
                @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "")
        })
        @ApiOperation(value = "全量查询", notes = "全量查询", httpMethod = "GET")
        @GetMapping(value = "/list")
        public List<TalkskillTypePO> getList(
                @RequestParam(value="isValid",required = false) Integer isValid
        ){
            TalkskillTypeDTO talkskillTypeDTO = new TalkskillTypeDTO();
            talkskillTypeDTO.setIsValid(isValid);
            return talkskillTypeService.selectListBySql(talkskillTypeDTO);
        }

        /**
         * 进行数据修改
         *
         * @param id 数据主键ID
         * @return com.yonyou.dmscus.repair.entity.dto.TalkskillTypeDTO
         * <AUTHOR>
         * @since 2020-03-20
         */
        @ApiImplicitParams({
                @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
        })
        @ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
        @GetMapping(value = "/{id}")
        public TalkskillTypeDTO getById(@PathVariable("id") Long id){
                return talkskillTypeService.getById(id);
                }

        /**
         * 进行数据新增-逐条
         *
         * @param
         * @return int
         * <AUTHOR>
         * @since 2020-03-20
         */
        @ApiImplicitParams({
                @ApiImplicitParam(paramType = "body", dataType = "List<TalkskillTypeDTO>", name = "listType", value = "", required = true)
        })
        @ApiOperation(value = "进行数据新增-逐条", notes = "进行数据新增-逐条", httpMethod = "POST")
        @PostMapping
        @ResponseStatus(HttpStatus.CREATED)
        public int insert(@RequestBody List<TalkskillTypeDTO> listType){
            try {
                return  talkskillTypeService.updateList(listType);
            }catch (Exception e){
                return 0;
            }

        }
        /**
         * 进行数据新增-批量
         *
         * @param
         * @return int
         * <AUTHOR>
         * @since 2020-03-20
         */
        @ApiImplicitParams({
                @ApiImplicitParam(paramType = "body", dataType = "List<TalkskillTypeDTO>", name = "listType", value = "", required = true)
        })
        @ApiOperation(value = "进行数据新增-批量", notes = "进行数据新增-批量", httpMethod = "POST")
        @PostMapping(value = "/list")
        @ResponseStatus(HttpStatus.CREATED)
        public int insertList(@RequestBody List<TalkskillTypeDTO> listType){
            LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
            String text = JSON.toJSONString(loginInfoDto);
            try {
                System.out.println(text);
                for(int i =0;i<listType.size();i++){
                    talkskillTypeService.insert(listType.get(i));
                }
                return 1;
            }catch (Exception e){
                System.out.println(e);
                return 0;
            }

        }
        /**
         * 进行数据修改
         *
         * @param id 需要修改数据的ID
         * @param talkskillTypeDTO 需要保存的DTO
         * @return int
         * <AUTHOR>
         * @since 2020-03-20
         */
        @ApiImplicitParams({
                @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
                @ApiImplicitParam(paramType = "body", dataType = "TalkskillTypeDTO", name = "talkskillTypeDTO", value = "需要保存的DTO", required = true)
        })
        @ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
        @PutMapping(value = "/{id}")
        @ResponseStatus(HttpStatus.CREATED)
        public int update(@PathVariable("id") Long id,@RequestBody TalkskillTypeDTO talkskillTypeDTO){
            return talkskillTypeService.update(id,talkskillTypeDTO);
        }



}