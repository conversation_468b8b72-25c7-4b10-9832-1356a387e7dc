package com.yonyou.dmscus.customer.controller.inviteInsurance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceDuplicateRemovalRuleDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceRuleDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceRuleVcdcParamsVo;
import com.yonyou.dmscus.customer.service.inviteInsurance.InviteInsuranceDuplicateRemovalRuleService;
import com.yonyou.dmscus.customer.service.inviteInsurance.InviteInsuranceRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-11-13
 */
@RestController
@RequestMapping("/inviteInsuranceRuleVcdc")
@Api(value = "厂端邀约规则设置")
public class InviteInsuranceRuleVcdcController {

    @Autowired
    InviteInsuranceRuleService inviteInsuranceRuleService;
    @Autowired
    InviteInsuranceDuplicateRemovalRuleService inviteInsuranceDuplicateRemovalRuleService;



    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteInsuranceRuleVcdcParamsVo", name = "vo", value = "", required = true)
    })
    @ApiOperation(value = "查询数据", notes = "", httpMethod = "POST")
    @PostMapping("/getInviteInsuranceRuleVcdc")
    public IPage<InviteInsuranceRuleDTO> getInviteInsuranceRuleVcdc(@RequestBody InviteInsuranceRuleVcdcParamsVo vo) {
        return inviteInsuranceRuleService.getInviteInsuranceRuleVcdc(vo);
    }


    /**
     * 查询续保去重规则
     *
     */
    @ApiOperation(value = "查询续保去重规则", notes = "查询续保去重规则", httpMethod = "GET")
    @GetMapping(value = "/getInsuranceDuplicateRemovalRule")
    public List<InviteInsuranceDuplicateRemovalRuleDTO> getInsuranceDuplicateRemovalRule() {
        InviteInsuranceDuplicateRemovalRuleDTO dto = new InviteInsuranceDuplicateRemovalRuleDTO();
        return inviteInsuranceDuplicateRemovalRuleService.selectListBySql(dto);
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteInsuranceRuleDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "保存续保规则", notes = "", httpMethod = "POST")
    @PostMapping(value = "/saveInviteInsuranceRuleVcdc")
    public int saveInviteInsuranceRule(@RequestBody InviteInsuranceRuleDTO dto) {
        return inviteInsuranceRuleService.saveInviteInsuranceRule(dto);
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "inviteType", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "inviteRule", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "dayInAdvance", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "remindInterval", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isUse", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "List<String>", name = "dealerCodes", value = "")
    })
    @ApiOperation(value = "查询数据", notes = "", httpMethod = "GET")
    @GetMapping("/getInviteInsuranceRule")
    public List<InviteInsuranceRuleDTO> getByList(
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "dealerCode", required = false) String dealerCode,
            @RequestParam(value = "inviteType", required = false) Integer inviteType,
            @RequestParam(value = "inviteRule", required = false) Integer inviteRule,
            @RequestParam(value = "dayInAdvance", required = false) Integer dayInAdvance,
            @RequestParam(value = "remindInterval", required = false) Integer remindInterval,
            @RequestParam(value = "isUse", required = false) Integer isUse,
            @RequestParam(value = "dataSources", required = false) Integer dataSources,
            @RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
            @RequestParam(value = "isValid", required = false) Integer isValid,
            @RequestParam(value = "dealerCodes", required = false) List<String> dealerCodes) {

        InviteInsuranceRuleDTO inviteInsuranceRuleDTO = new InviteInsuranceRuleDTO();
        inviteInsuranceRuleDTO.setDealerCode(dealerCode);
        return inviteInsuranceRuleService.selectListBySql(inviteInsuranceRuleDTO);
    }

}