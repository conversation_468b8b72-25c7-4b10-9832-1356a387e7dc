package com.yonyou.dmscus.customer.controller.vocAccidentInvitation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.service.excel.ExcelExportColumn;
import com.yonyou.dmscloud.framework.service.excel.ExcelGenerator;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.vocAccidentInvitation.InviteVehicleVocTaskDTO;
import com.yonyou.dmscus.customer.entity.dto.vocAccidentInvitation.TempInviteVehicleVocTaskDTO;
import com.yonyou.dmscus.customer.entity.po.vocAccidentInvitation.InviteVehicleVocTaskPO;
import com.yonyou.dmscus.customer.service.vocAccidentInvitation.InviteVehicleVocTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @since 2020-04-24
 */
@Api(value = "/inviteVehicleVocTask", tags = {"InviteVehicleVocTaskController"})
@RestController
@RequestMapping("/inviteVehicleVocTask")
public class InviteVehicleVocTaskController {

    @Autowired
    InviteVehicleVocTaskService inviteVehicleVocTaskService;
    @Autowired
    ExcelGenerator excelGenerator;

    /**
     * 分页查询数据
     * @param largeAreaId
     * @param areaId
     * @param name
     * @param vin
     * @param licensePlateNum
     * @param dealerCode
     * @param contactDateStart
     * @param contactDateEnd
     * @param newContactDateStart
     * @param newContactDateEnd
     * @param createdAtStart
     * @param createdAtEnd
     * @param currentPage
     * @param pageSize
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "largeAreaId", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "areaId", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "name", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "licensePlateNum", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "contactDateStart", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "contactDateEnd", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "newContactDateStart", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "newContactDateEnd", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAtStart", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAtEnd", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
    @GetMapping("/getVocInvitation")
    public IPage<InviteVehicleVocTaskDTO> getByPage(
            @RequestParam(value = "largeAreaId", required = false) String largeAreaId,
            @RequestParam(value = "areaId", required = false) String areaId,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "vin", required = false) String vin,
            @RequestParam(value = "licensePlateNum", required = false) String licensePlateNum,
            @RequestParam(value = "dealerCode", required = false) String dealerCode,
            @RequestParam(value = "contactDateStart", required = false) String contactDateStart,
            @RequestParam(value = "contactDateEnd", required = false) String contactDateEnd,
            @RequestParam(value = "newContactDateStart", required = false) String newContactDateStart,
            @RequestParam(value = "newContactDateEnd", required = false) String newContactDateEnd,
            @RequestParam(value = "createdAtStart", required = false) String createdAtStart,
            @RequestParam(value = "createdAtEnd", required = false) String createdAtEnd,
            @RequestParam("currentPage") int currentPage,
            @RequestParam("pageSize") int pageSize) {
        Page<InviteVehicleVocTaskPO> page = new Page(currentPage, pageSize);

        InviteVehicleVocTaskDTO inviteVehicleVocTaskDTO = new InviteVehicleVocTaskDTO();
        inviteVehicleVocTaskDTO.setLargeAreaId(largeAreaId);
        inviteVehicleVocTaskDTO.setAreaId(areaId);
        inviteVehicleVocTaskDTO.setName(name);
        inviteVehicleVocTaskDTO.setVin(vin);
        inviteVehicleVocTaskDTO.setLicensePlateNum(licensePlateNum);
        inviteVehicleVocTaskDTO.setDealerCode(dealerCode);
        inviteVehicleVocTaskDTO.setContactDateStart(contactDateStart);
        inviteVehicleVocTaskDTO.setContactDateEnd(contactDateEnd);
        inviteVehicleVocTaskDTO.setNewContactDateStart(newContactDateStart);
        inviteVehicleVocTaskDTO.setNewContactDateEnd(newContactDateEnd);
        inviteVehicleVocTaskDTO.setCreatedAtStart(createdAtStart);
        inviteVehicleVocTaskDTO.setCreatedAtEnd(createdAtEnd);
        return inviteVehicleVocTaskService.selectPageBysql(page, inviteVehicleVocTaskDTO);
    }

    /**
     * 保存
     *
     * @param inviteVehicleVocTaskDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-24
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteVehicleVocTaskDTO", name = "inviteVehicleVocTaskDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "保存", notes = "保存", httpMethod = "POST")
    @PostMapping("/saveVocInvitation")
    public int insert(@RequestBody InviteVehicleVocTaskDTO inviteVehicleVocTaskDTO) {
        return inviteVehicleVocTaskService.saveVocInvitation(inviteVehicleVocTaskDTO);
    }


    /**
     * 模板下载
     * @param request
     * @param response
     * @throws Exception
     */
    @ApiOperation(value = "模板下载", notes = "模板下载", httpMethod = "GET")
    @RequestMapping(value = "/vocInvitationExcelDownload", method = RequestMethod.GET)
    @ResponseBody
    public void excelDownload(HttpServletRequest request,
                              HttpServletResponse response) throws Exception {
        Map<String, List<Map>> excelData = new HashMap<String, List<Map>>();
        excelData.put("voc事故导入模板", null);
        List<ExcelExportColumn> exportColumnList = new ArrayList<ExcelExportColumn>();
        exportColumnList.add(new ExcelExportColumn("vin", "VIN"));
        exportColumnList.add(new ExcelExportColumn("licensePlateNum", "车牌号"));
        exportColumnList.add(new ExcelExportColumn("dealerCode", "经销商code"));
        exportColumnList.add(new ExcelExportColumn("contactDate", "电话联系经销商时间(格式：yyyy-MM-dd)"));
        exportColumnList.add(new ExcelExportColumn("name", "联系人姓名"));
        exportColumnList.add(new ExcelExportColumn("tel", "联系电话"));
        exportColumnList.add(new ExcelExportColumn("contactSituation", "与客户通话情况"));
        exportColumnList.add(new ExcelExportColumn("accidentNo", "voc事故号"));
        exportColumnList.add(new ExcelExportColumn("accidentDetail", "voc事故说明"));
        exportColumnList.add(new ExcelExportColumn("remark", "备注"));
        excelGenerator.generateExcel(excelData, exportColumnList, "voc事故导入模板.xls", request, response);
    }



    /**
     * voc事故导入临时表
     * @param importFile
     * @return
     * @throws Exception
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "", required = true)
    })
    @ApiOperation(value = "voc事故导入", notes = "voc事故导入临时表", httpMethod = "POST")
    @RequestMapping(value = "/importVocAccidentInvitation", method = RequestMethod.POST)
    @ResponseBody
    public ImportTempResult<TempInviteVehicleVocTaskDTO> importIdcPurchaseRuleTemp(@RequestParam(value = "file") MultipartFile importFile) throws Exception {
        return inviteVehicleVocTaskService.importTempVocInvitation(importFile);
    }


    /**
     * 临时表数据保存到正式表
     * @return
     */
    @ApiOperation(value = "临时表数据保存到正式表", notes = "临时表数据保存到正式表", httpMethod = "GET")
    @GetMapping(value = "importVocInvitation")
    public int importVocInvitation(){
        return inviteVehicleVocTaskService.importVocInvitation();
    }
}