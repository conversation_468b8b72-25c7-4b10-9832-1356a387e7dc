package com.yonyou.dmscus.customer.controller.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiTest;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRuleUsePO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintKpiBaseRuleUseService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;


/**
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
@Api(value = "/complaintKpiBaseRuleUse", tags = {"ComplaintKpiBaseRuleUseController"})
@RestController
@RequestMapping("/complaintKpiBaseRuleUse")
                public class ComplaintKpiBaseRuleUseController extends BaseController {
    
        @Autowired
        ComplaintKpiBaseRuleUseService complaintKpiBaseRuleUseService;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码 
        * @param ownerParCode 所有者的父组织代码 
        * @param orgId 组织ID
        * @param id 主键ID
        * @param ruleId 规则ID
        * @param dealerCode 进销商代码 厂端用VCDC
        * @param user 使用人，主要用于VCDC人员。 进销商不到具体使用人
        * @param warnValue 警戒值
        * @param dataSources 数据来源 
        * @param isDeleted 是否删除
        * @param isValid 是否有效 
        * @param createdAt 创建时间
        * @param updatedAt 更新时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @since 2020-04-14
        */
                @ApiImplicitParams({
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "ruleId", value = "规则ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "进销商代码 厂端用VCDC"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "user", value = "使用人，主要用于VCDC人员。 进销商不到具体使用人"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "warnValue", value = "警戒值"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = "数据来源"),
                        @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "createdAt", value = "创建时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "updatedAt", value = "更新时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
                })
                @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
                @GetMapping
        public IPage<ComplaintKpiBaseRuleUseDTO>getByPage(
@RequestParam(value="appId",required = false) String appId,
@RequestParam(value="ownerCode",required = false) String ownerCode,
@RequestParam(value="ownerParCode",required = false) String ownerParCode,
@RequestParam(value="orgId",required = false) Integer orgId,
@RequestParam(value="id",required = false) Long id,
@RequestParam(value="ruleId",required = false) Long ruleId,
@RequestParam(value="dealerCode",required = false) String dealerCode,
@RequestParam(value="user",required = false) String user,
@RequestParam(value="warnValue",required = false) String warnValue,
@RequestParam(value="dataSources",required = false) Integer dataSources,
@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
@RequestParam(value="isValid",required = false) Integer isValid,
@RequestParam(value="createdAt",required = false) Date createdAt,
@RequestParam(value="updatedAt",required = false) Date updatedAt,
        @RequestParam("currentPage")int currentPage,
        @RequestParam("pageSize")int pageSize){
        Page<ComplaintKpiBaseRuleUsePO>page=new Page(currentPage,pageSize);

        ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO =new ComplaintKpiBaseRuleUseDTO();
                                            complaintKpiBaseRuleUseDTO.setAppId(appId);
                                            complaintKpiBaseRuleUseDTO.setOwnerCode(ownerCode);
                                            complaintKpiBaseRuleUseDTO.setOwnerParCode(ownerParCode);
                                            complaintKpiBaseRuleUseDTO.setOrgId(orgId);
                                            complaintKpiBaseRuleUseDTO.setId(id);
                                            complaintKpiBaseRuleUseDTO.setRuleId(ruleId);
                                            complaintKpiBaseRuleUseDTO.setDealerCode(dealerCode);
                                            complaintKpiBaseRuleUseDTO.setUser(user);
                                            complaintKpiBaseRuleUseDTO.setWarnValue(warnValue);
                                            complaintKpiBaseRuleUseDTO.setDataSources(dataSources);
                                            complaintKpiBaseRuleUseDTO.setIsDeleted(isDeleted);
                                            complaintKpiBaseRuleUseDTO.setIsValid(isValid);
                                            complaintKpiBaseRuleUseDTO.setCreatedAt(createdAt);
                                            complaintKpiBaseRuleUseDTO.setUpdatedAt(updatedAt);
                return complaintKpiBaseRuleUseService.selectPageBysql(page,complaintKpiBaseRuleUseDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseDTO
 * <AUTHOR>
 * @since 2020-04-14
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public ComplaintKpiBaseRuleUseDTO getById(@PathVariable("id") Long id){
        return complaintKpiBaseRuleUseService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param complaintKpiBaseRuleUseDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-14
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintKpiBaseRuleUseDTO", name = "complaintKpiBaseRuleUseDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO){
        return complaintKpiBaseRuleUseService.insert( complaintKpiBaseRuleUseDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param complaintKpiBaseRuleUseDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-14
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintKpiBaseRuleUseDTO", name = "complaintKpiBaseRuleUseDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO){
        return complaintKpiBaseRuleUseService.update(id,complaintKpiBaseRuleUseDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-04-14
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    complaintKpiBaseRuleUseService.deleteById(id);
        return true;
        }

    /**
     * 分页查询数据(客诉业务设置)
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-14
     */
    @ApiOperation(value = "分页查询数据(客诉业务设置)", notes = "分页查询数据(客诉业务设置)", httpMethod = "GET")
    @RequestMapping(value = "/queryKPI", method = RequestMethod.GET)
    public List<ComplaintKpiBaseRuleUseTestDTO> queryKpi(){

        ComplaintKpiBaseRuleUseTestDTO complaintKpiBaseRuleUserDTO =new ComplaintKpiBaseRuleUseTestDTO();
        complaintKpiBaseRuleUserDTO.setDealerCode(FrameworkUtil.getLoginInfo().getOwnerCode());
        return complaintKpiBaseRuleUseService.selectListBySql1(complaintKpiBaseRuleUserDTO);
    }
    /**
     * 分页查询数据(警戒值维护)
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-14
     */
    @ApiOperation(value = "分页查询数据(警戒值维护)", notes = "分页查询数据(警戒值维护)", httpMethod = "GET")
    @RequestMapping(value = "/queryKPIByUse", method = RequestMethod.GET)
    public List<ComplaintKpiBaseRuleUseTestDTO> queryKpiByUser(){

        ComplaintKpiBaseRuleUseTestDTO complaintKpiBaseRuleUserDTO =new ComplaintKpiBaseRuleUseTestDTO();
        complaintKpiBaseRuleUserDTO.setDealerCode(FrameworkUtil.getLoginInfo().getOwnerCode());
        complaintKpiBaseRuleUserDTO.setUser(String.valueOf(FrameworkUtil.getLoginInfo().getUserId()));
        return complaintKpiBaseRuleUseService.selectListBySql1(complaintKpiBaseRuleUserDTO);
    }


    /**
     * 店端修改警戒值
     *
     * @return
     * <AUTHOR>
     * @since 2020-04-14
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<ComplaintKpiTest>", name = "list", value = "", required = true)
    })
    @ApiOperation(value = "店端修改警戒值", notes = "店端修改警戒值", httpMethod = "POST")
    @RequestMapping(value = "/updateWarnValue", method = RequestMethod.POST)
    public int updateWarnValue(@RequestBody List<ComplaintKpiTest> list){

        return complaintKpiBaseRuleUseService.updateWarnValue(list);
    }



}