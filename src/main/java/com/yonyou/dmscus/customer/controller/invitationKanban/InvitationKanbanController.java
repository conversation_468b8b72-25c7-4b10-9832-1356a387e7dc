package com.yonyou.dmscus.customer.controller.invitationKanban;


import com.yonyou.dmscloud.framework.service.excel.ExcelGenerator;
import com.yonyou.dmscus.customer.entity.dto.invitationKanban.InvitationKanbanInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationKanban.InvitationKanbanQueryDTO;
import com.yonyou.dmscus.customer.service.invitationKanban.InvitationKanbanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-04-24
 */
@Api(value = "/invitationKanban", tags = {"InvitationKanbanController"})
@RestController
@RequestMapping("/invitationKanban")
public class InvitationKanbanController {

    @Autowired
    InvitationKanbanService invitationKanbanService;
    @Autowired
    ExcelGenerator excelGenerator;

    /**
     * 查询邀约看板
     * @param largeAreaId
     * @param areaId
     * @param monthNo
     * @param dealerCode
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "largeAreaId", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "areaId", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "monthNo", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "")
    })
    @ApiOperation(value = "查询邀约看板", notes = "查询邀约看板", httpMethod = "GET")
    @GetMapping("/list")
    public List<InvitationKanbanInfoDTO> getInvitationKanban(
            @RequestParam(value = "largeAreaId", required = false) String largeAreaId,
            @RequestParam(value = "areaId", required = false) String areaId,
            @RequestParam(value = "monthNo", required = false) String monthNo,
            @RequestParam(value = "dealerCode", required = false) String dealerCode){
        InvitationKanbanQueryDTO query = new InvitationKanbanQueryDTO();
        query.setLargeAreaId(largeAreaId);
        query.setAreaId(areaId);
        query.setMonthNo(monthNo);
        query.setDealerCode(dealerCode);
        return invitationKanbanService.InvitationKanbanService(query);
    }
}