package com.yonyou.dmscus.customer.controller.inviteInsurance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.service.excel.ExcelGenerator;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordImportDTO;
import com.yonyou.dmscus.customer.service.inviteInsurance.InviteInsuranceVehicleRecordImportService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2021-01-19
 */
@Api(value = "/inviteInsuranceVehicleRecordImport", tags = {"InviteInsuranceVehicleRecordImportController"})
@RestController
@RequestMapping("/inviteInsuranceVehicleRecordImport")
public class InviteInsuranceVehicleRecordImportController extends BaseController {

    @Autowired
    InviteInsuranceVehicleRecordImportService inviteInsuranceVehicleRecordImportService;

    @Autowired
    ExcelGenerator excelGenerator;


    /**
     * 续保线索预导入临时表
     *
     * @param importFile
     * @return
     * @throws Exception
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "", required = true)
    })
    @ApiOperation(value = "续保线索预导入临时表", notes = "续保线索预导入临时表", httpMethod = "POST")
    @RequestMapping(value = "/importInsuranceRecordTemp", method = RequestMethod.POST)
    @ResponseBody
    public ImportTempResult<InviteInsuranceVehicleRecordImportDTO> importInsuranceRecordTemp(
            @RequestParam(value = "file") MultipartFile importFile) throws Exception {
        return inviteInsuranceVehicleRecordImportService.importInsuranceRecordTemp(importFile);
    }

    /**
     * 临时表数据保存到正式表
     *
     * @param
     * @return
     * <AUTHOR>
     * @throws Exception
     * @since 2020-04-30
     */
    @ApiOperation(value = "临时表数据保存到正式表", notes = "临时表数据保存到正式表", httpMethod = "GET")
    @GetMapping(value = "importInsuranceRecord")
    public void importInsuranceRecord() throws Exception {

        inviteInsuranceVehicleRecordImportService.importInsuranceRecord();
    }

    /**
     * 查询临时表分页--正确--续保线索
     *
     * @param
     * @return
     * <AUTHOR>
     * @since 2020-09-27
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "查询临时表分页--正确--续保线索", notes = "查询临时表分页--正确--续保线索", httpMethod = "GET")
    @GetMapping(value = "selectImportSuccessInsuranceRecord")
    public IPage<InviteInsuranceVehicleRecordImportDTO> selectImportSuccessInsuranceRecord(
            @RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize) {
        Page<InviteInsuranceVehicleRecordImportDTO> page = new Page(currentPage, pageSize);
        return inviteInsuranceVehicleRecordImportService.selectImportSuccessInsuranceRecord(page);
    }

    /**
     * 查询临时表分页--错误--续保线索
     *
     * @param
     * @return
     * <AUTHOR>
     * @since 2020-03-30
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "查询临时表分页--错误--续保线索", notes = "查询临时表分页--错误--续保线索", httpMethod = "GET")
    @GetMapping(value = "selectImportErrorInsuranceRecord")
    public IPage<InviteInsuranceVehicleRecordImportDTO> selectImportErrorInsuranceRecord(
            @RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize) {
        Page<InviteInsuranceVehicleRecordImportDTO> page = new Page(currentPage, pageSize);
        return inviteInsuranceVehicleRecordImportService.selectImportErrorInsuranceRecord(page);
    }


}