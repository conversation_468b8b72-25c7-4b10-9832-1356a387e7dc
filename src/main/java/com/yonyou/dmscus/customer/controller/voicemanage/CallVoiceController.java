package com.yonyou.dmscus.customer.controller.voicemanage;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.CallVoiceDTO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallVoicePO;
import com.yonyou.dmscus.customer.service.voicemanage.CallVoiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;


/**
 * <AUTHOR>
 * @since 2020-06-16
 */
@Api(value = "/callVoice", tags = {"CallVoiceController"})
@RestController
@RequestMapping("/callVoice")
public class CallVoiceController {

    @Autowired
    CallVoiceService callVoiceService;

    /**
     * 分页查询数据
     *
     * @param appId         系统ID
     * @param ownerCode     所有者代码
     * @param ownerParCode  所有者的父组织代码
     * @param orgId         组织ID
     * @param id            主键ID
     * @param sessionId     话单ID
     * @param callId        call_id
     * @param displayNumber 显示号码
     * @param callerNumber  主叫号码
     * @param calleeNumber  被叫号码
     * @param workNumber    AI语音工作号
     * @param fileSize      文件大小
     * @param voiceUrl      录音文件URL
     * @param voiceType     录音类型
     * @param dataSources   数据来源
     * @param isDeleted     是否删除
     * @param isValid       是否有效
     * @param createdAt     创建时间
     * @param updatedAt     更新时间
     * @param currentPage   页数
     * @param pageSize      分页大小
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-06-16
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "sessionId", value = "话单ID"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "callId", value = "call_id"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "displayNumber", value = "显示号码"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "callerNumber", value = "主叫号码"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "calleeNumber", value = "被叫号码"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "workNumber", value = "AI语音工作号"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "fileSize", value = "文件大小"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "voiceUrl", value = "录音文件URL"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "voiceType", value = "录音类型"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = "数据来源"),
            @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "createdAt", value = "创建时间"),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "updatedAt", value = "更新时间"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
    })
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
    @GetMapping
    public IPage<CallVoiceDTO> getByPage(
            @RequestParam(value = "appId", required = false) String appId,
            @RequestParam(value = "ownerCode", required = false) String ownerCode,
            @RequestParam(value = "ownerParCode", required = false) String ownerParCode,
            @RequestParam(value = "orgId", required = false) Integer orgId,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "sessionId", required = false) String sessionId,
            @RequestParam(value = "callId", required = false) String callId,
            @RequestParam(value = "displayNumber", required = false) String displayNumber,
            @RequestParam(value = "callerNumber", required = false) String callerNumber,
            @RequestParam(value = "calleeNumber", required = false) String calleeNumber,
            @RequestParam(value = "workNumber", required = false) String workNumber,
            @RequestParam(value = "fileSize", required = false) String fileSize,
            @RequestParam(value = "voiceUrl", required = false) String voiceUrl,
            @RequestParam(value = "voiceType", required = false) String voiceType,
            @RequestParam(value = "dataSources", required = false) Integer dataSources,
            @RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
            @RequestParam(value = "isValid", required = false) Integer isValid,
            @RequestParam(value = "createdAt", required = false) Date createdAt,
            @RequestParam(value = "updatedAt", required = false) Date updatedAt,
            @RequestParam("currentPage") int currentPage,
            @RequestParam("pageSize") int pageSize) {
        Page<CallVoicePO> page = new Page(currentPage, pageSize);

        CallVoiceDTO callVoiceDTO = new CallVoiceDTO();
        callVoiceDTO.setAppId(appId);
        callVoiceDTO.setOwnerCode(ownerCode);
        callVoiceDTO.setOwnerParCode(ownerParCode);
        callVoiceDTO.setOrgId(orgId);
        callVoiceDTO.setId(id);
        callVoiceDTO.setSessionId(sessionId);
        callVoiceDTO.setCallId(callId);
        callVoiceDTO.setDisplayNumber(displayNumber);
        callVoiceDTO.setCallerNumber(callerNumber);
        callVoiceDTO.setCalleeNumber(calleeNumber);
        callVoiceDTO.setWorkNumber(workNumber);
        callVoiceDTO.setFileSize(fileSize);
        callVoiceDTO.setVoiceUrl(voiceUrl);
        callVoiceDTO.setVoiceType(voiceType);
        callVoiceDTO.setDataSources(dataSources);
        callVoiceDTO.setIsDeleted(isDeleted);
        callVoiceDTO.setIsValid(isValid);
        callVoiceDTO.setCreatedAt(createdAt);
        callVoiceDTO.setUpdatedAt(updatedAt);
        return callVoiceService.selectPageBysql(page, callVoiceDTO);
    }

    /**
     * 进行数据修改
     *
     * @param id 数据主键ID
     * @return com.yonyou.dmscus.customer.entity.dto.CallVoiceDTO
     * <AUTHOR>
     * @since 2020-06-16
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
    })
    @ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
    @GetMapping(value = "/{id}")
    public CallVoiceDTO getById(@PathVariable("id") Long id) {
        return callVoiceService.getById(id);
    }

    /**
     * 进行数据新增
     *
     * @param callVoiceDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-06-16
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "CallVoiceDTO", name = "callVoiceDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public int insert(@RequestBody CallVoiceDTO callVoiceDTO) {
        return callVoiceService.insert(callVoiceDTO);
    }

    /**
     * 进行数据修改
     *
     * @param id           需要修改数据的ID
     * @param callVoiceDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-06-16
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
            @ApiImplicitParam(paramType = "body", dataType = "CallVoiceDTO", name = "callVoiceDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
    @PutMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.CREATED)
    public int update(@PathVariable("id") Long id, @RequestBody CallVoiceDTO callVoiceDTO) {
        return callVoiceService.update(id, callVoiceDTO);
    }

    /**
     * 根据id删除对象
     *
     * @param id 需要修改数据的ID
     * <AUTHOR>
     * @since 2020-06-16
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
    })
    @ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public boolean deleteById(@PathVariable("id") Long id) {
        callVoiceService.deleteById(id);
        return true;
    }

    /**
     * 根据IDs批量删除对象
     *
     * @param ids 需要修改数据的ID集合
     * <AUTHOR>
     * @since 2020-06-16
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "需要修改数据的ID集合", required = true)
    })
    @ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
    @DeleteMapping(value = "/batch/{ids}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public boolean deleteByIds(@PathVariable("ids") String ids) {
        callVoiceService.deleteBatchIds(ids);
        return true;
    }

    /**
     * 录音文件拉取（测试类）
     */
    @ApiOperation(value = "录音文件拉取（测试类）", notes = "录音文件拉取（测试类）", httpMethod = "GET")
    @GetMapping("/callVoiceByAi")
    public void getCallVoiceByAi() {
    	callVoiceService.getCallVoiceByAi();
    }
    
    /**
     * 录音文件推送大搜车（测试类）
     */
    @ApiOperation(value = "录音文件推送大搜车（测试类）", notes = "录音文件推送大搜车（测试类）", httpMethod = "GET")
    @GetMapping("/sendDSCCallVoice")
    public void sendDSCCallVoice() {
    	callVoiceService.sendDSCCallVoice();
    }
}