package com.yonyou.dmscus.customer.controller.complaint;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ReasonsforFiveDto;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintNotCloseCaseReasonTestPO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintNotCloseCaseReasonService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;


/**
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Api(value = "/complaintNotCloseCaseReason", tags = {"ComplaintNotCloseCaseReasonController"})
@RestController
@RequestMapping("/complaintNotCloseCaseReason")
                public class ComplaintNotCloseCaseReasonController extends BaseController {
    
        @Autowired
        ComplaintNotCloseCaseReasonService complaintNotCloseCaseReasonService;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码 
        * @param ownerParCode 所有者的父组织代码 
        * @param orgId 组织ID
        * @param id 主键ID
        * @param complaintInfoId 投诉信息表主键ID
        * @param object 跟进对象 区域经理、经销商、CCM、客服中心
        * @param followTime 填写时间
        * @param follower 填写人
        * @param bigClass 未结案原因（大类）
        * @param bigClassName 未结案原因（大类）名称
        * @param smallClass 未结案原因（小类）多选用逗号分隔
        * @param smallClassName 未结案原因（小类）名称 多选用逗号分隔
        * @param other 其他
        * @param duration 时长（天）
        * @param dataSources 数据来源 
        * @param isDeleted 是否删除
        * @param isValid 是否有效 
        * @param createdAt 创建时间
        * @param updatedAt 更新时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @since 2020-04-15
        */
                @ApiImplicitParams({
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "complaintInfoId", value = "投诉信息表主键ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "object", value = "跟进对象 区域经理、经销商、CCM、客服中心"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "followTime", value = "填写时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "follower", value = "填写人"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "bigClass", value = "未结案原因（大类）"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "bigClassName", value = "未结案原因（大类）名称"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "smallClass", value = "未结案原因（小类）多选用逗号分隔"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "smallClassName", value = "未结案原因（小类）名称 多选用逗号分隔"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "other", value = "其他"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "duration", value = "时长（天）"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = "数据来源"),
                        @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "createdAt", value = "创建时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "updatedAt", value = "更新时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
                })
                @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
                @GetMapping
        public IPage<ComplaintNotCloseCaseReasonTestDTO>getByPage(
@RequestParam(value="appId",required = false) String appId,
@RequestParam(value="ownerCode",required = false) String ownerCode,
@RequestParam(value="ownerParCode",required = false) String ownerParCode,
@RequestParam(value="orgId",required = false) Integer orgId,
@RequestParam(value="id",required = false) Long id,
@RequestParam(value="complaintInfoId",required = false) Long complaintInfoId,
@RequestParam(value="object",required = false) String object,
@RequestParam(value="followTime",required = false) Date followTime,
@RequestParam(value="follower",required = false) String follower,
@RequestParam(value="bigClass",required = false) Integer bigClass,
@RequestParam(value="bigClassName",required = false) String bigClassName,
@RequestParam(value="smallClass",required = false) String smallClass,
@RequestParam(value="smallClassName",required = false) String smallClassName,
@RequestParam(value="other",required = false) String other,
@RequestParam(value="duration",required = false) Integer duration,
@RequestParam(value="dataSources",required = false) Integer dataSources,
@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
@RequestParam(value="isValid",required = false) Integer isValid,
@RequestParam(value="createdAt",required = false) Date createdAt,
@RequestParam(value="updatedAt",required = false) Date updatedAt,
        @RequestParam("currentPage")int currentPage,
        @RequestParam("pageSize")int pageSize){
        Page<ComplaintNotCloseCaseReasonTestPO>page=new Page(currentPage,pageSize);

                    ComplaintNotCloseCaseReasonTestDTO complaintNotCloseCaseReasonTestDTO =new ComplaintNotCloseCaseReasonTestDTO();
                    complaintNotCloseCaseReasonTestDTO.setAppId(appId);
                    complaintNotCloseCaseReasonTestDTO.setOwnerCode(ownerCode);
                    complaintNotCloseCaseReasonTestDTO.setOwnerParCode(ownerParCode);
                    complaintNotCloseCaseReasonTestDTO.setOrgId(orgId);
                    complaintNotCloseCaseReasonTestDTO.setId(id);
                    complaintNotCloseCaseReasonTestDTO.setComplaintInfoId(complaintInfoId);
                    complaintNotCloseCaseReasonTestDTO.setObject(object);
                    complaintNotCloseCaseReasonTestDTO.setFollowTime(followTime);
                    complaintNotCloseCaseReasonTestDTO.setFollower(follower);
                    complaintNotCloseCaseReasonTestDTO.setBigClass(bigClass);
                    complaintNotCloseCaseReasonTestDTO.setBigClassName(bigClassName);
                    complaintNotCloseCaseReasonTestDTO.setSmallClass(smallClass);
                    complaintNotCloseCaseReasonTestDTO.setSmallClassName(smallClassName);
                    complaintNotCloseCaseReasonTestDTO.setOther(other);
                    complaintNotCloseCaseReasonTestDTO.setDuration(duration);
                    complaintNotCloseCaseReasonTestDTO.setDataSources(dataSources);
                    complaintNotCloseCaseReasonTestDTO.setIsDeleted(isDeleted);
                    complaintNotCloseCaseReasonTestDTO.setIsValid(isValid);
                    complaintNotCloseCaseReasonTestDTO.setCreatedAt(createdAt);
                    complaintNotCloseCaseReasonTestDTO.setUpdatedAt(updatedAt);
                return complaintNotCloseCaseReasonService.selectPageBysql3(page,complaintNotCloseCaseReasonTestDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonDTO
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public ComplaintNotCloseCaseReasonDTO getById(@PathVariable("id") Long id){
        return complaintNotCloseCaseReasonService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param complaintNotCloseCaseReasonDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintNotCloseCaseReasonDTO", name = "complaintNotCloseCaseReasonDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO){
        return complaintNotCloseCaseReasonService.insert( complaintNotCloseCaseReasonDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param complaintNotCloseCaseReasonDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintNotCloseCaseReasonDTO", name = "complaintNotCloseCaseReasonDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO){
        return complaintNotCloseCaseReasonService.update(id,complaintNotCloseCaseReasonDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    complaintNotCloseCaseReasonService.deleteById(id);
        return true;
        }

    /**
     * 新增5日未结案原因（店端）
     *
     * @param reasonsforFiveDto 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ReasonsforFiveDto", name = "reasonsforFiveDto", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "新增5日未结案原因（店端）", notes = "新增5日未结案原因（店端）", httpMethod = "POST")
    @RequestMapping(value = "/insertReasonsforFive", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertReasonsforFive(@RequestBody ReasonsforFiveDto reasonsforFiveDto){
        return complaintNotCloseCaseReasonService.insertReasonsforFive(reasonsforFiveDto);
    }
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "complaintInfoId", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @RequestMapping(value = "/selectReasonsforFive", method = RequestMethod.GET)
    public IPage<ComplaintNotCloseCaseReasonTestDTO>selectReasonsforFive(
            @RequestParam(value="complaintInfoId",required = false) Long complaintInfoId,
            @RequestParam("currentPage")int currentPage,
            @RequestParam("pageSize")int pageSize){
        Page<ComplaintNotCloseCaseReasonTestPO>page=new Page(currentPage,pageSize);
        ComplaintNotCloseCaseReasonTestDTO complaintNotCloseCaseReasonTestDTO =new ComplaintNotCloseCaseReasonTestDTO();
        complaintNotCloseCaseReasonTestDTO.setComplaintInfoId(complaintInfoId);
        return complaintNotCloseCaseReasonService.selectPageBysql3(page,complaintNotCloseCaseReasonTestDTO);
    }




}