package com.yonyou.dmscus.customer.controller.complaint.sale;

import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintCustomFieldUseDTO;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintCustomFieldUseService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-12-14
 */
@Api(value = "/saleComplaintCustomFieldUse", tags = {"SaleComplaintCustomFieldUseController"})
@RestController
@RequestMapping("/saleComplaintCustomFieldUse" )
public class SaleComplaintCustomFieldUseController extends BaseController {

    @Autowired
    SaleComplaintCustomFieldUseService saleComplaintCustomFieldUseService;

    /**
     * 进行数据新增(自定义搜索条件)
     *
     * @param complaintCustomFieldTestDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-22
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ComplaintCustomFieldTestDTO", name = "complaintCustomFieldTestDTO", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "进行数据新增(自定义搜索条件)", notes = "进行数据新增(自定义搜索条件)", httpMethod = "POST")
    @RequestMapping(value = "/insertFied", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertFied(@RequestBody ComplaintCustomFieldTestDTO complaintCustomFieldTestDTO) {
        return saleComplaintCustomFieldUseService.insertFied(complaintCustomFieldTestDTO);
    }

    /**
     * 查询自定义搜索条件
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-14
     */
    @ApiOperation(value = "查询自定义搜索条件", notes = "查询自定义搜索条件", httpMethod = "GET")
    @RequestMapping(value = "/queryField", method = RequestMethod.GET)
    public List<SaleComplaintCustomFieldUseDTO> queryField(){
        SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO =new SaleComplaintCustomFieldUseDTO();
        saleComplaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
        saleComplaintCustomFieldUseDTO.setIsDeleted(0);
        saleComplaintCustomFieldUseDTO.setIsQuery(true);
        return saleComplaintCustomFieldUseService.selectListBySql(saleComplaintCustomFieldUseDTO);
    }

    /**
     * 进行数据新增(排序)
     *
     * @param  sortList 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-04-22
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<SaleComplaintCustomFieldUseDTO>", name = "sortList", value = "需要保存的DTO", required = true)
    })
    @ApiOperation(value = "进行数据新增(排序)", notes = "进行数据新增(排序)", httpMethod = "POST")
    @RequestMapping(value = "/insertSort", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int insertSort(@RequestBody List<SaleComplaintCustomFieldUseDTO> sortList ){
        return saleComplaintCustomFieldUseService.insertSort(sortList);
    }

    /**
     * 查询自定义排序
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-14
     */
    @ApiOperation(value = "查询自定义排序", notes = "查询自定义排序", httpMethod = "GET")
    @RequestMapping(value = "/querySort", method = RequestMethod.GET)
    public List<SaleComplaintCustomFieldUseDTO> querySort(){

        SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO =new SaleComplaintCustomFieldUseDTO();
        saleComplaintCustomFieldUseDTO.setUserId(FrameworkUtil.getLoginInfo().getUserId());
        saleComplaintCustomFieldUseDTO.setIsDeleted(0);
        saleComplaintCustomFieldUseDTO.setIsSort(1);
        return saleComplaintCustomFieldUseService.selectListBySql(saleComplaintCustomFieldUseDTO);
    }

    /**
     * 自定义排序重置
     *
     * @return int
     * <AUTHOR>
     * @since 2020-04-22
     */
    @ApiOperation(value = "自定义排序重置", notes = "自定义排序重置", httpMethod = "POST")
    @RequestMapping(value = "/resetFied", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public int  resetFied(){
        return saleComplaintCustomFieldUseService.resetFied();
    }

}