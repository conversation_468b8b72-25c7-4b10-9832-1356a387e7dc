package com.yonyou.dmscus.customer.controller.lossCleanTask;

import com.yonyou.dmscus.customer.service.invitationautocreate.InvVehLossCleanTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 流失客户任务数据修复 - 刷数据
 */
@Api(value = "/invVehLossCleanTask/csrf", tags = {"流失客户任务数据修复 - 刷数据"})
@RestController
@RequestMapping("/invVehLossCleanTask/csrf")
public class InvVehLossCleanTaskController {

    @Autowired
    private InvVehLossCleanTaskService invVehLossCleanTaskService;

    /**
     * 流失任务清洗-未下发的定保任务 (修复数据,一次性功能)
     */
    @ApiOperation(value = "流失任务清洗-未下发的定保任务 (修复数据,一次性功能)", notes = "流失任务清洗-未下发的定保任务 (修复数据,一次性功能)", httpMethod = "POST")
    @PostMapping("/doCleanLossTaskByDis")
    public void doCleanLossTaskByDis() {
        invVehLossCleanTaskService.doCleanLossTaskByDis();
    }

    /**
     * 流失任务清洗-未完成的定保线索 (修复数据,一次性功能)
     */
    @ApiOperation(value = "流失任务清洗-未完成的定保线索 (修复数据,一次性功能)", notes = "流失任务清洗-未完成的定保线索 (修复数据,一次性功能)", httpMethod = "POST")
    @PostMapping("/doCleanLossTaskByUnf")
    public void doCleanLossTaskByUnf() {
        invVehLossCleanTaskService.doCleanLossTaskByUnf();
    }

    /**
     * 流失任务清洗-逾期的定保线索 (修复数据,一次性功能)
     */
    @ApiOperation(value = "流失任务清洗-逾期的定保线索 (修复数据,一次性功能)", notes = "流失任务清洗-逾期的定保线索 (修复数据,一次性功能)", httpMethod = "POST")
    @PostMapping("/doCleanLossTaskBySli")
    public void doCleanLossTaskBySli() {
        invVehLossCleanTaskService.doCleanLossTaskBySli();
    }

    /**
     * 流失任务新增时,没有生成扩展表 (修复数据,一次性功能)
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "startDate", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "endDate", value = "", required = true)
    })
    @ApiOperation(value = "流失任务新增时,没有生成扩展表 (修复数据,一次性功能)", notes = "流失任务新增时,没有生成扩展表 (修复数据,一次性功能)", httpMethod = "GET")
    @GetMapping("/addInviteVehicleTaskRecord")
    public void addInviteVehicleTaskRecord(@RequestParam(value = "startDate") String startDate,
                                           @RequestParam(value = "endDate") String endDate) {
        invVehLossCleanTaskService.addInviteVehicleTaskRecord(startDate, endDate);
    }

}