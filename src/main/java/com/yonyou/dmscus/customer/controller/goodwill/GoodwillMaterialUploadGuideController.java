package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMaterialUploadGuideDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMaterialUploadGuidePO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillMaterialUploadGuideService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;


/**
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
@Api(value = "/goodwillMaterialUploadGuide", tags = {"GoodwillMaterialUploadGuideController"})
@RestController
@RequestMapping("/goodwillMaterialUploadGuide")
public class GoodwillMaterialUploadGuideController {
    
        @Autowired
        GoodwillMaterialUploadGuideService goodwillMaterialUploadGuideService;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码
        * @param ownerParCode 所有者的父组织代码
        * @param orgId 组织ID
        * @param id 主键id
        * @param content 指南内容
        * @param isValid 是否有效
        * @param isDeleted 是否删除:1,删除；0,未删除
        * @param createdAt 创建时间
        * @param updatedAt 修改时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @throws ParseException 
         * @since 2020-04-18
        */
        @ApiImplicitParams({
				@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
				@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
				@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
				@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
				@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键id"),
				@ApiImplicitParam(paramType = "query", dataType = "string", name = "content", value = "指南内容"),
				@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
				@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除:1,删除；0,未删除"),
				@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = "创建时间"),
				@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = "修改时间"),
				@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
				@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
		})
		@ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
		@GetMapping
        public IPage<GoodwillMaterialUploadGuideDTO>getByPage(
		@RequestParam(value="appId",required = false) String appId,
		@RequestParam(value="ownerCode",required = false) String ownerCode,
		@RequestParam(value="ownerParCode",required = false) String ownerParCode,
		@RequestParam(value="orgId",required = false) Integer orgId,
		@RequestParam(value="id",required = false) Long id,
		@RequestParam(value="content",required = false) String content,
		@RequestParam(value="isValid",required = false) Integer isValid,
		@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
		@RequestParam(value="createdAt",required = false) String createdAt,
		@RequestParam(value="updatedAt",required = false) String updatedAt,
        @RequestParam("currentPage")int currentPage,
        @RequestParam("pageSize")int pageSize) throws ParseException{
        Page<GoodwillMaterialUploadGuidePO>page=new Page(currentPage,pageSize);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO =new GoodwillMaterialUploadGuideDTO();
        goodwillMaterialUploadGuideDTO.setAppId(appId);
        goodwillMaterialUploadGuideDTO.setOwnerCode(ownerCode);
        goodwillMaterialUploadGuideDTO.setOwnerParCode(ownerParCode);
        goodwillMaterialUploadGuideDTO.setOrgId(orgId);
        goodwillMaterialUploadGuideDTO.setId(id);
        goodwillMaterialUploadGuideDTO.setContent(content);
        goodwillMaterialUploadGuideDTO.setIsValid(isValid);
        goodwillMaterialUploadGuideDTO.setIsDeleted(isDeleted);
        if(createdAt != null){
        	goodwillMaterialUploadGuideDTO.setCreatedAt(sdf.parse(createdAt));
        }
        if(updatedAt != null){
        	goodwillMaterialUploadGuideDTO.setUpdatedAt(sdf.parse(updatedAt));
        }
                return goodwillMaterialUploadGuideService.selectPageBysql(page,goodwillMaterialUploadGuideDTO);
        }

        /**
         *  全量查询
         * <AUTHOR>
         * @since 2020-03-20
         */
        @ApiImplicitParams({
				@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "")
		})
		@ApiOperation(value = "全量查询", notes = "全量查询", httpMethod = "GET")
		@GetMapping(value = "/list")
        public GoodwillMaterialUploadGuideDTO getList(
                @RequestParam(value="isValid",required = false) Integer isValid
        ){
        	GoodwillMaterialUploadGuideDTO inviteRuleDTO = new GoodwillMaterialUploadGuideDTO();
        	inviteRuleDTO.setIsValid(isValid);
            return goodwillMaterialUploadGuideService.selecMaterialtInfo(inviteRuleDTO);
        }
        
		/**
		 * 进行数据修改
		 *
		 * @param id 数据主键ID
		 * @return com.yonyou.dmscus.customer.entity.dto.GoodwillMaterialUploadGuideDTO
		 * <AUTHOR>
		 * @since 2020-04-18
		 */
		@ApiImplicitParams({
				@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
		})
		@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
		@GetMapping(value = "/{id}")
		public GoodwillMaterialUploadGuideDTO getById(@PathVariable("id") Long id){
			return goodwillMaterialUploadGuideService.getById(id);
        }

		/**
		 * 进行数据新增
		 *
		 * @param goodwillMaterialUploadGuideDTO 需要保存的DTO
		 * @return int
		 * <AUTHOR>
		 * @since 2020-04-18
		 */
		@ApiImplicitParams({
				@ApiImplicitParam(paramType = "body", dataType = "GoodwillMaterialUploadGuideDTO", name = "goodwillMaterialUploadGuideDTO", value = "需要保存的DTO", required = true)
		})
		@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
		@PostMapping
		@ResponseStatus(HttpStatus.CREATED)
		public int insert(@RequestBody GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO){
			 try {
		         return  goodwillMaterialUploadGuideService.updateList(goodwillMaterialUploadGuideDTO);
		     }catch (Exception e){
		         return 0;
		     }
			
        }

		/**
		 * 进行数据修改
		 *
		 * @param id 需要修改数据的ID
		 * @param goodwillMaterialUploadGuideDTO 需要保存的DTO
		 * @return int
		 * <AUTHOR>
		 * @since 2020-04-18
		 */
		@ApiImplicitParams({
				@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
				@ApiImplicitParam(paramType = "body", dataType = "GoodwillMaterialUploadGuideDTO", name = "goodwillMaterialUploadGuideDTO", value = "需要保存的DTO", required = true)
		})
		@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
		@PutMapping(value = "/{id}")
		@ResponseStatus(HttpStatus.CREATED)
		public int update(@PathVariable("id") Long id,@RequestBody GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO){
		        return goodwillMaterialUploadGuideService.update(id,goodwillMaterialUploadGuideDTO);
        }

		/**
		 * 根据id删除对象
		 *
		 * @param id 需要修改数据的ID
		 * <AUTHOR>
		 * @since 2020-04-18
		 */
		@ApiImplicitParams({
				@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
		})
		@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
		@DeleteMapping(value = "/{id}")
		@ResponseStatus(HttpStatus.NO_CONTENT)
		public boolean deleteById(@PathVariable("id") Long id){
			goodwillMaterialUploadGuideService.deleteById(id);
			return true;
        }

        /**
         * 根据IDs批量删除对象
         *
         * @param ids 需要修改数据的ID集合
         * <AUTHOR>
         * @since 2020-04-18
         */
        @ApiImplicitParams({
				@ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "需要修改数据的ID集合", required = true)
		})
		@ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
		@DeleteMapping(value = "/batch/{ids}")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        public boolean deleteByIds(@PathVariable("ids") String ids){
            goodwillMaterialUploadGuideService.deleteBatchIds(ids);
            return true;
         }

}