package com.yonyou.dmscus.customer.controller.voc;

import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocInviteVehicleTaskRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @title: VocTaskRecordController
 * @projectName dmscus.customer
 * @date 2022/12/1915:24
 */
@RestController
@Slf4j
@RequestMapping("/voctask")
@Api(value = "保存工单开单时间")
public class VocTaskRecordController {

@Autowired
private VocInviteVehicleTaskRecordService service;

    /**
     *
     * <AUTHOR>
     *  @date 2022/12/1915:24
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "orderTime", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "code", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/setVocOrderTime/interf")
    public void  setVocOrderTime(@RequestParam("orderTime") String orderTime, @RequestParam("vin") String vin,@RequestParam("code") String code) {
        service.setVocOrderTime(orderTime,vin,code);
    }
    /**
     *
     * <AUTHOR>
     *  @date 2022/12/1915:24
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "code", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/deleteVocOrderTime/interf")
    public void  deleteVocOrderTime( @RequestParam("vin") String vin,@RequestParam("code") String code) {
        log.info("deleteVocOrderTime vin:{},code:{}",vin,code);
        service.deleteVocOrderTime(vin,code);
    }

}
