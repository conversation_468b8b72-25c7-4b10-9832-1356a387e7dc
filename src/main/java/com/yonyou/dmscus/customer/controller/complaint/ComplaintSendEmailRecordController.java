package com.yonyou.dmscus.customer.controller.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscus.customer.dto.EmailInfoDto;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailRecordDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintSendEmailRecordPO;
import com.yonyou.dmscus.customer.service.CommonService;
import com.yonyou.dmscus.customer.service.complaint.ComplaintSendEmailRecordService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
@Api(value = "/complaintSendEmailRecord", tags = {"ComplaintSendEmailRecordController"})
@RestController
@RequestMapping("/complaintSendEmailRecord")
                public class ComplaintSendEmailRecordController extends BaseController {
    
        @Autowired
        ComplaintSendEmailRecordService complaintSendEmailRecordService;

        @Autowired
        CommonService commonService;

        /**
     * 分页查询数据
     *
     * @param sendTime 发送时间
     * @param sendStatus 发送状态 发送成功、发送失败
     * @param title 标题
     * @param contect 主要内容
     * @param currentPage 页数
     * @param pageSize 分页大小
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-06-11
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "assistDepartment", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "assistDealerCode", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "sendTime", value = "发送时间"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "sendStatus", value = "发送状态 发送成功、发送失败"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "sendEmail", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "title", value = "标题"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "contect", value = "主要内容"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createdStartAt", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "createdEndAt", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
    })
    @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
    @RequestMapping(value = "/selectEmail", method = RequestMethod.GET)
    public IPage<ComplaintSendEmailRecordDTO>selectEmail(
            @RequestParam(value="assistDepartment",required = false) String assistDepartment,
            @RequestParam(value="assistDealerCode",required = false) String assistDealerCode,
            @RequestParam(value="sendTime",required = false) Date sendTime,
            @RequestParam(value="sendStatus",required = false) Integer sendStatus,
            @RequestParam(value="sendEmail",required = false) String sendEmail,
            @RequestParam(value="title",required = false) String title,
            @RequestParam(value="contect",required = false) String contect,
            @RequestParam(value="createdStartAt",required = false) String createdStartAt,
            @RequestParam(value="createdEndAt",required = false) String createdEndAt,
//            @RequestParam(value = "sendTimeList", required = false) String[] sendTimeList,
            @RequestParam("currentPage")int currentPage,
            @RequestParam("pageSize")int pageSize) throws ParseException {
        Page<ComplaintSendEmailRecordPO>page=new Page(currentPage,pageSize);
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO =new ComplaintSendEmailRecordDTO();
            if(!StringUtils.isNullOrEmpty(createdStartAt)){
                String sendTime1 = createdStartAt;
                Date createdAt = sf.parse(sendTime1);
                complaintSendEmailRecordDTO.setCreatedAt(createdAt);
            }
            if(!StringUtils.isNullOrEmpty(createdEndAt)){
                String sendTime2 = createdEndAt;
                Date updatedAt = sf.parse(sendTime2);
                complaintSendEmailRecordDTO.setUpdatedAt(updatedAt);

            }
        complaintSendEmailRecordDTO.setAssistDepartment(assistDepartment);
        complaintSendEmailRecordDTO.setAssistDealerCode(assistDealerCode);
        complaintSendEmailRecordDTO.setSendEmail(sendEmail);
        complaintSendEmailRecordDTO.setSendTime(sendTime);
        complaintSendEmailRecordDTO.setSendStatus(sendStatus);
        complaintSendEmailRecordDTO.setTitle(title);
        complaintSendEmailRecordDTO.setContect(contect);
        return complaintSendEmailRecordService.selectPageBysql(page,complaintSendEmailRecordDTO);
    }

    /**
     * List查询数据
     *
     * @param sendTime 发送时间
     * @param sendStatus 发送状态 发送成功、发送失败
     * @param title 标题
     * @param contect 主要内容
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-06-11
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "assistDepartment", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "assistDealerCode", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "Date", name = "sendTime", value = "发送时间"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "sendStatus", value = "发送状态 发送成功、发送失败"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "sendEmail", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "title", value = "标题"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "contect", value = "主要内容"),
            @ApiImplicitParam(paramType = "query", dataType = "String[]", name = "sendTimeList", value = "")
    })
    @ApiOperation(value = "List查询数据", notes = "List查询数据", httpMethod = "GET")
    @RequestMapping(value = "/selectEmailList", method = RequestMethod.GET)
    public List<ComplaintSendEmailRecordDTO>selectEmailList(
            @RequestParam(value="assistDepartment",required = false) String assistDepartment,
            @RequestParam(value="assistDealerCode",required = false) String assistDealerCode,
            @RequestParam(value="sendTime",required = false) Date sendTime,
            @RequestParam(value="sendStatus",required = false) Integer sendStatus,
            @RequestParam(value="sendEmail",required = false) String sendEmail,
            @RequestParam(value="title",required = false) String title,
            @RequestParam(value="contect",required = false) String contect,
            @RequestParam(value = "sendTimeList", required = false) String[] sendTimeList) throws ParseException {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO =new ComplaintSendEmailRecordDTO();
        if (sendTimeList != null) {
            String sendTime1 = sendTimeList[0];
            String sendTime2 = sendTimeList[1];
            Date createdAt = sf.parse(sendTime1);
            Date updatedAt = sf.parse(sendTime2);
            complaintSendEmailRecordDTO.setCreatedAt(createdAt);
            complaintSendEmailRecordDTO.setUpdatedAt(updatedAt);
        }
        complaintSendEmailRecordDTO.setAssistDepartment(assistDepartment);
        complaintSendEmailRecordDTO.setAssistDealerCode(assistDealerCode);
        complaintSendEmailRecordDTO.setSendEmail(sendEmail);
        complaintSendEmailRecordDTO.setSendTime(sendTime);
        complaintSendEmailRecordDTO.setSendStatus(sendStatus);
        complaintSendEmailRecordDTO.setTitle(title);
        complaintSendEmailRecordDTO.setContect(contect);
        return complaintSendEmailRecordService.selectListBySql(complaintSendEmailRecordDTO);
    }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailRecordDTO
 * <AUTHOR>
 * @since 2020-06-11
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public ComplaintSendEmailRecordDTO getById(@PathVariable("id") Long id){
        return complaintSendEmailRecordService.getById(id);
        }

/**
 * 发送邮件
 *
 * @param complaintSendEmailRecordDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-06-11
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintSendEmailRecordDTO", name = "complaintSendEmailRecordDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "发送邮件", notes = "发送邮件", httpMethod = "POST")
@RequestMapping(value = "/insertEmail", method = RequestMethod.POST)
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO){
    EmailInfoDto emailInfoDto=new EmailInfoDto();
        emailInfoDto.setFrom("<EMAIL>");
            String[] list=new String[64];
            if (complaintSendEmailRecordDTO.getSendEmail().indexOf(";") > 0) {
                list = complaintSendEmailRecordDTO.getSendEmail().toString().split(";");
            } else {
                list = new String[1];
                list[0] = complaintSendEmailRecordDTO.getSendEmail();
            }
            emailInfoDto.setTo(list);
        emailInfoDto.setSubject(complaintSendEmailRecordDTO.getTitle());
        emailInfoDto.setText(complaintSendEmailRecordDTO.getContect());
    String flag = commonService.sendMail(emailInfoDto);
    if (flag.equals("0")) {
        complaintSendEmailRecordDTO.setSendStatus(82771001);

    } else {
        complaintSendEmailRecordDTO.setSendStatus(82771002);
    }
        return complaintSendEmailRecordService.insert( complaintSendEmailRecordDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param complaintSendEmailRecordDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-06-11
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintSendEmailRecordDTO", name = "complaintSendEmailRecordDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO){
        return complaintSendEmailRecordService.update(id,complaintSendEmailRecordDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-06-11
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    complaintSendEmailRecordService.deleteById(id);
        return true;
        }

    /**
     *查询发件人最近使用过得邮箱
     * @return
     * @throws ParseException
     */
    @ApiOperation(value = "查询发件人最近使用过得邮箱", notes = "查询发件人最近使用过得邮箱", httpMethod = "GET")
    @RequestMapping(value = "/selectLastEmail", method = RequestMethod.GET)
    public String selectLastEmail()  {
        long userId= FrameworkUtil.getLoginInfo().getUserId();
        ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO=complaintSendEmailRecordService.selectLastEmail(userId);
        String email=null;
        String[] emailList=null;
        String sendEmail=null;
        if(complaintSendEmailRecordDTO!=null){
            email=complaintSendEmailRecordDTO.getSendEmail();
            emailList = email.split(";");
            sendEmail=emailList[0];
        }

        return sendEmail ;
    }
    /**
     *查询发件人使用过得邮箱
     * @return
     * @throws ParseException
     */
    @ApiOperation(value = "查询发件人使用过得邮箱", notes = "查询发件人使用过得邮箱", httpMethod = "GET")
    @RequestMapping(value = "/selectEmaillist", method = RequestMethod.GET)
    public List selectEmaillist()  {
        long userId= FrameworkUtil.getLoginInfo().getUserId();
        List<ComplaintSendEmailRecordDTO> emailList=complaintSendEmailRecordService.selectEmaillist(userId);
        List<String> sendEmailList=new ArrayList(16);
        if(emailList.size()!=0){
            for(int j=0;j<emailList.size();j++){
                String[] split = emailList.get(j).getSendEmail().split(";");
                for (int i = 0; i < split.length; i++) {
                    sendEmailList.add(split[i]);
                }
            }
            }
        sendEmailList=sendEmailList.stream().distinct().collect(Collectors.toList());

        return sendEmailList;
    }


}
