package com.yonyou.dmscus.customer.controller.inviteInsurance;

import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceCustomerInfoDTO;
import com.yonyou.dmscus.customer.service.inviteInsurance.InviteInsuranceCustomerInfoService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2021-01-15
 */
@Api(value = "/inviteInsuranceCustomerInfo", tags = {"InviteInsuranceCustomerInfoController"})
@RestController
@RequestMapping("/inviteInsuranceCustomerInfo")
public class InviteInsuranceCustomerInfoController extends BaseController {

    @Autowired
    InviteInsuranceCustomerInfoService inviteInsuranceCustomerInfoService;

    /**
     * 保险跟进失败 -- 计划任务
     *
     * <AUTHOR>
     * @since 2020-08-31
     */

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "insuranceId", value = "", required = true)
    })
    @ApiOperation(value = "查找客户自建的联系人", notes = "查找客户自建的联系人", httpMethod = "GET")
    @GetMapping("/selectInsuranceCustomerInfo/{insuranceId}")
    public List<InviteInsuranceCustomerInfoDTO> selectAllInsuranceCustomerInfo(@PathVariable("insuranceId") Long insuranceId) {
        return inviteInsuranceCustomerInfoService.selectAllInsuranceCustomerInfo(insuranceId);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteInsuranceCustomerInfoDTO", name = "infoDTO", value = "", required = true)
    })
    @ApiOperation(value = "修改客户自建的联系人", notes = "修改客户自建的联系人", httpMethod = "POST")
    @PostMapping("/updateInsuranceCustomerInfo")
    public void updateInsuranceCustomerInfo(@RequestBody InviteInsuranceCustomerInfoDTO infoDTO) {
        inviteInsuranceCustomerInfoService.updateInsuranceCustomerInfo(infoDTO);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "tiicId", value = "", required = true)
    })
    @ApiOperation(value = "删除客户自建的联系人", notes = "删除客户自建的联系人", httpMethod = "GET")
    @GetMapping("/deleteInsuranceCustomerInfo/{tiicId}")
    public void deleteInsuranceCustomerInfo(@PathVariable("tiicId") Long tiicId) {
        inviteInsuranceCustomerInfoService.deleteInsuranceCustomerInfo(tiicId);
    }

}