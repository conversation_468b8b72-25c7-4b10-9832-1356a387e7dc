package com.yonyou.dmscus.customer.controller.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.TeComplaintDealerCcmRefImportDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.TeComplaintDealerCcmRefImportPO;
import com.yonyou.dmscus.customer.service.complaint.TeComplaintDealerCcmRefImportService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;


/**
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Api(value = "/teComplaintDealerCcmRefImport", tags = {"TeComplaintDealerCcmRefImportController"})
@RestController
@RequestMapping("/teComplaintDealerCcmRefImport")
                public class TeComplaintDealerCcmRefImportController extends BaseController {
    
        @Autowired
        TeComplaintDealerCcmRefImportService teComplaintDealerCcmRefImportService;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码 
        * @param ownerParCode 所有者的父组织代码 
        * @param orgId 组织ID
        * @param id 主键ID
        * @param dealerCode 进销商代码
        * @param dealerName 进销商名称
        * @param ccmMan CCM负责人
        * @param isError 导入是否错误
        * @param errorMsg 错误信息
        * @param lineNumber 行数
        * @param dataSources 数据来源 
        * @param isDeleted 是否删除
        * @param isValid 是否有效 
        * @param createdAt 创建时间
        * @param updatedAt 更新时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @since 2020-05-28
        */
                @ApiImplicitParams({
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "进销商代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerName", value = "进销商名称"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ccmMan", value = "CCM负责人"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "isError", value = "导入是否错误"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "errorMsg", value = "错误信息"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "lineNumber", value = "行数"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = "数据来源"),
                        @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "createdAt", value = "创建时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "updatedAt", value = "更新时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
                })
                @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
                @GetMapping
        public IPage<TeComplaintDealerCcmRefImportDTO>getByPage(
@RequestParam(value="appId",required = false) String appId,
@RequestParam(value="ownerCode",required = false) String ownerCode,
@RequestParam(value="ownerParCode",required = false) String ownerParCode,
@RequestParam(value="orgId",required = false) Integer orgId,
@RequestParam(value="id",required = false) Long id,
@RequestParam(value="dealerCode",required = false) String dealerCode,
@RequestParam(value="dealerName",required = false) String dealerName,
@RequestParam(value="ccmMan",required = false) String ccmMan,
@RequestParam(value="isError",required = false) Integer isError,
@RequestParam(value="errorMsg",required = false) String errorMsg,
@RequestParam(value="lineNumber",required = false) Integer lineNumber,
@RequestParam(value="dataSources",required = false) Integer dataSources,
@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
@RequestParam(value="isValid",required = false) Integer isValid,
@RequestParam(value="createdAt",required = false) Date createdAt,
@RequestParam(value="updatedAt",required = false) Date updatedAt,
        @RequestParam("currentPage")int currentPage,
        @RequestParam("pageSize")int pageSize){
        Page<TeComplaintDealerCcmRefImportPO>page=new Page(currentPage,pageSize);

        TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO =new TeComplaintDealerCcmRefImportDTO();
                                            teComplaintDealerCcmRefImportDTO.setAppId(appId);
                                            teComplaintDealerCcmRefImportDTO.setOwnerCode(ownerCode);
                                            teComplaintDealerCcmRefImportDTO.setOwnerParCode(ownerParCode);
                                            teComplaintDealerCcmRefImportDTO.setOrgId(orgId);
                                            teComplaintDealerCcmRefImportDTO.setId(id);
                                            teComplaintDealerCcmRefImportDTO.setDealerCode(dealerCode);
                                            teComplaintDealerCcmRefImportDTO.setDealerName(dealerName);
                                            teComplaintDealerCcmRefImportDTO.setCcmMan(ccmMan);
                                            teComplaintDealerCcmRefImportDTO.setIsError(isError);
                                            teComplaintDealerCcmRefImportDTO.setErrorMsg(errorMsg);
                                            teComplaintDealerCcmRefImportDTO.setLineNumber(lineNumber);
                                            teComplaintDealerCcmRefImportDTO.setDataSources(dataSources);
                                            teComplaintDealerCcmRefImportDTO.setIsDeleted(isDeleted);
                                            teComplaintDealerCcmRefImportDTO.setIsValid(isValid);
                                            teComplaintDealerCcmRefImportDTO.setCreatedAt(createdAt);
                                            teComplaintDealerCcmRefImportDTO.setUpdatedAt(updatedAt);
                return teComplaintDealerCcmRefImportService.selectPageBysql(page,teComplaintDealerCcmRefImportDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.complaint.TeComplaintDealerCcmRefImportDTO
 * <AUTHOR>
 * @since 2020-05-28
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public TeComplaintDealerCcmRefImportDTO getById(@PathVariable("id") Long id){
        return teComplaintDealerCcmRefImportService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param teComplaintDealerCcmRefImportDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-05-28
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "TeComplaintDealerCcmRefImportDTO", name = "teComplaintDealerCcmRefImportDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO){
        return teComplaintDealerCcmRefImportService.insert( teComplaintDealerCcmRefImportDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param teComplaintDealerCcmRefImportDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-05-28
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "TeComplaintDealerCcmRefImportDTO", name = "teComplaintDealerCcmRefImportDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO){
        return teComplaintDealerCcmRefImportService.update(id,teComplaintDealerCcmRefImportDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-05-28
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    teComplaintDealerCcmRefImportService.deleteById(id);
        return true;
        }


}