package com.yonyou.dmscus.customer.controller.maintaininfo;

import com.yonyou.dmscus.customer.entity.dto.maintaininfo.RecMaintainInfoDTO;
import com.yonyou.dmscus.customer.service.maintaininfo.InviteMaintainInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 */

@Api(value = "/inviteMaintainInfo", tags = {"InviteMaintainInfoController"})
@RestController
@RequestMapping("/inviteMaintainInfo")
public class InviteMaintainInfoController {
    @Autowired
    private InviteMaintainInfoService inviteMaintainInfoService;

    /**
     * 添加保养记录
     * @param dto
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "RecMaintainInfoDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "添加保养记录", notes = "添加保养记录", httpMethod = "POST")
    @PostMapping("/addMaintainInfo/interf")
    public void insertMaintainInfo(@RequestBody RecMaintainInfoDTO dto){
         inviteMaintainInfoService.insertMaintainInfo(dto);
    }
}
