package com.yonyou.dmscus.customer.controller.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailTemplateDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintSendEmailTemplatePO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintSendEmailTemplateService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.Date;


/**
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
@Api(value = "/complaintSendEmailTemplate", tags = {"ComplaintSendEmailTemplateController"})
@RestController
@RequestMapping("/complaintSendEmailTemplate")
                public class ComplaintSendEmailTemplateController extends BaseController {
    
        @Autowired
        ComplaintSendEmailTemplateService complaintSendEmailTemplateService;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码 
        * @param ownerParCode 所有者的父组织代码 
        * @param orgId 组织ID
        * @param id 主键ID
        * @param emailType 邮箱类型
        * @param title 标题
        * @param contect 主要内容
        * @param dataSources 数据来源 
        * @param isDeleted 是否删除
        * @param isValid 是否有效 
        * @param createdAt 创建时间
        * @param updatedAt 更新时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @since 2020-06-11
        */
                @ApiImplicitParams({
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "emailType", value = "邮箱类型"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "title", value = "标题"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "contect", value = "主要内容"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "dataSources", value = "数据来源"),
                        @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "createdAt", value = "创建时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "Date", name = "updatedAt", value = "更新时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
                })
                @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
                @GetMapping
        public IPage<ComplaintSendEmailTemplateDTO>getByPage(
@RequestParam(value="appId",required = false) String appId,
@RequestParam(value="ownerCode",required = false) String ownerCode,
@RequestParam(value="ownerParCode",required = false) String ownerParCode,
@RequestParam(value="orgId",required = false) Integer orgId,
@RequestParam(value="id",required = false) Long id,
@RequestParam(value="emailType",required = false) Integer emailType,
@RequestParam(value="title",required = false) String title,
@RequestParam(value="contect",required = false) String contect,
@RequestParam(value="dataSources",required = false) Integer dataSources,
@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
@RequestParam(value="isValid",required = false) Integer isValid,
@RequestParam(value="createdAt",required = false) Date createdAt,
@RequestParam(value="updatedAt",required = false) Date updatedAt,
        @RequestParam("currentPage")int currentPage,
        @RequestParam("pageSize")int pageSize){
        Page<ComplaintSendEmailTemplatePO>page=new Page(currentPage,pageSize);

        ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO =new ComplaintSendEmailTemplateDTO();
                                            complaintSendEmailTemplateDTO.setAppId(appId);
                                            complaintSendEmailTemplateDTO.setOwnerCode(ownerCode);
                                            complaintSendEmailTemplateDTO.setOwnerParCode(ownerParCode);
                                            complaintSendEmailTemplateDTO.setOrgId(orgId);
                                            complaintSendEmailTemplateDTO.setId(id);
                                            complaintSendEmailTemplateDTO.setEmailType(emailType);
                                            complaintSendEmailTemplateDTO.setTitle(title);
                                            complaintSendEmailTemplateDTO.setContect(contect);
                                            complaintSendEmailTemplateDTO.setDataSources(dataSources);
                                            complaintSendEmailTemplateDTO.setIsDeleted(isDeleted);
                                            complaintSendEmailTemplateDTO.setIsValid(isValid);
                                            complaintSendEmailTemplateDTO.setCreatedAt(createdAt);
                                            complaintSendEmailTemplateDTO.setUpdatedAt(updatedAt);
                return complaintSendEmailTemplateService.selectPageBysql(page,complaintSendEmailTemplateDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailTemplateDTO
 * <AUTHOR>
 * @since 2020-06-11
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public ComplaintSendEmailTemplateDTO getById(@PathVariable("id") Long id){
        return complaintSendEmailTemplateService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param complaintSendEmailTemplateDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-06-11
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintSendEmailTemplateDTO", name = "complaintSendEmailTemplateDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@RequestMapping(value = "/insertEmailTemplate", method = RequestMethod.POST)
@ResponseStatus(HttpStatus.CREATED)
public int insertEmailTemplate(@RequestBody ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO){
        return complaintSendEmailTemplateService.insertEmailTemplate( complaintSendEmailTemplateDTO);
        }

    /**
     *查询邮箱模板
     * @return
     * @throws ParseException
     */
    @ApiOperation(value = "查询邮箱模板", notes = "查询邮箱模板", httpMethod = "GET")
    @RequestMapping(value = "/selectLastTemplate", method = RequestMethod.GET)
    public ComplaintSendEmailTemplateDTO selectLastTemplate()  {
        long userId= FrameworkUtil.getLoginInfo().getUserId();
        return complaintSendEmailTemplateService.selectLastTemplate(userId);
    }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param complaintSendEmailTemplateDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-06-11
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "ComplaintSendEmailTemplateDTO", name = "complaintSendEmailTemplateDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO){
        return complaintSendEmailTemplateService.update(id,complaintSendEmailTemplateDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-06-11
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    complaintSendEmailTemplateService.deleteById(id);
        return true;
        }


}