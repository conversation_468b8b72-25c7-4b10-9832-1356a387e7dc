package com.yonyou.dmscus.customer.controller.inviteTag;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dto.InviteVehicleTagDTO;
import com.yonyou.dmscus.customer.dto.InviteVehicleTagParamsDTO;
import com.yonyou.dmscus.customer.dto.RecVehicleTagDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.InviteClueParamDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.InviteClueResultDTO;
import com.yonyou.dmscus.customer.service.inviteTag.InviteVehicleTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 邀约管理--标签管理
 */
@Api(value = "/inviteVehicleTag", tags = {"邀约管理--标签管理"})
@RestController
@Slf4j
@RequestMapping("/inviteVehicleTag")
public class InviteVehicleTagController {

    @Autowired
    private InviteVehicleTagService inviteVehicleTagService;

	/**
	 * 列表查询
	 * @param param
	 * @return
	 */
    @ApiOperation(value = "列表查询", notes = "列表查询", httpMethod = "GET")
    @GetMapping("/list")
    public IPage<InviteVehicleTagDTO> getInviteVehicleTag (InviteVehicleTagParamsDTO param) {
    	Page<InviteVehicleTagDTO> page = new Page<>(param.getCurrentPage(), param.getPageSize());
    	IPage<InviteVehicleTagDTO> selectPageBysql = inviteVehicleTagService.selectPageBysql(page, param);
        return selectPageBysql;
    }
    
    /**
     * 查询VIN绑定的标签
     * @param vin
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "查询VIN绑定的标签", notes = "查询VIN绑定的标签", httpMethod = "GET")
    @GetMapping("/getVinTag")
    public InviteVehicleTagDTO getVINTag(@RequestParam(value = "vin") String vin) {
    	return inviteVehicleTagService.getVinTag(vin);
    }
 
    /**
     * Excel标签导入
     * @param multipartFile
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "multipartFile", value = "", required = true)
    })
    @ApiOperation(value = "Excel标签导入", notes = "Excel标签导入", httpMethod = "POST")
    @RequestMapping(value = "/importInviteTag", method = RequestMethod.POST)
    public void importInviteTag(@RequestParam("file") MultipartFile multipartFile) {
    	 inviteVehicleTagService.importInviteTag(multipartFile);
    }
    
    /**
     * 导出
     * @param response
     * @param param
     */
    @ApiOperation(value = "导出", notes = "导出", httpMethod = "GET")
    @GetMapping("/exportInviteTag")
    public void exportInviteTag(HttpServletResponse response, InviteVehicleTagParamsDTO param) {
    	inviteVehicleTagService.exportInviteTag(response, param);
    }


    /**
     * 接收vin对应的标签信息
     * @param recVehicleTagDTO
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "RecVehicleTagDTO", name = "recVehicleTagDTO", value = "", required = true)
    })
    @ApiOperation(value = "接收vin对应的标签信息", notes = "接收vin对应的标签信息", httpMethod = "POST")
    @PostMapping("/receiveVinTags/interf")
    public void receiveVinTags(@RequestBody RecVehicleTagDTO recVehicleTagDTO) {
        inviteVehicleTagService.receiveVinTags(recVehicleTagDTO);
    }

    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/getAllTag")
    public Map<String,String> getAllTag(){
        return inviteVehicleTagService.getAllTag();
    }

    /**
     * 流失标签查询
     * @param vin
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "", required = true)
    })
    @ApiOperation(value = "流失标签查询", notes = "流失标签查询", httpMethod = "GET")
    @GetMapping("/getLossTag")
    public boolean getLossTag(@RequestParam(value = "vin") String vin,
                             @RequestParam(value = "dealerCode") String dealerCode) {
        try {
            return inviteVehicleTagService.getLossTag(vin, dealerCode);
        } catch (Exception e) {
            log.info("getLossTag Exception:{}", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 客户画像线索查询
     * @param inviteClueParamDTO
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InviteClueParamDTO", name = "inviteClueParamDTO", value = "", required = true)
    })
    @ApiOperation(value = "客户画像线索查询", notes = "客户画像线索查询", httpMethod = "POST")
    @PostMapping("/selectInviteClueTag")
    public List<InviteClueResultDTO> selectInviteClueTag(@RequestBody InviteClueParamDTO inviteClueParamDTO) {

        //todo 必传项校验

        try {
            return inviteVehicleTagService.selectInviteClueTag(inviteClueParamDTO);
        } catch (Exception e) {
            log.info("selectInviteClueTag Exception:{}", e);
            throw new RuntimeException(e);
        }
    }

}
