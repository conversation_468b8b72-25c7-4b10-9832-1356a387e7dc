package com.yonyou.dmscus.customer.controller.ownervehicle;

import com.yonyou.dmscus.customer.entity.dto.common.VehicleDTO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.common.ownervehicle.VehicleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.Optional;

import static com.yonyou.dmscus.customer.constants.CommonConstants.DICT_IS_NO;
import static com.yonyou.dmscus.customer.constants.CommonConstants.DICT_IS_YES;
import static com.yonyou.dmscus.customer.constants.CommonConstants.KEY_CUSTOMERS;


/**
 * <AUTHOR>
 * @since 2020-04-17
 */
@RestController
@RequestMapping("/vehicleInfo")
@Api(value = "车主车辆相关查询")
public class VehicleController {

    @Autowired
    VehicleService vehicleService;

    @Autowired
    private BusinessPlatformService businessPlatformService;


    /**
     * 根据vin查询车主车辆
     * @param vin
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "根据vin查询车主车辆", notes = "根据vin查询车主车辆", httpMethod = "GET")
    @GetMapping(value = "/getVehicle")
    public VehicleDTO getVehicleByVin(@RequestParam(value = "vin") String vin){
        VehicleDTO vehicle = vehicleService.getVehicleByVin(vin);
        if(Objects.nonNull(vehicle)){
            vehicle.setFleetCode(DICT_IS_NO);
            Optional.ofNullable(businessPlatformService.getVehicleByVIN(vin)).ifPresent(remotVehicle -> {
                vehicle.setFleetCode(KEY_CUSTOMERS.equalsIgnoreCase(remotVehicle.getCommonTypeOfSale()) ? DICT_IS_YES : DICT_IS_NO);
            });
        }
        return vehicle;
    }


    /**
     * 根据vin查询车主车辆
     *
     * @param vin
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "根据vin查询车主车辆", notes = "根据vin查询车主车辆", httpMethod = "GET")
    @GetMapping(value = "/getFleetCodeByVin")
    public int getFleetCodeByVin(@RequestParam(value = "vin") String vin){
        final int[] fleetCode = {DICT_IS_NO};
            Optional.ofNullable(businessPlatformService.getVehicleByVIN(vin)).ifPresent(remotVehicle -> {
                fleetCode[0] = KEY_CUSTOMERS.equalsIgnoreCase(remotVehicle.getCommonTypeOfSale()) ? DICT_IS_YES : DICT_IS_NO;
            });
        return fleetCode[0];
    }

}