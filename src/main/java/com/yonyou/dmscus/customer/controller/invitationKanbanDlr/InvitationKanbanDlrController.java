package com.yonyou.dmscus.customer.controller.invitationKanbanDlr;


import com.yonyou.dmscloud.framework.service.excel.ExcelGenerator;
import com.yonyou.dmscus.customer.entity.dto.invitationKanban.InvitationKanbanInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationKanban.InvitationKanbanQueryDTO;
import com.yonyou.dmscus.customer.service.invitationKanban.InvitationKanbanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-04-24
 */
@Api(value = "/invitationKanbanDlr", tags = {"InvitationKanbanDlrController"})
@RestController
@RequestMapping("/invitationKanbanDlr")
public class InvitationKanbanDlrController {

    @Autowired
    InvitationKanbanService invitationKanbanService;
    @Autowired
    ExcelGenerator excelGenerator;

    /**
     * 查询邀约看板
     * @param monthNo
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "monthNo", value = "")
    })
    @ApiOperation(value = "查询邀约看板", notes = "查询邀约看板", httpMethod = "GET")
    @GetMapping("/list")
    public List<InvitationKanbanInfoDTO> getInvitationKanban(
            @RequestParam(value = "monthNo", required = false) String monthNo){
        InvitationKanbanQueryDTO query = new InvitationKanbanQueryDTO();
        query.setMonthNo(monthNo);
        return invitationKanbanService.InvitationKanbanDlrService(query);
    }
}