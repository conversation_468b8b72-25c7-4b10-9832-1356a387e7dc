package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.common.AjaxResponse;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.OwnerVehicleVO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFinalPartInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairPartInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillFirstPageDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillInvoiceTitleInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillNoticeInvoiceInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwilApplyAuditProcessService;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyFinalPartInfoService;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyInfoService;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyRepairInfoService;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyRepairPartInfoService;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillAuditInfoService;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillInvoiceTitleInfoService;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillNoticeInvoiceInfoService;
import com.yonyou.dmscus.customer.util.common.StringUtils;
import com.yonyou.dmscus.customer.utils.Utills;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
/**
 * <AUTHOR>
 *
 */
/**
 * <AUTHOR>
 *
 */
/**
 * <AUTHOR>
 *
 */
@Api(value = "/goodwillApplyInfo", tags = {"GoodwillApplyInfoController"})
@RestController
@RequestMapping("/goodwillApplyInfo")
public class GoodwillApplyInfoController {

	@Autowired
	GoodwillApplyInfoService goodwillApplyInfoService;

	@Autowired
	GoodwillApplyRepairInfoService goodwillApplyRepairInfoService;

	@Autowired
	GoodwillApplyRepairPartInfoService goodwillApplyRepairPartInfoService;

	@Autowired
	GoodwillApplyFinalPartInfoService goodwillApplyFinalPartInfoService;

	@Autowired
	GoodwilApplyAuditProcessService goodwilApplyAuditProcessService;
	@Autowired
	GoodwillAuditInfoService goodwillAuditInfoService;

	@Autowired
	GoodwillNoticeInvoiceInfoService goodwillNoticeInvoiceInfoService;
	@Autowired
	GoodwillInvoiceTitleInfoService goodwillInvoiceTitleInfoService;

	/**
	 * 分页查询数据
	 *
	 * @param appId
	 *            系统ID
	 * @param ownerCode
	 *            所有者代码
	 * @param ownerParCode
	 *            所有者的父组织代码
	 * @param orgId
	 *            组织ID
	 * @param id
	 *            预申请单表id
	 * @param applyNo
	 *            预申请单号
	 * @param dealerCode
	 *            申请经销商
	 * @param auditType
	 *            审核类型
	 * @param goodwillNature
	 *            亲善性质
	 * @param applyTime
	 *            申请时间
	 * @param complaintFalut
	 *            投诉故障
	 * @param complaintId
	 *            投诉单ID
	 * @param complaintDate
	 *            投诉日期
	 * @param vehicleUse
	 *            用车情况
	 * @param salesDealer
	 *            销售经销商
	 * @param applyAmount
	 *            申请金额
	 * @param auditAmount
	 *            审批金额
	 * @param settlementAmount
	 *            结算金额
	 * @param invoiceAmount
	 *            通知开票金额
	 * @param goodwillStatus
	 *            亲善单状态
	 * @param customerPain
	 *            客户痛点
	 * @param vin
	 *            VIN
	 * @param license
	 *            车牌号
	 * @param customerName
	 *            客户姓名
	 * @param customerMobile
	 *            客户电话
	 * @param mileage
	 *            里程
	 * @param model
	 *            车型
	 * @param buyCarDate
	 *            购车日期
	 * @param warrantyStartDate
	 *            保修开始日期
	 * @param isExtendWarranty
	 *            是否延保
	 * @param extendWarrantyName
	 *            延保名称
	 * @param extendWarrantyStartDate
	 *            延保开始日期
	 * @param extendWarrantyEndDate
	 *            延保结束日期
	 * @param maintainCost
	 *            保养成本
	 * @param extendWarrantyCost
	 *            延保成本
	 * @param accessoryCost
	 *            附件精品成本
	 * @param voucherCost
	 *            代金券成本
	 * @param walkingCarPrice
	 *            代步车/相关利益
	 * @param volvoIntegral
	 *            沃世界积分
	 * @param returnChangeCarPrice
	 *            退换车
	 * @param otherPrice
	 *            其他
	 * @param costTotal
	 *            成本总计
	 * @param customerPay
	 *            客户支付
	 * @param dealerUndertake
	 *            经销商承担
	 * @param volvoSupportGoodwillAmount
	 *            volvo支持亲善金额
	 * @param remark
	 *            备注
	 * @param applyFile
	 *            预申请附件
	 * @param costStatisticsFile
	 *            亲善成本统计
	 * @param costScreenshotFile
	 *            亲善成本截图
	 * @param ringCheckFile
	 *            故障维修工单/环检单
	 * @param troubleRepairRequisitionFile
	 *            故障维修领料单
	 * @param workOrderFile
	 *            亲善安装工单/领料单
	 * @param situationSettlementAgreementFile
	 *            情况说明和解协议
	 * @param supplementaryMaterialFile
	 *            退换车补充材料
	 * @param managementReviewEmailVpFile
	 *            管理层审核邮件-VP
	 * @param managementReviewEmailCeoFile
	 *            管理层审核邮件-CEO
	 * @param costUpdateFile
	 *            费用更新附件
	 * @param vcdcElseFile
	 *            VCDC其他附件
	 * @param customerIdentification
	 *            客户身份证明
	 * @param elseFile
	 *            其他附件
	 * @param commitTime
	 *            提交时间
	 * @param isValid
	 *            是否有效
	 * @param isDeleted
	 *            是否删除:1,删除；0,未删除
	 * @param createdAt
	 *            创建时间
	 * @param updatedAt
	 *            修改时间
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "dataType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCodes", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "bloc", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillNature", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintFalut", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "vehicleUse", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "salesDealer", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "auditAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "settlementAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "invoiceAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillStatus", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerPain", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerMobile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "mileage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "model", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "buyCarDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "warrantyStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isExtendWarranty", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "commitTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyStartAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyEndAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询数据", httpMethod = "GET")
	@GetMapping
	public IPage<GoodwillApplyInfoDTO> getByPage(@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "dataType", required = false) Integer dataType,
			@RequestParam(value = "ownerCodes", required = false) String ownerCodes,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "bloc", required = false) Integer bloc,
			@RequestParam(value = "areaManage", required = false) Integer areaManage,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "auditType", required = false) Integer auditType,
			@RequestParam(value = "goodwillNature", required = false) Integer goodwillNature,
			@RequestParam(value = "applyTime", required = false) String applyTime,
			@RequestParam(value = "complaintFalut", required = false) String complaintFalut,
			@RequestParam(value = "complaintId", required = false) String complaintId,
			@RequestParam(value = "complaintDate", required = false) String complaintDate,
			@RequestParam(value = "vehicleUse", required = false) Integer vehicleUse,
			@RequestParam(value = "salesDealer", required = false) String salesDealer,
			@RequestParam(value = "applyAmount", required = false) BigDecimal applyAmount,
			@RequestParam(value = "auditAmount", required = false) BigDecimal auditAmount,
			@RequestParam(value = "settlementAmount", required = false) BigDecimal settlementAmount,
			@RequestParam(value = "invoiceAmount", required = false) BigDecimal invoiceAmount,
			@RequestParam(value = "goodwillStatus", required = false) Integer goodwillStatus,
			@RequestParam(value = "customerPain", required = false) String customerPain,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "customerName", required = false) String customerName,
			@RequestParam(value = "customerMobile", required = false) String customerMobile,
			@RequestParam(value = "mileage", required = false) String mileage,
			@RequestParam(value = "model", required = false) String model,
			@RequestParam(value = "buyCarDate", required = false) String buyCarDate,
			@RequestParam(value = "warrantyStartDate", required = false) String warrantyStartDate,
			@RequestParam(value = "isExtendWarranty", required = false) Integer isExtendWarranty,
			@RequestParam(value = "extendWarrantyName", required = false) String extendWarrantyName,
			@RequestParam(value = "extendWarrantyStartDate", required = false) String extendWarrantyStartDate,
			@RequestParam(value = "extendWarrantyEndDate", required = false) String extendWarrantyEndDate,
			@RequestParam(value = "commitTime", required = false) String commitTime,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt,
			@RequestParam(value = "applyStartTime", required = false) String applyStartTime,
			@RequestParam(value = "applyEndTime", required = false) String applyEndTime,
			@RequestParam(value = "applyStartAmount", required = false) BigDecimal applyStartAmount,
			@RequestParam(value = "applyEndAmount", required = false) BigDecimal applyEndAmount,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyInfoPO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		goodwillApplyInfoDTO.setAppId(appId);
		goodwillApplyInfoDTO.setDataType(dataType);
		goodwillApplyInfoDTO.setOwnerCodes(ownerCodes);
		goodwillApplyInfoDTO.setOwnerCode(ownerCode);
		goodwillApplyInfoDTO.setOwnerParCode(ownerParCode);
		goodwillApplyInfoDTO.setOrgId(orgId);
		goodwillApplyInfoDTO.setId(id);
		goodwillApplyInfoDTO.setBlocId(bloc);
		goodwillApplyInfoDTO.setAreaManageId(areaManage);
		goodwillApplyInfoDTO.setApplyNo(applyNo);
		goodwillApplyInfoDTO.setDealerCode(dealerCode);
		goodwillApplyInfoDTO.setAuditType(auditType);
		goodwillApplyInfoDTO.setGoodwillNature(goodwillNature);
		goodwillApplyInfoDTO.setComplaintFalut(complaintFalut);
		goodwillApplyInfoDTO.setComplaintId(complaintId);
		goodwillApplyInfoDTO.setVehicleUse(vehicleUse);
		goodwillApplyInfoDTO.setSalesDealer(salesDealer);
		goodwillApplyInfoDTO.setApplyAmount(applyAmount);
		goodwillApplyInfoDTO.setAuditAmount(auditAmount);
		goodwillApplyInfoDTO.setSettlementAmount(settlementAmount);
		goodwillApplyInfoDTO.setInvoiceAmount(invoiceAmount);
		goodwillApplyInfoDTO.setGoodwillStatus(goodwillStatus);
		// goodwillApplyInfoDTO.setCustomerPain(customerPain);
		goodwillApplyInfoDTO.setVin(vin);
		goodwillApplyInfoDTO.setLicense(license);
		goodwillApplyInfoDTO.setCustomerName(customerName);
		goodwillApplyInfoDTO.setCustomerMobile(customerMobile);
		goodwillApplyInfoDTO.setMileage(mileage);
		goodwillApplyInfoDTO.setModel(model);
		goodwillApplyInfoDTO.setIsExtendWarranty(isExtendWarranty);
		goodwillApplyInfoDTO.setExtendWarrantyName(extendWarrantyName);
		goodwillApplyInfoDTO.setIsValid(isValid);
		goodwillApplyInfoDTO.setIsDeleted(isDeleted);
		goodwillApplyInfoDTO.setUserId(loginInfoDto.getUserId());
		goodwillApplyInfoDTO.setApplyStartAmount(applyStartAmount);
		goodwillApplyInfoDTO.setApplyEndAmount(applyEndAmount);
		if (applyStartTime != null) {
			goodwillApplyInfoDTO.setApplyStartTime(sdf.parse(applyStartTime));
		}
		if (applyEndTime != null) {
			goodwillApplyInfoDTO.setApplyEndTime(sdf.parse(applyEndTime));
		}
		if (createdAt != null) {
			goodwillApplyInfoDTO.setCreatedAt(sdf.parse(createdAt));
		}
		if (updatedAt != null) {
			goodwillApplyInfoDTO.setUpdatedAt(sdf.parse(updatedAt));
		}
		return goodwillApplyInfoService.selectPageBysql(page, goodwillApplyInfoDTO);
	}

	/**
	 * 获取亲善单号
	 *
	 * @param
	 * @return String
	 * <AUTHOR>
	 * @date 2019年9月17日
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "")
	})
	@ApiOperation(value = "获取亲善单号", notes = "获取亲善单号", httpMethod = "GET")
	@RequestMapping(value = "/getbillNo", method = RequestMethod.GET)
	public String queryBookingLimit(@RequestParam(value = "dealerCode", required = false) String dealerCode)
			throws ServiceBizException {
		// String dearCold = FrameworkUtil.getLoginInfo().getOwnerCode();
		return goodwillApplyInfoService.getBillNo("GW", dealerCode);
	}

	/**
	 * 查询经销商年度申请信息
	 *
	 * @param
	 * @return Map
	 * <AUTHOR>
	 * @date 2020年6月6日
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "year", value = "")
	})
	@ApiOperation(value = "查询经销商年度申请信息", notes = "查询经销商年度申请信息", httpMethod = "GET")
	@GetMapping(value = "/queryApplyByDealerCode")
	public Map queryApplyByDealerCode(@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "year", required = false) Integer year) {
		return goodwillApplyInfoService.queryApplyByDealerCode(dealerCode, year);

	}

	/**
	 * 进行数据修改
	 *
	 * @param id
	 *            数据主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO
	 * <AUTHOR>
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
	@GetMapping(value = "/{id}")
	public GoodwillApplyInfoDTO getById(@PathVariable("id") Long id) {
		return goodwillApplyInfoService.getById(id);
	}

	/**
	 * 进行数据新增
	 *
	 * @param goodwillApplyInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyInfoDTO", name = "goodwillApplyInfoDTO", value = "", required = true)
	})
	@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
	@PostMapping
	@ResponseStatus(HttpStatus.CREATED)
	public int insert(@RequestBody GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		return goodwillApplyInfoService.insert(goodwillApplyInfoDTO);
	}

	/**
	 * 提交预申请单
	 *
	 * @param goodwillApplyInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-23
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyInfoDTO", name = "goodwillApplyInfoDTO", value = "", required = true)
	})
	@ApiOperation(value = "提交预申请单", notes = "提交预申请单", httpMethod = "POST")
	@PostMapping(value = "/commitSupportApplyInfo")
	@ResponseStatus(HttpStatus.CREATED)
	public int commit(@RequestBody GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		return goodwillApplyInfoService.commitSupportApplyInfo(goodwillApplyInfoDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id
	 *            需要修改数据的ID
	 * @param goodwillApplyInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true),
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyInfoDTO", name = "goodwillApplyInfoDTO", value = "", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
	@PutMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.CREATED)
	public int update(@PathVariable("id") Long id, @RequestBody GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		return goodwillApplyInfoService.update(id, goodwillApplyInfoDTO);
	}

	/**
	 * 根据id删除对象
	 *
	 * @param id
	 *            需要修改数据的ID
	 * <AUTHOR>
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
	})
	@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteById(@PathVariable("id") Long id) {
		goodwillApplyInfoService.deleteById(id);
		return true;
	}

	/**
	 * 根据IDs批量删除对象
	 *
	 * @param ids
	 *            需要修改数据的ID集合
	 * <AUTHOR>
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "", required = true)
	})
	@ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/batch/{ids}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteByIds(@PathVariable("ids") String ids) {
		goodwillApplyInfoService.deleteBatchIds(ids);
		return true;
	}

	/**
	 * 分页查询投诉单 接口数据
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-04-22
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dataType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintDateStart", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintDateEnd", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询投诉单", notes = "分页查询投诉单", httpMethod = "GET")
	@GetMapping(value = "/queryComplaintInfo")
	@SuppressWarnings("rawtypes")
	public IPage<List> queryComplaintInfo(@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "complaintId", required = false) String complaintId,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "dataType", required = false) String dataType,
			@RequestParam(value = "complaintDateStart", required = false) String complaintDateStart,
			@RequestParam(value = "complaintDateEnd", required = false) String complaintDateEnd,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyInfoPO> page = new Page(currentPage, pageSize);
		Map map = new HashMap(16);
		map.put("license", license);
		map.put("vin", vin);
		map.put("dealerCode", dealerCode);
		//dataType: 10461001（表示经销商端）  10461003（表示厂端）
		map.put("dataType", dataType);
		map.put("complaintDateStart", complaintDateStart);
		map.put("complaintDateEnd", complaintDateEnd);
		map.put("complaintId",complaintId);
		return goodwillApplyInfoService.queryComplaintInfo(page, map);
	}

	/**
	 * 分页查询车主车辆信息 接口数据
	 *
	 * @param role
	 *            角色
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-04-22
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "companyCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerMobile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询车主车辆信息", notes = "分页查询车主车辆信息", httpMethod = "GET")
	@GetMapping(value = "/queryVehicleInfo")
	public IPage<OwnerVehicleVO> queryVehicleInfo(@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "companyCode", required = false) String companyCode,
			@RequestParam(value = "customerName", required = false) String customerName,
			@RequestParam(value = "customerMobile", required = false) String customerMobile,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyInfoPO> page = new Page(currentPage, pageSize);
		Map map = new HashMap(16);
		map.put("plateNumber", license);
		map.put("vin", vin);
		map.put("name", customerName);
		map.put("mobile", customerMobile);
		map.put("companyCode", companyCode);
		return goodwillApplyInfoService.queryVehicleInfo(page, map);
	}

	/**
	 * 查询亲善预申请维修记录
	 *
	 * @param id
	 *            主键ID
	 * @return List<GoodwillApplyRepairInfoDTO>
	 * <AUTHOR>
	 * @since 2020-05-11
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "")
	})
	@ApiOperation(value = "查询数据", notes = "查询亲善预申请维修记录            主键ID", httpMethod = "GET")
	@GetMapping("/getSupportApplyRepairInfoById/{id}")
	public List<GoodwillApplyRepairInfoDTO> getSupportApplyRepairInfoById(
			@PathVariable(value = "id", required = false) Long id) {
		return goodwillApplyRepairInfoService.getSupportApplyRepairInfoById(id);
	}

	/**
	 * 查询亲善预申请零配件信息
	 *
	 * @param id
	 *            主键ID
	 * @return List<GoodwillApplyRepairInfoDTO>
	 * <AUTHOR>
	 * @since 2020-05-11
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "")
	})
	@ApiOperation(value = "查询数据", notes = "查询亲善预申请零配件信息            主键ID", httpMethod = "GET")
	@GetMapping("/getSupportApplyPartInfoById/{id}")
	public List<GoodwillApplyRepairPartInfoDTO> getSupportApplyPartInfoById(
			@PathVariable(value = "id", required = false) Long id) {
		return goodwillApplyRepairPartInfoService.getSupportApplyPartInfoById(id);
	}

	/**
	 * 查询亲善金额及分类零配件信息
	 *
	 * @param id
	 *            主键ID
	 * @return List<GoodwillApplyRepairInfoDTO>
	 * <AUTHOR>
	 * @since 2020-05-11
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "int", name = "goodwillType", value = ""),
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "")
	})
	@ApiOperation(value = "查询数据", notes = "查询亲善金额及分类零配件信息", httpMethod = "GET")
	@GetMapping("/queryPartAmountInfo/{goodwillType}/id/{id}")
	public List<GoodwillApplyFinalPartInfoDTO> queryPartAmountInfo(
			@PathVariable(value = "goodwillType", required = false) Integer goodwillType,
			@PathVariable(value = "id", required = false) Long id) {
		return goodwillApplyFinalPartInfoService.queryPartAmountInfo(goodwillType, id);
	}

	/**
	 * 分页查询车主车辆信息 接口数据
	 *
	 * @param role
	 *            角色
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-04-22
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "partNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "partName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询车主车辆信息", notes = "分页查询车主车辆信息", httpMethod = "GET")
	@GetMapping(value = "/queryPartDetailInfo")
	public IPage<List> queryPartDetailInfo(@RequestParam(value = "partNo", required = false) String partNo,
			@RequestParam(value = "partName", required = false) String partName,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyInfoPO> page = new Page(currentPage, pageSize);
		Map map = new HashMap(16);
		map.put("partNo", partNo);
		map.put("partName", partName);
		return goodwillApplyInfoService.queryPartDetailInfo(page, map);
	}

	/**
	 * 查询零配件信息——分页查询配件信息 接口数据
	 *
	 * @param role
	 *            角色
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-04-22
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "repairNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "linense", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "partNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "partName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "orderDateStart", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "orderDateEnd", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "查询零配件信息——分页查询配件信息", notes = "查询零配件信息——分页查询配件信息", httpMethod = "GET")
	@GetMapping(value = "/queryPartInfo")
	public IPage<List> queryPartInfo(@RequestParam(value = "repairNo", required = false) String repairNo,
			@RequestParam(value = "linense", required = false) String linense,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "partNo", required = false) String partNo,
			@RequestParam(value = "partName", required = false) String partName,
			@RequestParam(value = "orderDateStart", required = false) String orderDateStart,
			@RequestParam(value = "orderDateEnd", required = false) String orderDateEnd,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyInfoPO> page = new Page(currentPage, pageSize);
		Map map = new HashMap(16);
		map.put("partNo", partNo);
		map.put("partName", partName);
		map.put("repairNo", repairNo);
		map.put("linense", linense);
		map.put("vin", vin);
		map.put("orderDateStart", orderDateStart);
		map.put("orderDateEnd", orderDateEnd);
		return goodwillApplyInfoService.queryPartInfo(page, map);
	}

	/**
	 * 分页查询维修记录——分页查询工单信息 接口数据
	 *
	 * @param role
	 *            角色
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-04-22
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "repairNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "linense", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "orderDateStart", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "orderDateEnd", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询维修记录——分页查询工单信息", notes = "分页查询维修记录——分页查询工单信息", httpMethod = "GET")
	@GetMapping(value = "/queryRepairOrderInfo")
	public IPage<List> queryRepairOrderInfo(@RequestParam(value = "repairNo", required = false) String repairNo,
			@RequestParam(value = "linense", required = false) String linense,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "orderDateStart", required = false) String orderDateStart,
			@RequestParam(value = "orderDateEnd", required = false) String orderDateEnd,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyInfoPO> page = new Page(currentPage, pageSize);
		Map map = new HashMap(16);
		map.put("repairNo", repairNo);
		map.put("linense", linense);
		map.put("vin", vin);
		map.put("orderDateStart", orderDateStart);
		map.put("orderDateEnd", orderDateEnd);
		return goodwillApplyInfoService.queryRepairOrderInfo(page, map);
	}

	/**
	 * 分页查询亲善oem待办事项数据
	 *
	 * @param appId
	 *            系统ID
	 * @param ownerCode
	 *            所有者代码
	 * @param ownerParCode
	 *            所有者的父组织代码
	 * @param orgId
	 *            组织ID
	 * @param id
	 *            预申请单表id
	 * @param applyNo
	 *            预申请单号
	 * @param dealerCode
	 *            申请经销商
	 * @param auditType
	 *            审核类型
	 * @param goodwillNature
	 *            亲善性质
	 * @param applyTime
	 *            申请时间
	 * @param complaintFalut
	 *            投诉故障
	 * @param complaintId
	 *            投诉单ID
	 * @param complaintDate
	 *            投诉日期
	 * @param vehicleUse
	 *            用车情况
	 * @param salesDealer
	 *            销售经销商
	 * @param applyAmount
	 *            申请金额
	 * @param auditAmount
	 *            审批金额
	 * @param settlementAmount
	 *            结算金额
	 * @param invoiceAmount
	 *            通知开票金额
	 * @param goodwillStatus
	 *            亲善单状态
	 * @param customerPain
	 *            客户痛点
	 * @param vin
	 *            VIN
	 * @param license
	 *            车牌号
	 * @param customerName
	 *            客户姓名
	 * @param customerMobile
	 *            客户电话
	 * @param mileage
	 *            里程
	 * @param model
	 *            车型
	 * @param buyCarDate
	 *            购车日期
	 * @param warrantyStartDate
	 *            保修开始日期
	 * @param isExtendWarranty
	 *            是否延保
	 * @param extendWarrantyName
	 *            延保名称
	 * @param extendWarrantyStartDate
	 *            延保开始日期
	 * @param extendWarrantyEndDate
	 *            延保结束日期
	 * @param maintainCost
	 *            保养成本
	 * @param extendWarrantyCost
	 *            延保成本
	 * @param accessoryCost
	 *            附件精品成本
	 * @param voucherCost
	 *            代金券成本
	 * @param walkingCarPrice
	 *            代步车/相关利益
	 * @param volvoIntegral
	 *            沃世界积分
	 * @param returnChangeCarPrice
	 *            退换车
	 * @param otherPrice
	 *            其他
	 * @param costTotal
	 *            成本总计
	 * @param customerPay
	 *            客户支付
	 * @param dealerUndertake
	 *            经销商承担
	 * @param volvoSupportGoodwillAmount
	 *            volvo支持亲善金额
	 * @param remark
	 *            备注
	 * @param applyFile
	 *            预申请附件
	 * @param costStatisticsFile
	 *            亲善成本统计
	 * @param costScreenshotFile
	 *            亲善成本截图
	 * @param ringCheckFile
	 *            故障维修工单/环检单
	 * @param troubleRepairRequisitionFile
	 *            故障维修领料单
	 * @param workOrderFile
	 *            亲善安装工单/领料单
	 * @param situationSettlementAgreementFile
	 *            情况说明和解协议
	 * @param supplementaryMaterialFile
	 *            退换车补充材料
	 * @param managementReviewEmailVpFile
	 *            管理层审核邮件-VP
	 * @param managementReviewEmailCeoFile
	 *            管理层审核邮件-CEO
	 * @param costUpdateFile
	 *            费用更新附件
	 * @param vcdcElseFile
	 *            VCDC其他附件
	 * @param customerIdentification
	 *            客户身份证明
	 * @param elseFile
	 *            其他附件
	 * @param commitTime
	 *            提交时间
	 * @param isValid
	 *            是否有效
	 * @param isDeleted
	 *            是否删除:1,删除；0,未删除
	 * @param createdAt
	 *            创建时间
	 * @param updatedAt
	 *            修改时间
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "bloc", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "smallAreaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyPerson", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillNature", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintFalut", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "String[]", name = "roleList", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "roleList1", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "String[]", name = "roleList2", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "vehicleUse", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "salesDealer", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "auditAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "settlementAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "invoiceAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillStatus", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerPain", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerMobile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "mileage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "model", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "buyCarDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "warrantyStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isExtendWarranty", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "maintainCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "extendWarrantyCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "accessoryCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "voucherCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "walkingCarPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "volvoIntegral", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "returnChangeCarPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "otherPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "costTotal", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "customerPay", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "dealerUndertake", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "volvoSupportGoodwillAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "remark", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "costStatisticsFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "costScreenshotFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ringCheckFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "troubleRepairRequisitionFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "workOrderFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "situationSettlementAgreementFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "supplementaryMaterialFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "managementReviewEmailVpFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "managementReviewEmailCeoFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "costUpdateFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vcdcElseFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerIdentification", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "elseFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "commitTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyStartAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyEndAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedStartAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询亲善oem待办事项数据", httpMethod = "GET")
	@GetMapping(value = "/querySupportApplyOemTodoInfo")
	public IPage<GoodwillApplyInfoDTO> getOemTodoByPage(@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "bloc", required = false) Integer bloc,
			@RequestParam(value = "areaManage", required = false) String areaManage,
			@RequestParam(value = "smallAreaManage", required = false) String smallAreaManage,
			@RequestParam(value = "auditName", required = false) String auditName,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "applyPerson", required = false) String applyPerson,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "auditType", required = false) Integer auditType,
			@RequestParam(value = "goodwillNature", required = false) Integer goodwillNature,
			@RequestParam(value = "applyTime", required = false) String applyTime,
			@RequestParam(value = "complaintFalut", required = false) String complaintFalut,
			@RequestParam(value = "roleList", required = false) String[] roleList,
			@RequestParam(value = "roleList1", required = false) String roleList1,
			@RequestParam(value = "roleList2", required = false) String[] roleList2,
			@RequestParam(value = "complaintId", required = false) String complaintId,
			@RequestParam(value = "complaintDate", required = false) String complaintDate,
			@RequestParam(value = "vehicleUse", required = false) Integer vehicleUse,
			@RequestParam(value = "salesDealer", required = false) String salesDealer,
			@RequestParam(value = "applyAmount", required = false) BigDecimal applyAmount,
			@RequestParam(value = "auditAmount", required = false) BigDecimal auditAmount,
			@RequestParam(value = "settlementAmount", required = false) BigDecimal settlementAmount,
			@RequestParam(value = "invoiceAmount", required = false) BigDecimal invoiceAmount,
			@RequestParam(value = "goodwillStatus", required = false) Integer goodwillStatus,
			@RequestParam(value = "customerPain", required = false) String customerPain,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "customerName", required = false) String customerName,
			@RequestParam(value = "customerMobile", required = false) String customerMobile,
			@RequestParam(value = "mileage", required = false) String mileage,
			@RequestParam(value = "model", required = false) String model,
			@RequestParam(value = "buyCarDate", required = false) String buyCarDate,
			@RequestParam(value = "warrantyStartDate", required = false) String warrantyStartDate,
			@RequestParam(value = "isExtendWarranty", required = false) Integer isExtendWarranty,
			@RequestParam(value = "extendWarrantyName", required = false) String extendWarrantyName,
			@RequestParam(value = "extendWarrantyStartDate", required = false) String extendWarrantyStartDate,
			@RequestParam(value = "extendWarrantyEndDate", required = false) String extendWarrantyEndDate,
			@RequestParam(value = "maintainCost", required = false) BigDecimal maintainCost,
			@RequestParam(value = "extendWarrantyCost", required = false) BigDecimal extendWarrantyCost,
			@RequestParam(value = "accessoryCost", required = false) BigDecimal accessoryCost,
			@RequestParam(value = "voucherCost", required = false) BigDecimal voucherCost,
			@RequestParam(value = "walkingCarPrice", required = false) BigDecimal walkingCarPrice,
			@RequestParam(value = "volvoIntegral", required = false) BigDecimal volvoIntegral,
			@RequestParam(value = "returnChangeCarPrice", required = false) BigDecimal returnChangeCarPrice,
			@RequestParam(value = "otherPrice", required = false) BigDecimal otherPrice,
			@RequestParam(value = "costTotal", required = false) BigDecimal costTotal,
			@RequestParam(value = "customerPay", required = false) BigDecimal customerPay,
			@RequestParam(value = "dealerUndertake", required = false) BigDecimal dealerUndertake,
			@RequestParam(value = "volvoSupportGoodwillAmount", required = false) BigDecimal volvoSupportGoodwillAmount,
			@RequestParam(value = "remark", required = false) String remark,
			@RequestParam(value = "applyFile", required = false) String applyFile,
			@RequestParam(value = "costStatisticsFile", required = false) String costStatisticsFile,
			@RequestParam(value = "costScreenshotFile", required = false) String costScreenshotFile,
			@RequestParam(value = "ringCheckFile", required = false) String ringCheckFile,
			@RequestParam(value = "troubleRepairRequisitionFile", required = false) String troubleRepairRequisitionFile,
			@RequestParam(value = "workOrderFile", required = false) String workOrderFile,
			@RequestParam(value = "situationSettlementAgreementFile", required = false) String situationSettlementAgreementFile,
			@RequestParam(value = "supplementaryMaterialFile", required = false) String supplementaryMaterialFile,
			@RequestParam(value = "managementReviewEmailVpFile", required = false) String managementReviewEmailVpFile,
			@RequestParam(value = "managementReviewEmailCeoFile", required = false) String managementReviewEmailCeoFile,
			@RequestParam(value = "costUpdateFile", required = false) String costUpdateFile,
			@RequestParam(value = "vcdcElseFile", required = false) String vcdcElseFile,
			@RequestParam(value = "customerIdentification", required = false) String customerIdentification,
			@RequestParam(value = "elseFile", required = false) String elseFile,
			@RequestParam(value = "commitTime", required = false) String commitTime,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt,
			@RequestParam(value = "applyStartTime", required = false) String applyStartTime,
			@RequestParam(value = "applyEndTime", required = false) String applyEndTime,
			@RequestParam(value = "applyStartAmount", required = false) BigDecimal applyStartAmount,
			@RequestParam(value = "applyEndAmount", required = false) BigDecimal applyEndAmount,
			@RequestParam(value = "passStartTime", required = false) String passStartTime,
			@RequestParam(value = "passEndTime", required = false) String passEndTime,
			@RequestParam(value = "noticeInvoiceStartDate", required = false) String noticeInvoiceStartDate,
			@RequestParam(value = "noticeInvoiceEndDate", required = false) String noticeInvoiceEndDate,
			@RequestParam(value = "updatedStartAt", required = false) String updatedStartAt,
			@RequestParam(value = "updatedEndAt", required = false) String updatedEndAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyInfoPO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		goodwillApplyInfoDTO.setAppId(appId);
		goodwillApplyInfoDTO.setOwnerCode(ownerCode);
		goodwillApplyInfoDTO.setOwnerParCode(ownerParCode);
		goodwillApplyInfoDTO.setOrgId(orgId);
		goodwillApplyInfoDTO.setId(id);
		goodwillApplyInfoDTO.setBlocId(bloc);
		goodwillApplyInfoDTO.setAreaManageId1(areaManage);
		goodwillApplyInfoDTO.setSmallAreaId1(smallAreaManage);
		goodwillApplyInfoDTO.setAuditName1(Utills.getList(auditName));
		goodwillApplyInfoDTO.setApplyNo(applyNo);
		goodwillApplyInfoDTO.setApplyPerson(applyPerson);
		goodwillApplyInfoDTO.setDealerCode(dealerCode);
		goodwillApplyInfoDTO.setAuditType(auditType);
		goodwillApplyInfoDTO.setGoodwillNature(goodwillNature);
		goodwillApplyInfoDTO.setRoleList(roleList);
		goodwillApplyInfoDTO.setRoleList1(roleList1);
		goodwillApplyInfoDTO.setRoleList2(roleList2);
		goodwillApplyInfoDTO.setComplaintFalut(complaintFalut);
		goodwillApplyInfoDTO.setComplaintId(complaintId);
		goodwillApplyInfoDTO.setVehicleUse(vehicleUse);
		goodwillApplyInfoDTO.setSalesDealer(salesDealer);
		goodwillApplyInfoDTO.setApplyAmount(applyAmount);
		goodwillApplyInfoDTO.setAuditAmount(auditAmount);
		goodwillApplyInfoDTO.setSettlementAmount(settlementAmount);
		goodwillApplyInfoDTO.setInvoiceAmount(invoiceAmount);
		goodwillApplyInfoDTO.setGoodwillStatus(goodwillStatus);
		// goodwillApplyInfoDTO.setCustomerPain(customerPain);
		goodwillApplyInfoDTO.setVin(vin);
		goodwillApplyInfoDTO.setLicense(license);
		goodwillApplyInfoDTO.setCustomerName(customerName);
		goodwillApplyInfoDTO.setCustomerMobile(customerMobile);
		goodwillApplyInfoDTO.setMileage(mileage);
		goodwillApplyInfoDTO.setModel(model);
		goodwillApplyInfoDTO.setIsExtendWarranty(isExtendWarranty);
		goodwillApplyInfoDTO.setExtendWarrantyName(extendWarrantyName);
		goodwillApplyInfoDTO.setMaintainCost(maintainCost);
		goodwillApplyInfoDTO.setExtendWarrantyCost(extendWarrantyCost);
		goodwillApplyInfoDTO.setAccessoryCost(accessoryCost);
		goodwillApplyInfoDTO.setVoucherCost(voucherCost);
		goodwillApplyInfoDTO.setWalkingCarPrice(walkingCarPrice);
		goodwillApplyInfoDTO.setVolvoIntegral(volvoIntegral);
		goodwillApplyInfoDTO.setReturnChangeCarPrice(returnChangeCarPrice);
		goodwillApplyInfoDTO.setOtherPrice(otherPrice);
		goodwillApplyInfoDTO.setCostTotal(costTotal);
		goodwillApplyInfoDTO.setCustomerPay(customerPay);
		goodwillApplyInfoDTO.setDealerUndertake(dealerUndertake);
		goodwillApplyInfoDTO.setVolvoSupportGoodwillAmount(volvoSupportGoodwillAmount);
		goodwillApplyInfoDTO.setRemark(remark);
		goodwillApplyInfoDTO.setApplyFile(applyFile);
		goodwillApplyInfoDTO.setCostStatisticsFile(costStatisticsFile);
		goodwillApplyInfoDTO.setCostScreenshotFile(costScreenshotFile);
		goodwillApplyInfoDTO.setRingCheckFile(ringCheckFile);
		goodwillApplyInfoDTO.setTroubleRepairRequisitionFile(troubleRepairRequisitionFile);
		goodwillApplyInfoDTO.setWorkOrderFile(workOrderFile);
		goodwillApplyInfoDTO.setSituationSettlementAgreementFile(situationSettlementAgreementFile);
		goodwillApplyInfoDTO.setSupplementaryMaterialFile(supplementaryMaterialFile);
		goodwillApplyInfoDTO.setManagementReviewEmailVpFile(managementReviewEmailVpFile);
		goodwillApplyInfoDTO.setManagementReviewEmailCeoFile(managementReviewEmailCeoFile);
		goodwillApplyInfoDTO.setCostUpdateFile(costUpdateFile);
		goodwillApplyInfoDTO.setVcdcElseFile(vcdcElseFile);
		goodwillApplyInfoDTO.setCustomerIdentification(customerIdentification);
		goodwillApplyInfoDTO.setElseFile(elseFile);
		goodwillApplyInfoDTO.setIsValid(isValid);
		goodwillApplyInfoDTO.setIsDeleted(isDeleted);
		goodwillApplyInfoDTO.setApplyStartAmount(applyStartAmount);
		goodwillApplyInfoDTO.setApplyEndAmount(applyEndAmount);
		if (applyStartTime != null && !"".equals(applyStartTime)) {
			goodwillApplyInfoDTO.setApplyStartTime(sdf.parse(applyStartTime));
		}
		if (applyEndTime != null && !"".equals(applyEndTime)) {
			goodwillApplyInfoDTO.setApplyEndTime(sdf.parse(applyEndTime));
		}
		if (passStartTime != null) {
			goodwillApplyInfoDTO.setPassStartTime(sdf.parse(passStartTime));
		}
		if (passEndTime != null) {
			goodwillApplyInfoDTO.setPassEndTime(sdf.parse(passEndTime));
		}
		if (noticeInvoiceStartDate != null) {
			goodwillApplyInfoDTO.setNoticeInvoiceStartDate(sdf.parse(noticeInvoiceStartDate));
		}
		if (noticeInvoiceEndDate != null) {
			goodwillApplyInfoDTO.setNoticeInvoiceEndDate(sdf.parse(noticeInvoiceEndDate));
		}
		if (updatedStartAt != null && !"".equals(updatedStartAt)) {
			goodwillApplyInfoDTO.setNoticeInvoiceStartDate(sdf.parse(updatedStartAt));
		}
		if (updatedEndAt != null && !"".equals(updatedEndAt)) {
			goodwillApplyInfoDTO.setNoticeInvoiceEndDate(sdf.parse(updatedEndAt));
		}
		return goodwillApplyInfoService.getOemTodoByPage(page, goodwillApplyInfoDTO);
	}

	/**
	 * 分页查询亲善oem待办事项数据
	 *
	 * @param appId
	 *            系统ID
	 * @param ownerCode
	 *            所有者代码
	 * @param ownerParCode
	 *            所有者的父组织代码
	 * @param orgId
	 *            组织ID
	 * @param id
	 *            预申请单表id
	 * @param applyNo
	 *            预申请单号
	 * @param dealerCode
	 *            申请经销商
	 * @param auditType
	 *            审核类型
	 * @param goodwillNature
	 *            亲善性质
	 * @param applyTime
	 *            申请时间
	 * @param complaintFalut
	 *            投诉故障
	 * @param complaintId
	 *            投诉单ID
	 * @param complaintDate
	 *            投诉日期
	 * @param vehicleUse
	 *            用车情况
	 * @param salesDealer
	 *            销售经销商
	 * @param applyAmount
	 *            申请金额
	 * @param auditAmount
	 *            审批金额
	 * @param settlementAmount
	 *            结算金额
	 * @param invoiceAmount
	 *            通知开票金额
	 * @param goodwillStatus
	 *            亲善单状态
	 * @param customerPain
	 *            客户痛点
	 * @param vin
	 *            VIN
	 * @param license
	 *            车牌号
	 * @param customerName
	 *            客户姓名
	 * @param customerMobile
	 *            客户电话
	 * @param mileage
	 *            里程
	 * @param model
	 *            车型
	 * @param buyCarDate
	 *            购车日期
	 * @param warrantyStartDate
	 *            保修开始日期
	 * @param isExtendWarranty
	 *            是否延保
	 * @param extendWarrantyName
	 *            延保名称
	 * @param extendWarrantyStartDate
	 *            延保开始日期
	 * @param extendWarrantyEndDate
	 *            延保结束日期
	 * @param maintainCost
	 *            保养成本
	 * @param extendWarrantyCost
	 *            延保成本
	 * @param accessoryCost
	 *            附件精品成本
	 * @param voucherCost
	 *            代金券成本
	 * @param walkingCarPrice
	 *            代步车/相关利益
	 * @param volvoIntegral
	 *            沃世界积分
	 * @param returnChangeCarPrice
	 *            退换车
	 * @param otherPrice
	 *            其他
	 * @param costTotal
	 *            成本总计
	 * @param customerPay
	 *            客户支付
	 * @param dealerUndertake
	 *            经销商承担
	 * @param volvoSupportGoodwillAmount
	 *            volvo支持亲善金额
	 * @param remark
	 *            备注
	 * @param applyFile
	 *            预申请附件
	 * @param costStatisticsFile
	 *            亲善成本统计
	 * @param costScreenshotFile
	 *            亲善成本截图
	 * @param ringCheckFile
	 *            故障维修工单/环检单
	 * @param troubleRepairRequisitionFile
	 *            故障维修领料单
	 * @param workOrderFile
	 *            亲善安装工单/领料单
	 * @param situationSettlementAgreementFile
	 *            情况说明和解协议
	 * @param supplementaryMaterialFile
	 *            退换车补充材料
	 * @param managementReviewEmailVpFile
	 *            管理层审核邮件-VP
	 * @param managementReviewEmailCeoFile
	 *            管理层审核邮件-CEO
	 * @param costUpdateFile
	 *            费用更新附件
	 * @param vcdcElseFile
	 *            VCDC其他附件
	 * @param customerIdentification
	 *            客户身份证明
	 * @param elseFile
	 *            其他附件
	 * @param commitTime
	 *            提交时间
	 * @param isValid
	 *            是否有效
	 * @param isDeleted
	 *            是否删除:1,删除；0,未删除
	 * @param createdAt
	 *            创建时间
	 * @param updatedAt
	 *            修改时间
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "bloc", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyPerson", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillNature", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintFalut", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "String[]", name = "roleList", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "roleList1", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "String[]", name = "roleList2", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "settlementAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "invoiceAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillStatus", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerPain", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerMobile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "commitTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyStartAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyEndAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedStartAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询亲善oem待办事项数据", httpMethod = "GET")
	@GetMapping(value = "/querySupportApplyOemTodoListInfo")
	public IPage<GoodwillApplyInfoDTO> getOemTodoListByPage(
			@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "bloc", required = false) Integer bloc,
			@RequestParam(value = "areaManage", required = false) Integer areaManage,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "applyPerson", required = false) String applyPerson,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "auditType", required = false) Integer auditType,
			@RequestParam(value = "goodwillNature", required = false) Integer goodwillNature,
			@RequestParam(value = "applyTime", required = false) String applyTime,
			@RequestParam(value = "complaintFalut", required = false) String complaintFalut,
			@RequestParam(value = "roleList", required = false) String[] roleList,
			@RequestParam(value = "roleList1", required = false) String roleList1,
			@RequestParam(value = "roleList2", required = false) String[] roleList2,
			@RequestParam(value = "complaintId", required = false) String complaintId,
			@RequestParam(value = "complaintDate", required = false) String complaintDate,
			@RequestParam(value = "settlementAmount", required = false) BigDecimal settlementAmount,
			@RequestParam(value = "invoiceAmount", required = false) BigDecimal invoiceAmount,
			@RequestParam(value = "goodwillStatus", required = false) Integer goodwillStatus,
			@RequestParam(value = "customerPain", required = false) String customerPain,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "customerName", required = false) String customerName,
			@RequestParam(value = "customerMobile", required = false) String customerMobile,
			@RequestParam(value = "commitTime", required = false) String commitTime,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt,
			@RequestParam(value = "applyStartTime", required = false) String applyStartTime,
			@RequestParam(value = "applyEndTime", required = false) String applyEndTime,
			@RequestParam(value = "applyStartAmount", required = false) BigDecimal applyStartAmount,
			@RequestParam(value = "applyEndAmount", required = false) BigDecimal applyEndAmount,
			@RequestParam(value = "passStartTime", required = false) String passStartTime,
			@RequestParam(value = "passEndTime", required = false) String passEndTime,
			@RequestParam(value = "noticeInvoiceStartDate", required = false) String noticeInvoiceStartDate,
			@RequestParam(value = "noticeInvoiceEndDate", required = false) String noticeInvoiceEndDate,
			@RequestParam(value = "updatedStartAt", required = false) String updatedStartAt,
			@RequestParam(value = "updatedEndAt", required = false) String updatedEndAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyInfoPO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		goodwillApplyInfoDTO.setAppId(appId);
		goodwillApplyInfoDTO.setOwnerCode(ownerCode);
		goodwillApplyInfoDTO.setOwnerParCode(ownerParCode);
		goodwillApplyInfoDTO.setOrgId(orgId);
		goodwillApplyInfoDTO.setId(id);
		goodwillApplyInfoDTO.setBlocId(bloc);
		goodwillApplyInfoDTO.setAreaManageId(areaManage);
		goodwillApplyInfoDTO.setApplyNo(applyNo);
		goodwillApplyInfoDTO.setApplyPerson(applyPerson);
		goodwillApplyInfoDTO.setDealerCode(dealerCode);
		goodwillApplyInfoDTO.setAuditType(auditType);
		goodwillApplyInfoDTO.setGoodwillNature(goodwillNature);
		goodwillApplyInfoDTO.setRoleList(roleList);
		goodwillApplyInfoDTO.setRoleList1(roleList1);
		goodwillApplyInfoDTO.setRoleList2(roleList2);
		goodwillApplyInfoDTO.setComplaintFalut(complaintFalut);
		goodwillApplyInfoDTO.setComplaintId(complaintId);
		goodwillApplyInfoDTO.setSettlementAmount(settlementAmount);
		goodwillApplyInfoDTO.setInvoiceAmount(invoiceAmount);
		goodwillApplyInfoDTO.setGoodwillStatus(goodwillStatus);
		// goodwillApplyInfoDTO.setCustomerPain(customerPain);
		goodwillApplyInfoDTO.setVin(vin);
		goodwillApplyInfoDTO.setLicense(license);
		goodwillApplyInfoDTO.setCustomerName(customerName);
		goodwillApplyInfoDTO.setCustomerMobile(customerMobile);
		goodwillApplyInfoDTO.setIsValid(isValid);
		goodwillApplyInfoDTO.setIsDeleted(isDeleted);
		goodwillApplyInfoDTO.setApplyStartAmount(applyStartAmount);
		goodwillApplyInfoDTO.setApplyEndAmount(applyEndAmount);
		if (applyStartTime != null && !"".equals(applyStartTime)) {
			goodwillApplyInfoDTO.setApplyStartTime(sdf.parse(applyStartTime));
		}
		if (applyEndTime != null && !"".equals(applyEndTime)) {
			goodwillApplyInfoDTO.setApplyEndTime(sdf.parse(applyEndTime));
		}
		if (passStartTime != null) {
			goodwillApplyInfoDTO.setPassStartTime(sdf.parse(passStartTime));
		}
		if (passEndTime != null) {
			goodwillApplyInfoDTO.setPassEndTime(sdf.parse(passEndTime));
		}
		if (noticeInvoiceStartDate != null) {
			goodwillApplyInfoDTO.setNoticeInvoiceStartDate(sdf.parse(noticeInvoiceStartDate));
		}
		if (noticeInvoiceEndDate != null) {
			goodwillApplyInfoDTO.setNoticeInvoiceEndDate(sdf.parse(noticeInvoiceEndDate));
		}
		if (updatedStartAt != null && !"".equals(updatedStartAt)) {
			goodwillApplyInfoDTO.setNoticeInvoiceStartDate(sdf.parse(updatedStartAt));
		}
		if (updatedEndAt != null && !"".equals(updatedEndAt)) {
			goodwillApplyInfoDTO.setNoticeInvoiceEndDate(sdf.parse(updatedEndAt));
		}
		return goodwillApplyInfoService.getOemTodoListByPage(page, goodwillApplyInfoDTO);
	}

	/**
	 * 分页查询亲善oem待办事项数据
	 *
	 * @param appId
	 *            系统ID
	 * @param ownerCode
	 *            所有者代码
	 * @param ownerParCode
	 *            所有者的父组织代码
	 * @param orgId
	 *            组织ID
	 * @param id
	 *            预申请单表id
	 * @param applyNo
	 *            预申请单号
	 * @param dealerCode
	 *            申请经销商
	 * @param auditType
	 *            审核类型
	 * @param goodwillNature
	 *            亲善性质
	 * @param applyTime
	 *            申请时间
	 * @param complaintFalut
	 *            投诉故障
	 * @param complaintId
	 *            投诉单ID
	 * @param complaintDate
	 *            投诉日期
	 * @param vehicleUse
	 *            用车情况
	 * @param salesDealer
	 *            销售经销商
	 * @param applyAmount
	 *            申请金额
	 * @param auditAmount
	 *            审批金额
	 * @param settlementAmount
	 *            结算金额
	 * @param invoiceAmount
	 *            通知开票金额
	 * @param goodwillStatus
	 *            亲善单状态
	 * @param customerPain
	 *            客户痛点
	 * @param vin
	 *            VIN
	 * @param license
	 *            车牌号
	 * @param customerName
	 *            客户姓名
	 * @param customerMobile
	 *            客户电话
	 * @param mileage
	 *            里程
	 * @param model
	 *            车型
	 * @param buyCarDate
	 *            购车日期
	 * @param warrantyStartDate
	 *            保修开始日期
	 * @param isExtendWarranty
	 *            是否延保
	 * @param extendWarrantyName
	 *            延保名称
	 * @param extendWarrantyStartDate
	 *            延保开始日期
	 * @param extendWarrantyEndDate
	 *            延保结束日期
	 * @param maintainCost
	 *            保养成本
	 * @param extendWarrantyCost
	 *            延保成本
	 * @param accessoryCost
	 *            附件精品成本
	 * @param voucherCost
	 *            代金券成本
	 * @param walkingCarPrice
	 *            代步车/相关利益
	 * @param volvoIntegral
	 *            沃世界积分
	 * @param returnChangeCarPrice
	 *            退换车
	 * @param otherPrice
	 *            其他
	 * @param costTotal
	 *            成本总计
	 * @param customerPay
	 *            客户支付
	 * @param dealerUndertake
	 *            经销商承担
	 * @param volvoSupportGoodwillAmount
	 *            volvo支持亲善金额
	 * @param remark
	 *            备注
	 * @param applyFile
	 *            预申请附件
	 * @param costStatisticsFile
	 *            亲善成本统计
	 * @param costScreenshotFile
	 *            亲善成本截图
	 * @param commitTime
	 *            提交时间
	 * @param isValid
	 *            是否有效
	 * @param isDeleted
	 *            是否删除:1,删除；0,未删除
	 * @param createdAt
	 *            创建时间
	 * @param updatedAt
	 *            修改时间
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "roleList1", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "bloc", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "smallAreaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "smallArea", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "auditor", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillNature", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintFalut", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "String[]", name = "complaintFalut1", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "vehicleUse", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "salesDealer", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "auditAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "settlementAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "invoiceAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillStatus", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerPain", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerMobile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "mileage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "model", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "buyCarDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "warrantyStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isExtendWarranty", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "maintainCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "extendWarrantyCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "accessoryCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "voucherCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "walkingCarPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "volvoIntegral", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "returnChangeCarPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "otherPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "costTotal", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "customerPay", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "dealerUndertake", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "volvoSupportGoodwillAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "remark", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerIdentification", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "elseFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "commitTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyStartAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyEndAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedStartAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询亲善oem待办事项数据", httpMethod = "GET")
	@GetMapping(value = "/querySupportApplyOemSearchInfo")
	public IPage<GoodwillApplyInfoDTO> getOemSearchByPage(@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "roleList1", required = false) String roleList1,
			@RequestParam(value = "bloc", required = false) Integer bloc,
			@RequestParam(value = "areaManage", required = false) String areaManage,
			@RequestParam(value = "smallAreaManage", required = false) String smallAreaManage,
			@RequestParam(value = "smallArea", required = false) Integer smallArea,
			@RequestParam(value = "auditor", required = false) Long auditor,
			@RequestParam(value = "auditName", required = false) String auditName,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "auditType", required = false) Integer auditType,
			@RequestParam(value = "goodwillNature", required = false) Integer goodwillNature,
			@RequestParam(value = "applyTime", required = false) String applyTime,
			@RequestParam(value = "complaintFalut", required = false) String complaintFalut,
			@RequestParam(value = "complaintFalut1", required = false) String[] complaintFalut1,
			@RequestParam(value = "complaintId", required = false) String complaintId,
			@RequestParam(value = "complaintDate", required = false) String complaintDate,
			@RequestParam(value = "vehicleUse", required = false) Integer vehicleUse,
			@RequestParam(value = "salesDealer", required = false) String salesDealer,
			@RequestParam(value = "applyAmount", required = false) BigDecimal applyAmount,
			@RequestParam(value = "auditAmount", required = false) BigDecimal auditAmount,
			@RequestParam(value = "settlementAmount", required = false) BigDecimal settlementAmount,
			@RequestParam(value = "invoiceAmount", required = false) BigDecimal invoiceAmount,
			@RequestParam(value = "goodwillStatus", required = false) Integer goodwillStatus,
			@RequestParam(value = "customerPain", required = false) String customerPain,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "customerName", required = false) String customerName,
			@RequestParam(value = "customerMobile", required = false) String customerMobile,
			@RequestParam(value = "mileage", required = false) String mileage,
			@RequestParam(value = "model", required = false) String model,
			@RequestParam(value = "buyCarDate", required = false) String buyCarDate,
			@RequestParam(value = "warrantyStartDate", required = false) String warrantyStartDate,
			@RequestParam(value = "isExtendWarranty", required = false) Integer isExtendWarranty,
			@RequestParam(value = "extendWarrantyName", required = false) String extendWarrantyName,
			@RequestParam(value = "extendWarrantyStartDate", required = false) String extendWarrantyStartDate,
			@RequestParam(value = "extendWarrantyEndDate", required = false) String extendWarrantyEndDate,
			@RequestParam(value = "maintainCost", required = false) BigDecimal maintainCost,
			@RequestParam(value = "extendWarrantyCost", required = false) BigDecimal extendWarrantyCost,
			@RequestParam(value = "accessoryCost", required = false) BigDecimal accessoryCost,
			@RequestParam(value = "voucherCost", required = false) BigDecimal voucherCost,
			@RequestParam(value = "walkingCarPrice", required = false) BigDecimal walkingCarPrice,
			@RequestParam(value = "volvoIntegral", required = false) BigDecimal volvoIntegral,
			@RequestParam(value = "returnChangeCarPrice", required = false) BigDecimal returnChangeCarPrice,
			@RequestParam(value = "otherPrice", required = false) BigDecimal otherPrice,
			@RequestParam(value = "costTotal", required = false) BigDecimal costTotal,
			@RequestParam(value = "customerPay", required = false) BigDecimal customerPay,
			@RequestParam(value = "dealerUndertake", required = false) BigDecimal dealerUndertake,
			@RequestParam(value = "volvoSupportGoodwillAmount", required = false) BigDecimal volvoSupportGoodwillAmount,
			@RequestParam(value = "remark", required = false) String remark,
			@RequestParam(value = "customerIdentification", required = false) String customerIdentification,
			@RequestParam(value = "elseFile", required = false) String elseFile,
			@RequestParam(value = "commitTime", required = false) String commitTime,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt,
			@RequestParam(value = "applyStartTime", required = false) String applyStartTime,
			@RequestParam(value = "applyEndTime", required = false) String applyEndTime,
			@RequestParam(value = "applyStartAmount", required = false) BigDecimal applyStartAmount,
			@RequestParam(value = "applyEndAmount", required = false) BigDecimal applyEndAmount,
			@RequestParam(value = "passStartTime", required = false) String passStartTime,
			@RequestParam(value = "passEndTime", required = false) String passEndTime,
			@RequestParam(value = "noticeInvoiceStartDate", required = false) String noticeInvoiceStartDate,
			@RequestParam(value = "noticeInvoiceEndDate", required = false) String noticeInvoiceEndDate,
			@RequestParam(value = "invoiceStartDate", required = false) String invoiceStartDate,
			@RequestParam(value = "invoiceEndDate", required = false) String invoiceEndDate,
			@RequestParam(value = "updatedStartAt", required = false) String updatedStartAt,
			@RequestParam(value = "updatedEndAt", required = false) String updatedEndAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyInfoPO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		goodwillApplyInfoDTO.setAppId(appId);
		goodwillApplyInfoDTO.setOwnerCode(ownerCode);
		goodwillApplyInfoDTO.setOwnerParCode(ownerParCode);
		goodwillApplyInfoDTO.setOrgId(orgId);
		goodwillApplyInfoDTO.setRoleList1(roleList1);
		goodwillApplyInfoDTO.setId(id);
		goodwillApplyInfoDTO.setBlocId(bloc);
		goodwillApplyInfoDTO.setAreaManageId1(areaManage);
		goodwillApplyInfoDTO.setSmallAreaId1(smallAreaManage);
		goodwillApplyInfoDTO.setSmallAreaId(smallArea);
		goodwillApplyInfoDTO.setAuditor(auditor);
		goodwillApplyInfoDTO.setAuditName1(Utills.getList(auditName));
		goodwillApplyInfoDTO.setApplyNo(applyNo);
		goodwillApplyInfoDTO.setDealerCode(dealerCode);
		goodwillApplyInfoDTO.setAuditType(auditType);
		goodwillApplyInfoDTO.setGoodwillNature(goodwillNature);
		goodwillApplyInfoDTO.setComplaintFalut1(complaintFalut1);
		goodwillApplyInfoDTO.setComplaintFalut(complaintFalut);
		goodwillApplyInfoDTO.setComplaintId(complaintId);
		goodwillApplyInfoDTO.setVehicleUse(vehicleUse);
		goodwillApplyInfoDTO.setSalesDealer(salesDealer);
		goodwillApplyInfoDTO.setApplyAmount(applyAmount);
		goodwillApplyInfoDTO.setAuditAmount(auditAmount);
		goodwillApplyInfoDTO.setSettlementAmount(settlementAmount);
		goodwillApplyInfoDTO.setInvoiceAmount(invoiceAmount);
		goodwillApplyInfoDTO.setGoodwillStatus(goodwillStatus);
		// goodwillApplyInfoDTO.setCustomerPain(customerPain);
		goodwillApplyInfoDTO.setVin(vin);
		goodwillApplyInfoDTO.setLicense(license);
		goodwillApplyInfoDTO.setCustomerName(customerName);
		goodwillApplyInfoDTO.setCustomerMobile(customerMobile);
		goodwillApplyInfoDTO.setMileage(mileage);
		goodwillApplyInfoDTO.setModel(model);
		goodwillApplyInfoDTO.setIsExtendWarranty(isExtendWarranty);
		goodwillApplyInfoDTO.setExtendWarrantyName(extendWarrantyName);
		goodwillApplyInfoDTO.setMaintainCost(maintainCost);
		goodwillApplyInfoDTO.setExtendWarrantyCost(extendWarrantyCost);
		goodwillApplyInfoDTO.setAccessoryCost(accessoryCost);
		goodwillApplyInfoDTO.setVoucherCost(voucherCost);
		goodwillApplyInfoDTO.setWalkingCarPrice(walkingCarPrice);
		goodwillApplyInfoDTO.setVolvoIntegral(volvoIntegral);
		goodwillApplyInfoDTO.setReturnChangeCarPrice(returnChangeCarPrice);
		goodwillApplyInfoDTO.setOtherPrice(otherPrice);
		goodwillApplyInfoDTO.setCostTotal(costTotal);
		goodwillApplyInfoDTO.setCustomerPay(customerPay);
		goodwillApplyInfoDTO.setDealerUndertake(dealerUndertake);
		goodwillApplyInfoDTO.setVolvoSupportGoodwillAmount(volvoSupportGoodwillAmount);
		goodwillApplyInfoDTO.setRemark(remark);
		goodwillApplyInfoDTO.setCustomerIdentification(customerIdentification);
		goodwillApplyInfoDTO.setElseFile(elseFile);
		goodwillApplyInfoDTO.setIsValid(isValid);
		goodwillApplyInfoDTO.setIsDeleted(isDeleted);
		goodwillApplyInfoDTO.setApplyStartAmount(applyStartAmount);
		goodwillApplyInfoDTO.setApplyEndAmount(applyEndAmount);
		if (!StringUtils.isNullOrEmpty(applyStartTime)) {
			goodwillApplyInfoDTO.setApplyStartTime(sdf.parse(applyStartTime));
		}
		if (!StringUtils.isNullOrEmpty(applyEndTime)) {
			goodwillApplyInfoDTO.setApplyEndTime(sdf.parse(applyEndTime));
		}
		if (!StringUtils.isNullOrEmpty(passStartTime)) {
			goodwillApplyInfoDTO.setPassStartTime(sdf.parse(passStartTime));
		}
		if (!StringUtils.isNullOrEmpty(passEndTime)) {
			goodwillApplyInfoDTO.setPassEndTime(sdf.parse(passEndTime));
		}
		if (!StringUtils.isNullOrEmpty(noticeInvoiceStartDate)) {
			goodwillApplyInfoDTO.setNoticeInvoiceStartDate(sdf.parse(noticeInvoiceStartDate));
		}
		if (!StringUtils.isNullOrEmpty(noticeInvoiceEndDate)) {
			goodwillApplyInfoDTO.setNoticeInvoiceEndDate(sdf.parse(noticeInvoiceEndDate));
		}
		if (!StringUtils.isNullOrEmpty(invoiceStartDate)) {
			goodwillApplyInfoDTO.setInvoiceStartDate(sdf.parse(invoiceStartDate));
		}
		if (!StringUtils.isNullOrEmpty(invoiceEndDate)) {
			goodwillApplyInfoDTO.setInvoiceEndDate(sdf.parse(invoiceEndDate));
		}
		if (!StringUtils.isNullOrEmpty(updatedStartAt)) {
			goodwillApplyInfoDTO.setUpdatedStartAt(sdf.parse(updatedStartAt));
		}
		if (!StringUtils.isNullOrEmpty(updatedEndAt)) {
			goodwillApplyInfoDTO.setUpdatedEndAt(sdf.parse(updatedEndAt));
		}
		return goodwillApplyInfoService.getOemSearchByPage(page, goodwillApplyInfoDTO);
	}

	/**
	 * 分页查询亲善店端待办事项数据
	 *
	 * @param appId
	 *            系统ID
	 * @param ownerCode
	 *            所有者代码
	 * @param ownerParCode
	 *            所有者的父组织代码
	 * @param orgId
	 *            组织ID
	 * @param id
	 *            预申请单表id
	 * @param applyNo
	 *            预申请单号
	 * @param dealerCode
	 *            申请经销商
	 * @param auditType
	 *            审核类型
	 * @param goodwillNature
	 *            亲善性质
	 * @param applyTime
	 *            申请时间
	 * @param complaintFalut
	 *            投诉故障
	 * @param complaintId
	 *            投诉单ID
	 * @param complaintDate
	 *            投诉日期
	 * @param vehicleUse
	 *            用车情况
	 * @param salesDealer
	 *            销售经销商
	 * @param applyAmount
	 *            申请金额
	 * @param auditAmount
	 *            审批金额
	 * @param settlementAmount
	 *            结算金额
	 * @param invoiceAmount
	 *            通知开票金额
	 * @param goodwillStatus
	 *            亲善单状态
	 * @param customerPain
	 *            客户痛点
	 * @param vin
	 *            VIN
	 * @param license
	 *            车牌号
	 * @param customerName
	 *            客户姓名
	 * @param customerMobile
	 *            客户电话
	 * @param mileage
	 *            里程
	 * @param model
	 *            车型
	 * @param buyCarDate
	 *            购车日期
	 * @param warrantyStartDate
	 *            保修开始日期
	 * @param isExtendWarranty
	 *            是否延保
	 * @param extendWarrantyName
	 *            延保名称
	 * @param extendWarrantyStartDate
	 *            延保开始日期
	 * @param extendWarrantyEndDate
	 *            延保结束日期
	 * @param maintainCost
	 *            保养成本
	 * @param extendWarrantyCost
	 *            延保成本
	 * @param accessoryCost
	 *            附件精品成本
	 * @param voucherCost
	 *            代金券成本
	 * @param walkingCarPrice
	 *            代步车/相关利益
	 * @param volvoIntegral
	 *            沃世界积分
	 * @param returnChangeCarPrice
	 *            退换车
	 * @param otherPrice
	 *            其他
	 * @param costTotal
	 *            成本总计
	 * @param customerPay
	 *            客户支付
	 * @param dealerUndertake
	 *            经销商承担
	 * @param volvoSupportGoodwillAmount
	 *            volvo支持亲善金额
	 * @param remark
	 *            备注
	 * @param applyFile
	 *            预申请附件
	 * @param costStatisticsFile
	 *            亲善成本统计
	 * @param costScreenshotFile
	 *            亲善成本截图
	 * @param commitTime
	 *            提交时间
	 * @param isValid
	 *            是否有效
	 * @param isDeleted
	 *            是否删除:1,删除；0,未删除
	 * @param createdAt
	 *            创建时间
	 * @param updatedAt
	 *            修改时间
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillNature", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintFalut", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "vehicleUse", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "salesDealer", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "auditAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "settlementAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "invoiceAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillStatus", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerPain", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerMobile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "mileage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "model", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "buyCarDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "warrantyStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isExtendWarranty", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "maintainCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "extendWarrantyCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "accessoryCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "voucherCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "walkingCarPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "volvoIntegral", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "returnChangeCarPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "otherPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "costTotal", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "customerPay", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "dealerUndertake", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "volvoSupportGoodwillAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "remark", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerIdentification", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "elseFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "commitTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyStartAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyEndAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询亲善店端待办事项数据", httpMethod = "GET")
	@GetMapping(value = "/querySupportApplyDealerSearchInfo")
	public IPage<GoodwillApplyInfoDTO> querySupportApplyDealerSearchInfo(
			@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "auditType", required = false) Integer auditType,
			@RequestParam(value = "goodwillNature", required = false) Integer goodwillNature,
			@RequestParam(value = "applyTime", required = false) String applyTime,
			@RequestParam(value = "complaintFalut", required = false) String complaintFalut,
			@RequestParam(value = "complaintId", required = false) String complaintId,
			@RequestParam(value = "complaintDate", required = false) String complaintDate,
			@RequestParam(value = "vehicleUse", required = false) Integer vehicleUse,
			@RequestParam(value = "salesDealer", required = false) String salesDealer,
			@RequestParam(value = "applyAmount", required = false) BigDecimal applyAmount,
			@RequestParam(value = "auditAmount", required = false) BigDecimal auditAmount,
			@RequestParam(value = "settlementAmount", required = false) BigDecimal settlementAmount,
			@RequestParam(value = "invoiceAmount", required = false) BigDecimal invoiceAmount,
			@RequestParam(value = "goodwillStatus", required = false) Integer goodwillStatus,
			@RequestParam(value = "customerPain", required = false) String customerPain,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "customerName", required = false) String customerName,
			@RequestParam(value = "customerMobile", required = false) String customerMobile,
			@RequestParam(value = "mileage", required = false) String mileage,
			@RequestParam(value = "model", required = false) String model,
			@RequestParam(value = "buyCarDate", required = false) String buyCarDate,
			@RequestParam(value = "warrantyStartDate", required = false) String warrantyStartDate,
			@RequestParam(value = "isExtendWarranty", required = false) Integer isExtendWarranty,
			@RequestParam(value = "extendWarrantyName", required = false) String extendWarrantyName,
			@RequestParam(value = "extendWarrantyStartDate", required = false) String extendWarrantyStartDate,
			@RequestParam(value = "extendWarrantyEndDate", required = false) String extendWarrantyEndDate,
			@RequestParam(value = "maintainCost", required = false) BigDecimal maintainCost,
			@RequestParam(value = "extendWarrantyCost", required = false) BigDecimal extendWarrantyCost,
			@RequestParam(value = "accessoryCost", required = false) BigDecimal accessoryCost,
			@RequestParam(value = "voucherCost", required = false) BigDecimal voucherCost,
			@RequestParam(value = "walkingCarPrice", required = false) BigDecimal walkingCarPrice,
			@RequestParam(value = "volvoIntegral", required = false) BigDecimal volvoIntegral,
			@RequestParam(value = "returnChangeCarPrice", required = false) BigDecimal returnChangeCarPrice,
			@RequestParam(value = "otherPrice", required = false) BigDecimal otherPrice,
			@RequestParam(value = "costTotal", required = false) BigDecimal costTotal,
			@RequestParam(value = "customerPay", required = false) BigDecimal customerPay,
			@RequestParam(value = "dealerUndertake", required = false) BigDecimal dealerUndertake,
			@RequestParam(value = "volvoSupportGoodwillAmount", required = false) BigDecimal volvoSupportGoodwillAmount,
			@RequestParam(value = "remark", required = false) String remark,
			@RequestParam(value = "customerIdentification", required = false) String customerIdentification,
			@RequestParam(value = "elseFile", required = false) String elseFile,
			@RequestParam(value = "commitTime", required = false) String commitTime,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt,
			@RequestParam(value = "applyStartTime", required = false) String applyStartTime,
			@RequestParam(value = "applyEndTime", required = false) String applyEndTime,
			@RequestParam(value = "applyStartAmount", required = false) BigDecimal applyStartAmount,
			@RequestParam(value = "applyEndAmount", required = false) BigDecimal applyEndAmount,
			@RequestParam(value = "passStartTime", required = false) String passStartTime,
			@RequestParam(value = "passEndTime", required = false) String passEndTime,
			@RequestParam(value = "noticeInvoiceStartDate", required = false) String noticeInvoiceStartDate,
			@RequestParam(value = "noticeInvoiceEndDate", required = false) String noticeInvoiceEndDate,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyInfoPO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		goodwillApplyInfoDTO.setAppId(appId);
		goodwillApplyInfoDTO.setOwnerCode(ownerCode);
		goodwillApplyInfoDTO.setOwnerParCode(ownerParCode);
		goodwillApplyInfoDTO.setOrgId(orgId);
		goodwillApplyInfoDTO.setId(id);
		goodwillApplyInfoDTO.setApplyNo(applyNo);
		goodwillApplyInfoDTO.setDealerCode(loginInfoDto.getOwnerCode());
		goodwillApplyInfoDTO.setAuditType(auditType);
		goodwillApplyInfoDTO.setGoodwillNature(goodwillNature);
		goodwillApplyInfoDTO.setComplaintFalut(complaintFalut);
		goodwillApplyInfoDTO.setComplaintId(complaintId);
		goodwillApplyInfoDTO.setVehicleUse(vehicleUse);
		goodwillApplyInfoDTO.setSalesDealer(salesDealer);
		goodwillApplyInfoDTO.setApplyAmount(applyAmount);
		goodwillApplyInfoDTO.setAuditAmount(auditAmount);
		goodwillApplyInfoDTO.setSettlementAmount(settlementAmount);
		goodwillApplyInfoDTO.setInvoiceAmount(invoiceAmount);
		goodwillApplyInfoDTO.setGoodwillStatus(goodwillStatus);
		// goodwillApplyInfoDTO.setCustomerPain(customerPain);
		goodwillApplyInfoDTO.setVin(vin);
		goodwillApplyInfoDTO.setLicense(license);
		goodwillApplyInfoDTO.setCustomerName(customerName);
		goodwillApplyInfoDTO.setCustomerMobile(customerMobile);
		goodwillApplyInfoDTO.setMileage(mileage);
		goodwillApplyInfoDTO.setModel(model);
		goodwillApplyInfoDTO.setIsExtendWarranty(isExtendWarranty);
		goodwillApplyInfoDTO.setExtendWarrantyName(extendWarrantyName);
		goodwillApplyInfoDTO.setMaintainCost(maintainCost);
		goodwillApplyInfoDTO.setExtendWarrantyCost(extendWarrantyCost);
		goodwillApplyInfoDTO.setAccessoryCost(accessoryCost);
		goodwillApplyInfoDTO.setVoucherCost(voucherCost);
		goodwillApplyInfoDTO.setWalkingCarPrice(walkingCarPrice);
		goodwillApplyInfoDTO.setVolvoIntegral(volvoIntegral);
		goodwillApplyInfoDTO.setReturnChangeCarPrice(returnChangeCarPrice);
		goodwillApplyInfoDTO.setOtherPrice(otherPrice);
		goodwillApplyInfoDTO.setCostTotal(costTotal);
		goodwillApplyInfoDTO.setCustomerPay(customerPay);
		goodwillApplyInfoDTO.setDealerUndertake(dealerUndertake);
		goodwillApplyInfoDTO.setVolvoSupportGoodwillAmount(volvoSupportGoodwillAmount);
		goodwillApplyInfoDTO.setRemark(remark);
		goodwillApplyInfoDTO.setCustomerIdentification(customerIdentification);
		goodwillApplyInfoDTO.setElseFile(elseFile);
		goodwillApplyInfoDTO.setIsValid(isValid);
		goodwillApplyInfoDTO.setIsDeleted(isDeleted);
		goodwillApplyInfoDTO.setApplyStartAmount(applyStartAmount);
		goodwillApplyInfoDTO.setApplyEndAmount(applyEndAmount);
		if (!StringUtils.isNullOrEmpty(applyStartTime)) {
			goodwillApplyInfoDTO.setApplyStartTime(sdf.parse(applyStartTime));
		}
		if (!StringUtils.isNullOrEmpty(applyEndTime)) {
			goodwillApplyInfoDTO.setApplyEndTime(sdf.parse(applyEndTime));
		}
		if (!StringUtils.isNullOrEmpty(passStartTime)) {
			goodwillApplyInfoDTO.setPassStartTime(sdf.parse(passStartTime));
		}
		if (!StringUtils.isNullOrEmpty(passEndTime)) {
			goodwillApplyInfoDTO.setPassEndTime(sdf.parse(passEndTime));
		}
		if (!StringUtils.isNullOrEmpty(noticeInvoiceStartDate)) {
			goodwillApplyInfoDTO.setNoticeInvoiceStartDate(sdf.parse(noticeInvoiceStartDate));
		}
		if (!StringUtils.isNullOrEmpty(noticeInvoiceEndDate)) {
			goodwillApplyInfoDTO.setNoticeInvoiceEndDate(sdf.parse(noticeInvoiceEndDate));
		}
		return goodwillApplyInfoService.querySupportApplyDealerSearchInfo(page, goodwillApplyInfoDTO);
	}

	/**
	 * 分页查询已审核亲善单数据
	 *
	 *
	 * @param id
	 *            预申请单表id
	 * @param dealerCode
	 *            申请经销商
	 * @param goodwillNature
	 *            亲善性质
	 * @param applyTime
	 *            申请时间
	 * @param complaintId
	 *            投诉单ID
	 * @param complaintDate
	 *            投诉日期
	 * @param applyAmount
	 *            申请金额
	 * @param goodwillStatus
	 *            亲善单状态
	 * @param vin
	 *            VIN
	 * @param license
	 *            车牌号
	 * @param customerName
	 *            客户姓名
	 * @param customerMobile
	 *            客户电话
	 * @param mileage
	 *            里程
	 * @param updatedAt
	 *            修改时间
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "bloc", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "smallArea", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "auditor", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillNature", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyPerson", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerMobile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintFalut", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "String[]", name = "complaintFalut1", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillStatus", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyStartAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyEndAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedStartAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询已审核亲善单数据", httpMethod = "GET")
	@RequestMapping(value = "/queryCheckedGoodWill", method = RequestMethod.GET)
	public IPage<GoodwillApplyInfoDTO> getCheckedGoodWill(@RequestParam(value = "bloc", required = false) Integer bloc,
			@RequestParam(value = "areaManage", required = false) Integer areaManage,
			@RequestParam(value = "smallArea", required = false) Integer smallArea,
			@RequestParam(value = "auditor", required = false) Long auditor,
			@RequestParam(value = "auditName", required = false) String auditName,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "goodwillNature", required = false) Integer goodwillNature,
			@RequestParam(value = "applyPerson", required = false) String applyPerson,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "customerName", required = false) String customerName,
			@RequestParam(value = "customerMobile", required = false) String customerMobile,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "complaintId", required = false) String complaintId,
			@RequestParam(value = "complaintFalut", required = false) String complaintFalut,
			@RequestParam(value = "complaintFalut1", required = false) String[] complaintFalut1,
			@RequestParam(value = "goodwillStatus", required = false) Integer goodwillStatus,
			@RequestParam(value = "applyStartTime", required = false) String applyStartTime,
			@RequestParam(value = "applyEndTime", required = false) String applyEndTime,
			@RequestParam(value = "applyStartAmount", required = false) BigDecimal applyStartAmount,
			@RequestParam(value = "applyEndAmount", required = false) BigDecimal applyEndAmount,
			@RequestParam(value = "passStartTime", required = false) String passStartTime,
			@RequestParam(value = "passEndTime", required = false) String passEndTime,
			@RequestParam(value = "noticeInvoiceStartDate", required = false) String noticeInvoiceStartDate,
			@RequestParam(value = "noticeInvoiceEndDate", required = false) String noticeInvoiceEndDate,
			@RequestParam(value = "invoiceStartDate", required = false) String invoiceStartDate,
			@RequestParam(value = "invoiceEndDate", required = false) String invoiceEndDate,
			@RequestParam(value = "updatedStartAt", required = false) String updatedStartAt,
			@RequestParam(value = "updatedEndAt", required = false) String updatedEndAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyInfoPO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		goodwillApplyInfoDTO.setBlocId(bloc);
		goodwillApplyInfoDTO.setAreaManageId(areaManage);
		goodwillApplyInfoDTO.setSmallAreaId(smallArea);
		goodwillApplyInfoDTO.setAuditor(auditor);
		goodwillApplyInfoDTO.setAuditName(auditName);
		goodwillApplyInfoDTO.setDealerCode(dealerCode);
		goodwillApplyInfoDTO.setGoodwillNature(goodwillNature);
		goodwillApplyInfoDTO.setApplyPerson(applyPerson);
		goodwillApplyInfoDTO.setId(id);
		goodwillApplyInfoDTO.setApplyNo(applyNo);
		goodwillApplyInfoDTO.setCustomerName(customerName);
		goodwillApplyInfoDTO.setCustomerMobile(customerMobile);
		goodwillApplyInfoDTO.setVin(vin);
		goodwillApplyInfoDTO.setLicense(license);
		goodwillApplyInfoDTO.setComplaintId(complaintId);
		goodwillApplyInfoDTO.setComplaintFalut(complaintFalut);
		goodwillApplyInfoDTO.setComplaintFalut1(complaintFalut1);
		goodwillApplyInfoDTO.setGoodwillStatus(goodwillStatus);
		goodwillApplyInfoDTO.setApplyStartAmount(applyStartAmount);
		goodwillApplyInfoDTO.setApplyEndAmount(applyEndAmount);
		if (applyStartTime != null) {
			goodwillApplyInfoDTO.setApplyStartTime(sdf.parse(applyStartTime));
		}
		if (applyEndTime != null) {
			goodwillApplyInfoDTO.setApplyEndTime(sdf.parse(applyEndTime));
		}
		if (passStartTime != null) {
			goodwillApplyInfoDTO.setPassStartTime(sdf.parse(passStartTime));
		}
		if (passEndTime != null) {
			goodwillApplyInfoDTO.setPassEndTime(sdf.parse(passEndTime));
		}
		if (noticeInvoiceStartDate != null) {
			goodwillApplyInfoDTO.setNoticeInvoiceStartDate(sdf.parse(noticeInvoiceStartDate));
		}
		if (noticeInvoiceEndDate != null) {
			goodwillApplyInfoDTO.setNoticeInvoiceEndDate(sdf.parse(noticeInvoiceEndDate));
		}
		if (invoiceStartDate != null) {
			goodwillApplyInfoDTO.setInvoiceStartDate(sdf.parse(invoiceStartDate));
		}
		if (invoiceEndDate != null) {
			goodwillApplyInfoDTO.setInvoiceEndDate(sdf.parse(invoiceEndDate));
		}
		if (updatedStartAt != null) {
			goodwillApplyInfoDTO.setUpdatedStartAt(sdf.parse(updatedStartAt));
		}
		if (updatedEndAt != null) {
			goodwillApplyInfoDTO.setUpdatedEndAt(sdf.parse(updatedEndAt));
		}
		return goodwillApplyInfoService.selectCheckedGoodWill(page, goodwillApplyInfoDTO);
	}

	/**
	 * 已审核亲善单导出
	 *
	 *
	 * @param id
	 *            预申请单表id
	 * @param dealerCode
	 *            申请经销商
	 * @param goodwillNature
	 *            亲善性质
	 * @param applyTime
	 *            申请时间
	 * @param complaintId
	 *            投诉单ID
	 * @param complaintDate
	 *            投诉日期
	 * @param applyAmount
	 *            申请金额
	 * @param goodwillStatus
	 *            亲善单状态
	 * @param vin
	 *            VIN
	 * @param license
	 *            车牌号
	 * @param customerName
	 *            客户姓名
	 * @param customerMobile
	 *            客户电话
	 * @param mileage
	 *            里程
	 * @param updatedAt
	 *            修改时间
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "bloc", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "smallArea", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "auditor", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillNature", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyPerson", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerMobile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintFalut", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "String[]", name = "complaintFalut1", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillStatus", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyStartAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyEndAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedStartAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedEndAt", value = "")
	})
	@ApiOperation(value = "已审核亲善单导出", httpMethod = "GET")
	@RequestMapping(value = "/exportCheckedApplyInfo", method = RequestMethod.GET)
	public List<GoodwillApplyInfoDTO> exportCheckedApplyInfo(
			@RequestParam(value = "bloc", required = false) Integer bloc,
			@RequestParam(value = "areaManage", required = false) Integer areaManage,
			@RequestParam(value = "smallArea", required = false) Integer smallArea,
			@RequestParam(value = "auditor", required = false) Long auditor,
			@RequestParam(value = "auditName", required = false) String auditName,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "goodwillNature", required = false) Integer goodwillNature,
			@RequestParam(value = "applyPerson", required = false) String applyPerson,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "customerName", required = false) String customerName,
			@RequestParam(value = "customerMobile", required = false) String customerMobile,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "complaintId", required = false) String complaintId,
			@RequestParam(value = "complaintFalut", required = false) String complaintFalut,
			@RequestParam(value = "complaintFalut1", required = false) String[] complaintFalut1,
			@RequestParam(value = "goodwillStatus", required = false) Integer goodwillStatus,
			@RequestParam(value = "applyStartTime", required = false) String applyStartTime,
			@RequestParam(value = "applyEndTime", required = false) String applyEndTime,
			@RequestParam(value = "applyStartAmount", required = false) BigDecimal applyStartAmount,
			@RequestParam(value = "applyEndAmount", required = false) BigDecimal applyEndAmount,
			@RequestParam(value = "passStartTime", required = false) String passStartTime,
			@RequestParam(value = "passEndTime", required = false) String passEndTime,
			@RequestParam(value = "noticeInvoiceStartDate", required = false) String noticeInvoiceStartDate,
			@RequestParam(value = "noticeInvoiceEndDate", required = false) String noticeInvoiceEndDate,
			@RequestParam(value = "invoiceStartDate", required = false) String invoiceStartDate,
			@RequestParam(value = "invoiceEndDate", required = false) String invoiceEndDate,
			@RequestParam(value = "updatedStartAt", required = false) String updatedStartAt,
			@RequestParam(value = "updatedEndAt", required = false) String updatedEndAt) throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		goodwillApplyInfoDTO.setBlocId(bloc);
		goodwillApplyInfoDTO.setAreaManageId(areaManage);
		goodwillApplyInfoDTO.setSmallAreaId(smallArea);
		goodwillApplyInfoDTO.setAuditor(auditor);
		goodwillApplyInfoDTO.setAuditName(auditName);
		goodwillApplyInfoDTO.setDealerCode(dealerCode);
		goodwillApplyInfoDTO.setGoodwillNature(goodwillNature);
		goodwillApplyInfoDTO.setApplyPerson(applyPerson);
		goodwillApplyInfoDTO.setId(id);
		goodwillApplyInfoDTO.setApplyNo(applyNo);
		goodwillApplyInfoDTO.setCustomerName(customerName);
		goodwillApplyInfoDTO.setCustomerMobile(customerMobile);
		goodwillApplyInfoDTO.setVin(vin);
		goodwillApplyInfoDTO.setLicense(license);
		goodwillApplyInfoDTO.setComplaintId(complaintId);
		goodwillApplyInfoDTO.setComplaintFalut(complaintFalut);
		goodwillApplyInfoDTO.setComplaintFalut1(complaintFalut1);
		goodwillApplyInfoDTO.setGoodwillStatus(goodwillStatus);
		goodwillApplyInfoDTO.setApplyStartAmount(applyStartAmount);
		goodwillApplyInfoDTO.setApplyEndAmount(applyEndAmount);
		if (applyStartTime != null) {
			goodwillApplyInfoDTO.setApplyStartTime(sdf.parse(applyStartTime));
		}
		if (applyEndTime != null) {
			goodwillApplyInfoDTO.setApplyEndTime(sdf.parse(applyEndTime));
		}
		if (passStartTime != null) {
			goodwillApplyInfoDTO.setPassStartTime(sdf.parse(passStartTime));
		}
		if (passEndTime != null) {
			goodwillApplyInfoDTO.setPassEndTime(sdf.parse(passEndTime));
		}
		if (noticeInvoiceStartDate != null) {
			goodwillApplyInfoDTO.setNoticeInvoiceStartDate(sdf.parse(noticeInvoiceStartDate));
		}
		if (noticeInvoiceEndDate != null) {
			goodwillApplyInfoDTO.setNoticeInvoiceEndDate(sdf.parse(noticeInvoiceEndDate));
		}
		if (invoiceStartDate != null) {
			goodwillApplyInfoDTO.setInvoiceStartDate(sdf.parse(invoiceStartDate));
		}
		if (invoiceEndDate != null) {
			goodwillApplyInfoDTO.setInvoiceEndDate(sdf.parse(invoiceEndDate));
		}
		if (updatedStartAt != null) {
			goodwillApplyInfoDTO.setUpdatedStartAt(sdf.parse(updatedStartAt));
		}
		if (updatedEndAt != null) {
			goodwillApplyInfoDTO.setUpdatedEndAt(sdf.parse(updatedEndAt));
		}
		return goodwillApplyInfoService.exportCheckedApplyInfo(goodwillApplyInfoDTO);
	}

	/**
	 * 预申请单审核通过
	 *
	 * @param goodwillApplyInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-23
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true),
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillAuditInfoDTO", name = "goodwillAuditInfoDTO", value = "", required = true)
	})
	@ApiOperation(value = "预申请单审核通过", httpMethod = "POST")
	@PostMapping(value = "/{id}/passApplyGoodwillInfo")
	@ResponseStatus(HttpStatus.CREATED)
	public int passApplyGoodwillInfo(@PathVariable("id") Long id,
			@RequestBody GoodwillAuditInfoDTO goodwillAuditInfoDTO) {
		return goodwillApplyInfoService.passApplyGoodwillInfo(id, goodwillAuditInfoDTO);
	}

	/**
	 * 预申请单审核驳回
	 *
	 * @param goodwillApplyInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-23
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true),
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillAuditInfoDTO", name = "goodwillAuditInfoDTO", value = "", required = true)
	})
	@ApiOperation(value = "预申请单审核驳回", httpMethod = "POST")
	@PostMapping(value = "/{id}/passFailApplyGoodwillInfo")
	@ResponseStatus(HttpStatus.CREATED)
	public int passFailApplyGoodwillInfo(@PathVariable("id") Long id,
			@RequestBody GoodwillAuditInfoDTO goodwillAuditInfoDTO) {
		return goodwillApplyInfoService.passFailApplyGoodwillInfo(id, goodwillAuditInfoDTO);
	}

	/**
	 * 驳回单位下拉框
	 *
	 * @param
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-23
	 */

	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true),
			@ApiImplicitParam(paramType = "path", dataType = "int", name = "goodwillStatus", value = "", required = true)
	})
	@ApiOperation(value = "驳回单位下拉框", notes = "驳回单位下拉框", httpMethod = "GET")
	@GetMapping(value = "/{id}/queryReturnTo/{goodwillStatus}")
	public List<Map> queryReturnTo(@PathVariable("id") Long id,
			@PathVariable("goodwillStatus") Integer goodwillStatus) {
		return goodwillApplyInfoService.queryReturnTo(id, goodwillStatus);
	}

	/**
	 * 根据VIN查询车辆亲善申请记录
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 * @since 2020-05-23
	 */

	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "vin", value = "", required = true),
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "applyNo", value = "", required = true)
	})
	@ApiOperation(value = "根据VIN查询车辆亲善申请记录", notes = "根据VIN查询车辆亲善申请记录", httpMethod = "GET")
	@GetMapping(value = "/{vin}/queryApplyHistory/{applyNo}")
	public List<GoodwillApplyInfoPO> queryApplyHistory(@PathVariable("vin") String vin,
			@PathVariable("applyNo") String applyNo) {
		return goodwillApplyInfoService.queryApplyHistory(vin, applyNo);
	}

	/**
	 * 查询亲善审核流程信息
	 *
	 * @param id
	 *            主键ID
	 * @return List<GoodwillApplyRepairInfoDTO>
	 * <AUTHOR>
	 * @since 2020-05-11
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "int", name = "auditObject", value = ""),
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "")
	})
	@ApiOperation(value = "查询数据", notes = "查询亲善审核流程信息", httpMethod = "GET")
	@GetMapping("/{auditObject}/queryAuditProcess/{id}")
	public List<Map> queryAuditProcess(@PathVariable(value = "auditObject", required = false) Integer auditObject,
			@PathVariable(value = "id", required = false) Long id) {
		return goodwilApplyAuditProcessService.queryAuditProcess(auditObject, id);
	}

	/**
	 * 分页查询已审核亲善单数据
	 *
	 *
	 * @param id
	 *            预申请单表id
	 * @param dealerCode
	 *            申请经销商
	 * @param goodwillNature
	 *            亲善性质
	 * @param applyTime
	 *            申请时间
	 * @param complaintId
	 *            投诉单ID
	 * @param complaintDate
	 *            投诉日期
	 * @param applyAmount
	 *            申请金额
	 * @param goodwillStatus
	 *            亲善单状态
	 * @param vin
	 *            VIN
	 * @param license
	 *            车牌号
	 * @param customerName
	 *            客户姓名
	 * @param customerMobile
	 *            客户电话
	 * @param mileage
	 *            里程
	 * @param updatedAt
	 *            修改时间
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "blocId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "areaManageId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillNature", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyPerson", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerMobile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintFalut", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillStatus", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyStartAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyEndAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedStartAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedEndAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询已审核亲善单数据", httpMethod = "GET")
	@RequestMapping(value = "/dealerTodo", method = RequestMethod.GET)
	public IPage<GoodwillApplyInfoDTO> getDealerTodo(@RequestParam(value = "blocId", required = false) Integer blocId,
			@RequestParam(value = "areaManageId", required = false) Integer areaManageId,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "goodwillNature", required = false) Integer goodwillNature,
			@RequestParam(value = "applyPerson", required = false) String applyPerson,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "auditType", required = false) Integer auditType,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "customerName", required = false) String customerName,
			@RequestParam(value = "customerMobile", required = false) String customerMobile,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "complaintId", required = false) String complaintId,
			@RequestParam(value = "complaintFalut", required = false) String complaintFalut,
			@RequestParam(value = "goodwillStatus", required = false) Integer goodwillStatus,
			@RequestParam(value = "applyStartTime", required = false) String applyStartTime,
			@RequestParam(value = "applyEndTime", required = false) String applyEndTime,
			@RequestParam(value = "applyStartAmount", required = false) BigDecimal applyStartAmount,
			@RequestParam(value = "applyEndAmount", required = false) BigDecimal applyEndAmount,
			@RequestParam(value = "passStartTime", required = false) String passStartTime,
			@RequestParam(value = "passEndTime", required = false) String passEndTime,
			@RequestParam(value = "noticeInvoiceStartDate", required = false) String noticeInvoiceStartDate,
			@RequestParam(value = "noticeInvoiceEndDate", required = false) String noticeInvoiceEndDate,
			@RequestParam(value = "invoiceStartDate", required = false) String invoiceStartDate,
			@RequestParam(value = "invoiceEndDate", required = false) String invoiceEndDate,
			@RequestParam(value = "updatedStartAt", required = false) String updatedStartAt,
			@RequestParam(value = "updatedEndAt", required = false) String updatedEndAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyInfoPO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		goodwillApplyInfoDTO.setBlocId(blocId);
		goodwillApplyInfoDTO.setAreaManageId(areaManageId);
		goodwillApplyInfoDTO.setDealerCode(dealerCode);
		goodwillApplyInfoDTO.setAuditType(auditType);
		goodwillApplyInfoDTO.setGoodwillNature(goodwillNature);
		goodwillApplyInfoDTO.setApplyPerson(applyPerson);
		goodwillApplyInfoDTO.setApplyNo(applyNo);
		goodwillApplyInfoDTO.setId(id);
		goodwillApplyInfoDTO.setCustomerName(customerName);
		goodwillApplyInfoDTO.setCustomerMobile(customerMobile);
		goodwillApplyInfoDTO.setVin(vin);
		goodwillApplyInfoDTO.setLicense(license);
		goodwillApplyInfoDTO.setComplaintId(complaintId);
		goodwillApplyInfoDTO.setComplaintFalut(complaintFalut);
		goodwillApplyInfoDTO.setGoodwillStatus(goodwillStatus);
		goodwillApplyInfoDTO.setApplyStartAmount(applyStartAmount);
		goodwillApplyInfoDTO.setApplyEndAmount(applyEndAmount);
		if (applyStartTime != null) {
			goodwillApplyInfoDTO.setApplyStartTime(sdf.parse(applyStartTime));
		}
		if (applyEndTime != null) {
			goodwillApplyInfoDTO.setApplyEndTime(sdf.parse(applyEndTime));
		}
		if (passStartTime != null) {
			goodwillApplyInfoDTO.setPassStartTime(sdf.parse(passStartTime));
		}
		if (passEndTime != null) {
			goodwillApplyInfoDTO.setPassEndTime(sdf.parse(passEndTime));
		}
		if (noticeInvoiceStartDate != null) {
			goodwillApplyInfoDTO.setNoticeInvoiceStartDate(sdf.parse(noticeInvoiceStartDate));
		}
		if (noticeInvoiceEndDate != null) {
			goodwillApplyInfoDTO.setNoticeInvoiceEndDate(sdf.parse(noticeInvoiceEndDate));
		}
		if (invoiceStartDate != null) {
			goodwillApplyInfoDTO.setInvoiceStartDate(sdf.parse(invoiceStartDate));
		}
		if (invoiceEndDate != null) {
			goodwillApplyInfoDTO.setInvoiceEndDate(sdf.parse(invoiceEndDate));
		}
		if (updatedStartAt != null) {
			goodwillApplyInfoDTO.setUpdatedStartAt(sdf.parse(updatedStartAt));
		}
		if (updatedEndAt != null) {
			goodwillApplyInfoDTO.setUpdatedEndAt(sdf.parse(updatedEndAt));
		}
		return goodwillApplyInfoService.getDealerTodo(page, goodwillApplyInfoDTO);
	}

	/**
	 * 提交预申请单材料上传信息
	 *
	 * @param goodwillApplyInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-23
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyInfoDTO", name = "goodwillApplyInfoDTO", value = "", required = true)
	})
	@ApiOperation(value = "提交预申请单材料上传信息", notes = "提交预申请单材料上传信息", httpMethod = "POST")
	@PostMapping(value = "/commitDealerSupportApplyInfo")
	@ResponseStatus(HttpStatus.CREATED)
	public int commitDealerSupportApplyInfo(@RequestBody GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		return goodwillApplyInfoService.commitDealerSupportApplyInfo(goodwillApplyInfoDTO);
	}

	/**
	 * 保存亲善预申请单信息
	 *
	 * @param goodwillApplyInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyInfoDTO", name = "goodwillApplyInfoDTO", value = "", required = true)
	})
	@ApiOperation(value = "保存亲善预申请单信息", notes = "保存亲善预申请单信息", httpMethod = "POST")
	@PostMapping(value = "/editSupportApplyInfo")
	@ResponseStatus(HttpStatus.CREATED)
	public int editSupportApplyInfo(@RequestBody GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		return goodwillApplyInfoService.editSupportApplyInfo(goodwillApplyInfoDTO);
	}

	/**
	 * 提交亲善预申请单信息
	 *
	 * @param goodwillApplyInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-23
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyInfoDTO", name = "goodwillApplyInfoDTO", value = "", required = true)
	})
	@ApiOperation(value = "提交亲善预申请单信息", notes = "提交亲善预申请单信息", httpMethod = "POST")
	@PostMapping(value = "/commitCcmqSupportApplyInfo")
	@ResponseStatus(HttpStatus.CREATED)
	public int commitCcmqSupportApplyInfo(@RequestBody GoodwillApplyInfoDTO goodwillApplyInfoDTO) {
		return goodwillApplyInfoService.commitCcmqSupportApplyInfo(goodwillApplyInfoDTO);
	}

	/**
	 * 查询亲善审核历史信息
	 *
	 * @param id
	 *            主键ID
	 * @return List<GoodwillApplyRepairInfoDTO>
	 * <AUTHOR>
	 * @since 2020-05-11
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "int", name = "auditObject", value = ""),
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "")
	})
	@ApiOperation(value = "查询数据", notes = "查询亲善审核历史信息", httpMethod = "GET")
	@GetMapping("/{auditObject}/queryApplyAuditHistoryInfo/{id}")
	public List<Map> queryApplyAuditHistoryInfo(
			@PathVariable(value = "auditObject", required = false) Integer auditObject,
			@PathVariable(value = "id", required = false) Long id) {
		return goodwillAuditInfoService.queryApplyAuditHistoryInfo(auditObject, id);
	}

	/**
	 * 查看亲善单驳回信息
	 *
	 * @return List<Map>
	 * <AUTHOR>
	 * @since 2020-06-05
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditObject", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "role", value = "")
	})
	@ApiOperation(value = "查看亲善单驳回信息", notes = "查看亲善单驳回信息", httpMethod = "GET")
	@RequestMapping(value = "/queryReturnList", method = RequestMethod.GET)
	public List<Map> queryReturnList(@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "auditObject", required = false) Integer auditObject,
			@RequestParam(value = "role", required = false) String role) {
		GoodwillAuditInfoDTO goodwillAuditInfoDto = new GoodwillAuditInfoDTO();
		goodwillAuditInfoDto.setGoodwillApplyId(id);
		goodwillAuditInfoDto.setAuditObject(auditObject);
		goodwillAuditInfoDto.setAuditRole(role);
		return goodwillAuditInfoService.queryReturnList(goodwillAuditInfoDto);
	}

	/**
	 * 亲善申请开票信息查询
	 *
	 * @param id
	 *            数据主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillNoticeInvoiceInfoDTO
	 * <AUTHOR>
	 * @since 2020-06-17
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "int", name = "invoiceTitle", value = "", required = true)
	})
	@ApiOperation(value = "亲善申请开票信息查询", notes = "亲善申请开票信息查询", httpMethod = "GET")
	@GetMapping(value = "/{invoiceTitle}/queryInvoiceInfo")
	public GoodwillInvoiceTitleInfoDTO queryInvoiceInfo(@PathVariable("invoiceTitle") Integer invoiceTitle) {
		return goodwillInvoiceTitleInfoService.queryInvoiceInfo(invoiceTitle);
	}

	/**
	 * 保存亲善预申请单通知开票信息
	 *
	 * @param GoodwillNoticeInvoiceInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillNoticeInvoiceInfoDTO", name = "goodwillNoticeInvoiceInfoDto", value = "", required = true)
	})
	@ApiOperation(value = "保存亲善预申请单通知开票信息", notes = "保存亲善预申请单通知开票信息", httpMethod = "POST")
	@PostMapping(value = "/editNoticeInvoiceInfo")
	@ResponseStatus(HttpStatus.CREATED)
	public int editNoticeInvoiceInfo(@RequestBody GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto) {
		return goodwillApplyInfoService.editNoticeInvoiceInfo(goodwillNoticeInvoiceInfoDto);
		// return goodwillNoticeInvoiceInfoService.insert(goodwillNoticeInvoiceInfoDto);
	}

	/**
	 * 根据亲善单ID查询付款信息
	 *
	 * @param id
	 *            数据主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO
	 * <AUTHOR>
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "goodwillApplyId", value = "", required = true)
	})
	@ApiOperation(value = "根据亲善单ID查询付款信息", notes = "根据亲善单ID查询付款信息", httpMethod = "GET")
	@GetMapping(value = "/queryPaymentInfoByGoodwillApplyId/{goodwillApplyId}")
	public GoodwillNoticeInvoiceInfoDTO queryPaymentInfoByGoodwillApplyId(
			@PathVariable("goodwillApplyId") Long goodwillApplyId) {
		return goodwillApplyInfoService.queryPaymentInfoByGoodwillApplyId(goodwillApplyId);
	}

	/**
	 * 保存录入开票信息
	 *
	 * @param GoodwillNoticeInvoiceInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillNoticeInvoiceInfoDTO", name = "goodwillNoticeInvoiceInfoDto", value = "", required = true)
	})
	@ApiOperation(value = "保存录入开票信息", notes = "保存录入开票信息", httpMethod = "POST")
	@PostMapping(value = "/saveInvoice")
	@ResponseStatus(HttpStatus.CREATED)
	public int saveInvoice(@RequestBody GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto) {
		return goodwillApplyInfoService.saveInvoice(goodwillNoticeInvoiceInfoDto);
		// return goodwillNoticeInvoiceInfoService.insert(goodwillNoticeInvoiceInfoDto);
	}

	/**
	 * 提交录入开票信息
	 *
	 * @param GoodwillNoticeInvoiceInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillNoticeInvoiceInfoDTO", name = "goodwillNoticeInvoiceInfoDto", value = "", required = true)
	})
	@ApiOperation(value = "提交录入开票信息", notes = "提交录入开票信息", httpMethod = "POST")
	@PostMapping(value = "/commitInvoice")
	@ResponseStatus(HttpStatus.CREATED)
	public int commitInvoice(@RequestBody GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto) {
		return goodwillApplyInfoService.commitInvoice(goodwillNoticeInvoiceInfoDto);
		// return goodwillNoticeInvoiceInfoService.insert(goodwillNoticeInvoiceInfoDto);
	}

	/**
	 * 查询录入发票信息
	 *
	 * @param id
	 *            主键ID
	 * @return List<GoodwillApplyRepairInfoDTO>
	 * <AUTHOR>
	 * @since 2020-05-11
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "")
	})
	@ApiOperation(value = "查询数据", notes = "查询录入发票信息", httpMethod = "GET")
	@GetMapping("/queryInvoiceInfo/{id}")
	public List<Map> queryInvoiceInfo(@PathVariable(value = "id", required = false) Long id) {
		return goodwillApplyInfoService.queryInvoiceInfo(id);
	}

	/**
	 * 厂端确认录入发票信息
	 *
	 * @param id
	 *            主键ID
	 * @return List<GoodwillApplyRepairInfoDTO>
	 * <AUTHOR>
	 * @since 2020-05-11
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "")
	})
	@ApiOperation(value = "查询数据", notes = "厂端确认录入发票信息", httpMethod = "GET")
	@GetMapping("/queryOemInvoiceInfo/{id}")
	public List<Map> queryOemInvoiceInfo(@PathVariable(value = "id", required = false) Long id) {
		return goodwillApplyInfoService.queryOemInvoiceInfo(id);
	}

	/**
	 * 确认发票信息
	 *
	 * @param GoodwillNoticeInvoiceInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillNoticeInvoiceInfoDTO", name = "goodwillNoticeInvoiceInfoDto", value = "", required = true)
	})
	@ApiOperation(value = "确认发票信息", notes = "确认发票信息", httpMethod = "POST")
	@PostMapping(value = "/saveInvoiceConfirm")
	@ResponseStatus(HttpStatus.CREATED)
	public int saveInvoiceConfirm(@RequestBody GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto) {
		return goodwillApplyInfoService.saveInvoiceConfirm(goodwillNoticeInvoiceInfoDto);
		// return goodwillNoticeInvoiceInfoService.insert(goodwillNoticeInvoiceInfoDto);
	}

	/**
	 * 预申请单拒绝支持
	 *
	 * @param goodwillApplyInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-23
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true),
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillAuditInfoDTO", name = "goodwillAuditInfoDTO", value = "", required = true)
	})
	@ApiOperation(value = "预申请单拒绝支持", notes = "预申请单拒绝支持", httpMethod = "POST")
	@PostMapping(value = "/{id}/refuseApplyGoodwillInfo")
	@ResponseStatus(HttpStatus.CREATED)
	public int refuseApplyGoodwillInfo(@PathVariable("id") Long id,
			@RequestBody GoodwillAuditInfoDTO goodwillAuditInfoDTO) {
		return goodwillApplyInfoService.refuseApplyGoodwillInfo(id, goodwillAuditInfoDTO);
	}

	/**
	 * 预申请单重启
	 *
	 * @param goodwillApplyInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-23
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true),
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillAuditInfoDTO", name = "goodwillAuditInfoDTO", value = "", required = true)
	})
	@ApiOperation(value = "预申请单重启", notes = "预申请单重启", httpMethod = "POST")
	@PostMapping(value = "/{id}/restartApplyGoodwillInfo")
	@ResponseStatus(HttpStatus.CREATED)
	public int restartApplyGoodwillInfo(@PathVariable("id") Long id,
			@RequestBody GoodwillAuditInfoDTO goodwillAuditInfoDTO) {
		return goodwillApplyInfoService.restartApplyGoodwillInfo(id, goodwillAuditInfoDTO);
	}

	/**
	 * 预申请单拒绝支持
	 *
	 * @param goodwillApplyInfoDTO
	 *            需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-05-23
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true),
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillAuditInfoDTO", name = "goodwillAuditInfoDTO", value = "", required = true)
	})
	@ApiOperation(value = "预申请单拒绝支持", notes = "预申请单拒绝支持", httpMethod = "POST")
	@PostMapping(value = "/{id}/noNeedApplyGoodwillInfo")
	@ResponseStatus(HttpStatus.CREATED)
	public int noNeedApplyGoodwillInfo(@PathVariable("id") Long id,
			@RequestBody GoodwillAuditInfoDTO goodwillAuditInfoDTO) {
		return goodwillApplyInfoService.noNeedApplyGoodwillInfo(id, goodwillAuditInfoDTO);
	}

	/**
	 * 分页查询亲善审计数据
	 *
	 * @param appId
	 *            系统ID
	 * @param ownerCode
	 *            所有者代码
	 * @param ownerParCode
	 *            所有者的父组织代码
	 * @param orgId
	 *            组织ID
	 * @param id
	 *            预申请单表id
	 * @param applyNo
	 *            预申请单号
	 * @param dealerCode
	 *            申请经销商
	 * @param auditType
	 *            审核类型
	 * @param goodwillNature
	 *            亲善性质
	 * @param applyTime
	 *            申请时间
	 * @param complaintFalut
	 *            投诉故障
	 * @param complaintId
	 *            投诉单ID
	 * @param complaintDate
	 *            投诉日期
	 * @param vehicleUse
	 *            用车情况
	 * @param salesDealer
	 *            销售经销商
	 * @param applyAmount
	 *            申请金额
	 * @param auditAmount
	 *            审批金额
	 * @param settlementAmount
	 *            结算金额
	 * @param invoiceAmount
	 *            通知开票金额
	 * @param goodwillStatus
	 *            亲善单状态
	 * @param customerPain
	 *            客户痛点
	 * @param vin
	 *            VIN
	 * @param license
	 *            车牌号
	 * @param customerName
	 *            客户姓名
	 * @param customerMobile
	 *            客户电话
	 * @param mileage
	 *            里程
	 * @param model
	 *            车型
	 * @param buyCarDate
	 *            购车日期
	 * @param warrantyStartDate
	 *            保修开始日期
	 * @param isExtendWarranty
	 *            是否延保
	 * @param extendWarrantyName
	 *            延保名称
	 * @param extendWarrantyStartDate
	 *            延保开始日期
	 * @param extendWarrantyEndDate
	 *            延保结束日期
	 * @param maintainCost
	 *            保养成本
	 * @param extendWarrantyCost
	 *            延保成本
	 * @param accessoryCost
	 *            附件精品成本
	 * @param voucherCost
	 *            代金券成本
	 * @param walkingCarPrice
	 *            代步车/相关利益
	 * @param volvoIntegral
	 *            沃世界积分
	 * @param returnChangeCarPrice
	 *            退换车
	 * @param otherPrice
	 *            其他
	 * @param costTotal
	 *            成本总计
	 * @param customerPay
	 *            客户支付
	 * @param dealerUndertake
	 *            经销商承担
	 * @param volvoSupportGoodwillAmount
	 *            volvo支持亲善金额
	 * @param remark
	 *            备注
	 * @param applyFile
	 *            预申请附件
	 * @param costStatisticsFile
	 *            亲善成本统计
	 * @param costScreenshotFile
	 *            亲善成本截图
	 * @param commitTime
	 *            提交时间
	 * @param isValid
	 *            是否有效
	 * @param isDeleted
	 *            是否删除:1,删除；0,未删除
	 * @param createdAt
	 *            创建时间
	 * @param updatedAt
	 *            修改时间
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "bloc", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillNature", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintFalut", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "vehicleUse", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "salesDealer", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "auditAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "settlementAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "invoiceAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillStatus", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerPain", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerMobile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "mileage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "model", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "buyCarDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "warrantyStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isExtendWarranty", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "maintainCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "extendWarrantyCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "accessoryCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "voucherCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "walkingCarPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "volvoIntegral", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "returnChangeCarPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "otherPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "costTotal", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "customerPay", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "dealerUndertake", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "volvoSupportGoodwillAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "remark", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerIdentification", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "elseFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "commitTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isAudit", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditWay", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditResult", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyStartAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyEndAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询亲善审计数据", httpMethod = "GET")
	@GetMapping(value = "/querySupportApplyAuditInfo")
	public IPage<GoodwillApplyInfoDTO> querySupportApplyAuditInfo(
			@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "bloc", required = false) Integer bloc,
			@RequestParam(value = "areaManage", required = false) Integer areaManage,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "auditType", required = false) Integer auditType,
			@RequestParam(value = "goodwillNature", required = false) Integer goodwillNature,
			@RequestParam(value = "applyTime", required = false) String applyTime,
			@RequestParam(value = "complaintFalut", required = false) String complaintFalut,
			@RequestParam(value = "complaintId", required = false) String complaintId,
			@RequestParam(value = "complaintDate", required = false) String complaintDate,
			@RequestParam(value = "vehicleUse", required = false) Integer vehicleUse,
			@RequestParam(value = "salesDealer", required = false) String salesDealer,
			@RequestParam(value = "applyAmount", required = false) BigDecimal applyAmount,
			@RequestParam(value = "auditAmount", required = false) BigDecimal auditAmount,
			@RequestParam(value = "settlementAmount", required = false) BigDecimal settlementAmount,
			@RequestParam(value = "invoiceAmount", required = false) BigDecimal invoiceAmount,
			@RequestParam(value = "goodwillStatus", required = false) Integer goodwillStatus,
			@RequestParam(value = "customerPain", required = false) String customerPain,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "customerName", required = false) String customerName,
			@RequestParam(value = "customerMobile", required = false) String customerMobile,
			@RequestParam(value = "mileage", required = false) String mileage,
			@RequestParam(value = "model", required = false) String model,
			@RequestParam(value = "buyCarDate", required = false) String buyCarDate,
			@RequestParam(value = "warrantyStartDate", required = false) String warrantyStartDate,
			@RequestParam(value = "isExtendWarranty", required = false) Integer isExtendWarranty,
			@RequestParam(value = "extendWarrantyName", required = false) String extendWarrantyName,
			@RequestParam(value = "extendWarrantyStartDate", required = false) String extendWarrantyStartDate,
			@RequestParam(value = "extendWarrantyEndDate", required = false) String extendWarrantyEndDate,
			@RequestParam(value = "maintainCost", required = false) BigDecimal maintainCost,
			@RequestParam(value = "extendWarrantyCost", required = false) BigDecimal extendWarrantyCost,
			@RequestParam(value = "accessoryCost", required = false) BigDecimal accessoryCost,
			@RequestParam(value = "voucherCost", required = false) BigDecimal voucherCost,
			@RequestParam(value = "walkingCarPrice", required = false) BigDecimal walkingCarPrice,
			@RequestParam(value = "volvoIntegral", required = false) BigDecimal volvoIntegral,
			@RequestParam(value = "returnChangeCarPrice", required = false) BigDecimal returnChangeCarPrice,
			@RequestParam(value = "otherPrice", required = false) BigDecimal otherPrice,
			@RequestParam(value = "costTotal", required = false) BigDecimal costTotal,
			@RequestParam(value = "customerPay", required = false) BigDecimal customerPay,
			@RequestParam(value = "dealerUndertake", required = false) BigDecimal dealerUndertake,
			@RequestParam(value = "volvoSupportGoodwillAmount", required = false) BigDecimal volvoSupportGoodwillAmount,
			@RequestParam(value = "remark", required = false) String remark,
			@RequestParam(value = "customerIdentification", required = false) String customerIdentification,
			@RequestParam(value = "elseFile", required = false) String elseFile,
			@RequestParam(value = "commitTime", required = false) String commitTime,
			@RequestParam(value = "isAudit", required = false) Integer isAudit,
			@RequestParam(value = "auditWay", required = false) Integer auditWay,
			@RequestParam(value = "auditResult", required = false) Integer auditResult,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "applyStartAmount", required = false) BigDecimal applyStartAmount,
			@RequestParam(value = "applyEndAmount", required = false) BigDecimal applyEndAmount,
			@RequestParam(value = "auditStartTime", required = false) String auditStartTime,
			@RequestParam(value = "auditEndTime", required = false) String auditEndTime,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyInfoPO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		goodwillApplyInfoDTO.setAppId(appId);
		goodwillApplyInfoDTO.setOwnerCode(ownerCode);
		goodwillApplyInfoDTO.setOwnerParCode(ownerParCode);
		goodwillApplyInfoDTO.setOrgId(orgId);
		goodwillApplyInfoDTO.setId(id);
		goodwillApplyInfoDTO.setBlocId(bloc);
		goodwillApplyInfoDTO.setAreaManageId(areaManage);
		goodwillApplyInfoDTO.setApplyNo(applyNo);
		goodwillApplyInfoDTO.setDealerCode(dealerCode);
		goodwillApplyInfoDTO.setAuditType(auditType);
		goodwillApplyInfoDTO.setGoodwillNature(goodwillNature);
		goodwillApplyInfoDTO.setComplaintFalut(complaintFalut);
		goodwillApplyInfoDTO.setComplaintId(complaintId);
		goodwillApplyInfoDTO.setVehicleUse(vehicleUse);
		goodwillApplyInfoDTO.setSalesDealer(salesDealer);
		goodwillApplyInfoDTO.setApplyAmount(applyAmount);
		goodwillApplyInfoDTO.setAuditAmount(auditAmount);
		goodwillApplyInfoDTO.setSettlementAmount(settlementAmount);
		goodwillApplyInfoDTO.setInvoiceAmount(invoiceAmount);
		goodwillApplyInfoDTO.setGoodwillStatus(goodwillStatus);
		// goodwillApplyInfoDTO.setCustomerPain(customerPain);
		goodwillApplyInfoDTO.setVin(vin);
		goodwillApplyInfoDTO.setLicense(license);
		goodwillApplyInfoDTO.setCustomerName(customerName);
		goodwillApplyInfoDTO.setCustomerMobile(customerMobile);
		goodwillApplyInfoDTO.setMileage(mileage);
		goodwillApplyInfoDTO.setModel(model);
		goodwillApplyInfoDTO.setIsExtendWarranty(isExtendWarranty);
		goodwillApplyInfoDTO.setExtendWarrantyName(extendWarrantyName);
		goodwillApplyInfoDTO.setMaintainCost(maintainCost);
		goodwillApplyInfoDTO.setExtendWarrantyCost(extendWarrantyCost);
		goodwillApplyInfoDTO.setAccessoryCost(accessoryCost);
		goodwillApplyInfoDTO.setVoucherCost(voucherCost);
		goodwillApplyInfoDTO.setWalkingCarPrice(walkingCarPrice);
		goodwillApplyInfoDTO.setVolvoIntegral(volvoIntegral);
		goodwillApplyInfoDTO.setReturnChangeCarPrice(returnChangeCarPrice);
		goodwillApplyInfoDTO.setOtherPrice(otherPrice);
		goodwillApplyInfoDTO.setCostTotal(costTotal);
		goodwillApplyInfoDTO.setCustomerPay(customerPay);
		goodwillApplyInfoDTO.setDealerUndertake(dealerUndertake);
		goodwillApplyInfoDTO.setVolvoSupportGoodwillAmount(volvoSupportGoodwillAmount);
		goodwillApplyInfoDTO.setRemark(remark);
		goodwillApplyInfoDTO.setCustomerIdentification(customerIdentification);
		goodwillApplyInfoDTO.setElseFile(elseFile);
		goodwillApplyInfoDTO.setIsValid(isValid);
		goodwillApplyInfoDTO.setIsAudit(isAudit);
		goodwillApplyInfoDTO.setAuditWay(auditWay);
		goodwillApplyInfoDTO.setAuditResult(auditResult);
		goodwillApplyInfoDTO.setIsDeleted(isDeleted);
		goodwillApplyInfoDTO.setApplyStartAmount(applyStartAmount);
		goodwillApplyInfoDTO.setApplyEndAmount(applyEndAmount);
		if (applyTime != null) {
			goodwillApplyInfoDTO.setApplyTime(sdf.parse(applyTime));
		}
		if (auditStartTime != null) {
			goodwillApplyInfoDTO.setAuditStartTime(sdf.parse(auditStartTime));
		}
		if (auditEndTime != null) {
			goodwillApplyInfoDTO.setAuditEndTime(sdf.parse(auditEndTime));
		}
		if (warrantyStartDate != null) {
			goodwillApplyInfoDTO.setWarrantyStartDate(sdf.parse(warrantyStartDate));
		}
		if (extendWarrantyStartDate != null) {
			goodwillApplyInfoDTO.setExtendWarrantyStartDate(sdf.parse(extendWarrantyStartDate));
		}
		if (extendWarrantyEndDate != null) {
			goodwillApplyInfoDTO.setExtendWarrantyEndDate(sdf.parse(extendWarrantyEndDate));
		}
		if (commitTime != null) {
			goodwillApplyInfoDTO.setCommitTime(sdf.parse(commitTime));
		}
		if (createdAt != null) {
			goodwillApplyInfoDTO.setCreatedAt(sdf.parse(createdAt));
		}
		if (updatedAt != null) {
			goodwillApplyInfoDTO.setUpdatedAt(sdf.parse(updatedAt));
		}
		return goodwillApplyInfoService.querySupportApplyAuditInfo(page, goodwillApplyInfoDTO);
	}

	/**
	 * 分页查询开票信息
	 *
	 * @param role
	 *            角色
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-04-22
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
	})
	@ApiOperation(value = "分页查询开票信息", httpMethod = "GET")
	@GetMapping(value = "/queryNoticeInvoiceInfo")
	public IPage<List> queryNoticeInvoiceInfo(
			@RequestParam(value = "goodwillApplyId", required = true) Long goodwillApplyId,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillApplyInfoPO> page = new Page(currentPage, pageSize);
		return goodwillApplyInfoService.queryNoticeInvoiceInfo(page, goodwillApplyId);
	}

	/**
	 * 查询打印AWA信息
	 *
	 * @param noticeInvoiceId
	 * @return Map
	 * <AUTHOR>
	 * @since 2020/7/13
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "noticeInvoiceId", value = "", required = true)
	})
	@ApiOperation(value = "查询打印AWA信息", notes = "查询打印AWA信息", httpMethod = "GET")
	@GetMapping(value = "/queryPrintInfo/{noticeInvoiceId}")
	public Map queryPrintInfo(@PathVariable(value = "noticeInvoiceId") Long noticeInvoiceId) {
		return goodwillApplyInfoService.queryPrintInfo(noticeInvoiceId);

	}

	/**
	 * 导出厂端亲善申请查询数据
	 *
	 * @param appId
	 *            系统ID
	 * @param ownerCode
	 *            所有者代码
	 * @param ownerParCode
	 *            所有者的父组织代码
	 * @param orgId
	 *            组织ID
	 * @param id
	 *            预申请单表id
	 * @param applyNo
	 *            预申请单号
	 * @param dealerCode
	 *            申请经销商
	 * @param auditType
	 *            审核类型
	 * @param goodwillNature
	 *            亲善性质
	 * @param applyTime
	 *            申请时间
	 * @param complaintFalut
	 *            投诉故障
	 * @param complaintId
	 *            投诉单ID
	 * @param complaintDate
	 *            投诉日期
	 * @param vehicleUse
	 *            用车情况
	 * @param salesDealer
	 *            销售经销商
	 * @param applyAmount
	 *            申请金额
	 * @param auditAmount
	 *            审批金额
	 * @param settlementAmount
	 *            结算金额
	 * @param invoiceAmount
	 *            通知开票金额
	 * @param goodwillStatus
	 *            亲善单状态
	 * @param customerPain
	 *            客户痛点
	 * @param vin
	 *            VIN
	 * @param license
	 *            车牌号
	 * @param customerName
	 *            客户姓名
	 * @param customerMobile
	 *            客户电话
	 * @param mileage
	 *            里程
	 * @param model
	 *            车型
	 * @param buyCarDate
	 *            购车日期
	 * @param warrantyStartDate
	 *            保修开始日期
	 * @param isExtendWarranty
	 *            是否延保
	 * @param extendWarrantyName
	 *            延保名称
	 * @param extendWarrantyStartDate
	 *            延保开始日期
	 * @param extendWarrantyEndDate
	 *            延保结束日期
	 * @param isValid
	 *            是否有效
	 * @param isDeleted
	 *            是否删除:1,删除；0,未删除
	 * @param createdAt
	 *            创建时间
	 * @param updatedAt
	 *            修改时间
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "bloc", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "smallAreaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "smallArea", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "auditor", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "roleList1", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillNature", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintFalut", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "String[]", name = "complaintFalut1", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "vehicleUse", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "salesDealer", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "auditAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "settlementAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "invoiceAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillStatus", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerPain", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerMobile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "mileage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "model", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "buyCarDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "warrantyStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isExtendWarranty", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "maintainCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "extendWarrantyCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "accessoryCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "voucherCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "walkingCarPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "volvoIntegral", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "returnChangeCarPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "otherPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "costTotal", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "customerPay", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "dealerUndertake", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "volvoSupportGoodwillAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "remark", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerIdentification", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "elseFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "commitTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyStartAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyEndAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "invoiceEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedStartAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedEndAt", value = "")
	})
	@ApiOperation(value = "导出厂端亲善申请查询数据", httpMethod = "GET")
	@GetMapping(value = "/exportSupportApplyOemSearchInfo")
	public List<GoodwillApplyInfoDTO> exportSupportApplyOemSearchInfo(
			@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "bloc", required = false) Integer bloc,
			@RequestParam(value = "areaManage", required = false) String areaManage,
			@RequestParam(value = "smallAreaManage", required = false) String smallAreaManage,
			@RequestParam(value = "smallArea", required = false) Integer smallArea,
			@RequestParam(value = "auditor", required = false) Long auditor,
			@RequestParam(value = "auditName", required = false) String auditName,
			@RequestParam(value = "roleList1", required = false) String roleList1,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "auditType", required = false) Integer auditType,
			@RequestParam(value = "goodwillNature", required = false) Integer goodwillNature,
			@RequestParam(value = "applyTime", required = false) String applyTime,
			@RequestParam(value = "complaintFalut", required = false) String complaintFalut,
			@RequestParam(value = "complaintFalut1", required = false) String[] complaintFalut1,
			@RequestParam(value = "complaintId", required = false) String complaintId,
			@RequestParam(value = "complaintDate", required = false) String complaintDate,
			@RequestParam(value = "vehicleUse", required = false) Integer vehicleUse,
			@RequestParam(value = "salesDealer", required = false) String salesDealer,
			@RequestParam(value = "applyAmount", required = false) BigDecimal applyAmount,
			@RequestParam(value = "auditAmount", required = false) BigDecimal auditAmount,
			@RequestParam(value = "settlementAmount", required = false) BigDecimal settlementAmount,
			@RequestParam(value = "invoiceAmount", required = false) BigDecimal invoiceAmount,
			@RequestParam(value = "goodwillStatus", required = false) Integer goodwillStatus,
			@RequestParam(value = "customerPain", required = false) String customerPain,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "customerName", required = false) String customerName,
			@RequestParam(value = "customerMobile", required = false) String customerMobile,
			@RequestParam(value = "mileage", required = false) String mileage,
			@RequestParam(value = "model", required = false) String model,
			@RequestParam(value = "buyCarDate", required = false) String buyCarDate,
			@RequestParam(value = "warrantyStartDate", required = false) String warrantyStartDate,
			@RequestParam(value = "isExtendWarranty", required = false) Integer isExtendWarranty,
			@RequestParam(value = "extendWarrantyName", required = false) String extendWarrantyName,
			@RequestParam(value = "extendWarrantyStartDate", required = false) String extendWarrantyStartDate,
			@RequestParam(value = "extendWarrantyEndDate", required = false) String extendWarrantyEndDate,
			@RequestParam(value = "maintainCost", required = false) BigDecimal maintainCost,
			@RequestParam(value = "extendWarrantyCost", required = false) BigDecimal extendWarrantyCost,
			@RequestParam(value = "accessoryCost", required = false) BigDecimal accessoryCost,
			@RequestParam(value = "voucherCost", required = false) BigDecimal voucherCost,
			@RequestParam(value = "walkingCarPrice", required = false) BigDecimal walkingCarPrice,
			@RequestParam(value = "volvoIntegral", required = false) BigDecimal volvoIntegral,
			@RequestParam(value = "returnChangeCarPrice", required = false) BigDecimal returnChangeCarPrice,
			@RequestParam(value = "otherPrice", required = false) BigDecimal otherPrice,
			@RequestParam(value = "costTotal", required = false) BigDecimal costTotal,
			@RequestParam(value = "customerPay", required = false) BigDecimal customerPay,
			@RequestParam(value = "dealerUndertake", required = false) BigDecimal dealerUndertake,
			@RequestParam(value = "volvoSupportGoodwillAmount", required = false) BigDecimal volvoSupportGoodwillAmount,
			@RequestParam(value = "remark", required = false) String remark,
			@RequestParam(value = "customerIdentification", required = false) String customerIdentification,
			@RequestParam(value = "elseFile", required = false) String elseFile,
			@RequestParam(value = "commitTime", required = false) String commitTime,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt,
			@RequestParam(value = "applyStartTime", required = false) String applyStartTime,
			@RequestParam(value = "applyEndTime", required = false) String applyEndTime,
			@RequestParam(value = "applyStartAmount", required = false) BigDecimal applyStartAmount,
			@RequestParam(value = "applyEndAmount", required = false) BigDecimal applyEndAmount,
			@RequestParam(value = "passStartTime", required = false) String passStartTime,
			@RequestParam(value = "passEndTime", required = false) String passEndTime,
			@RequestParam(value = "noticeInvoiceStartDate", required = false) String noticeInvoiceStartDate,
			@RequestParam(value = "noticeInvoiceEndDate", required = false) String noticeInvoiceEndDate,
			@RequestParam(value = "invoiceStartDate", required = false) String invoiceStartDate,
			@RequestParam(value = "invoiceEndDate", required = false) String invoiceEndDate,
			@RequestParam(value = "updatedStartAt", required = false) String updatedStartAt,
			@RequestParam(value = "updatedEndAt", required = false) String updatedEndAt) throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		goodwillApplyInfoDTO.setAppId(appId);
		goodwillApplyInfoDTO.setOwnerCode(ownerCode);
		goodwillApplyInfoDTO.setOwnerParCode(ownerParCode);
		goodwillApplyInfoDTO.setOrgId(orgId);
		goodwillApplyInfoDTO.setId(id);
		goodwillApplyInfoDTO.setRoleList1(roleList1);
		goodwillApplyInfoDTO.setApplyNo(applyNo);
		goodwillApplyInfoDTO.setBlocId(bloc);
		goodwillApplyInfoDTO.setAreaManageId1(areaManage);
		goodwillApplyInfoDTO.setSmallAreaId1(smallAreaManage);
		goodwillApplyInfoDTO.setSmallAreaId(smallArea);
		goodwillApplyInfoDTO.setAuditor(auditor);
		goodwillApplyInfoDTO.setAuditName1(Utills.getList(auditName));
		goodwillApplyInfoDTO.setDealerCode(dealerCode);
		goodwillApplyInfoDTO.setAuditType(auditType);
		goodwillApplyInfoDTO.setGoodwillNature(goodwillNature);
		goodwillApplyInfoDTO.setComplaintFalut1(complaintFalut1);
		goodwillApplyInfoDTO.setComplaintFalut(complaintFalut);
		goodwillApplyInfoDTO.setComplaintId(complaintId);
		goodwillApplyInfoDTO.setVehicleUse(vehicleUse);
		goodwillApplyInfoDTO.setSalesDealer(salesDealer);
		goodwillApplyInfoDTO.setApplyAmount(applyAmount);
		goodwillApplyInfoDTO.setAuditAmount(auditAmount);
		goodwillApplyInfoDTO.setSettlementAmount(settlementAmount);
		goodwillApplyInfoDTO.setInvoiceAmount(invoiceAmount);
		goodwillApplyInfoDTO.setGoodwillStatus(goodwillStatus);
		// goodwillApplyInfoDTO.setCustomerPain(customerPain);
		goodwillApplyInfoDTO.setVin(vin);
		goodwillApplyInfoDTO.setLicense(license);
		goodwillApplyInfoDTO.setCustomerName(customerName);
		goodwillApplyInfoDTO.setCustomerMobile(customerMobile);
		goodwillApplyInfoDTO.setMileage(mileage);
		goodwillApplyInfoDTO.setModel(model);
		goodwillApplyInfoDTO.setIsExtendWarranty(isExtendWarranty);
		goodwillApplyInfoDTO.setExtendWarrantyName(extendWarrantyName);
		goodwillApplyInfoDTO.setMaintainCost(maintainCost);
		goodwillApplyInfoDTO.setExtendWarrantyCost(extendWarrantyCost);
		goodwillApplyInfoDTO.setAccessoryCost(accessoryCost);
		goodwillApplyInfoDTO.setVoucherCost(voucherCost);
		goodwillApplyInfoDTO.setWalkingCarPrice(walkingCarPrice);
		goodwillApplyInfoDTO.setVolvoIntegral(volvoIntegral);
		goodwillApplyInfoDTO.setReturnChangeCarPrice(returnChangeCarPrice);
		goodwillApplyInfoDTO.setOtherPrice(otherPrice);
		goodwillApplyInfoDTO.setCostTotal(costTotal);
		goodwillApplyInfoDTO.setCustomerPay(customerPay);
		goodwillApplyInfoDTO.setDealerUndertake(dealerUndertake);
		goodwillApplyInfoDTO.setVolvoSupportGoodwillAmount(volvoSupportGoodwillAmount);
		goodwillApplyInfoDTO.setRemark(remark);
		goodwillApplyInfoDTO.setCustomerIdentification(customerIdentification);
		goodwillApplyInfoDTO.setElseFile(elseFile);
		goodwillApplyInfoDTO.setIsValid(isValid);
		goodwillApplyInfoDTO.setIsDeleted(isDeleted);
		goodwillApplyInfoDTO.setApplyStartAmount(applyStartAmount);
		goodwillApplyInfoDTO.setApplyEndAmount(applyEndAmount);
		if (!StringUtils.isNullOrEmpty(applyStartTime)) {
			goodwillApplyInfoDTO.setApplyStartTime(sdf.parse(applyStartTime));
		}
		if (!StringUtils.isNullOrEmpty(applyEndTime)) {
			goodwillApplyInfoDTO.setApplyEndTime(sdf.parse(applyEndTime));
		}
		if (!StringUtils.isNullOrEmpty(passStartTime)) {
			goodwillApplyInfoDTO.setPassStartTime(sdf.parse(passStartTime));
		}
		if (!StringUtils.isNullOrEmpty(passEndTime)) {
			goodwillApplyInfoDTO.setPassEndTime(sdf.parse(passEndTime));
		}
		if (!StringUtils.isNullOrEmpty(noticeInvoiceStartDate)) {
			goodwillApplyInfoDTO.setNoticeInvoiceStartDate(sdf.parse(noticeInvoiceStartDate));
		}
		if (!StringUtils.isNullOrEmpty(noticeInvoiceEndDate)) {
			goodwillApplyInfoDTO.setNoticeInvoiceEndDate(sdf.parse(noticeInvoiceEndDate));
		}
		if (!StringUtils.isNullOrEmpty(invoiceStartDate)) {
			goodwillApplyInfoDTO.setInvoiceStartDate(sdf.parse(invoiceStartDate));
		}
		if (!StringUtils.isNullOrEmpty(invoiceEndDate)) {
			goodwillApplyInfoDTO.setInvoiceEndDate(sdf.parse(invoiceEndDate));
		}
		if (!StringUtils.isNullOrEmpty(updatedStartAt)) {
			goodwillApplyInfoDTO.setUpdatedStartAt(sdf.parse(updatedStartAt));
		}
		if (!StringUtils.isNullOrEmpty(updatedEndAt)) {
			goodwillApplyInfoDTO.setUpdatedEndAt(sdf.parse(updatedEndAt));
		}
		return goodwillApplyInfoService.exportSupportApplyOemSearchInfo(goodwillApplyInfoDTO);
	}

	/**
	 * 导出店端亲善申请查询数据
	 *
	 * @param appId
	 *            系统ID
	 * @param ownerCode
	 *            所有者代码
	 * @param ownerParCode
	 *            所有者的父组织代码
	 * @param orgId
	 *            组织ID
	 * @param id
	 *            预申请单表id
	 * @param applyNo
	 *            预申请单号
	 * @param dealerCode
	 *            申请经销商
	 * @param auditType
	 *            审核类型
	 * @param goodwillNature
	 *            亲善性质
	 * @param applyTime
	 *            申请时间
	 * @param complaintFalut
	 *            投诉故障
	 * @param complaintId
	 *            投诉单ID
	 * @param complaintDate
	 *            投诉日期
	 * @param vehicleUse
	 *            用车情况
	 * @param salesDealer
	 *            销售经销商
	 * @param applyAmount
	 *            申请金额
	 * @param auditAmount
	 *            审批金额
	 * @param settlementAmount
	 *            结算金额
	 * @param invoiceAmount
	 *            通知开票金额
	 * @param goodwillStatus
	 *            亲善单状态
	 * @param customerPain
	 *            客户痛点
	 * @param vin
	 *            VIN
	 * @param license
	 *            车牌号
	 * @param customerName
	 *            客户姓名
	 * @param customerMobile
	 *            客户电话
	 * @param mileage
	 *            里程
	 * @param model
	 *            车型
	 * @param buyCarDate
	 *            购车日期
	 * @param warrantyStartDate
	 *            保修开始日期
	 * @param isExtendWarranty
	 *            是否延保
	 * @param extendWarrantyName
	 *            延保名称
	 * @param extendWarrantyStartDate
	 *            延保开始日期
	 * @param extendWarrantyEndDate
	 *            延保结束日期
	 * @param isValid
	 *            是否有效
	 * @param isDeleted
	 *            是否删除:1,删除；0,未删除
	 * @param createdAt
	 *            创建时间
	 * @param updatedAt
	 *            修改时间
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillNature", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintFalut", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "vehicleUse", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "salesDealer", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "auditAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "settlementAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "invoiceAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillStatus", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerPain", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerMobile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "mileage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "model", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "buyCarDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "warrantyStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isExtendWarranty", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "maintainCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "extendWarrantyCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "accessoryCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "voucherCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "walkingCarPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "volvoIntegral", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "returnChangeCarPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "otherPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "costTotal", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "customerPay", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "dealerUndertake", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "volvoSupportGoodwillAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "remark", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerIdentification", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "elseFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "commitTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyStartAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyEndAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "passEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "noticeInvoiceEndDate", value = "")
	})
	@ApiOperation(value = "导出店端亲善申请查询数据", httpMethod = "GET")
	@GetMapping(value = "/exportSupportApplyDealerSearchInfo")
	public List<GoodwillApplyInfoDTO> exportSupportApplyDealerSearchInfo(
			@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "auditType", required = false) Integer auditType,
			@RequestParam(value = "goodwillNature", required = false) Integer goodwillNature,
			@RequestParam(value = "applyTime", required = false) String applyTime,
			@RequestParam(value = "complaintFalut", required = false) String complaintFalut,
			@RequestParam(value = "complaintId", required = false) String complaintId,
			@RequestParam(value = "complaintDate", required = false) String complaintDate,
			@RequestParam(value = "vehicleUse", required = false) Integer vehicleUse,
			@RequestParam(value = "salesDealer", required = false) String salesDealer,
			@RequestParam(value = "applyAmount", required = false) BigDecimal applyAmount,
			@RequestParam(value = "auditAmount", required = false) BigDecimal auditAmount,
			@RequestParam(value = "settlementAmount", required = false) BigDecimal settlementAmount,
			@RequestParam(value = "invoiceAmount", required = false) BigDecimal invoiceAmount,
			@RequestParam(value = "goodwillStatus", required = false) Integer goodwillStatus,
			@RequestParam(value = "customerPain", required = false) String customerPain,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "customerName", required = false) String customerName,
			@RequestParam(value = "customerMobile", required = false) String customerMobile,
			@RequestParam(value = "mileage", required = false) String mileage,
			@RequestParam(value = "model", required = false) String model,
			@RequestParam(value = "buyCarDate", required = false) String buyCarDate,
			@RequestParam(value = "warrantyStartDate", required = false) String warrantyStartDate,
			@RequestParam(value = "isExtendWarranty", required = false) Integer isExtendWarranty,
			@RequestParam(value = "extendWarrantyName", required = false) String extendWarrantyName,
			@RequestParam(value = "extendWarrantyStartDate", required = false) String extendWarrantyStartDate,
			@RequestParam(value = "extendWarrantyEndDate", required = false) String extendWarrantyEndDate,
			@RequestParam(value = "maintainCost", required = false) BigDecimal maintainCost,
			@RequestParam(value = "extendWarrantyCost", required = false) BigDecimal extendWarrantyCost,
			@RequestParam(value = "accessoryCost", required = false) BigDecimal accessoryCost,
			@RequestParam(value = "voucherCost", required = false) BigDecimal voucherCost,
			@RequestParam(value = "walkingCarPrice", required = false) BigDecimal walkingCarPrice,
			@RequestParam(value = "volvoIntegral", required = false) BigDecimal volvoIntegral,
			@RequestParam(value = "returnChangeCarPrice", required = false) BigDecimal returnChangeCarPrice,
			@RequestParam(value = "otherPrice", required = false) BigDecimal otherPrice,
			@RequestParam(value = "costTotal", required = false) BigDecimal costTotal,
			@RequestParam(value = "customerPay", required = false) BigDecimal customerPay,
			@RequestParam(value = "dealerUndertake", required = false) BigDecimal dealerUndertake,
			@RequestParam(value = "volvoSupportGoodwillAmount", required = false) BigDecimal volvoSupportGoodwillAmount,
			@RequestParam(value = "remark", required = false) String remark,
			@RequestParam(value = "customerIdentification", required = false) String customerIdentification,
			@RequestParam(value = "elseFile", required = false) String elseFile,
			@RequestParam(value = "commitTime", required = false) String commitTime,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt,
			@RequestParam(value = "applyStartTime", required = false) String applyStartTime,
			@RequestParam(value = "applyEndTime", required = false) String applyEndTime,
			@RequestParam(value = "applyStartAmount", required = false) BigDecimal applyStartAmount,
			@RequestParam(value = "applyEndAmount", required = false) BigDecimal applyEndAmount,
			@RequestParam(value = "passStartTime", required = false) String passStartTime,
			@RequestParam(value = "passEndTime", required = false) String passEndTime,
			@RequestParam(value = "noticeInvoiceStartDate", required = false) String noticeInvoiceStartDate,
			@RequestParam(value = "noticeInvoiceEndDate", required = false) String noticeInvoiceEndDate)
			throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		LoginInfoDto loginInfoDto = FrameworkUtil.getLoginInfo();
		GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		goodwillApplyInfoDTO.setAppId(appId);
		goodwillApplyInfoDTO.setOwnerCode(ownerCode);
		goodwillApplyInfoDTO.setOwnerParCode(ownerParCode);
		goodwillApplyInfoDTO.setOrgId(orgId);
		goodwillApplyInfoDTO.setId(id);
		goodwillApplyInfoDTO.setApplyNo(applyNo);
		goodwillApplyInfoDTO.setDealerCode(loginInfoDto.getOwnerCode());
		goodwillApplyInfoDTO.setAuditType(auditType);
		goodwillApplyInfoDTO.setGoodwillNature(goodwillNature);
		goodwillApplyInfoDTO.setComplaintFalut(complaintFalut);
		goodwillApplyInfoDTO.setComplaintId(complaintId);
		goodwillApplyInfoDTO.setVehicleUse(vehicleUse);
		goodwillApplyInfoDTO.setSalesDealer(salesDealer);
		goodwillApplyInfoDTO.setApplyAmount(applyAmount);
		goodwillApplyInfoDTO.setAuditAmount(auditAmount);
		goodwillApplyInfoDTO.setSettlementAmount(settlementAmount);
		goodwillApplyInfoDTO.setInvoiceAmount(invoiceAmount);
		goodwillApplyInfoDTO.setGoodwillStatus(goodwillStatus);
		// goodwillApplyInfoDTO.setCustomerPain(customerPain);
		goodwillApplyInfoDTO.setVin(vin);
		goodwillApplyInfoDTO.setLicense(license);
		goodwillApplyInfoDTO.setCustomerName(customerName);
		goodwillApplyInfoDTO.setCustomerMobile(customerMobile);
		goodwillApplyInfoDTO.setMileage(mileage);
		goodwillApplyInfoDTO.setModel(model);
		goodwillApplyInfoDTO.setIsExtendWarranty(isExtendWarranty);
		goodwillApplyInfoDTO.setExtendWarrantyName(extendWarrantyName);
		goodwillApplyInfoDTO.setMaintainCost(maintainCost);
		goodwillApplyInfoDTO.setExtendWarrantyCost(extendWarrantyCost);
		goodwillApplyInfoDTO.setAccessoryCost(accessoryCost);
		goodwillApplyInfoDTO.setVoucherCost(voucherCost);
		goodwillApplyInfoDTO.setWalkingCarPrice(walkingCarPrice);
		goodwillApplyInfoDTO.setVolvoIntegral(volvoIntegral);
		goodwillApplyInfoDTO.setReturnChangeCarPrice(returnChangeCarPrice);
		goodwillApplyInfoDTO.setOtherPrice(otherPrice);
		goodwillApplyInfoDTO.setCostTotal(costTotal);
		goodwillApplyInfoDTO.setCustomerPay(customerPay);
		goodwillApplyInfoDTO.setDealerUndertake(dealerUndertake);
		goodwillApplyInfoDTO.setVolvoSupportGoodwillAmount(volvoSupportGoodwillAmount);
		goodwillApplyInfoDTO.setRemark(remark);
		goodwillApplyInfoDTO.setCustomerIdentification(customerIdentification);
		goodwillApplyInfoDTO.setElseFile(elseFile);
		goodwillApplyInfoDTO.setIsValid(isValid);
		goodwillApplyInfoDTO.setIsDeleted(isDeleted);
		goodwillApplyInfoDTO.setApplyStartAmount(applyStartAmount);
		goodwillApplyInfoDTO.setApplyEndAmount(applyEndAmount);
		if (!StringUtils.isNullOrEmpty(applyStartTime)) {
			goodwillApplyInfoDTO.setApplyStartTime(sdf.parse(applyStartTime));
		}
		if (!StringUtils.isNullOrEmpty(applyEndTime)) {
			goodwillApplyInfoDTO.setApplyEndTime(sdf.parse(applyEndTime));
		}
		if (!StringUtils.isNullOrEmpty(passStartTime)) {
			goodwillApplyInfoDTO.setPassStartTime(sdf.parse(passStartTime));
		}
		if (!StringUtils.isNullOrEmpty(passEndTime)) {
			goodwillApplyInfoDTO.setPassEndTime(sdf.parse(passEndTime));
		}
		if (!StringUtils.isNullOrEmpty(noticeInvoiceStartDate)) {
			goodwillApplyInfoDTO.setNoticeInvoiceStartDate(sdf.parse(noticeInvoiceStartDate));
		}
		if (!StringUtils.isNullOrEmpty(noticeInvoiceEndDate)) {
			goodwillApplyInfoDTO.setNoticeInvoiceEndDate(sdf.parse(noticeInvoiceEndDate));
		}
		return goodwillApplyInfoService.exportSupportApplyDealerSearchInfo(goodwillApplyInfoDTO);
	}

	/***
	 * 导出亲善审计数据**
	 *
	 * @param appId
	 *            系统ID
	 * @param ownerCode
	 *            所有者代码
	 * @param ownerParCode
	 *            所有者的父组织代码
	 * @param orgId
	 *            组织ID
	 * @param id
	 *            预申请单表id
	 * @param applyNo
	 *            预申请单号
	 * @param dealerCode
	 *            申请经销商
	 * @param auditType
	 *            审核类型
	 * @param goodwillNature
	 *            亲善性质
	 * @param applyTime
	 *            申请时间
	 * @param complaintFalut
	 *            投诉故障
	 * @param complaintId
	 *            投诉单ID
	 * @param complaintDate
	 *            投诉日期
	 * @param vehicleUse
	 *            用车情况
	 * @param salesDealer
	 *            销售经销商
	 * @param applyAmount
	 *            申请金额
	 * @param auditAmount
	 *            审批金额
	 * @param settlementAmount
	 *            结算金额
	 * @param invoiceAmount
	 *            通知开票金额
	 * @param goodwillStatus
	 *            亲善单状态
	 * @param customerPain
	 *            客户痛点
	 * @param vin
	 *            VIN
	 * @param license
	 *            车牌号
	 * @param customerName
	 *            客户姓名
	 * @param customerMobile
	 *            客户电话
	 * @param mileage
	 *            里程
	 * @param model
	 *            车型
	 * @param buyCarDate
	 *            购车日期
	 * @param warrantyStartDate
	 *            保修开始日期
	 * @param isExtendWarranty
	 *            是否延保
	 * @param extendWarrantyName
	 *            延保名称
	 * @param extendWarrantyStartDate
	 *            延保开始日期
	 * @param extendWarrantyEndDate
	 *            延保结束日期
	 * @param maintainCost
	 *            保养成本
	 * @param extendWarrantyCost
	 *            延保成本
	 * @param accessoryCost
	 *            附件精品成本
	 * @param voucherCost
	 *            代金券成本
	 * @param walkingCarPrice
	 *            代步车/相关利益
	 * @param volvoIntegral
	 *            沃世界积分
	 * @param returnChangeCarPrice
	 *            退换车
	 * @param otherPrice
	 *            其他
	 * @param costTotal
	 *            成本总计
	 * @param customerPay
	 *            客户支付
	 * @param dealerUndertake
	 *            经销商承担
	 * @param volvoSupportGoodwillAmount
	 *            volvo支持亲善金额
	 * @param remark
	 *            备注
	 * @param applyFile
	 *            预申请附件
	 * @param costStatisticsFile
	 *            亲善成本统计
	 * @param costScreenshotFile
	 *            亲善成本截图
	 * @param commitTime
	 *            提交时间
	 * @param isValid
	 *            是否有效
	 * @param isDeleted
	 *            是否删除:1,删除；0,未删除
	 * @param createdAt
	 *            创建时间
	 * @param updatedAt
	 *            修改时间
	 * @param currentPage
	 *            页数
	 * @param pageSize
	 *            分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象*
	 *
	 * <AUTHOR>
	 * @throws ParseException
	 * @since 2020-05-06
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "bloc", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "areaManage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyNo", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillNature", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "applyTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintFalut", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "complaintDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "vehicleUse", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "salesDealer", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "applyAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "auditAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "settlementAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "invoiceAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "goodwillStatus", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerPain", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerMobile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "mileage", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "model", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "buyCarDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "warrantyStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isExtendWarranty", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyName", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyStartDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "extendWarrantyEndDate", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "maintainCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "extendWarrantyCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "accessoryCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "voucherCost", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "walkingCarPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "volvoIntegral", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "returnChangeCarPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "otherPrice", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "costTotal", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "customerPay", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "dealerUndertake", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "volvoSupportGoodwillAmount", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "remark", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "customerIdentification", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "elseFile", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "commitTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isAudit", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditWay", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "auditResult", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditStartTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "auditEndTime", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = "")
	})
	@ApiOperation(value = "导出亲善审计数据", httpMethod = "GET")
	@GetMapping(value = "/exportSupportApplyAuditInfo")
	public List<GoodwillApplyInfoDTO> exportSupportApplyAuditInfo(
			@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "bloc", required = false) Integer bloc,
			@RequestParam(value = "areaManage", required = false) Integer areaManage,
			@RequestParam(value = "applyNo", required = false) String applyNo,
			@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "auditType", required = false) Integer auditType,
			@RequestParam(value = "goodwillNature", required = false) Integer goodwillNature,
			@RequestParam(value = "applyTime", required = false) String applyTime,
			@RequestParam(value = "complaintFalut", required = false) String complaintFalut,
			@RequestParam(value = "complaintId", required = false) String complaintId,
			@RequestParam(value = "complaintDate", required = false) String complaintDate,
			@RequestParam(value = "vehicleUse", required = false) Integer vehicleUse,
			@RequestParam(value = "salesDealer", required = false) String salesDealer,
			@RequestParam(value = "applyAmount", required = false) BigDecimal applyAmount,
			@RequestParam(value = "auditAmount", required = false) BigDecimal auditAmount,
			@RequestParam(value = "settlementAmount", required = false) BigDecimal settlementAmount,
			@RequestParam(value = "invoiceAmount", required = false) BigDecimal invoiceAmount,
			@RequestParam(value = "goodwillStatus", required = false) Integer goodwillStatus,
			@RequestParam(value = "customerPain", required = false) String customerPain,
			@RequestParam(value = "vin", required = false) String vin,
			@RequestParam(value = "license", required = false) String license,
			@RequestParam(value = "customerName", required = false) String customerName,
			@RequestParam(value = "customerMobile", required = false) String customerMobile,
			@RequestParam(value = "mileage", required = false) String mileage,
			@RequestParam(value = "model", required = false) String model,
			@RequestParam(value = "buyCarDate", required = false) String buyCarDate,
			@RequestParam(value = "warrantyStartDate", required = false) String warrantyStartDate,
			@RequestParam(value = "isExtendWarranty", required = false) Integer isExtendWarranty,
			@RequestParam(value = "extendWarrantyName", required = false) String extendWarrantyName,
			@RequestParam(value = "extendWarrantyStartDate", required = false) String extendWarrantyStartDate,
			@RequestParam(value = "extendWarrantyEndDate", required = false) String extendWarrantyEndDate,
			@RequestParam(value = "maintainCost", required = false) BigDecimal maintainCost,
			@RequestParam(value = "extendWarrantyCost", required = false) BigDecimal extendWarrantyCost,
			@RequestParam(value = "accessoryCost", required = false) BigDecimal accessoryCost,
			@RequestParam(value = "voucherCost", required = false) BigDecimal voucherCost,
			@RequestParam(value = "walkingCarPrice", required = false) BigDecimal walkingCarPrice,
			@RequestParam(value = "volvoIntegral", required = false) BigDecimal volvoIntegral,
			@RequestParam(value = "returnChangeCarPrice", required = false) BigDecimal returnChangeCarPrice,
			@RequestParam(value = "otherPrice", required = false) BigDecimal otherPrice,
			@RequestParam(value = "costTotal", required = false) BigDecimal costTotal,
			@RequestParam(value = "customerPay", required = false) BigDecimal customerPay,
			@RequestParam(value = "dealerUndertake", required = false) BigDecimal dealerUndertake,
			@RequestParam(value = "volvoSupportGoodwillAmount", required = false) BigDecimal volvoSupportGoodwillAmount,
			@RequestParam(value = "remark", required = false) String remark,
			@RequestParam(value = "customerIdentification", required = false) String customerIdentification,
			@RequestParam(value = "elseFile", required = false) String elseFile,
			@RequestParam(value = "commitTime", required = false) String commitTime,
			@RequestParam(value = "isAudit", required = false) Integer isAudit,
			@RequestParam(value = "auditWay", required = false) Integer auditWay,
			@RequestParam(value = "auditResult", required = false) Integer auditResult,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "auditStartTime", required = false) String auditStartTime,
			@RequestParam(value = "auditEndTime", required = false) String auditEndTime,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt) throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
		goodwillApplyInfoDTO.setAppId(appId);
		goodwillApplyInfoDTO.setOwnerCode(ownerCode);
		goodwillApplyInfoDTO.setOwnerParCode(ownerParCode);
		goodwillApplyInfoDTO.setOrgId(orgId);
		goodwillApplyInfoDTO.setId(id);
		goodwillApplyInfoDTO.setBlocId(bloc);
		goodwillApplyInfoDTO.setAreaManageId(areaManage);
		goodwillApplyInfoDTO.setApplyNo(applyNo);
		goodwillApplyInfoDTO.setDealerCode(dealerCode);
		goodwillApplyInfoDTO.setAuditType(auditType);
		goodwillApplyInfoDTO.setGoodwillNature(goodwillNature);
		goodwillApplyInfoDTO.setComplaintFalut(complaintFalut);
		goodwillApplyInfoDTO.setComplaintId(complaintId);
		goodwillApplyInfoDTO.setVehicleUse(vehicleUse);
		goodwillApplyInfoDTO.setSalesDealer(salesDealer);
		goodwillApplyInfoDTO.setApplyAmount(applyAmount);
		goodwillApplyInfoDTO.setAuditAmount(auditAmount);
		goodwillApplyInfoDTO.setSettlementAmount(settlementAmount);
		goodwillApplyInfoDTO.setInvoiceAmount(invoiceAmount);
		goodwillApplyInfoDTO.setGoodwillStatus(goodwillStatus);
		// goodwillApplyInfoDTO.setCustomerPain(customerPain);
		goodwillApplyInfoDTO.setVin(vin);
		goodwillApplyInfoDTO.setLicense(license);
		goodwillApplyInfoDTO.setCustomerName(customerName);
		goodwillApplyInfoDTO.setCustomerMobile(customerMobile);
		goodwillApplyInfoDTO.setMileage(mileage);
		goodwillApplyInfoDTO.setModel(model);
		goodwillApplyInfoDTO.setIsExtendWarranty(isExtendWarranty);
		goodwillApplyInfoDTO.setExtendWarrantyName(extendWarrantyName);
		goodwillApplyInfoDTO.setMaintainCost(maintainCost);
		goodwillApplyInfoDTO.setExtendWarrantyCost(extendWarrantyCost);
		goodwillApplyInfoDTO.setAccessoryCost(accessoryCost);
		goodwillApplyInfoDTO.setVoucherCost(voucherCost);
		goodwillApplyInfoDTO.setWalkingCarPrice(walkingCarPrice);
		goodwillApplyInfoDTO.setVolvoIntegral(volvoIntegral);
		goodwillApplyInfoDTO.setReturnChangeCarPrice(returnChangeCarPrice);
		goodwillApplyInfoDTO.setOtherPrice(otherPrice);
		goodwillApplyInfoDTO.setCostTotal(costTotal);
		goodwillApplyInfoDTO.setCustomerPay(customerPay);
		goodwillApplyInfoDTO.setDealerUndertake(dealerUndertake);
		goodwillApplyInfoDTO.setVolvoSupportGoodwillAmount(volvoSupportGoodwillAmount);
		goodwillApplyInfoDTO.setRemark(remark);
		goodwillApplyInfoDTO.setCustomerIdentification(customerIdentification);
		goodwillApplyInfoDTO.setElseFile(elseFile);
		goodwillApplyInfoDTO.setIsValid(isValid);
		goodwillApplyInfoDTO.setIsAudit(isAudit);
		goodwillApplyInfoDTO.setAuditWay(auditWay);
		goodwillApplyInfoDTO.setAuditResult(auditResult);
		goodwillApplyInfoDTO.setIsDeleted(isDeleted);
		if (applyTime != null) {
			goodwillApplyInfoDTO.setApplyTime(sdf.parse(applyTime));
		}
		if (auditStartTime != null) {
			goodwillApplyInfoDTO.setAuditStartTime(sdf.parse(auditStartTime));
		}
		if (auditEndTime != null) {
			goodwillApplyInfoDTO.setAuditEndTime(sdf.parse(auditEndTime));
		}
		if (warrantyStartDate != null) {
			goodwillApplyInfoDTO.setWarrantyStartDate(sdf.parse(warrantyStartDate));
		}
		if (extendWarrantyStartDate != null) {
			goodwillApplyInfoDTO.setExtendWarrantyStartDate(sdf.parse(extendWarrantyStartDate));
		}
		if (extendWarrantyEndDate != null) {
			goodwillApplyInfoDTO.setExtendWarrantyEndDate(sdf.parse(extendWarrantyEndDate));
		}
		if (commitTime != null) {
			goodwillApplyInfoDTO.setCommitTime(sdf.parse(commitTime));
		}
		if (createdAt != null) {
			goodwillApplyInfoDTO.setCreatedAt(sdf.parse(createdAt));
		}
		if (updatedAt != null) {
			goodwillApplyInfoDTO.setUpdatedAt(sdf.parse(updatedAt));
		}
		return goodwillApplyInfoService.exportSupportApplyAuditInfo(goodwillApplyInfoDTO);
	}

	/**
	 * 首页-商务亲善信息
	 * 
	 * @since 2020/11/23
	 * <AUTHOR>
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "dataType", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "roleOrgId", value = ""),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "roleList", value = "")
	})
	@ApiOperation(value = "首页-商务亲善信息", notes = "首页-商务亲善信息", httpMethod = "GET")
	@GetMapping(value = "queryGoodwillFirstPage")
	public Map getGoodwillFirstPage(@RequestParam(value = "dealerCode", required = false) String dealerCode,
			@RequestParam(value = "dataType", required = false) Integer dataType,
			@RequestParam(value = "roleOrgId", required = false) Integer roleOrgId,
			@RequestParam(value = "roleList", required = false) String roleList) {
		GoodwillFirstPageDTO goodwillFirstPageDTO = new GoodwillFirstPageDTO();
		if (dataType == 10461001) {
			goodwillFirstPageDTO.setDealerCode(dealerCode);
		} else {
			String[] roleLists = roleList.split(",");
			for (int i = 0; i < roleLists.length; i++) {
				if ("SHQYJL".equals(roleLists[i])) {
					goodwillFirstPageDTO.setSmallAreaManage(roleOrgId);
				} else if ("SHDQJL".equals(roleLists[i]) || "SHQYZJ".equals(roleLists[i])) {
					goodwillFirstPageDTO.setBigAreaManage(roleOrgId);
				}
			}
			goodwillFirstPageDTO.setRoleList(roleLists);
		}

		return goodwillApplyInfoService.getGoodwillFirstPage(goodwillFirstPageDTO);

	}

    /**
     * 主要用于迁移数据产生亲善审批流程
     * 根据申请单编号列表产生亲善审核流程
     * add by czm 20210721
     * @param applyNoList 亲善预申请单号列表
     * @return 是否执行完毕
     */
    @ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "List<String>", name = "applyNoList", value = "亲善预申请单号列表", required = true)
	})
	@ApiOperation(value = "主要用于迁移数据产生亲善审批流程 根据申请单编号列表产生亲善审核流程 add by czm 20210721", notes = "主要用于迁移数据产生亲善审批流程 根据申请单编号列表产生亲善审核流程 add by czm 20210721", httpMethod = "POST")
	@PostMapping(path = "/interf/dealgoodwillflowdata", produces = MediaType.APPLICATION_JSON_VALUE)
    public AjaxResponse dealGoodwillFlowData(@RequestBody List<String> applyNoList){
       return goodwillApplyInfoService.dealGoodwillFlowData(applyNoList);
    }

}