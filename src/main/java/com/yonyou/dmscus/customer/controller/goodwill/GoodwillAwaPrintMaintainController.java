package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAwaPrintMaintainDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAwaPrintMaintainPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillAwaPrintMaintainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @since 2020-04-23
 */
@Api(value = "/goodwillAwaPrintMaintain", tags = {"GoodwillAwaPrintMaintainController"})
@RestController
@RequestMapping("/goodwillAwaPrintMaintain")
public class GoodwillAwaPrintMaintainController {

	@Autowired
	GoodwillAwaPrintMaintainService goodwillAwaPrintMaintainService;

	/**
	 * 分页查询数据
	 *
	 * @param appId        系统ID
	 * @param ownerCode    所有者代码
	 * @param ownerParCode 所有者的父组织代码
	 * @param orgId        组织ID
	 * @param id           主键id
	 * @param approval     approval
	 * @param requestor    requestor
	 * @param lineDirector line_director
	 * @param csVp         cs_vp
	 * @param dateOne      date_one
	 * @param dateTwo      date_two
	 * @param dateThree    date_three
	 * @param cfo          cfo
	 * @param coo          coo
	 * @param md           md
	 * @param dateFour     date_four
	 * @param dateFive     date_five
	 * @param dateSix      date_six
	 * @param explainOne   explain_one
	 * @param explainTwo   explain_two
	 * @param isValid      是否有效
	 * @param isDeleted    是否删除:1,删除；0,未删除
	 * @param createdAt    创建时间
	 * @param updatedAt    修改时间
	 * @param currentPage  页数
	 * @param pageSize     分页大小
	 * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
	 * @throws ParseException
	 * <AUTHOR>
	 * @since 2020-04-23
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
			@ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键id"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "approval", value = "approval"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "requestor", value = "requestor"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "lineDirector", value = "line_director"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "csVp", value = "cs_vp"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dateOne", value = "date_one"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dateTwo", value = "date_two"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dateThree", value = "date_three"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "cfo", value = "cfo"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "coo", value = "coo"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "md", value = "md"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dateFour", value = "date_four"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dateFive", value = "date_five"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "dateSix", value = "date_six"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "explainOne", value = "explain_one"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "explainTwo", value = "explain_two"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
			@ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除:1,删除；0,未删除"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = "创建时间"),
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = "修改时间"),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
	})
	@ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
	@GetMapping
	public IPage<GoodwillAwaPrintMaintainDTO> getByPage(@RequestParam(value = "appId", required = false) String appId,
			@RequestParam(value = "ownerCode", required = false) String ownerCode,
			@RequestParam(value = "ownerParCode", required = false) String ownerParCode,
			@RequestParam(value = "orgId", required = false) Integer orgId,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "approval", required = false) String approval,
			@RequestParam(value = "requestor", required = false) String requestor,
			@RequestParam(value = "lineDirector", required = false) String lineDirector,
			@RequestParam(value = "csVp", required = false) String csVp,
			@RequestParam(value = "dateOne", required = false) String dateOne,
			@RequestParam(value = "dateTwo", required = false) String dateTwo,
			@RequestParam(value = "dateThree", required = false) String dateThree,
			@RequestParam(value = "cfo", required = false) String cfo,
			@RequestParam(value = "coo", required = false) String coo,
			@RequestParam(value = "md", required = false) String md,
			@RequestParam(value = "dateFour", required = false) String dateFour,
			@RequestParam(value = "dateFive", required = false) String dateFive,
			@RequestParam(value = "dateSix", required = false) String dateSix,
			@RequestParam(value = "explainOne", required = false) String explainOne,
			@RequestParam(value = "explainTwo", required = false) String explainTwo,
			@RequestParam(value = "isValid", required = false) Integer isValid,
			@RequestParam(value = "isDeleted", required = false) Boolean isDeleted,
			@RequestParam(value = "createdAt", required = false) String createdAt,
			@RequestParam(value = "updatedAt", required = false) String updatedAt,
			@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize)
			throws ParseException {
		Page<GoodwillAwaPrintMaintainPO> page = new Page(currentPage, pageSize);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO = new GoodwillAwaPrintMaintainDTO();
		goodwillAwaPrintMaintainDTO.setAppId(appId);
		goodwillAwaPrintMaintainDTO.setOwnerCode(ownerCode);
		goodwillAwaPrintMaintainDTO.setOwnerParCode(ownerParCode);
		goodwillAwaPrintMaintainDTO.setOrgId(orgId);
		goodwillAwaPrintMaintainDTO.setId(id);
		goodwillAwaPrintMaintainDTO.setApproval(approval);
		goodwillAwaPrintMaintainDTO.setRequestor(requestor);
		goodwillAwaPrintMaintainDTO.setLineDirector(lineDirector);
		goodwillAwaPrintMaintainDTO.setCsVp(csVp);
		goodwillAwaPrintMaintainDTO.setDateOne(dateOne);
		goodwillAwaPrintMaintainDTO.setDateTwo(dateTwo);
		goodwillAwaPrintMaintainDTO.setDateThree(dateThree);
		goodwillAwaPrintMaintainDTO.setCfo(cfo);
		goodwillAwaPrintMaintainDTO.setCoo(coo);
		goodwillAwaPrintMaintainDTO.setMd(md);
		goodwillAwaPrintMaintainDTO.setDateFour(dateFour);
		goodwillAwaPrintMaintainDTO.setDateFive(dateFive);
		goodwillAwaPrintMaintainDTO.setDateSix(dateSix);
		goodwillAwaPrintMaintainDTO.setExplainOne(explainOne);
		goodwillAwaPrintMaintainDTO.setExplainTwo(explainTwo);
		goodwillAwaPrintMaintainDTO.setIsValid(isValid);
		goodwillAwaPrintMaintainDTO.setIsDeleted(isDeleted);
		if (createdAt != null) {
			goodwillAwaPrintMaintainDTO.setCreatedAt(sdf.parse(createdAt));
		}
		if (updatedAt != null) {
			goodwillAwaPrintMaintainDTO.setUpdatedAt(sdf.parse(updatedAt));
		}
		return goodwillAwaPrintMaintainService.selectPageBysql(page, goodwillAwaPrintMaintainDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id 数据主键ID
	 * @return com.yonyou.dmscus.customer.entity.dto.GoodwillAwaPrintMaintainDTO
	 * <AUTHOR>
	 * @since 2020-04-23
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
	@GetMapping(value = "/{id}")
	public GoodwillAwaPrintMaintainDTO getById(@PathVariable("id") Long id) {
		return goodwillAwaPrintMaintainService.getById(id);
	}

	/**
	 * 进行数据新增
	 *
	 * @param goodwillAwaPrintMaintainDTO 需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-04-23
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillAwaPrintMaintainDTO", name = "goodwillAwaPrintMaintainDTO", value = "需要保存的DTO", required = true)
	})
	@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
	@PostMapping
	@ResponseStatus(HttpStatus.CREATED)
	public int insert(@RequestBody GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO) {
		return goodwillAwaPrintMaintainService.insert(goodwillAwaPrintMaintainDTO);
	}

	/**
	 * 进行数据修改
	 *
	 * @param id                          需要修改数据的ID
	 * @param goodwillAwaPrintMaintainDTO 需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-04-23
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillAwaPrintMaintainDTO", name = "goodwillAwaPrintMaintainDTO", value = "需要保存的DTO", required = true)
	})
	@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
	@PutMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.CREATED)
	public int update(@PathVariable("id") Long id,
			@RequestBody GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO) {
		return goodwillAwaPrintMaintainService.update(id, goodwillAwaPrintMaintainDTO);
	}

	/**
	 * 根据id删除对象
	 *
	 * @param id 需要修改数据的ID
	 * <AUTHOR>
	 * @since 2020-04-23
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
	})
	@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteById(@PathVariable("id") Long id) {
		goodwillAwaPrintMaintainService.deleteById(id);
		return true;
	}

	/**
	 * 根据IDs批量删除对象
	 *
	 * @param ids 需要修改数据的ID集合
	 * <AUTHOR>
	 * @since 2020-04-23
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "需要修改数据的ID集合", required = true)
	})
	@ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
	@DeleteMapping(value = "/batch/{ids}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public boolean deleteByIds(@PathVariable("ids") String ids) {
		goodwillAwaPrintMaintainService.deleteBatchIds(ids);
		return true;
	}

	/**
	 * 全量查询
	 * 
	 * <AUTHOR>
	 * @since 2020-04-20
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "")
	})
	@ApiOperation(value = "全量查询", notes = "全量查询", httpMethod = "GET")
	@GetMapping(value = "/list")
	public Map getList(@RequestParam(value = "isValid", required = false) Integer isValid) {
		GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO = new GoodwillAwaPrintMaintainDTO();
		goodwillAwaPrintMaintainDTO.setIsValid(isValid);
		List<Map> list = goodwillAwaPrintMaintainService.selectAwaPrintMaintainInfo(goodwillAwaPrintMaintainDTO);
		if (list != null && list.size() > 0) {
			return list.get(0);
		} else {
			return new HashMap(16);
		}

	}

	/**
	 * 进行数据新增
	 *
	 * @param GoodwillAwaPrintMaintainDTO 需要保存的DTO
	 * @return int
	 * <AUTHOR>
	 * @since 2020-04-20
	 */
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "GoodwillAwaPrintMaintainDTO", name = "goodwillAwaPrintMaintainDTO", value = "", required = true)
	})
	@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
	@PostMapping(value = "/addOrEditAwaPrint")
	@ResponseStatus(HttpStatus.CREATED)
	public int addOrEditAwaPrint(@RequestBody GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO) {
		return goodwillAwaPrintMaintainService.addOrEditAwaPrint(goodwillAwaPrintMaintainDTO);
	}

}