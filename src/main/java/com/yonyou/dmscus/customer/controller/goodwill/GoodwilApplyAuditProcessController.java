package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwilApplyAuditProcessDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwilApplyAuditProcessPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwilApplyAuditProcessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;






                                                                    /**
 *
 * <AUTHOR>
 * @since 2020-05-22
 */
@Api(value = "/goodwilApplyAuditProcess", tags = {"GoodwilApplyAuditProcessController"})
@RestController
@RequestMapping("/goodwilApplyAuditProcess")
                public class GoodwilApplyAuditProcessController {
    
        @Autowired
        GoodwilApplyAuditProcessService goodwilApplyAuditProcessService;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码
        * @param ownerParCode 所有者的父组织代码
        * @param orgId 组织ID
        * @param id 主键id
        * @param goodwillApplyId 亲善单id
        * @param auditObject 审核对象
        * @param auditType 审批类型
        * @param auditPosition 审批职位
        * @param auditSort 审批顺序
        * @param auditStatus 审批状态
        * @param auditDate 审批日期
        * @param isValid 是否有效
        * @param isDeleted 是否删除:1,删除；0,未删除
        * @param createdAt 创建时间
        * @param updatedAt 修改时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @throws ParseException 
         * @since 2020-05-22
        */
                @ApiImplicitParams({
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键id"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = "亲善单id"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "auditObject", value = "审核对象"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "auditType", value = "审批类型"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "auditPosition", value = "审批职位"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "auditSort", value = "审批顺序"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "auditStatus", value = "审批状态"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "auditDate", value = "审批日期"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
                        @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除:1,删除；0,未删除"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = "创建时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = "修改时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
                })
                @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
                @GetMapping
        public IPage<GoodwilApplyAuditProcessDTO>getByPage(
		@RequestParam(value="appId",required = false) String appId,
		@RequestParam(value="ownerCode",required = false) String ownerCode,
		@RequestParam(value="ownerParCode",required = false) String ownerParCode,
		@RequestParam(value="orgId",required = false) Integer orgId,
		@RequestParam(value="id",required = false) Long id,
		@RequestParam(value="goodwillApplyId",required = false) Long goodwillApplyId,
		@RequestParam(value="auditObject",required = false) Integer auditObject,
		@RequestParam(value="auditType",required = false) Integer auditType,
		@RequestParam(value="auditPosition",required = false) String auditPosition,
		@RequestParam(value="auditSort",required = false) Integer auditSort,
		@RequestParam(value="auditStatus",required = false) Integer auditStatus,
		@RequestParam(value="auditDate",required = false) String auditDate,
		@RequestParam(value="isValid",required = false) Integer isValid,
		@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
		@RequestParam(value="createdAt",required = false) String createdAt,
		@RequestParam(value="updatedAt",required = false) String updatedAt,
        @RequestParam("currentPage")int currentPage,
        @RequestParam("pageSize")int pageSize) throws ParseException{
        Page<GoodwilApplyAuditProcessPO>page=new Page(currentPage,pageSize);
        	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        	GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO =new GoodwilApplyAuditProcessDTO();
            goodwilApplyAuditProcessDTO.setAppId(appId);
            goodwilApplyAuditProcessDTO.setOwnerCode(ownerCode);
            goodwilApplyAuditProcessDTO.setOwnerParCode(ownerParCode);
            goodwilApplyAuditProcessDTO.setOrgId(orgId);
            goodwilApplyAuditProcessDTO.setId(id);
            goodwilApplyAuditProcessDTO.setGoodwillApplyId(goodwillApplyId);
            goodwilApplyAuditProcessDTO.setAuditObject(auditObject);
            goodwilApplyAuditProcessDTO.setAuditType(auditType);
            goodwilApplyAuditProcessDTO.setAuditPosition(auditPosition);
            goodwilApplyAuditProcessDTO.setAuditSort(auditSort);
            goodwilApplyAuditProcessDTO.setAuditStatus(auditStatus);
            goodwilApplyAuditProcessDTO.setIsValid(isValid);
            goodwilApplyAuditProcessDTO.setIsDeleted(isDeleted);
            if(auditDate != null){
            	goodwilApplyAuditProcessDTO.setAuditDate(sdf.parse(auditDate));
            }
            if(createdAt != null){
            	goodwilApplyAuditProcessDTO.setCreatedAt(sdf.parse(createdAt));
            }
            if(updatedAt != null){
            	goodwilApplyAuditProcessDTO.setUpdatedAt(sdf.parse(updatedAt));
            }
                return goodwilApplyAuditProcessService.selectPageBysql(page,goodwilApplyAuditProcessDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwilApplyAuditProcessDTO
 * <AUTHOR>
 * @since 2020-05-22
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public GoodwilApplyAuditProcessDTO getById(@PathVariable("id") Long id){
        return goodwilApplyAuditProcessService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param goodwilApplyAuditProcessDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-05-22
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "GoodwilApplyAuditProcessDTO", name = "goodwilApplyAuditProcessDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO){
        return goodwilApplyAuditProcessService.insert( goodwilApplyAuditProcessDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param goodwilApplyAuditProcessDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-05-22
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "GoodwilApplyAuditProcessDTO", name = "goodwilApplyAuditProcessDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO){
        return goodwilApplyAuditProcessService.update(id,goodwilApplyAuditProcessDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-05-22
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    goodwilApplyAuditProcessService.deleteById(id);
        return true;
        }

        /**
         * 根据IDs批量删除对象
         *
         * @param ids 需要修改数据的ID集合
         * <AUTHOR>
         * @since 2020-05-22
         */
                        @ApiImplicitParams({
                                @ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "需要修改数据的ID集合", required = true)
                        })
                        @ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
                        @DeleteMapping(value = "/batch/{ids}")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        public boolean deleteByIds(@PathVariable("ids") String ids){
            goodwilApplyAuditProcessService.deleteBatchIds(ids);
                return true;
                }

}