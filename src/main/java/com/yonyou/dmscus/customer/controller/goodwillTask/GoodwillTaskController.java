package com.yonyou.dmscus.customer.controller.goodwillTask;

import com.yonyou.dmscus.customer.service.goodwillTask.GoodwillTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2020-04-29
 */
@Api(value = "/goodwillTask", tags = {"GoodwillTaskController"})
@RestController
@RequestMapping("/goodwillTask")
public class GoodwillTaskController {

	@Autowired
	GoodwillTaskService goodwillTaskService;

	/**
	 * 超过2个月自动变为自动拒绝
	 * 
	 * @param createDate
	 * @return
	 */
	@ApiOperation(value = "超过2个月自动变为自动拒绝", notes = "超过2个月自动变为自动拒绝", httpMethod = "GET")
	@GetMapping("/createGoodwillTask")
	public int goodwillToRefuse() {
		return goodwillTaskService.goodwillToRefuse();
	}

	/**
	 * 商务亲善预申请审批超时提醒
	 * 
	 * @return
	 */
	@ApiOperation(value = "商务亲善预申请审批超时提醒", notes = "商务亲善预申请审批超时提醒", httpMethod = "GET")
	@GetMapping("/goodwillApplyTimeOutTask")
	public int goodwillApplyTimeOutTask() {
		return goodwillTaskService.goodwillApplyTimeOutTask();
	}

	/**
	 * 商务亲善材料审核超时提醒（2周）
	 * 
	 * @return
	 */
	@ApiOperation(value = "商务亲善材料审核超时提醒（2周）", notes = "商务亲善材料审核超时提醒（2周）", httpMethod = "GET")
	@GetMapping("/goodwillMatrialAuditOneTask")
	public int goodwillMatrialAuditOneTask() {
		return goodwillTaskService.goodwillMatrialAuditOneTask();
	}

	/**
	 * 商务亲善材料审核超时提醒（1个月）
	 * 
	 * @return
	 */
	@ApiOperation(value = "商务亲善材料审核超时提醒（1个月）", notes = "商务亲善材料审核超时提醒（1个月）", httpMethod = "GET")
	@GetMapping("/goodwillMatrialAuditTwoTask")
	public int goodwillMatrialAuditTwoTask() {
		return goodwillTaskService.goodwillMatrialAuditTwoTask();
	}

	/**
	 * 商务亲善材料提交超时提醒（1个月）
	 * 
	 * @return
	 */
	@ApiOperation(value = "商务亲善材料提交超时提醒（1个月）", notes = "商务亲善材料提交超时提醒（1个月）", httpMethod = "GET")
	@GetMapping("/goodwillMatrialCommitOneTask")
	public int goodwillMatrialCommitOneTask() {
		return goodwillTaskService.goodwillMatrialCommitOneTask();
	}

	/**
	 * 商务亲善材料审核超时提醒（2个月）
	 * 
	 * @return
	 */
	@ApiOperation(value = "商务亲善材料审核超时提醒（2个月）", notes = "商务亲善材料审核超时提醒（2个月）", httpMethod = "GET")
	@GetMapping("/goodwillMatrialCommitTwoTask")
	public int goodwillMatrialCommitTwoTask() {
		return goodwillTaskService.goodwillMatrialCommitTwoTask();
	}

	/**
	 * 商务亲善补充材料提交超时提醒（1个月）
	 * 
	 * @return
	 */
	@ApiOperation(value = "商务亲善补充材料提交超时提醒（1个月）", notes = "商务亲善补充材料提交超时提醒（1个月）", httpMethod = "GET")
	@GetMapping("/goodwillSupplyMatrialCommitOneTask")
	public int goodwillSupplyMatrialCommitOneTask() {
		return goodwillTaskService.goodwillSupplyMatrialCommitOneTask();
	}

	/**
	 * 商务亲善补充材料提交超时提醒（2个月）
	 * 
	 * @return
	 */
	@ApiOperation(value = "商务亲善补充材料提交超时提醒（2个月）", notes = "商务亲善补充材料提交超时提醒（2个月）", httpMethod = "GET")
	@GetMapping("/goodwillSupplyMatrialCommitTwoTask")
	public int goodwillSupplyMatrialCommitTwoTask() {
		return goodwillTaskService.goodwillSupplyMatrialCommitTwoTask();
	}

	/**
	 * 商务亲善材料提交过期提醒
	 * 
	 * @return
	 */
	@ApiOperation(value = "商务亲善材料提交过期提醒", notes = "商务亲善材料提交过期提醒", httpMethod = "GET")
	@GetMapping("/goodwillMatrialCommitTimeOutTask")
	public int goodwillMatrialCommitTimeOutTask() {
		return goodwillTaskService.goodwillMatrialCommitTimeOutTask();
	}

	/**
	 * 商务亲善开票超时通知
	 * 
	 * @return
	 */
	@ApiOperation(value = "商务亲善开票超时通知", notes = "商务亲善开票超时通知", httpMethod = "GET")
	@GetMapping("/goodwillInvoiceTimeOutTask")
	public int goodwillInvoiceTimeOutTask() {
		return goodwillTaskService.goodwillInvoiceTimeOutTask();
	}

}
