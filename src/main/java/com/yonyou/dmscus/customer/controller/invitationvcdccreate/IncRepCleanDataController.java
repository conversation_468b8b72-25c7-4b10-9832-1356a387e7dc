package com.yonyou.dmscus.customer.controller.invitationvcdccreate;

import com.yonyou.dmscus.customer.service.invitationautocreate.InvDupDataCleanTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 重复线索数据清洗-可弃用
 */
@Api(value = "/incRepCleanData/csrf", tags = {"重复线索数据清洗-可弃用"})
@RestController
@RequestMapping("/incRepCleanData/csrf")
public class IncRepCleanDataController {

    @Autowired
    InvDupDataCleanTaskService invDupDataCleanTaskService;

    /**
     * 未完成的重复任务清洗
     */
    @ApiOperation(value = "未完成的重复任务清洗", notes = "未完成的重复任务清洗", httpMethod = "POST")
    @PostMapping("/doIncRepTaskClean")
    public void doIncRepTaskClean() {
        invDupDataCleanTaskService.doIncRepTaskClean();
    }

    /**
     * 未完成的重复线索清洗
     */
    @ApiOperation(value = "未完成的重复线索清洗", notes = "未完成的重复线索清洗", httpMethod = "POST")
    @PostMapping("/doIncRepClueClean")
    public void doIncRepClueClean() {
        invDupDataCleanTaskService.doIncRepClueClean();
    }

}
