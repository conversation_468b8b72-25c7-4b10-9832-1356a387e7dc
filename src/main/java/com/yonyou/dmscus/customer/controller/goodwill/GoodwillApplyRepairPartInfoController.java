package com.yonyou.dmscus.customer.controller.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairPartInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyRepairPartInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyRepairPartInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;






                                                                        /**
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
@Api(value = "/goodwillApplyRepairPartInfo", tags = {"GoodwillApplyRepairPartInfoController"})
@RestController
@RequestMapping("/goodwillApplyRepairPartInfo")
                public class GoodwillApplyRepairPartInfoController {
    
        @Autowired
        GoodwillApplyRepairPartInfoService goodwillApplyRepairPartInfoService;

        /**
         * 分页查询数据
         *
        * @param appId 系统ID
        * @param ownerCode 所有者代码
        * @param ownerParCode 所有者的父组织代码
        * @param orgId 组织ID
        * @param id 主键id
        * @param goodwillApplyId 亲善预申请表id
        * @param repairOrderNo 工单号
        * @param partNo 零件号
        * @param partName 零件名称
        * @param quantity 数量
        * @param orderDate 订货日期
        * @param arrivalDate 到货日期
        * @param remark 备注
        * @param isValid 是否有效
        * @param isDeleted 是否删除:1,删除；0,未删除
        * @param createdAt 创建时间
        * @param updatedAt 修改时间
             * @param currentPage 页数
         * @param pageSize 分页大小
         * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
         * <AUTHOR>
         * @throws ParseException 
         * @since 2020-05-11
        */
                @ApiImplicitParams({
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "appId", value = "系统ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "所有者代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerParCode", value = "所有者的父组织代码"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "orgId", value = "组织ID"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键id"),
                        @ApiImplicitParam(paramType = "query", dataType = "long", name = "goodwillApplyId", value = "亲善预申请表id"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "repairOrderNo", value = "工单号"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "partNo", value = "零件号"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "partName", value = "零件名称"),
                        @ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "quantity", value = "数量"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "orderDate", value = "订货日期"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "arrivalDate", value = "到货日期"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "remark", value = "备注"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "isValid", value = "是否有效"),
                        @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "isDeleted", value = "是否删除:1,删除；0,未删除"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "createdAt", value = "创建时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "string", name = "updatedAt", value = "修改时间"),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "页数", required = true),
                        @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页大小", required = true)
                })
                @ApiOperation(value = "分页查询数据", notes = "分页查询数据", httpMethod = "GET")
                @GetMapping
        public IPage<GoodwillApplyRepairPartInfoDTO>getByPage(
			@RequestParam(value="appId",required = false) String appId,
			@RequestParam(value="ownerCode",required = false) String ownerCode,
			@RequestParam(value="ownerParCode",required = false) String ownerParCode,
			@RequestParam(value="orgId",required = false) Integer orgId,
			@RequestParam(value="id",required = false) Long id,
			@RequestParam(value="goodwillApplyId",required = false) Long goodwillApplyId,
			@RequestParam(value="repairOrderNo",required = false) String repairOrderNo,
			@RequestParam(value="partNo",required = false) String partNo,
			@RequestParam(value="partName",required = false) String partName,
			@RequestParam(value="quantity",required = false) BigDecimal quantity,
			@RequestParam(value="orderDate",required = false) String orderDate,
			@RequestParam(value="arrivalDate",required = false) String arrivalDate,
			@RequestParam(value="remark",required = false) String remark,
			@RequestParam(value="isValid",required = false) Integer isValid,
			@RequestParam(value="isDeleted",required = false) Boolean isDeleted,
			@RequestParam(value="createdAt",required = false) String createdAt,
			@RequestParam(value="updatedAt",required = false) String updatedAt,
			@RequestParam("currentPage")int currentPage,
			@RequestParam("pageSize")int pageSize) throws ParseException{
                	Page<GoodwillApplyRepairPartInfoPO>page=new Page(currentPage,pageSize);
                	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                	GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO =new GoodwillApplyRepairPartInfoDTO();
                    goodwillApplyRepairPartInfoDTO.setAppId(appId);
                    goodwillApplyRepairPartInfoDTO.setOwnerCode(ownerCode);
                    goodwillApplyRepairPartInfoDTO.setOwnerParCode(ownerParCode);
                    goodwillApplyRepairPartInfoDTO.setOrgId(orgId);
                    goodwillApplyRepairPartInfoDTO.setId(id);
                    goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(goodwillApplyId);
                    goodwillApplyRepairPartInfoDTO.setRepairOrderNo(repairOrderNo);
                    goodwillApplyRepairPartInfoDTO.setPartNo(partNo);
                    goodwillApplyRepairPartInfoDTO.setPartName(partName);
                    goodwillApplyRepairPartInfoDTO.setQuantity(quantity);
                    goodwillApplyRepairPartInfoDTO.setRemark(remark);
                    goodwillApplyRepairPartInfoDTO.setIsValid(isValid);
                    goodwillApplyRepairPartInfoDTO.setIsDeleted(isDeleted);
                    if(orderDate != null){
                    	goodwillApplyRepairPartInfoDTO.setOrderDate(sdf.parse(orderDate));
                    }
                    if(arrivalDate != null){
                    	goodwillApplyRepairPartInfoDTO.setArrivalDate(sdf.parse(arrivalDate));
                    }
                    if(createdAt != null){
                    	goodwillApplyRepairPartInfoDTO.setCreatedAt(sdf.parse(createdAt));
                    }
                    if(updatedAt != null){
                    	goodwillApplyRepairPartInfoDTO.setUpdatedAt(sdf.parse(updatedAt));
                    }
                return goodwillApplyRepairPartInfoService.selectPageBysql(page,goodwillApplyRepairPartInfoDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 数据主键ID
 * @return com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairPartInfoDTO
 * <AUTHOR>
 * @since 2020-05-11
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "数据主键ID", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "GET")
@GetMapping(value = "/{id}")
public GoodwillApplyRepairPartInfoDTO getById(@PathVariable("id") Long id){
        return goodwillApplyRepairPartInfoService.getById(id);
        }

/**
 * 进行数据新增
 *
 * @param goodwillApplyRepairPartInfoDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-05-11
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyRepairPartInfoDTO", name = "goodwillApplyRepairPartInfoDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据新增", notes = "进行数据新增", httpMethod = "POST")
@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public int insert(@RequestBody GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO){
        return goodwillApplyRepairPartInfoService.insert( goodwillApplyRepairPartInfoDTO);
        }

/**
 * 进行数据修改
 *
 * @param id 需要修改数据的ID
 * @param goodwillApplyRepairPartInfoDTO 需要保存的DTO
 * @return int
 * <AUTHOR>
 * @since 2020-05-11
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true),
        @ApiImplicitParam(paramType = "body", dataType = "GoodwillApplyRepairPartInfoDTO", name = "goodwillApplyRepairPartInfoDTO", value = "需要保存的DTO", required = true)
})
@ApiOperation(value = "进行数据修改", notes = "进行数据修改", httpMethod = "PUT")
@PutMapping(value = "/{id}")
@ResponseStatus(HttpStatus.CREATED)
public int update(@PathVariable("id") Long id,@RequestBody GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO){
        return goodwillApplyRepairPartInfoService.update(id,goodwillApplyRepairPartInfoDTO);
        }

/**
 * 根据id删除对象
 *
 * @param id 需要修改数据的ID
 * <AUTHOR>
 * @since 2020-05-11
 */
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "需要修改数据的ID", required = true)
})
@ApiOperation(value = "根据id删除对象", notes = "根据id删除对象", httpMethod = "DELETE")
@DeleteMapping(value = "/{id}")
@ResponseStatus(HttpStatus.NO_CONTENT)
public boolean deleteById(@PathVariable("id") Long id){
    goodwillApplyRepairPartInfoService.deleteById(id);
        return true;
        }

        /**
         * 根据IDs批量删除对象
         *
         * @param ids 需要修改数据的ID集合
         * <AUTHOR>
         * @since 2020-05-11
         */
                        @ApiImplicitParams({
                                @ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "需要修改数据的ID集合", required = true)
                        })
                        @ApiOperation(value = "根据IDs批量删除对象", notes = "根据IDs批量删除对象", httpMethod = "DELETE")
                        @DeleteMapping(value = "/batch/{ids}")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        public boolean deleteByIds(@PathVariable("ids") String ids){
            goodwillApplyRepairPartInfoService.deleteBatchIds(ids);
                return true;
                }

}