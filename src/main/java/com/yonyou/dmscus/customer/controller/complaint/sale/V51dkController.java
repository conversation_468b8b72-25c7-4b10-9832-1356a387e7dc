package com.yonyou.dmscus.customer.controller.complaint.sale;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.entity.dto.complaint.v51dk.V51dkComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.vo.V51dkComplaintInfMoreVO;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintInfoService;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;


/**
 * <AUTHOR>
 * @since 2020-12-10
 */
@Api(value = "/v51dkSaleComplaintInfo", tags = {"V51dkController"})
@RestController
@RequestMapping("/v51dkSaleComplaintInfo")
public class V51dkController extends BaseController {

    @Autowired
    SaleComplaintInfoService saleComplaintInfoService;

    /**
     * 分页查询数据(店端)
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "V51dkComplaintInfMoreDTO", name = "dto", value = "")
    })
    @ApiOperation(value = "分页查询数据(店端)", notes = "分页查询数据(店端)", httpMethod = "POST")
    @RequestMapping(value = "/selectCusByDeal", method = RequestMethod.POST)
    public Page<V51dkComplaintInfMoreVO> selectCusByDeal(@RequestBody(required=false)  V51dkComplaintInfMoreDTO dto) throws ParseException {
        Page page=new Page(dto.getCurrentPage(),dto.getPageSize());
        dto.setSort("1");
        return saleComplaintInfoService.select51dkCusByDeal(page,dto);
    }

    /**
     *分页查询数据(厂端)
     * @return com.baomidou.mybatisplus.core.metadata.IPage 分页对象
     * <AUTHOR>
     * @since 2020-04-15
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "V51dkComplaintInfMoreDTO", name = "complaintInfMoreDTO", value = "")
    })
    @ApiOperation(value = "分页查询数据(厂端)", notes = "分页查询数据(厂端)", httpMethod = "POST")
    @RequestMapping(value = "/selectCusByCcm", method = RequestMethod.POST)
    public Page<V51dkComplaintInfMoreVO> selectCusByCcm(@RequestBody(required=false)  V51dkComplaintInfMoreDTO complaintInfMoreDTO) throws ParseException {
        Page page=new Page(complaintInfMoreDTO.getCurrentPage(),complaintInfMoreDTO.getPageSize());
        return saleComplaintInfoService.select51dkCusByDeal(page,complaintInfMoreDTO);
    }

}