package com.yonyou.dmscus.customer.controller.complaint;

import com.yonyou.dmscus.customer.dao.complaint.ComplaintWeekDaysMapper;
import com.yonyou.dmscus.customer.entity.po.complaint.HolidayPO;
import com.yonyou.f4.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2021-12-30
 */

@Api(value = "/ComplaintWeekDays", tags = {"ComplaintWeekDaysController"})
@RestController
@RequestMapping("/ComplaintWeekDays")
public class ComplaintWeekDaysController extends BaseController {

    @Resource
    ComplaintWeekDaysMapper complaintWeekDaysMapper;

    /**
     * 查询客诉是否超过5个工作日未结案
     *
     * @param calltime 投诉时间
     * @param nowtime 结案时间
     * <AUTHOR>
     * @since 2021-12-30
     */
     @ApiImplicitParams({
             @ApiImplicitParam(paramType = "query", dataType = "string", name = "calltime", value = "投诉时间", required = true),
             @ApiImplicitParam(paramType = "query", dataType = "string", name = "nowtime", value = "结案时间", required = true)
     })
     @ApiOperation(value = "查询客诉是否超过5个工作日未结案", notes = "查询客诉是否超过5个工作日未结案", httpMethod = "GET")
     @RequestMapping(value = "/selectWeekDays", method = RequestMethod.GET)
     public boolean selectWeekDays(
             @RequestParam(value="calltime") String calltime,
             @RequestParam(value="nowtime") String nowtime) throws ParseException {
         SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
         //一天时间戳（精确到分秒）
         double day=1000*60*60*24;

         //开案时间赋值
         Calendar calltime1 = new GregorianCalendar();
         Calendar weekday = new GregorianCalendar();
         calltime1.setTime(sdf.parse(calltime));
         boolean b=false;
         while(true) {
             HolidayPO holidayPO = complaintWeekDaysMapper.selectOneDays(sdf.parse(calltime));
             weekday.setTime(holidayPO.getHolidayDate());
             weekday.add(weekday.DATE, 1);
             if (sdf.parse(calltime).getTime() - holidayPO.getHolidayDate().getTime() < day) {
                 //若是节假日进入此循环并重新计算下一日是否为节假日  --赋值
                 calltime1.add(calltime1.DATE, 1);
                 calltime= sdf.format(calltime1.getTime());
                 b=true;
             }else if(b==true){
                 // 工作日赋值完成
                 calltime=sdf.format(weekday.getTime());
                 break;
             }else{
                 //非节假日
                 break;
             }
         }

         //结案时间赋值
         Calendar nowtime1 = new GregorianCalendar();
         nowtime1.setTime(sdf.parse(nowtime));
         b= false;
         while(true){
             HolidayPO holidayPO = complaintWeekDaysMapper.selectOneDays(sdf.parse(nowtime));
             if(sdf.parse(nowtime).getTime() - holidayPO.getHolidayDate().getTime() < day){
                 nowtime1.add(nowtime1.DATE,-1);
                 nowtime= sdf.format(nowtime1.getTime());
                 b=true;
             }else if(b==true){
                 nowtime1.add(nowtime1.DATE,+1);
                 nowtime= sdf.format(nowtime1.getTime());
                 nowtime = sdf.format(complaintWeekDaysMapper.selectOneDays(sdf.parse(nowtime)).getHolidayDate());
                 break;
             }else{
                 break;
             }
         }

         //计算是否超过五个工作日
         List<HolidayPO> listholidayPO = complaintWeekDaysMapper.selectWeekDays(sdf.parse(nowtime),sdf.parse(calltime));
         double time=(sdf.parse(nowtime).getTime()-sdf.parse(calltime).getTime())/day;
         if(listholidayPO!=null)
             time=(sdf.parse(nowtime).getTime()-sdf.parse(calltime).getTime())/day-listholidayPO.size();
         if(time>5)
             return false;
         return true;
     }
}
