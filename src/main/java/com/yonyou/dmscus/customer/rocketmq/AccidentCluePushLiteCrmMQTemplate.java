package com.yonyou.dmscus.customer.rocketmq;

import org.apache.rocketmq.spring.annotation.ExtRocketMQTemplateConfiguration;
import org.apache.rocketmq.spring.core.RocketMQTemplate;

/**
 * <AUTHOR>
 * @Date 2023/11/16 17:39
 * @Version 1.0
 */
@ExtRocketMQTemplateConfiguration(nameServer = "${accidentClue.liteCrm.rocketmq.nameServer:}",
        group = "${accidentClue.liteCrm.rocketmq.producerGroup:}",
        accessKey = "${accidentClue.liteCrm.rocketmq.accessKey}",
        secretKey = "${accidentClue.liteCrm.rocketmq.secretKey}",
        tlsEnable = "${accidentClue.liteCrm.rocketmq.tlsEnable}")
public class AccidentCluePushLiteCrmMQTemplate extends RocketMQTemplate {

}
