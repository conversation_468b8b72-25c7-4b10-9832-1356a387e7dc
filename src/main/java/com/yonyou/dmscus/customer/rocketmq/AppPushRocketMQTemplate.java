package com.yonyou.dmscus.customer.rocketmq;

import org.apache.rocketmq.spring.annotation.ExtRocketMQTemplateConfiguration;
import org.apache.rocketmq.spring.core.RocketMQTemplate;

/**
 * <AUTHOR>
 * @Date 2023/10/31 14:46
 * @Version 1.0
 */
@ExtRocketMQTemplateConfiguration(nameServer = "${accidentClue.appPush.rocketmq.nameServer:}",
        group = "${accidentClue.appPush.rocketmq.producerGroup:}",
        accessKey = "${accidentClue.appPush.rocketmq.accessKey}",
        secretKey = "${accidentClue.appPush.rocketmq.secretKey}")
public class AppPushRocketMQTemplate extends RocketMQTemplate {

}
