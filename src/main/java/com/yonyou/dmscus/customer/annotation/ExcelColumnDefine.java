package com.yonyou.dmscus.customer.annotation;

import com.yonyou.dmscus.customer.ossexcel.ExcelDataType;

import java.lang.annotation.*;

/**
 * 定义excel 列对应DTO 的定义
 *
 * <AUTHOR>
 * @date 2016年8月12日
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ExcelColumnDefine {

    int value();

    String message() default "";

    String format() default "";

    ExcelDataType dataType() default ExcelDataType.NotDefine;

    int dataCode() default -1;

    String name() default "";
    boolean hiddenColumn() default false;
}
