package com.yonyou.dmscus.customer;

import com.yonyou.cyxdms.common.context.CustomerAnnotationConfigServletWebServerApplicationContext;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.HashMap;
import java.util.Map;

@EnableFeignClients({ "com.yonyou" })
@EnableDiscoveryClient
@SpringBootApplication(exclude = MongoAutoConfiguration.class)
@ServletComponentScan
@ComponentScan("com.yonyou.dmscloud.framework")
@EnableTransactionManagement(order = 0)
@MapperScan("com.yonyou.dmscus.customer.dao")
@EnableSwagger2
@EnableRetry
public class CustomerApplication {

	public static void main(String[] args) {
		SpringApplication springApplication = new SpringApplication(CustomerApplication.class);
		springApplication.setApplicationContextClass(CustomerAnnotationConfigServletWebServerApplicationContext.class);
		Map<String, Object> props = new HashMap<>();
		props.put("spring.main.allow-bean-definition-overriding", true);
		springApplication.setDefaultProperties(props);
		springApplication.run();
	}

}
