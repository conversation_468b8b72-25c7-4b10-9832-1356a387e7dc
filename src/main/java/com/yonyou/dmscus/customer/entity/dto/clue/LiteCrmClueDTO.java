package com.yonyou.dmscus.customer.entity.dto.clue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * description 线索下发通用接口入参
 *
 * <AUTHOR>
 * @date 2023/8/21 15:23
 */
@Data
@ApiModel(description="线索下发通用接口参数")
public class LiteCrmClueDTO {

    /**
     * LiteCrm线索id
     */
    @ApiModelProperty(value = "LiteCrm线索id", required = true, example = "100000")
    private Long id;

    /**
     * 数据更新时间
     */
    @ApiModelProperty(value = "数据更新时间", required = false)
    private String dataUpdateDate;

    /**
     * 线索状态
     */
    @ApiModelProperty(value = "线索状态", required = false)
    private String clueStatus;

    /**
     * 原始线索id（湖仓id)
     */
    @ApiModelProperty(value = "原始线索id（湖仓id)", required = true, example = "111111")
    private String sourceClueId;

    /**
     * 车辆Vin码
     */
    @ApiModelProperty(value = "车辆Vin码", required = true, example = "YV1LFA2D1G1011864")
    private String vehicleVin;

    /**
     * 线索收集时间
     */
    @ApiModelProperty(value = "线索收集时间", required = true, example = "2023-04-21 10:50:00")
    private String leadsReceiveTime;

    /**
     * 线索类型
     */
    @ApiModelProperty(value = "线索类型", required = true, example = "100")
    private String leadsType;

    /**
     * 线索数据
     */
    @ApiModelProperty(value = "线索数据", required = true)
    private ClueDataDTO data;
}
