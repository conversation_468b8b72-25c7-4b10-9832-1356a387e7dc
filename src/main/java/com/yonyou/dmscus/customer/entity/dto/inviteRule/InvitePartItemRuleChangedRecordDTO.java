package com.yonyou.dmscus.customer.entity.dto.inviteRule;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 邀约易损件和项目规则变更记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-18
 */
@Data
public class InvitePartItemRuleChangedRecordDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 经销商代码（VCDC：代表厂端，经销商代码：代表各自经销商）
     */
    private String dealerCode;

    /**
     * 类型：易损件、项目
     */
    private Integer type;

    /**
     * 零件、维修项目编号
     */
    private String code;

    /**
     * 零件、维修项目名称
     */
    private String name;

    /**
     * 再提醒间隔（月）
     */
    private Integer remindInterval;

    /**
     * 间隔里程（Km）
     */
    private Integer mileageInterval;

    /**
     * 间隔日期（月）
     */
    private Integer dateInterval;

    /**
     * 车架号 必须输入17位，---K---45—3----
     */
    private String vin;

    /**
     * 车型编码
     */
    private String modelCode;

    /**
     * 年款
     */
    private String modelYear;

    /**
     * 发动机编码
     */
    private String engineCode;

    /**
     * 变速箱编码
     */
    private String gearboxCode;

    /**
     * 规则关系：and 和，or 或
     */
    private Integer ruleRelationship;

    /**
     * 是否启用：1 启用，0 不启用
     */
    private Integer isUse;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 修改后未处理 1:是0,否
     */
    private Boolean updateIsExecute;

    /**
     * 原再提醒间隔（月）
     */
    private Integer lastRemindInterval;

    /**
     * 原间隔里程（Km）
     */
    private Integer lastMileageInterval;

    /**
     * 原间隔日期（月）
     */
    private Integer lastDateInterval;

    /**
     * 原车架号 必须输入17位，---K---45—3----
     */
    private String lastVin;

    /**
     * 原车型编码
     */
    private String lastModelCode;

    /**
     * 原年款
     */
    private String lastModelYear;

    /**
     * 原发动机编码
     */
    private String lastEngineCode;

    /**
     * 原变速箱编码
     */
    private String lastGearboxCode;

    /**
     * 原规则关系：and 和，or 或
     */
    private Integer lastRuleRelationship;

    /**
     * 原是否启用：1 启用，0 不启用
     */
    private Integer lastIsUse;

    /**
     * 备注
     */
    private String remark;


    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
