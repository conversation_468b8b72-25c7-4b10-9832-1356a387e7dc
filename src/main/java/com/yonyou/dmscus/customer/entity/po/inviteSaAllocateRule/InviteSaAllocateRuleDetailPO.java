package com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * SA分配规则明细表,通过经销商CODE与invite_sa_allocate_rule关联，用于经销商SA分配中平均分配
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
@Data
@TableName("tt_invite_sa_allocate_rule_detail")
public class InviteSaAllocateRuleDetailPO extends BasePO<InviteSaAllocateRuleDetailPO> {

    private static final long serialVersionUID = 1L;


    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;


    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经销商代码
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 服务顾问ID
     */
    @TableField("sa_id")
    private String saId;

    /**
     * 服务顾问姓名
     */
    @TableField("sa_name")
    private String saName;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;


    /**
     * 服务顾问code
     */
    @TableField("sa_code")
    private String saCode;

    /**
     * 是否续保
     */
    @TableField("is_insurance")
    private Integer isInsurance;

    /**
     * 是否其他
     */
    @TableField("is_other")
    private Integer isOther;




    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
