package com.yonyou.dmscus.customer.entity.po.complaint;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

    import com.yonyou.dmscloud.framework.base.po.BasePO;
    
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;

import java.util.Date;

/**
 * <p>
 * 5日未结案原因
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@TableName("tt_complaint_not_close_case_reason")
public class ComplaintNotCloseCaseReasonPO extends BasePO<ComplaintNotCloseCaseReasonPO> {

    private static final long serialVersionUID=1L;
    
    /**
     * 系统ID
     */
    @TableField("app_id")
        private String appId;
    
    /**
     * 所有者代码 
     */
    @TableField("owner_code")
        private String ownerCode;
    
    /**
     * 所有者的父组织代码 
     */
    @TableField("owner_par_code")
        private String ownerParCode;
    
    /**
     * 组织ID
     */
    @TableField("org_id")
        private Integer orgId;
        
    /**
     * 主键ID
     */
            @TableId(value = "id", type = IdType.AUTO)
                        private Long id;
    
    /**
     * 投诉信息表主键ID
     */
    @TableField("complaint_info_id")
        private Long complaintInfoId;
    
    /**
     * 跟进对象 区域经理、经销商、CCM、客服中心
     */
    @TableField("object")
        private String object;
    
    /**
     * 填写时间
     */
    @TableField("follow_time")
        private Date followTime;
    
    /**
     * 填写人
     */
    @TableField("follower")
        private String follower;

    /**
     * 填写人
     */
    @TableField("follower_name")
    private String followerName;
    
    /**
     * 未结案原因（大类）
     */
    @TableField("big_class")
        private Integer bigClass;
    
    /**
     * 未结案原因（大类）名称
     */
    @TableField("big_class_name")
        private String bigClassName;
    
    /**
     * 未结案原因（小类）多选用逗号分隔
     */
    @TableField("small_class")
        private String smallClass;
    
    /**
     * 未结案原因（小类）名称 多选用逗号分隔
     */
    @TableField("small_class_name")
        private String smallClassName;
    
    /**
     * 其他
     */
    @TableField("other")
        private String other;
    
    /**
     * 时长（天）
     */
    @TableField("duration")
        private Integer duration;
    
    /**
     * 数据来源 
     */
    @TableField("data_sources")
        private Integer dataSources;
    
    /**
     * 是否删除
     */
    @TableField("is_deleted")
        private Boolean isDeleted;
    
    /**
     * 是否有效 
     */
    @TableField("is_valid")
        private Integer isValid;
    
    /**
     * 创建时间
     */
    @TableField("created_at")
        private Date createdAt;
    
    /**
     * 更新时间
     */
    @TableField("updated_at")
        private Date updatedAt;

    public ComplaintNotCloseCaseReasonPO(){
        super();
    }

                    

    public String getOwnerCode(){
        return ownerCode;
    }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
            }

    public String getFollowerName() {
        return followerName;
    }

    public void setFollowerName(String followerName) {
        this.followerName = followerName;
    }

    public Integer getOrgId(){
        return orgId;
    }

        public void setOrgId(Integer orgId) {
            this.orgId = orgId;
            }
                    
    public Long getId(){
        return id;
    }

        public void setId(Long id) {
            this.id = id;
            }
                    
    public Long getComplaintInfoId(){
        return complaintInfoId;
    }

        public void setComplaintInfoId(Long complaintInfoId) {
            this.complaintInfoId = complaintInfoId;
            }
                    
    public String getObject(){
        return object;
    }

        public void setObject(String object) {
            this.object = object;
            }
                    
    public Date getFollowTime(){
        return followTime;
    }

        public void setFollowTime(Date followTime) {
            this.followTime = followTime;
            }
                    
    public String getFollower(){
        return follower;
    }

        public void setFollower(String follower) {
            this.follower = follower;
            }
                    
    public Integer getBigClass(){
        return bigClass;
    }

        public void setBigClass(Integer bigClass) {
            this.bigClass = bigClass;
            }
                    
    public String getBigClassName(){
        return bigClassName;
    }

        public void setBigClassName(String bigClassName) {
            this.bigClassName = bigClassName;
            }
                    
    public String getSmallClass(){
        return smallClass;
    }

        public void setSmallClass(String smallClass) {
            this.smallClass = smallClass;
            }
                    
    public String getSmallClassName(){
        return smallClassName;
    }

        public void setSmallClassName(String smallClassName) {
            this.smallClassName = smallClassName;
            }
                    
    public String getOther(){
        return other;
    }

        public void setOther(String other) {
            this.other = other;
            }
                    
    public Integer getDuration(){
        return duration;
    }

        public void setDuration(Integer duration) {
            this.duration = duration;
            }
                    
    public Integer getDataSources(){
        return dataSources;
    }

        public void setDataSources(Integer dataSources) {
            this.dataSources = dataSources;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }

        public void setIsDeleted(Boolean isDeleted) {
            this.isDeleted = isDeleted;
            }
                    
    public Integer getIsValid(){
        return isValid;
    }

        public void setIsValid(Integer isValid) {
            this.isValid = isValid;
            }
                    

    
    @Override
    protected Serializable pkVal(){
            return this.id;
        }

@Override
    public String toString(){
        return"ComplaintNotCloseCaseReasonPO{" +
                            "appId=" + appId +
                                    ", ownerCode=" + ownerCode +
                                    ", ownerParCode=" + ownerParCode +
                                    ", orgId=" + orgId +
                                    ", id=" + id +
                                    ", complaintInfoId=" + complaintInfoId +
                                    ", object=" + object +
                                    ", followTime=" + followTime +
                                    ", follower=" + follower +
                                    ", bigClass=" + bigClass +
                                    ", bigClassName=" + bigClassName +
                                    ", smallClass=" + smallClass +
                                    ", smallClassName=" + smallClassName +
                                    ", other=" + other +
                                    ", duration=" + duration +
                                    ", dataSources=" + dataSources +
                                    ", isDeleted=" + isDeleted +
                                    ", isValid=" + isValid +
                                    ", createdAt=" + createdAt +
                                    ", updatedAt=" + updatedAt +
                    "}";
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"id");
    }

}
