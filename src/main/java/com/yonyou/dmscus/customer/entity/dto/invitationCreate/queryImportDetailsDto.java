package com.yonyou.dmscus.customer.entity.dto.invitationCreate;

import com.yonyou.dmscus.customer.utils.excel.ExcelExport;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "自建线索导入查询结果实体（跨表聚合）")
public class queryImportDetailsDto {
    /**
     * 计划编号（来源：tt_voc_invite_vehicle_task_record表）
     */
    @ApiModelProperty(value = "计划编号")
    @ExcelExport("计划编号")
    private String planNo;

    /**
     * 邀约名称（来源：tt_invite_vehicle_record表）
     */
    @ApiModelProperty(value = "邀约名称")
    @ExcelExport("邀约名称")
    private String inviteName;

    /**
     * 经销商代码（来源：tt_invite_vehicle_record表）
     */
    @ApiModelProperty(value = "经销商代码")
    @ExcelExport("经销商代码")
    private String dealerCode;

    /**
     * 建议进厂日期（来源：tt_invite_vehicle_record表）
     */
    @ApiModelProperty(value = "建议进厂日期", example = "2025-09-10 09:30:00")
    @ExcelExport("建议进厂日期")
    private Date adviseInDate;

    /**
     * VIN（来源：tt_invite_vehicle_record表）
     */
    @ApiModelProperty(value = "车辆识别码（VIN）", example = "LFV2A28KXJ3000001")
    @ExcelExport("车辆识别码（VIN）")
    private String vin;

    /**
     * 车牌号（来源：tt_invite_vehicle_record表）
     */
    @ApiModelProperty(value = "车牌号", example = "京A12345")
    @ExcelExport("车牌号")
    private String licensePlateNum;

    /**
     * 客户名称（来源：tt_invite_vehicle_record表）
     */
    @ApiModelProperty(value = "客户名称", example = "张三")
    @ExcelExport("客户名称")
    private String name;

    /**
     * 联系方式（来源：tt_invite_vehicle_record表）
     */
    @ApiModelProperty(value = "客户联系方式", example = "13800138000")
    @ExcelExport("联系方式")
    private String tel;

    /**
     * 逾期关闭日期（来源：tt_voc_invite_vehicle_task_record表）
     */
    @ApiModelProperty(value = "逾期关闭日期", example = "2025-09-20 18:00:00")
    @ExcelExport("逾期关闭日期")
    private Date overdueCloseDate;

    /**
     * 导入时间（导入系统的时间，来源：tt_invite_vehicle_dealer_task表）
     */
    @ApiModelProperty(value = "数据导入系统时间", example = "2025-09-03 10:15:30")
    @ExcelExport("数据导入系统时间")
    private Date createdAt;

    /**
     * 操作人（导入数据的操作人账号，来源：tt_invite_vehicle_dealer_task表）
     */
    @ApiModelProperty(value = "导入数据的操作人账号", example = "admin_001")
    @ExcelExport("导入数据的操作人账号")
    private String createdName;

}
