package com.yonyou.dmscus.customer.entity.po.complaint;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("tt_common_config")
public class CommonConfigPO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @TableField("id")
    private Long id;

    /**
     * 配置代码
     */
    @ApiModelProperty("配置代码")
    @TableField("config_code")
    private String configCode;

    /**
     * 分组类型
     */
    @ApiModelProperty("分组类型")
    @TableField("group_type")
    private String groupType;

    /**
     * 配置key
     */
    @ApiModelProperty("配置key")
    @TableField("config_key")
    private String configKey;

    /**
     * 配置值
     */
    @ApiModelProperty("配置值")
    @TableField("config_value")
    private String configValue;

    /**
     * 配置描述
     */
    @ApiModelProperty("配置描述")
    @TableField("config_desc")
    private String configDesc;

    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    @TableField("config_ext1")
    private String configExt1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty("扩展字段2")
    @TableField("config_ext2")
    private String configExt2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty("扩展字段3")
    @TableField("config_ext3")
    private String configExt3;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField("remark")
    private String remark;

    /**
     * 是否删除:1，删除；0，未删除
     */
    @ApiModelProperty("是否删除:1，删除；0，未删除")
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 数据创建时间
     */
    @ApiModelProperty("数据创建时间")
    @TableField("created_at")
    private Date createdAt;

    /**
     * 数据创建人
     */
    @ApiModelProperty("数据创建人")
    @TableField("created_by")
    private String createdBy;

    /**
     * 数据修改时间
     */
    @ApiModelProperty("数据修改时间")
    @TableField("updated_at")
    private Date updatedAt;

    /**
     * 数据修改人
     */
    @ApiModelProperty("数据修改人")
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 创建sql人
     */
    @ApiModelProperty("创建sql人")
    @TableField("create_sqlby")
    private String createSqlby;

    /**
     * 更新人sql人
     */
    @ApiModelProperty("更新人sql人")
    @TableField("update_sqlby")
    private String updateSqlby;
}
