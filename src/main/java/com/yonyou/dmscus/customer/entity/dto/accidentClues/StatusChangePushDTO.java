package com.yonyou.dmscus.customer.entity.dto.accidentClues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/11/16 15:21
 * @Version 1.0
 */
@Data
@ApiModel("线索状态推送LiteCRM参数信息")
public class StatusChangePushDTO {

    @ApiModelProperty(value = "线索id")
    private String id;

    @ApiModelProperty(value = "线索id")
    private String sourceClueId;

    @ApiModelProperty(value = "工单状态")
    private String bizStatus;

    @ApiModelProperty(value = "线索状态")
    private String followUpStatus;

    @ApiModelProperty(value = "线索类型")
    private String leadsType;

    @ApiModelProperty(value = "经销商代码")
    private String dealerCode;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    @ApiModelProperty(value = "更改后的经销商")
    private String currentDealerCode;
    @ApiModelProperty(value = "之前的经销商")
    private String originalDealerCode;
}
