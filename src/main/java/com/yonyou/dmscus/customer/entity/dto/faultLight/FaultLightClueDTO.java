package com.yonyou.dmscus.customer.entity.dto.faultLight;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FaultLightClueDTO {

    private static final long serialVersionUID = 9097135326708418025L;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @TableField("id")
    private Long id;

    /**
     * 线索code
     */
    @ApiModelProperty("icmId")
    @TableField("icm_id")
    private Long icmId;

    /**
     * 车辆vin码
     */
    @ApiModelProperty("车辆vin码")
    @TableField("vin")
    private String vin;

    /**
     * 经销商code
     */
    @ApiModelProperty("经销商code")
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 经销商名称
     */
    @ApiModelProperty("经销商名称")
    @TableField("dealer_name")
    private String dealerName;

    /**
     * 故障id
     */
    @ApiModelProperty("故障id")
    @TableField("fault_id")
    private Long faultId;

    /**
     * 故障发生时间
     */
    @ApiModelProperty("故障发生时间")
    @TableField("alarm_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date alarmTime;

    /**
     * 故障发生城市
     */
    @ApiModelProperty("故障城市")
    @TableField(exist = false)
    private String warningName;

    /**
     * 大区id
     */
    @ApiModelProperty("大区id")
    @TableField("region_id")
    private Long regionId;

    /**
     * 大区名称
     */
    @ApiModelProperty("大区名称")
    @TableField("region_name")
    private String regionName;

    /**
     * 小区id
     */
    @ApiModelProperty("小区id")
    @TableField("cell_id")
    private Long cellId;

    /**
     * 小区名称
     */
    @ApiModelProperty("小区名称")
    @TableField("cell_name")
    private String cellName;

    /**
     * 城市id
     */
    @ApiModelProperty("城市id")
    @TableField("city_id")
    private Long cityId;

    /**
     * 城市名称
     */
    @ApiModelProperty("城市名称")
    @TableField("city_name")
    private String cityName;

    /**
     * 故障城市名称
     */
    @ApiModelProperty("故障城市名称")
    @TableField("fault_city_name")
    private String faultCityName;

    /**
     * 故障城市ID
     */
    @ApiModelProperty("故障城市id")
    @TableField("fault_city_id")
    private Long faultCityId;

    /**
     * 集团简称
     */
    @ApiModelProperty("集团简称")
    @TableField("group_company_short_name")
    private String groupCompanyShortName;

    /**
     * 线索前状态(乐观锁)
     */
    @TableField(exist = false)
    private Integer afClueStatus;

    /**
     * 线索状态
     */
    @ApiModelProperty("线索状态")
    @TableField("clue_status")
    private Integer clueStatus;

    /**
     * 跟进状态
     */
    @ApiModelProperty("跟进状态")
    @TableField("follow_status")
    private Integer followStatus;

    /**
     * 线索生成时间
     */
    @ApiModelProperty("线索生成时间")
    @TableField("clue_gen_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date clueGenTime;

    /**
     * 线索下发时间
     */
    @ApiModelProperty("线索下发时间")
    @TableField("clue_dis_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date clueDisTime;

    /**
     * 邀约时间
     */
    @ApiModelProperty("邀约时间")
    @TableField("invite_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inviteTime;

    /**
     * 工单号
     */
    @ApiModelProperty("工单号")
    @TableField("ro_no")
    private String roNo;

    /**
     * 工单开始时间
     */
    @ApiModelProperty("工单开始时间")
    @TableField("ro_start_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date roStartTime;

    /**
     * 工单结算时间
     */
    @ApiModelProperty("工单结算时间")
    @TableField("ro_end_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date roEndTime;

    /**
     * 维修完成时间
     */
    @ApiModelProperty("维修完成时间")
    @TableField("repair_com_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date repairComTime;

    /**
     * 线索关闭时间
     */
    @ApiModelProperty("线索关闭时间")
    @TableField("clue_clo_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date clueCloTime;

    /**
     * 线索完成时间
     */
    @ApiModelProperty("线索完成时间")
    @TableField("clue_com_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date clueComTime;

    /**
     * 工单类型
     */
    @ApiModelProperty("工单类型")
    @TableField("ro_type")
    private String roType;

    /**
     * 工单金额
     */
    @ApiModelProperty("工单金额")
    @TableField("ro_amount")
    private String roAmount;

    /**
     * 缺件 0.无 1.是
     */
    @ApiModelProperty("缺件 0.无 1.是")
    @TableField("miss_parts")
    private Integer missParts;

    /**
     * 不修 0.无 1.是
     */
    @ApiModelProperty("不修 0.无 1.是")
    @TableField("no_repair")
    private Integer noRepair;

    /**
     * 离店亮灯 0.无 1.是
     */
    @ApiModelProperty("离店亮灯 0.无 1.是")
    @TableField("lights_up")
    private Integer lightsUp;

    /**
     * 是否有责 0.无数据 1.无责 2.有责 3.待确认
     */
    @ApiModelProperty("是否有责 0.无数据 1.无责 2.有责 3.待确认")
    @TableField("whe_res")
    private Integer wheRes;

    /**
     * 邀约响应是否超时 0.没有超时 1.超时
     */
    @ApiModelProperty("邀约响应是否超时 0.没有超时 1.超时")
    @TableField("invite_overtime")
    private Integer inviteOvertime;

    /**
     * 预约进店时间
     */
    @ApiModelProperty("预约进店时间")
    @TableField("forecast_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date forecastTime;

    /**
     * 是否高亮 0:无 1:否 2:是
     */
    @ApiModelProperty("是否高亮 0:无 1:否 2:是")
    @TableField("highlight_flag")
    private Integer highlightFlag;
    /**
     * 是否删除:1，删除；0，未删除
     */
    @ApiModelProperty("是否删除:1，删除；0，未删除")
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    /**
     * 数据创建时间
     */
    @ApiModelProperty("数据创建时间")
    @TableField("created_at")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 数据创建人
     */
    @ApiModelProperty("数据创建人")
    @TableField("created_by")
    private String createdBy;

    /**
     * 数据修改时间
     */
    @ApiModelProperty("数据修改时间")
    @TableField("updated_at")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 数据修改人
     */
    @ApiModelProperty("数据修改人")
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 创建sql人
     */
    @ApiModelProperty("创建sql人")
    @TableField("create_sqlby")
    private String createSqlby;

    /**
     * 更新人sql人
     */
    @ApiModelProperty("更新人sql人")
    @TableField("update_sqlby")
    private String updateSqlby;

    /**
     * 线索来源类型
     */
    @ApiModelProperty("线索来源类型")
    @TableField("source_type")
    private String sourceType;

    /**
     * 是否展示DTC诊断信息 (0-否, 1-是)
     */
    @ApiModelProperty("是否展示DTC诊断信息 (0-否, 1-是)")
    @TableField("trouble_code")
    private String troubleCode;

    /**
     * 日均里程
     */
    @ApiModelProperty("日均里程")
    @TableField("daily_mile")
    private String dailyMile;

    /**
     * 胡仓线索ID
     */
    @ApiModelProperty("胡仓线索ID")
    @TableField("source_clue_id")
    private Long sourceClueId;

    /**
     * 预约单号
     */
    @ApiModelProperty("预约单号")
    @TableField("booking_order_no")
    private String bookingOrderNo;
}
