package com.yonyou.dmscus.customer.entity.po.talkskill;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 话术业务分类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-20
 */
@Data
@TableName("tt_talkskill_type")
public class TalkskillTypePO extends BasePO<TalkskillTypePO> {

    private static final long serialVersionUID=1L;
        
    /**
     * 主键ID
     */
    @TableId(value = "type_id", type = IdType.AUTO)
    private Long typeId;
    
    /**
     * 分类编码
     */
    @TableField("type_code")
    private String typeCode;
    
    /**
     * 分类名称
     */
    @TableField("type_name")
    private String typeName;
    
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;
    
    /**
     * 是否删除，1：删除，0：未删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;
    
    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;


    @TableField("created_at")
    private Date createdAt;
    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;

    public TalkskillTypePO(){
        super();
    }

    
    @Override
    protected Serializable pkVal(){
            return this.typeId;
        }


/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"typeId");
    }

}
