package com.yonyou.dmscus.customer.entity.dto.clue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * description 车辆信息参数
 * <AUTHOR>
 * @date 2023/8/21 17:50
 */

@Data
@ApiModel(description = "车辆信息参数")
public class CarInfoDTO {
    /**
     * 牌照
     */
    @ApiModelProperty(value = "牌照")
    private String licencePlate;
    /**
     * 日均里程
     */
    @ApiModelProperty(value = "日均里程")
    private String dailyMile;
    /**
     * 当前里程
     */
    @ApiModelProperty(value = "当前里程")
    private String currentMile;
    /**
     * 车牌ID
     */
    @ApiModelProperty(value = "车牌ID")
    private String carModelId;
    /**
     * 车型名称
     */
    @ApiModelProperty(value = "车型名称")
    private String carModelName;
    /**
     * 年款
     */
    @ApiModelProperty(value = "年款")
    private String modelYear;
}
