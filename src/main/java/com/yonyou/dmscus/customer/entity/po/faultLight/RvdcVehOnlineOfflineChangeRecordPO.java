package com.yonyou.dmscus.customer.entity.po.faultLight;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("车辆上下线状态变更记录")
@TableName("tt_rvdc_veh_online_offline_change_record")
public class RvdcVehOnlineOfflineChangeRecordPO {

    private static final long serialVersionUID = 9097135326708418025L;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @TableField("id")
    private Long id;

    /**
     * 车辆vin码
     */
    @ApiModelProperty("车辆vin码")
    @TableField("veh_vin")
    private String vehVin;

    /**
     * 状态类型
     */
    @ApiModelProperty("状态类型")
    @TableField("stat_type")
    private String statType;

    /**
     * 发送时间
     */
    @ApiModelProperty("发送时间")
    @TableField("send_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;

    /**
     * 省份编号
     */
    @ApiModelProperty("省份编号")
    @TableField("province_id")
    private Long provinceId;

    /**
     * 省份名称中文
     */
    @ApiModelProperty("省份名称中文")
    @TableField("province_name_cn")
    private String provinceNameCn;

    /**
     * 市编号
     */
    @ApiModelProperty("市编号")
    @TableField("city_id")
    private Long cityId;

    /**
     * 市名称中文
     */
    @ApiModelProperty("市名称中文")
    @TableField("city_name_cn")
    private String cityNameCn;

    /**
     * 区县号码
     */
    @ApiModelProperty("区县号码")
    @TableField("dist_id")
    private Long distId;

    /**
     * 区县名称中文
     */
    @ApiModelProperty("区县名称中文")
    @TableField("dist_name_cn")
    private String distNameCn;

    /**
     * 发生时里程
     */
    @ApiModelProperty("发生时里程")
    @TableField("mileage")
    private Long mileage;

    /**
     * ETL处理时间
     */
    @ApiModelProperty("ETL处理时间")
    @TableField("etl_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date etlTime;

    /**
     * 是否删除:1，删除；0，未删除
     */
    @ApiModelProperty("是否删除:1，删除；0，未删除")
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField("remarks")
    private String remarks;

    /**
     * 数据创建时间
     */
    @ApiModelProperty("数据创建时间")
    @TableField("created_at")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 数据创建人
     */
    @ApiModelProperty("数据创建人")
    @TableField("created_by")
    private String createdBy;

    /**
     * 数据修改时间
     */
    @ApiModelProperty("数据修改时间")
    @TableField("updated_at")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 数据修改人
     */
    @ApiModelProperty("数据修改人")
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 创建sql人
     */
    @ApiModelProperty("创建sql人")
    @TableField("create_sqlby")
    private String createSqlby;

    /**
     * 更新人sql人
     */
    @ApiModelProperty("更新人sql人")
    @TableField("update_sqlby")
    private String updateSqlby;
}
