package com.yonyou.dmscus.customer.entity.dto.accidentClues;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(value="AccidentCluesExportDTO",description="事故线索")
@Data
public class AccidentCluesExportDTO extends BaseDTO implements Serializable {

  @ApiModelProperty(value = "系统ID",name="appId")
  private String appId;

  @ApiModelProperty(value = "所有者代码",name="ownerCode")
  private String ownerCode;

  @ApiModelProperty(value = "所有者的父组织代码",name="ownerParCode")
  private String ownerParCode;

  @ApiModelProperty(value = "组织ID",name="orgId")
  private Integer orgId;

  @ApiModelProperty(value = "主键ID",name="acId")
  private Integer acId;

  @ApiModelProperty("主键list")
  private List<Integer> acIdList;

  @ApiModelProperty(value = "经销商license代码",name="dealerCode")
  private String dealerCode;

  @ApiModelProperty(value = "车牌号",name="license")
  private String license;

  @ApiModelProperty(value = "车型code",name="modelsId")
  private String modelsId;

  @ApiModelProperty(value = "保险公司id",name="insuranceCompanyId")
  private String insuranceCompanyId;
  
  @ApiModelProperty(value = "保险公司id",name="insuranceCompanyName")
  private String  insuranceCompanyName;

  @ApiModelProperty(value = "线索来源",name="cluesResource")
  private Integer cluesResource;

  @ApiModelProperty(value = "联系人",name="contacts")
  private String contacts;

  @ApiModelProperty(value = "联系人电话",name="contactsPhone")
  private String contactsPhone;

  @ApiModelProperty(value = "报案时间",name="reportDate")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date reportDate;

  @ApiModelProperty(value = "事故地点",name="accidentAddress")
  private String accidentAddress;

  @ApiModelProperty(value = "事故类型",name="accidentType")
  private Integer accidentType;

  @ApiModelProperty(value = "是否有人受伤",name="isBruise")
  private Integer isBruise;

  @ApiModelProperty(value = "外拓费用",name="outsideAmount")
  private BigDecimal outsideAmount;

  @ApiModelProperty(value = "跟进人员id",name="followPeople")
  private Integer followPeople;
  
  @ApiModelProperty(value = "跟进人员name",name="followPeopleName")
  private String  followPeopleName;

  @ApiModelProperty(value = "备注",name="remark")
  private String remark;

  @ApiModelProperty(value = "是否本店承保",name="isInsured")
  private Integer isInsured;

  @ApiModelProperty(value = "事故责任",name="accidentDuty")
  private Integer accidentDuty;

  @ApiModelProperty(value = "跟进状态",name="followStatus")
  private Integer followStatus;

  @ApiModelProperty(value = "线索状态",name="cluesStatus")
  private Integer cluesStatus;

  @ApiModelProperty(value = "进厂经销商",name="intoDealerCode")
  private String intoDealerCode;

  @ApiModelProperty(value = "进厂时间",name="intoDealerDate")
  private Date intoDealerDate;

  @ApiModelProperty(value = "进厂工单号",name="intoRoNo")
  private String intoRoNo;

  @ApiModelProperty(value = "下次跟进时间",name="nextFollowDate")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date nextFollowDate;

  @ApiModelProperty(value = "是否预约",name="isAppointment")
  private Integer isAppointment;

  @ApiModelProperty(value = "跟进次数",name="followCount")
  private Integer followCount;

  @ApiModelProperty(value = "线索类型",name="cluesType")
  private String cluesType;

  @ApiModelProperty(value = "数据来源",name="dataSources")
  private Integer dataSources;

  @ApiModelProperty(value = "是否删除，1：删除，0：未删除",name="isDeleted")
  private Integer isDeleted;

  @ApiModelProperty(value = "是否有效",name="isValid")
  private Integer isValid;

  @ApiModelProperty(value = "创建时间",name="createdAt")
  private Date createdAt;

  @ApiModelProperty(value = "创建人",name="createdBy")
  private String createdBy;

  @ApiModelProperty(value = "更新时间",name="updatedAt")
  private Date updatedAt;

  @ApiModelProperty(value = "更新人",name="updatedBy")
  private String updatedBy;

  @ApiModelProperty(value = "版本号（乐观锁）",name="recordVersion")
  private Integer recordVersion;
  @ApiModelProperty(value = "报案时间开始时间")
  private String reportDateStart;
  @ApiModelProperty(value = "报案时间结束时间")
  private String reportDateEnd;
  @ApiModelProperty(value = "下次跟进开始时间")
  private String nextFollowDateStart;
  @ApiModelProperty(value = "下次跟进结束时间")
  private String nextFollowDateEnd;

  @ApiModelProperty(value = "单/双方事故",name="doubleAccident")
  private Integer doubleAccident;
  
  //店端/厂端（vcdc）
  private String source;
  
  
  @ApiModelProperty(value = "售后大区ID")
  private Long    afterBigAreaId;

  @ApiModelProperty(value = "售后大区名称")
  private String  afterBigAreaName;

  @ApiModelProperty(value = "售后小区ID")
  private Long    afterSmallAreaId;

  @ApiModelProperty(value = "售后小区名称")
  private String  afterSmallAreaName;
	
  @ApiModelProperty(value = "分配状态",name="allotStatus")	
  private Integer allotStatus;
  
  private String isself;

  private Integer oneId;

  private Integer currentPage;

  private Integer pageSize;

  @ApiModelProperty("跟进状态集合")
  private List<Integer> followStatusList;

  private String createdDateStart;

  private String createdDateEnd;

  @ApiModelProperty("线索来源（多选）")
  private List<Integer> cluesResourceList;

  @ApiModelProperty("VIN")
  private String vin;

  @ApiModelProperty(value = "保司来源(来源渠道)")
  private String insuranceSource;

  @ApiModelProperty(value = "线索异常状态")
  private Integer dataStatus;

  @ApiModelProperty(value = "省ID")
  private Long    provinceId;

  @ApiModelProperty(value = "市ID")
  private Long    cityId;
  @ApiModelProperty(value = "经销商CODE")
  private List<String>    dealerCodeList;
  @ApiModelProperty(value = "来源渠道", hidden = true)
  private List<String> insuranceSourceList;

  @Override
  public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
    return super.transDtoToPo(poClass);
  }
}
