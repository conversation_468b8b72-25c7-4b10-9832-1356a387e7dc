package com.yonyou.dmscus.customer.entity.po.invitationFollow;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("tt_clue_complete_log")
public class ClueCompleteLogPo extends BasePO<ClueCompleteLogPo> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 主键
     */
    private Integer id;

    @TableField("vin")
    private String vin;
    @TableField("ro_no")
    private String roNo;
    @TableField("dealer_code")
    private String dealerCode;
    @TableField("invite_id")
    private Long inviteId;
    /**
     * fg
     */
    @TableField("function_group")
    private String functionGroup;
    /**
     * 线索当前状态
     */
    @TableField("current_status")
    private Integer currentStatus;
    /**
     * 线索完成状态
     */
    @TableField("complete_status")
    private Integer completeStatus;

    /**
     * 删除标识（0-未删除，1-已删除）
     */
    private Integer isDeleted;
}
