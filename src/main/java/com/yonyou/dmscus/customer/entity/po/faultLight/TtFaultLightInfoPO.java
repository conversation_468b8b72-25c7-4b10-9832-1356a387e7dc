package com.yonyou.dmscus.customer.entity.po.faultLight;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("故障灯线索推送信息表")
@TableName("tt_fault_clue_push")
public class TtFaultLightInfoPO {

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @TableId(value = "id",type = IdType.INPUT)
    private Long id;

    /**
     * 原始线索id（湖仓id)
     */
    @ApiModelProperty("原始线索id（湖仓id)")
    @TableField("source_clue_id")
    private String sourceClueId;

    /**
     * 车辆vin码
     */
    @ApiModelProperty("车辆vin码")
    @TableField("vehicle_vin")
    private String vehicleVin;

    /**
     * 经销商code
     */
    @ApiModelProperty("经销商code")
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 经销商名称
     */
    @ApiModelProperty("经销商名称")
    @TableField("dealer_name")
    private String dealerName;

    /**
     * 是否推荐经销商
     */
    @ApiModelProperty("是否推荐经销商")
    @TableField("recommend_dealer_flag")
    private String recommendDealerFlag;

    /**
     * 线索收集时间
     */
    @ApiModelProperty("线索收集时间")
    @TableField("leads_receive_time")
    private String leadsReceiveTime;

    /**
     * 故障id
     */
    @ApiModelProperty("故障id")
    @TableField("warning_id")
    private String warningId;

    /**
     * 故障名
     */
    @ApiModelProperty("故障名")
    @TableField("warning_name")
    private String warningName;

    /**
     * 故障描述（英文）
     */
    @ApiModelProperty("故障描述（英文）")
    @TableField("warning_en")
    private String warningEn;

    /**
     * 故障描述（中文）
     */
    @ApiModelProperty("故障描述（中文）")
    @TableField("warning_cn")
    private String warningCn;

    /**
     * 故障等级
     */
    @ApiModelProperty("故障等级")
    @TableField("warning_priority")
    private String warningPriority;

    /**
     * 中文故障分类
     */
    @ApiModelProperty("中文故障分类")
    @TableField("warining_category")
    private String wariningCategory;

    /**
     * 故障发生时间
     */
    @ApiModelProperty("故障发生时间")
    @TableField("warning_time")
    private String warningTime;

    /**
     * 故障发生省份（中文）
     */
    @ApiModelProperty("故障发生省份（中文）")
    @TableField("warning_province_cn")
    private String warningProvinceCn;

    /**
     * 故障发生省份（province_id）
     */
    @ApiModelProperty("故障发生省份（province_id）")
    @TableField("warning_province_id")
    private String warningProvinceId;

    /**
     * 故障发生城市（中文）
     */
    @ApiModelProperty("故障发生城市（中文）")
    @TableField("warning_city_cn")
    private String warningCityCn;

    /**
     * 故障发生城市（city_id）
     */
    @ApiModelProperty("故障发生城市（city_id）")
    @TableField("warning_city_id")
    private String warningCityId;

    /**
     * 客户类型(车主、绑车车主、售后客档、开票车主）
     */
    @ApiModelProperty("客户类型(车主、绑车车主、售后客档、开票车主）")
    @TableField("type_name")
    private String typeName;

    /**
     * 客户类型编号
     */
    @ApiModelProperty("客户类型编号")
    @TableField("type_code")
    private String typeCode;

    /**
     * 客户姓名
     */
    @ApiModelProperty("客户姓名")
    @TableField("customer_name")
    private String customerName;

    /**
     * 客户手机号
     */
    @ApiModelProperty("客户手机号")
    @TableField("customer_mobile")
    private String customerMobile;

    /**
     * 客户性别
     */
    @ApiModelProperty("客户性别")
    @TableField("gender")
    private String gender;

    /**
     * 拨打状态
     */
    @ApiModelProperty("拨打状态")
    @TableField("status")
    private String status;

    /**
     * 坐席联系时间
     */
    @ApiModelProperty("坐席联系时间")
    @TableField("call_end_time")
    private String callEndTime;

    /**
     * 坐席姓名
     */
    @ApiModelProperty("坐席姓名")
    @TableField("first_call_seat")
    private String firstCallSeat;

    /**
     * 拨打状态
     */
    @ApiModelProperty("拨打状态")
    @TableField("first_call_status")
    private String firstCallStatus;

    /**
     * 通话结果
     */
    @ApiModelProperty("通话结果")
    @TableField("call_result")
    private String callResult;

    /**
     * 外呼备注
     */
    @ApiModelProperty("外呼备注")
    @TableField("comments")
    private String comments;
}
