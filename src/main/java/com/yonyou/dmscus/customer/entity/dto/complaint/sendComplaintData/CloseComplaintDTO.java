package com.yonyou.dmscus.customer.entity.dto.complaint.sendComplaintData;

import lombok.Data;

/**
 * 发送结案信息(400接口)
 */
@Data
public class CloseComplaintDTO {
    /**
     *投诉ID
     */
    private  String ComplainID;

    /**
     * 数据来源
     */
    private  String Source;

    /**
     * 数据来源
     */
    private  String nature;

    /**
     * 结案状态
     */
    private  String CaseStatus;

    /**
     * 经销商申请结案时间
     */
    private  String  CaseDoneApplyDT;

    /**
     * 区域审核通过时间
     */
    private  String RegionApplyDT;

    /**
     * 总部审核通过时间
     */
    private  String HeadApplyDT;

    /**
     *是否有舆情风险
     */
    private  String RiskFlag;

    /**
     * 结案时间
     */
    private  String CaseDoneDT;

    /**
     * 是否回访
     */
    private  String VisitNeed;

    /**
     * 是否回访
     */
    private  String vistedNeed;

    /**
     * 投诉的根本原因
     */
    private  String BasicReason;

    /**
     * 车辆是否修复
     */
    private  String RepairFlag;

    /**
     * 潜在风险
     */
    private  String PotentialRisk;

    /**
     *填写人的角色
     */

    private  String Creater;

    /**
     * 填写人的姓名
     */
    private  String CreaterName;

    /**
     * 填写人的组织
     */
    private  String CreaterOrg;
    /**
     * 类型
     */
    private String complainType;
    /**
     * 是否满意
     */
    private String caseStatusFlag;
    /**
     * 业务场景
     */
    private String scenctype;
    /**
     * 厂端描述
     */
    private String follContent;

}
