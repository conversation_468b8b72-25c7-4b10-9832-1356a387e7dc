package com.yonyou.dmscus.customer.entity.po.invitationFollow;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 车辆邀约记录明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
@Data
@TableName("tt_invite_vehicle_record_detail")
public class InviteVehicleRecordDetailPO extends BasePO<InviteVehicleRecordDetailPO> {

    private static final long serialVersionUID = 1L;


    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;


    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 邀约ID
     */
    @TableField("invite_id")
    private Long inviteId;

    /**
     * 跟进内容
     */
    @TableField("content")
    private String content;

    /**
     * 客户反馈
     */
    @TableField("feedback")
    private String feedback;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 跟进状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 失败原因
     */
    @TableField("lose_reason")
    private Integer loseReason;

    /**
     * 不需跟进原因
     */
    @TableField("not_follow_reason")
    private Integer notFollowReason;

    /**
     * 下次跟进日期
     */
    @TableField("plan_date")
    private Date planDate;

    /**
     * 实际跟进日期
     */
    @TableField("actual_date")
    private Date actualDate;

    /**
     * 跟进方式
     */
    @TableField("mode")
    private Integer mode;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;

    /**
     * 跟进服务顾问ID
     */
    @TableField("SA_ID")
    private String saId;

    /**
     * 跟进服务顾问姓名
     */
    @TableField("SA_NAME")
    private String saName;


    /**
     *经销商代码
     */
    @TableField("dealer_code")
    private String dealerCode;


    /**
     * call_id
     */
    @TableField("call_id")
    private String callId;

    /**
     *车架号
     */
    @TableField("vin")
    private String vin;

    /**
     * 邀约类型
     */
    @TableField("invite_type")
    private Integer inviteType;

    /**
     * 建议进厂日期
     */
    @TableField(exist = false)
    private Date adviseInDate;

    /**
     * 新建议进厂日期
     */
    @TableField(exist = false)
    private Date newAdviseInDate;




    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
