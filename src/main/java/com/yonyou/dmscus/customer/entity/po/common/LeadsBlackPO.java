package com.yonyou.dmscus.customer.entity.po.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * description 线索黑名单
 *
 * <AUTHOR>
 * @date 2024/12/05 16:03
 */
@Data
@TableName("tm_leads_black")
public class LeadsBlackPO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * vin
     */
    @TableField("vin")
    private String vin;

    /**
     * 线索类型
     */
    @TableField(value = "leads_type")
    private String leadsType;

    /**
     * 是否删除:1,删除；0,未删除
     */
    @TableField(value = "is_deleted")
    private int isDeleted;
}
