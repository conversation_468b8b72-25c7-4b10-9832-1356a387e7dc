package com.yonyou.dmscus.customer.entity.dto.goodwill;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 亲善发票抬头信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-17
 */
    
public class GoodwillInvoiceTitleInfoDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

        
    /**
     * 系统ID
     */
                private String appId;
                
    /**
     * 所有者代码
     */
                private String ownerCode;
                
    /**
     * 所有者的父组织代码
     */
                private String ownerParCode;
                
    /**
     * 组织ID
     */
                private Integer orgId;
                
    /**
     * 主键id
     */
                private Long id;
                
    /**
     * 开票抬头
     */
                private Integer invoiceTitle;
                
    /**
     * 名称
     */
                private String name;
                
    /**
     * 纳税人识别号
     */
                private String taxpayerIdentificationNumber;
                
    /**
     * 电话
     */
                private String phone;
                
    /**
     * 地址
     */
                private String address;
                
    /**
     * 开户行
     */
                private String openBank;
                
    /**
     * 账号
     */
                private String account;
                
    /**
     * 是否有效
     */
                private Integer isValid;
                
    /**
     * 是否删除:1,删除；0,未删除
     */
                private Boolean isDeleted;
                
    /**
     * 创建时间
     */
            
    private Date createdAt;
                
    /**
     * 修改时间
     */
    
    private Date updatedAt;
            
    public GoodwillInvoiceTitleInfoDTO() {
        super();
    }

                                
    public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
            }
                                
    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
            }
                                
    public String getOwnerParCode(){
        return ownerParCode;
    }


    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
            }
                                
    public Integer getOrgId(){
        return orgId;
    }


    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
            }
                                            
    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
            }
                                
    
                                
    public Integer getInvoiceTitle() {
		return invoiceTitle;
	}


	public void setInvoiceTitle(Integer invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}


	public String getName(){
        return name;
    }


    public void  setName(String name) {
        this.name = name;
            }
                                
    public String getTaxpayerIdentificationNumber(){
        return taxpayerIdentificationNumber;
    }


    public void  setTaxpayerIdentificationNumber(String taxpayerIdentificationNumber) {
        this.taxpayerIdentificationNumber = taxpayerIdentificationNumber;
            }
                                
    public String getPhone(){
        return phone;
    }


    public void  setPhone(String phone) {
        this.phone = phone;
            }
                                
    public String getAddress(){
        return address;
    }


    public void  setAddress(String address) {
        this.address = address;
            }
                                
    public String getOpenBank(){
        return openBank;
    }


    public void  setOpenBank(String openBank) {
        this.openBank = openBank;
            }
                                
    public String getAccount(){
        return account;
    }


    public void  setAccount(String account) {
        this.account = account;
            }
                                
    public Integer getIsValid(){
        return isValid;
    }


    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
            }
                                
   
    
    public Date getCreatedAt() {
		return createdAt;
	}


	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}


	public Date getUpdatedAt() {
		return updatedAt;
	}


	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}


	@Override
    public String toString() {
        return "GoodwillInvoiceTitleInfoDTO{" +
                                            "appId=" + appId +
                                                            ", ownerCode=" + ownerCode +
                                                            ", ownerParCode=" + ownerParCode +
                                                            ", orgId=" + orgId +
                                                            ", id=" + id +
                                                            ", invoiceTitle=" + invoiceTitle +
                                                            ", name=" + name +
                                                            ", taxpayerIdentificationNumber=" + taxpayerIdentificationNumber +
                                                            ", phone=" + phone +
                                                            ", address=" + address +
                                                            ", openBank=" + openBank +
                                                            ", account=" + account +
                                                            ", isValid=" + isValid +
                                                            ", isDeleted=" + isDeleted +
                                                            ", createdAt=" + createdAt +
                                                            ", updatedAt=" + updatedAt +
                                    "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
