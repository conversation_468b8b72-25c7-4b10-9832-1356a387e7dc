package com.yonyou.dmscus.customer.entity.po.complaint.sale;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
    import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 销售客户投诉跟进表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-11
 */
@TableName("tt_sale_complaint_follow")
public class SaleComplaintFollowPO extends BasePO<SaleComplaintFollowPO> {

    private static final long serialVersionUID=1L;
    
    /**
     * 系统ID
     */
    @TableField("app_id")
        private String appId;
    
    /**
     * 所有者代码 
     */
    @TableField("owner_code")
        private String ownerCode;
    
    /**
     * 所有者的父组织代码 
     */
    @TableField("owner_par_code")
        private String ownerParCode;
    
    /**
     * 组织ID
     */
    @TableField("org_id")
        private Integer orgId;
        
    /**
     * 主键ID
     */
            @TableId(value = "id", type = IdType.AUTO)
                        private Long id;
    
    /**
     * 投诉信息表主键ID
     */
    @TableField("complaint_info_id")
        private Long complaintInfoId;
    
    /**
     * 跟进对象 区域经理、经销商、CCM、客服中心
     */
    @TableField("object")
        private String object;
    
    /**
     * 填写时间
     */
    @TableField("follow_time")
        private Date followTime;
    
    /**
     * 填写人
     */
    @TableField("follower")
        private String follower;
    
    /**
     * 跟进内容
     */
    @TableField("follow_content")
        private String followContent;
    
    /**
     * CCM跟进内容是否全网发布 1 公开 0 不公开
     */
    @TableField("ccm_not_publish")
        private Boolean ccmNotPublish;
    
    /**
     * CCM主题
     */
    @TableField("ccm_subject")
        private String ccmSubject;
    
    /**
     * 最新状态
     */
    @TableField("status")
        private String status;
    
    /**
     * 防止再发建议
     */
    @TableField("advise")
        private Integer advise;
    
    /**
     * CCM部位 多选用逗号分隔
     */
    @TableField("ccm_part")
        private String ccmPart;
    
    /**
     * CCM细分部位 多选用逗号分隔
     */
    @TableField("ccm_subdivision_part")
        private String ccmSubdivisionPart;
    
    /**
     * CCM主要原因 多选用逗号分隔
     */
    @TableField("cc_main_reason")
        private String ccMainReason;
    
    /**
     * CC解决结果 多选用逗号分隔
     */
    @TableField("cc_result")
        private String ccResult;
    
    /**
     * 关键字
     */
    @TableField("keyword")
        private String keyword;
    
    /**
     * 分类1
     */
    @TableField("classification1")
        private Integer classification1;
    
    /**
     * 分类2
     */
    @TableField("classification2")
        private Integer classification2;
        @TableField("classification3")
        private String classification3;
    
    /**
     * 分类4
     */
    @TableField("classification4")
        private Integer classification4;
        @TableField("classification5")
        private String classification5;
        @TableField("classification6")
        private String classification6;
    
    /**
     * 下次跟进时间
     */
    @TableField("plan_follow_time")
        private Date planFollowTime;
    
    /**
     * 实际跟进时间
     */
    @TableField("actuall_follow_time2")
        private Date actuallFollowTime2;
    
    /**
     * 数据来源 
     */
    @TableField("data_sources")
        private Integer dataSources;
    
    /**
     * 是否删除
     */
    @TableField("is_deleted")
        private Boolean isDeleted;
    
    /**
     * 是否有效 
     */
    @TableField("is_valid")
        private Integer isValid;
    
    /**
     * 创建时间
     */
    @TableField("created_at")
        private Date createdAt;
    
    /**
     * 更新时间
     */
    @TableField("updated_at")
        private Date updatedAt;
    
    /**
     * 质量部分类1
     */
    @TableField("quality_classification1")
        private Integer qualityClassification1;
    
    /**
     * 质量部分类2
     */
    @TableField("quality_classification2")
        private Integer qualityClassification2;
    
    /**
     * 质量部分类3
     */
    @TableField("quality_classification3")
        private String qualityClassification3;
    
    /**
     * 质量部分类4
     */
    @TableField("quality_classification4")
        private Integer qualityClassification4;
    
    /**
     * 故障分类
     */
    @TableField("fault_classification")
        private String faultClassification;
    
    /**
     * 备注1
     */
    @TableField("remark1")
        private String remark1;
    
    /**
     * 备注2
     */
    @TableField("remark2")
        private String remark2;
    
    /**
     * 备注3
     */
    @TableField("remark3")
        private String remark3;
    
    /**
     * 备注4
     */
    @TableField("remark4")
        private String remark4;
    
    /**
     * 是否CCM人员填写
     */
    @TableField("is_ccm")
        private Boolean isCcm;
    
    /**
     * 技术与维修方案
     */
    @TableField("regional_manager_comments")
        private String regionalManagerComments;
    
    /**
     * 填写人姓名
     */
    @TableField("follower_name")
        private String followerName;
    
    /**
     * 经销商跟进内容是否全网发布 1 公开 0 不公开
     */
    @TableField("dealer_not_publish")
        private Boolean dealerNotPublish;
    
    /**
     * 接口推送类型
     */
    @TableField("interface_push_type")
        private Integer interfacePushType;

    public SaleComplaintFollowPO(){
        super();
    }

                    
    public String getAppId(){
        return appId;
    }

        public void setAppId(String appId) {
            this.appId = appId;
            }
                    
    public String getOwnerCode(){
        return ownerCode;
    }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
            }
                    
    public String getOwnerParCode(){
        return ownerParCode;
    }

        public void setOwnerParCode(String ownerParCode) {
            this.ownerParCode = ownerParCode;
            }
                    
    public Integer getOrgId(){
        return orgId;
    }

        public void setOrgId(Integer orgId) {
            this.orgId = orgId;
            }
                    
    public Long getId(){
        return id;
    }

        public void setId(Long id) {
            this.id = id;
            }
                    
    public Long getComplaintInfoId(){
        return complaintInfoId;
    }

        public void setComplaintInfoId(Long complaintInfoId) {
            this.complaintInfoId = complaintInfoId;
            }
                    
    public String getObject(){
        return object;
    }

        public void setObject(String object) {
            this.object = object;
            }
                    
    public Date getFollowTime(){
        return followTime;
    }

        public void setFollowTime(Date followTime) {
            this.followTime = followTime;
            }
                    
    public String getFollower(){
        return follower;
    }

        public void setFollower(String follower) {
            this.follower = follower;
            }
                    
    public String getFollowContent(){
        return followContent;
    }

        public void setFollowContent(String followContent) {
            this.followContent = followContent;
            }
                    
    public Boolean getIsCcmNotPublish(){
        return ccmNotPublish;
    }

        public void setIsCcmNotPublish(Boolean ccmNotPublish) {
            this.ccmNotPublish = ccmNotPublish;
            }
                    
    public String getCcmSubject(){
        return ccmSubject;
    }

        public void setCcmSubject(String ccmSubject) {
            this.ccmSubject = ccmSubject;
            }
                    
    public String getStatus(){
        return status;
    }

        public void setStatus(String status) {
            this.status = status;
            }
                    
    public Integer getAdvise(){
        return advise;
    }

        public void setAdvise(Integer advise) {
            this.advise = advise;
            }
                    
    public String getCcmPart(){
        return ccmPart;
    }

        public void setCcmPart(String ccmPart) {
            this.ccmPart = ccmPart;
            }
                    
    public String getCcmSubdivisionPart(){
        return ccmSubdivisionPart;
    }

        public void setCcmSubdivisionPart(String ccmSubdivisionPart) {
            this.ccmSubdivisionPart = ccmSubdivisionPart;
            }
                    
    public String getCcMainReason(){
        return ccMainReason;
    }

        public void setCcMainReason(String ccMainReason) {
            this.ccMainReason = ccMainReason;
            }
                    
    public String getCcResult(){
        return ccResult;
    }

        public void setCcResult(String ccResult) {
            this.ccResult = ccResult;
            }
                    
    public String getKeyword(){
        return keyword;
    }

        public void setKeyword(String keyword) {
            this.keyword = keyword;
            }
                    
    public Integer getClassification1(){
        return classification1;
    }

        public void setClassification1(Integer classification1) {
            this.classification1 = classification1;
            }
                    
    public Integer getClassification2(){
        return classification2;
    }

        public void setClassification2(Integer classification2) {
            this.classification2 = classification2;
            }
                    
    public String getClassification3(){
        return classification3;
    }

        public void setClassification3(String classification3) {
            this.classification3 = classification3;
            }
                    
    public Integer getClassification4(){
        return classification4;
    }

        public void setClassification4(Integer classification4) {
            this.classification4 = classification4;
            }
                    
    public String getClassification5(){
        return classification5;
    }

        public void setClassification5(String classification5) {
            this.classification5 = classification5;
            }
                    
    public String getClassification6(){
        return classification6;
    }

        public void setClassification6(String classification6) {
            this.classification6 = classification6;
            }
                    
    public Date getPlanFollowTime(){
        return planFollowTime;
    }

        public void setPlanFollowTime(Date planFollowTime) {
            this.planFollowTime = planFollowTime;
            }
                    
    public Date getActuallFollowTime2(){
        return actuallFollowTime2;
    }

        public void setActuallFollowTime2(Date actuallFollowTime2) {
            this.actuallFollowTime2 = actuallFollowTime2;
            }
                    
    public Integer getDataSources(){
        return dataSources;
    }

        public void setDataSources(Integer dataSources) {
            this.dataSources = dataSources;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }

        public void setIsDeleted(Boolean isDeleted) {
            this.isDeleted = isDeleted;
            }
                    
    public Integer getIsValid(){
        return isValid;
    }

        public void setIsValid(Integer isValid) {
            this.isValid = isValid;
            }
                    
    public Date getCreatedAt(){
        return createdAt;
    }

        public void setCreatedAt(Date createdAt) {
            this.createdAt = createdAt;
            }
                    
    public Date getUpdatedAt(){
        return updatedAt;
    }

        public void setUpdatedAt(Date updatedAt) {
            this.updatedAt = updatedAt;
            }
                    
    public Integer getQualityClassification1(){
        return qualityClassification1;
    }

        public void setQualityClassification1(Integer qualityClassification1) {
            this.qualityClassification1 = qualityClassification1;
            }
                    
    public Integer getQualityClassification2(){
        return qualityClassification2;
    }

        public void setQualityClassification2(Integer qualityClassification2) {
            this.qualityClassification2 = qualityClassification2;
            }
                    
    public String getQualityClassification3(){
        return qualityClassification3;
    }

        public void setQualityClassification3(String qualityClassification3) {
            this.qualityClassification3 = qualityClassification3;
            }
                    
    public Integer getQualityClassification4(){
        return qualityClassification4;
    }

        public void setQualityClassification4(Integer qualityClassification4) {
            this.qualityClassification4 = qualityClassification4;
            }
                    
    public String getFaultClassification(){
        return faultClassification;
    }

        public void setFaultClassification(String faultClassification) {
            this.faultClassification = faultClassification;
            }
                    
    public String getRemark1(){
        return remark1;
    }

        public void setRemark1(String remark1) {
            this.remark1 = remark1;
            }
                    
    public String getRemark2(){
        return remark2;
    }

        public void setRemark2(String remark2) {
            this.remark2 = remark2;
            }
                    
    public String getRemark3(){
        return remark3;
    }

        public void setRemark3(String remark3) {
            this.remark3 = remark3;
            }
                    
    public String getRemark4(){
        return remark4;
    }

        public void setRemark4(String remark4) {
            this.remark4 = remark4;
            }
                    
    public Boolean getIsCcm(){
        return isCcm;
    }

        public void setIsCcm(Boolean isCcm) {
            this.isCcm = isCcm;
            }
                    
    public String getRegionalManagerComments(){
        return regionalManagerComments;
    }

        public void setRegionalManagerComments(String regionalManagerComments) {
            this.regionalManagerComments = regionalManagerComments;
            }
                    
    public String getFollowerName(){
        return followerName;
    }

        public void setFollowerName(String followerName) {
            this.followerName = followerName;
            }
                    
    public Boolean getIsDealerNotPublish(){
        return dealerNotPublish;
    }

        public void setIsDealerNotPublish(Boolean dealerNotPublish) {
            this.dealerNotPublish = dealerNotPublish;
            }
                    
    public Integer getInterfacePushType(){
        return interfacePushType;
    }

        public void setInterfacePushType(Integer interfacePushType) {
            this.interfacePushType = interfacePushType;
            }

    public Boolean getCcmNotPublish() {
        return ccmNotPublish;
    }

    public void setCcmNotPublish(Boolean ccmNotPublish) {
        this.ccmNotPublish = ccmNotPublish;
    }

    public Boolean getDeleted() {
        return isDeleted;
    }

    public void setDeleted(Boolean deleted) {
        isDeleted = deleted;
    }

    public Boolean getCcm() {
        return isCcm;
    }

    public void setCcm(Boolean ccm) {
        isCcm = ccm;
    }

    public Boolean getDealerNotPublish() {
        return dealerNotPublish;
    }

    public void setDealerNotPublish(Boolean dealerNotPublish) {
        this.dealerNotPublish = dealerNotPublish;
    }

    @Override
    protected Serializable pkVal(){
            return this.id;
        }

@Override
    public String toString(){
        return"SaleComplaintFollowPO{" +
                            "appId=" + appId +
                                    ", ownerCode=" + ownerCode +
                                    ", ownerParCode=" + ownerParCode +
                                    ", orgId=" + orgId +
                                    ", id=" + id +
                                    ", complaintInfoId=" + complaintInfoId +
                                    ", object=" + object +
                                    ", followTime=" + followTime +
                                    ", follower=" + follower +
                                    ", followContent=" + followContent +
                                    ", ccmNotPublish=" + ccmNotPublish +
                                    ", ccmSubject=" + ccmSubject +
                                    ", status=" + status +
                                    ", advise=" + advise +
                                    ", ccmPart=" + ccmPart +
                                    ", ccmSubdivisionPart=" + ccmSubdivisionPart +
                                    ", ccMainReason=" + ccMainReason +
                                    ", ccResult=" + ccResult +
                                    ", keyword=" + keyword +
                                    ", classification1=" + classification1 +
                                    ", classification2=" + classification2 +
                                    ", classification3=" + classification3 +
                                    ", classification4=" + classification4 +
                                    ", classification5=" + classification5 +
                                    ", classification6=" + classification6 +
                                    ", planFollowTime=" + planFollowTime +
                                    ", actuallFollowTime2=" + actuallFollowTime2 +
                                    ", dataSources=" + dataSources +
                                    ", isDeleted=" + isDeleted +
                                    ", isValid=" + isValid +
                                    ", createdAt=" + createdAt +
                                    ", updatedAt=" + updatedAt +
                                    ", qualityClassification1=" + qualityClassification1 +
                                    ", qualityClassification2=" + qualityClassification2 +
                                    ", qualityClassification3=" + qualityClassification3 +
                                    ", qualityClassification4=" + qualityClassification4 +
                                    ", faultClassification=" + faultClassification +
                                    ", remark1=" + remark1 +
                                    ", remark2=" + remark2 +
                                    ", remark3=" + remark3 +
                                    ", remark4=" + remark4 +
                                    ", isCcm=" + isCcm +
                                    ", regionalManagerComments=" + regionalManagerComments +
                                    ", followerName=" + followerName +
                                    ", dealerNotPublish=" + dealerNotPublish +
                                    ", interfacePushType=" + interfacePushType +
                    "}";
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"id");
    }

}
