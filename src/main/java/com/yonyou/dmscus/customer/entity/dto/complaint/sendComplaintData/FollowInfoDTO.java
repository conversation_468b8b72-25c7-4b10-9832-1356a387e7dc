package com.yonyou.dmscus.customer.entity.dto.complaint.sendComplaintData;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 下发400跟进信息
 */
@Data
public class FollowInfoDTO {

    /**
     * 投诉ID（投诉编号）
     */
    private String ComplainID;

    /**
     * 数据来源
     */
    private String Source;

    /**
     * 跟进时间
     */

    private String FollowDT;

    /**
     *跟进人的姓名
     */
    private String CreaterName;

    /**
     *跟进人的角色
     */
    private String Follower;

    /**
     * 跟进人的组织
     */
    private  String CreaterOrg;


    /**
     * 跟进内容
     */
    private String FollContent;

    /**
     * 工单状态
     */
    private String JobOrderStatus;
}
