package com.yonyou.dmscus.customer.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

/**
 * 日期节假日Vo
 */

@Data
@ToString
@ApiModel("日期节假日")
public class TmDateHolidayVo {

    @ApiModelProperty("节假日标识")
    private String holiday;

    @ApiModelProperty("上半月下半月")
    private String monthDay;

    @ApiModelProperty("日期")
    private LocalDate theTime;
}
