package com.yonyou.dmscus.customer.entity.dto.middleground;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscus.customer.middleInterface.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Arrays;
import java.util.List;
import java.util.Map;


@ApiModel(description = "App对象")
public class AppPushDTO extends BaseDTO {

    private static final long serialVersionUID = Constant.SERIAL_VERSION_UID;

    @ApiModelProperty(value = "推送目标列表，为空时广播")
    private String[] targetCodes;

    @ApiModelProperty(value = "用户列表，仅当发送站内信，且targetCodes为空时有效")
    private List<Integer> userIds;

    @ApiModelProperty(value =
            "推送目标类型。\n" +
                    "1.极光推送时，可选值all/tag/alias/registration_id\n" +
                    "2.阿里云推送时，可选值TAG/ALIAS/DEVICE"
    )
    private String targetType;

    @ApiModelProperty(value = "消息类型")
    private Long messageType;

    @ApiModelProperty(value = "重要提醒级别 -- 33081001: 一般, 33081002: 重要")
    private Long priority;

    @ApiModelProperty(value = "推送消息内容")
    private String content;

    @ApiModelProperty(value = "消息标签信息")
    private String title;

    @ApiModelProperty(value =
            "APP端消息展示模式\n" +
                    "notice-通知栏消息\n" +
                    "message-后台消息(即自定义消息)\n" +
                    "all-两者同时\n" +
                    "不存或为空时不发送"
    )
    private String mode;

    @ApiModelProperty(value = "消息点击触发内容")
    private String json;

    @ApiModelProperty(hidden = true)
    private String jobKey;

    @ApiModelProperty(value = "IOS环境: true-生产， false-开发， 默认false", example = "false")
    private Boolean prod;

    @ApiModelProperty(value = "通知的扩展属性")
    private Map<String, Map<String, String>> ext;

    private Object appId;

    private String event;

    public String[] getTargetCodes() {
        return targetCodes;
    }

    public void setTargetCodes(String[] targetCodes) {
        this.targetCodes = targetCodes;
    }

    public String getTargetType() {
        return targetType;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public Map<String, Map<String, String>> getExt() {
        return ext;
    }

    public void setExt(Map<String, Map<String, String>> ext) {
        this.ext = ext;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public String getJobKey() {
        return jobKey;
    }

    public void setJobKey(String jobKey) {
        this.jobKey = jobKey;
    }

    public Boolean getProd() {
        return prod;
    }

    public void setProd(Boolean prod) {
        this.prod = prod;
    }

    public List<Integer> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<Integer> userIds) {
        this.userIds = userIds;
    }

    public Long getMessageType() {
        return messageType;
    }

    public void setMessageType(Long messageType) {
        this.messageType = messageType;
    }

    public Long getPriority() {
        return priority;
    }

    public void setPriority(Long priority) {
        this.priority = priority;
    }

    public Object getAppId() {
        return appId;
    }

    public void setAppId(Object appId) {
        this.appId = appId;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    @Override
    public String toString() {
        return "AppPushDTO{" +
                "targetCodes=" + Arrays.toString(targetCodes) +
                ", userIds=" + userIds +
                ", targetType='" + targetType + '\'' +
                ", messageType=" + messageType +
                ", priority=" + priority +
                ", content='" + content + '\'' +
                ", title='" + title + '\'' +
                ", mode='" + mode + '\'' +
                ", json='" + json + '\'' +
                ", jobKey='" + jobKey + '\'' +
                ", prod=" + prod +
                ", ext=" + ext +
                ", appId=" + appId +
                ", event='" + event + '\'' +
                '}';
    }
}
