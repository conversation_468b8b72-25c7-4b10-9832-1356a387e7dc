package com.yonyou.dmscus.customer.entity.po.complaint;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

    import com.yonyou.dmscloud.framework.base.po.BasePO;

    
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;

import java.util.Date;

/**
 * <p>
 * 客户投诉KP基础规则使用表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
@TableName("tt_complaint_kpi_base_rule_use")
public class ComplaintKpiBaseRuleUsePO extends BasePO<ComplaintKpiBaseRuleUsePO> {

    private static final long serialVersionUID=1L;
    
    /**
     * 系统ID
     */
    @TableField("app_id")
        private String appId;
    
    /**
     * 所有者代码 
     */
    @TableField("owner_code")
        private String ownerCode;
    
    /**
     * 所有者的父组织代码 
     */
    @TableField("owner_par_code")
        private String ownerParCode;
    
    /**
     * 组织ID
     */
    @TableField("org_id")
        private Integer orgId;
        
    /**
     * 主键ID
     */
            @TableId(value = "id", type = IdType.AUTO)
                        private Long id;
    
    /**
     * 规则ID
     */
    @TableField("rule_id")
        private Long ruleId;
    
    /**
     * 进销商代码 厂端用VCDC
     */
    @TableField("dealer_code")
        private String dealerCode;
    
    /**
     * 使用人，主要用于VCDC人员。 进销商不到具体使用人
     */
    @TableField("user")
        private String user;
    
    /**
     * 警戒值
     */
    @TableField("warn_value")
        private String warnValue;
    
    /**
     * 数据来源 
     */
    @TableField("data_sources")
        private Integer dataSources;
    
    /**
     * 是否删除
     */
    @TableField("is_deleted")
        private Boolean isDeleted;
    
    /**
     * 是否有效 
     */
    @TableField("is_valid")
        private Integer isValid;
    
    /**
     * 创建时间
     */
    @TableField("created_at")
        private Date createdAt;
    
    /**
     * 更新时间
     */
    @TableField("updated_at")
        private Date updatedAt;

    public ComplaintKpiBaseRuleUsePO(){
        super();
    }

                    

                    
    public String getOwnerCode(){
        return ownerCode;
    }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
            }
                    

                    
    public Integer getOrgId(){
        return orgId;
    }

        public void setOrgId(Integer orgId) {
            this.orgId = orgId;
            }
                    
    public Long getId(){
        return id;
    }

        public void setId(Long id) {
            this.id = id;
            }
                    
    public Long getRuleId(){
        return ruleId;
    }

        public void setRuleId(Long ruleId) {
            this.ruleId = ruleId;
            }
                    
    public String getDealerCode(){
        return dealerCode;
    }

        public void setDealerCode(String dealerCode) {
            this.dealerCode = dealerCode;
            }
                    
    public String getUser(){
        return user;
    }

        public void setUser(String user) {
            this.user = user;
            }
                    
    public String getWarnValue(){
        return warnValue;
    }

        public void setWarnValue(String warnValue) {
            this.warnValue = warnValue;
            }
                    
    public Integer getDataSources(){
        return dataSources;
    }

        public void setDataSources(Integer dataSources) {
            this.dataSources = dataSources;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }

        public void setIsDeleted(Boolean isDeleted) {
            this.isDeleted = isDeleted;
            }
                    
    public Integer getIsValid(){
        return isValid;
    }

        public void setIsValid(Integer isValid) {
            this.isValid = isValid;
            }
                    

    
    @Override
    protected Serializable pkVal(){
            return this.id;
        }

@Override
    public String toString(){
        return"ComplaintKpiBaseRuleUsePO{" +
                            "appId=" + appId +
                                    ", ownerCode=" + ownerCode +
                                    ", ownerParCode=" + ownerParCode +
                                    ", orgId=" + orgId +
                                    ", id=" + id +
                                    ", ruleId=" + ruleId +
                                    ", dealerCode=" + dealerCode +
                                    ", user=" + user +
                                    ", warnValue=" + warnValue +
                                    ", dataSources=" + dataSources +
                                    ", isDeleted=" + isDeleted +
                                    ", isValid=" + isValid +
                                    ", createdAt=" + createdAt +
                                    ", updatedAt=" + updatedAt +
                    "}";
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"id");
    }

}
