package com.yonyou.dmscus.customer.entity.dto.businessPlatform;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscus.customer.middleInterface.Constant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 */
@ApiModel(value = "OwnerVehicleVO", description = "OwnerVehicleVO")
public class OwnerVehicleVO extends BaseDTO implements Serializable {

	private static final long serialVersionUID = Constant.SERIAL_VERSION_UID;

	/*** 车辆信息 */
	@ApiModelProperty(value = "id", required = true)
	private Long id;
	@ApiModelProperty(value = "客户信息表主键")
	private Long customerId;
	@ApiModelProperty(value = "公司代码")
	private String companyCode;
	@ApiModelProperty(value = "所有者的父组织代码（用于二网使用）")
	private String ownerParCode;
	@ApiModelProperty(value = "经销商代码   原产品OWNER_CODE")
	private String dealerCode;
	@ApiModelProperty(value = "VIN")
	private String vin;
	@ApiModelProperty(value = "发动机号")
	private String engineNo;
	@ApiModelProperty(value = "制造日期")
	private String productDate;
	@ApiModelProperty(value = "出厂日期")
	private String factoryDate;
	@ApiModelProperty(value = "品牌id")
	private Long brandId;
	@ApiModelProperty(value = "车系id")
	private Long seriesId;
	@ApiModelProperty(value = "车型id")
	private Long modelId;
	@ApiModelProperty(value = "配置id")
	private Long configId;
	@ApiModelProperty(value = "颜色")
	private Long colorId;
	@ApiModelProperty(value = "内饰ID")
	private Long trimId;
	private String productId;
	@ApiModelProperty(value = "里程")
	private Integer mileage;
	@ApiModelProperty(value = "开票时间（保修起始日期）")
	private String invoiceDate;
	@ApiModelProperty(value = "车牌号")
	private String plateNumber;
	@ApiModelProperty(value = "钥匙编号")
	private String keyNo;
	@ApiModelProperty(value = "产地")
	private String productingArea;
	@ApiModelProperty(value = "国Ⅲ、国Ⅳ、国Ⅲ+OBD、欧Ⅲ、欧Ⅳ、欧Ⅳ+OBD、国Ⅲ欧Ⅳ、国V")
	private Integer dischargeStandard;
	@ApiModelProperty(value = "排气量")
	private String exhaustQuantity;
	@ApiModelProperty(value = "备注")
	private String remark;
	@ApiModelProperty(value = "是否有合格证")
	private Integer hasCertificate;
	@ApiModelProperty(value = "合格证号")
	private String certificateNumber;
	@ApiModelProperty(value = "合格证存放地")
	private String certificateLocus;
	@ApiModelProperty(value = "是否")
	private Integer oemTag;
	@ApiModelProperty(value = "是，否")
	private Integer isDirect;
	@ApiModelProperty(value = "代收经销商姓名")
	private String collectingDealer;
	@ApiModelProperty(value = "仓库代码")
	private String storageId;
	@ApiModelProperty(value = "库位代码")
	private String storagePositionCode;
	@ApiModelProperty(value = "库存状态(1413)")
	private Integer ownStockStatus;
	@ApiModelProperty(value = "配车状态(1414)")
	private Integer dispatchedStatus;
	@ApiModelProperty(value = "是否锁定")
	private Integer isLock;
	@ApiModelProperty(value = "运损状态")
	private Integer trafficMarStatus;
	@ApiModelProperty(value = "是，否试驾车")
	private Integer isTestDrive;
	@ApiModelProperty(value = "入库类型(1317)")
	private Integer entryType;
	@ApiModelProperty(value = "首次入库日期")
	private String firstStockInDate;
	@ApiModelProperty(value = "最后入库日期")
	private String latestStockInDate;
	@ApiModelProperty(value = "最后入库人")
	private String lastStockInBy;
	@ApiModelProperty(value = "出库类型(1318)")
	private Integer deliveryType;
	@ApiModelProperty(value = "首次出库日期")
	private String firstStockOutDate;
	@ApiModelProperty(value = "最后出库日期")
	private String latestStockOutDate;
	@ApiModelProperty(value = "最后出库人")
	private String lastStockOutBy;
	@ApiModelProperty(value = "车辆配置代码")
	private String vsn;
	@ApiModelProperty(value = "含税采购价")
	private Double purchasePrice;
	@ApiModelProperty(value = "车厂销售指导价")
	private Double oemDirectivePrice;
	@ApiModelProperty(value = "销售指导价")
	private Double directivePrice;
	@ApiModelProperty(value = "批售指导价格")
	private Double wholesaleDirectivePrice;
	@ApiModelProperty(value = "采购日期")
	private String purchaseDate;
	@ApiModelProperty(value = "是否外借")
	private Integer isBorrowed;
	@ApiModelProperty(value = "保养手册号")
	private String warrantyManualNo;
	@ApiModelProperty(value = "数据来源的系统代码")
	private String sourceSystem;
	@ApiModelProperty(value = "APP_ID")
	private String appId;
	@ApiModelProperty(value = "保险开始日期")
	private String insuranceStartDate;
	@ApiModelProperty(value = "首选 经销商")
	private String firstDealer;
	@ApiModelProperty(value = "保险开始日期来源")
	private String insuranceStartDateSource;
	@ApiModelProperty(value = "保险开始日期页面是否可编辑")
	private Integer insuranceStartDatePageCanBeEdited;
	@ApiModelProperty(value = "车主上报销售日期")
	private String ownerReportedSalesDate;
	@ApiModelProperty(value = "品牌代码")
	private String brandCode;
	@ApiModelProperty(value = "品牌名称")
	private String brandName;
	@ApiModelProperty(value = "品牌英文名称")
	private String brandNameEn;
	@ApiModelProperty(value = "配置代码 车款代码")
	private String configCode;
	@ApiModelProperty(value = "配置名称 车款名称")
	private String configName;
	@ApiModelProperty(value = "配置名称英文名 车款英文名")
	private String configNameEn;
	@ApiModelProperty(value = "配置年份 年款")
	private String configYear;
	@ApiModelProperty(value = "变速箱")
	private String transMission;
	@ApiModelProperty(value = "水货车(0:否 1:是)")
	private Integer wwCar;
	@ApiModelProperty(value = "行驶证号")
	private String licenseNo;
	@ApiModelProperty(value = "上牌日期")
	private String licenseDate;
	@ApiModelProperty(value = "保修结束日期")
	private String invoiceEndDate;
	@ApiModelProperty(value = "车系名称")
	private String seriesName;
	@ApiModelProperty(value = "车型名称")
	private String modelName;
	@ApiModelProperty(value = "经销商名称")
	private String dealerName;
	/*** 车型信息 */
	@ApiModelProperty(value = "车型代码")
	private String modelCode;
	@ApiModelProperty(value = "车型英文名称")
	private String modelNameEn;
	@ApiModelProperty(value = "燃料类型(4106)")
	private Integer fuelType;
	@ApiModelProperty(value = "数据来源")
	private String dataSources;
	@ApiModelProperty(value = "是否有效")
	private Integer isValid;
	/*** 车主信息 */
	@ApiModelProperty(value = "客户唯一ID")
	private Long oneId;
	@ApiModelProperty(value = "潜客姓名")
	private String name;
	@ApiModelProperty(value = "手机")
	private String mobile;
	@ApiModelProperty(value = "联系人名称")
	private String contactName;
	@ApiModelProperty(value = "联系方式一")
	private String contactorMobile;
	@ApiModelProperty(value = "联系方式二 固定电话")
	private String contactorPhone;
	@ApiModelProperty(value = "QQ")
	private String qq;
	@ApiModelProperty(value = "所属行业(互联网/IT/电子/通信:70301002.广告/传媒/文化/体育:70301002.金融:70301003.教育培训:70301004.制药/医疗:70301005.交通/物流/贸易/零售:70301006.专业服务:70301007.房地产/建筑:70301008.汽车:70301009.机械/制造:70301010.消费品:70301011.服务业:70301012.能源/化工/环保:70301013.政府/非盈利机构/其他:70301014)")
	private Integer industry;
	@ApiModelProperty(value = "企业性质(机关:70291001,事业:70291002,企业:70291003,运营:70291004,公检司法:70291005,其它:70291006)")
	private Integer enterpriseType;
	@ApiModelProperty(value = "首次成交时间")
	private String bargainDate;
	@ApiModelProperty(value = "客户类型(企业:40451001,私人:40451002,政府:40451003,事业单位:40451004,租赁:40451005,车改:40451006,团购:40451007)")
	private Integer customerType;
	@ApiModelProperty(value = "性别(先生:10021001,女士:10021002,未知:10021003)")
	private Integer gender;
	@ApiModelProperty(value = "学历（小学:70271001,初中:70271002,高中/中专/技校:70271003,大专:70271004,本科:70271005,硕士及以上:70271006）")
	private Integer education;
	@ApiModelProperty(value = "职业(私营公司老板/自由职业者/个体户:15271001,公司高管:15271002,中层管理人员:15271003,销售人员/销售代表:15271004,私营公司职员/主管:15271005,家庭主妇/夫:15271006,退休:15271007,无工作:15271008,学生:15271009,技术工人:15271010,公务员/教师/警察:15271011,行政职员:15271012,专业人士:15271013,其他:15271014)")
	private Integer occupation;
	@ApiModelProperty(value = "生日")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date birthday;
	@ApiModelProperty(value = "证件类型(身份证:15081001,护照:15081002,军官证:15081003,士兵证:15081004,警察证:15081005,其他:15081006,机构代码:15081007)")
	private Integer ctCode;
	@ApiModelProperty(value = "证件号码")
	private String certificateNo;
	@ApiModelProperty(value = "邮箱")
	private String eMail;
	@ApiModelProperty(value = "地址")
	private String address;
	@ApiModelProperty(value = "邮编")
	private String zipCode;
	@ApiModelProperty(value = "婚姻状况(已婚:10361001,未婚:10361002,离异:10361003)")
	private Integer maritalStatus;
	@ApiModelProperty(value = "省份(1000)")
	private String province;
	@ApiModelProperty(value = "城市(1000)")
	private String city;
	@ApiModelProperty(value = "区县(1000)")
	private String district;
	@ApiModelProperty(value = "建档日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
	private Date createdDate;
	/*** 组织信息 */
	@ApiModelProperty(value = "经销商代码id")
	private Long companyId;
	@ApiModelProperty(value = "经销商名称中文")
	private String companyNameCn;
	@ApiModelProperty(value = "经销商简称中文")
	private String companyShortNameCn;
	@ApiModelProperty(value = "公司类型:1005")
	private Integer companyType;
	@ApiModelProperty(value = "集团")
	private String groupCompanyId;
	@ApiModelProperty(value = "集团名称")
	private String groupCompanyName;
	@ApiModelProperty(value = "组织id")
	private Long orgId;
	@ApiModelProperty(value = "省份")
	private Integer provinceId;
	@ApiModelProperty(value = "城市")
	private Integer cityId;
	@ApiModelProperty(value = "区域")
	private Integer countyId;
	@ApiModelProperty(value = "地址")
	private String addressZh;
	@ApiModelProperty(value = "经度")
	private double longitude;
	@ApiModelProperty(value = "纬度")
	private double latitude;
	@ApiModelProperty(value = "电话")
	private String phone;
	@ApiModelProperty(value = "传真")
	private String fax;
	@ApiModelProperty(value = "经销商类型")
	private Integer dealerType;
	@ApiModelProperty(value = "经销商规模")
	private Integer dealerScale;
	@ApiModelProperty(value = "是否批售授权经销商")
	private Integer wholesaleGrant;
	@ApiModelProperty(value = "状态")
	private Integer status;
	@ApiModelProperty(value = "销售热线")
	private String salesLine;
	@ApiModelProperty(value = "销售ParmaCode")
	private String salesParmaCode;
	@ApiModelProperty(value = "客户评分销售")
	private String gradeSales;
	@ApiModelProperty(value = "开户银行销售")
	private String bankSales;
	@ApiModelProperty(value = "开户账号销售")
	private String bankAccountSales;
	@ApiModelProperty(value = "税号销售")
	private String taxSales;
	@ApiModelProperty(value = "售后热线")
	private String afterLine;
	@ApiModelProperty(value = "售后ParmaCode")
	private String afterParmaCode;
	@ApiModelProperty(value = "客户评分售后")
	private String gradeAfter;
	@ApiModelProperty(value = "开户银行售后")
	private String bankAfter;
	@ApiModelProperty(value = "开户账号售后")
	private String bankAccountAfter;
	@ApiModelProperty(value = "税号售后")
	private String taxAfter;
	@ApiModelProperty(value = "FACILITY")
	private String facility;
	@ApiModelProperty(value = "DEALERSHIP_OUTLET")
	private String dealershipOutlet;
	@ApiModelProperty(value = "OPERATION_DATE_DN")
	private String operationDateDn;
	@ApiModelProperty(value = "OPERATION_DATE_RD")
	private String operationDateRd;
	@ApiModelProperty(value = "OPERATION_DATE_INTERNET")
	private String operationDateInterent;
	@ApiModelProperty(value = "RELOCATION_OR_UPGRADE")
	private String relocationOrUpgrade;
	@ApiModelProperty(value = "SHORT_DATE")
	private String shortDate;
	@ApiModelProperty(value = "VIPS_CODE")
	private String vipsCode;
	@ApiModelProperty(value = "VR_OOED")
	private String vrCode;
	@ApiModelProperty(value = "VMI_LDC")
	private Integer vmiLdc;
	@ApiModelProperty(value = "零配件仓库")
	private String partWarehouse;
	@ApiModelProperty(value = "仓库发货地址")
	private String warehouseAddress;
	@ApiModelProperty(value = "开店时间")
	private String openTime;
	@ApiModelProperty(value = "关店时间")
	private String closeTime;
	@ApiModelProperty(value = "经销商门头照片url")
	private String storefrontPhotoUrl;
	@ApiModelProperty(value = "售后大区ID")
	private Long afterBigAreaId;
	@ApiModelProperty(value = "售后大区名称")
	private String afterBigAreaName;
	@ApiModelProperty(value = "售后小区ID")
	private Long afterSmallAreaId;
	@ApiModelProperty(value = "售后小区名称")
	private String afterSmallAreaName;
	@ApiModelProperty(value = "销售大区ID")
	private Long saleBigAreaId;
	@ApiModelProperty(value = "销售大区名称")
	private String saleBigAreaName;
	@ApiModelProperty(value = "销售小区ID")
	private Long saleSmallAreaId;
	@ApiModelProperty(value = "销售小区名称")
	private String saleSmallAreaName;
	@ApiModelProperty(value = "微信号")
	private String weChat;
	@ApiModelProperty(value = "是否延保")
	private Integer isExtendWarranty;
	@ApiModelProperty(value = "延保名称")
	private String extendWarrantyName;
	@ApiModelProperty(value = "延保开始日期")
	private String extendWarrantyStartDate;
	@ApiModelProperty(value = "延保结束日期")
	private String extendWarrantyEndDate;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Long customerId) {
		this.customerId = customerId;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getOwnerParCode() {
		return ownerParCode;
	}

	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public String getDealerCode() {
		return dealerCode;
	}

	public void setDealerCode(String dealerCode) {
		this.dealerCode = dealerCode;
	}

	public String getVin() {
		return vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public String getEngineNo() {
		return engineNo;
	}

	public void setEngineNo(String engineNo) {
		this.engineNo = engineNo;
	}

	public String getFactoryDate() {
		return factoryDate;
	}

	public void setFactoryDate(String factoryDate) {
		this.factoryDate = factoryDate;
	}

	public Long getBrandId() {
		return brandId;
	}

	public void setBrandId(Long brandId) {
		this.brandId = brandId;
	}

	public Long getSeriesId() {
		return seriesId;
	}

	public void setSeriesId(Long seriesId) {
		this.seriesId = seriesId;
	}

	public Long getModelId() {
		return modelId;
	}

	public void setModelId(Long modelId) {
		this.modelId = modelId;
	}

	public Long getConfigId() {
		return configId;
	}

	public void setConfigId(Long configId) {
		this.configId = configId;
	}

	public Long getColorId() {
		return colorId;
	}

	public void setColorId(Long colorId) {
		this.colorId = colorId;
	}

	public Long getTrimId() {
		return trimId;
	}

	public void setTrimId(Long trimId) {
		this.trimId = trimId;
	}

	public String getProductId() {
		return productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}

	public Integer getMileage() {
		return mileage;
	}

	public void setMileage(Integer mileage) {
		this.mileage = mileage;
	}

	public String getInvoiceDate() {
		return invoiceDate;
	}

	public void setInvoiceDate(String invoiceDate) {
		this.invoiceDate = invoiceDate;
	}

	public String getPlateNumber() {
		return plateNumber;
	}

	public void setPlateNumber(String plateNumber) {
		this.plateNumber = plateNumber;
	}

	public String getKeyNo() {
		return keyNo;
	}

	public void setKeyNo(String keyNo) {
		this.keyNo = keyNo;
	}

	public String getProductingArea() {
		return productingArea;
	}

	public void setProductingArea(String productingArea) {
		this.productingArea = productingArea;
	}

	public Integer getDischargeStandard() {
		return dischargeStandard;
	}

	public void setDischargeStandard(Integer dischargeStandard) {
		this.dischargeStandard = dischargeStandard;
	}

	public String getExhaustQuantity() {
		return exhaustQuantity;
	}

	public void setExhaustQuantity(String exhaustQuantity) {
		this.exhaustQuantity = exhaustQuantity;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getHasCertificate() {
		return hasCertificate;
	}

	public void setHasCertificate(Integer hasCertificate) {
		this.hasCertificate = hasCertificate;
	}

	public String getCertificateNumber() {
		return certificateNumber;
	}

	public void setCertificateNumber(String certificateNumber) {
		this.certificateNumber = certificateNumber;
	}

	public String getCertificateLocus() {
		return certificateLocus;
	}

	public void setCertificateLocus(String certificateLocus) {
		this.certificateLocus = certificateLocus;
	}

	public Integer getOemTag() {
		return oemTag;
	}

	public void setOemTag(Integer oemTag) {
		this.oemTag = oemTag;
	}

	public Integer getIsDirect() {
		return isDirect;
	}

	public void setIsDirect(Integer isDirect) {
		this.isDirect = isDirect;
	}

	public String getCollectingDealer() {
		return collectingDealer;
	}

	public void setCollectingDealer(String collectingDealer) {
		this.collectingDealer = collectingDealer;
	}

	public String getStorageId() {
		return storageId;
	}

	public void setStorageId(String storageId) {
		this.storageId = storageId;
	}

	public String getStoragePositionCode() {
		return storagePositionCode;
	}

	public void setStoragePositionCode(String storagePositionCode) {
		this.storagePositionCode = storagePositionCode;
	}

	public Integer getOwnStockStatus() {
		return ownStockStatus;
	}

	public void setOwnStockStatus(Integer ownStockStatus) {
		this.ownStockStatus = ownStockStatus;
	}

	public Integer getDispatchedStatus() {
		return dispatchedStatus;
	}

	public void setDispatchedStatus(Integer dispatchedStatus) {
		this.dispatchedStatus = dispatchedStatus;
	}

	public Integer getIsLock() {
		return isLock;
	}

	public void setIsLock(Integer isLock) {
		this.isLock = isLock;
	}

	public Integer getTrafficMarStatus() {
		return trafficMarStatus;
	}

	public void setTrafficMarStatus(Integer trafficMarStatus) {
		this.trafficMarStatus = trafficMarStatus;
	}

	public Integer getIsTestDrive() {
		return isTestDrive;
	}

	public void setIsTestDrive(Integer isTestDrive) {
		this.isTestDrive = isTestDrive;
	}

	public Integer getEntryType() {
		return entryType;
	}

	public void setEntryType(Integer entryType) {
		this.entryType = entryType;
	}

	public String getFirstStockInDate() {
		return firstStockInDate;
	}

	public void setFirstStockInDate(String firstStockInDate) {
		this.firstStockInDate = firstStockInDate;
	}

	public String getLastStockInBy() {
		return lastStockInBy;
	}

	public void setLastStockInBy(String lastStockInBy) {
		this.lastStockInBy = lastStockInBy;
	}

	public Integer getDeliveryType() {
		return deliveryType;
	}

	public void setDeliveryType(Integer deliveryType) {
		this.deliveryType = deliveryType;
	}

	public String getLastStockOutBy() {
		return lastStockOutBy;
	}

	public void setLastStockOutBy(String lastStockOutBy) {
		this.lastStockOutBy = lastStockOutBy;
	}

	public String getVsn() {
		return vsn;
	}

	public void setVsn(String vsn) {
		this.vsn = vsn;
	}

	public Double getPurchasePrice() {
		return purchasePrice;
	}

	public void setPurchasePrice(Double purchasePrice) {
		this.purchasePrice = purchasePrice;
	}

	public Double getOemDirectivePrice() {
		return oemDirectivePrice;
	}

	public void setOemDirectivePrice(Double oemDirectivePrice) {
		this.oemDirectivePrice = oemDirectivePrice;
	}

	public Double getDirectivePrice() {
		return directivePrice;
	}

	public void setDirectivePrice(Double directivePrice) {
		this.directivePrice = directivePrice;
	}

	public Double getWholesaleDirectivePrice() {
		return wholesaleDirectivePrice;
	}

	public void setWholesaleDirectivePrice(Double wholesaleDirectivePrice) {
		this.wholesaleDirectivePrice = wholesaleDirectivePrice;
	}

	public Integer getIsBorrowed() {
		return isBorrowed;
	}

	public void setIsBorrowed(Integer isBorrowed) {
		this.isBorrowed = isBorrowed;
	}

	public String getWarrantyManualNo() {
		return warrantyManualNo;
	}

	public void setWarrantyManualNo(String warrantyManualNo) {
		this.warrantyManualNo = warrantyManualNo;
	}

	public String getSourceSystem() {
		return sourceSystem;
	}

	public void setSourceSystem(String sourceSystem) {
		this.sourceSystem = sourceSystem;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getFirstDealer() {
		return firstDealer;
	}

	public void setFirstDealer(String firstDealer) {
		this.firstDealer = firstDealer;
	}

	public String getInsuranceStartDateSource() {
		return insuranceStartDateSource;
	}

	public void setInsuranceStartDateSource(String insuranceStartDateSource) {
		this.insuranceStartDateSource = insuranceStartDateSource;
	}

	public Integer getInsuranceStartDatePageCanBeEdited() {
		return insuranceStartDatePageCanBeEdited;
	}

	public void setInsuranceStartDatePageCanBeEdited(Integer insuranceStartDatePageCanBeEdited) {
		this.insuranceStartDatePageCanBeEdited = insuranceStartDatePageCanBeEdited;
	}

	public String getBrandCode() {
		return brandCode;
	}

	public void setBrandCode(String brandCode) {
		this.brandCode = brandCode;
	}

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	public String getBrandNameEn() {
		return brandNameEn;
	}

	public void setBrandNameEn(String brandNameEn) {
		this.brandNameEn = brandNameEn;
	}

	public String getConfigCode() {
		return configCode;
	}

	public void setConfigCode(String configCode) {
		this.configCode = configCode;
	}

	public String getConfigName() {
		return configName;
	}

	public void setConfigName(String configName) {
		this.configName = configName;
	}

	public String getConfigNameEn() {
		return configNameEn;
	}

	public void setConfigNameEn(String configNameEn) {
		this.configNameEn = configNameEn;
	}

	public String getConfigYear() {
		return configYear;
	}

	public void setConfigYear(String configYear) {
		this.configYear = configYear;
	}

	public String getTransMission() {
		return transMission;
	}

	public void setTransMission(String transMission) {
		this.transMission = transMission;
	}

	public Integer getWwCar() {
		return wwCar;
	}

	public void setWwCar(Integer wwCar) {
		this.wwCar = wwCar;
	}

	public String getLicenseNo() {
		return licenseNo;
	}

	public void setLicenseNo(String licenseNo) {
		this.licenseNo = licenseNo;
	}

	public String getProductDate() {
		return productDate;
	}

	public void setProductDate(String productDate) {
		this.productDate = productDate;
	}

	public String getLicenseDate() {
		return licenseDate;
	}

	public void setLicenseDate(String licenseDate) {
		this.licenseDate = licenseDate;
	}

	public String getSeriesName() {
		return seriesName;
	}

	public void setSeriesName(String seriesName) {
		this.seriesName = seriesName;
	}

	public String getModelName() {
		return modelName;
	}

	public void setModelName(String modelName) {
		this.modelName = modelName;
	}

	public String getDealerName() {
		return dealerName;
	}

	public void setDealerName(String dealerName) {
		this.dealerName = dealerName;
	}

	public String getModelCode() {
		return modelCode;
	}

	public void setModelCode(String modelCode) {
		this.modelCode = modelCode;
	}

	public String getModelNameEn() {
		return modelNameEn;
	}

	public void setModelNameEn(String modelNameEn) {
		this.modelNameEn = modelNameEn;
	}

	public Integer getFuelType() {
		return fuelType;
	}

	public void setFuelType(Integer fuelType) {
		this.fuelType = fuelType;
	}

	public String getDataSources() {
		return dataSources;
	}

	public void setDataSources(String dataSources) {
		this.dataSources = dataSources;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Long getOneId() {
		return oneId;
	}

	public void setOneId(Long oneId) {
		this.oneId = oneId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getContactorMobile() {
		return contactorMobile;
	}

	public void setContactorMobile(String contactorMobile) {
		this.contactorMobile = contactorMobile;
	}

	public String getContactorPhone() {
		return contactorPhone;
	}

	public void setContactorPhone(String contactorPhone) {
		this.contactorPhone = contactorPhone;
	}

	public String getQq() {
		return qq;
	}

	public void setQq(String qq) {
		this.qq = qq;
	}

	public Integer getIndustry() {
		return industry;
	}

	public void setIndustry(Integer industry) {
		this.industry = industry;
	}

	public Integer getEnterpriseType() {
		return enterpriseType;
	}

	public void setEnterpriseType(Integer enterpriseType) {
		this.enterpriseType = enterpriseType;
	}

	public String getLatestStockInDate() {
		return latestStockInDate;
	}

	public void setLatestStockInDate(String latestStockInDate) {
		this.latestStockInDate = latestStockInDate;
	}

	public String getFirstStockOutDate() {
		return firstStockOutDate;
	}

	public void setFirstStockOutDate(String firstStockOutDate) {
		this.firstStockOutDate = firstStockOutDate;
	}

	public String getLatestStockOutDate() {
		return latestStockOutDate;
	}

	public void setLatestStockOutDate(String latestStockOutDate) {
		this.latestStockOutDate = latestStockOutDate;
	}

	public String getPurchaseDate() {
		return purchaseDate;
	}

	public void setPurchaseDate(String purchaseDate) {
		this.purchaseDate = purchaseDate;
	}

	public String getInsuranceStartDate() {
		return insuranceStartDate;
	}

	public void setInsuranceStartDate(String insuranceStartDate) {
		this.insuranceStartDate = insuranceStartDate;
	}

	public String getOwnerReportedSalesDate() {
		return ownerReportedSalesDate;
	}

	public void setOwnerReportedSalesDate(String ownerReportedSalesDate) {
		this.ownerReportedSalesDate = ownerReportedSalesDate;
	}

	public String getInvoiceEndDate() {
		return invoiceEndDate;
	}

	public void setInvoiceEndDate(String invoiceEndDate) {
		this.invoiceEndDate = invoiceEndDate;
	}

	public String getBargainDate() {
		return bargainDate;
	}

	public void setBargainDate(String bargainDate) {
		this.bargainDate = bargainDate;
	}

	public Integer getCustomerType() {
		return customerType;
	}

	public void setCustomerType(Integer customerType) {
		this.customerType = customerType;
	}

	public Integer getGender() {
		return gender;
	}

	public void setGender(Integer gender) {
		this.gender = gender;
	}

	public Integer getEducation() {
		return education;
	}

	public void setEducation(Integer education) {
		this.education = education;
	}

	public Integer getOccupation() {
		return occupation;
	}

	public void setOccupation(Integer occupation) {
		this.occupation = occupation;
	}

	public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}

	public Integer getCtCode() {
		return ctCode;
	}

	public void setCtCode(Integer ctCode) {
		this.ctCode = ctCode;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public String geteMail() {
		return eMail;
	}

	public void seteMail(String eMail) {
		this.eMail = eMail;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getZipCode() {
		return zipCode;
	}

	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}

	public Integer getMaritalStatus() {
		return maritalStatus;
	}

	public void setMaritalStatus(Integer maritalStatus) {
		this.maritalStatus = maritalStatus;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getDistrict() {
		return district;
	}

	public void setDistrict(String district) {
		this.district = district;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public String getCompanyNameCn() {
		return companyNameCn;
	}

	public void setCompanyNameCn(String companyNameCn) {
		this.companyNameCn = companyNameCn;
	}

	public String getCompanyShortNameCn() {
		return companyShortNameCn;
	}

	public void setCompanyShortNameCn(String companyShortNameCn) {
		this.companyShortNameCn = companyShortNameCn;
	}

	public Integer getCompanyType() {
		return companyType;
	}

	public void setCompanyType(Integer companyType) {
		this.companyType = companyType;
	}

	public String getGroupCompanyId() {
		return groupCompanyId;
	}

	public void setGroupCompanyId(String groupCompanyId) {
		this.groupCompanyId = groupCompanyId;
	}

	public String getGroupCompanyName() {
		return groupCompanyName;
	}

	public void setGroupCompanyName(String groupCompanyName) {
		this.groupCompanyName = groupCompanyName;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Integer getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Integer provinceId) {
		this.provinceId = provinceId;
	}

	public Integer getCityId() {
		return cityId;
	}

	public void setCityId(Integer cityId) {
		this.cityId = cityId;
	}

	public Integer getCountyId() {
		return countyId;
	}

	public void setCountyId(Integer countyId) {
		this.countyId = countyId;
	}

	public String getAddressZh() {
		return addressZh;
	}

	public void setAddressZh(String addressZh) {
		this.addressZh = addressZh;
	}

	public double getLongitude() {
		return longitude;
	}

	public void setLongitude(double longitude) {
		this.longitude = longitude;
	}

	public double getLatitude() {
		return latitude;
	}

	public void setLatitude(double latitude) {
		this.latitude = latitude;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getFax() {
		return fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	public Integer getDealerType() {
		return dealerType;
	}

	public void setDealerType(Integer dealerType) {
		this.dealerType = dealerType;
	}

	public Integer getDealerScale() {
		return dealerScale;
	}

	public void setDealerScale(Integer dealerScale) {
		this.dealerScale = dealerScale;
	}

	public Integer getWholesaleGrant() {
		return wholesaleGrant;
	}

	public void setWholesaleGrant(Integer wholesaleGrant) {
		this.wholesaleGrant = wholesaleGrant;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getSalesLine() {
		return salesLine;
	}

	public void setSalesLine(String salesLine) {
		this.salesLine = salesLine;
	}

	public String getSalesParmaCode() {
		return salesParmaCode;
	}

	public void setSalesParmaCode(String salesParmaCode) {
		this.salesParmaCode = salesParmaCode;
	}

	public String getGradeSales() {
		return gradeSales;
	}

	public void setGradeSales(String gradeSales) {
		this.gradeSales = gradeSales;
	}

	public String getBankSales() {
		return bankSales;
	}

	public void setBankSales(String bankSales) {
		this.bankSales = bankSales;
	}

	public String getBankAccountSales() {
		return bankAccountSales;
	}

	public void setBankAccountSales(String bankAccountSales) {
		this.bankAccountSales = bankAccountSales;
	}

	public String getTaxSales() {
		return taxSales;
	}

	public void setTaxSales(String taxSales) {
		this.taxSales = taxSales;
	}

	public String getAfterLine() {
		return afterLine;
	}

	public void setAfterLine(String afterLine) {
		this.afterLine = afterLine;
	}

	public String getAfterParmaCode() {
		return afterParmaCode;
	}

	public void setAfterParmaCode(String afterParmaCode) {
		this.afterParmaCode = afterParmaCode;
	}

	public String getGradeAfter() {
		return gradeAfter;
	}

	public void setGradeAfter(String gradeAfter) {
		this.gradeAfter = gradeAfter;
	}

	public String getBankAfter() {
		return bankAfter;
	}

	public void setBankAfter(String bankAfter) {
		this.bankAfter = bankAfter;
	}

	public String getBankAccountAfter() {
		return bankAccountAfter;
	}

	public void setBankAccountAfter(String bankAccountAfter) {
		this.bankAccountAfter = bankAccountAfter;
	}

	public String getTaxAfter() {
		return taxAfter;
	}

	public void setTaxAfter(String taxAfter) {
		this.taxAfter = taxAfter;
	}

	public String getFacility() {
		return facility;
	}

	public void setFacility(String facility) {
		this.facility = facility;
	}

	public String getDealershipOutlet() {
		return dealershipOutlet;
	}

	public void setDealershipOutlet(String dealershipOutlet) {
		this.dealershipOutlet = dealershipOutlet;
	}

	public String getOperationDateDn() {
		return operationDateDn;
	}

	public void setOperationDateDn(String operationDateDn) {
		this.operationDateDn = operationDateDn;
	}

	public String getOperationDateRd() {
		return operationDateRd;
	}

	public void setOperationDateRd(String operationDateRd) {
		this.operationDateRd = operationDateRd;
	}

	public String getOperationDateInterent() {
		return operationDateInterent;
	}

	public void setOperationDateInterent(String operationDateInterent) {
		this.operationDateInterent = operationDateInterent;
	}

	public String getRelocationOrUpgrade() {
		return relocationOrUpgrade;
	}

	public void setRelocationOrUpgrade(String relocationOrUpgrade) {
		this.relocationOrUpgrade = relocationOrUpgrade;
	}

	public String getShortDate() {
		return shortDate;
	}

	public void setShortDate(String shortDate) {
		this.shortDate = shortDate;
	}

	public String getVipsCode() {
		return vipsCode;
	}

	public void setVipsCode(String vipsCode) {
		this.vipsCode = vipsCode;
	}

	public String getVrCode() {
		return vrCode;
	}

	public void setVrCode(String vrCode) {
		this.vrCode = vrCode;
	}

	public Integer getVmiLdc() {
		return vmiLdc;
	}

	public void setVmiLdc(Integer vmiLdc) {
		this.vmiLdc = vmiLdc;
	}

	public String getPartWarehouse() {
		return partWarehouse;
	}

	public void setPartWarehouse(String partWarehouse) {
		this.partWarehouse = partWarehouse;
	}

	public String getWarehouseAddress() {
		return warehouseAddress;
	}

	public void setWarehouseAddress(String warehouseAddress) {
		this.warehouseAddress = warehouseAddress;
	}

	public String getOpenTime() {
		return openTime;
	}

	public void setOpenTime(String openTime) {
		this.openTime = openTime;
	}

	public String getCloseTime() {
		return closeTime;
	}

	public void setCloseTime(String closeTime) {
		this.closeTime = closeTime;
	}

	public String getStorefrontPhotoUrl() {
		return storefrontPhotoUrl;
	}

	public void setStorefrontPhotoUrl(String storefrontPhotoUrl) {
		this.storefrontPhotoUrl = storefrontPhotoUrl;
	}

	public Long getAfterBigAreaId() {
		return afterBigAreaId;
	}

	public void setAfterBigAreaId(Long afterBigAreaId) {
		this.afterBigAreaId = afterBigAreaId;
	}

	public String getAfterBigAreaName() {
		return afterBigAreaName;
	}

	public void setAfterBigAreaName(String afterBigAreaName) {
		this.afterBigAreaName = afterBigAreaName;
	}

	public Long getAfterSmallAreaId() {
		return afterSmallAreaId;
	}

	public void setAfterSmallAreaId(Long afterSmallAreaId) {
		this.afterSmallAreaId = afterSmallAreaId;
	}

	public String getAfterSmallAreaName() {
		return afterSmallAreaName;
	}

	public void setAfterSmallAreaName(String afterSmallAreaName) {
		this.afterSmallAreaName = afterSmallAreaName;
	}

	public Long getSaleBigAreaId() {
		return saleBigAreaId;
	}

	public void setSaleBigAreaId(Long saleBigAreaId) {
		this.saleBigAreaId = saleBigAreaId;
	}

	public String getSaleBigAreaName() {
		return saleBigAreaName;
	}

	public void setSaleBigAreaName(String saleBigAreaName) {
		this.saleBigAreaName = saleBigAreaName;
	}

	public Long getSaleSmallAreaId() {
		return saleSmallAreaId;
	}

	public void setSaleSmallAreaId(Long saleSmallAreaId) {
		this.saleSmallAreaId = saleSmallAreaId;
	}

	public String getSaleSmallAreaName() {
		return saleSmallAreaName;
	}

	public void setSaleSmallAreaName(String saleSmallAreaName) {
		this.saleSmallAreaName = saleSmallAreaName;
	}

	public String getWeChat() {
		return weChat;
	}

	public void setWeChat(String weChat) {
		this.weChat = weChat;
	}

	public String getExtendWarrantyName() {
		return extendWarrantyName;
	}

	public void setExtendWarrantyName(String extendWarrantyName) {
		this.extendWarrantyName = extendWarrantyName;
	}

	public Integer getIsExtendWarranty() {
		return isExtendWarranty;
	}

	public void setIsExtendWarranty(Integer isExtendWarranty) {
		this.isExtendWarranty = isExtendWarranty;
	}

	public String getExtendWarrantyStartDate() {
		return extendWarrantyStartDate;
	}

	public void setExtendWarrantyStartDate(String extendWarrantyStartDate) {
		this.extendWarrantyStartDate = extendWarrantyStartDate;
	}

	public String getExtendWarrantyEndDate() {
		return extendWarrantyEndDate;
	}

	public void setExtendWarrantyEndDate(String extendWarrantyEndDate) {
		this.extendWarrantyEndDate = extendWarrantyEndDate;
	}

}