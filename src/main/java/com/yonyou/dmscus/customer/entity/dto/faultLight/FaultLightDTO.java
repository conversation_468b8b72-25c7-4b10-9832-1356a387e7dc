package com.yonyou.dmscus.customer.entity.dto.faultLight;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class FaultLightDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -9037186655258054951L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 故障(en)
     */
    private String waringEnDescription;

    /**
     * 故障(cn)
     */
    private String waringCnDescription;

    /**
     * 故障名称
     */
    private String warningName;

    /**
     * 次数
     */
    private Integer num;

    /**
     * 次数
     */
    private Integer expireNumber;

    /**
     * 线索生成状态:1，启用；0，未启用
     */
    private Integer produceStatus;

    /**
     * 责任验证状态:1，启用；0，未启用
     */
    private Integer dutyStatus;

    /**
     * 是否删除:1，删除；0，未删除
     */
    private Integer isDeleted;

    /**
     * 数据创建时间
     */
    private Date createdAt;

    /**
     * 数据创建人
     */
    private String createdBy;

    /**
     * 数据修改时间
     */
    private Date updatedAt;

    /**
     * 数据修改人
     */
    private String updatedBy;

    /**
     * 创建sql人
     */
    private String createSqlby;

    /**
     * 更新人sql人
     */
    private String updateSqlby;

    private int currentPage;

    private int pageSize;
}
