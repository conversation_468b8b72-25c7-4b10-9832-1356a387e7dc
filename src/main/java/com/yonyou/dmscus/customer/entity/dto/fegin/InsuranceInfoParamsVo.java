package com.yonyou.dmscus.customer.entity.dto.fegin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@ApiModel("保险公司")
@Data
public class InsuranceInfoParamsVo {

    /**
     * 所有者代码
     */
    @ApiModelProperty(value = "经销商",name = "OWNER_CODE")
    private String OWNER_CODE;

    /**
     * 保险公司代码
     */
    @ApiModelProperty(value = "保险公司代码",name = "INSURATION_CODE")
    private String INSURATION_CODE;

    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保险公司名称",name = "INSURATION_NAME")
    private String INSURATION_NAME;

    /**
     * 保险公司简称
     */
    @ApiModelProperty(value = "保险公司简称",name = "INSURATION_SHORT_NAME")
    private String INSURATION_SHORT_NAME;

  


}
