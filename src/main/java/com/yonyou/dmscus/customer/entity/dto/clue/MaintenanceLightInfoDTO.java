package com.yonyou.dmscus.customer.entity.dto.clue;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * description 保养灯信息参数
 *
 * <AUTHOR>
 * @date 2023/8/21 16:49
 */
@Data
@ApiModel(description = "保养灯信息参数")
public class MaintenanceLightInfoDTO {
    /**
     * 线索来源
     */
    @ApiModelProperty(value = "线索来源")
    private String sourceType;
    /**
     * 邀约类型
     */
    @ApiModelProperty(value = "邀约类型")
    private String invitationType;
    /**
     * 建议进厂时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "建议进厂时间")
    private Date adviceEnterFactory;
    /**
     * 流失类型
     */
    @ApiModelProperty(value = "流失类型")
    private String lossType;
    /**
     * 流失预警类型
     */
    @ApiModelProperty(value = "流失预警类型")
    private String lossWarningType;
    /**
     * 返厂等级
     */
    @ApiModelProperty(value = "返厂等级")
    private String rtnIntentionRating;

    /**
     * 动力类型
     */
    @ApiModelProperty("动力类型,混动/电动/燃油")
    private String powerType;
}
