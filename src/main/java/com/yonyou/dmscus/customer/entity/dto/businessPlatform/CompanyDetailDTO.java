package com.yonyou.dmscus.customer.entity.dto.businessPlatform;


import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "经销商查询信息", description = "经销商查询信息")
@Data
public class CompanyDetailDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "经销商代码id")
    private Long    companyId;

    @ApiModelProperty(value = "经销商代码")
    private String  companyCode;

    @ApiModelProperty(value = "经销商名称中文")
    private String  companyNameCn;

    @ApiModelProperty(value = "经销商简称中文")
    private String  companyShortNameCn;

    @ApiModelProperty(value = "公司类型:1005")
    private Integer companyType;

    @ApiModelProperty(value = "集团")
    private String    groupCompanyId;

    @ApiModelProperty(value = "集团名称")
    private String  groupCompanyName;

    @ApiModelProperty(value = "组织id")
    private Long  orgId;

    @ApiModelProperty(value = "省份")
    private Integer provinceId;
    @ApiModelProperty(value = "省份")
    private String provinceName;
    @ApiModelProperty(value = "城市")
    private Integer cityId;
    @ApiModelProperty(value = "城市")
    private String cityName;
    @ApiModelProperty(value = "区域")
    private Integer countyId;
    @ApiModelProperty(value = "区域")
    private String countyName;
    @ApiModelProperty(value = "地址")
    private String  addressZh;

    @ApiModelProperty(value = "经度")
    private double  longitude;

    @ApiModelProperty(value = "纬度")
    private double  latitude;

    @ApiModelProperty(value = "电话")
    private String  phone;

    @ApiModelProperty(value = "传真")
    private String  fax;

    @ApiModelProperty(value = "经销商类型")
    private Integer dealerType;

    @ApiModelProperty(value = "经销商规模")
    private Integer dealerScale;

    @ApiModelProperty(value = "是否批售授权经销商")
    private Integer wholesaleGrant;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String  remark;

    @ApiModelProperty(value = "销售热线")
    private String  salesLine;

    @ApiModelProperty(value = "销售ParmaCode")
    private String  salesParmaCode;

    @ApiModelProperty(value = "客户评分销售")
    private String  gradeSales;

    @ApiModelProperty(value = "开户银行销售")
    private String  bankSales;

    @ApiModelProperty(value = "开户账号销售")
    private String  bankAccountSales;

    @ApiModelProperty(value = "税号销售")
    private String  taxSales;

    @ApiModelProperty(value = "售后热线")
    private String  afterLine;

    @ApiModelProperty(value = "售后ParmaCode")
    private String  afterParmaCode;

    @ApiModelProperty(value = "客户评分售后")
    private String  gradeAfter;

    @ApiModelProperty(value = "开户银行售后")
    private String  bankAfter;

    @ApiModelProperty(value = "开户账号售后")
    private String  bankAccountAfter;

    @ApiModelProperty(value = "税号售后")
    private String  taxAfter;

    @ApiModelProperty(value = "FACILITY")
    private String  facility;

    @ApiModelProperty(value = "DEALERSHIP_OUTLET")
    private String  dealershipOutlet;

    @ApiModelProperty(value = "OPERATION_DATE_DN")
    private String    operationDateDn;

    @ApiModelProperty(value = "OPERATION_DATE_RD")
    private String    operationDateRd;

    @ApiModelProperty(value = "OPERATION_DATE_INTERNET")
    private String    operationDateInterent;

    @ApiModelProperty(value = "RELOCATION_OR_UPGRADE")
    private String  relocationOrUpgrade;

    @ApiModelProperty(value = "SHORT_DATE")
    private String    shortDate;

    @ApiModelProperty(value = "VIPS_CODE")
    private String  vipsCode;

    @ApiModelProperty(value = "VR_OOED")
    private String  vrCode;

    @ApiModelProperty(value = "VMI_LDC")
    private Integer vmiLdc;

    @ApiModelProperty(value = "零配件仓库")
    private String  partWarehouse;

    @ApiModelProperty(value = "仓库发货地址")
    private String  warehouseAddress;

    @ApiModelProperty(value = "开店时间")
    private String  openTime;

    @ApiModelProperty(value = "关店时间")
    private String  closeTime;

    @ApiModelProperty(value = "经销商门头照片url")
    private String  storefrontPhotoUrl;

    @ApiModelProperty(value = "售后大区ID")
    private Long    afterBigAreaId;

    @ApiModelProperty(value = "售后大区名称")
    private String  afterBigAreaName;

    @ApiModelProperty(value = "售后小区ID")
    private Long    afterSmallAreaId;

    @ApiModelProperty(value = "售后小区名称")
    private String  afterSmallAreaName;

    @ApiModelProperty(value = "销售大区ID")
    private Long    saleBigAreaId;

    @ApiModelProperty(value = "销售大区名称")
    private String  saleBigAreaName;

    @ApiModelProperty(value = "销售小区ID")
    private Long    saleSmallAreaId;

    @ApiModelProperty(value = "销售小区名称")
    private String  saleSmallAreaName;




}
