package com.yonyou.dmscus.customer.entity.po.parse;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("tm_parse")
public class ParsePo  extends SimplePo {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /*
    * 名称
    * */
    private String projectName;


    /*
    * 保险公司名称*/
    private String applyTo;

    /*
    * 保险公司代码
    * */
    private String companyCode;

    // alter  table dms_manage.tm_parse add column companyCode varchar(30) not null UNIQUE comment '保险公司代码';
    // insert into dms_manage.tm_parse (projectName,applyTo,companyCode) select '事故线索',INSURATION_NAME,INSURATION_CODE FROM cyx_callcenter.tm_insurance WHERE  OWNER_CODE in (-1);
    // 将tc_parse_config里与tm_parse对应的相关信息改下
    // select id,applyTo,companyCode from dms_manage.tm_parse;
    // update dms_manage.tc_parse_config set parse_id= xxx where parse_id= xxx;

}
