package com.yonyou.dmscus.customer.entity.dto.middleground;

import java.util.List;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscus.customer.middleInterface.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "根据经销商代码查询角色对应用户", description = "根据经销商代码查询角色对应用户")
public class QueryRoleUserByCompanyCodeDTO extends BaseDTO {

    private static final long serialVersionUID = Constant.SERIAL_VERSION_UID;

    @ApiModelProperty(value = "经销商代码")
    private String companyCode;

    @ApiModelProperty(value = "人员姓名")
    private String employeeName;

    @ApiModelProperty(value = "角色代码")
    private List<String> roleCode;

    @ApiModelProperty(value = "在职状态 10081001:在职  , 10081002:离职")
    private Integer isOnjob;

    public Integer getIsOnjob() {
        return isOnjob;
    }

    public void setIsOnjob(Integer isOnjob) {
        this.isOnjob = isOnjob;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public List<String> getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(List<String> roleCode) {
        this.roleCode = roleCode;
    }

    @Override
    public String toString() {
        return "QueryRoleUserByCompanyCodeDTO [companyCode=" + companyCode + ", employeeName=" + employeeName
                + ", roleCode=" + roleCode + ", isOnjob=" + isOnjob + "]";
    }

}
