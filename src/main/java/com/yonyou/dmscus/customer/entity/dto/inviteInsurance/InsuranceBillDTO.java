package com.yonyou.dmscus.customer.entity.dto.inviteInsurance;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 保险投保单
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
@Data
public class InsuranceBillDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id自增序列
     */
    private Long id;

    /**
     * 经销商代码
     */
    private String dealerCode;

    /**
     * 投保单号
     */
    private String insuranceNo;

    /**
     * 客户投保类型
     */
    private Integer insuranceType;

    /**
     * 状态
     */
    private Integer insuranceStatus;

    /**
     * 车牌号
     */
    private String licenseNo;

    /**
     * VIN
     */
    private String vin;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;


}
