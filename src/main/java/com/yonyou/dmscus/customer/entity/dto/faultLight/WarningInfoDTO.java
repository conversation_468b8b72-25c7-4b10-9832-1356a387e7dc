package com.yonyou.dmscus.customer.entity.dto.faultLight;

import lombok.Data;

import java.io.Serializable;

/**线索同步接口入参(故障报警警信息)*/
@Data
public class WarningInfoDTO implements Serializable {

    private static final long serialVersionUID = 6995570272455274526L;

    /**车辆Vin码*/
    private String vehicleVin;

    /**故障id*/
    private String warningId;

    /**故障名*/
    private String warningName;

    /**故障描述（英文）*/
    private String warningEN;

    /**故障描述（中文）*/
    private String warningCN;

    /**故障等级*/
    private String warningPriority;

    /**中文故障分类*/
    private String wariningCategory;

    /**故障发生时间*/
    private String warningTime;

    /**故障发生省份（中文）*/
    private String warningProvinceCN;

    /**故障发生省份（province_id）*/
    private String warningProvinceId;

    /**故障发生城市（中文）*/
    private String warningCityCN;

    /**故障发生城市（city_id）*/
    private String warningCityId;
}
