package com.yonyou.dmscus.customer.entity.dto.saSllocateDlr;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.UserInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;

import java.io.Serializable;
import java.util.List;

public class SaSllocateDlrDto extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer ruleType;

    private List<UserInfoDTO> selectSa;

    private List<InviteVehicleRecordDTO> inviteVehicleRecordList;

    public Integer getRuleType() {
        return ruleType;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    public List<UserInfoDTO> getSelectSa() {
        return selectSa;
    }

    public void setSelectSa(List<UserInfoDTO> selectSa) {
        this.selectSa = selectSa;
    }

    public List<InviteVehicleRecordDTO> getInviteVehicleRecordList() {
        return inviteVehicleRecordList;
    }

    public void setInviteVehicleRecordList(List<InviteVehicleRecordDTO> inviteVehicleRecordList) {
        this.inviteVehicleRecordList = inviteVehicleRecordList;
    }
}
