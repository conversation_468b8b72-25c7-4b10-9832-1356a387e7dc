package com.yonyou.dmscus.customer.entity.po.goodwill;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善管理录入发票信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-19
 */
@TableName("tt_goodwill_invoice_record")
public class GoodwillInvoiceRecordPO extends BasePO<GoodwillInvoiceRecordPO> {

	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	@TableField("APP_ID")
	private String appId;

	/**
	 * 所有者代码
	 */
	@TableField("OWNER_CODE")
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	@TableField("OWNER_PAR_CODE")
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	@TableField("ORG_ID")
	private Integer orgId;

	/**
	 * 主键ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 亲善单ID
	 */
	@TableField("goodwill_apply_id")
	private Long goodwillApplyId;

	/**
	 * 开票通知单ID
	 */
	@TableField("notice_invoice_id")
	private Long noticeInvoiceId;

	/**
	 * 发票号
	 */
	@TableField("invoice_no")
	private String invoiceNo;

	/**
	 * 发票金额（含税）
	 */
	@TableField("invoice_price")
	private BigDecimal invoicePrice;

	/**
	 * 开票日期
	 */
	@TableField("invoice_date")
	private Date invoiceDate;

	/**
	 * 发票类型
	 */
	@TableField("invoice_type")
	private Integer invoiceType;

	/**
	 * 快递公司
	 */
	@TableField("express_company")
	private String expressCompany;

	/**
	 * 快递单号
	 */
	@TableField("express_no")
	private String expressNo;

	/**
	 * 快递日期
	 */
	@TableField("express_date")
	private Date expressDate;

	/**
	 * 收到发票日期
	 */
	@TableField("received_invoice_date")
	private Date receivedInvoiceDate;

	/**
	 * 收到发票金额
	 */
	@TableField("received_invoice_price")
	private BigDecimal receivedInvoicePrice;

	/**
	 * 开票id
	 */
	@TableField("invoice_id")
	private String invoiceId;

	/**
	 * 是否有效
	 */
	@TableField("is_valid")
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	@TableField("is_deleted")
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	@TableField("created_at")
	private Date createdAt;

	/**
	 * 修改时间
	 */
	@TableField("updated_at")
	private Date updatedAt;

	public GoodwillInvoiceRecordPO() {
		super();
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	public String getOwnerParCode() {
		return ownerParCode;
	}

	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getGoodwillApplyId() {
		return goodwillApplyId;
	}

	public void setGoodwillApplyId(Long goodwillApplyId) {
		this.goodwillApplyId = goodwillApplyId;
	}

	public Long getNoticeInvoiceId() {
		return noticeInvoiceId;
	}

	public void setNoticeInvoiceId(Long noticeInvoiceId) {
		this.noticeInvoiceId = noticeInvoiceId;
	}

	public String getInvoiceNo() {
		return invoiceNo;
	}

	public void setInvoiceNo(String invoiceNo) {
		this.invoiceNo = invoiceNo;
	}

	public BigDecimal getInvoicePrice() {
		return invoicePrice;
	}

	public void setInvoicePrice(BigDecimal invoicePrice) {
		this.invoicePrice = invoicePrice;
	}

	public Date getInvoiceDate() {
		return invoiceDate;
	}

	public void setInvoiceDate(Date invoiceDate) {
		this.invoiceDate = invoiceDate;
	}

	public Integer getInvoiceType() {
		return invoiceType;
	}

	public void setInvoiceType(Integer invoiceType) {
		this.invoiceType = invoiceType;
	}

	public String getExpressCompany() {
		return expressCompany;
	}

	public void setExpressCompany(String expressCompany) {
		this.expressCompany = expressCompany;
	}

	public String getExpressNo() {
		return expressNo;
	}

	public void setExpressNo(String expressNo) {
		this.expressNo = expressNo;
	}

	public BigDecimal getReceivedInvoicePrice() {
		return receivedInvoicePrice;
	}

	public void setReceivedInvoicePrice(BigDecimal receivedInvoicePrice) {
		this.receivedInvoicePrice = receivedInvoicePrice;
	}

	public String getInvoiceId() {
		return invoiceId;
	}

	public void setInvoiceId(String invoiceId) {
		this.invoiceId = invoiceId;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Date getExpressDate() {
		return expressDate;
	}

	public void setExpressDate(Date expressDate) {
		this.expressDate = expressDate;
	}

	public Date getReceivedInvoiceDate() {
		return receivedInvoiceDate;
	}

	public void setReceivedInvoiceDate(Date receivedInvoiceDate) {
		this.receivedInvoiceDate = receivedInvoiceDate;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	protected Serializable pkVal() {
		return this.id;
	}

	@Override
	public String toString() {
		return "GoodwillInvoiceRecordPO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", goodwillApplyId=" + goodwillApplyId
				+ ", noticeInvoiceId=" + noticeInvoiceId + ", invoiceNo=" + invoiceNo + ", invoicePrice=" + invoicePrice
				+ ", invoiceDate=" + invoiceDate + ", invoiceType=" + invoiceType + ", expressCompany=" + expressCompany
				+ ", expressNo=" + expressNo + ", expressDate=" + expressDate + ", receivedInvoiceDate="
				+ receivedInvoiceDate + ", receivedInvoicePrice=" + receivedInvoicePrice + ", invoiceId=" + invoiceId
				+ ", isValid=" + isValid + ", isDeleted=" + isDeleted + ", createdAt=" + createdAt + ", updatedAt="
				+ updatedAt + "}";
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dtoClass
	 *            需要进行转换的dtoClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
		return super.transDtoToPo(dtoClass);
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dto
	 *            需要进行转换的dto
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> void transPoToDto(T dto) {
		BeanMapperUtil.copyProperties(this, dto, "id");
	}

}
