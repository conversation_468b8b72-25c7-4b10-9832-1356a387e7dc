package com.yonyou.dmscus.customer.entity.po.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * description 线索黑名单记录
 *
 * <AUTHOR>
 * @date 2024/12/05 16:03
 */
@Data
@TableName("tt_leads_black_record")
public class LeadsBlackRecordPO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * vin
     */
    @TableField("vin")
    private String vin;

    /**
     * crm线索ID
     */
    @TableField(value = "icm_id")
    private String icmId;

    /**
     * 线索参数
     */
    @TableField("leads_data")
    private String leadsData;
}
