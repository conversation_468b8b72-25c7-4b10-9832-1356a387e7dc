package com.yonyou.dmscus.customer.entity.dto.businessPlatform;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import java.io.Serializable;
import java.util.List;

public class TmVehicleSelectDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String vin;

    private List<String> dealerCodes;

    private String dealerName;

    private String license;

    private String name;

    private Integer page;

    private Integer size;

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public List<String> getDealerCodes() {
        return dealerCodes;
    }

    public void setDealerCodes(List<String> dealerCodes) {
        this.dealerCodes = dealerCodes;
    }

    public String getDealerName() {
        return dealerName;
    }

    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }
}
