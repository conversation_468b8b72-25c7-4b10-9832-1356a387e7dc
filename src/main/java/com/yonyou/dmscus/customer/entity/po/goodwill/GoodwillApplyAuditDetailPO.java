package com.yonyou.dmscus.customer.entity.po.goodwill;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善审计明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-24
 */
@TableName("tt_goodwill_apply_audit_detail")
public class GoodwillApplyAuditDetailPO extends BasePO<GoodwillApplyAuditDetailPO> {

	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	@TableField("APP_ID")
	private String appId;

	/**
	 * 所有者代码
	 */
	@TableField("OWNER_CODE")
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	@TableField("OWNER_PAR_CODE")
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	@TableField("ORG_ID")
	private Integer orgId;

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 审计表主键
	 */
	@TableField("audit_id")
	private Long auditId;

	/**
	 * 审计方式
	 */
	@TableField("audit_way")
	private Integer auditWay;

	/**
	 * 审计时间
	 */
	@TableField("audit_time")
	private Date auditTime;

	/**
	 * 问题点
	 */
	@TableField("trouble_spots")
	private String troubleSpots;

	/**
	 * 处罚结果
	 */
	@TableField("punish_result")
	private String punishResult;

	/**
	 * 扣款金额
	 */
	@TableField("deductions_price")
	private BigDecimal deductionsPrice;

	/**
	 * 是否通报
	 */
	@TableField("is_notification")
	private Integer isNotification;

	/**
	 * 是否有效
	 */
	@TableField("is_valid")
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	@TableField("is_deleted")
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	@TableField("created_at")
	private Date createdAt;

	/**
	 * 修改时间
	 */
	@TableField("updated_at")
	private Date updatedAt;

	public GoodwillApplyAuditDetailPO() {
		super();
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	public String getOwnerParCode() {
		return ownerParCode;
	}

	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getAuditId() {
		return auditId;
	}

	public void setAuditId(Long auditId) {
		this.auditId = auditId;
	}

	public Integer getAuditWay() {
		return auditWay;
	}

	public void setAuditWay(Integer auditWay) {
		this.auditWay = auditWay;
	}

	public String getTroubleSpots() {
		return troubleSpots;
	}

	public void setTroubleSpots(String troubleSpots) {
		this.troubleSpots = troubleSpots;
	}

	public String getPunishResult() {
		return punishResult;
	}

	public void setPunishResult(String punishResult) {
		this.punishResult = punishResult;
	}

	public BigDecimal getDeductionsPrice() {
		return deductionsPrice;
	}

	public void setDeductionsPrice(BigDecimal deductionsPrice) {
		this.deductionsPrice = deductionsPrice;
	}

	public Integer getIsNotification() {
		return isNotification;
	}

	public void setIsNotification(Integer isNotification) {
		this.isNotification = isNotification;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Date getAuditTime() {
		return auditTime;
	}

	public void setAuditTime(Date auditTime) {
		this.auditTime = auditTime;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	protected Serializable pkVal() {
		return this.id;
	}

	@Override
	public String toString() {
		return "GoodwillApplyAuditDetailPO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", auditId=" + auditId + ", auditWay=" + auditWay
				+ ", auditTime=" + auditTime + ", troubleSpots=" + troubleSpots + ", punishResult=" + punishResult
				+ ", deductionsPrice=" + deductionsPrice + ", isNotification=" + isNotification + ", isValid=" + isValid
				+ ", isDeleted=" + isDeleted + ", createdAt=" + createdAt + ", updatedAt=" + updatedAt + "}";
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dtoClass
	 *            需要进行转换的dtoClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
		return super.transDtoToPo(dtoClass);
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dto
	 *            需要进行转换的dto
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> void transPoToDto(T dto) {
		BeanMapperUtil.copyProperties(this, dto, "id");
	}

}
