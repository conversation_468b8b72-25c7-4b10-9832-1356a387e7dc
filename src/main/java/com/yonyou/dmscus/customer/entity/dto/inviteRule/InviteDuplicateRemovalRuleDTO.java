package com.yonyou.dmscus.customer.entity.dto.inviteRule;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 邀约去重规则表，本章表所存储的数据，应该都是一个默认值。
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-17
 */

public class InviteDuplicateRemovalRuleDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 规则 原始默认值90天
     */
    private Integer rule;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 1:设置X天有进厂记录，则不再生成首保、定保、客户流失的邀约
     * 2:设置X天有首保或定保的邀约记录，则易损件、流失客户的邀约合并到首保、定保
     */
    private Integer ruleType;

    public InviteDuplicateRemovalRuleDTO() {
        super();
    }


    public String getAppId() {
        return appId;
    }


    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }


    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerParCode() {
        return ownerParCode;
    }


    public void setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public Integer getOrgId() {
        return orgId;
    }


    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId() {
        return id;
    }


    public void setId(Long id) {
        this.id = id;
    }

    public Integer getRule() {
        return rule;
    }


    public void setRule(Integer rule) {
        this.rule = rule;
    }

    public Integer getDataSources() {
        return dataSources;
    }


    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }


    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid() {
        return isValid;
    }


    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Date getCreatedAt() {
        return createdAt;
    }


    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }


    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Integer getRuleType() {
        return ruleType;
    }


    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    @Override
    public String toString() {
        return "InviteDuplicateRemovalRuleDTO{" +
                "appId=" + appId +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", rule=" + rule +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", ruleType=" + ruleType +
                "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
