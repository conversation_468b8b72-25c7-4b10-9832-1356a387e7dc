package com.yonyou.dmscus.customer.entity.dto.complaint;

/**
 * <AUTHOR>
 */
public class CcmImportDto1 {
    private  Long id;
    private  Long userId;
    private  String dealerName;
    private  String afterSmallAreaName;
    private  String  afterBigAreaName;
    private  String groupCompanyName;
    private  Long ccmManId;

    private  Long afterBigAreaId;
    private  Long afterSmallAreaId;

    public Long getCcmManId() {
        return ccmManId;
    }

    public void setCcmManId(Long ccmManId) {
        this.ccmManId = ccmManId;
    }

    public Long getAfterBigAreaId() {
        return afterBigAreaId;
    }

    public void setAfterBigAreaId(Long afterBigAreaId) {
        this.afterBigAreaId = afterBigAreaId;
    }

    public Long getAfterSmallAreaId() {
        return afterSmallAreaId;
    }

    public void setAfterSmallAreaId(Long afterSmallAreaId) {
        this.afterSmallAreaId = afterSmallAreaId;
    }

    public String getGroupCompanyName() {
        return groupCompanyName;
    }

    public void setGroupCompanyName(String groupCompanyName) {
        this.groupCompanyName = groupCompanyName;
    }

    public String getAfterSmallAreaName() {
        return afterSmallAreaName;
    }

    public void setAfterSmallAreaName(String afterSmallAreaName) {
        this.afterSmallAreaName = afterSmallAreaName;
    }

    public String getAfterBigAreaName() {
        return afterBigAreaName;
    }

    public void setAfterBigAreaName(String afterBigAreaName) {
        this.afterBigAreaName = afterBigAreaName;
    }

    public String getDealerName() {
        return dealerName;
    }

    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}
