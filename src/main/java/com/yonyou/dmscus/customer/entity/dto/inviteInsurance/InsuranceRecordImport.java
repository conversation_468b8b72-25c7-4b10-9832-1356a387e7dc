package com.yonyou.dmscus.customer.entity.dto.inviteInsurance;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.service.excel.ExcelColumnDefine;
import com.yonyou.dmscloud.function.domains.dto.DataImportDto;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel(value = "续保线索Excel导入实体")
public class InsuranceRecordImport extends DataImportDto {

	/**
	 * VIN
	 */
	@ExcelColumnDefine(value = 1)
	private String vin;

	/**
	 * 商业险到期日期
	 */
	@ExcelColumnDefine(value = 2)
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date viFinishDate;

	/**
	 * 交强险到期日期
	 */
	@ExcelColumnDefine(value = 3)
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date clivtaFinishDate;

	/**
	 * 续保人员账号
	 */
	@ExcelColumnDefine(value = 4)
	private String insureCustomerInfo;

	/**
	 * 保险公司名称
	 */
	@ExcelColumnDefine(value = 5)
	private String insuranceName;

}
