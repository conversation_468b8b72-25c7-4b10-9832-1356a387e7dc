package com.yonyou.dmscus.customer.entity.po.goodwill;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 卡券-使用规则-标准规则-部分经销商
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-19
 */
@TableName("tt_coupon_ur_standard_dealer")
public class CouponUrStandardDealerPO extends BasePO<CouponUrStandardDealerPO> {

	private static final long serialVersionUID = 1L;

	/**
	 * 系统id
	 */
	@TableField("app_id")
	private String appId;

	/**
	 * 所有者代码
	 */
	@TableField("owner_code")
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	@TableField("owner_par_code")
	private String ownerParCode;

	/**
	 * 组织id
	 */
	@TableField("org_id")
	private Integer orgId;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 卡券ID
	 */
	@TableField("coupon_id")
	private Long couponId;

	/**
	 * 经销商Code
	 */
	@TableField("dealer_code")
	private String dealerCode;

	/**
	 * 经销商名称
	 */
	@TableField("dealer_name")
	private String dealerName;

	/**
	 * 经销商大区 ID
	 */
	@TableField("big_area_id")
	private String bigAreaId;

	/**
	 * 经销商小区 ID
	 */
	@TableField("small_area_id")
	private String smallAreaId;

	/**
	 * 经销商大区
	 */
	@TableField("big_area")
	private String bigArea;

	/**
	 * 经销商小区
	 */
	@TableField("small_area")
	private String smallArea;

	/**
	 * 经销商运营地址
	 */
	@TableField("dealer_address")
	private String dealerAddress;

	/**
	 * 数据来源
	 */
	@TableField("data_sources")
	private Integer dataSources;

	/**
	 * 是否删除
	 */
	@TableField("is_deleted")
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	@TableField("created_at")
	private Date createdAt;

	/**
	 * 更新时间
	 */
	@TableField("updated_at")
	private Date updatedAt;

	public CouponUrStandardDealerPO() {
		super();
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	public String getOwnerParCode() {
		return ownerParCode;
	}

	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCouponId() {
		return couponId;
	}

	public void setCouponId(Long couponId) {
		this.couponId = couponId;
	}

	public String getDealerCode() {
		return dealerCode;
	}

	public void setDealerCode(String dealerCode) {
		this.dealerCode = dealerCode;
	}

	public String getDealerName() {
		return dealerName;
	}

	public void setDealerName(String dealerName) {
		this.dealerName = dealerName;
	}

	public String getBigAreaId() {
		return bigAreaId;
	}

	public void setBigAreaId(String bigAreaId) {
		this.bigAreaId = bigAreaId;
	}

	public String getSmallAreaId() {
		return smallAreaId;
	}

	public void setSmallAreaId(String smallAreaId) {
		this.smallAreaId = smallAreaId;
	}

	public String getBigArea() {
		return bigArea;
	}

	public void setBigArea(String bigArea) {
		this.bigArea = bigArea;
	}

	public String getSmallArea() {
		return smallArea;
	}

	public void setSmallArea(String smallArea) {
		this.smallArea = smallArea;
	}

	public String getDealerAddress() {
		return dealerAddress;
	}

	public void setDealerAddress(String dealerAddress) {
		this.dealerAddress = dealerAddress;
	}

	public Integer getDataSources() {
		return dataSources;
	}

	public void setDataSources(Integer dataSources) {
		this.dataSources = dataSources;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	protected Serializable pkVal() {
		return this.id;
	}

	@Override
	public String toString() {
		return "CouponUrStandardDealerPO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", couponId=" + couponId + ", dealerCode="
				+ dealerCode + ", dealerName=" + dealerName + ", bigAreaId=" + bigAreaId + ", smallAreaId="
				+ smallAreaId + ", bigArea=" + bigArea + ", smallArea=" + smallArea + ", dealerAddress=" + dealerAddress
				+ ", dataSources=" + dataSources + ", isDeleted=" + isDeleted + ", createdAt=" + createdAt
				+ ", updatedAt=" + updatedAt + "}";
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dtoClass
	 *            需要进行转换的dtoClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
		return super.transDtoToPo(dtoClass);
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dto
	 *            需要进行转换的dto
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> void transPoToDto(T dto) {
		BeanMapperUtil.copyProperties(this, dto, "id");
	}

}
