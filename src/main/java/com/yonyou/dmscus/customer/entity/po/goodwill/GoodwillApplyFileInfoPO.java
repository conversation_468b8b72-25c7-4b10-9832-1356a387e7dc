package com.yonyou.dmscus.customer.entity.po.goodwill;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善预申请上传附件表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
@TableName("tt_goodwill_apply_file_info")
public class GoodwillApplyFileInfoPO extends BasePO<GoodwillApplyFileInfoPO> {

	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	@TableField("APP_ID")
	private String appId;

	/**
	 * 所有者代码
	 */
	@TableField("OWNER_CODE")
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	@TableField("OWNER_PAR_CODE")
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	@TableField("ORG_ID")
	private Integer orgId;

	/**
	 * 主键ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 亲善预申请单id
	 */
	@TableField("goodwill_apply_id")
	private byte[] goodwillApplyId;

	/**
	 * 附件类型
	 */
	@TableField("file_type")
	private Integer fileType;

	/**
	 * 上传人
	 */
	@TableField("upload_person")
	private Long uploadPerson;

	/**
	 * 上传时间
	 */
	@TableField("upload_time")
	private Date uploadTime;

	/**
	 * 附件名称
	 */
	@TableField("file_name")
	private String fileName;

	/**
	 * url
	 */
	@TableField("url")
	private String url;

	/**
	 * 文件大小
	 */
	@TableField("file_size")
	private BigDecimal fileSize;

	/**
	 * 是否有效
	 */
	@TableField("is_valid")
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	@TableField("is_deleted")
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	@TableField("created_at")
	private Date createdAt;

	/**
	 * 修改时间
	 */
	@TableField("updated_at")
	private Date updatedAt;

	/**
	 * 亲善单号
	 */
	@TableField("apply_no")
	private String applyNo;

	public GoodwillApplyFileInfoPO() {
		super();
	}

	@Override
	public String getAppId() {
		return appId;
	}

	@Override
	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	@Override
	public String getOwnerParCode() {
		return ownerParCode;
	}

	@Override
	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public byte[] getGoodwillApplyId() {
		return goodwillApplyId;
	}

	public void setGoodwillApplyId(byte[] goodwillApplyId) {
		this.goodwillApplyId = goodwillApplyId;
	}

	public Integer getFileType() {
		return fileType;
	}

	public void setFileType(Integer fileType) {
		this.fileType = fileType;
	}

	public Long getUploadPerson() {
		return uploadPerson;
	}

	public void setUploadPerson(Long uploadPerson) {
		this.uploadPerson = uploadPerson;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public BigDecimal getFileSize() {
		return fileSize;
	}

	public void setFileSize(BigDecimal fileSize) {
		this.fileSize = fileSize;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Date getUploadTime() {
		return uploadTime;
	}

	public void setUploadTime(Date uploadTime) {
		this.uploadTime = uploadTime;
	}

	@Override
	public Date getCreatedAt() {
		return createdAt;
	}

	@Override
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Override
	public Date getUpdatedAt() {
		return updatedAt;
	}

	@Override
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public String getApplyNo() {
		return applyNo;
	}

	public void setApplyNo(String applyNo) {
		this.applyNo = applyNo;
	}

	@Override
	protected Serializable pkVal() {
		return this.id;
	}

	// @Override
	// public String toString() {
	// return "GoodwillApplyFileInfoPO{" + "appId=" + appId + ", ownerCode=" +
	// ownerCode + ", ownerParCode="
	// + ownerParCode + ", orgId=" + orgId + ", id=" + id + ", goodwillApplyId=" +
	// goodwillApplyId
	// + ", fileType=" + fileType + ", uploadPerson=" + uploadPerson + ",
	// uploadTime=" + uploadTime
	// + ", fileName=" + fileName + ", url=" + url + ", fileSize=" + fileSize + ",
	// isValid=" + isValid
	// + ", isDeleted=" + isDeleted + ", createdAt=" + createdAt + ", updatedAt=" +
	// updatedAt + ", applyNo="
	// + applyNo + "}";
	// }

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dtoClass
	 *            需要进行转换的dtoClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
		return super.transDtoToPo(dtoClass);
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dto
	 *            需要进行转换的dto
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> void transPoToDto(T dto) {
		BeanMapperUtil.copyProperties(this, dto, "id");
	}

}
