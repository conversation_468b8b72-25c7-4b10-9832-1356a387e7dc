package com.yonyou.dmscus.customer.entity.dto.invitationCreate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 车辆特约店自建邀约任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-29
 */

@Data
public class InviteVehicleDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;



    /**
     * 车主名称
     */
    private String name;

    /**
     * 车主电话
     */
    private String tel;

    /**
     * 联系人名称
     */
    private String linkmanName;

    /**
     * 联系人电话
     */
    private String linkmanTel;

    /**
     * 车牌号
     */
    private String licensePlateNum;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 车系
     */
    private String series;

    /**
     * 车型
     */
    private String model;

    /**
     * 销售日期
     */
    private Date saleDate;

    /**
     * 维修金额
     */
    private BigDecimal repairAmount;

    /**
     * 维修次数
     */
    private Integer repairTimes;

    /**
     * 上次维修日期
     */
    private Date lastRepairDate;

    /**
     * 上次维修里程
     */
    private Integer lastRepairMileage;

    /**
     * 上次跟进人员id
     */
    private  String saId;

    /**
     * 上次跟进人员姓名
     */
    private  String saName;




}
