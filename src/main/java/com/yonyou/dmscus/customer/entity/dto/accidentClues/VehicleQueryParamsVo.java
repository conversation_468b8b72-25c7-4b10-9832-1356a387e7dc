package com.yonyou.dmscus.customer.entity.dto.accidentClues;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel("车辆查询Vo")
public class VehicleQueryParamsVo {

    @ApiModelProperty(value = "排序字段",name = "sort")
    private String sort;

    @ApiModelProperty(value = "排序类型",name = "sortType")
    private String sortType;

    @ApiModelProperty(hidden = true)
    private String ownerCode;

    /**
     * 车辆信息
     */
    @ApiModelProperty(value = "车辆信息",name = "vehicleInfo")
    private String vehicleInfo;


    /**
     * 客户名称  pad 端字段
     */
    @ApiModelProperty(value = "客户名称",name = "ownerName")
    private String ownerName;

    /**
     * 客户手机 pad 端字段
     */
    @ApiModelProperty(value = "客户手机",name = "mobile")
    private String mobile;

    /**
     * 判断是否为pad端 pad 端字段
     */
    @ApiModelProperty(value = "pad端来源",name = "isPad")
    private String isPad;

}
