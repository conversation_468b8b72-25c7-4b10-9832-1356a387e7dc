package com.yonyou.dmscus.customer.entity.dto.vocAccidentInvitation;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 车辆特约店VOC事故邀约任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
@Data
public class TempInviteVehicleVocTaskDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 数据行号
     */
    private Integer lineNumber;

    /**
     * 是否错误
     */
    private Boolean isError;

    /**
     * 客户姓名
     */
    private String name;

    /**
     * 电话
     */
    private String tel;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车牌号
     */
    private String licensePlateNum;

    /**
     * 进销商代码
     */
    private String dealerCode;

    /**
     * VOC事故车联系经销商时间
     */
    private String contactDate;

    /**
     * 与客户通话情况： 接通、未接通
     */
    private String contactSituation;

    /**
     * VOC事故号
     */
    private String accidentNo;

    /**
     * VOC事故说明
     */
    private String accidentDetail;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;


    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
