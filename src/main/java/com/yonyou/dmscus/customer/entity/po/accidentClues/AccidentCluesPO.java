package com.yonyou.dmscus.customer.entity.po.accidentClues;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 事故线索
 */
@TableName("tt_accident_clues")
@Data
public class AccidentCluesPO extends BasePO<AccidentCluesPO> {

  @TableField("app_id")
  private String appId;

  @TableField("owner_code")
  private String ownerCode;

  @TableField("owner_par_code")
  private String ownerParCode;

  @TableField("org_id")
  private Integer orgId;

  /**
   * 主键ID
   */
  @TableId("ac_id")
  private Integer acId;

  @TableField(exist = false)
  private List<Integer> acIdList;

  /**
   * 经销商代码
   */
  @TableField("dealer_code")
  private String dealerCode;

  /**
   * 车牌号
   */
  @TableField("license")
  private String license;

  /**
   * vin
   */
  @TableField("vin")
  private String vin;

  /**
   * 车型id
   */
  @TableField("models_id")
  private String modelsId;

  /**
   * 保险公司id
   */
  @TableField("insurance_company_id")
  private String insuranceCompanyId;

  /**
   * 线索来源
   */
  @TableField("clues_resource")
  private Integer cluesResource;

  /**
   * 联系人
   */
  @TableField("contacts")
  private String contacts;

  /**
   * 联系人电话
   */
  @TableField("contacts_phone")
  private String contactsPhone;

  /**
   * 报案时间
   */
  @TableField("report_date")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date reportDate;

  /**
   * 事故地点
   */
  @TableField("accident_address")
  private String accidentAddress;

  /**
   * 事故类型
   */
  @TableField("accident_type")
  private Integer accidentType;

  /**
   * 是否有人受伤
   */
  @TableField("is_bruise")
  private Integer isBruise;

  /**
   * 外拓费用
   */
  @TableField("outside_amount")
  private BigDecimal outsideAmount;

  /**
   * 跟进人员id
   */
  @TableField("follow_people")
  private Integer followPeople;

  /**
   * 备注
   */
  @TableField("remark")
  private String remark;

  /**
   * 是否本店承保
   */
  @TableField("is_insured")
  private Integer isInsured;

  /**
   * 事故责任
   */
  @TableField("accident_duty")
  private Integer accidentDuty;

  /**
   * 跟进状态
   */
  @TableField("follow_status")
  private Integer followStatus;

  /**
   * 线索状态
   */
  @TableField("clues_status")
  private Integer cluesStatus;

  /**
   * 进厂经销商
   */
  @TableField("into_dealer_code")
  private String intoDealerCode;

  /**
   * 进厂时间
   */
  @TableField("into_dealer_date")
  private Date intoDealerDate;

  /**
   * 进厂工单号
   */
  @TableField("into_ro_no")
  private String intoRoNo;

  /**
   * 下次跟进时间
   */
  @TableField("next_follow_date")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date nextFollowDate;

  /**
   * 是否预约
   */
  @TableField("is_appointment")
  private Integer isAppointment;

  /**
   * 跟进次数
   */
  @TableField("follow_count")
  private Integer followCount;

  /**
   * 线索类型
   */
  @TableField("clues_type")
  private String cluesType;

  //数据来源
  @TableField("data_sources")
  private Integer dataSources;

  //是否删除，1：删除，0：未删除"
  @TableField("is_deleted")
  private Integer isDeleted;

  //是否有效"
  @TableField("is_valid")
  private Integer isValid;

  //创建时间"
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @TableField("created_at")
  private Date createdAt;

  //创建人"
  @TableField("created_by")
  private String createdBy;

  //更新时间"
  @TableField("updated_at")
  private Date updatedAt;

  //更新人
  @TableField("updated_by")
  private String updatedBy;

  //版本号（乐观锁）
  @TableField("record_version")
  private Integer recordVersion;

  @TableField(exist = false)
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date reportDateStart;

  @TableField(exist = false)
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date reportDateEnd;

  @TableField(exist = false)
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date createdDateStart;

  @TableField(exist = false)
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date createdDateEnd;

  @TableField(exist = false)
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date nextFollowDateStart;

  @TableField(exist = false)
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date nextFollowDateEnd;

  @TableField("double_accident")
  private Integer doubleAccident;

  @TableField("insurance_company_name")
  private String  insuranceCompanyName;

  @TableField("follow_people_name")
  private String  followPeopleName;


  @TableField("after_big_area_id")
  private Long    afterBigAreaId;

  @TableField("after_big_area_name")
  private String  afterBigAreaName;

  @TableField("after_small_area_id")
  private Long    afterSmallAreaId;

  @TableField("after_small_area_name")
  private String  afterSmallAreaName;

  @TableField(exist = false)
//店端/厂端（vcdc）
  private String source;

  @TableField(exist = false) //分配状态
  private Integer allotStatus;

  @TableField("one_id")
  private Integer oneId;

  @TableField(exist = false)
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date appointmentIntoDate;

  @TableField("pictures")
  private String pictures;

  /**
   * 预约单号
   */
  @TableField("booking_order_no")
  private String bookingOrderNo;

  @ApiModelProperty(value = "线索ID")
  @TableField(exist = false)
  @JsonSerialize(using = ToStringSerializer.class)
  private Long crmId;

  @ApiModelProperty(value = "报案号")
  @TableField(exist = false)
  private String registNo;

  @ApiModelProperty(value = "是否重复案件")
  @TableField(exist = false)
  private String repeatLead;

  @ApiModelProperty(value = "出险时间")
  @TableField(exist = false)
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date accidentDate;

  @ApiModelProperty(value = "出险内容")
  @TableField(exist = false)
  private String accidentReason;

  @ApiModelProperty(value = "客户类型")
  @TableField(exist = false)
  private String contactsName;

  @ApiModelProperty(value = "进线类型")
  @TableField(exist = false)
  private String callType;

  @ApiModelProperty(value = "是否报警")
  @TableField(exist = false)
  private String callPoliceFlag;

  @TableField(exist = false)
  @ApiModelProperty(value = "创建渠道,YB，400，Newbie")
  private String sourceChannel;

  @ApiModelProperty(value="渠道标签")
  @TableField(exist = false)
  private String channelType;

  @TableField(exist = false)
  @ApiModelProperty("车型名称")
  private String modelName;

  @TableField(exist = false)
  @ApiModelProperty("车主名")
  private String ownerName;

  @TableField(exist = false)
  @ApiModelProperty("车主电话")
  private String ownerMobile;

  @TableField(exist = false)
  @ApiModelProperty("送返修标识,送修/返修")
  private String clientType;

  @TableField(exist = false)
  @ApiModelProperty("首次跟进时间")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date firstFollowTime;

  @TableField(exist = false)
  @ApiModelProperty("二次跟进时间")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date lastFollowTime;

  @TableField(exist = false)
  @ApiModelProperty("跟进内容")
  private String followText;

  @TableField(exist = false)
  @ApiModelProperty("跟进失败原因")
  private String followFailWhy;

  @TableField(exist = false)
  @ApiModelProperty("是否虚拟号码")
  private Integer virtualPhoneFlag;

  @TableField(exist = false)
  private List<Integer> cluesStatusList;

  @TableField(exist = false)
  private List<Integer> followStatusList;

  @TableField(exist = false)
  private List<Integer> cluesResourceList;

  @TableField(exist = false)
  private String insuranceSource;

  @TableField(exist = false)
  private String provinceName;
  @TableField(exist = false)
  private String cityName;
  @TableField(exist = false)
  private Integer dataStatus;
  @TableField(exist = false)
  private Long    provinceId;
  @TableField(exist = false)
  private Long    cityId;
  @TableField(exist = false)
  private String    dataStatusRemark;
  /**
   * 将PO 信息转化为DTO
   *
   * @param dtoClass 需要进行转换的dtoClass
   * <AUTHOR>
   * @since 2018/7/22 0022
   */
  public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
    return super.transDtoToPo(dtoClass);
  }

  /**
   * 将PO 信息转化为DTO
   *
   * @param dto 需要进行转换的dto
   * <AUTHOR>
   * @since 2018/7/22 0022
   */
  public <T extends BaseDTO> void transPoToDto(T dto) {
    BeanMapperUtil.copyProperties(this, dto, "qualityId");
  }

}
