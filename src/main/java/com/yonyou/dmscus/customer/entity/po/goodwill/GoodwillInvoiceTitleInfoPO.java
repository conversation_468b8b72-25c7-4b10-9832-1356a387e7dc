package com.yonyou.dmscus.customer.entity.po.goodwill;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善发票抬头信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-17
 */
@TableName("tm_goodwill_invoice_title_info")
public class GoodwillInvoiceTitleInfoPO extends BasePO<GoodwillInvoiceTitleInfoPO> {

	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	@TableField("APP_ID")
	private String appId;

	/**
	 * 所有者代码
	 */
	@TableField("OWNER_CODE")
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	@TableField("OWNER_PAR_CODE")
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	@TableField("ORG_ID")
	private Integer orgId;

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 开票抬头
	 */
	@TableField("invoice_title")
	private Integer invoiceTitle;

	/**
	 * 名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 纳税人识别号
	 */
	@TableField("taxpayer_identification_number")
	private String taxpayerIdentificationNumber;

	/**
	 * 电话
	 */
	@TableField("phone")
	private String phone;

	/**
	 * 地址
	 */
	@TableField("address")
	private String address;

	/**
	 * 开户行
	 */
	@TableField("open_bank")
	private String openBank;

	/**
	 * 账号
	 */
	@TableField("account")
	private String account;

	/**
	 * 是否有效
	 */
	@TableField("is_valid")
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	@TableField("is_deleted")
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	@TableField("created_at")
	private Date createdAt;

	/**
	 * 修改时间
	 */
	@TableField("updated_at")
	private Date updatedAt;

	public GoodwillInvoiceTitleInfoPO() {
		super();
	}

	@Override
	public String getAppId() {
		return appId;
	}

	@Override
	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	@Override
	public String getOwnerParCode() {
		return ownerParCode;
	}

	@Override
	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getInvoiceTitle() {
		return invoiceTitle;
	}

	public void setInvoiceTitle(Integer invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getTaxpayerIdentificationNumber() {
		return taxpayerIdentificationNumber;
	}

	public void setTaxpayerIdentificationNumber(String taxpayerIdentificationNumber) {
		this.taxpayerIdentificationNumber = taxpayerIdentificationNumber;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getOpenBank() {
		return openBank;
	}

	public void setOpenBank(String openBank) {
		this.openBank = openBank;
	}

	public String getAccount() {
		return account;
	}

	public void setAccount(String account) {
		this.account = account;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	@Override
	public Date getCreatedAt() {
		return createdAt;
	}

	@Override
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Override
	public Date getUpdatedAt() {
		return updatedAt;
	}

	@Override
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	protected Serializable pkVal() {
		return this.id;
	}

	@Override
	public String toString() {
		return "GoodwillInvoiceTitleInfoPO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", invoiceTitle=" + invoiceTitle + ", name=" + name
				+ ", taxpayerIdentificationNumber=" + taxpayerIdentificationNumber + ", phone=" + phone + ", address="
				+ address + ", openBank=" + openBank + ", account=" + account + ", isValid=" + isValid + ", isDeleted="
				+ isDeleted + ", createdAt=" + createdAt + ", updatedAt=" + updatedAt + "}";
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dtoClass
	 *            需要进行转换的dtoClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
		return super.transDtoToPo(dtoClass);
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dto
	 *            需要进行转换的dto
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> void transPoToDto(T dto) {
		BeanMapperUtil.copyProperties(this, dto, "id");
	}

}
