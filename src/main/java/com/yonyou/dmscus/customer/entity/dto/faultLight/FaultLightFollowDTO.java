package com.yonyou.dmscus.customer.entity.dto.faultLight;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 跟进页面参数
 */
@Data
public class FaultLightFollowDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -9037186655258054951L;

    /**
     * 姓名
     */
    private String cusName;

    /**
     * 性别
     */
    private String gender;

    /**
     * 电话号码
     */
    private String cusPhone;

    /**
     * 年龄
     */
    private String age;

    /**
     * 车牌
     */
    private String plateNumber;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车型
     */
    private String model;

    /**
     * 日均里程
     */
    private String dailyAverageMileage;

    /**
     * 线索状态
     */
    private String clueStatus;

    /**
     * 跟进状态
     */
    private String followStatus;

    /**
     * 预计进店时间
     */
    private Date forecastTime;

    /**
     * 邀约时间
     */
    private Date inviteTime;

    /**
     * 关联工单
     */
    private String roNo;

    /**
     * 报警时间
     */
    private String warningTime;

    /**
     * 报警内容
     */
    private String waringCnDescription;

    /**
     * 报警城市
     */
    private String warningCity;

    /**
     * 外呼备注
     */
    private String comments;

    /**
     * 里程
     */
    private String dailyMile;

    /**
     * 前端展示里程
     */
    private String displayMile;

    /**
     * 预约单号
     */
    private String bookingOrderNo;

    /**
     * 预约单号
     */
    private Long icmId;

    /**
     * 经销商
     */
    private String dealerCode;
}
