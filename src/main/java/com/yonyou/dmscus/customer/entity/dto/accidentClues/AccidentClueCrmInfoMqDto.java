package com.yonyou.dmscus.customer.entity.dto.accidentClues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("事故线索上报更新，MQ消息")
public class AccidentClueCrmInfoMqDto {
    @ApiModelProperty("litecrmId")
    private Long id;
    @ApiModelProperty("newbieId")
    private String sourceClueId;
    @ApiModelProperty("是否重复")
    private Integer repeatFlag;
    @ApiModelProperty("重复线索ID")
    private List<Long> repeatIdList;
    @ApiModelProperty("渠道标签")
    private String channelTag;
    @ApiModelProperty("来源渠道（NewBie，YB，400）")
    private String sourceChannel;
    @ApiModelProperty("经销商代码")
    private String dealerCode;

    @ApiModelProperty("跟进失败原因")
    private String followFailWhy;

    @ApiModelProperty("售后大区ID")
    private Long afterBigAreaId;

    @ApiModelProperty("售后大区名")
    private String afterBigAreaName;

    @ApiModelProperty("售后小区ID")
    private Long afterSmallAreaId;

    @ApiModelProperty("售后小区名")
    private String afterSmallAreaName;
}