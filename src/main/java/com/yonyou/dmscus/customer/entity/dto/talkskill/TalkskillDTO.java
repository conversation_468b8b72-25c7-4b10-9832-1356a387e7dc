package com.yonyou.dmscus.customer.entity.dto.talkskill;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 话术
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-19
 */
@Data
public class TalkskillDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    private Long talkId;

    /**
     * 经销商编号
     */
    private String dealerCode;

    private  Long vcdcId ;

    private  Integer isDealerMod ;

    private Integer dataType;
    /**
     * 话术标题
     */
    private String title;

    /**
     * 业务分类
     */
    private String type;

    /**
     * 业务分类 code
     */
    private String typeCode;

    /**
     * 有效开始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private LocalDate beginDate;

    /**
     * 有效结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private LocalDate endDate;

    /**
     * 一级标签
     */
    private Integer tag1;

    /**
     * 一级标签 code
     */
    private String tag1Code;

    /**
     * 一级标签--名称
     */
    private String tag1Name;
    /**
     * 二级标签
     */
    private Integer tag2;
    /**
     * 二级标签-name
     */
    private String tag2Name;
    /**
     * 话术
     */
    private String talkskill;

    /**
     * 关键词1
     */
    private String keyword1;

    /**
     * 关键词2
     */
    private String keyword2;



    /**
     * 关键词3
     */
    private String keyword3;
    /**
     * 关键词old-list
     */
    private List<String> oldKeywordList;
    /**
     * 关键词new -list
     */
    private List<String> keywordList;
    /**
     * 是否强制推送
     */
    private Integer isCompel;

    /**
     * 是否启用
     */
    private Integer isEnable;

    /**
     * 是否厂端
     */
    private Integer isVcdc;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除，1：删除，0：未删除
     */
    private Integer isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    public TalkskillDTO() {
        super();
    }


    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "talkId");
    }

}
