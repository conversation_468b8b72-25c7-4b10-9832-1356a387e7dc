package com.yonyou.dmscus.customer.entity.po.vocAccidentInvitation;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import com.yonyou.dmscloud.framework.base.po.BasePO;

import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * <p>
 * 车辆特约店VOC事故邀约任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
@Data
@TableName("temp_invite_vehicle_voc_task")
public class TempInviteVehicleVocTaskPO extends BasePO<TempInviteVehicleVocTaskPO> {

    private static final long serialVersionUID = 1L;


    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;


    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 错误信息
     */
    @TableField("error_msg")
    private String errorMsg;

    /**
     * 数据行号
     */
    @TableField("line_number")
    private Integer lineNumber;

    /**
     * 是否错误
     */
    @TableField("is_error")
    private Boolean isError;

    /**
     * 客户姓名
     */
    @TableField("name")
    private String name;

    /**
     * 电话
     */
    @TableField("tel")
    private String tel;

    /**
     * 车架号
     */
    @TableField("vin")
    private String vin;

    /**
     * 车牌号
     */
    @TableField("license_plate_num")
    private String licensePlateNum;

    /**
     * 进销商代码
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * VOC事故车联系经销商时间
     */
    @TableField("contact_date")
    private String contactDate;

    /**
     * 与客户通话情况： 接通、未接通
     */
    @TableField("contact_situation")
    private String contactSituation;

    /**
     * VOC事故号
     */
    @TableField("accident_no")
    private String accidentNo;

    /**
     * VOC事故说明
     */
    @TableField("accident_detail")
    private String accidentDetail;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;


    /**
     * 备注
     */
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date contactDateValue;


    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
