package com.yonyou.dmscus.customer.entity.dto.goodwill;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善管理录入发票信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-19
 */

public class GoodwillInvoiceRecordDTO extends BaseDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	private String appId;

	/**
	 * 所有者代码
	 */
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	private Integer orgId;

	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 亲善单ID
	 */
	private Long goodwillApplyId;

	/**
	 * 开票通知单ID
	 */
	private Long noticeInvoiceId;

	/**
	 * 发票号
	 */
	private String invoiceNo;

	/**
	 * 发票金额（含税）
	 */
	private BigDecimal invoicePrice;

	/**
	 * 开票日期
	 */
	private Date invoiceDate;

	/**
	 * 发票类型
	 */
	private Integer invoiceType;

	/**
	 * 快递公司
	 */
	private String expressCompany;

	/**
	 * 快递单号
	 */
	private String expressNo;

	/**
	 * 快递日期
	 */
	private Date expressDate;

	/**
	 * 收到发票日期
	 */
	private Date receivedInvoiceDate;

	/**
	 * 收到发票金额
	 */
	private BigDecimal receivedInvoicePrice;

	/**
	 * 开票id
	 */
	private String invoiceId;

	/**
	 * 是否有效
	 */
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	private Date createdAt;

	/**
	 * 修改时间
	 */
	private Date updatedAt;

	public GoodwillInvoiceRecordDTO() {
		super();
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	public String getOwnerParCode() {
		return ownerParCode;
	}

	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getGoodwillApplyId() {
		return goodwillApplyId;
	}

	public void setGoodwillApplyId(Long goodwillApplyId) {
		this.goodwillApplyId = goodwillApplyId;
	}

	public Long getNoticeInvoiceId() {
		return noticeInvoiceId;
	}

	public void setNoticeInvoiceId(Long noticeInvoiceId) {
		this.noticeInvoiceId = noticeInvoiceId;
	}

	public String getInvoiceNo() {
		return invoiceNo;
	}

	public void setInvoiceNo(String invoiceNo) {
		this.invoiceNo = invoiceNo;
	}

	public BigDecimal getInvoicePrice() {
		return invoicePrice;
	}

	public void setInvoicePrice(BigDecimal invoicePrice) {
		this.invoicePrice = invoicePrice;
	}

	public Date getInvoiceDate() {
		return invoiceDate;
	}

	public void setInvoiceDate(Date invoiceDate) {
		this.invoiceDate = invoiceDate;
	}

	public Integer getInvoiceType() {
		return invoiceType;
	}

	public void setInvoiceType(Integer invoiceType) {
		this.invoiceType = invoiceType;
	}

	public String getExpressCompany() {
		return expressCompany;
	}

	public void setExpressCompany(String expressCompany) {
		this.expressCompany = expressCompany;
	}

	public String getExpressNo() {
		return expressNo;
	}

	public void setExpressNo(String expressNo) {
		this.expressNo = expressNo;
	}

	public BigDecimal getReceivedInvoicePrice() {
		return receivedInvoicePrice;
	}

	public void setReceivedInvoicePrice(BigDecimal receivedInvoicePrice) {
		this.receivedInvoicePrice = receivedInvoicePrice;
	}

	public String getInvoiceId() {
		return invoiceId;
	}

	public void setInvoiceId(String invoiceId) {
		this.invoiceId = invoiceId;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Date getExpressDate() {
		return expressDate;
	}

	public void setExpressDate(Date expressDate) {
		this.expressDate = expressDate;
	}

	public Date getReceivedInvoiceDate() {
		return receivedInvoiceDate;
	}

	public void setReceivedInvoiceDate(Date receivedInvoiceDate) {
		this.receivedInvoiceDate = receivedInvoiceDate;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	public String toString() {
		return "GoodwillInvoiceRecordDTO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", goodwillApplyId=" + goodwillApplyId
				+ ", noticeInvoiceId=" + noticeInvoiceId + ", invoiceNo=" + invoiceNo + ", invoicePrice=" + invoicePrice
				+ ", invoiceDate=" + invoiceDate + ", invoiceType=" + invoiceType + ", expressCompany=" + expressCompany
				+ ", expressNo=" + expressNo + ", expressDate=" + expressDate + ", receivedInvoiceDate="
				+ receivedInvoiceDate + ", receivedInvoicePrice=" + receivedInvoicePrice + ", invoiceId=" + invoiceId
				+ ", isValid=" + isValid + ", isDeleted=" + isDeleted + ", createdAt=" + createdAt + ", updatedAt="
				+ updatedAt + "}";
	}

	/**
	 * 将DTO 转换为PO //对某个对象属性进行赋值
	 * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
	 * 
	 * @param poClass
	 *            需要转换的poClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
		return super.transDtoToPo(poClass);
	}

	/**
	 * 将DTO 转换为PO
	 * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
	 * 
	 * @param po
	 *            需要转换的对象
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BasePO> void transDtoToPo(T po) {
		BeanMapperUtil.copyProperties(this, po, "id");
	}

}
