package com.yonyou.dmscus.customer.entity.dto.clue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * description 地址信息参数
 * <AUTHOR>
 * @date 2023/8/21 17:53
 */
@Data
@ApiModel(description = "地址信息参数")
public class AddressInfoDTO {
    /**
     * 省份id
     */
    @ApiModelProperty(value = "省份id")
    private String provinceId;
    /**
     * 省份名称
     */
    @ApiModelProperty(value = "省份名称")
    private String provinceName;
    /**
     * 城市id
     */
    @ApiModelProperty(value = "城市id")
    private String cityId;
    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String cityName;
}
