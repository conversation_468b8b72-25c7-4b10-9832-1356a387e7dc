package com.yonyou.dmscus.customer.entity.dto.busSetting;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.yonyou.dmscloud.function.utils.jsonSerializer.date.JsonDateTimeDeserializer;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 套餐主档
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */

@Data
public class SetMainFileVO {

	/**
	 * 系统ID
	 */
	private String	appId;

	/**
	 * 所有者代码
	 */
	private String	ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	private String	ownerParCode;

	/**
	 * 组织ID
	 */
	private Integer	orgId;

	/**
	 * 主键ID
	 */
	private Long	id;

	/**
	 * 套餐类别
	 */
	private Integer	setCategory;

	/**
	 * 特约店代码 厂端 VCDC
	 */
	private String	dealerCode;

	/**
	 * 套餐类型
	 */
	private Integer	setType;

	/**
	 * 套餐名称
	 */
	private String	setName;

	/**
	 * 套餐编码
	 */
	private String	setCode;

	/**
	 * 车辆用途
	 */
	private Integer	vehiclePurpose;

	/**
	 * 套餐说明
	 */
	private String	setExplain;

	private String	packageCode;

	/**
	 * 京东商品ID
	 */
	private String	jdItemId;

	private Integer	mileageDeviation;

	/**
	 * 启用日期
	 */
	@JsonDeserialize(using = JsonDateTimeDeserializer.class)
	private Date	enableDate;

	/**
	 * 停用日期
	 */
	@JsonDeserialize(using = JsonDateTimeDeserializer.class)
	private Date	discontinueDate;

	/**
	 * 车型
	 */
	private String	modelName;

	/**
	 * 车型代码
	 */
	private String	modelCode;

	/**
	 * 发动机代码,多个值用中文逗号分隔
	 */
	private String	engineCode;

	/**
	 * 帐类
	 */
	private String	accountGroup;



	/**
	 * 是否车型+发动机组合:1 是 0 否
	 */
	private Integer		isCombine;

	/**
	 * 适用工单类型
	 */
	private Integer		orderType;

	/**
	 * 套餐折扣
	 */
	private BigDecimal	setDiscount;


	/**
	 * 是否内结套餐
	 */
	private Integer		isInternalSettlement;

	/**
	 * 是否可升级套餐
	 */
	private Integer		isUpgrade;

	/**
	 * 查询页面显示 里程（"，"隔开）
	 */
	private String		vehicleMileage;

	/**
	 * 是否重复项目
	 */
	private Integer		isDuplicateItem;

	/**
	 * 套餐适用
	 */
	private String		setApply;

	/**
	 * 数据来源
	 */
	private Integer		dataSources;

	/**
	 * 是否删除
	 */
	private Boolean		isDeleted;

	/**
	 * 是否有效 是否启用
	 */
	private Integer		isValid;

	// 发动机代码 id集合
	private Object[]	engineCodeList;

	// 进厂里程
	private Object[]	mileageList;

	// 工单类型
	private Object[]	orderTypeList;

	/**
	 * 排序字段
	 */
	private Integer		sort;

	/**
	 * 预估金额
	 */
	private double totalAmount;


	// 维修项目集合
	private List<SetMainFileItemVO> laborList;





	// 维修配件集合
	private List<SetMainFilePartVO> partList;


	/**
	 * 话术
	 */
	private List<TalkskillDTO> talkskill;


	/**
	 * 套餐明细
	 */
	private List<SetMainFileDetailVO> mainFileDetail;





}
