package com.yonyou.dmscus.customer.entity.dto.faultLight;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class FaultLightOnlineOfflineDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 线索code
     */
    @ApiModelProperty("icmId")
    private Long icmId;

    /**
     * 车辆vin码
     */
    @ApiModelProperty("车辆vin码")
    private String vin;

    /**
     * 经销商code
     */
    @ApiModelProperty("经销商code")
    private String dealerCode;

    /**
     * 经销商名称
     */
    @ApiModelProperty("经销商名称")
    private String dealerName;

    /**
     * 故障id
     */
    @ApiModelProperty("故障id")
    private Long faultId;

    /**
     * 故障发生时间
     */
    @ApiModelProperty("故障发生时间")
    private Date alarmTime;

    /**
     * 故障发生城市
     */
    @ApiModelProperty("故障城市")
    private String warningName;

    /**
     * 大区id
     */
    @ApiModelProperty("大区id")
    private Long regionId;

    /**
     * 大区名称
     */
    @ApiModelProperty("大区名称")
    private String regionName;

    /**
     * 小区id
     */
    @ApiModelProperty("小区id")
    private Long cellId;

    /**
     * 小区名称
     */
    @ApiModelProperty("小区名称")
    private String cellName;

    /**
     * 城市id
     */
    @ApiModelProperty("城市id")
    private Long cityId;

    /**
     * 城市名称
     */
    @ApiModelProperty("城市名称")
    private String cityName;

    /**
     * 故障城市名称
     */
    @ApiModelProperty("故障城市名称")
    private String faultCityName;

    /**
     * 故障城市ID
     */
    @ApiModelProperty("故障城市id")
    private Long faultCityId;

    /**
     * 集团简称
     */
    @ApiModelProperty("集团简称")
    private String groupCompanyShortName;

    /**
     * 线索前状态(乐观锁)
     */
    private Integer afClueStatus;

    /**
     * 线索状态
     */
    @ApiModelProperty("线索状态")
    private Integer clueStatus;

    /**
     * 跟进状态
     */
    @ApiModelProperty("跟进状态")
    private Integer followStatus;

    /**
     * 线索生成时间
     */
    @ApiModelProperty("线索生成时间")
    private Date clueGenTime;

    /**
     * 线索下发时间
     */
    @ApiModelProperty("线索下发时间")
    private Date clueDisTime;

    /**
     * 邀约时间
     */
    @ApiModelProperty("邀约时间")
    private Date inviteTime;

    /**
     * 工单号
     */
    @ApiModelProperty("工单号")
    private String roNo;

    /**
     * 工单开始时间
     */
    @ApiModelProperty("工单开始时间")
    private Date roStartTime;

    /**
     * 工单结算时间
     */
    @ApiModelProperty("工单结算时间")
    private Date roEndTime;

    /**
     * 维修完成时间
     */
    @ApiModelProperty("维修完成时间")
    private Date repairComTime;

    /**
     * 线索关闭时间
     */
    @ApiModelProperty("线索关闭时间")
    private Date clueCloTime;

    /**
     * 线索完成时间
     */
    @ApiModelProperty("线索完成时间")
    private Date clueComTime;

    /**
     * 工单类型
     */
    @ApiModelProperty("工单类型")
    private String roType;

    /**
     * 工单金额
     */
    @ApiModelProperty("工单金额")
    private String roAmount;

    /**
     * 缺件 0.无 1.是
     */
    @ApiModelProperty("缺件 0.无 1.是")
    private Integer missParts;

    /**
     * 不修 0.无 1.是
     */
    @ApiModelProperty("不修 0.无 1.是")
    private Integer noRepair;

    /**
     * 离店亮灯 0.无 1.是
     */
    @ApiModelProperty("离店亮灯 0.无 1.是")
    private Integer lightsUp;

    /**
     * 是否有责 0.无数据 1.无责 2.有责 3.待确认
     */
    @ApiModelProperty("是否有责 0.无数据 1.无责 2.有责 3.待确认")
    private Integer wheRes;

    /**
     * 邀约响应是否超时 0.没有超时 1.超时
     */
    @ApiModelProperty("邀约响应是否超时 0.没有超时 1.超时")
    private Integer inviteOvertime;

    /**
     * 预约进店时间
     */
    @ApiModelProperty("预约进店时间")
    private Date forecastTime;

    /**
     * 是否删除:1，删除；0，未删除
     */
    @ApiModelProperty("是否删除:1，删除；0，未删除")
    private Integer isDeleted;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 数据创建时间
     */
    @ApiModelProperty("数据创建时间")
    private Date createdAt;

    /**
     * 数据创建人
     */
    @ApiModelProperty("数据创建人")
    private String createdBy;

    /**
     * 数据修改时间
     */
    @ApiModelProperty("数据修改时间")
    private Date updatedAt;

    /**
     * 数据修改人
     */
    @ApiModelProperty("数据修改人")
    private String updatedBy;

    /**
     * 创建sql人
     */
    @ApiModelProperty("创建sql人")
    private String createSqlby;

    /**
     * 更新人sql人
     */
    @ApiModelProperty("更新人sql人")
    private String updateSqlby;

    /**
     * 发送时间
     */
    @ApiModelProperty("发送时间")
    private Date sendTime;

    /**
     * RVDC数据接收时间
     */
    @ApiModelProperty("RVDC数据接收时间")
    private String ctUpload;

}
