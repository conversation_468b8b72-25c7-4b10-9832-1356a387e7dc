package com.yonyou.dmscus.customer.entity.dto.clue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * description 线索数据入参
 *
 * <AUTHOR>
 * @date 2023/8/21 15:23
 */
@Data
@ApiModel(description="数据线索参数")
public class ClueDataDTO {
    /**
     * 故障灯信息
     */
    @ApiModelProperty(value = "故障灯信息")
    private WarningInfoDTO warningInfo;
    /**
     * 客户联系信息集合
     */
    @ApiModelProperty(value = "客户联系信息集合")
    private List<ContactInfoDTO> contactInfo;
    /**
     * 保养灯信息
     */
    @ApiModelProperty(value = "保养灯信息")
    private MaintenanceLightInfoDTO maintenanceLightInfo;
    /**
     * 经销商信息集合
     */
    @ApiModelProperty(value = "经销商信息")
    private List<DealerInfoDTO> dealerInfo;
    /**
    * 车辆信息
    */
    @ApiModelProperty(value = "车辆信息")
    private CarInfoDTO carInfo;
    /**
    * 地址信息
    */
    @ApiModelProperty(value = "地址信息")
    private AddressInfoDTO addressInfo;
    /**
    * 零附件信息
    */
    @ApiModelProperty(value = "零附件信息")
    private ZeroAttInfoDTO zeroAttInfo;
    /**
     * 外呼信息
     */
    @ApiModelProperty(value = "外呼信息")
    private CallInfoDTO callInfo;
}
