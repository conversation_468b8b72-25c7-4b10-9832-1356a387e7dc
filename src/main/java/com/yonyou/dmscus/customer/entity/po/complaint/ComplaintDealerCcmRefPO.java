package com.yonyou.dmscus.customer.entity.po.complaint;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

    import com.yonyou.dmscloud.framework.base.po.BasePO;

    
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;

import java.util.Date;

/**
 * <p>
 * 客户投诉进销商和CCM复杂人对应关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
@TableName("tt_complaint_dealer_ccm_ref")
public class ComplaintDealerCcmRefPO extends BasePO<ComplaintDealerCcmRefPO> {

    private static final long serialVersionUID=1L;
    
    /**
     * 系统ID
     */
    @TableField("app_id")
        private String appId;
    
    /**
     * 所有者代码 
     */
    @TableField("owner_code")
        private String ownerCode;
    
    /**
     * 所有者的父组织代码 
     */
    @TableField("owner_par_code")
        private String ownerParCode;
    
    /**
     * 组织ID
     */
    @TableField("org_id")
        private Integer orgId;
        
    /**
     * 主键ID
     */
            @TableId(value = "id", type = IdType.AUTO)
                        private Long id;
    
    /**
     * 区域
     */
    @TableField("region")
        private String region;
    /**
     * 区域ID
     */
    @TableField("region_id")
    private  Long regionId;
    
    /**
     * 区域经理
     */
    @TableField("region_manager")
        private String regionManager;
    /**
     * 区域经理ID
     */
    @TableField("region_manager_id")
    private  Long regionManagerId;
    
    /**
     * 集团
     */
    @TableField("bloc")
        private String bloc;
    
    /**
     * 进销商代码
     */
    @TableField("dealer_code")
        private String dealerCode;
    
    /**
     * 进销商名称
     */
    @TableField("dealer_name")
        private String dealerName;
    
    /**
     * CCM负责人
     */
    @TableField("ccm_man")
        private String ccmMan;
    
    /**
     * CCM负责人ID
     */
    @TableField("ccm_man_id")
        private String ccmManId;
    
    /**
     * 数据来源 
     */
    @TableField("data_sources")
        private Integer dataSources;
    
    /**
     * 是否删除
     */
    @TableField("is_deleted")
        private Boolean isDeleted;
    
    /**
     * 是否有效 
     */
    @TableField("is_valid")
        private Integer isValid;
    
    /**
     * 创建时间
     */
    @TableField("created_at")
        private Date createdAt;
    
    /**
     * 更新时间
     */
    @TableField("updated_at")
        private Date updatedAt;

    public ComplaintDealerCcmRefPO(){
        super();
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public Long getRegionManagerId() {
        return regionManagerId;
    }

    public void setRegionManagerId(Long regionManagerId) {
        this.regionManagerId = regionManagerId;
    }


                    
    public String getOwnerCode(){
        return ownerCode;
    }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
            }
                    

                    
    public Integer getOrgId(){
        return orgId;
    }

        public void setOrgId(Integer orgId) {
            this.orgId = orgId;
            }
                    
    public Long getId(){
        return id;
    }

        public void setId(Long id) {
            this.id = id;
            }
                    
    public String getRegion(){
        return region;
    }

        public void setRegion(String region) {
            this.region = region;
            }
                    
    public String getRegionManager(){
        return regionManager;
    }

        public void setRegionManager(String regionManager) {
            this.regionManager = regionManager;
            }
                    
    public String getBloc(){
        return bloc;
    }

        public void setBloc(String bloc) {
            this.bloc = bloc;
            }
                    
    public String getDealerCode(){
        return dealerCode;
    }

        public void setDealerCode(String dealerCode) {
            this.dealerCode = dealerCode;
            }
                    
    public String getDealerName(){
        return dealerName;
    }

        public void setDealerName(String dealerName) {
            this.dealerName = dealerName;
            }
                    
    public String getCcmMan(){
        return ccmMan;
    }

        public void setCcmMan(String ccmMan) {
            this.ccmMan = ccmMan;
            }
                    
    public String getCcmManId(){
        return ccmManId;
    }

        public void setCcmManId(String ccmManId) {
            this.ccmManId = ccmManId;
            }
                    
    public Integer getDataSources(){
        return dataSources;
    }

        public void setDataSources(Integer dataSources) {
            this.dataSources = dataSources;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }

        public void setIsDeleted(Boolean isDeleted) {
            this.isDeleted = isDeleted;
            }
                    
    public Integer getIsValid(){
        return isValid;
    }

        public void setIsValid(Integer isValid) {
            this.isValid = isValid;
            }
                    

    
    @Override
    protected Serializable pkVal(){
            return this.id;
        }

@Override
    public String toString(){
        return"ComplaintDealerCcmRefPO{" +
                            "appId=" + appId +
                                    ", ownerCode=" + ownerCode +
                                    ", ownerParCode=" + ownerParCode +
                                    ", orgId=" + orgId +
                                    ", id=" + id +
                                    ", region=" + region +
                                    ", regionManager=" + regionManager +
                                    ", bloc=" + bloc +
                                    ", dealerCode=" + dealerCode +
                                    ", dealerName=" + dealerName +
                                    ", ccmMan=" + ccmMan +
                                    ", ccmManId=" + ccmManId +
                                    ", dataSources=" + dataSources +
                                    ", isDeleted=" + isDeleted +
                                    ", isValid=" + isValid +
                                    ", createdAt=" + createdAt +
                                    ", updatedAt=" + updatedAt +
                    "}";
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"id");
    }

}
