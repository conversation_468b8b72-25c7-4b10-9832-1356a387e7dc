package com.yonyou.dmscus.customer.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("车辆查询DTO")
public class VehicleQueryParamsDTO {

    @ApiModelProperty(value = "车牌号", name="license")
    private String license;

    @ApiModelProperty(value = "VIN", name="vin")
    private String vin;

    @ApiModelProperty(value = "经销商代码",name="ownerCode")
    private String ownerCode;


}
