package com.yonyou.dmscus.customer.entity.po.complaint;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFinalPartInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairPartInfoDTO;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;




    /**
     * <p>
     * 亲善预申请单
     * </p>
     *
     * <AUTHOR>
     * @since 2020-05-06
     */

    public class GoodwillApplyInfoPO extends BaseDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 系统ID
         */
        private String appId;

        /**
         * 所有者代码
         */
        private String ownerCode;

        /**
         * 所有者的父组织代码
         */
        private String ownerParCode;

        /**
         * 组织ID
         */
        private Integer orgId;

        /**
         * 预申请单表id
         */
        private Long id;
        /**
         * 预申请单表亲善信息Id
         */
        private Long materialId;

        /**
         * 预申请单号
         */
        private String applyNo;

        /**
         * 申请经销商
         */
        private String dealerCode;

        /**
         * 申请经销商名称
         */
        private String dealerName;
        /**
         * 区域ID
         */
        private Integer areaManageId;
        /**
         * 区域-区域经理
         */
        private String areaManage;
        /**
         * 大区ID
         */
        private Integer bigAreaId;
        /**
         * 大区名称
         */
        private String bigArea;
        /**
         * 集团ID
         */
        private Integer blocId;
        /**
         * 集团
         */
        private String bloc;

        /**
         * 申请人
         */
        private String applyPerson;

        /**
         * 审核类型
         */
        private Integer auditType;

        /**
         * 亲善性质
         */
        private Integer goodwillNature;

        /**
         * 申请时间
         */

        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date applyTime;

        /**
         * 投诉故障
         */
        private String complaintFalut;

        /**
         * 投诉单ID
         */
        private String complaintId;
        /**
         * 投诉单ID
         */
        private Date complaintDate;

        /**
         * 用车情况
         */
        private Integer vehicleUse;

        /**
         * 销售经销商
         */
        private String salesDealer;

        /**
         * 申请金额
         */
        private BigDecimal applyAmount;

        /**
         * 审批金额
         */
        private BigDecimal auditAmount;

        /**
         * 审批金额
         */
        private BigDecimal auditPrice;

        /**
         * 结算金额
         */
        private BigDecimal settlementAmount;

        /**
         * 通知开票金额
         */
        private BigDecimal invoiceAmount;

        /**
         * 亲善单状态
         */
        private Integer goodwillStatus;

        /**
         * 客户痛点
         */
        private List<Integer> customerPainVue;

        /**
         * 客户痛点
         */
        private String customerPain;

        /**
         * VIN
         */
        private String vin;

        /**
         * 车牌号
         */
        private String license;

        /**
         * 客户姓名
         */
        private String customerName;

        /**
         * 客户电话
         */
        private String customerMobile;

        /**
         * 里程
         */
        private String mileage;

        /**
         * 车型
         */
        private String model;

        /**
         * 购车日期
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date buyCarDate;

        /**
         * 保修开始日期
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date warrantyStartDate;

        /**
         * 是否延保
         */
        private Integer isExtendWarranty;

        /**
         * 延保名称
         */
        private String extendWarrantyName;

        /**
         * 延保开始日期
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date extendWarrantyStartDate;

        /**
         * 延保结束日期
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date extendWarrantyEndDate;

        /**
         * 保养成本
         */
        private BigDecimal maintainCost;

        /**
         * 延保成本
         */
        private BigDecimal extendWarrantyCost;

        /**
         * 附件精品成本
         */
        private BigDecimal accessoryCost;

        /**
         * 代金券成本
         */
        private BigDecimal voucherCost;

        /**
         * 代步车/相关利益
         */
        private BigDecimal walkingCarPrice;

        /**
         * 沃世界积分
         */
        private BigDecimal volvoIntegral;

        /**
         * 退换车
         */
        private BigDecimal returnChangeCarPrice;

        /**
         * 其他
         */
        private BigDecimal otherPrice;

        /**
         * 成本总计
         */
        private BigDecimal costTotal;

        /**
         * 客户支付
         */
        private BigDecimal customerPay;

        /**
         * 经销商承担
         */
        private BigDecimal dealerUndertake;

        /**
         * volvo支持亲善金额
         */
        private BigDecimal volvoSupportGoodwillAmount;

        /**
         * 备注
         */
        private String remark;

        /**
         * 预申请附件
         */
        private String applyFile;

        /**
         * 亲善成本统计
         */
        private String costStatisticsFile;

        /**
         * 亲善成本截图
         */
        private String costScreenshotFile;

        /**
         * 故障维修工单/环检单
         */
        private String ringCheckFile;

        /**
         * 故障维修领料单
         */
        private String troubleRepairRequisitionFile;

        /**
         * 亲善安装工单/领料单
         */
        private String workOrderFile;

        /**
         * 情况说明和解协议
         */
        private String situationSettlementAgreementFile;

        /**
         * 退换车补充材料
         */
        private String supplementaryMaterialFile;

        /**
         * 管理层审核邮件-VP
         */
        private String managementReviewEmailVpFile;

        /**
         * 管理层审核邮件-CEO
         */
        private String managementReviewEmailCeoFile;

        /**
         * 费用更新附件
         */
        private String costUpdateFile;

        /**
         * VCDC其他附件
         */
        private String vcdcElseFile;

        /**
         * 客户身份证明
         */
        private String customerIdentification;

        /**
         * 其他附件
         */
        private String elseFile;

        /**
         * 提交时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date commitTime;

        /**
         * 是否有效
         */
        private Integer isValid;

        /**
         * 是否删除:1,删除；0,未删除
         */
        private Boolean isDeleted;

        /**
         * 创建时间
         */
        private Date createdAt;

        /**
         * 修改时间
         */
        private Date updatedAt;

        /**
         * 客户背景
         */
        private String customerBackground;

        /**
         * 客户背景英文
         */
        private String customerBackgroundEn;

        /**
         * 投诉原因及处理经过
         */
        private String reasonAndDispose;

        /**
         * 投诉原因及处理经过英文
         */
        private String reasonAndDisposeEn;

        /**
         * 维修解决方案
         */
        private String repairSolution;

        /**
         * 维修解决方案英文
         */
        private String repairSolutionEn;

        /**
         * 客户要求
         */
        private String customerRequire;

        /**
         * 客户要求英文
         */
        private String customerRequireEn;

        /**
         * 潜在风险
         */
        private String potentialRisk;

        /**
         * 潜在风险英文
         */
        private String potentialRiskEn;

        /**
         * VR或TJ号英文
         */
        private String vrOrTjNoEn;

        /**
         * VR或TJ号
         */
        private String vrOrTjNo;

        /**
         * 商务亲善申请详情英文
         */
        private String businessGoodwillApplyDetailEn;

        /**
         * 商务亲善申请详情
         */
        private String businessGoodwillApplyDetail;

        /**
         * 维修记录
         */
        private List<GoodwillApplyRepairInfoDTO> goodwillApplyRepairInfoDTO;

        /**
         * 零配件信息
         */
        private List<GoodwillApplyRepairPartInfoDTO> goodwillApplyRepairPartInfoDTO;

        /**
         * 亲善类型及金额-保养成本
         */
        private List<GoodwillApplyFinalPartInfoDTO> maintainCostTable;

        /**
         * 亲善类型及金额-延保成本
         */
        private List<GoodwillApplyFinalPartInfoDTO> extendWarrantyCostTable;

        /**
         * 亲善类型及金额-附件精品成本
         */
        private List<GoodwillApplyFinalPartInfoDTO> accessoryCostTable;

        private Integer isMaintainChange;
        private Integer isExtendWarrantyChange;
        private Integer isAccessoryChange;

        /**
         * 预申请通过时间
         */
        private Date passTime;

        /**
         * 材料审核通过时间
         */
        private Date materialPassTime;
        /**
         * 申请时间
         */
        private Date applyStartTime;
        private Date applyEndTime;
        /**
         * 预申请通过时间
         */
        private Date passStartTime;
        private Date passEndTime;
        /**
         * 申请金额
         */
        private BigDecimal applyStartAmount;
        private BigDecimal applyEndAmount;

        /**
         * 开票时间
         */
        private Date invoiceStartDate;
        private Date invoiceEndDate;
        private Date invoiceDate;
        /**
         * 通知开票时间
         */
        private Date noticeInvoiceStartDate;
        private Date noticeInvoiceEndDate;
        private Date noticeInvoiceDate;

        /**
         * 更新时间
         */
        private Date updatedStartAt;
        private Date updatedEndAt;

        /**
         * 用户id
         */
        private Long userId;

        /**
         * 预申请已审核流程数
         */
        private Integer applyCount;
        /**
         * 材料已审核流程数
         */
        private Integer materialCount;

        public Date getMaterialPassTime() {
            return materialPassTime;
        }

        public void setMaterialPassTime(Date materialPassTime) {
            this.materialPassTime = materialPassTime;
        }

        public Integer getApplyCount() {
            return applyCount;
        }

        public void setApplyCount(Integer applyCount) {
            this.applyCount = applyCount;
        }

        public Integer getMaterialCount() {
            return materialCount;
        }

        public void setMaterialCount(Integer materialCount) {
            this.materialCount = materialCount;
        }

        public Integer getBigAreaId() {
            return bigAreaId;
        }

        public void setBigAreaId(Integer bigAreaId) {
            this.bigAreaId = bigAreaId;
        }

        public String getBigArea() {
            return bigArea;
        }

        public void setBigArea(String bigArea) {
            this.bigArea = bigArea;
        }

        public BigDecimal getAuditPrice() {
            return auditPrice;
        }

        public void setAuditPrice(BigDecimal auditPrice) {
            this.auditPrice = auditPrice;
        }

        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public Date getApplyStartTime() {
            return applyStartTime;
        }

        public void setApplyStartTime(Date applyStartTime) {
            this.applyStartTime = applyStartTime;
        }

        public Date getApplyEndTime() {
            return applyEndTime;
        }

        public void setApplyEndTime(Date applyEndTime) {
            this.applyEndTime = applyEndTime;
        }

        public Date getPassStartTime() {
            return passStartTime;
        }

        public void setPassStartTime(Date passStartTime) {
            this.passStartTime = passStartTime;
        }

        public Date getPassEndTime() {
            return passEndTime;
        }

        public void setPassEndTime(Date passEndTime) {
            this.passEndTime = passEndTime;
        }

        public BigDecimal getApplyStartAmount() {
            return applyStartAmount;
        }

        public void setApplyStartAmount(BigDecimal applyStartAmount) {
            this.applyStartAmount = applyStartAmount;
        }

        public BigDecimal getApplyEndAmount() {
            return applyEndAmount;
        }

        public void setApplyEndAmount(BigDecimal applyEndAmount) {
            this.applyEndAmount = applyEndAmount;
        }

        public Date getInvoiceStartDate() {
            return invoiceStartDate;
        }

        public void setInvoiceStartDate(Date invoiceStartDate) {
            this.invoiceStartDate = invoiceStartDate;
        }

        public Date getInvoiceEndDate() {
            return invoiceEndDate;
        }

        public void setInvoiceEndDate(Date invoiceEndDate) {
            this.invoiceEndDate = invoiceEndDate;
        }

        public Date getInvoiceDate() {
            return invoiceDate;
        }

        public void setInvoiceDate(Date invoiceDate) {
            this.invoiceDate = invoiceDate;
        }

        public Date getNoticeInvoiceStartDate() {
            return noticeInvoiceStartDate;
        }

        public void setNoticeInvoiceStartDate(Date noticeInvoiceStartDate) {
            this.noticeInvoiceStartDate = noticeInvoiceStartDate;
        }

        public Date getNoticeInvoiceEndDate() {
            return noticeInvoiceEndDate;
        }

        public void setNoticeInvoiceEndDate(Date noticeInvoiceEndDate) {
            this.noticeInvoiceEndDate = noticeInvoiceEndDate;
        }

        public Date getNoticeInvoiceDate() {
            return noticeInvoiceDate;
        }

        public void setNoticeInvoiceDate(Date noticeInvoiceDate) {
            this.noticeInvoiceDate = noticeInvoiceDate;
        }

        public Date getUpdatedStartAt() {
            return updatedStartAt;
        }

        public void setUpdatedStartAt(Date updatedStartAt) {
            this.updatedStartAt = updatedStartAt;
        }

        public Date getUpdatedEndAt() {
            return updatedEndAt;
        }

        public void setUpdatedEndAt(Date updatedEndAt) {
            this.updatedEndAt = updatedEndAt;
        }

        public Date getPassTime() {
            return passTime;
        }

        public void setPassTime(Date passTime) {
            this.passTime = passTime;
        }

        public Integer getIsMaintainChange() {
            return isMaintainChange;
        }

        public void setIsMaintainChange(Integer isMaintainChange) {
            this.isMaintainChange = isMaintainChange;
        }

        public Integer getIsExtendWarrantyChange() {
            return isExtendWarrantyChange;
        }

        public void setIsExtendWarrantyChange(Integer isExtendWarrantyChange) {
            this.isExtendWarrantyChange = isExtendWarrantyChange;
        }

        public Integer getIsAccessoryChange() {
            return isAccessoryChange;
        }

        public void setIsAccessoryChange(Integer isAccessoryChange) {
            this.isAccessoryChange = isAccessoryChange;
        }

        public List<GoodwillApplyFinalPartInfoDTO> getMaintainCostTable() {
            return maintainCostTable;
        }

        public void setMaintainCostTable(List<GoodwillApplyFinalPartInfoDTO> maintainCostTable) {
            this.maintainCostTable = maintainCostTable;
        }

        public List<GoodwillApplyFinalPartInfoDTO> getExtendWarrantyCostTable() {
            return extendWarrantyCostTable;
        }

        public void setExtendWarrantyCostTable(List<GoodwillApplyFinalPartInfoDTO> extendWarrantyCostTable) {
            this.extendWarrantyCostTable = extendWarrantyCostTable;
        }

        public List<GoodwillApplyFinalPartInfoDTO> getAccessoryCostTable() {
            return accessoryCostTable;
        }

        public void setAccessoryCostTable(List<GoodwillApplyFinalPartInfoDTO> accessoryCostTable) {
            this.accessoryCostTable = accessoryCostTable;
        }

        public String getCustomerBackground() {
            return customerBackground;
        }

        public void setCustomerBackground(String customerBackground) {
            this.customerBackground = customerBackground;
        }

        public String getCustomerBackgroundEn() {
            return customerBackgroundEn;
        }

        public void setCustomerBackgroundEn(String customerBackgroundEn) {
            this.customerBackgroundEn = customerBackgroundEn;
        }

        public String getReasonAndDispose() {
            return reasonAndDispose;
        }

        public void setReasonAndDispose(String reasonAndDispose) {
            this.reasonAndDispose = reasonAndDispose;
        }

        public String getReasonAndDisposeEn() {
            return reasonAndDisposeEn;
        }

        public void setReasonAndDisposeEn(String reasonAndDisposeEn) {
            this.reasonAndDisposeEn = reasonAndDisposeEn;
        }

        public String getRepairSolution() {
            return repairSolution;
        }

        public void setRepairSolution(String repairSolution) {
            this.repairSolution = repairSolution;
        }

        public String getRepairSolutionEn() {
            return repairSolutionEn;
        }

        public void setRepairSolutionEn(String repairSolutionEn) {
            this.repairSolutionEn = repairSolutionEn;
        }

        public String getCustomerRequire() {
            return customerRequire;
        }

        public void setCustomerRequire(String customerRequire) {
            this.customerRequire = customerRequire;
        }

        public String getCustomerRequireEn() {
            return customerRequireEn;
        }

        public void setCustomerRequireEn(String customerRequireEn) {
            this.customerRequireEn = customerRequireEn;
        }

        public String getPotentialRisk() {
            return potentialRisk;
        }

        public void setPotentialRisk(String potentialRisk) {
            this.potentialRisk = potentialRisk;
        }

        public String getPotentialRiskEn() {
            return potentialRiskEn;
        }

        public void setPotentialRiskEn(String potentialRiskEn) {
            this.potentialRiskEn = potentialRiskEn;
        }

        public String getVrOrTjNoEn() {
            return vrOrTjNoEn;
        }

        public void setVrOrTjNoEn(String vrOrTjNoEn) {
            this.vrOrTjNoEn = vrOrTjNoEn;
        }

        public String getVrOrTjNo() {
            return vrOrTjNo;
        }

        public void setVrOrTjNo(String vrOrTjNo) {
            this.vrOrTjNo = vrOrTjNo;
        }

        public String getBusinessGoodwillApplyDetailEn() {
            return businessGoodwillApplyDetailEn;
        }

        public void setBusinessGoodwillApplyDetailEn(String businessGoodwillApplyDetailEn) {
            this.businessGoodwillApplyDetailEn = businessGoodwillApplyDetailEn;
        }

        public String getBusinessGoodwillApplyDetail() {
            return businessGoodwillApplyDetail;
        }

        public void setBusinessGoodwillApplyDetail(String businessGoodwillApplyDetail) {
            this.businessGoodwillApplyDetail = businessGoodwillApplyDetail;
        }

        public List<GoodwillApplyRepairInfoDTO> getGoodwillApplyRepairInfoDTO() {
            return goodwillApplyRepairInfoDTO;
        }

        public void setGoodwillApplyRepairInfoDTO(List<GoodwillApplyRepairInfoDTO> goodwillApplyRepairInfoDTO) {
            this.goodwillApplyRepairInfoDTO = goodwillApplyRepairInfoDTO;
        }

        public List<GoodwillApplyRepairPartInfoDTO> getGoodwillApplyRepairPartInfoDTO() {
            return goodwillApplyRepairPartInfoDTO;
        }

        public void setGoodwillApplyRepairPartInfoDTO(List<GoodwillApplyRepairPartInfoDTO> goodwillApplyRepairPartInfoDTO) {
            this.goodwillApplyRepairPartInfoDTO = goodwillApplyRepairPartInfoDTO;
        }

        public GoodwillApplyInfoPO() {
            super();
        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getOwnerCode() {
            return ownerCode;
        }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
        }

        public String getOwnerParCode() {
            return ownerParCode;
        }

        public void setOwnerParCode(String ownerParCode) {
            this.ownerParCode = ownerParCode;
        }

        public Integer getOrgId() {
            return orgId;
        }

        public void setOrgId(Integer orgId) {
            this.orgId = orgId;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getApplyNo() {
            return applyNo;
        }

        public void setApplyNo(String applyNo) {
            this.applyNo = applyNo;
        }

        public String getDealerCode() {
            return dealerCode;
        }

        public void setDealerCode(String dealerCode) {
            this.dealerCode = dealerCode;
        }

        public Integer getAuditType() {
            return auditType;
        }

        public void setAuditType(Integer auditType) {
            this.auditType = auditType;
        }

        public Integer getGoodwillNature() {
            return goodwillNature;
        }

        public void setGoodwillNature(Integer goodwillNature) {
            this.goodwillNature = goodwillNature;
        }

        public String getComplaintFalut() {
            return complaintFalut;
        }

        public void setComplaintFalut(String complaintFalut) {
            this.complaintFalut = complaintFalut;
        }

        public String getComplaintId() {
            return complaintId;
        }

        public void setComplaintId(String complaintId) {
            this.complaintId = complaintId;
        }

        public Date getComplaintDate() {
            return complaintDate;
        }

        public void setComplaintDate(Date complaintDate) {
            this.complaintDate = complaintDate;
        }

        public Integer getVehicleUse() {
            return vehicleUse;
        }

        public void setVehicleUse(Integer vehicleUse) {
            this.vehicleUse = vehicleUse;
        }

        public String getSalesDealer() {
            return salesDealer;
        }

        public void setSalesDealer(String salesDealer) {
            this.salesDealer = salesDealer;
        }

        public BigDecimal getApplyAmount() {
            return applyAmount;
        }

        public void setApplyAmount(BigDecimal applyAmount) {
            this.applyAmount = applyAmount;
        }

        public BigDecimal getAuditAmount() {
            return auditAmount;
        }

        public void setAuditAmount(BigDecimal auditAmount) {
            this.auditAmount = auditAmount;
        }

        public BigDecimal getSettlementAmount() {
            return settlementAmount;
        }

        public void setSettlementAmount(BigDecimal settlementAmount) {
            this.settlementAmount = settlementAmount;
        }

        public BigDecimal getInvoiceAmount() {
            return invoiceAmount;
        }

        public void setInvoiceAmount(BigDecimal invoiceAmount) {
            this.invoiceAmount = invoiceAmount;
        }

        public Integer getGoodwillStatus() {
            return goodwillStatus;
        }

        public void setGoodwillStatus(Integer goodwillStatus) {
            this.goodwillStatus = goodwillStatus;
        }

        public List<Integer> getCustomerPainVue() {
            return customerPainVue;
        }

        public void setCustomerPainVue(List<Integer> customerPainVue) {
            this.customerPainVue = customerPainVue;
        }

        public String getCustomerPain() {
            return customerPain;
        }

        public void setCustomerPain(String customerPain) {
            this.customerPain = customerPain;
        }

        public String getVin() {
            return vin;
        }

        public void setVin(String vin) {
            this.vin = vin;
        }

        public String getLicense() {
            return license;
        }

        public void setLicense(String license) {
            this.license = license;
        }

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }

        public String getCustomerMobile() {
            return customerMobile;
        }

        public void setCustomerMobile(String customerMobile) {
            this.customerMobile = customerMobile;
        }

        public String getMileage() {
            return mileage;
        }

        public void setMileage(String mileage) {
            this.mileage = mileage;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        public Integer getIsExtendWarranty() {
            return isExtendWarranty;
        }

        public void setIsExtendWarranty(Integer isExtendWarranty) {
            this.isExtendWarranty = isExtendWarranty;
        }

        public String getExtendWarrantyName() {
            return extendWarrantyName;
        }

        public void setExtendWarrantyName(String extendWarrantyName) {
            this.extendWarrantyName = extendWarrantyName;
        }

        public BigDecimal getMaintainCost() {
            return maintainCost;
        }

        public void setMaintainCost(BigDecimal maintainCost) {
            this.maintainCost = maintainCost;
        }

        public BigDecimal getExtendWarrantyCost() {
            return extendWarrantyCost;
        }

        public void setExtendWarrantyCost(BigDecimal extendWarrantyCost) {
            this.extendWarrantyCost = extendWarrantyCost;
        }

        public BigDecimal getAccessoryCost() {
            return accessoryCost;
        }

        public void setAccessoryCost(BigDecimal accessoryCost) {
            this.accessoryCost = accessoryCost;
        }

        public BigDecimal getVoucherCost() {
            return voucherCost;
        }

        public void setVoucherCost(BigDecimal voucherCost) {
            this.voucherCost = voucherCost;
        }

        public BigDecimal getWalkingCarPrice() {
            return walkingCarPrice;
        }

        public void setWalkingCarPrice(BigDecimal walkingCarPrice) {
            this.walkingCarPrice = walkingCarPrice;
        }

        public BigDecimal getVolvoIntegral() {
            return volvoIntegral;
        }

        public void setVolvoIntegral(BigDecimal volvoIntegral) {
            this.volvoIntegral = volvoIntegral;
        }

        public BigDecimal getReturnChangeCarPrice() {
            return returnChangeCarPrice;
        }

        public void setReturnChangeCarPrice(BigDecimal returnChangeCarPrice) {
            this.returnChangeCarPrice = returnChangeCarPrice;
        }

        public BigDecimal getOtherPrice() {
            return otherPrice;
        }

        public void setOtherPrice(BigDecimal otherPrice) {
            this.otherPrice = otherPrice;
        }

        public BigDecimal getCostTotal() {
            return costTotal;
        }

        public void setCostTotal(BigDecimal costTotal) {
            this.costTotal = costTotal;
        }

        public BigDecimal getCustomerPay() {
            return customerPay;
        }

        public void setCustomerPay(BigDecimal customerPay) {
            this.customerPay = customerPay;
        }

        public BigDecimal getDealerUndertake() {
            return dealerUndertake;
        }

        public void setDealerUndertake(BigDecimal dealerUndertake) {
            this.dealerUndertake = dealerUndertake;
        }

        public BigDecimal getVolvoSupportGoodwillAmount() {
            return volvoSupportGoodwillAmount;
        }

        public void setVolvoSupportGoodwillAmount(BigDecimal volvoSupportGoodwillAmount) {
            this.volvoSupportGoodwillAmount = volvoSupportGoodwillAmount;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getApplyFile() {
            return applyFile;
        }

        public void setApplyFile(String applyFile) {
            this.applyFile = applyFile;
        }

        public String getCostStatisticsFile() {
            return costStatisticsFile;
        }

        public void setCostStatisticsFile(String costStatisticsFile) {
            this.costStatisticsFile = costStatisticsFile;
        }

        public String getCostScreenshotFile() {
            return costScreenshotFile;
        }

        public void setCostScreenshotFile(String costScreenshotFile) {
            this.costScreenshotFile = costScreenshotFile;
        }

        public String getRingCheckFile() {
            return ringCheckFile;
        }

        public void setRingCheckFile(String ringCheckFile) {
            this.ringCheckFile = ringCheckFile;
        }

        public String getTroubleRepairRequisitionFile() {
            return troubleRepairRequisitionFile;
        }

        public void setTroubleRepairRequisitionFile(String troubleRepairRequisitionFile) {
            this.troubleRepairRequisitionFile = troubleRepairRequisitionFile;
        }

        public String getWorkOrderFile() {
            return workOrderFile;
        }

        public void setWorkOrderFile(String workOrderFile) {
            this.workOrderFile = workOrderFile;
        }

        public String getSituationSettlementAgreementFile() {
            return situationSettlementAgreementFile;
        }

        public void setSituationSettlementAgreementFile(String situationSettlementAgreementFile) {
            this.situationSettlementAgreementFile = situationSettlementAgreementFile;
        }

        public String getSupplementaryMaterialFile() {
            return supplementaryMaterialFile;
        }

        public void setSupplementaryMaterialFile(String supplementaryMaterialFile) {
            this.supplementaryMaterialFile = supplementaryMaterialFile;
        }

        public String getManagementReviewEmailVpFile() {
            return managementReviewEmailVpFile;
        }

        public void setManagementReviewEmailVpFile(String managementReviewEmailVpFile) {
            this.managementReviewEmailVpFile = managementReviewEmailVpFile;
        }

        public String getManagementReviewEmailCeoFile() {
            return managementReviewEmailCeoFile;
        }

        public void setManagementReviewEmailCeoFile(String managementReviewEmailCeoFile) {
            this.managementReviewEmailCeoFile = managementReviewEmailCeoFile;
        }

        public String getCostUpdateFile() {
            return costUpdateFile;
        }

        public void setCostUpdateFile(String costUpdateFile) {
            this.costUpdateFile = costUpdateFile;
        }

        public String getVcdcElseFile() {
            return vcdcElseFile;
        }

        public void setVcdcElseFile(String vcdcElseFile) {
            this.vcdcElseFile = vcdcElseFile;
        }

        public String getCustomerIdentification() {
            return customerIdentification;
        }

        public void setCustomerIdentification(String customerIdentification) {
            this.customerIdentification = customerIdentification;
        }

        public String getElseFile() {
            return elseFile;
        }

        public void setElseFile(String elseFile) {
            this.elseFile = elseFile;
        }

        public Integer getIsValid() {
            return isValid;
        }

        public void setIsValid(Integer isValid) {
            this.isValid = isValid;
        }

        public Boolean getIsDeleted() {
            return isDeleted;
        }

        public void setIsDeleted(Boolean isDeleted) {
            this.isDeleted = isDeleted;
        }

        public Date getApplyTime() {
            return applyTime;
        }

        public void setApplyTime(Date applyTime) {
            this.applyTime = applyTime;
        }

        public Date getBuyCarDate() {
            return buyCarDate;
        }

        public void setBuyCarDate(Date buyCarDate) {
            this.buyCarDate = buyCarDate;
        }

        public Date getWarrantyStartDate() {
            return warrantyStartDate;
        }

        public void setWarrantyStartDate(Date warrantyStartDate) {
            this.warrantyStartDate = warrantyStartDate;
        }

        public Date getExtendWarrantyStartDate() {
            return extendWarrantyStartDate;
        }

        public void setExtendWarrantyStartDate(Date extendWarrantyStartDate) {
            this.extendWarrantyStartDate = extendWarrantyStartDate;
        }

        public Date getExtendWarrantyEndDate() {
            return extendWarrantyEndDate;
        }

        public void setExtendWarrantyEndDate(Date extendWarrantyEndDate) {
            this.extendWarrantyEndDate = extendWarrantyEndDate;
        }

        public Date getCommitTime() {
            return commitTime;
        }

        public void setCommitTime(Date commitTime) {
            this.commitTime = commitTime;
        }

        public Date getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(Date createdAt) {
            this.createdAt = createdAt;
        }

        public Date getUpdatedAt() {
            return updatedAt;
        }

        public void setUpdatedAt(Date updatedAt) {
            this.updatedAt = updatedAt;
        }

        public Long getMaterialId() {
            return materialId;
        }

        public void setMaterialId(Long materialId) {
            this.materialId = materialId;
        }

        public String getDealerName() {
            return dealerName;
        }

        public void setDealerName(String dealerName) {
            this.dealerName = dealerName;
        }

        public Integer getAreaManageId() {
            return areaManageId;
        }

        public void setAreaManageId(Integer areaManageId) {
            this.areaManageId = areaManageId;
        }

        public String getAreaManage() {
            return areaManage;
        }

        public void setAreaManage(String areaManage) {
            this.areaManage = areaManage;
        }

        public Integer getBlocId() {
            return blocId;
        }

        public void setBlocId(Integer blocId) {
            this.blocId = blocId;
        }

        public String getBloc() {
            return bloc;
        }

        public void setBloc(String bloc) {
            this.bloc = bloc;
        }

        public String getApplyPerson() {
            return applyPerson;
        }

        public void setApplyPerson(String applyPerson) {
            this.applyPerson = applyPerson;
        }

        @Override
        public String toString() {
            return "GoodwillApplyInfoDTO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
                    + ownerParCode + ", orgId=" + orgId + ", id=" + id + ", applyNo=" + applyNo + ", dealerCode="
                    + dealerCode + ", dealerName=" + dealerName + ", bloc=" + bloc + ", blocId=" + blocId
                    + ", areaManageId=" + areaManageId + ", areaManage=" + areaManage + ", auditType=" + auditType
                    + ", goodwillNature=" + goodwillNature + ", applyTime=" + applyTime + ", complaintFalut="
                    + complaintFalut + ", complaintId=" + complaintId + ", complaintDate=" + complaintDate + ", vehicleUse="
                    + vehicleUse + ", salesDealer=" + salesDealer + ", applyAmount=" + applyAmount + ", auditAmount="
                    + auditAmount + ", settlementAmount=" + settlementAmount + ", invoiceAmount=" + invoiceAmount
                    + ", goodwillStatus=" + goodwillStatus + ", customerPain=" + customerPain + ", vin=" + vin
                    + ", license=" + license + ", customerName=" + customerName + ", customerMobile=" + customerMobile
                    + ", mileage=" + mileage + ", model=" + model + ", buyCarDate=" + buyCarDate + ", warrantyStartDate="
                    + warrantyStartDate + ", isExtendWarranty=" + isExtendWarranty + ", extendWarrantyName="
                    + extendWarrantyName + ", extendWarrantyStartDate=" + extendWarrantyStartDate
                    + ", extendWarrantyEndDate=" + extendWarrantyEndDate + ", maintainCost=" + maintainCost
                    + ", extendWarrantyCost=" + extendWarrantyCost + ", accessoryCost=" + accessoryCost + ", voucherCost="
                    + voucherCost + ", walkingCarPrice=" + walkingCarPrice + ", volvoIntegral=" + volvoIntegral
                    + ", returnChangeCarPrice=" + returnChangeCarPrice + ", otherPrice=" + otherPrice + ", costTotal="
                    + costTotal + ", customerPay=" + customerPay + ", dealerUndertake=" + dealerUndertake
                    + ", volvoSupportGoodwillAmount=" + volvoSupportGoodwillAmount + ", remark=" + remark + ", applyFile="
                    + applyFile + ", costStatisticsFile=" + costStatisticsFile + ", costScreenshotFile="
                    + costScreenshotFile + ", ringCheckFile=" + ringCheckFile + ", troubleRepairRequisitionFile="
                    + troubleRepairRequisitionFile + ", workOrderFile=" + workOrderFile
                    + ", situationSettlementAgreementFile=" + situationSettlementAgreementFile
                    + ", supplementaryMaterialFile=" + supplementaryMaterialFile + ", managementReviewEmailVpFile="
                    + managementReviewEmailVpFile + ", managementReviewEmailCeoFile=" + managementReviewEmailCeoFile
                    + ", costUpdateFile=" + costUpdateFile + ", vcdcElseFile=" + vcdcElseFile + ", customerIdentification="
                    + customerIdentification + ", elseFile=" + elseFile + ", commitTime=" + commitTime + ", isValid="
                    + isValid + ", isDeleted=" + isDeleted + ", createdAt=" + createdAt + ", updatedAt=" + updatedAt + "}";
        }

        /**
         * 将DTO 转换为PO //对某个对象属性进行赋值
         * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
         *
         * @param poClass 需要转换的poClass
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        @Override
        public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
            return super.transDtoToPo(poClass);
        }

        /**
         * 将DTO 转换为PO
         * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
         *
         * @param po 需要转换的对象
         * <AUTHOR>
         * @since 2018/7/22 0022
         */
        public <T extends BasePO> void transDtoToPo(T po) {
            BeanMapperUtil.copyProperties(this, po, "id");
        }

    }



