package com.yonyou.dmscus.customer.entity.dto.accidentClues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 维修工具查询参数VO
 * <AUTHOR>
 */
@Data
@ApiModel("维修工具查询参数VO")
public class RepairToolQueryiParamsVo {

    private String rowKey;

    @ApiModelProperty(value="itemId ",name="itemId")
    private Integer itemId;

    @ApiModelProperty(value="工单号 ",name="roNo")
    private String roNo;

    @ApiModelProperty(value="估算单id ",name="estimateToolId")
    private String estimateToolId;

    @ApiModelProperty(value="工单工具ID",name="roToolId")
    private Integer roToolId;

    @ApiModelProperty(value="工具代码",name="toolCode")
    private String toolCode;

    @ApiModelProperty(value="工具名称",name="toolName")
    private String toolName;

    @ApiModelProperty(value="需求数量",name="needCount")
    private Integer needCount;

    @ApiModelProperty(name = "工时代码",value = "labourCode")
    private String labourCode;

    @ApiModelProperty(value="缺件标识",name="inspectFee")
    private Integer inspectFee;

    @ApiModelProperty(name = "关联交修项目",value = "jobNo")
    private Long jobNo;

    @ApiModelProperty(name = "工时名称",value = "labourName")
    private String labourName;

    @ApiModelProperty(name = "预约单号",value = "bookingOrderNo")
    private String bookingOrderNo;

    @ApiModelProperty(name = "仓库代码",value = "storageCode")
    private String storageCode;

    @ApiModelProperty(name = "库位代码",value = "storagePositionCode")
    private String storagePositionCode;

    @ApiModelProperty(name = "工具状态",value = "toolLendStatus")
    private String toolLendStatus;

    @ApiModelProperty(name = "工具代码",value = "toolNo")
    private String toolNo;

    @ApiModelProperty(name = "仓库数量",value = "stockQuantity")
    private String stockQuantity;

    @ApiModelProperty(name = "可用数量",value = "availableStock")
    private Double availableStock;

    @ApiModelProperty(value = "乐观锁",name = "recordVersion")
    private String recordVersion;



}
