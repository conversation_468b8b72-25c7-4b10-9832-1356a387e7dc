package com.yonyou.dmscus.customer.entity.dto.businessPlatform;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel(value = "OwnerVehicleDTO 对象", description = "OwnerVehicleDTO")
public class OwnerVehicleDTO extends BaseDTO {


    /**
     * VIN\车牌号 在车辆中心。
     */
    @ApiModelProperty(value = "VIN")
    private String vin;
    @ApiModelProperty(value = "车牌号")
    private String plateNumber;

    /**
     * 车型名称 在主数据中心。
     */
    @ApiModelProperty(value = "车型名称")
    private String modelName;

    /**
     * 经销商名称在组织中心。
     */
    @ApiModelProperty(value = "经销商名称中文")
    private String companyNameCn;

    @ApiModelProperty(value = "经销商代码")
    private String companyCode;

    /**
     * 车主姓名、建档日期、手机 在客户中心。
     */
    @ApiModelProperty(value = "车主编号")
    private String pontentialCustomerNo;
    @ApiModelProperty(value = "车主姓名")
    private String name;
    @ApiModelProperty(value = "建档日期-开始")
    private String foundDateBegin;
    @ApiModelProperty(value = "建档日期-结束")
    private String foundDateEnd;
    @ApiModelProperty(value = "手机1")
    private String contactorMobile;

}
