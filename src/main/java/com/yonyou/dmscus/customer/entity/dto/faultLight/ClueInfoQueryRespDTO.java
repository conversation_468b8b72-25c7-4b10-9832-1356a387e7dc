package com.yonyou.dmscus.customer.entity.dto.faultLight;

import com.yonyou.dmscus.customer.utils.excel.ExcelExport;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ClueInfoQueryRespDTO implements Serializable {

    private static final long serialVersionUID = -7331317985220936812L;

    private Long id;

    @ExcelExport("Ref NO.")
    private Integer number;

    private Integer clueStatus;

    @ExcelExport("线索状态")
    private String clueStatusCn;

    private Integer followStatus;

    @ExcelExport("跟进状态")
    private String followStatusCn;

    @ExcelExport("VIN")
    private String vin;

    @ExcelExport("报警名称（EN）")
    private String warningEnName;

    private String waringCnDescription;

    @ExcelExport("报警名称（CH）")
    private String warningName;

    @ExcelExport("报警等级")
    private String faultGrade;

    @ExcelExport("报警时间")
    private Date alarmTime;

    @ExcelExport("报警城市")
    private String cityName;

    @ExcelExport("线索生成时间")
    private Date clueGenTime;

    @ExcelExport("联络时间")
    private Date contactTime;

    @ExcelExport("联络人")
    private String contactsName;

    @ExcelExport("联络响应时间（h）")
    private String contactResTime;

    @ExcelExport("联络响应是否超时")
    private String contactOvertime;

    @ExcelExport("联络结果")
    private String contactResult;

    @ExcelExport("下发DLR")
    private String dlr;

    @ExcelExport("是否为推荐DLR")
    private String isDlr;

    @ExcelExport("下发时间")
    private Date clueDisTime;

    @ExcelExport("大区")
    private String regionName;

    @ExcelExport("小区")
    private String cellName;

    @ExcelExport("集团")
    private String groupCompanyShortName;

    @ExcelExport("邀约时间")
    private Date inviteTime;

    @ExcelExport("邀约人")
    private String inviteName;

    @ExcelExport("邀约响应时间（h）")
    private String inviteResTime;

    @ExcelExport("邀约响应是否超时")
    private String inviteOvertime;

    @ExcelExport("邀约结果")
    private String inviteResult;

    @ExcelExport("预约进店时间")
    private Date forecastTime;

    @ExcelExport("进店时间")
    private Date intoTime;

    @ExcelExport("是否按时进店")
    private String intoOnTime;

    @ExcelExport("是否爽约未进店")
    private String noInto;

    @ExcelExport("是否自然进店")
    private String selfInto;

    @ExcelExport("关联工单号")
    private String roNo;

    @ExcelExport("工单开始时间")
    private Date roStartTime;

    @ExcelExport("工单结算时间")
    private Date roEndTime;

    @ExcelExport("维修完成时间")
    private Date repairComTime;

    @ExcelExport("线索关闭时间")
    private Date clueCloTime;

    @ExcelExport("线索完成时间")
    private Date clueComTime;

    @ExcelExport("工单类型")
    private String roType;

    @ExcelExport("工单金额")
    private String roAmount;

    @ExcelExport("缺件")
    private String missParts;

    @ExcelExport("不修")
    private String noRepair;

    @ExcelExport("离店亮灯")
    private String lightsUp;

    private Integer wheRes;

    @ExcelExport("是否有责")
    private String wheResCn;

    private String dealerCode;

    @ExcelExport("外呼备注")
    private String comments;

    @ExcelExport("判责备注")
    private String remarks;

    @ExcelExport("失败备注")
    private String failureReason;

    @ExcelExport("二次预约时间")
    private Date reInviteTime;

    @ExcelExport("故障信息来源")
    private String sourceType;

    @ExcelExport("是否展示DTC诊断信息")
    private String troubleCode;

    @ExcelExport("故障信息")
    private String faultCategory;

    /**
     * 胡仓线索ID
     */
    private Long sourceClueId;
}
