package com.yonyou.dmscus.customer.entity.dto.businessPlatform;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import java.io.Serializable;

/**
 * <p>
 * 经销商信息（中台）
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */

public class UserInfoDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 用户id
     */
    private Long id;

    /**
     * 账号
     */
    private String account;


    /**
     *用户名
     */
    private String username;

    /**
     * 证件类型
     */
    private Integer idcardType;

    /**
     * 证件号
     */
    private String idcardNumber;

    /**
     * 用户角色
     */
    private String  roleCode;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 邮箱
     */
    private String  email;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getIdcardType() {
        return idcardType;
    }

    public void setIdcardType(Integer idcardType) {
        this.idcardType = idcardType;
    }

    public String getIdcardNumber() {
        return idcardNumber;
    }

    public void setIdcardNumber(String idcardNumber) {
        this.idcardNumber = idcardNumber;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
