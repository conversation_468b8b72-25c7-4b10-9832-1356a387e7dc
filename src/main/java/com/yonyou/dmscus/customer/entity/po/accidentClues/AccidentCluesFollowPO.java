package com.yonyou.dmscus.customer.entity.po.accidentClues;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 事故线索跟进
 */
@TableName("tt_accident_clues_follow")
@Data
public class AccidentCluesFollowPO extends BasePO<AccidentCluesFollowPO> {

  @TableField("app_id")
  private String appId;

  @TableField("owner_code")
  private String ownerCode;

  @TableField("owner_par_code")
  private String ownerParCode;

  @TableField("org_id")
  private Integer orgId;

  /**
   * 主键ID
   */
  @TableId("follow_id")
  private Integer followId;

  /**
   * 经销商代码
   */
  @TableField("dealer_code")
  private String dealerCode;

  /**
   * tt_accident_clues
   */
  @TableField("ac_id")
  private Integer acId;

  /**
   * 是否本店承保
   */
  @TableField("is_insured")
  private Integer isInsured;

  /**
   * 单方/双方事故
   */
  @TableField("double_accident")
  private Integer doubleAccident;

  /**
   * 事故责任划分
   */
  @TableField("duty_division")
  private Integer dutyDivision;

  /**
   *  是否现场报案
   */
  @TableField("is_report")
  private Integer isReport;

  /**
   * 是否拖车服务
   */
  @TableField("is_trailer")
  private Integer isTrailer;

  /**
   * 进厂工单号
   */
  @TableField("ro_no")
  private String roNo;

  /**
   * 跟进方式
   */
  @TableField("follow_type")
  private Integer followType;

  /**
   * 跟进内容/客户反馈
   */
  @TableField("follow_text")
  private String followText;

  /**
   * 跟进状态
   */
  @TableField("follow_status")
  private Integer followStatus;

  /**
   * 跟进失败原因
   */
  @TableField("follow_fail_why")
  private Integer followFailWhy;

  /**
   * 下次跟进日期
   */
  @TableField("next_follow_date")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date nextFollowDate;

  /**
   * 是否预约进店
   */
  @TableField("is_appointment")
  private Integer isAppointment;

  /**
   * 客户预约进店
   */
  @TableField("customer_appointment")
  private Integer customerAppointment;

  /**
   * 客户预约进店时间
   */
  @TableField("appointment_into_date")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
  private Date appointmentIntoDate;

  //数据来源
  @TableField("data_sources")
  private Integer dataSources;

  //是否删除，1：删除，0：未删除"
  @TableField("is_deleted")
  private Integer isDeleted;

  //是否有效"
  @TableField("is_valid")
  private Integer isValid;

  //创建时间"
  @TableField("created_at")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date createdAt;

  //创建人"
  @TableField("created_by")
  private String createdBy;

  //更新时间"
  @TableField("updated_at")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date updatedAt;

  //更新人
  @TableField("updated_by")
  private String updatedBy;

  //版本号（乐观锁）
  @TableField("record_version")
  private Integer recordVersion;

  /**
   * 工单状态
   */
  @TableField(exist = false)
  private Integer roStatus;

  /**
   * 跟进人员name
   */
  @TableField("follow_people_name")
  private String followPeopleName;

  /**
   * 进厂时间
   */
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  private Date intoDealerDate;

  /**
   * 进厂经销商
   */
  @TableField("into_dealer_code")
  private String intoDealerCode;

  @TableField("follow_people")
  private Integer followPeople;





  @Override
  public String toString() {
    return ToStringBuilder.reflectionToString(this);
  }

}
