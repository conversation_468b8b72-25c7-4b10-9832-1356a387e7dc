package com.yonyou.dmscus.customer.entity.po.complaint.sale;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

    import com.yonyou.dmscloud.framework.base.po.BasePO;

    
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;

import java.util.Date;

/**
 * <p>
 * 客户测试表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
@TableName("tt_sale_complaint_evidence")
public class SaleComplaintEvidencePO extends BasePO<SaleComplaintEvidencePO> {

    private static final long serialVersionUID=1L;
    
    /**
     * 系统ID
     */
    @TableField("app_id")
        private String appId;
    
    /**
     * 所有者代码 
     */
    @TableField("owner_code")
        private String ownerCode;
    
    /**
     * 所有者的父组织代码 
     */
    @TableField("owner_par_code")
        private String ownerParCode;
    
    /**
     * 组织ID
     */
    @TableField("org_id")
        private Integer orgId;
        
    /**
     * 主键ID
     */
            @TableId(value = "id", type = IdType.AUTO)
                        private Long id;
    
    /**
     * 投诉信息表主键ID
     */
    @TableField("complaint_info_id")
        private Long complaintInfoId;
    
    /**
     * 跟进对象 区域经理、经销商、CCM、客服中心
     */
    @TableField("object")
        private String object;
    
    /**
     * 工单号
     */
    @TableField("ro_no")
        private String roNo;
    
    /**
     * 数据来源 
     */
    @TableField("data_sources")
        private Integer dataSources;
    
    /**
     * 是否删除
     */
    @TableField("is_deleted")
        private Boolean isDeleted;
    
    /**
     * 是否有效 
     */
    @TableField("is_valid")
        private Integer isValid;
    
    /**
     * 创建时间
     */
    @TableField("created_at")
        private Date createdAt;
    
    /**
     * 更新时间
     */
    @TableField("updated_at")
        private Date updatedAt;
    
    /**
     * 操作人
     */
    @TableField("operator")
        private String operator;

    public SaleComplaintEvidencePO(){
        super();
    }

                    
    public String getAppId(){
        return appId;
    }

        public void setAppId(String appId) {
            this.appId = appId;
            }
                    
    public String getOwnerCode(){
        return ownerCode;
    }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
            }
                    
    public String getOwnerParCode(){
        return ownerParCode;
    }

        public void setOwnerParCode(String ownerParCode) {
            this.ownerParCode = ownerParCode;
            }
                    
    public Integer getOrgId(){
        return orgId;
    }

        public void setOrgId(Integer orgId) {
            this.orgId = orgId;
            }
                    
    public Long getId(){
        return id;
    }

        public void setId(Long id) {
            this.id = id;
            }
                    
    public Long getComplaintInfoId(){
        return complaintInfoId;
    }

        public void setComplaintInfoId(Long complaintInfoId) {
            this.complaintInfoId = complaintInfoId;
            }
                    
    public String getObject(){
        return object;
    }

        public void setObject(String object) {
            this.object = object;
            }
                    
    public String getRoNo(){
        return roNo;
    }

        public void setRoNo(String roNo) {
            this.roNo = roNo;
            }
                    
    public Integer getDataSources(){
        return dataSources;
    }

        public void setDataSources(Integer dataSources) {
            this.dataSources = dataSources;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }

        public void setIsDeleted(Boolean isDeleted) {
            this.isDeleted = isDeleted;
            }
                    
    public Integer getIsValid(){
        return isValid;
    }

        public void setIsValid(Integer isValid) {
            this.isValid = isValid;
            }
                    
    public Date getCreatedAt(){
        return createdAt;
    }

        public void setCreatedAt(Date createdAt) {
            this.createdAt = createdAt;
            }
                    
    public Date getUpdatedAt(){
        return updatedAt;
    }

        public void setUpdatedAt(Date updatedAt) {
            this.updatedAt = updatedAt;
            }
                    
    public String getOperator(){
        return operator;
    }

        public void setOperator(String operator) {
            this.operator = operator;
            }
    
    @Override
    protected Serializable pkVal(){
            return this.id;
        }

@Override
    public String toString(){
        return"SaleComplaintEvidencePO{" +
                            "appId=" + appId +
                                    ", ownerCode=" + ownerCode +
                                    ", ownerParCode=" + ownerParCode +
                                    ", orgId=" + orgId +
                                    ", id=" + id +
                                    ", complaintInfoId=" + complaintInfoId +
                                    ", object=" + object +
                                    ", roNo=" + roNo +
                                    ", dataSources=" + dataSources +
                                    ", isDeleted=" + isDeleted +
                                    ", isValid=" + isValid +
                                    ", createdAt=" + createdAt +
                                    ", updatedAt=" + updatedAt +
                                    ", operator=" + operator +
                    "}";
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"id");
    }

}
