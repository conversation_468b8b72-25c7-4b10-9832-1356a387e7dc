package com.yonyou.dmscus.customer.entity.po.complaint.sale;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

    import com.yonyou.dmscloud.framework.base.po.BasePO;
    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.extension.activerecord.Model;
    import com.baomidou.mybatisplus.annotation.Version;
    import com.baomidou.mybatisplus.annotation.TableId;
    import com.baomidou.mybatisplus.annotation.TableField;
    
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 销售客户投诉信息表-etl
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-22
 */
@TableName("tt_sale_complaint_info_etl")
public class SaleComplaintInfoEtlPO extends BasePO<SaleComplaintInfoEtlPO> {

    private static final long serialVersionUID=1L;
        
    /**
     * 主键ID
     */
            @TableId(value = "id", type = IdType.AUTO)
                        private Long id;
    
    /**
     * 投诉日期
     */
    @TableField("call_time")
        private String callTime;
    
    /**
     * 投诉ID（投诉编号）可 用于和呼叫中心对接
     */
    @TableField("complaint_id")
        private String complaintId;
    
    /**
     * 工单性质
     */
    @TableField("work_order_nature")
        private String workOrderNature;
    
    /**
     * 投诉单类别一级层
     */
    @TableField("category1")
        private String category1;
    
    /**
     * 投诉单类别二级层
     */
    @TableField("category2")
        private String category2;
    
    /**
     * 投诉单类别三级层
     */
    @TableField("category3")
        private String category3;
    
    /**
     * 购买经销商
     */
    @TableField("buy_dealer_code")
        private String buyDealerCode;
    
    /**
     * 购买经销商
     */
    @TableField("buy_dealer_name")
        private String buyDealerName;
    
    /**
     * 处理经销商代码
     */
    @TableField("dealer_code")
        private String dealerCode;
    
    /**
     * 处理经销商
     */
    @TableField("dealer_name")
        private String dealerName;
    
    /**
     * 客户类型
     */
    @TableField("customer_type")
        private String customerType;
    
    /**
     * 来电客户姓名/投诉人姓名
     */
    @TableField("call_name")
        private String callName;
    
    /**
     * 来电电话/投诉人电话
     */
    @TableField("call_tel")
        private String callTel;
    
    /**
     * 回复联系人手机1
     */
    @TableField("reply_tel")
        private String replyTel;
    
    /**
     * 回复联系人手机2
     */
    @TableField("reply_tel2")
        private String replyTel2;
    
    /**
     * 车架号
     */
    @TableField("vin")
        private String vin;
    
    /**
     * 车主姓名
     */
    @TableField("name")
        private String name;
    
    /**
     * 车主手机
     */
    @TableField("phone")
        private String phone;
    
    /**
     * 车型
     */
    @TableField("model")
        private String model;
    
    /**
     * 车牌号
     */
    @TableField("license_plate_num")
        private String licensePlateNum;
    
    /**
     * 购车时间
     */
    @TableField("buy_time")
        private String buyTime;
    
    /**
     * 生命周期
     */
    @TableField("life_cycle")
        private String lifeCycle;
    
    /**
     * 里程
     */
    @TableField("mileage")
        private String mileage;
    
    /**
     * 问题描述
     */
    @TableField("problem")
        private String problem;
    
    /**
     * 工单状态
     */
    @TableField("work_order_status")
        private String workOrderStatus;
    
    /**
     * 结案状态（投诉单状态）
     */
    @TableField("close_case_status")
        private String closeCaseStatus;
    
    /**
     * 经销商首次回复时间
     */
    @TableField("dealer_fisrt_reply_time")
        private String dealerFisrtReplyTime;
    
    /**
     * 结案时间
     */
    @TableField("close_case_time")
        private String closeCaseTime;
    
    /**
     * 投诉解决时长
     */
    @TableField("solve_time")
        private String solveTime;
    
    /**
     * 审核人
     */
    @TableField("reviewer")
        private String reviewer;
    
    /**
     * 活动来源
     */
    @TableField("activity_source")
        private String activitySource;
    
    /**
     * 回访需求
     */
    @TableField("revisit_demand")
        private String revisitDemand;
    
    /**
     * 邮件状态
     */
    @TableField("email_status")
        private String emailStatus;
    
    /**
     * 经销商是否在48H内推出邮件
     */
    @TableField("is_48H_send_email")
        private String is48hSendEmail;
    
    /**
     * 投诉类型
     */
    @TableField("type")
        private String type;
    
    /**
     * 再次投诉时间
     */
    @TableField("again_call_time")
        private String againCallTime;
    
    /**
     * 短信
     */
    @TableField("SMS")
        private String sms;
    
    /**
     * 有效投诉
     */
    @TableField("effective_complaint")
        private String effectiveComplaint;
    
    /**
     * 投诉来源
     */
    @TableField("source")
        private String source;
    
    /**
     * 未结案原因分类
     */
    @TableField("no_close_reasons_classification")
        private String noCloseReasonsClassification;
    
    /**
     * 最后跟进时间
     */
    @TableField("last_follow_time")
        private String lastFollowTime;
    
    /**
     * 最后投诉时间
     */
    @TableField("last_complaint_time")
        private String lastComplaintTime;
    
    /**
     * 总投诉次数
     */
    @TableField("complaint_number")
        private String complaintNumber;
    
    /**
     * 责任判定
     */
    @TableField("responsibility_determine")
        private String responsibilityDetermine;
    
    /**
     * 投诉主题
     */
    @TableField("subject")
        private String subject;
    
    /**
     * 分类1
     */
    @TableField("classification1")
        private String classification1;
    
    /**
     * 分类2
     */
    @TableField("classification2")
        private String classification2;
    
    /**
     * 分类3
     */
    @TableField("classification3")
        private String classification3;
    
    /**
     * 分类4
     */
    @TableField("classification4")
        private String classification4;
    
    /**
     * 分类5
     */
    @TableField("classification5")
        private String classification5;
    
    /**
     * 分类6
     */
    @TableField("classification6")
        private String classification6;
    
    /**
     * 信息来源
     */
    @TableField("information_source")
        private String informationSource;
    
    /**
     * 关键字
     */
    @TableField("keyword")
        private String keyword;
    
    /**
     * 咨询解决情况
     */
    @TableField("consultation_and_solution")
        private String consultationAndSolution;
    
    /**
     * 解决方法
     */
    @TableField("solution")
        private String solution;
    
    /**
     * FAQ支持
     */
    @TableField("is_FAQ")
        private String isFaq;
    
    /**
     * 情绪指数新建
     */
    @TableField("emotion_index_creation")
        private String emotionIndexCreation;
    
    /**
     * 情绪指数新建
     */
    @TableField("sentiment_index_current")
        private String sentimentIndexCurrent;
    
    /**
     * CCM系统协助处理
     */
    @TableField("ccm_deal")
        private String ccmDeal;
    
    /**
     * 首次回访需求时间
     */
    @TableField("first_revisit_time")
        private String firstRevisitTime;
    
    /**
     * 重启日期
     */
    @TableField("restart_time")
        private String restartTime;
    
    /**
     * 重启次数
     */
    @TableField("restart_number")
        private String restartNumber;
    
    /**
     * 第二次重启时间
     */
    @TableField("second_restart_time")
        private String secondRestartTime;
    
    /**
     * 第二次结案时间
     */
    @TableField("seconde_close_case_time")
        private String secondeCloseCaseTime;
    
    /**
     * 案件等级
     */
    @TableField("case_level")
        private String caseLevel;
    
    /**
     * 是否24H回复
     */
    @TableField("is_24H_reply")
        private String is24hReply;
    
    /**
     * 是否48H回访
     */
    @TableField("is_48H_revisit")
        private String is48hRevisit;
    
    /**
     * 是否5日结案
     */
    @TableField("is_5D_close_case")
        private String is5dCloseCase;
    
    /**
     * 区域反馈回访
     */
    @TableField("regional_feedback")
        private String regionalFeedback;
    
    /**
     * 区域反馈回访日
     */
    @TableField("regional_feedback_time")
        private String regionalFeedbackTime;
    
    /**
     * 不回访理由
     */
    @TableField("no_revisit_reason")
        private String noRevisitReason;
    
    /**
     * 未结案原因
     */
    @TableField("no_close_reasons")
        private String noCloseReasons;
    
    /**
     * 服务承诺
     */
    @TableField("service_commitment")
        private String serviceCommitment;
    
    /**
     * 首次回访时间
     */
    @TableField("first_return_time")
        private String firstReturnTime;

    /**
     * 开始投诉时间
     */
    private String startCallTime;
    /**
     * 结束投诉时间
     */
    private String endCallTime;

    public SaleComplaintInfoEtlPO(){
        super();
    }

                    
    public Long getId(){
        return id;
    }

        public void setId(Long id) {
            this.id = id;
            }
                    
    public String getCallTime(){
        return callTime;
    }

        public void setCallTime(String callTime) {
            this.callTime = callTime;
            }
                    
    public String getComplaintId(){
        return complaintId;
    }

        public void setComplaintId(String complaintId) {
            this.complaintId = complaintId;
            }
                    
    public String getWorkOrderNature(){
        return workOrderNature;
    }

        public void setWorkOrderNature(String workOrderNature) {
            this.workOrderNature = workOrderNature;
            }
                    
    public String getCategory1(){
        return category1;
    }

        public void setCategory1(String category1) {
            this.category1 = category1;
            }
                    
    public String getCategory2(){
        return category2;
    }

        public void setCategory2(String category2) {
            this.category2 = category2;
            }
                    
    public String getCategory3(){
        return category3;
    }

        public void setCategory3(String category3) {
            this.category3 = category3;
            }
                    
    public String getBuyDealerCode(){
        return buyDealerCode;
    }

        public void setBuyDealerCode(String buyDealerCode) {
            this.buyDealerCode = buyDealerCode;
            }
                    
    public String getBuyDealerName(){
        return buyDealerName;
    }

        public void setBuyDealerName(String buyDealerName) {
            this.buyDealerName = buyDealerName;
            }
                    
    public String getDealerCode(){
        return dealerCode;
    }

        public void setDealerCode(String dealerCode) {
            this.dealerCode = dealerCode;
            }
                    
    public String getDealerName(){
        return dealerName;
    }

        public void setDealerName(String dealerName) {
            this.dealerName = dealerName;
            }
                    
    public String getCustomerType(){
        return customerType;
    }

        public void setCustomerType(String customerType) {
            this.customerType = customerType;
            }
                    
    public String getCallName(){
        return callName;
    }

        public void setCallName(String callName) {
            this.callName = callName;
            }
                    
    public String getCallTel(){
        return callTel;
    }

        public void setCallTel(String callTel) {
            this.callTel = callTel;
            }
                    
    public String getReplyTel(){
        return replyTel;
    }

        public void setReplyTel(String replyTel) {
            this.replyTel = replyTel;
            }
                    
    public String getReplyTel2(){
        return replyTel2;
    }

        public void setReplyTel2(String replyTel2) {
            this.replyTel2 = replyTel2;
            }
                    
    public String getVin(){
        return vin;
    }

        public void setVin(String vin) {
            this.vin = vin;
            }
                    
    public String getName(){
        return name;
    }

        public void setName(String name) {
            this.name = name;
            }
                    
    public String getPhone(){
        return phone;
    }

        public void setPhone(String phone) {
            this.phone = phone;
            }
                    
    public String getModel(){
        return model;
    }

        public void setModel(String model) {
            this.model = model;
            }
                    
    public String getLicensePlateNum(){
        return licensePlateNum;
    }

        public void setLicensePlateNum(String licensePlateNum) {
            this.licensePlateNum = licensePlateNum;
            }
                    
    public String getBuyTime(){
        return buyTime;
    }

        public void setBuyTime(String buyTime) {
            this.buyTime = buyTime;
            }
                    
    public String getLifeCycle(){
        return lifeCycle;
    }

        public void setLifeCycle(String lifeCycle) {
            this.lifeCycle = lifeCycle;
            }
                    
    public String getMileage(){
        return mileage;
    }

        public void setMileage(String mileage) {
            this.mileage = mileage;
            }
                    
    public String getProblem(){
        return problem;
    }

        public void setProblem(String problem) {
            this.problem = problem;
            }
                    
    public String getWorkOrderStatus(){
        return workOrderStatus;
    }

        public void setWorkOrderStatus(String workOrderStatus) {
            this.workOrderStatus = workOrderStatus;
            }
                    
    public String getCloseCaseStatus(){
        return closeCaseStatus;
    }

        public void setCloseCaseStatus(String closeCaseStatus) {
            this.closeCaseStatus = closeCaseStatus;
            }
                    
    public String getDealerFisrtReplyTime(){
        return dealerFisrtReplyTime;
    }

        public void setDealerFisrtReplyTime(String dealerFisrtReplyTime) {
            this.dealerFisrtReplyTime = dealerFisrtReplyTime;
            }
                    
    public String getCloseCaseTime(){
        return closeCaseTime;
    }

        public void setCloseCaseTime(String closeCaseTime) {
            this.closeCaseTime = closeCaseTime;
            }
                    
    public String getSolveTime(){
        return solveTime;
    }

        public void setSolveTime(String solveTime) {
            this.solveTime = solveTime;
            }
                    
    public String getReviewer(){
        return reviewer;
    }

        public void setReviewer(String reviewer) {
            this.reviewer = reviewer;
            }
                    
    public String getActivitySource(){
        return activitySource;
    }

        public void setActivitySource(String activitySource) {
            this.activitySource = activitySource;
            }
                    
    public String getRevisitDemand(){
        return revisitDemand;
    }

        public void setRevisitDemand(String revisitDemand) {
            this.revisitDemand = revisitDemand;
            }
                    
    public String getEmailStatus(){
        return emailStatus;
    }

        public void setEmailStatus(String emailStatus) {
            this.emailStatus = emailStatus;
            }
                    
    public String getIs48hSendEmail(){
        return is48hSendEmail;
    }

        public void setIs48hSendEmail(String is48hSendEmail) {
            this.is48hSendEmail = is48hSendEmail;
            }
                    
    public String getType(){
        return type;
    }

        public void setType(String type) {
            this.type = type;
            }
                    
    public String getAgainCallTime(){
        return againCallTime;
    }

        public void setAgainCallTime(String againCallTime) {
            this.againCallTime = againCallTime;
            }
                    
    public String getSms(){
        return sms;
    }

        public void setSms(String sms) {
            this.sms = sms;
            }
                    
    public String getEffectiveComplaint(){
        return effectiveComplaint;
    }

        public void setEffectiveComplaint(String effectiveComplaint) {
            this.effectiveComplaint = effectiveComplaint;
            }
                    
    public String getSource(){
        return source;
    }

        public void setSource(String source) {
            this.source = source;
            }
                    
    public String getNoCloseReasonsClassification(){
        return noCloseReasonsClassification;
    }

        public void setNoCloseReasonsClassification(String noCloseReasonsClassification) {
            this.noCloseReasonsClassification = noCloseReasonsClassification;
            }
                    
    public String getLastFollowTime(){
        return lastFollowTime;
    }

        public void setLastFollowTime(String lastFollowTime) {
            this.lastFollowTime = lastFollowTime;
            }
                    
    public String getLastComplaintTime(){
        return lastComplaintTime;
    }

        public void setLastComplaintTime(String lastComplaintTime) {
            this.lastComplaintTime = lastComplaintTime;
            }
                    
    public String getComplaintNumber(){
        return complaintNumber;
    }

        public void setComplaintNumber(String complaintNumber) {
            this.complaintNumber = complaintNumber;
            }
                    
    public String getResponsibilityDetermine(){
        return responsibilityDetermine;
    }

        public void setResponsibilityDetermine(String responsibilityDetermine) {
            this.responsibilityDetermine = responsibilityDetermine;
            }
                    
    public String getSubject(){
        return subject;
    }

        public void setSubject(String subject) {
            this.subject = subject;
            }
                    
    public String getClassification1(){
        return classification1;
    }

        public void setClassification1(String classification1) {
            this.classification1 = classification1;
            }
                    
    public String getClassification2(){
        return classification2;
    }

        public void setClassification2(String classification2) {
            this.classification2 = classification2;
            }
                    
    public String getClassification3(){
        return classification3;
    }

        public void setClassification3(String classification3) {
            this.classification3 = classification3;
            }
                    
    public String getClassification4(){
        return classification4;
    }

        public void setClassification4(String classification4) {
            this.classification4 = classification4;
            }
                    
    public String getClassification5(){
        return classification5;
    }

        public void setClassification5(String classification5) {
            this.classification5 = classification5;
            }
                    
    public String getClassification6(){
        return classification6;
    }

        public void setClassification6(String classification6) {
            this.classification6 = classification6;
            }
                    
    public String getInformationSource(){
        return informationSource;
    }

        public void setInformationSource(String informationSource) {
            this.informationSource = informationSource;
            }
                    
    public String getKeyword(){
        return keyword;
    }

        public void setKeyword(String keyword) {
            this.keyword = keyword;
            }
                    
    public String getConsultationAndSolution(){
        return consultationAndSolution;
    }

        public void setConsultationAndSolution(String consultationAndSolution) {
            this.consultationAndSolution = consultationAndSolution;
            }
                    
    public String getSolution(){
        return solution;
    }

        public void setSolution(String solution) {
            this.solution = solution;
            }
                    
    public String getIsFaq(){
        return isFaq;
    }

        public void setIsFaq(String isFaq) {
            this.isFaq = isFaq;
            }
                    
    public String getEmotionIndexCreation(){
        return emotionIndexCreation;
    }

        public void setEmotionIndexCreation(String emotionIndexCreation) {
            this.emotionIndexCreation = emotionIndexCreation;
            }
                    
    public String getSentimentIndexCurrent(){
        return sentimentIndexCurrent;
    }

        public void setSentimentIndexCurrent(String sentimentIndexCurrent) {
            this.sentimentIndexCurrent = sentimentIndexCurrent;
            }
                    
    public String getCcmDeal(){
        return ccmDeal;
    }

        public void setCcmDeal(String ccmDeal) {
            this.ccmDeal = ccmDeal;
            }
                    
    public String getFirstRevisitTime(){
        return firstRevisitTime;
    }

        public void setFirstRevisitTime(String firstRevisitTime) {
            this.firstRevisitTime = firstRevisitTime;
            }
                    
    public String getRestartTime(){
        return restartTime;
    }

        public void setRestartTime(String restartTime) {
            this.restartTime = restartTime;
            }
                    
    public String getRestartNumber(){
        return restartNumber;
    }

        public void setRestartNumber(String restartNumber) {
            this.restartNumber = restartNumber;
            }
                    
    public String getSecondRestartTime(){
        return secondRestartTime;
    }

        public void setSecondRestartTime(String secondRestartTime) {
            this.secondRestartTime = secondRestartTime;
            }
                    
    public String getSecondeCloseCaseTime(){
        return secondeCloseCaseTime;
    }

        public void setSecondeCloseCaseTime(String secondeCloseCaseTime) {
            this.secondeCloseCaseTime = secondeCloseCaseTime;
            }
                    
    public String getCaseLevel(){
        return caseLevel;
    }

        public void setCaseLevel(String caseLevel) {
            this.caseLevel = caseLevel;
            }
                    
    public String getIs24hReply(){
        return is24hReply;
    }

        public void setIs24hReply(String is24hReply) {
            this.is24hReply = is24hReply;
            }
                    
    public String getIs48hRevisit(){
        return is48hRevisit;
    }

        public void setIs48hRevisit(String is48hRevisit) {
            this.is48hRevisit = is48hRevisit;
            }
                    
    public String getIs5dCloseCase(){
        return is5dCloseCase;
    }

        public void setIs5dCloseCase(String is5dCloseCase) {
            this.is5dCloseCase = is5dCloseCase;
            }
                    
    public String getRegionalFeedback(){
        return regionalFeedback;
    }

        public void setRegionalFeedback(String regionalFeedback) {
            this.regionalFeedback = regionalFeedback;
            }
                    
    public String getRegionalFeedbackTime(){
        return regionalFeedbackTime;
    }

        public void setRegionalFeedbackTime(String regionalFeedbackTime) {
            this.regionalFeedbackTime = regionalFeedbackTime;
            }
                    
    public String getNoRevisitReason(){
        return noRevisitReason;
    }

        public void setNoRevisitReason(String noRevisitReason) {
            this.noRevisitReason = noRevisitReason;
            }
                    
    public String getNoCloseReasons(){
        return noCloseReasons;
    }

        public void setNoCloseReasons(String noCloseReasons) {
            this.noCloseReasons = noCloseReasons;
            }
                    
    public String getServiceCommitment(){
        return serviceCommitment;
    }

        public void setServiceCommitment(String serviceCommitment) {
            this.serviceCommitment = serviceCommitment;
            }
                    
    public String getFirstReturnTime(){
        return firstReturnTime;
    }

        public void setFirstReturnTime(String firstReturnTime) {
            this.firstReturnTime = firstReturnTime;
            }

    public String getStartCallTime() {
        return startCallTime;
    }

    public void setStartCallTime(String startCallTime) {
        this.startCallTime = startCallTime;
    }

    public String getEndCallTime() {
        return endCallTime;
    }

    public void setEndCallTime(String endCallTime) {
        this.endCallTime = endCallTime;
    }

    @Override
    protected Serializable pkVal(){
            return this.id;
        }

@Override
    public String toString(){
        return"SaleComplaintInfoEtlPO{" +
                            "id=" + id +
                                    ", callTime=" + callTime +
                                    ", complaintId=" + complaintId +
                                    ", workOrderNature=" + workOrderNature +
                                    ", category1=" + category1 +
                                    ", category2=" + category2 +
                                    ", category3=" + category3 +
                                    ", buyDealerCode=" + buyDealerCode +
                                    ", buyDealerName=" + buyDealerName +
                                    ", dealerCode=" + dealerCode +
                                    ", dealerName=" + dealerName +
                                    ", customerType=" + customerType +
                                    ", callName=" + callName +
                                    ", callTel=" + callTel +
                                    ", replyTel=" + replyTel +
                                    ", replyTel2=" + replyTel2 +
                                    ", vin=" + vin +
                                    ", name=" + name +
                                    ", phone=" + phone +
                                    ", model=" + model +
                                    ", licensePlateNum=" + licensePlateNum +
                                    ", buyTime=" + buyTime +
                                    ", lifeCycle=" + lifeCycle +
                                    ", mileage=" + mileage +
                                    ", problem=" + problem +
                                    ", workOrderStatus=" + workOrderStatus +
                                    ", closeCaseStatus=" + closeCaseStatus +
                                    ", dealerFisrtReplyTime=" + dealerFisrtReplyTime +
                                    ", closeCaseTime=" + closeCaseTime +
                                    ", solveTime=" + solveTime +
                                    ", reviewer=" + reviewer +
                                    ", activitySource=" + activitySource +
                                    ", revisitDemand=" + revisitDemand +
                                    ", emailStatus=" + emailStatus +
                                    ", is48hSendEmail=" + is48hSendEmail +
                                    ", type=" + type +
                                    ", againCallTime=" + againCallTime +
                                    ", sms=" + sms +
                                    ", effectiveComplaint=" + effectiveComplaint +
                                    ", source=" + source +
                                    ", noCloseReasonsClassification=" + noCloseReasonsClassification +
                                    ", lastFollowTime=" + lastFollowTime +
                                    ", lastComplaintTime=" + lastComplaintTime +
                                    ", complaintNumber=" + complaintNumber +
                                    ", responsibilityDetermine=" + responsibilityDetermine +
                                    ", subject=" + subject +
                                    ", classification1=" + classification1 +
                                    ", classification2=" + classification2 +
                                    ", classification3=" + classification3 +
                                    ", classification4=" + classification4 +
                                    ", classification5=" + classification5 +
                                    ", classification6=" + classification6 +
                                    ", informationSource=" + informationSource +
                                    ", keyword=" + keyword +
                                    ", consultationAndSolution=" + consultationAndSolution +
                                    ", solution=" + solution +
                                    ", isFaq=" + isFaq +
                                    ", emotionIndexCreation=" + emotionIndexCreation +
                                    ", sentimentIndexCurrent=" + sentimentIndexCurrent +
                                    ", ccmDeal=" + ccmDeal +
                                    ", firstRevisitTime=" + firstRevisitTime +
                                    ", restartTime=" + restartTime +
                                    ", restartNumber=" + restartNumber +
                                    ", secondRestartTime=" + secondRestartTime +
                                    ", secondeCloseCaseTime=" + secondeCloseCaseTime +
                                    ", caseLevel=" + caseLevel +
                                    ", is24hReply=" + is24hReply +
                                    ", is48hRevisit=" + is48hRevisit +
                                    ", is5dCloseCase=" + is5dCloseCase +
                                    ", regionalFeedback=" + regionalFeedback +
                                    ", regionalFeedbackTime=" + regionalFeedbackTime +
                                    ", noRevisitReason=" + noRevisitReason +
                                    ", noCloseReasons=" + noCloseReasons +
                                    ", serviceCommitment=" + serviceCommitment +
                                    ", firstReturnTime=" + firstReturnTime +
                    "}";
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"id");
    }

}
