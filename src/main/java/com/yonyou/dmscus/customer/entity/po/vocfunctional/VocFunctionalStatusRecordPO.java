package com.yonyou.dmscus.customer.entity.po.vocfunctional;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Objects;


/**
 * bi推送数据记录表
 */

@Data
@ApiModel(value = "VocFunctionalStatusRecordPO", description = "BI推送车辆VOC信息表")
@TableName("tt_voc_functional_status_record")
public class VocFunctionalStatusRecordPO  extends  BasePo{
  //通知时间
  @ApiModelProperty(value = "更新时间",notes = "更新时间",required = true,name = "update_time",example = "20221011",dataType = "String")
  @TableField("update_time")
  private String updateTime;
  //需要修改的时间
  @ApiModelProperty(value = "更新时间",notes = "更新时间",required = true,name = "modify_time",example = "20221011",dataType = "String")
  @TableField("modify_time")
  private String modifyTime;
  @ApiModelProperty(value = "车架号",notes = "车架号",required = true,name = "dt",example = "YV1LFA2D2H1163606",dataType = "String")
  @TableField("vin")
  private String vin;

  @ApiModelProperty(value = "是否执行",notes = "是否执行 1,已执行; 0,未执行",required = true,name = "is_execute",example = "0",dataType = "Integer")
  @TableField("is_execute")
  private Integer isExecute;

  @TableField("activated_state")
  @ApiModelProperty(value = "是否激活",notes = "是否激活:1,激活；0,未激活",required = true,name = "activated_state",example = "0",dataType = "Integer")
  private Integer activatedState;

  @TableField("subscription_startdate")
  @ApiModelProperty(value = "开始时间",notes = "开始时间",required = true,name = "subscription_startdate",example = "0",dataType = "String")
  private Date subscriptionStartdate;

  @TableField("subscription_enddate")
  @ApiModelProperty(value = "结束时间",notes = "结束时间",required = true,name = "subscription_enddate",example = "0",dataType = "String")
  private Date subscriptionEnddate;

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    if (!super.equals(o)) return false;
    VocFunctionalStatusRecordPO that = (VocFunctionalStatusRecordPO) o;
    return Objects.equals(updateTime, that.updateTime) &&
            Objects.equals(modifyTime, that.modifyTime) &&
            Objects.equals(vin, that.vin) &&
            Objects.equals(activatedState, that.activatedState) &&
            Objects.equals(subscriptionStartdate, that.subscriptionStartdate) &&
            Objects.equals(subscriptionEnddate, that.subscriptionEnddate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), updateTime, modifyTime, vin, activatedState, subscriptionStartdate, subscriptionEnddate);
  }
}
