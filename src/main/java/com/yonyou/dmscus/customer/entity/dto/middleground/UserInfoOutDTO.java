package com.yonyou.dmscus.customer.entity.dto.middleground;

import com.yonyou.dmscus.customer.middleInterface.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "输出用户信息", description = "用户具体信息")
public class UserInfoOutDTO {

    private static final long serialVersionUID = Constant.SERIAL_VERSION_UID;

    @ApiModelProperty(value = "用户id", position = 1)
    private Long userId;

    @ApiModelProperty(value = "账号", position = 2)
    private String userCode;

    @ApiModelProperty(value = "用户名", position = 3)
    private String employeeName;

    @ApiModelProperty(value = "证件号", position = 5)
    private String idcardNumber;

    @ApiModelProperty(value = "电话号码", position = 6)
    private String phone;

    @ApiModelProperty(value = "邮箱", position = 7)
    private String email;

    @ApiModelProperty(value = "用戶创建人", position = 8)
    private String createBy;

    @ApiModelProperty(value = "用戶修改人", position = 10)
    private String updateBy;

    @ApiModelProperty(value = "用户账号状态", position = 14)
    private Integer accountStatus;

    @ApiModelProperty(value = "省级")
    private String province;

    @ApiModelProperty(value = "性别（********男 ********女）")
    private Integer gender;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "评分")
    private Integer score;

    @ApiModelProperty(value = "头像图片")
    private String heardImgUrl;

    @ApiModelProperty(value = "工作年限")
    private Integer workYears;

    @ApiModelProperty(value = "unionId")
    private String unionId;

    @ApiModelProperty(value = "本身所在company_id")
    private Long companyId;

    @ApiModelProperty(value = "地址")
    private String adderss;

    @ApiModelProperty(value = "微信号")
    private String wechat;

    @ApiModelProperty(value = "在职状态 ********:在职  , ********:离职")
    private Integer isOnjob;

    @ApiModelProperty(value = "职位（待需求明确）")
    private String position;

    @ApiModelProperty(value = "售后大区名称")
    private String afterBigAreaName;

    @ApiModelProperty(value = "售后小区名称")
    private String afterSmallAreaName;

    @ApiModelProperty(value = "销售大区名称")
    private String saleBigAreaName;

    @ApiModelProperty(value = "销售小区名称")
    private String saleSmallAreaName;

    @ApiModelProperty(value = "经销商代码")
    private String companyCode;

    @ApiModelProperty(value = "经销商名称中文")
    private String companyNameCn;

    @ApiModelProperty(value = "经销商简称中文")
    private String companyShortNameCn;

    @ApiModelProperty(value = "VCPA")
    private String vcpa;

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getVcpa() {
        return vcpa;
    }

    public void setVcpa(String vcpa) {
        this.vcpa = vcpa;
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public String getAdderss() {
        return adderss;
    }

    public void setAdderss(String adderss) {
        this.adderss = adderss;
    }

    public Integer getIsOnjob() {
        return isOnjob;
    }

    public void setIsOnjob(Integer isOnjob) {
        this.isOnjob = isOnjob;
    }

    public String getAfterBigAreaName() {
        return afterBigAreaName;
    }

    public void setAfterBigAreaName(String afterBigAreaName) {
        this.afterBigAreaName = afterBigAreaName;
    }

    public String getAfterSmallAreaName() {
        return afterSmallAreaName;
    }

    public void setAfterSmallAreaName(String afterSmallAreaName) {
        this.afterSmallAreaName = afterSmallAreaName;
    }

    public String getSaleBigAreaName() {
        return saleBigAreaName;
    }

    public void setSaleBigAreaName(String saleBigAreaName) {
        this.saleBigAreaName = saleBigAreaName;
    }

    public String getSaleSmallAreaName() {
        return saleSmallAreaName;
    }

    public void setSaleSmallAreaName(String saleSmallAreaName) {
        this.saleSmallAreaName = saleSmallAreaName;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyNameCn() {
        return companyNameCn;
    }

    public void setCompanyNameCn(String companyNameCn) {
        this.companyNameCn = companyNameCn;
    }

    public String getCompanyShortNameCn() {
        return companyShortNameCn;
    }

    public void setCompanyShortNameCn(String companyShortNameCn) {
        this.companyShortNameCn = companyShortNameCn;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getIdcardNumber() {
        return idcardNumber;
    }

    public void setIdcardNumber(String idcardNumber) {
        this.idcardNumber = idcardNumber;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(Integer accountStatus) {
        this.accountStatus = accountStatus;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public String getHeardImgUrl() {
        return heardImgUrl;
    }

    public void setHeardImgUrl(String heardImgUrl) {
        this.heardImgUrl = heardImgUrl;
    }

    public Integer getWorkYears() {
        return workYears;
    }

    public void setWorkYears(Integer workYears) {
        this.workYears = workYears;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    @Override
    public String toString() {
        return "UserInfoOutDTO [userId=" + userId + ", userCode=" + userCode + ", employeeName=" + employeeName
                + ", idcardNumber=" + idcardNumber + ", phone=" + phone + ", email=" + email + ", createBy=" + createBy
                + ", updateBy=" + updateBy
                + ", accountStatus=" + accountStatus + ", province=" + province + ", gender=" + gender + ", country="
                + country + ", score=" + score + ", heardImgUrl=" + heardImgUrl + ", workYears=" + workYears
                + ", unionId=" + unionId + ", companyId=" + companyId + ", adderss="
                + adderss + ", wechat=" + wechat + ", isOnjob=" + isOnjob + ", position=" + position
                + ", afterBigAreaName=" + afterBigAreaName + ", afterSmallAreaName=" + afterSmallAreaName
                + ", saleBigAreaName=" + saleBigAreaName + ", saleSmallAreaName=" + saleSmallAreaName + ", companyCode="
                + companyCode + ", companyNameCn=" + companyNameCn + ", companyShortNameCn=" + companyShortNameCn + "]";
    }


}

