package com.yonyou.dmscus.customer.entity.po.complaint;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

    import com.yonyou.dmscloud.framework.base.po.BasePO;

    
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;

import java.util.Date;

/**
 * <p>
 * 客户投诉进销商和CCM复杂人对应关系导入表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@TableName("te_complaint_dealer_ccm_ref_import")
public class TeComplaintDealerCcmRefImportPO extends BasePO<TeComplaintDealerCcmRefImportPO> {

    private static final long serialVersionUID=1L;
    
    /**
     * 系统ID
     */
    @TableField("app_id")
        private String appId;
    
    /**
     * 所有者代码 
     */
    @TableField("owner_code")
        private String ownerCode;
    
    /**
     * 所有者的父组织代码 
     */
    @TableField("owner_par_code")
        private String ownerParCode;
    
    /**
     * 组织ID
     */
    @TableField("org_id")
        private Integer orgId;
        
    /**
     * 主键ID
     */
            @TableId(value = "id", type = IdType.AUTO)
                        private Long id;
    
    /**
     * 进销商代码
     */
    @TableField("dealer_code")
        private String dealerCode;
    
    /**
     * 进销商名称
     */
    @TableField("dealer_name")
        private String dealerName;
    
    /**
     * CCM负责人
     */
    @TableField("ccm_man")
        private String ccmMan;
    
    /**
     * 导入是否错误
     */
    @TableField("is_error")
        private Integer isError;
    
    /**
     * 错误信息
     */
    @TableField("error_msg")
        private String errorMsg;
    
    /**
     * 行数
     */
    @TableField("line_number")
        private Integer lineNumber;
    
    /**
     * 数据来源 
     */
    @TableField("data_sources")
        private Integer dataSources;
    
    /**
     * 是否删除
     */
    @TableField("is_deleted")
        private Boolean isDeleted;
    
    /**
     * 是否有效 
     */
    @TableField("is_valid")
        private Integer isValid;
    
    /**
     * 创建时间
     */
    @TableField("created_at")
        private Date createdAt;
    
    /**
     * 更新时间
     */
    @TableField("updated_at")
        private Date updatedAt;

    public TeComplaintDealerCcmRefImportPO(){
        super();
    }

                    
    @Override
    public String getAppId(){
        return appId;
    }

        @Override
        public void setAppId(String appId) {
            this.appId = appId;
            }
                    
    public String getOwnerCode(){
        return ownerCode;
    }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
            }
                    
    @Override
    public String getOwnerParCode(){
        return ownerParCode;
    }

        @Override
        public void setOwnerParCode(String ownerParCode) {
            this.ownerParCode = ownerParCode;
            }
                    
    public Integer getOrgId(){
        return orgId;
    }

        public void setOrgId(Integer orgId) {
            this.orgId = orgId;
            }
                    
    public Long getId(){
        return id;
    }

        public void setId(Long id) {
            this.id = id;
            }
                    
    public String getDealerCode(){
        return dealerCode;
    }

        public void setDealerCode(String dealerCode) {
            this.dealerCode = dealerCode;
            }
                    
    public String getDealerName(){
        return dealerName;
    }

        public void setDealerName(String dealerName) {
            this.dealerName = dealerName;
            }
                    
    public String getCcmMan(){
        return ccmMan;
    }

        public void setCcmMan(String ccmMan) {
            this.ccmMan = ccmMan;
            }
                    
    public Integer getIsError(){
        return isError;
    }

        public void setIsError(Integer isError) {
            this.isError = isError;
            }
                    
    public String getErrorMsg(){
        return errorMsg;
    }

        public void setErrorMsg(String errorMsg) {
            this.errorMsg = errorMsg;
            }
                    
    public Integer getLineNumber(){
        return lineNumber;
    }

        public void setLineNumber(Integer lineNumber) {
            this.lineNumber = lineNumber;
            }
                    
    public Integer getDataSources(){
        return dataSources;
    }

        public void setDataSources(Integer dataSources) {
            this.dataSources = dataSources;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }

        public void setIsDeleted(Boolean isDeleted) {
            this.isDeleted = isDeleted;
            }
                    
    public Integer getIsValid(){
        return isValid;
    }

        public void setIsValid(Integer isValid) {
            this.isValid = isValid;
            }
                    
    @Override
    public Date getCreatedAt(){
        return createdAt;
    }

        @Override
        public void setCreatedAt(Date createdAt) {
            this.createdAt = createdAt;
            }
                    
    @Override
    public Date getUpdatedAt(){
        return updatedAt;
    }

        @Override
        public void setUpdatedAt(Date updatedAt) {
            this.updatedAt = updatedAt;
            }
    
    @Override
    protected Serializable pkVal(){
            return this.id;
        }

@Override
    public String toString(){
        return"TeComplaintDealerCcmRefImportPO{" +
                            "appId=" + appId +
                                    ", ownerCode=" + ownerCode +
                                    ", ownerParCode=" + ownerParCode +
                                    ", orgId=" + orgId +
                                    ", id=" + id +
                                    ", dealerCode=" + dealerCode +
                                    ", dealerName=" + dealerName +
                                    ", ccmMan=" + ccmMan +
                                    ", isError=" + isError +
                                    ", errorMsg=" + errorMsg +
                                    ", lineNumber=" + lineNumber +
                                    ", dataSources=" + dataSources +
                                    ", isDeleted=" + isDeleted +
                                    ", isValid=" + isValid +
                                    ", createdAt=" + createdAt +
                                    ", updatedAt=" + updatedAt +
                    "}";
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"id");
    }

}
