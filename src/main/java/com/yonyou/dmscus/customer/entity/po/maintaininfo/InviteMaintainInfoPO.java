package com.yonyou.dmscus.customer.entity.po.maintaininfo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <p>
 * 车辆保养记录
 * </p>
 */
@TableName("tt_maintain_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InviteMaintainInfoPO extends BasePO<InviteMaintainInfoPO> {

    private static final long serialVersionUID = 1L;

    /**
     * 系统ID
     */
    @TableField("id")
    private String id;

    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;

    /**
     * 工单号
     */
    @TableField("ro_no")
    private String roNo;

    /**
     * 车架号
     */
    @TableField("vin")
    private String vin;

    /**
     * 进厂里程
     */
    @TableId("maintain_mileage")
    private String maintainMileage;

    /**
     * 保养时间
     */
    @TableField("maintain_time")
    private LocalDateTime maintainTime;

    /**
     * 上一次进厂里程
     */
    @TableField("before_maintain_mileage")
    private String beforeMaintainMileage;

    /**
     * 上次保养时间
     */
    @TableField("before_maintain_time")
    private LocalDateTime beforeMaintainTime;

    /**
     * 进场里程差
     */
    @TableField("diff_mileage")
    private Double diffMileage;

    /**
     * 进厂时间(月份)差
     */
    @TableField("diff_month")
    private Integer diffMonth;

    /**
     * 里程基准数
     */
    @TableField("datum_mileage")
    private Double datumMileage;

    /**
     * 进厂时间(月份)基准数
     */
    @TableField("datum_month")
    private Integer datumMonth;

    /**
     * 提前进场里程
     */
    @TableField("advance_mileage")
    private Double advanceMileage;

    /**
     * 提前进厂时间(月份)
     */
    @TableField("advance_month")
    private Integer advanceMonth;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 乐观锁
     */
    @TableField("version")
    private Boolean version;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 创建sql人
     */
    @TableField("create_sqlby")
    private String createSqlby;

    /**
     * 更新sql人
     */
    @TableField("update_sqlby")
    private String updateSqlby;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updateTime;
}
