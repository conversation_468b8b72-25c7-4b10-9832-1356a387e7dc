package com.yonyou.dmscus.customer.entity.po.invitationCreate;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import lombok.Data;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @date 2020/11/3 0003
 */
@TableName("tt_invite_vehicle_dealer_task_import")
@Data
public class InviteVehicleDealerTaskImportPO extends BasePO<InviteVehicleDealerTaskImportPO> {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("app_id")
    private String appId;

    @TableField("owner_code")
    private String ownerCode;

    @TableField("owner_par_code")
    private String ownerParCode;

    @TableField("org_id")
    private Long orgId;

    @TableField("dealer_code")
    private String dealerCode;

    @TableField("invite_name")
    private String inviteName;

    @TableField("follow_mode")
    private String followMode;

    @TableField("advise_in_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date adviseInDate;

    @TableField("vin")
    private String vin;

    @TableField("name")
    private String name;

    @TableField("tel")
    private String tel;

    @TableField("is_error")
    private Integer isError;

    @TableField("error_msg")
    private String errorMsg;

    @TableField("line_number")
    private Integer lineNumber;

    @TableField("created_by")
    private String createdBy;

    @TableField("license_plate_num")
    private String licensePlateNum;

    @TableField("overdue_close_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date overdueCloseDate;

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
