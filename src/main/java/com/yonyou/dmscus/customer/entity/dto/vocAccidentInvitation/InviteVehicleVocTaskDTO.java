package com.yonyou.dmscus.customer.entity.dto.vocAccidentInvitation;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 车辆特约店VOC事故邀约任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */

public class InviteVehicleVocTaskDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户姓名
     */
    private String name;

    /**
     * 电话
     */
    private String tel;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车牌号
     */
    private String licensePlateNum;

    /**
     * 进销商代码
     */
    private String dealerCode;

    /**
     * VOC事故车联系经销商时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contactDate;

    /**
     * 与客户通话情况： 接通、未接通
     */
    private Integer contactSituation;

    /**
     * VOC事故号
     */
    private String accidentNo;

    /**
     * VOC事故说明
     */
    private String accidentDetail;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否已生成邀约线索：1 是，0 否
     */
    private Integer isCreateInvite;

    /**
     * 邀约ID （关联invite_vehicle_record中id）
     */
    private Long inviteId;

    /**
     * 跟进状态
     */
    private Integer followStatus;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;


    /**
     *大区id
     */
    private String largeAreaId;


    /**
     *小区id
     */
    private String areaId;



    /**
     * 建议进厂时间开始 查询条件
     */
    private String contactDateStart;

    /**
     * 建议进厂时间结束 查询条件
     */
    private String contactDateEnd;


    /**
     * 计划进厂时间开始 查询条件
     */
    private String newContactDateStart;

    /**
     * 计划进厂时间结束 查询条件
     */
    private String newContactDateEnd;



    /**
     * 邀约创建时间开始 查询条件
     */
    private String createdAtStart;

    /**
     * 邀约创建时间结束 查询条件
     */
    private String createdAtEnd;


    /**
     * 计划进厂时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planDate;



    public InviteVehicleVocTaskDTO() {
        super();
    }

    public Date getPlanDate() {
        return planDate;
    }

    public void setPlanDate(Date planDate) {
        this.planDate = planDate;
    }

    public String getNewContactDateStart() {
        return newContactDateStart;
    }

    public void setNewContactDateStart(String newContactDateStart) {
        this.newContactDateStart = newContactDateStart;
    }

    public String getNewContactDateEnd() {
        return newContactDateEnd;
    }

    public void setNewContactDateEnd(String newContactDateEnd) {
        this.newContactDateEnd = newContactDateEnd;
    }

    public String getCreatedAtStart() {
        return createdAtStart;
    }

    public void setCreatedAtStart(String createdAtStart) {
        this.createdAtStart = createdAtStart;
    }

    public String getCreatedAtEnd() {
        return createdAtEnd;
    }

    public void setCreatedAtEnd(String createdAtEnd) {
        this.createdAtEnd = createdAtEnd;
    }

    public String getContactDateStart() {
        return contactDateStart;
    }

    public void setContactDateStart(String contactDateStart) {
        this.contactDateStart = contactDateStart;
    }

    public String getContactDateEnd() {
        return contactDateEnd;
    }

    public void setContactDateEnd(String contactDateEnd) {
        this.contactDateEnd = contactDateEnd;
    }

    public String getLargeAreaId() {
        return largeAreaId;
    }

    public void setLargeAreaId(String largeAreaId) {
        this.largeAreaId = largeAreaId;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getAppId() {
        return appId;
    }


    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }


    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerParCode() {
        return ownerParCode;
    }


    public void setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public Integer getOrgId() {
        return orgId;
    }


    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId() {
        return id;
    }


    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }


    public void setName(String name) {
        this.name = name;
    }

    public String getTel() {
        return tel;
    }


    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getVin() {
        return vin;
    }


    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getLicensePlateNum() {
        return licensePlateNum;
    }


    public void setLicensePlateNum(String licensePlateNum) {
        this.licensePlateNum = licensePlateNum;
    }

    public String getDealerCode() {
        return dealerCode;
    }


    public void setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
    }


    public Integer getContactSituation() {
        return contactSituation;
    }


    public void setContactSituation(Integer contactSituation) {
        this.contactSituation = contactSituation;
    }


    public Integer getIsCreateInvite() {
        return isCreateInvite;
    }


    public void setIsCreateInvite(Integer isCreateInvite) {
        this.isCreateInvite = isCreateInvite;
    }

    public Long getInviteId() {
        return inviteId;
    }


    public void setInviteId(Long inviteId) {
        this.inviteId = inviteId;
    }

    public Integer getFollowStatus() {
        return followStatus;
    }


    public void setFollowStatus(Integer followStatus) {
        this.followStatus = followStatus;
    }

    public Integer getDataSources() {
        return dataSources;
    }


    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }


    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid() {
        return isValid;
    }


    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }


    public Date getContactDate() {
        return contactDate;
    }

    public void setContactDate(Date contactDate) {
        this.contactDate = contactDate;
    }

    public String getAccidentNo() {
        return accidentNo;
    }

    public void setAccidentNo(String accidentNo) {
        this.accidentNo = accidentNo;
    }

    public String getAccidentDetail() {
        return accidentDetail;
    }

    public void setAccidentDetail(String accidentDetail) {
        this.accidentDetail = accidentDetail;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "InviteVehicleVocTaskDTO{" +
                "appId=" + appId +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", name=" + name +
                ", tel=" + tel +
                ", vin=" + vin +
                ", licensePlateNum=" + licensePlateNum +
                ", dealerCode=" + dealerCode +
                ", contactDate=" + contactDate +
                ", contactSituation=" + contactSituation +
                ", accidentNo=" + accidentNo +
                ", accidentDetail=" + accidentDetail +
                ", remark=" + remark +
                ", isCreateInvite=" + isCreateInvite +
                ", inviteId=" + inviteId +
                ", followStatus=" + followStatus +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
