package com.yonyou.dmscus.customer.entity.dto.common;

import java.util.List;


import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscus.customer.middleInterface.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "查询人员及职位信息", description = "查询人员及职位信息")
public class QueryUserPositionDTO extends BaseDTO {

    private static final long serialVersionUID = Constant.SERIAL_VERSION_UID;

    @ApiModelProperty("目录代码（仅用于和userOrgId一起使用查询权限数据范围）")
    private String            menuId;

    @ApiModelProperty("职位id（仅用于和menuId一起使用查询权限数据范围）")
    private String            userOrgId;

    @ApiModelProperty("手机号")
    private String            mobilePhone;

    @ApiModelProperty("员工名")
    private String            employeeName;

    @ApiModelProperty("角色代码")
    private List<String>      roleCodes;

    @ApiModelProperty("组织id")
    private List<String>      orgIds;

    @ApiModelProperty("公司id")
    private List<Long>        companyIds;

    @ApiModelProperty("用户id")
    private List<Long>        userIds;

    @ApiModelProperty("在职状态")
    private String            isOnjob;

    @ApiModelProperty("是否需要组织名称")
    private Boolean           flag;

    @ApiModelProperty("数据范围 Type:1037（若赋值则不会通过menuId和userOrgId去查询）")
    private Integer           rangeCode;

    public String getIsOnjob() {
        return isOnjob;
    }

    public void setIsOnjob(String isOnjob) {
        this.isOnjob = isOnjob;
    }

    public Boolean getFlag() {
        return flag;
    }

    public void setFlag(Boolean flag) {
        this.flag = flag;
    }

    public String getMenuId() {
        return menuId;
    }

    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }

    public String getUserOrgId() {
        return userOrgId;
    }

    public void setUserOrgId(String userOrgId) {
        this.userOrgId = userOrgId;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public List<String> getRoleCodes() {
        return roleCodes;
    }

    public void setRoleCodes(List<String> roleCodes) {
        this.roleCodes = roleCodes;
    }

    public List<String> getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(List<String> orgIds) {
        this.orgIds = orgIds;
    }

    public List<Long> getCompanyIds() {
        return companyIds;
    }

    public void setCompanyIds(List<Long> companyIds) {
        this.companyIds = companyIds;
    }

    public List<Long> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<Long> userIds) {
        this.userIds = userIds;
    }

    public Integer getRangeCode() {
        return rangeCode;
    }

    public void setRangeCode(Integer rangeCode) {
        this.rangeCode = rangeCode;
    }

    @Override
    public String toString() {
        return "QueryUserPositionDTO [menuId=" + menuId + ", userOrgId=" + userOrgId + ", mobilePhone=" + mobilePhone
               + ", employeeName=" + employeeName + ", roleCodes=" + roleCodes + ", orgIds=" + orgIds + ", companyIds="
               + companyIds + ", userIds=" + userIds + ", isOnjob=" + isOnjob + ", flag=" + flag + ", rangeCode="
               + rangeCode + "]";
    }

}
