package com.yonyou.dmscus.customer.entity.dto.complaint.AcceptComlaintData;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 *接收400修改投诉单主信息
 */
@Data
public class AcceptUpdataComplaintInfoDTO {

    /**
     * 投诉ID（投诉编号）可 用于和呼叫中心对接
     */
    private String complaintId;

    /**
     * 来电电话/投诉人电话
     */
    private String callTel;

    /**
     * 车牌号
     */
    private String licensePlateNum;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 处理经销商
     */
    private String dealerName;

    /**
     * 处理经销商代码
     */
    private String dealerCode;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updatedAt;

    /**
     * 投诉类型
     */
    private Integer type;

    /**
     * 工单性质
     */
    private  Integer workOrderNature;
    /**
     * 工单分类
     */
    private  Integer workOrderClassification;

    /**
     * 投诉来源
     */
    private Integer source;

    /**
     * 投诉单类别一级层
     */
    private String category1;

    /**
     * 投诉单类别二级层
     */
    private String category2;

    /**
     * 投诉单类别三级层
     */
    private String category3;
    /**
     * 重要等级
     */
    private Integer importanceLevel;

}
