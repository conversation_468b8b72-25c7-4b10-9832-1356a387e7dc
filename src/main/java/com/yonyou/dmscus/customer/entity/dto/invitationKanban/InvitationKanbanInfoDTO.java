package com.yonyou.dmscus.customer.entity.dto.invitationKanban;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class InvitationKanbanInfoDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer inviteType;

    private Integer inviteCount;

    private Integer inviteFinishCount;

    private Double inviteRate;

    private Integer bookingCount;

    private Integer orderCount;

    private Double hasOrderRate;


}
