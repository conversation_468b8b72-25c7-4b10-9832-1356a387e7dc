package com.yonyou.dmscus.customer.entity.dto.goodwill;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善审批流程表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */

public class GoodwillAuditProcessDTO extends BaseDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	private String appId;

	/**
	 * 所有者代码
	 */
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	private Integer orgId;

	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 审核对象
	 */
	private Integer auditObject;

	/**
	 * 审批类型
	 */
	private Integer auditType;

	/**
	 * 审批职位
	 */
	private String auditPosition;

	/**
	 * 最小取值关系（大于等于小于）
	 */
	private Integer minValueRelation;

	/**
	 * 最小审批金额
	 */
	private BigDecimal minAuditPrice;

	/**
	 * 最大取值关系
	 */
	private Integer maxValueRelation;

	/**
	 * 最大审批金额
	 */
	private BigDecimal maxAuditPrice;

	/**
	 * 审批顺序
	 */
	private Integer auditSort;
	/**
	 * 是否有效
	 */
	private Integer isPublic;
	/**
	 * 是否有效
	 */
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	private Boolean isDeleted;
	/**
	 * 拒单设置
	 */
	private Integer month;

	/**
	 * 创建时间
	 */

	private Date createdAt;

	/**
	 * 修改时间
	 */

	private Date updatedAt;

	public Integer getIsPublic() {
		return isPublic;
	}

	public void setIsPublic(Integer isPublic) {
		this.isPublic = isPublic;
	}

	public GoodwillAuditProcessDTO() {
		super();
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	public String getOwnerParCode() {
		return ownerParCode;
	}

	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getAuditObject() {
		return auditObject;
	}

	public void setAuditObject(Integer auditObject) {
		this.auditObject = auditObject;
	}

	public Integer getAuditType() {
		return auditType;
	}

	public void setAuditType(Integer auditType) {
		this.auditType = auditType;
	}

	public String getAuditPosition() {
		return auditPosition;
	}

	public void setAuditPosition(String auditPosition) {
		this.auditPosition = auditPosition;
	}

	public Integer getMinValueRelation() {
		return minValueRelation;
	}

	public void setMinValueRelation(Integer minValueRelation) {
		this.minValueRelation = minValueRelation;
	}

	public BigDecimal getMinAuditPrice() {
		return minAuditPrice;
	}

	public void setMinAuditPrice(BigDecimal minAuditPrice) {
		this.minAuditPrice = minAuditPrice;
	}

	public Integer getMaxValueRelation() {
		return maxValueRelation;
	}

	public void setMaxValueRelation(Integer maxValueRelation) {
		this.maxValueRelation = maxValueRelation;
	}

	public BigDecimal getMaxAuditPrice() {
		return maxAuditPrice;
	}

	public void setMaxAuditPrice(BigDecimal maxAuditPrice) {
		this.maxAuditPrice = maxAuditPrice;
	}

	public Integer getAuditSort() {
		return auditSort;
	}

	public void setAuditSort(Integer auditSort) {
		this.auditSort = auditSort;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Integer getMonth() {
		return month;
	}

	public void setMonth(Integer month) {
		this.month = month;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	public String toString() {
		return "GoodwillAuditProcessDTO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", auditObject=" + auditObject + ", auditType="
				+ auditType + ", auditPosition=" + auditPosition + ", minValueRelation=" + minValueRelation
				+ ", minAuditPrice=" + minAuditPrice + ", maxValueRelation=" + maxValueRelation + ", maxAuditPrice="
				+ maxAuditPrice + ", auditSort=" + auditSort + ", isValid=" + isValid + ", isDeleted=" + isDeleted
				+ ", createdAt=" + createdAt + ", updatedAt=" + updatedAt + "}";
	}

	/**
	 * 将DTO 转换为PO //对某个对象属性进行赋值
	 * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
	 * 
	 * @param poClass
	 *            需要转换的poClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
		return super.transDtoToPo(poClass);
	}

	/**
	 * 将DTO 转换为PO
	 * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
	 * 
	 * @param po
	 *            需要转换的对象
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BasePO> void transDtoToPo(T po) {
		BeanMapperUtil.copyProperties(this, po, "id");
	}

}
