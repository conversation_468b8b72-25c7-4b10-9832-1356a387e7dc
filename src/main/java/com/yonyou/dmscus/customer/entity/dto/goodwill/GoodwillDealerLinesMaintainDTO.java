package com.yonyou.dmscus.customer.entity.dto.goodwill;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 经销商亲善额度维护
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
    
public class GoodwillDealerLinesMaintainDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

        
    /**
     * 系统ID
     */
                private String appId;
                
    /**
     * 所有者代码
     */
                private String ownerCode;
                
    /**
     * 所有者的父组织代码
     */
                private String ownerParCode;
                
    /**
     * 组织ID
     */
                private Integer orgId;
                
    /**
     * 主键id
     */
                private Long id;
                
    /**
     * 亲善性质
     */
                private Integer goodwillNature;
                
    /**
     * 经销商代码
     */
                private String dealerCode;
                
    /**
     * 有效开始时间
     */
    
    private Date validStartDate;
                
    /**
     * 有效结束时间
     */
    private Date validEndDate;
                
    /**
     * 年度预算
     */
                private BigDecimal yearlyBudget;
                
    /**
     * 创建日期(查询条件)
     */
    private Date foundDateStart;
    /**
     * 创建日期(查询条件)
     */
    private Date foundDateEnd;
    /**
     * 创建日期
     */
    private Date foundDate;
                
    /**
     * 是否有效
     */
                private Integer isValid;
                
    /**
     * 是否删除:1,删除；0,未删除
     */
                private Boolean isDeleted;
                
    /**
     * 创建时间
     */
    private Date createdAt;
                
    /**
     * 修改时间
     */
    private Date updatedAt;
    
    //区域-区域经理
    private Integer areaManage;
    
  //区域-区域经理(查询用)
    private String areaManages;
    
    //集团
    private String bloc;
    
    //经销商名称
    private String dealerName;
    
    //省份
    private Integer province;
    
  //省份
    private String provinceName;
    
    
            
    public String getProvinceName() {
		return provinceName;
	}


	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}


	public Integer getProvince() {
		return province;
	}


	public void setProvince(Integer province) {
		this.province = province;
	}


	public String getDealerName() {
		return dealerName;
	}


	public void setDealerName(String dealerName) {
		this.dealerName = dealerName;
	}


	public String getBloc() {
		return bloc;
	}


	public void setBloc(String bloc) {
		this.bloc = bloc;
	}


	public String getAreaManages() {
		return areaManages;
	}


	public void setAreaManages(String areaManages) {
		this.areaManages = areaManages;
	}


	public Integer getAreaManage() {
		return areaManage;
	}


	public void setAreaManage(Integer areaManage) {
		this.areaManage = areaManage;
	}


	public GoodwillDealerLinesMaintainDTO() {
        super();
    }

                                
    public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
            }
                                
    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
            }
                                
    public String getOwnerParCode(){
        return ownerParCode;
    }


    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
            }
                                
    public Integer getOrgId(){
        return orgId;
    }


    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
            }
                                            
    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
            }
                                
    public Integer getGoodwillNature(){
        return goodwillNature;
    }


    public void  setGoodwillNature(Integer goodwillNature) {
        this.goodwillNature = goodwillNature;
            }
                                
    public String getDealerCode(){
        return dealerCode;
    }


    public void  setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
            }
                                
  
                                
    public Date getValidStartDate() {
		return validStartDate;
	}


	public void setValidStartDate(Date validStartDate) {
		this.validStartDate = validStartDate;
	}


	public Date getValidEndDate() {
		return validEndDate;
	}


	public void setValidEndDate(Date validEndDate) {
		this.validEndDate = validEndDate;
	}


	public Date getFoundDate() {
		return foundDate;
	}


	public void setFoundDate(Date foundDate) {
		this.foundDate = foundDate;
	}


	public Date getFoundDateStart() {
		return foundDateStart;
	}


	public void setFoundDateStart(Date foundDateStart) {
		this.foundDateStart = foundDateStart;
	}


	public Date getFoundDateEnd() {
		return foundDateEnd;
	}


	public void setFoundDateEnd(Date foundDateEnd) {
		this.foundDateEnd = foundDateEnd;
	}


	public BigDecimal getYearlyBudget(){
        return yearlyBudget;
    }


    public void  setYearlyBudget(BigDecimal yearlyBudget) {
        this.yearlyBudget = yearlyBudget;
            }
                                
                                
    public Integer getIsValid(){
        return isValid;
    }


    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
            }
                                
   
    
    public Date getCreatedAt() {
		return createdAt;
	}


	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}


	public Date getUpdatedAt() {
		return updatedAt;
	}


	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}


	@Override
    public String toString() {
        return "GoodwillDealerLinesMaintainDTO{" +
                                            "appId=" + appId +
                                                            ", ownerCode=" + ownerCode +
                                                            ", ownerParCode=" + ownerParCode +
                                                            ", orgId=" + orgId +
                                                            ", id=" + id +
                                                            ", goodwillNature=" + goodwillNature +
                                                            ", dealerCode=" + dealerCode +
                                                            ", validStartDate=" + validStartDate +
                                                            ", validEndDate=" + validEndDate +
                                                            ", yearlyBudget=" + yearlyBudget +
                                                            ", foundDate=" + foundDate +
                                                            ", isValid=" + isValid +
                                                            ", isDeleted=" + isDeleted +
                                                            ", createdAt=" + createdAt +
                                                            ", updatedAt=" + updatedAt +
                                    "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
