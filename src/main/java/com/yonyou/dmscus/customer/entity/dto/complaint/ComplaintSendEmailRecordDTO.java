package com.yonyou.dmscus.customer.entity.dto.complaint;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.function.utils.jsonSerializer.date.JsonDateDeserializer;
import com.yonyou.dmscloud.function.utils.jsonSerializer.date.JsonDateSerializer;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 客户投诉发送邮件记录
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
    
public class ComplaintSendEmailRecordDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

        
    /**
     * 系统ID
     */
                private String appId;
                
    /**
     * 所有者代码 
     */
                private String ownerCode;
                
    /**
     * 所有者的父组织代码 
     */
                private String ownerParCode;
                
    /**
     * 组织ID
     */
                private Integer orgId;
                
    /**
     * 主键ID
     */
                private Long id;
                
    /**
     * 投诉信息表主键ID
     */
                private Long complaintInfoId;
                
    /**
     * 投诉ID
     */
                private String complaintId;
                
    /**
     * 收件人邮箱
     */
                private String sendEmail;
    /**
     * 收件人
     */
    private String receipter;
    /**
     * 协助部门
     */
    private String assistDepartment;
    /**
     * 协助经销商
     */
    private String assistDealerCode;
    /**
     * 协助部门名称
     */
    private String assistDepartmentName;
    /**
     * 协助经销商名称
     */
    private String assistDealerName;
                
    /**
     * 邮箱类型
     */
                private Integer emailType;
                
    /**
     * 发送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone="GMT+8")
    private Date sendTime;
                
    /**
     * 发送状态 发送成功、发送失败
     */
                private Integer sendStatus;
                
    /**
     * 邮箱
     */
                private String email;
                
    /**
     * 标题
     */
                private String title;
                
    /**
     * 主要内容
     */
                private String contect;
                
    /**
     * 数据来源 
     */
                private Integer dataSources;
                
    /**
     * 是否删除
     */
                private Boolean isDeleted;
                
    /**
     * 是否有效 
     */
                private Integer isValid;
                
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone="GMT+8")
    private Date createdAt;
                
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone="GMT+8")
    private Date updatedAt;
            
    public ComplaintSendEmailRecordDTO() {
        super();
    }

    public String getReceipter() {
        return receipter;
    }

    public void setReceipter(String receipter) {
        this.receipter = receipter;
    }

    public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
            }
                                
    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
            }
                                
    public String getOwnerParCode(){
        return ownerParCode;
    }


    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
            }
                                
    public Integer getOrgId(){
        return orgId;
    }


    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
            }
                                            
    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
            }
                                
    public Long getComplaintInfoId(){
        return complaintInfoId;
    }


    public void  setComplaintInfoId(Long complaintInfoId) {
        this.complaintInfoId = complaintInfoId;
            }
                                
    public String getComplaintId(){
        return complaintId;
    }


    public void  setComplaintId(String complaintId) {
        this.complaintId = complaintId;
            }
                                
    public String getSendEmail(){
        return sendEmail;
    }


    public void  setSendEmail(String sendEmail) {
        this.sendEmail = sendEmail;
            }
                                
    public Integer getEmailType(){
        return emailType;
    }


    public void  setEmailType(Integer emailType) {
        this.emailType = emailType;
            }
                                
    public Date getSendTime(){
        return sendTime;
    }


    public void  setSendTime(Date sendTime) {
        this.sendTime = sendTime;
            }
                                
    public Integer getSendStatus(){
        return sendStatus;
    }


    public void  setSendStatus(Integer sendStatus) {
        this.sendStatus = sendStatus;
            }
                                
    public String getEmail(){
        return email;
    }


    public void  setEmail(String email) {
        this.email = email;
            }
                                
    public String getTitle(){
        return title;
    }


    public void  setTitle(String title) {
        this.title = title;
            }
                                
    public String getContect(){
        return contect;
    }


    public void  setContect(String contect) {
        this.contect = contect;
            }
                                
    public Integer getDataSources(){
        return dataSources;
    }


    public void  setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
            }
                                
    public Integer getIsValid(){
        return isValid;
    }


    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
            }
                                
    public Date getCreatedAt(){
        return createdAt;
    }


    public void  setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
            }
                                
    public Date getUpdatedAt(){
        return updatedAt;
    }

    public String getAssistDepartment() {
        return assistDepartment;
    }

    public void setAssistDepartment(String assistDepartment) {
        this.assistDepartment = assistDepartment;
    }

    public String getAssistDealerCode() {
        return assistDealerCode;
    }

    public void setAssistDealerCode(String assistDealerCode) {
        this.assistDealerCode = assistDealerCode;
    }

    public String getAssistDepartmentName() {
        return assistDepartmentName;
    }

    public void setAssistDepartmentName(String assistDepartmentName) {
        this.assistDepartmentName = assistDepartmentName;
    }

    public String getAssistDealerName() {
        return assistDealerName;
    }

    public void setAssistDealerName(String assistDealerName) {
        this.assistDealerName = assistDealerName;
    }

    public void  setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
            }
    
    @Override
    public String toString() {
        return "ComplaintSendEmailRecordDTO{" +
                                            "appId=" + appId +
                                                            ", ownerCode=" + ownerCode +
                                                            ", ownerParCode=" + ownerParCode +
                                                            ", orgId=" + orgId +
                                                            ", id=" + id +
                                                            ", complaintInfoId=" + complaintInfoId +
                                                            ", complaintId=" + complaintId +
                                                            ", sendEmail=" + sendEmail +
                                                            ", emailType=" + emailType +
                                                            ", sendTime=" + sendTime +
                                                            ", sendStatus=" + sendStatus +
                                                            ", email=" + email +
                                                            ", title=" + title +
                                                            ", contect=" + contect +
                                                            ", dataSources=" + dataSources +
                                                            ", isDeleted=" + isDeleted +
                                                            ", isValid=" + isValid +
                                                            ", createdAt=" + createdAt +
                                                            ", updatedAt=" + updatedAt +
                                    "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
