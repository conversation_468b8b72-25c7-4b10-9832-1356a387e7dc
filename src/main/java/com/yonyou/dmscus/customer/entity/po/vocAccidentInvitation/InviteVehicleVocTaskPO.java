package com.yonyou.dmscus.customer.entity.po.vocAccidentInvitation;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 车辆特约店VOC事故邀约任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
@TableName("tt_invite_vehicle_voc_task")
public class InviteVehicleVocTaskPO extends BasePO<InviteVehicleVocTaskPO> {

    private static final long serialVersionUID = 1L;


    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;


    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户姓名
     */
    @TableField("name")
    private String name;

    /**
     * 电话
     */
    @TableField("tel")
    private String tel;

    /**
     * 车架号
     */
    @TableField("vin")
    private String vin;

    /**
     * 车牌号
     */
    @TableField("license_plate_num")
    private String licensePlateNum;

    /**
     * 进销商代码
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * VOC事故车联系经销商时间
     */
    @TableField("contact_date")
    private Date contactDate;

    /**
     * 与客户通话情况： 接通、未接通
     */
    @TableField("contact_situation")
    private Integer contactSituation;

    /**
     * VOC事故号
     */
    @TableField("accident_no")
    private String accidentNo;

    /**
     * VOC事故说明
     */
    @TableField("accident_detail")
    private String accidentDetail;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否已生成邀约线索：1 是，0 否
     */
    @TableField("is_create_invite")
    private Integer isCreateInvite;

    /**
     * 邀约ID （关联invite_vehicle_record中id）
     */
    @TableField("invite_id")
    private Long inviteId;

    /**
     * 跟进状态
     */
    @TableField("follow_status")
    private Integer followStatus;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;



    /**
     * 建议进厂时间开始 查询条件
     */
    @TableField(exist = false)
    private String contactDateStart;

    /**
     * 建议进厂时间结束 查询条件
     */
    @TableField(exist = false)
    private String contactDateEnd;


    /**
     * 计划进厂时间开始 查询条件
     */
    @TableField(exist = false)
    private String newContactDateStart;

    /**
     * 计划进厂时间结束 查询条件
     */
    @TableField(exist = false)
    private String newContactDateEnd;



    /**
     * 邀约创建时间开始 查询条件
     */
    @TableField(exist = false)
    private String createdAtStart;

    /**
     * 邀约创建时间结束 查询条件
     */
    @TableField(exist = false)
    private String createdAtEnd;


    /**
     * 计划进厂时间
     */
    @TableField(exist = false)
    private Date planDate;


    @TableField(exist = false)
    private List<String> dealerCodes;



    public InviteVehicleVocTaskPO() {
        super();
    }

    public List<String> getDealerCodes() {
        return dealerCodes;
    }

    public void setDealerCodes(List<String> dealerCodes) {
        this.dealerCodes = dealerCodes;
    }

    public Date getPlanDate() {
        return planDate;
    }

    public void setPlanDate(Date planDate) {
        this.planDate = planDate;
    }

    public String getContactDateStart() {
        return contactDateStart;
    }

    public void setContactDateStart(String contactDateStart) {
        this.contactDateStart = contactDateStart;
    }

    public String getContactDateEnd() {
        return contactDateEnd;
    }

    public void setContactDateEnd(String contactDateEnd) {
        this.contactDateEnd = contactDateEnd;
    }

    public String getNewContactDateStart() {
        return newContactDateStart;
    }

    public void setNewContactDateStart(String newContactDateStart) {
        this.newContactDateStart = newContactDateStart;
    }

    public String getNewContactDateEnd() {
        return newContactDateEnd;
    }

    public void setNewContactDateEnd(String newContactDateEnd) {
        this.newContactDateEnd = newContactDateEnd;
    }

    public String getCreatedAtStart() {
        return createdAtStart;
    }

    public void setCreatedAtStart(String createdAtStart) {
        this.createdAtStart = createdAtStart;
    }

    public String getCreatedAtEnd() {
        return createdAtEnd;
    }

    public void setCreatedAtEnd(String createdAtEnd) {
        this.createdAtEnd = createdAtEnd;
    }



    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }


    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getLicensePlateNum() {
        return licensePlateNum;
    }

    public void setLicensePlateNum(String licensePlateNum) {
        this.licensePlateNum = licensePlateNum;
    }

    public String getDealerCode() {
        return dealerCode;
    }

    public void setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
    }


    public Integer getIsCreateInvite() {
        return isCreateInvite;
    }

    public void setIsCreateInvite(Integer isCreateInvite) {
        this.isCreateInvite = isCreateInvite;
    }

    public Long getInviteId() {
        return inviteId;
    }

    public void setInviteId(Long inviteId) {
        this.inviteId = inviteId;
    }

    public Integer getFollowStatus() {
        return followStatus;
    }

    public void setFollowStatus(Integer followStatus) {
        this.followStatus = followStatus;
    }

    public Integer getDataSources() {
        return dataSources;
    }

    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Date getContactDate() {
        return contactDate;
    }

    public void setContactDate(Date contactDate) {
        this.contactDate = contactDate;
    }

    public Integer getContactSituation() {
        return contactSituation;
    }

    public void setContactSituation(Integer contactSituation) {
        this.contactSituation = contactSituation;
    }

    public String getAccidentNo() {
        return accidentNo;
    }

    public void setAccidentNo(String accidentNo) {
        this.accidentNo = accidentNo;
    }

    public String getAccidentDetail() {
        return accidentDetail;
    }

    public void setAccidentDetail(String accidentDetail) {
        this.accidentDetail = accidentDetail;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "InviteVehicleVocTaskPO{" +
                ", ownerCode=" + ownerCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", name=" + name +
                ", tel=" + tel +
                ", vin=" + vin +
                ", licensePlateNum=" + licensePlateNum +
                ", dealerCode=" + dealerCode +
                ", contactDate=" + contactDate +
                ", contactSituation=" + contactSituation +
                ", accidentNo=" + accidentNo +
                ", accidentDetail=" + accidentDetail +
                ", remark=" + remark +
                ", isCreateInvite=" + isCreateInvite +
                ", inviteId=" + inviteId +
                ", followStatus=" + followStatus +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                "}";
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
