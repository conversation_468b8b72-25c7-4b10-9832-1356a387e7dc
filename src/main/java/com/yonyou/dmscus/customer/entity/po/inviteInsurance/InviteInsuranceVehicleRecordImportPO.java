package com.yonyou.dmscus.customer.entity.po.inviteInsurance;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;

import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <p>
 * 车辆续保线索导入临时表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-19
 */
@Data
@ToString
@TableName("te_invite_insurance_vehicle_record_import")
public class InviteInsuranceVehicleRecordImportPO extends BasePO<InviteInsuranceVehicleRecordImportPO> {

    private static final long serialVersionUID = 1L;

    /**
     * 系统ID
     */
    @TableField("app_id")
    private String appId;

    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    @TableField("owner_par_code")
    private String ownerParCode;

    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 邀约ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 车架号
     */
    @TableField("vin")
    private String vin;

    /**
     * 车牌号
     */
    @TableField("license_plate_num")
    private String licensePlateNum;

    /**
     * 车主名称
     */
    @TableField("name")
    private String name;

    /**
     * 车主电话
     */
    @TableField("tel")
    private String tel;

    /**
     * 商业险到期日期
     */
    @TableField("vi_finish_date")
    private String viFinishDate;

    /**
     * 交强险到期日期
     */
    @TableField("clivta_finish_date")
    private String clivtaFinishDate;

    /**
     * 跟进服务顾问ID
     */
    @TableField("SA_ID")
    private String saId;

    /**
     * 跟进服务顾问姓名
     */
    @TableField("SA_NAME")
    private String saName;

    /**
     * 上次跟进服务顾问ID
     */
    @TableField("LAST_SA_ID")
    private String lastSaId;

    /**
     * 上次跟进服务顾问姓名
     */
    @TableField("LAST_SA_NAME")
    private String lastSaName;

    /**
     * 经销商代码
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 车主年龄
     */
    @TableField("age")
    private String age;

    /**
     * 车主性别
     */
    @TableField("sex")
    private String sex;

    /**
     * 车型
     */
    @TableField("model")
    private String model;

    /**
     * 客户唯一ID
     */
    @TableField("one_id")
    private Long oneId;

    /**
     * 是否错误,1：是,0：否
     */
    @TableField("is_error")
    private Integer isError;

    /**
     * 错误信息
     */
    @TableField("error_msg")
    private String errorMsg;

    /**
     * 行数
     */
    @TableField("line_number")
    private Integer lineNumber;

    /**
     * 数据来源：1:易保线索  2:投保线索  3:导入线索  4:迁移线索
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;

    /**
     * 导入保险公司名称
     */
    @TableField("insurance_name")
    private String insuranceName;

    public InviteInsuranceVehicleRecordImportPO() {
        super();
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
