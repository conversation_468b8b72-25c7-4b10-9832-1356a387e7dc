package com.yonyou.dmscus.customer.entity.dto.goodwill;

import java.io.Serializable;

//返回经销商信息
@SuppressWarnings("serial")
public class DealerInfoDTO implements Serializable {
	private Integer id;
	private String companyCode; // 经销商代码
	private String companyNameCn; // 经销商名称中文
	private String companyShortNameCn; // 经销商简称中文
	private Integer companyType; // 公司类型:1005
	private String groupCompanyId; // 集团
	private String groupCompanyName; // 集团名称
	private Integer provinceId; // 省份
	private String provinceName;// 省份名称
	private Integer cityId; // 城市
	private String cityName;// 城市名称
	private Integer countyId; // 区域
	private String addressZh; // 地址
	private double longitude; // 经度
	private double latitude; // 纬度
	private String phone; // 电话
	private String fax; // 传真
	private Integer dealerType; // 经销商类型
	private Integer dealerScale; // 经销商规模
	private Integer wholesaleGrant; // 是否批售授权经销商
	private Integer status; // 状态
	private String remark; // 备注
	private String salesLine; // 销售热线
	private String salesParmaCode; // 销售ParmaCode
	private String gradeSales; // 客户评分销售
	private String bankSales; // 开户银行销售
	private String bankAccountSales; // 开户账号销售
	private String taxSales; // 税号销售
	private String afterLine; // 售后热线
	private String afterParmaCode; // 售后ParmaCode
	private String gradeAfter; // 客户评分售后
	private String bankAfter; // 开户银行售后
	private String bankAccountAfter; // 开户账号售后
	private String taxAfter; // 税号售后
	private String facility; // FACILITY
	private String dealershipOutlet; // DEALERSHIP_OUTLET
	private String operationDateRd; // OPERATION_DATE_RD
	private String operationDateInterent; // OPERATION_DATE_INTERNET
	private String relocationOrUpgrade; // RELOCATION_OR_UPGRADE
	private String shortDate; // SHORT_DATE
	private String vipsCode; // VIPS_CODE
	private String vrCode; // VR_OOED
	private Integer vmiLdc; // VMI_LDC
	private String partWarehouse; // 零配件仓库
	private String warehouseAddress; // 仓库发货地址
	private String openTime; // 开店时间
	private String closeTime; // 关店时间
	private String storefrontPhotoUrl; // 经销商门头照片url
	private String orgName;
	private String orgCode;
	private Integer orgId;
	private Integer afterSmallAreaId;// 售后小区ID
	private Integer afterBigAreaId;// 售后大区ID
	private String afterSmallAreaName;// 售后小区名称
	private String afterBigAreaName;// 售后大区名称

	public Integer getAfterSmallAreaId() {
		return afterSmallAreaId;
	}

	public void setAfterSmallAreaId(Integer afterSmallAreaId) {
		this.afterSmallAreaId = afterSmallAreaId;
	}

	public Integer getAfterBigAreaId() {
		return afterBigAreaId;
	}

	public void setAfterBigAreaId(Integer afterBigAreaId) {
		this.afterBigAreaId = afterBigAreaId;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getCompanyNameCn() {
		return companyNameCn;
	}

	public void setCompanyNameCn(String companyNameCn) {
		this.companyNameCn = companyNameCn;
	}

	public String getCompanyShortNameCn() {
		return companyShortNameCn;
	}

	public void setCompanyShortNameCn(String companyShortNameCn) {
		this.companyShortNameCn = companyShortNameCn;
	}

	public Integer getCompanyType() {
		return companyType;
	}

	public void setCompanyType(Integer companyType) {
		this.companyType = companyType;
	}

	public String getGroupCompanyId() {
		return groupCompanyId;
	}

	public void setGroupCompanyId(String groupCompanyId) {
		this.groupCompanyId = groupCompanyId;
	}

	public String getGroupCompanyName() {
		return groupCompanyName;
	}

	public void setGroupCompanyName(String groupCompanyName) {
		this.groupCompanyName = groupCompanyName;
	}

	public Integer getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Integer provinceId) {
		this.provinceId = provinceId;
	}

	public Integer getCityId() {
		return cityId;
	}

	public void setCityId(Integer cityId) {
		this.cityId = cityId;
	}

	public Integer getCountyId() {
		return countyId;
	}

	public void setCountyId(Integer countyId) {
		this.countyId = countyId;
	}

	public String getAddressZh() {
		return addressZh;
	}

	public void setAddressZh(String addressZh) {
		this.addressZh = addressZh;
	}

	public double getLongitude() {
		return longitude;
	}

	public void setLongitude(double longitude) {
		this.longitude = longitude;
	}

	public double getLatitude() {
		return latitude;
	}

	public void setLatitude(double latitude) {
		this.latitude = latitude;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getFax() {
		return fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	public Integer getDealerType() {
		return dealerType;
	}

	public void setDealerType(Integer dealerType) {
		this.dealerType = dealerType;
	}

	public Integer getDealerScale() {
		return dealerScale;
	}

	public void setDealerScale(Integer dealerScale) {
		this.dealerScale = dealerScale;
	}

	public Integer getWholesaleGrant() {
		return wholesaleGrant;
	}

	public void setWholesaleGrant(Integer wholesaleGrant) {
		this.wholesaleGrant = wholesaleGrant;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getSalesLine() {
		return salesLine;
	}

	public void setSalesLine(String salesLine) {
		this.salesLine = salesLine;
	}

	public String getSalesParmaCode() {
		return salesParmaCode;
	}

	public void setSalesParmaCode(String salesParmaCode) {
		this.salesParmaCode = salesParmaCode;
	}

	public String getGradeSales() {
		return gradeSales;
	}

	public void setGradeSales(String gradeSales) {
		this.gradeSales = gradeSales;
	}

	public String getBankSales() {
		return bankSales;
	}

	public void setBankSales(String bankSales) {
		this.bankSales = bankSales;
	}

	public String getBankAccountSales() {
		return bankAccountSales;
	}

	public void setBankAccountSales(String bankAccountSales) {
		this.bankAccountSales = bankAccountSales;
	}

	public String getTaxSales() {
		return taxSales;
	}

	public void setTaxSales(String taxSales) {
		this.taxSales = taxSales;
	}

	public String getAfterLine() {
		return afterLine;
	}

	public void setAfterLine(String afterLine) {
		this.afterLine = afterLine;
	}

	public String getAfterParmaCode() {
		return afterParmaCode;
	}

	public void setAfterParmaCode(String afterParmaCode) {
		this.afterParmaCode = afterParmaCode;
	}

	public String getGradeAfter() {
		return gradeAfter;
	}

	public void setGradeAfter(String gradeAfter) {
		this.gradeAfter = gradeAfter;
	}

	public String getBankAfter() {
		return bankAfter;
	}

	public void setBankAfter(String bankAfter) {
		this.bankAfter = bankAfter;
	}

	public String getBankAccountAfter() {
		return bankAccountAfter;
	}

	public void setBankAccountAfter(String bankAccountAfter) {
		this.bankAccountAfter = bankAccountAfter;
	}

	public String getTaxAfter() {
		return taxAfter;
	}

	public void setTaxAfter(String taxAfter) {
		this.taxAfter = taxAfter;
	}

	public String getFacility() {
		return facility;
	}

	public void setFacility(String facility) {
		this.facility = facility;
	}

	public String getDealershipOutlet() {
		return dealershipOutlet;
	}

	public void setDealershipOutlet(String dealershipOutlet) {
		this.dealershipOutlet = dealershipOutlet;
	}

	public String getOperationDateRd() {
		return operationDateRd;
	}

	public void setOperationDateRd(String operationDateRd) {
		this.operationDateRd = operationDateRd;
	}

	public String getOperationDateInterent() {
		return operationDateInterent;
	}

	public void setOperationDateInterent(String operationDateInterent) {
		this.operationDateInterent = operationDateInterent;
	}

	public String getRelocationOrUpgrade() {
		return relocationOrUpgrade;
	}

	public void setRelocationOrUpgrade(String relocationOrUpgrade) {
		this.relocationOrUpgrade = relocationOrUpgrade;
	}

	public String getShortDate() {
		return shortDate;
	}

	public void setShortDate(String shortDate) {
		this.shortDate = shortDate;
	}

	public String getVipsCode() {
		return vipsCode;
	}

	public void setVipsCode(String vipsCode) {
		this.vipsCode = vipsCode;
	}

	public String getVrCode() {
		return vrCode;
	}

	public void setVrCode(String vrCode) {
		this.vrCode = vrCode;
	}

	public Integer getVmiLdc() {
		return vmiLdc;
	}

	public void setVmiLdc(Integer vmiLdc) {
		this.vmiLdc = vmiLdc;
	}

	public String getPartWarehouse() {
		return partWarehouse;
	}

	public void setPartWarehouse(String partWarehouse) {
		this.partWarehouse = partWarehouse;
	}

	public String getWarehouseAddress() {
		return warehouseAddress;
	}

	public void setWarehouseAddress(String warehouseAddress) {
		this.warehouseAddress = warehouseAddress;
	}

	public String getOpenTime() {
		return openTime;
	}

	public void setOpenTime(String openTime) {
		this.openTime = openTime;
	}

	public String getCloseTime() {
		return closeTime;
	}

	public void setCloseTime(String closeTime) {
		this.closeTime = closeTime;
	}

	public String getStorefrontPhotoUrl() {
		return storefrontPhotoUrl;
	}

	public void setStorefrontPhotoUrl(String storefrontPhotoUrl) {
		this.storefrontPhotoUrl = storefrontPhotoUrl;
	}

	public String getAfterSmallAreaName() {
		return afterSmallAreaName;
	}

	public void setAfterSmallAreaName(String afterSmallAreaName) {
		this.afterSmallAreaName = afterSmallAreaName;
	}

	public String getAfterBigAreaName() {
		return afterBigAreaName;
	}

	public void setAfterBigAreaName(String afterBigAreaName) {
		this.afterBigAreaName = afterBigAreaName;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

}
