package com.yonyou.dmscus.customer.entity.dto.faultLight;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TtFaultCallDetailsDTO implements Serializable {

    private static final long serialVersionUID = 6878952456276423386L;

    @ApiModelProperty("客户姓名")
    private String  cusName;

    @ApiModelProperty("客户手机号")
    private String  cusNumber;

    @ApiModelProperty("通话时长")
    private Integer  callLength;

    @ApiModelProperty("通话时间")
    private Date  startTime;

    private String inviteId;
}
