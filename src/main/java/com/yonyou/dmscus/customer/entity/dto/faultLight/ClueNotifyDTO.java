package com.yonyou.dmscus.customer.entity.dto.faultLight;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * description 故障灯线索消息通知入参
 * <AUTHOR>
 * @date 2023/7/20 10:31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClueNotifyDTO implements Serializable {
    /**车牌号*/
    private String plateNumber;
    /**车辆Vin码*/
    private String vehicleVin;
    /**故障发生城市*/
    private String faultCityName;
    /**线索生成时间*/
    private Date clueGenTimeDate;
    private String clueGenTime;
    /**故障名称*/
    private String warningName;
    /**线索下发时间*/
    private Date clueDisTimeDate;
    private String clueDisTime;
    /**400外呼备注*/
    private String comments;
    /**经销商CODE*/
    private String dealerCode;
}
