package com.yonyou.dmscus.customer.entity.po.productdeliverer;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import com.yonyou.dmscloud.framework.base.po.BasePO;

import java.time.LocalDateTime;

import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

/**
 * <p>
 * 主单经销商送修人信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-02
 */
@Data
@TableName("tt_product_deliverer")
public class ProductDelivererPO extends BasePO<ProductDelivererPO> {

    private static final long serialVersionUID = 1L;



    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;



    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经销商编码
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 经销商名称
     */
    @TableField("dealer_name")
    private String dealerName;

    /**
     * vin车架号
     */
    @TableField("vin")
    private String vin;

    /**
     * 送修人姓名
     */
    @TableField("deliverer")
    private String deliverer;

    /**
     * 送修人电话
     */
    @TableField("deliverer_phone")
    private String delivererPhone;

    /**
     * 送修人手机
     */
    @TableField("deliverer_mobile")
    private String delivererMobile;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除（0：否   1：是）
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;




    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
