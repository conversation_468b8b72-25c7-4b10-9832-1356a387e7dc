package com.yonyou.dmscus.customer.entity.dto.accidentClues;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.service.excel.ExcelColumnDefine;
import com.yonyou.dmscloud.function.domains.dto.DataImportDto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel(value = "事故线索Excel导入实体")
@Data
public class AccidentCluesImportDTO extends DataImportDto{
	
	@ExcelColumnDefine(value = 1)
	private String license;
	
	@ExcelColumnDefine(value = 2)
	private String models;
	
	@ExcelColumnDefine(value =3 )
	 private String contacts;
	
	@ExcelColumnDefine(value =4 )
	private String contactsPhone;
	
	@ExcelColumnDefine(value =5 )
	private String cluesResource;
	
	@ExcelColumnDefine(value =6 )
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date reportDate;
	
	@ExcelColumnDefine(value =7 )
	private String accidentAddress;
	
	@ExcelColumnDefine(value =8 )
	private String  insuranceCompanyName;
	
	@ExcelColumnDefine(value =9 )
	 private String remark;
	

}
