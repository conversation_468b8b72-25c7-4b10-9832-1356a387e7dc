package com.yonyou.dmscus.customer.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023/12/5 11:02
 * @Version 1.0
 */
@Data
@ApiModel(value = "HistogramDataVO", description = "事故线索柱状图")
public class HistogramDataVO {

    @ApiModelProperty(name = "日期")
    private String histogramDate;

    @ApiModelProperty(name = "跟进人")
    private Integer followPeople;

    @ApiModelProperty(name = "跟进人姓名")
    private String followPeopleName;

    @ApiModelProperty(name = "跟进成功数")
    private Integer successCount;

    @ApiModelProperty(name = "跟进失败数")
    private Integer failCount;

    @ApiModelProperty(name = "送修留修率")
    private BigDecimal repairRate;

    @ApiModelProperty(name = "返修留修率")
    private BigDecimal backRepairRate;

    @ApiModelProperty(name = "本店承保数")
    private Integer isInsuranceCount;

    @ApiModelProperty(name = "本店承保维修数")
    private Integer isInsuranceRepairCount;

    @ApiModelProperty(name = "非本店承保数")
    private Integer unInsuranceCount;

    @ApiModelProperty(name = "非店承保维修数")
    private Integer unInsuranceRepairCount;
}
