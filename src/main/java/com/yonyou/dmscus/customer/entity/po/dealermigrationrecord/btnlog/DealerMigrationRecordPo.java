package com.yonyou.dmscus.customer.entity.po.dealermigrationrecord.btnlog;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.BasePo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Objects;

/**
 * bi推送数据记录表
 */
@TableName("tm_dealer_migration_records")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "DealerMigrationRecordPo", description = "经销商迁移表")
public class DealerMigrationRecordPo {

  @ApiModelProperty("原经销商")
  @TableField("original_dealer_code")
  private String originalDealerCode;

  @ApiModelProperty("目标经销商")
  @TableField("target_dealer_code")
  private String targetDealerCode;

  @ApiModelProperty("1:邀约线索；2:续保线索")
  @TableField("migration_type")
  private Integer migrationType;

}
