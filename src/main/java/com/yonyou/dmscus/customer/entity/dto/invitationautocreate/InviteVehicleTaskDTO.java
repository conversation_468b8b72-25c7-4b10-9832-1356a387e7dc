package com.yonyou.dmscus.customer.entity.dto.invitationautocreate;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 车辆邀约任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-07
 */
@Data
public class InviteVehicleTaskDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车牌号
     */
    private String licensePlateNum;

    /**
     * 经销商代码
     */
    private String dealerCode;

    /**
     * 车主名称
     */
    private String name;

    /**
     * 车主电话
     */
    private String tel;

    /**
     * 车主年龄
     */
    private String age;

    /**
     * 车主性别
     */
    private String sex;
    /**
     * 车型
     */
    private String model;

    /**
     * 日均里程
     */
    private Double dailyMileage;

    /**
     * 建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date adviseInDate;

    /**
     * 建议进厂日期更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date adviseInDateUpdateTime;

    /**
     * 邀约类型
     */
    private Integer inviteType;

    /**
     * 提前N天邀约
     */
    private Integer dayInAdvance;

    /**
     * 再提醒间隔（月）
     */
    private Integer remindInterval;

    /**
     * 邀约规则类型(预留)：1 首保、定保、保险、客户流失(对应invite_rule表)，2 易损件、项目(对应invite_rule表)
     */
    private Integer inviteRuleType;

    /**
     * 邀约规则ID(预留)，用于计算再次邀约时间。（关联invite_rule或invite_part_item_rule中id）
     */
    private Long inviteRuleId;

    /**
     * 是否已生成邀约线索：1 是，0 否,2 不再生成
     */
    private Integer isCreateInvite;

    /**
     * 邀约ID （关联invite_vehicle_record中id）
     */
    private Long inviteId;

    /**
     * 生成邀约时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createInviteTime;

    /**
     * 跟进状态
     */
    private Integer followStatus;

    /**
     * 再次邀约时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reinviteTime;

    /**
     * 失效原因
     */
    private String invalidReason;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 易损件上次跟换时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastChangeDate;

    /**
     * 易损件code
     */
    private String itemCode;

    /**
     * 易损件名称
     */
    private String itemName;

    /**
     * 易损件类型
     */
    private Integer itemType;


    /**
     * 任务基准时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inviteTime;


    /**
     *qb号
     */
    private String qbNumber;



    /**
     *建议入厂里程
     */
    private Integer adviseInMileage;

    /**
     * 超时关闭时间间隔（月）
     */
    private Integer closeInterval;


    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
