package com.yonyou.dmscus.customer.entity.po.userCode;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: BiReportUserInfo
 * @projectName server2
 * @description: TODO
 * @date 2022/7/1915:14
 */
@Data
@TableName("tt_user_code_info_import")
public class UserCodeInfoImportPo  extends BasePO<UserCodeInfoImportPo> {


    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 经销商/,集团
     */
    @TableField("belonging")
    private String belonging;

    /**
     * 经销商代码/所属集团
     */
    @TableField("belonging_name")
    private String belongingName;

    /**
     * 姓名
     */
    @TableField("employee_name")
    private String employeeName;


    /**
     * 员工编号
     */
    @TableField("employee_no")
    private String employeeNo;

    /**
     * 岗位
     */
    @TableField("position")
    private String position;


    /**
     *newbie登录账号
     */
    @TableField("user_code")
    private String userCode;


    /**
     *手机号
     */
    @TableField("mobile")
    private String mobile;


    /**
     *邮箱
     */
    @TableField("email")
    private String email;


    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;

    /**
     * 是否导入数据有错误, 1有0否
     */
    @TableField("is_error")
    private Integer isError;
    /**
     * 错误信息
     */
    @TableField("error_msg")
    private String errorMsg;
    /**
     * 行数
     */
    @TableField("line_number")
    private Integer lineNumber;


    //创建时间"
    @TableField("created_at")
    private Date createdAt;

    //创建人"
    @TableField("created_by")
    private String createdBy;

    //更新时间"
    @TableField("updated_at")
    private Date updatedAt;

    //更新人
    @TableField("updated_by")
    private String updatedBy;

    //版本号（乐观锁）
    @TableField("record_version")
    private Integer recordVersion;

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
