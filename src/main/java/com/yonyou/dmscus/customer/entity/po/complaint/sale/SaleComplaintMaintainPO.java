package com.yonyou.dmscus.customer.entity.po.complaint.sale;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 销售客户投诉信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("tt_sale_complaint_maintain" )
public class SaleComplaintMaintainPO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 投诉ID（投诉编号）可 用于和呼叫中心对接
     */
    @TableField("complaint_id" )
    private String complaintId;

    /**
     * 工单状态
     */
    @TableField("work_status" )
    private Integer workStatus;

    /**
     * 工单性质
     */
    @TableField("work_order_nature")
    private  Integer workOrderNature;

    /**
     * 结案状态（投诉单状态）
     */
    @TableField("close_case_status")
    private Integer closeCaseStatus;

    /**
     * 结案时间
     */
    @TableField("close_case_time")
    private Date closeCaseTime;

    /**
     * 是否回访
     */
    @TableField("is_revisit")
    private Integer isRevisit;

    /**
     * '厂端描述'
     */
    @TableField("plant_description")
    private String plantDescription;
    /**
     * ''操作人''
     */
    @TableField("operational_by")
    private String operationalBy;

    /**
     * 操作时间
     */
    @TableField("operational_time")
    private Date operationalTime;

    /**
     * ''操作内容''
     */
    @TableField("operational_context")
    private String operationalContext;

    /**
     * '操作状态'
     */
    @TableField("operational_status")
    private Integer operationalStatus;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 乐观锁
     */
    @TableField("record_version")
    private Integer recordVersion;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

}
