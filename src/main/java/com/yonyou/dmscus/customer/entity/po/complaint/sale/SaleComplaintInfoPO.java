package com.yonyou.dmscus.customer.entity.po.complaint.sale;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import com.yonyou.dmscloud.framework.base.po.BasePO;


import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

import java.util.Date;

/**
 * <p>
 * 销售客户投诉信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-10
 */
@TableName("tt_sale_complaint_info" )
public class SaleComplaintInfoPO extends BasePO<SaleComplaintInfoPO> {

    private static final long serialVersionUID = 1L;

    /**
     * 系统ID
     */
    @TableField("app_id" )
    private String appId;

    /**
     * 所有者代码
     */
    @TableField("owner_code" )
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    @TableField("owner_par_code" )
    private String ownerParCode;

    /**
     * 组织ID
     */
    @TableField("org_id" )
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 投诉ID（投诉编号）可 用于和呼叫中心对接
     */
    @TableField("complaint_id" )
    private String complaintId;

    /**
     * 投诉类型
     */
    @TableField("type" )
    private Integer type;

    /**
     * 投诉分类
     */
    @TableField("classification" )
    private Integer classification;

    /**
     * 投诉来源
     */
    @TableField("source" )
    private Integer source;

    /**
     * 来电时间/投诉日期
     */
    @TableField("call_time" )
    private Date callTime;

    /**
     * 首次重启日期
     */
    @TableField("fisrt_restart_time" )
    private Date fisrtRestartTime;

    /**
     * 最新重启日期
     */
    @TableField("newest_restart_time" )
    private Date newestRestartTime;

    /**
     * 来电客户姓名/投诉人姓名
     */
    @TableField("call_name" )
    private String callName;

    /**
     * 来电电话/投诉人电话
     */
    @TableField("call_tel" )
    private String callTel;

    /**
     * 车主姓名
     */
    @TableField("name" )
    private String name;

    /**
     * 车牌号
     */
    @TableField("license_plate_num" )
    private String licensePlateNum;

    /**
     * 车架号
     */
    @TableField("vin" )
    private String vin;

    /**
     * 购车时间
     */
    @TableField("buy_time" )
    private Date buyTime;

    /**
     * 车型
     */
    @TableField("model" )
    private String model;

    /**
     * 年款
     */
    @TableField("model_year" )
    private String modelYear;

    /**
     * 购买经销商
     */
    @TableField("buy_dealer_name" )
    private String buyDealerName;

    /**
     * 处理经销商
     */
    @TableField("dealer_name" )
    private String dealerName;

    /**
     * 处理经销商代码
     */
    @TableField("dealer_code" )
    private String dealerCode;

    /**
     * 里程
     */
    @TableField("mileage" )
    private Integer mileage;

    /**
     * 回复联系人手机1
     */
    @TableField("reply_tel" )
    private String replyTel;

    /**
     * 回复联系人手机2
     */
    @TableField("reply_tel2" )
    private String replyTel2;

    /**
     * 投诉主题
     */
    @TableField("subject" )
    private String subject;

    /**
     * 细分部位
     */
    @TableField("subdivision_part" )
    private String subdivisionPart;

    /**
     * 部位
     */
    @TableField("part" )
    private String part;

    /**
     * 问题
     */
    @TableField("problem_info" )
    private String ProblemInfo;

    /**
     * 客户要求
     */
    @TableField("cus_requirement" )
    private String cusRequirement;

    /**
     * 问题描述
     */
    @TableField("problem" )
    private String problem;

    /**
     * 坐席主管说明
     */
    @TableField("illustrate" )
    private String illustrate;

    /**
     * 投诉单类别一级层
     */
    @TableField("category1" )
    private String category1;

    /**
     * 投诉单类别二级层
     */
    @TableField("category2" )
    private String category2;

    /**
     * 投诉单类别三级层
     */
    @TableField("category3" )
    private String category3;

    /**
     * CC部位
     */
    @TableField("cc_part" )
    private String ccPart;

    /**
     * CC细分部位
     */
    @TableField("cc_subdivision_part" )
    private String ccSubdivisionPart;

    /**
     * CC问题
     */
    @TableField("cc_problem" )
    private String ccProblem;

    /**
     * CC要求
     */
    @TableField("cc_requirement" )
    private String ccRequirement;

    /**
     * 投诉部门
     */
    @TableField("department" )
    private String department;

    /**
     * 接待员
     */
    @TableField("receptionist" )
    private String receptionist;

    /**
     * 重要等级
     */
    @TableField("importance_level" )
    private Integer importanceLevel;
    /**
     *期望经销商联系时间
     */
    @TableField("hope_reply_time" )
    private  Date hopeReplyTime;

    /**
     * 经销商首次回复时间
     */
    @TableField("dealer_fisrt_reply_time" )
    private Date dealerFisrtReplyTime;

    /**
     * 首次重启经销商首次回复时间
     */
    @TableField("fisrt_restart_dealer_fisrt_reply_time" )
    private Date fisrtRestartDealerFisrtReplyTime;

    /**
     * 是否回访
     */
    @TableField("is_revisit" )
    private Integer isRevisit;

    /**
     * 回访时间
     */
    @TableField("revisit_time" )
    private Date revisitTime;

    /**
     * 回访结果
     */
    @TableField("revisit_result" )
    private String revisitResult;

    /**
     * 回访内容
     */
    @TableField("revisit_content" )
    private String revisitContent;

    /**
     * 经销商申请结案时间
     */
    @TableField("apply_time" )
    private Date applyTime;

    /**
     * 区域经理是否同意
     */
    @TableField("is_agree" )
    private Integer isAgree;

    /**
     * 区域经理提交结案时间
     */
    @TableField("submit_time" )
    private Date submitTime;

    /**
     * 结案时间
     */
    @TableField("close_case_time" )
    private Date closeCaseTime;

    /**
     * 重启结案时间
     */
    @TableField("restart_close_case_time" )
    private Date restartCloseCaseTime;

    /**
     * 工单状态
     */
    @TableField("work_status" )
    private Integer workStatus;

    /**
     * 结案状态（投诉单状态）
     */
    @TableField("close_case_status" )
    private Integer closeCaseStatus;

    /**
     * 跟进状态
     */
    @TableField("follow_status" )
    private Integer followStatus;

    /**
     * 投诉的根本原因 多选用逗号分隔
     */
    @TableField("basic_reason" )
    private String basicReason;

    /**
     * 车辆是否修复
     */
    @TableField("is_repaired" )
    private Integer isRepaired;

    /**
     * 技术与维修方案
     */
    @TableField("tech_maintain_plan" )
    private String techMaintainPlan;

    /**
     * 亲善方案
     */
    @TableField("rapport_plan" )
    private String rapportPlan;

    /**
     * 潜在风险
     */
    @TableField("risk" )
    private String risk;

    /**
     * 进销商是否已读 1 已读 0 未读
     */
    @TableField("dealer_is_read" )
    private Boolean dealerIsRead;

    /**
     * 区域经理是否已读 1 已读 0 未读
     */
    @TableField("manager_is_read" )
    private Boolean managerIsRead;

    /**
     * CCM是否已读 1 已读 0 未读
     */
    @TableField("ccm_is_read" )
    private Boolean ccmIsRead;

    /**
     * 数据来源
     */
    @TableField("data_sources" )
    private Integer dataSources;

    /**
     * 是否上报
     */
    @TableField("is_report" )
    private Boolean isReport;

    /**
     * 是否删除
     */
    @TableField("is_deleted" )
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid" )
    private Integer isValid;

    /**
     * 创建时间
     */
    @TableField("created_at" )
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at" )
    private Date updatedAt;

    /**
     * 投诉人性别
     */
    @TableField("sex" )
    private Integer sex;

    /**
     * 车主地址
     */
    @TableField("owner_address" )
    private String ownerAddress;
    @TableField("work_order_status" )
    private Integer workOrderStatus;
    @TableField("is_close_case" )
    private Integer isCloseCase;

    /**
     * 技术与维修方案
     */
    @TableField("regional_manager_comments" )
    private String regionalManagerComments;

    /**
     * 区域
     */
    @TableField("region" )
    private String region;

    /**
     * 区域经理
     */
    @TableField("region_manager" )
    private String regionManager;

    /**
     * 区域ID
     */
    @TableField("region_id" )
    private Long regionId;

    /**
     * 区域经理ID
     */
    @TableField("region_manager_id" )
    private Long regionManagerId;

    /**
     * 集团
     */
    @TableField("bloc" )
    private String bloc;

    /**
     * 购买经销商
     */
    @TableField("buy_dealer_code" )
    private String buyDealerCode;

    /**
     * 区域(购买)
     */
    @TableField("buy_region" )
    private String buyRegion;

    /**
     * 购买区域经理(购买)
     */
    @TableField("buy_region_manager" )
    private String buyRegionManager;

    /**
     * 区域ID(购买)
     */
    @TableField("buy_region_id" )
    private Long buyRegionId;

    /**
     * 区域经理ID(购买)
     */
    @TableField("buy_region_manager_id" )
    private Long buyRegionManagerId;

    /**
     * 集团(购买)
     */
    @TableField("buy_bloc" )
    private String buyBloc;

    /**
     * 集团id
     */
    @TableField("bloc_id" )
    private Long blocId;

    /**
     * 客户是否满意
     */
    @TableField("is_satisfied" )
    private Integer isSatisfied;

    /**
     * 工单性质
     */
    @TableField("work_order_nature" )
    private Integer workOrderNature;

    /**
     * 工单分类
     */
    @TableField("work_order_classification" )
    private Integer workOrderClassification;

    /**
     * 服务承诺
     */
    @TableField("service_commitment" )
    private Integer serviceCommitment;

    /**
     * 回复联系人姓名
     */
    @TableField("reply_name" )
    private String replyName;

    /**
     * 是否有舆情风险
     */
    @TableField("is_opinion" )
    private  Integer isOpinion;
    /**
     *区域经理是否同意结案
     */
    @TableField("is_agree_region" )
    private Integer isAgreeRegion;
    /**
     * 结案备注
     */
    @TableField("close_case_remark" )
    private  String closeCaseRemark;

    /**
     * 区域经理意见
     */
    @TableField("region_comments" )
    private  String regionComments;
    /**
     *区域经理审核时间
     */
    @TableField("region_audit_time" )
    private  Date regionAuditTime;

    /**
     *总部是否同意结案
     */
    @TableField("is_agree_headquarters" )
    private Integer isAgreeHeadquarters;

    /**
     * 总部意见
     */
    @TableField("headquarters_comments" )
    private  String headquartersComments;

    /**
     * 提交重启时间
     */
    @TableField("restart_reply_time" )
    private  Date restartReplyTime;

    /**
     *是否匿名
     */
    @TableField("is_anonymous" )
    private Integer isAnonymous;

    /**
     *区域经理是否满意结案（低满意度）
     */
    @TableField("region_satisfied_case")
    private Integer regionSatisfiedCase;

    /**
     *总部是否满意结案（低满意度）
     */
    @TableField("HQ_satisfied_case")
    private Integer HQSatisfiedCase;

    public SaleComplaintInfoPO() {
        super();
    }

    public Integer getHQSatisfiedCase() {
        return HQSatisfiedCase;
    }

    public void setHQSatisfiedCase(Integer HQSatisfiedCase) {
        this.HQSatisfiedCase = HQSatisfiedCase;
    }

    public Integer getRegionSatisfiedCase() {
        return regionSatisfiedCase;
    }

    public void setRegionSatisfiedCase(Integer regionSatisfiedCase) {
        this.regionSatisfiedCase = regionSatisfiedCase;
    }

    public Integer getIsAnonymous() {
        return isAnonymous;
    }

    public void setIsAnonymous(Integer isAnonymous) {
        this.isAnonymous = isAnonymous;
    }

    public Date getRestartReplyTime() {
        return restartReplyTime;
    }

    public void setRestartReplyTime(Date restartReplyTime) {
        this.restartReplyTime = restartReplyTime;
    }

    public Integer getIsAgreeHeadquarters() {
        return isAgreeHeadquarters;
    }

    public void setIsAgreeHeadquarters(Integer isAgreeHeadquarters) {
        this.isAgreeHeadquarters = isAgreeHeadquarters;
    }

    public String getHeadquartersComments() {
        return headquartersComments;
    }

    public void setHeadquartersComments(String headquartersComments) {
        this.headquartersComments = headquartersComments;
    }

    public Date getRegionAuditTime() {
        return regionAuditTime;
    }

    public void setRegionAuditTime(Date regionAuditTime) {
        this.regionAuditTime = regionAuditTime;
    }

    public String getRegionComments() {
        return regionComments;
    }

    public void setRegionComments(String regionComments) {
        this.regionComments = regionComments;
    }

    public String getCloseCaseRemark() {
        return closeCaseRemark;
    }

    public void setCloseCaseRemark(String closeCaseRemark) {
        this.closeCaseRemark = closeCaseRemark;
    }

    public Integer getIsAgreeRegion() {
        return isAgreeRegion;
    }

    public void setIsAgreeRegion(Integer isAgreeRegion) {
        this.isAgreeRegion = isAgreeRegion;
    }

    public Integer getIsOpinion() {
        return isOpinion;
    }

    public void setIsOpinion(Integer isOpinion) {
        this.isOpinion = isOpinion;
    }

    public Date getHopeReplyTime() {
        return hopeReplyTime;
    }

    public void setHopeReplyTime(Date hopeReplyTime) {
        this.hopeReplyTime = hopeReplyTime;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerParCode() {
        return ownerParCode;
    }

    public void setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getComplaintId() {
        return complaintId;
    }

    public void setComplaintId(String complaintId) {
        this.complaintId = complaintId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getProblemInfo() {
        return ProblemInfo;
    }

    public Boolean getReport() {
        return isReport;
    }

    public void setReport(Boolean report) {
        isReport = report;
    }

    public void setProblemInfo(String problemInfo) {
        ProblemInfo = problemInfo;
    }

    public String getCusRequirement() {
        return cusRequirement;
    }

    public void setCusRequirement(String cusRequirement) {
        this.cusRequirement = cusRequirement;
    }

    public Integer getClassification() {
        return classification;
    }

    public void setClassification(Integer classification) {
        this.classification = classification;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Date getCallTime() {
        return callTime;
    }

    public void setCallTime(Date callTime) {
        this.callTime = callTime;
    }

    public Date getFisrtRestartTime() {
        return fisrtRestartTime;
    }

    public void setFisrtRestartTime(Date fisrtRestartTime) {
        this.fisrtRestartTime = fisrtRestartTime;
    }

    public Date getNewestRestartTime() {
        return newestRestartTime;
    }

    public void setNewestRestartTime(Date newestRestartTime) {
        this.newestRestartTime = newestRestartTime;
    }

    public String getCallName() {
        return callName;
    }

    public void setCallName(String callName) {
        this.callName = callName;
    }

    public String getCallTel() {
        return callTel;
    }

    public void setCallTel(String callTel) {
        this.callTel = callTel;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLicensePlateNum() {
        return licensePlateNum;
    }

    public void setLicensePlateNum(String licensePlateNum) {
        this.licensePlateNum = licensePlateNum;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public Date getBuyTime() {
        return buyTime;
    }

    public void setBuyTime(Date buyTime) {
        this.buyTime = buyTime;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getModelYear() {
        return modelYear;
    }

    public void setModelYear(String modelYear) {
        this.modelYear = modelYear;
    }

    public String getBuyDealerName() {
        return buyDealerName;
    }

    public void setBuyDealerName(String buyDealerName) {
        this.buyDealerName = buyDealerName;
    }

    public String getDealerName() {
        return dealerName;
    }

    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    public String getDealerCode() {
        return dealerCode;
    }

    public void setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
    }

    public Integer getMileage() {
        return mileage;
    }

    public void setMileage(Integer mileage) {
        this.mileage = mileage;
    }

    public String getReplyTel() {
        return replyTel;
    }

    public void setReplyTel(String replyTel) {
        this.replyTel = replyTel;
    }

    public String getReplyTel2() {
        return replyTel2;
    }

    public void setReplyTel2(String replyTel2) {
        this.replyTel2 = replyTel2;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getSubdivisionPart() {
        return subdivisionPart;
    }

    public void setSubdivisionPart(String subdivisionPart) {
        this.subdivisionPart = subdivisionPart;
    }

    public String getPart() {
        return part;
    }

    public void setPart(String part) {
        this.part = part;
    }

    public String getProblem() {
        return problem;
    }

    public void setProblem(String problem) {
        this.problem = problem;
    }

    public String getIllustrate() {
        return illustrate;
    }

    public void setIllustrate(String illustrate) {
        this.illustrate = illustrate;
    }

    public String getCategory1() {
        return category1;
    }

    public void setCategory1(String category1) {
        this.category1 = category1;
    }

    public String getCategory2() {
        return category2;
    }

    public void setCategory2(String category2) {
        this.category2 = category2;
    }

    public String getCategory3() {
        return category3;
    }

    public void setCategory3(String category3) {
        this.category3 = category3;
    }

    public String getCcPart() {
        return ccPart;
    }

    public void setCcPart(String ccPart) {
        this.ccPart = ccPart;
    }

    public String getCcSubdivisionPart() {
        return ccSubdivisionPart;
    }

    public void setCcSubdivisionPart(String ccSubdivisionPart) {
        this.ccSubdivisionPart = ccSubdivisionPart;
    }

    public String getCcProblem() {
        return ccProblem;
    }

    public void setCcProblem(String ccProblem) {
        this.ccProblem = ccProblem;
    }

    public String getCcRequirement() {
        return ccRequirement;
    }

    public void setCcRequirement(String ccRequirement) {
        this.ccRequirement = ccRequirement;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getReceptionist() {
        return receptionist;
    }

    public void setReceptionist(String receptionist) {
        this.receptionist = receptionist;
    }

    public Integer getImportanceLevel() {
        return importanceLevel;
    }

    public void setImportanceLevel(Integer importanceLevel) {
        this.importanceLevel = importanceLevel;
    }

    public Date getDealerFisrtReplyTime() {
        return dealerFisrtReplyTime;
    }

    public void setDealerFisrtReplyTime(Date dealerFisrtReplyTime) {
        this.dealerFisrtReplyTime = dealerFisrtReplyTime;
    }

    public Date getFisrtRestartDealerFisrtReplyTime() {
        return fisrtRestartDealerFisrtReplyTime;
    }

    public void setFisrtRestartDealerFisrtReplyTime(Date fisrtRestartDealerFisrtReplyTime) {
        this.fisrtRestartDealerFisrtReplyTime = fisrtRestartDealerFisrtReplyTime;
    }

    public Integer getIsRevisit() {
        return isRevisit;
    }

    public void setIsRevisit(Integer isRevisit) {
        this.isRevisit = isRevisit;
    }

    public Date getRevisitTime() {
        return revisitTime;
    }

    public void setRevisitTime(Date revisitTime) {
        this.revisitTime = revisitTime;
    }

    public String getRevisitResult() {
        return revisitResult;
    }

    public void setRevisitResult(String revisitResult) {
        this.revisitResult = revisitResult;
    }

    public String getRevisitContent() {
        return revisitContent;
    }

    public void setRevisitContent(String revisitContent) {
        this.revisitContent = revisitContent;
    }

    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public Integer getIsAgree() {
        return isAgree;
    }

    public void setIsAgree(Integer isAgree) {
        this.isAgree = isAgree;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public Date getCloseCaseTime() {
        return closeCaseTime;
    }

    public void setCloseCaseTime(Date closeCaseTime) {
        this.closeCaseTime = closeCaseTime;
    }

    public Date getRestartCloseCaseTime() {
        return restartCloseCaseTime;
    }

    public void setRestartCloseCaseTime(Date restartCloseCaseTime) {
        this.restartCloseCaseTime = restartCloseCaseTime;
    }

    public Integer getWorkStatus() {
        return workStatus;
    }

    public void setWorkStatus(Integer workStatus) {
        this.workStatus = workStatus;
    }

    public Integer getCloseCaseStatus() {
        return closeCaseStatus;
    }

    public void setCloseCaseStatus(Integer closeCaseStatus) {
        this.closeCaseStatus = closeCaseStatus;
    }

    public Integer getFollowStatus() {
        return followStatus;
    }

    public void setFollowStatus(Integer followStatus) {
        this.followStatus = followStatus;
    }

    public String getBasicReason() {
        return basicReason;
    }

    public void setBasicReason(String basicReason) {
        this.basicReason = basicReason;
    }

    public Integer getIsRepaired() {
        return isRepaired;
    }

    public void setIsRepaired(Integer isRepaired) {
        this.isRepaired = isRepaired;
    }

    public String getTechMaintainPlan() {
        return techMaintainPlan;
    }

    public void setTechMaintainPlan(String techMaintainPlan) {
        this.techMaintainPlan = techMaintainPlan;
    }

    public String getRapportPlan() {
        return rapportPlan;
    }

    public void setRapportPlan(String rapportPlan) {
        this.rapportPlan = rapportPlan;
    }

    public String getRisk() {
        return risk;
    }

    public void setRisk(String risk) {
        this.risk = risk;
    }

    public Boolean getIsDealerIsRead() {
        return dealerIsRead;
    }

    public void setIsDealerIsRead(Boolean dealerIsRead) {
        this.dealerIsRead = dealerIsRead;
    }

    public Boolean getIsManagerIsRead() {
        return managerIsRead;
    }

    public void setIsManagerIsRead(Boolean managerIsRead) {
        this.managerIsRead = managerIsRead;
    }

    public Boolean getIsCcmIsRead() {
        return ccmIsRead;
    }

    public void setIsCcmIsRead(Boolean ccmIsRead) {
        this.ccmIsRead = ccmIsRead;
    }

    public Integer getDataSources() {
        return dataSources;
    }

    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsReport() {
        return isReport;
    }

    public void setIsReport(Boolean isReport) {
        this.isReport = isReport;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getOwnerAddress() {
        return ownerAddress;
    }

    public void setOwnerAddress(String ownerAddress) {
        this.ownerAddress = ownerAddress;
    }

    public Integer getWorkOrderStatus() {
        return workOrderStatus;
    }

    public void setWorkOrderStatus(Integer workOrderStatus) {
        this.workOrderStatus = workOrderStatus;
    }

    public Integer getIsCloseCase() {
        return isCloseCase;
    }

    public void setIsCloseCase(Integer isCloseCase) {
        this.isCloseCase = isCloseCase;
    }

    public String getRegionalManagerComments() {
        return regionalManagerComments;
    }

    public void setRegionalManagerComments(String regionalManagerComments) {
        this.regionalManagerComments = regionalManagerComments;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getRegionManager() {
        return regionManager;
    }

    public void setRegionManager(String regionManager) {
        this.regionManager = regionManager;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public Long getRegionManagerId() {
        return regionManagerId;
    }

    public void setRegionManagerId(Long regionManagerId) {
        this.regionManagerId = regionManagerId;
    }

    public String getBloc() {
        return bloc;
    }

    public void setBloc(String bloc) {
        this.bloc = bloc;
    }

    public String getBuyDealerCode() {
        return buyDealerCode;
    }

    public void setBuyDealerCode(String buyDealerCode) {
        this.buyDealerCode = buyDealerCode;
    }

    public String getBuyRegion() {
        return buyRegion;
    }

    public void setBuyRegion(String buyRegion) {
        this.buyRegion = buyRegion;
    }

    public String getBuyRegionManager() {
        return buyRegionManager;
    }

    public void setBuyRegionManager(String buyRegionManager) {
        this.buyRegionManager = buyRegionManager;
    }

    public Long getBuyRegionId() {
        return buyRegionId;
    }

    public void setBuyRegionId(Long buyRegionId) {
        this.buyRegionId = buyRegionId;
    }

    public Long getBuyRegionManagerId() {
        return buyRegionManagerId;
    }

    public void setBuyRegionManagerId(Long buyRegionManagerId) {
        this.buyRegionManagerId = buyRegionManagerId;
    }

    public String getBuyBloc() {
        return buyBloc;
    }

    public void setBuyBloc(String buyBloc) {
        this.buyBloc = buyBloc;
    }

    public Long getBlocId() {
        return blocId;
    }

    public void setBlocId(Long blocId) {
        this.blocId = blocId;
    }

    public Integer getIsSatisfied() {
        return isSatisfied;
    }

    public void setIsSatisfied(Integer isSatisfied) {
        this.isSatisfied = isSatisfied;
    }

    public Integer getWorkOrderNature() {
        return workOrderNature;
    }

    public void setWorkOrderNature(Integer workOrderNature) {
        this.workOrderNature = workOrderNature;
    }

    public Integer getWorkOrderClassification() {
        return workOrderClassification;
    }

    public void setWorkOrderClassification(Integer workOrderClassification) {
        this.workOrderClassification = workOrderClassification;
    }

    public Integer getServiceCommitment() {
        return serviceCommitment;
    }

    public void setServiceCommitment(Integer serviceCommitment) {
        this.serviceCommitment = serviceCommitment;
    }

    public String getReplyName() {
        return replyName;
    }

    public void setReplyName(String replyName) {
        this.replyName = replyName;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "SaleComplaintInfoPO{" +
                "appId=" + appId +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", complaintId=" + complaintId +
                ", type=" + type +
                ", classification=" + classification +
                ", source=" + source +
                ", callTime=" + callTime +
                ", fisrtRestartTime=" + fisrtRestartTime +
                ", newestRestartTime=" + newestRestartTime +
                ", callName=" + callName +
                ", callTel=" + callTel +
                ", name=" + name +
                ", licensePlateNum=" + licensePlateNum +
                ", vin=" + vin +
                ", buyTime=" + buyTime +
                ", model=" + model +
                ", modelYear=" + modelYear +
                ", buyDealerName=" + buyDealerName +
                ", dealerName=" + dealerName +
                ", dealerCode=" + dealerCode +
                ", mileage=" + mileage +
                ", replyTel=" + replyTel +
                ", replyTel2=" + replyTel2 +
                ", subject=" + subject +
                ", subdivisionPart=" + subdivisionPart +
                ", part=" + part +
                ", problem=" + problem +
                ", illustrate=" + illustrate +
                ", category1=" + category1 +
                ", category2=" + category2 +
                ", category3=" + category3 +
                ", ccPart=" + ccPart +
                ", ccSubdivisionPart=" + ccSubdivisionPart +
                ", ccProblem=" + ccProblem +
                ", ccRequirement=" + ccRequirement +
                ", department=" + department +
                ", receptionist=" + receptionist +
                ", importanceLevel=" + importanceLevel +
                ", dealerFisrtReplyTime=" + dealerFisrtReplyTime +
                ", fisrtRestartDealerFisrtReplyTime=" + fisrtRestartDealerFisrtReplyTime +
                ", isRevisit=" + isRevisit +
                ", revisitTime=" + revisitTime +
                ", revisitResult=" + revisitResult +
                ", revisitContent=" + revisitContent +
                ", applyTime=" + applyTime +
                ", isAgree=" + isAgree +
                ", submitTime=" + submitTime +
                ", closeCaseTime=" + closeCaseTime +
                ", restartCloseCaseTime=" + restartCloseCaseTime +
                ", workStatus=" + workStatus +
                ", closeCaseStatus=" + closeCaseStatus +
                ", followStatus=" + followStatus +
                ", basicReason=" + basicReason +
                ", isRepaired=" + isRepaired +
                ", techMaintainPlan=" + techMaintainPlan +
                ", rapportPlan=" + rapportPlan +
                ", risk=" + risk +
                ", dealerIsRead=" + dealerIsRead +
                ", managerIsRead=" + managerIsRead +
                ", ccmIsRead=" + ccmIsRead +
                ", dataSources=" + dataSources +
                ", isReport=" + isReport +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", sex=" + sex +
                ", ownerAddress=" + ownerAddress +
                ", workOrderStatus=" + workOrderStatus +
                ", isCloseCase=" + isCloseCase +
                ", regionalManagerComments=" + regionalManagerComments +
                ", region=" + region +
                ", regionManager=" + regionManager +
                ", regionId=" + regionId +
                ", regionManagerId=" + regionManagerId +
                ", bloc=" + bloc +
                ", buyDealerCode=" + buyDealerCode +
                ", buyRegion=" + buyRegion +
                ", buyRegionManager=" + buyRegionManager +
                ", buyRegionId=" + buyRegionId +
                ", buyRegionManagerId=" + buyRegionManagerId +
                ", buyBloc=" + buyBloc +
                ", blocId=" + blocId +
                ", isSatisfied=" + isSatisfied +
                ", workOrderNature=" + workOrderNature +
                ", workOrderClassification=" + workOrderClassification +
                ", serviceCommitment=" + serviceCommitment +
                ", replyName=" + replyName +
                "}";
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id" );
    }

}
