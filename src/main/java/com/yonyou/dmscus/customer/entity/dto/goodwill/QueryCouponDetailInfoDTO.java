package com.yonyou.dmscus.customer.entity.dto.goodwill;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 卡券基础数据到中台 dto
 */
@ApiModel(value = "QueryCouponDetailInfoDTO ", description = "卡券表模版")
public class QueryCouponDetailInfoDTO {
	@ApiModelProperty(value = "卡券代码")
	private String couponCode;
	@ApiModelProperty(value = "卡券名称")
	private String couponName;
	@ApiModelProperty(value = "卡券id")
	private Long couponId;
	@ApiModelProperty(value = "卡券id数组")
	private long[] couponIds;
	@ApiModelProperty(value = "oneid")
	private Long oneId;
	@ApiModelProperty(value = "卡券面额")
	private BigDecimal couponValue;
	@ApiModelProperty(value = "是否亲善券(0:否 1:是)")
	private Integer kindness;
	@ApiModelProperty(value = "卡券余额")
	private BigDecimal leftValue;
	@ApiModelProperty(value = "激活时间（生效日期）")
	private String activateDate;
	@ApiModelProperty(value = "激活状态（已激活:31071001 未激活:31071002）")
	private Integer activeState;
	@ApiModelProperty(value = "活动编号")
	private String activityCode;
	@ApiModelProperty(value = "活动名称")
	private String activityName;
	@ApiModelProperty(value = "卡券来源（83241001 VCDC发券 83241002 沃世界领券 83241003 商城购买）")
	private Integer couponSource;
	@ApiModelProperty(value = "兑换码 核销码")
	private String exchangeCode;
	@ApiModelProperty(value = "卡券失效日期")
	private String expirationDate;
	@ApiModelProperty(value = "领取日期")
	private String getDate;
	@ApiModelProperty(value = "领用id")
	private Integer id;
	@ApiModelProperty(value = "是否订单退券")
	private Integer isRefunded;
	@ApiModelProperty(value = "发券人id")
	private Integer issuerId;
	@ApiModelProperty(value = "最后使用时间")
	private String lastUseDate;
	@ApiModelProperty(value = "锁定单据号")
	private String lockOrderNo;
	@ApiModelProperty(value = "单据类型")
	private Integer orderType;
	@ApiModelProperty(value = "二维码")
	private String qrCodeUrl;
	@ApiModelProperty(value = "来源编号")
	private Integer sourceNumber;
	@ApiModelProperty(value = "卡券状态(31061001:已领用, 31061002:已锁定, 31061003:已核销, 31061004:已过期, 31061005:已作废,)")
	private Integer ticketState;
	@ApiModelProperty(value = "vin")
	private String vin;

	public BigDecimal getLeftValue() {
		return leftValue;
	}

	public void setLeftValue(BigDecimal leftValue) {
		this.leftValue = leftValue;
	}

	public String getCouponCode() {
		return couponCode;
	}

	public void setCouponCode(String couponCode) {
		this.couponCode = couponCode;
	}

	public String getCouponName() {
		return couponName;
	}

	public void setCouponName(String couponName) {
		this.couponName = couponName;
	}

	public Long getCouponId() {
		return couponId;
	}

	public void setCouponId(Long couponId) {
		this.couponId = couponId;
	}

	public long[] getCouponIds() {
		return couponIds;
	}

	public void setCouponIds(long[] couponIds) {
		this.couponIds = couponIds;
	}

	public Long getOneId() {
		return oneId;
	}

	public void setOneId(Long oneId) {
		this.oneId = oneId;
	}

	public BigDecimal getCouponValue() {
		return couponValue;
	}

	public void setCouponValue(BigDecimal couponValue) {
		this.couponValue = couponValue;
	}

	public Integer getKindness() {
		return kindness;
	}

	public void setKindness(Integer kindness) {
		this.kindness = kindness;
	}

	public String getActivateDate() {
		return activateDate;
	}

	public void setActivateDate(String activateDate) {
		this.activateDate = activateDate;
	}

	public Integer getActiveState() {
		return activeState;
	}

	public void setActiveState(Integer activeState) {
		this.activeState = activeState;
	}

	public String getActivityCode() {
		return activityCode;
	}

	public void setActivityCode(String activityCode) {
		this.activityCode = activityCode;
	}

	public String getActivityName() {
		return activityName;
	}

	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}

	public Integer getCouponSource() {
		return couponSource;
	}

	public void setCouponSource(Integer couponSource) {
		this.couponSource = couponSource;
	}

	public String getExchangeCode() {
		return exchangeCode;
	}

	public void setExchangeCode(String exchangeCode) {
		this.exchangeCode = exchangeCode;
	}

	public String getExpirationDate() {
		return expirationDate;
	}

	public void setExpirationDate(String expirationDate) {
		this.expirationDate = expirationDate;
	}

	public String getGetDate() {
		return getDate;
	}

	public void setGetDate(String getDate) {
		this.getDate = getDate;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getIsRefunded() {
		return isRefunded;
	}

	public void setIsRefunded(Integer isRefunded) {
		this.isRefunded = isRefunded;
	}

	public Integer getIssuerId() {
		return issuerId;
	}

	public void setIssuerId(Integer issuerId) {
		this.issuerId = issuerId;
	}

	public String getLastUseDate() {
		return lastUseDate;
	}

	public void setLastUseDate(String lastUseDate) {
		this.lastUseDate = lastUseDate;
	}

	public String getLockOrderNo() {
		return lockOrderNo;
	}

	public void setLockOrderNo(String lockOrderNo) {
		this.lockOrderNo = lockOrderNo;
	}

	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	public String getQrCodeUrl() {
		return qrCodeUrl;
	}

	public void setQrCodeUrl(String qrCodeUrl) {
		this.qrCodeUrl = qrCodeUrl;
	}

	public Integer getSourceNumber() {
		return sourceNumber;
	}

	public void setSourceNumber(Integer sourceNumber) {
		this.sourceNumber = sourceNumber;
	}

	public Integer getTicketState() {
		return ticketState;
	}

	public void setTicketState(Integer ticketState) {
		this.ticketState = ticketState;
	}

	public String getVin() {
		return vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

}
