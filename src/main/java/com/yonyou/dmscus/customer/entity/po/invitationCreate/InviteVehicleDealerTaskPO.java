package com.yonyou.dmscus.customer.entity.po.invitationCreate;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 车辆特约店自建邀约任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-29
 */
@TableName("tt_invite_vehicle_dealer_task")
public class InviteVehicleDealerTaskPO extends BasePO<InviteVehicleDealerTaskPO> {

    private static final long serialVersionUID = 1L;


    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;


    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 进销商代码
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 计划编号
     */
    @TableField("plan_no")
    private String planNo;

    /**
     * 邀约类型：服务活动、召回、保修、延保、其他
     */
    @TableField("invite_type")
    private Integer inviteType;

    /**
     * 邀约名称
     */
    @TableField("invite_name")
    private String inviteName;

    /**
     * 跟进方式：电话、短信、微信、问卷、其他
     */
    @TableField("follow_mode")
    private Integer followMode;

    /**
     * 日均里程
     */
    @TableField("daily_mileage")
    private Integer dailyMileage;

    /**
     * 计划提醒日期
     */
    @TableField("plan_remind_date")
    private Date planRemindDate;

    /**
     * 建议进厂日期
     */
    @TableField("advise_in_date")
    private Date adviseInDate;

    /**
     * 是否已生成邀约线索（是否下发）：1 是，0 否
     */
    @TableField("is_create_invite")
    private Integer isCreateInvite;

    /**
     * 邀约ID （关联invite_vehicle_record中id）
     */
    @TableField("invite_id")
    private Long inviteId;

    /**
     * 分配人员
     */
    @TableField("distribution_person")
    private String distributionPerson;

    /**
     * 分配日期
     */
    @TableField("distribution_date")
    private Date distributionDate;

    /**
     * 跟进内容
     */
    @TableField("follow_content")
    private String followContent;

    /**
     * 跟进人员 可指定，如没有指定按照特约店维护的SA分配规则表分配跟进人员
     */
    @TableField("follow_sa_id")
    private String followSaId;

    /**
     * 服务活动,存放服务活动id
     */
    @TableField("service_activity")
    private Long serviceActivity;

    /**
     * 跟进状态
     */
    @TableField("follow_status")
    private Integer followStatus;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;




    /**
     * 创建人姓名
     */
    @TableField("created_name")
    private  String createdName;


    /**
     * 创建时间开始 查询条件
     */
    @TableField(exist = false)
    private String createdAtStart;


    /**
     * 创建时间结束 查询条件
     */
    @TableField(exist = false)
    private String createdAtEnd;



    public InviteVehicleDealerTaskPO() {
        super();
    }


    public String getCreatedAtStart() {
        return createdAtStart;
    }

    public void setCreatedAtStart(String createdAtStart) {
        this.createdAtStart = createdAtStart;
    }

    public String getCreatedAtEnd() {
        return createdAtEnd;
    }

    public void setCreatedAtEnd(String createdAtEnd) {
        this.createdAtEnd = createdAtEnd;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Date getPlanRemindDate() {
        return planRemindDate;
    }

    public void setPlanRemindDate(Date planRemindDate) {
        this.planRemindDate = planRemindDate;
    }

    public Date getAdviseInDate() {
        return adviseInDate;
    }

    public void setAdviseInDate(Date adviseInDate) {
        this.adviseInDate = adviseInDate;
    }

    public Date getDistributionDate() {
        return distributionDate;
    }

    public void setDistributionDate(Date distributionDate) {
        this.distributionDate = distributionDate;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }


    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDealerCode() {
        return dealerCode;
    }

    public void setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
    }

    public String getPlanNo() {
        return planNo;
    }

    public void setPlanNo(String planNo) {
        this.planNo = planNo;
    }

    public Integer getInviteType() {
        return inviteType;
    }

    public void setInviteType(Integer inviteType) {
        this.inviteType = inviteType;
    }

    public String getInviteName() {
        return inviteName;
    }

    public void setInviteName(String inviteName) {
        this.inviteName = inviteName;
    }

    public Integer getFollowMode() {
        return followMode;
    }

    public void setFollowMode(Integer followMode) {
        this.followMode = followMode;
    }

    public Integer getDailyMileage() {
        return dailyMileage;
    }

    public void setDailyMileage(Integer dailyMileage) {
        this.dailyMileage = dailyMileage;
    }


    public Integer getIsCreateInvite() {
        return isCreateInvite;
    }

    public void setIsCreateInvite(Integer isCreateInvite) {
        this.isCreateInvite = isCreateInvite;
    }

    public Long getInviteId() {
        return inviteId;
    }

    public void setInviteId(Long inviteId) {
        this.inviteId = inviteId;
    }

    public String getDistributionPerson() {
        return distributionPerson;
    }

    public void setDistributionPerson(String distributionPerson) {
        this.distributionPerson = distributionPerson;
    }


    public String getFollowContent() {
        return followContent;
    }

    public void setFollowContent(String followContent) {
        this.followContent = followContent;
    }

    public String getFollowSaId() {
        return followSaId;
    }

    public void setFollowSaId(String followSaId) {
        this.followSaId = followSaId;
    }

    public Long getServiceActivity() {
        return serviceActivity;
    }

    public void setServiceActivity(Long serviceActivity) {
        this.serviceActivity = serviceActivity;
    }

    public Integer getFollowStatus() {
        return followStatus;
    }

    public void setFollowStatus(Integer followStatus) {
        this.followStatus = followStatus;
    }

    public Integer getDataSources() {
        return dataSources;
    }

    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "InviteVehicleDealerTaskPO{" +
                ", ownerCode=" + ownerCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", dealerCode=" + dealerCode +
                ", planNo=" + planNo +
                ", inviteType=" + inviteType +
                ", inviteName=" + inviteName +
                ", followMode=" + followMode +
                ", dailyMileage=" + dailyMileage +
                ", planRemindDate=" + planRemindDate +
                ", adviseInDate=" + adviseInDate +
                ", isCreateInvite=" + isCreateInvite +
                ", inviteId=" + inviteId +
                ", distributionPerson=" + distributionPerson +
                ", distributionDate=" + distributionDate +
                ", followContent=" + followContent +
                ", followSaId=" + followSaId +
                ", serviceActivity=" + serviceActivity +
                ", followStatus=" + followStatus +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                "}";
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
