package com.yonyou.dmscus.customer.entity.po.complaint;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import lombok.Data;

import java.util.Date;

/**
 * 节假日日期
 */
@TableName("tm_holiday")
@Data
public class HolidayPO extends BasePO<HolidayPO> {
    /**
     * 系统ID
     */
    @TableField("id")
    private String Id;

    /**
     * 年
     */
    @TableField("year")
    private String year;

    /**
     * 月
     */
    @TableField("month")
    private String month;

    /**
     * 时间
     */
    @TableField("holiday_date")
    private Date holidayDate;

}
