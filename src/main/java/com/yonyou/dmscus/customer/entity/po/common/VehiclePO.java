package com.yonyou.dmscus.customer.entity.po.common;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import java.math.BigDecimal;

import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;

import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 车辆资料
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-08
 */
@Data
@TableName("tm_vehicle")
public class VehiclePO extends BasePO<VehiclePO> {

    private static final long serialVersionUID = 1L;


    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;


    /**
     * 组织id
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * vin
     */
    @TableField("vin")
    private String vin;

    /**
     * 车主编号
     */
    @TableField("owner_no")
    private String ownerNo;

    /**
     * 客户编号
     */
    @TableField("customer_no")
    private String customerNo;

    /**
     * 原车主编号
     */
    @TableField("owner_no_old")
    private String ownerNoOld;

    /**
     * 车牌号
     */
    @TableField("license")
    private String license;

    /**
     * 发动机号
     */
    @TableField("engine_no")
    private String engineNo;

    /**
     * 变速箱箱号
     */
    @TableField("gear_box")
    private String gearBox;

    /**
     * 出厂日期
     */
    @TableField("factory_date")
    private Date factoryDate;

    /**
     * 建档日期
     */
    @TableField("found_date")
    private Date foundDate;

    /**
     * 内饰颜色
     */
    @TableField("inner_color")
    private String innerColor;

    /**
     * 内饰
     */
    @TableField("innerId")
    private String innerId;

    /**
     * 厂牌(品牌)
     */
    @TableField("brand")
    private String brand;

    /**
     * 车系
     */
    @TableField("series")
    private String series;

    /**
     * 车型
     */
    @TableField("model")
    private String model;

    /**
     * 颜色
     */
    @TableField("color")
    private String color;

    /**
     * 配置（车款）
     */
    @TableField("apackage")
    private String apackage;

    /**
     * 车型年款
     */
    @TableField("model_year")
    private String modelYear;

    /**
     * 排气量
     */
    @TableField("exhaust_quantity")
    private String exhaustQuantity;

    /**
     * 制造日期
     */
    @TableField("product_date")
    private Date productDate;

    /**
     * 排挡类别
     */
    @TableField("shift_type")
    private String shiftType;

    /**
     * 燃料类别
     */
    @TableField("fuel_type")
    private Integer fuelType;

    /**
     * 车辆用途
     */
    @TableField("vehicle_purpose")
    private Integer vehiclePurpose;

    /**
     * 营运性质
     */
    @TableField("business_kind")
    private Integer businessKind;

    /**
     * 营运证日期
     */
    @TableField("business_date")
    private Date businessDate;

    /**
     * 旧发动机号
     */
    @TableField("engine_no_old")
    private String engineNoOld;

    /**
     * 上次维修经销商
     */
    @TableField("last_repair_dealer")
    private String lastRepairDealer;

    /**
     * 更换发动机事项说明
     */
    @TableField("change_engine_desc")
    private String changeEngineDesc;

    /**
     * 销售经销商
     */
    @TableField("sales_agent_name")
    private String salesAgentName;

    /**
     * 销售顾问
     */
    @TableField("consultant")
    private String consultant;

    /**
     * 是否允许邀约
     */
    @TableField("is_allow_invitation")
    private Integer isAllowInvitation;

    /**
     * 是否本公司购车
     */
    @TableField("is_self_company")
    private Integer isSelfCompany;

    /**
     * 销售日期
     */
    @TableField("sales_date")
    private Date salesDate;

    /**
     * 销售里程
     */
    @TableField("sales_mileage")
    private BigDecimal salesMileage;

    /**
     * 车辆价格
     */
    @TableField("vehicle_price")
    private BigDecimal vehiclePrice;

    /**
     * 保修起始日期
     */
    @TableField("wrt_begin_date")
    private Date wrtBeginDate;

    /**
     * 保修结束日期
     */
    @TableField("wrt_end_date")
    private Date wrtEndDate;

    /**
     * 保修起始里程
     */
    @TableField("wrt_begin_mileage")
    private BigDecimal wrtBeginMileage;

    /**
     * 保修结束里程
     */
    @TableField("wrt_end_mileage")
    private BigDecimal wrtEndMileage;

    /**
     * 上牌日期
     */
    @TableField("license_date")
    private Date licenseDate;

    /**
     * 行驶里程
     */
    @TableField("mileage")
    private BigDecimal mileage;

    /**
     * 是否换表
     */
    @TableField("is_change_odograph")
    private Integer isChangeOdograph;

    /**
     * 累计换表里程
     */
    @TableField("total_change_mileage")
    private BigDecimal totalChangeMileage;

    /**
     * 上次换表日期
     */
    @TableField("change_date")
    private Date changeDate;

    /**
     * 加装说明
     */
    @TableField("add_equipment")
    private String addEquipment;

    /**
     * 首次进厂日期
     */
    @TableField("first_in_date")
    private Date firstInDate;

    /**
     * 预计下次保养日期
     */
    @TableField("next_maintain_date")
    private Date nextMaintainDate;

    /**
     * 下次保养里程
     */
    @TableField("next_maintain_mileage")
    private BigDecimal nextMaintainMileage;

    /**
     * 日平均行驶里程
     */
    @TableField("daily_average_mileage")
    private BigDecimal dailyAverageMileage;

    /**
     * 上次验车日期
     */
    @TableField("last_inspect_date")
    private Date lastInspectDate;

    /**
     * 下次验车日期
     */
    @TableField("next_inspect_date")
    private Date nextInspectDate;

    /**
     * 保险到期日
     */
    @TableField("expired_date")
    private Date expiredDate;

    /**
     * 送修人
     */
    @TableField("deliverer")
    private String deliverer;

    /**
     * 送修人性别
     */
    @TableField("deliverer_gender")
    private Integer delivererGender;

    /**
     * 送修人电话
     */
    @TableField("deliverer_phone")
    private String delivererPhone;

    /**
     * 送修人手机
     */
    @TableField("deliverer_mobile")
    private String delivererMobile;

    /**
     * 送修人喜欢联络方式
     */
    @TableField("deliverer_hobby_contact")
    private Integer delivererHobbyContact;

    /**
     * 送修人与车主关系
     */
    @TableField("deliverer_relation_to_owner")
    private String delivererRelationToOwner;

    /**
     * 送修人工作单位
     */
    @TableField("deliverer_company")
    private String delivererCompany;

    /**
     * 送修人身份证
     */
    @TableField("deliverer_credit")
    private String delivererCredit;

    /**
     * 送修人地址
     */
    @TableField("deliverer_address")
    private String delivererAddress;

    /**
     * 邮编
     */
    @TableField("zip_code")
    private String zipCode;

    /**
     * 指定技师
     */
    @TableField("chief_technician")
    private String chiefTechnician;

    /**
     * 服务专员
     */
    @TableField("service_advisor")
    private String serviceAdvisor;

    /**
     * 续保专员
     */
    @TableField("insurance_advisor")
    private String insuranceAdvisor;

    /**
     * 定保专员
     */
    @TableField("maintain_advisor")
    private String maintainAdvisor;

    /**
     * 专属服务顾问姓名
     */
    @TableField("exclusive_service_consultant")
    private String exclusiveServiceConsultant;

    /**
     * 上次SA
     */
    @TableField("last_sa")
    private String lastSa;

    /**
     * 上次维修日期
     */
    @TableField("last_maintain_date")
    private Date lastMaintainDate;

    /**
     * 上次维修里程
     */
    @TableField("last_maintain_mileage")
    private BigDecimal lastMaintainMileage;

    /**
     * 上次保养日期
     */
    @TableField("last_maintenance_date")
    private Date lastMaintenanceDate;

    /**
     * 上次保养里程
     */
    @TableField("last_maintenance_mileage")
    private BigDecimal lastMaintenanceMileage;

    /**
     * 优惠模式代码
     */
    @TableField("discount_mode_code")
    private String discountModeCode;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 上报日期（最近维修日）
     */
    @TableField("submit_time")
    private Date submitTime;

    /**
     * 变速箱型式
     */
    @TableField("gear_type")
    private String gearType;

    /**
     * 年款
     */
    @TableField("year_model")
    private String yearModel;

    /**
     * 产品代码
     */
    @TableField("product_code")
    private String productCode;

    /**
     * 产地
     */
    @TableField("producting_area")
    private String productingArea;

    /**
     * 工单开单时间
     */
    @TableField("ro_create_date")
    private Date roCreateDate;

    /**
     * 是否dcrc专员
     */
    @TableField("is_dcrc_advisor")
    private Integer isDcrcAdvisor;

    /**
     * dcrc专员
     */
    @TableField("dcrc_advisor")
    private String dcrcAdvisor;

    /**
     * 车辆配置代码
     */
    @TableField("vsn")
    private String vsn;

    /**
     * 排放标准
     */
    @TableField("discharge_standard")
    private Integer dischargeStandard;

    /**
     * 系统日志
     */
    @TableField("system_remark")
    private String systemRemark;

    /**
     * 修改前上次保养日期
     */
    @TableField("system_last_maintenance_date")
    private Date systemLastMaintenanceDate;

    /**
     * 修改时间
     */
    @TableField("system_update_date")
    private Date systemUpdateDate;

    /**
     * 区县
     */
    @TableField("district")
    private Integer district;

    /**
     * 城市
     */
    @TableField("city")
    private Integer city;

    /**
     * 省份
     */
    @TableField("province")
    private Integer province;

    /**
     * 当前行驶里程
     */
    @TableField("current_mileage")
    private BigDecimal currentMileage;

    /**
     * 当前里程日期
     */
    @TableField("current_mileage_date")
    private Date currentMileageDate;

    /**
     * 购买方式
     */
    @TableField("ways_to_buy")
    private Integer waysToBuy;

    /**
     * 车辆类别
     */
    @TableField("vehicle_category")
    private Integer vehicleCategory;

    /**
     * 发票号
     */
    @TableField("v_invoice_no")
    private String vInvoiceNo;

    /**
     * 发票类型
     */
    @TableField("invoice_type")
    private String invoiceType;

    /**
     * 临近保养时间
     */
    @TableField("approach_maintenance_time")
    private Date approachMaintenanceTime;

    /**
     * 开票日期
     */
    @TableField("invoice_date")
    private Date invoiceDate;

    /**
     * 企业性质
     */
    @TableField("commpany_property")
    private Integer commpanyProperty;
    @TableField("unit_name")
    private String unitName;

    /**
     * bzd码
     */
    @TableField("bzd")
    private String bzd;

    /**
     * 电子首保卡号
     */
    @TableField("efw_card")
    private String efwCard;

    /**
     * jd标识
     */
    @TableField("jd_identification")
    private Integer jdIdentification;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;

    /**
     * 行驶证号
     */
    @TableField("driving_license")
    private String drivingLicense;

    /**
     * 选装(销售主数据)
     */
    @TableField("option_packag")
    private String optionPackag;

    /**
     * 水货车(是 否)1004
     */
    @TableField("smuggled_goods_vehicle")
    private Integer smuggledGoodsVehicle;

    /**
     * 动力形式(燃油车、新能源)
     */
    @TableField("dynamic_code")
    private Integer dynamicCode;


    /**
     * 首次进厂经销商编码
     */
    @TableField("first_in_dealer")
    private String firstInDealer;

    /**
     * OneID
     */
    @TableField("one_id")
    private Long oneId;

    /**
     * 是否voc
     */
    @TableField("is_voc")
    private Integer isVoc;

    /**
     * voc当前里程
     */
    @TableField("voc_mileage")
    private Integer vocMileage;


    /**
     * voc里程获取时间
     */
    @TableField("voc_time")
    private Date vocTime;


    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
