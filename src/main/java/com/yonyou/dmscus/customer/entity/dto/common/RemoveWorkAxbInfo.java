package com.yonyou.dmscus.customer.entity.dto.common;

import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.text.ParseException;
import java.util.List;

@Data
@Builder
public class RemoveWorkAxbInfo {
    /**
     * AXB中的A号码，只携带call
     * NumB或只携带callNumA、c
     * allNumB请求无效
     * */
    private String callNumA;
    /**
     * AXB中的B号码，只携带call
     * NumB或只携带callNumA、c
     * allNumB请求无效
     * */
    private String callNumB;
    /**
     * AXB中的X号码,平台签约的
     * 业务号码
     * */
    private String callNumX;
    /**
     * 绑定关系唯一标识
     * 携带bindId时，表示只解除b
     * indId标识的绑定关系。
     * 当携带bindId和其他参数时
     * （无论几个）系统都以bindId为准。
     * */
    private String bindId;

    public List<RemoveWorkAxbInfo> buildWorkAxnInfoList() throws ParseException {
        return Lists.newArrayList(this);
    }

}
