package com.yonyou.dmscus.customer.entity.dto.inviteInsurance;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import lombok.Data;

@Data
public class InviteInsuranceVehicleCustomerNumberDTO extends BaseDTO implements Serializable{

	 private static final long serialVersionUID = 1L;
    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * tt_invite_insurance_vehicle_record
     */
    private Long insuranceId;
    
    /**
     * tt_invite_insurance_vehicle_record_detail
     */
    private Long insuranceDetailId;

    /**
     * call_id
     */
    private String callId;

    /**
     * 服务顾问ID
     */
    private String saId;

    /**
     * 客户名称
     */
    private String cusName;

    /**
     * 客户电话
     */
    private String cusNumber;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;



    /**
     * 经销商代码
     */
    private String dealerCode;


    /**
     * 服务顾问姓名
     */
    private String saName;

    /**
     * 服务顾问手机号
     */
    private String saNumber;

    /**
     * AI语音工作号
     */
    private String workNumber;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }
}
