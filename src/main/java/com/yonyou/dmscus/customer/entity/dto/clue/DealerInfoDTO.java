package com.yonyou.dmscus.customer.entity.dto.clue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * description 经销商信息参数
 *
 * <AUTHOR>
 * @date 2023/8/21 17:18
 */
@Data
@ApiModel(description = "经销商信息参数")
public class DealerInfoDTO {
    /**
     * 经销商编号
     */
    @ApiModelProperty(value = "经销商编号", required = true, example = "SHJ")
    private String dealerCode;
    /**
     * 经销商类型
     */
    @ApiModelProperty(value = "经销商类型")
    private String dealerType;
    /**
     * 经销商名称
     */
    @ApiModelProperty(value = "经销商名称", required = true, example = "AA")
    private String dealerName;
}
