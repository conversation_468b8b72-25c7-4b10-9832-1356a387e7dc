package com.yonyou.dmscus.customer.entity.dto.accidentClues;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.util.common.DateUtils;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/11/17 10:28
 * @Version 1.0
 */
@ApiModel("线索信息推送LiteCRM参数信息")
@Data
@Slf4j
public class ClueInfoPushDTO {
    @ApiModelProperty(value = "线索信息")
    private AccidentClueInfo accidentInfo;
    @ApiModelProperty(value = "车辆信息")
    private CarInfo carInfo;
    @ApiModelProperty(value = "联系人信息")
    private List<ContactInfo> contactInfo;
    @ApiModelProperty(value = "经销商信息")
    private List<DealerInfo> dealerInfo;
    @ApiModelProperty(value = "线索id")
    private String sourceClueId;
    @ApiModelProperty(value = "固定值103")
    private String leadsType;
    @ApiModelProperty(value = "车辆vin码")
    private String vehicleVin;
    @ApiModelProperty(value = "工单状态")
    private String bizStatus;
    @ApiModelProperty(value = "跟进状态")
    private String followUpStatus;

    public ClueInfoPushDTO() {
    }
    public ClueInfoPushDTO(AccidentCluesDTO clue) {

        this.accidentInfo = getClueInfo(clue);
        this.carInfo = getCarInfo(clue);
        this.contactInfo = getContactsInfo(clue);
        this.dealerInfo = getDealerInfo(clue.getDealerCode(),clue.getDealerName());
        this.sourceClueId = String.valueOf(clue.getAcId());

        log.info("事故线索信息推送：{}", this);
    }

    private AccidentClueInfo getClueInfo(AccidentCluesDTO clue){

        AccidentClueInfo clueInfo = new AccidentClueInfo();
        clueInfo.setSourceClueId(String.valueOf(clue.getAcId()));
        clueInfo.setComments(ObjectUtils.isEmpty(clue.getRemark()) ? "" : clue.getRemark());
        clueInfo.setAccidentType(String.valueOf(clue.getAccidentType()));
        clueInfo.setReportTime(DateUtils.formatDate(clue.getReportDate(), DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS));
        clueInfo.setInsuranceCompany(clue.getInsuranceCompanyName());
        clueInfo.setCasualtiesFlag(ObjectUtils.nullSafeEquals(CommonConstants.DICT_IS_YES, clue.getIsBruise()));
        clueInfo.setSourceType(String.valueOf(clue.getCluesResource()));
        clueInfo.setSourceChannel("NewBie");
        return clueInfo;
    }

    private CarInfo getCarInfo(AccidentCluesDTO clue){

        CarInfo carInfo = new CarInfo();
        carInfo.setCarModelId(clue.getModelsId());
        carInfo.setLicencePlate(clue.getLicense());
        return carInfo;
    }

    private List<ContactInfo> getContactsInfo(AccidentCluesDTO clue){

        if (ObjectUtils.isEmpty(clue.getContactList())){
            return new ArrayList<>();
        }
        List<ContactInfo> contactInfos = new ArrayList<>(clue.getContactList().size());
        clue.getContactList().forEach(con -> {
            ContactInfo contactInfo = new ContactInfo();
            contactInfo.setCustomerMobile(con.getContactsPhone());
            contactInfo.setCustomerName(con.getContacts());
            contactInfos.add(contactInfo);
        });
        return contactInfos;
    }

    private List<DealerInfo> getDealerInfo(String dealerCode,String dealerName){

        DealerInfo dealerInfo = new DealerInfo();
        dealerInfo.setDealerCode(dealerCode);
        dealerInfo.setDealerName(dealerName);
        return Collections.singletonList(dealerInfo);
    }

    public String getPushParams(ClueInfoPushDTO pushInfo, AccidentCluesDTO clue){

        Map<String, Object> pushParams = new HashMap<>();
        pushParams.put("data",pushInfo);
        pushParams.put("sourceClueId", pushInfo.getSourceClueId());
        pushParams.put("leadsType", "103");
        pushParams.put("vehicleVin", clue.getVin());
        pushParams.put("bizStatus", ObjectUtils.isEmpty(clue.getCluesStatus()) ? String.valueOf(CommonConstants.CLUES_STATUS_UNFINISHED) : String.valueOf(clue.getCluesStatus()));
        pushParams.put("followUpStatus", ObjectUtils.isEmpty(clue.getFollowStatus()) ? String.valueOf(CommonConstants.FOLLOW_STATUS_WGJ) : String.valueOf(clue.getFollowStatus()));
        pushParams.put("talkFlag",false);
        pushParams.put("comments",clue.getRemark());
        List<ContactInfo> contactList = pushInfo.getContactInfo();
        if (CollUtil.isNotEmpty(contactList) && Objects.nonNull(contactList.get(0))) {
            pushParams.put("customerName", contactList.get(0).getCustomerName());
            pushParams.put("customerMobile", contactList.get(0).getCustomerMobile());
        }
        return JSONObject.toJSONString(pushParams);
    }

    public boolean checkFieldsNotNull(ClueInfoPushDTO pushInfo){

        AccidentClueInfo clueInfo = pushInfo.getAccidentInfo();
        boolean infoFlag = !ObjectUtils.isEmpty(clueInfo)
                && !ObjectUtils.isEmpty(clueInfo.getAccidentType())
                && !ObjectUtils.isEmpty(clueInfo.getCasualtiesFlag())
                && !ObjectUtils.isEmpty(clueInfo.getInsuranceCompany())
                && !ObjectUtils.isEmpty(clueInfo.getSourceType())
                && !ObjectUtils.isEmpty(clueInfo.getReportTime());

        CarInfo carInfo = pushInfo.getCarInfo();
        boolean carFlag = !ObjectUtils.isEmpty(carInfo)
                // && !ObjectUtils.isEmpty(carInfo.getCarModelId())
                && !ObjectUtils.isEmpty(carInfo.getLicencePlate());

        boolean dealerFlag = !ObjectUtils.isEmpty(pushInfo.getDealerInfo());

        return infoFlag && carFlag && dealerFlag;
    }
}

@Data
class AccidentClueInfo{

    @ApiModelProperty(value = "线索id")
    private String sourceClueId;
    @ApiModelProperty(value = "事故类型")
    private String accidentType;
    @ApiModelProperty(value = "人员伤亡")
    private Boolean casualtiesFlag;
    @ApiModelProperty(value = "备注信息")
    private String comments;
    @ApiModelProperty(value = "保险公司")
    private String insuranceCompany;
    @ApiModelProperty(value = "报案时间")
    private String reportTime;
    @ApiModelProperty(value = "线索来源code")
    private String sourceType;
    @ApiModelProperty("来源渠道")
    private String sourceChannel;
}
@Data
class CarInfo{

    @ApiModelProperty(value = "车型")
    private String carModelId;
    @ApiModelProperty(value = "拍照")
    private String licencePlate;
}
@Data
class ContactInfo{

    @ApiModelProperty(value = "客户手机号")
    private String customerMobile;
    @ApiModelProperty(value = "客户姓名")
    private String customerName;
}
@Data
class DealerInfo{

    @ApiModelProperty(value = "经销商code")
    private String dealerCode;
    @ApiModelProperty("经销商名")
    private String dealerName;
}
