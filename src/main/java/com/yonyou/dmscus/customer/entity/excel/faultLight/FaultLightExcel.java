package com.yonyou.dmscus.customer.entity.excel.faultLight;

import com.yonyou.dmscus.customer.utils.excel.ExcelImport;
import lombok.Data;

@Data
public class FaultLightExcel{

    /**
     * 主键id
     */
    @ExcelImport("waring ID")
    private Long id;

    /**
     * 故障(en)
     */
    @ExcelImport("waring english description")
    private String waringEnDescription;

    /**
     * 故障(cn)
     */
    @ExcelImport("waring chinese description")
    private String waringCnDescription;

    /**
     * 故障名称
     */
    @ExcelImport("Warning Chinese name")
    private String warningName;

    /**
     * 故障英文名
     */
    @ExcelImport("warning name")
    private String warningEnName;

    /**
     * 故障等级
     */
    @ExcelImport("报警等级")
    private String faultGrade;

    /**
     * 次数
     */
    private Integer num = 3;

    /**
     * 过期数
     */
    private Integer expireNumber = 3;

    /**
     * 线索生成状态:1，启用；0，未启用
     */
    private Integer produceStatus = 1;

    /**
     * 责任验证状态:1，启用；0，未启用
     */
    private Integer dutyStatus = 1;
}
