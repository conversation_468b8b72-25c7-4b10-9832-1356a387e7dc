package com.yonyou.dmscus.customer.entity.dto.accidentClues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 预约单返回参数Vo
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiModel("预约单返回参数Vo")
@Data
public class BookingOrderReturnVo {

    @ApiModelProperty(value = "预约单号",name = "bookingOrderNo")
    private String bookingOrderNo;

    @ApiModelProperty(value = "修改次数",name = "changeNumber")
    private Integer changeNumber;

    @ApiModelProperty(value="中台预约单号",name="appointmentId")
    private String appointmentId;

    @ApiModelProperty(value="预约单状态",name="bookingOrderStatus")
    private Integer bookingOrderStatus;
}
