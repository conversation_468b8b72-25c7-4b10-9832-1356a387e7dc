package com.yonyou.dmscus.customer.entity.dto.goodwill;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 亲善AWA打印模板维护
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-23
 */
    
public class GoodwillAwaPrintMaintainDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

        
    /**
     * 系统ID
     */
                private String appId;
                
    /**
     * 所有者代码
     */
                private String ownerCode;
                
    /**
     * 所有者的父组织代码
     */
                private String ownerParCode;
                
    /**
     * 组织ID
     */
                private Integer orgId;
                
    /**
     * 主键id
     */
                private Long id;
                
    /**
     * approval
     */
                private String approval;
                
    /**
     * requestor
     */
                private String requestor;
                
    /**
     * line_director
     */
                private String lineDirector;
                
    /**
     * cs_vp
     */
                private String csVp;
                
    /**
     * date_one
     */
                private String dateOne;
                
    /**
     * date_two
     */
                private String dateTwo;
                
    /**
     * date_three
     */
                private String dateThree;
                
    /**
     * cfo
     */
                private String cfo;
                
    /**
     * coo
     */
                private String coo;
                
    /**
     * md
     */
                private String md;
                
    /**
     * date_four
     */
                private String dateFour;
                
    /**
     * date_five
     */
                private String dateFive;
                
    /**
     * date_six
     */
                private String dateSix;
                
    /**
     * explain_one
     */
                private String explainOne;
                
    /**
     * explain_two
     */
                private String explainTwo;
                
    /**
     * 是否有效
     */
                private Integer isValid;
                
    /**
     * 是否删除:1,删除；0,未删除
     */
                private Boolean isDeleted;
                
    /**
     * 创建时间
     */
    private Date createdAt;
                
    /**
     * 修改时间
     */
    private Date updatedAt;
            
    public GoodwillAwaPrintMaintainDTO() {
        super();
    }

                                
    public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
            }
                                
    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
            }
                                
    public String getOwnerParCode(){
        return ownerParCode;
    }


    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
            }
                                
    public Integer getOrgId(){
        return orgId;
    }


    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
            }
                                            
    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
            }
                                
    public String getApproval(){
        return approval;
    }


    public void  setApproval(String approval) {
        this.approval = approval;
            }
                                
    public String getRequestor(){
        return requestor;
    }


    public void  setRequestor(String requestor) {
        this.requestor = requestor;
            }
                                
    public String getLineDirector(){
        return lineDirector;
    }


    public void  setLineDirector(String lineDirector) {
        this.lineDirector = lineDirector;
            }
                                
    public String getCsVp(){
        return csVp;
    }


    public void  setCsVp(String csVp) {
        this.csVp = csVp;
            }
                                
    public String getDateOne(){
        return dateOne;
    }


    public void  setDateOne(String dateOne) {
        this.dateOne = dateOne;
            }
                                
    public String getDateTwo(){
        return dateTwo;
    }


    public void  setDateTwo(String dateTwo) {
        this.dateTwo = dateTwo;
            }
                                
    public String getDateThree(){
        return dateThree;
    }


    public void  setDateThree(String dateThree) {
        this.dateThree = dateThree;
            }
                                
    public String getCfo(){
        return cfo;
    }


    public void  setCfo(String cfo) {
        this.cfo = cfo;
            }
                                
    public String getCoo(){
        return coo;
    }


    public void  setCoo(String coo) {
        this.coo = coo;
            }
                                
    public String getMd(){
        return md;
    }


    public void  setMd(String md) {
        this.md = md;
            }
                                
    public String getDateFour(){
        return dateFour;
    }


    public void  setDateFour(String dateFour) {
        this.dateFour = dateFour;
            }
                                
    public String getDateFive(){
        return dateFive;
    }


    public void  setDateFive(String dateFive) {
        this.dateFive = dateFive;
            }
                                
    public String getDateSix(){
        return dateSix;
    }


    public void  setDateSix(String dateSix) {
        this.dateSix = dateSix;
            }
                                
    public String getExplainOne(){
        return explainOne;
    }


    public void  setExplainOne(String explainOne) {
        this.explainOne = explainOne;
            }
                                
    public String getExplainTwo(){
        return explainTwo;
    }


    public void  setExplainTwo(String explainTwo) {
        this.explainTwo = explainTwo;
            }
                                
    public Integer getIsValid(){
        return isValid;
    }


    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
            }
                                
   
    
    public Date getCreatedAt() {
		return createdAt;
	}


	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}


	public Date getUpdatedAt() {
		return updatedAt;
	}


	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}


	@Override
    public String toString() {
        return "GoodwillAwaPrintMaintainDTO{" +
                                            "appId=" + appId +
                                                            ", ownerCode=" + ownerCode +
                                                            ", ownerParCode=" + ownerParCode +
                                                            ", orgId=" + orgId +
                                                            ", id=" + id +
                                                            ", approval=" + approval +
                                                            ", requestor=" + requestor +
                                                            ", lineDirector=" + lineDirector +
                                                            ", csVp=" + csVp +
                                                            ", dateOne=" + dateOne +
                                                            ", dateTwo=" + dateTwo +
                                                            ", dateThree=" + dateThree +
                                                            ", cfo=" + cfo +
                                                            ", coo=" + coo +
                                                            ", md=" + md +
                                                            ", dateFour=" + dateFour +
                                                            ", dateFive=" + dateFive +
                                                            ", dateSix=" + dateSix +
                                                            ", explainOne=" + explainOne +
                                                            ", explainTwo=" + explainTwo +
                                                            ", isValid=" + isValid +
                                                            ", isDeleted=" + isDeleted +
                                                            ", createdAt=" + createdAt +
                                                            ", updatedAt=" + updatedAt +
                                    "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
