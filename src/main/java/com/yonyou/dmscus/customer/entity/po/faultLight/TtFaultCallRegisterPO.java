package com.yonyou.dmscus.customer.entity.po.faultLight;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("故障灯呼叫登记")
@TableName("tt_fault_call_register")
    public class TtFaultCallRegisterPO{

    private static final long serialVersionUID = -6025213429496675538L;

    /**
     * 系统id
     */
    @TableField("app_id")
    private String appId;

    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    @TableField("owner_par_code")
    private String ownerParCode;

    /**
     * 组织id
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键id
     */
    @TableField("id")
    private Long id;

    /**
     * 邀约id
     */
    @TableField("invite_id")
    private Long inviteId;

    @TableField("detail_id")
    private Long detailId;

    /**
     * call_id
     */
    @TableField("call_id")
    private String callId;

    /**
     * 服务顾问id
     */
    @TableField("sa_id")
    private String saId;

    /**
     * 客户名称
     */
    @TableField("cus_name")
    private String cusName;

    /**
     * 客户电话
     */
    @TableField("cus_number")
    private String cusNumber;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private int isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;

    /**
     * 创建人id
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 更新人id
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;

    /**
     * 版本号(乐观锁)
     */
    @TableField("record_version")
    private Integer recordVersion;

    /**
     * 经销商代码
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 服务顾问姓名
     */
    @TableField("sa_name")
    private String saName;

    /**
     * 服务顾问手机号
     */
    @TableField("sa_number")
    private String saNumber;

    /**
     * ai语音工作号
     */
    @TableField("work_number")
    private String workNumber;

    /**
     * etl需求，用于标记抽取数据，便于清除
     */
    @TableField("mark")
    private String mark;

    /**
     * 批次号
     */
    @TableField("batch_no")
    private String batchNo;
}
