package com.yonyou.dmscus.customer.entity.dto.goodwill;

import java.io.Serializable;

//返回经销商信息
@SuppressWarnings("serial")
public class GoodwillFirstPageDTO implements Serializable {
	private String dealerCode; // 经销商代码

	private Integer smallAreaManage;// 小区ID

	private Integer bigAreaManage;// 大区ID

	private String[] roleList;// 角色

	public String getDealerCode() {
		return dealerCode;
	}

	public void setDealerCode(String dealerCode) {
		this.dealerCode = dealerCode;
	}

	public Integer getSmallAreaManage() {
		return smallAreaManage;
	}

	public void setSmallAreaManage(Integer smallAreaManage) {
		this.smallAreaManage = smallAreaManage;
	}

	public Integer getBigAreaManage() {
		return bigAreaManage;
	}

	public void setBigAreaManage(Integer bigAreaManage) {
		this.bigAreaManage = bigAreaManage;
	}

	public String[] getRoleList() {
		return roleList;
	}

	public void setRoleList(String[] roleList) {
		this.roleList = roleList;
	}

}
