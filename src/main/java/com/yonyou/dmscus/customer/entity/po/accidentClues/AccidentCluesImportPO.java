package com.yonyou.dmscus.customer.entity.po.accidentClues;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.service.excel.ExcelColumnDefine;

import lombok.Data;

@Data
@TableName("tt_accident_clues_import")
public class AccidentCluesImportPO extends BasePO<AccidentCluesImportPO>{
	
	@TableId("id")
	private Integer id;
	
	@TableField("dealer_code")
	private String dealerCode;
	
	@TableField("license")
	private String license;
	
	@TableField("models")
	private String models;
	
//	@TableField("models_id")
//	private String models_id;
	
	@TableField("contacts")
	private String contacts;
	
	@TableField("contacts_phone")
	private String contactsPhone;
	
	@TableField("clues_resource")
	private Integer cluesResource;
	
	@TableField("outside_amount")
	private BigDecimal outsideAmount;
	
	@TableField("report_date")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date reportDate;
	
	@TableField("accident_type")
	private Integer accidentType;
	
	@TableField("is_bruise")
	private Integer isBruise;
	
	@TableField("accident_address")
	private String accidentAddress;
	
	@TableField("insurance_company_name")
	private String  insuranceCompanyName;
	
	@TableField("remark")
	private String remark;
	
	@TableField("is_error")
	private Integer isError;
	
	@TableField("error_msg")
	private String errorMsg;
	
	@TableField("line_number")
	private Integer lineNumber;
	
	@TableField(exist = false)
	private String importSuccess;
	
	

}
