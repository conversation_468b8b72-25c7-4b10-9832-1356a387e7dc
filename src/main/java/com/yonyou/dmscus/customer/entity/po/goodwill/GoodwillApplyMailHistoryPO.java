package com.yonyou.dmscus.customer.entity.po.goodwill;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善预申请邮件发送记录
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
@TableName("tt_goodwill_apply_mail_history")
public class GoodwillApplyMailHistoryPO extends BasePO<GoodwillApplyMailHistoryPO> {

	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	@TableField("APP_ID")
	private String appId;

	/**
	 * 所有者代码
	 */
	@TableField("OWNER_CODE")
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	@TableField("OWNER_PAR_CODE")
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	@TableField("ORG_ID")
	private Integer orgId;

	/**
	 * 主键ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 亲善单id
	 */
	@TableField("goodwill_apply_id")
	private Long goodwillApplyId;

	/**
	 * 经销商
	 */
	@TableField("dealer_code")
	private String dealerCode;

	/**
	 * 邮件类型
	 */
	@TableField("mail_type")
	private Integer mailType;

	/**
	 * 发件人
	 */
	@TableField("send_by")
	private String sendBy;

	/**
	 * 收件人邮箱
	 */
	@TableField("receiver_mail")
	private String receiverMail;

	/**
	 * 主题
	 */
	@TableField("title")
	private String title;

	/**
	 * 邮件内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 发送状态
	 */
	@TableField("send_status")
	private Integer sendStatus;

	/**
	 * 发送时间
	 */
	@TableField("send_time")
	private Date sendTime;

	/**
	 * 是否有效
	 */
	@TableField("is_valid")
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	@TableField("is_deleted")
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	@TableField("created_at")
	private Date createdAt;

	/**
	 * 修改时间
	 */
	@TableField("updated_at")
	private Date updatedAt;

	@TableField(exist = false)
	private String createdStartAt;
	@TableField(exist = false)
	private String createdEndAt;

	@TableField(exist = false)
	private String vin;

	@TableField(exist = false)
	private String applyNo;

	/**
	 * 经销商
	 */
	@TableField(exist = false)
	private String dealerName;
	/**
	 * 集团ID
	 */
	@TableField(exist = false)
	private Integer blocId;
	/**
	 * 区域ID
	 */
	@TableField(exist = false)
	private String areaManage;
	/**
	 * 区域ID
	 */
	@TableField(exist = false)
	private String smallArea;
	/**
	 * 小區經理名稱
	 */
	@TableField(exist = false)
	private String auditName;
	/**
	 * 小區經理名稱
	 */
	@TableField(exist = false)
	private List<String> auditName1;

	public String getAuditName() {
		return auditName;
	}

	public void setAuditName(String auditName) {
		this.auditName = auditName;
	}

	public List<String> getAuditName1() {
		return auditName1;
	}

	public void setAuditName1(List<String> auditName1) {
		this.auditName1 = auditName1;
	}

	public Integer getBlocId() {
		return blocId;
	}

	public void setBlocId(Integer blocId) {
		this.blocId = blocId;
	}

	public String getAreaManage() {
		return areaManage;
	}

	public void setAreaManage(String areaManage) {
		this.areaManage = areaManage;
	}

	public String getSmallArea() {
		return smallArea;
	}

	public void setSmallArea(String smallArea) {
		this.smallArea = smallArea;
	}

	public String getDealerName() {
		return dealerName;
	}

	public void setDealerName(String dealerName) {
		this.dealerName = dealerName;
	}

	public String getApplyNo() {
		return applyNo;
	}

	public void setApplyNo(String applyNo) {
		this.applyNo = applyNo;
	}

	public String getVin() {
		return vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public String getCreatedStartAt() {
		return createdStartAt;
	}

	public void setCreatedStartAt(String createdStartAt) {
		this.createdStartAt = createdStartAt;
	}

	public String getCreatedEndAt() {
		return createdEndAt;
	}

	public void setCreatedEndAt(String createdEndAt) {
		this.createdEndAt = createdEndAt;
	}

	public GoodwillApplyMailHistoryPO() {
		super();
	}

	@Override
	public String getAppId() {
		return appId;
	}

	@Override
	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	@Override
	public String getOwnerParCode() {
		return ownerParCode;
	}

	@Override
	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getGoodwillApplyId() {
		return goodwillApplyId;
	}

	public void setGoodwillApplyId(Long goodwillApplyId) {
		this.goodwillApplyId = goodwillApplyId;
	}

	public String getDealerCode() {
		return dealerCode;
	}

	public void setDealerCode(String dealerCode) {
		this.dealerCode = dealerCode;
	}

	public Integer getMailType() {
		return mailType;
	}

	public void setMailType(Integer mailType) {
		this.mailType = mailType;
	}

	public String getSendBy() {
		return sendBy;
	}

	public void setSendBy(String sendBy) {
		this.sendBy = sendBy;
	}

	public String getReceiverMail() {
		return receiverMail;
	}

	public void setReceiverMail(String receiverMail) {
		this.receiverMail = receiverMail;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Integer getSendStatus() {
		return sendStatus;
	}

	public void setSendStatus(Integer sendStatus) {
		this.sendStatus = sendStatus;
	}

	public Date getSendTime() {
		return sendTime;
	}

	public void setSendTime(Date sendTime) {
		this.sendTime = sendTime;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	@Override
	public Date getCreatedAt() {
		return createdAt;
	}

	@Override
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Override
	public Date getUpdatedAt() {
		return updatedAt;
	}

	@Override
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	protected Serializable pkVal() {
		return this.id;
	}

	@Override
	public String toString() {
		return "GoodwillApplyMailHistoryPO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", goodwillApplyId=" + goodwillApplyId
				+ ", dealerCode=" + dealerCode + ", mailType=" + mailType + ", sendBy=" + sendBy + ", receiverMail="
				+ receiverMail + ", title=" + title + ", content=" + content + ", sendStatus=" + sendStatus
				+ ", sendTime=" + sendTime + ", isValid=" + isValid + ", isDeleted=" + isDeleted + ", createdAt="
				+ createdAt + ", updatedAt=" + updatedAt + "}";
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dtoClass
	 *            需要进行转换的dtoClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
		return super.transDtoToPo(dtoClass);
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dto
	 *            需要进行转换的dto
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> void transPoToDto(T dto) {
		BeanMapperUtil.copyProperties(this, dto, "id");
	}

}
