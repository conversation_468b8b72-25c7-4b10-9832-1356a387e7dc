package com.yonyou.dmscus.customer.entity.po.accidentClues;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/10/25 15:58
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tt_accident_clues_contacts")
public class AccidentCluesContactsPO extends BasePO<AccidentCluesContactsPO> {

    /**
     * 主键ID
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    @TableField("app_id")
    private String appId;

    @TableField("owner_code")
    private String ownerCode;

    @TableField("owner_par_code")
    private String ownerParCode;

    @TableField("org_id")
    private Integer orgId;

    @TableField("ac_id")
    private Long acId;

    @TableField("dealer_code")
    private String dealerCode;

    @TableField("contacts")
    private String contacts;

    @TableField("contacts_phone")
    private String contactsPhone;

    @TableField("one_id")
    private Long oneId;

    @TableField("is_owner")
    private Integer isOwner;

    //数据来源
    @TableField("data_sources")
    private Integer dataSources;

    //是否删除，1：删除，0：未删除"
    @TableField("is_deleted")
    private Integer isDeleted;

    //是否有效"
    @TableField("is_valid")
    private Integer isValid;

    //创建时间"
    @TableField("created_at")
    private Date createdAt;

    //创建人"
    @TableField("created_by")
    private String createdBy;

    //更新时间"
    @TableField("updated_at")
    private Date updatedAt;

    //更新人
    @TableField("updated_by")
    private String updatedBy;

    //版本号（乐观锁）
    @TableField("record_version")
    private Integer recordVersion;
}
