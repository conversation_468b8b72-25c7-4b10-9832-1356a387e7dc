package com.yonyou.dmscus.customer.entity.dto.goodwill;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 亲善预申请单
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-06
 */

public class GoodwillApplyInfoDTO extends BaseDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	private String appId;

	/**
	 * 所有者代码
	 */
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	private Integer orgId;

	/**
	 * 预申请单表id
	 */
	private Long id;
	/**
	 * 预申请单表亲善信息Id
	 */
	private Long materialId;

	/**
	 * 通知开票Id
	 */
	private Long noticeInvoiceId;
	/**
	 * 卡券Id
	 */
	private Long couponId;

	/**
	 * 预申请单号
	 */
	private String applyNo;

	/**
	 * 申请经销商
	 */
	private String dealerCode;

	/**
	 * 申请经销商名称
	 */
	private String dealerName;
	/**
	 * 区域ID
	 */
	private Integer areaManageId;
	/**
	 * 区域ID
	 */
	private String areaManageId1;
	/**
	 * 区域-区域经理
	 */
	private String areaManage;
	/**
	 * 大区ID
	 */
	private Integer smallAreaId;
	/**
	 * 大区ID
	 */
	private String smallAreaId1;
	/**
	 * 大区名称
	 */
	private String smallArea;
	/**
	 * 集团ID
	 */
	private Integer blocId;
	/**
	 * 集团
	 */
	private String bloc;

	/**
	 * 申请人
	 */
	private String applyPerson;

	/**
	 * 审核类型
	 */
	private Integer auditType;

	/**
	 * 亲善性质
	 */
	private Integer goodwillNature;

	/**
	 * 申请时间
	 */

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date applyTime;

	/**
	 * 投诉故障
	 */
	private String complaintFalut;
	/**
	 * 投诉故障
	 */
	private String[] complaintFalut1;

	/**
	 * 厂端登录账号角色
	 */
	private String[] roleList;
	/**
	 * 大区登录账号角色
	 */
	private String[] roleList2;
	/**
	 * 小区登录账号角色
	 */
	private String roleList1;

	/**
	 * 投诉单ID
	 */
	private String complaintId;
	/**
	 * 投诉日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date complaintDate;

	/**
	 * 用车情况
	 */
	private Integer vehicleUse;

	/**
	 * 销售经销商
	 */
	private String salesDealer;

	/**
	 * 申请金额
	 */
	private BigDecimal applyAmount;

	/**
	 * 审批金额
	 */
	private BigDecimal auditAmount;

	/**
	 * 审批金额
	 */
	private BigDecimal auditPrice;

	/**
	 * 结算金额
	 */
	private BigDecimal settlementAmount;

	/**
	 * 通知开票金额
	 */
	private BigDecimal invoiceAmount;

	/**
	 * 是否需要CCMQ翻译
	 */
	private BigDecimal isNeedTranslate;

	/**
	 * 亲善单状态
	 */
	private Integer goodwillStatus;

	/**
	 * 重启前亲善单状态
	 */
	private Integer lastGoodwillStatus;

	/**
	 * 客户痛点
	 */
	private List<Integer> customerPainVue;

	/**
	 * 客户痛点
	 */
	private String customerPain;

	/**
	 * VIN
	 */
	private String vin;

	/**
	 * 车牌号
	 */
	private String license;
	/**
	 * 客户oneId
	 */
	private String ownerNo;

	/**
	 * 客户姓名
	 */
	private String customerName;

	/**
	 * 客户电话
	 */
	private String customerMobile;

	/**
	 * 里程
	 */
	private String mileage;

	/**
	 * 车型
	 */
	private String model;

	/**
	 * 购车日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date buyCarDate;

	/**
	 * 保修开始日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date warrantyStartDate;

	/**
	 * 是否延保
	 */
	private Integer isExtendWarranty;

	/**
	 * 延保名称
	 */
	private String extendWarrantyName;

	/**
	 * 延保开始日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date extendWarrantyStartDate;

	/**
	 * 延保结束日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date extendWarrantyEndDate;

	/**
	 * 保养成本
	 */
	private BigDecimal maintainCost;

	/**
	 * 延保成本
	 */
	private BigDecimal extendWarrantyCost;

	/**
	 * 附件精品成本
	 */
	private BigDecimal accessoryCost;

	/**
	 * 代金券成本
	 */
	private BigDecimal voucherCost;

	/**
	 * 代步车/相关利益
	 */
	private BigDecimal walkingCarPrice;

	/**
	 * 沃世界积分
	 */
	private BigDecimal volvoIntegral;

	/**
	 * 退换车
	 */
	private BigDecimal returnChangeCarPrice;

	/**
	 * 其他
	 */
	private BigDecimal otherPrice;

	/**
	 * 成本总计
	 */
	private BigDecimal costTotal;

	/**
	 * 客户支付
	 */
	private BigDecimal customerPay;

	/**
	 * 经销商承担
	 */
	private BigDecimal dealerUndertake;

	/**
	 * volvo支持亲善金额
	 */
	private BigDecimal volvoSupportGoodwillAmount;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 预申请附件
	 */
	private String applyFile;

	/**
	 * 亲善成本统计
	 */
	private String costStatisticsFile;

	/**
	 * 亲善成本截图
	 */
	private String costScreenshotFile;

	/**
	 * 故障维修工单/环检单
	 */
	private String ringCheckFile;

	/**
	 * 故障维修领料单
	 */
	private String troubleRepairRequisitionFile;

	/**
	 * 亲善安装工单/领料单
	 */
	private String workOrderFile;

	/**
	 * 情况说明和解协议
	 */
	private String situationSettlementAgreementFile;

	/**
	 * 退换车补充材料
	 */
	private String supplementaryMaterialFile;

	/**
	 * 管理层审核邮件-VP
	 */
	private String managementReviewEmailVpFile;

	/**
	 * 管理层审核邮件-CEO
	 */
	private String managementReviewEmailCeoFile;

	/**
	 * 费用更新附件
	 */
	private String costUpdateFile;

	/**
	 * VCDC其他附件
	 */
	private String vcdcElseFile;

	/**
	 * 客户身份证明
	 */
	private String customerIdentification;

	/**
	 * 其他附件
	 */
	private String elseFile;

	/**
	 * 提交时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date commitTime;

	/**
	 * 是否有效
	 */
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	private Date createdAt;

	/**
	 * 修改时间
	 */
	private Date updatedAt;

	/**
	 * 最后操作人
	 */
	private String updatedPerson;

	/**
	 * 客户背景
	 */
	private String customerBackground;

	/**
	 * 客户背景英文
	 */
	private String customerBackgroundEn;

	/**
	 * 投诉原因及处理经过
	 */
	private String reasonAndDispose;

	/**
	 * 投诉原因及处理经过英文
	 */
	private String reasonAndDisposeEn;

	/**
	 * 维修解决方案
	 */
	private String repairSolution;

	/**
	 * 维修解决方案英文
	 */
	private String repairSolutionEn;

	/**
	 * 客户要求
	 */
	private String customerRequire;

	/**
	 * 客户要求英文
	 */
	private String customerRequireEn;

	/**
	 * 潜在风险
	 */
	private String potentialRisk;

	/**
	 * 潜在风险英文
	 */
	private String potentialRiskEn;

	/**
	 * VR或TJ号英文
	 */
	private String vrOrTjNoEn;

	/**
	 * VR或TJ号
	 */
	private String vrOrTjNo;

	/**
	 * 商务亲善申请详情英文
	 */
	private String businessGoodwillApplyDetailEn;

	/**
	 * 商务亲善申请详情
	 */
	private String businessGoodwillApplyDetail;

	/**
	 * 维修记录
	 */
	private List<GoodwillApplyRepairInfoDTO> goodwillApplyRepairInfoDTO;

	/**
	 * 零配件信息
	 */
	private List<GoodwillApplyRepairPartInfoDTO> goodwillApplyRepairPartInfoDTO;

	/**
	 * 亲善类型及金额-保养成本
	 */
	private List<GoodwillApplyFinalPartInfoDTO> maintainCostTable;

	/**
	 * 亲善类型及金额-延保成本
	 */
	private List<GoodwillApplyFinalPartInfoDTO> extendWarrantyCostTable;

	/**
	 * 亲善类型及金额-附件精品成本
	 */
	private List<GoodwillApplyFinalPartInfoDTO> accessoryCostTable;

	private Integer isMaintainChange;
	private Integer isExtendWarrantyChange;
	private Integer isAccessoryChange;

	/**
	 * 预申请通过时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date passTime;

	/**
	 * 材料审核通过时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date materialPassTime;
	/**
	 * 申请时间
	 */
	private Date applyStartTime;
	private Date applyEndTime;
	/**
	 * 预申请通过时间
	 */
	private Date passStartTime;
	private Date passEndTime;
	/**
	 * 申请金额
	 */
	private BigDecimal applyStartAmount;
	private BigDecimal applyEndAmount;

	/**
	 * 开票时间
	 */
	private Date invoiceStartDate;
	private Date invoiceEndDate;
	private Date invoiceDate;
	/**
	 * 通知开票时间
	 */
	private Date noticeInvoiceStartDate;
	private Date noticeInvoiceEndDate;
	private Date noticeInvoiceDate;

	/**
	 * 审计时间
	 */
	private Date auditStartTime;
	private Date auditEndTime;

	/**
	 * 更新时间
	 */
	private Date updatedStartAt;
	private Date updatedEndAt;

	/**
	 * 用户id
	 */
	private Long userId;

	/**
	 * 预申请已审核流程数
	 */
	private Integer applyCount;
	/**
	 * 材料已审核流程数
	 */
	private Integer materialCount;

	/**
	 * 账号类型
	 */
	private Integer dataType;

	/**
	 * 查询限制条件经销商
	 */
	private String ownerCodes;

	/**
	 * 下一步审核角色
	 */
	private String auditRole;

	/**
	 * 是否CCMQ翻译
	 */
	private String auditCcmq;

	/**
	 * 审计结果
	 */
	private Integer auditResult;

	/**
	 * 审计方式
	 */
	private Integer auditWay;
	/**
	 * 是否审计
	 */
	private Integer isAudit;

	/**
	 * 是否重启
	 */
	private Integer isRestart;
	/**
	 * 审计主表ID
	 */
	private Long auditId;

	/**
	 * 通知经销商时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date noticeDealerTime;

	/**
	 * 材料审核时长
	 */
	private String materialAuditLength;

	/**
	 * 材料提交时长
	 */
	private String materialCommitLength;
	/**
	 * 材料提交时间
	 */
	private Date materialCommitTime;

	/**
	 * 无需支持时间
	 */
	private Date unSupportDate;

	/**
	 * 无需支持理由
	 */
	private String unSupportReason;

	/**
	 * 拒绝支持时间
	 */
	private Date refuseSupportDate;

	/**
	 * 拒绝支持理由
	 */
	private String refuseSupportReason;

	/**
	 * 重启时间
	 */
	private Date restartDate;

	/**
	 * 重启理由
	 */
	private String restartReason;

	/**
	 * 最新审核时间
	 */
	private Date newAuditDate;

	/**
	 * 最新审核结果
	 */
	private Integer newAuditResult;
	/**
	 * 最新审核人
	 */
	private String newestAuditor;
	/**
	 * 最新审核意见
	 */
	private String newAuditOpinion;

	/**
	 * 材料提交时长
	 */
	private Integer materialSummit;

	/**
	 * 材料审核时长
	 */
	private Integer materialAudit;

	/**
	 * 审计时间
	 */
	private Date auditTime;

	/**
	 * 通知开票次数
	 */
	private Integer noticeTimes;

	/**
	 * 代金券已通知开票金额
	 */
	private BigDecimal voucherInvoiceAmount;

	/**
	 * 代金券未通知开票金额
	 */
	private BigDecimal voucherNotInvoiceAmount;

	/**
	 * 审批部门
	 */
	private Integer auditPart;

	/**
	 * 区域驳回至经销商次数
	 */
	private Integer areaRejectedTimes;
	/**
	 * CCMQ驳回至区域次数
	 */
	private Integer ccmqRejectedTimes;

	/**
	 * 通知/开票金额
	 */
	private BigDecimal noticeOrInvoicePrice;
	/**
	 * 
	 * 充值金额
	 */
	private BigDecimal rechargeAmount;

	/**
	 * 已消费金额
	 */
	private BigDecimal usedAmount;

	/**
	 * 剩余金额
	 */
	private BigDecimal leftAmount;

	/**
	 * 店端已通知开票金额
	 */
	private BigDecimal noticeInvoicePrice;

	/**
	 * 店端开票金额
	 */
	private BigDecimal invoicePrice;

	/**
	 * 是否CCMQ翻译
	 */
	private Integer isCcmq;

	/**
	 * 审计方式
	 */
	// private Integer auditWay;

	/**
	 * 审计时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date auditTimes;

	/**
	 * 问题点
	 */
	private String troubleSpots;

	/**
	 * 处罚结果
	 */
	private String punishResult;

	/**
	 * 扣款
	 */
	private BigDecimal deductionsPrice;

	/**
	 * 是否通知
	 */
	private Integer isNotification;
	private BigDecimal costRate;
	/**
	 * 消费成本金额
	 */
	@TableField(exist = false)
	private BigDecimal costConsumeAmount;
	/**
	 *代金券充值金额
	 */
	@TableField(exist = false)
	private BigDecimal voucherCouponFaceRechargePrice;

	private  String auditName;

	private  List<String> auditName1;
	private  Long  auditor;

	public BigDecimal getVoucherCouponFaceRechargePrice() {
		return voucherCouponFaceRechargePrice;
	}

	public void setVoucherCouponFaceRechargePrice(BigDecimal voucherCouponFaceRechargePrice) {
		this.voucherCouponFaceRechargePrice = voucherCouponFaceRechargePrice;
	}

	public BigDecimal getCostConsumeAmount() {
		return costConsumeAmount;
	}

	public void setCostConsumeAmount(BigDecimal costConsumeAmount) {
		this.costConsumeAmount = costConsumeAmount;
	}

	public BigDecimal getCostRate() {
		return costRate;
	}

	public void setCostRate(BigDecimal costRate) {
		this.costRate = costRate;
	}

	public Date getAuditTimes() {
		return auditTimes;
	}

	public void setAuditTimes(Date auditTimes) {
		this.auditTimes = auditTimes;
	}

	public String getTroubleSpots() {
		return troubleSpots;
	}

	public void setTroubleSpots(String troubleSpots) {
		this.troubleSpots = troubleSpots;
	}

	public String getPunishResult() {
		return punishResult;
	}

	public void setPunishResult(String punishResult) {
		this.punishResult = punishResult;
	}

	public BigDecimal getDeductionsPrice() {
		return deductionsPrice;
	}

	public void setDeductionsPrice(BigDecimal deductionsPrice) {
		this.deductionsPrice = deductionsPrice;
	}

	public Integer getIsNotification() {
		return isNotification;
	}

	public void setIsNotification(Integer isNotification) {
		this.isNotification = isNotification;
	}

	public Integer getIsCcmq() {
		return isCcmq;
	}

	public void setIsCcmq(Integer isCcmq) {
		this.isCcmq = isCcmq;
	}

	public BigDecimal getNoticeInvoicePrice() {
		return noticeInvoicePrice;
	}

	public void setNoticeInvoicePrice(BigDecimal noticeInvoicePrice) {
		this.noticeInvoicePrice = noticeInvoicePrice;
	}

	public BigDecimal getInvoicePrice() {
		return invoicePrice;
	}

	public void setInvoicePrice(BigDecimal invoicePrice) {
		this.invoicePrice = invoicePrice;
	}

	public BigDecimal getRechargeAmount() {
		return rechargeAmount;
	}

	public void setRechargeAmount(BigDecimal rechargeAmount) {
		this.rechargeAmount = rechargeAmount;
	}

	public BigDecimal getUsedAmount() {
		return usedAmount;
	}

	public void setUsedAmount(BigDecimal usedAmount) {
		this.usedAmount = usedAmount;
	}

	public BigDecimal getLeftAmount() {
		return leftAmount;
	}

	public void setLeftAmount(BigDecimal leftAmount) {
		this.leftAmount = leftAmount;
	}

	public BigDecimal getNoticeOrInvoicePrice() {
		return noticeOrInvoicePrice;
	}

	public void setNoticeOrInvoicePrice(BigDecimal noticeOrInvoicePrice) {
		this.noticeOrInvoicePrice = noticeOrInvoicePrice;
	}

	public Integer getAreaRejectedTimes() {
		return areaRejectedTimes;
	}

	public void setAreaRejectedTimes(Integer areaRejectedTimes) {
		this.areaRejectedTimes = areaRejectedTimes;
	}

	public Integer getCcmqRejectedTimes() {
		return ccmqRejectedTimes;
	}

	public void setCcmqRejectedTimes(Integer ccmqRejectedTimes) {
		this.ccmqRejectedTimes = ccmqRejectedTimes;
	}

	public Integer getAuditPart() {
		return auditPart;
	}

	public void setAuditPart(Integer auditPart) {
		this.auditPart = auditPart;
	}

	public String getNewAuditOpinion() {
		return newAuditOpinion;
	}

	public void setNewAuditOpinion(String newAuditOpinion) {
		this.newAuditOpinion = newAuditOpinion;
	}

	public Long getNoticeInvoiceId() {
		return noticeInvoiceId;
	}

	public void setNoticeInvoiceId(Long noticeInvoiceId) {
		this.noticeInvoiceId = noticeInvoiceId;
	}

	public Long getCouponId() {
		return couponId;
	}

	public void setCouponId(Long couponId) {
		this.couponId = couponId;
	}

	public Integer getNoticeTimes() {
		return noticeTimes;
	}

	public void setNoticeTimes(Integer noticeTimes) {
		this.noticeTimes = noticeTimes;
	}

	public Date getAuditTime() {
		return auditTime;
	}

	public void setAuditTime(Date auditTime) {
		this.auditTime = auditTime;
	}

	public String[] getComplaintFalut1() {
		return complaintFalut1;
	}

	public void setComplaintFalut1(String[] complaintFalut1) {
		this.complaintFalut1 = complaintFalut1;
	}

	public Integer getMaterialSummit() {
		return materialSummit;
	}

	public void setMaterialSummit(Integer materialSummit) {
		this.materialSummit = materialSummit;
	}

	public Integer getMaterialAudit() {
		return materialAudit;
	}

	public void setMaterialAudit(Integer materialAudit) {
		this.materialAudit = materialAudit;
	}

	public Integer getIsRestart() {
		return isRestart;
	}

	public void setIsRestart(Integer isRestart) {
		this.isRestart = isRestart;
	}

	public Date getMaterialCommitTime() {
		return materialCommitTime;
	}

	public void setMaterialCommitTime(Date materialCommitTime) {
		this.materialCommitTime = materialCommitTime;
	}

	public String getNewestAuditor() {
		return newestAuditor;
	}

	public void setNewestAuditor(String newestAuditor) {
		this.newestAuditor = newestAuditor;
	}

	public Date getNewAuditDate() {
		return newAuditDate;
	}

	public void setNewAuditDate(Date newAuditDate) {
		this.newAuditDate = newAuditDate;
	}

	public Integer getNewAuditResult() {
		return newAuditResult;
	}

	public void setNewAuditResult(Integer newAuditResult) {
		this.newAuditResult = newAuditResult;
	}

	public Date getUnSupportDate() {
		return unSupportDate;
	}

	public void setUnSupportDate(Date unSupportDate) {
		this.unSupportDate = unSupportDate;
	}

	public String getUnSupportReason() {
		return unSupportReason;
	}

	public void setUnSupportReason(String unSupportReason) {
		this.unSupportReason = unSupportReason;
	}

	public Date getRefuseSupportDate() {
		return refuseSupportDate;
	}

	public void setRefuseSupportDate(Date refuseSupportDate) {
		this.refuseSupportDate = refuseSupportDate;
	}

	public String getRefuseSupportReason() {
		return refuseSupportReason;
	}

	public void setRefuseSupportReason(String refuseSupportReason) {
		this.refuseSupportReason = refuseSupportReason;
	}

	public Date getRestartDate() {
		return restartDate;
	}

	public void setRestartDate(Date restartDate) {
		this.restartDate = restartDate;
	}

	public String getRestartReason() {
		return restartReason;
	}

	public void setRestartReason(String restartReason) {
		this.restartReason = restartReason;
	}

	public String getMaterialCommitLength() {
		return materialCommitLength;
	}

	public void setMaterialCommitLength(String materialCommitLength) {
		this.materialCommitLength = materialCommitLength;
	}

	public String getMaterialAuditLength() {
		return materialAuditLength;
	}

	public void setMaterialAuditLength(String materialAuditLength) {
		this.materialAuditLength = materialAuditLength;
	}

	public Integer getAuditWay() {
		return auditWay;
	}

	public void setAuditWay(Integer auditWay) {
		this.auditWay = auditWay;
	}

	public Date getAuditStartTime() {
		return auditStartTime;
	}

	public void setAuditStartTime(Date auditStartTime) {
		this.auditStartTime = auditStartTime;
	}

	public Date getAuditEndTime() {
		return auditEndTime;
	}

	public void setAuditEndTime(Date auditEndTime) {
		this.auditEndTime = auditEndTime;
	}

	public Integer getIsAudit() {
		return isAudit;
	}

	public void setIsAudit(Integer isAudit) {
		this.isAudit = isAudit;
	}

	public Integer getAuditResult() {
		return auditResult;
	}

	public void setAuditResult(Integer auditResult) {
		this.auditResult = auditResult;
	}

	public Long getAuditId() {
		return auditId;
	}

	public void setAuditId(Long auditId) {
		this.auditId = auditId;
	}

	public Date getNoticeDealerTime() {
		return noticeDealerTime;
	}

	public void setNoticeDealerTime(Date noticeDealerTime) {
		this.noticeDealerTime = noticeDealerTime;
	}

	public String getAuditCcmq() {
		return auditCcmq;
	}

	public void setAuditCcmq(String auditCcmq) {
		this.auditCcmq = auditCcmq;
	}

	public BigDecimal getIsNeedTranslate() {
		return isNeedTranslate;
	}

	public void setIsNeedTranslate(BigDecimal isNeedTranslate) {
		this.isNeedTranslate = isNeedTranslate;
	}

	public String getAuditRole() {
		return auditRole;
	}

	public void setAuditRole(String auditRole) {
		this.auditRole = auditRole;
	}

	public Integer getDataType() {
		return dataType;
	}

	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}

	public String getOwnerCodes() {
		return ownerCodes;
	}

	public void setOwnerCodes(String ownerCodes) {
		this.ownerCodes = ownerCodes;
	}

	public Date getMaterialPassTime() {
		return materialPassTime;
	}

	public void setMaterialPassTime(Date materialPassTime) {
		this.materialPassTime = materialPassTime;
	}

	public Integer getApplyCount() {
		return applyCount;
	}

	public void setApplyCount(Integer applyCount) {
		this.applyCount = applyCount;
	}

	public Integer getMaterialCount() {
		return materialCount;
	}

	public void setMaterialCount(Integer materialCount) {
		this.materialCount = materialCount;
	}

	public Integer getSmallAreaId() {
		return smallAreaId;
	}

	public void setSmallAreaId(Integer smallAreaId) {
		this.smallAreaId = smallAreaId;
	}

	public String getSmallArea() {
		return smallArea;
	}

	public void setSmallArea(String smallArea) {
		this.smallArea = smallArea;
	}

	public BigDecimal getAuditPrice() {
		return auditPrice;
	}

	public void setAuditPrice(BigDecimal auditPrice) {
		this.auditPrice = auditPrice;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Date getApplyStartTime() {
		return applyStartTime;
	}

	public void setApplyStartTime(Date applyStartTime) {
		this.applyStartTime = applyStartTime;
	}

	public Date getApplyEndTime() {
		return applyEndTime;
	}

	public void setApplyEndTime(Date applyEndTime) {
		this.applyEndTime = applyEndTime;
	}

	public Date getPassStartTime() {
		return passStartTime;
	}

	public void setPassStartTime(Date passStartTime) {
		this.passStartTime = passStartTime;
	}

	public Date getPassEndTime() {
		return passEndTime;
	}

	public void setPassEndTime(Date passEndTime) {
		this.passEndTime = passEndTime;
	}

	public BigDecimal getApplyStartAmount() {
		return applyStartAmount;
	}

	public void setApplyStartAmount(BigDecimal applyStartAmount) {
		this.applyStartAmount = applyStartAmount;
	}

	public BigDecimal getApplyEndAmount() {
		return applyEndAmount;
	}

	public void setApplyEndAmount(BigDecimal applyEndAmount) {
		this.applyEndAmount = applyEndAmount;
	}

	public Date getInvoiceStartDate() {
		return invoiceStartDate;
	}

	public void setInvoiceStartDate(Date invoiceStartDate) {
		this.invoiceStartDate = invoiceStartDate;
	}

	public Date getInvoiceEndDate() {
		return invoiceEndDate;
	}

	public void setInvoiceEndDate(Date invoiceEndDate) {
		this.invoiceEndDate = invoiceEndDate;
	}

	public Date getInvoiceDate() {
		return invoiceDate;
	}

	public void setInvoiceDate(Date invoiceDate) {
		this.invoiceDate = invoiceDate;
	}

	public Date getNoticeInvoiceStartDate() {
		return noticeInvoiceStartDate;
	}

	public void setNoticeInvoiceStartDate(Date noticeInvoiceStartDate) {
		this.noticeInvoiceStartDate = noticeInvoiceStartDate;
	}

	public Date getNoticeInvoiceEndDate() {
		return noticeInvoiceEndDate;
	}

	public void setNoticeInvoiceEndDate(Date noticeInvoiceEndDate) {
		this.noticeInvoiceEndDate = noticeInvoiceEndDate;
	}

	public Date getNoticeInvoiceDate() {
		return noticeInvoiceDate;
	}

	public void setNoticeInvoiceDate(Date noticeInvoiceDate) {
		this.noticeInvoiceDate = noticeInvoiceDate;
	}

	public Date getUpdatedStartAt() {
		return updatedStartAt;
	}

	public void setUpdatedStartAt(Date updatedStartAt) {
		this.updatedStartAt = updatedStartAt;
	}

	public Date getUpdatedEndAt() {
		return updatedEndAt;
	}

	public void setUpdatedEndAt(Date updatedEndAt) {
		this.updatedEndAt = updatedEndAt;
	}

	public Date getPassTime() {
		return passTime;
	}

	public void setPassTime(Date passTime) {
		this.passTime = passTime;
	}

	public Integer getIsMaintainChange() {
		return isMaintainChange;
	}

	public void setIsMaintainChange(Integer isMaintainChange) {
		this.isMaintainChange = isMaintainChange;
	}

	public Integer getIsExtendWarrantyChange() {
		return isExtendWarrantyChange;
	}

	public void setIsExtendWarrantyChange(Integer isExtendWarrantyChange) {
		this.isExtendWarrantyChange = isExtendWarrantyChange;
	}

	public Integer getIsAccessoryChange() {
		return isAccessoryChange;
	}

	public void setIsAccessoryChange(Integer isAccessoryChange) {
		this.isAccessoryChange = isAccessoryChange;
	}

	public List<GoodwillApplyFinalPartInfoDTO> getMaintainCostTable() {
		return maintainCostTable;
	}

	public void setMaintainCostTable(List<GoodwillApplyFinalPartInfoDTO> maintainCostTable) {
		this.maintainCostTable = maintainCostTable;
	}

	public List<GoodwillApplyFinalPartInfoDTO> getExtendWarrantyCostTable() {
		return extendWarrantyCostTable;
	}

	public void setExtendWarrantyCostTable(List<GoodwillApplyFinalPartInfoDTO> extendWarrantyCostTable) {
		this.extendWarrantyCostTable = extendWarrantyCostTable;
	}

	public List<GoodwillApplyFinalPartInfoDTO> getAccessoryCostTable() {
		return accessoryCostTable;
	}

	public void setAccessoryCostTable(List<GoodwillApplyFinalPartInfoDTO> accessoryCostTable) {
		this.accessoryCostTable = accessoryCostTable;
	}

	public String getCustomerBackground() {
		return customerBackground;
	}

	public void setCustomerBackground(String customerBackground) {
		this.customerBackground = customerBackground;
	}

	public String getCustomerBackgroundEn() {
		return customerBackgroundEn;
	}

	public void setCustomerBackgroundEn(String customerBackgroundEn) {
		this.customerBackgroundEn = customerBackgroundEn;
	}

	public String getReasonAndDispose() {
		return reasonAndDispose;
	}

	public void setReasonAndDispose(String reasonAndDispose) {
		this.reasonAndDispose = reasonAndDispose;
	}

	public String getReasonAndDisposeEn() {
		return reasonAndDisposeEn;
	}

	public void setReasonAndDisposeEn(String reasonAndDisposeEn) {
		this.reasonAndDisposeEn = reasonAndDisposeEn;
	}

	public String getRepairSolution() {
		return repairSolution;
	}

	public void setRepairSolution(String repairSolution) {
		this.repairSolution = repairSolution;
	}

	public String getRepairSolutionEn() {
		return repairSolutionEn;
	}

	public void setRepairSolutionEn(String repairSolutionEn) {
		this.repairSolutionEn = repairSolutionEn;
	}

	public String getCustomerRequire() {
		return customerRequire;
	}

	public void setCustomerRequire(String customerRequire) {
		this.customerRequire = customerRequire;
	}

	public String getCustomerRequireEn() {
		return customerRequireEn;
	}

	public void setCustomerRequireEn(String customerRequireEn) {
		this.customerRequireEn = customerRequireEn;
	}

	public String getPotentialRisk() {
		return potentialRisk;
	}

	public void setPotentialRisk(String potentialRisk) {
		this.potentialRisk = potentialRisk;
	}

	public String getPotentialRiskEn() {
		return potentialRiskEn;
	}

	public void setPotentialRiskEn(String potentialRiskEn) {
		this.potentialRiskEn = potentialRiskEn;
	}

	public String getVrOrTjNoEn() {
		return vrOrTjNoEn;
	}

	public void setVrOrTjNoEn(String vrOrTjNoEn) {
		this.vrOrTjNoEn = vrOrTjNoEn;
	}

	public String getVrOrTjNo() {
		return vrOrTjNo;
	}

	public void setVrOrTjNo(String vrOrTjNo) {
		this.vrOrTjNo = vrOrTjNo;
	}

	public String getBusinessGoodwillApplyDetailEn() {
		return businessGoodwillApplyDetailEn;
	}

	public void setBusinessGoodwillApplyDetailEn(String businessGoodwillApplyDetailEn) {
		this.businessGoodwillApplyDetailEn = businessGoodwillApplyDetailEn;
	}

	public String getBusinessGoodwillApplyDetail() {
		return businessGoodwillApplyDetail;
	}

	public void setBusinessGoodwillApplyDetail(String businessGoodwillApplyDetail) {
		this.businessGoodwillApplyDetail = businessGoodwillApplyDetail;
	}

	public List<GoodwillApplyRepairInfoDTO> getGoodwillApplyRepairInfoDTO() {
		return goodwillApplyRepairInfoDTO;
	}

	public void setGoodwillApplyRepairInfoDTO(List<GoodwillApplyRepairInfoDTO> goodwillApplyRepairInfoDTO) {
		this.goodwillApplyRepairInfoDTO = goodwillApplyRepairInfoDTO;
	}

	public List<GoodwillApplyRepairPartInfoDTO> getGoodwillApplyRepairPartInfoDTO() {
		return goodwillApplyRepairPartInfoDTO;
	}

	public void setGoodwillApplyRepairPartInfoDTO(List<GoodwillApplyRepairPartInfoDTO> goodwillApplyRepairPartInfoDTO) {
		this.goodwillApplyRepairPartInfoDTO = goodwillApplyRepairPartInfoDTO;
	}

	public GoodwillApplyInfoDTO() {
		super();
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	public String getOwnerParCode() {
		return ownerParCode;
	}

	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getApplyNo() {
		return applyNo;
	}

	public void setApplyNo(String applyNo) {
		this.applyNo = applyNo;
	}

	public String getDealerCode() {
		return dealerCode;
	}

	public void setDealerCode(String dealerCode) {
		this.dealerCode = dealerCode;
	}

	public Integer getAuditType() {
		return auditType;
	}

	public void setAuditType(Integer auditType) {
		this.auditType = auditType;
	}

	public Integer getGoodwillNature() {
		return goodwillNature;
	}

	public void setGoodwillNature(Integer goodwillNature) {
		this.goodwillNature = goodwillNature;
	}

	public String getComplaintFalut() {
		return complaintFalut;
	}

	public void setComplaintFalut(String complaintFalut) {
		this.complaintFalut = complaintFalut;
	}

	public String getComplaintId() {
		return complaintId;
	}

	public void setComplaintId(String complaintId) {
		this.complaintId = complaintId;
	}

	public Date getComplaintDate() {
		return complaintDate;
	}

	public void setComplaintDate(Date complaintDate) {
		this.complaintDate = complaintDate;
	}

	public Integer getVehicleUse() {
		return vehicleUse;
	}

	public void setVehicleUse(Integer vehicleUse) {
		this.vehicleUse = vehicleUse;
	}

	public String getSalesDealer() {
		return salesDealer;
	}

	public void setSalesDealer(String salesDealer) {
		this.salesDealer = salesDealer;
	}

	public BigDecimal getApplyAmount() {
		return applyAmount;
	}

	public void setApplyAmount(BigDecimal applyAmount) {
		this.applyAmount = applyAmount;
	}

	public BigDecimal getAuditAmount() {
		return auditAmount;
	}

	public void setAuditAmount(BigDecimal auditAmount) {
		this.auditAmount = auditAmount;
	}

	public BigDecimal getSettlementAmount() {
		return settlementAmount;
	}

	public void setSettlementAmount(BigDecimal settlementAmount) {
		this.settlementAmount = settlementAmount;
	}

	public BigDecimal getInvoiceAmount() {
		return invoiceAmount;
	}

	public void setInvoiceAmount(BigDecimal invoiceAmount) {
		this.invoiceAmount = invoiceAmount;
	}

	public Integer getGoodwillStatus() {
		return goodwillStatus;
	}

	public void setGoodwillStatus(Integer goodwillStatus) {
		this.goodwillStatus = goodwillStatus;
	}

	public Integer getLastGoodwillStatus() {
		return lastGoodwillStatus;
	}

	public void setLastGoodwillStatus(Integer lastGoodwillStatus) {
		this.lastGoodwillStatus = lastGoodwillStatus;
	}

	public List<Integer> getCustomerPainVue() {
		return customerPainVue;
	}

	public void setCustomerPainVue(List<Integer> customerPainVue) {
		this.customerPainVue = customerPainVue;
	}

	public String getCustomerPain() {
		return customerPain;
	}

	public void setCustomerPain(String customerPain) {
		this.customerPain = customerPain;
	}

	public String getVin() {
		return vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public String getLicense() {
		return license;
	}

	public void setLicense(String license) {
		this.license = license;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getCustomerMobile() {
		return customerMobile;
	}

	public void setCustomerMobile(String customerMobile) {
		this.customerMobile = customerMobile;
	}

	public String getMileage() {
		return mileage;
	}

	public void setMileage(String mileage) {
		this.mileage = mileage;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public Integer getIsExtendWarranty() {
		return isExtendWarranty;
	}

	public void setIsExtendWarranty(Integer isExtendWarranty) {
		this.isExtendWarranty = isExtendWarranty;
	}

	public String getExtendWarrantyName() {
		return extendWarrantyName;
	}

	public void setExtendWarrantyName(String extendWarrantyName) {
		this.extendWarrantyName = extendWarrantyName;
	}

	public BigDecimal getMaintainCost() {
		return maintainCost;
	}

	public void setMaintainCost(BigDecimal maintainCost) {
		this.maintainCost = maintainCost;
	}

	public BigDecimal getExtendWarrantyCost() {
		return extendWarrantyCost;
	}

	public void setExtendWarrantyCost(BigDecimal extendWarrantyCost) {
		this.extendWarrantyCost = extendWarrantyCost;
	}

	public BigDecimal getAccessoryCost() {
		return accessoryCost;
	}

	public void setAccessoryCost(BigDecimal accessoryCost) {
		this.accessoryCost = accessoryCost;
	}

	public BigDecimal getVoucherCost() {
		return voucherCost;
	}

	public void setVoucherCost(BigDecimal voucherCost) {
		this.voucherCost = voucherCost;
	}

	public BigDecimal getWalkingCarPrice() {
		return walkingCarPrice;
	}

	public void setWalkingCarPrice(BigDecimal walkingCarPrice) {
		this.walkingCarPrice = walkingCarPrice;
	}

	public BigDecimal getVolvoIntegral() {
		return volvoIntegral;
	}

	public void setVolvoIntegral(BigDecimal volvoIntegral) {
		this.volvoIntegral = volvoIntegral;
	}

	public BigDecimal getReturnChangeCarPrice() {
		return returnChangeCarPrice;
	}

	public void setReturnChangeCarPrice(BigDecimal returnChangeCarPrice) {
		this.returnChangeCarPrice = returnChangeCarPrice;
	}

	public BigDecimal getOtherPrice() {
		return otherPrice;
	}

	public void setOtherPrice(BigDecimal otherPrice) {
		this.otherPrice = otherPrice;
	}

	public BigDecimal getCostTotal() {
		return costTotal;
	}

	public void setCostTotal(BigDecimal costTotal) {
		this.costTotal = costTotal;
	}

	public BigDecimal getCustomerPay() {
		return customerPay;
	}

	public void setCustomerPay(BigDecimal customerPay) {
		this.customerPay = customerPay;
	}

	public BigDecimal getDealerUndertake() {
		return dealerUndertake;
	}

	public void setDealerUndertake(BigDecimal dealerUndertake) {
		this.dealerUndertake = dealerUndertake;
	}

	public BigDecimal getVolvoSupportGoodwillAmount() {
		return volvoSupportGoodwillAmount;
	}

	public void setVolvoSupportGoodwillAmount(BigDecimal volvoSupportGoodwillAmount) {
		this.volvoSupportGoodwillAmount = volvoSupportGoodwillAmount;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getApplyFile() {
		return applyFile;
	}

	public void setApplyFile(String applyFile) {
		this.applyFile = applyFile;
	}

	public String getCostStatisticsFile() {
		return costStatisticsFile;
	}

	public void setCostStatisticsFile(String costStatisticsFile) {
		this.costStatisticsFile = costStatisticsFile;
	}

	public String getCostScreenshotFile() {
		return costScreenshotFile;
	}

	public void setCostScreenshotFile(String costScreenshotFile) {
		this.costScreenshotFile = costScreenshotFile;
	}

	public String getRingCheckFile() {
		return ringCheckFile;
	}

	public void setRingCheckFile(String ringCheckFile) {
		this.ringCheckFile = ringCheckFile;
	}

	public String getTroubleRepairRequisitionFile() {
		return troubleRepairRequisitionFile;
	}

	public void setTroubleRepairRequisitionFile(String troubleRepairRequisitionFile) {
		this.troubleRepairRequisitionFile = troubleRepairRequisitionFile;
	}

	public String getWorkOrderFile() {
		return workOrderFile;
	}

	public void setWorkOrderFile(String workOrderFile) {
		this.workOrderFile = workOrderFile;
	}

	public String getSituationSettlementAgreementFile() {
		return situationSettlementAgreementFile;
	}

	public void setSituationSettlementAgreementFile(String situationSettlementAgreementFile) {
		this.situationSettlementAgreementFile = situationSettlementAgreementFile;
	}

	public String getSupplementaryMaterialFile() {
		return supplementaryMaterialFile;
	}

	public void setSupplementaryMaterialFile(String supplementaryMaterialFile) {
		this.supplementaryMaterialFile = supplementaryMaterialFile;
	}

	public String getManagementReviewEmailVpFile() {
		return managementReviewEmailVpFile;
	}

	public void setManagementReviewEmailVpFile(String managementReviewEmailVpFile) {
		this.managementReviewEmailVpFile = managementReviewEmailVpFile;
	}

	public String getManagementReviewEmailCeoFile() {
		return managementReviewEmailCeoFile;
	}

	public void setManagementReviewEmailCeoFile(String managementReviewEmailCeoFile) {
		this.managementReviewEmailCeoFile = managementReviewEmailCeoFile;
	}

	public String getCostUpdateFile() {
		return costUpdateFile;
	}

	public void setCostUpdateFile(String costUpdateFile) {
		this.costUpdateFile = costUpdateFile;
	}

	public String getVcdcElseFile() {
		return vcdcElseFile;
	}

	public void setVcdcElseFile(String vcdcElseFile) {
		this.vcdcElseFile = vcdcElseFile;
	}

	public String getCustomerIdentification() {
		return customerIdentification;
	}

	public void setCustomerIdentification(String customerIdentification) {
		this.customerIdentification = customerIdentification;
	}

	public String getElseFile() {
		return elseFile;
	}

	public void setElseFile(String elseFile) {
		this.elseFile = elseFile;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Date getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(Date applyTime) {
		this.applyTime = applyTime;
	}

	public Date getBuyCarDate() {
		return buyCarDate;
	}

	public void setBuyCarDate(Date buyCarDate) {
		this.buyCarDate = buyCarDate;
	}

	public Date getWarrantyStartDate() {
		return warrantyStartDate;
	}

	public void setWarrantyStartDate(Date warrantyStartDate) {
		this.warrantyStartDate = warrantyStartDate;
	}

	public Date getExtendWarrantyStartDate() {
		return extendWarrantyStartDate;
	}

	public void setExtendWarrantyStartDate(Date extendWarrantyStartDate) {
		this.extendWarrantyStartDate = extendWarrantyStartDate;
	}

	public Date getExtendWarrantyEndDate() {
		return extendWarrantyEndDate;
	}

	public void setExtendWarrantyEndDate(Date extendWarrantyEndDate) {
		this.extendWarrantyEndDate = extendWarrantyEndDate;
	}

	public Date getCommitTime() {
		return commitTime;
	}

	public void setCommitTime(Date commitTime) {
		this.commitTime = commitTime;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public Long getMaterialId() {
		return materialId;
	}

	public void setMaterialId(Long materialId) {
		this.materialId = materialId;
	}

	public String getDealerName() {
		return dealerName;
	}

	public void setDealerName(String dealerName) {
		this.dealerName = dealerName;
	}

	public Integer getAreaManageId() {
		return areaManageId;
	}

	public void setAreaManageId(Integer areaManageId) {
		this.areaManageId = areaManageId;
	}

	public String getAreaManageId1() {
		return areaManageId1;
	}

	public void setAreaManageId1(String areaManageId1) {
		this.areaManageId1 = areaManageId1;
	}

	public String getSmallAreaId1() {
		return smallAreaId1;
	}

	public void setSmallAreaId1(String smallAreaId1) {
		this.smallAreaId1 = smallAreaId1;
	}

	public String getAreaManage() {
		return areaManage;
	}

	public void setAreaManage(String areaManage) {
		this.areaManage = areaManage;
	}

	public Integer getBlocId() {
		return blocId;
	}

	public void setBlocId(Integer blocId) {
		this.blocId = blocId;
	}

	public String getBloc() {
		return bloc;
	}

	public void setBloc(String bloc) {
		this.bloc = bloc;
	}

	public String getApplyPerson() {
		return applyPerson;
	}

	public void setApplyPerson(String applyPerson) {
		this.applyPerson = applyPerson;
	}

	public BigDecimal getVoucherInvoiceAmount() {
		return voucherInvoiceAmount;
	}

	public void setVoucherInvoiceAmount(BigDecimal voucherInvoiceAmount) {
		this.voucherInvoiceAmount = voucherInvoiceAmount;
	}

	public BigDecimal getVoucherNotInvoiceAmount() {
		return voucherNotInvoiceAmount;
	}

	public void setVoucherNotInvoiceAmount(BigDecimal voucherNotInvoiceAmount) {
		this.voucherNotInvoiceAmount = voucherNotInvoiceAmount;
	}

	public String[] getRoleList() {
		return roleList;
	}

	public void setRoleList(String[] roleList) {
		this.roleList = roleList;
	}

	public String[] getRoleList2() {
		return roleList2;
	}

	public void setRoleList2(String[] roleList2) {
		this.roleList2 = roleList2;
	}

	public String getRoleList1() {
		return roleList1;
	}

	public void setRoleList1(String roleList1) {
		this.roleList1 = roleList1;
	}

	public String getUpdatedPerson() {
		return updatedPerson;
	}

	public void setUpdatedPerson(String updatedPerson) {
		this.updatedPerson = updatedPerson;
	}

	public String getOwnerNo() {
		return ownerNo;
	}

	public void setOwnerNo(String ownerNo) {
		this.ownerNo = ownerNo;
	}

	public String getAuditName() {
		return auditName;
	}

	public void setAuditName(String auditName) {
		this.auditName = auditName;
	}

	public List<String> getAuditName1() {
		return auditName1;
	}

	public void setAuditName1(List<String> auditName1) {
		this.auditName1 = auditName1;
	}

	public Long getAuditor() {
		return auditor;
	}

	public void setAuditor(Long auditor) {
		this.auditor = auditor;
	}

	@Override
	public String toString() {
		return "GoodwillApplyInfoDTO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", applyNo=" + applyNo + ", dealerCode="
				+ dealerCode + ", dealerName=" + dealerName + ", bloc=" + bloc + ", blocId=" + blocId
				+ ", areaManageId=" + areaManageId + ", areaManage=" + areaManage + ", auditType=" + auditType
				+ ", goodwillNature=" + goodwillNature + ", applyTime=" + applyTime + ", complaintFalut="
				+ complaintFalut + ", complaintId=" + complaintId + ", complaintDate=" + complaintDate + ", vehicleUse="
				+ vehicleUse + ", salesDealer=" + salesDealer + ", applyAmount=" + applyAmount + ", auditAmount="
				+ auditAmount + ", settlementAmount=" + settlementAmount + ", invoiceAmount=" + invoiceAmount
				+ ", goodwillStatus=" + goodwillStatus + ", customerPain=" + customerPain + ", vin=" + vin
				+ ", license=" + license + ", customerName=" + customerName + ", customerMobile=" + customerMobile
				+ ", mileage=" + mileage + ", model=" + model + ", buyCarDate=" + buyCarDate + ", warrantyStartDate="
				+ warrantyStartDate + ", isExtendWarranty=" + isExtendWarranty + ", extendWarrantyName="
				+ extendWarrantyName + ", extendWarrantyStartDate=" + extendWarrantyStartDate
				+ ", extendWarrantyEndDate=" + extendWarrantyEndDate + ", maintainCost=" + maintainCost
				+ ", extendWarrantyCost=" + extendWarrantyCost + ", accessoryCost=" + accessoryCost + ", voucherCost="
				+ voucherCost + ", walkingCarPrice=" + walkingCarPrice + ", volvoIntegral=" + volvoIntegral
				+ ", returnChangeCarPrice=" + returnChangeCarPrice + ", otherPrice=" + otherPrice + ", costTotal="
				+ costTotal + ", customerPay=" + customerPay + ", dealerUndertake=" + dealerUndertake
				+ ", volvoSupportGoodwillAmount=" + volvoSupportGoodwillAmount + ", remark=" + remark + ", applyFile="
				+ applyFile + ", costStatisticsFile=" + costStatisticsFile + ", costScreenshotFile="
				+ costScreenshotFile + ", ringCheckFile=" + ringCheckFile + ", troubleRepairRequisitionFile="
				+ troubleRepairRequisitionFile + ", workOrderFile=" + workOrderFile
				+ ", situationSettlementAgreementFile=" + situationSettlementAgreementFile
				+ ", supplementaryMaterialFile=" + supplementaryMaterialFile + ", managementReviewEmailVpFile="
				+ managementReviewEmailVpFile + ", managementReviewEmailCeoFile=" + managementReviewEmailCeoFile
				+ ", costUpdateFile=" + costUpdateFile + ", vcdcElseFile=" + vcdcElseFile + ", customerIdentification="
				+ customerIdentification + ", elseFile=" + elseFile + ", commitTime=" + commitTime + ",voucherCouponFaceRechargePrice="+voucherCouponFaceRechargePrice+",costConsumeAmount="+costConsumeAmount+
				 ", isValid="+ isValid +",costRate="+ costRate+", isDeleted=" + isDeleted + ", createdAt=" + createdAt +",auditName="+auditName+",auditor="+auditor+
				", updatedAt=" + updatedAt + "}";
	}

	/**
	 * 将DTO 转换为PO //对某个对象属性进行赋值
	 * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
	 * 
	 * @param poClass
	 *            需要转换的poClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
		return super.transDtoToPo(poClass);
	}

	/**
	 * 将DTO 转换为PO
	 * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
	 * 
	 * @param po
	 *            需要转换的对象
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BasePO> void transDtoToPo(T po) {
		BeanMapperUtil.copyProperties(this, po, "id");
	}

}
