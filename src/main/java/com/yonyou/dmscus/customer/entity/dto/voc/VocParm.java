package com.yonyou.dmscus.customer.entity.dto.voc;

import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataRecordPo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @title: VocParm
 * @projectName dmscus.customer
 * @date 2022/12/821:06
 */
@Data
public class VocParm {
    private VocWarningDataRecordPo po;
    private String dateTime;
    private Integer inm;
    private List<Long> upst6 = new ArrayList<>();
    private List<Long> upsr6  = new ArrayList<>();
    List<Long> upsr12= new ArrayList<>();
}
