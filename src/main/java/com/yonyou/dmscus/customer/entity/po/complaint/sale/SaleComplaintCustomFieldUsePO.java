package com.yonyou.dmscus.customer.entity.po.complaint.sale;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

    import com.yonyou.dmscloud.framework.base.po.BasePO;



    
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;
import java.util.Date;


/**
 * <p>
 * 客户投诉自定义字段使用表 每个人不同对应不同
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
@TableName("tt_sale_complaint_custom_field_use")
public class SaleComplaintCustomFieldUsePO extends BasePO<SaleComplaintCustomFieldUsePO> {

    private static final long serialVersionUID=1L;
    
    /**
     * 系统ID
     */
    @TableField("app_id")
        private String appId;
    
    /**
     * 所有者代码 
     */
    @TableField("owner_code")
        private String ownerCode;
    
    /**
     * 所有者的父组织代码 
     */
    @TableField("owner_par_code")
        private String ownerParCode;
    
    /**
     * 组织ID
     */
    @TableField("org_id")
        private Integer orgId;
        
    /**
     * 主键ID
     */
            @TableId(value = "id", type = IdType.AUTO)
                        private Long id;
    
    /**
     * 自定义表ID
     */
    @TableField("custom_field_id")
        private Long customFieldId;
    
    /**
     * 表名
     */
    @TableField("table_name")
        private String tableName;
    
    /**
     * 字段名
     */
    @TableField("field_name")
        private String fieldName;
    
    /**
     * 字段描述
     */
    @TableField("field_describe")
        private String fieldDescribe;
    
    /**
     * 是否查询 1 是 0 否
     */
    @TableField("is_query")
        private Boolean isQuery;
    
    /**
     * 是否排序 1 是 0 否
     */
    @TableField("is_sort")
        private Integer isSort;
    
    /**
     * 排序类型 存 ASC/DESC
     */
    @TableField("sort_type")
        private String sortType;
    
    /**
     * 使用人
     */
    @TableField("user_id")
        private Long userId;
    
    /**
     * 数据来源 
     */
    @TableField("data_sources")
        private Integer dataSources;
    
    /**
     * 是否删除
     */
    @TableField("is_deleted")
        private Integer isDeleted;
    
    /**
     * 是否有效 
     */
    @TableField("is_valid")
        private Integer isValid;
    
    /**
     * 创建时间
     */
    @TableField("created_at")
        private Date createdAt;
    
    /**
     * 更新时间
     */
    @TableField("updated_at")
        private Date updatedAt;

    public SaleComplaintCustomFieldUsePO(){
        super();
    }

                    
    public String getAppId(){
        return appId;
    }

        public void setAppId(String appId) {
            this.appId = appId;
            }
                    
    public String getOwnerCode(){
        return ownerCode;
    }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
            }
                    
    public String getOwnerParCode(){
        return ownerParCode;
    }

        public void setOwnerParCode(String ownerParCode) {
            this.ownerParCode = ownerParCode;
            }
                    
    public Integer getOrgId(){
        return orgId;
    }

        public void setOrgId(Integer orgId) {
            this.orgId = orgId;
            }
                    
    public Long getId(){
        return id;
    }

        public void setId(Long id) {
            this.id = id;
            }
                    
    public Long getCustomFieldId(){
        return customFieldId;
    }

        public void setCustomFieldId(Long customFieldId) {
            this.customFieldId = customFieldId;
            }
                    
    public String getTableName(){
        return tableName;
    }

        public void setTableName(String tableName) {
            this.tableName = tableName;
            }
                    
    public String getFieldName(){
        return fieldName;
    }

        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
            }
                    
    public String getFieldDescribe(){
        return fieldDescribe;
    }

        public void setFieldDescribe(String fieldDescribe) {
            this.fieldDescribe = fieldDescribe;
            }
                    
    public Boolean getIsQuery(){
        return isQuery;
    }

        public void setIsQuery(Boolean isQuery) {
            this.isQuery = isQuery;
            }
                    
    public Integer getIsSort(){
        return isSort;
    }

        public void setIsSort(Integer isSort) {
            this.isSort = isSort;
            }
                    
    public String getSortType(){
        return sortType;
    }

        public void setSortType(String sortType) {
            this.sortType = sortType;
            }
                    
    public Long getUserId(){
        return userId;
    }

        public void setUserId(Long userId) {
            this.userId = userId;
            }
                    
    public Integer getDataSources(){
        return dataSources;
    }

        public void setDataSources(Integer dataSources) {
            this.dataSources = dataSources;
            }
                    
    public Integer getIsDeleted(){
        return isDeleted;
    }

        public void setIsDeleted(Integer isDeleted) {
            this.isDeleted = isDeleted;
            }
                    
    public Integer getIsValid(){
        return isValid;
    }

        public void setIsValid(Integer isValid) {
            this.isValid = isValid;
            }
                    
    public Date getCreatedAt(){
        return createdAt;
    }

        public void setCreatedAt(Date createdAt) {
            this.createdAt = createdAt;
            }
                    
    public Date getUpdatedAt(){
        return updatedAt;
    }

        public void setUpdatedAt(Date updatedAt) {
            this.updatedAt = updatedAt;
            }
    
    @Override
    protected Serializable pkVal(){
            return this.id;
        }

@Override
    public String toString(){
        return"SaleComplaintCustomFieldUsePO{" +
                            "appId=" + appId +
                                    ", ownerCode=" + ownerCode +
                                    ", ownerParCode=" + ownerParCode +
                                    ", orgId=" + orgId +
                                    ", id=" + id +
                                    ", customFieldId=" + customFieldId +
                                    ", tableName=" + tableName +
                                    ", fieldName=" + fieldName +
                                    ", fieldDescribe=" + fieldDescribe +
                                    ", isQuery=" + isQuery +
                                    ", isSort=" + isSort +
                                    ", sortType=" + sortType +
                                    ", userId=" + userId +
                                    ", dataSources=" + dataSources +
                                    ", isDeleted=" + isDeleted +
                                    ", isValid=" + isValid +
                                    ", createdAt=" + createdAt +
                                    ", updatedAt=" + updatedAt +
                    "}";
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"id");
    }

}
