package com.yonyou.dmscus.customer.entity.dto.goodwill;

import java.io.Serializable;

@SuppressWarnings("serial")
public class companySelectDTO implements Serializable {
	private String afterBigArea;
	private String afterSmallArea;
	private String cityId;
	private String companyCode;
	private String companyId;
	private String companyNameCn;
	private String companyType;
	private String countyId;
	private String provinceId;
	private String salesBigArea;
	private String salesSmallArea;
	private Integer dealerType;

	public Integer getDealerType() {
		return dealerType;
	}

	public void setDealerType(Integer dealerType) {
		this.dealerType = dealerType;
	}

	public String getAfterBigArea() {
		return afterBigArea;
	}

	public void setAfterBigArea(String afterBigArea) {
		this.afterBigArea = afterBigArea;
	}

	public String getAfterSmallArea() {
		return afterSmallArea;
	}

	public void setAfterSmallArea(String afterSmallArea) {
		this.afterSmallArea = afterSmallArea;
	}

	public String getCityId() {
		return cityId;
	}

	public void setCityId(String cityId) {
		this.cityId = cityId;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getCompanyId() {
		return companyId;
	}

	public void setCompanyId(String companyId) {
		this.companyId = companyId;
	}

	public String getCompanyNameCn() {
		return companyNameCn;
	}

	public void setCompanyNameCn(String companyNameCn) {
		this.companyNameCn = companyNameCn;
	}

	public String getCompanyType() {
		return companyType;
	}

	public void setCompanyType(String companyType) {
		this.companyType = companyType;
	}

	public String getCountyId() {
		return countyId;
	}

	public void setCountyId(String countyId) {
		this.countyId = countyId;
	}

	public String getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(String provinceId) {
		this.provinceId = provinceId;
	}

	public String getSalesBigArea() {
		return salesBigArea;
	}

	public void setSalesBigArea(String salesBigArea) {
		this.salesBigArea = salesBigArea;
	}

	public String getSalesSmallArea() {
		return salesSmallArea;
	}

	public void setSalesSmallArea(String salesSmallArea) {
		this.salesSmallArea = salesSmallArea;
	}

}
