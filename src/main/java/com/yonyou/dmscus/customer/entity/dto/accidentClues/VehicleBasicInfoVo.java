package com.yonyou.dmscus.customer.entity.dto.accidentClues;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.mybatis.plugin.sensitive.SensitiveInfo;
import com.yonyou.mybatis.plugin.sensitive.SensitiveType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


@Data
@ApiModel("车辆基本信息Vo")
public class VehicleBasicInfoVo {
    /**
     * 车辆表主键
     */
    @ApiModelProperty(value = "车辆表主键",name = "id")
    private Integer id;

    @ApiModelProperty(value = "欠款金额",name = "arrearageAmount")
    private Double arrearageAmount;

    @ApiModelProperty(value = "当前行驶里程",name = "currentMileage")
    private Double currentMileage;

    @ApiModelProperty(value = "当前里程日期",name = "currentMileageDate")
    private Date currentMileageDate;

    @ApiModelProperty(value = "下次保养里程",name = "nextMaintainMileage")
    private Double nextMaintainMileage;

    @ApiModelProperty(value = "预计下次保养日期",name = "nextMaintainDate")
    private Date nextMaintainDate;

    @ApiModelProperty(value = "上次维修日期",name = "lastMaintainDate")
    private Date lastMaintainDate;

    @ApiModelProperty(value = "上次保养里程",name = "lastMaintenanceMileage")
    private Double lastMaintenanceMileage;

    @ApiModelProperty(value = "上次保养日期",name = "lastMaintenanceDate")
    private Date lastMaintenanceDate;

    @ApiModelProperty(value = "第一次进厂日期 ",name = "firstInDate")
    private Date firstInDate;
    /**
     * 行驶里程
     */
    @ApiModelProperty(value = "行驶里程",name = "MILEAGE")
    private Double mileage;

    /**
     * 日平均行驶里程
     */
    @ApiModelProperty(value = "日平均行驶里程",name = "dailyAverageMileage")
    private Double dailyAverageMileage;

    @ApiModelProperty(value = "是否沃世界绑定(是否)",name = "isWorldBingding")
    private Integer isWorldBingding;

    /**
     * 出厂日期
     */
    @ApiModelProperty(value = "出厂日期",name = "factoryDate")
    private Date factoryDate;

    /**
     * 底盘编号
     */
    @ApiModelProperty(value = "底盘编号",name = "chassisNumberNo")
    private String chassisNumberNo;

    /**
     * 日平均行驶里程
     */
    @ApiModelProperty(value = "上次保养里程 ",name = "lastMaintainMileage")
    private Double lastMaintainMileage;

    /**
     * 车主编号
     */
    @ApiModelProperty(value = "客户编号",name = "ownerNo")   //必须存储车主ownerNo
    private String ownerNo;


    /**
     * 车辆唯一识别代码
     */
    @ApiModelProperty(value = "VIN",name = "vin")
    private String vin;

    /**
     * 车辆唯一识别代码
     */
    @ApiModelProperty(value = "VINforShow",name = "vinForShow")
    @SensitiveInfo(SensitiveType.VIN)
    private String vinForShow;


    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号" ,name = "license")
    private String license;

    /**
     * 发动机号
     */
    @ApiModelProperty(value = "发动机号",name = "engineNo")
    private String engineNo;

    /**
     * 发动机号
     */
    @ApiModelProperty(value = "发动机代码",name = "engineCode")
    private String engineCode;

    /**
     * 电机号1
     */
    @ApiModelProperty(value = "电机号1",name = "motorNo1")
    private String motorNo1;

    /**
     * 电机号2
     */
    @ApiModelProperty(value = "电机号2",name = "motorNo2")
    private String motorNo2;

    /**
     * 动力形式(燃油车、新能源)
     */
    @ApiModelProperty(value = "动力形式",name = "dynamicCode")
    private Integer dynamicCode;

    /**
     * 品牌  厂牌
     */
    @ApiModelProperty(value = "品牌", name = "brand")
    private String brand;

    /**
     * 车系
     */
    @ApiModelProperty(value = "车系" , name = "series")
    private String series;

    /**
     * 车型
     */
    @ApiModelProperty(value = "车型" ,name = "model")
    private String model;

    /**
     * 车型
     */
    @ApiModelProperty(value = "排气量" ,name = "exhaustQuantity")
    private String exhaustQuantity;

    /**
     * 车型名称
     */
    @ApiModelProperty(value = "车型名称" ,name = "modelName")
    private String modelName;


    /**
     * 车型年款
     */
    @ApiModelProperty(value = "车款" ,name = "modelYear")
    private String modelYear;

    /**
     * 年款
     */
    @ApiModelProperty(value = "年款" ,name = "yearModel")
    private String yearModel;

    /**
     * 变速箱箱号
     */
    @ApiModelProperty(value = "变速箱" ,name = "gearBox")
    private String gearBox;


    /**
     * 建档日期
     */
    @ApiModelProperty(value = "年款" ,name = "foundDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date foundDate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改日期" ,name = "systemUpdateDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date systemUpdateDate;

    /**
     * 保修结束日期
     */
    @ApiModelProperty(value = "保修结束日期" ,name = "wrtEndDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date wrtEndDate;

    /**
     * 保险结束日期
     */
    @ApiModelProperty(value = "保险结束日期" ,name = "insuranceEndDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date insuranceEndDate;



    /**
     * 经销商
     */
    @ApiModelProperty(value = "销售经销商" ,name = "salesAgentName")
    private String salesAgentName;

    /**
     * 服务专员
     */
    @ApiModelProperty(value = "上次SA" ,name = "roServiceAdvisor")   //tt_reapir_order表 serviceAdvisor
    private String roServiceAdvisor;


    /**
     * 水货车(是 否)1004
     */
    @ApiModelProperty(value = "水货车" ,name = "smuggledGoodsVehicle")
    private Integer smuggledGoodsVehicle;

    /**
     * 选装(销售主数据)
     */
    @ApiModelProperty(value = "选装" ,name = "optionPackag")       //重新添加字段
    private String optionPackag;


    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人" ,name = "updatedBy")
    private String updatedBy;

    /**
     * 配置
     */
    @ApiModelProperty(value = "配置",name = "apackage" )
    private String apackage;

    /**
     * 配置代码
     */
    @ApiModelProperty(value = "配置",name = "apackageCode" )
    private String apackageCode;

    /**
     * 车型代码
     */
    @ApiModelProperty(value = "车型代码",name = "modelCode" )
    private String modelCode;

    /**
     * MODEL_CODE的前3位字符
     */
    @ApiModelProperty(value = "MODEL_CODE的前3位字符",name = "seriesCode" )
    private String seriesCode;

    /**
     * 品牌代码
     */
    @ApiModelProperty(value = "品牌代码",name = "brandCode" )
    private String brandCode;

    /**
     * 销售日期
     */
    @ApiModelProperty(value = "销售日期",name = "salesDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date salesDate;

    /**
     * 车辆性质
     */
    @ApiModelProperty(value = "车辆性质",name = "businessKind")
    private Integer businessKind;



    /**
     * 车辆用途
     */
    @ApiModelProperty(value = "车辆用途",name = "vehiclePurpose")
    private Integer vehiclePurpose;

    /**
     * 外饰色
     */
    @ApiModelProperty(value = "外饰色",name = "color")
    private String color;

    /**
     * 内饰色
     */
    @ApiModelProperty(value = "内饰色",name = "innerColor")
    private String innerColor;


    /**
     * 燃油类别
     */
    @ApiModelProperty(value = "燃料类别",name = "fuelType")
    private Integer fuelType;

    /**
     * 排量
     */
    @ApiModelProperty(value = "排量",name = "engineDesc")
    private String engineDesc;

    /**
     * 排放标准
     */
    @ApiModelProperty(value = "排放标准",name = "dischargeStandard")
    private Integer dischargeStandard;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期",name = "productDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date productDate;

    /**
     * 钥匙号
     */
    @ApiModelProperty(value = "钥匙号",name = "keyNumber")
    private String keyNumber;

    /**
     * 产地
     */
    @ApiModelProperty(value = "产地",name = "productingArea")
    private String productingArea;

    /**
     * 厂牌型号
     */
    @ApiModelProperty(value = "厂牌型号",name = "brandModel")
    private String brandModel;



    /**
     * 行驶证号
     */
    @ApiModelProperty(value = "行驶证号",name = "drivingLicense")
    private String drivingLicense;


    /**
     * 销售顾问
     */
    @ApiModelProperty(value = "销售顾问",name = "consultant")
    private String consultant;

    /**
     * 是否本公司购车
     */
    @ApiModelProperty(value = "本经销商购车",name = "isSelfCompany")
    private Integer isSelfCompany;




    /**
     * 上牌日期
     */
    @ApiModelProperty(value = "上牌日期",name = "licenseDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date licenseDate;

    /**
     * 指定技师
     */
    @ApiModelProperty(value = "指定技师",name = "chiefTechnician")
    private String chiefTechnician;

    /**
     * 服务专员
     */
    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor")
    private String serviceAdvisor;

    /**
     * 续保专员
     */
    @ApiModelProperty(value = "续保专员",name = "insuranceAdvisor")
    private String insuranceAdvisor;

    /**
     * 定保专员
     */
    @ApiModelProperty(value = "定保专员",name = "maintainAdvisor")
    private String maintainAdvisor;

    /**
     * 客服专员
     */
    @ApiModelProperty(value = "客服专员",name = "dcrcAdvisor")
    private String dcrcAdvisor;


    /**
     * 优惠截止日期
     */
    @ApiModelProperty(value = "优惠截止日期",name = "discountExpireDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date discountExpireDate;

    /**
     * 优惠开始日期
     */
    @ApiModelProperty(value = "优惠开始日期",name = "discountBeginDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date discountBeginDate;

    /**
     * 优惠模式代码
     */
    @ApiModelProperty(value = "优惠模式",name = "discountModeCode")
    private String discountModeCode;


    /**
     * 购买方式
     */
    @ApiModelProperty(value = "购买方式",name = "waysToBuy")
    private Integer waysToBuy;

    /**
     * 置换意向车型
     */
    @ApiModelProperty(value = "置换意向车型",name = "replaceIntentModel")
    private String replaceIntentModel;

    /**
     * 置换日期
     */
    @ApiModelProperty(value = "置换日期",name = "replaceDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date replaceDate;


    /**
     * 重购日期
     */
    @ApiModelProperty(value = "置换日期",name = "rebuyDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date rebuyDate;

    /**
     * 提车日期
     */
    @ApiModelProperty(value = "提车日期",name = "vehicleDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date vehicleDate;

    /**
     * 营运信息
     */
    @ApiModelProperty(value = "营运信息",name = "operateMessage")
    private String operateMessage;

    /**
     * 开票日期
     */
    @ApiModelProperty(value = "开票日期",name = "invoiceDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date invoiceDate;


    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否激活",name = "isValid")
    private Integer isValid;

    /**
     * 车龄
     */
    @ApiModelProperty(value = "车龄",name = "autoAge")
    private Integer autoAge;
}
