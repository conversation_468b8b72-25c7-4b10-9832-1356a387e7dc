package com.yonyou.dmscus.customer.entity.po.httpLog;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/24 0024
 */
@Data
@TableName("tm_http_ai_log")
public class HttpLogAiPO extends BasePO<HttpLogAiPO> {

    @TableField("log_id")
    private String logId;

    @TableField("description")
    private String description;   //接口描述

    @TableField("dealer_code")
    private String dealerCode;  //经销商代码

    @TableField("request_url")
    private String requestUrl;  //请求url

    @TableField("request_param")
    private String requestParam;  //请求参数

    @TableField("request_type")
    private String requestType; //请求类型

    @TableField("response_code")
    private String responseCode;  //响应代码

    @TableField("response_msg")
    private String responseMsg;  //响应结果
    @TableField("session_id")
    private String sessionId;  //响应结果

}
