package com.yonyou.dmscus.customer.entity.dto.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 车辆资料
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-08
 */
@Data
public class VehicleDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统id
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织id
     */
    private Integer orgId;

    /**
     * 主键
     */
    private Long id;

    /**
     * vin
     */
    private String vin;

    /**
     * 车主编号
     */
    private String ownerNo;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 原车主编号
     */
    private String ownerNoOld;

    /**
     * 车牌号
     */
    private String license;

    /**
     * 发动机号
     */
    private String engineNo;

    /**
     * 变速箱箱号
     */
    private String gearBox;

    /**
     * 出厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date factoryDate;

    /**
     * 建档日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date foundDate;

    /**
     * 内饰颜色
     */
    private String innerColor;

    /**
     * 内饰
     */
    private String innerId;

    /**
     * 厂牌(品牌)
     */
    private String brand;

    /**
     * 车系
     */
    private String series;

    /**
     * 车型
     */
    private String model;

    /**
     * 颜色
     */
    private String color;

    /**
     * 配置（车款）
     */
    private String apackage;

    /**
     * 车型年款
     */
    private String modelYear;

    /**
     * 排气量
     */
    private String exhaustQuantity;

    /**
     * 制造日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productDate;

    /**
     * 排挡类别
     */
    private String shiftType;

    /**
     * 燃料类别
     */
    private Integer fuelType;

    /**
     * 车辆用途
     */
    private Integer vehiclePurpose;

    /**
     * 营运性质
     */
    private Integer businessKind;

    /**
     * 营运证日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date businessDate;

    /**
     * 旧发动机号
     */
    private String engineNoOld;

    /**
     * 上次维修经销商
     */
    private String lastRepairDealer;

    /**
     * 更换发动机事项说明
     */
    private String changeEngineDesc;

    /**
     * 销售经销商
     */
    private String salesAgentName;

    /**
     * 销售顾问
     */
    private String consultant;

    /**
     * 是否允许邀约
     */
    private Integer isAllowInvitation;

    /**
     * 是否本公司购车
     */
    private Integer isSelfCompany;

    /**
     * 销售日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date salesDate;

    /**
     * 销售里程
     */
    private BigDecimal salesMileage;

    /**
     * 车辆价格
     */
    private BigDecimal vehiclePrice;

    /**
     * 保修起始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date wrtBeginDate;

    /**
     * 保修结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date wrtEndDate;

    /**
     * 保修起始里程
     */
    private BigDecimal wrtBeginMileage;

    /**
     * 保修结束里程
     */
    private BigDecimal wrtEndMileage;

    /**
     * 上牌日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date licenseDate;

    /**
     * 行驶里程
     */
    private BigDecimal mileage;

    /**
     * 是否换表
     */
    private Integer isChangeOdograph;

    /**
     * 累计换表里程
     */
    private BigDecimal totalChangeMileage;

    /**
     * 上次换表日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changeDate;

    /**
     * 加装说明
     */
    private String addEquipment;

    /**
     * 首次进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstInDate;

    /**
     * 预计下次保养日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date nextMaintainDate;

    /**
     * 下次保养里程
     */
    private BigDecimal nextMaintainMileage;

    /**
     * 日平均行驶里程
     */
    private BigDecimal dailyAverageMileage;

    /**
     * 上次验车日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastInspectDate;

    /**
     * 下次验车日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date nextInspectDate;

    /**
     * 保险到期日
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expiredDate;

    /**
     * 送修人
     */
    private String deliverer;

    /**
     * 送修人性别
     */
    private Integer delivererGender;

    /**
     * 送修人电话
     */
    private String delivererPhone;

    /**
     * 送修人手机
     */
    private String delivererMobile;

    /**
     * 送修人喜欢联络方式
     */
    private Integer delivererHobbyContact;

    /**
     * 送修人与车主关系
     */
    private String delivererRelationToOwner;

    /**
     * 送修人工作单位
     */
    private String delivererCompany;

    /**
     * 送修人身份证
     */
    private String delivererCredit;

    /**
     * 送修人地址
     */
    private String delivererAddress;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 指定技师
     */
    private String chiefTechnician;

    /**
     * 服务专员
     */
    private String serviceAdvisor;

    /**
     * 续保专员
     */
    private String insuranceAdvisor;

    /**
     * 定保专员
     */
    private String maintainAdvisor;

    /**
     * 专属服务顾问姓名
     */
    private String exclusiveServiceConsultant;

    /**
     * 上次SA
     */
    private String lastSa;

    /**
     * 上次维修日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date lastMaintainDate;

    /**
     * 上次维修里程
     */
    private BigDecimal lastMaintainMileage;

    /**
     * 上次保养日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastMaintenanceDate;

    /**
     * 上次保养里程
     */
    private BigDecimal lastMaintenanceMileage;

    /**
     * 优惠模式代码
     */
    private String discountModeCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 上报日期（最近维修日）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitTime;

    /**
     * 变速箱型式
     */
    private String gearType;

    /**
     * 年款
     */
    private String yearModel;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产地
     */
    private String productingArea;

    /**
     * 工单开单时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date roCreateDate;

    /**
     * 是否dcrc专员
     */
    private Integer isDcrcAdvisor;

    /**
     * dcrc专员
     */
    private String dcrcAdvisor;

    /**
     * 车辆配置代码
     */
    private String vsn;

    /**
     * 排放标准
     */
    private Integer dischargeStandard;

    /**
     * 系统日志
     */
    private String systemRemark;

    /**
     * 修改前上次保养日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date systemLastMaintenanceDate;

    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date systemUpdateDate;

    /**
     * 区县
     */
    private Integer district;

    /**
     * 城市
     */
    private Integer city;

    /**
     * 省份
     */
    private Integer province;

    /**
     * 当前行驶里程
     */
    private BigDecimal currentMileage;

    /**
     * 当前里程日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date currentMileageDate;

    /**
     * 购买方式
     */
    private Integer waysToBuy;

    /**
     * 车辆类别
     */
    private Integer vehicleCategory;

    /**
     * 发票号
     */
    private String vInvoiceNo;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 临近保养时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approachMaintenanceTime;

    /**
     * 开票日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date invoiceDate;

    /**
     * 企业性质
     */
    private Integer commpanyProperty;
    private String unitName;

    /**
     * bzd码
     */
    private String bzd;

    /**
     * 电子首保卡号
     */
    private String efwCard;

    /**
     * jd标识
     */
    private Integer jdIdentification;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 行驶证号
     */
    private String drivingLicense;

    /**
     * 选装(销售主数据)
     */
    private String optionPackag;

    /**
     * 水货车(是 否)1004
     */
    private Integer smuggledGoodsVehicle;

    /**
     * 动力形式(燃油车、新能源)
     */
    private Integer dynamicCode;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
    private Integer version;

    /**
     * 首次进厂经销商编码
     */
    private String firstInDealer;

    /**
     * OneID
     */
    private Long oneId;

    /** 是否大客户代码 : (是:10041001,否:10041002) */
    private Integer fleetCode;


    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
