package com.yonyou.dmscus.customer.entity.dto.clue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * description 故障灯信息参数
 *
 * <AUTHOR>
 * @date 2023/8/21 16:49
 */
@Data
@ApiModel(description = "故障灯信息参数")
public class WarningInfoDTO {
    /**
     * 故障id
     */
    @ApiModelProperty(value = "故障id", required = true, example = "1")
    private String warningId;
    /**
     * 故障名
     */
    @ApiModelProperty(value = "故障名", required = true, example = "雨刷器损坏")
    private String warningName;
    /**
     * 故障描述（英文）
     */
    @ApiModelProperty(value = "故障描述（英文", required = true, example = "雨刷器损坏")
    private String warningEN;
    /**
     * 故障描述（中文）
     */
    @ApiModelProperty(value = "故障描述（中文）", required = true, example = "Damaged wiper")
    private String warningCN;
    /**
     * 故障等级
     */
    @ApiModelProperty(value = "故障等级", required = true, example = "雨刷器损坏")
    private String warningPriority;
    /**
     * 故障发生时间
     */
    @ApiModelProperty(value = "故障发生时间", required = true, example = "2023-04-20 10:10:10")
    private String warningTime;
    /**
     * 故障发生省份（中文）
     */
    @ApiModelProperty(value = "故障发生省份（中文）", required = true, example = "上海市")
    private String warningProvinceCN;
    /**
     * 故障发生省份（province_id）
     */
    @ApiModelProperty(value = "故障发生省份", required = true, example = "1000")
    private String warningProvinceId;
    /**
     * 故障发生城市（中文）
     */
    @ApiModelProperty(value = "故障发生城市（中文）", required = true, example = "上海")
    private String warningCityCN;
    /**
     * 故障发生城市（city_id）
     */
    @ApiModelProperty(value = "故障发生城市（city_id）", required = true, example = "1111")
    private String warningCityId;
    /**
     * 中文故障分类
     */
    @ApiModelProperty(value = "中文故障分类", required = true, example = "1")
    private String wariningCategory;
    /**
     * 是否推荐经销商
     */
    @ApiModelProperty(value = "是否推荐经销商", required = true, example = "true")
    private boolean recommendDealerFlag;
}
