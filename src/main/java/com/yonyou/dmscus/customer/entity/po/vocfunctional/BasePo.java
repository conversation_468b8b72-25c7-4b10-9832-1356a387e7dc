package com.yonyou.dmscus.customer.entity.po.vocfunctional;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: BasePo
 * @projectName dmscus.customer
 * @date 2022/12/911:11
 */
@Data
public class BasePo {

    //主键ID
    @TableId("id")
    @ApiModelProperty(value = "主键",notes = "主键",required = true,name = "id",example = "1",dataType = "Long")
    private Long id;


    //是否删除，1：删除，0：未删除"
    @TableField("is_deleted")
    @ApiModelProperty(value = "是否删除",notes = "是否删除:1,删除；0,未删除",required = true,name = "is_deleted",example = "0",dataType = "Integer")
    private Integer isDeleted;


    //创建时间"
    @TableField("created_at")
    @ApiModelProperty(value = "数据创建时间",notes = "数据创建时间",required = true,name = "created_at",dataType = "Date")
    private Date createdAt;

    //创建人"
    @TableField("created_by")
    @ApiModelProperty(value = "数据创建人",notes = "数据创建人",required = true,name = "created_by",example = "1",dataType = "String")
    private String createdBy;

    //更新时间"
    @TableField("updated_at")
    @ApiModelProperty(value = "数据修改人",notes = "数据修改人",required = true,name = "updated_at",dataType = "Date")
    private Date updatedAt;

    //更新人
    @TableField("updated_by")
    @ApiModelProperty(value = "数据修改时间",notes = "数据修改时间",required = true,name = "updated_by",example = "2",dataType = "String")
    private String updatedBy;

    //版本号（乐观锁）
    @TableField("version")
    @ApiModelProperty(value = "版本号",notes = "版本号",required = true,name = "version",example = "1",dataType = "Integer")
    private Integer version;

    //创建人"
    @TableField("create_sqlby")
    @ApiModelProperty(value = "数据创建人",notes = "数据创建人",required = true,name = "create_sqlby",example = "1",dataType = "String")
    private String createSqlby;

    //更新人
    @TableField("update_sqlby")
    @ApiModelProperty(value = "数据修改时间",notes = "数据修改时间",required = true,name = "update_sqlby",example = "2",dataType = "String")
    private String updateSqlby;
}
