package com.yonyou.dmscus.customer.entity.dto.common;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscus.customer.middleInterface.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "输出用户和职位信息", description = "输出用户和职位信息")
public class UserPositionOutDTO extends BaseDTO {

	private static final long serialVersionUID = Constant.SERIAL_VERSION_UID;

	@ApiModelProperty(value = "用户id", position = 1)
    private Long userId;

	@ApiModelProperty(value = "职位id", position = 2)
    private Long userOrgId;

	@ApiModelProperty(value = "员工id", position = 3)
    private Long empId;

	@ApiModelProperty("组织id")
    private Long orgId;

	@ApiModelProperty("组织名称")
    private String orgName;

	@ApiModelProperty("员工姓名")
    private String employeeName;

	@ApiModelProperty("在职状态 10081001:在职  , 10081002:离职")
    private Integer isOnjob;

	@ApiModelProperty("用户状态 10031001:启用 , 10031002:禁用")
    private Integer userStatus;

	@ApiModelProperty("角色代码")
    private String roleCodes;

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getUserOrgId() {
		return userOrgId;
	}

	public void setUserOrgId(Long userOrgId) {
		this.userOrgId = userOrgId;
	}

	public Long getEmpId() {
		return empId;
	}

	public void setEmpId(Long empId) {
		this.empId = empId;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public Integer getIsOnjob() {
		return isOnjob;
	}

	public void setIsOnjob(Integer isOnjob) {
		this.isOnjob = isOnjob;
	}

	public Integer getUserStatus() {
		return userStatus;
	}

	public void setUserStatus(Integer userStatus) {
		this.userStatus = userStatus;
	}

	public String getRoleCodes() {
		return roleCodes;
	}

	public void setRoleCodes(String roleCodes) {
		this.roleCodes = roleCodes;
	}

	@Override
	public String toString() {
		return "UserPositionOutDTO [userId=" + userId + ", userOrgId=" + userOrgId + ", empId=" + empId + ", orgId="
				+ orgId + ", orgName=" + orgName + ", employeeName=" + employeeName + ", isOnjob=" + isOnjob
				+ ", userStatus=" + userStatus + ", roleCodes=" + roleCodes + "]";
	}



}
