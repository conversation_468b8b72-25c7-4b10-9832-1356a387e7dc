package com.yonyou.dmscus.customer.entity.po.invitationFollow;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 车辆邀约记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
@Data
@TableName("tt_invite_vehicle_record")
public class InviteVehicleRecordPO extends BasePO<InviteVehicleRecordPO> {

    private static final long serialVersionUID = 1L;


    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;


    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 邀约ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 邀约父类ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 是否主要线索:1主要线索、0附属线索
     */
    @TableField("is_main")
    private Integer isMain;

    /**
     * 邀约来源类型：1VCDC下发邀约、2经销商自建邀约、3VOC事故邀约，4voc
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 车架号
     */
    @TableField("vin")
    private String vin;

    /**
     * 车牌号
     */
    @TableField("license_plate_num")
    private String licensePlateNum;

    /**
     * 车主姓名
     */
    @TableField("name")
    private String name;

    /**
     * 车主电话
     */
    @TableField("tel")
    private String tel;

    /**
     * 邀约类型
     */
    @TableField("invite_type")
    private Integer inviteType;

    /**
     * 建议进厂日期
     */
    @TableField("advise_in_date")
    private Date adviseInDate;

    /**
     * 新建议进厂日期
     */
    @TableField("new_advise_in_date")
    private Date newAdviseInDate;

    /**
     * 最新建议进厂日期
     */
    @TableField("newest_advise_in_date")
    private Date newestAdviseInDate;

    /**
     * 计划跟进日期
     */
    @TableField(value ="plan_follow_date",updateStrategy = FieldStrategy.IGNORED,jdbcType = JdbcType.DATE)
    private Date planFollowDate;



    /**
     * 实际跟进日期
     */
    @TableField("actual_follow_date")
    private Date actualFollowDate;

    /**
     * 计划提醒日期
     */
    @TableField("plan_remind_date")
    private Date planRemindDate;

    /**
     * 实际提醒日期
     */
    @TableField("actual_remind_date")
    private Date actualRemindDate;


    /**
     * 首次跟进时间
     */
    @TableField("first_follow_date")
    private Date firstFollowDate;


    /**
     * 线索完成时间
     */
    @TableField("order_finish_date")
    private Date orderFinishDate;


    /**
     * 跟进服务顾问ID
     */
    @TableField(value = "SA_ID",updateStrategy = FieldStrategy.IGNORED,jdbcType = JdbcType.VARCHAR)
    private String saId;

    /**
     * 跟进服务顾问姓名
     */
    @TableField(value = "SA_NAME",updateStrategy = FieldStrategy.IGNORED,jdbcType = JdbcType.VARCHAR)
    private String saName;

    /**
     * 上次跟进服务顾问ID
     */
    @TableField("LAST_SA_ID")
    private String lastSaId;

    /**
     * 上次跟进服务顾问姓名
     */
    @TableField("LAST_SA_NAME")
    private String lastSaName;

    /**
     * 跟进状态
     */
    @TableField("follow_status")
    private Integer followStatus;

    /**
     * 是否预约单：1 是，0 否
     */
    @TableField("is_book")
    private Integer isBook;

    /**
     * 预约单号
     */
    @TableField("book_no")
    private String bookNo;

    /**
     * 线索完成状态
     */
    @TableField("order_status")
    private Integer orderStatus;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;

    /**
     * 经销商代码
     */
    @TableField("dealer_code")
    private  String dealerCode;

    /**
     * 车主年龄
     */
    @TableField("age")
    private  String age;

    /**
     * 车主性别
     */
    @TableField("sex")
    private  String sex;

    /**
     * 车型
     */
    @TableField("model")
    private  String model;


    /**
     * 客户唯一id
     */
    @TableField("one_id")
    private Long oneId;


    /**
     * 工单号
     */
    @TableField("ro_no")
    private  String roNo;


    /**
     * 维修类型
     */
    @TableField("repair_type_code")
    private  String repairTypeCode;


    /**
     * 出厂里程
     */
    @TableField("OUT_MILEAGE")
    private  Double outMileage;


    /**
     * 开单时间
     */
    @TableField("RO_CREATE_DATE")
    private  Date roCreateDate;



    /**
     * 完成经销商
     */
    @TableField("finish_dealer_code")
    private  String finishDealerCode;

    /**
     * 上次进厂日期
     */
    @TableField("last_in_date")
    private Date lastInDate;

    /**
     *线索类型:0 ：非VOC类型,1 ：18个月未保养，2：非4S店保养流失,3:流失客户保养灯亮
     */
    @TableField(exist = false)
    private Integer lossType;

    /**
     *流失预警线索类型
     */
    @TableField(exist = false)
    private Integer lossWarningType;

    /**
     * 日均里程
     */
    @TableField(exist = false)
    private BigDecimal dailyAverageMileage;



    /**
     * 邀约类型 查询条件
     */
    @TableField(exist = false)
    private List<Integer> inviteTypeParam;


    /**
     * 计划跟进日期开始 查询条件
     */
    @TableField(exist = false)
    private String planFollowDateStart;

    /**
     * 计划跟进日期结束 查询条件
     */
    @TableField(exist = false)
    private String planFollowDateEnd;

    /**
     * 实际跟进日期开始 查询条件
     */
    @TableField(exist = false)
    private String actualFollowDateStart;

    /**
     * 实际跟进日期结束 查询条件
     */
    @TableField(exist = false)
    private String actualFollowDateEnd;

    /**
     * 建议进厂日期开始 查询条件
     */
    @TableField(exist = false)
    private String adviseInDateStart;

    /**
     * 建议进厂日期结束 查询条件
     */
    @TableField(exist = false)
    private String adviseInDateEnd;


    /**
     * 邀约状态 查询条件
     */
    @TableField(exist = false)
    private List<Integer> followStatusParam;

    /**
     * 工单状态 线索完成状态
     */
    @TableField(exist = false)
    private List<Integer> orderStatusParam;

    /**
     * 线索类型 查询条件
     */
    @TableField(exist = false)
    private List<Integer> recordTypeParam;

    /**
     * 离职用户 id 查询条件
     */
    @TableField(exist = false)
    private List<Integer> leaveIds;

    /**
     * 邀约创建日期开始 查询条件
     */
    @TableField(exist = false)
    private String createdAtStart;

    /**
     * 邀约创建日期结束 查询条件
     */
    @TableField(exist = false)
    private String createdAtEnd;


    /**
     * 是否逾期未跟进：1 是，0 否 查询条件
     */
    @TableField(exist = false)
    private Integer overdue;


    /**
     * 是否查询未分配 查询条件
     */
    @TableField(exist = false)
    private  Integer isNoDistribute;


    /**
     * 是否查询待分配 查询条件
     */
    @TableField(exist = false)
    private  Integer isWaitDistribute;

    /**
     * 易损件规则id
     */
    @TableField("part_item_rule_id")
    private Long partItemRuleId;


    /**
     * 易损件上次更换时间
     */
    @TableField("last_change_date")
    private Date lastChangeDate;

    /**
     * 易损件code
     */
    @TableField("item_code")
    private String itemCode;

    /**
     *易损件名称
     */
    @TableField("item_name")
    private String itemName;

    /**
     *易损件类型
     */
    @TableField("item_type")
    private Integer itemType;


    /**
     * 最新失败原因
     */
    @TableField(value = "lose_reason",updateStrategy = FieldStrategy.IGNORED,jdbcType = JdbcType.INTEGER)
    private Integer loseReason;


    /**
     * 最新跟进内容
     */
    @TableField(value = "content",updateStrategy = FieldStrategy.IGNORED,jdbcType = JdbcType.VARCHAR)
    private String content;


    @TableField(exist = false)
    private List<String> dealerCodes;


    /**
     * 邀约记录明细集合
     */
    @TableField(exist = false)
    private List<InviteVehicleRecordDetailPO> recordDetailList;


    /**
     * 是否voc车辆
     */
    @TableField(exist = false)
    private  Integer isVoc;


    /**
     * 上次保养日期
     */
    @TableField(exist = false)
    private  Date lastMaintenanceDate;


    /**
     * 建议入厂日期间隔
     */
    @TableField(exist = false)
    private Integer dateInterval;


    /**
     * 子线索
     */
    @TableField(exist = false)
    private String sonInviteType;

    /**
     *建议入厂里程
     */
    @TableField(exist = false)
    private Integer adviseInMileage;


    /**
     *任务基准日期
     */
    @TableField(exist = false)
    private Date inviteTime;

    /**
     *投保单状态
     */
    @TableField(exist = false)
    private Integer insuranceStatus;

    /**
     *是否联保
     */
    @TableField(exist = false)
    private Integer isJointGuarantee;


    /**
     * 最新得分
     */
    @TableField(exist = false)
    private Integer score;


    @TableField(exist = false)
    private Integer callLength;


    /**
     *    通话详情Id
     */
    @TableField(exist = false)
    private Long callDetailId;


    /**
     *    通话时间
     */
    @TableField(exist = false)
    private Date callTime;

    /**
     *    超时关闭间隔
     */
    @TableField(exist = false)
    private Integer closeInterval;

    /**
     * 二次跟进月份
     */
    @TableField(exist = false)
    private String monthTwice;

    /**
     * 是否二次跟进
     */
    @TableField(exist = false)
    private String isAi;


    /**
     * 是否二次跟进
     */
    @TableField(exist = false)
    private Integer recordNum;


    /**
     * 是否二次跟进
     */
    @TableField(exist = false)
    private Date aiAt;


    /**
     * 是否二次跟进
     */
    @TableField(exist = false)
    private Date orderAt;



    /**
     * 是否二次跟进
     */
    @TableField(exist = false)
    private Date recordAt;
    /**
     * 是否二次跟进
     */
    @TableField(exist = false)
    private Integer recordType;

    /**
     * 日均里程
     */
    @TableField(exist = false)
    private String dailyMile;

    /**
     * 券码
     */
    @TableField(exist = false)
    private String couponCode;

    /**
     * 卡券名称
     */
    @TableField(exist = false)
    private String couponName;

    /**
     * 验证状态
     */
    @TableField(exist = false)
    private String verifyStatus;

    /**
     * crm线索ID
     */
    @TableField(exist = false)
    private Long icmId;

    @TableField(exist = false)
    private boolean isPartClue;

    @TableField(exist = false)
    private Boolean isFlag;
    //返厂意向等级
    @TableField(exist = false)
    private Integer returnIntentionLevel;

    @TableField(exist = false)
    private Integer bevFlag;

    @TableField(exist = false)
    private String functionGroup;

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
