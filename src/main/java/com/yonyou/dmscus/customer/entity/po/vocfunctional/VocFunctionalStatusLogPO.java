package com.yonyou.dmscus.customer.entity.po.vocfunctional;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;


/**
 * bi推送数据记录表
 */

@Data
@ApiModel(value = "VocFunctionalStatusLogPO", description = "BI推送车辆VOC信息表流失表")
@TableName("tt_voc_functional_status_log")
public class VocFunctionalStatusLogPO  extends  BasePo{
  //通知时间
  @ApiModelProperty(value = "更新时间",notes = "更新时间",required = true,name = "dt",example = "20221011",dataType = "String")
  @TableField("dt")
  private String dt;
  @ApiModelProperty(value = "车架号",notes = "车架号",required = true,name = "dt",example = "YV1LFA2D2H1163606",dataType = "String")
  @TableField("vin")
  private String vin;

  @TableField("activated_state")
  @ApiModelProperty(value = "已激活，未激活",notes = "已激活，未激活",required = true,name = "activated_state",example = "已激活",dataType = "String")
  private String activatedState;


  @TableField("subscription_startdate")
  @ApiModelProperty(value = "开始时间",notes = "开始时间",required = true,name = "subscription_startdate",example = "0",dataType = "String")
  private String subscriptionStartdate;

  @TableField("subscription_enddate")
  @ApiModelProperty(value = "结束时间",notes = "结束时间",required = true,name = "subscription_enddate",example = "0",dataType = "String")
  private String subscriptionEnddate;

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    if (!super.equals(o)) return false;
    VocFunctionalStatusLogPO that = (VocFunctionalStatusLogPO) o;
    return Objects.equals(dt, that.dt) &&
            Objects.equals(vin, that.vin) &&
            Objects.equals(activatedState, that.activatedState) &&
            Objects.equals(subscriptionStartdate, that.subscriptionStartdate) &&
            Objects.equals(subscriptionEnddate, that.subscriptionEnddate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), dt, vin, activatedState, subscriptionStartdate, subscriptionEnddate);
  }
}
