package com.yonyou.dmscus.customer.entity.dto.businessPlatform;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import java.io.Serializable;
import java.util.List;

public class CheckTmVehicleDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<String> vinList;

    private Boolean isAllExists;

    private List<String> notExistVinList;

    public List<String> getVinList() {
        return vinList;
    }

    public void setVinList(List<String> vinList) {
        this.vinList = vinList;
    }

    public Boolean getIsAllExists() {
        return isAllExists;
    }

    public void setIsAllExists(Boolean isAllExists) {
        this.isAllExists = isAllExists;
    }

    public List<String> getNotExistVinList() {
        return notExistVinList;
    }

    public void setNotExistVinList(List<String> notExistVinList) {
        this.notExistVinList = notExistVinList;
    }
}
