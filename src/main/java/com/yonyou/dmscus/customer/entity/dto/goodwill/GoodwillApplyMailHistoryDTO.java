package com.yonyou.dmscus.customer.entity.dto.goodwill;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */

public class GoodwillApplyMailHistoryDTO extends BaseDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	private String appId;

	/**
	 * 所有者代码
	 */
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	private Integer orgId;

	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 亲善单id
	 */
	private Long goodwillApplyId;
	/**
	 * 亲善单号
	 */
	private String applyNo;
	/**
	 * 集团ID
	 */
	private Integer blocId;
	/**
	 * 区域ID
	 */
	private Integer areaManageId;
	/**
	 * 区域ID
	 */
	private String areaManage;
	/**
	 * 区域ID
	 */
	private String smallArea;

	/**
	 * 经销商
	 */
	private String dealerCode;

	/**
	 * 邮件类型
	 */
	private Integer mailType;

	/**
	 * 发件人
	 */
	private String sendBy;

	/**
	 * 收件人邮箱
	 */
	private String receiverMail;

	/**
	 * 主题
	 */
	private String title;

	/**
	 * 邮件内容
	 */
	private String content;

	/**
	 * 发送状态
	 */
	private Integer sendStatus;

	/**
	 * 发送时间
	 */
	private Date sendTime;

	/**
	 * 是否有效
	 */
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	private Boolean isDeleted;

	public Integer getBlocId() {
		return blocId;
	}

	public void setBlocId(Integer blocId) {
		this.blocId = blocId;
	}

	public Integer getAreaManageId() {
		return areaManageId;
	}

	public void setAreaManageId(Integer areaManageId) {
		this.areaManageId = areaManageId;
	}


	public String getAreaManage() {
		return areaManage;
	}

	public void setAreaManage(String areaManage) {
		this.areaManage = areaManage;
	}

	public String getSmallArea() {
		return smallArea;
	}

	public void setSmallArea(String smallArea) {
		this.smallArea = smallArea;
	}

	public String getApplyNo() {
		return applyNo;
	}

	public void setApplyNo(String applyNo) {
		this.applyNo = applyNo;
	}

	public String getCreatedStartAt() {
		return createdStartAt;
	}

	public void setCreatedStartAt(String createdStartAt) {
		this.createdStartAt = createdStartAt;
	}

	public String getCreatedEndAt() {
		return createdEndAt;
	}

	public void setCreatedEndAt(String createdEndAt) {
		this.createdEndAt = createdEndAt;
	}

	/**
	 * 
	 * 创建时间
	 */
	private Date createdAt;

	/**
	 * 修改时间
	 */
	private Date updatedAt;

	private String createdStartAt;

	private String createdEndAt;

	private String vin;
	private String auditName;

	private List<String> auditName1;

	public String getAuditName() {
		return auditName;
	}

	public void setAuditName(String auditName) {
		this.auditName = auditName;
	}

	public List<String> getAuditName1() {
		return auditName1;
	}

	public void setAuditName1(List<String> auditName1) {
		this.auditName1 = auditName1;
	}

	public String getVin() {
		return vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public GoodwillApplyMailHistoryDTO() {
		super();
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	public String getOwnerParCode() {
		return ownerParCode;
	}

	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getGoodwillApplyId() {
		return goodwillApplyId;
	}

	public void setGoodwillApplyId(Long goodwillApplyId) {
		this.goodwillApplyId = goodwillApplyId;
	}

	public String getDealerCode() {
		return dealerCode;
	}

	public void setDealerCode(String dealerCode) {
		this.dealerCode = dealerCode;
	}

	public Integer getMailType() {
		return mailType;
	}

	public void setMailType(Integer mailType) {
		this.mailType = mailType;
	}

	public String getSendBy() {
		return sendBy;
	}

	public void setSendBy(String sendBy) {
		this.sendBy = sendBy;
	}

	public String getReceiverMail() {
		return receiverMail;
	}

	public void setReceiverMail(String receiverMail) {
		this.receiverMail = receiverMail;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Integer getSendStatus() {
		return sendStatus;
	}

	public void setSendStatus(Integer sendStatus) {
		this.sendStatus = sendStatus;
	}

	public Date getSendTime() {
		return sendTime;
	}

	public void setSendTime(Date sendTime) {
		this.sendTime = sendTime;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	public String toString() {
		return "GoodwillApplyMailHistoryDTO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", goodwillApplyId=" + goodwillApplyId
				+ ", dealerCode=" + dealerCode + ", mailType=" + mailType + ", sendBy=" + sendBy + ", receiverMail="
				+ receiverMail + ", title=" + title + ", content=" + content + ", sendStatus=" + sendStatus
				+ ", sendTime=" + sendTime + ", isValid=" + isValid + ", isDeleted=" + isDeleted + ", createdAt="
				+ createdAt + ", updatedAt=" + updatedAt + "}";
	}

	/**
	 * 将DTO 转换为PO //对某个对象属性进行赋值
	 * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
	 * 
	 * @param poClass
	 *            需要转换的poClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
		return super.transDtoToPo(poClass);
	}

	/**
	 * 将DTO 转换为PO
	 * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
	 * 
	 * @param po
	 *            需要转换的对象
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BasePO> void transDtoToPo(T po) {
		BeanMapperUtil.copyProperties(this, po, "id");
	}

}
