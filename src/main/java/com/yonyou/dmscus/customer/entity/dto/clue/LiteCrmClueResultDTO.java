package com.yonyou.dmscus.customer.entity.dto.clue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * description 线索下发通用接口返回参数
 * <AUTHOR>
 * @date 2023/10/23 10:26
 */
@Data
@ApiModel(description="线索下发通用接口返回参数")
public class LiteCrmClueResultDTO {

    /**
     * 拒收的经销商code
     */
    @ApiModelProperty(value = "拒收的经销商code", required = true, example = "[\"SHJ\",\"SHN\"]")
    private List<String> rejectionCodes;

    /**
     * 执行成功与否
     */
    @ApiModelProperty(value = "执行成功与否", required = true, example = "true")
    private Boolean flag;

    public LiteCrmClueResultDTO() {
    }

    public LiteCrmClueResultDTO(Boolean flag) {
        this.flag = flag;
    }

    public LiteCrmClueResultDTO(List<String> rejectionCodes, Boolean flag) {
        this.rejectionCodes = rejectionCodes;
        this.flag = flag;
    }
}
