package com.yonyou.dmscus.customer.entity.dto.complaint;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.function.utils.jsonSerializer.date.JsonDateDeserializer;
import com.yonyou.dmscloud.function.utils.jsonSerializer.date.JsonDateSerializer;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 客户投诉跟进表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
    
public class ComplaintFollowDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

        
    /**
     * 系统ID
     */
                private String appId;
                
    /**
     * 所有者代码 
     */
                private String ownerCode;
                
    /**
     * 所有者的父组织代码 
     */
                private String ownerParCode;
                
    /**
     * 组织ID
     */
                private Integer orgId;
                
    /**
     * 主键ID
     */
                private Long id;
                
    /**
     * 投诉信息表主键ID
     */
                private Long complaintInfoId;
                
    /**
     * 跟进对象 区域经理、经销商、CCM、客服中心
     */
                private String object;
                
    /**
     * 填写时间
     */
                @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date followTime;
                
    /**
     * 填写人
     */
                private String follower;
    /**
     * 填写人
     */
    private String followerName;
    /**
     * 车主地址
     */
    private String ownerAddress;
                
    /**
     * 跟进内容
     */
                private String followContent;
                
    /**
     * CCM跟进内容是否全网发布 1 公开 0 不公开
     */
                private Boolean ccmNotPublish;
    /**
     * 经销商跟进内容是否全网发布 1 公开 0 不公开
     */
    private Boolean dealerNotPublish;

    /**
     * CCM主题
     */
                private String ccmSubject;
                
    /**
     * 最新状态
     */
                private String status;
                
    /**
     * 防止再发建议
     */
                private Integer advise;
                
    /**
     * CCM部位 多选用逗号分隔
     */
                private String ccmPart;
                
    /**
     * CCM细分部位 多选用逗号分隔
     */
                private String ccmSubdivisionPart;
                
    /**
     * CCM主要原因 多选用逗号分隔
     */
                private String ccMainReason;
                
    /**
     * CC解决结果 多选用逗号分隔
     */
                private String ccResult;
                
    /**
     * 关键字
     */
                private String keyword;
                
    /**
     * 分类1
     */
                private Integer classification1;
                
    /**
     * 分类2
     */
                private String classification2;
                
    /**
     * 分类3
     */
                private String classification3;
                
    /**
     * 分类4
     */
                private String classification4;
                
    /**
     * 分类5
     */
                private String classification5;
                
    /**
     * 分类6
     */
                private String classification6;
                
    /**
     * 下次跟进时间
     */
                @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date planFollowTime;
                
    /**
     * 实际跟进时间
     */
                @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date actuallFollowTime2;
                
    /**
     * 数据来源 
     */
                private Integer dataSources;

    /**
     * 质量部分类1
     */
    private Integer qualityClassification1;

    /**
     * 质量部分类2
     */
    private Integer qualityClassification2;
    /**
     * 质量部分类3
     */
    private String qualityClassification3;
    /**
     * 质量部分类4
     */
    private Integer qualityClassification4;
    /**
     * 故障分类
     */
    private String faultClassification;
    /**
     * 备注1
     */
    private String remark1;
    /**
     * 备注2
     */
    private String remark2;
    /**
     * 备注3
     */
    private String remark3;
    /**
     * 备注4
     */
    private String remark4;

    /**
     * 是否CC填写
     */
    private Boolean isCcm;

    /**
     * 是否删除
     */
                private Boolean isDeleted;
                
    /**
     * 是否有效 
     */
                private Integer isValid;
                
    /**
     * 创建时间
     */
                @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date createdAt;
                
    /**
     * 更新时间
     */
                @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date updatedAt;
            
    public ComplaintFollowDTO() {
        super();
    }

                                
    public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
            }
                                
    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
            }
                                
    public String getOwnerParCode(){
        return ownerParCode;
    }

    public Boolean getCcmNotPublish() {
        return ccmNotPublish;
    }

    public void setCcmNotPublish(Boolean ccmNotPublish) {
        this.ccmNotPublish = ccmNotPublish;
    }

    public Boolean getDealerNotPublish() {
        return dealerNotPublish;
    }

    public void setDealerNotPublish(Boolean dealerNotPublish) {
        this.dealerNotPublish = dealerNotPublish;
    }

    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
            }
                                
    public Integer getOrgId(){
        return orgId;
    }

    public String getFollowerName() {
        return followerName;
    }

    public void setFollowerName(String followerName) {
        this.followerName = followerName;
    }

    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
            }
                                            
    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
            }
                                
    public Long getComplaintInfoId(){
        return complaintInfoId;
    }

    public String getOwnerAddress() {
        return ownerAddress;
    }

    public void setOwnerAddress(String ownerAddress) {
        this.ownerAddress = ownerAddress;
    }

    public void  setComplaintInfoId(Long complaintInfoId) {
        this.complaintInfoId = complaintInfoId;
            }
                                
    public String getObject(){
        return object;
    }


    public void  setObject(String object) {
        this.object = object;
            }
                                
    public Date getFollowTime(){
        return followTime;
    }


    public void  setFollowTime(Date followTime) {
        this.followTime = followTime;
            }
                                
    public String getFollower(){
        return follower;
    }


    public void  setFollower(String follower) {
        this.follower = follower;
            }
                                
    public String getFollowContent(){
        return followContent;
    }


    public void  setFollowContent(String followContent) {
        this.followContent = followContent;
            }
                    
    public Boolean getIsCcmNotPublish(){
        return ccmNotPublish;
    }


    public void  setIsCcmNotPublish(Boolean ccmNotPublish) {
        this.ccmNotPublish = ccmNotPublish;
            }
                                
    public String getCcmSubject(){
        return ccmSubject;
    }


    public void  setCcmSubject(String ccmSubject) {
        this.ccmSubject = ccmSubject;
            }
                                
    public String getStatus(){
        return status;
    }


    public void  setStatus(String status) {
        this.status = status;
            }
                                
    public Integer getAdvise(){
        return advise;
    }


    public void  setAdvise(Integer advise) {
        this.advise = advise;
            }
                                
    public String getCcmPart(){
        return ccmPart;
    }


    public void  setCcmPart(String ccmPart) {
        this.ccmPart = ccmPart;
            }
                                
    public String getCcmSubdivisionPart(){
        return ccmSubdivisionPart;
    }


    public void  setCcmSubdivisionPart(String ccmSubdivisionPart) {
        this.ccmSubdivisionPart = ccmSubdivisionPart;
            }
                                
    public String getCcMainReason(){
        return ccMainReason;
    }


    public void  setCcMainReason(String ccMainReason) {
        this.ccMainReason = ccMainReason;
            }
                                
    public String getCcResult(){
        return ccResult;
    }


    public void  setCcResult(String ccResult) {
        this.ccResult = ccResult;
            }
                                
    public String getKeyword(){
        return keyword;
    }


    public void  setKeyword(String keyword) {
        this.keyword = keyword;
            }
                                
    public Integer getClassification1(){
        return classification1;
    }


    public void  setClassification1(Integer classification1) {
        this.classification1 = classification1;
            }

    public String getClassification2() {
        return classification2;
    }

    public void setClassification2(String classification2) {
        this.classification2 = classification2;
    }

    public String getClassification3(){
        return classification3;
    }


    public void  setClassification3(String classification3) {
        this.classification3 = classification3;
            }

    public String getClassification4() {
        return classification4;
    }

    public void setClassification4(String classification4) {
        this.classification4 = classification4;
    }

    public String getClassification5(){
        return classification5;
    }


    public void  setClassification5(String classification5) {
        this.classification5 = classification5;
            }
                                
    public String getClassification6(){
        return classification6;
    }


    public void  setClassification6(String classification6) {
        this.classification6 = classification6;
            }
                                
    public Date getPlanFollowTime(){
        return planFollowTime;
    }


    public void  setPlanFollowTime(Date planFollowTime) {
        this.planFollowTime = planFollowTime;
            }
                                
    public Date getActuallFollowTime2(){
        return actuallFollowTime2;
    }


    public void  setActuallFollowTime2(Date actuallFollowTime2) {
        this.actuallFollowTime2 = actuallFollowTime2;
            }
                                
    public Integer getDataSources(){
        return dataSources;
    }


    public void  setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
            }
                                
    public Integer getIsValid(){
        return isValid;
    }

    public Integer getQualityClassification1() {
        return qualityClassification1;
    }

    public void setQualityClassification1(Integer qualityClassification1) {
        this.qualityClassification1 = qualityClassification1;
    }

    public Integer getQualityClassification2() {
        return qualityClassification2;
    }

    public void setQualityClassification2(Integer qualityClassification2) {
        this.qualityClassification2 = qualityClassification2;
    }

    public String getQualityClassification3() {
        return qualityClassification3;
    }

    public void setQualityClassification3(String qualityClassification3) {
        this.qualityClassification3 = qualityClassification3;
    }

    public Boolean getCcm() {
        return isCcm;
    }

    public void setCcm(Boolean ccm) {
        isCcm = ccm;
    }

    public Integer getQualityClassification4() {
        return qualityClassification4;
    }

    public void setQualityClassification4(Integer qualityClassification4) {
        this.qualityClassification4 = qualityClassification4;
    }

    public String getFaultClassification() {
        return faultClassification;
    }

    public void setFaultClassification(String faultClassification) {
        this.faultClassification = faultClassification;
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1;
    }

    public String getRemark2() {
        return remark2;
    }

    public void setRemark2(String remark2) {
        this.remark2 = remark2;
    }

    public String getRemark3() {
        return remark3;
    }

    public void setRemark3(String remark3) {
        this.remark3 = remark3;
    }

    public String getRemark4() {
        return remark4;
    }

    public void setRemark4(String remark4) {
        this.remark4 = remark4;
    }

    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
            }
                                
    public Date getCreatedAt(){
        return createdAt;
    }


    public void  setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
            }
                                
    public Date getUpdatedAt(){
        return updatedAt;
    }


    public void  setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
            }
    
    @Override
    public String toString() {
        return "ComplaintFollowDTO{" +
                                            "appId=" + appId +
                                                            ", ownerCode=" + ownerCode +
                                                            ", ownerParCode=" + ownerParCode +
                                                            ", orgId=" + orgId +
                                                            ", id=" + id +
                                                            ", complaintInfoId=" + complaintInfoId +
                                                            ", object=" + object +
                                                            ", followTime=" + followTime +
                                                            ", follower=" + follower +
                                                            ", followContent=" + followContent +
                                                            ", ccmNotPublish=" + ccmNotPublish +
                                                            ", ccmSubject=" + ccmSubject +
                                                            ", status=" + status +
                                                            ", advise=" + advise +
                                                            ", ccmPart=" + ccmPart +
                                                            ", ccmSubdivisionPart=" + ccmSubdivisionPart +
                                                            ", ccMainReason=" + ccMainReason +
                                                            ", ccResult=" + ccResult +
                                                            ", keyword=" + keyword +
                                                            ", classification1=" + classification1 +
                                                            ", classification2=" + classification2 +
                                                            ", classification3=" + classification3 +
                                                            ", classification4=" + classification4 +
                                                            ", classification5=" + classification5 +
                                                            ", classification6=" + classification6 +
                                                            ", planFollowTime=" + planFollowTime +
                                                            ", actuallFollowTime2=" + actuallFollowTime2 +
                                                            ", dataSources=" + dataSources +
                                                            ", isDeleted=" + isDeleted +
                                                            ", isValid=" + isValid +
                                                            ", createdAt=" + createdAt +
                                                            ", updatedAt=" + updatedAt +
                                    "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
