package com.yonyou.dmscus.customer.entity.po.goodwill;

import java.math.BigDecimal;
import java.util.Date;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善券
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-23
 */
public class GoodwillStampsPO extends BasePO<GoodwillStampsPO> {

	private static final long serialVersionUID = 1L;
	/**
	 * 通知开票id
	 */
	private Long noticeId;

	/**
	 * 亲善单id
	 */
	private Long goodwillApplyId;
	/**
	 * 卡券id
	 */
	private Long couponId;

	/**
	 * 核销id
	 */
	private Long consumeId;

	/**
	 * 消费ID
	 */
	private String[] consume;

	/**
	 * 消费ID
	 */
	private String consumes;

	public String getConsumes() {
		return consumes;
	}

	public void setConsumes(String consumes) {
		this.consumes = consumes;
	}

	/**
	 * 经销商
	 */
	private String dealerCode;
	/**
	 * 经销商名称
	 */
	private String dealerName;
	/**
	 * 亲善单号
	 */
	private String applyNo;

	/**
	 * 车主姓名
	 */
	private String veOwnerName;

	/**
	 * 车牌号
	 */
	private String license;

	/**
	 * VIN
	 */
	private String vin;

	/**
	 * 通知金额
	 */
	private BigDecimal noticeAmount;

	/**
	 * 资产类型
	 */
	private String resourceType;

	/**
	 * 通知充值日期
	 */
	private Date noticeDate;

	/**
	 * 充值日期
	 */
	private Date rechargeDate;

	/**
	 * 开票通知时间
	 */
	private Date noticeInvoiceDate;
	/**
	 * 开票时间
	 */
	private Date invoiceDate;

	/**
	 * 开票金额
	 */
	private BigDecimal invoiceAmount;

	/**
	 * 亲善经销商
	 */
	private Date goodwillDealer;

	/**
	 * 发票收到时间
	 */
	private Date receiveDate;

	/**
	 * 快递公司
	 */
	private String courierCompany;
	/**
	 * 快递单号
	 */
	private String courierNumber;

	/**
	 * 开票id
	 */
	private String invoiceId;

	/**
	 * 已通知开票金额
	 */
	private BigDecimal invoicedAmount;

	/**
	 * 开票抬头
	 */

	private String invoiceTitle;

	/**
	 * 通知开票金额
	 */

	private BigDecimal noticeInvoicePrice;

	public String getInvoiceTitle() {
		return invoiceTitle;
	}

	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	public BigDecimal getNoticeInvoicePrice() {
		return noticeInvoicePrice;
	}

	public void setNoticeInvoicePrice(BigDecimal noticeInvoicePrice) {
		this.noticeInvoicePrice = noticeInvoicePrice;
	}

	public BigDecimal getInvoicedAmount() {
		return invoicedAmount;
	}

	public void setInvoicedAmount(BigDecimal invoicedAmount) {
		this.invoicedAmount = invoicedAmount;
	}

	public String getCourierCompany() {
		return courierCompany;
	}

	public void setCourierCompany(String courierCompany) {
		this.courierCompany = courierCompany;
	}

	public String getCourierNumber() {
		return courierNumber;
	}

	public void setCourierNumber(String courierNumber) {
		this.courierNumber = courierNumber;
	}

	public String getInvoiceId() {
		return invoiceId;
	}

	public void setInvoiceId(String invoiceId) {
		this.invoiceId = invoiceId;
	}

	public Long getCouponId() {
		return couponId;
	}

	public void setCouponId(Long couponId) {
		this.couponId = couponId;
	}

	public Date getDeliveryDate() {
		return deliveryDate;
	}

	public void setDeliveryDate(Date deliveryDate) {
		this.deliveryDate = deliveryDate;
	}

	/**
	 * 快递时间
	 */
	private Date deliveryDate;

	public Date getNoticeInvoiceDate() {
		return noticeInvoiceDate;
	}

	public void setNoticeInvoiceDate(Date noticeInvoiceDate) {
		this.noticeInvoiceDate = noticeInvoiceDate;
	}

	public Date getInvoiceDate() {
		return invoiceDate;
	}

	public void setInvoiceDate(Date invoiceDate) {
		this.invoiceDate = invoiceDate;
	}

	public BigDecimal getInvoiceAmount() {
		return invoiceAmount;
	}

	public void setInvoiceAmount(BigDecimal invoiceAmount) {
		this.invoiceAmount = invoiceAmount;
	}

	public Date getGoodwillDealer() {
		return goodwillDealer;
	}

	public void setGoodwillDealer(Date goodwillDealer) {
		this.goodwillDealer = goodwillDealer;
	}

	public Date getReceiveDate() {
		return receiveDate;
	}

	public void setReceiveDate(Date receiveDate) {
		this.receiveDate = receiveDate;
	}

	public String getConsumeDealer() {
		return consumeDealer;
	}

	public void setConsumeDealer(String consumeDealer) {
		this.consumeDealer = consumeDealer;
	}

	public String getRepairNumber() {
		return repairNumber;
	}

	public void setRepairNumber(String repairNumber) {
		this.repairNumber = repairNumber;
	}

	public BigDecimal getConsumeAmount() {
		return consumeAmount;
	}

	public void setConsumeAmount(BigDecimal consumeAmount) {
		this.consumeAmount = consumeAmount;
	}

	/**
	 * 
	 * 充值金额
	 */
	private BigDecimal rechargeAmount;

	/**
	 * 已消费金额
	 */
	private BigDecimal usedAmount;

	/**
	 * 剩余金额
	 */
	private BigDecimal leftAmount;

	/**
	 * 代金券已充值金额
	 */
	private BigDecimal voucherRechargeAmount;

	/**
	 * 代金券未充值金额
	 */
	private BigDecimal voucherNotAmount;
	/**
	 * 代金券已开票金额
	 */
	private BigDecimal voucherInvoiceAmount;

	/**
	 * 代金券未开票金额
	 */
	private BigDecimal voucherNotInvoiceAmount;

	/**
	 * 通知充值日期
	 */

	private Date noticeStartdAt;

	/**
	 * 通知充值日期
	 */

	private Date noticeEndAt;

	/**
	 * 充值时间
	 */

	private Date rechargeStartdAt;
	/**
	 * 充值时间
	 */

	private Date rechargeEndAt;

	/**
	 * 消费开始时间
	 */

	private Date consumeStartdAt;
	/**
	 * 消费结束时间
	 */

	private Date consumeEndAt;

	public Date getConsumeDate() {
		return consumeDate;
	}

	public void setConsumeDate(Date consumeDate) {
		this.consumeDate = consumeDate;
	}

	/**
	 * 消费时间
	 */
	private Date consumeDate;

	/**
	 * 消费经销商
	 */
	private String consumeDealer;

	/**
	 * 维修工单号
	 */
	private String repairNumber;

	/**
	 * 消费金额
	 */
	private BigDecimal consumeAmount;

	/**
	 * 开票开始时间
	 */

	private Date invoiceStartdAt;
	/**
	 * 开票结束时间
	 */

	private Date invoiceEndAt;

	/**
	 * 发票号
	 */

	private String invoiceNumber;

	public Date getConsumeStartdAt() {
		return consumeStartdAt;
	}

	public void setConsumeStartdAt(Date consumeStartdAt) {
		this.consumeStartdAt = consumeStartdAt;
	}

	public Date getConsumeEndAt() {
		return consumeEndAt;
	}

	public void setConsumeEndAt(Date consumeEndAt) {
		this.consumeEndAt = consumeEndAt;
	}

	public Date getInvoiceStartdAt() {
		return invoiceStartdAt;
	}

	public void setInvoiceStartdAt(Date invoiceStartdAt) {
		this.invoiceStartdAt = invoiceStartdAt;
	}

	public Date getInvoiceEndAt() {
		return invoiceEndAt;
	}

	public void setInvoiceEndAt(Date invoiceEndAt) {
		this.invoiceEndAt = invoiceEndAt;
	}

	public String getInvoiceNumber() {
		return invoiceNumber;
	}

	public void setInvoiceNumber(String invoiceNumber) {
		this.invoiceNumber = invoiceNumber;
	}

	/**
	 * 是否已充值
	 */

	private Integer isChargerd;

	/**
	 * 集团
	 */

	private String group;

	/**
	 * 区域-区域经理
	 */

	private String areaManager;
	/**
	 * 区域-区域经理
	 */

	private String areaManage;
	/**
	 * 区域-区域经理
	 */

	private String smallArea;

	/**
	 * 代金券充值金额
	 */
	private BigDecimal voucherCouponFaceRechargePrice;
	/**
	 * 费率
	 */
	private BigDecimal costRate;
	/**
	 * 小区经理名称
	 */
	private String auditName;

	public String getAuditName() {
		return auditName;
	}

	public void setAuditName(String auditName) {
		this.auditName = auditName;
	}

	public BigDecimal getCostRate() {
		return costRate;
	}

	public void setCostRate(BigDecimal costRate) {
		this.costRate = costRate;
	}

	public BigDecimal getVoucherCouponFaceRechargePrice() {
		return voucherCouponFaceRechargePrice;
	}

	public void setVoucherCouponFaceRechargePrice(BigDecimal voucherCouponFaceRechargePrice) {
		this.voucherCouponFaceRechargePrice = voucherCouponFaceRechargePrice;
	}


	public Integer getIsChargerd() {
		return isChargerd;
	}

	public void setIsChargerd(Integer isChargerd) {
		this.isChargerd = isChargerd;
	}

	public String getGroup() {
		return group;
	}

	public void setGroup(String group) {
		this.group = group;
	}

	public String getAreaManager() {
		return areaManager;
	}

	public void setAreaManager(String areaManager) {
		this.areaManager = areaManager;
	}

	public String getAreaManage() {
		return areaManage;
	}

	public void setAreaManage(String areaManage) {
		this.areaManage = areaManage;
	}

	public String getSmallArea() {
		return smallArea;
	}

	public void setSmallArea(String smallArea) {
		this.smallArea = smallArea;
	}

	public Long getNoticeId() {
		return noticeId;
	}

	public void setNoticeId(Long noticeId) {
		this.noticeId = noticeId;
	}

	public Long getGoodwillApplyId() {
		return goodwillApplyId;
	}

	public void setGoodwillApplyId(Long goodwillApplyId) {
		this.goodwillApplyId = goodwillApplyId;
	}

	public String getDealerCode() {
		return dealerCode;
	}

	public void setDealerCode(String dealerCode) {
		this.dealerCode = dealerCode;
	}

	public String getVeOwnerName() {
		return veOwnerName;
	}

	public void setVeOwnerName(String veOwnerName) {
		this.veOwnerName = veOwnerName;
	}

	public String getLicense() {
		return license;
	}

	public void setLicense(String license) {
		this.license = license;
	}

	public String getVin() {
		return vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public BigDecimal getNoticeAmount() {
		return noticeAmount;
	}

	public void setNoticeAmount(BigDecimal noticeAmount) {
		this.noticeAmount = noticeAmount;
	}

	public String getResourceType() {
		return resourceType;
	}

	public void setResourceType(String resourceType) {
		this.resourceType = resourceType;
	}

	public Date getNoticeDate() {
		return noticeDate;
	}

	public void setNoticeDate(Date noticeDate) {
		this.noticeDate = noticeDate;
	}

	public Date getRechargeDate() {
		return rechargeDate;
	}

	public void setRechargeDate(Date rechargeDate) {
		this.rechargeDate = rechargeDate;
	}

	public BigDecimal getRechargeAmount() {
		return rechargeAmount;
	}

	public void setRechargeAmount(BigDecimal rechargeAmount) {
		this.rechargeAmount = rechargeAmount;
	}

	public BigDecimal getUsedAmount() {
		return usedAmount;
	}

	public void setUsedAmount(BigDecimal usedAmount) {
		this.usedAmount = usedAmount;
	}

	public BigDecimal getLeftAmount() {
		return leftAmount;
	}

	public void setLeftAmount(BigDecimal leftAmount) {
		this.leftAmount = leftAmount;
	}

	public BigDecimal getVoucherRechargeAmount() {
		return voucherRechargeAmount;
	}

	public void setVoucherRechargeAmount(BigDecimal voucherRechargeAmount) {
		this.voucherRechargeAmount = voucherRechargeAmount;
	}

	public BigDecimal getVoucherNotAmount() {
		return voucherNotAmount;
	}

	public void setVoucherNotAmount(BigDecimal voucherNotAmount) {
		this.voucherNotAmount = voucherNotAmount;
	}

	public Date getNoticeStartdAt() {
		return noticeStartdAt;
	}

	public void setNoticeStartdAt(Date noticeStartdAt) {
		this.noticeStartdAt = noticeStartdAt;
	}

	public Date getNoticeEndAt() {
		return noticeEndAt;
	}

	public void setNoticeEndAt(Date noticeEndAt) {
		this.noticeEndAt = noticeEndAt;
	}

	public Date getRechargeStartdAt() {
		return rechargeStartdAt;
	}

	public void setRechargeStartdAt(Date rechargeStartdAt) {
		this.rechargeStartdAt = rechargeStartdAt;
	}

	public Date getRechargeEndAt() {
		return rechargeEndAt;
	}

	public void setRechargeEndAt(Date rechargeEndAt) {
		this.rechargeEndAt = rechargeEndAt;
	}

	public String getDealerName() {
		return dealerName;
	}

	public void setDealerName(String dealerName) {
		this.dealerName = dealerName;
	}

	public String getApplyNo() {
		return applyNo;
	}

	public void setApplyNo(String applyNo) {
		this.applyNo = applyNo;
	}

	public BigDecimal getVoucherInvoiceAmount() {
		return voucherInvoiceAmount;
	}

	public void setVoucherInvoiceAmount(BigDecimal voucherInvoiceAmount) {
		this.voucherInvoiceAmount = voucherInvoiceAmount;
	}

	public BigDecimal getVoucherNotInvoiceAmount() {
		return voucherNotInvoiceAmount;
	}

	public void setVoucherNotInvoiceAmount(BigDecimal voucherNotInvoiceAmount) {
		this.voucherNotInvoiceAmount = voucherNotInvoiceAmount;
	}

	public Long getConsumeId() {
		return consumeId;
	}

	public void setConsumeId(Long consumeId) {
		this.consumeId = consumeId;
	}

	public String[] getConsume() {
		return consume;
	}

	public void setConsume(String[] consume) {
		this.consume = consume;
	}

	public GoodwillStampsPO() {
		super();
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dtoClass
	 *            需要进行转换的dtoClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
		return super.transDtoToPo(dtoClass);
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dto
	 *            需要进行转换的dto
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> void transPoToDto(T dto) {
		BeanMapperUtil.copyProperties(this, dto, "id");
	}

}
