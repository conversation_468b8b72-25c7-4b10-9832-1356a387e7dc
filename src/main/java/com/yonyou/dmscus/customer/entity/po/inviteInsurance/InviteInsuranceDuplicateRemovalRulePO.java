package com.yonyou.dmscus.customer.entity.po.inviteInsurance;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 续保去重规则表，本章表所存储的数据，应该都是一个默认值。
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@TableName("tt_invite_insurance_duplicate_removal_rule")
@Data
public class InviteInsuranceDuplicateRemovalRulePO extends BasePO<InviteInsuranceDuplicateRemovalRulePO> {

    private static final long serialVersionUID = 1L;


    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    @TableField("owner_par_code")
    private String ownerParCode;

    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则 原始默认值90天
     */
    @TableField("rule")
    private Integer rule;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;

    /**
     * 1:设置X天有进厂记录，则不再生成首保、定保、客户流失的邀约
     * 2:设置X天有首保或定保的邀约记录，则易损件、流失客户的邀约合并到首保、定保
     */
    @TableField("rule_type")
    private Integer ruleType;

    public InviteInsuranceDuplicateRemovalRulePO() {
        super();
    }


    @Override
    public String toString() {
        return "InviteInsuranceDuplicateRemovalRulePO{" +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", rule=" + rule +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                ", ruleType=" + ruleType +
                "}";
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
