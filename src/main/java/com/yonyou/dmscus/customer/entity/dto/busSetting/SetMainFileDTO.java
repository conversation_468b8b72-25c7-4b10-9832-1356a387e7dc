package com.yonyou.dmscus.customer.entity.dto.busSetting;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.function.utils.jsonSerializer.date.JsonSimpleDateDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 套餐主档
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
@Data
@ApiModel(value = "套餐主档")
public class SetMainFileDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    @ApiModelProperty(value = "系统ID")
    private String appId;

    /**
     * 所有者代码
     */
    @ApiModelProperty(value = "所有者代码")
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 套餐类别
     */
    @ApiModelProperty(value = "套餐类别")
    private Integer setCategory;

    /**
     * 特约店代码  厂端 VCDC
     */
    @ApiModelProperty(value = "厂端 VCDC")
    private String dealerCode;

    /**
     * 套餐类型
     */
    @ApiModelProperty(value = "套餐类型")
    private Integer setType;

    /**
     * 套餐名称
     */
    @ApiModelProperty(value = "套餐名称")
    private String setName;

    /**
     * 套餐编码
     */
    @ApiModelProperty(value = "套餐编码")
    private String setCode;

    /**
     * 车辆用途
     */
    @ApiModelProperty(value = "车辆用途")
    private Integer vehiclePurpose;

    /**
     * 套餐说明
     */
    @ApiModelProperty(value = "套餐说明")
    private String setExplain;

    /**
     * 启用日期
     */
    @ApiModelProperty(value = "启用日期")
    @JsonDeserialize(using = JsonSimpleDateDeserializer.class)
    private Date enableDate;

    /**
     * 停用日期
     */
    @ApiModelProperty(value = "停用日期")
    @JsonDeserialize(using = JsonSimpleDateDeserializer.class)
    private Date discontinueDate;

    /**
     * 车型
     */
    @ApiModelProperty(value = "车型")
    private String modelName;

    /**
     * 车型代码
     */
    @ApiModelProperty(value = "车型代码")
    private String modelCode;

    /**
     * 发动机代码,多个值用中文逗号分隔
     */
    @ApiModelProperty(value = "发动机代码,多个值用中文逗号分隔")
    private String engineCode;

    /**
     * 是否车型+发动机组合:1 是 0 否
     */
    @ApiModelProperty(value = "是否车型+发动机组合:1 是 0 否")
    private Integer isCombine;



    /**
     * 套餐折扣
     */
    @ApiModelProperty(value = "套餐折扣")
    private BigDecimal setDiscount;

    /**
     * 是否内结套餐
     */
    @ApiModelProperty(value = "是否内结套餐")
    private Integer isInternalSettlement;

    /**
     * 是否可升级套餐
     */
    @ApiModelProperty(value = "是否可升级套餐")
    private Integer isUpgrade;

    /**
     * 是否重复项目
     */
    @ApiModelProperty(value = "是否重复项目")
    private Integer isDuplicateItem;

    /**
     * 套餐适用
     */
    @ApiModelProperty(value = "套餐适用")
    private String setApply;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效  是否启用
     */
    private Integer isValid;



    private String accountGroup;

    /**
     * 话术
     */
    private List<String> talkskill;




    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
