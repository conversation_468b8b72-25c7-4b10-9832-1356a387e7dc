package com.yonyou.dmscus.customer.entity.po.inviteTag;


import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import lombok.Data;

@Data
@TableName("tt_invite_tag")
public class InviteTagPO extends BasePO<InviteTagPO> {

	@TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("name")
    private String name;

    @TableField("tag_desc")
    private String tagDesc;

    @TableField("topic")
    private String topic;

    @TableField("parent_id")
    private Long parentId;

    @TableLogic
    @TableField("is_deleted")
    private Boolean deleted;
}
