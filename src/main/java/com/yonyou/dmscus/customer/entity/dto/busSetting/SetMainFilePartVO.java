package com.yonyou.dmscus.customer.entity.dto.busSetting;

import java.math.BigDecimal;

/**
 * <p>
 * 套餐主档-维修材料
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */

@SuppressWarnings("ALL")
public class SetMainFilePartVO {


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 套餐主档主键ID
     */
    private Long setId;

    /**
     * 项目名称
     */
    private String opName;

    /**
     * 项目代码
     */
    private String opCode;

    /**
     * 零部件名称
     */
    private String partName;

    /**
     * 零部件代码
     */
    private String partCode;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 帐类
     */
    private String type;

    /**
     * 帐类折扣
     */
    private BigDecimal discount;

    /**
     * 是否可升级零部件
     */
    private Integer isUpgrade;

    /**
     * 升级零部件代码 多个用“，”分隔
     */
    private String upgradePart;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;


    /**
     * 升级fg代码 多个用“，”分隔
     */
    private String upgradeFg;


    /**
     * fg
     */
    private String functionCode;


    public SetMainFilePartVO() {
        super();
    }


    public String getAppId() {
        return appId;
    }


    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }


    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerParCode() {
        return ownerParCode;
    }


    public void setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public Integer getOrgId() {
        return orgId;
    }


    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId() {
        return id;
    }


    public void setId(Long id) {
        this.id = id;
    }

    public Long getSetId() {
        return setId;
    }


    public void setSetId(Long setId) {
        this.setId = setId;
    }


    public String getOpName() {
        return opName;
    }

    public void setOpName(String opName) {
        this.opName = opName;
    }

    public String getOpCode() {
        return opCode;
    }

    public void setOpCode(String opCode) {
        this.opCode = opCode;
    }

    public String getPartName() {
        return partName;
    }


    public void setPartName(String partName) {
        this.partName = partName;
    }

    public String getPartCode() {
        return partCode;
    }


    public void setPartCode(String partCode) {
        this.partCode = partCode;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }


    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getType() {
        return type;
    }


    public void setType(String type) {
        this.type = type;
    }

    public BigDecimal getDiscount() {
        return discount;
    }


    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public Integer getIsUpgrade() {
        return isUpgrade;
    }


    public void setIsUpgrade(Integer isUpgrade) {
        this.isUpgrade = isUpgrade;
    }

    public String getUpgradePart() {
        return upgradePart;
    }


    public void setUpgradePart(String upgradePart) {
        this.upgradePart = upgradePart;
    }

    public Integer getDataSources() {
        return dataSources;
    }


    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }


    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid() {
        return isValid;
    }


    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public String getUpgradeFg() {
        return upgradeFg;
    }

    public void setUpgradeFg(String upgradeFg) {
        this.upgradeFg = upgradeFg;
    }

    public String getFunctionCode() {
        return functionCode;
    }

    public void setFunctionCode(String functionCode) {
        this.functionCode = functionCode;
    }
}
