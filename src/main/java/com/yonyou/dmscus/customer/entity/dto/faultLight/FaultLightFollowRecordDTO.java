package com.yonyou.dmscus.customer.entity.dto.faultLight;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class FaultLightFollowRecordDTO implements Serializable {

    private static final long serialVersionUID = 8564089899678836193L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 故障灯线索id
     */
    private Long clueId;

    /**
     * 故障id
     */
    private Long faultId;

    /**
     * 故障城市名称
     */
    private String faultCityName;

    /**
     * 线索下发时间
     */
    private Date clueDisTime;

    /**
     * 跟进时间
     */
    private Date followTime;

    /**
     * 线索下发时间
     */
    private String clueDisTimeStr;

    /**
     * 跟进时间
     */
    private String followTimeStr;

    /**
     * 线索状态: 0.待联络 10.待预约 20.待进店 30.已进店 40.待验证 50.待确认 60.已完成 70.自然进店 80.关闭
     */
    private Integer clueStatus;

    /**
     * 线索状态: 0.待联络 10.待预约 20.待进店 30.已进店 40.待验证 50.待确认 60.已完成 70.自然进店 80.关闭
     */
    private String clueStatusStr;

    /**
     * 跟进状态: 0.待联络 1.联络失败 10.待预约 11.预约失败 20.待进店 21.超时未进店 30.已进店 31.未关联工单 32缺零件 33.车主不修 40.待验证 50.待确认 60.已完成 70.自然进店 80.线索超时
     */
    private Integer followStatus;

    /**
     * 跟进状态: 0.待联络 1.联络失败 10.待预约 11.预约失败 20.待进店 21.超时未进店 30.已进店 31.未关联工单 32缺零件 33.车主不修 40.待验证 50.待确认 60.已完成 70.自然进店 80.线索超时
     */
    private String followStatusStr;

    /**
     * 联络人
     */
    private String followName;

    /**
     * 故障类别
     */
    private String warningName;
}
