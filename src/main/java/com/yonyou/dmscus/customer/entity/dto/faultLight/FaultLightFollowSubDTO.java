package com.yonyou.dmscus.customer.entity.dto.faultLight;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**跟进页面参数*/
@Data
public class FaultLightFollowSubDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -9037186655258054951L;

    /**
     * 线索ID
     */
    private Long id;

    /**
     * 跟进状态
     */
    private Integer followStatus;

    /**
     * 失败备注
     */
    private String failureReason;
    /**
     * 二次预约时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date reInviteTime;
    /**
     * 预计进店时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date forecastTime;

    /**
     * 关联工单
     */
    private String roNo;
}
