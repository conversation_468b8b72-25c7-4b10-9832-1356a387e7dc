package com.yonyou.dmscus.customer.entity.po.voicemanage;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.time.LocalDateTime;

import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * SA呼叫登记
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Data
@TableName("tt_sa_customer_number")
public class SaCustomerNumberPO extends BasePO<SaCustomerNumberPO> {

    private static final long serialVersionUID = 1L;


    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;



    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 邀约ID
     */
    @TableField("invite_id")
    private Long inviteId;

    /**
     * call_id
     */
    @TableField("call_id")
    private String callId;

    /**
     * 服务顾问ID
     */
    @TableField("sa_id")
    private String saId;

    /**
     * 客户名称
     */
    @TableField("cus_name")
    private String cusName;

    /**
     * 客户电话
     */
    @TableField("cus_number")
    private String cusNumber;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;


    /**
     * 经销商代码
     */
    @TableField("dealer_code")
    private String dealerCode;


    /**
     * 服务顾问姓名
     */
    @TableField("sa_name")
    private String saName;

    /**
     * 服务顾问手机号
     */
    @TableField("sa_number")
    private String saNumber;

    /**
     * AI语音工作号
     */
    @TableField("work_number")
    private String workNumber;

    /**
     * 跟进记录ID
     */
    @TableField("detail_id")
    private Long detailId;

    /**
     * 批次号
     */
    @TableField("batch_no")
    private String batchNo;

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
