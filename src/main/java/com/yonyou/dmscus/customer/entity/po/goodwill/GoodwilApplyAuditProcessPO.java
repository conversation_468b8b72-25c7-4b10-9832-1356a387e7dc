package com.yonyou.dmscus.customer.entity.po.goodwill;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善审批流程表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-22
 */
@TableName("tt_goodwil_apply_audit_process")
public class GoodwilApplyAuditProcessPO extends BasePO<GoodwilApplyAuditProcessPO> {

	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	@TableField("APP_ID")
	private String appId;

	/**
	 * 所有者代码
	 */
	@TableField("OWNER_CODE")
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	@TableField("OWNER_PAR_CODE")
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	@TableField("ORG_ID")
	private Integer orgId;

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 亲善单id
	 */
	@TableField("goodwill_apply_id")
	private Long goodwillApplyId;

	/**
	 * 审核对象
	 */
	@TableField("audit_object")
	private Integer auditObject;

	/**
	 * 审批类型
	 */
	@TableField("audit_type")
	private Integer auditType;

	/**
	 * 审批职位
	 */
	@TableField("audit_position")
	private String auditPosition;
	/**
	 * 审批角色
	 */
	@TableField("audit_role")
	private String auditRole;

	/**
	 * 审批顺序
	 */
	@TableField("audit_sort")
	private Integer auditSort;

	/**
	 * 审批状态
	 */
	@TableField("audit_status")
	private Integer auditStatus;

	/**
	 * 审批日期
	 */
	@TableField("audit_date")
	private Date auditDate;

	/**
	 * 是否有效
	 */
	@TableField("is_valid")
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	@TableField("is_deleted")
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	@TableField("created_at")
	private Date createdAt;

	/**
	 * 修改时间
	 */
	@TableField("updated_at")
	private Date updatedAt;

	/**
	 * 剩余审核角色
	 */
	@TableField(exist = false)
	private Integer counts;

	/**
	 * 预申请剩余审核角色
	 */
	@TableField(exist = false)
	private Integer applyCounts;

	public Integer getApplyCounts() {
		return applyCounts;
	}

	public void setApplyCounts(Integer applyCounts) {
		this.applyCounts = applyCounts;
	}

	public Integer getCounts() {
		return counts;
	}

	public void setCounts(Integer counts) {
		this.counts = counts;
	}

	public GoodwilApplyAuditProcessPO() {
		super();
	}

	public String getAuditRole() {
		return auditRole;
	}

	public void setAuditRole(String auditRole) {
		this.auditRole = auditRole;
	}

	@Override
	public String getAppId() {
		return appId;
	}

	@Override
	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	@Override
	public String getOwnerParCode() {
		return ownerParCode;
	}

	@Override
	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getGoodwillApplyId() {
		return goodwillApplyId;
	}

	public void setGoodwillApplyId(Long goodwillApplyId) {
		this.goodwillApplyId = goodwillApplyId;
	}

	public Integer getAuditObject() {
		return auditObject;
	}

	public void setAuditObject(Integer auditObject) {
		this.auditObject = auditObject;
	}

	public Integer getAuditType() {
		return auditType;
	}

	public void setAuditType(Integer auditType) {
		this.auditType = auditType;
	}

	public String getAuditPosition() {
		return auditPosition;
	}

	public void setAuditPosition(String auditPosition) {
		this.auditPosition = auditPosition;
	}

	public Integer getAuditSort() {
		return auditSort;
	}

	public void setAuditSort(Integer auditSort) {
		this.auditSort = auditSort;
	}

	public Integer getAuditStatus() {
		return auditStatus;
	}

	public void setAuditStatus(Integer auditStatus) {
		this.auditStatus = auditStatus;
	}

	public Date getAuditDate() {
		return auditDate;
	}

	public void setAuditDate(Date auditDate) {
		this.auditDate = auditDate;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	@Override
	public Date getCreatedAt() {
		return createdAt;
	}

	@Override
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Override
	public Date getUpdatedAt() {
		return updatedAt;
	}

	@Override
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	protected Serializable pkVal() {
		return this.id;
	}

	@Override
	public String toString() {
		return "GoodwilApplyAuditProcessPO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", goodwillApplyId=" + goodwillApplyId
				+ ", auditObject=" + auditObject + ", auditType=" + auditType + ", auditPosition=" + auditPosition
				+ ", auditSort=" + auditSort + ", auditStatus=" + auditStatus + ", auditDate=" + auditDate
				+ ", isValid=" + isValid + ", isDeleted=" + isDeleted + ", createdAt=" + createdAt + ", updatedAt="
				+ updatedAt + "}";
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dtoClass
	 *            需要进行转换的dtoClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
		return super.transDtoToPo(dtoClass);
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dto
	 *            需要进行转换的dto
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> void transPoToDto(T dto) {
		BeanMapperUtil.copyProperties(this, dto, "id");
	}

}
