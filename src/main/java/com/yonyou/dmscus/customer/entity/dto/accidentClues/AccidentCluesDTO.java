package com.yonyou.dmscus.customer.entity.dto.accidentClues;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dto.AccidentCluesUserDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@ApiModel(value="AccidentCluesDTO",description="事故线索")
@Data
public class AccidentCluesDTO extends BaseDTO implements Serializable {

  @ApiModelProperty(value = "系统ID",name="appId")
  private String appId;

  @ApiModelProperty(value = "所有者代码",name="ownerCode")
  private String ownerCode;

  @ApiModelProperty(value = "所有者的父组织代码",name="ownerParCode")
  private String ownerParCode;

  @ApiModelProperty(value = "组织ID",name="orgId")
  private Integer orgId;

  @ApiModelProperty(value = "主键ID",name="acId")
  private Integer acId;

  @ApiModelProperty("主键list")
  private List<Integer> acIdList;

  @ApiModelProperty(value = "经销商license代码",name="dealerCode")
  private String dealerCode;

  @ApiModelProperty("经销商名")
  private String dealerName;

  @ApiModelProperty(value = "车牌号",name="license")
  private String license;

  @ApiModelProperty(value = "车型code",name="modelsId")
  private String modelsId;

  @ApiModelProperty(value = "保险公司id",name="insuranceCompanyId")
  private String insuranceCompanyId;

  @ApiModelProperty(value = "保险公司名称",name="insuranceCompanyName")
  private String  insuranceCompanyName;

  @ApiModelProperty(value = "线索来源",name="cluesResource")
  private Integer cluesResource;

  @ApiModelProperty("线索来源（多选）")
  private List<Integer> cluesResourceList;

  @ApiModelProperty(value = "联系人",name="contacts")
  private String contacts;

  @ApiModelProperty(value = "联系人电话",name="contactsPhone")
  private String contactsPhone;

  @ApiModelProperty(value = "报案时间",name="reportDate")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date reportDate;

  @ApiModelProperty(value = "事故地点",name="accidentAddress")
  private String accidentAddress;

  @ApiModelProperty(value = "事故类型",name="accidentType")
  private Integer accidentType;

  @ApiModelProperty(value = "是否有人受伤",name="isBruise")
  private Integer isBruise;

  @ApiModelProperty(value = "外拓费用",name="outsideAmount")
  private BigDecimal outsideAmount;

  @ApiModelProperty(value = "跟进人员id",name="followPeople")
  private Integer followPeople;

  @ApiModelProperty(value = "跟进人员name",name="followPeopleName")
  private String  followPeopleName;

  @ApiModelProperty(value = "备注",name="remark")
  private String remark;

  @ApiModelProperty(value = "是否本店承保",name="isInsured")
  private Integer isInsured;

  @ApiModelProperty(value = "事故责任",name="accidentDuty")
  private Integer accidentDuty;

  @ApiModelProperty(value = "跟进状态",name="followStatus")
  private Integer followStatus;

  @ApiModelProperty(value = "线索状态",name="cluesStatus")
  private Integer cluesStatus;

  @ApiModelProperty(value = "进厂经销商",name="intoDealerCode")
  private String intoDealerCode;

  @ApiModelProperty(value = "进厂时间",name="intoDealerDate")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date intoDealerDate;

  @ApiModelProperty(value = "进厂工单号",name="intoRoNo")
  private String intoRoNo;

  @ApiModelProperty(value = "下次跟进时间",name="nextFollowDate")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date nextFollowDate;

  @ApiModelProperty(value = "是否预约",name="isAppointment")
  private Integer isAppointment;

  @ApiModelProperty(value = "跟进次数",name="followCount")
  private Integer followCount;

  @ApiModelProperty(value = "线索类型",name="cluesType")
  private String cluesType;

  @ApiModelProperty(value = "数据来源",name="dataSources")
  private Integer dataSources;

  @ApiModelProperty(value = "是否删除，1：删除，0：未删除",name="isDeleted")
  private Integer isDeleted;

  @ApiModelProperty(value = "是否有效",name="isValid")
  private Integer isValid;

  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @ApiModelProperty(value = "创建时间",name="createdAt")
  private Date createdAt;

  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  @ApiModelProperty("线索创建/推送时间起始")
  private Date createdDateStart;

  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  @ApiModelProperty("线索创建/推送时间结束")
  private Date createdDateEnd;

  @ApiModelProperty(value = "创建人",name="createdBy")
  private String createdBy;

  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @ApiModelProperty(value = "更新时间",name="updatedAt")
  private Date updatedAt;

  @ApiModelProperty(value = "更新人",name="updatedBy")
  private String updatedBy;

  @ApiModelProperty(value = "版本号（乐观锁）",name="recordVersion")
  private Integer recordVersion;

  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date reportDateStart;

  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date reportDateEnd;


  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date nextFollowDateStart;

  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date nextFollowDateEnd;

  @ApiModelProperty(value = "单/双方事故",name="doubleAccident")
  private Integer doubleAccident;

  //店端/厂端（vcdc）
  private String source;


  @ApiModelProperty(value = "售后大区ID")
  private Long    afterBigAreaId;

  @ApiModelProperty(value = "售后大区名称")
  private String  afterBigAreaName;

  @ApiModelProperty(value = "售后小区ID")
  private Long    afterSmallAreaId;

  @ApiModelProperty(value = "售后小区名称")
  private String  afterSmallAreaName;

  @ApiModelProperty(value = "分配状态",name="allotStatus")
  private Integer allotStatus;

  private String isself;

  private Integer oneId;

  private Integer currentPage;

  private Integer pageSize;

  @ApiModelProperty(value = "车架号")
  private String vin;

  @ApiModelProperty(value = "联系人列表")
  private List<AccidentClueContact> contactList;

  @ApiModelProperty(value = "线索图片",example = "uid1,uid2,uid3")
  private String pictures;

  @ApiModelProperty(value = "车主姓名")
  private String ownerName;

  @ApiModelProperty(value = "车主手机号")
  private String mobile;

  /*
  * 短信识别入参
  * */
  @ApiModelProperty(value = "短信识别入参")
  private String param;

  /**
   * 预约单号
   */
  @ApiModelProperty(value = "预约单号")
  private String bookingOrderNo;

  @ApiModelProperty(value = "服务顾问")
  private String serviceAdvisor;

  @ApiModelProperty(value = "客户需求")
  private String customerDesc;

  @ApiModelProperty(value = "预约来源")
  private Integer bookingSource;

  @ApiModelProperty("创建来源是否app")
  private boolean sourceApp;



  @ApiModelProperty(value = "线索ID")
  @JsonSerialize(using = ToStringSerializer.class)
  private Long crmId;

  @ApiModelProperty(value = "报案号")
  private String registNo;

  @ApiModelProperty("是否重复")
  private Integer repeatLead;

  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @ApiModelProperty(value = "出险时间")
  private Date accidentDate;

  @ApiModelProperty(value = "出险内容")
  private String accidentReason;

  @ApiModelProperty(value = "客户类型")
  private String contactsName;

  @ApiModelProperty(value = "进线类型")
  private String callType;

  @ApiModelProperty(value = "是否报警")
  private Integer callPoliceFlag;

  @ApiModelProperty(value = "创建渠道,YB，400，Newbie")
  private String sourceChannel;

  @ApiModelProperty("线索状态集合")
  private List<Integer> cluesStatusList;

  @ApiModelProperty(value = "跟进状态集合",name="followStatusList")
  private List<Integer> followStatusList;



  @ApiModelProperty(value = "车型名称")
  private String modelName;
  @ApiModelProperty(value = "联系方式-手机号")
  private String contactsInformation;

  @ApiModelProperty(value = "是否虚拟号码")
  private Integer virtualPhoneFlag;

  @ApiModelProperty(value = "CDP车主信息")
  private List<AccidentCluesUserDto> cdpUserInfos;

  @ApiModelProperty(value = "工单送修人信息")
  private List<AccidentCluesUserDto> orderUserInfos;

  @ApiModelProperty(value = "来源渠道")
  private String insuranceSource;

  @ApiModelProperty(value = "线索异常状态")
  private Integer dataStatus;

  @ApiModelProperty(value = "省ID")
  private Long    provinceId;

  @ApiModelProperty(value = "市ID")
  private Long    cityId;

  @ApiModelProperty(value = "经销商CODE")
  private List<String>    dealerCodeList;

  @ApiModelProperty(value = "来源渠道")
  private List<String> insuranceSourceList;

  @ApiModelProperty(value = "deviceId",name = "设备id",hidden = true)
  private String deviceId;

  @ApiModelProperty(value = "gsensor",name = "G-sensor曲线",hidden = true)
  private String gsensor;

  @ApiModelProperty(value = "dvrStore",name = "DVR销售门店",hidden = true)
  private String dvrStore;


  @Override
  public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
    return super.transDtoToPo(poClass);
  }

  public void setDefault() {
      Optional.ofNullable(this.getDealerCode()).orElseThrow(()->new ServiceBizException("经销商代码不能为空"));
      Optional.ofNullable(this.getLicense()).orElseThrow(()->new ServiceBizException("车牌号不能为空"));
      Optional.ofNullable(this.getReportDate()).orElseThrow(()->new ServiceBizException("报案时间不能为空"));
      Optional.ofNullable(this.getInsuranceCompanyId()).orElseThrow(()->new ServiceBizException("保险公司id不能为空"));
      Optional.ofNullable(this.getInsuranceCompanyName()).orElseThrow(()->new ServiceBizException("保险公司名称不能为空"));
      Optional.ofNullable(this.getContactsPhone()).orElseThrow(()->new ServiceBizException("联系人电话不能为空"));
      this.cluesResource=CommonConstants.CLUES_RESOURCE_BXGSTS;
      if(accidentType==null){
          this.accidentType=CommonConstants.ACCIDENT_TYPE_QW;
      }
      if(isBruise==null){
        isBruise=CommonConstants.DICT_IS_NO;
      }

  }
}
