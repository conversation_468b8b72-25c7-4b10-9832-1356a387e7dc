package com.yonyou.dmscus.customer.entity.dto.goodwill;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 亲善预申请子表——维修零配件信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
    
public class GoodwillApplyRepairPartInfoDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

        
    /**
     * 系统ID
     */
                private String appId;
                
    /**
     * 所有者代码
     */
                private String ownerCode;
                
    /**
     * 所有者的父组织代码
     */
                private String ownerParCode;
                
    /**
     * 组织ID
     */
                private Integer orgId;
                
    /**
     * 主键id
     */
                private Long id;
                
    /**
     * 亲善预申请表id
     */
                private Long goodwillApplyId;
                
    /**
     * 工单号
     */
                private String repairOrderNo;
                
    /**
     * 零件号
     */
                private String partNo;
                
    /**
     * 零件名称
     */
                private String partName;
                
    /**
     * 数量
     */
                private BigDecimal quantity;
                
    /**
     * 订货日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date orderDate;
                
    /**
     * 到货日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date arrivalDate;
                
    /**
     * 备注
     */
                private String remark;
                
    /**
     * 是否有效
     */
                private Integer isValid;
                
    /**
     * 是否删除:1,删除；0,未删除
     */
                private Boolean isDeleted;
                
    /**
     * 创建时间
     */
    private Date createdAt;
                
    /**
     * 修改时间
     */
    private Date updatedAt;
            
    public GoodwillApplyRepairPartInfoDTO() {
        super();
    }

                                
    public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
            }
                                
    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
            }
                                
    public String getOwnerParCode(){
        return ownerParCode;
    }


    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
            }
                                
    public Integer getOrgId(){
        return orgId;
    }


    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
            }
                                            
    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
            }
                                
    public Long getGoodwillApplyId(){
        return goodwillApplyId;
    }


    public void  setGoodwillApplyId(Long goodwillApplyId) {
        this.goodwillApplyId = goodwillApplyId;
            }
                                
    public String getRepairOrderNo(){
        return repairOrderNo;
    }


    public void  setRepairOrderNo(String repairOrderNo) {
        this.repairOrderNo = repairOrderNo;
            }
                                
    public String getPartNo(){
        return partNo;
    }


    public void  setPartNo(String partNo) {
        this.partNo = partNo;
            }
                                
    public String getPartName(){
        return partName;
    }


    public void  setPartName(String partName) {
        this.partName = partName;
            }
                                
    public BigDecimal getQuantity(){
        return quantity;
    }


    public void  setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
            }
                                

                                
    public Date getOrderDate() {
		return orderDate;
	}


	public void setOrderDate(Date orderDate) {
		this.orderDate = orderDate;
	}


	public Date getArrivalDate() {
		return arrivalDate;
	}


	public void setArrivalDate(Date arrivalDate) {
		this.arrivalDate = arrivalDate;
	}


	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}


	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}


	public String getRemark(){
        return remark;
    }


    public void  setRemark(String remark) {
        this.remark = remark;
            }
                                
    public Integer getIsValid(){
        return isValid;
    }


    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
            }
                                
    
    @Override
    public String toString() {
        return "GoodwillApplyRepairPartInfoDTO{" +
                                            "appId=" + appId +
                                                            ", ownerCode=" + ownerCode +
                                                            ", ownerParCode=" + ownerParCode +
                                                            ", orgId=" + orgId +
                                                            ", id=" + id +
                                                            ", goodwillApplyId=" + goodwillApplyId +
                                                            ", repairOrderNo=" + repairOrderNo +
                                                            ", partNo=" + partNo +
                                                            ", partName=" + partName +
                                                            ", quantity=" + quantity +
                                                            ", orderDate=" + orderDate +
                                                            ", arrivalDate=" + arrivalDate +
                                                            ", remark=" + remark +
                                                            ", isValid=" + isValid +
                                                            ", isDeleted=" + isDeleted +
                                                            ", createdAt=" + createdAt +
                                                            ", updatedAt=" + updatedAt +
                                    "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
