package com.yonyou.dmscus.customer.entity.po.parse;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

public class SimplePo {


    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;
    /**
     * 创建sql人
     */
    @ApiModelProperty("创建sql人")
    @TableField("create_sqlby")
    private String createSqlby;

    /**
     * 更新人sql人
     */
    @ApiModelProperty("更新人sql人")
    @TableField("update_sqlby")
    private String updateSqlby;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


    public String getCreateSqlby() {
        return createSqlby;
    }

    public void setCreateSqlby(String createSqlby) {
        this.createSqlby = createSqlby;
    }

    public String getUpdateSqlby() {
        return updateSqlby;
    }

    public void setUpdateSqlby(String updateSqlby) {
        this.updateSqlby = updateSqlby;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDeleted() {
        return isDeleted;
    }

    public void setDeleted(Boolean deleted) {
        isDeleted = deleted;
    }
}
