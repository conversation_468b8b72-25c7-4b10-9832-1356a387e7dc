package com.yonyou.dmscus.customer.entity.po.invitationautocreate;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;


@Data
public class InvVehLossCleanTaskPO {

    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    private Long id;

    /**
     * 经销商代码
     */
    private String dealerCode;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 建议进厂日期
     */
    private Date adviseInDate;

    /**
     * 生成邀约时间
     */
    //private Date createInviteTime;

    /**
     * 基准时间(工单结算时间)
     */
    private Date inviteTime;

    /**
     * 车牌号
     */
    private String licensePlateNum;

    /**
     * 车主名称
     */
    private String name;

    /**
     * 车主电话
     */
    private String tel;

    /**
     * 车主年龄
     */
    private String age;

    /**
     * 车主性别
     */
    private String sex;

    /**
     * 车型
     */
    private String model;

    /**
     * 日均里程
     */
    private Double dailyMileage;

    /**
     * 再提醒间隔（月）
     */
    private Integer remindInterval;

    /**
     * 超时关闭时间间隔（月）
     */
    private Integer closeInterval;

    /**
     *建议入厂里程
     */
    private Integer adviseInMileage;

    /**
     *基准里程
     */
    private Integer outMileage;

}
