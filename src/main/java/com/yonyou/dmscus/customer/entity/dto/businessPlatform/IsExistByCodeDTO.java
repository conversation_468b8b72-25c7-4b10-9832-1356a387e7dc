package com.yonyou.dmscus.customer.entity.dto.businessPlatform;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import java.io.Serializable;
import java.util.List;

public class IsExistByCodeDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String companyCode;


    private Boolean isAllExist;

    private List<String> notExistCompanyCodeList;

    public Boolean getIsAllExist() {
        return isAllExist;
    }

    public void setIsAllExist(Boolean isAllExist) {
        this.isAllExist = isAllExist;
    }

    public List<String> getNotExistCompanyCodeList() {
        return notExistCompanyCodeList;
    }

    public void setNotExistCompanyCodeList(List<String> notExistCompanyCodeList) {
        this.notExistCompanyCodeList = notExistCompanyCodeList;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }
}
