package com.yonyou.dmscus.customer.entity.po.inviteInsurance;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 车辆邀约续保任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@TableName("tt_invite_insurance_vehicle_task")
@Data
@ToString
public class InviteInsuranceVehicleTaskPO extends BasePO<InviteInsuranceVehicleTaskPO> {

    private static final long serialVersionUID = 1L;

    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    @TableField("owner_par_code")
    private String ownerParCode;

    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 车架号
     */
    @TableField("vin")
    private String vin;

    /**
     * 车牌号
     */
    @TableField("license_plate_num")
    private String licensePlateNum;

    /**
     * 经销商代码
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 车主名称
     */
    @TableField("name")
    private String name;

    /**
     * 车主电话
     */
    @TableField("tel")
    private String tel;

    /**
     * 车主年龄
     */
    @TableField("age")
    private String age;

    /**
     * 车主性别
     */
    @TableField("sex")
    private String sex;
    @TableField("model")
    private String model;

    /**
     * 日均里程
     */
    @TableField("daily_mileage")
    private Integer dailyMileage;

    /**
     * 建议进厂日期
     */
    @TableField("advise_in_date")
    private Date adviseInDate;

    /**
     * 建议进厂日期更新时间
     */
    @TableField("advise_in_date_update_time")
    private Date adviseInDateUpdateTime;

    /**
     * 邀约类型
     */
    @TableField("invite_type")
    private Integer inviteType;

    /**
     * 提前N天邀约
     */
    @TableField("day_in_advance")
    private Integer dayInAdvance;

    /**
     * 再提醒间隔（月）
     */
    @TableField("remind_interval")
    private Integer remindInterval;

    /**
     * 邀约规则类型(预留)：1 首保、定保、保险、客户流失(对应invite_rule表)，2 易损件、项目(对应invite_rule表)
     */
    @TableField("invite_rule_type")
    private Boolean inviteRuleType;

    /**
     * 邀约规则ID(预留)，用于计算再次邀约时间。（关联invite_rule或invite_part_item_rule中id）
     */
    @TableField("invite_rule_id")
    private Long inviteRuleId;

    /**
     * 是否已生成邀约线索：1 已生成，0 未生成 ,2不再生成,3跟进失败再次待生成
     */
    @TableField("is_create_invite")
    private Integer isCreateInvite;

    /**
     * 邀约ID （关联invite_vehicle_record中id）
     */
    @TableField("invite_id")
    private Long inviteId;

    /**
     * 生成邀约时间
     */
    @TableField("create_invite_time")
    private Date createInviteTime;

    /**
     * 跟进状态
     */
    @TableField("follow_status")
    private Integer followStatus;

    /**
     * 再次邀约时间
     */
    @TableField("reinvite_time")
    private Date reinviteTime;

    /**
     * 失效原因
     */
    @TableField("invalid_reason")
    private String invalidReason;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;

    @TableField("last_change_date")
    private LocalDate lastChangeDate;

    /**
     * 计算 建议进厂时间 的基准时间 ,用于日均里程更新时重新计算建议进厂时间
     */
    @TableField("invite_time")
    private Date inviteTime;

    /**
     * 类型：易损件、项目
     */
    @TableField("item_type")
    private Integer itemType;

    /**
     * 零件、维修项目编号
     */
    @TableField("item_code")
    private String itemCode;

    /**
     * 零件、维修项目名称
     */
    @TableField("item_name")
    private String itemName;

    /**
     * QB号
     */
    @TableField("qb_number")
    private String qbNumber;

    /**
     * 超时关闭时间
     */
    @TableField("close_interval")
    private Integer closeInterval;

    /**
     * 超时关闭次数
     */
    @TableField("close_times")
    private Integer closeTimes;

    /**
     * 建议入厂里程
     */
    @TableField("advise_in_mileage")
    private Integer adviseInMileage;

    /**
     * 续保客户类型(81761001 新保客户  81761002 新转续  81761003  续转续   81761004 在修不在保)
     */
    @TableField("insurance_type")
    private Integer insuranceType;

    /**
     *保险投保单(tt_insurance_bill)表主键id'
     */
    @TableField("insurance_bill_id")
    private Long insuranceBillId;

    /**
     *线索类型：1:交强险   2:商业险'
     */
    @TableField("clue_type")
    private Integer clueType;

    /**
     *原线索记录新生的投保单号'
     */
    @TableField("new_insure_no")
    private String newInsureNo;

    public InviteInsuranceVehicleTaskPO() {
        super();
    }


    @Override
    protected Serializable pkVal() {
        return this.id;
    }


    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
