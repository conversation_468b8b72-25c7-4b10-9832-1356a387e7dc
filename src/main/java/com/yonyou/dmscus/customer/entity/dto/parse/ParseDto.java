package com.yonyou.dmscus.customer.entity.dto.parse;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(value = "文本识别解析规则")
public class ParseDto {

    private Long id;

    private Long parseId;

    /*
     * 正则
     * */
    private String matchingRule;

    /*
     * 解析后的文本
     * */
    private String ruleText;

    /*
     * 字段名称
     * */
    private String fieldName;

    /*
    * 解析地址
    * */
    private Integer parseIndex;




}
