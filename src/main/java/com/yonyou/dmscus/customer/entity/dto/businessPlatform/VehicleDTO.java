package com.yonyou.dmscus.customer.entity.dto.businessPlatform;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


@Data
public class VehicleDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 车架号
     */
    private String vin;


    /**
     * 行驶里程
     */
    private Integer mileage;



    /**
     * 发动机
     */
    private String engineNo;


    /**
     * 保修到期日
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date invoiceEndDate;

    /**
     * 开票日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date invoiceDate;


    /**
     * 车型
     */
    private String modelCode;


    /**
     * 店代码
     */
    private String dealerCode;






}
