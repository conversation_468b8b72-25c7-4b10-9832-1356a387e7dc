package com.yonyou.dmscus.customer.entity.dto.goodwill;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * UseRule
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-30
 */
@ApiModel(value = "UseRule", description = "UseRule")
public class UseRule {
	@ApiModelProperty(value = "限制经销商(空表示全部)")
	private List<String> limitDealer;
	@ApiModelProperty(value = "维修类型代码(空表示全部)（G:保修 I:事故 M:保养 N:机电维修 P:PDS S:零售）")
	private List<String> limitRepairType;
	@ApiModelProperty(value = "使用门槛（空表示无门槛）")
	private Double useThreshold;
	@ApiModelProperty(value = "可叠加卡券（空表示无可叠加卡券）")
	private List<String> overlapCoupon;
	@ApiModelProperty(value = "需要激活（1：是）")
	private Integer needActive;
	@ApiModelProperty(value = "车型（空表示全部车型 否则传入车型code列表）")
	private List<String> model;
	@ApiModelProperty(value = "适用车辆范围（车辆vin列表）")
	private List<String> vehicleRange;

	@ApiModelProperty(value = "有效类目（L1[],L2[],L3[]）")
	private List<List<String>> validCategory;

	@ApiModelProperty(value = "激活条件（字典）")
	private Integer activationConditions;
	@ApiModelProperty(value = "使用规则(最小车龄-含)")
	private Integer useruleMincarage;
	@ApiModelProperty(value = "使用规则(最大车龄-不含)")
	private Integer useruleMaxcarage;
	// @ApiModelProperty(value = "使用规则(高级设置)")
	// private UseRuleAdvancedSetting useruleAdvancedsettings;

	public List<String> getLimitDealer() {
		return limitDealer;
	}

	public void setLimitDealer(List<String> limitDealer) {
		this.limitDealer = limitDealer;
	}

	public List<String> getLimitRepairType() {
		return limitRepairType;
	}

	public void setLimitRepairType(List<String> limitRepairType) {
		this.limitRepairType = limitRepairType;
	}

	public Double getUseThreshold() {
		return useThreshold;
	}

	public void setUseThreshold(Double useThreshold) {
		this.useThreshold = useThreshold;
	}

	public List<String> getOverlapCoupon() {
		return overlapCoupon;
	}

	public void setOverlapCoupon(List<String> overlapCoupon) {
		this.overlapCoupon = overlapCoupon;
	}

	public Integer getNeedActive() {
		return needActive;
	}

	public void setNeedActive(Integer needActive) {
		this.needActive = needActive;
	}

	public List<String> getModel() {
		return model;
	}

	public void setModel(List<String> model) {
		this.model = model;
	}

	public List<String> getVehicleRange() {
		return vehicleRange;
	}

	public void setVehicleRange(List<String> vehicleRange) {
		this.vehicleRange = vehicleRange;
	}

	public List<List<String>> getValidCategory() {
		return validCategory;
	}

	public void setValidCategory(List<List<String>> validCategory) {
		this.validCategory = validCategory;
	}

	public Integer getActivationConditions() {
		return activationConditions;
	}

	public void setActivationConditions(Integer activationConditions) {
		this.activationConditions = activationConditions;
	}

	public Integer getUseruleMincarage() {
		return useruleMincarage;
	}

	public void setUseruleMincarage(Integer useruleMincarage) {
		this.useruleMincarage = useruleMincarage;
	}

	public Integer getUseruleMaxcarage() {
		return useruleMaxcarage;
	}

	public void setUseruleMaxcarage(Integer useruleMaxcarage) {
		this.useruleMaxcarage = useruleMaxcarage;
	}

	// public UseRuleAdvancedSetting getUseruleAdvancedsettings() {
	// return useruleAdvancedsettings;
	// }
	//
	// public void setUseruleAdvancedsettings(UseRuleAdvancedSetting
	// useruleAdvancedsettings) {
	// this.useruleAdvancedsettings = useruleAdvancedsettings;
	// }
}
