package com.yonyou.dmscus.customer.entity.po.pushRecord;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
/**
 * <AUTHOR>
 * @Date 2023/11/16 18:17
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tt_remote_push_record")
public class RemotePushRecordPO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 场景类型   1.预约单推送预约中心，2.事故线索推送LiteCRM，3、预约单号推送线索服务
     */
    @TableField("since_type")
    private Integer sinceType;

    /**
     * 子场景类型   1.事故线索，2、事故线索未跟进，3、事故线索跟进中，4、事故线索跟进成功，5、事故线索跟进失败
     */
    @TableField("sub_since_type")
    private Integer subSinceType;

    /**
     * 业务主键或业务编码
     */
    @TableField("biz_no")
    private String bizNo;

    /**
     * 子业务编码
     */
    @TableField("sub_biz_no")
    private String subBizNo;

    /**
     * 请求参数
     */
    @TableField("req_params")
    private String reqParams;

    /**
     * 重试次数,默认3次
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 最后一次重试时间
     */
    @TableField("last_retry_time")
    private Date lastRetryTime;

    /**
     * 响应码
     */
    @TableField("resp_code")
    private String respCode;

    /**
     * 错误消息 截取200长度字符
     */
    @TableField("resp_content")
    private String respContent;

    /**
     * 任务状态:  0:待处理 1.处理成功 2.处理失败 -1:重试最大次数异常
     */
    @TableField("task_status")
    private Integer taskStatus;

    /**
     * 删除标识（0-未删除，1-已删除）
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 创建sql人
     */
    @TableField("create_sqlby")
    private String createSqlby;

    /**
     * 更新sql人
     */
    @TableField("update_sqlby")
    private String updateSqlby;
}
