package com.yonyou.dmscus.customer.entity.dto.vocmileage;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * VOC里程基础表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-08
 */

public class VocMileageDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 系统ID
     */
    private String            appId;

    /**
     * 所有者代码
     */
    private String            ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String            ownerParCode;

    /**
     * 组织ID
     */
    private Integer           orgId;

    /**
     * 主键ID
     */
    private Long              id;

    /**
     * VIN
     */
    private String            vin;

    /**
     * 里程(米)
     */
    private Integer           mileageM;

    /**
     * 里程(公里)
     */
    private Integer           mileageKm;

    /**
     * 获取里程时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date     getTime;
    
    /**获取里程开始时间范围*/
    private String   getTimeBegin;
    
    /**获取里程结束时间范围*/
    private String   getTimeEnd;
    
    /**
     * 导入账号
     */
    private String            createdCode;

    /**
     * 数据来源 (1：界面新增 2：Excel导入)
     */
    private Integer           dataSources;

    /**
     * 是否删除
     */
    private Boolean           isDeleted;

    /**
     * 是否有效 (10041001：有效 10041002：无效)
     */
    private Integer           isValid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date     createdAt;
    
    /**导入开始时间范围*/
    private String   createdAtBegin;
    
    /**导入截止时间范围*/
    private String   createdAtEnd;
    
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date     updatedAt;

    public VocMileageDTO(){
        super();
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerParCode() {
        return ownerParCode;
    }

    public void setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public Integer getMileageM() {
        return mileageM;
    }

    public void setMileageM(Integer mileageM) {
        this.mileageM = mileageM;
    }

    public Integer getMileageKm() {
        return mileageKm;
    }

    public void setMileageKm(Integer mileageKm) {
        this.mileageKm = mileageKm;
    }

    public Date getGetTime() {
        return getTime;
    }

    public void setGetTime(Date getTime) {
        this.getTime = getTime;
    }

    public String getCreatedCode() {
        return createdCode;
    }

    public void setCreatedCode(String createdCode) {
        this.createdCode = createdCode;
    }

    public Integer getDataSources() {
        return dataSources;
    }

    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    
    /**
     * @return the getTimeBegin
     */
    public String getGetTimeBegin() {
        return getTimeBegin;
    }

    
    /**
     * @param getTimeBegin the getTimeBegin to set
     */
    public void setGetTimeBegin(String getTimeBegin) {
        this.getTimeBegin = getTimeBegin;
    }

    
    /**
     * @return the getTimeEnd
     */
    public String getGetTimeEnd() {
        return getTimeEnd;
    }

    
    /**
     * @param getTimeEnd the getTimeEnd to set
     */
    public void setGetTimeEnd(String getTimeEnd) {
        this.getTimeEnd = getTimeEnd;
    }

    
    /**
     * @return the createdAtBegin
     */
    public String getCreatedAtBegin() {
        return createdAtBegin;
    }

    
    /**
     * @param createdAtBegin the createdAtBegin to set
     */
    public void setCreatedAtBegin(String createdAtBegin) {
        this.createdAtBegin = createdAtBegin;
    }

    
    /**
     * @return the createdAtEnd
     */
    public String getCreatedAtEnd() {
        return createdAtEnd;
    }

    
    /**
     * @param createdAtEnd the createdAtEnd to set
     */
    public void setCreatedAtEnd(String createdAtEnd) {
        this.createdAtEnd = createdAtEnd;
    }

    @Override
    public String toString() {
        return "VocMileageDTO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode=" + ownerParCode
               + ", orgId=" + orgId + ", id=" + id + ", vin=" + vin + ", mileageM=" + mileageM + ", mileageKm="
               + mileageKm + ", getTime=" + getTime + ", createdCode=" + createdCode + ", dataSources=" + dataSources
               + ", isDeleted=" + isDeleted + ", isValid=" + isValid + ", createdAt=" + createdAt + ", updatedAt="
               + updatedAt + "}";
    }

    /**
     * 将DTO 转换为PO //对某个对象属性进行赋值 //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * 
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * 
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
