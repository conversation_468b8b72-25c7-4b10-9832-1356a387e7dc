package com.yonyou.dmscus.customer.entity.dto.complaint.v51dk;

import lombok.Data;

@Data
public class V51dkComplaintInfoSyncDTO {

    /**
     *来电时间
     */
    private String callTime;

    /**
     *车架号
     */
    private String vin;

    /**
     *购车时间
     */
    private String buyTime;

    /**
     *车型
     */
    private String model;

    /**
     *年款
     */
    private String modelYear;

    /**
     *购买经销商代码
     */
    private String buyDealerCode;

    /**
     *处理经销商代码
     */
    private String dealerCode;

    /**
     *里程
     */
    private Integer mileage;

    /**
     *投诉主题
     */
    private String subject;

    /**
     *问题描述
     */
    private String problem;

    /**
     *售前案件单类别一级层
     */
    private String category1;

    /**
     *售前案件单类别二级层
     */
    private String category2;

    /**
     *售前案件单类别三级层
     */
    private String category3;

    /**
     *投诉id
     */
    private String complaintId;

    /**
     * 工单状态
     */
    private String jobOrderStatus;

    /**
     *工单性质
     */
    private String nature;
}
