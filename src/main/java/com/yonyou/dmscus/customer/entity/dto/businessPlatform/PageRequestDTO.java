package com.yonyou.dmscus.customer.entity.dto.businessPlatform;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * @param <T>
 * <AUTHOR>
 */
@Data
@ApiModel(description = "分页请求")
public class PageRequestDTO<T> extends RequestDTO<T> {
    @ApiModelProperty(value = "当前页")
    private Long page;

    @ApiModelProperty(value = "每页数量")
    private Long pageSize;

}
