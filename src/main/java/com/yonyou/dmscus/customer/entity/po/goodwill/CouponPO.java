package com.yonyou.dmscus.customer.entity.po.goodwill;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 卡券
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@TableName("tt_coupon")
public class CouponPO extends BasePO<CouponPO> {

    private static final long serialVersionUID = 1L;

    /**
     * 系统id
     */
    @TableField("app_id")
    private String appId;

    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    @TableField("owner_par_code")
    private String ownerParCode;

    /**
     * 组织id
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 卡券编号
     */
    @TableField("coupon_code")
    private String couponCode;

    /**
     * 卡券名称
     */
    @TableField("coupon_name")
    private String couponName;

    /**
     * 卡券类型（抵扣券，代金券）
     */
    @TableField("coupon_type")
    private Integer couponType;

    /**
     * 卡券状态（未提交，提交待确认，已确认，已过期，已停用）
     */
    @TableField("coupon_status")
    private Integer couponStatus;

    /**
     * 有效期类型（期限，时间）
     */
    @TableField("validity_period_type")
    private Integer validityPeriodType;

    /**
     * 自领取日起（天）
     */
    @TableField(value = "validity_period_days",updateStrategy = FieldStrategy.IGNORED)
    private Integer validityPeriodDays;

    /**
     * 卡券开始日期
     */
    @TableField(value = "coupon_begin_date",updateStrategy = FieldStrategy.IGNORED)
    private Date couponBeginDate;

    /**
     * 卡券结束日期
     */
    @TableField(value = "coupon_end_date",updateStrategy = FieldStrategy.IGNORED)
    private Date couponEndDate;

    /**
     * 上架状态（待上架，已上架）
     */
    @TableField("shelves_status")
    private Integer shelvesStatus;

    /**
     * 券用途（售后，销售，线上商城）
     */
    @TableField("coupon_purpose")
    private Integer couponPurpose;

    /**
     * 可领取数不限量
     */
    @TableField(value = "is_available_unlimited",updateStrategy = FieldStrategy.IGNORED)
    private Integer isAvailableUnlimited;

    /**
     * 可领取总数
     */
    @TableField(value = "available_total_count",updateStrategy = FieldStrategy.IGNORED)
    private Integer availableTotalCount;

    /**
     * 已领卡券数
     */
    @TableField("got_total_count")
    private Integer gotTotalCount;

    /**
     * 面额
     */
    @TableField(value = "coupon_denomination",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal couponDenomination;

    /**
     * 总计金额
     */
    @TableField(value = "total_amount",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal totalAmount;

    @TableField(value = "coupon_discount",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal couponDiscount;

    @TableField(value = "collect_range",updateStrategy = FieldStrategy.IGNORED)
    private String collectRange;

    @TableField(value = "is_superposition_similar",updateStrategy = FieldStrategy.IGNORED)
    private Integer isSuperpositionSimilar;

    public String getCollectRange() {
        return collectRange;
    }

    public void setCollectRange(String collectRange) {
        this.collectRange = collectRange;
    }

    public Integer getIsSuperpositionSimilar() {
        return isSuperpositionSimilar;
    }

    public void setIsSuperpositionSimilar(Integer isSuperpositionSimilar) {
        this.isSuperpositionSimilar = isSuperpositionSimilar;
    }

    @TableField(value = "yh_type",updateStrategy = FieldStrategy.IGNORED)
    private Integer yhType;
    @TableField(value = "is_goodwill",updateStrategy = FieldStrategy.IGNORED)
    private Integer isGoodwill;


    public Integer getYhType() {
        return yhType;
    }

    public void setYhType(Integer yhType) {
        this.yhType = yhType;
    }

    public Integer getIsGoodwill() {
        return isGoodwill;
    }

    public void setIsGoodwill(Integer isGoodwill) {
        this.isGoodwill = isGoodwill;
    }

    public BigDecimal getCouponDiscount() {
        return couponDiscount;
    }

    public void setCouponDiscount(BigDecimal couponDiscount) {
        this.couponDiscount = couponDiscount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 每人最多可领张数
     */
    @TableField(value = "each_available_most_count",updateStrategy = FieldStrategy.IGNORED)
    private Integer eachAvailableMostCount;

    /**
     * 是否可叠加
     */
    @TableField(value = "is_superposition",updateStrategy = FieldStrategy.IGNORED)
    private Integer isSuperposition;

    /**
     * 单次可叠加使用张数
     */
    @TableField(value = "single_superposition_count",updateStrategy = FieldStrategy.IGNORED)
    private Integer singleSuperpositionCount;

    /**
     * 领用范围（全部用户，部分用户，高级设置）
     */
    @TableField(value = "recipients_range",updateStrategy = FieldStrategy.IGNORED)
    private Integer recipientsRange;

    /**
     * 领用范围部分（沃龄，用户标签，使用用户范围）
     */
    @TableField(value = "recipients_range_partion",updateStrategy = FieldStrategy.IGNORED)
    private Integer recipientsRangePartion;

    /**
     * 沃龄下限（月）
     */
    @TableField(value = "volvo_age_lower_limit",updateStrategy = FieldStrategy.IGNORED)
    private Integer volvoAgeLowerLimit;

    /**
     * 沃龄上限（月）
     */
    @TableField(value = "volvo_age_upper_limit",updateStrategy = FieldStrategy.IGNORED)
    private Integer volvoAgeUpperLimit;

    /**
     * 用户标签 ？？？
     */
    @TableField("user_label")
    private Integer userLabel;

    /**
     * 领用规则高级设置所需分组数
     */
    @TableField(value = "recipients_range_group_count",updateStrategy = FieldStrategy.IGNORED)
    private Integer recipientsRangeGroupCount;

    /**
     * 使用规则（简单设置，高级设置）
     */
    @TableField(value = "use_rules",updateStrategy = FieldStrategy.IGNORED)
    private Integer useRules;

    /**
     * 使用规则-标准规则-是否限定经销商（全部经销商10041001，部分经销商10041002）
     */
    @TableField(value = "standard_limit_dealer_required",updateStrategy = FieldStrategy.IGNORED)
    private Integer standardLimitDealerRequired;

    /**
     * 使用规则-标准规则-限定维修类型（全部类型10041001，部分类型10041002 ）
     */
    @TableField(value = "standard_limit_repair_type",updateStrategy = FieldStrategy.IGNORED)
    private Integer standardLimitRepairType;

    /**
     * 使用规则-标准规则-限定维修类型:机电维修、保养、事故、零售（多选，英文逗号隔开）
     */
    @TableField(value = "standard_limit_repair_type_code",updateStrategy = FieldStrategy.IGNORED)
    private String standardLimitRepairTypeCode;

    /**
     * 使用规则-标准规则-是否有使用门槛 :有门槛 10041001，无门槛10041002
     */
    @TableField(value = "standard_use_threshold",updateStrategy = FieldStrategy.IGNORED)
    private Integer standardUseThreshold;

    /**
     * 使用规则-使用门槛-标准规则-满多少元可用
     */
    @TableField(value = "standard_use_threshold_available_amount",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal standardUseThresholdAvailableAmount;

    /**
     * 是否有可叠加卡券:是 10041001，否10041002
     */
    @TableField(value = "standard_has_superposition",updateStrategy = FieldStrategy.IGNORED)
    private Integer standardHasSuperposition;

    /**
     * 是否需激活 :否:31121001 是:31121002
     */
    @TableField(value = "standard_activation_required",updateStrategy = FieldStrategy.IGNORED)
    private Integer standardActivationRequired;

    /**
     * 使用规则-车型：（全部车型10041001，部分车型10041002）
     */
    @TableField(value = "standard_model",updateStrategy = FieldStrategy.IGNORED)
    private Integer standardModel;

    /**
     * 使用规则-高级设置-车型：（全部车型10041001，部分车型10041002）
     */
    @TableField(value = "advanced_model",updateStrategy = FieldStrategy.IGNORED)
    private Integer advancedModel;

    public Integer getAdvancedModel() {
        return advancedModel;
    }

    public void setAdvancedModel(Integer advancedModel) {
        this.advancedModel = advancedModel;
    }

    /**
     * 使用规则-车龄：（无限制10041001，有限制10041002）
     */
    @TableField(value = "standard_vehicle_age",updateStrategy = FieldStrategy.IGNORED)
    private Integer standardVehicleAge;

    /**
     * 使用规则-车龄:下限
     */
    @TableField(value = "standard_vehicle_age_lower",updateStrategy = FieldStrategy.IGNORED)
    private Integer standardVehicleAgeLower;

    /**
     * 使用规则-车龄:上限
     */
    @TableField(value = "standard_vehicle_age_upper",updateStrategy = FieldStrategy.IGNORED)
    private Integer standardVehicleAgeUpper;

    /**
     * 使用规则-高级设置-是否限定经销商（全部经销商10041001，部分经销商10041002）
     */
    @TableField(value = "advanced_limit_dealer_required",updateStrategy = FieldStrategy.IGNORED)
    private Integer advancedLimitDealerRequired;

    /**
     * 使用规则-高级设置-限定维修类型（全部类型10041001，部分类型10041002 ）
     */
    @TableField(value = "advanced_limit_repair_type",updateStrategy = FieldStrategy.IGNORED)
    private Integer advancedLimitRepairType;

    /**
     * 使用规则-高级设置-限定维修类型:机电维修、保养、事故、零售（多选，英文逗号隔开）
     */
    @TableField(value = "advanced_limit_repair_type_code",updateStrategy = FieldStrategy.IGNORED)
    private String advancedLimitRepairTypeCode;

    /**
     * 使用规则-高级设置-是否有使用门槛 :有门槛 10041001，无门槛10041002
     */
    @TableField(value = "advanced_use_threshold",updateStrategy = FieldStrategy.IGNORED)
    private Integer advancedUseThreshold;

    /**
     * 使用规则-使用门槛-高级设置-满多少元可用
     */
    @TableField(value = "advanced_use_threshold_available_amount",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal advancedUseThresholdAvailableAmount;

    /**
     * 是否有可叠加卡券:是 10041001，否10041002
     */
    @TableField(value = "advanced_has_superposition",updateStrategy = FieldStrategy.IGNORED)
    private Integer advancedHasSuperposition;
    /**
     * 使用规则-高级设置-车龄:下限
     */
    @TableField(value = "advanced_vehicle_age_lower",updateStrategy = FieldStrategy.IGNORED)
    private Integer advancedVehicleAgeLower;
    @TableField(value = "advanced_vehicle_age",updateStrategy = FieldStrategy.IGNORED)
    private Integer advancedVehicleAge;

    public Integer getAdvancedVehicleAge() {
        return advancedVehicleAge;
    }

    public void setAdvancedVehicleAge(Integer advancedVehicleAge) {
        this.advancedVehicleAge = advancedVehicleAge;
    }

    /**
     * 使用规则-高级设置-车龄:上限
     */
    @TableField(value = "advanced_vehicle_age_upper",updateStrategy = FieldStrategy.IGNORED)
    private Integer advancedVehicleAgeUpper;

    public Integer getAdvancedVehicleAgeLower() {
        return advancedVehicleAgeLower;
    }

    public void setAdvancedVehicleAgeLower(Integer advancedVehicleAgeLower) {
        this.advancedVehicleAgeLower = advancedVehicleAgeLower;
    }

    public Integer getAdvancedVehicleAgeUpper() {
        return advancedVehicleAgeUpper;
    }

    public void setAdvancedVehicleAgeUpper(Integer advancedVehicleAgeUpper) {
        this.advancedVehicleAgeUpper = advancedVehicleAgeUpper;
    }

    /**
     * 使用规则高级设置所需分组数
     */
    @TableField(value = "use_threshold_group_count",updateStrategy = FieldStrategy.IGNORED)
    private Integer useThresholdGroupCount;

    /**
     * 适用项目（全部适用，指定适用，高级设置）
     */
    @TableField(value = "apply_project",updateStrategy = FieldStrategy.IGNORED)
    private Integer applyProject;

    /**
     * 商品类目（全场通用10041001，指定类目10041002）
     */
    @TableField(value = "item_category",updateStrategy = FieldStrategy.IGNORED)
    private Integer itemCategory;

    public Integer getItemCategory() {
        return itemCategory;
    }

    public void setItemCategory(Integer itemCategory) {
        this.itemCategory = itemCategory;
    }

    /**
     * 指定项目（零件，工时，商品，套餐）
     */
    @TableField(value = "assign_apply_project",updateStrategy = FieldStrategy.IGNORED)
    private Integer assignApplyProject;

    /**
     * 用途描述
     */
    @TableField(value = "purponse_desc",updateStrategy = FieldStrategy.IGNORED)
    private String purponseDesc;

    /**
     * 卡券说明
     */
    @TableField(value = "coupon_instructions",updateStrategy = FieldStrategy.IGNORED)
    private String couponInstructions;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 创建人姓名
     */
    @TableField("created_by_name")
    private String createdByName;

    public String getCreatedByName() {
        return createdByName;
    }

    public void setCreatedByName(String createdByName) {
        this.createdByName = createdByName;
    }

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;


    /**
     * 中台卡券信息表ID
     */
    @TableField("mid_coupon_id")
    private Long midCouponId;

    public Long getMidCouponId() {
        return midCouponId;
    }

    public void setMidCouponId(Long midCouponId) {
        this.midCouponId = midCouponId;
    }

    /**
     * 使用场景
     */
    @TableField("is_use_multiple")
    private Integer isUseMultiple;

    public Integer getIsUseMultiple() {
        return isUseMultiple;
    }

    public void setIsUseMultiple(Integer isUseMultiple) {
        this.isUseMultiple = isUseMultiple;
    }

    /**
     * 使用场景
     */
    @TableField("usage_scenarios")
    private Integer usageScenarios;

    public Integer getUsageScenarios() {
        return usageScenarios;
    }

    public void setUsageScenarios(Integer usageScenarios) {
        this.usageScenarios = usageScenarios;
    }

    public CouponPO() {
        super();
    }

    @Override
    public String getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerParCode() {
        return ownerParCode;
    }

    public void setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public Integer getCouponType() {
        return couponType;
    }

    public void setCouponType(Integer couponType) {
        this.couponType = couponType;
    }

    public Integer getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(Integer couponStatus) {
        this.couponStatus = couponStatus;
    }

    public Integer getValidityPeriodType() {
        return validityPeriodType;
    }

    public void setValidityPeriodType(Integer validityPeriodType) {
        this.validityPeriodType = validityPeriodType;
    }

    public Integer getValidityPeriodDays() {
        return validityPeriodDays;
    }

    public void setValidityPeriodDays(Integer validityPeriodDays) {
        this.validityPeriodDays = validityPeriodDays;
    }

    public Date getCouponBeginDate() {
        return couponBeginDate;
    }

    public void setCouponBeginDate(Date couponBeginDate) {
        this.couponBeginDate = couponBeginDate;
    }

    public Date getCouponEndDate() {
        return couponEndDate;
    }

    public void setCouponEndDate(Date couponEndDate) {
        this.couponEndDate = couponEndDate;
    }

    public Integer getShelvesStatus() {
        return shelvesStatus;
    }

    public void setShelvesStatus(Integer shelvesStatus) {
        this.shelvesStatus = shelvesStatus;
    }

    public Integer getCouponPurpose() {
        return couponPurpose;
    }

    public void setCouponPurpose(Integer couponPurpose) {
        this.couponPurpose = couponPurpose;
    }

    public Integer getIsAvailableUnlimited() {
        return isAvailableUnlimited;
    }

    public void setIsAvailableUnlimited(Integer isAvailableUnlimited) {
        this.isAvailableUnlimited = isAvailableUnlimited;
    }

    public Integer getAvailableTotalCount() {
        return availableTotalCount;
    }

    public void setAvailableTotalCount(Integer availableTotalCount) {
        this.availableTotalCount = availableTotalCount;
    }

    public Integer getGotTotalCount() {
        return gotTotalCount;
    }

    public void setGotTotalCount(Integer gotTotalCount) {
        this.gotTotalCount = gotTotalCount;
    }

    public BigDecimal getCouponDenomination() {
        return couponDenomination;
    }

    public void setCouponDenomination(BigDecimal couponDenomination) {
        this.couponDenomination = couponDenomination;
    }

    public Integer getEachAvailableMostCount() {
        return eachAvailableMostCount;
    }

    public void setEachAvailableMostCount(Integer eachAvailableMostCount) {
        this.eachAvailableMostCount = eachAvailableMostCount;
    }

    public Integer getIsSuperposition() {
        return isSuperposition;
    }

    public void setIsSuperposition(Integer isSuperposition) {
        this.isSuperposition = isSuperposition;
    }

    public Integer getSingleSuperpositionCount() {
        return singleSuperpositionCount;
    }

    public void setSingleSuperpositionCount(Integer singleSuperpositionCount) {
        this.singleSuperpositionCount = singleSuperpositionCount;
    }

    public Integer getRecipientsRange() {
        return recipientsRange;
    }

    public void setRecipientsRange(Integer recipientsRange) {
        this.recipientsRange = recipientsRange;
    }

    public Integer getRecipientsRangePartion() {
        return recipientsRangePartion;
    }

    public void setRecipientsRangePartion(Integer recipientsRangePartion) {
        this.recipientsRangePartion = recipientsRangePartion;
    }

    public Integer getVolvoAgeLowerLimit() {
        return volvoAgeLowerLimit;
    }

    public void setVolvoAgeLowerLimit(Integer volvoAgeLowerLimit) {
        this.volvoAgeLowerLimit = volvoAgeLowerLimit;
    }

    public Integer getVolvoAgeUpperLimit() {
        return volvoAgeUpperLimit;
    }

    public void setVolvoAgeUpperLimit(Integer volvoAgeUpperLimit) {
        this.volvoAgeUpperLimit = volvoAgeUpperLimit;
    }

    public Integer getUserLabel() {
        return userLabel;
    }

    public void setUserLabel(Integer userLabel) {
        this.userLabel = userLabel;
    }

    public Integer getRecipientsRangeGroupCount() {
        return recipientsRangeGroupCount;
    }

    public void setRecipientsRangeGroupCount(Integer recipientsRangeGroupCount) {
        this.recipientsRangeGroupCount = recipientsRangeGroupCount;
    }

    public Integer getUseRules() {
        return useRules;
    }

    public void setUseRules(Integer useRules) {
        this.useRules = useRules;
    }

    public Integer getStandardLimitDealerRequired() {
        return standardLimitDealerRequired;
    }

    public void setStandardLimitDealerRequired(Integer standardLimitDealerRequired) {
        this.standardLimitDealerRequired = standardLimitDealerRequired;
    }

    public Integer getStandardLimitRepairType() {
        return standardLimitRepairType;
    }

    public void setStandardLimitRepairType(Integer standardLimitRepairType) {
        this.standardLimitRepairType = standardLimitRepairType;
    }

    public String getStandardLimitRepairTypeCode() {
        return standardLimitRepairTypeCode;
    }

    public void setStandardLimitRepairTypeCode(String standardLimitRepairTypeCode) {
        this.standardLimitRepairTypeCode = standardLimitRepairTypeCode;
    }

    public Integer getStandardUseThreshold() {
        return standardUseThreshold;
    }

    public void setStandardUseThreshold(Integer standardUseThreshold) {
        this.standardUseThreshold = standardUseThreshold;
    }

    public BigDecimal getStandardUseThresholdAvailableAmount() {
        return standardUseThresholdAvailableAmount;
    }

    public void setStandardUseThresholdAvailableAmount(BigDecimal standardUseThresholdAvailableAmount) {
        this.standardUseThresholdAvailableAmount = standardUseThresholdAvailableAmount;
    }

    public Integer getStandardHasSuperposition() {
        return standardHasSuperposition;
    }

    public void setStandardHasSuperposition(Integer standardHasSuperposition) {
        this.standardHasSuperposition = standardHasSuperposition;
    }

    public Integer getStandardActivationRequired() {
        return standardActivationRequired;
    }

    public void setStandardActivationRequired(Integer standardActivationRequired) {
        this.standardActivationRequired = standardActivationRequired;
    }

    public Integer getStandardModel() {
        return standardModel;
    }

    public void setStandardModel(Integer standardModel) {
        this.standardModel = standardModel;
    }

    public Integer getStandardVehicleAge() {
        return standardVehicleAge;
    }

    public void setStandardVehicleAge(Integer standardVehicleAge) {
        this.standardVehicleAge = standardVehicleAge;
    }

    public Integer getStandardVehicleAgeLower() {
        return standardVehicleAgeLower;
    }

    public void setStandardVehicleAgeLower(Integer standardVehicleAgeLower) {
        this.standardVehicleAgeLower = standardVehicleAgeLower;
    }

    public Integer getStandardVehicleAgeUpper() {
        return standardVehicleAgeUpper;
    }

    public void setStandardVehicleAgeUpper(Integer standardVehicleAgeUpper) {
        this.standardVehicleAgeUpper = standardVehicleAgeUpper;
    }

    public Integer getAdvancedLimitDealerRequired() {
        return advancedLimitDealerRequired;
    }

    public void setAdvancedLimitDealerRequired(Integer advancedLimitDealerRequired) {
        this.advancedLimitDealerRequired = advancedLimitDealerRequired;
    }

    public Integer getAdvancedLimitRepairType() {
        return advancedLimitRepairType;
    }

    public void setAdvancedLimitRepairType(Integer advancedLimitRepairType) {
        this.advancedLimitRepairType = advancedLimitRepairType;
    }

    public String getAdvancedLimitRepairTypeCode() {
        return advancedLimitRepairTypeCode;
    }

    public void setAdvancedLimitRepairTypeCode(String advancedLimitRepairTypeCode) {
        this.advancedLimitRepairTypeCode = advancedLimitRepairTypeCode;
    }

    public Integer getAdvancedUseThreshold() {
        return advancedUseThreshold;
    }

    public void setAdvancedUseThreshold(Integer advancedUseThreshold) {
        this.advancedUseThreshold = advancedUseThreshold;
    }

    public BigDecimal getAdvancedUseThresholdAvailableAmount() {
        return advancedUseThresholdAvailableAmount;
    }

    public void setAdvancedUseThresholdAvailableAmount(BigDecimal advancedUseThresholdAvailableAmount) {
        this.advancedUseThresholdAvailableAmount = advancedUseThresholdAvailableAmount;
    }

    public Integer getAdvancedHasSuperposition() {
        return advancedHasSuperposition;
    }

    public void setAdvancedHasSuperposition(Integer advancedHasSuperposition) {
        this.advancedHasSuperposition = advancedHasSuperposition;
    }

    public Integer getUseThresholdGroupCount() {
        return useThresholdGroupCount;
    }

    public void setUseThresholdGroupCount(Integer useThresholdGroupCount) {
        this.useThresholdGroupCount = useThresholdGroupCount;
    }

    public Integer getApplyProject() {
        return applyProject;
    }

    public void setApplyProject(Integer applyProject) {
        this.applyProject = applyProject;
    }

    public Integer getAssignApplyProject() {
        return assignApplyProject;
    }

    public void setAssignApplyProject(Integer assignApplyProject) {
        this.assignApplyProject = assignApplyProject;
    }

    public String getPurponseDesc() {
        return purponseDesc;
    }

    public void setPurponseDesc(String purponseDesc) {
        this.purponseDesc = purponseDesc;
    }

    public String getCouponInstructions() {
        return couponInstructions;
    }

    public void setCouponInstructions(String couponInstructions) {
        this.couponInstructions = couponInstructions;
    }

    public Integer getDataSources() {
        return dataSources;
    }

    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "CouponPO{" +
                "appId=" + appId +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", couponCode=" + couponCode +
                ", couponName=" + couponName +
                ", couponType=" + couponType +
                ", couponStatus=" + couponStatus +
                ", validityPeriodType=" + validityPeriodType +
                ", validityPeriodDays=" + validityPeriodDays +
                ", couponBeginDate=" + couponBeginDate +
                ", couponEndDate=" + couponEndDate +
                ", shelvesStatus=" + shelvesStatus +
                ", couponPurpose=" + couponPurpose +
                ", isAvailableUnlimited=" + isAvailableUnlimited +
                ", availableTotalCount=" + availableTotalCount +
                ", gotTotalCount=" + gotTotalCount +
                ", couponDenomination=" + couponDenomination +
                ", eachAvailableMostCount=" + eachAvailableMostCount +
                ", isSuperposition=" + isSuperposition +
                ", singleSuperpositionCount=" + singleSuperpositionCount +
                ", recipientsRange=" + recipientsRange +
                ", recipientsRangePartion=" + recipientsRangePartion +
                ", volvoAgeLowerLimit=" + volvoAgeLowerLimit +
                ", volvoAgeUpperLimit=" + volvoAgeUpperLimit +
                ", userLabel=" + userLabel +
                ", recipientsRangeGroupCount=" + recipientsRangeGroupCount +
                ", useRules=" + useRules +
                ", standardLimitDealerRequired=" + standardLimitDealerRequired +
                ", standardLimitRepairType=" + standardLimitRepairType +
                ", standardLimitRepairTypeCode=" + standardLimitRepairTypeCode +
                ", standardUseThreshold=" + standardUseThreshold +
                ", standardUseThresholdAvailableAmount=" + standardUseThresholdAvailableAmount +
                ", standardHasSuperposition=" + standardHasSuperposition +
                ", standardActivationRequired=" + standardActivationRequired +
                ", standardModel=" + standardModel +
                ", standardVehicleAge=" + standardVehicleAge +
                ", standardVehicleAgeLower=" + standardVehicleAgeLower +
                ", standardVehicleAgeUpper=" + standardVehicleAgeUpper +
                ", advancedLimitDealerRequired=" + advancedLimitDealerRequired +
                ", advancedLimitRepairType=" + advancedLimitRepairType +
                ", advancedLimitRepairTypeCode=" + advancedLimitRepairTypeCode +
                ", advancedUseThreshold=" + advancedUseThreshold +
                ", advancedUseThresholdAvailableAmount=" + advancedUseThresholdAvailableAmount +
                ", advancedHasSuperposition=" + advancedHasSuperposition +
                ", useThresholdGroupCount=" + useThresholdGroupCount +
                ", applyProject=" + applyProject +
                ", assignApplyProject=" + assignApplyProject +
                ", purponseDesc=" + purponseDesc +
                ", couponInstructions=" + couponInstructions +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                "}";
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
