package com.yonyou.dmscus.customer.entity.po.goodwill;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善管理通知开票信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-17
 */
@TableName("tt_goodwill_notice_invoice_info")
public class GoodwillNoticeInvoiceInfoPO extends BasePO<GoodwillNoticeInvoiceInfoPO> {

	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	@TableField("APP_ID")
	private String appId;

	/**
	 * 所有者代码
	 */
	@TableField("OWNER_CODE")
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	@TableField("OWNER_PAR_CODE")
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	@TableField("ORG_ID")
	private Integer orgId;

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 亲善单id
	 */
	@TableField("goodwill_apply_id")
	private Long goodwillApplyId;

	/**
	 * 开票id
	 */
	@TableField("invoice_id")
	private String invoiceId;

	/**
	 * 开票对象
	 */
	@TableField("invoice_object")
	private Integer invoiceObject;

	/**
	 * 开票抬头
	 */
	@TableField("invoice_title")
	private String invoiceTitle;

	/**
	 * 名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 纳税人识别号
	 */
	@TableField("taxpayer_identification_number")
	private String taxpayerIdentificationNumber;

	/**
	 * 电话
	 */
	@TableField("phone")
	private String phone;

	/**
	 * 地址
	 */
	@TableField("address")
	private String address;

	/**
	 * 开户行
	 */
	@TableField("open_bank")
	private String openBank;

	/**
	 * 账号
	 */
	@TableField("account")
	private String account;

	/**
	 * 通知开票日期
	 */
	@TableField("notice_invoice_date")
	private Date noticeInvoiceDate;

	/**
	 * 通知开票金额
	 */
	@TableField("notice_invoice_price")
	private BigDecimal noticeInvoicePrice;

	/**
	 * 代金券充值成本金额
	 */
	@TableField("voucher_recharge_price")
	private BigDecimal voucherRechargePrice;

	/**
	 * 代金券充值金额 :与最终结算无关,直接充值关联到工单账户和沃世界
	 */
	@TableField("voucher_coupon_face_recharge_price")
	private  BigDecimal voucherCouponFaceRechargePrice;

	/**
	 * 沃世界积分充值金额
	 */
	@TableField("volvo_credits_recharge_price")
	private BigDecimal volvoCreditsRechargePrice;

	/**
	 * 代金券通知类型
	 */
	@TableField("voucher_type")
	private Integer voucherType;
	/**
	 * 充值剩余金额
	 */
	@TableField("recharge_price")
	private BigDecimal rechargePrice;

	/**
	 * 是否提交
	 */
	@TableField("is_commit")
	private Integer isCommit;

	/**
	 * 是否确认
	 */
	@TableField("is_confirm")
	private Integer isConfirm;

	/**
	 * 是否有效
	 */
	@TableField("is_valid")
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	@TableField("is_deleted")
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	@TableField("created_at")
	private Date createdAt;

	/**
	 * 修改时间
	 */
	@TableField("updated_at")
	private Date updatedAt;
	/**
	 * 代金券充值状态
	 */
	@TableField("voucher_status")
	private Integer voucherStatus;

	/**
	 * 代金券充值时间
	 */
	@TableField("voucher_time")
	private Date voucherTime;
	/**
	 * 积分充值状态
	 */
	@TableField("credits_status")
	private Integer creditsStatus;

	/**
	 * 积分充值时间
	 */
	@TableField("credits_time")
	private Date creditsTime;

	/**
	 * 消费ID
	 */
	@TableField("consume_id")
	private String consumeId;

	@TableField(exist = false)
	private BigDecimal costRate;

	public String getConsumeId() {
		return consumeId;
	}

	public void setConsumeId(String consumeId) {
		this.consumeId = consumeId;
	}

	public Integer getVoucherStatus() {
		return voucherStatus;
	}

	public void setVoucherStatus(Integer voucherStatus) {
		this.voucherStatus = voucherStatus;
	}

	public Date getVoucherTime() {
		return voucherTime;
	}

	public void setVoucherTime(Date voucherTime) {
		this.voucherTime = voucherTime;
	}

	public Integer getCreditsStatus() {
		return creditsStatus;
	}

	public void setCreditsStatus(Integer creditsStatus) {
		this.creditsStatus = creditsStatus;
	}

	public Date getCreditsTime() {
		return creditsTime;
	}

	public void setCreditsTime(Date creditsTime) {
		this.creditsTime = creditsTime;
	}

	public Integer getIsConfirm() {
		return isConfirm;
	}

	public void setIsConfirm(Integer isConfirm) {
		this.isConfirm = isConfirm;
	}

	public Integer getIsCommit() {
		return isCommit;
	}

	public void setIsCommit(Integer isCommit) {
		this.isCommit = isCommit;
	}

	public Integer getInvoiceObject() {
		return invoiceObject;
	}

	public void setInvoiceObject(Integer invoiceObject) {
		this.invoiceObject = invoiceObject;
	}

	public Integer getVoucherType() {
		return voucherType;
	}

	public void setVoucherType(Integer voucherType) {
		this.voucherType = voucherType;
	}

	public BigDecimal getRechargePrice() {
		return rechargePrice;
	}

	public void setRechargePrice(BigDecimal rechargePrice) {
		this.rechargePrice = rechargePrice;
	}

	public Date getNoticeInvoiceDate() {
		return noticeInvoiceDate;
	}

	public void setNoticeInvoiceDate(Date noticeInvoiceDate) {
		this.noticeInvoiceDate = noticeInvoiceDate;
	}

	public GoodwillNoticeInvoiceInfoPO() {
		super();
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	public String getOwnerParCode() {
		return ownerParCode;
	}

	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getGoodwillApplyId() {
		return goodwillApplyId;
	}

	public void setGoodwillApplyId(Long goodwillApplyId) {
		this.goodwillApplyId = goodwillApplyId;
	}

	public String getInvoiceId() {
		return invoiceId;
	}

	public void setInvoiceId(String invoiceId) {
		this.invoiceId = invoiceId;
	}

	public String getInvoiceTitle() {
		return invoiceTitle;
	}

	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getTaxpayerIdentificationNumber() {
		return taxpayerIdentificationNumber;
	}

	public void setTaxpayerIdentificationNumber(String taxpayerIdentificationNumber) {
		this.taxpayerIdentificationNumber = taxpayerIdentificationNumber;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getOpenBank() {
		return openBank;
	}

	public void setOpenBank(String openBank) {
		this.openBank = openBank;
	}

	public String getAccount() {
		return account;
	}

	public void setAccount(String account) {
		this.account = account;
	}

	public BigDecimal getNoticeInvoicePrice() {
		return noticeInvoicePrice;
	}

	public void setNoticeInvoicePrice(BigDecimal noticeInvoicePrice) {
		this.noticeInvoicePrice = noticeInvoicePrice;
	}

	public BigDecimal getVoucherRechargePrice() {
		return voucherRechargePrice;
	}

	public void setVoucherRechargePrice(BigDecimal voucherRechargePrice) {
		this.voucherRechargePrice = voucherRechargePrice;
	}

	public BigDecimal getVolvoCreditsRechargePrice() {
		return volvoCreditsRechargePrice;
	}

	public void setVolvoCreditsRechargePrice(BigDecimal volvoCreditsRechargePrice) {
		this.volvoCreditsRechargePrice = volvoCreditsRechargePrice;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public BigDecimal getCostRate() {return costRate;}

	public void setCostRate(BigDecimal costRate) {
		this.costRate = costRate;
	}

	@Override
	protected Serializable pkVal() {
		return this.id;
	}

	public BigDecimal getVoucherCouponFaceRechargePrice() {
		return voucherCouponFaceRechargePrice;
	}

	public void setVoucherCouponFaceRechargePrice(BigDecimal voucherCouponFaceRechargePrice) {
		this.voucherCouponFaceRechargePrice = voucherCouponFaceRechargePrice;
	}

	@Override
	public String toString() {
		return "GoodwillNoticeInvoiceInfoPO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", goodwillApplyId=" + goodwillApplyId
				+ ", invoiceId=" + invoiceId + ", invoiceTitle=" + invoiceTitle + ", name=" + name
				+ ", taxpayerIdentificationNumber=" + taxpayerIdentificationNumber + ", phone=" + phone + ", address="
				+ address + ", openBank=" + openBank + ", account=" + account + ", noticeInvoicePrice="
				+ noticeInvoicePrice + ", voucherRechargePrice=" + voucherRechargePrice + ", volvoCreditsRechargePrice="
				+ volvoCreditsRechargePrice + ", voucherCouponFaceRechargePrice="+voucherCouponFaceRechargePrice+", isValid=" + isValid + ", isDeleted=" + isDeleted + ", createdAt="
				+ createdAt + ", updatedAt=" + updatedAt +", costRate=" + costRate + "}";
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dtoClass
	 *            需要进行转换的dtoClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
		return super.transDtoToPo(dtoClass);
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dto
	 *            需要进行转换的dto
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> void transPoToDto(T dto) {
		BeanMapperUtil.copyProperties(this, dto, "id");
	}

}
