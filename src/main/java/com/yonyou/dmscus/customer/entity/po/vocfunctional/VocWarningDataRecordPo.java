package com.yonyou.dmscus.customer.entity.po.vocfunctional;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @title: VocWarningDataLog
 * @projectName dmscus.customer
 * @date 2022/11/118:25
 */
@Data
@ApiModel(value = "VocWarningDatarecordPo", description = "BI推送VOC车辆每日保养灯信息表")
@TableName("tt_voc_warning_daily_record")
public class VocWarningDataRecordPo  extends  BasePo{
    //通知时间
    @ApiModelProperty(value = "报告时间",notes = "报告时间",required = true,name = "report_date",example = "20221026",dataType = "String")
    @TableField("report_date")
    private String reportDate;
    //需要修改的时间
    @ApiModelProperty(value = "更新时间",notes = "更新时间",required = true,name = "modify_time",example = "20221011",dataType = "String")
    @TableField("modify_time")
    private String modifyTime;
    
    @ApiModelProperty(value = "车架号",notes = "车架号",required = true,name = "dt",example = "LVSHGFAR4FF087294",dataType = "String")
    @TableField("vin")
    private String vin;

    @ApiModelProperty(value = "监控状态名称",notes = "监控状态名称",required = true,name = "status_name",example = "ServiceWarningStatus",dataType = "String")
    @TableField("status_name")
    private String statusName;

    @ApiModelProperty(value = "报警状态对应的状态值",notes = "报警状态对应的状态值：83981001 ：Normal,83981002：TimeExceeded,83981003：AlmostTimeForService,83981004：TimeForService",required = true,name = "status_value",example = "83981003",dataType = "Integer")
    @TableField("status_value")
    private Integer statusValue;

    @ApiModelProperty(value = "报警名称:",notes = "报警名称:消失，改变，新增",required = true,name = "status_change",example = "新增",dataType = "String")
    @TableField("status_change")
    private String statusChange;

    @ApiModelProperty(value = "总里程m:",notes = "总里程m",required = true,name = "mileage",example = "137838173",dataType = "String")
    @TableField("mileage")
    private String mileage;

    @ApiModelProperty(value = "总里程km:",notes = "总里程km",required = true,name = "mileage_km",example = "137838.173",dataType = "String")
    @TableField("mileage_km")
    private String mileageKm;

    @ApiModelProperty(value = "车型",notes = "车型",required = true,name = "model",example = "S80",dataType = "String")
    @TableField("model")
    private String model;

    @ApiModelProperty(value = "年款",notes = "年款",required = true,name = "year_model",example = "2015",dataType = "String")
    @TableField("year_model")
    private String yearModel;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        VocWarningDataRecordPo that = (VocWarningDataRecordPo) o;
        return Objects.equals(reportDate, that.reportDate) &&
                Objects.equals(modifyTime, that.modifyTime) &&
                Objects.equals(vin, that.vin) &&
                Objects.equals(statusValue, that.statusValue) &&
                Objects.equals(statusChange, that.statusChange);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), reportDate, modifyTime, vin, statusValue, statusChange);
    }
}
