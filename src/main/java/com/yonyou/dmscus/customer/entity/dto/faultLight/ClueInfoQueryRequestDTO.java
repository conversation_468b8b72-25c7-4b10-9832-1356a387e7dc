package com.yonyou.dmscus.customer.entity.dto.faultLight;

import com.yonyou.dmscus.customer.util.common.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ClueInfoQueryRequestDTO implements Serializable {

    private static final long serialVersionUID = -4546493993335942864L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty(name = "线索状态")
    private List<Integer> clueStatus;

    @ApiModelProperty(name = "车辆VIN")
    private String vin;

    @ApiModelProperty(name = "故障类别")
    private String warningName;

    @ApiModelProperty(name = "大区")
    private String regionName;

    @ApiModelProperty(name = "小区")
    private String cellName;

    @ApiModelProperty(name = "城市名称")
    private String cityName;

    @ApiModelProperty(name = "大区ID")
    private Long regionId;

    @ApiModelProperty(name = "小区ID")
    private Long cellId;

    @ApiModelProperty(name = "城市ID")
    private Long cityId;

    @ApiModelProperty(name = "跟进状态")
    private List<Integer> followStatus;

    @ApiModelProperty(name = "线索生成开始时间")
    private String clueGenStartTime;

    @ApiModelProperty(name = "线索生成结束时间")
    private String clueGenEndTime;

    @ApiModelProperty(name = "线索下发开始时间")
    private String clueDisStartTime;

    @ApiModelProperty(name = "线索下发结束时间")
    private String clueDisEndTime;

    @ApiModelProperty(name = "集团简称")
    private String groupCompanyShortName;

    @ApiModelProperty(name = "经销商代码")
    private List<String> dealerCode;

    @ApiModelProperty("是否有责 0.无数据 1.无责 2.有责 3.待确认")
    private Integer wheRes;

    private int currentPage;

    private int pageSize;

    private String remarks;

    public String getRemarks() {
        if(!StringUtils.isBlank(remarks) && remarks.length() > 255){
            return remarks.substring(0,200);
        }
        return remarks;
    }
}
