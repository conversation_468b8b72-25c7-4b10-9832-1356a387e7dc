package com.yonyou.dmscus.customer.entity.dto.goodwill;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 亲善预申请单亲善信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
    
public class GoodwillMaterialAuditDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

        
    /**
     * 系统ID
     */
                private String appId;
                
    /**
     * 所有者代码
     */
                private String ownerCode;
                
    /**
     * 所有者的父组织代码
     */
                private String ownerParCode;
                
    /**
     * 组织ID
     */
                private Integer orgId;
                
    /**
     * 主键id
     */
                private Long id;
                
    /**
     * 亲善单id
     */
                private Long goodwillApplyId;
                
    /**
     * 客户背景
     */
                private String customerBackground;
                
    /**
     * 客户背景英文
     */
                private String customerBackgroundEn;
                
    /**
     * 投诉原因及处理经过
     */
                private String reasonAndDispose;
                
    /**
     * 投诉原因及处理经过英文
     */
                private String reasonAndDisposeEn;
                
    /**
     * 维修解决方案
     */
                private String repairSolution;
                
    /**
     * 维修解决方案英文
     */
                private String repairSolutionEn;
                
    /**
     * 客户要求
     */
                private String customerRequire;
                
    /**
     * 客户要求英文
     */
                private String customerRequireEn;
                
    /**
     * 潜在风险
     */
                private String potentialRisk;
                
    /**
     * 潜在风险英文
     */
                private String potentialRiskEn;
                
    /**
     * VR或TJ号英文
     */
                private String vrOrTjNoEn;
                
    /**
     * VR或TJ号
     */
                private String vrOrTjNo;
                
    /**
     * 商务亲善申请详情英文
     */
                private String businessGoodwillApplyDetailEn;
                
    /**
     * 商务亲善申请详情
     */
                private String businessGoodwillApplyDetail;
                
    /**
     * 是否有效
     */
                private Integer isValid;
                
    /**
     * 是否删除:1,删除；0,未删除
     */
                private Boolean isDeleted;
                
    /**
     * 创建时间
     */
    
    private Date createdAt;
                
    /**
     * 修改时间
     */
    
    private Date updatedAt;
            
    public GoodwillMaterialAuditDTO() {
        super();
    }

                                
    public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
            }
                                
    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
            }
                                
    public String getOwnerParCode(){
        return ownerParCode;
    }


    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
            }
                                
    public Integer getOrgId(){
        return orgId;
    }


    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
            }
                                            
    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
            }
                                
    public Long getGoodwillApplyId(){
        return goodwillApplyId;
    }


    public void  setGoodwillApplyId(Long goodwillApplyId) {
        this.goodwillApplyId = goodwillApplyId;
            }
                                
    public String getCustomerBackground(){
        return customerBackground;
    }


    public void  setCustomerBackground(String customerBackground) {
        this.customerBackground = customerBackground;
            }
                                
    public String getCustomerBackgroundEn(){
        return customerBackgroundEn;
    }


    public void  setCustomerBackgroundEn(String customerBackgroundEn) {
        this.customerBackgroundEn = customerBackgroundEn;
            }
                                
    public String getReasonAndDispose(){
        return reasonAndDispose;
    }


    public void  setReasonAndDispose(String reasonAndDispose) {
        this.reasonAndDispose = reasonAndDispose;
            }
                                
    public String getReasonAndDisposeEn(){
        return reasonAndDisposeEn;
    }


    public void  setReasonAndDisposeEn(String reasonAndDisposeEn) {
        this.reasonAndDisposeEn = reasonAndDisposeEn;
            }
                                
    public String getRepairSolution(){
        return repairSolution;
    }


    public void  setRepairSolution(String repairSolution) {
        this.repairSolution = repairSolution;
            }
                                
    public String getRepairSolutionEn(){
        return repairSolutionEn;
    }


    public void  setRepairSolutionEn(String repairSolutionEn) {
        this.repairSolutionEn = repairSolutionEn;
            }
                                
    public String getCustomerRequire(){
        return customerRequire;
    }


    public void  setCustomerRequire(String customerRequire) {
        this.customerRequire = customerRequire;
            }
                                
    public String getCustomerRequireEn(){
        return customerRequireEn;
    }


    public void  setCustomerRequireEn(String customerRequireEn) {
        this.customerRequireEn = customerRequireEn;
            }
                                
    public String getPotentialRisk(){
        return potentialRisk;
    }


    public void  setPotentialRisk(String potentialRisk) {
        this.potentialRisk = potentialRisk;
            }
                                
    public String getPotentialRiskEn(){
        return potentialRiskEn;
    }


    public void  setPotentialRiskEn(String potentialRiskEn) {
        this.potentialRiskEn = potentialRiskEn;
            }
                                
    public String getVrOrTjNoEn(){
        return vrOrTjNoEn;
    }


    public void  setVrOrTjNoEn(String vrOrTjNoEn) {
        this.vrOrTjNoEn = vrOrTjNoEn;
            }
                                
    public String getVrOrTjNo(){
        return vrOrTjNo;
    }


    public void  setVrOrTjNo(String vrOrTjNo) {
        this.vrOrTjNo = vrOrTjNo;
            }
                                
    public String getBusinessGoodwillApplyDetailEn(){
        return businessGoodwillApplyDetailEn;
    }


    public void  setBusinessGoodwillApplyDetailEn(String businessGoodwillApplyDetailEn) {
        this.businessGoodwillApplyDetailEn = businessGoodwillApplyDetailEn;
            }
                                
    public String getBusinessGoodwillApplyDetail(){
        return businessGoodwillApplyDetail;
    }


    public void  setBusinessGoodwillApplyDetail(String businessGoodwillApplyDetail) {
        this.businessGoodwillApplyDetail = businessGoodwillApplyDetail;
            }
                                
    public Integer getIsValid(){
        return isValid;
    }


    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
            }
                                
    
    public Date getCreatedAt() {
		return createdAt;
	}


	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}


	public Date getUpdatedAt() {
		return updatedAt;
	}


	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}


	@Override
    public String toString() {
        return "GoodwillMaterialAuditDTO{" +
                                            "appId=" + appId +
                                                            ", ownerCode=" + ownerCode +
                                                            ", ownerParCode=" + ownerParCode +
                                                            ", orgId=" + orgId +
                                                            ", id=" + id +
                                                            ", goodwillApplyId=" + goodwillApplyId +
                                                            ", customerBackground=" + customerBackground +
                                                            ", customerBackgroundEn=" + customerBackgroundEn +
                                                            ", reasonAndDispose=" + reasonAndDispose +
                                                            ", reasonAndDisposeEn=" + reasonAndDisposeEn +
                                                            ", repairSolution=" + repairSolution +
                                                            ", repairSolutionEn=" + repairSolutionEn +
                                                            ", customerRequire=" + customerRequire +
                                                            ", customerRequireEn=" + customerRequireEn +
                                                            ", potentialRisk=" + potentialRisk +
                                                            ", potentialRiskEn=" + potentialRiskEn +
                                                            ", vrOrTjNoEn=" + vrOrTjNoEn +
                                                            ", vrOrTjNo=" + vrOrTjNo +
                                                            ", businessGoodwillApplyDetailEn=" + businessGoodwillApplyDetailEn +
                                                            ", businessGoodwillApplyDetail=" + businessGoodwillApplyDetail +
                                                            ", isValid=" + isValid +
                                                            ", isDeleted=" + isDeleted +
                                                            ", createdAt=" + createdAt +
                                                            ", updatedAt=" + updatedAt +
                                    "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
