package com.yonyou.dmscus.customer.entity.vo.accidentClues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@ApiModel("事故线索跟进计数")
@Accessors(chain = true)
public class AccidentClueFollowVo {
    @ApiModelProperty("跟进类型")
    private Integer followStatus;
    @ApiModelProperty("数量")
    private Integer count;
    private Integer notFollow;
    private Integer keepOnFollow;
    private Integer followTimeOut;
    private Integer total;
}
