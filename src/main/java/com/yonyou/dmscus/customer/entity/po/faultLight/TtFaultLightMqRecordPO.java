package com.yonyou.dmscus.customer.entity.po.faultLight;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("MQ消息记录")
@TableName("tt_fault_light_mq_record")
public class TtFaultLightMqRecordPO implements Serializable {

    @ApiModelProperty("主键id")
    @TableField("id")
    private Long id;

    @ApiModelProperty("icmId")
    @TableField("icm_id")
    private Long icmId;

    @ApiModelProperty("线索状态")
    @TableField("biz_status")
    private String bizStatus;

    @ApiModelProperty("跟进状态")
    @TableField("follow_up_status")
    private String followUpStatus;

    @ApiModelProperty("修改时间")
    @TableField("update_time")
    private Date updateTime;

    @ApiModelProperty("是否删除:1，删除；0，未删除")
    @TableField("is_deleted")
    private int isDeleted;

    @ApiModelProperty("数据创建时间")
    @TableField("created_at")
    private Date createdAt;

    @ApiModelProperty("数据创建人")
    @TableField("created_by")
    private String createdBy;

    @ApiModelProperty("数据修改时间")
    @TableField("updated_at")
    private Date updatedAt;

    @ApiModelProperty("数据修改人")
    @TableField("updated_by")
    private String updatedBy;

    @ApiModelProperty("创建sql人")
    @TableField("create_sqlby")
    private String createSqlby;

    @ApiModelProperty("更新人sql人")
    @TableField("update_sqlby")
    private String updateSqlby;
}
