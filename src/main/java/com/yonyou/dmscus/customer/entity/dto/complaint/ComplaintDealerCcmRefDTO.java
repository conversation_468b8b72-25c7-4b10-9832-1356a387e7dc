package com.yonyou.dmscus.customer.entity.dto.complaint;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import com.yonyou.dmscloud.function.utils.jsonSerializer.date.JsonDateDeserializer;
import com.yonyou.dmscloud.function.utils.jsonSerializer.date.JsonDateSerializer;

import java.io.Serializable;

import java.util.Date;

/**
 * <p>
 * 客户投诉进销商和CCM复杂人对应关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
    
public class ComplaintDealerCcmRefDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

        
    /**
     * 系统ID
     */
                private String appId;
                
    /**
     * 所有者代码 
     */
                private String ownerCode;
                
    /**
     * 所有者的父组织代码 
     */
                private String ownerParCode;
                
    /**
     * 组织ID
     */
                private Integer orgId;
                
    /**
     * 主键ID
     */
                private Long id;
                
    /**
     * 区域
     */
                private String region;
    /**
     * 区域ID
     */
                private  Long regionId;
                
    /**
     * 区域经理
     */
                private String regionManager;
    /**
     * 区域经理ID
     */
    private  Long regionManagerId;
                
    /**
     * 集团
     */
                private String bloc;
                
    /**
     * 进销商代码
     */
                private String dealerCode;
                
    /**
     * 进销商名称
     */
                private String dealerName;
                
    /**
     * CCM负责人
     */
                private String ccmMan;
                
    /**
     * CCM负责人ID
     */
                private String ccmManId;
                
    /**
     * 数据来源 
     */
                private Integer dataSources;
                
    /**
     * 是否删除
     */
                private Boolean isDeleted;
                
    /**
     * 是否有效 
     */
                private Integer isValid;
                
    /**
     * 创建时间
     */
                @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date createdAt;
                
    /**
     * 更新时间
     */
                @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date updatedAt;
            
    public ComplaintDealerCcmRefDTO() {
        super();
    }

                                
    public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
            }
                                
    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
            }
                                
    public String getOwnerParCode(){
        return ownerParCode;
    }


    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
            }
                                
    public Integer getOrgId(){
        return orgId;
    }


    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
            }
                                            
    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
            }
                                
    public String getRegion(){
        return region;
    }


    public void  setRegion(String region) {
        this.region = region;
            }
                                
    public String getRegionManager(){
        return regionManager;
    }


    public void  setRegionManager(String regionManager) {
        this.regionManager = regionManager;
            }
                                
    public String getBloc(){
        return bloc;
    }


    public void  setBloc(String bloc) {
        this.bloc = bloc;
            }
                                
    public String getDealerCode(){
        return dealerCode;
    }


    public void  setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
            }
                                
    public String getDealerName(){
        return dealerName;
    }


    public void  setDealerName(String dealerName) {
        this.dealerName = dealerName;
            }
                                
    public String getCcmMan(){
        return ccmMan;
    }


    public void  setCcmMan(String ccmMan) {
        this.ccmMan = ccmMan;
            }
                                
    public String getCcmManId(){
        return ccmManId;
    }


    public void  setCcmManId(String ccmManId) {
        this.ccmManId = ccmManId;
            }
                                
    public Integer getDataSources(){
        return dataSources;
    }


    public void  setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
            }
                                
    public Integer getIsValid(){
        return isValid;
    }


    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
            }
                                
    public Date getCreatedAt(){
        return createdAt;
    }


    public void  setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
            }
                                
    public Date getUpdatedAt(){
        return updatedAt;
    }


    public void  setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
            }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public Long getRegionManagerId() {
        return regionManagerId;
    }

    public void setRegionManagerId(Long regionManagerId) {
        this.regionManagerId = regionManagerId;
    }

    @Override
    public String toString() {
        return "ComplaintDealerCcmRefDTO{" +
                                            "appId=" + appId +
                                                            ", ownerCode=" + ownerCode +
                                                            ", ownerParCode=" + ownerParCode +
                                                            ", orgId=" + orgId +
                                                            ", id=" + id +
                                                            ", region=" + region +
                                                            ", regionManager=" + regionManager +
                                                            ", bloc=" + bloc +
                                                            ", dealerCode=" + dealerCode +
                                                            ", dealerName=" + dealerName +
                                                            ", ccmMan=" + ccmMan +
                                                            ", ccmManId=" + ccmManId +
                                                            ", dataSources=" + dataSources +
                                                            ", isDeleted=" + isDeleted +
                                                            ", isValid=" + isValid +
                                                            ", createdAt=" + createdAt +
                                                            ", updatedAt=" + updatedAt +
                                    "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
