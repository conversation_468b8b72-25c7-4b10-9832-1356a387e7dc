package com.yonyou.dmscus.customer.entity.dto.goodwill;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善审计明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-24
 */

public class GoodwillApplyAuditDetailDTO extends BaseDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	private String appId;

	/**
	 * 所有者代码
	 */
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	private Integer orgId;

	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 审计表主键
	 */
	private Long auditId;

	/**
	 * 审计方式
	 */
	private Integer auditWay;

	/**
	 * 审计时间
	 */
	private Date auditTime;

	/**
	 * 问题点
	 */
	private String troubleSpots;

	/**
	 * 处罚结果
	 */
	private String punishResult;

	/**
	 * 扣款金额
	 */
	private BigDecimal deductionsPrice;

	/**
	 * 是否通报
	 */
	private Integer isNotification;

	/**
	 * 是否有效
	 */
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	private Date createdAt;

	/**
	 * 修改时间
	 */
	private Date updatedAt;

	public GoodwillApplyAuditDetailDTO() {
		super();
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	public String getOwnerParCode() {
		return ownerParCode;
	}

	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getAuditId() {
		return auditId;
	}

	public void setAuditId(Long auditId) {
		this.auditId = auditId;
	}

	public Integer getAuditWay() {
		return auditWay;
	}

	public void setAuditWay(Integer auditWay) {
		this.auditWay = auditWay;
	}

	public String getTroubleSpots() {
		return troubleSpots;
	}

	public void setTroubleSpots(String troubleSpots) {
		this.troubleSpots = troubleSpots;
	}

	public String getPunishResult() {
		return punishResult;
	}

	public void setPunishResult(String punishResult) {
		this.punishResult = punishResult;
	}

	public BigDecimal getDeductionsPrice() {
		return deductionsPrice;
	}

	public void setDeductionsPrice(BigDecimal deductionsPrice) {
		this.deductionsPrice = deductionsPrice;
	}

	public Integer getIsNotification() {
		return isNotification;
	}

	public void setIsNotification(Integer isNotification) {
		this.isNotification = isNotification;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Date getAuditTime() {
		return auditTime;
	}

	public void setAuditTime(Date auditTime) {
		this.auditTime = auditTime;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	public String toString() {
		return "GoodwillApplyAuditDetailDTO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", auditId=" + auditId + ", auditWay=" + auditWay
				+ ", auditTime=" + auditTime + ", troubleSpots=" + troubleSpots + ", punishResult=" + punishResult
				+ ", deductionsPrice=" + deductionsPrice + ", isNotification=" + isNotification + ", isValid=" + isValid
				+ ", isDeleted=" + isDeleted + ", createdAt=" + createdAt + ", updatedAt=" + updatedAt + "}";
	}

	/**
	 * 将DTO 转换为PO //对某个对象属性进行赋值
	 * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
	 * 
	 * @param poClass
	 *            需要转换的poClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
		return super.transDtoToPo(poClass);
	}

	/**
	 * 将DTO 转换为PO
	 * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
	 * 
	 * @param po
	 *            需要转换的对象
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BasePO> void transDtoToPo(T po) {
		BeanMapperUtil.copyProperties(this, po, "id");
	}

}
