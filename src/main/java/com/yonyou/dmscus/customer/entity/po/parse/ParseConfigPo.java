package com.yonyou.dmscus.customer.entity.po.parse;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("tc_parse_config")
public class ParseConfigPo extends SimplePo{

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    *主表id
     * */
    private Long parseId;

    /*
    * 正则
    * */
    private String matchingRule;

    /*
    * 解析后的文本
    * */
    private String ruleText;

    /*
    * 字段名称
    * */
    private String fieldName;

    /*
     * 0 标题 1 正文
     * */
    private Integer parseType;

    /*
     * 解析地址
     * */
    private Integer parseIndex;


}
