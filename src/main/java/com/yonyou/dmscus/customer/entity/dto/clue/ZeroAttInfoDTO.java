package com.yonyou.dmscus.customer.entity.dto.clue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * description 零附件信息参数
 *
 * <AUTHOR>
 * @date 2023/8/21 17:56
 */
@Data
@ApiModel(description = "零附件信息参数")
public class ZeroAttInfoDTO {
	/**
	 * 会员id
	 */
	@ApiModelProperty(value = "会员id")
	private String vipId;

	/**
	 * 券码（领用id）
	 */
	@ApiModelProperty(value = "券码（领用id）")
	private String ticketId;

	/**
	 * 券码
	 */
	@ApiModelProperty(value = "券码")
	private String ticketCode;

	/**
	 * 卡券模板id
	 */
	@ApiModelProperty(value = "卡券模板id")
	private String ticketTemplateId;

	/**
	 * 卡券名称
	 */
	@ApiModelProperty(value = "卡券名称")
	private String ticketName;

	/**
	 * 核销时间
	 */
	@ApiModelProperty(value = "核销时间")
	private String verificationDate;

	/**
	 * 核销订单号
	 */
	@ApiModelProperty(value = "核销订单号")
	private String verificationOrder;

	/**
	 * 核销经销商
	 */
	@ApiModelProperty(value = "核销经销商")
	private String verificationDealerCode;


	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private String orderId;
}
