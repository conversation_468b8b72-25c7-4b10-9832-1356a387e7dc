package com.yonyou.dmscus.customer.entity.dto.accidentClues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/12/5 19:07
 * @Version 1.0
 */
@Data
@ApiModel(value = "DashBoardQueryDTO", description = "看板查询条件")
public class DashBoardQueryDTO {

    @ApiModelProperty(name = "查询开始日期")
    private String beginDate;

    @ApiModelProperty(name = "查询结束日期")
    private String endDate;

    @ApiModelProperty(name = "查询类型")
    private Integer queryType;
}
