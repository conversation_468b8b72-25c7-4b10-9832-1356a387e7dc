package com.yonyou.dmscus.customer.entity.dto.qb;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import java.util.Date;

/**
 * QB车辆
 */
@Data
public class QbNumberInfoDTO extends  BaseDTO{

  private Integer qbNumberId;

  private String qbNumber;

  private Date startingDate;

  private Date closingDate;

  private Integer isClosed;

  private Date closedDate;

  private String appId;

  private String ownerCode;

  private String ownerParCode;

  private long orgId;

  private long dataSources;

  private long isValid;

  private long isDeleted;

  private Date createdAt;

  private String createdBy;

  private Date updatedAt;

  private String updatedBy;

  private String mark;

  @Override
  public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
    return super.transDtoToPo(poClass);
  }

  public <T extends BasePO> void transDtoToPo(BaseDTO dto, T po) {
    BeanMapperUtil.copyProperties(dto, po);
  }

}
