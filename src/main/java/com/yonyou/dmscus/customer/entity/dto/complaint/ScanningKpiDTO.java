package com.yonyou.dmscus.customer.entity.dto.complaint;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

public class ScanningKpiDTO {

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date startDate;
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date endDate;
    /**
     * 区域id
     */
    private  String regionId;

    /**
     * 区域
     */
    private  String region;

    /**
     * 区域经理id
     */
    private  String regionManagerId;

    /**
     * 区域经理
     */
    private String regionManager;

    /**
     * 集团id
     */
    private  String blocId;

    /**
     * 集团
     */
    private  String bloc;

    /**
     * 小数点设置
     */
    private Integer decimalPoint;

    /**
     * 投诉率
     */
    private  String complaintRate;
    /**
     * 8小时回复率
     */
    private  String eightHourResponseRate;
    /**
     * 24小时回复率
     */
    private  String twentyfourHourResponseRate;
    /**
     * 3日结案率
     */
    private  String threeDayClosingRate;
    /**
     * 5日结案率
     */
    private  String fiveDayClosingRate;
    /**
     * 14日结案率
     */
    private  String fourteenDayClosingRate;
    /**
     * 回访率
     */
    private  String returnVisitRate;
    /**
     * 重启率
     */
    private  String restartRate;
    /**
     * 平均结案时间
     */
    private  String averageClosingTime;

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getComplaintRate() {
        return complaintRate;
    }

    public void setComplaintRate(String complaintRate) {
        this.complaintRate = complaintRate;
    }

    public String getEightHourResponseRate() {
        return eightHourResponseRate;
    }

    public void setEightHourResponseRate(String eightHourResponseRate) {
        this.eightHourResponseRate = eightHourResponseRate;
    }

    public String getTwentyfourHourResponseRate() {
        return twentyfourHourResponseRate;
    }

    public void setTwentyfourHourResponseRate(String twentyfourHourResponseRate) {
        this.twentyfourHourResponseRate = twentyfourHourResponseRate;
    }

    public String getThreeDayClosingRate() {
        return threeDayClosingRate;
    }

    public void setThreeDayClosingRate(String threeDayClosingRate) {
        this.threeDayClosingRate = threeDayClosingRate;
    }

    public String getFiveDayClosingRate() {
        return fiveDayClosingRate;
    }

    public void setFiveDayClosingRate(String fiveDayClosingRate) {
        this.fiveDayClosingRate = fiveDayClosingRate;
    }

    public String getFourteenDayClosingRate() {
        return fourteenDayClosingRate;
    }

    public void setFourteenDayClosingRate(String fourteenDayClosingRate) {
        this.fourteenDayClosingRate = fourteenDayClosingRate;
    }

    public String getReturnVisitRate() {
        return returnVisitRate;
    }

    public void setReturnVisitRate(String returnVisitRate) {
        this.returnVisitRate = returnVisitRate;
    }

    public String getRestartRate() {
        return restartRate;
    }

    public void setRestartRate(String restartRate) {
        this.restartRate = restartRate;
    }

    public String getAverageClosingTime() {
        return averageClosingTime;
    }

    public void setAverageClosingTime(String averageClosingTime) {
        this.averageClosingTime = averageClosingTime;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionManagerId() {
        return regionManagerId;
    }

    public void setRegionManagerId(String regionManagerId) {
        this.regionManagerId = regionManagerId;
    }

    public String getRegionManager() {
        return regionManager;
    }

    public void setRegionManager(String regionManager) {
        this.regionManager = regionManager;
    }

    public String getBlocId() {
        return blocId;
    }

    public void setBlocId(String blocId) {
        this.blocId = blocId;
    }

    public String getBloc() {
        return bloc;
    }

    public void setBloc(String bloc) {
        this.bloc = bloc;
    }

    public Integer getDecimalPoint() {
        return decimalPoint;
    }

    public void setDecimalPoint(Integer decimalPoint) {
        this.decimalPoint = decimalPoint;
    }
}
