package com.yonyou.dmscus.customer.entity.po.inviteRule;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import com.yonyou.dmscloud.framework.base.po.BasePO;

import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;

/**
 * <p>
 * 邀约易损件和项目规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
@TableName("tt_invite_part_item_rule")
public class InvitePartItemRulePO extends BasePO<InvitePartItemRulePO> {

    private static final long serialVersionUID = 1L;


    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;


    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经销商代码（VCDC：代表厂端，经销商代码：代表各自经销商）
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 类型：易损件、项目
     */
    @TableField("type")
    private Integer type;

    /**
     * 零件、维修项目编号
     */
    @TableField("code")
    private String code;

    /**
     * 零件、维修项目名称
     */
    @TableField("name")
    private String name;

    /**
     * 邀约生成时间 ：NULL 代表 每天 0  每月，1~12  1~12月
     */
    @TableField("invite_create_month")
    private Integer inviteCreateMonth;

    /**
     * 提前N天邀约
     */
    @TableField("day_in_advance")
    private Integer dayInAdvance;

    /**
     * 再提醒间隔（月）
     */
    @TableField("remind_interval")
    private Integer remindInterval;

    /**
     * 间隔里程（Km）
     */
    @TableField("mileage_interval")
    private Integer mileageInterval;

    /**
     * 间隔日期（月）
     */
    @TableField("date_interval")
    private Integer dateInterval;

    /**
     * 车架号 必须输入17位，---K---45—3----
     */
    @TableField("vin")
    private String vin;

    /**
     * 车型编码
     */
    @TableField("model_code")
    private String modelCode;

    /**
     * 年款
     */
    @TableField("model_year")
    private String modelYear;

    /**
     * 发动机编码
     */
    @TableField("engine_code")
    private String engineCode;

    /**
     * 变速箱编码
     */
    @TableField("gearbox_code")
    private String gearboxCode;

    /**
     * 规则关系：and 和，or 或
     */
    @TableField("rule_relationship")
    private Integer ruleRelationship;

    /**
     * 是否启用：1 启用，0 不启用
     */
    @TableField("is_use")
    private Integer isUse;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;



    public InvitePartItemRulePO() {
        super();
    }




    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }


    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDealerCode() {
        return dealerCode;
    }

    public void setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getInviteCreateMonth() {
        return inviteCreateMonth;
    }

    public void setInviteCreateMonth(Integer inviteCreateMonth) {
        this.inviteCreateMonth = inviteCreateMonth;
    }

    public Integer getDayInAdvance() {
        return dayInAdvance;
    }

    public void setDayInAdvance(Integer dayInAdvance) {
        this.dayInAdvance = dayInAdvance;
    }

    public Integer getRemindInterval() {
        return remindInterval;
    }

    public void setRemindInterval(Integer remindInterval) {
        this.remindInterval = remindInterval;
    }

    public Integer getMileageInterval() {
        return mileageInterval;
    }

    public void setMileageInterval(Integer mileageInterval) {
        this.mileageInterval = mileageInterval;
    }

    public Integer getDateInterval() {
        return dateInterval;
    }

    public void setDateInterval(Integer dateInterval) {
        this.dateInterval = dateInterval;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getModelYear() {
        return modelYear;
    }

    public void setModelYear(String modelYear) {
        this.modelYear = modelYear;
    }

    public String getEngineCode() {
        return engineCode;
    }

    public void setEngineCode(String engineCode) {
        this.engineCode = engineCode;
    }

    public String getGearboxCode() {
        return gearboxCode;
    }

    public void setGearboxCode(String gearboxCode) {
        this.gearboxCode = gearboxCode;
    }

    public Integer getRuleRelationship() {
        return ruleRelationship;
    }

    public void setRuleRelationship(Integer ruleRelationship) {
        this.ruleRelationship = ruleRelationship;
    }

    public Integer getIsUse() {
        return isUse;
    }

    public void setIsUse(Integer isUse) {
        this.isUse = isUse;
    }

    public Integer getDataSources() {
        return dataSources;
    }

    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "InvitePartItemRulePO{" +
                ", ownerCode=" + ownerCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", dealerCode=" + dealerCode +
                ", type=" + type +
                ", code=" + code +
                ", name=" + name +
                ", inviteCreateMonth=" + inviteCreateMonth +
                ", dayInAdvance=" + dayInAdvance +
                ", remindInterval=" + remindInterval +
                ", mileageInterval=" + mileageInterval +
                ", dateInterval=" + dateInterval +
                ", vin=" + vin +
                ", modelCode=" + modelCode +
                ", modelYear=" + modelYear +
                ", engineCode=" + engineCode +
                ", gearboxCode=" + gearboxCode +
                ", ruleRelationship=" + ruleRelationship +
                ", isUse=" + isUse +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                "}";
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
