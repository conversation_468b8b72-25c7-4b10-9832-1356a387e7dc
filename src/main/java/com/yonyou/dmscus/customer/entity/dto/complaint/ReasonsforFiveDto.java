package com.yonyou.dmscus.customer.entity.dto.complaint;

public class ReasonsforFiveDto {

    private  Long complaintInfoId;
    private  reason reason;

    public Long getComplaintInfoId() {
        return complaintInfoId;
    }

    public void setComplaintInfoId(Long complaintInfoId) {
        this.complaintInfoId = complaintInfoId;
    }

    public ReasonsforFiveDto.reason getReason() {
        return reason;
    }

    public void setReason(ReasonsforFiveDto.reason reason) {
        this.reason = reason;
    }

    public  static class  reason{
        private  String customerReason;
        private  Boolean customerReason1;
        private  Boolean customerReason2;
        private  Boolean customerReason3;
        private  Boolean customerReason4;
        private  Boolean customerReason5;
        private  Boolean customerReason6;
        private  Boolean customerReason7;
        private  Boolean dearReason1;
        private  Boolean dearReason2;
        private  Boolean dearReason3;
        private  Boolean dearReason4;
        private  Boolean dearReason5;
        private  Boolean dearReason6;
        private  Boolean dearReason7;
        private  Boolean dearReason8;
        private  Boolean dearReason9;
        private  Boolean vcdcReason1;
        private  Boolean vcdcReason2;
        private  Boolean vcdcReason3;
        private  Boolean vcdcReason4;
        private  Boolean vcdcReason5;
        private  Boolean vcdcReason6;
        private  Boolean thirdReason1;
        private  Boolean thirdReason2;
        private  Boolean thirdReason3;
        private  Boolean thirdReason4;
        private  Boolean thirdReason5;
        private  Boolean thirdReason6;
        private  String input1;
        private  String input2;
        private  String input3;
        private  String input5;

        public Boolean getDearReason9() {
            return dearReason9;
        }

        public void setDearReason9(Boolean dearReason9) {
            this.dearReason9 = dearReason9;
        }

        public Boolean getVcdcReason6() {
            return vcdcReason6;
        }

        public void setVcdcReason6(Boolean vcdcReason6) {
            this.vcdcReason6 = vcdcReason6;
        }

        public Boolean getThirdReason6() {
            return thirdReason6;
        }

        public void setThirdReason6(Boolean thirdReason6) {
            this.thirdReason6 = thirdReason6;
        }

        public Boolean getDearReason1() {
            return dearReason1;
        }

        public void setDearReason1(Boolean dearReason1) {
            this.dearReason1 = dearReason1;
        }

        public Boolean getDearReason2() {
            return dearReason2;
        }

        public void setDearReason2(Boolean dearReason2) {
            this.dearReason2 = dearReason2;
        }

        public Boolean getDearReason3() {
            return dearReason3;
        }

        public void setDearReason3(Boolean dearReason3) {
            this.dearReason3 = dearReason3;
        }

        public Boolean getDearReason4() {
            return dearReason4;
        }

        public void setDearReason4(Boolean dearReason4) {
            this.dearReason4 = dearReason4;
        }

        public Boolean getDearReason5() {
            return dearReason5;
        }

        public void setDearReason5(Boolean dearReason5) {
            this.dearReason5 = dearReason5;
        }

        public Boolean getDearReason6() {
            return dearReason6;
        }

        public void setDearReason6(Boolean dearReason6) {
            this.dearReason6 = dearReason6;
        }

        public Boolean getDearReason7() {
            return dearReason7;
        }

        public void setDearReason7(Boolean dearReason7) {
            this.dearReason7 = dearReason7;
        }

        public Boolean getDearReason8() {
            return dearReason8;
        }

        public void setDearReason8(Boolean dearReason8) {
            this.dearReason8 = dearReason8;
        }

        public Boolean getVcdcReason1() {
            return vcdcReason1;
        }

        public void setVcdcReason1(Boolean vcdcReason1) {
            this.vcdcReason1 = vcdcReason1;
        }

        public Boolean getVcdcReason2() {
            return vcdcReason2;
        }

        public void setVcdcReason2(Boolean vcdcReason2) {
            this.vcdcReason2 = vcdcReason2;
        }

        public Boolean getVcdcReason3() {
            return vcdcReason3;
        }

        public void setVcdcReason3(Boolean vcdcReason3) {
            this.vcdcReason3 = vcdcReason3;
        }

        public Boolean getVcdcReason4() {
            return vcdcReason4;
        }

        public void setVcdcReason4(Boolean vcdcReason4) {
            this.vcdcReason4 = vcdcReason4;
        }

        public Boolean getVcdcReason5() {
            return vcdcReason5;
        }

        public void setVcdcReason5(Boolean vcdcReason5) {
            this.vcdcReason5 = vcdcReason5;
        }

        public Boolean getThirdReason1() {
            return thirdReason1;
        }

        public void setThirdReason1(Boolean thirdReason1) {
            this.thirdReason1 = thirdReason1;
        }

        public Boolean getThirdReason2() {
            return thirdReason2;
        }

        public void setThirdReason2(Boolean thirdReason2) {
            this.thirdReason2 = thirdReason2;
        }

        public Boolean getThirdReason3() {
            return thirdReason3;
        }

        public void setThirdReason3(Boolean thirdReason3) {
            this.thirdReason3 = thirdReason3;
        }

        public Boolean getThirdReason4() {
            return thirdReason4;
        }

        public void setThirdReason4(Boolean thirdReason4) {
            this.thirdReason4 = thirdReason4;
        }

        public Boolean getThirdReason5() {
            return thirdReason5;
        }

        public void setThirdReason5(Boolean thirdReason5) {
            this.thirdReason5 = thirdReason5;
        }

        public String getCustomerReason() {
            return customerReason;
        }

        public void setCustomerReason(String customerReason) {
            this.customerReason = customerReason;
        }

        public Boolean getCustomerReason1() {
            return customerReason1;
        }

        public void setCustomerReason1(Boolean customerReason1) {
            this.customerReason1 = customerReason1;
        }

        public Boolean getCustomerReason2() {
            return customerReason2;
        }

        public void setCustomerReason2(Boolean customerReason2) {
            this.customerReason2 = customerReason2;
        }

        public Boolean getCustomerReason3() {
            return customerReason3;
        }

        public void setCustomerReason3(Boolean customerReason3) {
            this.customerReason3 = customerReason3;
        }

        public Boolean getCustomerReason4() {
            return customerReason4;
        }

        public void setCustomerReason4(Boolean customerReason4) {
            this.customerReason4 = customerReason4;
        }

        public Boolean getCustomerReason5() {
            return customerReason5;
        }

        public void setCustomerReason5(Boolean customerReason5) {
            this.customerReason5 = customerReason5;
        }

        public Boolean getCustomerReason6() {
            return customerReason6;
        }

        public void setCustomerReason6(Boolean customerReason6) {
            this.customerReason6 = customerReason6;
        }

        public Boolean getCustomerReason7() {
            return customerReason7;
        }

        public void setCustomerReason7(Boolean customerReason7) {
            this.customerReason7 = customerReason7;
        }

        public String getInput1() {
            return input1;
        }

        public void setInput1(String input1) {
            this.input1 = input1;
        }

        public String getInput2() {
            return input2;
        }

        public void setInput2(String input2) {
            this.input2 = input2;
        }

        public String getInput3() {
            return input3;
        }

        public void setInput3(String input3) {
            this.input3 = input3;
        }

        public String getInput5() {
            return input5;
        }

        public void setInput5(String input5) {
            this.input5 = input5;
        }
    }
}
