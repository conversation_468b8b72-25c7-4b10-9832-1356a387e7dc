package com.yonyou.dmscus.customer.entity.dto.complaint.AcceptComlaintData;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 接受400下发客诉单跟新信息
 */
@Data
public class AcceptUpdataInfoDTO {

    /**
     * 投诉ID（投诉编号）可 用于和呼叫中心对接
     */
    private String complaintId;

    /**
     * 工单状态
     */
    private Integer workOrderStatus;

    /**
     * 结案时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date closeCaseTime;

    /**
     * 跟进内容
     */
    private String followContent;

}
