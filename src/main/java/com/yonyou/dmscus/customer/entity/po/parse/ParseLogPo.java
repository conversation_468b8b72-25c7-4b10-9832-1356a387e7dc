package com.yonyou.dmscus.customer.entity.po.parse;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("tt_parse_log")
public class ParseLogPo extends SimplePo{

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long configId;

    private Long parseId;

    private String msgText;

    private String parseText;

    private Boolean parseResult;


}
