package com.yonyou.dmscus.customer.entity.dto.inviteInsurance;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 车辆续保线索导入临时表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-19
 */

@Data
@ToString
public class InviteInsuranceVehicleRecordImportDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 邀约ID
     */
    private Long id;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车牌号
     */
    private String licensePlateNum;

    /**
     * 车主名称
     */
    private String name;

    /**
     * 车主电话
     */
    private String tel;

    /**
     * 商业险到期日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date viFinishDate;

    /**
     * 交强险到期日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date clivtaFinishDate;

    /**
     * 跟进服务顾问ID
     */
    private String saId;

    /**
     * 跟进服务顾问姓名
     */
    private String saName;

    /**
     * 上次跟进服务顾问ID
     */
    private String lastSaId;

    /**
     * 上次跟进服务顾问姓名
     */
    private String lastSaName;


    /**
     * 经销商代码
     */
    private String dealerCode;

    /**
     * 车主年龄
     */
    private String age;

    /**
     * 车主性别
     */
    private String sex;

    /**
     * 车型
     */
    private String model;

    /**
     * 客户唯一ID
     */
    private Long oneId;

    /**
     * 数据来源：1:易保线索  2:投保线索  3:导入线索  4:迁移线索
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    private LocalDateTime updatedAt;

    /**
     * 是否错误,1：是,0：否
     */
    private Integer isError;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 行数
     */
    private Integer lineNumber;

    public InviteInsuranceVehicleRecordImportDTO() {
        super();
    }


    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
