package com.yonyou.dmscus.customer.entity.dto.middleground;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import com.yonyou.dmscus.customer.middleInterface.Constant;

@ApiModel(description = "返回结果")
public class ResponseDTO<T> implements Serializable {
	private static final long serialVersionUID = Constant.SERIAL_VERSION_UID;

	@ApiModelProperty(value = "返回代码，0表示成功，其他表示失败")
	private String returnCode;

	@ApiModelProperty(value = "返回描述")
	private String returnMessage;
	
	@ApiModelProperty(value = "错误原因")
	private String cause;

	@ApiModelProperty(value = "返回数据")
	private T data;

	public String getReturnCode() {
		return returnCode;
	}

	public void setReturnCode(String returnCode) {
		this.returnCode = returnCode;
	}

	public String getReturnMessage() {
		return returnMessage;
	}

	public void setReturnMessage(String returnMessage) {
		this.returnMessage = returnMessage;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}

	@Override
	public String toString() {
		return "ResponseDTO{" + "returnCode='" + returnCode + '\'' + ", returnMessage='" + returnMessage + '\''
				+ ", data=" + data + '}';
	}

	/**
	 * @return the cause
	 */
	public String getCause() {
		return cause;
	}

	/**
	 * @param cause the cause to set
	 */
	public void setCause(String cause) {
		this.cause = cause;
	}
	
	
}
