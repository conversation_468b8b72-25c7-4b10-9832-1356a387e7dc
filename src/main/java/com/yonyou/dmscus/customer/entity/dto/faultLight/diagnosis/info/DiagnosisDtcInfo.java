package com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.info;

import java.util.Date;
import java.util.Map;

public class DiagnosisDtcInfo {
    private String dtcName;
    private Map<String, DiagnosisSnapshotInfo> snapshots;
    private Map<String, String> statusByExt;
    private Map<String, String> strings;
    private Map<String, String> statusIndicator;
    private Map<String, String> doubles;
    private Map<String, String> statusByMask;
    private Map<String, String> snapshotsStrings;
    private Date dtc_time;

    public String getDtcName() {
        return dtcName;
    }

    public void setDtcName(String dtcName) {
        this.dtcName = dtcName;
    }

    public Map<String, DiagnosisSnapshotInfo> getSnapshots() {
        return snapshots;
    }

    public void setSnapshots(Map<String, DiagnosisSnapshotInfo> snapshots) {
        this.snapshots = snapshots;
    }

    public Map<String, String> getStatusByExt() {
        return statusByExt;
    }

    public void setStatusByExt(Map<String, String> statusByExt) {
        this.statusByExt = statusByExt;
    }

    public Map<String, String> getStrings() {
        return strings;
    }

    public void setStrings(Map<String, String> strings) {
        this.strings = strings;
    }

    public Map<String, String> getStatusIndicator() {
        return statusIndicator;
    }

    public void setStatusIndicator(Map<String, String> statusIndicator) {
        this.statusIndicator = statusIndicator;
    }

    public Map<String, String> getDoubles() {
        return doubles;
    }

    public void setDoubles(Map<String, String> doubles) {
        this.doubles = doubles;
    }

    public Map<String, String> getStatusByMask() {
        return statusByMask;
    }

    public void setStatusByMask(Map<String, String> statusByMask) {
        this.statusByMask = statusByMask;
    }

    public Map<String, String> getSnapshotsStrings() {
        return snapshotsStrings;
    }

    public void setSnapshotsStrings(Map<String, String> snapshotsStrings) {
        this.snapshotsStrings = snapshotsStrings;
    }

    public Date getDtc_time() {
        return dtc_time;
    }

    public void setDtc_time(Date dtc_time) {
        this.dtc_time = dtc_time;
    }
}
