package com.yonyou.dmscus.customer.entity.dto.qb;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import lombok.Data;

import java.util.Date;

@Data
public class QbVehicleInfoDTO extends BaseDTO {

  private Integer id;

  private Integer qbNumberId;

  private String vin;

  private String dealerCode;

  private String dealerName;

  private Integer isPerformed;

  private Date performedTime;

  private String appId;

  private String ownerCode;

  private String ownerParCode;

  private Integer orgId;

  private Integer dataSources;

  private Integer isValid;

  private Integer isDeleted;

  private Date createdAt;

  private String createdBy;

  private Date updatedAt;

  private String updatedBy;

  private String mark;

  private String qbNumber;

  private Integer isClosed;

}
