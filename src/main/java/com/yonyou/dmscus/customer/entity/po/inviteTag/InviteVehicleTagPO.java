package com.yonyou.dmscus.customer.entity.po.inviteTag;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import io.swagger.models.auth.In;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tt_invite_vehicle_tag")
public class InviteVehicleTagPO extends BasePO<InviteVehicleTagPO> {

	@TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("vin")
    private String vin;

    @TableField("tag_id")
    private Long tagId;

    @TableField("bind_type")
    private Integer bindType;

    @TableField("create_date")
    private Date createDate;

    @TableLogic
    @TableField("is_deleted")
    private Boolean deleted;

}
