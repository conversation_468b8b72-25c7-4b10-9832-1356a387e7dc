package com.yonyou.dmscus.customer.entity.dto.clue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * description 邀约线索查询通用接口入参
 *
 * <AUTHOR>
 * @date 2023/10/09 10:02
 */
@Data
@ApiModel(description = "邀约线索查询通用接口入参")
public class InviteClueParamDTO {
    //最大页码数100
    public static final Integer MAX_PAGE_SIZE = 100;
    //当前页
    public static final Integer DEFAULT_CURRENT_PAGE = 1;
    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号", required = true)
    private List<String> vin;

    /**
     * 验证状态
     */
    @ApiModelProperty(value = "验证状态", example = "[87911001,87911002,87911003]")
    private List<Integer> verifyStatus;

    /**
     * 线索状态
     */
    @ApiModelProperty(value = "线索状态", example = "[82411001,82411002,82411003]")
    private List<Integer> orderStatus;

    /**
     * 邀约类型
     */
    @ApiModelProperty(value = "邀约类型", example = "[82381001,82381002,82381006,82381012]")
    private List<Integer> inviteType;

    /**
     * 跟进状态
     */
    @ApiModelProperty(value = "跟进状态", example = "[82401001,82401002,82401003,82401004,82401005]")
    private List<Integer> followStatus;


    /**
     * 邀约线索下发来源
     */
    @ApiModelProperty(value = "邀约线索来源", example = "[1,2,3,4]")
    private List<Integer> sourceType;

    /**
     * 经销商
     */
    @ApiModelProperty(value = "经销商", example = "SHJ")
    private String dealerCode;

    /**
     * 建议进场开始时间
     */
    @ApiModelProperty(value = "建议进场开始时间", example = "2023-09-09 00:00:00")
    private String adviseStartDate;

    /**
     * 建议进场结束时间
     */
    @ApiModelProperty(value = "建议进场结束时间", example = "2023-10-09 00:00:00")
    private String adviseEndDate;

    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页", required = true, example = "1")
    private Integer currentPage = DEFAULT_CURRENT_PAGE;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码", required = true, example = "100")
    private Integer pageSize = MAX_PAGE_SIZE;

    public void setPageSize(Integer pageSize) {
        if (pageSize > MAX_PAGE_SIZE) {
            this.pageSize = MAX_PAGE_SIZE;
        } else {
            this.pageSize = pageSize;
        }
    }
}
