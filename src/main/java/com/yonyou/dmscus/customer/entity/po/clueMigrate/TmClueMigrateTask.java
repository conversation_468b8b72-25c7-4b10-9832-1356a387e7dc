package com.yonyou.dmscus.customer.entity.po.clueMigrate;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 线索迁移任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Getter
@Setter
@TableName("tm_clue_migrate_task")
public class TmClueMigrateTask implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 同步类型 1 店店迁移 2 车店迁移
     */
    private Integer syncType;

    /**
     * 原经销商代码
     */
    private String sOwnerCode;

    /**
     * 经销商代码
     */
    private String ownerCode;

    private String vin;

    /**
     * 同步状态 0 初始等待 1 完成 2 失败
     */
    private Integer syncStatus;


    private LocalDateTime startTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    private String remark;

    private String createdBy;

    private String updatedBy;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private String appId;

    private String ownerParCode;

    private Integer recordVersion;

    private Integer isDeleted;

    @TableField("migrated_cdp")
    private Integer migratedCdp;
}
