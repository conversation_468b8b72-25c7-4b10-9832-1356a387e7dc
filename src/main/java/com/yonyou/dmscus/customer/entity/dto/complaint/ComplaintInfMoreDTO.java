package com.yonyou.dmscus.customer.entity.dto.complaint;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;
import com.yonyou.dmscloud.function.utils.jsonSerializer.date.JsonDateDeserializer;
import com.yonyou.dmscloud.function.utils.jsonSerializer.date.JsonDateSerializer;
import com.yonyou.dmscus.customer.enums.CompanyBusinessStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;


/**
 * <p>
 * 客户投诉信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */

public class ComplaintInfMoreDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;


    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 投诉ID（投诉编号）可 用于和呼叫中心对接
     */
    private String complaintId;

    /**
     * 投诉类型
     */
    private String type;

    /**
     * 投诉分类
     */
    private Integer classification;

    /**
     * 投诉来源
     */
    private String source;

    /**
     * 通知时间
     */
    @TableField("inform_time")
    private String informTime;

    /**
     * 通知时间
     */
    @TableField("first_restart_time")
    private String firstRestartTime;

    /**
     * 来电时间/投诉日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date callTime;
    /**
     * 来电时间/投诉日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date callTime2;
    /**
     * 来电时间/投诉日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actuallFollowTime;
    /**
     * 来电时间/投诉日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actuallFollowTime1;

    /**
     * 首次重启日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date fisrtRestartTime;
    /**
     * 首次重启日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date fisrtRestartTime2;


    /**
     * 最新重启日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date newestRestartTime;


    /**
     * 首次重启结案时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstRestartCloseCaseTime;

    /**
     * 来电客户姓名/投诉人姓名
     */
    private String callName;
    /**
     * 投诉人性别
     */
    private Integer sex;


    /**
     * 来电电话/投诉人电话
     */
    private String callTel;

    /**
     * 车主姓名
     */
    private String name;

    /**
     * 车牌号
     */
    private String licensePlateNum;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 购车时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date buyTime;

    /**
     * 车型
     */
    private String model;

    /**
     * 年款
     */
    private String modelYear;

    /**
     * 车款名称
     */
    private String configName;

    /**
     * 购买经销商
     */
    private String buyDealerName;

    /**
     * 处理经销商
     */
    private String dealerName;

    /**
     * 处理经销商代码
     */
    private String dealerCode;

    /**
     * 购买经销商代码
     */
    private String buyDealerCode;

    /**
     * 里程
     */
    private Integer mileage;
    /**
     * 车主地址
     */

    private String ownerAddress;

    /**
     * 回复联系人手机1
     */
    private String replyTel;

    /**
     * 回复联系人手机2
     */
    private String replyTel2;

    /**
     * 投诉主题
     */
    private String subject;

    /**
     * 问题描述
     */
    private String problem;

    /**
     * 坐席主管说明
     */
    private String illustrate;

    /**
     * 投诉单类别一级层
     */
    private String category1;

    /**
     * 投诉单类别二级层
     */
    private String category2;

    /**
     * 投诉单类别三级层
     */
    private String category3;
    /**
     * 部位
     */
    private String part;

    /**
     * 细分部位
     */
    private String subdivisionPart;

    /**
     * CC部位
     */
    private String ccPart;

    /**
     * CC细分部位
     */
    private String ccSubdivisionPart;

    /**
     * CC问题
     */
    private String ccProblem;

    /**
     * 工单状态
     */
    private Integer workOrderStatus;
    /**
     * 是否结案
     */
    private Integer isCloseCase;

    /**
     * CC要求
     */
    private String ccRequirement;

    /**
     * 投诉部门
     */
    private String department;

    /**
     * 接待员
     */
    private String receptionist;

    /**
     * 重要等级
     */
    private Integer importanceLevel;

    /**
     * 经销商首次回复时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dealerFisrtReplyTime;

    /**
     * 首次重启经销商首次回复时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date fisrtRestartDealerFisrtReplyTime;

    /**
     * 是否回访
     */
    private Integer isRevisit;

    /**
     * 回访时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date revisitTime;

    /**
     * 回访结果
     */
    private String revisitResult;

    /**
     * 回访内容
     */
    private String revisitContent;

    /**
     * 经销商申请结案时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

    /**
     * 区域经理是否同意
     */
    private Integer isAgree;

    /**
     * 区域经理提交结案时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitTime;

    /**
     * 结案时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date closeCaseTime;

    /**
     * 重启结案时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date restartCloseCaseTime;

    /**
     * 结案状态（投诉单状态）
     */
    private Integer closeCaseStatus;

    /**
     * 跟进状态
     */
    private String followStatus;

    /**
     * 投诉的根本原因 多选用逗号分隔
     */
    private String basicReason;

    /**
     * 车辆是否修复
     */
    private Integer isRepaired;
    /**
     * 是否上报
     */
    private Boolean isReport;


    /**
     * 技术与维修方案
     */
    private String techMaintainPlan;

    /**
     * 亲善方案
     */
    private String rapportPlan;

    /**
     * 潜在风险
     */
    private String risk;

    /**
     * 进销商是否已读 1 已读 0 未读
     */
    private Boolean dealerIsRead;

    /**
     * 区域经理是否已读 1 已读 0 未读
     */
    private Boolean managerIsRead;

    /**
     * CCM是否已读 1 已读 0 未读
     */
    private Boolean ccmIsRead;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 区域
     */
    private String region;

    /**
     * 区域经理
     */
    private String regionManager;

    /**
     * 小区
     */
    private String areaId;

    /**
     * 集团
     */
    private String bloc;
    /**
     * 集团Id
     */
    private String blocId;

    /**
     * 进销商代码
     */

    /**
     * CCM负责人
     */
    private String ccmMan;

    /**
     * CCM负责人ID
     */
    private String ccmManId;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date createdAt;

    /**
     * 更新时间
     */
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date updatedAt;


    /**
     * 质量部分类1
     */

    private Integer qualityClassification1;

    /**
     * 质量部分类2
     */

    private Integer qualityClassification2;
    /**
     * 质量部分类3
     */

    private String qualityClassification3;
    /**
     * 质量部分类4
     */
    @TableField("quality_classification4")
    private Integer qualityClassification4;
    /**
     * 故障分类
     */

    private String faultClassification;
    /**
     * 备注1
     */

    private String remark1;
    /**
     * 备注2
     */

    private String remark2;
    /**
     * 备注3
     */

    private String remark3;
    /**
     * 备注4
     */

    private String remark4;

    /**
     * CCM跟进内容是否全网发布 1 公开 0 不公开
     */
    private Boolean ccmNotPublish;

    /**
     * 跟进内容
     */
    private String followContent;

    /**
     * CCM主题
     */
    private String ccmSubject;

    /**
     * 最新状态
     */
    private String status;

    /**
     * 防止再发建议
     */
    private Integer advise;

    /**
     * CCM部位 多选用逗号分隔
     */
    private String ccmPart;

    /**
     * CCM细分部位 多选用逗号分隔
     */
    private String ccmSubdivisionPart;

    /**
     * CCM主要原因 多选用逗号分隔
     */
    private String ccMainReason;

    /**
     * CC解决结果 多选用逗号分隔
     */
    private String ccResult;

    /**
     * 关键字
     */
    private String keyword;
    /**
     * 分类1
     */
    private Integer classification1;

    /**
     * 分类2
     */
    private String classification2;

    /**
     * 分类3
     */
    private String classification3;

    /**
     * 分类4
     */
    private String classification4;

    /**
     * 分类5
     */
    private String classification5;

    /**
     * 分类6
     */
    private String classification6;

    /**
     * 下次跟进时间
     */
    private Date planFollowTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date closeCaseTime2;
    private Date firstCloseCaseTime2;
    private Date firstRestartCloseCaseTime2;

    private Integer currentPage;

    private Integer pageSize;

    private List bloc1;
    private List dealerCode1;
    private List ccmManId1;
    private List region1;
    private List regionManager1;
    private List areaId1;


    private List model1;
    private List source1;
    private List workOrderStatus1;
    private List importanceLevel1;
    private List type1;
    private String[] actuallFollowTime2;
    private String[] callTime1;
    private String[] closeCaseTime1;
    private String[] firstCloseCaseTime1;
    private String[] firstRestartCloseCaseTime1;
    private String[] fisrtRestartTime1;
    private String[] planFollowTime5;
    private String classification11;
    private String classification21;
    private String classification31;
    private String smallClass;
    private String sql;
    private String sql1;
    private String buyDealName;
    private String buyBloc;
    private String buyRegionManager;
    private String handleDealName;
    private String handleBloc;
    private String handleRegionManager;
    private String handleRegion;
    private String bigClassName;
    private String smallClassNameOther;
    private String assitdep;
    private List followStatus1;
    private List ccMainReason1;
    private List ccResult1;
    private List ccmPart1;
    private List ccmSubdivisionPart1;
    private List category11;
    private List category21;
    private List category31;
    private List classification12;
    private List classification22;
    private List smallClass1;
    private String assisDepartment;

    private String isValid1;

    private String workOrderStatusData;

    private String importanceLevelData;

    private String lastFollowTime;
    private String isFiveCloseCase;
    private String is24HourReply;
    private String is48HourReply;
    private  Integer invalidCaseHidden;

    @ApiModelProperty(value = "风险分类(SCRT1001 售前-一般案件、SCRT1002 售前-重要案件 SCRT1003 售前-预警案件 、SCRT1004 售前-重要案件-媒体风险)")
    private String riskType;

    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    @ApiModelProperty(value = "客诉案件创建时间 yyyy-MM-dd HH:mm:ss")
    private LocalDateTime caseCreateTime;

    /**
     * 结案目标天数
     */
    @ApiModelProperty(value = "结案目标天数")
    private Integer closeCaseDays;

    public Integer getCloseCaseDays() {
        return closeCaseDays;
    }

    public void setCloseCaseDays(Integer closeCaseDays) {
        this.closeCaseDays = closeCaseDays;
    }

    public String getRiskType() {
        return riskType;
    }

    public void setRiskType(String riskType) {
        this.riskType = riskType;
    }

    public LocalDateTime getCaseCreateTime() {
        return caseCreateTime;
    }

    public void setCaseCreateTime(LocalDateTime caseCreateTime) {
        this.caseCreateTime = caseCreateTime;
    }

    public Integer getInvalidCaseHidden() {
        return invalidCaseHidden;
    }

    public void setInvalidCaseHidden(Integer invalidCaseHidden) {
        this.invalidCaseHidden = invalidCaseHidden;
    }

    public String getWorkOrderStatusData() {
        return workOrderStatusData;
    }

    public void setWorkOrderStatusData(String workOrderStatusData) {
        this.workOrderStatusData = workOrderStatusData;
    }

    public String getImportanceLevelData() {
        return importanceLevelData;
    }

    public void setImportanceLevelData(String importanceLevelData) {
        this.importanceLevelData = importanceLevelData;
    }

    public List getWorkOrderStatus1() {
        return workOrderStatus1;
    }

    public void setWorkOrderStatus1(List workOrderStatus1) {
        this.workOrderStatus1 = workOrderStatus1;
    }

    public List getImportanceLevel1() {
        return importanceLevel1;
    }

    public void setImportanceLevel1(List importanceLevel1) {
        this.importanceLevel1 = importanceLevel1;
    }

    public String getAssisDepartment() {
        return assisDepartment;
    }

    public void setAssisDepartment(String assisDepartment) {
        this.assisDepartment = assisDepartment;
    }

    public String getIsValid1() {
        return isValid1;
    }

    public void setIsValid1(String isValid1) {
        this.isValid1 = isValid1;
    }

    /**
     * 区域ID
     */
    private String regionId;

    /**
     * 区域经理ID
     */
    private String regionManagerId;

    /**
     * 客户是否满意
     */
    private Integer isSatisfied;

    private String role;

    /**
     * 工单性质
     */
    private Integer workOrderNature;

    /**
     * 工单分类
     */
    private Integer workOrderClassification;

    /**
     * 回复联系人姓名
     *
     * @return
     */
    private String replyName;

    /**
     * 服务承诺
     *
     * @return
     */
    private String serviceCommitment;

    private String followName;

    private Date followTime;

    /**
     * 是否有舆情风险
     */

    private Integer isOpinion;
    /**
     * 结案备注
     */
    private String closeCaseRemark;
    /**
     * 区域经理是否同意结案
     */
    private Integer isAgreeRegion;

    /**
     * 区域经理意见
     */
    private String regionComments;
    /**
     * 区域经理审核时间
     */
    private Date regionAuditTime;

    /**
     * 总部是否同意结案
     */
    private Integer isAgreeHeadquarters;

    /**
     * 总部意见
     */
    private String headquartersComments;

    /**
     * CCM系统协助处理
     */
    private String ccmSystemAssistedProcessing;
    /**
     * 提交重启时间
     */
    private Date restartReplyTime;

    /**
     * 案件重启后是否回访
     */
    private Integer isRestartRevisit;

    /**
     * 案件首次结案时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstCloseCaseTime;

    /**
     * 区域经理意见
     */
    private String regionalManagerComments;

    /**
     *是否匿名
     */
    private Integer isAnonymous;

    /**
     *区域经理是否满意结案（低满意度）
     */
    private Integer regionSatisfiedCase;

    /**
     *总部是否满意结案（低满意度）
     */
    private Integer HQSatisfiedCase;

    /**
     * 所属经销商code
     */
    @ApiModelProperty(value = "所属经销商code")
    private String ownedCompanyCode;

    /**
     * 所属经销商名称中文
     */
    @ApiModelProperty(value = "所属经销商名称中文")
    private String ownedCompanyNameCn;

    /**
     * 所属经销商简称中文
     */
    @ApiModelProperty(value = "所属经销商简称中文")
    private String ownedCompanyShortNameCn;

    /**
     * 所属经销商经营状态，字典1603
     */
    @ApiModelProperty(value = "所属经销商经营状态，字典1603")
    private Integer ownedCompanyStatus;

    /**
     * 所属经销商经营状态字典描述
     */
    @ApiModelProperty(value = "所属经销商经营状态字典描述")
    private String ownedCompanyStatusDesc;

    public String getOwnedCompanyStatusDesc() {

        return CompanyBusinessStatusEnum.getBusinessDesc(getOwnedCompanyStatus());
    }

    public void setOwnedCompanyStatusDesc(String ownedCompanyStatusDesc) {
        this.ownedCompanyStatusDesc = ownedCompanyStatusDesc;
    }

    public String getOwnedCompanyCode() {
        return ownedCompanyCode;
    }

    public void setOwnedCompanyCode(String ownedCompanyCode) {
        this.ownedCompanyCode = ownedCompanyCode;
    }

    public String getOwnedCompanyNameCn() {
        return ownedCompanyNameCn;
    }

    public void setOwnedCompanyNameCn(String ownedCompanyNameCn) {
        this.ownedCompanyNameCn = ownedCompanyNameCn;
    }

    public String getOwnedCompanyShortNameCn() {
        return ownedCompanyShortNameCn;
    }

    public void setOwnedCompanyShortNameCn(String ownedCompanyShortNameCn) {
        this.ownedCompanyShortNameCn = ownedCompanyShortNameCn;
    }

    public Integer getOwnedCompanyStatus() {
        return ownedCompanyStatus;
    }

    public void setOwnedCompanyStatus(Integer ownedCompanyStatus) {
        this.ownedCompanyStatus = ownedCompanyStatus;
    }

    /**
     *厂端描述
     */
    private String plantDescription;

    public String getPlantDescription() {
        return plantDescription;
    }

    public void setPlantDescription(String plantDescription) {
        this.plantDescription = plantDescription;
    }

    public Integer getRegionSatisfiedCase() {
        return regionSatisfiedCase;
    }

    public Integer getHQSatisfiedCase() {
        return HQSatisfiedCase;
    }

    public void setHQSatisfiedCase(Integer HQSatisfiedCase) {
        this.HQSatisfiedCase = HQSatisfiedCase;
    }

    public void setRegionSatisfiedCase(Integer regionSatisfiedCase) {
        this.regionSatisfiedCase = regionSatisfiedCase;
    }

    public Integer getIsAnonymous() {
        return isAnonymous;
    }

    public void setIsAnonymous(Integer isAnonymous) {
        this.isAnonymous = isAnonymous;
    }

    public String getLastFollowTime() {
        return lastFollowTime;
    }

    public void setLastFollowTime(String lastFollowTime) {
        this.lastFollowTime = lastFollowTime;
    }

    public String getIsFiveCloseCase() {
        return isFiveCloseCase;
    }

    public void setIsFiveCloseCase(String isFiveCloseCase) {
        this.isFiveCloseCase = isFiveCloseCase;
    }

    public String getIs24HourReply() {
        return is24HourReply;
    }

    public void setIs24HourReply(String is24HourReply) {
        this.is24HourReply = is24HourReply;
    }

    public String getIs48HourReply() {
        return is48HourReply;
    }

    public void setIs48HourReply(String is48HourReply) {
        this.is48HourReply = is48HourReply;
    }

    public String getRegionalManagerComments() {
        return regionalManagerComments;
    }

    public void setRegionalManagerComments(String regionalManagerComments) {
        this.regionalManagerComments = regionalManagerComments;
    }

    public Integer getIsRestartRevisit() {
        return isRestartRevisit;
    }

    public void setIsRestartRevisit(Integer isRestartRevisit) {
        this.isRestartRevisit = isRestartRevisit;
    }

    public Date getFirstCloseCaseTime() {
        return firstCloseCaseTime;
    }

    public void setFirstCloseCaseTime(Date firstCloseCaseTime) {
        this.firstCloseCaseTime = firstCloseCaseTime;
    }

    public String getBuyDealerCode() {
        return buyDealerCode;
    }

    public void setBuyDealerCode(String buyDealerCode) {
        this.buyDealerCode = buyDealerCode;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public Date getRestartReplyTime() {
        return restartReplyTime;
    }

    public Date getCloseCaseTime2() {
        return closeCaseTime2;
    }

    public void setCloseCaseTime2(Date closeCaseTime2) {
        this.closeCaseTime2 = closeCaseTime2;
    }

    public String[] getCloseCaseTime1() {
        return closeCaseTime1;
    }

    public void setCloseCaseTime1(String[] closeCaseTime1) {
        this.closeCaseTime1 = closeCaseTime1;
    }

    public void setRestartReplyTime(Date restartReplyTime) {
        this.restartReplyTime = restartReplyTime;
    }

    public String getCcmSystemAssistedProcessing() {
        return ccmSystemAssistedProcessing;
    }

    public void setCcmSystemAssistedProcessing(String ccmSystemAssistedProcessing) {
        this.ccmSystemAssistedProcessing = ccmSystemAssistedProcessing;
    }

    public Date getRegionAuditTime() {
        return regionAuditTime;
    }

    public void setRegionAuditTime(Date regionAuditTime) {
        this.regionAuditTime = regionAuditTime;
    }

    public Integer getIsAgreeHeadquarters() {
        return isAgreeHeadquarters;
    }

    public void setIsAgreeHeadquarters(Integer isAgreeHeadquarters) {
        this.isAgreeHeadquarters = isAgreeHeadquarters;
    }

    public String getHeadquartersComments() {
        return headquartersComments;
    }

    public void setHeadquartersComments(String headquartersComments) {
        this.headquartersComments = headquartersComments;
    }

    public Integer getIsAgreeRegion() {
        return isAgreeRegion;
    }

    public void setIsAgreeRegion(Integer isAgreeRegion) {
        this.isAgreeRegion = isAgreeRegion;
    }

    public String getRegionComments() {
        return regionComments;
    }

    public void setRegionComments(String regionComments) {
        this.regionComments = regionComments;
    }

    public Integer getIsOpinion() {
        return isOpinion;
    }

    public void setIsOpinion(Integer isOpinion) {
        this.isOpinion = isOpinion;
    }

    public String getCloseCaseRemark() {
        return closeCaseRemark;
    }

    public void setCloseCaseRemark(String closeCaseRemark) {
        this.closeCaseRemark = closeCaseRemark;
    }

    public Date getFollowTime() {
        return followTime;
    }

    public void setFollowTime(Date followTime) {
        this.followTime = followTime;
    }

    public String getFollowName() {
        return followName;
    }

    public void setFollowName(String followName) {
        this.followName = followName;
    }

    public Integer getWorkOrderNature() {
        return workOrderNature;
    }

    public void setWorkOrderNature(Integer workOrderNature) {
        this.workOrderNature = workOrderNature;
    }

    public Integer getWorkOrderClassification() {
        return workOrderClassification;
    }

    public void setWorkOrderClassification(Integer workOrderClassification) {
        this.workOrderClassification = workOrderClassification;
    }

    public String getReplyName() {
        return replyName;
    }

    public void setReplyName(String replyName) {
        this.replyName = replyName;
    }

    public String getServiceCommitment() {
        return serviceCommitment;
    }

    public void setServiceCommitment(String serviceCommitment) {
        this.serviceCommitment = serviceCommitment;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public Integer getIsSatisfied() {
        return isSatisfied;
    }

    public void setIsSatisfied(Integer isSatisfied) {
        this.isSatisfied = isSatisfied;
    }

    public String getHandleRegion() {
        return handleRegion;
    }

    public void setHandleRegion(String handleRegion) {
        this.handleRegion = handleRegion;
    }

    public List getFollowStatus1() {
        return followStatus1;
    }

    public void setFollowStatus1(List followStatus1) {
        this.followStatus1 = followStatus1;
    }

    public List getCcMainReason1() {
        return ccMainReason1;
    }

    public void setCcMainReason1(List ccMainReason1) {
        this.ccMainReason1 = ccMainReason1;
    }

    public List getCcResult1() {
        return ccResult1;
    }

    public void setCcResult1(List ccResult1) {
        this.ccResult1 = ccResult1;
    }

    public List getCcmPart1() {
        return ccmPart1;
    }

    public void setCcmPart1(List ccmPart1) {
        this.ccmPart1 = ccmPart1;
    }

    public List getCcmSubdivisionPart1() {
        return ccmSubdivisionPart1;
    }

    public void setCcmSubdivisionPart1(List ccmSubdivisionPart1) {
        this.ccmSubdivisionPart1 = ccmSubdivisionPart1;
    }

    public List getCategory11() {
        return category11;
    }

    public void setCategory11(List category11) {
        this.category11 = category11;
    }

    public List getCategory21() {
        return category21;
    }

    public void setCategory21(List category21) {
        this.category21 = category21;
    }

    public List getCategory31() {
        return category31;
    }

    public void setCategory31(List category31) {
        this.category31 = category31;
    }

    public List getClassification12() {
        return classification12;
    }

    public void setClassification12(List classification12) {
        this.classification12 = classification12;
    }

    public List getClassification22() {
        return classification22;
    }

    public void setClassification22(List classification22) {
        this.classification22 = classification22;
    }

    public List getSmallClass1() {
        return smallClass1;
    }

    public void setSmallClass1(List smallClass1) {
        this.smallClass1 = smallClass1;
    }

    public String getBlocId() {
        return blocId;
    }

    public void setBlocId(String blocId) {
        this.blocId = blocId;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionManagerId() {
        return regionManagerId;
    }

    public void setRegionManagerId(String regionManagerId) {
        this.regionManagerId = regionManagerId;
    }

    public String getClassification31() {
        return classification31;
    }

    public void setClassification31(String classification31) {
        this.classification31 = classification31;
    }

    public String getSql1() {
        return sql1;
    }

    public void setSql1(String sql1) {
        this.sql1 = sql1;
    }

    public String getAssitdep() {
        return assitdep;
    }

    public void setAssitdep(String assitdep) {
        this.assitdep = assitdep;
    }

    public Integer getClassification1() {
        return classification1;
    }

    public void setClassification1(Integer classification1) {
        this.classification1 = classification1;
    }

    public String getClassification2() {
        return classification2;
    }

    public void setClassification2(String classification2) {
        this.classification2 = classification2;
    }

    public String getClassification3() {
        return classification3;
    }

    public void setClassification3(String classification3) {
        this.classification3 = classification3;
    }

    public String getClassification4() {
        return classification4;
    }

    public void setClassification4(String classification4) {
        this.classification4 = classification4;
    }

    public List getDealerCode1() {
        return dealerCode1;
    }

    public void setDealerCode1(List dealerCode1) {
        this.dealerCode1 = dealerCode1;
    }

    public List getCcmManId1() {
        return ccmManId1;
    }

    public void setCcmManId1(List ccmManId1) {
        this.ccmManId1 = ccmManId1;
    }

    public List getRegion1() {
        return region1;
    }

    public void setRegion1(List region1) {
        this.region1 = region1;
    }

    public List getRegionManager1() {
        return regionManager1;
    }

    public void setRegionManager1(List regionManager1) {
        this.regionManager1 = regionManager1;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public List getAreaId1() {
        return areaId1;
    }

    public void setAreaId1(List areaId1) {
        this.areaId1 = areaId1;
    }

    public List getBloc1() {
        return bloc1;
    }

    public void setBloc1(List bloc1) {
        this.bloc1 = bloc1;
    }

    public Integer getQualityClassification1() {
        return qualityClassification1;
    }

    public void setQualityClassification1(Integer qualityClassification1) {
        this.qualityClassification1 = qualityClassification1;
    }

    public Integer getQualityClassification2() {
        return qualityClassification2;
    }

    public void setQualityClassification2(Integer qualityClassification2) {
        this.qualityClassification2 = qualityClassification2;
    }

    public String getQualityClassification3() {
        return qualityClassification3;
    }

    public void setQualityClassification3(String qualityClassification3) {
        this.qualityClassification3 = qualityClassification3;
    }

    public Integer getQualityClassification4() {
        return qualityClassification4;
    }

    public void setQualityClassification4(Integer qualityClassification4) {
        this.qualityClassification4 = qualityClassification4;
    }

    public String getFaultClassification() {
        return faultClassification;
    }

    public void setFaultClassification(String faultClassification) {
        this.faultClassification = faultClassification;
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1;
    }

    public String getRemark2() {
        return remark2;
    }

    public void setRemark2(String remark2) {
        this.remark2 = remark2;
    }

    public String getRemark3() {
        return remark3;
    }

    public void setRemark3(String remark3) {
        this.remark3 = remark3;
    }

    public String getRemark4() {
        return remark4;
    }

    public void setRemark4(String remark4) {
        this.remark4 = remark4;
    }

    public List getModel1() {
        return model1;
    }

    public void setModel1(List model1) {
        this.model1 = model1;
    }

    public String getBigClassName() {
        return bigClassName;
    }

    public void setBigClassName(String bigClassName) {
        this.bigClassName = bigClassName;
    }

    public String getSmallClassNameOther() {
        return smallClassNameOther;
    }

    public void setSmallClassNameOther(String smallClassNameOther) {
        this.smallClassNameOther = smallClassNameOther;
    }

    public String getBuyDealName() {
        return buyDealName;
    }

    public void setBuyDealName(String buyDealName) {
        this.buyDealName = buyDealName;
    }

    public String getBuyBloc() {
        return buyBloc;
    }

    public Integer getWorkOrderStatus() {
        return workOrderStatus;
    }

    public void setWorkOrderStatus(Integer workOrderStatus) {
        this.workOrderStatus = workOrderStatus;
    }

    public Integer getIsCloseCase() {
        return isCloseCase;
    }

    public void setIsCloseCase(Integer isCloseCase) {
        this.isCloseCase = isCloseCase;
    }

    public void setBuyBloc(String buyBloc) {
        this.buyBloc = buyBloc;
    }

    public String getBuyRegionManager() {
        return buyRegionManager;
    }

    public void setBuyRegionManager(String buyRegionManager) {
        this.buyRegionManager = buyRegionManager;
    }

    public String getHandleDealName() {
        return handleDealName;
    }

    public void setHandleDealName(String handleDealName) {
        this.handleDealName = handleDealName;
    }

    public String getHandleBloc() {
        return handleBloc;
    }

    public void setHandleBloc(String handleBloc) {
        this.handleBloc = handleBloc;
    }

    public String getHandleRegionManager() {
        return handleRegionManager;
    }

    public void setHandleRegionManager(String handleRegionManager) {
        this.handleRegionManager = handleRegionManager;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public String getSmallClass() {
        return smallClass;
    }

    public void setSmallClass(String smallClass) {
        this.smallClass = smallClass;
    }

    public String getClassification11() {
        return classification11;
    }

    public void setClassification11(String classification11) {
        this.classification11 = classification11;
    }

    public String getClassification21() {
        return classification21;
    }

    public void setClassification21(String classification21) {
        this.classification21 = classification21;
    }

    public String getCcResult() {
        return ccResult;
    }

    public void setCcResult(String ccResult) {
        this.ccResult = ccResult;
    }

    public String getCcmSubdivisionPart() {
        return ccmSubdivisionPart;
    }

    public void setCcmSubdivisionPart(String ccmSubdivisionPart) {
        this.ccmSubdivisionPart = ccmSubdivisionPart;
    }

    public String getCcmPart() {
        return ccmPart;
    }

    public void setCcmPart(String ccmPart) {
        this.ccmPart = ccmPart;
    }

    public String getCcMainReason() {
        return ccMainReason;
    }

    public void setCcMainReason(String ccMainReason) {
        this.ccMainReason = ccMainReason;
    }

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planFollowTime1;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planFollowTime2;

    public Date getPlanFollowTime1() {
        return planFollowTime1;
    }

    public void setPlanFollowTime1(Date planFollowTime1) {
        this.planFollowTime1 = planFollowTime1;
    }

    public Date getPlanFollowTime2() {
        return planFollowTime2;
    }

    public void setPlanFollowTime2(Date planFollowTime2) {
        this.planFollowTime2 = planFollowTime2;
    }

    public Date getFisrtRestartTime2() {
        return fisrtRestartTime2;
    }

    public void setFisrtRestartTime2(Date fisrtRestartTime2) {
        this.fisrtRestartTime2 = fisrtRestartTime2;
    }

    public Date getActuallFollowTime() {
        return actuallFollowTime;
    }

    public void setActuallFollowTime(Date actuallFollowTime) {
        this.actuallFollowTime = actuallFollowTime;
    }

    public Date getActuallFollowTime1() {
        return actuallFollowTime1;
    }

    public void setActuallFollowTime1(Date actuallFollowTime1) {
        this.actuallFollowTime1 = actuallFollowTime1;
    }

    public List getSource1() {
        return source1;
    }

    public void setSource1(List source1) {
        this.source1 = source1;
    }

    public Date getCallTime2() {
        return callTime2;
    }

    public void setCallTime2(Date callTime2) {
        this.callTime2 = callTime2;
    }

    public List getType1() {
        return type1;
    }

    public void setType1(List type1) {
        this.type1 = type1;
    }

    public String[] getActuallFollowTime2() {
        return actuallFollowTime2;
    }

    public void setActuallFollowTime2(String[] actuallFollowTime2) {
        this.actuallFollowTime2 = actuallFollowTime2;
    }

    public String[] getCallTime1() {
        return callTime1;
    }

    public void setCallTime1(String[] callTime1) {
        this.callTime1 = callTime1;
    }

    public String[] getFisrtRestartTime1() {
        return fisrtRestartTime1;
    }

    public void setFisrtRestartTime1(String[] fisrtRestartTime1) {
        this.fisrtRestartTime1 = fisrtRestartTime1;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Boolean getDealerIsRead() {
        return dealerIsRead;
    }

    public void setDealerIsRead(Boolean dealerIsRead) {
        this.dealerIsRead = dealerIsRead;
    }

    public Boolean getManagerIsRead() {
        return managerIsRead;
    }

    public void setManagerIsRead(Boolean managerIsRead) {
        this.managerIsRead = managerIsRead;
    }

    public Boolean getCcmIsRead() {
        return ccmIsRead;
    }

    public void setCcmIsRead(Boolean ccmIsRead) {
        this.ccmIsRead = ccmIsRead;
    }

    public Boolean getDeleted() {
        return isDeleted;
    }

    public void setDeleted(Boolean deleted) {
        isDeleted = deleted;
    }

    public Boolean getCcmNotPublish() {
        return ccmNotPublish;
    }

    public void setCcmNotPublish(Boolean ccmNotPublish) {
        this.ccmNotPublish = ccmNotPublish;
    }

    public String getFollowContent() {
        return followContent;
    }

    public void setFollowContent(String followContent) {
        this.followContent = followContent;
    }

    public String getCcmSubject() {
        return ccmSubject;
    }

    public void setCcmSubject(String ccmSubject) {
        this.ccmSubject = ccmSubject;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getAdvise() {
        return advise;
    }

    public void setAdvise(Integer advise) {
        this.advise = advise;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getClassification5() {
        return classification5;
    }

    public void setClassification5(String classification5) {
        this.classification5 = classification5;
    }

    public String getClassification6() {
        return classification6;
    }

    public void setClassification6(String classification6) {
        this.classification6 = classification6;
    }

    public Date getPlanFollowTime() {
        return planFollowTime;
    }

    public void setPlanFollowTime(Date planFollowTime) {
        this.planFollowTime = planFollowTime;
    }

    public String[] getPlanFollowTime5() {
        return planFollowTime5;
    }

    public void setPlanFollowTime5(String[] planFollowTime5) {
        this.planFollowTime5 = planFollowTime5;
    }

    public ComplaintInfMoreDTO() {
        super();
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getAppId() {
        return appId;
    }


    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }


    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerParCode() {
        return ownerParCode;
    }


    public void setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public Integer getOrgId() {
        return orgId;
    }


    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId() {
        return id;
    }

    public String getOwnerAddress() {
        return ownerAddress;
    }

    public void setOwnerAddress(String ownerAddress) {
        this.ownerAddress = ownerAddress;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getComplaintId() {
        return complaintId;
    }


    public void setComplaintId(String complaintId) {
        this.complaintId = complaintId;
    }

    public String getType() {
        return type;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getClassification() {
        return classification;
    }


    public void setClassification(Integer classification) {
        this.classification = classification;
    }

    public String getSource() {
        return source;
    }


    public void setSource(String source) {
        this.source = source;
    }


    public Date getCallTime() {
        return callTime;
    }


    public void setCallTime(Date callTime) {
        this.callTime = callTime;
    }

    public Date getFisrtRestartTime() {
        return fisrtRestartTime;
    }


    public void setFisrtRestartTime(Date fisrtRestartTime) {
        this.fisrtRestartTime = fisrtRestartTime;
    }

    public Date getNewestRestartTime() {
        return newestRestartTime;
    }

    public String getPart() {
        return part;
    }

    public void setPart(String part) {
        this.part = part;
    }

    public String getSubdivisionPart() {
        return subdivisionPart;
    }

    public void setSubdivisionPart(String subdivisionPart) {
        this.subdivisionPart = subdivisionPart;
    }

    public void setNewestRestartTime(Date newestRestartTime) {
        this.newestRestartTime = newestRestartTime;
    }

    public String getCallName() {
        return callName;
    }


    public void setCallName(String callName) {
        this.callName = callName;
    }

    public String getCallTel() {
        return callTel;
    }


    public void setCallTel(String callTel) {
        this.callTel = callTel;
    }

    public String getName() {
        return name;
    }


    public void setName(String name) {
        this.name = name;
    }

    public String getLicensePlateNum() {
        return licensePlateNum;
    }


    public void setLicensePlateNum(String licensePlateNum) {
        this.licensePlateNum = licensePlateNum;
    }

    public String getVin() {
        return vin;
    }


    public void setVin(String vin) {
        this.vin = vin;
    }

    public Date getBuyTime() {
        return buyTime;
    }


    public void setBuyTime(Date buyTime) {
        this.buyTime = buyTime;
    }

    public String getModel() {
        return model;
    }


    public void setModel(String model) {
        this.model = model;
    }

    public String getModelYear() {
        return modelYear;
    }


    public void setModelYear(String modelYear) {
        this.modelYear = modelYear;
    }

    public String getBuyDealerName() {
        return buyDealerName;
    }


    public void setBuyDealerName(String buyDealerName) {
        this.buyDealerName = buyDealerName;
    }

    public String getDealerName() {
        return dealerName;
    }


    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    public String getDealerCode() {
        return dealerCode;
    }


    public void setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
    }

    public Integer getMileage() {
        return mileage;
    }


    public void setMileage(Integer mileage) {
        this.mileage = mileage;
    }

    public String getReplyTel() {
        return replyTel;
    }


    public void setReplyTel(String replyTel) {
        this.replyTel = replyTel;
    }

    public String getReplyTel2() {
        return replyTel2;
    }


    public void setReplyTel2(String replyTel2) {
        this.replyTel2 = replyTel2;
    }

    public String getSubject() {
        return subject;
    }


    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getProblem() {
        return problem;
    }

    public Boolean getReport() {
        return isReport;
    }

    public void setReport(Boolean report) {
        isReport = report;
    }

    public void setProblem(String problem) {
        this.problem = problem;
    }

    public String getIllustrate() {
        return illustrate;
    }


    public void setIllustrate(String illustrate) {
        this.illustrate = illustrate;
    }

    public String getCategory1() {
        return category1;
    }


    public void setCategory1(String category1) {
        this.category1 = category1;
    }

    public String getCategory2() {
        return category2;
    }


    public void setCategory2(String category2) {
        this.category2 = category2;
    }

    public String getCategory3() {
        return category3;
    }


    public void setCategory3(String category3) {
        this.category3 = category3;
    }

    public String getCcPart() {
        return ccPart;
    }


    public void setCcPart(String ccPart) {
        this.ccPart = ccPart;
    }

    public String getCcSubdivisionPart() {
        return ccSubdivisionPart;
    }


    public void setCcSubdivisionPart(String ccSubdivisionPart) {
        this.ccSubdivisionPart = ccSubdivisionPart;
    }

    public String getCcProblem() {
        return ccProblem;
    }


    public void setCcProblem(String ccProblem) {
        this.ccProblem = ccProblem;
    }

    public String getCcRequirement() {
        return ccRequirement;
    }


    public void setCcRequirement(String ccRequirement) {
        this.ccRequirement = ccRequirement;
    }

    public String getDepartment() {
        return department;
    }


    public void setDepartment(String department) {
        this.department = department;
    }

    public String getReceptionist() {
        return receptionist;
    }


    public void setReceptionist(String receptionist) {
        this.receptionist = receptionist;
    }

    public Integer getImportanceLevel() {
        return importanceLevel;
    }


    public void setImportanceLevel(Integer importanceLevel) {
        this.importanceLevel = importanceLevel;
    }

    public Date getDealerFisrtReplyTime() {
        return dealerFisrtReplyTime;
    }


    public void setDealerFisrtReplyTime(Date dealerFisrtReplyTime) {
        this.dealerFisrtReplyTime = dealerFisrtReplyTime;
    }

    public Date getFisrtRestartDealerFisrtReplyTime() {
        return fisrtRestartDealerFisrtReplyTime;
    }


    public void setFisrtRestartDealerFisrtReplyTime(Date fisrtRestartDealerFisrtReplyTime) {
        this.fisrtRestartDealerFisrtReplyTime = fisrtRestartDealerFisrtReplyTime;
    }

    public Integer getIsRevisit() {
        return isRevisit;
    }


    public void setIsRevisit(Integer isRevisit) {
        this.isRevisit = isRevisit;
    }

    public Date getRevisitTime() {
        return revisitTime;
    }


    public void setRevisitTime(Date revisitTime) {
        this.revisitTime = revisitTime;
    }

    public String getRevisitResult() {
        return revisitResult;
    }


    public void setRevisitResult(String revisitResult) {
        this.revisitResult = revisitResult;
    }

    public String getRevisitContent() {
        return revisitContent;
    }


    public void setRevisitContent(String revisitContent) {
        this.revisitContent = revisitContent;
    }

    public Date getApplyTime() {
        return applyTime;
    }


    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public Integer getIsAgree() {
        return isAgree;
    }


    public void setIsAgree(Integer isAgree) {
        this.isAgree = isAgree;
    }

    public Date getSubmitTime() {
        return submitTime;
    }


    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public Date getCloseCaseTime() {
        return closeCaseTime;
    }


    public void setCloseCaseTime(Date closeCaseTime) {
        this.closeCaseTime = closeCaseTime;
    }

    public Date getRestartCloseCaseTime() {
        return restartCloseCaseTime;
    }


    public void setRestartCloseCaseTime(Date restartCloseCaseTime) {
        this.restartCloseCaseTime = restartCloseCaseTime;
    }

    public Integer getCloseCaseStatus() {
        return closeCaseStatus;
    }


    public void setCloseCaseStatus(Integer closeCaseStatus) {
        this.closeCaseStatus = closeCaseStatus;
    }

    public String getFollowStatus() {
        return followStatus;
    }


    public void setFollowStatus(String followStatus) {
        this.followStatus = followStatus;
    }

    public String getBasicReason() {
        return basicReason;
    }


    public void setBasicReason(String basicReason) {
        this.basicReason = basicReason;
    }

    public Integer getIsRepaired() {
        return isRepaired;
    }


    public void setIsRepaired(Integer isRepaired) {
        this.isRepaired = isRepaired;
    }

    public String getTechMaintainPlan() {
        return techMaintainPlan;
    }


    public void setTechMaintainPlan(String techMaintainPlan) {
        this.techMaintainPlan = techMaintainPlan;
    }

    public String getRapportPlan() {
        return rapportPlan;
    }


    public void setRapportPlan(String rapportPlan) {
        this.rapportPlan = rapportPlan;
    }

    public String getRisk() {
        return risk;
    }


    public void setRisk(String risk) {
        this.risk = risk;
    }

    public Boolean getIsDealerIsRead() {
        return dealerIsRead;
    }


    public void setIsDealerIsRead(Boolean dealerIsRead) {
        this.dealerIsRead = dealerIsRead;
    }

    public Boolean getIsManagerIsRead() {
        return managerIsRead;
    }


    public void setIsManagerIsRead(Boolean managerIsRead) {
        this.managerIsRead = managerIsRead;
    }

    public Boolean getIsCcmIsRead() {
        return ccmIsRead;
    }


    public void setIsCcmIsRead(Boolean ccmIsRead) {
        this.ccmIsRead = ccmIsRead;
    }

    public Integer getDataSources() {
        return dataSources;
    }


    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }


    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid() {
        return isValid;
    }


    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Date getCreatedAt() {
        return createdAt;
    }


    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }


    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getRegionManager() {
        return regionManager;
    }

    public void setRegionManager(String regionManager) {
        this.regionManager = regionManager;
    }

    public String getBloc() {
        return bloc;
    }

    public void setBloc(String bloc) {
        this.bloc = bloc;
    }

    public String getCcmMan() {
        return ccmMan;
    }

    public void setCcmMan(String ccmMan) {
        this.ccmMan = ccmMan;
    }

    public String getCcmManId() {
        return ccmManId;
    }

    public void setCcmManId(String ccmManId) {
        this.ccmManId = ccmManId;
    }

    public Date getFirstRestartCloseCaseTime(){ return firstRestartCloseCaseTime; }

    public void setFirstRestartCloseCaseTime(Date firstRestartCloseCaseTime){ this.firstRestartCloseCaseTime = firstRestartCloseCaseTime ;}

    @Override
    public String toString() {
        return "ComplaintInfoDTO{" +
                "appId=" + appId +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", complaintId=" + complaintId +
                ", type=" + type +
                ", classification=" + classification +
                ", source=" + source +
                ", callTime=" + callTime +
                ", fisrtRestartTime=" + fisrtRestartTime +
                ", newestRestartTime=" + newestRestartTime +
                ", callName=" + callName +
                ", callTel=" + callTel +
                ", name=" + name +
                ", licensePlateNum=" + licensePlateNum +
                ", vin=" + vin +
                ", buyTime=" + buyTime +
                ", model=" + model +
                ", modelYear=" + modelYear +
                ", buyDealerName=" + buyDealerName +
                ", dealerName=" + dealerName +
                ", dealerCode=" + dealerCode +
                ", mileage=" + mileage +
                ", replyTel=" + replyTel +
                ", replyTel2=" + replyTel2 +
                ", subject=" + subject +
                ", problem=" + problem +
                ", illustrate=" + illustrate +
                ", category1=" + category1 +
                ", category2=" + category2 +
                ", category3=" + category3 +
                ", ccPart=" + ccPart +
                ", ccSubdivisionPart=" + ccSubdivisionPart +
                ", ccProblem=" + ccProblem +
                ", ccRequirement=" + ccRequirement +
                ", department=" + department +
                ", receptionist=" + receptionist +
                ", importanceLevel=" + importanceLevel +
                ", dealerFisrtReplyTime=" + dealerFisrtReplyTime +
                ", fisrtRestartDealerFisrtReplyTime=" + fisrtRestartDealerFisrtReplyTime +
                ", isRevisit=" + isRevisit +
                ", revisitTime=" + revisitTime +
                ", revisitResult=" + revisitResult +
                ", revisitContent=" + revisitContent +
                ", applyTime=" + applyTime +
                ", isAgree=" + isAgree +
                ", submitTime=" + submitTime +
                ", closeCaseTime=" + closeCaseTime +
                ", restartCloseCaseTime=" + restartCloseCaseTime +
                ", closeCaseStatus=" + closeCaseStatus +
                ", followStatus=" + followStatus +
                ", basicReason=" + basicReason +
                ", isRepaired=" + isRepaired +
                ", techMaintainPlan=" + techMaintainPlan +
                ", rapportPlan=" + rapportPlan +
                ", risk=" + risk +
                ", dealerIsRead=" + dealerIsRead +
                ", managerIsRead=" + managerIsRead +
                ", ccmIsRead=" + ccmIsRead +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", plantDescription=" + plantDescription +
                "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }


    public String getInformTime() {
        return informTime;
    }

    public void setInformTime(String informTime) {
        this.informTime = informTime;
    }

    public String getFirstRestartTime() {
        return firstRestartTime;
    }

    public void setFirstRestartTime(String firstRestartTime) {
        this.firstRestartTime = firstRestartTime;
    }

	public Date getFirstCloseCaseTime2() {
		return firstCloseCaseTime2;
	}

	public void setFirstCloseCaseTime2(Date firstCloseCaseTime2) {
		this.firstCloseCaseTime2 = firstCloseCaseTime2;
	}

	public Date getFirstRestartCloseCaseTime2() {
		return firstRestartCloseCaseTime2;
	}

	public void setFirstRestartCloseCaseTime2(Date firstRestartCloseCaseTime2) {
		this.firstRestartCloseCaseTime2 = firstRestartCloseCaseTime2;
	}

	public String[] getFirstCloseCaseTime1() {
		return firstCloseCaseTime1;
	}

	public void setFirstCloseCaseTime1(String[] firstCloseCaseTime1) {
		this.firstCloseCaseTime1 = firstCloseCaseTime1;
	}

	public String[] getFirstRestartCloseCaseTime1() {
		return firstRestartCloseCaseTime1;
	}

	public void setFirstRestartCloseCaseTime1(String[] firstRestartCloseCaseTime1) {
		this.firstRestartCloseCaseTime1 = firstRestartCloseCaseTime1;
	}
}
