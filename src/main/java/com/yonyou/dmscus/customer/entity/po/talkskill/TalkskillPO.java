package com.yonyou.dmscus.customer.entity.po.talkskill;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import com.yonyou.dmscloud.framework.base.po.BasePO;

import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 话术
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-19
 */
@TableName("tt_talkskill")
@Data
public class TalkskillPO extends BasePO<TalkskillPO> {

    private static final long serialVersionUID=1L;
        
    /**
     * 主键ID
     */
    @TableId(value = "talk_id", type = IdType.AUTO)
    private Long talkId;

    /**
     * 厂端话术id
     */
    @TableField("vcdc_id")
    private  Long vcdcId ;
    /**
     * 经销商是否修改
     */
    @TableField("is_dealer_mod")
    private  Integer isDealerMod ;

    @TableField(exist = false)
    private Integer dataType;
    /**
     * 经销商编号
     */
    @TableField("dealer_code")
    private String dealerCode;
    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;
    /**
     * 组织id
     */
    @TableField("org_id")
    private Long orgId;
    /**
     * 话术标题
     */
    @TableField("title")
    private String title;
    
    /**
     * 业务分类
     */
    @TableField("type")
    private String type;
    
    /**
     * 有效开始日期
     */
    @TableField("begin_date")
    private LocalDate beginDate;
    
    /**
     * 有效结束日期
     */
    @TableField(value = "end_date",updateStrategy = FieldStrategy.IGNORED,jdbcType = JdbcType.DATE)
    private LocalDate endDate;
    
    /**
     * 一级标签-id
     */
    @TableField("tag1")
    private Integer tag1;

    /**
     * 一级标签--名称
     */
    @TableField(exist = false)
    private String tag1Name;

    /**
     * 二级标签-id
     */
    @TableField("tag2")
    private Integer tag2;
    /**
     * 二级标签-name
     */
    @TableField(exist = false)
    private String tag2Name;
    /**
     * 话术
     */
    @TableField("talkskill")
    private String talkskill;
    
    /**
     * 关键词1
     */
    @TableField("keyword1")
    private String keyword1;
    
    /**
     * 关键词2
     */
    @TableField("keyword2")
    private String keyword2;
    
    /**
     * 关键词3
     */
    @TableField("keyword3")
    private String keyword3;
    
    /**
     * 是否强制推送
     */
    @TableField("is_compel")
    private Integer isCompel;
    
    /**
     * 是否启用
     */
    @TableField("is_enable")
    private Integer isEnable;
    
    /**
     * 是否厂端
     */
    @TableField("is_vcdc")
    private Integer isVcdc;
    
    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;
    
    /**
     * 是否删除，1：删除，0：未删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;
    
    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;


    @TableField("created_at")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;

    
    public TalkskillPO(){
        super();
    }


    @Override
    protected Serializable pkVal(){
            return this.talkId;
        }


/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"talkId");
    }

}
