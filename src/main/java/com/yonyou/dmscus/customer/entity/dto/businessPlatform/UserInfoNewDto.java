package com.yonyou.dmscus.customer.entity.dto.businessPlatform;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title: UserInfoNew
 * @projectName server2
 * @description: TODO
 * @date 2022/1/2623:52
 */
@Data
public class UserInfoNewDto  implements Serializable {
    private static final long serialVersionUID = 1L;
    private String userId;
    private String employeeName;
    private String companyId;
    private String companyCode;
    private String ownerCode;
    private String dataType;
    private String orgId;
    private String orgIds;
    private String groupCode;
    private String userOrgId;
    private String loginWay;
    private List<RoleListDto> roleList;
}
