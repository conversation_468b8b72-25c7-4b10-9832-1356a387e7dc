package com.yonyou.dmscus.customer.entity.po.accidentClues;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 事故线索-扩展表2
 */
@ApiModel(description="事故线索-扩展表2")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@TableName(value = "tt_accident_clues_ext_2")
public class AccidentCluesExt2Po {
  /**
   * ID
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * tt_accident_clues
   */
  @TableField(value = "ac_id")
  private Integer acId;
  /**
   * 联系方式-手机号
   */
  @TableField(value = "contacts_information")
  private String contactsInformation;

  /**
   * CDP联系人名称
   */
  @TableField(value = "cdp_contacts")
  private String cdpContacts;

  /**
   * CDP联系人名称手机号
   */
  @TableField(value = "cdp_contacts_phone")
  private String cdpContactsPhone;

  /**
   * 工单联系人
   */
  @TableField(value = "order_contacts_info")
  private String orderContactsInfo;

  /**
   * 是否虚拟号码
   */
  @TableField(value = "virtual_phone_flag")
  private Integer virtualPhoneFlag;

  /**
   * 保司来源（来源渠道）
   */
  @TableField(value = "insurance_source")
  private String insuranceSource;
  /**
   * 设备id
   */
  @TableField(value = "device_id")
  private String deviceId;
  /**
   * DVR销售门店
   */
  @TableField(value = "dvr_store")
  private String dvrStore;
  /**
   * G-sensor曲线
   */
  @TableField(value = "gsensor")
  private String gsensor;


}