package com.yonyou.dmscus.customer.entity.po.faultLight;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tt_fault_call_details")
public class TtFaultCallDetailsPO {

    private static final long serialVersionUID = 8695577479726639593L;

    @TableId(type = IdType.AUTO)
    /**
     * 系统id
     */
    @ApiModelProperty("系统id")
    @TableField("app_id")
    private String appId;

    /**
     * 所有者代码
     */
    @ApiModelProperty("所有者代码")
    @TableField("owner_code")
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    @ApiModelProperty("所有者的父组织代码")
    @TableField("owner_par_code")
    private String ownerParCode;

    /**
     * 组织id
     */
    @ApiModelProperty("组织id")
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @TableField("id")
    private Long id;

    /**
     * 话单id
     */
    @ApiModelProperty("话单id")
    @TableField("session_id")
    private String sessionId;

    /**
     * call_id
     */
    @ApiModelProperty("call_id")
    @TableField("call_id")
    private String callId;

    /**
     * 显示号码
     */
    @ApiModelProperty("显示号码")
    @TableField("display_number")
    private String displayNumber;

    /**
     * 主叫号码
     */
    @ApiModelProperty("主叫号码")
    @TableField("caller_number")
    private String callerNumber;

    /**
     * 被叫号码
     */
    @ApiModelProperty("被叫号码")
    @TableField("callee_number")
    private String calleeNumber;

    /**
     * ai语音工作号
     */
    @ApiModelProperty("ai语音工作号")
    @TableField("work_number")
    private String workNumber;

    /**
     * 通话开始时间
     */
    @ApiModelProperty("通话开始时间")
    @TableField("start_time")
    private Date startTime;

    /**
     * 通话结束时间
     */
    @ApiModelProperty("通话结束时间")
    @TableField("end_time")
    private Date endTime;

    /**
     * 通话时长
     */
    @ApiModelProperty("通话时长")
    @TableField("call_length")
    private Integer callLength;

    /**
     * 呼叫类型
     */
    @ApiModelProperty("呼叫类型")
    @TableField("service_type")
    private String serviceType;

    /**
     * 总分
     */
    @ApiModelProperty("总分")
    @TableField("total_score")
    private Integer totalScore;

    /**
     * 数据来源
     */
    @ApiModelProperty("数据来源")
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    private int isDeleted;

    /**
     * 是否有效
     */
    @ApiModelProperty("是否有效")
    @TableField("is_valid")
    private Integer isValid;

    /**
     * 创建人id
     */
    @ApiModelProperty("创建人id")
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private Date createdAt;

    /**
     * 更新人id
     */
    @ApiModelProperty("更新人id")
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private Date updatedAt;

    /**
     * 版本号(乐观锁)
     */
    @ApiModelProperty("版本号(乐观锁)")
    @TableField("record_version")
    private Integer recordVersion;

    /**
     * 经销商代码
     */
    @ApiModelProperty("经销商代码")
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * etl需求，用于标记抽取数据，便于清除
     */
    @ApiModelProperty("etl需求，用于标记抽取数据，便于清除")
    @TableField("mark")
    private String mark;

    /**
     * 线索记录明细id
     */
    @ApiModelProperty("线索记录明细id")
    @TableField("detail_id")
    private Long detailId;

    /**
     * 类型（1001-邀约，1002-事故线索，1003-保险跟进）
     */
    @ApiModelProperty("类型（1001-邀约，1002-事故线索，1003-保险跟进）")
    @TableField("call_type")
    private Integer callType;
}
