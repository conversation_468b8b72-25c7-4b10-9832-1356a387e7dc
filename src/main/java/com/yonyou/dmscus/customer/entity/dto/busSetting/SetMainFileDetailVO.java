package com.yonyou.dmscus.customer.entity.dto.busSetting;


import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "套餐明细信息")
public class SetMainFileDetailVO {


    /**
     * 明细代码
     */
    @ApiModelProperty(value = "明细名称")
    private String code;

    /**
     * 明细名称
     */
    @ApiModelProperty(value = "明细名称")
    private String name;


    /**
     * 工时/数量
     */
    @TableField("labor_hour")
    private BigDecimal quantity;
}
