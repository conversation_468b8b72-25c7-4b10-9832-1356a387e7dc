package com.yonyou.dmscus.customer.entity.dto.inviteInsurance;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscus.customer.entity.dto.busSetting.SetMainFileVO;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 车辆邀约续保记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */

@ApiModel("车辆邀约续保记录表")
@Data
public class InviteInsuranceVehicleRecordDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 系统ID
     */
    @ApiModelProperty(value = "系统ID",name = "appId")
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 邀约ID
     */
    private Long id;

    /**
     * 邀约父类ID
     */
    private Long parentId;

    /**
     * 是否主要线索:1主要线索、0附属线索
     */
    @ApiModelProperty(value = "是否主要线索:1主要线索、0附属线索",name = "isMain")
    private Integer isMain;

    /**
     * 邀约来源类型：1VCDC下发邀约、2经销商自建邀约、3VOC事故邀约
     */
    @ApiModelProperty(value = "邀约来源类型：1VCDC下发邀约、2经销商自建邀约、3VOC事故邀约",name = "sourceType")
    private Integer sourceType;

    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号",name = "vin")
    private String vin;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号",name = "licensePlateNum")
    private String licensePlateNum;

    /**
     * 车主姓名
     */
    private String name;

    /**
     * 车主电话
     */
    private String tel;

    /**
     * 邀约类型
     */
    private Integer inviteType;

    /**
     * 建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "续保到期日期",name = "adviseInDate")
    private Date adviseInDate;

    /**
     * 新建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date newAdviseInDate;

    /**
     * 最新建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date newestAdviseInDate;

    /**
     * 计划跟进日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planFollowDate;

    /**
     * 实际跟进日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualFollowDate;

    /**
     * 计划提醒日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planRemindDate;

    /**
     * 实际提醒日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualRemindDate;


    /**
     * 首次跟进时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date firstFollowDate;


    /**
     * 线索完成时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderFinishDate;

    /**
     * 续保客户类型(81761001 新保客户  81761002 新转续  81761003  续转续   81761004 在修不在保)
     */
    private Integer insuranceType;

    /**
     *保险投保单(tt_insurance_bill)表主键id'
     */
    private Long insuranceBillId;

    /**
     *线索类型：1:交强险   2:商业险'
     */
    @ApiModelProperty(value = "线索类型：1:交强险   2:商业险",name = "clueType")
    private Integer clueType;

    /**
     *原线索记录新生的投保单号'
     */
    private String newInsureNo;

    /**
     * 跟进服务顾问ID
     */
    private String saId;

    /**
     * 跟进服务顾问姓名
     */
    private String saName;

    /**
     * 上次跟进服务顾问ID
     */
    private String lastSaId;

    /**
     * 上次跟进服务顾问姓名
     */
    private String lastSaName;

    /**
     * 跟进状态
     */
    private Integer followStatus;

    /**
     * 是否预约单：1 是，0 否
     */
    private Integer isBook;

    /**
     * 预约单号
     */
    private String bookNo;

    /**
     * 线索完成状态
     */
    private Integer orderStatus;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 经销商代码
     */
    private String dealerCode;


    /**
     * 邀约类型 查询条件
     */
    private List<Integer> inviteTypeParam;


    /**
     * 计划跟进日期开始 查询条件
     */
    private String planFollowDateStart;

    /**
     * 计划跟进日期结束 查询条件
     */
    private String planFollowDateEnd;

    /**
     * 实际跟进日期开始 查询条件
     */
    private String actualFollowDateStart;

    /**
     * 实际跟进日期结束 查询条件
     */
    private String actualFollowDateEnd;

    /**
     * 建议进厂日期开始 查询条件
     */
    private String adviseInDateStart;

    /**
     * 建议进厂日期结束 查询条件
     */
    private String adviseInDateEnd;


    /**
     * 邀约状态 查询条件
     */
    private List<Integer> followStatusParam;


    /**
     * 离职用户 id 查询条件
     */
    private List<Integer> leaveIds;


    /**
     * 邀约创建日期开始 查询条件
     */
    private String createdAtStart;

    /**
     * 邀约创建日期结束 查询条件
     */
    private String createdAtEnd;


    /**
     * 是否逾期未跟进：1 是，0 否 查询条件
     */
    private Integer overdue;


    /**
     * 大区 查询条件
     */
    private String largeAreaId;

    /**
     * 小区 查询条件
     */
    private String areaId;


    /**
     * 是否查询本人
     */
    private Integer isself;


    /**
     * 是否查询未分配
     */
    private  Boolean isNoDistribute;

    /**
     * 是否查询待分配
     */
    private  Boolean isWaitDistribute;


    /**
     * 易损件规则id
     */
    private Long partItemRuleId;

    /**
     * 易损件code
     */
    private String itemCode;

    /**
     * 易损件名称
     */
    private String itemName;

    /**
     * 易损件类型
     */
    private Integer itemType;


    /**
     * 易损件上次更换时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date lastChangeDate;


    /**
     * 是否voc车辆
     */
    private  Integer isVoc;


    /**
     * 上次保养日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private  Date lastMaintenanceDate;


    /**
     * 建议入厂日期间隔
     */
    private Integer dateInterval;


    /**
     * 子线索
     */
    private String sonInviteType;


    /**
     * 话术
     */
    private List<TalkskillDTO> talkskill;

    private List<InviteInsuranceVehicleRecordDetailDTO> recordDetailList;


    /**
     * 推荐保养套餐
     */
    private List<SetMainFileVO> maintainList;

    /**
     * 客户是否投保成功
     */
    private Integer isInsureSuccess;


    /**
     * 最新得分
     */
    private Integer score;

    /**
     * 录音播放地址
     */
    private String voiceUrl;


    /**
     * 最近通话时长
     */
    private Integer callLength;


    /**
     *    通话时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date callTime;



    /**
     * 日平均行驶里程
     */
    private BigDecimal dailyAverageMileage;


    /**
     *    通话详情Id
     */
    private Long callDetailId;

    /**
     *建议入厂里程
     */
    private Integer adviseInMileage;


    /**
     *任务基准日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date inviteTime;

    /**
     * 延保提示信息
     */
    private String extensionInsurance;

    /**
     *投保单状态
     */
    private Integer insuranceStatus;

    /**
     *是否联保
     */
    private Integer isJointGuarantee;

    /**
     *跟进次数
     */
    private Integer followTotal;


    /**
     * 客户唯一id
     */
    private Long oneId;

    /**
     *    超时关闭间隔
     */
    private Integer closeInterval;


    /**
     * 最新失败原因
     */
    private Integer loseReason;


    /**
     * 最新跟进内容
     */
    private String content;

    private int currentPage;

    private int pageSize;

    /**
     * 保险公司
     */
    private String insuranceName;

    private Integer adviseDateInterval;

    private Integer planDateInterval;

    private Long insuranceId;

    /**
     * 厂家保险到期日期	日历选择
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String factoryInsuranceExpiryDate;


    /**
     * 建议关怀日期	日历选择
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String suggestedCareDate;

    /**
     * 线索数据源	下拉选择
     */
    private Integer clueDataSource;

    /**
     * 线索下发类型	下拉选择
     */
    private Integer clueIssuanceType;

    /**
     * 是否有自店线索升级	下拉选择
     */
    private Integer hasInstoreUpgrade;

    /**
     * 是否有自店修改保险到期日期	下拉选择
     */
    private Integer hasInstoreModified;

    private String factoryInsuranceExpiryDateStart;

    private String factoryInsuranceExpiryDateEnd;

    private String suggestedCareDateStart;

    private String suggestedCareDateEnd;

    private Date advanceIssuanceDate;

    private Date clueIssuanceDate;

    private Date instoreUpgradeTime;

    private Integer clueGenerator;

    private Date instoreModificationTime;

    private String insuranceNo;

    private Date policyCreationDate;

    private Integer policySource;

    private Date policyEffectiveDate;

    private Date policyExpirationDate;

    private Integer ownerType;

    private String newInsuranceNo;

    private String newInsuranceName;

    private Date newPolicyCreationDate;

    private Integer newPolicySource;

    private Date  newPolicyEffectiveDate;

    private String  commercialInsuranceType;

    private String insuranceNames;

    private String newCommercialInsuranceType;

    @ApiModelProperty(value = "续保客户类型",name = "insuranceTypeList",hidden = true)
    private  List<Integer> insuranceTypeList;



    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
