package com.yonyou.dmscus.customer.entity.dto.complaint;

import com.yonyou.dmscloud.framework.service.excel.ExcelColumnDefine;
import com.yonyou.dmscloud.function.domains.dto.DataImportDto;
import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 */
@ApiModel(value = "Excel导入实体")
public class CcmImportDTO extends DataImportDto {
    /**
     * 行数
     */
    @ExcelColumnDefine(value = 1)

    private Integer lineNumber;

    /**
     * 经销商代码
     */
    @ExcelColumnDefine(value = 2)
    private String dealerCode;

    /**
     * CCM中文姓名
     */
    @ExcelColumnDefine(value = 3)
    private String ccmMan;

    public String getDealerCode() {
        return dealerCode;
    }

    public void setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
    }

    public String getCcmMan() {
        return ccmMan;
    }

    public void setCcmMan(String ccmMan) {
        this.ccmMan = ccmMan;
    }

    public Integer getLineNumber() {
        return lineNumber;
    }

    public void setLineNumber(Integer lineNumber) {
        this.lineNumber = lineNumber;
    }

    @Override
    public String toString() {
        return "CcmImportDTO{" +
                "lineNumber=" + lineNumber +
                ", dealerCode='" + dealerCode + '\'' +
                ", ccmMan='" + ccmMan + '\'' +
                '}';
    }
}
