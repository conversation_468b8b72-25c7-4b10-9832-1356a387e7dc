package com.yonyou.dmscus.customer.entity.dto.faultLight;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SourceClueIdRelationDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 线索表主键ID
     */
    private Long cluePrimaryId;

    /**
     * 胡仓线索ID
     */
    private Long sourceClueId;
}
