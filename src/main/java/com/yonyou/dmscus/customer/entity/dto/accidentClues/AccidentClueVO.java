package com.yonyou.dmscus.customer.entity.dto.accidentClues;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/24 15:55
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AccidentClueVO extends BaseDTO {

    @ApiModelProperty(value = "id",name="acId")
    private Long acId;

    @ApiModelProperty(value = "经销商license代码",name="dealerCode")
    private String dealerCode;

    @ApiModelProperty(value = "车牌号",name="license")
    private String license;

    @ApiModelProperty(value = "车型code",name="modelsId")
    private String modelsId;

    @ApiModelProperty(value = "保险公司id",name="insuranceCompanyId")
    private String insuranceCompanyId;

    @ApiModelProperty(value = "保险公司id",name="insuranceCompanyName")
    private String  insuranceCompanyName;

    @ApiModelProperty(value = "线索来源",name="cluesResource")
    private Integer cluesResource;

    @ApiModelProperty(value = "联系人",name="contacts")
    private String contacts;

    @ApiModelProperty(value = "联系人电话",name="contactsPhone")
    private String contactsPhone;

    @ApiModelProperty(value = "报案时间",name="reportDate")
    private String reportDate;

    @ApiModelProperty(value = "事故地点",name="accidentAddress")
    private String accidentAddress;

    @ApiModelProperty(value = "事故类型",name="accidentType")
    private Integer accidentType;

    @ApiModelProperty(value = "是否有人受伤",name="isBruise")
    private Integer isBruise;

    @ApiModelProperty(value = "外拓费用",name="outsideAmount")
    private BigDecimal outsideAmount;

    @ApiModelProperty(value = "跟进人员id",name="followPeople")
    private Integer followPeople;

    @ApiModelProperty(value = "跟进人员名称",name="followPeopleName")
    private String followPeopleName;

    @ApiModelProperty(value = "备注",name="remark")
    private String remark;

    @ApiModelProperty(value = "是否本店承保",name="isInsured")
    private Integer isInsured;

    @ApiModelProperty(value = "事故责任",name="accidentDuty")
    private Integer accidentDuty;

    @ApiModelProperty(value = "跟进状态",name="followStatus")
    private Integer followStatus;

    @ApiModelProperty(value = "线索状态",name="cluesStatus")
    private Integer cluesStatus;

    @ApiModelProperty(value = "下次跟进时间",name="nextFollowDate")
    private String nextFollowDate;

    @ApiModelProperty(value = "是否预约",name="isAppointment")
    private Integer isAppointment;

    @ApiModelProperty(value = "跟进次数",name="followCount")
    private Integer followCount;

    @ApiModelProperty(value = "线索类型",name="cluesType")
    private String cluesType;

    @ApiModelProperty(value = "报案时间开始",name="reportDateStart")
    private String reportDateStart;

    @ApiModelProperty(value = "报案时间结束",name="reportDateEnd")
    private String reportDateEnd;

    @ApiModelProperty(value = "下次跟进时间开始",name="nextFollowDateStart")
    private String nextFollowDateStart;

    @ApiModelProperty(value = "下次跟进时间结束",name="nextFollowDateEnd")
    private String nextFollowDateEnd;

    @ApiModelProperty(value = "单/双方事故",name="doubleAccident")
    private Integer doubleAccident;

    @ApiModelProperty(value = "分配状态",name="allotStatus")
    private Integer allotStatus;

    @ApiModelProperty(value = "车架号")
    private String vin;

    @ApiModelProperty(value = "跟进人员列表")
    private List<Long> followIds;

    @ApiModelProperty(value = "线索创建时间开始",name="createdDateStart")
    private String createdDateStart;

    @ApiModelProperty(value = "线索创建时间结束",name="createdDateEnd")
    private String createdDateEnd;

    @ApiModelProperty(value = "主界面查询参数",name="subParam")
    private String subParam;

    private Integer pageNum;

    private Integer limit;

    @ApiModelProperty(value = "联系人列表")
    private List<AccidentClueContact> contactList;

    @ApiModelProperty(value = "跟进状态")
    private List<Integer> followStatusList;

    @ApiModelProperty(value = "可以查询线索相关的用户(权限控制)")
    private List<Long> userList;

    @ApiModelProperty(value = "工单状态")
    private List<Integer> cluesStatusList;

    @ApiModelProperty(value = "保险公司")
    private List<String> insuranceCodeList;


    @ApiModelProperty(value = "线索ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long crmId;
    @ApiModelProperty(value = "报案号")
    private String registNo;
    @ApiModelProperty("是否重复")
    private Integer repeatLead;
    @ApiModelProperty(value = "出险时间")
    private String accidentDate;
    @ApiModelProperty(value = "线索推送时间")
    private String createdAt;
    @ApiModelProperty(value = "出险内容")
    private String accidentReason;
    @ApiModelProperty(value = "客户类型")
    private String contactsName;
    @ApiModelProperty(value = "进线类型")
    private String callType;
    @ApiModelProperty(value = "是否报警")
    private String callPoliceFlag;
    @ApiModelProperty(value = "创建渠道,YB，400，Newbie")
    private String sourceChannel;
    @ApiModelProperty(value = "车型名称")
    private String modelName;
    @ApiModelProperty(value = "来源渠道")
    private String insuranceSource;
    @ApiModelProperty(value = "来源渠道", hidden = true)
    private List<String> insuranceSourceList;
}
