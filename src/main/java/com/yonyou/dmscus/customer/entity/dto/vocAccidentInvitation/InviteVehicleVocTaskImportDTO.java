package com.yonyou.dmscus.customer.entity.dto.vocAccidentInvitation;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.service.excel.ExcelColumnDefine;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.function.domains.dto.DataImportDto;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


@ApiModel(value = "Excel导入实体")
@Data
public class InviteVehicleVocTaskImportDTO extends DataImportDto {


    /**
     * 车架号
     */
    @ExcelColumnDefine(value = 1)
    private String vin;
    /**
     * 车牌号
     */
    @ExcelColumnDefine(value = 2)
    private String licensePlateNum;

    /**
     * 进销商代码
     */
    @ExcelColumnDefine(value = 3)
    private String dealerCode;

    /**
     * VOC事故车联系经销商时间
     */
    @ExcelColumnDefine(value = 4)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date contactDate;

    /**
     * 客户姓名
     */
    @ExcelColumnDefine(value = 5)
    private String name;

    /**
     * 电话
     */
    @ExcelColumnDefine(value = 6)
    private String tel;
    /**
     * 与客户通话情况： 接通、未接通
     */
    @ExcelColumnDefine(value = 7)
    private String contactSituation;

    /**
     * VOC事故号
     */
    @ExcelColumnDefine(value = 8)
    private String accidentNo;

    /**
     * VOC事故说明
     */
    @ExcelColumnDefine(value = 9)
    private String accidentDetail;

    /**
     * 备注
     */
    @ExcelColumnDefine(value = 10)
    private String remark;

}
