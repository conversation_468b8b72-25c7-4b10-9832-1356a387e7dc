package com.yonyou.dmscus.customer.entity.dto.businessPlatform;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import java.io.Serializable;

/**
 * <p>
 * 经销商信息（中台）
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */

public class CompanyInfoDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 地址
     */
    private String addressEn;

    /**
     * 城市
     */
    private Integer cityId;


    /**
     * 经销商代码
     */
    private String companyCode;

    /**
     * 经销商名称中文
     */
    private String companyNameCn;

    /**
     * 区域
     */
    private Integer countyId;

    /**
     * 经销商规模
     */
    private Integer dealerScale;

    /**
     * 公司类型
     */
    private Integer dealerType;

    /**
     * 距离
     */
    private Double distance;


    /**
     * 传真
     */
    private String fax;
    /**
     * 集团
     */
    private String groupCompanyId;
    /**
     * 经销商代码id
     */
    private Integer id;
    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 经度
     */
    private Double longitude;

    /**
     * 电话
     */
    private String phone;

    /**
     * 省份
     */
    private Integer provinceId;

    /**
     * 备注
     */
    private String remark;


    /**
     * 状态
     */
    private Integer status;



    public CompanyInfoDTO() {
        super();
    }


    public String getAddressEn() {
        return addressEn;
    }

    public void setAddressEn(String addressEn) {
        this.addressEn = addressEn;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyNameCn() {
        return companyNameCn;
    }

    public void setCompanyNameCn(String companyNameCn) {
        this.companyNameCn = companyNameCn;
    }

    public Integer getCountyId() {
        return countyId;
    }

    public void setCountyId(Integer countyId) {
        this.countyId = countyId;
    }

    public Integer getDealerScale() {
        return dealerScale;
    }

    public void setDealerScale(Integer dealerScale) {
        this.dealerScale = dealerScale;
    }

    public Integer getDealerType() {
        return dealerType;
    }

    public void setDealerType(Integer dealerType) {
        this.dealerType = dealerType;
    }

    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getGroupCompanyId() {
        return groupCompanyId;
    }

    public void setGroupCompanyId(String groupCompanyId) {
        this.groupCompanyId = groupCompanyId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Integer provinceId) {
        this.provinceId = provinceId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
