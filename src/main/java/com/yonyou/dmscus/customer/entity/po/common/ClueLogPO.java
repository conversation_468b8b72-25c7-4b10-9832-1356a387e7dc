package com.yonyou.dmscus.customer.entity.po.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * description 线索下发log
 *
 * <AUTHOR>
 * @date 2023/10/20 16:03
 */
@Data
@TableName("tt_clue_log_data")
public class ClueLogPO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * crm团推线索id
     */
    @TableField("icm_id")
    private Long icmId;

    /**
     * 参数
     */
    @TableField(value = "request_data")
    private String requestData;


}
