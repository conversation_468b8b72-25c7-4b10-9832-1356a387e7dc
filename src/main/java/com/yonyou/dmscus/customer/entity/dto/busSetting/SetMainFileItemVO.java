package com.yonyou.dmscus.customer.entity.dto.busSetting;

import java.math.BigDecimal;

/**
 * <p>
 * 套餐主档-维修项目
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
    
public class SetMainFileItemVO  {

        
    /**
     * 系统ID
     */
                private String appId;
                
    /**
     * 所有者代码 
     */
                private String ownerCode;
                
    /**
     * 所有者的父组织代码 
     */
                private String ownerParCode;
                
    /**
     * 组织ID
     */
                private Integer orgId;
                
    /**
     * 主键ID
     */
                private Long id;
                
    /**
     * 套餐主档主键ID
     */
                private Long setId;
                
    /**
     * 项目代码
     */
                private String opCode;
                
    /**
     * 项目名称
     */
                private String opName;
                
    /**
     * 工时
     */
                private BigDecimal laborHour;
                
    /**
     * 帐类
     */
                private String type;
                
    /**
     * 帐类折扣
     */
                private BigDecimal discount;
                
    /**
     * 数据来源 
     */
                private Integer dataSources;
                
    /**
     * 是否删除
     */
                private Boolean isDeleted;
                
    /**
     * 是否有效 
     */
                private Integer isValid;
                

            
    public SetMainFileItemVO() {
        super();
    }

                                
    public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
            }
                                
    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
            }
                                
    public String getOwnerParCode(){
        return ownerParCode;
    }


    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
            }
                                
    public Integer getOrgId(){
        return orgId;
    }


    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
            }
                                            
    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
            }
                                
    public Long getSetId(){
        return setId;
    }


    public void  setSetId(Long setId) {
        this.setId = setId;
            }
                                
    public String getOpCode(){
        return opCode;
    }


    public void  setOpCode(String opCode) {
        this.opCode = opCode;
            }
                                
    public String getOpName(){
        return opName;
    }


    public void  setOpName(String opName) {
        this.opName = opName;
            }
                                
    public BigDecimal getLaborHour(){
        return laborHour;
    }


    public void  setLaborHour(BigDecimal laborHour) {
        this.laborHour = laborHour;
            }
                                
    public String getType(){
        return type;
    }


    public void  setType(String type) {
        this.type = type;
            }
                                
    public BigDecimal getDiscount(){
        return discount;
    }


    public void  setDiscount(BigDecimal discount) {
        this.discount = discount;
            }
                                
    public Integer getDataSources(){
        return dataSources;
    }


    public void  setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
            }
                                
    public Integer getIsValid(){
        return isValid;
    }


    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
            }
                                

    



}
