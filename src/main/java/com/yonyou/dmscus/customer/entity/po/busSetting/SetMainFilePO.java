package com.yonyou.dmscus.customer.entity.po.busSetting;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 套餐主档
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
@TableName("tt_set_main_file")
public class SetMainFilePO extends BasePO<SetMainFilePO> {

    private static final long serialVersionUID=1L;
    
    /**
     * 系统ID
     */
    @TableField("app_id")
        private String appId;
    
    /**
     * 所有者代码 
     */
    @TableField("owner_code")
        private String ownerCode;
    
    /**
     * 所有者的父组织代码 
     */
    @TableField("owner_par_code")
        private String ownerParCode;
    
    /**
     * 组织ID
     */
    @TableField("org_id")
        private Integer orgId;
        
    /**
     * 主键ID
     */
            @TableId(value = "id", type = IdType.AUTO)
                        private Long id;
    
    /**
     * 套餐类别
     */
    @TableField("set_category")
        private Integer setCategory;
    
    /**
     * 特约店代码  厂端 VCDC
     */
    @TableField("dealer_code")
        private String dealerCode;
    
    /**
     * 套餐类型
     */
    @TableField("set_type")
        private Integer setType;
    
    /**
     * 套餐名称
     */
    @TableField("set_name")
        private String setName;
    
    /**
     * 套餐编码
     */
    @TableField("set_code")
        private String setCode;


    /**
     * 套餐帐类
     */
    @TableField(value = "account_group",updateStrategy= FieldStrategy.IGNORED)
    private String accountGroup;

    public String getAccountGroup() {
        return accountGroup;
    }

    public void setAccountGroup(String accountGroup) {
        this.accountGroup = accountGroup;
    }

    /**
     * 车辆用途
     */
    @TableField(value = "vehicle_purpose",updateStrategy= FieldStrategy.IGNORED)
        private Integer vehiclePurpose;
    
    /**
     * 套餐说明
     */
    @TableField(value = "set_explain",updateStrategy= FieldStrategy.IGNORED)
        private String setExplain;
    
    /**
     * 启用日期
     */
    @TableField(value = "enable_date",updateStrategy= FieldStrategy.IGNORED)
        private Date enableDate;
    
    /**
     * 停用日期
     */
    @TableField("discontinue_date")
        private Date discontinueDate;
    
    /**
     * 车型
     */
    @TableField("model_name")
        private String modelName;
    
    /**
     * 车型代码
     */
    @TableField("model_code")
        private String modelCode;
    
    /**
     * 发动机代码,多个值用中文逗号分隔
     */
    @TableField("engine_code")
        private String engineCode;
    
    /**
     * 是否车型+发动机组合:1 是 0 否
     */
    @TableField("is_combine")
        private Integer isCombine;
    
    /**
     * 适用工单类型
     */
    @TableField("order_type")
        private Integer orderType;
    
    /**
     * 套餐折扣
     */
    @TableField("set_discount")
        private BigDecimal setDiscount;
    
    /**
     * 是否内结套餐
     */
    @TableField("is_internal_settlement")
        private Integer isInternalSettlement;
    
    /**
     * 是否可升级套餐
     */
    @TableField("is_upgrade")
        private Integer isUpgrade;
    
    /**
     * 是否重复项目
     */
    @TableField("is_duplicate_item")
        private Integer isDuplicateItem;
    
    /**
     * 套餐适用
     */
    @TableField("set_apply")
        private String setApply;

    /**
     * 数据来源 
     */
    @TableField("data_sources")
        private Integer dataSources;
    
    /**
     * 是否删除
     */
    @TableField("is_deleted")
        private Boolean isDeleted;
    
    /**
     * 是否有效  是否启用
     */
    @TableField("is_valid")
        private Integer isValid;
    
    /**
     * 创建时间
     */
    @TableField("created_at")
        private Date createdAt;
    
    /**
     * 更新时间
     */
    @TableField("updated_at")
        private Date updatedAt;

    public SetMainFilePO(){
        super();
    }

                    
    public String getAppId(){
        return appId;
    }

        public void setAppId(String appId) {
            this.appId = appId;
            }
                    
    public String getOwnerCode(){
        return ownerCode;
    }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
            }
                    
    public String getOwnerParCode(){
        return ownerParCode;
    }

        public void setOwnerParCode(String ownerParCode) {
            this.ownerParCode = ownerParCode;
            }
                    
    public Integer getOrgId(){
        return orgId;
    }

        public void setOrgId(Integer orgId) {
            this.orgId = orgId;
            }
                    
    public Long getId(){
        return id;
    }

        public void setId(Long id) {
            this.id = id;
            }
                    
    public Integer getSetCategory(){
        return setCategory;
    }

        public void setSetCategory(Integer setCategory) {
            this.setCategory = setCategory;
            }
                    
    public String getDealerCode(){
        return dealerCode;
    }

        public void setDealerCode(String dealerCode) {
            this.dealerCode = dealerCode;
            }
                    
    public Integer getSetType(){
        return setType;
    }

        public void setSetType(Integer setType) {
            this.setType = setType;
            }
                    
    public String getSetName(){
        return setName;
    }

        public void setSetName(String setName) {
            this.setName = setName;
            }
                    
    public String getSetCode(){
        return setCode;
    }

        public void setSetCode(String setCode) {
            this.setCode = setCode;
            }
                    
    public Integer getVehiclePurpose(){
        return vehiclePurpose;
    }

        public void setVehiclePurpose(Integer vehiclePurpose) {
            this.vehiclePurpose = vehiclePurpose;
            }
                    
    public String getSetExplain(){
        return setExplain;
    }

        public void setSetExplain(String setExplain) {
            this.setExplain = setExplain;
            }
                    
    public Date getEnableDate(){
        return enableDate;
    }

        public void setEnableDate(Date enableDate) {
            this.enableDate = enableDate;
            }
                    
    public Date getDiscontinueDate(){
        return discontinueDate;
    }

        public void setDiscontinueDate(Date discontinueDate) {
            this.discontinueDate = discontinueDate;
            }
                    
    public String getModelName(){
        return modelName;
    }

        public void setModelName(String modelName) {
            this.modelName = modelName;
            }
                    
    public String getModelCode(){
        return modelCode;
    }

        public void setModelCode(String modelCode) {
            this.modelCode = modelCode;
            }
                    
    public String getEngineCode(){
        return engineCode;
    }

        public void setEngineCode(String engineCode) {
            this.engineCode = engineCode;
            }
                    
    public Integer getIsCombine(){
        return isCombine;
    }

        public void setIsCombine(Integer isCombine) {
            this.isCombine = isCombine;
            }
                    
    public Integer getOrderType(){
        return orderType;
    }

        public void setOrderType(Integer orderType) {
            this.orderType = orderType;
            }
                    
    public BigDecimal getSetDiscount(){
        return setDiscount;
    }

        public void setSetDiscount(BigDecimal setDiscount) {
            this.setDiscount = setDiscount;
            }
                    
    public Integer getIsInternalSettlement(){
        return isInternalSettlement;
    }

        public void setIsInternalSettlement(Integer isInternalSettlement) {
            this.isInternalSettlement = isInternalSettlement;
            }
                    
    public Integer getIsUpgrade(){
        return isUpgrade;
    }

        public void setIsUpgrade(Integer isUpgrade) {
            this.isUpgrade = isUpgrade;
            }
                    
    public Integer getIsDuplicateItem(){
        return isDuplicateItem;
    }

        public void setIsDuplicateItem(Integer isDuplicateItem) {
            this.isDuplicateItem = isDuplicateItem;
            }
                    
    public String getSetApply(){
        return setApply;
    }

        public void setSetApply(String setApply) {
            this.setApply = setApply;
            }
                    
    public Integer getDataSources(){
        return dataSources;
    }

        public void setDataSources(Integer dataSources) {
            this.dataSources = dataSources;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }

        public void setIsDeleted(Boolean isDeleted) {
            this.isDeleted = isDeleted;
            }
                    
    public Integer getIsValid(){
        return isValid;
    }

        public void setIsValid(Integer isValid) {
            this.isValid = isValid;
            }
                    
    public Date getCreatedAt(){
        return createdAt;
    }

        public void setCreatedAt(Date createdAt) {
            this.createdAt = createdAt;
            }
                    
    public Date getUpdatedAt(){
        return updatedAt;
    }

        public void setUpdatedAt(Date updatedAt) {
            this.updatedAt = updatedAt;
            }
    
    @Override
    protected Serializable pkVal(){
            return this.id;
        }

@Override
    public String toString(){
        return"SetMainFilePO{" +
                            "appId=" + appId +
                                    ", ownerCode=" + ownerCode +
                                    ", ownerParCode=" + ownerParCode +
                                    ", orgId=" + orgId +
                                    ", id=" + id +
                                    ", setCategory=" + setCategory +
                                    ", dealerCode=" + dealerCode +
                                    ", setType=" + setType +
                                    ", setName=" + setName +
                                    ", setCode=" + setCode +
                                    ", vehiclePurpose=" + vehiclePurpose +
                                    ", setExplain=" + setExplain +
                                    ", enableDate=" + enableDate +
                                    ", discontinueDate=" + discontinueDate +
                                    ", modelName=" + modelName +
                                    ", modelCode=" + modelCode +
                                    ", engineCode=" + engineCode +
                                    ", isCombine=" + isCombine +
                                    ", orderType=" + orderType +
                                    ", setDiscount=" + setDiscount +
                                    ", isInternalSettlement=" + isInternalSettlement +
                                    ", isUpgrade=" + isUpgrade +
                                    ", isDuplicateItem=" + isDuplicateItem +
                                    ", setApply=" + setApply +
                                    ", dataSources=" + dataSources +
                                    ", isDeleted=" + isDeleted +
                                    ", isValid=" + isValid +
                                    ", createdAt=" + createdAt +
                                    ", updatedAt=" + updatedAt +
                    "}";
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"id");
    }

}
