package com.yonyou.dmscus.customer.entity.dto.inviteInsurance;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 邀约续保规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */

@Data
public class InviteInsuranceRuleDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 经销商代码（VCDC：代表厂端，经销商代码：代表各自经销商）
     */
    private String dealerCode;

    /**
     * 邀约类型：首保、定保、保险、客户流失
     */
    private Integer inviteType;

    /**
     * 邀约规则
     */
    private Integer inviteRule;

    /**
     * 邀约规则值
     */
    private Integer ruleValue;

    /**
     * 提前N天邀约
     */
    private Integer dayInAdvance;

    /**
     * 再提醒间隔（月）
     */
    private Integer remindInterval;

    /**
     * 是否启用：1、启用，2、不启用
     */
    private Integer isUse;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    private LocalDateTime updatedAt;

    /**
     * 超时关闭时间
     */
    private Integer closeInterval;

    /**
     * 修改后未处理 1:是0,否
     */
    private Integer updateIsExecute;

    public InviteInsuranceRuleDTO() {
        super();
    }


    @Override
    public String toString() {
        return "InviteInsuranceRuleDTO{" +
                "appId=" + appId +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", dealerCode=" + dealerCode +
                ", inviteType=" + inviteType +
                ", inviteRule=" + inviteRule +
                ", ruleValue=" + ruleValue +
                ", dayInAdvance=" + dayInAdvance +
                ", remindInterval=" + remindInterval +
                ", isUse=" + isUse +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", closeInterval=" + closeInterval +
                ", updateIsExecute=" + updateIsExecute +
                "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
