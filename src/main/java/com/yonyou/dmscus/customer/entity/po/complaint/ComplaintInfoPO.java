package com.yonyou.dmscus.customer.entity.po.complaint;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

    import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.baomidou.mybatisplus.extension.activerecord.Model;

    
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 客户投诉信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@TableName("tt_complaint_info")
public class ComplaintInfoPO extends BasePO<ComplaintInfoPO> {

    private static final long serialVersionUID=1L;
    
    /**
     * 系统ID
     */
    @TableField("app_id")
        private String appId;
    
    /**
     * 所有者代码 
     */
    @TableField("owner_code")
        private String ownerCode;
    
    /**
     * 所有者的父组织代码 
     */
    @TableField("owner_par_code")
        private String ownerParCode;
    
    /**
     * 组织ID
     */
    @TableField("org_id")
        private Integer orgId;
        
    /**
     * 主键ID
     */
            @TableId(value = "id", type = IdType.AUTO)
                        private Long id;
    
    /**
     * 投诉ID（投诉编号）可 用于和呼叫中心对接
     */
    @TableField("complaint_id")
        private String complaintId;
    /**
     * 工单状态
     */
    @TableField("work_order_status")
    private Integer workOrderStatus;
    /**
     * 是否结案
     */
    @TableField("is_close_case")
    private Integer isCloseCase;
    
    /**
     * 投诉类型
     */
    @TableField("type")
        private Integer type;
    
    /**
     * 投诉分类
     */
    @TableField("classification")
        private Integer classification;
    
    /**
     * 投诉来源
     */
    @TableField("source")
        private Integer source;
    
    /**
     * 来电时间/投诉日期
     */
    @TableField("call_time")
        private Date callTime;
    
    /**
     * 首次重启日期
     */
    @TableField("fisrt_restart_time")
        private Date fisrtRestartTime;
    
    /**
     * 最新重启日期
     */
    @TableField("newest_restart_time")
        private Date newestRestartTime;
    
    /**
     * 来电客户姓名/投诉人姓名
     */
    @TableField("call_name")
        private String callName;

    /**
     * 投诉人性别
     */
    @TableField("sex")
    private Integer sex;
    
    /**
     * 来电电话/投诉人电话
     */
    @TableField("call_tel")
        private String callTel;
    
    /**
     * 车主姓名
     */
    @TableField("name")
        private String name;
    
    /**
     * 车牌号
     */
    @TableField("license_plate_num")
        private String licensePlateNum;
    
    /**
     * 车架号
     */
    @TableField("vin")
        private String vin;
    
    /**
     * 购车时间
     */
    @TableField("buy_time")
        private Date buyTime;
    
    /**
     * 车型
     */
    @TableField("model")
        private String model;
    
    /**
     * 年款
     */
    @TableField("model_year")
        private String modelYear;

    /**
     * 车款名称
     */
    @TableField("config_name")
    private String configName;
    
    /**
     * 购买经销商
     */
    @TableField("buy_dealer_name")
        private String buyDealerName;
    
    /**
     * 处理经销商
     */
    @TableField("dealer_name")
        private String dealerName;
    
    /**
     * 处理经销商代码
     */
    @TableField("dealer_code")
        private String dealerCode;
    
    /**
     * 里程
     */
    @TableField("mileage")
        private Integer mileage;
    /**
     * 车主地址
     */
    @TableField("owner_address")
    private String ownerAddress;
    
    /**
     * 回复联系人手机1
     */
    @TableField("reply_tel")
        private String replyTel;
    
    /**
     * 回复联系人手机2
     */
    @TableField("reply_tel2")
        private String replyTel2;
    
    /**
     * 投诉主题
     */
    @TableField("subject")
        private String subject;
    
    /**
     * 问题描述
     */
    @TableField("problem")
        private String problem;
    
    /**
     * 坐席主管说明
     */
    @TableField("illustrate")
        private String illustrate;
    
    /**
     * 投诉单类别一级层
     */
    @TableField("category1")
        private String category1;
    
    /**
     * 投诉单类别二级层
     */
    @TableField("category2")
        private String category2;
    
    /**
     * 投诉单类别三级层
     */
    @TableField("category3")
        private String category3;

    /**
     * 部位
     */
    @TableField("part")
    private String part;

    /**
     * 细分部位
     */
    @TableField("subdivision_part")
    private String subdivisionPart;

    /**
     * 问题
     */
    @TableField("problem_info")
    private String ProblemInfo;

    /**
     * 客户要求
     */
    @TableField("cus_requirement")
    private String cusRequirement;

    /**
     * CC部位
     */
    @TableField("cc_part")
        private String ccPart;
    
    /**
     * CC细分部位
     */
    @TableField("cc_subdivision_part")
        private String ccSubdivisionPart;
    
    /**
     * CC问题
     */
    @TableField("cc_problem")
        private String ccProblem;
    
    /**
     * CC要求
     */
    @TableField("cc_requirement")
        private String ccRequirement;

    /**
     * 是否上报
     */
    @TableField("is_report")
    private Boolean isReport;
    
    /**
     * 投诉部门
     */
    @TableField("department")
        private String department;
    
    /**
     * 接待员
     */
    @TableField("receptionist")
        private String receptionist;
    
    /**
     * 重要等级
     */
    @TableField("importance_level")
        private Integer importanceLevel;

    /**
     *期望经销商联系时间
     */
    @TableField("hope_reply_time")
    private  Date hopeReplyTime;
    
    /**
     * 经销商首次回复时间
     */
    @TableField("dealer_fisrt_reply_time")
        private Date dealerFisrtReplyTime;
    
    /**
     * 首次重启经销商首次回复时间
     */
    @TableField("fisrt_restart_dealer_fisrt_reply_time")
        private Date fisrtRestartDealerFisrtReplyTime;
    
    /**
     * 是否回访
     */
    @TableField("is_revisit")
        private Integer isRevisit;
    /**
     * 区域
     */
    @TableField("region")
    private String region;
    /**
     * 区域ID
     */
    @TableField("region_id")
    private  Long regionId;

    /**
     * 区域经理
     */
    @TableField("region_manager")
    private String regionManager;
    /**
     * 区域经理ID
     */
    @TableField("region_manager_id")
    private  Long regionManagerId;
    /**
     * 区域(购买)
     */
    @TableField("buy_region")
    private String buyRegion;
    /**
     * 区域ID(购买)
     */
    @TableField("buy_region_id")
    private  Long buyRegionId;

    /**
     * 区域经理(购买)
     */
    @TableField("buy_region_manager")
    private String buyRegionManager;
    /**
     * 区域经理ID(购买)
     */
    @TableField("buy_region_manager_id")
    private  Long buyRegionManagerId;
    /**
     * 集团(购买)
     */
    @TableField("buy_bloc")
    private String buyBloc;
    /**
     * 购买经销商代码
     */
    @TableField("buy_dealer_code")
    private String buyDealerCode;
    
    /**
     * 回访时间
     */
    @TableField("revisit_time")
        private Date revisitTime;
    
    /**
     * 回访结果
     */
    @TableField("revisit_result")
        private String revisitResult;
    
    /**
     * 回访内容
     */
    @TableField("revisit_content")
        private String revisitContent;
    
    /**
     * 经销商申请结案时间
     */
    @TableField("apply_time")
        private Date applyTime;
    
    /**
     * 区域经理是否同意
     */
    @TableField("is_agree")
        private Integer isAgree;
    
    /**
     * 区域经理提交结案时间
     */
    @TableField("submit_time")
        private Date submitTime;
    
    /**
     * 结案时间
     */
    @TableField("close_case_time")
        private Date closeCaseTime;
    
    /**
     * 重启结案时间
     */
    @TableField("restart_close_case_time")
        private Date restartCloseCaseTime;
    
    /**
     * 结案状态（投诉单状态）
     */
    @TableField("close_case_status")
        private Integer closeCaseStatus;
    /**
     * 集团
     */
    @TableField("bloc")
    private String bloc;
    
    /**
     * 跟进状态
     */
    @TableField("follow_status")
        private Integer followStatus;
    
    /**
     * 投诉的根本原因 多选用逗号分隔
     */
    @TableField("basic_reason")
        private String basicReason;
    
    /**
     * 车辆是否修复
     */
    @TableField("is_repaired")
        private Integer isRepaired;
    
    /**
     * 技术与维修方案
     */
    @TableField("tech_maintain_plan")
        private String techMaintainPlan;
    
    /**
     * 亲善方案
     */
    @TableField("rapport_plan")
        private String rapportPlan;
    
    /**
     * 潜在风险
     */
    @TableField("risk")
        private String risk;

    /**
     * 区域经理意见
     */
    @TableField("regional_manager_comments")
    private String regionalManagerComments;
    
    /**
     * 进销商是否已读 1 已读 0 未读
     */
    @TableField("dealer_is_read")
        private Boolean dealerIsRead;
    
    /**
     * 区域经理是否已读 1 已读 0 未读
     */
    @TableField("manager_is_read")
        private Boolean managerIsRead;
    
    /**
     * CCM是否已读 1 已读 0 未读
     */
    @TableField("ccm_is_read")
        private Boolean ccmIsRead;
    
    /**
     * 数据来源 
     */
    @TableField("data_sources")
        private Integer dataSources;
    
    /**
     * 是否删除
     */
    @TableField("is_deleted")
        private Boolean isDeleted;
    
    /**
     * 是否有效 
     */
    @TableField("is_valid")
        private Integer isValid;
    
    /**
     * 创建时间
     */
    @TableField("created_at")
        private Date createdAt;
    
    /**
     * 更新时间
     */
    @TableField("updated_at")
        private Date updatedAt;
    /**
     * 集团Id
     */
    @TableField("bloc_id")
    private Long blocId;
    /**
     * 客户是否满意
     */
    @TableField("is_satisfied")
    private  Integer isSatisfied;

    /**
     * 工单性质
     */
    @TableField("work_order_nature")
    private  Integer workOrderNature;

    /**
     * 工单分类
     */
    @TableField("work_order_classification")
    private  Integer workOrderClassification;

    /**
     * 回复联系人姓名
     *
     */
    @TableField("reply_name")
    private  String replyName;

    /**
     * 服务承诺
     *
     */
    @TableField("service_commitment")
    private  Integer serviceCommitment;

    /**
     * 案件重启后是否回访
     */
    @TableField("is_restart_revisit")
    private Integer isRestartRevisit;

    /**
     * 案件首次结案时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @TableField("first_close_case_time")
    private Date firstCloseCaseTime;

    /**
     * 首次重启结案时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @TableField("first_restart_close_case_time")
    private Date firstRestartCloseCaseTime;


    public ComplaintInfoPO(){
        super();
    }

    public Date getFirstRestartCloseCaseTime() {
        return firstRestartCloseCaseTime;
    }

    public void setFirstRestartCloseCaseTime(Date firstRestartCloseCaseTime) {
        this.firstRestartCloseCaseTime = firstRestartCloseCaseTime;
    }

    public Integer getIsRestartRevisit() {
        return isRestartRevisit;
    }

    public void setIsRestartRevisit(Integer isRestartRevisit) {
        this.isRestartRevisit = isRestartRevisit;
    }

    public Date getFirstCloseCaseTime() {
        return firstCloseCaseTime;
    }

    public void setFirstCloseCaseTime(Date firstCloseCaseTime) {
        this.firstCloseCaseTime = firstCloseCaseTime;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public Date getHopeReplyTime() {
        return hopeReplyTime;
    }

    public void setHopeReplyTime(Date hopeReplyTime) {
        this.hopeReplyTime = hopeReplyTime;
    }

    public String getProblemInfo() {
        return ProblemInfo;
    }

    public void setProblemInfo(String problemInfo) {
        ProblemInfo = problemInfo;
    }

    public String getCusRequirement() {
        return cusRequirement;
    }

    public void setCusRequirement(String cusRequirement) {
        this.cusRequirement = cusRequirement;
    }

    public String getReplyName() {
        return replyName;
    }

    public void setReplyName(String replyName) {
        this.replyName = replyName;
    }

    public Integer getServiceCommitment() {
        return serviceCommitment;
    }

    public void setServiceCommitment(Integer serviceCommitment) {
        this.serviceCommitment = serviceCommitment;
    }

    public Integer getWorkOrderClassification() {
        return workOrderClassification;
    }

    public void setWorkOrderClassification(Integer workOrderClassification) {
        this.workOrderClassification = workOrderClassification;
    }

    public Integer getWorkOrderNature() {
        return workOrderNature;
    }

    public void setWorkOrderNature(Integer workOrderNature) {
        this.workOrderNature = workOrderNature;
    }

    public String getBuyRegion() {
        return buyRegion;
    }

    public void setBuyRegion(String buyRegion) {
        this.buyRegion = buyRegion;
    }

    public Long getBuyRegionId() {
        return buyRegionId;
    }

    public void setBuyRegionId(Long buyRegionId) {
        this.buyRegionId = buyRegionId;
    }

    public String getBuyRegionManager() {
        return buyRegionManager;
    }

    public void setBuyRegionManager(String buyRegionManager) {
        this.buyRegionManager = buyRegionManager;
    }

    public Long getBuyRegionManagerId() {
        return buyRegionManagerId;
    }

    public void setBuyRegionManagerId(Long buyRegionManagerId) {
        this.buyRegionManagerId = buyRegionManagerId;
    }

    public String getBuyBloc() {
        return buyBloc;
    }

    public void setBuyBloc(String buyBloc) {
        this.buyBloc = buyBloc;
    }

    public String getBuyDealerCode() {
        return buyDealerCode;
    }

    public void setBuyDealerCode(String buyDealerCode) {
        this.buyDealerCode = buyDealerCode;
    }

    public Boolean getDealerIsRead() {
        return dealerIsRead;
    }

    public void setDealerIsRead(Boolean dealerIsRead) {
        this.dealerIsRead = dealerIsRead;
    }

    public Boolean getManagerIsRead() {
        return managerIsRead;
    }

    public void setManagerIsRead(Boolean managerIsRead) {
        this.managerIsRead = managerIsRead;
    }

    public Boolean getCcmIsRead() {
        return ccmIsRead;
    }

    public void setCcmIsRead(Boolean ccmIsRead) {
        this.ccmIsRead = ccmIsRead;
    }

    public Integer getIsSatisfied() {
        return isSatisfied;
    }

    public void setIsSatisfied(Integer isSatisfied) {
        this.isSatisfied = isSatisfied;
    }

    public Long getBlocId() {
        return blocId;
    }

    public void setBlocId(Long blocId) {
        this.blocId = blocId;
    }

    public String getBloc() {
        return bloc;
    }

    public void setBloc(String bloc) {
        this.bloc = bloc;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public String getRegionManager() {
        return regionManager;
    }

    public void setRegionManager(String regionManager) {
        this.regionManager = regionManager;
    }

    public Long getRegionManagerId() {
        return regionManagerId;
    }

    public void setRegionManagerId(Long regionManagerId) {
        this.regionManagerId = regionManagerId;
    }

    public Integer getWorkOrderStatus() {
        return workOrderStatus;
    }

    public void setWorkOrderStatus(Integer workOrderStatus) {
        this.workOrderStatus = workOrderStatus;
    }

    public Integer getIsCloseCase() {
        return isCloseCase;
    }

    public void setIsCloseCase(Integer isCloseCase) {
        this.isCloseCase = isCloseCase;
    }



    public String getOwnerAddress() {
        return ownerAddress;
    }

    public void setOwnerAddress(String ownerAddress) {
        this.ownerAddress = ownerAddress;
    }

    public String getOwnerCode(){
        return ownerCode;
    }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
            }
                    

                    
    public Integer getOrgId(){
        return orgId;
    }

        public void setOrgId(Integer orgId) {
            this.orgId = orgId;
            }
                    
    public Long getId(){
        return id;
    }

        public void setId(Long id) {
            this.id = id;
            }
                    
    public String getComplaintId(){
        return complaintId;
    }

        public void setComplaintId(String complaintId) {
            this.complaintId = complaintId;
            }
                    
    public Integer getType(){
        return type;
    }

        public void setType(Integer type) {
            this.type = type;
            }

    public Boolean getReport() {
        return isReport;
    }

    public void setReport(Boolean report) {
        isReport = report;
    }

    public Integer getClassification(){
        return classification;
    }

        public void setClassification(Integer classification) {
            this.classification = classification;
            }
                    
    public Integer getSource(){
        return source;
    }

        public void setSource(Integer source) {
            this.source = source;
            }
                    
    public Date getCallTime(){
        return callTime;
    }

        public void setCallTime(Date callTime) {
            this.callTime = callTime;
            }
                    
    public Date getFisrtRestartTime(){
        return fisrtRestartTime;
    }

        public void setFisrtRestartTime(Date fisrtRestartTime) {
            this.fisrtRestartTime = fisrtRestartTime;
            }
                    
    public Date getNewestRestartTime(){
        return newestRestartTime;
    }

        public void setNewestRestartTime(Date newestRestartTime) {
            this.newestRestartTime = newestRestartTime;
            }
                    
    public String getCallName(){
        return callName;
    }

        public void setCallName(String callName) {
            this.callName = callName;
            }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getCallTel(){
        return callTel;
    }

        public void setCallTel(String callTel) {
            this.callTel = callTel;
            }
                    
    public String getName(){
        return name;
    }

        public void setName(String name) {
            this.name = name;
            }
                    
    public String getLicensePlateNum(){
        return licensePlateNum;
    }

        public void setLicensePlateNum(String licensePlateNum) {
            this.licensePlateNum = licensePlateNum;
            }
                    
    public String getVin(){
        return vin;
    }

        public void setVin(String vin) {
            this.vin = vin;
            }
                    
    public Date getBuyTime(){
        return buyTime;
    }

        public void setBuyTime(Date buyTime) {
            this.buyTime = buyTime;
            }
                    
    public String getModel(){
        return model;
    }

        public void setModel(String model) {
            this.model = model;
            }
                    
    public String getModelYear(){
        return modelYear;
    }

        public void setModelYear(String modelYear) {
            this.modelYear = modelYear;
            }
                    
    public String getBuyDealerName(){
        return buyDealerName;
    }

        public void setBuyDealerName(String buyDealerName) {
            this.buyDealerName = buyDealerName;
            }
                    
    public String getDealerName(){
        return dealerName;
    }

        public void setDealerName(String dealerName) {
            this.dealerName = dealerName;
            }
                    
    public String getDealerCode(){
        return dealerCode;
    }

        public void setDealerCode(String dealerCode) {
            this.dealerCode = dealerCode;
            }
                    
    public Integer getMileage(){
        return mileage;
    }

        public void setMileage(Integer mileage) {
            this.mileage = mileage;
            }
                    
    public String getReplyTel(){
        return replyTel;
    }

        public void setReplyTel(String replyTel) {
            this.replyTel = replyTel;
            }
                    
    public String getReplyTel2(){
        return replyTel2;
    }

        public void setReplyTel2(String replyTel2) {
            this.replyTel2 = replyTel2;
            }

    public String getPart() {
        return part;
    }

    public void setPart(String part) {
        this.part = part;
    }

    public String getSubdivisionPart() {
        return subdivisionPart;
    }

    public void setSubdivisionPart(String subdivisionPart) {
        this.subdivisionPart = subdivisionPart;
    }

    public String getSubject(){
        return subject;
    }

        public void setSubject(String subject) {
            this.subject = subject;
            }
                    
    public String getProblem(){
        return problem;
    }

        public void setProblem(String problem) {
            this.problem = problem;
            }
                    
    public String getIllustrate(){
        return illustrate;
    }

        public void setIllustrate(String illustrate) {
            this.illustrate = illustrate;
            }
                    
    public String getCategory1(){
        return category1;
    }

        public void setCategory1(String category1) {
            this.category1 = category1;
            }
                    
    public String getCategory2(){
        return category2;
    }

        public void setCategory2(String category2) {
            this.category2 = category2;
            }
                    
    public String getCategory3(){
        return category3;
    }

        public void setCategory3(String category3) {
            this.category3 = category3;
            }
                    
    public String getCcPart(){
        return ccPart;
    }

        public void setCcPart(String ccPart) {
            this.ccPart = ccPart;
            }
                    
    public String getCcSubdivisionPart(){
        return ccSubdivisionPart;
    }

        public void setCcSubdivisionPart(String ccSubdivisionPart) {
            this.ccSubdivisionPart = ccSubdivisionPart;
            }
                    
    public String getCcProblem(){
        return ccProblem;
    }

        public void setCcProblem(String ccProblem) {
            this.ccProblem = ccProblem;
            }
                    
    public String getCcRequirement(){
        return ccRequirement;
    }

        public void setCcRequirement(String ccRequirement) {
            this.ccRequirement = ccRequirement;
            }
                    
    public String getDepartment(){
        return department;
    }

        public void setDepartment(String department) {
            this.department = department;
            }
                    
    public String getReceptionist(){
        return receptionist;
    }

        public void setReceptionist(String receptionist) {
            this.receptionist = receptionist;
            }
                    
    public Integer getImportanceLevel(){
        return importanceLevel;
    }

        public void setImportanceLevel(Integer importanceLevel) {
            this.importanceLevel = importanceLevel;
            }
                    
    public Date getDealerFisrtReplyTime(){
        return dealerFisrtReplyTime;
    }

        public void setDealerFisrtReplyTime(Date dealerFisrtReplyTime) {
            this.dealerFisrtReplyTime = dealerFisrtReplyTime;
            }
                    
    public Date getFisrtRestartDealerFisrtReplyTime(){
        return fisrtRestartDealerFisrtReplyTime;
    }

        public void setFisrtRestartDealerFisrtReplyTime(Date fisrtRestartDealerFisrtReplyTime) {
            this.fisrtRestartDealerFisrtReplyTime = fisrtRestartDealerFisrtReplyTime;
            }
                    
    public Integer getIsRevisit(){
        return isRevisit;
    }

        public void setIsRevisit(Integer isRevisit) {
            this.isRevisit = isRevisit;
            }
                    
    public Date getRevisitTime(){
        return revisitTime;
    }

        public void setRevisitTime(Date revisitTime) {
            this.revisitTime = revisitTime;
            }
                    
    public String getRevisitResult(){
        return revisitResult;
    }

        public void setRevisitResult(String revisitResult) {
            this.revisitResult = revisitResult;
            }
                    
    public String getRevisitContent(){
        return revisitContent;
    }

        public void setRevisitContent(String revisitContent) {
            this.revisitContent = revisitContent;
            }
                    
    public Date getApplyTime(){
        return applyTime;
    }

        public void setApplyTime(Date applyTime) {
            this.applyTime = applyTime;
            }
                    
    public Integer getIsAgree(){
        return isAgree;
    }

        public void setIsAgree(Integer isAgree) {
            this.isAgree = isAgree;
            }
                    
    public Date getSubmitTime(){
        return submitTime;
    }

        public void setSubmitTime(Date submitTime) {
            this.submitTime = submitTime;
            }
                    
    public Date getCloseCaseTime(){
        return closeCaseTime;
    }

        public void setCloseCaseTime(Date closeCaseTime) {
            this.closeCaseTime = closeCaseTime;
            }
                    
    public Date getRestartCloseCaseTime(){
        return restartCloseCaseTime;
    }

        public void setRestartCloseCaseTime(Date restartCloseCaseTime) {
            this.restartCloseCaseTime = restartCloseCaseTime;
            }
                    
    public Integer getCloseCaseStatus(){
        return closeCaseStatus;
    }

        public void setCloseCaseStatus(Integer closeCaseStatus) {
            this.closeCaseStatus = closeCaseStatus;
            }
                    
    public Integer getFollowStatus(){
        return followStatus;
    }

        public void setFollowStatus(Integer followStatus) {
            this.followStatus = followStatus;
            }
                    
    public String getBasicReason(){
        return basicReason;
    }

        public void setBasicReason(String basicReason) {
            this.basicReason = basicReason;
            }
                    
    public Integer getIsRepaired(){
        return isRepaired;
    }

        public void setIsRepaired(Integer isRepaired) {
            this.isRepaired = isRepaired;
            }
                    
    public String getTechMaintainPlan(){
        return techMaintainPlan;
    }

        public void setTechMaintainPlan(String techMaintainPlan) {
            this.techMaintainPlan = techMaintainPlan;
            }
                    
    public String getRapportPlan(){
        return rapportPlan;
    }

        public void setRapportPlan(String rapportPlan) {
            this.rapportPlan = rapportPlan;
            }
                    
    public String getRisk(){
        return risk;
    }

        public void setRisk(String risk) {
            this.risk = risk;
            }
                    
    public Boolean getIsDealerIsRead(){
        return dealerIsRead;
    }

        public void setIsDealerIsRead(Boolean dealerIsRead) {
            this.dealerIsRead = dealerIsRead;
            }
                    
    public Boolean getIsManagerIsRead(){
        return managerIsRead;
    }

        public void setIsManagerIsRead(Boolean managerIsRead) {
            this.managerIsRead = managerIsRead;
            }
                    
    public Boolean getIsCcmIsRead(){
        return ccmIsRead;
    }

        public void setIsCcmIsRead(Boolean ccmIsRead) {
            this.ccmIsRead = ccmIsRead;
            }
                    
    public Integer getDataSources(){
        return dataSources;
    }

        public void setDataSources(Integer dataSources) {
            this.dataSources = dataSources;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }

        public void setIsDeleted(Boolean isDeleted) {
            this.isDeleted = isDeleted;
            }
                    
    public Integer getIsValid(){
        return isValid;
    }

        public void setIsValid(Integer isValid) {
            this.isValid = isValid;
            }
                    


    public String getRegionalManagerComments() {
        return regionalManagerComments;
    }

    public void setRegionalManagerComments(String regionalManagerComments) {
        this.regionalManagerComments = regionalManagerComments;
    }

    @Override
    protected Serializable pkVal(){
            return this.id;
        }

@Override
    public String toString(){
        return"ComplaintInfoPO{" +
                            "appId=" + appId +
                                    ", ownerCode=" + ownerCode +
                                    ", ownerParCode=" + ownerParCode +
                                    ", orgId=" + orgId +
                                    ", id=" + id +
                                    ", complaintId=" + complaintId +
                                    ", type=" + type +
                                    ", classification=" + classification +
                                    ", source=" + source +
                                    ", callTime=" + callTime +
                                    ", fisrtRestartTime=" + fisrtRestartTime +
                                    ", newestRestartTime=" + newestRestartTime +
                                    ", callName=" + callName +
                                    ", callTel=" + callTel +
                                    ", name=" + name +
                                    ", licensePlateNum=" + licensePlateNum +
                                    ", vin=" + vin +
                                    ", buyTime=" + buyTime +
                                    ", model=" + model +
                                    ", modelYear=" + modelYear +
                                    ", buyDealerName=" + buyDealerName +
                                    ", dealerName=" + dealerName +
                                    ", dealerCode=" + dealerCode +
                                    ", mileage=" + mileage +
                                    ", replyTel=" + replyTel +
                                    ", replyTel2=" + replyTel2 +
                                    ", subject=" + subject +
                                    ", problem=" + problem +
                                    ", illustrate=" + illustrate +
                                    ", category1=" + category1 +
                                    ", category2=" + category2 +
                                    ", category3=" + category3 +
                                    ", ccPart=" + ccPart +
                                    ", ccSubdivisionPart=" + ccSubdivisionPart +
                                    ", ccProblem=" + ccProblem +
                                    ", ccRequirement=" + ccRequirement +
                                    ", department=" + department +
                                    ", receptionist=" + receptionist +
                                    ", importanceLevel=" + importanceLevel +
                                    ", dealerFisrtReplyTime=" + dealerFisrtReplyTime +
                                    ", fisrtRestartDealerFisrtReplyTime=" + fisrtRestartDealerFisrtReplyTime +
                                    ", isRevisit=" + isRevisit +
                                    ", revisitTime=" + revisitTime +
                                    ", revisitResult=" + revisitResult +
                                    ", revisitContent=" + revisitContent +
                                    ", applyTime=" + applyTime +
                                    ", isAgree=" + isAgree +
                                    ", submitTime=" + submitTime +
                                    ", closeCaseTime=" + closeCaseTime +
                                    ", restartCloseCaseTime=" + restartCloseCaseTime +
                                    ", closeCaseStatus=" + closeCaseStatus +
                                    ", followStatus=" + followStatus +
                                    ", basicReason=" + basicReason +
                                    ", isRepaired=" + isRepaired +
                                    ", techMaintainPlan=" + techMaintainPlan +
                                    ", rapportPlan=" + rapportPlan +
                                    ", risk=" + risk +
                                    ", dealerIsRead=" + dealerIsRead +
                                    ", managerIsRead=" + managerIsRead +
                                    ", ccmIsRead=" + ccmIsRead +
                                    ", dataSources=" + dataSources +
                                    ", isDeleted=" + isDeleted +
                                    ", isValid=" + isValid +
                                    ", createdAt=" + createdAt +
                                    ", updatedAt=" + updatedAt +
                    "}";
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"id");
    }

}
