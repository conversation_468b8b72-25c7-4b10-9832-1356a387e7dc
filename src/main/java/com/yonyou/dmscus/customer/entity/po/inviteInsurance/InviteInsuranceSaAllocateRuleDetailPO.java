package com.yonyou.dmscus.customer.entity.po.inviteInsurance;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 续保SA分配规则明细表,通过经销商CODE与invite_insurance_sa_allocate_rule关联，用于经销商SA分配中平均分配
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@TableName("tt_invite_insurance_sa_allocate_rule_detail")
@Data
public class InviteInsuranceSaAllocateRuleDetailPO extends BasePO<InviteInsuranceSaAllocateRuleDetailPO> {

    private static final long serialVersionUID = 1L;

    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    @TableField("owner_par_code")
    private String ownerParCode;

    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经销商代码
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 服务顾问ID
     */
    @TableField("sa_id")
    private String saId;

    /**
     * 服务顾问姓名
     */
    @TableField("sa_name")
    private String saName;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;

    /**
     * 服务顾问账号
     */
    @TableField("sa_code")
    private String saCode;

    /**
     * 10041001 是 10041002 否
     */
    @TableField("is_insurance")
    private Integer isInsurance;

    /**
     * 10041001 是 10041002 否
     */
    @TableField("is_other")
    private Integer isOther;

    public InviteInsuranceSaAllocateRuleDetailPO() {
        super();
    }


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "InviteInsuranceSaAllocateRuleDetailPO{" +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", dealerCode=" + dealerCode +
                ", saId=" + saId +
                ", saName=" + saName +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                ", saCode=" + saCode +
                ", isInsurance=" + isInsurance +
                ", isOther=" + isOther +
                "}";
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
