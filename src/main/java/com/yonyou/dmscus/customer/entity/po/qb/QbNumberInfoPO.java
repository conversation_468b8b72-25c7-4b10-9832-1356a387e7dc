package com.yonyou.dmscus.customer.entity.po.qb;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import javax.persistence.Table;
import java.util.Date;

/**
 * QB车辆
 */
@Data
@TableName("tt_qb_number_info")
public class QbNumberInfoPO extends BasePO<QbNumberInfoPO> {

  @TableField("qb_number_id")
  private Integer qbNumberId;

  @TableField("qb_number")
  private String qbNumber;

  @TableField("starting_date")
  private Date startingDate;

  @TableField("closing_date")
  private Date closingDate;

  @TableField("is_closed")
  private Integer isClosed;

  @TableField("closed_date")
  private Date closedDate;

  @TableField("APP_ID")
  private String appId;

  @TableField("OWNER_CODE")
  private String ownerCode;

  @TableField("OWNER_PAR_CODE")
  private String ownerParCode;

  @TableField("ORG_ID")
  private long orgId;

  @TableField("data_sources")
  private long dataSources;

  @TableField("is_valid")
  private long isValid;

  @TableField("is_deleted")
  private long isDeleted;

  @TableField("created_at")
  private Date createdAt;

  @TableField("created_by")
  private String createdBy;

  @TableField("updated_at")
  private Date updatedAt;

  @TableField("updated_by")
  private String updatedBy;

  @TableField("mark")
  private String mark;

  /**
   * 将PO 信息转化为DTO
   *
   * @param dtoClass 需要进行转换的dtoClass
   * <AUTHOR>
   * @since 2018/7/22 0022
   */
  public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
    return super.transDtoToPo(dtoClass);
  }

  /**
   * 将PO 信息转化为DTO
   *
   * @param dto 需要进行转换的dto
   * <AUTHOR>
   * @since 2018/7/22 0022
   */
  public <T extends BaseDTO> void transPoToDto(T dto) {
    BeanMapperUtil.copyProperties(this, dto, "id");
  }
}
