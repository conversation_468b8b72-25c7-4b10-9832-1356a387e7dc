package com.yonyou.dmscus.customer.entity.dto.inviteRule;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 邀约易损件和项目规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */

public class InvitePartItemRuleDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 经销商代码（VCDC：代表厂端，经销商代码：代表各自经销商）
     */
    private String dealerCode;

    /**
     * 类型：易损件、项目
     */
    private Integer type;

    /**
     * 零件、维修项目编号
     */
    private String code;

    /**
     * 零件、维修项目名称
     */
    private String name;

    /**
     * 邀约生成时间 ：NULL 代表 每天 0  每月，1~12  1~12月
     */
    private Integer inviteCreateMonth;

    /**
     * 提前N天邀约
     */
    private Integer dayInAdvance;

    /**
     * 再提醒间隔（月）
     */
    private Integer remindInterval;

    /**
     * 间隔里程（Km）
     */
    private Integer mileageInterval;

    /**
     * 间隔日期（月）
     */
    private Integer dateInterval;

    /**
     * 车架号 必须输入17位，---K---45—3----
     */
    private String vin;

    /**
     * 车型编码
     */
    private String modelCode;

    /**
     * 年款
     */
    private String modelYear;

    /**
     * 发动机编码
     */
    private String engineCode;

    /**
     * 变速箱编码
     */
    private String gearboxCode;

    /**
     * 规则关系：
     */
    private Integer ruleRelationship;

    /**
     * 是否启用：1 启用，0 不启用
     */
    private Integer isUse;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;


    /**
     * 状态 A 新增 U 更新
     */
    private String status;

    public InvitePartItemRuleDTO() {
        super();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAppId() {
        return appId;
    }


    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }


    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerParCode() {
        return ownerParCode;
    }


    public void setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public Integer getOrgId() {
        return orgId;
    }


    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId() {
        return id;
    }


    public void setId(Long id) {
        this.id = id;
    }

    public String getDealerCode() {
        return dealerCode;
    }


    public void setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
    }

    public Integer getType() {
        return type;
    }


    public void setType(Integer type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }


    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }


    public void setName(String name) {
        this.name = name;
    }

    public Integer getInviteCreateMonth() {
        return inviteCreateMonth;
    }


    public void setInviteCreateMonth(Integer inviteCreateMonth) {
        this.inviteCreateMonth = inviteCreateMonth;
    }

    public Integer getDayInAdvance() {
        return dayInAdvance;
    }


    public void setDayInAdvance(Integer dayInAdvance) {
        this.dayInAdvance = dayInAdvance;
    }

    public Integer getRemindInterval() {
        return remindInterval;
    }


    public void setRemindInterval(Integer remindInterval) {
        this.remindInterval = remindInterval;
    }

    public Integer getMileageInterval() {
        return mileageInterval;
    }


    public void setMileageInterval(Integer mileageInterval) {
        this.mileageInterval = mileageInterval;
    }

    public Integer getDateInterval() {
        return dateInterval;
    }


    public void setDateInterval(Integer dateInterval) {
        this.dateInterval = dateInterval;
    }

    public String getVin() {
        return vin;
    }


    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getModelCode() {
        return modelCode;
    }


    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getModelYear() {
        return modelYear;
    }


    public void setModelYear(String modelYear) {
        this.modelYear = modelYear;
    }

    public String getEngineCode() {
        return engineCode;
    }


    public void setEngineCode(String engineCode) {
        this.engineCode = engineCode;
    }

    public String getGearboxCode() {
        return gearboxCode;
    }


    public void setGearboxCode(String gearboxCode) {
        this.gearboxCode = gearboxCode;
    }

    public Integer getRuleRelationship() {
        return ruleRelationship;
    }


    public void setRuleRelationship(Integer ruleRelationship) {
        this.ruleRelationship = ruleRelationship;
    }

    public Integer getIsUse() {
        return isUse;
    }


    public void setIsUse(Integer isUse) {
        this.isUse = isUse;
    }

    public Integer getDataSources() {
        return dataSources;
    }


    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }


    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid() {
        return isValid;
    }


    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Date getCreatedAt() {
        return createdAt;
    }


    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }


    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "InvitePartItemRuleDTO{" +
                "appId=" + appId +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", dealerCode=" + dealerCode +
                ", type=" + type +
                ", code=" + code +
                ", name=" + name +
                ", inviteCreateMonth=" + inviteCreateMonth +
                ", dayInAdvance=" + dayInAdvance +
                ", remindInterval=" + remindInterval +
                ", mileageInterval=" + mileageInterval +
                ", dateInterval=" + dateInterval +
                ", vin=" + vin +
                ", modelCode=" + modelCode +
                ", modelYear=" + modelYear +
                ", engineCode=" + engineCode +
                ", gearboxCode=" + gearboxCode +
                ", ruleRelationship=" + ruleRelationship +
                ", isUse=" + isUse +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
