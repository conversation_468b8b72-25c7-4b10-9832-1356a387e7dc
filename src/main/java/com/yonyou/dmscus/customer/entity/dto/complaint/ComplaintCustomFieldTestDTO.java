package com.yonyou.dmscus.customer.entity.dto.complaint;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
@Data
public class ComplaintCustomFieldTestDTO {
    private List followStatus;
    private  List ccMainReason;
    private  List ccResult;
    private  List ccmPart;
    private  List ccmSubdivisionPart;
    private  List bigClassName;
    private  List category1;
    private  List category2;
    private  List category3;
    private  List classification11;
    private  List classification21;
    private  String isValid1;
    private  String classification31;
    private  List sort;
    private  List smallClass;
    private  String planFollowTime;
    private  String ccmIsRead;
    private  String importanceLevel;
    private  String callTime;
    private  String isFinish;
    private  Boolean category11;
    private  Boolean category21;
    private  Boolean category31;
    private  Boolean ccMainReason1;
    private  Boolean ccResult1;
    private  Boolean ccmPart1;
    private  Boolean classification111;
    private  Boolean classification211;
    private  Boolean classification311;
    private  Boolean isValid11;
    private  Boolean followStatus1;
    private  Boolean smallClass1;
    private  Boolean ccmSubdivisionPart1;

    /**
     * 是否有舆情风险
     */

    private  Integer isOpinion;
    /**
     * 结案备注
     */
    private  String closeCaseRemark;

    /**
     * 是否满意结案
     */
    private  String isCloseSatisfied;

    /**
     * 投诉类型
     */
    private  Integer saleType;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private   Date closeCaseTime;


    private Integer isAgree;
    private  Integer isRevisit;

    private  String regionalManagerComments;


    /**
     * 投诉信息表主键ID
     */
    private Long complaintInfoId;
    /**
     * 跟进内容
     */
    private String followContent;
    /**
     * 技术与维修方案
     */
    private String techMaintainPlan;

    /**
     * 亲善方案
     */
    private String rapportPlan;

    /**
     * 潜在风险
     */
    private String risk;
    /**
     * 投诉的根本原因 多选用逗号分隔
     */
    private List basicReason;

    /**
     * 车辆是否修复
     */
    private Integer isRepaired;
    /**
     * 客户是否满意
     */
    private  Integer isSatisfied;
    /**
     * 经销商申请结案时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date applyTime;

    private  String orgId;

    /**
     *区域经理是否同意结案
     */
    private Integer isAgreeRegion;

    /**
     * 区域经理意见
     */
    private  String regionComments;

    /**
     *总部是否同意结案
     */
    private Integer isAgreeHeadquarters;

    /**
     * 总部意见
     */
    private  String headquartersComments;

    /**
     * 总部是否同意案件重启
     */
    private  Integer isAgreeRestart;

    /**
     *区域经理是否满意结案（低满意度）
     */
    private Integer regionSatisfiedCase;
    /**
     *总部是否满意结案（低满意度）
     */
    private Integer HQSatisfiedCase;




}
