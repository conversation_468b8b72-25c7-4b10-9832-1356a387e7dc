package com.yonyou.dmscus.customer.entity.dto.goodwill;

import java.io.Serializable;
import java.util.Map;

@SuppressWarnings("serial")
public class queryUserByOrgTypeDTO implements Serializable{
	private Map data;
	private Integer orgType;
	private String roleCode;
	public Integer getOrgType() {
		return orgType;
	}
	public void setOrgType(Integer orgType) {
		this.orgType = orgType;
	}
	public String getRoleCode() {
		return roleCode;
	}
	public void setRoleCode(String roleCode) {
		this.roleCode = roleCode;
	}
	public Map getData() {
		return data;
	}
	public void setData(Map data) {
		this.data = data;
	}
	
	
	
	
}
