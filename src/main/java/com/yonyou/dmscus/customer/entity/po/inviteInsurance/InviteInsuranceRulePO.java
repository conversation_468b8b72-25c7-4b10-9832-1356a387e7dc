package com.yonyou.dmscus.customer.entity.po.inviteInsurance;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 邀约续保规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@TableName("tt_invite_insurance_rule")
@Data
public class InviteInsuranceRulePO extends BasePO<InviteInsuranceRulePO> {

    private static final long serialVersionUID = 1L;

    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    @TableField("owner_par_code")
    private String ownerParCode;

    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经销商代码（VCDC：代表厂端，经销商代码：代表各自经销商）
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 邀约类型：首保、定保、保险、客户流失
     */
    @TableField("invite_type")
    private Integer inviteType;

    /**
     * 邀约规则
     */
    @TableField("invite_rule")
    private Integer inviteRule;

    /**
     * 邀约规则值
     */
    @TableField("rule_value")
    private Integer ruleValue;

    /**
     * 提前N天邀约
     */
    @TableField("day_in_advance")
    private Integer dayInAdvance;

    /**
     * 再提醒间隔（月）
     */
    @TableField("remind_interval")
    private Integer remindInterval;

    /**
     * 是否启用：1、启用，2、不启用
     */
    @TableField("is_use")
    private Integer isUse;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;


    /**
     * 超时关闭时间
     */
    @TableField("close_interval")
    private Integer closeInterval;

    /**
     * 修改后未处理 1:是0,否
     */
    @TableField("update_is_execute")
    private Integer updateIsExecute;

    public InviteInsuranceRulePO() {
        super();
    }


    @Override
    public String toString() {
        return "InviteInsuranceRulePO{" +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", dealerCode=" + dealerCode +
                ", inviteType=" + inviteType +
                ", inviteRule=" + inviteRule +
                ", ruleValue=" + ruleValue +
                ", dayInAdvance=" + dayInAdvance +
                ", remindInterval=" + remindInterval +
                ", isUse=" + isUse +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                ", closeInterval=" + closeInterval +
                ", updateIsExecute=" + updateIsExecute +
                "}";
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
