package com.yonyou.dmscus.customer.entity.po.goodwill;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善预申请单亲善信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
@TableName("tt_goodwill_material_audit")
public class GoodwillMaterialAuditPO extends BasePO<GoodwillMaterialAuditPO> {

	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	@TableField("APP_ID")
	private String appId;

	/**
	 * 所有者代码
	 */
	@TableField("OWNER_CODE")
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	@TableField("OWNER_PAR_CODE")
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	@TableField("ORG_ID")
	private Integer orgId;

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 亲善单id
	 */
	@TableField("goodwill_apply_id")
	private Long goodwillApplyId;

	/**
	 * 客户背景
	 */
	@TableField("customer_background")
	private String customerBackground;

	/**
	 * 客户背景英文
	 */
	@TableField("customer_background_en")
	private String customerBackgroundEn;

	/**
	 * 投诉原因及处理经过
	 */
	@TableField("reason_and_dispose")
	private String reasonAndDispose;

	/**
	 * 投诉原因及处理经过英文
	 */
	@TableField("reason_and_dispose_en")
	private String reasonAndDisposeEn;

	/**
	 * 维修解决方案
	 */
	@TableField("repair_solution")
	private String repairSolution;

	/**
	 * 维修解决方案英文
	 */
	@TableField("repair_solution_en")
	private String repairSolutionEn;

	/**
	 * 客户要求
	 */
	@TableField("customer_require")
	private String customerRequire;

	/**
	 * 客户要求英文
	 */
	@TableField("customer_require_en")
	private String customerRequireEn;

	/**
	 * 潜在风险
	 */
	@TableField("potential_risk")
	private String potentialRisk;

	/**
	 * 潜在风险英文
	 */
	@TableField("potential_risk_en")
	private String potentialRiskEn;

	/**
	 * VR或TJ号英文
	 */
	@TableField("vr_or_tj_no_en")
	private String vrOrTjNoEn;

	/**
	 * VR或TJ号
	 */
	@TableField("vr_or_tj_no")
	private String vrOrTjNo;

	/**
	 * 商务亲善申请详情英文
	 */
	@TableField("business_goodwill_apply_detail_en")
	private String businessGoodwillApplyDetailEn;

	/**
	 * 商务亲善申请详情
	 */
	@TableField("business_goodwill_apply_detail")
	private String businessGoodwillApplyDetail;

	/**
	 * 是否有效
	 */
	@TableField("is_valid")
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	@TableField("is_deleted")
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	@TableField("created_at")
	private Date createdAt;

	/**
	 * 修改时间
	 */
	@TableField("updated_at")
	private Date updatedAt;

	public GoodwillMaterialAuditPO() {
		super();
	}

	@Override
	public String getAppId() {
		return appId;
	}

	@Override
	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	@Override
	public String getOwnerParCode() {
		return ownerParCode;
	}

	@Override
	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getGoodwillApplyId() {
		return goodwillApplyId;
	}

	public void setGoodwillApplyId(Long goodwillApplyId) {
		this.goodwillApplyId = goodwillApplyId;
	}

	public String getCustomerBackground() {
		return customerBackground;
	}

	public void setCustomerBackground(String customerBackground) {
		this.customerBackground = customerBackground;
	}

	public String getCustomerBackgroundEn() {
		return customerBackgroundEn;
	}

	public void setCustomerBackgroundEn(String customerBackgroundEn) {
		this.customerBackgroundEn = customerBackgroundEn;
	}

	public String getReasonAndDispose() {
		return reasonAndDispose;
	}

	public void setReasonAndDispose(String reasonAndDispose) {
		this.reasonAndDispose = reasonAndDispose;
	}

	public String getReasonAndDisposeEn() {
		return reasonAndDisposeEn;
	}

	public void setReasonAndDisposeEn(String reasonAndDisposeEn) {
		this.reasonAndDisposeEn = reasonAndDisposeEn;
	}

	public String getRepairSolution() {
		return repairSolution;
	}

	public void setRepairSolution(String repairSolution) {
		this.repairSolution = repairSolution;
	}

	public String getRepairSolutionEn() {
		return repairSolutionEn;
	}

	public void setRepairSolutionEn(String repairSolutionEn) {
		this.repairSolutionEn = repairSolutionEn;
	}

	public String getCustomerRequire() {
		return customerRequire;
	}

	public void setCustomerRequire(String customerRequire) {
		this.customerRequire = customerRequire;
	}

	public String getCustomerRequireEn() {
		return customerRequireEn;
	}

	public void setCustomerRequireEn(String customerRequireEn) {
		this.customerRequireEn = customerRequireEn;
	}

	public String getPotentialRisk() {
		return potentialRisk;
	}

	public void setPotentialRisk(String potentialRisk) {
		this.potentialRisk = potentialRisk;
	}

	public String getPotentialRiskEn() {
		return potentialRiskEn;
	}

	public void setPotentialRiskEn(String potentialRiskEn) {
		this.potentialRiskEn = potentialRiskEn;
	}

	public String getVrOrTjNoEn() {
		return vrOrTjNoEn;
	}

	public void setVrOrTjNoEn(String vrOrTjNoEn) {
		this.vrOrTjNoEn = vrOrTjNoEn;
	}

	public String getVrOrTjNo() {
		return vrOrTjNo;
	}

	public void setVrOrTjNo(String vrOrTjNo) {
		this.vrOrTjNo = vrOrTjNo;
	}

	public String getBusinessGoodwillApplyDetailEn() {
		return businessGoodwillApplyDetailEn;
	}

	public void setBusinessGoodwillApplyDetailEn(String businessGoodwillApplyDetailEn) {
		this.businessGoodwillApplyDetailEn = businessGoodwillApplyDetailEn;
	}

	public String getBusinessGoodwillApplyDetail() {
		return businessGoodwillApplyDetail;
	}

	public void setBusinessGoodwillApplyDetail(String businessGoodwillApplyDetail) {
		this.businessGoodwillApplyDetail = businessGoodwillApplyDetail;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	@Override
	public Date getCreatedAt() {
		return createdAt;
	}

	@Override
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Override
	public Date getUpdatedAt() {
		return updatedAt;
	}

	@Override
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	protected Serializable pkVal() {
		return this.id;
	}

	@Override
	public String toString() {
		return "GoodwillMaterialAuditPO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", goodwillApplyId=" + goodwillApplyId
				+ ", customerBackground=" + customerBackground + ", customerBackgroundEn=" + customerBackgroundEn
				+ ", reasonAndDispose=" + reasonAndDispose + ", reasonAndDisposeEn=" + reasonAndDisposeEn
				+ ", repairSolution=" + repairSolution + ", repairSolutionEn=" + repairSolutionEn + ", customerRequire="
				+ customerRequire + ", customerRequireEn=" + customerRequireEn + ", potentialRisk=" + potentialRisk
				+ ", potentialRiskEn=" + potentialRiskEn + ", vrOrTjNoEn=" + vrOrTjNoEn + ", vrOrTjNo=" + vrOrTjNo
				+ ", businessGoodwillApplyDetailEn=" + businessGoodwillApplyDetailEn + ", businessGoodwillApplyDetail="
				+ businessGoodwillApplyDetail + ", isValid=" + isValid + ", isDeleted=" + isDeleted + ", createdAt="
				+ createdAt + ", updatedAt=" + updatedAt + "}";
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dtoClass
	 *            需要进行转换的dtoClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
		return super.transDtoToPo(dtoClass);
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dto
	 *            需要进行转换的dto
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> void transPoToDto(T dto) {
		BeanMapperUtil.copyProperties(this, dto, "id");
	}

}
