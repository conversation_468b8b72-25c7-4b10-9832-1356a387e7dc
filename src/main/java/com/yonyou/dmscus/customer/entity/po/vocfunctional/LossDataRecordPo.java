package com.yonyou.dmscus.customer.entity.po.vocfunctional;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @title: LossDataRecord
 * @projectName dmscus.customer
 * @date 2022/12/1217:35
 */
@Data
@TableName("tt_loss_data_record")
@ApiModel(value = "LossDataRecordPo", description = "流失客户表")
public class LossDataRecordPo  extends  BasePo {

    @ApiModelProperty(value = "车架号",notes = "车架号",required = true,name = "dt",example = "YV1LFA2D2H1163606",dataType = "String")
    @TableField("vin")
    private String vin;

    @ApiModelProperty(value = "经销商",notes = "经销商",required = true,name = "dealer_code",example = "SHJ",dataType = "String")
    @TableField("dealer_code")
    private String dealerCode;


    @ApiModelProperty(value = "最后一次保养时间",notes = "最后一次保养时间:指上一次含机滤工单结算日期",required = true,name = "order_at",example = "",dataType = "Date")
    @TableField("order_at")
    private Date orderAt;


    @ApiModelProperty(value = "最后一次保养经销商:指机率工单经销商",notes = "最后一次保养经销商:指机率工单经销商",required = true,name = "last_dealer_code",example = "SHJ",dataType = "String")
    @TableField("last_dealer_code")
    private String lastDealerCode;

    @ApiModelProperty(value = "流失线索下发时间",notes = "流失线索下发时间",required = true,name = "record_at",example = "",dataType = "Date")
    @TableField("record_at")
    private Date recordAt;
    @ApiModelProperty(value = "线索id",notes = "线索id",required = true,name = "record_id",example = "",dataType = "Long")
    @TableField("record_id")
    private Long recordId;

    @ApiModelProperty(value = "开票时间",notes = "开票时间",required = true,name = "invoice_date",example = "",dataType = "Date")
    @TableField("invoice_date")
    private Date invoiceDate;

    @ApiModelProperty(value = "激活时间:线索下发后的机滤工单时间",notes = "激活时间:线索下发后的机滤工单时间",required = true,name = "active_at",example = "",dataType = "Date")
    @TableField("active_at")
    private Date activeAt;

    @ApiModelProperty(value = "激活经销商:线索下发后的机率工单开单经销商",notes = "激活经销商:线索下发后的机率工单开单经销商",required = true,name = "active_dealer_code",example = "",dataType = "String")
    @TableField("active_dealer_code")
    private String activeDealerCode;

    @ApiModelProperty(value = "是否激活:1,是；0,否",notes = "是否激活:1,是；0,否",required = true,name = "is_active",example = "",dataType = "String")
    @TableField("is_active")
    private Integer isActive;

    @ApiModelProperty(value = "客户姓名:有进厂取最近一次送修人信息，没有进厂取店端开票信息",notes = "客户姓名:有进厂取最近一次送修人信息，没有进厂取店端开票信息",required = true,name = "owner_name",example = "",dataType = "String")
    @TableField("owner_name")
    private String ownerName;

    @ApiModelProperty(value = "手机:有进厂取最近一次送修人信息，没有进厂取店端开票信息",notes = "手机:有进厂取最近一次送修人信息，没有进厂取店端开票信息",required = true,name = "mobile",example = "",dataType = "String")
    @TableField("mobile")
    private String mobile;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        LossDataRecordPo that = (LossDataRecordPo) o;
        return Objects.equals(vin, that.vin) &&
                Objects.equals(dealerCode, that.dealerCode) &&
                Objects.equals(orderAt, that.orderAt) &&
                Objects.equals(lastDealerCode, that.lastDealerCode) &&
                Objects.equals(recordAt, that.recordAt) &&
                Objects.equals(invoiceDate, that.invoiceDate) &&
                Objects.equals(activeAt, that.activeAt) &&
                Objects.equals(activeDealerCode, that.activeDealerCode) &&
                Objects.equals(isActive, that.isActive) &&
                Objects.equals(ownerName, that.ownerName) &&
                Objects.equals(mobile, that.mobile);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), vin, dealerCode, orderAt, lastDealerCode, recordAt, invoiceDate, activeAt, activeDealerCode, isActive, ownerName, mobile);
    }
}
