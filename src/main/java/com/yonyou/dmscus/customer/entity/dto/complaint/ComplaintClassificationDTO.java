package com.yonyou.dmscus.customer.entity.dto.complaint;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 客诉工单分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-13
 */
    
public class ComplaintClassificationDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

        
    /**
     * 系统ID
     */
                private String appId;
                
    /**
     * 所有者代码 
     */
                private String ownerCode;
                
    /**
     * 所有者的父组织代码 
     */
                private String ownerParCode;
                
    /**
     * 组织ID
     */
                private Integer orgId;
                
    /**
     * 主键ID
     */
                private Long id;
                
    /**
     * 父级ID
     */
                private Long parentId;
                
    /**
     * 分类编码
     */
                private String cateCode;
                
    /**
     * 分类名称
     */
                private String cateName;
                
    /**
     * 分类层级 0为工单性质 1为投诉单类别一级层 2为投诉单类别二级层 3为投诉单类别三级层
     */
                private String cateLevel;
                
    /**
     * 分类状态 1为新增 2为作废 3为更新
     */
                private String cateStatus;
                
    /**
     * 数据来源 
     */
                private Integer dataSources;
                
    /**
     * 是否删除
     */
                private Boolean isDeleted;
                
    /**
     * 是否有效 
     */
                private Integer isValid;
                
    /**
     * 创建时间
     */

    private Date createdAt;
                
    /**
     * 更新时间
     */

    private Date updatedAt;

    /**
     *多选父类ID
     */
    private  String parentId1;

    public ComplaintClassificationDTO() {
        super();
    }

    public String getParentId1() {
        return parentId1;
    }

    public void setParentId1(String parentId1) {
        this.parentId1 = parentId1;
    }

    public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
            }
                                
    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
            }
                                
    public String getOwnerParCode(){
        return ownerParCode;
    }


    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
            }
                                
    public Integer getOrgId(){
        return orgId;
    }


    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
            }
                                            
    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
            }
                                
    public Long getParentId(){
        return parentId;
    }


    public void  setParentId(Long parentId) {
        this.parentId = parentId;
            }
                                
    public String getCateCode(){
        return cateCode;
    }


    public void  setCateCode(String cateCode) {
        this.cateCode = cateCode;
            }
                                
    public String getCateName(){
        return cateName;
    }


    public void  setCateName(String cateName) {
        this.cateName = cateName;
            }
                                
    public String getCateLevel(){
        return cateLevel;
    }


    public void  setCateLevel(String cateLevel) {
        this.cateLevel = cateLevel;
            }
                                
    public String getCateStatus(){
        return cateStatus;
    }


    public void  setCateStatus(String cateStatus) {
        this.cateStatus = cateStatus;
            }
                                
    public Integer getDataSources(){
        return dataSources;
    }


    public void  setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
            }
                                
    public Integer getIsValid(){
        return isValid;
    }


    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
            }
                                
    public Date getCreatedAt(){
        return createdAt;
    }


    public void  setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
            }
                                
    public Date getUpdatedAt(){
        return updatedAt;
    }


    public void  setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
            }
    
    @Override
    public String toString() {
        return "ComplaintClassificationDTO{" +
                                            "appId=" + appId +
                                                            ", ownerCode=" + ownerCode +
                                                            ", ownerParCode=" + ownerParCode +
                                                            ", orgId=" + orgId +
                                                            ", id=" + id +
                                                            ", parentId=" + parentId +
                                                            ", cateCode=" + cateCode +
                                                            ", cateName=" + cateName +
                                                            ", cateLevel=" + cateLevel +
                                                            ", cateStatus=" + cateStatus +
                                                            ", dataSources=" + dataSources +
                                                            ", isDeleted=" + isDeleted +
                                                            ", isValid=" + isValid +
                                                            ", createdAt=" + createdAt +
                                                            ", updatedAt=" + updatedAt +
                                    "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
