package com.yonyou.dmscus.customer.entity.po.remote;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
@Data
@TableName("tm_company")
@ApiModel(value = "tm_company", description = "tm_company")
public class TmCompany implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("DEALER_ID")
    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("公司代码")
    private String companyCode;

    @ApiModelProperty("公司类型:1005")
    private Integer companyType;

    @ApiModelProperty("公司中文全称")
    private String companyNameCn;

    @ApiModelProperty("公司英文全称")
    private String companyNameEn;

    @ApiModelProperty("公司简称中文")
    private String companyShortNameCn;

    @ApiModelProperty("公司简称英文")
    private String companyShortNameEn;

    @ApiModelProperty("省份")
    private Integer provinceId;

    @ApiModelProperty("省份")
    private String provinceName;

    @ApiModelProperty("城市")
    private Integer cityId;

    @ApiModelProperty("城市")
    private String cityName;

    @ApiModelProperty("所在区县")
    private Integer countyId;

    @ApiModelProperty("所在区县")
    private String countyName;

    @ApiModelProperty("公司地址中文")
    private String addressZh;

    @ApiModelProperty("公司地址英文")
    private String addressEn;

    @ApiModelProperty("组织ID，与组织表对应")
    private Long orgId;

    private String groupCompanyId;

    @ApiModelProperty("集团名称")
    private String groupCompanyName;

    @ApiModelProperty("有效状态:1001")
    private Integer validStatus;

    @ApiModelProperty("联系电话(公司总机）")
    private String phone;

    @ApiModelProperty("邮编")
    private String zipCode;

    @ApiModelProperty("传真")
    private String fax;

    @ApiModelProperty("E_MAIL")
    private String eMail;

    @ApiModelProperty("经销商类型（3S，4S，SC，SR，SC+SR）")
    private Integer dealerType;

    @ApiModelProperty("经销商规模(V150,V300,V600...)")
    private Integer dealerScale;

    @ApiModelProperty("是否批售授权经销商")
    private Integer wholesaleGrant;

    @ApiModelProperty("纬度")
    private Double latitude;

    @ApiModelProperty("经度")
    private Double longitude;

    @ApiModelProperty("公司主页")
    private String companyHomepage;

    @ApiModelProperty("开业日期")
    private LocalDate openDate;

    @ApiModelProperty("建站日期")
    private LocalDate createdDate;

    @ApiModelProperty(" 业务显示：待开业，已开业，停业（1011）")
    private Integer status;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("开店时间")
    private String openTime;

    @ApiModelProperty("关店时间")
    private String closeTime;

    @ApiModelProperty("经销商门头照片")
    private String storefrontPhotoUrl;

    @ApiModelProperty("是否切换到新DMS")
    private Integer isChangeNewDms;

    @ApiModelProperty("是否在沃世界提供送车服务标识")
    private Integer isPositionDelievy;

    private Integer version;

    @ApiModelProperty("CREATED_BY")
    private String createBy;

    @ApiModelProperty("CREATED_AT")
    private LocalDateTime createTime;

    @ApiModelProperty("UPDATED_BY")
    private String updateBy;

    @ApiModelProperty("UPDATED_AT")
    private LocalDateTime updateTime;

    private Integer isDeleted;

    @ApiModelProperty("是否在沃世界提供试驾服务标识")
    private Integer isTestDriver;

    @ApiModelProperty("是否在沃世界提供养修服务标识")
    private Integer isMaintenanceRepair;

    @ApiModelProperty("集团简称")
    private String groupCompanyShortName;

    @ApiModelProperty("是否卫星店")
    private Integer isSatelliteStore;

    @ApiModelProperty("所属经销商")
    private String ownedCompanyName;

    @ApiModelProperty("级别")
    private Integer companyRank;

    @ApiModelProperty("设施类型")
    private Integer facilitiesType;

    @ApiModelProperty("停业日期")
    private LocalDateTime shutDownDate;

    @ApiModelProperty("经销商类型(LDC,VMI)")
    private Integer dealerCompanyType;

    @ApiModelProperty("京东仓经销商")
    private Integer jdDealer;

    @ApiModelProperty("Dealer Tire经销商")
    private Integer dealerTire;

    @ApiModelProperty("维修资质等级")
    private String maintenanceQualificationLevel;

    @ApiModelProperty("使用进厂识别")
    private Integer useIncomingIdentification;

    @ApiModelProperty("使用ipad接车")
    private Integer useIpadPickCar;

    @ApiModelProperty("配备工位摄像头")
    private Integer workStationCamera;

    @ApiModelProperty("电池回收资质")
    private String batteryRecyclingQualification;

    @ApiModelProperty("保险代理人资质")
    private String insuranceBroker;

    @ApiModelProperty("E代驾用户id")
    private String eDesignatedDriverUserId;

    @ApiModelProperty("钣喷资质")
    private String sheetMetalInjectionQualification;

    @ApiModelProperty("抖音号")
    private String flutteringHorn;

    @ApiModelProperty("微信公众号")
    private String wechatAccount;

    @ApiModelProperty("微博账号")
    private String twitterAccount;

    @ApiModelProperty("总经理姓名")
    private String generalManagerName;

    @ApiModelProperty("总经理邮箱")
    private String generalManagerEMail;

    @ApiModelProperty("销售经理姓名")
    private String salesManagerName;

    @ApiModelProperty("销售经理邮箱")
    private String salesManagerEMail;

    @ApiModelProperty("服务经理姓名")
    private String serviceManagerName;

    @ApiModelProperty("服务经理邮箱")
    private String serviceManagerEMail;

    @ApiModelProperty("公司纳税人识别号")
    private String taxNo;

    @ApiModelProperty("公司开户行")
    private String bank;

    @ApiModelProperty("公司账户")
    private String account;

    @ApiModelProperty("生效时间")
    private LocalDateTime effectiveTime;

    @ApiModelProperty("发票邮寄地址")
    private String invoiceMailingAddress;

    @ApiModelProperty("发票邮寄地址(英)")
    private String invoiceMailingAddressEn;

    @ApiModelProperty("财务经理")
    private String financialManager;

    @ApiModelProperty("联系电话(财务)")
    private String financePhone;

    @ApiModelProperty("邮箱(财务)")
    private String financeEMail;

    @ApiModelProperty("整车物流地址")
    private String vehicleLogisticsAddress;

    @ApiModelProperty("整车物流地址(英)")
    private String vehicleLogisticsAddressEn;

    @ApiModelProperty("进口车单证接收地址")
    private String logisticsReceivingAddress;

    @ApiModelProperty("进口车单证接收地址(英)")
    private String logisticsReceivingAddressEn;

    @ApiModelProperty("物流经理")
    private String logisticsManager;

    @ApiModelProperty("联系电话(物流)")
    private String logisticsPhone;

    @ApiModelProperty("邮箱(物流)")
    private String logisticsEMail;

    @ApiModelProperty("账号管理员")
    private String accountManager;

    @ApiModelProperty("联系电话(账号管理员)")
    private String accountManagerPhone;

    @ApiModelProperty("邮箱(账号管理员)")
    private String accountManagerEMail;

    @ApiModelProperty("VISTA账号使用者1")
    private String vistaUser1;

    @ApiModelProperty("CDSID(VISTA账号使用者1)")
    private String vistaUserCdsid1;

    @ApiModelProperty("邮箱(VISTA账号使用者1)")
    private String vistaUserEMail1;

    @ApiModelProperty("VISTA账号使用者2")
    private String vistaUser2;

    @ApiModelProperty("CDSID(VISTA账号使用者2)")
    private String vistaUserCdsid2;

    @ApiModelProperty("邮箱(VISTA账号使用者2)")
    private String vistaUserEMail2;

    @ApiModelProperty("谅解备忘录MOU协议url")
    private String memorandumAgreementUrl;

    @ApiModelProperty("增值税一般纳税人登记表url")
    private String vatRegistrationFormUrl;

    @ApiModelProperty("营业执照url")
    private String businessLicenseUrl;

    @ApiModelProperty("企业信用信息报告url")
    private String enterpriseCreditInformationUrl;

    @ApiModelProperty("是否主营网点")
    private Integer isMainDot;

    @ApiModelProperty("网点代号")
    private String dotNum;

    @ApiModelProperty("网点代码")
    private String dotCode;

    @ApiModelProperty("展厅经理名称")
    private String showroomManagerName;

    @ApiModelProperty("展厅经理邮箱")
    private String showroomManagerEMail;

    @ApiModelProperty("销售热线电话")
    private String salesPhone;

    @ApiModelProperty("售后热线电话")
    private String afterSalePhone;

    @ApiModelProperty("配件仓库")
    private String partWarehouse;

    @ApiModelProperty("仓库发货地址")
    private String warehouseAddress;

    @ApiModelProperty("谅解备忘录MOU协议文件名")
    private String memorandumAgreementName;

    @ApiModelProperty("增值税一般纳税人登记表文件名")
    private String vatRegistrationFormName;

    @ApiModelProperty("营业执照文件名")
    private String businessLicenseName;

    @ApiModelProperty("企业信用信息报告文件名")
    private String enterpriseCreditInformationName;

    @ApiModelProperty("是否晨星守候经销商:10041001是,10041002否")
    private Integer starDealer;

    @ApiModelProperty("晨星守候经销商运行时间默认8:00~20:00/非晨星守护经销商运营时间默认8:00~18:30 开始")
    private String starDealerRunTimeStart;

    @ApiModelProperty("晨星守候经销商运行时间默认8:00~20:00/非晨星守护经销商运营时间默认8:00~18:30 结束")
    private String starDealerRunTimeEnd;

    @ApiModelProperty("是否内部经销商:10041001是,10041002否")
    private Integer isInsideDealer;

    @ApiModelProperty("是否直售经销商:10041001是,10041002否")
    private Integer isDirectDealer;

    @ApiModelProperty("销售是否切换NEWBIE:10041001是,10041002否")
    private Integer isChangeNewbie;

    @ApiModelProperty("电动车维修资质(1:是 0:否)")
    private Integer electricVehicleMaintenanceQualification;

    @ApiModelProperty("是否开启白名单")
    private Integer whiteListSwitch;

    @ApiModelProperty("经销商官方全称")
    private String officialDealerName;

    @ApiModelProperty("京东门店ID")
    private Integer jdStoreId;


}
