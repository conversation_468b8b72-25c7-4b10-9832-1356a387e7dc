package com.yonyou.dmscus.customer.entity.po.invitationautocreate;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import com.yonyou.dmscloud.framework.base.po.BasePO;

import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 车辆邀约任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-07
 */
@TableName("tt_invite_vehicle_task")
@Data
public class InviteVehicleTaskPO extends BasePO<InviteVehicleTaskPO> {

    private static final long serialVersionUID = 1L;


    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;


    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 车架号
     */
    @TableField("vin")
    private String vin;


    /**
     * 车牌号
     */
    @TableField("license_plate_num")
    private String licensePlateNum;

    /**
     * 经销商代码
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 车主名称
     */
    @TableField("name")
    private String name;

    /**
     * 车主电话
     */
    @TableField("tel")
    private String tel;

    /**
     * 车主年龄
     */
    @TableField("age")
    private String age;

    /**
     * 车主性别
     */
    @TableField("sex")
    private String sex;

    /**
     * 车型
     */
    @TableField("model")
    private String model;

    /**
     * 日均里程
     */
    @TableField("daily_mileage")
    private Double dailyMileage;

    /**
     * 建议进厂日期
     */
    @TableField("advise_in_date")
    private Date adviseInDate;

    /**
     * 建议进厂日期更新时间
     */
    @TableField("advise_in_date_update_time")
    private Date adviseInDateUpdateTime;

    /**
     * 邀约类型
     */
    @TableField("invite_type")
    private Integer inviteType;

    /**
     * 提前N天邀约
     */
    @TableField("day_in_advance")
    private Integer dayInAdvance;

    /**
     * 再提醒间隔（月）
     */
    @TableField("remind_interval")
    private Integer remindInterval;


    /**
     * 超时关闭时间间隔（月）
     */
    @TableField("close_interval")
    private Integer closeInterval;

    /**
     * 超时关闭次数）
     */
    @TableField("close_times")
    private Integer closeTimes;

    /**
     * 邀约规则类型(预留)：1 首保、定保、保险、客户流失(对应invite_rule表)，2 易损件、项目(对应invite_rule表)
     */
    @TableField("invite_rule_type")
    private Integer inviteRuleType;

    /**
     * 邀约规则ID(预留)，用于计算再次邀约时间。（关联invite_rule或invite_part_item_rule中id）
     */
    @TableField("invite_rule_id")
    private Long inviteRuleId;

    /**
     * 是否已生成邀约线索：1 已生成，0 未生成 ,2不再生成,3跟进失败再次待生成
     */
    @TableField("is_create_invite")
    private Integer isCreateInvite;

    /**
     * 邀约ID （关联invite_vehicle_record中id）
     */
    @TableField("invite_id")
    private Long inviteId;

    /**
     * 生成邀约时间
     */
    @TableField("create_invite_time")
    private Date createInviteTime;

    /**
     * 跟进状态
     */
    @TableField("follow_status")
    private Integer followStatus;

    /**
     * 再次邀约时间
     */
    @TableField("reinvite_time")
    private Date reinviteTime;

    /**
     * 失效原因
     */
    @TableField("invalid_reason")
    private String invalidReason;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;

    @TableField("last_change_date")
    private Date lastChangeDate;


    /**
     * 任务基准时间
     */
    @TableField("invite_time")
    private Date inviteTime;

    /**
     * 易损件code
     */
    @TableField("item_code")
    private String itemCode;

    /**
     *易损件名称
     */
    @TableField("item_name")
    private String itemName;

    /**
     *易损件类型
     */
    @TableField("item_type")
    private Integer itemType;

    /**
     *建议入厂里程
     */
    @TableField("advise_in_mileage")
    private Integer adviseInMileage;


    /**
     *基准里程
     */
    @TableField("out_mileage ")
    private Integer outMileage;


    /**
     * qb号
     */
    @TableField("qb_number")
    private String qbNumber;


    /**
     * 规则修改记录id
     */
    @TableField("changed_id")
    private Long changedId;

    /**
     * 易损件规则 里程间隔
     */
    @TableField(exist = false)
    private Integer mileageInterval;

    /**
     * 易损件规则 日期间隔
     */
    @TableField(exist = false)
    private Integer dateInterval;


    /**
     * 是否voc 车辆
     */
    @TableField(exist = false)
    private Integer isVoc;

    /**
     * 日均行驶里程
     */
    @TableField(exist = false)
    private Double dailyAverageMileage;


    /**
     * 当前voc里程
     */
    @TableField(exist = false)
    private Integer vocMileage;



    /**
     * 上次维修日期
     */
    @TableField(exist = false)
    private Date lastMaintainDate;


    /**
     * 上次保养日期
     */
    @TableField(exist = false)
    private Date lastMaintenanceDate;

    /**
     * 线索来源
     */
    @TableField(exist = false)
    private Integer sourceType;


    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
