package com.yonyou.dmscus.customer.entity.dto.complaint.AcceptComlaintData;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 接受400下发客诉单
 */
@Data
public class AcceptNewComplaintDTO {
    /**
     * 投诉ID（投诉编号）
     */
    private String complaintId;

    /**
     * 投诉类型
     */
    private String type;

    /**
     * 工单性质
     */
    private String workOrderNature;
    /**
     * 工单分类
     */
    private String workOrderClassification;

    /**
     * 投诉来源
     */
    private String source;

    /**
     * 工单状态
     */
    private Integer workOrderStatus;

    /**
     * 服务承诺
     */
    private String serviceCommitment;

    /**
     * 来电时间/投诉日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss HH:mm:ss" )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date callTime;

    /**
     * 来电客户姓名/投诉人姓名
     */
    private String callName;

    /**
     * 来电电话/投诉人电话
     */
    private String callTel;

    /**
     * 车主姓名
     */
    private String name;

    /**
     * 车牌号
     */
    private String licensePlateNum;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 购车时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date buyTime;

    /**
     * 车型
     */
    private String model;

    /**
     * 年款
     */
    private String modelYear;

    /**
     * 购买经销商
     */
    private String buyDealerName;

    /**
     * 购买经销商代码
     */
    private String buyDealerCode;

    /**
     * 处理经销商
     */
    private String dealerName;

    /**
     * 处理经销商代码
     */
    private String dealerCode;

    /**
     * 里程
     */
    private Integer mileage;

    /**
     * 回复联系人姓名
     */
    private String replyName;

    /**
     * 回复联系人手机1
     */
    private String replyTel;

    /**
     * 回复联系人手机2
     */
    private String replyTel2;

    /**
     * 投诉主题
     */
    private String subject;

    /**
     * 问题描述
     */
    private String problem;

    /**
     * 坐席主管说明(写入follow表中 作为跟进信息)
     */
    private String illustrate;

    /**
     *期望经销商联系时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private  Date hopeReplyTime;

    /**
     * 投诉单类别一级层
     */
    private String category1;

    /**
     * 投诉单类别二级层
     */
    private String category2;

    /**
     * 投诉单类别三级层
     */
    private String category3;

    /**
     * CC部位
     */
    private String ccPart;

    /**
     * CC细分部位
     */
    private String ccSubdivisionPart;

    /**
     * CC问题
     */
    private String ccProblem;

    /**
     * CC要求
     */
    private String ccRequirement;

    /**
     * 重要等级
     */
    private String importanceLevel;



}
