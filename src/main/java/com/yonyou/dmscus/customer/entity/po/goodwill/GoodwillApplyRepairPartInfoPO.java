package com.yonyou.dmscus.customer.entity.po.goodwill;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善预申请子表——维修零配件信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
@TableName("tt_goodwill_apply_repair_part_info")
public class GoodwillApplyRepairPartInfoPO extends BasePO<GoodwillApplyRepairPartInfoPO> {

	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	@TableField("APP_ID")
	private String appId;

	/**
	 * 所有者代码
	 */
	@TableField("OWNER_CODE")
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	@TableField("OWNER_PAR_CODE")
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	@TableField("ORG_ID")
	private Integer orgId;

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 亲善预申请表id
	 */
	@TableField("goodwill_apply_id")
	private Long goodwillApplyId;

	/**
	 * 工单号
	 */
	@TableField("repair_order_no")
	private String repairOrderNo;

	/**
	 * 零件号
	 */
	@TableField("part_no")
	private String partNo;

	/**
	 * 零件名称
	 */
	@TableField("part_name")
	private String partName;

	/**
	 * 数量
	 */
	@TableField("quantity")
	private BigDecimal quantity;

	/**
	 * 订货日期
	 */
	@TableField("order_date")
	private Date orderDate;

	/**
	 * 到货日期
	 */
	@TableField("arrival_date")
	private Date arrivalDate;

	/**
	 * 备注
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 是否有效
	 */
	@TableField("is_valid")
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	@TableField("is_deleted")
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	@TableField("created_at")
	private Date createdAt;

	/**
	 * 修改时间
	 */
	@TableField("updated_at")
	private Date updatedAt;

	public GoodwillApplyRepairPartInfoPO() {
		super();
	}

	@Override
	public String getAppId() {
		return appId;
	}

	@Override
	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	@Override
	public String getOwnerParCode() {
		return ownerParCode;
	}

	@Override
	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getGoodwillApplyId() {
		return goodwillApplyId;
	}

	public void setGoodwillApplyId(Long goodwillApplyId) {
		this.goodwillApplyId = goodwillApplyId;
	}

	public String getRepairOrderNo() {
		return repairOrderNo;
	}

	public void setRepairOrderNo(String repairOrderNo) {
		this.repairOrderNo = repairOrderNo;
	}

	public String getPartNo() {
		return partNo;
	}

	public void setPartNo(String partNo) {
		this.partNo = partNo;
	}

	public String getPartName() {
		return partName;
	}

	public void setPartName(String partName) {
		this.partName = partName;
	}

	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Date getOrderDate() {
		return orderDate;
	}

	public void setOrderDate(Date orderDate) {
		this.orderDate = orderDate;
	}

	public Date getArrivalDate() {
		return arrivalDate;
	}

	public void setArrivalDate(Date arrivalDate) {
		this.arrivalDate = arrivalDate;
	}

	@Override
	public Date getCreatedAt() {
		return createdAt;
	}

	@Override
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Override
	public Date getUpdatedAt() {
		return updatedAt;
	}

	@Override
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	protected Serializable pkVal() {
		return this.id;
	}

	@Override
	public String toString() {
		return "GoodwillApplyRepairPartInfoPO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", goodwillApplyId=" + goodwillApplyId
				+ ", repairOrderNo=" + repairOrderNo + ", partNo=" + partNo + ", partName=" + partName + ", quantity="
				+ quantity + ", orderDate=" + orderDate + ", arrivalDate=" + arrivalDate + ", remark=" + remark
				+ ", isValid=" + isValid + ", isDeleted=" + isDeleted + ", createdAt=" + createdAt + ", updatedAt="
				+ updatedAt + "}";
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dtoClass
	 *            需要进行转换的dtoClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
		return super.transDtoToPo(dtoClass);
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dto
	 *            需要进行转换的dto
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> void transPoToDto(T dto) {
		BeanMapperUtil.copyProperties(this, dto, "id");
	}

}
