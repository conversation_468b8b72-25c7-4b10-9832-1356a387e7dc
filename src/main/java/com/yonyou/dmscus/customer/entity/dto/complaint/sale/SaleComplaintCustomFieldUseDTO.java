package com.yonyou.dmscus.customer.entity.dto.complaint.sale;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 客户投诉自定义字段使用表 每个人不同对应不同
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
    
public class SaleComplaintCustomFieldUseDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

        
    /**
     * 系统ID
     */
                private String appId;
                
    /**
     * 所有者代码 
     */
                private String ownerCode;
                
    /**
     * 所有者的父组织代码 
     */
                private String ownerParCode;
                
    /**
     * 组织ID
     */
                private Integer orgId;
                
    /**
     * 主键ID
     */
                private Long id;
                
    /**
     * 自定义表ID
     */
                private Long customFieldId;
                
    /**
     * 表名
     */
                private String tableName;
                
    /**
     * 字段名
     */
                private String fieldName;
                
    /**
     * 字段描述
     */
                private String fieldDescribe;
                
    /**
     * 是否查询 1 是 0 否
     */
                private Boolean isQuery;
                
    /**
     * 是否排序 1 是 0 否
     */
                private Integer isSort;
                
    /**
     * 排序类型 存 ASC/DESC
     */
                private String sortType;
                
    /**
     * 使用人
     */
                private Long userId;
                
    /**
     * 数据来源 
     */
                private Integer dataSources;
                
    /**
     * 是否删除
     */
                private Integer isDeleted;
                
    /**
     * 是否有效 
     */
                private Integer isValid;
                
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createdAt;
                
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updatedAt;
            
    public SaleComplaintCustomFieldUseDTO() {
        super();
    }

                                
    public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
            }
                                
    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
            }
                                
    public String getOwnerParCode(){
        return ownerParCode;
    }


    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
            }
                                
    public Integer getOrgId(){
        return orgId;
    }


    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
            }
                                            
    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
            }
                                
    public Long getCustomFieldId(){
        return customFieldId;
    }


    public void  setCustomFieldId(Long customFieldId) {
        this.customFieldId = customFieldId;
            }
                                
    public String getTableName(){
        return tableName;
    }


    public void  setTableName(String tableName) {
        this.tableName = tableName;
            }
                                
    public String getFieldName(){
        return fieldName;
    }


    public void  setFieldName(String fieldName) {
        this.fieldName = fieldName;
            }
                                
    public String getFieldDescribe(){
        return fieldDescribe;
    }


    public void  setFieldDescribe(String fieldDescribe) {
        this.fieldDescribe = fieldDescribe;
            }
                    
    public Boolean getIsQuery(){
        return isQuery;
    }


    public void  setIsQuery(Boolean isQuery) {
        this.isQuery = isQuery;
            }
                                
    public Integer getIsSort(){
        return isSort;
    }


    public void  setIsSort(Integer isSort) {
        this.isSort = isSort;
            }
                                
    public String getSortType(){
        return sortType;
    }


    public void  setSortType(String sortType) {
        this.sortType = sortType;
            }
                                
    public Long getUserId(){
        return userId;
    }


    public void  setUserId(Long userId) {
        this.userId = userId;
            }
                                
    public Integer getDataSources(){
        return dataSources;
    }


    public void  setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
            }
                                
    public Integer getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
            }
                                
    public Integer getIsValid(){
        return isValid;
    }


    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
            }
                                
    public Date getCreatedAt(){
        return createdAt;
    }


    public void  setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
            }
                                
    public Date getUpdatedAt(){
        return updatedAt;
    }


    public void  setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
            }
    
    @Override
    public String toString() {
        return "SaleComplaintCustomFieldUseDTO{" +
                                            "appId=" + appId +
                                                            ", ownerCode=" + ownerCode +
                                                            ", ownerParCode=" + ownerParCode +
                                                            ", orgId=" + orgId +
                                                            ", id=" + id +
                                                            ", customFieldId=" + customFieldId +
                                                            ", tableName=" + tableName +
                                                            ", fieldName=" + fieldName +
                                                            ", fieldDescribe=" + fieldDescribe +
                                                            ", isQuery=" + isQuery +
                                                            ", isSort=" + isSort +
                                                            ", sortType=" + sortType +
                                                            ", userId=" + userId +
                                                            ", dataSources=" + dataSources +
                                                            ", isDeleted=" + isDeleted +
                                                            ", isValid=" + isValid +
                                                            ", createdAt=" + createdAt +
                                                            ", updatedAt=" + updatedAt +
                                    "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
