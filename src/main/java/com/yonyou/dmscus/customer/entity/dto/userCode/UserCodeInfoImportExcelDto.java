package com.yonyou.dmscus.customer.entity.dto.userCode;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.service.excel.ExcelColumnDefine;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.function.domains.dto.DataImportDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: BiReportUserInfo
 * @projectvalue server2
 * @description: TODO
 * @date 2022/7/1915:14
 */
@Data
@ApiModel("app权限数据")
public class UserCodeInfoImportExcelDto extends DataImportDto {


    /**
     * 经销商/,集团
     */
     @ApiModelProperty(value ="经销商/集团",example ="经销商")
     @ExcelColumnDefine(value = 1)
    private String belonging;

    /**
     * 经销商代码/所属集团
     */
     @ApiModelProperty(value ="经销商代码/所属集团",example ="SHJ")
     @ExcelColumnDefine(value = 2)
    private String belongingName;

    /**
     * 姓名
     */
     @ApiModelProperty(value ="姓名",example ="姓名")
     @ExcelColumnDefine(value = 3)
    private String employeeName;


    /**
     * 员工编号
     */
     @ApiModelProperty(value ="员工编号",example ="SDSSSS0001")
     @ExcelColumnDefine(value = 4)
    private String employeeNo;

    /**
     * 岗位
     */
     @ApiModelProperty(value ="岗位",example ="服务经理")
     @ExcelColumnDefine(value = 5)
    private String position;


    /**
     *newbie登录账号
     */
     @ApiModelProperty(value ="newbie登录账号",example ="test")
     @ExcelColumnDefine(value = 6)
    private String userCode;


    /**
     *手机号
     */
     @ApiModelProperty(value ="手机号",example ="13000000000")
     @ExcelColumnDefine(value = 7)
    private String mobile;


    /**
     *邮箱
     */
     @ApiModelProperty(value ="邮箱",example ="<EMAIL>")
     @ExcelColumnDefine(value = 8)
    private String email;


    /**
     * 是否有效
     */
     @ApiModelProperty(value ="是否有效是：10041001，否：10041002",example ="是/否")
     @ExcelColumnDefine(value = 9)
    private String isValid;


}
