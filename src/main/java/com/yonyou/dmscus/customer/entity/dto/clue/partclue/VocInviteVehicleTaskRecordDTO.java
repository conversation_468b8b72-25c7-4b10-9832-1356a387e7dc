package com.yonyou.dmscus.customer.entity.dto.clue.partclue;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 车辆邀约记录扩展表
 */
@ApiModel("车辆邀约记录扩展表")
@Data
public class VocInviteVehicleTaskRecordDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    // 线索类型
    @ApiModelProperty(name = "inviteType", dataType = "Integer")
    private Integer inviteType;

    // 线索状态
    @ApiModelProperty(name = "orderStatus", dataType = "Integer")
    private Integer orderStatus;

    // liteCrm id
    @ApiModelProperty(name = "icmId", dataType = "Integer")
    private Integer icmId;

    // 卡卷模版id
    @ApiModelProperty(name = "couponId", dataType = "Long")
    private Long couponId;

    // 卡卷模版CODE
    @ApiModelProperty(name = "couponCode", dataType = "String")
    private String couponCode;

    // 卡卷模版名称
    @ApiModelProperty(value = "coupon_name",name = "couponName", dataType = "String")
    private String couponName;

    // 卡劵id
    @ApiModelProperty(value = "detail_id",name = "detailId", dataType = "Long")
    private Long detailId;

    // 核销经销商
    @ApiModelProperty(value = "exchange_owner_code",name = "exchangeOwnerCode", dataType = "String")
    private String exchangeOwnerCode;

    // 核销工单号
    @ApiModelProperty(value = "exchange_ro_no",name = "exchangeRoNo", dataType = "String")
    private String exchangeRoNo;

    // 卡劵状态
    @ApiModelProperty(value = "coupon_state",name = "couponState", dataType = "Integer")
    private Integer couponState;

    // 卡劵状态变更时间
    @ApiModelProperty(value = "coupon_state_time",name = "couponStateTime", dataType = "String")
    private String couponStateTime;

    // 是否是反结算（参见枚举：1004，10041001  = 反结算 10041002  = 非反结算）
    @ApiModelProperty(value = "is_return",name = "isReturn", dataType = "Integer")
    private Integer isReturn;
}
