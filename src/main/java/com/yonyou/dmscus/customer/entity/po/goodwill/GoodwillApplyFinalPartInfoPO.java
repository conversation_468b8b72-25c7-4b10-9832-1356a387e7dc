package com.yonyou.dmscus.customer.entity.po.goodwill;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善预申请最终解决方案配件信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
@TableName("tt_goodwill_apply_final_part_info")
public class GoodwillApplyFinalPartInfoPO extends BasePO<GoodwillApplyFinalPartInfoPO> {

	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	@TableField("APP_ID")
	private String appId;

	/**
	 * 所有者代码
	 */
	@TableField("OWNER_CODE")
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	@TableField("OWNER_PAR_CODE")
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	@TableField("ORG_ID")
	private Integer orgId;

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 亲善单id
	 */
	@TableField("goodwill_apply_id")
	private Long goodwillApplyId;

	/**
	 * 亲善类型
	 */
	@TableField("goodwill_type")
	private Integer goodwillType;

	/**
	 * 零件代码
	 */
	@TableField("part_code")
	private String partCode;

	/**
	 * 零件名称
	 */
	@TableField("part_name")
	private String partName;

	/**
	 * 单价（不含税）
	 */
	@TableField("unit_price_no_tax")
	private BigDecimal unitPriceNoTax;

	/**
	 * 税率
	 */
	@TableField("rate")
	private String rate;

	/**
	 * 单价（含税）
	 */
	@TableField("unit_price_tax")
	private BigDecimal unitPriceTax;

	/**
	 * 数量
	 */
	@TableField("quantity")
	private BigDecimal quantity;

	/**
	 * 总价（含税）
	 */
	@TableField("amount_tax")
	private BigDecimal amountTax;

	/**
	 * 支持比例
	 */
	@TableField("support_proportion")
	private BigDecimal supportProportion;

	/**
	 * 亲善金额（含税）
	 */
	@TableField("goodwill_amount_tax")
	private BigDecimal goodwillAmountTax;

	/**
	 * 是否有效
	 */
	@TableField("is_valid")
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	@TableField("is_deleted")
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	@TableField("created_at")
	private Date createdAt;

	/**
	 * 修改时间
	 */
	@TableField("updated_at")
	private Date updatedAt;

	public GoodwillApplyFinalPartInfoPO() {
		super();
	}

	@Override
	public String getAppId() {
		return appId;
	}

	@Override
	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	@Override
	public String getOwnerParCode() {
		return ownerParCode;
	}

	@Override
	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getGoodwillApplyId() {
		return goodwillApplyId;
	}

	public void setGoodwillApplyId(Long goodwillApplyId) {
		this.goodwillApplyId = goodwillApplyId;
	}

	public Integer getGoodwillType() {
		return goodwillType;
	}

	public void setGoodwillType(Integer goodwillType) {
		this.goodwillType = goodwillType;
	}

	public String getPartCode() {
		return partCode;
	}

	public void setPartCode(String partCode) {
		this.partCode = partCode;
	}

	public String getPartName() {
		return partName;
	}

	public void setPartName(String partName) {
		this.partName = partName;
	}

	public BigDecimal getUnitPriceNoTax() {
		return unitPriceNoTax;
	}

	public void setUnitPriceNoTax(BigDecimal unitPriceNoTax) {
		this.unitPriceNoTax = unitPriceNoTax;
	}

	public String getRate() {
		return rate;
	}

	public void setRate(String rate) {
		this.rate = rate;
	}

	public BigDecimal getUnitPriceTax() {
		return unitPriceTax;
	}

	public void setUnitPriceTax(BigDecimal unitPriceTax) {
		this.unitPriceTax = unitPriceTax;
	}

	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	public BigDecimal getAmountTax() {
		return amountTax;
	}

	public void setAmountTax(BigDecimal amountTax) {
		this.amountTax = amountTax;
	}

	public BigDecimal getSupportProportion() {
		return supportProportion;
	}

	public void setSupportProportion(BigDecimal supportProportion) {
		this.supportProportion = supportProportion;
	}

	public BigDecimal getGoodwillAmountTax() {
		return goodwillAmountTax;
	}

	public void setGoodwillAmountTax(BigDecimal goodwillAmountTax) {
		this.goodwillAmountTax = goodwillAmountTax;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	@Override
	public Date getCreatedAt() {
		return createdAt;
	}

	@Override
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Override
	public Date getUpdatedAt() {
		return updatedAt;
	}

	@Override
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	protected Serializable pkVal() {
		return this.id;
	}

	@Override
	public String toString() {
		return "GoodwillApplyFinalPartInfoPO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", goodwillApplyId=" + goodwillApplyId
				+ ", goodwillType=" + goodwillType + ", partCode=" + partCode + ", partName=" + partName
				+ ", unitPriceNoTax=" + unitPriceNoTax + ", rate=" + rate + ", unitPriceTax=" + unitPriceTax
				+ ", quantity=" + quantity + ", amountTax=" + amountTax + ", supportProportion=" + supportProportion
				+ ", goodwillAmountTax=" + goodwillAmountTax + ", isValid=" + isValid + ", isDeleted=" + isDeleted
				+ ", createdAt=" + createdAt + ", updatedAt=" + updatedAt + "}";
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dtoClass
	 *            需要进行转换的dtoClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
		return super.transDtoToPo(dtoClass);
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dto
	 *            需要进行转换的dto
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> void transPoToDto(T dto) {
		BeanMapperUtil.copyProperties(this, dto, "id");
	}

}
