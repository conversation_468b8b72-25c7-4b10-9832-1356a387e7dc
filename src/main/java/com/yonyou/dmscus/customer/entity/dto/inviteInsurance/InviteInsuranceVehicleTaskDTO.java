package com.yonyou.dmscus.customer.entity.dto.inviteInsurance;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 车辆邀约续保任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */

@Data
@ToString
public class InviteInsuranceVehicleTaskDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车牌号
     */
    private String licensePlateNum;

    /**
     * 经销商代码
     */
    private String dealerCode;

    /**
     * 车主名称
     */
    private String name;

    /**
     * 车主电话
     */
    private String tel;

    /**
     * 车主年龄
     */
    private String age;

    /**
     * 车主性别
     */
    private String sex;
    private String model;

    /**
     * 日均里程
     */
    private Integer dailyMileage;

    /**
     * 建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date adviseInDate;

    /**
     * 建议进厂日期更新时间
     */
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    private LocalDateTime adviseInDateUpdateTime;

    /**
     * 邀约类型
     */
    private Integer inviteType;

    /**
     * 提前N天邀约
     */
    private Integer dayInAdvance;

    /**
     * 再提醒间隔（月）
     */
    private Integer remindInterval;

    /**
     * 邀约规则类型(预留)：1 首保、定保、保险、客户流失(对应invite_rule表)，2 易损件、项目(对应invite_rule表)
     */
    private Boolean inviteRuleType;

    /**
     * 邀约规则ID(预留)，用于计算再次邀约时间。（关联invite_rule或invite_part_item_rule中id）
     */
    private Long inviteRuleId;

    /**
     * 是否已生成邀约线索：1 已生成，0 未生成 ,2不再生成,3跟进失败再次待生成
     */
    private Integer isCreateInvite;

    /**
     * 邀约ID （关联invite_vehicle_record中id）
     */
    private Long inviteId;

    /**
     * 建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createInviteTime;

    /**
     * 跟进状态
     */
    private Integer followStatus;

    /**
     * 建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date reinviteTime;

    /**
     * 失效原因
     */
    private String invalidReason;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    private LocalDateTime updatedAt;

    /**
     * 续保客户类型(81761001 新保客户  81761002 新转续  81761003  续转续   81761004 在修不在保)
     */
    private Integer insuranceType;

    /**
     *保险投保单(tt_insurance_bill)表主键id'
     */
    private Long insuranceBillId;

    /**
     *线索类型：1:交强险   2:商业险'
     */
    private Integer clueType;

    /**
     *原线索记录新生的投保单号'
     */
    private String newInsureNo;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date lastChangeDate;

    /**
     * 计算 建议进厂时间 的基准时间 ,用于日均里程更新时重新计算建议进厂时间
     */
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    private LocalDateTime inviteTime;

    /**
     * 类型：易损件、项目
     */
    private Integer itemType;

    /**
     * 零件、维修项目编号
     */
    private String itemCode;

    /**
     * 零件、维修项目名称
     */
    private String itemName;

    /**
     * QB号
     */
    private String qbNumber;

    /**
     * 超时关闭时间
     */
    private Integer closeInterval;

    /**
     * 超时关闭次数
     */
    private Integer closeTimes;

    /**
     * 建议入厂里程
     */
    private Integer adviseInMileage;

    public InviteInsuranceVehicleTaskDTO() {
        super();
    }


    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
