package com.yonyou.dmscus.customer.entity.po.inviteRule;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

/**
 * <p>
 * 邀约易损件和项目规则变更记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-18
 */
@Data
@TableName("tt_invite_part_item_rule_changed_record")
public class InvitePartItemRuleChangedRecordPO extends BasePO<InvitePartItemRuleChangedRecordPO> {

    private static final long serialVersionUID = 1L;


    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;


    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经销商代码（VCDC：代表厂端，经销商代码：代表各自经销商）
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 类型：易损件、项目
     */
    @TableField("type")
    private Integer type;

    /**
     * 零件、维修项目编号
     */
    @TableField("code")
    private String code;

    /**
     * 零件、维修项目名称
     */
    @TableField("name")
    private String name;

    /**
     * 再提醒间隔（月）
     */
    @TableField("remind_interval")
    private Integer remindInterval;

    /**
     * 间隔里程（Km）
     */
    @TableField("mileage_interval")
    private Integer mileageInterval;

    /**
     * 间隔日期（月）
     */
    @TableField("date_interval")
    private Integer dateInterval;

    /**
     * 车架号 必须输入17位，---K---45—3----
     */
    @TableField("vin")
    private String vin;

    /**
     * 车型编码
     */
    @TableField("model_code")
    private String modelCode;

    /**
     * 年款
     */
    @TableField("model_year")
    private String modelYear;

    /**
     * 发动机编码
     */
    @TableField("engine_code")
    private String engineCode;

    /**
     * 变速箱编码
     */
    @TableField("gearbox_code")
    private String gearboxCode;

    /**
     * 规则关系：and 和，or 或
     */
    @TableField("rule_relationship")
    private Integer ruleRelationship;

    /**
     * 是否启用：1 启用，0 不启用
     */
    @TableField("is_use")
    private Integer isUse;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;


    /**
     * 修改后未处理 1:是0,否
     */
    @TableField("update_is_execute")
    private Boolean updateIsExecute;

    /**
     * 原再提醒间隔（月）
     */
    @TableField("last_remind_interval")
    private Integer lastRemindInterval;

    /**
     * 原间隔里程（Km）
     */
    @TableField("last_mileage_interval")
    private Integer lastMileageInterval;

    /**
     * 原间隔日期（月）
     */
    @TableField("last_date_interval")
    private Integer lastDateInterval;

    /**
     * 原车架号 必须输入17位，---K---45—3----
     */
    @TableField("last_vin")
    private String lastVin;

    /**
     * 原车型编码
     */
    @TableField("last_model_code")
    private String lastModelCode;

    /**
     * 原年款
     */
    @TableField("last_model_year")
    private String lastModelYear;

    /**
     * 原发动机编码
     */
    @TableField("last_engine_code")
    private String lastEngineCode;

    /**
     * 原变速箱编码
     */
    @TableField("last_gearbox_code")
    private String lastGearboxCode;

    /**
     * 原规则关系：and 和，or 或
     */
    @TableField("last_rule_relationship")
    private Integer lastRuleRelationship;

    /**
     * 原是否启用：1 启用，0 不启用
     */
    @TableField("last_is_use")
    private Integer lastIsUse;


    /**
     * 备注
     */
    @TableField("remark")
    private String remark;


    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
