package com.yonyou.dmscus.customer.entity.dto.inviteInsurance;


import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
public class InviteInsuranceFollowParamsDTO {


    private List<Integer> inviteType;
    private String planFollowDateStart;
    private String planFollowDateEnd;
    private String licensePlateNum;
    private String vin;
    private String actualFollowDateStart;
    private String actualFollowDateEnd;
    private String name;
    private Integer isBook;
    private String adviseInDateStart;
    private String adviseInDateEnd;
    private List<Integer> followStatus;
    private List<Integer> leaveIds;
    private Integer orderStatus;
    private String saName;
    private  String saId;
    private String createdAtStart;
    private String createdAtEnd;
    private Integer isself;
    private Long currentPage;
    private Long pageSize;
    /**
     * 大区 查询条件
     */
    private String largeAreaId;

    /**
     * 小区 查询条件
     */
    private String areaId;
    /**
     * 经销商代码
     */
    private String dealerCode;



}
