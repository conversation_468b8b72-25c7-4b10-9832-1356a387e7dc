package com.yonyou.dmscus.customer.entity.dto.clue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * description 客户信息参数
 *
 * <AUTHOR>
 * @date 2023/8/21 17:12
 */
@Data
@ApiModel(description = "客户信息参数")
public class ContactInfoDTO {
    /**
     * 客户类型(车主、绑车车主、售后客档、开票车主）
     */
    @ApiModelProperty(value = "客户类型", required = true, example = "1")
    private String typeName;
    /**
     * 客户类型编号
     */
    @ApiModelProperty(value = "客户类型编号", required = true, example = "1000")
    private String typeCode;
    /**
     * 客户姓名
     */
    @ApiModelProperty(value = "客户姓名", required = true, example = "AA")
    private String customerName;
    /**
     * 客户手机号
     */
    @ApiModelProperty(value = "客户手机号", required = true, example = "188811123654")
    private String customerMobile;
    /**
     * 客户性别
     */
    @ApiModelProperty(value = "客户性别", example = "10003")
    private String gender;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priority;
    /**
     * 维修次数
     */
    @ApiModelProperty(value = "维修次数", required = true, example = "100")
    private int repairNum;
    /**
     * 维修时间
     */
    @ApiModelProperty(value = "维修时间", required = true, example = "2023-09-01 00:00:00")
    private Date balanceTime;
}
