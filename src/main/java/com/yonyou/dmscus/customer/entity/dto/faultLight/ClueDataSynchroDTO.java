package com.yonyou.dmscus.customer.entity.dto.faultLight;

import lombok.Data;

import java.io.Serializable;

/**线索同步接口入参*/
@Data
public class ClueDataSynchroDTO implements Serializable {

    private static final long serialVersionUID = 6995570272455274526L;

    /**LiteCrm线索id*/
    private Long id;

    /**原始线索id（湖仓id)*/
    private String sourceClueId;

    /**经销商CODE*/
    private String dealerCode;

    /**经销商名称*/
    private String dealerName;

    /**车辆Vin码*/
    private String vehicleVin;

    /**是否推荐经销商*/
    private Boolean recommendDealerFlag;

    /**线索收集时间*/
    private String leadsReceiveTime;

    /**故障报警警信息*/
    private WarningInfoDTO warningInfo;

    /**客户信息*/
    private ContactInfoDTO contactInfo;

    /**外呼信息*/
    private CallInfoDTO callInfo;
}
