package com.yonyou.dmscus.customer.entity.po.qb;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/8/3 0001
 */
@TableName("tt_invite_qb_vehicle_import")
@Data
public class InviteQBVehicleImportPO extends BasePO<InviteQBVehicleImportPO> {

    @TableField("app_id")
    private String appId;

    @TableField("owner_code")
    private String ownerCode;

    @TableField("owner_par_code")
    private String ownerParCode;

    @TableField("org_id")
    private Long orgId;

    @TableField("vin")
    private String vin;

    @TableField("dealer_code")
    private String dealerCode;

    @TableField("qb_number")
    private String qbNumber;

    @TableField("is_error")
    private Integer isError;

    @TableField("error_msg")
    private String errorMsg;

    @TableField("line_number")
    private Integer lineNumber;

    @TableField("created_by")
    private String createdBy;

    
    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
