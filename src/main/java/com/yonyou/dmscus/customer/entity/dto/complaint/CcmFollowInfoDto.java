package com.yonyou.dmscus.customer.entity.dto.complaint;

import java.util.List;

/**
 * <AUTHOR>
 */
public class CcmFollowInfoDto {
    private Integer advise;
    private List ccMainReason;
    private List ccmPart;
    private List ccmSubdivisionPart;
    private List ccResult;
    private String ccmSubject;
    private  Integer classification1;
    private  String classification2;
    private  String classification3;
    private  String classification4;
    private  String classification5;
    private  String classification6;
    private Long complaintInfoId;
    private String followContent;
    private Integer ccmNotPublish;
    private String keyword;
    private  String planFollowTime;
    private  String status;
    /**
     * 质量部分类1
     */
    private Integer qualityClassification1;

    /**
     * 质量部分类2
     */
    private Integer qualityClassification2;
    /**
     * 质量部分类3
     */
    private String qualityClassification3;
    /**
     * 质量部分类4
     */
    private Integer qualityClassification4;
    /**
     * 故障分类
     */
    private String faultClassification;
    /**
     * 备注1
     */
    private String remark1;
    /**
     * 备注2
     */
    private String remark2;
    /**
     * 备注3
     */
    private String remark3;
    /**
     * 备注4
     */
    private String remark4;

    public Integer getQualityClassification1() {
        return qualityClassification1;
    }

    public void setQualityClassification1(Integer qualityClassification1) {
        this.qualityClassification1 = qualityClassification1;
    }

    public Integer getQualityClassification2() {
        return qualityClassification2;
    }

    public void setQualityClassification2(Integer qualityClassification2) {
        this.qualityClassification2 = qualityClassification2;
    }

    public String getQualityClassification3() {
        return qualityClassification3;
    }

    public void setQualityClassification3(String qualityClassification3) {
        this.qualityClassification3 = qualityClassification3;
    }

    public Integer getQualityClassification4() {
        return qualityClassification4;
    }

    public void setQualityClassification4(Integer qualityClassification4) {
        this.qualityClassification4 = qualityClassification4;
    }

    public String getFaultClassification() {
        return faultClassification;
    }

    public void setFaultClassification(String faultClassification) {
        this.faultClassification = faultClassification;
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1;
    }

    public String getRemark2() {
        return remark2;
    }

    public void setRemark2(String remark2) {
        this.remark2 = remark2;
    }

    public String getRemark3() {
        return remark3;
    }

    public void setRemark3(String remark3) {
        this.remark3 = remark3;
    }

    public String getRemark4() {
        return remark4;
    }

    public void setRemark4(String remark4) {
        this.remark4 = remark4;
    }

    public Integer getAdvise() {
        return advise;
    }

    public void setAdvise(Integer advise) {
        this.advise = advise;
    }

    public List getCcMainReason() {
        return ccMainReason;
    }

    public void setCcMainReason(List ccMainReason) {
        this.ccMainReason = ccMainReason;
    }

    public List getCcmPart() {
        return ccmPart;
    }

    public void setCcmPart(List ccmPart) {
        this.ccmPart = ccmPart;
    }

    public List getCcmSubdivisionPart() {
        return ccmSubdivisionPart;
    }

    public void setCcmSubdivisionPart(List ccmSubdivisionPart) {
        this.ccmSubdivisionPart = ccmSubdivisionPart;
    }

    public List getCcResult() {
        return ccResult;
    }

    public void setCcResult(List ccResult) {
        this.ccResult = ccResult;
    }

    public String getCcmSubject() {
        return ccmSubject;
    }

    public void setCcmSubject(String ccmSubject) {
        this.ccmSubject = ccmSubject;
    }

    public Integer getClassification1() {
        return classification1;
    }

    public void setClassification1(Integer classification1) {
        this.classification1 = classification1;
    }

    public String getClassification2() {
        return classification2;
    }

    public void setClassification2(String classification2) {
        this.classification2 = classification2;
    }

    public String getClassification3() {
        return classification3;
    }

    public void setClassification3(String classification3) {
        this.classification3 = classification3;
    }

    public String getClassification4() {
        return classification4;
    }

    public void setClassification4(String classification4) {
        this.classification4 = classification4;
    }

    public String getClassification5() {
        return classification5;
    }

    public void setClassification5(String classification5) {
        this.classification5 = classification5;
    }

    public String getClassification6() {
        return classification6;
    }

    public void setClassification6(String classification6) {
        this.classification6 = classification6;
    }

    public Long getComplaintInfoId() {
        return complaintInfoId;
    }

    public void setComplaintInfoId(Long complaintInfoId) {
        this.complaintInfoId = complaintInfoId;
    }

    public String getFollowContent() {
        return followContent;
    }

    public void setFollowContent(String followContent) {
        this.followContent = followContent;
    }

    public Integer getCcmNotPublish() {
        return ccmNotPublish;
    }

    public void setCcmNotPublish(Integer ccmNotPublish) {
        this.ccmNotPublish = ccmNotPublish;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getPlanFollowTime() {
        return planFollowTime;
    }

    public void setPlanFollowTime(String planFollowTime) {
        this.planFollowTime = planFollowTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
