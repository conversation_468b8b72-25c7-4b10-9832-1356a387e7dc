package com.yonyou.dmscus.customer.entity.dto.goodwill;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 卡券基础数据到中台 dto
 */
@ApiModel(value = "SaveCouponInfoDTO ", description = "卡券表模版")
public class SaveCouponInfoDTO {
	@ApiModelProperty(value = "卡券代码")
	private String couponCode;
	@ApiModelProperty(value = "卡券名称")
	private String couponName;
	@ApiModelProperty(value = "卡券类型(抵扣券:31081001 代金券（储值券）:31081002 折扣券:31081003)")
	private Integer couponType;
	@ApiModelProperty(value = "卡券面额")
	private BigDecimal couponValue;
	@ApiModelProperty(value = "卡券折扣")
	private BigDecimal couponDiscount;
	@ApiModelProperty(value = "卡券满减(满)")
	private BigDecimal couponFull;
	@ApiModelProperty(value = "卡券描述")
	private String denomination;
	@ApiModelProperty(value = "期限类型（字典）")
	private Integer termType;
	@ApiModelProperty(value = "使用期限（单位月）")
	private Integer term;
	@ApiModelProperty(value = "开始时间")
	private String startDate;
	@ApiModelProperty(value = "结束时间")
	private String endDate;
	@ApiModelProperty(value = "是否是商品")
	private Integer asGoods;
	@ApiModelProperty(value = "是否上架(未上架:31091001 已上架:31091002)")
	private Integer asList;
	@ApiModelProperty(value = "可领取总数")
	private Integer totalGet;
	@ApiModelProperty(value = "是否可无限领取(否:31101001 是:31101002)")
	private Integer existLimit;
	@ApiModelProperty(value = "每人最大可领取数")
	private Integer maxLimit;
	@ApiModelProperty(value = "用途描述")
	private String couponExplain;
	@ApiModelProperty(value = "卡券图片")
	private String couponImg;
	@ApiModelProperty(value = "卡券创建日期")
	private String couponCreateDate;
	@ApiModelProperty(value = "卡券业务类型(MKT:31111001 售后券、:31111002 销售券:31111003)")
	private Integer serviceType;
	@ApiModelProperty(value = "是否需要激活（否:31121001 是:31121002）")
	private Integer activationRequired;
	@ApiModelProperty(value = "是否被占用(0:否 1:是)")
	private Integer isOccupied;
	@ApiModelProperty(value = "占用编号")
	private Integer occupyNumber;
	@ApiModelProperty(value = "卡券发布状态(未发布:31131001 发布:31131002)")
	private Integer publishState;
	@ApiModelProperty(value = "创建人姓名")
	private String creator;
	@ApiModelProperty(value = "创建人ID")
	private Long createBy;
	@ApiModelProperty(value = "是否被作废(0:否 1:是)")
	private Integer isVolid;
	@ApiModelProperty(value = "是否亲善券(0:否 1:是)")
	private Integer kindness;
	@ApiModelProperty(value = "使用规则")
	private UseRule useRule;
	@ApiModelProperty(value = "使用场景（线上上城 线下门店 通用）")
	private Integer useScenes;
	@ApiModelProperty(value = "卡券余额")
	private BigDecimal leftValue;

	public BigDecimal getLeftValue() {
		return leftValue;
	}

	public void setLeftValue(BigDecimal leftValue) {
		this.leftValue = leftValue;
	}

	public Integer getUseScenes() {
		return useScenes;
	}

	public void setUseScenes(Integer useScenes) {
		this.useScenes = useScenes;
	}

	public Long getCreateBy() {
		return createBy;
	}

	public void setCreateBy(Long createBy) {
		this.createBy = createBy;
	}

	public UseRule getUseRule() {
		return useRule;
	}

	public void setUseRule(UseRule useRule) {
		this.useRule = useRule;
	}

	public Integer getKindness() {
		return kindness;
	}

	public void setKindness(Integer kindness) {
		this.kindness = kindness;
	}

	public Integer getIsVolid() {
		return isVolid;
	}

	public void setIsVolid(Integer isVolid) {
		this.isVolid = isVolid;
	}

	public String getCouponCode() {
		return couponCode;
	}

	public void setCouponCode(String couponCode) {
		this.couponCode = couponCode;
	}

	public String getCouponName() {
		return couponName;
	}

	public void setCouponName(String couponName) {
		this.couponName = couponName;
	}

	public Integer getCouponType() {
		return couponType;
	}

	public void setCouponType(Integer couponType) {
		this.couponType = couponType;
	}

	public BigDecimal getCouponValue() {
		return couponValue;
	}

	public void setCouponValue(BigDecimal couponValue) {
		this.couponValue = couponValue;
	}

	public BigDecimal getCouponDiscount() {
		return couponDiscount;
	}

	public void setCouponDiscount(BigDecimal couponDiscount) {
		this.couponDiscount = couponDiscount;
	}

	public BigDecimal getCouponFull() {
		return couponFull;
	}

	public void setCouponFull(BigDecimal couponFull) {
		this.couponFull = couponFull;
	}

	public String getDenomination() {
		return denomination;
	}

	public void setDenomination(String denomination) {
		this.denomination = denomination;
	}

	public Integer getTermType() {
		return termType;
	}

	public void setTermType(Integer termType) {
		this.termType = termType;
	}

	public Integer getTerm() {
		return term;
	}

	public void setTerm(Integer term) {
		this.term = term;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public Integer getAsGoods() {
		return asGoods;
	}

	public void setAsGoods(Integer asGoods) {
		this.asGoods = asGoods;
	}

	public Integer getAsList() {
		return asList;
	}

	public void setAsList(Integer asList) {
		this.asList = asList;
	}

	public Integer getTotalGet() {
		return totalGet;
	}

	public void setTotalGet(Integer totalGet) {
		this.totalGet = totalGet;
	}

	public Integer getExistLimit() {
		return existLimit;
	}

	public void setExistLimit(Integer existLimit) {
		this.existLimit = existLimit;
	}

	public Integer getMaxLimit() {
		return maxLimit;
	}

	public void setMaxLimit(Integer maxLimit) {
		this.maxLimit = maxLimit;
	}

	public String getCouponExplain() {
		return couponExplain;
	}

	public void setCouponExplain(String couponExplain) {
		this.couponExplain = couponExplain;
	}

	public String getCouponImg() {
		return couponImg;
	}

	public void setCouponImg(String couponImg) {
		this.couponImg = couponImg;
	}

	public String getCouponCreateDate() {
		return couponCreateDate;
	}

	public void setCouponCreateDate(String couponCreateDate) {
		this.couponCreateDate = couponCreateDate;
	}

	public Integer getServiceType() {
		return serviceType;
	}

	public void setServiceType(Integer serviceType) {
		this.serviceType = serviceType;
	}

	public Integer getActivationRequired() {
		return activationRequired;
	}

	public void setActivationRequired(Integer activationRequired) {
		this.activationRequired = activationRequired;
	}

	public Integer getIsOccupied() {
		return isOccupied;
	}

	public void setIsOccupied(Integer isOccupied) {
		this.isOccupied = isOccupied;
	}

	public Integer getOccupyNumber() {
		return occupyNumber;
	}

	public void setOccupyNumber(Integer occupyNumber) {
		this.occupyNumber = occupyNumber;
	}

	public Integer getPublishState() {
		return publishState;
	}

	public void setPublishState(Integer publishState) {
		this.publishState = publishState;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	@Override
	public String toString() {
		return "SaveCouponInfoDTO{" + "couponCode=" + couponCode + ", couponName=" + couponName + ", couponType="
				+ couponType + ", couponValue=" + couponValue + ", couponDiscount=" + couponDiscount + ", couponFull="
				+ couponFull + ", denomination=" + denomination + ", termType=" + termType + ", term=" + term
				+ ", startDate=" + startDate + ", endDate=" + endDate + ", asGoods=" + asGoods + ", asList=" + asList
				+ ", totalGet=" + totalGet + ", existLimit=" + existLimit + ", maxLimit=" + maxLimit
				+ ", couponExplain=" + couponExplain + ", couponImg=" + couponImg + ", couponCreateDate="
				+ couponCreateDate + ", serviceType=" + serviceType + ", activationRequired=" + activationRequired
				+ ", isOccupied=" + isOccupied + ", occupyNumber=" + occupyNumber + ", publishState=" + publishState
				+ ", creator=" + creator + "}";
	}

}
