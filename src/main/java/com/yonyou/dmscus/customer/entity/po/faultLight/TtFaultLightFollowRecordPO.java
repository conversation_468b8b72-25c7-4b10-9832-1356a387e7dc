package com.yonyou.dmscus.customer.entity.po.faultLight;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("跟进记录详情表")
@TableName("tt_fault_light_follow_record")
public class TtFaultLightFollowRecordPO {

    private static final long serialVersionUID = -1404494213779468044L;

    /**
     * 主键id
     */
    @TableField("id")
    private Long id;

    /**
     * 故障灯线索id
     */
    @TableField("clue_id")
    private Long clueId;

    /**
     * 故障id
     */
    @TableField("fault_id")
    private Long faultId;

    /**
     * 故障城市名称
     */
    @TableField("fault_city_name")
    private String faultCityName;

    /**
     * 线索下发时间
     */
    @TableField("clue_dis_time")
    private Date clueDisTime;

    /**
     * 跟进时间
     */
    @TableField("follow_time")
    private Date followTime;

    /**
     * 线索状态
     */
    @TableField("clue_status")
    private Integer clueStatus;

    /**
     * 跟进状态
     */
    @TableField("follow_status")
    private Integer followStatus;

    /**
     * 预约进店时间
     */
    @TableField("forecast_time")
    private Date forecastTime;

    /**
     * 工单号
     */
    @TableField("ro_no")
    private String roNo;

    /**
     * 联络人
     */
    @TableField("follow_name")
    private String followName;
}
