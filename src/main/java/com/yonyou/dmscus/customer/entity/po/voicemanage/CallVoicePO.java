package com.yonyou.dmscus.customer.entity.po.voicemanage;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 通话录音
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Data
@TableName("tt_call_voice")
public class CallVoicePO extends BasePO<CallVoicePO> {

    private static final long serialVersionUID = 1L;



    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;



    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 话单ID
     */
    @TableField("session_Id")
    private String sessionId;

    /**
     * call_id
     */
    @TableField("call_id")
    private String callId;

    /**
     * 显示号码
     */
    @TableField("display_number")
    private String displayNumber;

    /**
     * 主叫号码
     */
    @TableField("caller_number")
    private String callerNumber;

    /**
     * 被叫号码
     */
    @TableField("callee_number")
    private String calleeNumber;

    /**
     * AI语音工作号
     */
    @TableField("work_number")
    private String workNumber;

    /**
     * 文件大小
     */
    @TableField("file_size")
    private String fileSize;

    /**
     * 录音文件URL
     */
    @TableField("voice_url")
    private String voiceUrl;

    /**
     * 录音类型
     */
    @TableField("voice_type")
    private String voiceType;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;


    /**
     * 经销商代码
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 是否已发送大搜车
     */
    @TableField("is_send")
    private Integer isSend;

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
