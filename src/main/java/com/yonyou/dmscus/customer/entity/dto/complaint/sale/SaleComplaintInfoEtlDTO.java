package com.yonyou.dmscus.customer.entity.dto.complaint.sale;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 销售客户投诉信息表-etl
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-22
 */
    
public class SaleComplaintInfoEtlDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

        
    /**
     * 主键ID
     */
                private Long id;
                
    /**
     * 投诉日期
     */
                private String callTime;
                
    /**
     * 投诉ID（投诉编号）可 用于和呼叫中心对接
     */
                private String complaintId;
                
    /**
     * 工单性质
     */
                private String workOrderNature;
                
    /**
     * 投诉单类别一级层
     */
                private String category1;
                
    /**
     * 投诉单类别二级层
     */
                private String category2;
                
    /**
     * 投诉单类别三级层
     */
                private String category3;
                
    /**
     * 购买经销商
     */
                private String buyDealerCode;
                
    /**
     * 购买经销商
     */
                private String buyDealerName;
                
    /**
     * 处理经销商代码
     */
                private String dealerCode;
                
    /**
     * 处理经销商
     */
                private String dealerName;
                
    /**
     * 客户类型
     */
                private String customerType;
                
    /**
     * 来电客户姓名/投诉人姓名
     */
                private String callName;
                
    /**
     * 来电电话/投诉人电话
     */
                private String callTel;
                
    /**
     * 回复联系人手机1
     */
                private String replyTel;
                
    /**
     * 回复联系人手机2
     */
                private String replyTel2;
                
    /**
     * 车架号
     */
                private String vin;
                
    /**
     * 车主姓名
     */
                private String name;
                
    /**
     * 车主手机
     */
                private String phone;
                
    /**
     * 车型
     */
                private String model;
                
    /**
     * 车牌号
     */
                private String licensePlateNum;
                
    /**
     * 购车时间
     */
                private String buyTime;
                
    /**
     * 生命周期
     */
                private String lifeCycle;
                
    /**
     * 里程
     */
                private String mileage;
                
    /**
     * 问题描述
     */
                private String problem;
                
    /**
     * 工单状态
     */
                private String workOrderStatus;
                
    /**
     * 结案状态（投诉单状态）
     */
                private String closeCaseStatus;
                
    /**
     * 经销商首次回复时间
     */
                private String dealerFisrtReplyTime;
                
    /**
     * 结案时间
     */
                private String closeCaseTime;
                
    /**
     * 投诉解决时长
     */
                private String solveTime;
                
    /**
     * 审核人
     */
                private String reviewer;
                
    /**
     * 活动来源
     */
                private String activitySource;
                
    /**
     * 回访需求
     */
                private String revisitDemand;
                
    /**
     * 邮件状态
     */
                private String emailStatus;
                
    /**
     * 经销商是否在48H内推出邮件
     */
                private String is48hSendEmail;
                
    /**
     * 投诉类型
     */
                private String type;
                
    /**
     * 再次投诉时间
     */
                private String againCallTime;
                
    /**
     * 短信
     */
                private String sms;
                
    /**
     * 有效投诉
     */
                private String effectiveComplaint;
                
    /**
     * 投诉来源
     */
                private String source;
                
    /**
     * 未结案原因分类
     */
                private String noCloseReasonsClassification;
                
    /**
     * 最后跟进时间
     */
                private String lastFollowTime;
                
    /**
     * 最后投诉时间
     */
                private String lastComplaintTime;
                
    /**
     * 总投诉次数
     */
                private String complaintNumber;
                
    /**
     * 责任判定
     */
                private String responsibilityDetermine;
                
    /**
     * 投诉主题
     */
                private String subject;
                
    /**
     * 分类1
     */
                private String classification1;
                
    /**
     * 分类2
     */
                private String classification2;
                
    /**
     * 分类3
     */
                private String classification3;
                
    /**
     * 分类4
     */
                private String classification4;
                
    /**
     * 分类5
     */
                private String classification5;
                
    /**
     * 分类6
     */
                private String classification6;
                
    /**
     * 信息来源
     */
                private String informationSource;
                
    /**
     * 关键字
     */
                private String keyword;
                
    /**
     * 咨询解决情况
     */
                private String consultationAndSolution;
                
    /**
     * 解决方法
     */
                private String solution;
                
    /**
     * FAQ支持
     */
                private String isFaq;
                
    /**
     * 情绪指数新建
     */
                private String emotionIndexCreation;
                
    /**
     * 情绪指数新建
     */
                private String sentimentIndexCurrent;
                
    /**
     * CCM系统协助处理
     */
                private String ccmDeal;
                
    /**
     * 首次回访需求时间
     */
                private String firstRevisitTime;
                
    /**
     * 重启日期
     */
                private String restartTime;
                
    /**
     * 重启次数
     */
                private String restartNumber;
                
    /**
     * 第二次重启时间
     */
                private String secondRestartTime;
                
    /**
     * 第二次结案时间
     */
                private String secondeCloseCaseTime;
                
    /**
     * 案件等级
     */
                private String caseLevel;
                
    /**
     * 是否24H回复
     */
                private String is24hReply;
                
    /**
     * 是否48H回访
     */
                private String is48hRevisit;
                
    /**
     * 是否5日结案
     */
                private String is5dCloseCase;
                
    /**
     * 区域反馈回访
     */
                private String regionalFeedback;
                
    /**
     * 区域反馈回访日
     */
                private String regionalFeedbackTime;
                
    /**
     * 不回访理由
     */
                private String noRevisitReason;
                
    /**
     * 未结案原因
     */
                private String noCloseReasons;
                
    /**
     * 服务承诺
     */
                private String serviceCommitment;
                
    /**
     * 首次回访时间
     */
                private String firstReturnTime;
    /**
     * 开始投诉时间
     */
    private String startCallTime;
    /**
     * 结束投诉时间
     */
    private String endCallTime;
            
    public SaleComplaintInfoEtlDTO() {
        super();
    }

                                            
    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
            }
                                
    public String getCallTime(){
        return callTime;
    }


    public void  setCallTime(String callTime) {
        this.callTime = callTime;
            }
                                
    public String getComplaintId(){
        return complaintId;
    }


    public void  setComplaintId(String complaintId) {
        this.complaintId = complaintId;
            }
                                
    public String getWorkOrderNature(){
        return workOrderNature;
    }


    public void  setWorkOrderNature(String workOrderNature) {
        this.workOrderNature = workOrderNature;
            }
                                
    public String getCategory1(){
        return category1;
    }


    public void  setCategory1(String category1) {
        this.category1 = category1;
            }
                                
    public String getCategory2(){
        return category2;
    }


    public void  setCategory2(String category2) {
        this.category2 = category2;
            }
                                
    public String getCategory3(){
        return category3;
    }


    public void  setCategory3(String category3) {
        this.category3 = category3;
            }
                                
    public String getBuyDealerCode(){
        return buyDealerCode;
    }


    public void  setBuyDealerCode(String buyDealerCode) {
        this.buyDealerCode = buyDealerCode;
            }
                                
    public String getBuyDealerName(){
        return buyDealerName;
    }


    public void  setBuyDealerName(String buyDealerName) {
        this.buyDealerName = buyDealerName;
            }
                                
    public String getDealerCode(){
        return dealerCode;
    }


    public void  setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
            }
                                
    public String getDealerName(){
        return dealerName;
    }


    public void  setDealerName(String dealerName) {
        this.dealerName = dealerName;
            }
                                
    public String getCustomerType(){
        return customerType;
    }


    public void  setCustomerType(String customerType) {
        this.customerType = customerType;
            }
                                
    public String getCallName(){
        return callName;
    }


    public void  setCallName(String callName) {
        this.callName = callName;
            }
                                
    public String getCallTel(){
        return callTel;
    }


    public void  setCallTel(String callTel) {
        this.callTel = callTel;
            }
                                
    public String getReplyTel(){
        return replyTel;
    }


    public void  setReplyTel(String replyTel) {
        this.replyTel = replyTel;
            }
                                
    public String getReplyTel2(){
        return replyTel2;
    }


    public void  setReplyTel2(String replyTel2) {
        this.replyTel2 = replyTel2;
            }
                                
    public String getVin(){
        return vin;
    }


    public void  setVin(String vin) {
        this.vin = vin;
            }
                                
    public String getName(){
        return name;
    }


    public void  setName(String name) {
        this.name = name;
            }
                                
    public String getPhone(){
        return phone;
    }


    public void  setPhone(String phone) {
        this.phone = phone;
            }
                                
    public String getModel(){
        return model;
    }


    public void  setModel(String model) {
        this.model = model;
            }
                                
    public String getLicensePlateNum(){
        return licensePlateNum;
    }


    public void  setLicensePlateNum(String licensePlateNum) {
        this.licensePlateNum = licensePlateNum;
            }
                                
    public String getBuyTime(){
        return buyTime;
    }


    public void  setBuyTime(String buyTime) {
        this.buyTime = buyTime;
            }
                                
    public String getLifeCycle(){
        return lifeCycle;
    }


    public void  setLifeCycle(String lifeCycle) {
        this.lifeCycle = lifeCycle;
            }
                                
    public String getMileage(){
        return mileage;
    }


    public void  setMileage(String mileage) {
        this.mileage = mileage;
            }
                                
    public String getProblem(){
        return problem;
    }


    public void  setProblem(String problem) {
        this.problem = problem;
            }
                                
    public String getWorkOrderStatus(){
        return workOrderStatus;
    }


    public void  setWorkOrderStatus(String workOrderStatus) {
        this.workOrderStatus = workOrderStatus;
            }
                                
    public String getCloseCaseStatus(){
        return closeCaseStatus;
    }


    public void  setCloseCaseStatus(String closeCaseStatus) {
        this.closeCaseStatus = closeCaseStatus;
            }
                                
    public String getDealerFisrtReplyTime(){
        return dealerFisrtReplyTime;
    }


    public void  setDealerFisrtReplyTime(String dealerFisrtReplyTime) {
        this.dealerFisrtReplyTime = dealerFisrtReplyTime;
            }
                                
    public String getCloseCaseTime(){
        return closeCaseTime;
    }


    public void  setCloseCaseTime(String closeCaseTime) {
        this.closeCaseTime = closeCaseTime;
            }
                                
    public String getSolveTime(){
        return solveTime;
    }


    public void  setSolveTime(String solveTime) {
        this.solveTime = solveTime;
            }
                                
    public String getReviewer(){
        return reviewer;
    }


    public void  setReviewer(String reviewer) {
        this.reviewer = reviewer;
            }
                                
    public String getActivitySource(){
        return activitySource;
    }


    public void  setActivitySource(String activitySource) {
        this.activitySource = activitySource;
            }
                                
    public String getRevisitDemand(){
        return revisitDemand;
    }


    public void  setRevisitDemand(String revisitDemand) {
        this.revisitDemand = revisitDemand;
            }
                                
    public String getEmailStatus(){
        return emailStatus;
    }


    public void  setEmailStatus(String emailStatus) {
        this.emailStatus = emailStatus;
            }
                                
    public String getIs48hSendEmail(){
        return is48hSendEmail;
    }


    public void  setIs48hSendEmail(String is48hSendEmail) {
        this.is48hSendEmail = is48hSendEmail;
            }
                                
    public String getType(){
        return type;
    }


    public void  setType(String type) {
        this.type = type;
            }
                                
    public String getAgainCallTime(){
        return againCallTime;
    }


    public void  setAgainCallTime(String againCallTime) {
        this.againCallTime = againCallTime;
            }
                                
    public String getSms(){
        return sms;
    }


    public void  setSms(String sms) {
        this.sms = sms;
            }
                                
    public String getEffectiveComplaint(){
        return effectiveComplaint;
    }


    public void  setEffectiveComplaint(String effectiveComplaint) {
        this.effectiveComplaint = effectiveComplaint;
            }
                                
    public String getSource(){
        return source;
    }


    public void  setSource(String source) {
        this.source = source;
            }
                                
    public String getNoCloseReasonsClassification(){
        return noCloseReasonsClassification;
    }


    public void  setNoCloseReasonsClassification(String noCloseReasonsClassification) {
        this.noCloseReasonsClassification = noCloseReasonsClassification;
            }
                                
    public String getLastFollowTime(){
        return lastFollowTime;
    }


    public void  setLastFollowTime(String lastFollowTime) {
        this.lastFollowTime = lastFollowTime;
            }
                                
    public String getLastComplaintTime(){
        return lastComplaintTime;
    }


    public void  setLastComplaintTime(String lastComplaintTime) {
        this.lastComplaintTime = lastComplaintTime;
            }
                                
    public String getComplaintNumber(){
        return complaintNumber;
    }


    public void  setComplaintNumber(String complaintNumber) {
        this.complaintNumber = complaintNumber;
            }
                                
    public String getResponsibilityDetermine(){
        return responsibilityDetermine;
    }


    public void  setResponsibilityDetermine(String responsibilityDetermine) {
        this.responsibilityDetermine = responsibilityDetermine;
            }
                                
    public String getSubject(){
        return subject;
    }


    public void  setSubject(String subject) {
        this.subject = subject;
            }
                                
    public String getClassification1(){
        return classification1;
    }


    public void  setClassification1(String classification1) {
        this.classification1 = classification1;
            }
                                
    public String getClassification2(){
        return classification2;
    }


    public void  setClassification2(String classification2) {
        this.classification2 = classification2;
            }
                                
    public String getClassification3(){
        return classification3;
    }


    public void  setClassification3(String classification3) {
        this.classification3 = classification3;
            }
                                
    public String getClassification4(){
        return classification4;
    }


    public void  setClassification4(String classification4) {
        this.classification4 = classification4;
            }
                                
    public String getClassification5(){
        return classification5;
    }


    public void  setClassification5(String classification5) {
        this.classification5 = classification5;
            }
                                
    public String getClassification6(){
        return classification6;
    }


    public void  setClassification6(String classification6) {
        this.classification6 = classification6;
            }
                                
    public String getInformationSource(){
        return informationSource;
    }


    public void  setInformationSource(String informationSource) {
        this.informationSource = informationSource;
            }
                                
    public String getKeyword(){
        return keyword;
    }


    public void  setKeyword(String keyword) {
        this.keyword = keyword;
            }
                                
    public String getConsultationAndSolution(){
        return consultationAndSolution;
    }


    public void  setConsultationAndSolution(String consultationAndSolution) {
        this.consultationAndSolution = consultationAndSolution;
            }
                                
    public String getSolution(){
        return solution;
    }


    public void  setSolution(String solution) {
        this.solution = solution;
            }
                                
    public String getIsFaq(){
        return isFaq;
    }


    public void  setIsFaq(String isFaq) {
        this.isFaq = isFaq;
            }
                                
    public String getEmotionIndexCreation(){
        return emotionIndexCreation;
    }


    public void  setEmotionIndexCreation(String emotionIndexCreation) {
        this.emotionIndexCreation = emotionIndexCreation;
            }
                                
    public String getSentimentIndexCurrent(){
        return sentimentIndexCurrent;
    }


    public void  setSentimentIndexCurrent(String sentimentIndexCurrent) {
        this.sentimentIndexCurrent = sentimentIndexCurrent;
            }
                                
    public String getCcmDeal(){
        return ccmDeal;
    }


    public void  setCcmDeal(String ccmDeal) {
        this.ccmDeal = ccmDeal;
            }
                                
    public String getFirstRevisitTime(){
        return firstRevisitTime;
    }


    public void  setFirstRevisitTime(String firstRevisitTime) {
        this.firstRevisitTime = firstRevisitTime;
            }
                                
    public String getRestartTime(){
        return restartTime;
    }


    public void  setRestartTime(String restartTime) {
        this.restartTime = restartTime;
            }
                                
    public String getRestartNumber(){
        return restartNumber;
    }


    public void  setRestartNumber(String restartNumber) {
        this.restartNumber = restartNumber;
            }
                                
    public String getSecondRestartTime(){
        return secondRestartTime;
    }


    public void  setSecondRestartTime(String secondRestartTime) {
        this.secondRestartTime = secondRestartTime;
            }
                                
    public String getSecondeCloseCaseTime(){
        return secondeCloseCaseTime;
    }


    public void  setSecondeCloseCaseTime(String secondeCloseCaseTime) {
        this.secondeCloseCaseTime = secondeCloseCaseTime;
            }
                                
    public String getCaseLevel(){
        return caseLevel;
    }


    public void  setCaseLevel(String caseLevel) {
        this.caseLevel = caseLevel;
            }
                                
    public String getIs24hReply(){
        return is24hReply;
    }


    public void  setIs24hReply(String is24hReply) {
        this.is24hReply = is24hReply;
            }
                                
    public String getIs48hRevisit(){
        return is48hRevisit;
    }


    public void  setIs48hRevisit(String is48hRevisit) {
        this.is48hRevisit = is48hRevisit;
            }
                                
    public String getIs5dCloseCase(){
        return is5dCloseCase;
    }


    public void  setIs5dCloseCase(String is5dCloseCase) {
        this.is5dCloseCase = is5dCloseCase;
            }
                                
    public String getRegionalFeedback(){
        return regionalFeedback;
    }


    public void  setRegionalFeedback(String regionalFeedback) {
        this.regionalFeedback = regionalFeedback;
            }
                                
    public String getRegionalFeedbackTime(){
        return regionalFeedbackTime;
    }


    public void  setRegionalFeedbackTime(String regionalFeedbackTime) {
        this.regionalFeedbackTime = regionalFeedbackTime;
            }
                                
    public String getNoRevisitReason(){
        return noRevisitReason;
    }


    public void  setNoRevisitReason(String noRevisitReason) {
        this.noRevisitReason = noRevisitReason;
            }
                                
    public String getNoCloseReasons(){
        return noCloseReasons;
    }


    public void  setNoCloseReasons(String noCloseReasons) {
        this.noCloseReasons = noCloseReasons;
            }
                                
    public String getServiceCommitment(){
        return serviceCommitment;
    }


    public void  setServiceCommitment(String serviceCommitment) {
        this.serviceCommitment = serviceCommitment;
            }
                                
    public String getFirstReturnTime(){
        return firstReturnTime;
    }


    public void  setFirstReturnTime(String firstReturnTime) {
        this.firstReturnTime = firstReturnTime;
            }

    public String getStartCallTime() {
        return startCallTime;
    }

    public void setStartCallTime(String startCallTime) {
        this.startCallTime = startCallTime;
    }

    public String getEndCallTime() {
        return endCallTime;
    }

    public void setEndCallTime(String endCallTime) {
        this.endCallTime = endCallTime;
    }

    @Override
    public String toString() {
        return "SaleComplaintInfoEtlDTO{" +
                                            "id=" + id +
                                                            ", callTime=" + callTime +
                                                            ", complaintId=" + complaintId +
                                                            ", workOrderNature=" + workOrderNature +
                                                            ", category1=" + category1 +
                                                            ", category2=" + category2 +
                                                            ", category3=" + category3 +
                                                            ", buyDealerCode=" + buyDealerCode +
                                                            ", buyDealerName=" + buyDealerName +
                                                            ", dealerCode=" + dealerCode +
                                                            ", dealerName=" + dealerName +
                                                            ", customerType=" + customerType +
                                                            ", callName=" + callName +
                                                            ", callTel=" + callTel +
                                                            ", replyTel=" + replyTel +
                                                            ", replyTel2=" + replyTel2 +
                                                            ", vin=" + vin +
                                                            ", name=" + name +
                                                            ", phone=" + phone +
                                                            ", model=" + model +
                                                            ", licensePlateNum=" + licensePlateNum +
                                                            ", buyTime=" + buyTime +
                                                            ", lifeCycle=" + lifeCycle +
                                                            ", mileage=" + mileage +
                                                            ", problem=" + problem +
                                                            ", workOrderStatus=" + workOrderStatus +
                                                            ", closeCaseStatus=" + closeCaseStatus +
                                                            ", dealerFisrtReplyTime=" + dealerFisrtReplyTime +
                                                            ", closeCaseTime=" + closeCaseTime +
                                                            ", solveTime=" + solveTime +
                                                            ", reviewer=" + reviewer +
                                                            ", activitySource=" + activitySource +
                                                            ", revisitDemand=" + revisitDemand +
                                                            ", emailStatus=" + emailStatus +
                                                            ", is48hSendEmail=" + is48hSendEmail +
                                                            ", type=" + type +
                                                            ", againCallTime=" + againCallTime +
                                                            ", sms=" + sms +
                                                            ", effectiveComplaint=" + effectiveComplaint +
                                                            ", source=" + source +
                                                            ", noCloseReasonsClassification=" + noCloseReasonsClassification +
                                                            ", lastFollowTime=" + lastFollowTime +
                                                            ", lastComplaintTime=" + lastComplaintTime +
                                                            ", complaintNumber=" + complaintNumber +
                                                            ", responsibilityDetermine=" + responsibilityDetermine +
                                                            ", subject=" + subject +
                                                            ", classification1=" + classification1 +
                                                            ", classification2=" + classification2 +
                                                            ", classification3=" + classification3 +
                                                            ", classification4=" + classification4 +
                                                            ", classification5=" + classification5 +
                                                            ", classification6=" + classification6 +
                                                            ", informationSource=" + informationSource +
                                                            ", keyword=" + keyword +
                                                            ", consultationAndSolution=" + consultationAndSolution +
                                                            ", solution=" + solution +
                                                            ", isFaq=" + isFaq +
                                                            ", emotionIndexCreation=" + emotionIndexCreation +
                                                            ", sentimentIndexCurrent=" + sentimentIndexCurrent +
                                                            ", ccmDeal=" + ccmDeal +
                                                            ", firstRevisitTime=" + firstRevisitTime +
                                                            ", restartTime=" + restartTime +
                                                            ", restartNumber=" + restartNumber +
                                                            ", secondRestartTime=" + secondRestartTime +
                                                            ", secondeCloseCaseTime=" + secondeCloseCaseTime +
                                                            ", caseLevel=" + caseLevel +
                                                            ", is24hReply=" + is24hReply +
                                                            ", is48hRevisit=" + is48hRevisit +
                                                            ", is5dCloseCase=" + is5dCloseCase +
                                                            ", regionalFeedback=" + regionalFeedback +
                                                            ", regionalFeedbackTime=" + regionalFeedbackTime +
                                                            ", noRevisitReason=" + noRevisitReason +
                                                            ", noCloseReasons=" + noCloseReasons +
                                                            ", serviceCommitment=" + serviceCommitment +
                                                            ", firstReturnTime=" + firstReturnTime +
                                    "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
