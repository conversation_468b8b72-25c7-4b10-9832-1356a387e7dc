package com.yonyou.dmscus.customer.entity.po.complaint;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("tt_complaint_info_extend")
public class ComplaintInfoExtendPO {

    private static final long serialVersionUID = 5564094251952044712L;


    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客诉ID
     */
    @TableField("complaint_id")
    private String complaintId;

    /**
     * 第二次重启邮件时间
     */
//    @TableField("two_restart_date")
//    private String twoRestartDate;


    /**
     * 版本号(乐观锁)
     */
//    @TableField("record_version")
//    private Integer recordVersion;

    /**
     * 售后小区名称
     */
    @TableField("after_small_area_name")
    private String afterSmallAreaName;


    /**
     * 投诉信息表主键ID
     */
    @TableField("complaint_info_id")
    private Long complaintInfoId;

    /**
     * 作废时间
     */
//    @TableField("cancel_time")
//    private Date cancelTime;


    /**
     * 是否删除:1，删除；0，未删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 数据创建时间
     */
//    @TableField("create_time")
//    private Date createdAt;

    /**
     * 数据修改时间
     */
//    @TableField("update_time")
//    private Date updatedAt;

    /**
     * 数据创建人
     */
//    @TableField("created_by")
//    private String createdBy;


    /**
     * 数据修改人
     */
//    @TableField("updated_by")
//    private String updatedBy;

    /**
     * 创建sql人
     */
//    @TableField("create_sqlby")
//    private String createSqlby;

    /**
     * 更新人sql人
     */
//    @TableField("update_sqlby")
//    private String updateSqlby;


}
