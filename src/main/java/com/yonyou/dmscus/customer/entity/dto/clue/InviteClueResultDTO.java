package com.yonyou.dmscus.customer.entity.dto.clue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * description 邀约线索查询通用接口回参
 *
 * <AUTHOR>
 * @date 2023/10/09 10:02
 */
@Data
@ApiModel(description = "邀约线索查询通用接口回参")
public class InviteClueResultDTO {

    /**
     * id
     */
    @ApiModelProperty(value = "id", required = true, example = "1")
    private Long id;

    /**
     * 系统id
     */
    @ApiModelProperty(value = "系统id", required = true, example = "volvo")
    private String appId;

    /**
     * 所有者代码
     */
    @ApiModelProperty(value = "所有者代码", required = true, example = "VCDC")
    private String ownerCode;

    /**
     * 邀约来源类型：1vcdc下发邀约、2经销商自建邀约、3voc事故邀约、4crm下发
     */
    @ApiModelProperty(value = "邀约来源类型", required = true, example = "4")
    private Integer sourceType;

    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号", required = true, example = "LVYZBAKD6MP117577")
    private String vin;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号", required = true, example = "沪B***05")
    private String licensePlateNum;

    /**
     * 车主名称
     */
    @ApiModelProperty(value = "车主名称", required = true, example = "无")
    private String name;

    /**
     * 车主电话
     */
    @ApiModelProperty(value = "车主电话", required = true, example = "923****1234")
    private String tel;

    /**
     * 邀约类型
     */
    @ApiModelProperty(value = "邀约类型", required = true, example = "82381006")
    private Integer inviteType;

    /**
     * 建议进场时间
     */
    @ApiModelProperty(value = "建议进场时间", required = true, example = "2023-12-01 11:11:11")
    private Date adviseInDate;

    /**
     * 计划跟进日期
     */
    @ApiModelProperty(value = "计划跟进日期", required = true, example = "2022-03-09 00:00:00")
    private Date planFollowDate;

    /**
     * 实际跟进日期
     */
    @ApiModelProperty(value = "实际跟进日期", required = true, example = "2022-03-08 16:24:44")
    private Date actualFollowDate;

    /**
     * 跟进服务顾问id
     */
    @ApiModelProperty(value = "跟进服务顾问id", required = true, example = "2015387")
    private String saId;

    /**
     * 跟进服务顾问姓名
     */
    @ApiModelProperty(value = "跟进服务顾问姓名", required = true, example = "测试")
    private String saName;

    /**
     * 上次跟进服务顾问id
     */
    @ApiModelProperty(value = "上次跟进服务顾问id", required = true, example = "2015386")
    private String lastSaId;

    /**
     * 上次跟进服务顾问姓名
     */
    @ApiModelProperty(value = "上次跟进服务顾问姓名", required = true, example = "测试2")
    private String lastSaName;

    /**
     * 跟进状态
     */
    @ApiModelProperty(value = "跟进状态", required = true, example = "82401004")
    private Integer followStatus;

    /**
     * 是否预约单：1 是，0 否
     */
    @ApiModelProperty(value = "是否预约单", required = true, example = "1")
    private int isBook;

    /**
     * 预约单号
     */
    @ApiModelProperty(value = "预约单号", required = true, example = "1501111662537744385")
    private String bookNo;

    /**
     * 工单状态
     */
    @ApiModelProperty(value = "工单状态", required = true, example = "82411001")
    private Integer orderStatus;

    /**
     * 数据来源
     */
    @ApiModelProperty(value = "数据来源", required = true, example = "10451001")
    private Integer dataSources;

    /**
     * 经销商代码
     */
    @ApiModelProperty(value = "经销商代码", required = true, example = "SHJ")
    private String dealerCode;

    /**
     * 车主年龄
     */
    @ApiModelProperty(value = "车主年龄", required = true, example = "33")
    private String age;

    /**
     * 车主性别
     */
    @ApiModelProperty(value = "车主性别", required = true, example = "10021002")
    private String sex;

    /**
     * 车型
     */
    @ApiModelProperty(value = "车型", required = true, example = "1034")
    private String model;

    /**
     * 完成工单号
     */
    @ApiModelProperty(value = "完成工单号", required = true, example = "WV2209042302")
    private String roNo;

    /**
     * 完成工单维修类型
     */
    @ApiModelProperty(value = "完成工单维修类型", required = true, example = "S")
    private String repairTypeCode;

    /**
     * 完成工单出厂里程
     */
    @ApiModelProperty(value = "出厂里程", required = true, example = "200000")
    private BigDecimal outMileage;

    /**
     * 完成工单开单时间
     */
    @ApiModelProperty(value = "完成工单开单时间", required = true, example = "2023-06-01 08:00:00")
    private Date roCreateDate;

    /**
     * 完成工单经销商
     */
    @ApiModelProperty(value = "完成工单经销商", required = true, example = "SHJ")
    private String finishDealerCode;

    /**
     * 跟进记录次数
     */
    @ApiModelProperty(value = "跟进记录次数", required = true, example = "0")
    private Integer recordNum;

    /**
     * 线索类型
     */
    @ApiModelProperty(value = "线索类型", required = true, example = "87891003")
    private Integer recordType;

    /**
     * 流失类型
     */
    @ApiModelProperty(value = "线索类型", required = true, example = "0")
    private Integer lossType;

    /**
     * crm团队线索id
     */
    @ApiModelProperty(value = "icmId", required = true, example = "12")
    private Long icmId;

    /**
     * 卡卷模版id
     */
    @ApiModelProperty(value = "卡卷模版id", required = true, example = "16")
    private Long couponId;

    /**
     * 卡卷模版code
     */
    @ApiModelProperty(value = "卡卷模版code", required = true, example = "COUPONDMS00011016")
    private String couponCode;

    /**
     * 卡卷模版名称
     */
    @ApiModelProperty(value = "卡卷模版名称", required = true, example = "冬季活动奖品券")
    private String couponName;

    /**
     * 卡劵id
     */
    @ApiModelProperty(value = "卡劵id", required = true, example = "10")
    private Long detailId;

    /**
     * 核销经销商
     */
    @ApiModelProperty(value = "核销经销商", required = true, example = "AYB")
    private String exchangeOwnerCode;

    /**
     * 核销工单号
     */
    @ApiModelProperty(value = "核销工单号", required = true, example = "AYB")
    private String exchangeRoNo;

    /**
     * 卡劵状态
     */
    @ApiModelProperty(value = "卡劵状态", required = true, example = "null")
    private Integer couponState;

    /**
     * 卡劵状态变更时间
     */
    @ApiModelProperty(value = "卡劵状态变更时间", required = true, example = "2023-09-21 19:14:08")
    private Date couponStateTime;

    /**
     * 日均里程(cdp)
     */
    @ApiModelProperty(value = "日均里程(cdp)", required = true, example = "1000")
    private String dailyMile;

    /**
     * 验证状态
     */
    @ApiModelProperty(value = "验证状态", required = true, example = "87911001")
    private Integer verifyStatus;

    /**
     * 流失预警类型
     */
    @ApiModelProperty(value = "流失预警类型", required = true, example = "0")
    private Integer lossWarningType;

    /**
     * 进入待验证的时间
     */
    @ApiModelProperty(value = "进入待验证的时间", required = true, example = "2023-09-21 00:00:00")
    private Date verifyTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "线索生成时间", required = true, example = "2023-09-21 00:00:00")
    private Date createdAt;
}
