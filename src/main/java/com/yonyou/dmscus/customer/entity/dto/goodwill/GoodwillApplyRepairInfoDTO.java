package com.yonyou.dmscus.customer.entity.dto.goodwill;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 亲善预约申请维修记录子表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
    
public class GoodwillApplyRepairInfoDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

        
    /**
     * 系统ID
     */
                private String appId;
                
    /**
     * 所有者代码
     */
                private String ownerCode;
                
    /**
     * 所有者的父组织代码
     */
                private String ownerParCode;
                
    /**
     * 组织ID
     */
                private Integer orgId;
                
    /**
     * 维修记录表id
     */
                private Long id;
                
    /**
     * 亲善预申请表id
     */
                private Long goodwillApplyId;
                
    /**
     * 工单号
     */
                private String repairNo;
                
    /**
     * 进厂日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date intoFactoryDate;
                
    /**
     * 出厂日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date leaveFactoryDate;
                
    /**
     * 里程
     */
                private String mileage;
                
    /**
     * 车辆故障详情
     */
                private String vehicleFaultDetail;
                
    /**
     * 检查结果
     */
                private String checkResult;
                
    /**
     * 备注
     */
                private String remark;
                
    /**
     * 是否有效
     */
                private Integer isValid;
                
    /**
     * 是否删除:1,删除；0,未删除
     */
                private Boolean isDeleted;
                
    /**
     * 创建时间
     */
    private Date createdAt;
                
    /**
     * 修改时间
     */
    private Date updatedAt;
            
    public GoodwillApplyRepairInfoDTO() {
        super();
    }

                                
    public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
            }
                                
    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
            }
                                
    public String getOwnerParCode(){
        return ownerParCode;
    }


    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
            }
                                
    public Integer getOrgId(){
        return orgId;
    }


    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
            }
                                            
    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
            }
                                
    public Long getGoodwillApplyId(){
        return goodwillApplyId;
    }


    public void  setGoodwillApplyId(Long goodwillApplyId) {
        this.goodwillApplyId = goodwillApplyId;
            }
                                
    public String getRepairNo(){
        return repairNo;
    }


    public void  setRepairNo(String repairNo) {
        this.repairNo = repairNo;
            }
                                
    public Date getIntoFactoryDate() {
		return intoFactoryDate;
	}


	public void setIntoFactoryDate(Date intoFactoryDate) {
		this.intoFactoryDate = intoFactoryDate;
	}


	public Date getLeaveFactoryDate() {
		return leaveFactoryDate;
	}


	public void setLeaveFactoryDate(Date leaveFactoryDate) {
		this.leaveFactoryDate = leaveFactoryDate;
	}


	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}


	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}


	public String getMileage(){
        return mileage;
    }


    public void  setMileage(String mileage) {
        this.mileage = mileage;
            }
                                
    public String getVehicleFaultDetail(){
        return vehicleFaultDetail;
    }


    public void  setVehicleFaultDetail(String vehicleFaultDetail) {
        this.vehicleFaultDetail = vehicleFaultDetail;
            }
                                
    public String getCheckResult(){
        return checkResult;
    }


    public void  setCheckResult(String checkResult) {
        this.checkResult = checkResult;
            }
                                
    public String getRemark(){
        return remark;
    }


    public void  setRemark(String remark) {
        this.remark = remark;
            }
                                
    public Integer getIsValid(){
        return isValid;
    }


    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
            }
                                
    
    @Override
    public String toString() {
        return "GoodwillApplyRepairInfoDTO{" +
                                            "appId=" + appId +
                                                            ", ownerCode=" + ownerCode +
                                                            ", ownerParCode=" + ownerParCode +
                                                            ", orgId=" + orgId +
                                                            ", id=" + id +
                                                            ", goodwillApplyId=" + goodwillApplyId +
                                                            ", repairNo=" + repairNo +
                                                            ", intoFactoryDate=" + intoFactoryDate +
                                                            ", leaveFactoryDate=" + leaveFactoryDate +
                                                            ", mileage=" + mileage +
                                                            ", vehicleFaultDetail=" + vehicleFaultDetail +
                                                            ", checkResult=" + checkResult +
                                                            ", remark=" + remark +
                                                            ", isValid=" + isValid +
                                                            ", isDeleted=" + isDeleted +
                                                            ", createdAt=" + createdAt +
                                                            ", updatedAt=" + updatedAt +
                                    "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
