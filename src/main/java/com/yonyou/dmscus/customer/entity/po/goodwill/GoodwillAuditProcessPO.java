package com.yonyou.dmscus.customer.entity.po.goodwill;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善审批流程表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@TableName("tm_goodwill_audit_process")
public class GoodwillAuditProcessPO extends BasePO<GoodwillAuditProcessPO> {

	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	@TableField("APP_ID")
	private String appId;

	/**
	 * 所有者代码
	 */
	@TableField("OWNER_CODE")
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	@TableField("OWNER_PAR_CODE")
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	@TableField("ORG_ID")
	private Integer orgId;

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 审核对象
	 */
	@TableField("audit_object")
	private Integer auditObject;

	/**
	 * 审批类型
	 */
	@TableField("audit_type")
	private Integer auditType;

	/**
	 * 审批职位
	 */
	@TableField("audit_position")
	private String auditPosition;

	/**
	 * 最小取值关系（大于等于小于）
	 */
	@TableField("min_value_relation")
	private Integer minValueRelation;

	/**
	 * 最小审批金额
	 */
	@TableField("min_audit_price")
	private BigDecimal minAuditPrice;

	/**
	 * 最大取值关系
	 */
	@TableField("max_value_relation")
	private Integer maxValueRelation;

	/**
	 * 最大审批金额
	 */
	@TableField("max_audit_price")
	private BigDecimal maxAuditPrice;

	/**
	 * 审批顺序
	 */
	@TableField("audit_sort")
	private Integer auditSort;
	/**
	 * 是否共用
	 */
	@TableField("is_public")
	private Integer isPublic;

	/**
	 * 是否有效
	 */
	@TableField("is_valid")
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	@TableField("is_deleted")
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	@TableField("created_at")
	private Date createdAt;

	/**
	 * 修改时间
	 */
	@TableField("updated_at")
	private Date updatedAt;
	/**
	 * 拒单设置
	 */
	@TableField("month")
	private Integer month;

	public Integer getIsPublic() {
		return isPublic;
	}

	public void setIsPublic(Integer isPublic) {
		this.isPublic = isPublic;
	}

	public GoodwillAuditProcessPO() {
		super();
	}

	@Override
	public String getAppId() {
		return appId;
	}

	@Override
	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	@Override
	public String getOwnerParCode() {
		return ownerParCode;
	}

	@Override
	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getAuditObject() {
		return auditObject;
	}

	public void setAuditObject(Integer auditObject) {
		this.auditObject = auditObject;
	}

	public Integer getAuditType() {
		return auditType;
	}

	public void setAuditType(Integer auditType) {
		this.auditType = auditType;
	}

	public String getAuditPosition() {
		return auditPosition;
	}

	public void setAuditPosition(String auditPosition) {
		this.auditPosition = auditPosition;
	}

	public Integer getMinValueRelation() {
		return minValueRelation;
	}

	public void setMinValueRelation(Integer minValueRelation) {
		this.minValueRelation = minValueRelation;
	}

	public BigDecimal getMinAuditPrice() {
		return minAuditPrice;
	}

	public void setMinAuditPrice(BigDecimal minAuditPrice) {
		this.minAuditPrice = minAuditPrice;
	}

	public Integer getMaxValueRelation() {
		return maxValueRelation;
	}

	public void setMaxValueRelation(Integer maxValueRelation) {
		this.maxValueRelation = maxValueRelation;
	}

	public BigDecimal getMaxAuditPrice() {
		return maxAuditPrice;
	}

	public void setMaxAuditPrice(BigDecimal maxAuditPrice) {
		this.maxAuditPrice = maxAuditPrice;
	}

	public Integer getAuditSort() {
		return auditSort;
	}

	public void setAuditSort(Integer auditSort) {
		this.auditSort = auditSort;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	@Override
	public Date getCreatedAt() {
		return createdAt;
	}

	@Override
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Override
	public Date getUpdatedAt() {
		return updatedAt;
	}

	@Override
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public Integer getMonth() {
		return month;
	}

	public void setMonth(Integer month) {
		this.month = month;
	}

	@Override
	protected Serializable pkVal() {
		return this.id;
	}

	@Override
	public String toString() {
		return "GoodwillAuditProcessPO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", auditObject=" + auditObject + ", auditType="
				+ auditType + ", auditPosition=" + auditPosition + ", minValueRelation=" + minValueRelation
				+ ", minAuditPrice=" + minAuditPrice + ", maxValueRelation=" + maxValueRelation + ", maxAuditPrice="
				+ maxAuditPrice + ", auditSort=" + auditSort + ", isValid=" + isValid + ", isDeleted=" + isDeleted
				+ ", createdAt=" + createdAt + ", updatedAt=" + updatedAt + "}";
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dtoClass
	 *            需要进行转换的dtoClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
		return super.transDtoToPo(dtoClass);
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dto
	 *            需要进行转换的dto
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> void transPoToDto(T dto) {
		BeanMapperUtil.copyProperties(this, dto, "id");
	}

}
