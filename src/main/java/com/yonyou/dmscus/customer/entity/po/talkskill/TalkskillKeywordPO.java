package com.yonyou.dmscus.customer.entity.po.talkskill;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 话术关键词与话术关系
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-27
 */
@TableName("tt_talkskill_keyword")
@Data
public class TalkskillKeywordPO extends BasePO<TalkskillKeywordPO> {

    private static final long serialVersionUID=1L;
        
    /**
     * 主键ID
     */
    @TableId(value = "key_id", type = IdType.AUTO)
    private Long keyId;
    /**
     * 话术id
     */
    @TableField(value = "talk_id")
    private Long talkId;
    /**
     * 关键词
     */
    @TableField("keyword")
    private String keyword;
    
    /**
     * 次数
     */
    @TableField("num")
    private Integer num;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;
    
    /**
     * 是否删除，1：删除，0：未删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;
    
    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;
    
    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;
    
    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;

    public TalkskillKeywordPO(){
        super();
    }

    @Override
    protected Serializable pkVal(){
            return this.talkId;
        }

/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"keyId");
    }

}
