package com.yonyou.dmscus.customer.entity.dto.complaint.AcceptComlaintData;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 接受400下发回访结果
 */
@Data
public class AcceptReplyResultDTO {
    /**
     * 投诉ID（投诉编号）
     */
    private String complaintId;

    /**
     * 回访时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date revisitTime;

    /**
     * 回访结果
     */
    private String revisitResult;

    /**
     * 回访内容
     */
    private String revisitContent;

    /**
     * 工单状态
     */
    private Integer workOrderStatus;

    /**
     * 结案状态（投诉单状态）
     */
    private Integer closeCaseStatus;

    /**
     * 结案时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date closeCaseTime;

    /**
     * 重启结案时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date restartCloseCaseTime;

    /**
     * 主管审核说明
     */
    private  String supervisorAuditNote;

    /**
     * 接口推送类型
     */
    private  Integer interfacePushType;

}
