package com.yonyou.dmscus.customer.entity.dto.inviteInsurance;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.UserInfoDTO;

import java.io.Serializable;
import java.util.List;

public class InsuranceSaSllocateDlrDto extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer ruleType;

    private List<UserInfoDTO> selectSa;

    private List<InviteInsuranceVehicleRecordDTO> inviteInsuranceVehicleRecordList;

    public Integer getRuleType() {
        return ruleType;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    public List<UserInfoDTO> getSelectSa() {
        return selectSa;
    }

    public void setSelectSa(List<UserInfoDTO> selectSa) {
        this.selectSa = selectSa;
    }

    public List<InviteInsuranceVehicleRecordDTO> getInviteInsuranceVehicleRecordList() {
        return inviteInsuranceVehicleRecordList;
    }

    public void setInviteInsuranceVehicleRecordList(List<InviteInsuranceVehicleRecordDTO> inviteInsuranceVehicleRecordList) {
        this.inviteInsuranceVehicleRecordList = inviteInsuranceVehicleRecordList;
    }
}
