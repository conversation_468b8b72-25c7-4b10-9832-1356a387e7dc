package com.yonyou.dmscus.customer.entity.po.faultLight;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tt_fault_light_dispose")
public class FaultLightPO extends BasePO<FaultLightPO> {

    private static final long serialVersionUID = 7031716563627435284L;

    /**
     * 主键id
     */
    @TableField("id")
    private Long id;

    /**
     * 故障(en)
     */
    @TableField("waring_en_description")
    private String waringEnDescription;

    /**
     * 故障(cn)
     */
    @TableField("waring_cn_description")
    private String waringCnDescription;

    /**
     * 故障名称
     */
    @TableField("warning_name")
    private String warningName;

    /**
     * 故障英文名
     */
    @TableField("warning_en_name")
    private String warningEnName;


    /**
     * 故障等级
     */
    @TableField("fault_grade")
    private String faultGrade;

    /**
     * 次数
     */
    @TableField("num")
    private Integer num;

    /**
     * 次数
     */
    @TableField("expire_number")
    private Integer expireNumber;

    /**
     * 线索生成状态:1，启用；0，未启用
     */
    @TableField("produce_status")
    private Integer produceStatus;

    /**
     * 责任验证状态:1，启用；0，未启用
     */
    @TableField("duty_status")
    private Integer dutyStatus;

    /**
     * 是否删除:1，删除；0，未删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 数据创建时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 数据创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 数据修改时间
     */
    @TableField("updated_at")
    private Date updatedAt;

    /**
     * 数据修改人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 创建sql人
     */
    @TableField("create_sqlby")
    private String createSqlby;

    /**
     * 更新人sql人
     */
    @TableField("update_sqlby")
    private String updateSqlby;

    /**
     * 车型ID集合
     */
    @TableField("vehicle_model_ids")
    private String vehicleModelIds;
    @TableField(exist = false)
    private int currentPage;
    @TableField(exist = false)
    private int pageSize;
}
