package com.yonyou.dmscus.customer.entity.dto.accidentClues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 预约登记悬浮框属性Vo
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiModel("预约登记悬浮框属性Vo")
@Data
public class CheckBoxSettingVo {

    @ApiModelProperty(value = "属性code",name = "settingCode")
    private String settingCode;

    @ApiModelProperty(value = "属性名称",name = "settingName")
    private String settingName;

    @ApiModelProperty(value = "属性状态",name = "settingStatus")
    private Integer settingStatus;

    @ApiModelProperty(value = "是否勾选",name = "isChecked")
    private Integer isChecked;
}
