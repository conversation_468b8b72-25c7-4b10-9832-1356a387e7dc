package com.yonyou.dmscus.customer.entity.po.goodwill;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善预申请单
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
@TableName("tt_goodwill_apply_info")
public class GoodwillApplyInfoPO extends BasePO<GoodwillApplyInfoPO> {

	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	@TableField("APP_ID")
	private String appId;

	/**
	 * 所有者代码
	 */
	@TableField("OWNER_CODE")
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	@TableField("OWNER_PAR_CODE")
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	@TableField("ORG_ID")
	private Integer orgId;

	/**
	 * 预申请单表id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 预申请单号
	 */
	@TableField("apply_no")
	private String applyNo;

	/**
	 * 申请经销商
	 */
	@TableField("dealer_code")
	private String dealerCode;
	/**
	 * 申请经销商名称
	 */
	@TableField("dealer_name")
	private String dealerName;
	/**
	 * 区域ID
	 */
	@TableField("area_manage_id")
	private Integer areaManageId;
	/**
	 * 区域-区域经理
	 */
	@TableField("area_manage")
	private String areaManage;
	/**
	 * 大区ID
	 */
	@TableField("small_area_id")
	private Integer smallAreaId;
	/**
	 * 大区名称
	 */
	@TableField("small_area")
	private String smallArea;
	/**
	 * 集团ID
	 */
	@TableField("bloc_id")
	private Integer blocId;
	/**
	 * 集团
	 */
	@TableField("bloc")
	private String bloc;

	/**
	 * 审核类型
	 */
	@TableField("audit_type")
	private Integer auditType;

	/**
	 * 亲善性质
	 */
	@TableField("goodwill_nature")
	private Integer goodwillNature;

	/**
	 * 申请时间
	 */
	@TableField("apply_time")
	private Date applyTime;

	/**
	 * 投诉故障
	 */
	@TableField("complaint_falut")
	private String complaintFalut;

	/**
	 * 投诉单ID
	 */
	@TableField("complaint_id")
	private String complaintId;

	/**
	 * 投诉日期
	 */
	@TableField("complaint_date")
	private Date complaintDate;
	/**
	 * 用车情况
	 */
	@TableField("vehicle_use")
	private Integer vehicleUse;

	/**
	 * 销售经销商
	 */
	@TableField("sales_dealer")
	private String salesDealer;

	/**
	 * 申请金额
	 */
	@TableField("apply_amount")
	private BigDecimal applyAmount;

	/**
	 * 审批金额
	 */
	@TableField("audit_amount")
	private BigDecimal auditAmount;

	/**
	 * 结算金额
	 */
	@TableField("settlement_amount")
	private BigDecimal settlementAmount;

	/**
	 * 通知开票金额
	 */
	@TableField("invoice_amount")
	private BigDecimal invoiceAmount;

	/**
	 * 是否需要CCMQ翻译
	 */
	@TableField("is_need_translate")
	private Integer isNeedTranslate;

	/**
	 * 亲善单状态
	 */
	@TableField("goodwill_status")
	private Integer goodwillStatus;

	/**
	 * 重启前亲善单状态
	 */
	@TableField("last_goodwill_status")
	private Integer lastGoodwillStatus;

	/**
	 * 客户痛点
	 */
	@TableField("customer_pain")
	private String customerPain;

	/**
	 * VIN
	 */
	@TableField("vin")
	private String vin;

	/**
	 * 车牌号
	 */
	@TableField("license")
	private String license;
	/**
	 * 客户oneId
	 */
	@TableField("owner_no")
	private String ownerNo;

	/**
	 * 客户姓名
	 */
	@TableField("customer_name")
	private String customerName;

	/**
	 * 客户电话
	 */
	@TableField("customer_mobile")
	private String customerMobile;

	/**
	 * 里程
	 */
	@TableField("mileage")
	private String mileage;

	/**
	 * 车型
	 */
	@TableField("model")
	private String model;

	/**
	 * 购车日期
	 */
	@TableField("buy_car_date")
	private Date buyCarDate;

	/**
	 * 保修开始日期
	 */
	@TableField("warranty_start_date")
	private Date warrantyStartDate;

	/**
	 * 是否延保
	 */
	@TableField("is_extend_warranty")
	private Integer isExtendWarranty;

	/**
	 * 延保名称
	 */
	@TableField("extend_warranty_name")
	private String extendWarrantyName;

	/**
	 * 延保开始日期
	 */
	@TableField("extend_warranty_start_date")
	private Date extendWarrantyStartDate;

	/**
	 * 延保结束日期
	 */
	@TableField("extend_warranty_end_date")
	private Date extendWarrantyEndDate;

	/**
	 * 保养成本
	 */
	@TableField("maintain_cost")
	private BigDecimal maintainCost;

	/**
	 * 延保成本
	 */
	@TableField("extend_warranty_cost")
	private BigDecimal extendWarrantyCost;

	/**
	 * 附件精品成本
	 */
	@TableField("accessory_cost")
	private BigDecimal accessoryCost;

	/**
	 * 代金券成本
	 */
	@TableField("voucher_cost")
	private BigDecimal voucherCost;

	/**
	 * 代步车/相关利益
	 */
	@TableField("walking_car_price")
	private BigDecimal walkingCarPrice;

	/**
	 * 沃世界积分
	 */
	@TableField("volvo_integral")
	private BigDecimal volvoIntegral;

	/**
	 * 退换车
	 */
	@TableField("return_change_car_price")
	private BigDecimal returnChangeCarPrice;

	/**
	 * 其他
	 */
	@TableField("other_price")
	private BigDecimal otherPrice;

	/**
	 * 成本总计
	 */
	@TableField("cost_total")
	private BigDecimal costTotal;

	/**
	 * 客户支付
	 */
	@TableField("customer_pay")
	private BigDecimal customerPay;

	/**
	 * 经销商承担
	 */
	@TableField("dealer_undertake")
	private BigDecimal dealerUndertake;

	/**
	 * volvo支持亲善金额
	 */
	@TableField("volvo_support_goodwill_amount")
	private BigDecimal volvoSupportGoodwillAmount;

	/**
	 * 备注
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 预申请附件
	 */
	@TableField("apply_file")
	private String applyFile;

	/**
	 * 亲善成本统计
	 */
	@TableField("cost_statistics_file")
	private String costStatisticsFile;

	/**
	 * 亲善成本截图
	 */
	@TableField("cost_screenshot_file")
	private String costScreenshotFile;

	/**
	 * 故障维修工单/环检单
	 */
	@TableField("ring_check_file")
	private String ringCheckFile;

	/**
	 * 故障维修领料单
	 */
	@TableField("trouble_repair_requisition_file")
	private String troubleRepairRequisitionFile;

	/**
	 * 亲善安装工单/领料单
	 */
	@TableField("work_order_file")
	private String workOrderFile;

	/**
	 * 情况说明和解协议
	 */
	@TableField("situation_settlement_agreement_file")
	private String situationSettlementAgreementFile;

	/**
	 * 退换车补充材料
	 */
	@TableField("supplementary_material_file")
	private String supplementaryMaterialFile;

	/**
	 * 管理层审核邮件-VP
	 */
	@TableField("management_review_email_vp_file")
	private String managementReviewEmailVpFile;

	/**
	 * 管理层审核邮件-CEO
	 */
	@TableField("management_review_email_ceo_file")
	private String managementReviewEmailCeoFile;

	/**
	 * 费用更新附件
	 */
	@TableField("cost_update_file")
	private String costUpdateFile;

	/**
	 * VCDC其他附件
	 */
	@TableField("vcdc_else_file")
	private String vcdcElseFile;

	/**
	 * 客户身份证明
	 */
	@TableField("customer_identification")
	private String customerIdentification;

	/**
	 * 其他附件
	 */
	@TableField("else_file")
	private String elseFile;

	/**
	 * 提交时间
	 */
	@TableField("commit_time")
	private Date commitTime;

	/**
	 * 是否有效
	 */
	@TableField("is_valid")
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	@TableField("is_deleted")
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	@TableField("created_at")
	private Date createdAt;

	/**
	 * 修改时间
	 */
	@TableField("updated_at")
	private Date updatedAt;
	/**
	 * 最后操作人
	 */
	@TableField("updated_person")
	private String updatedPerson;

	/**
	 * 申请人
	 */
	@TableField("apply_person")
	private String applyPerson;
	/**
	 * 申请人
	 */
	@TableField("coupon_id")
	private Long couponId;

	/**
	 * 预申请通过时间
	 */
	@TableField("pass_time")
	private Date passTime;
	/**
	 * 材料提交时间
	 */
	@TableField("material_commit_time")
	private Date materialCommitTime;
	/**
	 * 材料审核通过时间
	 */
	@TableField("material_pass_time")
	private Date materialPassTime;

	/**
	 * 申请时间
	 */
	@TableField(exist = false)
	private Date applyStartTime;
	@TableField(exist = false)
	private Date applyEndTime;
	/**
	 * 预申请通过时间
	 */
	@TableField(exist = false)
	private Date passStartTime;
	@TableField(exist = false)
	private Date passEndTime;
	/**
	 * 申请金额
	 */
	@TableField(exist = false)
	private BigDecimal applyStartAmount;
	@TableField(exist = false)
	private BigDecimal applyEndAmount;

	/**
	 * 开票时间
	 */
	@TableField(exist = false)
	private Date invoiceStartDate;
	@TableField(exist = false)
	private Date invoiceEndDate;
	@TableField(exist = false)
	private Date invoiceDate;
	/**
	 * 通知开票时间
	 */
	@TableField(exist = false)
	private Date noticeInvoiceStartDate;
	@TableField(exist = false)
	private Date noticeInvoiceEndDate;
	@TableField(exist = false)
	private Date noticeInvoiceDate;

	/**
	 * 更新时间
	 */
	@TableField(exist = false)
	private Date updatedStartAt;
	@TableField(exist = false)
	private Date updatedEndAt;

	/**
	 * 用户id
	 */
	@TableField(exist = false)
	private Long userId;

	/**
	 * 审计结果
	 */
	@TableField(exist = false)
	private Integer auditResult;

	/**
	 * 是否审计
	 */
	@TableField(exist = false)
	private Integer isAudit;

	/**
	 * 审计时间
	 */
	@TableField(exist = false)
	private Date auditTime;

	/**
	 * 材料审核时长
	 */
	@TableField(exist = false)
	private String materialAuditLength;

	/**
	 * 材料提交时长
	 */
	@TableField(exist = false)
	private String materialCommitLength;

	/**
	 * 无需支持时间
	 */
	@TableField(exist = false)
	private Date unSupportDate;

	/**
	 * 无需支持理由
	 */
	@TableField(exist = false)
	private String unSupportReason;

	/**
	 * 拒绝支持时间
	 */
	@TableField(exist = false)
	private Date refuseSupportDate;

	/**
	 * 拒绝支持理由
	 */
	@TableField(exist = false)
	private String refuseSupportReason;

	/**
	 * 重启时间
	 */
	@TableField(exist = false)
	private Date restartDate;

	/**
	 * 重启理由
	 */
	@TableField(exist = false)
	private String restartReason;

	/**
	 * 最新审核时间
	 */
	@TableField(exist = false)
	private Date newAuditDate;

	/**
	 * 最新审核结果
	 */
	@TableField(exist = false)
	private Integer newAuditResult;
	/**
	 * 最新审核人
	 */
	@TableField(exist = false)
	private String newestAuditor;

	/**
	 * 是否重启
	 */
	@TableField(exist = false)
	private Integer isRestart;

	/**
	 * 材料提交时长
	 */
	@TableField(exist = false)
	private Integer materialSummit;

	/**
	 * 材料审核时长
	 */
	@TableField(exist = false)
	private Integer materialAudit;

	/**
	 * 账号类型
	 */
	@TableField(exist = false)
	private Integer dataType;

	/**
	 * 投诉故障
	 */
	@TableField(exist = false)
	private String[] complaintFalut1;

	/**
	 * 代金券已通知开票金额
	 */
	@TableField(exist = false)
	private BigDecimal voucherInvoiceAmount;

	/**
	 * 代金券未通知开票金额
	 */
	@TableField(exist = false)
	private BigDecimal voucherNotInvoiceAmount;

	/**
	 * 最新审核意见
	 */
	@TableField(exist = false)
	private String newAuditOpinion;

	/**
	 * 投诉原因及处理经过
	 */
	@TableField(exist = false)
	private String reasonAndDispose;

	/**
	 * 维修解决方案
	 */
	@TableField(exist = false)
	private String repairSolution;

	/**
	 * 商务亲善申请详情
	 */
	@TableField(exist = false)
	private String businessGoodwillApplyDetail;

	/**
	 * 已通知开票金额
	 */
	@TableField(exist = false)
	private BigDecimal noticeInvoicePrice;

	/**
	 * 开票金额
	 */
	@TableField(exist = false)
	private BigDecimal invoicePrice;

	/**
	 * 审批部门
	 */
	@TableField(exist = false)
	private Integer auditPart;
	/**
	 * 区域驳回至经销商次数
	 */
	@TableField(exist = false)
	private Integer areaRejectedTimes;
	/**
	 * CCMQ驳回至区域次数
	 */
	@TableField(exist = false)
	private Integer ccmqRejectedTimes;
	/**
	 * 通知/开票金额
	 */
	@TableField(exist = false)
	private BigDecimal noticeOrInvoicePrice;

	/**
	 * 
	 * 充值金额
	 */
	@TableField(exist = false)
	private BigDecimal rechargeAmount;
	/**
	 * 已消费金额
	 */
	@TableField(exist = false)
	private BigDecimal usedAmount;

	/**
	 * 剩余金额
	 */
	@TableField(exist = false)
	private BigDecimal leftAmount;

	/**
	 * 是否CCMQ翻译
	 */
	@TableField(exist = false)
	private Integer isCcmq;

	/**
	 * 审计方式
	 */
	@TableField(exist = false)
	private Integer auditWay;

	/**
	 * 审计时间
	 */
	@TableField(exist = false)
	private Date auditTimes;

	/**
	 * 问题点
	 */
	@TableField(exist = false)
	private String troubleSpots;

	/**
	 * 处罚结果
	 */
	@TableField(exist = false)
	private String punishResult;

	/**
	 * 扣款
	 */
	@TableField(exist = false)
	private BigDecimal deductionsPrice;

	/**
	 * 是否通知
	 */
	@TableField(exist = false)
	private Integer isNotification;
	/**
	 *代金券充值金额
	 */
	@TableField(exist = false)
	private BigDecimal voucherCouponFaceRechargePrice;
	/**
	 *成本费率默认70%
	 */
	@TableField("cost_rate")
	private BigDecimal costRate;

	/**
	 * 消费成本金额
	 */
	@TableField(exist = false)
	private BigDecimal costConsumeAmount;
	@TableField(exist = false)
	private  String auditName;
	@TableField(exist = false)
	private  Long  auditor;

	/**
	 * 审批角色
	 */
	@TableField(exist = false)
	private  String  auditRole;

	public String getAuditRole() {
		return auditRole;
	}

	public void setAuditRole(String auditRole) {
		this.auditRole = auditRole;
	}

	public String getAuditName() {
		return auditName;
	}

	public void setAuditName(String auditName) {
		this.auditName = auditName;
	}

	public Long getAuditor() {
		return auditor;
	}

	public void setAuditor(Long auditor) {
		this.auditor = auditor;
	}

	public BigDecimal getCostConsumeAmount() {
		return costConsumeAmount;
	}

	public void setCostConsumeAmount(BigDecimal costConsumeAmount) {
		this.costConsumeAmount = costConsumeAmount;
	}

	public BigDecimal getVoucherCouponFaceRechargePrice() {
		return voucherCouponFaceRechargePrice;
	}

	public void setVoucherCouponFaceRechargePrice(BigDecimal voucherCouponFaceRechargePrice) {
		this.voucherCouponFaceRechargePrice = voucherCouponFaceRechargePrice;
	}

	public BigDecimal getCostRate() {
		return costRate;
	}

	public void setCostRate(BigDecimal costRate) {
		this.costRate = costRate;
	}

	public Integer getAuditWay() {
		return auditWay;
	}

	public void setAuditWay(Integer auditWay) {
		this.auditWay = auditWay;
	}

	public Date getAuditTimes() {
		return auditTimes;
	}

	public void setAuditTimes(Date auditTimes) {
		this.auditTimes = auditTimes;
	}

	public String getTroubleSpots() {
		return troubleSpots;
	}

	public void setTroubleSpots(String troubleSpots) {
		this.troubleSpots = troubleSpots;
	}

	public String getPunishResult() {
		return punishResult;
	}

	public void setPunishResult(String punishResult) {
		this.punishResult = punishResult;
	}

	public BigDecimal getDeductionsPrice() {
		return deductionsPrice;
	}

	public void setDeductionsPrice(BigDecimal deductionsPrice) {
		this.deductionsPrice = deductionsPrice;
	}

	public Integer getIsNotification() {
		return isNotification;
	}

	public void setIsNotification(Integer isNotification) {
		this.isNotification = isNotification;
	}

	public Integer getIsCcmq() {
		return isCcmq;
	}

	public void setIsCcmq(Integer isCcmq) {
		this.isCcmq = isCcmq;
	}

	public BigDecimal getUsedAmount() {
		return usedAmount;
	}

	public void setUsedAmount(BigDecimal usedAmount) {
		this.usedAmount = usedAmount;
	}

	public BigDecimal getLeftAmount() {
		return leftAmount;
	}

	public void setLeftAmount(BigDecimal leftAmount) {
		this.leftAmount = leftAmount;
	}

	public BigDecimal getRechargeAmount() {
		return rechargeAmount;
	}

	public void setRechargeAmount(BigDecimal rechargeAmount) {
		this.rechargeAmount = rechargeAmount;
	}

	public BigDecimal getNoticeOrInvoicePrice() {
		return noticeOrInvoicePrice;
	}

	public void setNoticeOrInvoicePrice(BigDecimal noticeOrInvoicePrice) {
		this.noticeOrInvoicePrice = noticeOrInvoicePrice;
	}

	public Integer getAreaRejectedTimes() {
		return areaRejectedTimes;
	}

	public void setAreaRejectedTimes(Integer areaRejectedTimes) {
		this.areaRejectedTimes = areaRejectedTimes;
	}

	public Integer getCcmqRejectedTimes() {
		return ccmqRejectedTimes;
	}

	public void setCcmqRejectedTimes(Integer ccmqRejectedTimes) {
		this.ccmqRejectedTimes = ccmqRejectedTimes;
	}

	public Integer getAuditPart() {
		return auditPart;
	}

	public void setAuditPart(Integer auditPart) {
		this.auditPart = auditPart;
	}

	public BigDecimal getInvoicePrice() {
		return invoicePrice;
	}

	public void setInvoicePrice(BigDecimal invoicePrice) {
		this.invoicePrice = invoicePrice;
	}

	public BigDecimal getNoticeInvoicePrice() {
		return noticeInvoicePrice;
	}

	public void setNoticeInvoicePrice(BigDecimal noticeInvoicePrice) {
		this.noticeInvoicePrice = noticeInvoicePrice;
	}

	public String getReasonAndDispose() {
		return reasonAndDispose;
	}

	public void setReasonAndDispose(String reasonAndDispose) {
		this.reasonAndDispose = reasonAndDispose;
	}

	public String getRepairSolution() {
		return repairSolution;
	}

	public void setRepairSolution(String repairSolution) {
		this.repairSolution = repairSolution;
	}

	public String getBusinessGoodwillApplyDetail() {
		return businessGoodwillApplyDetail;
	}

	public void setBusinessGoodwillApplyDetail(String businessGoodwillApplyDetail) {
		this.businessGoodwillApplyDetail = businessGoodwillApplyDetail;
	}

	public String getNewAuditOpinion() {
		return newAuditOpinion;
	}

	public void setNewAuditOpinion(String newAuditOpinion) {
		this.newAuditOpinion = newAuditOpinion;
	}

	public Integer getDataType() {
		return dataType;
	}

	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}

	public String[] getComplaintFalut1() {
		return complaintFalut1;
	}

	public void setComplaintFalut1(String[] complaintFalut1) {
		this.complaintFalut1 = complaintFalut1;
	}

	public Integer getMaterialSummit() {
		return materialSummit;
	}

	public void setMaterialSummit(Integer materialSummit) {
		this.materialSummit = materialSummit;
	}

	public Integer getMaterialAudit() {
		return materialAudit;
	}

	public void setMaterialAudit(Integer materialAudit) {
		this.materialAudit = materialAudit;
	}

	public Integer getIsRestart() {
		return isRestart;
	}

	public void setIsRestart(Integer isRestart) {
		this.isRestart = isRestart;
	}

	public Date getNewAuditDate() {
		return newAuditDate;
	}

	public void setNewAuditDate(Date newAuditDate) {
		this.newAuditDate = newAuditDate;
	}

	public Integer getNewAuditResult() {
		return newAuditResult;
	}

	public void setNewAuditResult(Integer newAuditResult) {
		this.newAuditResult = newAuditResult;
	}

	public String getNewestAuditor() {
		return newestAuditor;
	}

	public void setNewestAuditor(String newestAuditor) {
		this.newestAuditor = newestAuditor;
	}

	public Date getUnSupportDate() {
		return unSupportDate;
	}

	public void setUnSupportDate(Date unSupportDate) {
		this.unSupportDate = unSupportDate;
	}

	public String getUnSupportReason() {
		return unSupportReason;
	}

	public void setUnSupportReason(String unSupportReason) {
		this.unSupportReason = unSupportReason;
	}

	public Date getRefuseSupportDate() {
		return refuseSupportDate;
	}

	public void setRefuseSupportDate(Date refuseSupportDate) {
		this.refuseSupportDate = refuseSupportDate;
	}

	public String getRefuseSupportReason() {
		return refuseSupportReason;
	}

	public void setRefuseSupportReason(String refuseSupportReason) {
		this.refuseSupportReason = refuseSupportReason;
	}

	public Date getRestartDate() {
		return restartDate;
	}

	public void setRestartDate(Date restartDate) {
		this.restartDate = restartDate;
	}

	public String getRestartReason() {
		return restartReason;
	}

	public void setRestartReason(String restartReason) {
		this.restartReason = restartReason;
	}

	public String getMaterialAuditLength() {
		return materialAuditLength;
	}

	public void setMaterialAuditLength(String materialAuditLength) {
		this.materialAuditLength = materialAuditLength;
	}

	public String getMaterialCommitLength() {
		return materialCommitLength;
	}

	public void setMaterialCommitLength(String materialCommitLength) {
		this.materialCommitLength = materialCommitLength;
	}

	public Integer getAuditResult() {
		return auditResult;
	}

	public void setAuditResult(Integer auditResult) {
		this.auditResult = auditResult;
	}

	public Integer getIsAudit() {
		return isAudit;
	}

	public void setIsAudit(Integer isAudit) {
		this.isAudit = isAudit;
	}

	public Date getAuditTime() {
		return auditTime;
	}

	public void setAuditTime(Date auditTime) {
		this.auditTime = auditTime;
	}

	public Integer getIsNeedTranslate() {
		return isNeedTranslate;
	}

	public void setIsNeedTranslate(Integer isNeedTranslate) {
		this.isNeedTranslate = isNeedTranslate;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Date getApplyStartTime() {
		return applyStartTime;
	}

	public void setApplyStartTime(Date applyStartTime) {
		this.applyStartTime = applyStartTime;
	}

	public Date getApplyEndTime() {
		return applyEndTime;
	}

	public void setApplyEndTime(Date applyEndTime) {
		this.applyEndTime = applyEndTime;
	}

	public Date getPassStartTime() {
		return passStartTime;
	}

	public void setPassStartTime(Date passStartTime) {
		this.passStartTime = passStartTime;
	}

	public Date getPassEndTime() {
		return passEndTime;
	}

	public void setPassEndTime(Date passEndTime) {
		this.passEndTime = passEndTime;
	}

	public BigDecimal getApplyStartAmount() {
		return applyStartAmount;
	}

	public void setApplyStartAmount(BigDecimal applyStartAmount) {
		this.applyStartAmount = applyStartAmount;
	}

	public BigDecimal getApplyEndAmount() {
		return applyEndAmount;
	}

	public void setApplyEndAmount(BigDecimal applyEndAmount) {
		this.applyEndAmount = applyEndAmount;
	}

	public Date getInvoiceStartDate() {
		return invoiceStartDate;
	}

	public void setInvoiceStartDate(Date invoiceStartDate) {
		this.invoiceStartDate = invoiceStartDate;
	}

	public Date getInvoiceEndDate() {
		return invoiceEndDate;
	}

	public void setInvoiceEndDate(Date invoiceEndDate) {
		this.invoiceEndDate = invoiceEndDate;
	}

	public Date getInvoiceDate() {
		return invoiceDate;
	}

	public void setInvoiceDate(Date invoiceDate) {
		this.invoiceDate = invoiceDate;
	}

	public Date getNoticeInvoiceStartDate() {
		return noticeInvoiceStartDate;
	}

	public void setNoticeInvoiceStartDate(Date noticeInvoiceStartDate) {
		this.noticeInvoiceStartDate = noticeInvoiceStartDate;
	}

	public Date getNoticeInvoiceEndDate() {
		return noticeInvoiceEndDate;
	}

	public void setNoticeInvoiceEndDate(Date noticeInvoiceEndDate) {
		this.noticeInvoiceEndDate = noticeInvoiceEndDate;
	}

	public Date getNoticeInvoiceDate() {
		return noticeInvoiceDate;
	}

	public void setNoticeInvoiceDate(Date noticeInvoiceDate) {
		this.noticeInvoiceDate = noticeInvoiceDate;
	}

	public Date getUpdatedStartAt() {
		return updatedStartAt;
	}

	public void setUpdatedStartAt(Date updatedStartAt) {
		this.updatedStartAt = updatedStartAt;
	}

	public Date getUpdatedEndAt() {
		return updatedEndAt;
	}

	public void setUpdatedEndAt(Date updatedEndAt) {
		this.updatedEndAt = updatedEndAt;
	}

	public Date getPassTime() {
		return passTime;
	}

	public void setPassTime(Date passTime) {
		this.passTime = passTime;
	}

	public Date getMaterialCommitTime() {
		return materialCommitTime;
	}

	public void setMaterialCommitTime(Date materialCommitTime) {
		this.materialCommitTime = materialCommitTime;
	}

	public Date getMaterialPassTime() {
		return materialPassTime;
	}

	public void setMaterialPassTime(Date materialPassTime) {
		this.materialPassTime = materialPassTime;
	}

	public String getDealerName() {
		return dealerName;
	}

	public void setDealerName(String dealerName) {
		this.dealerName = dealerName;
	}

	public Integer getAreaManageId() {
		return areaManageId;
	}

	public void setAreaManageId(Integer areaManageId) {
		this.areaManageId = areaManageId;
	}

	public String getAreaManage() {
		return areaManage;
	}

	public void setAreaManage(String areaManage) {
		this.areaManage = areaManage;
	}

	public Integer getBlocId() {
		return blocId;
	}

	public void setBlocId(Integer blocId) {
		this.blocId = blocId;
	}

	public String getBloc() {
		return bloc;
	}

	public void setBloc(String bloc) {
		this.bloc = bloc;
	}

	public String getApplyPerson() {
		return applyPerson;
	}

	public void setApplyPerson(String applyPerson) {
		this.applyPerson = applyPerson;
	}

	public GoodwillApplyInfoPO() {
		super();
	}

	@Override
	public String getAppId() {
		return appId;
	}

	@Override
	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	@Override
	public String getOwnerParCode() {
		return ownerParCode;
	}

	@Override
	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getApplyNo() {
		return applyNo;
	}

	public void setApplyNo(String applyNo) {
		this.applyNo = applyNo;
	}

	public String getDealerCode() {
		return dealerCode;
	}

	public void setDealerCode(String dealerCode) {
		this.dealerCode = dealerCode;
	}

	public Integer getSmallAreaId() {
		return smallAreaId;
	}

	public void setSmallAreaId(Integer smallAreaId) {
		this.smallAreaId = smallAreaId;
	}

	public String getSmallArea() {
		return smallArea;
	}

	public void setSmallArea(String smallArea) {
		this.smallArea = smallArea;
	}

	public Integer getAuditType() {
		return auditType;
	}

	public void setAuditType(Integer auditType) {
		this.auditType = auditType;
	}

	public Integer getGoodwillNature() {
		return goodwillNature;
	}

	public void setGoodwillNature(Integer goodwillNature) {
		this.goodwillNature = goodwillNature;
	}

	public String getComplaintFalut() {
		return complaintFalut;
	}

	public void setComplaintFalut(String complaintFalut) {
		this.complaintFalut = complaintFalut;
	}

	public String getComplaintId() {
		return complaintId;
	}

	public void setComplaintId(String complaintId) {
		this.complaintId = complaintId;
	}

	public Date getComplaintDate() {
		return complaintDate;
	}

	public void setComplaintDate(Date complaintDate) {
		this.complaintDate = complaintDate;
	}

	public Integer getVehicleUse() {
		return vehicleUse;
	}

	public void setVehicleUse(Integer vehicleUse) {
		this.vehicleUse = vehicleUse;
	}

	public String getSalesDealer() {
		return salesDealer;
	}

	public void setSalesDealer(String salesDealer) {
		this.salesDealer = salesDealer;
	}

	public BigDecimal getApplyAmount() {
		return applyAmount;
	}

	public void setApplyAmount(BigDecimal applyAmount) {
		this.applyAmount = applyAmount;
	}

	public BigDecimal getAuditAmount() {
		return auditAmount;
	}

	public void setAuditAmount(BigDecimal auditAmount) {
		this.auditAmount = auditAmount;
	}

	public BigDecimal getSettlementAmount() {
		return settlementAmount;
	}

	public void setSettlementAmount(BigDecimal settlementAmount) {
		this.settlementAmount = settlementAmount;
	}

	public BigDecimal getInvoiceAmount() {
		return invoiceAmount;
	}

	public void setInvoiceAmount(BigDecimal invoiceAmount) {
		this.invoiceAmount = invoiceAmount;
	}

	public Integer getGoodwillStatus() {
		return goodwillStatus;
	}

	public void setGoodwillStatus(Integer goodwillStatus) {
		this.goodwillStatus = goodwillStatus;
	}

	public Integer getLastGoodwillStatus() {
		return lastGoodwillStatus;
	}

	public void setLastGoodwillStatus(Integer lastGoodwillStatus) {
		this.lastGoodwillStatus = lastGoodwillStatus;
	}

	public String getCustomerPain() {
		return customerPain;
	}

	public void setCustomerPain(String customerPain) {
		this.customerPain = customerPain;
	}

	public String getVin() {
		return vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public String getLicense() {
		return license;
	}

	public void setLicense(String license) {
		this.license = license;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getCustomerMobile() {
		return customerMobile;
	}

	public void setCustomerMobile(String customerMobile) {
		this.customerMobile = customerMobile;
	}

	public String getMileage() {
		return mileage;
	}

	public void setMileage(String mileage) {
		this.mileage = mileage;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public Integer getIsExtendWarranty() {
		return isExtendWarranty;
	}

	public void setIsExtendWarranty(Integer isExtendWarranty) {
		this.isExtendWarranty = isExtendWarranty;
	}

	public String getExtendWarrantyName() {
		return extendWarrantyName;
	}

	public void setExtendWarrantyName(String extendWarrantyName) {
		this.extendWarrantyName = extendWarrantyName;
	}

	public BigDecimal getMaintainCost() {
		return maintainCost;
	}

	public void setMaintainCost(BigDecimal maintainCost) {
		this.maintainCost = maintainCost;
	}

	public BigDecimal getExtendWarrantyCost() {
		return extendWarrantyCost;
	}

	public void setExtendWarrantyCost(BigDecimal extendWarrantyCost) {
		this.extendWarrantyCost = extendWarrantyCost;
	}

	public BigDecimal getAccessoryCost() {
		return accessoryCost;
	}

	public void setAccessoryCost(BigDecimal accessoryCost) {
		this.accessoryCost = accessoryCost;
	}

	public BigDecimal getVoucherCost() {
		return voucherCost;
	}

	public void setVoucherCost(BigDecimal voucherCost) {
		this.voucherCost = voucherCost;
	}

	public BigDecimal getWalkingCarPrice() {
		return walkingCarPrice;
	}

	public void setWalkingCarPrice(BigDecimal walkingCarPrice) {
		this.walkingCarPrice = walkingCarPrice;
	}

	public BigDecimal getVolvoIntegral() {
		return volvoIntegral;
	}

	public void setVolvoIntegral(BigDecimal volvoIntegral) {
		this.volvoIntegral = volvoIntegral;
	}

	public BigDecimal getReturnChangeCarPrice() {
		return returnChangeCarPrice;
	}

	public void setReturnChangeCarPrice(BigDecimal returnChangeCarPrice) {
		this.returnChangeCarPrice = returnChangeCarPrice;
	}

	public BigDecimal getOtherPrice() {
		return otherPrice;
	}

	public void setOtherPrice(BigDecimal otherPrice) {
		this.otherPrice = otherPrice;
	}

	public BigDecimal getCostTotal() {
		return costTotal;
	}

	public void setCostTotal(BigDecimal costTotal) {
		this.costTotal = costTotal;
	}

	public BigDecimal getCustomerPay() {
		return customerPay;
	}

	public void setCustomerPay(BigDecimal customerPay) {
		this.customerPay = customerPay;
	}

	public BigDecimal getDealerUndertake() {
		return dealerUndertake;
	}

	public void setDealerUndertake(BigDecimal dealerUndertake) {
		this.dealerUndertake = dealerUndertake;
	}

	public BigDecimal getVolvoSupportGoodwillAmount() {
		return volvoSupportGoodwillAmount;
	}

	public void setVolvoSupportGoodwillAmount(BigDecimal volvoSupportGoodwillAmount) {
		this.volvoSupportGoodwillAmount = volvoSupportGoodwillAmount;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getApplyFile() {
		return applyFile;
	}

	public void setApplyFile(String applyFile) {
		this.applyFile = applyFile;
	}

	public String getCostStatisticsFile() {
		return costStatisticsFile;
	}

	public void setCostStatisticsFile(String costStatisticsFile) {
		this.costStatisticsFile = costStatisticsFile;
	}

	public String getCostScreenshotFile() {
		return costScreenshotFile;
	}

	public void setCostScreenshotFile(String costScreenshotFile) {
		this.costScreenshotFile = costScreenshotFile;
	}

	public String getRingCheckFile() {
		return ringCheckFile;
	}

	public void setRingCheckFile(String ringCheckFile) {
		this.ringCheckFile = ringCheckFile;
	}

	public String getTroubleRepairRequisitionFile() {
		return troubleRepairRequisitionFile;
	}

	public void setTroubleRepairRequisitionFile(String troubleRepairRequisitionFile) {
		this.troubleRepairRequisitionFile = troubleRepairRequisitionFile;
	}

	public String getWorkOrderFile() {
		return workOrderFile;
	}

	public void setWorkOrderFile(String workOrderFile) {
		this.workOrderFile = workOrderFile;
	}

	public String getSituationSettlementAgreementFile() {
		return situationSettlementAgreementFile;
	}

	public void setSituationSettlementAgreementFile(String situationSettlementAgreementFile) {
		this.situationSettlementAgreementFile = situationSettlementAgreementFile;
	}

	public String getSupplementaryMaterialFile() {
		return supplementaryMaterialFile;
	}

	public void setSupplementaryMaterialFile(String supplementaryMaterialFile) {
		this.supplementaryMaterialFile = supplementaryMaterialFile;
	}

	public String getManagementReviewEmailVpFile() {
		return managementReviewEmailVpFile;
	}

	public void setManagementReviewEmailVpFile(String managementReviewEmailVpFile) {
		this.managementReviewEmailVpFile = managementReviewEmailVpFile;
	}

	public String getManagementReviewEmailCeoFile() {
		return managementReviewEmailCeoFile;
	}

	public void setManagementReviewEmailCeoFile(String managementReviewEmailCeoFile) {
		this.managementReviewEmailCeoFile = managementReviewEmailCeoFile;
	}

	public String getCostUpdateFile() {
		return costUpdateFile;
	}

	public void setCostUpdateFile(String costUpdateFile) {
		this.costUpdateFile = costUpdateFile;
	}

	public String getVcdcElseFile() {
		return vcdcElseFile;
	}

	public void setVcdcElseFile(String vcdcElseFile) {
		this.vcdcElseFile = vcdcElseFile;
	}

	public String getCustomerIdentification() {
		return customerIdentification;
	}

	public void setCustomerIdentification(String customerIdentification) {
		this.customerIdentification = customerIdentification;
	}

	public String getElseFile() {
		return elseFile;
	}

	public void setElseFile(String elseFile) {
		this.elseFile = elseFile;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Date getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(Date applyTime) {
		this.applyTime = applyTime;
	}

	public Date getBuyCarDate() {
		return buyCarDate;
	}

	public void setBuyCarDate(Date buyCarDate) {
		this.buyCarDate = buyCarDate;
	}

	public Date getWarrantyStartDate() {
		return warrantyStartDate;
	}

	public void setWarrantyStartDate(Date warrantyStartDate) {
		this.warrantyStartDate = warrantyStartDate;
	}

	public Date getExtendWarrantyStartDate() {
		return extendWarrantyStartDate;
	}

	public void setExtendWarrantyStartDate(Date extendWarrantyStartDate) {
		this.extendWarrantyStartDate = extendWarrantyStartDate;
	}

	public Date getExtendWarrantyEndDate() {
		return extendWarrantyEndDate;
	}

	public void setExtendWarrantyEndDate(Date extendWarrantyEndDate) {
		this.extendWarrantyEndDate = extendWarrantyEndDate;
	}

	public Date getCommitTime() {
		return commitTime;
	}

	public void setCommitTime(Date commitTime) {
		this.commitTime = commitTime;
	}

	@Override
	public Date getCreatedAt() {
		return createdAt;
	}

	@Override
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Override
	public Date getUpdatedAt() {
		return updatedAt;
	}

	@Override
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public BigDecimal getVoucherInvoiceAmount() {
		return voucherInvoiceAmount;
	}

	public void setVoucherInvoiceAmount(BigDecimal voucherInvoiceAmount) {
		this.voucherInvoiceAmount = voucherInvoiceAmount;
	}

	public BigDecimal getVoucherNotInvoiceAmount() {
		return voucherNotInvoiceAmount;
	}

	public void setVoucherNotInvoiceAmount(BigDecimal voucherNotInvoiceAmount) {
		this.voucherNotInvoiceAmount = voucherNotInvoiceAmount;
	}

	@Override
	protected Serializable pkVal() {
		return this.id;
	}

	public String getUpdatedPerson() {
		return updatedPerson;
	}

	public void setUpdatedPerson(String updatedPerson) {
		this.updatedPerson = updatedPerson;
	}

	public String getOwnerNo() {
		return ownerNo;
	}

	public void setOwnerNo(String ownerNo) {
		this.ownerNo = ownerNo;
	}

	public Long getCouponId() {
		return couponId;
	}

	public void setCouponId(Long couponId) {
		this.couponId = couponId;
	}

	@Override
	public String toString() {
		return "GoodwillApplyInfoPO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode=" + ownerParCode
				+ ", orgId=" + orgId + ", id=" + id + ", applyNo=" + applyNo + ", dealerCode=" + dealerCode
				+ ", dealerName=" + dealerName + ", bloc=" + bloc + ", blocId=" + blocId + ", areaManageId="
				+ areaManageId + ", areaManage=" + areaManage + ", auditType=" + auditType + ", goodwillNature="
				+ goodwillNature + ", applyTime=" + applyTime + ", complaintFalut=" + complaintFalut + ", complaintId="
				+ complaintId + ", complaintDate=" + complaintDate + ", vehicleUse=" + vehicleUse + ", salesDealer="
				+ salesDealer + ", applyAmount=" + applyAmount + ", auditAmount=" + auditAmount + ", settlementAmount="
				+ settlementAmount + ", invoiceAmount=" + invoiceAmount + ", goodwillStatus=" + goodwillStatus
				+ ", customerPain=" + customerPain + ", vin=" + vin + ", license=" + license + ", customerName="
				+ customerName + ", customerMobile=" + customerMobile + ", mileage=" + mileage + ", model=" + model
				+ ", buyCarDate=" + buyCarDate + ", warrantyStartDate=" + warrantyStartDate + ", isExtendWarranty="
				+ isExtendWarranty + ", extendWarrantyName=" + extendWarrantyName + ", extendWarrantyStartDate="
				+ extendWarrantyStartDate + ", extendWarrantyEndDate=" + extendWarrantyEndDate + ", maintainCost="
				+ maintainCost + ", extendWarrantyCost=" + extendWarrantyCost + ", accessoryCost=" + accessoryCost
				+ ", voucherCost=" + voucherCost + ", walkingCarPrice=" + walkingCarPrice + ", volvoIntegral="
				+ volvoIntegral + ", returnChangeCarPrice=" + returnChangeCarPrice + ", otherPrice=" + otherPrice
				+ ", costTotal=" + costTotal + ", customerPay=" + customerPay + ", dealerUndertake=" + dealerUndertake
				+ ", volvoSupportGoodwillAmount=" + volvoSupportGoodwillAmount + ", remark=" + remark + ", applyFile="
				+ applyFile + ", costStatisticsFile=" + costStatisticsFile + ", costScreenshotFile="
				+ costScreenshotFile + ", ringCheckFile=" + ringCheckFile + ", troubleRepairRequisitionFile="
				+ troubleRepairRequisitionFile + ", workOrderFile=" + workOrderFile
				+ ", situationSettlementAgreementFile=" + situationSettlementAgreementFile
				+ ", supplementaryMaterialFile=" + supplementaryMaterialFile + ", managementReviewEmailVpFile="
				+ managementReviewEmailVpFile + ", managementReviewEmailCeoFile=" + managementReviewEmailCeoFile
				+ ", costUpdateFile=" + costUpdateFile + ", vcdcElseFile=" + vcdcElseFile + ", customerIdentification="
				+ customerIdentification + ", elseFile=" + elseFile + ", commitTime=" + commitTime + ", isValid="
				+ isValid + ",voucherCouponFaceRechargePrice="+voucherCouponFaceRechargePrice+",costConsumeAmount="+costConsumeAmount+",auditName="+auditName+",auditor="+auditor+
				",costRate="+costRate+", isDeleted=" + isDeleted + ", createdAt=" + createdAt + ", updatedAt=" + updatedAt + "}";
	}
	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dtoClass
	 *            需要进行转换的dtoClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
		return super.transDtoToPo(dtoClass);
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dto
	 *            需要进行转换的dto
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> void transPoToDto(T dto) {
		BeanMapperUtil.copyProperties(this, dto, "id");
	}

}
