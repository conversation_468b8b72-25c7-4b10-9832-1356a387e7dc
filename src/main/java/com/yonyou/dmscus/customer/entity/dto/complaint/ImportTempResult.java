package com.yonyou.dmscus.customer.entity.dto.complaint;


import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "临时表导入返回实体")
public class ImportTempResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    private int successCount;

    private List<T> errorList;

    private List<T> successList;
    
    private String errorMsg;

    public int getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(int successCount) {
        this.successCount = successCount;
    }

    public List<T> getErrorList() {
        return errorList;
    }

    public void setErrorList(List<T> errorList) {
        this.errorList = errorList;
    }

    public List<T> getSuccessList() {
        return successList;
    }

    public void setSuccessList(List<T> successList) {
        this.successList = successList;
    }

	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}

}
