package com.yonyou.dmscus.customer.entity.po.talkskill;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 话术使用页面
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-20
 */
@TableName("tt_talkskill_page")
@Data
public class TalkskillPagePO extends BasePO<TalkskillPagePO> {

    private static final long serialVersionUID=1L;

    /**
     * 主键ID
     */
    @TableId(value = "page_id", type = IdType.AUTO)
    private Long pageId;

    /**
     * 页面名称
     */
    @TableField("page_name")
    private String pageName;

    /**
     * 标签名称
     */
    @TableField("tag_name")
    private String tagName;

    @TableField("created_at")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;

    public TalkskillPagePO(){
        super();
    }


    @Override
    protected Serializable pkVal(){
            return this.pageId;
        }


/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"typeId");
    }

}
