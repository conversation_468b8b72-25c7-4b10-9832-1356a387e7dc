package com.yonyou.dmscus.customer.entity.po.userCode;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: BiReportUserInfo
 * @projectName server2
 * @description: TODO
 * @date 2022/7/1915:14
 */
@Data
@TableName("tt_user_code_info")
public class UserCodeInfoPo {


    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 经销商/,集团
     */
    @TableField("belonging")
    private String belonging;

    /**
     * 经销商代码/所属集团
     */
    @TableField("belonging_name")
    private String belongingName;

    /**
     * 姓名
     */
    @TableField("employee_name")
    private String employeeName;


    /**
     * 员工编号
     */
    @TableField("employee_no")
    private String employeeNo;

    /**
     * 岗位
     */
    @TableField("position")
    private String position;


    /**
     *newbie登录账号
     */
    @TableField("user_code")
    private String userCode;


    /**
     *手机号
     */
    @TableField("mobile")
    private String mobile;


    /**
     *邮箱
     */
    @TableField("email")
    private String email;


    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;



    //创建时间"
    @TableField("created_at")
    private Date createdAt;

    //创建人"
    @TableField("created_by")
    private String createdBy;

    //更新时间"
    @TableField("updated_at")
    private Date updatedAt;

    //更新人
    @TableField("updated_by")
    private String updatedBy;

    //版本号（乐观锁）
    @TableField("record_version")
    private Integer recordVersion;



}
