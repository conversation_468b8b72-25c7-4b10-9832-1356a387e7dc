package com.yonyou.dmscus.customer.entity.dto.complaint.sale;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.function.utils.jsonSerializer.date.JsonDateDeserializer;
import com.yonyou.dmscloud.function.utils.jsonSerializer.date.JsonDateSerializer;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 销售客户投诉KP基础规则使用表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-21
 */
    
public class SaleComplaintKpiBaseRuleUseDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

        
    /**
     * 系统ID
     */
                private String appId;
                
    /**
     * 所有者代码 
     */
                private String ownerCode;
                
    /**
     * 所有者的父组织代码 
     */
                private String ownerParCode;
                
    /**
     * 组织ID
     */
                private Integer orgId;
                
    /**
     * 主键ID
     */
                private Long id;
                
    /**
     * 规则ID
     */
                private Long ruleId;
                
    /**
     * 进销商代码 厂端用VCDC
     */
                private String dealerCode;
                
    /**
     * 使用人，主要用于VCDC人员。 进销商不到具体使用人
     */
                private String user;
                
    /**
     * 警戒值
     */
                private String warnValue;
                
    /**
     * 数据来源 
     */
                private Integer dataSources;
                
    /**
     * 是否删除
     */
                private Boolean isDeleted;
                
    /**
     * 是否有效 
     */
                private Integer isValid;
                
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createdAt;
                
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updatedAt;
            
    public SaleComplaintKpiBaseRuleUseDTO() {
        super();
    }

                                
    public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
            }
                                
    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
            }
                                
    public String getOwnerParCode(){
        return ownerParCode;
    }


    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
            }
                                
    public Integer getOrgId(){
        return orgId;
    }


    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
            }
                                            
    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
            }
                                
    public Long getRuleId(){
        return ruleId;
    }


    public void  setRuleId(Long ruleId) {
        this.ruleId = ruleId;
            }
                                
    public String getDealerCode(){
        return dealerCode;
    }


    public void  setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
            }
                                
    public String getUser(){
        return user;
    }


    public void  setUser(String user) {
        this.user = user;
            }
                                
    public String getWarnValue(){
        return warnValue;
    }


    public void  setWarnValue(String warnValue) {
        this.warnValue = warnValue;
            }
                                
    public Integer getDataSources(){
        return dataSources;
    }


    public void  setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
            }
                                
    public Integer getIsValid(){
        return isValid;
    }


    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
            }
                                
    public Date getCreatedAt(){
        return createdAt;
    }


    public void  setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
            }
                                
    public Date getUpdatedAt(){
        return updatedAt;
    }


    public void  setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
            }
    
    @Override
    public String toString() {
        return "SaleComplaintKpiBaseRuleUseDTO{" +
                                            "appId=" + appId +
                                                            ", ownerCode=" + ownerCode +
                                                            ", ownerParCode=" + ownerParCode +
                                                            ", orgId=" + orgId +
                                                            ", id=" + id +
                                                            ", ruleId=" + ruleId +
                                                            ", dealerCode=" + dealerCode +
                                                            ", user=" + user +
                                                            ", warnValue=" + warnValue +
                                                            ", dataSources=" + dataSources +
                                                            ", isDeleted=" + isDeleted +
                                                            ", isValid=" + isValid +
                                                            ", createdAt=" + createdAt +
                                                            ", updatedAt=" + updatedAt +
                                    "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
