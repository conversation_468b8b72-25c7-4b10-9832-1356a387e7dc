package com.yonyou.dmscus.customer.entity.po.faultLight;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("故障灯预约记录表")
@TableName("tt_fault_light_invitation")
public class TtFaultLightInvitationPO{

    private static final long serialVersionUID = -2057595274752626166L;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @TableField("id")
    private Long id;

    /**
     * 线索id
     */
    @ApiModelProperty("线索id")
    @TableField("clue_id")
    private Long clueId;

    /**
     * 联络时间
     */
    @ApiModelProperty("联络时间")
    @TableField("contact_time")
    private Date contactTime;

    /**
     * 联络人
     */
    @ApiModelProperty("联络人")
    @TableField("contacts_name")
    private String contactsName;

    /**
     * 联络响应时间（h）
     */
    @ApiModelProperty("联络响应时间（h）")
    @TableField("contact_res_time")
    private String contactResTime;

    /**
     * 联络是否超时: 0.无 1.否 2.是
     */
    @ApiModelProperty("联络是否超时: 0.无 1.否 2.是")
    @TableField("contact_overtime")
    private Integer contactOvertime;

    /**
     * 联络结果
     */
    @ApiModelProperty("联络结果")
    @TableField("contact_result")
    private String contactResult;

    /**
     * 外呼备注
     */
    @ApiModelProperty("外呼备注")
    @TableField("comments")
    private String comments;

    /**
     * 下发dlr
     */
    @ApiModelProperty("下发dlr")
    @TableField("dlr")
    private String dlr;

    /**
     * 是否为推荐dlr
     * 是：车主未指定经销商，由400人员建议一个经销商/否：车主自己指定了经销商/空：电话没打通
     * 0: 空, 1:否, 2:是
     */
    @ApiModelProperty("是否为推荐dlr")
    @TableField("is_dlr")
    private Integer isDlr;

    /**
     * 线索下发时间
     */
    @ApiModelProperty("线索下发时间")
    @TableField("clue_dis_time")
    private Date clueDisTime;

    /**
     * 客户姓名
     */
    @ApiModelProperty("客户姓名")
    @TableField("cus_name")
    private String cusName;

    /**
     * 客户电话
     */
    @ApiModelProperty("客户电话")
    @TableField("cus_phone")
    private String cusPhone;

    /**
     * 客户性别
     */
    @ApiModelProperty("客户性别")
    @TableField("gender")
    private String gender;

    /**
     * '客户年龄'
     */
    @ApiModelProperty("'客户年龄'")
    @TableField("gender")
    private String age;

    /**
     * 邀约时间
     */
    @ApiModelProperty("邀约时间")
    @TableField("invite_time")
    private Date inviteTime;

    /**
     * 邀约人
     */
    @ApiModelProperty("邀约人")
    @TableField("invite_name")
    private String inviteName;

    /**
     * 邀约人id
     */
    @ApiModelProperty("邀约人ID")
    @TableField("SA_ID")
    private String saId;

    /**
     * 邀约响应时间(h)
     */
    @ApiModelProperty("邀约响应时间(h)")
    @TableField("invite_res_time")
    private String inviteResTime;

    /**
     * 邀约响应是否超时 0.没有超时 1.超时
     */
    @ApiModelProperty("邀约响应是否超时 0.无 1.没有超时 2.超时")
    @TableField("invite_overtime")
    private Integer inviteOvertime;

    /**
     * 邀约结果是否进店: 0.无 1.预约失败 2.预约成功
     */
    @ApiModelProperty("邀约结果是否进店: 0.无 1.预约失败 2.预约成功")
    @TableField("invite_result")
    private Integer inviteResult;

    /**
     * 预约进店时间
     */
    @ApiModelProperty("预约进店时间")
    @TableField("forecast_time")
    private Date forecastTime;

    /**
     * 进店时间
     */
    @ApiModelProperty("进店时间")
    @TableField("into_time")
    private Date intoTime;

    /**
     * 是否按时进店: 0.无 1.否 2.是
     */
    @ApiModelProperty("是否按时进店: 0.无 1.否 2.是")
    @TableField("into_on_time")
    private Integer intoOnTime;

    /**
     * 是否爽约未进店: 0.无 1.否 2.是
     */
    @ApiModelProperty("是否爽约未进店: 0.无 1.否 2.是")
    @TableField("no_into")
    private Integer noInto;

    /**
     * 是否自然进店: 0.无 1.否 2.是
     */
    @ApiModelProperty("是否自然进店: 0.无 1.否 2.是")
    @TableField("self_into")
    private Integer selfInto;

    /**
     * 失败备注
     */
    @ApiModelProperty("失败备注")
    @TableField("failure_reason")
    private String failureReason;

    /**
     * 二次预约时间
     */
    @ApiModelProperty("二次预约时间")
    @TableField("re_invite_time")
    private Date reInviteTime;


    /**
     * 是否删除:1，删除；0，未删除
     */
    @ApiModelProperty("是否删除:1，删除；0，未删除")
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 数据创建时间
     */
    @ApiModelProperty("数据创建时间")
    @TableField("created_at")
    private Date createdAt;

    /**
     * 数据创建人
     */
    @ApiModelProperty("数据创建人")
    @TableField("created_by")
    private String createdBy;

    /**
     * 数据修改时间
     */
    @ApiModelProperty("数据修改时间")
    @TableField("updated_at")
    private Date updatedAt;

    /**
     * 数据修改人
     */
    @ApiModelProperty("数据修改人")
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 创建sql人
     */
    @ApiModelProperty("创建sql人")
    @TableField("create_sqlby")
    private String createSqlby;

    /**
     * 更新人sql人
     */
    @ApiModelProperty("更新人sql人")
    @TableField("update_sqlby")
    private String updateSqlby;
}
