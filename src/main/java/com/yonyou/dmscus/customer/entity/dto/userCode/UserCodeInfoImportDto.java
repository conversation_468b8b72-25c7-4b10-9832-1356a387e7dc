package com.yonyou.dmscus.customer.entity.dto.userCode;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: BiReportUserInfo
 * @projectvalue server2
 * @description: TODO
 * @date 2022/7/1915:14
 */
@Data
@ApiModel("app权限数据")
public class UserCodeInfoImportDto extends BaseDTO {


    /**
     * ID
     */
    @ApiModelProperty(value = "id",example ="1")
    private Long id;
    /**
     * 经销商/,集团
     */
     @ApiModelProperty(value ="经销商/集团",example ="经销商")
    private String belonging;

    /**
     * 经销商代码/所属集团
     */
     @ApiModelProperty(value ="经销商代码/所属集团",example ="SHJ")
    private String belongingName;

    /**
     * 姓名
     */
     @ApiModelProperty(value ="姓名",example ="姓名")
    private String employeeName;


    /**
     * 员工编号
     */
     @ApiModelProperty(value ="员工编号",example ="SDSSSS0001")
    private String employeeNo;

    /**
     * 岗位
     */
     @ApiModelProperty(value ="岗位",example ="服务经理")
    private String position;


    /**
     *newbie登录账号
     */
     @ApiModelProperty(value ="newbie登录账号",example ="test")
    private String userCode;


    /**
     *手机号
     */
     @ApiModelProperty(value ="手机号",example ="13000000000")
    private String mobile;


    /**
     *邮箱
     */
     @ApiModelProperty(value ="邮箱",example ="<EMAIL>")
    private String email;


    /**
     * 是否有效
     */
     @ApiModelProperty(value ="是否有效是：10041001，否：10041002",example ="10041001")
    private Integer isValid;


    //创建时间"
     @ApiModelProperty(value ="创建时间")
    private Date createdAt;

    //创建人"
     @ApiModelProperty(value ="创建人")
    private String createdBy;

    //更新时间"
     @ApiModelProperty(value ="更新时间")
    private Date updatedAt;

    //更新人
     @ApiModelProperty(value ="更新人")
    private String updatedBy;

    /**
     * 是否导入数据有错误, 1有0否
     */
    @ApiModelProperty(value ="是否导入数据有错误, 1有0否")
    private Integer isError;
    /**
     * 错误信息
     */
    @ApiModelProperty(value ="错误信息")
    private String errorMsg;
    /**
     * 行数
     */
    @ApiModelProperty(value ="行数")
    private Integer lineNumber;
    /**
     * 将DTO 转换为PO //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass
     *            需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po
     *            需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
