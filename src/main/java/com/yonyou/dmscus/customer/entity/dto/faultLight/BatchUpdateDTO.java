package com.yonyou.dmscus.customer.entity.dto.faultLight;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BatchUpdateDTO implements Serializable {

    private static final long serialVersionUID = 6995570272455274526L;

    private List<Long> id;

    /**
     * 线索生成状态:1，启用；0，未启用
     */
    private Integer produceStatus;

    /**
     * 责任验证状态:1，启用；0，未启用
     */
    private Integer dutyStatus;

    /**
     * 次数
     */
    private Integer num;

    /**
     * 过期天数
     */
    private Integer expireNumber;
}
