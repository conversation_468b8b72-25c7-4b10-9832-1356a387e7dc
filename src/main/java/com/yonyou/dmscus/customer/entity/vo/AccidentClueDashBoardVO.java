package com.yonyou.dmscus.customer.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/2 14:41
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "AccidentClueDashBoardVO", description = "事故线索看板")
public class AccidentClueDashBoardVO {

    @ApiModelProperty(name = "线索数量")
    private Integer totalCount;

    @ApiModelProperty(name = "未跟进线索数量")
    private Integer notFollowCount;

    @ApiModelProperty(name = "未跟进率")
    private BigDecimal notFollowRate;

    @ApiModelProperty(name = "未跟进周环比")
    private String notFollowWow;

    @ApiModelProperty(name = "未跟进月环比")
    private String notFollowMom;

    @ApiModelProperty(name = "跟进中线索数量")
    private Integer followUpCount;

    @ApiModelProperty(name = "跟进中率")
    private BigDecimal followUpRate;

    @ApiModelProperty(name = "跟进中周环比")
    private String followUpWow;

    @ApiModelProperty(name = "跟进中月环比")
    private String followUpMom;

    @ApiModelProperty(name = "跟进成功数")
    private Integer successFollowCount;

    @ApiModelProperty(name = "跟进成功率")
    private BigDecimal successFollowRate;

    @ApiModelProperty(name = "跟进成功周环比")
    private String successFollowWow;

    @ApiModelProperty(name = "跟进成功月环比")
    private String successFollowMom;

    @ApiModelProperty(name = "跟进失败数")
    private Integer followFailCount;

    @ApiModelProperty(name = "跟进失败率")
    private BigDecimal followFailRate;

    @ApiModelProperty(name = "跟进失败周环比")
    private String followFailWow;

    @ApiModelProperty(name = "跟进失败月环比")
    private String followFailMom;

    @ApiModelProperty(name = "柱状图列表")
    private List<HistogramDataVO> histogramList;

    @ApiModelProperty(name = "日期")
    private List<String> histogramDateList;

    @ApiModelProperty(name = "跟进人列表")
    private List<String> followPeopleNameList;

    @ApiModelProperty(name = "成功数")
    private List<Integer> successCountList;

    @ApiModelProperty(name = "失败数")
    private List<Integer> failCountList;

    @ApiModelProperty(name = "送修留修率")
    private List<BigDecimal> repairRateList;

    @ApiModelProperty(name = "返修留修率")
    private List<BigDecimal> backRepairRateList;

    /**
     * 构建空看板对象
     * @return
     */
    public static AccidentClueDashBoardVO buildEmptyVo() {

        return new AccidentClueDashBoardVO().setNotFollowCount(0).setNotFollowRate(BigDecimal.ZERO)
                .setFollowUpCount(0).setFollowUpRate(BigDecimal.ZERO)
                .setSuccessFollowCount(0).setSuccessFollowRate(BigDecimal.ZERO)
                .setFollowFailCount(0).setFollowFailRate(BigDecimal.ZERO);
    }

    /**
     * 初始化柱状图数值
     * @param dashBoardInfo
     * @param capacity
     */
    public void initHistogramData(AccidentClueDashBoardVO dashBoardInfo, int capacity){

        dashBoardInfo.setHistogramDateList(new ArrayList<>(capacity));
        dashBoardInfo.setSuccessCountList(new ArrayList<>(capacity));
        dashBoardInfo.setFailCountList(new ArrayList<>(capacity));
        dashBoardInfo.setRepairRateList(new ArrayList<>(capacity));
        dashBoardInfo.setBackRepairRateList(new ArrayList<>(capacity));
        dashBoardInfo.setFollowPeopleNameList(new ArrayList<>(capacity));
    }
}
