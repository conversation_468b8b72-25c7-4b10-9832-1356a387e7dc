package com.yonyou.dmscus.customer.entity.dto.goodwill;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善管理通知开票信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-17
 */

public class GoodwillNoticeInvoiceInfoDTO extends BaseDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	private String appId;

	/**
	 * 所有者代码
	 */
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	private Integer orgId;

	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 亲善单id
	 */
	private Long goodwillApplyId;

	/**
	 * 开票id
	 */
	private String invoiceId;
	/**
	 * 开票对象
	 */
	private Integer invoiceObject;

	/**
	 * 开票抬头
	 */
	private String invoiceTitle;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 纳税人识别号
	 */
	private String taxpayerIdentificationNumber;

	/**
	 * 电话
	 */
	private String phone;

	/**
	 * 地址
	 */
	private String address;

	/**
	 * 开户行
	 */
	private String openBank;

	/**
	 * 账号
	 */
	private String account;

	/**
	 * 通知开票日期
	 */
	private Date noticeInvoiceDate;

	/**
	 * 通知开票金额
	 */
	private BigDecimal noticeInvoicePrice;

	/**
	 * 代金券充值成本金额
	 */
	private BigDecimal voucherRechargePrice;
	/**
	 * 代金券充值金额 :与最终结算无关,直接充值关联到工单账户和沃世界
	 */
	private  BigDecimal voucherCouponFaceRechargePrice;
	/**
	 * 沃世界积分充值金额
	 */
	private BigDecimal volvoCreditsRechargePrice;

	/**
	 * 代金券通知类型
	 */
	private Integer voucherType;
	/**
	 * 充值剩余金额
	 */
	private BigDecimal rechargePrice;
	/**
	 * 是否提交
	 */
	private Integer isCommit;
	/**
	 * 是否确认
	 */
	private Integer isConfirm;
	/**
	 * 是否有效
	 */
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	private Date createdAt;

	/**
	 * 修改时间
	 */
	private Date updatedAt;
	/**
	 * 代金券充值状态
	 */
	private Integer voucherStatus;

	/**
	 * 代金券充值时间
	 */
	private Date voucherTime;
	/**
	 * 积分充值状态
	 */
	private Integer creditsStatus;

	/**
	 * 积分充值时间
	 */
	private Date creditsTime;

	/**
	 * 录入发票信息
	 */
	private List<GoodwillInvoiceRecordDTO> goodwillInvoiceRecordDTO;
	/**
	 * 消费ID
	 */
	private String consumeId;


	public String getConsumeId() {
		return consumeId;
	}

	public void setConsumeId(String consumeId) {
		this.consumeId = consumeId;
	}

	public Integer getVoucherStatus() {
		return voucherStatus;
	}

	public void setVoucherStatus(Integer voucherStatus) {
		this.voucherStatus = voucherStatus;
	}

	public Date getVoucherTime() {
		return voucherTime;
	}

	public void setVoucherTime(Date voucherTime) {
		this.voucherTime = voucherTime;
	}

	public Integer getCreditsStatus() {
		return creditsStatus;
	}

	public void setCreditsStatus(Integer creditsStatus) {
		this.creditsStatus = creditsStatus;
	}

	public Date getCreditsTime() {
		return creditsTime;
	}

	public void setCreditsTime(Date creditsTime) {
		this.creditsTime = creditsTime;
	}

	public Integer getIsConfirm() {
		return isConfirm;
	}

	public void setIsConfirm(Integer isConfirm) {
		this.isConfirm = isConfirm;
	}

	public Integer getIsCommit() {
		return isCommit;
	}

	public void setIsCommit(Integer isCommit) {
		this.isCommit = isCommit;
	}

	public Integer getInvoiceObject() {
		return invoiceObject;
	}

	public void setInvoiceObject(Integer invoiceObject) {
		this.invoiceObject = invoiceObject;
	}

	public Integer getVoucherType() {
		return voucherType;
	}

	public void setVoucherType(Integer voucherType) {
		this.voucherType = voucherType;
	}

	public BigDecimal getRechargePrice() {
		return rechargePrice;
	}

	public void setRechargePrice(BigDecimal rechargePrice) {
		this.rechargePrice = rechargePrice;
	}

	public List<GoodwillInvoiceRecordDTO> getGoodwillInvoiceRecordDTO() {
		return goodwillInvoiceRecordDTO;
	}

	public void setGoodwillInvoiceRecordDTO(List<GoodwillInvoiceRecordDTO> goodwillInvoiceRecordDTO) {
		this.goodwillInvoiceRecordDTO = goodwillInvoiceRecordDTO;
	}

	public Date getNoticeInvoiceDate() {
		return noticeInvoiceDate;
	}

	public void setNoticeInvoiceDate(Date noticeInvoiceDate) {
		this.noticeInvoiceDate = noticeInvoiceDate;
	}

	public GoodwillNoticeInvoiceInfoDTO() {
		super();
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	public String getOwnerParCode() {
		return ownerParCode;
	}

	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getGoodwillApplyId() {
		return goodwillApplyId;
	}

	public void setGoodwillApplyId(Long goodwillApplyId) {
		this.goodwillApplyId = goodwillApplyId;
	}

	public String getInvoiceId() {
		return invoiceId;
	}

	public void setInvoiceId(String invoiceId) {
		this.invoiceId = invoiceId;
	}

	public String getInvoiceTitle() {
		return invoiceTitle;
	}

	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getTaxpayerIdentificationNumber() {
		return taxpayerIdentificationNumber;
	}

	public void setTaxpayerIdentificationNumber(String taxpayerIdentificationNumber) {
		this.taxpayerIdentificationNumber = taxpayerIdentificationNumber;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getOpenBank() {
		return openBank;
	}

	public void setOpenBank(String openBank) {
		this.openBank = openBank;
	}

	public String getAccount() {
		return account;
	}

	public void setAccount(String account) {
		this.account = account;
	}

	public BigDecimal getNoticeInvoicePrice() {
		return noticeInvoicePrice;
	}

	public void setNoticeInvoicePrice(BigDecimal noticeInvoicePrice) {
		this.noticeInvoicePrice = noticeInvoicePrice;
	}

	public BigDecimal getVoucherRechargePrice() {
		return voucherRechargePrice;
	}

	public void setVoucherRechargePrice(BigDecimal voucherRechargePrice) {
		this.voucherRechargePrice = voucherRechargePrice;
	}

	public BigDecimal getVolvoCreditsRechargePrice() {
		return volvoCreditsRechargePrice;
	}

	public void setVolvoCreditsRechargePrice(BigDecimal volvoCreditsRechargePrice) {
		this.volvoCreditsRechargePrice = volvoCreditsRechargePrice;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public BigDecimal getVoucherCouponFaceRechargePrice() {
		return voucherCouponFaceRechargePrice;
	}

	public void setVoucherCouponFaceRechargePrice(BigDecimal voucherCouponFaceRechargePrice) {
		this.voucherCouponFaceRechargePrice = voucherCouponFaceRechargePrice;
	}

	@Override
	public String toString() {
		return "GoodwillNoticeInvoiceInfoDTO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", goodwillApplyId=" + goodwillApplyId
				+ ", invoiceId=" + invoiceId + ", invoiceTitle=" + invoiceTitle + ", name=" + name
				+ ", taxpayerIdentificationNumber=" + taxpayerIdentificationNumber + ", phone=" + phone + ", address="
				+ address + ", openBank=" + openBank + ", account=" + account + ", noticeInvoicePrice="
				+ noticeInvoicePrice + ", voucherRechargePrice=" + voucherRechargePrice + ", volvoCreditsRechargePrice="
				+ volvoCreditsRechargePrice + ", voucherCouponFaceRechargePrice="+voucherCouponFaceRechargePrice+ ", isValid=" + isValid + ", isDeleted=" + isDeleted + ", createdAt="
				+ createdAt + ", updatedAt=" + updatedAt + "}";
	}

	/**
	 * 将DTO 转换为PO //对某个对象属性进行赋值
	 * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
	 * 
	 * @param poClass
	 *            需要转换的poClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
		return super.transDtoToPo(poClass);
	}

	/**
	 * 将DTO 转换为PO
	 * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
	 * 
	 * @param po
	 *            需要转换的对象
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BasePO> void transDtoToPo(T po) {
		BeanMapperUtil.copyProperties(this, po, "id");
	}

}
