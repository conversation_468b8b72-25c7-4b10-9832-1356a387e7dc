package com.yonyou.dmscus.customer.entity.dto.common;

import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Builder
public class WorkAxbInfoBindDto {
    /**主叫号*/
    private String callNumA;
    /**被叫号*/
    private String callNumB;
    /**工作号 X*/
    private String callNumX;
    /**是否双向绑定*/
    private String bothWay;
    /**绑定服务开始时间*/
    private Date effectStartTime;
    /**绑定服务结束时间*/
    private Date effectEndTime;
    /**是否透传*/
    private String oneHide;
    /**附加字段*/
    private String userData;
    /**客户侧业务订单号*/
    private String userOrderId;

    public List<WorkAxbInfoBindDto> buildWorkAxbInfoList(){
        return Lists.newArrayList(this);
    }

}
