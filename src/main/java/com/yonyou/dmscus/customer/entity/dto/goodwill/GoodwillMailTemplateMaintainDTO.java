package com.yonyou.dmscus.customer.entity.dto.goodwill;

import java.io.Serializable;
import java.util.Date;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善邮件模板维护
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-28
 */

public class GoodwillMailTemplateMaintainDTO extends BaseDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	private String appId;

	/**
	 * 所有者代码
	 */
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	private Integer orgId;

	/**
	 * id
	 */
	private Long id;

	/**
	 * 邮件类型
	 */
	private Integer mailType;

	/**
	 * 发送类型
	 */
	private Integer sendType;

	/**
	 * 是否启用
	 */
	private Integer isUse;
	/**
	 * 间隔（天）
	 */
	private Integer days;
	/**
	 * 发送对象
	 */
	private String sendObject;

	/**
	 * 发送对象
	 */
	private String[] sendObjects;

	/**
	 * 邮件标题
	 */
	private String mailTitle;

	/**
	 * 邮件内容
	 */
	private String mailContent;

	/**
	 * 是否有效
	 */
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	private Date createdAt;

	/**
	 * 修改时间
	 */
	private Date updatedAt;

	public GoodwillMailTemplateMaintainDTO() {
		super();
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	public String getOwnerParCode() {
		return ownerParCode;
	}

	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getMailType() {
		return mailType;
	}

	public void setMailType(Integer mailType) {
		this.mailType = mailType;
	}

	public Integer getSendType() {
		return sendType;
	}

	public void setSendType(Integer sendType) {
		this.sendType = sendType;
	}

	public Integer getIsUse() {
		return isUse;
	}

	public void setIsUse(Integer isUse) {
		this.isUse = isUse;
	}

	public String getMailTitle() {
		return mailTitle;
	}

	public void setMailTitle(String mailTitle) {
		this.mailTitle = mailTitle;
	}

	public String getMailContent() {
		return mailContent;
	}

	public void setMailContent(String mailContent) {
		this.mailContent = mailContent;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public String getSendObject() {
		return sendObject;
	}

	public void setSendObject(String sendObject) {
		this.sendObject = sendObject;
	}

	public String[] getSendObjects() {
		return sendObjects;
	}

	public void setSendObjects(String[] sendObjects) {
		this.sendObjects = sendObjects;
	}

	public Integer getDays() {
		return days;
	}

	public void setDays(Integer days) {
		this.days = days;
	}

	@Override
	public String toString() {
		return "GoodwillMailTemplateMaintainDTO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", mailType=" + mailType + ", sendType=" + sendType
				+ ", isUse=" + isUse + ", sendObject=" + sendObject + ", mailTitle=" + mailTitle + ", mailContent="
				+ mailContent + ", isValid=" + isValid + ", isDeleted=" + isDeleted + ", createdAt=" + createdAt
				+ ", updatedAt=" + updatedAt + "}";
	}

	/**
	 * 将DTO 转换为PO //对某个对象属性进行赋值
	 * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
	 * 
	 * @param poClass
	 *            需要转换的poClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Override
	public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
		return super.transDtoToPo(poClass);
	}

	/**
	 * 将DTO 转换为PO
	 * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
	 * 
	 * @param po
	 *            需要转换的对象
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BasePO> void transDtoToPo(T po) {
		BeanMapperUtil.copyProperties(this, po, "id");
	}

}
