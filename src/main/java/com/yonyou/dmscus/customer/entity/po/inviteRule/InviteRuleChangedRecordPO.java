package com.yonyou.dmscus.customer.entity.po.inviteRule;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import com.yonyou.dmscloud.framework.base.po.BasePO;

import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

/**
 * <p>
 * 邀约规则变更记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-18
 */
@Data
@TableName("tt_invite_rule_changed_record")
public class InviteRuleChangedRecordPO extends BasePO<InviteRuleChangedRecordPO> {

    private static final long serialVersionUID = 1L;


    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;

    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经销商代码（VCDC：代表厂端，经销商代码：代表各自经销商）
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 邀约类型：首保、定保、保险、客户流失
     */
    @TableField("invite_type")
    private Integer inviteType;

    /**
     * 邀约规则
     */
    @TableField("invite_rule")
    private Integer inviteRule;

    /**
     * 邀约规则值
     */
    @TableField("rule_value")
    private Integer ruleValue;

    /**
     * 原邀约规则值
     */
    @TableField("last_rule_value")
    private Integer lastRuleValue;

    /**
     * 再提醒间隔（月）
     */
    @TableField("remind_interval")
    private Integer remindInterval;

    /**
     * 原再提醒间隔（月）
     */
    @TableField("last_remind_interval")
    private Integer lastRemindInterval;

    /**
     * 原超时关闭时间
     */
    @TableField("last_close_interval")
    private Integer lastCloseInterval;

    /**
     * 超时关闭时间
     */
    @TableField("close_interval")
    private Integer closeInterval;

    /**
     * 是否启用：1、启用，0、不启用
     */
    @TableField("is_use")
    private Integer isUse;

    /**
     * 原是否启用：1、启用，0、不启用
     */
    @TableField("last_is_use")
    private Integer lastIsUse;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;


    /**
     * 修改后未处理 1:是0,否
     */
    @TableField("update_is_execute")
    private Boolean updateIsExecute;


    /**
     * 备注
     */
    @TableField("remark")
    private String remark;


    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
