package com.yonyou.dmscus.customer.entity.dto.businessPlatform;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


@Data
public class InviteVulDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String vin ;


    private String ownerCode ;


    private String functionGroup ;


    private String roNo;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String roCreateDate;


    private Double inMileage;


    private Double outMileage;


    private Integer useStatus;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date roBalanceTimeFirst;






}
