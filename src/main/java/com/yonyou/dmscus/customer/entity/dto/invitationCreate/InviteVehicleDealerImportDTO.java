package com.yonyou.dmscus.customer.entity.dto.invitationCreate;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscus.customer.entity.dto.inviteSaAllocateRule.InviteSaAllocateRuleDetailDTO;

import lombok.Data;

import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 车辆特约店自建邀约任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-29
 */
@Data
public class InviteVehicleDealerImportDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;



    private String appId;

    private String ownerCode;

    private String ownerParCode;

    private Long orgId;

    private String dealerCode;

    private String inviteName;

    private String followMode;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date adviseInDate;

    private String vin;

    private String name;

    private String tel;

    private Integer isError;

    private String errorMsg;

    private Integer lineNumber;

    private String createdBy;

    private String licensePlateNum;

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
