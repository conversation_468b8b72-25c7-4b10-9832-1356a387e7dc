package com.yonyou.dmscus.customer.entity.dto.invitationCreate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscus.customer.entity.dto.inviteSaAllocateRule.InviteSaAllocateRuleDetailDTO;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 车辆特约店自建邀约任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-29
 */

public class InviteVehicleDealerTaskDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 进销商代码
     */
    private String dealerCode;

    /**
     * 计划编号
     */
    private String planNo;

    /**
     * 邀约类型：服务活动、召回、保修、延保、其他
     */
    private Integer inviteType;

    /**
     * 邀约名称
     */
    private String inviteName;

    /**
     * 跟进方式：电话、短信、微信、问卷、其他
     */
    private Integer followMode;

    /**
     * 日均里程
     */
    private Integer dailyMileage;

    /**
     * 计划提醒日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planRemindDate;

    /**
     * 建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date adviseInDate;

    /**
     * 是否已生成邀约线索（是否下发）：1 是，0 否
     */
    private Integer isCreateInvite;

    /**
     * 邀约ID （关联invite_vehicle_record中id）
     */
    private Long inviteId;

    /**
     * 分配人员
     */
    private String distributionPerson;

    /**
     * 分配日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date distributionDate;

    /**
     * 跟进内容
     */
    private String followContent;

    /**
     * 跟进人员 可指定，如没有指定按照特约店维护的SA分配规则表分配跟进人员
     */
    private String followSaId;

    /**
     * 服务活动,存放服务活动id
     */
    private Long serviceActivity;

    /**
     * 跟进状态
     */
    private Integer followStatus;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 创建人姓名
     */
    private String createdName;


    /**
     * 创建时间开始 查询条件
     */
    private String createdAtStart;


    /**
     * 创建时间结束 查询条件
     */
    private String createdAtEnd;


    private List<InviteVehicleDTO> vehList;


    private List<InviteSaAllocateRuleDetailDTO> saList;

    public InviteVehicleDealerTaskDTO() {
        super();
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public String getCreatedAtStart() {
        return createdAtStart;
    }

    public void setCreatedAtStart(String createdAtStart) {
        this.createdAtStart = createdAtStart;
    }

    public String getCreatedAtEnd() {
        return createdAtEnd;
    }

    public void setCreatedAtEnd(String createdAtEnd) {
        this.createdAtEnd = createdAtEnd;
    }

    public List<InviteVehicleDTO> getVehList() {
        return vehList;
    }

    public void setVehList(List<InviteVehicleDTO> vehList) {
        this.vehList = vehList;
    }

    public List<InviteSaAllocateRuleDetailDTO> getSaList() {
        return saList;
    }

    public void setSaList(List<InviteSaAllocateRuleDetailDTO> saList) {
        this.saList = saList;
    }

    public Date getPlanRemindDate() {
        return planRemindDate;
    }

    public void setPlanRemindDate(Date planRemindDate) {
        this.planRemindDate = planRemindDate;
    }

    public Date getAdviseInDate() {
        return adviseInDate;
    }

    public void setAdviseInDate(Date adviseInDate) {
        this.adviseInDate = adviseInDate;
    }

    public Date getDistributionDate() {
        return distributionDate;
    }

    public void setDistributionDate(Date distributionDate) {
        this.distributionDate = distributionDate;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getAppId() {
        return appId;
    }


    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }


    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerParCode() {
        return ownerParCode;
    }


    public void setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public Integer getOrgId() {
        return orgId;
    }


    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId() {
        return id;
    }


    public void setId(Long id) {
        this.id = id;
    }

    public String getDealerCode() {
        return dealerCode;
    }


    public void setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
    }

    public String getPlanNo() {
        return planNo;
    }


    public void setPlanNo(String planNo) {
        this.planNo = planNo;
    }

    public Integer getInviteType() {
        return inviteType;
    }


    public void setInviteType(Integer inviteType) {
        this.inviteType = inviteType;
    }

    public String getInviteName() {
        return inviteName;
    }


    public void setInviteName(String inviteName) {
        this.inviteName = inviteName;
    }

    public Integer getFollowMode() {
        return followMode;
    }


    public void setFollowMode(Integer followMode) {
        this.followMode = followMode;
    }

    public Integer getDailyMileage() {
        return dailyMileage;
    }


    public void setDailyMileage(Integer dailyMileage) {
        this.dailyMileage = dailyMileage;
    }

    public Integer getIsCreateInvite() {
        return isCreateInvite;
    }


    public void setIsCreateInvite(Integer isCreateInvite) {
        this.isCreateInvite = isCreateInvite;
    }

    public Long getInviteId() {
        return inviteId;
    }


    public void setInviteId(Long inviteId) {
        this.inviteId = inviteId;
    }

    public String getDistributionPerson() {
        return distributionPerson;
    }


    public void setDistributionPerson(String distributionPerson) {
        this.distributionPerson = distributionPerson;
    }


    public String getFollowContent() {
        return followContent;
    }


    public void setFollowContent(String followContent) {
        this.followContent = followContent;
    }

    public String getFollowSaId() {
        return followSaId;
    }


    public void setFollowSaId(String followSaId) {
        this.followSaId = followSaId;
    }

    public Long getServiceActivity() {
        return serviceActivity;
    }


    public void setServiceActivity(Long serviceActivity) {
        this.serviceActivity = serviceActivity;
    }

    public Integer getFollowStatus() {
        return followStatus;
    }


    public void setFollowStatus(Integer followStatus) {
        this.followStatus = followStatus;
    }

    public Integer getDataSources() {
        return dataSources;
    }


    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }


    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid() {
        return isValid;
    }


    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }


    @Override
    public String toString() {
        return "InviteVehicleDealerTaskDTO{" +
                "appId=" + appId +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", dealerCode=" + dealerCode +
                ", planNo=" + planNo +
                ", inviteType=" + inviteType +
                ", inviteName=" + inviteName +
                ", followMode=" + followMode +
                ", dailyMileage=" + dailyMileage +
                ", planRemindDate=" + planRemindDate +
                ", adviseInDate=" + adviseInDate +
                ", isCreateInvite=" + isCreateInvite +
                ", inviteId=" + inviteId +
                ", distributionPerson=" + distributionPerson +
                ", distributionDate=" + distributionDate +
                ", followContent=" + followContent +
                ", followSaId=" + followSaId +
                ", serviceActivity=" + serviceActivity +
                ", followStatus=" + followStatus +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
