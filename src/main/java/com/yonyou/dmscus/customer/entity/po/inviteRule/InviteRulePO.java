package com.yonyou.dmscus.customer.entity.po.inviteRule;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

/**
 * <p>
 * 邀约规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-20
 */
@TableName("tt_invite_rule")
@Data
public class InviteRulePO extends BasePO<InviteRulePO> {

    private static final long serialVersionUID = 1L;



    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;


    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经销商代码（VCDC：代表厂端，经销商代码：代表各自经销商）
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 邀约类型：首保、定保、保险、客户流失
     */
    @TableField("invite_type")
    private Integer inviteType;

    /**
     * 邀约规则
     */
    @TableField("invite_rule")
    private Integer inviteRule;

    /**
     * 邀约规则值
     */
    @TableField("rule_value")
    private Integer ruleValue;

    /**
     * 提前N天邀约
     */
    @TableField("day_in_advance")
    private Integer dayInAdvance;

    /**
     * 再提醒间隔（月）
     */
    @TableField(value = "remind_interval",updateStrategy = FieldStrategy.IGNORED,jdbcType= JdbcType.INTEGER)
    private Integer remindInterval;

    /**
     * 超时关闭间隔（月）
     */
    @TableField(value = "close_interval",updateStrategy = FieldStrategy.IGNORED,jdbcType= JdbcType.INTEGER)
    private Integer closeInterval;

    /**
     * 是否启用：1、启用，2、不启用
     */
    @TableField("is_use")
    private Integer isUse;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;



    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
