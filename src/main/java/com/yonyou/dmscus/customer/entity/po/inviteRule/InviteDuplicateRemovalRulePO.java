package com.yonyou.dmscus.customer.entity.po.inviteRule;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import com.yonyou.dmscloud.framework.base.po.BasePO;

import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;

/**
 * <p>
 * 邀约去重规则表，本章表所存储的数据，应该都是一个默认值。
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-17
 */
@TableName("tt_invite_duplicate_removal_rule")
public class InviteDuplicateRemovalRulePO extends BasePO<InviteDuplicateRemovalRulePO> {

    private static final long serialVersionUID = 1L;

    /**
     * 系统ID
     */
    @TableField("app_id")
    private String appId;

    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    @TableField("owner_par_code")
    private String ownerParCode;

    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则 原始默认值90天
     */
    @TableField("rule")
    private Integer rule;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;


    /**
     * 1:设置X天有进厂记录，则不再生成首保、定保、客户流失的邀约
     * 2:设置X天有首保或定保的邀约记录，则易损件、流失客户的邀约合并到首保、定保
     */
    @TableField("rule_type")
    private Integer ruleType;

    public InviteDuplicateRemovalRulePO() {
        super();
    }


    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerParCode() {
        return ownerParCode;
    }

    public void setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getRule() {
        return rule;
    }

    public void setRule(Integer rule) {
        this.rule = rule;
    }

    public Integer getDataSources() {
        return dataSources;
    }

    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }


    public Integer getRuleType() {
        return ruleType;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "InviteDuplicateRemovalRulePO{" +
                "appId=" + appId +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", rule=" + rule +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                ", ruleType=" + ruleType +
                "}";
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
