package com.yonyou.dmscus.customer.entity.dto.complaint.sale;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 销售客户投诉信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-10
 */

public class SaleComplaintInfoDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 投诉ID（投诉编号）可 用于和呼叫中心对接
     */
    private String complaintId;

    /**
     * 投诉类型
     */
    private Integer type;

    /**
     * 投诉分类
     */
    private Integer classification;

    /**
     * 投诉来源
     */
    private Integer source;

    /**
     * 来电时间/投诉日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date callTime;

    /**
     * 首次重启日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date fisrtRestartTime;

    /**
     * 最新重启日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date newestRestartTime;

    /**
     * 来电客户姓名/投诉人姓名
     */
    private String callName;

    /**
     * 来电电话/投诉人电话
     */
    private String callTel;

    /**
     * 车主姓名
     */
    private String name;

    /**
     * 车牌号
     */
    private String licensePlateNum;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 购车时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date buyTime;

    /**
     * 车型
     */
    private String model;

    /**
     * 年款
     */
    private String modelYear;

    /**
     * 购买经销商
     */
    private String buyDealerName;

    /**
     * 处理经销商
     */
    private String dealerName;

    /**
     * 处理经销商代码
     */
    private String dealerCode;

    /**
     * 里程
     */
    private Integer mileage;

    /**
     * 回复联系人手机1
     */
    private String replyTel;

    /**
     * 回复联系人手机2
     */
    private String replyTel2;

    /**
     * 投诉主题
     */
    private String subject;

    /**
     * 细分部位
     */
    private String subdivisionPart;

    /**
     * 部位
     */
    private String part;

    /**
     * 问题
     */
    private String ProblemInfo;

    /**
     * 客户要求
     */
    private String cusRequirement;

    /**
     * 问题描述
     */
    private String problem;

    /**
     * 坐席主管说明
     */
    private String illustrate;

    /**
     * 投诉单类别一级层
     */
    private String category1;

    /**
     * 投诉单类别二级层
     */
    private String category2;

    /**
     * 投诉单类别三级层
     */
    private String category3;

    /**
     * CC部位
     */
    private String ccPart;

    /**
     * CC细分部位
     */
    private String ccSubdivisionPart;

    /**
     * CC问题
     */
    private String ccProblem;

    /**
     * CC要求
     */
    private String ccRequirement;

    /**
     * 投诉部门
     */
    private String department;

    /**
     * 接待员
     */
    private String receptionist;

    /**
     * 重要等级
     */
    private Integer importanceLevel;

    /**
     * 经销商首次回复时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dealerFisrtReplyTime;

    /**
     * 首次重启经销商首次回复时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date fisrtRestartDealerFisrtReplyTime;

    /**
     * 是否回访
     */
    private Integer isRevisit;

    /**
     * 回访时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date revisitTime;

    /**
     * 回访结果
     */
    private String revisitResult;

    /**
     * 回访内容
     */
    private String revisitContent;

    /**
     * 期望经销商联系时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date hopeReplyTime;

    /**
     * 经销商申请结案时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

    /**
     * 区域经理是否同意
     */
    private Integer isAgree;

    /**
     * 区域经理提交结案时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitTime;

    /**
     * 结案时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date closeCaseTime;

    /**
     * 重启结案时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date restartCloseCaseTime;

    /**
     * 工单状态
     */
    private Integer workStatus;

    /**
     * 结案状态（投诉单状态）
     */
    private Integer closeCaseStatus;

    /**
     * 跟进状态
     */
    private Integer followStatus;

    /**
     * 投诉的根本原因 多选用逗号分隔
     */
    private String basicReason;

    /**
     * 车辆是否修复
     */
    private Integer isRepaired;

    /**
     * 技术与维修方案
     */
    private String techMaintainPlan;

    /**
     * 亲善方案
     */
    private String rapportPlan;

    /**
     * 潜在风险
     */
    private String risk;

    /**
     * 进销商是否已读 1 已读 0 未读
     */
    private Boolean dealerIsRead;

    /**
     * 区域经理是否已读 1 已读 0 未读
     */
    private Boolean managerIsRead;

    /**
     * CCM是否已读 1 已读 0 未读
     */
    private Boolean ccmIsRead;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否上报
     */
    private Boolean isReport;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 投诉人性别
     */
    private Integer sex;

    /**
     * 车主地址
     */
    private String ownerAddress;
    private Integer workOrderStatus;
    private Integer isCloseCase;

    /**
     * 技术与维修方案
     */
    private String regionalManagerComments;

    /**
     * 区域
     */
    private String region;

    /**
     * 区域经理
     */
    private String regionManager;

    /**
     * 区域ID
     */
    private Long regionId;

    /**
     * 区域经理ID
     */
    private Long regionManagerId;

    /**
     * 集团
     */
    private String bloc;

    /**
     * 购买经销商
     */
    private String buyDealerCode;

    /**
     * 区域(购买)
     */
    private String buyRegion;

    /**
     * 购买区域经理(购买)
     */
    private String buyRegionManager;

    /**
     * 区域ID(购买)
     */
    private Long buyRegionId;

    /**
     * 区域经理ID(购买)
     */
    private Long buyRegionManagerId;

    /**
     * 集团(购买)
     */
    private String buyBloc;

    /**
     * 集团id
     */
    private Long blocId;

    /**
     * 客户是否满意
     */
    private Integer isSatisfied;

    /**
     * 工单性质
     */
    private Integer workOrderNature;

    /**
     * 工单分类
     */
    private Integer workOrderClassification;

    /**
     * 服务承诺
     */
    private Integer serviceCommitment;

    /**
     * 回复联系人姓名
     */
    private String replyName;

    /**
     * 是否有舆情风险
     */

    private Integer isOpinion;

    /**
     * 结案备注
     */

    private  String closeCaseRemark;

    /**
     *区域经理是否同意结案
     */
    private Integer isAgreeRegion;

    /**
     * 区域经理意见
     */
    private  String regionComments;

    /**
     *区域经理审核时间
     */
    private  Date regionAuditTime;

    /**
     *总部是否同意结案
     */
    private Integer isAgreeHeadquarters;

    /**
     * 总部意见
     */
    private  String headquartersComments;
    /**
     * 提交重启时间
     */
    private  Date restartReplyTime;

    /**
     *是否匿名
     */
    private Integer isAnonymous;

    /**
     *区域经理是否满意结案（低满意度）
     */
    private Integer regionSatisfiedCase;

    /**
     *总部是否满意结案（低满意度）
     */
    private Integer HQSatisfiedCase;

    /**
     * '厂端描述'
     */
    private String plantDescription;

    public String getPlantDescription() {
        return plantDescription;
    }

    public void setPlantDescription(String plantDescription) {
        this.plantDescription = plantDescription;
    }

    public SaleComplaintInfoDTO() {
        super();
    }

    public Integer getHQSatisfiedCase() {
        return HQSatisfiedCase;
    }

    public void setHQSatisfiedCase(Integer HQSatisfiedCase) {
        this.HQSatisfiedCase = HQSatisfiedCase;
    }

    public Integer getRegionSatisfiedCase() {
        return regionSatisfiedCase;
    }

    public void setRegionSatisfiedCase(Integer regionSatisfiedCase) {
        this.regionSatisfiedCase = regionSatisfiedCase;
    }

    public Integer getIsAnonymous() {
        return isAnonymous;
    }

    public void setIsAnonymous(Integer isAnonymous) {
        this.isAnonymous = isAnonymous;
    }

    public Date getRestartReplyTime() {
        return restartReplyTime;
    }

    public void setRestartReplyTime(Date restartReplyTime) {
        this.restartReplyTime = restartReplyTime;
    }

    public Integer getIsAgreeHeadquarters() {
        return isAgreeHeadquarters;
    }

    public void setIsAgreeHeadquarters(Integer isAgreeHeadquarters) {
        this.isAgreeHeadquarters = isAgreeHeadquarters;
    }

    public String getHeadquartersComments() {
        return headquartersComments;
    }

    public void setHeadquartersComments(String headquartersComments) {
        this.headquartersComments = headquartersComments;
    }

    public Date getRegionAuditTime() {
        return regionAuditTime;
    }

    public void setRegionAuditTime(Date regionAuditTime) {
        this.regionAuditTime = regionAuditTime;
    }

    public String getRegionComments() {
        return regionComments;
    }

    public void setRegionComments(String regionComments) {
        this.regionComments = regionComments;
    }

    public String getCloseCaseRemark() {
        return closeCaseRemark;
    }

    public void setCloseCaseRemark(String closeCaseRemark) {
        this.closeCaseRemark = closeCaseRemark;
    }

    public Integer getIsAgreeRegion() {
        return isAgreeRegion;
    }

    public void setIsAgreeRegion(Integer isAgreeRegion) {
        this.isAgreeRegion = isAgreeRegion;
    }

    public Integer getIsOpinion() {
        return isOpinion;
    }

    public void setIsOpinion(Integer isOpinion) {
        this.isOpinion = isOpinion;
    }

    public Date getHopeReplyTime() {
        return hopeReplyTime;
    }

    public void setHopeReplyTime(Date hopeReplyTime) {
        this.hopeReplyTime = hopeReplyTime;
    }

    public String getAppId() {
        return appId;
    }


    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }


    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerParCode() {
        return ownerParCode;
    }


    public void setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public Integer getOrgId() {
        return orgId;
    }


    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId() {
        return id;
    }


    public void setId(Long id) {
        this.id = id;
    }

    public String getComplaintId() {
        return complaintId;
    }


    public void setComplaintId(String complaintId) {
        this.complaintId = complaintId;
    }

    public Integer getType() {
        return type;
    }


    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getClassification() {
        return classification;
    }


    public void setClassification(Integer classification) {
        this.classification = classification;
    }

    public Integer getSource() {
        return source;
    }


    public void setSource(Integer source) {
        this.source = source;
    }

    public Date getCallTime() {
        return callTime;
    }


    public void setCallTime(Date callTime) {
        this.callTime = callTime;
    }

    public Date getFisrtRestartTime() {
        return fisrtRestartTime;
    }


    public void setFisrtRestartTime(Date fisrtRestartTime) {
        this.fisrtRestartTime = fisrtRestartTime;
    }

    public Date getNewestRestartTime() {
        return newestRestartTime;
    }


    public void setNewestRestartTime(Date newestRestartTime) {
        this.newestRestartTime = newestRestartTime;
    }

    public String getCallName() {
        return callName;
    }


    public void setCallName(String callName) {
        this.callName = callName;
    }

    public String getCallTel() {
        return callTel;
    }


    public void setCallTel(String callTel) {
        this.callTel = callTel;
    }

    public String getName() {
        return name;
    }


    public void setName(String name) {
        this.name = name;
    }

    public String getLicensePlateNum() {
        return licensePlateNum;
    }


    public void setLicensePlateNum(String licensePlateNum) {
        this.licensePlateNum = licensePlateNum;
    }

    public String getVin() {
        return vin;
    }


    public void setVin(String vin) {
        this.vin = vin;
    }

    public Date getBuyTime() {
        return buyTime;
    }


    public void setBuyTime(Date buyTime) {
        this.buyTime = buyTime;
    }

    public String getModel() {
        return model;
    }


    public void setModel(String model) {
        this.model = model;
    }

    public String getModelYear() {
        return modelYear;
    }


    public void setModelYear(String modelYear) {
        this.modelYear = modelYear;
    }

    public String getBuyDealerName() {
        return buyDealerName;
    }


    public void setBuyDealerName(String buyDealerName) {
        this.buyDealerName = buyDealerName;
    }

    public String getDealerName() {
        return dealerName;
    }


    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    public String getDealerCode() {
        return dealerCode;
    }


    public void setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
    }

    public Integer getMileage() {
        return mileage;
    }


    public void setMileage(Integer mileage) {
        this.mileage = mileage;
    }

    public String getReplyTel() {
        return replyTel;
    }


    public void setReplyTel(String replyTel) {
        this.replyTel = replyTel;
    }

    public String getReplyTel2() {
        return replyTel2;
    }


    public void setReplyTel2(String replyTel2) {
        this.replyTel2 = replyTel2;
    }

    public String getSubject() {
        return subject;
    }


    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getSubdivisionPart() {
        return subdivisionPart;
    }


    public void setSubdivisionPart(String subdivisionPart) {
        this.subdivisionPart = subdivisionPart;
    }

    public String getPart() {
        return part;
    }


    public void setPart(String part) {
        this.part = part;
    }

    public String getProblem() {
        return problem;
    }


    public void setProblem(String problem) {
        this.problem = problem;
    }

    public String getIllustrate() {
        return illustrate;
    }


    public void setIllustrate(String illustrate) {
        this.illustrate = illustrate;
    }

    public String getCategory1() {
        return category1;
    }


    public void setCategory1(String category1) {
        this.category1 = category1;
    }

    public String getCategory2() {
        return category2;
    }


    public void setCategory2(String category2) {
        this.category2 = category2;
    }

    public String getCategory3() {
        return category3;
    }


    public void setCategory3(String category3) {
        this.category3 = category3;
    }

    public String getCcPart() {
        return ccPart;
    }


    public void setCcPart(String ccPart) {
        this.ccPart = ccPart;
    }

    public String getCcSubdivisionPart() {
        return ccSubdivisionPart;
    }


    public void setCcSubdivisionPart(String ccSubdivisionPart) {
        this.ccSubdivisionPart = ccSubdivisionPart;
    }

    public String getCcProblem() {
        return ccProblem;
    }


    public void setCcProblem(String ccProblem) {
        this.ccProblem = ccProblem;
    }

    public String getCcRequirement() {
        return ccRequirement;
    }


    public void setCcRequirement(String ccRequirement) {
        this.ccRequirement = ccRequirement;
    }

    public String getDepartment() {
        return department;
    }


    public void setDepartment(String department) {
        this.department = department;
    }

    public String getReceptionist() {
        return receptionist;
    }


    public void setReceptionist(String receptionist) {
        this.receptionist = receptionist;
    }

    public Integer getImportanceLevel() {
        return importanceLevel;
    }


    public void setImportanceLevel(Integer importanceLevel) {
        this.importanceLevel = importanceLevel;
    }

    public Date getDealerFisrtReplyTime() {
        return dealerFisrtReplyTime;
    }


    public void setDealerFisrtReplyTime(Date dealerFisrtReplyTime) {
        this.dealerFisrtReplyTime = dealerFisrtReplyTime;
    }

    public Date getFisrtRestartDealerFisrtReplyTime() {
        return fisrtRestartDealerFisrtReplyTime;
    }


    public void setFisrtRestartDealerFisrtReplyTime(Date fisrtRestartDealerFisrtReplyTime) {
        this.fisrtRestartDealerFisrtReplyTime = fisrtRestartDealerFisrtReplyTime;
    }

    public Integer getIsRevisit() {
        return isRevisit;
    }


    public void setIsRevisit(Integer isRevisit) {
        this.isRevisit = isRevisit;
    }

    public Date getRevisitTime() {
        return revisitTime;
    }


    public void setRevisitTime(Date revisitTime) {
        this.revisitTime = revisitTime;
    }

    public String getRevisitResult() {
        return revisitResult;
    }


    public void setRevisitResult(String revisitResult) {
        this.revisitResult = revisitResult;
    }

    public String getRevisitContent() {
        return revisitContent;
    }


    public void setRevisitContent(String revisitContent) {
        this.revisitContent = revisitContent;
    }

    public Date getApplyTime() {
        return applyTime;
    }


    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public Integer getIsAgree() {
        return isAgree;
    }


    public void setIsAgree(Integer isAgree) {
        this.isAgree = isAgree;
    }

    public Date getSubmitTime() {
        return submitTime;
    }


    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public Date getCloseCaseTime() {
        return closeCaseTime;
    }


    public void setCloseCaseTime(Date closeCaseTime) {
        this.closeCaseTime = closeCaseTime;
    }

    public Date getRestartCloseCaseTime() {
        return restartCloseCaseTime;
    }


    public void setRestartCloseCaseTime(Date restartCloseCaseTime) {
        this.restartCloseCaseTime = restartCloseCaseTime;
    }

    public Integer getWorkStatus() {
        return workStatus;
    }


    public void setWorkStatus(Integer workStatus) {
        this.workStatus = workStatus;
    }

    public Integer getCloseCaseStatus() {
        return closeCaseStatus;
    }


    public void setCloseCaseStatus(Integer closeCaseStatus) {
        this.closeCaseStatus = closeCaseStatus;
    }

    public Integer getFollowStatus() {
        return followStatus;
    }


    public void setFollowStatus(Integer followStatus) {
        this.followStatus = followStatus;
    }

    public String getBasicReason() {
        return basicReason;
    }


    public void setBasicReason(String basicReason) {
        this.basicReason = basicReason;
    }

    public Integer getIsRepaired() {
        return isRepaired;
    }


    public void setIsRepaired(Integer isRepaired) {
        this.isRepaired = isRepaired;
    }

    public String getTechMaintainPlan() {
        return techMaintainPlan;
    }


    public void setTechMaintainPlan(String techMaintainPlan) {
        this.techMaintainPlan = techMaintainPlan;
    }

    public String getRapportPlan() {
        return rapportPlan;
    }


    public void setRapportPlan(String rapportPlan) {
        this.rapportPlan = rapportPlan;
    }

    public String getRisk() {
        return risk;
    }


    public void setRisk(String risk) {
        this.risk = risk;
    }

    public Boolean getIsDealerIsRead() {
        return dealerIsRead;
    }

    public String getCusRequirement() {
        return cusRequirement;
    }

    public void setCusRequirement(String cusRequirement) {
        this.cusRequirement = cusRequirement;
    }

    public Boolean getDealerIsRead() {
        return dealerIsRead;
    }

    public String getProblemInfo() {
        return ProblemInfo;
    }

    public void setProblemInfo(String problemInfo) {
        ProblemInfo = problemInfo;
    }

    public Boolean getReport() {
        return isReport;
    }

    public void setReport(Boolean report) {
        isReport = report;
    }

    public void setDealerIsRead(Boolean dealerIsRead) {
        this.dealerIsRead = dealerIsRead;
    }

    public void setIsDealerIsRead(Boolean dealerIsRead) {
        this.dealerIsRead = dealerIsRead;
    }

    public Boolean getIsManagerIsRead() {
        return managerIsRead;
    }


    public void setIsManagerIsRead(Boolean managerIsRead) {
        this.managerIsRead = managerIsRead;
    }

    public Boolean getIsCcmIsRead() {
        return ccmIsRead;
    }


    public void setIsCcmIsRead(Boolean ccmIsRead) {
        this.ccmIsRead = ccmIsRead;
    }

    public Integer getDataSources() {
        return dataSources;
    }


    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsReport() {
        return isReport;
    }


    public void setIsReport(Boolean isReport) {
        this.isReport = isReport;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }


    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid() {
        return isValid;
    }


    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Date getCreatedAt() {
        return createdAt;
    }


    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }


    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Integer getSex() {
        return sex;
    }


    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getOwnerAddress() {
        return ownerAddress;
    }


    public void setOwnerAddress(String ownerAddress) {
        this.ownerAddress = ownerAddress;
    }

    public Integer getWorkOrderStatus() {
        return workOrderStatus;
    }


    public void setWorkOrderStatus(Integer workOrderStatus) {
        this.workOrderStatus = workOrderStatus;
    }

    public Integer getIsCloseCase() {
        return isCloseCase;
    }


    public void setIsCloseCase(Integer isCloseCase) {
        this.isCloseCase = isCloseCase;
    }

    public String getRegionalManagerComments() {
        return regionalManagerComments;
    }


    public void setRegionalManagerComments(String regionalManagerComments) {
        this.regionalManagerComments = regionalManagerComments;
    }

    public String getRegion() {
        return region;
    }


    public void setRegion(String region) {
        this.region = region;
    }

    public String getRegionManager() {
        return regionManager;
    }


    public void setRegionManager(String regionManager) {
        this.regionManager = regionManager;
    }

    public Long getRegionId() {
        return regionId;
    }


    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public Long getRegionManagerId() {
        return regionManagerId;
    }


    public void setRegionManagerId(Long regionManagerId) {
        this.regionManagerId = regionManagerId;
    }

    public String getBloc() {
        return bloc;
    }


    public void setBloc(String bloc) {
        this.bloc = bloc;
    }

    public String getBuyDealerCode() {
        return buyDealerCode;
    }


    public void setBuyDealerCode(String buyDealerCode) {
        this.buyDealerCode = buyDealerCode;
    }

    public String getBuyRegion() {
        return buyRegion;
    }


    public void setBuyRegion(String buyRegion) {
        this.buyRegion = buyRegion;
    }

    public String getBuyRegionManager() {
        return buyRegionManager;
    }


    public void setBuyRegionManager(String buyRegionManager) {
        this.buyRegionManager = buyRegionManager;
    }

    public Long getBuyRegionId() {
        return buyRegionId;
    }


    public void setBuyRegionId(Long buyRegionId) {
        this.buyRegionId = buyRegionId;
    }

    public Long getBuyRegionManagerId() {
        return buyRegionManagerId;
    }


    public void setBuyRegionManagerId(Long buyRegionManagerId) {
        this.buyRegionManagerId = buyRegionManagerId;
    }

    public String getBuyBloc() {
        return buyBloc;
    }


    public void setBuyBloc(String buyBloc) {
        this.buyBloc = buyBloc;
    }

    public Long getBlocId() {
        return blocId;
    }


    public void setBlocId(Long blocId) {
        this.blocId = blocId;
    }

    public Integer getIsSatisfied() {
        return isSatisfied;
    }


    public void setIsSatisfied(Integer isSatisfied) {
        this.isSatisfied = isSatisfied;
    }

    public Integer getWorkOrderNature() {
        return workOrderNature;
    }


    public void setWorkOrderNature(Integer workOrderNature) {
        this.workOrderNature = workOrderNature;
    }

    public Integer getWorkOrderClassification() {
        return workOrderClassification;
    }


    public void setWorkOrderClassification(Integer workOrderClassification) {
        this.workOrderClassification = workOrderClassification;
    }

    public Integer getServiceCommitment() {
        return serviceCommitment;
    }


    public void setServiceCommitment(Integer serviceCommitment) {
        this.serviceCommitment = serviceCommitment;
    }

    public String getReplyName() {
        return replyName;
    }


    public void setReplyName(String replyName) {
        this.replyName = replyName;
    }

    @Override
    public String toString() {
        return "SaleComplaintInfoDTO{" +
                "appId=" + appId +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", complaintId=" + complaintId +
                ", type=" + type +
                ", classification=" + classification +
                ", source=" + source +
                ", callTime=" + callTime +
                ", fisrtRestartTime=" + fisrtRestartTime +
                ", newestRestartTime=" + newestRestartTime +
                ", callName=" + callName +
                ", callTel=" + callTel +
                ", name=" + name +
                ", licensePlateNum=" + licensePlateNum +
                ", vin=" + vin +
                ", buyTime=" + buyTime +
                ", model=" + model +
                ", modelYear=" + modelYear +
                ", buyDealerName=" + buyDealerName +
                ", dealerName=" + dealerName +
                ", dealerCode=" + dealerCode +
                ", mileage=" + mileage +
                ", replyTel=" + replyTel +
                ", replyTel2=" + replyTel2 +
                ", subject=" + subject +
                ", subdivisionPart=" + subdivisionPart +
                ", part=" + part +
                ", problem=" + problem +
                ", illustrate=" + illustrate +
                ", category1=" + category1 +
                ", category2=" + category2 +
                ", category3=" + category3 +
                ", ccPart=" + ccPart +
                ", ccSubdivisionPart=" + ccSubdivisionPart +
                ", ccProblem=" + ccProblem +
                ", ccRequirement=" + ccRequirement +
                ", department=" + department +
                ", receptionist=" + receptionist +
                ", importanceLevel=" + importanceLevel +
                ", dealerFisrtReplyTime=" + dealerFisrtReplyTime +
                ", fisrtRestartDealerFisrtReplyTime=" + fisrtRestartDealerFisrtReplyTime +
                ", isRevisit=" + isRevisit +
                ", revisitTime=" + revisitTime +
                ", revisitResult=" + revisitResult +
                ", revisitContent=" + revisitContent +
                ", applyTime=" + applyTime +
                ", isAgree=" + isAgree +
                ", submitTime=" + submitTime +
                ", closeCaseTime=" + closeCaseTime +
                ", restartCloseCaseTime=" + restartCloseCaseTime +
                ", workStatus=" + workStatus +
                ", closeCaseStatus=" + closeCaseStatus +
                ", followStatus=" + followStatus +
                ", basicReason=" + basicReason +
                ", isRepaired=" + isRepaired +
                ", techMaintainPlan=" + techMaintainPlan +
                ", rapportPlan=" + rapportPlan +
                ", risk=" + risk +
                ", dealerIsRead=" + dealerIsRead +
                ", managerIsRead=" + managerIsRead +
                ", ccmIsRead=" + ccmIsRead +
                ", dataSources=" + dataSources +
                ", isReport=" + isReport +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", sex=" + sex +
                ", ownerAddress=" + ownerAddress +
                ", workOrderStatus=" + workOrderStatus +
                ", isCloseCase=" + isCloseCase +
                ", regionalManagerComments=" + regionalManagerComments +
                ", region=" + region +
                ", regionManager=" + regionManager +
                ", regionId=" + regionId +
                ", regionManagerId=" + regionManagerId +
                ", bloc=" + bloc +
                ", buyDealerCode=" + buyDealerCode +
                ", buyRegion=" + buyRegion +
                ", buyRegionManager=" + buyRegionManager +
                ", buyRegionId=" + buyRegionId +
                ", buyRegionManagerId=" + buyRegionManagerId +
                ", buyBloc=" + buyBloc +
                ", blocId=" + blocId +
                ", isSatisfied=" + isSatisfied +
                ", workOrderNature=" + workOrderNature +
                ", workOrderClassification=" + workOrderClassification +
                ", serviceCommitment=" + serviceCommitment +
                ", replyName=" + replyName +
                "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
