package com.yonyou.dmscus.customer.entity.po.goodwill;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * 亲善AWA打印模板维护
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-23
 */
@TableName("tm_goodwill_awa_print_maintain")
public class GoodwillAwaPrintMaintainPO extends BasePO<GoodwillAwaPrintMaintainPO> {

	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	@TableField("APP_ID")
	private String appId;

	/**
	 * 所有者代码
	 */
	@TableField("OWNER_CODE")
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	@TableField("OWNER_PAR_CODE")
	private String ownerParCode;

	/**
	 * 组织ID
	 */
	@TableField("ORG_ID")
	private Integer orgId;

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * approval
	 */
	@TableField("approval")
	private String approval;

	/**
	 * requestor
	 */
	@TableField("requestor")
	private String requestor;

	/**
	 * line_director
	 */
	@TableField("line_director")
	private String lineDirector;

	/**
	 * cs_vp
	 */
	@TableField("cs_vp")
	private String csVp;

	/**
	 * date_one
	 */
	@TableField("date_one")
	private String dateOne;

	/**
	 * date_two
	 */
	@TableField("date_two")
	private String dateTwo;

	/**
	 * date_three
	 */
	@TableField("date_three")
	private String dateThree;

	/**
	 * cfo
	 */
	@TableField("cfo")
	private String cfo;

	/**
	 * coo
	 */
	@TableField("coo")
	private String coo;

	/**
	 * md
	 */
	@TableField("md")
	private String md;

	/**
	 * date_four
	 */
	@TableField("date_four")
	private String dateFour;

	/**
	 * date_five
	 */
	@TableField("date_five")
	private String dateFive;

	/**
	 * date_six
	 */
	@TableField("date_six")
	private String dateSix;

	/**
	 * explain_one
	 */
	@TableField("explain_one")
	private String explainOne;

	/**
	 * explain_two
	 */
	@TableField("explain_two")
	private String explainTwo;

	/**
	 * 是否有效
	 */
	@TableField("is_valid")
	private Integer isValid;

	/**
	 * 是否删除:1,删除；0,未删除
	 */
	@TableField("is_deleted")
	private Boolean isDeleted;

	/**
	 * 创建时间
	 */
	@TableField("created_at")
	private Date createdAt;

	/**
	 * 修改时间
	 */
	@TableField("updated_at")
	private Date updatedAt;

	public GoodwillAwaPrintMaintainPO() {
		super();
	}

	@Override
	public String getAppId() {
		return appId;
	}

	@Override
	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOwnerCode() {
		return ownerCode;
	}

	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}

	@Override
	public String getOwnerParCode() {
		return ownerParCode;
	}

	@Override
	public void setOwnerParCode(String ownerParCode) {
		this.ownerParCode = ownerParCode;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getApproval() {
		return approval;
	}

	public void setApproval(String approval) {
		this.approval = approval;
	}

	public String getRequestor() {
		return requestor;
	}

	public void setRequestor(String requestor) {
		this.requestor = requestor;
	}

	public String getLineDirector() {
		return lineDirector;
	}

	public void setLineDirector(String lineDirector) {
		this.lineDirector = lineDirector;
	}

	public String getCsVp() {
		return csVp;
	}

	public void setCsVp(String csVp) {
		this.csVp = csVp;
	}

	public String getDateOne() {
		return dateOne;
	}

	public void setDateOne(String dateOne) {
		this.dateOne = dateOne;
	}

	public String getDateTwo() {
		return dateTwo;
	}

	public void setDateTwo(String dateTwo) {
		this.dateTwo = dateTwo;
	}

	public String getDateThree() {
		return dateThree;
	}

	public void setDateThree(String dateThree) {
		this.dateThree = dateThree;
	}

	public String getCfo() {
		return cfo;
	}

	public void setCfo(String cfo) {
		this.cfo = cfo;
	}

	public String getCoo() {
		return coo;
	}

	public void setCoo(String coo) {
		this.coo = coo;
	}

	public String getMd() {
		return md;
	}

	public void setMd(String md) {
		this.md = md;
	}

	public String getDateFour() {
		return dateFour;
	}

	public void setDateFour(String dateFour) {
		this.dateFour = dateFour;
	}

	public String getDateFive() {
		return dateFive;
	}

	public void setDateFive(String dateFive) {
		this.dateFive = dateFive;
	}

	public String getDateSix() {
		return dateSix;
	}

	public void setDateSix(String dateSix) {
		this.dateSix = dateSix;
	}

	public String getExplainOne() {
		return explainOne;
	}

	public void setExplainOne(String explainOne) {
		this.explainOne = explainOne;
	}

	public String getExplainTwo() {
		return explainTwo;
	}

	public void setExplainTwo(String explainTwo) {
		this.explainTwo = explainTwo;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	@Override
	public Date getCreatedAt() {
		return createdAt;
	}

	@Override
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Override
	public Date getUpdatedAt() {
		return updatedAt;
	}

	@Override
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	protected Serializable pkVal() {
		return this.id;
	}

	@Override
	public String toString() {
		return "GoodwillAwaPrintMaintainPO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode="
				+ ownerParCode + ", orgId=" + orgId + ", id=" + id + ", approval=" + approval + ", requestor="
				+ requestor + ", lineDirector=" + lineDirector + ", csVp=" + csVp + ", dateOne=" + dateOne
				+ ", dateTwo=" + dateTwo + ", dateThree=" + dateThree + ", cfo=" + cfo + ", coo=" + coo + ", md=" + md
				+ ", dateFour=" + dateFour + ", dateFive=" + dateFive + ", dateSix=" + dateSix + ", explainOne="
				+ explainOne + ", explainTwo=" + explainTwo + ", isValid=" + isValid + ", isDeleted=" + isDeleted
				+ ", createdAt=" + createdAt + ", updatedAt=" + updatedAt + "}";
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dtoClass
	 *            需要进行转换的dtoClass
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
		return super.transDtoToPo(dtoClass);
	}

	/**
	 * 将PO 信息转化为DTO
	 *
	 * @param dto
	 *            需要进行转换的dto
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	public <T extends BaseDTO> void transPoToDto(T dto) {
		BeanMapperUtil.copyProperties(this, dto, "id");
	}

}
