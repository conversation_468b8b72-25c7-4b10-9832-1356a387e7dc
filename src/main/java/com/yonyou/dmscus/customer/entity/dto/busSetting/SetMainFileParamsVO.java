package com.yonyou.dmscus.customer.entity.dto.busSetting;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 套餐主档
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
@Data
@ApiModel(value = "套餐主档请求参数")
public class SetMainFileParamsVO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 经销商代码
     */
    @ApiModelProperty(value = "经销商代码（返回该经销商自己的套餐和官方套餐）",required = true)
    private String dealerCode;

    /**
     * 车型代码
     */
    @ApiModelProperty(value = "车型代码")
    private String modelCode;

    @ApiModelProperty(value = "发动机代码")
    private String engineCode;

    @ApiModelProperty(value = "进厂行驶里程")
    private BigDecimal inMileage;

    /**
     * 套餐编码
     */
    @ApiModelProperty(value = "套餐编码")
    private String setCode;

    /**
     * 套餐名称
     */
    @ApiModelProperty(value = "套餐名称")
    private String setName;

    /**
     * 套餐类型
     */
    @ApiModelProperty(value = "套餐类型（保养套餐：81861001，通用套餐：81861002，其它套餐：81861003）")
    private Integer setType;




    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
