package com.yonyou.dmscus.customer.entity.po.vocfunctional;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @title: VocWarningDataLog
 * @projectName dmscus.customer
 * @date 2022/11/118:25
 */
@Data
@ApiModel(value = "VocInviteVehicleTaskRecordPo", description = "VOC线索任务关系表存在线索新增字段")
@TableName("tt_voc_invite_vehicle_task_record")
public class VocInviteVehicleTaskRecordPo  extends  BasePo  {

    @ApiModelProperty(value = "车架号",notes = "车架号",required = true,name = "dt",example = "LVSHGFAR4FF087294",dataType = "String")
    @TableField("vin")
    private String vin;

    //任务id
    @TableField("task_id")
    @ApiModelProperty(value = "任务id",notes = "任务id",required = true,name = "task_id",example = "1",dataType = "Long")
    private Long taskId;

    //线索id
    @TableField("record_id")
    @ApiModelProperty(value = "线索id",notes = "线索id",required = true,name = "record_id",example = "1",dataType = "Long")
    private Long recordId;
    //跟进记录次数
    @TableField("record_num")
    @ApiModelProperty(value = "跟进记录次数",notes = "跟进记录次数",required = true,name = "record_num",example = "1",dataType = "Integer")
    private Integer recordNum;
    //跟进记录次数
    @TableField("ai_at")
    @ApiModelProperty(value = "首次AI跟进时间",notes = "首次AI跟进时间",required = true,name = "ai_at",example = "1",dataType = "Date")
    private Date aiAt;
    //跟进记录次数
    @TableField("order_at")
    @ApiModelProperty(value = "工单开单时间",notes = "工单开单时间",required = true,name = "order_at",example = "1",dataType = "Date")
    private Date orderAt;
    //跟进记录次数
    @TableField("record_at")
    @ApiModelProperty(value = "预约时间",notes = "预约时间",required = true,name = "record_at",example = "1",dataType = "Date")
    private Date recordAt;
    //跟进记录次数
    @TableField("record_type")
    @ApiModelProperty(value = "1",notes = "线索类型:0 VOC线索,1普通线索 默认1",required = true,name = "record_type",example = "0",dataType = "Integer")
    private Integer recordType;

    //TableField
    @TableField("loss_type")
    @ApiModelProperty(value = "1",notes = "线索类型:0 ：非VOC类型,1 ：18个月未保养，2：非4S店保养流失,3:流失客户保养灯亮",required = true,name = "loss_type",example = "0",dataType = "Integer")
    private Integer lossType;

    /**
     * 流失预警类型
     * */
    @TableField("loss_warning_type ")
    @ApiModelProperty(value = "87902001",notes = "线索预警类型:0 ：无,87902001 ：3个月流失预警，87902002：保养灯流失预警",required = true,name = "loss_warning_type",example = "0",dataType = "Integer")
    private Integer lossWarningType ;

    //跟进记录次数
    @TableField("invite_type")
    @ApiModelProperty(value = "1",notes = "邀约类型",required = true,name = "invite_type",example = "82381006",dataType = "Integer")
    private Integer inviteType;

    /**
     * CDP提供的日均里程
     */
    @TableField("daily_mile")
    @ApiModelProperty(value = "CDP提供的日均里程", example = "9478.23")
    private String dailyMile;

    /**
     * icm线索ID
     */
    @TableField("icm_id")
    @ApiModelProperty(value = "icm线索ID", example = "78")
    private Long icmId;

    // 卡卷模版id
    @TableField("coupon_id")
    @ApiModelProperty(value = "coupon_id",name = "couponId", dataType = "Long")
    private Long couponId;

    // 卡卷模版CODE
    @TableField("coupon_code")
    @ApiModelProperty(value = "coupon_code",name = "couponCode", dataType = "String")
    private String couponCode;

    // 卡卷模版名称
    @TableField("coupon_name")
    @ApiModelProperty(value = "coupon_name",name = "couponName", dataType = "String")
    private String couponName;

    // 卡劵id
    @TableField("detail_id")
    @ApiModelProperty(value = "detail_id",name = "detailId", dataType = "Long")
    private Long detailId;

    // 核销经销商
    @TableField("exchange_owner_code")
    @ApiModelProperty(value = "exchange_owner_code",name = "exchangeOwnerCode", dataType = "String")
    private String exchangeOwnerCode;

    // 核销工单号
    @TableField("exchange_ro_no")
    @ApiModelProperty(value = "exchange_ro_no",name = "exchangeRoNo", dataType = "String")
    private String exchangeRoNo;

    // 卡劵状态
    @TableField("coupon_state")
    @ApiModelProperty(value = "coupon_state",name = "couponState", dataType = "Integer")
    private Integer couponState;

    // 核销时间
    @TableField("coupon_state_time")
    @ApiModelProperty(value = "coupon_state_time",name = "couponStateTime", dataType = "String")
    private String couponStateTime;

    // 验证状态
    @TableField("verify_status")
    @ApiModelProperty(value = "验证状态",name = "verify_status", dataType = "Integer")
    private Integer verifyStatus;

    // 验证变更时间
    @TableField("verify_time")
    @ApiModelProperty(value = "验证变更时间",name = "verify_status", dataType = "LocalDate")
    private LocalDate verifyTime;

    // 返厂意向等级
    @TableField("return_intention_level")
    @ApiModelProperty(value = "返厂意向等级",name = "return_intention_level", dataType = "Integer")
    private Integer returnIntentionLevel;

    /**
     * 是否bev
     */
    @TableField("bev_flag")
    private Integer bevFlag;
    /**
     * 动力类型
     */
    @TableField("power_type")
    private String powerType;
    /**
     *订单id
     */
    @TableField("order_Id")
    private String orderId;

    /**
     *替换线索id
     */
    @TableField("replace_Record_Id")
    private String replaceRecordId;
}
