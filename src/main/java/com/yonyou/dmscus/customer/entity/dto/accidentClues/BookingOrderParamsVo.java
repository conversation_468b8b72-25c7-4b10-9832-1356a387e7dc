package com.yonyou.dmscus.customer.entity.dto.accidentClues;

import com.yonyou.mybatis.plugin.sensitive.SensitiveInfo;
import com.yonyou.mybatis.plugin.sensitive.SensitiveType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 预约登记保存主信息Vo
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiModel("预约登记保存主信息Vo")
@Data
public class BookingOrderParamsVo {

    //---------------------车主车辆信息模块begin--------------------

    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;
    @ApiModelProperty(value = "车架号",name = "vin")
    private String vin;
    @ApiModelProperty(value = "车架号",name = "vinForShow")
    @SensitiveInfo(SensitiveType.VIN)
    private String vinForShow;
    @ApiModelProperty(value = "车主编号",name = "ownerNo")
    private String ownerNo;
    @ApiModelProperty(value = "车主姓名",name = "ownerName")
    private String ownerName;
    @ApiModelProperty(value = "车主oneId",name = "ownerOneId")
    private Integer ownerOneId;
    @ApiModelProperty(value = "预约人oneId",name = "oneId")
    private Integer oneId;
    /**以车辆的销售日期与当前日期进行比较*/
    @ApiModelProperty(value = "车龄",name = "autoAge")
    private Integer autoAge;
    // 预约人
    @ApiModelProperty(value = "联系人",name = "contactorName")
    private String contactorName;
    @ApiModelProperty(value = "联系人手机",name = "contactorMobile")
    private String contactorMobile;
    @ApiModelProperty(value = "联系人电话",name = "contactorPhone")
    private String contactorPhone;
    @ApiModelProperty(value = "品牌",name = "brand")
    private String brand;
    @ApiModelProperty(value = "车系",name = "series")
    private String series;
    @ApiModelProperty(value = "车型",name = "model")
    private String model;
    @ApiModelProperty(value = "年款",name = "yearModel")
    private String yearModel;
    @ApiModelProperty(value="配置",name="config")
    private String config;
    @ApiModelProperty(value="车型代码",name="modelCode")
    private String modelCode;
    @ApiModelProperty(value="进厂行驶里程",name="inMileage")
    private Double inMileage;
    //---------------------车主车辆信息模块end--------------------

    //---------------------预约登记信息模块begin------------------

    @ApiModelProperty(value = "预约单号",name = "bookingOrderNo")
    private String bookingOrderNo;
    @ApiModelProperty(value = "预约进厂时间",name = "bookingComeTime")
    private String bookingComeTime;
    @ApiModelProperty(value = "预约工位",name = "bookingStation")
    private String bookingStation;
    /**新加字段 当前时间*/
    @ApiModelProperty(value = "预约登记时间",name = "acceptBookingDate")
    private String acceptBookingDate;
    @ApiModelProperty(value = "经销商代码",name = "ownerCode")
    private String ownerCode;
    @ApiModelProperty(value = "公司代码",name = "orgId")
    private String orgId;
    @ApiModelProperty(value = "预约维修类型",name = "repairCategoryCode")
    private String repairCategoryCode;
    @ApiModelProperty(value = "预约来源",name = "bookingSource")
    private Integer bookingSource;
    @ApiModelProperty(value = "预约类别",name = "bookingTypeCode")
    private Integer bookingTypeCode;
    @ApiModelProperty(value = "预计交车时间",name = "endTimeSupposed")
    private String endTimeSupposed;
    @ApiModelProperty(value = "预约状态",name = "bookingOrderStatus")
    private String bookingOrderStatus;
    @ApiModelProperty(value = "确认预约时间",name = "confirmOrderDate")
    private String confirmOrderDate;
    @ApiModelProperty(value = "指定技师",name = "chiefTechnician")
    private String chiefTechnician;
    @ApiModelProperty(value = "服务专员",name = "serviceAdvisor")
    private String serviceAdvisor;
    @ApiModelProperty(value = "是否取送车",name = "isFetchDeliverCar")
    private String isFetchDeliverCar;
    @ApiModelProperty(value = "创建来源",name = "createSource")
    private Integer createSource;
    @ApiModelProperty(value = "预约单创建时间",name = "createdAt")
    private String createdAt;
    @ApiModelProperty(value = "是否送车",name = "isDeliver")
    private Integer isDeliver;
    @ApiModelProperty(value = "是否取车",name = "needAscDrive")
    private Integer needAscDrive;
    @ApiModelProperty(value = "是否餐食",name = "isMeal")
    private Integer isMeal;
    @ApiModelProperty(value = "维修类型",name = "repairTypeCode")
    private String repairTypeCode;
    @ApiModelProperty(value = "预约限量数量",name = "resourceQuantity")
    private Integer resourceQuantity;
    @ApiModelProperty(value = "折扣名称",name = "discountModeCode")
    private String discountModeCode;
    //---------------------预约登记信息模块end--------------------


    //---------------------预估信息begin--------------------

    @ApiModelProperty(value = "预估总金额",name = "estimateAmount")
    private Double estimateAmount;
    @ApiModelProperty(value = "预估工时费",name = "estimateAmountLabour")
    private Double estimateAmountLabour;
    @ApiModelProperty(value = "预估零件费",name = "estimateAmountPart")
    private Double estimateAmountPart;
    @ApiModelProperty(value = "预估附加项目费",name = "estimateAmountAdditem")
    private Double estimateAmountAdditem;
    @ApiModelProperty(value = "修改次数",name = "changeNumber")
    private Integer changeNumber;
    @ApiModelProperty(value = "客户需求",name = "customerDesc")
    private String customerDesc;
    @ApiModelProperty(value = "备注",name = "remark")
    private String remark;
    //---------------------预估信息end-------------------------
    /** 新增字段是否缺料登记*/
    @ApiModelProperty(value = "是否缺料登记",name = "isLackPart")
    private Integer isLackPart;

    /** 新增字段是否提前确认*/
    @ApiModelProperty(value = "是否提前确认",name = "advanceOrderConfirmationStatus")
    private Integer advanceOrderConfirmationStatus;

    /** 新增字段是否提前确认*/
    @ApiModelProperty(value = "中台预约单号",name = "APPOINTMENT_ID")
    private String appointmentId;

    @ApiModelProperty(value = "发动机号",name = "ENGINE_NO")
    private String engineNo;

    @ApiModelProperty(value = "发动机代码",name = "ENGINE_CODE")
    private String engineCode;

    @ApiModelProperty(value = "上次出厂里程",name = "LAST_MAINTAIN_MILEAGE")
    private String lastMaintainMileage;

    @ApiModelProperty(value = "距离进厂分钟",name = "comingMinutes")
    private String comingMinutes;

    @ApiModelProperty(value = "乐观锁",name = "recordVersion")
    private Integer recordVersion;

    @ApiModelProperty(value = "数据源",name = "dataSources")
    private Integer dataSources;

    @ApiModelProperty(value = "是否事故线索预约单，10041001-是 10041002-否",name = "isAccidentClues")
    private Integer isAccidentClues;

    @ApiModelProperty(value = "事故线索ID",name = "acId")
    private Integer acId;

    @ApiModelProperty(value = "系统标识",name = "appId")
    private String appId;

    /**
     * 交修项目list
     */
    private List<HandRepairProjectParamsVo> handRepairProjectList;

    /**
     * 维修工时
     */
    private List<RepairWorkHourParamsVo> repairWorkHourList;

    /**
     * 维修零件
     */
    private List<RepairPartParamsVo> repairPartList;


    /**
     * 附加项目集合
     */
    private List<RepairAppendItemQueryiParamsVo> addItemList;

    /**
     * 维修工具
     */
    private List<RepairToolQueryiParamsVo> repairToolList;

    /**
     * checkBox属性
     */
    private List<CheckBoxSettingVo> checkBoxSettingList;

    /**
     * checkBox属性
     */
    private Map<String,Integer> checkBoxTypeMap;
}
