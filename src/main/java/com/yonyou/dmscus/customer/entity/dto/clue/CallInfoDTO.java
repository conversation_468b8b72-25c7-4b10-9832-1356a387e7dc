package com.yonyou.dmscus.customer.entity.dto.clue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * description 外呼信息参数
 *
 * <AUTHOR>
 * @date 2023/8/21 18:00
 */
@Data
@ApiModel(description = "外呼信息参数")
public class CallInfoDTO {
    /**
     * 坐席联系时间
     */
    @ApiModelProperty(value = "联络完成时间")
    private String callEndTime;
    /**
     * 坐席姓名
     */
    @ApiModelProperty(value = "坐席姓名")
    private String firstCallSeat;
    /**
     * 拨打状态
     */
    @ApiModelProperty(value = "拨打状态")
    private String firstCallStatus;
    /**
     * 通话结果
     */
    @ApiModelProperty(value = "外呼结果")
    private String callResult;
    /**
     * 外呼备注
     */
    @ApiModelProperty(value = "备注")
    private String comments;
}
