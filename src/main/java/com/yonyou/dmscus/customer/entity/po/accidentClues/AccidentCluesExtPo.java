package com.yonyou.dmscus.customer.entity.po.accidentClues;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 事故线索-扩展表
 */
@ApiModel(description="事故线索-扩展表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@TableName(value = "tt_accident_clues_ext")
public class AccidentCluesExtPo {
  /**
   * ID
   */
  @TableId(value = "id", type = IdType.AUTO)
  @ApiModelProperty(value="ID")
  private Long id;

  /**
   * tt_accident_clues
   */
  @TableField(value = "ac_id")
  @ApiModelProperty(value="tt_accident_clues")
  private Integer acId;

  /**
   * 线索ID
   */
  @TableField(value = "crm_id")
  @ApiModelProperty(value="线索ID")
  private Long crmId;

  /**
   * 线索id
   */
  @TableField(value = "source_clue_id")
  @ApiModelProperty(value="线索id")
  private String sourceClueId;

  /**
   * 出险时间
   */
  @TableField(value = "accident_date")
  @ApiModelProperty(value="出险时间")
  private Date accidentDate;

  /**
   * 来源渠道
   */
  @TableField(value = "source_channel")
  @ApiModelProperty(value="来源渠道")
  private String sourceChannel;

  /**
   * 渠道标签
   */
  @TableField(value = "channel_type")
  @ApiModelProperty(value="渠道标签")
  private String channelType;

  /**
   * 是否重复线索
   */
  @TableField(value = "repeat_lead")
  @ApiModelProperty(value="是否重复线索")
  private Integer repeatLead;

  /**
   * 重复线索ID,多个用","分割
   */
  @TableField(value = "repeat_lead_id")
  @ApiModelProperty(value="重复线索ID,多个用','分割")
  private String repeatLeadId;

  /**
   * 报案号
   */
  @TableField(value = "regist_no")
  @ApiModelProperty(value="报案号")
  private String registNo;

  /**
   * 送返修标识
   */
  @TableField(value = "client_type")
  @ApiModelProperty(value="送返修标识")
  private String clientType;

  /**
   * 车型名称
   */
  @TableField(value = "model_name")
  @ApiModelProperty(value="车型名称")
  private String modelName;

  /**
   * 日均里程
   */
  @TableField(value = "daily_mile")
  @ApiModelProperty(value="日均里程")
  private String dailyMile;

  /**
   * 当前里程
   */
  @TableField(value = "current_mile")
  @ApiModelProperty(value="当前里程")
  private String currentMile;

  /**
   * 年款
   */
  @TableField(value = "model_year")
  @ApiModelProperty(value="年款")
  private String modelYear;

  /**
   * 车辆类型
   */
  @TableField(value = "vehicle_type")
  @ApiModelProperty(value="车辆类型")
  private String vehicleType;

  /**
   * 车主名称
   */
  @TableField(value = "owner_name")
  @ApiModelProperty(value="车主名称")
  private String ownerName;

  /**
   * 车主手机号
   */
  @TableField(value = "owner_mobile")
  @ApiModelProperty(value="车主手机号")
  private String ownerMobile;

  /**
   * 出险原因（内容）
   */
  @TableField(value = "accident_reason")
  @ApiModelProperty(value="出险原因（内容）")
  private String accidentReason;

  /**
   * 出险地点经度
   */
  @TableField(value = "damage_addr_lng")
  @ApiModelProperty(value="出险地点经度")
  private String damageAddrLng;

  /**
   * 出险地点纬度
   */
  @TableField(value = "damage_addr_lat")
  @ApiModelProperty(value="出险地点纬度")
  private String damageAddrLat;

  /**
   * 经销商类型
   */
  @TableField(value = "dealer_type")
  @ApiModelProperty(value="经销商类型")
  private String dealerType;

  /**
   * 经销商名称
   */
  @TableField(value = "dealer_name")
  @ApiModelProperty(value="经销商名称")
  private String dealerName;

  /**
   * 经销商来源
   */
  @TableField(value = "dealer_code_soure")
  @ApiModelProperty(value="经销商来源")
  private String dealerCodeSoure;

  /**
   * 进线类型
   */
  @TableField(value = "call_type")
  @ApiModelProperty(value="进线类型")
  private String callType;

  /**
   * 是否报警
   */
  @TableField(value = "call_police_flag")
  @ApiModelProperty(value="是否报警")
  private Integer callPoliceFlag;

  /**
   * 联络完成时间
   */
  @TableField(value = "call_end_time")
  @ApiModelProperty(value="联络完成时间")
  private String callEndTime;

  /**
   * 省份id
   */
  @TableField(value = "province_id")
  @ApiModelProperty(value="省份id")
  private String provinceId;

  /**
   * 省份名称
   */
  @TableField(value = "province_name")
  @ApiModelProperty(value="省份名称")
  private String provinceName;

  /**
   * 城市id
   */
  @TableField(value = "city_id")
  @ApiModelProperty(value="城市id")
  private String cityId;

  /**
   * 城市名称
   */
  @TableField(value = "city_name")
  @ApiModelProperty(value="城市名称")
  private String cityName;

  /**
   * 客户类型code
   */
  @TableField(value = "contacts_type")
  @ApiModelProperty(value="客户类型code")
  private String contactsType;

  /**
   * 客户类型name
   */
  @TableField(value = "contacts_name")
  @ApiModelProperty(value="客户类型name")
  private String contactsName;

  /**
   * 删除标识（0-未删除，1-已删除）
   */
  @TableField(value = "is_deleted")
  @ApiModelProperty(value="删除标识（0-未删除，1-已删除）")
  private Integer isDeleted;

  /**
   * 创建时间
   */
  @TableField(value = "create_time")
  @ApiModelProperty(value="创建时间")
  private Date createTime;

  /**
   * 更新时间
   */
  @TableField(value = "update_time")
  @ApiModelProperty(value="更新时间")
  private Date updateTime;

  /**
   * 创建人
   */
  @TableField(value = "create_by")
  @ApiModelProperty(value="创建人")
  private String createBy;

  /**
   * 更新人
   */
  @TableField(value = "update_by")
  @ApiModelProperty(value="更新人")
  private String updateBy;

  /**
   * 创建sql人
   */
  @TableField(value = "create_sqlby")
  @ApiModelProperty(value="创建sql人")
  private String createSqlby;

  /**
   * 更新sql人
   */
  @TableField(value = "update_sqlby")
  @ApiModelProperty(value="更新sql人")
  private String updateSqlby;

  @TableField("first_follow_time")
  @ApiModelProperty("首次跟进时间")
  private Date firstFollowTime;

  @TableField("last_follow_time")
  @ApiModelProperty("最后跟进时间")
  private Date lastFollowTime;

  @TableField("follow_text")
  @ApiModelProperty("最后跟进内容")
  private String followText;

  @TableField("follow_fail_why")
  @ApiModelProperty("跟进失败原因")
  private Integer followFailWhy;

  @TableField("insurance_company_code")
  @ApiModelProperty("保险公司code")
  private String insuranceCompanyCode;

  @TableField(value = "repeat_son_lead_id")
  private String repeatSonLeadId;

  @TableField(value = "parent_crm_id")
  private Long parentCrmId;

  @TableField(value = "old_follow_status")
  private Integer oldFollowStatus;


}