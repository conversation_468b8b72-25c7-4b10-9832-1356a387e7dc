package com.yonyou.dmscus.customer.entity.dto.loss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @title: LossDataRecord
 * @projectName dmscus.customer
 * @date 2022/12/1217:35
 */
@Data
@ApiModel(value = "LossDataRecordDto", description = "流失客户表")
public class LossDataRecordDto  {
    @ApiModelProperty(value = "vin",notes = "vin",required = true,name = "vin",example = "SHJ",dataType = "String")
    private String vin;


    @ApiModelProperty(value = "经销商",notes = "经销商",required = true,name = "dealerCode",example = "SHJ",dataType = "String")
    private String dealerCode;

    @ApiModelProperty(value = "最后一次保养时间",notes = "最后一次保养时间:指上一次含机滤工单结算日期",required = true,name = "orderAt",example = "",dataType = "Date")
    private Date orderAt;


    @ApiModelProperty(value = "最后一次保养经销商:指机率工单经销商",notes = "最后一次保养经销商:指机率工单经销商",required = true,name = "lastDealerCode",example = "SHJ",dataType = "String")
    private String  lastDealerCode;
    @ApiModelProperty(value = "流失线索下发时间",notes = "流失线索下发时间",required = true,name = "recordAt",example = "",dataType = "Date")
    private Date recordAt;

    @ApiModelProperty(value = "开票时间",notes = "开票时间",required = true,name = "invoiceDate",example = "",dataType = "Date")
    private Date invoiceDate;

    @ApiModelProperty(value = "激活时间:线索下发后的机滤工单时间",notes = "激活时间:线索下发后的机滤工单时间",required = true,name = "activeAt",example = "",dataType = "Date")
    private Date activeAt;

    @ApiModelProperty(value = "激活经销商:线索下发后的机率工单开单经销商",notes = "激活经销商:线索下发后的机率工单开单经销商",required = true,name = "activeDealerCode",example = "",dataType = "String")
    private String activeDealerCode;

    @ApiModelProperty(value = "是否激活:1,是；0,否",notes = "是否激活:1,是；0,否",required = true,name = "isActive",example = "",dataType = "Integer")
    private Integer isActive;

    @ApiModelProperty(value = "客户姓名:有进厂取最近一次送修人信息，没有进厂取店端开票信息",notes = "客户姓名:有进厂取最近一次送修人信息，没有进厂取店端开票信息",required = true,name = "ownerName",example = "",dataType = "String")
    private String ownerName;

    @ApiModelProperty(value = "手机:有进厂取最近一次送修人信息，没有进厂取店端开票信息",notes = "手机:有进厂取最近一次送修人信息，没有进厂取店端开票信息",required = true,name = "mobile",example = "",dataType = "String")
    private String mobile;

    private int currentPage;
    private int pageSize;
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        LossDataRecordDto that = (LossDataRecordDto) o;
        return Objects.equals(vin, that.vin) &&
                Objects.equals(dealerCode, that.dealerCode) &&
                Objects.equals(orderAt, that.orderAt) &&
                Objects.equals(lastDealerCode, that.lastDealerCode) &&
                Objects.equals(recordAt, that.recordAt) &&
                Objects.equals(invoiceDate, that.invoiceDate) &&
                Objects.equals(activeAt, that.activeAt) &&
                Objects.equals(activeDealerCode, that.activeDealerCode) &&
                Objects.equals(isActive, that.isActive) &&
                Objects.equals(ownerName, that.ownerName) &&
                Objects.equals(mobile, that.mobile);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), vin, dealerCode, orderAt, lastDealerCode, recordAt, invoiceDate, activeAt, activeDealerCode, isActive, ownerName, mobile);
    }

}
