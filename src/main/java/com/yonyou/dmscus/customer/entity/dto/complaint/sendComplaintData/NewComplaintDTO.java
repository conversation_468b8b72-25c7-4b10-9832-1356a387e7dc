package com.yonyou.dmscus.customer.entity.dto.complaint.sendComplaintData;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 新建客诉案例发送到400
 */
@Data
public class NewComplaintDTO {

    /**
     * 投诉ID（投诉编号）可 用于和呼叫中心对接
     */
    private String ComplainID;

    /**
     * 投诉类型
     */
    private String ComplainType;

    /**
     * 投诉来源
     */
    private String Source;

    /**
     * 来电时间/投诉日期
     */

    private String ComplainDT;

    /**
     * 数据来源
     */
    private String ComplainSource;

    /**
     * 工单状态
     */
    private Integer JobOrderStatus;

    /**
     * 来电客户姓名/投诉人姓名
     */
    private String ReplyContacts;

    /**
     * 来电电话/投诉人电话
     */
    private String ReplyMobile;

    /**
     * 车牌号
     */
    private String RelatedLicense;

    /**
     * 车架号
     */
    private String RelatedVin;

    /**
     * 购车时间
     */

    private String BuyDT;

    /**
     * 车型
     */
    private String RelatedModel;

    /**
     * 年款
     */
    private String RelatedVersion;

    /**
     * 购买经销商
     */
    private String Buy_DlrName;

    /**
     * 购买经销商代码
     */
    private String Buy_DlrCode;

    /**
     * 处理经销商
     */
    private String Proc_DlrName;

    /**
     * 处理经销商代码
     */
    private String Proc_DlrCode;

    /**
     * 里程
     */
    private Integer Mileage;

    /**
     * 投诉主题
     */
    private String Subject;

    /**
     * 问题描述
     */
    private String ProblemDescr;

    /**
     * 问题
     */
    private String CategoryProplem;

    /**
     * 要求
     */
    private String CustomerClaims;
    /**
     * 工单性质
     */
    private String Nature;
    /**
     * 投诉单类别一级层
     */
    private String Category_1;
    /**
     * 投诉单类别二级层
     */
    private String Category_2;

    /**
     * 投诉单类别三级层
     */
    private String Category_3;




}
