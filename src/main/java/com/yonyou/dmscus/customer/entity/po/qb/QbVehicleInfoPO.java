package com.yonyou.dmscus.customer.entity.po.qb;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tt_qb_vehicle_info")
public class QbVehicleInfoPO extends BasePO<QbVehicleInfoPO> {

  @TableField("id")
  private Integer id;

  @TableField("qb_number_id")
  private Integer qbNumberId;

  @TableField("vin")
  private String vin;

  @TableField("dealer_code")
  private String dealerCode;

  @TableField("dealer_name")
  private String dealerName;

  @TableField("is_performed")
  private Integer isPerformed;

  @TableField("performed_time")
  private Date performedTime;

  @TableField("APP_ID")
  private String appId;

  @TableField("OWNER_CODE")
  private String ownerCode;

  @TableField("OWNER_PAR_CODE")
  private String ownerParCode;

  @TableField("ORG_ID")
  private Integer orgId;

  @TableField("data_sources")
  private Integer dataSources;

  @TableField("is_valid")
  private Integer isValid;

  @TableField("is_deleted")
  private Integer isDeleted;

  @TableField("created_at")
  private Date createdAt;

  @TableField("created_by")
  private String createdBy;

  @TableField("updated_at")
  private Date updatedAt;

  @TableField("updated_by")
  private String updatedBy;

  @TableField("mark")
  private String mark;

  @TableField("qb_number")
  private String qbNumber;


}
