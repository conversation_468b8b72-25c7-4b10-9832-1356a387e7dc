package com.yonyou.dmscus.customer.entity.dto.middleground;

import com.yonyou.dmscus.customer.middleInterface.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(description = "返回结果")
public class MessageResponseDTO<T> implements Serializable {
	private static final long serialVersionUID = Constant.SERIAL_VERSION_UID;

	@ApiModelProperty(value = "返回代码，0表示成功，其他表示失败")
	private String code;

	@ApiModelProperty(value = "msgId")
	private String msgId;
	
	@ApiModelProperty(value = "错误原因")
	private String error;

	@ApiModelProperty(value = "返回数据")
	private T data;


	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMsgId() {
		return msgId;
	}

	public void setMsgId(String msgId) {
		this.msgId = msgId;
	}

	public String getError() {
		return error;
	}

	public void setError(String error) {
		this.error = error;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}
}
