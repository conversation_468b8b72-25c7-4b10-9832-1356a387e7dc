package com.yonyou.dmscus.customer.entity.dto.inviteInsurance;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 车辆邀约续保记录明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */

@Data
public class InviteInsuranceVehicleRecordDetailDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 邀约ID
     */
    private Long inviteId;

    /**
     * 跟进内容
     */
    private String content;

    /**
     * 客户反馈
     */
    private String feedback;

    /**
     * 备注
     */
    private String remark;

    /**
     * 跟进状态
     */
    private Integer status;

    /**
     * 失败原因
     */
    private Integer loseReason;

    /**
     * 不需跟进原因
     */
    private Integer notFollowReason;

    /**
     * 下次跟进日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planDate;

    /**
     * 实际跟进日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualDate;

    /**
     * 跟进方式
     */
    private Integer mode;

    /**
     * VIN
     */
    private String vin;

    /**
     * 邀约类型
     */
    private Integer inviteType;

    /**
     * 续保到期日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date adviseInDate;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updatedAt;


    /**
     * 跟进服务顾问ID
     */
    private String saId;

    /**
     * 跟进服务顾问姓名
     */
    private String saName;


    /**
     *经销商代码
     */
    private String dealerCode;


    /**
     * 新建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date newAdviseInDate;


    /**
     * call_id
     */
    private String callId;

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
