package com.yonyou.dmscus.customer.entity.dto.complaint;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import com.yonyou.dmscloud.function.utils.jsonSerializer.date.JsonDateDeserializer;
import com.yonyou.dmscloud.function.utils.jsonSerializer.date.JsonDateSerializer;

import java.io.Serializable;

import java.util.Date;

/**
 * <p>
 * 协助部门
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */

public class ComplaintAssistDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 投诉信息表主键ID
     */
    private Long complaintInfoId;

    /**
     * 跟进对象 区域经理、经销商、CCM、客服中心
     */
    private String object;

    /**
     * 分配时间
     */
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date followTime;

    /**
     * 分配人
     */
    private String follower;
    /**
     * 分配人
     */
    private String followerName;

    /**
     * 协助部门
     */
    private String assistDepartment;

    /**
     * 协助部门名称
     */
    private String assistDepartmentName;

    /**
     * 协助部门
     */
    private String assistDealerCode;

    /**
     * 协助部门名称
     */
    private String assistDealerName;

    /**
     * 希望回复时间
     */
    private Integer hopeReplyTime;

    /**
     * 需协助说明
     */
    private String assistExplain;

    /**
     * 是否回复
     */
    private Integer isReply;

    /**
     * 回复时间
     */
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date replyTime;

    /**
     * 是否完成
     */
    private Integer isFinish;

    /**
     * 完成时间
     */
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date finishTime;

    /**
     * 是否已读 1 已读 0 未读
     */
    private Boolean isRead;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date createdAt;

    private String allDealer;

    /**
     * 更新时间
     */
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date updatedAt;



    public ComplaintAssistDTO() {
        super();
    }


    public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
    }

    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerParCode(){
        return ownerParCode;
    }


    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public Integer getOrgId(){
        return orgId;
    }


    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
    }

    public Long getComplaintInfoId(){
        return complaintInfoId;
    }


    public void  setComplaintInfoId(Long complaintInfoId) {
        this.complaintInfoId = complaintInfoId;
    }

    public String getObject(){
        return object;
    }

    public String getFollowerName() {
        return followerName;
    }

    public void setFollowerName(String followerName) {
        this.followerName = followerName;
    }

    public void  setObject(String object) {
        this.object = object;
    }

    public Date getFollowTime(){
        return followTime;
    }


    public void  setFollowTime(Date followTime) {
        this.followTime = followTime;
    }

    public String getFollower(){
        return follower;
    }


    public void  setFollower(String follower) {
        this.follower = follower;
    }

    public String getAssistDepartment(){
        return assistDepartment;
    }


    public void  setAssistDepartment(String assistDepartment) {
        this.assistDepartment = assistDepartment;
    }

    public String getAssistDepartmentName(){
        return assistDepartmentName;
    }


    public void  setAssistDepartmentName(String assistDepartmentName) {
        this.assistDepartmentName = assistDepartmentName;
    }

    public String getAssistDealerCode(){
        return assistDealerCode;
    }


    public void  setAssistDealerCode(String assistDealerCode) {
        this.assistDealerCode = assistDealerCode;
    }

    public String getAssistDealerName(){
        return assistDealerName;
    }


    public void  setAssistDealerName(String assistDealerName) {
        this.assistDealerName = assistDealerName;
    }

    public Integer getHopeReplyTime(){
        return hopeReplyTime;
    }


    public void  setHopeReplyTime(Integer hopeReplyTime) {
        this.hopeReplyTime = hopeReplyTime;
    }

    public String getAssistExplain(){
        return assistExplain;
    }


    public void  setAssistExplain(String assistExplain) {
        this.assistExplain = assistExplain;
    }

    public Integer getIsReply(){
        return isReply;
    }


    public void  setIsReply(Integer isReply) {
        this.isReply = isReply;
    }

    public Date getReplyTime(){
        return replyTime;
    }


    public void  setReplyTime(Date replyTime) {
        this.replyTime = replyTime;
    }

    public Integer getIsFinish(){
        return isFinish;
    }


    public void  setIsFinish(Integer isFinish) {
        this.isFinish = isFinish;
    }

    public Date getFinishTime(){
        return finishTime;
    }


    public void  setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Boolean getIsRead(){
        return isRead;
    }


    public void  setIsRead(Boolean isRead) {
        this.isRead = isRead;
    }

    public Integer getDataSources(){
        return dataSources;
    }


    public void  setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid(){
        return isValid;
    }


    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public String getAllDealer() {
        return allDealer;
    }

    public void setAllDealer(String allDealer) {
        this.allDealer = allDealer;
    }

    public Date getCreatedAt(){
        return createdAt;
    }


    public void  setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt(){
        return updatedAt;
    }


    public void  setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "ComplaintAssistDepartmentDTO{" +
                "appId=" + appId +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", complaintInfoId=" + complaintInfoId +
                ", object=" + object +
                ", followTime=" + followTime +
                ", follower=" + follower +
                ", assistDepartment=" + assistDepartment +
                ", assistDepartmentName=" + assistDepartmentName +
                ", assistDealerCode=" + assistDealerCode +
                ", assistDealerName=" + assistDealerName +
                ", hopeReplyTime=" + hopeReplyTime +
                ", assistExplain=" + assistExplain +
                ", isReply=" + isReply +
                ", replyTime=" + replyTime +
                ", isFinish=" + isFinish +
                ", finishTime=" + finishTime +
                ", isRead=" + isRead +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
