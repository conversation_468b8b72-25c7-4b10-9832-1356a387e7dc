package com.yonyou.dmscus.customer.entity.dto.maintaininfo;

/**
 * description: add a description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/12/07 15:30:00
 */

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MaintainInfoDTO extends BaseDTO implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 工单号
     */
    private String roNo;

    /**
     * 经销商code
     */
    private String ownerCode;

    /**
     * 车辆唯一识别代码
     */
    private String vin;

    /**
     * 进厂里程
     */
    private String maintainMileage;

    /**
     * 保养时间
     */
    private Date maintainTime;

    /**
     * 上一次进厂里程
     */
    private String beforeMaintainMileage;

    /**
     * 上一次保养时间
     */
    private Date beforeMaintainTime;

    /**
     * 提前进厂里程
     */
    private Double diffMileage;

    /**
     * 提前进厂时间(月份呢)
     */
    private Integer diffMonth;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 将DTO 转换为PO
     */
    public <T extends BasePO> T transPoToDto(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     */
    public <T extends BasePO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
