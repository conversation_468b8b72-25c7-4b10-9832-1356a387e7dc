package com.yonyou.dmscus.customer.entity.po.inviteInsurance;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordDetailPO;
import io.swagger.models.auth.In;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 车辆邀约续保记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@TableName("tt_invite_insurance_vehicle_record")
@Data
public class InviteInsuranceVehicleRecordPO extends BasePO<InviteInsuranceVehicleRecordPO> {

    private static final long serialVersionUID = 1L;


    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;


    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 邀约ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 邀约父类ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 是否主要线索:1主要线索、0附属线索
     */
    @TableField("is_main")
    private Integer isMain;

    /**
     * 邀约来源类型：1VCDC下发邀约、2经销商自建邀约、3VOC事故邀约
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 车架号
     */
    @TableField("vin")
    private String vin;

    /**
     * 车牌号
     */
    @TableField("license_plate_num")
    private String licensePlateNum;

    /**
     * 车主姓名
     */
    @TableField("name")
    private String name;

    /**
     * 车主电话
     */
    @TableField("tel")
    private String tel;

    /**
     * 邀约类型
     */
    @TableField("invite_type")
    private Integer inviteType;

    /**
     * 建议进厂日期
     */
    @TableField("advise_in_date")
    private Date adviseInDate;

    /**
     * 新建议进厂日期
     */
    @TableField("new_advise_in_date")
    private Date newAdviseInDate;

    /**
     * 最新建议进厂日期
     */
    @TableField("newest_advise_in_date")
    private Date newestAdviseInDate;

    /**
     * 计划跟进日期
     */
    @TableField(value ="plan_follow_date",updateStrategy = FieldStrategy.IGNORED,jdbcType = JdbcType.DATE)
    private Date planFollowDate;



    /**
     * 实际跟进日期
     */
    @TableField("actual_follow_date")
    private Date actualFollowDate;

    /**
     * 计划提醒日期
     */
    @TableField("plan_remind_date")
    private Date planRemindDate;

    /**
     * 实际提醒日期
     */
    @TableField("actual_remind_date")
    private Date actualRemindDate;


    /**
     * 首次跟进时间
     */
    @TableField("first_follow_date")
    private Date firstFollowDate;


    /**
     * 线索完成时间
     */
    @TableField("order_finish_date")
    private Date orderFinishDate;


    /**
     * 跟进服务顾问ID
     */
    @TableField(value = "SA_ID",updateStrategy = FieldStrategy.IGNORED,jdbcType = JdbcType.VARCHAR)
    private String saId;

    /**
     * 跟进服务顾问姓名
     */
    @TableField(value = "SA_NAME",updateStrategy = FieldStrategy.IGNORED,jdbcType = JdbcType.VARCHAR)
    private String saName;

    /**
     * 上次跟进服务顾问ID
     */
    @TableField("LAST_SA_ID")
    private String lastSaId;

    /**
     * 上次跟进服务顾问姓名
     */
    @TableField("LAST_SA_NAME")
    private String lastSaName;

    /**
     * 跟进状态
     */
    @TableField("follow_status")
    private Integer followStatus;

    /**
     * 是否预约单：1 是，0 否
     */
    @TableField("is_book")
    private Integer isBook;

    /**
     * 预约单号
     */
    @TableField("book_no")
    private String bookNo;

    /**
     * 线索完成状态
     */
    @TableField("order_status")
    private Integer orderStatus;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;

    /**
     * 经销商代码
     */
    @TableField("dealer_code")
    private  String dealerCode;

    /**
     * 车主年龄
     */
    @TableField("age")
    private  String age;

    /**
     * 车主性别
     */
    @TableField("sex")
    private  String sex;

    /**
     * 车型
     */
    @TableField("model")
    private  String model;


    /**
     * 客户唯一id
     */
    @TableField("one_id")
    private Long oneId;



    /**
     * 邀约类型 查询条件
     */
    @TableField(exist = false)
    private List<Integer> inviteTypeParam;


    /**
     * 计划跟进日期开始 查询条件
     */
    @TableField(exist = false)
    private String planFollowDateStart;

    /**
     * 计划跟进日期结束 查询条件
     */
    @TableField(exist = false)
    private String planFollowDateEnd;

    /**
     * 实际跟进日期开始 查询条件
     */
    @TableField(exist = false)
    private String actualFollowDateStart;

    /**
     * 实际跟进日期结束 查询条件
     */
    @TableField(exist = false)
    private String actualFollowDateEnd;

    /**
     * 建议进厂日期开始 查询条件
     */
    @TableField(exist = false)
    private String adviseInDateStart;

    /**
     * 建议进厂日期结束 查询条件
     */
    @TableField(exist = false)
    private String adviseInDateEnd;


    /**
     * 邀约状态 查询条件
     */
    @TableField(exist = false)
    private List<Integer> followStatusParam;

    /**
     * 工单状态 线索完成状态
     */
    @TableField(exist = false)
    private List<Integer> orderStatusParam;

    /**
     * 离职用户 id 查询条件
     */
    @TableField(exist = false)
    private List<Integer> leaveIds;

    /**
     * 邀约创建日期开始 查询条件
     */
    @TableField(exist = false)
    private String createdAtStart;

    /**
     * 邀约创建日期结束 查询条件
     */
    @TableField(exist = false)
    private String createdAtEnd;


    /**
     * 是否逾期未跟进：1 是，0 否 查询条件
     */
    @TableField(exist = false)
    private Integer overdue;


    /**
     * 是否查询未分配 查询条件
     */
    @TableField(exist = false)
    private  Integer isNoDistribute;


    /**
     * 是否查询待分配 查询条件
     */
    @TableField(exist = false)
    private  Integer isWaitDistribute;

    /**
     * 易损件规则id
     */
    @TableField("part_item_rule_id")
    private Long partItemRuleId;


    /**
     * 易损件上次更换时间
     */
    @TableField("last_change_date")
    private Date lastChangeDate;

    /**
     * 易损件code
     */
    @TableField("item_code")
    private String itemCode;

    /**
     *易损件名称
     */
    @TableField("item_name")
    private String itemName;

    /**
     *易损件类型
     */
    @TableField("item_type")
    private Integer itemType;

    /**
     * 续保客户类型(81761001 新保客户  81761002 新转续  81761003  续转续   81761004 在修不在保)
     */
    @TableField("insurance_type")
    private Integer insuranceType;

    /**
     *保险投保单(tt_insurance_bill)表主键id'
     */
    @TableField("insurance_bill_id")
    private Long insuranceBillId;

    /**
     *线索类型：1:交强险   2:商业险'
     */
    @TableField("clue_type")
    private Integer clueType;

    /**
     *原线索记录新生的投保单号'
     */
    @TableField("new_insure_no")
    private String newInsureNo;


    @TableField("insurance_name")
    private String insuranceName;


    /**
     * 最新失败原因
     */
    @TableField(value = "lose_reason",updateStrategy = FieldStrategy.IGNORED,jdbcType = JdbcType.INTEGER)
    private Integer loseReason;


    /**
     * 最新跟进内容
     */
    @TableField(value = "content",updateStrategy = FieldStrategy.IGNORED,jdbcType = JdbcType.VARCHAR)
    private String content;


    @TableField(exist = false)
    private List<String> dealerCodes;


    /**
     * 邀约记录明细集合
     */
    @TableField(exist = false)
    private List<InviteVehicleRecordDetailPO> recordDetailList;


    /**
     * 是否voc车辆
     */
    @TableField(exist = false)
    private  Integer isVoc;


    /**
     * 上次保养日期
     */
    @TableField(exist = false)
    private  Date lastMaintenanceDate;


    /**
     * 建议入厂日期间隔
     */
    @TableField(exist = false)
    private Integer dateInterval;


    /**
     * 子线索
     */
    @TableField(exist = false)
    private String sonInviteType;

    /**
     *建议入厂里程
     */
    @TableField(exist = false)
    private Integer adviseInMileage;


    /**
     *任务基准日期
     */
    @TableField(exist = false)
    private Date inviteTime;

    /**
     *投保单状态
     */
    @TableField(exist = false)
    private Integer insuranceStatus;

    /**
     *是否联保
     */
    @TableField(exist = false)
    private Integer isJointGuarantee;


    /**
     * 最新得分
     */
    @TableField(exist = false)
    private Integer score;


    @TableField(exist = false)
    private Integer callLength;


    /**
     *    通话详情Id
     */
    @TableField(exist = false)
    private Long callDetailId;


    /**
     *    通话时间
     */
    @TableField(exist = false)
    private Date callTime;

    /**
     *    超时关闭间隔
     */
    @TableField(exist = false)
    private Integer closeInterval;

    /**
     *    跟进次数
     */
    @TableField(exist = false)
    private Integer followTotal;

    /**
     * 客户是否投保成功
     */
    @TableField(exist = false)
    private Integer isInsureSuccess;

    @TableField(exist = false)
    private Integer adviseDateInterval;

    @TableField(exist = false)
    private Integer planDateInterval;

    @TableField(exist = false)
    private Long insuranceId;

    /**
     * 厂家保险到期日期	日历选择
     */
    @TableField(exist = false)
    private String factoryInsuranceExpiryDate;


    /**
     * 建议关怀日期	日历选择
     */
    @TableField(exist = false)
    private String suggestedCareDate;

    /**
     * 线索数据源	下拉选择
     */
    @TableField(exist = false)
    private Integer clueDataSource;

    /**
     * 线索下发类型	下拉选择
     */
    @TableField(exist = false)
    private Integer clueIssuanceType;

    /**
     * 是否有自店线索升级	下拉选择
     */
    @TableField(exist = false)
    private Integer hasInstoreUpgrade;

    /**
     * 是否有自店修改保险到期日期	下拉选择
     */
    @TableField(exist = false)
    private Integer hasInstoreModified;

    @TableField(exist = false)
    private String factoryInsuranceExpiryDateStart;

    @TableField(exist = false)
    private String factoryInsuranceExpiryDateEnd;

    @TableField(exist = false)
    private String suggestedCareDateStart;

    @TableField(exist = false)
    private String suggestedCareDateEnd;

    @TableField(exist = false)
    private Date advanceIssuanceDate;

    @TableField(exist = false)
    private Date clueIssuanceDate;

    @TableField(exist = false)
    private Date instoreUpgradeTime;

    @TableField(exist = false)
    private Integer clueGenerator;

    @TableField(exist = false)
    private Date instoreModificationTime;

    @TableField(exist = false)
    private String insuranceNo;

    @TableField(exist = false)
    private Date policyCreationDate;

    @TableField(exist = false)
    private Integer policySource;

    @TableField(exist = false)
    private Date policyEffectiveDate;

    @TableField(exist = false)
    private Date policyExpirationDate;

    @TableField(exist = false)
    private Integer ownerType;

    @TableField(exist = false)
    private String newInsuranceNo;

    @TableField(exist = false)
    private String newInsuranceName;

    @TableField(exist = false)
    private Date newPolicyCreationDate;

    @TableField(exist = false)
    private Integer newPolicySource;

    @TableField(exist = false)
    private Date  newPolicyEffectiveDate;

    @TableField(exist = false)
    private String  commercialInsuranceType;

    @TableField(exist = false)
    private String insuranceNames;

    @TableField(exist = false)
    private String newCommercialInsuranceType;
    @TableField(exist = false)
    private  List<Integer> insuranceTypeList;


    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}

