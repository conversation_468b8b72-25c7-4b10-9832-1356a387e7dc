package com.yonyou.dmscus.customer.entity.po.accidentClues;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 事故线索分配
 */
@TableName("tt_accident_clues_allot")
@Data
public class AccidentCluesAllotPO extends BasePO<AccidentCluesAllotPO> {

  @TableField("app_id")
  private String appId;

  @TableField("owner_code")
  private String ownerCode;

  @TableField("owner_par_code")
  private String ownerParCode;

  @TableField("org_id")
  private Integer orgId;

  //主键ID
  @TableId("allot_id")
  private Integer allotId;

  //tt_accident_clues
  @TableField("ac_id")
  private Integer acId;

  //经销商代码"
  @TableField("dealer_code")
  private String dealerCode;

  //跟进人员"
  @TableField("follow_people")
  private Integer followPeople;

  //分配人员
  @TableField("allot_personnel")
  private Integer allotPersonnel;

  //分配时间
  @TableField("allot_date")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date allotDate;

  //数据来源
  @TableField("data_sources")
  private Integer dataSources;

  //是否删除，1：删除，0：未删除"
  @TableField("is_deleted")
  private Integer isDeleted;

  //是否有效"
  @TableField("is_valid")
  private Integer isValid;

  //创建时间"
  @TableField("created_at")
  private Date createdAt;

  //创建人"
  @TableField("created_by")
  private String createdBy;

  //更新时间"
  @TableField("updated_at")
  private Date updatedAt;

  //更新人
  @TableField("updated_by")
  private String updatedBy;

  //版本号（乐观锁）
  @TableField("record_version")
  private Integer recordVersion;
  //
  @TableField("allot_type")
  private Integer allotType;



}
