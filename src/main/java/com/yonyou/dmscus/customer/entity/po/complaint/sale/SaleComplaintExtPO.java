package com.yonyou.dmscus.customer.entity.po.complaint.sale;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 销售客诉拓展表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("tt_sale_complaint_ext")
public class SaleComplaintExtPO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableId("sale_complaint_id")
    private Long saleComplaintId;

    @TableId( "risk_type")
    private String riskType;

    @TableId("case_create_time")
    private Date caseCreateTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    @TableField("record_version")
    private Integer recordVersion;

    /**
     * '厂端描述'
     */
    @TableField("plant_description")
    private String plantDescription;
}
