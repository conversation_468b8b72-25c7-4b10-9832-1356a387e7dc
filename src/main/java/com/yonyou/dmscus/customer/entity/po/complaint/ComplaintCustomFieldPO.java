package com.yonyou.dmscus.customer.entity.po.complaint;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

    import com.yonyou.dmscloud.framework.base.po.BasePO;

import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;

import java.util.Date;

/**
 * <p>
 * 客户投诉自定义字段 用于是否查询条件及是否排序判断
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
@TableName("tt_complaint_custom_field")
public class ComplaintCustomFieldPO extends BasePO<ComplaintCustomFieldPO> {

    private static final long serialVersionUID=1L;
    
    /**
     * 系统ID
     */
    @TableField("app_id")
        private String appId;
    
    /**
     * 所有者代码 
     */
    @TableField("owner_code")
        private String ownerCode;
    
    /**
     * 所有者的父组织代码 
     */
    @TableField("owner_par_code")
        private String ownerParCode;
    
    /**
     * 组织ID
     */
    @TableField("org_id")
        private Integer orgId;
        
    /**
     * 主键ID
     */
            @TableId(value = "id", type = IdType.AUTO)
                        private Long id;
    
    /**
     * 表名
     */
    @TableField("table_name")
        private String tableName;
    
    /**
     * 字段名
     */
    @TableField("field_name")
        private String fieldName;
    
    /**
     * 字段描述
     */
    @TableField("field_describe")
        private String fieldDescribe;
    
    /**
     * 是否查询 1 是 0 否
     */
    @TableField("is_query")
        private Boolean isQuery;
    
    /**
     * 是否排序 1 是 0 否
     */
    @TableField("is_sort")
        private Integer isSort;
    
    /**
     * 数据来源 
     */
    @TableField("data_sources")
        private Integer dataSources;
    
    /**
     * 是否删除
     */
    @TableField("is_deleted")
        private Boolean isDeleted;
    
    /**
     * 是否有效 
     */
    @TableField("is_valid")
        private Integer isValid;
    
    /**
     * 创建时间
     */
    @TableField("created_at")
        private Date createdAt;
    
    /**
     * 更新时间
     */
    @TableField("updated_at")
        private Date updatedAt;

    public ComplaintCustomFieldPO(){
        super();
    }

                    

                    
    public String getOwnerCode(){
        return ownerCode;
    }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
            }
                    

                    
    public Integer getOrgId(){
        return orgId;
    }

        public void setOrgId(Integer orgId) {
            this.orgId = orgId;
            }
                    
    public Long getId(){
        return id;
    }

        public void setId(Long id) {
            this.id = id;
            }
                    
    public String getTableName(){
        return tableName;
    }

        public void setTableName(String tableName) {
            this.tableName = tableName;
            }
                    
    public String getFieldName(){
        return fieldName;
    }

        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
            }
                    
    public String getFieldDescribe(){
        return fieldDescribe;
    }

        public void setFieldDescribe(String fieldDescribe) {
            this.fieldDescribe = fieldDescribe;
            }
                    
    public Boolean getIsQuery(){
        return isQuery;
    }

        public void setIsQuery(Boolean isQuery) {
            this.isQuery = isQuery;
            }
                    
    public Integer getIsSort(){
        return isSort;
    }

        public void setIsSort(Integer isSort) {
            this.isSort = isSort;
            }
                    
    public Integer getDataSources(){
        return dataSources;
    }

        public void setDataSources(Integer dataSources) {
            this.dataSources = dataSources;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }

        public void setIsDeleted(Boolean isDeleted) {
            this.isDeleted = isDeleted;
            }
                    
    public Integer getIsValid(){
        return isValid;
    }

        public void setIsValid(Integer isValid) {
            this.isValid = isValid;
            }
                    

    @Override
    protected Serializable pkVal(){
            return this.id;
        }

@Override
    public String toString(){
        return"ComplaintCustomFieldPO{" +
                            "appId=" + appId +
                                    ", ownerCode=" + ownerCode +
                                    ", ownerParCode=" + ownerParCode +
                                    ", orgId=" + orgId +
                                    ", id=" + id +
                                    ", tableName=" + tableName +
                                    ", fieldName=" + fieldName +
                                    ", fieldDescribe=" + fieldDescribe +
                                    ", isQuery=" + isQuery +
                                    ", isSort=" + isSort +
                                    ", dataSources=" + dataSources +
                                    ", isDeleted=" + isDeleted +
                                    ", isValid=" + isValid +
                                    ", createdAt=" + createdAt +
                                    ", updatedAt=" + updatedAt +
                    "}";
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"id");
    }

}
