package com.yonyou.dmscus.customer.entity.dto.accidentClues;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value="AccidentCluesDTO",description="事故线索跟进")
@Data
public class AccidentCluesFollowDTO extends BaseDTO implements Serializable {

  @ApiModelProperty(value = "系统ID",name="appId")
  private String appId;

  @ApiModelProperty(value = "所有者代码",name="ownerCode")
  private String ownerCode;

  @ApiModelProperty(value = "所有者的父组织代码",name="ownerParCode")
  private String ownerParCode;

  @ApiModelProperty(value = "组织ID",name="orgId")
  private Integer orgId;

  @ApiModelProperty(value = "主键ID",name="followId")
  private Integer followId;

  @ApiModelProperty(value = "经销商代码",name="dealerCode")
  private String dealerCode;

  @ApiModelProperty(value = "tt_accident_clues",name="acId")
  private Integer acId;

  @ApiModelProperty(value = "是否本店承保",name="isInsured")
  private Integer isInsured;

  @ApiModelProperty(value = "单方/双方事故",name="doubleAccident")
  private Integer doubleAccident;

  @ApiModelProperty(value = "事故责任划分",name="dutyDivision")
  private Integer dutyDivision;

  @ApiModelProperty(value = "是否现场报案",name="isReport")
  private Integer isReport;

  @ApiModelProperty(value = "是否拖车服务",name="isTrailer")
  private Integer isTrailer;

  @ApiModelProperty(value = "进厂工单号",name="roNo")
  private String roNo;

  @ApiModelProperty(value = "跟进方式",name="followType")
  private Integer followType;

  @ApiModelProperty(value = "跟进内容/客户反馈",name="followText")
  private String followText;

  @ApiModelProperty(value = "跟进状态",name="followStatus")
  private Integer followStatus;

  @ApiModelProperty(value = "跟进失败原因",name="followFailWhy")
  private Integer followFailWhy;

  @ApiModelProperty(value = "下次跟进日期",name="nextFollowDate")
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  private Date nextFollowDate;

  @ApiModelProperty(value = "是否预约进店",name="isAppointment")
  private Integer isAppointment;

  @ApiModelProperty(value = "客户预约进店",name="customerAppointment")
  private Integer customerAppointment;

  @ApiModelProperty(value = "客户预约进店时间",name="appointmentIntoDate")
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
  @JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone="GMT+8")
  private Date appointmentIntoDate;

  @ApiModelProperty(value = "数据来源",name="dataSources")
  private Integer dataSources;

  @ApiModelProperty(value = "是否删除，1：删除，0：未删除",name="isDeleted")
  private Integer isDeleted;

  @ApiModelProperty(value = "是否有效",name="isValid")
  private Integer isValid;

  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  @ApiModelProperty(value = "创建时间",name="createdAt")
  private Date createdAt;

  @ApiModelProperty(value = "创建人",name="createdBy")
  private String createdBy;

  @ApiModelProperty(value = "更新时间",name="updatedAt")
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  private Date updatedAt;

  @ApiModelProperty(value = "更新人",name="updatedBy")
  private String updatedBy;

  @ApiModelProperty(value = "版本号（乐观锁）",name="recordVersion")
  private Integer recordVersion;

  @ApiModelProperty(value = "工单状态",name="roStatus")
  private Integer roStatus;

  @ApiModelProperty(value = "进厂时间",name="intoDealerDate")
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  private Date intoDealerDate;

  @ApiModelProperty(value = "进厂经销商",name="roStatus")
  private String intoDealerCode;

  private Integer followPeople;

  private String followPeopleName;

  @ApiModelProperty(value = "预约单号",name="bookingOrderNo")
  private String bookingOrderNo;

  @ApiModelProperty(value = "是否有人受伤",name="isBruise")
  private Integer isBruise;

  @ApiModelProperty(value = "联系方式-手机号",name="contactsInformation")
  private String contactsInformation;

  @Override
  public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
    return super.transDtoToPo(poClass);
  }

  @Override
  public String toString() {
    return ToStringBuilder.reflectionToString(this);
  }
}
