package com.yonyou.dmscus.customer.entity.po.vocfunctional;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @title: VocWarningDataLog
 * @projectName dmscus.customer
 * @date 2022/11/118:25
 */
@Data
@ApiModel(value = "VocWarningDataLogPo", description = "BI推送VOC车辆每日保养灯流水表")
@TableName("tt_voc_warning_data_log")
public class VocWarningDataLogPo  extends  BasePo{
    //通知时间
    @ApiModelProperty(value = "报告时间",notes = "报告时间",required = true,name = "dt",example = "20221026",dataType = "String")
    @TableField("dt")
    private String dt;
    
    @ApiModelProperty(value = "车架号",notes = "车架号",required = true,name = "dt",example = "LVSHGFAR4FF087294",dataType = "String")
    @TableField("vin")
    private String vin;

    @ApiModelProperty(value = "监控状态名称",notes = "监控状态名称",required = true,name = "status_name",example = "ServiceWarningStatus",dataType = "String")
    @TableField("status_name")
    private String statusName;

    @ApiModelProperty(value = "报警状态对应的状态值",notes = "报警状态对应的状态值：Normal,TimeExceeded,AlmostTimeForService,TimeForService",required = true,name = "status_value",example = "AlmostTimeForService",dataType = "String")
    @TableField("status_value")
    private String statusValue;


    @ApiModelProperty(value = "车型",notes = "车型",required = true,name = "model",example = "S80",dataType = "String")
    @TableField("model")
    private String model;


    @ApiModelProperty(value = "报警名称:",notes = "报警名称:消失，改变，新增",required = true,name = "status_change",example = "新增",dataType = "String")
    @TableField("status_change")
    private String statusChange;

    @ApiModelProperty(value = "总里程m:",notes = "总里程m",required = true,name = "mileage_m",example = "137838173",dataType = "String")
    @TableField("mileage_m")
    private String mileagem;

    @ApiModelProperty(value = "总里程km:",notes = "总里程km",required = true,name = "mileage_km",example = "137838.173",dataType = "String")
    @TableField("mileage_km")
    private String mileageKm;

    @ApiModelProperty(value = "年款",notes = "年款",required = true,name = "model_year",example = "2015",dataType = "String")
    @TableField("model_year")
    private String modelYear;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        VocWarningDataLogPo that = (VocWarningDataLogPo) o;
        return Objects.equals(dt, that.dt) &&
                Objects.equals(vin, that.vin) &&
                Objects.equals(statusName, that.statusName) &&
                Objects.equals(statusValue, that.statusValue) &&
                Objects.equals(statusChange, that.statusChange) &&
                Objects.equals(mileagem, that.mileagem) &&
                Objects.equals(mileageKm, that.mileageKm) &&
                Objects.equals(modelYear, that.modelYear) &&
                Objects.equals(model, that.model);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), dt, vin, statusName, statusValue, statusChange, mileagem, mileageKm, modelYear, model);
    }
}
