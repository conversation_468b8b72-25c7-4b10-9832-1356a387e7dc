package com.yonyou.dmscus.customer.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentClueContact;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "线索信息")
public class AccidentClueVo {


    //保险公司	太平洋保险

    @ApiModelProperty(value = "线索来源",name="cluesResource")
    private Integer cluesResource;

    @ApiModelProperty(value = "车牌号",name="license")
    private String license;

    @ApiModelProperty(value = "事故地点",name="accidentAddress")
    private String accidentAddress;

    @ApiModelProperty(value = "报案时间",name="reportDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportDate;

    @ApiModelProperty(value = "联系人",name="contacts")
    private String contacts;

    @ApiModelProperty(value = "手机号",name="contactsPhone")
    private String contactsPhone;

    @ApiModelProperty(value = "保险公司名称",name="insuranceCompanyName")
    private String  insuranceCompanyName;

    @ApiModelProperty(value = "保险公司code",name="insuranceCompanyId")
    private String  insuranceCompanyId;

    @ApiModelProperty(value = "联系人信息",name="contactList")
    private List<AccidentClueContact> contactList;

}
