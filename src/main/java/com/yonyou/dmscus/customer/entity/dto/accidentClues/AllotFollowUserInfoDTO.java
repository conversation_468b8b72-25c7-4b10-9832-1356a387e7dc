package com.yonyou.dmscus.customer.entity.dto.accidentClues;

import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @Date 2023/11/7 13:52
 * @Version 1.0
 */
@ApiModel(value = "线索分配人信息")
public class AllotFollowUserInfoDTO {

    private Integer userId;
    private String employeeName;

    public AllotFollowUserInfoDTO() {
    }

    public AllotFollowUserInfoDTO(Integer userId, String employeeName) {
        this.userId = userId;
        this.employeeName = employeeName;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    @Override
    public String toString() {
        return "AllotFollowUserInfoDTO{" +
                "userId=" + userId +
                ", employeeName='" + employeeName + '\'' +
                '}';
    }
}
