package com.yonyou.dmscus.customer.entity.dto.invitationCreate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "自建线索导入查询入参（含计划编号、导入时间、创建人及分页信息）")
public class InviteTaskQueryParamsDto {

    /**
     * 计划编号（必填）
     */
    @ApiModelProperty(value = "计划编号", example = "PLAN_20250903_001", required = true)
    private String planNo;

    /**
     * 当前页（默认值：1）
     */
    @ApiModelProperty(value = "当前页", example = "1", notes = "默认值为1")
    private Integer currentPage = 1;

    /**
     * 每页显示条数（默认值：20）
     */
    @ApiModelProperty(value = "每页显示条数", example = "20", notes = "默认值为20")
    private Integer pageSize = 20;
}
