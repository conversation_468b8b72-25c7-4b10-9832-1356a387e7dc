package com.yonyou.dmscus.customer.entity.dto.clue.dictionary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * description
 * <AUTHOR>
 * @date 2023/9/4 16:39
 */
@Data
@ApiModel(description = "id")
public class DictionaryIdDTO {
    /**
     * 最小id
     */
    @ApiModelProperty(value = "最小id")
    private Long minId;
    /**
     * 最大id
     */
    @ApiModelProperty(value = "最大id")
    private Long maxId;

}
