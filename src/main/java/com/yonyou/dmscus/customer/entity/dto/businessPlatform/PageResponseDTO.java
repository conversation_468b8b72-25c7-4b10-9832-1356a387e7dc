package com.yonyou.dmscus.customer.entity.dto.businessPlatform;

import com.yonyou.dmscus.customer.middleInterface.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "分页返回结果")
public class PageResponseDTO<T>{

    private static final long serialVersionUID = Constant.SERIAL_VERSION_UID;

    @ApiModelProperty(value = "总数量")
    private Long total;
    @ApiModelProperty(value = "当前页")
    private Long current;
    @ApiModelProperty(value = "当前页")
    private Long pages;
    @ApiModelProperty(value = "每页数量")
    private Long size;

    @ApiModelProperty(value = "总页数")
    private T records;
}