package com.yonyou.dmscus.customer.entity.dto.talkskill;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 话术关键词库
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-27
 */
@Data
public class TalkskillKeywordLibDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    private Long keyId;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 次数
     */
    private Integer num;



    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除，1：删除，0：未删除
     */
    private Integer isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    private Date createdAt;
    /**
     * 更新时间
     */
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    private LocalDateTime updatedAt;

    public TalkskillKeywordLibDTO() {
        super();
    }



    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "keyId");
    }

}
