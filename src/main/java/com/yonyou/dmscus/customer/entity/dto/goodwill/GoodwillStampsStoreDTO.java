package com.yonyou.dmscus.customer.entity.dto.goodwill;

import cn.hutool.core.date.DateUtil;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscus.customer.annotation.ExcelColumnDefine;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class GoodwillStampsStoreDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 账号类型
     */
    private Integer dataType;
    /**
     * 通知开票id
     */
    private Long noticeId;

    /**
     * 亲善单id
     */
    @ApiModelProperty("亲善单id")
    // @ExcelColumnDefine(value = 1, name = "亲善单id")
    private Long goodwillApplyId;

    /**
     * 亲善单号
     */
    @ApiModelProperty("亲善单号")
    @ExcelColumnDefine(value = 1, name = "亲善单id")
    private String applyNo;

    /**
     * 大区
     */
    @ApiModelProperty("区域")
    private String areaManage;

    /**
     * 小区
     */
    @ApiModelProperty("小区")
    private String smallArea;

    /**
     * 经销商名称
     */
    @ApiModelProperty("经销商")
    @ExcelColumnDefine(value = 2, name = "经销商")
    private String dealerName;

    /**
     * 车主姓名
     */
    @ApiModelProperty("车主姓名")
    @ExcelColumnDefine(value = 3, name = "车主姓名")
    private String veOwnerName;

    /**
     * 车牌号
     */
    @ApiModelProperty("车牌号")
    @ExcelColumnDefine(value = 4, name = "车牌号")
    private String license;

    /**
     * VIN
     */
    @ApiModelProperty("VIN")
    @ExcelColumnDefine(value = 5, name = "VIN")
    private String vin;

    /**
     * 消费时间
     */
    @ApiModelProperty("消费时间")
    // @ExcelColumnDefine(value = 6, name = "消费时间")
    private Date consumeDate;

    @ApiModelProperty("消费时间，字符串格式yyyy-MM-dd 例如：2023-09-20")
    @ExcelColumnDefine(value = 6, name = "消费时间")
    private String consumeDateStr;

    /**
     * 维修工单号
     */
    @ApiModelProperty("维修工单号")
    @ExcelColumnDefine(value = 7, name = "维修工单号")
    private String repairNumber;

    /**
     * 消费金额
     */
    @ApiModelProperty("消费金额")
    @ExcelColumnDefine(value = 8, name = "消费金额")
    private BigDecimal consumeAmount;

    /**
     * 消费成本金额
     */
    @ApiModelProperty("已消费金额成本")
    private BigDecimal costConsumeAmount;

    /**
     * 开票通知时间
     */
    @ApiModelProperty("通知开票时间")
    // @ExcelColumnDefine(value = 9, name = "通知开票时间")
    private Date noticeInvoiceDate;

    @ApiModelProperty("通知开票时间，字符串格式yyyy-MM-dd 例如：2023-09-20")
    @ExcelColumnDefine(value = 9, name = "通知开票时间")
    private String noticeInvoiceDateStr;

    /**
     * 发票号
     */
    @ApiModelProperty("发票号")
    @ExcelColumnDefine(value = 10, name = "发票号")
    private String invoiceNumber;

    /**
     * 开票时间
     */
    @ApiModelProperty("开票时间")
    // @ExcelColumnDefine(value = 11, name = "开票时间")
    private Date invoiceDate;

    @ApiModelProperty("开票时间，字符串格式yyyy-MM-dd 例如：2023-09-20")
    @ExcelColumnDefine(value = 11, name = "开票时间")
    private String invoiceDateStr;

    /**
     * 发票收到时间
     */
    @ApiModelProperty("发票收到时间")
    // @ExcelColumnDefine(value = 12, name = "发票收到时间")
    private Date receiveDate;

    @ApiModelProperty("发票收到时间，字符串格式yyyy-MM-dd 例如：2023-09-20")
    @ExcelColumnDefine(value = 12, name = "发票收到时间")
    private String receiveDateStr;

    /**
     * 快递公司
     */
    @ApiModelProperty("快递公司")
    @ExcelColumnDefine(value = 13, name = "快递公司")
    private String courierCompany;

    /**
     * 快递单号
     */
    @ApiModelProperty("快递单号")
    @ExcelColumnDefine(value = 14, name = "快递单号")
    private String courierNumber;

    /**
     * 快递时间
     */
    @ApiModelProperty("快递时间")
    // @ExcelColumnDefine(value = 15, name = "快递时间")
    private Date deliveryDate;

    @ApiModelProperty("快递时间，字符串格式yyyy-MM-dd 例如：2023-09-20")
    @ExcelColumnDefine(value = 15, name = "快递时间")
    private String deliveryDateStr;

    /**
     * 开票id
     */
    @ApiModelProperty("开票id")
    @ExcelColumnDefine(value = 16, name = "开票id")
    private String invoiceId;


    /**
     * 卡券id
     */
    private Long couponId;

    /**
     * 核销id
     */
    private Long consumeId;

    /**
     * 消费ID
     */
    private String[] consume;
    /**
     * 消费ID
     */
    private String consumes;

    /**
     * 经销商
     */
    private String dealerCode;

    /**
     * 通知金额
     */
    private BigDecimal noticeAmount;

    /**
     * 资产类型
     */
    private String resourceType;

    /**
     * 通知充值日期
     */
    private Date noticeDate;

    /**
     * 充值日期
     */
    private Date rechargeDate;

    /**
     * 开票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 亲善经销商
     */
    private Date goodwillDealer;

    /**
     * 已通知开票金额
     */
    private BigDecimal invoicedAmount;

    /**
     *
     * 充值金额
     */
    private BigDecimal rechargeAmount;

    /**
     * 已消费金额
     */
    private BigDecimal usedAmount;

    /**
     * 剩余金额
     */
    private BigDecimal leftAmount;

    /**
     * 代金券已充值金额
     */
    private BigDecimal voucherRechargeAmount;

    /**
     * 代金券未充值金额
     */
    private BigDecimal voucherNotAmount;
    /**
     * 代金券已开票金额
     */
    private BigDecimal voucherInvoiceAmount;

    /**
     * 代金券未开票金额
     */
    private BigDecimal voucherNotInvoiceAmount;

    /**
     * 通知充值日期
     */

    private Date noticeStartdAt;

    /**
     * 通知充值日期
     */

    private Date noticeEndAt;

    /**
     * 充值时间
     */

    private Date rechargeStartdAt;
    /**
     * 充值时间
     */

    private Date rechargeEndAt;

    /**
     * 消费开始时间
     */

    private Date consumeStartdAt;
    /**
     * 消费结束时间
     */

    private Date consumeEndAt;

    /**
     * 消费经销商
     */
    private String consumeDealer;


    /**
     * 开票开始时间
     */

    private Date invoiceStartdAt;
    /**
     * 开票结束时间
     */

    private Date invoiceEndAt;

    /**
     * 是否已充值
     */

    private Integer isChargerd;

    /**
     * 集团
     */

    private String group;

    /**
     * 开票抬头
     */

    private String invoiceTitle;

    /**
     * 通知开票金额
     */

    private BigDecimal noticeInvoicePrice;

    /**
     * 代金券充值金额
     */
    private BigDecimal voucherCouponFaceRechargePrice;

    /**
     * 费率
     */
    private BigDecimal costRate;

    /**
     * 小区经理名称
     */
    private String auditName;

    /**
     * 小区经理名称
     */
    private List<String> auditName1;

    public String getConsumeDateStr() {

        // 消费时间
        return null == consumeDate ? "" : DateUtil.format(consumeDate, "yyyy-MM-dd");
    }

    public void setConsumeDateStr(String consumeDateStr) {
        this.consumeDateStr = consumeDateStr;
    }

    public String getNoticeInvoiceDateStr() {

        return null == noticeInvoiceDate ? "" : DateUtil.format(noticeInvoiceDate, "yyyy-MM-dd");
    }

    public void setNoticeInvoiceDateStr(String noticeInvoiceDateStr) {
        this.noticeInvoiceDateStr = noticeInvoiceDateStr;
    }

    public String getInvoiceDateStr() {

        return null == invoiceDate ? "" : DateUtil.format(invoiceDate, "yyyy-MM-dd");
    }

    public void setInvoiceDateStr(String invoiceDateStr) {
        this.invoiceDateStr = invoiceDateStr;
    }

    public String getReceiveDateStr() {

        return null == receiveDate ? "" : DateUtil.format(receiveDate, "yyyy-MM-dd");
    }

    public void setReceiveDateStr(String receiveDateStr) {
        this.receiveDateStr = receiveDateStr;
    }

    public String getDeliveryDateStr() {

        return null == deliveryDate ? "" : DateUtil.format(deliveryDate, "yyyy-MM-dd");
    }

    public void setDeliveryDateStr(String deliveryDateStr) {
        this.deliveryDateStr = deliveryDateStr;
    }


    public String getConsumes() {
        return consumes;
    }

    public void setConsumes(String consumes) {
        this.consumes = consumes;
    }

    public BigDecimal getInvoicedAmount() {
        return invoicedAmount;
    }

    public void setInvoicedAmount(BigDecimal invoicedAmount) {
        this.invoicedAmount = invoicedAmount;
    }

    public String getCourierCompany() {
        return courierCompany;
    }

    public void setCourierCompany(String courierCompany) {
        this.courierCompany = courierCompany;
    }

    public String getCourierNumber() {
        return courierNumber;
    }

    public void setCourierNumber(String courierNumber) {
        this.courierNumber = courierNumber;
    }

    public String getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(String invoiceId) {
        this.invoiceId = invoiceId;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public Date getNoticeInvoiceDate() {
        return noticeInvoiceDate;
    }

    public void setNoticeInvoiceDate(Date noticeInvoiceDate) {
        this.noticeInvoiceDate = noticeInvoiceDate;
    }

    public Date getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(Date invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public Date getGoodwillDealer() {
        return goodwillDealer;
    }

    public void setGoodwillDealer(Date goodwillDealer) {
        this.goodwillDealer = goodwillDealer;
    }

    public Date getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Date receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getConsumeDealer() {
        return consumeDealer;
    }

    public void setConsumeDealer(String consumeDealer) {
        this.consumeDealer = consumeDealer;
    }

    public String getRepairNumber() {
        return repairNumber;
    }

    public void setRepairNumber(String repairNumber) {
        this.repairNumber = repairNumber;
    }

    public BigDecimal getConsumeAmount() {
        return consumeAmount;
    }

    public void setConsumeAmount(BigDecimal consumeAmount) {
        this.consumeAmount = consumeAmount;
    }

    public Date getConsumeDate() {
        return consumeDate;
    }

    public void setConsumeDate(Date consumeDate) {
        this.consumeDate = consumeDate;
    }

    public BigDecimal getCostConsumeAmount() {
        return costConsumeAmount;
    }

    public void setCostConsumeAmount(BigDecimal costConsumeAmount) {
        this.costConsumeAmount = costConsumeAmount;
    }

    public Date getConsumeStartdAt() {
        return consumeStartdAt;
    }

    public void setConsumeStartdAt(Date consumeStartdAt) {
        this.consumeStartdAt = consumeStartdAt;
    }

    public Date getConsumeEndAt() {
        return consumeEndAt;
    }

    public void setConsumeEndAt(Date consumeEndAt) {
        this.consumeEndAt = consumeEndAt;
    }

    public Date getInvoiceStartdAt() {
        return invoiceStartdAt;
    }

    public void setInvoiceStartdAt(Date invoiceStartdAt) {
        this.invoiceStartdAt = invoiceStartdAt;
    }

    public Date getInvoiceEndAt() {
        return invoiceEndAt;
    }

    public void setInvoiceEndAt(Date invoiceEndAt) {
        this.invoiceEndAt = invoiceEndAt;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }



    public String getAuditName() {
        return auditName;
    }

    public void setAuditName(String auditName) {
        this.auditName = auditName;
    }

    public List<String> getAuditName1() {
        return auditName1;
    }

    public void setAuditName1(List<String> auditName1) {
        this.auditName1 = auditName1;
    }

    public BigDecimal getCostRate() {
        return costRate;
    }

    public void setCostRate(BigDecimal costRate) {
        this.costRate = costRate;
    }

    public BigDecimal getVoucherCouponFaceRechargePrice() {
        return voucherCouponFaceRechargePrice;
    }

    public void setVoucherCouponFaceRechargePrice(BigDecimal voucherCouponFaceRechargePrice) {
        this.voucherCouponFaceRechargePrice = voucherCouponFaceRechargePrice;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public BigDecimal getNoticeInvoicePrice() {
        return noticeInvoicePrice;
    }

    public void setNoticeInvoicePrice(BigDecimal noticeInvoicePrice) {
        this.noticeInvoicePrice = noticeInvoicePrice;
    }

    public Integer getIsChargerd() {
        return isChargerd;
    }

    public void setIsChargerd(Integer isChargerd) {
        this.isChargerd = isChargerd;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getAreaManage() {
        return areaManage;
    }

    public void setAreaManage(String areaManage) {
        this.areaManage = areaManage;
    }

    public String getSmallArea() {
        return smallArea;
    }

    public void setSmallArea(String smallArea) {
        this.smallArea = smallArea;
    }

    public Long getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(Long noticeId) {
        this.noticeId = noticeId;
    }

    public Long getGoodwillApplyId() {
        return goodwillApplyId;
    }

    public void setGoodwillApplyId(Long goodwillApplyId) {
        this.goodwillApplyId = goodwillApplyId;
    }

    public String getDealerCode() {
        return dealerCode;
    }

    public void setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
    }

    public String getVeOwnerName() {
        return veOwnerName;
    }

    public void setVeOwnerName(String veOwnerName) {
        this.veOwnerName = veOwnerName;
    }

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public BigDecimal getNoticeAmount() {
        return noticeAmount;
    }

    public void setNoticeAmount(BigDecimal noticeAmount) {
        this.noticeAmount = noticeAmount;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public Date getNoticeDate() {
        return noticeDate;
    }

    public void setNoticeDate(Date noticeDate) {
        this.noticeDate = noticeDate;
    }

    public Date getRechargeDate() {
        return rechargeDate;
    }

    public void setRechargeDate(Date rechargeDate) {
        this.rechargeDate = rechargeDate;
    }

    public BigDecimal getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(BigDecimal rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public BigDecimal getUsedAmount() {
        return usedAmount;
    }

    public void setUsedAmount(BigDecimal usedAmount) {
        this.usedAmount = usedAmount;
    }

    public BigDecimal getLeftAmount() {
        return leftAmount;
    }

    public void setLeftAmount(BigDecimal leftAmount) {
        this.leftAmount = leftAmount;
    }

    public BigDecimal getVoucherRechargeAmount() {
        return voucherRechargeAmount;
    }

    public void setVoucherRechargeAmount(BigDecimal voucherRechargeAmount) {
        this.voucherRechargeAmount = voucherRechargeAmount;
    }

    public BigDecimal getVoucherNotAmount() {
        return voucherNotAmount;
    }

    public void setVoucherNotAmount(BigDecimal voucherNotAmount) {
        this.voucherNotAmount = voucherNotAmount;
    }

    public Date getNoticeStartdAt() {
        return noticeStartdAt;
    }

    public void setNoticeStartdAt(Date noticeStartdAt) {
        this.noticeStartdAt = noticeStartdAt;
    }

    public Date getNoticeEndAt() {
        return noticeEndAt;
    }

    public void setNoticeEndAt(Date noticeEndAt) {
        this.noticeEndAt = noticeEndAt;
    }

    public Date getRechargeStartdAt() {
        return rechargeStartdAt;
    }

    public void setRechargeStartdAt(Date rechargeStartdAt) {
        this.rechargeStartdAt = rechargeStartdAt;
    }

    public Date getRechargeEndAt() {
        return rechargeEndAt;
    }

    public void setRechargeEndAt(Date rechargeEndAt) {
        this.rechargeEndAt = rechargeEndAt;
    }

    public String getDealerName() {
        return dealerName;
    }

    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public BigDecimal getVoucherInvoiceAmount() {
        return voucherInvoiceAmount;
    }

    public void setVoucherInvoiceAmount(BigDecimal voucherInvoiceAmount) {
        this.voucherInvoiceAmount = voucherInvoiceAmount;
    }

    public BigDecimal getVoucherNotInvoiceAmount() {
        return voucherNotInvoiceAmount;
    }

    public void setVoucherNotInvoiceAmount(BigDecimal voucherNotInvoiceAmount) {
        this.voucherNotInvoiceAmount = voucherNotInvoiceAmount;
    }

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public Long getCouponId() {
        return couponId;
    }

    public void setCouponId(Long couponId) {
        this.couponId = couponId;
    }

    public Long getConsumeId() {
        return consumeId;
    }

    public void setConsumeId(Long consumeId) {
        this.consumeId = consumeId;
    }

    public String[] getConsume() {
        return consume;
    }

    public void setConsume(String[] consume) {
        this.consume = consume;
    }

    public GoodwillStampsStoreDTO() {
        super();
    }

    /**
     * 将DTO 转换为PO //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass
     *            需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po
     *            需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }


}
