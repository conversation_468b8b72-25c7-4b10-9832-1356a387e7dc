package com.yonyou.dmscus.customer.entity.vo;


import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscus.customer.entity.dto.busSetting.SetMainFileVO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 车辆邀约记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
@ApiModel("车辆邀约记录表")
@Data
public class InviteVehicleRecordVo extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    @ApiModelProperty(value = "系统ID",name = "appId")
    private String appId;

    /**
     * 所有者代码
     */
    @ApiModelProperty(value = "所有者代码",name = "ownerCode")
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    @ApiModelProperty(value = "所有者的父组织代码",name = "ownerParCode")
    private String ownerParCode;

    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID",name = "orgId")
    private Integer orgId;

    /**
     * 邀约ID
     */
    @ApiModelProperty(value = "邀约ID",name = "id")
    private Long id;

    /**
     * 邀约父类ID
     */
    @ApiModelProperty(value = "邀约父类ID",name = "parentId")
    private Long parentId;

    /**
     * 是否主要线索:1主要线索、0附属线索
     */
    @ApiModelProperty(value = "是否主要线索:1主要线索、0附属线索",name = "isMain")
    private Integer isMain;

    /**
     * 邀约来源类型：1VCDC下发邀约、2经销商自建邀约、3VOC事故邀约
     */
    @ApiModelProperty(value = "邀约来源类型：1VCDC下发邀约、2经销商自建邀约、3VOC事故邀约",name = "sourceType")
    private Integer sourceType;

    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号",name = "vin")
    private String vin;

    /**
     * 车型
     */
    @ApiModelProperty(value = "车型",name = "model")
    private String model;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号",name = "licensePlateNum")
    private String licensePlateNum;

    /**
     * 车主姓名
     */
    @ApiModelProperty(value = "车主姓名",name = "name")
    private String name;

    /**
     * 车主电话
     */
    @ApiModelProperty(value = "车主电话",name = "tel")
    private String tel;

    /**
     * 车主性别
     */
    @ApiModelProperty(value = "车主性别",name = "tel")
    private String sex;

    /**
     * 邀约类型
     */
    @ApiModelProperty(value = "邀约类型",name = "inviteType")
    private Integer inviteType;

    /**
     * 建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "建议进厂日期",name = "adviseInDate")
    private Date adviseInDate;

    /**
     * 新建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "新建议进厂日期",name = "newAdviseInDate")
    private Date newAdviseInDate;

    /**
     * 最新建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最新建议进厂日期",name = "newestAdviseInDate")
    private Date newestAdviseInDate;

    /**
     * 计划跟进日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "计划跟进日期",name = "planFollowDate")
    private Date planFollowDate;

    /**
     * 实际跟进日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "实际跟进日期",name = "actualFollowDate")
    private Date actualFollowDate;

    /**
     * 计划提醒日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "计划提醒日期",name = "planRemindDate")
    private Date planRemindDate;

    /**
     * 实际提醒日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "实际提醒日期",name = "actualRemindDate")
    private Date actualRemindDate;


    /**
     * 首次跟进时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "首次跟进时间",name = "firstFollowDate")
    private Date firstFollowDate;


    /**
     * 线索完成时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "线索完成时间",name = "orderFinishDate")
    private Date orderFinishDate;

    /**
     * 跟进服务顾问ID
     */
    @ApiModelProperty(value = "跟进服务顾问ID",name = "saId")
    private String saId;

    /**
     * 跟进服务顾问姓名
     */
    @ApiModelProperty(value = "跟进服务顾问姓名",name = "saName")
    private String saName;

    /**
     * 上次跟进服务顾问ID
     */
    @ApiModelProperty(value = "上次跟进服务顾问ID",name = "lastSaId")
    private String lastSaId;

    /**
     * 上次跟进服务顾问姓名
     */
    @ApiModelProperty(value = "上次跟进服务顾问姓名",name = "lastSaName")
    private String lastSaName;

    /**
     * 跟进状态
     */
    @ApiModelProperty(value = "跟进状态",name = "followStatus")
    private Integer followStatus;

    /**
     * 是否预约单：1 是，0 否
     */
    @ApiModelProperty(value = "是否预约单：1 是，0 否",name = "isBook")
    private Integer isBook;

    /**
     * 预约单号
     */
    @ApiModelProperty(value = "预约单号",name = "bookNo")
    private String bookNo;

    /**
     * 线索完成状态
     */
    @ApiModelProperty(value = "线索完成状态",name = "orderStatus")
    private Integer orderStatus;

    /**
     * 数据来源
     */
    @ApiModelProperty(value = "数据来源",name = "dataSources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除",name = "isDeleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效",name = "isValid")
    private Integer isValid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 线索类型 查询条件
     */
    @ApiModelProperty(value = "线索类型",name = "recordTypeParam", example = "[87891001,87891002,87891003]")
    private List<Integer> recordTypeParam;

    /**
     * 工单号
     */
    private  String roNo;


    /**
     * 维修类型
     */
    private  String repairTypeCode;


    /**
     * 出厂里程
     */
    private  Double outMileage;


    /**
     * 开单时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private  Date roCreateDate;



    /**
     * 完成经销商
     */
    private  String finishDealerCode;


    /**
     * 经销商代码
     */
    private String dealerCode;

    /**
     * 上次进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date lastInDate;


    /**
     * 邀约类型 查询条件
     */
    private List<Integer> inviteTypeParam;


    /**
     * 计划跟进日期开始 查询条件
     */
    private String planFollowDateStart;

    /**
     * 计划跟进日期结束 查询条件
     */
    private String planFollowDateEnd;

    /**
     * 实际跟进日期开始 查询条件
     */
    private String actualFollowDateStart;

    /**
     * 实际跟进日期结束 查询条件
     */
    private String actualFollowDateEnd;

    /**
     * 建议进厂日期开始 查询条件
     */
    private String adviseInDateStart;

    /**
     * 建议进厂日期结束 查询条件
     */
    private String adviseInDateEnd;


    /**
     * 邀约状态 查询条件
     */
    private List<Integer> followStatusParam;


    /**
     * 邀约状态 查询条件
     */
    private List<Integer> orderStatusParam;


    /**
     * 离职用户 id 查询条件
     */
    private List<Integer> leaveIds;


    /**
     * 邀约创建日期开始 查询条件
     */
    private String createdAtStart;

    /**
     * 邀约创建日期结束 查询条件
     */
    private String createdAtEnd;


    /**
     * 是否逾期未跟进：1 是，0 否 查询条件
     */
    private Integer overdue;


    /**
     * 大区 查询条件
     */
    private String largeAreaId;

    /**
     * 小区 查询条件
     */
    private String areaId;


    /**
     * 是否查询本人
     */
    private Integer isself;


    /**
     * 是否查询未分配
     */
    private  Boolean isNoDistribute;

    /**
     * 是否查询待分配
     */
    private  Boolean isWaitDistribute;


    /**
     * 易损件规则id
     */
    private Long partItemRuleId;

    /**
     * 易损件code
     */
    private String itemCode;

    /**
     * 易损件名称
     */
    private String itemName;

    /**
     * 易损件类型
     */
    private Integer itemType;


    /**
     * 易损件上次更换时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date lastChangeDate;


    /**
     * 是否voc车辆
     */
    private  Integer isVoc;


    /**
     * 上次保养日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private  Date lastMaintenanceDate;


    /**
     * 建议入厂日期间隔
     */
    private Integer dateInterval;


    /**
     * 子线索
     */
    private String sonInviteType;


    /**
     * 话术
     */
    private List<TalkskillDTO> talkskill;

    private List<InviteVehicleRecordDetailDTO> recordDetailList;


    /**
     * 推荐保养套餐
     */
    private List<SetMainFileVO> maintainList;

    /**
     * 客户是否投保成功
     */
    private Integer isInsureSuccess;

    
    /**
     * 最新得分
     */
    private Integer score;
    
    /**
     * 录音播放地址
     */
    private String voiceUrl;


    /**
     * 最近通话时长
     */
    private Integer callLength;


    /**
     *    通话时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date callTime;



    /**
     * 日平均行驶里程
     */
    private BigDecimal dailyAverageMileage;
    
    
    /**
     *    通话详情Id
     */
    private Long callDetailId;

    /**
     *建议入厂里程
     */
    private Integer adviseInMileage;


    /**
     *任务基准日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date inviteTime;

    /**
     * 延保提示信息
     */
    private String extensionInsurance;

    /**
     *投保单状态
     */
    private Integer insuranceStatus;

    /**
     *是否联保
     */
    private Integer isJointGuarantee;


    /**
     * 客户唯一id
     */
    private Long oneId;

    /**
     *    超时关闭间隔
     */
    private Integer closeInterval;


    /**
     * 最新失败原因
     */
    private Integer loseReason;


    /**
     * 最新跟进内容
     */
    private String content;




    /**
     * 上次保养里程
     */
    private BigDecimal lastMaintenanceMileage;



    /**
     * 上次保养经销商
     */
    private String lastMaintenanceDealer;

    /**
     * 二次跟进月份
     */
    private String monthTwice;

    /**
     * 是否二次跟进
     */
    private String isAi;

    /**
     * 是否二次跟进
     */
    private Integer recordNum;


    /**
     * 是否二次跟进
     */
    private Date aiAt;


    /**
     * 是否二次跟进
     */
    private Date orderAt;



    /**
     * 是否二次跟进
     */
    private Date recordAt;


    /**
     * 线索类型
     */
    @ApiModelProperty(value = "线索类型:0 VOC线索,1普通线索 ",name = "线索类型")
    private Integer recordType;

    /**
     *线索类型:0 ：非VOC类型,1 ：18个月未保养，2：非4S店保养流失,3:流失客户保养灯亮
     */
    private Integer lossType;
    /**
     * 流失预警类型
     */
    @ApiModelProperty(value = "流失预警类型")
    private Integer lossWarningType;

    @ApiModelProperty(value = "券码",name = "couponCode")
    private String couponCode;

    @ApiModelProperty(value = "卡劵名称",name = "couponName")
    private String couponName;

    /**
     * CDP提供的日均里程
     */
    @ApiModelProperty(value = "日均里程")
    private String dailyMile;

    /**
     * 验证状态
     */
    @TableField(exist = false)
    private String verifyStatus;

    @ApiModelProperty("是否bev")
    private Integer bevFlag;
    @ApiModelProperty("动力类型")
    private String powerType;

    /**
     * 返厂意向等级
     */
    @TableField(exist = false)
    private Integer returnIntentionLevel;

    /**
     * 是否已分配
     */
    @TableField(exist = false)
    private boolean allocation;

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param poClass 需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     *
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
