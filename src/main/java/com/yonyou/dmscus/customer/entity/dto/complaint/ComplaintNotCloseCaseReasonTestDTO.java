package com.yonyou.dmscus.customer.entity.dto.complaint;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.function.utils.jsonSerializer.date.JsonDateDeserializer;
import com.yonyou.dmscloud.function.utils.jsonSerializer.date.JsonDateSerializer;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 5日未结案原因
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */

public class ComplaintNotCloseCaseReasonTestDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 投诉信息表主键ID
     */
    private Long complaintInfoId;

    /**
     * 跟进对象 区域经理、经销商、CCM、客服中心
     */
    private String object;

    /**
     * 填写时间
     */
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date followTime;

    /**
     * 填写人
     */
    private String follower;
    /**
     * 填写人
     */
    private String followerName;

    /**
     * 未结案原因（大类）
     */
    private Integer bigClass;

    /**
     * 未结案原因（大类）名称
     */
    private String bigClassName;

    /**
     * 未结案原因（小类）多选用逗号分隔
     */
    private String smallClass;

    /**
     * 未结案原因（小类）名称 多选用逗号分隔
     */
    private String smallClassName;

    /**
     * 其他
     */
    private String other;

    /**
     * 时长（天）
     */
    private Integer duration;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;
    private String smallClassNameOther;

    /**
     * 未结案原因
     */
    private String classCode;

    /**
     * 未结案原因名称
     */
    private String className;
    /**
     * 创建时间
     */
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date createdAt;

    /**
     * 更新时间
     */
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @JsonSerialize(using = JsonDateSerializer.class)
    private Date updatedAt;

    public ComplaintNotCloseCaseReasonTestDTO() {
        super();
    }

    public String getClassCode() {
        return classCode;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
    }

    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerParCode(){
        return ownerParCode;
    }

    public String getFollowerName() {
        return followerName;
    }

    public void setFollowerName(String followerName) {
        this.followerName = followerName;
    }

    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public Integer getOrgId(){
        return orgId;
    }


    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
    }

    public Long getComplaintInfoId(){
        return complaintInfoId;
    }


    public void  setComplaintInfoId(Long complaintInfoId) {
        this.complaintInfoId = complaintInfoId;
    }

    public String getObject(){
        return object;
    }


    public void  setObject(String object) {
        this.object = object;
    }

    public String getSmallClassNameOther() {
        return smallClassNameOther;
    }

    public void setSmallClassNameOther(String smallClassNameOther) {
        this.smallClassNameOther = smallClassNameOther;
    }

    public Date getFollowTime(){
        return followTime;
    }


    public void  setFollowTime(Date followTime) {
        this.followTime = followTime;
    }

    public String getFollower(){
        return follower;
    }


    public void  setFollower(String follower) {
        this.follower = follower;
    }

    public Integer getBigClass(){
        return bigClass;
    }


    public void  setBigClass(Integer bigClass) {
        this.bigClass = bigClass;
    }

    public String getBigClassName(){
        return bigClassName;
    }


    public void  setBigClassName(String bigClassName) {
        this.bigClassName = bigClassName;
    }

    public String getSmallClass(){
        return smallClass;
    }


    public void  setSmallClass(String smallClass) {
        this.smallClass = smallClass;
    }

    public String getSmallClassName(){
        return smallClassName;
    }


    public void  setSmallClassName(String smallClassName) {
        this.smallClassName = smallClassName;
    }

    public String getOther(){
        return other;
    }


    public void  setOther(String other) {
        this.other = other;
    }

    public Integer getDuration(){
        return duration;
    }


    public void  setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getDataSources(){
        return dataSources;
    }


    public void  setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid(){
        return isValid;
    }


    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Date getCreatedAt(){
        return createdAt;
    }


    public void  setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt(){
        return updatedAt;
    }


    public void  setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "ComplaintNotCloseCaseReasonDTO{" +
                "appId=" + appId +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", complaintInfoId=" + complaintInfoId +
                ", object=" + object +
                ", followTime=" + followTime +
                ", follower=" + follower +
                ", bigClass=" + bigClass +
                ", bigClassName=" + bigClassName +
                ", smallClass=" + smallClass +
                ", smallClassName=" + smallClassName +
                ", other=" + other +
                ", duration=" + duration +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
