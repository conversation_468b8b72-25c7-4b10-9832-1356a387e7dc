package com.yonyou.dmscus.customer.entity.po.complaint;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

    import com.yonyou.dmscloud.framework.base.po.BasePO;

    
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;

import java.util.Date;

/**
 * <p>
 * 客户投诉发送邮件记录
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
@TableName("tt_complaint_send_email_record")
public class ComplaintSendEmailRecordPO extends BasePO<ComplaintSendEmailRecordPO> {

    private static final long serialVersionUID=1L;
    
    /**
     * 系统ID
     */
    @TableField("app_id")
        private String appId;
    
    /**
     * 所有者代码 
     */
    @TableField("owner_code")
        private String ownerCode;
    
    /**
     * 所有者的父组织代码 
     */
    @TableField("owner_par_code")
        private String ownerParCode;
    
    /**
     * 组织ID
     */
    @TableField("org_id")
        private Integer orgId;
        
    /**
     * 主键ID
     */
            @TableId(value = "id", type = IdType.AUTO)
                        private Long id;
    
    /**
     * 投诉信息表主键ID
     */
    @TableField("complaint_info_id")
        private Long complaintInfoId;
    
    /**
     * 投诉ID
     */
    @TableField("complaint_id")
        private String complaintId;
    
    /**
     * 收件人邮箱
     */
    @TableField("send_email")
        private String sendEmail;

    /**
     * 收件人
     */
    @TableField("receipter")
    private String receipter;

    /**
     * 协助部门
     */
    @TableField("assist_department")
    private String assistDepartment;
    /**
     * 协助经销商
     */
    @TableField("assist_dealer_code")
    private String assistDealerCode;

    /**
     * 协助部门名称
     */
    @TableField("assist_department_name")
    private String assistDepartmentName;
    /**
     * 协助经销商名称
     */
    @TableField("assist_dealer_name")
    private String assistDealerName;
    
    /**
     * 邮箱类型
     */
    @TableField("email_type")
        private Integer emailType;
    
    /**
     * 发送时间
     */
    @TableField("send_time")
        private Date sendTime;
    
    /**
     * 发送状态 发送成功、发送失败
     */
    @TableField("send_status")
        private Integer sendStatus;
    
    /**
     * 邮箱
     */
    @TableField("email")
        private String email;
    
    /**
     * 标题
     */
    @TableField("title")
        private String title;
    
    /**
     * 主要内容
     */
    @TableField("contect")
        private String contect;
    
    /**
     * 数据来源 
     */
    @TableField("data_sources")
        private Integer dataSources;
    
    /**
     * 是否删除
     */
    @TableField("is_deleted")
        private Boolean isDeleted;
    
    /**
     * 是否有效 
     */
    @TableField("is_valid")
        private Integer isValid;
    
    /**
     * 创建时间
     */
    @TableField("created_at")
        private Date createdAt;
    
    /**
     * 更新时间
     */
    @TableField("updated_at")
        private Date updatedAt;

    public ComplaintSendEmailRecordPO(){
        super();
    }

                    
    @Override
    public String getAppId(){
        return appId;
    }

        @Override
        public void setAppId(String appId) {
            this.appId = appId;
            }
                    
    public String getOwnerCode(){
        return ownerCode;
    }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
            }
                    
    @Override
    public String getOwnerParCode(){
        return ownerParCode;
    }

        @Override
        public void setOwnerParCode(String ownerParCode) {
            this.ownerParCode = ownerParCode;
            }
                    
    public Integer getOrgId(){
        return orgId;
    }

        public void setOrgId(Integer orgId) {
            this.orgId = orgId;
            }
                    
    public Long getId(){
        return id;
    }

        public void setId(Long id) {
            this.id = id;
            }
                    
    public Long getComplaintInfoId(){
        return complaintInfoId;
    }

        public void setComplaintInfoId(Long complaintInfoId) {
            this.complaintInfoId = complaintInfoId;
            }
                    
    public String getComplaintId(){
        return complaintId;
    }

        public void setComplaintId(String complaintId) {
            this.complaintId = complaintId;
            }
                    
    public String getSendEmail(){
        return sendEmail;
    }

        public void setSendEmail(String sendEmail) {
            this.sendEmail = sendEmail;
            }
                    
    public Integer getEmailType(){
        return emailType;
    }

        public void setEmailType(Integer emailType) {
            this.emailType = emailType;
            }
                    
    public Date getSendTime(){
        return sendTime;
    }

        public void setSendTime(Date sendTime) {
            this.sendTime = sendTime;
            }
                    
    public Integer getSendStatus(){
        return sendStatus;
    }

        public void setSendStatus(Integer sendStatus) {
            this.sendStatus = sendStatus;
            }
                    
    public String getEmail(){
        return email;
    }

        public void setEmail(String email) {
            this.email = email;
            }
                    
    public String getTitle(){
        return title;
    }

        public void setTitle(String title) {
            this.title = title;
            }
                    
    public String getContect(){
        return contect;
    }

        public void setContect(String contect) {
            this.contect = contect;
            }
                    
    public Integer getDataSources(){
        return dataSources;
    }

        public void setDataSources(Integer dataSources) {
            this.dataSources = dataSources;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }

        public void setIsDeleted(Boolean isDeleted) {
            this.isDeleted = isDeleted;
            }
                    
    public Integer getIsValid(){
        return isValid;
    }

        public void setIsValid(Integer isValid) {
            this.isValid = isValid;
            }
                    
    @Override
    public Date getCreatedAt(){
        return createdAt;
    }

        @Override
        public void setCreatedAt(Date createdAt) {
            this.createdAt = createdAt;
            }
                    
    @Override
    public Date getUpdatedAt(){
        return updatedAt;
    }

        @Override
        public void setUpdatedAt(Date updatedAt) {
            this.updatedAt = updatedAt;
            }

    public String getReceipter() {
        return receipter;
    }

    public void setReceipter(String receipter) {
        this.receipter = receipter;
    }

    public String getAssistDepartment() {
        return assistDepartment;
    }

    public void setAssistDepartment(String assistDepartment) {
        this.assistDepartment = assistDepartment;
    }

    public String getAssistDealerCode() {
        return assistDealerCode;
    }

    public void setAssistDealerCode(String assistDealerCode) {
        this.assistDealerCode = assistDealerCode;
    }

    public String getAssistDepartmentName() {
        return assistDepartmentName;
    }

    public void setAssistDepartmentName(String assistDepartmentName) {
        this.assistDepartmentName = assistDepartmentName;
    }

    public String getAssistDealerName() {
        return assistDealerName;
    }

    public void setAssistDealerName(String assistDealerName) {
        this.assistDealerName = assistDealerName;
    }

    @Override
    protected Serializable pkVal(){
            return this.id;
        }

@Override
    public String toString(){
        return"ComplaintSendEmailRecordPO{" +
                            "appId=" + appId +
                                    ", ownerCode=" + ownerCode +
                                    ", ownerParCode=" + ownerParCode +
                                    ", orgId=" + orgId +
                                    ", id=" + id +
                                    ", complaintInfoId=" + complaintInfoId +
                                    ", complaintId=" + complaintId +
                                    ", sendEmail=" + sendEmail +
                                    ", emailType=" + emailType +
                                    ", sendTime=" + sendTime +
                                    ", sendStatus=" + sendStatus +
                                    ", email=" + email +
                                    ", title=" + title +
                                    ", contect=" + contect +
                                    ", dataSources=" + dataSources +
                                    ", isDeleted=" + isDeleted +
                                    ", isValid=" + isValid +
                                    ", createdAt=" + createdAt +
                                    ", updatedAt=" + updatedAt +
                    "}";
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dtoClass 需要进行转换的dtoClass
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

/**
 * 将PO 信息转化为DTO
 *
 * @param dto 需要进行转换的dto
 * <AUTHOR>
 * @since 2018/7/22 0022
 */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"id");
    }

}
