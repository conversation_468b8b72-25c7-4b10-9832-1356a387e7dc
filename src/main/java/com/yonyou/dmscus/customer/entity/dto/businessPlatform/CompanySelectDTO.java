package com.yonyou.dmscus.customer.entity.dto.businessPlatform;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 经销商多条件查询（中台）
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@ApiModel(value = "经销商查询条件", description="经销商查询条件")
@Data
public class CompanySelectDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "经销商主键ID")
    private String  companyId;

    @ApiModelProperty(value = "经销商代码")
    private String  companyCode;

    @ApiModelProperty(value = "经销商名称中文")
    private String  companyNameCn;

    @ApiModelProperty(value = "省份(可多个,用','分隔)")
    private String 	provinceId;

    @ApiModelProperty(value = "城市(可多个,用','分隔)")
    private String cityId;

    @ApiModelProperty(value = "区县(可多个,用','分隔)")
    private String countyId;

    @ApiModelProperty(value = "销售大区(可多个,用','分隔)")
    private String  salesBigArea;

    @ApiModelProperty(value = "销售小区(可多个,用','分隔)")
    private String  salesSmallArea;

    @ApiModelProperty(value = "售后大区(可多个,用','分隔)")
    private String  afterBigArea;

    @ApiModelProperty(value = "售后小区(可多个,用','分隔)")
    private String  afterSmallArea;

    @ApiModelProperty(value = "公司类型:15061001:销售公司;15061002:经销商集团;15061003:经销商公司")
    private String  companyType;

    @ApiModelProperty(value = "组织id")
    private String  orgId;




}
