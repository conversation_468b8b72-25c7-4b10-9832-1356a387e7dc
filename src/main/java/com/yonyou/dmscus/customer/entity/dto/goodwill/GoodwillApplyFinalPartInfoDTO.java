package com.yonyou.dmscus.customer.entity.dto.goodwill;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 亲善预申请最终解决方案配件信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
    
public class GoodwillApplyFinalPartInfoDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

        
    /**
     * 系统ID
     */
                private String appId;
                
    /**
     * 所有者代码
     */
                private String ownerCode;
                
    /**
     * 所有者的父组织代码
     */
                private String ownerParCode;
                
    /**
     * 组织ID
     */
                private Integer orgId;
                
    /**
     * 主键id
     */
                private Long id;
                
    /**
     * 亲善单id
     */
                private Long goodwillApplyId;
                
    /**
     * 亲善类型
     */
                private Integer goodwillType;
                
    /**
     * 零件代码
     */
                private String partCode;
                
    /**
     * 零件名称
     */
                private String partName;
                
    /**
     * 单价（不含税）
     */
                private BigDecimal unitPriceNoTax;
                
    /**
     * 税率
     */
                private String rate;
                
    /**
     * 税率
     */
                private String rates;
    
                
    /**
     * 单价（含税）
     */
                private String unitPriceTax;
                
    /**
     * 数量
     */
                private String quantity;
                
    /**
     * 总价（含税）
     */
                private BigDecimal amountTax;
                
    /**
     * 支持比例
     */
                private String supportProportion;
                
    /**
     * 亲善金额（含税）
     */
                private BigDecimal goodwillAmountTax;
                
    /**
     * 是否有效
     */
                private Integer isValid;
                
    /**
     * 是否删除:1,删除；0,未删除
     */
                private Boolean isDeleted;
                
    /**
     * 创建时间
     */
    private Date createdAt;
                
    /**
     * 修改时间
     */
    private Date updatedAt;
            
    public GoodwillApplyFinalPartInfoDTO() {
        super();
    }

                                
    public String getRates() {
		return rates;
	}


	public void setRates(String rates) {
		this.rates = rates;
	}


	public String getAppId(){
        return appId;
    }


    public void  setAppId(String appId) {
        this.appId = appId;
            }
                                
    public String getOwnerCode(){
        return ownerCode;
    }


    public void  setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
            }
                                
    public String getOwnerParCode(){
        return ownerParCode;
    }


    public void  setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
            }
                                
    public Integer getOrgId(){
        return orgId;
    }


    public void  setOrgId(Integer orgId) {
        this.orgId = orgId;
            }
                                            
    public Long getId(){
        return id;
    }


    public void  setId(Long id) {
        this.id = id;
            }
                                
    public Long getGoodwillApplyId(){
        return goodwillApplyId;
    }


    public void  setGoodwillApplyId(Long goodwillApplyId) {
        this.goodwillApplyId = goodwillApplyId;
            }
                                
    public Integer getGoodwillType(){
        return goodwillType;
    }


    public void  setGoodwillType(Integer goodwillType) {
        this.goodwillType = goodwillType;
            }
                                
    public String getPartCode(){
        return partCode;
    }


    public void  setPartCode(String partCode) {
        this.partCode = partCode;
            }
                                
    public String getPartName(){
        return partName;
    }


    public void  setPartName(String partName) {
        this.partName = partName;
            }
                                
    public BigDecimal getUnitPriceNoTax(){
        return unitPriceNoTax;
    }


    public void  setUnitPriceNoTax(BigDecimal unitPriceNoTax) {
        this.unitPriceNoTax = unitPriceNoTax;
            }
                                
                                
    public String getRate() {
		return rate;
	}


	public void setRate(String rate) {
		this.rate = rate;
	}


                                
    public BigDecimal getAmountTax(){
        return amountTax;
    }


    public void  setAmountTax(BigDecimal amountTax) {
        this.amountTax = amountTax;
            }
                                
                                
    public String getUnitPriceTax() {
		return unitPriceTax;
	}


	public void setUnitPriceTax(String unitPriceTax) {
		this.unitPriceTax = unitPriceTax;
	}


	public String getQuantity() {
		return quantity;
	}


	public void setQuantity(String quantity) {
		this.quantity = quantity;
	}


	public String getSupportProportion() {
		return supportProportion;
	}


	public void setSupportProportion(String supportProportion) {
		this.supportProportion = supportProportion;
	}


	public BigDecimal getGoodwillAmountTax(){
        return goodwillAmountTax;
    }


    public void  setGoodwillAmountTax(BigDecimal goodwillAmountTax) {
        this.goodwillAmountTax = goodwillAmountTax;
            }
                                
    public Integer getIsValid(){
        return isValid;
    }


    public void  setIsValid(Integer isValid) {
        this.isValid = isValid;
            }
                    
    public Boolean getIsDeleted(){
        return isDeleted;
    }


    public void  setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
            }
                                
    
    public Date getCreatedAt() {
		return createdAt;
	}


	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}


	public Date getUpdatedAt() {
		return updatedAt;
	}


	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}


	@Override
    public String toString() {
        return "GoodwillApplyFinalPartInfoDTO{" +
                                            "appId=" + appId +
                                                            ", ownerCode=" + ownerCode +
                                                            ", ownerParCode=" + ownerParCode +
                                                            ", orgId=" + orgId +
                                                            ", id=" + id +
                                                            ", goodwillApplyId=" + goodwillApplyId +
                                                            ", goodwillType=" + goodwillType +
                                                            ", partCode=" + partCode +
                                                            ", partName=" + partName +
                                                            ", unitPriceNoTax=" + unitPriceNoTax +
                                                            ", rate=" + rate +
                                                            ", unitPriceTax=" + unitPriceTax +
                                                            ", quantity=" + quantity +
                                                            ", amountTax=" + amountTax +
                                                            ", supportProportion=" + supportProportion +
                                                            ", goodwillAmountTax=" + goodwillAmountTax +
                                                            ", isValid=" + isValid +
                                                            ", isDeleted=" + isDeleted +
                                                            ", createdAt=" + createdAt +
                                                            ", updatedAt=" + updatedAt +
                                    "}";
    }

    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "id");
    }

}
