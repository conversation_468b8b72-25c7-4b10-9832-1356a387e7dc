package com.yonyou.dmscus.customer.entity.dto.talkskill;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 话术标签
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-20
 */
@Data
public class TalkskillTagDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

        
    /**
     * 主键ID
     */
    private Long tagId;
                
    /**
     * 父ID
     1 未冲账 2已冲账
     */
    private Integer parentId;
                
    /**
     * 标签编码
     */
    private String tagCode;
                
    /**
     * 标签名称
     */
    private String tagName;
                
    /**
     * 级别
     */
    private Integer level;
                
    /**
     * 数据来源
     */
    private Integer dataSources;
                
    /**
     * 是否删除，1：删除，0：未删除
     */
    private Integer isDeleted;
                
    /**
     * 是否有效
     */
    private Integer isValid;

    private Date createdAt;

    /**
     * 更新时间
     */
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    private LocalDateTime updatedAt;
            
    public TalkskillTagDTO() {
        super();
    }

    
    /**
     * 将DTO 转换为PO
     * //对某个对象属性进行赋值
     * //BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param poClass  需要转换的poClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @Override
    public <T extends BasePO> T transDtoToPo(Class<T> poClass) {
        return super.transDtoToPo(poClass);
    }

    /**
     * 将DTO 转换为PO
     * BeanMapperUtil.populateValueByMethod(transPO,"setXxx",this.getXxx);
     * @param po 需要转换的对象
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BasePO> void transDtoToPo(T po) {
        BeanMapperUtil.copyProperties(this, po, "tagId");
    }

}
