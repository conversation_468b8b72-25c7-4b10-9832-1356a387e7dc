package com.yonyou.dmscus.customer.entity.po.invitationautocreate;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import com.yonyou.dmscloud.framework.base.po.BasePO;

import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 平均里程计算日志
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-13
 */
@Data
@TableName("tt_daily_mileage_log")
public class DailyMileageLogPO extends BasePO<DailyMileageLogPO> {

    private static final long serialVersionUID = 1L;



    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;



    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;



    /**
     * 车架号
     */
    @TableField("vin")
    private String vin;

    /**
     * 最近记录时间1
     */
    @TableField("time_one")
    private Date timeOne;

    /**
     * 最近记录时间2
     */
    @TableField("time_two")
    private Date timeTwo;

    /**
     * 最近记录时间3
     */
    @TableField("time_three")
    private Date timeThree;

    /**
     * 最近记录时间4
     */
    @TableField("time_four")
    private Date timeFour;

    /**
     * 销售时间
     */
    @TableField("sales_date")
    private Date salesDate;

    /**
     * 最近里程(km)1
     */
    @TableField("mileage_one")
    private Integer mileageOne;

    /**
     * 最近里程(km)2
     */
    @TableField("mileage_two")
    private Integer mileageTwo;

    /**
     * 最近里程(km)3
     */
    @TableField("mileage_three")
    private Integer mileageThree;

    /**
     * 最近里程(km)4
     */
    @TableField("mileage_four")
    private Integer mileageFour;

    /**
     * 原日均里程
     */
    @TableField("last_daily_mileage")
    private BigDecimal lastDailyMileage;

    /**
     * 日均里程
     */
    @TableField("daily_mileage")
    private BigDecimal dailyMileage;

    /**
     * 来源：voc: voc里程计算  repair:工单计算
     */
    @TableField("count_type")
    private String countType;


    /**
     * 工单号
     */
    @TableField("ro_no")
    private String roNo;



    /**
     * 门店代码
     */
    @TableField("dealer_code")
    private String dealerCode;


    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
