package com.yonyou.dmscus.customer.entity.po.vocmileage;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.base.po.BasePO;
import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

/**
 * <p>
 * VOC里程基础表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-08
 */
@TableName("tt_voc_mileage")
public class VocMileagePO extends BasePO<VocMileagePO> {

    private static final long serialVersionUID = 1L;

    /**
     * 系统ID
     */
    @TableField("app_id")
    private String            appId;

    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String            ownerCode;

    /**
     * 所有者的父组织代码
     */
    @TableField("owner_par_code")
    private String            ownerParCode;

    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer           orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long              id;

    /**
     * VIN
     */
    @TableField("vin")
    private String            vin;

    /**
     * 里程(米)
     */
    @TableField("mileage_m")
    private Integer           mileageM;

    /**
     * 里程(公里)
     */
    @TableField("mileage_km")
    private Integer           mileageKm;

    /**
     * 获取里程时间
     */
    @TableField("get_time")
    private Date     getTime;

    /**
     * 导入账号
     */
    @TableField("created_code")
    private String            createdCode;

    /**
     * 数据来源 (1：界面新增 2：Excel导入)
     */
    @TableField("data_sources")
    private Integer           dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean           isDeleted;

    /**
     * 是否有效 (10041001：有效 10041002：无效)
     */
    @TableField("is_valid")
    private Integer           isValid;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date     createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date     updatedAt;

    public VocMileagePO(){
        super();
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerParCode() {
        return ownerParCode;
    }

    public void setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public Integer getMileageM() {
        return mileageM;
    }

    public void setMileageM(Integer mileageM) {
        this.mileageM = mileageM;
    }

    public Integer getMileageKm() {
        return mileageKm;
    }

    public void setMileageKm(Integer mileageKm) {
        this.mileageKm = mileageKm;
    }

    public Date getGetTime() {
        return getTime;
    }

    public void setGetTime(Date getTime) {
        this.getTime = getTime;
    }

    public String getCreatedCode() {
        return createdCode;
    }

    public void setCreatedCode(String createdCode) {
        this.createdCode = createdCode;
    }

    public Integer getDataSources() {
        return dataSources;
    }

    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "VocMileagePO{" + "appId=" + appId + ", ownerCode=" + ownerCode + ", ownerParCode=" + ownerParCode
               + ", orgId=" + orgId + ", id=" + id + ", vin=" + vin + ", mileageM=" + mileageM + ", mileageKm="
               + mileageKm + ", getTime=" + getTime + ", createdCode=" + createdCode + ", dataSources=" + dataSources
               + ", isDeleted=" + isDeleted + ", isValid=" + isValid + ", createdAt=" + createdAt + ", updatedAt="
               + updatedAt + "}";
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto) {
        BeanMapperUtil.copyProperties(this, dto, "id");
    }

}
