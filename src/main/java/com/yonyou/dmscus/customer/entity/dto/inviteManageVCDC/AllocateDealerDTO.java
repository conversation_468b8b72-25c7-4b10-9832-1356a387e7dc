package com.yonyou.dmscus.customer.entity.dto.inviteManageVCDC;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AllocateDealerDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String dealerCode;


    private List<InviteVehicleRecordDTO> inviteRecordList;


}
