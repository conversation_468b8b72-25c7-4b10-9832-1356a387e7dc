package com.yonyou.dmscus.customer.entity.po.faultLight;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("线索与诊断信息的关联关系 ")
@TableName("tt_clues_diagnostic_info_relation")
public class CluesDiagnosticInfoRelationPO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @TableField("id")
    private Long id;

    /**
     * 线索预处理编号
     */
    @ApiModelProperty("线索预处理编号")
    @TableField("leads_preprocessing_id")
    private Long leadsPreprocessingId;

    /**
     * RVDC数据接收时间
     */
    @ApiModelProperty("RVDC数据接收时间")
    @TableField("ct_upload")
    private String ctUpload;

    /**
     * etl处理时间
     */
    @ApiModelProperty("etl处理时间")
    @TableField("etl_time")
    private String etlTime;

    /**
     * 处理状态（0-未处理，1-处理中，2-处理完成）
     */
    @ApiModelProperty("处理状态（0-未处理，1-处理中，2-处理完成，3-处理失败）")
    @TableField("processing_status")
    private Integer processingStatus;

    /**
     * 执行次数
     */
    @ApiModelProperty("执行次数")
    @TableField("number_of_executions")
    private Integer numberOfExecutions;

    /**
     * 是否删除:1，删除；0，未删除
     */
    @ApiModelProperty("是否删除:1，删除；0，未删除")
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 数据创建时间
     */
    @ApiModelProperty("数据创建时间")
    @TableField("created_at")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 数据创建人
     */
    @ApiModelProperty("数据创建人")
    @TableField("created_by")
    private String createdBy;

    /**
     * 数据修改时间
     */
    @ApiModelProperty("数据修改时间")
    @TableField("updated_at")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 数据修改人
     */
    @ApiModelProperty("数据修改人")
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 创建sql人
     */
    @ApiModelProperty("创建sql人")
    @TableField("create_sqlby")
    private String createSqlby;

    /**
     * 更新人sql人
     */
    @ApiModelProperty("更新人sql人")
    @TableField("update_sqlby")
    private String updateSqlby;
}
