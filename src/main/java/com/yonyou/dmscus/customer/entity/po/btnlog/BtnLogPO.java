package com.yonyou.dmscus.customer.entity.po.btnlog;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.BasePo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * bi推送数据记录表
 */
@TableName("tt_btn_log")
@Data
@ApiModel(value = "BtnLogPO", description = "VOC日志记录表")
public class BtnLogPO  extends BasePo {
//通知时间
  @ApiModelProperty(value = "通知时间",notes = "通知时间",required = true,name = "update_time",example = "20221011",dataType = "String")
  @TableField("update_time")
  private String updateTime;

  @ApiModelProperty(value = "ossurl",notes = "ossurl",required = true,name = "file_url",example = "xxx",dataType = "String")
  @TableField("file_url")
  private String fileUrl;

  @ApiModelProperty(value = "数据类型",notes = "数据类型:1,激活数据；0,车灯数据，2拉取激活，3拉取车灯",required = true,name = "data_type",example = "0",dataType = "Integer")
  @TableField("data_type")
  private Integer dataType;


  //tt_accident_clues
  @TableField("is_sc")
  @ApiModelProperty(value = "是否成功",notes = "是否成功:1,失败；0,成功，2数据为空",required = true,name = "is_sc",example = "0",dataType = "Integer")
  private Integer isSc;

  @TableField("mess")
  @ApiModelProperty(value = "描述",notes = "描述",required = true,name = "mess",example = "0",dataType = "String")
  private String mess;

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    if (!super.equals(o)) return false;
    BtnLogPO po = (BtnLogPO) o;
    return Objects.equals(updateTime, po.updateTime) &&
            Objects.equals(fileUrl, po.fileUrl) &&
            Objects.equals(dataType, po.dataType) &&
            Objects.equals(isSc, po.isSc) &&
            Objects.equals(mess, po.mess);
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), updateTime, fileUrl, dataType, isSc, mess);
  }

}
