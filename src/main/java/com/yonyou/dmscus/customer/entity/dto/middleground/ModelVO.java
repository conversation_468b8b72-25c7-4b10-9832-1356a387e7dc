package com.yonyou.dmscus.customer.entity.dto.middleground;

public class ModelVO {
    private String companyCode;
    private String dataSources;
    private Integer fuelType;
    private Integer id;
    private Integer isValid;
    private String modelCode;
    private String modelName;
    private String modelNameEn;
    private String modelYear;
    private Integer seriesId;

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getDataSources() {
        return dataSources;
    }

    public void setDataSources(String dataSources) {
        this.dataSources = dataSources;
    }

    public Integer getFuelType() {
        return fuelType;
    }

    public void setFuelType(Integer fuelType) {
        this.fuelType = fuelType;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getModelNameEn() {
        return modelNameEn;
    }

    public void setModelNameEn(String modelNameEn) {
        this.modelNameEn = modelNameEn;
    }

    public String getModelYear() {
        return modelYear;
    }

    public void setModelYear(String modelYear) {
        this.modelYear = modelYear;
    }

    public Integer getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(Integer seriesId) {
        this.seriesId = seriesId;
    }

    @Override
    public String toString() {
        return "ModelVO{" +
                "companyCode='" + companyCode + '\'' +
                ", dataSources='" + dataSources + '\'' +
                ", fuelType=" + fuelType +
                ", id=" + id +
                ", isValid=" + isValid +
                ", modelCode='" + modelCode + '\'' +
                ", modelName='" + modelName + '\'' +
                ", modelNameEn='" + modelNameEn + '\'' +
                ", modelYear='" + modelYear + '\'' +
                ", seriesId=" + seriesId +
                '}';
    }
}
