package com.yonyou.dmscus.customer.entity.po.complaint;

import com.baomidou.mybatisplus.annotation.*;
import com.yonyou.dmscloud.framework.base.dto.BaseDTO;

import com.yonyou.dmscloud.framework.base.po.BasePO;


import com.yonyou.dmscloud.framework.util.bean.BeanMapperUtil;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 协助部门
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@TableName("tt_complaint_assist_department")
public class ComplaintAssistPO extends BasePO<ComplaintAssistDepartmentPO> {

    private static final long serialVersionUID=1L;

    /**
     * 系统ID
     */
    @TableField("app_id")
    private String appId;

    /**
     * 所有者代码
     */
    @TableField("owner_code")
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    @TableField("owner_par_code")
    private String ownerParCode;

    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 投诉信息表主键ID
     */
    @TableField("complaint_info_id")
    private Long complaintInfoId;

    /**
     * 跟进对象 区域经理、经销商、CCM、客服中心
     */
    @TableField("object")
    private String object;

    /**
     * 分配时间
     */
    @TableField("follow_time")
    private Date followTime;

    /**
     * 分配人
     */
    @TableField("follower")
    private String follower;
    /**
     * 分配人
     */
    @TableField("follower_name")
    private String followerName;

    /**
     * 协助部门
     */
    @TableField("assist_department")
    private String assistDepartment;

    /**
     * 协助部门名称
     */
    @TableField("assist_department_name")
    private String assistDepartmentName;

    /**
     * 协助部门
     */
    @TableField("assist_dealer_code")
    private String assistDealerCode;

    /**
     * 协助部门名称
     */
    @TableField("assist_dealer_name")
    private String assistDealerName;

    /**
     * 希望回复时间
     */
    @TableField("hope_reply_time")
    private Integer hopeReplyTime;

    /**
     * 需协助说明
     */
    @TableField("assist_explain")
    private String assistExplain;

    /**
     * 是否回复
     */
    @TableField("is_reply")
    private Integer isReply;

    /**
     * 回复时间
     */
    @TableField("reply_time")
    private Date replyTime;

    /**
     * 是否完成
     */
    @TableField("is_finish")
    private Integer isFinish;

    /**
     * 完成时间
     */
    @TableField("finish_time")
    private Date finishTime;

    /**
     * 是否已读 1 已读 0 未读
     */
    @TableField("is_read")
    private Boolean isRead;

    /**
     * 数据来源
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Integer isValid;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;
    private String allDealer;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;

    public ComplaintAssistPO(){
        super();
    }


    public String getFollowerName() {
        return followerName;
    }

    public void setFollowerName(String followerName) {
        this.followerName = followerName;
    }

    public String getOwnerCode(){
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }



    public Integer getOrgId(){
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getId(){
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getComplaintInfoId(){
        return complaintInfoId;
    }

    public void setComplaintInfoId(Long complaintInfoId) {
        this.complaintInfoId = complaintInfoId;
    }

    public String getObject(){
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public Date getFollowTime(){
        return followTime;
    }

    public void setFollowTime(Date followTime) {
        this.followTime = followTime;
    }

    public String getFollower(){
        return follower;
    }

    public void setFollower(String follower) {
        this.follower = follower;
    }

    public String getAssistDepartment(){
        return assistDepartment;
    }

    public void setAssistDepartment(String assistDepartment) {
        this.assistDepartment = assistDepartment;
    }

    public String getAssistDepartmentName(){
        return assistDepartmentName;
    }

    public void setAssistDepartmentName(String assistDepartmentName) {
        this.assistDepartmentName = assistDepartmentName;
    }

    public String getAssistDealerCode(){
        return assistDealerCode;
    }

    public void setAssistDealerCode(String assistDealerCode) {
        this.assistDealerCode = assistDealerCode;
    }

    public String getAssistDealerName(){
        return assistDealerName;
    }

    public void setAssistDealerName(String assistDealerName) {
        this.assistDealerName = assistDealerName;
    }

    public Integer getHopeReplyTime(){
        return hopeReplyTime;
    }

    public void setHopeReplyTime(Integer hopeReplyTime) {
        this.hopeReplyTime = hopeReplyTime;
    }

    public String getAssistExplain(){
        return assistExplain;
    }

    public void setAssistExplain(String assistExplain) {
        this.assistExplain = assistExplain;
    }

    public Integer getIsReply(){
        return isReply;
    }

    public void setIsReply(Integer isReply) {
        this.isReply = isReply;
    }

    public Date getReplyTime(){
        return replyTime;
    }

    public void setReplyTime(Date replyTime) {
        this.replyTime = replyTime;
    }

    public Integer getIsFinish(){
        return isFinish;
    }

    public void setIsFinish(Integer isFinish) {
        this.isFinish = isFinish;
    }

    public Date getFinishTime(){
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Boolean getIsRead(){
        return isRead;
    }

    public void setIsRead(Boolean isRead) {
        this.isRead = isRead;
    }

    public Integer getDataSources(){
        return dataSources;
    }

    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Boolean getIsDeleted(){
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid(){
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }


    public String getAllDealer() {
        return allDealer;
    }

    public void setAllDealer(String allDealer) {
        this.allDealer = allDealer;
    }

    @Override
    protected Serializable pkVal(){
        return this.id;
    }

    @Override
    public String toString(){
        return"ComplaintAssistDepartmentPO{" +
                "appId=" + appId +
                ", ownerCode=" + ownerCode +
                ", ownerParCode=" + ownerParCode +
                ", orgId=" + orgId +
                ", id=" + id +
                ", complaintInfoId=" + complaintInfoId +
                ", object=" + object +
                ", followTime=" + followTime +
                ", follower=" + follower +
                ", assistDepartment=" + assistDepartment +
                ", assistDepartmentName=" + assistDepartmentName +
                ", assistDealerCode=" + assistDealerCode +
                ", assistDealerName=" + assistDealerName +
                ", hopeReplyTime=" + hopeReplyTime +
                ", assistExplain=" + assistExplain +
                ", isReply=" + isReply +
                ", replyTime=" + replyTime +
                ", isFinish=" + isFinish +
                ", finishTime=" + finishTime +
                ", isRead=" + isRead +
                ", dataSources=" + dataSources +
                ", isDeleted=" + isDeleted +
                ", isValid=" + isValid +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                "}";
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dtoClass 需要进行转换的dtoClass
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> T transPoToDto(Class<T> dtoClass) {
        return super.transDtoToPo(dtoClass);
    }

    /**
     * 将PO 信息转化为DTO
     *
     * @param dto 需要进行转换的dto
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public <T extends BaseDTO> void transPoToDto(T dto){
        BeanMapperUtil.copyProperties(this,dto,"id");
    }

}
