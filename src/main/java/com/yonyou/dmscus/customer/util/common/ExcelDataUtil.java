package com.yonyou.dmscus.customer.util.common;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.yonyou.dmscus.customer.common.BaseCheckDataVO;
import com.yonyou.dmscus.customer.enums.ExcelColumnEnum;



public class ExcelDataUtil {

    /** 日志 */
    private final static Logger LOGGER = LoggerFactory.getLogger(ExcelDataUtil.class);
    
    public static String checkAndGetValue(BaseCheckDataVO checkDataVO, ExcelColumnEnum excelColumn) {
        
        String value = checkDataVO.getColumnValue(excelColumn.code());
        
        if(excelColumn.rule().isMust() && StringUtils.isBlank(value)) {
            checkDataVO.addError(excelColumn.display()+"字段为空！");
        }
        
        if(StringUtils.isNotBlank(value)) {
            Integer maxLength = excelColumn.rule().getMaxLength();
            Integer minLength = excelColumn.rule().getMinLength();
            
            if(maxLength != null && value.length() > maxLength) {
                checkDataVO.addError(excelColumn.display()+"不能超过"+maxLength+"个字！");
            }
            
            if(minLength != null && value.length() < minLength) {
                checkDataVO.addError(excelColumn.display()+"不能少于"+minLength+"个字！");
            }
        }
        
        return value;
    }
    
    public static BigDecimal checkAndGetPrice(BaseCheckDataVO checkDataVO, ExcelColumnEnum excelColumn) {
        String value = checkAndGetValue(checkDataVO, excelColumn);
        
        BigDecimal price = null;
        if(StringUtils.isNotBlank(value)) {
            try {
            	price = BigDecimal.valueOf(Double.valueOf(value));
            	BigDecimal minPrice = excelColumn.rule().getMinPrice();
                BigDecimal maxPrice = excelColumn.rule().getMaxPrice();
                
            	if(minPrice != null && price.compareTo(minPrice) == -1) {
            		checkDataVO.addError(excelColumn.display()+"不能小于"+minPrice);
                }
            	
            	if(maxPrice != null && price.compareTo(maxPrice) == 1) {
            		checkDataVO.addError(excelColumn.display()+"不能大于"+maxPrice);
            	}
            } catch (Exception e) {
                LOGGER.error("ExcelDataUtil.checkAndGetPrice"+excelColumn.display()+"字段转换失败:{}",e);
                checkDataVO.addError(excelColumn.display()+"字段转换失败");
            }
        }
        
        return price;
    }
    
    /**
     * 	校验数量
     * @param checkDataVO
     * @param excelColumn
     * @return
     */
    public static Long checkAndGetNum(BaseCheckDataVO checkDataVO, ExcelColumnEnum excelColumn) {
        String value = checkAndGetValue(checkDataVO, excelColumn);
        
        Long num = null;
        if(StringUtils.isNotBlank(value)) {
            try {
            	num = Long.valueOf(value);
            	Long minNum = excelColumn.rule().getMinNum();
            	Long maxNum = excelColumn.rule().getMaxNum();
                
            	if(minNum != null && num.compareTo(minNum) == -1) {
            		checkDataVO.addError(excelColumn.display()+"不能小于"+minNum);
                }
            	
            	if(maxNum != null && num.compareTo(maxNum) == 1) {
            		checkDataVO.addError(excelColumn.display()+"不能大于"+maxNum);
            	}
            } catch (Exception e) {
                LOGGER.error("ExcelDataUtil.checkAndGetNum"+excelColumn.display()+"字段转换失败:{}",e);
                checkDataVO.addError(excelColumn.display()+"字段转换失败");
            }
        }
        
        return num;
    }
    
    
    
    
    /**
     * 功能描述: <br>
     * 〈功能详细描述〉
     * 根据格式样式日期转换(默认yyyy-MM-DD)
     * 
     * @param checkDataVO
     * @param excelColumn
     * @param pattern
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static Date checkAndGetpatternDate(BaseCheckDataVO checkDataVO, ExcelColumnEnum excelColumn,String pattern) {
        String value = checkAndGetValue(checkDataVO, excelColumn);
        Date date = null;
        if(StringUtils.isNotBlank(value)) {
            try {
                date = DateUtils.parse(value, pattern);
            } catch (Exception e) {
                LOGGER.error("ExcelDataUtil.checkAndGetpatternDate("+excelColumn.display()+","+value+")出错:{}",e);
                checkDataVO.addError(excelColumn.display()+"日期格式不对！");
            }
        }
        return date;
    }
}
