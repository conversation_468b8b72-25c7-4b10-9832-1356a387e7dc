package com.yonyou.dmscus.customer.util.common;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.Date;

import com.yonyou.dmscus.customer.service.common.HttpLogService;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.yonyou.dmscloud.function.exception.DALException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class HttpClient {

	private static final Logger logger = LoggerFactory.getLogger(HttpClient.class);

	static HttpLogService httpLogService;
	@SuppressWarnings("unused")
	/**
	 *
	 * @param xml
	 * @param url
	 * @return
	 */
	public static Document requestInterface(String xml,String url) {
		Document soapRes = null;
		//创建httpcleint对象
		CloseableHttpClient httpClient = HttpClients.createDefault();
		//创建http Post请求
		HttpPost httpPost = new HttpPost(url);
		// 构建请求配置信息
		RequestConfig config = RequestConfig.custom().setConnectTimeout(60*1000) //连接超时时间
				.setConnectionRequestTimeout(60*1000) //从连接池中取的连接的最长时间
				.setSocketTimeout(60 * 1000) //数据传输的超时时间
				.build();
		httpPost.setConfig(config);
		CloseableHttpResponse response = null;
		Integer code =null;
		String content="";
		try {
			//采用SOAP1.1调用服务端，这种方式能调用服务端为soap1.1和soap1.2的服务
			httpPost.setHeader("Content-Type", "text/xml;charset=UTF-8");
			StringEntity stringEntity = new StringEntity(xml, Charset.forName("UTF-8"));
			httpPost.setEntity(stringEntity);
			response = httpClient.execute(httpPost);
			code= response.getStatusLine().getStatusCode();
			logger.info("响应结果code："+code);
			// 判断返回状态是否为200
			if (code == 200) {
				content = EntityUtils.toString(response.getEntity(), "UTF-8");
				logger.info("响应结果："+content);
				soapRes = Jsoup.parse(content);
			} else {
				logger.info("调用失败!");
				throw new DALException("调用失败!");
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new DALException("调用失败!");
		} finally {
			if (null != response) {
				try {
					response.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (null != httpClient) {
				try {
					httpClient.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			httpLogService.saveHttpLog("",url,xml,"POST",code+"",content);
		}
		return soapRes;
	}


	public static  Document requestInterface2(String xml,String http) {
		OutputStream os=null;
		Integer responseCode = null;  //返回状态码
		String responseXml=null;   //返回xml
		Document soapRes = null;
		try {
			logger.info("请求开始："+http);
			logger.info("请求参数："+xml);
			logger.info("开始请求："+DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
			// 第一步：创建服务地址
			URL url = new URL(http);
			// 第二步：打开一个通向服务地址的连接
			HttpURLConnection connection = (HttpURLConnection) url.openConnection();
			logger.info("建立连接："+DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
			// 第三步：设置超时
			connection.setConnectTimeout(60*1000);
			connection.setReadTimeout(60*1000);
			logger.info("超时设置："+120*1000);
			// 3.1发送方式设置
			connection.setRequestMethod("POST");
			// 3.2设置数据格式：content-type
			connection.setRequestProperty("content-type", "text/xml;charset=UTF-8");
			// 3.3设置输入输出，因为默认新创建的connection没有读写权限，
			connection.setDoInput(true);
			connection.setDoOutput(true);
			// 第四步：拼接SOAP数据，发送请求
			String soapXML = xml;
			os = connection.getOutputStream();
			os.write(soapXML.getBytes());
			logger.info("发送请求："+DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
			// 第五步：接收服务端响应，打印
			responseCode = connection.getResponseCode();
			logger.info("响应结果："+DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
			if (200 == responseCode) {// 表示服务端响应成功
				InputStream is = connection.getInputStream();
				// 将字节流转换为字符流
				InputStreamReader isr = new InputStreamReader(is, "UTF-8");
				// 使用缓存区
				BufferedReader br = new BufferedReader(isr);
				StringBuilder sb = new StringBuilder();
				String temp = null;
				while (null != (temp = br.readLine())) {
					sb.append(temp);
				}
				responseXml=sb.toString();
				soapRes = Jsoup.parse(responseXml);
				is.close();
				isr.close();
				br.close();

			}else {
				logger.info("请求失败");
			}
		} catch (Exception e) {
			logger.info("请求失败");
			e.printStackTrace();
			throw new DALException("请求失败");
		}finally {
			if(os!=null) {
				try {
					os.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			logger.info("响应结果状态码："+responseCode);
			logger.info("响应结果："+responseXml);
		}
		return soapRes;
	}



	/**
	 * GET
	 * @param http
	 * @return
	 */
	public static  String requestInterfaceJsonGet(String http) {

		OutputStream os=null;
		Integer responseCode = null;  //返回状态码
		String resutlStr = null;
		try {
			logger.info("请求开始："+http);
			logger.info("开始请求："+DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
			// 第一步：创建服务地址
			URL url = new URL(http);
			// 第二步：打开一个通向服务地址的连接
			HttpURLConnection connection = (HttpURLConnection) url.openConnection();
			logger.info("建立连接："+DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
			// 第三步：设置超时
			connection.setConnectTimeout(60*1000);
			connection.setReadTimeout(60*1000);
			logger.info("超时设置："+120*1000);
			/** 设置通用的请求属性 */
			connection.setRequestMethod("GET");//  
			connection.setRequestProperty("accept", "*/*");
			connection.setRequestProperty("connection", "Keep-Alive");
			connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			// 3.2设置数据格式：content-type   
			connection.setRequestProperty("content-type", "application/x-www-form-urlencoded");
			connection.connect();
			responseCode=connection.getResponseCode();
			if (200 == responseCode) {// 表示服务端响应成功
				InputStream is=connection.getInputStream();		//以输入流的形式返回
				//将输入流转换成字符串
				ByteArrayOutputStream baos=new ByteArrayOutputStream();
				byte [] buffer=new byte[1024];
				int len=0;
				while((len=is.read(buffer))!=-1){
					baos.write(buffer, 0, len);
				}
				resutlStr=baos.toString();
				logger.info("返回-result=========:"+resutlStr);
			}else {
				logger.info("请求失败");
				throw new DALException("请求失败");
			}
		} catch (Exception e) {
			logger.info("请求失败");
			e.printStackTrace();
			throw new DALException("请求失败");
		}finally {
			if(os!=null) {
				try {
					os.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			logger.info("响应结果状态码："+responseCode);
		}
		return resutlStr;
	}


	/**
	 * POST
	 * 模拟客户端发送json
	 * @param json
	 * @param http
	 * @return
	 */
	public static  String requestInterfaceJson(String json,String http) {

		OutputStream os=null;
		Integer responseCode = null;  //返回状态码
		String resultStr = null;
		try {
			logger.info("请求开始："+http);
			logger.info("开始请求："+DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
			// 第一步：创建服务地址
			URL url = new URL(http);
			// 第二步：打开一个通向服务地址的连接
			HttpURLConnection connection = (HttpURLConnection) url.openConnection();
			logger.info("建立连接："+DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
			// 第三步：设置超时
			connection.setConnectTimeout(60*1000);
			connection.setReadTimeout(60*1000);
			logger.info("超时设置："+120*1000);
			// 3.1发送方式设置
			connection.setRequestMethod("POST");

			// 3.2设置数据格式：content-type
			connection.setRequestProperty("content-type", "application/json;charset=UTF-8");
			// 3.3设置输入输出，因为默认新创建的connection没有读写权限，
			connection.setDoInput(true);
			connection.setDoOutput(true);
			// 第四步：拼接SOAP数据，发送请求
			os = connection.getOutputStream();
			if(json!=null) {
				os.write(json.getBytes());
			}
			logger.info("发送请求："+DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
			// 第五步：接收服务端响应，打印
			responseCode = connection.getResponseCode();
			logger.info("响应结果："+DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
			if (200 == responseCode) {// 表示服务端响应成功
				InputStream in = connection.getInputStream();
				try {
					byte[] data1 = new byte[in.available()];
					in.read(data1);
					// 转成字符串
					resultStr = new String(data1);
					logger.info("响应结果："+resultStr);
				} catch (Exception e1) {
					e1.printStackTrace();
				}

			}else {
				logger.info("请求失败");
				throw new DALException("请求失败");
			}
		} catch (Exception e) {
			logger.info("请求失败");
			e.printStackTrace();
			throw new DALException("请求失败");
		}finally {
			if(os!=null) {
				try {
					os.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			logger.info("响应结果状态码："+responseCode);
			httpLogService.saveHttpLog("",http,json,"POST",responseCode+"",resultStr);
		}
		return resultStr;
	}
	/**
	 * POST
	 * 模拟客户端发送json
	 * @param json
	 * @param http
	 * @return
	 */
	public static  String requestInterfaceJsonCus(String describe,String json,String http,String sign,String apiuid,String noncestr,String timestamp) {
		OutputStream os=null;
		Integer responseCode = null;  //返回状态码
		String resultStr = null;
		try {
			logger.info("请求开始："+http);
			logger.info("开始请求："+DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
			// 第一步：创建服务地址
			URL url = new URL(http);
			// 第二步：打开一个通向服务地址的连接
			HttpURLConnection connection = (HttpURLConnection) url.openConnection();
			logger.info("建立连接："+DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
			// 第三步：设置超时
			connection.setConnectTimeout(60*1000);
			connection.setReadTimeout(60*1000);
			logger.info("超时设置："+120*1000);

			// 3.1发送方式设置
			connection.setRequestMethod("POST");
			// 3.2设置数据格式：content-type
			connection.setRequestProperty("content-type", "application/json;charset=UTF-8");
			//添加header
			connection.setRequestProperty("sign",sign);
			logger.info("sign："+sign);
			connection.setRequestProperty("apiuid",apiuid);
			logger.info("apiuid："+apiuid);
			connection.setRequestProperty("noncestr",noncestr);
			logger.info("noncestr："+noncestr);
			connection.setRequestProperty("timestamp",timestamp);
			logger.info("timestamp："+timestamp);

			// 3.3设置输入输出，因为默认新创建的connection没有读写权限，
			connection.setDoInput(true);
			connection.setDoOutput(true);
			// 第四步：拼接SOAP数据，发送请求
			os = connection.getOutputStream();
			if(json!=null) {
				os.write(json.getBytes());
			}
			logger.info("发送请求："+DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
			// 第五步：接收服务端响应，打印
			responseCode = connection.getResponseCode();
			logger.info("响应结果："+DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
			if (200 == responseCode) {// 表示服务端响应成功
				InputStream in = connection.getInputStream();
				try {
					byte[] data1 = new byte[in.available()];
					in.read(data1);
					// 转成字符串
					resultStr = new String(data1);
					logger.info("响应结果："+resultStr);
				} catch (Exception e1) {
					e1.printStackTrace();
				}

			}else {
				logger.info("请求失败");
				throw new DALException("请求失败");
			}
		} catch (Exception e) {
			logger.info("请求失败");
			e.printStackTrace();
			throw new DALException("请求失败");
		}finally {
			if(os!=null) {
				try {
					os.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			logger.info("响应结果状态码："+responseCode);
			httpLogService.saveHttpLog(describe,http,json,"POST",responseCode+"",resultStr);
		}
		return resultStr;
	}

	@Resource
	public  void setHttpLogService(HttpLogService httpLogService) {
		HttpClient.httpLogService = httpLogService;
	}
}
