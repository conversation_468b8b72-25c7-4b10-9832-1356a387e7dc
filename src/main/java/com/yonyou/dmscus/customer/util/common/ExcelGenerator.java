package com.yonyou.dmscus.customer.util.common;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.ibatis.session.ResultContext;
import org.apache.ibatis.session.ResultHandler;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.util.Map;

/**
 * Mybatis查询数据采用List的方式返回结果集，当结果集过大，内存无法将结果集对象全部容纳，抛出OOM异常，
 * 本类主要采用Mybatis提供的ResultHandler接口逐条处理数据的方式读取数据，对数据进行转化。
 * 并将结果集逐行转化为EXCEL，当结果集过大，Workbook对象也会太大，故可以设置isLargeExcel=true，
 * 采用POI缓存机制，达到一定条目，缓存到临时文件的方式，用以实现巨量数据导出为EXCEL。
 * <AUTHOR>
 *
 * @param
 * @param <E> Mybatis对象类
 */
public class ExcelGenerator<E> implements ResultHandler<E> {
    protected String[] titles;
    protected String[] fields;
    protected int colcnt;
    protected Map<String,DecimalFormat> numberFormats;
    protected Map<String,DateFormat> dateFormats;

    protected Workbook workbook;
    protected String sheetName;
    protected int sheetIndex;
    protected Sheet sheet;
    protected int rownum = 0;
    protected boolean isLargeExcel=false;

    /**
     *
     * @param titles excel表头名列表
     * @param fields 需要输出的excel列对应的数据对象属性名列表
     * @param numberFormats 需要格式化的数字类属性的输出格式映射表
     * @param dateFormats 需要格式化的日期型字段属性的输出格式映射表
     */
    public ExcelGenerator(String[] titles, String[] fields,
                          Map<String,DecimalFormat> numberFormats,
                          Map<String,DateFormat> dateFormats) {
        if (titles==null)
            this.titles=fields;
        else
            this.titles=titles;
        this.fields=fields;
        this.colcnt=fields.length;
        this.numberFormats=numberFormats;
        this.dateFormats=dateFormats;
    }

    public void initWorkbook(String sheetName,boolean isLargeExcel) throws IOException{

        this.isLargeExcel=isLargeExcel;
        this.sheetName=sheetName;

        if (isLargeExcel){
            SXSSFWorkbook wb = new SXSSFWorkbook(100);
            wb.setCompressTempFiles(false);
            workbook=wb;
        }else{
            workbook = new XSSFWorkbook();
        }
        sheetIndex=1;
        sheet = workbook.createSheet(sheetName+"-"+sheetIndex);
        createHeader();
    }

    public Workbook getWorkbook(){
        return workbook;
    }

    @Override
    public void handleResult(ResultContext<? extends E> resultContext) {
        rownum++;
        try {
            E entity=resultContext.getResultObject();
            if (entity==null){
                return;
            }
            Row row = null;
            Cell cell = null;

//            createRow(entity);
            row = sheet.createRow(rownum);
            for(int cellnum = 0; cellnum < colcnt; cellnum++){
                createCell(rownum,row,cellnum,entity);
            }

            if (isLargeExcel && rownum % 100 == 0) {
                // 每100行，缓存一下，减少内存消耗
                //System.out.println("rownum:"+rownum+"-----------"+System.currentTimeMillis());
                ((SXSSFSheet)sheet).flushRows(100);
            }
            if (rownum % 1000000 == 0) {
                //一百万行，另外创建一个sheet
                sheetIndex++;
                String newSheetName=sheetName+"-"+sheetIndex;
                //System.out.println("rownum:"+rownum+"-----------"+newSheetName);
                sheet=workbook.createSheet(newSheetName);
                createHeader();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 数据转化处理
     */
//    public abstract T convert(E entity);

    private void createRow(Object data) throws IOException{
        Row row = sheet.createRow(rownum);
        for(int cellnum = 0; cellnum < colcnt; cellnum++){
            createCell(rownum,row,cellnum,data);
        }

        if (isLargeExcel && rownum % 100 == 0) {
            // 每100行，缓存一下，减少内存消耗
            //System.out.println("rownum:"+rownum+"-----------"+System.currentTimeMillis());
            ((SXSSFSheet)sheet).flushRows(100);
        }
        if (rownum % 1000000 == 0) {
            //一百万行，另外创建一个sheet
            sheetIndex++;
            String newSheetName=sheetName+"-"+sheetIndex;
            //System.out.println("rownum:"+rownum+"-----------"+newSheetName);
            sheet=workbook.createSheet(newSheetName);
            createHeader();
        }
        row = null;
    }

    private void createCell(int rownum,Row row,int cellnum,Object data) throws IOException{
        Cell cell = row.createCell(cellnum);
        String fieldName=fields[cellnum];
        Object fieldValue=getFieldData(data,fieldName);
        Object cellValue=fieldValue;
        if ( numberFormats != null && numberFormats.containsKey(fieldName) ){
            cellValue=numberFormats.get(fieldName).format(fieldValue);
        }
        if ( dateFormats != null && dateFormats.containsKey(fieldName) ){
            cellValue=dateFormats.get(fieldName).format(fieldValue);
        }
        if (cellValue!=null){
            cell.setCellValue(cellValue.toString());
        }else{
            cell.setCellValue("");
        }
        cell = null;
    }

    private Object getFieldData(Object data,String fieldName){
        Object ret=data;
        String[] fieldList=fieldName.split("\\.");
        for(String fn:fieldList){
            if (ret==null)
                return ret;

            try {
                ret= BeanUtils.getProperty(ret,fn);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            }

        }
        return ret;
    }

    private void createHeader(){
        rownum=0;
        //输出表头
        Row row = sheet.createRow(rownum);

        Cell cell;
        for (int cellnum=0;cellnum<titles.length;cellnum++ ){
            cell = row.createCell(cellnum);
            cell.setCellValue(titles[cellnum]);
        }
    }

    /**
     * 输出Excel
     * @param response
     * @param fileName
     * @param wb
     */
    public void writeAndClose(HttpServletResponse response, String fileName, Workbook wb) {
        try {
            this.setResponseHeader(response, fileName);
            OutputStream os = response.getOutputStream();
            wb.write(os);
            os.flush();
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置响应头
     * @param response
     * @param fileName
     */
    public void setResponseHeader(HttpServletResponse response, String fileName) {
        try {
            try {
                fileName = new String(fileName.getBytes(), "UTF-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            response.setContentType("application/octet-stream;charset=ISO8859-1");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            response.addHeader("Pargam", "no-cache");
            response.addHeader("Cache-Control", "no-cache");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

}
