/*
 * Copyright (C), 2013-2017, 上海汽车集团股份有限公司
 * FileName: DateUtils.java
 * Author:   caizhongming
 * Date:     2017年3月15日 上午10:00:08
 * Description: //模块目的、功能描述
 * History: //修改记录
 * <author>      <time>      <version>    <desc>
 * 修改人姓名             修改时间            版本号                  描述
 */
package com.yonyou.dmscus.customer.util.common;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * 〈一句话功能简述〉<br>
 * 〈功能详细描述〉 日期工具类
 *
 * <AUTHOR>
 * @see [相关类/方法]（可选）
 * @since [产品/模块版本] （可选）
 */
public class DateUtils {

    /** 日志*/
    private static Logger logger = LoggerFactory.getLogger(DateUtils.class);
    /**
     * 仅显示年月日，例如 2015-08-11.
     */
    public static final String DATE_FORMAT = "yyyy-MM-dd";

    /**默认日期格式为:yyyy-MM-dd HH:mm:ss. */
    public static final String PATTERN_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static final String PATTERN_YYYY_MM_DD_23_59_59 = "yyyy-MM-dd 23:59:59";

    public static final String PATTERN_YYYY_MM_DD_00_00_00 = "yyyy-MM-dd 00:00:00";

    /** 日期格式：yyyy-MM-dd. */
    public static final String PATTERN_YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
    public static final String PATTERN_YYYY_MM_DD_HH = "yyyy-MM-dd HH";

    /** 日期格式：yyyy-MM-dd. */
    public static final String PATTERN_YYYY_MM_DD = "yyyy-MM-dd";

    /** 一年的月日开始，值为:01-01 代表 1月1日. */
    public static final String YEAR_MONTH_DAY_START_SUFFIX = "-01-01";

    /** 一年的月日结束，值为:12-31 代表 1月1日. */
    public static final String YEAR_MONTH_DAY_END_SUFFIX = "-12-31";

    public static final String YYYYMMDD = "yyyyMMdd";
    public static final Integer OUT_MOTH = -18;

    /**
     * 默认构造函数
     */
    public DateUtils() {
        super();
    }


    /**
     * 功能描述:格式化日期函数 <br>
     * 用于将日期转化为字符串日期格式.
     * @param date 日期
     * @param pattern 日期格式
     *   比如：(1)年月日时分秒格式(yyyy-MM-dd HH:mm:ss)
     *       (2)年月日时分格式(yyyy-MM-dd HH:mm)
     *       (3)年月日格式(yyyy-MM-dd)
     * @return 日期字符串
     */
    public static String formateDateToString(Date date, String pattern) {
        String patternTemp = PATTERN_YYYY_MM_DD;
        if (pattern != null && pattern.length() > 0) {
            patternTemp = pattern;
        }
        SimpleDateFormat format = new SimpleDateFormat(patternTemp);
        return format.format(date);
    }

    /**
     * 功能描述:格式化日期函数 <br>
     * 用于将字符串日期转换为日期格式.
     * @param pattern 日期格式
     *   比如：(1)年月日时分秒格式(yyyy-MM-dd HH:mm:ss)
     *       (2)年月日时分格式(yyyy-MM-dd HH:mm)
     *       (3)年月日格式(yyyy-MM-dd)
     * @return 日期
     */
    public static Date parseDateStrToDate(String dataStr, String pattern) {
        try {
            String patternTemp = PATTERN_YYYY_MM_DD;
            if (pattern != null && pattern.length() > 0) {
                patternTemp = pattern;
            }
            SimpleDateFormat format = new SimpleDateFormat(patternTemp);
            return format.parse(dataStr);
        } catch (Exception e) {
            logger.error(">>>字符串转日期格式转换异常={}",e);
        }
        return null;
    }


    /**
     * 功能描述: <br>
     * 〈功能详细描述〉
     * 日期格式转换
     * @param dataStr
     * @param pattern
     * @return
     * @throws Exception
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static Date parse(String dataStr, String pattern) throws Exception {
        if (StringUtils.isBlank(dataStr)) {
            return null;
        }
        String patternTemp = pattern;
        if (StringUtils.isBlank(pattern)) {
            patternTemp = PATTERN_YYYY_MM_DD;
        }
        SimpleDateFormat format = new SimpleDateFormat(patternTemp);
        try {
            return format.parse(dataStr);
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    /**
     * 功能描述: <br>
     * 〈功能详细描述〉当前系统时间往前推N天
     *
     * @param date
     * @param days
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static String getBeforeDateStr(Date date, int days) {
        SimpleDateFormat df = new SimpleDateFormat(PATTERN_YYYY_MM_DD_HH_MM_SS);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - days);
        return df.format(calendar.getTime());
    }

    /**
     * 功能描述: <br>
     * 〈功能详细描述〉
     * 当前系统时间往前推N天
     * @param date
     * @param days
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static Date getBeforeDate(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - days);
        return calendar.getTime();
    }

    /**
     * 功能描述: <br>
     * 〈功能详细描述〉当前系统时间往后推N天
     *
     * @param date
     * @param days
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static String getAfterDateStr(Date date, int days) {
        SimpleDateFormat df = new SimpleDateFormat(PATTERN_YYYY_MM_DD_HH_MM_SS);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) + days);
        return df.format(calendar.getTime());
    }

    /**
     * 功能描述: <br>
     * 〈功能详细描述〉当前系统时间往后推N天
     *
     * @param date
     * @param days
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static Date getAfterDate(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) + days);
        return calendar.getTime();
    }

    /**
     * 功能描述: <br>
     * 〈功能详细描述〉
     * 当前日期后几年
     * @param date
     * @param year
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static Date getAfterYearDate(Date date, int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, year);
        return calendar.getTime();
    }

    /**
     * 功能描述: <br>
     * 〈功能详细描述〉获取日期后几个分钟的日期信息
     * @param date
     * @param minutes
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static String getAfterDateMinute(Date date, int minutes) {
        SimpleDateFormat df = new SimpleDateFormat(PATTERN_YYYY_MM_DD_HH_MM_SS);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MINUTE, calendar.get(Calendar.MINUTE) + minutes);
        return df.format(calendar.getTime());
    }

    /**
     * 计算两个日期之间相差的天数
     *
     * @param smdate 较小的时间
     * @param bdate 较大的时间
     * @return 相差天数
     * @throws ParseException
     */
    public static int daysDateBetween(Date smdate, Date bdate){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            smdate = sdf.parse(sdf.format(smdate));
            bdate = sdf.parse(sdf.format(bdate));
            Calendar cal = Calendar.getInstance();
            cal.setTime(smdate);
            long time1 = cal.getTimeInMillis();
            cal.setTime(bdate);
            long time2 = cal.getTimeInMillis();
            long between_days = (time2 - time1) / (1000 * 3600 * 24);

            return Integer.parseInt(String.valueOf(between_days));
        } catch (Exception e) {
            logger.error(">>>两个日期之间相差的天数转换异常={}",e);
        }
        return -1;
    }
    /**
     * 获取日期时间字符串，默认格式为（yyyy-MM-dd）.
     *
     * @param date    需要转化的日期时间
     * @param pattern 时间格式，例如"yyyy-MM-dd" "HH:mm:ss" "E"等
     * @return String 格式转换后的时间字符串
     * @since 1.0
     */
    public static String formatDate(Date date, Object... pattern) {
        String formatDate = null;
        if (pattern != null && pattern.length > 0) {
            formatDate = DateFormatUtils.format(date, pattern[0].toString());
        } else {
            formatDate = DateFormatUtils.format(date, DateUtils.DATE_FORMAT);
        }
        return formatDate;
    }

    /**
     * 计算两个字符串日期之间相差的天数
     *
     * @param smdate 较小的时间
     * @param bdate 较大的时间
     * @return 相差天数
     * @throws ParseException
     */
    public static int daysStringBetween(String smdate, String bdate) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cal = Calendar.getInstance();
            cal.setTime(sdf.parse(smdate));
            long time1 = cal.getTimeInMillis();
            cal.setTime(sdf.parse(bdate));
            long time2 = cal.getTimeInMillis();
            long between_days = (time2 - time1) / (1000 * 3600 * 24);
            return Integer.parseInt(String.valueOf(between_days));
        }catch (Exception e) {
            logger.error(">>>两个字符串日期之间相差的天数转换异常={}",e);
        }
        return -1;
    }

    /**
     *计算两个字符串日期之间相差的小时(保留一位小数)
     * @param startDate 较小的时间
     * @param endDate 较大的时间
     * @return 相差小时
     * @throws ParseException
     */
    public static Double getDatePoor(Date startDate, Date endDate) throws ParseException {
        //DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - startDate.getTime();
        // 计算差多少天
        //long day = diff / nd;
        // 计算差多少小时
        //long hour = diff % nd / nh;
        // 计算差多少分钟
        //Long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        Long mymint = diff/1000/60;
        int intValue = mymint.intValue();
        BigDecimal b = new BigDecimal((double)intValue/60);
        Double hour = b.setScale(1,BigDecimal.ROUND_DOWN).doubleValue();
        return hour;
    }

    /**
     * 功能描述: <br>
     * 〈功能详细描述〉
     *
     * @param date1 日期1
     * @param date2 日期2
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static boolean isEqualsDate(Date date1,Date date2) {
    	if(date1 != null && date2 != null) {
    		Calendar time1 = Calendar.getInstance();
    		Calendar time2 = Calendar.getInstance();
    		time1.setTime(date1);
    		time2.setTime(date2);
    		if(time1.equals(time2)) {
    			return true;
    		}
    	}
    	return false;
    }
    /**
     * 功能描述: <br>
     * 〈功能详细描述〉
     *
     * @param date1 日期1
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static boolean isEqualsTheSameDay(Date date1) {
        Date date2 = new Date();
        String patternTemp = PATTERN_YYYY_MM_DD;
        SimpleDateFormat format = new SimpleDateFormat(patternTemp);
        try {
            date1 = format.parse(format.format(date1));
            date2 = format.parse(format.format(date2));
        } catch (ParseException e) {
            logger.info("日期转换异常:{}",e.getMessage());
            return false;
        }
        if(date1 != null && date2 != null) {
            Calendar time1 = Calendar.getInstance();
            Calendar time2 = Calendar.getInstance();
            time1.setTime(date1);
            time2.setTime(date2);
            if(time1.equals(time2)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Long 转换 日期
     * @param date
     * @param pattern
     * @return
     */
    public static Date dateTimeFormatter(Long date, String pattern) {
    	try {
    		DateTimeFormatter df= DateTimeFormatter.ofPattern(pattern);
        	return parseDateStrToDate(df.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(date),ZoneId.of("Asia/Shanghai"))),PATTERN_YYYY_MM_DD_HH_MM_SS);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;

    }

    /**
     * 时间戳转date
     * @param timeLong
     * @param pattern
     * @return
     */
    public static Date timeStamp2Date(Long timeLong, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);//要转换的时间格式
        Date date;
        try {
            date = sdf.parse(sdf.format(timeLong));
            return  date;
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * date转字符串
     * @param date
     * @param pattern
     * @return
     */
    public static String dateToString(Date date, String pattern) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat(pattern);
        String time = formatter.format(date);
        return time;
    }
    /**
     * 判断是否当前月或之前
     * @param date
     * @return
     */
    public static Boolean comto(Date date)  {
        Calendar calendar = Calendar.getInstance();
        // 获取当前年
        int year = calendar.get(Calendar.YEAR);
        // 获取当前月
        int month = calendar.get(Calendar.MONTH) + 1;
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date);
        // 获取当前年
        int    year2=  calendar2.get(Calendar.YEAR);
        // 获取当前月
        int    month2=    calendar2.get(Calendar.MONTH)+1;
         return  (year2<year || (year2==year && month2<=month)) ;
    }





    /**
     * 判断是否当前日或之前
     * @param date
     * @return
     */
    public static Boolean comDayto(Date date)  {
        Calendar calendar = Calendar.getInstance();
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date);
        return  calendar2.getTime().compareTo(calendar.getTime())<=0;
    }


    /**
     * 判断是否当前日或之前
     * @param date
     * @return
     */
    public static Boolean comMothtoBetween(Date date)  {
        Calendar calendar = Calendar.getInstance();
        // 获取当前年
        int year = calendar.get(Calendar.YEAR);
        // 获取当前月
        int month = calendar.get(Calendar.MONTH) + 1;
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date);
        // 获取当前年
        int    year2=  calendar2.get(Calendar.YEAR);
        // 获取当前月
        int    month2=    calendar2.get(Calendar.MONTH)+1;
        return   year2 <=year && month2-month<=1  ;
    }

    /**
     * 功能描述: <br>
     * 〈功能详细描述〉当前系统时间往前推N天
     *
     * @param date
     * @param days
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static String getBeforeDateStrParm(Date date, int days,String pattern) {
        SimpleDateFormat df = new SimpleDateFormat(pattern);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - days);
        return df.format(calendar.getTime());
    }
    /**
     * 比较时间前后
     * @param date1
     * @param date2
     * @param pattern
     * @return
     */
    public static Boolean compareTo(String date1 ,String date2,String pattern)  {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            Date date11 = sdf.parse(date1);
            Date date22= sdf.parse(date2);
             return  date11.compareTo(date22) > 0;
        } catch (ParseException e) {
            return false ;
        }
    }

     // true ： date 时间在 deliveryDate 之前
    public static boolean check18(Date deliveryDate, String datatime) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(YYYYMMDD);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime( sdf.parse(datatime));
            calendar.add(Calendar.MONTH, OUT_MOTH);
            Date  date =   calendar.getTime();
            return  date.compareTo(deliveryDate)>0;
    }
    /**
     * 功能描述: <br>
     * 〈功能详细描述〉
     * 当前系统时间往前推N月
     * @param date
     * @param month
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static Date getBeforemonthDate(Date date, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, -month);
        return calendar.getTime();
    }


    /**
     * 功能描述: <br>
     * 〈功能详细描述〉
     * 当前系统时间0点
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static Date getDate() {
    return  new Date(86400000L * (int)(System.currentTimeMillis() / 86400000.0)-1000*60*60*8);
    }
    /**
     * 功能描述: <br>
     * 〈功能详细描述〉
     * 日期格式转换
     * @param pattern
     * @return
     * @throws Exception
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static Date parseDate(Date date, String pattern)  {

        SimpleDateFormat format = new SimpleDateFormat(pattern);

        SimpleDateFormat format1 = new SimpleDateFormat(PATTERN_YYYY_MM_DD_HH);
        try {
            return format1.parse(format.format(date));
        } catch (Exception e) {
            logger.info("预约时间获取失败");
          return  null;
        }
    }
    /**
     * 判断是否当前月或之前
     * @param date
     * @return
     */
    public static Boolean comto(Date date,int moth)  {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, moth);
        Date dd= calendar.getTime();
        return dd.compareTo(new Date())<=0;
    }

    //时间加减小时
    public static String getHourAfter(Date date ,int hour) {

        SimpleDateFormat formatter = new SimpleDateFormat(PATTERN_YYYY_MM_DD_HH_MM_SS);

        Calendar lastDate = Calendar.getInstance();
        lastDate.setTime(date);
        lastDate.set(Calendar.HOUR_OF_DAY, lastDate.get(Calendar.HOUR_OF_DAY) + hour);

        return formatter.format(lastDate.getTime());
    }

    public static Date getDateHourAfter(Date date ,int hour) {

        SimpleDateFormat formatter = new SimpleDateFormat(PATTERN_YYYY_MM_DD_HH_MM_SS);

        Calendar lastDate = Calendar.getInstance();
        lastDate.setTime(date);
        lastDate.set(Calendar.HOUR_OF_DAY, lastDate.get(Calendar.HOUR_OF_DAY) + hour);

        return lastDate.getTime();
    }

    public static Date getdiffDays(Date date ,int hour) {

        Date currentDate = new Date();

        // 将字符串日期解析为Date对象
        Date targetDate = null;

        // 计算日期差
        long diff = currentDate.getTime() - targetDate.getTime();
        long diffDays = diff / (24 * 60 * 60 * 1000);

        if (diffDays > 30) {
            System.out.println("currentDate 减去 2023-10-23 大于30天");
        } else {
            System.out.println("currentDate 减去 2023-10-23 不大于30天");
        }

        return currentDate;
    }

    //计算两个时间差多少分
    public static Long getDateAfter(Date startDate ,Date endDate) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(endDate);

        long ll = calendar1.getTimeInMillis() - calendar.getTimeInMillis();
        ll = ll / (1000*60);

        return ll;
    }

    //比较两个时间大小
    public static Integer dateCompare(String startDate,String endDate) throws ParseException {

        SimpleDateFormat format = new SimpleDateFormat(PATTERN_YYYY_MM_DD_HH_MM_SS);

        return format.parse(startDate).compareTo(format.parse(endDate));
    }

    //转24小时
    public static Integer twentyFour(Date date) {

        return Integer.parseInt(DateUtils.dateToString(date,"HH"));
    }

    //转24小时
    public static Integer twentyFour(Date date,String format){

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);

        return Integer.parseInt(DateUtils.dateToString(date,"HH"));
    }

    public static String parseDate(Date date, int day) {

        SimpleDateFormat format = new SimpleDateFormat(DATE_FORMAT);

        Calendar lastDate = Calendar.getInstance();
        lastDate.setTime(date);
        lastDate.set(Calendar.DATE, lastDate.get(Calendar.DATE) + day);

        return format.format(lastDate.getTime());
    }

    public static Date parseDate(Date date, int day,String pattern) {

        SimpleDateFormat format = new SimpleDateFormat(pattern);

        Calendar lastDate = Calendar.getInstance();
        lastDate.setTime(date);
        lastDate.set(Calendar.DATE, lastDate.get(Calendar.DATE) + day);

        return DateUtils.parseDateStrToDate(format.format(lastDate.getTime()), PATTERN_YYYY_MM_DD_HH_MM_SS);
    }

    public static String dateFormat(Date date, String pattern) {

        if (date == null){
            return "";
        }
        SimpleDateFormat formatter=new SimpleDateFormat(pattern);

        return formatter.format(date);

    }

    public static String parseDate(String date) {

        SimpleDateFormat format = new SimpleDateFormat("EE");


        return format.format( parseDateStrToDate(date,"yyyy-MM-dd"));
    }

    public static void main(String[] args) {
        String s = parseDate("2023-05-17 07:12:59");
        System.out.println(s);
        System.out.println(parseDate("2023-05-17 07:12:59"));
    }
    public static Date lastDayOfYear(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int year = calendar.get(Calendar.YEAR);
        calendar.set(year,11,31,23,59,59);
        return calendar.getTime();
    }

    public static Date firstDayOfYear(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int year = calendar.get(Calendar.YEAR);
        calendar.set(year,1,1,0,0,0);
        return calendar.getTime();
    }
    public static String queryNow() {
        SimpleDateFormat format = new SimpleDateFormat(DATE_FORMAT);
        return format.format(new Date());
    }

    public static String queryBeginTime(String date) {
        String time;
        if (StringUtils.isNullOrEmpty(date)){
            time = queryNow();
        }else {
            SimpleDateFormat format = new SimpleDateFormat(PATTERN_YYYY_MM_DD_00_00_00);
            try {
                time = format.format(format.parse(date));
            } catch (Exception e) {
                logger.info("queryBeginTime转换异常:{}",e.getMessage());
                time = queryNow();
            }
        }
        return time;
    }
    public static String queryEndTime(String date) {
        if (StringUtils.isNullOrEmpty(date)){
            date=queryNow();
        }
        SimpleDateFormat format = new SimpleDateFormat(PATTERN_YYYY_MM_DD_23_59_59);
        try {
            return format.format(format.parse(date));
        } catch (Exception e) {
            logger.info("queryEndTime转换异常:{}",e.getMessage());
        }
        return date;
    }

    public static Date convertLocalDateTimeToDate(LocalDateTime localDateTime) {
        if(localDateTime == null){
            return null;
        }
        // 将 LocalDateTime 转换为 Instant，然后转换为 Date
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
}
