package com.yonyou.dmscus.customer.util.common;

import cn.hutool.core.util.ObjectUtil;
import io.reactivex.Single;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

@SuppressWarnings({ "unchecked", "rawtypes" })
public class Utility {

	private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	public static final String FULL_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

	/**
	 * string转换成int
	 * 
	 * <AUTHOR>
	 * @date 2017年1月19日
	 * @param index
	 * @return
	 * @throws Exception
	 */
	public static int getInt(String index) throws Exception {
		int iRtn;
		if (index == null || index.trim().equals("")) {
			iRtn = 0;
			return iRtn;
		}
		return Integer.parseInt(index);
	}

	/**
	 * string转换成timestamp
	 * 
	 * <AUTHOR>
	 * @date 2017年1月19日
	 * @param index
	 * @return
	 * @throws Exception
	 */
	public static Timestamp getTimeStamp(String index) throws Exception {
		if (index == null || index.trim().equals("")) {
			return null;
		} else if (index.length() == 10) {
			index += " 00:00:00.000000000";
		} else if (index.length() == 19) {
			index += ".000000000";
		}
		return Timestamp.valueOf(index);
	}

	/**
	 * 定义1为mysql数据库
	 */
	public static final int MySql_TYPE = 1;

	/**
	 * String转换成double
	 * 
	 * @param index
	 * @return
	 */
	public static double getDouble(String index) {
		double dRtn;
		if (index == null || index.trim().equals("")) {
			dRtn = 0;
			return dRtn;
		}
		return Double.valueOf(index);

	}

	/**
	 * 判断是否为空或NULL
	 * 
	 * @param
	 * @return
	 * @throws Exception
	 */
	public static boolean testString(String src) {
		if (src != null && !"".equals(src.trim()))
			return true;
		return false;
	}

	public static Date getCurrentDateTime() throws ParseException {
		return new Date();
	}

	/**
	 * string转换成date
	 *
	 * @param
	 * @return
	 * @throws Exception
	 */
	public static Date parseString2Date(String dateStr, String dateFormat) throws ParseException {

		if (null == dateStr || dateStr.length() <= 0)
			return null;

		if (null == dateFormat || dateFormat.length() <= 0)
			dateFormat = FULL_DATE_FORMAT;

		DateFormat formatter = new SimpleDateFormat(dateFormat);
		Date date = formatter.parse(dateStr);
		return date;
	}

	/**
	 * 说明：其他特殊情况
	 *
	 * @param db2sqlStr
	 *            DB2 sql
	 * @param oracleSqlStr
	 *            ORACLE sql
	 * @return String
	 */
	public static String gerSpecialSql(String db2sqlStr, String oracleSqlStr) {

		// 值为1 时，为MySql数据库
		if (MySql_TYPE == 1) {
			return db2sqlStr;
		} else {
			return oracleSqlStr;
		}

	}

	/**
	 * string转换成long
	 *
	 * @param index
	 * @return
	 * @throws Exception
	 */
	public static Long getLong(String index) throws Exception {
		Long lRtn;
		if (index == null || index.trim().equals("")) {
			lRtn = Long.valueOf(0);
			return lRtn;
		}
		return Long.valueOf(index);
	}

	/**
	 * 字段过滤，只保留纯数字类型，若小于零则为零
	 *
	 * @param
	 * @return
	 * @throws Exception
	 */
	public static String StringFilter(String str) throws Exception {
		String num = str.replaceAll("[^0-9]", "");
		if (Utility.testString(num) && Utility.getLong(num) < 0) {
			return "0";
		} else {
			return num.trim();
		}
	}

	public static Timestamp getCurrentTimestamp() throws Exception {
		return new Timestamp(System.currentTimeMillis());

	}

	/**
	 * 获取当前年份
	 *
	 * @return
	 */
	public static String getYear() {
		Calendar calendar = new GregorianCalendar();
		int year = calendar.get(Calendar.YEAR);
		return Integer.toString(year);
	}

	/**
	 * Method 得到当前月(两位数)
	 *
	 * @return String
	 */
	public static String getMonth() {
		Calendar calendar = new GregorianCalendar();
		int month = calendar.get(Calendar.MONTH) + 1;
		String sRtn = String.valueOf(month);
		if (String.valueOf(month).length() == 1) {
			sRtn = "0" + sRtn;
		}
		return sRtn;
	}

	/**
	 * 获取当前日期
	 * 
	 * @return
	 */
	public static String getDate() {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
		Date now = new Date();
		String res = simpleDateFormat.format(now);
		return res;
	}

	/**
	 * 获取当前日期的前一天
	 */
	public static String getBeforeDate(String time) {
		LocalDate localDate = LocalDate.now();
		if(ObjectUtil.isNotEmpty(time)){
			localDate = LocalDate.parse(time);
		}
		// 获取前一天的日期
		localDate = localDate.minusDays(1);
		return localDate.toString();
	}

	/**
	 * 获取当前日期前的指定天
	 */
	public static String getBeforeDateSpecified(String time,Integer offset) {
		LocalDate localDate = LocalDate.now();
		if(ObjectUtil.isNotEmpty(time)){
			localDate = LocalDate.parse(time);
		}
		// 获取前指定天的日期
		localDate = localDate.minusDays(offset);
		return localDate.toString();
	}

	/**
	 * 获取日期的前num月
	 */
	public static String getBeforeMonth(String time, int month) {
		LocalDate localDate = LocalDate.now();
		if(ObjectUtil.isNotEmpty(time)){
			localDate = LocalDate.parse(time);
		}
		// 获取前一天的日期
		localDate = localDate.minusMonths(month);
		return localDate.toString();
	}

	public static String getBeforeDate() {
		return getBeforeDate(null);
	}

	/**
	 * Method 得到当前月（一位数）
	 *
	 * @return int
	 */
	public static int getMonthNoZero() {
		Calendar calendar = new GregorianCalendar();
		int month = calendar.get(Calendar.MONTH) + 1;
		return month;
	}

	// 检查非空
	public static String checkNull(Object obj) {
		if (obj != null)
			return obj.toString().trim();
		else
			return "";
	}

	/**
	 * 将时间戳转换为日期格式
	 * 
	 * <AUTHOR>
	 * @date 2017年5月4日
	 * @param s
	 * @return
	 */
	public static String stampToDate(String s) {
		String res;
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		long lt = Long.parseLong(s);
		Date date = new Date(lt);
		res = simpleDateFormat.format(date);
		return res;

	}

	/**
	 * 获取税率(临时使用)
	 * 
	 * @return
	 */
	public static String getVat() {
		return "13%";
	}
}
