/*
 * Copyright (C), 2020-2020, 上海用友汽车有限公司
 * FileName: ImportExcelUtil.java
 * Author:   caizhongming
 * Date:     2020年5月21日 下午6:38:13
 * Description: //模块目的、功能描述      
 * History: //修改记录
 * <author>      <time>      <version>    <desc>
 * 修改人姓名             修改时间            版本号                  描述
 */
package com.yonyou.dmscus.customer.util.common;

import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * 〈一句话功能简述〉<br>
 * 〈功能详细描述〉
 *
 * <AUTHOR>
 * @see [相关类/方法]（可选）
 * @since [产品/模块版本] （可选）
 */
public class ImportExcelUtil implements Serializable {

	/**
	 * 生成序列
	 */
	private static final long serialVersionUID = -4143322736311269612L;

	/**
	 * 
	 * 功能描述: <br>
	 * 读取Excel表单中的数据
	 * 
	 * @param templateId  模板编号
	 * @param inputStream 导入的excel文件输入流
	 * @return 所有表单数据
	 * @throws IOException Excel读取异常
	 * @see [相关类/方法](可选)
	 * @since [产品/模块版本](可选)
	 */
	public static List<Map<Integer, String>> getImportExcelData(InputStream inputStream, String fileName)
			throws IOException {

		// Excel工作薄
		Workbook workbook = null;
		// Excel表单
		Sheet sheet = null;
		// Excel行
		Row row = null;
		// 表单中的一行数据
		Map<Integer, String> dataMap = null;
		// 表单中的所有数据
		List<Map<Integer, String>> dataList = null;

		// 获取Excel工作簿对象
		if ("xls".equalsIgnoreCase(fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length()))) {
			workbook = new HSSFWorkbook(inputStream);
		} else {
			workbook = new XSSFWorkbook(inputStream);
		}

		// 获取表单对象
		sheet = workbook.getSheetAt(0);
		// Excel行中是否是有效数据行
		boolean isValidRow = false;

		/**
		 * 从表单第二行开始读取数据，读取整个表单中的数据
		 */
		dataList = new ArrayList<Map<Integer, String>>();
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			// 初始化是否有效数据行：非有效数据行
			isValidRow = false;

			row = sheet.getRow(i);
			if (row != null) {
				dataMap = new HashMap<Integer, String>();
				/**
				 * 根据模板表头长度,读取一行数据
				 */
				for (int j = 0; j < sheet.getRow(0).getPhysicalNumberOfCells(); j++) {
					if (row.getCell(j) == null) {
						dataMap.put(j, "");
					} else {
						String parseExcel = parseExcel(row.getCell(j)).trim();
						if (!StringUtils.isBlank(parseExcel)) {
							dataMap.put(j, parseExcel);
							isValidRow = true;
						} else {
							dataMap.put(j, "");
						}
					}
				}

				/**
				 * 读取完一条记录，如果是有效数据行，则加入返回结果中
				 */
				if (isValidRow) {
					dataList.add(dataMap);
				}
			}
		}

		return dataList;

	}

	private static String parseExcel(Cell cell) {
		String result = "";
		switch (cell.getCellType()) {
		case HSSFCell.CELL_TYPE_NUMERIC:// 数字类型
			if (HSSFDateUtil.isCellDateFormatted(cell)) {// 处理日期格式、时间格式
				SimpleDateFormat sdf = null;
				if (cell.getCellStyle().getDataFormat() == HSSFDataFormat.getBuiltinFormat("h:mm")) {
					sdf = new SimpleDateFormat("HH:mm");
				} else {// 日期
					sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				}
				Date date = cell.getDateCellValue();
				result = sdf.format(date);
			} else if (cell.getCellStyle().getDataFormat() == 58) {
				// 处理自定义日期格式：m月d日(通过判断单元格的格式id解决，id的值是58)
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				double value = cell.getNumericCellValue();
				Date date = org.apache.poi.ss.usermodel.DateUtil.getJavaDate(value);
				result = sdf.format(date);
			} else {
				cell.setCellType(XSSFCell.CELL_TYPE_STRING);
				result = cell.getStringCellValue().toString();
			}
			break;
		case HSSFCell.CELL_TYPE_STRING:// String类型
			result = cell.getRichStringCellValue().toString();
			break;
		case HSSFCell.CELL_TYPE_BLANK:
			result = "";
			break;
		default:
			result = "";
			break;
		}
		return result;
	}
}
