package com.yonyou.dmscus.customer.util.common;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.yonyou.dmscloud.framework.util.bean.JavaBeanUtil;
import com.yonyou.dmscloud.function.exception.UtilException;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.lang.reflect.Method;
import java.util.*;

/**
 * Bean 映射转换
 * <AUTHOR>
 * @data 2020/5/12 17:11
 */
public class BeanUtils extends org.apache.commons.collections.MapUtils{
    // 定义日志接口
    private static final Logger logger = LoggerFactory.getLogger(BeanUtils.class);

    private static String errorMsg = "Map 转换对象失败";
    private static String errorMsg2 = "对象转换MAP失败";


   

    
    
    

    /**
     * 查询null 值，不进行copy 操作
     *
     * @param source
     * @return java.lang.String[]
     * @throws
     * <AUTHOR>
     * @since 2018/7/24 0024
     */
    private static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    
    /**
     * 将map 转换成对象
     *
     * @param clazz
     * @param map
     * @return T
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public static <T, V> T toObject(Class<T> clazz, Map<String, V> map) {
        try {
            T object = clazz.getDeclaredConstructor().newInstance();
            return toObject(object, map);
        } catch (Exception e) {
            throw new UtilException(errorMsg, e);
        }
    }

    /**
     * 将map 转换成对象
     *
     * @param clazz
     * @param map
     * @param toCamelCase
     * @return T
     * @throws
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public static <T, V> T toObject(Class<T> clazz, Map<String, V> map, boolean toCamelCase) {
        try {
            T object = clazz.getDeclaredConstructor().newInstance();
            return toObject(object, map, toCamelCase);
        } catch (Exception e) {
            throw new UtilException(errorMsg, e);
        }

    }

    /**
     * 将map 转换成对象
     *
     * @param object
     * @param map
     * @return T
     * @throws
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public static <T, V> T toObject(T object, Map<String, V> map) {
        try {
            org.apache.commons.beanutils.BeanUtils.populate(object, map);
            return object;
        } catch (Exception e) {
            throw new UtilException(errorMsg, e);
        }

    }

    /**
     * 将map 转换成对象,将完成驼峰式转换
     *
     * @param object
     * @param map
     * @param toCamelCase
     * @return T
     * @throws
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public static <T, V> T toObject(T object, Map<String, V> map, boolean toCamelCase) {
        try {
            if (toCamelCase) {
                map = toCamelCaseMap(map);
            }
            org.apache.commons.beanutils.BeanUtils.populate(object, map);
            return object;
        } catch (Exception e) {
            throw new UtilException(errorMsg, e);
        }
    }

    /**
     * 将对象转换成MAP
     *
     * @param obj
     * @return java.util.Map<java.lang.String
            *       ,
            *       java.lang
            *       .
            *                                                                                                                                                                                                                                                               <                                                                                                                               p>
     * String>
     * @throws
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public static Map<String, Object> toMap(Object obj) {
        try {
            if (obj == null) {
                return null;
            }

            Map<String, Object> map = new HashMap();
            BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor property : propertyDescriptors) {
                String key = property.getName();
                if (key.compareToIgnoreCase("class") == 0) {
                    continue;
                }
                Method getter = property.getReadMethod();
                Object value = getter != null ? getter.invoke(obj) : null;
                map.put(key, value);
            }
            return map;
        } catch (Exception e) {
            throw new UtilException(errorMsg2, e);
        }

    }

    /**
     * 转换成Map并提供字段命名驼峰转平行
     *
     * @param object
     * @return java.util.Map<java.lang.String
            *       ,
            *       java.lang
            *       .
            *                                                                                                                                                                                                                                                               String>
     * @throws
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public static Map<String, Object> toMapForFlat(Object object) {
        try {
            Map<String, Object> map = toMap(object);
            return toUnderlineStringMap(map);
        } catch (Exception e) {
            throw new UtilException(errorMsg2, e);
        }
    }

    /**
     * 对对象集合转换成MAP 集合
     *
     * @param collection
     * @return java.util.Collection<java.util.Map
            *       <   p>
     * <
     * java
     * .       lang.String
     * ,
     * java.lang.String>>
     * @throws
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public static <T> Collection<Map<String, Object>> toMapList(Collection<T> collection) {
        List<Map<String, Object>> retList = new ArrayList();
        if (collection != null && !collection.isEmpty()) {
            for (Object object : collection) {
                Map<String, Object> map = toMap(object);
                retList.add(map);
            }
        }
        return retList;
    }

    /**
     * 转换成驼峰格式
     *
     * @param map
     * @return java.util.Map<java.lang.String
            *       <   p>
     * ,
     * V>
     * @throws
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public static <V> Map<String, V> toCamelCaseMap(Map<String, V> map) {
        Map<String, V> newMap = new HashMap();
        for (Map.Entry<String, V> entry : map.entrySet()) {
            safeAddToMap(newMap, JavaBeanUtil.toCamelCaseString(entry.getKey()), entry.getValue());
        }
        return newMap;
    }

    /**
     * 将Map的Keys转译成下划线格式的<br>
     *
     * @param map 需要转换的map
     * @return java.util.Map<java.lang.String
            *       ,
            *       V>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    public static <V> Map<String, V> toUnderlineStringMap(Map<String, V> map) {
        Map<String, V> newMap = new HashMap();
        for (Map.Entry<String, V> entry : map.entrySet()) {
            newMap.put(JavaBeanUtil.toUnderlineString(entry.getKey()), entry.getValue());
        }
        return newMap;
    }


    /**
     * 转换为Collection,同时为字段做驼峰转换<Map<K, V>>
     *
     * @param collection 需要转换的集合
     * @return java.util.Collection<java.util.Map
            *       <
            *       java
            *       .
            *       lang.String
            *       ,
            *       java.lang.String>>
     * <AUTHOR>
     * @since 2018/7/22 0022
     */
    @SuppressWarnings("unchecked")
    public static <T> Collection<Map<String, Object>> toMapListForFlat(Collection<T> collection) {
        List<Map<String, Object>> retList = new ArrayList();
        if (collection != null && !collection.isEmpty()) {
            for (Object object : collection) {
                Map<String, Object> map = toMapForFlat(object);
                retList.add(map);
            }
        }
        return retList;
    }
    
    /**
     * 功能描述: <br>
     * 〈功能详细描述〉 获取详细异常信息
     * add by caizhongming 2020-06-19
     * @param e
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static String getExceptionMsg(Throwable e) {
        Writer result = new StringWriter();  
        PrintWriter printWriter = new PrintWriter(result);
        if(e != null){
            e.printStackTrace(printWriter);
        }
        return result.toString();
    }
    
    /**
	 * 功能描述: <br>
	 * 〈功能详细描述〉
	 * 转换JSON字符串  add by caizhongming 2020-06-29
	 * @param object
	 * @return
	 * @see [相关类/方法](可选)
	 * @since [产品/模块版本](可选)
	 */
	public static String toJSONString(Object object) {

		ValueFilter valueFilter = new ValueFilter() {
			@Override
			public Object process(Object object, String name, Object value) {
				if (value == null) {
					return "";
				}

				return value;
			}
		};

		String json = JSONObject.toJSONString(object, valueFilter, SerializerFeature.PrettyFormat,
				SerializerFeature.WriteDateUseDateFormat, SerializerFeature.IgnoreNonFieldGetter,
				SerializerFeature.WriteBigDecimalAsPlain);

		return json;
	}
}
