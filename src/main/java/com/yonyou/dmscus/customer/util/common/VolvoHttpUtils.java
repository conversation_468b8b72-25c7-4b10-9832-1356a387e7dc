package com.yonyou.dmscus.customer.util.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Component
public class VolvoHttpUtils {

    public HttpHeaders getHeaders() {
        HttpHeaders headers= new HttpHeaders();

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        headers.setContentType(MediaType.APPLICATION_JSON);
        if(!headers.containsKey(HttpHeaders.AUTHORIZATION)){
            headers.set(HttpHeaders.AUTHORIZATION, request.getHeader(HttpHeaders.AUTHORIZATION));
        }
        log.info("VolvoHttpUtils1 Authorization:{}",request.getHeader(HttpHeaders.AUTHORIZATION));
//        headers.set(HttpHeaders.AUTHORIZATION, "Bearer LHtaVXNFu60q_gDAkwHQXa9h0zZYeGyXBvVG25bagbw.ZGWFTsWHf7V40N-ctdu3qE71O6BgfhhiBbPA3sYidjo");
        return headers;
    }

}
