package com.yonyou.dmscus.customer.util;

import com.yonyou.dmscus.customer.util.common.StringUtils;

public class SensitiveUtils {
    public SensitiveUtils() {
    }

    public static String allReplace(String str) {
        return org.apache.commons.lang3.StringUtils.isBlank(str) ? "" : org.apache.commons.lang3.StringUtils.repeat("*", str.length());
    }

    public static String idCard(String str) {
        if (org.apache.commons.lang3.StringUtils.isBlank(str)) {
            return "";
        } else {
            String leftStr = org.apache.commons.lang3.StringUtils.left(str, 4);
            return replace(leftStr, str);
        }
    }

    public static String replace(String str1, String str2) {
        return org.apache.commons.lang3.StringUtils.rightPad(str1, org.apache.commons.lang3.StringUtils.length(str2), "*");
    }

    public static String email(String email){
        return allReplace(email);
    }

    public static String mobile(String mobile) {
        return mobileEncrypt(mobile);
    }

    public static String mobileEncrypt(String mobile) {
        if (StringUtils.isNullOrEmpty(mobile)) {
            return mobile;
        } else {
            String cn = "";
            if (mobile.length() > 11) {
                cn = mobile.substring(0, 3);
                boolean flag = false;
                if ("+86".equals(cn)) {
                    mobile = mobile.substring(3);
                    flag = true;
                }

                if (!flag) {
                    cn = mobile.substring(0, 2);
                    if ("86".equals(cn)) {
                        mobile = mobile.substring(2);
                    }
                }
            }

            return mobile.length() == 11 ? cn + mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2") : SensitiveUtils.allReplace(mobile);
        }
    }
}