package com.yonyou.dmscus.customer.util;

import java.util.Map;
import java.util.Objects;

public class HeaderUtil {

    private HeaderUtil() {
    }

    private static ThreadLocal<Map<String,String>> threadLocal = new ThreadLocal<>();

    public static Map<String,String> getAll(){
        return threadLocal.get();
    }

    public static String get(String key){
        Map<String,String> headerMap = threadLocal.get();
        return Objects.nonNull(headerMap) ? headerMap.get(key) : null;
    }

    public static void set(Map<String,String> header){
        threadLocal.set(header);
    }

    public static void remove() {
        threadLocal.remove();
    }

}
