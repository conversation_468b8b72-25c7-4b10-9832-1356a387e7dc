package com.yonyou.dmscus.customer.utils.utExampled.dtoe;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * 费用结算
 * <AUTHOR>
 *
 */
@SuppressWarnings("rawtypes")
@ApiModel(value = "", description = "")
public class UtHDTO {
	// 接收结算单表的参数


	@ApiModelProperty(value = "出厂里程",name = "outMileage")
	private Double utoutMileage;

	@ApiModelProperty(value = "是否自动交车",name = "automaticDelivery")
	private String utautomaticDelivery;

	@ApiModelProperty(value = "上次保养日期",name = "lastMaintenanceDate")
	private String utlastMaintenanceDate;

	@ApiModelProperty(value = "上次保养里程",name = "lastmaintenancemileage")
	private String utlastmaintenancemileage;

	@ApiModelProperty(value = "是否洗车",name = "isWash")
	private String utisWash;

	@ApiModelProperty(value="标识 ",required = false)
	private String  utrowkey;

	@ApiModelProperty(value="优惠前总金额 ",required = false)
	private Double   utsumFrontEtotalAmount;

	@ApiModelProperty(value="店内优惠金额 ",required = false)
	private Double  utsumDiscountAmount;

	@ApiModelProperty(value="厂家优惠金额 ",required = false)
	private Double  utsumManufacturersAmount;

	@ApiModelProperty(value="合计优惠金额 ",required = false)
	private Double  utsumDiscountAmountPrice;

    @ApiModelProperty(value="去零金额 ",required = false)
    private Double  utsumSubObbAmount;

	@ApiModelProperty(value="总应收账款 ",required = false)
	private Double  utsumReceiveablEtotalAmount;

	@ApiModelProperty(value="税率 ",required = false)
	private Double  utsumTaxs;

	@ApiModelProperty(value="税额 ",required = false)
	private Double  utsumTaxAmount;

	@ApiModelProperty(value="不含税金额 ",required = false)
	private Double  utsumNetAmount;

	@ApiModelProperty(value="自费总金额 ",required = false)
	private Double  utsumPrice;

	@ApiModelProperty(value="保修成本",required = false)
	private Double  utsumWarrantyCosts;

	@ApiModelProperty(value="结算单号",required = false)
	private String  utbalanceNo;

	@ApiModelProperty(value="圆整金额",required = false)
	private Double  utyzAmaount;


	@ApiModelProperty(value = "希望联系方式", required = false)
	private String uthopeContact;//HOPE_CONTACT  希望联系方式
	@ApiModelProperty(value = "合同证书", required = false)
	private String utcontractCard;//CONTRACT_CARD   合同证书
	@ApiModelProperty(value = "工时费", required = false)
	private String utlabourAmount;//LABOUR_AMOUNT  工时费
	@ApiModelProperty(value = "减免金额", required = false)
	private String utderateAmount;//DERATE_AMOUNT  减免金额
	@ApiModelProperty(value = "销售材料费", required = false)
	private String utsalesPartAmount;//SALES_PART_AMOUNT  销售材料费
	@ApiModelProperty(value = "附加项目费", required = false)
	private String utaddItemAmount;//ADD_ITEM_AMOUNT  附加项目费
	@ApiModelProperty(value = "付款方式代码", required = false)
	private String utpayTypeCode;//PAY_TYPE_CODE  付款方式代码
	@ApiModelProperty(value = "辅料管理费", required = false)
	private String utoverItemAmount;//OVER_ITEM_AMOUNT  辅料管理费
	@ApiModelProperty(value = "收款金额", required = false)
	private String utreceiveAmount;//RECEIVE_AMOUNT  收款金额
	@ApiModelProperty(value = "计算收款金额", required = false)
	private String utcalcReceiveAmount;
	@ApiModelProperty(value = "维修材料费", required = false)
	private String utrepairPartAmount;//REPAIR_PART_AMOUNT  维修材料费
	@ApiModelProperty(value = "税率", required = false)
	private String uttax;//TAX  税率
	@ApiModelProperty(value = "税额", required = false)
	private String uttaxAmountBalance;//TAX_AMOUNT   税额
	@ApiModelProperty(value = "不含税金额", required = false)
	private String utnetAmountBalance;//NET_AMOUNT    不含税金额
	@ApiModelProperty(value = "自费总金额", required = false)
	private String utownAmount;//OWN_AMOUNT    自费总金额
	@ApiModelProperty(value = "发票编号", required = false)
	private String utinvoiceNo;//INVOICE_NO  发票编号OWN_AMOUNT
	@ApiModelProperty(value = "配件销售单号", required = false)
	private String utsalesPartNo;//SALES_PART_NO   配件销售单号
	@ApiModelProperty(value = "人工成本", required = false)
	private String utlabourCost;//LABOUR_COST  人工成本
	@ApiModelProperty(value = "发票类型代码", required = false)
	private String utinvoiceTypeCode;//INVOICE_TYPE_CODE  发票类型代码
	@ApiModelProperty(value = "销售材料成本", required = false)
	private String utsalesPartCost;//SALES_PART_COST  销售材料成本
	@ApiModelProperty(value = "优惠模式代码", required = false)
	private String utdiscountModeCode;//DISCOUNT_MODE_CODE  优惠模式代码
	@ApiModelProperty(value = "维修材料成本", required = false)
	private String utrepairPartCost;//REPAIR_PART_COST  维修材料成本
	@ApiModelProperty(value = "工单号", required = false)
	private String utroNo;//RO_NO  工单号
	@ApiModelProperty(value = "总金额", required = false)
	private String utallAmount;// TOTAL_AMOUNT  总金额
	@ApiModelProperty(value = "vin", required = false)
	private String utvin;
	@ApiModelProperty(value = "去零金额", required = false)
	private String utsubObbAmount;//SUB_OBB_AMOUNT 去零金额
	@ApiModelProperty(value = "礼券抵扣金额", required = false)
	private String utgiftAmount;//GIFT_AMOUNT  礼券抵扣金额
	@ApiModelProperty(value = "汇总金额", required = false)
	private String utsumAmount;//SUM_AMOUNT  汇总金额
	@ApiModelProperty(value = "是否结清", required = false)
	private String utpayOff;//PAY_OFF  是否结清
	@ApiModelProperty(value = "是否欠款结算", required = false)
	private String utarrBalance;//ARR_BALANCE  是否欠款结算
	@ApiModelProperty(value = "担保方", required = false)
	private String utguarantor;//GUARANTOR  担保方
    @ApiModelProperty(value = "保险公司代码", required = false)
	private String utinsurationCode;//INSURATION_CODE  保险公司代码
    @ApiModelProperty(value = "理赔单号", required = false)
	private String utinsurationNo;//INSURATION_NO  理赔单号
    @ApiModelProperty(value = "电子礼券账户", required = false)
	private String utaccId;// ACC_ID  电子礼券账户
    @ApiModelProperty(value = "备注", required = false)
	private String utremarkS;//REMARK  备注
    @ApiModelProperty(value = "备注1", required = false)
	private String utremark1Balance;//REMARK1 备注1
    @ApiModelProperty(value = "送车服务", required = false)
	private String utisDeliver;//送车服务
    @ApiModelProperty(value = "车主", required = false)
	private String utowner;
	@ApiModelProperty(value = "车主代码", required = false)
	private String utownerNo;
	@ApiModelProperty(value = "客户代码", required = false)
	private String utcustomerCode;
	@ApiModelProperty(value = "客户名称", required = false)
	private String utcustomerName;
	@ApiModelProperty(value = "三日电访时间", required = false)
	private String uttraceTime;
	@ApiModelProperty(value = "希望联络方式", required = false)
	private String utdelivererHobbyContact;
	@ApiModelProperty(value = "索赔成本", required = false)
	private String utclaimCost;
	@ApiModelProperty(value = "结算单维修项目明细参数", required = false)
	private List<Map> utbLDtoList;//结算单维修项目明细参数
	@ApiModelProperty(value = "结算单维修项目收费对象参数", required = false)
	private List<Map> uthiddenList1;//结算单维修项目收费对象参数
	@ApiModelProperty(value = "结算单维修材料明细参数", required = false)
	private List<Map> utbRPDtoList;//结算单维修材料明细参数
	@ApiModelProperty(value = "结算单维修材料收费对象参数", required = false)
	private List<Map> uthiddenList2;//结算单维修材料收费对象参数
	@ApiModelProperty(value = "结算单销售材料明细参数", required = false)
	private List<Map> utbSPDtoList;//结算单销售材料明细参数
	@ApiModelProperty(value = "结算单销售材料收费对象参数", required = false)
	private List<Map> uthiddenList3;
	@ApiModelProperty(value = "结算单附加项目明细参数", required = false)
	private List<Map> utbAIDtoList;//结算单附加项目明细参数
	@ApiModelProperty(value = "附加项目收费对象参数", required = false)
	private List<Map> uthiddenList4;//附加项目收费对象参数
	@ApiModelProperty(value = "应收", required = false)
	private List<Map> utreceivableList;//应收
	@ApiModelProperty(value = "实收", required = false)
	private List<Map> utreceivedList;//实收
	@ApiModelProperty(value = "其他成本", required = false)
	private List<Map> uthiddenList5;//其他成本
	@ApiModelProperty(value = "优惠券", required = false)
	private List<Map> utdmsCoupon;//优惠券
	@ApiModelProperty(value = "发票登记信息", required = false)
	private List<Map> utinvoiceList;

	@ApiModelProperty(value = "优惠信息", required = false)
	private List<Map> utpreferential;

	@ApiModelProperty(value = "支付方式", required = false)
	private Integer utpayModel;

	private String utdiscountAmount;

	private String utusingStore;//USING_STORE 本次使用储值
	private String utusingPrePay;//本次使用余额
	private String utactivitySpecialFund;//专项资金余额
	private String utcheckNo;//支票号码
	private String utbillNo;//发票号码
	private String utInvoiceTag;//已开票
	private String utremarkdetail;//明细备注
	private String utpaymentObjectCode;//结算单收费对象
	private String utpaymentObjectName;
	private String utserviceAdvisor;//服务工程师
	private String uthandler;//经手人
	private String utreceiveDate;//收款日期
	private String utwriteoffTag;//销账标志
	private String utisAllInvoice;//是否全部开票
	private String utprePay;//可用余款
	private List<Map> utpaymentObjectList;//结算信息表
	private String utisBalancePrint;
	private String utisPad; // 是否pad
	private String utnextMaintainDate; // 建议下次保养日期
	private String utnextMaintainMileage; // 建议下次保养里程
	private String utnoTraceReason; // 不回访原因
	public Double getUtoutMileage() {
		return utoutMileage;
	}
	public void setUtoutMileage(Double utoutMileage) {
		this.utoutMileage = utoutMileage;
	}
	public String getUtautomaticDelivery() {
		return utautomaticDelivery;
	}
	public void setUtautomaticDelivery(String utautomaticDelivery) {
		this.utautomaticDelivery = utautomaticDelivery;
	}
	public String getUtlastMaintenanceDate() {
		return utlastMaintenanceDate;
	}
	public void setUtlastMaintenanceDate(String utlastMaintenanceDate) {
		this.utlastMaintenanceDate = utlastMaintenanceDate;
	}
	public String getUtlastmaintenancemileage() {
		return utlastmaintenancemileage;
	}
	public void setUtlastmaintenancemileage(String utlastmaintenancemileage) {
		this.utlastmaintenancemileage = utlastmaintenancemileage;
	}
	public String getUtisWash() {
		return utisWash;
	}
	public void setUtisWash(String utisWash) {
		this.utisWash = utisWash;
	}
	public String getUtrowkey() {
		return utrowkey;
	}
	public void setUtrowkey(String utrowkey) {
		this.utrowkey = utrowkey;
	}
	public Double getUtsumFrontEtotalAmount() {
		return utsumFrontEtotalAmount;
	}
	public void setUtsumFrontEtotalAmount(Double utsumFrontEtotalAmount) {
		this.utsumFrontEtotalAmount = utsumFrontEtotalAmount;
	}
	public Double getUtsumDiscountAmount() {
		return utsumDiscountAmount;
	}
	public void setUtsumDiscountAmount(Double utsumDiscountAmount) {
		this.utsumDiscountAmount = utsumDiscountAmount;
	}
	public Double getUtsumManufacturersAmount() {
		return utsumManufacturersAmount;
	}
	public void setUtsumManufacturersAmount(Double utsumManufacturersAmount) {
		this.utsumManufacturersAmount = utsumManufacturersAmount;
	}
	public Double getUtsumDiscountAmountPrice() {
		return utsumDiscountAmountPrice;
	}
	public void setUtsumDiscountAmountPrice(Double utsumDiscountAmountPrice) {
		this.utsumDiscountAmountPrice = utsumDiscountAmountPrice;
	}
	public Double getUtsumSubObbAmount() {
		return utsumSubObbAmount;
	}
	public void setUtsumSubObbAmount(Double utsumSubObbAmount) {
		this.utsumSubObbAmount = utsumSubObbAmount;
	}
	public Double getUtsumReceiveablEtotalAmount() {
		return utsumReceiveablEtotalAmount;
	}
	public void setUtsumReceiveablEtotalAmount(Double utsumReceiveablEtotalAmount) {
		this.utsumReceiveablEtotalAmount = utsumReceiveablEtotalAmount;
	}
	public Double getUtsumTaxs() {
		return utsumTaxs;
	}
	public void setUtsumTaxs(Double utsumTaxs) {
		this.utsumTaxs = utsumTaxs;
	}
	public Double getUtsumTaxAmount() {
		return utsumTaxAmount;
	}
	public void setUtsumTaxAmount(Double utsumTaxAmount) {
		this.utsumTaxAmount = utsumTaxAmount;
	}
	public Double getUtsumNetAmount() {
		return utsumNetAmount;
	}
	public void setUtsumNetAmount(Double utsumNetAmount) {
		this.utsumNetAmount = utsumNetAmount;
	}
	public Double getUtsumPrice() {
		return utsumPrice;
	}
	public void setUtsumPrice(Double utsumPrice) {
		this.utsumPrice = utsumPrice;
	}
	public Double getUtsumWarrantyCosts() {
		return utsumWarrantyCosts;
	}
	public void setUtsumWarrantyCosts(Double utsumWarrantyCosts) {
		this.utsumWarrantyCosts = utsumWarrantyCosts;
	}
	public String getUtbalanceNo() {
		return utbalanceNo;
	}
	public void setUtbalanceNo(String utbalanceNo) {
		this.utbalanceNo = utbalanceNo;
	}
	public Double getUtyzAmaount() {
		return utyzAmaount;
	}
	public void setUtyzAmaount(Double utyzAmaount) {
		this.utyzAmaount = utyzAmaount;
	}
	public String getUthopeContact() {
		return uthopeContact;
	}
	public void setUthopeContact(String uthopeContact) {
		this.uthopeContact = uthopeContact;
	}
	public String getUtcontractCard() {
		return utcontractCard;
	}
	public void setUtcontractCard(String utcontractCard) {
		this.utcontractCard = utcontractCard;
	}
	public String getUtlabourAmount() {
		return utlabourAmount;
	}
	public void setUtlabourAmount(String utlabourAmount) {
		this.utlabourAmount = utlabourAmount;
	}
	public String getUtderateAmount() {
		return utderateAmount;
	}
	public void setUtderateAmount(String utderateAmount) {
		this.utderateAmount = utderateAmount;
	}
	public String getUtsalesPartAmount() {
		return utsalesPartAmount;
	}
	public void setUtsalesPartAmount(String utsalesPartAmount) {
		this.utsalesPartAmount = utsalesPartAmount;
	}
	public String getUtaddItemAmount() {
		return utaddItemAmount;
	}
	public void setUtaddItemAmount(String utaddItemAmount) {
		this.utaddItemAmount = utaddItemAmount;
	}
	public String getUtpayTypeCode() {
		return utpayTypeCode;
	}
	public void setUtpayTypeCode(String utpayTypeCode) {
		this.utpayTypeCode = utpayTypeCode;
	}
	public String getUtoverItemAmount() {
		return utoverItemAmount;
	}
	public void setUtoverItemAmount(String utoverItemAmount) {
		this.utoverItemAmount = utoverItemAmount;
	}
	public String getUtreceiveAmount() {
		return utreceiveAmount;
	}
	public void setUtreceiveAmount(String utreceiveAmount) {
		this.utreceiveAmount = utreceiveAmount;
	}
	public String getUtcalcReceiveAmount() {
		return utcalcReceiveAmount;
	}
	public void setUtcalcReceiveAmount(String utcalcReceiveAmount) {
		this.utcalcReceiveAmount = utcalcReceiveAmount;
	}
	public String getUtrepairPartAmount() {
		return utrepairPartAmount;
	}
	public void setUtrepairPartAmount(String utrepairPartAmount) {
		this.utrepairPartAmount = utrepairPartAmount;
	}
	public String getUttax() {
		return uttax;
	}
	public void setUttax(String uttax) {
		this.uttax = uttax;
	}
	public String getUttaxAmountBalance() {
		return uttaxAmountBalance;
	}
	public void setUttaxAmountBalance(String uttaxAmountBalance) {
		this.uttaxAmountBalance = uttaxAmountBalance;
	}
	public String getUtnetAmountBalance() {
		return utnetAmountBalance;
	}
	public void setUtnetAmountBalance(String utnetAmountBalance) {
		this.utnetAmountBalance = utnetAmountBalance;
	}
	public String getUtownAmount() {
		return utownAmount;
	}
	public void setUtownAmount(String utownAmount) {
		this.utownAmount = utownAmount;
	}
	public String getUtinvoiceNo() {
		return utinvoiceNo;
	}
	public void setUtinvoiceNo(String utinvoiceNo) {
		this.utinvoiceNo = utinvoiceNo;
	}
	public String getUtsalesPartNo() {
		return utsalesPartNo;
	}
	public void setUtsalesPartNo(String utsalesPartNo) {
		this.utsalesPartNo = utsalesPartNo;
	}
	public String getUtlabourCost() {
		return utlabourCost;
	}
	public void setUtlabourCost(String utlabourCost) {
		this.utlabourCost = utlabourCost;
	}
	public String getUtinvoiceTypeCode() {
		return utinvoiceTypeCode;
	}
	public void setUtinvoiceTypeCode(String utinvoiceTypeCode) {
		this.utinvoiceTypeCode = utinvoiceTypeCode;
	}
	public String getUtsalesPartCost() {
		return utsalesPartCost;
	}
	public void setUtsalesPartCost(String utsalesPartCost) {
		this.utsalesPartCost = utsalesPartCost;
	}
	public String getUtdiscountModeCode() {
		return utdiscountModeCode;
	}
	public void setUtdiscountModeCode(String utdiscountModeCode) {
		this.utdiscountModeCode = utdiscountModeCode;
	}
	public String getUtrepairPartCost() {
		return utrepairPartCost;
	}
	public void setUtrepairPartCost(String utrepairPartCost) {
		this.utrepairPartCost = utrepairPartCost;
	}
	public String getUtroNo() {
		return utroNo;
	}
	public void setUtroNo(String utroNo) {
		this.utroNo = utroNo;
	}
	public String getUtallAmount() {
		return utallAmount;
	}
	public void setUtallAmount(String utallAmount) {
		this.utallAmount = utallAmount;
	}
	public String getUtvin() {
		return utvin;
	}
	public void setUtvin(String utvin) {
		this.utvin = utvin;
	}
	public String getUtsubObbAmount() {
		return utsubObbAmount;
	}
	public void setUtsubObbAmount(String utsubObbAmount) {
		this.utsubObbAmount = utsubObbAmount;
	}
	public String getUtgiftAmount() {
		return utgiftAmount;
	}
	public void setUtgiftAmount(String utgiftAmount) {
		this.utgiftAmount = utgiftAmount;
	}
	public String getUtsumAmount() {
		return utsumAmount;
	}
	public void setUtsumAmount(String utsumAmount) {
		this.utsumAmount = utsumAmount;
	}
	public String getUtpayOff() {
		return utpayOff;
	}
	public void setUtpayOff(String utpayOff) {
		this.utpayOff = utpayOff;
	}
	public String getUtarrBalance() {
		return utarrBalance;
	}
	public void setUtarrBalance(String utarrBalance) {
		this.utarrBalance = utarrBalance;
	}
	public String getUtguarantor() {
		return utguarantor;
	}
	public void setUtguarantor(String utguarantor) {
		this.utguarantor = utguarantor;
	}
	public String getUtinsurationCode() {
		return utinsurationCode;
	}
	public void setUtinsurationCode(String utinsurationCode) {
		this.utinsurationCode = utinsurationCode;
	}
	public String getUtinsurationNo() {
		return utinsurationNo;
	}
	public void setUtinsurationNo(String utinsurationNo) {
		this.utinsurationNo = utinsurationNo;
	}
	public String getUtaccId() {
		return utaccId;
	}
	public void setUtaccId(String utaccId) {
		this.utaccId = utaccId;
	}
	public String getUtremarkS() {
		return utremarkS;
	}
	public void setUtremarkS(String utremarkS) {
		this.utremarkS = utremarkS;
	}
	public String getUtremark1Balance() {
		return utremark1Balance;
	}
	public void setUtremark1Balance(String utremark1Balance) {
		this.utremark1Balance = utremark1Balance;
	}
	public String getUtisDeliver() {
		return utisDeliver;
	}
	public void setUtisDeliver(String utisDeliver) {
		this.utisDeliver = utisDeliver;
	}
	public String getUtowner() {
		return utowner;
	}
	public void setUtowner(String utowner) {
		this.utowner = utowner;
	}
	public String getUtownerNo() {
		return utownerNo;
	}
	public void setUtownerNo(String utownerNo) {
		this.utownerNo = utownerNo;
	}
	public String getUtcustomerCode() {
		return utcustomerCode;
	}
	public void setUtcustomerCode(String utcustomerCode) {
		this.utcustomerCode = utcustomerCode;
	}
	public String getUtcustomerName() {
		return utcustomerName;
	}
	public void setUtcustomerName(String utcustomerName) {
		this.utcustomerName = utcustomerName;
	}
	public String getUttraceTime() {
		return uttraceTime;
	}
	public void setUttraceTime(String uttraceTime) {
		this.uttraceTime = uttraceTime;
	}
	public String getUtdelivererHobbyContact() {
		return utdelivererHobbyContact;
	}
	public void setUtdelivererHobbyContact(String utdelivererHobbyContact) {
		this.utdelivererHobbyContact = utdelivererHobbyContact;
	}
	public String getUtclaimCost() {
		return utclaimCost;
	}
	public void setUtclaimCost(String utclaimCost) {
		this.utclaimCost = utclaimCost;
	}
	public List<Map> getUtbLDtoList() {
		return utbLDtoList;
	}
	public void setUtbLDtoList(List<Map> utbLDtoList) {
		this.utbLDtoList = utbLDtoList;
	}
	public List<Map> getUthiddenList1() {
		return uthiddenList1;
	}
	public void setUthiddenList1(List<Map> uthiddenList1) {
		this.uthiddenList1 = uthiddenList1;
	}
	public List<Map> getUtbRPDtoList() {
		return utbRPDtoList;
	}
	public void setUtbRPDtoList(List<Map> utbRPDtoList) {
		this.utbRPDtoList = utbRPDtoList;
	}
	public List<Map> getUthiddenList2() {
		return uthiddenList2;
	}
	public void setUthiddenList2(List<Map> uthiddenList2) {
		this.uthiddenList2 = uthiddenList2;
	}
	public List<Map> getUtbSPDtoList() {
		return utbSPDtoList;
	}
	public void setUtbSPDtoList(List<Map> utbSPDtoList) {
		this.utbSPDtoList = utbSPDtoList;
	}
	public List<Map> getUthiddenList3() {
		return uthiddenList3;
	}
	public void setUthiddenList3(List<Map> uthiddenList3) {
		this.uthiddenList3 = uthiddenList3;
	}
	public List<Map> getUtbAIDtoList() {
		return utbAIDtoList;
	}
	public void setUtbAIDtoList(List<Map> utbAIDtoList) {
		this.utbAIDtoList = utbAIDtoList;
	}
	public List<Map> getUthiddenList4() {
		return uthiddenList4;
	}
	public void setUthiddenList4(List<Map> uthiddenList4) {
		this.uthiddenList4 = uthiddenList4;
	}
	public List<Map> getUtreceivableList() {
		return utreceivableList;
	}
	public void setUtreceivableList(List<Map> utreceivableList) {
		this.utreceivableList = utreceivableList;
	}
	public List<Map> getUtreceivedList() {
		return utreceivedList;
	}
	public void setUtreceivedList(List<Map> utreceivedList) {
		this.utreceivedList = utreceivedList;
	}
	public List<Map> getUthiddenList5() {
		return uthiddenList5;
	}
	public void setUthiddenList5(List<Map> uthiddenList5) {
		this.uthiddenList5 = uthiddenList5;
	}
	public List<Map> getUtdmsCoupon() {
		return utdmsCoupon;
	}
	public void setUtdmsCoupon(List<Map> utdmsCoupon) {
		this.utdmsCoupon = utdmsCoupon;
	}
	public List<Map> getUtinvoiceList() {
		return utinvoiceList;
	}
	public void setUtinvoiceList(List<Map> utinvoiceList) {
		this.utinvoiceList = utinvoiceList;
	}
	public List<Map> getUtpreferential() {
		return utpreferential;
	}
	public void setUtpreferential(List<Map> utpreferential) {
		this.utpreferential = utpreferential;
	}
	public Integer getUtpayModel() {
		return utpayModel;
	}
	public void setUtpayModel(Integer utpayModel) {
		this.utpayModel = utpayModel;
	}
	public String getUtdiscountAmount() {
		return utdiscountAmount;
	}
	public void setUtdiscountAmount(String utdiscountAmount) {
		this.utdiscountAmount = utdiscountAmount;
	}
	public String getUtusingStore() {
		return utusingStore;
	}
	public void setUtusingStore(String utusingStore) {
		this.utusingStore = utusingStore;
	}
	public String getUtusingPrePay() {
		return utusingPrePay;
	}
	public void setUtusingPrePay(String utusingPrePay) {
		this.utusingPrePay = utusingPrePay;
	}
	public String getUtactivitySpecialFund() {
		return utactivitySpecialFund;
	}
	public void setUtactivitySpecialFund(String utactivitySpecialFund) {
		this.utactivitySpecialFund = utactivitySpecialFund;
	}
	public String getUtcheckNo() {
		return utcheckNo;
	}
	public void setUtcheckNo(String utcheckNo) {
		this.utcheckNo = utcheckNo;
	}
	public String getUtbillNo() {
		return utbillNo;
	}
	public void setUtbillNo(String utbillNo) {
		this.utbillNo = utbillNo;
	}
	public String getUtInvoiceTag() {
		return utInvoiceTag;
	}
	public void setUtInvoiceTag(String utInvoiceTag) {
		this.utInvoiceTag = utInvoiceTag;
	}
	public String getUtremarkdetail() {
		return utremarkdetail;
	}
	public void setUtremarkdetail(String utremarkdetail) {
		this.utremarkdetail = utremarkdetail;
	}
	public String getUtpaymentObjectCode() {
		return utpaymentObjectCode;
	}
	public void setUtpaymentObjectCode(String utpaymentObjectCode) {
		this.utpaymentObjectCode = utpaymentObjectCode;
	}
	public String getUtpaymentObjectName() {
		return utpaymentObjectName;
	}
	public void setUtpaymentObjectName(String utpaymentObjectName) {
		this.utpaymentObjectName = utpaymentObjectName;
	}
	public String getUtserviceAdvisor() {
		return utserviceAdvisor;
	}
	public void setUtserviceAdvisor(String utserviceAdvisor) {
		this.utserviceAdvisor = utserviceAdvisor;
	}
	public String getUthandler() {
		return uthandler;
	}
	public void setUthandler(String uthandler) {
		this.uthandler = uthandler;
	}
	public String getUtreceiveDate() {
		return utreceiveDate;
	}
	public void setUtreceiveDate(String utreceiveDate) {
		this.utreceiveDate = utreceiveDate;
	}
	public String getUtwriteoffTag() {
		return utwriteoffTag;
	}
	public void setUtwriteoffTag(String utwriteoffTag) {
		this.utwriteoffTag = utwriteoffTag;
	}
	public String getUtisAllInvoice() {
		return utisAllInvoice;
	}
	public void setUtisAllInvoice(String utisAllInvoice) {
		this.utisAllInvoice = utisAllInvoice;
	}
	public String getUtprePay() {
		return utprePay;
	}
	public void setUtprePay(String utprePay) {
		this.utprePay = utprePay;
	}
	public List<Map> getUtpaymentObjectList() {
		return utpaymentObjectList;
	}
	public void setUtpaymentObjectList(List<Map> utpaymentObjectList) {
		this.utpaymentObjectList = utpaymentObjectList;
	}
	public String getUtisBalancePrint() {
		return utisBalancePrint;
	}
	public void setUtisBalancePrint(String utisBalancePrint) {
		this.utisBalancePrint = utisBalancePrint;
	}
	public String getUtisPad() {
		return utisPad;
	}
	public void setUtisPad(String utisPad) {
		this.utisPad = utisPad;
	}
	public String getUtnextMaintainDate() {
		return utnextMaintainDate;
	}
	public void setUtnextMaintainDate(String utnextMaintainDate) {
		this.utnextMaintainDate = utnextMaintainDate;
	}
	public String getUtnextMaintainMileage() {
		return utnextMaintainMileage;
	}
	public void setUtnextMaintainMileage(String utnextMaintainMileage) {
		this.utnextMaintainMileage = utnextMaintainMileage;
	}
	public String getUtnoTraceReason() {
		return utnoTraceReason;
	}
	public void setUtnoTraceReason(String utnoTraceReason) {
		this.utnoTraceReason = utnoTraceReason;
	}
}
