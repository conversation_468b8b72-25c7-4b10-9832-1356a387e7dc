package com.yonyou.dmscus.customer.utils.ai;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;

public class SoucheUtil {
	
	public static String createToken(String accessSecret,String accessKey) {
		RSA rsa = new RSA(accessSecret, null);
		byte[] encrypt = rsa.encrypt(StrUtil.bytes("" + System.currentTimeMillis(), CharsetUtil.CHARSET_UTF_8), KeyType.PrivateKey);
		return "accessKeyIs" + accessKey + "-SPLIT-" + new String(HexUtil.encodeHex(encrypt));
	}
	

}
