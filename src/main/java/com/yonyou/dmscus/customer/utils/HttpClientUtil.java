package com.yonyou.dmscus.customer.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * httpClient工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpClientUtil {
    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);

    private  static CloseableHttpClient httpClient = null;
    /**
     * 连接池总大小
     **/
    private static final int TOTAL_COUNT = 1000;
    /**
     * 每个路由最大连接数
     **/
    private static final int MAX_PRE_ROUTE = 500;
    /**
     * 连接超时，单位毫秒
     **/
    private static final int CONNECTION_TIMEOUT = 5000;
    /**
     * socket超时，单位毫秒
     **/
    private static final int SOCKET_TIMEOUT = 10000;
    /**
     * 从连接池中获取连接超时，单位毫秒
     **/
    private static final int CONNECTION_REQUEST_TIMEOUT = 5000;
    /**
     * 关闭空闲连接，单位秒
     **/
    private static final int MAX_IDLE_TIME = 1800;

    private static final String UTF = "UTF-8";

    private static final String JSONS = "application/json";
    private static final String TYPE = "Content-Type";
    private static final String SHU = "响应数据:{}";



    private HttpClientUtil() {
    }

    /**
     * 初始化连接池
     */
    public static void initPools() {
        if (httpClient != null) {
            return;
        }
        synchronized (HttpClientUtil.class) {
            if (httpClient == null) {
                RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(SOCKET_TIMEOUT).setConnectTimeout(CONNECTION_TIMEOUT).setConnectionRequestTimeout(CONNECTION_REQUEST_TIMEOUT).build();

                PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
                cm.setMaxTotal(TOTAL_COUNT);
                cm.setDefaultMaxPerRoute(MAX_PRE_ROUTE);
                httpClient = HttpClients.custom().setConnectionManager(cm).setDefaultRequestConfig(requestConfig).evictExpiredConnections().evictIdleConnections(MAX_IDLE_TIME, TimeUnit.SECONDS).build();
            }
        }
    }

    /**
     * 发送POST请求
     *
     * @param url 请求URL
     * @return 返回值
     * @throws Exception 异常
     */
    public static String doPost(String url, String param, Map<String, String> headers) throws IOException {
        String result = null;
        HttpPost httpPost = null;
        CloseableHttpResponse response = null;
        try {
            if (httpClient == null) {
                initPools();
            }
            httpPost = new HttpPost(url);
            if (param != null && !param.isEmpty()) {
                StringEntity entity = new StringEntity(param, UTF);
                entity.setContentEncoding(UTF);
                entity.setContentType(JSONS);
                httpPost.setEntity(entity);
            }
            httpPost.addHeader(TYPE, JSONS);
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> next : headers.entrySet()) {
                    httpPost.addHeader(next.getKey(), next.getValue());
                }
            }
            response = httpClient.execute(httpPost);
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                String jsonString = JSON.toJSONString(resEntity);
                logger.info("=====response:{}", jsonString);
                if (resEntity != null) {
                    result = EntityUtils.toString(resEntity, StandardCharsets.UTF_8);
                    logger.info(SHU, result);
                }
            }
        } catch (IOException e) {
            logger.error("新电信平台异常:", e);
        }finally {
            releaseAll(httpPost, response);
        }
        return result;
    }


    /**
     * 发送PUT请求
     *
     * @param url 请求URL
     * @return 返回值
     * @throws Exception 异常
     */
    public static String doPut(String url, Map<String, Object> params, Map<String, String> headers) throws IOException {
        String result = null;
        HttpPut httpPut = null;
        CloseableHttpResponse response = null;
        try {
            if (httpClient == null) {
                initPools();
            }
            httpPut = new HttpPut(url);
            if (params != null && !params.isEmpty()) {
                String s = JSONObject.toJSONString(params);
                StringEntity entity = new StringEntity(s, UTF);
                entity.setContentEncoding(UTF);
                entity.setContentType(JSONS);
                httpPut.setEntity(entity);
            }
            httpPut.addHeader(TYPE, JSONS);
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> next : headers.entrySet()) {
                    httpPut.addHeader(next.getKey(), next.getValue());
                }
            }
            response = httpClient.execute(httpPut);
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    result = EntityUtils.toString(resEntity, StandardCharsets.UTF_8);
                    logger.info(SHU, result);
                }
            }
        } finally {
            releaseAll(httpPut, response);
        }
        return result;
    }

    /**
     * 发送GET请求
     *
     * @param url 请求URL
     * @return 返回值
     * @throws IOException 异常
     */
    public static String doGet(String url, Map<String, Object> params, Map<String, String> headers) throws IOException {
        String result = null;
        HttpGet httpGet = null;
        CloseableHttpResponse response = null;
        try {
            if (httpClient == null) {
                initPools();
            }
            ArrayList<NameValuePair> pairs = new ArrayList<>();
            if (params != null && !params.isEmpty()) {
                params.forEach((k, v) -> pairs.add(new BasicNameValuePair(k, v == null ? null : v.toString())));
                url += "?" + EntityUtils.toString(new UrlEncodedFormEntity(pairs), StandardCharsets.UTF_8);
            }
            httpGet = new HttpGet(url);
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> next : headers.entrySet()) {
                    httpGet.addHeader(next.getKey(), next.getValue());
                }
            }
            response = httpClient.execute(httpGet);
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    result = EntityUtils.toString(resEntity, StandardCharsets.UTF_8);
                    logger.info(SHU, result);
                }
            }
        } finally {
            releaseAll(httpGet, response);
        }

        return result;
    }

    private static void releaseAll(HttpRequestBase request, CloseableHttpResponse response) {
        try {
            if (request != null) {
                request.releaseConnection();
            }
            if (response != null) {
                response.close();
            }
        } catch (IOException e) {
            throw new ServiceBizException(e);
        }
    }
}