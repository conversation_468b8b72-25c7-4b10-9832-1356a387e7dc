package com.yonyou.dmscus.customer.utils.souche;

import com.alibaba.fastjson.JSON;
import com.souche.api.SoucheApiException;
import com.souche.api.SoucheClient;
import com.souche.api.SoucheRequest;
import com.souche.api.SoucheResponse;

import java.util.HashMap;
import java.util.Map;

public class DasoucheTest {
	
	
//	public static void main(String[] args) {
//		//System.out.println(
//		//DateUtils.parseDateStrToDate("2020-08-20 18:04:34","yyyy-MM-dd HH:mm:ss").getTime());
//
//		String a= createToken("MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIoO1ttMZVxx2QgOmh7GEZTIV19MvgRfQH/iOeSiXg46RW0JtS4wA6QO49GyCIMy77jcbm7gMLqwsaQ/Kjay4Z/9vMO6qWqi/1dtQLN+tDzqcYIS/gk0zWOSGlS863BGaZrFlWZwX58TOYKvO7EcBUSPQuEZC3lzx9U9lkaTH5QTAgMBAAECgYAlKoeG9BMZhwChXaT6AeJ7xk9XNYa2EJqBkvp45YkilWdZSlK/Q2fHg8arDh2ijFTi1FzadnpH2AeKqwKHRTLeBS1NXDcT13Kep8tvvehGjrr/9qncRDSrHbK44bb4te9lBc2x4NnCd0b0vzCE+yoSLLXQAKsfSy7FfAnN9PS96QJBALwqWUpVT/tZ53GCnMaeUyZXXPBnfHJkwt2tDej098hFmQmhbehPIuSjo91ZhSIESa8PFSnaxsLt9VmuiOr8hDUCQQC71Bv5xoPB+abph/kg0Yy+B2NYKVzyrzbZV7iNEau240OFCWgeuh3wX/rZ3+ffB2rhtiieBPrb8eECoht/F7AnAkEArXoJqjtvKtefRopZidBL7qxathViqYB2G7PBgEXg75Yi2rsmvie/jrXDjH5M/KihWjD4nQbjerGyIsWD28mFBQJATB4uZC/V74M76EIJbQpe7hfSY8UHfPYS1UueZ9T0cL5y+3QPdPxZ67MJ1sfNkO3Rm70Vgsxj2USRul51pDC2WwJBAImwijDVOaIhsdyQLNOJKYSqGWt5Gmn8VJF+CJxuRQoLA9gjjFT5JgnyXWuN8CO/utkwxbR7n4xNVByLQgKotec=","e9d1219539b34b4e9cd0c3cb030c7584");
//	    System.out.println(a);
//	}
	
//	public static String createToken(String accessSecret,String accessKey) {
//		RSA rsa = new RSA(accessSecret, null);
//		byte[] encrypt = rsa.encrypt(StrUtil.bytes("" + System.currentTimeMillis(), CharsetUtil.CHARSET_UTF_8), KeyType.PrivateKey);
//		return "accessKeyIs" + accessKey + "-SPLIT-" + new String(HexUtil.encodeHex(encrypt));
//	}
	// 录音文件推送测试类
	public static void main(String[] args) {

		String serverUrl = "http://openapi.proxy.dasouche.com/v3";
		String appKey = "4d036b310fd2946672d3486df6509549";
		String appSecret = "febf74f0e960c00ba39ccf5dac67b17e";

		// 实例化 SDK 客户端
		SoucheClient soucheClient = new SoucheClient(serverUrl, appKey, appSecret);

		// 实例化请求类
		SoucheRequest request = new SoucheRequest();

		Map<String, Object> dataMap = new HashMap<String, Object>();
		//固定不变
        dataMap.put("serviceId", "1");
        dataMap.put("channelNumber", "2");
        dataMap.put("direction", 1);//1: 呼出, 2:呼入
        dataMap.put("inspects", "3");//质检类型
        //不确定值
        dataMap.put("customerNumber", "");
        dataMap.put("cityCode", "");
        dataMap.put("provinceCode", "");
        dataMap.put("shopCode", ""); //经销商代码
        dataMap.put("isFirstFollowUp", 1); //是否首次跟进拨打


        dataMap.put("sessionId","12387");
        dataMap.put("url","http://baidu.com");
        //dataMap.put("callTime", new Date().getTime()); //通话开始时间


		// 设置请求参数 api {String}
		request.setApi("com.souche.voipvoice.voipVoiceController.insert.json");
		// 设置请求参数 data {Map<String, Object>}
		Map<String, Object> dataMap2 = new HashMap<String, Object>();
		String jsonData= JSON.toJSONString(dataMap);
		dataMap2.put("json", jsonData);
		request.setData(dataMap2);

		try {
		  // 发送请求
		  SoucheResponse response = soucheClient.execute(request);
		  if (response.isSuccess()) {
		    // 请求成功
		    String data = response.getData();
		    System.out.println(data);
		  }
		} catch (SoucheApiException e) {
		  // 业务错误查看 errCode 和 errMessage 内容
		  int errCode = e.getErrCode();
		  String errMessage = e.getErrMessage();
		  System.out.println(errCode + ": " + errMessage);
		  // 通信错误查看 message 内容
		  String errorMessage = e.getMessage();
		  System.out.println(errorMessage);
		}

	}
	

	
	


}
