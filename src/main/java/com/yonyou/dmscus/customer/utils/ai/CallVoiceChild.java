package com.yonyou.dmscus.customer.utils.ai;

import lombok.Data;

/**
 * 录音文件（子元素）
 * <AUTHOR>
 *
 */
@Data
public class CallVoiceChild {
	
	/**
	 * 接口字段
	 */
	private String sessionId;  //话单 ID
	
	private String callerNbr;  //主叫号码
	
	private String workNbr;  //工作号码
	
	private String calleeNbr; //被叫号码
	
	private String voiceAddr; //录音文件下载地址
	
	private String fileSize;  //文件大小
	
	private String type;// 录音类型 (in：主叫单声道;out：被叫单声道;mix：主被叫混合声道)

	/**
	 * 冗余字段
	 */
	private String ownerCode;
	
	private String ownerParCode;
	
	private String callId;
	
	private String dealerCode;
	
}
