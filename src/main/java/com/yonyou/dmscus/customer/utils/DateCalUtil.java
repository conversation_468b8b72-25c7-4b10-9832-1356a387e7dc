package com.yonyou.dmscus.customer.utils;
import com.yonyou.dmscus.customer.util.common.DateUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
/**
 * <AUTHOR>
 * @date 2023/05/12 11:19:47
 */
public class DateCalUtil
{
    private static final String PATTERN_HH = "HH";

    private static final String PATTERN_YYYY_MM_DD = "yyyy-MM-dd 00:00:00";

    /**默认日期格式为:yyyy-MM-dd HH:mm:ss. */
    private static final String PATTERN_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    //工作开始时间
    private static final String PATTERN_YYYY_MM_DD_09_00_00 = "yyyy-MM-dd 09:00:00";
    //工作结束时间
    private static final String PATTERN_YYYY_MM_DD_20_00_00 = "yyyy-MM-dd 20:00:00";
    //休息开始时间
    private static final int startHour = 20;
    //休息结束时间
    private static final int endHour = 9;
    //休息时间
    private static final int restHour =  endHour - startHour < 0 ? 24 - startHour + endHour : endHour - startHour;

    public static void main(String[] args) {
        Date startTime = DateUtils.parseDateStrToDate("2023-05-15 01:50:00", DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS);;
        //Date endTime = new Date();
        Date endTime = DateUtils.parseDateStrToDate("2023-05-15 02:00:00", DateUtils.PATTERN_YYYY_MM_DD_HH_MM_SS);;
        Long time = calculateTime(startTime, endTime);
        BigDecimal b = new BigDecimal((double)time/60);
        Double hour = b.setScale(1,BigDecimal.ROUND_DOWN).doubleValue();

        System.out.println(time);
        System.out.println(hour);
    }


    public static Long calculateTime(Date startTime, Date endTime){
        //通过startTime计算开始时间
        startTime = calculateStartTime(startTime);
        System.out.println(DateUtils.dateToString(startTime, PATTERN_YYYY_MM_DD_HH_MM_SS));
        //时间
        endTime = calculateEndTime(endTime, startTime);
        System.out.println(DateUtils.dateToString(endTime, PATTERN_YYYY_MM_DD_HH_MM_SS));
        //计算时间查
        Long time = dateDiff(endTime,startTime);
        time = time <= 0 ? 0 : time;
        return time ;
    }

    /**
     * 计算开始时间
     */
    private static Date calculateStartTime(Date startTime){
        //通过startTime计算开始时间
        Date workStartTime = timeConversion(startTime, PATTERN_YYYY_MM_DD_09_00_00);
        Date time = null;
        //判断时间
        if(workStartTime.compareTo(startTime) > 0){
            time =  workStartTime;
        }else{
            time = startTime;
        }
        //判断是否在休息时间中
        return calculatePlusRestTime(time, PATTERN_YYYY_MM_DD_09_00_00);
    }

    /**
     * 通过开始时间计算结束时间
     */
    private static Date calculateEndTime(Date endTime, Date startTime){
        //通过startTime计算开始时间
        Date workStartTime = timeConversion(endTime, PATTERN_YYYY_MM_DD_20_00_00);
        Date time = null;
        //判断时间
        if(workStartTime.compareTo(endTime) > 0){
            time = endTime;
        }else{
            time = workStartTime;
        }
        //判断是否在休息时间中
        return calculateReduceRestTime(time, PATTERN_YYYY_MM_DD_20_00_00);
    }

    /**
     * 时间转换
     */
    private static Date timeConversion(Date date, String pattern){
        //通过startTime计算开始时间
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        String time = format.format(date);

        return parseDate(time, PATTERN_YYYY_MM_DD_HH_MM_SS);

    }

    private static Date parseDate(String dataStr, String pattern) {
        try {
            SimpleDateFormat format = new SimpleDateFormat(pattern);
            return format.parse(dataStr);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    //判断是否在休息时间休息时间
    private static Date calculateReduceRestTime(Date date, String pattern) {
        //获取HH
        SimpleDateFormat formatter = new SimpleDateFormat(PATTERN_HH);
        Integer time = Integer.parseInt(formatter.format(date));
        if(time <= endHour){
            //时间减一天
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.DATE, -1);
            return timeConversion(calendar.getTime(), pattern);
        }
        return date;
    }

    private static Date calculatePlusRestTime(Date date, String pattern) {
        //获取HH
        SimpleDateFormat formatter = new SimpleDateFormat(PATTERN_HH);
        Integer time = Integer.parseInt(formatter.format(date));
        if(time >= startHour){
            //时间加一天
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.DATE, 1);
            return timeConversion(calendar.getTime(), pattern);
        }
        return date;
    }

    //计算时间相差的天数
    private static long calculateTimeDay(Date startTime, Date endTime, String pattern){
        //获取HH
        startTime = timeConversion(startTime, pattern);
        endTime = timeConversion(endTime, pattern);
        return (endTime.getTime()-startTime.getTime())/(1000*3600*24);
    }
    /**
     * 计算2个时间相差的分钟
     * @param startTime 开始时间
     * @param endTime 截止时间
     * @return
     */
    private static Long dateDiff(Date endTime, Date startTime) {
        long nm = 1000 * 60;// 一分钟的毫秒数
        long day = 0;
        long diff;
        long min = 0;
        // 获得两个时间的毫秒时间差异
        try {
            diff = endTime.getTime() - startTime.getTime();
            // 计算差多少天
            day = calculateTimeDay(startTime, endTime, PATTERN_YYYY_MM_DD);
            // 计算差多少分钟
            min = diff / nm;
            // 除去休息时间
            if(day >= 1){
                min = min - (day * restHour * 60);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return min;
    }
}