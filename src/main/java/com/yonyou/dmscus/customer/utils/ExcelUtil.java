package com.yonyou.dmscus.customer.utils;

import com.yonyou.dmscus.customer.annotation.ExcelColumnDefine;
import com.yonyou.dmscus.customer.ossexcel.ExcelExportColumn;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * 基于excel进行导出 基于反射获取字段
 */
public class ExcelUtil {

    public static List<ExcelExportColumn> getExcelExportColumnFromAnnotation(Class<?> clazz) {
        List<ExcelExportColumn> result = new ArrayList<>();
        Field[] declaredFields = clazz.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            ExcelColumnDefine annotation = declaredField.getAnnotation(ExcelColumnDefine.class);
            if (annotation != null) {
                ExcelExportColumn excelExportColumn = new ExcelExportColumn(declaredField.getName(), annotation.name());
                excelExportColumn.setExportFormat(annotation.format());
                result.add(excelExportColumn);
            }
        }
        return result;
    }

}
