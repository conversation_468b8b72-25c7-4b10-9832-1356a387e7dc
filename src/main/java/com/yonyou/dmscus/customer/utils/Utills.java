package com.yonyou.dmscus.customer.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class Utills {

    public List strToLong (String ids){

        String[] newStr = ids.split(",");

        List<Long> list = new ArrayList();

        for(String str: newStr){
            list.add(Long.parseLong(str));
        }
        return list;
    }

    public static List<String> getList(String s){
        if (!com.yonyou.dmscus.customer.util.common.StringUtils.isNullOrEmpty(s)){
            return Arrays.asList(s.split(",")).stream()
                    .collect(Collectors.toList());
        }return Collections.emptyList();
    }

    /**
     * 5W < count <= 10W  线程数: 2
     * 10W < count <= 20W 线程数: 4
     * 20W < count   线程数: 8
     * @param countCdpTag
     * @return 线程数
     */
    public static int getThreadSize(Integer countCdpTag) {
        int threadSize = 0;
        if (countCdpTag > 50000 && countCdpTag <=100000){//线程数: 2
            threadSize = 2;

        }else if (countCdpTag > 100000 && countCdpTag <=200000){//线程数: 4
            threadSize = 4;
        } else if (countCdpTag > 200000) {//线程数: 8
            threadSize = 8;
        }
        return threadSize;
    }

}
