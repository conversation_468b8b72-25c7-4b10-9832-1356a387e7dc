package com.yonyou.dmscus.customer.utils.ai;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.yonyou.dmscus.customer.service.common.HttpLogService;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.TreeSet;


@Component
public class DccHttpHelper {
	
	
	static String BASE_URL;
	
	static String URL_MAPPING_WORKBIND;
	
	static String PUBLIC_KEY;
	
	static String APP_ID_VALUE;
	
	static String APP_SECRET_VALUE;
	
	static String APP_ID;
	
	static String ACCESS_KEY;
	
	static String ACCESS_OFFSET;

	static HttpLogService httpLogService;
	
	private static Logger logger = LoggerFactory.getLogger(DccHttpHelper.class);
	
	public static String httpPost(String strUrl, Map<String, Object> map) {
		String url = BASE_URL + strUrl;
		String requestBodyString="";
		String code="200";
		String jsonString="";
		try {
			logger.info("BASE_URL:"+BASE_URL);
			logger.info("register_url:"+strUrl);
			logger.info("url："+url);
			logger.info("APP_ID:"+APP_ID_VALUE);
			logger.info("APP_SECRET:"+APP_SECRET_VALUE);
			logger.info("publickey:"+PUBLIC_KEY);
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.TEXT_PLAIN);
			// 首先生成随机的密钥和向量
			String aesKey = AesUtils.getRandomString(16);// 秘钥
			String aesIv = AesUtils.getRandomString(16);// 向量
			String accessKey = RsaUtils.encryptByPublicKey(aesKey, PUBLIC_KEY);// 公钥加密
			String accessIv = RsaUtils.encryptByPublicKey(aesIv, PUBLIC_KEY);// 公钥加密

			Object jsonObject = JSONObject.toJSON(map);
			requestBodyString = jsonObject.toString()
					+ DigestUtils.sha256Hex(getValues(jsonObject) + APP_SECRET_VALUE);
			logger.info("requestBodyString:"+requestBodyString);
			headers.add(APP_ID, APP_ID_VALUE);
			headers.add(ACCESS_KEY, accessKey);
			headers.add(ACCESS_OFFSET, accessIv);
			headers.setContentType(MediaType.TEXT_PLAIN);
			// 使用随机密钥和和向量对明 文json 数据及签名串AES 加密
			String cipherText = AesUtils.encrypt(requestBodyString, aesKey, aesIv);
			HttpEntity<String> entity = new HttpEntity<String>(URLEncoder.encode(cipherText, "UTF-8"), headers);
			RestTemplate re = new RestTemplate();
			ResponseEntity<String> responseEntity = null;
			responseEntity = re.postForEntity(URI.create(url), entity, String.class);
			String decryptKey = RsaUtils.decryptByPublicKey(responseEntity.getHeaders().getFirst(ACCESS_KEY),
					PUBLIC_KEY);
			String decryptIv = RsaUtils.decryptByPublicKey(responseEntity.getHeaders().getFirst(ACCESS_OFFSET),
					PUBLIC_KEY);
			String responseBodyString = AesUtils.decrypt(
					URLDecoder.decode(Objects.requireNonNull(responseEntity.getBody()), StandardCharsets.UTF_8.name()),
					decryptKey, decryptIv);
			int index = responseBodyString.lastIndexOf("}") + 1;
			jsonString = responseBodyString.substring(0, index);
			if (responseBodyString.length() > jsonString.length()) {
				String originSign = responseBodyString.substring(index);
				//Object respObj = JSONObject.fromObject(jsonString);
				//String localSign = DigestUtils.sha256Hex(getValues(respObj) + APP_SECRET_VALUE);
				//if (Objects.equals(originSign, localSign)) {// 签名校验通过
				//	LOG.error("解密后数据=>{}" + jsonString);
				//} else {// 签名校验失败
				//	LOG.error("签名不一致");
				//}
			}
			logger.info("return:"+jsonString);
			return jsonString;
		} catch (Exception e) {
			Map<String, Object> remap = new LinkedHashMap<String, Object>();
			remap.put("code", "-1");
			remap.put("message", "失败！");
			code="500";
			Gson gson = new Gson();
			jsonString = gson.toJson(remap);
			logger.error("请求出错:{}",e.getMessage());
			return jsonString;
		}finally {
			httpLogService.saveHttpLog("电信-呼叫登记",url,requestBodyString,"POST",code+"",jsonString);
		}
	}

	// 获取json values String
	public static String getValues(Object obj) {
		StringBuilder result = new StringBuilder();
		if (obj instanceof JSONObject) {
			JSONObject jsonObject = (JSONObject) obj;
			TreeSet<String> keys = new TreeSet<String>(jsonObject.keySet());
			for (String key : keys) {
				Object value = jsonObject.get(key);
				if (value instanceof JSONObject || value instanceof JSONArray) {
					result.append(getValues(value));
				} else {
					result.append(value);
				}
			}
		} else if (obj instanceof JSONArray) {
			JSONArray jsonArray = (JSONArray) obj;
			for (Object value : jsonArray) {
				result.append(getValues(value));
			}
		} else {
			result.append(obj);
		}
		return result.toString();
	}

	


	@Value("${ai.telecom.param.base_url}")
	public  void setBASE_URL(String bASE_URL) {
		BASE_URL = bASE_URL;
	}

	@Value("${ai.telecom.common.url_mapping_workbind}")
	public  void setURL_MAPPING_WORKBIND(String uRL_MAPPING_WORKBIND) {
		URL_MAPPING_WORKBIND = uRL_MAPPING_WORKBIND;
	}

	@Value("${ai.telecom.param.public_key}")
	public  void setPUBLIC_KEY(String pUBLIC_KEY) {
		PUBLIC_KEY = pUBLIC_KEY;
	}

	@Value("${ai.telecom.param.app_id_value}")
	public  void setAPP_ID_VALUE(String aPP_ID_VALUE) {
		APP_ID_VALUE = aPP_ID_VALUE;
	}

	@Value("${ai.telecom.param.app_secret_value}")
	public  void setAPP_SECRET_VALUE(String aPP_SECRET_VALUE) {
		APP_SECRET_VALUE = aPP_SECRET_VALUE;
	}

	@Value("${ai.telecom.common.app_id}")
	public  void setAPP_ID(String aPP_ID) {
		APP_ID = aPP_ID;
	}

	@Value("${ai.telecom.common.access_key}")
	public  void setACCESS_KEY(String aCCESS_KEY) {
		ACCESS_KEY = aCCESS_KEY;
	}

	@Value("${ai.telecom.common.access_offset}")
	public  void setACCESS_OFFSET(String aCCESS_OFFSET) {
		ACCESS_OFFSET = aCCESS_OFFSET;
	}

	@Resource
	public  void setHttpLogService(HttpLogService httpLogService) {
		DccHttpHelper.httpLogService = httpLogService;
	}
	
}
