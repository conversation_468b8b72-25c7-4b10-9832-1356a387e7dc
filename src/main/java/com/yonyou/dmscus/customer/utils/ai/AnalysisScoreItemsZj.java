package com.yonyou.dmscus.customer.utils.ai;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/4/25 0025
 */
@Data
public class AnalysisScoreItemsZj {

    private String dateCreate;   	//我们没有质检时间，所以⽬前放对话开始 时间

    private String id;  //我们的系统的dialog

    private String itemText; //计分项名称

    private String model;  //评分⼩组名称

    private Integer score; //评分项实际得分
    private Integer itemFullScore; //评分项满分(质检点满分值)

    private String scoreItemId; //计分项id

    private String serviceId; //销售1，售后2

    private String sessionId; //通话的外部业务id
    private Integer totalScore; //对话总实际得分
    private String socreTemplateName; //评分模板的名称


}
