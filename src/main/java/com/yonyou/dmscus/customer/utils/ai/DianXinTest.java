package com.yonyou.dmscus.customer.utils.ai;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;


public class DianXinTest {
	
	

	/**
	 * AI
	 * @param args
	 */
	public static void main(String[] args) {
		String BASE_URL="http://101.89.109.121:10000"; //（配置）
		Map<String,Object> SaWorkMap=new HashMap<String,Object>();
		//绑定关系查询
		String urlMapping="/api/open/ax/v1.0/number/info";  //参数传入（配置）
		SaWorkMap.put("number", "15313129714"); // Ai语音工作号
		
		//工作号绑定
//		String urlMapping="/api/open/ax/v1.0/number/bind";  //参数传入（配置）
//		SaWorkMap.put("holderNumber", "15221805734"); //手机号
//		SaWorkMap.put("workNumber", "15313129714"); // Ai语音工作号
		
		//登记
//		String urlMapping="/api/open/ax/v1.0/call/register";  //参数传入（配置）
//		SaWorkMap.put("callId", UUID.randomUUID().toString().replace("-", "").toLowerCase()); //手机号
//		SaWorkMap.put("holderNumber", "15221805734"); //手机号
//		SaWorkMap.put("workNumber", "15313129714"); // Ai语音工作号
//		SaWorkMap.put("customNumber","18571800934"); //联系方式
//		SaWorkMap.put("expireMinute", 30);  //默认写死30
		
		//解绑
//		String urlMapping="/api/open/ax/v1.0/number/unbind";
//		SaWorkMap.put("workNumber", "15313129714");
		
		String url=BASE_URL+urlMapping;
		Dianxin(url,SaWorkMap);
		
		
		
	}

	
	
	private static void Dianxin(String url,Map<String,Object> SaWorkMap){
		try {
			System.out.println("进入...");
			String PUBLIC_KEY="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAl/SfOERTPWQLAG5otinPBEevO7QVtykSaFZdUmTBCWyy1a0+eEBcmSkHBIZbJz3C6hfF7sQOSVpQltEVpW+G8iJ2q/8GKURpix6TZ7W7Nj5O7N0rUQLkdhV7zh7Qcb2RWPuhQ6NJUndyALiZjuFe8qEDQ0gOv0Rb2HbUk2IKuRP/PItdfk4bgWyaW+/oA61pVEtPQTRlW5gnh+0KxDhhwRaXX2cKQ3JgezF4Yu3pHfXWklNhJJ2WlteuECZTIIl1OEcptKpbWmLOnio28U6UDeg0/i5yBxrsCn01t41R/8haLLxtt5xK6OL+fBL9yBSCevoddhYsjRcpazwr7mYPiwIDAQAB";
			System.out.println("url:"+url);
			Object jsonObject = JSONObject.toJSON(SaWorkMap);
			System.out.println("JsonObject:"+jsonObject);
			
			//数据加密
			String APP_SECRET_VALUE="4a53486c3435494932394538bbd5cd638f580f36"; //应用密钥串(需配置)
			String requestBodyString = jsonObject.toString()+ DigestUtils.sha256Hex(DccHttpHelper.getValues(jsonObject) + APP_SECRET_VALUE);
			// 首先生成随机的密钥和向量
			String aesKey = AesUtils.getRandomString(16);// 秘钥
		    String aesIv = AesUtils.getRandomString(16);// 向量
		    // PUBLIC_KEY公钥加密
			String accessKey = RsaUtils.encryptByPublicKey(aesKey,PUBLIC_KEY); 
			String accessIv = RsaUtils.encryptByPublicKey(aesIv,PUBLIC_KEY);
		    HttpHeaders headers = new HttpHeaders();
		    headers.setContentType(MediaType.TEXT_PLAIN);
		    headers.add("App-Id", "31379eb13ec250573573");//APP_ID_VALUE (需配置)
		    headers.add("Access-Key", accessKey);
			headers.add("Access-Offset", accessIv);
			// 使用随机密钥和和向量对明 文json 数据及签名串AES 加密
			String cipherText = AesUtils.encrypt(requestBodyString, aesKey, aesIv);
			HttpEntity<String> entity = new HttpEntity<String>(URLEncoder.encode(cipherText, "UTF-8"), headers);
			RestTemplate re = new RestTemplate();
			ResponseEntity<String> responseEntity = null;
			responseEntity = re.postForEntity(URI.create(url), entity, String.class);
			String decryptKey = RsaUtils.decryptByPublicKey(responseEntity.getHeaders().getFirst("Access-Key"),PUBLIC_KEY);
			String decryptIv = RsaUtils.decryptByPublicKey(responseEntity.getHeaders().getFirst("Access-Offset"),PUBLIC_KEY);
			String responseBodyString = AesUtils.decrypt(
					URLDecoder.decode(Objects.requireNonNull(responseEntity.getBody()), StandardCharsets.UTF_8.name()),
					decryptKey, decryptIv);
			int index = responseBodyString.lastIndexOf("}") + 1;
			String jsonString = responseBodyString.substring(0, index);
			System.out.println("响应结果："+jsonString);
		} catch (Exception e) {
			Map<String, Object> remap = new LinkedHashMap<String, Object>();
			remap.put("code", "-1");
			remap.put("message", "失败！");
			Gson gson = new Gson();
			String s = gson.toJson(remap);
			System.out.println("异常"+s);
			e.printStackTrace();
		}
	}
	
	
	
	
	


}
