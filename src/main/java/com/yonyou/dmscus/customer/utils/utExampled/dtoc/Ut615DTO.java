package com.yonyou.dmscus.customer.utils.utExampled.dtoc;

public class Ut615DTO {

    private String field110051;
    private String field210051;
    private String field310051;
    private String field410051;
    private String field510051;
    private String field610051;
    private String field710051;
    private String field810051;
    private String field910051;
    private String field1010051;
    private String field1110051;
    private String field1210051;
    private String field1310051;
    private String field1410051;
    private String field1510051;
    private String field1610051;
    private String field1710051;
    private String field1810051;
    private String field1910051;
    private String field2010051;
    private String field2110051;
    private String field2210051;
    private String field2310051;
    private String field2410051;
    private String field2510051;
    private String field2610051;
    private String field2710051;
    private String field2810051;
    private String field2910051;
    private String field3010051;
    private String field3110051;
    private String field3210051;
    private String field3310051;
    private String field3410051;
    private String field3510051;
    private String field3610051;
    private String field3710051;
    private String field3810051;
    private String field3910051;
    private String field4010051;
    private String field4110051;
    private String field4210051;
    private String field4310051;
    private String field4410051;
    private String field4510051;
    private String field4610051;
    private String field4710051;
    private String field4810051;
    private String field4910051;
    private String field5110051;
    private String field5210051;
    private String field5310051;
    private String field5410051;
    private String field5510051;
    private String field5610051;
    private String field5710051;
    private String field5810051;
    private String field5910051;
    private String field6010051;
    private String field6110051;
    private String field6210051;
    private String field6310051;
    private String field6410051;
    private String field6510051;
    private String field6610051;
    private String field6710051;
    private String field6810051;
    private String field6910051;
    private String field7010051;
    private String field7110051;
    private String field7210051;
    private String field7310051;
    private String field7410051;
    private String field7510051;
    private String field7610051;
    private String field7710051;
    private String field7810051;
    private String field7910051;
    private String field8010051;
    private String field8110051;
    private String field8210051;
    private String field8310051;
    private String field8410051;
    private String field8510051;
    private String field8610051;
    private String field8710051;
    private String field8810051;
    private String field8910051;
    private String field9010051;
    private String field9110051;
    private String field9210051;
    private String field9310051;
    private String field9410051;
    private String field9510051;
    private String field9610051;
    private String field9710051;
    private String field9810051;
    private String field9910051;
    private String field10010051;

    public String getField110051() {
        return field110051;
    }

    public void setField110051(String field110051) {
        this.field110051 = field110051;
    }

    public String getField210051() {
        return field210051;
    }

    public void setField210051(String field210051) {
        this.field210051 = field210051;
    }

    public String getField310051() {
        return field310051;
    }

    public void setField310051(String field310051) {
        this.field310051 = field310051;
    }

    public String getField410051() {
        return field410051;
    }

    public void setField410051(String field410051) {
        this.field410051 = field410051;
    }

    public String getField510051() {
        return field510051;
    }

    public void setField510051(String field510051) {
        this.field510051 = field510051;
    }

    public String getField610051() {
        return field610051;
    }

    public void setField610051(String field610051) {
        this.field610051 = field610051;
    }

    public String getField710051() {
        return field710051;
    }

    public void setField710051(String field710051) {
        this.field710051 = field710051;
    }

    public String getField810051() {
        return field810051;
    }

    public void setField810051(String field810051) {
        this.field810051 = field810051;
    }

    public String getField910051() {
        return field910051;
    }

    public void setField910051(String field910051) {
        this.field910051 = field910051;
    }

    public String getField1010051() {
        return field1010051;
    }

    public void setField1010051(String field1010051) {
        this.field1010051 = field1010051;
    }

    public String getField1110051() {
        return field1110051;
    }

    public void setField1110051(String field1110051) {
        this.field1110051 = field1110051;
    }

    public String getField1210051() {
        return field1210051;
    }

    public void setField1210051(String field1210051) {
        this.field1210051 = field1210051;
    }

    public String getField1310051() {
        return field1310051;
    }

    public void setField1310051(String field1310051) {
        this.field1310051 = field1310051;
    }

    public String getField1410051() {
        return field1410051;
    }

    public void setField1410051(String field1410051) {
        this.field1410051 = field1410051;
    }

    public String getField1510051() {
        return field1510051;
    }

    public void setField1510051(String field1510051) {
        this.field1510051 = field1510051;
    }

    public String getField1610051() {
        return field1610051;
    }

    public void setField1610051(String field1610051) {
        this.field1610051 = field1610051;
    }

    public String getField1710051() {
        return field1710051;
    }

    public void setField1710051(String field1710051) {
        this.field1710051 = field1710051;
    }

    public String getField1810051() {
        return field1810051;
    }

    public void setField1810051(String field1810051) {
        this.field1810051 = field1810051;
    }

    public String getField1910051() {
        return field1910051;
    }

    public void setField1910051(String field1910051) {
        this.field1910051 = field1910051;
    }

    public String getField2010051() {
        return field2010051;
    }

    public void setField2010051(String field2010051) {
        this.field2010051 = field2010051;
    }

    public String getField2110051() {
        return field2110051;
    }

    public void setField2110051(String field2110051) {
        this.field2110051 = field2110051;
    }

    public String getField2210051() {
        return field2210051;
    }

    public void setField2210051(String field2210051) {
        this.field2210051 = field2210051;
    }

    public String getField2310051() {
        return field2310051;
    }

    public void setField2310051(String field2310051) {
        this.field2310051 = field2310051;
    }

    public String getField2410051() {
        return field2410051;
    }

    public void setField2410051(String field2410051) {
        this.field2410051 = field2410051;
    }

    public String getField2510051() {
        return field2510051;
    }

    public void setField2510051(String field2510051) {
        this.field2510051 = field2510051;
    }

    public String getField2610051() {
        return field2610051;
    }

    public void setField2610051(String field2610051) {
        this.field2610051 = field2610051;
    }

    public String getField2710051() {
        return field2710051;
    }

    public void setField2710051(String field2710051) {
        this.field2710051 = field2710051;
    }

    public String getField2810051() {
        return field2810051;
    }

    public void setField2810051(String field2810051) {
        this.field2810051 = field2810051;
    }

    public String getField2910051() {
        return field2910051;
    }

    public void setField2910051(String field2910051) {
        this.field2910051 = field2910051;
    }

    public String getField3010051() {
        return field3010051;
    }

    public void setField3010051(String field3010051) {
        this.field3010051 = field3010051;
    }

    public String getField3110051() {
        return field3110051;
    }

    public void setField3110051(String field3110051) {
        this.field3110051 = field3110051;
    }

    public String getField3210051() {
        return field3210051;
    }

    public void setField3210051(String field3210051) {
        this.field3210051 = field3210051;
    }

    public String getField3310051() {
        return field3310051;
    }

    public void setField3310051(String field3310051) {
        this.field3310051 = field3310051;
    }

    public String getField3410051() {
        return field3410051;
    }

    public void setField3410051(String field3410051) {
        this.field3410051 = field3410051;
    }

    public String getField3510051() {
        return field3510051;
    }

    public void setField3510051(String field3510051) {
        this.field3510051 = field3510051;
    }

    public String getField3610051() {
        return field3610051;
    }

    public void setField3610051(String field3610051) {
        this.field3610051 = field3610051;
    }

    public String getField3710051() {
        return field3710051;
    }

    public void setField3710051(String field3710051) {
        this.field3710051 = field3710051;
    }

    public String getField3810051() {
        return field3810051;
    }

    public void setField3810051(String field3810051) {
        this.field3810051 = field3810051;
    }

    public String getField3910051() {
        return field3910051;
    }

    public void setField3910051(String field3910051) {
        this.field3910051 = field3910051;
    }

    public String getField4010051() {
        return field4010051;
    }

    public void setField4010051(String field4010051) {
        this.field4010051 = field4010051;
    }

    public String getField4110051() {
        return field4110051;
    }

    public void setField4110051(String field4110051) {
        this.field4110051 = field4110051;
    }

    public String getField4210051() {
        return field4210051;
    }

    public void setField4210051(String field4210051) {
        this.field4210051 = field4210051;
    }

    public String getField4310051() {
        return field4310051;
    }

    public void setField4310051(String field4310051) {
        this.field4310051 = field4310051;
    }

    public String getField4410051() {
        return field4410051;
    }

    public void setField4410051(String field4410051) {
        this.field4410051 = field4410051;
    }

    public String getField4510051() {
        return field4510051;
    }

    public void setField4510051(String field4510051) {
        this.field4510051 = field4510051;
    }

    public String getField4610051() {
        return field4610051;
    }

    public void setField4610051(String field4610051) {
        this.field4610051 = field4610051;
    }

    public String getField4710051() {
        return field4710051;
    }

    public void setField4710051(String field4710051) {
        this.field4710051 = field4710051;
    }

    public String getField4810051() {
        return field4810051;
    }

    public void setField4810051(String field4810051) {
        this.field4810051 = field4810051;
    }

    public String getField4910051() {
        return field4910051;
    }

    public void setField4910051(String field4910051) {
        this.field4910051 = field4910051;
    }

    public String getField5110051() {
        return field5110051;
    }

    public void setField5110051(String field5110051) {
        this.field5110051 = field5110051;
    }

    public String getField5210051() {
        return field5210051;
    }

    public void setField5210051(String field5210051) {
        this.field5210051 = field5210051;
    }

    public String getField5310051() {
        return field5310051;
    }

    public void setField5310051(String field5310051) {
        this.field5310051 = field5310051;
    }

    public String getField5410051() {
        return field5410051;
    }

    public void setField5410051(String field5410051) {
        this.field5410051 = field5410051;
    }

    public String getField5510051() {
        return field5510051;
    }

    public void setField5510051(String field5510051) {
        this.field5510051 = field5510051;
    }

    public String getField5610051() {
        return field5610051;
    }

    public void setField5610051(String field5610051) {
        this.field5610051 = field5610051;
    }

    public String getField5710051() {
        return field5710051;
    }

    public void setField5710051(String field5710051) {
        this.field5710051 = field5710051;
    }

    public String getField5810051() {
        return field5810051;
    }

    public void setField5810051(String field5810051) {
        this.field5810051 = field5810051;
    }

    public String getField5910051() {
        return field5910051;
    }

    public void setField5910051(String field5910051) {
        this.field5910051 = field5910051;
    }

    public String getField6010051() {
        return field6010051;
    }

    public void setField6010051(String field6010051) {
        this.field6010051 = field6010051;
    }

    public String getField6110051() {
        return field6110051;
    }

    public void setField6110051(String field6110051) {
        this.field6110051 = field6110051;
    }

    public String getField6210051() {
        return field6210051;
    }

    public void setField6210051(String field6210051) {
        this.field6210051 = field6210051;
    }

    public String getField6310051() {
        return field6310051;
    }

    public void setField6310051(String field6310051) {
        this.field6310051 = field6310051;
    }

    public String getField6410051() {
        return field6410051;
    }

    public void setField6410051(String field6410051) {
        this.field6410051 = field6410051;
    }

    public String getField6510051() {
        return field6510051;
    }

    public void setField6510051(String field6510051) {
        this.field6510051 = field6510051;
    }

    public String getField6610051() {
        return field6610051;
    }

    public void setField6610051(String field6610051) {
        this.field6610051 = field6610051;
    }

    public String getField6710051() {
        return field6710051;
    }

    public void setField6710051(String field6710051) {
        this.field6710051 = field6710051;
    }

    public String getField6810051() {
        return field6810051;
    }

    public void setField6810051(String field6810051) {
        this.field6810051 = field6810051;
    }

    public String getField6910051() {
        return field6910051;
    }

    public void setField6910051(String field6910051) {
        this.field6910051 = field6910051;
    }

    public String getField7010051() {
        return field7010051;
    }

    public void setField7010051(String field7010051) {
        this.field7010051 = field7010051;
    }

    public String getField7110051() {
        return field7110051;
    }

    public void setField7110051(String field7110051) {
        this.field7110051 = field7110051;
    }

    public String getField7210051() {
        return field7210051;
    }

    public void setField7210051(String field7210051) {
        this.field7210051 = field7210051;
    }

    public String getField7310051() {
        return field7310051;
    }

    public void setField7310051(String field7310051) {
        this.field7310051 = field7310051;
    }

    public String getField7410051() {
        return field7410051;
    }

    public void setField7410051(String field7410051) {
        this.field7410051 = field7410051;
    }

    public String getField7510051() {
        return field7510051;
    }

    public void setField7510051(String field7510051) {
        this.field7510051 = field7510051;
    }

    public String getField7610051() {
        return field7610051;
    }

    public void setField7610051(String field7610051) {
        this.field7610051 = field7610051;
    }

    public String getField7710051() {
        return field7710051;
    }

    public void setField7710051(String field7710051) {
        this.field7710051 = field7710051;
    }

    public String getField7810051() {
        return field7810051;
    }

    public void setField7810051(String field7810051) {
        this.field7810051 = field7810051;
    }

    public String getField7910051() {
        return field7910051;
    }

    public void setField7910051(String field7910051) {
        this.field7910051 = field7910051;
    }

    public String getField8010051() {
        return field8010051;
    }

    public void setField8010051(String field8010051) {
        this.field8010051 = field8010051;
    }

    public String getField8110051() {
        return field8110051;
    }

    public void setField8110051(String field8110051) {
        this.field8110051 = field8110051;
    }

    public String getField8210051() {
        return field8210051;
    }

    public void setField8210051(String field8210051) {
        this.field8210051 = field8210051;
    }

    public String getField8310051() {
        return field8310051;
    }

    public void setField8310051(String field8310051) {
        this.field8310051 = field8310051;
    }

    public String getField8410051() {
        return field8410051;
    }

    public void setField8410051(String field8410051) {
        this.field8410051 = field8410051;
    }

    public String getField8510051() {
        return field8510051;
    }

    public void setField8510051(String field8510051) {
        this.field8510051 = field8510051;
    }

    public String getField8610051() {
        return field8610051;
    }

    public void setField8610051(String field8610051) {
        this.field8610051 = field8610051;
    }

    public String getField8710051() {
        return field8710051;
    }

    public void setField8710051(String field8710051) {
        this.field8710051 = field8710051;
    }

    public String getField8810051() {
        return field8810051;
    }

    public void setField8810051(String field8810051) {
        this.field8810051 = field8810051;
    }

    public String getField8910051() {
        return field8910051;
    }

    public void setField8910051(String field8910051) {
        this.field8910051 = field8910051;
    }

    public String getField9010051() {
        return field9010051;
    }

    public void setField9010051(String field9010051) {
        this.field9010051 = field9010051;
    }

    public String getField9110051() {
        return field9110051;
    }

    public void setField9110051(String field9110051) {
        this.field9110051 = field9110051;
    }

    public String getField9210051() {
        return field9210051;
    }

    public void setField9210051(String field9210051) {
        this.field9210051 = field9210051;
    }

    public String getField9310051() {
        return field9310051;
    }

    public void setField9310051(String field9310051) {
        this.field9310051 = field9310051;
    }

    public String getField9410051() {
        return field9410051;
    }

    public void setField9410051(String field9410051) {
        this.field9410051 = field9410051;
    }

    public String getField9510051() {
        return field9510051;
    }

    public void setField9510051(String field9510051) {
        this.field9510051 = field9510051;
    }

    public String getField9610051() {
        return field9610051;
    }

    public void setField9610051(String field9610051) {
        this.field9610051 = field9610051;
    }

    public String getField9710051() {
        return field9710051;
    }

    public void setField9710051(String field9710051) {
        this.field9710051 = field9710051;
    }

    public String getField9810051() {
        return field9810051;
    }

    public void setField9810051(String field9810051) {
        this.field9810051 = field9810051;
    }

    public String getField9910051() {
        return field9910051;
    }

    public void setField9910051(String field9910051) {
        this.field9910051 = field9910051;
    }

    public String getField10010051() {
        return field10010051;
    }

    public void setField10010051(String field10010051) {
        this.field10010051 = field10010051;
    }

    @Override
    public String toString() {
        return "Ut51DTO{" +
                "field110051='" + field110051 + '\'' +
                ", field210051='" + field210051 + '\'' +
                ", field310051='" + field310051 + '\'' +
                ", field410051='" + field410051 + '\'' +
                ", field510051='" + field510051 + '\'' +
                ", field610051='" + field610051 + '\'' +
                ", field710051='" + field710051 + '\'' +
                ", field810051='" + field810051 + '\'' +
                ", field910051='" + field910051 + '\'' +
                ", field1010051='" + field1010051 + '\'' +
                ", field1110051='" + field1110051 + '\'' +
                ", field1210051='" + field1210051 + '\'' +
                ", field1310051='" + field1310051 + '\'' +
                ", field1410051='" + field1410051 + '\'' +
                ", field1510051='" + field1510051 + '\'' +
                ", field1610051='" + field1610051 + '\'' +
                ", field1710051='" + field1710051 + '\'' +
                ", field1810051='" + field1810051 + '\'' +
                ", field1910051='" + field1910051 + '\'' +
                ", field2010051='" + field2010051 + '\'' +
                ", field2110051='" + field2110051 + '\'' +
                ", field2210051='" + field2210051 + '\'' +
                ", field2310051='" + field2310051 + '\'' +
                ", field2410051='" + field2410051 + '\'' +
                ", field2510051='" + field2510051 + '\'' +
                ", field2610051='" + field2610051 + '\'' +
                ", field2710051='" + field2710051 + '\'' +
                ", field2810051='" + field2810051 + '\'' +
                ", field2910051='" + field2910051 + '\'' +
                ", field3010051='" + field3010051 + '\'' +
                ", field3110051='" + field3110051 + '\'' +
                ", field3210051='" + field3210051 + '\'' +
                ", field3310051='" + field3310051 + '\'' +
                ", field3410051='" + field3410051 + '\'' +
                ", field3510051='" + field3510051 + '\'' +
                ", field3610051='" + field3610051 + '\'' +
                ", field3710051='" + field3710051 + '\'' +
                ", field3810051='" + field3810051 + '\'' +
                ", field3910051='" + field3910051 + '\'' +
                ", field4010051='" + field4010051 + '\'' +
                ", field4110051='" + field4110051 + '\'' +
                ", field4210051='" + field4210051 + '\'' +
                ", field4310051='" + field4310051 + '\'' +
                ", field4410051='" + field4410051 + '\'' +
                ", field4510051='" + field4510051 + '\'' +
                ", field4610051='" + field4610051 + '\'' +
                ", field4710051='" + field4710051 + '\'' +
                ", field4810051='" + field4810051 + '\'' +
                ", field4910051='" + field4910051 + '\'' +
                ", field5110051='" + field5110051 + '\'' +
                ", field5210051='" + field5210051 + '\'' +
                ", field5310051='" + field5310051 + '\'' +
                ", field5410051='" + field5410051 + '\'' +
                ", field5510051='" + field5510051 + '\'' +
                ", field5610051='" + field5610051 + '\'' +
                ", field5710051='" + field5710051 + '\'' +
                ", field5810051='" + field5810051 + '\'' +
                ", field5910051='" + field5910051 + '\'' +
                ", field6010051='" + field6010051 + '\'' +
                ", field6110051='" + field6110051 + '\'' +
                ", field6210051='" + field6210051 + '\'' +
                ", field6310051='" + field6310051 + '\'' +
                ", field6410051='" + field6410051 + '\'' +
                ", field6510051='" + field6510051 + '\'' +
                ", field6610051='" + field6610051 + '\'' +
                ", field6710051='" + field6710051 + '\'' +
                ", field6810051='" + field6810051 + '\'' +
                ", field6910051='" + field6910051 + '\'' +
                ", field7010051='" + field7010051 + '\'' +
                ", field7110051='" + field7110051 + '\'' +
                ", field7210051='" + field7210051 + '\'' +
                ", field7310051='" + field7310051 + '\'' +
                ", field7410051='" + field7410051 + '\'' +
                ", field7510051='" + field7510051 + '\'' +
                ", field7610051='" + field7610051 + '\'' +
                ", field7710051='" + field7710051 + '\'' +
                ", field7810051='" + field7810051 + '\'' +
                ", field7910051='" + field7910051 + '\'' +
                ", field8010051='" + field8010051 + '\'' +
                ", field8110051='" + field8110051 + '\'' +
                ", field8210051='" + field8210051 + '\'' +
                ", field8310051='" + field8310051 + '\'' +
                ", field8410051='" + field8410051 + '\'' +
                ", field8510051='" + field8510051 + '\'' +
                ", field8610051='" + field8610051 + '\'' +
                ", field8710051='" + field8710051 + '\'' +
                ", field8810051='" + field8810051 + '\'' +
                ", field8910051='" + field8910051 + '\'' +
                ", field9010051='" + field9010051 + '\'' +
                ", field9110051='" + field9110051 + '\'' +
                ", field9210051='" + field9210051 + '\'' +
                ", field9310051='" + field9310051 + '\'' +
                ", field9410051='" + field9410051 + '\'' +
                ", field9510051='" + field9510051 + '\'' +
                ", field9610051='" + field9610051 + '\'' +
                ", field9710051='" + field9710051 + '\'' +
                ", field9810051='" + field9810051 + '\'' +
                ", field9910051='" + field9910051 + '\'' +
                ", field10010051='" + field10010051 + '\'' +
                '}';
    }
}
