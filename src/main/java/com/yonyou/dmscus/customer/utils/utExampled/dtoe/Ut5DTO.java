package com.yonyou.dmscus.customer.utils.utExampled.dtoe;


import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public class Ut5DTO {
    private Double primarySupplier;
    private Integer unlockedItem;
    private Float inactiveCart;
    private Integer unpublishedCategory;
    private String syncedLog;
    private Integer verifiedPrice;
    private Integer deletedBill;
    private Long unlockedStrategy;
    private Double unreviewedWarehouse;
    private BigDecimal previousPayment;
    private String unflaggedPolicy;
    private String completedHistory;
    private List activePermission;
    private Integer closedShipment;
    private Integer unpublishedGoal;
    private Float levelPlan;
    private String exportedInvoice;
    private String archivedBrand;
    private String lockedDepartment;
    private String levelFolder;
    private Integer nextAgreement;
    private Integer lockedUser;
    private BigDecimal nextPhone;
    private Double newMilestone;
    private Float archivedAgreement;
    private Set typeRole;
    private Boolean previousStock;
    private Integer securePolicy;
    private Float temporaryReview;
    private BigDecimal savedDate;
    private String publishedItem;
    private Double newReview;
    private Float encryptedShipment;
    private Double previousFile;
    private Long newDocument;
    private Set restoredResource;
    private Long savedHistory;
    private Long previousPhone;
    private Double activeGoal;
    private Boolean publicReview;
    private BigDecimal unpublishedRole;
    private String reviewedGoal;
    private Float unflaggedDate;
    private Boolean temporaryDocument;
    private Boolean maxPlan;
    private BigDecimal initialPermission;
    private List secondaryBill;
    private Float hiddenStrategy;
    private String rejectedPolicy;
    private List failedEvent;
    private List draftBrand;
    private Float newReport;
    private Integer sharedBrand;
    private Long closedRecord;
    private String unverifiedPlan;
    private String visibleAsset;
    private Set unsecureMessage;
    private Set decryptedEvent;
    private BigDecimal decryptedDiscount;
    private Double maxTask;
    private String nextContract;
    private String secondaryDate;
    private Set minTransaction;
    private BigDecimal unsecurePermission;
    private Boolean disabledActivity;
    private String enabledHistory;
    private Double decryptedLog;
    private Set permanentWarehouse;
    private String minEmail;
    private Boolean completedRecord;
    private Set hiddenReport;
    private String unreviewedPhone;
    private Float countMerchant;
    private String draftAchievement;
    private Boolean draftLog;
    private BigDecimal previousTask;
    private Integer pendingInvoice;
    private String countDepartment;
    private String updatedNotification;
    private Boolean temporaryLog;
    private Boolean archivedFeedback;
    private String deletedGoal;
    private Float deletedShipment;
    private String syncedSchedule;
    private String flaggedItem;
    private String unsecureRecord;
    private Float activeCart;
    private Double unreviewedHistory;
    private BigDecimal previousGoal;
    private String totalItem;
    private String updatedEmail;
    private BigDecimal visibleStrategy;
    private Long countTeam;
    private Long inactiveProduct;
    private List levelDocument;
    private String exportedRole;
    private Integer lockedFolder;
    private Boolean decryptedPlan;
    private Long primaryGroup;

    public Double getPrimarySupplier() {
        return primarySupplier;
    }

    public void setPrimarySupplier(Double primarySupplier) {
        this.primarySupplier = primarySupplier;
    }

    public Integer getUnlockedItem() {
        return unlockedItem;
    }

    public void setUnlockedItem(Integer unlockedItem) {
        this.unlockedItem = unlockedItem;
    }

    public Float getInactiveCart() {
        return inactiveCart;
    }

    public void setInactiveCart(Float inactiveCart) {
        this.inactiveCart = inactiveCart;
    }

    public Integer getUnpublishedCategory() {
        return unpublishedCategory;
    }

    public void setUnpublishedCategory(Integer unpublishedCategory) {
        this.unpublishedCategory = unpublishedCategory;
    }

    public String getSyncedLog() {
        return syncedLog;
    }

    public void setSyncedLog(String syncedLog) {
        this.syncedLog = syncedLog;
    }

    public Integer getVerifiedPrice() {
        return verifiedPrice;
    }

    public void setVerifiedPrice(Integer verifiedPrice) {
        this.verifiedPrice = verifiedPrice;
    }

    public Integer getDeletedBill() {
        return deletedBill;
    }

    public void setDeletedBill(Integer deletedBill) {
        this.deletedBill = deletedBill;
    }

    public Long getUnlockedStrategy() {
        return unlockedStrategy;
    }

    public void setUnlockedStrategy(Long unlockedStrategy) {
        this.unlockedStrategy = unlockedStrategy;
    }

    public Double getUnreviewedWarehouse() {
        return unreviewedWarehouse;
    }

    public void setUnreviewedWarehouse(Double unreviewedWarehouse) {
        this.unreviewedWarehouse = unreviewedWarehouse;
    }

    public BigDecimal getPreviousPayment() {
        return previousPayment;
    }

    public void setPreviousPayment(BigDecimal previousPayment) {
        this.previousPayment = previousPayment;
    }

    public String getUnflaggedPolicy() {
        return unflaggedPolicy;
    }

    public void setUnflaggedPolicy(String unflaggedPolicy) {
        this.unflaggedPolicy = unflaggedPolicy;
    }

    public String getCompletedHistory() {
        return completedHistory;
    }

    public void setCompletedHistory(String completedHistory) {
        this.completedHistory = completedHistory;
    }

    public List getActivePermission() {
        return activePermission;
    }

    public void setActivePermission(List activePermission) {
        this.activePermission = activePermission;
    }

    public Integer getClosedShipment() {
        return closedShipment;
    }

    public void setClosedShipment(Integer closedShipment) {
        this.closedShipment = closedShipment;
    }

    public Integer getUnpublishedGoal() {
        return unpublishedGoal;
    }

    public void setUnpublishedGoal(Integer unpublishedGoal) {
        this.unpublishedGoal = unpublishedGoal;
    }

    public Float getLevelPlan() {
        return levelPlan;
    }

    public void setLevelPlan(Float levelPlan) {
        this.levelPlan = levelPlan;
    }

    public String getExportedInvoice() {
        return exportedInvoice;
    }

    public void setExportedInvoice(String exportedInvoice) {
        this.exportedInvoice = exportedInvoice;
    }

    public String getArchivedBrand() {
        return archivedBrand;
    }

    public void setArchivedBrand(String archivedBrand) {
        this.archivedBrand = archivedBrand;
    }

    public String getLockedDepartment() {
        return lockedDepartment;
    }

    public void setLockedDepartment(String lockedDepartment) {
        this.lockedDepartment = lockedDepartment;
    }

    public String getLevelFolder() {
        return levelFolder;
    }

    public void setLevelFolder(String levelFolder) {
        this.levelFolder = levelFolder;
    }

    public Integer getNextAgreement() {
        return nextAgreement;
    }

    public void setNextAgreement(Integer nextAgreement) {
        this.nextAgreement = nextAgreement;
    }

    public Integer getLockedUser() {
        return lockedUser;
    }

    public void setLockedUser(Integer lockedUser) {
        this.lockedUser = lockedUser;
    }

    public BigDecimal getNextPhone() {
        return nextPhone;
    }

    public void setNextPhone(BigDecimal nextPhone) {
        this.nextPhone = nextPhone;
    }

    public Double getNewMilestone() {
        return newMilestone;
    }

    public void setNewMilestone(Double newMilestone) {
        this.newMilestone = newMilestone;
    }

    public Float getArchivedAgreement() {
        return archivedAgreement;
    }

    public void setArchivedAgreement(Float archivedAgreement) {
        this.archivedAgreement = archivedAgreement;
    }

    public Set getTypeRole() {
        return typeRole;
    }

    public void setTypeRole(Set typeRole) {
        this.typeRole = typeRole;
    }

    public Boolean getPreviousStock() {
        return previousStock;
    }

    public void setPreviousStock(Boolean previousStock) {
        this.previousStock = previousStock;
    }

    public Integer getSecurePolicy() {
        return securePolicy;
    }

    public void setSecurePolicy(Integer securePolicy) {
        this.securePolicy = securePolicy;
    }

    public Float getTemporaryReview() {
        return temporaryReview;
    }

    public void setTemporaryReview(Float temporaryReview) {
        this.temporaryReview = temporaryReview;
    }

    public BigDecimal getSavedDate() {
        return savedDate;
    }

    public void setSavedDate(BigDecimal savedDate) {
        this.savedDate = savedDate;
    }

    public String getPublishedItem() {
        return publishedItem;
    }

    public void setPublishedItem(String publishedItem) {
        this.publishedItem = publishedItem;
    }

    public Double getNewReview() {
        return newReview;
    }

    public void setNewReview(Double newReview) {
        this.newReview = newReview;
    }

    public Float getEncryptedShipment() {
        return encryptedShipment;
    }

    public void setEncryptedShipment(Float encryptedShipment) {
        this.encryptedShipment = encryptedShipment;
    }

    public Double getPreviousFile() {
        return previousFile;
    }

    public void setPreviousFile(Double previousFile) {
        this.previousFile = previousFile;
    }

    public Long getNewDocument() {
        return newDocument;
    }

    public void setNewDocument(Long newDocument) {
        this.newDocument = newDocument;
    }

    public Set getRestoredResource() {
        return restoredResource;
    }

    public void setRestoredResource(Set restoredResource) {
        this.restoredResource = restoredResource;
    }

    public Long getSavedHistory() {
        return savedHistory;
    }

    public void setSavedHistory(Long savedHistory) {
        this.savedHistory = savedHistory;
    }

    public Long getPreviousPhone() {
        return previousPhone;
    }

    public void setPreviousPhone(Long previousPhone) {
        this.previousPhone = previousPhone;
    }

    public Double getActiveGoal() {
        return activeGoal;
    }

    public void setActiveGoal(Double activeGoal) {
        this.activeGoal = activeGoal;
    }

    public Boolean getPublicReview() {
        return publicReview;
    }

    public void setPublicReview(Boolean publicReview) {
        this.publicReview = publicReview;
    }

    public BigDecimal getUnpublishedRole() {
        return unpublishedRole;
    }

    public void setUnpublishedRole(BigDecimal unpublishedRole) {
        this.unpublishedRole = unpublishedRole;
    }

    public String getReviewedGoal() {
        return reviewedGoal;
    }

    public void setReviewedGoal(String reviewedGoal) {
        this.reviewedGoal = reviewedGoal;
    }

    public Float getUnflaggedDate() {
        return unflaggedDate;
    }

    public void setUnflaggedDate(Float unflaggedDate) {
        this.unflaggedDate = unflaggedDate;
    }

    public Boolean getTemporaryDocument() {
        return temporaryDocument;
    }

    public void setTemporaryDocument(Boolean temporaryDocument) {
        this.temporaryDocument = temporaryDocument;
    }

    public Boolean getMaxPlan() {
        return maxPlan;
    }

    public void setMaxPlan(Boolean maxPlan) {
        this.maxPlan = maxPlan;
    }

    public BigDecimal getInitialPermission() {
        return initialPermission;
    }

    public void setInitialPermission(BigDecimal initialPermission) {
        this.initialPermission = initialPermission;
    }

    public List getSecondaryBill() {
        return secondaryBill;
    }

    public void setSecondaryBill(List secondaryBill) {
        this.secondaryBill = secondaryBill;
    }

    public Float getHiddenStrategy() {
        return hiddenStrategy;
    }

    public void setHiddenStrategy(Float hiddenStrategy) {
        this.hiddenStrategy = hiddenStrategy;
    }

    public String getRejectedPolicy() {
        return rejectedPolicy;
    }

    public void setRejectedPolicy(String rejectedPolicy) {
        this.rejectedPolicy = rejectedPolicy;
    }

    public List getFailedEvent() {
        return failedEvent;
    }

    public void setFailedEvent(List failedEvent) {
        this.failedEvent = failedEvent;
    }

    public List getDraftBrand() {
        return draftBrand;
    }

    public void setDraftBrand(List draftBrand) {
        this.draftBrand = draftBrand;
    }

    public Float getNewReport() {
        return newReport;
    }

    public void setNewReport(Float newReport) {
        this.newReport = newReport;
    }

    public Integer getSharedBrand() {
        return sharedBrand;
    }

    public void setSharedBrand(Integer sharedBrand) {
        this.sharedBrand = sharedBrand;
    }

    public Long getClosedRecord() {
        return closedRecord;
    }

    public void setClosedRecord(Long closedRecord) {
        this.closedRecord = closedRecord;
    }

    public String getUnverifiedPlan() {
        return unverifiedPlan;
    }

    public void setUnverifiedPlan(String unverifiedPlan) {
        this.unverifiedPlan = unverifiedPlan;
    }

    public String getVisibleAsset() {
        return visibleAsset;
    }

    public void setVisibleAsset(String visibleAsset) {
        this.visibleAsset = visibleAsset;
    }

    public Set getUnsecureMessage() {
        return unsecureMessage;
    }

    public void setUnsecureMessage(Set unsecureMessage) {
        this.unsecureMessage = unsecureMessage;
    }

    public Set getDecryptedEvent() {
        return decryptedEvent;
    }

    public void setDecryptedEvent(Set decryptedEvent) {
        this.decryptedEvent = decryptedEvent;
    }

    public BigDecimal getDecryptedDiscount() {
        return decryptedDiscount;
    }

    public void setDecryptedDiscount(BigDecimal decryptedDiscount) {
        this.decryptedDiscount = decryptedDiscount;
    }

    public Double getMaxTask() {
        return maxTask;
    }

    public void setMaxTask(Double maxTask) {
        this.maxTask = maxTask;
    }

    public String getNextContract() {
        return nextContract;
    }

    public void setNextContract(String nextContract) {
        this.nextContract = nextContract;
    }

    public String getSecondaryDate() {
        return secondaryDate;
    }

    public void setSecondaryDate(String secondaryDate) {
        this.secondaryDate = secondaryDate;
    }

    public Set getMinTransaction() {
        return minTransaction;
    }

    public void setMinTransaction(Set minTransaction) {
        this.minTransaction = minTransaction;
    }

    public BigDecimal getUnsecurePermission() {
        return unsecurePermission;
    }

    public void setUnsecurePermission(BigDecimal unsecurePermission) {
        this.unsecurePermission = unsecurePermission;
    }

    public Boolean getDisabledActivity() {
        return disabledActivity;
    }

    public void setDisabledActivity(Boolean disabledActivity) {
        this.disabledActivity = disabledActivity;
    }

    public String getEnabledHistory() {
        return enabledHistory;
    }

    public void setEnabledHistory(String enabledHistory) {
        this.enabledHistory = enabledHistory;
    }

    public Double getDecryptedLog() {
        return decryptedLog;
    }

    public void setDecryptedLog(Double decryptedLog) {
        this.decryptedLog = decryptedLog;
    }

    public Set getPermanentWarehouse() {
        return permanentWarehouse;
    }

    public void setPermanentWarehouse(Set permanentWarehouse) {
        this.permanentWarehouse = permanentWarehouse;
    }

    public String getMinEmail() {
        return minEmail;
    }

    public void setMinEmail(String minEmail) {
        this.minEmail = minEmail;
    }

    public Boolean getCompletedRecord() {
        return completedRecord;
    }

    public void setCompletedRecord(Boolean completedRecord) {
        this.completedRecord = completedRecord;
    }

    public Set getHiddenReport() {
        return hiddenReport;
    }

    public void setHiddenReport(Set hiddenReport) {
        this.hiddenReport = hiddenReport;
    }

    public String getUnreviewedPhone() {
        return unreviewedPhone;
    }

    public void setUnreviewedPhone(String unreviewedPhone) {
        this.unreviewedPhone = unreviewedPhone;
    }

    public Float getCountMerchant() {
        return countMerchant;
    }

    public void setCountMerchant(Float countMerchant) {
        this.countMerchant = countMerchant;
    }

    public String getDraftAchievement() {
        return draftAchievement;
    }

    public void setDraftAchievement(String draftAchievement) {
        this.draftAchievement = draftAchievement;
    }

    public Boolean getDraftLog() {
        return draftLog;
    }

    public void setDraftLog(Boolean draftLog) {
        this.draftLog = draftLog;
    }

    public BigDecimal getPreviousTask() {
        return previousTask;
    }

    public void setPreviousTask(BigDecimal previousTask) {
        this.previousTask = previousTask;
    }

    public Integer getPendingInvoice() {
        return pendingInvoice;
    }

    public void setPendingInvoice(Integer pendingInvoice) {
        this.pendingInvoice = pendingInvoice;
    }

    public String getCountDepartment() {
        return countDepartment;
    }

    public void setCountDepartment(String countDepartment) {
        this.countDepartment = countDepartment;
    }

    public String getUpdatedNotification() {
        return updatedNotification;
    }

    public void setUpdatedNotification(String updatedNotification) {
        this.updatedNotification = updatedNotification;
    }

    public Boolean getTemporaryLog() {
        return temporaryLog;
    }

    public void setTemporaryLog(Boolean temporaryLog) {
        this.temporaryLog = temporaryLog;
    }

    public Boolean getArchivedFeedback() {
        return archivedFeedback;
    }

    public void setArchivedFeedback(Boolean archivedFeedback) {
        this.archivedFeedback = archivedFeedback;
    }

    public String getDeletedGoal() {
        return deletedGoal;
    }

    public void setDeletedGoal(String deletedGoal) {
        this.deletedGoal = deletedGoal;
    }

    public Float getDeletedShipment() {
        return deletedShipment;
    }

    public void setDeletedShipment(Float deletedShipment) {
        this.deletedShipment = deletedShipment;
    }

    public String getSyncedSchedule() {
        return syncedSchedule;
    }

    public void setSyncedSchedule(String syncedSchedule) {
        this.syncedSchedule = syncedSchedule;
    }

    public String getFlaggedItem() {
        return flaggedItem;
    }

    public void setFlaggedItem(String flaggedItem) {
        this.flaggedItem = flaggedItem;
    }

    public String getUnsecureRecord() {
        return unsecureRecord;
    }

    public void setUnsecureRecord(String unsecureRecord) {
        this.unsecureRecord = unsecureRecord;
    }

    public Float getActiveCart() {
        return activeCart;
    }

    public void setActiveCart(Float activeCart) {
        this.activeCart = activeCart;
    }

    public Double getUnreviewedHistory() {
        return unreviewedHistory;
    }

    public void setUnreviewedHistory(Double unreviewedHistory) {
        this.unreviewedHistory = unreviewedHistory;
    }

    public BigDecimal getPreviousGoal() {
        return previousGoal;
    }

    public void setPreviousGoal(BigDecimal previousGoal) {
        this.previousGoal = previousGoal;
    }

    public String getTotalItem() {
        return totalItem;
    }

    public void setTotalItem(String totalItem) {
        this.totalItem = totalItem;
    }

    public String getUpdatedEmail() {
        return updatedEmail;
    }

    public void setUpdatedEmail(String updatedEmail) {
        this.updatedEmail = updatedEmail;
    }

    public BigDecimal getVisibleStrategy() {
        return visibleStrategy;
    }

    public void setVisibleStrategy(BigDecimal visibleStrategy) {
        this.visibleStrategy = visibleStrategy;
    }

    public Long getCountTeam() {
        return countTeam;
    }

    public void setCountTeam(Long countTeam) {
        this.countTeam = countTeam;
    }

    public Long getInactiveProduct() {
        return inactiveProduct;
    }

    public void setInactiveProduct(Long inactiveProduct) {
        this.inactiveProduct = inactiveProduct;
    }

    public List getLevelDocument() {
        return levelDocument;
    }

    public void setLevelDocument(List levelDocument) {
        this.levelDocument = levelDocument;
    }

    public String getExportedRole() {
        return exportedRole;
    }

    public void setExportedRole(String exportedRole) {
        this.exportedRole = exportedRole;
    }

    public Integer getLockedFolder() {
        return lockedFolder;
    }

    public void setLockedFolder(Integer lockedFolder) {
        this.lockedFolder = lockedFolder;
    }

    public Boolean getDecryptedPlan() {
        return decryptedPlan;
    }

    public void setDecryptedPlan(Boolean decryptedPlan) {
        this.decryptedPlan = decryptedPlan;
    }

    public Long getPrimaryGroup() {
        return primaryGroup;
    }

    public void setPrimaryGroup(Long primaryGroup) {
        this.primaryGroup = primaryGroup;
    }
}
