package com.yonyou.dmscus.customer.utils.ai;

import com.alibaba.fastjson.JSON;
import com.yonyou.dmscloud.framework.util.redis.RedisClient;
import com.yonyou.dmscloud.function.utils.jsonSerializer.JSONUtil;
import com.yonyou.dmscus.customer.configuration.NewTelecomConfig;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.feign.TechServiceClient;
import com.yonyou.dmscus.customer.utils.HttpClientUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@Component
public class NewTelecomHelper {

    public static final Logger logger = LoggerFactory.getLogger(NewTelecomHelper.class);
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private NewTelecomConfig newTelecomConfig;
    /**协同报表库 feign 调用客户端*/
    @Resource
    private TechServiceClient techServiceClient;

    /**
     * 获取电信新平台token并存入redis
     */
    public void setTokenToRedis() {
        String obtainTokenUrl = newTelecomConfig.getBasicUrl() + newTelecomConfig.getAuthenticate() + "?appkey=" + newTelecomConfig.getAppKey()
                + "&secret=" + newTelecomConfig.getSecret();
        try {
            logger.info("新电信平台获取请求Url：{}", obtainTokenUrl);
            String result = HttpClientUtil.doGet(obtainTokenUrl, null, null);
            Map<String, Object> obtainTokenResMap = JSONUtil.jsonToMap(JSONUtil.objectToJson(result));
            logger.info("新电信平台获取token响应：{}", obtainTokenResMap);
            if (Objects.equals(obtainTokenResMap.get("code"), 0)) {
                Map<String, Object> jsonToMap = JSONUtil.jsonToMap(JSON.toJSONString(obtainTokenResMap.get("result")));
                redisClient.set(CommonConstants.NEW_TELECOM_TOKEN_KEY, jsonToMap.get("access_token")
                        , Long.parseLong(jsonToMap.get("expire_in").toString()) * 1000);
                logger.info("存入redis成功");
            }
        } catch (IOException e) {
            logger.error("新电信平台获取token异常:", e);
        }
    }

    public String getToken() {

        Map<String, Object> map = techServiceClient.registerGetToken();
        logger.info("=====map:{}", JSON.toJSONString(map));
        String data = map.get("data").toString();
        return data.substring(data.indexOf('=')+1, data.length()-1);
    }

    public String httpPost(String strUrl, String param) throws IOException {
        String serviceUrl = newTelecomConfig.getBasicUrl() + strUrl + "?access_token="+ getToken();
        logger.info("=====serviceUrl:{}", serviceUrl);
        String returnStr = HttpClientUtil.doPost(serviceUrl, param, getHeader());
        if(returnStr.contains("没有找到和access_token匹配的数据")){
            logger.info("token过期 重新获取");
            setTokenToRedis();
            String serviceUrlSec = newTelecomConfig.getBasicUrl() + strUrl + "?access_token="+ getToken();
            logger.info("=====serviceUrl:{}", serviceUrlSec);
            return HttpClientUtil.doPost(serviceUrlSec, param, getHeader());
        }
        return returnStr;
    }

    private Map<String, String> getHeader() {
        HashMap<String, String> header = new HashMap<>();
        String requestId = getRequestId();
        logger.info("本次请求x-request-id:{}",requestId);
        header.put("x-request-id", requestId);
        return header;
    }

    /**
     * 客户发送请求时生成的一个随机数，长度为32位，可包含数字和大小写字母。
     **/
    private String getRequestId() {
        return UUID.randomUUID().toString().replace("-", "");
    }



}
