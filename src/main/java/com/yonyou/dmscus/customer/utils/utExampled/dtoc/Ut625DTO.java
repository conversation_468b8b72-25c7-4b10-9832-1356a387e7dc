package com.yonyou.dmscus.customer.utils.utExampled.dtoc;

public class Ut625DTO {

    private String field110052;
    private String field210052;
    private String field310052;
    private String field410052;
    private String field510052;
    private String field610052;
    private String field710052;
    private String field810052;
    private String field910052;
    private String field1010052;
    private String field1110052;
    private String field1210052;
    private String field1310052;
    private String field1410052;
    private String field1510052;
    private String field1610052;
    private String field1710052;
    private String field1810052;
    private String field1910052;
    private String field2010052;
    private String field2110052;
    private String field2210052;
    private String field2310052;
    private String field2410052;
    private String field2510052;
    private String field2610052;
    private String field2710052;
    private String field2810052;
    private String field2910052;
    private String field3010052;
    private String field3110052;
    private String field3210052;
    private String field3310052;
    private String field3410052;
    private String field3510052;
    private String field3610052;
    private String field3710052;
    private String field3810052;
    private String field3910052;
    private String field4010052;
    private String field4110052;
    private String field4210052;
    private String field4310052;
    private String field4410052;
    private String field4510052;
    private String field4610052;
    private String field4710052;
    private String field4810052;
    private String field4910052;
    private String field5110052;
    private String field5210052;
    private String field5310052;
    private String field5410052;
    private String field5510052;
    private String field5610052;
    private String field5710052;
    private String field5810052;
    private String field5910052;
    private String field6010052;
    private String field6110052;
    private String field6210052;
    private String field6310052;
    private String field6410052;
    private String field6510052;
    private String field6610052;
    private String field6710052;
    private String field6810052;
    private String field6910052;
    private String field7010052;
    private String field7110052;
    private String field7210052;
    private String field7310052;
    private String field7410052;
    private String field7510052;
    private String field7610052;
    private String field7710052;
    private String field7810052;
    private String field7910052;
    private String field8010052;
    private String field8110052;
    private String field8210052;
    private String field8310052;
    private String field8410052;
    private String field8510052;
    private String field8610052;
    private String field8710052;
    private String field8810052;
    private String field8910052;
    private String field9010052;
    private String field9110052;
    private String field9210052;
    private String field9310052;
    private String field9410052;
    private String field9510052;
    private String field9610052;
    private String field9710052;
    private String field9810052;
    private String field9910052;
    private String field10010052;

    public String getField110052() {
        return field110052;
    }

    public void setField110052(String field110052) {
        this.field110052 = field110052;
    }

    public String getField210052() {
        return field210052;
    }

    public void setField210052(String field210052) {
        this.field210052 = field210052;
    }

    public String getField310052() {
        return field310052;
    }

    public void setField310052(String field310052) {
        this.field310052 = field310052;
    }

    public String getField410052() {
        return field410052;
    }

    public void setField410052(String field410052) {
        this.field410052 = field410052;
    }

    public String getField510052() {
        return field510052;
    }

    public void setField510052(String field510052) {
        this.field510052 = field510052;
    }

    public String getField610052() {
        return field610052;
    }

    public void setField610052(String field610052) {
        this.field610052 = field610052;
    }

    public String getField710052() {
        return field710052;
    }

    public void setField710052(String field710052) {
        this.field710052 = field710052;
    }

    public String getField810052() {
        return field810052;
    }

    public void setField810052(String field810052) {
        this.field810052 = field810052;
    }

    public String getField910052() {
        return field910052;
    }

    public void setField910052(String field910052) {
        this.field910052 = field910052;
    }

    public String getField1010052() {
        return field1010052;
    }

    public void setField1010052(String field1010052) {
        this.field1010052 = field1010052;
    }

    public String getField1110052() {
        return field1110052;
    }

    public void setField1110052(String field1110052) {
        this.field1110052 = field1110052;
    }

    public String getField1210052() {
        return field1210052;
    }

    public void setField1210052(String field1210052) {
        this.field1210052 = field1210052;
    }

    public String getField1310052() {
        return field1310052;
    }

    public void setField1310052(String field1310052) {
        this.field1310052 = field1310052;
    }

    public String getField1410052() {
        return field1410052;
    }

    public void setField1410052(String field1410052) {
        this.field1410052 = field1410052;
    }

    public String getField1510052() {
        return field1510052;
    }

    public void setField1510052(String field1510052) {
        this.field1510052 = field1510052;
    }

    public String getField1610052() {
        return field1610052;
    }

    public void setField1610052(String field1610052) {
        this.field1610052 = field1610052;
    }

    public String getField1710052() {
        return field1710052;
    }

    public void setField1710052(String field1710052) {
        this.field1710052 = field1710052;
    }

    public String getField1810052() {
        return field1810052;
    }

    public void setField1810052(String field1810052) {
        this.field1810052 = field1810052;
    }

    public String getField1910052() {
        return field1910052;
    }

    public void setField1910052(String field1910052) {
        this.field1910052 = field1910052;
    }

    public String getField2010052() {
        return field2010052;
    }

    public void setField2010052(String field2010052) {
        this.field2010052 = field2010052;
    }

    public String getField2110052() {
        return field2110052;
    }

    public void setField2110052(String field2110052) {
        this.field2110052 = field2110052;
    }

    public String getField2210052() {
        return field2210052;
    }

    public void setField2210052(String field2210052) {
        this.field2210052 = field2210052;
    }

    public String getField2310052() {
        return field2310052;
    }

    public void setField2310052(String field2310052) {
        this.field2310052 = field2310052;
    }

    public String getField2410052() {
        return field2410052;
    }

    public void setField2410052(String field2410052) {
        this.field2410052 = field2410052;
    }

    public String getField2510052() {
        return field2510052;
    }

    public void setField2510052(String field2510052) {
        this.field2510052 = field2510052;
    }

    public String getField2610052() {
        return field2610052;
    }

    public void setField2610052(String field2610052) {
        this.field2610052 = field2610052;
    }

    public String getField2710052() {
        return field2710052;
    }

    public void setField2710052(String field2710052) {
        this.field2710052 = field2710052;
    }

    public String getField2810052() {
        return field2810052;
    }

    public void setField2810052(String field2810052) {
        this.field2810052 = field2810052;
    }

    public String getField2910052() {
        return field2910052;
    }

    public void setField2910052(String field2910052) {
        this.field2910052 = field2910052;
    }

    public String getField3010052() {
        return field3010052;
    }

    public void setField3010052(String field3010052) {
        this.field3010052 = field3010052;
    }

    public String getField3110052() {
        return field3110052;
    }

    public void setField3110052(String field3110052) {
        this.field3110052 = field3110052;
    }

    public String getField3210052() {
        return field3210052;
    }

    public void setField3210052(String field3210052) {
        this.field3210052 = field3210052;
    }

    public String getField3310052() {
        return field3310052;
    }

    public void setField3310052(String field3310052) {
        this.field3310052 = field3310052;
    }

    public String getField3410052() {
        return field3410052;
    }

    public void setField3410052(String field3410052) {
        this.field3410052 = field3410052;
    }

    public String getField3510052() {
        return field3510052;
    }

    public void setField3510052(String field3510052) {
        this.field3510052 = field3510052;
    }

    public String getField3610052() {
        return field3610052;
    }

    public void setField3610052(String field3610052) {
        this.field3610052 = field3610052;
    }

    public String getField3710052() {
        return field3710052;
    }

    public void setField3710052(String field3710052) {
        this.field3710052 = field3710052;
    }

    public String getField3810052() {
        return field3810052;
    }

    public void setField3810052(String field3810052) {
        this.field3810052 = field3810052;
    }

    public String getField3910052() {
        return field3910052;
    }

    public void setField3910052(String field3910052) {
        this.field3910052 = field3910052;
    }

    public String getField4010052() {
        return field4010052;
    }

    public void setField4010052(String field4010052) {
        this.field4010052 = field4010052;
    }

    public String getField4110052() {
        return field4110052;
    }

    public void setField4110052(String field4110052) {
        this.field4110052 = field4110052;
    }

    public String getField4210052() {
        return field4210052;
    }

    public void setField4210052(String field4210052) {
        this.field4210052 = field4210052;
    }

    public String getField4310052() {
        return field4310052;
    }

    public void setField4310052(String field4310052) {
        this.field4310052 = field4310052;
    }

    public String getField4410052() {
        return field4410052;
    }

    public void setField4410052(String field4410052) {
        this.field4410052 = field4410052;
    }

    public String getField4510052() {
        return field4510052;
    }

    public void setField4510052(String field4510052) {
        this.field4510052 = field4510052;
    }

    public String getField4610052() {
        return field4610052;
    }

    public void setField4610052(String field4610052) {
        this.field4610052 = field4610052;
    }

    public String getField4710052() {
        return field4710052;
    }

    public void setField4710052(String field4710052) {
        this.field4710052 = field4710052;
    }

    public String getField4810052() {
        return field4810052;
    }

    public void setField4810052(String field4810052) {
        this.field4810052 = field4810052;
    }

    public String getField4910052() {
        return field4910052;
    }

    public void setField4910052(String field4910052) {
        this.field4910052 = field4910052;
    }

    public String getField5110052() {
        return field5110052;
    }

    public void setField5110052(String field5110052) {
        this.field5110052 = field5110052;
    }

    public String getField5210052() {
        return field5210052;
    }

    public void setField5210052(String field5210052) {
        this.field5210052 = field5210052;
    }

    public String getField5310052() {
        return field5310052;
    }

    public void setField5310052(String field5310052) {
        this.field5310052 = field5310052;
    }

    public String getField5410052() {
        return field5410052;
    }

    public void setField5410052(String field5410052) {
        this.field5410052 = field5410052;
    }

    public String getField5510052() {
        return field5510052;
    }

    public void setField5510052(String field5510052) {
        this.field5510052 = field5510052;
    }

    public String getField5610052() {
        return field5610052;
    }

    public void setField5610052(String field5610052) {
        this.field5610052 = field5610052;
    }

    public String getField5710052() {
        return field5710052;
    }

    public void setField5710052(String field5710052) {
        this.field5710052 = field5710052;
    }

    public String getField5810052() {
        return field5810052;
    }

    public void setField5810052(String field5810052) {
        this.field5810052 = field5810052;
    }

    public String getField5910052() {
        return field5910052;
    }

    public void setField5910052(String field5910052) {
        this.field5910052 = field5910052;
    }

    public String getField6010052() {
        return field6010052;
    }

    public void setField6010052(String field6010052) {
        this.field6010052 = field6010052;
    }

    public String getField6110052() {
        return field6110052;
    }

    public void setField6110052(String field6110052) {
        this.field6110052 = field6110052;
    }

    public String getField6210052() {
        return field6210052;
    }

    public void setField6210052(String field6210052) {
        this.field6210052 = field6210052;
    }

    public String getField6310052() {
        return field6310052;
    }

    public void setField6310052(String field6310052) {
        this.field6310052 = field6310052;
    }

    public String getField6410052() {
        return field6410052;
    }

    public void setField6410052(String field6410052) {
        this.field6410052 = field6410052;
    }

    public String getField6510052() {
        return field6510052;
    }

    public void setField6510052(String field6510052) {
        this.field6510052 = field6510052;
    }

    public String getField6610052() {
        return field6610052;
    }

    public void setField6610052(String field6610052) {
        this.field6610052 = field6610052;
    }

    public String getField6710052() {
        return field6710052;
    }

    public void setField6710052(String field6710052) {
        this.field6710052 = field6710052;
    }

    public String getField6810052() {
        return field6810052;
    }

    public void setField6810052(String field6810052) {
        this.field6810052 = field6810052;
    }

    public String getField6910052() {
        return field6910052;
    }

    public void setField6910052(String field6910052) {
        this.field6910052 = field6910052;
    }

    public String getField7010052() {
        return field7010052;
    }

    public void setField7010052(String field7010052) {
        this.field7010052 = field7010052;
    }

    public String getField7110052() {
        return field7110052;
    }

    public void setField7110052(String field7110052) {
        this.field7110052 = field7110052;
    }

    public String getField7210052() {
        return field7210052;
    }

    public void setField7210052(String field7210052) {
        this.field7210052 = field7210052;
    }

    public String getField7310052() {
        return field7310052;
    }

    public void setField7310052(String field7310052) {
        this.field7310052 = field7310052;
    }

    public String getField7410052() {
        return field7410052;
    }

    public void setField7410052(String field7410052) {
        this.field7410052 = field7410052;
    }

    public String getField7510052() {
        return field7510052;
    }

    public void setField7510052(String field7510052) {
        this.field7510052 = field7510052;
    }

    public String getField7610052() {
        return field7610052;
    }

    public void setField7610052(String field7610052) {
        this.field7610052 = field7610052;
    }

    public String getField7710052() {
        return field7710052;
    }

    public void setField7710052(String field7710052) {
        this.field7710052 = field7710052;
    }

    public String getField7810052() {
        return field7810052;
    }

    public void setField7810052(String field7810052) {
        this.field7810052 = field7810052;
    }

    public String getField7910052() {
        return field7910052;
    }

    public void setField7910052(String field7910052) {
        this.field7910052 = field7910052;
    }

    public String getField8010052() {
        return field8010052;
    }

    public void setField8010052(String field8010052) {
        this.field8010052 = field8010052;
    }

    public String getField8110052() {
        return field8110052;
    }

    public void setField8110052(String field8110052) {
        this.field8110052 = field8110052;
    }

    public String getField8210052() {
        return field8210052;
    }

    public void setField8210052(String field8210052) {
        this.field8210052 = field8210052;
    }

    public String getField8310052() {
        return field8310052;
    }

    public void setField8310052(String field8310052) {
        this.field8310052 = field8310052;
    }

    public String getField8410052() {
        return field8410052;
    }

    public void setField8410052(String field8410052) {
        this.field8410052 = field8410052;
    }

    public String getField8510052() {
        return field8510052;
    }

    public void setField8510052(String field8510052) {
        this.field8510052 = field8510052;
    }

    public String getField8610052() {
        return field8610052;
    }

    public void setField8610052(String field8610052) {
        this.field8610052 = field8610052;
    }

    public String getField8710052() {
        return field8710052;
    }

    public void setField8710052(String field8710052) {
        this.field8710052 = field8710052;
    }

    public String getField8810052() {
        return field8810052;
    }

    public void setField8810052(String field8810052) {
        this.field8810052 = field8810052;
    }

    public String getField8910052() {
        return field8910052;
    }

    public void setField8910052(String field8910052) {
        this.field8910052 = field8910052;
    }

    public String getField9010052() {
        return field9010052;
    }

    public void setField9010052(String field9010052) {
        this.field9010052 = field9010052;
    }

    public String getField9110052() {
        return field9110052;
    }

    public void setField9110052(String field9110052) {
        this.field9110052 = field9110052;
    }

    public String getField9210052() {
        return field9210052;
    }

    public void setField9210052(String field9210052) {
        this.field9210052 = field9210052;
    }

    public String getField9310052() {
        return field9310052;
    }

    public void setField9310052(String field9310052) {
        this.field9310052 = field9310052;
    }

    public String getField9410052() {
        return field9410052;
    }

    public void setField9410052(String field9410052) {
        this.field9410052 = field9410052;
    }

    public String getField9510052() {
        return field9510052;
    }

    public void setField9510052(String field9510052) {
        this.field9510052 = field9510052;
    }

    public String getField9610052() {
        return field9610052;
    }

    public void setField9610052(String field9610052) {
        this.field9610052 = field9610052;
    }

    public String getField9710052() {
        return field9710052;
    }

    public void setField9710052(String field9710052) {
        this.field9710052 = field9710052;
    }

    public String getField9810052() {
        return field9810052;
    }

    public void setField9810052(String field9810052) {
        this.field9810052 = field9810052;
    }

    public String getField9910052() {
        return field9910052;
    }

    public void setField9910052(String field9910052) {
        this.field9910052 = field9910052;
    }

    public String getField10010052() {
        return field10010052;
    }

    public void setField10010052(String field10010052) {
        this.field10010052 = field10010052;
    }

    @Override
    public String toString() {
        return "Ut51DTO{" +
                "field110052='" + field110052 + '\'' +
                ", field210052='" + field210052 + '\'' +
                ", field310052='" + field310052 + '\'' +
                ", field410052='" + field410052 + '\'' +
                ", field510052='" + field510052 + '\'' +
                ", field610052='" + field610052 + '\'' +
                ", field710052='" + field710052 + '\'' +
                ", field810052='" + field810052 + '\'' +
                ", field910052='" + field910052 + '\'' +
                ", field1010052='" + field1010052 + '\'' +
                ", field1110052='" + field1110052 + '\'' +
                ", field1210052='" + field1210052 + '\'' +
                ", field1310052='" + field1310052 + '\'' +
                ", field1410052='" + field1410052 + '\'' +
                ", field1510052='" + field1510052 + '\'' +
                ", field1610052='" + field1610052 + '\'' +
                ", field1710052='" + field1710052 + '\'' +
                ", field1810052='" + field1810052 + '\'' +
                ", field1910052='" + field1910052 + '\'' +
                ", field2010052='" + field2010052 + '\'' +
                ", field2110052='" + field2110052 + '\'' +
                ", field2210052='" + field2210052 + '\'' +
                ", field2310052='" + field2310052 + '\'' +
                ", field2410052='" + field2410052 + '\'' +
                ", field2510052='" + field2510052 + '\'' +
                ", field2610052='" + field2610052 + '\'' +
                ", field2710052='" + field2710052 + '\'' +
                ", field2810052='" + field2810052 + '\'' +
                ", field2910052='" + field2910052 + '\'' +
                ", field3010052='" + field3010052 + '\'' +
                ", field3110052='" + field3110052 + '\'' +
                ", field3210052='" + field3210052 + '\'' +
                ", field3310052='" + field3310052 + '\'' +
                ", field3410052='" + field3410052 + '\'' +
                ", field3510052='" + field3510052 + '\'' +
                ", field3610052='" + field3610052 + '\'' +
                ", field3710052='" + field3710052 + '\'' +
                ", field3810052='" + field3810052 + '\'' +
                ", field3910052='" + field3910052 + '\'' +
                ", field4010052='" + field4010052 + '\'' +
                ", field4110052='" + field4110052 + '\'' +
                ", field4210052='" + field4210052 + '\'' +
                ", field4310052='" + field4310052 + '\'' +
                ", field4410052='" + field4410052 + '\'' +
                ", field4510052='" + field4510052 + '\'' +
                ", field4610052='" + field4610052 + '\'' +
                ", field4710052='" + field4710052 + '\'' +
                ", field4810052='" + field4810052 + '\'' +
                ", field4910052='" + field4910052 + '\'' +
                ", field5110052='" + field5110052 + '\'' +
                ", field5210052='" + field5210052 + '\'' +
                ", field5310052='" + field5310052 + '\'' +
                ", field5410052='" + field5410052 + '\'' +
                ", field5510052='" + field5510052 + '\'' +
                ", field5610052='" + field5610052 + '\'' +
                ", field5710052='" + field5710052 + '\'' +
                ", field5810052='" + field5810052 + '\'' +
                ", field5910052='" + field5910052 + '\'' +
                ", field6010052='" + field6010052 + '\'' +
                ", field6110052='" + field6110052 + '\'' +
                ", field6210052='" + field6210052 + '\'' +
                ", field6310052='" + field6310052 + '\'' +
                ", field6410052='" + field6410052 + '\'' +
                ", field6510052='" + field6510052 + '\'' +
                ", field6610052='" + field6610052 + '\'' +
                ", field6710052='" + field6710052 + '\'' +
                ", field6810052='" + field6810052 + '\'' +
                ", field6910052='" + field6910052 + '\'' +
                ", field7010052='" + field7010052 + '\'' +
                ", field7110052='" + field7110052 + '\'' +
                ", field7210052='" + field7210052 + '\'' +
                ", field7310052='" + field7310052 + '\'' +
                ", field7410052='" + field7410052 + '\'' +
                ", field7510052='" + field7510052 + '\'' +
                ", field7610052='" + field7610052 + '\'' +
                ", field7710052='" + field7710052 + '\'' +
                ", field7810052='" + field7810052 + '\'' +
                ", field7910052='" + field7910052 + '\'' +
                ", field8010052='" + field8010052 + '\'' +
                ", field8110052='" + field8110052 + '\'' +
                ", field8210052='" + field8210052 + '\'' +
                ", field8310052='" + field8310052 + '\'' +
                ", field8410052='" + field8410052 + '\'' +
                ", field8510052='" + field8510052 + '\'' +
                ", field8610052='" + field8610052 + '\'' +
                ", field8710052='" + field8710052 + '\'' +
                ", field8810052='" + field8810052 + '\'' +
                ", field8910052='" + field8910052 + '\'' +
                ", field9010052='" + field9010052 + '\'' +
                ", field9110052='" + field9110052 + '\'' +
                ", field9210052='" + field9210052 + '\'' +
                ", field9310052='" + field9310052 + '\'' +
                ", field9410052='" + field9410052 + '\'' +
                ", field9510052='" + field9510052 + '\'' +
                ", field9610052='" + field9610052 + '\'' +
                ", field9710052='" + field9710052 + '\'' +
                ", field9810052='" + field9810052 + '\'' +
                ", field9910052='" + field9910052 + '\'' +
                ", field10010052='" + field10010052 + '\'' +
                '}';
    }
}
