package com.yonyou.dmscus.customer.utils.ai;

import java.util.Date;

import lombok.Data;

/**
 * 话单拉取子元素
 * <AUTHOR>
 *
 */
@Data
public class CallDetailsChild {
	
    /**
     *  接口返回字段
     */
	
	private String sessionId; //话单 ID
	
	private String displayNbr; //显示号码
	
	private String callerNbr; //主叫号码
	
	private String workNbr; //工作号码
	
	private String calleeNbr; //被叫号码
	
	private Long startTime; //通话开始时间
	
	private Long endTime; //呼叫结束时间
	
	private Integer duration; //通话时长（秒）
	
	private String serviceType; //呼叫类型：(gzh-caller：工作号主叫 ;  gzh-callee：工作号被叫)
	
	/**
	 * 冗余字段
	 */
	private String callId;
	
	private String ownerCode;
	
	private String ownerParCode;
	
	private String dealerCode;
	
	private Date startDate;
	
	private Date endDate;
}
