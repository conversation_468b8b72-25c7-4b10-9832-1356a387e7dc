package com.yonyou.dmscus.customer.utils;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.yonyou.dmscus.customer.util.common.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * 数据转换
 *
 */
@Component
@Slf4j
public class DataConvertUtils {
	
    private DataConvertUtils() {
		super();
	}

    /**
     * String集合转String
     * @return 
     */
    public static String stringListToString(List<String> list){
    	if(CollectionUtils.isEmpty(list)) {
    		return null;
    	}
        return String.join(",", list);
    }
    
    /**
     * String转String集合
     * @return 
     */
    public static List<String> stringToStringList(Object str){
    	if(StringUtils.isNullOrEmpty(str)) {
    		return new ArrayList<>();
    	}
        return Arrays.asList(str.toString().split(","));
    }
    
    /**
     * Integer集合转String
     * @return 
     */
    public static String integerListToString(List<Integer> list){
    	if(CollectionUtils.isEmpty(list)) {
    		return null;
    	}
    	return list.stream().filter(Objects::nonNull).map(Objects::toString).collect(Collectors.joining(","));
    }
    
    /**
     * String转Integer集合
     * @return 
     */
    public static List<Integer> stringToIntegerList(Object str){
    	if(StringUtils.isNullOrEmpty(str)) {
    		return new ArrayList<>();
    	}
    	return Arrays.stream(str.toString().split(",")).map(Integer::parseInt).collect(Collectors.toList());
    }
    
    
    /**
     * 时间格式化
     * @return 
     */
    public static Map mapDateFormat(Map<String, Object> map){
    	for (Map.Entry<String, Object> entry : map.entrySet()) {
    		try {				
    			String key = entry.getKey();
    			Object val = entry.getValue();
    			Date date = null;
    			if(val instanceof String) {
    				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.sssZ");
    				String str = val.toString();
    				if(str.length()==28) {
    					// 解析时区
    					int index = str.indexOf('+');
    					if (index < 0) {
    						index = str.indexOf('-');
    					}
    					String timeZoneString = str.substring(index);
    					// 设置时区
    					sdf.setTimeZone(TimeZone.getTimeZone("GMT" + timeZoneString));
    					// 解析日期
    					date = sdf.parse(str);
    				}
    			} else if(val instanceof Timestamp) {
    				long time = ((Timestamp) val).getTime();
    				date = new Date(time);
    			}
    			if(Objects.nonNull(date)) {
    				SimpleDateFormat shortSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    				String formatDate = shortSdf.format(date);
    				map.put(key, formatDate);
    			}
			} catch (Exception e) {
				
			}
		}
		return map;
    }
}
