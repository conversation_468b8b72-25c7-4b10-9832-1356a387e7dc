package com.yonyou.dmscus.customer.utils.utExampled.dtoe;


import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
public class Ut2DTO {
    private String activeSupplier;
    private Float savedFeedback;
    private String unverifiedBill;
    private List deletedShipment;
    private Boolean unverifiedDepartment;
    private Float unsecureCustomer;
    private List unreviewedCategory;
    private Long flaggedRecord;
    private Double rejectedMessage;
    private Integer levelRole;
    private String unsecureStrategy;
    private List publicStrategy;
    private Integer privateBrand;
    private Float reviewedInvoice;
    private String createdBill;
    private Boolean secureFeedback;
    private Set maxStock;
    private Integer createdFolder;
    private Float previousSupplier;
    private Long lockedTeam;
    private Long unsecureDocument;
    private Double unsecureNotification;
    private Long permanentHistory;
    private Boolean draftSession;
    private Float approvedResource;
    private Boolean modifiedTransaction;
    private Long initialUser;
    private BigDecimal permanentPayment;
    private String hiddenCart;
    private Boolean rejectedEmail;
    private Long closedProduct;
    private Double previousBrand;
    private Long verifiedOrder;
    private Boolean maxEvent;
    private Float statusFile;
    private Long restoredMilestone;
    private Boolean publishedBrand;
    private BigDecimal encryptedGroup;
    private Integer draftPayment;
    private String permanentGoal;
    private Set finalLog;
    private List pendingPhone;
    private BigDecimal publicInvoice;
    private Float nextOrder;
    private Integer lockedAgreement;
    private String failedStock;
    private Boolean pendingOrder;
    private Boolean secondarySchedule;
    private Set verifiedReview;
    private Boolean unreportedLog;
    private Float verifiedSubscription;
    private Long nextMessage;
    private String privateDepartment;
    private List verifiedAchievement;
    private Boolean temporaryRole;
    private BigDecimal previousName;
    private Integer lastEvent;
    private Boolean createdCategory;
    private Double firstBill;
    private Set unsecureGroup;
    private Double temporaryPermission;
    private String currentStock;
    private Long averageCart;
    private Float activeEmail;
    private Float activeCustomer;
    private Float finalUser;
    private Double pendingLog;
    private Float unflaggedItem;
    private Boolean decryptedAgreement;
    private Boolean reportedFeedback;
    private String minAchievement;
    private List minFeedback;
    private BigDecimal importedAddress;
    private Integer archivedGoal;
    private String maxBill;
    private String importedTask;
    private Double sharedTransaction;
    private Long reportedEvent;
    private Set publishedProduct;
    private Integer finalOrder;
    private Long unlockedSession;
    private Boolean newFile;
    private String unsecureFile;
    private Long secondarySupplier;
    private BigDecimal countFeedback;
    private Float unreviewedCart;
    private String firstInvoice;
    private Integer newPlan;
    private List closedProject;
    private BigDecimal successfulDocument;
    private Long updatedReport;
    private Float unflaggedBrand;
    private Integer mainHistory;
    private Boolean oldPrice;
    private Float archivedCustomer;
    private Float importedWarehouse;
    private Float exportedItem;
    private Integer unflaggedGroup;
    private Long primaryFolder;

    public String getActiveSupplier() {
        return activeSupplier;
    }

    public void setActiveSupplier(String activeSupplier) {
        this.activeSupplier = activeSupplier;
    }

    public Float getSavedFeedback() {
        return savedFeedback;
    }

    public void setSavedFeedback(Float savedFeedback) {
        this.savedFeedback = savedFeedback;
    }

    public String getUnverifiedBill() {
        return unverifiedBill;
    }

    public void setUnverifiedBill(String unverifiedBill) {
        this.unverifiedBill = unverifiedBill;
    }

    public List getDeletedShipment() {
        return deletedShipment;
    }

    public void setDeletedShipment(List deletedShipment) {
        this.deletedShipment = deletedShipment;
    }

    public Boolean getUnverifiedDepartment() {
        return unverifiedDepartment;
    }

    public void setUnverifiedDepartment(Boolean unverifiedDepartment) {
        this.unverifiedDepartment = unverifiedDepartment;
    }

    public Float getUnsecureCustomer() {
        return unsecureCustomer;
    }

    public void setUnsecureCustomer(Float unsecureCustomer) {
        this.unsecureCustomer = unsecureCustomer;
    }

    public List getUnreviewedCategory() {
        return unreviewedCategory;
    }

    public void setUnreviewedCategory(List unreviewedCategory) {
        this.unreviewedCategory = unreviewedCategory;
    }

    public Long getFlaggedRecord() {
        return flaggedRecord;
    }

    public void setFlaggedRecord(Long flaggedRecord) {
        this.flaggedRecord = flaggedRecord;
    }

    public Double getRejectedMessage() {
        return rejectedMessage;
    }

    public void setRejectedMessage(Double rejectedMessage) {
        this.rejectedMessage = rejectedMessage;
    }

    public Integer getLevelRole() {
        return levelRole;
    }

    public void setLevelRole(Integer levelRole) {
        this.levelRole = levelRole;
    }

    public String getUnsecureStrategy() {
        return unsecureStrategy;
    }

    public void setUnsecureStrategy(String unsecureStrategy) {
        this.unsecureStrategy = unsecureStrategy;
    }

    public List getPublicStrategy() {
        return publicStrategy;
    }

    public void setPublicStrategy(List publicStrategy) {
        this.publicStrategy = publicStrategy;
    }

    public Integer getPrivateBrand() {
        return privateBrand;
    }

    public void setPrivateBrand(Integer privateBrand) {
        this.privateBrand = privateBrand;
    }

    public Float getReviewedInvoice() {
        return reviewedInvoice;
    }

    public void setReviewedInvoice(Float reviewedInvoice) {
        this.reviewedInvoice = reviewedInvoice;
    }

    public String getCreatedBill() {
        return createdBill;
    }

    public void setCreatedBill(String createdBill) {
        this.createdBill = createdBill;
    }

    public Boolean getSecureFeedback() {
        return secureFeedback;
    }

    public void setSecureFeedback(Boolean secureFeedback) {
        this.secureFeedback = secureFeedback;
    }

    public Set getMaxStock() {
        return maxStock;
    }

    public void setMaxStock(Set maxStock) {
        this.maxStock = maxStock;
    }

    public Integer getCreatedFolder() {
        return createdFolder;
    }

    public void setCreatedFolder(Integer createdFolder) {
        this.createdFolder = createdFolder;
    }

    public Float getPreviousSupplier() {
        return previousSupplier;
    }

    public void setPreviousSupplier(Float previousSupplier) {
        this.previousSupplier = previousSupplier;
    }

    public Long getLockedTeam() {
        return lockedTeam;
    }

    public void setLockedTeam(Long lockedTeam) {
        this.lockedTeam = lockedTeam;
    }

    public Long getUnsecureDocument() {
        return unsecureDocument;
    }

    public void setUnsecureDocument(Long unsecureDocument) {
        this.unsecureDocument = unsecureDocument;
    }

    public Double getUnsecureNotification() {
        return unsecureNotification;
    }

    public void setUnsecureNotification(Double unsecureNotification) {
        this.unsecureNotification = unsecureNotification;
    }

    public Long getPermanentHistory() {
        return permanentHistory;
    }

    public void setPermanentHistory(Long permanentHistory) {
        this.permanentHistory = permanentHistory;
    }

    public Boolean getDraftSession() {
        return draftSession;
    }

    public void setDraftSession(Boolean draftSession) {
        this.draftSession = draftSession;
    }

    public Float getApprovedResource() {
        return approvedResource;
    }

    public void setApprovedResource(Float approvedResource) {
        this.approvedResource = approvedResource;
    }

    public Boolean getModifiedTransaction() {
        return modifiedTransaction;
    }

    public void setModifiedTransaction(Boolean modifiedTransaction) {
        this.modifiedTransaction = modifiedTransaction;
    }

    public Long getInitialUser() {
        return initialUser;
    }

    public void setInitialUser(Long initialUser) {
        this.initialUser = initialUser;
    }

    public BigDecimal getPermanentPayment() {
        return permanentPayment;
    }

    public void setPermanentPayment(BigDecimal permanentPayment) {
        this.permanentPayment = permanentPayment;
    }

    public String getHiddenCart() {
        return hiddenCart;
    }

    public void setHiddenCart(String hiddenCart) {
        this.hiddenCart = hiddenCart;
    }

    public Boolean getRejectedEmail() {
        return rejectedEmail;
    }

    public void setRejectedEmail(Boolean rejectedEmail) {
        this.rejectedEmail = rejectedEmail;
    }

    public Long getClosedProduct() {
        return closedProduct;
    }

    public void setClosedProduct(Long closedProduct) {
        this.closedProduct = closedProduct;
    }

    public Double getPreviousBrand() {
        return previousBrand;
    }

    public void setPreviousBrand(Double previousBrand) {
        this.previousBrand = previousBrand;
    }

    public Long getVerifiedOrder() {
        return verifiedOrder;
    }

    public void setVerifiedOrder(Long verifiedOrder) {
        this.verifiedOrder = verifiedOrder;
    }

    public Boolean getMaxEvent() {
        return maxEvent;
    }

    public void setMaxEvent(Boolean maxEvent) {
        this.maxEvent = maxEvent;
    }

    public Float getStatusFile() {
        return statusFile;
    }

    public void setStatusFile(Float statusFile) {
        this.statusFile = statusFile;
    }

    public Long getRestoredMilestone() {
        return restoredMilestone;
    }

    public void setRestoredMilestone(Long restoredMilestone) {
        this.restoredMilestone = restoredMilestone;
    }

    public Boolean getPublishedBrand() {
        return publishedBrand;
    }

    public void setPublishedBrand(Boolean publishedBrand) {
        this.publishedBrand = publishedBrand;
    }

    public BigDecimal getEncryptedGroup() {
        return encryptedGroup;
    }

    public void setEncryptedGroup(BigDecimal encryptedGroup) {
        this.encryptedGroup = encryptedGroup;
    }

    public Integer getDraftPayment() {
        return draftPayment;
    }

    public void setDraftPayment(Integer draftPayment) {
        this.draftPayment = draftPayment;
    }

    public String getPermanentGoal() {
        return permanentGoal;
    }

    public void setPermanentGoal(String permanentGoal) {
        this.permanentGoal = permanentGoal;
    }

    public Set getFinalLog() {
        return finalLog;
    }

    public void setFinalLog(Set finalLog) {
        this.finalLog = finalLog;
    }

    public List getPendingPhone() {
        return pendingPhone;
    }

    public void setPendingPhone(List pendingPhone) {
        this.pendingPhone = pendingPhone;
    }

    public BigDecimal getPublicInvoice() {
        return publicInvoice;
    }

    public void setPublicInvoice(BigDecimal publicInvoice) {
        this.publicInvoice = publicInvoice;
    }

    public Float getNextOrder() {
        return nextOrder;
    }

    public void setNextOrder(Float nextOrder) {
        this.nextOrder = nextOrder;
    }

    public Integer getLockedAgreement() {
        return lockedAgreement;
    }

    public void setLockedAgreement(Integer lockedAgreement) {
        this.lockedAgreement = lockedAgreement;
    }

    public String getFailedStock() {
        return failedStock;
    }

    public void setFailedStock(String failedStock) {
        this.failedStock = failedStock;
    }

    public Boolean getPendingOrder() {
        return pendingOrder;
    }

    public void setPendingOrder(Boolean pendingOrder) {
        this.pendingOrder = pendingOrder;
    }

    public Boolean getSecondarySchedule() {
        return secondarySchedule;
    }

    public void setSecondarySchedule(Boolean secondarySchedule) {
        this.secondarySchedule = secondarySchedule;
    }

    public Set getVerifiedReview() {
        return verifiedReview;
    }

    public void setVerifiedReview(Set verifiedReview) {
        this.verifiedReview = verifiedReview;
    }

    public Boolean getUnreportedLog() {
        return unreportedLog;
    }

    public void setUnreportedLog(Boolean unreportedLog) {
        this.unreportedLog = unreportedLog;
    }

    public Float getVerifiedSubscription() {
        return verifiedSubscription;
    }

    public void setVerifiedSubscription(Float verifiedSubscription) {
        this.verifiedSubscription = verifiedSubscription;
    }

    public Long getNextMessage() {
        return nextMessage;
    }

    public void setNextMessage(Long nextMessage) {
        this.nextMessage = nextMessage;
    }

    public String getPrivateDepartment() {
        return privateDepartment;
    }

    public void setPrivateDepartment(String privateDepartment) {
        this.privateDepartment = privateDepartment;
    }

    public List getVerifiedAchievement() {
        return verifiedAchievement;
    }

    public void setVerifiedAchievement(List verifiedAchievement) {
        this.verifiedAchievement = verifiedAchievement;
    }

    public Boolean getTemporaryRole() {
        return temporaryRole;
    }

    public void setTemporaryRole(Boolean temporaryRole) {
        this.temporaryRole = temporaryRole;
    }

    public BigDecimal getPreviousName() {
        return previousName;
    }

    public void setPreviousName(BigDecimal previousName) {
        this.previousName = previousName;
    }

    public Integer getLastEvent() {
        return lastEvent;
    }

    public void setLastEvent(Integer lastEvent) {
        this.lastEvent = lastEvent;
    }

    public Boolean getCreatedCategory() {
        return createdCategory;
    }

    public void setCreatedCategory(Boolean createdCategory) {
        this.createdCategory = createdCategory;
    }

    public Double getFirstBill() {
        return firstBill;
    }

    public void setFirstBill(Double firstBill) {
        this.firstBill = firstBill;
    }

    public Set getUnsecureGroup() {
        return unsecureGroup;
    }

    public void setUnsecureGroup(Set unsecureGroup) {
        this.unsecureGroup = unsecureGroup;
    }

    public Double getTemporaryPermission() {
        return temporaryPermission;
    }

    public void setTemporaryPermission(Double temporaryPermission) {
        this.temporaryPermission = temporaryPermission;
    }

    public String getCurrentStock() {
        return currentStock;
    }

    public void setCurrentStock(String currentStock) {
        this.currentStock = currentStock;
    }

    public Long getAverageCart() {
        return averageCart;
    }

    public void setAverageCart(Long averageCart) {
        this.averageCart = averageCart;
    }

    public Float getActiveEmail() {
        return activeEmail;
    }

    public void setActiveEmail(Float activeEmail) {
        this.activeEmail = activeEmail;
    }

    public Float getActiveCustomer() {
        return activeCustomer;
    }

    public void setActiveCustomer(Float activeCustomer) {
        this.activeCustomer = activeCustomer;
    }

    public Float getFinalUser() {
        return finalUser;
    }

    public void setFinalUser(Float finalUser) {
        this.finalUser = finalUser;
    }

    public Double getPendingLog() {
        return pendingLog;
    }

    public void setPendingLog(Double pendingLog) {
        this.pendingLog = pendingLog;
    }

    public Float getUnflaggedItem() {
        return unflaggedItem;
    }

    public void setUnflaggedItem(Float unflaggedItem) {
        this.unflaggedItem = unflaggedItem;
    }

    public Boolean getDecryptedAgreement() {
        return decryptedAgreement;
    }

    public void setDecryptedAgreement(Boolean decryptedAgreement) {
        this.decryptedAgreement = decryptedAgreement;
    }

    public Boolean getReportedFeedback() {
        return reportedFeedback;
    }

    public void setReportedFeedback(Boolean reportedFeedback) {
        this.reportedFeedback = reportedFeedback;
    }

    public String getMinAchievement() {
        return minAchievement;
    }

    public void setMinAchievement(String minAchievement) {
        this.minAchievement = minAchievement;
    }

    public List getMinFeedback() {
        return minFeedback;
    }

    public void setMinFeedback(List minFeedback) {
        this.minFeedback = minFeedback;
    }

    public BigDecimal getImportedAddress() {
        return importedAddress;
    }

    public void setImportedAddress(BigDecimal importedAddress) {
        this.importedAddress = importedAddress;
    }

    public Integer getArchivedGoal() {
        return archivedGoal;
    }

    public void setArchivedGoal(Integer archivedGoal) {
        this.archivedGoal = archivedGoal;
    }

    public String getMaxBill() {
        return maxBill;
    }

    public void setMaxBill(String maxBill) {
        this.maxBill = maxBill;
    }

    public String getImportedTask() {
        return importedTask;
    }

    public void setImportedTask(String importedTask) {
        this.importedTask = importedTask;
    }

    public Double getSharedTransaction() {
        return sharedTransaction;
    }

    public void setSharedTransaction(Double sharedTransaction) {
        this.sharedTransaction = sharedTransaction;
    }

    public Long getReportedEvent() {
        return reportedEvent;
    }

    public void setReportedEvent(Long reportedEvent) {
        this.reportedEvent = reportedEvent;
    }

    public Set getPublishedProduct() {
        return publishedProduct;
    }

    public void setPublishedProduct(Set publishedProduct) {
        this.publishedProduct = publishedProduct;
    }

    public Integer getFinalOrder() {
        return finalOrder;
    }

    public void setFinalOrder(Integer finalOrder) {
        this.finalOrder = finalOrder;
    }

    public Long getUnlockedSession() {
        return unlockedSession;
    }

    public void setUnlockedSession(Long unlockedSession) {
        this.unlockedSession = unlockedSession;
    }

    public Boolean getNewFile() {
        return newFile;
    }

    public void setNewFile(Boolean newFile) {
        this.newFile = newFile;
    }

    public String getUnsecureFile() {
        return unsecureFile;
    }

    public void setUnsecureFile(String unsecureFile) {
        this.unsecureFile = unsecureFile;
    }

    public Long getSecondarySupplier() {
        return secondarySupplier;
    }

    public void setSecondarySupplier(Long secondarySupplier) {
        this.secondarySupplier = secondarySupplier;
    }

    public BigDecimal getCountFeedback() {
        return countFeedback;
    }

    public void setCountFeedback(BigDecimal countFeedback) {
        this.countFeedback = countFeedback;
    }

    public Float getUnreviewedCart() {
        return unreviewedCart;
    }

    public void setUnreviewedCart(Float unreviewedCart) {
        this.unreviewedCart = unreviewedCart;
    }

    public String getFirstInvoice() {
        return firstInvoice;
    }

    public void setFirstInvoice(String firstInvoice) {
        this.firstInvoice = firstInvoice;
    }

    public Integer getNewPlan() {
        return newPlan;
    }

    public void setNewPlan(Integer newPlan) {
        this.newPlan = newPlan;
    }

    public List getClosedProject() {
        return closedProject;
    }

    public void setClosedProject(List closedProject) {
        this.closedProject = closedProject;
    }

    public BigDecimal getSuccessfulDocument() {
        return successfulDocument;
    }

    public void setSuccessfulDocument(BigDecimal successfulDocument) {
        this.successfulDocument = successfulDocument;
    }

    public Long getUpdatedReport() {
        return updatedReport;
    }

    public void setUpdatedReport(Long updatedReport) {
        this.updatedReport = updatedReport;
    }

    public Float getUnflaggedBrand() {
        return unflaggedBrand;
    }

    public void setUnflaggedBrand(Float unflaggedBrand) {
        this.unflaggedBrand = unflaggedBrand;
    }

    public Integer getMainHistory() {
        return mainHistory;
    }

    public void setMainHistory(Integer mainHistory) {
        this.mainHistory = mainHistory;
    }

    public Boolean getOldPrice() {
        return oldPrice;
    }

    public void setOldPrice(Boolean oldPrice) {
        this.oldPrice = oldPrice;
    }

    public Float getArchivedCustomer() {
        return archivedCustomer;
    }

    public void setArchivedCustomer(Float archivedCustomer) {
        this.archivedCustomer = archivedCustomer;
    }

    public Float getImportedWarehouse() {
        return importedWarehouse;
    }

    public void setImportedWarehouse(Float importedWarehouse) {
        this.importedWarehouse = importedWarehouse;
    }

    public Float getExportedItem() {
        return exportedItem;
    }

    public void setExportedItem(Float exportedItem) {
        this.exportedItem = exportedItem;
    }

    public Integer getUnflaggedGroup() {
        return unflaggedGroup;
    }

    public void setUnflaggedGroup(Integer unflaggedGroup) {
        this.unflaggedGroup = unflaggedGroup;
    }

    public Long getPrimaryFolder() {
        return primaryFolder;
    }

    public void setPrimaryFolder(Long primaryFolder) {
        this.primaryFolder = primaryFolder;
    }
}
