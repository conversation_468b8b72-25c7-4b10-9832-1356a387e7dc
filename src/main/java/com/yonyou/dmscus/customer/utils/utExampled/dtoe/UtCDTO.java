package com.yonyou.dmscus.customer.utils.utExampled.dtoe;


import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public class UtCDTO {

	private List verifiedDocument;
	private String unreportedCustomer;
	private Float inactiveSupplier;
	private Double lastEmail;
	private List rejectedCustomer;
	private String countSession;
	private BigDecimal minDepartment;
	private Float modifiedName;
	private Integer typeLog;
	private Integer deletedFolder;
	private Float completedSubscription;
	private Set secondaryStrategy;
	private BigDecimal permanentBrand;
	private Long unverifiedName;
	private String publishedPayment;
	private Boolean restoredPermission;
	private Long initialFolder;
	private List statusDocument;
	private Float averagePolicy;
	private Float unlockedInvoice;
	private List encryptedTransaction;
	private List lockedPlan;
	private BigDecimal lastBrand;
	private String sharedContract;
	private String restoredCoupon;
	private Set enabledActivity;
	private String levelFeedback;
	private Double unsecurePayment;
	private String openItem;
	private String unflaggedCustomer;
	private Boolean unpublishedFile;
	private Long rejectedLog;
	private Long modifiedCart;
	private Boolean draftAsset;
	private Boolean failedPolicy;
	private Boolean secondaryItem;
	private Integer publicSession;
	private List lastBill;
	private Float unlockedBill;
	private String encryptedDocument;
	private List savedTransaction;
	private Float syncedMilestone;
	private Integer encryptedFeedback;
	private BigDecimal modifiedEvent;
	private String successfulSubscription;
	private Double hiddenAddress;
	private Boolean firstBill;
	private Long unflaggedMilestone;
	private Float nextPlan;
	private Double disabledShipment;
	private Double sharedMilestone;
	private Set enabledRole;
	private List lockedActivity;
	private BigDecimal unreviewedMerchant;
	private String sharedCategory;
	private String encryptedBrand;
	private BigDecimal restoredAddress;
	private BigDecimal unflaggedAgreement;
	private Double unflaggedTransaction;
	private List enabledItem;
	private Integer publicPayment;
	private String modifiedUser;
	private Long openDate;
	private BigDecimal verifiedPolicy;
	private Double temporaryMerchant;
	private Long permanentReport;
	private Integer exportedMessage;
	private Set primaryCart;
	private Double failedPrice;
	private String unverifiedAccount;
	private Boolean draftProduct;
	private Integer syncedStock;
	private Double unpublishedAgreement;
	private Integer reportedDiscount;
	private List finalContract;
	private Double savedAccount;
	private Long averagePrice;
	private Boolean approvedPermission;
	private String finalDiscount;
	private Long loadedDepartment;
	private Double archivedFile;
	private String unflaggedAccount;
	private String previousEvent;
	private Float publicInvoice;
	private Float publicSupplier;
	private Long failedPlan;
	private String maxOrder;
	private String approvedSupplier;
	private Boolean privateFolder;
	private String unreviewedAddress;
	private BigDecimal hiddenFeedback;


	public List getVerifiedDocument() {
		return verifiedDocument;
	}

	public void setVerifiedDocument(List verifiedDocument) {
		this.verifiedDocument = verifiedDocument;
	}

	public String getUnreportedCustomer() {
		return unreportedCustomer;
	}

	public void setUnreportedCustomer(String unreportedCustomer) {
		this.unreportedCustomer = unreportedCustomer;
	}

	public Float getInactiveSupplier() {
		return inactiveSupplier;
	}

	public void setInactiveSupplier(Float inactiveSupplier) {
		this.inactiveSupplier = inactiveSupplier;
	}

	public Double getLastEmail() {
		return lastEmail;
	}

	public void setLastEmail(Double lastEmail) {
		this.lastEmail = lastEmail;
	}

	public List getRejectedCustomer() {
		return rejectedCustomer;
	}

	public void setRejectedCustomer(List rejectedCustomer) {
		this.rejectedCustomer = rejectedCustomer;
	}

	public String getCountSession() {
		return countSession;
	}

	public void setCountSession(String countSession) {
		this.countSession = countSession;
	}

	public BigDecimal getMinDepartment() {
		return minDepartment;
	}

	public void setMinDepartment(BigDecimal minDepartment) {
		this.minDepartment = minDepartment;
	}

	public Float getModifiedName() {
		return modifiedName;
	}

	public void setModifiedName(Float modifiedName) {
		this.modifiedName = modifiedName;
	}

	public Integer getTypeLog() {
		return typeLog;
	}

	public void setTypeLog(Integer typeLog) {
		this.typeLog = typeLog;
	}

	public Integer getDeletedFolder() {
		return deletedFolder;
	}

	public void setDeletedFolder(Integer deletedFolder) {
		this.deletedFolder = deletedFolder;
	}

	public Float getCompletedSubscription() {
		return completedSubscription;
	}

	public void setCompletedSubscription(Float completedSubscription) {
		this.completedSubscription = completedSubscription;
	}

	public Set getSecondaryStrategy() {
		return secondaryStrategy;
	}

	public void setSecondaryStrategy(Set secondaryStrategy) {
		this.secondaryStrategy = secondaryStrategy;
	}

	public BigDecimal getPermanentBrand() {
		return permanentBrand;
	}

	public void setPermanentBrand(BigDecimal permanentBrand) {
		this.permanentBrand = permanentBrand;
	}

	public Long getUnverifiedName() {
		return unverifiedName;
	}

	public void setUnverifiedName(Long unverifiedName) {
		this.unverifiedName = unverifiedName;
	}

	public String getPublishedPayment() {
		return publishedPayment;
	}

	public void setPublishedPayment(String publishedPayment) {
		this.publishedPayment = publishedPayment;
	}

	public Boolean getRestoredPermission() {
		return restoredPermission;
	}

	public void setRestoredPermission(Boolean restoredPermission) {
		this.restoredPermission = restoredPermission;
	}

	public Long getInitialFolder() {
		return initialFolder;
	}

	public void setInitialFolder(Long initialFolder) {
		this.initialFolder = initialFolder;
	}

	public List getStatusDocument() {
		return statusDocument;
	}

	public void setStatusDocument(List statusDocument) {
		this.statusDocument = statusDocument;
	}

	public Float getAveragePolicy() {
		return averagePolicy;
	}

	public void setAveragePolicy(Float averagePolicy) {
		this.averagePolicy = averagePolicy;
	}

	public Float getUnlockedInvoice() {
		return unlockedInvoice;
	}

	public void setUnlockedInvoice(Float unlockedInvoice) {
		this.unlockedInvoice = unlockedInvoice;
	}

	public List getEncryptedTransaction() {
		return encryptedTransaction;
	}

	public void setEncryptedTransaction(List encryptedTransaction) {
		this.encryptedTransaction = encryptedTransaction;
	}

	public List getLockedPlan() {
		return lockedPlan;
	}

	public void setLockedPlan(List lockedPlan) {
		this.lockedPlan = lockedPlan;
	}

	public BigDecimal getLastBrand() {
		return lastBrand;
	}

	public void setLastBrand(BigDecimal lastBrand) {
		this.lastBrand = lastBrand;
	}

	public String getSharedContract() {
		return sharedContract;
	}

	public void setSharedContract(String sharedContract) {
		this.sharedContract = sharedContract;
	}

	public String getRestoredCoupon() {
		return restoredCoupon;
	}

	public void setRestoredCoupon(String restoredCoupon) {
		this.restoredCoupon = restoredCoupon;
	}

	public Set getEnabledActivity() {
		return enabledActivity;
	}

	public void setEnabledActivity(Set enabledActivity) {
		this.enabledActivity = enabledActivity;
	}

	public String getLevelFeedback() {
		return levelFeedback;
	}

	public void setLevelFeedback(String levelFeedback) {
		this.levelFeedback = levelFeedback;
	}

	public Double getUnsecurePayment() {
		return unsecurePayment;
	}

	public void setUnsecurePayment(Double unsecurePayment) {
		this.unsecurePayment = unsecurePayment;
	}

	public String getOpenItem() {
		return openItem;
	}

	public void setOpenItem(String openItem) {
		this.openItem = openItem;
	}

	public String getUnflaggedCustomer() {
		return unflaggedCustomer;
	}

	public void setUnflaggedCustomer(String unflaggedCustomer) {
		this.unflaggedCustomer = unflaggedCustomer;
	}

	public Boolean getUnpublishedFile() {
		return unpublishedFile;
	}

	public void setUnpublishedFile(Boolean unpublishedFile) {
		this.unpublishedFile = unpublishedFile;
	}

	public Long getRejectedLog() {
		return rejectedLog;
	}

	public void setRejectedLog(Long rejectedLog) {
		this.rejectedLog = rejectedLog;
	}

	public Long getModifiedCart() {
		return modifiedCart;
	}

	public void setModifiedCart(Long modifiedCart) {
		this.modifiedCart = modifiedCart;
	}

	public Boolean getDraftAsset() {
		return draftAsset;
	}

	public void setDraftAsset(Boolean draftAsset) {
		this.draftAsset = draftAsset;
	}

	public Boolean getFailedPolicy() {
		return failedPolicy;
	}

	public void setFailedPolicy(Boolean failedPolicy) {
		this.failedPolicy = failedPolicy;
	}

	public Boolean getSecondaryItem() {
		return secondaryItem;
	}

	public void setSecondaryItem(Boolean secondaryItem) {
		this.secondaryItem = secondaryItem;
	}

	public Integer getPublicSession() {
		return publicSession;
	}

	public void setPublicSession(Integer publicSession) {
		this.publicSession = publicSession;
	}

	public List getLastBill() {
		return lastBill;
	}

	public void setLastBill(List lastBill) {
		this.lastBill = lastBill;
	}

	public Float getUnlockedBill() {
		return unlockedBill;
	}

	public void setUnlockedBill(Float unlockedBill) {
		this.unlockedBill = unlockedBill;
	}

	public String getEncryptedDocument() {
		return encryptedDocument;
	}

	public void setEncryptedDocument(String encryptedDocument) {
		this.encryptedDocument = encryptedDocument;
	}

	public List getSavedTransaction() {
		return savedTransaction;
	}

	public void setSavedTransaction(List savedTransaction) {
		this.savedTransaction = savedTransaction;
	}

	public Float getSyncedMilestone() {
		return syncedMilestone;
	}

	public void setSyncedMilestone(Float syncedMilestone) {
		this.syncedMilestone = syncedMilestone;
	}

	public Integer getEncryptedFeedback() {
		return encryptedFeedback;
	}

	public void setEncryptedFeedback(Integer encryptedFeedback) {
		this.encryptedFeedback = encryptedFeedback;
	}

	public BigDecimal getModifiedEvent() {
		return modifiedEvent;
	}

	public void setModifiedEvent(BigDecimal modifiedEvent) {
		this.modifiedEvent = modifiedEvent;
	}

	public String getSuccessfulSubscription() {
		return successfulSubscription;
	}

	public void setSuccessfulSubscription(String successfulSubscription) {
		this.successfulSubscription = successfulSubscription;
	}

	public Double getHiddenAddress() {
		return hiddenAddress;
	}

	public void setHiddenAddress(Double hiddenAddress) {
		this.hiddenAddress = hiddenAddress;
	}

	public Boolean getFirstBill() {
		return firstBill;
	}

	public void setFirstBill(Boolean firstBill) {
		this.firstBill = firstBill;
	}

	public Long getUnflaggedMilestone() {
		return unflaggedMilestone;
	}

	public void setUnflaggedMilestone(Long unflaggedMilestone) {
		this.unflaggedMilestone = unflaggedMilestone;
	}

	public Float getNextPlan() {
		return nextPlan;
	}

	public void setNextPlan(Float nextPlan) {
		this.nextPlan = nextPlan;
	}

	public Double getDisabledShipment() {
		return disabledShipment;
	}

	public void setDisabledShipment(Double disabledShipment) {
		this.disabledShipment = disabledShipment;
	}

	public Double getSharedMilestone() {
		return sharedMilestone;
	}

	public void setSharedMilestone(Double sharedMilestone) {
		this.sharedMilestone = sharedMilestone;
	}

	public Set getEnabledRole() {
		return enabledRole;
	}

	public void setEnabledRole(Set enabledRole) {
		this.enabledRole = enabledRole;
	}

	public List getLockedActivity() {
		return lockedActivity;
	}

	public void setLockedActivity(List lockedActivity) {
		this.lockedActivity = lockedActivity;
	}

	public BigDecimal getUnreviewedMerchant() {
		return unreviewedMerchant;
	}

	public void setUnreviewedMerchant(BigDecimal unreviewedMerchant) {
		this.unreviewedMerchant = unreviewedMerchant;
	}

	public String getSharedCategory() {
		return sharedCategory;
	}

	public void setSharedCategory(String sharedCategory) {
		this.sharedCategory = sharedCategory;
	}

	public String getEncryptedBrand() {
		return encryptedBrand;
	}

	public void setEncryptedBrand(String encryptedBrand) {
		this.encryptedBrand = encryptedBrand;
	}

	public BigDecimal getRestoredAddress() {
		return restoredAddress;
	}

	public void setRestoredAddress(BigDecimal restoredAddress) {
		this.restoredAddress = restoredAddress;
	}

	public BigDecimal getUnflaggedAgreement() {
		return unflaggedAgreement;
	}

	public void setUnflaggedAgreement(BigDecimal unflaggedAgreement) {
		this.unflaggedAgreement = unflaggedAgreement;
	}

	public Double getUnflaggedTransaction() {
		return unflaggedTransaction;
	}

	public void setUnflaggedTransaction(Double unflaggedTransaction) {
		this.unflaggedTransaction = unflaggedTransaction;
	}

	public List getEnabledItem() {
		return enabledItem;
	}

	public void setEnabledItem(List enabledItem) {
		this.enabledItem = enabledItem;
	}

	public Integer getPublicPayment() {
		return publicPayment;
	}

	public void setPublicPayment(Integer publicPayment) {
		this.publicPayment = publicPayment;
	}

	public String getModifiedUser() {
		return modifiedUser;
	}

	public void setModifiedUser(String modifiedUser) {
		this.modifiedUser = modifiedUser;
	}

	public Long getOpenDate() {
		return openDate;
	}

	public void setOpenDate(Long openDate) {
		this.openDate = openDate;
	}

	public BigDecimal getVerifiedPolicy() {
		return verifiedPolicy;
	}

	public void setVerifiedPolicy(BigDecimal verifiedPolicy) {
		this.verifiedPolicy = verifiedPolicy;
	}

	public Double getTemporaryMerchant() {
		return temporaryMerchant;
	}

	public void setTemporaryMerchant(Double temporaryMerchant) {
		this.temporaryMerchant = temporaryMerchant;
	}

	public Long getPermanentReport() {
		return permanentReport;
	}

	public void setPermanentReport(Long permanentReport) {
		this.permanentReport = permanentReport;
	}

	public Integer getExportedMessage() {
		return exportedMessage;
	}

	public void setExportedMessage(Integer exportedMessage) {
		this.exportedMessage = exportedMessage;
	}

	public Set getPrimaryCart() {
		return primaryCart;
	}

	public void setPrimaryCart(Set primaryCart) {
		this.primaryCart = primaryCart;
	}

	public Double getFailedPrice() {
		return failedPrice;
	}

	public void setFailedPrice(Double failedPrice) {
		this.failedPrice = failedPrice;
	}

	public String getUnverifiedAccount() {
		return unverifiedAccount;
	}

	public void setUnverifiedAccount(String unverifiedAccount) {
		this.unverifiedAccount = unverifiedAccount;
	}

	public Boolean getDraftProduct() {
		return draftProduct;
	}

	public void setDraftProduct(Boolean draftProduct) {
		this.draftProduct = draftProduct;
	}

	public Integer getSyncedStock() {
		return syncedStock;
	}

	public void setSyncedStock(Integer syncedStock) {
		this.syncedStock = syncedStock;
	}

	public Double getUnpublishedAgreement() {
		return unpublishedAgreement;
	}

	public void setUnpublishedAgreement(Double unpublishedAgreement) {
		this.unpublishedAgreement = unpublishedAgreement;
	}

	public Integer getReportedDiscount() {
		return reportedDiscount;
	}

	public void setReportedDiscount(Integer reportedDiscount) {
		this.reportedDiscount = reportedDiscount;
	}

	public List getFinalContract() {
		return finalContract;
	}

	public void setFinalContract(List finalContract) {
		this.finalContract = finalContract;
	}

	public Double getSavedAccount() {
		return savedAccount;
	}

	public void setSavedAccount(Double savedAccount) {
		this.savedAccount = savedAccount;
	}

	public Long getAveragePrice() {
		return averagePrice;
	}

	public void setAveragePrice(Long averagePrice) {
		this.averagePrice = averagePrice;
	}

	public Boolean getApprovedPermission() {
		return approvedPermission;
	}

	public void setApprovedPermission(Boolean approvedPermission) {
		this.approvedPermission = approvedPermission;
	}

	public String getFinalDiscount() {
		return finalDiscount;
	}

	public void setFinalDiscount(String finalDiscount) {
		this.finalDiscount = finalDiscount;
	}

	public Long getLoadedDepartment() {
		return loadedDepartment;
	}

	public void setLoadedDepartment(Long loadedDepartment) {
		this.loadedDepartment = loadedDepartment;
	}

	public Double getArchivedFile() {
		return archivedFile;
	}

	public void setArchivedFile(Double archivedFile) {
		this.archivedFile = archivedFile;
	}

	public String getUnflaggedAccount() {
		return unflaggedAccount;
	}

	public void setUnflaggedAccount(String unflaggedAccount) {
		this.unflaggedAccount = unflaggedAccount;
	}

	public String getPreviousEvent() {
		return previousEvent;
	}

	public void setPreviousEvent(String previousEvent) {
		this.previousEvent = previousEvent;
	}

	public Float getPublicInvoice() {
		return publicInvoice;
	}

	public void setPublicInvoice(Float publicInvoice) {
		this.publicInvoice = publicInvoice;
	}

	public Float getPublicSupplier() {
		return publicSupplier;
	}

	public void setPublicSupplier(Float publicSupplier) {
		this.publicSupplier = publicSupplier;
	}

	public Long getFailedPlan() {
		return failedPlan;
	}

	public void setFailedPlan(Long failedPlan) {
		this.failedPlan = failedPlan;
	}

	public String getMaxOrder() {
		return maxOrder;
	}

	public void setMaxOrder(String maxOrder) {
		this.maxOrder = maxOrder;
	}

	public String getApprovedSupplier() {
		return approvedSupplier;
	}

	public void setApprovedSupplier(String approvedSupplier) {
		this.approvedSupplier = approvedSupplier;
	}

	public Boolean getPrivateFolder() {
		return privateFolder;
	}

	public void setPrivateFolder(Boolean privateFolder) {
		this.privateFolder = privateFolder;
	}

	public String getUnreviewedAddress() {
		return unreviewedAddress;
	}

	public void setUnreviewedAddress(String unreviewedAddress) {
		this.unreviewedAddress = unreviewedAddress;
	}

	public BigDecimal getHiddenFeedback() {
		return hiddenFeedback;
	}

	public void setHiddenFeedback(BigDecimal hiddenFeedback) {
		this.hiddenFeedback = hiddenFeedback;
	}
}
