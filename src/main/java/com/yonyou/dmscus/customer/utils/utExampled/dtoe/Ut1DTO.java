package com.yonyou.dmscus.customer.utils.utExampled.dtoe;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
public class Ut1DTO {

    private Long savedCart;
    private List lockedName;
    private Double createdRole;
    private Boolean maxCategory;
    private Double countAchievement;
    private Double loadedDepartment;
    private Double createdSession;
    private String minReview;
    private String levelRole;
    private Float unpublishedInvoice;
    private Double unreportedMilestone;
    private Set secondaryEvent;
    private Double unreportedCustomer;
    private String totalCart;
    private Boolean flaggedFolder;
    private String newTransaction;
    private Boolean rejectedPolicy;
    private Float decryptedEvent;
    private Double unverifiedPrice;
    private Boolean averageDocument;
    private Integer inactiveActivity;
    private String closedTransaction;
    private Set oldAsset;
    private Float exportedResource;
    private Float flaggedLog;
    private Float maxAddress;
    private List failedDate;
    private Float createdRecord;
    private Float lockedDocument;
    private Long newUser;
    private String restoredSubscription;
    private String updatedContract;
    private BigDecimal openPayment;
    private BigDecimal primaryMilestone;
    private Long savedShipment;
    private Integer unverifiedFile;
    private BigDecimal privateMerchant;
    private String oldGroup;
    private Long sharedAudit;
    private String unlockedRecord;
    private List deletedProject;
    private String closedRecord;
    private String successfulName;
    private BigDecimal nextRole;
    private String mainFeedback;
    private Float countAddress;
    private String sharedSchedule;
    private Long unsecureSchedule;
    private Long failedTeam;
    private Integer visibleDate;
    private String draftFolder;
    private Integer secondaryDepartment;
    private Double syncedAchievement;
    private Long minAccount;
    private String updatedEvent;
    private Integer countFolder;
    private Boolean firstHistory;
    private Set previousBrand;
    private Double secondaryHistory;
    private String reportedAudit;
    private BigDecimal mainShipment;
    private List unlockedAudit;
    private Double levelSession;
    private Integer unsecureAudit;
    private Float updatedName;
    private String finalReport;
    private Double reviewedAchievement;
    private Double disabledCoupon;
    private Double totalHistory;
    private String disabledContract;
    private Set lastBill;
    private Set verifiedUser;
    private Double averageInvoice;
    private Double pendingEvent;
    private Double initialMessage;
    private BigDecimal firstSupplier;
    private BigDecimal restoredTransaction;
    private Integer unpublishedActivity;
    private Double totalOrder;
    private String privateMessage;
    private String restoredRole;
    private List currentDocument;
    private String draftSchedule;
    private List visibleMerchant;
    private String primaryActivity;
    private Set privateTask;
    private List unsecureLog;
    private String successfulReport;
    private Long lastDiscount;
    private Set unverifiedEvent;
    private Integer secondaryMilestone;
    private Boolean lastSchedule;
    private Set previousUser;
    private Long draftNotification;
    private String syncedUser;
    private Boolean finalBrand;
    private BigDecimal openCoupon;
    private BigDecimal approvedProject;

    public Long getSavedCart() {
        return savedCart;
    }

    public void setSavedCart(Long savedCart) {
        this.savedCart = savedCart;
    }

    public List getLockedName() {
        return lockedName;
    }

    public void setLockedName(List lockedName) {
        this.lockedName = lockedName;
    }

    public Double getCreatedRole() {
        return createdRole;
    }

    public void setCreatedRole(Double createdRole) {
        this.createdRole = createdRole;
    }

    public Boolean getMaxCategory() {
        return maxCategory;
    }

    public void setMaxCategory(Boolean maxCategory) {
        this.maxCategory = maxCategory;
    }

    public Double getCountAchievement() {
        return countAchievement;
    }

    public void setCountAchievement(Double countAchievement) {
        this.countAchievement = countAchievement;
    }

    public Double getLoadedDepartment() {
        return loadedDepartment;
    }

    public void setLoadedDepartment(Double loadedDepartment) {
        this.loadedDepartment = loadedDepartment;
    }

    public Double getCreatedSession() {
        return createdSession;
    }

    public void setCreatedSession(Double createdSession) {
        this.createdSession = createdSession;
    }

    public String getMinReview() {
        return minReview;
    }

    public void setMinReview(String minReview) {
        this.minReview = minReview;
    }

    public String getLevelRole() {
        return levelRole;
    }

    public void setLevelRole(String levelRole) {
        this.levelRole = levelRole;
    }

    public Float getUnpublishedInvoice() {
        return unpublishedInvoice;
    }

    public void setUnpublishedInvoice(Float unpublishedInvoice) {
        this.unpublishedInvoice = unpublishedInvoice;
    }

    public Double getUnreportedMilestone() {
        return unreportedMilestone;
    }

    public void setUnreportedMilestone(Double unreportedMilestone) {
        this.unreportedMilestone = unreportedMilestone;
    }

    public Set getSecondaryEvent() {
        return secondaryEvent;
    }

    public void setSecondaryEvent(Set secondaryEvent) {
        this.secondaryEvent = secondaryEvent;
    }

    public Double getUnreportedCustomer() {
        return unreportedCustomer;
    }

    public void setUnreportedCustomer(Double unreportedCustomer) {
        this.unreportedCustomer = unreportedCustomer;
    }

    public String getTotalCart() {
        return totalCart;
    }

    public void setTotalCart(String totalCart) {
        this.totalCart = totalCart;
    }

    public Boolean getFlaggedFolder() {
        return flaggedFolder;
    }

    public void setFlaggedFolder(Boolean flaggedFolder) {
        this.flaggedFolder = flaggedFolder;
    }

    public String getNewTransaction() {
        return newTransaction;
    }

    public void setNewTransaction(String newTransaction) {
        this.newTransaction = newTransaction;
    }

    public Boolean getRejectedPolicy() {
        return rejectedPolicy;
    }

    public void setRejectedPolicy(Boolean rejectedPolicy) {
        this.rejectedPolicy = rejectedPolicy;
    }

    public Float getDecryptedEvent() {
        return decryptedEvent;
    }

    public void setDecryptedEvent(Float decryptedEvent) {
        this.decryptedEvent = decryptedEvent;
    }

    public Double getUnverifiedPrice() {
        return unverifiedPrice;
    }

    public void setUnverifiedPrice(Double unverifiedPrice) {
        this.unverifiedPrice = unverifiedPrice;
    }

    public Boolean getAverageDocument() {
        return averageDocument;
    }

    public void setAverageDocument(Boolean averageDocument) {
        this.averageDocument = averageDocument;
    }

    public Integer getInactiveActivity() {
        return inactiveActivity;
    }

    public void setInactiveActivity(Integer inactiveActivity) {
        this.inactiveActivity = inactiveActivity;
    }

    public String getClosedTransaction() {
        return closedTransaction;
    }

    public void setClosedTransaction(String closedTransaction) {
        this.closedTransaction = closedTransaction;
    }

    public Set getOldAsset() {
        return oldAsset;
    }

    public void setOldAsset(Set oldAsset) {
        this.oldAsset = oldAsset;
    }

    public Float getExportedResource() {
        return exportedResource;
    }

    public void setExportedResource(Float exportedResource) {
        this.exportedResource = exportedResource;
    }

    public Float getFlaggedLog() {
        return flaggedLog;
    }

    public void setFlaggedLog(Float flaggedLog) {
        this.flaggedLog = flaggedLog;
    }

    public Float getMaxAddress() {
        return maxAddress;
    }

    public void setMaxAddress(Float maxAddress) {
        this.maxAddress = maxAddress;
    }

    public List getFailedDate() {
        return failedDate;
    }

    public void setFailedDate(List failedDate) {
        this.failedDate = failedDate;
    }

    public Float getCreatedRecord() {
        return createdRecord;
    }

    public void setCreatedRecord(Float createdRecord) {
        this.createdRecord = createdRecord;
    }

    public Float getLockedDocument() {
        return lockedDocument;
    }

    public void setLockedDocument(Float lockedDocument) {
        this.lockedDocument = lockedDocument;
    }

    public Long getNewUser() {
        return newUser;
    }

    public void setNewUser(Long newUser) {
        this.newUser = newUser;
    }

    public String getRestoredSubscription() {
        return restoredSubscription;
    }

    public void setRestoredSubscription(String restoredSubscription) {
        this.restoredSubscription = restoredSubscription;
    }

    public String getUpdatedContract() {
        return updatedContract;
    }

    public void setUpdatedContract(String updatedContract) {
        this.updatedContract = updatedContract;
    }

    public BigDecimal getOpenPayment() {
        return openPayment;
    }

    public void setOpenPayment(BigDecimal openPayment) {
        this.openPayment = openPayment;
    }

    public BigDecimal getPrimaryMilestone() {
        return primaryMilestone;
    }

    public void setPrimaryMilestone(BigDecimal primaryMilestone) {
        this.primaryMilestone = primaryMilestone;
    }

    public Long getSavedShipment() {
        return savedShipment;
    }

    public void setSavedShipment(Long savedShipment) {
        this.savedShipment = savedShipment;
    }

    public Integer getUnverifiedFile() {
        return unverifiedFile;
    }

    public void setUnverifiedFile(Integer unverifiedFile) {
        this.unverifiedFile = unverifiedFile;
    }

    public BigDecimal getPrivateMerchant() {
        return privateMerchant;
    }

    public void setPrivateMerchant(BigDecimal privateMerchant) {
        this.privateMerchant = privateMerchant;
    }

    public String getOldGroup() {
        return oldGroup;
    }

    public void setOldGroup(String oldGroup) {
        this.oldGroup = oldGroup;
    }

    public Long getSharedAudit() {
        return sharedAudit;
    }

    public void setSharedAudit(Long sharedAudit) {
        this.sharedAudit = sharedAudit;
    }

    public String getUnlockedRecord() {
        return unlockedRecord;
    }

    public void setUnlockedRecord(String unlockedRecord) {
        this.unlockedRecord = unlockedRecord;
    }

    public List getDeletedProject() {
        return deletedProject;
    }

    public void setDeletedProject(List deletedProject) {
        this.deletedProject = deletedProject;
    }

    public String getClosedRecord() {
        return closedRecord;
    }

    public void setClosedRecord(String closedRecord) {
        this.closedRecord = closedRecord;
    }

    public String getSuccessfulName() {
        return successfulName;
    }

    public void setSuccessfulName(String successfulName) {
        this.successfulName = successfulName;
    }

    public BigDecimal getNextRole() {
        return nextRole;
    }

    public void setNextRole(BigDecimal nextRole) {
        this.nextRole = nextRole;
    }

    public String getMainFeedback() {
        return mainFeedback;
    }

    public void setMainFeedback(String mainFeedback) {
        this.mainFeedback = mainFeedback;
    }

    public Float getCountAddress() {
        return countAddress;
    }

    public void setCountAddress(Float countAddress) {
        this.countAddress = countAddress;
    }

    public String getSharedSchedule() {
        return sharedSchedule;
    }

    public void setSharedSchedule(String sharedSchedule) {
        this.sharedSchedule = sharedSchedule;
    }

    public Long getUnsecureSchedule() {
        return unsecureSchedule;
    }

    public void setUnsecureSchedule(Long unsecureSchedule) {
        this.unsecureSchedule = unsecureSchedule;
    }

    public Long getFailedTeam() {
        return failedTeam;
    }

    public void setFailedTeam(Long failedTeam) {
        this.failedTeam = failedTeam;
    }

    public Integer getVisibleDate() {
        return visibleDate;
    }

    public void setVisibleDate(Integer visibleDate) {
        this.visibleDate = visibleDate;
    }

    public String getDraftFolder() {
        return draftFolder;
    }

    public void setDraftFolder(String draftFolder) {
        this.draftFolder = draftFolder;
    }

    public Integer getSecondaryDepartment() {
        return secondaryDepartment;
    }

    public void setSecondaryDepartment(Integer secondaryDepartment) {
        this.secondaryDepartment = secondaryDepartment;
    }

    public Double getSyncedAchievement() {
        return syncedAchievement;
    }

    public void setSyncedAchievement(Double syncedAchievement) {
        this.syncedAchievement = syncedAchievement;
    }

    public Long getMinAccount() {
        return minAccount;
    }

    public void setMinAccount(Long minAccount) {
        this.minAccount = minAccount;
    }

    public String getUpdatedEvent() {
        return updatedEvent;
    }

    public void setUpdatedEvent(String updatedEvent) {
        this.updatedEvent = updatedEvent;
    }

    public Integer getCountFolder() {
        return countFolder;
    }

    public void setCountFolder(Integer countFolder) {
        this.countFolder = countFolder;
    }

    public Boolean getFirstHistory() {
        return firstHistory;
    }

    public void setFirstHistory(Boolean firstHistory) {
        this.firstHistory = firstHistory;
    }

    public Set getPreviousBrand() {
        return previousBrand;
    }

    public void setPreviousBrand(Set previousBrand) {
        this.previousBrand = previousBrand;
    }

    public Double getSecondaryHistory() {
        return secondaryHistory;
    }

    public void setSecondaryHistory(Double secondaryHistory) {
        this.secondaryHistory = secondaryHistory;
    }

    public String getReportedAudit() {
        return reportedAudit;
    }

    public void setReportedAudit(String reportedAudit) {
        this.reportedAudit = reportedAudit;
    }

    public BigDecimal getMainShipment() {
        return mainShipment;
    }

    public void setMainShipment(BigDecimal mainShipment) {
        this.mainShipment = mainShipment;
    }

    public List getUnlockedAudit() {
        return unlockedAudit;
    }

    public void setUnlockedAudit(List unlockedAudit) {
        this.unlockedAudit = unlockedAudit;
    }

    public Double getLevelSession() {
        return levelSession;
    }

    public void setLevelSession(Double levelSession) {
        this.levelSession = levelSession;
    }

    public Integer getUnsecureAudit() {
        return unsecureAudit;
    }

    public void setUnsecureAudit(Integer unsecureAudit) {
        this.unsecureAudit = unsecureAudit;
    }

    public Float getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(Float updatedName) {
        this.updatedName = updatedName;
    }

    public String getFinalReport() {
        return finalReport;
    }

    public void setFinalReport(String finalReport) {
        this.finalReport = finalReport;
    }

    public Double getReviewedAchievement() {
        return reviewedAchievement;
    }

    public void setReviewedAchievement(Double reviewedAchievement) {
        this.reviewedAchievement = reviewedAchievement;
    }

    public Double getDisabledCoupon() {
        return disabledCoupon;
    }

    public void setDisabledCoupon(Double disabledCoupon) {
        this.disabledCoupon = disabledCoupon;
    }

    public Double getTotalHistory() {
        return totalHistory;
    }

    public void setTotalHistory(Double totalHistory) {
        this.totalHistory = totalHistory;
    }

    public String getDisabledContract() {
        return disabledContract;
    }

    public void setDisabledContract(String disabledContract) {
        this.disabledContract = disabledContract;
    }

    public Set getLastBill() {
        return lastBill;
    }

    public void setLastBill(Set lastBill) {
        this.lastBill = lastBill;
    }

    public Set getVerifiedUser() {
        return verifiedUser;
    }

    public void setVerifiedUser(Set verifiedUser) {
        this.verifiedUser = verifiedUser;
    }

    public Double getAverageInvoice() {
        return averageInvoice;
    }

    public void setAverageInvoice(Double averageInvoice) {
        this.averageInvoice = averageInvoice;
    }

    public Double getPendingEvent() {
        return pendingEvent;
    }

    public void setPendingEvent(Double pendingEvent) {
        this.pendingEvent = pendingEvent;
    }

    public Double getInitialMessage() {
        return initialMessage;
    }

    public void setInitialMessage(Double initialMessage) {
        this.initialMessage = initialMessage;
    }

    public BigDecimal getFirstSupplier() {
        return firstSupplier;
    }

    public void setFirstSupplier(BigDecimal firstSupplier) {
        this.firstSupplier = firstSupplier;
    }

    public BigDecimal getRestoredTransaction() {
        return restoredTransaction;
    }

    public void setRestoredTransaction(BigDecimal restoredTransaction) {
        this.restoredTransaction = restoredTransaction;
    }

    public Integer getUnpublishedActivity() {
        return unpublishedActivity;
    }

    public void setUnpublishedActivity(Integer unpublishedActivity) {
        this.unpublishedActivity = unpublishedActivity;
    }

    public Double getTotalOrder() {
        return totalOrder;
    }

    public void setTotalOrder(Double totalOrder) {
        this.totalOrder = totalOrder;
    }

    public String getPrivateMessage() {
        return privateMessage;
    }

    public void setPrivateMessage(String privateMessage) {
        this.privateMessage = privateMessage;
    }

    public String getRestoredRole() {
        return restoredRole;
    }

    public void setRestoredRole(String restoredRole) {
        this.restoredRole = restoredRole;
    }

    public List getCurrentDocument() {
        return currentDocument;
    }

    public void setCurrentDocument(List currentDocument) {
        this.currentDocument = currentDocument;
    }

    public String getDraftSchedule() {
        return draftSchedule;
    }

    public void setDraftSchedule(String draftSchedule) {
        this.draftSchedule = draftSchedule;
    }

    public List getVisibleMerchant() {
        return visibleMerchant;
    }

    public void setVisibleMerchant(List visibleMerchant) {
        this.visibleMerchant = visibleMerchant;
    }

    public String getPrimaryActivity() {
        return primaryActivity;
    }

    public void setPrimaryActivity(String primaryActivity) {
        this.primaryActivity = primaryActivity;
    }

    public Set getPrivateTask() {
        return privateTask;
    }

    public void setPrivateTask(Set privateTask) {
        this.privateTask = privateTask;
    }

    public List getUnsecureLog() {
        return unsecureLog;
    }

    public void setUnsecureLog(List unsecureLog) {
        this.unsecureLog = unsecureLog;
    }

    public String getSuccessfulReport() {
        return successfulReport;
    }

    public void setSuccessfulReport(String successfulReport) {
        this.successfulReport = successfulReport;
    }

    public Long getLastDiscount() {
        return lastDiscount;
    }

    public void setLastDiscount(Long lastDiscount) {
        this.lastDiscount = lastDiscount;
    }

    public Set getUnverifiedEvent() {
        return unverifiedEvent;
    }

    public void setUnverifiedEvent(Set unverifiedEvent) {
        this.unverifiedEvent = unverifiedEvent;
    }

    public Integer getSecondaryMilestone() {
        return secondaryMilestone;
    }

    public void setSecondaryMilestone(Integer secondaryMilestone) {
        this.secondaryMilestone = secondaryMilestone;
    }

    public Boolean getLastSchedule() {
        return lastSchedule;
    }

    public void setLastSchedule(Boolean lastSchedule) {
        this.lastSchedule = lastSchedule;
    }

    public Set getPreviousUser() {
        return previousUser;
    }

    public void setPreviousUser(Set previousUser) {
        this.previousUser = previousUser;
    }

    public Long getDraftNotification() {
        return draftNotification;
    }

    public void setDraftNotification(Long draftNotification) {
        this.draftNotification = draftNotification;
    }

    public String getSyncedUser() {
        return syncedUser;
    }

    public void setSyncedUser(String syncedUser) {
        this.syncedUser = syncedUser;
    }

    public Boolean getFinalBrand() {
        return finalBrand;
    }

    public void setFinalBrand(Boolean finalBrand) {
        this.finalBrand = finalBrand;
    }

    public BigDecimal getOpenCoupon() {
        return openCoupon;
    }

    public void setOpenCoupon(BigDecimal openCoupon) {
        this.openCoupon = openCoupon;
    }

    public BigDecimal getApprovedProject() {
        return approvedProject;
    }

    public void setApprovedProject(BigDecimal approvedProject) {
        this.approvedProject = approvedProject;
    }
}
