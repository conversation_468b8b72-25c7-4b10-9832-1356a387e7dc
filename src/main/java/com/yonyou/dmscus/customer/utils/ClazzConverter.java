package com.yonyou.dmscus.customer.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.dmscus.customer.entity.dto.faultLight.ClueDataSynchroDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.WarningInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 相同属性对象转换
 */
@Slf4j
public class ClazzConverter {

    /**
     * 具有相同属性名称的对象转化
     */
    public static <T1, T2> T1 converterClass(final T2 srcClazz, Class<T1> dstClazz) {
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(srcClazz);
        if (jsonObject == null) {
            return null;
        }
        return JSONObject.toJavaObject(jsonObject, dstClazz);

    }

    /**
     * 集合转化
     */
    public static <T1, T2> Collection<T1> converterClass(final Collection<T2> srcClazzCollection, Class<T1> dstClazz) {
        JSONArray jsonArray = (JSONArray) JSONObject.toJSON(srcClazzCollection);
        return JSONArray.parseArray(jsonArray.toJSONString(), dstClazz);
    }

    /**
     * 集合转化
     */
    public static <T1, T2> List<T1> converterClass(final List<T2> srcClazzCollection, Class<T1> dstClazz) {
        if (CollectionUtils.isEmpty(srcClazzCollection)){
            return null;
        }
        JSONArray jsonArray = (JSONArray) JSONObject.toJSON(srcClazzCollection);
        return JSONArray.parseArray(jsonArray.toJSONString(), dstClazz);
    }


    /**
     * 数组转化
     */
    @SuppressWarnings("unchecked")
    public static <T1, T2> T1[] converterClass(final T2[] srcClazzArray, Class<T1> dstClazz) {
        JSONArray jsonArray = (JSONArray) JSONObject.toJSON(srcClazzArray);
        if (jsonArray == null) {
            return null;
        }
        List<T1> result = JSONArray.parseArray(jsonArray.toJSONString(), dstClazz);
        if (result == null) {
            return null;
        }

        return (T1[]) result.toArray();
    }

    public static <T1, T2> T1 converterClassWithDateFormat(final T2 srcClazz, Class<T1> dstClazz, String dateFormat) {
        String jsonString = JSON.toJSONStringWithDateFormat(srcClazz, dateFormat);
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        if (jsonObject == null) {
            return null;
        }
        return JSONObject.toJavaObject(jsonObject, dstClazz);
    }

    /**
     * 交集
     */
    public static <T1, T2> Collection<T1> intersectionCollection(Collection<T1> collection1 ,Collection<T2> collection2){

        return collection1.stream().filter(collection2::contains).collect(Collectors.toList());
    }

    /**
     * 差集
     */
    public static <T1, T2> Collection<T1> reduceCollection(Collection<T1> collection1 ,Collection<T2> collection2){

        return collection1.stream().filter(item -> !collection2.contains(item)).collect(Collectors.toList());
    }

    /**
     * 并集
     */
    public static <T1> Collection<T1> allCollection(Collection<T1> collection1 ,Collection<T1> collection2){

        Collection<T1> collectionAll = collection1.parallelStream().collect(Collectors.toList());
        Collection<T1> collectionAll2 = collection2.parallelStream().collect(Collectors.toList());
        collectionAll.addAll(collectionAll2);
        return collectionAll;
    }

    /**
     * 去重并集
     */
    public static <T1> Collection<T1> collectionAllDistinct(Collection<T1> collection1 ,Collection<T1> collection2){

        return allCollection(collection1,collection2).stream().distinct().collect(Collectors.toList());
    }

    /**
     * String类型xml转对象
     * @param xmlStr        xmlStr
     * @param clazz         Class
     */
    @SuppressWarnings("unchecked")
    public static <T>  T convertXmlStrToObject(String xmlStr, Class<T> clazz){

        T message = null;
        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(clazz);

            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

            StringReader reader = new StringReader(xmlStr);

            return (T) unmarshaller.unmarshal(reader);

        }catch (Exception e){
            System.out.println("XML转换异常");

        }
        return message;
    }
}

