package com.yonyou.dmscus.customer.utils.utExampled.dtoe;


import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public class Ut4DTO {
    private String publishedRecord;
    private Float restoredFile;
    private Double unreviewedPermission;
    private BigDecimal publishedCart;
    private BigDecimal publicAddress;
    private String newReview;
    private Float oldEmail;
    private List secondaryPermission;
    private BigDecimal hiddenBill;
    private List disabledTransaction;
    private Float syncedStock;
    private Float exportedAsset;
    private Double closedSupplier;
    private Set currentDepartment;
    private List flaggedStrategy;
    private Float permanentSession;
    private String lastRecord;
    private List levelBrand;
    private BigDecimal publishedEvent;
    private Float firstTeam;
    private Float countReport;
    private String oldResource;
    private List draftProduct;
    private Long inactiveSupplier;
    private Double unverifiedDiscount;
    private Float newTask;
    private String totalName;
    private String openShipment;
    private Float inactiveBrand;
    private String totalMerchant;
    private Boolean countPolicy;
    private String typeTask;
    private Integer openPolicy;
    private String closedWarehouse;
    private Integer closedCart;
    private Integer temporaryFeedback;
    private Double disabledTask;
    private Set finalAchievement;
    private Boolean disabledShipment;
    private Double temporaryFile;
    private Integer syncedName;
    private Float updatedTask;
    private Float finalPermission;
    private Set archivedOrder;
    private List modifiedInvoice;
    private Integer completedTask;
    private String unreportedDepartment;
    private Integer inactiveCoupon;
    private BigDecimal primaryTask;
    private BigDecimal unreportedAgreement;
    private Boolean verifiedTransaction;
    private Long successfulProject;
    private String temporaryTask;
    private Float secureSchedule;
    private List totalEmail;
    private Long loadedHistory;
    private Boolean rejectedSession;
    private List typeCustomer;
    private Double openDiscount;
    private Boolean initialBrand;
    private String loadedPhone;
    private Boolean newGoal;
    private Integer updatedUser;
    private BigDecimal typeName;
    private String maxPlan;
    private Integer reportedLog;
    private Boolean modifiedFeedback;
    private Long publishedRole;
    private String unsecureCoupon;
    private Long syncedCart;
    private Boolean decryptedHistory;
    private String temporaryHistory;
    private String updatedDate;
    private List privateBrand;
    private String completedActivity;
    private Float pendingFeedback;
    private BigDecimal openActivity;
    private Set typePlan;
    private Float unlockedEvent;
    private String enabledDocument;
    private Set verifiedEvent;
    private Float reviewedMerchant;
    private String levelWarehouse;
    private Boolean unflaggedCoupon;
    private Boolean unflaggedPermission;
    private List exportedPhone;
    private Float publicDocument;
    private Float enabledCoupon;
    private Long inactiveStrategy;
    private String currentRecord;
    private List closedTeam;
    private List permanentTransaction;
    private BigDecimal publishedCategory;
    private Boolean typeShipment;
    private Double levelMessage;
    private Boolean createdHistory;
    private Long pendingPrice;
    private Integer decryptedAddress;
    private List nextAsset;
    private Double publishedActivity;

    public String getPublishedRecord() {
        return publishedRecord;
    }

    public void setPublishedRecord(String publishedRecord) {
        this.publishedRecord = publishedRecord;
    }

    public Float getRestoredFile() {
        return restoredFile;
    }

    public void setRestoredFile(Float restoredFile) {
        this.restoredFile = restoredFile;
    }

    public Double getUnreviewedPermission() {
        return unreviewedPermission;
    }

    public void setUnreviewedPermission(Double unreviewedPermission) {
        this.unreviewedPermission = unreviewedPermission;
    }

    public BigDecimal getPublishedCart() {
        return publishedCart;
    }

    public void setPublishedCart(BigDecimal publishedCart) {
        this.publishedCart = publishedCart;
    }

    public BigDecimal getPublicAddress() {
        return publicAddress;
    }

    public void setPublicAddress(BigDecimal publicAddress) {
        this.publicAddress = publicAddress;
    }

    public String getNewReview() {
        return newReview;
    }

    public void setNewReview(String newReview) {
        this.newReview = newReview;
    }

    public Float getOldEmail() {
        return oldEmail;
    }

    public void setOldEmail(Float oldEmail) {
        this.oldEmail = oldEmail;
    }

    public List getSecondaryPermission() {
        return secondaryPermission;
    }

    public void setSecondaryPermission(List secondaryPermission) {
        this.secondaryPermission = secondaryPermission;
    }

    public BigDecimal getHiddenBill() {
        return hiddenBill;
    }

    public void setHiddenBill(BigDecimal hiddenBill) {
        this.hiddenBill = hiddenBill;
    }

    public List getDisabledTransaction() {
        return disabledTransaction;
    }

    public void setDisabledTransaction(List disabledTransaction) {
        this.disabledTransaction = disabledTransaction;
    }

    public Float getSyncedStock() {
        return syncedStock;
    }

    public void setSyncedStock(Float syncedStock) {
        this.syncedStock = syncedStock;
    }

    public Float getExportedAsset() {
        return exportedAsset;
    }

    public void setExportedAsset(Float exportedAsset) {
        this.exportedAsset = exportedAsset;
    }

    public Double getClosedSupplier() {
        return closedSupplier;
    }

    public void setClosedSupplier(Double closedSupplier) {
        this.closedSupplier = closedSupplier;
    }

    public Set getCurrentDepartment() {
        return currentDepartment;
    }

    public void setCurrentDepartment(Set currentDepartment) {
        this.currentDepartment = currentDepartment;
    }

    public List getFlaggedStrategy() {
        return flaggedStrategy;
    }

    public void setFlaggedStrategy(List flaggedStrategy) {
        this.flaggedStrategy = flaggedStrategy;
    }

    public Float getPermanentSession() {
        return permanentSession;
    }

    public void setPermanentSession(Float permanentSession) {
        this.permanentSession = permanentSession;
    }

    public String getLastRecord() {
        return lastRecord;
    }

    public void setLastRecord(String lastRecord) {
        this.lastRecord = lastRecord;
    }

    public List getLevelBrand() {
        return levelBrand;
    }

    public void setLevelBrand(List levelBrand) {
        this.levelBrand = levelBrand;
    }

    public BigDecimal getPublishedEvent() {
        return publishedEvent;
    }

    public void setPublishedEvent(BigDecimal publishedEvent) {
        this.publishedEvent = publishedEvent;
    }

    public Float getFirstTeam() {
        return firstTeam;
    }

    public void setFirstTeam(Float firstTeam) {
        this.firstTeam = firstTeam;
    }

    public Float getCountReport() {
        return countReport;
    }

    public void setCountReport(Float countReport) {
        this.countReport = countReport;
    }

    public String getOldResource() {
        return oldResource;
    }

    public void setOldResource(String oldResource) {
        this.oldResource = oldResource;
    }

    public List getDraftProduct() {
        return draftProduct;
    }

    public void setDraftProduct(List draftProduct) {
        this.draftProduct = draftProduct;
    }

    public Long getInactiveSupplier() {
        return inactiveSupplier;
    }

    public void setInactiveSupplier(Long inactiveSupplier) {
        this.inactiveSupplier = inactiveSupplier;
    }

    public Double getUnverifiedDiscount() {
        return unverifiedDiscount;
    }

    public void setUnverifiedDiscount(Double unverifiedDiscount) {
        this.unverifiedDiscount = unverifiedDiscount;
    }

    public Float getNewTask() {
        return newTask;
    }

    public void setNewTask(Float newTask) {
        this.newTask = newTask;
    }

    public String getTotalName() {
        return totalName;
    }

    public void setTotalName(String totalName) {
        this.totalName = totalName;
    }

    public String getOpenShipment() {
        return openShipment;
    }

    public void setOpenShipment(String openShipment) {
        this.openShipment = openShipment;
    }

    public Float getInactiveBrand() {
        return inactiveBrand;
    }

    public void setInactiveBrand(Float inactiveBrand) {
        this.inactiveBrand = inactiveBrand;
    }

    public String getTotalMerchant() {
        return totalMerchant;
    }

    public void setTotalMerchant(String totalMerchant) {
        this.totalMerchant = totalMerchant;
    }

    public Boolean getCountPolicy() {
        return countPolicy;
    }

    public void setCountPolicy(Boolean countPolicy) {
        this.countPolicy = countPolicy;
    }

    public String getTypeTask() {
        return typeTask;
    }

    public void setTypeTask(String typeTask) {
        this.typeTask = typeTask;
    }

    public Integer getOpenPolicy() {
        return openPolicy;
    }

    public void setOpenPolicy(Integer openPolicy) {
        this.openPolicy = openPolicy;
    }

    public String getClosedWarehouse() {
        return closedWarehouse;
    }

    public void setClosedWarehouse(String closedWarehouse) {
        this.closedWarehouse = closedWarehouse;
    }

    public Integer getClosedCart() {
        return closedCart;
    }

    public void setClosedCart(Integer closedCart) {
        this.closedCart = closedCart;
    }

    public Integer getTemporaryFeedback() {
        return temporaryFeedback;
    }

    public void setTemporaryFeedback(Integer temporaryFeedback) {
        this.temporaryFeedback = temporaryFeedback;
    }

    public Double getDisabledTask() {
        return disabledTask;
    }

    public void setDisabledTask(Double disabledTask) {
        this.disabledTask = disabledTask;
    }

    public Set getFinalAchievement() {
        return finalAchievement;
    }

    public void setFinalAchievement(Set finalAchievement) {
        this.finalAchievement = finalAchievement;
    }

    public Boolean getDisabledShipment() {
        return disabledShipment;
    }

    public void setDisabledShipment(Boolean disabledShipment) {
        this.disabledShipment = disabledShipment;
    }

    public Double getTemporaryFile() {
        return temporaryFile;
    }

    public void setTemporaryFile(Double temporaryFile) {
        this.temporaryFile = temporaryFile;
    }

    public Integer getSyncedName() {
        return syncedName;
    }

    public void setSyncedName(Integer syncedName) {
        this.syncedName = syncedName;
    }

    public Float getUpdatedTask() {
        return updatedTask;
    }

    public void setUpdatedTask(Float updatedTask) {
        this.updatedTask = updatedTask;
    }

    public Float getFinalPermission() {
        return finalPermission;
    }

    public void setFinalPermission(Float finalPermission) {
        this.finalPermission = finalPermission;
    }

    public Set getArchivedOrder() {
        return archivedOrder;
    }

    public void setArchivedOrder(Set archivedOrder) {
        this.archivedOrder = archivedOrder;
    }

    public List getModifiedInvoice() {
        return modifiedInvoice;
    }

    public void setModifiedInvoice(List modifiedInvoice) {
        this.modifiedInvoice = modifiedInvoice;
    }

    public Integer getCompletedTask() {
        return completedTask;
    }

    public void setCompletedTask(Integer completedTask) {
        this.completedTask = completedTask;
    }

    public String getUnreportedDepartment() {
        return unreportedDepartment;
    }

    public void setUnreportedDepartment(String unreportedDepartment) {
        this.unreportedDepartment = unreportedDepartment;
    }

    public Integer getInactiveCoupon() {
        return inactiveCoupon;
    }

    public void setInactiveCoupon(Integer inactiveCoupon) {
        this.inactiveCoupon = inactiveCoupon;
    }

    public BigDecimal getPrimaryTask() {
        return primaryTask;
    }

    public void setPrimaryTask(BigDecimal primaryTask) {
        this.primaryTask = primaryTask;
    }

    public BigDecimal getUnreportedAgreement() {
        return unreportedAgreement;
    }

    public void setUnreportedAgreement(BigDecimal unreportedAgreement) {
        this.unreportedAgreement = unreportedAgreement;
    }

    public Boolean getVerifiedTransaction() {
        return verifiedTransaction;
    }

    public void setVerifiedTransaction(Boolean verifiedTransaction) {
        this.verifiedTransaction = verifiedTransaction;
    }

    public Long getSuccessfulProject() {
        return successfulProject;
    }

    public void setSuccessfulProject(Long successfulProject) {
        this.successfulProject = successfulProject;
    }

    public String getTemporaryTask() {
        return temporaryTask;
    }

    public void setTemporaryTask(String temporaryTask) {
        this.temporaryTask = temporaryTask;
    }

    public Float getSecureSchedule() {
        return secureSchedule;
    }

    public void setSecureSchedule(Float secureSchedule) {
        this.secureSchedule = secureSchedule;
    }

    public List getTotalEmail() {
        return totalEmail;
    }

    public void setTotalEmail(List totalEmail) {
        this.totalEmail = totalEmail;
    }

    public Long getLoadedHistory() {
        return loadedHistory;
    }

    public void setLoadedHistory(Long loadedHistory) {
        this.loadedHistory = loadedHistory;
    }

    public Boolean getRejectedSession() {
        return rejectedSession;
    }

    public void setRejectedSession(Boolean rejectedSession) {
        this.rejectedSession = rejectedSession;
    }

    public List getTypeCustomer() {
        return typeCustomer;
    }

    public void setTypeCustomer(List typeCustomer) {
        this.typeCustomer = typeCustomer;
    }

    public Double getOpenDiscount() {
        return openDiscount;
    }

    public void setOpenDiscount(Double openDiscount) {
        this.openDiscount = openDiscount;
    }

    public Boolean getInitialBrand() {
        return initialBrand;
    }

    public void setInitialBrand(Boolean initialBrand) {
        this.initialBrand = initialBrand;
    }

    public String getLoadedPhone() {
        return loadedPhone;
    }

    public void setLoadedPhone(String loadedPhone) {
        this.loadedPhone = loadedPhone;
    }

    public Boolean getNewGoal() {
        return newGoal;
    }

    public void setNewGoal(Boolean newGoal) {
        this.newGoal = newGoal;
    }

    public Integer getUpdatedUser() {
        return updatedUser;
    }

    public void setUpdatedUser(Integer updatedUser) {
        this.updatedUser = updatedUser;
    }

    public BigDecimal getTypeName() {
        return typeName;
    }

    public void setTypeName(BigDecimal typeName) {
        this.typeName = typeName;
    }

    public String getMaxPlan() {
        return maxPlan;
    }

    public void setMaxPlan(String maxPlan) {
        this.maxPlan = maxPlan;
    }

    public Integer getReportedLog() {
        return reportedLog;
    }

    public void setReportedLog(Integer reportedLog) {
        this.reportedLog = reportedLog;
    }

    public Boolean getModifiedFeedback() {
        return modifiedFeedback;
    }

    public void setModifiedFeedback(Boolean modifiedFeedback) {
        this.modifiedFeedback = modifiedFeedback;
    }

    public Long getPublishedRole() {
        return publishedRole;
    }

    public void setPublishedRole(Long publishedRole) {
        this.publishedRole = publishedRole;
    }

    public String getUnsecureCoupon() {
        return unsecureCoupon;
    }

    public void setUnsecureCoupon(String unsecureCoupon) {
        this.unsecureCoupon = unsecureCoupon;
    }

    public Long getSyncedCart() {
        return syncedCart;
    }

    public void setSyncedCart(Long syncedCart) {
        this.syncedCart = syncedCart;
    }

    public Boolean getDecryptedHistory() {
        return decryptedHistory;
    }

    public void setDecryptedHistory(Boolean decryptedHistory) {
        this.decryptedHistory = decryptedHistory;
    }

    public String getTemporaryHistory() {
        return temporaryHistory;
    }

    public void setTemporaryHistory(String temporaryHistory) {
        this.temporaryHistory = temporaryHistory;
    }

    public String getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(String updatedDate) {
        this.updatedDate = updatedDate;
    }

    public List getPrivateBrand() {
        return privateBrand;
    }

    public void setPrivateBrand(List privateBrand) {
        this.privateBrand = privateBrand;
    }

    public String getCompletedActivity() {
        return completedActivity;
    }

    public void setCompletedActivity(String completedActivity) {
        this.completedActivity = completedActivity;
    }

    public Float getPendingFeedback() {
        return pendingFeedback;
    }

    public void setPendingFeedback(Float pendingFeedback) {
        this.pendingFeedback = pendingFeedback;
    }

    public BigDecimal getOpenActivity() {
        return openActivity;
    }

    public void setOpenActivity(BigDecimal openActivity) {
        this.openActivity = openActivity;
    }

    public Set getTypePlan() {
        return typePlan;
    }

    public void setTypePlan(Set typePlan) {
        this.typePlan = typePlan;
    }

    public Float getUnlockedEvent() {
        return unlockedEvent;
    }

    public void setUnlockedEvent(Float unlockedEvent) {
        this.unlockedEvent = unlockedEvent;
    }

    public String getEnabledDocument() {
        return enabledDocument;
    }

    public void setEnabledDocument(String enabledDocument) {
        this.enabledDocument = enabledDocument;
    }

    public Set getVerifiedEvent() {
        return verifiedEvent;
    }

    public void setVerifiedEvent(Set verifiedEvent) {
        this.verifiedEvent = verifiedEvent;
    }

    public Float getReviewedMerchant() {
        return reviewedMerchant;
    }

    public void setReviewedMerchant(Float reviewedMerchant) {
        this.reviewedMerchant = reviewedMerchant;
    }

    public String getLevelWarehouse() {
        return levelWarehouse;
    }

    public void setLevelWarehouse(String levelWarehouse) {
        this.levelWarehouse = levelWarehouse;
    }

    public Boolean getUnflaggedCoupon() {
        return unflaggedCoupon;
    }

    public void setUnflaggedCoupon(Boolean unflaggedCoupon) {
        this.unflaggedCoupon = unflaggedCoupon;
    }

    public Boolean getUnflaggedPermission() {
        return unflaggedPermission;
    }

    public void setUnflaggedPermission(Boolean unflaggedPermission) {
        this.unflaggedPermission = unflaggedPermission;
    }

    public List getExportedPhone() {
        return exportedPhone;
    }

    public void setExportedPhone(List exportedPhone) {
        this.exportedPhone = exportedPhone;
    }

    public Float getPublicDocument() {
        return publicDocument;
    }

    public void setPublicDocument(Float publicDocument) {
        this.publicDocument = publicDocument;
    }

    public Float getEnabledCoupon() {
        return enabledCoupon;
    }

    public void setEnabledCoupon(Float enabledCoupon) {
        this.enabledCoupon = enabledCoupon;
    }

    public Long getInactiveStrategy() {
        return inactiveStrategy;
    }

    public void setInactiveStrategy(Long inactiveStrategy) {
        this.inactiveStrategy = inactiveStrategy;
    }

    public String getCurrentRecord() {
        return currentRecord;
    }

    public void setCurrentRecord(String currentRecord) {
        this.currentRecord = currentRecord;
    }

    public List getClosedTeam() {
        return closedTeam;
    }

    public void setClosedTeam(List closedTeam) {
        this.closedTeam = closedTeam;
    }

    public List getPermanentTransaction() {
        return permanentTransaction;
    }

    public void setPermanentTransaction(List permanentTransaction) {
        this.permanentTransaction = permanentTransaction;
    }

    public BigDecimal getPublishedCategory() {
        return publishedCategory;
    }

    public void setPublishedCategory(BigDecimal publishedCategory) {
        this.publishedCategory = publishedCategory;
    }

    public Boolean getTypeShipment() {
        return typeShipment;
    }

    public void setTypeShipment(Boolean typeShipment) {
        this.typeShipment = typeShipment;
    }

    public Double getLevelMessage() {
        return levelMessage;
    }

    public void setLevelMessage(Double levelMessage) {
        this.levelMessage = levelMessage;
    }

    public Boolean getCreatedHistory() {
        return createdHistory;
    }

    public void setCreatedHistory(Boolean createdHistory) {
        this.createdHistory = createdHistory;
    }

    public Long getPendingPrice() {
        return pendingPrice;
    }

    public void setPendingPrice(Long pendingPrice) {
        this.pendingPrice = pendingPrice;
    }

    public Integer getDecryptedAddress() {
        return decryptedAddress;
    }

    public void setDecryptedAddress(Integer decryptedAddress) {
        this.decryptedAddress = decryptedAddress;
    }

    public List getNextAsset() {
        return nextAsset;
    }

    public void setNextAsset(List nextAsset) {
        this.nextAsset = nextAsset;
    }

    public Double getPublishedActivity() {
        return publishedActivity;
    }

    public void setPublishedActivity(Double publishedActivity) {
        this.publishedActivity = publishedActivity;
    }
}
