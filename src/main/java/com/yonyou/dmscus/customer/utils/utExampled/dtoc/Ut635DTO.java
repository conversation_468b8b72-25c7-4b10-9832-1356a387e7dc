package com.yonyou.dmscus.customer.utils.utExampled.dtoc;

public class Ut635DTO {

    private String field110053;
    private String field210053;
    private String field310053;
    private String field410053;
    private String field510053;
    private String field610053;
    private String field710053;
    private String field810053;
    private String field910053;
    private String field1010053;
    private String field1110053;
    private String field1210053;
    private String field1310053;
    private String field1410053;
    private String field1510053;
    private String field1610053;
    private String field1710053;
    private String field1810053;
    private String field1910053;
    private String field2010053;
    private String field2110053;
    private String field2210053;
    private String field2310053;
    private String field2410053;
    private String field2510053;
    private String field2610053;
    private String field2710053;
    private String field2810053;
    private String field2910053;
    private String field3010053;
    private String field3110053;
    private String field3210053;
    private String field3310053;
    private String field3410053;
    private String field3510053;
    private String field3610053;
    private String field3710053;
    private String field3810053;
    private String field3910053;
    private String field4010053;
    private String field4110053;
    private String field4210053;
    private String field4310053;
    private String field4410053;
    private String field4510053;
    private String field4610053;
    private String field4710053;
    private String field4810053;
    private String field4910053;
    private String field5110053;
    private String field5210053;
    private String field5310053;
    private String field5410053;
    private String field5510053;
    private String field5610053;
    private String field5710053;
    private String field5810053;
    private String field5910053;
    private String field6010053;
    private String field6110053;
    private String field6210053;
    private String field6310053;
    private String field6410053;
    private String field6510053;
    private String field6610053;
    private String field6710053;
    private String field6810053;
    private String field6910053;
    private String field7010053;
    private String field7110053;
    private String field7210053;
    private String field7310053;
    private String field7410053;
    private String field7510053;
    private String field7610053;
    private String field7710053;
    private String field7810053;
    private String field7910053;
    private String field8010053;
    private String field8110053;
    private String field8210053;
    private String field8310053;
    private String field8410053;
    private String field8510053;
    private String field8610053;
    private String field8710053;
    private String field8810053;
    private String field8910053;
    private String field9010053;
    private String field9110053;
    private String field9210053;
    private String field9310053;
    private String field9410053;
    private String field9510053;
    private String field9610053;
    private String field9710053;
    private String field9810053;
    private String field9910053;
    private String field10010053;

    public String getField110053() {
        return field110053;
    }

    public void setField110053(String field110053) {
        this.field110053 = field110053;
    }

    public String getField210053() {
        return field210053;
    }

    public void setField210053(String field210053) {
        this.field210053 = field210053;
    }

    public String getField310053() {
        return field310053;
    }

    public void setField310053(String field310053) {
        this.field310053 = field310053;
    }

    public String getField410053() {
        return field410053;
    }

    public void setField410053(String field410053) {
        this.field410053 = field410053;
    }

    public String getField510053() {
        return field510053;
    }

    public void setField510053(String field510053) {
        this.field510053 = field510053;
    }

    public String getField610053() {
        return field610053;
    }

    public void setField610053(String field610053) {
        this.field610053 = field610053;
    }

    public String getField710053() {
        return field710053;
    }

    public void setField710053(String field710053) {
        this.field710053 = field710053;
    }

    public String getField810053() {
        return field810053;
    }

    public void setField810053(String field810053) {
        this.field810053 = field810053;
    }

    public String getField910053() {
        return field910053;
    }

    public void setField910053(String field910053) {
        this.field910053 = field910053;
    }

    public String getField1010053() {
        return field1010053;
    }

    public void setField1010053(String field1010053) {
        this.field1010053 = field1010053;
    }

    public String getField1110053() {
        return field1110053;
    }

    public void setField1110053(String field1110053) {
        this.field1110053 = field1110053;
    }

    public String getField1210053() {
        return field1210053;
    }

    public void setField1210053(String field1210053) {
        this.field1210053 = field1210053;
    }

    public String getField1310053() {
        return field1310053;
    }

    public void setField1310053(String field1310053) {
        this.field1310053 = field1310053;
    }

    public String getField1410053() {
        return field1410053;
    }

    public void setField1410053(String field1410053) {
        this.field1410053 = field1410053;
    }

    public String getField1510053() {
        return field1510053;
    }

    public void setField1510053(String field1510053) {
        this.field1510053 = field1510053;
    }

    public String getField1610053() {
        return field1610053;
    }

    public void setField1610053(String field1610053) {
        this.field1610053 = field1610053;
    }

    public String getField1710053() {
        return field1710053;
    }

    public void setField1710053(String field1710053) {
        this.field1710053 = field1710053;
    }

    public String getField1810053() {
        return field1810053;
    }

    public void setField1810053(String field1810053) {
        this.field1810053 = field1810053;
    }

    public String getField1910053() {
        return field1910053;
    }

    public void setField1910053(String field1910053) {
        this.field1910053 = field1910053;
    }

    public String getField2010053() {
        return field2010053;
    }

    public void setField2010053(String field2010053) {
        this.field2010053 = field2010053;
    }

    public String getField2110053() {
        return field2110053;
    }

    public void setField2110053(String field2110053) {
        this.field2110053 = field2110053;
    }

    public String getField2210053() {
        return field2210053;
    }

    public void setField2210053(String field2210053) {
        this.field2210053 = field2210053;
    }

    public String getField2310053() {
        return field2310053;
    }

    public void setField2310053(String field2310053) {
        this.field2310053 = field2310053;
    }

    public String getField2410053() {
        return field2410053;
    }

    public void setField2410053(String field2410053) {
        this.field2410053 = field2410053;
    }

    public String getField2510053() {
        return field2510053;
    }

    public void setField2510053(String field2510053) {
        this.field2510053 = field2510053;
    }

    public String getField2610053() {
        return field2610053;
    }

    public void setField2610053(String field2610053) {
        this.field2610053 = field2610053;
    }

    public String getField2710053() {
        return field2710053;
    }

    public void setField2710053(String field2710053) {
        this.field2710053 = field2710053;
    }

    public String getField2810053() {
        return field2810053;
    }

    public void setField2810053(String field2810053) {
        this.field2810053 = field2810053;
    }

    public String getField2910053() {
        return field2910053;
    }

    public void setField2910053(String field2910053) {
        this.field2910053 = field2910053;
    }

    public String getField3010053() {
        return field3010053;
    }

    public void setField3010053(String field3010053) {
        this.field3010053 = field3010053;
    }

    public String getField3110053() {
        return field3110053;
    }

    public void setField3110053(String field3110053) {
        this.field3110053 = field3110053;
    }

    public String getField3210053() {
        return field3210053;
    }

    public void setField3210053(String field3210053) {
        this.field3210053 = field3210053;
    }

    public String getField3310053() {
        return field3310053;
    }

    public void setField3310053(String field3310053) {
        this.field3310053 = field3310053;
    }

    public String getField3410053() {
        return field3410053;
    }

    public void setField3410053(String field3410053) {
        this.field3410053 = field3410053;
    }

    public String getField3510053() {
        return field3510053;
    }

    public void setField3510053(String field3510053) {
        this.field3510053 = field3510053;
    }

    public String getField3610053() {
        return field3610053;
    }

    public void setField3610053(String field3610053) {
        this.field3610053 = field3610053;
    }

    public String getField3710053() {
        return field3710053;
    }

    public void setField3710053(String field3710053) {
        this.field3710053 = field3710053;
    }

    public String getField3810053() {
        return field3810053;
    }

    public void setField3810053(String field3810053) {
        this.field3810053 = field3810053;
    }

    public String getField3910053() {
        return field3910053;
    }

    public void setField3910053(String field3910053) {
        this.field3910053 = field3910053;
    }

    public String getField4010053() {
        return field4010053;
    }

    public void setField4010053(String field4010053) {
        this.field4010053 = field4010053;
    }

    public String getField4110053() {
        return field4110053;
    }

    public void setField4110053(String field4110053) {
        this.field4110053 = field4110053;
    }

    public String getField4210053() {
        return field4210053;
    }

    public void setField4210053(String field4210053) {
        this.field4210053 = field4210053;
    }

    public String getField4310053() {
        return field4310053;
    }

    public void setField4310053(String field4310053) {
        this.field4310053 = field4310053;
    }

    public String getField4410053() {
        return field4410053;
    }

    public void setField4410053(String field4410053) {
        this.field4410053 = field4410053;
    }

    public String getField4510053() {
        return field4510053;
    }

    public void setField4510053(String field4510053) {
        this.field4510053 = field4510053;
    }

    public String getField4610053() {
        return field4610053;
    }

    public void setField4610053(String field4610053) {
        this.field4610053 = field4610053;
    }

    public String getField4710053() {
        return field4710053;
    }

    public void setField4710053(String field4710053) {
        this.field4710053 = field4710053;
    }

    public String getField4810053() {
        return field4810053;
    }

    public void setField4810053(String field4810053) {
        this.field4810053 = field4810053;
    }

    public String getField4910053() {
        return field4910053;
    }

    public void setField4910053(String field4910053) {
        this.field4910053 = field4910053;
    }

    public String getField5110053() {
        return field5110053;
    }

    public void setField5110053(String field5110053) {
        this.field5110053 = field5110053;
    }

    public String getField5210053() {
        return field5210053;
    }

    public void setField5210053(String field5210053) {
        this.field5210053 = field5210053;
    }

    public String getField5310053() {
        return field5310053;
    }

    public void setField5310053(String field5310053) {
        this.field5310053 = field5310053;
    }

    public String getField5410053() {
        return field5410053;
    }

    public void setField5410053(String field5410053) {
        this.field5410053 = field5410053;
    }

    public String getField5510053() {
        return field5510053;
    }

    public void setField5510053(String field5510053) {
        this.field5510053 = field5510053;
    }

    public String getField5610053() {
        return field5610053;
    }

    public void setField5610053(String field5610053) {
        this.field5610053 = field5610053;
    }

    public String getField5710053() {
        return field5710053;
    }

    public void setField5710053(String field5710053) {
        this.field5710053 = field5710053;
    }

    public String getField5810053() {
        return field5810053;
    }

    public void setField5810053(String field5810053) {
        this.field5810053 = field5810053;
    }

    public String getField5910053() {
        return field5910053;
    }

    public void setField5910053(String field5910053) {
        this.field5910053 = field5910053;
    }

    public String getField6010053() {
        return field6010053;
    }

    public void setField6010053(String field6010053) {
        this.field6010053 = field6010053;
    }

    public String getField6110053() {
        return field6110053;
    }

    public void setField6110053(String field6110053) {
        this.field6110053 = field6110053;
    }

    public String getField6210053() {
        return field6210053;
    }

    public void setField6210053(String field6210053) {
        this.field6210053 = field6210053;
    }

    public String getField6310053() {
        return field6310053;
    }

    public void setField6310053(String field6310053) {
        this.field6310053 = field6310053;
    }

    public String getField6410053() {
        return field6410053;
    }

    public void setField6410053(String field6410053) {
        this.field6410053 = field6410053;
    }

    public String getField6510053() {
        return field6510053;
    }

    public void setField6510053(String field6510053) {
        this.field6510053 = field6510053;
    }

    public String getField6610053() {
        return field6610053;
    }

    public void setField6610053(String field6610053) {
        this.field6610053 = field6610053;
    }

    public String getField6710053() {
        return field6710053;
    }

    public void setField6710053(String field6710053) {
        this.field6710053 = field6710053;
    }

    public String getField6810053() {
        return field6810053;
    }

    public void setField6810053(String field6810053) {
        this.field6810053 = field6810053;
    }

    public String getField6910053() {
        return field6910053;
    }

    public void setField6910053(String field6910053) {
        this.field6910053 = field6910053;
    }

    public String getField7010053() {
        return field7010053;
    }

    public void setField7010053(String field7010053) {
        this.field7010053 = field7010053;
    }

    public String getField7110053() {
        return field7110053;
    }

    public void setField7110053(String field7110053) {
        this.field7110053 = field7110053;
    }

    public String getField7210053() {
        return field7210053;
    }

    public void setField7210053(String field7210053) {
        this.field7210053 = field7210053;
    }

    public String getField7310053() {
        return field7310053;
    }

    public void setField7310053(String field7310053) {
        this.field7310053 = field7310053;
    }

    public String getField7410053() {
        return field7410053;
    }

    public void setField7410053(String field7410053) {
        this.field7410053 = field7410053;
    }

    public String getField7510053() {
        return field7510053;
    }

    public void setField7510053(String field7510053) {
        this.field7510053 = field7510053;
    }

    public String getField7610053() {
        return field7610053;
    }

    public void setField7610053(String field7610053) {
        this.field7610053 = field7610053;
    }

    public String getField7710053() {
        return field7710053;
    }

    public void setField7710053(String field7710053) {
        this.field7710053 = field7710053;
    }

    public String getField7810053() {
        return field7810053;
    }

    public void setField7810053(String field7810053) {
        this.field7810053 = field7810053;
    }

    public String getField7910053() {
        return field7910053;
    }

    public void setField7910053(String field7910053) {
        this.field7910053 = field7910053;
    }

    public String getField8010053() {
        return field8010053;
    }

    public void setField8010053(String field8010053) {
        this.field8010053 = field8010053;
    }

    public String getField8110053() {
        return field8110053;
    }

    public void setField8110053(String field8110053) {
        this.field8110053 = field8110053;
    }

    public String getField8210053() {
        return field8210053;
    }

    public void setField8210053(String field8210053) {
        this.field8210053 = field8210053;
    }

    public String getField8310053() {
        return field8310053;
    }

    public void setField8310053(String field8310053) {
        this.field8310053 = field8310053;
    }

    public String getField8410053() {
        return field8410053;
    }

    public void setField8410053(String field8410053) {
        this.field8410053 = field8410053;
    }

    public String getField8510053() {
        return field8510053;
    }

    public void setField8510053(String field8510053) {
        this.field8510053 = field8510053;
    }

    public String getField8610053() {
        return field8610053;
    }

    public void setField8610053(String field8610053) {
        this.field8610053 = field8610053;
    }

    public String getField8710053() {
        return field8710053;
    }

    public void setField8710053(String field8710053) {
        this.field8710053 = field8710053;
    }

    public String getField8810053() {
        return field8810053;
    }

    public void setField8810053(String field8810053) {
        this.field8810053 = field8810053;
    }

    public String getField8910053() {
        return field8910053;
    }

    public void setField8910053(String field8910053) {
        this.field8910053 = field8910053;
    }

    public String getField9010053() {
        return field9010053;
    }

    public void setField9010053(String field9010053) {
        this.field9010053 = field9010053;
    }

    public String getField9110053() {
        return field9110053;
    }

    public void setField9110053(String field9110053) {
        this.field9110053 = field9110053;
    }

    public String getField9210053() {
        return field9210053;
    }

    public void setField9210053(String field9210053) {
        this.field9210053 = field9210053;
    }

    public String getField9310053() {
        return field9310053;
    }

    public void setField9310053(String field9310053) {
        this.field9310053 = field9310053;
    }

    public String getField9410053() {
        return field9410053;
    }

    public void setField9410053(String field9410053) {
        this.field9410053 = field9410053;
    }

    public String getField9510053() {
        return field9510053;
    }

    public void setField9510053(String field9510053) {
        this.field9510053 = field9510053;
    }

    public String getField9610053() {
        return field9610053;
    }

    public void setField9610053(String field9610053) {
        this.field9610053 = field9610053;
    }

    public String getField9710053() {
        return field9710053;
    }

    public void setField9710053(String field9710053) {
        this.field9710053 = field9710053;
    }

    public String getField9810053() {
        return field9810053;
    }

    public void setField9810053(String field9810053) {
        this.field9810053 = field9810053;
    }

    public String getField9910053() {
        return field9910053;
    }

    public void setField9910053(String field9910053) {
        this.field9910053 = field9910053;
    }

    public String getField10010053() {
        return field10010053;
    }

    public void setField10010053(String field10010053) {
        this.field10010053 = field10010053;
    }

    @Override
    public String toString() {
        return "Ut51DTO{" +
                "field110053='" + field110053 + '\'' +
                ", field210053='" + field210053 + '\'' +
                ", field310053='" + field310053 + '\'' +
                ", field410053='" + field410053 + '\'' +
                ", field510053='" + field510053 + '\'' +
                ", field610053='" + field610053 + '\'' +
                ", field710053='" + field710053 + '\'' +
                ", field810053='" + field810053 + '\'' +
                ", field910053='" + field910053 + '\'' +
                ", field1010053='" + field1010053 + '\'' +
                ", field1110053='" + field1110053 + '\'' +
                ", field1210053='" + field1210053 + '\'' +
                ", field1310053='" + field1310053 + '\'' +
                ", field1410053='" + field1410053 + '\'' +
                ", field1510053='" + field1510053 + '\'' +
                ", field1610053='" + field1610053 + '\'' +
                ", field1710053='" + field1710053 + '\'' +
                ", field1810053='" + field1810053 + '\'' +
                ", field1910053='" + field1910053 + '\'' +
                ", field2010053='" + field2010053 + '\'' +
                ", field2110053='" + field2110053 + '\'' +
                ", field2210053='" + field2210053 + '\'' +
                ", field2310053='" + field2310053 + '\'' +
                ", field2410053='" + field2410053 + '\'' +
                ", field2510053='" + field2510053 + '\'' +
                ", field2610053='" + field2610053 + '\'' +
                ", field2710053='" + field2710053 + '\'' +
                ", field2810053='" + field2810053 + '\'' +
                ", field2910053='" + field2910053 + '\'' +
                ", field3010053='" + field3010053 + '\'' +
                ", field3110053='" + field3110053 + '\'' +
                ", field3210053='" + field3210053 + '\'' +
                ", field3310053='" + field3310053 + '\'' +
                ", field3410053='" + field3410053 + '\'' +
                ", field3510053='" + field3510053 + '\'' +
                ", field3610053='" + field3610053 + '\'' +
                ", field3710053='" + field3710053 + '\'' +
                ", field3810053='" + field3810053 + '\'' +
                ", field3910053='" + field3910053 + '\'' +
                ", field4010053='" + field4010053 + '\'' +
                ", field4110053='" + field4110053 + '\'' +
                ", field4210053='" + field4210053 + '\'' +
                ", field4310053='" + field4310053 + '\'' +
                ", field4410053='" + field4410053 + '\'' +
                ", field4510053='" + field4510053 + '\'' +
                ", field4610053='" + field4610053 + '\'' +
                ", field4710053='" + field4710053 + '\'' +
                ", field4810053='" + field4810053 + '\'' +
                ", field4910053='" + field4910053 + '\'' +
                ", field5110053='" + field5110053 + '\'' +
                ", field5210053='" + field5210053 + '\'' +
                ", field5310053='" + field5310053 + '\'' +
                ", field5410053='" + field5410053 + '\'' +
                ", field5510053='" + field5510053 + '\'' +
                ", field5610053='" + field5610053 + '\'' +
                ", field5710053='" + field5710053 + '\'' +
                ", field5810053='" + field5810053 + '\'' +
                ", field5910053='" + field5910053 + '\'' +
                ", field6010053='" + field6010053 + '\'' +
                ", field6110053='" + field6110053 + '\'' +
                ", field6210053='" + field6210053 + '\'' +
                ", field6310053='" + field6310053 + '\'' +
                ", field6410053='" + field6410053 + '\'' +
                ", field6510053='" + field6510053 + '\'' +
                ", field6610053='" + field6610053 + '\'' +
                ", field6710053='" + field6710053 + '\'' +
                ", field6810053='" + field6810053 + '\'' +
                ", field6910053='" + field6910053 + '\'' +
                ", field7010053='" + field7010053 + '\'' +
                ", field7110053='" + field7110053 + '\'' +
                ", field7210053='" + field7210053 + '\'' +
                ", field7310053='" + field7310053 + '\'' +
                ", field7410053='" + field7410053 + '\'' +
                ", field7510053='" + field7510053 + '\'' +
                ", field7610053='" + field7610053 + '\'' +
                ", field7710053='" + field7710053 + '\'' +
                ", field7810053='" + field7810053 + '\'' +
                ", field7910053='" + field7910053 + '\'' +
                ", field8010053='" + field8010053 + '\'' +
                ", field8110053='" + field8110053 + '\'' +
                ", field8210053='" + field8210053 + '\'' +
                ", field8310053='" + field8310053 + '\'' +
                ", field8410053='" + field8410053 + '\'' +
                ", field8510053='" + field8510053 + '\'' +
                ", field8610053='" + field8610053 + '\'' +
                ", field8710053='" + field8710053 + '\'' +
                ", field8810053='" + field8810053 + '\'' +
                ", field8910053='" + field8910053 + '\'' +
                ", field9010053='" + field9010053 + '\'' +
                ", field9110053='" + field9110053 + '\'' +
                ", field9210053='" + field9210053 + '\'' +
                ", field9310053='" + field9310053 + '\'' +
                ", field9410053='" + field9410053 + '\'' +
                ", field9510053='" + field9510053 + '\'' +
                ", field9610053='" + field9610053 + '\'' +
                ", field9710053='" + field9710053 + '\'' +
                ", field9810053='" + field9810053 + '\'' +
                ", field9910053='" + field9910053 + '\'' +
                ", field10010053='" + field10010053 + '\'' +
                '}';
    }
}
