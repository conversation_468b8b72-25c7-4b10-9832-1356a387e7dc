package com.yonyou.dmscus.customer.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.AppPushDTO;
import com.yonyou.dmscus.customer.service.accidentClues.AccidentCluesService;
import com.yonyou.dmscus.customer.service.cdpTagTask.qb.CdpTagTaskService;
import com.yonyou.dmscus.customer.service.clueMigrate.ITmClueMigrateTaskService;
import com.yonyou.dmscus.customer.service.complaint.ComplaintFollowService;
import com.yonyou.dmscus.customer.service.complaint.sale.CommonalityMethodService;
import com.yonyou.dmscus.customer.service.faultLight.CluesDiagnosticInfoRelationService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightClueService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightService;
import com.yonyou.dmscus.customer.service.goodwillTask.GoodwillTaskService;
import com.yonyou.dmscus.customer.service.impl.CommonServiceImpl;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.invitationautocreate.InviteVehicleDoTaskService;
import com.yonyou.dmscus.customer.service.invitationautocreate.InviteVehicleTaskService;
import com.yonyou.dmscus.customer.service.invitationautocreate.VocManageService;
import com.yonyou.dmscus.customer.service.inviteInsurance.InviteInsuranceVehicleTaskService;
import com.yonyou.dmscus.customer.service.oss.OssBusProService;
import com.yonyou.dmscus.customer.service.oss.OssService;
import com.yonyou.dmscus.customer.service.pushRecord.CompensateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class XxlJobHandler {

    public static final String CREATE_DATE = "createDate";
    public static final String OWNER_CODE = "ownerCode";
    public static final String VIN = "vin";
    public static final String PAGE = "page";
    public static final String NUMBER = "number";
    public static final String PARTITION_SIZE = "partitionSize";
    public static final String END_DATE = "endDate";
    @Autowired
    ComplaintFollowService complaintFollowService;

    @Autowired
    GoodwillTaskService goodwillTaskService;

    @Autowired
    InviteVehicleTaskService inviteVehicleTaskService;

    @Autowired
    InviteVehicleDoTaskService inviteVehicleDoTaskService;

    @Autowired
    VocManageService vocManageService;

    @Autowired
    InviteInsuranceVehicleTaskService inviteInsuranceVehicleTaskService;

    @Autowired
    InviteVehicleRecordService inviteVehicleRecordService;

    @Autowired
    CdpTagTaskService cdpTagTaskService;

    @Autowired
    CommonServiceImpl commonService;

    @Autowired
    OssService ossService;

    @Autowired
    OssBusProService ossBusProService;

    @Autowired
    FaultLightService faultLightService;

    @Autowired
    FaultLightClueService faultLightClueService;

    @Resource
    AccidentCluesService accidentCluesService;
    @Resource
    CompensateService compensateService;

    @Resource
    CommonalityMethodService commonMathService;

    @Autowired
    private ITmClueMigrateTaskService clueMigrateTaskService;

    @Autowired
    private CluesDiagnosticInfoRelationService cluesDiagnosticInfoRelationService;

    /**
     * 提醒下次跟进时间
     */
    @XxlJob("remindNextFollowing")
    public void remindNextFollowing(){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<ComplaintInfMoreDTO> queryNextFollowing=complaintFollowService.queryNextFollowing();
        for(int i=0;i<queryNextFollowing.size();i++){

            AppPushDTO appPushDTO=new AppPushDTO();
            List<Integer> idList=new ArrayList<>();
            idList.add(Integer.valueOf(queryNextFollowing.get(i).getFollowName()));
            appPushDTO.setUserIds(idList);
            appPushDTO.setPriority(33081001L);
            appPushDTO.setTitle("售后客诉单跟进提醒");
            JSONObject json = new JSONObject();
            //向json中添加数据
            json.put("complaintNo",queryNextFollowing.get(i).getComplaintId());
            json.put("dealerCode",queryNextFollowing.get(i).getDealerCode());
            json.put("regionManager",queryNextFollowing.get(i).getRegionManager());
            json.put("subject",queryNextFollowing.get(i).getSubject());
            json.put("FollowTime",formatter.format(queryNextFollowing.get(i).getFollowTime()));
            //转换为字符串
            String jsonStr = json.toString();
            appPushDTO.setJson(jsonStr);
            appPushDTO.setContent("您有需要跟进的客诉单:"
                    +"客诉单号:"+queryNextFollowing.get(i).getComplaintId()+",处理经销商:"+queryNextFollowing.get(i).getDealerCode()
                    +",区域经理:"+queryNextFollowing.get(i).getRegionManager()+",主题:"+queryNextFollowing.get(i).getSubject()
                    +",跟进时间:"+ formatter.format(queryNextFollowing.get(i).getFollowTime())
                    +"!点击案件进行处理");
            commonService.messageSendApp(appPushDTO);
        }
    }

    /**
     * 超过2个月自动变为自动拒绝
     */
    @XxlJob("createGoodwillTask")
    public void goodwillToRefuse() {
       goodwillTaskService.goodwillToRefuse();
    }

    /**
     * 商务亲善预申请审批超时提醒
     */
    @XxlJob("goodwillApplyTimeOutTask")
    public void goodwillApplyTimeOutTask() {
        goodwillTaskService.goodwillApplyTimeOutTask();
    }

    /**
     * 商务亲善开票超时通知
     * @return
     */
    @XxlJob("goodwillInvoiceTimeOutTask")
    public void goodwillInvoiceTimeOutTask() {
        goodwillTaskService.goodwillInvoiceTimeOutTask();
    }

    /**
     * 商务亲善材料审核超时提醒（2周）
     */
    @XxlJob("goodwillMatrialAuditOneTask")
    public void goodwillMatrialAuditOneTask() {
        goodwillTaskService.goodwillMatrialAuditOneTask();
    }

    /**
     * 商务亲善材料审核超时提醒（1个月）
     */
    @XxlJob("goodwillMatrialAuditTwoTask")
    public void goodwillMatrialAuditTwoTask() {
        goodwillTaskService.goodwillMatrialAuditTwoTask();
    }

    /**
     * 商务亲善材料提交超时提醒（1个月）
     */
    @XxlJob("goodwillMatrialCommitOneTask")
    public void goodwillMatrialCommitOneTask() {
        goodwillTaskService.goodwillMatrialCommitOneTask();
    }

    /**
     * 商务亲善材料提交过期提醒
     */
    @XxlJob("goodwillMatrialCommitTimeOutTask")
    public void goodwillMatrialCommitTimeOutTask() {
        goodwillTaskService.goodwillMatrialCommitTimeOutTask();
    }

    /**
     * 商务亲善材料审核超时提醒（2个月）
     */
    @XxlJob("goodwillMatrialCommitTwoTask")
    public void goodwillMatrialCommitTwoTask() {
        goodwillTaskService.goodwillMatrialCommitTwoTask();
    }

    /**
     * 商务亲善补充材料提交超时提醒（1个月）
     */
    @XxlJob("goodwillSupplyMatrialCommitOneTask")
    public void goodwillSupplyMatrialCommitOneTask() {
        goodwillTaskService.goodwillSupplyMatrialCommitOneTask();
    }

    /**
     * 商务亲善补充材料提交超时提醒（2个月）
     */
    @XxlJob("goodwillSupplyMatrialCommitTwoTask")
    public void goodwillSupplyMatrialCommitTwoTask() {
        goodwillTaskService.goodwillSupplyMatrialCommitTwoTask();
    }


    /**
     * 自动关闭线索，首保未下发任务
     */
    @XxlJob("closeInvite")
    public void closeInvite(){
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        inviteVehicleTaskService.closeInvite(createDate);
    }

    /**
     * 创建邀约任务
     */
    @XxlJob("createInviteTask1")
    public void inviteAutoCreate1(){
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        inviteVehicleTaskService.inviteAutoCreateTask1(createDate);
    }

    /**
     * 创建邀约任务
     */
    @XxlJob("createInviteTask2")
    public void inviteAutoCreate2(){
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        inviteVehicleDoTaskService.inviteAutoCreateTask(createDate, null, null);
    }

    /**
     * 计算voc日均行驶里程
     */
    @XxlJob("computeDailyAverageMileageForVoc")
    public void computeDailyAverageMileageForVoc(){
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        Integer page = this.getParamInt(jsonObject, PAGE);
        Integer number = this.getParamInt(jsonObject, NUMBER);
        Integer partitionSize = this.getParamInt(jsonObject, PARTITION_SIZE);
        vocManageService.partitionGetAllVocVeh(createDate, page, number,partitionSize);
    }

    /**
     * 计算非VOC日均行驶里程
     */
    @XxlJob("computeDailyAverageMileage")
    public void computeDailyAverageMileage(){
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        Integer page = this.getParamInt(jsonObject, PAGE);
        Integer number = this.getParamInt(jsonObject, NUMBER);
        Integer partitionSize = this.getParamInt(jsonObject, PARTITION_SIZE);
        inviteVehicleTaskService.partitionGetNotVocVeh(createDate, page, number, partitionSize);
    }

    /**
     * 计算voc日均行驶里程
     */
    @XxlJob("computeDailyAverageMileageForVocOld")
    public void computeDailyAverageMileageForVocOld(){
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        Integer page = this.getParamInt(jsonObject, PAGE);
        Integer number = this.getParamInt(jsonObject, NUMBER);
        Integer partitionSize = this.getParamInt(jsonObject, PARTITION_SIZE);
        vocManageService.partitionGetAllVocVehOld(createDate, page, number,partitionSize);
    }

    /**
     * 计算非VOC日均行驶里程
     */
    @XxlJob("computeDailyAverageMileageOld")
    public void computeDailyAverageMileageOld(){
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        Integer page = this.getParamInt(jsonObject, PAGE);
        Integer number = this.getParamInt(jsonObject, NUMBER);
        Integer partitionSize = this.getParamInt(jsonObject, PARTITION_SIZE);
        inviteVehicleTaskService.partitionComputeDailyAverageMileage(createDate, page, number, partitionSize);
    }

    /**
     * 根据邀约任务创建邀约线索
     */
    @XxlJob("createInviteByTask")
    public void createInviteByTask(){
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        inviteVehicleTaskService.createInviteByTask(createDate);
    }



    /**
     * 查询前一天voc数据
     */
    @XxlJob("updateVoc")
    public void updateVoc(){
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        vocManageService.updateVoc(createDate);
    }

    /**
     * 定保 保险丝规则处理
     * @return
     */
    @GetMapping("/MaintainFuseRule")
    public void MaintainFuseRule(){
        inviteVehicleTaskService.fuseRule();
    }


    /**
     * 跟据规则改变更新易损件邀约任务和线索
     * @param createDate
     * @return
     */
    @GetMapping("/updateInviteVulnerable")
    public void updateInviteVulnerable(@RequestParam(value = CREATE_DATE, required = false) String createDate) {
        inviteVehicleTaskService.updateInviteVulnerable(createDate);
    }


    /**
     * 跟据规则改变更新首保邀约任务和线索
     */
    @XxlJob("updateInviteFristMaintainByRuleChanged")
    public void updateInviteFristMaintainByRuleChanged() {
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        inviteVehicleTaskService.updateInviteFristMaintainByRuleChanged(createDate);
    }

    /**
     * 跟据规则改变更新定保邀约任务和线索
     */
    @XxlJob("updateInviteMaintainByRuleChanged")
    public void updateInviteMaintainByRuleChanged() {
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        inviteVehicleTaskService.updateInviteMaintainByRuleChanged(createDate);
    }

    /**
     * 跟据规则改变更新客户流失邀约任务和线索
     */
    @XxlJob("updateInviteCustomerLossByRuleChanged")
    public void updateInviteCustomerLossByRuleChanged() {
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        inviteVehicleTaskService.updateInviteCustomerLossByRuleChanged(createDate);
    }

    /**
     * 跟据规则改变更新保修邀约任务和线索
     */
    @XxlJob("updateInviteGuaranteeByRuleChanged")
    public void updateInviteGuaranteeByRuleChanged() {
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        inviteVehicleTaskService.updateInviteGuaranteeByRuleChanged(createDate);
    }


    /**
     * 每月月初生成上月二次跟进数据
     */
    @XxlJob("createTwiceFollow")
    public void createTwiceFollow() {
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        inviteVehicleTaskService.createTwiceFollow(createDate);
    }

    /**
     * 根据续保邀约任务创建邀约线索
     *
     */
    @XxlJob("createInviteInsuranceByTask")
    public void createInviteInsuranceByTask(){
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        String ownerCode = this.getParamStr(jsonObject, OWNER_CODE);
        String vin = this.getParamStr(jsonObject, VIN);
        inviteInsuranceVehicleTaskService.createInviteInsuranceByTask(ownerCode, vin, createDate);
    }

    /**
     * 创建新车的续保任务
     */
    @XxlJob("createNewVehicleInsuranceTask")
    public void insuranceTaskAutoCreate(){
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        inviteInsuranceVehicleTaskService.insuranceTaskAutoCreate(createDate);
    }

    /**
     * 保险跟进失败 -- 计划任务
     *
     * <AUTHOR>
     * @since 2020-08-31
     */
    @XxlJob("updateInsureFollowStatus")
    public void updateInsureFollowStatus() {
        inviteVehicleRecordService.updateInsureFollowStatus();
    }

    /**
     * T+2 获取oss保养灯信息文件
     */
    @XxlJob("getRun")
    public void getRun(){
        ossBusProService.run(null);
    }

    /**
     * 定时任务生成流失客户线索
     */
    @XxlJob("checkVocAlert")
    public void checkVocAlert(){
        ossService.checkVocAlert();
    }

    /**
     * 故障灯-关联工单
     */
    @XxlJob("faultLightOrderCorrelation")
    public void faultLightOrderCorrelation(){
        faultLightService.faultLightOrderCorrelation();
    }

    /**
     * 故障灯-关联工单 4.0 400代客预约
     */
    @XxlJob("faultLightOrderCorrelationV4")
    public void faultLightOrderCorrelationV4(){
        CompletableFuture.runAsync(() -> faultLightService.faultLightOrderCorrelationV4());
        CompletableFuture.runAsync(() -> faultLightService.faultLightOrderCorrelationV5());
    }

    /**
     * 故障灯-关联工单 4.0 400代客预约 预约单取消时关联线索关闭
     */
    @XxlJob("closeClueOnCancelledBookingOrder")
    public void closeClueOnCancelledBookingOrder(){
        faultLightService.closeClueOnCancelledBookingOrder();
    }

    /**
     * 故障灯-48H待验证线索
     */
    @XxlJob("doPendingVerificationClue")
    public void doPendingVerificationClue(){
        faultLightService.doPendingVerificationClue();
    }

    /**
     * 保养灯-通过工单完成线索,接口调试用
     */
    @XxlJob("doLeadCompletionByWorkOrder")
    public void checkLeadCompletionByWorkOrder(){
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        inviteVehicleRecordService.querySettledMaintenanceByTime(createDate);
    }

    /**
     * 保养灯-逾期接口关闭,接口调试
     */
    @XxlJob("doTerminateOverdueLeads")
    public void terminateOverdueLeads() {
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        inviteVehicleRecordService.closeOverdueLeads(createDate);
    }

    /**
     * 保养灯-通过保养灯信息判断线索验证状态
     */
    @XxlJob("doValidateMaintenanceLight")
    public void validateMaintenanceLight() {inviteVehicleRecordService.isValidationSuccessful();}

    /**
     * 保养灯-写入CDP标签同步任务数据
     */
    @XxlJob("doWritesCdpTagTask")
    public void writesCdpTagTask() {
        XxlJobHelper.log("-------------writesCdpTagTask start------------");
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        String endDate = this.getParamStr(jsonObject, END_DATE);
        cdpTagTaskService.writesCdpTagTask(createDate,endDate);
        XxlJobHelper.log("-------------writesCdpTagTask end------------");
    }

    /**
     * 保养灯-更新CDP标签数据
     */
    @XxlJob("doDisposeCdpTagTask")
    public void disposeCdpTagTask() {
        XxlJobHelper.log("-------------disposeCdpTagTask start------------");
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        String endDate = this.getParamStr(jsonObject, END_DATE);
        cdpTagTaskService.disposeCdpTagTask(createDate,endDate);
        XxlJobHelper.log("-------------disposeCdpTagTask end------------");
    }

    /**
     * 故障灯-线索自动进入待验证
     */
    @XxlJob("faultLightStatusChange")
    public void faultLightStatusChange() {faultLightService.faultLightStatusChange();}

    /**
     * 故障灯-更新高亮
     */
    @XxlJob("updateHighlightFlag")
    public void updateHighlightFlag(){
        faultLightClueService.updateHighlightFlag();
    }
    /**
     * 故障灯-获取高亮
     */
    @XxlJob("queryHighlightFlag")
    public void queryHighlightFlag(){
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        String createDate = this.getParamStr(jsonObject, CREATE_DATE);
        String endDate = this.getParamStr(jsonObject, END_DATE);
        faultLightClueService.queryHighlightFlag(createDate,endDate);
    }
    /**
     * CDP更新返厂意向等级
     */
    @XxlJob("cdpUpdateReturnIntentionLevel")
    public void cdpUpdateReturnIntentionLevel() {
        XxlJobHelper.log("-------------cdpUpdateReturnIntentionLevel start------------");
        inviteVehicleRecordService.cdpUpdateReturnIntentionLevel();
        XxlJobHelper.log("-------------cdpUpdateReturnIntentionLevel start------------");
    }

    private String getParamStr(JSONObject jsonObject,String key){
        return Objects.nonNull(jsonObject) ? jsonObject.getString(key) : null;
    }
    private Integer getParamInt(JSONObject jsonObject,String key){
        return Objects.nonNull(jsonObject) ? jsonObject.getInteger(key) : null;
    }

    @XxlJob("doAccidentClueFollowRemindJob")
    public void doAccidentClueFollowRemindJob(){
        log.info("跟进提醒定时任务开始执行");
        accidentCluesService.followRemind();
        log.info("跟进提醒定时任务执行结束");
    }

    @XxlJob("doAccidentClueAppointmentTimeOutRemind")
    public void doAccidentClueAppointmentTimeOutRemind(){
        accidentCluesService.appointmentTimeOutRemind();
    }

    @XxlJob("doUpdateExpireAccidentCluesJob")
    public void doUpdateExpireAccidentClues(){
        accidentCluesService.updateTimeOutClues();
    }

    @XxlJob("doAccidentCluePushCompensate")
    public void doAccidentCluePushCompensate(){
        log.info("事故线索推送LiteCrm补偿job开始执行");
        compensateService.accidentCluePushCompensate();
        log.info("事故线索推送LiteCrm补偿job结束执行");
    }

    @XxlJob("doCleanDealerToDealerTask")
    public void doCleanDealerToDealerTask(){
        log.info("经销商代码数据清洗job开始执行");
        clueMigrateTaskService.cleanDealerToDealerTask();
        log.info("经销商代码数据清洗job结束执行");
    }

    @XxlJob("markIsDisplayDiagnosticInfo")
    public void markIsDisplayDiagnosticInfo(){
        log.info("标记是否展示诊断信息job开始执行");
        faultLightService.handleDimClues(1000);
        log.info("标记是否展示诊断信息job结束执行");
    }

    @XxlJob("regionInformationRefresh")
    public void regionInformationRefresh(){
        log.info("区域数据刷新job开始执行");
        commonMathService.regionInformationRefresh(null);
        log.info("区域数据刷新job结束执行");
    }

    @XxlJob("updateForecastTime")
    public void updateForecastTime(){
        log.info("区域数据刷新job开始执行");
        faultLightService.updateForecastTime();
        log.info("区域数据刷新job结束执行");
    }


}
