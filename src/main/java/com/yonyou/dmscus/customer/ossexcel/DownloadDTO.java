//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.yonyou.dmscus.customer.ossexcel;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import com.yonyou.dmscloud.framework.service.excel.ExcelExportColumn;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

public class DownloadDTO extends BaseDTO {
    @ApiModelProperty("回调的url")
    private String serviceUrl;
    @ApiModelProperty("查询条件")
    private Map<String, Object> queryParams;
    @ApiModelProperty("表头信息")
    private List<ExcelExportColumn> excelExportColumnList;
    @ApiModelProperty("excel命名")
    private String excelName;
    @ApiModelProperty("sheet命名")
    private String sheetName;
    @ApiModelProperty("id列属性名")
    private String fieldId;

    public DownloadDTO() {
    }

    public String getServiceUrl() {
        return this.serviceUrl;
    }

    public void setServiceUrl(String serviceUrl) {
        this.serviceUrl = serviceUrl;
    }

    public Map<String, Object> getQueryParams() {
        return this.queryParams;
    }

    public void setQueryParams(Map<String, Object> queryParams) {
        this.queryParams = queryParams;
    }

    public List<ExcelExportColumn> getExcelExportColumnList() {
        return this.excelExportColumnList;
    }

    public void setExcelExportColumnList(List<ExcelExportColumn> excelExportColumnList) {
        this.excelExportColumnList = excelExportColumnList;
    }

    public String getExcelName() {
        return this.excelName;
    }

    public void setExcelName(String excelName) {
        this.excelName = excelName;
    }

    public String getSheetName() {
        return this.sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public String getFieldId() {
        return this.fieldId;
    }

    public void setFieldId(String fieldId) {
        this.fieldId = fieldId;
    }
}
