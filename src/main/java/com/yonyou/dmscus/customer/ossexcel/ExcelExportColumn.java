//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.yonyou.dmscus.customer.ossexcel;

public class ExcelExportColumn {
    private String fieldName;
    private String title;
    private String format;
    private ExcelDataType dataType;
    private String exportFormat;
    private boolean cellLock;
    private boolean hiddenColumn;
    private boolean whetherNumerial;

    public ExcelExportColumn() {
    }

    public ExcelExportColumn(String fieldName, String title) {
        this.fieldName = fieldName;
        this.title = title;
    }

    public ExcelExportColumn(String fieldName, String title, String format) {
        this.fieldName = fieldName;
        this.title = title;
        this.format = format;
    }

    public ExcelExportColumn(String fieldName, String title, ExcelDataType dataType) {
        this.fieldName = fieldName;
        this.title = title;
        this.dataType = dataType;
    }

    public String getExportFormat() {
        return this.exportFormat;
    }

    public void setExportFormat(String exportFormat) {
        this.exportFormat = exportFormat;
    }

    public String getFieldName() {
        return this.fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getFormat() {
        return this.format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public ExcelDataType getDataType() {
        return this.dataType;
    }

    public void setDataType(ExcelDataType dataType) {
        this.dataType = dataType;
    }

    public boolean isHiddenColumn() {
        return this.hiddenColumn;
    }

    public void setHiddenColumn(boolean hiddenColumn) {
        this.hiddenColumn = hiddenColumn;
    }

    public boolean isCellLock() {
        return this.cellLock;
    }

    public void setCellLock(boolean cellLock) {
        this.cellLock = cellLock;
    }

    public boolean isWhetherNumerial() {
        return this.whetherNumerial;
    }

    public void setWhetherNumerial(boolean whetherNumerial) {
        this.whetherNumerial = whetherNumerial;
    }
}
