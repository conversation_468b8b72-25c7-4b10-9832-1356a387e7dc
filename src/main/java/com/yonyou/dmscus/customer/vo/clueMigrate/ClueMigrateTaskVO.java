package com.yonyou.dmscus.customer.vo.clueMigrate;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.dmscloud.framework.util.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel
@Data
public class ClueMigrateTaskVO implements java.io.Serializable {

    private Integer id;

    @ApiModelProperty( "同步类型 1 店店迁移 2 车店迁移" )
    private Integer syncType;

    private String syncTypeName;

    @ApiModelProperty( "原经销商代码" )
    private String sourceOwnerCode;

    private String sourceOwnerName;

    @ApiModelProperty( "经销商代码" )
    private String ownerCode;

    private String ownerName;

    @ApiModelProperty( "vin" )
    private String vin;

    @ApiModelProperty( "同步状态 0 初始等待 1 完成 2 失败" )
    private Integer syncStatus;

    private String syncStatusName;

    @ApiModelProperty( "开始时间" )
    private LocalDateTime startTime;

    private String startTimeStr;

    @ApiModelProperty( "结束时间" )
    private LocalDateTime finishTime;

    private String finishTimeStr;

    @ApiModelProperty( "备注" )
    private String remark;

    @ApiModelProperty( "操作人" )
    private String operationPerson;

    private String ownerParCode;
    @ApiModelProperty( "操作人" )
    private String createdBy;

    @ApiModelProperty( "是否迁移CDP(0: 不迁移, 1: 迁移)" )
    private Integer migratedCdp;
    private String migratedCdpString;
}
