package com.yonyou.dmscloud.framework.util;

import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class FrameworkUtilTest {

    @Test
    void testGetLoginInfo() {
        // Setup
        // Run the test
        final LoginInfoDto result = FrameworkUtil.getLoginInfo();

        // Verify the results
    }

    @Test
    void testIsCanAccess() {
        assertThat(FrameworkUtil.isCanAccess("requestUrl", "actionUrl")).isFalse();
    }

}
