package com.yonyou.dmscloud.framework.interceptors;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

class ExceptionControllerAdviceHandelTest {

    private ExceptionControllerAdviceHandel exceptionControllerAdviceHandelUnderTest;

    @BeforeEach
    void setUp() {
        exceptionControllerAdviceHandelUnderTest = new ExceptionControllerAdviceHandel();
    }

    @Test
    void testGetDefaultExceptionResponse() {
        // Setup
        final MockHttpServletRequest req = new MockHttpServletRequest();
        final MockHttpServletResponse resp = new MockHttpServletResponse();

        // Run the test
        final ResponseEntity result = exceptionControllerAdviceHandelUnderTest.getDefaultExceptionResponse(req, resp,
                new Exception("message"));

        // Verify the results
    }
}
