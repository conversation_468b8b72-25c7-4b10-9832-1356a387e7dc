package com.yonyou.dmscloud.framework.interceptors;

import com.yonyou.dmscloud.framework.util.redis.RedisClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DmsFrameworkInterceptorTest {

    @Mock
    private RedisClient mockRedisClient;

    private DmsFrameworkInterceptor dmsFrameworkInterceptorUnderTest;

    @BeforeEach
    void setUp() {
        dmsFrameworkInterceptorUnderTest = new DmsFrameworkInterceptor(mockRedisClient);
    }

    @Test
    void testPreHandle() throws Exception {
        // Setup
        final MockHttpServletRequest httpServletRequest = new MockHttpServletRequest();
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockRedisClient.hget("cyxdms:user:login:10041001", "field")).thenReturn("result");
        when(mockRedisClient.get("key")).thenReturn("result");

        // Run the test
        final boolean result = dmsFrameworkInterceptorUnderTest.preHandle(httpServletRequest, response, "handler");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockRedisClient).expires("key", 43200000L);
    }
}
