package com.yonyou.dmscus.customer.util;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class SensitiveUtilsTest {

    @Test
    void testAllReplace() {
        assertThat(SensitiveUtils.allReplace("str")).isEqualTo("result");
    }

    @Test
    void testIdCard() {
        assertThat(SensitiveUtils.idCard("str")).isEqualTo("");
    }

    @Test
    void testReplace() {
        assertThat(SensitiveUtils.replace("str1", "str2")).isEqualTo("result");
    }

    @Test
    void testEmail() {
        assertThat(SensitiveUtils.email("email")).isEqualTo("result");
    }

    @Test
    void testMobile() {
        assertThat(SensitiveUtils.mobile("mobile")).isEqualTo("result");
    }

    @Test
    void testMobileEncrypt() {
        assertThat(SensitiveUtils.mobileEncrypt("mobile")).isEqualTo("mobile");
    }
}
