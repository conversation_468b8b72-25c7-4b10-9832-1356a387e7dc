package com.yonyou.dmscus.customer.util.common;

import com.yonyou.dmscus.customer.service.common.HttpLogService;
import org.jsoup.nodes.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class HttpClientTest {

    private HttpClient httpClientUnderTest;

    @BeforeEach
    void setUp() {
        httpClientUnderTest = new HttpClient();
    }

    @Test
    void testSetHttpLogService() {
        final HttpLogService httpLogService = null;
        httpClientUnderTest.setHttpLogService(httpLogService);
    }
}
