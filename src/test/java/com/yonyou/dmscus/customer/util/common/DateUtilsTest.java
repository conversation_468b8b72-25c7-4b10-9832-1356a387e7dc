package com.yonyou.dmscus.customer.util.common;

import org.junit.jupiter.api.Test;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import static org.assertj.core.api.Assertions.*;

class DateUtilsTest {

    @Test
    void testFormateDateToString() {
        assertThat(DateUtils.formateDateToString(new Date(),
                "yyyy-MM-dd HH:mm:s")).isEqualTo("result");
    }

    @Test
    void testParseDateStrToDate() {
        assertThat(DateUtils.parseDateStrToDate("2024-06-14 12:11:11", "yyyy-MM-dd HH:mm:s"))
                .isEqualTo(new Date());
    }

    @Test
    void testParse() throws Exception {
        DateUtils.parse("2024-06-14 12:11:11", "yyyy-MM-dd HH:mm:s");
//        assertThat(DateUtils.parse("2024-06-14 12:11:11", "yyyy-MM-dd HH:mm:s"))
//                .isEqualTo(new Date());
//        assertThatThrownBy(() -> DateUtils.parse("2024-06-14 12:11:11", "yyyy-MM-dd HH:mm:s")).isInstanceOf(Exception.class);
    }

    @Test
    void testGetBeforeDateStr() {
        assertThat(DateUtils.getBeforeDateStr(new Date(), 0))
                .isEqualTo("result");
    }

    @Test
    void testGetBeforeDate() {
        assertThat(DateUtils.getBeforeDate(new Date(), 0))
                .isEqualTo(new Date());
    }

    @Test
    void testGetAfterDateStr() {
        assertThat(DateUtils.getAfterDateStr(new Date(), 0))
                .isEqualTo("result");
    }

    @Test
    void testGetAfterDate() {
        assertThat(DateUtils.getAfterDate(new Date(), 0))
                .isEqualTo(new Date());
    }

    @Test
    void testGetAfterYearDate() {
        assertThat(
                DateUtils.getAfterYearDate(new Date(), 2020))
                .isEqualTo(new Date());
    }

    @Test
    void testGetAfterDateMinute() {
        assertThat(
                DateUtils.getAfterDateMinute(new Date(), 0))
                .isEqualTo("result");
    }

    @Test
    void testDaysDateBetween() {
        assertThat(DateUtils.daysDateBetween(new Date(),
                new Date())).isEqualTo(0);
    }

    @Test
    void testFormatDate() {
        assertThat(
                DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"))
                .isEqualTo("result");
    }

    @Test
    void testDaysStringBetween() {
        assertThat(DateUtils.daysStringBetween("2024-06-14", "2024-06-14")).isEqualTo(0);
    }

    @Test
    void testGetDatePoor() throws Exception {
        DateUtils.getDatePoor(new Date(),
                new Date());
//        assertThat(DateUtils.getDatePoor(new Date(),
//                new Date())).isEqualTo(0.0, within(0.0001));
//        assertThatThrownBy(() -> DateUtils.getDatePoor(new Date(),
//                new Date())).isInstanceOf(ParseException.class);
    }

    @Test
    void testIsEqualsDate() {
        assertThat(DateUtils.isEqualsDate(new Date(),
                new Date())).isFalse();
    }

    @Test
    void testIsEqualsTheSameDay() {
        assertThat(DateUtils.isEqualsTheSameDay(new Date())).isFalse();
    }

    @Test
    void testDateTimeFormatter() {
        assertThat(DateUtils.dateTimeFormatter(System.currentTimeMillis(), "pattern"))
                .isEqualTo(new Date());
    }

    @Test
    void testTimeStamp2Date() {
        assertThat(DateUtils.timeStamp2Date(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:s"))
                .isEqualTo(new Date());
    }

    @Test
    void testDateToString() {
        assertThat(
                DateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:s"))
                .isEqualTo("result");
    }

    @Test
    void testComto1() {
        assertThat(DateUtils.comto(new Date())).isFalse();
    }

    @Test
    void testComDayto() {
        assertThat(DateUtils.comDayto(new Date())).isFalse();
    }

    @Test
    void testComMothtoBetween() {
        assertThat(DateUtils.comMothtoBetween(new Date())).isFalse();
    }

    @Test
    void testGetBeforeDateStrParm() {
        assertThat(DateUtils.getBeforeDateStrParm(new Date(), 0,
                "yyyy-MM-dd HH:mm:s")).isEqualTo("result");
    }

    @Test
    void testCompareTo() {
        assertThat(DateUtils.compareTo("2024-06-14 12:44:11", "2024-06-14 12:44:11", "yyyy-MM-dd HH:mm:s")).isFalse();
    }

    @Test
    void testCheck18() throws Exception {
        assertThat(DateUtils.check18(new Date(), "2024-06-14")).isFalse();
        assertThatThrownBy(() -> DateUtils.check18(new Date(),
                "datatime")).isInstanceOf(ParseException.class);
    }

    @Test
    void testGetBeforemonthDate() {
        assertThat(
                DateUtils.getBeforemonthDate(new Date(), 1))
                .isEqualTo(new Date());
    }

    @Test
    void testGetDate() {
        assertThat(DateUtils.getDate()).isEqualTo(new Date());
    }

    @Test
    void testParseDate1() {
        DateUtils.parseDate(new Date(), "yyyy-MM-dd HH:mm:s");
    }

    @Test
    void testComto2() {
        assertThat(DateUtils.comto(new Date(), 0)).isFalse();
    }

    @Test
    void testGetHourAfter() {
        assertThat(DateUtils.getHourAfter(new Date(), 0))
                .isEqualTo("result");
    }

    @Test
    void testGetDateHourAfter() {
        assertThat(DateUtils.getDateHourAfter(new Date(), 0))
                .isEqualTo(new Date());
    }

    @Test
    void testGetDateAfter() {
        assertThat(DateUtils.getDateAfter(new Date(),
                new Date())).isEqualTo(0L);
    }

    @Test
    void testDateCompare() throws Exception {
        assertThat(DateUtils.dateCompare("2024-06-14 12:44:11", "2024-06-14 12:44:11")).isEqualTo(0);
        assertThatThrownBy(() -> DateUtils.dateCompare("2024-06-14 12:44:11", "2024-06-14 12:44:11")).isInstanceOf(ParseException.class);
    }

    @Test
    void testTwentyFour1() {
        assertThat(DateUtils.twentyFour(new Date())).isEqualTo(0);
    }

    @Test
    void testTwentyFour2() {
        assertThat(
                DateUtils.twentyFour(new Date(), "yyyy-MM-dd HH:mm:s"))
                .isEqualTo(0);
    }

    @Test
    void testParseDate2() {
        DateUtils.parseDate(new Date(), 1);
//        assertThat()
//                .isEqualTo("result");
    }

    @Test
    void testParseDate3() {
//        assertThat(
//                )
//                .isEqualTo(new Date());
        DateUtils.parseDate(new Date(), 1, "yyyy-MM-dd HH:mm:s");
    }

    @Test
    void testDateFormat() {
        assertThat(
                DateUtils.dateFormat(new Date(), "yyyy-MM-dd HH:mm:s"))
                .isEqualTo("");
    }

    @Test
    void testParseDate4() {
//        assertThat(DateUtils.parseDate("2024-06-14")).isEqualTo("result");
        DateUtils.parseDate("2024-06-14");
    }

    @Test
    void testMain() {
        // Setup
        // Run the test
        DateUtils.main(new String[]{"args"});

        // Verify the results
    }

    @Test
    void testLastDayOfYear() {
        assertThat(DateUtils.lastDayOfYear(new Date()))
                .isEqualTo(new Date());
    }

    @Test
    void testFirstDayOfYear() {
        assertThat(DateUtils.firstDayOfYear(new Date()))
                .isEqualTo(new Date());
    }

    @Test
    void testQueryNow() {
        assertThat(DateUtils.queryNow()).isEqualTo("result");
    }

    @Test
    void testQueryBeginTime() {
        assertThat(DateUtils.queryBeginTime("2024-06-14")).isEqualTo("result");
    }

    @Test
    void testQueryEndTime() {
        assertThat(DateUtils.queryEndTime("2024-06-14")).isEqualTo("2024-06-14");
    }
}
