/*
 * Copyright (c) Volvo CAR Distribution (SHANGHAI) Co., Ltd. 2023. All rights reserved.
 */

package com.yonyou.dmscus.customer.utils.utExampled;

import org.junit.jupiter.api.Test;
import org.reflections.Reflections;
import org.reflections.scanners.SubTypesScanner;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


class SupperObjTest {

    private static final Logger log = LoggerFactory.getLogger(SupperObjTest.class);

    @Test
    public void testAllClasses() {
        // 使用 Reflections 配置，扫描当前目录及其子目录
        Reflections reflections = new Reflections(
                new ConfigurationBuilder()
                        .setUrls(ClasspathHelper.forPackage("com.yonyou")) // 空字符串表示扫描所有包
                        .setScanners(new SubTypesScanner(false)) // 扫描子类和接口
                        .filterInputsBy(input -> input != null
                                && input.endsWith(".class")
//                                && input.endsWith("AccidentInfo")
                                && (input.contains("Po") || (input.contains("PO") || input.contains("Vo") || input.contains("VO") || input.contains("Dto") || input.contains("DTO") || input.contains("Entity") || input.contains("ENTITY")))
                                && !input.contains("Test")) // 过滤掉非类文件
        );

        // 扫描所有的类
        Set<Class<? extends Object>> basePoClass = reflections.getSubTypesOf(Object.class);
        Set<Class<? extends Serializable>> serializableClass = reflections.getSubTypesOf(Serializable.class);


        Set<Class<?>> allClasses = reflections.getSubTypesOf(Object.class);
        allClasses.addAll(basePoClass);
        allClasses.addAll(serializableClass);
        callMethod(allClasses);

        // 扫描所有的接口
        Set<Class<?>> allInterfaces = reflections.getSubTypesOf(Object.class).stream()
                .filter(Class::isInterface)
                .collect(Collectors.toSet());
        for (Class<?> iface : allInterfaces) {
            System.out.println("Interface: " + iface.getName());
            // 这里可以添加针对每个接口的具体测试逻辑
        }
    }

    private static void callMethod(Set<Class<?>> allClasses) {
        for (Class<?> clazz : allClasses) {
            System.out.println("Class: " + clazz.getName());
            // 这里可以添加针对每个类的具体测试逻辑
            try {
                List<Object> obj1 = createObj(clazz);
                List<Object> obj2 = createObj(clazz);
                callMethod(clazz, obj1.get(0), obj2.get(0));
                //build注解
                Method builder1 = clazz.getMethod("builder");
                if(Objects.nonNull(builder1)){
                    Object buildObj1 = builder1.invoke(obj1);
                    Object buildObj2 = builder1.invoke(obj2);
                    callMethod(buildObj1.getClass(), buildObj1, buildObj2);
                    Method buildMethod = buildObj1.getClass().getMethod("build");
                    buildMethod.setAccessible(true);
                    buildMethod.invoke(buildObj1);
                    buildMethod.invoke(buildObj2);
                }
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
    }

    private static void callMethod(Class<?> clazz, Object o1, Object o2) {
        o1.toString();
        o1.hashCode();
        BeanUtils.copyProperties(o1, o2);
        o1.equals(o2);
        try {
            Method canEqual = clazz.getMethod("canEqual");
            canEqual.setAccessible(true);
            canEqual.invoke(o1, o2);
        } catch (Exception e) {
            log.error("method invoke error", e.getMessage());
        }


        Method[] declaredMethods = clazz.getDeclaredMethods();
        Arrays.stream(declaredMethods).forEach(method -> {
            int parameterCount = method.getParameterCount();
            if(parameterCount == 0){
                try{
                    method.setAccessible(true);
                    method.invoke(o1);
                    log.info("method invoke success"+method.getName());
                }catch (Exception e) {
                    log.error("method invoke error",method.getName()+ e.getMessage());
                }
            }else{
                Object[] args = new Object[parameterCount];
                IntStream.range(0,parameterCount).forEach(index -> args[index] = null );
                try {
                    method.setAccessible(true);
                    method.invoke(o1,args);
                    log.info("method invoke success"+method.getName());
                } catch (Exception e) {
                    log.error("method invoke error", method.getName()+e.getMessage());
                }
            }
        });
    }

    private static List<Object> createObj(Class<?> clazz) {
        Constructor<?>[] constructors = clazz.getConstructors();
        return Arrays.stream(constructors).map(c -> {
            int parameterCount = c.getParameterCount();
            if (parameterCount == 0) {
                try {
                    c.setAccessible(true);
                    return c.newInstance();
                } catch (Exception e) {
                    log.error("method invoke error", e);
                }
            } else {
                Object[] args = new Object[parameterCount];
                IntStream.range(0, parameterCount).forEach(index -> args[index] = null);
                try {
                    c.setAccessible(true);
                    return c.newInstance(args);
                } catch (Exception e) {
                    log.error("method invoke error", e.getMessage());
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
