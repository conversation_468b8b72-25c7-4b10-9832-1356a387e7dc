package com.yonyou.dmscus.customer.utils;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;

class DataConvertUtilsTest {

    @Test
    void testIntegerListToString() {
        DataConvertUtils.integerListToString(null);
        DataConvertUtils.stringToIntegerList(null);
        List<Integer> list = new ArrayList<>();
        list.add(11111);
        list.add(22222);
        String integerListToString = DataConvertUtils.integerListToString(list);
        DataConvertUtils.stringToIntegerList(integerListToString);
    }
    
    @Test
    void testStringListToString() {
        DataConvertUtils.stringListToString(null);
        DataConvertUtils.stringToStringList(null);
        List<String> list = new ArrayList<>();
        list.add("111111");
        list.add("222222");
        String stringListToString = DataConvertUtils.stringListToString(list);
        DataConvertUtils.stringToStringList(stringListToString);
    }
}
