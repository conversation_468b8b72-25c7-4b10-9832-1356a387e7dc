package com.yonyou.dmscus.customer.utils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class UtillsTest {

    private Utills utillsUnderTest;

    @BeforeEach
    void setUp() {
        utillsUnderTest = new Utills();
    }

    @Test
    void testStrToLong() {
        // Setup
        // Run the test
        final List result = utillsUnderTest.strToLong("ids");

        // Verify the results
    }

    @Test
    void testGetList() {
        assertThat(Utills.getList("s")).isEqualTo(Arrays.asList("value"));
        assertThat(Utills.getList("s")).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetThreadSize() {
        assertThat(Utills.getThreadSize(0)).isEqualTo(0);
    }
}
