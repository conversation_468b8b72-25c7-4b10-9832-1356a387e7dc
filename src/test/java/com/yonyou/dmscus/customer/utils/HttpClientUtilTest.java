package com.yonyou.dmscus.customer.utils;

import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class HttpClientUtilTest {

    @Test
    void testInitPools() {
        // Setup
        // Run the test
        HttpClientUtil.initPools();

        // Verify the results
    }

    @Test
    void testDoPost() throws Exception {
        // Setup
        final Map<String, String> headers = new HashMap<>();

        // Run the test
        final String result = HttpClientUtil.doPost("url", "param", headers);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    void testDoPost_ThrowsIOException() {
        // Setup
        final Map<String, String> headers = new HashMap<>();

        // Run the test
        assertThatThrownBy(() -> HttpClientUtil.doPost("url", "param", headers)).isInstanceOf(IOException.class);
    }
    @Test
    void testDoPut_ThrowsIOException() {
        // Setup
        final Map<String, Object> params = new HashMap<>();
        final Map<String, String> headers = new HashMap<>();

        // Run the test
        assertThatThrownBy(() -> HttpClientUtil.doPut("url", params, headers)).isInstanceOf(IOException.class);
    }

    @Test
    void testDoGet_ThrowsIOException() {
        // Setup
        final Map<String, Object> params = new HashMap<>();
        final Map<String, String> headers = new HashMap<>();

        // Run the test
        assertThatThrownBy(() -> HttpClientUtil.doGet("url", params, headers)).isInstanceOf(IOException.class);
    }
}
