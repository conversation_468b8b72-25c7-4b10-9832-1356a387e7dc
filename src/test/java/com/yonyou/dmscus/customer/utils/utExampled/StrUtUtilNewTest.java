package com.yonyou.dmscus.customer.utils.utExampled;

import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.text.StrBuilder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.StringReader;
import java.io.StringWriter;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.within;

@ExtendWith(MockitoExtension.class)
class StrUtUtilNewTest {
    @Test
    void testAddPrefixIfNot() throws Exception {
        assertThat(UtBStrUtils.addPrefixIfNot("str", "prefix")).isEqualTo("result");
    }

    @Test
    void testAddSuffixIfNot() throws Exception {
        assertThat(UtBStrUtils.addSuffixIfNot("str", "suffix")).isEqualTo("result");
    }

    @Test
    void testAppendIfMissing1() throws Exception {
        assertThat(UtBStrUtils.appendIfMissing("str", "suffix", "suffixes")).isEqualTo("result");
    }

    @Test
    void testAppendIfMissing2() throws Exception {
        assertThat(UtBStrUtils.appendIfMissing("str", "suffix", false, "suffixes")).isEqualTo("result");
    }

    @Test
    void testAppendIfMissingIgnoreCase() throws Exception {
        assertThat(UtBStrUtils.appendIfMissingIgnoreCase("str", "suffix", "suffixes")).isEqualTo("result");
    }

    @Test
    void testBlankToDefault() throws Exception {
        assertThat(UtBStrUtils.blankToDefault("str", "defaultStr")).isEqualTo("result");
    }

    @Test
    void testBrief() throws Exception {
        assertThat(UtBStrUtils.brief("str", 0)).isEqualTo("result");
    }

    @Test
    void testBuilder1() throws Exception {
        assertThat(UtBStrUtils.builder()).isEqualTo(new StringBuilder());
    }

    @Test
    void testBuilder2() throws Exception {
        assertThat(UtBStrUtils.builder(0)).isEqualTo(new StringBuilder());
    }

    @Test
    void testBuilder3() throws Exception {
        assertThat(UtBStrUtils.builder("strs")).isEqualTo(new StringBuilder());
    }

    @Test
    void testByteBuffer() throws Exception {
        // Setup
        final ByteBuffer expectedResult = ByteBuffer.wrap("content".getBytes());

        // Run the test
        final ByteBuffer result = UtBStrUtils.byteBuffer("str", "UTF-8");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testByteLength() throws Exception {
        assertThat(UtBStrUtils.byteLength("cs", StandardCharsets.UTF_8)).isEqualTo(0);
    }

    @Test
    void testBytes1() throws Exception {
        assertThat(UtBStrUtils.bytes("str")).isEqualTo("content".getBytes());
    }

    @Test
    void testBytes2() throws Exception {
        assertThat(UtBStrUtils.bytes("str", "UTF-8")).isEqualTo("content".getBytes());
    }

    @Test
    void testBytes3() throws Exception {
        assertThat(UtBStrUtils.bytes("str", StandardCharsets.UTF_8)).isEqualTo("content".getBytes());
    }

    @Test
    void testCenter1() throws Exception {
        assertThat(UtBStrUtils.center("str", 0)).isEqualTo("result");
    }

    @Test
    void testCenter2() throws Exception {
        assertThat(UtBStrUtils.center("str", 0, 'a')).isEqualTo("result");
    }

    @Test
    void testCenter3() throws Exception {
        assertThat(UtBStrUtils.center("str", 0, "padStr")).isEqualTo("str");
    }

    @Test
    void testCleanBlank() throws Exception {
        assertThat(UtBStrUtils.cleanBlank("str")).isEqualTo("result");
    }

    @Test
    void testCompare() throws Exception {
        assertThat(UtBStrUtils.compare("str1", "str2", false)).isEqualTo(0);
    }

    @Test
    void testCompareIgnoreCase() throws Exception {
        assertThat(UtBStrUtils.compareIgnoreCase("str1", "str2", false)).isEqualTo(0);
    }

    @Test
    void testCompareVersion() throws Exception {
        assertThat(UtBStrUtils.compareVersion("version1", "version2")).isEqualTo(0);
    }

    @Test
    void testConcat() throws Exception {
        assertThat(UtBStrUtils.concat(false, "strs")).isEqualTo("result");
    }

    @Test
    void testContains1() throws Exception {
        assertThat(UtBStrUtils.contains("str", 'a')).isFalse();
    }

    @Test
    void testContains2() throws Exception {
        assertThat(UtBStrUtils.contains("str", "searchStr")).isFalse();
    }

    @Test
    void testContainsAny1() throws Exception {
        assertThat(UtBStrUtils.containsAny("str", "testStrs")).isFalse();
    }

    @Test
    void testContainsAny2() throws Exception {
        assertThat(UtBStrUtils.containsAny("str", 'a')).isFalse();
    }

    @Test
    void testContainsAnyIgnoreCase() throws Exception {
        assertThat(UtBStrUtils.containsAnyIgnoreCase("str", "testStrs")).isFalse();
    }

    @Test
    void testContainsBlank() throws Exception {
        assertThat(UtBStrUtils.containsBlank("str")).isFalse();
    }

    @Test
    void testContainsIgnoreCase() throws Exception {
        assertThat(UtBStrUtils.containsIgnoreCase("str", "testStr")).isFalse();
    }

    @Test
    void testContainsOnly() throws Exception {
        assertThat(UtBStrUtils.containsOnly("str", 'a')).isTrue();
    }

    @Test
    void testCount1() throws Exception {
        assertThat(UtBStrUtils.count("content", "strForSearch")).isEqualTo(0);
    }

    @Test
    void testCount2() throws Exception {
        assertThat(UtBStrUtils.count("content", 'a')).isEqualTo(0);
    }

    @Test
    void testEmptyIfNull() throws Exception {
        assertThat(UtBStrUtils.emptyIfNull("str")).isEqualTo("result");
    }

    @Test
    void testEmptyToDefault() throws Exception {
        assertThat(UtBStrUtils.emptyToDefault("str", "defaultStr")).isEqualTo("result");
    }

    @Test
    void testEmptyToNull() throws Exception {
        assertThat(UtBStrUtils.emptyToNull("str")).isEqualTo("result");
    }

    @Test
    void testEndWith1() throws Exception {
        assertThat(UtBStrUtils.endWith("str", 'a')).isFalse();
    }

    @Test
    void testEndWith2() throws Exception {
        assertThat(UtBStrUtils.endWith("str", "suffix", false)).isFalse();
    }

    @Test
    void testEndWith3() throws Exception {
        assertThat(UtBStrUtils.endWith("str", "suffix")).isFalse();
    }

    @Test
    void testEndWithAny() throws Exception {
        assertThat(UtBStrUtils.endWithAny("str", "suffixes")).isFalse();
    }

    @Test
    void testEndWithIgnoreCase() throws Exception {
        assertThat(UtBStrUtils.endWithIgnoreCase("str", "suffix")).isFalse();
    }

    @Test
    void testEquals1() throws Exception {
        assertThat(UtBStrUtils.equals("str1", "str2")).isFalse();
    }

    @Test
    void testEquals2() throws Exception {
        assertThat(UtBStrUtils.equals("str1", "str2", false)).isFalse();
    }

    @Test
    void testEqualsAny1() throws Exception {
        assertThat(UtBStrUtils.equalsAny("str1", "strs")).isFalse();
    }

    @Test
    void testEqualsAny2() throws Exception {
        assertThat(UtBStrUtils.equalsAny("str1", false, "strs")).isFalse();
    }

    @Test
    void testEqualsAnyIgnoreCase() throws Exception {
        assertThat(UtBStrUtils.equalsAnyIgnoreCase("str1", "strs")).isFalse();
    }

    @Test
    void testEqualsCharAt() throws Exception {
        assertThat(UtBStrUtils.equalsCharAt("str", 0, 'a')).isFalse();
    }

    @Test
    void testEqualsIgnoreCase() throws Exception {
        assertThat(UtBStrUtils.equalsIgnoreCase("str1", "str2")).isFalse();
    }

    @Test
    void testFill() throws Exception {
        assertThat(UtBStrUtils.fill("str", 'a', 0, false)).isEqualTo("str");
    }

    @Test
    void testFillAfter() throws Exception {
        assertThat(UtBStrUtils.fillAfter("str", 'a', 0)).isEqualTo("result");
    }

    @Test
    void testFillBefore() throws Exception {
        assertThat(UtBStrUtils.fillBefore("str", 'a', 0)).isEqualTo("result");
    }

    @Test
    void testFormat() throws Exception {
        assertThat(UtBStrUtils.format("template", "params")).isEqualTo("result");
    }

    @Test
    void testGenGetter() throws Exception {
        assertThat(UtBStrUtils.genGetter("fieldName")).isEqualTo("result");
    }

    @Test
    void testGenSetter() throws Exception {
        assertThat(UtBStrUtils.genSetter("fieldName")).isEqualTo("result");
    }

    @Test
    void testGetContainsStr() throws Exception {
        assertThat(UtBStrUtils.getContainsStr("str", "testStrs")).isNull();
    }

    @Test
    void testGetContainsStrIgnoreCase() throws Exception {
        assertThat(UtBStrUtils.getContainsStrIgnoreCase("str", "testStrs")).isNull();
    }

    @Test
    void testGetGeneralField() throws Exception {
        assertThat(UtBStrUtils.getGeneralField("getOrSetMethodName")).isEqualTo("result");
    }

    @Test
    void testGetReader() throws Exception {
        assertThat(UtBStrUtils.getReader("str")).isEqualTo(new StringReader("stringContent"));
    }

    @Test
    void testGetWriter() throws Exception {
        // Setup
        // Run the test
        final StringWriter result = UtBStrUtils.getWriter();

        // Verify the results
    }

    @Test
    void testHasBlank() throws Exception {
        assertThat(UtBStrUtils.hasBlank("strs")).isFalse();
    }

    @Test
    void testHasEmpty() throws Exception {
        assertThat(UtBStrUtils.hasEmpty("strs")).isFalse();
    }

    @Test
    void testHide() throws Exception {
        assertThat(UtBStrUtils.hide("str", 0, 0)).isEqualTo("result");
    }

    @Test
    void testIndexOf1() throws Exception {
        assertThat(UtBStrUtils.indexOf("str", 'a')).isEqualTo(0);
    }

    @Test
    void testIndexOf2() throws Exception {
        assertThat(UtBStrUtils.indexOf("str", 'a', 0)).isEqualTo(0);
    }

    @Test
    void testIndexOf3() throws Exception {
        assertThat(UtBStrUtils.indexOf("str", 'a', 0, 0)).isEqualTo(0);
    }

    @Test
    void testIndexOf4() throws Exception {
        assertThat(UtBStrUtils.indexOf("str", "searchStr", 0, false)).isEqualTo(0);
    }

    @Test
    void testIndexOfIgnoreCase1() throws Exception {
        assertThat(UtBStrUtils.indexOfIgnoreCase("str", "searchStr")).isEqualTo(0);
    }

    @Test
    void testIndexOfIgnoreCase2() throws Exception {
        assertThat(UtBStrUtils.indexOfIgnoreCase("str", "searchStr", 0)).isEqualTo(0);
    }

    @Test
    void testIndexedFormat() throws Exception {
        assertThat(UtBStrUtils.indexedFormat("pattern", "arguments")).isEqualTo("result");
    }

    @Test
    void testIsAllBlank() throws Exception {
        assertThat(UtBStrUtils.isAllBlank("strs")).isFalse();
    }

    @Test
    void testIsAllCharMatch() throws Exception {
        // Setup
        // Run the test
        final boolean result = UtBStrUtils.isAllCharMatch("value", null);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testIsAllEmpty() throws Exception {
        assertThat(UtBStrUtils.isAllEmpty("strs")).isTrue();
    }

    @Test
    void testIsAllNotBlank() throws Exception {
        assertThat(UtBStrUtils.isAllNotBlank("args")).isFalse();
    }

    @Test
    void testIsAllNotEmpty() throws Exception {
        assertThat(UtBStrUtils.isAllNotEmpty("args")).isFalse();
    }

    @Test
    void testIsBlank() throws Exception {
        assertThat(UtBStrUtils.isBlank("str")).isFalse();
    }

    @Test
    void testIsBlankIfStr() throws Exception {
        assertThat(UtBStrUtils.isBlankIfStr("obj")).isFalse();
    }

    @Test
    void testIsBlankOrUndefined() throws Exception {
        assertThat(UtBStrUtils.isBlankOrUndefined("str")).isFalse();
    }

    @Test
    void testIsEmpty() throws Exception {
        assertThat(UtBStrUtils.isEmpty("str")).isFalse();
    }

    @Test
    void testIsEmptyIfStr() throws Exception {
        assertThat(UtBStrUtils.isEmptyIfStr("obj")).isFalse();
    }

    @Test
    void testIsEmptyOrUndefined() throws Exception {
        assertThat(UtBStrUtils.isEmptyOrUndefined("str")).isFalse();
    }

    @Test
    void testIsLowerCase() throws Exception {
        assertThat(UtBStrUtils.isLowerCase("str")).isFalse();
    }

    @Test
    void testIsNotBlank() throws Exception {
        assertThat(UtBStrUtils.isNotBlank("str")).isFalse();
    }

    @Test
    void testIsNotEmpty() throws Exception {
        assertThat(UtBStrUtils.isNotEmpty("str")).isFalse();
    }

    @Test
    void testIsNullOrUndefined() throws Exception {
        assertThat(UtBStrUtils.isNullOrUndefined("str")).isFalse();
    }

    @Test
    void testIsSubEquals() throws Exception {
        assertThat(UtBStrUtils.isSubEquals("str1", 0, "str2", 0, 0, false)).isFalse();
    }

    @Test
    void testIsSurround1() throws Exception {
        assertThat(UtBStrUtils.isSurround("str", "prefix", "suffix")).isFalse();
    }

    @Test
    void testIsSurround2() throws Exception {
        assertThat(UtBStrUtils.isSurround("str", 'a', 'a')).isFalse();
    }

    @Test
    void testIsUpperCase() throws Exception {
        assertThat(UtBStrUtils.isUpperCase("str")).isFalse();
    }

    @Test
    void testIsWrap1() throws Exception {
        assertThat(UtBStrUtils.isWrap("str", "prefix", "suffix")).isFalse();
    }

    @Test
    void testIsWrap2() throws Exception {
        assertThat(UtBStrUtils.isWrap("str", "wrapper")).isFalse();
    }

    @Test
    void testIsWrap3() throws Exception {
        assertThat(UtBStrUtils.isWrap("str", 'a')).isFalse();
    }

    @Test
    void testIsWrap4() throws Exception {
        assertThat(UtBStrUtils.isWrap("str", 'a', 'a')).isFalse();
    }

    @Test
    void testJoin() throws Exception {
        assertThat(UtBStrUtils.join("conjunction", "objs")).isEqualTo("result");
    }

    @Test
    void testLastIndexOf() throws Exception {
        assertThat(UtBStrUtils.lastIndexOf("str", "searchStr", 0, false)).isEqualTo(0);
    }

    @Test
    void testLastIndexOfIgnoreCase1() throws Exception {
        assertThat(UtBStrUtils.lastIndexOfIgnoreCase("str", "searchStr")).isEqualTo(0);
    }

    @Test
    void testLastIndexOfIgnoreCase2() throws Exception {
        assertThat(UtBStrUtils.lastIndexOfIgnoreCase("str", "searchStr", 0)).isEqualTo(0);
    }

    @Test
    void testLength() throws Exception {
        assertThat(UtBStrUtils.length("cs")).isEqualTo(0);
    }

    @Test
    void testLowerFirst() throws Exception {
        assertThat(UtBStrUtils.lowerFirst("str")).isEqualTo("str");
    }

    @Test
    void testMaxLength() throws Exception {
        assertThat(UtBStrUtils.maxLength("string", 0)).isEqualTo("result");
    }

    @Test
    void testMove() throws Exception {
        assertThat(UtBStrUtils.move("str", 0, 0, 0)).isNull();
    }

    @Test
    void testNullToDefault() throws Exception {
        assertThat(UtBStrUtils.nullToDefault("str", "defaultStr")).isEqualTo("result");
    }

    @Test
    void testNullToEmpty() throws Exception {
        assertThat(UtBStrUtils.nullToEmpty("str")).isEqualTo("result");
    }

    @Test
    void testOrdinalIndexOf() throws Exception {
        assertThat(UtBStrUtils.ordinalIndexOf("str", "searchStr", 0)).isEqualTo(-1);
    }

    @Test
    void testPadAfter1() throws Exception {
        assertThat(UtBStrUtils.padAfter("str", 0, 'a')).isEqualTo("result");
    }

    @Test
    void testPadAfter2() throws Exception {
        assertThat(UtBStrUtils.padAfter("str", 0, "padStr")).isEqualTo("result");
    }

    @Test
    void testPadPre1() throws Exception {
        assertThat(UtBStrUtils.padPre("str", 0, "padStr")).isEqualTo("result");
    }

    @Test
    void testPadPre2() throws Exception {
        assertThat(UtBStrUtils.padPre("str", 0, 'a')).isEqualTo("result");
    }

    @Test
    void testPrependIfMissing1() throws Exception {
        assertThat(UtBStrUtils.prependIfMissing("str", "prefix", "prefixes")).isEqualTo("result");
    }

    @Test
    void testPrependIfMissing2() throws Exception {
        assertThat(UtBStrUtils.prependIfMissing("str", "prefix", false, "prefixes")).isEqualTo("result");
    }

    @Test
    void testPrependIfMissingIgnoreCase() throws Exception {
        assertThat(UtBStrUtils.prependIfMissingIgnoreCase("str", "prefix", "prefixes")).isEqualTo("result");
    }

    @Test
    void testRemoveAll1() throws Exception {
        assertThat(UtBStrUtils.removeAll("str", "strToRemove")).isEqualTo("result");
    }

    @Test
    void testRemoveAll2() throws Exception {
        assertThat(UtBStrUtils.removeAll("str", 'a')).isEqualTo("result");
    }

    @Test
    void testRemoveAllLineBreaks() throws Exception {
        assertThat(UtBStrUtils.removeAllLineBreaks("str")).isEqualTo("result");
    }

    @Test
    void testRemoveAny() throws Exception {
        assertThat(UtBStrUtils.removeAny("str", "strsToRemove")).isEqualTo("result");
    }

    @Test
    void testRemovePreAndLowerFirst1() throws Exception {
        assertThat(UtBStrUtils.removePreAndLowerFirst("str", 0)).isNull();
    }

    @Test
    void testRemovePreAndLowerFirst2() throws Exception {
        assertThat(UtBStrUtils.removePreAndLowerFirst("str", "prefix")).isEqualTo("result");
    }

    @Test
    void testRemovePrefix() throws Exception {
        assertThat(UtBStrUtils.removePrefix("str", "prefix")).isNull();
    }

    @Test
    void testRemovePrefixIgnoreCase() throws Exception {
        assertThat(UtBStrUtils.removePrefixIgnoreCase("str", "prefix")).isNull();
    }

    @Test
    void testRemoveSufAndLowerFirst() throws Exception {
        assertThat(UtBStrUtils.removeSufAndLowerFirst("str", "suffix")).isEqualTo("result");
    }

    @Test
    void testRemoveSuffix() throws Exception {
        assertThat(UtBStrUtils.removeSuffix("str", "suffix")).isNull();
    }

    @Test
    void testRemoveSuffixIgnoreCase() throws Exception {
        assertThat(UtBStrUtils.removeSuffixIgnoreCase("str", "suffix")).isNull();
    }

    @Test
    void testRepeat1() throws Exception {
        assertThat(UtBStrUtils.repeat('a', 0)).isEqualTo("");
    }

    @Test
    void testRepeat2() throws Exception {
        assertThat(UtBStrUtils.repeat("str", 0)).isEqualTo("result");
    }

    @Test
    void testRepeatAndJoin() throws Exception {
        assertThat(UtBStrUtils.repeatAndJoin("str", 0, "conjunction")).isEqualTo("");
    }

    @Test
    void testRepeatByLength() throws Exception {
        assertThat(UtBStrUtils.repeatByLength("str", 0)).isEqualTo("result");
    }

    @Test
    void testReplace1() throws Exception {
        assertThat(UtBStrUtils.replace("str", "searchStr", "replacement")).isEqualTo("result");
    }

    @Test
    void testReplace2() throws Exception {
        assertThat(UtBStrUtils.replace("str", "searchStr", "replacement", false)).isEqualTo("result");
    }

    @Test
    void testReplace3() throws Exception {
        assertThat(UtBStrUtils.replace("str", 0, "searchStr", "replacement", false)).isEqualTo("result");
    }

    @Test
    void testReplace4() throws Exception {
        assertThat(UtBStrUtils.replace("str", 0, 0, 'a')).isEqualTo("result");
    }

    @Test
    void testReplace5() throws Exception {
        // Setup
        final Func1<Matcher, String> replaceFun = null;

        // Run the test
        final String result = UtBStrUtils.replace("str", Pattern.compile("regex"), replaceFun);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    void testReplace6() throws Exception {
        // Setup
        final Func1<Matcher, String> replaceFun = null;

        // Run the test
        final String result = UtBStrUtils.replace("str", "regex", replaceFun);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    void testReplaceChars1() throws Exception {
        assertThat(UtBStrUtils.replaceChars("str", "chars", "replacedStr")).isEqualTo("result");
    }

    @Test
    void testReplaceChars2() throws Exception {
        assertThat(UtBStrUtils.replaceChars("str", new char[]{'a'}, "replacedStr")).isEqualTo("result");
    }

    @Test
    void testReplaceIgnoreCase() throws Exception {
        assertThat(UtBStrUtils.replaceIgnoreCase("str", "searchStr", "replacement")).isEqualTo("result");
    }

    @Test
    void testReverse() throws Exception {
        assertThat(UtBStrUtils.reverse("str")).isEqualTo("result");
    }

    @Test
    void testSimilar1() throws Exception {
        assertThat(UtBStrUtils.similar("str1", "str2")).isEqualTo(0.0, within(0.0001));
    }

    @Test
    void testSimilar2() throws Exception {
        assertThat(UtBStrUtils.similar("str1", "str2", 0)).isEqualTo("result");
    }

    @Test
    void testSplit1() throws Exception {
        assertThat(UtBStrUtils.split("str", 'a')).isEqualTo(Arrays.asList("value"));
        assertThat(UtBStrUtils.split("str", 'a')).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSplit2() throws Exception {
        assertThat(UtBStrUtils.split("str", 'a', 0)).isEqualTo(Arrays.asList("value"));
        assertThat(UtBStrUtils.split("str", 'a', 0)).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSplit3() throws Exception {
        assertThat(UtBStrUtils.split("str", 'a', false, false)).isEqualTo(Arrays.asList("value"));
        assertThat(UtBStrUtils.split("str", 'a', false, false)).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSplit4() throws Exception {
        assertThat(UtBStrUtils.split("str", 'a', 0, false, false)).isEqualTo(Arrays.asList("value"));
        assertThat(UtBStrUtils.split("str", 'a', 0, false, false)).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSplit5() throws Exception {
        assertThat(UtBStrUtils.split("str", "separator", 0, false, false)).isEqualTo(Arrays.asList("value"));
        assertThat(UtBStrUtils.split("str", "separator", 0, false, false)).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSplit6() throws Exception {
        assertThat(UtBStrUtils.split("str", "separator")).isEqualTo(new String[]{"result"});
        assertThat(UtBStrUtils.split("str", "separator")).isEqualTo(new String[]{});
    }

    @Test
    void testSplit7() throws Exception {
        assertThat(UtBStrUtils.split("str", 0)).isEqualTo(new String[]{"result"});
        assertThat(UtBStrUtils.split("str", 0)).isEqualTo(new String[]{});
    }

    @Test
    void testSplitToArray1() throws Exception {
        assertThat(UtBStrUtils.splitToArray("str", 'a')).isEqualTo(new String[]{"result"});
        assertThat(UtBStrUtils.splitToArray("str", 'a')).isEqualTo(new String[]{});
    }

    @Test
    void testSplitToArray2() throws Exception {
        assertThat(UtBStrUtils.splitToArray("str", 'a', 0)).isEqualTo(new String[]{"result"});
        assertThat(UtBStrUtils.splitToArray("str", 'a', 0)).isEqualTo(new String[]{});
    }

    @Test
    void testSplitToInt1() throws Exception {
        assertThat(UtBStrUtils.splitToInt("str", 'a')).isEqualTo(new int[]{0});
        assertThat(UtBStrUtils.splitToInt("str", 'a')).isEqualTo(new int[]{});
    }

    @Test
    void testSplitToInt2() throws Exception {
        assertThat(UtBStrUtils.splitToInt("str", "separator")).isEqualTo(new int[]{0});
        assertThat(UtBStrUtils.splitToInt("str", "separator")).isEqualTo(new int[]{});
    }

    @Test
    void testSplitToLong1() throws Exception {
        assertThat(UtBStrUtils.splitToLong("str", 'a')).isEqualTo(new long[]{0L});
        assertThat(UtBStrUtils.splitToLong("str", 'a')).isEqualTo(new long[]{});
    }

    @Test
    void testSplitToLong2() throws Exception {
        assertThat(UtBStrUtils.splitToLong("str", "separator")).isEqualTo(new long[]{0L});
        assertThat(UtBStrUtils.splitToLong("str", "separator")).isEqualTo(new long[]{});
    }

    @Test
    void testSplitTrim1() throws Exception {
        assertThat(UtBStrUtils.splitTrim("str", 'a')).isEqualTo(Arrays.asList("value"));
        assertThat(UtBStrUtils.splitTrim("str", 'a')).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSplitTrim2() throws Exception {
        assertThat(UtBStrUtils.splitTrim("str", "separator")).isEqualTo(Arrays.asList("value"));
        assertThat(UtBStrUtils.splitTrim("str", "separator")).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSplitTrim3() throws Exception {
        assertThat(UtBStrUtils.splitTrim("str", 'a', 0)).isEqualTo(Arrays.asList("value"));
        assertThat(UtBStrUtils.splitTrim("str", 'a', 0)).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSplitTrim4() throws Exception {
        assertThat(UtBStrUtils.splitTrim("str", "separator", 0)).isEqualTo(Arrays.asList("value"));
        assertThat(UtBStrUtils.splitTrim("str", "separator", 0)).isEqualTo(Collections.emptyList());
    }

    @Test
    void testStartWith1() throws Exception {
        assertThat(UtBStrUtils.startWith("str", 'a')).isFalse();
    }

    @Test
    void testStartWith2() throws Exception {
        assertThat(UtBStrUtils.startWith("str", "prefix", false)).isFalse();
    }

    @Test
    void testStartWith3() throws Exception {
        assertThat(UtBStrUtils.startWith("str", "prefix")).isFalse();
    }

    @Test
    void testStartWithAny() throws Exception {
        assertThat(UtBStrUtils.startWithAny("str", "prefixes")).isFalse();
    }

    @Test
    void testStartWithIgnoreCase() throws Exception {
        assertThat(UtBStrUtils.startWithIgnoreCase("str", "prefix")).isFalse();
    }

    @Test
    void testStr1() throws Exception {
        assertThat(UtBStrUtils.str("content".getBytes(), "UTF-8")).isEqualTo("result");
    }

    @Test
    void testStr2() throws Exception {
        assertThat(UtBStrUtils.str("content".getBytes(), StandardCharsets.UTF_8)).isEqualTo("result");
    }

    @Test
    void testStr3() throws Exception {
        assertThat(UtBStrUtils.str(new Byte[]{(byte) 0b0}, "UTF-8")).isEqualTo("result");
    }

    @Test
    void testStr4() throws Exception {
        assertThat(UtBStrUtils.str(new Byte[]{(byte) 0b0}, StandardCharsets.UTF_8)).isEqualTo("result");
    }

    @Test
    void testStr5() throws Exception {
        // Setup
        final ByteBuffer data = ByteBuffer.wrap("content".getBytes());

        // Run the test
        final String result = UtBStrUtils.str(data, "UTF-8");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    void testStr6() throws Exception {
        // Setup
        final ByteBuffer data = ByteBuffer.wrap("content".getBytes());

        // Run the test
        final String result = UtBStrUtils.str(data, StandardCharsets.UTF_8);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    void testStr7() throws Exception {
        assertThat(UtBStrUtils.str("cs")).isEqualTo("result");
    }

    @Test
    void testStrBuilder1() throws Exception {
        // Setup
        // Run the test
        final StrBuilder result = UtBStrUtils.strBuilder();

        // Verify the results
    }

    @Test
    void testStrBuilder2() throws Exception {
        // Setup
        // Run the test
        final StrBuilder result = UtBStrUtils.strBuilder(0);

        // Verify the results
    }

    @Test
    void testStrBuilder3() throws Exception {
        // Setup
        // Run the test
        final StrBuilder result = UtBStrUtils.strBuilder("strs");

        // Verify the results
    }

    @Test
    void testStrip1() throws Exception {
        assertThat(UtBStrUtils.strip("str", "prefixOrSuffix")).isEqualTo("result");
    }

    @Test
    void testStrip2() throws Exception {
        assertThat(UtBStrUtils.strip("str", "prefix", "suffix")).isEqualTo("result");
    }

    @Test
    void testStripIgnoreCase1() throws Exception {
        assertThat(UtBStrUtils.stripIgnoreCase("str", "prefixOrSuffix")).isEqualTo("result");
    }

    @Test
    void testStripIgnoreCase2() throws Exception {
        assertThat(UtBStrUtils.stripIgnoreCase("str", "prefix", "suffix")).isEqualTo("result");
    }

    @Test
    void testSub() throws Exception {
        assertThat(UtBStrUtils.sub("str", 0, 0)).isEqualTo("");
    }

    @Test
    void testSubAfter1() throws Exception {
        assertThat(UtBStrUtils.subAfter("string", "separator", false)).isEqualTo("result");
    }

    @Test
    void testSubAfter2() throws Exception {
        assertThat(UtBStrUtils.subAfter("string", 'a', false)).isEqualTo("123");
    }

    @Test
    void testSubBefore1() throws Exception {
        assertThat(UtBStrUtils.subBefore("string", "separator", false)).isNull();
    }

    @Test
    void testSubBefore2() throws Exception {
        assertThat(UtBStrUtils.subBefore("string", 'a', false)).isEqualTo("result");
    }

    @Test
    void testSubBetween1() throws Exception {
        assertThat(UtBStrUtils.subBetween("str", "before", "after")).isEqualTo("result");
    }

    @Test
    void testSubBetween2() throws Exception {
        assertThat(UtBStrUtils.subBetween("str", "beforeAndAfter")).isEqualTo("result");
    }

    @Test
    void testSubBetweenAll() throws Exception {
        assertThat(UtBStrUtils.subBetweenAll("str", "prefix", "suffix")).isEqualTo(new String[]{"result"});
        assertThat(UtBStrUtils.subBetweenAll("str", "prefix", "suffix")).isEqualTo(new String[]{});
    }

    @Test
    void testSubByCodePoint() throws Exception {
        assertThat(UtBStrUtils.subByCodePoint("str", 0, 0)).isNull();
    }

    @Test
    void testSubPre() throws Exception {
        assertThat(UtBStrUtils.subPre("string", 0)).isEqualTo("result");
    }

    @Test
    void testSubPreGbk() throws Exception {
        assertThat(UtBStrUtils.subPreGbk("str", 0, "suffix")).isEqualTo("result");
    }

    @Test
    void testSubSuf() throws Exception {
        assertThat(UtBStrUtils.subSuf("string", 0)).isEqualTo("result");
    }

    @Test
    void testSubSufByLength() throws Exception {
        assertThat(UtBStrUtils.subSufByLength("string", 0)).isEqualTo("result");
    }

    @Test
    void testSubWithLength() throws Exception {
        assertThat(UtBStrUtils.subWithLength("input", 0, 0)).isEqualTo("result");
    }

    @Test
    void testSwapCase() throws Exception {
        assertThat(UtBStrUtils.swapCase("str")).isEqualTo("result");
    }

    @Test
    void testToCamelCase() throws Exception {
        assertThat(UtBStrUtils.toCamelCase("name")).isEqualTo("name");
    }

    @Test
    void testToString() throws Exception {
        assertThat(UtBStrUtils.toString("obj")).isEqualTo("result");
    }

    @Test
    void testToSymbolCase() throws Exception {
        assertThat(UtBStrUtils.toSymbolCase("str", 'a')).isEqualTo("result");
    }

    @Test
    void testToUnderlineCase() throws Exception {
        assertThat(UtBStrUtils.toUnderlineCase("str")).isEqualTo("result");
    }

    @Test
    void testTotalLength() throws Exception {
        assertThat(UtBStrUtils.totalLength("strs")).isEqualTo(0);
    }

    @Test
    void testTrim1() throws Exception {
        assertThat(UtBStrUtils.trim("str")).isEqualTo("result");
    }

    @Test
    void testTrim2() throws Exception {
        // Setup
        // Run the test
        UtBStrUtils.trim(new String[]{"strs"});

        // Verify the results
    }

    @Test
    void testTrim3() throws Exception {
        assertThat(UtBStrUtils.trim("str", 0)).isEqualTo("str");
    }

    @Test
    void testTrimEnd() throws Exception {
        assertThat(UtBStrUtils.trimEnd("str")).isEqualTo("result");
    }

    @Test
    void testTrimStart() throws Exception {
        assertThat(UtBStrUtils.trimStart("str")).isEqualTo("result");
    }

    @Test
    void testTrimToEmpty() throws Exception {
        assertThat(UtBStrUtils.trimToEmpty("str")).isEqualTo("result");
    }

    @Test
    void testTrimToNull() throws Exception {
        assertThat(UtBStrUtils.trimToNull("str")).isEqualTo("result");
    }

    @Test
    void testUnWrap1() throws Exception {
        assertThat(UtBStrUtils.unWrap("str", "prefix", "suffix")).isEqualTo("str");
    }

    @Test
    void testUnWrap2() throws Exception {
        assertThat(UtBStrUtils.unWrap("str", 'a', 'a')).isEqualTo("str");
    }

    @Test
    void testUnWrap3() throws Exception {
        assertThat(UtBStrUtils.unWrap("str", 'a')).isEqualTo("result");
    }

    @Test
    void testUpperFirst() throws Exception {
        assertThat(UtBStrUtils.upperFirst("str")).isEqualTo("str");
    }

    @Test
    void testUpperFirstAndAddPre() throws Exception {
        assertThat(UtBStrUtils.upperFirstAndAddPre("str", "preString")).isEqualTo("result");
    }

    @Test
    void testUtf8Bytes() throws Exception {
        assertThat(UtBStrUtils.utf8Bytes("str")).isEqualTo("content".getBytes());
    }

    @Test
    void testUuid() throws Exception {
        assertThat(UtBStrUtils.uuid()).isEqualTo("result");
    }

    @Test
    void testWrap1() throws Exception {
        assertThat(UtBStrUtils.wrap("str", "prefixAndSuffix")).isEqualTo("result");
    }

    @Test
    void testWrap2() throws Exception {
        assertThat(UtBStrUtils.wrap("str", "prefix", "suffix")).isEqualTo("result");
    }

    @Test
    void testWrapIfMissing() throws Exception {
        assertThat(UtBStrUtils.wrapIfMissing("str", "prefix", "suffix")).isEqualTo("result");
    }

    @Test
    void testTest01() {
        assertThat(UtBStrUtils.test01("str")).isEqualTo("result");
    }

    @Test
    void testTest02() {
        assertThat(UtBStrUtils.test02("str")).isEqualTo("result");
    }

    @Test
    void testTest03() {
        assertThat(UtBStrUtils.test03("str")).isEqualTo("");
    }
}
