package com.yonyou.dmscus.customer.utils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class ResponseUtilTest {

    private ResponseUtil responseUtilUnderTest;

    @BeforeEach
    void setUp() {
        responseUtilUnderTest = new ResponseUtil();
    }

    @Test
    void testResultCodeGetterAndSetter() {
        final int resultCode = 0;
        responseUtilUnderTest.setResultCode(resultCode);
        assertThat(responseUtilUnderTest.getResultCode()).isEqualTo(resultCode);
    }

    @Test
    void testSuccessGetterAndSetter() {
        final boolean success = false;
        responseUtilUnderTest.setSuccess(success);
        assertThat(responseUtilUnderTest.isSuccess()).isFalse();
    }

    @Test
    void testDataGetterAndSetter() {
        final Object data = "data";
        responseUtilUnderTest.setData(data);
        assertThat(responseUtilUnderTest.getData()).isEqualTo(data);
    }

    @Test
    void testErrMsgGetterAndSetter() {
        final Object errMsg = "errMsg";
        responseUtilUnderTest.setErrMsg(errMsg);
        assertThat(responseUtilUnderTest.getErrMsg()).isEqualTo(errMsg);
    }
}
