package com.yonyou.dmscus.customer.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class CommonConstantsTest {

    @Test
    public void testDictValidType() {
        assertEquals("1001", CommonConstants.DICT_VALIDS_TYPE);
        assertEquals(10011001, CommonConstants.DICT_VALIDS_VALID);
        assertEquals(10011002, CommonConstants.DICT_VALIDS_INVALID);
    }

    @Test
    public void testInviteTypes() {
        assertEquals(82381009, CommonConstants.INVITE_TYPE_IX);
        assertEquals(82381010, CommonConstants.INVITE_TYPE_X);
        assertEquals(82381011, CommonConstants.INVITE_TYPE_XI);
        assertEquals(82381012, CommonConstants.INVITE_TYPE_XII);
        assertEquals(82381013, CommonConstants.INVITE_TYPE_XIII);
    }

    @Test
    public void testInviteRules() {
        assertEquals(82041001, CommonConstants.INVITE_RULE_I);
        assertEquals(82041002, CommonConstants.INVITE_RULE_II);
        assertEquals(82041003, CommonConstants.INVITE_RULE_III);
        assertEquals(82041004, CommonConstants.INVITE_RULE_IV);
        assertEquals(82041005, CommonConstants.INVITE_RULE_V);
        assertEquals(82041006, CommonConstants.INVITE_RULE_VI);
    }

    @Test
    public void testMiscellaneousCommonConstants() {
        assertEquals(-90, CommonConstants.UP_DAY);
        assertEquals(2, CommonConstants.TAG_BINTTYPE_AGLOR);
        assertEquals("volvo", CommonConstants.DEFAULT_APPID);
        assertEquals("vin", CommonConstants.TAG_GROUP_VIN);
        assertEquals("65A", CommonConstants.KEY_CUSTOMERS);
        assertEquals("0", CommonConstants.ZERO);
    }

    @Test
    public void testVOCCommonConstants() {
        assertEquals("Normal", CommonConstants.VOC_NORMAL);
        assertEquals("TimeExceeded", CommonConstants.VOC_TIMEEXCEEDED);
        assertEquals("AlmostTimeForService", CommonConstants.VOC_ALMOSTTIMEFORSERVICE);
        assertEquals("TimeForService", CommonConstants.VOC_TIMEFORSERVICE);
        assertEquals(83981001, CommonConstants.VOC_NORMAL_NUM);
        assertEquals(83981002, CommonConstants.VOC_TIMEEXCEEDED_NUM);
        assertEquals(83981003, CommonConstants.VOC_ALMOSTTIMEFORSERVICE_NUM);
        assertEquals(83981004, CommonConstants.VOC_TIMEFORSERVICE_NUM);
        assertEquals("新增", CommonConstants.VOC_INSERT);
        assertEquals("改变", CommonConstants.VOC_UPDATE);
        assertEquals("消失", CommonConstants.VOC_FADED);
    }

    @Test
    public void testLogMessages() {
        assertEquals("查询结果,size{}", CommonConstants.LOG_S1);
        assertEquals("开始创建首保任务{}", CommonConstants.LOG_S2);
        assertEquals("不存在首保日期间隔规则{}", CommonConstants.LOG_S3);
        assertEquals("开票日期{}", CommonConstants.LOG_S4);
        assertEquals("规则间隔月{}", CommonConstants.LOG_S5);
        assertEquals("建议进厂{}", CommonConstants.LOG_S6);
        assertEquals("开始创建客户流失任务{}", CommonConstants.LOG_S7);
        assertEquals("修改流失客户，数据小于200", CommonConstants.LOG_S8);
        assertEquals("修改流失客户，数据大于200{},{}", CommonConstants.LOG_S9);
        assertEquals("voc任务下发成功，数据小于200", CommonConstants.LOG_S10);
        assertEquals("voc任务下发成功，数据大于200{},{}", CommonConstants.LOG_S11);
        assertEquals("voc线索下发成功，数据大于200{},{}", CommonConstants.LOG_S12);
        assertEquals("voc线索下发成功，数据小于200", CommonConstants.LOG_S13);
        assertEquals("voc线索多出字段保存VocInviteVehicleTaskRecordPo成功，数据小于200", CommonConstants.LOG_S14);
        assertEquals("voc线索多出字段保存VocInviteVehicleTaskRecordPo成功，数据大于200{},{}", CommonConstants.LOG_S15);
        assertEquals("修改流失预警，数据小于200", CommonConstants.LOG_S16);
        assertEquals("修改流失预警，数据大于200{},{}", CommonConstants.LOG_S17);
        assertEquals("voc新增任务返回数据{}", CommonConstants.LOG_S18);
        assertEquals("voc新增任务返回数据1{}", CommonConstants.LOG_S19);
        assertEquals("查询结果,size{}", CommonConstants.LOG_S20);
        assertEquals("查询结果，rule{}", CommonConstants.LOG_S21);
        assertEquals("18个月upsr6VOC修改数据{}", CommonConstants.LOG_S22);
        assertEquals("18个月upst6VOC修改数据{}", CommonConstants.LOG_S23);
        assertEquals("文件内容为空", CommonConstants.LOG_S24);
        assertEquals("客户流失初始化开始:{}", CommonConstants.LOG_S25);
    }

    @Test
    public void testLeadTypes() {
        assertEquals(87891002, CommonConstants.RECORD_TYPE_0);
        assertEquals(87891001, CommonConstants.RECORD_TYPE_1);
    }

    @Test
    public void testLossTypes() {
        assertEquals(0, CommonConstants.LOSS_TYPE_0);
        assertEquals(87901001, CommonConstants.LOSS_TYPE_1);
        assertEquals(87901002, CommonConstants.LOSS_TYPE_2);
        assertEquals(87901003, CommonConstants.LOSS_TYPE_3);
    }

    @Test
    public void testModTypes() {
        assertEquals(91111002, CommonConstants.MODTYPE_91111002);
        assertEquals(91111011, CommonConstants.MOD_TYPE_91111011);
        assertEquals(91111015, CommonConstants.MOD_TYPE_91111015);
        assertEquals(91112011, CommonConstants.MOD_TYPE_91112011);
    }

    @Test
    public void testAccidentLeadTypes() {
        assertEquals(91111029, CommonConstants.ACCIDENT_LEAD_TYPE);
    }

    @Test
    public void testFollowTimeOutClose() {
        assertEquals(83531006, CommonConstants.FOLLOW_TIME_OUT_CLOSE);
        assertEquals(0, CommonConstants.ALL_FOLLOW_STATUS);
    }

    @Test
    public void testOrderBy() {
        assertEquals("tt.created_at asc ", CommonConstants.ORDER_BY_STR);
    }

    @Test
    public void testIsCloseCase() {
        assertEquals(10041001, CommonConstants.IS_CLOSE_CASE_YES);
        assertEquals(10041002, CommonConstants.IS_CLOSE_CASE_NO);
    }

    @Test
    public void testResultCodes() {
        assertEquals("200", CommonConstants.RESULT_CODE_200);
    }

    @Test
    public void testTimezone() {
        assertEquals("Asia/Shanghai", CommonConstants.LOCALDATETIME_CHINA_ZONE);
    }

    @Test
    public void testLeadType103() {
        assertEquals("103", CommonConstants.LEADS_TYPE_103);
    }

    @Test
    public void testSuccessCodes() {
        assertEquals("0", CommonConstants.SUCCESS_CODE);
        assertEquals("成功", CommonConstants.SUCCESS_MSG);
    }

    @Test
    public void testCdpCustomerProfileToken() {
        assertEquals("cdp:customerProfile:token:", CommonConstants.CDP_CUSTOMER_PROFILE_TOKEN);
    }
}