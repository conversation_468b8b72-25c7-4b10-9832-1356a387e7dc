package com.yonyou.dmscus.customer.constants.faultLight;

import org.junit.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

public class FaultClueStateEnumTest {


    @Test
    public void testEnumValues() {
        // 测试枚举值是否正常加载
        assertNotNull(FaultClueStateEnum.WAITING_FOR_CONTACT);
        assertNotNull(FaultClueStateEnum.WAITING_FOR_APPOINTMENT);
        assertNotNull(FaultClueStateEnum.ALREADY_COMPLETED);
    }

    @ParameterizedTest
    @MethodSource("provideCodeAndNamePairs")
    public void testGetNameByCode(Integer code, String expectedName) {
        // 测试 getNameByCode 方法
        assertEquals(expectedName, FaultClueStateEnum.getNameByCode(code));
    }

    @ParameterizedTest
    @MethodSource("provideNameAndCodePairs")
    public void testGetCodeByName(String name, Integer expectedCode) {
        // 测试 getCodeByName 方法
        assertEquals(expectedCode, FaultClueStateEnum.getCodeByName(name));
    }

    @Test
    public void testInvalidCode() {
        // 测试无效的 code
        assertNull(FaultClueStateEnum.getNameByCode(99999999));
    }

    @Test
    public void testInvalidName() {
        // 测试无效的 name
        assertNull(FaultClueStateEnum.getCodeByName("无效名称"));
    }

    // 提供测试数据
    private static Stream<Arguments> provideCodeAndNamePairs() {
        return Stream.of(
                Arguments.of(10541001, "待联络"),
                Arguments.of(10541002, "待预约"),
                Arguments.of(10541007, "已完成")
        );
    }

    private static Stream<Arguments> provideNameAndCodePairs() {
        return Stream.of(
                Arguments.of("待联络", 10541001),
                Arguments.of("待预约", 10541002),
                Arguments.of("已完成", 10541007)
        );
    }
}
