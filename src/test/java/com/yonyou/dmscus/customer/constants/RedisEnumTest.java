package com.yonyou.dmscus.customer.constants;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class RedisEnumTest {

    @Test
    void testGetKey1() {
        assertThat(RedisEnum.FAULT_NX.getKey()).isEqualTo("pk");
        assertThat(RedisEnum.CLUE_DATA_SYNCHRO.getKey()).isEqualTo("pk");
        assertThat(RedisEnum.DO_FOLLOW_SUB.getKey()).isEqualTo("pk");
        assertThat(RedisEnum.END.getKey()).isEqualTo("pk");
    }

    @Test
    void testGetKey2() {
        assertThat(RedisEnum.FAULT_NX.getKey("key_")).isEqualTo("result");
        assertThat(RedisEnum.CLUE_DATA_SYNCHRO.getKey("key_")).isEqualTo("result");
        assertThat(RedisEnum.DO_FOLLOW_SUB.getKey("key_")).isEqualTo("result");
        assertThat(RedisEnum.END.getKey("key_")).isEqualTo("result");
    }

    @Test
    void testGetKey3() {
        assertThat(RedisEnum.FAULT_NX.getKey("key_")).isEqualTo("pk");
        assertThat(RedisEnum.CLUE_DATA_SYNCHRO.getKey("key_")).isEqualTo("pk");
        assertThat(RedisEnum.DO_FOLLOW_SUB.getKey("key_")).isEqualTo("pk");
        assertThat(RedisEnum.END.getKey("key_")).isEqualTo("pk");
    }

    @Test
    void testGetTimeoutMsecs() {
        assertThat(RedisEnum.FAULT_NX.getTimeoutMsecs()).isEqualTo(0);
        assertThat(RedisEnum.CLUE_DATA_SYNCHRO.getTimeoutMsecs()).isEqualTo(0);
        assertThat(RedisEnum.DO_FOLLOW_SUB.getTimeoutMsecs()).isEqualTo(0);
        assertThat(RedisEnum.END.getTimeoutMsecs()).isEqualTo(0);
    }

    @Test
    void testGetExpireMsecs() {
        assertThat(RedisEnum.FAULT_NX.getExpireMsecs()).isEqualTo(0);
        assertThat(RedisEnum.CLUE_DATA_SYNCHRO.getExpireMsecs()).isEqualTo(0);
        assertThat(RedisEnum.DO_FOLLOW_SUB.getExpireMsecs()).isEqualTo(0);
        assertThat(RedisEnum.END.getExpireMsecs()).isEqualTo(0);
    }
}
