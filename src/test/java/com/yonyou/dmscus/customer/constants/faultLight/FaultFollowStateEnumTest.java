package com.yonyou.dmscus.customer.constants.faultLight;

import org.junit.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

public class FaultFollowStateEnumTest {

    @Test
    public void testEnumValues() {
        // 测试枚举值是否正常加载
        assertNotNull(FaultFollowStateEnum.WAITING_FOR_CONTACT);
        assertNotNull(FaultFollowStateEnum.WAITING_FOR_APPOINTMENT);
        assertNotNull(FaultFollowStateEnum.ALREADY_COMPLETED);
    }

    @ParameterizedTest
    @MethodSource("provideCodeAndNamePairs")
    public void testGetNameByCode(Integer code, String expectedName) {
        // 测试 getNameByCode 方法
        assertEquals(expectedName, FaultFollowStateEnum.getNameByCode(code));
    }

    @ParameterizedTest
    @MethodSource("provideNameAndCodePairs")
    public void testGetCodeByName(String name, Integer expectedCode) {
        // 测试 getCodeByName 方法
        assertEquals(expectedCode, FaultFollowStateEnum.getCodeByName(name));
    }


    @Test
    public void testInvalidCode() {
        // 测试无效的 code
        assertNull(FaultFollowStateEnum.getNameByCode(99999999));
    }

    @Test
    public void testInvalidName() {
        // 测试无效的 name
        assertNull(FaultFollowStateEnum.getCodeByName("无效名称"));
    }

    // 提供测试数据
    private static Stream<Arguments> provideCodeAndNamePairs() {
        return Stream.of(
                Arguments.of(10551001, "待联络"),
                Arguments.of(10551002, "待预约"),
                Arguments.of(10551009, "已完成")
        );
    }

    private static Stream<Arguments> provideNameAndCodePairs() {
        return Stream.of(
                Arguments.of("待联络", 10551001),
                Arguments.of("待预约", 10551002),
                Arguments.of("已完成", 10551009)
        );
    }
}
