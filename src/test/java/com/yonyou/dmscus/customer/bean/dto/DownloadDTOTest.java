package com.yonyou.dmscus.customer.bean.dto;

import com.yonyou.dmscloud.framework.service.excel.ExcelExportColumn;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

class DownloadDTOTest {

    private DownloadDTO downloadDTOUnderTest;

    @BeforeEach
    void setUp() {
        downloadDTOUnderTest = new DownloadDTO();
    }

    @Test
    void testServiceUrlGetterAndSetter() {
        final String serviceUrl = "serviceUrl";
        downloadDTOUnderTest.setServiceUrl(serviceUrl);
        assertThat(downloadDTOUnderTest.getServiceUrl()).isEqualTo(serviceUrl);
    }

    @Test
    void testQueryParamsGetterAndSetter() {
        final Map<String, Object> queryParams = new HashMap<>();
        downloadDTOUnderTest.setQueryParams(queryParams);
        assertThat(downloadDTOUnderTest.getQueryParams()).isEqualTo(queryParams);
    }

    @Test
    void testExcelExportColumnListGetterAndSetter() {
        final List<ExcelExportColumn> excelExportColumnList = Arrays.asList(
                new ExcelExportColumn("fieldName", "title", "format"));
        downloadDTOUnderTest.setExcelExportColumnList(excelExportColumnList);
        assertThat(downloadDTOUnderTest.getExcelExportColumnList()).isEqualTo(excelExportColumnList);
    }

    @Test
    void testExcelNameGetterAndSetter() {
        final String excelName = "excelName";
        downloadDTOUnderTest.setExcelName(excelName);
        assertThat(downloadDTOUnderTest.getExcelName()).isEqualTo(excelName);
    }

    @Test
    void testSheetNameGetterAndSetter() {
        final String sheetName = "sheetName";
        downloadDTOUnderTest.setSheetName(sheetName);
        assertThat(downloadDTOUnderTest.getSheetName()).isEqualTo(sheetName);
    }

    @Test
    void testFieldIdGetterAndSetter() {
        final String fieldId = "fieldId";
        downloadDTOUnderTest.setFieldId(fieldId);
        assertThat(downloadDTOUnderTest.getFieldId()).isEqualTo(fieldId);
    }
}
