package com.yonyou.dmscus.customer.bean.entity;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import static org.assertj.core.api.Assertions.assertThat;

class GoodwilApplyAuditProcessPOTest {

    private GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPOUnderTest;

    @BeforeEach
    void setUp() {
        goodwilApplyAuditProcessPOUnderTest = new GoodwilApplyAuditProcessPO();
    }

    @Test
    void testAppIdGetterAndSetter() {
        final String appId = "appId";
        goodwilApplyAuditProcessPOUnderTest.setAppId(appId);
        assertThat(goodwilApplyAuditProcessPOUnderTest.getAppId()).isEqualTo(appId);
    }

    @Test
    void testOwnerCodeGetterAndSetter() {
        final String ownerCode = "ownerCode";
        goodwilApplyAuditProcessPOUnderTest.setOwnerCode(ownerCode);
        assertThat(goodwilApplyAuditProcessPOUnderTest.getOwnerCode()).isEqualTo(ownerCode);
    }

    @Test
    void testOwnerParCodeGetterAndSetter() {
        final String ownerParCode = "ownerParCode";
        goodwilApplyAuditProcessPOUnderTest.setOwnerParCode(ownerParCode);
        assertThat(goodwilApplyAuditProcessPOUnderTest.getOwnerParCode()).isEqualTo(ownerParCode);
    }

    @Test
    void testOrgIdGetterAndSetter() {
        final Integer orgId = 0;
        goodwilApplyAuditProcessPOUnderTest.setOrgId(orgId);
        assertThat(goodwilApplyAuditProcessPOUnderTest.getOrgId()).isEqualTo(orgId);
    }

    @Test
    void testIdGetterAndSetter() {
        final Long id = 0L;
        goodwilApplyAuditProcessPOUnderTest.setId(id);
        assertThat(goodwilApplyAuditProcessPOUnderTest.getId()).isEqualTo(id);
    }

    @Test
    void testGoodwillApplyIdGetterAndSetter() {
        final Long goodwillApplyId = 0L;
        goodwilApplyAuditProcessPOUnderTest.setGoodwillApplyId(goodwillApplyId);
        assertThat(goodwilApplyAuditProcessPOUnderTest.getGoodwillApplyId()).isEqualTo(goodwillApplyId);
    }

    @Test
    void testAuditObjectGetterAndSetter() {
        final Integer auditObject = 0;
        goodwilApplyAuditProcessPOUnderTest.setAuditObject(auditObject);
        assertThat(goodwilApplyAuditProcessPOUnderTest.getAuditObject()).isEqualTo(auditObject);
    }

    @Test
    void testAuditTypeGetterAndSetter() {
        final Integer auditType = 0;
        goodwilApplyAuditProcessPOUnderTest.setAuditType(auditType);
        assertThat(goodwilApplyAuditProcessPOUnderTest.getAuditType()).isEqualTo(auditType);
    }

    @Test
    void testAuditPositionGetterAndSetter() {
        final String auditPosition = "auditPosition";
        goodwilApplyAuditProcessPOUnderTest.setAuditPosition(auditPosition);
        assertThat(goodwilApplyAuditProcessPOUnderTest.getAuditPosition()).isEqualTo(auditPosition);
    }

    @Test
    void testAuditSortGetterAndSetter() {
        final Integer auditSort = 0;
        goodwilApplyAuditProcessPOUnderTest.setAuditSort(auditSort);
        assertThat(goodwilApplyAuditProcessPOUnderTest.getAuditSort()).isEqualTo(auditSort);
    }

    @Test
    void testAuditStatusGetterAndSetter() {
        final Integer auditStatus = 0;
        goodwilApplyAuditProcessPOUnderTest.setAuditStatus(auditStatus);
        assertThat(goodwilApplyAuditProcessPOUnderTest.getAuditStatus()).isEqualTo(auditStatus);
    }

    @Test
    void testAuditDateGetterAndSetter() {
        final LocalDateTime auditDate = LocalDateTime.of(2020, 1, 1, 0, 0, 0);
        goodwilApplyAuditProcessPOUnderTest.setAuditDate(auditDate);
        assertThat(goodwilApplyAuditProcessPOUnderTest.getAuditDate()).isEqualTo(auditDate);
    }

    @Test
    void testIsValidGetterAndSetter() {
        final Integer isValid = 0;
        goodwilApplyAuditProcessPOUnderTest.setIsValid(isValid);
        assertThat(goodwilApplyAuditProcessPOUnderTest.getIsValid()).isEqualTo(isValid);
    }

    @Test
    void testIsDeletedGetterAndSetter() {
        final Boolean isDeleted = false;
        goodwilApplyAuditProcessPOUnderTest.setIsDeleted(isDeleted);
        assertThat(goodwilApplyAuditProcessPOUnderTest.getIsDeleted()).isFalse();
    }

    @Test
    void testCreatedAtGetterAndSetter() {
        final Date createdAt = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        goodwilApplyAuditProcessPOUnderTest.setCreatedAt(createdAt);
        assertThat(goodwilApplyAuditProcessPOUnderTest.getCreatedAt()).isEqualTo(createdAt);
    }

    @Test
    void testUpdatedAtGetterAndSetter() {
        final Date updatedAt = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        goodwilApplyAuditProcessPOUnderTest.setUpdatedAt(updatedAt);
        assertThat(goodwilApplyAuditProcessPOUnderTest.getUpdatedAt()).isEqualTo(updatedAt);
    }

    @Test
    void testId1GetterAndSetter() {
        final Long id = 0L;
        goodwilApplyAuditProcessPOUnderTest.setId(id);
        assertThat(goodwilApplyAuditProcessPOUnderTest.pkVal()).isEqualTo(id);
    }

    @Test
    void testToString() {
        assertThat(goodwilApplyAuditProcessPOUnderTest.toString()).isEqualTo("result");
    }
}
