package com.yonyou.dmscus.customer.bean.dto;

import com.yonyou.dmscloud.framework.base.po.BasePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

class GoodwilApplyAuditProcessDTOTest {

    private GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTOUnderTest;

    @BeforeEach
    void setUp() {
        goodwilApplyAuditProcessDTOUnderTest = new GoodwilApplyAuditProcessDTO();
    }

    @Test
    void testAppIdGetterAndSetter() {
        final String appId = "appId";
        goodwilApplyAuditProcessDTOUnderTest.setAppId(appId);
        assertThat(goodwilApplyAuditProcessDTOUnderTest.getAppId()).isEqualTo(appId);
    }

    @Test
    void testOwnerCodeGetterAndSetter() {
        final String ownerCode = "ownerCode";
        goodwilApplyAuditProcessDTOUnderTest.setOwnerCode(ownerCode);
        assertThat(goodwilApplyAuditProcessDTOUnderTest.getOwnerCode()).isEqualTo(ownerCode);
    }

    @Test
    void testOwnerParCodeGetterAndSetter() {
        final String ownerParCode = "ownerParCode";
        goodwilApplyAuditProcessDTOUnderTest.setOwnerParCode(ownerParCode);
        assertThat(goodwilApplyAuditProcessDTOUnderTest.getOwnerParCode()).isEqualTo(ownerParCode);
    }

    @Test
    void testOrgIdGetterAndSetter() {
        final Integer orgId = 0;
        goodwilApplyAuditProcessDTOUnderTest.setOrgId(orgId);
        assertThat(goodwilApplyAuditProcessDTOUnderTest.getOrgId()).isEqualTo(orgId);
    }

    @Test
    void testIdGetterAndSetter() {
        final Long id = 0L;
        goodwilApplyAuditProcessDTOUnderTest.setId(id);
        assertThat(goodwilApplyAuditProcessDTOUnderTest.getId()).isEqualTo(id);
    }

    @Test
    void testGoodwillApplyIdGetterAndSetter() {
        final Long goodwillApplyId = 0L;
        goodwilApplyAuditProcessDTOUnderTest.setGoodwillApplyId(goodwillApplyId);
        assertThat(goodwilApplyAuditProcessDTOUnderTest.getGoodwillApplyId()).isEqualTo(goodwillApplyId);
    }

    @Test
    void testAuditObjectGetterAndSetter() {
        final Integer auditObject = 0;
        goodwilApplyAuditProcessDTOUnderTest.setAuditObject(auditObject);
        assertThat(goodwilApplyAuditProcessDTOUnderTest.getAuditObject()).isEqualTo(auditObject);
    }

    @Test
    void testAuditTypeGetterAndSetter() {
        final Integer auditType = 0;
        goodwilApplyAuditProcessDTOUnderTest.setAuditType(auditType);
        assertThat(goodwilApplyAuditProcessDTOUnderTest.getAuditType()).isEqualTo(auditType);
    }

    @Test
    void testAuditPositionGetterAndSetter() {
        final String auditPosition = "auditPosition";
        goodwilApplyAuditProcessDTOUnderTest.setAuditPosition(auditPosition);
        assertThat(goodwilApplyAuditProcessDTOUnderTest.getAuditPosition()).isEqualTo(auditPosition);
    }

    @Test
    void testAuditSortGetterAndSetter() {
        final Integer auditSort = 0;
        goodwilApplyAuditProcessDTOUnderTest.setAuditSort(auditSort);
        assertThat(goodwilApplyAuditProcessDTOUnderTest.getAuditSort()).isEqualTo(auditSort);
    }

    @Test
    void testAuditStatusGetterAndSetter() {
        final Integer auditStatus = 0;
        goodwilApplyAuditProcessDTOUnderTest.setAuditStatus(auditStatus);
        assertThat(goodwilApplyAuditProcessDTOUnderTest.getAuditStatus()).isEqualTo(auditStatus);
    }

    @Test
    void testAuditDateGetterAndSetter() {
        final LocalDateTime auditDate = LocalDateTime.of(2020, 1, 1, 0, 0, 0);
        goodwilApplyAuditProcessDTOUnderTest.setAuditDate(auditDate);
        assertThat(goodwilApplyAuditProcessDTOUnderTest.getAuditDate()).isEqualTo(auditDate);
    }

    @Test
    void testIsValidGetterAndSetter() {
        final Integer isValid = 0;
        goodwilApplyAuditProcessDTOUnderTest.setIsValid(isValid);
        assertThat(goodwilApplyAuditProcessDTOUnderTest.getIsValid()).isEqualTo(isValid);
    }

    @Test
    void testIsDeletedGetterAndSetter() {
        final Boolean isDeleted = false;
        goodwilApplyAuditProcessDTOUnderTest.setIsDeleted(isDeleted);
        assertThat(goodwilApplyAuditProcessDTOUnderTest.getIsDeleted()).isFalse();
    }

    @Test
    void testCreatedAtGetterAndSetter() {
        final LocalDateTime createdAt = LocalDateTime.of(2020, 1, 1, 0, 0, 0);
        goodwilApplyAuditProcessDTOUnderTest.setCreatedAt(createdAt);
        assertThat(goodwilApplyAuditProcessDTOUnderTest.getCreatedAt()).isEqualTo(createdAt);
    }

    @Test
    void testUpdatedAtGetterAndSetter() {
        final LocalDateTime updatedAt = LocalDateTime.of(2020, 1, 1, 0, 0, 0);
        goodwilApplyAuditProcessDTOUnderTest.setUpdatedAt(updatedAt);
        assertThat(goodwilApplyAuditProcessDTOUnderTest.getUpdatedAt()).isEqualTo(updatedAt);
    }

    @Test
    void testToString() {
        assertThat(goodwilApplyAuditProcessDTOUnderTest.toString()).isEqualTo("result");
    }

}
