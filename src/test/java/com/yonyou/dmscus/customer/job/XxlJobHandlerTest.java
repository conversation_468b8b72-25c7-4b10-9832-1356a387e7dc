package com.yonyou.dmscus.customer.job;

import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.AppPushDTO;
import com.yonyou.dmscus.customer.service.accidentClues.AccidentCluesService;
import com.yonyou.dmscus.customer.service.cdpTagTask.qb.CdpTagTaskService;
import com.yonyou.dmscus.customer.service.clueMigrate.ITmClueMigrateTaskService;
import com.yonyou.dmscus.customer.service.complaint.ComplaintFollowService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightClueService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightService;
import com.yonyou.dmscus.customer.service.goodwillTask.GoodwillTaskService;
import com.yonyou.dmscus.customer.service.impl.CommonServiceImpl;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.invitationautocreate.InviteVehicleDoTaskService;
import com.yonyou.dmscus.customer.service.invitationautocreate.InviteVehicleTaskService;
import com.yonyou.dmscus.customer.service.invitationautocreate.VocManageService;
import com.yonyou.dmscus.customer.service.inviteInsurance.InviteInsuranceVehicleTaskService;
import com.yonyou.dmscus.customer.service.oss.OssBusProService;
import com.yonyou.dmscus.customer.service.oss.OssService;
import com.yonyou.dmscus.customer.service.pushRecord.CompensateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class XxlJobHandlerTest {

    @Mock
    private ComplaintFollowService mockComplaintFollowService;
    @Mock
    private GoodwillTaskService mockGoodwillTaskService;
    @Mock
    private InviteVehicleTaskService mockInviteVehicleTaskService;
    @Mock
    private InviteVehicleDoTaskService mockInviteVehicleDoTaskService;
    @Mock
    private VocManageService mockVocManageService;
    @Mock
    private InviteInsuranceVehicleTaskService mockInviteInsuranceVehicleTaskService;
    @Mock
    private InviteVehicleRecordService mockInviteVehicleRecordService;
    @Mock
    private CdpTagTaskService mockCdpTagTaskService;
    @Mock
    private CommonServiceImpl mockCommonService;
    @Mock
    private OssService mockOssService;
    @Mock
    private OssBusProService mockOssBusProService;
    @Mock
    private FaultLightService mockFaultLightService;
    @Mock
    private FaultLightClueService mockFaultLightClueService;
    @Mock
    private AccidentCluesService mockAccidentCluesService;
    @Mock
    private CompensateService mockCompensateService;
    @Mock
    private ITmClueMigrateTaskService mockClueMigrateTaskService;

    @InjectMocks
    private XxlJobHandler xxlJobHandlerUnderTest;

    @BeforeEach
    void setUp() {
        xxlJobHandlerUnderTest.complaintFollowService = mockComplaintFollowService;
        xxlJobHandlerUnderTest.goodwillTaskService = mockGoodwillTaskService;
        xxlJobHandlerUnderTest.inviteVehicleTaskService = mockInviteVehicleTaskService;
        xxlJobHandlerUnderTest.inviteVehicleDoTaskService = mockInviteVehicleDoTaskService;
        xxlJobHandlerUnderTest.vocManageService = mockVocManageService;
        xxlJobHandlerUnderTest.inviteInsuranceVehicleTaskService = mockInviteInsuranceVehicleTaskService;
        xxlJobHandlerUnderTest.inviteVehicleRecordService = mockInviteVehicleRecordService;
        xxlJobHandlerUnderTest.cdpTagTaskService = mockCdpTagTaskService;
        xxlJobHandlerUnderTest.commonService = mockCommonService;
        xxlJobHandlerUnderTest.ossService = mockOssService;
        xxlJobHandlerUnderTest.ossBusProService = mockOssBusProService;
        xxlJobHandlerUnderTest.faultLightService = mockFaultLightService;
        xxlJobHandlerUnderTest.faultLightClueService = mockFaultLightClueService;
        xxlJobHandlerUnderTest.accidentCluesService = mockAccidentCluesService;
        xxlJobHandlerUnderTest.compensateService = mockCompensateService;
    }

    @Test
    void testRemindNextFollowing_ComplaintFollowServiceReturnsNoItems() {
        // Setup
        when(mockComplaintFollowService.queryNextFollowing()).thenReturn(Collections.emptyList());

        // Run the test
        xxlJobHandlerUnderTest.remindNextFollowing();

        // Verify the results
        verify(mockCommonService).messageSendApp(any(AppPushDTO.class));
    }

    @Test
    void testGoodwillToRefuse() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.goodwillToRefuse();

        // Verify the results
        verify(mockGoodwillTaskService).goodwillToRefuse();
    }

    @Test
    void testGoodwillApplyTimeOutTask() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.goodwillApplyTimeOutTask();

        // Verify the results
        verify(mockGoodwillTaskService).goodwillApplyTimeOutTask();
    }

    @Test
    void testGoodwillInvoiceTimeOutTask() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.goodwillInvoiceTimeOutTask();

        // Verify the results
        verify(mockGoodwillTaskService).goodwillInvoiceTimeOutTask();
    }

    @Test
    void testGoodwillMatrialAuditOneTask() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.goodwillMatrialAuditOneTask();

        // Verify the results
        verify(mockGoodwillTaskService).goodwillMatrialAuditOneTask();
    }

    @Test
    void testGoodwillMatrialAuditTwoTask() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.goodwillMatrialAuditTwoTask();

        // Verify the results
        verify(mockGoodwillTaskService).goodwillMatrialAuditTwoTask();
    }

    @Test
    void testGoodwillMatrialCommitOneTask() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.goodwillMatrialCommitOneTask();

        // Verify the results
        verify(mockGoodwillTaskService).goodwillMatrialCommitOneTask();
    }

    @Test
    void testGoodwillMatrialCommitTimeOutTask() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.goodwillMatrialCommitTimeOutTask();

        // Verify the results
        verify(mockGoodwillTaskService).goodwillMatrialCommitTimeOutTask();
    }

    @Test
    void testGoodwillMatrialCommitTwoTask() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.goodwillMatrialCommitTwoTask();

        // Verify the results
        verify(mockGoodwillTaskService).goodwillMatrialCommitTwoTask();
    }

    @Test
    void testGoodwillSupplyMatrialCommitOneTask() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.goodwillSupplyMatrialCommitOneTask();

        // Verify the results
        verify(mockGoodwillTaskService).goodwillSupplyMatrialCommitOneTask();
    }

    @Test
    void testGoodwillSupplyMatrialCommitTwoTask() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.goodwillSupplyMatrialCommitTwoTask();

        // Verify the results
        verify(mockGoodwillTaskService).goodwillSupplyMatrialCommitTwoTask();
    }

    @Test
    void testCloseInvite() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.closeInvite();

        // Verify the results
        verify(mockInviteVehicleTaskService).closeInvite("createDate");
    }

    @Test
    void testInviteAutoCreate1() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.inviteAutoCreate1();

        // Verify the results
        verify(mockInviteVehicleTaskService).inviteAutoCreateTask1("createDate");
    }

    @Test
    void testInviteAutoCreate2() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.inviteAutoCreate2();

        // Verify the results
        verify(mockInviteVehicleDoTaskService).inviteAutoCreateTask("createDate", "ownerCode", "orderNo");
    }

    @Test
    void testComputeDailyAverageMileageForVoc() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.computeDailyAverageMileageForVoc();

        // Verify the results
        verify(mockVocManageService).partitionGetAllVocVeh("createDate", 0, 0, 0);
    }

    @Test
    void testComputeDailyAverageMileage() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.computeDailyAverageMileage();

        // Verify the results
        verify(mockInviteVehicleTaskService).partitionGetNotVocVeh("createDate", 0, 0, 0);
    }

    @Test
    void testComputeDailyAverageMileageForVocOld() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.computeDailyAverageMileageForVocOld();

        // Verify the results
        verify(mockVocManageService).partitionGetAllVocVehOld("createDate", 0, 0, 0);
    }

    @Test
    void testComputeDailyAverageMileageOld() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.computeDailyAverageMileageOld();

        // Verify the results
        verify(mockInviteVehicleTaskService).partitionComputeDailyAverageMileage("createDate", 0, 0, 0);
    }

    @Test
    void testCreateInviteByTask() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.createInviteByTask();

        // Verify the results
        verify(mockInviteVehicleTaskService).createInviteByTask("createDate");
    }

    @Test
    void testUpdateVoc() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.updateVoc();

        // Verify the results
        verify(mockVocManageService).updateVoc("createDate");
    }

    @Test
    void testMaintainFuseRule() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.MaintainFuseRule();

        // Verify the results
        verify(mockInviteVehicleTaskService).fuseRule();
    }

    @Test
    void testUpdateInviteVulnerable() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.updateInviteVulnerable("createDate");

        // Verify the results
        verify(mockInviteVehicleTaskService).updateInviteVulnerable("createDate");
    }

    @Test
    void testUpdateInviteFristMaintainByRuleChanged() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.updateInviteFristMaintainByRuleChanged();

        // Verify the results
        verify(mockInviteVehicleTaskService).updateInviteFristMaintainByRuleChanged("createDate");
    }

    @Test
    void testUpdateInviteMaintainByRuleChanged() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.updateInviteMaintainByRuleChanged();

        // Verify the results
        verify(mockInviteVehicleTaskService).updateInviteMaintainByRuleChanged("createDate");
    }

    @Test
    void testUpdateInviteCustomerLossByRuleChanged() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.updateInviteCustomerLossByRuleChanged();

        // Verify the results
        verify(mockInviteVehicleTaskService).updateInviteCustomerLossByRuleChanged("createDate");
    }

    @Test
    void testUpdateInviteGuaranteeByRuleChanged() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.updateInviteGuaranteeByRuleChanged();

        // Verify the results
        verify(mockInviteVehicleTaskService).updateInviteGuaranteeByRuleChanged("createDate");
    }

    @Test
    void testCreateTwiceFollow() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.createTwiceFollow();

        // Verify the results
        verify(mockInviteVehicleTaskService).createTwiceFollow("createDate");
    }

    @Test
    void testCreateInviteInsuranceByTask() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.createInviteInsuranceByTask();

        // Verify the results
        verify(mockInviteInsuranceVehicleTaskService).createInviteInsuranceByTask("ownerCode", "vin", "createDate");
    }

    @Test
    void testInsuranceTaskAutoCreate() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.insuranceTaskAutoCreate();

        // Verify the results
        verify(mockInviteInsuranceVehicleTaskService).insuranceTaskAutoCreate("createDate");
    }

    @Test
    void testUpdateInsureFollowStatus() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.updateInsureFollowStatus();

        // Verify the results
        verify(mockInviteVehicleRecordService).updateInsureFollowStatus();
    }

    @Test
    void testGetRun() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.getRun();

        // Verify the results
        verify(mockOssBusProService).run("dateTime");
    }

    @Test
    void testCheckVocAlert() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.checkVocAlert();

        // Verify the results
        verify(mockOssService).checkVocAlert();
    }

    @Test
    void testFaultLightOrderCorrelation() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.faultLightOrderCorrelation();

        // Verify the results
        verify(mockFaultLightService).faultLightOrderCorrelation();
    }

    @Test
    void testDoPendingVerificationClue() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.doPendingVerificationClue();

        // Verify the results
        verify(mockFaultLightService).doPendingVerificationClue();
    }

    @Test
    void testCheckLeadCompletionByWorkOrder() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.checkLeadCompletionByWorkOrder();

        // Verify the results
        verify(mockInviteVehicleRecordService).querySettledMaintenanceByTime("createDate");
    }

    @Test
    void testTerminateOverdueLeads() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.terminateOverdueLeads();

        // Verify the results
        verify(mockInviteVehicleRecordService).closeOverdueLeads("createDate");
    }

    @Test
    void testValidateMaintenanceLight() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.validateMaintenanceLight();

        // Verify the results
        verify(mockInviteVehicleRecordService).isValidationSuccessful();
    }

    @Test
    void testWritesCdpTagTask() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.writesCdpTagTask();

        // Verify the results
        verify(mockCdpTagTaskService).writesCdpTagTask("startTime", "endTime");
    }

    @Test
    void testDisposeCdpTagTask() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.disposeCdpTagTask();

        // Verify the results
        verify(mockCdpTagTaskService).disposeCdpTagTask("startTime", "endTime");
    }

    @Test
    void testFaultLightStatusChange() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.faultLightStatusChange();

        // Verify the results
        verify(mockFaultLightService).faultLightStatusChange();
    }

    @Test
    void testUpdateHighlightFlag() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.updateHighlightFlag();

        // Verify the results
        verify(mockFaultLightClueService).updateHighlightFlag();
    }

    @Test
    void testQueryHighlightFlag() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.queryHighlightFlag();

        // Verify the results
        verify(mockFaultLightClueService).queryHighlightFlag("createDate", "endDate");
    }

    @Test
    void testCdpUpdateReturnIntentionLevel() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.cdpUpdateReturnIntentionLevel();

        // Verify the results
        verify(mockInviteVehicleRecordService).cdpUpdateReturnIntentionLevel();
    }

    @Test
    void testDoAccidentClueFollowRemindJob() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.doAccidentClueFollowRemindJob();

        // Verify the results
        verify(mockAccidentCluesService).followRemind();
    }

    @Test
    void testDoAccidentClueAppointmentTimeOutRemind() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.doAccidentClueAppointmentTimeOutRemind();

        // Verify the results
        verify(mockAccidentCluesService).appointmentTimeOutRemind();
    }

    @Test
    void testDoUpdateExpireAccidentClues() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.doUpdateExpireAccidentClues();

        // Verify the results
        verify(mockAccidentCluesService).updateTimeOutClues();
    }

    @Test
    void testDoAccidentCluePushCompensate() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.doAccidentCluePushCompensate();

        // Verify the results
        verify(mockCompensateService).accidentCluePushCompensate();
    }

    @Test
    void testDoAccidentCluePushCompensate_CompensateServiceThrowsServiceBizException() {
        // Setup
        doThrow(ServiceBizException.class).when(mockCompensateService).accidentCluePushCompensate();

        // Run the test
        assertThatThrownBy(() -> xxlJobHandlerUnderTest.doAccidentCluePushCompensate())
                .isInstanceOf(ServiceBizException.class);
    }

    @Test
    void testDoCleanDealerToDealerTask() {
        // Setup
        // Run the test
        xxlJobHandlerUnderTest.doCleanDealerToDealerTask();

        // Verify the results
        verify(mockClueMigrateTaskService).cleanDealerToDealerTask();
    }
}
