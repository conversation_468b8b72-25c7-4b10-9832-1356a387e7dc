package com.yonyou.dmscus.customer.configuration;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedConstruction;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.ClassPathResource;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SwaggerFilterTest {

    @Test
    void shouldInterceptApiDocsRequest() throws Exception {
        // Mock 对象
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/v2/api-docs");
        MockHttpServletResponse response = new MockHttpServletResponse();
        FilterChain filterChain = mock(FilterChain.class);

        // 使用真实 swagger.json 文件测试
        SwaggerFilter filter = new SwaggerFilter();
        filter.doFilter(request, response, filterChain);

        // 验证响应
        assertEquals("application/json; charset=utf-8", response.getContentType());
        ClassPathResource resource = new ClassPathResource("swagger.json");
        try (InputStream is = resource.getInputStream()) {
            String expected = IOUtils.toString(is, String.valueOf(StandardCharsets.UTF_8));
            assertEquals(expected, response.getContentAsString());
        }
    }
}
