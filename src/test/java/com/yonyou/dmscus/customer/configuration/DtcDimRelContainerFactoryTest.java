package com.yonyou.dmscus.customer.configuration;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.autoconfigure.kafka.ConcurrentKafkaListenerContainerFactoryConfigurer;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;

@ExtendWith(MockitoExtension.class)
class DtcDimRelContainerFactoryTest {

    @Mock
    private KafkaProperties mockProperties;

    @InjectMocks
    private DtcDimRelContainerFactoryConfig dtcDimRelContainerFactoryUnderTest;


    @Test
    void testConsumerFactory() {
        // Setup
        // Run the test
        final ConsumerFactory<Object, Object> result = dtcDimRelContainerFactoryUnderTest.consumerFactory();

        // Verify the results
    }
}
