package com.yonyou.dmscus.customer.configuration;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

@ExtendWith(MockitoExtension.class)
class HttpConfigurationTest {

    @Mock
    private TokenInterceptor mockTokenInterceptor;

    @InjectMocks
    private HttpConfiguration httpConfigurationUnderTest;

    @Test
    void testRestTemplate() {
        // Setup
        // Run the test
        final RestTemplate result = httpConfigurationUnderTest.restTemplate();

        // Verify the results
    }

    @Test
    void testNoRedirectRestTemplate() {
        // Setup
        // Run the test
        final RestTemplate result = httpConfigurationUnderTest.noRedirectRestTemplate();

        // Verify the results
    }

    @Test
    void testDirectRestTemplate() {
        // Setup
        // Run the test
        final RestTemplate result = httpConfigurationUnderTest.directRestTemplate();

        // Verify the results
    }
}
