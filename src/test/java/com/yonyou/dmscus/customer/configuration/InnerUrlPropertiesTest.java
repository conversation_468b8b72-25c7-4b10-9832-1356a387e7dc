package com.yonyou.dmscus.customer.configuration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class InnerUrlPropertiesTest {

    private InnerUrlProperties innerUrlPropertiesUnderTest;

    @BeforeEach
    void setUp() {
        innerUrlPropertiesUnderTest = new InnerUrlProperties();
    }

    @Test
    void testEquals() {
        assertThat(innerUrlPropertiesUnderTest.equals("o")).isFalse();
    }

    @Test
    void testHashCode() {
        assertThat(innerUrlPropertiesUnderTest.hashCode()).isEqualTo(0);
    }
}
