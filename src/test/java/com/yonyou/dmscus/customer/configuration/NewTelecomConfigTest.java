package com.yonyou.dmscus.customer.configuration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class NewTelecomConfigTest {

    private NewTelecomConfig newTelecomConfigUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        newTelecomConfigUnderTest = new NewTelecomConfig();
    }

    @Test
    void testAppKeyGetterAndSetter() {
        final String appKey = "appKey";
        newTelecomConfigUnderTest.setAppKey(appKey);
        assertThat(newTelecomConfigUnderTest.getAppKey()).isEqualTo(appKey);
    }

    @Test
    void testSecretGetterAndSetter() {
        final String secret = "secret";
        newTelecomConfigUnderTest.setSecret(secret);
        assertThat(newTelecomConfigUnderTest.getSecret()).isEqualTo(secret);
    }

    @Test
    void testBasicUrlGetterAndSetter() {
        final String basicUrl = "basicUrl";
        newTelecomConfigUnderTest.setBasicUrl(basicUrl);
        assertThat(newTelecomConfigUnderTest.getBasicUrl()).isEqualTo(basicUrl);
    }

    @Test
    void testBindGetterAndSetter() {
        final String bind = "bind";
        newTelecomConfigUnderTest.setBind(bind);
        assertThat(newTelecomConfigUnderTest.getBind()).isEqualTo(bind);
    }

    @Test
    void testInfoGetterAndSetter() {
        final String info = "info";
        newTelecomConfigUnderTest.setInfo(info);
        assertThat(newTelecomConfigUnderTest.getInfo()).isEqualTo(info);
    }

    @Test
    void testUnbindGetterAndSetter() {
        final String unbind = "unbind";
        newTelecomConfigUnderTest.setUnbind(unbind);
        assertThat(newTelecomConfigUnderTest.getUnbind()).isEqualTo(unbind);
    }

    @Test
    void testAuthenticateGetterAndSetter() {
        final String authenticate = "authenticate";
        newTelecomConfigUnderTest.setAuthenticate(authenticate);
        assertThat(newTelecomConfigUnderTest.getAuthenticate()).isEqualTo(authenticate);
    }

    @Test
    void testBindBGetterAndSetter() {
        final String bindB = "bindB";
        newTelecomConfigUnderTest.setBindB(bindB);
        assertThat(newTelecomConfigUnderTest.getBindB()).isEqualTo(bindB);
    }

    @Test
    void testUnbindBGetterAndSetter() {
        final String unbindB = "unbindB";
        newTelecomConfigUnderTest.setUnbindB(unbindB);
        assertThat(newTelecomConfigUnderTest.getUnbindB()).isEqualTo(unbindB);
    }

    @Test
    void testToString() {
        assertThat(newTelecomConfigUnderTest.toString()).isEqualTo("result");
    }
}
