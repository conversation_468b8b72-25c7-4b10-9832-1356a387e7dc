package com.yonyou.dmscus.customer.configuration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class MidUrlPropertiesTest {

    private MidUrlProperties midUrlPropertiesUnderTest;

    @BeforeEach
    void setUp() {
        midUrlPropertiesUnderTest = new MidUrlProperties();
    }

    @Test
    void testEquals() {
        assertThat(midUrlPropertiesUnderTest.equals("o")).isFalse();
    }

    @Test
    void testHashCode() {
        assertThat(midUrlPropertiesUnderTest.hashCode()).isEqualTo(0);
    }
}
