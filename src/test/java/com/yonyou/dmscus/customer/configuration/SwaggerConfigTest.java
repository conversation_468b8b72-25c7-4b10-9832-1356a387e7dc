package com.yonyou.dmscus.customer.configuration;

import org.junit.Before;
import org.junit.Test;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import springfox.documentation.spring.web.plugins.Docket;

public class SwaggerConfigTest {

    private SwaggerConfig swaggerConfigUnderTest;

    @Before
    public void setUp() throws Exception {
        swaggerConfigUnderTest = new SwaggerConfig();
    }

    @Test
    public void testCreateRestApi() {
        // Setup
        // Run the test
        final Docket result = swaggerConfigUnderTest.createRestApi();

        // Verify the results
    }

    @Test
    public void testSwaggerFilterRegistration() {
        // Setup
        // Run the test
        final FilterRegistrationBean<SwaggerFilter> result = swaggerConfigUnderTest.swaggerFilterRegistration();

        // Verify the results
    }
}
