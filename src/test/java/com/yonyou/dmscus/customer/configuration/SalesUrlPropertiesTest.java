package com.yonyou.dmscus.customer.configuration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class SalesUrlPropertiesTest {

    private SalesUrlProperties salesUrlPropertiesUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        salesUrlPropertiesUnderTest = new SalesUrlProperties();
    }

    @Test
    void testEquals() {
        assertThat(salesUrlPropertiesUnderTest.equals("o")).isFalse();
    }

    @Test
    void testHashCode() {
        assertThat(salesUrlPropertiesUnderTest.hashCode()).isEqualTo(0);
    }
}
