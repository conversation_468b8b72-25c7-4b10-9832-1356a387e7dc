package com.yonyou.dmscus.customer.configuration;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

class XxlJobConfigTest {

    private XxlJobConfig xxlJobConfigUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        xxlJobConfigUnderTest = new XxlJobConfig();
        ReflectionTestUtils.setField(xxlJobConfigUnderTest, "adminAddresses", "adminAddresses");
        ReflectionTestUtils.setField(xxlJobConfigUnderTest, "accessToken", "accessToken");
        ReflectionTestUtils.setField(xxlJobConfigUnderTest, "appname", "appname");
        ReflectionTestUtils.setField(xxlJobConfigUnderTest, "address", "address");
        ReflectionTestUtils.setField(xxlJobConfigUnderTest, "ip", "ip");
        ReflectionTestUtils.setField(xxlJobConfigUnderTest, "port", 0);
        ReflectionTestUtils.setField(xxlJobConfigUnderTest, "logPath", "logPath");
        ReflectionTestUtils.setField(xxlJobConfigUnderTest, "logRetentionDays", 0);
    }

    @Test
    void testXxlJobExecutor() {
        // Setup
        // Run the test
        final XxlJobSpringExecutor result = xxlJobConfigUnderTest.xxlJobExecutor();

        // Verify the results
    }
}
