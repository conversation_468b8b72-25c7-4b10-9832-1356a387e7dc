package com.yonyou.dmscus.customer.configuration;

import org.apache.commons.io.input.BrokenInputStream;
import org.apache.commons.io.input.NullInputStream;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.model.StylesTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.xml.sax.SAXException;
import org.xml.sax.XMLReader;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class ExcelParserTest {

    private ExcelParser excelParserUnderTest;

    @BeforeEach
    void setUp() {
        excelParserUnderTest = new ExcelParser();
    }

    @Test
    void testParse1_BrokenStream() {
        // Setup
        final InputStream stream = new BrokenInputStream();

        // Run the test
        assertThatThrownBy(() -> excelParserUnderTest.parse(stream)).isInstanceOf(IOException.class);
    }

    @Test
    void testParse2_BrokenStream() {
        // Setup
        final InputStream stream = new BrokenInputStream();

        // Run the test
        assertThatThrownBy(() -> excelParserUnderTest.parse(stream, 0)).isInstanceOf(IOException.class);
    }

    @Test
    void testGetDatas1() {
        // Setup
        // Run the test
        final List<String[]> result = excelParserUnderTest.getDatas();

        // Verify the results
    }

    @Test
    void testGetDatas2() {
        // Setup
        // Run the test
        final List<String[]> result = excelParserUnderTest.getDatas(false);

        // Verify the results
    }


    @Test
    void testContentHandlerGetterAndSetter() {
        final ExcelParser.ISheetContentHandler contentHandler = null;
        excelParserUnderTest.setContentHandler(contentHandler);
        assertThat(excelParserUnderTest.getContentHandler()).isEqualTo(contentHandler);
    }
}
