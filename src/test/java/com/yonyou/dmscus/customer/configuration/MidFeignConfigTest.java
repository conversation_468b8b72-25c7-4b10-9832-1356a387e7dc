package com.yonyou.dmscus.customer.configuration;

import feign.RequestInterceptor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

class MidFeignConfigTest {

    private MidFeignConfig midFeignConfigUnderTest;

    @BeforeEach
    void setUp() {
        midFeignConfigUnderTest = new MidFeignConfig();
        ReflectionTestUtils.setField(midFeignConfigUnderTest, "tenant", "tenant");
    }

    @Test
    void testMidRequestInterceptor() {
        // Setup
        // Run the test
        final RequestInterceptor result = midFeignConfigUnderTest.midRequestInterceptor();

        // Verify the results
    }
}
