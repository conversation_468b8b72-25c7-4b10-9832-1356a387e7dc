package com.yonyou.dmscus.customer.configuration;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CdpRestTemplateConfigureTest {

    @Mock
    private CdpUrlProperties mockCdpUrlProperties;

    @InjectMocks
    private CdpRestTemplateConfigure cdpRestTemplateConfigureUnderTest;

    @Test
    void testCdpHttpRequestFactory() {
        // Setup
        when(mockCdpUrlProperties.getTimeout()).thenReturn(0);

        // Run the test
        final HttpComponentsClientHttpRequestFactory result = cdpRestTemplateConfigureUnderTest.cdpHttpRequestFactory();

        // Verify the results
    }

    @Test
    void testCdpRestTemplate() {
        // Setup
        when(mockCdpUrlProperties.getTimeout()).thenReturn(0);

        // Run the test
        final RestTemplate result = cdpRestTemplateConfigureUnderTest.cdpRestTemplate();

        // Verify the results
    }
}
