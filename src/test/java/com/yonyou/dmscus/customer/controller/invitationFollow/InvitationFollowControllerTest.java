package com.yonyou.dmscus.customer.controller.invitationFollow;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.service.excel.ExcelGenerator;
import com.yonyou.dmscus.customer.dto.*;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.RecommendationDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceCustomerInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.SaCustomerNumberDTO;
import com.yonyou.dmscus.customer.entity.vo.InviteVehicleRecordVo;
import com.yonyou.dmscus.customer.service.impl.invitationFollow.InviteVehicleRecordServiceImpl;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.voicemanage.SaCustomerNumberService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@ExtendWith(MockitoExtension.class)
class InvitationFollowControllerTest {

    @Mock
    private InviteVehicleRecordService mockInviteVehicleRecordService;


    @InjectMocks
    private InvitationFollowController invitationFollowController;

    @Test
    void testGetInviteVehicleRecord() throws Exception {
        // Setup
        // Configure InviteVehicleRecordService.getInviteVehicleRecord(...).
        final InvitationFollowParamsDTO inviteVehicleRecordDTO = new InvitationFollowParamsDTO();
        inviteVehicleRecordDTO.setPageSize(11L);
        inviteVehicleRecordDTO.setCurrentPage(1L);
        inviteVehicleRecordDTO.setVin("vin");
        inviteVehicleRecordDTO.setLicensePlateNum("licensePlateNum");
        inviteVehicleRecordDTO.setName("name");
        inviteVehicleRecordDTO.setSaId("saId");
        inviteVehicleRecordDTO.setSaName("saName");
        inviteVehicleRecordDTO.setIsBook(0);
        inviteVehicleRecordDTO.setRecordTypeParam(Arrays.asList(0));
        inviteVehicleRecordDTO.setPlanFollowDateStart("planFollowDateStart");
        inviteVehicleRecordDTO.setPlanFollowDateEnd("planFollowDateEnd");
        inviteVehicleRecordDTO.setActualFollowDateStart("actualFollowDateStart");
        inviteVehicleRecordDTO.setActualFollowDateEnd("actualFollowDateEnd");
        inviteVehicleRecordDTO.setAdviseInDateStart("adviseInDateStart");
        inviteVehicleRecordDTO.setAdviseInDateEnd("adviseInDateEnd");
        inviteVehicleRecordDTO.setOrderStatusParam(Arrays.asList(0));
        inviteVehicleRecordDTO.setLeaveIds(Arrays.asList(0));
        inviteVehicleRecordDTO.setCreatedAtStart("createdAtStart");
        inviteVehicleRecordDTO.setCreatedAtEnd("createdAtEnd");
        inviteVehicleRecordDTO.setIsself(0);
        inviteVehicleRecordDTO.setMonthTwice("monthTwice");
        inviteVehicleRecordDTO.setRecordType(0);
        inviteVehicleRecordDTO.setLossType(0);
        inviteVehicleRecordDTO.setLossWarningType(0);
        inviteVehicleRecordDTO.setCouponCode("couponCode");
        inviteVehicleRecordDTO.setCouponName("couponName");
        inviteVehicleRecordDTO.setBevFlag(0);
        inviteVehicleRecordDTO.setReturnIntentionLevel(0);
        when(mockInviteVehicleRecordService.getInviteVehicleRecord(any(),
                any())).thenReturn(new Page<>());

        // Run the test
        final IPage<InviteVehicleRecordDTO> result = invitationFollowController.getInviteVehicleRecord(inviteVehicleRecordDTO);

        // Verify the results
        assertThat(result).isNotNull();

    }


}
