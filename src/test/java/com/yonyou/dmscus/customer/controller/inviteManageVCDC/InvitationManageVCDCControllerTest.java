package com.yonyou.dmscus.customer.controller.inviteManageVCDC;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.yonyou.dmscus.customer.dto.InvitationFollowParamsDTO;
import com.yonyou.dmscus.customer.service.inviteManageVCDC.InviteManageVCDCService;

@ExtendWith(MockitoExtension.class)
class InvitationManageVCDCControllerTest {
	
    @Mock
    private InviteManageVCDCService inviteManageVCDCService;
    
    @InjectMocks
    private InvitationManageVCDCController invitationManageVCDCControllerTest;

    @Test
    void testExportExcelByDown() throws Exception {
        doNothing().when(inviteManageVCDCService).exportExcelByDown(any());
        invitationManageVCDCControllerTest.exportExcelByDown(new InvitationFollowParamsDTO());     
    }
}
