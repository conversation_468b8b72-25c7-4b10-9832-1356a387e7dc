package com.yonyou.dmscus.customer.enums;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

class GoodwillFileTypeEnumTest {

    @Test
    void testCode() {
        assertThat(GoodwillFileTypeEnum.PRE_APPLY.code()).isEqualTo(0);
        assertThat(GoodwillFileTypeEnum.GOODWILL_COST_STATIS.code()).isEqualTo(0);
        assertThat(GoodwillFileTypeEnum.GOODWILL_COST_IMAG.code()).isEqualTo(0);
        assertThat(GoodwillFileTypeEnum.TROUBLE_REPAIR_ORDER.code()).isEqualTo(0);
        assertThat(GoodwillFileTypeEnum.TROUBLE_REPAIR_MATERIAL.code()).isEqualTo(0);
        assertThat(GoodwillFileTypeEnum.GOODWILL_INSTALL_MATERIAL.code()).isEqualTo(0);
        assertThat(GoodwillFileTypeEnum.QK_AGREEMENT.code()).isEqualTo(0);
        assertThat(GoodwillFileTypeEnum.RETURN_CAR_MATERIAL.code()).isEqualTo(0);
        assertThat(GoodwillFileTypeEnum.MANAGER_AUDIT_VP.code()).isEqualTo(0);
        assertThat(GoodwillFileTypeEnum.MANAGER_AUDIT_CEO.code()).isEqualTo(0);
        assertThat(GoodwillFileTypeEnum.AMOUNT_UPDATE.code()).isEqualTo(0);
        assertThat(GoodwillFileTypeEnum.VCDC_ATTACH.code()).isEqualTo(0);
        assertThat(GoodwillFileTypeEnum.CUST_IDENTIFICATION.code()).isEqualTo(0);
        assertThat(GoodwillFileTypeEnum.OTHER_ATTACH.code()).isEqualTo(0);
    }

    @Test
    void testDisplay() {
        assertThat(GoodwillFileTypeEnum.PRE_APPLY.display()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.GOODWILL_COST_STATIS.display()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.GOODWILL_COST_IMAG.display()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.TROUBLE_REPAIR_ORDER.display()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.TROUBLE_REPAIR_MATERIAL.display()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.GOODWILL_INSTALL_MATERIAL.display()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.QK_AGREEMENT.display()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.RETURN_CAR_MATERIAL.display()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.MANAGER_AUDIT_VP.display()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.MANAGER_AUDIT_CEO.display()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.AMOUNT_UPDATE.display()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.VCDC_ATTACH.display()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.CUST_IDENTIFICATION.display()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.OTHER_ATTACH.display()).isEqualTo("display");
    }

    @Test
    void testFromCode() {
        assertThat(GoodwillFileTypeEnum.fromCode(0)).isEqualTo(GoodwillFileTypeEnum.PRE_APPLY);
    }

    @Test
    void testToString() {
        assertThat(GoodwillFileTypeEnum.PRE_APPLY.toString()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.GOODWILL_COST_STATIS.toString()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.GOODWILL_COST_IMAG.toString()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.TROUBLE_REPAIR_ORDER.toString()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.TROUBLE_REPAIR_MATERIAL.toString()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.GOODWILL_INSTALL_MATERIAL.toString()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.QK_AGREEMENT.toString()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.RETURN_CAR_MATERIAL.toString()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.MANAGER_AUDIT_VP.toString()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.MANAGER_AUDIT_CEO.toString()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.AMOUNT_UPDATE.toString()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.VCDC_ATTACH.toString()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.CUST_IDENTIFICATION.toString()).isEqualTo("display");
        assertThat(GoodwillFileTypeEnum.OTHER_ATTACH.toString()).isEqualTo("display");
    }

    @Test
    void testGetObjEnum() {
        // Setup
        final Map<Integer, GoodwillFileTypeEnum> expectedResult = new HashMap<>();

        // Run the test
        final Map<Integer, GoodwillFileTypeEnum> result = GoodwillFileTypeEnum.getObjEnum();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
