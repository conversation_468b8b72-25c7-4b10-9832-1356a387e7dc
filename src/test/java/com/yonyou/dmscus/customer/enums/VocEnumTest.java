package com.yonyou.dmscus.customer.enums;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class VocEnumTest {

    @Test
    void testGetStatusName1() {
        assertThat(VocEnum.NORMAL.getStatusName()).isEqualTo("statusName");
        assertThat(VocEnum.TIMEEXCEEDED.getStatusName()).isEqualTo("statusName");
        assertThat(VocEnum.ALMOSTTIMEFORSERVICE.getStatusName()).isEqualTo("statusName");
        assertThat(VocEnum.TIMEFORSERVICE.getStatusName()).isEqualTo("statusName");
    }

    @Test
    void testGetStatusValue() {
        assertThat(VocEnum.NORMAL.getStatusValue()).isEqualTo(0);
        assertThat(VocEnum.TIMEEXCEEDED.getStatusValue()).isEqualTo(0);
        assertThat(VocEnum.ALMOSTTIMEFORSERVICE.getStatusValue()).isEqualTo(0);
        assertThat(VocEnum.TIMEFORSERVICE.getStatusValue()).isEqualTo(0);
    }

    @Test
    void testGetStatusValues() {
        assertThat(VocEnum.getStatusValues("statusName")).isEqualTo(0);
    }

    @Test
    void testGetStatusName2() {
        assertThat(VocEnum.getStatusName(0)).isEqualTo("statusName");
    }
}
