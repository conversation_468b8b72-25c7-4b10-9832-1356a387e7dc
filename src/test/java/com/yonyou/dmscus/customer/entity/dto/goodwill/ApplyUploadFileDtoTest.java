package com.yonyou.dmscus.customer.entity.dto.goodwill;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class ApplyUploadFileDtoTest {

    private ApplyUploadFileDto applyUploadFileDtoUnderTest;

    @BeforeEach
    void setUp() {
        applyUploadFileDtoUnderTest = new ApplyUploadFileDto();
    }

    @Test
    void testFileTypeGetterAndSetter() {
        final Integer fileType = 0;
        applyUploadFileDtoUnderTest.setFileType(fileType);
        assertThat(applyUploadFileDtoUnderTest.getFileType()).isEqualTo(fileType);
    }

    @Test
    void testNameGetterAndSetter() {
        final String name = "name";
        applyUploadFileDtoUnderTest.setName(name);
        assertThat(applyUploadFileDtoUnderTest.getName()).isEqualTo(name);
    }

    @Test
    void testResGetterAndSetter() {
        final ApplyUploadFileDto.Res res = new ApplyUploadFileDto.Res();
        applyUploadFileDtoUnderTest.setRes(res);
        assertThat(applyUploadFileDtoUnderTest.getRes()).isEqualTo(res);
    }

    @Test
    void testBaseDateGetterAndSetter() {
        final ApplyUploadFileDto.BaseDate baseDate = new ApplyUploadFileDto.BaseDate();
        applyUploadFileDtoUnderTest.setBaseDate(baseDate);
        assertThat(applyUploadFileDtoUnderTest.getBaseDate()).isEqualTo(baseDate);
    }

    @Test
    void testUrlGetterAndSetter() {
        final String url = "url";
        applyUploadFileDtoUnderTest.setUrl(url);
        assertThat(applyUploadFileDtoUnderTest.getUrl()).isEqualTo(url);
    }
}
