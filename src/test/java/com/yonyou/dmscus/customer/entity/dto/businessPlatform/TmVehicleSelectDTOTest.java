package com.yonyou.dmscus.customer.entity.dto.businessPlatform;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class TmVehicleSelectDTOTest {

    private TmVehicleSelectDTO tmVehicleSelectDTOUnderTest;

    @BeforeEach
    void setUp() {
        tmVehicleSelectDTOUnderTest = new TmVehicleSelectDTO();
    }

    @Test
    void testVinGetterAndSetter() {
        final String vin = "vin";
        tmVehicleSelectDTOUnderTest.setVin(vin);
        assertThat(tmVehicleSelectDTOUnderTest.getVin()).isEqualTo(vin);
    }

    @Test
    void testDealerCodesGetterAndSetter() {
        final List<String> dealerCodes = Arrays.asList("value");
        tmVehicleSelectDTOUnderTest.setDealerCodes(dealerCodes);
        assertThat(tmVehicleSelectDTOUnderTest.getDealerCodes()).isEqualTo(dealerCodes);
    }

    @Test
    void testDealerNameGetterAndSetter() {
        final String dealerName = "dealerName";
        tmVehicleSelectDTOUnderTest.setDealerName(dealerName);
        assertThat(tmVehicleSelectDTOUnderTest.getDealerName()).isEqualTo(dealerName);
    }

    @Test
    void testLicenseGetterAndSetter() {
        final String license = "license";
        tmVehicleSelectDTOUnderTest.setLicense(license);
        assertThat(tmVehicleSelectDTOUnderTest.getLicense()).isEqualTo(license);
    }

    @Test
    void testNameGetterAndSetter() {
        final String name = "name";
        tmVehicleSelectDTOUnderTest.setName(name);
        assertThat(tmVehicleSelectDTOUnderTest.getName()).isEqualTo(name);
    }

    @Test
    void testPageGetterAndSetter() {
        final Integer page = 0;
        tmVehicleSelectDTOUnderTest.setPage(page);
        assertThat(tmVehicleSelectDTOUnderTest.getPage()).isEqualTo(page);
    }

    @Test
    void testSizeGetterAndSetter() {
        final Integer size = 0;
        tmVehicleSelectDTOUnderTest.setSize(size);
        assertThat(tmVehicleSelectDTOUnderTest.getSize()).isEqualTo(size);
    }
}
