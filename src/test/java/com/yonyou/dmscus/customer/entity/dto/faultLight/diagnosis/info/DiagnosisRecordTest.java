package com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.info;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

class DiagnosisRecordTest {

    private DiagnosisRecord diagnosisRecordUnderTest;

    @BeforeEach
    void setUp() {
        diagnosisRecordUnderTest = new DiagnosisRecord();
    }

    @Test
    void testEcuGetterAndSetter() {
        final String ecu = "ecu";
        diagnosisRecordUnderTest.setEcu(ecu);
        assertThat(diagnosisRecordUnderTest.getEcu()).isEqualTo(ecu);
    }

    @Test
    void testCtUploadGetterAndSetter() {
        final String ctUpload = "ctUpload";
        diagnosisRecordUnderTest.setCtUpload(ctUpload);
        assertThat(diagnosisRecordUnderTest.getCtUpload()).isEqualTo(ctUpload);
    }

    @Test
    void test_etl_timeGetterAndSetter() {
        final String _etl_time = "_etl_time";
        diagnosisRecordUnderTest.set_etl_time(_etl_time);
        assertThat(diagnosisRecordUnderTest.get_etl_time()).isEqualTo(_etl_time);
    }

    @Test
    void testDtcsGetterAndSetter() {
        final Map<String, DiagnosisDtcInfo> dtcs = new HashMap<>();
        diagnosisRecordUnderTest.setDtcs(dtcs);
        assertThat(diagnosisRecordUnderTest.getDtcs()).isEqualTo(dtcs);
    }
}
