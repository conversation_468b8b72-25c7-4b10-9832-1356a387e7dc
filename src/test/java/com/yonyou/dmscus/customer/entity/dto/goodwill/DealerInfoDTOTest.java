package com.yonyou.dmscus.customer.entity.dto.goodwill;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.within;

class DealerInfoDTOTest {

    private DealerInfoDTO dealerInfoDTOUnderTest;

    @BeforeEach
    void setUp() {
        dealerInfoDTOUnderTest = new DealerInfoDTO();
    }

    @Test
    void testAfterSmallAreaIdGetterAndSetter() {
        final Integer afterSmallAreaId = 0;
        dealerInfoDTOUnderTest.setAfterSmallAreaId(afterSmallAreaId);
        assertThat(dealerInfoDTOUnderTest.getAfterSmallAreaId()).isEqualTo(afterSmallAreaId);
    }

    @Test
    void testAfterBigAreaIdGetterAndSetter() {
        final Integer afterBigAreaId = 0;
        dealerInfoDTOUnderTest.setAfterBigAreaId(afterBigAreaId);
        assertThat(dealerInfoDTOUnderTest.getAfterBigAreaId()).isEqualTo(afterBigAreaId);
    }

    @Test
    void testOrgNameGetterAndSetter() {
        final String orgName = "orgName";
        dealerInfoDTOUnderTest.setOrgName(orgName);
        assertThat(dealerInfoDTOUnderTest.getOrgName()).isEqualTo(orgName);
    }

    @Test
    void testOrgCodeGetterAndSetter() {
        final String orgCode = "orgCode";
        dealerInfoDTOUnderTest.setOrgCode(orgCode);
        assertThat(dealerInfoDTOUnderTest.getOrgCode()).isEqualTo(orgCode);
    }

    @Test
    void testOrgIdGetterAndSetter() {
        final Integer orgId = 0;
        dealerInfoDTOUnderTest.setOrgId(orgId);
        assertThat(dealerInfoDTOUnderTest.getOrgId()).isEqualTo(orgId);
    }

    @Test
    void testIdGetterAndSetter() {
        final Integer id = 0;
        dealerInfoDTOUnderTest.setId(id);
        assertThat(dealerInfoDTOUnderTest.getId()).isEqualTo(id);
    }

    @Test
    void testCompanyCodeGetterAndSetter() {
        final String companyCode = "companyCode";
        dealerInfoDTOUnderTest.setCompanyCode(companyCode);
        assertThat(dealerInfoDTOUnderTest.getCompanyCode()).isEqualTo(companyCode);
    }

    @Test
    void testCompanyNameCnGetterAndSetter() {
        final String companyNameCn = "companyNameCn";
        dealerInfoDTOUnderTest.setCompanyNameCn(companyNameCn);
        assertThat(dealerInfoDTOUnderTest.getCompanyNameCn()).isEqualTo(companyNameCn);
    }

    @Test
    void testCompanyShortNameCnGetterAndSetter() {
        final String companyShortNameCn = "companyShortNameCn";
        dealerInfoDTOUnderTest.setCompanyShortNameCn(companyShortNameCn);
        assertThat(dealerInfoDTOUnderTest.getCompanyShortNameCn()).isEqualTo(companyShortNameCn);
    }

    @Test
    void testCompanyTypeGetterAndSetter() {
        final Integer companyType = 0;
        dealerInfoDTOUnderTest.setCompanyType(companyType);
        assertThat(dealerInfoDTOUnderTest.getCompanyType()).isEqualTo(companyType);
    }

    @Test
    void testGroupCompanyIdGetterAndSetter() {
        final String groupCompanyId = "groupCompanyId";
        dealerInfoDTOUnderTest.setGroupCompanyId(groupCompanyId);
        assertThat(dealerInfoDTOUnderTest.getGroupCompanyId()).isEqualTo(groupCompanyId);
    }

    @Test
    void testGroupCompanyNameGetterAndSetter() {
        final String groupCompanyName = "groupCompanyName";
        dealerInfoDTOUnderTest.setGroupCompanyName(groupCompanyName);
        assertThat(dealerInfoDTOUnderTest.getGroupCompanyName()).isEqualTo(groupCompanyName);
    }

    @Test
    void testProvinceIdGetterAndSetter() {
        final Integer provinceId = 0;
        dealerInfoDTOUnderTest.setProvinceId(provinceId);
        assertThat(dealerInfoDTOUnderTest.getProvinceId()).isEqualTo(provinceId);
    }

    @Test
    void testCityIdGetterAndSetter() {
        final Integer cityId = 0;
        dealerInfoDTOUnderTest.setCityId(cityId);
        assertThat(dealerInfoDTOUnderTest.getCityId()).isEqualTo(cityId);
    }

    @Test
    void testCountyIdGetterAndSetter() {
        final Integer countyId = 0;
        dealerInfoDTOUnderTest.setCountyId(countyId);
        assertThat(dealerInfoDTOUnderTest.getCountyId()).isEqualTo(countyId);
    }

    @Test
    void testAddressZhGetterAndSetter() {
        final String addressZh = "addressZh";
        dealerInfoDTOUnderTest.setAddressZh(addressZh);
        assertThat(dealerInfoDTOUnderTest.getAddressZh()).isEqualTo(addressZh);
    }

    @Test
    void testLongitudeGetterAndSetter() {
        final double longitude = 0.0;
        dealerInfoDTOUnderTest.setLongitude(longitude);
        assertThat(dealerInfoDTOUnderTest.getLongitude()).isEqualTo(longitude, within(0.0001));
    }

    @Test
    void testLatitudeGetterAndSetter() {
        final double latitude = 0.0;
        dealerInfoDTOUnderTest.setLatitude(latitude);
        assertThat(dealerInfoDTOUnderTest.getLatitude()).isEqualTo(latitude, within(0.0001));
    }

    @Test
    void testPhoneGetterAndSetter() {
        final String phone = "phone";
        dealerInfoDTOUnderTest.setPhone(phone);
        assertThat(dealerInfoDTOUnderTest.getPhone()).isEqualTo(phone);
    }

    @Test
    void testFaxGetterAndSetter() {
        final String fax = "fax";
        dealerInfoDTOUnderTest.setFax(fax);
        assertThat(dealerInfoDTOUnderTest.getFax()).isEqualTo(fax);
    }

    @Test
    void testDealerTypeGetterAndSetter() {
        final Integer dealerType = 0;
        dealerInfoDTOUnderTest.setDealerType(dealerType);
        assertThat(dealerInfoDTOUnderTest.getDealerType()).isEqualTo(dealerType);
    }

    @Test
    void testDealerScaleGetterAndSetter() {
        final Integer dealerScale = 0;
        dealerInfoDTOUnderTest.setDealerScale(dealerScale);
        assertThat(dealerInfoDTOUnderTest.getDealerScale()).isEqualTo(dealerScale);
    }

    @Test
    void testWholesaleGrantGetterAndSetter() {
        final Integer wholesaleGrant = 0;
        dealerInfoDTOUnderTest.setWholesaleGrant(wholesaleGrant);
        assertThat(dealerInfoDTOUnderTest.getWholesaleGrant()).isEqualTo(wholesaleGrant);
    }

    @Test
    void testStatusGetterAndSetter() {
        final Integer status = 0;
        dealerInfoDTOUnderTest.setStatus(status);
        assertThat(dealerInfoDTOUnderTest.getStatus()).isEqualTo(status);
    }

    @Test
    void testRemarkGetterAndSetter() {
        final String remark = "remark";
        dealerInfoDTOUnderTest.setRemark(remark);
        assertThat(dealerInfoDTOUnderTest.getRemark()).isEqualTo(remark);
    }

    @Test
    void testSalesLineGetterAndSetter() {
        final String salesLine = "salesLine";
        dealerInfoDTOUnderTest.setSalesLine(salesLine);
        assertThat(dealerInfoDTOUnderTest.getSalesLine()).isEqualTo(salesLine);
    }

    @Test
    void testSalesParmaCodeGetterAndSetter() {
        final String salesParmaCode = "salesParmaCode";
        dealerInfoDTOUnderTest.setSalesParmaCode(salesParmaCode);
        assertThat(dealerInfoDTOUnderTest.getSalesParmaCode()).isEqualTo(salesParmaCode);
    }

    @Test
    void testGradeSalesGetterAndSetter() {
        final String gradeSales = "gradeSales";
        dealerInfoDTOUnderTest.setGradeSales(gradeSales);
        assertThat(dealerInfoDTOUnderTest.getGradeSales()).isEqualTo(gradeSales);
    }

    @Test
    void testBankSalesGetterAndSetter() {
        final String bankSales = "bankSales";
        dealerInfoDTOUnderTest.setBankSales(bankSales);
        assertThat(dealerInfoDTOUnderTest.getBankSales()).isEqualTo(bankSales);
    }

    @Test
    void testBankAccountSalesGetterAndSetter() {
        final String bankAccountSales = "bankAccountSales";
        dealerInfoDTOUnderTest.setBankAccountSales(bankAccountSales);
        assertThat(dealerInfoDTOUnderTest.getBankAccountSales()).isEqualTo(bankAccountSales);
    }

    @Test
    void testTaxSalesGetterAndSetter() {
        final String taxSales = "taxSales";
        dealerInfoDTOUnderTest.setTaxSales(taxSales);
        assertThat(dealerInfoDTOUnderTest.getTaxSales()).isEqualTo(taxSales);
    }

    @Test
    void testAfterLineGetterAndSetter() {
        final String afterLine = "afterLine";
        dealerInfoDTOUnderTest.setAfterLine(afterLine);
        assertThat(dealerInfoDTOUnderTest.getAfterLine()).isEqualTo(afterLine);
    }

    @Test
    void testAfterParmaCodeGetterAndSetter() {
        final String afterParmaCode = "afterParmaCode";
        dealerInfoDTOUnderTest.setAfterParmaCode(afterParmaCode);
        assertThat(dealerInfoDTOUnderTest.getAfterParmaCode()).isEqualTo(afterParmaCode);
    }

    @Test
    void testGradeAfterGetterAndSetter() {
        final String gradeAfter = "gradeAfter";
        dealerInfoDTOUnderTest.setGradeAfter(gradeAfter);
        assertThat(dealerInfoDTOUnderTest.getGradeAfter()).isEqualTo(gradeAfter);
    }

    @Test
    void testBankAfterGetterAndSetter() {
        final String bankAfter = "bankAfter";
        dealerInfoDTOUnderTest.setBankAfter(bankAfter);
        assertThat(dealerInfoDTOUnderTest.getBankAfter()).isEqualTo(bankAfter);
    }

    @Test
    void testBankAccountAfterGetterAndSetter() {
        final String bankAccountAfter = "bankAccountAfter";
        dealerInfoDTOUnderTest.setBankAccountAfter(bankAccountAfter);
        assertThat(dealerInfoDTOUnderTest.getBankAccountAfter()).isEqualTo(bankAccountAfter);
    }

    @Test
    void testTaxAfterGetterAndSetter() {
        final String taxAfter = "taxAfter";
        dealerInfoDTOUnderTest.setTaxAfter(taxAfter);
        assertThat(dealerInfoDTOUnderTest.getTaxAfter()).isEqualTo(taxAfter);
    }

    @Test
    void testFacilityGetterAndSetter() {
        final String facility = "facility";
        dealerInfoDTOUnderTest.setFacility(facility);
        assertThat(dealerInfoDTOUnderTest.getFacility()).isEqualTo(facility);
    }

    @Test
    void testDealershipOutletGetterAndSetter() {
        final String dealershipOutlet = "dealershipOutlet";
        dealerInfoDTOUnderTest.setDealershipOutlet(dealershipOutlet);
        assertThat(dealerInfoDTOUnderTest.getDealershipOutlet()).isEqualTo(dealershipOutlet);
    }

    @Test
    void testOperationDateRdGetterAndSetter() {
        final String operationDateRd = "operationDateRd";
        dealerInfoDTOUnderTest.setOperationDateRd(operationDateRd);
        assertThat(dealerInfoDTOUnderTest.getOperationDateRd()).isEqualTo(operationDateRd);
    }

    @Test
    void testOperationDateInterentGetterAndSetter() {
        final String operationDateInterent = "operationDateInterent";
        dealerInfoDTOUnderTest.setOperationDateInterent(operationDateInterent);
        assertThat(dealerInfoDTOUnderTest.getOperationDateInterent()).isEqualTo(operationDateInterent);
    }

    @Test
    void testRelocationOrUpgradeGetterAndSetter() {
        final String relocationOrUpgrade = "relocationOrUpgrade";
        dealerInfoDTOUnderTest.setRelocationOrUpgrade(relocationOrUpgrade);
        assertThat(dealerInfoDTOUnderTest.getRelocationOrUpgrade()).isEqualTo(relocationOrUpgrade);
    }

    @Test
    void testShortDateGetterAndSetter() {
        final String shortDate = "shortDate";
        dealerInfoDTOUnderTest.setShortDate(shortDate);
        assertThat(dealerInfoDTOUnderTest.getShortDate()).isEqualTo(shortDate);
    }

    @Test
    void testVipsCodeGetterAndSetter() {
        final String vipsCode = "vipsCode";
        dealerInfoDTOUnderTest.setVipsCode(vipsCode);
        assertThat(dealerInfoDTOUnderTest.getVipsCode()).isEqualTo(vipsCode);
    }

    @Test
    void testVrCodeGetterAndSetter() {
        final String vrCode = "vrCode";
        dealerInfoDTOUnderTest.setVrCode(vrCode);
        assertThat(dealerInfoDTOUnderTest.getVrCode()).isEqualTo(vrCode);
    }

    @Test
    void testVmiLdcGetterAndSetter() {
        final Integer vmiLdc = 0;
        dealerInfoDTOUnderTest.setVmiLdc(vmiLdc);
        assertThat(dealerInfoDTOUnderTest.getVmiLdc()).isEqualTo(vmiLdc);
    }

    @Test
    void testPartWarehouseGetterAndSetter() {
        final String partWarehouse = "partWarehouse";
        dealerInfoDTOUnderTest.setPartWarehouse(partWarehouse);
        assertThat(dealerInfoDTOUnderTest.getPartWarehouse()).isEqualTo(partWarehouse);
    }

    @Test
    void testWarehouseAddressGetterAndSetter() {
        final String warehouseAddress = "warehouseAddress";
        dealerInfoDTOUnderTest.setWarehouseAddress(warehouseAddress);
        assertThat(dealerInfoDTOUnderTest.getWarehouseAddress()).isEqualTo(warehouseAddress);
    }

    @Test
    void testOpenTimeGetterAndSetter() {
        final String openTime = "openTime";
        dealerInfoDTOUnderTest.setOpenTime(openTime);
        assertThat(dealerInfoDTOUnderTest.getOpenTime()).isEqualTo(openTime);
    }

    @Test
    void testCloseTimeGetterAndSetter() {
        final String closeTime = "closeTime";
        dealerInfoDTOUnderTest.setCloseTime(closeTime);
        assertThat(dealerInfoDTOUnderTest.getCloseTime()).isEqualTo(closeTime);
    }

    @Test
    void testStorefrontPhotoUrlGetterAndSetter() {
        final String storefrontPhotoUrl = "storefrontPhotoUrl";
        dealerInfoDTOUnderTest.setStorefrontPhotoUrl(storefrontPhotoUrl);
        assertThat(dealerInfoDTOUnderTest.getStorefrontPhotoUrl()).isEqualTo(storefrontPhotoUrl);
    }

    @Test
    void testAfterSmallAreaNameGetterAndSetter() {
        final String afterSmallAreaName = "afterSmallAreaName";
        dealerInfoDTOUnderTest.setAfterSmallAreaName(afterSmallAreaName);
        assertThat(dealerInfoDTOUnderTest.getAfterSmallAreaName()).isEqualTo(afterSmallAreaName);
    }

    @Test
    void testAfterBigAreaNameGetterAndSetter() {
        final String afterBigAreaName = "afterBigAreaName";
        dealerInfoDTOUnderTest.setAfterBigAreaName(afterBigAreaName);
        assertThat(dealerInfoDTOUnderTest.getAfterBigAreaName()).isEqualTo(afterBigAreaName);
    }

    @Test
    void testProvinceNameGetterAndSetter() {
        final String provinceName = "provinceName";
        dealerInfoDTOUnderTest.setProvinceName(provinceName);
        assertThat(dealerInfoDTOUnderTest.getProvinceName()).isEqualTo(provinceName);
    }

    @Test
    void testCityNameGetterAndSetter() {
        final String cityName = "cityName";
        dealerInfoDTOUnderTest.setCityName(cityName);
        assertThat(dealerInfoDTOUnderTest.getCityName()).isEqualTo(cityName);
    }
}
