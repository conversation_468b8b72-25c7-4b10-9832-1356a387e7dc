package com.yonyou.dmscus.customer.entity.dto.common;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class WorkAxbInfoBindDtoTest {

    private WorkAxbInfoBindDto workAxbInfoBindDtoUnderTest;

    @BeforeEach
    void setUp() {
        workAxbInfoBindDtoUnderTest = new WorkAxbInfoBindDto("callNumA", "callNumB", "callNumX", "bothWay",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "oneHide", "userData", "userOrderId");
    }

    @Test
    void testBuildWorkAxbInfoList() {
        // Setup
        final List<WorkAxbInfoBindDto> expectedResult = Arrays.asList(WorkAxbInfoBindDto.builder().build());

        // Run the test
        final List<WorkAxbInfoBindDto> result = workAxbInfoBindDtoUnderTest.buildWorkAxbInfoList();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
