package com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.info;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class DiagnosisRetResultTest {

    private DiagnosisRetResult diagnosisRetResultUnderTest;

    @BeforeEach
    void setUp() {
        diagnosisRetResultUnderTest = new DiagnosisRetResult();
    }

    @Test
    void testRecordsGetterAndSetter() {
        final List<DiagnosisRecord> records = Arrays.asList(new DiagnosisRecord());
        diagnosisRetResultUnderTest.setRecords(records);
        assertThat(diagnosisRetResultUnderTest.getRecords()).isEqualTo(records);
    }
}
