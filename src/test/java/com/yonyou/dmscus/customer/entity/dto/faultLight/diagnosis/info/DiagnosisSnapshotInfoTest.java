package com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.info;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

class DiagnosisSnapshotInfoTest {

    private DiagnosisSnapshotInfo diagnosisSnapshotInfoUnderTest;

    @BeforeEach
    void setUp() {
        diagnosisSnapshotInfoUnderTest = new DiagnosisSnapshotInfo();
    }

    @Test
    void testStringsGetterAndSetter() {
        final Map<String, String> strings = new HashMap<>();
        diagnosisSnapshotInfoUnderTest.setStrings(strings);
        assertThat(diagnosisSnapshotInfoUnderTest.getStrings()).isEqualTo(strings);
    }
}
