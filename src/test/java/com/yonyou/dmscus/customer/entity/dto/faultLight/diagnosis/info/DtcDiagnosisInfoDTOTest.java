package com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.info;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class DtcDiagnosisInfoDTOTest {

    private DtcDiagnosisInfoDTO dtcDiagnosisInfoDTOUnderTest;

    @BeforeEach
    void setUp() {
        dtcDiagnosisInfoDTOUnderTest = new DtcDiagnosisInfoDTO();
    }

    @Test
    void testRetCodeGetterAndSetter() {
        final String retCode = "retCode";
        dtcDiagnosisInfoDTOUnderTest.setRetCode(retCode);
        assertThat(dtcDiagnosisInfoDTOUnderTest.getRetCode()).isEqualTo(retCode);
    }

    @Test
    void testRetInfoGetterAndSetter() {
        final String retInfo = "retInfo";
        dtcDiagnosisInfoDTOUnderTest.setRetInfo(retInfo);
        assertThat(dtcDiagnosisInfoDTOUnderTest.getRetInfo()).isEqualTo(retInfo);
    }

    @Test
    void testRetResultGetterAndSetter() {
        final DiagnosisRetResult retResult = new DiagnosisRetResult();
        dtcDiagnosisInfoDTOUnderTest.setRetResult(retResult);
        assertThat(dtcDiagnosisInfoDTOUnderTest.getRetResult()).isEqualTo(retResult);
    }
}
