package com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.info;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class OriginDtcDiagnosisResultTest {

    private OriginDtcDiagnosisResult originDtcDiagnosisResultUnderTest;

    @BeforeEach
    void setUp() {
        originDtcDiagnosisResultUnderTest = new OriginDtcDiagnosisResult();
    }

    @Test
    void testRetCodeGetterAndSetter() {
        final String retCode = "retCode";
        originDtcDiagnosisResultUnderTest.setRetCode(retCode);
        assertThat(originDtcDiagnosisResultUnderTest.getRetCode()).isEqualTo(retCode);
    }

    @Test
    void testRetInfoGetterAndSetter() {
        final String retInfo = "retInfo";
        originDtcDiagnosisResultUnderTest.setRetInfo(retInfo);
        assertThat(originDtcDiagnosisResultUnderTest.getRetInfo()).isEqualTo(retInfo);
    }
}
