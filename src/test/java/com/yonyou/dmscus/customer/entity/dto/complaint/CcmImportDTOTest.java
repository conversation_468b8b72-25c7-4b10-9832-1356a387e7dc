package com.yonyou.dmscus.customer.entity.dto.complaint;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class CcmImportDTOTest {

    private CcmImportDTO ccmImportDTOUnderTest;

    @BeforeEach
    void setUp() {
        ccmImportDTOUnderTest = new CcmImportDTO();
    }

    @Test
    void testDealerCodeGetterAndSetter() {
        final String dealerCode = "dealerCode";
        ccmImportDTOUnderTest.setDealerCode(dealerCode);
        assertThat(ccmImportDTOUnderTest.getDealerCode()).isEqualTo(dealerCode);
    }

    @Test
    void testCcmManGetterAndSetter() {
        final String ccmMan = "ccmMan";
        ccmImportDTOUnderTest.setCcmMan(ccmMan);
        assertThat(ccmImportDTOUnderTest.getCcmMan()).isEqualTo(ccmMan);
    }

    @Test
    void testLineNumberGetterAndSetter() {
        final Integer lineNumber = 0;
        ccmImportDTOUnderTest.setLineNumber(lineNumber);
        assertThat(ccmImportDTOUnderTest.getLineNumber()).isEqualTo(lineNumber);
    }

    @Test
    void testToString() {
        assertThat(ccmImportDTOUnderTest.toString()).isEqualTo("result");
    }
}
