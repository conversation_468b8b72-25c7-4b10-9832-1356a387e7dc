package com.yonyou.dmscus.customer.entity.dto.busSetting;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;

class SetMainFileItemVOTest {

    private SetMainFileItemVO setMainFileItemVOUnderTest;

    @BeforeEach
    void setUp() {
        setMainFileItemVOUnderTest = new SetMainFileItemVO();
    }

    @Test
    void testAppIdGetterAndSetter() {
        final String appId = "appId";
        setMainFileItemVOUnderTest.setAppId(appId);
        assertThat(setMainFileItemVOUnderTest.getAppId()).isEqualTo(appId);
    }

    @Test
    void testOwnerCodeGetterAndSetter() {
        final String ownerCode = "ownerCode";
        setMainFileItemVOUnderTest.setOwnerCode(ownerCode);
        assertThat(setMainFileItemVOUnderTest.getOwnerCode()).isEqualTo(ownerCode);
    }

    @Test
    void testOwnerParCodeGetterAndSetter() {
        final String ownerParCode = "ownerParCode";
        setMainFileItemVOUnderTest.setOwnerParCode(ownerParCode);
        assertThat(setMainFileItemVOUnderTest.getOwnerParCode()).isEqualTo(ownerParCode);
    }

    @Test
    void testOrgIdGetterAndSetter() {
        final Integer orgId = 0;
        setMainFileItemVOUnderTest.setOrgId(orgId);
        assertThat(setMainFileItemVOUnderTest.getOrgId()).isEqualTo(orgId);
    }

    @Test
    void testIdGetterAndSetter() {
        final Long id = 0L;
        setMainFileItemVOUnderTest.setId(id);
        assertThat(setMainFileItemVOUnderTest.getId()).isEqualTo(id);
    }

    @Test
    void testSetIdGetterAndSetter() {
        final Long setId = 0L;
        setMainFileItemVOUnderTest.setSetId(setId);
        assertThat(setMainFileItemVOUnderTest.getSetId()).isEqualTo(setId);
    }

    @Test
    void testOpCodeGetterAndSetter() {
        final String opCode = "opCode";
        setMainFileItemVOUnderTest.setOpCode(opCode);
        assertThat(setMainFileItemVOUnderTest.getOpCode()).isEqualTo(opCode);
    }

    @Test
    void testOpNameGetterAndSetter() {
        final String opName = "opName";
        setMainFileItemVOUnderTest.setOpName(opName);
        assertThat(setMainFileItemVOUnderTest.getOpName()).isEqualTo(opName);
    }

    @Test
    void testLaborHourGetterAndSetter() {
        final BigDecimal laborHour = new BigDecimal("0.00");
        setMainFileItemVOUnderTest.setLaborHour(laborHour);
        assertThat(setMainFileItemVOUnderTest.getLaborHour()).isEqualTo(laborHour);
    }

    @Test
    void testTypeGetterAndSetter() {
        final String type = "type";
        setMainFileItemVOUnderTest.setType(type);
        assertThat(setMainFileItemVOUnderTest.getType()).isEqualTo(type);
    }

    @Test
    void testDiscountGetterAndSetter() {
        final BigDecimal discount = new BigDecimal("0.00");
        setMainFileItemVOUnderTest.setDiscount(discount);
        assertThat(setMainFileItemVOUnderTest.getDiscount()).isEqualTo(discount);
    }

    @Test
    void testDataSourcesGetterAndSetter() {
        final Integer dataSources = 0;
        setMainFileItemVOUnderTest.setDataSources(dataSources);
        assertThat(setMainFileItemVOUnderTest.getDataSources()).isEqualTo(dataSources);
    }

    @Test
    void testIsDeletedGetterAndSetter() {
        final Boolean isDeleted = false;
        setMainFileItemVOUnderTest.setIsDeleted(isDeleted);
        assertThat(setMainFileItemVOUnderTest.getIsDeleted()).isFalse();
    }

    @Test
    void testIsValidGetterAndSetter() {
        final Integer isValid = 0;
        setMainFileItemVOUnderTest.setIsValid(isValid);
        assertThat(setMainFileItemVOUnderTest.getIsValid()).isEqualTo(isValid);
    }
}
