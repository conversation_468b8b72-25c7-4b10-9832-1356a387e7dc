package com.yonyou.dmscus.customer.entity.dto.businessPlatform;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.within;

class CompanyInfoDTOTest {

    private CompanyInfoDTO companyInfoDTOUnderTest;

    @BeforeEach
    void setUp() {
        companyInfoDTOUnderTest = new CompanyInfoDTO();
    }

    @Test
    void testAddressEnGetterAndSetter() {
        final String addressEn = "addressEn";
        companyInfoDTOUnderTest.setAddressEn(addressEn);
        assertThat(companyInfoDTOUnderTest.getAddressEn()).isEqualTo(addressEn);
    }

    @Test
    void testCityIdGetterAndSetter() {
        final Integer cityId = 0;
        companyInfoDTOUnderTest.setCityId(cityId);
        assertThat(companyInfoDTOUnderTest.getCityId()).isEqualTo(cityId);
    }

    @Test
    void testCompanyCodeGetterAndSetter() {
        final String companyCode = "companyCode";
        companyInfoDTOUnderTest.setCompanyCode(companyCode);
        assertThat(companyInfoDTOUnderTest.getCompanyCode()).isEqualTo(companyCode);
    }

    @Test
    void testCompanyNameCnGetterAndSetter() {
        final String companyNameCn = "companyNameCn";
        companyInfoDTOUnderTest.setCompanyNameCn(companyNameCn);
        assertThat(companyInfoDTOUnderTest.getCompanyNameCn()).isEqualTo(companyNameCn);
    }

    @Test
    void testCountyIdGetterAndSetter() {
        final Integer countyId = 0;
        companyInfoDTOUnderTest.setCountyId(countyId);
        assertThat(companyInfoDTOUnderTest.getCountyId()).isEqualTo(countyId);
    }

    @Test
    void testDealerScaleGetterAndSetter() {
        final Integer dealerScale = 0;
        companyInfoDTOUnderTest.setDealerScale(dealerScale);
        assertThat(companyInfoDTOUnderTest.getDealerScale()).isEqualTo(dealerScale);
    }

    @Test
    void testDealerTypeGetterAndSetter() {
        final Integer dealerType = 0;
        companyInfoDTOUnderTest.setDealerType(dealerType);
        assertThat(companyInfoDTOUnderTest.getDealerType()).isEqualTo(dealerType);
    }

    @Test
    void testDistanceGetterAndSetter() {
        final Double distance = 0.0;
        companyInfoDTOUnderTest.setDistance(distance);
        assertThat(companyInfoDTOUnderTest.getDistance()).isEqualTo(distance, within(0.0001));
    }

    @Test
    void testFaxGetterAndSetter() {
        final String fax = "fax";
        companyInfoDTOUnderTest.setFax(fax);
        assertThat(companyInfoDTOUnderTest.getFax()).isEqualTo(fax);
    }

    @Test
    void testGroupCompanyIdGetterAndSetter() {
        final String groupCompanyId = "groupCompanyId";
        companyInfoDTOUnderTest.setGroupCompanyId(groupCompanyId);
        assertThat(companyInfoDTOUnderTest.getGroupCompanyId()).isEqualTo(groupCompanyId);
    }

    @Test
    void testIdGetterAndSetter() {
        final Integer id = 0;
        companyInfoDTOUnderTest.setId(id);
        assertThat(companyInfoDTOUnderTest.getId()).isEqualTo(id);
    }

    @Test
    void testLatitudeGetterAndSetter() {
        final Double latitude = 0.0;
        companyInfoDTOUnderTest.setLatitude(latitude);
        assertThat(companyInfoDTOUnderTest.getLatitude()).isEqualTo(latitude, within(0.0001));
    }

    @Test
    void testLongitudeGetterAndSetter() {
        final Double longitude = 0.0;
        companyInfoDTOUnderTest.setLongitude(longitude);
        assertThat(companyInfoDTOUnderTest.getLongitude()).isEqualTo(longitude, within(0.0001));
    }

    @Test
    void testPhoneGetterAndSetter() {
        final String phone = "phone";
        companyInfoDTOUnderTest.setPhone(phone);
        assertThat(companyInfoDTOUnderTest.getPhone()).isEqualTo(phone);
    }

    @Test
    void testProvinceIdGetterAndSetter() {
        final Integer provinceId = 0;
        companyInfoDTOUnderTest.setProvinceId(provinceId);
        assertThat(companyInfoDTOUnderTest.getProvinceId()).isEqualTo(provinceId);
    }

    @Test
    void testRemarkGetterAndSetter() {
        final String remark = "remark";
        companyInfoDTOUnderTest.setRemark(remark);
        assertThat(companyInfoDTOUnderTest.getRemark()).isEqualTo(remark);
    }

    @Test
    void testStatusGetterAndSetter() {
        final Integer status = 0;
        companyInfoDTOUnderTest.setStatus(status);
        assertThat(companyInfoDTOUnderTest.getStatus()).isEqualTo(status);
    }
}
