package com.yonyou.dmscus.customer.entity.dto.complaint;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class AttcahmentUpdateDtoTest {

    private AttcahmentUpdateDto attcahmentUpdateDtoUnderTest;

    @BeforeEach
    void setUp() {
        attcahmentUpdateDtoUnderTest = new AttcahmentUpdateDto();
    }

    @Test
    void testIdGetterAndSetter() {
        final Long id = 0L;
        attcahmentUpdateDtoUnderTest.setId(id);
        assertThat(attcahmentUpdateDtoUnderTest.getId()).isEqualTo(id);
    }

    @Test
    void testNoGetterAndSetter() {
        final String no = "no";
        attcahmentUpdateDtoUnderTest.setNo(no);
        assertThat(attcahmentUpdateDtoUnderTest.getNo()).isEqualTo(no);
    }
}
