package com.yonyou.dmscus.customer.entity.dto.loss;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class LossDataRecordDtoTest {

    private LossDataRecordDto lossDataRecordDtoUnderTest;

    @BeforeEach
    void setUp() {
        lossDataRecordDtoUnderTest = new LossDataRecordDto();
    }

    @Test
    void testEquals() {
        assertThat(lossDataRecordDtoUnderTest.equals("o")).isFalse();
    }

    @Test
    void testHashCode() {
        assertThat(lossDataRecordDtoUnderTest.hashCode()).isEqualTo(0);
    }
}
