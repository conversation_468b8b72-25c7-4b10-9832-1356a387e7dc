package com.yonyou.dmscus.customer.entity.dto.businessPlatform;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.within;

class OwnerVehicleVOTest {

    private OwnerVehicleVO ownerVehicleVOUnderTest;

    @BeforeEach
    void setUp() {
        ownerVehicleVOUnderTest = new OwnerVehicleVO();
    }

    @Test
    void testIdGetterAndSetter() {
        final Long id = 0L;
        ownerVehicleVOUnderTest.setId(id);
        assertThat(ownerVehicleVOUnderTest.getId()).isEqualTo(id);
    }

    @Test
    void testCustomerIdGetterAndSetter() {
        final Long customerId = 0L;
        ownerVehicleVOUnderTest.setCustomerId(customerId);
        assertThat(ownerVehicleVOUnderTest.getCustomerId()).isEqualTo(customerId);
    }

    @Test
    void testCompanyCodeGetterAndSetter() {
        final String companyCode = "companyCode";
        ownerVehicleVOUnderTest.setCompanyCode(companyCode);
        assertThat(ownerVehicleVOUnderTest.getCompanyCode()).isEqualTo(companyCode);
    }

    @Test
    void testOwnerParCodeGetterAndSetter() {
        final String ownerParCode = "ownerParCode";
        ownerVehicleVOUnderTest.setOwnerParCode(ownerParCode);
        assertThat(ownerVehicleVOUnderTest.getOwnerParCode()).isEqualTo(ownerParCode);
    }

    @Test
    void testDealerCodeGetterAndSetter() {
        final String dealerCode = "dealerCode";
        ownerVehicleVOUnderTest.setDealerCode(dealerCode);
        assertThat(ownerVehicleVOUnderTest.getDealerCode()).isEqualTo(dealerCode);
    }

    @Test
    void testVinGetterAndSetter() {
        final String vin = "vin";
        ownerVehicleVOUnderTest.setVin(vin);
        assertThat(ownerVehicleVOUnderTest.getVin()).isEqualTo(vin);
    }

    @Test
    void testEngineNoGetterAndSetter() {
        final String engineNo = "engineNo";
        ownerVehicleVOUnderTest.setEngineNo(engineNo);
        assertThat(ownerVehicleVOUnderTest.getEngineNo()).isEqualTo(engineNo);
    }

    @Test
    void testFactoryDateGetterAndSetter() {
        final String factoryDate = "factoryDate";
        ownerVehicleVOUnderTest.setFactoryDate(factoryDate);
        assertThat(ownerVehicleVOUnderTest.getFactoryDate()).isEqualTo(factoryDate);
    }

    @Test
    void testBrandIdGetterAndSetter() {
        final Long brandId = 0L;
        ownerVehicleVOUnderTest.setBrandId(brandId);
        assertThat(ownerVehicleVOUnderTest.getBrandId()).isEqualTo(brandId);
    }

    @Test
    void testSeriesIdGetterAndSetter() {
        final Long seriesId = 0L;
        ownerVehicleVOUnderTest.setSeriesId(seriesId);
        assertThat(ownerVehicleVOUnderTest.getSeriesId()).isEqualTo(seriesId);
    }

    @Test
    void testModelIdGetterAndSetter() {
        final Long modelId = 0L;
        ownerVehicleVOUnderTest.setModelId(modelId);
        assertThat(ownerVehicleVOUnderTest.getModelId()).isEqualTo(modelId);
    }

    @Test
    void testConfigIdGetterAndSetter() {
        final Long configId = 0L;
        ownerVehicleVOUnderTest.setConfigId(configId);
        assertThat(ownerVehicleVOUnderTest.getConfigId()).isEqualTo(configId);
    }

    @Test
    void testColorIdGetterAndSetter() {
        final Long colorId = 0L;
        ownerVehicleVOUnderTest.setColorId(colorId);
        assertThat(ownerVehicleVOUnderTest.getColorId()).isEqualTo(colorId);
    }

    @Test
    void testTrimIdGetterAndSetter() {
        final Long trimId = 0L;
        ownerVehicleVOUnderTest.setTrimId(trimId);
        assertThat(ownerVehicleVOUnderTest.getTrimId()).isEqualTo(trimId);
    }

    @Test
    void testProductIdGetterAndSetter() {
        final String productId = "productId";
        ownerVehicleVOUnderTest.setProductId(productId);
        assertThat(ownerVehicleVOUnderTest.getProductId()).isEqualTo(productId);
    }

    @Test
    void testMileageGetterAndSetter() {
        final Integer mileage = 0;
        ownerVehicleVOUnderTest.setMileage(mileage);
        assertThat(ownerVehicleVOUnderTest.getMileage()).isEqualTo(mileage);
    }

    @Test
    void testInvoiceDateGetterAndSetter() {
        final String invoiceDate = "invoiceDate";
        ownerVehicleVOUnderTest.setInvoiceDate(invoiceDate);
        assertThat(ownerVehicleVOUnderTest.getInvoiceDate()).isEqualTo(invoiceDate);
    }

    @Test
    void testPlateNumberGetterAndSetter() {
        final String plateNumber = "plateNumber";
        ownerVehicleVOUnderTest.setPlateNumber(plateNumber);
        assertThat(ownerVehicleVOUnderTest.getPlateNumber()).isEqualTo(plateNumber);
    }

    @Test
    void testKeyNoGetterAndSetter() {
        final String keyNo = "keyNo";
        ownerVehicleVOUnderTest.setKeyNo(keyNo);
        assertThat(ownerVehicleVOUnderTest.getKeyNo()).isEqualTo(keyNo);
    }

    @Test
    void testProductingAreaGetterAndSetter() {
        final String productingArea = "productingArea";
        ownerVehicleVOUnderTest.setProductingArea(productingArea);
        assertThat(ownerVehicleVOUnderTest.getProductingArea()).isEqualTo(productingArea);
    }

    @Test
    void testDischargeStandardGetterAndSetter() {
        final Integer dischargeStandard = 0;
        ownerVehicleVOUnderTest.setDischargeStandard(dischargeStandard);
        assertThat(ownerVehicleVOUnderTest.getDischargeStandard()).isEqualTo(dischargeStandard);
    }

    @Test
    void testExhaustQuantityGetterAndSetter() {
        final String exhaustQuantity = "exhaustQuantity";
        ownerVehicleVOUnderTest.setExhaustQuantity(exhaustQuantity);
        assertThat(ownerVehicleVOUnderTest.getExhaustQuantity()).isEqualTo(exhaustQuantity);
    }

    @Test
    void testRemarkGetterAndSetter() {
        final String remark = "remark";
        ownerVehicleVOUnderTest.setRemark(remark);
        assertThat(ownerVehicleVOUnderTest.getRemark()).isEqualTo(remark);
    }

    @Test
    void testHasCertificateGetterAndSetter() {
        final Integer hasCertificate = 0;
        ownerVehicleVOUnderTest.setHasCertificate(hasCertificate);
        assertThat(ownerVehicleVOUnderTest.getHasCertificate()).isEqualTo(hasCertificate);
    }

    @Test
    void testCertificateNumberGetterAndSetter() {
        final String certificateNumber = "certificateNumber";
        ownerVehicleVOUnderTest.setCertificateNumber(certificateNumber);
        assertThat(ownerVehicleVOUnderTest.getCertificateNumber()).isEqualTo(certificateNumber);
    }

    @Test
    void testCertificateLocusGetterAndSetter() {
        final String certificateLocus = "certificateLocus";
        ownerVehicleVOUnderTest.setCertificateLocus(certificateLocus);
        assertThat(ownerVehicleVOUnderTest.getCertificateLocus()).isEqualTo(certificateLocus);
    }

    @Test
    void testOemTagGetterAndSetter() {
        final Integer oemTag = 0;
        ownerVehicleVOUnderTest.setOemTag(oemTag);
        assertThat(ownerVehicleVOUnderTest.getOemTag()).isEqualTo(oemTag);
    }

    @Test
    void testIsDirectGetterAndSetter() {
        final Integer isDirect = 0;
        ownerVehicleVOUnderTest.setIsDirect(isDirect);
        assertThat(ownerVehicleVOUnderTest.getIsDirect()).isEqualTo(isDirect);
    }

    @Test
    void testCollectingDealerGetterAndSetter() {
        final String collectingDealer = "collectingDealer";
        ownerVehicleVOUnderTest.setCollectingDealer(collectingDealer);
        assertThat(ownerVehicleVOUnderTest.getCollectingDealer()).isEqualTo(collectingDealer);
    }

    @Test
    void testStorageIdGetterAndSetter() {
        final String storageId = "storageId";
        ownerVehicleVOUnderTest.setStorageId(storageId);
        assertThat(ownerVehicleVOUnderTest.getStorageId()).isEqualTo(storageId);
    }

    @Test
    void testStoragePositionCodeGetterAndSetter() {
        final String storagePositionCode = "storagePositionCode";
        ownerVehicleVOUnderTest.setStoragePositionCode(storagePositionCode);
        assertThat(ownerVehicleVOUnderTest.getStoragePositionCode()).isEqualTo(storagePositionCode);
    }

    @Test
    void testOwnStockStatusGetterAndSetter() {
        final Integer ownStockStatus = 0;
        ownerVehicleVOUnderTest.setOwnStockStatus(ownStockStatus);
        assertThat(ownerVehicleVOUnderTest.getOwnStockStatus()).isEqualTo(ownStockStatus);
    }

    @Test
    void testDispatchedStatusGetterAndSetter() {
        final Integer dispatchedStatus = 0;
        ownerVehicleVOUnderTest.setDispatchedStatus(dispatchedStatus);
        assertThat(ownerVehicleVOUnderTest.getDispatchedStatus()).isEqualTo(dispatchedStatus);
    }

    @Test
    void testIsLockGetterAndSetter() {
        final Integer isLock = 0;
        ownerVehicleVOUnderTest.setIsLock(isLock);
        assertThat(ownerVehicleVOUnderTest.getIsLock()).isEqualTo(isLock);
    }

    @Test
    void testTrafficMarStatusGetterAndSetter() {
        final Integer trafficMarStatus = 0;
        ownerVehicleVOUnderTest.setTrafficMarStatus(trafficMarStatus);
        assertThat(ownerVehicleVOUnderTest.getTrafficMarStatus()).isEqualTo(trafficMarStatus);
    }

    @Test
    void testIsTestDriveGetterAndSetter() {
        final Integer isTestDrive = 0;
        ownerVehicleVOUnderTest.setIsTestDrive(isTestDrive);
        assertThat(ownerVehicleVOUnderTest.getIsTestDrive()).isEqualTo(isTestDrive);
    }

    @Test
    void testEntryTypeGetterAndSetter() {
        final Integer entryType = 0;
        ownerVehicleVOUnderTest.setEntryType(entryType);
        assertThat(ownerVehicleVOUnderTest.getEntryType()).isEqualTo(entryType);
    }

    @Test
    void testFirstStockInDateGetterAndSetter() {
        final String firstStockInDate = "firstStockInDate";
        ownerVehicleVOUnderTest.setFirstStockInDate(firstStockInDate);
        assertThat(ownerVehicleVOUnderTest.getFirstStockInDate()).isEqualTo(firstStockInDate);
    }

    @Test
    void testLastStockInByGetterAndSetter() {
        final String lastStockInBy = "lastStockInBy";
        ownerVehicleVOUnderTest.setLastStockInBy(lastStockInBy);
        assertThat(ownerVehicleVOUnderTest.getLastStockInBy()).isEqualTo(lastStockInBy);
    }

    @Test
    void testDeliveryTypeGetterAndSetter() {
        final Integer deliveryType = 0;
        ownerVehicleVOUnderTest.setDeliveryType(deliveryType);
        assertThat(ownerVehicleVOUnderTest.getDeliveryType()).isEqualTo(deliveryType);
    }

    @Test
    void testLastStockOutByGetterAndSetter() {
        final String lastStockOutBy = "lastStockOutBy";
        ownerVehicleVOUnderTest.setLastStockOutBy(lastStockOutBy);
        assertThat(ownerVehicleVOUnderTest.getLastStockOutBy()).isEqualTo(lastStockOutBy);
    }

    @Test
    void testVsnGetterAndSetter() {
        final String vsn = "vsn";
        ownerVehicleVOUnderTest.setVsn(vsn);
        assertThat(ownerVehicleVOUnderTest.getVsn()).isEqualTo(vsn);
    }

    @Test
    void testPurchasePriceGetterAndSetter() {
        final Double purchasePrice = 0.0;
        ownerVehicleVOUnderTest.setPurchasePrice(purchasePrice);
        assertThat(ownerVehicleVOUnderTest.getPurchasePrice()).isEqualTo(purchasePrice, within(0.0001));
    }

    @Test
    void testOemDirectivePriceGetterAndSetter() {
        final Double oemDirectivePrice = 0.0;
        ownerVehicleVOUnderTest.setOemDirectivePrice(oemDirectivePrice);
        assertThat(ownerVehicleVOUnderTest.getOemDirectivePrice()).isEqualTo(oemDirectivePrice, within(0.0001));
    }

    @Test
    void testDirectivePriceGetterAndSetter() {
        final Double directivePrice = 0.0;
        ownerVehicleVOUnderTest.setDirectivePrice(directivePrice);
        assertThat(ownerVehicleVOUnderTest.getDirectivePrice()).isEqualTo(directivePrice, within(0.0001));
    }

    @Test
    void testWholesaleDirectivePriceGetterAndSetter() {
        final Double wholesaleDirectivePrice = 0.0;
        ownerVehicleVOUnderTest.setWholesaleDirectivePrice(wholesaleDirectivePrice);
        assertThat(ownerVehicleVOUnderTest.getWholesaleDirectivePrice()).isEqualTo(wholesaleDirectivePrice,
                within(0.0001));
    }

    @Test
    void testIsBorrowedGetterAndSetter() {
        final Integer isBorrowed = 0;
        ownerVehicleVOUnderTest.setIsBorrowed(isBorrowed);
        assertThat(ownerVehicleVOUnderTest.getIsBorrowed()).isEqualTo(isBorrowed);
    }

    @Test
    void testWarrantyManualNoGetterAndSetter() {
        final String warrantyManualNo = "warrantyManualNo";
        ownerVehicleVOUnderTest.setWarrantyManualNo(warrantyManualNo);
        assertThat(ownerVehicleVOUnderTest.getWarrantyManualNo()).isEqualTo(warrantyManualNo);
    }

    @Test
    void testSourceSystemGetterAndSetter() {
        final String sourceSystem = "sourceSystem";
        ownerVehicleVOUnderTest.setSourceSystem(sourceSystem);
        assertThat(ownerVehicleVOUnderTest.getSourceSystem()).isEqualTo(sourceSystem);
    }

    @Test
    void testAppIdGetterAndSetter() {
        final String appId = "appId";
        ownerVehicleVOUnderTest.setAppId(appId);
        assertThat(ownerVehicleVOUnderTest.getAppId()).isEqualTo(appId);
    }

    @Test
    void testFirstDealerGetterAndSetter() {
        final String firstDealer = "firstDealer";
        ownerVehicleVOUnderTest.setFirstDealer(firstDealer);
        assertThat(ownerVehicleVOUnderTest.getFirstDealer()).isEqualTo(firstDealer);
    }

    @Test
    void testInsuranceStartDateSourceGetterAndSetter() {
        final String insuranceStartDateSource = "insuranceStartDateSource";
        ownerVehicleVOUnderTest.setInsuranceStartDateSource(insuranceStartDateSource);
        assertThat(ownerVehicleVOUnderTest.getInsuranceStartDateSource()).isEqualTo(insuranceStartDateSource);
    }

    @Test
    void testInsuranceStartDatePageCanBeEditedGetterAndSetter() {
        final Integer insuranceStartDatePageCanBeEdited = 0;
        ownerVehicleVOUnderTest.setInsuranceStartDatePageCanBeEdited(insuranceStartDatePageCanBeEdited);
        assertThat(ownerVehicleVOUnderTest.getInsuranceStartDatePageCanBeEdited())
                .isEqualTo(insuranceStartDatePageCanBeEdited);
    }

    @Test
    void testBrandCodeGetterAndSetter() {
        final String brandCode = "brandCode";
        ownerVehicleVOUnderTest.setBrandCode(brandCode);
        assertThat(ownerVehicleVOUnderTest.getBrandCode()).isEqualTo(brandCode);
    }

    @Test
    void testBrandNameGetterAndSetter() {
        final String brandName = "brandName";
        ownerVehicleVOUnderTest.setBrandName(brandName);
        assertThat(ownerVehicleVOUnderTest.getBrandName()).isEqualTo(brandName);
    }

    @Test
    void testBrandNameEnGetterAndSetter() {
        final String brandNameEn = "brandNameEn";
        ownerVehicleVOUnderTest.setBrandNameEn(brandNameEn);
        assertThat(ownerVehicleVOUnderTest.getBrandNameEn()).isEqualTo(brandNameEn);
    }

    @Test
    void testConfigCodeGetterAndSetter() {
        final String configCode = "configCode";
        ownerVehicleVOUnderTest.setConfigCode(configCode);
        assertThat(ownerVehicleVOUnderTest.getConfigCode()).isEqualTo(configCode);
    }

    @Test
    void testConfigNameGetterAndSetter() {
        final String configName = "configName";
        ownerVehicleVOUnderTest.setConfigName(configName);
        assertThat(ownerVehicleVOUnderTest.getConfigName()).isEqualTo(configName);
    }

    @Test
    void testConfigNameEnGetterAndSetter() {
        final String configNameEn = "configNameEn";
        ownerVehicleVOUnderTest.setConfigNameEn(configNameEn);
        assertThat(ownerVehicleVOUnderTest.getConfigNameEn()).isEqualTo(configNameEn);
    }

    @Test
    void testConfigYearGetterAndSetter() {
        final String configYear = "configYear";
        ownerVehicleVOUnderTest.setConfigYear(configYear);
        assertThat(ownerVehicleVOUnderTest.getConfigYear()).isEqualTo(configYear);
    }

    @Test
    void testTransMissionGetterAndSetter() {
        final String transMission = "transMission";
        ownerVehicleVOUnderTest.setTransMission(transMission);
        assertThat(ownerVehicleVOUnderTest.getTransMission()).isEqualTo(transMission);
    }

    @Test
    void testWwCarGetterAndSetter() {
        final Integer wwCar = 0;
        ownerVehicleVOUnderTest.setWwCar(wwCar);
        assertThat(ownerVehicleVOUnderTest.getWwCar()).isEqualTo(wwCar);
    }

    @Test
    void testLicenseNoGetterAndSetter() {
        final String licenseNo = "licenseNo";
        ownerVehicleVOUnderTest.setLicenseNo(licenseNo);
        assertThat(ownerVehicleVOUnderTest.getLicenseNo()).isEqualTo(licenseNo);
    }

    @Test
    void testProductDateGetterAndSetter() {
        final String productDate = "productDate";
        ownerVehicleVOUnderTest.setProductDate(productDate);
        assertThat(ownerVehicleVOUnderTest.getProductDate()).isEqualTo(productDate);
    }

    @Test
    void testLicenseDateGetterAndSetter() {
        final String licenseDate = "licenseDate";
        ownerVehicleVOUnderTest.setLicenseDate(licenseDate);
        assertThat(ownerVehicleVOUnderTest.getLicenseDate()).isEqualTo(licenseDate);
    }

    @Test
    void testSeriesNameGetterAndSetter() {
        final String seriesName = "seriesName";
        ownerVehicleVOUnderTest.setSeriesName(seriesName);
        assertThat(ownerVehicleVOUnderTest.getSeriesName()).isEqualTo(seriesName);
    }

    @Test
    void testModelNameGetterAndSetter() {
        final String modelName = "modelName";
        ownerVehicleVOUnderTest.setModelName(modelName);
        assertThat(ownerVehicleVOUnderTest.getModelName()).isEqualTo(modelName);
    }

    @Test
    void testDealerNameGetterAndSetter() {
        final String dealerName = "dealerName";
        ownerVehicleVOUnderTest.setDealerName(dealerName);
        assertThat(ownerVehicleVOUnderTest.getDealerName()).isEqualTo(dealerName);
    }

    @Test
    void testModelCodeGetterAndSetter() {
        final String modelCode = "modelCode";
        ownerVehicleVOUnderTest.setModelCode(modelCode);
        assertThat(ownerVehicleVOUnderTest.getModelCode()).isEqualTo(modelCode);
    }

    @Test
    void testModelNameEnGetterAndSetter() {
        final String modelNameEn = "modelNameEn";
        ownerVehicleVOUnderTest.setModelNameEn(modelNameEn);
        assertThat(ownerVehicleVOUnderTest.getModelNameEn()).isEqualTo(modelNameEn);
    }

    @Test
    void testFuelTypeGetterAndSetter() {
        final Integer fuelType = 0;
        ownerVehicleVOUnderTest.setFuelType(fuelType);
        assertThat(ownerVehicleVOUnderTest.getFuelType()).isEqualTo(fuelType);
    }

    @Test
    void testDataSourcesGetterAndSetter() {
        final String dataSources = "dataSources";
        ownerVehicleVOUnderTest.setDataSources(dataSources);
        assertThat(ownerVehicleVOUnderTest.getDataSources()).isEqualTo(dataSources);
    }

    @Test
    void testIsValidGetterAndSetter() {
        final Integer isValid = 0;
        ownerVehicleVOUnderTest.setIsValid(isValid);
        assertThat(ownerVehicleVOUnderTest.getIsValid()).isEqualTo(isValid);
    }

    @Test
    void testOneIdGetterAndSetter() {
        final Long oneId = 0L;
        ownerVehicleVOUnderTest.setOneId(oneId);
        assertThat(ownerVehicleVOUnderTest.getOneId()).isEqualTo(oneId);
    }

    @Test
    void testNameGetterAndSetter() {
        final String name = "name";
        ownerVehicleVOUnderTest.setName(name);
        assertThat(ownerVehicleVOUnderTest.getName()).isEqualTo(name);
    }

    @Test
    void testMobileGetterAndSetter() {
        final String mobile = "mobile";
        ownerVehicleVOUnderTest.setMobile(mobile);
        assertThat(ownerVehicleVOUnderTest.getMobile()).isEqualTo(mobile);
    }

    @Test
    void testContactNameGetterAndSetter() {
        final String contactName = "contactName";
        ownerVehicleVOUnderTest.setContactName(contactName);
        assertThat(ownerVehicleVOUnderTest.getContactName()).isEqualTo(contactName);
    }

    @Test
    void testContactorMobileGetterAndSetter() {
        final String contactorMobile = "contactorMobile";
        ownerVehicleVOUnderTest.setContactorMobile(contactorMobile);
        assertThat(ownerVehicleVOUnderTest.getContactorMobile()).isEqualTo(contactorMobile);
    }

    @Test
    void testContactorPhoneGetterAndSetter() {
        final String contactorPhone = "contactorPhone";
        ownerVehicleVOUnderTest.setContactorPhone(contactorPhone);
        assertThat(ownerVehicleVOUnderTest.getContactorPhone()).isEqualTo(contactorPhone);
    }

    @Test
    void testQqGetterAndSetter() {
        final String qq = "qq";
        ownerVehicleVOUnderTest.setQq(qq);
        assertThat(ownerVehicleVOUnderTest.getQq()).isEqualTo(qq);
    }

    @Test
    void testIndustryGetterAndSetter() {
        final Integer industry = 0;
        ownerVehicleVOUnderTest.setIndustry(industry);
        assertThat(ownerVehicleVOUnderTest.getIndustry()).isEqualTo(industry);
    }

    @Test
    void testEnterpriseTypeGetterAndSetter() {
        final Integer enterpriseType = 0;
        ownerVehicleVOUnderTest.setEnterpriseType(enterpriseType);
        assertThat(ownerVehicleVOUnderTest.getEnterpriseType()).isEqualTo(enterpriseType);
    }

    @Test
    void testLatestStockInDateGetterAndSetter() {
        final String latestStockInDate = "latestStockInDate";
        ownerVehicleVOUnderTest.setLatestStockInDate(latestStockInDate);
        assertThat(ownerVehicleVOUnderTest.getLatestStockInDate()).isEqualTo(latestStockInDate);
    }

    @Test
    void testFirstStockOutDateGetterAndSetter() {
        final String firstStockOutDate = "firstStockOutDate";
        ownerVehicleVOUnderTest.setFirstStockOutDate(firstStockOutDate);
        assertThat(ownerVehicleVOUnderTest.getFirstStockOutDate()).isEqualTo(firstStockOutDate);
    }

    @Test
    void testLatestStockOutDateGetterAndSetter() {
        final String latestStockOutDate = "latestStockOutDate";
        ownerVehicleVOUnderTest.setLatestStockOutDate(latestStockOutDate);
        assertThat(ownerVehicleVOUnderTest.getLatestStockOutDate()).isEqualTo(latestStockOutDate);
    }

    @Test
    void testPurchaseDateGetterAndSetter() {
        final String purchaseDate = "purchaseDate";
        ownerVehicleVOUnderTest.setPurchaseDate(purchaseDate);
        assertThat(ownerVehicleVOUnderTest.getPurchaseDate()).isEqualTo(purchaseDate);
    }

    @Test
    void testInsuranceStartDateGetterAndSetter() {
        final String insuranceStartDate = "insuranceStartDate";
        ownerVehicleVOUnderTest.setInsuranceStartDate(insuranceStartDate);
        assertThat(ownerVehicleVOUnderTest.getInsuranceStartDate()).isEqualTo(insuranceStartDate);
    }

    @Test
    void testOwnerReportedSalesDateGetterAndSetter() {
        final String ownerReportedSalesDate = "ownerReportedSalesDate";
        ownerVehicleVOUnderTest.setOwnerReportedSalesDate(ownerReportedSalesDate);
        assertThat(ownerVehicleVOUnderTest.getOwnerReportedSalesDate()).isEqualTo(ownerReportedSalesDate);
    }

    @Test
    void testInvoiceEndDateGetterAndSetter() {
        final String invoiceEndDate = "invoiceEndDate";
        ownerVehicleVOUnderTest.setInvoiceEndDate(invoiceEndDate);
        assertThat(ownerVehicleVOUnderTest.getInvoiceEndDate()).isEqualTo(invoiceEndDate);
    }

    @Test
    void testBargainDateGetterAndSetter() {
        final String bargainDate = "bargainDate";
        ownerVehicleVOUnderTest.setBargainDate(bargainDate);
        assertThat(ownerVehicleVOUnderTest.getBargainDate()).isEqualTo(bargainDate);
    }

    @Test
    void testCustomerTypeGetterAndSetter() {
        final Integer customerType = 0;
        ownerVehicleVOUnderTest.setCustomerType(customerType);
        assertThat(ownerVehicleVOUnderTest.getCustomerType()).isEqualTo(customerType);
    }

    @Test
    void testGenderGetterAndSetter() {
        final Integer gender = 0;
        ownerVehicleVOUnderTest.setGender(gender);
        assertThat(ownerVehicleVOUnderTest.getGender()).isEqualTo(gender);
    }

    @Test
    void testEducationGetterAndSetter() {
        final Integer education = 0;
        ownerVehicleVOUnderTest.setEducation(education);
        assertThat(ownerVehicleVOUnderTest.getEducation()).isEqualTo(education);
    }

    @Test
    void testOccupationGetterAndSetter() {
        final Integer occupation = 0;
        ownerVehicleVOUnderTest.setOccupation(occupation);
        assertThat(ownerVehicleVOUnderTest.getOccupation()).isEqualTo(occupation);
    }

    @Test
    void testBirthdayGetterAndSetter() {
        final Date birthday = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        ownerVehicleVOUnderTest.setBirthday(birthday);
        assertThat(ownerVehicleVOUnderTest.getBirthday()).isEqualTo(birthday);
    }

    @Test
    void testCtCodeGetterAndSetter() {
        final Integer ctCode = 0;
        ownerVehicleVOUnderTest.setCtCode(ctCode);
        assertThat(ownerVehicleVOUnderTest.getCtCode()).isEqualTo(ctCode);
    }

    @Test
    void testCertificateNoGetterAndSetter() {
        final String certificateNo = "certificateNo";
        ownerVehicleVOUnderTest.setCertificateNo(certificateNo);
        assertThat(ownerVehicleVOUnderTest.getCertificateNo()).isEqualTo(certificateNo);
    }

    @Test
    void testEMailGetterAndSetter() {
        final String eMail = "eMail";
        ownerVehicleVOUnderTest.seteMail(eMail);
        assertThat(ownerVehicleVOUnderTest.geteMail()).isEqualTo(eMail);
    }

    @Test
    void testAddressGetterAndSetter() {
        final String address = "address";
        ownerVehicleVOUnderTest.setAddress(address);
        assertThat(ownerVehicleVOUnderTest.getAddress()).isEqualTo(address);
    }

    @Test
    void testZipCodeGetterAndSetter() {
        final String zipCode = "zipCode";
        ownerVehicleVOUnderTest.setZipCode(zipCode);
        assertThat(ownerVehicleVOUnderTest.getZipCode()).isEqualTo(zipCode);
    }

    @Test
    void testMaritalStatusGetterAndSetter() {
        final Integer maritalStatus = 0;
        ownerVehicleVOUnderTest.setMaritalStatus(maritalStatus);
        assertThat(ownerVehicleVOUnderTest.getMaritalStatus()).isEqualTo(maritalStatus);
    }

    @Test
    void testProvinceGetterAndSetter() {
        final String province = "province";
        ownerVehicleVOUnderTest.setProvince(province);
        assertThat(ownerVehicleVOUnderTest.getProvince()).isEqualTo(province);
    }

    @Test
    void testCityGetterAndSetter() {
        final String city = "city";
        ownerVehicleVOUnderTest.setCity(city);
        assertThat(ownerVehicleVOUnderTest.getCity()).isEqualTo(city);
    }

    @Test
    void testDistrictGetterAndSetter() {
        final String district = "district";
        ownerVehicleVOUnderTest.setDistrict(district);
        assertThat(ownerVehicleVOUnderTest.getDistrict()).isEqualTo(district);
    }

    @Test
    void testCreatedDateGetterAndSetter() {
        final Date createdDate = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        ownerVehicleVOUnderTest.setCreatedDate(createdDate);
        assertThat(ownerVehicleVOUnderTest.getCreatedDate()).isEqualTo(createdDate);
    }

    @Test
    void testCompanyIdGetterAndSetter() {
        final Long companyId = 0L;
        ownerVehicleVOUnderTest.setCompanyId(companyId);
        assertThat(ownerVehicleVOUnderTest.getCompanyId()).isEqualTo(companyId);
    }

    @Test
    void testCompanyNameCnGetterAndSetter() {
        final String companyNameCn = "companyNameCn";
        ownerVehicleVOUnderTest.setCompanyNameCn(companyNameCn);
        assertThat(ownerVehicleVOUnderTest.getCompanyNameCn()).isEqualTo(companyNameCn);
    }

    @Test
    void testCompanyShortNameCnGetterAndSetter() {
        final String companyShortNameCn = "companyShortNameCn";
        ownerVehicleVOUnderTest.setCompanyShortNameCn(companyShortNameCn);
        assertThat(ownerVehicleVOUnderTest.getCompanyShortNameCn()).isEqualTo(companyShortNameCn);
    }

    @Test
    void testCompanyTypeGetterAndSetter() {
        final Integer companyType = 0;
        ownerVehicleVOUnderTest.setCompanyType(companyType);
        assertThat(ownerVehicleVOUnderTest.getCompanyType()).isEqualTo(companyType);
    }

    @Test
    void testGroupCompanyIdGetterAndSetter() {
        final String groupCompanyId = "groupCompanyId";
        ownerVehicleVOUnderTest.setGroupCompanyId(groupCompanyId);
        assertThat(ownerVehicleVOUnderTest.getGroupCompanyId()).isEqualTo(groupCompanyId);
    }

    @Test
    void testGroupCompanyNameGetterAndSetter() {
        final String groupCompanyName = "groupCompanyName";
        ownerVehicleVOUnderTest.setGroupCompanyName(groupCompanyName);
        assertThat(ownerVehicleVOUnderTest.getGroupCompanyName()).isEqualTo(groupCompanyName);
    }

    @Test
    void testOrgIdGetterAndSetter() {
        final Long orgId = 0L;
        ownerVehicleVOUnderTest.setOrgId(orgId);
        assertThat(ownerVehicleVOUnderTest.getOrgId()).isEqualTo(orgId);
    }

    @Test
    void testProvinceIdGetterAndSetter() {
        final Integer provinceId = 0;
        ownerVehicleVOUnderTest.setProvinceId(provinceId);
        assertThat(ownerVehicleVOUnderTest.getProvinceId()).isEqualTo(provinceId);
    }

    @Test
    void testCityIdGetterAndSetter() {
        final Integer cityId = 0;
        ownerVehicleVOUnderTest.setCityId(cityId);
        assertThat(ownerVehicleVOUnderTest.getCityId()).isEqualTo(cityId);
    }

    @Test
    void testCountyIdGetterAndSetter() {
        final Integer countyId = 0;
        ownerVehicleVOUnderTest.setCountyId(countyId);
        assertThat(ownerVehicleVOUnderTest.getCountyId()).isEqualTo(countyId);
    }

    @Test
    void testAddressZhGetterAndSetter() {
        final String addressZh = "addressZh";
        ownerVehicleVOUnderTest.setAddressZh(addressZh);
        assertThat(ownerVehicleVOUnderTest.getAddressZh()).isEqualTo(addressZh);
    }

    @Test
    void testLongitudeGetterAndSetter() {
        final double longitude = 0.0;
        ownerVehicleVOUnderTest.setLongitude(longitude);
        assertThat(ownerVehicleVOUnderTest.getLongitude()).isEqualTo(longitude, within(0.0001));
    }

    @Test
    void testLatitudeGetterAndSetter() {
        final double latitude = 0.0;
        ownerVehicleVOUnderTest.setLatitude(latitude);
        assertThat(ownerVehicleVOUnderTest.getLatitude()).isEqualTo(latitude, within(0.0001));
    }

    @Test
    void testPhoneGetterAndSetter() {
        final String phone = "phone";
        ownerVehicleVOUnderTest.setPhone(phone);
        assertThat(ownerVehicleVOUnderTest.getPhone()).isEqualTo(phone);
    }

    @Test
    void testFaxGetterAndSetter() {
        final String fax = "fax";
        ownerVehicleVOUnderTest.setFax(fax);
        assertThat(ownerVehicleVOUnderTest.getFax()).isEqualTo(fax);
    }

    @Test
    void testDealerTypeGetterAndSetter() {
        final Integer dealerType = 0;
        ownerVehicleVOUnderTest.setDealerType(dealerType);
        assertThat(ownerVehicleVOUnderTest.getDealerType()).isEqualTo(dealerType);
    }

    @Test
    void testDealerScaleGetterAndSetter() {
        final Integer dealerScale = 0;
        ownerVehicleVOUnderTest.setDealerScale(dealerScale);
        assertThat(ownerVehicleVOUnderTest.getDealerScale()).isEqualTo(dealerScale);
    }

    @Test
    void testWholesaleGrantGetterAndSetter() {
        final Integer wholesaleGrant = 0;
        ownerVehicleVOUnderTest.setWholesaleGrant(wholesaleGrant);
        assertThat(ownerVehicleVOUnderTest.getWholesaleGrant()).isEqualTo(wholesaleGrant);
    }

    @Test
    void testStatusGetterAndSetter() {
        final Integer status = 0;
        ownerVehicleVOUnderTest.setStatus(status);
        assertThat(ownerVehicleVOUnderTest.getStatus()).isEqualTo(status);
    }

    @Test
    void testSalesLineGetterAndSetter() {
        final String salesLine = "salesLine";
        ownerVehicleVOUnderTest.setSalesLine(salesLine);
        assertThat(ownerVehicleVOUnderTest.getSalesLine()).isEqualTo(salesLine);
    }

    @Test
    void testSalesParmaCodeGetterAndSetter() {
        final String salesParmaCode = "salesParmaCode";
        ownerVehicleVOUnderTest.setSalesParmaCode(salesParmaCode);
        assertThat(ownerVehicleVOUnderTest.getSalesParmaCode()).isEqualTo(salesParmaCode);
    }

    @Test
    void testGradeSalesGetterAndSetter() {
        final String gradeSales = "gradeSales";
        ownerVehicleVOUnderTest.setGradeSales(gradeSales);
        assertThat(ownerVehicleVOUnderTest.getGradeSales()).isEqualTo(gradeSales);
    }

    @Test
    void testBankSalesGetterAndSetter() {
        final String bankSales = "bankSales";
        ownerVehicleVOUnderTest.setBankSales(bankSales);
        assertThat(ownerVehicleVOUnderTest.getBankSales()).isEqualTo(bankSales);
    }

    @Test
    void testBankAccountSalesGetterAndSetter() {
        final String bankAccountSales = "bankAccountSales";
        ownerVehicleVOUnderTest.setBankAccountSales(bankAccountSales);
        assertThat(ownerVehicleVOUnderTest.getBankAccountSales()).isEqualTo(bankAccountSales);
    }

    @Test
    void testTaxSalesGetterAndSetter() {
        final String taxSales = "taxSales";
        ownerVehicleVOUnderTest.setTaxSales(taxSales);
        assertThat(ownerVehicleVOUnderTest.getTaxSales()).isEqualTo(taxSales);
    }

    @Test
    void testAfterLineGetterAndSetter() {
        final String afterLine = "afterLine";
        ownerVehicleVOUnderTest.setAfterLine(afterLine);
        assertThat(ownerVehicleVOUnderTest.getAfterLine()).isEqualTo(afterLine);
    }

    @Test
    void testAfterParmaCodeGetterAndSetter() {
        final String afterParmaCode = "afterParmaCode";
        ownerVehicleVOUnderTest.setAfterParmaCode(afterParmaCode);
        assertThat(ownerVehicleVOUnderTest.getAfterParmaCode()).isEqualTo(afterParmaCode);
    }

    @Test
    void testGradeAfterGetterAndSetter() {
        final String gradeAfter = "gradeAfter";
        ownerVehicleVOUnderTest.setGradeAfter(gradeAfter);
        assertThat(ownerVehicleVOUnderTest.getGradeAfter()).isEqualTo(gradeAfter);
    }

    @Test
    void testBankAfterGetterAndSetter() {
        final String bankAfter = "bankAfter";
        ownerVehicleVOUnderTest.setBankAfter(bankAfter);
        assertThat(ownerVehicleVOUnderTest.getBankAfter()).isEqualTo(bankAfter);
    }

    @Test
    void testBankAccountAfterGetterAndSetter() {
        final String bankAccountAfter = "bankAccountAfter";
        ownerVehicleVOUnderTest.setBankAccountAfter(bankAccountAfter);
        assertThat(ownerVehicleVOUnderTest.getBankAccountAfter()).isEqualTo(bankAccountAfter);
    }

    @Test
    void testTaxAfterGetterAndSetter() {
        final String taxAfter = "taxAfter";
        ownerVehicleVOUnderTest.setTaxAfter(taxAfter);
        assertThat(ownerVehicleVOUnderTest.getTaxAfter()).isEqualTo(taxAfter);
    }

    @Test
    void testFacilityGetterAndSetter() {
        final String facility = "facility";
        ownerVehicleVOUnderTest.setFacility(facility);
        assertThat(ownerVehicleVOUnderTest.getFacility()).isEqualTo(facility);
    }

    @Test
    void testDealershipOutletGetterAndSetter() {
        final String dealershipOutlet = "dealershipOutlet";
        ownerVehicleVOUnderTest.setDealershipOutlet(dealershipOutlet);
        assertThat(ownerVehicleVOUnderTest.getDealershipOutlet()).isEqualTo(dealershipOutlet);
    }

    @Test
    void testOperationDateDnGetterAndSetter() {
        final String operationDateDn = "operationDateDn";
        ownerVehicleVOUnderTest.setOperationDateDn(operationDateDn);
        assertThat(ownerVehicleVOUnderTest.getOperationDateDn()).isEqualTo(operationDateDn);
    }

    @Test
    void testOperationDateRdGetterAndSetter() {
        final String operationDateRd = "operationDateRd";
        ownerVehicleVOUnderTest.setOperationDateRd(operationDateRd);
        assertThat(ownerVehicleVOUnderTest.getOperationDateRd()).isEqualTo(operationDateRd);
    }

    @Test
    void testOperationDateInterentGetterAndSetter() {
        final String operationDateInterent = "operationDateInterent";
        ownerVehicleVOUnderTest.setOperationDateInterent(operationDateInterent);
        assertThat(ownerVehicleVOUnderTest.getOperationDateInterent()).isEqualTo(operationDateInterent);
    }

    @Test
    void testRelocationOrUpgradeGetterAndSetter() {
        final String relocationOrUpgrade = "relocationOrUpgrade";
        ownerVehicleVOUnderTest.setRelocationOrUpgrade(relocationOrUpgrade);
        assertThat(ownerVehicleVOUnderTest.getRelocationOrUpgrade()).isEqualTo(relocationOrUpgrade);
    }

    @Test
    void testShortDateGetterAndSetter() {
        final String shortDate = "shortDate";
        ownerVehicleVOUnderTest.setShortDate(shortDate);
        assertThat(ownerVehicleVOUnderTest.getShortDate()).isEqualTo(shortDate);
    }

    @Test
    void testVipsCodeGetterAndSetter() {
        final String vipsCode = "vipsCode";
        ownerVehicleVOUnderTest.setVipsCode(vipsCode);
        assertThat(ownerVehicleVOUnderTest.getVipsCode()).isEqualTo(vipsCode);
    }

    @Test
    void testVrCodeGetterAndSetter() {
        final String vrCode = "vrCode";
        ownerVehicleVOUnderTest.setVrCode(vrCode);
        assertThat(ownerVehicleVOUnderTest.getVrCode()).isEqualTo(vrCode);
    }

    @Test
    void testVmiLdcGetterAndSetter() {
        final Integer vmiLdc = 0;
        ownerVehicleVOUnderTest.setVmiLdc(vmiLdc);
        assertThat(ownerVehicleVOUnderTest.getVmiLdc()).isEqualTo(vmiLdc);
    }

    @Test
    void testPartWarehouseGetterAndSetter() {
        final String partWarehouse = "partWarehouse";
        ownerVehicleVOUnderTest.setPartWarehouse(partWarehouse);
        assertThat(ownerVehicleVOUnderTest.getPartWarehouse()).isEqualTo(partWarehouse);
    }

    @Test
    void testWarehouseAddressGetterAndSetter() {
        final String warehouseAddress = "warehouseAddress";
        ownerVehicleVOUnderTest.setWarehouseAddress(warehouseAddress);
        assertThat(ownerVehicleVOUnderTest.getWarehouseAddress()).isEqualTo(warehouseAddress);
    }

    @Test
    void testOpenTimeGetterAndSetter() {
        final String openTime = "openTime";
        ownerVehicleVOUnderTest.setOpenTime(openTime);
        assertThat(ownerVehicleVOUnderTest.getOpenTime()).isEqualTo(openTime);
    }

    @Test
    void testCloseTimeGetterAndSetter() {
        final String closeTime = "closeTime";
        ownerVehicleVOUnderTest.setCloseTime(closeTime);
        assertThat(ownerVehicleVOUnderTest.getCloseTime()).isEqualTo(closeTime);
    }

    @Test
    void testStorefrontPhotoUrlGetterAndSetter() {
        final String storefrontPhotoUrl = "storefrontPhotoUrl";
        ownerVehicleVOUnderTest.setStorefrontPhotoUrl(storefrontPhotoUrl);
        assertThat(ownerVehicleVOUnderTest.getStorefrontPhotoUrl()).isEqualTo(storefrontPhotoUrl);
    }

    @Test
    void testAfterBigAreaIdGetterAndSetter() {
        final Long afterBigAreaId = 0L;
        ownerVehicleVOUnderTest.setAfterBigAreaId(afterBigAreaId);
        assertThat(ownerVehicleVOUnderTest.getAfterBigAreaId()).isEqualTo(afterBigAreaId);
    }

    @Test
    void testAfterBigAreaNameGetterAndSetter() {
        final String afterBigAreaName = "afterBigAreaName";
        ownerVehicleVOUnderTest.setAfterBigAreaName(afterBigAreaName);
        assertThat(ownerVehicleVOUnderTest.getAfterBigAreaName()).isEqualTo(afterBigAreaName);
    }

    @Test
    void testAfterSmallAreaIdGetterAndSetter() {
        final Long afterSmallAreaId = 0L;
        ownerVehicleVOUnderTest.setAfterSmallAreaId(afterSmallAreaId);
        assertThat(ownerVehicleVOUnderTest.getAfterSmallAreaId()).isEqualTo(afterSmallAreaId);
    }

    @Test
    void testAfterSmallAreaNameGetterAndSetter() {
        final String afterSmallAreaName = "afterSmallAreaName";
        ownerVehicleVOUnderTest.setAfterSmallAreaName(afterSmallAreaName);
        assertThat(ownerVehicleVOUnderTest.getAfterSmallAreaName()).isEqualTo(afterSmallAreaName);
    }

    @Test
    void testSaleBigAreaIdGetterAndSetter() {
        final Long saleBigAreaId = 0L;
        ownerVehicleVOUnderTest.setSaleBigAreaId(saleBigAreaId);
        assertThat(ownerVehicleVOUnderTest.getSaleBigAreaId()).isEqualTo(saleBigAreaId);
    }

    @Test
    void testSaleBigAreaNameGetterAndSetter() {
        final String saleBigAreaName = "saleBigAreaName";
        ownerVehicleVOUnderTest.setSaleBigAreaName(saleBigAreaName);
        assertThat(ownerVehicleVOUnderTest.getSaleBigAreaName()).isEqualTo(saleBigAreaName);
    }

    @Test
    void testSaleSmallAreaIdGetterAndSetter() {
        final Long saleSmallAreaId = 0L;
        ownerVehicleVOUnderTest.setSaleSmallAreaId(saleSmallAreaId);
        assertThat(ownerVehicleVOUnderTest.getSaleSmallAreaId()).isEqualTo(saleSmallAreaId);
    }

    @Test
    void testSaleSmallAreaNameGetterAndSetter() {
        final String saleSmallAreaName = "saleSmallAreaName";
        ownerVehicleVOUnderTest.setSaleSmallAreaName(saleSmallAreaName);
        assertThat(ownerVehicleVOUnderTest.getSaleSmallAreaName()).isEqualTo(saleSmallAreaName);
    }

    @Test
    void testWeChatGetterAndSetter() {
        final String weChat = "weChat";
        ownerVehicleVOUnderTest.setWeChat(weChat);
        assertThat(ownerVehicleVOUnderTest.getWeChat()).isEqualTo(weChat);
    }

    @Test
    void testExtendWarrantyNameGetterAndSetter() {
        final String extendWarrantyName = "extendWarrantyName";
        ownerVehicleVOUnderTest.setExtendWarrantyName(extendWarrantyName);
        assertThat(ownerVehicleVOUnderTest.getExtendWarrantyName()).isEqualTo(extendWarrantyName);
    }

    @Test
    void testIsExtendWarrantyGetterAndSetter() {
        final Integer isExtendWarranty = 0;
        ownerVehicleVOUnderTest.setIsExtendWarranty(isExtendWarranty);
        assertThat(ownerVehicleVOUnderTest.getIsExtendWarranty()).isEqualTo(isExtendWarranty);
    }

    @Test
    void testExtendWarrantyStartDateGetterAndSetter() {
        final String extendWarrantyStartDate = "extendWarrantyStartDate";
        ownerVehicleVOUnderTest.setExtendWarrantyStartDate(extendWarrantyStartDate);
        assertThat(ownerVehicleVOUnderTest.getExtendWarrantyStartDate()).isEqualTo(extendWarrantyStartDate);
    }

    @Test
    void testExtendWarrantyEndDateGetterAndSetter() {
        final String extendWarrantyEndDate = "extendWarrantyEndDate";
        ownerVehicleVOUnderTest.setExtendWarrantyEndDate(extendWarrantyEndDate);
        assertThat(ownerVehicleVOUnderTest.getExtendWarrantyEndDate()).isEqualTo(extendWarrantyEndDate);
    }
}
