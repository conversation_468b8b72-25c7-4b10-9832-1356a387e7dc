package com.yonyou.dmscus.customer.entity.dto.busSetting;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;

class SetMainFilePartVOTest {

    private SetMainFilePartVO setMainFilePartVOUnderTest;

    @BeforeEach
    void setUp() {
        setMainFilePartVOUnderTest = new SetMainFilePartVO();
    }

    @Test
    void testAppIdGetterAndSetter() {
        final String appId = "appId";
        setMainFilePartVOUnderTest.setAppId(appId);
        assertThat(setMainFilePartVOUnderTest.getAppId()).isEqualTo(appId);
    }

    @Test
    void testOwnerCodeGetterAndSetter() {
        final String ownerCode = "ownerCode";
        setMainFilePartVOUnderTest.setOwnerCode(ownerCode);
        assertThat(setMainFilePartVOUnderTest.getOwnerCode()).isEqualTo(ownerCode);
    }

    @Test
    void testOwnerParCodeGetterAndSetter() {
        final String ownerParCode = "ownerParCode";
        setMainFilePartVOUnderTest.setOwnerParCode(ownerParCode);
        assertThat(setMainFilePartVOUnderTest.getOwnerParCode()).isEqualTo(ownerParCode);
    }

    @Test
    void testOrgIdGetterAndSetter() {
        final Integer orgId = 0;
        setMainFilePartVOUnderTest.setOrgId(orgId);
        assertThat(setMainFilePartVOUnderTest.getOrgId()).isEqualTo(orgId);
    }

    @Test
    void testIdGetterAndSetter() {
        final Long id = 0L;
        setMainFilePartVOUnderTest.setId(id);
        assertThat(setMainFilePartVOUnderTest.getId()).isEqualTo(id);
    }

    @Test
    void testSetIdGetterAndSetter() {
        final Long setId = 0L;
        setMainFilePartVOUnderTest.setSetId(setId);
        assertThat(setMainFilePartVOUnderTest.getSetId()).isEqualTo(setId);
    }

    @Test
    void testOpNameGetterAndSetter() {
        final String opName = "opName";
        setMainFilePartVOUnderTest.setOpName(opName);
        assertThat(setMainFilePartVOUnderTest.getOpName()).isEqualTo(opName);
    }

    @Test
    void testOpCodeGetterAndSetter() {
        final String opCode = "opCode";
        setMainFilePartVOUnderTest.setOpCode(opCode);
        assertThat(setMainFilePartVOUnderTest.getOpCode()).isEqualTo(opCode);
    }

    @Test
    void testPartNameGetterAndSetter() {
        final String partName = "partName";
        setMainFilePartVOUnderTest.setPartName(partName);
        assertThat(setMainFilePartVOUnderTest.getPartName()).isEqualTo(partName);
    }

    @Test
    void testPartCodeGetterAndSetter() {
        final String partCode = "partCode";
        setMainFilePartVOUnderTest.setPartCode(partCode);
        assertThat(setMainFilePartVOUnderTest.getPartCode()).isEqualTo(partCode);
    }

    @Test
    void testQuantityGetterAndSetter() {
        final BigDecimal quantity = new BigDecimal("0.00");
        setMainFilePartVOUnderTest.setQuantity(quantity);
        assertThat(setMainFilePartVOUnderTest.getQuantity()).isEqualTo(quantity);
    }

    @Test
    void testTypeGetterAndSetter() {
        final String type = "type";
        setMainFilePartVOUnderTest.setType(type);
        assertThat(setMainFilePartVOUnderTest.getType()).isEqualTo(type);
    }

    @Test
    void testDiscountGetterAndSetter() {
        final BigDecimal discount = new BigDecimal("0.00");
        setMainFilePartVOUnderTest.setDiscount(discount);
        assertThat(setMainFilePartVOUnderTest.getDiscount()).isEqualTo(discount);
    }

    @Test
    void testIsUpgradeGetterAndSetter() {
        final Integer isUpgrade = 0;
        setMainFilePartVOUnderTest.setIsUpgrade(isUpgrade);
        assertThat(setMainFilePartVOUnderTest.getIsUpgrade()).isEqualTo(isUpgrade);
    }

    @Test
    void testUpgradePartGetterAndSetter() {
        final String upgradePart = "upgradePart";
        setMainFilePartVOUnderTest.setUpgradePart(upgradePart);
        assertThat(setMainFilePartVOUnderTest.getUpgradePart()).isEqualTo(upgradePart);
    }

    @Test
    void testDataSourcesGetterAndSetter() {
        final Integer dataSources = 0;
        setMainFilePartVOUnderTest.setDataSources(dataSources);
        assertThat(setMainFilePartVOUnderTest.getDataSources()).isEqualTo(dataSources);
    }

    @Test
    void testIsDeletedGetterAndSetter() {
        final Boolean isDeleted = false;
        setMainFilePartVOUnderTest.setIsDeleted(isDeleted);
        assertThat(setMainFilePartVOUnderTest.getIsDeleted()).isFalse();
    }

    @Test
    void testIsValidGetterAndSetter() {
        final Integer isValid = 0;
        setMainFilePartVOUnderTest.setIsValid(isValid);
        assertThat(setMainFilePartVOUnderTest.getIsValid()).isEqualTo(isValid);
    }

    @Test
    void testUpgradeFgGetterAndSetter() {
        final String upgradeFg = "upgradeFg";
        setMainFilePartVOUnderTest.setUpgradeFg(upgradeFg);
        assertThat(setMainFilePartVOUnderTest.getUpgradeFg()).isEqualTo(upgradeFg);
    }

    @Test
    void testFunctionCodeGetterAndSetter() {
        final String functionCode = "functionCode";
        setMainFilePartVOUnderTest.setFunctionCode(functionCode);
        assertThat(setMainFilePartVOUnderTest.getFunctionCode()).isEqualTo(functionCode);
    }
}
