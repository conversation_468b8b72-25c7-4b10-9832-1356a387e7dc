package com.yonyou.dmscus.customer.entity.dto.faultLight.diagnosis.info;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

class DiagnosisDtcInfoTest {

    private DiagnosisDtcInfo diagnosisDtcInfoUnderTest;

    @BeforeEach
    void setUp() {
        diagnosisDtcInfoUnderTest = new DiagnosisDtcInfo();
    }

    @Test
    void testSnapshotsGetterAndSetter() {
        final Map<String, DiagnosisSnapshotInfo> snapshots = new HashMap<>();
        diagnosisDtcInfoUnderTest.setSnapshots(snapshots);
        assertThat(diagnosisDtcInfoUnderTest.getSnapshots()).isEqualTo(snapshots);
    }

    @Test
    void testStatusByExtGetterAndSetter() {
        final Map<String, String> statusByExt = new HashMap<>();
        diagnosisDtcInfoUnderTest.setStatusByExt(statusByExt);
        assertThat(diagnosisDtcInfoUnderTest.getStatusByExt()).isEqualTo(statusByExt);
    }

    @Test
    void testStringsGetterAndSetter() {
        final Map<String, String> strings = new HashMap<>();
        diagnosisDtcInfoUnderTest.setStrings(strings);
        assertThat(diagnosisDtcInfoUnderTest.getStrings()).isEqualTo(strings);
    }

    @Test
    void testStatusIndicatorGetterAndSetter() {
        final Map<String, String> statusIndicator = new HashMap<>();
        diagnosisDtcInfoUnderTest.setStatusIndicator(statusIndicator);
        assertThat(diagnosisDtcInfoUnderTest.getStatusIndicator()).isEqualTo(statusIndicator);
    }

    @Test
    void testDoublesGetterAndSetter() {
        final Map<String, String> doubles = new HashMap<>();
        diagnosisDtcInfoUnderTest.setDoubles(doubles);
        assertThat(diagnosisDtcInfoUnderTest.getDoubles()).isEqualTo(doubles);
    }

    @Test
    void testStatusByMaskGetterAndSetter() {
        final Map<String, String> statusByMask = new HashMap<>();
        diagnosisDtcInfoUnderTest.setStatusByMask(statusByMask);
        assertThat(diagnosisDtcInfoUnderTest.getStatusByMask()).isEqualTo(statusByMask);
    }

    @Test
    void testSnapshotsStringsGetterAndSetter() {
        final Map<String, String> snapshotsStrings = new HashMap<>();
        diagnosisDtcInfoUnderTest.setSnapshotsStrings(snapshotsStrings);
        assertThat(diagnosisDtcInfoUnderTest.getSnapshotsStrings()).isEqualTo(snapshotsStrings);
    }
}
