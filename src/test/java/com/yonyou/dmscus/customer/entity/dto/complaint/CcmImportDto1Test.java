package com.yonyou.dmscus.customer.entity.dto.complaint;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class CcmImportDto1Test {

    private CcmImportDto1 ccmImportDto1UnderTest;

    @BeforeEach
    void setUp() {
        ccmImportDto1UnderTest = new CcmImportDto1();
    }

    @Test
    void testCcmManIdGetterAndSetter() {
        final Long ccmManId = 0L;
        ccmImportDto1UnderTest.setCcmManId(ccmManId);
        assertThat(ccmImportDto1UnderTest.getCcmManId()).isEqualTo(ccmManId);
    }

    @Test
    void testAfterBigAreaIdGetterAndSetter() {
        final Long afterBigAreaId = 0L;
        ccmImportDto1UnderTest.setAfterBigAreaId(afterBigAreaId);
        assertThat(ccmImportDto1UnderTest.getAfterBigAreaId()).isEqualTo(afterBigAreaId);
    }

    @Test
    void testAfterSmallAreaIdGetterAndSetter() {
        final Long afterSmallAreaId = 0L;
        ccmImportDto1UnderTest.setAfterSmallAreaId(afterSmallAreaId);
        assertThat(ccmImportDto1UnderTest.getAfterSmallAreaId()).isEqualTo(afterSmallAreaId);
    }

    @Test
    void testGroupCompanyNameGetterAndSetter() {
        final String groupCompanyName = "groupCompanyName";
        ccmImportDto1UnderTest.setGroupCompanyName(groupCompanyName);
        assertThat(ccmImportDto1UnderTest.getGroupCompanyName()).isEqualTo(groupCompanyName);
    }

    @Test
    void testAfterSmallAreaNameGetterAndSetter() {
        final String afterSmallAreaName = "afterSmallAreaName";
        ccmImportDto1UnderTest.setAfterSmallAreaName(afterSmallAreaName);
        assertThat(ccmImportDto1UnderTest.getAfterSmallAreaName()).isEqualTo(afterSmallAreaName);
    }

    @Test
    void testAfterBigAreaNameGetterAndSetter() {
        final String afterBigAreaName = "afterBigAreaName";
        ccmImportDto1UnderTest.setAfterBigAreaName(afterBigAreaName);
        assertThat(ccmImportDto1UnderTest.getAfterBigAreaName()).isEqualTo(afterBigAreaName);
    }

    @Test
    void testDealerNameGetterAndSetter() {
        final String dealerName = "dealerName";
        ccmImportDto1UnderTest.setDealerName(dealerName);
        assertThat(ccmImportDto1UnderTest.getDealerName()).isEqualTo(dealerName);
    }

    @Test
    void testIdGetterAndSetter() {
        final Long id = 0L;
        ccmImportDto1UnderTest.setId(id);
        assertThat(ccmImportDto1UnderTest.getId()).isEqualTo(id);
    }

    @Test
    void testUserIdGetterAndSetter() {
        final Long userId = 0L;
        ccmImportDto1UnderTest.setUserId(userId);
        assertThat(ccmImportDto1UnderTest.getUserId()).isEqualTo(userId);
    }
}
