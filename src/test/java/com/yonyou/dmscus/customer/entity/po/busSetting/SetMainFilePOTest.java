package com.yonyou.dmscus.customer.entity.po.busSetting;

import com.yonyou.dmscloud.framework.base.dto.BaseDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import static org.assertj.core.api.Assertions.assertThat;

class SetMainFilePOTest {

    private SetMainFilePO setMainFilePOUnderTest;

    @BeforeEach
    void setUp() {
        setMainFilePOUnderTest = new SetMainFilePO();
    }

    @Test
    void testAccountGroupGetterAndSetter() {
        final String accountGroup = "accountGroup";
        setMainFilePOUnderTest.setAccountGroup(accountGroup);
        assertThat(setMainFilePOUnderTest.getAccountGroup()).isEqualTo(accountGroup);
    }

    @Test
    void testAppIdGetterAndSetter() {
        final String appId = "appId";
        setMainFilePOUnderTest.setAppId(appId);
        assertThat(setMainFilePOUnderTest.getAppId()).isEqualTo(appId);
    }

    @Test
    void testOwnerCodeGetterAndSetter() {
        final String ownerCode = "ownerCode";
        setMainFilePOUnderTest.setOwnerCode(ownerCode);
        assertThat(setMainFilePOUnderTest.getOwnerCode()).isEqualTo(ownerCode);
    }

    @Test
    void testOwnerParCodeGetterAndSetter() {
        final String ownerParCode = "ownerParCode";
        setMainFilePOUnderTest.setOwnerParCode(ownerParCode);
        assertThat(setMainFilePOUnderTest.getOwnerParCode()).isEqualTo(ownerParCode);
    }

    @Test
    void testOrgIdGetterAndSetter() {
        final Integer orgId = 0;
        setMainFilePOUnderTest.setOrgId(orgId);
        assertThat(setMainFilePOUnderTest.getOrgId()).isEqualTo(orgId);
    }

    @Test
    void testIdGetterAndSetter() {
        final Long id = 0L;
        setMainFilePOUnderTest.setId(id);
        assertThat(setMainFilePOUnderTest.getId()).isEqualTo(id);
    }

    @Test
    void testSetCategoryGetterAndSetter() {
        final Integer setCategory = 0;
        setMainFilePOUnderTest.setSetCategory(setCategory);
        assertThat(setMainFilePOUnderTest.getSetCategory()).isEqualTo(setCategory);
    }

    @Test
    void testDealerCodeGetterAndSetter() {
        final String dealerCode = "dealerCode";
        setMainFilePOUnderTest.setDealerCode(dealerCode);
        assertThat(setMainFilePOUnderTest.getDealerCode()).isEqualTo(dealerCode);
    }

    @Test
    void testSetTypeGetterAndSetter() {
        final Integer setType = 0;
        setMainFilePOUnderTest.setSetType(setType);
        assertThat(setMainFilePOUnderTest.getSetType()).isEqualTo(setType);
    }

    @Test
    void testSetNameGetterAndSetter() {
        final String setName = "setName";
        setMainFilePOUnderTest.setSetName(setName);
        assertThat(setMainFilePOUnderTest.getSetName()).isEqualTo(setName);
    }

    @Test
    void testSetCodeGetterAndSetter() {
        final String setCode = "setCode";
        setMainFilePOUnderTest.setSetCode(setCode);
        assertThat(setMainFilePOUnderTest.getSetCode()).isEqualTo(setCode);
    }

    @Test
    void testVehiclePurposeGetterAndSetter() {
        final Integer vehiclePurpose = 0;
        setMainFilePOUnderTest.setVehiclePurpose(vehiclePurpose);
        assertThat(setMainFilePOUnderTest.getVehiclePurpose()).isEqualTo(vehiclePurpose);
    }

    @Test
    void testSetExplainGetterAndSetter() {
        final String setExplain = "setExplain";
        setMainFilePOUnderTest.setSetExplain(setExplain);
        assertThat(setMainFilePOUnderTest.getSetExplain()).isEqualTo(setExplain);
    }

    @Test
    void testEnableDateGetterAndSetter() {
        final Date enableDate = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        setMainFilePOUnderTest.setEnableDate(enableDate);
        assertThat(setMainFilePOUnderTest.getEnableDate()).isEqualTo(enableDate);
    }

    @Test
    void testDiscontinueDateGetterAndSetter() {
        final Date discontinueDate = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        setMainFilePOUnderTest.setDiscontinueDate(discontinueDate);
        assertThat(setMainFilePOUnderTest.getDiscontinueDate()).isEqualTo(discontinueDate);
    }

    @Test
    void testModelNameGetterAndSetter() {
        final String modelName = "modelName";
        setMainFilePOUnderTest.setModelName(modelName);
        assertThat(setMainFilePOUnderTest.getModelName()).isEqualTo(modelName);
    }

    @Test
    void testModelCodeGetterAndSetter() {
        final String modelCode = "modelCode";
        setMainFilePOUnderTest.setModelCode(modelCode);
        assertThat(setMainFilePOUnderTest.getModelCode()).isEqualTo(modelCode);
    }

    @Test
    void testEngineCodeGetterAndSetter() {
        final String engineCode = "engineCode";
        setMainFilePOUnderTest.setEngineCode(engineCode);
        assertThat(setMainFilePOUnderTest.getEngineCode()).isEqualTo(engineCode);
    }

    @Test
    void testIsCombineGetterAndSetter() {
        final Integer isCombine = 0;
        setMainFilePOUnderTest.setIsCombine(isCombine);
        assertThat(setMainFilePOUnderTest.getIsCombine()).isEqualTo(isCombine);
    }

    @Test
    void testOrderTypeGetterAndSetter() {
        final Integer orderType = 0;
        setMainFilePOUnderTest.setOrderType(orderType);
        assertThat(setMainFilePOUnderTest.getOrderType()).isEqualTo(orderType);
    }

    @Test
    void testSetDiscountGetterAndSetter() {
        final BigDecimal setDiscount = new BigDecimal("0.00");
        setMainFilePOUnderTest.setSetDiscount(setDiscount);
        assertThat(setMainFilePOUnderTest.getSetDiscount()).isEqualTo(setDiscount);
    }

    @Test
    void testIsInternalSettlementGetterAndSetter() {
        final Integer isInternalSettlement = 0;
        setMainFilePOUnderTest.setIsInternalSettlement(isInternalSettlement);
        assertThat(setMainFilePOUnderTest.getIsInternalSettlement()).isEqualTo(isInternalSettlement);
    }

    @Test
    void testIsUpgradeGetterAndSetter() {
        final Integer isUpgrade = 0;
        setMainFilePOUnderTest.setIsUpgrade(isUpgrade);
        assertThat(setMainFilePOUnderTest.getIsUpgrade()).isEqualTo(isUpgrade);
    }

    @Test
    void testIsDuplicateItemGetterAndSetter() {
        final Integer isDuplicateItem = 0;
        setMainFilePOUnderTest.setIsDuplicateItem(isDuplicateItem);
        assertThat(setMainFilePOUnderTest.getIsDuplicateItem()).isEqualTo(isDuplicateItem);
    }

    @Test
    void testSetApplyGetterAndSetter() {
        final String setApply = "setApply";
        setMainFilePOUnderTest.setSetApply(setApply);
        assertThat(setMainFilePOUnderTest.getSetApply()).isEqualTo(setApply);
    }

    @Test
    void testDataSourcesGetterAndSetter() {
        final Integer dataSources = 0;
        setMainFilePOUnderTest.setDataSources(dataSources);
        assertThat(setMainFilePOUnderTest.getDataSources()).isEqualTo(dataSources);
    }

    @Test
    void testIsDeletedGetterAndSetter() {
        final Boolean isDeleted = false;
        setMainFilePOUnderTest.setIsDeleted(isDeleted);
        assertThat(setMainFilePOUnderTest.getIsDeleted()).isFalse();
    }

    @Test
    void testIsValidGetterAndSetter() {
        final Integer isValid = 0;
        setMainFilePOUnderTest.setIsValid(isValid);
        assertThat(setMainFilePOUnderTest.getIsValid()).isEqualTo(isValid);
    }

    @Test
    void testCreatedAtGetterAndSetter() {
        final Date createdAt = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        setMainFilePOUnderTest.setCreatedAt(createdAt);
        assertThat(setMainFilePOUnderTest.getCreatedAt()).isEqualTo(createdAt);
    }

    @Test
    void testUpdatedAtGetterAndSetter() {
        final Date updatedAt = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        setMainFilePOUnderTest.setUpdatedAt(updatedAt);
        assertThat(setMainFilePOUnderTest.getUpdatedAt()).isEqualTo(updatedAt);
    }

    @Test
    void testId1GetterAndSetter() {
        final Long id = 0L;
        setMainFilePOUnderTest.setId(id);
        assertThat(setMainFilePOUnderTest.pkVal()).isEqualTo(id);
    }
}
