package com.yonyou.dmscus.customer.entity.dto.complaint;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class ImportTempResultTest {

    private ImportTempResult<String> importTempResultUnderTest;

    @BeforeEach
    void setUp() {
        importTempResultUnderTest = new ImportTempResult<>();
    }

    @Test
    void testSuccessCountGetterAndSetter() {
        final int successCount = 0;
        importTempResultUnderTest.setSuccessCount(successCount);
        assertThat(importTempResultUnderTest.getSuccessCount()).isEqualTo(successCount);
    }

    @Test
    void testErrorListGetterAndSetter() {
        final List<String> errorList = Arrays.asList("value");
        importTempResultUnderTest.setErrorList(errorList);
        assertThat(importTempResultUnderTest.getErrorList()).isEqualTo(errorList);
    }

    @Test
    void testSuccessListGetterAndSetter() {
        final List<String> successList = Arrays.asList("value");
        importTempResultUnderTest.setSuccessList(successList);
        assertThat(importTempResultUnderTest.getSuccessList()).isEqualTo(successList);
    }

    @Test
    void testErrorMsgGetterAndSetter() {
        final String errorMsg = "errorMsg";
        importTempResultUnderTest.setErrorMsg(errorMsg);
        assertThat(importTempResultUnderTest.getErrorMsg()).isEqualTo(errorMsg);
    }
}
