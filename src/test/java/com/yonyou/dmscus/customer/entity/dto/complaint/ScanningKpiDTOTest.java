package com.yonyou.dmscus.customer.entity.dto.complaint;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import static org.assertj.core.api.Assertions.assertThat;

class ScanningKpiDTOTest {

    private ScanningKpiDTO scanningKpiDTOUnderTest;

    @BeforeEach
    void setUp() {
        scanningKpiDTOUnderTest = new ScanningKpiDTO();
    }

    @Test
    void testRegionGetterAndSetter() {
        final String region = "region";
        scanningKpiDTOUnderTest.setRegion(region);
        assertThat(scanningKpiDTOUnderTest.getRegion()).isEqualTo(region);
    }

    @Test
    void testComplaintRateGetterAndSetter() {
        final String complaintRate = "complaintRate";
        scanningKpiDTOUnderTest.setComplaintRate(complaintRate);
        assertThat(scanningKpiDTOUnderTest.getComplaintRate()).isEqualTo(complaintRate);
    }

    @Test
    void testEightHourResponseRateGetterAndSetter() {
        final String eightHourResponseRate = "eightHourResponseRate";
        scanningKpiDTOUnderTest.setEightHourResponseRate(eightHourResponseRate);
        assertThat(scanningKpiDTOUnderTest.getEightHourResponseRate()).isEqualTo(eightHourResponseRate);
    }

    @Test
    void testTwentyfourHourResponseRateGetterAndSetter() {
        final String twentyfourHourResponseRate = "twentyfourHourResponseRate";
        scanningKpiDTOUnderTest.setTwentyfourHourResponseRate(twentyfourHourResponseRate);
        assertThat(scanningKpiDTOUnderTest.getTwentyfourHourResponseRate()).isEqualTo(twentyfourHourResponseRate);
    }

    @Test
    void testThreeDayClosingRateGetterAndSetter() {
        final String threeDayClosingRate = "threeDayClosingRate";
        scanningKpiDTOUnderTest.setThreeDayClosingRate(threeDayClosingRate);
        assertThat(scanningKpiDTOUnderTest.getThreeDayClosingRate()).isEqualTo(threeDayClosingRate);
    }

    @Test
    void testFiveDayClosingRateGetterAndSetter() {
        final String fiveDayClosingRate = "fiveDayClosingRate";
        scanningKpiDTOUnderTest.setFiveDayClosingRate(fiveDayClosingRate);
        assertThat(scanningKpiDTOUnderTest.getFiveDayClosingRate()).isEqualTo(fiveDayClosingRate);
    }

    @Test
    void testFourteenDayClosingRateGetterAndSetter() {
        final String fourteenDayClosingRate = "fourteenDayClosingRate";
        scanningKpiDTOUnderTest.setFourteenDayClosingRate(fourteenDayClosingRate);
        assertThat(scanningKpiDTOUnderTest.getFourteenDayClosingRate()).isEqualTo(fourteenDayClosingRate);
    }

    @Test
    void testReturnVisitRateGetterAndSetter() {
        final String returnVisitRate = "returnVisitRate";
        scanningKpiDTOUnderTest.setReturnVisitRate(returnVisitRate);
        assertThat(scanningKpiDTOUnderTest.getReturnVisitRate()).isEqualTo(returnVisitRate);
    }

    @Test
    void testRestartRateGetterAndSetter() {
        final String restartRate = "restartRate";
        scanningKpiDTOUnderTest.setRestartRate(restartRate);
        assertThat(scanningKpiDTOUnderTest.getRestartRate()).isEqualTo(restartRate);
    }

    @Test
    void testAverageClosingTimeGetterAndSetter() {
        final String averageClosingTime = "averageClosingTime";
        scanningKpiDTOUnderTest.setAverageClosingTime(averageClosingTime);
        assertThat(scanningKpiDTOUnderTest.getAverageClosingTime()).isEqualTo(averageClosingTime);
    }

    @Test
    void testStartDateGetterAndSetter() {
        final Date startDate = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        scanningKpiDTOUnderTest.setStartDate(startDate);
        assertThat(scanningKpiDTOUnderTest.getStartDate()).isEqualTo(startDate);
    }

    @Test
    void testEndDateGetterAndSetter() {
        final Date endDate = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        scanningKpiDTOUnderTest.setEndDate(endDate);
        assertThat(scanningKpiDTOUnderTest.getEndDate()).isEqualTo(endDate);
    }

    @Test
    void testRegionIdGetterAndSetter() {
        final String regionId = "regionId";
        scanningKpiDTOUnderTest.setRegionId(regionId);
        assertThat(scanningKpiDTOUnderTest.getRegionId()).isEqualTo(regionId);
    }

    @Test
    void testRegionManagerIdGetterAndSetter() {
        final String regionManagerId = "regionManagerId";
        scanningKpiDTOUnderTest.setRegionManagerId(regionManagerId);
        assertThat(scanningKpiDTOUnderTest.getRegionManagerId()).isEqualTo(regionManagerId);
    }

    @Test
    void testRegionManagerGetterAndSetter() {
        final String regionManager = "regionManager";
        scanningKpiDTOUnderTest.setRegionManager(regionManager);
        assertThat(scanningKpiDTOUnderTest.getRegionManager()).isEqualTo(regionManager);
    }

    @Test
    void testBlocIdGetterAndSetter() {
        final String blocId = "blocId";
        scanningKpiDTOUnderTest.setBlocId(blocId);
        assertThat(scanningKpiDTOUnderTest.getBlocId()).isEqualTo(blocId);
    }

    @Test
    void testBlocGetterAndSetter() {
        final String bloc = "bloc";
        scanningKpiDTOUnderTest.setBloc(bloc);
        assertThat(scanningKpiDTOUnderTest.getBloc()).isEqualTo(bloc);
    }

    @Test
    void testDecimalPointGetterAndSetter() {
        final Integer decimalPoint = 0;
        scanningKpiDTOUnderTest.setDecimalPoint(decimalPoint);
        assertThat(scanningKpiDTOUnderTest.getDecimalPoint()).isEqualTo(decimalPoint);
    }
}
