package com.yonyou.dmscus.customer.entity.dto.complaint;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class AttachmentDtoTest {

    private AttachmentDto attachmentDtoUnderTest;

    @BeforeEach
    void setUp() {
        attachmentDtoUnderTest = new AttachmentDto();
    }

    @Test
    void testNameGetterAndSetter() {
        final String name = "name";
        attachmentDtoUnderTest.setName(name);
        assertThat(attachmentDtoUnderTest.getName()).isEqualTo(name);
    }

    @Test
    void testResGetterAndSetter() {
        final AttachmentDto.Res res = new AttachmentDto.Res();
        attachmentDtoUnderTest.setRes(res);
        assertThat(attachmentDtoUnderTest.getRes()).isEqualTo(res);
    }

    @Test
    void testBaseDateGetterAndSetter() {
        final AttachmentDto.BaseDate baseDate = new AttachmentDto.BaseDate();
        attachmentDtoUnderTest.setBaseDate(baseDate);
        assertThat(attachmentDtoUnderTest.getBaseDate()).isEqualTo(baseDate);
    }
}
