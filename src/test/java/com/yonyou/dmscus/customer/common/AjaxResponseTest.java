package com.yonyou.dmscus.customer.common;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

class AjaxResponseTest {

    private AjaxResponse ajaxResponseUnderTest;

    @BeforeEach
    void setUp() {
        ajaxResponseUnderTest = new AjaxResponse();
    }

    @Test
    void testSetSuccess() {
        // Setup
        // Run the test
        ajaxResponseUnderTest.setSuccess("code", "msg");

        // Verify the results
    }

    @Test
    void testSetFail() {
        // Setup
        // Run the test
        ajaxResponseUnderTest.setFail("code", "msg");

        // Verify the results
    }

    @Test
    void testCodeGetterAndSetter() {
        final String code = "code";
        ajaxResponseUnderTest.setCode(code);
        assertThat(ajaxResponseUnderTest.getCode()).isEqualTo(code);
    }

    @Test
    void testResultGetterAndSetter() {
        final int result1 = 0;
        ajaxResponseUnderTest.setResult(result1);
        assertThat(ajaxResponseUnderTest.getResult()).isEqualTo(result1);
    }

    @Test
    void testMsgGetterAndSetter() {
        final String msg = "msg";
        ajaxResponseUnderTest.setMsg(msg);
        assertThat(ajaxResponseUnderTest.getMsg()).isEqualTo(msg);
    }

    @Test
    void testAddObject1() {
        // Setup
        // Run the test
        ajaxResponseUnderTest.addObject("key", "obj");

        // Verify the results
    }

    @Test
    void testAddObject2() {
        // Setup
        // Run the test
        ajaxResponseUnderTest.addObject("key", "obj", "defaultValue");

        // Verify the results
    }

    @Test
    void testAddObject3() {
        // Setup
        // Run the test
        ajaxResponseUnderTest.addObject("key", "obj", String.class);

        // Verify the results
    }

    @Test
    void testGetObject() {
        // Setup
        // Run the test
        final Object result = ajaxResponseUnderTest.getObject("key");

        // Verify the results
    }

    @Test
    void testGetResponse() {
        final Map<String, Object> result = ajaxResponseUnderTest.getResponse();
    }

    @Test
    void testSuccess() {
        // Run the test
        final AjaxResponse result = AjaxResponse.success("code", "msg");
        assertThat(result.getCode()).isEqualTo("code");
        assertThat(result.getResult()).isEqualTo(0);
        assertThat(result.getMsg()).isEqualTo("msg");
        assertThat(result.getObject("key")).isEqualTo("result");
        assertThat(result.getResponse()).isEqualTo(new HashMap<>());
        assertThat(result.toString()).isEqualTo("result");
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.isFail()).isFalse();
    }

    @Test
    void testFail() {
        // Run the test
        final AjaxResponse result = AjaxResponse.fail("code", "msg");
        assertThat(result.getCode()).isEqualTo("code");
        assertThat(result.getResult()).isEqualTo(0);
        assertThat(result.getMsg()).isEqualTo("msg");
        assertThat(result.getObject("key")).isEqualTo("result");
        assertThat(result.getResponse()).isEqualTo(new HashMap<>());
        assertThat(result.toString()).isEqualTo("result");
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.isFail()).isFalse();
    }

    @Test
    void testToString() {
        assertThat(ajaxResponseUnderTest.toString()).isEqualTo("result");
    }

    @Test
    void testIsSuccess() {
        assertThat(ajaxResponseUnderTest.isSuccess()).isFalse();
    }

    @Test
    void testIsFail() {
        assertThat(ajaxResponseUnderTest.isFail()).isFalse();
    }
}
