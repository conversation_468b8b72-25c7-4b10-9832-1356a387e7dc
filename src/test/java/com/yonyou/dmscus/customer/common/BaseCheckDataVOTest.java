package com.yonyou.dmscus.customer.common;

import com.yonyou.dmscloud.function.domains.dto.DataImportDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

class BaseCheckDataVOTest {

    private BaseCheckDataVO baseCheckDataVOUnderTest;

    @BeforeEach
    void setUp() {
        baseCheckDataVOUnderTest = new BaseCheckDataVO();
    }

    @Test
    void testErrorListGetterAndSetter() {
        final List<String> errorList = Arrays.asList("value");
        baseCheckDataVOUnderTest.setErrorList(errorList);
        assertThat(baseCheckDataVOUnderTest.getErrorList()).isEqualTo(errorList);
    }

    @Test
    void testRowDataGetterAndSetter() {
        final Map<Integer, String> rowData = new HashMap<>();
        baseCheckDataVOUnderTest.setRowData(rowData);
        assertThat(baseCheckDataVOUnderTest.getRowData()).isEqualTo(rowData);
    }

    @Test
    void testAddError() {
        // Setup
        // Run the test
        baseCheckDataVOUnderTest.addError("errorMsg");

        // Verify the results
    }

    @Test
    void testGetColumnValue() {
        // Setup
        // Run the test
        final String result = baseCheckDataVOUnderTest.getColumnValue(0);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    void testRowIdGetterAndSetter() {
        final int rowId = 0;
        baseCheckDataVOUnderTest.setRowId(rowId);
        assertThat(baseCheckDataVOUnderTest.getRowId()).isEqualTo(rowId);
    }

    @Test
    void testImportErrorListGetterAndSetter() {
        final List<DataImportDto> importErrorList = Arrays.asList(new DataImportDto());
        baseCheckDataVOUnderTest.setImportErrorList(importErrorList);
        assertThat(baseCheckDataVOUnderTest.getImportErrorList()).isEqualTo(importErrorList);
    }
}
