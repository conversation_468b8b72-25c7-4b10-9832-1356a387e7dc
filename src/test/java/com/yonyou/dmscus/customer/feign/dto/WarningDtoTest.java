package com.yonyou.dmscus.customer.feign.dto;

import org.junit.jupiter.api.BeforeEach;

import java.util.Arrays;

class WarningDtoTest {

    private WarningDto warningDtoUnderTest;

    @BeforeEach
    void setUp() {
        warningDtoUnderTest = new WarningDto("warning_type", Arrays.asList(WarningDto.vinListInfo.builder()
                .vehicle_vin("vehicle_vin")
                .start_time("start_time")
                .end_time("end_time")
                .build()), 0, 0, 0);
    }
}
