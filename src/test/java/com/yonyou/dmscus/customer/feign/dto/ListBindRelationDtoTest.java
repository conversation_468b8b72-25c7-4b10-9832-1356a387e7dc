package com.yonyou.dmscus.customer.feign.dto;

import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2024/07/24 17:52:45
 */


public class ListBindRelationDtoTest {

    private ListBindRelationDto listBindRelationDtoTest;

    @Test
    void setUp() {
        listBindRelationDtoTest = new ListBindRelationDto();
        listBindRelationDtoTest.toString();
        listBindRelationDtoTest.hashCode();
        ListBindRelationDto listBindRelationDto = new ListBindRelationDto();
        listBindRelationDtoTest.equals(listBindRelationDto);
        listBindRelationDtoTest.canEqual(listBindRelationDto);
        BeanUtils.copyProperties(listBindRelationDto, listBindRelationDtoTest);
    }
}
