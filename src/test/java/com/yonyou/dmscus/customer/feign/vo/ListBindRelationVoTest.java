package com.yonyou.dmscus.customer.feign.vo;

import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2024/07/24 17:49:50
 */


public class ListBindRelationVoTest {
    private ListBindRelationVo listBindRelationVoTest;

    @Test
    void setUp() {
        listBindRelationVoTest = new ListBindRelationVo();
        listBindRelationVoTest.toString();
        listBindRelationVoTest.hashCode();
        ListBindRelationVo listBindRelationVo = new ListBindRelationVo();
        listBindRelationVoTest.equals(listBindRelationVo);
        listBindRelationVoTest.canEqual(listBindRelationVo);
        BeanUtils.copyProperties(listBindRelationVo, listBindRelationVoTest);
    }

}
