package com.yonyou.dmscus.customer.feign.response;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class CdpResponseTest {

    private CdpResponse<String> cdpResponseUnderTest;

    @BeforeEach
    void setUp() {
        cdpResponseUnderTest = new CdpResponse<>();
    }

    @Test
    void testIsSuccess() {
        assertThat(cdpResponseUnderTest.isSuccess()).isFalse();
    }

    @Test
    void testIsFail() {
        assertThat(cdpResponseUnderTest.isFail()).isFalse();
    }
}
