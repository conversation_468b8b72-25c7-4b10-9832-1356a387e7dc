package com.yonyou.dmscus.customer.dto;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class IsExistDealerResponseDtoTest {

    private IsExistDealerResponseDto isExistDealerResponseDtoUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        isExistDealerResponseDtoUnderTest = new IsExistDealerResponseDto();
    }

    @Test
    void testNotExistCompanyCodeListGetterAndSetter() {
        final List<String> notExistCompanyCodeList = Arrays.asList("value");
        isExistDealerResponseDtoUnderTest.setNotExistCompanyCodeList(notExistCompanyCodeList);
        assertThat(isExistDealerResponseDtoUnderTest.getNotExistCompanyCodeList()).isEqualTo(notExistCompanyCodeList);
    }

    @Test
    void testIsAllExistGetterAndSetter() {
        final boolean isAllExist = false;
        isExistDealerResponseDtoUnderTest.setAllExist(isAllExist);
        assertThat(isExistDealerResponseDtoUnderTest.isAllExist()).isFalse();
    }
}
