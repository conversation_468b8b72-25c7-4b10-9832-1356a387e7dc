package com.yonyou.dmscus.customer.dto;

import com.yonyou.dmscus.customer.entity.dto.clue.LiteCrmClueDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationCreate.InviteVehicleDealerTaskDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteRule.InviteVehicleDealerTaskImportDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class EM90MessageRemindDtoTest {

    @Mock
    private InviteVehicleDealerTaskDTO mockInviteVehicleDealerTaskDto;
    @Mock
    private InviteVehicleDealerTaskImportDto mockInviteVehicleDealerTaskImportDto;
    @Mock
    private LiteCrmClueDTO mockLiteCrmClueDto;

    private EM90MessageRemindDto em90MessageRemindDtoUnderTest;

    @BeforeEach
    void setUp() {
        em90MessageRemindDtoUnderTest = new EM90MessageRemindDto(mockInviteVehicleDealerTaskDto,
                mockInviteVehicleDealerTaskImportDto, mockLiteCrmClueDto);
    }
}
