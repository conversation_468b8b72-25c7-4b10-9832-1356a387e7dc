package com.yonyou.dmscus.customer.dto;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class PersonAbilityLevelDtoTest {

    private PersonAbilityLevelDto personAbilityLevelDtoUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        personAbilityLevelDtoUnderTest = new PersonAbilityLevelDto();
    }

    @Test
    void testDealerCodeGetterAndSetter() {
        final String dealerCode = "dealerCode";
        personAbilityLevelDtoUnderTest.setDealerCode(dealerCode);
        assertThat(personAbilityLevelDtoUnderTest.getDealerCode()).isEqualTo(dealerCode);
    }

    @Test
    void testNameGetterAndSetter() {
        final String name = "name";
        personAbilityLevelDtoUnderTest.setName(name);
        assertThat(personAbilityLevelDtoUnderTest.getName()).isEqualTo(name);
    }

    @Test
    void testSurnameGetterAndSetter() {
        final String surname = "surname";
        personAbilityLevelDtoUnderTest.setSurname(surname);
        assertThat(personAbilityLevelDtoUnderTest.getSurname()).isEqualTo(surname);
    }

    @Test
    void testAccountCodeGetterAndSetter() {
        final String accountCode = "accountCode";
        personAbilityLevelDtoUnderTest.setAccountCode(accountCode);
        assertThat(personAbilityLevelDtoUnderTest.getAccountCode()).isEqualTo(accountCode);
    }

    @Test
    void testAbilityLevelGetterAndSetter() {
        final List<String> abilityLevel = Arrays.asList("value");
        personAbilityLevelDtoUnderTest.setAbilityLevel(abilityLevel);
        assertThat(personAbilityLevelDtoUnderTest.getAbilityLevel()).isEqualTo(abilityLevel);
    }

    @Test
    void testPositionsGetterAndSetter() {
        final List<String> positions = Arrays.asList("value");
        personAbilityLevelDtoUnderTest.setPositions(positions);
        assertThat(personAbilityLevelDtoUnderTest.getPositions()).isEqualTo(positions);
    }
}
