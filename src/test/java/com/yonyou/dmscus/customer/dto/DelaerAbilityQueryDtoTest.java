package com.yonyou.dmscus.customer.dto;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import static org.assertj.core.api.Assertions.assertThat;

class DelaerAbilityQueryDtoTest {

    private DelaerAbilityQueryDto delaerAbilityQueryDtoUnderTest;

    @BeforeEach
    void setUp() {
        delaerAbilityQueryDtoUnderTest = new DelaerAbilityQueryDto();
    }

    @Test
    void testYearGetterAndSetter() {
        final Integer year = 2020;
        delaerAbilityQueryDtoUnderTest.setYear(year);
        assertThat(delaerAbilityQueryDtoUnderTest.getYear()).isEqualTo(year);
    }

    @Test
    void testMonthGetterAndSetter() {
        final Integer month = 1;
        delaerAbilityQueryDtoUnderTest.setMonth(month);
        assertThat(delaerAbilityQueryDtoUnderTest.getMonth()).isEqualTo(month);
    }

    @Test
    void testDealerAreaGetterAndSetter() {
        final String dealerArea = "dealerArea";
        delaerAbilityQueryDtoUnderTest.setDealerArea(dealerArea);
        assertThat(delaerAbilityQueryDtoUnderTest.getDealerArea()).isEqualTo(dealerArea);
    }

    @Test
    void testAreaGetterAndSetter() {
        final String area = "area";
        delaerAbilityQueryDtoUnderTest.setArea(area);
        assertThat(delaerAbilityQueryDtoUnderTest.getArea()).isEqualTo(area);
    }

    @Test
    void testDealerCodeGetterAndSetter() {
        final String dealerCode = "dealerCode";
        delaerAbilityQueryDtoUnderTest.setDealerCode(dealerCode);
        assertThat(delaerAbilityQueryDtoUnderTest.getDealerCode()).isEqualTo(dealerCode);
    }

    @Test
    void testDealerNameGetterAndSetter() {
        final String dealerName = "dealerName";
        delaerAbilityQueryDtoUnderTest.setDealerName(dealerName);
        assertThat(delaerAbilityQueryDtoUnderTest.getDealerName()).isEqualTo(dealerName);
    }

    @Test
    void testDealerScaleGetterAndSetter() {
        final String dealerScale = "dealerScale";
        delaerAbilityQueryDtoUnderTest.setDealerScale(dealerScale);
        assertThat(delaerAbilityQueryDtoUnderTest.getDealerScale()).isEqualTo(dealerScale);
    }

    @Test
    void testAccountCodeGetterAndSetter() {
        final String accountCode = "accountCode";
        delaerAbilityQueryDtoUnderTest.setAccountCode(accountCode);
        assertThat(delaerAbilityQueryDtoUnderTest.getAccountCode()).isEqualTo(accountCode);
    }

    @Test
    void testAccountStatusGetterAndSetter() {
        final String accountStatus = "accountStatus";
        delaerAbilityQueryDtoUnderTest.setAccountStatus(accountStatus);
        assertThat(delaerAbilityQueryDtoUnderTest.getAccountStatus()).isEqualTo(accountStatus);
    }

    @Test
    void testServiceStartTimeGetterAndSetter() {
        final Date serviceStartTime = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        delaerAbilityQueryDtoUnderTest.setServiceStartTime(serviceStartTime);
        assertThat(delaerAbilityQueryDtoUnderTest.getServiceStartTime()).isEqualTo(serviceStartTime);
    }

    @Test
    void testSnameGetterAndSetter() {
        final String sname = "sname";
        delaerAbilityQueryDtoUnderTest.setSname(sname);
        assertThat(delaerAbilityQueryDtoUnderTest.getSname()).isEqualTo(sname);
    }

    @Test
    void testSunameGetterAndSetter() {
        final String suname = "suname";
        delaerAbilityQueryDtoUnderTest.setSuname(suname);
        assertThat(delaerAbilityQueryDtoUnderTest.getSuname()).isEqualTo(suname);
    }

    @Test
    void testPostNameGetterAndSetter() {
        final String postName = "postName";
        delaerAbilityQueryDtoUnderTest.setPostName(postName);
        assertThat(delaerAbilityQueryDtoUnderTest.getPostName()).isEqualTo(postName);
    }

    @Test
    void testAbilityNameGetterAndSetter() {
        final String abilityName = "abilityName";
        delaerAbilityQueryDtoUnderTest.setAbilityName(abilityName);
        assertThat(delaerAbilityQueryDtoUnderTest.getAbilityName()).isEqualTo(abilityName);
    }

    @Test
    void testAbilityGetTimeGetterAndSetter() {
        final Date abilityGetTime = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        delaerAbilityQueryDtoUnderTest.setAbilityGetTime(abilityGetTime);
        assertThat(delaerAbilityQueryDtoUnderTest.getAbilityGetTime()).isEqualTo(abilityGetTime);
    }

    @Test
    void testUpdateTimeGetterAndSetter() {
        final Date updateTime = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        delaerAbilityQueryDtoUnderTest.setUpdateTime(updateTime);
        assertThat(delaerAbilityQueryDtoUnderTest.getUpdateTime()).isEqualTo(updateTime);
    }

    @Test
    void testCurrentPageGetterAndSetter() {
        final int currentPage = 0;
        delaerAbilityQueryDtoUnderTest.setCurrentPage(currentPage);
        assertThat(delaerAbilityQueryDtoUnderTest.getCurrentPage()).isEqualTo(currentPage);
    }

    @Test
    void testPageSizeGetterAndSetter() {
        final int pageSize = 0;
        delaerAbilityQueryDtoUnderTest.setPageSize(pageSize);
        assertThat(delaerAbilityQueryDtoUnderTest.getPageSize()).isEqualTo(pageSize);
    }

    @Test
    void testYearAndMonthGetterAndSetter() {
        final String yearAndMonth = "yearAndMonth";
        delaerAbilityQueryDtoUnderTest.setYearAndMonth(yearAndMonth);
        assertThat(delaerAbilityQueryDtoUnderTest.getYearAndMonth()).isEqualTo(yearAndMonth);
    }

    @Test
    void testDealerCodeOrNameGetterAndSetter() {
        final String dealerCodeOrName = "dealerCodeOrName";
        delaerAbilityQueryDtoUnderTest.setDealerCodeOrName(dealerCodeOrName);
        assertThat(delaerAbilityQueryDtoUnderTest.getDealerCodeOrName()).isEqualTo(dealerCodeOrName);
    }

    @Test
    void testBigAreaIdGetterAndSetter() {
        final Long bigAreaId = 0L;
        delaerAbilityQueryDtoUnderTest.setBigAreaId(bigAreaId);
        assertThat(delaerAbilityQueryDtoUnderTest.getBigAreaId()).isEqualTo(bigAreaId);
    }

    @Test
    void testBigAreaGetterAndSetter() {
        final String bigArea = "bigArea";
        delaerAbilityQueryDtoUnderTest.setBigArea(bigArea);
        assertThat(delaerAbilityQueryDtoUnderTest.getBigArea()).isEqualTo(bigArea);
    }

    @Test
    void testSmallAreaIdGetterAndSetter() {
        final Long smallAreaId = 0L;
        delaerAbilityQueryDtoUnderTest.setSmallAreaId(smallAreaId);
        assertThat(delaerAbilityQueryDtoUnderTest.getSmallAreaId()).isEqualTo(smallAreaId);
    }

    @Test
    void testSmallAreaGetterAndSetter() {
        final String smallArea = "smallArea";
        delaerAbilityQueryDtoUnderTest.setSmallArea(smallArea);
        assertThat(delaerAbilityQueryDtoUnderTest.getSmallArea()).isEqualTo(smallArea);
    }
}
