package com.yonyou.dmscus.customer.enevt;

import com.yonyou.dmscus.customer.dto.EM90MessageRemindDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class EM90MessageRemindEventTest {

    @Mock
    private EM90MessageRemindDto mockEventDto;

    private EM90MessageRemindEvent em90MessageRemindEventUnderTest;

    @BeforeEach
    void setUp() {
        em90MessageRemindEventUnderTest = new EM90MessageRemindEvent(mockEventDto);
    }
}
