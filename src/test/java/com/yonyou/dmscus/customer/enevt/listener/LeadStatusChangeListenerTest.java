package com.yonyou.dmscus.customer.enevt.listener;

import com.yonyou.dmscus.customer.enevt.LeadStatusChangeEvent;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightTopicDTO;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class LeadStatusChangeListenerTest {

    @Mock
    private RocketMQTemplate mockRocketMQTemplate;

    @InjectMocks
    private LeadStatusChangeListener leadStatusChangeListenerUnderTest;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(leadStatusChangeListenerUnderTest, "faultLightTopic", "faultLightTopic");
    }

    @Test
    void testSendMessage() {
        // Setup
        final FaultLightTopicDTO faultLightTopicDTO = new FaultLightTopicDTO();
        faultLightTopicDTO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        faultLightTopicDTO.setId(0L);
        faultLightTopicDTO.setFollowUpStatus("followUpStatus");
        faultLightTopicDTO.setBizStatus("bizStatus");
        faultLightTopicDTO.setVerifyStatus("verifyStatus");
        final LeadStatusChangeEvent event = new LeadStatusChangeEvent(Arrays.asList(faultLightTopicDTO));

        // Run the test
        leadStatusChangeListenerUnderTest.sendMessage(event);

        // Verify the results
        verify(mockRocketMQTemplate).asyncSend(eq("faultLightTopic"), any(Message.class), any(SendCallback.class));
    }
}
