package com.yonyou.dmscus.customer.enevt.listener;

import com.yonyou.dmscus.customer.enevt.VocDataEvent;
import com.yonyou.dmscus.customer.service.oss.OssService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class VocDataEventListenerTest {

    @Mock
    private OssService mockOssService;

    @InjectMocks
    private VocDataEventListener vocDataEventListenerUnderTest;

    @Test
    void testSetVoc() {
        // Setup
        final VocDataEvent voc = new VocDataEvent("updateTime");

        // Run the test
        vocDataEventListenerUnderTest.setVoc(voc);

        // Verify the results
        verify(mockOssService).vocdata("updateTime");
    }
}
