package com.yonyou.dmscus.customer.enevt.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yonyou.dmscus.customer.dao.faultLight.CluesDiagnosticInfoRelationMapper;
import com.yonyou.dmscus.customer.entity.po.faultLight.CluesDiagnosticInfoRelationPO;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.support.Acknowledgment;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CluesDiagnosticRelationListenerTest {

    @Mock
    private CluesDiagnosticInfoRelationMapper mockCluesDiagnosticInfoRelationMapper;

    @InjectMocks
    private CluesDiagnosticRelationListener cluesDiagnosticRelationListenerUnderTest;

    @Test
    void testConsumerRelationMessage() {
        // Setup
        final ConsumerRecord<?, String> record = new ConsumerRecord<>("topic", 0, 0L, null, "value");
        final Acknowledgment acknowledgment = null;

        // Configure CluesDiagnosticInfoRelationMapper.selectList(...).
        final List<CluesDiagnosticInfoRelationPO> cluesDiagnosticInfoRelationPOS = Arrays.asList(
                CluesDiagnosticInfoRelationPO.builder()
                        .leadsPreprocessingId(0L)
                        .ctUpload("ctUpload")
                        .etlTime("etlTime")
                        .isDeleted(0)
                        .build());
        when(mockCluesDiagnosticInfoRelationMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(cluesDiagnosticInfoRelationPOS);

        when(mockCluesDiagnosticInfoRelationMapper.insert(CluesDiagnosticInfoRelationPO.builder()
                .leadsPreprocessingId(0L)
                .ctUpload("ctUpload")
                .etlTime("etlTime")
                .isDeleted(0)
                .build())).thenReturn(0);

        // Run the test
        cluesDiagnosticRelationListenerUnderTest.consumerRelationMessage(record, acknowledgment);

        // Verify the results
    }

    @Test
    void testConsumerRelationMessage_CluesDiagnosticInfoRelationMapperSelectListReturnsNoItems() {
        // Setup
        final ConsumerRecord<?, String> record = new ConsumerRecord<>("topic", 0, 0L, null, "value");
        final Acknowledgment acknowledgment = null;
        when(mockCluesDiagnosticInfoRelationMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());
        when(mockCluesDiagnosticInfoRelationMapper.insert(CluesDiagnosticInfoRelationPO.builder()
                .leadsPreprocessingId(0L)
                .ctUpload("ctUpload")
                .etlTime("etlTime")
                .isDeleted(0)
                .build())).thenReturn(0);

        // Run the test
        cluesDiagnosticRelationListenerUnderTest.consumerRelationMessage(record, acknowledgment);

        // Verify the results
    }
}
