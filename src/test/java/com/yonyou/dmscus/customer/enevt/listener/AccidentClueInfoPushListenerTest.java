package com.yonyou.dmscus.customer.enevt.listener;

import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.enevt.AccidentClueInfoPushEvent;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesDTO;
import com.yonyou.dmscus.customer.service.accidentClues.CluePushService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class AccidentClueInfoPushListenerTest {

    @Mock
    private CluePushService mockCluePushService;

    @InjectMocks
    private AccidentClueInfoPushListener accidentClueInfoPushListenerUnderTest;

    @Test
    void testPushMessage() {
        // Setup
        final AccidentCluesDTO accidentCluesDTO = new AccidentCluesDTO();
        accidentCluesDTO.setAppId("appId");
        accidentCluesDTO.setOwnerCode("ownerCode");
        accidentCluesDTO.setOwnerParCode("ownerParCode");
        accidentCluesDTO.setOrgId(0);
        accidentCluesDTO.setAcId(0);
        final AccidentClueInfoPushEvent event = new AccidentClueInfoPushEvent(Arrays.asList(accidentCluesDTO));

        // Run the test
        accidentClueInfoPushListenerUnderTest.pushMessage(event);

        // Verify the results
        // Confirm CluePushService.pushLiteCrmClueInfo(...).
        final AccidentCluesDTO clueInfo = new AccidentCluesDTO();
        clueInfo.setAppId("appId");
        clueInfo.setOwnerCode("ownerCode");
        clueInfo.setOwnerParCode("ownerParCode");
        clueInfo.setOrgId(0);
        clueInfo.setAcId(0);
        verify(mockCluePushService).pushLiteCrmClueInfo(clueInfo);
    }

    @Test
    void testPushMessage_CluePushServiceThrowsServiceBizException() {
        // Setup
        final AccidentCluesDTO accidentCluesDTO = new AccidentCluesDTO();
        accidentCluesDTO.setAppId("appId");
        accidentCluesDTO.setOwnerCode("ownerCode");
        accidentCluesDTO.setOwnerParCode("ownerParCode");
        accidentCluesDTO.setOrgId(0);
        accidentCluesDTO.setAcId(0);
        final AccidentClueInfoPushEvent event = new AccidentClueInfoPushEvent(Arrays.asList(accidentCluesDTO));

        // Configure CluePushService.pushLiteCrmClueInfo(...).
        final AccidentCluesDTO clueInfo = new AccidentCluesDTO();
        clueInfo.setAppId("appId");
        clueInfo.setOwnerCode("ownerCode");
        clueInfo.setOwnerParCode("ownerParCode");
        clueInfo.setOrgId(0);
        clueInfo.setAcId(0);
        doThrow(ServiceBizException.class).when(mockCluePushService).pushLiteCrmClueInfo(clueInfo);

        // Run the test
        assertThatThrownBy(() -> accidentClueInfoPushListenerUnderTest.pushMessage(event))
                .isInstanceOf(ServiceBizException.class);
    }
}
