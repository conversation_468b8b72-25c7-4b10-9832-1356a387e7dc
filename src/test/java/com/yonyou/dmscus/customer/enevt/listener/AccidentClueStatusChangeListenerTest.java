package com.yonyou.dmscus.customer.enevt.listener;

import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.enevt.AccidentClueStatusChangeEvent;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.StatusChangePushDTO;
import com.yonyou.dmscus.customer.service.accidentClues.CluePushService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class AccidentClueStatusChangeListenerTest {

    @Mock
    private CluePushService mockCluePushService;

    @InjectMocks
    private AccidentClueStatusChangeListener accidentClueStatusChangeListenerUnderTest;

    @Test
    void testPushMessage() {
        // Setup
        final StatusChangePushDTO statusChangePushDTO = new StatusChangePushDTO();
        statusChangePushDTO.setId("id");
        statusChangePushDTO.setSourceClueId("sourceClueId");
        statusChangePushDTO.setBizStatus("bizStatus");
        statusChangePushDTO.setFollowUpStatus("followUpStatus");
        statusChangePushDTO.setLeadsType("leadsType");
        final AccidentClueStatusChangeEvent event = new AccidentClueStatusChangeEvent(
                Arrays.asList(statusChangePushDTO));

        // Run the test
        accidentClueStatusChangeListenerUnderTest.pushMessage(event);

        // Verify the results
        // Confirm CluePushService.pushLiteCrmClueStatus(...).
        final StatusChangePushDTO pushInfo = new StatusChangePushDTO();
        pushInfo.setId("id");
        pushInfo.setSourceClueId("sourceClueId");
        pushInfo.setBizStatus("bizStatus");
        pushInfo.setFollowUpStatus("followUpStatus");
        pushInfo.setLeadsType("leadsType");
        verify(mockCluePushService).pushLiteCrmClueStatus(pushInfo);
    }

    @Test
    void testPushMessage_CluePushServiceThrowsServiceBizException() {
        // Setup
        final StatusChangePushDTO statusChangePushDTO = new StatusChangePushDTO();
        statusChangePushDTO.setId("id");
        statusChangePushDTO.setSourceClueId("sourceClueId");
        statusChangePushDTO.setBizStatus("bizStatus");
        statusChangePushDTO.setFollowUpStatus("followUpStatus");
        statusChangePushDTO.setLeadsType("leadsType");
        final AccidentClueStatusChangeEvent event = new AccidentClueStatusChangeEvent(
                Arrays.asList(statusChangePushDTO));

        // Configure CluePushService.pushLiteCrmClueStatus(...).
        final StatusChangePushDTO pushInfo = new StatusChangePushDTO();
        pushInfo.setId("id");
        pushInfo.setSourceClueId("sourceClueId");
        pushInfo.setBizStatus("bizStatus");
        pushInfo.setFollowUpStatus("followUpStatus");
        pushInfo.setLeadsType("leadsType");
        doThrow(ServiceBizException.class).when(mockCluePushService).pushLiteCrmClueStatus(pushInfo);

        // Run the test
        assertThatThrownBy(() -> accidentClueStatusChangeListenerUnderTest.pushMessage(event))
                .isInstanceOf(ServiceBizException.class);
    }
}
