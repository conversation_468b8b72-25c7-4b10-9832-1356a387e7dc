package com.yonyou.dmscus.customer.enevt.listener;

import com.yonyou.dmscus.customer.dto.EM90MessageRemindDto;
import com.yonyou.dmscus.customer.enevt.EM90MessageRemindEvent;
import com.yonyou.dmscus.customer.feign.ApplicationAfterSalesManagementClient;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class EM90MessageRemindListenerTest {

    @Mock
    private ApplicationAfterSalesManagementClient mockApplicationAfterSalesManagementClient;

    @InjectMocks
    private EM90MessageRemindListener em90MessageRemindListenerUnderTest;

    @Test
    void testPushMessage() {
        // Setup
        final EM90MessageRemindEvent event = new EM90MessageRemindEvent(EM90MessageRemindDto.builder().build());

        // Run the test
        em90MessageRemindListenerUnderTest.pushMessage(event);

        // Verify the results
        verify(mockApplicationAfterSalesManagementClient).clueReminder(EM90MessageRemindDto.builder().build());
    }
}
