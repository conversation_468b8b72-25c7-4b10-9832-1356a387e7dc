package com.yonyou.dmscus.customer.enevt.listener;

import com.yonyou.dmscus.customer.enevt.VocEvent;
import com.yonyou.dmscus.customer.service.oss.OssService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class VocEventListenerTest {

    @Mock
    private OssService mockOssService;

    @InjectMocks
    private VocEventListener vocEventListenerUnderTest;

    @Test
    void testSetVoc() {
        // Setup
        final VocEvent voc = new VocEvent("updateTime");

        // Run the test
        vocEventListenerUnderTest.setVoc(voc);

        // Verify the results
        verify(mockOssService).getcontent("updateTime");
    }
}
