package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillMailTemplateMaintainMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMailTemplateMaintainDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMailTemplateMaintainPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillMailTemplateMaintainServiceImplTest {

    @Mock
    private GoodwillMailTemplateMaintainMapper mockGoodwillMailTemplateMaintainMapper;

    private GoodwillMailTemplateMaintainServiceImpl goodwillMailTemplateMaintainServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        goodwillMailTemplateMaintainServiceImplUnderTest = new GoodwillMailTemplateMaintainServiceImpl();
        goodwillMailTemplateMaintainServiceImplUnderTest.goodwillMailTemplateMaintainMapper = mockGoodwillMailTemplateMaintainMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO = new GoodwillMailTemplateMaintainDTO();
        goodwillMailTemplateMaintainDTO.setAppId("appId");
        goodwillMailTemplateMaintainDTO.setOwnerCode("ownerCode");
        goodwillMailTemplateMaintainDTO.setId(0L);
        goodwillMailTemplateMaintainDTO.setSendObject("sendObject");
        goodwillMailTemplateMaintainDTO.setSendObjects(new String[]{"sendObjects"});

        // Configure GoodwillMailTemplateMaintainMapper.selectPageBySql(...).
        final GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPO = new GoodwillMailTemplateMaintainPO();
        goodwillMailTemplateMaintainPO.setAppId("appId");
        goodwillMailTemplateMaintainPO.setOwnerCode("ownerCode");
        goodwillMailTemplateMaintainPO.setOwnerParCode("ownerParCode");
        goodwillMailTemplateMaintainPO.setSendObject("sendObject");
        goodwillMailTemplateMaintainPO.setSendObjects(new String[]{"sendObjects"});
        final List<GoodwillMailTemplateMaintainPO> goodwillMailTemplateMaintainPOS = Arrays.asList(
                goodwillMailTemplateMaintainPO);
        when(mockGoodwillMailTemplateMaintainMapper.selectPageBySql(any(Page.class),
                any(GoodwillMailTemplateMaintainPO.class))).thenReturn(goodwillMailTemplateMaintainPOS);

        // Run the test
        final IPage<GoodwillMailTemplateMaintainDTO> result = goodwillMailTemplateMaintainServiceImplUnderTest.selectPageBysql(
                page, goodwillMailTemplateMaintainDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillMailTemplateMaintainMapperReturnsNoItems() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO = new GoodwillMailTemplateMaintainDTO();
        goodwillMailTemplateMaintainDTO.setAppId("appId");
        goodwillMailTemplateMaintainDTO.setOwnerCode("ownerCode");
        goodwillMailTemplateMaintainDTO.setId(0L);
        goodwillMailTemplateMaintainDTO.setSendObject("sendObject");
        goodwillMailTemplateMaintainDTO.setSendObjects(new String[]{"sendObjects"});

        when(mockGoodwillMailTemplateMaintainMapper.selectPageBySql(any(Page.class),
                any(GoodwillMailTemplateMaintainPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillMailTemplateMaintainDTO> result = goodwillMailTemplateMaintainServiceImplUnderTest.selectPageBysql(
                page, goodwillMailTemplateMaintainDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO = new GoodwillMailTemplateMaintainDTO();
        goodwillMailTemplateMaintainDTO.setAppId("appId");
        goodwillMailTemplateMaintainDTO.setOwnerCode("ownerCode");
        goodwillMailTemplateMaintainDTO.setId(0L);
        goodwillMailTemplateMaintainDTO.setSendObject("sendObject");
        goodwillMailTemplateMaintainDTO.setSendObjects(new String[]{"sendObjects"});

        // Configure GoodwillMailTemplateMaintainMapper.selectListBySql(...).
        final GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPO = new GoodwillMailTemplateMaintainPO();
        goodwillMailTemplateMaintainPO.setAppId("appId");
        goodwillMailTemplateMaintainPO.setOwnerCode("ownerCode");
        goodwillMailTemplateMaintainPO.setOwnerParCode("ownerParCode");
        goodwillMailTemplateMaintainPO.setSendObject("sendObject");
        goodwillMailTemplateMaintainPO.setSendObjects(new String[]{"sendObjects"});
        final List<GoodwillMailTemplateMaintainPO> goodwillMailTemplateMaintainPOS = Arrays.asList(
                goodwillMailTemplateMaintainPO);
        when(mockGoodwillMailTemplateMaintainMapper.selectListBySql(
                any(GoodwillMailTemplateMaintainPO.class))).thenReturn(goodwillMailTemplateMaintainPOS);

        // Run the test
        final List<GoodwillMailTemplateMaintainDTO> result = goodwillMailTemplateMaintainServiceImplUnderTest.selectListBySql(
                goodwillMailTemplateMaintainDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillMailTemplateMaintainMapperReturnsNoItems() throws Exception {
        // Setup
        final GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO = new GoodwillMailTemplateMaintainDTO();
        goodwillMailTemplateMaintainDTO.setAppId("appId");
        goodwillMailTemplateMaintainDTO.setOwnerCode("ownerCode");
        goodwillMailTemplateMaintainDTO.setId(0L);
        goodwillMailTemplateMaintainDTO.setSendObject("sendObject");
        goodwillMailTemplateMaintainDTO.setSendObjects(new String[]{"sendObjects"});

        when(mockGoodwillMailTemplateMaintainMapper.selectListBySql(
                any(GoodwillMailTemplateMaintainPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillMailTemplateMaintainDTO> result = goodwillMailTemplateMaintainServiceImplUnderTest.selectListBySql(
                goodwillMailTemplateMaintainDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure GoodwillMailTemplateMaintainMapper.selectById(...).
        final GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPO = new GoodwillMailTemplateMaintainPO();
        goodwillMailTemplateMaintainPO.setAppId("appId");
        goodwillMailTemplateMaintainPO.setOwnerCode("ownerCode");
        goodwillMailTemplateMaintainPO.setOwnerParCode("ownerParCode");
        goodwillMailTemplateMaintainPO.setSendObject("sendObject");
        goodwillMailTemplateMaintainPO.setSendObjects(new String[]{"sendObjects"});
        when(mockGoodwillMailTemplateMaintainMapper.selectById(0L)).thenReturn(goodwillMailTemplateMaintainPO);

        // Run the test
        final GoodwillMailTemplateMaintainDTO result = goodwillMailTemplateMaintainServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillMailTemplateMaintainMapperReturnsNull() {
        // Setup
        when(mockGoodwillMailTemplateMaintainMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillMailTemplateMaintainServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO = new GoodwillMailTemplateMaintainDTO();
        goodwillMailTemplateMaintainDTO.setAppId("appId");
        goodwillMailTemplateMaintainDTO.setOwnerCode("ownerCode");
        goodwillMailTemplateMaintainDTO.setId(0L);
        goodwillMailTemplateMaintainDTO.setSendObject("sendObject");
        goodwillMailTemplateMaintainDTO.setSendObjects(new String[]{"sendObjects"});

        when(mockGoodwillMailTemplateMaintainMapper.insert(any(GoodwillMailTemplateMaintainPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillMailTemplateMaintainServiceImplUnderTest.insert(goodwillMailTemplateMaintainDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO = new GoodwillMailTemplateMaintainDTO();
        goodwillMailTemplateMaintainDTO.setAppId("appId");
        goodwillMailTemplateMaintainDTO.setOwnerCode("ownerCode");
        goodwillMailTemplateMaintainDTO.setId(0L);
        goodwillMailTemplateMaintainDTO.setSendObject("sendObject");
        goodwillMailTemplateMaintainDTO.setSendObjects(new String[]{"sendObjects"});

        // Configure GoodwillMailTemplateMaintainMapper.selectById(...).
        final GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPO = new GoodwillMailTemplateMaintainPO();
        goodwillMailTemplateMaintainPO.setAppId("appId");
        goodwillMailTemplateMaintainPO.setOwnerCode("ownerCode");
        goodwillMailTemplateMaintainPO.setOwnerParCode("ownerParCode");
        goodwillMailTemplateMaintainPO.setSendObject("sendObject");
        goodwillMailTemplateMaintainPO.setSendObjects(new String[]{"sendObjects"});
        when(mockGoodwillMailTemplateMaintainMapper.selectById(0L)).thenReturn(goodwillMailTemplateMaintainPO);

        when(mockGoodwillMailTemplateMaintainMapper.updateById(any(GoodwillMailTemplateMaintainPO.class)))
                .thenReturn(0);

        // Run the test
        final int result = goodwillMailTemplateMaintainServiceImplUnderTest.update(0L, goodwillMailTemplateMaintainDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }


    @Test
    void testUpdateEmailMaintain() {
        // Setup
        final GoodwillMailTemplateMaintainDTO goodwillMailTemplateMaintainDTO = new GoodwillMailTemplateMaintainDTO();
        goodwillMailTemplateMaintainDTO.setAppId("appId");
        goodwillMailTemplateMaintainDTO.setOwnerCode("ownerCode");
        goodwillMailTemplateMaintainDTO.setId(0L);
        goodwillMailTemplateMaintainDTO.setSendObject("sendObject");
        goodwillMailTemplateMaintainDTO.setSendObjects(new String[]{"sendObjects"});
        final List<GoodwillMailTemplateMaintainDTO> list = Arrays.asList(goodwillMailTemplateMaintainDTO);

        // Configure GoodwillMailTemplateMaintainMapper.selectById(...).
        final GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPO = new GoodwillMailTemplateMaintainPO();
        goodwillMailTemplateMaintainPO.setAppId("appId");
        goodwillMailTemplateMaintainPO.setOwnerCode("ownerCode");
        goodwillMailTemplateMaintainPO.setOwnerParCode("ownerParCode");
        goodwillMailTemplateMaintainPO.setSendObject("sendObject");
        goodwillMailTemplateMaintainPO.setSendObjects(new String[]{"sendObjects"});
        when(mockGoodwillMailTemplateMaintainMapper.selectById(0L)).thenReturn(goodwillMailTemplateMaintainPO);

        when(mockGoodwillMailTemplateMaintainMapper.updateById(any(GoodwillMailTemplateMaintainPO.class)))
                .thenReturn(0);

        // Run the test
        final int result = goodwillMailTemplateMaintainServiceImplUnderTest.updateEmailMaintain(list);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
