package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintKpiBaseRuleUseMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiTest;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRuleUsePO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRuleUseTestPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintKpiBaseRuleUseServiceImplTest {

    @Mock
    private ComplaintKpiBaseRuleUseMapper mockComplaintKpiBaseRuleUseMapper;

    private ComplaintKpiBaseRuleUseServiceImpl complaintKpiBaseRuleUseServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        complaintKpiBaseRuleUseServiceImplUnderTest = new ComplaintKpiBaseRuleUseServiceImpl();
        complaintKpiBaseRuleUseServiceImplUnderTest.complaintKpiBaseRuleUseMapper = mockComplaintKpiBaseRuleUseMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO = new ComplaintKpiBaseRuleUseDTO();
        complaintKpiBaseRuleUseDTO.setId(0L);
        complaintKpiBaseRuleUseDTO.setRuleId(0L);
        complaintKpiBaseRuleUseDTO.setDealerCode("dealerCode");
        complaintKpiBaseRuleUseDTO.setUser("user");
        complaintKpiBaseRuleUseDTO.setWarnValue("warnValue");

        // Configure ComplaintKpiBaseRuleUseMapper.selectPageBySql(...).
        final ComplaintKpiBaseRuleUsePO complaintKpiBaseRuleUsePO = new ComplaintKpiBaseRuleUsePO();
        complaintKpiBaseRuleUsePO.setOwnerCode("ownerCode");
        complaintKpiBaseRuleUsePO.setOrgId(0);
        complaintKpiBaseRuleUsePO.setId(0L);
        complaintKpiBaseRuleUsePO.setRuleId(0L);
        complaintKpiBaseRuleUsePO.setDealerCode("dealerCode");
        final List<ComplaintKpiBaseRuleUsePO> complaintKpiBaseRuleUsePOS = Arrays.asList(complaintKpiBaseRuleUsePO);
        when(mockComplaintKpiBaseRuleUseMapper.selectPageBySql(any(Page.class),
                any(ComplaintKpiBaseRuleUsePO.class))).thenReturn(complaintKpiBaseRuleUsePOS);

        // Run the test
        final IPage<ComplaintKpiBaseRuleUseDTO> result = complaintKpiBaseRuleUseServiceImplUnderTest.selectPageBysql(
                page, complaintKpiBaseRuleUseDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintKpiBaseRuleUseMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO = new ComplaintKpiBaseRuleUseDTO();
        complaintKpiBaseRuleUseDTO.setId(0L);
        complaintKpiBaseRuleUseDTO.setRuleId(0L);
        complaintKpiBaseRuleUseDTO.setDealerCode("dealerCode");
        complaintKpiBaseRuleUseDTO.setUser("user");
        complaintKpiBaseRuleUseDTO.setWarnValue("warnValue");

        when(mockComplaintKpiBaseRuleUseMapper.selectPageBySql(any(Page.class),
                any(ComplaintKpiBaseRuleUsePO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintKpiBaseRuleUseDTO> result = complaintKpiBaseRuleUseServiceImplUnderTest.selectPageBysql(
                page, complaintKpiBaseRuleUseDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO = new ComplaintKpiBaseRuleUseDTO();
        complaintKpiBaseRuleUseDTO.setId(0L);
        complaintKpiBaseRuleUseDTO.setRuleId(0L);
        complaintKpiBaseRuleUseDTO.setDealerCode("dealerCode");
        complaintKpiBaseRuleUseDTO.setUser("user");
        complaintKpiBaseRuleUseDTO.setWarnValue("warnValue");

        // Configure ComplaintKpiBaseRuleUseMapper.selectListBySql(...).
        final ComplaintKpiBaseRuleUsePO complaintKpiBaseRuleUsePO = new ComplaintKpiBaseRuleUsePO();
        complaintKpiBaseRuleUsePO.setOwnerCode("ownerCode");
        complaintKpiBaseRuleUsePO.setOrgId(0);
        complaintKpiBaseRuleUsePO.setId(0L);
        complaintKpiBaseRuleUsePO.setRuleId(0L);
        complaintKpiBaseRuleUsePO.setDealerCode("dealerCode");
        final List<ComplaintKpiBaseRuleUsePO> complaintKpiBaseRuleUsePOS = Arrays.asList(complaintKpiBaseRuleUsePO);
        when(mockComplaintKpiBaseRuleUseMapper.selectListBySql(any(ComplaintKpiBaseRuleUsePO.class)))
                .thenReturn(complaintKpiBaseRuleUsePOS);

        // Run the test
        final List<ComplaintKpiBaseRuleUseDTO> result = complaintKpiBaseRuleUseServiceImplUnderTest.selectListBySql(
                complaintKpiBaseRuleUseDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintKpiBaseRuleUseMapperReturnsNoItems() {
        // Setup
        final ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO = new ComplaintKpiBaseRuleUseDTO();
        complaintKpiBaseRuleUseDTO.setId(0L);
        complaintKpiBaseRuleUseDTO.setRuleId(0L);
        complaintKpiBaseRuleUseDTO.setDealerCode("dealerCode");
        complaintKpiBaseRuleUseDTO.setUser("user");
        complaintKpiBaseRuleUseDTO.setWarnValue("warnValue");

        when(mockComplaintKpiBaseRuleUseMapper.selectListBySql(any(ComplaintKpiBaseRuleUsePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintKpiBaseRuleUseDTO> result = complaintKpiBaseRuleUseServiceImplUnderTest.selectListBySql(
                complaintKpiBaseRuleUseDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure ComplaintKpiBaseRuleUseMapper.selectById(...).
        final ComplaintKpiBaseRuleUsePO complaintKpiBaseRuleUsePO = new ComplaintKpiBaseRuleUsePO();
        complaintKpiBaseRuleUsePO.setOwnerCode("ownerCode");
        complaintKpiBaseRuleUsePO.setOrgId(0);
        complaintKpiBaseRuleUsePO.setId(0L);
        complaintKpiBaseRuleUsePO.setRuleId(0L);
        complaintKpiBaseRuleUsePO.setDealerCode("dealerCode");
        when(mockComplaintKpiBaseRuleUseMapper.selectById(0L)).thenReturn(complaintKpiBaseRuleUsePO);

        // Run the test
        final ComplaintKpiBaseRuleUseDTO result = complaintKpiBaseRuleUseServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintKpiBaseRuleUseMapperReturnsNull() {
        // Setup
        when(mockComplaintKpiBaseRuleUseMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintKpiBaseRuleUseServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO = new ComplaintKpiBaseRuleUseDTO();
        complaintKpiBaseRuleUseDTO.setId(0L);
        complaintKpiBaseRuleUseDTO.setRuleId(0L);
        complaintKpiBaseRuleUseDTO.setDealerCode("dealerCode");
        complaintKpiBaseRuleUseDTO.setUser("user");
        complaintKpiBaseRuleUseDTO.setWarnValue("warnValue");

        when(mockComplaintKpiBaseRuleUseMapper.insert(any(ComplaintKpiBaseRuleUsePO.class))).thenReturn(0);

        // Run the test
        final int result = complaintKpiBaseRuleUseServiceImplUnderTest.insert(complaintKpiBaseRuleUseDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final ComplaintKpiBaseRuleUseDTO complaintKpiBaseRuleUseDTO = new ComplaintKpiBaseRuleUseDTO();
        complaintKpiBaseRuleUseDTO.setId(0L);
        complaintKpiBaseRuleUseDTO.setRuleId(0L);
        complaintKpiBaseRuleUseDTO.setDealerCode("dealerCode");
        complaintKpiBaseRuleUseDTO.setUser("user");
        complaintKpiBaseRuleUseDTO.setWarnValue("warnValue");

        // Configure ComplaintKpiBaseRuleUseMapper.selectById(...).
        final ComplaintKpiBaseRuleUsePO complaintKpiBaseRuleUsePO = new ComplaintKpiBaseRuleUsePO();
        complaintKpiBaseRuleUsePO.setOwnerCode("ownerCode");
        complaintKpiBaseRuleUsePO.setOrgId(0);
        complaintKpiBaseRuleUsePO.setId(0L);
        complaintKpiBaseRuleUsePO.setRuleId(0L);
        complaintKpiBaseRuleUsePO.setDealerCode("dealerCode");
        when(mockComplaintKpiBaseRuleUseMapper.selectById(0L)).thenReturn(complaintKpiBaseRuleUsePO);

        when(mockComplaintKpiBaseRuleUseMapper.updateById(any(ComplaintKpiBaseRuleUsePO.class))).thenReturn(0);

        // Run the test
        final int result = complaintKpiBaseRuleUseServiceImplUnderTest.update(0L, complaintKpiBaseRuleUseDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }


    @Test
    void testSelectPageBysql1() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintKpiBaseRuleUseTestDTO complaintKpiBaseRuleUseTestDTO = new ComplaintKpiBaseRuleUseTestDTO();
        complaintKpiBaseRuleUseTestDTO.setAppId("appId");
        complaintKpiBaseRuleUseTestDTO.setOwnerCode("ownerCode");
        complaintKpiBaseRuleUseTestDTO.setOwnerParCode("ownerParCode");
        complaintKpiBaseRuleUseTestDTO.setOrgId(0);
        complaintKpiBaseRuleUseTestDTO.setId(0L);

        // Configure ComplaintKpiBaseRuleUseMapper.selectPageBySql1(...).
        final ComplaintKpiBaseRuleUseTestPO complaintKpiBaseRuleUseTestPO = new ComplaintKpiBaseRuleUseTestPO();
        complaintKpiBaseRuleUseTestPO.setOwnerCode("ownerCode");
        complaintKpiBaseRuleUseTestPO.setOrgId(0);
        complaintKpiBaseRuleUseTestPO.setId(0L);
        complaintKpiBaseRuleUseTestPO.setRuleId(0L);
        complaintKpiBaseRuleUseTestPO.setDealerCode("dealerCode");
        final List<ComplaintKpiBaseRuleUseTestPO> complaintKpiBaseRuleUseTestPOS = Arrays.asList(
                complaintKpiBaseRuleUseTestPO);
        when(mockComplaintKpiBaseRuleUseMapper.selectPageBySql1(any(Page.class),
                any(ComplaintKpiBaseRuleUseTestPO.class))).thenReturn(complaintKpiBaseRuleUseTestPOS);

        // Run the test
        final IPage<ComplaintKpiBaseRuleUseTestDTO> result = complaintKpiBaseRuleUseServiceImplUnderTest.selectPageBysql1(
                page, complaintKpiBaseRuleUseTestDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql1_ComplaintKpiBaseRuleUseMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintKpiBaseRuleUseTestDTO complaintKpiBaseRuleUseTestDTO = new ComplaintKpiBaseRuleUseTestDTO();
        complaintKpiBaseRuleUseTestDTO.setAppId("appId");
        complaintKpiBaseRuleUseTestDTO.setOwnerCode("ownerCode");
        complaintKpiBaseRuleUseTestDTO.setOwnerParCode("ownerParCode");
        complaintKpiBaseRuleUseTestDTO.setOrgId(0);
        complaintKpiBaseRuleUseTestDTO.setId(0L);

        when(mockComplaintKpiBaseRuleUseMapper.selectPageBySql1(any(Page.class),
                any(ComplaintKpiBaseRuleUseTestPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintKpiBaseRuleUseTestDTO> result = complaintKpiBaseRuleUseServiceImplUnderTest.selectPageBysql1(
                page, complaintKpiBaseRuleUseTestDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql1() {
        // Setup
        final ComplaintKpiBaseRuleUseTestDTO complaintKpiBaseRuleUseTestDTO = new ComplaintKpiBaseRuleUseTestDTO();
        complaintKpiBaseRuleUseTestDTO.setAppId("appId");
        complaintKpiBaseRuleUseTestDTO.setOwnerCode("ownerCode");
        complaintKpiBaseRuleUseTestDTO.setOwnerParCode("ownerParCode");
        complaintKpiBaseRuleUseTestDTO.setOrgId(0);
        complaintKpiBaseRuleUseTestDTO.setId(0L);

        // Configure ComplaintKpiBaseRuleUseMapper.selectListBySql1(...).
        final ComplaintKpiBaseRuleUseTestPO complaintKpiBaseRuleUseTestPO = new ComplaintKpiBaseRuleUseTestPO();
        complaintKpiBaseRuleUseTestPO.setOwnerCode("ownerCode");
        complaintKpiBaseRuleUseTestPO.setOrgId(0);
        complaintKpiBaseRuleUseTestPO.setId(0L);
        complaintKpiBaseRuleUseTestPO.setRuleId(0L);
        complaintKpiBaseRuleUseTestPO.setDealerCode("dealerCode");
        final List<ComplaintKpiBaseRuleUseTestPO> complaintKpiBaseRuleUseTestPOS = Arrays.asList(
                complaintKpiBaseRuleUseTestPO);
        when(mockComplaintKpiBaseRuleUseMapper.selectListBySql1(any(ComplaintKpiBaseRuleUseTestPO.class)))
                .thenReturn(complaintKpiBaseRuleUseTestPOS);

        // Run the test
        final List<ComplaintKpiBaseRuleUseTestDTO> result = complaintKpiBaseRuleUseServiceImplUnderTest.selectListBySql1(
                complaintKpiBaseRuleUseTestDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql1_ComplaintKpiBaseRuleUseMapperReturnsNoItems() {
        // Setup
        final ComplaintKpiBaseRuleUseTestDTO complaintKpiBaseRuleUseTestDTO = new ComplaintKpiBaseRuleUseTestDTO();
        complaintKpiBaseRuleUseTestDTO.setAppId("appId");
        complaintKpiBaseRuleUseTestDTO.setOwnerCode("ownerCode");
        complaintKpiBaseRuleUseTestDTO.setOwnerParCode("ownerParCode");
        complaintKpiBaseRuleUseTestDTO.setOrgId(0);
        complaintKpiBaseRuleUseTestDTO.setId(0L);

        when(mockComplaintKpiBaseRuleUseMapper.selectListBySql1(any(ComplaintKpiBaseRuleUseTestPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintKpiBaseRuleUseTestDTO> result = complaintKpiBaseRuleUseServiceImplUnderTest.selectListBySql1(
                complaintKpiBaseRuleUseTestDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

}
