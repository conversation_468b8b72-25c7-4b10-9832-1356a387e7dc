package com.yonyou.dmscus.customer.service.inviteInsurance;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.service.excel.ExcelRead;
import com.yonyou.dmscloud.framework.service.excel.impl.AbstractExcelReadCallBack;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceRuleMapper;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleRecordImportMapper;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleRecordMapper;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleTaskMapper;
import com.yonyou.dmscus.customer.dto.CheckRepairOrderDTO;
import com.yonyou.dmscus.customer.dto.QueryVehicleOwnerDataVo;
import com.yonyou.dmscus.customer.dto.RepairOrderVO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InsuranceRecordImport;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleOwnerInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordImportDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.QueryRoleUserByCompanyCodeDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.UserInfoOutDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceRulePO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordImportPO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleTaskPO;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.service.common.IMiddleGroundVehicleService;
import com.yonyou.dmscus.customer.service.middleground.BasicdataCenterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteInsuranceVehicleRecordImportServiceImplTest {

    @Mock
    private InviteInsuranceVehicleRecordImportMapper mockInviteInsuranceVehicleRecordImportMapper;
    @Mock
    private ExcelRead<InsuranceRecordImport> mockExcelReadService;
    @Mock
    private ReportCommonClient mockReportCommonClient;
    @Mock
    private BasicdataCenterService mockBasicdataCenterService;
    @Mock
    private InviteInsuranceRuleMapper mockInviteInsuranceRuleMapper;
    @Mock
    private IMiddleGroundVehicleService mockIMiddleGroundVehicleService;
    @Mock
    private InviteInsuranceVehicleTaskMapper mockInviteInsuranceVehicleTaskMapper;
    @Mock
    private InviteInsuranceVehicleRecordMapper mockInviteInsuranceVehicleRecordMapper;

    private InviteInsuranceVehicleRecordImportServiceImpl inviteInsuranceVehicleRecordImportServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteInsuranceVehicleRecordImportServiceImplUnderTest = new InviteInsuranceVehicleRecordImportServiceImpl();
        inviteInsuranceVehicleRecordImportServiceImplUnderTest.inviteInsuranceVehicleRecordImportMapper = mockInviteInsuranceVehicleRecordImportMapper;
        inviteInsuranceVehicleRecordImportServiceImplUnderTest.excelReadService = mockExcelReadService;
        inviteInsuranceVehicleRecordImportServiceImplUnderTest.reportCommonClient = mockReportCommonClient;
        inviteInsuranceVehicleRecordImportServiceImplUnderTest.basicdataCenterService = mockBasicdataCenterService;
        inviteInsuranceVehicleRecordImportServiceImplUnderTest.inviteInsuranceRuleMapper = mockInviteInsuranceRuleMapper;
        inviteInsuranceVehicleRecordImportServiceImplUnderTest.iMiddleGroundVehicleService = mockIMiddleGroundVehicleService;
        inviteInsuranceVehicleRecordImportServiceImplUnderTest.inviteInsuranceVehicleTaskMapper = mockInviteInsuranceVehicleTaskMapper;
        inviteInsuranceVehicleRecordImportServiceImplUnderTest.inviteInsuranceVehicleRecordMapper = mockInviteInsuranceVehicleRecordMapper;
    }

    @Test
    void testCreateInviteByTask() {
        // Setup
        final InviteInsuranceVehicleTaskPO po = new InviteInsuranceVehicleTaskPO();
        po.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        po.setVin("vin");
        po.setLicensePlateNum("licensePlateNum");
        po.setDealerCode("dealerCode");
        po.setName("name");
        po.setTel("tel");
        po.setAge("age");
        po.setSex("sex");
        po.setModel("model");
        po.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        po.setInviteType(0);
        po.setDayInAdvance(1);
        po.setIsCreateInvite(0);
        po.setInviteId(0L);
        po.setDataSources(0);
        po.setInviteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        po.setItemType(0);
        po.setItemCode("itemCode");
        po.setItemName("itemName");
        po.setInsuranceType(0);
        po.setInsuranceBillId(0L);
        po.setClueType(0);

        // Run the test
        inviteInsuranceVehicleRecordImportServiceImplUnderTest.createInviteByTask(po);

        // Verify the results
        // Confirm InviteInsuranceVehicleRecordMapper.insert(...).
        final InviteInsuranceVehicleRecordPO entity = new InviteInsuranceVehicleRecordPO();
        entity.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setId(0L);
        entity.setIsMain(0);
        entity.setSourceType(0);
        entity.setVin("vin");
        entity.setLicensePlateNum("licensePlateNum");
        entity.setName("name");
        entity.setTel("tel");
        entity.setInviteType(0);
        entity.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPlanFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setFollowStatus(0);
        entity.setOrderStatus(0);
        entity.setDataSources(0);
        entity.setIsDeleted(false);
        entity.setDealerCode("dealerCode");
        entity.setAge("age");
        entity.setSex("sex");
        entity.setModel("model");
        entity.setLastChangeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setItemCode("itemCode");
        entity.setItemName("itemName");
        entity.setItemType(0);
        entity.setInsuranceType(0);
        entity.setInsuranceBillId(0L);
        entity.setClueType(0);
//        verify(mockInviteInsuranceVehicleRecordMapper).insert(entity);

        // Confirm InviteInsuranceVehicleTaskMapper.updateById(...).
        final InviteInsuranceVehicleTaskPO entity1 = new InviteInsuranceVehicleTaskPO();
        entity1.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity1.setVin("vin");
        entity1.setLicensePlateNum("licensePlateNum");
        entity1.setDealerCode("dealerCode");
        entity1.setName("name");
        entity1.setTel("tel");
        entity1.setAge("age");
        entity1.setSex("sex");
        entity1.setModel("model");
        entity1.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity1.setInviteType(0);
        entity1.setDayInAdvance(1);
        entity1.setIsCreateInvite(0);
        entity1.setInviteId(0L);
        entity1.setDataSources(0);
        entity1.setInviteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity1.setItemType(0);
        entity1.setItemCode("itemCode");
        entity1.setItemName("itemName");
        entity1.setInsuranceType(0);
        entity1.setInsuranceBillId(0L);
        entity1.setClueType(0);
//        verify(mockInviteInsuranceVehicleTaskMapper).updateById(entity1);
    }

}
