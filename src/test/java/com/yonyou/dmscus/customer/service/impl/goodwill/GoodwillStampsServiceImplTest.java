package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyInfoMapper;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillInvoiceRecordMapper;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillNoticeInvoiceInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillNoticeInvoiceInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillNoticeInvoiceInfoPO;
import com.yonyou.dmscus.customer.feign.DmscloudServiceClient;
import com.yonyou.dmscus.customer.feign.dto.CommonConfigDTO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyInfoService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillStampsServiceImplTest {
    @Mock
    private GoodwillNoticeInvoiceInfoMapper goodwillNoticeInvoiceInfoMapper;
    @Mock
    private GoodwillApplyInfoMapper goodwillApplyInfoMapper;
    @Mock
    GoodwillInvoiceRecordMapper goodwillInvoiceRecordMapper;

    @Mock
    private GoodwillApplyInfoService goodwillApplyInfoService;

    @Mock
    private DmscloudServiceClient dmscloudServiceClient;

    @InjectMocks
    private GoodwillStampsServiceImpl goodwillStampsServiceImplUnderTest;


    @Test
    void testEditNoticeInvoiceInfo() throws Exception {
        // Setup
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto = new GoodwillNoticeInvoiceInfoDTO();
        goodwillNoticeInvoiceInfoDto.setConsumeId("1007573");
        goodwillNoticeInvoiceInfoDto.setGoodwillApplyId(111L);
        goodwillNoticeInvoiceInfoDto.setInvoiceId("11111");
        goodwillNoticeInvoiceInfoDto.setInvoiceTitle("invoiceTitle");
        goodwillNoticeInvoiceInfoDto.setNoticeInvoicePrice(new BigDecimal("1.00"));

        when(goodwillApplyInfoMapper.queryNoticeInvoiceCount(any())).thenReturn(0);

        // Configure GoodwillApplyInfoMapper.selectById(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setAuditor(0L);
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.7"));

        when(goodwillApplyInfoMapper.selectById(any())).thenReturn(goodwillApplyInfoPO);

        // Configure dmscloudServiceClient.getCommonConfig(...).
        final CommonConfigDTO commonConfigDTO = new CommonConfigDTO();
        when(dmscloudServiceClient.getCommonConfig(CommonConstants.GOODWILL_APPLY_MODEL_ID_CONFIG_KEY)).thenReturn(null);
        // goodwillNoticeInvoiceInfoMapper.queryNoticeInvoice(goodwillApplyId);
        final List<GoodwillNoticeInvoiceInfoPO> listPo = new ArrayList<>();
        GoodwillNoticeInvoiceInfoPO go1 = new GoodwillNoticeInvoiceInfoPO();
        go1.setConsumeId(null);
        go1.setInvoiceObject(0);
        go1.setNoticeInvoicePrice(new BigDecimal("2375.20"));
        go1.setVoucherRechargePrice(new BigDecimal("700.00"));
        go1.setCostRate(new BigDecimal("0.70"));
        listPo.add(go1);
        GoodwillNoticeInvoiceInfoPO go2 = new GoodwillNoticeInvoiceInfoPO();
        go2.setConsumeId("1007567");
        go2.setInvoiceObject(1);
        go2.setNoticeInvoicePrice(new BigDecimal("100.00"));
        go2.setVoucherRechargePrice(null);
        go2.setCostRate(new BigDecimal("0.70"));
        listPo.add(go2);
        GoodwillNoticeInvoiceInfoPO go3 = new GoodwillNoticeInvoiceInfoPO();
        go3.setConsumeId("1007572");
        go3.setInvoiceObject(1);
        go3.setNoticeInvoicePrice(new BigDecimal("325.00"));
        go3.setVoucherRechargePrice(null);
        go3.setCostRate(new BigDecimal("0.70"));
        listPo.add(go3);
      //  when(goodwillNoticeInvoiceInfoMapper.queryNoticeInvoice(any())).thenReturn(listPo);
     //   doNothing().when(goodwillApplyInfoService).sendEmail(any(),1, null, any());
        when(goodwillNoticeInvoiceInfoMapper.insert(any())).thenReturn(1);
        when(goodwillInvoiceRecordMapper.insert(any())).thenReturn(1);
        when(goodwillApplyInfoMapper.updateById(any())).thenReturn(1);
        // Run the test
        final int result = goodwillStampsServiceImplUnderTest.editNoticeInvoiceInfo(goodwillNoticeInvoiceInfoDto);
        assertTrue(result == 1);
    }

    @Test
    void testEditNoticeInvoiceInfoToError() {
        // Setup
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto = new GoodwillNoticeInvoiceInfoDTO();
        goodwillNoticeInvoiceInfoDto.setConsumeId("consumeId");
        goodwillNoticeInvoiceInfoDto.setGoodwillApplyId(0L);
        goodwillNoticeInvoiceInfoDto.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setInvoiceTitle("invoiceTitle");
        goodwillNoticeInvoiceInfoDto.setNoticeInvoicePrice(new BigDecimal("0.00"));
        when(goodwillApplyInfoMapper.queryNoticeInvoiceCount(any())).thenReturn(0);
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setAuditor(0L);
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.7"));
        when(goodwillApplyInfoMapper.selectById(any())).thenReturn(null);
        // Run the test
        try {
            goodwillStampsServiceImplUnderTest.editNoticeInvoiceInfo(goodwillNoticeInvoiceInfoDto);
        } catch (Exception e) {
            assertTrue("数据错误,请检查".equals(e.getMessage()));
        }

    }
}
