package com.yonyou.dmscus.customer.service.impl.InviteInsuranceVCDC;

import com.yonyou.dmscloud.framework.domains.dto.basedata.ResponseDTO;
import com.yonyou.dmscus.customer.configuration.InnerUrlProperties;
import com.yonyou.dmscus.customer.configuration.SalesUrlProperties;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDTO;
import com.yonyou.dmscus.customer.util.common.VolvoHttpUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteInsuranceVCDCSOssServiceImplTest {

    @Mock
    private VolvoHttpUtils mockVolvoHttpUtils;
    @Mock
    private RestTemplate mockDirectRestTemplate;
    @Mock
    private SalesUrlProperties mockSalesUrlProperties;
    @Mock
    private InnerUrlProperties mockInnerUrlProperties;

    @InjectMocks
    private InviteInsuranceVCDCSOssServiceImpl inviteInsuranceVCDCSOssServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteInsuranceVCDCSOssServiceImplUnderTest.volvoHttpUtils = mockVolvoHttpUtils;
    }

    @Test
    void testExportExcelOss() {
        // Setup
        final InviteInsuranceVehicleRecordDTO dto = new InviteInsuranceVehicleRecordDTO();
        dto.setAppId("appId");
        dto.setOwnerCode("ownerCode");
        dto.setOwnerParCode("ownerParCode");
        dto.setOrgId(0);
        dto.setId(0L);

        when(mockSalesUrlProperties.getDownloadService()).thenReturn("result");
        when(mockSalesUrlProperties.getExportExcelUrl()).thenReturn("result");
        when(mockInnerUrlProperties.getInviteInsuranceVCDCExport()).thenReturn("serviceUrl");

        // Configure VolvoHttpUtils.getHeaders(...).
        final HttpHeaders httpHeaders = new HttpHeaders(new LinkedMultiValueMap(new HashMap<>()));
        when(mockVolvoHttpUtils.getHeaders()).thenReturn(httpHeaders);

        // Configure RestTemplate.exchange(...).
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("error");
        responseDTO.setReturnMessage("message");
        responseDTO.setData(null);
        responseDTO.setCause("cause");
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST,
                new HttpEntity<>(new HashMap<>(), new HttpHeaders()), ResponseDTO.class))
                .thenReturn(responseDTOResponseEntity);

        // Run the test
        inviteInsuranceVCDCSOssServiceImplUnderTest.exportExcelOss(dto);

        // Verify the results
    }

    @Test
    void testExportExcelOss_RestTemplateThrowsRestClientException() {
        // Setup
        final InviteInsuranceVehicleRecordDTO dto = new InviteInsuranceVehicleRecordDTO();
        dto.setAppId("appId");
        dto.setOwnerCode("ownerCode");
        dto.setOwnerParCode("ownerParCode");
        dto.setOrgId(0);
        dto.setId(0L);

        when(mockSalesUrlProperties.getDownloadService()).thenReturn("result");
        when(mockSalesUrlProperties.getExportExcelUrl()).thenReturn("result");
        when(mockInnerUrlProperties.getInviteInsuranceVCDCExport()).thenReturn("serviceUrl");

        // Configure VolvoHttpUtils.getHeaders(...).
        final HttpHeaders httpHeaders = new HttpHeaders(new LinkedMultiValueMap(new HashMap<>()));
        when(mockVolvoHttpUtils.getHeaders()).thenReturn(httpHeaders);

        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST,
                new HttpEntity<>(new HashMap<>(), new HttpHeaders()), ResponseDTO.class))
                .thenThrow(RestClientException.class);

        // Run the test
        inviteInsuranceVCDCSOssServiceImplUnderTest.exportExcelOss(dto);

        // Verify the results
    }

    @Test
    void testExportColumnList() {
        // Setup
        final Map param = new HashMap<>();

        // Configure VolvoHttpUtils.getHeaders(...).
        final HttpHeaders httpHeaders = new HttpHeaders(new LinkedMultiValueMap(new HashMap<>()));
        when(mockVolvoHttpUtils.getHeaders()).thenReturn(httpHeaders);

        // Configure RestTemplate.exchange(...).
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("error");
        responseDTO.setReturnMessage("message");
        responseDTO.setData(null);
        responseDTO.setCause("cause");
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST,
                new HttpEntity<>(new HashMap<>(), new HttpHeaders()), ResponseDTO.class))
                .thenReturn(responseDTOResponseEntity);

        // Run the test
        final ResponseDTO result = inviteInsuranceVCDCSOssServiceImplUnderTest.exportColumnList("url", param);

        // Verify the results
    }

    @Test
    void testExportColumnList_RestTemplateThrowsRestClientException() {
        // Setup
        final Map param = new HashMap<>();

        // Configure VolvoHttpUtils.getHeaders(...).
        final HttpHeaders httpHeaders = new HttpHeaders(new LinkedMultiValueMap(new HashMap<>()));
        when(mockVolvoHttpUtils.getHeaders()).thenReturn(httpHeaders);

        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST,
                new HttpEntity<>(new HashMap<>(), new HttpHeaders()), ResponseDTO.class))
                .thenThrow(RestClientException.class);

        // Run the test
        final ResponseDTO result = inviteInsuranceVCDCSOssServiceImplUnderTest.exportColumnList("url", param);

        // Verify the results
    }
}
