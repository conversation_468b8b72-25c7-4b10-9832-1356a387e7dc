package com.yonyou.dmscus.customer.service.middleground;

import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.dto.EmpQueryDto;
import com.yonyou.dmscus.customer.dto.UserOrgInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.OrgQueryDto;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.PageResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.*;
import com.yonyou.dmscus.customer.entity.vo.OrgVo;
import com.yonyou.dmscus.customer.feign.MidEndAuthCenterClient;
import com.yonyou.dmscus.customer.feign.MidUserOrganizationClient;
import com.yonyou.dmscus.customer.util.common.VolvoHttpUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BasicdataCenterServiceImplTest {

    @Mock
    private RestTemplate mockDirectRestTemplate;
    @Mock
    private MidUrlProperties mockMidUrlProperties;
    @Mock
    private MidUserOrganizationClient mockOrganizationClient;
    @Mock
    private MidEndAuthCenterClient mockAuthCenterClient;
    @Mock
    private VolvoHttpUtils mockVolvoHttpUtils;

    @InjectMocks
    private BasicdataCenterServiceImpl basicdataCenterServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        basicdataCenterServiceImplUnderTest.volvoHttpUtils = mockVolvoHttpUtils;
    }

    @Test
    void testGetDealerInfo() {
        // Setup
        final OrgVo expectedResult = new OrgVo();
        expectedResult.setCompanyId(0);
        expectedResult.setCompanyCode(0);
        expectedResult.setId(0L);
        expectedResult.setOrgId(0L);
        expectedResult.setEmployeeName("employeeName");

        // Configure MidUserOrganizationClient.orgInfo(...).
        final com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO<OrgVo> orgVoResponseDTO = new com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO<>();
        orgVoResponseDTO.setReturnCode("returnCode");
        orgVoResponseDTO.setReturnMessage("returnMessage");
        final OrgVo orgVo = new OrgVo();
        orgVo.setCompanyId(0);
        orgVo.setCompanyCode(0);
        orgVoResponseDTO.setData(orgVo);
        final OrgQueryDto dto = new OrgQueryDto();
        dto.setOrgCodes(Arrays.asList("value"));
        when(mockOrganizationClient.orgInfo(dto)).thenReturn(orgVoResponseDTO);

        // Run the test
        final OrgVo result = basicdataCenterServiceImplUnderTest.getDealerInfo(Arrays.asList("value"));

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetChildOrgIdByOrgId() {
        // Setup
        // Configure MidUserOrganizationClient.getOrgs(...).
        final com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO<String> stringResponseDTO = new com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO<>();
        stringResponseDTO.setReturnCode("returnCode");
        stringResponseDTO.setReturnMessage("returnMessage");
        stringResponseDTO.setData("value");
        when(mockOrganizationClient.getOrgs("orgId")).thenReturn(stringResponseDTO);

        // Run the test
        final List<Long> result = basicdataCenterServiceImplUnderTest.getChildOrgIdByOrgId("orgId");

        // Verify the results
//        assertThat(result).isEqualTo(Arrays.asList(0L));
    }

    @Test
    void testGetUserOrgInfo() {
        // Setup
        final UserOrgInfoDTO expectedResult = new UserOrgInfoDTO();
        expectedResult.setOrgid(0L);
        expectedResult.setOrgId(0L);
        expectedResult.setUserId(0L);
        expectedResult.setAccount("account");
        expectedResult.setUsername("username");

        // Configure MidEndAuthCenterClient.userInfo(...).
        final com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO<UserOrgInfoDTO> userOrgInfoDTOResponseDTO = new com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO<>();
        userOrgInfoDTOResponseDTO.setReturnCode("returnCode");
        userOrgInfoDTOResponseDTO.setReturnMessage("returnMessage");
        final UserOrgInfoDTO userOrgInfoDTO = new UserOrgInfoDTO();
        userOrgInfoDTO.setOrgid(0L);
        userOrgInfoDTO.setOrgId(0L);
        userOrgInfoDTOResponseDTO.setData(userOrgInfoDTO);
        when(mockAuthCenterClient.userInfo("userId")).thenReturn(userOrgInfoDTOResponseDTO);

        // Run the test
        final UserOrgInfoDTO result = basicdataCenterServiceImplUnderTest.getUserOrgInfo("userId");

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetCompanyAllUser() {
        // Setup
        final UserOrgInfoDTO userOrgInfoDTO = new UserOrgInfoDTO();
        userOrgInfoDTO.setOrgid(0L);
        userOrgInfoDTO.setOrgId(0L);
        userOrgInfoDTO.setUserId(0L);
        userOrgInfoDTO.setAccount("account");
        userOrgInfoDTO.setUsername("username");
        final List<UserOrgInfoDTO> expectedResult = Arrays.asList(userOrgInfoDTO);

        // Configure MidEndAuthCenterClient.empInfo(...).
        final com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO<PageResponseDTO<List<UserOrgInfoDTO>>> pageResponseDTOResponseDTO = new com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO<>();
        pageResponseDTOResponseDTO.setReturnCode("returnCode");
        pageResponseDTOResponseDTO.setReturnMessage("returnMessage");
        final PageResponseDTO<List<UserOrgInfoDTO>> listPageResponseDTO = new PageResponseDTO<>();
        listPageResponseDTO.setTotal(0L);
        final UserOrgInfoDTO userOrgInfoDTO1 = new UserOrgInfoDTO();
        listPageResponseDTO.setRecords(Arrays.asList(userOrgInfoDTO1));
        pageResponseDTOResponseDTO.setData(listPageResponseDTO);
        when(mockAuthCenterClient.empInfo(new EmpQueryDto(0L))).thenReturn(pageResponseDTOResponseDTO);

        // Run the test
        final List<UserOrgInfoDTO> result = basicdataCenterServiceImplUnderTest.getCompanyAllUser(0L);

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }
}
