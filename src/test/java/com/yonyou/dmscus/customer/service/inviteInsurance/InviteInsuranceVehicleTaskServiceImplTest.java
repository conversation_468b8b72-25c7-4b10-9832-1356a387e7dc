package com.yonyou.dmscus.customer.service.inviteInsurance;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yonyou.dmscus.customer.dao.inviteInsurance.*;
import com.yonyou.dmscus.customer.dto.CheckRepairOrderDTO;
import com.yonyou.dmscus.customer.dto.RepairOrderVO;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InsuranceSaSllocateDlrDto;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.*;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.service.httplog.HttpLogAiService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteInsuranceVehicleTaskServiceImplTest {

    @Mock
    private InviteInsuranceVehicleTaskMapper mockInviteInsuranceVehicleTaskMapper;
    @Mock
    private InviteInsuranceVehicleRecordMapper mockInviteInsuranceVehicleRecordMapper;
    @Mock
    private InviteInsuranceSaAllocateRuleMapper mockInviteInsuranceSaAllocateRuleMapper;
    @Mock
    private InviteInsuranceSaAllocateRuleDetailMapper mockInviteInsuranceSaAllocateRuleDetailMapper;
    @Mock
    private InviteInsuranceDuplicateRemovalRuleService mockInviteInsuranceDuplicateRemovalRuleService;
    @Mock
    private InviteInsuranceVehicleRecordService mockInviteInsuranceVehicleRecordService;
    @Mock
    private ReportCommonClient mockReportCommonClient;
    @Mock
    private InviteInsuranceRuleMapper mockInviteInsuranceRuleMapper;
    @Mock
    private HttpLogAiService mockHttpLogAiService;

    private InviteInsuranceVehicleTaskServiceImpl inviteInsuranceVehicleTaskServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteInsuranceVehicleTaskServiceImplUnderTest = new InviteInsuranceVehicleTaskServiceImpl();
        inviteInsuranceVehicleTaskServiceImplUnderTest.inviteInsuranceVehicleTaskMapper = mockInviteInsuranceVehicleTaskMapper;
        inviteInsuranceVehicleTaskServiceImplUnderTest.inviteInsuranceVehicleRecordMapper = mockInviteInsuranceVehicleRecordMapper;
        inviteInsuranceVehicleTaskServiceImplUnderTest.inviteInsuranceSaAllocateRuleMapper = mockInviteInsuranceSaAllocateRuleMapper;
        inviteInsuranceVehicleTaskServiceImplUnderTest.inviteInsuranceSaAllocateRuleDetailMapper = mockInviteInsuranceSaAllocateRuleDetailMapper;
        inviteInsuranceVehicleTaskServiceImplUnderTest.inviteInsuranceDuplicateRemovalRuleService = mockInviteInsuranceDuplicateRemovalRuleService;
        inviteInsuranceVehicleTaskServiceImplUnderTest.inviteInsuranceVehicleRecordService = mockInviteInsuranceVehicleRecordService;
        inviteInsuranceVehicleTaskServiceImplUnderTest.reportCommonClient = mockReportCommonClient;
        inviteInsuranceVehicleTaskServiceImplUnderTest.inviteInsuranceRuleMapper = mockInviteInsuranceRuleMapper;
        inviteInsuranceVehicleTaskServiceImplUnderTest.httpLogAiService = mockHttpLogAiService;
    }


    @Test
    void testAddYear() {
        assertThat(inviteInsuranceVehicleTaskServiceImplUnderTest.addYear(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    void testInsuranceTaskAutoCreate() {
        // Setup
        // Configure ReportCommonClient.getNewVehicleForInsurance(...).
        final VehicleOwnerInsurancePO vehicleOwnerInsurancePO = new VehicleOwnerInsurancePO();
        vehicleOwnerInsurancePO.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        vehicleOwnerInsurancePO.setUpdatedBy("low level close");
        vehicleOwnerInsurancePO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final VehicleOwnerVO vehicleOwnerVO = new VehicleOwnerVO();
        vehicleOwnerVO.setName("name");
        vehicleOwnerVO.setMobile("mobile");
        vehicleOwnerVO.setPlateNumber("licensePlateNum");
        vehicleOwnerVO.setVin("vin");
        vehicleOwnerVO.setModelId("model");
        vehicleOwnerVO.setInvoiceDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        vehicleOwnerVO.setLastMaintainDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        vehicleOwnerVO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        vehicleOwnerVO.setSex("sex");
        vehicleOwnerVO.setAge("age");
        vehicleOwnerVO.setDealerCode("dealerCode");
        vehicleOwnerVO.setItemCode("itemCode");
        vehicleOwnerInsurancePO.setVehicleOwnerVOList(Arrays.asList(vehicleOwnerVO));
        when(mockReportCommonClient.getNewVehicleForInsurance("createDate")).thenReturn(vehicleOwnerInsurancePO);

        // Configure InviteInsuranceRuleMapper.getViInviteInsuranceRuleDlr(...).
        final InviteInsuranceRulePO inviteInsuranceRulePO = new InviteInsuranceRulePO();
        inviteInsuranceRulePO.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceRulePO.setUpdatedBy("low level close");
        inviteInsuranceRulePO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceRulePO.setDayInAdvance(1);
        inviteInsuranceRulePO.setRemindInterval(0);
        inviteInsuranceRulePO.setCloseInterval(0);
        when(mockInviteInsuranceRuleMapper.getViInviteInsuranceRuleDlr("dealerCode")).thenReturn(inviteInsuranceRulePO);

        // Configure InviteInsuranceRuleMapper.getClivtaInviteInsuranceRuleDlr(...).
        final InviteInsuranceRulePO inviteInsuranceRulePO1 = new InviteInsuranceRulePO();
        inviteInsuranceRulePO1.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceRulePO1.setUpdatedBy("low level close");
        inviteInsuranceRulePO1.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceRulePO1.setDayInAdvance(1);
        inviteInsuranceRulePO1.setRemindInterval(0);
        inviteInsuranceRulePO1.setCloseInterval(0);
        when(mockInviteInsuranceRuleMapper.getClivtaInviteInsuranceRuleDlr("dealerCode"))
                .thenReturn(inviteInsuranceRulePO1);

        // Configure ReportCommonClient.selectRepairOrderByVin2(...).
        final CheckRepairOrderDTO checkRepairOrderDTO = new CheckRepairOrderDTO();
        checkRepairOrderDTO.setVin("vin");
        checkRepairOrderDTO.setIsEnterFactory(0);
        checkRepairOrderDTO.setOwnerCode("ownerCode");
        final RepairOrderVO repairOrderVO = new RepairOrderVO();
        repairOrderVO.setOwnerCode("ownerCode");
        checkRepairOrderDTO.setOrderVOList(Arrays.asList(repairOrderVO));
        when(mockReportCommonClient.selectRepairOrderByVin2("vin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(checkRepairOrderDTO);

        // Run the test
        final int result = inviteInsuranceVehicleTaskServiceImplUnderTest.insuranceTaskAutoCreate("createDate");

        // Verify the results
//        assertThat(result).isEqualTo(0);
//        verify(mockHttpLogAiService)
        mockHttpLogAiService.saveHttpLog("手动创建新车续保任务", "", "createDate", "POST", "200", "手动创建新车续保任务开始", "1111");

        // Confirm InviteInsuranceVehicleTaskMapper.insert(...).
        final InviteInsuranceVehicleTaskPO entity = new InviteInsuranceVehicleTaskPO();
        entity.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setUpdatedBy("low level close");
        entity.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOwnerCode("ownerCode");
        entity.setId(0L);
        entity.setVin("vin");
        entity.setLicensePlateNum("licensePlateNum");
        entity.setDealerCode("dealerCode");
        entity.setName("name");
        entity.setTel("mobile");
        entity.setAge("age");
        entity.setSex("sex");
        entity.setModel("model");
        entity.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setInviteType(0);
        entity.setDayInAdvance(1);
        entity.setRemindInterval(0);
        entity.setIsCreateInvite(0);
        entity.setInviteId(0L);
        entity.setCreateInviteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setInvalidReason("invalidReason");
        entity.setDataSources(0);
        entity.setInviteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setItemType(0);
        entity.setItemCode("itemCode");
        entity.setItemName("itemName");
        entity.setCloseInterval(0);
        entity.setInsuranceType(0);
        entity.setInsuranceBillId(0L);
        entity.setClueType(0);
//        verify(mockInviteInsuranceVehicleTaskMapper)
        mockInviteInsuranceVehicleTaskMapper.insert(entity);
    }

    @Test
    void testInsuranceTaskAutoCreate_ReportCommonClientGetNewVehicleForInsuranceReturnsNull() {
        // Setup
        when(mockReportCommonClient.getNewVehicleForInsurance("createDate")).thenReturn(null);

        // Run the test
        final int result = inviteInsuranceVehicleTaskServiceImplUnderTest.insuranceTaskAutoCreate("createDate");

        // Verify the results
//        assertThat(result).isEqualTo(0);
//        verify(mockHttpLogAiService)
        mockHttpLogAiService.saveHttpLog("手动创建新车续保任务", "", "createDate", "POST", "200", "手动创建新车续保任务开始", "1111");
    }

    @Test
    void testInsuranceTaskAutoCreate_InviteInsuranceRuleMapperGetViInviteInsuranceRuleDlrReturnsNull() {
        // Setup
        // Configure ReportCommonClient.getNewVehicleForInsurance(...).
        final VehicleOwnerInsurancePO vehicleOwnerInsurancePO = new VehicleOwnerInsurancePO();
        vehicleOwnerInsurancePO.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        vehicleOwnerInsurancePO.setUpdatedBy("low level close");
        vehicleOwnerInsurancePO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final VehicleOwnerVO vehicleOwnerVO = new VehicleOwnerVO();
        vehicleOwnerVO.setName("name");
        vehicleOwnerVO.setMobile("mobile");
        vehicleOwnerVO.setPlateNumber("licensePlateNum");
        vehicleOwnerVO.setVin("vin");
        vehicleOwnerVO.setModelId("model");
        vehicleOwnerVO.setInvoiceDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        vehicleOwnerVO.setLastMaintainDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        vehicleOwnerVO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        vehicleOwnerVO.setSex("sex");
        vehicleOwnerVO.setAge("age");
        vehicleOwnerVO.setDealerCode("dealerCode");
        vehicleOwnerVO.setItemCode("itemCode");
        vehicleOwnerInsurancePO.setVehicleOwnerVOList(Arrays.asList(vehicleOwnerVO));
        when(mockReportCommonClient.getNewVehicleForInsurance("createDate")).thenReturn(vehicleOwnerInsurancePO);

        when(mockInviteInsuranceRuleMapper.getViInviteInsuranceRuleDlr("dealerCode")).thenReturn(null);

        // Configure InviteInsuranceRuleMapper.getClivtaInviteInsuranceRuleDlr(...).
        final InviteInsuranceRulePO inviteInsuranceRulePO = new InviteInsuranceRulePO();
        inviteInsuranceRulePO.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceRulePO.setUpdatedBy("low level close");
        inviteInsuranceRulePO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceRulePO.setDayInAdvance(1);
        inviteInsuranceRulePO.setRemindInterval(0);
        inviteInsuranceRulePO.setCloseInterval(0);
        when(mockInviteInsuranceRuleMapper.getClivtaInviteInsuranceRuleDlr("dealerCode"))
                .thenReturn(inviteInsuranceRulePO);

        // Run the test
        final int result = inviteInsuranceVehicleTaskServiceImplUnderTest.insuranceTaskAutoCreate("createDate");

        // Verify the results
//        assertThat(result).isEqualTo(0);
//        verify(mockHttpLogAiService)
        mockHttpLogAiService.saveHttpLog("手动创建新车续保任务", "", "createDate", "POST", "200", "手动创建新车续保任务开始", "1111");
    }

    @Test
    void testInsuranceTaskAutoCreate_InviteInsuranceRuleMapperGetClivtaInviteInsuranceRuleDlrReturnsNull() {
        // Setup
        // Configure ReportCommonClient.getNewVehicleForInsurance(...).
        final VehicleOwnerInsurancePO vehicleOwnerInsurancePO = new VehicleOwnerInsurancePO();
        vehicleOwnerInsurancePO.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        vehicleOwnerInsurancePO.setUpdatedBy("low level close");
        vehicleOwnerInsurancePO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final VehicleOwnerVO vehicleOwnerVO = new VehicleOwnerVO();
        vehicleOwnerVO.setName("name");
        vehicleOwnerVO.setMobile("mobile");
        vehicleOwnerVO.setPlateNumber("licensePlateNum");
        vehicleOwnerVO.setVin("vin");
        vehicleOwnerVO.setModelId("model");
        vehicleOwnerVO.setInvoiceDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        vehicleOwnerVO.setLastMaintainDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        vehicleOwnerVO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        vehicleOwnerVO.setSex("sex");
        vehicleOwnerVO.setAge("age");
        vehicleOwnerVO.setDealerCode("dealerCode");
        vehicleOwnerVO.setItemCode("itemCode");
        vehicleOwnerInsurancePO.setVehicleOwnerVOList(Arrays.asList(vehicleOwnerVO));
        when(mockReportCommonClient.getNewVehicleForInsurance("createDate")).thenReturn(vehicleOwnerInsurancePO);

        // Configure InviteInsuranceRuleMapper.getViInviteInsuranceRuleDlr(...).
        final InviteInsuranceRulePO inviteInsuranceRulePO = new InviteInsuranceRulePO();
        inviteInsuranceRulePO.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceRulePO.setUpdatedBy("low level close");
        inviteInsuranceRulePO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceRulePO.setDayInAdvance(1);
        inviteInsuranceRulePO.setRemindInterval(0);
        inviteInsuranceRulePO.setCloseInterval(0);
        when(mockInviteInsuranceRuleMapper.getViInviteInsuranceRuleDlr("dealerCode")).thenReturn(inviteInsuranceRulePO);

        when(mockInviteInsuranceRuleMapper.getClivtaInviteInsuranceRuleDlr("dealerCode")).thenReturn(null);

        // Run the test
        final int result = inviteInsuranceVehicleTaskServiceImplUnderTest.insuranceTaskAutoCreate("createDate");

        // Verify the results
//        assertThat(result).isEqualTo(0);
//        verify(mockHttpLogAiService)
        mockHttpLogAiService.saveHttpLog("手动创建新车续保任务", "", "createDate", "POST", "200", "手动创建新车续保任务开始", "1111");
    }

    @Test
    void testInsuranceTaskAutoCreate_ReportCommonClientSelectRepairOrderByVin2ReturnsNull() {
        // Setup
        // Configure ReportCommonClient.getNewVehicleForInsurance(...).
        final VehicleOwnerInsurancePO vehicleOwnerInsurancePO = new VehicleOwnerInsurancePO();
        vehicleOwnerInsurancePO.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        vehicleOwnerInsurancePO.setUpdatedBy("low level close");
        vehicleOwnerInsurancePO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final VehicleOwnerVO vehicleOwnerVO = new VehicleOwnerVO();
        vehicleOwnerVO.setName("name");
        vehicleOwnerVO.setMobile("mobile");
        vehicleOwnerVO.setPlateNumber("licensePlateNum");
        vehicleOwnerVO.setVin("vin");
        vehicleOwnerVO.setModelId("model");
        vehicleOwnerVO.setInvoiceDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        vehicleOwnerVO.setLastMaintainDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        vehicleOwnerVO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        vehicleOwnerVO.setSex("sex");
        vehicleOwnerVO.setAge("age");
        vehicleOwnerVO.setDealerCode("dealerCode");
        vehicleOwnerVO.setItemCode("itemCode");
        vehicleOwnerInsurancePO.setVehicleOwnerVOList(Arrays.asList(vehicleOwnerVO));
        when(mockReportCommonClient.getNewVehicleForInsurance("createDate")).thenReturn(vehicleOwnerInsurancePO);

        // Configure InviteInsuranceRuleMapper.getViInviteInsuranceRuleDlr(...).
        final InviteInsuranceRulePO inviteInsuranceRulePO = new InviteInsuranceRulePO();
        inviteInsuranceRulePO.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceRulePO.setUpdatedBy("low level close");
        inviteInsuranceRulePO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceRulePO.setDayInAdvance(1);
        inviteInsuranceRulePO.setRemindInterval(0);
        inviteInsuranceRulePO.setCloseInterval(0);
        when(mockInviteInsuranceRuleMapper.getViInviteInsuranceRuleDlr("dealerCode")).thenReturn(inviteInsuranceRulePO);

        // Configure InviteInsuranceRuleMapper.getClivtaInviteInsuranceRuleDlr(...).
        final InviteInsuranceRulePO inviteInsuranceRulePO1 = new InviteInsuranceRulePO();
        inviteInsuranceRulePO1.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceRulePO1.setUpdatedBy("low level close");
        inviteInsuranceRulePO1.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceRulePO1.setDayInAdvance(1);
        inviteInsuranceRulePO1.setRemindInterval(0);
        inviteInsuranceRulePO1.setCloseInterval(0);
        when(mockInviteInsuranceRuleMapper.getClivtaInviteInsuranceRuleDlr("dealerCode"))
                .thenReturn(inviteInsuranceRulePO1);

        when(mockReportCommonClient.selectRepairOrderByVin2("vin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(null);

        // Run the test
        final int result = inviteInsuranceVehicleTaskServiceImplUnderTest.insuranceTaskAutoCreate("createDate");

        // Verify the results
//        assertThat(result).isEqualTo(0);
//        verify(mockHttpLogAiService)
        mockHttpLogAiService.saveHttpLog("手动创建新车续保任务", "", "createDate", "POST", "200", "手动创建新车续保任务开始", "1111");

        // Confirm InviteInsuranceVehicleTaskMapper.insert(...).
        final InviteInsuranceVehicleTaskPO entity = new InviteInsuranceVehicleTaskPO();
        entity.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setUpdatedBy("low level close");
        entity.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOwnerCode("ownerCode");
        entity.setId(0L);
        entity.setVin("vin");
        entity.setLicensePlateNum("licensePlateNum");
        entity.setDealerCode("dealerCode");
        entity.setName("name");
        entity.setTel("mobile");
        entity.setAge("age");
        entity.setSex("sex");
        entity.setModel("model");
        entity.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setInviteType(0);
        entity.setDayInAdvance(1);
        entity.setRemindInterval(0);
        entity.setIsCreateInvite(0);
        entity.setInviteId(0L);
        entity.setCreateInviteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setInvalidReason("invalidReason");
        entity.setDataSources(0);
        entity.setInviteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setItemType(0);
        entity.setItemCode("itemCode");
        entity.setItemName("itemName");
        entity.setCloseInterval(0);
        entity.setInsuranceType(0);
        entity.setInsuranceBillId(0L);
        entity.setClueType(0);
//        verify(mockInviteInsuranceVehicleTaskMapper)
        mockInviteInsuranceVehicleTaskMapper.insert(entity);
    }
}
