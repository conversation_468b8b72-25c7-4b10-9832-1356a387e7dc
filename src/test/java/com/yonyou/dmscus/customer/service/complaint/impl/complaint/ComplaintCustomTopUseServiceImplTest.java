package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintCustomTopUseMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomTopUseDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomTopUsePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintCustomTopUseServiceImplTest {

    @Mock
    private ComplaintCustomTopUseMapper mockComplaintCustomTopUseMapper;

    private ComplaintCustomTopUseServiceImpl complaintCustomTopUseServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        complaintCustomTopUseServiceImplUnderTest = new ComplaintCustomTopUseServiceImpl();
        complaintCustomTopUseServiceImplUnderTest.complaintCustomTopUseMapper = mockComplaintCustomTopUseMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintCustomTopUseDTO complaintCustomTopUseDTO = new ComplaintCustomTopUseDTO();
        complaintCustomTopUseDTO.setAppId("appId");
        complaintCustomTopUseDTO.setOwnerCode("ownerCode");
        complaintCustomTopUseDTO.setType("type");
        complaintCustomTopUseDTO.setUserId(0L);
        complaintCustomTopUseDTO.setIsValid(0);

        // Configure ComplaintCustomTopUseMapper.selectPageBySql(...).
        final ComplaintCustomTopUsePO complaintCustomTopUsePO = new ComplaintCustomTopUsePO();
        complaintCustomTopUsePO.setOwnerCode("ownerCode");
        complaintCustomTopUsePO.setOrgId(0);
        complaintCustomTopUsePO.setId(0L);
        complaintCustomTopUsePO.setType("type");
        complaintCustomTopUsePO.setUserId(0L);
        final List<ComplaintCustomTopUsePO> complaintCustomTopUsePOS = Arrays.asList(complaintCustomTopUsePO);
        when(mockComplaintCustomTopUseMapper.selectPageBySql(any(Page.class),
                any(ComplaintCustomTopUsePO.class))).thenReturn(complaintCustomTopUsePOS);

        // Run the test
        final IPage<ComplaintCustomTopUseDTO> result = complaintCustomTopUseServiceImplUnderTest.selectPageBysql(page,
                complaintCustomTopUseDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintCustomTopUseMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintCustomTopUseDTO complaintCustomTopUseDTO = new ComplaintCustomTopUseDTO();
        complaintCustomTopUseDTO.setAppId("appId");
        complaintCustomTopUseDTO.setOwnerCode("ownerCode");
        complaintCustomTopUseDTO.setType("type");
        complaintCustomTopUseDTO.setUserId(0L);
        complaintCustomTopUseDTO.setIsValid(0);

        when(mockComplaintCustomTopUseMapper.selectPageBySql(any(Page.class),
                any(ComplaintCustomTopUsePO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintCustomTopUseDTO> result = complaintCustomTopUseServiceImplUnderTest.selectPageBysql(page,
                complaintCustomTopUseDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final ComplaintCustomTopUseDTO complaintCustomTopUseDTO = new ComplaintCustomTopUseDTO();
        complaintCustomTopUseDTO.setAppId("appId");
        complaintCustomTopUseDTO.setOwnerCode("ownerCode");
        complaintCustomTopUseDTO.setType("type");
        complaintCustomTopUseDTO.setUserId(0L);
        complaintCustomTopUseDTO.setIsValid(0);

        // Configure ComplaintCustomTopUseMapper.selectListBySql(...).
        final ComplaintCustomTopUsePO complaintCustomTopUsePO = new ComplaintCustomTopUsePO();
        complaintCustomTopUsePO.setOwnerCode("ownerCode");
        complaintCustomTopUsePO.setOrgId(0);
        complaintCustomTopUsePO.setId(0L);
        complaintCustomTopUsePO.setType("type");
        complaintCustomTopUsePO.setUserId(0L);
        final List<ComplaintCustomTopUsePO> complaintCustomTopUsePOS = Arrays.asList(complaintCustomTopUsePO);
        when(mockComplaintCustomTopUseMapper.selectListBySql(any(ComplaintCustomTopUsePO.class)))
                .thenReturn(complaintCustomTopUsePOS);

        // Run the test
        final List<ComplaintCustomTopUseDTO> result = complaintCustomTopUseServiceImplUnderTest.selectListBySql(
                complaintCustomTopUseDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintCustomTopUseMapperReturnsNoItems() {
        // Setup
        final ComplaintCustomTopUseDTO complaintCustomTopUseDTO = new ComplaintCustomTopUseDTO();
        complaintCustomTopUseDTO.setAppId("appId");
        complaintCustomTopUseDTO.setOwnerCode("ownerCode");
        complaintCustomTopUseDTO.setType("type");
        complaintCustomTopUseDTO.setUserId(0L);
        complaintCustomTopUseDTO.setIsValid(0);

        when(mockComplaintCustomTopUseMapper.selectListBySql(any(ComplaintCustomTopUsePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintCustomTopUseDTO> result = complaintCustomTopUseServiceImplUnderTest.selectListBySql(
                complaintCustomTopUseDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure ComplaintCustomTopUseMapper.selectById(...).
        final ComplaintCustomTopUsePO complaintCustomTopUsePO = new ComplaintCustomTopUsePO();
        complaintCustomTopUsePO.setOwnerCode("ownerCode");
        complaintCustomTopUsePO.setOrgId(0);
        complaintCustomTopUsePO.setId(0L);
        complaintCustomTopUsePO.setType("type");
        complaintCustomTopUsePO.setUserId(0L);
        when(mockComplaintCustomTopUseMapper.selectById(0L)).thenReturn(complaintCustomTopUsePO);

        // Run the test
        final ComplaintCustomTopUseDTO result = complaintCustomTopUseServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintCustomTopUseMapperReturnsNull() {
        // Setup
        when(mockComplaintCustomTopUseMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintCustomTopUseServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final ComplaintCustomTopUseDTO complaintCustomTopUseDTO = new ComplaintCustomTopUseDTO();
        complaintCustomTopUseDTO.setAppId("appId");
        complaintCustomTopUseDTO.setOwnerCode("ownerCode");
        complaintCustomTopUseDTO.setType("type");
        complaintCustomTopUseDTO.setUserId(0L);
        complaintCustomTopUseDTO.setIsValid(0);

        when(mockComplaintCustomTopUseMapper.insert(any(ComplaintCustomTopUsePO.class))).thenReturn(0);

        // Run the test
        final int result = complaintCustomTopUseServiceImplUnderTest.insert(complaintCustomTopUseDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final ComplaintCustomTopUseDTO complaintCustomTopUseDTO = new ComplaintCustomTopUseDTO();
        complaintCustomTopUseDTO.setAppId("appId");
        complaintCustomTopUseDTO.setOwnerCode("ownerCode");
        complaintCustomTopUseDTO.setType("type");
        complaintCustomTopUseDTO.setUserId(0L);
        complaintCustomTopUseDTO.setIsValid(0);

        // Configure ComplaintCustomTopUseMapper.selectById(...).
        final ComplaintCustomTopUsePO complaintCustomTopUsePO = new ComplaintCustomTopUsePO();
        complaintCustomTopUsePO.setOwnerCode("ownerCode");
        complaintCustomTopUsePO.setOrgId(0);
        complaintCustomTopUsePO.setId(0L);
        complaintCustomTopUsePO.setType("type");
        complaintCustomTopUsePO.setUserId(0L);
        when(mockComplaintCustomTopUseMapper.selectById(0L)).thenReturn(complaintCustomTopUsePO);

        when(mockComplaintCustomTopUseMapper.updateById(any(ComplaintCustomTopUsePO.class))).thenReturn(0);

        // Run the test
        final int result = complaintCustomTopUseServiceImplUnderTest.update(0L, complaintCustomTopUseDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
