package com.yonyou.dmscus.customer.service.goodwillTask.impl;

import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.dao.goodwill.*;
import com.yonyou.dmscus.customer.dto.EmailInfoDto;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyMailHistoryDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.*;
import com.yonyou.dmscus.customer.service.CommonService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillTaskServiceImplTest {

    @Mock
    private GoodwillApplyInfoMapper mockGoodwillApplyInfoMapper;
    @Mock
    private GoodwillAuditProcessMapper mockGoodwillAuditProcessMapper;
    @Mock
    private GoodwillAuditInfoMapper mockGoodwillAuditInfoMapper;
    @Mock
    private GoodwillDealerMailInfoMapper mockGoodwillDealerMailInfoMapper;
    @Mock
    private GoodwillNoticeInvoiceInfoMapper mockGoodwillNoticeInvoiceInfoMapper;
    @Mock
    private GoodwillApplyMailHistoryMapper mockGoodwillApplyMailHistoryMapper;
    @Mock
    private GoodwillMailTemplateMaintainMapper mockGoodwillMailTemplateMaintainMapper;
    @Mock
    private GoodwilApplyAuditProcessMapper mockGoodwilApplyAuditProcessMapper;
    @Mock
    private CommonService mockCommonService;
    @Mock
    private RestTemplate mockDirectRestTemplate;
    @Mock
    private MidUrlProperties mockMidUrlProperties;

    @InjectMocks
    private GoodwillTaskServiceImpl goodwillTaskServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        goodwillTaskServiceImplUnderTest.goodwillApplyInfoMapper = mockGoodwillApplyInfoMapper;
        goodwillTaskServiceImplUnderTest.goodwillAuditProcessMapper = mockGoodwillAuditProcessMapper;
        goodwillTaskServiceImplUnderTest.goodwillAuditInfoMapper = mockGoodwillAuditInfoMapper;
        goodwillTaskServiceImplUnderTest.goodwillDealerMailInfoMapper = mockGoodwillDealerMailInfoMapper;
        goodwillTaskServiceImplUnderTest.goodwillNoticeInvoiceInfoMapper = mockGoodwillNoticeInvoiceInfoMapper;
        goodwillTaskServiceImplUnderTest.goodwillApplyMailHistoryMapper = mockGoodwillApplyMailHistoryMapper;
        goodwillTaskServiceImplUnderTest.goodwillMailTemplateMaintainMapper = mockGoodwillMailTemplateMaintainMapper;
        goodwillTaskServiceImplUnderTest.goodwilApplyAuditProcessMapper = mockGoodwilApplyAuditProcessMapper;
        goodwillTaskServiceImplUnderTest.commonService = mockCommonService;
    }

    @Test
    void testGoodwillToRefuse_GoodwillAuditProcessMapperReturnsNull() {
        // Setup
        when(mockGoodwillAuditProcessMapper.queryRefuseTime()).thenReturn(null);

        // Run the test
        final int result = goodwillTaskServiceImplUnderTest.goodwillToRefuse();

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testGetDays() {
        // Setup
        // Configure GoodwillMailTemplateMaintainMapper.selectTemplateMaintain(...).
        final GoodwillMailTemplateMaintainPO goodwillMailTemplateMaintainPO = new GoodwillMailTemplateMaintainPO();
        goodwillMailTemplateMaintainPO.setAppId("appId");
        goodwillMailTemplateMaintainPO.setSendObject("sendObject");
        goodwillMailTemplateMaintainPO.setMailTitle("mailTitle");
        goodwillMailTemplateMaintainPO.setMailContent("mailContent");
        goodwillMailTemplateMaintainPO.setDays(0);
        when(mockGoodwillMailTemplateMaintainMapper.selectTemplateMaintain(0))
                .thenReturn(goodwillMailTemplateMaintainPO);

        // Run the test
        final Integer result = goodwillTaskServiceImplUnderTest.getDays(0);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
