package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintSendEmailTemplateMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailTemplateDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintSendEmailTemplatePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintSendEmailTemplateServiceImplTest {

    @Mock
    private ComplaintSendEmailTemplateMapper mockComplaintSendEmailTemplateMapper;

    private ComplaintSendEmailTemplateServiceImpl complaintSendEmailTemplateServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        complaintSendEmailTemplateServiceImplUnderTest = new ComplaintSendEmailTemplateServiceImpl();
        complaintSendEmailTemplateServiceImplUnderTest.complaintSendEmailTemplateMapper = mockComplaintSendEmailTemplateMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO = new ComplaintSendEmailTemplateDTO();
        complaintSendEmailTemplateDTO.setAppId("appId");
        complaintSendEmailTemplateDTO.setOwnerCode("ownerCode");
        complaintSendEmailTemplateDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailTemplateDTO.setOrgId(0);
        complaintSendEmailTemplateDTO.setId(0L);

        // Configure ComplaintSendEmailTemplateMapper.selectPageBySql(...).
        final ComplaintSendEmailTemplatePO complaintSendEmailTemplatePO = new ComplaintSendEmailTemplatePO();
        complaintSendEmailTemplatePO.setAppId("appId");
        complaintSendEmailTemplatePO.setOwnerCode("ownerCode");
        complaintSendEmailTemplatePO.setOwnerParCode("ownerParCode");
        complaintSendEmailTemplatePO.setOrgId(0);
        complaintSendEmailTemplatePO.setId(0L);
        final List<ComplaintSendEmailTemplatePO> complaintSendEmailTemplatePOS = Arrays.asList(
                complaintSendEmailTemplatePO);
        when(mockComplaintSendEmailTemplateMapper.selectPageBySql(any(Page.class),
                any(ComplaintSendEmailTemplatePO.class))).thenReturn(complaintSendEmailTemplatePOS);

        // Run the test
        final IPage<ComplaintSendEmailTemplateDTO> result = complaintSendEmailTemplateServiceImplUnderTest.selectPageBysql(
                page, complaintSendEmailTemplateDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintSendEmailTemplateMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO = new ComplaintSendEmailTemplateDTO();
        complaintSendEmailTemplateDTO.setAppId("appId");
        complaintSendEmailTemplateDTO.setOwnerCode("ownerCode");
        complaintSendEmailTemplateDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailTemplateDTO.setOrgId(0);
        complaintSendEmailTemplateDTO.setId(0L);

        when(mockComplaintSendEmailTemplateMapper.selectPageBySql(any(Page.class),
                any(ComplaintSendEmailTemplatePO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintSendEmailTemplateDTO> result = complaintSendEmailTemplateServiceImplUnderTest.selectPageBysql(
                page, complaintSendEmailTemplateDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO = new ComplaintSendEmailTemplateDTO();
        complaintSendEmailTemplateDTO.setAppId("appId");
        complaintSendEmailTemplateDTO.setOwnerCode("ownerCode");
        complaintSendEmailTemplateDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailTemplateDTO.setOrgId(0);
        complaintSendEmailTemplateDTO.setId(0L);

        // Configure ComplaintSendEmailTemplateMapper.selectListBySql(...).
        final ComplaintSendEmailTemplatePO complaintSendEmailTemplatePO = new ComplaintSendEmailTemplatePO();
        complaintSendEmailTemplatePO.setAppId("appId");
        complaintSendEmailTemplatePO.setOwnerCode("ownerCode");
        complaintSendEmailTemplatePO.setOwnerParCode("ownerParCode");
        complaintSendEmailTemplatePO.setOrgId(0);
        complaintSendEmailTemplatePO.setId(0L);
        final List<ComplaintSendEmailTemplatePO> complaintSendEmailTemplatePOS = Arrays.asList(
                complaintSendEmailTemplatePO);
        when(mockComplaintSendEmailTemplateMapper.selectListBySql(any(ComplaintSendEmailTemplatePO.class)))
                .thenReturn(complaintSendEmailTemplatePOS);

        // Run the test
        final List<ComplaintSendEmailTemplateDTO> result = complaintSendEmailTemplateServiceImplUnderTest.selectListBySql(
                complaintSendEmailTemplateDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintSendEmailTemplateMapperReturnsNoItems() {
        // Setup
        final ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO = new ComplaintSendEmailTemplateDTO();
        complaintSendEmailTemplateDTO.setAppId("appId");
        complaintSendEmailTemplateDTO.setOwnerCode("ownerCode");
        complaintSendEmailTemplateDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailTemplateDTO.setOrgId(0);
        complaintSendEmailTemplateDTO.setId(0L);

        when(mockComplaintSendEmailTemplateMapper.selectListBySql(any(ComplaintSendEmailTemplatePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintSendEmailTemplateDTO> result = complaintSendEmailTemplateServiceImplUnderTest.selectListBySql(
                complaintSendEmailTemplateDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure ComplaintSendEmailTemplateMapper.selectById(...).
        final ComplaintSendEmailTemplatePO complaintSendEmailTemplatePO = new ComplaintSendEmailTemplatePO();
        complaintSendEmailTemplatePO.setAppId("appId");
        complaintSendEmailTemplatePO.setOwnerCode("ownerCode");
        complaintSendEmailTemplatePO.setOwnerParCode("ownerParCode");
        complaintSendEmailTemplatePO.setOrgId(0);
        complaintSendEmailTemplatePO.setId(0L);
        when(mockComplaintSendEmailTemplateMapper.selectById(0L)).thenReturn(complaintSendEmailTemplatePO);

        // Run the test
        final ComplaintSendEmailTemplateDTO result = complaintSendEmailTemplateServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintSendEmailTemplateMapperReturnsNull() {
        // Setup
        when(mockComplaintSendEmailTemplateMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintSendEmailTemplateServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO = new ComplaintSendEmailTemplateDTO();
        complaintSendEmailTemplateDTO.setAppId("appId");
        complaintSendEmailTemplateDTO.setOwnerCode("ownerCode");
        complaintSendEmailTemplateDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailTemplateDTO.setOrgId(0);
        complaintSendEmailTemplateDTO.setId(0L);

        when(mockComplaintSendEmailTemplateMapper.insert(any(ComplaintSendEmailTemplatePO.class))).thenReturn(0);

        // Run the test
        final int result = complaintSendEmailTemplateServiceImplUnderTest.insert(complaintSendEmailTemplateDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO = new ComplaintSendEmailTemplateDTO();
        complaintSendEmailTemplateDTO.setAppId("appId");
        complaintSendEmailTemplateDTO.setOwnerCode("ownerCode");
        complaintSendEmailTemplateDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailTemplateDTO.setOrgId(0);
        complaintSendEmailTemplateDTO.setId(0L);

        // Configure ComplaintSendEmailTemplateMapper.selectById(...).
        final ComplaintSendEmailTemplatePO complaintSendEmailTemplatePO = new ComplaintSendEmailTemplatePO();
        complaintSendEmailTemplatePO.setAppId("appId");
        complaintSendEmailTemplatePO.setOwnerCode("ownerCode");
        complaintSendEmailTemplatePO.setOwnerParCode("ownerParCode");
        complaintSendEmailTemplatePO.setOrgId(0);
        complaintSendEmailTemplatePO.setId(0L);
        when(mockComplaintSendEmailTemplateMapper.selectById(0L)).thenReturn(complaintSendEmailTemplatePO);

        when(mockComplaintSendEmailTemplateMapper.updateById(any(ComplaintSendEmailTemplatePO.class))).thenReturn(0);

        // Run the test
        final int result = complaintSendEmailTemplateServiceImplUnderTest.update(0L, complaintSendEmailTemplateDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectLastTemplate() {
        // Setup
        // Configure ComplaintSendEmailTemplateMapper.selectLastTemplate(...).
        final ComplaintSendEmailTemplateDTO complaintSendEmailTemplateDTO = new ComplaintSendEmailTemplateDTO();
        complaintSendEmailTemplateDTO.setAppId("appId");
        complaintSendEmailTemplateDTO.setOwnerCode("ownerCode");
        complaintSendEmailTemplateDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailTemplateDTO.setOrgId(0);
        complaintSendEmailTemplateDTO.setId(0L);
        when(mockComplaintSendEmailTemplateMapper.selectLastTemplate(0L)).thenReturn(complaintSendEmailTemplateDTO);

        // Run the test
        final ComplaintSendEmailTemplateDTO result = complaintSendEmailTemplateServiceImplUnderTest.selectLastTemplate(
                0L);

        // Verify the results
    }
}
