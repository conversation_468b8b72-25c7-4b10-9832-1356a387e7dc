package com.yonyou.dmscus.customer.service.inviteInsurance;

import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceCustomerInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceCustomerInfoDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteInsuranceCustomerInfoServiceImplTest {

    @Mock
    private InviteInsuranceCustomerInfoMapper mockInviteInsuranceCustomerInfoMapper;

    private InviteInsuranceCustomerInfoServiceImpl inviteInsuranceCustomerInfoServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteInsuranceCustomerInfoServiceImplUnderTest = new InviteInsuranceCustomerInfoServiceImpl();
        inviteInsuranceCustomerInfoServiceImplUnderTest.inviteInsuranceCustomerInfoMapper = mockInviteInsuranceCustomerInfoMapper;
    }

    @Test
    void testDeleteInsuranceCustomerInfo() {
        // Setup
        // Run the test
        inviteInsuranceCustomerInfoServiceImplUnderTest.deleteInsuranceCustomerInfo(0L);

        // Verify the results
        verify(mockInviteInsuranceCustomerInfoMapper).deleteInsuranceCustomerInfo(0L);
    }
}
