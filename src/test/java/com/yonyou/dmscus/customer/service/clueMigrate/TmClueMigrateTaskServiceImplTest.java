package com.yonyou.dmscus.customer.service.clueMigrate;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yonyou.dmscloud.framework.service.excel.ExcelGenerator;
import com.yonyou.dmscloud.framework.service.excel.ExcelRead;
import com.yonyou.dmscloud.framework.service.excel.impl.AbstractExcelReadCallBack;
import com.yonyou.dmscloud.function.domains.dto.ImportResultDto;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.dao.clueMigrate.TmClueMigrateTaskMapper;
import com.yonyou.dmscus.customer.dao.dealermigrationrecord.DealerMigrationRecordMapper;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.dto.clueMigrate.*;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.CompanyDetailDTO;
import com.yonyou.dmscus.customer.entity.po.clueMigrate.TmClueMigrateTask;
import com.yonyou.dmscus.customer.entity.po.dealermigrationrecord.btnlog.DealerMigrationRecordPo;
import com.yonyou.dmscus.customer.entity.po.remote.TmCompany;
import com.yonyou.dmscus.customer.feign.CompanyClient;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.feign.VehicleInfoClient;
import com.yonyou.dmscus.customer.middleInterface.ResponseDTO;
import com.yonyou.dmscus.customer.vo.clueMigrate.ClueMigrateTaskVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.*;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class TmClueMigrateTaskServiceImplTest {

    @Mock
    private ExcelGenerator mockExcelGenerator;
    @Mock
    private ExcelRead<DealerToDealerTaskImportDTO> mockDealerToDealerTaskImportDTOExcelRead;
    @Mock
    private ExcelRead<VinToDealerTaskImportDTO> mockVinToDealerTaskImportDTOExcelRead;
    @Mock
    private ReportCommonClient mockReportCommonClient;
    @Mock
    private VehicleInfoClient mockVehicleInfoClient;
    @Mock
    private CompanyClient mockCompanyClient;
    @Mock
    private RestTemplate mockDirectRestTemplate;
    @Mock
    private MidUrlProperties mockMidUrlProperties;
    @Mock
    private DealerMigrationRecordMapper mockDealerMigrationRecordMapper;
    @Mock
    private TmClueMigrateTaskMapper baseMapper;
    @InjectMocks
    private TmClueMigrateTaskServiceImpl tmClueMigrateTaskServiceImplUnderTest;
    @BeforeEach
    void setUp() throws Exception {
        baseMapper = Mockito.mock(TmClueMigrateTaskMapper.class);
    }

    @Test
    void testEdit() {
        tmClueMigrateTaskServiceImplUnderTest.edit(new ClueMigrateTaskAddDTO());
    }

    @Test
    void testDetail() {
        assertThat(tmClueMigrateTaskServiceImplUnderTest.detail()).isNull();
    }

    @Test
    void testCreateDealerToDealerTask1_CompanyClientReturnsNoItems() {
        // Setup
        final ClueMigrateTaskAddDTO clueMigrateTaskAddDTO = new ClueMigrateTaskAddDTO();
        clueMigrateTaskAddDTO.setSyncType(0);
        clueMigrateTaskAddDTO.setSourceOwnerCode("sourceOwnerCode");
        clueMigrateTaskAddDTO.setOwnerCode("ownerCode");
        clueMigrateTaskAddDTO.setVin("vin");
        clueMigrateTaskAddDTO.setOwnerParCode("companyNameCn");

        final CompanyDetailDTO companyDetailDTO = new CompanyDetailDTO();
        companyDetailDTO.setCompanyId(0L);
        companyDetailDTO.setCompanyCode("companyCode");
        companyDetailDTO.setCompanyNameCn("companyNameCn");
        companyDetailDTO.setCompanyShortNameCn("companyShortNameCn");
        companyDetailDTO.setStatus(0);

        when(mockCompanyClient.selectListByCodeList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> tmClueMigrateTaskServiceImplUnderTest.createDealerToDealerTask(clueMigrateTaskAddDTO,
                companyDetailDTO)).isInstanceOf(DALException.class);
    }

    @Test
    void testCreateVinToDealerTask1_CompanyClientReturnsNoItems() {
        // Setup
        final ClueMigrateTaskAddDTO clueMigrateTaskAddDTO = new ClueMigrateTaskAddDTO();
        clueMigrateTaskAddDTO.setSyncType(0);
        clueMigrateTaskAddDTO.setSourceOwnerCode("sourceOwnerCode");
        clueMigrateTaskAddDTO.setOwnerCode("ownerCode");
        clueMigrateTaskAddDTO.setVin("vin");
        clueMigrateTaskAddDTO.setOwnerParCode("companyNameCn");

        final CompanyDetailDTO companyDetailDTO = new CompanyDetailDTO();
        companyDetailDTO.setCompanyId(0L);
        companyDetailDTO.setCompanyCode("companyCode");
        companyDetailDTO.setCompanyNameCn("companyNameCn");
        companyDetailDTO.setCompanyShortNameCn("companyShortNameCn");
        companyDetailDTO.setStatus(0);

        when(mockCompanyClient.selectListByCodeList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> tmClueMigrateTaskServiceImplUnderTest.createVinToDealerTask(clueMigrateTaskAddDTO,
                companyDetailDTO)).isInstanceOf(DALException.class);
    }

    @Test
    void testCreateVinToDealerTask1_VehicleInfoClientReturnsNull() {
        // Setup
        final ClueMigrateTaskAddDTO clueMigrateTaskAddDTO = new ClueMigrateTaskAddDTO();
        clueMigrateTaskAddDTO.setSyncType(0);
        clueMigrateTaskAddDTO.setSourceOwnerCode("sourceOwnerCode");
        clueMigrateTaskAddDTO.setOwnerCode("ownerCode");
        clueMigrateTaskAddDTO.setVin("vin");
        clueMigrateTaskAddDTO.setOwnerParCode("companyNameCn");

        final CompanyDetailDTO companyDetailDTO = new CompanyDetailDTO();
        companyDetailDTO.setCompanyId(0L);
        companyDetailDTO.setCompanyCode("companyCode");
        companyDetailDTO.setCompanyNameCn("companyNameCn");
        companyDetailDTO.setCompanyShortNameCn("companyShortNameCn");
        companyDetailDTO.setStatus(0);

        // Configure CompanyClient.selectListByCodeList(...).
        final TmCompany tmCompany = new TmCompany();
        tmCompany.setId(0L);
        tmCompany.setCompanyCode("companyCode");
        tmCompany.setCompanyType(0);
        tmCompany.setCompanyNameCn("companyNameCn");
        tmCompany.setCompanyNameEn("companyNameEn");
        final List<TmCompany> tmCompanies = Arrays.asList(tmCompany);
        when(mockCompanyClient.selectListByCodeList(Arrays.asList("value"))).thenReturn(tmCompanies);

        when(mockVehicleInfoClient.getVehicleVOByVin("vin")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> tmClueMigrateTaskServiceImplUnderTest.createVinToDealerTask(clueMigrateTaskAddDTO,
                companyDetailDTO)).isInstanceOf(DALException.class);
    }

    @Test
    void testGetDealerToDealerMigrateTaskVOPageList() {
        // Setup
        final ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO = new ClueMigrateTaskQueryDTO();
        clueMigrateTaskQueryDTO.setCurrentPage(0);
        clueMigrateTaskQueryDTO.setPageSize(0);
        clueMigrateTaskQueryDTO.setSyncType(0);
        clueMigrateTaskQueryDTO.setOwnerCode("ownerCode");
        clueMigrateTaskQueryDTO.setSourceOwnerCode("sourceOwnerCode");

        // Configure CompanyClient.selectListByCodeList(...).
        final TmCompany tmCompany = new TmCompany();
        tmCompany.setId(0L);
        tmCompany.setCompanyCode("companyCode");
        tmCompany.setCompanyType(0);
        tmCompany.setCompanyNameCn("companyNameCn");
        tmCompany.setCompanyNameEn("companyNameEn");
        final List<TmCompany> tmCompanies = Arrays.asList(tmCompany);
        when(mockCompanyClient.selectListByCodeList(Arrays.asList("value"))).thenReturn(tmCompanies);

        // Run the test
        final IPage<ClueMigrateTaskVO> result = tmClueMigrateTaskServiceImplUnderTest.getDealerToDealerMigrateTaskVOPageList(
                clueMigrateTaskQueryDTO);

        // Verify the results
    }

    @Test
    void testGetDealerToDealerMigrateTaskVOPageList_CompanyClientReturnsNoItems() {
        // Setup
        final ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO = new ClueMigrateTaskQueryDTO();
        clueMigrateTaskQueryDTO.setCurrentPage(0);
        clueMigrateTaskQueryDTO.setPageSize(0);
        clueMigrateTaskQueryDTO.setSyncType(0);
        clueMigrateTaskQueryDTO.setOwnerCode("ownerCode");
        clueMigrateTaskQueryDTO.setSourceOwnerCode("sourceOwnerCode");

        when(mockCompanyClient.selectListByCodeList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ClueMigrateTaskVO> result = tmClueMigrateTaskServiceImplUnderTest.getDealerToDealerMigrateTaskVOPageList(
                clueMigrateTaskQueryDTO);

        // Verify the results
    }

    @Test
    void testGetVinToDealerMigrateTaskVOPageList() {
        // Setup
        final ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO = new ClueMigrateTaskQueryDTO();
        clueMigrateTaskQueryDTO.setCurrentPage(0);
        clueMigrateTaskQueryDTO.setPageSize(0);
        clueMigrateTaskQueryDTO.setSyncType(0);
        clueMigrateTaskQueryDTO.setOwnerCode("ownerCode");
        clueMigrateTaskQueryDTO.setSourceOwnerCode("sourceOwnerCode");

        // Configure CompanyClient.selectListByCodeList(...).
        final TmCompany tmCompany = new TmCompany();
        tmCompany.setId(0L);
        tmCompany.setCompanyCode("companyCode");
        tmCompany.setCompanyType(0);
        tmCompany.setCompanyNameCn("companyNameCn");
        tmCompany.setCompanyNameEn("companyNameEn");
        final List<TmCompany> tmCompanies = Arrays.asList(tmCompany);
        when(mockCompanyClient.selectListByCodeList(Arrays.asList("value"))).thenReturn(tmCompanies);

        // Run the test
        final IPage<ClueMigrateTaskVO> result = tmClueMigrateTaskServiceImplUnderTest.getVinToDealerMigrateTaskVOPageList(
                clueMigrateTaskQueryDTO);

        // Verify the results
    }

    @Test
    void testGetVinToDealerMigrateTaskVOPageList_CompanyClientReturnsNoItems() {
        // Setup
        final ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO = new ClueMigrateTaskQueryDTO();
        clueMigrateTaskQueryDTO.setCurrentPage(0);
        clueMigrateTaskQueryDTO.setPageSize(0);
        clueMigrateTaskQueryDTO.setSyncType(0);
        clueMigrateTaskQueryDTO.setOwnerCode("ownerCode");
        clueMigrateTaskQueryDTO.setSourceOwnerCode("sourceOwnerCode");

        when(mockCompanyClient.selectListByCodeList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ClueMigrateTaskVO> result = tmClueMigrateTaskServiceImplUnderTest.getVinToDealerMigrateTaskVOPageList(
                clueMigrateTaskQueryDTO);

        // Verify the results
    }

    @Test
    void testPageList() {
        // Setup
        final ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO = new ClueMigrateTaskQueryDTO();
        clueMigrateTaskQueryDTO.setCurrentPage(0);
        clueMigrateTaskQueryDTO.setPageSize(0);
        clueMigrateTaskQueryDTO.setSyncType(0);
        clueMigrateTaskQueryDTO.setOwnerCode("ownerCode");
        clueMigrateTaskQueryDTO.setSourceOwnerCode("sourceOwnerCode");

        // Configure CompanyClient.selectListByCodeList(...).
        final TmCompany tmCompany = new TmCompany();
        tmCompany.setId(0L);
        tmCompany.setCompanyCode("companyCode");
        tmCompany.setCompanyType(0);
        tmCompany.setCompanyNameCn("companyNameCn");
        tmCompany.setCompanyNameEn("companyNameEn");
        final List<TmCompany> tmCompanies = Arrays.asList(tmCompany);
        when(mockCompanyClient.selectListByCodeList(Arrays.asList("value"))).thenReturn(tmCompanies);

        // Run the test
        final IPage<ClueMigrateTaskVO> result = tmClueMigrateTaskServiceImplUnderTest.pageList(clueMigrateTaskQueryDTO);

        // Verify the results
    }

    @Test
    void testPageList_CompanyClientReturnsNoItems() {
        // Setup
        final ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO = new ClueMigrateTaskQueryDTO();
        clueMigrateTaskQueryDTO.setCurrentPage(0);
        clueMigrateTaskQueryDTO.setPageSize(0);
        clueMigrateTaskQueryDTO.setSyncType(0);
        clueMigrateTaskQueryDTO.setOwnerCode("ownerCode");
        clueMigrateTaskQueryDTO.setSourceOwnerCode("sourceOwnerCode");

        when(mockCompanyClient.selectListByCodeList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ClueMigrateTaskVO> result = tmClueMigrateTaskServiceImplUnderTest.pageList(clueMigrateTaskQueryDTO);

        // Verify the results
    }

    @Test
    void testGetDealerToDealerMigrateTaskVOList_CompanyClientReturnsNoItems() {
        // Setup
        final ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO = new ClueMigrateTaskQueryDTO();
        clueMigrateTaskQueryDTO.setCurrentPage(0);
        clueMigrateTaskQueryDTO.setPageSize(0);
        clueMigrateTaskQueryDTO.setSyncType(0);
        clueMigrateTaskQueryDTO.setOwnerCode("ownerCode");
        clueMigrateTaskQueryDTO.setSourceOwnerCode("sourceOwnerCode");

        when(mockCompanyClient.selectListByCodeList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<ClueMigrateTaskVO> result = tmClueMigrateTaskServiceImplUnderTest.getDealerToDealerMigrateTaskVOList(
                clueMigrateTaskQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetVinToDealerMigrateTaskVOList_CompanyClientReturnsNoItems() {
        // Setup
        final ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO = new ClueMigrateTaskQueryDTO();
        clueMigrateTaskQueryDTO.setCurrentPage(0);
        clueMigrateTaskQueryDTO.setPageSize(0);
        clueMigrateTaskQueryDTO.setSyncType(0);
        clueMigrateTaskQueryDTO.setOwnerCode("ownerCode");
        clueMigrateTaskQueryDTO.setSourceOwnerCode("sourceOwnerCode");

        when(mockCompanyClient.selectListByCodeList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<ClueMigrateTaskVO> result = tmClueMigrateTaskServiceImplUnderTest.getVinToDealerMigrateTaskVOList(
                clueMigrateTaskQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetClueMigrateTaskVOList_CompanyClientReturnsNoItems() {
        // Setup
        final ClueMigrateTaskQueryDTO clueMigrateTaskQueryDTO = new ClueMigrateTaskQueryDTO();
        clueMigrateTaskQueryDTO.setCurrentPage(0);
        clueMigrateTaskQueryDTO.setPageSize(0);
        clueMigrateTaskQueryDTO.setSyncType(0);
        clueMigrateTaskQueryDTO.setOwnerCode("ownerCode");
        clueMigrateTaskQueryDTO.setSourceOwnerCode("sourceOwnerCode");

        when(mockCompanyClient.selectListByCodeList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<ClueMigrateTaskVO> result = tmClueMigrateTaskServiceImplUnderTest.getClueMigrateTaskVOList(
                clueMigrateTaskQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testExportClueMigrateTaskList() {
        tmClueMigrateTaskServiceImplUnderTest.exportClueMigrateTaskList(new ClueMigrateTaskQueryDTO(),
                new MockHttpServletRequest(), new MockHttpServletResponse());
    }

    @Test
    void testDealerToDealerTaskImportValidate() {
        tmClueMigrateTaskServiceImplUnderTest.dealerToDealerTaskImportValidate(new DealerToDealerTaskImportDTO(),
                false);
    }

    @Test
    void testVinToDealerTaskImportValidate() {
        tmClueMigrateTaskServiceImplUnderTest.vinToDealerTaskImportValidate(new VinToDealerTaskImportDTO(), false);
    }

    @Test
    void testIsStopService() {
        // Setup
        final CompanyDetailDTO companyDetailDTO = new CompanyDetailDTO();
        companyDetailDTO.setCompanyId(0L);
        companyDetailDTO.setCompanyCode("companyCode");
        companyDetailDTO.setCompanyNameCn("companyNameCn");
        companyDetailDTO.setCompanyShortNameCn("companyShortNameCn");
        companyDetailDTO.setStatus(0);

        // Run the test
        final boolean result = tmClueMigrateTaskServiceImplUnderTest.isStopService(companyDetailDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testLeadsTransfer() {
        // Setup
        when(mockDealerMigrationRecordMapper.selectPage(any(IPage.class), any(QueryWrapper.class))).thenReturn(null);

        // Run the test
        final IPage<DealerMigrationRecordPo> result = tmClueMigrateTaskServiceImplUnderTest.leadsTransfer(0, 0, 1);

        // Verify the results
    }

    @Test
    void testCleanDealerToDealerTask() {
        // Setup
        // Run the test
        final List<TmClueMigrateTask> list = new ArrayList<>();
        final TmClueMigrateTask tt = new TmClueMigrateTask();
        tt.setSOwnerCode("SHJ");
        tt.setOwnerCode("SHI");
        list.add(tt);
        when(tmClueMigrateTaskServiceImplUnderTest.getBaseMapper().selectList(any())).thenReturn(list);
        tmClueMigrateTaskServiceImplUnderTest.cleanDealerToDealerTask();

    }

    @Test
    void testGetValue() {
        // Setup
        final Map<String, String> values = new HashMap<>();

        // Run the test
        final String result = TmClueMigrateTaskServiceImpl.getValue(values, "key");

        // Verify the results
        assertThat(result).isEqualTo("key");
    }

    @Test
    void testGetCompanyInfoByDealerCode_RestTemplateThrowsRestClientException() {
        // Setup
        when(mockMidUrlProperties.getMidEndOrgCenter()).thenReturn("result");
        when(mockMidUrlProperties.getCompanyInfo()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final CompanyNewSelectDTO companyNewSelectDTO = new CompanyNewSelectDTO();
        companyNewSelectDTO.setCompanyCodeLike("companyCodeLike");
        companyNewSelectDTO.setCompanyCodes(Arrays.asList("value"));
        companyNewSelectDTO.setCompanyNameCn("companyNameCn");
        companyNewSelectDTO.setProvinceId("provinceId");
        companyNewSelectDTO.setCityId("cityId");
        final HttpEntity<CompanyNewSelectDTO> requestEntity = new HttpEntity<>(companyNewSelectDTO, new HttpHeaders());
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST, requestEntity, ResponseDTO.class))
                .thenThrow(RestClientException.class);

        // Run the test
        final CompanyDetailDTO result = tmClueMigrateTaskServiceImplUnderTest.getCompanyInfoByDealerCode("dealerCode");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    void testCreateDealerToDealerTask2_CompanyClientReturnsNoItems() {
        // Setup
        final ClueMigrateTaskAddDTO clueMigrateTaskAddDTO = new ClueMigrateTaskAddDTO();
        clueMigrateTaskAddDTO.setSyncType(0);
        clueMigrateTaskAddDTO.setSourceOwnerCode("sourceOwnerCode");
        clueMigrateTaskAddDTO.setOwnerCode("ownerCode");
        clueMigrateTaskAddDTO.setVin("vin");
        clueMigrateTaskAddDTO.setOwnerParCode("companyNameCn");

        when(mockMidUrlProperties.getMidEndOrgCenter()).thenReturn("result");
        when(mockMidUrlProperties.getCompanyInfo()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("returnCode");
        responseDTO.setReturnMessage("returnMessage");
        responseDTO.setData(null);
        responseDTO.setCause("cause");
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        final CompanyNewSelectDTO companyNewSelectDTO = new CompanyNewSelectDTO();
        companyNewSelectDTO.setCompanyCodeLike("companyCodeLike");
        companyNewSelectDTO.setCompanyCodes(Arrays.asList("value"));
        companyNewSelectDTO.setCompanyNameCn("companyNameCn");
        companyNewSelectDTO.setProvinceId("provinceId");
        companyNewSelectDTO.setCityId("cityId");
        final HttpEntity<CompanyNewSelectDTO> requestEntity = new HttpEntity<>(companyNewSelectDTO, new HttpHeaders());
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST, requestEntity, ResponseDTO.class))
                .thenReturn(responseDTOResponseEntity);

        when(mockCompanyClient.selectListByCodeList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> tmClueMigrateTaskServiceImplUnderTest.createDealerToDealerTask(
                clueMigrateTaskAddDTO)).isInstanceOf(DALException.class);
    }

    @Test
    void testCreateVinToDealerTask2_CompanyClientReturnsNoItems() {
        // Setup
        final ClueMigrateTaskAddDTO clueMigrateTaskAddDTO = new ClueMigrateTaskAddDTO();
        clueMigrateTaskAddDTO.setSyncType(0);
        clueMigrateTaskAddDTO.setSourceOwnerCode("sourceOwnerCode");
        clueMigrateTaskAddDTO.setOwnerCode("ownerCode");
        clueMigrateTaskAddDTO.setVin("vin");
        clueMigrateTaskAddDTO.setOwnerParCode("companyNameCn");

        when(mockMidUrlProperties.getMidEndOrgCenter()).thenReturn("result");
        when(mockMidUrlProperties.getCompanyInfo()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("returnCode");
        responseDTO.setReturnMessage("returnMessage");
        responseDTO.setData(null);
        responseDTO.setCause("cause");
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        final CompanyNewSelectDTO companyNewSelectDTO = new CompanyNewSelectDTO();
        companyNewSelectDTO.setCompanyCodeLike("companyCodeLike");
        companyNewSelectDTO.setCompanyCodes(Arrays.asList("value"));
        companyNewSelectDTO.setCompanyNameCn("companyNameCn");
        companyNewSelectDTO.setProvinceId("provinceId");
        companyNewSelectDTO.setCityId("cityId");
        final HttpEntity<CompanyNewSelectDTO> requestEntity = new HttpEntity<>(companyNewSelectDTO, new HttpHeaders());
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST, requestEntity, ResponseDTO.class))
                .thenReturn(responseDTOResponseEntity);

        when(mockCompanyClient.selectListByCodeList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(
                () -> tmClueMigrateTaskServiceImplUnderTest.createVinToDealerTask(clueMigrateTaskAddDTO))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testCreateVinToDealerTask2_VehicleInfoClientReturnsNull() {
        // Setup
        final ClueMigrateTaskAddDTO clueMigrateTaskAddDTO = new ClueMigrateTaskAddDTO();
        clueMigrateTaskAddDTO.setSyncType(0);
        clueMigrateTaskAddDTO.setSourceOwnerCode("sourceOwnerCode");
        clueMigrateTaskAddDTO.setOwnerCode("ownerCode");
        clueMigrateTaskAddDTO.setVin("vin");
        clueMigrateTaskAddDTO.setOwnerParCode("companyNameCn");

        when(mockMidUrlProperties.getMidEndOrgCenter()).thenReturn("result");
        when(mockMidUrlProperties.getCompanyInfo()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("returnCode");
        responseDTO.setReturnMessage("returnMessage");
        responseDTO.setData(null);
        responseDTO.setCause("cause");
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        final CompanyNewSelectDTO companyNewSelectDTO = new CompanyNewSelectDTO();
        companyNewSelectDTO.setCompanyCodeLike("companyCodeLike");
        companyNewSelectDTO.setCompanyCodes(Arrays.asList("value"));
        companyNewSelectDTO.setCompanyNameCn("companyNameCn");
        companyNewSelectDTO.setProvinceId("provinceId");
        companyNewSelectDTO.setCityId("cityId");
        final HttpEntity<CompanyNewSelectDTO> requestEntity = new HttpEntity<>(companyNewSelectDTO, new HttpHeaders());
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST, requestEntity, ResponseDTO.class))
                .thenReturn(responseDTOResponseEntity);

        // Configure CompanyClient.selectListByCodeList(...).
        final TmCompany tmCompany = new TmCompany();
        tmCompany.setId(0L);
        tmCompany.setCompanyCode("companyCode");
        tmCompany.setCompanyType(0);
        tmCompany.setCompanyNameCn("companyNameCn");
        tmCompany.setCompanyNameEn("companyNameEn");
        final List<TmCompany> tmCompanies = Arrays.asList(tmCompany);
        when(mockCompanyClient.selectListByCodeList(Arrays.asList("value"))).thenReturn(tmCompanies);

        when(mockVehicleInfoClient.getVehicleVOByVin("vin")).thenReturn(null);

        // Run the test
        assertThatThrownBy(
                () -> tmClueMigrateTaskServiceImplUnderTest.createVinToDealerTask(clueMigrateTaskAddDTO))
                .isInstanceOf(DALException.class);
    }
}
