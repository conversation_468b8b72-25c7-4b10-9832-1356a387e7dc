package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillNoticeInvoiceInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillNoticeInvoiceInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillNoticeInvoiceInfoPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillNoticeInvoiceInfoServiceImplTest {

    @Mock
    private GoodwillNoticeInvoiceInfoMapper mockGoodwillNoticeInvoiceInfoMapper;

    private GoodwillNoticeInvoiceInfoServiceImpl goodwillNoticeInvoiceInfoServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        goodwillNoticeInvoiceInfoServiceImplUnderTest = new GoodwillNoticeInvoiceInfoServiceImpl();
        goodwillNoticeInvoiceInfoServiceImplUnderTest.goodwillNoticeInvoiceInfoMapper = mockGoodwillNoticeInvoiceInfoMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO = new GoodwillNoticeInvoiceInfoDTO();
        goodwillNoticeInvoiceInfoDTO.setConsumeId("consumeId");
        goodwillNoticeInvoiceInfoDTO.setVoucherStatus(0);
        goodwillNoticeInvoiceInfoDTO.setVoucherTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillNoticeInvoiceInfoDTO.setCreditsStatus(0);
        goodwillNoticeInvoiceInfoDTO.setCreditsTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure GoodwillNoticeInvoiceInfoMapper.selectPageBySql(...).
        final GoodwillNoticeInvoiceInfoPO goodwillNoticeInvoiceInfoPO = new GoodwillNoticeInvoiceInfoPO();
        goodwillNoticeInvoiceInfoPO.setConsumeId("consumeId");
        goodwillNoticeInvoiceInfoPO.setVoucherStatus(0);
        goodwillNoticeInvoiceInfoPO.setVoucherTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillNoticeInvoiceInfoPO.setCreditsStatus(0);
        goodwillNoticeInvoiceInfoPO.setCreditsTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<GoodwillNoticeInvoiceInfoPO> goodwillNoticeInvoiceInfoPOS = Arrays.asList(
                goodwillNoticeInvoiceInfoPO);
        when(mockGoodwillNoticeInvoiceInfoMapper.selectPageBySql(any(Page.class),
                any(GoodwillNoticeInvoiceInfoPO.class))).thenReturn(goodwillNoticeInvoiceInfoPOS);

        // Run the test
        final IPage<GoodwillNoticeInvoiceInfoDTO> result = goodwillNoticeInvoiceInfoServiceImplUnderTest.selectPageBysql(
                page, goodwillNoticeInvoiceInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillNoticeInvoiceInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO = new GoodwillNoticeInvoiceInfoDTO();
        goodwillNoticeInvoiceInfoDTO.setConsumeId("consumeId");
        goodwillNoticeInvoiceInfoDTO.setVoucherStatus(0);
        goodwillNoticeInvoiceInfoDTO.setVoucherTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillNoticeInvoiceInfoDTO.setCreditsStatus(0);
        goodwillNoticeInvoiceInfoDTO.setCreditsTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockGoodwillNoticeInvoiceInfoMapper.selectPageBySql(any(Page.class),
                any(GoodwillNoticeInvoiceInfoPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillNoticeInvoiceInfoDTO> result = goodwillNoticeInvoiceInfoServiceImplUnderTest.selectPageBysql(
                page, goodwillNoticeInvoiceInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO = new GoodwillNoticeInvoiceInfoDTO();
        goodwillNoticeInvoiceInfoDTO.setConsumeId("consumeId");
        goodwillNoticeInvoiceInfoDTO.setVoucherStatus(0);
        goodwillNoticeInvoiceInfoDTO.setVoucherTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillNoticeInvoiceInfoDTO.setCreditsStatus(0);
        goodwillNoticeInvoiceInfoDTO.setCreditsTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure GoodwillNoticeInvoiceInfoMapper.selectListBySql(...).
        final GoodwillNoticeInvoiceInfoPO goodwillNoticeInvoiceInfoPO = new GoodwillNoticeInvoiceInfoPO();
        goodwillNoticeInvoiceInfoPO.setConsumeId("consumeId");
        goodwillNoticeInvoiceInfoPO.setVoucherStatus(0);
        goodwillNoticeInvoiceInfoPO.setVoucherTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillNoticeInvoiceInfoPO.setCreditsStatus(0);
        goodwillNoticeInvoiceInfoPO.setCreditsTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<GoodwillNoticeInvoiceInfoPO> goodwillNoticeInvoiceInfoPOS = Arrays.asList(
                goodwillNoticeInvoiceInfoPO);
        when(mockGoodwillNoticeInvoiceInfoMapper.selectListBySql(any(GoodwillNoticeInvoiceInfoPO.class)))
                .thenReturn(goodwillNoticeInvoiceInfoPOS);

        // Run the test
        final List<GoodwillNoticeInvoiceInfoDTO> result = goodwillNoticeInvoiceInfoServiceImplUnderTest.selectListBySql(
                goodwillNoticeInvoiceInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillNoticeInvoiceInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO = new GoodwillNoticeInvoiceInfoDTO();
        goodwillNoticeInvoiceInfoDTO.setConsumeId("consumeId");
        goodwillNoticeInvoiceInfoDTO.setVoucherStatus(0);
        goodwillNoticeInvoiceInfoDTO.setVoucherTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillNoticeInvoiceInfoDTO.setCreditsStatus(0);
        goodwillNoticeInvoiceInfoDTO.setCreditsTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockGoodwillNoticeInvoiceInfoMapper.selectListBySql(any(GoodwillNoticeInvoiceInfoPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillNoticeInvoiceInfoDTO> result = goodwillNoticeInvoiceInfoServiceImplUnderTest.selectListBySql(
                goodwillNoticeInvoiceInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure GoodwillNoticeInvoiceInfoMapper.selectById(...).
        final GoodwillNoticeInvoiceInfoPO goodwillNoticeInvoiceInfoPO = new GoodwillNoticeInvoiceInfoPO();
        goodwillNoticeInvoiceInfoPO.setConsumeId("consumeId");
        goodwillNoticeInvoiceInfoPO.setVoucherStatus(0);
        goodwillNoticeInvoiceInfoPO.setVoucherTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillNoticeInvoiceInfoPO.setCreditsStatus(0);
        goodwillNoticeInvoiceInfoPO.setCreditsTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockGoodwillNoticeInvoiceInfoMapper.selectById(0L)).thenReturn(goodwillNoticeInvoiceInfoPO);

        // Run the test
        final GoodwillNoticeInvoiceInfoDTO result = goodwillNoticeInvoiceInfoServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillNoticeInvoiceInfoMapperReturnsNull() {
        // Setup
        when(mockGoodwillNoticeInvoiceInfoMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillNoticeInvoiceInfoServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO = new GoodwillNoticeInvoiceInfoDTO();
        goodwillNoticeInvoiceInfoDTO.setConsumeId("consumeId");
        goodwillNoticeInvoiceInfoDTO.setVoucherStatus(0);
        goodwillNoticeInvoiceInfoDTO.setVoucherTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillNoticeInvoiceInfoDTO.setCreditsStatus(0);
        goodwillNoticeInvoiceInfoDTO.setCreditsTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockGoodwillNoticeInvoiceInfoMapper.insert(any(GoodwillNoticeInvoiceInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillNoticeInvoiceInfoServiceImplUnderTest.insert(goodwillNoticeInvoiceInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDTO = new GoodwillNoticeInvoiceInfoDTO();
        goodwillNoticeInvoiceInfoDTO.setConsumeId("consumeId");
        goodwillNoticeInvoiceInfoDTO.setVoucherStatus(0);
        goodwillNoticeInvoiceInfoDTO.setVoucherTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillNoticeInvoiceInfoDTO.setCreditsStatus(0);
        goodwillNoticeInvoiceInfoDTO.setCreditsTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure GoodwillNoticeInvoiceInfoMapper.selectById(...).
        final GoodwillNoticeInvoiceInfoPO goodwillNoticeInvoiceInfoPO = new GoodwillNoticeInvoiceInfoPO();
        goodwillNoticeInvoiceInfoPO.setConsumeId("consumeId");
        goodwillNoticeInvoiceInfoPO.setVoucherStatus(0);
        goodwillNoticeInvoiceInfoPO.setVoucherTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillNoticeInvoiceInfoPO.setCreditsStatus(0);
        goodwillNoticeInvoiceInfoPO.setCreditsTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockGoodwillNoticeInvoiceInfoMapper.selectById(0L)).thenReturn(goodwillNoticeInvoiceInfoPO);

        when(mockGoodwillNoticeInvoiceInfoMapper.updateById(any(GoodwillNoticeInvoiceInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillNoticeInvoiceInfoServiceImplUnderTest.update(0L, goodwillNoticeInvoiceInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
