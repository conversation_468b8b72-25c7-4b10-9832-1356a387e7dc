package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintNotCloseCaseReasonMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ReasonsforFiveDto;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintNotCloseCaseReasonPO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintNotCloseCaseReasonTestPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintNotCloseCaseReasonServiceImplTest {

    @Mock
    private ComplaintNotCloseCaseReasonMapper mockComplaintNotCloseCaseReasonMapper;

    private ComplaintNotCloseCaseReasonServiceImpl complaintNotCloseCaseReasonServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        complaintNotCloseCaseReasonServiceImplUnderTest = new ComplaintNotCloseCaseReasonServiceImpl();
        complaintNotCloseCaseReasonServiceImplUnderTest.complaintNotCloseCaseReasonMapper = mockComplaintNotCloseCaseReasonMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO = new ComplaintNotCloseCaseReasonDTO();
        complaintNotCloseCaseReasonDTO.setFollowerName("followerName");
        complaintNotCloseCaseReasonDTO.setComplaintInfoId(0L);
        complaintNotCloseCaseReasonDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintNotCloseCaseReasonDTO.setFollower("follower");
        complaintNotCloseCaseReasonDTO.setBigClass(0);
        complaintNotCloseCaseReasonDTO.setBigClassName("bigClassName");
        complaintNotCloseCaseReasonDTO.setSmallClass("smallClass");
        complaintNotCloseCaseReasonDTO.setSmallClassName("smallClassName");
        complaintNotCloseCaseReasonDTO.setOther("input1");
        complaintNotCloseCaseReasonDTO.setDuration(0);

        // Configure ComplaintNotCloseCaseReasonMapper.selectPageBySql(...).
        final ComplaintNotCloseCaseReasonPO complaintNotCloseCaseReasonPO = new ComplaintNotCloseCaseReasonPO();
        complaintNotCloseCaseReasonPO.setOwnerCode("ownerCode");
        complaintNotCloseCaseReasonPO.setFollowerName("followerName");
        complaintNotCloseCaseReasonPO.setOrgId(0);
        complaintNotCloseCaseReasonPO.setId(0L);
        complaintNotCloseCaseReasonPO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<ComplaintNotCloseCaseReasonPO> complaintNotCloseCaseReasonPOS = Arrays.asList(
                complaintNotCloseCaseReasonPO);
        when(mockComplaintNotCloseCaseReasonMapper.selectPageBySql(any(Page.class),
                any(ComplaintNotCloseCaseReasonPO.class))).thenReturn(complaintNotCloseCaseReasonPOS);

        // Run the test
        final IPage<ComplaintNotCloseCaseReasonDTO> result = complaintNotCloseCaseReasonServiceImplUnderTest.selectPageBysql(
                page, complaintNotCloseCaseReasonDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintNotCloseCaseReasonMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO = new ComplaintNotCloseCaseReasonDTO();
        complaintNotCloseCaseReasonDTO.setFollowerName("followerName");
        complaintNotCloseCaseReasonDTO.setComplaintInfoId(0L);
        complaintNotCloseCaseReasonDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintNotCloseCaseReasonDTO.setFollower("follower");
        complaintNotCloseCaseReasonDTO.setBigClass(0);
        complaintNotCloseCaseReasonDTO.setBigClassName("bigClassName");
        complaintNotCloseCaseReasonDTO.setSmallClass("smallClass");
        complaintNotCloseCaseReasonDTO.setSmallClassName("smallClassName");
        complaintNotCloseCaseReasonDTO.setOther("input1");
        complaintNotCloseCaseReasonDTO.setDuration(0);

        when(mockComplaintNotCloseCaseReasonMapper.selectPageBySql(any(Page.class),
                any(ComplaintNotCloseCaseReasonPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintNotCloseCaseReasonDTO> result = complaintNotCloseCaseReasonServiceImplUnderTest.selectPageBysql(
                page, complaintNotCloseCaseReasonDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO = new ComplaintNotCloseCaseReasonDTO();
        complaintNotCloseCaseReasonDTO.setFollowerName("followerName");
        complaintNotCloseCaseReasonDTO.setComplaintInfoId(0L);
        complaintNotCloseCaseReasonDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintNotCloseCaseReasonDTO.setFollower("follower");
        complaintNotCloseCaseReasonDTO.setBigClass(0);
        complaintNotCloseCaseReasonDTO.setBigClassName("bigClassName");
        complaintNotCloseCaseReasonDTO.setSmallClass("smallClass");
        complaintNotCloseCaseReasonDTO.setSmallClassName("smallClassName");
        complaintNotCloseCaseReasonDTO.setOther("input1");
        complaintNotCloseCaseReasonDTO.setDuration(0);

        // Configure ComplaintNotCloseCaseReasonMapper.selectListBySql(...).
        final ComplaintNotCloseCaseReasonPO complaintNotCloseCaseReasonPO = new ComplaintNotCloseCaseReasonPO();
        complaintNotCloseCaseReasonPO.setOwnerCode("ownerCode");
        complaintNotCloseCaseReasonPO.setFollowerName("followerName");
        complaintNotCloseCaseReasonPO.setOrgId(0);
        complaintNotCloseCaseReasonPO.setId(0L);
        complaintNotCloseCaseReasonPO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<ComplaintNotCloseCaseReasonPO> complaintNotCloseCaseReasonPOS = Arrays.asList(
                complaintNotCloseCaseReasonPO);
        when(mockComplaintNotCloseCaseReasonMapper.selectListBySql(
                any(ComplaintNotCloseCaseReasonPO.class))).thenReturn(complaintNotCloseCaseReasonPOS);

        // Run the test
        final List<ComplaintNotCloseCaseReasonDTO> result = complaintNotCloseCaseReasonServiceImplUnderTest.selectListBySql(
                complaintNotCloseCaseReasonDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintNotCloseCaseReasonMapperReturnsNoItems() {
        // Setup
        final ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO = new ComplaintNotCloseCaseReasonDTO();
        complaintNotCloseCaseReasonDTO.setFollowerName("followerName");
        complaintNotCloseCaseReasonDTO.setComplaintInfoId(0L);
        complaintNotCloseCaseReasonDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintNotCloseCaseReasonDTO.setFollower("follower");
        complaintNotCloseCaseReasonDTO.setBigClass(0);
        complaintNotCloseCaseReasonDTO.setBigClassName("bigClassName");
        complaintNotCloseCaseReasonDTO.setSmallClass("smallClass");
        complaintNotCloseCaseReasonDTO.setSmallClassName("smallClassName");
        complaintNotCloseCaseReasonDTO.setOther("input1");
        complaintNotCloseCaseReasonDTO.setDuration(0);

        when(mockComplaintNotCloseCaseReasonMapper.selectListBySql(
                any(ComplaintNotCloseCaseReasonPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintNotCloseCaseReasonDTO> result = complaintNotCloseCaseReasonServiceImplUnderTest.selectListBySql(
                complaintNotCloseCaseReasonDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure ComplaintNotCloseCaseReasonMapper.selectById(...).
        final ComplaintNotCloseCaseReasonPO complaintNotCloseCaseReasonPO = new ComplaintNotCloseCaseReasonPO();
        complaintNotCloseCaseReasonPO.setOwnerCode("ownerCode");
        complaintNotCloseCaseReasonPO.setFollowerName("followerName");
        complaintNotCloseCaseReasonPO.setOrgId(0);
        complaintNotCloseCaseReasonPO.setId(0L);
        complaintNotCloseCaseReasonPO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockComplaintNotCloseCaseReasonMapper.selectById(0L)).thenReturn(complaintNotCloseCaseReasonPO);

        // Run the test
        final ComplaintNotCloseCaseReasonDTO result = complaintNotCloseCaseReasonServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintNotCloseCaseReasonMapperReturnsNull() {
        // Setup
        when(mockComplaintNotCloseCaseReasonMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintNotCloseCaseReasonServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO = new ComplaintNotCloseCaseReasonDTO();
        complaintNotCloseCaseReasonDTO.setFollowerName("followerName");
        complaintNotCloseCaseReasonDTO.setComplaintInfoId(0L);
        complaintNotCloseCaseReasonDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintNotCloseCaseReasonDTO.setFollower("follower");
        complaintNotCloseCaseReasonDTO.setBigClass(0);
        complaintNotCloseCaseReasonDTO.setBigClassName("bigClassName");
        complaintNotCloseCaseReasonDTO.setSmallClass("smallClass");
        complaintNotCloseCaseReasonDTO.setSmallClassName("smallClassName");
        complaintNotCloseCaseReasonDTO.setOther("input1");
        complaintNotCloseCaseReasonDTO.setDuration(0);

        when(mockComplaintNotCloseCaseReasonMapper.insert(any(ComplaintNotCloseCaseReasonPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintNotCloseCaseReasonServiceImplUnderTest.insert(complaintNotCloseCaseReasonDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final ComplaintNotCloseCaseReasonDTO complaintNotCloseCaseReasonDTO = new ComplaintNotCloseCaseReasonDTO();
        complaintNotCloseCaseReasonDTO.setFollowerName("followerName");
        complaintNotCloseCaseReasonDTO.setComplaintInfoId(0L);
        complaintNotCloseCaseReasonDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintNotCloseCaseReasonDTO.setFollower("follower");
        complaintNotCloseCaseReasonDTO.setBigClass(0);
        complaintNotCloseCaseReasonDTO.setBigClassName("bigClassName");
        complaintNotCloseCaseReasonDTO.setSmallClass("smallClass");
        complaintNotCloseCaseReasonDTO.setSmallClassName("smallClassName");
        complaintNotCloseCaseReasonDTO.setOther("input1");
        complaintNotCloseCaseReasonDTO.setDuration(0);

        // Configure ComplaintNotCloseCaseReasonMapper.selectById(...).
        final ComplaintNotCloseCaseReasonPO complaintNotCloseCaseReasonPO = new ComplaintNotCloseCaseReasonPO();
        complaintNotCloseCaseReasonPO.setOwnerCode("ownerCode");
        complaintNotCloseCaseReasonPO.setFollowerName("followerName");
        complaintNotCloseCaseReasonPO.setOrgId(0);
        complaintNotCloseCaseReasonPO.setId(0L);
        complaintNotCloseCaseReasonPO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockComplaintNotCloseCaseReasonMapper.selectById(0L)).thenReturn(complaintNotCloseCaseReasonPO);

        when(mockComplaintNotCloseCaseReasonMapper.updateById(any(ComplaintNotCloseCaseReasonPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintNotCloseCaseReasonServiceImplUnderTest.update(0L, complaintNotCloseCaseReasonDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectPageBysql3() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintNotCloseCaseReasonTestDTO complaintNotCloseCaseReasonTestDTO = new ComplaintNotCloseCaseReasonTestDTO();
        complaintNotCloseCaseReasonTestDTO.setClassCode("classCode");
        complaintNotCloseCaseReasonTestDTO.setClassName("className");
        complaintNotCloseCaseReasonTestDTO.setAppId("appId");
        complaintNotCloseCaseReasonTestDTO.setOwnerCode("ownerCode");
        complaintNotCloseCaseReasonTestDTO.setFollowerName("followerName");

        // Configure ComplaintNotCloseCaseReasonMapper.selectPageBySql2(...).
        final ComplaintNotCloseCaseReasonTestPO complaintNotCloseCaseReasonTestPO = new ComplaintNotCloseCaseReasonTestPO();
        complaintNotCloseCaseReasonTestPO.setClassCode("classCode");
        complaintNotCloseCaseReasonTestPO.setClassName("className");
        complaintNotCloseCaseReasonTestPO.setSmallClassNameOther("smallClassNameOther");
        complaintNotCloseCaseReasonTestPO.setOwnerCode("ownerCode");
        complaintNotCloseCaseReasonTestPO.setFollowerName("followerName");
        final List<ComplaintNotCloseCaseReasonTestPO> complaintNotCloseCaseReasonTestPOS = Arrays.asList(
                complaintNotCloseCaseReasonTestPO);
        when(mockComplaintNotCloseCaseReasonMapper.selectPageBySql2(any(Page.class),
                any(ComplaintNotCloseCaseReasonTestPO.class))).thenReturn(complaintNotCloseCaseReasonTestPOS);

        // Run the test
        final IPage<ComplaintNotCloseCaseReasonTestDTO> result = complaintNotCloseCaseReasonServiceImplUnderTest.selectPageBysql3(
                page, complaintNotCloseCaseReasonTestDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql3_ComplaintNotCloseCaseReasonMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintNotCloseCaseReasonTestDTO complaintNotCloseCaseReasonTestDTO = new ComplaintNotCloseCaseReasonTestDTO();
        complaintNotCloseCaseReasonTestDTO.setClassCode("classCode");
        complaintNotCloseCaseReasonTestDTO.setClassName("className");
        complaintNotCloseCaseReasonTestDTO.setAppId("appId");
        complaintNotCloseCaseReasonTestDTO.setOwnerCode("ownerCode");
        complaintNotCloseCaseReasonTestDTO.setFollowerName("followerName");

        when(mockComplaintNotCloseCaseReasonMapper.selectPageBySql2(any(Page.class),
                any(ComplaintNotCloseCaseReasonTestPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintNotCloseCaseReasonTestDTO> result = complaintNotCloseCaseReasonServiceImplUnderTest.selectPageBysql3(
                page, complaintNotCloseCaseReasonTestDTO);

        // Verify the results
    }
}
