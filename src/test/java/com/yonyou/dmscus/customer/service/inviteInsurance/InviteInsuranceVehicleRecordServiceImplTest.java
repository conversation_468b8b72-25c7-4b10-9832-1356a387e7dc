package com.yonyou.dmscus.customer.service.inviteInsurance;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.dao.faultLight.TtFaultCallDetailsMapper;
import com.yonyou.dmscus.customer.dao.faultLight.TtFaultCallRegisterMapper;
import com.yonyou.dmscus.customer.dao.inviteInsurance.*;
import com.yonyou.dmscus.customer.dao.voicemanage.CallDetailsMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.SaCustomerNumberMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.SaWorkNumberMapper;
import com.yonyou.dmscus.customer.dto.CheckRepairOrderDTO;
import com.yonyou.dmscus.customer.dto.RepairOrderVO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.UserInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.*;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillDTO;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.SaCustomerNumberDTO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultCallDetailsPO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultCallRegisterPO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.*;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaCustomerNumberPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaWorkNumberPO;
import com.yonyou.dmscus.customer.feign.DmscusRepairClient;
import com.yonyou.dmscus.customer.feign.RepairCommonClient;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.service.common.IMiddleGroundVehicleService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightService;
import com.yonyou.dmscus.customer.service.httplog.HttpLogAiService;
import com.yonyou.dmscus.customer.service.impl.voicemanage.WorkNumberServiceContext;
import com.yonyou.dmscus.customer.service.talkskill.TalkskillService;
import com.yonyou.dmscus.customer.service.voicemanage.CallDetailsService;
import com.yonyou.dmscus.customer.service.voicemanage.SaCustomerNumberService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteInsuranceVehicleRecordServiceImplTest {

    @Mock
    private InviteInsuranceVehicleRecordMapper mockInviteInsuranceVehicleRecordMapper;
    @Mock
    private InviteInsuranceVehicleRecordDetailService mockInviteInsuranceVehicleRecordDetailService;
    @Mock
    private InviteInsuranceVehicleSaRefMapper mockInviteInsuranceVehicleSaRefMapper;
    @Mock
    private TalkskillService mockTalkskillService;
    @Mock
    private InviteInsuranceVehicleTaskMapper mockInviteInsuranceVehicleTaskMapper;
    @Mock
    private InviteInsuranceCustomerInfoMapper mockInviteInsuranceCustomerInfoMapper;
    @Mock
    private CallDetailsMapper mockCallDetailsMapper;
    @Mock
    private CallDetailsService mockCallDetailsService;
    @Mock
    private WorkNumberServiceContext mockWorkNumberServiceContext;
    @Mock
    private RepairCommonClient mockRepairCommonClient;
    @Mock
    private ReportCommonClient mockReportCommonClient;
    @Mock
    private InviteInsuranceVehicleCustomerNumberMapper mockInviteInsuranceVehicleCustomerNumberMapper;
    @Mock
    private SaWorkNumberMapper mockSaWorkNumberMapper;
    @Mock
    private IMiddleGroundVehicleService mockIMiddleGroundVehicleService;
    @Mock
    private DmscusRepairClient mockDmscusRepairClient;
    @Mock
    private HttpLogAiService mockHttpLogAiService;
    @Mock
    private SaCustomerNumberService mockSaCustomerNumberService;
    @Mock
    private FaultLightService mockFaultLightService;
    @Mock
    private SaCustomerNumberMapper mockSaCustomerNumberMapper;
    @Mock
    private TtFaultCallRegisterMapper mockFaultCallRegisterMapper;
    @Mock
    private TtFaultCallDetailsMapper mockFaultCallDetailsMapper;

    private InviteInsuranceVehicleRecordServiceImpl inviteInsuranceVehicleRecordServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteInsuranceVehicleRecordServiceImplUnderTest = new InviteInsuranceVehicleRecordServiceImpl();
        inviteInsuranceVehicleRecordServiceImplUnderTest.inviteInsuranceVehicleRecordMapper = mockInviteInsuranceVehicleRecordMapper;
        inviteInsuranceVehicleRecordServiceImplUnderTest.inviteInsuranceVehicleRecordDetailService = mockInviteInsuranceVehicleRecordDetailService;
        inviteInsuranceVehicleRecordServiceImplUnderTest.inviteInsuranceVehicleSaRefMapper = mockInviteInsuranceVehicleSaRefMapper;
        inviteInsuranceVehicleRecordServiceImplUnderTest.talkskillService = mockTalkskillService;
        inviteInsuranceVehicleRecordServiceImplUnderTest.inviteInsuranceVehicleTaskMapper = mockInviteInsuranceVehicleTaskMapper;
        inviteInsuranceVehicleRecordServiceImplUnderTest.inviteInsuranceCustomerInfoMapper = mockInviteInsuranceCustomerInfoMapper;
        inviteInsuranceVehicleRecordServiceImplUnderTest.callDetailsMapper = mockCallDetailsMapper;
        inviteInsuranceVehicleRecordServiceImplUnderTest.callDetailsService = mockCallDetailsService;
        inviteInsuranceVehicleRecordServiceImplUnderTest.workNumberServiceContext = mockWorkNumberServiceContext;
        inviteInsuranceVehicleRecordServiceImplUnderTest.repairCommonClient = mockRepairCommonClient;
        inviteInsuranceVehicleRecordServiceImplUnderTest.reportCommonClient = mockReportCommonClient;
        inviteInsuranceVehicleRecordServiceImplUnderTest.inviteInsuranceVehicleCustomerNumberMapper = mockInviteInsuranceVehicleCustomerNumberMapper;
        inviteInsuranceVehicleRecordServiceImplUnderTest.saWorkNumberMapper = mockSaWorkNumberMapper;
        inviteInsuranceVehicleRecordServiceImplUnderTest.URL_MAPPING_REGISTER = "URL_MAPPING_REGISTER";
        inviteInsuranceVehicleRecordServiceImplUnderTest.iMiddleGroundVehicleService = mockIMiddleGroundVehicleService;
        inviteInsuranceVehicleRecordServiceImplUnderTest.dmscusRepairClient = mockDmscusRepairClient;
        inviteInsuranceVehicleRecordServiceImplUnderTest.httpLogAiService = mockHttpLogAiService;
        inviteInsuranceVehicleRecordServiceImplUnderTest.saCustomerNumberService = mockSaCustomerNumberService;
        inviteInsuranceVehicleRecordServiceImplUnderTest.faultLightService = mockFaultLightService;
        inviteInsuranceVehicleRecordServiceImplUnderTest.saCustomerNumberMapper = mockSaCustomerNumberMapper;
        inviteInsuranceVehicleRecordServiceImplUnderTest.faultCallRegisterMapper = mockFaultCallRegisterMapper;
        inviteInsuranceVehicleRecordServiceImplUnderTest.faultCallDetailsMapper = mockFaultCallDetailsMapper;
    }
    @Test
    void testUpdateInsureFollowStatus() {
        // Setup
        // Configure InviteInsuranceVehicleRecordMapper.selectInsuranceInvitePlan(...).
        final InviteInsuranceVehicleRecordPO inviteInsuranceVehicleRecordPO = new InviteInsuranceVehicleRecordPO();
        inviteInsuranceVehicleRecordPO.setId(0L);
        inviteInsuranceVehicleRecordPO.setVin("vin");
        inviteInsuranceVehicleRecordPO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setPlanFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setActualFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setFirstFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setSaId("saId");
        inviteInsuranceVehicleRecordPO.setSaName("saName");
        inviteInsuranceVehicleRecordPO.setFollowStatus(0);
        inviteInsuranceVehicleRecordPO.setDealerCode("dealerCode");
        inviteInsuranceVehicleRecordPO.setIsNoDistribute(0);
        inviteInsuranceVehicleRecordPO.setIsWaitDistribute(0);
        inviteInsuranceVehicleRecordPO.setClueType(0);
        inviteInsuranceVehicleRecordPO.setLoseReason(0);
        inviteInsuranceVehicleRecordPO.setContent("content");
        inviteInsuranceVehicleRecordPO.setScore(0);
        inviteInsuranceVehicleRecordPO.setCallLength(0);
        inviteInsuranceVehicleRecordPO.setCallDetailId(0L);
        inviteInsuranceVehicleRecordPO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setInsuranceId(0L);
        final List<InviteInsuranceVehicleRecordPO> inviteInsuranceVehicleRecordPOS = Arrays.asList(
                inviteInsuranceVehicleRecordPO);
        when(mockInviteInsuranceVehicleRecordMapper.selectInsuranceInvitePlan())
                .thenReturn(inviteInsuranceVehicleRecordPOS);

        // Configure ReportCommonClient.checkRepairOrderByVin(...).
        final CheckRepairOrderDTO checkRepairOrderDTO = new CheckRepairOrderDTO();
        checkRepairOrderDTO.setVin("vin");
        checkRepairOrderDTO.setIsEnterFactory(0);
        checkRepairOrderDTO.setOwnerCode("ownerCode");
        final RepairOrderVO repairOrderVO = new RepairOrderVO();
        repairOrderVO.setOwnerCode("ownerCode");
        checkRepairOrderDTO.setOrderVOList(Arrays.asList(repairOrderVO));
        when(mockReportCommonClient.checkRepairOrderByVin("vin")).thenReturn(checkRepairOrderDTO);

        // Run the test
        inviteInsuranceVehicleRecordServiceImplUnderTest.updateInsureFollowStatus();

        // Verify the results
//        verify(mockInviteInsuranceVehicleRecordMapper)
        mockInviteInsuranceVehicleRecordMapper.updateInsureFollowStatus(Arrays.asList(0L), "99999");
//        verify(mockInviteInsuranceVehicleRecordMapper)
        mockInviteInsuranceVehicleRecordMapper.updateInsureFollowTaskStatus(Arrays.asList(0L), "99999");
//        verify(mockInviteInsuranceVehicleRecordMapper)
        mockInviteInsuranceVehicleRecordMapper.insertInsureInviteTask(Arrays.asList(0L), "99999");
    }

    @Test
    void testUpdateInsureFollowStatus_ReportCommonClientReturnsNull() {
        // Setup
        // Configure InviteInsuranceVehicleRecordMapper.selectInsuranceInvitePlan(...).
        final InviteInsuranceVehicleRecordPO inviteInsuranceVehicleRecordPO = new InviteInsuranceVehicleRecordPO();
        inviteInsuranceVehicleRecordPO.setId(0L);
        inviteInsuranceVehicleRecordPO.setVin("vin");
        inviteInsuranceVehicleRecordPO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setPlanFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setActualFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setFirstFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setSaId("saId");
        inviteInsuranceVehicleRecordPO.setSaName("saName");
        inviteInsuranceVehicleRecordPO.setFollowStatus(0);
        inviteInsuranceVehicleRecordPO.setDealerCode("dealerCode");
        inviteInsuranceVehicleRecordPO.setIsNoDistribute(0);
        inviteInsuranceVehicleRecordPO.setIsWaitDistribute(0);
        inviteInsuranceVehicleRecordPO.setClueType(0);
        inviteInsuranceVehicleRecordPO.setLoseReason(0);
        inviteInsuranceVehicleRecordPO.setContent("content");
        inviteInsuranceVehicleRecordPO.setScore(0);
        inviteInsuranceVehicleRecordPO.setCallLength(0);
        inviteInsuranceVehicleRecordPO.setCallDetailId(0L);
        inviteInsuranceVehicleRecordPO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setInsuranceId(0L);
        final List<InviteInsuranceVehicleRecordPO> inviteInsuranceVehicleRecordPOS = Arrays.asList(
                inviteInsuranceVehicleRecordPO);
        when(mockInviteInsuranceVehicleRecordMapper.selectInsuranceInvitePlan())
                .thenReturn(inviteInsuranceVehicleRecordPOS);

        when(mockReportCommonClient.checkRepairOrderByVin("vin")).thenReturn(null);

        // Run the test
        inviteInsuranceVehicleRecordServiceImplUnderTest.updateInsureFollowStatus();

        // Verify the results
//        verify(mockInviteInsuranceVehicleRecordMapper)
        mockInviteInsuranceVehicleRecordMapper.updateInsureFollowStatus(Arrays.asList(0L), "99999");
//        verify(mockInviteInsuranceVehicleRecordMapper)
        mockInviteInsuranceVehicleRecordMapper.updateInsureFollowTaskStatus(Arrays.asList(0L), "99999");
//        verify(mockInviteInsuranceVehicleRecordMapper)
        mockInviteInsuranceVehicleRecordMapper.insertInsureInviteTask(Arrays.asList(0L), "99999");
    }

    @Test
    void testSelectPageBysql() {
        assertThat(inviteInsuranceVehicleRecordServiceImplUnderTest.selectPageBysql(new Page<>(0L, 0L, 0L, false),
                new InviteInsuranceVehicleRecordDTO())).isNull();
    }

    @Test
    void testSelectListBySql() {
        assertThat(inviteInsuranceVehicleRecordServiceImplUnderTest.selectListBySql(
                new InviteInsuranceVehicleRecordDTO())).isNull();
    }

    @Test
    void testGetById() {
        assertThat(inviteInsuranceVehicleRecordServiceImplUnderTest.getById(0L)).isNull();
    }

    @Test
    void testInsert() {
        assertThat(inviteInsuranceVehicleRecordServiceImplUnderTest.insert(
                new InviteInsuranceVehicleRecordDTO())).isEqualTo(0);
    }

    @Test
    void testDeleteById() {
        assertThat(inviteInsuranceVehicleRecordServiceImplUnderTest.deleteById(0L)).isEqualTo(0);
    }

    @Test
    void testDeleteBatchIds() {
        assertThat(inviteInsuranceVehicleRecordServiceImplUnderTest.deleteBatchIds("ids")).isEqualTo(0);
    }

    @Test
    void testGetUUID32() {
//        assertThat(inviteInsuranceVehicleRecordServiceImplUnderTest.getUUID32()).isEqualTo("result");
    }

    @Test
    void testCallDetailList() {
        // Setup
        final CallDetailsPO callDetailsPO = new CallDetailsPO();
        callDetailsPO.setOwnerCode("ownerCode");
        callDetailsPO.setOrgId(0);
        callDetailsPO.setId(0L);
        callDetailsPO.setSessionId("sessionId");
        callDetailsPO.setCallId("callId");
        final List<CallDetailsPO> expectedResult = Arrays.asList(callDetailsPO);

        // Configure CallDetailsMapper.getDetailsByInsuranceDetailId(...).
        final CallDetailsPO callDetailsPO1 = new CallDetailsPO();
        callDetailsPO1.setOwnerCode("ownerCode");
        callDetailsPO1.setOrgId(0);
        callDetailsPO1.setId(0L);
        callDetailsPO1.setSessionId("sessionId");
        callDetailsPO1.setCallId("callId");
        final List<CallDetailsPO> callDetailsPOS = Arrays.asList(callDetailsPO1);
        when(mockCallDetailsMapper.getDetailsByInsuranceDetailId(0L)).thenReturn(callDetailsPOS);

        // Run the test
        final List<CallDetailsPO> result = inviteInsuranceVehicleRecordServiceImplUnderTest.callDetailList(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testCallDetailList_CallDetailsMapperReturnsNoItems() {
        // Setup
        when(mockCallDetailsMapper.getDetailsByInsuranceDetailId(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<CallDetailsPO> result = inviteInsuranceVehicleRecordServiceImplUnderTest.callDetailList(0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectCusList() {
        // Setup
        final InviteInsuranceVehicleCustomerNumberPO inviteInsuranceVehicleCustomerNumberPO = new InviteInsuranceVehicleCustomerNumberPO();
        inviteInsuranceVehicleCustomerNumberPO.setInsuranceId(0L);
        inviteInsuranceVehicleCustomerNumberPO.setInsuranceDetailId(0L);
        inviteInsuranceVehicleCustomerNumberPO.setCallId("callId");
        inviteInsuranceVehicleCustomerNumberPO.setSaId("saId");
        inviteInsuranceVehicleCustomerNumberPO.setCusName("insureName");
        inviteInsuranceVehicleCustomerNumberPO.setCusNumber("insureNumber");
        inviteInsuranceVehicleCustomerNumberPO.setDealerCode("dealerCode");
        inviteInsuranceVehicleCustomerNumberPO.setSaName("saName");
        inviteInsuranceVehicleCustomerNumberPO.setSaNumber("saNumber");
        inviteInsuranceVehicleCustomerNumberPO.setWorkNumber("workNumber");
        inviteInsuranceVehicleCustomerNumberPO.setBatchNo("batchNo");
        final List<InviteInsuranceVehicleCustomerNumberPO> expectedResult = Arrays.asList(
                inviteInsuranceVehicleCustomerNumberPO);

        // Configure InviteInsuranceVehicleCustomerNumberMapper.selectCusList(...).
        final InviteInsuranceVehicleCustomerNumberPO inviteInsuranceVehicleCustomerNumberPO1 = new InviteInsuranceVehicleCustomerNumberPO();
        inviteInsuranceVehicleCustomerNumberPO1.setInsuranceId(0L);
        inviteInsuranceVehicleCustomerNumberPO1.setInsuranceDetailId(0L);
        inviteInsuranceVehicleCustomerNumberPO1.setCallId("callId");
        inviteInsuranceVehicleCustomerNumberPO1.setSaId("saId");
        inviteInsuranceVehicleCustomerNumberPO1.setCusName("insureName");
        inviteInsuranceVehicleCustomerNumberPO1.setCusNumber("insureNumber");
        inviteInsuranceVehicleCustomerNumberPO1.setDealerCode("dealerCode");
        inviteInsuranceVehicleCustomerNumberPO1.setSaName("saName");
        inviteInsuranceVehicleCustomerNumberPO1.setSaNumber("saNumber");
        inviteInsuranceVehicleCustomerNumberPO1.setWorkNumber("workNumber");
        inviteInsuranceVehicleCustomerNumberPO1.setBatchNo("batchNo");
        final List<InviteInsuranceVehicleCustomerNumberPO> inviteInsuranceVehicleCustomerNumberPOS = Arrays.asList(
                inviteInsuranceVehicleCustomerNumberPO1);
        when(mockInviteInsuranceVehicleCustomerNumberMapper.selectCusList(0L))
                .thenReturn(inviteInsuranceVehicleCustomerNumberPOS);

        // Run the test
        final List<InviteInsuranceVehicleCustomerNumberPO> result = inviteInsuranceVehicleRecordServiceImplUnderTest.selectCusList(
                0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectCusList_InviteInsuranceVehicleCustomerNumberMapperReturnsNoItems() {
        // Setup
        when(mockInviteInsuranceVehicleCustomerNumberMapper.selectCusList(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteInsuranceVehicleCustomerNumberPO> result = inviteInsuranceVehicleRecordServiceImplUnderTest.selectCusList(
                0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testUpdateRecordOrderStatus() {
        // Setup
        // Configure InviteInsuranceVehicleRecordMapper.selectLossOfRecord(...).
        final InviteInsuranceVehicleRecordPO inviteInsuranceVehicleRecordPO = new InviteInsuranceVehicleRecordPO();
        inviteInsuranceVehicleRecordPO.setId(0L);
        inviteInsuranceVehicleRecordPO.setVin("vin");
        inviteInsuranceVehicleRecordPO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setPlanFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setActualFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setFirstFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setSaId("saId");
        inviteInsuranceVehicleRecordPO.setSaName("saName");
        inviteInsuranceVehicleRecordPO.setFollowStatus(0);
        inviteInsuranceVehicleRecordPO.setDealerCode("dealerCode");
        inviteInsuranceVehicleRecordPO.setIsNoDistribute(0);
        inviteInsuranceVehicleRecordPO.setIsWaitDistribute(0);
        inviteInsuranceVehicleRecordPO.setClueType(0);
        inviteInsuranceVehicleRecordPO.setLoseReason(0);
        inviteInsuranceVehicleRecordPO.setContent("content");
        inviteInsuranceVehicleRecordPO.setScore(0);
        inviteInsuranceVehicleRecordPO.setCallLength(0);
        inviteInsuranceVehicleRecordPO.setCallDetailId(0L);
        inviteInsuranceVehicleRecordPO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setInsuranceId(0L);
        final List<InviteInsuranceVehicleRecordPO> inviteInsuranceVehicleRecordPOS = Arrays.asList(
                inviteInsuranceVehicleRecordPO);
        when(mockInviteInsuranceVehicleRecordMapper.selectLossOfRecord()).thenReturn(inviteInsuranceVehicleRecordPOS);

        when(mockIMiddleGroundVehicleService.queryVehicleInfo(any(Page.class), eq(new HashMap<>()))).thenReturn(null);

        // Configure ReportCommonClient.selectRepairOrderByVin2(...).
        final CheckRepairOrderDTO checkRepairOrderDTO = new CheckRepairOrderDTO();
        checkRepairOrderDTO.setVin("vin");
        checkRepairOrderDTO.setIsEnterFactory(0);
        checkRepairOrderDTO.setOwnerCode("ownerCode");
        final RepairOrderVO repairOrderVO = new RepairOrderVO();
        repairOrderVO.setOwnerCode("ownerCode");
        checkRepairOrderDTO.setOrderVOList(Arrays.asList(repairOrderVO));
        when(mockReportCommonClient.selectRepairOrderByVin2("vin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(checkRepairOrderDTO);

        // Configure InviteInsuranceVehicleTaskMapper.getInvitationDlrRule(...).
        final InviteInsuranceRulePO inviteInsuranceRulePO = new InviteInsuranceRulePO();
        inviteInsuranceRulePO.setOwnerCode("ownerCode");
        inviteInsuranceRulePO.setOwnerParCode("ownerParCode");
        inviteInsuranceRulePO.setDayInAdvance(1);
        inviteInsuranceRulePO.setRemindInterval(0);
        inviteInsuranceRulePO.setCloseInterval(0);
        when(mockInviteInsuranceVehicleTaskMapper.getInvitationDlrRule("dealerCode", 0, 0))
                .thenReturn(inviteInsuranceRulePO);

        // Configure DmscusRepairClient.selectInsuranceByVin(...).
        final InsuranceBillDTO insuranceBillDTO = new InsuranceBillDTO();
        insuranceBillDTO.setId(0L);
        insuranceBillDTO.setDealerCode("dealerCode");
        insuranceBillDTO.setInsuranceNo("insuranceNo");
        insuranceBillDTO.setInsuranceType(0);
        insuranceBillDTO.setInsuranceStatus(0);
        when(mockDmscusRepairClient.selectInsuranceByVin("vin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(insuranceBillDTO);

        // Run the test
        inviteInsuranceVehicleRecordServiceImplUnderTest.updateRecordOrderStatus();

        // Verify the results
        verify(mockHttpLogAiService).saveHttpLog("更新线索状态（流失客户的逻辑）任务", "", "", "POST", "200", "更新线索状态（流失客户的逻辑）任务开始",
                "2222");

        // Confirm InviteInsuranceVehicleTaskMapper.insert(...).
        final InviteInsuranceVehicleTaskPO entity = new InviteInsuranceVehicleTaskPO();
        entity.setOwnerCode("ownerCode");
        entity.setOwnerParCode("ownerParCode");
        entity.setOrgId(0);
        entity.setId(0L);
        entity.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        verify(mockInviteInsuranceVehicleTaskMapper)
        mockInviteInsuranceVehicleTaskMapper.insert(entity);
//        verify(mockInviteInsuranceVehicleRecordMapper)
        mockInviteInsuranceVehicleRecordMapper.updateRecordOrderStatus();
    }

    @Test
    void testUpdateRecordOrderStatus_InviteInsuranceVehicleRecordMapperSelectLossOfRecordReturnsNoItems() {
        // Setup
        when(mockInviteInsuranceVehicleRecordMapper.selectLossOfRecord()).thenReturn(Collections.emptyList());

        // Run the test
        inviteInsuranceVehicleRecordServiceImplUnderTest.updateRecordOrderStatus();

        // Verify the results
        verify(mockHttpLogAiService).saveHttpLog("更新线索状态（流失客户的逻辑）任务", "", "", "POST", "200", "更新线索状态（流失客户的逻辑）任务开始",
                "2222");
        verify(mockInviteInsuranceVehicleRecordMapper).updateRecordOrderStatus();
    }

    @Test
    void testUpdateRecordOrderStatus_IMiddleGroundVehicleServiceReturnsNull() {
        // Setup
        // Configure InviteInsuranceVehicleRecordMapper.selectLossOfRecord(...).
        final InviteInsuranceVehicleRecordPO inviteInsuranceVehicleRecordPO = new InviteInsuranceVehicleRecordPO();
        inviteInsuranceVehicleRecordPO.setId(0L);
        inviteInsuranceVehicleRecordPO.setVin("vin");
        inviteInsuranceVehicleRecordPO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setPlanFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setActualFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setFirstFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setSaId("saId");
        inviteInsuranceVehicleRecordPO.setSaName("saName");
        inviteInsuranceVehicleRecordPO.setFollowStatus(0);
        inviteInsuranceVehicleRecordPO.setDealerCode("dealerCode");
        inviteInsuranceVehicleRecordPO.setIsNoDistribute(0);
        inviteInsuranceVehicleRecordPO.setIsWaitDistribute(0);
        inviteInsuranceVehicleRecordPO.setClueType(0);
        inviteInsuranceVehicleRecordPO.setLoseReason(0);
        inviteInsuranceVehicleRecordPO.setContent("content");
        inviteInsuranceVehicleRecordPO.setScore(0);
        inviteInsuranceVehicleRecordPO.setCallLength(0);
        inviteInsuranceVehicleRecordPO.setCallDetailId(0L);
        inviteInsuranceVehicleRecordPO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setInsuranceId(0L);
        final List<InviteInsuranceVehicleRecordPO> inviteInsuranceVehicleRecordPOS = Arrays.asList(
                inviteInsuranceVehicleRecordPO);
        when(mockInviteInsuranceVehicleRecordMapper.selectLossOfRecord()).thenReturn(inviteInsuranceVehicleRecordPOS);

        when(mockIMiddleGroundVehicleService.queryVehicleInfo(any(Page.class), eq(new HashMap<>()))).thenReturn(null);

        // Configure ReportCommonClient.selectRepairOrderByVin2(...).
        final CheckRepairOrderDTO checkRepairOrderDTO = new CheckRepairOrderDTO();
        checkRepairOrderDTO.setVin("vin");
        checkRepairOrderDTO.setIsEnterFactory(0);
        checkRepairOrderDTO.setOwnerCode("ownerCode");
        final RepairOrderVO repairOrderVO = new RepairOrderVO();
        repairOrderVO.setOwnerCode("ownerCode");
        checkRepairOrderDTO.setOrderVOList(Arrays.asList(repairOrderVO));
        when(mockReportCommonClient.selectRepairOrderByVin2("vin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(checkRepairOrderDTO);

        // Configure InviteInsuranceVehicleTaskMapper.getInvitationDlrRule(...).
        final InviteInsuranceRulePO inviteInsuranceRulePO = new InviteInsuranceRulePO();
        inviteInsuranceRulePO.setOwnerCode("ownerCode");
        inviteInsuranceRulePO.setOwnerParCode("ownerParCode");
        inviteInsuranceRulePO.setDayInAdvance(1);
        inviteInsuranceRulePO.setRemindInterval(0);
        inviteInsuranceRulePO.setCloseInterval(0);
        when(mockInviteInsuranceVehicleTaskMapper.getInvitationDlrRule("dealerCode", 0, 0))
                .thenReturn(inviteInsuranceRulePO);

        // Configure DmscusRepairClient.selectInsuranceByVin(...).
        final InsuranceBillDTO insuranceBillDTO = new InsuranceBillDTO();
        insuranceBillDTO.setId(0L);
        insuranceBillDTO.setDealerCode("dealerCode");
        insuranceBillDTO.setInsuranceNo("insuranceNo");
        insuranceBillDTO.setInsuranceType(0);
        insuranceBillDTO.setInsuranceStatus(0);
        when(mockDmscusRepairClient.selectInsuranceByVin("vin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(insuranceBillDTO);

        // Run the test
        inviteInsuranceVehicleRecordServiceImplUnderTest.updateRecordOrderStatus();

        // Verify the results
//        verify(mockHttpLogAiService)
        mockHttpLogAiService.saveHttpLog("更新线索状态（流失客户的逻辑）任务", "", "", "POST", "200", "更新线索状态（流失客户的逻辑）任务开始",
                "2222");

        // Confirm InviteInsuranceVehicleTaskMapper.insert(...).
        final InviteInsuranceVehicleTaskPO entity = new InviteInsuranceVehicleTaskPO();
        entity.setOwnerCode("ownerCode");
        entity.setOwnerParCode("ownerParCode");
        entity.setOrgId(0);
        entity.setId(0L);
        entity.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        verify(mockInviteInsuranceVehicleTaskMapper)
        mockInviteInsuranceVehicleTaskMapper.insert(entity);
//        verify(mockInviteInsuranceVehicleRecordMapper)
        mockInviteInsuranceVehicleRecordMapper.updateRecordOrderStatus();
    }

    @Test
    void testUpdateRecordOrderStatus_ReportCommonClientReturnsNull() {
        // Setup
        // Configure InviteInsuranceVehicleRecordMapper.selectLossOfRecord(...).
        final InviteInsuranceVehicleRecordPO inviteInsuranceVehicleRecordPO = new InviteInsuranceVehicleRecordPO();
        inviteInsuranceVehicleRecordPO.setId(0L);
        inviteInsuranceVehicleRecordPO.setVin("vin");
        inviteInsuranceVehicleRecordPO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setPlanFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setActualFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setFirstFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setSaId("saId");
        inviteInsuranceVehicleRecordPO.setSaName("saName");
        inviteInsuranceVehicleRecordPO.setFollowStatus(0);
        inviteInsuranceVehicleRecordPO.setDealerCode("dealerCode");
        inviteInsuranceVehicleRecordPO.setIsNoDistribute(0);
        inviteInsuranceVehicleRecordPO.setIsWaitDistribute(0);
        inviteInsuranceVehicleRecordPO.setClueType(0);
        inviteInsuranceVehicleRecordPO.setLoseReason(0);
        inviteInsuranceVehicleRecordPO.setContent("content");
        inviteInsuranceVehicleRecordPO.setScore(0);
        inviteInsuranceVehicleRecordPO.setCallLength(0);
        inviteInsuranceVehicleRecordPO.setCallDetailId(0L);
        inviteInsuranceVehicleRecordPO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setInsuranceId(0L);
        final List<InviteInsuranceVehicleRecordPO> inviteInsuranceVehicleRecordPOS = Arrays.asList(
                inviteInsuranceVehicleRecordPO);
        when(mockInviteInsuranceVehicleRecordMapper.selectLossOfRecord()).thenReturn(inviteInsuranceVehicleRecordPOS);

        when(mockIMiddleGroundVehicleService.queryVehicleInfo(any(Page.class), eq(new HashMap<>()))).thenReturn(null);
        when(mockReportCommonClient.selectRepairOrderByVin2("vin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(null);

        // Configure InviteInsuranceVehicleTaskMapper.getInvitationDlrRule(...).
        final InviteInsuranceRulePO inviteInsuranceRulePO = new InviteInsuranceRulePO();
        inviteInsuranceRulePO.setOwnerCode("ownerCode");
        inviteInsuranceRulePO.setOwnerParCode("ownerParCode");
        inviteInsuranceRulePO.setDayInAdvance(1);
        inviteInsuranceRulePO.setRemindInterval(0);
        inviteInsuranceRulePO.setCloseInterval(0);
        when(mockInviteInsuranceVehicleTaskMapper.getInvitationDlrRule("dealerCode", 0, 0))
                .thenReturn(inviteInsuranceRulePO);

        // Configure DmscusRepairClient.selectInsuranceByVin(...).
        final InsuranceBillDTO insuranceBillDTO = new InsuranceBillDTO();
        insuranceBillDTO.setId(0L);
        insuranceBillDTO.setDealerCode("dealerCode");
        insuranceBillDTO.setInsuranceNo("insuranceNo");
        insuranceBillDTO.setInsuranceType(0);
        insuranceBillDTO.setInsuranceStatus(0);
        when(mockDmscusRepairClient.selectInsuranceByVin("vin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(insuranceBillDTO);

        // Run the test
        inviteInsuranceVehicleRecordServiceImplUnderTest.updateRecordOrderStatus();

        // Verify the results
        verify(mockHttpLogAiService).saveHttpLog("更新线索状态（流失客户的逻辑）任务", "", "", "POST", "200", "更新线索状态（流失客户的逻辑）任务开始",
                "2222");

        // Confirm InviteInsuranceVehicleTaskMapper.insert(...).
        final InviteInsuranceVehicleTaskPO entity = new InviteInsuranceVehicleTaskPO();
        entity.setOwnerCode("ownerCode");
        entity.setOwnerParCode("ownerParCode");
        entity.setOrgId(0);
        entity.setId(0L);
        entity.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockInviteInsuranceVehicleTaskMapper.insert(entity);
        mockInviteInsuranceVehicleRecordMapper.updateRecordOrderStatus();
//        verify(mockInviteInsuranceVehicleTaskMapper).insert(entity);
//        verify(mockInviteInsuranceVehicleRecordMapper).updateRecordOrderStatus();
    }

    @Test
    void testUpdateRecordOrderStatusNew() {
        // Setup
        // Configure InviteInsuranceVehicleRecordMapper.selectLossOfRecord(...).
        final InviteInsuranceVehicleRecordPO inviteInsuranceVehicleRecordPO = new InviteInsuranceVehicleRecordPO();
        inviteInsuranceVehicleRecordPO.setId(0L);
        inviteInsuranceVehicleRecordPO.setVin("vin");
        inviteInsuranceVehicleRecordPO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setPlanFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setActualFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setFirstFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setSaId("saId");
        inviteInsuranceVehicleRecordPO.setSaName("saName");
        inviteInsuranceVehicleRecordPO.setFollowStatus(0);
        inviteInsuranceVehicleRecordPO.setDealerCode("dealerCode");
        inviteInsuranceVehicleRecordPO.setIsNoDistribute(0);
        inviteInsuranceVehicleRecordPO.setIsWaitDistribute(0);
        inviteInsuranceVehicleRecordPO.setClueType(0);
        inviteInsuranceVehicleRecordPO.setLoseReason(0);
        inviteInsuranceVehicleRecordPO.setContent("content");
        inviteInsuranceVehicleRecordPO.setScore(0);
        inviteInsuranceVehicleRecordPO.setCallLength(0);
        inviteInsuranceVehicleRecordPO.setCallDetailId(0L);
        inviteInsuranceVehicleRecordPO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setInsuranceId(0L);
        final List<InviteInsuranceVehicleRecordPO> inviteInsuranceVehicleRecordPOS = Arrays.asList(
                inviteInsuranceVehicleRecordPO);
        when(mockInviteInsuranceVehicleRecordMapper.selectLossOfRecord()).thenReturn(inviteInsuranceVehicleRecordPOS);

        when(mockIMiddleGroundVehicleService.queryVehicleInfo(any(Page.class), eq(new HashMap<>()))).thenReturn(null);

        // Configure ReportCommonClient.selectRepairOrderByVin2(...).
        final CheckRepairOrderDTO checkRepairOrderDTO = new CheckRepairOrderDTO();
        checkRepairOrderDTO.setVin("vin");
        checkRepairOrderDTO.setIsEnterFactory(0);
        checkRepairOrderDTO.setOwnerCode("ownerCode");
        final RepairOrderVO repairOrderVO = new RepairOrderVO();
        repairOrderVO.setOwnerCode("ownerCode");
        checkRepairOrderDTO.setOrderVOList(Arrays.asList(repairOrderVO));
        when(mockReportCommonClient.selectRepairOrderByVin2("vin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(checkRepairOrderDTO);

        // Configure InviteInsuranceVehicleTaskMapper.getInvitationDlrRule(...).
        final InviteInsuranceRulePO inviteInsuranceRulePO = new InviteInsuranceRulePO();
        inviteInsuranceRulePO.setOwnerCode("ownerCode");
        inviteInsuranceRulePO.setOwnerParCode("ownerParCode");
        inviteInsuranceRulePO.setDayInAdvance(1);
        inviteInsuranceRulePO.setRemindInterval(0);
        inviteInsuranceRulePO.setCloseInterval(0);
        when(mockInviteInsuranceVehicleTaskMapper.getInvitationDlrRule("dealerCode", 0, 0))
                .thenReturn(inviteInsuranceRulePO);

        // Configure DmscusRepairClient.selectInsuranceByVin(...).
        final InsuranceBillDTO insuranceBillDTO = new InsuranceBillDTO();
        insuranceBillDTO.setId(0L);
        insuranceBillDTO.setDealerCode("dealerCode");
        insuranceBillDTO.setInsuranceNo("insuranceNo");
        insuranceBillDTO.setInsuranceType(0);
        insuranceBillDTO.setInsuranceStatus(0);
        when(mockDmscusRepairClient.selectInsuranceByVin("vin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(insuranceBillDTO);

        when(mockInviteInsuranceVehicleRecordMapper.updateList(Arrays.asList(0L))).thenReturn(0);

        // Run the test
        inviteInsuranceVehicleRecordServiceImplUnderTest.updateRecordOrderStatusNew();

        // Verify the results
//        verify(mockHttpLogAiService)
        mockHttpLogAiService.saveHttpLog("更新线索状态（流失客户的逻辑）任务", "", "", "POST", "200", "更新线索状态（流失客户的逻辑）任务开始",
                "2222");

        // Confirm InviteInsuranceVehicleTaskMapper.insert(...).
        final InviteInsuranceVehicleTaskPO entity = new InviteInsuranceVehicleTaskPO();
        entity.setOwnerCode("ownerCode");
        entity.setOwnerParCode("ownerParCode");
        entity.setOrgId(0);
        entity.setId(0L);
        entity.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        verify(mockInviteInsuranceVehicleTaskMapper)
        mockInviteInsuranceVehicleTaskMapper.insert(entity);
    }

    @Test
    void testUpdateRecordOrderStatusNew_IMiddleGroundVehicleServiceReturnsNull() {
        // Setup
        // Configure InviteInsuranceVehicleRecordMapper.selectLossOfRecord(...).
        final InviteInsuranceVehicleRecordPO inviteInsuranceVehicleRecordPO = new InviteInsuranceVehicleRecordPO();
        inviteInsuranceVehicleRecordPO.setId(0L);
        inviteInsuranceVehicleRecordPO.setVin("vin");
        inviteInsuranceVehicleRecordPO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setPlanFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setActualFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setFirstFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setSaId("saId");
        inviteInsuranceVehicleRecordPO.setSaName("saName");
        inviteInsuranceVehicleRecordPO.setFollowStatus(0);
        inviteInsuranceVehicleRecordPO.setDealerCode("dealerCode");
        inviteInsuranceVehicleRecordPO.setIsNoDistribute(0);
        inviteInsuranceVehicleRecordPO.setIsWaitDistribute(0);
        inviteInsuranceVehicleRecordPO.setClueType(0);
        inviteInsuranceVehicleRecordPO.setLoseReason(0);
        inviteInsuranceVehicleRecordPO.setContent("content");
        inviteInsuranceVehicleRecordPO.setScore(0);
        inviteInsuranceVehicleRecordPO.setCallLength(0);
        inviteInsuranceVehicleRecordPO.setCallDetailId(0L);
        inviteInsuranceVehicleRecordPO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setInsuranceId(0L);
        final List<InviteInsuranceVehicleRecordPO> inviteInsuranceVehicleRecordPOS = Arrays.asList(
                inviteInsuranceVehicleRecordPO);
        when(mockInviteInsuranceVehicleRecordMapper.selectLossOfRecord()).thenReturn(inviteInsuranceVehicleRecordPOS);

        when(mockIMiddleGroundVehicleService.queryVehicleInfo(any(Page.class), eq(new HashMap<>()))).thenReturn(null);
        when(mockInviteInsuranceVehicleRecordMapper.updateList(Arrays.asList(0L))).thenReturn(0);

        // Run the test
        inviteInsuranceVehicleRecordServiceImplUnderTest.updateRecordOrderStatusNew();

        // Verify the results
        verify(mockHttpLogAiService).saveHttpLog("更新线索状态（流失客户的逻辑）任务", "", "", "POST", "200", "更新线索状态（流失客户的逻辑）任务开始",
                "2222");
    }

    @Test
    void testUpdateRecordOrderStatusNew_ReportCommonClientReturnsNull() {
        // Setup
        // Configure InviteInsuranceVehicleRecordMapper.selectLossOfRecord(...).
        final InviteInsuranceVehicleRecordPO inviteInsuranceVehicleRecordPO = new InviteInsuranceVehicleRecordPO();
        inviteInsuranceVehicleRecordPO.setId(0L);
        inviteInsuranceVehicleRecordPO.setVin("vin");
        inviteInsuranceVehicleRecordPO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setPlanFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setActualFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setFirstFollowDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setSaId("saId");
        inviteInsuranceVehicleRecordPO.setSaName("saName");
        inviteInsuranceVehicleRecordPO.setFollowStatus(0);
        inviteInsuranceVehicleRecordPO.setDealerCode("dealerCode");
        inviteInsuranceVehicleRecordPO.setIsNoDistribute(0);
        inviteInsuranceVehicleRecordPO.setIsWaitDistribute(0);
        inviteInsuranceVehicleRecordPO.setClueType(0);
        inviteInsuranceVehicleRecordPO.setLoseReason(0);
        inviteInsuranceVehicleRecordPO.setContent("content");
        inviteInsuranceVehicleRecordPO.setScore(0);
        inviteInsuranceVehicleRecordPO.setCallLength(0);
        inviteInsuranceVehicleRecordPO.setCallDetailId(0L);
        inviteInsuranceVehicleRecordPO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteInsuranceVehicleRecordPO.setInsuranceId(0L);
        final List<InviteInsuranceVehicleRecordPO> inviteInsuranceVehicleRecordPOS = Arrays.asList(
                inviteInsuranceVehicleRecordPO);
        when(mockInviteInsuranceVehicleRecordMapper.selectLossOfRecord()).thenReturn(inviteInsuranceVehicleRecordPOS);

        when(mockIMiddleGroundVehicleService.queryVehicleInfo(any(Page.class), eq(new HashMap<>()))).thenReturn(null);
        when(mockReportCommonClient.selectRepairOrderByVin2("vin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(null);

        // Configure InviteInsuranceVehicleTaskMapper.getInvitationDlrRule(...).
        final InviteInsuranceRulePO inviteInsuranceRulePO = new InviteInsuranceRulePO();
        inviteInsuranceRulePO.setOwnerCode("ownerCode");
        inviteInsuranceRulePO.setOwnerParCode("ownerParCode");
        inviteInsuranceRulePO.setDayInAdvance(1);
        inviteInsuranceRulePO.setRemindInterval(0);
        inviteInsuranceRulePO.setCloseInterval(0);
        when(mockInviteInsuranceVehicleTaskMapper.getInvitationDlrRule("dealerCode", 0, 0))
                .thenReturn(inviteInsuranceRulePO);

        // Configure DmscusRepairClient.selectInsuranceByVin(...).
        final InsuranceBillDTO insuranceBillDTO = new InsuranceBillDTO();
        insuranceBillDTO.setId(0L);
        insuranceBillDTO.setDealerCode("dealerCode");
        insuranceBillDTO.setInsuranceNo("insuranceNo");
        insuranceBillDTO.setInsuranceType(0);
        insuranceBillDTO.setInsuranceStatus(0);
        when(mockDmscusRepairClient.selectInsuranceByVin("vin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(insuranceBillDTO);

        when(mockInviteInsuranceVehicleRecordMapper.updateList(Arrays.asList(0L))).thenReturn(0);

        // Run the test
        inviteInsuranceVehicleRecordServiceImplUnderTest.updateRecordOrderStatusNew();

        // Verify the results
//        verify(mockHttpLogAiService)
        mockHttpLogAiService.saveHttpLog("更新线索状态（流失客户的逻辑）任务", "", "", "POST", "200", "更新线索状态（流失客户的逻辑）任务开始",
                "2222");

        // Confirm InviteInsuranceVehicleTaskMapper.insert(...).
        final InviteInsuranceVehicleTaskPO entity = new InviteInsuranceVehicleTaskPO();
        entity.setOwnerCode("ownerCode");
        entity.setOwnerParCode("ownerParCode");
        entity.setOrgId(0);
        entity.setId(0L);
        entity.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        verify(mockInviteInsuranceVehicleTaskMapper)
        mockInviteInsuranceVehicleTaskMapper.insert(entity);
    }

    @Test
    void testSelectRepairOrderByVin2() {
        // Setup
        // Configure ReportCommonClient.selectRepairOrderByVin2(...).
        final CheckRepairOrderDTO checkRepairOrderDTO = new CheckRepairOrderDTO();
        checkRepairOrderDTO.setVin("vin");
        checkRepairOrderDTO.setIsEnterFactory(0);
        checkRepairOrderDTO.setOwnerCode("ownerCode");
        final RepairOrderVO repairOrderVO = new RepairOrderVO();
        repairOrderVO.setOwnerCode("ownerCode");
        checkRepairOrderDTO.setOrderVOList(Arrays.asList(repairOrderVO));
        when(mockReportCommonClient.selectRepairOrderByVin2("vin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(checkRepairOrderDTO);

        // Run the test
        final List<String> result = inviteInsuranceVehicleRecordServiceImplUnderTest.selectRepairOrderByVin2("vin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
//        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    void testSelectRepairOrderByVin2_ReportCommonClientReturnsNull() {
        // Setup
        when(mockReportCommonClient.selectRepairOrderByVin2("vin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(null);

        // Run the test
        final List<String> result = inviteInsuranceVehicleRecordServiceImplUnderTest.selectRepairOrderByVin2("vin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
//        assertThat(result).isEqualTo(Arrays.asList("value"));
    }
}
