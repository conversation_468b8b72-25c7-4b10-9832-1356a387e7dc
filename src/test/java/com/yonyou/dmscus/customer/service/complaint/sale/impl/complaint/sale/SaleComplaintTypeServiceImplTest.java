package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintTypeMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintTypeDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintTypePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SaleComplaintTypeServiceImplTest {

    @Mock
    private SaleComplaintTypeMapper mockSaleComplaintTypeMapper;

    private SaleComplaintTypeServiceImpl saleComplaintTypeServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        saleComplaintTypeServiceImplUnderTest = new SaleComplaintTypeServiceImpl();
        saleComplaintTypeServiceImplUnderTest.saleComplaintTypeMapper = mockSaleComplaintTypeMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintTypeDTO saleComplaintTypeDTO = new SaleComplaintTypeDTO();
        saleComplaintTypeDTO.setAppId("appId");
        saleComplaintTypeDTO.setOwnerCode("ownerCode");
        saleComplaintTypeDTO.setOwnerParCode("ownerParCode");
        saleComplaintTypeDTO.setOrgId(0);
        saleComplaintTypeDTO.setId(0L);

        // Configure SaleComplaintTypeMapper.selectPageBySql(...).
        final SaleComplaintTypePO saleComplaintTypePO = new SaleComplaintTypePO();
        saleComplaintTypePO.setAppId("appId");
        saleComplaintTypePO.setOwnerCode("ownerCode");
        saleComplaintTypePO.setOwnerParCode("ownerParCode");
        saleComplaintTypePO.setOrgId(0);
        saleComplaintTypePO.setId(0L);
        final List<SaleComplaintTypePO> saleComplaintTypePOS = Arrays.asList(saleComplaintTypePO);
        when(mockSaleComplaintTypeMapper.selectPageBySql(any(Page.class), any(SaleComplaintTypePO.class)))
                .thenReturn(saleComplaintTypePOS);

        // Run the test
        final IPage<SaleComplaintTypeDTO> result = saleComplaintTypeServiceImplUnderTest.selectPageBysql(page,
                saleComplaintTypeDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_SaleComplaintTypeMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintTypeDTO saleComplaintTypeDTO = new SaleComplaintTypeDTO();
        saleComplaintTypeDTO.setAppId("appId");
        saleComplaintTypeDTO.setOwnerCode("ownerCode");
        saleComplaintTypeDTO.setOwnerParCode("ownerParCode");
        saleComplaintTypeDTO.setOrgId(0);
        saleComplaintTypeDTO.setId(0L);

        when(mockSaleComplaintTypeMapper.selectPageBySql(any(Page.class), any(SaleComplaintTypePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final IPage<SaleComplaintTypeDTO> result = saleComplaintTypeServiceImplUnderTest.selectPageBysql(page,
                saleComplaintTypeDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final SaleComplaintTypeDTO saleComplaintTypeDTO = new SaleComplaintTypeDTO();
        saleComplaintTypeDTO.setAppId("appId");
        saleComplaintTypeDTO.setOwnerCode("ownerCode");
        saleComplaintTypeDTO.setOwnerParCode("ownerParCode");
        saleComplaintTypeDTO.setOrgId(0);
        saleComplaintTypeDTO.setId(0L);

        // Configure SaleComplaintTypeMapper.selectListBySql(...).
        final SaleComplaintTypePO saleComplaintTypePO = new SaleComplaintTypePO();
        saleComplaintTypePO.setAppId("appId");
        saleComplaintTypePO.setOwnerCode("ownerCode");
        saleComplaintTypePO.setOwnerParCode("ownerParCode");
        saleComplaintTypePO.setOrgId(0);
        saleComplaintTypePO.setId(0L);
        final List<SaleComplaintTypePO> saleComplaintTypePOS = Arrays.asList(saleComplaintTypePO);
        when(mockSaleComplaintTypeMapper.selectListBySql(any(SaleComplaintTypePO.class)))
                .thenReturn(saleComplaintTypePOS);

        // Run the test
        final List<SaleComplaintTypeDTO> result = saleComplaintTypeServiceImplUnderTest.selectListBySql(
                saleComplaintTypeDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_SaleComplaintTypeMapperReturnsNoItems() {
        // Setup
        final SaleComplaintTypeDTO saleComplaintTypeDTO = new SaleComplaintTypeDTO();
        saleComplaintTypeDTO.setAppId("appId");
        saleComplaintTypeDTO.setOwnerCode("ownerCode");
        saleComplaintTypeDTO.setOwnerParCode("ownerParCode");
        saleComplaintTypeDTO.setOrgId(0);
        saleComplaintTypeDTO.setId(0L);

        when(mockSaleComplaintTypeMapper.selectListBySql(any(SaleComplaintTypePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<SaleComplaintTypeDTO> result = saleComplaintTypeServiceImplUnderTest.selectListBySql(
                saleComplaintTypeDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure SaleComplaintTypeMapper.selectById(...).
        final SaleComplaintTypePO saleComplaintTypePO = new SaleComplaintTypePO();
        saleComplaintTypePO.setAppId("appId");
        saleComplaintTypePO.setOwnerCode("ownerCode");
        saleComplaintTypePO.setOwnerParCode("ownerParCode");
        saleComplaintTypePO.setOrgId(0);
        saleComplaintTypePO.setId(0L);
        when(mockSaleComplaintTypeMapper.selectById(0L)).thenReturn(saleComplaintTypePO);

        // Run the test
        final SaleComplaintTypeDTO result = saleComplaintTypeServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_SaleComplaintTypeMapperReturnsNull() {
        // Setup
        when(mockSaleComplaintTypeMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> saleComplaintTypeServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final SaleComplaintTypeDTO saleComplaintTypeDTO = new SaleComplaintTypeDTO();
        saleComplaintTypeDTO.setAppId("appId");
        saleComplaintTypeDTO.setOwnerCode("ownerCode");
        saleComplaintTypeDTO.setOwnerParCode("ownerParCode");
        saleComplaintTypeDTO.setOrgId(0);
        saleComplaintTypeDTO.setId(0L);

        when(mockSaleComplaintTypeMapper.insert(any(SaleComplaintTypePO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintTypeServiceImplUnderTest.insert(saleComplaintTypeDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final SaleComplaintTypeDTO saleComplaintTypeDTO = new SaleComplaintTypeDTO();
        saleComplaintTypeDTO.setAppId("appId");
        saleComplaintTypeDTO.setOwnerCode("ownerCode");
        saleComplaintTypeDTO.setOwnerParCode("ownerParCode");
        saleComplaintTypeDTO.setOrgId(0);
        saleComplaintTypeDTO.setId(0L);

        // Configure SaleComplaintTypeMapper.selectById(...).
        final SaleComplaintTypePO saleComplaintTypePO = new SaleComplaintTypePO();
        saleComplaintTypePO.setAppId("appId");
        saleComplaintTypePO.setOwnerCode("ownerCode");
        saleComplaintTypePO.setOwnerParCode("ownerParCode");
        saleComplaintTypePO.setOrgId(0);
        saleComplaintTypePO.setId(0L);
        when(mockSaleComplaintTypeMapper.selectById(0L)).thenReturn(saleComplaintTypePO);

        when(mockSaleComplaintTypeMapper.updateById(any(SaleComplaintTypePO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintTypeServiceImplUnderTest.update(0L, saleComplaintTypeDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectList() {
        // Setup
        final SaleComplaintTypeDTO saleComplaintTypeDTO = new SaleComplaintTypeDTO();
        saleComplaintTypeDTO.setAppId("appId");
        saleComplaintTypeDTO.setOwnerCode("ownerCode");
        saleComplaintTypeDTO.setOwnerParCode("ownerParCode");
        saleComplaintTypeDTO.setOrgId(0);
        saleComplaintTypeDTO.setId(0L);

        // Configure SaleComplaintTypeMapper.selectListFW(...).
        final SaleComplaintTypePO saleComplaintTypePO = new SaleComplaintTypePO();
        saleComplaintTypePO.setAppId("appId");
        saleComplaintTypePO.setOwnerCode("ownerCode");
        saleComplaintTypePO.setOwnerParCode("ownerParCode");
        saleComplaintTypePO.setOrgId(0);
        saleComplaintTypePO.setId(0L);
        final List<SaleComplaintTypePO> saleComplaintTypePOS = Arrays.asList(saleComplaintTypePO);
        when(mockSaleComplaintTypeMapper.selectListFW(any(SaleComplaintTypePO.class))).thenReturn(saleComplaintTypePOS);

        // Run the test
        final List<SaleComplaintTypeDTO> result = saleComplaintTypeServiceImplUnderTest.selectList(
                saleComplaintTypeDTO);

        // Verify the results
    }

    @Test
    void testSelectList_SaleComplaintTypeMapperReturnsNoItems() {
        // Setup
        final SaleComplaintTypeDTO saleComplaintTypeDTO = new SaleComplaintTypeDTO();
        saleComplaintTypeDTO.setAppId("appId");
        saleComplaintTypeDTO.setOwnerCode("ownerCode");
        saleComplaintTypeDTO.setOwnerParCode("ownerParCode");
        saleComplaintTypeDTO.setOrgId(0);
        saleComplaintTypeDTO.setId(0L);

        when(mockSaleComplaintTypeMapper.selectListFW(any(SaleComplaintTypePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<SaleComplaintTypeDTO> result = saleComplaintTypeServiceImplUnderTest.selectList(
                saleComplaintTypeDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectListNotFw() {
        // Setup
        final SaleComplaintTypeDTO saleComplaintTypeDTO = new SaleComplaintTypeDTO();
        saleComplaintTypeDTO.setAppId("appId");
        saleComplaintTypeDTO.setOwnerCode("ownerCode");
        saleComplaintTypeDTO.setOwnerParCode("ownerParCode");
        saleComplaintTypeDTO.setOrgId(0);
        saleComplaintTypeDTO.setId(0L);

        // Configure SaleComplaintTypeMapper.selectListNotFw(...).
        final SaleComplaintTypePO saleComplaintTypePO = new SaleComplaintTypePO();
        saleComplaintTypePO.setAppId("appId");
        saleComplaintTypePO.setOwnerCode("ownerCode");
        saleComplaintTypePO.setOwnerParCode("ownerParCode");
        saleComplaintTypePO.setOrgId(0);
        saleComplaintTypePO.setId(0L);
        final List<SaleComplaintTypePO> saleComplaintTypePOS = Arrays.asList(saleComplaintTypePO);
        when(mockSaleComplaintTypeMapper.selectListNotFw(any(SaleComplaintTypePO.class)))
                .thenReturn(saleComplaintTypePOS);

        // Run the test
        final List<SaleComplaintTypeDTO> result = saleComplaintTypeServiceImplUnderTest.selectListNotFw(
                saleComplaintTypeDTO);

        // Verify the results
    }

    @Test
    void testSelectListNotFw_SaleComplaintTypeMapperReturnsNoItems() {
        // Setup
        final SaleComplaintTypeDTO saleComplaintTypeDTO = new SaleComplaintTypeDTO();
        saleComplaintTypeDTO.setAppId("appId");
        saleComplaintTypeDTO.setOwnerCode("ownerCode");
        saleComplaintTypeDTO.setOwnerParCode("ownerParCode");
        saleComplaintTypeDTO.setOrgId(0);
        saleComplaintTypeDTO.setId(0L);

        when(mockSaleComplaintTypeMapper.selectListNotFw(any(SaleComplaintTypePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<SaleComplaintTypeDTO> result = saleComplaintTypeServiceImplUnderTest.selectListNotFw(
                saleComplaintTypeDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
