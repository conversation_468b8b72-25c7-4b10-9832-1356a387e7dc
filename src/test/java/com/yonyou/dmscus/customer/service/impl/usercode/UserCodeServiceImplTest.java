package com.yonyou.dmscus.customer.service.impl.usercode;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.service.excel.ExcelRead;
import com.yonyou.dmscloud.framework.service.excel.impl.AbstractExcelReadCallBack;
import com.yonyou.dmscus.customer.dao.userCode.UserCodeInfoImportMapper;
import com.yonyou.dmscus.customer.dao.userCode.UserCodeInfoMapper;
import com.yonyou.dmscus.customer.dto.UserCodeVo;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.userCode.UserCodeInfoDto;
import com.yonyou.dmscus.customer.entity.dto.userCode.UserCodeInfoImportDto;
import com.yonyou.dmscus.customer.entity.dto.userCode.UserCodeInfoImportExcelDto;
import com.yonyou.dmscus.customer.entity.po.userCode.UserCodeInfoImportPo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserCodeServiceImplTest {

    @Mock
    private UserCodeInfoMapper mockUserCodeInfoMapper;
    @Mock
    private UserCodeInfoImportMapper mockUserCodeInfoImportMapper;
    @Mock
    private ExcelRead<UserCodeInfoImportExcelDto> mockExcelReadServiceIs;

    private UserCodeServiceImpl userCodeServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        userCodeServiceImplUnderTest = new UserCodeServiceImpl();
        userCodeServiceImplUnderTest.userCodeInfoMapper = mockUserCodeInfoMapper;
        userCodeServiceImplUnderTest.userCodeInfoImportMapper = mockUserCodeInfoImportMapper;
        userCodeServiceImplUnderTest.excelReadServiceIs = mockExcelReadServiceIs;
    }

    @Test
    void testGetOneByUerCode() {
        // Setup
        final UserCodeVo vo = new UserCodeVo();
        vo.setUserCode("userCode");
        vo.setCurrentPage(0L);
        vo.setPageSize(0L);

        when(mockUserCodeInfoMapper.getCountByUserCode("userCode")).thenReturn(0);

        // Run the test
        final int result = userCodeServiceImplUnderTest.getOneByUerCode(vo);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final UserCodeVo dto = new UserCodeVo();
        dto.setUserCode("userCode");
        dto.setCurrentPage(0L);
        dto.setPageSize(0L);

        // Configure UserCodeInfoMapper.selectPageBySql(...).
        final UserCodeInfoDto userCodeInfoDto = new UserCodeInfoDto();
        userCodeInfoDto.setId(0L);
        userCodeInfoDto.setBelonging("belonging");
        userCodeInfoDto.setBelongingName("belongingName");
        userCodeInfoDto.setEmployeeName("employeeName");
        userCodeInfoDto.setEmployeeNo("employeeNo");
        final List<UserCodeInfoDto> userCodeInfoDtos = Arrays.asList(userCodeInfoDto);
        final UserCodeVo vo = new UserCodeVo();
        vo.setUserCode("userCode");
        vo.setCurrentPage(0L);
        vo.setPageSize(0L);
        when(mockUserCodeInfoMapper.selectPageBySql(any(Page.class), eq(vo))).thenReturn(userCodeInfoDtos);

        // Run the test
        final IPage<UserCodeInfoDto> result = userCodeServiceImplUnderTest.selectPageBysql(page, dto);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_UserCodeInfoMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final UserCodeVo dto = new UserCodeVo();
        dto.setUserCode("userCode");
        dto.setCurrentPage(0L);
        dto.setPageSize(0L);

        // Configure UserCodeInfoMapper.selectPageBySql(...).
        final UserCodeVo vo = new UserCodeVo();
        vo.setUserCode("userCode");
        vo.setCurrentPage(0L);
        vo.setPageSize(0L);
        when(mockUserCodeInfoMapper.selectPageBySql(any(Page.class), eq(vo))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<UserCodeInfoDto> result = userCodeServiceImplUnderTest.selectPageBysql(page, dto);

        // Verify the results
    }
}
