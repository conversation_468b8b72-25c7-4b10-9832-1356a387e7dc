package com.yonyou.dmscus.customer.service.impl.voicemanage;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.voicemanage.CallDetailsMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.CallVoiceMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.SaCustomerNumberMapper;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.CallVoiceDTO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallVoicePO;
import com.yonyou.dmscus.customer.utils.ai.CallVoiceChild;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CallVoiceServiceImplTest {

    @Mock
    private CallVoiceMapper mockCallVoiceMapper;
    @Mock
    private SaCustomerNumberMapper mockSaCustomerNumberMapper;
    @Mock
    private CallDetailsMapper mockCallDetailsMapper;

    private CallVoiceServiceImpl callVoiceServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        callVoiceServiceImplUnderTest = new CallVoiceServiceImpl();
        callVoiceServiceImplUnderTest.callVoiceMapper = mockCallVoiceMapper;
        callVoiceServiceImplUnderTest.saCustomerNumberMapper = mockSaCustomerNumberMapper;
        callVoiceServiceImplUnderTest.callDetailsMapper = mockCallDetailsMapper;
        callVoiceServiceImplUnderTest.URL_MAPPING_RECORDING = "URL_MAPPING_RECORDING";
        callVoiceServiceImplUnderTest.serverUrl = "serverUrl";
        callVoiceServiceImplUnderTest.appKey = "appKey";
        callVoiceServiceImplUnderTest.appSecret = "appSecret";
    }

    @Test
    void testGetById_CallVoiceMapperReturnsNull() {
        // Setup
        when(mockCallVoiceMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> callVoiceServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testSendDSCCallVoice_CallVoiceMapperSelectSendCallVoiceReturnsNoItems() {
        // Setup
        when(mockCallVoiceMapper.selectSendCallVoice(10041002)).thenReturn(Collections.emptyList());

        // Run the test
        callVoiceServiceImplUnderTest.sendDSCCallVoice();

        // Verify the results
    }
}
