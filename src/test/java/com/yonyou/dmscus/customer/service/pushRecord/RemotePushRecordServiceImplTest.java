package com.yonyou.dmscus.customer.service.pushRecord;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yonyou.dmscus.customer.constants.RemotePushSinceTypeEnum;
import com.yonyou.dmscus.customer.constants.RemotePushStatusEnum;
import com.yonyou.dmscus.customer.dao.pushRecord.RemotePushRecordMapper;
import com.yonyou.dmscus.customer.entity.po.pushRecord.RemotePushRecordPO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RemotePushRecordServiceImplTest {

    @Mock
    private RemotePushRecordMapper mockRemotePushRecordMapper;

    @InjectMocks
    private RemotePushRecordServiceImpl remotePushRecordServiceImplUnderTest;

    @Test
    void testAccidentClueLiteCrmPushRecord() {
        // Setup
        // Configure RemotePushRecordMapper.selectList(...).
        final RemotePushRecordPO remotePushRecordPO = new RemotePushRecordPO();
        remotePushRecordPO.setSinceType(0);
        remotePushRecordPO.setSubSinceType(0);
        remotePushRecordPO.setBizNo("bizNo");
        remotePushRecordPO.setReqParams("reqParams");
        remotePushRecordPO.setRetryCount(0);
        remotePushRecordPO.setLastRetryTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        remotePushRecordPO.setRespContent("resp");
        remotePushRecordPO.setTaskStatus(0);
        remotePushRecordPO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<RemotePushRecordPO> remotePushRecordPOS = Arrays.asList(remotePushRecordPO);
        when(mockRemotePushRecordMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(remotePushRecordPOS);

        // Run the test
        remotePushRecordServiceImplUnderTest.accidentClueLiteCrmPushRecord("reqParams", "resp", 0,
                RemotePushSinceTypeEnum.CLUE_INFO, RemotePushStatusEnum.STATUS_PENDING);

        // Verify the results
        // Confirm RemotePushRecordMapper.insert(...).
        final RemotePushRecordPO entity = new RemotePushRecordPO();
        entity.setSinceType(0);
        entity.setSubSinceType(0);
        entity.setBizNo("bizNo");
        entity.setReqParams("reqParams");
        entity.setRetryCount(0);
        entity.setLastRetryTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setRespContent("resp");
        entity.setTaskStatus(0);
        entity.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        verify(mockRemotePushRecordMapper)
        mockRemotePushRecordMapper.insert(entity);

        // Confirm RemotePushRecordMapper.updateById(...).
        final RemotePushRecordPO entity1 = new RemotePushRecordPO();
        entity1.setSinceType(0);
        entity1.setSubSinceType(0);
        entity1.setBizNo("bizNo");
        entity1.setReqParams("reqParams");
        entity1.setRetryCount(0);
        entity1.setLastRetryTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity1.setRespContent("resp");
        entity1.setTaskStatus(0);
        entity1.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        verify(mockRemotePushRecordMapper)
        mockRemotePushRecordMapper.updateById(entity1);
    }

    @Test
    void testAccidentClueLiteCrmPushRecord_RemotePushRecordMapperSelectListReturnsNoItems() {
        // Setup
        when(mockRemotePushRecordMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        remotePushRecordServiceImplUnderTest.accidentClueLiteCrmPushRecord("reqParams", "resp", 0,
                RemotePushSinceTypeEnum.CLUE_INFO, RemotePushStatusEnum.STATUS_PENDING);

        // Verify the results
        // Confirm RemotePushRecordMapper.insert(...).
        final RemotePushRecordPO entity = new RemotePushRecordPO();
        entity.setSinceType(0);
        entity.setSubSinceType(0);
        entity.setBizNo("bizNo");
        entity.setReqParams("reqParams");
        entity.setRetryCount(0);
        entity.setLastRetryTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setRespContent("resp");
        entity.setTaskStatus(0);
        entity.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        verify(mockRemotePushRecordMapper)
        mockRemotePushRecordMapper.insert(entity);

        // Confirm RemotePushRecordMapper.updateById(...).
        final RemotePushRecordPO entity1 = new RemotePushRecordPO();
        entity1.setSinceType(0);
        entity1.setSubSinceType(0);
        entity1.setBizNo("bizNo");
        entity1.setReqParams("reqParams");
        entity1.setRetryCount(0);
        entity1.setLastRetryTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity1.setRespContent("resp");
        entity1.setTaskStatus(0);
        entity1.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        verify(mockRemotePushRecordMapper)
        mockRemotePushRecordMapper.updateById(entity1);
    }

    @Test
    void testUpdateAcCompensateRecord() {
        // Setup
        final RemotePushRecordPO record = new RemotePushRecordPO();
        record.setSinceType(0);
        record.setSubSinceType(0);
        record.setBizNo("bizNo");
        record.setReqParams("reqParams");
        record.setRetryCount(0);
        record.setLastRetryTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setRespContent("resp");
        record.setTaskStatus(0);
        record.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        remotePushRecordServiceImplUnderTest.updateAcCompensateRecord(record, "resp",
                RemotePushStatusEnum.STATUS_PENDING);

        // Verify the results
        // Confirm RemotePushRecordMapper.updateById(...).
        final RemotePushRecordPO entity = new RemotePushRecordPO();
        entity.setSinceType(0);
        entity.setSubSinceType(0);
        entity.setBizNo("bizNo");
        entity.setReqParams("reqParams");
        entity.setRetryCount(0);
        entity.setLastRetryTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setRespContent("resp");
        entity.setTaskStatus(0);
        entity.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        verify(mockRemotePushRecordMapper)
        mockRemotePushRecordMapper.updateById(entity);
    }
}
