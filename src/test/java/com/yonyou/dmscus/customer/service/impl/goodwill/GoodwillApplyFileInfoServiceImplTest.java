package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyFileInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFileInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyFileInfoPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillApplyFileInfoServiceImplTest {

    @Mock
    private GoodwillApplyFileInfoMapper mockGoodwillApplyFileInfoMapper;

    private GoodwillApplyFileInfoServiceImpl goodwillApplyFileInfoServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        goodwillApplyFileInfoServiceImplUnderTest = new GoodwillApplyFileInfoServiceImpl();
        goodwillApplyFileInfoServiceImplUnderTest.goodwillApplyFileInfoMapper = mockGoodwillApplyFileInfoMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO = new GoodwillApplyFileInfoDTO();
        goodwillApplyFileInfoDTO.setAppId("appId");
        goodwillApplyFileInfoDTO.setFileType(0);
        goodwillApplyFileInfoDTO.setFileName("fileName");
        goodwillApplyFileInfoDTO.setApplyNo("applyNo");
        goodwillApplyFileInfoDTO.setDownloadFileName("downloadFileName");

        // Configure GoodwillApplyFileInfoMapper.selectPageBySql(...).
        final GoodwillApplyFileInfoPO goodwillApplyFileInfoPO = new GoodwillApplyFileInfoPO();
        goodwillApplyFileInfoPO.setAppId("appId");
        goodwillApplyFileInfoPO.setOwnerCode("ownerCode");
        goodwillApplyFileInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyFileInfoPO.setOrgId(0);
        goodwillApplyFileInfoPO.setIsDeleted(false);
        final List<GoodwillApplyFileInfoPO> goodwillApplyFileInfoPOS = Arrays.asList(goodwillApplyFileInfoPO);
        when(mockGoodwillApplyFileInfoMapper.selectPageBySql(any(Page.class),
                any(GoodwillApplyFileInfoPO.class))).thenReturn(goodwillApplyFileInfoPOS);

        // Run the test
        final IPage<GoodwillApplyFileInfoDTO> result = goodwillApplyFileInfoServiceImplUnderTest.selectPageBysql(page,
                goodwillApplyFileInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillApplyFileInfoMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO = new GoodwillApplyFileInfoDTO();
        goodwillApplyFileInfoDTO.setAppId("appId");
        goodwillApplyFileInfoDTO.setFileType(0);
        goodwillApplyFileInfoDTO.setFileName("fileName");
        goodwillApplyFileInfoDTO.setApplyNo("applyNo");
        goodwillApplyFileInfoDTO.setDownloadFileName("downloadFileName");

        when(mockGoodwillApplyFileInfoMapper.selectPageBySql(any(Page.class),
                any(GoodwillApplyFileInfoPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillApplyFileInfoDTO> result = goodwillApplyFileInfoServiceImplUnderTest.selectPageBysql(page,
                goodwillApplyFileInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO = new GoodwillApplyFileInfoDTO();
        goodwillApplyFileInfoDTO.setAppId("appId");
        goodwillApplyFileInfoDTO.setFileType(0);
        goodwillApplyFileInfoDTO.setFileName("fileName");
        goodwillApplyFileInfoDTO.setApplyNo("applyNo");
        goodwillApplyFileInfoDTO.setDownloadFileName("downloadFileName");

        // Configure GoodwillApplyFileInfoMapper.selectListBySql(...).
        final GoodwillApplyFileInfoPO goodwillApplyFileInfoPO = new GoodwillApplyFileInfoPO();
        goodwillApplyFileInfoPO.setAppId("appId");
        goodwillApplyFileInfoPO.setOwnerCode("ownerCode");
        goodwillApplyFileInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyFileInfoPO.setOrgId(0);
        goodwillApplyFileInfoPO.setIsDeleted(false);
        final List<GoodwillApplyFileInfoPO> goodwillApplyFileInfoPOS = Arrays.asList(goodwillApplyFileInfoPO);
        when(mockGoodwillApplyFileInfoMapper.selectListBySql(any(GoodwillApplyFileInfoPO.class)))
                .thenReturn(goodwillApplyFileInfoPOS);

        // Run the test
        final List<GoodwillApplyFileInfoDTO> result = goodwillApplyFileInfoServiceImplUnderTest.selectListBySql(
                goodwillApplyFileInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillApplyFileInfoMapperReturnsNoItems() {
        // Setup
        final GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO = new GoodwillApplyFileInfoDTO();
        goodwillApplyFileInfoDTO.setAppId("appId");
        goodwillApplyFileInfoDTO.setFileType(0);
        goodwillApplyFileInfoDTO.setFileName("fileName");
        goodwillApplyFileInfoDTO.setApplyNo("applyNo");
        goodwillApplyFileInfoDTO.setDownloadFileName("downloadFileName");

        when(mockGoodwillApplyFileInfoMapper.selectListBySql(any(GoodwillApplyFileInfoPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillApplyFileInfoDTO> result = goodwillApplyFileInfoServiceImplUnderTest.selectListBySql(
                goodwillApplyFileInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure GoodwillApplyFileInfoMapper.selectById(...).
        final GoodwillApplyFileInfoPO goodwillApplyFileInfoPO = new GoodwillApplyFileInfoPO();
        goodwillApplyFileInfoPO.setAppId("appId");
        goodwillApplyFileInfoPO.setOwnerCode("ownerCode");
        goodwillApplyFileInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyFileInfoPO.setOrgId(0);
        goodwillApplyFileInfoPO.setIsDeleted(false);
        when(mockGoodwillApplyFileInfoMapper.selectById(0L)).thenReturn(goodwillApplyFileInfoPO);

        // Run the test
        final GoodwillApplyFileInfoDTO result = goodwillApplyFileInfoServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillApplyFileInfoMapperReturnsNull() {
        // Setup
        when(mockGoodwillApplyFileInfoMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillApplyFileInfoServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO = new GoodwillApplyFileInfoDTO();
        goodwillApplyFileInfoDTO.setAppId("appId");
        goodwillApplyFileInfoDTO.setFileType(0);
        goodwillApplyFileInfoDTO.setFileName("fileName");
        goodwillApplyFileInfoDTO.setApplyNo("applyNo");
        goodwillApplyFileInfoDTO.setDownloadFileName("downloadFileName");

        when(mockGoodwillApplyFileInfoMapper.insert(any(GoodwillApplyFileInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillApplyFileInfoServiceImplUnderTest.insert(goodwillApplyFileInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final GoodwillApplyFileInfoDTO goodwillApplyFileInfoDTO = new GoodwillApplyFileInfoDTO();
        goodwillApplyFileInfoDTO.setAppId("appId");
        goodwillApplyFileInfoDTO.setFileType(0);
        goodwillApplyFileInfoDTO.setFileName("fileName");
        goodwillApplyFileInfoDTO.setApplyNo("applyNo");
        goodwillApplyFileInfoDTO.setDownloadFileName("downloadFileName");

        // Configure GoodwillApplyFileInfoMapper.selectById(...).
        final GoodwillApplyFileInfoPO goodwillApplyFileInfoPO = new GoodwillApplyFileInfoPO();
        goodwillApplyFileInfoPO.setAppId("appId");
        goodwillApplyFileInfoPO.setOwnerCode("ownerCode");
        goodwillApplyFileInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyFileInfoPO.setOrgId(0);
        goodwillApplyFileInfoPO.setIsDeleted(false);
        when(mockGoodwillApplyFileInfoMapper.selectById(0L)).thenReturn(goodwillApplyFileInfoPO);

        when(mockGoodwillApplyFileInfoMapper.updateById(any(GoodwillApplyFileInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillApplyFileInfoServiceImplUnderTest.update(0L, goodwillApplyFileInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testDeleteById() {
        // Setup
        // Configure GoodwillApplyFileInfoMapper.selectById(...).
        final GoodwillApplyFileInfoPO goodwillApplyFileInfoPO = new GoodwillApplyFileInfoPO();
        goodwillApplyFileInfoPO.setAppId("appId");
        goodwillApplyFileInfoPO.setOwnerCode("ownerCode");
        goodwillApplyFileInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyFileInfoPO.setOrgId(0);
        goodwillApplyFileInfoPO.setIsDeleted(false);
        when(mockGoodwillApplyFileInfoMapper.selectById(0L)).thenReturn(goodwillApplyFileInfoPO);

        // Run the test
        goodwillApplyFileInfoServiceImplUnderTest.deleteById(0L);

        // Verify the results
        verify(mockGoodwillApplyFileInfoMapper).updateById(any(GoodwillApplyFileInfoPO.class));
    }

    @Test
    void testSelectListBySql1() {
        // Setup
        final GoodwillApplyFileInfoDTO goodwillApplyFileInfoDto = new GoodwillApplyFileInfoDTO();
        goodwillApplyFileInfoDto.setAppId("appId");
        goodwillApplyFileInfoDto.setFileType(0);
        goodwillApplyFileInfoDto.setFileName("fileName");
        goodwillApplyFileInfoDto.setApplyNo("applyNo");
        goodwillApplyFileInfoDto.setDownloadFileName("downloadFileName");

        // Configure GoodwillApplyFileInfoMapper.selectListBySql1(...).
        final GoodwillApplyFileInfoPO goodwillApplyFileInfoPO = new GoodwillApplyFileInfoPO();
        goodwillApplyFileInfoPO.setAppId("appId");
        goodwillApplyFileInfoPO.setOwnerCode("ownerCode");
        goodwillApplyFileInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyFileInfoPO.setOrgId(0);
        goodwillApplyFileInfoPO.setIsDeleted(false);
        final List<GoodwillApplyFileInfoPO> goodwillApplyFileInfoPOS = Arrays.asList(goodwillApplyFileInfoPO);
        when(mockGoodwillApplyFileInfoMapper.selectListBySql1(any(GoodwillApplyFileInfoPO.class)))
                .thenReturn(goodwillApplyFileInfoPOS);

        // Run the test
        final List<GoodwillApplyFileInfoDTO> result = goodwillApplyFileInfoServiceImplUnderTest.selectListBySql1(
                goodwillApplyFileInfoDto);

        // Verify the results
    }

    @Test
    void testSelectListBySql1_GoodwillApplyFileInfoMapperReturnsNoItems() {
        // Setup
        final GoodwillApplyFileInfoDTO goodwillApplyFileInfoDto = new GoodwillApplyFileInfoDTO();
        goodwillApplyFileInfoDto.setAppId("appId");
        goodwillApplyFileInfoDto.setFileType(0);
        goodwillApplyFileInfoDto.setFileName("fileName");
        goodwillApplyFileInfoDto.setApplyNo("applyNo");
        goodwillApplyFileInfoDto.setDownloadFileName("downloadFileName");

        when(mockGoodwillApplyFileInfoMapper.selectListBySql1(any(GoodwillApplyFileInfoPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillApplyFileInfoDTO> result = goodwillApplyFileInfoServiceImplUnderTest.selectListBySql1(
                goodwillApplyFileInfoDto);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectMaterialUploadList_GoodwillApplyFileInfoMapperReturnsNoItems() {
        // Setup
        final GoodwillApplyFileInfoDTO goodwillApplyFileInfoDto = new GoodwillApplyFileInfoDTO();
        goodwillApplyFileInfoDto.setAppId("appId");
        goodwillApplyFileInfoDto.setFileType(0);
        goodwillApplyFileInfoDto.setFileName("fileName");
        goodwillApplyFileInfoDto.setApplyNo("applyNo");
        goodwillApplyFileInfoDto.setDownloadFileName("downloadFileName");

        when(mockGoodwillApplyFileInfoMapper.selectMaterialUploadList(any(GoodwillApplyFileInfoPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillApplyFileInfoDTO> result = goodwillApplyFileInfoServiceImplUnderTest.selectMaterialUploadList(
                goodwillApplyFileInfoDto);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
