package com.yonyou.dmscus.customer.service.impl.inviteRule;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.inviteRule.InviteDuplicateRemovalRuleMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteRule.InviteDuplicateRemovalRuleDTO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InviteDuplicateRemovalRulePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteDuplicateRemovalRuleServiceImplTest {

    @Mock
    private InviteDuplicateRemovalRuleMapper mockInviteDuplicateRemovalRuleMapper;

    private InviteDuplicateRemovalRuleServiceImpl inviteDuplicateRemovalRuleServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteDuplicateRemovalRuleServiceImplUnderTest = new InviteDuplicateRemovalRuleServiceImpl();
        inviteDuplicateRemovalRuleServiceImplUnderTest.inviteDuplicateRemovalRuleMapper = mockInviteDuplicateRemovalRuleMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final InviteDuplicateRemovalRuleDTO inviteDuplicateRemovalRuleDTO = new InviteDuplicateRemovalRuleDTO();
        inviteDuplicateRemovalRuleDTO.setAppId("appId");
        inviteDuplicateRemovalRuleDTO.setOwnerCode("ownerCode");
        inviteDuplicateRemovalRuleDTO.setOwnerParCode("ownerParCode");
        inviteDuplicateRemovalRuleDTO.setOrgId(0);
        inviteDuplicateRemovalRuleDTO.setId(0L);

        // Configure InviteDuplicateRemovalRuleMapper.selectPageBySql(...).
        final InviteDuplicateRemovalRulePO inviteDuplicateRemovalRulePO = new InviteDuplicateRemovalRulePO();
        inviteDuplicateRemovalRulePO.setAppId("appId");
        inviteDuplicateRemovalRulePO.setOwnerCode("ownerCode");
        inviteDuplicateRemovalRulePO.setOwnerParCode("ownerParCode");
        inviteDuplicateRemovalRulePO.setOrgId(0);
        inviteDuplicateRemovalRulePO.setId(0L);
        final List<InviteDuplicateRemovalRulePO> inviteDuplicateRemovalRulePOS = Arrays.asList(
                inviteDuplicateRemovalRulePO);
        when(mockInviteDuplicateRemovalRuleMapper.selectPageBySql(any(Page.class),
                any(InviteDuplicateRemovalRulePO.class))).thenReturn(inviteDuplicateRemovalRulePOS);

        // Run the test
        final IPage<InviteDuplicateRemovalRuleDTO> result = inviteDuplicateRemovalRuleServiceImplUnderTest.selectPageBysql(
                page, inviteDuplicateRemovalRuleDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_InviteDuplicateRemovalRuleMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final InviteDuplicateRemovalRuleDTO inviteDuplicateRemovalRuleDTO = new InviteDuplicateRemovalRuleDTO();
        inviteDuplicateRemovalRuleDTO.setAppId("appId");
        inviteDuplicateRemovalRuleDTO.setOwnerCode("ownerCode");
        inviteDuplicateRemovalRuleDTO.setOwnerParCode("ownerParCode");
        inviteDuplicateRemovalRuleDTO.setOrgId(0);
        inviteDuplicateRemovalRuleDTO.setId(0L);

        when(mockInviteDuplicateRemovalRuleMapper.selectPageBySql(any(Page.class),
                any(InviteDuplicateRemovalRulePO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<InviteDuplicateRemovalRuleDTO> result = inviteDuplicateRemovalRuleServiceImplUnderTest.selectPageBysql(
                page, inviteDuplicateRemovalRuleDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final InviteDuplicateRemovalRuleDTO inviteDuplicateRemovalRuleDTO = new InviteDuplicateRemovalRuleDTO();
        inviteDuplicateRemovalRuleDTO.setAppId("appId");
        inviteDuplicateRemovalRuleDTO.setOwnerCode("ownerCode");
        inviteDuplicateRemovalRuleDTO.setOwnerParCode("ownerParCode");
        inviteDuplicateRemovalRuleDTO.setOrgId(0);
        inviteDuplicateRemovalRuleDTO.setId(0L);

        // Configure InviteDuplicateRemovalRuleMapper.selectListBySql(...).
        final InviteDuplicateRemovalRulePO inviteDuplicateRemovalRulePO = new InviteDuplicateRemovalRulePO();
        inviteDuplicateRemovalRulePO.setAppId("appId");
        inviteDuplicateRemovalRulePO.setOwnerCode("ownerCode");
        inviteDuplicateRemovalRulePO.setOwnerParCode("ownerParCode");
        inviteDuplicateRemovalRulePO.setOrgId(0);
        inviteDuplicateRemovalRulePO.setId(0L);
        final List<InviteDuplicateRemovalRulePO> inviteDuplicateRemovalRulePOS = Arrays.asList(
                inviteDuplicateRemovalRulePO);
        when(mockInviteDuplicateRemovalRuleMapper.selectListBySql(any(InviteDuplicateRemovalRulePO.class)))
                .thenReturn(inviteDuplicateRemovalRulePOS);

        // Run the test
        final List<InviteDuplicateRemovalRuleDTO> result = inviteDuplicateRemovalRuleServiceImplUnderTest.selectListBySql(
                inviteDuplicateRemovalRuleDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_InviteDuplicateRemovalRuleMapperReturnsNoItems() {
        // Setup
        final InviteDuplicateRemovalRuleDTO inviteDuplicateRemovalRuleDTO = new InviteDuplicateRemovalRuleDTO();
        inviteDuplicateRemovalRuleDTO.setAppId("appId");
        inviteDuplicateRemovalRuleDTO.setOwnerCode("ownerCode");
        inviteDuplicateRemovalRuleDTO.setOwnerParCode("ownerParCode");
        inviteDuplicateRemovalRuleDTO.setOrgId(0);
        inviteDuplicateRemovalRuleDTO.setId(0L);

        when(mockInviteDuplicateRemovalRuleMapper.selectListBySql(any(InviteDuplicateRemovalRulePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteDuplicateRemovalRuleDTO> result = inviteDuplicateRemovalRuleServiceImplUnderTest.selectListBySql(
                inviteDuplicateRemovalRuleDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectListBySqlNew() {
        // Setup
        final InviteDuplicateRemovalRuleDTO t = new InviteDuplicateRemovalRuleDTO();
        t.setAppId("appId");
        t.setOwnerCode("ownerCode");
        t.setOwnerParCode("ownerParCode");
        t.setOrgId(0);
        t.setId(0L);

        // Configure InviteDuplicateRemovalRuleMapper.selectListBySql(...).
        final InviteDuplicateRemovalRulePO inviteDuplicateRemovalRulePO = new InviteDuplicateRemovalRulePO();
        inviteDuplicateRemovalRulePO.setAppId("appId");
        inviteDuplicateRemovalRulePO.setOwnerCode("ownerCode");
        inviteDuplicateRemovalRulePO.setOwnerParCode("ownerParCode");
        inviteDuplicateRemovalRulePO.setOrgId(0);
        inviteDuplicateRemovalRulePO.setId(0L);
        final List<InviteDuplicateRemovalRulePO> inviteDuplicateRemovalRulePOS = Arrays.asList(
                inviteDuplicateRemovalRulePO);
        when(mockInviteDuplicateRemovalRuleMapper.selectListBySql(any(InviteDuplicateRemovalRulePO.class)))
                .thenReturn(inviteDuplicateRemovalRulePOS);

        // Run the test
        final List<InviteDuplicateRemovalRuleDTO> result = inviteDuplicateRemovalRuleServiceImplUnderTest.selectListBySqlNew(
                t);

        // Verify the results
    }

    @Test
    void testSelectListBySqlNew_InviteDuplicateRemovalRuleMapperReturnsNoItems() {
        // Setup
        final InviteDuplicateRemovalRuleDTO t = new InviteDuplicateRemovalRuleDTO();
        t.setAppId("appId");
        t.setOwnerCode("ownerCode");
        t.setOwnerParCode("ownerParCode");
        t.setOrgId(0);
        t.setId(0L);

        when(mockInviteDuplicateRemovalRuleMapper.selectListBySql(any(InviteDuplicateRemovalRulePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteDuplicateRemovalRuleDTO> result = inviteDuplicateRemovalRuleServiceImplUnderTest.selectListBySqlNew(
                t);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure InviteDuplicateRemovalRuleMapper.selectById(...).
        final InviteDuplicateRemovalRulePO inviteDuplicateRemovalRulePO = new InviteDuplicateRemovalRulePO();
        inviteDuplicateRemovalRulePO.setAppId("appId");
        inviteDuplicateRemovalRulePO.setOwnerCode("ownerCode");
        inviteDuplicateRemovalRulePO.setOwnerParCode("ownerParCode");
        inviteDuplicateRemovalRulePO.setOrgId(0);
        inviteDuplicateRemovalRulePO.setId(0L);
        when(mockInviteDuplicateRemovalRuleMapper.selectById(0L)).thenReturn(inviteDuplicateRemovalRulePO);

        // Run the test
        final InviteDuplicateRemovalRuleDTO result = inviteDuplicateRemovalRuleServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_InviteDuplicateRemovalRuleMapperReturnsNull() {
        // Setup
        when(mockInviteDuplicateRemovalRuleMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inviteDuplicateRemovalRuleServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final InviteDuplicateRemovalRuleDTO inviteDuplicateRemovalRuleDTO = new InviteDuplicateRemovalRuleDTO();
        inviteDuplicateRemovalRuleDTO.setAppId("appId");
        inviteDuplicateRemovalRuleDTO.setOwnerCode("ownerCode");
        inviteDuplicateRemovalRuleDTO.setOwnerParCode("ownerParCode");
        inviteDuplicateRemovalRuleDTO.setOrgId(0);
        inviteDuplicateRemovalRuleDTO.setId(0L);

        when(mockInviteDuplicateRemovalRuleMapper.insert(any(InviteDuplicateRemovalRulePO.class))).thenReturn(0);

        // Run the test
        final int result = inviteDuplicateRemovalRuleServiceImplUnderTest.insert(inviteDuplicateRemovalRuleDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final InviteDuplicateRemovalRuleDTO inviteDuplicateRemovalRuleDTO = new InviteDuplicateRemovalRuleDTO();
        inviteDuplicateRemovalRuleDTO.setAppId("appId");
        inviteDuplicateRemovalRuleDTO.setOwnerCode("ownerCode");
        inviteDuplicateRemovalRuleDTO.setOwnerParCode("ownerParCode");
        inviteDuplicateRemovalRuleDTO.setOrgId(0);
        inviteDuplicateRemovalRuleDTO.setId(0L);

        // Configure InviteDuplicateRemovalRuleMapper.selectById(...).
        final InviteDuplicateRemovalRulePO inviteDuplicateRemovalRulePO = new InviteDuplicateRemovalRulePO();
        inviteDuplicateRemovalRulePO.setAppId("appId");
        inviteDuplicateRemovalRulePO.setOwnerCode("ownerCode");
        inviteDuplicateRemovalRulePO.setOwnerParCode("ownerParCode");
        inviteDuplicateRemovalRulePO.setOrgId(0);
        inviteDuplicateRemovalRulePO.setId(0L);
        when(mockInviteDuplicateRemovalRuleMapper.selectById(0L)).thenReturn(inviteDuplicateRemovalRulePO);

        when(mockInviteDuplicateRemovalRuleMapper.updateById(any(InviteDuplicateRemovalRulePO.class))).thenReturn(0);

        // Run the test
        final int result = inviteDuplicateRemovalRuleServiceImplUnderTest.update(0L, inviteDuplicateRemovalRuleDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
