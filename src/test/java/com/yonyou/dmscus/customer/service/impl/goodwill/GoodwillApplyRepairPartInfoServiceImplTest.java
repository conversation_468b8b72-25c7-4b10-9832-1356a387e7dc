package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyRepairPartInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairPartInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyRepairPartInfoPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillApplyRepairPartInfoServiceImplTest {

    @Mock
    private GoodwillApplyRepairPartInfoMapper mockGoodwillApplyRepairPartInfoMapper;

    private GoodwillApplyRepairPartInfoServiceImpl goodwillApplyRepairPartInfoServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        goodwillApplyRepairPartInfoServiceImplUnderTest = new GoodwillApplyRepairPartInfoServiceImpl();
        goodwillApplyRepairPartInfoServiceImplUnderTest.goodwillApplyRepairPartInfoMapper = mockGoodwillApplyRepairPartInfoMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setAppId("appId");
        goodwillApplyRepairPartInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyRepairPartInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairPartInfoDTO.setOrgId(0);
        goodwillApplyRepairPartInfoDTO.setId(0L);

        // Configure GoodwillApplyRepairPartInfoMapper.selectPageBySql(...).
        final GoodwillApplyRepairPartInfoPO goodwillApplyRepairPartInfoPO = new GoodwillApplyRepairPartInfoPO();
        goodwillApplyRepairPartInfoPO.setAppId("appId");
        goodwillApplyRepairPartInfoPO.setOwnerCode("ownerCode");
        goodwillApplyRepairPartInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairPartInfoPO.setOrgId(0);
        goodwillApplyRepairPartInfoPO.setId(0L);
        final List<GoodwillApplyRepairPartInfoPO> goodwillApplyRepairPartInfoPOS = Arrays.asList(
                goodwillApplyRepairPartInfoPO);
        when(mockGoodwillApplyRepairPartInfoMapper.selectPageBySql(any(Page.class),
                any(GoodwillApplyRepairPartInfoPO.class))).thenReturn(goodwillApplyRepairPartInfoPOS);

        // Run the test
        final IPage<GoodwillApplyRepairPartInfoDTO> result = goodwillApplyRepairPartInfoServiceImplUnderTest.selectPageBysql(
                page, goodwillApplyRepairPartInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillApplyRepairPartInfoMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setAppId("appId");
        goodwillApplyRepairPartInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyRepairPartInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairPartInfoDTO.setOrgId(0);
        goodwillApplyRepairPartInfoDTO.setId(0L);

        when(mockGoodwillApplyRepairPartInfoMapper.selectPageBySql(any(Page.class),
                any(GoodwillApplyRepairPartInfoPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillApplyRepairPartInfoDTO> result = goodwillApplyRepairPartInfoServiceImplUnderTest.selectPageBysql(
                page, goodwillApplyRepairPartInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setAppId("appId");
        goodwillApplyRepairPartInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyRepairPartInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairPartInfoDTO.setOrgId(0);
        goodwillApplyRepairPartInfoDTO.setId(0L);

        // Configure GoodwillApplyRepairPartInfoMapper.selectListBySql(...).
        final GoodwillApplyRepairPartInfoPO goodwillApplyRepairPartInfoPO = new GoodwillApplyRepairPartInfoPO();
        goodwillApplyRepairPartInfoPO.setAppId("appId");
        goodwillApplyRepairPartInfoPO.setOwnerCode("ownerCode");
        goodwillApplyRepairPartInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairPartInfoPO.setOrgId(0);
        goodwillApplyRepairPartInfoPO.setId(0L);
        final List<GoodwillApplyRepairPartInfoPO> goodwillApplyRepairPartInfoPOS = Arrays.asList(
                goodwillApplyRepairPartInfoPO);
        when(mockGoodwillApplyRepairPartInfoMapper.selectListBySql(
                any(GoodwillApplyRepairPartInfoPO.class))).thenReturn(goodwillApplyRepairPartInfoPOS);

        // Run the test
        final List<GoodwillApplyRepairPartInfoDTO> result = goodwillApplyRepairPartInfoServiceImplUnderTest.selectListBySql(
                goodwillApplyRepairPartInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillApplyRepairPartInfoMapperReturnsNoItems() {
        // Setup
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setAppId("appId");
        goodwillApplyRepairPartInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyRepairPartInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairPartInfoDTO.setOrgId(0);
        goodwillApplyRepairPartInfoDTO.setId(0L);

        when(mockGoodwillApplyRepairPartInfoMapper.selectListBySql(
                any(GoodwillApplyRepairPartInfoPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillApplyRepairPartInfoDTO> result = goodwillApplyRepairPartInfoServiceImplUnderTest.selectListBySql(
                goodwillApplyRepairPartInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure GoodwillApplyRepairPartInfoMapper.selectById(...).
        final GoodwillApplyRepairPartInfoPO goodwillApplyRepairPartInfoPO = new GoodwillApplyRepairPartInfoPO();
        goodwillApplyRepairPartInfoPO.setAppId("appId");
        goodwillApplyRepairPartInfoPO.setOwnerCode("ownerCode");
        goodwillApplyRepairPartInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairPartInfoPO.setOrgId(0);
        goodwillApplyRepairPartInfoPO.setId(0L);
        when(mockGoodwillApplyRepairPartInfoMapper.selectById(0L)).thenReturn(goodwillApplyRepairPartInfoPO);

        // Run the test
        final GoodwillApplyRepairPartInfoDTO result = goodwillApplyRepairPartInfoServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillApplyRepairPartInfoMapperReturnsNull() {
        // Setup
        when(mockGoodwillApplyRepairPartInfoMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillApplyRepairPartInfoServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setAppId("appId");
        goodwillApplyRepairPartInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyRepairPartInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairPartInfoDTO.setOrgId(0);
        goodwillApplyRepairPartInfoDTO.setId(0L);

        when(mockGoodwillApplyRepairPartInfoMapper.insert(any(GoodwillApplyRepairPartInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillApplyRepairPartInfoServiceImplUnderTest.insert(goodwillApplyRepairPartInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setAppId("appId");
        goodwillApplyRepairPartInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyRepairPartInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairPartInfoDTO.setOrgId(0);
        goodwillApplyRepairPartInfoDTO.setId(0L);

        // Configure GoodwillApplyRepairPartInfoMapper.selectById(...).
        final GoodwillApplyRepairPartInfoPO goodwillApplyRepairPartInfoPO = new GoodwillApplyRepairPartInfoPO();
        goodwillApplyRepairPartInfoPO.setAppId("appId");
        goodwillApplyRepairPartInfoPO.setOwnerCode("ownerCode");
        goodwillApplyRepairPartInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairPartInfoPO.setOrgId(0);
        goodwillApplyRepairPartInfoPO.setId(0L);
        when(mockGoodwillApplyRepairPartInfoMapper.selectById(0L)).thenReturn(goodwillApplyRepairPartInfoPO);

        when(mockGoodwillApplyRepairPartInfoMapper.updateById(any(GoodwillApplyRepairPartInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillApplyRepairPartInfoServiceImplUnderTest.update(0L, goodwillApplyRepairPartInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testGetSupportApplyPartInfoById() throws Exception {
        // Setup
        // Configure GoodwillApplyRepairPartInfoMapper.getSupportApplyPartInfoById(...).
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setAppId("appId");
        goodwillApplyRepairPartInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyRepairPartInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairPartInfoDTO.setOrgId(0);
        goodwillApplyRepairPartInfoDTO.setId(0L);
        final List<GoodwillApplyRepairPartInfoDTO> goodwillApplyRepairPartInfoDTOS = Arrays.asList(
                goodwillApplyRepairPartInfoDTO);
        when(mockGoodwillApplyRepairPartInfoMapper.getSupportApplyPartInfoById(0L))
                .thenReturn(goodwillApplyRepairPartInfoDTOS);

        // Run the test
        final List<GoodwillApplyRepairPartInfoDTO> result = goodwillApplyRepairPartInfoServiceImplUnderTest.getSupportApplyPartInfoById(
                0L);

        // Verify the results
    }

    @Test
    void testGetSupportApplyPartInfoById_GoodwillApplyRepairPartInfoMapperReturnsNoItems() {
        // Setup
        when(mockGoodwillApplyRepairPartInfoMapper.getSupportApplyPartInfoById(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillApplyRepairPartInfoDTO> result = goodwillApplyRepairPartInfoServiceImplUnderTest.getSupportApplyPartInfoById(
                0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
