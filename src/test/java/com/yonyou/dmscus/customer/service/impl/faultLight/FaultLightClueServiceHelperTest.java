package com.yonyou.dmscus.customer.service.impl.faultLight;

import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.dto.EmailInfoDto;
import com.yonyou.dmscus.customer.dto.SmsPushDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.TmVehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.ClueNotifyDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.QueryRoleUserByCompanyCodeDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.UserInfoOutDTO;
import com.yonyou.dmscus.customer.service.CommonService;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.middleground.BasicdataCenterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FaultLightClueServiceHelperTest {

    @Mock
    private CommonService mockCommonService;
    @Mock
    private BasicdataCenterService mockBasicdataCenterService;
    @Mock
    private BusinessPlatformService mockBusinessPlatformService;

    @InjectMocks
    private FaultLightClueServiceHelper faultLightClueServiceHelperUnderTest;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(faultLightClueServiceHelperUnderTest, "publicFrom", "from");
        ReflectionTestUtils.setField(faultLightClueServiceHelperUnderTest, "subject", "subject");
        ReflectionTestUtils.setField(faultLightClueServiceHelperUnderTest, "emailText", "emailText");
        ReflectionTestUtils.setField(faultLightClueServiceHelperUnderTest, "roleCodeArray",
                new String[]{"roleCodeArray"});
        ReflectionTestUtils.setField(faultLightClueServiceHelperUnderTest, "templateCode", "templateCode");
    }

    @Test
    void testDoClueNotify_BasicdataCenterServiceReturnsNoItems() {
        // Setup
        final ClueNotifyDTO dto = ClueNotifyDTO.builder()
                .plateNumber("plateNumber")
                .vehicleVin("vehicleVin")
                .faultCityName("faultCityName")
                .clueGenTimeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueGenTime("clueDisTime")
                .warningName("warningName")
                .clueDisTimeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueDisTime("clueDisTime")
                .comments("comments")
                .dealerCode("dealerCode")
                .build();

        // Configure BusinessPlatformService.getVehicleByVIN(...).
        final TmVehicleDTO tmVehicleDTO = new TmVehicleDTO();
        tmVehicleDTO.setId(0L);
        tmVehicleDTO.setCustomerId(0L);
        tmVehicleDTO.setCompanyCode("companyCode");
        tmVehicleDTO.setOwnerParCode("ownerParCode");
        tmVehicleDTO.setPlateNumber("plateNumber");
        when(mockBusinessPlatformService.getVehicleByVIN("vehicleVin")).thenReturn(tmVehicleDTO);

        when(mockBasicdataCenterService.queryRoleUserByCompanyCode(
                any(QueryRoleUserByCompanyCodeDTO.class))).thenReturn(Collections.emptyList());

        // Run the test
        faultLightClueServiceHelperUnderTest.doClueNotify(dto);

        // Verify the results
    }

    @Test
    void testDoClueNotify_BasicdataCenterServiceThrowsServiceBizException() {
        // Setup
        final ClueNotifyDTO dto = ClueNotifyDTO.builder()
                .plateNumber("plateNumber")
                .vehicleVin("vehicleVin")
                .faultCityName("faultCityName")
                .clueGenTimeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueGenTime("clueDisTime")
                .warningName("warningName")
                .clueDisTimeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueDisTime("clueDisTime")
                .comments("comments")
                .dealerCode("dealerCode")
                .build();

        // Configure BusinessPlatformService.getVehicleByVIN(...).
        final TmVehicleDTO tmVehicleDTO = new TmVehicleDTO();
        tmVehicleDTO.setId(0L);
        tmVehicleDTO.setCustomerId(0L);
        tmVehicleDTO.setCompanyCode("companyCode");
        tmVehicleDTO.setOwnerParCode("ownerParCode");
        tmVehicleDTO.setPlateNumber("plateNumber");
        when(mockBusinessPlatformService.getVehicleByVIN("vehicleVin")).thenReturn(tmVehicleDTO);

        when(mockBasicdataCenterService.queryRoleUserByCompanyCode(any(QueryRoleUserByCompanyCodeDTO.class)))
                .thenThrow(ServiceBizException.class);

        // Run the test
        assertThatThrownBy(() -> faultLightClueServiceHelperUnderTest.doClueNotify(dto))
                .isInstanceOf(ServiceBizException.class);
    }
}
