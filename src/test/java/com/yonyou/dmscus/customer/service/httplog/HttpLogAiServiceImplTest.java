package com.yonyou.dmscus.customer.service.httplog;

import com.yonyou.dmscus.customer.dao.httpLog.HttpLogAiMapper;
import com.yonyou.dmscus.customer.entity.po.httpLog.HttpLogAiPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class HttpLogAiServiceImplTest {

    @Mock
    private HttpLogAiMapper mockHttpLogAiMapper;

    private HttpLogAiServiceImpl httpLogAiServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        httpLogAiServiceImplUnderTest = new HttpLogAiServiceImpl();
        httpLogAiServiceImplUnderTest.httpLogAiMapper = mockHttpLogAiMapper;
    }

    @Test
    void testSaveHttpLog() {
        // Setup
        // Run the test
        httpLogAiServiceImplUnderTest.saveHttpLog("describe", "requestUrl", "requestParam", "requestType",
                "responseCode", "responseMsg", "SessionId");

        // Verify the results
        // Confirm HttpLogAiMapper.insert(...).
        final HttpLogAiPO entity = new HttpLogAiPO();
        entity.setDescription("describe");
        entity.setDealerCode("dealerCode");
        entity.setRequestUrl("requestUrl");
        entity.setRequestParam("requestParam");
        entity.setRequestType("requestType");
        entity.setResponseCode("responseCode");
        entity.setResponseMsg("responseMsg");
        entity.setSessionId("SessionId");
        verify(mockHttpLogAiMapper).insert(entity);
    }
}
