package com.yonyou.dmscus.customer.service.inviteInsurance;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceSaAllocateRuleDetailMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceSaAllocateRuleDetailDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceSaAllocateRuleDetailPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteInsuranceSaAllocateRuleDetailServiceImplTest {

    @Mock
    private InviteInsuranceSaAllocateRuleDetailMapper mockInviteInsuranceSaAllocateRuleDetailMapper;

    private InviteInsuranceSaAllocateRuleDetailServiceImpl inviteInsuranceSaAllocateRuleDetailServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteInsuranceSaAllocateRuleDetailServiceImplUnderTest = new InviteInsuranceSaAllocateRuleDetailServiceImpl();
        inviteInsuranceSaAllocateRuleDetailServiceImplUnderTest.inviteInsuranceSaAllocateRuleDetailMapper = mockInviteInsuranceSaAllocateRuleDetailMapper;
    }

    @Test
    void testGetInsuranceSaAllocateRuleDetailList_InviteInsuranceSaAllocateRuleDetailMapperReturnsNoItems() {
        // Setup
        when(mockInviteInsuranceSaAllocateRuleDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteInsuranceSaAllocateRuleDetailDTO> result = inviteInsuranceSaAllocateRuleDetailServiceImplUnderTest.getInsuranceSaAllocateRuleDetailList(
                "dealerCodeList");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
