package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyFinalPartInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyFinalPartInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyFinalPartInfoPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillApplyFinalPartInfoServiceImplTest {

    @Mock
    private GoodwillApplyFinalPartInfoMapper mockGoodwillApplyFinalPartInfoMapper;

    private GoodwillApplyFinalPartInfoServiceImpl goodwillApplyFinalPartInfoServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        goodwillApplyFinalPartInfoServiceImplUnderTest = new GoodwillApplyFinalPartInfoServiceImpl();
        goodwillApplyFinalPartInfoServiceImplUnderTest.goodwillApplyFinalPartInfoMapper = mockGoodwillApplyFinalPartInfoMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setRates("rates");
        goodwillApplyFinalPartInfoDTO.setAppId("appId");
        goodwillApplyFinalPartInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyFinalPartInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyFinalPartInfoDTO.setOrgId(0);

        // Configure GoodwillApplyFinalPartInfoMapper.selectPageBySql(...).
        final GoodwillApplyFinalPartInfoPO goodwillApplyFinalPartInfoPO = new GoodwillApplyFinalPartInfoPO();
        goodwillApplyFinalPartInfoPO.setAppId("appId");
        goodwillApplyFinalPartInfoPO.setOwnerCode("ownerCode");
        goodwillApplyFinalPartInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyFinalPartInfoPO.setOrgId(0);
        goodwillApplyFinalPartInfoPO.setId(0L);
        final List<GoodwillApplyFinalPartInfoPO> goodwillApplyFinalPartInfoPOS = Arrays.asList(
                goodwillApplyFinalPartInfoPO);
        when(mockGoodwillApplyFinalPartInfoMapper.selectPageBySql(any(Page.class),
                any(GoodwillApplyFinalPartInfoPO.class))).thenReturn(goodwillApplyFinalPartInfoPOS);

        // Run the test
        final IPage<GoodwillApplyFinalPartInfoDTO> result = goodwillApplyFinalPartInfoServiceImplUnderTest.selectPageBysql(
                page, goodwillApplyFinalPartInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillApplyFinalPartInfoMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setRates("rates");
        goodwillApplyFinalPartInfoDTO.setAppId("appId");
        goodwillApplyFinalPartInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyFinalPartInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyFinalPartInfoDTO.setOrgId(0);

        when(mockGoodwillApplyFinalPartInfoMapper.selectPageBySql(any(Page.class),
                any(GoodwillApplyFinalPartInfoPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillApplyFinalPartInfoDTO> result = goodwillApplyFinalPartInfoServiceImplUnderTest.selectPageBysql(
                page, goodwillApplyFinalPartInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setRates("rates");
        goodwillApplyFinalPartInfoDTO.setAppId("appId");
        goodwillApplyFinalPartInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyFinalPartInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyFinalPartInfoDTO.setOrgId(0);

        // Configure GoodwillApplyFinalPartInfoMapper.selectListBySql(...).
        final GoodwillApplyFinalPartInfoPO goodwillApplyFinalPartInfoPO = new GoodwillApplyFinalPartInfoPO();
        goodwillApplyFinalPartInfoPO.setAppId("appId");
        goodwillApplyFinalPartInfoPO.setOwnerCode("ownerCode");
        goodwillApplyFinalPartInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyFinalPartInfoPO.setOrgId(0);
        goodwillApplyFinalPartInfoPO.setId(0L);
        final List<GoodwillApplyFinalPartInfoPO> goodwillApplyFinalPartInfoPOS = Arrays.asList(
                goodwillApplyFinalPartInfoPO);
        when(mockGoodwillApplyFinalPartInfoMapper.selectListBySql(any(GoodwillApplyFinalPartInfoPO.class)))
                .thenReturn(goodwillApplyFinalPartInfoPOS);

        // Run the test
        final List<GoodwillApplyFinalPartInfoDTO> result = goodwillApplyFinalPartInfoServiceImplUnderTest.selectListBySql(
                goodwillApplyFinalPartInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillApplyFinalPartInfoMapperReturnsNoItems() {
        // Setup
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setRates("rates");
        goodwillApplyFinalPartInfoDTO.setAppId("appId");
        goodwillApplyFinalPartInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyFinalPartInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyFinalPartInfoDTO.setOrgId(0);

        when(mockGoodwillApplyFinalPartInfoMapper.selectListBySql(any(GoodwillApplyFinalPartInfoPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillApplyFinalPartInfoDTO> result = goodwillApplyFinalPartInfoServiceImplUnderTest.selectListBySql(
                goodwillApplyFinalPartInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure GoodwillApplyFinalPartInfoMapper.selectById(...).
        final GoodwillApplyFinalPartInfoPO goodwillApplyFinalPartInfoPO = new GoodwillApplyFinalPartInfoPO();
        goodwillApplyFinalPartInfoPO.setAppId("appId");
        goodwillApplyFinalPartInfoPO.setOwnerCode("ownerCode");
        goodwillApplyFinalPartInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyFinalPartInfoPO.setOrgId(0);
        goodwillApplyFinalPartInfoPO.setId(0L);
        when(mockGoodwillApplyFinalPartInfoMapper.selectById(0L)).thenReturn(goodwillApplyFinalPartInfoPO);

        // Run the test
        final GoodwillApplyFinalPartInfoDTO result = goodwillApplyFinalPartInfoServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillApplyFinalPartInfoMapperReturnsNull() {
        // Setup
        when(mockGoodwillApplyFinalPartInfoMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillApplyFinalPartInfoServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setRates("rates");
        goodwillApplyFinalPartInfoDTO.setAppId("appId");
        goodwillApplyFinalPartInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyFinalPartInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyFinalPartInfoDTO.setOrgId(0);

        when(mockGoodwillApplyFinalPartInfoMapper.insert(any(GoodwillApplyFinalPartInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillApplyFinalPartInfoServiceImplUnderTest.insert(goodwillApplyFinalPartInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setRates("rates");
        goodwillApplyFinalPartInfoDTO.setAppId("appId");
        goodwillApplyFinalPartInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyFinalPartInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyFinalPartInfoDTO.setOrgId(0);

        // Configure GoodwillApplyFinalPartInfoMapper.selectById(...).
        final GoodwillApplyFinalPartInfoPO goodwillApplyFinalPartInfoPO = new GoodwillApplyFinalPartInfoPO();
        goodwillApplyFinalPartInfoPO.setAppId("appId");
        goodwillApplyFinalPartInfoPO.setOwnerCode("ownerCode");
        goodwillApplyFinalPartInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyFinalPartInfoPO.setOrgId(0);
        goodwillApplyFinalPartInfoPO.setId(0L);
        when(mockGoodwillApplyFinalPartInfoMapper.selectById(0L)).thenReturn(goodwillApplyFinalPartInfoPO);

        when(mockGoodwillApplyFinalPartInfoMapper.updateById(any(GoodwillApplyFinalPartInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillApplyFinalPartInfoServiceImplUnderTest.update(0L, goodwillApplyFinalPartInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testQueryPartAmountInfo() {
        // Setup
        // Configure GoodwillApplyFinalPartInfoMapper.queryPartAmountInfo(...).
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setRates("rates");
        goodwillApplyFinalPartInfoDTO.setAppId("appId");
        goodwillApplyFinalPartInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyFinalPartInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyFinalPartInfoDTO.setOrgId(0);
        final List<GoodwillApplyFinalPartInfoDTO> goodwillApplyFinalPartInfoDTOS = Arrays.asList(
                goodwillApplyFinalPartInfoDTO);
        when(mockGoodwillApplyFinalPartInfoMapper.queryPartAmountInfo(0, 0L))
                .thenReturn(goodwillApplyFinalPartInfoDTOS);

        // Run the test
        final List<GoodwillApplyFinalPartInfoDTO> result = goodwillApplyFinalPartInfoServiceImplUnderTest.queryPartAmountInfo(
                0, 0L);

        // Verify the results
    }

    @Test
    void testQueryPartAmountInfo_GoodwillApplyFinalPartInfoMapperReturnsNoItems() {
        // Setup
        when(mockGoodwillApplyFinalPartInfoMapper.queryPartAmountInfo(0, 0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillApplyFinalPartInfoDTO> result = goodwillApplyFinalPartInfoServiceImplUnderTest.queryPartAmountInfo(
                0, 0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
