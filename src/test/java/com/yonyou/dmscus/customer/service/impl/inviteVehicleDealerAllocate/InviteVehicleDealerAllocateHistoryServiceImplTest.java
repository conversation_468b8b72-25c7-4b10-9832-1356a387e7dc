package com.yonyou.dmscus.customer.service.impl.inviteVehicleDealerAllocate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryDTO;
import com.yonyou.dmscus.customer.entity.po.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteVehicleDealerAllocateHistoryServiceImplTest {

    @Mock
    private InviteVehicleDealerAllocateHistoryMapper mockInviteVehicleDealerAllocateHistoryMapper;

    private InviteVehicleDealerAllocateHistoryServiceImpl inviteVehicleDealerAllocateHistoryServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteVehicleDealerAllocateHistoryServiceImplUnderTest = new InviteVehicleDealerAllocateHistoryServiceImpl();
        inviteVehicleDealerAllocateHistoryServiceImplUnderTest.inviteVehicleDealerAllocateHistoryMapper = mockInviteVehicleDealerAllocateHistoryMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final InviteVehicleDealerAllocateHistoryDTO inviteVehicleDealerAllocateHistoryDTO = new InviteVehicleDealerAllocateHistoryDTO();
        inviteVehicleDealerAllocateHistoryDTO.setOperatorName("operatorName");
        inviteVehicleDealerAllocateHistoryDTO.setAppId("appId");
        inviteVehicleDealerAllocateHistoryDTO.setOwnerCode("ownerCode");
        inviteVehicleDealerAllocateHistoryDTO.setOwnerParCode("ownerParCode");
        inviteVehicleDealerAllocateHistoryDTO.setOrgId(0);

        // Configure InviteVehicleDealerAllocateHistoryMapper.selectPageBySql(...).
        final InviteVehicleDealerAllocateHistoryPO inviteVehicleDealerAllocateHistoryPO = new InviteVehicleDealerAllocateHistoryPO();
        inviteVehicleDealerAllocateHistoryPO.setOperatorName("operatorName");
        inviteVehicleDealerAllocateHistoryPO.setOwnerCode("ownerCode");
        inviteVehicleDealerAllocateHistoryPO.setOrgId(0);
        inviteVehicleDealerAllocateHistoryPO.setId(0L);
        inviteVehicleDealerAllocateHistoryPO.setVin("vin");
        final List<InviteVehicleDealerAllocateHistoryPO> inviteVehicleDealerAllocateHistoryPOS = Arrays.asList(
                inviteVehicleDealerAllocateHistoryPO);
        when(mockInviteVehicleDealerAllocateHistoryMapper.selectPageBySql(any(Page.class),
                any(InviteVehicleDealerAllocateHistoryPO.class))).thenReturn(inviteVehicleDealerAllocateHistoryPOS);

        // Run the test
        final IPage<InviteVehicleDealerAllocateHistoryDTO> result = inviteVehicleDealerAllocateHistoryServiceImplUnderTest.selectPageBysql(
                page, inviteVehicleDealerAllocateHistoryDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_InviteVehicleDealerAllocateHistoryMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final InviteVehicleDealerAllocateHistoryDTO inviteVehicleDealerAllocateHistoryDTO = new InviteVehicleDealerAllocateHistoryDTO();
        inviteVehicleDealerAllocateHistoryDTO.setOperatorName("operatorName");
        inviteVehicleDealerAllocateHistoryDTO.setAppId("appId");
        inviteVehicleDealerAllocateHistoryDTO.setOwnerCode("ownerCode");
        inviteVehicleDealerAllocateHistoryDTO.setOwnerParCode("ownerParCode");
        inviteVehicleDealerAllocateHistoryDTO.setOrgId(0);

        when(mockInviteVehicleDealerAllocateHistoryMapper.selectPageBySql(any(Page.class),
                any(InviteVehicleDealerAllocateHistoryPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<InviteVehicleDealerAllocateHistoryDTO> result = inviteVehicleDealerAllocateHistoryServiceImplUnderTest.selectPageBysql(
                page, inviteVehicleDealerAllocateHistoryDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final InviteVehicleDealerAllocateHistoryDTO inviteVehicleDealerAllocateHistoryDTO = new InviteVehicleDealerAllocateHistoryDTO();
        inviteVehicleDealerAllocateHistoryDTO.setOperatorName("operatorName");
        inviteVehicleDealerAllocateHistoryDTO.setAppId("appId");
        inviteVehicleDealerAllocateHistoryDTO.setOwnerCode("ownerCode");
        inviteVehicleDealerAllocateHistoryDTO.setOwnerParCode("ownerParCode");
        inviteVehicleDealerAllocateHistoryDTO.setOrgId(0);

        // Configure InviteVehicleDealerAllocateHistoryMapper.selectListBySql(...).
        final InviteVehicleDealerAllocateHistoryPO inviteVehicleDealerAllocateHistoryPO = new InviteVehicleDealerAllocateHistoryPO();
        inviteVehicleDealerAllocateHistoryPO.setOperatorName("operatorName");
        inviteVehicleDealerAllocateHistoryPO.setOwnerCode("ownerCode");
        inviteVehicleDealerAllocateHistoryPO.setOrgId(0);
        inviteVehicleDealerAllocateHistoryPO.setId(0L);
        inviteVehicleDealerAllocateHistoryPO.setVin("vin");
        final List<InviteVehicleDealerAllocateHistoryPO> inviteVehicleDealerAllocateHistoryPOS = Arrays.asList(
                inviteVehicleDealerAllocateHistoryPO);
        when(mockInviteVehicleDealerAllocateHistoryMapper.selectListBySql(
                any(InviteVehicleDealerAllocateHistoryPO.class))).thenReturn(inviteVehicleDealerAllocateHistoryPOS);

        // Run the test
        final List<InviteVehicleDealerAllocateHistoryDTO> result = inviteVehicleDealerAllocateHistoryServiceImplUnderTest.selectListBySql(
                inviteVehicleDealerAllocateHistoryDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_InviteVehicleDealerAllocateHistoryMapperReturnsNoItems() {
        // Setup
        final InviteVehicleDealerAllocateHistoryDTO inviteVehicleDealerAllocateHistoryDTO = new InviteVehicleDealerAllocateHistoryDTO();
        inviteVehicleDealerAllocateHistoryDTO.setOperatorName("operatorName");
        inviteVehicleDealerAllocateHistoryDTO.setAppId("appId");
        inviteVehicleDealerAllocateHistoryDTO.setOwnerCode("ownerCode");
        inviteVehicleDealerAllocateHistoryDTO.setOwnerParCode("ownerParCode");
        inviteVehicleDealerAllocateHistoryDTO.setOrgId(0);

        when(mockInviteVehicleDealerAllocateHistoryMapper.selectListBySql(
                any(InviteVehicleDealerAllocateHistoryPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteVehicleDealerAllocateHistoryDTO> result = inviteVehicleDealerAllocateHistoryServiceImplUnderTest.selectListBySql(
                inviteVehicleDealerAllocateHistoryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure InviteVehicleDealerAllocateHistoryMapper.selectById(...).
        final InviteVehicleDealerAllocateHistoryPO inviteVehicleDealerAllocateHistoryPO = new InviteVehicleDealerAllocateHistoryPO();
        inviteVehicleDealerAllocateHistoryPO.setOperatorName("operatorName");
        inviteVehicleDealerAllocateHistoryPO.setOwnerCode("ownerCode");
        inviteVehicleDealerAllocateHistoryPO.setOrgId(0);
        inviteVehicleDealerAllocateHistoryPO.setId(0L);
        inviteVehicleDealerAllocateHistoryPO.setVin("vin");
        when(mockInviteVehicleDealerAllocateHistoryMapper.selectById(0L))
                .thenReturn(inviteVehicleDealerAllocateHistoryPO);

        // Run the test
        final InviteVehicleDealerAllocateHistoryDTO result = inviteVehicleDealerAllocateHistoryServiceImplUnderTest.getById(
                0L);

        // Verify the results
    }

    @Test
    void testGetById_InviteVehicleDealerAllocateHistoryMapperReturnsNull() {
        // Setup
        when(mockInviteVehicleDealerAllocateHistoryMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inviteVehicleDealerAllocateHistoryServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final InviteVehicleDealerAllocateHistoryDTO inviteVehicleDealerAllocateHistoryDTO = new InviteVehicleDealerAllocateHistoryDTO();
        inviteVehicleDealerAllocateHistoryDTO.setOperatorName("operatorName");
        inviteVehicleDealerAllocateHistoryDTO.setAppId("appId");
        inviteVehicleDealerAllocateHistoryDTO.setOwnerCode("ownerCode");
        inviteVehicleDealerAllocateHistoryDTO.setOwnerParCode("ownerParCode");
        inviteVehicleDealerAllocateHistoryDTO.setOrgId(0);

        when(mockInviteVehicleDealerAllocateHistoryMapper.insert(
                any(InviteVehicleDealerAllocateHistoryPO.class))).thenReturn(0);

        // Run the test
        final int result = inviteVehicleDealerAllocateHistoryServiceImplUnderTest.insert(
                inviteVehicleDealerAllocateHistoryDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final InviteVehicleDealerAllocateHistoryDTO inviteVehicleDealerAllocateHistoryDTO = new InviteVehicleDealerAllocateHistoryDTO();
        inviteVehicleDealerAllocateHistoryDTO.setOperatorName("operatorName");
        inviteVehicleDealerAllocateHistoryDTO.setAppId("appId");
        inviteVehicleDealerAllocateHistoryDTO.setOwnerCode("ownerCode");
        inviteVehicleDealerAllocateHistoryDTO.setOwnerParCode("ownerParCode");
        inviteVehicleDealerAllocateHistoryDTO.setOrgId(0);

        // Configure InviteVehicleDealerAllocateHistoryMapper.selectById(...).
        final InviteVehicleDealerAllocateHistoryPO inviteVehicleDealerAllocateHistoryPO = new InviteVehicleDealerAllocateHistoryPO();
        inviteVehicleDealerAllocateHistoryPO.setOperatorName("operatorName");
        inviteVehicleDealerAllocateHistoryPO.setOwnerCode("ownerCode");
        inviteVehicleDealerAllocateHistoryPO.setOrgId(0);
        inviteVehicleDealerAllocateHistoryPO.setId(0L);
        inviteVehicleDealerAllocateHistoryPO.setVin("vin");
        when(mockInviteVehicleDealerAllocateHistoryMapper.selectById(0L))
                .thenReturn(inviteVehicleDealerAllocateHistoryPO);

        when(mockInviteVehicleDealerAllocateHistoryMapper.updateById(
                any(InviteVehicleDealerAllocateHistoryPO.class))).thenReturn(0);

        // Run the test
        final int result = inviteVehicleDealerAllocateHistoryServiceImplUnderTest.update(0L,
                inviteVehicleDealerAllocateHistoryDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testGetAllocationHistory() {
        // Setup
        // Configure InviteVehicleDealerAllocateHistoryMapper.selectList(...).
        final InviteVehicleDealerAllocateHistoryPO inviteVehicleDealerAllocateHistoryPO = new InviteVehicleDealerAllocateHistoryPO();
        inviteVehicleDealerAllocateHistoryPO.setOperatorName("operatorName");
        inviteVehicleDealerAllocateHistoryPO.setOwnerCode("ownerCode");
        inviteVehicleDealerAllocateHistoryPO.setOrgId(0);
        inviteVehicleDealerAllocateHistoryPO.setId(0L);
        inviteVehicleDealerAllocateHistoryPO.setVin("vin");
        final List<InviteVehicleDealerAllocateHistoryPO> inviteVehicleDealerAllocateHistoryPOS = Arrays.asList(
                inviteVehicleDealerAllocateHistoryPO);
        when(mockInviteVehicleDealerAllocateHistoryMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(inviteVehicleDealerAllocateHistoryPOS);

        // Run the test
        final List<InviteVehicleDealerAllocateHistoryDTO> result = inviteVehicleDealerAllocateHistoryServiceImplUnderTest.getAllocationHistory(
                "vin");

        // Verify the results
    }

    @Test
    void testGetAllocationHistory_InviteVehicleDealerAllocateHistoryMapperReturnsNoItems() {
        // Setup
        when(mockInviteVehicleDealerAllocateHistoryMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteVehicleDealerAllocateHistoryDTO> result = inviteVehicleDealerAllocateHistoryServiceImplUnderTest.getAllocationHistory(
                "vin");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
