package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintKpiBaseRuleUseMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleUseTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiTest;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintKpiBaseRuleUseDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRuleUseTestPO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintKpiBaseRuleUsePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SaleComplaintKpiBaseRuleUseServiceImplTest {

    @Mock
    private SaleComplaintKpiBaseRuleUseMapper mockSaleComplaintKpiBaseRuleUseMapper;

    private SaleComplaintKpiBaseRuleUseServiceImpl saleComplaintKpiBaseRuleUseServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        saleComplaintKpiBaseRuleUseServiceImplUnderTest = new SaleComplaintKpiBaseRuleUseServiceImpl();
        saleComplaintKpiBaseRuleUseServiceImplUnderTest.saleComplaintKpiBaseRuleUseMapper = mockSaleComplaintKpiBaseRuleUseMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintKpiBaseRuleUseDTO saleComplaintKpiBaseRuleUseDTO = new SaleComplaintKpiBaseRuleUseDTO();
        saleComplaintKpiBaseRuleUseDTO.setId(0L);
        saleComplaintKpiBaseRuleUseDTO.setRuleId(0L);
        saleComplaintKpiBaseRuleUseDTO.setDealerCode("dealerCode");
        saleComplaintKpiBaseRuleUseDTO.setUser("user");
        saleComplaintKpiBaseRuleUseDTO.setWarnValue("warnValue");

        // Configure SaleComplaintKpiBaseRuleUseMapper.selectPageBySql(...).
        final SaleComplaintKpiBaseRuleUsePO saleComplaintKpiBaseRuleUsePO = new SaleComplaintKpiBaseRuleUsePO();
        saleComplaintKpiBaseRuleUsePO.setAppId("appId");
        saleComplaintKpiBaseRuleUsePO.setOwnerCode("ownerCode");
        saleComplaintKpiBaseRuleUsePO.setOwnerParCode("ownerParCode");
        saleComplaintKpiBaseRuleUsePO.setOrgId(0);
        saleComplaintKpiBaseRuleUsePO.setId(0L);
        final List<SaleComplaintKpiBaseRuleUsePO> saleComplaintKpiBaseRuleUsePOS = Arrays.asList(
                saleComplaintKpiBaseRuleUsePO);
        when(mockSaleComplaintKpiBaseRuleUseMapper.selectPageBySql(any(Page.class),
                any(SaleComplaintKpiBaseRuleUsePO.class))).thenReturn(saleComplaintKpiBaseRuleUsePOS);

        // Run the test
        final IPage<SaleComplaintKpiBaseRuleUseDTO> result = saleComplaintKpiBaseRuleUseServiceImplUnderTest.selectPageBysql(
                page, saleComplaintKpiBaseRuleUseDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_SaleComplaintKpiBaseRuleUseMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintKpiBaseRuleUseDTO saleComplaintKpiBaseRuleUseDTO = new SaleComplaintKpiBaseRuleUseDTO();
        saleComplaintKpiBaseRuleUseDTO.setId(0L);
        saleComplaintKpiBaseRuleUseDTO.setRuleId(0L);
        saleComplaintKpiBaseRuleUseDTO.setDealerCode("dealerCode");
        saleComplaintKpiBaseRuleUseDTO.setUser("user");
        saleComplaintKpiBaseRuleUseDTO.setWarnValue("warnValue");

        when(mockSaleComplaintKpiBaseRuleUseMapper.selectPageBySql(any(Page.class),
                any(SaleComplaintKpiBaseRuleUsePO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<SaleComplaintKpiBaseRuleUseDTO> result = saleComplaintKpiBaseRuleUseServiceImplUnderTest.selectPageBysql(
                page, saleComplaintKpiBaseRuleUseDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final SaleComplaintKpiBaseRuleUseDTO saleComplaintKpiBaseRuleUseDTO = new SaleComplaintKpiBaseRuleUseDTO();
        saleComplaintKpiBaseRuleUseDTO.setId(0L);
        saleComplaintKpiBaseRuleUseDTO.setRuleId(0L);
        saleComplaintKpiBaseRuleUseDTO.setDealerCode("dealerCode");
        saleComplaintKpiBaseRuleUseDTO.setUser("user");
        saleComplaintKpiBaseRuleUseDTO.setWarnValue("warnValue");

        // Configure SaleComplaintKpiBaseRuleUseMapper.selectListBySql(...).
        final SaleComplaintKpiBaseRuleUsePO saleComplaintKpiBaseRuleUsePO = new SaleComplaintKpiBaseRuleUsePO();
        saleComplaintKpiBaseRuleUsePO.setAppId("appId");
        saleComplaintKpiBaseRuleUsePO.setOwnerCode("ownerCode");
        saleComplaintKpiBaseRuleUsePO.setOwnerParCode("ownerParCode");
        saleComplaintKpiBaseRuleUsePO.setOrgId(0);
        saleComplaintKpiBaseRuleUsePO.setId(0L);
        final List<SaleComplaintKpiBaseRuleUsePO> saleComplaintKpiBaseRuleUsePOS = Arrays.asList(
                saleComplaintKpiBaseRuleUsePO);
        when(mockSaleComplaintKpiBaseRuleUseMapper.selectListBySql(
                any(SaleComplaintKpiBaseRuleUsePO.class))).thenReturn(saleComplaintKpiBaseRuleUsePOS);

        // Run the test
        final List<SaleComplaintKpiBaseRuleUseDTO> result = saleComplaintKpiBaseRuleUseServiceImplUnderTest.selectListBySql(
                saleComplaintKpiBaseRuleUseDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_SaleComplaintKpiBaseRuleUseMapperReturnsNoItems() {
        // Setup
        final SaleComplaintKpiBaseRuleUseDTO saleComplaintKpiBaseRuleUseDTO = new SaleComplaintKpiBaseRuleUseDTO();
        saleComplaintKpiBaseRuleUseDTO.setId(0L);
        saleComplaintKpiBaseRuleUseDTO.setRuleId(0L);
        saleComplaintKpiBaseRuleUseDTO.setDealerCode("dealerCode");
        saleComplaintKpiBaseRuleUseDTO.setUser("user");
        saleComplaintKpiBaseRuleUseDTO.setWarnValue("warnValue");

        when(mockSaleComplaintKpiBaseRuleUseMapper.selectListBySql(
                any(SaleComplaintKpiBaseRuleUsePO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<SaleComplaintKpiBaseRuleUseDTO> result = saleComplaintKpiBaseRuleUseServiceImplUnderTest.selectListBySql(
                saleComplaintKpiBaseRuleUseDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure SaleComplaintKpiBaseRuleUseMapper.selectById(...).
        final SaleComplaintKpiBaseRuleUsePO saleComplaintKpiBaseRuleUsePO = new SaleComplaintKpiBaseRuleUsePO();
        saleComplaintKpiBaseRuleUsePO.setAppId("appId");
        saleComplaintKpiBaseRuleUsePO.setOwnerCode("ownerCode");
        saleComplaintKpiBaseRuleUsePO.setOwnerParCode("ownerParCode");
        saleComplaintKpiBaseRuleUsePO.setOrgId(0);
        saleComplaintKpiBaseRuleUsePO.setId(0L);
        when(mockSaleComplaintKpiBaseRuleUseMapper.selectById(0L)).thenReturn(saleComplaintKpiBaseRuleUsePO);

        // Run the test
        final SaleComplaintKpiBaseRuleUseDTO result = saleComplaintKpiBaseRuleUseServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_SaleComplaintKpiBaseRuleUseMapperReturnsNull() {
        // Setup
        when(mockSaleComplaintKpiBaseRuleUseMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> saleComplaintKpiBaseRuleUseServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final SaleComplaintKpiBaseRuleUseDTO saleComplaintKpiBaseRuleUseDTO = new SaleComplaintKpiBaseRuleUseDTO();
        saleComplaintKpiBaseRuleUseDTO.setId(0L);
        saleComplaintKpiBaseRuleUseDTO.setRuleId(0L);
        saleComplaintKpiBaseRuleUseDTO.setDealerCode("dealerCode");
        saleComplaintKpiBaseRuleUseDTO.setUser("user");
        saleComplaintKpiBaseRuleUseDTO.setWarnValue("warnValue");

        when(mockSaleComplaintKpiBaseRuleUseMapper.insert(any(SaleComplaintKpiBaseRuleUsePO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintKpiBaseRuleUseServiceImplUnderTest.insert(saleComplaintKpiBaseRuleUseDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final SaleComplaintKpiBaseRuleUseDTO saleComplaintKpiBaseRuleUseDTO = new SaleComplaintKpiBaseRuleUseDTO();
        saleComplaintKpiBaseRuleUseDTO.setId(0L);
        saleComplaintKpiBaseRuleUseDTO.setRuleId(0L);
        saleComplaintKpiBaseRuleUseDTO.setDealerCode("dealerCode");
        saleComplaintKpiBaseRuleUseDTO.setUser("user");
        saleComplaintKpiBaseRuleUseDTO.setWarnValue("warnValue");

        // Configure SaleComplaintKpiBaseRuleUseMapper.selectById(...).
        final SaleComplaintKpiBaseRuleUsePO saleComplaintKpiBaseRuleUsePO = new SaleComplaintKpiBaseRuleUsePO();
        saleComplaintKpiBaseRuleUsePO.setAppId("appId");
        saleComplaintKpiBaseRuleUsePO.setOwnerCode("ownerCode");
        saleComplaintKpiBaseRuleUsePO.setOwnerParCode("ownerParCode");
        saleComplaintKpiBaseRuleUsePO.setOrgId(0);
        saleComplaintKpiBaseRuleUsePO.setId(0L);
        when(mockSaleComplaintKpiBaseRuleUseMapper.selectById(0L)).thenReturn(saleComplaintKpiBaseRuleUsePO);

        when(mockSaleComplaintKpiBaseRuleUseMapper.updateById(any(SaleComplaintKpiBaseRuleUsePO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintKpiBaseRuleUseServiceImplUnderTest.update(0L, saleComplaintKpiBaseRuleUseDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectListBySql1() {
        // Setup
        final ComplaintKpiBaseRuleUseTestDTO complaintKpiBaseRuleUseTestDTO = new ComplaintKpiBaseRuleUseTestDTO();
        complaintKpiBaseRuleUseTestDTO.setAppId("appId");
        complaintKpiBaseRuleUseTestDTO.setOwnerCode("ownerCode");
        complaintKpiBaseRuleUseTestDTO.setOwnerParCode("ownerParCode");
        complaintKpiBaseRuleUseTestDTO.setOrgId(0);
        complaintKpiBaseRuleUseTestDTO.setId(0L);

        // Configure SaleComplaintKpiBaseRuleUseMapper.selectListBySql1(...).
        final ComplaintKpiBaseRuleUseTestPO complaintKpiBaseRuleUseTestPO = new ComplaintKpiBaseRuleUseTestPO();
        complaintKpiBaseRuleUseTestPO.setOwnerCode("ownerCode");
        complaintKpiBaseRuleUseTestPO.setOrgId(0);
        complaintKpiBaseRuleUseTestPO.setId(0L);
        complaintKpiBaseRuleUseTestPO.setRuleId(0L);
        complaintKpiBaseRuleUseTestPO.setDealerCode("dealerCode");
        final List<ComplaintKpiBaseRuleUseTestPO> complaintKpiBaseRuleUseTestPOS = Arrays.asList(
                complaintKpiBaseRuleUseTestPO);
        when(mockSaleComplaintKpiBaseRuleUseMapper.selectListBySql1(
                any(ComplaintKpiBaseRuleUseTestPO.class))).thenReturn(complaintKpiBaseRuleUseTestPOS);

        // Run the test
        final List<ComplaintKpiBaseRuleUseTestDTO> result = saleComplaintKpiBaseRuleUseServiceImplUnderTest.selectListBySql1(
                complaintKpiBaseRuleUseTestDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql1_SaleComplaintKpiBaseRuleUseMapperReturnsNoItems() {
        // Setup
        final ComplaintKpiBaseRuleUseTestDTO complaintKpiBaseRuleUseTestDTO = new ComplaintKpiBaseRuleUseTestDTO();
        complaintKpiBaseRuleUseTestDTO.setAppId("appId");
        complaintKpiBaseRuleUseTestDTO.setOwnerCode("ownerCode");
        complaintKpiBaseRuleUseTestDTO.setOwnerParCode("ownerParCode");
        complaintKpiBaseRuleUseTestDTO.setOrgId(0);
        complaintKpiBaseRuleUseTestDTO.setId(0L);

        when(mockSaleComplaintKpiBaseRuleUseMapper.selectListBySql1(
                any(ComplaintKpiBaseRuleUseTestPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintKpiBaseRuleUseTestDTO> result = saleComplaintKpiBaseRuleUseServiceImplUnderTest.selectListBySql1(
                complaintKpiBaseRuleUseTestDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
