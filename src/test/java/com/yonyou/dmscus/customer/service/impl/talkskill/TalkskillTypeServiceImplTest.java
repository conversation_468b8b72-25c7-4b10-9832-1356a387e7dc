package com.yonyou.dmscus.customer.service.impl.talkskill;

import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.talkskill.TalkskillTypeMapper;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillTypeDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillTypePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TalkskillTypeServiceImplTest {

    @Mock
    private TalkskillTypeMapper mockTalkskillTypeMapper;

    private TalkskillTypeServiceImpl talkskillTypeServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        talkskillTypeServiceImplUnderTest = new TalkskillTypeServiceImpl();
        talkskillTypeServiceImplUnderTest.talkskillTypeMapper = mockTalkskillTypeMapper;
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final TalkskillTypeDTO talkskillTypeDTO = new TalkskillTypeDTO();
        talkskillTypeDTO.setTypeId(0L);
        talkskillTypeDTO.setTypeCode("typeCode");
        talkskillTypeDTO.setTypeName("typeName");
        talkskillTypeDTO.setRemark("remark");
        talkskillTypeDTO.setDataSources(0);

        final TalkskillTypePO talkskillTypePO = new TalkskillTypePO();
        talkskillTypePO.setTypeId(0L);
        talkskillTypePO.setTypeCode("typeCode");
        talkskillTypePO.setTypeName("typeName");
        talkskillTypePO.setRemark("remark");
        talkskillTypePO.setDataSources(0);
        final List<TalkskillTypePO> expectedResult = Arrays.asList(talkskillTypePO);

        // Configure TalkskillTypeMapper.selectListBySql(...).
        final TalkskillTypePO talkskillTypePO1 = new TalkskillTypePO();
        talkskillTypePO1.setTypeId(0L);
        talkskillTypePO1.setTypeCode("typeCode");
        talkskillTypePO1.setTypeName("typeName");
        talkskillTypePO1.setRemark("remark");
        talkskillTypePO1.setDataSources(0);
        final List<TalkskillTypePO> talkskillTypePOS = Arrays.asList(talkskillTypePO1);
        final TalkskillTypePO t = new TalkskillTypePO();
        t.setTypeId(0L);
        t.setTypeCode("typeCode");
        t.setTypeName("typeName");
        t.setRemark("remark");
        t.setDataSources(0);
        when(mockTalkskillTypeMapper.selectListBySql(t)).thenReturn(talkskillTypePOS);

        // Run the test
        final List<TalkskillTypePO> result = talkskillTypeServiceImplUnderTest.selectListBySql(talkskillTypeDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectListBySql_TalkskillTypeMapperReturnsNoItems() {
        // Setup
        final TalkskillTypeDTO talkskillTypeDTO = new TalkskillTypeDTO();
        talkskillTypeDTO.setTypeId(0L);
        talkskillTypeDTO.setTypeCode("typeCode");
        talkskillTypeDTO.setTypeName("typeName");
        talkskillTypeDTO.setRemark("remark");
        talkskillTypeDTO.setDataSources(0);

        // Configure TalkskillTypeMapper.selectListBySql(...).
        final TalkskillTypePO t = new TalkskillTypePO();
        t.setTypeId(0L);
        t.setTypeCode("typeCode");
        t.setTypeName("typeName");
        t.setRemark("remark");
        t.setDataSources(0);
        when(mockTalkskillTypeMapper.selectListBySql(t)).thenReturn(Collections.emptyList());

        // Run the test
        final List<TalkskillTypePO> result = talkskillTypeServiceImplUnderTest.selectListBySql(talkskillTypeDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        final TalkskillTypeDTO expectedResult = new TalkskillTypeDTO();
        expectedResult.setTypeId(0L);
        expectedResult.setTypeCode("typeCode");
        expectedResult.setTypeName("typeName");
        expectedResult.setRemark("remark");
        expectedResult.setDataSources(0);

        // Configure TalkskillTypeMapper.selectById(...).
        final TalkskillTypePO talkskillTypePO = new TalkskillTypePO();
        talkskillTypePO.setTypeId(0L);
        talkskillTypePO.setTypeCode("typeCode");
        talkskillTypePO.setTypeName("typeName");
        talkskillTypePO.setRemark("remark");
        talkskillTypePO.setDataSources(0);
        when(mockTalkskillTypeMapper.selectById(0L)).thenReturn(talkskillTypePO);

        // Run the test
        final TalkskillTypeDTO result = talkskillTypeServiceImplUnderTest.getById(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetById_TalkskillTypeMapperReturnsNull() {
        // Setup
        when(mockTalkskillTypeMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> talkskillTypeServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final TalkskillTypeDTO talkskillTypeDTO = new TalkskillTypeDTO();
        talkskillTypeDTO.setTypeId(0L);
        talkskillTypeDTO.setTypeCode("typeCode");
        talkskillTypeDTO.setTypeName("typeName");
        talkskillTypeDTO.setRemark("remark");
        talkskillTypeDTO.setDataSources(0);

        // Configure TalkskillTypeMapper.insert(...).
        final TalkskillTypePO entity = new TalkskillTypePO();
        entity.setTypeId(0L);
        entity.setTypeCode("typeCode");
        entity.setTypeName("typeName");
        entity.setRemark("remark");
        entity.setDataSources(0);
        when(mockTalkskillTypeMapper.insert(entity)).thenReturn(0);

        // Run the test
        final int result = talkskillTypeServiceImplUnderTest.insert(talkskillTypeDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final TalkskillTypeDTO talkskillTypeDTO = new TalkskillTypeDTO();
        talkskillTypeDTO.setTypeId(0L);
        talkskillTypeDTO.setTypeCode("typeCode");
        talkskillTypeDTO.setTypeName("typeName");
        talkskillTypeDTO.setRemark("remark");
        talkskillTypeDTO.setDataSources(0);

        // Configure TalkskillTypeMapper.selectById(...).
        final TalkskillTypePO talkskillTypePO = new TalkskillTypePO();
        talkskillTypePO.setTypeId(0L);
        talkskillTypePO.setTypeCode("typeCode");
        talkskillTypePO.setTypeName("typeName");
        talkskillTypePO.setRemark("remark");
        talkskillTypePO.setDataSources(0);
        when(mockTalkskillTypeMapper.selectById(0L)).thenReturn(talkskillTypePO);

        // Configure TalkskillTypeMapper.updateById(...).
        final TalkskillTypePO entity = new TalkskillTypePO();
        entity.setTypeId(0L);
        entity.setTypeCode("typeCode");
        entity.setTypeName("typeName");
        entity.setRemark("remark");
        entity.setDataSources(0);
        when(mockTalkskillTypeMapper.updateById(entity)).thenReturn(0);

        // Run the test
        final int result = talkskillTypeServiceImplUnderTest.update(0L, talkskillTypeDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdateList() {
        // Setup
        final TalkskillTypeDTO talkskillTypeDTO1 = new TalkskillTypeDTO();
        talkskillTypeDTO1.setTypeId(0L);
        talkskillTypeDTO1.setTypeCode("typeCode");
        talkskillTypeDTO1.setTypeName("typeName");
        talkskillTypeDTO1.setRemark("remark");
        talkskillTypeDTO1.setDataSources(0);
        final List<TalkskillTypeDTO> talkskillTypeDTO = Arrays.asList(talkskillTypeDTO1);

        // Run the test
        final int result = talkskillTypeServiceImplUnderTest.updateList(talkskillTypeDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);

        // Confirm TalkskillTypeMapper.insert(...).
        final TalkskillTypePO entity = new TalkskillTypePO();
        entity.setTypeId(0L);
        entity.setTypeCode("typeCode");
        entity.setTypeName("typeName");
        entity.setRemark("remark");
        entity.setDataSources(0);
        verify(mockTalkskillTypeMapper).insert(entity);

        // Confirm TalkskillTypeMapper.updateById(...).
        final TalkskillTypePO entity1 = new TalkskillTypePO();
        entity1.setTypeId(0L);
        entity1.setTypeCode("typeCode");
        entity1.setTypeName("typeName");
        entity1.setRemark("remark");
        entity1.setDataSources(0);
        verify(mockTalkskillTypeMapper).updateById(entity1);
    }
}
