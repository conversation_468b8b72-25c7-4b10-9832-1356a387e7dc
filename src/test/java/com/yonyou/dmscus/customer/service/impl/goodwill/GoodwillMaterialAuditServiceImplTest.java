package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillMaterialAuditMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMaterialAuditDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMaterialAuditPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillMaterialAuditServiceImplTest {

    @Mock
    private GoodwillMaterialAuditMapper mockGoodwillMaterialAuditMapper;

    private GoodwillMaterialAuditServiceImpl goodwillMaterialAuditServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        goodwillMaterialAuditServiceImplUnderTest = new GoodwillMaterialAuditServiceImpl();
        goodwillMaterialAuditServiceImplUnderTest.goodwillMaterialAuditMapper = mockGoodwillMaterialAuditMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillMaterialAuditDTO goodwillMaterialAuditDTO = new GoodwillMaterialAuditDTO();
        goodwillMaterialAuditDTO.setAppId("appId");
        goodwillMaterialAuditDTO.setOwnerCode("ownerCode");
        goodwillMaterialAuditDTO.setOwnerParCode("ownerParCode");
        goodwillMaterialAuditDTO.setOrgId(0);
        goodwillMaterialAuditDTO.setId(0L);

        // Configure GoodwillMaterialAuditMapper.selectPageBySql(...).
        final GoodwillMaterialAuditPO goodwillMaterialAuditPO = new GoodwillMaterialAuditPO();
        goodwillMaterialAuditPO.setAppId("appId");
        goodwillMaterialAuditPO.setOwnerCode("ownerCode");
        goodwillMaterialAuditPO.setOwnerParCode("ownerParCode");
        goodwillMaterialAuditPO.setOrgId(0);
        goodwillMaterialAuditPO.setId(0L);
        final List<GoodwillMaterialAuditPO> goodwillMaterialAuditPOS = Arrays.asList(goodwillMaterialAuditPO);
        when(mockGoodwillMaterialAuditMapper.selectPageBySql(any(Page.class),
                any(GoodwillMaterialAuditPO.class))).thenReturn(goodwillMaterialAuditPOS);

        // Run the test
        final IPage<GoodwillMaterialAuditDTO> result = goodwillMaterialAuditServiceImplUnderTest.selectPageBysql(page,
                goodwillMaterialAuditDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillMaterialAuditMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillMaterialAuditDTO goodwillMaterialAuditDTO = new GoodwillMaterialAuditDTO();
        goodwillMaterialAuditDTO.setAppId("appId");
        goodwillMaterialAuditDTO.setOwnerCode("ownerCode");
        goodwillMaterialAuditDTO.setOwnerParCode("ownerParCode");
        goodwillMaterialAuditDTO.setOrgId(0);
        goodwillMaterialAuditDTO.setId(0L);

        when(mockGoodwillMaterialAuditMapper.selectPageBySql(any(Page.class),
                any(GoodwillMaterialAuditPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillMaterialAuditDTO> result = goodwillMaterialAuditServiceImplUnderTest.selectPageBysql(page,
                goodwillMaterialAuditDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final GoodwillMaterialAuditDTO goodwillMaterialAuditDTO = new GoodwillMaterialAuditDTO();
        goodwillMaterialAuditDTO.setAppId("appId");
        goodwillMaterialAuditDTO.setOwnerCode("ownerCode");
        goodwillMaterialAuditDTO.setOwnerParCode("ownerParCode");
        goodwillMaterialAuditDTO.setOrgId(0);
        goodwillMaterialAuditDTO.setId(0L);

        // Configure GoodwillMaterialAuditMapper.selectListBySql(...).
        final GoodwillMaterialAuditPO goodwillMaterialAuditPO = new GoodwillMaterialAuditPO();
        goodwillMaterialAuditPO.setAppId("appId");
        goodwillMaterialAuditPO.setOwnerCode("ownerCode");
        goodwillMaterialAuditPO.setOwnerParCode("ownerParCode");
        goodwillMaterialAuditPO.setOrgId(0);
        goodwillMaterialAuditPO.setId(0L);
        final List<GoodwillMaterialAuditPO> goodwillMaterialAuditPOS = Arrays.asList(goodwillMaterialAuditPO);
        when(mockGoodwillMaterialAuditMapper.selectListBySql(any(GoodwillMaterialAuditPO.class)))
                .thenReturn(goodwillMaterialAuditPOS);

        // Run the test
        final List<GoodwillMaterialAuditDTO> result = goodwillMaterialAuditServiceImplUnderTest.selectListBySql(
                goodwillMaterialAuditDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillMaterialAuditMapperReturnsNoItems() {
        // Setup
        final GoodwillMaterialAuditDTO goodwillMaterialAuditDTO = new GoodwillMaterialAuditDTO();
        goodwillMaterialAuditDTO.setAppId("appId");
        goodwillMaterialAuditDTO.setOwnerCode("ownerCode");
        goodwillMaterialAuditDTO.setOwnerParCode("ownerParCode");
        goodwillMaterialAuditDTO.setOrgId(0);
        goodwillMaterialAuditDTO.setId(0L);

        when(mockGoodwillMaterialAuditMapper.selectListBySql(any(GoodwillMaterialAuditPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillMaterialAuditDTO> result = goodwillMaterialAuditServiceImplUnderTest.selectListBySql(
                goodwillMaterialAuditDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure GoodwillMaterialAuditMapper.selectById(...).
        final GoodwillMaterialAuditPO goodwillMaterialAuditPO = new GoodwillMaterialAuditPO();
        goodwillMaterialAuditPO.setAppId("appId");
        goodwillMaterialAuditPO.setOwnerCode("ownerCode");
        goodwillMaterialAuditPO.setOwnerParCode("ownerParCode");
        goodwillMaterialAuditPO.setOrgId(0);
        goodwillMaterialAuditPO.setId(0L);
        when(mockGoodwillMaterialAuditMapper.selectById(0L)).thenReturn(goodwillMaterialAuditPO);

        // Run the test
        final GoodwillMaterialAuditDTO result = goodwillMaterialAuditServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillMaterialAuditMapperReturnsNull() throws Exception {
        // Setup
        when(mockGoodwillMaterialAuditMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillMaterialAuditServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final GoodwillMaterialAuditDTO goodwillMaterialAuditDTO = new GoodwillMaterialAuditDTO();
        goodwillMaterialAuditDTO.setAppId("appId");
        goodwillMaterialAuditDTO.setOwnerCode("ownerCode");
        goodwillMaterialAuditDTO.setOwnerParCode("ownerParCode");
        goodwillMaterialAuditDTO.setOrgId(0);
        goodwillMaterialAuditDTO.setId(0L);

        when(mockGoodwillMaterialAuditMapper.insert(any(GoodwillMaterialAuditPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillMaterialAuditServiceImplUnderTest.insert(goodwillMaterialAuditDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final GoodwillMaterialAuditDTO goodwillMaterialAuditDTO = new GoodwillMaterialAuditDTO();
        goodwillMaterialAuditDTO.setAppId("appId");
        goodwillMaterialAuditDTO.setOwnerCode("ownerCode");
        goodwillMaterialAuditDTO.setOwnerParCode("ownerParCode");
        goodwillMaterialAuditDTO.setOrgId(0);
        goodwillMaterialAuditDTO.setId(0L);

        // Configure GoodwillMaterialAuditMapper.selectById(...).
        final GoodwillMaterialAuditPO goodwillMaterialAuditPO = new GoodwillMaterialAuditPO();
        goodwillMaterialAuditPO.setAppId("appId");
        goodwillMaterialAuditPO.setOwnerCode("ownerCode");
        goodwillMaterialAuditPO.setOwnerParCode("ownerParCode");
        goodwillMaterialAuditPO.setOrgId(0);
        goodwillMaterialAuditPO.setId(0L);
        when(mockGoodwillMaterialAuditMapper.selectById(0L)).thenReturn(goodwillMaterialAuditPO);

        when(mockGoodwillMaterialAuditMapper.updateById(any(GoodwillMaterialAuditPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillMaterialAuditServiceImplUnderTest.update(0L, goodwillMaterialAuditDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
