package com.yonyou.dmscus.customer.service.impl.inviteSaAllocateRule;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.inviteSaAllocateRule.InviteSaAllocateRuleDetailMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteSaAllocateRule.InviteSaAllocateRuleDetailDTO;
import com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule.InviteSaAllocateRuleDetailPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteSaAllocateRuleDetailServiceImplTest {

    @Mock
    private InviteSaAllocateRuleDetailMapper mockInviteSaAllocateRuleDetailMapper;

    private InviteSaAllocateRuleDetailServiceImpl inviteSaAllocateRuleDetailServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteSaAllocateRuleDetailServiceImplUnderTest = new InviteSaAllocateRuleDetailServiceImpl();
        inviteSaAllocateRuleDetailServiceImplUnderTest.inviteSaAllocateRuleDetailMapper = mockInviteSaAllocateRuleDetailMapper;
    }

    @Test
    void testGetById_InviteSaAllocateRuleDetailMapperReturnsNull() {
        // Setup
        when(mockInviteSaAllocateRuleDetailMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inviteSaAllocateRuleDetailServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }
}
