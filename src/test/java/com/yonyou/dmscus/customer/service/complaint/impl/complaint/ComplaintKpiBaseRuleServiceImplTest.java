package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintKpiBaseRuleMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiBaseRuleDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiSetDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintKpiBaseRulePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintKpiBaseRuleServiceImplTest {

    @Mock
    private ComplaintKpiBaseRuleMapper mockComplaintKpiBaseRuleMapper;

    private ComplaintKpiBaseRuleServiceImpl complaintKpiBaseRuleServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        complaintKpiBaseRuleServiceImplUnderTest = new ComplaintKpiBaseRuleServiceImpl();
        complaintKpiBaseRuleServiceImplUnderTest.complaintKpiBaseRuleMapper = mockComplaintKpiBaseRuleMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO = new ComplaintKpiBaseRuleDTO();
        complaintKpiBaseRuleDTO.setCreatedBy("createdBy");
        complaintKpiBaseRuleDTO.setKpi(0);
        complaintKpiBaseRuleDTO.setIndexValue("indexValue");
        complaintKpiBaseRuleDTO.setScore(0);
        complaintKpiBaseRuleDTO.setIsValid(0);

        // Configure ComplaintKpiBaseRuleMapper.selectPageBySql(...).
        final ComplaintKpiBaseRulePO complaintKpiBaseRulePO = new ComplaintKpiBaseRulePO();
        complaintKpiBaseRulePO.setOwnerCode("ownerCode");
        complaintKpiBaseRulePO.setOrgId(0);
        complaintKpiBaseRulePO.setId(0L);
        complaintKpiBaseRulePO.setKpiName("kpiName");
        complaintKpiBaseRulePO.setKpi(0);
        final List<ComplaintKpiBaseRulePO> complaintKpiBaseRulePOS = Arrays.asList(complaintKpiBaseRulePO);
        when(mockComplaintKpiBaseRuleMapper.selectPageBySql(any(Page.class),
                any(ComplaintKpiBaseRulePO.class))).thenReturn(complaintKpiBaseRulePOS);

        // Run the test
        final IPage<ComplaintKpiBaseRuleDTO> result = complaintKpiBaseRuleServiceImplUnderTest.selectPageBysql(page,
                complaintKpiBaseRuleDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintKpiBaseRuleMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO = new ComplaintKpiBaseRuleDTO();
        complaintKpiBaseRuleDTO.setCreatedBy("createdBy");
        complaintKpiBaseRuleDTO.setKpi(0);
        complaintKpiBaseRuleDTO.setIndexValue("indexValue");
        complaintKpiBaseRuleDTO.setScore(0);
        complaintKpiBaseRuleDTO.setIsValid(0);

        when(mockComplaintKpiBaseRuleMapper.selectPageBySql(any(Page.class),
                any(ComplaintKpiBaseRulePO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintKpiBaseRuleDTO> result = complaintKpiBaseRuleServiceImplUnderTest.selectPageBysql(page,
                complaintKpiBaseRuleDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO = new ComplaintKpiBaseRuleDTO();
        complaintKpiBaseRuleDTO.setCreatedBy("createdBy");
        complaintKpiBaseRuleDTO.setKpi(0);
        complaintKpiBaseRuleDTO.setIndexValue("indexValue");
        complaintKpiBaseRuleDTO.setScore(0);
        complaintKpiBaseRuleDTO.setIsValid(0);

        // Configure ComplaintKpiBaseRuleMapper.selectListBySql(...).
        final ComplaintKpiBaseRulePO complaintKpiBaseRulePO = new ComplaintKpiBaseRulePO();
        complaintKpiBaseRulePO.setOwnerCode("ownerCode");
        complaintKpiBaseRulePO.setOrgId(0);
        complaintKpiBaseRulePO.setId(0L);
        complaintKpiBaseRulePO.setKpiName("kpiName");
        complaintKpiBaseRulePO.setKpi(0);
        final List<ComplaintKpiBaseRulePO> complaintKpiBaseRulePOS = Arrays.asList(complaintKpiBaseRulePO);
        when(mockComplaintKpiBaseRuleMapper.selectListBySql(any(ComplaintKpiBaseRulePO.class)))
                .thenReturn(complaintKpiBaseRulePOS);

        // Run the test
        final List<ComplaintKpiBaseRuleDTO> result = complaintKpiBaseRuleServiceImplUnderTest.selectListBySql(
                complaintKpiBaseRuleDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintKpiBaseRuleMapperReturnsNoItems() {
        // Setup
        final ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO = new ComplaintKpiBaseRuleDTO();
        complaintKpiBaseRuleDTO.setCreatedBy("createdBy");
        complaintKpiBaseRuleDTO.setKpi(0);
        complaintKpiBaseRuleDTO.setIndexValue("indexValue");
        complaintKpiBaseRuleDTO.setScore(0);
        complaintKpiBaseRuleDTO.setIsValid(0);

        when(mockComplaintKpiBaseRuleMapper.selectListBySql(any(ComplaintKpiBaseRulePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintKpiBaseRuleDTO> result = complaintKpiBaseRuleServiceImplUnderTest.selectListBySql(
                complaintKpiBaseRuleDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure ComplaintKpiBaseRuleMapper.selectById(...).
        final ComplaintKpiBaseRulePO complaintKpiBaseRulePO = new ComplaintKpiBaseRulePO();
        complaintKpiBaseRulePO.setOwnerCode("ownerCode");
        complaintKpiBaseRulePO.setOrgId(0);
        complaintKpiBaseRulePO.setId(0L);
        complaintKpiBaseRulePO.setKpiName("kpiName");
        complaintKpiBaseRulePO.setKpi(0);
        when(mockComplaintKpiBaseRuleMapper.selectById(0L)).thenReturn(complaintKpiBaseRulePO);

        // Run the test
        final ComplaintKpiBaseRuleDTO result = complaintKpiBaseRuleServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintKpiBaseRuleMapperReturnsNull() {
        // Setup
        when(mockComplaintKpiBaseRuleMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintKpiBaseRuleServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO = new ComplaintKpiBaseRuleDTO();
        complaintKpiBaseRuleDTO.setCreatedBy("createdBy");
        complaintKpiBaseRuleDTO.setKpi(0);
        complaintKpiBaseRuleDTO.setIndexValue("indexValue");
        complaintKpiBaseRuleDTO.setScore(0);
        complaintKpiBaseRuleDTO.setIsValid(0);

        when(mockComplaintKpiBaseRuleMapper.insert(any(ComplaintKpiBaseRulePO.class))).thenReturn(0);

        // Run the test
        final int result = complaintKpiBaseRuleServiceImplUnderTest.insert(complaintKpiBaseRuleDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final ComplaintKpiBaseRuleDTO complaintKpiBaseRuleDTO = new ComplaintKpiBaseRuleDTO();
        complaintKpiBaseRuleDTO.setCreatedBy("createdBy");
        complaintKpiBaseRuleDTO.setKpi(0);
        complaintKpiBaseRuleDTO.setIndexValue("indexValue");
        complaintKpiBaseRuleDTO.setScore(0);
        complaintKpiBaseRuleDTO.setIsValid(0);

        // Configure ComplaintKpiBaseRuleMapper.selectById(...).
        final ComplaintKpiBaseRulePO complaintKpiBaseRulePO = new ComplaintKpiBaseRulePO();
        complaintKpiBaseRulePO.setOwnerCode("ownerCode");
        complaintKpiBaseRulePO.setOrgId(0);
        complaintKpiBaseRulePO.setId(0L);
        complaintKpiBaseRulePO.setKpiName("kpiName");
        complaintKpiBaseRulePO.setKpi(0);
        when(mockComplaintKpiBaseRuleMapper.selectById(0L)).thenReturn(complaintKpiBaseRulePO);

        when(mockComplaintKpiBaseRuleMapper.updateById(any(ComplaintKpiBaseRulePO.class))).thenReturn(0);

        // Run the test
        final int result = complaintKpiBaseRuleServiceImplUnderTest.update(0L, complaintKpiBaseRuleDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
