package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillAuditProcessMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditProcessDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAuditProcessPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillAuditProcessServiceImplTest {

    @Mock
    private GoodwillAuditProcessMapper mockGoodwillAuditProcessMapper;

    private GoodwillAuditProcessServiceImpl goodwillAuditProcessServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        goodwillAuditProcessServiceImplUnderTest = new GoodwillAuditProcessServiceImpl();
        goodwillAuditProcessServiceImplUnderTest.goodwillAuditProcessMapper = mockGoodwillAuditProcessMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillAuditProcessDTO goodwillAuditProcessDTO = new GoodwillAuditProcessDTO();
        goodwillAuditProcessDTO.setId(0L);
        goodwillAuditProcessDTO.setAuditObject(0);
        goodwillAuditProcessDTO.setAuditType(0);
        goodwillAuditProcessDTO.setAuditPosition("auditPosition");
        goodwillAuditProcessDTO.setMinValueRelation(0);
        goodwillAuditProcessDTO.setMinAuditPrice(new BigDecimal("0.00"));
        goodwillAuditProcessDTO.setMaxAuditPrice(new BigDecimal("0.00"));

        // Configure GoodwillAuditProcessMapper.selectPageBySql(...).
        final GoodwillAuditProcessPO goodwillAuditProcessPO = new GoodwillAuditProcessPO();
        goodwillAuditProcessPO.setIsPublic(0);
        goodwillAuditProcessPO.setAppId("appId");
        goodwillAuditProcessPO.setOwnerCode("ownerCode");
        goodwillAuditProcessPO.setOwnerParCode("ownerParCode");
        goodwillAuditProcessPO.setAuditPosition("auditPosition");
        final List<GoodwillAuditProcessPO> goodwillAuditProcessPOS = Arrays.asList(goodwillAuditProcessPO);
        when(mockGoodwillAuditProcessMapper.selectPageBySql(any(Page.class),
                any(GoodwillAuditProcessPO.class))).thenReturn(goodwillAuditProcessPOS);

        // Run the test
        final IPage<GoodwillAuditProcessDTO> result = goodwillAuditProcessServiceImplUnderTest.selectPageBysql(page,
                goodwillAuditProcessDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillAuditProcessMapperReturnsNoItems() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillAuditProcessDTO goodwillAuditProcessDTO = new GoodwillAuditProcessDTO();
        goodwillAuditProcessDTO.setId(0L);
        goodwillAuditProcessDTO.setAuditObject(0);
        goodwillAuditProcessDTO.setAuditType(0);
        goodwillAuditProcessDTO.setAuditPosition("auditPosition");
        goodwillAuditProcessDTO.setMinValueRelation(0);
        goodwillAuditProcessDTO.setMinAuditPrice(new BigDecimal("0.00"));
        goodwillAuditProcessDTO.setMaxAuditPrice(new BigDecimal("0.00"));

        when(mockGoodwillAuditProcessMapper.selectPageBySql(any(Page.class),
                any(GoodwillAuditProcessPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillAuditProcessDTO> result = goodwillAuditProcessServiceImplUnderTest.selectPageBysql(page,
                goodwillAuditProcessDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final GoodwillAuditProcessDTO goodwillAuditProcessDTO = new GoodwillAuditProcessDTO();
        goodwillAuditProcessDTO.setId(0L);
        goodwillAuditProcessDTO.setAuditObject(0);
        goodwillAuditProcessDTO.setAuditType(0);
        goodwillAuditProcessDTO.setAuditPosition("auditPosition");
        goodwillAuditProcessDTO.setMinValueRelation(0);
        goodwillAuditProcessDTO.setMinAuditPrice(new BigDecimal("0.00"));
        goodwillAuditProcessDTO.setMaxAuditPrice(new BigDecimal("0.00"));

        // Configure GoodwillAuditProcessMapper.selectListBySql(...).
        final GoodwillAuditProcessPO goodwillAuditProcessPO = new GoodwillAuditProcessPO();
        goodwillAuditProcessPO.setIsPublic(0);
        goodwillAuditProcessPO.setAppId("appId");
        goodwillAuditProcessPO.setOwnerCode("ownerCode");
        goodwillAuditProcessPO.setOwnerParCode("ownerParCode");
        goodwillAuditProcessPO.setAuditPosition("auditPosition");
        final List<GoodwillAuditProcessPO> goodwillAuditProcessPOS = Arrays.asList(goodwillAuditProcessPO);
        when(mockGoodwillAuditProcessMapper.selectListBySql(any(GoodwillAuditProcessPO.class)))
                .thenReturn(goodwillAuditProcessPOS);

        // Run the test
        final List<GoodwillAuditProcessDTO> result = goodwillAuditProcessServiceImplUnderTest.selectListBySql(
                goodwillAuditProcessDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillAuditProcessMapperReturnsNoItems() throws Exception {
        // Setup
        final GoodwillAuditProcessDTO goodwillAuditProcessDTO = new GoodwillAuditProcessDTO();
        goodwillAuditProcessDTO.setId(0L);
        goodwillAuditProcessDTO.setAuditObject(0);
        goodwillAuditProcessDTO.setAuditType(0);
        goodwillAuditProcessDTO.setAuditPosition("auditPosition");
        goodwillAuditProcessDTO.setMinValueRelation(0);
        goodwillAuditProcessDTO.setMinAuditPrice(new BigDecimal("0.00"));
        goodwillAuditProcessDTO.setMaxAuditPrice(new BigDecimal("0.00"));

        when(mockGoodwillAuditProcessMapper.selectListBySql(any(GoodwillAuditProcessPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillAuditProcessDTO> result = goodwillAuditProcessServiceImplUnderTest.selectListBySql(
                goodwillAuditProcessDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectAuditProcessInfo() {
        // Setup
        final GoodwillAuditProcessDTO goodwillAuditProcessDTO = new GoodwillAuditProcessDTO();
        goodwillAuditProcessDTO.setId(0L);
        goodwillAuditProcessDTO.setAuditObject(0);
        goodwillAuditProcessDTO.setAuditType(0);
        goodwillAuditProcessDTO.setAuditPosition("auditPosition");
        goodwillAuditProcessDTO.setMinValueRelation(0);
        goodwillAuditProcessDTO.setMinAuditPrice(new BigDecimal("0.00"));
        goodwillAuditProcessDTO.setMaxAuditPrice(new BigDecimal("0.00"));

        when(mockGoodwillAuditProcessMapper.selectAuditProcessInfo(any(GoodwillAuditProcessPO.class)))
                .thenReturn(Arrays.asList(new HashMap<>()));

        // Run the test
        final List<Map> result = goodwillAuditProcessServiceImplUnderTest.selectAuditProcessInfo(
                goodwillAuditProcessDTO);

        // Verify the results
    }

    @Test
    void testSelectAuditProcessInfo_GoodwillAuditProcessMapperReturnsNoItems() {
        // Setup
        final GoodwillAuditProcessDTO goodwillAuditProcessDTO = new GoodwillAuditProcessDTO();
        goodwillAuditProcessDTO.setId(0L);
        goodwillAuditProcessDTO.setAuditObject(0);
        goodwillAuditProcessDTO.setAuditType(0);
        goodwillAuditProcessDTO.setAuditPosition("auditPosition");
        goodwillAuditProcessDTO.setMinValueRelation(0);
        goodwillAuditProcessDTO.setMinAuditPrice(new BigDecimal("0.00"));
        goodwillAuditProcessDTO.setMaxAuditPrice(new BigDecimal("0.00"));

        when(mockGoodwillAuditProcessMapper.selectAuditProcessInfo(any(GoodwillAuditProcessPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<Map> result = goodwillAuditProcessServiceImplUnderTest.selectAuditProcessInfo(
                goodwillAuditProcessDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure GoodwillAuditProcessMapper.selectById(...).
        final GoodwillAuditProcessPO goodwillAuditProcessPO = new GoodwillAuditProcessPO();
        goodwillAuditProcessPO.setIsPublic(0);
        goodwillAuditProcessPO.setAppId("appId");
        goodwillAuditProcessPO.setOwnerCode("ownerCode");
        goodwillAuditProcessPO.setOwnerParCode("ownerParCode");
        goodwillAuditProcessPO.setAuditPosition("auditPosition");
        when(mockGoodwillAuditProcessMapper.selectById(0L)).thenReturn(goodwillAuditProcessPO);

        // Run the test
        final GoodwillAuditProcessDTO result = goodwillAuditProcessServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillAuditProcessMapperReturnsNull() {
        // Setup
        when(mockGoodwillAuditProcessMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillAuditProcessServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final GoodwillAuditProcessDTO goodwillAuditProcessDTO = new GoodwillAuditProcessDTO();
        goodwillAuditProcessDTO.setId(0L);
        goodwillAuditProcessDTO.setAuditObject(0);
        goodwillAuditProcessDTO.setAuditType(0);
        goodwillAuditProcessDTO.setAuditPosition("auditPosition");
        goodwillAuditProcessDTO.setMinValueRelation(0);
        goodwillAuditProcessDTO.setMinAuditPrice(new BigDecimal("0.00"));
        goodwillAuditProcessDTO.setMaxAuditPrice(new BigDecimal("0.00"));

        when(mockGoodwillAuditProcessMapper.insert(any(GoodwillAuditProcessPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillAuditProcessServiceImplUnderTest.insert(goodwillAuditProcessDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testInsertAuditProcessInfo() {
        // Setup
        final GoodwillAuditProcessDTO goodwillAuditProcessDTO1 = new GoodwillAuditProcessDTO();
        goodwillAuditProcessDTO1.setId(0L);
        goodwillAuditProcessDTO1.setAuditObject(0);
        goodwillAuditProcessDTO1.setAuditType(0);
        goodwillAuditProcessDTO1.setAuditPosition("auditPosition");
        goodwillAuditProcessDTO1.setMinValueRelation(0);
        goodwillAuditProcessDTO1.setMinAuditPrice(new BigDecimal("0.00"));
        goodwillAuditProcessDTO1.setMaxAuditPrice(new BigDecimal("0.00"));
        final List<GoodwillAuditProcessDTO> goodwillAuditProcessDTO = Arrays.asList(goodwillAuditProcessDTO1);

        // Configure GoodwillAuditProcessMapper.selectById(...).
        final GoodwillAuditProcessPO goodwillAuditProcessPO = new GoodwillAuditProcessPO();
        goodwillAuditProcessPO.setIsPublic(0);
        goodwillAuditProcessPO.setAppId("appId");
        goodwillAuditProcessPO.setOwnerCode("ownerCode");
        goodwillAuditProcessPO.setOwnerParCode("ownerParCode");
        goodwillAuditProcessPO.setAuditPosition("auditPosition");
        when(mockGoodwillAuditProcessMapper.selectById(0L)).thenReturn(goodwillAuditProcessPO);

        // Run the test
        final int result = goodwillAuditProcessServiceImplUnderTest.insertAuditProcessInfo(goodwillAuditProcessDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockGoodwillAuditProcessMapper).insert(any(GoodwillAuditProcessPO.class));
        verify(mockGoodwillAuditProcessMapper).updateById(any(GoodwillAuditProcessPO.class));
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final GoodwillAuditProcessDTO goodwillAuditProcessDTO = new GoodwillAuditProcessDTO();
        goodwillAuditProcessDTO.setId(0L);
        goodwillAuditProcessDTO.setAuditObject(0);
        goodwillAuditProcessDTO.setAuditType(0);
        goodwillAuditProcessDTO.setAuditPosition("auditPosition");
        goodwillAuditProcessDTO.setMinValueRelation(0);
        goodwillAuditProcessDTO.setMinAuditPrice(new BigDecimal("0.00"));
        goodwillAuditProcessDTO.setMaxAuditPrice(new BigDecimal("0.00"));

        // Configure GoodwillAuditProcessMapper.selectById(...).
        final GoodwillAuditProcessPO goodwillAuditProcessPO = new GoodwillAuditProcessPO();
        goodwillAuditProcessPO.setIsPublic(0);
        goodwillAuditProcessPO.setAppId("appId");
        goodwillAuditProcessPO.setOwnerCode("ownerCode");
        goodwillAuditProcessPO.setOwnerParCode("ownerParCode");
        goodwillAuditProcessPO.setAuditPosition("auditPosition");
        when(mockGoodwillAuditProcessMapper.selectById(0L)).thenReturn(goodwillAuditProcessPO);

        when(mockGoodwillAuditProcessMapper.updateById(any(GoodwillAuditProcessPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillAuditProcessServiceImplUnderTest.update(0L, goodwillAuditProcessDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectListBydealGoodwillFlowData() {
        // Setup
        // Configure GoodwillAuditProcessMapper.selectList(...).
        final GoodwillAuditProcessPO goodwillAuditProcessPO = new GoodwillAuditProcessPO();
        goodwillAuditProcessPO.setIsPublic(0);
        goodwillAuditProcessPO.setAppId("appId");
        goodwillAuditProcessPO.setOwnerCode("ownerCode");
        goodwillAuditProcessPO.setOwnerParCode("ownerParCode");
        goodwillAuditProcessPO.setAuditPosition("auditPosition");
        final List<GoodwillAuditProcessPO> goodwillAuditProcessPOS = Arrays.asList(goodwillAuditProcessPO);
        when(mockGoodwillAuditProcessMapper.selectList(0, 0)).thenReturn(goodwillAuditProcessPOS);

        // Run the test
        final List<GoodwillAuditProcessDTO> result = goodwillAuditProcessServiceImplUnderTest.selectListBydealGoodwillFlowData(
                0, 0, new BigDecimal("0.00"));

        // Verify the results
    }

    @Test
    void testSelectList() {
        // Setup
        // Configure GoodwillAuditProcessMapper.selectList(...).
        final GoodwillAuditProcessPO goodwillAuditProcessPO = new GoodwillAuditProcessPO();
        goodwillAuditProcessPO.setIsPublic(0);
        goodwillAuditProcessPO.setAppId("appId");
        goodwillAuditProcessPO.setOwnerCode("ownerCode");
        goodwillAuditProcessPO.setOwnerParCode("ownerParCode");
        goodwillAuditProcessPO.setAuditPosition("auditPosition");
        final List<GoodwillAuditProcessPO> goodwillAuditProcessPOS = Arrays.asList(goodwillAuditProcessPO);
        when(mockGoodwillAuditProcessMapper.selectList(0, 0)).thenReturn(goodwillAuditProcessPOS);

        // Run the test
        final List<GoodwillAuditProcessDTO> result = goodwillAuditProcessServiceImplUnderTest.selectList(0, 0,
                new BigDecimal("0.00"), 0L, 0L);

        // Verify the results
    }

    @Test
    void testSelectList_GoodwillAuditProcessMapperReturnsNoItems() {
        // Setup
        when(mockGoodwillAuditProcessMapper.selectList(0, 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillAuditProcessDTO> result = goodwillAuditProcessServiceImplUnderTest.selectList(0, 0,
                new BigDecimal("0.00"), 0L, 0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
