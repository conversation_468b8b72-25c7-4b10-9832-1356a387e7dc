package com.yonyou.dmscus.customer.service.impl.invitationKanban;

import com.yonyou.dmscus.customer.dao.invitationKanban.InvitationKanbanMapper;
import com.yonyou.dmscus.customer.entity.dto.invitationKanban.InvitationKanbanInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationKanban.InvitationKanbanQueryDTO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InvitationKanbanServiceImplTest {

    @Mock
    private InvitationKanbanMapper mockInvitationKanbanMapper;
    @Mock
    private BusinessPlatformService mockBusinessPlatformService;

    private InvitationKanbanServiceImpl invitationKanbanServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        invitationKanbanServiceImplUnderTest = new InvitationKanbanServiceImpl();
        invitationKanbanServiceImplUnderTest.invitationKanbanMapper = mockInvitationKanbanMapper;
        invitationKanbanServiceImplUnderTest.businessPlatformService = mockBusinessPlatformService;
    }

    @Test
    void testInvitationKanbanService_InvitationKanbanMapperGetInvitationKanbanInfoByDealerCodeReturnsNoItems() {
        // Setup
        final InvitationKanbanQueryDTO query = new InvitationKanbanQueryDTO();
        query.setLargeAreaId("largeAreaId");
        query.setAreaId("areaId");
        query.setMonthNo("monthNo");
        query.setDealerCode("dealerCode");
        query.setDealers(Arrays.asList("value"));

        // Configure InvitationKanbanMapper.getInvitationKanbanInfoByDealerCode(...).
        final InvitationKanbanQueryDTO query1 = new InvitationKanbanQueryDTO();
        query1.setLargeAreaId("largeAreaId");
        query1.setAreaId("areaId");
        query1.setMonthNo("monthNo");
        query1.setDealerCode("dealerCode");
        query1.setDealers(Arrays.asList("value"));
        when(mockInvitationKanbanMapper.getInvitationKanbanInfoByDealerCode(query1))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InvitationKanbanInfoDTO> result = invitationKanbanServiceImplUnderTest.InvitationKanbanService(
                query);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
