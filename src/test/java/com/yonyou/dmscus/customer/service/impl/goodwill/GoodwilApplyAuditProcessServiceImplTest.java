package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwilApplyAuditProcessMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwilApplyAuditProcessDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwilApplyAuditProcessPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwilApplyAuditProcessServiceImplTest {

    @Mock
    private GoodwilApplyAuditProcessMapper mockGoodwilApplyAuditProcessMapper;

    private GoodwilApplyAuditProcessServiceImpl goodwilApplyAuditProcessServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        goodwilApplyAuditProcessServiceImplUnderTest = new GoodwilApplyAuditProcessServiceImpl();
        goodwilApplyAuditProcessServiceImplUnderTest.goodwilApplyAuditProcessMapper = mockGoodwilApplyAuditProcessMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO = new GoodwilApplyAuditProcessDTO();
        goodwilApplyAuditProcessDTO.setAuditRole("auditRole");
        goodwilApplyAuditProcessDTO.setAppId("appId");
        goodwilApplyAuditProcessDTO.setOwnerCode("ownerCode");
        goodwilApplyAuditProcessDTO.setOwnerParCode("ownerParCode");
        goodwilApplyAuditProcessDTO.setOrgId(0);

        // Configure GoodwilApplyAuditProcessMapper.selectPageBySql(...).
        final GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPO = new GoodwilApplyAuditProcessPO();
        goodwilApplyAuditProcessPO.setApplyCounts(0);
        goodwilApplyAuditProcessPO.setCounts(0);
        goodwilApplyAuditProcessPO.setAuditRole("auditRole");
        goodwilApplyAuditProcessPO.setAppId("appId");
        goodwilApplyAuditProcessPO.setOwnerCode("ownerCode");
        final List<GoodwilApplyAuditProcessPO> goodwilApplyAuditProcessPOS = Arrays.asList(goodwilApplyAuditProcessPO);
        when(mockGoodwilApplyAuditProcessMapper.selectPageBySql(any(Page.class),
                any(GoodwilApplyAuditProcessPO.class))).thenReturn(goodwilApplyAuditProcessPOS);

        // Run the test
        final IPage<GoodwilApplyAuditProcessDTO> result = goodwilApplyAuditProcessServiceImplUnderTest.selectPageBysql(
                page, goodwilApplyAuditProcessDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwilApplyAuditProcessMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO = new GoodwilApplyAuditProcessDTO();
        goodwilApplyAuditProcessDTO.setAuditRole("auditRole");
        goodwilApplyAuditProcessDTO.setAppId("appId");
        goodwilApplyAuditProcessDTO.setOwnerCode("ownerCode");
        goodwilApplyAuditProcessDTO.setOwnerParCode("ownerParCode");
        goodwilApplyAuditProcessDTO.setOrgId(0);

        when(mockGoodwilApplyAuditProcessMapper.selectPageBySql(any(Page.class),
                any(GoodwilApplyAuditProcessPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwilApplyAuditProcessDTO> result = goodwilApplyAuditProcessServiceImplUnderTest.selectPageBysql(
                page, goodwilApplyAuditProcessDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO = new GoodwilApplyAuditProcessDTO();
        goodwilApplyAuditProcessDTO.setAuditRole("auditRole");
        goodwilApplyAuditProcessDTO.setAppId("appId");
        goodwilApplyAuditProcessDTO.setOwnerCode("ownerCode");
        goodwilApplyAuditProcessDTO.setOwnerParCode("ownerParCode");
        goodwilApplyAuditProcessDTO.setOrgId(0);

        // Configure GoodwilApplyAuditProcessMapper.selectListBySql(...).
        final GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPO = new GoodwilApplyAuditProcessPO();
        goodwilApplyAuditProcessPO.setApplyCounts(0);
        goodwilApplyAuditProcessPO.setCounts(0);
        goodwilApplyAuditProcessPO.setAuditRole("auditRole");
        goodwilApplyAuditProcessPO.setAppId("appId");
        goodwilApplyAuditProcessPO.setOwnerCode("ownerCode");
        final List<GoodwilApplyAuditProcessPO> goodwilApplyAuditProcessPOS = Arrays.asList(goodwilApplyAuditProcessPO);
        when(mockGoodwilApplyAuditProcessMapper.selectListBySql(any(GoodwilApplyAuditProcessPO.class)))
                .thenReturn(goodwilApplyAuditProcessPOS);

        // Run the test
        final List<GoodwilApplyAuditProcessDTO> result = goodwilApplyAuditProcessServiceImplUnderTest.selectListBySql(
                goodwilApplyAuditProcessDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwilApplyAuditProcessMapperReturnsNoItems() {
        // Setup
        final GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO = new GoodwilApplyAuditProcessDTO();
        goodwilApplyAuditProcessDTO.setAuditRole("auditRole");
        goodwilApplyAuditProcessDTO.setAppId("appId");
        goodwilApplyAuditProcessDTO.setOwnerCode("ownerCode");
        goodwilApplyAuditProcessDTO.setOwnerParCode("ownerParCode");
        goodwilApplyAuditProcessDTO.setOrgId(0);

        when(mockGoodwilApplyAuditProcessMapper.selectListBySql(any(GoodwilApplyAuditProcessPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwilApplyAuditProcessDTO> result = goodwilApplyAuditProcessServiceImplUnderTest.selectListBySql(
                goodwilApplyAuditProcessDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure GoodwilApplyAuditProcessMapper.selectById(...).
        final GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPO = new GoodwilApplyAuditProcessPO();
        goodwilApplyAuditProcessPO.setApplyCounts(0);
        goodwilApplyAuditProcessPO.setCounts(0);
        goodwilApplyAuditProcessPO.setAuditRole("auditRole");
        goodwilApplyAuditProcessPO.setAppId("appId");
        goodwilApplyAuditProcessPO.setOwnerCode("ownerCode");
        when(mockGoodwilApplyAuditProcessMapper.selectById(0L)).thenReturn(goodwilApplyAuditProcessPO);

        // Run the test
        final GoodwilApplyAuditProcessDTO result = goodwilApplyAuditProcessServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwilApplyAuditProcessMapperReturnsNull() {
        // Setup
        when(mockGoodwilApplyAuditProcessMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwilApplyAuditProcessServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO = new GoodwilApplyAuditProcessDTO();
        goodwilApplyAuditProcessDTO.setAuditRole("auditRole");
        goodwilApplyAuditProcessDTO.setAppId("appId");
        goodwilApplyAuditProcessDTO.setOwnerCode("ownerCode");
        goodwilApplyAuditProcessDTO.setOwnerParCode("ownerParCode");
        goodwilApplyAuditProcessDTO.setOrgId(0);

        when(mockGoodwilApplyAuditProcessMapper.insert(any(GoodwilApplyAuditProcessPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwilApplyAuditProcessServiceImplUnderTest.insert(goodwilApplyAuditProcessDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final GoodwilApplyAuditProcessDTO goodwilApplyAuditProcessDTO = new GoodwilApplyAuditProcessDTO();
        goodwilApplyAuditProcessDTO.setAuditRole("auditRole");
        goodwilApplyAuditProcessDTO.setAppId("appId");
        goodwilApplyAuditProcessDTO.setOwnerCode("ownerCode");
        goodwilApplyAuditProcessDTO.setOwnerParCode("ownerParCode");
        goodwilApplyAuditProcessDTO.setOrgId(0);

        // Configure GoodwilApplyAuditProcessMapper.selectById(...).
        final GoodwilApplyAuditProcessPO goodwilApplyAuditProcessPO = new GoodwilApplyAuditProcessPO();
        goodwilApplyAuditProcessPO.setApplyCounts(0);
        goodwilApplyAuditProcessPO.setCounts(0);
        goodwilApplyAuditProcessPO.setAuditRole("auditRole");
        goodwilApplyAuditProcessPO.setAppId("appId");
        goodwilApplyAuditProcessPO.setOwnerCode("ownerCode");
        when(mockGoodwilApplyAuditProcessMapper.selectById(0L)).thenReturn(goodwilApplyAuditProcessPO);

        when(mockGoodwilApplyAuditProcessMapper.updateById(any(GoodwilApplyAuditProcessPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwilApplyAuditProcessServiceImplUnderTest.update(0L, goodwilApplyAuditProcessDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testQueryAuditProcess() {
        // Setup
        when(mockGoodwilApplyAuditProcessMapper.queryAuditProcess(0, 0L)).thenReturn(Arrays.asList(new HashMap<>()));

        // Run the test
        final List<Map> result = goodwilApplyAuditProcessServiceImplUnderTest.queryAuditProcess(0, 0L);

        // Verify the results
    }

    @Test
    void testQueryAuditProcess_GoodwilApplyAuditProcessMapperReturnsNoItems() {
        // Setup
        when(mockGoodwilApplyAuditProcessMapper.queryAuditProcess(0, 0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<Map> result = goodwilApplyAuditProcessServiceImplUnderTest.queryAuditProcess(0, 0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
