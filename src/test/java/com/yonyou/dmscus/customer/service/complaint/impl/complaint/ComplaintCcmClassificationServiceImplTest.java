package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintCcmClassificationMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCcmClassificationDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCcmClassificationPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintCcmClassificationServiceImplTest {

    @Mock
    private ComplaintCcmClassificationMapper mockComplaintCcmClassificationMapper;

    private ComplaintCcmClassificationServiceImpl complaintCcmClassificationServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        complaintCcmClassificationServiceImplUnderTest = new ComplaintCcmClassificationServiceImpl();
        complaintCcmClassificationServiceImplUnderTest.complaintCcmClassificationMapper = mockComplaintCcmClassificationMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintCcmClassificationDTO complaintCcmClassificationDTO = new ComplaintCcmClassificationDTO();
        complaintCcmClassificationDTO.setAppId("appId");
        complaintCcmClassificationDTO.setOwnerCode("ownerCode");
        complaintCcmClassificationDTO.setOwnerParCode("ownerParCode");
        complaintCcmClassificationDTO.setOrgId(0);
        complaintCcmClassificationDTO.setId(0L);

        // Configure ComplaintCcmClassificationMapper.selectPageBySql(...).
        final ComplaintCcmClassificationPO complaintCcmClassificationPO = new ComplaintCcmClassificationPO();
        complaintCcmClassificationPO.setAppId("appId");
        complaintCcmClassificationPO.setOwnerCode("ownerCode");
        complaintCcmClassificationPO.setOwnerParCode("ownerParCode");
        complaintCcmClassificationPO.setOrgId(0);
        complaintCcmClassificationPO.setId(0L);
        final List<ComplaintCcmClassificationPO> complaintCcmClassificationPOS = Arrays.asList(
                complaintCcmClassificationPO);
        when(mockComplaintCcmClassificationMapper.selectPageBySql(any(Page.class),
                any(ComplaintCcmClassificationPO.class))).thenReturn(complaintCcmClassificationPOS);

        // Run the test
        final IPage<ComplaintCcmClassificationDTO> result = complaintCcmClassificationServiceImplUnderTest.selectPageBysql(
                page, complaintCcmClassificationDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintCcmClassificationMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintCcmClassificationDTO complaintCcmClassificationDTO = new ComplaintCcmClassificationDTO();
        complaintCcmClassificationDTO.setAppId("appId");
        complaintCcmClassificationDTO.setOwnerCode("ownerCode");
        complaintCcmClassificationDTO.setOwnerParCode("ownerParCode");
        complaintCcmClassificationDTO.setOrgId(0);
        complaintCcmClassificationDTO.setId(0L);

        when(mockComplaintCcmClassificationMapper.selectPageBySql(any(Page.class),
                any(ComplaintCcmClassificationPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintCcmClassificationDTO> result = complaintCcmClassificationServiceImplUnderTest.selectPageBysql(
                page, complaintCcmClassificationDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final ComplaintCcmClassificationDTO complaintCcmClassificationDTO = new ComplaintCcmClassificationDTO();
        complaintCcmClassificationDTO.setAppId("appId");
        complaintCcmClassificationDTO.setOwnerCode("ownerCode");
        complaintCcmClassificationDTO.setOwnerParCode("ownerParCode");
        complaintCcmClassificationDTO.setOrgId(0);
        complaintCcmClassificationDTO.setId(0L);

        // Configure ComplaintCcmClassificationMapper.selectListBySql(...).
        final ComplaintCcmClassificationPO complaintCcmClassificationPO = new ComplaintCcmClassificationPO();
        complaintCcmClassificationPO.setAppId("appId");
        complaintCcmClassificationPO.setOwnerCode("ownerCode");
        complaintCcmClassificationPO.setOwnerParCode("ownerParCode");
        complaintCcmClassificationPO.setOrgId(0);
        complaintCcmClassificationPO.setId(0L);
        final List<ComplaintCcmClassificationPO> complaintCcmClassificationPOS = Arrays.asList(
                complaintCcmClassificationPO);
        when(mockComplaintCcmClassificationMapper.selectListBySql(any(ComplaintCcmClassificationPO.class)))
                .thenReturn(complaintCcmClassificationPOS);

        // Run the test
        final List<ComplaintCcmClassificationDTO> result = complaintCcmClassificationServiceImplUnderTest.selectListBySql(
                complaintCcmClassificationDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintCcmClassificationMapperReturnsNoItems() {
        // Setup
        final ComplaintCcmClassificationDTO complaintCcmClassificationDTO = new ComplaintCcmClassificationDTO();
        complaintCcmClassificationDTO.setAppId("appId");
        complaintCcmClassificationDTO.setOwnerCode("ownerCode");
        complaintCcmClassificationDTO.setOwnerParCode("ownerParCode");
        complaintCcmClassificationDTO.setOrgId(0);
        complaintCcmClassificationDTO.setId(0L);

        when(mockComplaintCcmClassificationMapper.selectListBySql(any(ComplaintCcmClassificationPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintCcmClassificationDTO> result = complaintCcmClassificationServiceImplUnderTest.selectListBySql(
                complaintCcmClassificationDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure ComplaintCcmClassificationMapper.selectById(...).
        final ComplaintCcmClassificationPO complaintCcmClassificationPO = new ComplaintCcmClassificationPO();
        complaintCcmClassificationPO.setAppId("appId");
        complaintCcmClassificationPO.setOwnerCode("ownerCode");
        complaintCcmClassificationPO.setOwnerParCode("ownerParCode");
        complaintCcmClassificationPO.setOrgId(0);
        complaintCcmClassificationPO.setId(0L);
        when(mockComplaintCcmClassificationMapper.selectById(0L)).thenReturn(complaintCcmClassificationPO);

        // Run the test
        final ComplaintCcmClassificationDTO result = complaintCcmClassificationServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintCcmClassificationMapperReturnsNull() {
        // Setup
        when(mockComplaintCcmClassificationMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintCcmClassificationServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final ComplaintCcmClassificationDTO complaintCcmClassificationDTO = new ComplaintCcmClassificationDTO();
        complaintCcmClassificationDTO.setAppId("appId");
        complaintCcmClassificationDTO.setOwnerCode("ownerCode");
        complaintCcmClassificationDTO.setOwnerParCode("ownerParCode");
        complaintCcmClassificationDTO.setOrgId(0);
        complaintCcmClassificationDTO.setId(0L);

        when(mockComplaintCcmClassificationMapper.insert(any(ComplaintCcmClassificationPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintCcmClassificationServiceImplUnderTest.insert(complaintCcmClassificationDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final ComplaintCcmClassificationDTO complaintCcmClassificationDTO = new ComplaintCcmClassificationDTO();
        complaintCcmClassificationDTO.setAppId("appId");
        complaintCcmClassificationDTO.setOwnerCode("ownerCode");
        complaintCcmClassificationDTO.setOwnerParCode("ownerParCode");
        complaintCcmClassificationDTO.setOrgId(0);
        complaintCcmClassificationDTO.setId(0L);

        // Configure ComplaintCcmClassificationMapper.selectById(...).
        final ComplaintCcmClassificationPO complaintCcmClassificationPO = new ComplaintCcmClassificationPO();
        complaintCcmClassificationPO.setAppId("appId");
        complaintCcmClassificationPO.setOwnerCode("ownerCode");
        complaintCcmClassificationPO.setOwnerParCode("ownerParCode");
        complaintCcmClassificationPO.setOrgId(0);
        complaintCcmClassificationPO.setId(0L);
        when(mockComplaintCcmClassificationMapper.selectById(0L)).thenReturn(complaintCcmClassificationPO);

        when(mockComplaintCcmClassificationMapper.updateById(any(ComplaintCcmClassificationPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintCcmClassificationServiceImplUnderTest.update(0L, complaintCcmClassificationDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
