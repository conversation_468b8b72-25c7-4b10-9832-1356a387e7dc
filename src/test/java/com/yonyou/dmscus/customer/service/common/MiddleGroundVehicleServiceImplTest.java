package com.yonyou.dmscus.customer.service.common;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.middleInterface.OwnerVehicleVO;
import com.yonyou.dmscus.customer.middleInterface.ResponseDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MiddleGroundVehicleServiceImplTest {

    @Mock
    private RestTemplate mockDirectRestTemplate;
    @Mock
    private MidUrlProperties mockMidUrlProperties;

    @InjectMocks
    private MiddleGroundVehicleServiceImpl middleGroundVehicleServiceImplUnderTest;

    @Test
    void testAddMidVehicle() {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        when(mockMidUrlProperties.getMidEndVehicleCenter()).thenReturn("result");
        when(mockMidUrlProperties.getVehicle()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("returnCode");
        responseDTO.setReturnMessage("message");
        responseDTO.setData(null);
        responseDTO.setCause("cause");
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        when(mockDirectRestTemplate.exchange("url", HttpMethod.GET,
                new HttpEntity<>(new HashMap<>(), new HttpHeaders()), ResponseDTO.class))
                .thenReturn(responseDTOResponseEntity);

        // Run the test
        final ResponseDTO result = middleGroundVehicleServiceImplUnderTest.addMidVehicle(paramMap);

        // Verify the results
    }

    @Test
    void testAddMidVehicle_RestTemplateThrowsRestClientException() {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        when(mockMidUrlProperties.getMidEndVehicleCenter()).thenReturn("result");
        when(mockMidUrlProperties.getVehicle()).thenReturn("result");
        when(mockDirectRestTemplate.exchange("url", HttpMethod.GET,
                new HttpEntity<>(new HashMap<>(), new HttpHeaders()), ResponseDTO.class))
                .thenThrow(RestClientException.class);

        // Run the test
        final ResponseDTO result = middleGroundVehicleServiceImplUnderTest.addMidVehicle(paramMap);

        // Verify the results
    }

    @Test
    void testUpdateMidVehicle() {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        when(mockMidUrlProperties.getMidEndVehicleCenter()).thenReturn("result");
        when(mockMidUrlProperties.getVehicle()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("returnCode");
        responseDTO.setReturnMessage("message");
        responseDTO.setData(null);
        responseDTO.setCause("cause");
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        when(mockDirectRestTemplate.exchange("url", HttpMethod.GET,
                new HttpEntity<>(new HashMap<>(), new HttpHeaders()), ResponseDTO.class))
                .thenReturn(responseDTOResponseEntity);

        // Run the test
        final ResponseDTO result = middleGroundVehicleServiceImplUnderTest.updateMidVehicle(paramMap);

        // Verify the results
    }

    @Test
    void testUpdateMidVehicle_RestTemplateThrowsRestClientException() {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        when(mockMidUrlProperties.getMidEndVehicleCenter()).thenReturn("result");
        when(mockMidUrlProperties.getVehicle()).thenReturn("result");
        when(mockDirectRestTemplate.exchange("url", HttpMethod.GET,
                new HttpEntity<>(new HashMap<>(), new HttpHeaders()), ResponseDTO.class))
                .thenThrow(RestClientException.class);

        // Run the test
        final ResponseDTO result = middleGroundVehicleServiceImplUnderTest.updateMidVehicle(paramMap);

        // Verify the results
    }

    @Test
    void testGetVinListCheckInfo() {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        when(mockMidUrlProperties.getMidEndVehicleCenter()).thenReturn("result");
        when(mockMidUrlProperties.getVinList()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("returnCode");
        responseDTO.setReturnMessage("message");
        responseDTO.setData(null);
        responseDTO.setCause("cause");
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        when(mockDirectRestTemplate.exchange("url", HttpMethod.GET,
                new HttpEntity<>(new HashMap<>(), new HttpHeaders()), ResponseDTO.class))
                .thenReturn(responseDTOResponseEntity);

        // Run the test
        final ResponseDTO<Map<String, Object>> result = middleGroundVehicleServiceImplUnderTest.getVinListCheckInfo(
                paramMap);

        // Verify the results
    }

    @Test
    void testGetVinListCheckInfo_RestTemplateThrowsRestClientException() {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        when(mockMidUrlProperties.getMidEndVehicleCenter()).thenReturn("result");
        when(mockMidUrlProperties.getVinList()).thenReturn("result");
        when(mockDirectRestTemplate.exchange("url", HttpMethod.GET,
                new HttpEntity<>(new HashMap<>(), new HttpHeaders()), ResponseDTO.class))
                .thenThrow(RestClientException.class);

        // Run the test
        final ResponseDTO<Map<String, Object>> result = middleGroundVehicleServiceImplUnderTest.getVinListCheckInfo(
                paramMap);

        // Verify the results
    }
}
