package com.yonyou.dmscus.customer.service.impl.faultLight;

import com.yonyou.dmscus.customer.dao.faultLight.TtFaultLightInvitationMapper;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightInvitationPO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FaultLightInvitationServiceImplTest {

    @Mock
    private TtFaultLightInvitationMapper mockTtFaultLightInvitationMapper;

    @InjectMocks
    private FaultLightInvitationServiceImpl faultLightInvitationServiceImplUnderTest;

    @Test
    void testQueryFaultLightInvitationByClurId() {
        // Setup
        final TtFaultLightInvitationPO expectedResult = TtFaultLightInvitationPO.builder().build();

        // Configure TtFaultLightInvitationMapper.queryFaultLightInvitationByClurId(...).
        final TtFaultLightInvitationPO ttFaultLightInvitationPO = TtFaultLightInvitationPO.builder().build();
        when(mockTtFaultLightInvitationMapper.queryFaultLightInvitationByClurId(0L))
                .thenReturn(ttFaultLightInvitationPO);

        // Run the test
        final TtFaultLightInvitationPO result = faultLightInvitationServiceImplUnderTest.queryFaultLightInvitationByClurId(
                0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
