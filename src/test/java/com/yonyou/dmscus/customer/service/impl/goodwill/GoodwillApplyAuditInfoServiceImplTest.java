package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyAuditDetailLogMapper;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyAuditDetailMapper;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyAuditInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditDetailLogPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditDetailPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditInfoPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillApplyAuditInfoServiceImplTest {

    @Mock
    private GoodwillApplyAuditInfoMapper mockGoodwillApplyAuditInfoMapper;
    @Mock
    private GoodwillApplyAuditDetailMapper mockGoodwillApplyAuditDetailMapper;
    @Mock
    private GoodwillApplyAuditDetailLogMapper mockGoodwillApplyAuditDetailLogMapper;

    private GoodwillApplyAuditInfoServiceImpl goodwillApplyAuditInfoServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        goodwillApplyAuditInfoServiceImplUnderTest = new GoodwillApplyAuditInfoServiceImpl();
        goodwillApplyAuditInfoServiceImplUnderTest.goodwillApplyAuditInfoMapper = mockGoodwillApplyAuditInfoMapper;
        goodwillApplyAuditInfoServiceImplUnderTest.goodwillApplyAuditDetailMapper = mockGoodwillApplyAuditDetailMapper;
        goodwillApplyAuditInfoServiceImplUnderTest.goodwillApplyAuditDetailLogMapper = mockGoodwillApplyAuditDetailLogMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO = new GoodwillApplyAuditInfoDTO();
        goodwillApplyAuditInfoDTO.setAuditWay("auditWay");
        final GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO = new GoodwillApplyAuditDetailDTO();
        goodwillApplyAuditDetailDTO.setAppId("appId");
        goodwillApplyAuditDetailDTO.setOwnerCode("ownerCode");
        goodwillApplyAuditInfoDTO.setGoodwillApplyAuditDetailDto(Arrays.asList(goodwillApplyAuditDetailDTO));
        goodwillApplyAuditInfoDTO.setId(0L);

        // Configure GoodwillApplyAuditInfoMapper.selectPageBySql(...).
        final GoodwillApplyAuditInfoPO goodwillApplyAuditInfoPO = new GoodwillApplyAuditInfoPO();
        goodwillApplyAuditInfoPO.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyAuditInfoPO.setAppId("appId");
        goodwillApplyAuditInfoPO.setId(0L);
        goodwillApplyAuditInfoPO.setGoodwillApplyId(0L);
        goodwillApplyAuditInfoPO.setIsAudit(0);
        final List<GoodwillApplyAuditInfoPO> goodwillApplyAuditInfoPOS = Arrays.asList(goodwillApplyAuditInfoPO);
        when(mockGoodwillApplyAuditInfoMapper.selectPageBySql(any(Page.class),
                any(GoodwillApplyAuditInfoPO.class))).thenReturn(goodwillApplyAuditInfoPOS);

        // Run the test
        final IPage<GoodwillApplyAuditInfoDTO> result = goodwillApplyAuditInfoServiceImplUnderTest.selectPageBysql(page,
                goodwillApplyAuditInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillApplyAuditInfoMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO = new GoodwillApplyAuditInfoDTO();
        goodwillApplyAuditInfoDTO.setAuditWay("auditWay");
        final GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO = new GoodwillApplyAuditDetailDTO();
        goodwillApplyAuditDetailDTO.setAppId("appId");
        goodwillApplyAuditDetailDTO.setOwnerCode("ownerCode");
        goodwillApplyAuditInfoDTO.setGoodwillApplyAuditDetailDto(Arrays.asList(goodwillApplyAuditDetailDTO));
        goodwillApplyAuditInfoDTO.setId(0L);

        when(mockGoodwillApplyAuditInfoMapper.selectPageBySql(any(Page.class),
                any(GoodwillApplyAuditInfoPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillApplyAuditInfoDTO> result = goodwillApplyAuditInfoServiceImplUnderTest.selectPageBysql(page,
                goodwillApplyAuditInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO = new GoodwillApplyAuditInfoDTO();
        goodwillApplyAuditInfoDTO.setAuditWay("auditWay");
        final GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO = new GoodwillApplyAuditDetailDTO();
        goodwillApplyAuditDetailDTO.setAppId("appId");
        goodwillApplyAuditDetailDTO.setOwnerCode("ownerCode");
        goodwillApplyAuditInfoDTO.setGoodwillApplyAuditDetailDto(Arrays.asList(goodwillApplyAuditDetailDTO));
        goodwillApplyAuditInfoDTO.setId(0L);

        // Configure GoodwillApplyAuditInfoMapper.selectListBySql(...).
        final GoodwillApplyAuditInfoPO goodwillApplyAuditInfoPO = new GoodwillApplyAuditInfoPO();
        goodwillApplyAuditInfoPO.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyAuditInfoPO.setAppId("appId");
        goodwillApplyAuditInfoPO.setId(0L);
        goodwillApplyAuditInfoPO.setGoodwillApplyId(0L);
        goodwillApplyAuditInfoPO.setIsAudit(0);
        final List<GoodwillApplyAuditInfoPO> goodwillApplyAuditInfoPOS = Arrays.asList(goodwillApplyAuditInfoPO);
        when(mockGoodwillApplyAuditInfoMapper.selectListBySql(any(GoodwillApplyAuditInfoPO.class)))
                .thenReturn(goodwillApplyAuditInfoPOS);

        // Run the test
        final List<GoodwillApplyAuditInfoDTO> result = goodwillApplyAuditInfoServiceImplUnderTest.selectListBySql(
                goodwillApplyAuditInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillApplyAuditInfoMapperReturnsNoItems() {
        // Setup
        final GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO = new GoodwillApplyAuditInfoDTO();
        goodwillApplyAuditInfoDTO.setAuditWay("auditWay");
        final GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO = new GoodwillApplyAuditDetailDTO();
        goodwillApplyAuditDetailDTO.setAppId("appId");
        goodwillApplyAuditDetailDTO.setOwnerCode("ownerCode");
        goodwillApplyAuditInfoDTO.setGoodwillApplyAuditDetailDto(Arrays.asList(goodwillApplyAuditDetailDTO));
        goodwillApplyAuditInfoDTO.setId(0L);

        when(mockGoodwillApplyAuditInfoMapper.selectListBySql(any(GoodwillApplyAuditInfoPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillApplyAuditInfoDTO> result = goodwillApplyAuditInfoServiceImplUnderTest.selectListBySql(
                goodwillApplyAuditInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure GoodwillApplyAuditInfoMapper.selectById(...).
        final GoodwillApplyAuditInfoPO goodwillApplyAuditInfoPO = new GoodwillApplyAuditInfoPO();
        goodwillApplyAuditInfoPO.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyAuditInfoPO.setAppId("appId");
        goodwillApplyAuditInfoPO.setId(0L);
        goodwillApplyAuditInfoPO.setGoodwillApplyId(0L);
        goodwillApplyAuditInfoPO.setIsAudit(0);
        when(mockGoodwillApplyAuditInfoMapper.selectById(0L)).thenReturn(goodwillApplyAuditInfoPO);

        // Run the test
        final GoodwillApplyAuditInfoDTO result = goodwillApplyAuditInfoServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillApplyAuditInfoMapperReturnsNull() {
        // Setup
        when(mockGoodwillApplyAuditInfoMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillApplyAuditInfoServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO = new GoodwillApplyAuditInfoDTO();
        goodwillApplyAuditInfoDTO.setAuditWay("auditWay");
        final GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO = new GoodwillApplyAuditDetailDTO();
        goodwillApplyAuditDetailDTO.setAppId("appId");
        goodwillApplyAuditDetailDTO.setOwnerCode("ownerCode");
        goodwillApplyAuditInfoDTO.setGoodwillApplyAuditDetailDto(Arrays.asList(goodwillApplyAuditDetailDTO));
        goodwillApplyAuditInfoDTO.setId(0L);

        when(mockGoodwillApplyAuditInfoMapper.insert(any(GoodwillApplyAuditInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillApplyAuditInfoServiceImplUnderTest.insert(goodwillApplyAuditInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final GoodwillApplyAuditInfoDTO goodwillApplyAuditInfoDTO = new GoodwillApplyAuditInfoDTO();
        goodwillApplyAuditInfoDTO.setAuditWay("auditWay");
        final GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO = new GoodwillApplyAuditDetailDTO();
        goodwillApplyAuditDetailDTO.setAppId("appId");
        goodwillApplyAuditDetailDTO.setOwnerCode("ownerCode");
        goodwillApplyAuditInfoDTO.setGoodwillApplyAuditDetailDto(Arrays.asList(goodwillApplyAuditDetailDTO));
        goodwillApplyAuditInfoDTO.setId(0L);

        // Configure GoodwillApplyAuditInfoMapper.selectById(...).
        final GoodwillApplyAuditInfoPO goodwillApplyAuditInfoPO = new GoodwillApplyAuditInfoPO();
        goodwillApplyAuditInfoPO.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyAuditInfoPO.setAppId("appId");
        goodwillApplyAuditInfoPO.setId(0L);
        goodwillApplyAuditInfoPO.setGoodwillApplyId(0L);
        goodwillApplyAuditInfoPO.setIsAudit(0);
        when(mockGoodwillApplyAuditInfoMapper.selectById(0L)).thenReturn(goodwillApplyAuditInfoPO);

        when(mockGoodwillApplyAuditInfoMapper.updateById(any(GoodwillApplyAuditInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillApplyAuditInfoServiceImplUnderTest.update(0L, goodwillApplyAuditInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }


    @Test
    void testQueryAuditDetailInfo() {
        // Setup
        // Configure GoodwillApplyAuditDetailMapper.queryAuditDetailInfo(...).
        final GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO = new GoodwillApplyAuditDetailDTO();
        goodwillApplyAuditDetailDTO.setAppId("appId");
        goodwillApplyAuditDetailDTO.setOwnerCode("ownerCode");
        goodwillApplyAuditDetailDTO.setOwnerParCode("ownerParCode");
        goodwillApplyAuditDetailDTO.setOrgId(0);
        goodwillApplyAuditDetailDTO.setId(0L);
        final List<GoodwillApplyAuditDetailDTO> goodwillApplyAuditDetailDTOS = Arrays.asList(
                goodwillApplyAuditDetailDTO);
        when(mockGoodwillApplyAuditDetailMapper.queryAuditDetailInfo(0L)).thenReturn(goodwillApplyAuditDetailDTOS);

        // Run the test
        final List<GoodwillApplyAuditDetailDTO> result = goodwillApplyAuditInfoServiceImplUnderTest.queryAuditDetailInfo(
                0L);

        // Verify the results
    }

    @Test
    void testQueryAuditDetailInfo_GoodwillApplyAuditDetailMapperReturnsNoItems() {
        // Setup
        when(mockGoodwillApplyAuditDetailMapper.queryAuditDetailInfo(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillApplyAuditDetailDTO> result = goodwillApplyAuditInfoServiceImplUnderTest.queryAuditDetailInfo(
                0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
