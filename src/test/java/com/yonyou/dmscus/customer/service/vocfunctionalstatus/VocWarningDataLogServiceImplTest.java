package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yonyou.dmscus.customer.dao.voc.VocWarningDataLogMapper;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataLogPo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class VocWarningDataLogServiceImplTest {

    @Mock
    private VocWarningDataLogMapper mockVocWarningDataLogMapper;

    private VocWarningDataLogServiceImpl vocWarningDataLogServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        vocWarningDataLogServiceImplUnderTest = new VocWarningDataLogServiceImpl();
        vocWarningDataLogServiceImplUnderTest.vocWarningDataLogMapper = mockVocWarningDataLogMapper;
    }

    @Test
    void testInsertList() {
        // Setup
        final VocWarningDataLogPo vocWarningDataLogPo = new VocWarningDataLogPo();
        vocWarningDataLogPo.setId(0L);
        vocWarningDataLogPo.setIsDeleted(0);
        vocWarningDataLogPo.setDt("dt");
        vocWarningDataLogPo.setVin("vin");
        vocWarningDataLogPo.setStatusName("statusName");
        vocWarningDataLogPo.setStatusValue("statusValue");
        final List<VocWarningDataLogPo> logPOSList = Arrays.asList(vocWarningDataLogPo);

        // Run the test
        final int result = vocWarningDataLogServiceImplUnderTest.insertList(logPOSList);

        // Verify the results
//        assertThat(result).isEqualTo(0);

        // Confirm VocWarningDataLogMapper.insertList(...).
        final VocWarningDataLogPo vocWarningDataLogPo1 = new VocWarningDataLogPo();
        vocWarningDataLogPo1.setId(0L);
        vocWarningDataLogPo1.setIsDeleted(0);
        vocWarningDataLogPo1.setDt("dt");
        vocWarningDataLogPo1.setVin("vin");
        vocWarningDataLogPo1.setStatusName("statusName");
        vocWarningDataLogPo1.setStatusValue("statusValue");
        final List<VocWarningDataLogPo> list = Arrays.asList(vocWarningDataLogPo1);
        verify(mockVocWarningDataLogMapper).insertList(list);
    }

    @Test
    void testSelectListBydt() {
        // Setup
        final VocWarningDataLogPo vocWarningDataLogPo = new VocWarningDataLogPo();
        vocWarningDataLogPo.setId(0L);
        vocWarningDataLogPo.setIsDeleted(0);
        vocWarningDataLogPo.setDt("dt");
        vocWarningDataLogPo.setVin("vin");
        vocWarningDataLogPo.setStatusName("statusName");
        vocWarningDataLogPo.setStatusValue("statusValue");
        final List<VocWarningDataLogPo> expectedResult = Arrays.asList(vocWarningDataLogPo);

        // Configure VocWarningDataLogMapper.selectList(...).
        final VocWarningDataLogPo vocWarningDataLogPo1 = new VocWarningDataLogPo();
        vocWarningDataLogPo1.setId(0L);
        vocWarningDataLogPo1.setIsDeleted(0);
        vocWarningDataLogPo1.setDt("dt");
        vocWarningDataLogPo1.setVin("vin");
        vocWarningDataLogPo1.setStatusName("statusName");
        vocWarningDataLogPo1.setStatusValue("statusValue");
        final List<VocWarningDataLogPo> vocWarningDataLogPos = Arrays.asList(vocWarningDataLogPo1);
        when(mockVocWarningDataLogMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(vocWarningDataLogPos);

        // Run the test
        final List<VocWarningDataLogPo> result = vocWarningDataLogServiceImplUnderTest.selectListBydt("data", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectListBydt_VocWarningDataLogMapperReturnsNoItems() {
        // Setup
        when(mockVocWarningDataLogMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<VocWarningDataLogPo> result = vocWarningDataLogServiceImplUnderTest.selectListBydt("data", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectCount() {
        // Setup
        when(mockVocWarningDataLogMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final int result = vocWarningDataLogServiceImplUnderTest.selectCount("vin");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectWarningdailyLogByVin() {
        // Setup
        // Configure VocWarningDataLogMapper.selectList(...).
        final VocWarningDataLogPo vocWarningDataLogPo = new VocWarningDataLogPo();
        vocWarningDataLogPo.setId(0L);
        vocWarningDataLogPo.setIsDeleted(0);
        vocWarningDataLogPo.setDt("dt");
        vocWarningDataLogPo.setVin("vin");
        vocWarningDataLogPo.setStatusName("statusName");
        vocWarningDataLogPo.setStatusValue("statusValue");
        final List<VocWarningDataLogPo> vocWarningDataLogPos = Arrays.asList(vocWarningDataLogPo);
        when(mockVocWarningDataLogMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(vocWarningDataLogPos);

        // Run the test
        final int result = vocWarningDataLogServiceImplUnderTest.selectWarningdailyLogByVin("vin");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectWarningdailyLogByVin_VocWarningDataLogMapperReturnsNoItems() {
        // Setup
        when(mockVocWarningDataLogMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final int result = vocWarningDataLogServiceImplUnderTest.selectWarningdailyLogByVin("vin");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
