package com.yonyou.dmscus.customer.service.common.clue;

import com.yonyou.dmscus.customer.dao.common.ClueLogMapper;
import com.yonyou.dmscus.customer.entity.po.common.ClueLogPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class ClueLogServiceImplTest {

    @Mock
    private ClueLogMapper mockClueLogMapper;

    private ClueLogServiceImpl clueLogServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        clueLogServiceImplUnderTest = new ClueLogServiceImpl();
        clueLogServiceImplUnderTest.clueLogMapper = mockClueLogMapper;
    }

    @Test
    void testAdd() {
        // Setup
        final ClueLogPO po = new ClueLogPO();
        po.setId(0L);
        po.setIcmId(0L);
        po.setRequestData("requestData");

        // Run the test
        clueLogServiceImplUnderTest.add(po);

        // Verify the results
        // Confirm ClueLogMapper.insert(...).
        final ClueLogPO entity = new ClueLogPO();
        entity.setId(0L);
        entity.setIcmId(0L);
        entity.setRequestData("requestData");
        verify(mockClueLogMapper).insert(entity);
    }
}
