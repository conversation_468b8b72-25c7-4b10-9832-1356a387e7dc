package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyMailHistoryMapper;
import com.yonyou.dmscus.customer.dto.EmailInfoDto;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyMailHistoryDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyMailHistoryPO;
import com.yonyou.dmscus.customer.service.CommonService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillApplyMailHistoryServiceImplTest {

    @Mock
    private GoodwillApplyMailHistoryMapper mockGoodwillApplyMailHistoryMapper;
    @Mock
    private CommonService mockCommonService;

    private GoodwillApplyMailHistoryServiceImpl goodwillApplyMailHistoryServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        goodwillApplyMailHistoryServiceImplUnderTest = new GoodwillApplyMailHistoryServiceImpl();
        goodwillApplyMailHistoryServiceImplUnderTest.goodwillApplyMailHistoryMapper = mockGoodwillApplyMailHistoryMapper;
        goodwillApplyMailHistoryServiceImplUnderTest.commonService = mockCommonService;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO = new GoodwillApplyMailHistoryDTO();
        goodwillApplyMailHistoryDTO.setBlocId(0);
        goodwillApplyMailHistoryDTO.setAreaManageId(0);
        goodwillApplyMailHistoryDTO.setAreaManage("areaManage");
        goodwillApplyMailHistoryDTO.setSmallArea("smallArea");
        goodwillApplyMailHistoryDTO.setApplyNo("applyNo");

        // Configure GoodwillApplyMailHistoryMapper.selectPageBySql(...).
        final GoodwillApplyMailHistoryPO goodwillApplyMailHistoryPO = new GoodwillApplyMailHistoryPO();
        goodwillApplyMailHistoryPO.setSendBy("sendBy");
        goodwillApplyMailHistoryPO.setReceiverMail("receiverMail");
        goodwillApplyMailHistoryPO.setTitle("title");
        goodwillApplyMailHistoryPO.setContent("content");
        goodwillApplyMailHistoryPO.setSendStatus(0);
        goodwillApplyMailHistoryPO.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<GoodwillApplyMailHistoryPO> goodwillApplyMailHistoryPOS = Arrays.asList(goodwillApplyMailHistoryPO);
        when(mockGoodwillApplyMailHistoryMapper.selectPageBySql(any(Page.class),
                any(GoodwillApplyMailHistoryPO.class))).thenReturn(goodwillApplyMailHistoryPOS);

        // Run the test
        final IPage<GoodwillApplyMailHistoryDTO> result = goodwillApplyMailHistoryServiceImplUnderTest.selectPageBysql(
                page, goodwillApplyMailHistoryDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillApplyMailHistoryMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO = new GoodwillApplyMailHistoryDTO();
        goodwillApplyMailHistoryDTO.setBlocId(0);
        goodwillApplyMailHistoryDTO.setAreaManageId(0);
        goodwillApplyMailHistoryDTO.setAreaManage("areaManage");
        goodwillApplyMailHistoryDTO.setSmallArea("smallArea");
        goodwillApplyMailHistoryDTO.setApplyNo("applyNo");

        when(mockGoodwillApplyMailHistoryMapper.selectPageBySql(any(Page.class),
                any(GoodwillApplyMailHistoryPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillApplyMailHistoryDTO> result = goodwillApplyMailHistoryServiceImplUnderTest.selectPageBysql(
                page, goodwillApplyMailHistoryDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO = new GoodwillApplyMailHistoryDTO();
        goodwillApplyMailHistoryDTO.setBlocId(0);
        goodwillApplyMailHistoryDTO.setAreaManageId(0);
        goodwillApplyMailHistoryDTO.setAreaManage("areaManage");
        goodwillApplyMailHistoryDTO.setSmallArea("smallArea");
        goodwillApplyMailHistoryDTO.setApplyNo("applyNo");

        // Configure GoodwillApplyMailHistoryMapper.selectListBySql(...).
        final GoodwillApplyMailHistoryPO goodwillApplyMailHistoryPO = new GoodwillApplyMailHistoryPO();
        goodwillApplyMailHistoryPO.setSendBy("sendBy");
        goodwillApplyMailHistoryPO.setReceiverMail("receiverMail");
        goodwillApplyMailHistoryPO.setTitle("title");
        goodwillApplyMailHistoryPO.setContent("content");
        goodwillApplyMailHistoryPO.setSendStatus(0);
        goodwillApplyMailHistoryPO.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<GoodwillApplyMailHistoryPO> goodwillApplyMailHistoryPOS = Arrays.asList(goodwillApplyMailHistoryPO);
        when(mockGoodwillApplyMailHistoryMapper.selectListBySql(any(GoodwillApplyMailHistoryPO.class)))
                .thenReturn(goodwillApplyMailHistoryPOS);

        // Run the test
        final List<GoodwillApplyMailHistoryDTO> result = goodwillApplyMailHistoryServiceImplUnderTest.selectListBySql(
                goodwillApplyMailHistoryDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillApplyMailHistoryMapperReturnsNoItems() {
        // Setup
        final GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO = new GoodwillApplyMailHistoryDTO();
        goodwillApplyMailHistoryDTO.setBlocId(0);
        goodwillApplyMailHistoryDTO.setAreaManageId(0);
        goodwillApplyMailHistoryDTO.setAreaManage("areaManage");
        goodwillApplyMailHistoryDTO.setSmallArea("smallArea");
        goodwillApplyMailHistoryDTO.setApplyNo("applyNo");

        when(mockGoodwillApplyMailHistoryMapper.selectListBySql(any(GoodwillApplyMailHistoryPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillApplyMailHistoryDTO> result = goodwillApplyMailHistoryServiceImplUnderTest.selectListBySql(
                goodwillApplyMailHistoryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure GoodwillApplyMailHistoryMapper.selectById(...).
        final GoodwillApplyMailHistoryPO goodwillApplyMailHistoryPO = new GoodwillApplyMailHistoryPO();
        goodwillApplyMailHistoryPO.setSendBy("sendBy");
        goodwillApplyMailHistoryPO.setReceiverMail("receiverMail");
        goodwillApplyMailHistoryPO.setTitle("title");
        goodwillApplyMailHistoryPO.setContent("content");
        goodwillApplyMailHistoryPO.setSendStatus(0);
        goodwillApplyMailHistoryPO.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockGoodwillApplyMailHistoryMapper.selectById(0L)).thenReturn(goodwillApplyMailHistoryPO);

        // Run the test
        final GoodwillApplyMailHistoryDTO result = goodwillApplyMailHistoryServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillApplyMailHistoryMapperReturnsNull() {
        // Setup
        when(mockGoodwillApplyMailHistoryMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillApplyMailHistoryServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO = new GoodwillApplyMailHistoryDTO();
        goodwillApplyMailHistoryDTO.setBlocId(0);
        goodwillApplyMailHistoryDTO.setAreaManageId(0);
        goodwillApplyMailHistoryDTO.setAreaManage("areaManage");
        goodwillApplyMailHistoryDTO.setSmallArea("smallArea");
        goodwillApplyMailHistoryDTO.setApplyNo("applyNo");

        when(mockGoodwillApplyMailHistoryMapper.insert(any(GoodwillApplyMailHistoryPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillApplyMailHistoryServiceImplUnderTest.insert(goodwillApplyMailHistoryDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final GoodwillApplyMailHistoryDTO goodwillApplyMailHistoryDTO = new GoodwillApplyMailHistoryDTO();
        goodwillApplyMailHistoryDTO.setBlocId(0);
        goodwillApplyMailHistoryDTO.setAreaManageId(0);
        goodwillApplyMailHistoryDTO.setAreaManage("areaManage");
        goodwillApplyMailHistoryDTO.setSmallArea("smallArea");
        goodwillApplyMailHistoryDTO.setApplyNo("applyNo");

        // Configure GoodwillApplyMailHistoryMapper.selectById(...).
        final GoodwillApplyMailHistoryPO goodwillApplyMailHistoryPO = new GoodwillApplyMailHistoryPO();
        goodwillApplyMailHistoryPO.setSendBy("sendBy");
        goodwillApplyMailHistoryPO.setReceiverMail("receiverMail");
        goodwillApplyMailHistoryPO.setTitle("title");
        goodwillApplyMailHistoryPO.setContent("content");
        goodwillApplyMailHistoryPO.setSendStatus(0);
        goodwillApplyMailHistoryPO.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockGoodwillApplyMailHistoryMapper.selectById(0L)).thenReturn(goodwillApplyMailHistoryPO);

        when(mockGoodwillApplyMailHistoryMapper.updateById(any(GoodwillApplyMailHistoryPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillApplyMailHistoryServiceImplUnderTest.update(0L, goodwillApplyMailHistoryDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSendAgain() {
        // Setup
        // Configure GoodwillApplyMailHistoryMapper.selectById(...).
        final GoodwillApplyMailHistoryPO goodwillApplyMailHistoryPO = new GoodwillApplyMailHistoryPO();
        goodwillApplyMailHistoryPO.setSendBy("sendBy");
        goodwillApplyMailHistoryPO.setReceiverMail("receiverMail");
        goodwillApplyMailHistoryPO.setTitle("title");
        goodwillApplyMailHistoryPO.setContent("content");
        goodwillApplyMailHistoryPO.setSendStatus(0);
        goodwillApplyMailHistoryPO.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockGoodwillApplyMailHistoryMapper.selectById(0L)).thenReturn(goodwillApplyMailHistoryPO);

        // Configure CommonService.sendGoodwillMail(...).
        final EmailInfoDto emailInfoDto = new EmailInfoDto();
        emailInfoDto.setFrom("sendBy");
        emailInfoDto.setToOneIds(new String[]{"toOneIds"});
        emailInfoDto.setTo(new String[]{"to"});
        emailInfoDto.setSubject("title");
        emailInfoDto.setText("content");
        when(mockCommonService.sendGoodwillMail(emailInfoDto)).thenReturn("result");

        when(mockGoodwillApplyMailHistoryMapper.updateById(any(GoodwillApplyMailHistoryPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillApplyMailHistoryServiceImplUnderTest.sendAgain(0L);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
