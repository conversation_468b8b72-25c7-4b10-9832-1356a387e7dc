package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.yonyou.dmscus.customer.dao.complaint.ComplaintInfoMapper;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintInfoMapper;
import com.yonyou.dmscus.customer.dto.GetModelNameDTO;
import com.yonyou.dmscus.customer.dto.modelNameDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.AcceptComlaintData.AcceptNewComplaintDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.SelectSmallManagerDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.SmallManagerDataDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoPO;
import com.yonyou.dmscus.customer.middleInterface.CompanyDetailByCodeDTO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintFollowService;
import com.yonyou.dmscus.customer.service.complaint.ComplaintInfoService;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintFollowService;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintInfoService;
import com.yonyou.dmscus.customer.service.impl.CommonServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AcceptComplaintServiceImplTest {

    @Mock
    private ComplaintInfoService mockComplaintInfoService;
    @Mock
    private ComplaintInfoMapper mockComplaintInfoMapper;
    @Mock
    private ComplaintFollowService mockComplaintFollowService;
    @Mock
    private CommonServiceImpl mockCommonServiceImpl;
    @Mock
    private ComplaintDealerCcmRefServiceImpl mockComplaintDealerCcmRefService;
    @Mock
    private SaleComplaintInfoMapper mockSaleComplaintInfoMapper;
    @Mock
    private SaleComplaintInfoService mockSaleComplaintInfoService;
    @Mock
    private SaleComplaintFollowService mockSaleComplaintFollowService;

    private AcceptComplaintServiceImpl acceptComplaintServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        acceptComplaintServiceImplUnderTest = new AcceptComplaintServiceImpl();
        acceptComplaintServiceImplUnderTest.complaintInfoService = mockComplaintInfoService;
        acceptComplaintServiceImplUnderTest.complaintInfoMapper = mockComplaintInfoMapper;
        acceptComplaintServiceImplUnderTest.complaintFollowService = mockComplaintFollowService;
        acceptComplaintServiceImplUnderTest.commonServiceImpl = mockCommonServiceImpl;
        acceptComplaintServiceImplUnderTest.complaintDealerCcmRefService = mockComplaintDealerCcmRefService;
        acceptComplaintServiceImplUnderTest.saleComplaintInfoMapper = mockSaleComplaintInfoMapper;
        acceptComplaintServiceImplUnderTest.saleComplaintInfoService = mockSaleComplaintInfoService;
        acceptComplaintServiceImplUnderTest.saleComplaintFollowService = mockSaleComplaintFollowService;
    }

    @Test
    void testGetWorkOrderClassification() {
        assertThat(acceptComplaintServiceImplUnderTest.getWorkOrderClassification("workOrderNatureData")).isEqualTo(0);
    }

    @Test
    void testGetSource() {
        assertThat(acceptComplaintServiceImplUnderTest.getSource("SourceData")).isEqualTo(0);
    }

    @Test
    void testGetServiceCommitment() {
        assertThat(acceptComplaintServiceImplUnderTest.getServiceCommitment("serviceCommitmentData")).isEqualTo(0);
    }

    @Test
    void testGetCategory1() {
        assertThat(acceptComplaintServiceImplUnderTest.getCategory1("Category1Data")).isEqualTo("");
    }

    @Test
    void testGetCategory2() {
        assertThat(acceptComplaintServiceImplUnderTest.getCategory2("Category2Data")).isEqualTo("");
    }

    @Test
    void testGetCategory3() {
        assertThat(acceptComplaintServiceImplUnderTest.getCategory3("Category3Data")).isEqualTo("");
    }

    @Test
    void testGetCcPart() {
        assertThat(acceptComplaintServiceImplUnderTest.getCcPart("ccPartData")).isEqualTo("result");
    }

    @Test
    void testGetCcSubdivisionPart() {
        assertThat(acceptComplaintServiceImplUnderTest.getCcSubdivisionPart("ccSubdivisionPartData"))
                .isEqualTo("result");
    }

    @Test
    void testGetCcProblem() {
        assertThat(acceptComplaintServiceImplUnderTest.getCcProblem("ccProblemData")).isEqualTo("result");
    }

    @Test
    void testGetCcRequirement() {
        assertThat(acceptComplaintServiceImplUnderTest.getCcRequirement("ccRequirementData")).isEqualTo("result");
    }

    @Test
    void testGetImportanceLevel() {
        assertThat(acceptComplaintServiceImplUnderTest.getImportanceLevel("importanceLevelData")).isEqualTo(0);
    }
}
