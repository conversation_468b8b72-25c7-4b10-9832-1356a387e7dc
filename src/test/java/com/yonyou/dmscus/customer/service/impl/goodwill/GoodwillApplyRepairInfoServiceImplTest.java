package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyRepairInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyRepairInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyRepairInfoPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillApplyRepairInfoServiceImplTest {

    @Mock
    private GoodwillApplyRepairInfoMapper mockGoodwillApplyRepairInfoMapper;

    private GoodwillApplyRepairInfoServiceImpl goodwillApplyRepairInfoServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        goodwillApplyRepairInfoServiceImplUnderTest = new GoodwillApplyRepairInfoServiceImpl();
        goodwillApplyRepairInfoServiceImplUnderTest.goodwillApplyRepairInfoMapper = mockGoodwillApplyRepairInfoMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setAppId("appId");
        goodwillApplyRepairInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyRepairInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairInfoDTO.setOrgId(0);
        goodwillApplyRepairInfoDTO.setId(0L);

        // Configure GoodwillApplyRepairInfoMapper.selectPageBySql(...).
        final GoodwillApplyRepairInfoPO goodwillApplyRepairInfoPO = new GoodwillApplyRepairInfoPO();
        goodwillApplyRepairInfoPO.setAppId("appId");
        goodwillApplyRepairInfoPO.setOwnerCode("ownerCode");
        goodwillApplyRepairInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairInfoPO.setOrgId(0);
        goodwillApplyRepairInfoPO.setId(0L);
        final List<GoodwillApplyRepairInfoPO> goodwillApplyRepairInfoPOS = Arrays.asList(goodwillApplyRepairInfoPO);
        when(mockGoodwillApplyRepairInfoMapper.selectPageBySql(any(Page.class),
                any(GoodwillApplyRepairInfoPO.class))).thenReturn(goodwillApplyRepairInfoPOS);

        // Run the test
        final IPage<GoodwillApplyRepairInfoDTO> result = goodwillApplyRepairInfoServiceImplUnderTest.selectPageBysql(
                page, goodwillApplyRepairInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillApplyRepairInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setAppId("appId");
        goodwillApplyRepairInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyRepairInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairInfoDTO.setOrgId(0);
        goodwillApplyRepairInfoDTO.setId(0L);

        when(mockGoodwillApplyRepairInfoMapper.selectPageBySql(any(Page.class),
                any(GoodwillApplyRepairInfoPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillApplyRepairInfoDTO> result = goodwillApplyRepairInfoServiceImplUnderTest.selectPageBysql(
                page, goodwillApplyRepairInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setAppId("appId");
        goodwillApplyRepairInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyRepairInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairInfoDTO.setOrgId(0);
        goodwillApplyRepairInfoDTO.setId(0L);

        // Configure GoodwillApplyRepairInfoMapper.selectListBySql(...).
        final GoodwillApplyRepairInfoPO goodwillApplyRepairInfoPO = new GoodwillApplyRepairInfoPO();
        goodwillApplyRepairInfoPO.setAppId("appId");
        goodwillApplyRepairInfoPO.setOwnerCode("ownerCode");
        goodwillApplyRepairInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairInfoPO.setOrgId(0);
        goodwillApplyRepairInfoPO.setId(0L);
        final List<GoodwillApplyRepairInfoPO> goodwillApplyRepairInfoPOS = Arrays.asList(goodwillApplyRepairInfoPO);
        when(mockGoodwillApplyRepairInfoMapper.selectListBySql(any(GoodwillApplyRepairInfoPO.class)))
                .thenReturn(goodwillApplyRepairInfoPOS);

        // Run the test
        final List<GoodwillApplyRepairInfoDTO> result = goodwillApplyRepairInfoServiceImplUnderTest.selectListBySql(
                goodwillApplyRepairInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillApplyRepairInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setAppId("appId");
        goodwillApplyRepairInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyRepairInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairInfoDTO.setOrgId(0);
        goodwillApplyRepairInfoDTO.setId(0L);

        when(mockGoodwillApplyRepairInfoMapper.selectListBySql(any(GoodwillApplyRepairInfoPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillApplyRepairInfoDTO> result = goodwillApplyRepairInfoServiceImplUnderTest.selectListBySql(
                goodwillApplyRepairInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure GoodwillApplyRepairInfoMapper.selectById(...).
        final GoodwillApplyRepairInfoPO goodwillApplyRepairInfoPO = new GoodwillApplyRepairInfoPO();
        goodwillApplyRepairInfoPO.setAppId("appId");
        goodwillApplyRepairInfoPO.setOwnerCode("ownerCode");
        goodwillApplyRepairInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairInfoPO.setOrgId(0);
        goodwillApplyRepairInfoPO.setId(0L);
        when(mockGoodwillApplyRepairInfoMapper.selectById(0L)).thenReturn(goodwillApplyRepairInfoPO);

        // Run the test
        final GoodwillApplyRepairInfoDTO result = goodwillApplyRepairInfoServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillApplyRepairInfoMapperReturnsNull() {
        // Setup
        when(mockGoodwillApplyRepairInfoMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillApplyRepairInfoServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setAppId("appId");
        goodwillApplyRepairInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyRepairInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairInfoDTO.setOrgId(0);
        goodwillApplyRepairInfoDTO.setId(0L);

        when(mockGoodwillApplyRepairInfoMapper.insert(any(GoodwillApplyRepairInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillApplyRepairInfoServiceImplUnderTest.insert(goodwillApplyRepairInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setAppId("appId");
        goodwillApplyRepairInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyRepairInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairInfoDTO.setOrgId(0);
        goodwillApplyRepairInfoDTO.setId(0L);

        // Configure GoodwillApplyRepairInfoMapper.selectById(...).
        final GoodwillApplyRepairInfoPO goodwillApplyRepairInfoPO = new GoodwillApplyRepairInfoPO();
        goodwillApplyRepairInfoPO.setAppId("appId");
        goodwillApplyRepairInfoPO.setOwnerCode("ownerCode");
        goodwillApplyRepairInfoPO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairInfoPO.setOrgId(0);
        goodwillApplyRepairInfoPO.setId(0L);
        when(mockGoodwillApplyRepairInfoMapper.selectById(0L)).thenReturn(goodwillApplyRepairInfoPO);

        when(mockGoodwillApplyRepairInfoMapper.updateById(any(GoodwillApplyRepairInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillApplyRepairInfoServiceImplUnderTest.update(0L, goodwillApplyRepairInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testGetSupportApplyRepairInfoById() throws Exception {
        // Setup
        // Configure GoodwillApplyRepairInfoMapper.getSupportApplyRepairInfoById(...).
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setAppId("appId");
        goodwillApplyRepairInfoDTO.setOwnerCode("ownerCode");
        goodwillApplyRepairInfoDTO.setOwnerParCode("ownerParCode");
        goodwillApplyRepairInfoDTO.setOrgId(0);
        goodwillApplyRepairInfoDTO.setId(0L);
        final List<GoodwillApplyRepairInfoDTO> goodwillApplyRepairInfoDTOS = Arrays.asList(goodwillApplyRepairInfoDTO);
        when(mockGoodwillApplyRepairInfoMapper.getSupportApplyRepairInfoById(0L))
                .thenReturn(goodwillApplyRepairInfoDTOS);

        // Run the test
        final List<GoodwillApplyRepairInfoDTO> result = goodwillApplyRepairInfoServiceImplUnderTest.getSupportApplyRepairInfoById(
                0L);

        // Verify the results
    }

    @Test
    void testGetSupportApplyRepairInfoById_GoodwillApplyRepairInfoMapperReturnsNoItems() throws Exception {
        // Setup
        when(mockGoodwillApplyRepairInfoMapper.getSupportApplyRepairInfoById(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillApplyRepairInfoDTO> result = goodwillApplyRepairInfoServiceImplUnderTest.getSupportApplyRepairInfoById(
                0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
