package com.yonyou.dmscus.customer.service.impl.invitationautocreate;

import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dao.invitationautocreate.InviteVehicleTaskMapper;
import com.yonyou.dmscus.customer.service.invitationautocreate.po.InviteVehiclePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class IncRepCleanDataServiceImplTest {

    @Mock
    private InviteVehicleTaskMapper mockInviteVehicleTaskMapper;
    @Mock
    private InviteVehicleRecordMapper mockInviteVehicleRecordMapper;

    private IncRepCleanDataServiceImpl incRepCleanDataServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        incRepCleanDataServiceImplUnderTest = new IncRepCleanDataServiceImpl();
        incRepCleanDataServiceImplUnderTest.inviteVehicleTaskMapper = mockInviteVehicleTaskMapper;
        incRepCleanDataServiceImplUnderTest.inviteVehicleRecordMapper = mockInviteVehicleRecordMapper;
    }

    @Test
    void testDoTaskClean() {
        // Setup
        final InviteVehiclePO inviteVehiclePO = new InviteVehiclePO();
        inviteVehiclePO.setId(0L);
        inviteVehiclePO.setVin("vin");
        inviteVehiclePO.setName("name");
        inviteVehiclePO.setTel("tel");
        inviteVehiclePO.setInviteType(0);
        final List<InviteVehiclePO> cloTask = Arrays.asList(inviteVehiclePO);
        final InviteVehiclePO inviteVehiclePO1 = new InviteVehiclePO();
        inviteVehiclePO1.setId(0L);
        inviteVehiclePO1.setVin("vin");
        inviteVehiclePO1.setName("name");
        inviteVehiclePO1.setTel("tel");
        inviteVehiclePO1.setInviteType(0);
        final List<InviteVehiclePO> upTask = Arrays.asList(inviteVehiclePO1);

        // Run the test
        incRepCleanDataServiceImplUnderTest.doTaskClean(cloTask, upTask);

        // Verify the results
        // Confirm InviteVehicleTaskMapper.doProDataCloseTask(...).
        final InviteVehiclePO inviteVehiclePO2 = new InviteVehiclePO();
        inviteVehiclePO2.setId(0L);
        inviteVehiclePO2.setVin("vin");
        inviteVehiclePO2.setName("name");
        inviteVehiclePO2.setTel("tel");
        inviteVehiclePO2.setInviteType(0);
        final List<InviteVehiclePO> params = Arrays.asList(inviteVehiclePO2);
        verify(mockInviteVehicleTaskMapper).doProDataCloseTask(params);

        // Confirm InviteVehicleTaskMapper.doProDataUpdateTask(...).
        final InviteVehiclePO inviteVehiclePO3 = new InviteVehiclePO();
        inviteVehiclePO3.setId(0L);
        inviteVehiclePO3.setVin("vin");
        inviteVehiclePO3.setName("name");
        inviteVehiclePO3.setTel("tel");
        inviteVehiclePO3.setInviteType(0);
        final List<InviteVehiclePO> params1 = Arrays.asList(inviteVehiclePO3);
        verify(mockInviteVehicleTaskMapper).doProDataUpdateTask(params1);
    }

    @Test
    void testDoClueClean() {
        // Setup
        final InviteVehiclePO inviteVehiclePO = new InviteVehiclePO();
        inviteVehiclePO.setId(0L);
        inviteVehiclePO.setVin("vin");
        inviteVehiclePO.setName("name");
        inviteVehiclePO.setTel("tel");
        inviteVehiclePO.setInviteType(0);
        final List<InviteVehiclePO> cloClue = Arrays.asList(inviteVehiclePO);
        final InviteVehiclePO inviteVehiclePO1 = new InviteVehiclePO();
        inviteVehiclePO1.setId(0L);
        inviteVehiclePO1.setVin("vin");
        inviteVehiclePO1.setName("name");
        inviteVehiclePO1.setTel("tel");
        inviteVehiclePO1.setInviteType(0);
        final List<InviteVehiclePO> upClue = Arrays.asList(inviteVehiclePO1);

        // Run the test
        incRepCleanDataServiceImplUnderTest.doClueClean(cloClue, upClue);

        // Verify the results
        // Confirm InviteVehicleRecordMapper.doProDataCloseClue(...).
        final InviteVehiclePO inviteVehiclePO2 = new InviteVehiclePO();
        inviteVehiclePO2.setId(0L);
        inviteVehiclePO2.setVin("vin");
        inviteVehiclePO2.setName("name");
        inviteVehiclePO2.setTel("tel");
        inviteVehiclePO2.setInviteType(0);
        final List<InviteVehiclePO> params = Arrays.asList(inviteVehiclePO2);
        verify(mockInviteVehicleRecordMapper).doProDataCloseClue(params);

        // Confirm InviteVehicleRecordMapper.doProDataCloseSubClue(...).
        final InviteVehiclePO inviteVehiclePO3 = new InviteVehiclePO();
        inviteVehiclePO3.setId(0L);
        inviteVehiclePO3.setVin("vin");
        inviteVehiclePO3.setName("name");
        inviteVehiclePO3.setTel("tel");
        inviteVehiclePO3.setInviteType(0);
        final List<InviteVehiclePO> params1 = Arrays.asList(inviteVehiclePO3);
        verify(mockInviteVehicleRecordMapper).doProDataCloseSubClue(params1);

        // Confirm InviteVehicleRecordMapper.doProDataUpdateClue(...).
        final InviteVehiclePO inviteVehiclePO4 = new InviteVehiclePO();
        inviteVehiclePO4.setId(0L);
        inviteVehiclePO4.setVin("vin");
        inviteVehiclePO4.setName("name");
        inviteVehiclePO4.setTel("tel");
        inviteVehiclePO4.setInviteType(0);
        final List<InviteVehiclePO> params2 = Arrays.asList(inviteVehiclePO4);
        verify(mockInviteVehicleRecordMapper).doProDataUpdateClue(params2);
    }
}
