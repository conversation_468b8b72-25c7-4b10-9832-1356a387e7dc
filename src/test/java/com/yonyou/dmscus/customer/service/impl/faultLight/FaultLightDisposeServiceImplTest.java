package com.yonyou.dmscus.customer.service.impl.faultLight;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dao.faultLight.FaultLightDisposeMapper;
import com.yonyou.dmscus.customer.entity.dto.faultLight.BatchUpdateDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightDTO;
import com.yonyou.dmscus.customer.entity.po.faultLight.FaultLightPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FaultLightDisposeServiceImplTest {

    @Mock
    private FaultLightDisposeMapper mockFaultLightDisposeMapper;

    @InjectMocks
    private FaultLightDisposeServiceImpl faultLightDisposeServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(faultLightDisposeServiceImplUnderTest, "number", "number");
    }

    @Test
    void testBatchUpdate() {
        // Setup
        final BatchUpdateDTO batchUpdate = new BatchUpdateDTO();
        batchUpdate.setId(Arrays.asList(0L));
        batchUpdate.setProduceStatus(0);
        batchUpdate.setDutyStatus(0);
        batchUpdate.setNum(0);
        batchUpdate.setExpireNumber(0);

        // Configure FaultLightDisposeMapper.batchUpdate(...).
        final BatchUpdateDTO batchUpdate1 = new BatchUpdateDTO();
        batchUpdate1.setId(Arrays.asList(0L));
        batchUpdate1.setProduceStatus(0);
        batchUpdate1.setDutyStatus(0);
        batchUpdate1.setNum(0);
        batchUpdate1.setExpireNumber(0);
        when(mockFaultLightDisposeMapper.batchUpdate(batchUpdate1, Arrays.asList(0L))).thenReturn(0);

        // Run the test
        final int result = faultLightDisposeServiceImplUnderTest.batchUpdate(batchUpdate);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectDutyStatusByIds() {
        // Setup
        when(mockFaultLightDisposeMapper.selectDutyStatusByIds(Arrays.asList(0L))).thenReturn(0);

        // Run the test
        final boolean result = faultLightDisposeServiceImplUnderTest.selectDutyStatusByIds(Arrays.asList(0L));

        // Verify the results
        assertThat(result).isFalse();
    }
}
