package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintCustomFieldUseMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldUseDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldUsePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintCustomFieldUseServiceImplTest {

    @Mock
    private ComplaintCustomFieldUseMapper mockComplaintCustomFieldUseMapper;

    private ComplaintCustomFieldUseServiceImpl complaintCustomFieldUseServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        complaintCustomFieldUseServiceImplUnderTest = new ComplaintCustomFieldUseServiceImpl();
        complaintCustomFieldUseServiceImplUnderTest.complaintCustomFieldUseMapper = mockComplaintCustomFieldUseMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO = new ComplaintCustomFieldUseDTO();
        complaintCustomFieldUseDTO.setTableName("tableName");
        complaintCustomFieldUseDTO.setFieldName("fieldName");
        complaintCustomFieldUseDTO.setFieldDescribe("fieldDescribe");
        complaintCustomFieldUseDTO.setIsQuery(false);
        complaintCustomFieldUseDTO.setIsSort(0);
        complaintCustomFieldUseDTO.setSortType("sortType");
        complaintCustomFieldUseDTO.setUserId(0L);

        // Configure ComplaintCustomFieldUseMapper.selectPageBySql(...).
        final ComplaintCustomFieldUsePO complaintCustomFieldUsePO = new ComplaintCustomFieldUsePO();
        complaintCustomFieldUsePO.setOwnerCode("ownerCode");
        complaintCustomFieldUsePO.setOrgId(0);
        complaintCustomFieldUsePO.setId(0L);
        complaintCustomFieldUsePO.setCustomFieldId(0L);
        complaintCustomFieldUsePO.setTableName("tableName");
        final List<ComplaintCustomFieldUsePO> complaintCustomFieldUsePOS = Arrays.asList(complaintCustomFieldUsePO);
        when(mockComplaintCustomFieldUseMapper.selectPageBySql(any(Page.class),
                any(ComplaintCustomFieldUsePO.class))).thenReturn(complaintCustomFieldUsePOS);

        // Run the test
        final IPage<ComplaintCustomFieldUseDTO> result = complaintCustomFieldUseServiceImplUnderTest.selectPageBysql(
                page, complaintCustomFieldUseDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintCustomFieldUseMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO = new ComplaintCustomFieldUseDTO();
        complaintCustomFieldUseDTO.setTableName("tableName");
        complaintCustomFieldUseDTO.setFieldName("fieldName");
        complaintCustomFieldUseDTO.setFieldDescribe("fieldDescribe");
        complaintCustomFieldUseDTO.setIsQuery(false);
        complaintCustomFieldUseDTO.setIsSort(0);
        complaintCustomFieldUseDTO.setSortType("sortType");
        complaintCustomFieldUseDTO.setUserId(0L);

        when(mockComplaintCustomFieldUseMapper.selectPageBySql(any(Page.class),
                any(ComplaintCustomFieldUsePO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintCustomFieldUseDTO> result = complaintCustomFieldUseServiceImplUnderTest.selectPageBysql(
                page, complaintCustomFieldUseDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO = new ComplaintCustomFieldUseDTO();
        complaintCustomFieldUseDTO.setTableName("tableName");
        complaintCustomFieldUseDTO.setFieldName("fieldName");
        complaintCustomFieldUseDTO.setFieldDescribe("fieldDescribe");
        complaintCustomFieldUseDTO.setIsQuery(false);
        complaintCustomFieldUseDTO.setIsSort(0);
        complaintCustomFieldUseDTO.setSortType("sortType");
        complaintCustomFieldUseDTO.setUserId(0L);

        // Configure ComplaintCustomFieldUseMapper.selectListBySql(...).
        final ComplaintCustomFieldUsePO complaintCustomFieldUsePO = new ComplaintCustomFieldUsePO();
        complaintCustomFieldUsePO.setOwnerCode("ownerCode");
        complaintCustomFieldUsePO.setOrgId(0);
        complaintCustomFieldUsePO.setId(0L);
        complaintCustomFieldUsePO.setCustomFieldId(0L);
        complaintCustomFieldUsePO.setTableName("tableName");
        final List<ComplaintCustomFieldUsePO> complaintCustomFieldUsePOS = Arrays.asList(complaintCustomFieldUsePO);
        when(mockComplaintCustomFieldUseMapper.selectListBySql(any(ComplaintCustomFieldUsePO.class)))
                .thenReturn(complaintCustomFieldUsePOS);

        // Run the test
        final List<ComplaintCustomFieldUseDTO> result = complaintCustomFieldUseServiceImplUnderTest.selectListBySql(
                complaintCustomFieldUseDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintCustomFieldUseMapperReturnsNoItems() {
        // Setup
        final ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO = new ComplaintCustomFieldUseDTO();
        complaintCustomFieldUseDTO.setTableName("tableName");
        complaintCustomFieldUseDTO.setFieldName("fieldName");
        complaintCustomFieldUseDTO.setFieldDescribe("fieldDescribe");
        complaintCustomFieldUseDTO.setIsQuery(false);
        complaintCustomFieldUseDTO.setIsSort(0);
        complaintCustomFieldUseDTO.setSortType("sortType");
        complaintCustomFieldUseDTO.setUserId(0L);

        when(mockComplaintCustomFieldUseMapper.selectListBySql(any(ComplaintCustomFieldUsePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintCustomFieldUseDTO> result = complaintCustomFieldUseServiceImplUnderTest.selectListBySql(
                complaintCustomFieldUseDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure ComplaintCustomFieldUseMapper.selectById(...).
        final ComplaintCustomFieldUsePO complaintCustomFieldUsePO = new ComplaintCustomFieldUsePO();
        complaintCustomFieldUsePO.setOwnerCode("ownerCode");
        complaintCustomFieldUsePO.setOrgId(0);
        complaintCustomFieldUsePO.setId(0L);
        complaintCustomFieldUsePO.setCustomFieldId(0L);
        complaintCustomFieldUsePO.setTableName("tableName");
        when(mockComplaintCustomFieldUseMapper.selectById(0L)).thenReturn(complaintCustomFieldUsePO);

        // Run the test
        final ComplaintCustomFieldUseDTO result = complaintCustomFieldUseServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintCustomFieldUseMapperReturnsNull() {
        // Setup
        when(mockComplaintCustomFieldUseMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintCustomFieldUseServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO = new ComplaintCustomFieldUseDTO();
        complaintCustomFieldUseDTO.setTableName("tableName");
        complaintCustomFieldUseDTO.setFieldName("fieldName");
        complaintCustomFieldUseDTO.setFieldDescribe("fieldDescribe");
        complaintCustomFieldUseDTO.setIsQuery(false);
        complaintCustomFieldUseDTO.setIsSort(0);
        complaintCustomFieldUseDTO.setSortType("sortType");
        complaintCustomFieldUseDTO.setUserId(0L);

        when(mockComplaintCustomFieldUseMapper.insert(any(ComplaintCustomFieldUsePO.class))).thenReturn(0);

        // Run the test
        final int result = complaintCustomFieldUseServiceImplUnderTest.insert(complaintCustomFieldUseDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final ComplaintCustomFieldUseDTO complaintCustomFieldUseDTO = new ComplaintCustomFieldUseDTO();
        complaintCustomFieldUseDTO.setTableName("tableName");
        complaintCustomFieldUseDTO.setFieldName("fieldName");
        complaintCustomFieldUseDTO.setFieldDescribe("fieldDescribe");
        complaintCustomFieldUseDTO.setIsQuery(false);
        complaintCustomFieldUseDTO.setIsSort(0);
        complaintCustomFieldUseDTO.setSortType("sortType");
        complaintCustomFieldUseDTO.setUserId(0L);

        // Configure ComplaintCustomFieldUseMapper.selectById(...).
        final ComplaintCustomFieldUsePO complaintCustomFieldUsePO = new ComplaintCustomFieldUsePO();
        complaintCustomFieldUsePO.setOwnerCode("ownerCode");
        complaintCustomFieldUsePO.setOrgId(0);
        complaintCustomFieldUsePO.setId(0L);
        complaintCustomFieldUsePO.setCustomFieldId(0L);
        complaintCustomFieldUsePO.setTableName("tableName");
        when(mockComplaintCustomFieldUseMapper.selectById(0L)).thenReturn(complaintCustomFieldUsePO);

        when(mockComplaintCustomFieldUseMapper.updateById(any(ComplaintCustomFieldUsePO.class))).thenReturn(0);

        // Run the test
        final int result = complaintCustomFieldUseServiceImplUnderTest.update(0L, complaintCustomFieldUseDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
