package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintAssistDepartmentMapper;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintFollowMapper;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.*;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintFollowPO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoPO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintAssistDepartmentService;
import com.yonyou.dmscus.customer.service.complaint.ComplaintInfoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintFollowServiceImplTest {

    @Mock
    private ComplaintFollowMapper mockComplaintFollowMapper;
    @Mock
    private ComplaintInfoMapper mockComplaintInfoMapper;
    @Mock
    private ComplaintAssistDepartmentMapper mockComplaintAssistDepartmentMapper;
    @Mock
    private ComplaintInfoService mockComplaintInfoService;
    @Mock
    private ComplaintInfoServiceImpl mockComplaintInfoServiceImpl;
    @Mock
    private ComplaintAssistDepartmentService mockComplaintAssistDepartmentService;

    private ComplaintFollowServiceImpl complaintFollowServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        complaintFollowServiceImplUnderTest = new ComplaintFollowServiceImpl();
        complaintFollowServiceImplUnderTest.complaintFollowMapper = mockComplaintFollowMapper;
        complaintFollowServiceImplUnderTest.complaintInfoMapper = mockComplaintInfoMapper;
        complaintFollowServiceImplUnderTest.complaintAssistDepartmentMapper = mockComplaintAssistDepartmentMapper;
        complaintFollowServiceImplUnderTest.complaintInfoService = mockComplaintInfoService;
        complaintFollowServiceImplUnderTest.complaintInfoServiceImpl = mockComplaintInfoServiceImpl;
        complaintFollowServiceImplUnderTest.complaintAssistDepartmentService = mockComplaintAssistDepartmentService;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
        complaintFollowDTO.setDealerNotPublish(false);
        complaintFollowDTO.setFollowerName("followerName");
        complaintFollowDTO.setComplaintInfoId(0L);
        complaintFollowDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setFollower("follower");
        complaintFollowDTO.setFollowContent("followContent");
        complaintFollowDTO.setIsCcmNotPublish(false);
        complaintFollowDTO.setCcmSubject("ccmSubject");
        complaintFollowDTO.setStatus("status");
        complaintFollowDTO.setAdvise(0);
        complaintFollowDTO.setCcmPart("ccmPart");
        complaintFollowDTO.setCcmSubdivisionPart("ccmSubdivisionPart");
        complaintFollowDTO.setCcMainReason("ccMainReason");
        complaintFollowDTO.setCcResult("ccResult");
        complaintFollowDTO.setKeyword("keyword");
        complaintFollowDTO.setClassification1(0);
        complaintFollowDTO.setClassification2("classification2");
        complaintFollowDTO.setClassification3("classification3");
        complaintFollowDTO.setClassification4("classification4");
        complaintFollowDTO.setClassification5("classification5");
        complaintFollowDTO.setClassification6("classification6");
        complaintFollowDTO.setPlanFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setActuallFollowTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setDataSources(0);
        complaintFollowDTO.setQualityClassification1(0);
        complaintFollowDTO.setQualityClassification2(0);
        complaintFollowDTO.setQualityClassification3("qualityClassification3");
        complaintFollowDTO.setCcm(false);
        complaintFollowDTO.setQualityClassification4(0);
        complaintFollowDTO.setFaultClassification("faultClassification");
        complaintFollowDTO.setRemark1("remark1");
        complaintFollowDTO.setRemark2("remark2");
        complaintFollowDTO.setRemark3("remark3");
        complaintFollowDTO.setRemark4("remark4");

        // Configure ComplaintFollowMapper.selectPageBySql(...).
        final ComplaintFollowPO complaintFollowPO = new ComplaintFollowPO();
        complaintFollowPO.setDealerNotPublish(false);
        complaintFollowPO.setCcm(false);
        complaintFollowPO.setOwnerCode("ownerCode");
        complaintFollowPO.setFollowerName("followerName");
        complaintFollowPO.setOrgId(0);
        final List<ComplaintFollowPO> complaintFollowPOS = Arrays.asList(complaintFollowPO);
        when(mockComplaintFollowMapper.selectPageBySql(any(Page.class), any(ComplaintFollowPO.class)))
                .thenReturn(complaintFollowPOS);

        // Run the test
        final IPage<ComplaintFollowDTO> result = complaintFollowServiceImplUnderTest.selectPageBysql(page,
                complaintFollowDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintFollowMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
        complaintFollowDTO.setDealerNotPublish(false);
        complaintFollowDTO.setFollowerName("followerName");
        complaintFollowDTO.setComplaintInfoId(0L);
        complaintFollowDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setFollower("follower");
        complaintFollowDTO.setFollowContent("followContent");
        complaintFollowDTO.setIsCcmNotPublish(false);
        complaintFollowDTO.setCcmSubject("ccmSubject");
        complaintFollowDTO.setStatus("status");
        complaintFollowDTO.setAdvise(0);
        complaintFollowDTO.setCcmPart("ccmPart");
        complaintFollowDTO.setCcmSubdivisionPart("ccmSubdivisionPart");
        complaintFollowDTO.setCcMainReason("ccMainReason");
        complaintFollowDTO.setCcResult("ccResult");
        complaintFollowDTO.setKeyword("keyword");
        complaintFollowDTO.setClassification1(0);
        complaintFollowDTO.setClassification2("classification2");
        complaintFollowDTO.setClassification3("classification3");
        complaintFollowDTO.setClassification4("classification4");
        complaintFollowDTO.setClassification5("classification5");
        complaintFollowDTO.setClassification6("classification6");
        complaintFollowDTO.setPlanFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setActuallFollowTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setDataSources(0);
        complaintFollowDTO.setQualityClassification1(0);
        complaintFollowDTO.setQualityClassification2(0);
        complaintFollowDTO.setQualityClassification3("qualityClassification3");
        complaintFollowDTO.setCcm(false);
        complaintFollowDTO.setQualityClassification4(0);
        complaintFollowDTO.setFaultClassification("faultClassification");
        complaintFollowDTO.setRemark1("remark1");
        complaintFollowDTO.setRemark2("remark2");
        complaintFollowDTO.setRemark3("remark3");
        complaintFollowDTO.setRemark4("remark4");

        when(mockComplaintFollowMapper.selectPageBySql(any(Page.class), any(ComplaintFollowPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintFollowDTO> result = complaintFollowServiceImplUnderTest.selectPageBysql(page,
                complaintFollowDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintFollowMapperSelectListByVcdcReturnsNoItems() {
        // Setup
        final ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
        complaintFollowDTO.setDealerNotPublish(false);
        complaintFollowDTO.setFollowerName("followerName");
        complaintFollowDTO.setComplaintInfoId(0L);
        complaintFollowDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setFollower("follower");
        complaintFollowDTO.setFollowContent("followContent");
        complaintFollowDTO.setIsCcmNotPublish(false);
        complaintFollowDTO.setCcmSubject("ccmSubject");
        complaintFollowDTO.setStatus("status");
        complaintFollowDTO.setAdvise(0);
        complaintFollowDTO.setCcmPart("ccmPart");
        complaintFollowDTO.setCcmSubdivisionPart("ccmSubdivisionPart");
        complaintFollowDTO.setCcMainReason("ccMainReason");
        complaintFollowDTO.setCcResult("ccResult");
        complaintFollowDTO.setKeyword("keyword");
        complaintFollowDTO.setClassification1(0);
        complaintFollowDTO.setClassification2("classification2");
        complaintFollowDTO.setClassification3("classification3");
        complaintFollowDTO.setClassification4("classification4");
        complaintFollowDTO.setClassification5("classification5");
        complaintFollowDTO.setClassification6("classification6");
        complaintFollowDTO.setPlanFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setActuallFollowTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setDataSources(0);
        complaintFollowDTO.setQualityClassification1(0);
        complaintFollowDTO.setQualityClassification2(0);
        complaintFollowDTO.setQualityClassification3("qualityClassification3");
        complaintFollowDTO.setCcm(false);
        complaintFollowDTO.setQualityClassification4(0);
        complaintFollowDTO.setFaultClassification("faultClassification");
        complaintFollowDTO.setRemark1("remark1");
        complaintFollowDTO.setRemark2("remark2");
        complaintFollowDTO.setRemark3("remark3");
        complaintFollowDTO.setRemark4("remark4");

        when(mockComplaintFollowMapper.selectListByVcdc(any(ComplaintFollowPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintFollowDTO> result = complaintFollowServiceImplUnderTest.selectListBySql("flag",
                complaintFollowDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure ComplaintFollowMapper.selectById(...).
        final ComplaintFollowPO complaintFollowPO = new ComplaintFollowPO();
        complaintFollowPO.setDealerNotPublish(false);
        complaintFollowPO.setCcm(false);
        complaintFollowPO.setOwnerCode("ownerCode");
        complaintFollowPO.setFollowerName("followerName");
        complaintFollowPO.setOrgId(0);
        when(mockComplaintFollowMapper.selectById(0L)).thenReturn(complaintFollowPO);

        // Run the test
        final ComplaintFollowDTO result = complaintFollowServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintFollowMapperReturnsNull() {
        // Setup
        when(mockComplaintFollowMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintFollowServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
        complaintFollowDTO.setDealerNotPublish(false);
        complaintFollowDTO.setFollowerName("followerName");
        complaintFollowDTO.setComplaintInfoId(0L);
        complaintFollowDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setFollower("follower");
        complaintFollowDTO.setFollowContent("followContent");
        complaintFollowDTO.setIsCcmNotPublish(false);
        complaintFollowDTO.setCcmSubject("ccmSubject");
        complaintFollowDTO.setStatus("status");
        complaintFollowDTO.setAdvise(0);
        complaintFollowDTO.setCcmPart("ccmPart");
        complaintFollowDTO.setCcmSubdivisionPart("ccmSubdivisionPart");
        complaintFollowDTO.setCcMainReason("ccMainReason");
        complaintFollowDTO.setCcResult("ccResult");
        complaintFollowDTO.setKeyword("keyword");
        complaintFollowDTO.setClassification1(0);
        complaintFollowDTO.setClassification2("classification2");
        complaintFollowDTO.setClassification3("classification3");
        complaintFollowDTO.setClassification4("classification4");
        complaintFollowDTO.setClassification5("classification5");
        complaintFollowDTO.setClassification6("classification6");
        complaintFollowDTO.setPlanFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setActuallFollowTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setDataSources(0);
        complaintFollowDTO.setQualityClassification1(0);
        complaintFollowDTO.setQualityClassification2(0);
        complaintFollowDTO.setQualityClassification3("qualityClassification3");
        complaintFollowDTO.setCcm(false);
        complaintFollowDTO.setQualityClassification4(0);
        complaintFollowDTO.setFaultClassification("faultClassification");
        complaintFollowDTO.setRemark1("remark1");
        complaintFollowDTO.setRemark2("remark2");
        complaintFollowDTO.setRemark3("remark3");
        complaintFollowDTO.setRemark4("remark4");

        when(mockComplaintFollowMapper.insert(any(ComplaintFollowPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintFollowServiceImplUnderTest.insert(complaintFollowDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
        complaintFollowDTO.setDealerNotPublish(false);
        complaintFollowDTO.setFollowerName("followerName");
        complaintFollowDTO.setComplaintInfoId(0L);
        complaintFollowDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setFollower("follower");
        complaintFollowDTO.setFollowContent("followContent");
        complaintFollowDTO.setIsCcmNotPublish(false);
        complaintFollowDTO.setCcmSubject("ccmSubject");
        complaintFollowDTO.setStatus("status");
        complaintFollowDTO.setAdvise(0);
        complaintFollowDTO.setCcmPart("ccmPart");
        complaintFollowDTO.setCcmSubdivisionPart("ccmSubdivisionPart");
        complaintFollowDTO.setCcMainReason("ccMainReason");
        complaintFollowDTO.setCcResult("ccResult");
        complaintFollowDTO.setKeyword("keyword");
        complaintFollowDTO.setClassification1(0);
        complaintFollowDTO.setClassification2("classification2");
        complaintFollowDTO.setClassification3("classification3");
        complaintFollowDTO.setClassification4("classification4");
        complaintFollowDTO.setClassification5("classification5");
        complaintFollowDTO.setClassification6("classification6");
        complaintFollowDTO.setPlanFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setActuallFollowTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setDataSources(0);
        complaintFollowDTO.setQualityClassification1(0);
        complaintFollowDTO.setQualityClassification2(0);
        complaintFollowDTO.setQualityClassification3("qualityClassification3");
        complaintFollowDTO.setCcm(false);
        complaintFollowDTO.setQualityClassification4(0);
        complaintFollowDTO.setFaultClassification("faultClassification");
        complaintFollowDTO.setRemark1("remark1");
        complaintFollowDTO.setRemark2("remark2");
        complaintFollowDTO.setRemark3("remark3");
        complaintFollowDTO.setRemark4("remark4");

        // Configure ComplaintFollowMapper.selectById(...).
        final ComplaintFollowPO complaintFollowPO = new ComplaintFollowPO();
        complaintFollowPO.setDealerNotPublish(false);
        complaintFollowPO.setCcm(false);
        complaintFollowPO.setOwnerCode("ownerCode");
        complaintFollowPO.setFollowerName("followerName");
        complaintFollowPO.setOrgId(0);
        when(mockComplaintFollowMapper.selectById(0L)).thenReturn(complaintFollowPO);

        when(mockComplaintFollowMapper.updateById(any(ComplaintFollowPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintFollowServiceImplUnderTest.update(0L, complaintFollowDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testQueryNewCus() {
        // Setup
        final ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
        complaintFollowDTO.setDealerNotPublish(false);
        complaintFollowDTO.setFollowerName("followerName");
        complaintFollowDTO.setComplaintInfoId(0L);
        complaintFollowDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setFollower("follower");
        complaintFollowDTO.setFollowContent("followContent");
        complaintFollowDTO.setIsCcmNotPublish(false);
        complaintFollowDTO.setCcmSubject("ccmSubject");
        complaintFollowDTO.setStatus("status");
        complaintFollowDTO.setAdvise(0);
        complaintFollowDTO.setCcmPart("ccmPart");
        complaintFollowDTO.setCcmSubdivisionPart("ccmSubdivisionPart");
        complaintFollowDTO.setCcMainReason("ccMainReason");
        complaintFollowDTO.setCcResult("ccResult");
        complaintFollowDTO.setKeyword("keyword");
        complaintFollowDTO.setClassification1(0);
        complaintFollowDTO.setClassification2("classification2");
        complaintFollowDTO.setClassification3("classification3");
        complaintFollowDTO.setClassification4("classification4");
        complaintFollowDTO.setClassification5("classification5");
        complaintFollowDTO.setClassification6("classification6");
        complaintFollowDTO.setPlanFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setActuallFollowTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setDataSources(0);
        complaintFollowDTO.setQualityClassification1(0);
        complaintFollowDTO.setQualityClassification2(0);
        complaintFollowDTO.setQualityClassification3("qualityClassification3");
        complaintFollowDTO.setCcm(false);
        complaintFollowDTO.setQualityClassification4(0);
        complaintFollowDTO.setFaultClassification("faultClassification");
        complaintFollowDTO.setRemark1("remark1");
        complaintFollowDTO.setRemark2("remark2");
        complaintFollowDTO.setRemark3("remark3");
        complaintFollowDTO.setRemark4("remark4");

        // Configure ComplaintFollowMapper.queryNewCus(...).
        final ComplaintFollowPO complaintFollowPO = new ComplaintFollowPO();
        complaintFollowPO.setDealerNotPublish(false);
        complaintFollowPO.setCcm(false);
        complaintFollowPO.setOwnerCode("ownerCode");
        complaintFollowPO.setFollowerName("followerName");
        complaintFollowPO.setOrgId(0);
        final List<ComplaintFollowPO> complaintFollowPOS = Arrays.asList(complaintFollowPO);
        when(mockComplaintFollowMapper.queryNewCus(any(ComplaintFollowPO.class))).thenReturn(complaintFollowPOS);

        // Run the test
        final List<ComplaintFollowDTO> result = complaintFollowServiceImplUnderTest.queryNewCus(complaintFollowDTO);

        // Verify the results
    }

    @Test
    void testQueryNewCus_ComplaintFollowMapperReturnsNoItems() {
        // Setup
        final ComplaintFollowDTO complaintFollowDTO = new ComplaintFollowDTO();
        complaintFollowDTO.setDealerNotPublish(false);
        complaintFollowDTO.setFollowerName("followerName");
        complaintFollowDTO.setComplaintInfoId(0L);
        complaintFollowDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setFollower("follower");
        complaintFollowDTO.setFollowContent("followContent");
        complaintFollowDTO.setIsCcmNotPublish(false);
        complaintFollowDTO.setCcmSubject("ccmSubject");
        complaintFollowDTO.setStatus("status");
        complaintFollowDTO.setAdvise(0);
        complaintFollowDTO.setCcmPart("ccmPart");
        complaintFollowDTO.setCcmSubdivisionPart("ccmSubdivisionPart");
        complaintFollowDTO.setCcMainReason("ccMainReason");
        complaintFollowDTO.setCcResult("ccResult");
        complaintFollowDTO.setKeyword("keyword");
        complaintFollowDTO.setClassification1(0);
        complaintFollowDTO.setClassification2("classification2");
        complaintFollowDTO.setClassification3("classification3");
        complaintFollowDTO.setClassification4("classification4");
        complaintFollowDTO.setClassification5("classification5");
        complaintFollowDTO.setClassification6("classification6");
        complaintFollowDTO.setPlanFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setActuallFollowTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintFollowDTO.setDataSources(0);
        complaintFollowDTO.setQualityClassification1(0);
        complaintFollowDTO.setQualityClassification2(0);
        complaintFollowDTO.setQualityClassification3("qualityClassification3");
        complaintFollowDTO.setCcm(false);
        complaintFollowDTO.setQualityClassification4(0);
        complaintFollowDTO.setFaultClassification("faultClassification");
        complaintFollowDTO.setRemark1("remark1");
        complaintFollowDTO.setRemark2("remark2");
        complaintFollowDTO.setRemark3("remark3");
        complaintFollowDTO.setRemark4("remark4");

        when(mockComplaintFollowMapper.queryNewCus(any(ComplaintFollowPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintFollowDTO> result = complaintFollowServiceImplUnderTest.queryNewCus(complaintFollowDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testQueryNextFollowing() {
        // Setup
        // Configure ComplaintFollowMapper.queryNextFollowing(...).
        final ComplaintInfMoreDTO complaintInfMoreDTO = new ComplaintInfMoreDTO();
        complaintInfMoreDTO.setRiskType("riskType");
        complaintInfMoreDTO.setCaseCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        complaintInfMoreDTO.setInvalidCaseHidden(0);
        complaintInfMoreDTO.setWorkOrderStatusData("workOrderStatusData");
        complaintInfMoreDTO.setImportanceLevelData("importanceLevelData");
        final List<ComplaintInfMoreDTO> complaintInfMoreDTOS = Arrays.asList(complaintInfMoreDTO);
        when(mockComplaintFollowMapper.queryNextFollowing()).thenReturn(complaintInfMoreDTOS);

        // Run the test
        final List<ComplaintInfMoreDTO> result = complaintFollowServiceImplUnderTest.queryNextFollowing();

        // Verify the results
    }

    @Test
    void testQueryNextFollowing_ComplaintFollowMapperReturnsNoItems() {
        // Setup
        when(mockComplaintFollowMapper.queryNextFollowing()).thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintInfMoreDTO> result = complaintFollowServiceImplUnderTest.queryNextFollowing();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
