package com.yonyou.dmscus.customer.service.impl.inviteManageVCDC;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dao.common.VehicleMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordDetailMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.CallDetailsMapper;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.TmVehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteManageVCDC.AllocateDealerDTO;
import com.yonyou.dmscus.customer.entity.po.common.VehiclePO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordDetailPO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.voicemanage.CallDetailsService;
import com.yonyou.dmscus.customer.util.common.ExcelGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteManageVCDCServiceImplTest {

    @Mock
    private InviteVehicleRecordMapper mockInviteVehicleRecordMapper;
    @Mock
    private InviteVehicleRecordDetailMapper mockInviteVehicleRecordDetailMapper;
    @Mock
    private BusinessPlatformService mockBusinessPlatformService;
    @Mock
    private VehicleMapper mockVehicleMapper;
    @Mock
    private CallDetailsMapper mockCallDetailsMapper;
    @Mock
    private CallDetailsService mockCallDetailsService;

    private InviteManageVCDCServiceImpl inviteManageVCDCServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteManageVCDCServiceImplUnderTest = new InviteManageVCDCServiceImpl();
        inviteManageVCDCServiceImplUnderTest.inviteVehicleRecordMapper = mockInviteVehicleRecordMapper;
        inviteManageVCDCServiceImplUnderTest.inviteVehicleRecordDetailMapper = mockInviteVehicleRecordDetailMapper;
        inviteManageVCDCServiceImplUnderTest.businessPlatformService = mockBusinessPlatformService;
        inviteManageVCDCServiceImplUnderTest.vehicleMapper = mockVehicleMapper;
        inviteManageVCDCServiceImplUnderTest.callDetailsMapper = mockCallDetailsMapper;
        inviteManageVCDCServiceImplUnderTest.callDetailsService = mockCallDetailsService;
    }

    @Test
    void testGetInviteVehicleRecordInfoDlr_InviteVehicleRecordDetailMapperReturnsNoItems() {
        // Setup
        when(mockInviteVehicleRecordDetailMapper.getInviteVehicleRecordInfoDlr(0L, "ownerCode"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteVehicleRecordDetailDTO> result = inviteManageVCDCServiceImplUnderTest.getInviteVehicleRecordInfoDlr(
                "vin", 0L, "ownerCode");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetInviteVehicleRecordInfoHistoryDlr() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);

        // Configure InviteVehicleRecordDetailMapper.getInviteVehicleRecordInfoHistoryDlr(...).
        final InviteVehicleRecordDetailPO inviteVehicleRecordDetailPO = new InviteVehicleRecordDetailPO();
        inviteVehicleRecordDetailPO.setOwnerCode("ownerCode");
        inviteVehicleRecordDetailPO.setOrgId(0);
        inviteVehicleRecordDetailPO.setId(0L);
        inviteVehicleRecordDetailPO.setInviteId(0L);
        inviteVehicleRecordDetailPO.setContent("content");
        final List<InviteVehicleRecordDetailPO> inviteVehicleRecordDetailPOS = Arrays.asList(
                inviteVehicleRecordDetailPO);
        when(mockInviteVehicleRecordDetailMapper.getInviteVehicleRecordInfoHistoryDlr(any(Page.class), eq("vin"),
                eq("ownerCode"))).thenReturn(inviteVehicleRecordDetailPOS);

        // Run the test
        final IPage<InviteVehicleRecordDetailDTO> result = inviteManageVCDCServiceImplUnderTest.getInviteVehicleRecordInfoHistoryDlr(
                page, "vin", "ownerCode");

        // Verify the results
    }

    @Test
    void testGetInviteVehicleRecordInfoHistoryDlr_InviteVehicleRecordDetailMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        when(mockInviteVehicleRecordDetailMapper.getInviteVehicleRecordInfoHistoryDlr(any(Page.class), eq("vin"),
                eq("ownerCode"))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<InviteVehicleRecordDetailDTO> result = inviteManageVCDCServiceImplUnderTest.getInviteVehicleRecordInfoHistoryDlr(
                page, "vin", "ownerCode");

        // Verify the results
    }
}
