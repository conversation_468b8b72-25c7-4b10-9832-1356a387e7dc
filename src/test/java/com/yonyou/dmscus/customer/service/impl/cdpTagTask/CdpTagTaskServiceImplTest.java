package com.yonyou.dmscus.customer.service.impl.cdpTagTask;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.configuration.CdpUrlProperties;
import com.yonyou.dmscus.customer.dao.voc.CdpTagTaskMapper;
import com.yonyou.dmscus.customer.dto.cdp.CdpTagTaskParameterDto;
import com.yonyou.dmscus.customer.dto.cdp.TokenPortraitDto;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.CdpTagTaskPo;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocInviteVehicleTaskRecordService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CdpTagTaskServiceImplTest {

    @Mock
    private RestTemplate mockCdpRestTemplate;
    @Mock
    private InviteVehicleRecordService mockInviteVehicleRecordService;
    @Mock
    private CdpTagTaskMapper mockCdpTagTaskMapper;
    @Mock
    private VocInviteVehicleTaskRecordService mockVocInviteVehicleTaskRecordService;
    @Mock
    private CdpUrlProperties mockCdpUrlProperties;

    @InjectMocks
    private CdpTagTaskServiceImpl cdpTagTaskServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        cdpTagTaskServiceImplUnderTest.inviteVehicleRecordService = mockInviteVehicleRecordService;
        cdpTagTaskServiceImplUnderTest.cdpTagTaskMapper = mockCdpTagTaskMapper;
        cdpTagTaskServiceImplUnderTest.vocInviteVehicleTaskRecordService = mockVocInviteVehicleTaskRecordService;
    }

    @Test
    void testInsertCdpTagTask() {
        // Setup
        // Run the test
        cdpTagTaskServiceImplUnderTest.insertCdpTagTask(Arrays.asList("value"));

        // Verify the results
        verify(mockCdpTagTaskMapper).insertCdpTagTask(Arrays.asList("value"));
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        when(mockCdpTagTaskMapper.selectInviteStatusPlan(any(Page.class), eq(Arrays.asList("value")), eq("startTime"),
                eq("endTime"))).thenReturn(Arrays.asList("value"));

        // Run the test
        final IPage<String> result = cdpTagTaskServiceImplUnderTest.selectPageBysql(page, Arrays.asList("value"),
                "startTime", "endTime");

        // Verify the results
    }

    @Test
    void testSelectPageBysql_CdpTagTaskMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        when(mockCdpTagTaskMapper.selectInviteStatusPlan(any(Page.class), eq(Arrays.asList("value")), eq("startTime"),
                eq("endTime"))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<String> result = cdpTagTaskServiceImplUnderTest.selectPageBysql(page, Arrays.asList("value"),
                "startTime", "endTime");

        // Verify the results
    }

    @Test
    void testSelectTaskList() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        when(mockCdpTagTaskMapper.selectTaskList(any(Page.class), eq("startTime"), eq("endTime")))
                .thenReturn(Arrays.asList("value"));

        // Run the test
        final IPage<String> result = cdpTagTaskServiceImplUnderTest.selectTaskList(page, "startTime", "endTime");

        // Verify the results
    }

    @Test
    void testSelectTaskList_CdpTagTaskMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        when(mockCdpTagTaskMapper.selectTaskList(any(Page.class), eq("startTime"), eq("endTime")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final IPage<String> result = cdpTagTaskServiceImplUnderTest.selectTaskList(page, "startTime", "endTime");

        // Verify the results
    }

    @Test
    void testUpdateDailyMileAdviseInDate2() {
        // Setup
        final CdpTagTaskParameterDto cdpTagTaskParameterDto = new CdpTagTaskParameterDto();
        cdpTagTaskParameterDto.setVin("vin");
        cdpTagTaskParameterDto.setDailyMile("dailyMile");
        cdpTagTaskParameterDto.setAdviseInDate("adviseInDate");
        cdpTagTaskParameterDto.setReturnIntentionLevel("returnIntentionLevel");
        cdpTagTaskParameterDto.setWhiteList(Arrays.asList("value"));
        final List<CdpTagTaskParameterDto> list = Arrays.asList(cdpTagTaskParameterDto);

        // Run the test
        cdpTagTaskServiceImplUnderTest.updateDailyMileAdviseInDate(list);

        // Verify the results
        // Confirm VocInviteVehicleTaskRecordService.updateDailyMile(...).
        final CdpTagTaskParameterDto cdpTagTaskParameterDto1 = new CdpTagTaskParameterDto();
        cdpTagTaskParameterDto1.setVin("vin");
        cdpTagTaskParameterDto1.setDailyMile("dailyMile");
        cdpTagTaskParameterDto1.setAdviseInDate("adviseInDate");
        cdpTagTaskParameterDto1.setReturnIntentionLevel("returnIntentionLevel");
        cdpTagTaskParameterDto1.setWhiteList(Arrays.asList("value"));
        final List<CdpTagTaskParameterDto> list1 = Arrays.asList(cdpTagTaskParameterDto1);
        verify(mockVocInviteVehicleTaskRecordService).updateDailyMile(list1);

        // Confirm InviteVehicleRecordService.updateAdviseInDate(...).
        final CdpTagTaskParameterDto cdpTagTaskParameterDto2 = new CdpTagTaskParameterDto();
        cdpTagTaskParameterDto2.setVin("vin");
        cdpTagTaskParameterDto2.setDailyMile("dailyMile");
        cdpTagTaskParameterDto2.setAdviseInDate("adviseInDate");
        cdpTagTaskParameterDto2.setReturnIntentionLevel("returnIntentionLevel");
        cdpTagTaskParameterDto2.setWhiteList(Arrays.asList("value"));
        final List<CdpTagTaskParameterDto> list2 = Arrays.asList(cdpTagTaskParameterDto2);
        verify(mockInviteVehicleRecordService).updateAdviseInDate(list2);

        // Confirm CdpTagTaskMapper.updateCdpTagTask(...).
        final CdpTagTaskParameterDto cdpTagTaskParameterDto3 = new CdpTagTaskParameterDto();
        cdpTagTaskParameterDto3.setVin("vin");
        cdpTagTaskParameterDto3.setDailyMile("dailyMile");
        cdpTagTaskParameterDto3.setAdviseInDate("adviseInDate");
        cdpTagTaskParameterDto3.setReturnIntentionLevel("returnIntentionLevel");
        cdpTagTaskParameterDto3.setWhiteList(Arrays.asList("value"));
        final List<CdpTagTaskParameterDto> vinList = Arrays.asList(cdpTagTaskParameterDto3);
        verify(mockCdpTagTaskMapper).updateCdpTagTask(vinList);
    }

    @Test
    void testBatchInsertHighlightFlagClueId() {
        // Setup
        // Run the test
        cdpTagTaskServiceImplUnderTest.batchInsertHighlightFlagClueId(Arrays.asList(0L));

        // Verify the results
        verify(mockCdpTagTaskMapper).batchInsertHighlightFlagClueId(Arrays.asList(0L));
    }

    @Test
    void testQueryFaultLightClueTotal() {
        // Setup
        when(mockCdpTagTaskMapper.queryFaultLightClueTotal()).thenReturn(0);

        // Run the test
        final Integer result = cdpTagTaskServiceImplUnderTest.queryFaultLightClueTotal();

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testQueryFaultLightClueId() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final CdpTagTaskPo cdpTagTaskPo = new CdpTagTaskPo();
        cdpTagTaskPo.setSinceType(0);
        cdpTagTaskPo.setBizNo("bizNo");
        cdpTagTaskPo.setReqParams("reqParams");
        cdpTagTaskPo.setRetryCount(0);
        cdpTagTaskPo.setLastRetryTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CdpTagTaskPo> expectedResult = Arrays.asList(cdpTagTaskPo);

        // Configure CdpTagTaskMapper.queryFaultLightClueId(...).
        final CdpTagTaskPo cdpTagTaskPo1 = new CdpTagTaskPo();
        cdpTagTaskPo1.setSinceType(0);
        cdpTagTaskPo1.setBizNo("bizNo");
        cdpTagTaskPo1.setReqParams("reqParams");
        cdpTagTaskPo1.setRetryCount(0);
        cdpTagTaskPo1.setLastRetryTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CdpTagTaskPo> cdpTagTaskPos = Arrays.asList(cdpTagTaskPo1);
        when(mockCdpTagTaskMapper.queryFaultLightClueId(any(Page.class))).thenReturn(cdpTagTaskPos);

        // Run the test
        final List<CdpTagTaskPo> result = cdpTagTaskServiceImplUnderTest.queryFaultLightClueId(page);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryFaultLightClueId_CdpTagTaskMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        when(mockCdpTagTaskMapper.queryFaultLightClueId(any(Page.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<CdpTagTaskPo> result = cdpTagTaskServiceImplUnderTest.queryFaultLightClueId(page);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testUpdateFaultLightClueById() {
        // Setup
        // Run the test
        cdpTagTaskServiceImplUnderTest.updateFaultLightClueById(Arrays.asList(0L));

        // Verify the results
        verify(mockCdpTagTaskMapper).updateFaultLightClueById(Arrays.asList(0L));
    }

    @Test
    void testUpdateErrorTask() {
        // Setup
        final CdpTagTaskPo cdpTagTaskPo = new CdpTagTaskPo();
        cdpTagTaskPo.setSinceType(0);
        cdpTagTaskPo.setBizNo("bizNo");
        cdpTagTaskPo.setReqParams("reqParams");
        cdpTagTaskPo.setRetryCount(0);
        cdpTagTaskPo.setLastRetryTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CdpTagTaskPo> cdpTagTaskPos = Arrays.asList(cdpTagTaskPo);

        // Run the test
        cdpTagTaskServiceImplUnderTest.updateErrorTask(cdpTagTaskPos);

        // Verify the results
        // Confirm CdpTagTaskMapper.updateErrorTask(...).
        final CdpTagTaskPo cdpTagTaskPo1 = new CdpTagTaskPo();
        cdpTagTaskPo1.setSinceType(0);
        cdpTagTaskPo1.setBizNo("bizNo");
        cdpTagTaskPo1.setReqParams("reqParams");
        cdpTagTaskPo1.setRetryCount(0);
        cdpTagTaskPo1.setLastRetryTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CdpTagTaskPo> cdpTagTaskPos1 = Arrays.asList(cdpTagTaskPo1);
        verify(mockCdpTagTaskMapper).updateErrorTask(cdpTagTaskPos1);
    }
}
