package com.yonyou.dmscus.customer.service.impl.voicemanage;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.voicemanage.CallDetailsMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.CallScoreItemsMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.SaCustomerNumberMapper;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.CallDetailsDTO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO;
import com.yonyou.dmscus.customer.service.common.HttpLogService;
import com.yonyou.dmscus.customer.utils.ai.AnalysisScoreItems;
import com.yonyou.dmscus.customer.utils.ai.AnalysisScoreItemsZj;
import com.yonyou.dmscus.customer.utils.ai.CallDetailsChild;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CallDetailsServiceImplTest {

    @Mock
    private CallDetailsMapper mockCallDetailsMapper;
    @Mock
    private SaCustomerNumberMapper mockSaCustomerNumberMapper;
    @Mock
    private CallScoreItemsMapper mockCallScoreItemsMapper;
    @Mock
    private HttpLogService mockHttpLogService;

    private CallDetailsServiceImpl callDetailsServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        callDetailsServiceImplUnderTest = new CallDetailsServiceImpl();
        callDetailsServiceImplUnderTest.callDetailsMapper = mockCallDetailsMapper;
        callDetailsServiceImplUnderTest.saCustomerNumberMapper = mockSaCustomerNumberMapper;
        callDetailsServiceImplUnderTest.callScoreItemsMapper = mockCallScoreItemsMapper;
        callDetailsServiceImplUnderTest.URL_MAPPING_SESSION = "URL_MAPPING_SESSION";
        callDetailsServiceImplUnderTest.serverUrl = "serverUrl";
        callDetailsServiceImplUnderTest.appKey = "appKey";
        callDetailsServiceImplUnderTest.appSecret = "appSecret";
        callDetailsServiceImplUnderTest.voiceUrl = "voiceUrl";
        callDetailsServiceImplUnderTest.accessKey = "accessKey";
        callDetailsServiceImplUnderTest.accessSecret = "accessSecret";
        callDetailsServiceImplUnderTest.zijieServerUrl = "zijieServerUrl";
        callDetailsServiceImplUnderTest.zijieAppKey = "zijieAppKey";
        callDetailsServiceImplUnderTest.zijievoiceUrl = "voiceUrl";
        callDetailsServiceImplUnderTest.zijievoiceUser = "zijievoiceUser";
        callDetailsServiceImplUnderTest.zijievoiceToken = "zijievoiceToken";
        callDetailsServiceImplUnderTest.zijieoldtime = "zijieoldtime";
        callDetailsServiceImplUnderTest.httpLogService = mockHttpLogService;
    }

    @Test
    void testGetUrlParamsByMap() {
        // Setup
        final Map<String, Object> map = new HashMap<>();

        // Run the test
        final String result = CallDetailsServiceImpl.getUrlParamsByMap(map);

        // Verify the results
        assertThat(result).isEqualTo("");
    }

    @Test
    void testGetById() {
        // Setup
        final CallDetailsDTO expectedResult = new CallDetailsDTO();
        expectedResult.setAppId("appId");
        expectedResult.setOwnerCode("ownerCode");
        expectedResult.setOwnerParCode("ownerParCode");
        expectedResult.setOrgId(0);
        expectedResult.setId(0L);

        // Configure CallDetailsMapper.selectById(...).
        final CallDetailsPO callDetailsPO = new CallDetailsPO();
        callDetailsPO.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        callDetailsPO.setId(0L);
        callDetailsPO.setSessionId("sessionId");
        callDetailsPO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        callDetailsPO.setTotalScore(0);
        when(mockCallDetailsMapper.selectById(0L)).thenReturn(callDetailsPO);

        // Run the test
        final CallDetailsDTO result = callDetailsServiceImplUnderTest.getById(0L);

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetById_CallDetailsMapperReturnsNull() {
        // Setup
        when(mockCallDetailsMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> callDetailsServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testGetCallDetails() {
        // Setup
        final CallDetailsDTO callDetailsDTO = new CallDetailsDTO();
        callDetailsDTO.setAppId("appId");
        callDetailsDTO.setOwnerCode("ownerCode");
        callDetailsDTO.setOwnerParCode("ownerParCode");
        callDetailsDTO.setOrgId(0);
        callDetailsDTO.setId(0L);
        final List<CallDetailsDTO> expectedResult = Arrays.asList(callDetailsDTO);

        // Configure CallDetailsMapper.getCallDetails(...).
        final CallDetailsPO callDetailsPO = new CallDetailsPO();
        callDetailsPO.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        callDetailsPO.setId(0L);
        callDetailsPO.setSessionId("sessionId");
        callDetailsPO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        callDetailsPO.setTotalScore(0);
        final List<CallDetailsPO> callDetailsPOS = Arrays.asList(callDetailsPO);
        when(mockCallDetailsMapper.getCallDetails("detailId")).thenReturn(callDetailsPOS);

        // Run the test
        final List<CallDetailsDTO> result = callDetailsServiceImplUnderTest.getCallDetails("detailId");

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetCallDetails_CallDetailsMapperReturnsNoItems() {
        // Setup
        when(mockCallDetailsMapper.getCallDetails("detailId")).thenReturn(Collections.emptyList());

        // Run the test
        final List<CallDetailsDTO> result = callDetailsServiceImplUnderTest.getCallDetails("detailId");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetCallDetailsByAi_SaCustomerNumberMapperReturnsNoItems() {
        // Setup
        when(mockSaCustomerNumberMapper.selectYesterdayAll()).thenReturn(Collections.emptyList());

        // Run the test
        callDetailsServiceImplUnderTest.getCallDetailsByAi();

        // Verify the results
        // Confirm CallDetailsMapper.insertCallDetails(...).
        final CallDetailsChild callDetailsChild = new CallDetailsChild();
        callDetailsChild.setStartTime(0L);
        callDetailsChild.setEndTime(0L);
        callDetailsChild.setCallId("callId");
        callDetailsChild.setOwnerCode("ownerCode");
        callDetailsChild.setOwnerParCode("ownerParCode");
        callDetailsChild.setDealerCode("dealerCode");
        callDetailsChild.setStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        callDetailsChild.setEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CallDetailsChild> allDetail = Arrays.asList(callDetailsChild);
//        verify(mockCallDetailsMapper)
        mockCallDetailsMapper.insertCallDetails(allDetail);
    }

    @Test
    void testSendSouche() {
        // Setup
        // Run the test
        final String result = callDetailsServiceImplUnderTest.sendSouche("sessionId", "serviceId",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
//        assertThat(result).isEqualTo("data");
    }

    @Test
    void testSendSouchezj() {
        // Setup
        // Run the test
        final String result = callDetailsServiceImplUnderTest.sendSouchezj("sessionId", "serviceId",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
//        assertThat(result).isEqualTo("result");
//        verify(mockHttpLogService)
        mockHttpLogService.saveHttpLog("字节-获取总分", "requestUrl", "", "POST", "500", "message");
    }
}
