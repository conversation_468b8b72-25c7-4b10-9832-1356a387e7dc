package com.yonyou.dmscus.customer.service.common.businessPlatform.dto;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class VehicleOwnerSelectByIdListDTOTest {

    private VehicleOwnerSelectByIdListDTO vehicleOwnerSelectByIdListDTOUnderTest;

    @BeforeEach
    void setUp() {
        vehicleOwnerSelectByIdListDTOUnderTest = new VehicleOwnerSelectByIdListDTO();
    }

    @Test
    void testListGetterAndSetter() {
        final List<Long> list = Arrays.asList(0L);
        vehicleOwnerSelectByIdListDTOUnderTest.setList(list);
        assertThat(vehicleOwnerSelectByIdListDTOUnderTest.getList()).isEqualTo(list);
    }

    @Test
    void testToString() {
        assertThat(vehicleOwnerSelectByIdListDTOUnderTest.toString()).isEqualTo("result");
    }
}
