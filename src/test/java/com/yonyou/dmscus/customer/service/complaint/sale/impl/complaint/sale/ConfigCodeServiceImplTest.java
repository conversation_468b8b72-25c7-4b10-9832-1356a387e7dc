package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yonyou.dmscus.customer.dao.complaint.CommonConfigMapper;
import com.yonyou.dmscus.customer.entity.po.complaint.CommonConfigPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ConfigCodeServiceImplTest {

    @Mock
    private CommonConfigMapper mockCommonConfigMapper;

    private ConfigCodeServiceImpl configCodeServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        configCodeServiceImplUnderTest = new ConfigCodeServiceImpl(mockCommonConfigMapper);
    }

    @Test
    void testGetByConfigCode() {
        // Setup
        final CommonConfigPO commonConfigPO = new CommonConfigPO();
        commonConfigPO.setId(0L);
        commonConfigPO.setConfigCode("configCode");
        commonConfigPO.setGroupType("groupType");
        commonConfigPO.setConfigKey("configKey");
        commonConfigPO.setIsDeleted(0);
        final List<CommonConfigPO> expectedResult = Arrays.asList(commonConfigPO);

        // Configure CommonConfigMapper.selectList(...).
        final CommonConfigPO commonConfigPO1 = new CommonConfigPO();
        commonConfigPO1.setId(0L);
        commonConfigPO1.setConfigCode("configCode");
        commonConfigPO1.setGroupType("groupType");
        commonConfigPO1.setConfigKey("configKey");
        commonConfigPO1.setIsDeleted(0);
        final List<CommonConfigPO> commonConfigPOS = Arrays.asList(commonConfigPO1);
        when(mockCommonConfigMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(commonConfigPOS);

        // Run the test
        final List<CommonConfigPO> result = configCodeServiceImplUnderTest.getByConfigCode("configCode");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetByConfigCode_CommonConfigMapperReturnsNoItems() {
        // Setup
        when(mockCommonConfigMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<CommonConfigPO> result = configCodeServiceImplUnderTest.getByConfigCode("configCode");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
