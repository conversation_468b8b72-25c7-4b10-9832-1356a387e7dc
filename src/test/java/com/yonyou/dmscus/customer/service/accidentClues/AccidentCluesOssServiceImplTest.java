package com.yonyou.dmscus.customer.service.accidentClues;

import com.yonyou.dmscloud.framework.domains.dto.basedata.ResponseDTO;
import com.yonyou.dmscloud.framework.service.excel.ExcelExportColumn;
import com.yonyou.dmscus.customer.configuration.InnerUrlProperties;
import com.yonyou.dmscus.customer.configuration.SalesUrlProperties;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesExportDTO;
import com.yonyou.dmscus.customer.util.common.VolvoHttpUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AccidentCluesOssServiceImplTest {

    @Mock
    private VolvoHttpUtils mockVolvoHttpUtils;
    @Mock
    private RestTemplate mockDirectRestTemplate;
    @Mock
    private SalesUrlProperties mockSalesUrlProperties;
    @Mock
    private InnerUrlProperties mockInnerUrlProperties;

    @InjectMocks
    private AccidentCluesOssServiceImpl accidentCluesOssServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(accidentCluesOssServiceImplUnderTest, "exportParam", "exportParam");
        accidentCluesOssServiceImplUnderTest.volvoHttpUtils = mockVolvoHttpUtils;
    }

    @Test
    void testExportExcelAccidentExcel() {
        // Setup
        final AccidentCluesExportDTO dto = new AccidentCluesExportDTO();
        dto.setAppId("appId");
        dto.setOwnerCode("ownerCode");
        dto.setOwnerParCode("ownerParCode");
        dto.setOrgId(0);
        dto.setSource("source");

        when(mockSalesUrlProperties.getDownloadService()).thenReturn("result");
        when(mockSalesUrlProperties.getExportExcelUrl()).thenReturn("result");
        when(mockInnerUrlProperties.getDownloadAccidentReturn()).thenReturn("serviceUrl");

        // Configure VolvoHttpUtils.getHeaders(...).
        final HttpHeaders httpHeaders = new HttpHeaders(new LinkedMultiValueMap(new HashMap<>()));
        when(mockVolvoHttpUtils.getHeaders()).thenReturn(httpHeaders);

        // Configure RestTemplate.exchange(...).
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("error");
        responseDTO.setReturnMessage("message");
        responseDTO.setData(null);
        responseDTO.setCause("cause");
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST,
                new HttpEntity<>(new HashMap<>(), new HttpHeaders()), ResponseDTO.class))
                .thenReturn(responseDTOResponseEntity);

        // Run the test
        accidentCluesOssServiceImplUnderTest.exportExcelAccidentExcel(dto);

        // Verify the results
    }

    @Test
    void testExportExcelAccidentExcel_RestTemplateThrowsRestClientException() {
        // Setup
        final AccidentCluesExportDTO dto = new AccidentCluesExportDTO();
        dto.setAppId("appId");
        dto.setOwnerCode("ownerCode");
        dto.setOwnerParCode("ownerParCode");
        dto.setOrgId(0);
        dto.setSource("source");

        when(mockSalesUrlProperties.getDownloadService()).thenReturn("result");
        when(mockSalesUrlProperties.getExportExcelUrl()).thenReturn("result");
        when(mockInnerUrlProperties.getDownloadAccidentReturn()).thenReturn("serviceUrl");

        // Configure VolvoHttpUtils.getHeaders(...).
        final HttpHeaders httpHeaders = new HttpHeaders(new LinkedMultiValueMap(new HashMap<>()));
        when(mockVolvoHttpUtils.getHeaders()).thenReturn(httpHeaders);

        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST,
                new HttpEntity<>(new HashMap<>(), new HttpHeaders()), ResponseDTO.class))
                .thenThrow(RestClientException.class);

        // Run the test
        accidentCluesOssServiceImplUnderTest.exportExcelAccidentExcel(dto);

        // Verify the results
    }

    @Test
    void testExportColumnList() {
        // Setup
        final Map param = new HashMap<>();

        // Configure VolvoHttpUtils.getHeaders(...).
        final HttpHeaders httpHeaders = new HttpHeaders(new LinkedMultiValueMap(new HashMap<>()));
        when(mockVolvoHttpUtils.getHeaders()).thenReturn(httpHeaders);

        // Configure RestTemplate.exchange(...).
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("error");
        responseDTO.setReturnMessage("message");
        responseDTO.setData(null);
        responseDTO.setCause("cause");
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST,
                new HttpEntity<>(new HashMap<>(), new HttpHeaders()), ResponseDTO.class))
                .thenReturn(responseDTOResponseEntity);

        // Run the test
        final ResponseDTO result = accidentCluesOssServiceImplUnderTest.exportColumnList("url", param);

        // Verify the results
    }

    @Test
    void testExportColumnList_RestTemplateThrowsRestClientException() {
        // Setup
        final Map param = new HashMap<>();

        // Configure VolvoHttpUtils.getHeaders(...).
        final HttpHeaders httpHeaders = new HttpHeaders(new LinkedMultiValueMap(new HashMap<>()));
        when(mockVolvoHttpUtils.getHeaders()).thenReturn(httpHeaders);

        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST,
                new HttpEntity<>(new HashMap<>(), new HttpHeaders()), ResponseDTO.class))
                .thenThrow(RestClientException.class);

        // Run the test
        final ResponseDTO result = accidentCluesOssServiceImplUnderTest.exportColumnList("url", param);

        // Verify the results
    }

    @Test
    void testgetExcelColumn() {
        final  AccidentCluesExportDTO param=new AccidentCluesExportDTO();
        param.setSource("vcdc");
        final List<ExcelExportColumn> result = accidentCluesOssServiceImplUnderTest.getExcelColumn(param);
    }
}
