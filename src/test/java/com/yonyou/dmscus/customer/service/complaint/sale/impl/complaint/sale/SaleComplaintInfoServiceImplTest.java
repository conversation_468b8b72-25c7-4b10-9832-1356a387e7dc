package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintAttachmentMapper;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintInfoMapper;
import com.yonyou.dmscus.customer.dto.*;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.*;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintCustomFieldUseDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintTypeDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sendComplaintData.NewComplaintDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.v51dk.V51dkComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfMorePO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoPO;
import com.yonyou.dmscus.customer.entity.vo.V51dkComplaintInfMoreVO;
import com.yonyou.dmscus.customer.feign.DmscloudServiceClient;
import com.yonyou.dmscus.customer.feign.MidUserOrganizationClient;
import com.yonyou.dmscus.customer.middleInterface.CompanyDetailByCodeDTO;
import com.yonyou.dmscus.customer.service.complaint.ComplaintCustomTopUseService;
import com.yonyou.dmscus.customer.service.complaint.ComplaintInfoService;
import com.yonyou.dmscus.customer.service.complaint.impl.complaint.ComplaintCommonServiceImpl;
import com.yonyou.dmscus.customer.service.complaint.impl.complaint.ComplaintDealerCcmRefServiceImpl;
import com.yonyou.dmscus.customer.service.complaint.impl.complaint.ComplaintInfoServiceImpl;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintCustomFieldUseService;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintFollowService;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintTypeService;
import com.yonyou.dmscus.customer.service.impl.CommonServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SaleComplaintInfoServiceImplTest {

    @Mock
    private SaleComplaintInfoMapper mockSaleComplaintInfoMapper;
    @Mock
    private ComplaintDealerCcmRefServiceImpl mockComplaintDealerCcmRefService;
    @Mock
    private SaleComplaintFollowService mockSaleComplaintFollowService;
    @Mock
    private SaleComplaintAttachmentMapper mockSaleComplaintAttachmentMapper;
    @Mock
    private ComplaintCustomTopUseService mockComplaintCustomTopUseService;
    @Mock
    private SaleComplaintCustomFieldUseService mockSalecomplaintCustomFieldUseService;
    @Mock
    private ComplaintInfoServiceImpl mockComplaintInfoServiceImpll;
    @Mock
    private CommonServiceImpl mockCommonServiceImpl;
    @Mock
    private ComplaintCommonServiceImpl mockComplaintCommonServiceImpl;
    @Mock
    private SaleComplaintTypeService mockSaleComplaintTypeService;
    @Mock
    private ComplaintInfoService mockComplaintInfoService;
    @Mock
    private MidUserOrganizationClient mockMidUserOrganizationClient;
    @Mock
    private DmscloudServiceClient mockDmscloudServiceClient;

    @InjectMocks
    private SaleComplaintInfoServiceImpl saleComplaintInfoServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(saleComplaintInfoServiceImplUnderTest, "baseUrl", "baseUrl");
        ReflectionTestUtils.setField(saleComplaintInfoServiceImplUnderTest, "apiuid", "apiuid");
        ReflectionTestUtils.setField(saleComplaintInfoServiceImplUnderTest, "apipwd", "apipwd");
        saleComplaintInfoServiceImplUnderTest.saleComplaintInfoMapper = mockSaleComplaintInfoMapper;
        saleComplaintInfoServiceImplUnderTest.complaintDealerCcmRefService = mockComplaintDealerCcmRefService;
        saleComplaintInfoServiceImplUnderTest.saleComplaintFollowService = mockSaleComplaintFollowService;
        saleComplaintInfoServiceImplUnderTest.saleComplaintAttachmentMapper = mockSaleComplaintAttachmentMapper;
        saleComplaintInfoServiceImplUnderTest.complaintCustomTopUseService = mockComplaintCustomTopUseService;
        saleComplaintInfoServiceImplUnderTest.salecomplaintCustomFieldUseService = mockSalecomplaintCustomFieldUseService;
        saleComplaintInfoServiceImplUnderTest.complaintInfoServiceImpll = mockComplaintInfoServiceImpll;
        saleComplaintInfoServiceImplUnderTest.commonServiceImpl = mockCommonServiceImpl;
        saleComplaintInfoServiceImplUnderTest.complaintCommonServiceImpl = mockComplaintCommonServiceImpl;
        saleComplaintInfoServiceImplUnderTest.saleComplaintTypeService = mockSaleComplaintTypeService;
        saleComplaintInfoServiceImplUnderTest.complaintInfoService = mockComplaintInfoService;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintInfoDTO saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        saleComplaintInfoDTO.setHQSatisfiedCase(0);
        saleComplaintInfoDTO.setRegionSatisfiedCase(0);
        saleComplaintInfoDTO.setIsAgreeHeadquarters(0);
        saleComplaintInfoDTO.setHeadquartersComments("closeCaseRemark");
        saleComplaintInfoDTO.setRegionAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setRegionComments("closeCaseRemark");
        saleComplaintInfoDTO.setCloseCaseRemark("closeCaseRemark");
        saleComplaintInfoDTO.setIsAgreeRegion(0);
        saleComplaintInfoDTO.setIsOpinion(0);
        saleComplaintInfoDTO.setId(0L);
        saleComplaintInfoDTO.setComplaintId("complaintId");
        saleComplaintInfoDTO.setType(0);
        saleComplaintInfoDTO.setSource(0);
        saleComplaintInfoDTO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setNewestRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCallName("callName");
        saleComplaintInfoDTO.setCallTel("callTel");
        saleComplaintInfoDTO.setName("name");
        saleComplaintInfoDTO.setLicensePlateNum("licensePlateNum");
        saleComplaintInfoDTO.setVin("vin");
        saleComplaintInfoDTO.setBuyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setModel("model");
        saleComplaintInfoDTO.setModelYear("modelYear");
        saleComplaintInfoDTO.setBuyDealerName("buyDealerName");
        saleComplaintInfoDTO.setDealerName("companyNameCn");
        saleComplaintInfoDTO.setDealerCode("CreaterOrg");
        saleComplaintInfoDTO.setMileage(0);
        saleComplaintInfoDTO.setSubject("subject");
        saleComplaintInfoDTO.setSubdivisionPart("subdivisionPart");
        saleComplaintInfoDTO.setPart("part");
        saleComplaintInfoDTO.setProblem("problem");
        saleComplaintInfoDTO.setCategory1("category1");
        saleComplaintInfoDTO.setCategory2("category2");
        saleComplaintInfoDTO.setCategory3("category3");
        saleComplaintInfoDTO.setDepartment("department");
        saleComplaintInfoDTO.setImportanceLevel(0);
        saleComplaintInfoDTO.setDealerFisrtReplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setIsRevisit(0);
        saleComplaintInfoDTO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCloseCaseStatus(0);
        saleComplaintInfoDTO.setCusRequirement("cusRequirement");
        saleComplaintInfoDTO.setProblemInfo("problemInfo");
        saleComplaintInfoDTO.setReport(false);
        saleComplaintInfoDTO.setDataSources(0);
        saleComplaintInfoDTO.setSex(0);
        saleComplaintInfoDTO.setOwnerAddress("ownerAddress");
        saleComplaintInfoDTO.setWorkOrderStatus(0);
        saleComplaintInfoDTO.setIsCloseCase(0);
        saleComplaintInfoDTO.setRegion("buyRegion");
        saleComplaintInfoDTO.setRegionManager("buyRegionManager");
        saleComplaintInfoDTO.setRegionId(0L);
        saleComplaintInfoDTO.setRegionManagerId(0L);
        saleComplaintInfoDTO.setBloc("bloc");
        saleComplaintInfoDTO.setBuyDealerCode("buyDealerCode");
        saleComplaintInfoDTO.setBuyRegion("buyRegion");
        saleComplaintInfoDTO.setBuyRegionManager("buyRegionManager");
        saleComplaintInfoDTO.setBuyRegionId(0L);
        saleComplaintInfoDTO.setBuyRegionManagerId(0L);
        saleComplaintInfoDTO.setBuyBloc("bloc");
        saleComplaintInfoDTO.setBlocId(0L);
        saleComplaintInfoDTO.setWorkOrderNature(0);
        saleComplaintInfoDTO.setWorkOrderClassification(0);

        // Configure SaleComplaintInfoMapper.selectPageBySql(...).
        final SaleComplaintInfoPO saleComplaintInfoPO = new SaleComplaintInfoPO();
        saleComplaintInfoPO.setRegionAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoPO.setId(0L);
        saleComplaintInfoPO.setComplaintId("complaintId");
        saleComplaintInfoPO.setType(0);
        saleComplaintInfoPO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoPO.setIsRevisit(0);
        saleComplaintInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoPO.setDataSources(0);
        saleComplaintInfoPO.setWorkOrderStatus(0);
        saleComplaintInfoPO.setIsSatisfied(0);
        final List<SaleComplaintInfoPO> saleComplaintInfoPOS = Arrays.asList(saleComplaintInfoPO);
        when(mockSaleComplaintInfoMapper.selectPageBySql(any(Page.class), any(SaleComplaintInfoPO.class)))
                .thenReturn(saleComplaintInfoPOS);

        // Run the test
        final IPage<SaleComplaintInfoDTO> result = saleComplaintInfoServiceImplUnderTest.selectPageBysql(page,
                saleComplaintInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_SaleComplaintInfoMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintInfoDTO saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        saleComplaintInfoDTO.setHQSatisfiedCase(0);
        saleComplaintInfoDTO.setRegionSatisfiedCase(0);
        saleComplaintInfoDTO.setIsAgreeHeadquarters(0);
        saleComplaintInfoDTO.setHeadquartersComments("closeCaseRemark");
        saleComplaintInfoDTO.setRegionAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setRegionComments("closeCaseRemark");
        saleComplaintInfoDTO.setCloseCaseRemark("closeCaseRemark");
        saleComplaintInfoDTO.setIsAgreeRegion(0);
        saleComplaintInfoDTO.setIsOpinion(0);
        saleComplaintInfoDTO.setId(0L);
        saleComplaintInfoDTO.setComplaintId("complaintId");
        saleComplaintInfoDTO.setType(0);
        saleComplaintInfoDTO.setSource(0);
        saleComplaintInfoDTO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setNewestRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCallName("callName");
        saleComplaintInfoDTO.setCallTel("callTel");
        saleComplaintInfoDTO.setName("name");
        saleComplaintInfoDTO.setLicensePlateNum("licensePlateNum");
        saleComplaintInfoDTO.setVin("vin");
        saleComplaintInfoDTO.setBuyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setModel("model");
        saleComplaintInfoDTO.setModelYear("modelYear");
        saleComplaintInfoDTO.setBuyDealerName("buyDealerName");
        saleComplaintInfoDTO.setDealerName("companyNameCn");
        saleComplaintInfoDTO.setDealerCode("CreaterOrg");
        saleComplaintInfoDTO.setMileage(0);
        saleComplaintInfoDTO.setSubject("subject");
        saleComplaintInfoDTO.setSubdivisionPart("subdivisionPart");
        saleComplaintInfoDTO.setPart("part");
        saleComplaintInfoDTO.setProblem("problem");
        saleComplaintInfoDTO.setCategory1("category1");
        saleComplaintInfoDTO.setCategory2("category2");
        saleComplaintInfoDTO.setCategory3("category3");
        saleComplaintInfoDTO.setDepartment("department");
        saleComplaintInfoDTO.setImportanceLevel(0);
        saleComplaintInfoDTO.setDealerFisrtReplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setIsRevisit(0);
        saleComplaintInfoDTO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCloseCaseStatus(0);
        saleComplaintInfoDTO.setCusRequirement("cusRequirement");
        saleComplaintInfoDTO.setProblemInfo("problemInfo");
        saleComplaintInfoDTO.setReport(false);
        saleComplaintInfoDTO.setDataSources(0);
        saleComplaintInfoDTO.setSex(0);
        saleComplaintInfoDTO.setOwnerAddress("ownerAddress");
        saleComplaintInfoDTO.setWorkOrderStatus(0);
        saleComplaintInfoDTO.setIsCloseCase(0);
        saleComplaintInfoDTO.setRegion("buyRegion");
        saleComplaintInfoDTO.setRegionManager("buyRegionManager");
        saleComplaintInfoDTO.setRegionId(0L);
        saleComplaintInfoDTO.setRegionManagerId(0L);
        saleComplaintInfoDTO.setBloc("bloc");
        saleComplaintInfoDTO.setBuyDealerCode("buyDealerCode");
        saleComplaintInfoDTO.setBuyRegion("buyRegion");
        saleComplaintInfoDTO.setBuyRegionManager("buyRegionManager");
        saleComplaintInfoDTO.setBuyRegionId(0L);
        saleComplaintInfoDTO.setBuyRegionManagerId(0L);
        saleComplaintInfoDTO.setBuyBloc("bloc");
        saleComplaintInfoDTO.setBlocId(0L);
        saleComplaintInfoDTO.setWorkOrderNature(0);
        saleComplaintInfoDTO.setWorkOrderClassification(0);

        when(mockSaleComplaintInfoMapper.selectPageBySql(any(Page.class), any(SaleComplaintInfoPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final IPage<SaleComplaintInfoDTO> result = saleComplaintInfoServiceImplUnderTest.selectPageBysql(page,
                saleComplaintInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final SaleComplaintInfoDTO saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        saleComplaintInfoDTO.setHQSatisfiedCase(0);
        saleComplaintInfoDTO.setRegionSatisfiedCase(0);
        saleComplaintInfoDTO.setIsAgreeHeadquarters(0);
        saleComplaintInfoDTO.setHeadquartersComments("closeCaseRemark");
        saleComplaintInfoDTO.setRegionAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setRegionComments("closeCaseRemark");
        saleComplaintInfoDTO.setCloseCaseRemark("closeCaseRemark");
        saleComplaintInfoDTO.setIsAgreeRegion(0);
        saleComplaintInfoDTO.setIsOpinion(0);
        saleComplaintInfoDTO.setId(0L);
        saleComplaintInfoDTO.setComplaintId("complaintId");
        saleComplaintInfoDTO.setType(0);
        saleComplaintInfoDTO.setSource(0);
        saleComplaintInfoDTO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setNewestRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCallName("callName");
        saleComplaintInfoDTO.setCallTel("callTel");
        saleComplaintInfoDTO.setName("name");
        saleComplaintInfoDTO.setLicensePlateNum("licensePlateNum");
        saleComplaintInfoDTO.setVin("vin");
        saleComplaintInfoDTO.setBuyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setModel("model");
        saleComplaintInfoDTO.setModelYear("modelYear");
        saleComplaintInfoDTO.setBuyDealerName("buyDealerName");
        saleComplaintInfoDTO.setDealerName("companyNameCn");
        saleComplaintInfoDTO.setDealerCode("CreaterOrg");
        saleComplaintInfoDTO.setMileage(0);
        saleComplaintInfoDTO.setSubject("subject");
        saleComplaintInfoDTO.setSubdivisionPart("subdivisionPart");
        saleComplaintInfoDTO.setPart("part");
        saleComplaintInfoDTO.setProblem("problem");
        saleComplaintInfoDTO.setCategory1("category1");
        saleComplaintInfoDTO.setCategory2("category2");
        saleComplaintInfoDTO.setCategory3("category3");
        saleComplaintInfoDTO.setDepartment("department");
        saleComplaintInfoDTO.setImportanceLevel(0);
        saleComplaintInfoDTO.setDealerFisrtReplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setIsRevisit(0);
        saleComplaintInfoDTO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCloseCaseStatus(0);
        saleComplaintInfoDTO.setCusRequirement("cusRequirement");
        saleComplaintInfoDTO.setProblemInfo("problemInfo");
        saleComplaintInfoDTO.setReport(false);
        saleComplaintInfoDTO.setDataSources(0);
        saleComplaintInfoDTO.setSex(0);
        saleComplaintInfoDTO.setOwnerAddress("ownerAddress");
        saleComplaintInfoDTO.setWorkOrderStatus(0);
        saleComplaintInfoDTO.setIsCloseCase(0);
        saleComplaintInfoDTO.setRegion("buyRegion");
        saleComplaintInfoDTO.setRegionManager("buyRegionManager");
        saleComplaintInfoDTO.setRegionId(0L);
        saleComplaintInfoDTO.setRegionManagerId(0L);
        saleComplaintInfoDTO.setBloc("bloc");
        saleComplaintInfoDTO.setBuyDealerCode("buyDealerCode");
        saleComplaintInfoDTO.setBuyRegion("buyRegion");
        saleComplaintInfoDTO.setBuyRegionManager("buyRegionManager");
        saleComplaintInfoDTO.setBuyRegionId(0L);
        saleComplaintInfoDTO.setBuyRegionManagerId(0L);
        saleComplaintInfoDTO.setBuyBloc("bloc");
        saleComplaintInfoDTO.setBlocId(0L);
        saleComplaintInfoDTO.setWorkOrderNature(0);
        saleComplaintInfoDTO.setWorkOrderClassification(0);

        // Configure SaleComplaintInfoMapper.selectListBySql(...).
        final SaleComplaintInfoPO saleComplaintInfoPO = new SaleComplaintInfoPO();
        saleComplaintInfoPO.setRegionAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoPO.setId(0L);
        saleComplaintInfoPO.setComplaintId("complaintId");
        saleComplaintInfoPO.setType(0);
        saleComplaintInfoPO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoPO.setIsRevisit(0);
        saleComplaintInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoPO.setDataSources(0);
        saleComplaintInfoPO.setWorkOrderStatus(0);
        saleComplaintInfoPO.setIsSatisfied(0);
        final List<SaleComplaintInfoPO> saleComplaintInfoPOS = Arrays.asList(saleComplaintInfoPO);
        when(mockSaleComplaintInfoMapper.selectListBySql(any(SaleComplaintInfoPO.class)))
                .thenReturn(saleComplaintInfoPOS);

        // Run the test
        final List<SaleComplaintInfoDTO> result = saleComplaintInfoServiceImplUnderTest.selectListBySql(
                saleComplaintInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_SaleComplaintInfoMapperReturnsNoItems() {
        // Setup
        final SaleComplaintInfoDTO saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        saleComplaintInfoDTO.setHQSatisfiedCase(0);
        saleComplaintInfoDTO.setRegionSatisfiedCase(0);
        saleComplaintInfoDTO.setIsAgreeHeadquarters(0);
        saleComplaintInfoDTO.setHeadquartersComments("closeCaseRemark");
        saleComplaintInfoDTO.setRegionAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setRegionComments("closeCaseRemark");
        saleComplaintInfoDTO.setCloseCaseRemark("closeCaseRemark");
        saleComplaintInfoDTO.setIsAgreeRegion(0);
        saleComplaintInfoDTO.setIsOpinion(0);
        saleComplaintInfoDTO.setId(0L);
        saleComplaintInfoDTO.setComplaintId("complaintId");
        saleComplaintInfoDTO.setType(0);
        saleComplaintInfoDTO.setSource(0);
        saleComplaintInfoDTO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setNewestRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCallName("callName");
        saleComplaintInfoDTO.setCallTel("callTel");
        saleComplaintInfoDTO.setName("name");
        saleComplaintInfoDTO.setLicensePlateNum("licensePlateNum");
        saleComplaintInfoDTO.setVin("vin");
        saleComplaintInfoDTO.setBuyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setModel("model");
        saleComplaintInfoDTO.setModelYear("modelYear");
        saleComplaintInfoDTO.setBuyDealerName("buyDealerName");
        saleComplaintInfoDTO.setDealerName("companyNameCn");
        saleComplaintInfoDTO.setDealerCode("CreaterOrg");
        saleComplaintInfoDTO.setMileage(0);
        saleComplaintInfoDTO.setSubject("subject");
        saleComplaintInfoDTO.setSubdivisionPart("subdivisionPart");
        saleComplaintInfoDTO.setPart("part");
        saleComplaintInfoDTO.setProblem("problem");
        saleComplaintInfoDTO.setCategory1("category1");
        saleComplaintInfoDTO.setCategory2("category2");
        saleComplaintInfoDTO.setCategory3("category3");
        saleComplaintInfoDTO.setDepartment("department");
        saleComplaintInfoDTO.setImportanceLevel(0);
        saleComplaintInfoDTO.setDealerFisrtReplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setIsRevisit(0);
        saleComplaintInfoDTO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCloseCaseStatus(0);
        saleComplaintInfoDTO.setCusRequirement("cusRequirement");
        saleComplaintInfoDTO.setProblemInfo("problemInfo");
        saleComplaintInfoDTO.setReport(false);
        saleComplaintInfoDTO.setDataSources(0);
        saleComplaintInfoDTO.setSex(0);
        saleComplaintInfoDTO.setOwnerAddress("ownerAddress");
        saleComplaintInfoDTO.setWorkOrderStatus(0);
        saleComplaintInfoDTO.setIsCloseCase(0);
        saleComplaintInfoDTO.setRegion("buyRegion");
        saleComplaintInfoDTO.setRegionManager("buyRegionManager");
        saleComplaintInfoDTO.setRegionId(0L);
        saleComplaintInfoDTO.setRegionManagerId(0L);
        saleComplaintInfoDTO.setBloc("bloc");
        saleComplaintInfoDTO.setBuyDealerCode("buyDealerCode");
        saleComplaintInfoDTO.setBuyRegion("buyRegion");
        saleComplaintInfoDTO.setBuyRegionManager("buyRegionManager");
        saleComplaintInfoDTO.setBuyRegionId(0L);
        saleComplaintInfoDTO.setBuyRegionManagerId(0L);
        saleComplaintInfoDTO.setBuyBloc("bloc");
        saleComplaintInfoDTO.setBlocId(0L);
        saleComplaintInfoDTO.setWorkOrderNature(0);
        saleComplaintInfoDTO.setWorkOrderClassification(0);

        when(mockSaleComplaintInfoMapper.selectListBySql(any(SaleComplaintInfoPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<SaleComplaintInfoDTO> result = saleComplaintInfoServiceImplUnderTest.selectListBySql(
                saleComplaintInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure SaleComplaintInfoMapper.selectById(...).
        final SaleComplaintInfoPO saleComplaintInfoPO = new SaleComplaintInfoPO();
        saleComplaintInfoPO.setRegionAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoPO.setId(0L);
        saleComplaintInfoPO.setComplaintId("complaintId");
        saleComplaintInfoPO.setType(0);
        saleComplaintInfoPO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoPO.setIsRevisit(0);
        saleComplaintInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoPO.setDataSources(0);
        saleComplaintInfoPO.setWorkOrderStatus(0);
        saleComplaintInfoPO.setIsSatisfied(0);
        when(mockSaleComplaintInfoMapper.selectById(0L)).thenReturn(saleComplaintInfoPO);

        // Run the test
        final SaleComplaintInfoDTO result = saleComplaintInfoServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_SaleComplaintInfoMapperReturnsNull() {
        // Setup
        when(mockSaleComplaintInfoMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> saleComplaintInfoServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final SaleComplaintInfoDTO saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        saleComplaintInfoDTO.setHQSatisfiedCase(0);
        saleComplaintInfoDTO.setRegionSatisfiedCase(0);
        saleComplaintInfoDTO.setIsAgreeHeadquarters(0);
        saleComplaintInfoDTO.setHeadquartersComments("closeCaseRemark");
        saleComplaintInfoDTO.setRegionAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setRegionComments("closeCaseRemark");
        saleComplaintInfoDTO.setCloseCaseRemark("closeCaseRemark");
        saleComplaintInfoDTO.setIsAgreeRegion(0);
        saleComplaintInfoDTO.setIsOpinion(0);
        saleComplaintInfoDTO.setId(0L);
        saleComplaintInfoDTO.setComplaintId("complaintId");
        saleComplaintInfoDTO.setType(0);
        saleComplaintInfoDTO.setSource(0);
        saleComplaintInfoDTO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setNewestRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCallName("callName");
        saleComplaintInfoDTO.setCallTel("callTel");
        saleComplaintInfoDTO.setName("name");
        saleComplaintInfoDTO.setLicensePlateNum("licensePlateNum");
        saleComplaintInfoDTO.setVin("vin");
        saleComplaintInfoDTO.setBuyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setModel("model");
        saleComplaintInfoDTO.setModelYear("modelYear");
        saleComplaintInfoDTO.setBuyDealerName("buyDealerName");
        saleComplaintInfoDTO.setDealerName("companyNameCn");
        saleComplaintInfoDTO.setDealerCode("CreaterOrg");
        saleComplaintInfoDTO.setMileage(0);
        saleComplaintInfoDTO.setSubject("subject");
        saleComplaintInfoDTO.setSubdivisionPart("subdivisionPart");
        saleComplaintInfoDTO.setPart("part");
        saleComplaintInfoDTO.setProblem("problem");
        saleComplaintInfoDTO.setCategory1("category1");
        saleComplaintInfoDTO.setCategory2("category2");
        saleComplaintInfoDTO.setCategory3("category3");
        saleComplaintInfoDTO.setDepartment("department");
        saleComplaintInfoDTO.setImportanceLevel(0);
        saleComplaintInfoDTO.setDealerFisrtReplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setIsRevisit(0);
        saleComplaintInfoDTO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCloseCaseStatus(0);
        saleComplaintInfoDTO.setCusRequirement("cusRequirement");
        saleComplaintInfoDTO.setProblemInfo("problemInfo");
        saleComplaintInfoDTO.setReport(false);
        saleComplaintInfoDTO.setDataSources(0);
        saleComplaintInfoDTO.setSex(0);
        saleComplaintInfoDTO.setOwnerAddress("ownerAddress");
        saleComplaintInfoDTO.setWorkOrderStatus(0);
        saleComplaintInfoDTO.setIsCloseCase(0);
        saleComplaintInfoDTO.setRegion("buyRegion");
        saleComplaintInfoDTO.setRegionManager("buyRegionManager");
        saleComplaintInfoDTO.setRegionId(0L);
        saleComplaintInfoDTO.setRegionManagerId(0L);
        saleComplaintInfoDTO.setBloc("bloc");
        saleComplaintInfoDTO.setBuyDealerCode("buyDealerCode");
        saleComplaintInfoDTO.setBuyRegion("buyRegion");
        saleComplaintInfoDTO.setBuyRegionManager("buyRegionManager");
        saleComplaintInfoDTO.setBuyRegionId(0L);
        saleComplaintInfoDTO.setBuyRegionManagerId(0L);
        saleComplaintInfoDTO.setBuyBloc("bloc");
        saleComplaintInfoDTO.setBlocId(0L);
        saleComplaintInfoDTO.setWorkOrderNature(0);
        saleComplaintInfoDTO.setWorkOrderClassification(0);

        when(mockSaleComplaintInfoMapper.insert(any(SaleComplaintInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintInfoServiceImplUnderTest.insert(saleComplaintInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final SaleComplaintInfoDTO saleComplaintInfoDTO = new SaleComplaintInfoDTO();
        saleComplaintInfoDTO.setHQSatisfiedCase(0);
        saleComplaintInfoDTO.setRegionSatisfiedCase(0);
        saleComplaintInfoDTO.setIsAgreeHeadquarters(0);
        saleComplaintInfoDTO.setHeadquartersComments("closeCaseRemark");
        saleComplaintInfoDTO.setRegionAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setRegionComments("closeCaseRemark");
        saleComplaintInfoDTO.setCloseCaseRemark("closeCaseRemark");
        saleComplaintInfoDTO.setIsAgreeRegion(0);
        saleComplaintInfoDTO.setIsOpinion(0);
        saleComplaintInfoDTO.setId(0L);
        saleComplaintInfoDTO.setComplaintId("complaintId");
        saleComplaintInfoDTO.setType(0);
        saleComplaintInfoDTO.setSource(0);
        saleComplaintInfoDTO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setNewestRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCallName("callName");
        saleComplaintInfoDTO.setCallTel("callTel");
        saleComplaintInfoDTO.setName("name");
        saleComplaintInfoDTO.setLicensePlateNum("licensePlateNum");
        saleComplaintInfoDTO.setVin("vin");
        saleComplaintInfoDTO.setBuyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setModel("model");
        saleComplaintInfoDTO.setModelYear("modelYear");
        saleComplaintInfoDTO.setBuyDealerName("buyDealerName");
        saleComplaintInfoDTO.setDealerName("companyNameCn");
        saleComplaintInfoDTO.setDealerCode("CreaterOrg");
        saleComplaintInfoDTO.setMileage(0);
        saleComplaintInfoDTO.setSubject("subject");
        saleComplaintInfoDTO.setSubdivisionPart("subdivisionPart");
        saleComplaintInfoDTO.setPart("part");
        saleComplaintInfoDTO.setProblem("problem");
        saleComplaintInfoDTO.setCategory1("category1");
        saleComplaintInfoDTO.setCategory2("category2");
        saleComplaintInfoDTO.setCategory3("category3");
        saleComplaintInfoDTO.setDepartment("department");
        saleComplaintInfoDTO.setImportanceLevel(0);
        saleComplaintInfoDTO.setDealerFisrtReplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setIsRevisit(0);
        saleComplaintInfoDTO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoDTO.setCloseCaseStatus(0);
        saleComplaintInfoDTO.setCusRequirement("cusRequirement");
        saleComplaintInfoDTO.setProblemInfo("problemInfo");
        saleComplaintInfoDTO.setReport(false);
        saleComplaintInfoDTO.setDataSources(0);
        saleComplaintInfoDTO.setSex(0);
        saleComplaintInfoDTO.setOwnerAddress("ownerAddress");
        saleComplaintInfoDTO.setWorkOrderStatus(0);
        saleComplaintInfoDTO.setIsCloseCase(0);
        saleComplaintInfoDTO.setRegion("buyRegion");
        saleComplaintInfoDTO.setRegionManager("buyRegionManager");
        saleComplaintInfoDTO.setRegionId(0L);
        saleComplaintInfoDTO.setRegionManagerId(0L);
        saleComplaintInfoDTO.setBloc("bloc");
        saleComplaintInfoDTO.setBuyDealerCode("buyDealerCode");
        saleComplaintInfoDTO.setBuyRegion("buyRegion");
        saleComplaintInfoDTO.setBuyRegionManager("buyRegionManager");
        saleComplaintInfoDTO.setBuyRegionId(0L);
        saleComplaintInfoDTO.setBuyRegionManagerId(0L);
        saleComplaintInfoDTO.setBuyBloc("bloc");
        saleComplaintInfoDTO.setBlocId(0L);
        saleComplaintInfoDTO.setWorkOrderNature(0);
        saleComplaintInfoDTO.setWorkOrderClassification(0);

        // Configure SaleComplaintInfoMapper.selectById(...).
        final SaleComplaintInfoPO saleComplaintInfoPO = new SaleComplaintInfoPO();
        saleComplaintInfoPO.setRegionAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoPO.setId(0L);
        saleComplaintInfoPO.setComplaintId("complaintId");
        saleComplaintInfoPO.setType(0);
        saleComplaintInfoPO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoPO.setIsRevisit(0);
        saleComplaintInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintInfoPO.setDataSources(0);
        saleComplaintInfoPO.setWorkOrderStatus(0);
        saleComplaintInfoPO.setIsSatisfied(0);
        when(mockSaleComplaintInfoMapper.selectById(0L)).thenReturn(saleComplaintInfoPO);

        when(mockSaleComplaintInfoMapper.updateById(any(SaleComplaintInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintInfoServiceImplUnderTest.update(0L, saleComplaintInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectSaleCusDetailById() {
        // Setup
        final ComplaintInfMoreDTO complaintInfMoreDTO = new ComplaintInfMoreDTO();
        complaintInfMoreDTO.setWorkOrderStatusData("workOrderStatusData");
        complaintInfMoreDTO.setImportanceLevelData("importanceLevelData");
        complaintInfMoreDTO.setWorkOrderStatus1(Arrays.asList());
        complaintInfMoreDTO.setImportanceLevel1(Arrays.asList());
        complaintInfMoreDTO.setOwnedCompanyCode("ownedCompanyCode");
        complaintInfMoreDTO.setOwnedCompanyNameCn("ownedCompanyNameCn");
        complaintInfMoreDTO.setOwnedCompanyShortNameCn("ownedCompanyShortNameCn");
        complaintInfMoreDTO.setOwnedCompanyStatus(0);
        complaintInfMoreDTO.setIsAnonymous(0);
        complaintInfMoreDTO.setCategory11(Arrays.asList());
        complaintInfMoreDTO.setCategory21(Arrays.asList());
        complaintInfMoreDTO.setCategory31(Arrays.asList());
        complaintInfMoreDTO.setSmallClass1(Arrays.asList());
        complaintInfMoreDTO.setRegionManager1(Arrays.asList());
        complaintInfMoreDTO.setModel1(Arrays.asList());
        complaintInfMoreDTO.setSql("sql");
        complaintInfMoreDTO.setSmallClass("smallClass");
        complaintInfMoreDTO.setPlanFollowTime1(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setPlanFollowTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setFisrtRestartTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setActuallFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setActuallFollowTime1(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setSource1(Arrays.asList());
        complaintInfMoreDTO.setCallTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setType1(Arrays.asList());
        complaintInfMoreDTO.setActuallFollowTime2(new String[]{"actuallFollowTime2"});
        complaintInfMoreDTO.setCallTime1(new String[]{"callTime1"});
        complaintInfMoreDTO.setFisrtRestartTime1(new String[]{"fisrtRestartTime1"});
        complaintInfMoreDTO.setPlanFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setPlanFollowTime5(new String[]{"planFollowTime5"});
        complaintInfMoreDTO.setType("type");
        complaintInfMoreDTO.setSource("source");
        complaintInfMoreDTO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setModel("model");
        complaintInfMoreDTO.setDealerCode("dealerCode");
        complaintInfMoreDTO.setReport(false);
        complaintInfMoreDTO.setCategory1("category1");
        complaintInfMoreDTO.setCategory2("category2");
        complaintInfMoreDTO.setCategory3("category3");

        final ComplaintInfMoreVo expectedResult = new ComplaintInfMoreVo();
        expectedResult.setInvalidCaseHidden(0);
        expectedResult.setHQSatisfiedCase(0);
        expectedResult.setRegionSatisfiedCase(0);
        expectedResult.setIsAnonymous(0);
        expectedResult.setLastFollowTime("lastFollowTime");

        // Configure SaleComplaintInfoMapper.selectSaleCusDetailById(...).
        final ComplaintInfMoreVo complaintInfMoreVo = new ComplaintInfMoreVo();
        complaintInfMoreVo.setInvalidCaseHidden(0);
        complaintInfMoreVo.setHQSatisfiedCase(0);
        complaintInfMoreVo.setRegionSatisfiedCase(0);
        complaintInfMoreVo.setIsAnonymous(0);
        complaintInfMoreVo.setLastFollowTime("lastFollowTime");
        when(mockSaleComplaintInfoMapper.selectSaleCusDetailById(any(ComplaintInfMoreDTO.class)))
                .thenReturn(complaintInfMoreVo);

        // Run the test
        final ComplaintInfMoreVo result = saleComplaintInfoServiceImplUnderTest.selectSaleCusDetailById(
                complaintInfMoreDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
