package com.yonyou.dmscus.customer.service.common;

import com.yonyou.dmscus.customer.bean.entity.HttpLogPO;
import com.yonyou.dmscus.customer.dao.common.HttpLogMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class HttpLogServiceImplTest {

    @Mock
    private HttpLogMapper mockHttpLogMapper;

    private HttpLogServiceImpl httpLogServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        httpLogServiceImplUnderTest = new HttpLogServiceImpl();
        httpLogServiceImplUnderTest.httpLogMapper = mockHttpLogMapper;
    }

    @Test
    void testSaveHttpLog() {
        // Setup
        // Run the test
        httpLogServiceImplUnderTest.saveHttpLog("describe", "requestUrl", "requestParam", "requestType", "responseCode",
                "responseMsg");

        // Verify the results
        // Confirm HttpLogMapper.insert(...).
        final HttpLogPO entity = new HttpLogPO();
        entity.setDescription("describe");
        entity.setDealerCode("dealerCode");
        entity.setRequestUrl("requestUrl");
        entity.setRequestParam("requestParam");
        entity.setRequestType("requestType");
        entity.setResponseCode("responseCode");
        entity.setResponseMsg("responseMsg");
        verify(mockHttpLogMapper).insert(entity);
    }
}
