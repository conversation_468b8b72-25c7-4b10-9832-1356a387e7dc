package com.yonyou.dmscus.customer.service.impl.inviteVehicleDealerAllocate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.common.AjaxResponse;
import com.yonyou.dmscus.customer.dao.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryMapper;
import com.yonyou.dmscus.customer.dao.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateMapper;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.*;
import com.yonyou.dmscus.customer.entity.dto.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryDTO;
import com.yonyou.dmscus.customer.entity.po.inviteVehicleDealerAllocate.InviteVehicleDealerAllocateHistoryPO;
import com.yonyou.dmscus.customer.entity.po.inviteVehicleDealerAllocate.InviteVehicleDealerAllocatePO;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteVehicleDealerAllocateServiceImplTest {

    @Mock
    private InviteVehicleDealerAllocateMapper mockInviteVehicleDealerAllocateMapper;
    @Mock
    private InviteVehicleDealerAllocateHistoryMapper mockInviteVehicleDealerAllocateHistoryMapper;
    @Mock
    private BusinessPlatformService mockBusinessPlatformService;
    @Mock
    private InviteVehicleRecordService mockInviteVehicleRecordService;
    @Mock
    private ReportCommonClient mockReportCommonClient;

    private InviteVehicleDealerAllocateServiceImpl inviteVehicleDealerAllocateServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteVehicleDealerAllocateServiceImplUnderTest = new InviteVehicleDealerAllocateServiceImpl();
        inviteVehicleDealerAllocateServiceImplUnderTest.inviteVehicleDealerAllocateMapper = mockInviteVehicleDealerAllocateMapper;
        inviteVehicleDealerAllocateServiceImplUnderTest.inviteVehicleDealerAllocateHistoryMapper = mockInviteVehicleDealerAllocateHistoryMapper;
        inviteVehicleDealerAllocateServiceImplUnderTest.businessPlatformService = mockBusinessPlatformService;
        inviteVehicleDealerAllocateServiceImplUnderTest.inviteVehicleRecordService = mockInviteVehicleRecordService;
        inviteVehicleDealerAllocateServiceImplUnderTest.reportCommonClient = mockReportCommonClient;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final InviteVehicleDealerAllocateDTO inviteVehicleDealerAllocateDTO = new InviteVehicleDealerAllocateDTO();
        final InviteVehicleDealerAllocateHistoryDTO inviteVehicleDealerAllocateHistoryDTO = new InviteVehicleDealerAllocateHistoryDTO();
        inviteVehicleDealerAllocateHistoryDTO.setVin("vin");
        inviteVehicleDealerAllocateHistoryDTO.setLicensePlateNum("licensePlateNum");
        inviteVehicleDealerAllocateHistoryDTO.setName("name");
        inviteVehicleDealerAllocateHistoryDTO.setDealerName("lastDealerName");
        inviteVehicleDealerAllocateHistoryDTO.setDealerCode("lastDealerCode");
        inviteVehicleDealerAllocateDTO.setVehicleList(Arrays.asList(inviteVehicleDealerAllocateHistoryDTO));
        inviteVehicleDealerAllocateDTO.setDealerName("dealerName");
        inviteVehicleDealerAllocateDTO.setDealerCode("dealerCode");

        // Configure InviteVehicleDealerAllocateMapper.selectPageBySql(...).
        final InviteVehicleDealerAllocatePO inviteVehicleDealerAllocatePO = new InviteVehicleDealerAllocatePO();
        inviteVehicleDealerAllocatePO.setCreatedBy("createdBy");
        inviteVehicleDealerAllocatePO.setUpdatedBy("updatedBy");
        inviteVehicleDealerAllocatePO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleDealerAllocatePO.setVin("vin");
        inviteVehicleDealerAllocatePO.setDealerName("dealerName");
        inviteVehicleDealerAllocatePO.setDealerCode("dealerCode");
        final List<InviteVehicleDealerAllocatePO> inviteVehicleDealerAllocatePOS = Arrays.asList(
                inviteVehicleDealerAllocatePO);
        when(mockInviteVehicleDealerAllocateMapper.selectPageBySql(any(Page.class),
                any(InviteVehicleDealerAllocatePO.class))).thenReturn(inviteVehicleDealerAllocatePOS);

        // Run the test
        final IPage<InviteVehicleDealerAllocateDTO> result = inviteVehicleDealerAllocateServiceImplUnderTest.selectPageBysql(
                page, inviteVehicleDealerAllocateDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_InviteVehicleDealerAllocateMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final InviteVehicleDealerAllocateDTO inviteVehicleDealerAllocateDTO = new InviteVehicleDealerAllocateDTO();
        final InviteVehicleDealerAllocateHistoryDTO inviteVehicleDealerAllocateHistoryDTO = new InviteVehicleDealerAllocateHistoryDTO();
        inviteVehicleDealerAllocateHistoryDTO.setVin("vin");
        inviteVehicleDealerAllocateHistoryDTO.setLicensePlateNum("licensePlateNum");
        inviteVehicleDealerAllocateHistoryDTO.setName("name");
        inviteVehicleDealerAllocateHistoryDTO.setDealerName("lastDealerName");
        inviteVehicleDealerAllocateHistoryDTO.setDealerCode("lastDealerCode");
        inviteVehicleDealerAllocateDTO.setVehicleList(Arrays.asList(inviteVehicleDealerAllocateHistoryDTO));
        inviteVehicleDealerAllocateDTO.setDealerName("dealerName");
        inviteVehicleDealerAllocateDTO.setDealerCode("dealerCode");

        when(mockInviteVehicleDealerAllocateMapper.selectPageBySql(any(Page.class),
                any(InviteVehicleDealerAllocatePO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<InviteVehicleDealerAllocateDTO> result = inviteVehicleDealerAllocateServiceImplUnderTest.selectPageBysql(
                page, inviteVehicleDealerAllocateDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final InviteVehicleDealerAllocateDTO inviteVehicleDealerAllocateDTO = new InviteVehicleDealerAllocateDTO();
        final InviteVehicleDealerAllocateHistoryDTO inviteVehicleDealerAllocateHistoryDTO = new InviteVehicleDealerAllocateHistoryDTO();
        inviteVehicleDealerAllocateHistoryDTO.setVin("vin");
        inviteVehicleDealerAllocateHistoryDTO.setLicensePlateNum("licensePlateNum");
        inviteVehicleDealerAllocateHistoryDTO.setName("name");
        inviteVehicleDealerAllocateHistoryDTO.setDealerName("lastDealerName");
        inviteVehicleDealerAllocateHistoryDTO.setDealerCode("lastDealerCode");
        inviteVehicleDealerAllocateDTO.setVehicleList(Arrays.asList(inviteVehicleDealerAllocateHistoryDTO));
        inviteVehicleDealerAllocateDTO.setDealerName("dealerName");
        inviteVehicleDealerAllocateDTO.setDealerCode("dealerCode");

        // Configure InviteVehicleDealerAllocateMapper.selectListBySql(...).
        final InviteVehicleDealerAllocatePO inviteVehicleDealerAllocatePO = new InviteVehicleDealerAllocatePO();
        inviteVehicleDealerAllocatePO.setCreatedBy("createdBy");
        inviteVehicleDealerAllocatePO.setUpdatedBy("updatedBy");
        inviteVehicleDealerAllocatePO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleDealerAllocatePO.setVin("vin");
        inviteVehicleDealerAllocatePO.setDealerName("dealerName");
        inviteVehicleDealerAllocatePO.setDealerCode("dealerCode");
        final List<InviteVehicleDealerAllocatePO> inviteVehicleDealerAllocatePOS = Arrays.asList(
                inviteVehicleDealerAllocatePO);
        when(mockInviteVehicleDealerAllocateMapper.selectListBySql(
                any(InviteVehicleDealerAllocatePO.class))).thenReturn(inviteVehicleDealerAllocatePOS);

        // Run the test
        final List<InviteVehicleDealerAllocateDTO> result = inviteVehicleDealerAllocateServiceImplUnderTest.selectListBySql(
                inviteVehicleDealerAllocateDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_InviteVehicleDealerAllocateMapperReturnsNoItems() {
        // Setup
        final InviteVehicleDealerAllocateDTO inviteVehicleDealerAllocateDTO = new InviteVehicleDealerAllocateDTO();
        final InviteVehicleDealerAllocateHistoryDTO inviteVehicleDealerAllocateHistoryDTO = new InviteVehicleDealerAllocateHistoryDTO();
        inviteVehicleDealerAllocateHistoryDTO.setVin("vin");
        inviteVehicleDealerAllocateHistoryDTO.setLicensePlateNum("licensePlateNum");
        inviteVehicleDealerAllocateHistoryDTO.setName("name");
        inviteVehicleDealerAllocateHistoryDTO.setDealerName("lastDealerName");
        inviteVehicleDealerAllocateHistoryDTO.setDealerCode("lastDealerCode");
        inviteVehicleDealerAllocateDTO.setVehicleList(Arrays.asList(inviteVehicleDealerAllocateHistoryDTO));
        inviteVehicleDealerAllocateDTO.setDealerName("dealerName");
        inviteVehicleDealerAllocateDTO.setDealerCode("dealerCode");

        when(mockInviteVehicleDealerAllocateMapper.selectListBySql(
                any(InviteVehicleDealerAllocatePO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteVehicleDealerAllocateDTO> result = inviteVehicleDealerAllocateServiceImplUnderTest.selectListBySql(
                inviteVehicleDealerAllocateDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure InviteVehicleDealerAllocateMapper.selectById(...).
        final InviteVehicleDealerAllocatePO inviteVehicleDealerAllocatePO = new InviteVehicleDealerAllocatePO();
        inviteVehicleDealerAllocatePO.setCreatedBy("createdBy");
        inviteVehicleDealerAllocatePO.setUpdatedBy("updatedBy");
        inviteVehicleDealerAllocatePO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleDealerAllocatePO.setVin("vin");
        inviteVehicleDealerAllocatePO.setDealerName("dealerName");
        inviteVehicleDealerAllocatePO.setDealerCode("dealerCode");
        when(mockInviteVehicleDealerAllocateMapper.selectById(0L)).thenReturn(inviteVehicleDealerAllocatePO);

        // Run the test
        final InviteVehicleDealerAllocateDTO result = inviteVehicleDealerAllocateServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_InviteVehicleDealerAllocateMapperReturnsNull() {
        // Setup
        when(mockInviteVehicleDealerAllocateMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inviteVehicleDealerAllocateServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final InviteVehicleDealerAllocateDTO inviteVehicleDealerAllocateDTO = new InviteVehicleDealerAllocateDTO();
        final InviteVehicleDealerAllocateHistoryDTO inviteVehicleDealerAllocateHistoryDTO = new InviteVehicleDealerAllocateHistoryDTO();
        inviteVehicleDealerAllocateHistoryDTO.setVin("vin");
        inviteVehicleDealerAllocateHistoryDTO.setLicensePlateNum("licensePlateNum");
        inviteVehicleDealerAllocateHistoryDTO.setName("name");
        inviteVehicleDealerAllocateHistoryDTO.setDealerName("lastDealerName");
        inviteVehicleDealerAllocateHistoryDTO.setDealerCode("lastDealerCode");
        inviteVehicleDealerAllocateDTO.setVehicleList(Arrays.asList(inviteVehicleDealerAllocateHistoryDTO));
        inviteVehicleDealerAllocateDTO.setDealerName("dealerName");
        inviteVehicleDealerAllocateDTO.setDealerCode("dealerCode");

        when(mockInviteVehicleDealerAllocateMapper.insert(any(InviteVehicleDealerAllocatePO.class))).thenReturn(0);

        // Run the test
        final int result = inviteVehicleDealerAllocateServiceImplUnderTest.insert(inviteVehicleDealerAllocateDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final InviteVehicleDealerAllocateDTO inviteVehicleDealerAllocateDTO = new InviteVehicleDealerAllocateDTO();
        final InviteVehicleDealerAllocateHistoryDTO inviteVehicleDealerAllocateHistoryDTO = new InviteVehicleDealerAllocateHistoryDTO();
        inviteVehicleDealerAllocateHistoryDTO.setVin("vin");
        inviteVehicleDealerAllocateHistoryDTO.setLicensePlateNum("licensePlateNum");
        inviteVehicleDealerAllocateHistoryDTO.setName("name");
        inviteVehicleDealerAllocateHistoryDTO.setDealerName("lastDealerName");
        inviteVehicleDealerAllocateHistoryDTO.setDealerCode("lastDealerCode");
        inviteVehicleDealerAllocateDTO.setVehicleList(Arrays.asList(inviteVehicleDealerAllocateHistoryDTO));
        inviteVehicleDealerAllocateDTO.setDealerName("dealerName");
        inviteVehicleDealerAllocateDTO.setDealerCode("dealerCode");

        // Configure InviteVehicleDealerAllocateMapper.selectById(...).
        final InviteVehicleDealerAllocatePO inviteVehicleDealerAllocatePO = new InviteVehicleDealerAllocatePO();
        inviteVehicleDealerAllocatePO.setCreatedBy("createdBy");
        inviteVehicleDealerAllocatePO.setUpdatedBy("updatedBy");
        inviteVehicleDealerAllocatePO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleDealerAllocatePO.setVin("vin");
        inviteVehicleDealerAllocatePO.setDealerName("dealerName");
        inviteVehicleDealerAllocatePO.setDealerCode("dealerCode");
        when(mockInviteVehicleDealerAllocateMapper.selectById(0L)).thenReturn(inviteVehicleDealerAllocatePO);

        when(mockInviteVehicleDealerAllocateMapper.updateById(any(InviteVehicleDealerAllocatePO.class))).thenReturn(0);

        // Run the test
        final int result = inviteVehicleDealerAllocateServiceImplUnderTest.update(0L, inviteVehicleDealerAllocateDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testImportUpload() {
        // Setup
        final MultipartFile importFile = new MockMultipartFile("name", "content".getBytes());

        // Run the test
        final AjaxResponse result = inviteVehicleDealerAllocateServiceImplUnderTest.importUpload(importFile);

        // Verify the results
//        verify(mockReportCommonClient)
        mockReportCommonClient.sendMessageByRole("邀约管理", "message", "dealerCode", "KFJL");
    }

    @Test
    void testImportExcelData() {
        // Setup
        final List<Map<Integer, String>> importDataList = Arrays.asList(new HashMap<>());

        // Run the test
        final AjaxResponse result = inviteVehicleDealerAllocateServiceImplUnderTest.importExcelData(0L, "operationName",
                importDataList);

        // Verify the results
//        verify(mockReportCommonClient)
        mockReportCommonClient.sendMessageByRole("邀约管理", "message", "dealerCode", "KFJL");
    }
}
