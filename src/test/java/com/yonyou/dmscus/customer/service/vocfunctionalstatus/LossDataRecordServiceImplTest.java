package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domains.dto.basedata.ResponseDTO;
import com.yonyou.dmscus.customer.dao.voc.LossDataRecordMapper;
import com.yonyou.dmscus.customer.dto.LossDataRecordOss;
import com.yonyou.dmscus.customer.dto.LossDataRecordVo;
import com.yonyou.dmscus.customer.entity.dto.loss.LossDataRecordDto;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.LossDataRecordPo;
import com.yonyou.dmscus.customer.util.common.VolvoHttpUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LossDataRecordServiceImplTest {

    @Mock
    private VolvoHttpUtils mockVolvoHttpUtils;
    @Mock
    private RestTemplate mockRestTemplate;
    @Mock
    private LossDataRecordMapper mockLossDataRecordMapper;

    @InjectMocks
    private LossDataRecordServiceImpl lossDataRecordServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        lossDataRecordServiceImplUnderTest.volvoHttpUtils = mockVolvoHttpUtils;
        lossDataRecordServiceImplUnderTest.restTemplate = mockRestTemplate;
    }

    @Test
    void testSave() {
        // Setup
        final LossDataRecordPo po = new LossDataRecordPo();
        po.setIsDeleted(0);
        po.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        po.setVin("vin");
        po.setDealerCode("dealerCode");
        po.setOrderAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure LossDataRecordMapper.insert(...).
        final LossDataRecordPo entity = new LossDataRecordPo();
        entity.setIsDeleted(0);
        entity.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setVin("vin");
        entity.setDealerCode("dealerCode");
        entity.setOrderAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockLossDataRecordMapper.insert(entity)).thenReturn(0);

        // Run the test
        final int result = lossDataRecordServiceImplUnderTest.save(po);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectByVin() {
        // Setup
        final LossDataRecordPo expectedResult = new LossDataRecordPo();
        expectedResult.setIsDeleted(0);
        expectedResult.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setVin("vin");
        expectedResult.setDealerCode("dealerCode");
        expectedResult.setOrderAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure LossDataRecordMapper.selectOne(...).
        final LossDataRecordPo lossDataRecordPo = new LossDataRecordPo();
        lossDataRecordPo.setIsDeleted(0);
        lossDataRecordPo.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lossDataRecordPo.setVin("vin");
        lossDataRecordPo.setDealerCode("dealerCode");
        lossDataRecordPo.setOrderAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockLossDataRecordMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(lossDataRecordPo);

        // Run the test
        final LossDataRecordPo result = lossDataRecordServiceImplUnderTest.selectByVin("vin");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testUpdate() {
        // Setup
        final LossDataRecordPo po = new LossDataRecordPo();
        po.setIsDeleted(0);
        po.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        po.setVin("vin");
        po.setDealerCode("dealerCode");
        po.setOrderAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure LossDataRecordMapper.updateById(...).
        final LossDataRecordPo entity = new LossDataRecordPo();
        entity.setIsDeleted(0);
        entity.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setVin("vin");
        entity.setDealerCode("dealerCode");
        entity.setOrderAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockLossDataRecordMapper.updateById(entity)).thenReturn(0);

        // Run the test
        final int result = lossDataRecordServiceImplUnderTest.update(po);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testInsertList() {
        // Setup
        final LossDataRecordPo lossDataRecordPo = new LossDataRecordPo();
        lossDataRecordPo.setIsDeleted(0);
        lossDataRecordPo.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lossDataRecordPo.setVin("vin");
        lossDataRecordPo.setDealerCode("dealerCode");
        lossDataRecordPo.setOrderAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<LossDataRecordPo> pos = Arrays.asList(lossDataRecordPo);

        // Run the test
        lossDataRecordServiceImplUnderTest.insertList(pos);

        // Verify the results
        // Confirm LossDataRecordMapper.insertList(...).
        final LossDataRecordPo lossDataRecordPo1 = new LossDataRecordPo();
        lossDataRecordPo1.setIsDeleted(0);
        lossDataRecordPo1.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lossDataRecordPo1.setVin("vin");
        lossDataRecordPo1.setDealerCode("dealerCode");
        lossDataRecordPo1.setOrderAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<LossDataRecordPo> list = Arrays.asList(lossDataRecordPo1);
        verify(mockLossDataRecordMapper).insertList(list);
    }

    @Test
    void testExportExcel() {
        // Setup
        final LossDataRecordVo dto = new LossDataRecordVo();
        dto.setVin("vin");
        dto.setCurrentPage(0);
        dto.setPageSize(0);
        dto.setDealerCode("dealerCode");
        dto.setIndexFactory(0);

        // Configure LossDataRecordMapper.exportExcel(...).
        final LossDataRecordVo dto1 = new LossDataRecordVo();
        dto1.setVin("vin");
        dto1.setCurrentPage(0);
        dto1.setPageSize(0);
        dto1.setDealerCode("dealerCode");
        dto1.setIndexFactory(0);
        when(mockLossDataRecordMapper.exportExcel(any(Page.class), eq(dto1)))
                .thenReturn(Arrays.asList(new HashMap<>()));

        // Run the test
        final List<Map> result = lossDataRecordServiceImplUnderTest.exportExcel(dto);

        // Verify the results
    }

    @Test
    void testExportExcel_LossDataRecordMapperReturnsNoItems() {
        // Setup
        final LossDataRecordVo dto = new LossDataRecordVo();
        dto.setVin("vin");
        dto.setCurrentPage(0);
        dto.setPageSize(0);
        dto.setDealerCode("dealerCode");
        dto.setIndexFactory(0);

        // Configure LossDataRecordMapper.exportExcel(...).
        final LossDataRecordVo dto1 = new LossDataRecordVo();
        dto1.setVin("vin");
        dto1.setCurrentPage(0);
        dto1.setPageSize(0);
        dto1.setDealerCode("dealerCode");
        dto1.setIndexFactory(0);
        when(mockLossDataRecordMapper.exportExcel(any(Page.class), eq(dto1))).thenReturn(Collections.emptyList());

        // Run the test
        final List<Map> result = lossDataRecordServiceImplUnderTest.exportExcel(dto);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testExportColumnList() {
        // Setup
        final Map param = new HashMap<>();

        // Configure VolvoHttpUtils.getHeaders(...).
        final HttpHeaders httpHeaders = new HttpHeaders(new LinkedMultiValueMap(new HashMap<>()));
        when(mockVolvoHttpUtils.getHeaders()).thenReturn(httpHeaders);

        // Configure RestTemplate.exchange(...).
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("error");
        responseDTO.setReturnMessage("message");
        responseDTO.setData(null);
        responseDTO.setCause("cause");
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        when(mockRestTemplate.exchange("url", HttpMethod.POST, new HttpEntity<>(new HashMap<>(), new HttpHeaders()),
                ResponseDTO.class)).thenReturn(responseDTOResponseEntity);

        // Run the test
        final ResponseDTO result = lossDataRecordServiceImplUnderTest.exportColumnList("url", param);

        // Verify the results
    }

    @Test
    void testExportColumnList_RestTemplateThrowsRestClientException() {
        // Setup
        final Map param = new HashMap<>();

        // Configure VolvoHttpUtils.getHeaders(...).
        final HttpHeaders httpHeaders = new HttpHeaders(new LinkedMultiValueMap(new HashMap<>()));
        when(mockVolvoHttpUtils.getHeaders()).thenReturn(httpHeaders);

        when(mockRestTemplate.exchange("url", HttpMethod.POST, new HttpEntity<>(new HashMap<>(), new HttpHeaders()),
                ResponseDTO.class)).thenThrow(RestClientException.class);

        // Run the test
        final ResponseDTO result = lossDataRecordServiceImplUnderTest.exportColumnList("url", param);

        // Verify the results
    }
}
