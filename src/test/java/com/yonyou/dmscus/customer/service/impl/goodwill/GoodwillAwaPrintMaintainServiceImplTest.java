package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillAwaPrintMaintainMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAwaPrintMaintainDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAwaPrintMaintainPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillAwaPrintMaintainServiceImplTest {

    @Mock
    private GoodwillAwaPrintMaintainMapper mockGoodwillAwaPrintMaintainMapper;

    private GoodwillAwaPrintMaintainServiceImpl goodwillAwaPrintMaintainServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        goodwillAwaPrintMaintainServiceImplUnderTest = new GoodwillAwaPrintMaintainServiceImpl();
        goodwillAwaPrintMaintainServiceImplUnderTest.goodwillAwaPrintMaintainMapper = mockGoodwillAwaPrintMaintainMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO = new GoodwillAwaPrintMaintainDTO();
        goodwillAwaPrintMaintainDTO.setAppId("appId");
        goodwillAwaPrintMaintainDTO.setOwnerCode("ownerCode");
        goodwillAwaPrintMaintainDTO.setOwnerParCode("ownerParCode");
        goodwillAwaPrintMaintainDTO.setOrgId(0);
        goodwillAwaPrintMaintainDTO.setId(0L);

        // Configure GoodwillAwaPrintMaintainMapper.selectPageBySql(...).
        final GoodwillAwaPrintMaintainPO goodwillAwaPrintMaintainPO = new GoodwillAwaPrintMaintainPO();
        goodwillAwaPrintMaintainPO.setAppId("appId");
        goodwillAwaPrintMaintainPO.setOwnerCode("ownerCode");
        goodwillAwaPrintMaintainPO.setOwnerParCode("ownerParCode");
        goodwillAwaPrintMaintainPO.setOrgId(0);
        goodwillAwaPrintMaintainPO.setId(0L);
        final List<GoodwillAwaPrintMaintainPO> goodwillAwaPrintMaintainPOS = Arrays.asList(goodwillAwaPrintMaintainPO);
        when(mockGoodwillAwaPrintMaintainMapper.selectPageBySql(any(Page.class),
                any(GoodwillAwaPrintMaintainPO.class))).thenReturn(goodwillAwaPrintMaintainPOS);

        // Run the test
        final IPage<GoodwillAwaPrintMaintainDTO> result = goodwillAwaPrintMaintainServiceImplUnderTest.selectPageBysql(
                page, goodwillAwaPrintMaintainDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillAwaPrintMaintainMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO = new GoodwillAwaPrintMaintainDTO();
        goodwillAwaPrintMaintainDTO.setAppId("appId");
        goodwillAwaPrintMaintainDTO.setOwnerCode("ownerCode");
        goodwillAwaPrintMaintainDTO.setOwnerParCode("ownerParCode");
        goodwillAwaPrintMaintainDTO.setOrgId(0);
        goodwillAwaPrintMaintainDTO.setId(0L);

        when(mockGoodwillAwaPrintMaintainMapper.selectPageBySql(any(Page.class),
                any(GoodwillAwaPrintMaintainPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillAwaPrintMaintainDTO> result = goodwillAwaPrintMaintainServiceImplUnderTest.selectPageBysql(
                page, goodwillAwaPrintMaintainDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO = new GoodwillAwaPrintMaintainDTO();
        goodwillAwaPrintMaintainDTO.setAppId("appId");
        goodwillAwaPrintMaintainDTO.setOwnerCode("ownerCode");
        goodwillAwaPrintMaintainDTO.setOwnerParCode("ownerParCode");
        goodwillAwaPrintMaintainDTO.setOrgId(0);
        goodwillAwaPrintMaintainDTO.setId(0L);

        // Configure GoodwillAwaPrintMaintainMapper.selectListBySql(...).
        final GoodwillAwaPrintMaintainPO goodwillAwaPrintMaintainPO = new GoodwillAwaPrintMaintainPO();
        goodwillAwaPrintMaintainPO.setAppId("appId");
        goodwillAwaPrintMaintainPO.setOwnerCode("ownerCode");
        goodwillAwaPrintMaintainPO.setOwnerParCode("ownerParCode");
        goodwillAwaPrintMaintainPO.setOrgId(0);
        goodwillAwaPrintMaintainPO.setId(0L);
        final List<GoodwillAwaPrintMaintainPO> goodwillAwaPrintMaintainPOS = Arrays.asList(goodwillAwaPrintMaintainPO);
        when(mockGoodwillAwaPrintMaintainMapper.selectListBySql(any(GoodwillAwaPrintMaintainPO.class)))
                .thenReturn(goodwillAwaPrintMaintainPOS);

        // Run the test
        final List<GoodwillAwaPrintMaintainDTO> result = goodwillAwaPrintMaintainServiceImplUnderTest.selectListBySql(
                goodwillAwaPrintMaintainDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillAwaPrintMaintainMapperReturnsNoItems() {
        // Setup
        final GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO = new GoodwillAwaPrintMaintainDTO();
        goodwillAwaPrintMaintainDTO.setAppId("appId");
        goodwillAwaPrintMaintainDTO.setOwnerCode("ownerCode");
        goodwillAwaPrintMaintainDTO.setOwnerParCode("ownerParCode");
        goodwillAwaPrintMaintainDTO.setOrgId(0);
        goodwillAwaPrintMaintainDTO.setId(0L);

        when(mockGoodwillAwaPrintMaintainMapper.selectListBySql(any(GoodwillAwaPrintMaintainPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillAwaPrintMaintainDTO> result = goodwillAwaPrintMaintainServiceImplUnderTest.selectListBySql(
                goodwillAwaPrintMaintainDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure GoodwillAwaPrintMaintainMapper.selectById(...).
        final GoodwillAwaPrintMaintainPO goodwillAwaPrintMaintainPO = new GoodwillAwaPrintMaintainPO();
        goodwillAwaPrintMaintainPO.setAppId("appId");
        goodwillAwaPrintMaintainPO.setOwnerCode("ownerCode");
        goodwillAwaPrintMaintainPO.setOwnerParCode("ownerParCode");
        goodwillAwaPrintMaintainPO.setOrgId(0);
        goodwillAwaPrintMaintainPO.setId(0L);
        when(mockGoodwillAwaPrintMaintainMapper.selectById(0L)).thenReturn(goodwillAwaPrintMaintainPO);

        // Run the test
        final GoodwillAwaPrintMaintainDTO result = goodwillAwaPrintMaintainServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillAwaPrintMaintainMapperReturnsNull() {
        // Setup
        when(mockGoodwillAwaPrintMaintainMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillAwaPrintMaintainServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO = new GoodwillAwaPrintMaintainDTO();
        goodwillAwaPrintMaintainDTO.setAppId("appId");
        goodwillAwaPrintMaintainDTO.setOwnerCode("ownerCode");
        goodwillAwaPrintMaintainDTO.setOwnerParCode("ownerParCode");
        goodwillAwaPrintMaintainDTO.setOrgId(0);
        goodwillAwaPrintMaintainDTO.setId(0L);

        when(mockGoodwillAwaPrintMaintainMapper.insert(any(GoodwillAwaPrintMaintainPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillAwaPrintMaintainServiceImplUnderTest.insert(goodwillAwaPrintMaintainDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO = new GoodwillAwaPrintMaintainDTO();
        goodwillAwaPrintMaintainDTO.setAppId("appId");
        goodwillAwaPrintMaintainDTO.setOwnerCode("ownerCode");
        goodwillAwaPrintMaintainDTO.setOwnerParCode("ownerParCode");
        goodwillAwaPrintMaintainDTO.setOrgId(0);
        goodwillAwaPrintMaintainDTO.setId(0L);

        // Configure GoodwillAwaPrintMaintainMapper.selectById(...).
        final GoodwillAwaPrintMaintainPO goodwillAwaPrintMaintainPO = new GoodwillAwaPrintMaintainPO();
        goodwillAwaPrintMaintainPO.setAppId("appId");
        goodwillAwaPrintMaintainPO.setOwnerCode("ownerCode");
        goodwillAwaPrintMaintainPO.setOwnerParCode("ownerParCode");
        goodwillAwaPrintMaintainPO.setOrgId(0);
        goodwillAwaPrintMaintainPO.setId(0L);
        when(mockGoodwillAwaPrintMaintainMapper.selectById(0L)).thenReturn(goodwillAwaPrintMaintainPO);

        when(mockGoodwillAwaPrintMaintainMapper.updateById(any(GoodwillAwaPrintMaintainPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillAwaPrintMaintainServiceImplUnderTest.update(0L, goodwillAwaPrintMaintainDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectAwaPrintMaintainInfo() {
        // Setup
        final GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO = new GoodwillAwaPrintMaintainDTO();
        goodwillAwaPrintMaintainDTO.setAppId("appId");
        goodwillAwaPrintMaintainDTO.setOwnerCode("ownerCode");
        goodwillAwaPrintMaintainDTO.setOwnerParCode("ownerParCode");
        goodwillAwaPrintMaintainDTO.setOrgId(0);
        goodwillAwaPrintMaintainDTO.setId(0L);

        when(mockGoodwillAwaPrintMaintainMapper.selectAwaPrintMaintainInfo(
                any(GoodwillAwaPrintMaintainPO.class))).thenReturn(Arrays.asList(new HashMap<>()));

        // Run the test
        final List<Map> result = goodwillAwaPrintMaintainServiceImplUnderTest.selectAwaPrintMaintainInfo(
                goodwillAwaPrintMaintainDTO);

        // Verify the results
    }

    @Test
    void testSelectAwaPrintMaintainInfo_GoodwillAwaPrintMaintainMapperReturnsNoItems() {
        // Setup
        final GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO = new GoodwillAwaPrintMaintainDTO();
        goodwillAwaPrintMaintainDTO.setAppId("appId");
        goodwillAwaPrintMaintainDTO.setOwnerCode("ownerCode");
        goodwillAwaPrintMaintainDTO.setOwnerParCode("ownerParCode");
        goodwillAwaPrintMaintainDTO.setOrgId(0);
        goodwillAwaPrintMaintainDTO.setId(0L);

        when(mockGoodwillAwaPrintMaintainMapper.selectAwaPrintMaintainInfo(
                any(GoodwillAwaPrintMaintainPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<Map> result = goodwillAwaPrintMaintainServiceImplUnderTest.selectAwaPrintMaintainInfo(
                goodwillAwaPrintMaintainDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testAddOrEditAwaPrint() {
        // Setup
        final GoodwillAwaPrintMaintainDTO goodwillAwaPrintMaintainDTO = new GoodwillAwaPrintMaintainDTO();
        goodwillAwaPrintMaintainDTO.setAppId("appId");
        goodwillAwaPrintMaintainDTO.setOwnerCode("ownerCode");
        goodwillAwaPrintMaintainDTO.setOwnerParCode("ownerParCode");
        goodwillAwaPrintMaintainDTO.setOrgId(0);
        goodwillAwaPrintMaintainDTO.setId(0L);

        // Configure GoodwillAwaPrintMaintainMapper.selectById(...).
        final GoodwillAwaPrintMaintainPO goodwillAwaPrintMaintainPO = new GoodwillAwaPrintMaintainPO();
        goodwillAwaPrintMaintainPO.setAppId("appId");
        goodwillAwaPrintMaintainPO.setOwnerCode("ownerCode");
        goodwillAwaPrintMaintainPO.setOwnerParCode("ownerParCode");
        goodwillAwaPrintMaintainPO.setOrgId(0);
        goodwillAwaPrintMaintainPO.setId(0L);
        when(mockGoodwillAwaPrintMaintainMapper.selectById(0L)).thenReturn(goodwillAwaPrintMaintainPO);

        // Run the test
        final int result = goodwillAwaPrintMaintainServiceImplUnderTest.addOrEditAwaPrint(goodwillAwaPrintMaintainDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockGoodwillAwaPrintMaintainMapper).insert(any(GoodwillAwaPrintMaintainPO.class));
        verify(mockGoodwillAwaPrintMaintainMapper).updateById(any(GoodwillAwaPrintMaintainPO.class));
    }
}
