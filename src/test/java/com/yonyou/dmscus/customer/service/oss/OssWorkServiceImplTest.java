package com.yonyou.dmscus.customer.service.oss;

import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusLogPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusRecordPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataLogPo;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataRecordPo;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocFunctionalStatusLogService;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocFunctionalStatusRecordService;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocWarningDataLogService;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocWarningDataRecordService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OssWorkServiceImplTest {

    @Mock
    private VocFunctionalStatusLogService mockLogService;
    @Mock
    private VocFunctionalStatusRecordService mockRecordService;
    @Mock
    private VocWarningDataLogService mockWarningDataLogSrevice;
    @Mock
    private VocWarningDataRecordService mockWarningDataRecordService;

    private OssWorkServiceImpl ossWorkServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        ossWorkServiceImplUnderTest = new OssWorkServiceImpl();
        ossWorkServiceImplUnderTest.logService = mockLogService;
        ossWorkServiceImplUnderTest.recordService = mockRecordService;
        ossWorkServiceImplUnderTest.warningDataLogSrevice = mockWarningDataLogSrevice;
        ossWorkServiceImplUnderTest.warningDataRecordService = mockWarningDataRecordService;
    }

    @Test
    void testInsertListLog() {
        // Setup
        final VocFunctionalStatusLogPO vocFunctionalStatusLogPO = new VocFunctionalStatusLogPO();
        vocFunctionalStatusLogPO.setDt("dt");
        vocFunctionalStatusLogPO.setVin("vin");
        vocFunctionalStatusLogPO.setActivatedState("activatedState");
        vocFunctionalStatusLogPO.setSubscriptionStartdate("subscriptionStartdate");
        vocFunctionalStatusLogPO.setSubscriptionEnddate("subscriptionEnddate");
        final List<VocFunctionalStatusLogPO> logPOSList = Arrays.asList(vocFunctionalStatusLogPO);

        // Configure VocFunctionalStatusLogService.insertList(...).
        final VocFunctionalStatusLogPO vocFunctionalStatusLogPO1 = new VocFunctionalStatusLogPO();
        vocFunctionalStatusLogPO1.setDt("dt");
        vocFunctionalStatusLogPO1.setVin("vin");
        vocFunctionalStatusLogPO1.setActivatedState("activatedState");
        vocFunctionalStatusLogPO1.setSubscriptionStartdate("subscriptionStartdate");
        vocFunctionalStatusLogPO1.setSubscriptionEnddate("subscriptionEnddate");
        final List<VocFunctionalStatusLogPO> logPOSList1 = Arrays.asList(vocFunctionalStatusLogPO1);
        when(mockLogService.insertList(logPOSList1)).thenReturn(0);

        // Run the test
        ossWorkServiceImplUnderTest.insertListLog(logPOSList);

        // Verify the results
    }

    @Test
    void testInsertList() {
        // Setup
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO.setUpdateTime("updateTime");
        vocFunctionalStatusRecordPO.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO.setVin("vin");
        vocFunctionalStatusRecordPO.setIsExecute(0);
        vocFunctionalStatusRecordPO.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> list = Arrays.asList(vocFunctionalStatusRecordPO);

        // Configure VocFunctionalStatusRecordService.insertList(...).
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO1 = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO1.setUpdateTime("updateTime");
        vocFunctionalStatusRecordPO1.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO1.setVin("vin");
        vocFunctionalStatusRecordPO1.setIsExecute(0);
        vocFunctionalStatusRecordPO1.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> list1 = Arrays.asList(vocFunctionalStatusRecordPO1);
        when(mockRecordService.insertList(list1)).thenReturn(0);

        // Run the test
        ossWorkServiceImplUnderTest.insertList(list);

        // Verify the results
    }

    @Test
    void testInsertListWarningDataLog() {
        // Setup
        final VocWarningDataLogPo vocWarningDataLogPo = new VocWarningDataLogPo();
        vocWarningDataLogPo.setDt("dt");
        vocWarningDataLogPo.setVin("vin");
        vocWarningDataLogPo.setStatusName("statusName");
        vocWarningDataLogPo.setStatusValue("statusValue");
        vocWarningDataLogPo.setModel("model");
        final List<VocWarningDataLogPo> logPOSList = Arrays.asList(vocWarningDataLogPo);

        // Configure VocWarningDataLogService.insertList(...).
        final VocWarningDataLogPo vocWarningDataLogPo1 = new VocWarningDataLogPo();
        vocWarningDataLogPo1.setDt("dt");
        vocWarningDataLogPo1.setVin("vin");
        vocWarningDataLogPo1.setStatusName("statusName");
        vocWarningDataLogPo1.setStatusValue("statusValue");
        vocWarningDataLogPo1.setModel("model");
        final List<VocWarningDataLogPo> logPOSList1 = Arrays.asList(vocWarningDataLogPo1);
        when(mockWarningDataLogSrevice.insertList(logPOSList1)).thenReturn(0);

        // Run the test
        ossWorkServiceImplUnderTest.insertListWarningDataLog(logPOSList);

        // Verify the results
    }

    @Test
    void testSelectVocFunctionalStatusLogLog() {
        // Setup
        final VocFunctionalStatusLogPO vocFunctionalStatusLogPO = new VocFunctionalStatusLogPO();
        vocFunctionalStatusLogPO.setDt("dt");
        vocFunctionalStatusLogPO.setVin("vin");
        vocFunctionalStatusLogPO.setActivatedState("activatedState");
        vocFunctionalStatusLogPO.setSubscriptionStartdate("subscriptionStartdate");
        vocFunctionalStatusLogPO.setSubscriptionEnddate("subscriptionEnddate");
        final List<VocFunctionalStatusLogPO> expectedResult = Arrays.asList(vocFunctionalStatusLogPO);

        // Configure VocFunctionalStatusLogService.selectListBydt(...).
        final VocFunctionalStatusLogPO vocFunctionalStatusLogPO1 = new VocFunctionalStatusLogPO();
        vocFunctionalStatusLogPO1.setDt("dt");
        vocFunctionalStatusLogPO1.setVin("vin");
        vocFunctionalStatusLogPO1.setActivatedState("activatedState");
        vocFunctionalStatusLogPO1.setSubscriptionStartdate("subscriptionStartdate");
        vocFunctionalStatusLogPO1.setSubscriptionEnddate("subscriptionEnddate");
        final List<VocFunctionalStatusLogPO> vocFunctionalStatusLogPOS = Arrays.asList(vocFunctionalStatusLogPO1);
        when(mockLogService.selectListBydt("data", 0, 0)).thenReturn(vocFunctionalStatusLogPOS);

        // Run the test
        final List<VocFunctionalStatusLogPO> result = ossWorkServiceImplUnderTest.selectVocFunctionalStatusLogLog(
                "data", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectVocFunctionalStatusLogLog_VocFunctionalStatusLogServiceReturnsNoItems() {
        // Setup
        when(mockLogService.selectListBydt("data", 0, 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<VocFunctionalStatusLogPO> result = ossWorkServiceImplUnderTest.selectVocFunctionalStatusLogLog(
                "data", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testUpdateRecord() {
        // Setup
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO.setUpdateTime("updateTime");
        vocFunctionalStatusRecordPO.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO.setVin("vin");
        vocFunctionalStatusRecordPO.setIsExecute(0);
        vocFunctionalStatusRecordPO.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> updateList = Arrays.asList(vocFunctionalStatusRecordPO);

        // Configure VocFunctionalStatusRecordService.updateList(...).
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO1 = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO1.setUpdateTime("updateTime");
        vocFunctionalStatusRecordPO1.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO1.setVin("vin");
        vocFunctionalStatusRecordPO1.setIsExecute(0);
        vocFunctionalStatusRecordPO1.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> updateList1 = Arrays.asList(vocFunctionalStatusRecordPO1);
        when(mockRecordService.updateList(updateList1)).thenReturn(0);

        // Run the test
        ossWorkServiceImplUnderTest.updateRecord(updateList);

        // Verify the results
    }

    @Test
    void testSelectListStatusRecord() {
        // Setup
        final VocFunctionalStatusLogPO vocFunctionalStatusLogPO = new VocFunctionalStatusLogPO();
        vocFunctionalStatusLogPO.setDt("dt");
        vocFunctionalStatusLogPO.setVin("vin");
        vocFunctionalStatusLogPO.setActivatedState("activatedState");
        vocFunctionalStatusLogPO.setSubscriptionStartdate("subscriptionStartdate");
        vocFunctionalStatusLogPO.setSubscriptionEnddate("subscriptionEnddate");
        final List<VocFunctionalStatusLogPO> x = Arrays.asList(vocFunctionalStatusLogPO);
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO.setUpdateTime("updateTime");
        vocFunctionalStatusRecordPO.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO.setVin("vin");
        vocFunctionalStatusRecordPO.setIsExecute(0);
        vocFunctionalStatusRecordPO.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> expectedResult = Arrays.asList(vocFunctionalStatusRecordPO);

        // Configure VocFunctionalStatusRecordService.selectListStatusRecord(...).
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO1 = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO1.setUpdateTime("updateTime");
        vocFunctionalStatusRecordPO1.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO1.setVin("vin");
        vocFunctionalStatusRecordPO1.setIsExecute(0);
        vocFunctionalStatusRecordPO1.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> vocFunctionalStatusRecordPOS = Arrays.asList(
                vocFunctionalStatusRecordPO1);
        final VocFunctionalStatusLogPO vocFunctionalStatusLogPO1 = new VocFunctionalStatusLogPO();
        vocFunctionalStatusLogPO1.setDt("dt");
        vocFunctionalStatusLogPO1.setVin("vin");
        vocFunctionalStatusLogPO1.setActivatedState("activatedState");
        vocFunctionalStatusLogPO1.setSubscriptionStartdate("subscriptionStartdate");
        vocFunctionalStatusLogPO1.setSubscriptionEnddate("subscriptionEnddate");
        final List<VocFunctionalStatusLogPO> x1 = Arrays.asList(vocFunctionalStatusLogPO1);
        when(mockRecordService.selectListStatusRecord(x1)).thenReturn(vocFunctionalStatusRecordPOS);

        // Run the test
        final List<VocFunctionalStatusRecordPO> result = ossWorkServiceImplUnderTest.selectListStatusRecord(x);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectListStatusRecord_VocFunctionalStatusRecordServiceReturnsNoItems() {
        // Setup
        final VocFunctionalStatusLogPO vocFunctionalStatusLogPO = new VocFunctionalStatusLogPO();
        vocFunctionalStatusLogPO.setDt("dt");
        vocFunctionalStatusLogPO.setVin("vin");
        vocFunctionalStatusLogPO.setActivatedState("activatedState");
        vocFunctionalStatusLogPO.setSubscriptionStartdate("subscriptionStartdate");
        vocFunctionalStatusLogPO.setSubscriptionEnddate("subscriptionEnddate");
        final List<VocFunctionalStatusLogPO> x = Arrays.asList(vocFunctionalStatusLogPO);

        // Configure VocFunctionalStatusRecordService.selectListStatusRecord(...).
        final VocFunctionalStatusLogPO vocFunctionalStatusLogPO1 = new VocFunctionalStatusLogPO();
        vocFunctionalStatusLogPO1.setDt("dt");
        vocFunctionalStatusLogPO1.setVin("vin");
        vocFunctionalStatusLogPO1.setActivatedState("activatedState");
        vocFunctionalStatusLogPO1.setSubscriptionStartdate("subscriptionStartdate");
        vocFunctionalStatusLogPO1.setSubscriptionEnddate("subscriptionEnddate");
        final List<VocFunctionalStatusLogPO> x1 = Arrays.asList(vocFunctionalStatusLogPO1);
        when(mockRecordService.selectListStatusRecord(x1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<VocFunctionalStatusRecordPO> result = ossWorkServiceImplUnderTest.selectListStatusRecord(x);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectWarningdailyLog() {
        // Setup
        final VocWarningDataLogPo vocWarningDataLogPo = new VocWarningDataLogPo();
        vocWarningDataLogPo.setDt("dt");
        vocWarningDataLogPo.setVin("vin");
        vocWarningDataLogPo.setStatusName("statusName");
        vocWarningDataLogPo.setStatusValue("statusValue");
        vocWarningDataLogPo.setModel("model");
        final List<VocWarningDataLogPo> expectedResult = Arrays.asList(vocWarningDataLogPo);

        // Configure VocWarningDataLogService.selectListBydt(...).
        final VocWarningDataLogPo vocWarningDataLogPo1 = new VocWarningDataLogPo();
        vocWarningDataLogPo1.setDt("dt");
        vocWarningDataLogPo1.setVin("vin");
        vocWarningDataLogPo1.setStatusName("statusName");
        vocWarningDataLogPo1.setStatusValue("statusValue");
        vocWarningDataLogPo1.setModel("model");
        final List<VocWarningDataLogPo> vocWarningDataLogPos = Arrays.asList(vocWarningDataLogPo1);
        when(mockWarningDataLogSrevice.selectListBydt("data", 0, 0)).thenReturn(vocWarningDataLogPos);

        // Run the test
        final List<VocWarningDataLogPo> result = ossWorkServiceImplUnderTest.selectWarningdailyLog("data", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectWarningdailyLog_VocWarningDataLogServiceReturnsNoItems() {
        // Setup
        when(mockWarningDataLogSrevice.selectListBydt("data", 0, 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<VocWarningDataLogPo> result = ossWorkServiceImplUnderTest.selectWarningdailyLog("data", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectListWarningDataRecord() {
        // Setup
        final VocWarningDataLogPo vocWarningDataLogPo = new VocWarningDataLogPo();
        vocWarningDataLogPo.setDt("dt");
        vocWarningDataLogPo.setVin("vin");
        vocWarningDataLogPo.setStatusName("statusName");
        vocWarningDataLogPo.setStatusValue("statusValue");
        vocWarningDataLogPo.setModel("model");
        final List<VocWarningDataLogPo> x = Arrays.asList(vocWarningDataLogPo);
        final VocWarningDataRecordPo vocWarningDataRecordPo = new VocWarningDataRecordPo();
        vocWarningDataRecordPo.setReportDate("reportDate");
        vocWarningDataRecordPo.setModifyTime("modifyTime");
        vocWarningDataRecordPo.setVin("vin");
        vocWarningDataRecordPo.setStatusName("statusName");
        vocWarningDataRecordPo.setStatusValue(0);
        final List<VocWarningDataRecordPo> expectedResult = Arrays.asList(vocWarningDataRecordPo);

        // Configure VocWarningDataRecordService.selectListWarningDataRecord(...).
        final VocWarningDataRecordPo vocWarningDataRecordPo1 = new VocWarningDataRecordPo();
        vocWarningDataRecordPo1.setReportDate("reportDate");
        vocWarningDataRecordPo1.setModifyTime("modifyTime");
        vocWarningDataRecordPo1.setVin("vin");
        vocWarningDataRecordPo1.setStatusName("statusName");
        vocWarningDataRecordPo1.setStatusValue(0);
        final List<VocWarningDataRecordPo> vocWarningDataRecordPos = Arrays.asList(vocWarningDataRecordPo1);
        final VocWarningDataLogPo vocWarningDataLogPo1 = new VocWarningDataLogPo();
        vocWarningDataLogPo1.setDt("dt");
        vocWarningDataLogPo1.setVin("vin");
        vocWarningDataLogPo1.setStatusName("statusName");
        vocWarningDataLogPo1.setStatusValue("statusValue");
        vocWarningDataLogPo1.setModel("model");
        final List<VocWarningDataLogPo> x1 = Arrays.asList(vocWarningDataLogPo1);
        when(mockWarningDataRecordService.selectListWarningDataRecord(x1)).thenReturn(vocWarningDataRecordPos);

        // Run the test
        final List<VocWarningDataRecordPo> result = ossWorkServiceImplUnderTest.selectListWarningDataRecord(x);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectListWarningDataRecord_VocWarningDataRecordServiceReturnsNoItems() {
        // Setup
        final VocWarningDataLogPo vocWarningDataLogPo = new VocWarningDataLogPo();
        vocWarningDataLogPo.setDt("dt");
        vocWarningDataLogPo.setVin("vin");
        vocWarningDataLogPo.setStatusName("statusName");
        vocWarningDataLogPo.setStatusValue("statusValue");
        vocWarningDataLogPo.setModel("model");
        final List<VocWarningDataLogPo> x = Arrays.asList(vocWarningDataLogPo);

        // Configure VocWarningDataRecordService.selectListWarningDataRecord(...).
        final VocWarningDataLogPo vocWarningDataLogPo1 = new VocWarningDataLogPo();
        vocWarningDataLogPo1.setDt("dt");
        vocWarningDataLogPo1.setVin("vin");
        vocWarningDataLogPo1.setStatusName("statusName");
        vocWarningDataLogPo1.setStatusValue("statusValue");
        vocWarningDataLogPo1.setModel("model");
        final List<VocWarningDataLogPo> x1 = Arrays.asList(vocWarningDataLogPo1);
        when(mockWarningDataRecordService.selectListWarningDataRecord(x1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<VocWarningDataRecordPo> result = ossWorkServiceImplUnderTest.selectListWarningDataRecord(x);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testInsertVocWarningDataRecordList() {
        // Setup
        final VocWarningDataRecordPo vocWarningDataRecordPo = new VocWarningDataRecordPo();
        vocWarningDataRecordPo.setReportDate("reportDate");
        vocWarningDataRecordPo.setModifyTime("modifyTime");
        vocWarningDataRecordPo.setVin("vin");
        vocWarningDataRecordPo.setStatusName("statusName");
        vocWarningDataRecordPo.setStatusValue(0);
        final List<VocWarningDataRecordPo> insertList = Arrays.asList(vocWarningDataRecordPo);

        // Configure VocWarningDataRecordService.insertList(...).
        final VocWarningDataRecordPo vocWarningDataRecordPo1 = new VocWarningDataRecordPo();
        vocWarningDataRecordPo1.setReportDate("reportDate");
        vocWarningDataRecordPo1.setModifyTime("modifyTime");
        vocWarningDataRecordPo1.setVin("vin");
        vocWarningDataRecordPo1.setStatusName("statusName");
        vocWarningDataRecordPo1.setStatusValue(0);
        final List<VocWarningDataRecordPo> insertList1 = Arrays.asList(vocWarningDataRecordPo1);
        when(mockWarningDataRecordService.insertList(insertList1)).thenReturn(0);

        // Run the test
        ossWorkServiceImplUnderTest.insertVocWarningDataRecordList(insertList);

        // Verify the results
    }

    @Test
    void testUpdateVocWarningDataRecordList() {
        // Setup
        final VocWarningDataRecordPo vocWarningDataRecordPo = new VocWarningDataRecordPo();
        vocWarningDataRecordPo.setReportDate("reportDate");
        vocWarningDataRecordPo.setModifyTime("modifyTime");
        vocWarningDataRecordPo.setVin("vin");
        vocWarningDataRecordPo.setStatusName("statusName");
        vocWarningDataRecordPo.setStatusValue(0);
        final List<VocWarningDataRecordPo> updateList = Arrays.asList(vocWarningDataRecordPo);

        // Configure VocWarningDataRecordService.updateList(...).
        final VocWarningDataRecordPo vocWarningDataRecordPo1 = new VocWarningDataRecordPo();
        vocWarningDataRecordPo1.setReportDate("reportDate");
        vocWarningDataRecordPo1.setModifyTime("modifyTime");
        vocWarningDataRecordPo1.setVin("vin");
        vocWarningDataRecordPo1.setStatusName("statusName");
        vocWarningDataRecordPo1.setStatusValue(0);
        final List<VocWarningDataRecordPo> updateList1 = Arrays.asList(vocWarningDataRecordPo1);
        when(mockWarningDataRecordService.updateList(updateList1)).thenReturn(0);

        // Run the test
        ossWorkServiceImplUnderTest.updateVocWarningDataRecordList(updateList);

        // Verify the results
    }

    @Test
    void testSelectVocFunctionalStatusRecordByModfiyTime() {
        // Setup
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO.setUpdateTime("updateTime");
        vocFunctionalStatusRecordPO.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO.setVin("vin");
        vocFunctionalStatusRecordPO.setIsExecute(0);
        vocFunctionalStatusRecordPO.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> expectedResult = Arrays.asList(vocFunctionalStatusRecordPO);

        // Configure VocFunctionalStatusRecordService.selectVocFunctionalStatusRecordByModfiyTime(...).
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO1 = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO1.setUpdateTime("updateTime");
        vocFunctionalStatusRecordPO1.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO1.setVin("vin");
        vocFunctionalStatusRecordPO1.setIsExecute(0);
        vocFunctionalStatusRecordPO1.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> vocFunctionalStatusRecordPOS = Arrays.asList(
                vocFunctionalStatusRecordPO1);
        when(mockRecordService.selectVocFunctionalStatusRecordByModfiyTime("dateTime", 0, 0))
                .thenReturn(vocFunctionalStatusRecordPOS);

        // Run the test
        final List<VocFunctionalStatusRecordPO> result = ossWorkServiceImplUnderTest.selectVocFunctionalStatusRecordByModfiyTime(
                "dateTime", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectVocFunctionalStatusRecordByModfiyTime_VocFunctionalStatusRecordServiceReturnsNoItems() {
        // Setup
        when(mockRecordService.selectVocFunctionalStatusRecordByModfiyTime("dateTime", 0, 0))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<VocFunctionalStatusRecordPO> result = ossWorkServiceImplUnderTest.selectVocFunctionalStatusRecordByModfiyTime(
                "dateTime", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectVocWarningDataRecordPoByModfiyTime() {
        // Setup
        final VocWarningDataRecordPo vocWarningDataRecordPo = new VocWarningDataRecordPo();
        vocWarningDataRecordPo.setReportDate("reportDate");
        vocWarningDataRecordPo.setModifyTime("modifyTime");
        vocWarningDataRecordPo.setVin("vin");
        vocWarningDataRecordPo.setStatusName("statusName");
        vocWarningDataRecordPo.setStatusValue(0);
        final List<VocWarningDataRecordPo> expectedResult = Arrays.asList(vocWarningDataRecordPo);

        // Configure VocWarningDataRecordService.selectVocWarningDataRecordPoByModfiyTime(...).
        final VocWarningDataRecordPo vocWarningDataRecordPo1 = new VocWarningDataRecordPo();
        vocWarningDataRecordPo1.setReportDate("reportDate");
        vocWarningDataRecordPo1.setModifyTime("modifyTime");
        vocWarningDataRecordPo1.setVin("vin");
        vocWarningDataRecordPo1.setStatusName("statusName");
        vocWarningDataRecordPo1.setStatusValue(0);
        final List<VocWarningDataRecordPo> vocWarningDataRecordPos = Arrays.asList(vocWarningDataRecordPo1);
        when(mockWarningDataRecordService.selectVocWarningDataRecordPoByModfiyTime("dateTime", 0, 0))
                .thenReturn(vocWarningDataRecordPos);

        // Run the test
        final List<VocWarningDataRecordPo> result = ossWorkServiceImplUnderTest.selectVocWarningDataRecordPoByModfiyTime(
                "dateTime", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectVocWarningDataRecordPoByModfiyTime_VocWarningDataRecordServiceReturnsNoItems() {
        // Setup
        when(mockWarningDataRecordService.selectVocWarningDataRecordPoByModfiyTime("dateTime", 0, 0))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<VocWarningDataRecordPo> result = ossWorkServiceImplUnderTest.selectVocWarningDataRecordPoByModfiyTime(
                "dateTime", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testUpdateWarningIsExecute() {
        // Setup
        final VocWarningDataRecordPo vocWarningDataRecordPo = new VocWarningDataRecordPo();
        vocWarningDataRecordPo.setReportDate("reportDate");
        vocWarningDataRecordPo.setModifyTime("modifyTime");
        vocWarningDataRecordPo.setVin("vin");
        vocWarningDataRecordPo.setStatusName("statusName");
        vocWarningDataRecordPo.setStatusValue(0);
        final List<VocWarningDataRecordPo> list = Arrays.asList(vocWarningDataRecordPo);

        // Configure VocWarningDataRecordService.updateWarningIsExecute(...).
        final VocWarningDataRecordPo vocWarningDataRecordPo1 = new VocWarningDataRecordPo();
        vocWarningDataRecordPo1.setReportDate("reportDate");
        vocWarningDataRecordPo1.setModifyTime("modifyTime");
        vocWarningDataRecordPo1.setVin("vin");
        vocWarningDataRecordPo1.setStatusName("statusName");
        vocWarningDataRecordPo1.setStatusValue(0);
        final List<VocWarningDataRecordPo> list1 = Arrays.asList(vocWarningDataRecordPo1);
        when(mockWarningDataRecordService.updateWarningIsExecute(list1)).thenReturn(0);

        // Run the test
        final int result = ossWorkServiceImplUnderTest.updateWarningIsExecute(list);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectListByVins() {
        // Setup
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO.setUpdateTime("updateTime");
        vocFunctionalStatusRecordPO.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO.setVin("vin");
        vocFunctionalStatusRecordPO.setIsExecute(0);
        vocFunctionalStatusRecordPO.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> expectedResult = Arrays.asList(vocFunctionalStatusRecordPO);

        // Configure VocFunctionalStatusRecordService.selectListByVins(...).
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO1 = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO1.setUpdateTime("updateTime");
        vocFunctionalStatusRecordPO1.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO1.setVin("vin");
        vocFunctionalStatusRecordPO1.setIsExecute(0);
        vocFunctionalStatusRecordPO1.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> vocFunctionalStatusRecordPOS = Arrays.asList(
                vocFunctionalStatusRecordPO1);
        when(mockRecordService.selectListByVins(Arrays.asList("value"))).thenReturn(vocFunctionalStatusRecordPOS);

        // Run the test
        final List<VocFunctionalStatusRecordPO> result = ossWorkServiceImplUnderTest.selectListByVins(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectListByVins_VocFunctionalStatusRecordServiceReturnsNoItems() {
        // Setup
        when(mockRecordService.selectListByVins(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<VocFunctionalStatusRecordPO> result = ossWorkServiceImplUnderTest.selectListByVins(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectCountWarn() {
        // Setup
        when(mockWarningDataLogSrevice.selectCount("vin")).thenReturn(0);

        // Run the test
        final int result = ossWorkServiceImplUnderTest.selectCountWarn("vin");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectWarningdailyReodeByVin() {
        // Setup
        when(mockWarningDataRecordService.selectWarningdailyReodeByVin("vin")).thenReturn(0);

        // Run the test
        final int result = ossWorkServiceImplUnderTest.selectWarningdailyReodeByVin("vin");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectVocFunctionalStatusRecordByVin() {
        // Setup
        when(mockRecordService.selectVocFunctionalStatusRecordByVin("vin")).thenReturn(0);

        // Run the test
        final int result = ossWorkServiceImplUnderTest.selectVocFunctionalStatusRecordByVin("vin");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectVocWarningDataRecordPo() {
        // Setup
        final VocWarningDataRecordPo expectedResult = new VocWarningDataRecordPo();
        expectedResult.setReportDate("reportDate");
        expectedResult.setModifyTime("modifyTime");
        expectedResult.setVin("vin");
        expectedResult.setStatusName("statusName");
        expectedResult.setStatusValue(0);

        // Configure VocWarningDataRecordService.selectVocWarningDataRecordPo(...).
        final VocWarningDataRecordPo vocWarningDataRecordPo = new VocWarningDataRecordPo();
        vocWarningDataRecordPo.setReportDate("reportDate");
        vocWarningDataRecordPo.setModifyTime("modifyTime");
        vocWarningDataRecordPo.setVin("vin");
        vocWarningDataRecordPo.setStatusName("statusName");
        vocWarningDataRecordPo.setStatusValue(0);
        when(mockWarningDataRecordService.selectVocWarningDataRecordPo("vin")).thenReturn(vocWarningDataRecordPo);

        // Run the test
        final VocWarningDataRecordPo result = ossWorkServiceImplUnderTest.selectVocWarningDataRecordPo("vin");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectVocFunctionalStatusRecordPOByModfiyTime() {
        // Setup
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO.setUpdateTime("updateTime");
        vocFunctionalStatusRecordPO.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO.setVin("vin");
        vocFunctionalStatusRecordPO.setIsExecute(0);
        vocFunctionalStatusRecordPO.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> expectedResult = Arrays.asList(vocFunctionalStatusRecordPO);

        // Configure VocFunctionalStatusRecordService.selectVocFunctionalStatusRecordPOByModfiyTime(...).
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO1 = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO1.setUpdateTime("updateTime");
        vocFunctionalStatusRecordPO1.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO1.setVin("vin");
        vocFunctionalStatusRecordPO1.setIsExecute(0);
        vocFunctionalStatusRecordPO1.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> vocFunctionalStatusRecordPOS = Arrays.asList(
                vocFunctionalStatusRecordPO1);
        when(mockRecordService.selectVocFunctionalStatusRecordPOByModfiyTime("dateTime", 0, 0))
                .thenReturn(vocFunctionalStatusRecordPOS);

        // Run the test
        final List<VocFunctionalStatusRecordPO> result = ossWorkServiceImplUnderTest.selectVocFunctionalStatusRecordPOByModfiyTime(
                "dateTime", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectVocFunctionalStatusRecordPOByModfiyTime_VocFunctionalStatusRecordServiceReturnsNoItems() {
        // Setup
        when(mockRecordService.selectVocFunctionalStatusRecordPOByModfiyTime("dateTime", 0, 0))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<VocFunctionalStatusRecordPO> result = ossWorkServiceImplUnderTest.selectVocFunctionalStatusRecordPOByModfiyTime(
                "dateTime", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testUpdateFunctionalIsExecute() {
        // Setup
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO.setUpdateTime("updateTime");
        vocFunctionalStatusRecordPO.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO.setVin("vin");
        vocFunctionalStatusRecordPO.setIsExecute(0);
        vocFunctionalStatusRecordPO.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> list = Arrays.asList(vocFunctionalStatusRecordPO);

        // Configure VocFunctionalStatusRecordService.updateFunctionalIsExecute(...).
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO1 = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO1.setUpdateTime("updateTime");
        vocFunctionalStatusRecordPO1.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO1.setVin("vin");
        vocFunctionalStatusRecordPO1.setIsExecute(0);
        vocFunctionalStatusRecordPO1.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> list1 = Arrays.asList(vocFunctionalStatusRecordPO1);
        when(mockRecordService.updateFunctionalIsExecute(list1)).thenReturn(0);

        // Run the test
        final int result = ossWorkServiceImplUnderTest.updateFunctionalIsExecute(list);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectCountVocWarningDataRecordPo() {
        // Setup
        when(mockWarningDataRecordService.selectCountByVinAndModfiy("vin", "dateTime")).thenReturn(0);

        // Run the test
        final int result = ossWorkServiceImplUnderTest.selectCountVocWarningDataRecordPo("vin", "dateTime");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectVocFunctionalStatusLogByVin() {
        // Setup
        when(mockLogService.selectVocFunctionalStatusLogByVin("vin")).thenReturn(0);

        // Run the test
        final int result = ossWorkServiceImplUnderTest.selectVocFunctionalStatusLogByVin("vin");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectWarningdailyLogByVin() {
        // Setup
        when(mockWarningDataLogSrevice.selectWarningdailyLogByVin("vin")).thenReturn(0);

        // Run the test
        final int result = ossWorkServiceImplUnderTest.selectWarningdailyLogByVin("vin");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
