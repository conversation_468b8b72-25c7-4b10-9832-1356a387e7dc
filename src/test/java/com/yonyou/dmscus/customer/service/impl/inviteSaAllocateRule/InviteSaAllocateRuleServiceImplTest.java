package com.yonyou.dmscus.customer.service.impl.inviteSaAllocateRule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.inviteSaAllocateRule.InviteSaAllocateRuleMapper;
import com.yonyou.dmscus.customer.dto.InvitationRuleVcdcParamsVo;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.UserInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteSaAllocateRule.InviteSaAllocateRuleDTO;
import com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule.InviteSaAllocateRulePO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteSaAllocateRuleServiceImplTest {

    @Mock
    private InviteSaAllocateRuleMapper mockInviteSaAllocateRuleMapper;
    @Mock
    private BusinessPlatformService mockBusinessPlatformService;

    private InviteSaAllocateRuleServiceImpl inviteSaAllocateRuleServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteSaAllocateRuleServiceImplUnderTest = new InviteSaAllocateRuleServiceImpl();
        inviteSaAllocateRuleServiceImplUnderTest.inviteSaAllocateRuleMapper = mockInviteSaAllocateRuleMapper;
        inviteSaAllocateRuleServiceImplUnderTest.businessPlatformService = mockBusinessPlatformService;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final InviteSaAllocateRuleDTO inviteSaAllocateRuleDTO = new InviteSaAllocateRuleDTO();
        inviteSaAllocateRuleDTO.setAppId("appId");
        inviteSaAllocateRuleDTO.setOwnerCode("ownerCode");
        inviteSaAllocateRuleDTO.setOwnerParCode("ownerParCode");
        inviteSaAllocateRuleDTO.setOrgId(0);
        inviteSaAllocateRuleDTO.setId(0L);

        // Configure InviteSaAllocateRuleMapper.selectPageBySql(...).
        final InviteSaAllocateRulePO inviteSaAllocateRulePO = new InviteSaAllocateRulePO();
        inviteSaAllocateRulePO.setOwnerCode("ownerCode");
        inviteSaAllocateRulePO.setOrgId(0);
        inviteSaAllocateRulePO.setId(0L);
        inviteSaAllocateRulePO.setDealerCode("dealerCode");
        inviteSaAllocateRulePO.setDealerName("dealerName");
        final List<InviteSaAllocateRulePO> inviteSaAllocateRulePOS = Arrays.asList(inviteSaAllocateRulePO);
        when(mockInviteSaAllocateRuleMapper.selectPageBySql(any(Page.class),
                any(InviteSaAllocateRulePO.class))).thenReturn(inviteSaAllocateRulePOS);

        // Run the test
        final IPage<InviteSaAllocateRuleDTO> result = inviteSaAllocateRuleServiceImplUnderTest.selectPageBysql(page,
                inviteSaAllocateRuleDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_InviteSaAllocateRuleMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final InviteSaAllocateRuleDTO inviteSaAllocateRuleDTO = new InviteSaAllocateRuleDTO();
        inviteSaAllocateRuleDTO.setAppId("appId");
        inviteSaAllocateRuleDTO.setOwnerCode("ownerCode");
        inviteSaAllocateRuleDTO.setOwnerParCode("ownerParCode");
        inviteSaAllocateRuleDTO.setOrgId(0);
        inviteSaAllocateRuleDTO.setId(0L);

        when(mockInviteSaAllocateRuleMapper.selectPageBySql(any(Page.class),
                any(InviteSaAllocateRulePO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<InviteSaAllocateRuleDTO> result = inviteSaAllocateRuleServiceImplUnderTest.selectPageBysql(page,
                inviteSaAllocateRuleDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final InviteSaAllocateRuleDTO inviteSaAllocateRuleDTO = new InviteSaAllocateRuleDTO();
        inviteSaAllocateRuleDTO.setAppId("appId");
        inviteSaAllocateRuleDTO.setOwnerCode("ownerCode");
        inviteSaAllocateRuleDTO.setOwnerParCode("ownerParCode");
        inviteSaAllocateRuleDTO.setOrgId(0);
        inviteSaAllocateRuleDTO.setId(0L);

        // Configure InviteSaAllocateRuleMapper.selectListBySql(...).
        final InviteSaAllocateRulePO inviteSaAllocateRulePO = new InviteSaAllocateRulePO();
        inviteSaAllocateRulePO.setOwnerCode("ownerCode");
        inviteSaAllocateRulePO.setOrgId(0);
        inviteSaAllocateRulePO.setId(0L);
        inviteSaAllocateRulePO.setDealerCode("dealerCode");
        inviteSaAllocateRulePO.setDealerName("dealerName");
        final List<InviteSaAllocateRulePO> inviteSaAllocateRulePOS = Arrays.asList(inviteSaAllocateRulePO);
        when(mockInviteSaAllocateRuleMapper.selectListBySql(any(InviteSaAllocateRulePO.class)))
                .thenReturn(inviteSaAllocateRulePOS);

        // Run the test
        final List<InviteSaAllocateRuleDTO> result = inviteSaAllocateRuleServiceImplUnderTest.selectListBySql(
                inviteSaAllocateRuleDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_InviteSaAllocateRuleMapperReturnsNoItems() {
        // Setup
        final InviteSaAllocateRuleDTO inviteSaAllocateRuleDTO = new InviteSaAllocateRuleDTO();
        inviteSaAllocateRuleDTO.setAppId("appId");
        inviteSaAllocateRuleDTO.setOwnerCode("ownerCode");
        inviteSaAllocateRuleDTO.setOwnerParCode("ownerParCode");
        inviteSaAllocateRuleDTO.setOrgId(0);
        inviteSaAllocateRuleDTO.setId(0L);

        when(mockInviteSaAllocateRuleMapper.selectListBySql(any(InviteSaAllocateRulePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteSaAllocateRuleDTO> result = inviteSaAllocateRuleServiceImplUnderTest.selectListBySql(
                inviteSaAllocateRuleDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure InviteSaAllocateRuleMapper.selectById(...).
        final InviteSaAllocateRulePO inviteSaAllocateRulePO = new InviteSaAllocateRulePO();
        inviteSaAllocateRulePO.setOwnerCode("ownerCode");
        inviteSaAllocateRulePO.setOrgId(0);
        inviteSaAllocateRulePO.setId(0L);
        inviteSaAllocateRulePO.setDealerCode("dealerCode");
        inviteSaAllocateRulePO.setDealerName("dealerName");
        when(mockInviteSaAllocateRuleMapper.selectById(0L)).thenReturn(inviteSaAllocateRulePO);

        // Run the test
        final InviteSaAllocateRuleDTO result = inviteSaAllocateRuleServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_InviteSaAllocateRuleMapperReturnsNull() {
        // Setup
        when(mockInviteSaAllocateRuleMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inviteSaAllocateRuleServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final InviteSaAllocateRuleDTO inviteSaAllocateRuleDTO = new InviteSaAllocateRuleDTO();
        inviteSaAllocateRuleDTO.setAppId("appId");
        inviteSaAllocateRuleDTO.setOwnerCode("ownerCode");
        inviteSaAllocateRuleDTO.setOwnerParCode("ownerParCode");
        inviteSaAllocateRuleDTO.setOrgId(0);
        inviteSaAllocateRuleDTO.setId(0L);

        when(mockInviteSaAllocateRuleMapper.insert(any(InviteSaAllocateRulePO.class))).thenReturn(0);

        // Run the test
        final int result = inviteSaAllocateRuleServiceImplUnderTest.insert(inviteSaAllocateRuleDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final InviteSaAllocateRuleDTO inviteSaAllocateRuleDTO = new InviteSaAllocateRuleDTO();
        inviteSaAllocateRuleDTO.setAppId("appId");
        inviteSaAllocateRuleDTO.setOwnerCode("ownerCode");
        inviteSaAllocateRuleDTO.setOwnerParCode("ownerParCode");
        inviteSaAllocateRuleDTO.setOrgId(0);
        inviteSaAllocateRuleDTO.setId(0L);

        // Configure InviteSaAllocateRuleMapper.selectById(...).
        final InviteSaAllocateRulePO inviteSaAllocateRulePO = new InviteSaAllocateRulePO();
        inviteSaAllocateRulePO.setOwnerCode("ownerCode");
        inviteSaAllocateRulePO.setOrgId(0);
        inviteSaAllocateRulePO.setId(0L);
        inviteSaAllocateRulePO.setDealerCode("dealerCode");
        inviteSaAllocateRulePO.setDealerName("dealerName");
        when(mockInviteSaAllocateRuleMapper.selectById(0L)).thenReturn(inviteSaAllocateRulePO);

        when(mockInviteSaAllocateRuleMapper.updateById(any(InviteSaAllocateRulePO.class))).thenReturn(0);

        // Run the test
        final int result = inviteSaAllocateRuleServiceImplUnderTest.update(0L, inviteSaAllocateRuleDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSaveInviteSaAllocateRule() {
        // Setup
        final InviteSaAllocateRuleDTO inviteSaAllocateRuleDTO = new InviteSaAllocateRuleDTO();
        inviteSaAllocateRuleDTO.setAppId("appId");
        inviteSaAllocateRuleDTO.setOwnerCode("ownerCode");
        inviteSaAllocateRuleDTO.setOwnerParCode("ownerParCode");
        inviteSaAllocateRuleDTO.setOrgId(0);
        inviteSaAllocateRuleDTO.setId(0L);

        // Configure InviteSaAllocateRuleMapper.selectById(...).
        final InviteSaAllocateRulePO inviteSaAllocateRulePO = new InviteSaAllocateRulePO();
        inviteSaAllocateRulePO.setOwnerCode("ownerCode");
        inviteSaAllocateRulePO.setOrgId(0);
        inviteSaAllocateRulePO.setId(0L);
        inviteSaAllocateRulePO.setDealerCode("dealerCode");
        inviteSaAllocateRulePO.setDealerName("dealerName");
        when(mockInviteSaAllocateRuleMapper.selectById(0L)).thenReturn(inviteSaAllocateRulePO);

        when(mockInviteSaAllocateRuleMapper.updateById(any(InviteSaAllocateRulePO.class))).thenReturn(0);

        // Run the test
        final int result = inviteSaAllocateRuleServiceImplUnderTest.saveInviteSaAllocateRule(inviteSaAllocateRuleDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testGetUserInfo() {
        // Setup
        // Run the test
        final List<UserInfoDTO> result = inviteSaAllocateRuleServiceImplUnderTest.getUserInfo();

        // Verify the results
    }

    @Test
    void testGetInviteSaAllocateRule() {
        // Setup
        final InvitationRuleVcdcParamsVo vo = new InvitationRuleVcdcParamsVo();
        vo.setLargeAreaId("largeAreaId");
        vo.setAreaId("areaId");
        vo.setAreaManageId("areaId");
        vo.setDealerCode("dealerCode");
        vo.setDealerName("dealerName");

        when(mockBusinessPlatformService.getDealercodes("areaId", "largeAreaId", "dealerCode",
                "dealerName")).thenReturn(Arrays.asList("value"));

        // Configure InviteSaAllocateRuleMapper.getInviteSaAllocateRule(...).
        final InviteSaAllocateRulePO inviteSaAllocateRulePO = new InviteSaAllocateRulePO();
        inviteSaAllocateRulePO.setOwnerCode("ownerCode");
        inviteSaAllocateRulePO.setOrgId(0);
        inviteSaAllocateRulePO.setId(0L);
        inviteSaAllocateRulePO.setDealerCode("dealerCode");
        inviteSaAllocateRulePO.setDealerName("dealerName");
        final List<InviteSaAllocateRulePO> inviteSaAllocateRulePOS = Arrays.asList(inviteSaAllocateRulePO);
        when(mockInviteSaAllocateRuleMapper.getInviteSaAllocateRule(Arrays.asList("value")))
                .thenReturn(inviteSaAllocateRulePOS);

        // Run the test
        final List<InviteSaAllocateRuleDTO> result = inviteSaAllocateRuleServiceImplUnderTest.getInviteSaAllocateRule(
                vo);

        // Verify the results
    }

    @Test
    void testGetInviteSaAllocateRule_InviteSaAllocateRuleMapperReturnsNoItems() {
        // Setup
        final InvitationRuleVcdcParamsVo vo = new InvitationRuleVcdcParamsVo();
        vo.setLargeAreaId("largeAreaId");
        vo.setAreaId("areaId");
        vo.setAreaManageId("areaId");
        vo.setDealerCode("dealerCode");
        vo.setDealerName("dealerName");

        when(mockBusinessPlatformService.getDealercodes("areaId", "largeAreaId", "dealerCode",
                "dealerName")).thenReturn(Arrays.asList("value"));
        when(mockInviteSaAllocateRuleMapper.getInviteSaAllocateRule(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteSaAllocateRuleDTO> result = inviteSaAllocateRuleServiceImplUnderTest.getInviteSaAllocateRule(
                vo);

        // Verify the results
    }
}
