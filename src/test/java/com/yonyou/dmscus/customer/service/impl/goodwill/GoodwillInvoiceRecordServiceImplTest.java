package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillInvoiceRecordMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillInvoiceRecordDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillInvoiceRecordPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillInvoiceRecordServiceImplTest {

    @Mock
    private GoodwillInvoiceRecordMapper mockGoodwillInvoiceRecordMapper;

    private GoodwillInvoiceRecordServiceImpl goodwillInvoiceRecordServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        goodwillInvoiceRecordServiceImplUnderTest = new GoodwillInvoiceRecordServiceImpl();
        goodwillInvoiceRecordServiceImplUnderTest.goodwillInvoiceRecordMapper = mockGoodwillInvoiceRecordMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setAppId("appId");
        goodwillInvoiceRecordDTO.setOwnerCode("ownerCode");
        goodwillInvoiceRecordDTO.setOwnerParCode("ownerParCode");
        goodwillInvoiceRecordDTO.setOrgId(0);
        goodwillInvoiceRecordDTO.setId(0L);

        // Configure GoodwillInvoiceRecordMapper.selectPageBySql(...).
        final GoodwillInvoiceRecordPO goodwillInvoiceRecordPO = new GoodwillInvoiceRecordPO();
        goodwillInvoiceRecordPO.setAppId("appId");
        goodwillInvoiceRecordPO.setOwnerCode("ownerCode");
        goodwillInvoiceRecordPO.setOwnerParCode("ownerParCode");
        goodwillInvoiceRecordPO.setOrgId(0);
        goodwillInvoiceRecordPO.setId(0L);
        final List<GoodwillInvoiceRecordPO> goodwillInvoiceRecordPOS = Arrays.asList(goodwillInvoiceRecordPO);
        when(mockGoodwillInvoiceRecordMapper.selectPageBySql(any(Page.class),
                any(GoodwillInvoiceRecordPO.class))).thenReturn(goodwillInvoiceRecordPOS);

        // Run the test
        final IPage<GoodwillInvoiceRecordDTO> result = goodwillInvoiceRecordServiceImplUnderTest.selectPageBysql(page,
                goodwillInvoiceRecordDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillInvoiceRecordMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setAppId("appId");
        goodwillInvoiceRecordDTO.setOwnerCode("ownerCode");
        goodwillInvoiceRecordDTO.setOwnerParCode("ownerParCode");
        goodwillInvoiceRecordDTO.setOrgId(0);
        goodwillInvoiceRecordDTO.setId(0L);

        when(mockGoodwillInvoiceRecordMapper.selectPageBySql(any(Page.class),
                any(GoodwillInvoiceRecordPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillInvoiceRecordDTO> result = goodwillInvoiceRecordServiceImplUnderTest.selectPageBysql(page,
                goodwillInvoiceRecordDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setAppId("appId");
        goodwillInvoiceRecordDTO.setOwnerCode("ownerCode");
        goodwillInvoiceRecordDTO.setOwnerParCode("ownerParCode");
        goodwillInvoiceRecordDTO.setOrgId(0);
        goodwillInvoiceRecordDTO.setId(0L);

        // Configure GoodwillInvoiceRecordMapper.selectListBySql(...).
        final GoodwillInvoiceRecordPO goodwillInvoiceRecordPO = new GoodwillInvoiceRecordPO();
        goodwillInvoiceRecordPO.setAppId("appId");
        goodwillInvoiceRecordPO.setOwnerCode("ownerCode");
        goodwillInvoiceRecordPO.setOwnerParCode("ownerParCode");
        goodwillInvoiceRecordPO.setOrgId(0);
        goodwillInvoiceRecordPO.setId(0L);
        final List<GoodwillInvoiceRecordPO> goodwillInvoiceRecordPOS = Arrays.asList(goodwillInvoiceRecordPO);
        when(mockGoodwillInvoiceRecordMapper.selectListBySql(any(GoodwillInvoiceRecordPO.class)))
                .thenReturn(goodwillInvoiceRecordPOS);

        // Run the test
        final List<GoodwillInvoiceRecordDTO> result = goodwillInvoiceRecordServiceImplUnderTest.selectListBySql(
                goodwillInvoiceRecordDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillInvoiceRecordMapperReturnsNoItems() {
        // Setup
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setAppId("appId");
        goodwillInvoiceRecordDTO.setOwnerCode("ownerCode");
        goodwillInvoiceRecordDTO.setOwnerParCode("ownerParCode");
        goodwillInvoiceRecordDTO.setOrgId(0);
        goodwillInvoiceRecordDTO.setId(0L);

        when(mockGoodwillInvoiceRecordMapper.selectListBySql(any(GoodwillInvoiceRecordPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillInvoiceRecordDTO> result = goodwillInvoiceRecordServiceImplUnderTest.selectListBySql(
                goodwillInvoiceRecordDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure GoodwillInvoiceRecordMapper.selectById(...).
        final GoodwillInvoiceRecordPO goodwillInvoiceRecordPO = new GoodwillInvoiceRecordPO();
        goodwillInvoiceRecordPO.setAppId("appId");
        goodwillInvoiceRecordPO.setOwnerCode("ownerCode");
        goodwillInvoiceRecordPO.setOwnerParCode("ownerParCode");
        goodwillInvoiceRecordPO.setOrgId(0);
        goodwillInvoiceRecordPO.setId(0L);
        when(mockGoodwillInvoiceRecordMapper.selectById(0L)).thenReturn(goodwillInvoiceRecordPO);

        // Run the test
        final GoodwillInvoiceRecordDTO result = goodwillInvoiceRecordServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillInvoiceRecordMapperReturnsNull() throws Exception {
        // Setup
        when(mockGoodwillInvoiceRecordMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillInvoiceRecordServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setAppId("appId");
        goodwillInvoiceRecordDTO.setOwnerCode("ownerCode");
        goodwillInvoiceRecordDTO.setOwnerParCode("ownerParCode");
        goodwillInvoiceRecordDTO.setOrgId(0);
        goodwillInvoiceRecordDTO.setId(0L);

        when(mockGoodwillInvoiceRecordMapper.insert(any(GoodwillInvoiceRecordPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillInvoiceRecordServiceImplUnderTest.insert(goodwillInvoiceRecordDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setAppId("appId");
        goodwillInvoiceRecordDTO.setOwnerCode("ownerCode");
        goodwillInvoiceRecordDTO.setOwnerParCode("ownerParCode");
        goodwillInvoiceRecordDTO.setOrgId(0);
        goodwillInvoiceRecordDTO.setId(0L);

        // Configure GoodwillInvoiceRecordMapper.selectById(...).
        final GoodwillInvoiceRecordPO goodwillInvoiceRecordPO = new GoodwillInvoiceRecordPO();
        goodwillInvoiceRecordPO.setAppId("appId");
        goodwillInvoiceRecordPO.setOwnerCode("ownerCode");
        goodwillInvoiceRecordPO.setOwnerParCode("ownerParCode");
        goodwillInvoiceRecordPO.setOrgId(0);
        goodwillInvoiceRecordPO.setId(0L);
        when(mockGoodwillInvoiceRecordMapper.selectById(0L)).thenReturn(goodwillInvoiceRecordPO);

        when(mockGoodwillInvoiceRecordMapper.updateById(any(GoodwillInvoiceRecordPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillInvoiceRecordServiceImplUnderTest.update(0L, goodwillInvoiceRecordDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
