package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintSendEmailTypeSettingsMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailTypeSettingsDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintSendEmailTypeSettingsPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintSendEmailTypeSettingsServiceImplTest {

    @Mock
    private ComplaintSendEmailTypeSettingsMapper mockComplaintSendEmailTypeSettingsMapper;

    private ComplaintSendEmailTypeSettingsServiceImpl complaintSendEmailTypeSettingsServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        complaintSendEmailTypeSettingsServiceImplUnderTest = new ComplaintSendEmailTypeSettingsServiceImpl();
        complaintSendEmailTypeSettingsServiceImplUnderTest.complaintSendEmailTypeSettingsMapper = mockComplaintSendEmailTypeSettingsMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintSendEmailTypeSettingsDTO complaintSendEmailTypeSettingsDTO = new ComplaintSendEmailTypeSettingsDTO();
        complaintSendEmailTypeSettingsDTO.setAppId("appId");
        complaintSendEmailTypeSettingsDTO.setOwnerCode("ownerCode");
        complaintSendEmailTypeSettingsDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailTypeSettingsDTO.setOrgId(0);
        complaintSendEmailTypeSettingsDTO.setId(0L);

        // Configure ComplaintSendEmailTypeSettingsMapper.selectPageBySql(...).
        final ComplaintSendEmailTypeSettingsPO complaintSendEmailTypeSettingsPO = new ComplaintSendEmailTypeSettingsPO();
        complaintSendEmailTypeSettingsPO.setAppId("appId");
        complaintSendEmailTypeSettingsPO.setOwnerCode("ownerCode");
        complaintSendEmailTypeSettingsPO.setOwnerParCode("ownerParCode");
        complaintSendEmailTypeSettingsPO.setOrgId(0);
        complaintSendEmailTypeSettingsPO.setId(0L);
        final List<ComplaintSendEmailTypeSettingsPO> complaintSendEmailTypeSettingsPOS = Arrays.asList(
                complaintSendEmailTypeSettingsPO);
        when(mockComplaintSendEmailTypeSettingsMapper.selectPageBySql(any(Page.class),
                any(ComplaintSendEmailTypeSettingsPO.class))).thenReturn(complaintSendEmailTypeSettingsPOS);

        // Run the test
        final IPage<ComplaintSendEmailTypeSettingsDTO> result = complaintSendEmailTypeSettingsServiceImplUnderTest.selectPageBysql(
                page, complaintSendEmailTypeSettingsDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintSendEmailTypeSettingsMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintSendEmailTypeSettingsDTO complaintSendEmailTypeSettingsDTO = new ComplaintSendEmailTypeSettingsDTO();
        complaintSendEmailTypeSettingsDTO.setAppId("appId");
        complaintSendEmailTypeSettingsDTO.setOwnerCode("ownerCode");
        complaintSendEmailTypeSettingsDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailTypeSettingsDTO.setOrgId(0);
        complaintSendEmailTypeSettingsDTO.setId(0L);

        when(mockComplaintSendEmailTypeSettingsMapper.selectPageBySql(any(Page.class),
                any(ComplaintSendEmailTypeSettingsPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintSendEmailTypeSettingsDTO> result = complaintSendEmailTypeSettingsServiceImplUnderTest.selectPageBysql(
                page, complaintSendEmailTypeSettingsDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final ComplaintSendEmailTypeSettingsDTO complaintSendEmailTypeSettingsDTO = new ComplaintSendEmailTypeSettingsDTO();
        complaintSendEmailTypeSettingsDTO.setAppId("appId");
        complaintSendEmailTypeSettingsDTO.setOwnerCode("ownerCode");
        complaintSendEmailTypeSettingsDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailTypeSettingsDTO.setOrgId(0);
        complaintSendEmailTypeSettingsDTO.setId(0L);

        // Configure ComplaintSendEmailTypeSettingsMapper.selectListBySql(...).
        final ComplaintSendEmailTypeSettingsPO complaintSendEmailTypeSettingsPO = new ComplaintSendEmailTypeSettingsPO();
        complaintSendEmailTypeSettingsPO.setAppId("appId");
        complaintSendEmailTypeSettingsPO.setOwnerCode("ownerCode");
        complaintSendEmailTypeSettingsPO.setOwnerParCode("ownerParCode");
        complaintSendEmailTypeSettingsPO.setOrgId(0);
        complaintSendEmailTypeSettingsPO.setId(0L);
        final List<ComplaintSendEmailTypeSettingsPO> complaintSendEmailTypeSettingsPOS = Arrays.asList(
                complaintSendEmailTypeSettingsPO);
        when(mockComplaintSendEmailTypeSettingsMapper.selectListBySql(
                any(ComplaintSendEmailTypeSettingsPO.class))).thenReturn(complaintSendEmailTypeSettingsPOS);

        // Run the test
        final List<ComplaintSendEmailTypeSettingsDTO> result = complaintSendEmailTypeSettingsServiceImplUnderTest.selectListBySql(
                complaintSendEmailTypeSettingsDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintSendEmailTypeSettingsMapperReturnsNoItems() {
        // Setup
        final ComplaintSendEmailTypeSettingsDTO complaintSendEmailTypeSettingsDTO = new ComplaintSendEmailTypeSettingsDTO();
        complaintSendEmailTypeSettingsDTO.setAppId("appId");
        complaintSendEmailTypeSettingsDTO.setOwnerCode("ownerCode");
        complaintSendEmailTypeSettingsDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailTypeSettingsDTO.setOrgId(0);
        complaintSendEmailTypeSettingsDTO.setId(0L);

        when(mockComplaintSendEmailTypeSettingsMapper.selectListBySql(
                any(ComplaintSendEmailTypeSettingsPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintSendEmailTypeSettingsDTO> result = complaintSendEmailTypeSettingsServiceImplUnderTest.selectListBySql(
                complaintSendEmailTypeSettingsDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure ComplaintSendEmailTypeSettingsMapper.selectById(...).
        final ComplaintSendEmailTypeSettingsPO complaintSendEmailTypeSettingsPO = new ComplaintSendEmailTypeSettingsPO();
        complaintSendEmailTypeSettingsPO.setAppId("appId");
        complaintSendEmailTypeSettingsPO.setOwnerCode("ownerCode");
        complaintSendEmailTypeSettingsPO.setOwnerParCode("ownerParCode");
        complaintSendEmailTypeSettingsPO.setOrgId(0);
        complaintSendEmailTypeSettingsPO.setId(0L);
        when(mockComplaintSendEmailTypeSettingsMapper.selectById(0L)).thenReturn(complaintSendEmailTypeSettingsPO);

        // Run the test
        final ComplaintSendEmailTypeSettingsDTO result = complaintSendEmailTypeSettingsServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintSendEmailTypeSettingsMapperReturnsNull() {
        // Setup
        when(mockComplaintSendEmailTypeSettingsMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintSendEmailTypeSettingsServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final ComplaintSendEmailTypeSettingsDTO complaintSendEmailTypeSettingsDTO = new ComplaintSendEmailTypeSettingsDTO();
        complaintSendEmailTypeSettingsDTO.setAppId("appId");
        complaintSendEmailTypeSettingsDTO.setOwnerCode("ownerCode");
        complaintSendEmailTypeSettingsDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailTypeSettingsDTO.setOrgId(0);
        complaintSendEmailTypeSettingsDTO.setId(0L);

        when(mockComplaintSendEmailTypeSettingsMapper.insert(any(ComplaintSendEmailTypeSettingsPO.class)))
                .thenReturn(0);

        // Run the test
        final int result = complaintSendEmailTypeSettingsServiceImplUnderTest.insert(complaintSendEmailTypeSettingsDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final ComplaintSendEmailTypeSettingsDTO complaintSendEmailTypeSettingsDTO = new ComplaintSendEmailTypeSettingsDTO();
        complaintSendEmailTypeSettingsDTO.setAppId("appId");
        complaintSendEmailTypeSettingsDTO.setOwnerCode("ownerCode");
        complaintSendEmailTypeSettingsDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailTypeSettingsDTO.setOrgId(0);
        complaintSendEmailTypeSettingsDTO.setId(0L);

        // Configure ComplaintSendEmailTypeSettingsMapper.selectById(...).
        final ComplaintSendEmailTypeSettingsPO complaintSendEmailTypeSettingsPO = new ComplaintSendEmailTypeSettingsPO();
        complaintSendEmailTypeSettingsPO.setAppId("appId");
        complaintSendEmailTypeSettingsPO.setOwnerCode("ownerCode");
        complaintSendEmailTypeSettingsPO.setOwnerParCode("ownerParCode");
        complaintSendEmailTypeSettingsPO.setOrgId(0);
        complaintSendEmailTypeSettingsPO.setId(0L);
        when(mockComplaintSendEmailTypeSettingsMapper.selectById(0L)).thenReturn(complaintSendEmailTypeSettingsPO);

        when(mockComplaintSendEmailTypeSettingsMapper.updateById(
                any(ComplaintSendEmailTypeSettingsPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintSendEmailTypeSettingsServiceImplUnderTest.update(0L,
                complaintSendEmailTypeSettingsDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
