package com.yonyou.dmscus.customer.service.vocbtnlog;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.yonyou.dmscus.customer.dao.btnLog.BtnLogMapper;
import com.yonyou.dmscus.customer.entity.po.btnlog.BtnLogPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BtnLogServiceImplTest {

    @Mock
    private BtnLogMapper mockBtnLogMapper;

    private BtnLogServiceImpl btnLogServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        btnLogServiceImplUnderTest = new BtnLogServiceImpl();
        btnLogServiceImplUnderTest.btnLogMapper = mockBtnLogMapper;
    }

    @Test
    void testInsert() {
        // Setup
        final BtnLogPO po = new BtnLogPO();
        po.setUpdateTime("updateTime");
        po.setFileUrl("fileUrl");
        po.setDataType(0);
        po.setIsSc(0);
        po.setMess("mess");

        // Configure BtnLogMapper.insert(...).
        final BtnLogPO entity = new BtnLogPO();
        entity.setUpdateTime("updateTime");
        entity.setFileUrl("fileUrl");
        entity.setDataType(0);
        entity.setIsSc(0);
        entity.setMess("mess");
        when(mockBtnLogMapper.insert(entity)).thenReturn(0);

        // Run the test
        final int result = btnLogServiceImplUnderTest.insert(po);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final BtnLogPO po = new BtnLogPO();
        po.setUpdateTime("updateTime");
        po.setFileUrl("fileUrl");
        po.setDataType(0);
        po.setIsSc(0);
        po.setMess("mess");

        // Configure BtnLogMapper.updateById(...).
        final BtnLogPO entity = new BtnLogPO();
        entity.setUpdateTime("updateTime");
        entity.setFileUrl("fileUrl");
        entity.setDataType(0);
        entity.setIsSc(0);
        entity.setMess("mess");
        when(mockBtnLogMapper.updateById(entity)).thenReturn(0);

        // Run the test
        final int result = btnLogServiceImplUnderTest.update(po);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectCount() {
        // Setup
        when(mockBtnLogMapper.selectCount(any(Wrapper.class))).thenReturn(0);

        // Run the test
        final int result = btnLogServiceImplUnderTest.selectCount("dateTime", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
