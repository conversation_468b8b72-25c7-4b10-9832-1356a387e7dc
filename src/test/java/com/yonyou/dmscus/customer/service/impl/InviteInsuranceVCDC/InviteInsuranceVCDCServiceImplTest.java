package com.yonyou.dmscus.customer.service.impl.InviteInsuranceVCDC;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleRecordMapper;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.TmVehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordPO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteInsuranceVCDCServiceImplTest {

    @Mock
    private InviteInsuranceVehicleRecordMapper mockInviteInsuranceVehicleRecordMapper;
    @Mock
    private BusinessPlatformService mockBusinessPlatformService;

    private InviteInsuranceVCDCServiceImpl inviteInsuranceVCDCServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteInsuranceVCDCServiceImplUnderTest = new InviteInsuranceVCDCServiceImpl();
        inviteInsuranceVCDCServiceImplUnderTest.inviteInsuranceVehicleRecordMapper = mockInviteInsuranceVehicleRecordMapper;
        inviteInsuranceVCDCServiceImplUnderTest.businessPlatformService = mockBusinessPlatformService;
    }

    @Test
    void testSelectVehicleByVin() {
        // Setup
        // Configure BusinessPlatformService.getVehicleByVIN(...).
        final TmVehicleDTO tmVehicleDTO = new TmVehicleDTO();
        tmVehicleDTO.setId(0L);
        tmVehicleDTO.setCustomerId(0L);
        tmVehicleDTO.setCompanyCode("companyCode");
        tmVehicleDTO.setDealerCode("dealerCode");
        tmVehicleDTO.setMileage(0);
        when(mockBusinessPlatformService.getVehicleByVIN("vin")).thenReturn(tmVehicleDTO);

        // Run the test
        final Map<String, Object> result = inviteInsuranceVCDCServiceImplUnderTest.selectVehicleByVin("vin");

        // Verify the results
    }

    @Test
    void testSelectVehicleByVin_BusinessPlatformServiceReturnsNull() {
        // Setup
        when(mockBusinessPlatformService.getVehicleByVIN("vin")).thenReturn(null);

        // Run the test
        final Map<String, Object> result = inviteInsuranceVCDCServiceImplUnderTest.selectVehicleByVin("vin");

        // Verify the results
    }
}
