package com.yonyou.dmscus.customer.service.impl.vocAccidentInvitation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.service.excel.ExcelRead;
import com.yonyou.dmscloud.framework.service.excel.impl.AbstractExcelReadCallBack;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.vocAccidentInvitation.InviteVehicleVocTaskMapper;
import com.yonyou.dmscus.customer.dao.vocAccidentInvitation.TempInviteVehicleVocTaskMapper;
import com.yonyou.dmscus.customer.dto.CustomerInfoCenterDTO;
import com.yonyou.dmscus.customer.dto.CustomerInfoListReturnDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.CheckTmVehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.IsExistByCodeDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.vocAccidentInvitation.InviteVehicleVocTaskDTO;
import com.yonyou.dmscus.customer.entity.dto.vocAccidentInvitation.InviteVehicleVocTaskImportDTO;
import com.yonyou.dmscus.customer.entity.dto.vocAccidentInvitation.TempInviteVehicleVocTaskDTO;
import com.yonyou.dmscus.customer.entity.po.vocAccidentInvitation.InviteVehicleVocTaskPO;
import com.yonyou.dmscus.customer.entity.po.vocAccidentInvitation.TempInviteVehicleVocTaskPO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordDetailService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteVehicleVocTaskServiceImplTest {

    @Mock
    private InviteVehicleVocTaskMapper mockInviteVehicleVocTaskMapper;
    @Mock
    private TempInviteVehicleVocTaskMapper mockTempInviteVehicleVocTaskMapper;
    @Mock
    private InviteVehicleRecordService mockInviteVehicleRecordService;
    @Mock
    private InviteVehicleRecordDetailService mockInviteVehicleRecordDetailService;
    @Mock
    private ExcelRead<InviteVehicleVocTaskImportDTO> mockExcelReadService;
    @Mock
    private BusinessPlatformService mockBusinessPlatformService;

    private InviteVehicleVocTaskServiceImpl inviteVehicleVocTaskServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteVehicleVocTaskServiceImplUnderTest = new InviteVehicleVocTaskServiceImpl();
        inviteVehicleVocTaskServiceImplUnderTest.inviteVehicleVocTaskMapper = mockInviteVehicleVocTaskMapper;
        inviteVehicleVocTaskServiceImplUnderTest.tempInviteVehicleVocTaskMapper = mockTempInviteVehicleVocTaskMapper;
        inviteVehicleVocTaskServiceImplUnderTest.inviteVehicleRecordService = mockInviteVehicleRecordService;
        inviteVehicleVocTaskServiceImplUnderTest.inviteVehicleRecordDetailService = mockInviteVehicleRecordDetailService;
        inviteVehicleVocTaskServiceImplUnderTest.excelReadService = mockExcelReadService;
        inviteVehicleVocTaskServiceImplUnderTest.businessPlatformService = mockBusinessPlatformService;
    }

    @Test
    void testGetById() {
        // Setup
        // Configure InviteVehicleVocTaskMapper.selectById(...).
        final InviteVehicleVocTaskPO inviteVehicleVocTaskPO = new InviteVehicleVocTaskPO();
        inviteVehicleVocTaskPO.setDealerCodes(Arrays.asList("value"));
        inviteVehicleVocTaskPO.setId(0L);
        inviteVehicleVocTaskPO.setName("name");
        inviteVehicleVocTaskPO.setTel("mobile");
        inviteVehicleVocTaskPO.setVin("vin");
        inviteVehicleVocTaskPO.setLicensePlateNum("licensePlateNum");
        inviteVehicleVocTaskPO.setDealerCode("dealerCode");
        inviteVehicleVocTaskPO.setIsCreateInvite(0);
        inviteVehicleVocTaskPO.setInviteId(0L);
        inviteVehicleVocTaskPO.setContactDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleVocTaskPO.setContactSituation(0);
        inviteVehicleVocTaskPO.setAccidentNo("accidentNo");
        inviteVehicleVocTaskPO.setAccidentDetail("accidentDetail");
        inviteVehicleVocTaskPO.setRemark("remark");
        when(mockInviteVehicleVocTaskMapper.selectById(0L)).thenReturn(inviteVehicleVocTaskPO);

        // Run the test
        final InviteVehicleVocTaskDTO result = inviteVehicleVocTaskServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_InviteVehicleVocTaskMapperReturnsNull() {
        // Setup
        when(mockInviteVehicleVocTaskMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inviteVehicleVocTaskServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final InviteVehicleVocTaskDTO inviteVehicleVocTaskDTO = new InviteVehicleVocTaskDTO();
        inviteVehicleVocTaskDTO.setPlanDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleVocTaskDTO.setNewContactDateStart("newContactDateStart");
        inviteVehicleVocTaskDTO.setLargeAreaId("largeAreaId");
        inviteVehicleVocTaskDTO.setAreaId("areaId");
        inviteVehicleVocTaskDTO.setDealerCode("dealerCode");

        when(mockInviteVehicleVocTaskMapper.insert(any(InviteVehicleVocTaskPO.class))).thenReturn(0);

        // Run the test
        final int result = inviteVehicleVocTaskServiceImplUnderTest.insert(inviteVehicleVocTaskDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final InviteVehicleVocTaskDTO inviteVehicleVocTaskDTO = new InviteVehicleVocTaskDTO();
        inviteVehicleVocTaskDTO.setPlanDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleVocTaskDTO.setNewContactDateStart("newContactDateStart");
        inviteVehicleVocTaskDTO.setLargeAreaId("largeAreaId");
        inviteVehicleVocTaskDTO.setAreaId("areaId");
        inviteVehicleVocTaskDTO.setDealerCode("dealerCode");

        // Configure InviteVehicleVocTaskMapper.selectById(...).
        final InviteVehicleVocTaskPO inviteVehicleVocTaskPO = new InviteVehicleVocTaskPO();
        inviteVehicleVocTaskPO.setDealerCodes(Arrays.asList("value"));
        inviteVehicleVocTaskPO.setId(0L);
        inviteVehicleVocTaskPO.setName("name");
        inviteVehicleVocTaskPO.setTel("mobile");
        inviteVehicleVocTaskPO.setVin("vin");
        inviteVehicleVocTaskPO.setLicensePlateNum("licensePlateNum");
        inviteVehicleVocTaskPO.setDealerCode("dealerCode");
        inviteVehicleVocTaskPO.setIsCreateInvite(0);
        inviteVehicleVocTaskPO.setInviteId(0L);
        inviteVehicleVocTaskPO.setContactDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleVocTaskPO.setContactSituation(0);
        inviteVehicleVocTaskPO.setAccidentNo("accidentNo");
        inviteVehicleVocTaskPO.setAccidentDetail("accidentDetail");
        inviteVehicleVocTaskPO.setRemark("remark");
        when(mockInviteVehicleVocTaskMapper.selectById(0L)).thenReturn(inviteVehicleVocTaskPO);

        when(mockInviteVehicleVocTaskMapper.updateById(any(InviteVehicleVocTaskPO.class))).thenReturn(0);

        // Run the test
        final int result = inviteVehicleVocTaskServiceImplUnderTest.update(0L, inviteVehicleVocTaskDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
