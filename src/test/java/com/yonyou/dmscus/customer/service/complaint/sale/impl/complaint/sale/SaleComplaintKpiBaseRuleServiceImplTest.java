package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintKpiBaseRuleMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintKpiSetDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintKpiBaseRuleDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintKpiBaseRulePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SaleComplaintKpiBaseRuleServiceImplTest {

    @Mock
    private SaleComplaintKpiBaseRuleMapper mockSaleComplaintKpiBaseRuleMapper;

    private SaleComplaintKpiBaseRuleServiceImpl saleComplaintKpiBaseRuleServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        saleComplaintKpiBaseRuleServiceImplUnderTest = new SaleComplaintKpiBaseRuleServiceImpl();
        saleComplaintKpiBaseRuleServiceImplUnderTest.saleComplaintKpiBaseRuleMapper = mockSaleComplaintKpiBaseRuleMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintKpiBaseRuleDTO saleComplaintKpiBaseRuleDTO = new SaleComplaintKpiBaseRuleDTO();
        saleComplaintKpiBaseRuleDTO.setKpi(0);
        saleComplaintKpiBaseRuleDTO.setIndexValue("indexValue");
        saleComplaintKpiBaseRuleDTO.setScore(0);
        saleComplaintKpiBaseRuleDTO.setIsValid(0);
        saleComplaintKpiBaseRuleDTO.setCreatedBy("createdBy");

        // Configure SaleComplaintKpiBaseRuleMapper.selectPageBySql(...).
        final SaleComplaintKpiBaseRulePO saleComplaintKpiBaseRulePO = new SaleComplaintKpiBaseRulePO();
        saleComplaintKpiBaseRulePO.setAppId("appId");
        saleComplaintKpiBaseRulePO.setOwnerCode("ownerCode");
        saleComplaintKpiBaseRulePO.setOwnerParCode("ownerParCode");
        saleComplaintKpiBaseRulePO.setOrgId(0);
        saleComplaintKpiBaseRulePO.setId(0L);
        final List<SaleComplaintKpiBaseRulePO> saleComplaintKpiBaseRulePOS = Arrays.asList(saleComplaintKpiBaseRulePO);
        when(mockSaleComplaintKpiBaseRuleMapper.selectPageBySql(any(Page.class),
                any(SaleComplaintKpiBaseRulePO.class))).thenReturn(saleComplaintKpiBaseRulePOS);

        // Run the test
        final IPage<SaleComplaintKpiBaseRuleDTO> result = saleComplaintKpiBaseRuleServiceImplUnderTest.selectPageBysql(
                page, saleComplaintKpiBaseRuleDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_SaleComplaintKpiBaseRuleMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintKpiBaseRuleDTO saleComplaintKpiBaseRuleDTO = new SaleComplaintKpiBaseRuleDTO();
        saleComplaintKpiBaseRuleDTO.setKpi(0);
        saleComplaintKpiBaseRuleDTO.setIndexValue("indexValue");
        saleComplaintKpiBaseRuleDTO.setScore(0);
        saleComplaintKpiBaseRuleDTO.setIsValid(0);
        saleComplaintKpiBaseRuleDTO.setCreatedBy("createdBy");

        when(mockSaleComplaintKpiBaseRuleMapper.selectPageBySql(any(Page.class),
                any(SaleComplaintKpiBaseRulePO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<SaleComplaintKpiBaseRuleDTO> result = saleComplaintKpiBaseRuleServiceImplUnderTest.selectPageBysql(
                page, saleComplaintKpiBaseRuleDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final SaleComplaintKpiBaseRuleDTO saleComplaintKpiBaseRuleDTO = new SaleComplaintKpiBaseRuleDTO();
        saleComplaintKpiBaseRuleDTO.setKpi(0);
        saleComplaintKpiBaseRuleDTO.setIndexValue("indexValue");
        saleComplaintKpiBaseRuleDTO.setScore(0);
        saleComplaintKpiBaseRuleDTO.setIsValid(0);
        saleComplaintKpiBaseRuleDTO.setCreatedBy("createdBy");

        // Configure SaleComplaintKpiBaseRuleMapper.selectListBySql(...).
        final SaleComplaintKpiBaseRulePO saleComplaintKpiBaseRulePO = new SaleComplaintKpiBaseRulePO();
        saleComplaintKpiBaseRulePO.setAppId("appId");
        saleComplaintKpiBaseRulePO.setOwnerCode("ownerCode");
        saleComplaintKpiBaseRulePO.setOwnerParCode("ownerParCode");
        saleComplaintKpiBaseRulePO.setOrgId(0);
        saleComplaintKpiBaseRulePO.setId(0L);
        final List<SaleComplaintKpiBaseRulePO> saleComplaintKpiBaseRulePOS = Arrays.asList(saleComplaintKpiBaseRulePO);
        when(mockSaleComplaintKpiBaseRuleMapper.selectListBySql(any(SaleComplaintKpiBaseRulePO.class)))
                .thenReturn(saleComplaintKpiBaseRulePOS);

        // Run the test
        final List<SaleComplaintKpiBaseRuleDTO> result = saleComplaintKpiBaseRuleServiceImplUnderTest.selectListBySql(
                saleComplaintKpiBaseRuleDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_SaleComplaintKpiBaseRuleMapperReturnsNoItems() {
        // Setup
        final SaleComplaintKpiBaseRuleDTO saleComplaintKpiBaseRuleDTO = new SaleComplaintKpiBaseRuleDTO();
        saleComplaintKpiBaseRuleDTO.setKpi(0);
        saleComplaintKpiBaseRuleDTO.setIndexValue("indexValue");
        saleComplaintKpiBaseRuleDTO.setScore(0);
        saleComplaintKpiBaseRuleDTO.setIsValid(0);
        saleComplaintKpiBaseRuleDTO.setCreatedBy("createdBy");

        when(mockSaleComplaintKpiBaseRuleMapper.selectListBySql(any(SaleComplaintKpiBaseRulePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<SaleComplaintKpiBaseRuleDTO> result = saleComplaintKpiBaseRuleServiceImplUnderTest.selectListBySql(
                saleComplaintKpiBaseRuleDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure SaleComplaintKpiBaseRuleMapper.selectById(...).
        final SaleComplaintKpiBaseRulePO saleComplaintKpiBaseRulePO = new SaleComplaintKpiBaseRulePO();
        saleComplaintKpiBaseRulePO.setAppId("appId");
        saleComplaintKpiBaseRulePO.setOwnerCode("ownerCode");
        saleComplaintKpiBaseRulePO.setOwnerParCode("ownerParCode");
        saleComplaintKpiBaseRulePO.setOrgId(0);
        saleComplaintKpiBaseRulePO.setId(0L);
        when(mockSaleComplaintKpiBaseRuleMapper.selectById(0L)).thenReturn(saleComplaintKpiBaseRulePO);

        // Run the test
        final SaleComplaintKpiBaseRuleDTO result = saleComplaintKpiBaseRuleServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_SaleComplaintKpiBaseRuleMapperReturnsNull() {
        // Setup
        when(mockSaleComplaintKpiBaseRuleMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> saleComplaintKpiBaseRuleServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final SaleComplaintKpiBaseRuleDTO saleComplaintKpiBaseRuleDTO = new SaleComplaintKpiBaseRuleDTO();
        saleComplaintKpiBaseRuleDTO.setKpi(0);
        saleComplaintKpiBaseRuleDTO.setIndexValue("indexValue");
        saleComplaintKpiBaseRuleDTO.setScore(0);
        saleComplaintKpiBaseRuleDTO.setIsValid(0);
        saleComplaintKpiBaseRuleDTO.setCreatedBy("createdBy");

        when(mockSaleComplaintKpiBaseRuleMapper.insert(any(SaleComplaintKpiBaseRulePO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintKpiBaseRuleServiceImplUnderTest.insert(saleComplaintKpiBaseRuleDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final SaleComplaintKpiBaseRuleDTO saleComplaintKpiBaseRuleDTO = new SaleComplaintKpiBaseRuleDTO();
        saleComplaintKpiBaseRuleDTO.setKpi(0);
        saleComplaintKpiBaseRuleDTO.setIndexValue("indexValue");
        saleComplaintKpiBaseRuleDTO.setScore(0);
        saleComplaintKpiBaseRuleDTO.setIsValid(0);
        saleComplaintKpiBaseRuleDTO.setCreatedBy("createdBy");

        // Configure SaleComplaintKpiBaseRuleMapper.selectById(...).
        final SaleComplaintKpiBaseRulePO saleComplaintKpiBaseRulePO = new SaleComplaintKpiBaseRulePO();
        saleComplaintKpiBaseRulePO.setAppId("appId");
        saleComplaintKpiBaseRulePO.setOwnerCode("ownerCode");
        saleComplaintKpiBaseRulePO.setOwnerParCode("ownerParCode");
        saleComplaintKpiBaseRulePO.setOrgId(0);
        saleComplaintKpiBaseRulePO.setId(0L);
        when(mockSaleComplaintKpiBaseRuleMapper.selectById(0L)).thenReturn(saleComplaintKpiBaseRulePO);

        when(mockSaleComplaintKpiBaseRuleMapper.updateById(any(SaleComplaintKpiBaseRulePO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintKpiBaseRuleServiceImplUnderTest.update(0L, saleComplaintKpiBaseRuleDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
