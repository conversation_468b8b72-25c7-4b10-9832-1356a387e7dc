package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillDealerLinesMaintainMapper;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillDealerLinesMaintainDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.companySelectDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillDealerLinesMaintainPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillDealerLinesMaintainServiceImplTest {

    @Mock
    private GoodwillDealerLinesMaintainMapper mockGoodwillDealerLinesMaintainMapper;
    @Mock
    private RestTemplate mockDirectRestTemplate;
    @Mock
    private MidUrlProperties mockMidUrlProperties;

    @InjectMocks
    private GoodwillDealerLinesMaintainServiceImpl goodwillDealerLinesMaintainServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        goodwillDealerLinesMaintainServiceImplUnderTest.goodwillDealerLinesMaintainMapper = mockGoodwillDealerLinesMaintainMapper;
        goodwillDealerLinesMaintainServiceImplUnderTest.request = new MockHttpServletRequest();
    }

    @Test
    void testSelectPageBysql_RestTemplateThrowsRestClientException() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO = new GoodwillDealerLinesMaintainDTO();
        goodwillDealerLinesMaintainDTO.setProvinceName("provinceName");
        goodwillDealerLinesMaintainDTO.setProvince(0);
        goodwillDealerLinesMaintainDTO.setDealerName("dealerName");
        goodwillDealerLinesMaintainDTO.setBloc("bloc");
        goodwillDealerLinesMaintainDTO.setAreaManages("areaManages");
        goodwillDealerLinesMaintainDTO.setAreaManage(0);
        goodwillDealerLinesMaintainDTO.setId(0L);
        goodwillDealerLinesMaintainDTO.setDealerCode("dealerCode");

        when(mockMidUrlProperties.getMidEndOrgCenter()).thenReturn("result");
        when(mockMidUrlProperties.getSelectCompanyInfo()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final companySelectDTO companySelectDTO = new companySelectDTO();
        companySelectDTO.setDealerType(0);
        companySelectDTO.setAfterBigArea("afterBigArea");
        companySelectDTO.setAfterSmallArea("afterSmallArea");
        companySelectDTO.setCityId("cityId");
        companySelectDTO.setCompanyType("companyType");
        final HttpEntity<com.yonyou.dmscus.customer.entity.dto.goodwill.companySelectDTO> requestEntity = new HttpEntity<>(
                companySelectDTO, new HttpHeaders());
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST, requestEntity, ResponseDTO.class))
                .thenThrow(RestClientException.class);

        // Run the test
        assertThatThrownBy(() -> goodwillDealerLinesMaintainServiceImplUnderTest.selectPageBysql(page,
                goodwillDealerLinesMaintainDTO)).isInstanceOf(RestClientException.class);
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO = new GoodwillDealerLinesMaintainDTO();
        goodwillDealerLinesMaintainDTO.setProvinceName("provinceName");
        goodwillDealerLinesMaintainDTO.setProvince(0);
        goodwillDealerLinesMaintainDTO.setDealerName("dealerName");
        goodwillDealerLinesMaintainDTO.setBloc("bloc");
        goodwillDealerLinesMaintainDTO.setAreaManages("areaManages");
        goodwillDealerLinesMaintainDTO.setAreaManage(0);
        goodwillDealerLinesMaintainDTO.setId(0L);
        goodwillDealerLinesMaintainDTO.setDealerCode("dealerCode");

        // Configure GoodwillDealerLinesMaintainMapper.selectListBySql(...).
        final GoodwillDealerLinesMaintainPO goodwillDealerLinesMaintainPO = new GoodwillDealerLinesMaintainPO();
        goodwillDealerLinesMaintainPO.setAppId("appId");
        goodwillDealerLinesMaintainPO.setOwnerCode("ownerCode");
        goodwillDealerLinesMaintainPO.setOwnerParCode("ownerParCode");
        goodwillDealerLinesMaintainPO.setFoundDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillDealerLinesMaintainPO.setIsDeleted(false);
        final List<GoodwillDealerLinesMaintainPO> goodwillDealerLinesMaintainPOS = Arrays.asList(
                goodwillDealerLinesMaintainPO);
        when(mockGoodwillDealerLinesMaintainMapper.selectListBySql(
                any(GoodwillDealerLinesMaintainPO.class))).thenReturn(goodwillDealerLinesMaintainPOS);

        // Run the test
        final List<GoodwillDealerLinesMaintainDTO> result = goodwillDealerLinesMaintainServiceImplUnderTest.selectListBySql(
                goodwillDealerLinesMaintainDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillDealerLinesMaintainMapperReturnsNoItems() {
        // Setup
        final GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO = new GoodwillDealerLinesMaintainDTO();
        goodwillDealerLinesMaintainDTO.setProvinceName("provinceName");
        goodwillDealerLinesMaintainDTO.setProvince(0);
        goodwillDealerLinesMaintainDTO.setDealerName("dealerName");
        goodwillDealerLinesMaintainDTO.setBloc("bloc");
        goodwillDealerLinesMaintainDTO.setAreaManages("areaManages");
        goodwillDealerLinesMaintainDTO.setAreaManage(0);
        goodwillDealerLinesMaintainDTO.setId(0L);
        goodwillDealerLinesMaintainDTO.setDealerCode("dealerCode");

        when(mockGoodwillDealerLinesMaintainMapper.selectListBySql(
                any(GoodwillDealerLinesMaintainPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillDealerLinesMaintainDTO> result = goodwillDealerLinesMaintainServiceImplUnderTest.selectListBySql(
                goodwillDealerLinesMaintainDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure GoodwillDealerLinesMaintainMapper.selectById(...).
        final GoodwillDealerLinesMaintainPO goodwillDealerLinesMaintainPO = new GoodwillDealerLinesMaintainPO();
        goodwillDealerLinesMaintainPO.setAppId("appId");
        goodwillDealerLinesMaintainPO.setOwnerCode("ownerCode");
        goodwillDealerLinesMaintainPO.setOwnerParCode("ownerParCode");
        goodwillDealerLinesMaintainPO.setFoundDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillDealerLinesMaintainPO.setIsDeleted(false);
        when(mockGoodwillDealerLinesMaintainMapper.selectById(0L)).thenReturn(goodwillDealerLinesMaintainPO);

        // Run the test
        final GoodwillDealerLinesMaintainDTO result = goodwillDealerLinesMaintainServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillDealerLinesMaintainMapperReturnsNull() {
        // Setup
        when(mockGoodwillDealerLinesMaintainMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillDealerLinesMaintainServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO = new GoodwillDealerLinesMaintainDTO();
        goodwillDealerLinesMaintainDTO.setProvinceName("provinceName");
        goodwillDealerLinesMaintainDTO.setProvince(0);
        goodwillDealerLinesMaintainDTO.setDealerName("dealerName");
        goodwillDealerLinesMaintainDTO.setBloc("bloc");
        goodwillDealerLinesMaintainDTO.setAreaManages("areaManages");
        goodwillDealerLinesMaintainDTO.setAreaManage(0);
        goodwillDealerLinesMaintainDTO.setId(0L);
        goodwillDealerLinesMaintainDTO.setDealerCode("dealerCode");

        when(mockGoodwillDealerLinesMaintainMapper.insert(any(GoodwillDealerLinesMaintainPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillDealerLinesMaintainServiceImplUnderTest.insert(goodwillDealerLinesMaintainDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO = new GoodwillDealerLinesMaintainDTO();
        goodwillDealerLinesMaintainDTO.setProvinceName("provinceName");
        goodwillDealerLinesMaintainDTO.setProvince(0);
        goodwillDealerLinesMaintainDTO.setDealerName("dealerName");
        goodwillDealerLinesMaintainDTO.setBloc("bloc");
        goodwillDealerLinesMaintainDTO.setAreaManages("areaManages");
        goodwillDealerLinesMaintainDTO.setAreaManage(0);
        goodwillDealerLinesMaintainDTO.setId(0L);
        goodwillDealerLinesMaintainDTO.setDealerCode("dealerCode");

        // Configure GoodwillDealerLinesMaintainMapper.selectById(...).
        final GoodwillDealerLinesMaintainPO goodwillDealerLinesMaintainPO = new GoodwillDealerLinesMaintainPO();
        goodwillDealerLinesMaintainPO.setAppId("appId");
        goodwillDealerLinesMaintainPO.setOwnerCode("ownerCode");
        goodwillDealerLinesMaintainPO.setOwnerParCode("ownerParCode");
        goodwillDealerLinesMaintainPO.setFoundDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillDealerLinesMaintainPO.setIsDeleted(false);
        when(mockGoodwillDealerLinesMaintainMapper.selectById(0L)).thenReturn(goodwillDealerLinesMaintainPO);

        when(mockGoodwillDealerLinesMaintainMapper.updateById(any(GoodwillDealerLinesMaintainPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillDealerLinesMaintainServiceImplUnderTest.update(0L, goodwillDealerLinesMaintainDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testGetDealerInfo_RestTemplateThrowsRestClientException() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final companySelectDTO dto = new companySelectDTO();
        dto.setDealerType(0);
        dto.setAfterBigArea("afterBigArea");
        dto.setAfterSmallArea("afterSmallArea");
        dto.setCityId("cityId");
        dto.setCompanyType("companyType");

        when(mockMidUrlProperties.getMidEndOrgCenter()).thenReturn("result");
        when(mockMidUrlProperties.getSelectCompanyInfo()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final companySelectDTO companySelectDTO = new companySelectDTO();
        companySelectDTO.setDealerType(0);
        companySelectDTO.setAfterBigArea("afterBigArea");
        companySelectDTO.setAfterSmallArea("afterSmallArea");
        companySelectDTO.setCityId("cityId");
        companySelectDTO.setCompanyType("companyType");
        final HttpEntity<com.yonyou.dmscus.customer.entity.dto.goodwill.companySelectDTO> requestEntity = new HttpEntity<>(
                companySelectDTO, new HttpHeaders());
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST, requestEntity, ResponseDTO.class))
                .thenThrow(RestClientException.class);

        // Run the test
        assertThatThrownBy(() -> goodwillDealerLinesMaintainServiceImplUnderTest.getDealerInfo(page, dto))
                .isInstanceOf(RestClientException.class);
    }

    @Test
    void testAddDealerLinesInfo() {
        // Setup
        final GoodwillDealerLinesMaintainDTO goodwillDealerLinesMaintainDTO = new GoodwillDealerLinesMaintainDTO();
        goodwillDealerLinesMaintainDTO.setProvinceName("provinceName");
        goodwillDealerLinesMaintainDTO.setProvince(0);
        goodwillDealerLinesMaintainDTO.setDealerName("dealerName");
        goodwillDealerLinesMaintainDTO.setBloc("bloc");
        goodwillDealerLinesMaintainDTO.setAreaManages("areaManages");
        goodwillDealerLinesMaintainDTO.setAreaManage(0);
        goodwillDealerLinesMaintainDTO.setId(0L);
        goodwillDealerLinesMaintainDTO.setDealerCode("dealerCode");

        // Configure GoodwillDealerLinesMaintainMapper.selectById(...).
        final GoodwillDealerLinesMaintainPO goodwillDealerLinesMaintainPO = new GoodwillDealerLinesMaintainPO();
        goodwillDealerLinesMaintainPO.setAppId("appId");
        goodwillDealerLinesMaintainPO.setOwnerCode("ownerCode");
        goodwillDealerLinesMaintainPO.setOwnerParCode("ownerParCode");
        goodwillDealerLinesMaintainPO.setFoundDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillDealerLinesMaintainPO.setIsDeleted(false);
        when(mockGoodwillDealerLinesMaintainMapper.selectById(0L)).thenReturn(goodwillDealerLinesMaintainPO);

        // Run the test
        final int result = goodwillDealerLinesMaintainServiceImplUnderTest.addDealerLinesInfo(
                goodwillDealerLinesMaintainDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockGoodwillDealerLinesMaintainMapper).insert(any(GoodwillDealerLinesMaintainPO.class));
        verify(mockGoodwillDealerLinesMaintainMapper).updateById(any(GoodwillDealerLinesMaintainPO.class));
    }

    @Test
    void testSelectExportListBySql_RestTemplateThrowsRestClientException() {
        // Setup
        final Map<String, String> queryParam = new HashMap<>();
        when(mockMidUrlProperties.getMidEndOrgCenter()).thenReturn("result");
        when(mockMidUrlProperties.getSelectCompanyInfo()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final companySelectDTO companySelectDTO = new companySelectDTO();
        companySelectDTO.setDealerType(0);
        companySelectDTO.setAfterBigArea("afterBigArea");
        companySelectDTO.setAfterSmallArea("afterSmallArea");
        companySelectDTO.setCityId("cityId");
        companySelectDTO.setCompanyType("companyType");
        final HttpEntity<com.yonyou.dmscus.customer.entity.dto.goodwill.companySelectDTO> requestEntity = new HttpEntity<>(
                companySelectDTO, new HttpHeaders());
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST, requestEntity, ResponseDTO.class))
                .thenThrow(RestClientException.class);

        // Run the test
        assertThatThrownBy(
                () -> goodwillDealerLinesMaintainServiceImplUnderTest.selectExportListBySql(queryParam))
                .isInstanceOf(RestClientException.class);
    }

    @Test
    void testDeleteDealerLinesById() {
        // Setup
        // Configure GoodwillDealerLinesMaintainMapper.selectById(...).
        final GoodwillDealerLinesMaintainPO goodwillDealerLinesMaintainPO = new GoodwillDealerLinesMaintainPO();
        goodwillDealerLinesMaintainPO.setAppId("appId");
        goodwillDealerLinesMaintainPO.setOwnerCode("ownerCode");
        goodwillDealerLinesMaintainPO.setOwnerParCode("ownerParCode");
        goodwillDealerLinesMaintainPO.setFoundDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillDealerLinesMaintainPO.setIsDeleted(false);
        when(mockGoodwillDealerLinesMaintainMapper.selectById(0L)).thenReturn(goodwillDealerLinesMaintainPO);

        // Run the test
        goodwillDealerLinesMaintainServiceImplUnderTest.deleteDealerLinesById(0L);

        // Verify the results
        verify(mockGoodwillDealerLinesMaintainMapper).updateById(any(GoodwillDealerLinesMaintainPO.class));
    }
}
