package com.yonyou.dmscus.customer.service.impl.talkskill;

import com.yonyou.dmscus.customer.dao.talkskill.TalkskillKeywordLibMapper;
import com.yonyou.dmscus.customer.dao.talkskill.TalkskillKeywordMapper;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillKeywordLibDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillKeywordLibPO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillKeywordPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TalkskillKeywordLibServiceImplTest {

    @Mock
    private TalkskillKeywordLibMapper mockTalkskillKeywordLibMapper;
    @Mock
    private TalkskillKeywordMapper mockTalkskillKeywordMapper;

    private TalkskillKeywordLibServiceImpl talkskillKeywordLibServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        talkskillKeywordLibServiceImplUnderTest = new TalkskillKeywordLibServiceImpl();
        talkskillKeywordLibServiceImplUnderTest.talkskillKeywordLibMapper = mockTalkskillKeywordLibMapper;
        talkskillKeywordLibServiceImplUnderTest.talkskillKeywordMapper = mockTalkskillKeywordMapper;
    }

    @Test
    void testInsert() {
        // Setup
        final TalkskillKeywordLibDTO talkskillKeywordLibDTO = new TalkskillKeywordLibDTO();
        talkskillKeywordLibDTO.setKeyId(0L);
        talkskillKeywordLibDTO.setKeyword("keyword");
        talkskillKeywordLibDTO.setNum(0);
        talkskillKeywordLibDTO.setDataSources(0);
        talkskillKeywordLibDTO.setIsDeleted(0);

        // Configure TalkskillKeywordLibMapper.insert(...).
        final TalkskillKeywordLibPO entity = new TalkskillKeywordLibPO();
        entity.setKeyId(0L);
        entity.setKeyword("keyword");
        entity.setNum(0);
        entity.setDataSources(0);
        entity.setIsDeleted(0);
        when(mockTalkskillKeywordLibMapper.insert(entity)).thenReturn(0);

        // Run the test
        final int result = talkskillKeywordLibServiceImplUnderTest.insert(talkskillKeywordLibDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
