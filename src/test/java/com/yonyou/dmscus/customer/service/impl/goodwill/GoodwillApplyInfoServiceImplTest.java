package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.goodwill.*;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.*;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillInvoiceRecordPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMaterialAuditPO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillNoticeInvoiceInfoPO;
import com.yonyou.dmscus.customer.feign.VehicleOwnershipClient;
import com.yonyou.dmscus.customer.feign.WhitelistQueryService;
import com.yonyou.dmscus.customer.feign.vo.ListBindRelationVo;
import com.yonyou.dmscus.customer.middleInterface.CouponReturnVO;
import com.yonyou.dmscus.customer.middleInterface.RequestDTO;
import com.yonyou.dmscus.customer.middleInterface.SaveCouponDetailDTO;
import com.yonyou.dmscus.customer.middleInterface.SaveCouponRechargeDTO;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GoodwillApplyInfoServiceImplTest {
    @Mock
    private GoodwillApplyInfoMapper mockGoodwillApplyInfoMapper;
    @Mock
    private GoodwillMaterialAuditMapper mockGoodwillMaterialAuditMapper;
    @Mock
    private GoodwillNoticeInvoiceInfoMapper mockGoodwillNoticeInvoiceInfoMapper;
    @Mock
    private GoodwillInvoiceRecordMapper mockGoodwillInvoiceRecordMapper;
    @Mock
    private GoodwillApplyInfoServiceHelper mockGoodwillApplyInfoServiceHelper;
    @Mock
    private RestTemplate mockDirectRestTemplate;
    @Mock
    private MidUrlProperties mockMidUrlProperties;
    @Mock
    private CouponMapper couponMapper;
    @Mock
    private CouponUrStandardDealerMapper couponUrStandardDealerMapper;
    @Mock
    private WhitelistQueryService whitelistQueryService;
    @Mock
    private VehicleOwnershipClient vehicleOwnershipClient;

    @InjectMocks
    private GoodwillApplyInfoServiceImpl goodwillApplyInfoServiceImplUnderTest;
    private static MockedStatic<FrameworkUtil> mockStatic;

    @BeforeEach
    void setUp() {
        // Setup
        mockStatic = Mockito.mockStatic(FrameworkUtil.class);
        LoginInfoDto loginInfoDto = new LoginInfoDto();
        loginInfoDto.setOwnerCode("SHJ");
        loginInfoDto.setUserName("userName");
        mockStatic.when(FrameworkUtil::getLoginInfo).thenReturn(loginInfoDto);
    }

    @AfterEach
    void after() {
        // Setup
        mockStatic.close();
    }


    @Test
    void testEditNoticeInvoiceInfo1() {
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto = new GoodwillNoticeInvoiceInfoDTO();
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setId(0L);
        goodwillInvoiceRecordDTO.setNoticeInvoiceId(0L);
        goodwillInvoiceRecordDTO.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setGoodwillInvoiceRecordDTO(Arrays.asList(goodwillInvoiceRecordDTO));
        goodwillNoticeInvoiceInfoDto.setGoodwillApplyId(0L);
        goodwillNoticeInvoiceInfoDto.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setInvoiceTitle("82061001");
        goodwillNoticeInvoiceInfoDto.setNoticeInvoicePrice(new BigDecimal("100.00"));
        goodwillNoticeInvoiceInfoDto.setVolvoCreditsRechargePrice(new BigDecimal("100.00"));
        goodwillNoticeInvoiceInfoDto.setVoucherCouponFaceRechargePrice(new BigDecimal("100.00"));

        // Configure GoodwillApplyInfoMapper.selectById(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo("1543");
        goodwillApplyInfoPO.setCouponId(0L);
        when(mockGoodwillApplyInfoMapper.selectById(0L)).thenReturn(goodwillApplyInfoPO);
        when(mockGoodwillNoticeInvoiceInfoMapper.insert(any())).thenReturn(1);
        when(mockGoodwillInvoiceRecordMapper.insert(any())).thenReturn(1);

        when(mockMidUrlProperties.getMidEndCouponCenter()).thenReturn("http://test.url");
        when(mockMidUrlProperties.getTtCouponInfo()).thenReturn("/vehiclePage");
        String requestUrl = "http://test.url/vehiclePage";
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("0");
        responseDTO.setReturnMessage("returnMessage");
        CouponReturnVO vo = new CouponReturnVO();
        vo.setId(11111L);
        responseDTO.setData(vo);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class))).thenReturn(responseDTOResponseEntity);


        // Setup
        final RequestDTO<SaveCouponDetailDTO> requestDto = new RequestDTO<>();
        final SaveCouponDetailDTO saveCouponDetailDTO = new SaveCouponDetailDTO();
        saveCouponDetailDTO.setCouponId(0L);
        saveCouponDetailDTO.setTicketState(0);
        saveCouponDetailDTO.setCouponSource(0);
        saveCouponDetailDTO.setGetDate("getDate");
        saveCouponDetailDTO.setOneId(0);
        saveCouponDetailDTO.setVin("vin");
        requestDto.setData(saveCouponDetailDTO);

        when(mockMidUrlProperties.getTtCouponDetail()).thenReturn("/vehiclePage123");
        String requestUrl123 = "http://test.url/vehiclePage123";
        // Configure RestTemplate.exchange(...).
        final RequestDTO requestDTO = new RequestDTO<>();
        requestDTO.setData(null);
        final HttpEntity<RequestDTO> requestEntity = new HttpEntity<>(requestDTO, new HttpHeaders());
        final ResponseDTO responseDTO1 = new ResponseDTO<>();
        responseDTO1.setReturnCode("0");
        responseDTO1.setReturnMessage("returnMessage");
        Map map = new HashMap<>();
        map.put("id","1");
        responseDTO1.setData(map);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity1 = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl123), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class)))
                .thenReturn(responseDTOResponseEntity1);
        when(couponMapper.insert(any())).thenReturn(1);
        when(couponUrStandardDealerMapper.insert(any())).thenReturn(1);
        when(mockGoodwillApplyInfoMapper.updateById(any(GoodwillApplyInfoPO.class))).thenReturn(0);
        // Run the test
        final int result = goodwillApplyInfoServiceImplUnderTest.editNoticeInvoiceInfo(goodwillNoticeInvoiceInfoDto);
        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testEditNoticeInvoiceInfo2() {
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto = new GoodwillNoticeInvoiceInfoDTO();
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setId(0L);
        goodwillInvoiceRecordDTO.setNoticeInvoiceId(0L);
        goodwillInvoiceRecordDTO.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setGoodwillInvoiceRecordDTO(Arrays.asList(goodwillInvoiceRecordDTO));
        goodwillNoticeInvoiceInfoDto.setGoodwillApplyId(0L);
        goodwillNoticeInvoiceInfoDto.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setInvoiceTitle("82061002");
        goodwillNoticeInvoiceInfoDto.setNoticeInvoicePrice(new BigDecimal("100.00"));
        goodwillNoticeInvoiceInfoDto.setVolvoCreditsRechargePrice(new BigDecimal("100.00"));
        goodwillNoticeInvoiceInfoDto.setVoucherCouponFaceRechargePrice(new BigDecimal("100.00"));

        // Configure GoodwillApplyInfoMapper.selectById(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo(null);
        goodwillApplyInfoPO.setCouponId(0L);
        when(mockGoodwillApplyInfoMapper.selectById(0L)).thenReturn(goodwillApplyInfoPO);
        Integer ttt = CommonConstants.ONEID_REPLACEMENT_WHITELIST;
        when(whitelistQueryService.checkWhitelist(any(),eq(ttt),any(),any())).thenReturn(true);
        ListBindRelationVo onVo = new ListBindRelationVo();
        onVo.setMemberId("2383986");
        ResponseDTO<List<ListBindRelationVo>> rDto = new ResponseDTO<>();
        List<ListBindRelationVo> listBindRelationVoList = new ArrayList<>();
        listBindRelationVoList.add(onVo);
        rDto.setData(listBindRelationVoList);
        when(vehicleOwnershipClient.listBindRelation(any())).thenReturn(rDto);



        when(mockGoodwillNoticeInvoiceInfoMapper.insert(any())).thenReturn(1);
        when(mockGoodwillInvoiceRecordMapper.insert(any())).thenReturn(1);

        when(mockMidUrlProperties.getMidEndCouponCenter()).thenReturn("http://test.url");
        when(mockMidUrlProperties.getTtCouponInfo()).thenReturn("/vehiclePage");
        String requestUrl = "http://test.url/vehiclePage";
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("0");
        responseDTO.setReturnMessage("returnMessage");
        CouponReturnVO vo = new CouponReturnVO();
        vo.setId(11111L);
        responseDTO.setData(vo);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class))).thenReturn(responseDTOResponseEntity);


        // Setup
        final RequestDTO<SaveCouponDetailDTO> requestDto = new RequestDTO<>();
        final SaveCouponDetailDTO saveCouponDetailDTO = new SaveCouponDetailDTO();
        saveCouponDetailDTO.setCouponId(0L);
        saveCouponDetailDTO.setTicketState(0);
        saveCouponDetailDTO.setCouponSource(0);
        saveCouponDetailDTO.setGetDate("getDate");
        saveCouponDetailDTO.setOneId(0);
        saveCouponDetailDTO.setVin("vin");
        requestDto.setData(saveCouponDetailDTO);

        when(mockMidUrlProperties.getTtCouponDetail()).thenReturn("/vehiclePage123");
        String requestUrl123 = "http://test.url/vehiclePage123";
        // Configure RestTemplate.exchange(...).
        final RequestDTO requestDTO = new RequestDTO<>();
        requestDTO.setData(null);
        final HttpEntity<RequestDTO> requestEntity = new HttpEntity<>(requestDTO, new HttpHeaders());
        final ResponseDTO responseDTO1 = new ResponseDTO<>();
        responseDTO1.setReturnCode("0");
        responseDTO1.setReturnMessage("returnMessage");
        Map map = new HashMap<>();
        map.put("id","1");
        responseDTO1.setData(map);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity1 = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl123), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class)))
                .thenReturn(responseDTOResponseEntity1);
        when(couponMapper.insert(any())).thenReturn(1);
        when(couponUrStandardDealerMapper.insert(any())).thenReturn(1);
        when(mockGoodwillApplyInfoMapper.updateById(any(GoodwillApplyInfoPO.class))).thenReturn(0);
        // Run the test
        final int result = goodwillApplyInfoServiceImplUnderTest.editNoticeInvoiceInfo(goodwillNoticeInvoiceInfoDto);
        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testEditNoticeInvoiceInfo3() {
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto = new GoodwillNoticeInvoiceInfoDTO();
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setId(0L);
        goodwillInvoiceRecordDTO.setNoticeInvoiceId(0L);
        goodwillInvoiceRecordDTO.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setGoodwillInvoiceRecordDTO(Arrays.asList(goodwillInvoiceRecordDTO));
        goodwillNoticeInvoiceInfoDto.setGoodwillApplyId(0L);
        goodwillNoticeInvoiceInfoDto.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setInvoiceTitle("invoiceTitle");
        goodwillNoticeInvoiceInfoDto.setNoticeInvoicePrice(new BigDecimal("100.00"));
        goodwillNoticeInvoiceInfoDto.setVolvoCreditsRechargePrice(new BigDecimal("100.00"));
        goodwillNoticeInvoiceInfoDto.setVoucherCouponFaceRechargePrice(new BigDecimal("100.00"));

        // Configure GoodwillApplyInfoMapper.selectById(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo(null);
        goodwillApplyInfoPO.setCouponId(0L);
        when(mockGoodwillApplyInfoMapper.selectById(0L)).thenReturn(goodwillApplyInfoPO);

        when(whitelistQueryService.checkWhitelist(any(),any(),any(),any())).thenReturn(true);
        ResponseDTO<List<ListBindRelationVo>> rDto = new ResponseDTO<>();
        List<ListBindRelationVo> listBindRelationVoList = new ArrayList<>();
        ListBindRelationVo onVo = new ListBindRelationVo();
        onVo.setMemberId(null);
        listBindRelationVoList.add(onVo);
        rDto.setData(listBindRelationVoList);
        when(vehicleOwnershipClient.listBindRelation(any())).thenReturn(rDto);
        // Run the test
        try {
            goodwillApplyInfoServiceImplUnderTest.editNoticeInvoiceInfo(goodwillNoticeInvoiceInfoDto);
        } catch (Exception e) {
            // Verify the results
            assertThat(e.getMessage()).isEqualTo("未注册沃世界，请先注册");
        }

    }

    @Test
    void testEditNoticeInvoiceInfo4() {
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto = new GoodwillNoticeInvoiceInfoDTO();
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setId(0L);
        goodwillInvoiceRecordDTO.setNoticeInvoiceId(0L);
        goodwillInvoiceRecordDTO.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setGoodwillInvoiceRecordDTO(Arrays.asList(goodwillInvoiceRecordDTO));
        goodwillNoticeInvoiceInfoDto.setGoodwillApplyId(0L);
        goodwillNoticeInvoiceInfoDto.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setInvoiceTitle("82061001");
        goodwillNoticeInvoiceInfoDto.setNoticeInvoicePrice(new BigDecimal("100.00"));
        goodwillNoticeInvoiceInfoDto.setVolvoCreditsRechargePrice(new BigDecimal("0"));
        goodwillNoticeInvoiceInfoDto.setVoucherCouponFaceRechargePrice(new BigDecimal("111.00"));

        // Configure GoodwillApplyInfoMapper.selectById(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo("1543");
        goodwillApplyInfoPO.setCouponId(0L);
        when(mockGoodwillApplyInfoMapper.selectById(0L)).thenReturn(goodwillApplyInfoPO);
        when(mockGoodwillNoticeInvoiceInfoMapper.insert(any())).thenReturn(1);
        when(mockGoodwillInvoiceRecordMapper.insert(any())).thenReturn(1);

        when(mockMidUrlProperties.getMidEndCouponCenter()).thenReturn("http://test.url");
        when(mockMidUrlProperties.getTtCouponInfo()).thenReturn("/vehiclePage");
        String requestUrl = "http://test.url/vehiclePage";
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("0");
        responseDTO.setReturnMessage("returnMessage");
        CouponReturnVO vo = new CouponReturnVO();
        vo.setId(11111L);
        responseDTO.setData(vo);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class))).thenReturn(responseDTOResponseEntity);


        // Setup
        final RequestDTO<SaveCouponDetailDTO> requestDto = new RequestDTO<>();
        final SaveCouponDetailDTO saveCouponDetailDTO = new SaveCouponDetailDTO();
        saveCouponDetailDTO.setCouponId(0L);
        saveCouponDetailDTO.setTicketState(0);
        saveCouponDetailDTO.setCouponSource(0);
        saveCouponDetailDTO.setGetDate("getDate");
        saveCouponDetailDTO.setOneId(0);
        saveCouponDetailDTO.setVin("vin");
        requestDto.setData(saveCouponDetailDTO);

        when(mockMidUrlProperties.getTtCouponDetail()).thenReturn("/vehiclePage123");
        String requestUrl123 = "http://test.url/vehiclePage123";
        // Configure RestTemplate.exchange(...).
        final RequestDTO requestDTO = new RequestDTO<>();
        requestDTO.setData(null);
        final HttpEntity<RequestDTO> requestEntity = new HttpEntity<>(requestDTO, new HttpHeaders());
        final ResponseDTO responseDTO1 = new ResponseDTO<>();
        responseDTO1.setReturnCode("0");
        responseDTO1.setReturnMessage("returnMessage");
        Map map = new HashMap<>();
        map.put("id","1");
        responseDTO1.setData(map);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity1 = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl123), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class)))
                .thenReturn(responseDTOResponseEntity1);
        when(couponMapper.insert(any())).thenReturn(1);
        when(couponUrStandardDealerMapper.insert(any())).thenReturn(1);
        when(mockGoodwillApplyInfoMapper.updateById(any(GoodwillApplyInfoPO.class))).thenReturn(0);
        // Run the test
        final int result = goodwillApplyInfoServiceImplUnderTest.editNoticeInvoiceInfo(goodwillNoticeInvoiceInfoDto);
        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testEditNoticeInvoiceInfo5() {
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto = new GoodwillNoticeInvoiceInfoDTO();
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setId(0L);
        goodwillInvoiceRecordDTO.setNoticeInvoiceId(0L);
        goodwillInvoiceRecordDTO.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setGoodwillInvoiceRecordDTO(Arrays.asList(goodwillInvoiceRecordDTO));
        goodwillNoticeInvoiceInfoDto.setGoodwillApplyId(0L);
        goodwillNoticeInvoiceInfoDto.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setInvoiceTitle("82061002");
        goodwillNoticeInvoiceInfoDto.setNoticeInvoicePrice(new BigDecimal("100.00"));
        goodwillNoticeInvoiceInfoDto.setVolvoCreditsRechargePrice(new BigDecimal("0"));
        goodwillNoticeInvoiceInfoDto.setVoucherCouponFaceRechargePrice(new BigDecimal("111.00"));

        // Configure GoodwillApplyInfoMapper.selectById(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo(null);
        goodwillApplyInfoPO.setCouponId(0L);
        when(mockGoodwillApplyInfoMapper.selectById(0L)).thenReturn(goodwillApplyInfoPO);

        when(whitelistQueryService.checkWhitelist(any(),any(),any(),any())).thenReturn(true);
        ResponseDTO<List<ListBindRelationVo>> rDto = new ResponseDTO<>();
        List<ListBindRelationVo> listBindRelationVoList = new ArrayList<>();
        ListBindRelationVo onVo = new ListBindRelationVo();
        onVo.setMemberId("2383986");
        listBindRelationVoList.add(onVo);
        rDto.setData(listBindRelationVoList);

        when(vehicleOwnershipClient.listBindRelation(any())).thenReturn(rDto);



        when(mockGoodwillNoticeInvoiceInfoMapper.insert(any())).thenReturn(1);
        when(mockGoodwillInvoiceRecordMapper.insert(any())).thenReturn(1);

        when(mockMidUrlProperties.getMidEndCouponCenter()).thenReturn("http://test.url");
        when(mockMidUrlProperties.getTtCouponInfo()).thenReturn("/vehiclePage");
        String requestUrl = "http://test.url/vehiclePage";
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("0");
        responseDTO.setReturnMessage("returnMessage");
        CouponReturnVO vo = new CouponReturnVO();
        vo.setId(11111L);
        responseDTO.setData(vo);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class))).thenReturn(responseDTOResponseEntity);


        // Setup
        final RequestDTO<SaveCouponDetailDTO> requestDto = new RequestDTO<>();
        final SaveCouponDetailDTO saveCouponDetailDTO = new SaveCouponDetailDTO();
        saveCouponDetailDTO.setCouponId(0L);
        saveCouponDetailDTO.setTicketState(0);
        saveCouponDetailDTO.setCouponSource(0);
        saveCouponDetailDTO.setGetDate("getDate");
        saveCouponDetailDTO.setOneId(0);
        saveCouponDetailDTO.setVin("vin");
        requestDto.setData(saveCouponDetailDTO);

        when(mockMidUrlProperties.getTtCouponDetail()).thenReturn("/vehiclePage123");
        String requestUrl123 = "http://test.url/vehiclePage123";
        // Configure RestTemplate.exchange(...).
        final RequestDTO requestDTO = new RequestDTO<>();
        requestDTO.setData(null);
        final HttpEntity<RequestDTO> requestEntity = new HttpEntity<>(requestDTO, new HttpHeaders());
        final ResponseDTO responseDTO1 = new ResponseDTO<>();
        responseDTO1.setReturnCode("0");
        responseDTO1.setReturnMessage("returnMessage");
        Map map = new HashMap<>();
        map.put("id","1");
        responseDTO1.setData(map);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity1 = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl123), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class)))
                .thenReturn(responseDTOResponseEntity1);
        when(couponMapper.insert(any())).thenReturn(1);
        when(couponUrStandardDealerMapper.insert(any())).thenReturn(1);
        when(mockGoodwillApplyInfoMapper.updateById(any(GoodwillApplyInfoPO.class))).thenReturn(0);
        // Run the test
        final int result = goodwillApplyInfoServiceImplUnderTest.editNoticeInvoiceInfo(goodwillNoticeInvoiceInfoDto);
        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testEditNoticeInvoiceInfo6() {
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto = new GoodwillNoticeInvoiceInfoDTO();
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setId(0L);
        goodwillInvoiceRecordDTO.setNoticeInvoiceId(0L);
        goodwillInvoiceRecordDTO.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setGoodwillInvoiceRecordDTO(Arrays.asList(goodwillInvoiceRecordDTO));
        goodwillNoticeInvoiceInfoDto.setGoodwillApplyId(0L);
        goodwillNoticeInvoiceInfoDto.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setInvoiceTitle("82061001");
        goodwillNoticeInvoiceInfoDto.setNoticeInvoicePrice(new BigDecimal("0"));
        goodwillNoticeInvoiceInfoDto.setVolvoCreditsRechargePrice(new BigDecimal("7757.00"));
        goodwillNoticeInvoiceInfoDto.setVoucherCouponFaceRechargePrice(new BigDecimal("111.00"));

        // Configure GoodwillApplyInfoMapper.selectById(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo("1543");
        goodwillApplyInfoPO.setCouponId(0L);
        when(mockGoodwillApplyInfoMapper.selectById(0L)).thenReturn(goodwillApplyInfoPO);
        when(mockGoodwillNoticeInvoiceInfoMapper.insert(any())).thenReturn(1);
       // when(mockGoodwillInvoiceRecordMapper.insert(any())).thenReturn(1);

        when(mockMidUrlProperties.getMidEndCouponCenter()).thenReturn("http://test.url");
        when(mockMidUrlProperties.getTtCouponInfo()).thenReturn("/vehiclePage");
        String requestUrl = "http://test.url/vehiclePage";
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("0");
        responseDTO.setReturnMessage("returnMessage");
        CouponReturnVO vo = new CouponReturnVO();
        vo.setId(11111L);
        responseDTO.setData(vo);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class))).thenReturn(responseDTOResponseEntity);


        // Setup
        final RequestDTO<SaveCouponDetailDTO> requestDto = new RequestDTO<>();
        final SaveCouponDetailDTO saveCouponDetailDTO = new SaveCouponDetailDTO();
        saveCouponDetailDTO.setCouponId(0L);
        saveCouponDetailDTO.setTicketState(0);
        saveCouponDetailDTO.setCouponSource(0);
        saveCouponDetailDTO.setGetDate("getDate");
        saveCouponDetailDTO.setOneId(0);
        saveCouponDetailDTO.setVin("vin");
        requestDto.setData(saveCouponDetailDTO);

        when(mockMidUrlProperties.getTtCouponDetail()).thenReturn("/vehiclePage123");
        String requestUrl123 = "http://test.url/vehiclePage123";
        // Configure RestTemplate.exchange(...).
        final RequestDTO requestDTO = new RequestDTO<>();
        requestDTO.setData(null);
        final HttpEntity<RequestDTO> requestEntity = new HttpEntity<>(requestDTO, new HttpHeaders());
        final ResponseDTO responseDTO1 = new ResponseDTO<>();
        responseDTO1.setReturnCode("0");
        responseDTO1.setReturnMessage("returnMessage");
        Map map = new HashMap<>();
        map.put("id","1");
        responseDTO1.setData(map);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity1 = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl123), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class)))
                .thenReturn(responseDTOResponseEntity1);
        when(couponMapper.insert(any())).thenReturn(1);
        when(couponUrStandardDealerMapper.insert(any())).thenReturn(1);
        when(mockGoodwillApplyInfoMapper.updateById(any(GoodwillApplyInfoPO.class))).thenReturn(0);
        // Run the test
        final int result = goodwillApplyInfoServiceImplUnderTest.editNoticeInvoiceInfo(goodwillNoticeInvoiceInfoDto);
        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testEditNoticeInvoiceInfo7() {
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto = new GoodwillNoticeInvoiceInfoDTO();
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setId(0L);
        goodwillInvoiceRecordDTO.setNoticeInvoiceId(0L);
        goodwillInvoiceRecordDTO.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setGoodwillInvoiceRecordDTO(Arrays.asList(goodwillInvoiceRecordDTO));
        goodwillNoticeInvoiceInfoDto.setGoodwillApplyId(0L);
        goodwillNoticeInvoiceInfoDto.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setInvoiceTitle("82061002");
        goodwillNoticeInvoiceInfoDto.setNoticeInvoicePrice(new BigDecimal("0"));
        goodwillNoticeInvoiceInfoDto.setVolvoCreditsRechargePrice(new BigDecimal("7757.00"));
        goodwillNoticeInvoiceInfoDto.setVoucherCouponFaceRechargePrice(new BigDecimal("111.00"));

        // Configure GoodwillApplyInfoMapper.selectById(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo(null);
        goodwillApplyInfoPO.setCouponId(0L);
        when(mockGoodwillApplyInfoMapper.selectById(0L)).thenReturn(goodwillApplyInfoPO);

        when(whitelistQueryService.checkWhitelist(any(),any(),any(),any())).thenReturn(true);
        ResponseDTO<List<ListBindRelationVo>> rDto = new ResponseDTO<>();
        List<ListBindRelationVo> listBindRelationVoList = new ArrayList<>();
        ListBindRelationVo onVo = new ListBindRelationVo();
        onVo.setMemberId("2383986");
        listBindRelationVoList.add(onVo);
        rDto.setData(listBindRelationVoList);
        when(vehicleOwnershipClient.listBindRelation(any())).thenReturn(rDto);



        when(mockGoodwillNoticeInvoiceInfoMapper.insert(any())).thenReturn(1);
//        when(mockGoodwillInvoiceRecordMapper.insert(any())).thenReturn(1);

        when(mockMidUrlProperties.getMidEndCouponCenter()).thenReturn("http://test.url");
        when(mockMidUrlProperties.getTtCouponInfo()).thenReturn("/vehiclePage");
        String requestUrl = "http://test.url/vehiclePage";
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("0");
        responseDTO.setReturnMessage("returnMessage");
        CouponReturnVO vo = new CouponReturnVO();
        vo.setId(11111L);
        responseDTO.setData(vo);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class))).thenReturn(responseDTOResponseEntity);


        // Setup
        final RequestDTO<SaveCouponDetailDTO> requestDto = new RequestDTO<>();
        final SaveCouponDetailDTO saveCouponDetailDTO = new SaveCouponDetailDTO();
        saveCouponDetailDTO.setCouponId(0L);
        saveCouponDetailDTO.setTicketState(0);
        saveCouponDetailDTO.setCouponSource(0);
        saveCouponDetailDTO.setGetDate("getDate");
        saveCouponDetailDTO.setOneId(0);
        saveCouponDetailDTO.setVin("vin");
        requestDto.setData(saveCouponDetailDTO);

        when(mockMidUrlProperties.getTtCouponDetail()).thenReturn("/vehiclePage123");
        String requestUrl123 = "http://test.url/vehiclePage123";
        // Configure RestTemplate.exchange(...).
        final RequestDTO requestDTO = new RequestDTO<>();
        requestDTO.setData(null);
        final HttpEntity<RequestDTO> requestEntity = new HttpEntity<>(requestDTO, new HttpHeaders());
        final ResponseDTO responseDTO1 = new ResponseDTO<>();
        responseDTO1.setReturnCode("0");
        responseDTO1.setReturnMessage("returnMessage");
        Map map = new HashMap<>();
        map.put("id","1");
        responseDTO1.setData(map);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity1 = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl123), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class)))
                .thenReturn(responseDTOResponseEntity1);
        when(couponMapper.insert(any())).thenReturn(1);
        when(couponUrStandardDealerMapper.insert(any())).thenReturn(1);
        when(mockGoodwillApplyInfoMapper.updateById(any(GoodwillApplyInfoPO.class))).thenReturn(0);
        // Run the test
        final int result = goodwillApplyInfoServiceImplUnderTest.editNoticeInvoiceInfo(goodwillNoticeInvoiceInfoDto);
        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testEditNoticeInvoiceInfo8() {
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto = new GoodwillNoticeInvoiceInfoDTO();
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setId(0L);
        goodwillInvoiceRecordDTO.setNoticeInvoiceId(0L);
        goodwillInvoiceRecordDTO.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setGoodwillInvoiceRecordDTO(Arrays.asList(goodwillInvoiceRecordDTO));
        goodwillNoticeInvoiceInfoDto.setGoodwillApplyId(0L);
        goodwillNoticeInvoiceInfoDto.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setInvoiceTitle("82061001");
        goodwillNoticeInvoiceInfoDto.setNoticeInvoicePrice(new BigDecimal("0"));
        goodwillNoticeInvoiceInfoDto.setVolvoCreditsRechargePrice(new BigDecimal("0"));
        goodwillNoticeInvoiceInfoDto.setVoucherCouponFaceRechargePrice(new BigDecimal("111.00"));

        // Configure GoodwillApplyInfoMapper.selectById(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo("1543");
        goodwillApplyInfoPO.setCouponId(0L);
        when(mockGoodwillApplyInfoMapper.selectById(0L)).thenReturn(goodwillApplyInfoPO);
        when(mockGoodwillNoticeInvoiceInfoMapper.insert(any())).thenReturn(1);
        // when(mockGoodwillInvoiceRecordMapper.insert(any())).thenReturn(1);

        when(mockMidUrlProperties.getMidEndCouponCenter()).thenReturn("http://test.url");
        when(mockMidUrlProperties.getTtCouponInfo()).thenReturn("/vehiclePage");
        String requestUrl = "http://test.url/vehiclePage";
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("0");
        responseDTO.setReturnMessage("returnMessage");
        CouponReturnVO vo = new CouponReturnVO();
        vo.setId(11111L);
        responseDTO.setData(vo);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class))).thenReturn(responseDTOResponseEntity);

        // Setup
        final RequestDTO<SaveCouponDetailDTO> requestDto = new RequestDTO<>();
        final SaveCouponDetailDTO saveCouponDetailDTO = new SaveCouponDetailDTO();
        saveCouponDetailDTO.setCouponId(0L);
        saveCouponDetailDTO.setTicketState(0);
        saveCouponDetailDTO.setCouponSource(0);
        saveCouponDetailDTO.setGetDate("getDate");
        saveCouponDetailDTO.setOneId(0);
        saveCouponDetailDTO.setVin("vin");
        requestDto.setData(saveCouponDetailDTO);

        when(mockMidUrlProperties.getTtCouponDetail()).thenReturn("/vehiclePage123");
        String requestUrl123 = "http://test.url/vehiclePage123";
        // Configure RestTemplate.exchange(...).
        final RequestDTO requestDTO = new RequestDTO<>();
        requestDTO.setData(null);
        final HttpEntity<RequestDTO> requestEntity = new HttpEntity<>(requestDTO, new HttpHeaders());
        final ResponseDTO responseDTO1 = new ResponseDTO<>();
        responseDTO1.setReturnCode("0");
        responseDTO1.setReturnMessage("returnMessage");
        Map map = new HashMap<>();
        map.put("id","1");
        responseDTO1.setData(map);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity1 = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl123), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class)))
                .thenReturn(responseDTOResponseEntity1);
        when(couponMapper.insert(any())).thenReturn(1);
        when(couponUrStandardDealerMapper.insert(any())).thenReturn(1);
        when(mockGoodwillApplyInfoMapper.updateById(any(GoodwillApplyInfoPO.class))).thenReturn(0);
        // Run the test
        final int result = goodwillApplyInfoServiceImplUnderTest.editNoticeInvoiceInfo(goodwillNoticeInvoiceInfoDto);
        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testEditNoticeInvoiceInfo9() {
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto = new GoodwillNoticeInvoiceInfoDTO();
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setId(0L);
        goodwillInvoiceRecordDTO.setNoticeInvoiceId(0L);
        goodwillInvoiceRecordDTO.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setGoodwillInvoiceRecordDTO(Arrays.asList(goodwillInvoiceRecordDTO));
        goodwillNoticeInvoiceInfoDto.setGoodwillApplyId(0L);
        goodwillNoticeInvoiceInfoDto.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setInvoiceTitle("82061002");
        goodwillNoticeInvoiceInfoDto.setNoticeInvoicePrice(new BigDecimal("0"));
        goodwillNoticeInvoiceInfoDto.setVolvoCreditsRechargePrice(new BigDecimal("0"));
        goodwillNoticeInvoiceInfoDto.setVoucherCouponFaceRechargePrice(new BigDecimal("111.00"));

        // Configure GoodwillApplyInfoMapper.selectById(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo(null);
        goodwillApplyInfoPO.setCouponId(0L);
        when(mockGoodwillApplyInfoMapper.selectById(0L)).thenReturn(goodwillApplyInfoPO);

        when(whitelistQueryService.checkWhitelist(any(),any(),any(),any())).thenReturn(false);

        when(mockMidUrlProperties.getMidEndVehicleCenter()).thenReturn("http://test.url");
        when(mockMidUrlProperties.getListOwnerVehiclePage()).thenReturn("/vehiclePage8888");
        String requestUrl8888 = "http://test.url/vehiclePage8888";
        final ResponseDTO responseDTO00 = new ResponseDTO<>();
        responseDTO00.setReturnCode("0");
        responseDTO00.setReturnMessage("returnMessage");
        List<Map> list = new ArrayList<>();
        Map mm = new HashMap<>();
        mm.put("oneId", 4243);
        list.add(mm);
        Map map1 = new HashMap<>();
        map1.put("records", list);
        responseDTO00.setData(map1);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity = new ResponseEntity<>(responseDTO00, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl8888), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class))).thenReturn(responseDTOResponseEntity);


        when(mockGoodwillNoticeInvoiceInfoMapper.insert(any())).thenReturn(1);
//        when(mockGoodwillInvoiceRecordMapper.insert(any())).thenReturn(1);

        when(mockMidUrlProperties.getMidEndCouponCenter()).thenReturn("http://test.url");
        when(mockMidUrlProperties.getTtCouponInfo()).thenReturn("/vehiclePage");
        String requestUrl = "http://test.url/vehiclePage";
        final ResponseDTO responseDTO = new ResponseDTO<>();
        responseDTO.setReturnCode("0");
        responseDTO.setReturnMessage("returnMessage");
        CouponReturnVO vo = new CouponReturnVO();
        vo.setId(11111L);
        responseDTO.setData(vo);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity333 = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class))).thenReturn(responseDTOResponseEntity333);


        // Setup
        final RequestDTO<SaveCouponDetailDTO> requestDto = new RequestDTO<>();
        final SaveCouponDetailDTO saveCouponDetailDTO = new SaveCouponDetailDTO();
        saveCouponDetailDTO.setCouponId(0L);
        saveCouponDetailDTO.setTicketState(0);
        saveCouponDetailDTO.setCouponSource(0);
        saveCouponDetailDTO.setGetDate("getDate");
        saveCouponDetailDTO.setOneId(0);
        saveCouponDetailDTO.setVin("vin");
        requestDto.setData(saveCouponDetailDTO);

        when(mockMidUrlProperties.getTtCouponDetail()).thenReturn("/vehiclePage123");
        String requestUrl123 = "http://test.url/vehiclePage123";
        // Configure RestTemplate.exchange(...).
        final RequestDTO requestDTO = new RequestDTO<>();
        requestDTO.setData(null);
        final HttpEntity<RequestDTO> requestEntity = new HttpEntity<>(requestDTO, new HttpHeaders());
        final ResponseDTO responseDTO1 = new ResponseDTO<>();
        responseDTO1.setReturnCode("0");
        responseDTO1.setReturnMessage("returnMessage");
        Map map = new HashMap<>();
        map.put("id","1");
        responseDTO1.setData(map);
        final ResponseEntity<ResponseDTO> responseDTOResponseEntity1 = new ResponseEntity<>(responseDTO, HttpStatus.OK);
        lenient().when(mockDirectRestTemplate.exchange(eq(requestUrl123), eq(HttpMethod.POST), any(HttpEntity.class), eq(ResponseDTO.class)))
                .thenReturn(responseDTOResponseEntity1);
        when(couponMapper.insert(any())).thenReturn(1);
        when(couponUrStandardDealerMapper.insert(any())).thenReturn(1);
        when(mockGoodwillApplyInfoMapper.updateById(any(GoodwillApplyInfoPO.class))).thenReturn(0);
        // Run the test
        final int result = goodwillApplyInfoServiceImplUnderTest.editNoticeInvoiceInfo(goodwillNoticeInvoiceInfoDto);
        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testEditNoticeInvoiceInfo10() {
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto = new GoodwillNoticeInvoiceInfoDTO();
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setId(0L);
        goodwillInvoiceRecordDTO.setNoticeInvoiceId(0L);
        goodwillInvoiceRecordDTO.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setGoodwillInvoiceRecordDTO(Arrays.asList(goodwillInvoiceRecordDTO));
        goodwillNoticeInvoiceInfoDto.setGoodwillApplyId(0L);
        goodwillNoticeInvoiceInfoDto.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setInvoiceTitle("82061002");
        goodwillNoticeInvoiceInfoDto.setNoticeInvoicePrice(new BigDecimal("0"));
        goodwillNoticeInvoiceInfoDto.setVolvoCreditsRechargePrice(new BigDecimal("1002.0"));
        goodwillNoticeInvoiceInfoDto.setVoucherCouponFaceRechargePrice(new BigDecimal("0"));

        // Configure GoodwillApplyInfoMapper.selectById(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo(null);
        goodwillApplyInfoPO.setCouponId(0L);
        when(mockGoodwillApplyInfoMapper.selectById(0L)).thenReturn(goodwillApplyInfoPO);
        when(mockGoodwillNoticeInvoiceInfoMapper.insert(any())).thenReturn(1);

        when(mockGoodwillApplyInfoMapper.updateById(any(GoodwillApplyInfoPO.class))).thenReturn(0);
        // Run the test
        final int result = goodwillApplyInfoServiceImplUnderTest.editNoticeInvoiceInfo(goodwillNoticeInvoiceInfoDto);
        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testEditNoticeInvoiceInfo11() {
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto = new GoodwillNoticeInvoiceInfoDTO();
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setId(0L);
        goodwillInvoiceRecordDTO.setNoticeInvoiceId(0L);
        goodwillInvoiceRecordDTO.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setGoodwillInvoiceRecordDTO(Arrays.asList(goodwillInvoiceRecordDTO));
        goodwillNoticeInvoiceInfoDto.setGoodwillApplyId(0L);
        goodwillNoticeInvoiceInfoDto.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setInvoiceTitle("82061002");
        goodwillNoticeInvoiceInfoDto.setNoticeInvoicePrice(new BigDecimal("1587000.22"));
        goodwillNoticeInvoiceInfoDto.setVolvoCreditsRechargePrice(new BigDecimal("0"));
        goodwillNoticeInvoiceInfoDto.setVoucherCouponFaceRechargePrice(new BigDecimal("0"));

        // Configure GoodwillApplyInfoMapper.selectById(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo(null);
        goodwillApplyInfoPO.setCouponId(0L);
        when(mockGoodwillApplyInfoMapper.selectById(0L)).thenReturn(goodwillApplyInfoPO);
        when(mockGoodwillNoticeInvoiceInfoMapper.insert(any())).thenReturn(1);
        when(mockGoodwillInvoiceRecordMapper.insert(any())).thenReturn(1);
        when(mockGoodwillApplyInfoMapper.updateById(any(GoodwillApplyInfoPO.class))).thenReturn(0);
        // Run the test
        final int result = goodwillApplyInfoServiceImplUnderTest.editNoticeInvoiceInfo(goodwillNoticeInvoiceInfoDto);
        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCouponId(0L);
        goodwillApplyInfoDTO.setUserId(0L);
        goodwillApplyInfoDTO.setIsMaintainChange(0);
        goodwillApplyInfoDTO.setIsExtendWarrantyChange(0);
        goodwillApplyInfoDTO.setIsAccessoryChange(0);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setMaintainCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO1 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO1.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO1.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO1.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO1.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO1.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setExtendWarrantyCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO1));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO2 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO2.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO2.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO2.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO2.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO2.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setAccessoryCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO2));
        goodwillApplyInfoDTO.setCustomerBackground("customerBackground");
        goodwillApplyInfoDTO.setCustomerBackgroundEn("customerBackgroundEn");
        goodwillApplyInfoDTO.setReasonAndDispose("reasonAndDispose");
        goodwillApplyInfoDTO.setReasonAndDisposeEn("reasonAndDisposeEn");
        goodwillApplyInfoDTO.setRepairSolution("repairSolution");
        goodwillApplyInfoDTO.setRepairSolutionEn("repairSolutionEn");
        goodwillApplyInfoDTO.setCustomerRequire("customerRequire");
        goodwillApplyInfoDTO.setCustomerRequireEn("customerRequireEn");
        goodwillApplyInfoDTO.setPotentialRisk("potentialRisk");
        goodwillApplyInfoDTO.setPotentialRiskEn("potentialRiskEn");
        goodwillApplyInfoDTO.setVrOrTjNoEn("vrOrTjNoEn");
        goodwillApplyInfoDTO.setVrOrTjNo("vrOrTjNo");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetailEn("businessGoodwillApplyDetailEn");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetail("businessGoodwillApplyDetail");
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairInfoDTO(Arrays.asList(goodwillApplyRepairInfoDTO));
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairPartInfoDTO(Arrays.asList(goodwillApplyRepairPartInfoDTO));
        goodwillApplyInfoDTO.setId(0L);
        goodwillApplyInfoDTO.setApplyNo("applyNo");
        goodwillApplyInfoDTO.setDealerCode("dealerCode");
        goodwillApplyInfoDTO.setAuditType(0);
        goodwillApplyInfoDTO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setGoodwillStatus(0);
        goodwillApplyInfoDTO.setCustomerPainVue(Arrays.asList(0));
        goodwillApplyInfoDTO.setCustomerPain("customerPain");
        goodwillApplyInfoDTO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setMaterialId(0L);
        goodwillApplyInfoDTO.setRoleList(new String[]{"roleList"});
        goodwillApplyInfoDTO.setRoleList2(new String[]{"roleList2"});
        goodwillApplyInfoDTO.setRoleList1("roleList1");

        // Configure GoodwillApplyInfoMapper.selectGoodwillApplyInfo(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo("ownerNo");
        goodwillApplyInfoPO.setCouponId(0L);
        final List<GoodwillApplyInfoPO> goodwillApplyInfoPOS = Arrays.asList(goodwillApplyInfoPO);
        when(mockGoodwillApplyInfoMapper.selectGoodwillApplyInfo(any(Page.class),
                any(GoodwillApplyInfoDTO.class))).thenReturn(goodwillApplyInfoPOS);

        // Run the test
        final IPage<GoodwillApplyInfoDTO> result = goodwillApplyInfoServiceImplUnderTest.selectPageBysql(page,
                goodwillApplyInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillApplyInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCouponId(0L);
        goodwillApplyInfoDTO.setUserId(0L);
        goodwillApplyInfoDTO.setIsMaintainChange(0);
        goodwillApplyInfoDTO.setIsExtendWarrantyChange(0);
        goodwillApplyInfoDTO.setIsAccessoryChange(0);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setMaintainCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO1 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO1.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO1.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO1.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO1.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO1.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setExtendWarrantyCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO1));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO2 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO2.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO2.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO2.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO2.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO2.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setAccessoryCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO2));
        goodwillApplyInfoDTO.setCustomerBackground("customerBackground");
        goodwillApplyInfoDTO.setCustomerBackgroundEn("customerBackgroundEn");
        goodwillApplyInfoDTO.setReasonAndDispose("reasonAndDispose");
        goodwillApplyInfoDTO.setReasonAndDisposeEn("reasonAndDisposeEn");
        goodwillApplyInfoDTO.setRepairSolution("repairSolution");
        goodwillApplyInfoDTO.setRepairSolutionEn("repairSolutionEn");
        goodwillApplyInfoDTO.setCustomerRequire("customerRequire");
        goodwillApplyInfoDTO.setCustomerRequireEn("customerRequireEn");
        goodwillApplyInfoDTO.setPotentialRisk("potentialRisk");
        goodwillApplyInfoDTO.setPotentialRiskEn("potentialRiskEn");
        goodwillApplyInfoDTO.setVrOrTjNoEn("vrOrTjNoEn");
        goodwillApplyInfoDTO.setVrOrTjNo("vrOrTjNo");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetailEn("businessGoodwillApplyDetailEn");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetail("businessGoodwillApplyDetail");
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairInfoDTO(Arrays.asList(goodwillApplyRepairInfoDTO));
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairPartInfoDTO(Arrays.asList(goodwillApplyRepairPartInfoDTO));
        goodwillApplyInfoDTO.setId(0L);
        goodwillApplyInfoDTO.setApplyNo("applyNo");
        goodwillApplyInfoDTO.setDealerCode("dealerCode");
        goodwillApplyInfoDTO.setAuditType(0);
        goodwillApplyInfoDTO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setGoodwillStatus(0);
        goodwillApplyInfoDTO.setCustomerPainVue(Arrays.asList(0));
        goodwillApplyInfoDTO.setCustomerPain("customerPain");
        goodwillApplyInfoDTO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setMaterialId(0L);
        goodwillApplyInfoDTO.setRoleList(new String[]{"roleList"});
        goodwillApplyInfoDTO.setRoleList2(new String[]{"roleList2"});
        goodwillApplyInfoDTO.setRoleList1("roleList1");

        when(mockGoodwillApplyInfoMapper.selectGoodwillApplyInfo(any(Page.class),
                any(GoodwillApplyInfoDTO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillApplyInfoDTO> result = goodwillApplyInfoServiceImplUnderTest.selectPageBysql(page,
                goodwillApplyInfoDTO);

        // Verify the results
    }

    @Test
    void testGetOemTodoByPage() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCouponId(0L);
        goodwillApplyInfoDTO.setUserId(0L);
        goodwillApplyInfoDTO.setIsMaintainChange(0);
        goodwillApplyInfoDTO.setIsExtendWarrantyChange(0);
        goodwillApplyInfoDTO.setIsAccessoryChange(0);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setMaintainCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO1 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO1.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO1.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO1.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO1.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO1.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setExtendWarrantyCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO1));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO2 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO2.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO2.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO2.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO2.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO2.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setAccessoryCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO2));
        goodwillApplyInfoDTO.setCustomerBackground("customerBackground");
        goodwillApplyInfoDTO.setCustomerBackgroundEn("customerBackgroundEn");
        goodwillApplyInfoDTO.setReasonAndDispose("reasonAndDispose");
        goodwillApplyInfoDTO.setReasonAndDisposeEn("reasonAndDisposeEn");
        goodwillApplyInfoDTO.setRepairSolution("repairSolution");
        goodwillApplyInfoDTO.setRepairSolutionEn("repairSolutionEn");
        goodwillApplyInfoDTO.setCustomerRequire("customerRequire");
        goodwillApplyInfoDTO.setCustomerRequireEn("customerRequireEn");
        goodwillApplyInfoDTO.setPotentialRisk("potentialRisk");
        goodwillApplyInfoDTO.setPotentialRiskEn("potentialRiskEn");
        goodwillApplyInfoDTO.setVrOrTjNoEn("vrOrTjNoEn");
        goodwillApplyInfoDTO.setVrOrTjNo("vrOrTjNo");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetailEn("businessGoodwillApplyDetailEn");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetail("businessGoodwillApplyDetail");
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairInfoDTO(Arrays.asList(goodwillApplyRepairInfoDTO));
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairPartInfoDTO(Arrays.asList(goodwillApplyRepairPartInfoDTO));
        goodwillApplyInfoDTO.setId(0L);
        goodwillApplyInfoDTO.setApplyNo("applyNo");
        goodwillApplyInfoDTO.setDealerCode("dealerCode");
        goodwillApplyInfoDTO.setAuditType(0);
        goodwillApplyInfoDTO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setGoodwillStatus(0);
        goodwillApplyInfoDTO.setCustomerPainVue(Arrays.asList(0));
        goodwillApplyInfoDTO.setCustomerPain("customerPain");
        goodwillApplyInfoDTO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setMaterialId(0L);
        goodwillApplyInfoDTO.setRoleList(new String[]{"roleList"});
        goodwillApplyInfoDTO.setRoleList2(new String[]{"roleList2"});
        goodwillApplyInfoDTO.setRoleList1("roleList1");

        // Configure GoodwillApplyInfoMapper.selectOemTodoGoodwillApplyInfo(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo("ownerNo");
        goodwillApplyInfoPO.setCouponId(0L);
        final List<GoodwillApplyInfoPO> goodwillApplyInfoPOS = Arrays.asList(goodwillApplyInfoPO);
        when(mockGoodwillApplyInfoMapper.selectOemTodoGoodwillApplyInfo(any(Page.class),
                any(GoodwillApplyInfoDTO.class))).thenReturn(goodwillApplyInfoPOS);

        // Run the test
        final IPage<GoodwillApplyInfoDTO> result = goodwillApplyInfoServiceImplUnderTest.getOemTodoByPage(page,
                goodwillApplyInfoDTO);

        // Verify the results
    }

    @Test
    void testGetOemTodoByPage_GoodwillApplyInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCouponId(0L);
        goodwillApplyInfoDTO.setUserId(0L);
        goodwillApplyInfoDTO.setIsMaintainChange(0);
        goodwillApplyInfoDTO.setIsExtendWarrantyChange(0);
        goodwillApplyInfoDTO.setIsAccessoryChange(0);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setMaintainCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO1 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO1.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO1.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO1.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO1.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO1.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setExtendWarrantyCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO1));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO2 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO2.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO2.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO2.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO2.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO2.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setAccessoryCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO2));
        goodwillApplyInfoDTO.setCustomerBackground("customerBackground");
        goodwillApplyInfoDTO.setCustomerBackgroundEn("customerBackgroundEn");
        goodwillApplyInfoDTO.setReasonAndDispose("reasonAndDispose");
        goodwillApplyInfoDTO.setReasonAndDisposeEn("reasonAndDisposeEn");
        goodwillApplyInfoDTO.setRepairSolution("repairSolution");
        goodwillApplyInfoDTO.setRepairSolutionEn("repairSolutionEn");
        goodwillApplyInfoDTO.setCustomerRequire("customerRequire");
        goodwillApplyInfoDTO.setCustomerRequireEn("customerRequireEn");
        goodwillApplyInfoDTO.setPotentialRisk("potentialRisk");
        goodwillApplyInfoDTO.setPotentialRiskEn("potentialRiskEn");
        goodwillApplyInfoDTO.setVrOrTjNoEn("vrOrTjNoEn");
        goodwillApplyInfoDTO.setVrOrTjNo("vrOrTjNo");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetailEn("businessGoodwillApplyDetailEn");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetail("businessGoodwillApplyDetail");
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairInfoDTO(Arrays.asList(goodwillApplyRepairInfoDTO));
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairPartInfoDTO(Arrays.asList(goodwillApplyRepairPartInfoDTO));
        goodwillApplyInfoDTO.setId(0L);
        goodwillApplyInfoDTO.setApplyNo("applyNo");
        goodwillApplyInfoDTO.setDealerCode("dealerCode");
        goodwillApplyInfoDTO.setAuditType(0);
        goodwillApplyInfoDTO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setGoodwillStatus(0);
        goodwillApplyInfoDTO.setCustomerPainVue(Arrays.asList(0));
        goodwillApplyInfoDTO.setCustomerPain("customerPain");
        goodwillApplyInfoDTO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setMaterialId(0L);
        goodwillApplyInfoDTO.setRoleList(new String[]{"roleList"});
        goodwillApplyInfoDTO.setRoleList2(new String[]{"roleList2"});
        goodwillApplyInfoDTO.setRoleList1("roleList1");

        when(mockGoodwillApplyInfoMapper.selectOemTodoGoodwillApplyInfo(any(Page.class),
                any(GoodwillApplyInfoDTO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillApplyInfoDTO> result = goodwillApplyInfoServiceImplUnderTest.getOemTodoByPage(page,
                goodwillApplyInfoDTO);

        // Verify the results
    }

    @Test
    void testGetOemTodoListByPage() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCouponId(0L);
        goodwillApplyInfoDTO.setUserId(0L);
        goodwillApplyInfoDTO.setIsMaintainChange(0);
        goodwillApplyInfoDTO.setIsExtendWarrantyChange(0);
        goodwillApplyInfoDTO.setIsAccessoryChange(0);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setMaintainCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO1 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO1.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO1.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO1.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO1.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO1.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setExtendWarrantyCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO1));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO2 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO2.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO2.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO2.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO2.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO2.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setAccessoryCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO2));
        goodwillApplyInfoDTO.setCustomerBackground("customerBackground");
        goodwillApplyInfoDTO.setCustomerBackgroundEn("customerBackgroundEn");
        goodwillApplyInfoDTO.setReasonAndDispose("reasonAndDispose");
        goodwillApplyInfoDTO.setReasonAndDisposeEn("reasonAndDisposeEn");
        goodwillApplyInfoDTO.setRepairSolution("repairSolution");
        goodwillApplyInfoDTO.setRepairSolutionEn("repairSolutionEn");
        goodwillApplyInfoDTO.setCustomerRequire("customerRequire");
        goodwillApplyInfoDTO.setCustomerRequireEn("customerRequireEn");
        goodwillApplyInfoDTO.setPotentialRisk("potentialRisk");
        goodwillApplyInfoDTO.setPotentialRiskEn("potentialRiskEn");
        goodwillApplyInfoDTO.setVrOrTjNoEn("vrOrTjNoEn");
        goodwillApplyInfoDTO.setVrOrTjNo("vrOrTjNo");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetailEn("businessGoodwillApplyDetailEn");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetail("businessGoodwillApplyDetail");
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairInfoDTO(Arrays.asList(goodwillApplyRepairInfoDTO));
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairPartInfoDTO(Arrays.asList(goodwillApplyRepairPartInfoDTO));
        goodwillApplyInfoDTO.setId(0L);
        goodwillApplyInfoDTO.setApplyNo("applyNo");
        goodwillApplyInfoDTO.setDealerCode("dealerCode");
        goodwillApplyInfoDTO.setAuditType(0);
        goodwillApplyInfoDTO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setGoodwillStatus(0);
        goodwillApplyInfoDTO.setCustomerPainVue(Arrays.asList(0));
        goodwillApplyInfoDTO.setCustomerPain("customerPain");
        goodwillApplyInfoDTO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setMaterialId(0L);
        goodwillApplyInfoDTO.setRoleList(new String[]{"roleList"});
        goodwillApplyInfoDTO.setRoleList2(new String[]{"roleList2"});
        goodwillApplyInfoDTO.setRoleList1("roleList1");

        // Configure GoodwillApplyInfoMapper.selectOemTodoListGoodwillApplyInfo(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo("ownerNo");
        goodwillApplyInfoPO.setCouponId(0L);
        final List<GoodwillApplyInfoPO> goodwillApplyInfoPOS = Arrays.asList(goodwillApplyInfoPO);
        when(mockGoodwillApplyInfoMapper.selectOemTodoListGoodwillApplyInfo(any(Page.class),
                any(GoodwillApplyInfoDTO.class))).thenReturn(goodwillApplyInfoPOS);

        // Run the test
        final IPage<GoodwillApplyInfoDTO> result = goodwillApplyInfoServiceImplUnderTest.getOemTodoListByPage(page,
                goodwillApplyInfoDTO);

        // Verify the results
    }

    @Test
    void testGetOemTodoListByPage_GoodwillApplyInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCouponId(0L);
        goodwillApplyInfoDTO.setUserId(0L);
        goodwillApplyInfoDTO.setIsMaintainChange(0);
        goodwillApplyInfoDTO.setIsExtendWarrantyChange(0);
        goodwillApplyInfoDTO.setIsAccessoryChange(0);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setMaintainCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO1 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO1.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO1.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO1.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO1.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO1.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setExtendWarrantyCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO1));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO2 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO2.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO2.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO2.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO2.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO2.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setAccessoryCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO2));
        goodwillApplyInfoDTO.setCustomerBackground("customerBackground");
        goodwillApplyInfoDTO.setCustomerBackgroundEn("customerBackgroundEn");
        goodwillApplyInfoDTO.setReasonAndDispose("reasonAndDispose");
        goodwillApplyInfoDTO.setReasonAndDisposeEn("reasonAndDisposeEn");
        goodwillApplyInfoDTO.setRepairSolution("repairSolution");
        goodwillApplyInfoDTO.setRepairSolutionEn("repairSolutionEn");
        goodwillApplyInfoDTO.setCustomerRequire("customerRequire");
        goodwillApplyInfoDTO.setCustomerRequireEn("customerRequireEn");
        goodwillApplyInfoDTO.setPotentialRisk("potentialRisk");
        goodwillApplyInfoDTO.setPotentialRiskEn("potentialRiskEn");
        goodwillApplyInfoDTO.setVrOrTjNoEn("vrOrTjNoEn");
        goodwillApplyInfoDTO.setVrOrTjNo("vrOrTjNo");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetailEn("businessGoodwillApplyDetailEn");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetail("businessGoodwillApplyDetail");
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairInfoDTO(Arrays.asList(goodwillApplyRepairInfoDTO));
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairPartInfoDTO(Arrays.asList(goodwillApplyRepairPartInfoDTO));
        goodwillApplyInfoDTO.setId(0L);
        goodwillApplyInfoDTO.setApplyNo("applyNo");
        goodwillApplyInfoDTO.setDealerCode("dealerCode");
        goodwillApplyInfoDTO.setAuditType(0);
        goodwillApplyInfoDTO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setGoodwillStatus(0);
        goodwillApplyInfoDTO.setCustomerPainVue(Arrays.asList(0));
        goodwillApplyInfoDTO.setCustomerPain("customerPain");
        goodwillApplyInfoDTO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setMaterialId(0L);
        goodwillApplyInfoDTO.setRoleList(new String[]{"roleList"});
        goodwillApplyInfoDTO.setRoleList2(new String[]{"roleList2"});
        goodwillApplyInfoDTO.setRoleList1("roleList1");

        when(mockGoodwillApplyInfoMapper.selectOemTodoListGoodwillApplyInfo(any(Page.class),
                any(GoodwillApplyInfoDTO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillApplyInfoDTO> result = goodwillApplyInfoServiceImplUnderTest.getOemTodoListByPage(page,
                goodwillApplyInfoDTO);

        // Verify the results
    }

    @Test
    void testGetLeftValue_RestTemplateThrowsRestClientException() throws Exception {
        // Setup
        when(mockMidUrlProperties.getMidEndCouponCenter()).thenReturn("result");
        when(mockMidUrlProperties.getAllEx()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final RequestDTO requestDTO = new RequestDTO<>();
        requestDTO.setData(null);
        final HttpEntity<RequestDTO> requestEntity = new HttpEntity<>(requestDTO, new HttpHeaders());
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST, requestEntity, ResponseDTO.class))
                .thenThrow(RestClientException.class);

        // Run the test
        assertThatThrownBy(() -> goodwillApplyInfoServiceImplUnderTest.getLeftValue(new long[]{0L}))
                .isInstanceOf(ServiceBizException.class);
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCouponId(0L);
        goodwillApplyInfoDTO.setUserId(0L);
        goodwillApplyInfoDTO.setIsMaintainChange(0);
        goodwillApplyInfoDTO.setIsExtendWarrantyChange(0);
        goodwillApplyInfoDTO.setIsAccessoryChange(0);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setMaintainCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO1 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO1.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO1.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO1.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO1.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO1.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setExtendWarrantyCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO1));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO2 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO2.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO2.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO2.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO2.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO2.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setAccessoryCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO2));
        goodwillApplyInfoDTO.setCustomerBackground("customerBackground");
        goodwillApplyInfoDTO.setCustomerBackgroundEn("customerBackgroundEn");
        goodwillApplyInfoDTO.setReasonAndDispose("reasonAndDispose");
        goodwillApplyInfoDTO.setReasonAndDisposeEn("reasonAndDisposeEn");
        goodwillApplyInfoDTO.setRepairSolution("repairSolution");
        goodwillApplyInfoDTO.setRepairSolutionEn("repairSolutionEn");
        goodwillApplyInfoDTO.setCustomerRequire("customerRequire");
        goodwillApplyInfoDTO.setCustomerRequireEn("customerRequireEn");
        goodwillApplyInfoDTO.setPotentialRisk("potentialRisk");
        goodwillApplyInfoDTO.setPotentialRiskEn("potentialRiskEn");
        goodwillApplyInfoDTO.setVrOrTjNoEn("vrOrTjNoEn");
        goodwillApplyInfoDTO.setVrOrTjNo("vrOrTjNo");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetailEn("businessGoodwillApplyDetailEn");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetail("businessGoodwillApplyDetail");
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairInfoDTO(Arrays.asList(goodwillApplyRepairInfoDTO));
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairPartInfoDTO(Arrays.asList(goodwillApplyRepairPartInfoDTO));
        goodwillApplyInfoDTO.setId(0L);
        goodwillApplyInfoDTO.setApplyNo("applyNo");
        goodwillApplyInfoDTO.setDealerCode("dealerCode");
        goodwillApplyInfoDTO.setAuditType(0);
        goodwillApplyInfoDTO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setGoodwillStatus(0);
        goodwillApplyInfoDTO.setCustomerPainVue(Arrays.asList(0));
        goodwillApplyInfoDTO.setCustomerPain("customerPain");
        goodwillApplyInfoDTO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setMaterialId(0L);
        goodwillApplyInfoDTO.setRoleList(new String[]{"roleList"});
        goodwillApplyInfoDTO.setRoleList2(new String[]{"roleList2"});
        goodwillApplyInfoDTO.setRoleList1("roleList1");

        // Configure GoodwillApplyInfoMapper.selectListBySql(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo("ownerNo");
        goodwillApplyInfoPO.setCouponId(0L);
        final List<GoodwillApplyInfoPO> goodwillApplyInfoPOS = Arrays.asList(goodwillApplyInfoPO);
        when(mockGoodwillApplyInfoMapper.selectListBySql(any(GoodwillApplyInfoPO.class)))
                .thenReturn(goodwillApplyInfoPOS);

        // Run the test
        final List<GoodwillApplyInfoDTO> result = goodwillApplyInfoServiceImplUnderTest.selectListBySql(
                goodwillApplyInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillApplyInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCouponId(0L);
        goodwillApplyInfoDTO.setUserId(0L);
        goodwillApplyInfoDTO.setIsMaintainChange(0);
        goodwillApplyInfoDTO.setIsExtendWarrantyChange(0);
        goodwillApplyInfoDTO.setIsAccessoryChange(0);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setMaintainCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO1 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO1.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO1.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO1.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO1.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO1.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setExtendWarrantyCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO1));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO2 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO2.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO2.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO2.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO2.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO2.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setAccessoryCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO2));
        goodwillApplyInfoDTO.setCustomerBackground("customerBackground");
        goodwillApplyInfoDTO.setCustomerBackgroundEn("customerBackgroundEn");
        goodwillApplyInfoDTO.setReasonAndDispose("reasonAndDispose");
        goodwillApplyInfoDTO.setReasonAndDisposeEn("reasonAndDisposeEn");
        goodwillApplyInfoDTO.setRepairSolution("repairSolution");
        goodwillApplyInfoDTO.setRepairSolutionEn("repairSolutionEn");
        goodwillApplyInfoDTO.setCustomerRequire("customerRequire");
        goodwillApplyInfoDTO.setCustomerRequireEn("customerRequireEn");
        goodwillApplyInfoDTO.setPotentialRisk("potentialRisk");
        goodwillApplyInfoDTO.setPotentialRiskEn("potentialRiskEn");
        goodwillApplyInfoDTO.setVrOrTjNoEn("vrOrTjNoEn");
        goodwillApplyInfoDTO.setVrOrTjNo("vrOrTjNo");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetailEn("businessGoodwillApplyDetailEn");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetail("businessGoodwillApplyDetail");
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairInfoDTO(Arrays.asList(goodwillApplyRepairInfoDTO));
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairPartInfoDTO(Arrays.asList(goodwillApplyRepairPartInfoDTO));
        goodwillApplyInfoDTO.setId(0L);
        goodwillApplyInfoDTO.setApplyNo("applyNo");
        goodwillApplyInfoDTO.setDealerCode("dealerCode");
        goodwillApplyInfoDTO.setAuditType(0);
        goodwillApplyInfoDTO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setGoodwillStatus(0);
        goodwillApplyInfoDTO.setCustomerPainVue(Arrays.asList(0));
        goodwillApplyInfoDTO.setCustomerPain("customerPain");
        goodwillApplyInfoDTO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setMaterialId(0L);
        goodwillApplyInfoDTO.setRoleList(new String[]{"roleList"});
        goodwillApplyInfoDTO.setRoleList2(new String[]{"roleList2"});
        goodwillApplyInfoDTO.setRoleList1("roleList1");

        when(mockGoodwillApplyInfoMapper.selectListBySql(any(GoodwillApplyInfoPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillApplyInfoDTO> result = goodwillApplyInfoServiceImplUnderTest.selectListBySql(
                goodwillApplyInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure GoodwillApplyInfoMapper.getByApplyId(...).
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCouponId(0L);
        goodwillApplyInfoDTO.setUserId(0L);
        goodwillApplyInfoDTO.setIsMaintainChange(0);
        goodwillApplyInfoDTO.setIsExtendWarrantyChange(0);
        goodwillApplyInfoDTO.setIsAccessoryChange(0);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setMaintainCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO1 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO1.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO1.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO1.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO1.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO1.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setExtendWarrantyCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO1));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO2 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO2.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO2.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO2.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO2.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO2.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setAccessoryCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO2));
        goodwillApplyInfoDTO.setCustomerBackground("customerBackground");
        goodwillApplyInfoDTO.setCustomerBackgroundEn("customerBackgroundEn");
        goodwillApplyInfoDTO.setReasonAndDispose("reasonAndDispose");
        goodwillApplyInfoDTO.setReasonAndDisposeEn("reasonAndDisposeEn");
        goodwillApplyInfoDTO.setRepairSolution("repairSolution");
        goodwillApplyInfoDTO.setRepairSolutionEn("repairSolutionEn");
        goodwillApplyInfoDTO.setCustomerRequire("customerRequire");
        goodwillApplyInfoDTO.setCustomerRequireEn("customerRequireEn");
        goodwillApplyInfoDTO.setPotentialRisk("potentialRisk");
        goodwillApplyInfoDTO.setPotentialRiskEn("potentialRiskEn");
        goodwillApplyInfoDTO.setVrOrTjNoEn("vrOrTjNoEn");
        goodwillApplyInfoDTO.setVrOrTjNo("vrOrTjNo");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetailEn("businessGoodwillApplyDetailEn");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetail("businessGoodwillApplyDetail");
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairInfoDTO(Arrays.asList(goodwillApplyRepairInfoDTO));
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairPartInfoDTO(Arrays.asList(goodwillApplyRepairPartInfoDTO));
        goodwillApplyInfoDTO.setId(0L);
        goodwillApplyInfoDTO.setApplyNo("applyNo");
        goodwillApplyInfoDTO.setDealerCode("dealerCode");
        goodwillApplyInfoDTO.setAuditType(0);
        goodwillApplyInfoDTO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setGoodwillStatus(0);
        goodwillApplyInfoDTO.setCustomerPainVue(Arrays.asList(0));
        goodwillApplyInfoDTO.setCustomerPain("customerPain");
        goodwillApplyInfoDTO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setMaterialId(0L);
        goodwillApplyInfoDTO.setRoleList(new String[]{"roleList"});
        goodwillApplyInfoDTO.setRoleList2(new String[]{"roleList2"});
        goodwillApplyInfoDTO.setRoleList1("roleList1");
        when(mockGoodwillApplyInfoMapper.getByApplyId(0L)).thenReturn(goodwillApplyInfoDTO);

        // Run the test
        final GoodwillApplyInfoDTO result = goodwillApplyInfoServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillApplyInfoMapperReturnsNull() throws Exception {
        // Setup
        when(mockGoodwillApplyInfoMapper.getByApplyId(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillApplyInfoServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testQueryComplaintInfo() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final Map map = new HashMap<>();
        when(mockGoodwillApplyInfoMapper.queryComplaintInfo(any(Page.class), eq(new HashMap<>())))
                .thenReturn(Arrays.asList(new HashMap<>()));
        when(mockGoodwillApplyInfoMapper.queryComplaintCountInfo(new HashMap<>())).thenReturn(0);

        // Run the test
        final IPage<List> result = goodwillApplyInfoServiceImplUnderTest.queryComplaintInfo(page, map);

        // Verify the results
    }

    @Test
    void testQueryComplaintInfo_GoodwillApplyInfoMapperQueryComplaintInfoReturnsNoItems() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final Map map = new HashMap<>();
        when(mockGoodwillApplyInfoMapper.queryComplaintInfo(any(Page.class), eq(new HashMap<>())))
                .thenReturn(Collections.emptyList());
        when(mockGoodwillApplyInfoMapper.queryComplaintCountInfo(new HashMap<>())).thenReturn(0);

        // Run the test
        final IPage<List> result = goodwillApplyInfoServiceImplUnderTest.queryComplaintInfo(page, map);

        // Verify the results
    }

    @Test
    void testQueryPartInfo() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final Map map1 = new HashMap<>();

        // Run the test
        final IPage<List> result = goodwillApplyInfoServiceImplUnderTest.queryPartInfo(page, map1);

        // Verify the results
    }

    @Test
    void testQueryRepairOrderInfo() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final Map map1 = new HashMap<>();

        // Run the test
        final IPage<List> result = goodwillApplyInfoServiceImplUnderTest.queryRepairOrderInfo(page, map1);

        // Verify the results
    }

    @Test
    void testGetBillNo1_ThrowsServiceBizException() throws Exception {
        // Setup
        // Run the test
        assertThatThrownBy(() -> goodwillApplyInfoServiceImplUnderTest.getBillNo1("type", "dealerCode"))
                .isInstanceOf(ServiceBizException.class);
    }

    @Test
    void testSelectOemTodoPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCouponId(0L);
        goodwillApplyInfoDTO.setUserId(0L);
        goodwillApplyInfoDTO.setIsMaintainChange(0);
        goodwillApplyInfoDTO.setIsExtendWarrantyChange(0);
        goodwillApplyInfoDTO.setIsAccessoryChange(0);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setMaintainCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO1 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO1.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO1.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO1.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO1.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO1.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setExtendWarrantyCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO1));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO2 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO2.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO2.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO2.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO2.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO2.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setAccessoryCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO2));
        goodwillApplyInfoDTO.setCustomerBackground("customerBackground");
        goodwillApplyInfoDTO.setCustomerBackgroundEn("customerBackgroundEn");
        goodwillApplyInfoDTO.setReasonAndDispose("reasonAndDispose");
        goodwillApplyInfoDTO.setReasonAndDisposeEn("reasonAndDisposeEn");
        goodwillApplyInfoDTO.setRepairSolution("repairSolution");
        goodwillApplyInfoDTO.setRepairSolutionEn("repairSolutionEn");
        goodwillApplyInfoDTO.setCustomerRequire("customerRequire");
        goodwillApplyInfoDTO.setCustomerRequireEn("customerRequireEn");
        goodwillApplyInfoDTO.setPotentialRisk("potentialRisk");
        goodwillApplyInfoDTO.setPotentialRiskEn("potentialRiskEn");
        goodwillApplyInfoDTO.setVrOrTjNoEn("vrOrTjNoEn");
        goodwillApplyInfoDTO.setVrOrTjNo("vrOrTjNo");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetailEn("businessGoodwillApplyDetailEn");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetail("businessGoodwillApplyDetail");
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairInfoDTO(Arrays.asList(goodwillApplyRepairInfoDTO));
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairPartInfoDTO(Arrays.asList(goodwillApplyRepairPartInfoDTO));
        goodwillApplyInfoDTO.setId(0L);
        goodwillApplyInfoDTO.setApplyNo("applyNo");
        goodwillApplyInfoDTO.setDealerCode("dealerCode");
        goodwillApplyInfoDTO.setAuditType(0);
        goodwillApplyInfoDTO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setGoodwillStatus(0);
        goodwillApplyInfoDTO.setCustomerPainVue(Arrays.asList(0));
        goodwillApplyInfoDTO.setCustomerPain("customerPain");
        goodwillApplyInfoDTO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setMaterialId(0L);
        goodwillApplyInfoDTO.setRoleList(new String[]{"roleList"});
        goodwillApplyInfoDTO.setRoleList2(new String[]{"roleList2"});
        goodwillApplyInfoDTO.setRoleList1("roleList1");

        // Configure GoodwillApplyInfoMapper.selectPageBySql(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo("ownerNo");
        goodwillApplyInfoPO.setCouponId(0L);
        final List<GoodwillApplyInfoPO> goodwillApplyInfoPOS = Arrays.asList(goodwillApplyInfoPO);
        when(mockGoodwillApplyInfoMapper.selectPageBySql(any(Page.class), any(GoodwillApplyInfoPO.class)))
                .thenReturn(goodwillApplyInfoPOS);

        // Run the test
        final IPage<GoodwillApplyInfoDTO> result = goodwillApplyInfoServiceImplUnderTest.selectOemTodoPageBysql(page,
                goodwillApplyInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectOemTodoPageBysql_GoodwillApplyInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCouponId(0L);
        goodwillApplyInfoDTO.setUserId(0L);
        goodwillApplyInfoDTO.setIsMaintainChange(0);
        goodwillApplyInfoDTO.setIsExtendWarrantyChange(0);
        goodwillApplyInfoDTO.setIsAccessoryChange(0);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setMaintainCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO1 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO1.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO1.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO1.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO1.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO1.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setExtendWarrantyCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO1));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO2 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO2.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO2.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO2.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO2.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO2.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setAccessoryCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO2));
        goodwillApplyInfoDTO.setCustomerBackground("customerBackground");
        goodwillApplyInfoDTO.setCustomerBackgroundEn("customerBackgroundEn");
        goodwillApplyInfoDTO.setReasonAndDispose("reasonAndDispose");
        goodwillApplyInfoDTO.setReasonAndDisposeEn("reasonAndDisposeEn");
        goodwillApplyInfoDTO.setRepairSolution("repairSolution");
        goodwillApplyInfoDTO.setRepairSolutionEn("repairSolutionEn");
        goodwillApplyInfoDTO.setCustomerRequire("customerRequire");
        goodwillApplyInfoDTO.setCustomerRequireEn("customerRequireEn");
        goodwillApplyInfoDTO.setPotentialRisk("potentialRisk");
        goodwillApplyInfoDTO.setPotentialRiskEn("potentialRiskEn");
        goodwillApplyInfoDTO.setVrOrTjNoEn("vrOrTjNoEn");
        goodwillApplyInfoDTO.setVrOrTjNo("vrOrTjNo");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetailEn("businessGoodwillApplyDetailEn");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetail("businessGoodwillApplyDetail");
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairInfoDTO(Arrays.asList(goodwillApplyRepairInfoDTO));
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairPartInfoDTO(Arrays.asList(goodwillApplyRepairPartInfoDTO));
        goodwillApplyInfoDTO.setId(0L);
        goodwillApplyInfoDTO.setApplyNo("applyNo");
        goodwillApplyInfoDTO.setDealerCode("dealerCode");
        goodwillApplyInfoDTO.setAuditType(0);
        goodwillApplyInfoDTO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setGoodwillStatus(0);
        goodwillApplyInfoDTO.setCustomerPainVue(Arrays.asList(0));
        goodwillApplyInfoDTO.setCustomerPain("customerPain");
        goodwillApplyInfoDTO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setMaterialId(0L);
        goodwillApplyInfoDTO.setRoleList(new String[]{"roleList"});
        goodwillApplyInfoDTO.setRoleList2(new String[]{"roleList2"});
        goodwillApplyInfoDTO.setRoleList1("roleList1");

        when(mockGoodwillApplyInfoMapper.selectPageBySql(any(Page.class), any(GoodwillApplyInfoPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillApplyInfoDTO> result = goodwillApplyInfoServiceImplUnderTest.selectOemTodoPageBysql(page,
                goodwillApplyInfoDTO);

        // Verify the results
    }

    @Test
    void testQueryApplyHistory() throws Exception {
        // Setup
        // Configure GoodwillApplyInfoMapper.queryApplyHistory(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo("ownerNo");
        goodwillApplyInfoPO.setCouponId(0L);
        final List<GoodwillApplyInfoPO> goodwillApplyInfoPOS = Arrays.asList(goodwillApplyInfoPO);
        when(mockGoodwillApplyInfoMapper.queryApplyHistory("vin", "applyNo")).thenReturn(goodwillApplyInfoPOS);

        // Run the test
        final List<GoodwillApplyInfoPO> result = goodwillApplyInfoServiceImplUnderTest.queryApplyHistory("vin",
                "applyNo");

        // Verify the results
    }

    @Test
    void testQueryApplyHistory_GoodwillApplyInfoMapperReturnsNoItems() throws Exception {
        // Setup
        when(mockGoodwillApplyInfoMapper.queryApplyHistory("vin", "applyNo")).thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillApplyInfoPO> result = goodwillApplyInfoServiceImplUnderTest.queryApplyHistory("vin",
                "applyNo");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testQueryApplyByDealerCode() throws Exception {
        // Setup
        when(mockGoodwillApplyInfoMapper.queryApplyByDealerCode("dealerCode", 2020)).thenReturn(new HashMap<>());

        // Run the test
        final Map result = goodwillApplyInfoServiceImplUnderTest.queryApplyByDealerCode("dealerCode", 2020);

        // Verify the results
    }

    @Test
    void testEditSupportApplyInfo() throws Exception {
        // Setup
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCouponId(0L);
        goodwillApplyInfoDTO.setUserId(0L);
        goodwillApplyInfoDTO.setIsMaintainChange(0);
        goodwillApplyInfoDTO.setIsExtendWarrantyChange(0);
        goodwillApplyInfoDTO.setIsAccessoryChange(0);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setMaintainCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO1 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO1.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO1.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO1.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO1.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO1.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setExtendWarrantyCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO1));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO2 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO2.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO2.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO2.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO2.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO2.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setAccessoryCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO2));
        goodwillApplyInfoDTO.setCustomerBackground("customerBackground");
        goodwillApplyInfoDTO.setCustomerBackgroundEn("customerBackgroundEn");
        goodwillApplyInfoDTO.setReasonAndDispose("reasonAndDispose");
        goodwillApplyInfoDTO.setReasonAndDisposeEn("reasonAndDisposeEn");
        goodwillApplyInfoDTO.setRepairSolution("repairSolution");
        goodwillApplyInfoDTO.setRepairSolutionEn("repairSolutionEn");
        goodwillApplyInfoDTO.setCustomerRequire("customerRequire");
        goodwillApplyInfoDTO.setCustomerRequireEn("customerRequireEn");
        goodwillApplyInfoDTO.setPotentialRisk("potentialRisk");
        goodwillApplyInfoDTO.setPotentialRiskEn("potentialRiskEn");
        goodwillApplyInfoDTO.setVrOrTjNoEn("vrOrTjNoEn");
        goodwillApplyInfoDTO.setVrOrTjNo("vrOrTjNo");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetailEn("businessGoodwillApplyDetailEn");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetail("businessGoodwillApplyDetail");
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairInfoDTO(Arrays.asList(goodwillApplyRepairInfoDTO));
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairPartInfoDTO(Arrays.asList(goodwillApplyRepairPartInfoDTO));
        goodwillApplyInfoDTO.setId(0L);
        goodwillApplyInfoDTO.setApplyNo("applyNo");
        goodwillApplyInfoDTO.setDealerCode("dealerCode");
        goodwillApplyInfoDTO.setAuditType(0);
        goodwillApplyInfoDTO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setGoodwillStatus(0);
        goodwillApplyInfoDTO.setCustomerPainVue(Arrays.asList(0));
        goodwillApplyInfoDTO.setCustomerPain("customerPain");
        goodwillApplyInfoDTO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setMaterialId(0L);
        goodwillApplyInfoDTO.setRoleList(new String[]{"roleList"});
        goodwillApplyInfoDTO.setRoleList2(new String[]{"roleList2"});
        goodwillApplyInfoDTO.setRoleList1("roleList1");

        // Configure GoodwillMaterialAuditMapper.selectById(...).
        final GoodwillMaterialAuditPO goodwillMaterialAuditPO = new GoodwillMaterialAuditPO();
        goodwillMaterialAuditPO.setCreatedBy("hand");
        goodwillMaterialAuditPO.setAppId("appId");
        goodwillMaterialAuditPO.setOwnerCode("ownerCode");
        goodwillMaterialAuditPO.setOwnerParCode("ownerParCode");
        goodwillMaterialAuditPO.setOrgId(0);
        when(mockGoodwillMaterialAuditMapper.selectById(0L)).thenReturn(goodwillMaterialAuditPO);

        when(mockGoodwillMaterialAuditMapper.updateById(any(GoodwillMaterialAuditPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillApplyInfoServiceImplUnderTest.editSupportApplyInfo(goodwillApplyInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSaveCouponToMidCouponCenter_RestTemplateThrowsRestClientException() throws Exception {
        // Setup
        final RequestDTO<SaveCouponInfoDTO> requestDto = new RequestDTO<>();
        final SaveCouponInfoDTO saveCouponInfoDTO = new SaveCouponInfoDTO();
        saveCouponInfoDTO.setUseScenes(0);
        saveCouponInfoDTO.setCreateBy(0L);
        final UseRule useRule = new UseRule();
        useRule.setLimitDealer(Arrays.asList("value"));
        saveCouponInfoDTO.setUseRule(useRule);
        saveCouponInfoDTO.setKindness(0);
        saveCouponInfoDTO.setIsVolid(0);
        saveCouponInfoDTO.setCouponCode("applyNo");
        saveCouponInfoDTO.setCouponName("couponName");
        saveCouponInfoDTO.setCouponType(0);
        saveCouponInfoDTO.setCouponValue(new BigDecimal("0.00"));
        saveCouponInfoDTO.setDenomination("denomination");
        saveCouponInfoDTO.setTermType(0);
        saveCouponInfoDTO.setStartDate("startDate");
        saveCouponInfoDTO.setEndDate("endDate");
        saveCouponInfoDTO.setAsList(0);
        saveCouponInfoDTO.setTotalGet(0);
        saveCouponInfoDTO.setCouponExplain("couponExplain");
        saveCouponInfoDTO.setServiceType(0);
        saveCouponInfoDTO.setIsOccupied(0);
        saveCouponInfoDTO.setPublishState(0);
        saveCouponInfoDTO.setCreator("applyPerson");
        requestDto.setData(saveCouponInfoDTO);

        when(mockMidUrlProperties.getMidEndCouponCenter()).thenReturn("result");
        when(mockMidUrlProperties.getTtCouponInfo()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final RequestDTO requestDTO = new RequestDTO<>();
        requestDTO.setData(null);
        final HttpEntity<RequestDTO> requestEntity = new HttpEntity<>(requestDTO, new HttpHeaders());
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST, requestEntity, ResponseDTO.class))
                .thenThrow(RestClientException.class);

        // Run the test
        assertThatThrownBy(
                () -> goodwillApplyInfoServiceImplUnderTest.saveCouponToMidCouponCenter(requestDto))
                .isInstanceOf(ServiceBizException.class);
    }

    @Test
    void testGetCouponDetail_RestTemplateThrowsRestClientException() throws Exception {
        // Setup
        final RequestDTO<SaveCouponDetailDTO> requestDto = new RequestDTO<>();
        final SaveCouponDetailDTO saveCouponDetailDTO = new SaveCouponDetailDTO();
        saveCouponDetailDTO.setCouponId(0L);
        saveCouponDetailDTO.setTicketState(0);
        saveCouponDetailDTO.setCouponSource(0);
        saveCouponDetailDTO.setGetDate("getDate");
        saveCouponDetailDTO.setOneId(0);
        saveCouponDetailDTO.setVin("vin");
        requestDto.setData(saveCouponDetailDTO);

        when(mockMidUrlProperties.getMidEndCouponCenter()).thenReturn("result");
        when(mockMidUrlProperties.getTtCouponDetail()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final RequestDTO requestDTO = new RequestDTO<>();
        requestDTO.setData(null);
        final HttpEntity<RequestDTO> requestEntity = new HttpEntity<>(requestDTO, new HttpHeaders());
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST, requestEntity, ResponseDTO.class))
                .thenThrow(RestClientException.class);

        // Run the test
        assertThatThrownBy(() -> goodwillApplyInfoServiceImplUnderTest.getCouponDetail(requestDto))
                .isInstanceOf(ServiceBizException.class);
    }

    @Test
    void testRechargeCouponDetail_RestTemplateThrowsRestClientException() throws Exception {
        // Setup
        final RequestDTO<SaveCouponRechargeDTO> requestDto = new RequestDTO<>();
        final SaveCouponRechargeDTO saveCouponRechargeDTO = new SaveCouponRechargeDTO();
        saveCouponRechargeDTO.setCouponId(0L);
        saveCouponRechargeDTO.setCouponDetailId(0L);
        saveCouponRechargeDTO.setCustomerId(0L);
        saveCouponRechargeDTO.setBeforeRechargeAmount(new BigDecimal("0.00"));
        requestDto.setData(saveCouponRechargeDTO);

        when(mockMidUrlProperties.getMidEndCouponCenter()).thenReturn("result");
        when(mockMidUrlProperties.getCouponRecharge()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final RequestDTO requestDTO = new RequestDTO<>();
        requestDTO.setData(null);
        final HttpEntity<RequestDTO> requestEntity = new HttpEntity<>(requestDTO, new HttpHeaders());
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST, requestEntity, ResponseDTO.class))
                .thenThrow(RestClientException.class);

        // Run the test
        assertThatThrownBy(() -> goodwillApplyInfoServiceImplUnderTest.rechargeCouponDetail(requestDto))
                .isInstanceOf(ServiceBizException.class);
    }

    @Test
    void testQueryPaymentInfoByGoodwillApplyId() throws Exception {
        // Setup
        // Configure GoodwillNoticeInvoiceInfoMapper.queryPaymentInfoByGoodwillApplyId(...).
        final GoodwillNoticeInvoiceInfoPO goodwillNoticeInvoiceInfoPO = new GoodwillNoticeInvoiceInfoPO();
        goodwillNoticeInvoiceInfoPO.setCreatedBy("hand");
        goodwillNoticeInvoiceInfoPO.setVoucherStatus(0);
        goodwillNoticeInvoiceInfoPO.setVoucherTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillNoticeInvoiceInfoPO.setCreditsStatus(0);
        goodwillNoticeInvoiceInfoPO.setCreditsTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillNoticeInvoiceInfoPO.setIsConfirm(0);
        goodwillNoticeInvoiceInfoPO.setIsCommit(0);
        goodwillNoticeInvoiceInfoPO.setInvoiceObject(0);
        goodwillNoticeInvoiceInfoPO.setVoucherType(0);
        goodwillNoticeInvoiceInfoPO.setNoticeInvoiceDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillNoticeInvoiceInfoPO.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoPO.setName("name");
        goodwillNoticeInvoiceInfoPO.setVoucherRechargePrice(new BigDecimal("0.00"));
        goodwillNoticeInvoiceInfoPO.setVoucherCouponFaceRechargePrice(new BigDecimal("0.00"));
        when(mockGoodwillNoticeInvoiceInfoMapper.queryPaymentInfoByGoodwillApplyId(0L))
                .thenReturn(goodwillNoticeInvoiceInfoPO);

        // Run the test
        final GoodwillNoticeInvoiceInfoDTO result = goodwillApplyInfoServiceImplUnderTest.queryPaymentInfoByGoodwillApplyId(
                0L);

        // Verify the results
    }

    @Test
    void testQueryPaymentInfoByGoodwillApplyId_GoodwillNoticeInvoiceInfoMapperReturnsNull() throws Exception {
        // Setup
        when(mockGoodwillNoticeInvoiceInfoMapper.queryPaymentInfoByGoodwillApplyId(0L)).thenReturn(null);

        // Run the test
        final GoodwillNoticeInvoiceInfoDTO result = goodwillApplyInfoServiceImplUnderTest.queryPaymentInfoByGoodwillApplyId(
                0L);

        // Verify the results
    }

    @Test
    void testSaveInvoice() throws Exception {
        // Setup
        final GoodwillNoticeInvoiceInfoDTO goodwillNoticeInvoiceInfoDto = new GoodwillNoticeInvoiceInfoDTO();
        final GoodwillInvoiceRecordDTO goodwillInvoiceRecordDTO = new GoodwillInvoiceRecordDTO();
        goodwillInvoiceRecordDTO.setId(0L);
        goodwillInvoiceRecordDTO.setNoticeInvoiceId(0L);
        goodwillInvoiceRecordDTO.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setGoodwillInvoiceRecordDTO(Arrays.asList(goodwillInvoiceRecordDTO));
        goodwillNoticeInvoiceInfoDto.setGoodwillApplyId(0L);
        goodwillNoticeInvoiceInfoDto.setInvoiceId("invoiceId");
        goodwillNoticeInvoiceInfoDto.setInvoiceTitle("invoiceTitle");
        goodwillNoticeInvoiceInfoDto.setNoticeInvoicePrice(new BigDecimal("0.00"));
        goodwillNoticeInvoiceInfoDto.setVolvoCreditsRechargePrice(new BigDecimal("0.00"));
        goodwillNoticeInvoiceInfoDto.setVoucherCouponFaceRechargePrice(new BigDecimal("0.00"));

        // Configure GoodwillInvoiceRecordMapper.selectById(...).
        final GoodwillInvoiceRecordPO goodwillInvoiceRecordPO = new GoodwillInvoiceRecordPO();
        goodwillInvoiceRecordPO.setCreatedBy("hand");
        goodwillInvoiceRecordPO.setAppId("appId");
        goodwillInvoiceRecordPO.setGoodwillApplyId(0L);
        goodwillInvoiceRecordPO.setNoticeInvoiceId(0L);
        goodwillInvoiceRecordPO.setInvoiceId("invoiceId");
        when(mockGoodwillInvoiceRecordMapper.selectById(0L)).thenReturn(goodwillInvoiceRecordPO);

        // Run the test
        final int result = goodwillApplyInfoServiceImplUnderTest.saveInvoice(goodwillNoticeInvoiceInfoDto);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockGoodwillInvoiceRecordMapper).updateById(any(GoodwillInvoiceRecordPO.class));
    }

    @Test
    void testQueryInvoiceInfo() throws Exception {
        // Setup
        when(mockGoodwillInvoiceRecordMapper.queryInvoiceInfo(0L)).thenReturn(Arrays.asList(new HashMap<>()));

        // Run the test
        final List<Map> result = goodwillApplyInfoServiceImplUnderTest.queryInvoiceInfo(0L);

        // Verify the results
    }

    @Test
    void testQueryInvoiceInfo_GoodwillInvoiceRecordMapperReturnsNoItems() throws Exception {
        // Setup
        when(mockGoodwillInvoiceRecordMapper.queryInvoiceInfo(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<Map> result = goodwillApplyInfoServiceImplUnderTest.queryInvoiceInfo(0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testQueryOemInvoiceInfo() throws Exception {
        // Setup
        when(mockGoodwillInvoiceRecordMapper.queryOemInvoiceInfo(0L)).thenReturn(Arrays.asList(new HashMap<>()));

        // Run the test
        final List<Map> result = goodwillApplyInfoServiceImplUnderTest.queryOemInvoiceInfo(0L);

        // Verify the results
    }

    @Test
    void testQueryOemInvoiceInfo_GoodwillInvoiceRecordMapperReturnsNoItems() throws Exception {
        // Setup
        when(mockGoodwillInvoiceRecordMapper.queryOemInvoiceInfo(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<Map> result = goodwillApplyInfoServiceImplUnderTest.queryOemInvoiceInfo(0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testQuerySupportApplyAuditInfo() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCouponId(0L);
        goodwillApplyInfoDTO.setUserId(0L);
        goodwillApplyInfoDTO.setIsMaintainChange(0);
        goodwillApplyInfoDTO.setIsExtendWarrantyChange(0);
        goodwillApplyInfoDTO.setIsAccessoryChange(0);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setMaintainCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO1 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO1.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO1.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO1.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO1.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO1.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setExtendWarrantyCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO1));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO2 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO2.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO2.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO2.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO2.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO2.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setAccessoryCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO2));
        goodwillApplyInfoDTO.setCustomerBackground("customerBackground");
        goodwillApplyInfoDTO.setCustomerBackgroundEn("customerBackgroundEn");
        goodwillApplyInfoDTO.setReasonAndDispose("reasonAndDispose");
        goodwillApplyInfoDTO.setReasonAndDisposeEn("reasonAndDisposeEn");
        goodwillApplyInfoDTO.setRepairSolution("repairSolution");
        goodwillApplyInfoDTO.setRepairSolutionEn("repairSolutionEn");
        goodwillApplyInfoDTO.setCustomerRequire("customerRequire");
        goodwillApplyInfoDTO.setCustomerRequireEn("customerRequireEn");
        goodwillApplyInfoDTO.setPotentialRisk("potentialRisk");
        goodwillApplyInfoDTO.setPotentialRiskEn("potentialRiskEn");
        goodwillApplyInfoDTO.setVrOrTjNoEn("vrOrTjNoEn");
        goodwillApplyInfoDTO.setVrOrTjNo("vrOrTjNo");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetailEn("businessGoodwillApplyDetailEn");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetail("businessGoodwillApplyDetail");
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairInfoDTO(Arrays.asList(goodwillApplyRepairInfoDTO));
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairPartInfoDTO(Arrays.asList(goodwillApplyRepairPartInfoDTO));
        goodwillApplyInfoDTO.setId(0L);
        goodwillApplyInfoDTO.setApplyNo("applyNo");
        goodwillApplyInfoDTO.setDealerCode("dealerCode");
        goodwillApplyInfoDTO.setAuditType(0);
        goodwillApplyInfoDTO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setGoodwillStatus(0);
        goodwillApplyInfoDTO.setCustomerPainVue(Arrays.asList(0));
        goodwillApplyInfoDTO.setCustomerPain("customerPain");
        goodwillApplyInfoDTO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setMaterialId(0L);
        goodwillApplyInfoDTO.setRoleList(new String[]{"roleList"});
        goodwillApplyInfoDTO.setRoleList2(new String[]{"roleList2"});
        goodwillApplyInfoDTO.setRoleList1("roleList1");

        // Configure GoodwillApplyInfoMapper.querySupportApplyAuditInfo(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo("ownerNo");
        goodwillApplyInfoPO.setCouponId(0L);
        final List<GoodwillApplyInfoPO> goodwillApplyInfoPOS = Arrays.asList(goodwillApplyInfoPO);
        when(mockGoodwillApplyInfoMapper.querySupportApplyAuditInfo(any(Page.class),
                any(GoodwillApplyInfoDTO.class))).thenReturn(goodwillApplyInfoPOS);

        // Run the test
        final IPage<GoodwillApplyInfoDTO> result = goodwillApplyInfoServiceImplUnderTest.querySupportApplyAuditInfo(
                page, goodwillApplyInfoDTO);

        // Verify the results
    }

    @Test
    void testQuerySupportApplyAuditInfo_GoodwillApplyInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCouponId(0L);
        goodwillApplyInfoDTO.setUserId(0L);
        goodwillApplyInfoDTO.setIsMaintainChange(0);
        goodwillApplyInfoDTO.setIsExtendWarrantyChange(0);
        goodwillApplyInfoDTO.setIsAccessoryChange(0);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setMaintainCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO1 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO1.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO1.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO1.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO1.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO1.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setExtendWarrantyCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO1));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO2 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO2.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO2.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO2.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO2.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO2.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setAccessoryCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO2));
        goodwillApplyInfoDTO.setCustomerBackground("customerBackground");
        goodwillApplyInfoDTO.setCustomerBackgroundEn("customerBackgroundEn");
        goodwillApplyInfoDTO.setReasonAndDispose("reasonAndDispose");
        goodwillApplyInfoDTO.setReasonAndDisposeEn("reasonAndDisposeEn");
        goodwillApplyInfoDTO.setRepairSolution("repairSolution");
        goodwillApplyInfoDTO.setRepairSolutionEn("repairSolutionEn");
        goodwillApplyInfoDTO.setCustomerRequire("customerRequire");
        goodwillApplyInfoDTO.setCustomerRequireEn("customerRequireEn");
        goodwillApplyInfoDTO.setPotentialRisk("potentialRisk");
        goodwillApplyInfoDTO.setPotentialRiskEn("potentialRiskEn");
        goodwillApplyInfoDTO.setVrOrTjNoEn("vrOrTjNoEn");
        goodwillApplyInfoDTO.setVrOrTjNo("vrOrTjNo");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetailEn("businessGoodwillApplyDetailEn");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetail("businessGoodwillApplyDetail");
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairInfoDTO(Arrays.asList(goodwillApplyRepairInfoDTO));
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairPartInfoDTO(Arrays.asList(goodwillApplyRepairPartInfoDTO));
        goodwillApplyInfoDTO.setId(0L);
        goodwillApplyInfoDTO.setApplyNo("applyNo");
        goodwillApplyInfoDTO.setDealerCode("dealerCode");
        goodwillApplyInfoDTO.setAuditType(0);
        goodwillApplyInfoDTO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setGoodwillStatus(0);
        goodwillApplyInfoDTO.setCustomerPainVue(Arrays.asList(0));
        goodwillApplyInfoDTO.setCustomerPain("customerPain");
        goodwillApplyInfoDTO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setMaterialId(0L);
        goodwillApplyInfoDTO.setRoleList(new String[]{"roleList"});
        goodwillApplyInfoDTO.setRoleList2(new String[]{"roleList2"});
        goodwillApplyInfoDTO.setRoleList1("roleList1");

        when(mockGoodwillApplyInfoMapper.querySupportApplyAuditInfo(any(Page.class),
                any(GoodwillApplyInfoDTO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillApplyInfoDTO> result = goodwillApplyInfoServiceImplUnderTest.querySupportApplyAuditInfo(
                page, goodwillApplyInfoDTO);

        // Verify the results
    }

    @Test
    void testQueryNoticeInvoiceInfo() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        when(mockGoodwillApplyInfoMapper.queryNoticeInvoiceInfo(any(Page.class), eq(0L)))
                .thenReturn(Arrays.asList(new HashMap<>()));
        when(mockGoodwillApplyInfoMapper.queryNoticeInvoiceCount(0L)).thenReturn(0);

        // Run the test
        final IPage<List> result = goodwillApplyInfoServiceImplUnderTest.queryNoticeInvoiceInfo(page, 0L);

        // Verify the results
    }

    @Test
    void testQueryNoticeInvoiceInfo_GoodwillApplyInfoMapperQueryNoticeInvoiceInfoReturnsNoItems() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        when(mockGoodwillApplyInfoMapper.queryNoticeInvoiceInfo(any(Page.class), eq(0L)))
                .thenReturn(Collections.emptyList());
        when(mockGoodwillApplyInfoMapper.queryNoticeInvoiceCount(0L)).thenReturn(0);

        // Run the test
        final IPage<List> result = goodwillApplyInfoServiceImplUnderTest.queryNoticeInvoiceInfo(page, 0L);

        // Verify the results
    }

    @Test
    void testQueryPrintInfo() throws Exception {
        // Setup
        when(mockGoodwillApplyInfoMapper.queryPrintInfo(0L)).thenReturn(new HashMap<>());

        // Run the test
        final Map result = goodwillApplyInfoServiceImplUnderTest.queryPrintInfo(0L);

        // Verify the results
    }

    @Test
    void testGetConsume1_RestTemplateThrowsRestClientException() throws Exception {
        // Setup
        when(mockMidUrlProperties.getMidEndCouponCenter()).thenReturn("result");
        when(mockMidUrlProperties.getTtCouponVerifyAll()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final RequestDTO requestDTO = new RequestDTO<>();
        requestDTO.setData(null);
        final HttpEntity<RequestDTO> requestEntity = new HttpEntity<>(requestDTO, new HttpHeaders());
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST, requestEntity, ResponseDTO.class))
                .thenThrow(RestClientException.class);

        // Run the test
        assertThatThrownBy(() -> goodwillApplyInfoServiceImplUnderTest.getConsume(new String[]{"ids"}))
                .isInstanceOf(ServiceBizException.class);
    }

    @Test
    void testSendEmail() throws Exception {
        // Setup
        final GoodwillApplyInfoPO goodwillApplyInfoPo = new GoodwillApplyInfoPO();
        goodwillApplyInfoPo.setCreatedBy("hand");
        goodwillApplyInfoPo.setAuditRole("auditRole");
        goodwillApplyInfoPo.setAuditName("auditName");
        goodwillApplyInfoPo.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPo.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPo.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPo.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPo.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPo.setIsNeedTranslate(0);
        goodwillApplyInfoPo.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPo.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPo.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPo.setDealerName("dealerName");
        goodwillApplyInfoPo.setAreaManageId(0);
        goodwillApplyInfoPo.setAreaManage("areaManage");
        goodwillApplyInfoPo.setApplyPerson("applyPerson");
        goodwillApplyInfoPo.setId(0L);
        goodwillApplyInfoPo.setApplyNo("applyNo");
        goodwillApplyInfoPo.setDealerCode("auditRole");
        goodwillApplyInfoPo.setSmallAreaId(0);
        goodwillApplyInfoPo.setSmallArea("smallArea");
        goodwillApplyInfoPo.setAuditType(0);
        goodwillApplyInfoPo.setGoodwillNature(0);
        goodwillApplyInfoPo.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPo.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPo.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPo.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPo.setGoodwillStatus(0);
        goodwillApplyInfoPo.setLastGoodwillStatus(0);
        goodwillApplyInfoPo.setVin("vin");
        goodwillApplyInfoPo.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPo.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPo.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPo.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPo.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPo.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPo.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPo.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPo.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPo.setOwnerNo("ownerNo");
        goodwillApplyInfoPo.setCouponId(0L);

        // Run the test
        goodwillApplyInfoServiceImplUnderTest.sendEmail(goodwillApplyInfoPo, 0, "auditRole", 0);

        // Verify the results
        verify(mockGoodwillApplyInfoServiceHelper).sendEmail(any(GoodwillApplyInfoPO.class), eq(0), eq("auditRole"),
                eq(0));
    }

    @Test
    void testExportSupportApplyAuditInfo() throws Exception {
        // Setup
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCouponId(0L);
        goodwillApplyInfoDTO.setUserId(0L);
        goodwillApplyInfoDTO.setIsMaintainChange(0);
        goodwillApplyInfoDTO.setIsExtendWarrantyChange(0);
        goodwillApplyInfoDTO.setIsAccessoryChange(0);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setMaintainCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO1 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO1.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO1.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO1.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO1.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO1.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setExtendWarrantyCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO1));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO2 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO2.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO2.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO2.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO2.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO2.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setAccessoryCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO2));
        goodwillApplyInfoDTO.setCustomerBackground("customerBackground");
        goodwillApplyInfoDTO.setCustomerBackgroundEn("customerBackgroundEn");
        goodwillApplyInfoDTO.setReasonAndDispose("reasonAndDispose");
        goodwillApplyInfoDTO.setReasonAndDisposeEn("reasonAndDisposeEn");
        goodwillApplyInfoDTO.setRepairSolution("repairSolution");
        goodwillApplyInfoDTO.setRepairSolutionEn("repairSolutionEn");
        goodwillApplyInfoDTO.setCustomerRequire("customerRequire");
        goodwillApplyInfoDTO.setCustomerRequireEn("customerRequireEn");
        goodwillApplyInfoDTO.setPotentialRisk("potentialRisk");
        goodwillApplyInfoDTO.setPotentialRiskEn("potentialRiskEn");
        goodwillApplyInfoDTO.setVrOrTjNoEn("vrOrTjNoEn");
        goodwillApplyInfoDTO.setVrOrTjNo("vrOrTjNo");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetailEn("businessGoodwillApplyDetailEn");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetail("businessGoodwillApplyDetail");
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairInfoDTO(Arrays.asList(goodwillApplyRepairInfoDTO));
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairPartInfoDTO(Arrays.asList(goodwillApplyRepairPartInfoDTO));
        goodwillApplyInfoDTO.setId(0L);
        goodwillApplyInfoDTO.setApplyNo("applyNo");
        goodwillApplyInfoDTO.setDealerCode("dealerCode");
        goodwillApplyInfoDTO.setAuditType(0);
        goodwillApplyInfoDTO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setGoodwillStatus(0);
        goodwillApplyInfoDTO.setCustomerPainVue(Arrays.asList(0));
        goodwillApplyInfoDTO.setCustomerPain("customerPain");
        goodwillApplyInfoDTO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setMaterialId(0L);
        goodwillApplyInfoDTO.setRoleList(new String[]{"roleList"});
        goodwillApplyInfoDTO.setRoleList2(new String[]{"roleList2"});
        goodwillApplyInfoDTO.setRoleList1("roleList1");

        // Configure GoodwillApplyInfoMapper.exportSupportApplyAuditInfo(...).
        final GoodwillApplyInfoPO goodwillApplyInfoPO = new GoodwillApplyInfoPO();
        goodwillApplyInfoPO.setCreatedBy("hand");
        goodwillApplyInfoPO.setAuditRole("auditRole");
        goodwillApplyInfoPO.setAuditName("auditName");
        goodwillApplyInfoPO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setIsNeedTranslate(0);
        goodwillApplyInfoPO.setPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setMaterialPassTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setDealerName("dealerName");
        goodwillApplyInfoPO.setAreaManageId(0);
        goodwillApplyInfoPO.setAreaManage("areaManage");
        goodwillApplyInfoPO.setApplyPerson("applyPerson");
        goodwillApplyInfoPO.setId(0L);
        goodwillApplyInfoPO.setApplyNo("applyNo");
        goodwillApplyInfoPO.setDealerCode("auditRole");
        goodwillApplyInfoPO.setSmallAreaId(0);
        goodwillApplyInfoPO.setSmallArea("smallArea");
        goodwillApplyInfoPO.setAuditType(0);
        goodwillApplyInfoPO.setGoodwillNature(0);
        goodwillApplyInfoPO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setAuditAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setGoodwillStatus(0);
        goodwillApplyInfoPO.setLastGoodwillStatus(0);
        goodwillApplyInfoPO.setVin("vin");
        goodwillApplyInfoPO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setCommitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoPO.setVoucherInvoiceAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPO.setUpdatedPerson("applyPerson");
        goodwillApplyInfoPO.setOwnerNo("ownerNo");
        goodwillApplyInfoPO.setCouponId(0L);
        final List<GoodwillApplyInfoPO> goodwillApplyInfoPOS = Arrays.asList(goodwillApplyInfoPO);
        when(mockGoodwillApplyInfoMapper.exportSupportApplyAuditInfo(any(GoodwillApplyInfoDTO.class)))
                .thenReturn(goodwillApplyInfoPOS);

        // Run the test
        final List<GoodwillApplyInfoDTO> result = goodwillApplyInfoServiceImplUnderTest.exportSupportApplyAuditInfo(
                goodwillApplyInfoDTO);

        // Verify the results
    }

    @Test
    void testExportSupportApplyAuditInfo_GoodwillApplyInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setRechargeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setUsedAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setLeftAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCouponId(0L);
        goodwillApplyInfoDTO.setUserId(0L);
        goodwillApplyInfoDTO.setIsMaintainChange(0);
        goodwillApplyInfoDTO.setIsExtendWarrantyChange(0);
        goodwillApplyInfoDTO.setIsAccessoryChange(0);
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setMaintainCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO1 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO1.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO1.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO1.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO1.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO1.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setExtendWarrantyCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO1));
        final GoodwillApplyFinalPartInfoDTO goodwillApplyFinalPartInfoDTO2 = new GoodwillApplyFinalPartInfoDTO();
        goodwillApplyFinalPartInfoDTO2.setGoodwillApplyId(0L);
        goodwillApplyFinalPartInfoDTO2.setGoodwillType(0);
        goodwillApplyFinalPartInfoDTO2.setUnitPriceTax("quantity");
        goodwillApplyFinalPartInfoDTO2.setQuantity("quantity");
        goodwillApplyFinalPartInfoDTO2.setSupportProportion("quantity");
        goodwillApplyInfoDTO.setAccessoryCostTable(Arrays.asList(goodwillApplyFinalPartInfoDTO2));
        goodwillApplyInfoDTO.setCustomerBackground("customerBackground");
        goodwillApplyInfoDTO.setCustomerBackgroundEn("customerBackgroundEn");
        goodwillApplyInfoDTO.setReasonAndDispose("reasonAndDispose");
        goodwillApplyInfoDTO.setReasonAndDisposeEn("reasonAndDisposeEn");
        goodwillApplyInfoDTO.setRepairSolution("repairSolution");
        goodwillApplyInfoDTO.setRepairSolutionEn("repairSolutionEn");
        goodwillApplyInfoDTO.setCustomerRequire("customerRequire");
        goodwillApplyInfoDTO.setCustomerRequireEn("customerRequireEn");
        goodwillApplyInfoDTO.setPotentialRisk("potentialRisk");
        goodwillApplyInfoDTO.setPotentialRiskEn("potentialRiskEn");
        goodwillApplyInfoDTO.setVrOrTjNoEn("vrOrTjNoEn");
        goodwillApplyInfoDTO.setVrOrTjNo("vrOrTjNo");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetailEn("businessGoodwillApplyDetailEn");
        goodwillApplyInfoDTO.setBusinessGoodwillApplyDetail("businessGoodwillApplyDetail");
        final GoodwillApplyRepairInfoDTO goodwillApplyRepairInfoDTO = new GoodwillApplyRepairInfoDTO();
        goodwillApplyRepairInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairInfoDTO(Arrays.asList(goodwillApplyRepairInfoDTO));
        final GoodwillApplyRepairPartInfoDTO goodwillApplyRepairPartInfoDTO = new GoodwillApplyRepairPartInfoDTO();
        goodwillApplyRepairPartInfoDTO.setGoodwillApplyId(0L);
        goodwillApplyInfoDTO.setGoodwillApplyRepairPartInfoDTO(Arrays.asList(goodwillApplyRepairPartInfoDTO));
        goodwillApplyInfoDTO.setId(0L);
        goodwillApplyInfoDTO.setApplyNo("applyNo");
        goodwillApplyInfoDTO.setDealerCode("dealerCode");
        goodwillApplyInfoDTO.setAuditType(0);
        goodwillApplyInfoDTO.setApplyAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setGoodwillStatus(0);
        goodwillApplyInfoDTO.setCustomerPainVue(Arrays.asList(0));
        goodwillApplyInfoDTO.setCustomerPain("customerPain");
        goodwillApplyInfoDTO.setVoucherCost(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setWalkingCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setVolvoIntegral(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setReturnChangeCarPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setOtherPrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setMaterialId(0L);
        goodwillApplyInfoDTO.setRoleList(new String[]{"roleList"});
        goodwillApplyInfoDTO.setRoleList2(new String[]{"roleList2"});
        goodwillApplyInfoDTO.setRoleList1("roleList1");

        when(mockGoodwillApplyInfoMapper.exportSupportApplyAuditInfo(any(GoodwillApplyInfoDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillApplyInfoDTO> result = goodwillApplyInfoServiceImplUnderTest.exportSupportApplyAuditInfo(
                goodwillApplyInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetGoodwillFirstPage() throws Exception {
        // Setup
        final GoodwillFirstPageDTO goodwillFirstPageDTO = new GoodwillFirstPageDTO();
        goodwillFirstPageDTO.setDealerCode("dealerCode");
        goodwillFirstPageDTO.setSmallAreaManage(0);
        goodwillFirstPageDTO.setBigAreaManage(0);
        goodwillFirstPageDTO.setRoleList(new String[]{"roleList"});

        when(mockGoodwillApplyInfoMapper.getGoodwillFirstPage(any(GoodwillFirstPageDTO.class)))
                .thenReturn(new HashMap<>());

        // Run the test
        final Map result = goodwillApplyInfoServiceImplUnderTest.getGoodwillFirstPage(goodwillFirstPageDTO);

        // Verify the results
    }

}
