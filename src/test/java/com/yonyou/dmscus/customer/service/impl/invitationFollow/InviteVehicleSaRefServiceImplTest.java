package com.yonyou.dmscus.customer.service.impl.invitationFollow;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleSaRefMapper;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleSaRefDTO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleSaRefPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteVehicleSaRefServiceImplTest {

    @Mock
    private InviteVehicleSaRefMapper mockInviteVehicleSaRefMapper;

    private InviteVehicleSaRefServiceImpl inviteVehicleSaRefServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        inviteVehicleSaRefServiceImplUnderTest = new InviteVehicleSaRefServiceImpl();
        inviteVehicleSaRefServiceImplUnderTest.inviteVehicleSaRefMapper = mockInviteVehicleSaRefMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final InviteVehicleSaRefDTO inviteVehicleSaRefDTO = new InviteVehicleSaRefDTO();
        inviteVehicleSaRefDTO.setAppId("appId");
        inviteVehicleSaRefDTO.setOwnerCode("ownerCode");
        inviteVehicleSaRefDTO.setOwnerParCode("ownerParCode");
        inviteVehicleSaRefDTO.setOrgId(0);
        inviteVehicleSaRefDTO.setId(0L);

        // Configure InviteVehicleSaRefMapper.selectPageBySql(...).
        final InviteVehicleSaRefPO inviteVehicleSaRefPO = new InviteVehicleSaRefPO();
        inviteVehicleSaRefPO.setOwnerCode("ownerCode");
        inviteVehicleSaRefPO.setOrgId(0);
        inviteVehicleSaRefPO.setId(0L);
        inviteVehicleSaRefPO.setVin("vin");
        inviteVehicleSaRefPO.setDealerCode("dealerCode");
        final List<InviteVehicleSaRefPO> inviteVehicleSaRefPOS = Arrays.asList(inviteVehicleSaRefPO);
        when(mockInviteVehicleSaRefMapper.selectPageBySql(any(Page.class), any(InviteVehicleSaRefPO.class)))
                .thenReturn(inviteVehicleSaRefPOS);

        // Run the test
        final IPage<InviteVehicleSaRefDTO> result = inviteVehicleSaRefServiceImplUnderTest.selectPageBysql(page,
                inviteVehicleSaRefDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_InviteVehicleSaRefMapperReturnsNoItems() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final InviteVehicleSaRefDTO inviteVehicleSaRefDTO = new InviteVehicleSaRefDTO();
        inviteVehicleSaRefDTO.setAppId("appId");
        inviteVehicleSaRefDTO.setOwnerCode("ownerCode");
        inviteVehicleSaRefDTO.setOwnerParCode("ownerParCode");
        inviteVehicleSaRefDTO.setOrgId(0);
        inviteVehicleSaRefDTO.setId(0L);

        when(mockInviteVehicleSaRefMapper.selectPageBySql(any(Page.class), any(InviteVehicleSaRefPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final IPage<InviteVehicleSaRefDTO> result = inviteVehicleSaRefServiceImplUnderTest.selectPageBysql(page,
                inviteVehicleSaRefDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final InviteVehicleSaRefDTO inviteVehicleSaRefDTO = new InviteVehicleSaRefDTO();
        inviteVehicleSaRefDTO.setAppId("appId");
        inviteVehicleSaRefDTO.setOwnerCode("ownerCode");
        inviteVehicleSaRefDTO.setOwnerParCode("ownerParCode");
        inviteVehicleSaRefDTO.setOrgId(0);
        inviteVehicleSaRefDTO.setId(0L);

        // Configure InviteVehicleSaRefMapper.selectListBySql(...).
        final InviteVehicleSaRefPO inviteVehicleSaRefPO = new InviteVehicleSaRefPO();
        inviteVehicleSaRefPO.setOwnerCode("ownerCode");
        inviteVehicleSaRefPO.setOrgId(0);
        inviteVehicleSaRefPO.setId(0L);
        inviteVehicleSaRefPO.setVin("vin");
        inviteVehicleSaRefPO.setDealerCode("dealerCode");
        final List<InviteVehicleSaRefPO> inviteVehicleSaRefPOS = Arrays.asList(inviteVehicleSaRefPO);
        when(mockInviteVehicleSaRefMapper.selectListBySql(any(InviteVehicleSaRefPO.class)))
                .thenReturn(inviteVehicleSaRefPOS);

        // Run the test
        final List<InviteVehicleSaRefDTO> result = inviteVehicleSaRefServiceImplUnderTest.selectListBySql(
                inviteVehicleSaRefDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_InviteVehicleSaRefMapperReturnsNoItems() throws Exception {
        // Setup
        final InviteVehicleSaRefDTO inviteVehicleSaRefDTO = new InviteVehicleSaRefDTO();
        inviteVehicleSaRefDTO.setAppId("appId");
        inviteVehicleSaRefDTO.setOwnerCode("ownerCode");
        inviteVehicleSaRefDTO.setOwnerParCode("ownerParCode");
        inviteVehicleSaRefDTO.setOrgId(0);
        inviteVehicleSaRefDTO.setId(0L);

        when(mockInviteVehicleSaRefMapper.selectListBySql(any(InviteVehicleSaRefPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteVehicleSaRefDTO> result = inviteVehicleSaRefServiceImplUnderTest.selectListBySql(
                inviteVehicleSaRefDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure InviteVehicleSaRefMapper.selectById(...).
        final InviteVehicleSaRefPO inviteVehicleSaRefPO = new InviteVehicleSaRefPO();
        inviteVehicleSaRefPO.setOwnerCode("ownerCode");
        inviteVehicleSaRefPO.setOrgId(0);
        inviteVehicleSaRefPO.setId(0L);
        inviteVehicleSaRefPO.setVin("vin");
        inviteVehicleSaRefPO.setDealerCode("dealerCode");
        when(mockInviteVehicleSaRefMapper.selectById(0L)).thenReturn(inviteVehicleSaRefPO);

        // Run the test
        final InviteVehicleSaRefDTO result = inviteVehicleSaRefServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_InviteVehicleSaRefMapperReturnsNull() throws Exception {
        // Setup
        when(mockInviteVehicleSaRefMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inviteVehicleSaRefServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final InviteVehicleSaRefDTO inviteVehicleSaRefDTO = new InviteVehicleSaRefDTO();
        inviteVehicleSaRefDTO.setAppId("appId");
        inviteVehicleSaRefDTO.setOwnerCode("ownerCode");
        inviteVehicleSaRefDTO.setOwnerParCode("ownerParCode");
        inviteVehicleSaRefDTO.setOrgId(0);
        inviteVehicleSaRefDTO.setId(0L);

        when(mockInviteVehicleSaRefMapper.insert(any(InviteVehicleSaRefPO.class))).thenReturn(0);

        // Run the test
        final int result = inviteVehicleSaRefServiceImplUnderTest.insert(inviteVehicleSaRefDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final InviteVehicleSaRefDTO inviteVehicleSaRefDTO = new InviteVehicleSaRefDTO();
        inviteVehicleSaRefDTO.setAppId("appId");
        inviteVehicleSaRefDTO.setOwnerCode("ownerCode");
        inviteVehicleSaRefDTO.setOwnerParCode("ownerParCode");
        inviteVehicleSaRefDTO.setOrgId(0);
        inviteVehicleSaRefDTO.setId(0L);

        // Configure InviteVehicleSaRefMapper.selectById(...).
        final InviteVehicleSaRefPO inviteVehicleSaRefPO = new InviteVehicleSaRefPO();
        inviteVehicleSaRefPO.setOwnerCode("ownerCode");
        inviteVehicleSaRefPO.setOrgId(0);
        inviteVehicleSaRefPO.setId(0L);
        inviteVehicleSaRefPO.setVin("vin");
        inviteVehicleSaRefPO.setDealerCode("dealerCode");
        when(mockInviteVehicleSaRefMapper.selectById(0L)).thenReturn(inviteVehicleSaRefPO);

        when(mockInviteVehicleSaRefMapper.updateById(any(InviteVehicleSaRefPO.class))).thenReturn(0);

        // Run the test
        final int result = inviteVehicleSaRefServiceImplUnderTest.update(0L, inviteVehicleSaRefDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
