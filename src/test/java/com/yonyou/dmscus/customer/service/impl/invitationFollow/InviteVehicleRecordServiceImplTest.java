package com.yonyou.dmscus.customer.service.impl.invitationFollow;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.MoreExecutors;
import com.yonyou.dmscloud.framework.domain.LoginInfoDto;
import com.yonyou.dmscloud.framework.util.FrameworkUtil;
import com.yonyou.dmscloud.framework.util.redis.RedisClient;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.busSetting.SetMainFileMapper;
import com.yonyou.dmscus.customer.dao.common.VehicleMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleSaRefMapper;
import com.yonyou.dmscus.customer.dao.invitationautocreate.InviteVehicleTaskMapper;
import com.yonyou.dmscus.customer.dao.inviteRule.InvitePartItemRuleMapper;
import com.yonyou.dmscus.customer.dao.voc.CdpTagTaskMapper;
import com.yonyou.dmscus.customer.dao.voc.VocInviteVehicleTaskRecordMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.CallDetailsMapper;
import com.yonyou.dmscus.customer.dto.*;
import com.yonyou.dmscus.customer.dto.cdp.CdpTagTaskParameterDto;
import com.yonyou.dmscus.customer.entity.dto.busSetting.SetMainFileDetailVO;
import com.yonyou.dmscus.customer.entity.dto.busSetting.SetMainFileVO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.*;
import com.yonyou.dmscus.customer.entity.dto.clue.*;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightTopicDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.RecommendationDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InsuranceBillDTO;
import com.yonyou.dmscus.customer.entity.dto.saSllocateDlr.SaSllocateDlrDto;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillDTO;
import com.yonyou.dmscus.customer.entity.po.common.VehiclePO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.ClueCompleteLogPo;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleSaRefPO;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InvitePartItemRulePO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocInviteVehicleTaskRecordPo;
import com.yonyou.dmscus.customer.entity.po.voicemanage.CallDetailsPO;
import com.yonyou.dmscus.customer.entity.vo.InviteVehicleRecordVo;
import com.yonyou.dmscus.customer.feign.*;
import com.yonyou.dmscus.customer.feign.dto.CommonConfigDTO;
import com.yonyou.dmscus.customer.feign.vo.ClueParamVO;
import com.yonyou.dmscus.customer.feign.vo.OwnerInfoResultsVo;
import com.yonyou.dmscus.customer.service.ExternalAPIService;
import com.yonyou.dmscus.customer.service.cdpTagTask.qb.CdpTagTaskService;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.common.businessPlatform.dto.VehicleOwnerDetailByIdListDTO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.dto.VehicleOwnerSelectByIdListDTO;
import com.yonyou.dmscus.customer.service.common.ownervehicle.VehicleService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordDetailService;
import com.yonyou.dmscus.customer.service.invitationautocreate.ClueCompleteLogService;
import com.yonyou.dmscus.customer.service.talkskill.TalkskillService;
import com.yonyou.dmscus.customer.service.vocfunctionalstatus.VocInviteVehicleTaskRecordService;
import org.apache.poi.ss.formula.functions.T;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteVehicleRecordServiceImplTest {

    @Mock
    private InviteVehicleRecordMapper mockInviteVehicleRecordMapper;
    @Mock
    private SetMainFileMapper mockSetMainFileMapper;
    @Mock
    private InviteVehicleTaskMapper mockInviteVehicleTaskMapper;
    @Mock
    private InvitePartItemRuleMapper mockInvitePartItemRuleMapper;
    @Mock
    private VehicleMapper mockVehicleMapper;
    @Mock
    private CallDetailsMapper mockCallDetailsMapper;
    @Mock
    private InviteVehicleSaRefMapper mockInviteVehicleSaRefMapper;
    @Mock
    private VocInviteVehicleTaskRecordMapper mockVocInviteVehicleTaskRecordMapper;
    @Mock
    private TalkskillService mockTalkskillService;
    @Mock
    private VehicleService mockVehicleService;
    @Mock
    private InviteVehicleRecordDetailService mockInviteVehicleRecordDetailService;
    @Mock
    private BusinessPlatformService mockBusinessPlatformService;
    @Mock
    private VocInviteVehicleTaskRecordService mockTaskRecordService;
    @Mock
    private RepairCommonClient mockRepairCommonClient;
    @Mock
    private ReportCommonClient mockReportCommonClient;
    @Mock
    private WhitelistQueryService mockWhitelistQueryService;
    @Mock
    private MidEndCustomerCenterClient mockMidEndCustomerCenterClient;
    @Mock
    private ClueCompleteLogService mockClueCompleteLogService;
    @Mock
    private ApplicationEventPublisher mockEventPublisher;
    @Mock
    private ExternalAPIService mockExternalAPIService;
    @Mock
    private RedisClient mockRedisClient;
    @Mock
    private CdpTagTaskService mockCdpTagTaskService;
    @Mock
    private CdpTagTaskMapper mockCdpTagTaskMapper;
    @Mock
    private DmscloudServiceClient mockDmscloudServiceClient;


    @InjectMocks
    private InviteVehicleRecordServiceImpl inviteVehicleRecordServiceImplUnderTest;

    MockedStatic<FrameworkUtil> loginInfoUtil;

    @BeforeEach
    void testBefore() {
        loginInfoUtil = Mockito.mockStatic(FrameworkUtil.class);
        LoginInfoDto loginInfo = new LoginInfoDto();
        loginInfo.setOwnerCode("SHJ");
        loginInfo.setUserId(111L);
        loginInfo.setOrgId(111L);
        loginInfo.setAppId("volvo");
        loginInfo.setOrgCode("code");
        loginInfoUtil.when(FrameworkUtil::getLoginInfo).thenReturn(loginInfo);
    }
    @AfterEach
    void testAfter(){
        loginInfoUtil.close();
    }

    @Test
    void testGetInviteVehicleRecord() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final InviteVehicleRecordDTO inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        inviteVehicleRecordDTO.setOwnerCode("dealerCode");
        inviteVehicleRecordDTO.setId(0L);
        inviteVehicleRecordDTO.setIsMain(0);
        inviteVehicleRecordDTO.setSourceType(0);
        inviteVehicleRecordDTO.setVin("vin");
        inviteVehicleRecordDTO.setModel("carModelName");
        inviteVehicleRecordDTO.setLicensePlateNum("licencePlate");
        inviteVehicleRecordDTO.setName("name");
        inviteVehicleRecordDTO.setTel("mobile");
        inviteVehicleRecordDTO.setSex("sex");
        inviteVehicleRecordDTO.setInviteType(0);
        inviteVehicleRecordDTO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleRecordDTO.setSaId("lastSaId");
        inviteVehicleRecordDTO.setSaName("username");
        inviteVehicleRecordDTO.setLastSaId("lastSaId");
        inviteVehicleRecordDTO.setLastSaName("username");
        inviteVehicleRecordDTO.setFollowStatus(0);
        inviteVehicleRecordDTO.setOrderStatus(0);
        inviteVehicleRecordDTO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleRecordDTO.setOutMileage(0.0);
        inviteVehicleRecordDTO.setDealerCode("dealerCode");
        inviteVehicleRecordDTO.setLastInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleRecordDTO.setIsself(1);
        inviteVehicleRecordDTO.setIsNoDistribute(true);
        inviteVehicleRecordDTO.setIsWaitDistribute(false);
        inviteVehicleRecordDTO.setItemCode("code");
        inviteVehicleRecordDTO.setItemName("name");
        inviteVehicleRecordDTO.setItemType(0);
        inviteVehicleRecordDTO.setLastChangeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleRecordDTO.setLastMaintenanceDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final TalkskillDTO talkskillDTO = new TalkskillDTO();
        talkskillDTO.setTalkskill("talkskill");
        inviteVehicleRecordDTO.setTalkskill(Arrays.asList(talkskillDTO));
        final SetMainFileVO setMainFileVO = new SetMainFileVO();
        setMainFileVO.setId(0L);
        setMainFileVO.setSetName("setName");
        final TalkskillDTO talkskillDTO1 = new TalkskillDTO();
        talkskillDTO1.setTalkskill("talkskill");
        setMainFileVO.setTalkskill(Arrays.asList(talkskillDTO1));
        final SetMainFileDetailVO setMainFileDetailVO = new SetMainFileDetailVO();
        setMainFileVO.setMainFileDetail(Arrays.asList(setMainFileDetailVO));
        inviteVehicleRecordDTO.setMaintainList(Arrays.asList(setMainFileVO));
        inviteVehicleRecordDTO.setIsInsureSuccess(0);
        inviteVehicleRecordDTO.setAdviseInMileage(0);
        inviteVehicleRecordDTO.setExtensionInsurance("extensionInsurance");
        inviteVehicleRecordDTO.setLastMaintenanceMileage(new BigDecimal("0.00"));
        inviteVehicleRecordDTO.setLastMaintenanceDealer("lastMaintenanceDealer");
        inviteVehicleRecordDTO.setLossType(0);
        inviteVehicleRecordDTO.setLossWarningType(0);
        inviteVehicleRecordDTO.setDailyMile("dailyMileString");
        inviteVehicleRecordDTO.setBevFlag(0);
        inviteVehicleRecordDTO.setPowerType("powerType");
        inviteVehicleRecordDTO.setReturnIntentionLevel(0);
        inviteVehicleRecordDTO.setAllocation(false);
        List<Integer> inviteTypeParam = Lists.newArrayList(82381013);
        inviteVehicleRecordDTO.setInviteTypeParam(inviteTypeParam);
        // Configure InviteVehicleRecordMapper.selectInviteVehicleRecord(...).
        final InviteVehicleRecordDTO inviteVehicleRecordDTO1 = new InviteVehicleRecordDTO();
        inviteVehicleRecordDTO1.setOwnerCode("dealerCode");
        inviteVehicleRecordDTO1.setIsMain(0);
        inviteVehicleRecordDTO1.setSourceType(0);
        inviteVehicleRecordDTO1.setVin("vin");
        inviteVehicleRecordDTO1.setModel("carModelName");
        inviteVehicleRecordDTO1.setLicensePlateNum("licencePlate");
        inviteVehicleRecordDTO1.setName("name");
        inviteVehicleRecordDTO1.setTel("mobile");
        inviteVehicleRecordDTO1.setSex("sex");
        inviteVehicleRecordDTO1.setInviteType(0);
        inviteVehicleRecordDTO1.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleRecordDTO1.setSaId("lastSaId");
        inviteVehicleRecordDTO1.setSaName("username");
        inviteVehicleRecordDTO1.setLastSaId("lastSaId");
        inviteVehicleRecordDTO1.setLastSaName("username");
        inviteVehicleRecordDTO1.setFollowStatus(0);
        inviteVehicleRecordDTO1.setOrderStatus(0);
        inviteVehicleRecordDTO1.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleRecordDTO1.setOutMileage(0.0);
        inviteVehicleRecordDTO1.setDealerCode("dealerCode");
        inviteVehicleRecordDTO1.setLastInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleRecordDTO1.setIsself(0);
        inviteVehicleRecordDTO1.setIsNoDistribute(false);
        inviteVehicleRecordDTO1.setIsWaitDistribute(false);
        inviteVehicleRecordDTO1.setItemCode("code");
        inviteVehicleRecordDTO1.setItemName("name");
        inviteVehicleRecordDTO1.setItemType(0);
        inviteVehicleRecordDTO1.setLastChangeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleRecordDTO1.setLastMaintenanceDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final TalkskillDTO talkskillDTO2 = new TalkskillDTO();
        talkskillDTO2.setTalkskill("talkskill");
        inviteVehicleRecordDTO1.setTalkskill(Arrays.asList(talkskillDTO2));
        final SetMainFileVO setMainFileVO1 = new SetMainFileVO();
        setMainFileVO1.setId(0L);
        setMainFileVO1.setSetName("setName");
        final TalkskillDTO talkskillDTO3 = new TalkskillDTO();
        talkskillDTO3.setTalkskill("talkskill");
        setMainFileVO1.setTalkskill(Arrays.asList(talkskillDTO3));
        final SetMainFileDetailVO setMainFileDetailVO1 = new SetMainFileDetailVO();
        setMainFileVO1.setMainFileDetail(Arrays.asList(setMainFileDetailVO1));
        inviteVehicleRecordDTO1.setMaintainList(Arrays.asList(setMainFileVO1));
        inviteVehicleRecordDTO1.setIsInsureSuccess(0);
        inviteVehicleRecordDTO1.setAdviseInMileage(0);
        inviteVehicleRecordDTO1.setExtensionInsurance("extensionInsurance");
        inviteVehicleRecordDTO1.setLastMaintenanceMileage(new BigDecimal("0.00"));
        inviteVehicleRecordDTO1.setLastMaintenanceDealer("lastMaintenanceDealer");
        inviteVehicleRecordDTO1.setRecordType(0);
        inviteVehicleRecordDTO1.setLossType(0);
        inviteVehicleRecordDTO1.setLossWarningType(0);
        inviteVehicleRecordDTO1.setDailyMile("dailyMileString");
        inviteVehicleRecordDTO1.setBevFlag(0);
        inviteVehicleRecordDTO1.setPowerType("powerType");
        inviteVehicleRecordDTO1.setReturnIntentionLevel(0);
        inviteVehicleRecordDTO1.setAllocation(false);

        final List<InviteVehicleRecordDTO> inviteVehicleRecordDTOS = Arrays.asList(inviteVehicleRecordDTO1);
        when(mockInviteVehicleRecordMapper.selectInviteVehicleRecord(any(Page.class), any()))
                .thenReturn(inviteVehicleRecordDTOS);


           // Run the test
           final IPage<InviteVehicleRecordDTO> result = inviteVehicleRecordServiceImplUnderTest.getInviteVehicleRecord(
                   page, inviteVehicleRecordDTO);


        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    void testGetInviteVehicleRecord_inviteVehicleRecordDTOIsNull() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final InviteVehicleRecordDTO inviteVehicleRecordDTO = new InviteVehicleRecordDTO();
        inviteVehicleRecordDTO.setOwnerCode("dealerCode");
        inviteVehicleRecordDTO.setId(0L);
        inviteVehicleRecordDTO.setIsMain(0);
        inviteVehicleRecordDTO.setSourceType(0);
        inviteVehicleRecordDTO.setVin("vin");
        inviteVehicleRecordDTO.setModel("carModelName");
        inviteVehicleRecordDTO.setLicensePlateNum("licencePlate");
        inviteVehicleRecordDTO.setName("name");
        inviteVehicleRecordDTO.setTel("mobile");
        inviteVehicleRecordDTO.setSex("sex");
        inviteVehicleRecordDTO.setInviteType(0);
        inviteVehicleRecordDTO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleRecordDTO.setSaId("lastSaId");
        inviteVehicleRecordDTO.setSaName("username");
        inviteVehicleRecordDTO.setLastSaId("lastSaId");
        inviteVehicleRecordDTO.setLastSaName("username");
        inviteVehicleRecordDTO.setFollowStatus(0);
        inviteVehicleRecordDTO.setOrderStatus(0);
        inviteVehicleRecordDTO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleRecordDTO.setOutMileage(0.0);
        inviteVehicleRecordDTO.setDealerCode("dealerCode");
        inviteVehicleRecordDTO.setLastInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleRecordDTO.setIsself(0);
        inviteVehicleRecordDTO.setIsNoDistribute(false);
        inviteVehicleRecordDTO.setIsWaitDistribute(false);
        inviteVehicleRecordDTO.setItemCode("code");
        inviteVehicleRecordDTO.setItemName("name");
        inviteVehicleRecordDTO.setItemType(0);
        inviteVehicleRecordDTO.setLastChangeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleRecordDTO.setLastMaintenanceDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final TalkskillDTO talkskillDTO = new TalkskillDTO();
        talkskillDTO.setTalkskill("talkskill");
        inviteVehicleRecordDTO.setTalkskill(Arrays.asList(talkskillDTO));
        final SetMainFileVO setMainFileVO = new SetMainFileVO();
        setMainFileVO.setId(0L);
        setMainFileVO.setSetName("setName");
        final TalkskillDTO talkskillDTO1 = new TalkskillDTO();
        talkskillDTO1.setTalkskill("talkskill");
        setMainFileVO.setTalkskill(Arrays.asList(talkskillDTO1));
        final SetMainFileDetailVO setMainFileDetailVO = new SetMainFileDetailVO();
        setMainFileVO.setMainFileDetail(Arrays.asList(setMainFileDetailVO));
        inviteVehicleRecordDTO.setMaintainList(Arrays.asList(setMainFileVO));
        inviteVehicleRecordDTO.setIsInsureSuccess(0);
        inviteVehicleRecordDTO.setAdviseInMileage(0);
        inviteVehicleRecordDTO.setExtensionInsurance("extensionInsurance");
        inviteVehicleRecordDTO.setLastMaintenanceMileage(new BigDecimal("0.00"));
        inviteVehicleRecordDTO.setLastMaintenanceDealer("lastMaintenanceDealer");
        inviteVehicleRecordDTO.setLossType(0);
        inviteVehicleRecordDTO.setLossWarningType(0);
        inviteVehicleRecordDTO.setDailyMile("dailyMileString");
        inviteVehicleRecordDTO.setBevFlag(0);
        inviteVehicleRecordDTO.setPowerType("powerType");
        inviteVehicleRecordDTO.setReturnIntentionLevel(0);
        inviteVehicleRecordDTO.setAllocation(false);
        when(mockInviteVehicleRecordMapper.selectInviteVehicleRecord(any(Page.class), any()))
                .thenReturn(null);

        // Run the test
        final IPage<InviteVehicleRecordDTO> result = inviteVehicleRecordServiceImplUnderTest.getInviteVehicleRecord(
                page, null);


        // Verify the results
        assertThat(result).isNotNull();
    }


}
