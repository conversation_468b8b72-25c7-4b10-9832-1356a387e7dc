package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintAttachmentMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintAttachmentDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintAttachmentPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SaleComplaintAttachmentServiceImplTest {

    @Mock
    private SaleComplaintAttachmentMapper mockSaleComplaintAttachmentMapper;

    private SaleComplaintAttachmentServiceImpl saleComplaintAttachmentServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        saleComplaintAttachmentServiceImplUnderTest = new SaleComplaintAttachmentServiceImpl();
        saleComplaintAttachmentServiceImplUnderTest.saleComplaintAttachmentMapper = mockSaleComplaintAttachmentMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintAttachmentDTO saleComplaintAttachmentDTO = new SaleComplaintAttachmentDTO();
        saleComplaintAttachmentDTO.setAppId("appId");
        saleComplaintAttachmentDTO.setOwnerCode("ownerCode");
        saleComplaintAttachmentDTO.setOwnerParCode("ownerParCode");
        saleComplaintAttachmentDTO.setOrgId(0);
        saleComplaintAttachmentDTO.setId(0L);

        // Configure SaleComplaintAttachmentMapper.selectPageBySql(...).
        final SaleComplaintAttachmentPO saleComplaintAttachmentPO = new SaleComplaintAttachmentPO();
        saleComplaintAttachmentPO.setAppId("appId");
        saleComplaintAttachmentPO.setOwnerCode("ownerCode");
        saleComplaintAttachmentPO.setOwnerParCode("ownerParCode");
        saleComplaintAttachmentPO.setOrgId(0);
        saleComplaintAttachmentPO.setId(0L);
        final List<SaleComplaintAttachmentPO> saleComplaintAttachmentPOS = Arrays.asList(saleComplaintAttachmentPO);
        when(mockSaleComplaintAttachmentMapper.selectPageBySql(any(Page.class),
                any(SaleComplaintAttachmentPO.class))).thenReturn(saleComplaintAttachmentPOS);

        // Run the test
        final IPage<SaleComplaintAttachmentDTO> result = saleComplaintAttachmentServiceImplUnderTest.selectPageBysql(
                page, saleComplaintAttachmentDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_SaleComplaintAttachmentMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintAttachmentDTO saleComplaintAttachmentDTO = new SaleComplaintAttachmentDTO();
        saleComplaintAttachmentDTO.setAppId("appId");
        saleComplaintAttachmentDTO.setOwnerCode("ownerCode");
        saleComplaintAttachmentDTO.setOwnerParCode("ownerParCode");
        saleComplaintAttachmentDTO.setOrgId(0);
        saleComplaintAttachmentDTO.setId(0L);

        when(mockSaleComplaintAttachmentMapper.selectPageBySql(any(Page.class),
                any(SaleComplaintAttachmentPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<SaleComplaintAttachmentDTO> result = saleComplaintAttachmentServiceImplUnderTest.selectPageBysql(
                page, saleComplaintAttachmentDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final SaleComplaintAttachmentDTO saleComplaintAttachmentDTO = new SaleComplaintAttachmentDTO();
        saleComplaintAttachmentDTO.setAppId("appId");
        saleComplaintAttachmentDTO.setOwnerCode("ownerCode");
        saleComplaintAttachmentDTO.setOwnerParCode("ownerParCode");
        saleComplaintAttachmentDTO.setOrgId(0);
        saleComplaintAttachmentDTO.setId(0L);

        // Configure SaleComplaintAttachmentMapper.selectListBySql(...).
        final SaleComplaintAttachmentPO saleComplaintAttachmentPO = new SaleComplaintAttachmentPO();
        saleComplaintAttachmentPO.setAppId("appId");
        saleComplaintAttachmentPO.setOwnerCode("ownerCode");
        saleComplaintAttachmentPO.setOwnerParCode("ownerParCode");
        saleComplaintAttachmentPO.setOrgId(0);
        saleComplaintAttachmentPO.setId(0L);
        final List<SaleComplaintAttachmentPO> saleComplaintAttachmentPOS = Arrays.asList(saleComplaintAttachmentPO);
        when(mockSaleComplaintAttachmentMapper.selectListBySql(any(SaleComplaintAttachmentPO.class)))
                .thenReturn(saleComplaintAttachmentPOS);

        // Run the test
        final List<SaleComplaintAttachmentDTO> result = saleComplaintAttachmentServiceImplUnderTest.selectListBySql(
                saleComplaintAttachmentDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_SaleComplaintAttachmentMapperReturnsNoItems() {
        // Setup
        final SaleComplaintAttachmentDTO saleComplaintAttachmentDTO = new SaleComplaintAttachmentDTO();
        saleComplaintAttachmentDTO.setAppId("appId");
        saleComplaintAttachmentDTO.setOwnerCode("ownerCode");
        saleComplaintAttachmentDTO.setOwnerParCode("ownerParCode");
        saleComplaintAttachmentDTO.setOrgId(0);
        saleComplaintAttachmentDTO.setId(0L);

        when(mockSaleComplaintAttachmentMapper.selectListBySql(any(SaleComplaintAttachmentPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<SaleComplaintAttachmentDTO> result = saleComplaintAttachmentServiceImplUnderTest.selectListBySql(
                saleComplaintAttachmentDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure SaleComplaintAttachmentMapper.selectById(...).
        final SaleComplaintAttachmentPO saleComplaintAttachmentPO = new SaleComplaintAttachmentPO();
        saleComplaintAttachmentPO.setAppId("appId");
        saleComplaintAttachmentPO.setOwnerCode("ownerCode");
        saleComplaintAttachmentPO.setOwnerParCode("ownerParCode");
        saleComplaintAttachmentPO.setOrgId(0);
        saleComplaintAttachmentPO.setId(0L);
        when(mockSaleComplaintAttachmentMapper.selectById(0L)).thenReturn(saleComplaintAttachmentPO);

        // Run the test
        final SaleComplaintAttachmentDTO result = saleComplaintAttachmentServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_SaleComplaintAttachmentMapperReturnsNull() {
        // Setup
        when(mockSaleComplaintAttachmentMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> saleComplaintAttachmentServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final SaleComplaintAttachmentDTO saleComplaintAttachmentDTO = new SaleComplaintAttachmentDTO();
        saleComplaintAttachmentDTO.setAppId("appId");
        saleComplaintAttachmentDTO.setOwnerCode("ownerCode");
        saleComplaintAttachmentDTO.setOwnerParCode("ownerParCode");
        saleComplaintAttachmentDTO.setOrgId(0);
        saleComplaintAttachmentDTO.setId(0L);

        when(mockSaleComplaintAttachmentMapper.insert(any(SaleComplaintAttachmentPO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintAttachmentServiceImplUnderTest.insert(saleComplaintAttachmentDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final SaleComplaintAttachmentDTO saleComplaintAttachmentDTO = new SaleComplaintAttachmentDTO();
        saleComplaintAttachmentDTO.setAppId("appId");
        saleComplaintAttachmentDTO.setOwnerCode("ownerCode");
        saleComplaintAttachmentDTO.setOwnerParCode("ownerParCode");
        saleComplaintAttachmentDTO.setOrgId(0);
        saleComplaintAttachmentDTO.setId(0L);

        // Configure SaleComplaintAttachmentMapper.selectById(...).
        final SaleComplaintAttachmentPO saleComplaintAttachmentPO = new SaleComplaintAttachmentPO();
        saleComplaintAttachmentPO.setAppId("appId");
        saleComplaintAttachmentPO.setOwnerCode("ownerCode");
        saleComplaintAttachmentPO.setOwnerParCode("ownerParCode");
        saleComplaintAttachmentPO.setOrgId(0);
        saleComplaintAttachmentPO.setId(0L);
        when(mockSaleComplaintAttachmentMapper.selectById(0L)).thenReturn(saleComplaintAttachmentPO);

        when(mockSaleComplaintAttachmentMapper.updateById(any(SaleComplaintAttachmentPO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintAttachmentServiceImplUnderTest.update(0L, saleComplaintAttachmentDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectListBySql1() {
        // Setup
        final SaleComplaintAttachmentDTO saleComplaintAttachmentDTO = new SaleComplaintAttachmentDTO();
        saleComplaintAttachmentDTO.setAppId("appId");
        saleComplaintAttachmentDTO.setOwnerCode("ownerCode");
        saleComplaintAttachmentDTO.setOwnerParCode("ownerParCode");
        saleComplaintAttachmentDTO.setOrgId(0);
        saleComplaintAttachmentDTO.setId(0L);

        // Configure SaleComplaintAttachmentMapper.selectListBySql1(...).
        final ComplaintAttachmentTestDTO complaintAttachmentTestDTO = new ComplaintAttachmentTestDTO();
        complaintAttachmentTestDTO.setUserId("userId");
        complaintAttachmentTestDTO.setAppId("appId");
        complaintAttachmentTestDTO.setOwnerCode("ownerCode");
        complaintAttachmentTestDTO.setOwnerParCode("ownerParCode");
        complaintAttachmentTestDTO.setOrgId(0);
        final List<ComplaintAttachmentTestDTO> complaintAttachmentTestDTOS = Arrays.asList(complaintAttachmentTestDTO);
        when(mockSaleComplaintAttachmentMapper.selectListBySql1(any(SaleComplaintAttachmentDTO.class)))
                .thenReturn(complaintAttachmentTestDTOS);

        // Run the test
        final List<ComplaintAttachmentTestDTO> result = saleComplaintAttachmentServiceImplUnderTest.selectListBySql1(
                saleComplaintAttachmentDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql1_SaleComplaintAttachmentMapperReturnsNoItems() {
        // Setup
        final SaleComplaintAttachmentDTO saleComplaintAttachmentDTO = new SaleComplaintAttachmentDTO();
        saleComplaintAttachmentDTO.setAppId("appId");
        saleComplaintAttachmentDTO.setOwnerCode("ownerCode");
        saleComplaintAttachmentDTO.setOwnerParCode("ownerParCode");
        saleComplaintAttachmentDTO.setOrgId(0);
        saleComplaintAttachmentDTO.setId(0L);

        when(mockSaleComplaintAttachmentMapper.selectListBySql1(any(SaleComplaintAttachmentDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintAttachmentTestDTO> result = saleComplaintAttachmentServiceImplUnderTest.selectListBySql1(
                saleComplaintAttachmentDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
