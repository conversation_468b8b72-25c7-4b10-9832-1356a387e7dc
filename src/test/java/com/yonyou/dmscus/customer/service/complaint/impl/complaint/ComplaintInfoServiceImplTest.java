package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintAttachmentMapper;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintInfoExtendMapper;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintInfoMapper;
import com.yonyou.dmscus.customer.dto.GetModelNameDTO;
import com.yonyou.dmscus.customer.entity.dto.common.HolidayDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.*;
import com.yonyou.dmscus.customer.entity.dto.complaint.sendComplaintData.NewComplaintDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.v51dk.V51dkComplaintInfoSyncDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyInfoDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintClassificationPO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfMorePO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoExtendPO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintInfoPO;
import com.yonyou.dmscus.customer.feign.DmscloudServiceClient;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.feign.dto.CommonConfigDTO;
import com.yonyou.dmscus.customer.middleInterface.CompanyDetailByCodeDTO;
import com.yonyou.dmscus.customer.service.complaint.*;
import com.yonyou.dmscus.customer.service.impl.CommonServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintInfoServiceImplTest {

    @Mock
    private ComplaintInfoMapper mockComplaintInfoMapper;
    @Mock
    private ComplaintFollowService mockComplaintFollowService;
    @Mock
    private ComplaintCustomFieldUseService mockComplaintCustomFieldUseService;
    @Mock
    private ComplaintCustomTopUseService mockComplaintCustomTopUseService;
    @Mock
    private ComplaintAttachmentService mockComplaintAttachmentService;
    @Mock
    private ComplaintAttachmentMapper mockComplaintAttachmentMapper;
    @Mock
    private ComplaintDealerCcmRefServiceImpl mockComplaintDealerCcmRefService;
    @Mock
    private CommonServiceImpl mockCommonServiceImpl;
    @Mock
    private ComplaintCommonServiceImpl mockComplaintCommonServiceImpl;
    @Mock
    private ReportCommonClient mockReportCommonClient;
    @Mock
    private ComplaintInfoExtendMapper mockComplaintInfoExtendMapper;
    @Mock
    private MidUrlProperties mockMidUrlProperties;
    @Mock
    private RestTemplate mockDirectRestTemplate;
    @Mock
    private ComplaintClassificationService mockComplaintClassificationService;
    @Mock
    private DmscloudServiceClient mockDmscloudServiceClient;

    @InjectMocks
    private ComplaintInfoServiceImpl complaintInfoServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        ReflectionTestUtils.setField(complaintInfoServiceImplUnderTest, "baseUrl", "baseUrl");
        ReflectionTestUtils.setField(complaintInfoServiceImplUnderTest, "apiuid", "apiuid");
        ReflectionTestUtils.setField(complaintInfoServiceImplUnderTest, "apipwd", "apipwd");
        complaintInfoServiceImplUnderTest.complaintInfoMapper = mockComplaintInfoMapper;
        complaintInfoServiceImplUnderTest.complaintFollowService = mockComplaintFollowService;
        complaintInfoServiceImplUnderTest.complaintCustomFieldUseService = mockComplaintCustomFieldUseService;
        complaintInfoServiceImplUnderTest.complaintCustomTopUseService = mockComplaintCustomTopUseService;
        complaintInfoServiceImplUnderTest.complaintAttachmentService = mockComplaintAttachmentService;
        complaintInfoServiceImplUnderTest.complaintAttachmentMapper = mockComplaintAttachmentMapper;
        complaintInfoServiceImplUnderTest.complaintDealerCcmRefService = mockComplaintDealerCcmRefService;
        complaintInfoServiceImplUnderTest.commonServiceImpl = mockCommonServiceImpl;
        complaintInfoServiceImplUnderTest.complaintCommonServiceImpl = mockComplaintCommonServiceImpl;
        complaintInfoServiceImplUnderTest.reportCommonClient = mockReportCommonClient;
        complaintInfoServiceImplUnderTest.complaintInfoExtendMapper = mockComplaintInfoExtendMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintInfoDTO complaintInfoDTO = new ComplaintInfoDTO();
        complaintInfoDTO.setAfterSmallAreaName("afterSmallAreaName");
        complaintInfoDTO.setFirstRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setIsRestartRevisit(0);
        complaintInfoDTO.setFirstCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setProblemInfo("problemInfo");
        complaintInfoDTO.setCusRequirement("cusRequirement");
        complaintInfoDTO.setWorkOrderClassification(0);
        complaintInfoDTO.setWorkOrderNature(0);
        complaintInfoDTO.setIsSatisfied(0);
        complaintInfoDTO.setBlocId(0L);
        complaintInfoDTO.setBloc("bloc");
        complaintInfoDTO.setOwnerAddress("ownerAddress");
        complaintInfoDTO.setId(0L);
        complaintInfoDTO.setComplaintId("complaintId");
        complaintInfoDTO.setSex(0);
        complaintInfoDTO.setType(0);
        complaintInfoDTO.setSource(0);
        complaintInfoDTO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setPart("part");
        complaintInfoDTO.setSubdivisionPart("subdivisionPart");
        complaintInfoDTO.setCallName("callName");
        complaintInfoDTO.setCallTel("callTel");
        complaintInfoDTO.setName("name");
        complaintInfoDTO.setLicensePlateNum("licensePlateNum");
        complaintInfoDTO.setVin("vin");
        complaintInfoDTO.setBuyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setModel("model");
        complaintInfoDTO.setConfigName("configName");
        complaintInfoDTO.setWorkOrderStatus(0);
        complaintInfoDTO.setIsCloseCase(0);
        complaintInfoDTO.setModelYear("modelYear");
        complaintInfoDTO.setBuyDealerName("buyDealerName");
        complaintInfoDTO.setDealerName("buyDealerName");
        complaintInfoDTO.setDealerCode("CreaterOrg");
        complaintInfoDTO.setMileage(0);
        complaintInfoDTO.setSubject("subject");
        complaintInfoDTO.setReport(false);
        complaintInfoDTO.setProblem("problem");
        complaintInfoDTO.setCategory1("category1");
        complaintInfoDTO.setCategory2("category2");
        complaintInfoDTO.setCategory3("category3");
        complaintInfoDTO.setDepartment("department");
        complaintInfoDTO.setImportanceLevel(0);
        complaintInfoDTO.setDealerFisrtReplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setIsRevisit(0);
        complaintInfoDTO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setIsAgree(0);
        complaintInfoDTO.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setCloseCaseStatus(0);
        complaintInfoDTO.setBasicReason("basicReason");
        complaintInfoDTO.setIsRepaired(0);
        complaintInfoDTO.setTechMaintainPlan("techMaintainPlan");
        complaintInfoDTO.setRapportPlan("rapportPlan");
        complaintInfoDTO.setRisk("risk");
        complaintInfoDTO.setDataSources(0);
        complaintInfoDTO.setRegionalManagerComments("regionalManagerComments");
        complaintInfoDTO.setRegion("afterBigAreaName");
        complaintInfoDTO.setRegionId(0L);
        complaintInfoDTO.setRegionManager("buyRegionManager");
        complaintInfoDTO.setRegionManagerId(0L);
        complaintInfoDTO.setBuyRegion("buyRegion");
        complaintInfoDTO.setBuyRegionId(0L);
        complaintInfoDTO.setBuyRegionManager("buyRegionManager");
        complaintInfoDTO.setBuyRegionManagerId(0L);
        complaintInfoDTO.setBuyBloc("bloc");
        complaintInfoDTO.setBuyDealerCode("buyDealerCode");

        // Configure ComplaintInfoMapper.selectPageBySql(...).
        final ComplaintInfoPO complaintInfoPO = new ComplaintInfoPO();
        complaintInfoPO.setFirstRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setFirstCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setId(0L);
        complaintInfoPO.setComplaintId("complaintId");
        complaintInfoPO.setType(0);
        complaintInfoPO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setDealerFisrtReplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setDataSources(0);
        final List<ComplaintInfoPO> complaintInfoPOS = Arrays.asList(complaintInfoPO);
        when(mockComplaintInfoMapper.selectPageBySql(any(Page.class), any(ComplaintInfoPO.class)))
                .thenReturn(complaintInfoPOS);

        // Run the test
        final IPage<ComplaintInfoDTO> result = complaintInfoServiceImplUnderTest.selectPageBysql(page,
                complaintInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintInfoMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintInfoDTO complaintInfoDTO = new ComplaintInfoDTO();
        complaintInfoDTO.setAfterSmallAreaName("afterSmallAreaName");
        complaintInfoDTO.setFirstRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setIsRestartRevisit(0);
        complaintInfoDTO.setFirstCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setProblemInfo("problemInfo");
        complaintInfoDTO.setCusRequirement("cusRequirement");
        complaintInfoDTO.setWorkOrderClassification(0);
        complaintInfoDTO.setWorkOrderNature(0);
        complaintInfoDTO.setIsSatisfied(0);
        complaintInfoDTO.setBlocId(0L);
        complaintInfoDTO.setBloc("bloc");
        complaintInfoDTO.setOwnerAddress("ownerAddress");
        complaintInfoDTO.setId(0L);
        complaintInfoDTO.setComplaintId("complaintId");
        complaintInfoDTO.setSex(0);
        complaintInfoDTO.setType(0);
        complaintInfoDTO.setSource(0);
        complaintInfoDTO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setPart("part");
        complaintInfoDTO.setSubdivisionPart("subdivisionPart");
        complaintInfoDTO.setCallName("callName");
        complaintInfoDTO.setCallTel("callTel");
        complaintInfoDTO.setName("name");
        complaintInfoDTO.setLicensePlateNum("licensePlateNum");
        complaintInfoDTO.setVin("vin");
        complaintInfoDTO.setBuyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setModel("model");
        complaintInfoDTO.setConfigName("configName");
        complaintInfoDTO.setWorkOrderStatus(0);
        complaintInfoDTO.setIsCloseCase(0);
        complaintInfoDTO.setModelYear("modelYear");
        complaintInfoDTO.setBuyDealerName("buyDealerName");
        complaintInfoDTO.setDealerName("buyDealerName");
        complaintInfoDTO.setDealerCode("CreaterOrg");
        complaintInfoDTO.setMileage(0);
        complaintInfoDTO.setSubject("subject");
        complaintInfoDTO.setReport(false);
        complaintInfoDTO.setProblem("problem");
        complaintInfoDTO.setCategory1("category1");
        complaintInfoDTO.setCategory2("category2");
        complaintInfoDTO.setCategory3("category3");
        complaintInfoDTO.setDepartment("department");
        complaintInfoDTO.setImportanceLevel(0);
        complaintInfoDTO.setDealerFisrtReplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setIsRevisit(0);
        complaintInfoDTO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setIsAgree(0);
        complaintInfoDTO.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setCloseCaseStatus(0);
        complaintInfoDTO.setBasicReason("basicReason");
        complaintInfoDTO.setIsRepaired(0);
        complaintInfoDTO.setTechMaintainPlan("techMaintainPlan");
        complaintInfoDTO.setRapportPlan("rapportPlan");
        complaintInfoDTO.setRisk("risk");
        complaintInfoDTO.setDataSources(0);
        complaintInfoDTO.setRegionalManagerComments("regionalManagerComments");
        complaintInfoDTO.setRegion("afterBigAreaName");
        complaintInfoDTO.setRegionId(0L);
        complaintInfoDTO.setRegionManager("buyRegionManager");
        complaintInfoDTO.setRegionManagerId(0L);
        complaintInfoDTO.setBuyRegion("buyRegion");
        complaintInfoDTO.setBuyRegionId(0L);
        complaintInfoDTO.setBuyRegionManager("buyRegionManager");
        complaintInfoDTO.setBuyRegionManagerId(0L);
        complaintInfoDTO.setBuyBloc("bloc");
        complaintInfoDTO.setBuyDealerCode("buyDealerCode");

        when(mockComplaintInfoMapper.selectPageBySql(any(Page.class), any(ComplaintInfoPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintInfoDTO> result = complaintInfoServiceImplUnderTest.selectPageBysql(page,
                complaintInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final ComplaintInfoDTO complaintInfoDTO = new ComplaintInfoDTO();
        complaintInfoDTO.setAfterSmallAreaName("afterSmallAreaName");
        complaintInfoDTO.setFirstRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setIsRestartRevisit(0);
        complaintInfoDTO.setFirstCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setProblemInfo("problemInfo");
        complaintInfoDTO.setCusRequirement("cusRequirement");
        complaintInfoDTO.setWorkOrderClassification(0);
        complaintInfoDTO.setWorkOrderNature(0);
        complaintInfoDTO.setIsSatisfied(0);
        complaintInfoDTO.setBlocId(0L);
        complaintInfoDTO.setBloc("bloc");
        complaintInfoDTO.setOwnerAddress("ownerAddress");
        complaintInfoDTO.setId(0L);
        complaintInfoDTO.setComplaintId("complaintId");
        complaintInfoDTO.setSex(0);
        complaintInfoDTO.setType(0);
        complaintInfoDTO.setSource(0);
        complaintInfoDTO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setPart("part");
        complaintInfoDTO.setSubdivisionPart("subdivisionPart");
        complaintInfoDTO.setCallName("callName");
        complaintInfoDTO.setCallTel("callTel");
        complaintInfoDTO.setName("name");
        complaintInfoDTO.setLicensePlateNum("licensePlateNum");
        complaintInfoDTO.setVin("vin");
        complaintInfoDTO.setBuyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setModel("model");
        complaintInfoDTO.setConfigName("configName");
        complaintInfoDTO.setWorkOrderStatus(0);
        complaintInfoDTO.setIsCloseCase(0);
        complaintInfoDTO.setModelYear("modelYear");
        complaintInfoDTO.setBuyDealerName("buyDealerName");
        complaintInfoDTO.setDealerName("buyDealerName");
        complaintInfoDTO.setDealerCode("CreaterOrg");
        complaintInfoDTO.setMileage(0);
        complaintInfoDTO.setSubject("subject");
        complaintInfoDTO.setReport(false);
        complaintInfoDTO.setProblem("problem");
        complaintInfoDTO.setCategory1("category1");
        complaintInfoDTO.setCategory2("category2");
        complaintInfoDTO.setCategory3("category3");
        complaintInfoDTO.setDepartment("department");
        complaintInfoDTO.setImportanceLevel(0);
        complaintInfoDTO.setDealerFisrtReplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setIsRevisit(0);
        complaintInfoDTO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setIsAgree(0);
        complaintInfoDTO.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setCloseCaseStatus(0);
        complaintInfoDTO.setBasicReason("basicReason");
        complaintInfoDTO.setIsRepaired(0);
        complaintInfoDTO.setTechMaintainPlan("techMaintainPlan");
        complaintInfoDTO.setRapportPlan("rapportPlan");
        complaintInfoDTO.setRisk("risk");
        complaintInfoDTO.setDataSources(0);
        complaintInfoDTO.setRegionalManagerComments("regionalManagerComments");
        complaintInfoDTO.setRegion("afterBigAreaName");
        complaintInfoDTO.setRegionId(0L);
        complaintInfoDTO.setRegionManager("buyRegionManager");
        complaintInfoDTO.setRegionManagerId(0L);
        complaintInfoDTO.setBuyRegion("buyRegion");
        complaintInfoDTO.setBuyRegionId(0L);
        complaintInfoDTO.setBuyRegionManager("buyRegionManager");
        complaintInfoDTO.setBuyRegionManagerId(0L);
        complaintInfoDTO.setBuyBloc("bloc");
        complaintInfoDTO.setBuyDealerCode("buyDealerCode");

        // Configure ComplaintInfoMapper.selectListBySql(...).
        final ComplaintInfoPO complaintInfoPO = new ComplaintInfoPO();
        complaintInfoPO.setFirstRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setFirstCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setId(0L);
        complaintInfoPO.setComplaintId("complaintId");
        complaintInfoPO.setType(0);
        complaintInfoPO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setDealerFisrtReplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setDataSources(0);
        final List<ComplaintInfoPO> complaintInfoPOS = Arrays.asList(complaintInfoPO);
        when(mockComplaintInfoMapper.selectListBySql(any(ComplaintInfoPO.class))).thenReturn(complaintInfoPOS);

        // Run the test
        final List<ComplaintInfoDTO> result = complaintInfoServiceImplUnderTest.selectListBySql(complaintInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintInfoMapperReturnsNoItems() {
        // Setup
        final ComplaintInfoDTO complaintInfoDTO = new ComplaintInfoDTO();
        complaintInfoDTO.setAfterSmallAreaName("afterSmallAreaName");
        complaintInfoDTO.setFirstRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setIsRestartRevisit(0);
        complaintInfoDTO.setFirstCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setProblemInfo("problemInfo");
        complaintInfoDTO.setCusRequirement("cusRequirement");
        complaintInfoDTO.setWorkOrderClassification(0);
        complaintInfoDTO.setWorkOrderNature(0);
        complaintInfoDTO.setIsSatisfied(0);
        complaintInfoDTO.setBlocId(0L);
        complaintInfoDTO.setBloc("bloc");
        complaintInfoDTO.setOwnerAddress("ownerAddress");
        complaintInfoDTO.setId(0L);
        complaintInfoDTO.setComplaintId("complaintId");
        complaintInfoDTO.setSex(0);
        complaintInfoDTO.setType(0);
        complaintInfoDTO.setSource(0);
        complaintInfoDTO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setPart("part");
        complaintInfoDTO.setSubdivisionPart("subdivisionPart");
        complaintInfoDTO.setCallName("callName");
        complaintInfoDTO.setCallTel("callTel");
        complaintInfoDTO.setName("name");
        complaintInfoDTO.setLicensePlateNum("licensePlateNum");
        complaintInfoDTO.setVin("vin");
        complaintInfoDTO.setBuyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setModel("model");
        complaintInfoDTO.setConfigName("configName");
        complaintInfoDTO.setWorkOrderStatus(0);
        complaintInfoDTO.setIsCloseCase(0);
        complaintInfoDTO.setModelYear("modelYear");
        complaintInfoDTO.setBuyDealerName("buyDealerName");
        complaintInfoDTO.setDealerName("buyDealerName");
        complaintInfoDTO.setDealerCode("CreaterOrg");
        complaintInfoDTO.setMileage(0);
        complaintInfoDTO.setSubject("subject");
        complaintInfoDTO.setReport(false);
        complaintInfoDTO.setProblem("problem");
        complaintInfoDTO.setCategory1("category1");
        complaintInfoDTO.setCategory2("category2");
        complaintInfoDTO.setCategory3("category3");
        complaintInfoDTO.setDepartment("department");
        complaintInfoDTO.setImportanceLevel(0);
        complaintInfoDTO.setDealerFisrtReplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setIsRevisit(0);
        complaintInfoDTO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setIsAgree(0);
        complaintInfoDTO.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoDTO.setCloseCaseStatus(0);
        complaintInfoDTO.setBasicReason("basicReason");
        complaintInfoDTO.setIsRepaired(0);
        complaintInfoDTO.setTechMaintainPlan("techMaintainPlan");
        complaintInfoDTO.setRapportPlan("rapportPlan");
        complaintInfoDTO.setRisk("risk");
        complaintInfoDTO.setDataSources(0);
        complaintInfoDTO.setRegionalManagerComments("regionalManagerComments");
        complaintInfoDTO.setRegion("afterBigAreaName");
        complaintInfoDTO.setRegionId(0L);
        complaintInfoDTO.setRegionManager("buyRegionManager");
        complaintInfoDTO.setRegionManagerId(0L);
        complaintInfoDTO.setBuyRegion("buyRegion");
        complaintInfoDTO.setBuyRegionId(0L);
        complaintInfoDTO.setBuyRegionManager("buyRegionManager");
        complaintInfoDTO.setBuyRegionManagerId(0L);
        complaintInfoDTO.setBuyBloc("bloc");
        complaintInfoDTO.setBuyDealerCode("buyDealerCode");

        when(mockComplaintInfoMapper.selectListBySql(any(ComplaintInfoPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintInfoDTO> result = complaintInfoServiceImplUnderTest.selectListBySql(complaintInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure ComplaintInfoMapper.selectById(...).
        final ComplaintInfoPO complaintInfoPO = new ComplaintInfoPO();
        complaintInfoPO.setFirstRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setFirstCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setId(0L);
        complaintInfoPO.setComplaintId("complaintId");
        complaintInfoPO.setType(0);
        complaintInfoPO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setDealerFisrtReplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfoPO.setDataSources(0);
        when(mockComplaintInfoMapper.selectById(0L)).thenReturn(complaintInfoPO);

        // Run the test
        final ComplaintInfoDTO result = complaintInfoServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintInfoMapperReturnsNull() {
        // Setup
        when(mockComplaintInfoMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintInfoServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testSelectGoodWill() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setVoucherCouponFaceRechargePrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setAuditTimes(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoDTO.setTroubleSpots("troubleSpots");

        // Configure ComplaintInfoMapper.getByvin(...).
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO1 = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO1.setVoucherCouponFaceRechargePrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO1.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO1.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO1.setAuditTimes(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoDTO1.setTroubleSpots("troubleSpots");
        final List<GoodwillApplyInfoDTO> goodwillApplyInfoDTOS = Arrays.asList(goodwillApplyInfoDTO1);
        when(mockComplaintInfoMapper.getByvin(any(Page.class), any(GoodwillApplyInfoDTO.class)))
                .thenReturn(goodwillApplyInfoDTOS);

        // Run the test
        final IPage<GoodwillApplyInfoDTO> result = complaintInfoServiceImplUnderTest.selectGoodWill(page,
                goodwillApplyInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectGoodWill_ComplaintInfoMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyInfoDTO goodwillApplyInfoDTO = new GoodwillApplyInfoDTO();
        goodwillApplyInfoDTO.setVoucherCouponFaceRechargePrice(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setCostRate(new BigDecimal("0.00"));
        goodwillApplyInfoDTO.setAuditTimes(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillApplyInfoDTO.setTroubleSpots("troubleSpots");

        when(mockComplaintInfoMapper.getByvin(any(Page.class), any(GoodwillApplyInfoDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillApplyInfoDTO> result = complaintInfoServiceImplUnderTest.selectGoodWill(page,
                goodwillApplyInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectCusDetailById() {
        // Setup
        final ComplaintInfMoreDTO complaintInfMoreDTO = new ComplaintInfMoreDTO();
        complaintInfMoreDTO.setAssisDepartment("assisDepartment");
        complaintInfMoreDTO.setFirstCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setCloseCaseTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setCloseCaseTime1(new String[]{"closeCaseTime1"});
        complaintInfMoreDTO.setFollowStatus1(Arrays.asList());
        complaintInfMoreDTO.setCcMainReason1(Arrays.asList());
        complaintInfMoreDTO.setCcResult1(Arrays.asList());
        complaintInfMoreDTO.setCcmPart1(Arrays.asList());
        complaintInfMoreDTO.setCcmSubdivisionPart1(Arrays.asList());
        complaintInfMoreDTO.setCategory11(Arrays.asList());
        complaintInfMoreDTO.setCategory21(Arrays.asList());
        complaintInfMoreDTO.setCategory31(Arrays.asList());
        complaintInfMoreDTO.setClassification12(Arrays.asList());
        complaintInfMoreDTO.setClassification22(Arrays.asList());
        complaintInfMoreDTO.setSmallClass1(Arrays.asList());
        complaintInfMoreDTO.setBlocId("blocId");
        complaintInfMoreDTO.setRegionId("regionId");
        complaintInfMoreDTO.setRegionManagerId("regionManagerId");
        complaintInfMoreDTO.setSql1("sql1");
        complaintInfMoreDTO.setAssitdep("CreaterOrg");
        complaintInfMoreDTO.setClassification3("classification3");
        complaintInfMoreDTO.setDealerCode1(Arrays.asList());
        complaintInfMoreDTO.setCcmManId1(Arrays.asList());
        complaintInfMoreDTO.setRegion1(Arrays.asList());
        complaintInfMoreDTO.setRegionManager1(Arrays.asList());
        complaintInfMoreDTO.setAreaId("areaId");
        complaintInfMoreDTO.setAreaId1(Arrays.asList());
        complaintInfMoreDTO.setBloc1(Arrays.asList());
        complaintInfMoreDTO.setModel1(Arrays.asList());
        complaintInfMoreDTO.setSql("sql");
        complaintInfMoreDTO.setSmallClass("smallClass");
        complaintInfMoreDTO.setClassification11("classification11");
        complaintInfMoreDTO.setClassification21("classification21");
        complaintInfMoreDTO.setCcResult("ccResult");
        complaintInfMoreDTO.setCcmSubdivisionPart("ccmSubdivisionPart");
        complaintInfMoreDTO.setCcmPart("ccmPart");
        complaintInfMoreDTO.setCcMainReason("ccMainReason");
        complaintInfMoreDTO.setPlanFollowTime1(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setPlanFollowTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setFisrtRestartTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setActuallFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setActuallFollowTime1(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setSource1(Arrays.asList());
        complaintInfMoreDTO.setCallTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setType1(Arrays.asList());
        complaintInfMoreDTO.setActuallFollowTime2(new String[]{"actuallFollowTime2"});
        complaintInfMoreDTO.setCallTime1(new String[]{"callTime1"});
        complaintInfMoreDTO.setFisrtRestartTime1(new String[]{"fisrtRestartTime1"});
        complaintInfMoreDTO.setPlanFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setPlanFollowTime5(new String[]{"planFollowTime5"});
        complaintInfMoreDTO.setType("type");
        complaintInfMoreDTO.setSource("source");
        complaintInfMoreDTO.setCallTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setFisrtRestartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setModel("model");
        complaintInfMoreDTO.setDealerCode("dealerCode");
        complaintInfMoreDTO.setReport(false);
        complaintInfMoreDTO.setCategory1("category1");
        complaintInfMoreDTO.setCategory2("category2");
        complaintInfMoreDTO.setCategory3("category3");
        complaintInfMoreDTO.setCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setFollowStatus("followStatus");
        complaintInfMoreDTO.setCcmManId("ccmManId");
        complaintInfMoreDTO.setFirstRestartCloseCaseTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setFirstCloseCaseTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setFirstRestartCloseCaseTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        complaintInfMoreDTO.setFirstCloseCaseTime1(new String[]{"firstCloseCaseTime1"});
        complaintInfMoreDTO.setFirstRestartCloseCaseTime1(new String[]{"firstRestartCloseCaseTime1"});

        // Configure ComplaintInfoMapper.selectCusDetailById(...).
        final ComplaintInfMorePO complaintInfMorePO = new ComplaintInfMorePO();
        complaintInfMorePO.setInvalidCaseHidden(0);
        complaintInfMorePO.setHQSatisfiedCase(0);
        complaintInfMorePO.setRegionSatisfiedCase(0);
        complaintInfMorePO.setIsAnonymous(0);
        complaintInfMorePO.setLastFollowTime("lastFollowTime");
        when(mockComplaintInfoMapper.selectCusDetailById(any(ComplaintInfMoreDTO.class)))
                .thenReturn(complaintInfMorePO);

        // Run the test
        final ComplaintInfMorePO result = complaintInfoServiceImplUnderTest.selectCusDetailById(complaintInfMoreDTO);

        // Verify the results
    }

    @Test
    void testGetBillNo1_ThrowsServiceBizException() {
        // Setup
        // Run the test
        assertThatThrownBy(() -> complaintInfoServiceImplUnderTest.getBillNo1("type", "dealerCode"))
                .isInstanceOf(ServiceBizException.class);
    }
}
