package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyInfoPO;
import com.yonyou.dmscus.customer.service.goodwill.GoodwillApplyInfoService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class GoodwillApplyInfoServiceHelperTest {

    @Mock
    private GoodwillApplyInfoService mockGoodwillApplyInfoService;

    @InjectMocks
    private GoodwillApplyInfoServiceHelper goodwillApplyInfoServiceHelperUnderTest;

    @Test
    void testSendEmail() {
        // Setup
        final GoodwillApplyInfoPO goodwillApplyInfoPo = new GoodwillApplyInfoPO();
        goodwillApplyInfoPo.setAuditRole("auditRole");
        goodwillApplyInfoPo.setAuditName("auditName");
        goodwillApplyInfoPo.setAuditor(0L);
        goodwillApplyInfoPo.setCostConsumeAmount(new BigDecimal("0.00"));
        goodwillApplyInfoPo.setVoucherCouponFaceRechargePrice(new BigDecimal("0.00"));

        // Run the test
        goodwillApplyInfoServiceHelperUnderTest.sendEmail(goodwillApplyInfoPo, 0, "auditRole", 0);

        // Verify the results
        verify(mockGoodwillApplyInfoService).sendEmailAsync(any(GoodwillApplyInfoPO.class), eq(0), eq("auditRole"),
                eq(0));
    }
}
