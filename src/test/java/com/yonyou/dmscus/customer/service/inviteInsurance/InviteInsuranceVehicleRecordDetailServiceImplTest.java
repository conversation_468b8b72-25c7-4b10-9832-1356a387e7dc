package com.yonyou.dmscus.customer.service.inviteInsurance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleRecordDetailMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceVehicleRecordDetailPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteInsuranceVehicleRecordDetailServiceImplTest {

    @Mock
    private InviteInsuranceVehicleRecordDetailMapper mockInviteInsuranceVehicleRecordDetailMapper;

    private InviteInsuranceVehicleRecordDetailServiceImpl inviteInsuranceVehicleRecordDetailServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteInsuranceVehicleRecordDetailServiceImplUnderTest = new InviteInsuranceVehicleRecordDetailServiceImpl();
        inviteInsuranceVehicleRecordDetailServiceImplUnderTest.inviteInsuranceVehicleRecordDetailMapper = mockInviteInsuranceVehicleRecordDetailMapper;
    }

    @Test
    void testAddInviteInsuranceVehicleRecordDetail() {
        // Setup
        final InviteInsuranceVehicleRecordDetailDTO inviteInsuranceVehicleRecordDetailDTO = new InviteInsuranceVehicleRecordDetailDTO();
        inviteInsuranceVehicleRecordDetailDTO.setAppId("appId");
        inviteInsuranceVehicleRecordDetailDTO.setOwnerCode("ownerCode");
        inviteInsuranceVehicleRecordDetailDTO.setOwnerParCode("ownerParCode");
        inviteInsuranceVehicleRecordDetailDTO.setOrgId(0);
        inviteInsuranceVehicleRecordDetailDTO.setId(0L);

        // Run the test
        final Long result = inviteInsuranceVehicleRecordDetailServiceImplUnderTest.addInviteInsuranceVehicleRecordDetail(
                inviteInsuranceVehicleRecordDetailDTO);

        // Verify the results
        assertThat(result).isEqualTo(0L);

        // Confirm InviteInsuranceVehicleRecordDetailMapper.insert(...).
        final InviteInsuranceVehicleRecordDetailPO entity = new InviteInsuranceVehicleRecordDetailPO();
        entity.setOwnerCode("ownerCode");
        entity.setOrgId(0);
        entity.setId(0L);
        entity.setInviteId(0L);
        entity.setContent("content");
//        verify(mockInviteInsuranceVehicleRecordDetailMapper).insert(entity);
    }

    @Test
    void testGetInviteInsuranceVehicleRecordInfoDlr() {
        // Setup
        final InviteInsuranceVehicleRecordDetailDTO inviteInsuranceVehicleRecordDetailDTO = new InviteInsuranceVehicleRecordDetailDTO();
        inviteInsuranceVehicleRecordDetailDTO.setAppId("appId");
        inviteInsuranceVehicleRecordDetailDTO.setOwnerCode("ownerCode");
        inviteInsuranceVehicleRecordDetailDTO.setOwnerParCode("ownerParCode");
        inviteInsuranceVehicleRecordDetailDTO.setOrgId(0);
        inviteInsuranceVehicleRecordDetailDTO.setId(0L);
        final List<InviteInsuranceVehicleRecordDetailDTO> expectedResult = Arrays.asList(
                inviteInsuranceVehicleRecordDetailDTO);

        // Configure InviteInsuranceVehicleRecordDetailMapper.getInviteInsuranceVehicleRecordInfoDlr(...).
        final InviteInsuranceVehicleRecordDetailPO inviteInsuranceVehicleRecordDetailPO = new InviteInsuranceVehicleRecordDetailPO();
        inviteInsuranceVehicleRecordDetailPO.setOwnerCode("ownerCode");
        inviteInsuranceVehicleRecordDetailPO.setOrgId(0);
        inviteInsuranceVehicleRecordDetailPO.setId(0L);
        inviteInsuranceVehicleRecordDetailPO.setInviteId(0L);
        inviteInsuranceVehicleRecordDetailPO.setContent("content");
        final List<InviteInsuranceVehicleRecordDetailPO> inviteInsuranceVehicleRecordDetailPOS = Arrays.asList(
                inviteInsuranceVehicleRecordDetailPO);
        when(mockInviteInsuranceVehicleRecordDetailMapper.getInviteInsuranceVehicleRecordInfoDlr(0L))
                .thenReturn(inviteInsuranceVehicleRecordDetailPOS);

        // Run the test
        final List<InviteInsuranceVehicleRecordDetailDTO> result = inviteInsuranceVehicleRecordDetailServiceImplUnderTest.getInviteInsuranceVehicleRecordInfoDlr(
                "vin", 0L);

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetInviteInsuranceVehicleRecordInfoDlr_InviteInsuranceVehicleRecordDetailMapperReturnsNoItems() {
        // Setup
        when(mockInviteInsuranceVehicleRecordDetailMapper.getInviteInsuranceVehicleRecordInfoDlr(0L))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteInsuranceVehicleRecordDetailDTO> result = inviteInsuranceVehicleRecordDetailServiceImplUnderTest.getInviteInsuranceVehicleRecordInfoDlr(
                "vin", 0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectFollowInviteInsureDetailHistory() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);

        // Configure InviteInsuranceVehicleRecordDetailMapper.selectFollowInviteInsureDetailHistory(...).
        final InviteInsuranceVehicleRecordDetailPO inviteInsuranceVehicleRecordDetailPO = new InviteInsuranceVehicleRecordDetailPO();
        inviteInsuranceVehicleRecordDetailPO.setOwnerCode("ownerCode");
        inviteInsuranceVehicleRecordDetailPO.setOrgId(0);
        inviteInsuranceVehicleRecordDetailPO.setId(0L);
        inviteInsuranceVehicleRecordDetailPO.setInviteId(0L);
        inviteInsuranceVehicleRecordDetailPO.setContent("content");
        final List<InviteInsuranceVehicleRecordDetailPO> inviteInsuranceVehicleRecordDetailPOS = Arrays.asList(
                inviteInsuranceVehicleRecordDetailPO);
        when(mockInviteInsuranceVehicleRecordDetailMapper.selectFollowInviteInsureDetailHistory(any(Page.class),
                eq("vin"), eq("dealerCode"))).thenReturn(inviteInsuranceVehicleRecordDetailPOS);

        // Run the test
        final IPage<InviteInsuranceVehicleRecordDetailDTO> result = inviteInsuranceVehicleRecordDetailServiceImplUnderTest.selectFollowInviteInsureDetailHistory(
                page, "vin", "dealerCode");

        // Verify the results
    }

    @Test
    void testSelectFollowInviteInsureDetailHistory_InviteInsuranceVehicleRecordDetailMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        when(mockInviteInsuranceVehicleRecordDetailMapper.selectFollowInviteInsureDetailHistory(any(Page.class),
                eq("vin"), eq("dealerCode"))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<InviteInsuranceVehicleRecordDetailDTO> result = inviteInsuranceVehicleRecordDetailServiceImplUnderTest.selectFollowInviteInsureDetailHistory(
                page, "vin", "dealerCode");

        // Verify the results
    }
}
