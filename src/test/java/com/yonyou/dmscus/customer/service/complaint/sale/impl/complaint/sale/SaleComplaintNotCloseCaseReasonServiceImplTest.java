package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintNotCloseCaseReasonMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintNotCloseCaseReasonTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ReasonsforFiveDto;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintNotCloseCaseReasonDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintNotCloseCaseReasonTestPO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintNotCloseCaseReasonPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SaleComplaintNotCloseCaseReasonServiceImplTest {

    @Mock
    private SaleComplaintNotCloseCaseReasonMapper mockSaleComplaintNotCloseCaseReasonMapper;

    private SaleComplaintNotCloseCaseReasonServiceImpl saleComplaintNotCloseCaseReasonServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        saleComplaintNotCloseCaseReasonServiceImplUnderTest = new SaleComplaintNotCloseCaseReasonServiceImpl();
        saleComplaintNotCloseCaseReasonServiceImplUnderTest.saleComplaintNotCloseCaseReasonMapper = mockSaleComplaintNotCloseCaseReasonMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintNotCloseCaseReasonDTO saleComplaintNotCloseCaseReasonDTO = new SaleComplaintNotCloseCaseReasonDTO();
        saleComplaintNotCloseCaseReasonDTO.setComplaintInfoId(0L);
        saleComplaintNotCloseCaseReasonDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintNotCloseCaseReasonDTO.setFollower("follower");
        saleComplaintNotCloseCaseReasonDTO.setClassCode("classCode");
        saleComplaintNotCloseCaseReasonDTO.setClassName("className");
        saleComplaintNotCloseCaseReasonDTO.setOther("input1");
        saleComplaintNotCloseCaseReasonDTO.setDuration(0);
        saleComplaintNotCloseCaseReasonDTO.setFollowerName("followerName");

        // Configure SaleComplaintNotCloseCaseReasonMapper.selectPageBySql(...).
        final SaleComplaintNotCloseCaseReasonPO saleComplaintNotCloseCaseReasonPO = new SaleComplaintNotCloseCaseReasonPO();
        saleComplaintNotCloseCaseReasonPO.setAppId("appId");
        saleComplaintNotCloseCaseReasonPO.setOwnerCode("ownerCode");
        saleComplaintNotCloseCaseReasonPO.setOwnerParCode("ownerParCode");
        saleComplaintNotCloseCaseReasonPO.setId(0L);
        saleComplaintNotCloseCaseReasonPO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SaleComplaintNotCloseCaseReasonPO> saleComplaintNotCloseCaseReasonPOS = Arrays.asList(
                saleComplaintNotCloseCaseReasonPO);
        when(mockSaleComplaintNotCloseCaseReasonMapper.selectPageBySql(any(Page.class),
                any(SaleComplaintNotCloseCaseReasonPO.class))).thenReturn(saleComplaintNotCloseCaseReasonPOS);

        // Run the test
        final IPage<SaleComplaintNotCloseCaseReasonDTO> result = saleComplaintNotCloseCaseReasonServiceImplUnderTest.selectPageBysql(
                page, saleComplaintNotCloseCaseReasonDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_SaleComplaintNotCloseCaseReasonMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintNotCloseCaseReasonDTO saleComplaintNotCloseCaseReasonDTO = new SaleComplaintNotCloseCaseReasonDTO();
        saleComplaintNotCloseCaseReasonDTO.setComplaintInfoId(0L);
        saleComplaintNotCloseCaseReasonDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintNotCloseCaseReasonDTO.setFollower("follower");
        saleComplaintNotCloseCaseReasonDTO.setClassCode("classCode");
        saleComplaintNotCloseCaseReasonDTO.setClassName("className");
        saleComplaintNotCloseCaseReasonDTO.setOther("input1");
        saleComplaintNotCloseCaseReasonDTO.setDuration(0);
        saleComplaintNotCloseCaseReasonDTO.setFollowerName("followerName");

        when(mockSaleComplaintNotCloseCaseReasonMapper.selectPageBySql(any(Page.class),
                any(SaleComplaintNotCloseCaseReasonPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<SaleComplaintNotCloseCaseReasonDTO> result = saleComplaintNotCloseCaseReasonServiceImplUnderTest.selectPageBysql(
                page, saleComplaintNotCloseCaseReasonDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final SaleComplaintNotCloseCaseReasonDTO saleComplaintNotCloseCaseReasonDTO = new SaleComplaintNotCloseCaseReasonDTO();
        saleComplaintNotCloseCaseReasonDTO.setComplaintInfoId(0L);
        saleComplaintNotCloseCaseReasonDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintNotCloseCaseReasonDTO.setFollower("follower");
        saleComplaintNotCloseCaseReasonDTO.setClassCode("classCode");
        saleComplaintNotCloseCaseReasonDTO.setClassName("className");
        saleComplaintNotCloseCaseReasonDTO.setOther("input1");
        saleComplaintNotCloseCaseReasonDTO.setDuration(0);
        saleComplaintNotCloseCaseReasonDTO.setFollowerName("followerName");

        // Configure SaleComplaintNotCloseCaseReasonMapper.selectListBySql(...).
        final SaleComplaintNotCloseCaseReasonPO saleComplaintNotCloseCaseReasonPO = new SaleComplaintNotCloseCaseReasonPO();
        saleComplaintNotCloseCaseReasonPO.setAppId("appId");
        saleComplaintNotCloseCaseReasonPO.setOwnerCode("ownerCode");
        saleComplaintNotCloseCaseReasonPO.setOwnerParCode("ownerParCode");
        saleComplaintNotCloseCaseReasonPO.setId(0L);
        saleComplaintNotCloseCaseReasonPO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SaleComplaintNotCloseCaseReasonPO> saleComplaintNotCloseCaseReasonPOS = Arrays.asList(
                saleComplaintNotCloseCaseReasonPO);
        when(mockSaleComplaintNotCloseCaseReasonMapper.selectListBySql(
                any(SaleComplaintNotCloseCaseReasonPO.class))).thenReturn(saleComplaintNotCloseCaseReasonPOS);

        // Run the test
        final List<SaleComplaintNotCloseCaseReasonDTO> result = saleComplaintNotCloseCaseReasonServiceImplUnderTest.selectListBySql(
                saleComplaintNotCloseCaseReasonDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_SaleComplaintNotCloseCaseReasonMapperReturnsNoItems() {
        // Setup
        final SaleComplaintNotCloseCaseReasonDTO saleComplaintNotCloseCaseReasonDTO = new SaleComplaintNotCloseCaseReasonDTO();
        saleComplaintNotCloseCaseReasonDTO.setComplaintInfoId(0L);
        saleComplaintNotCloseCaseReasonDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintNotCloseCaseReasonDTO.setFollower("follower");
        saleComplaintNotCloseCaseReasonDTO.setClassCode("classCode");
        saleComplaintNotCloseCaseReasonDTO.setClassName("className");
        saleComplaintNotCloseCaseReasonDTO.setOther("input1");
        saleComplaintNotCloseCaseReasonDTO.setDuration(0);
        saleComplaintNotCloseCaseReasonDTO.setFollowerName("followerName");

        when(mockSaleComplaintNotCloseCaseReasonMapper.selectListBySql(
                any(SaleComplaintNotCloseCaseReasonPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<SaleComplaintNotCloseCaseReasonDTO> result = saleComplaintNotCloseCaseReasonServiceImplUnderTest.selectListBySql(
                saleComplaintNotCloseCaseReasonDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure SaleComplaintNotCloseCaseReasonMapper.selectById(...).
        final SaleComplaintNotCloseCaseReasonPO saleComplaintNotCloseCaseReasonPO = new SaleComplaintNotCloseCaseReasonPO();
        saleComplaintNotCloseCaseReasonPO.setAppId("appId");
        saleComplaintNotCloseCaseReasonPO.setOwnerCode("ownerCode");
        saleComplaintNotCloseCaseReasonPO.setOwnerParCode("ownerParCode");
        saleComplaintNotCloseCaseReasonPO.setId(0L);
        saleComplaintNotCloseCaseReasonPO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockSaleComplaintNotCloseCaseReasonMapper.selectById(0L)).thenReturn(saleComplaintNotCloseCaseReasonPO);

        // Run the test
        final SaleComplaintNotCloseCaseReasonDTO result = saleComplaintNotCloseCaseReasonServiceImplUnderTest.getById(
                0L);

        // Verify the results
    }

    @Test
    void testGetById_SaleComplaintNotCloseCaseReasonMapperReturnsNull() {
        // Setup
        when(mockSaleComplaintNotCloseCaseReasonMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> saleComplaintNotCloseCaseReasonServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final SaleComplaintNotCloseCaseReasonDTO saleComplaintNotCloseCaseReasonDTO = new SaleComplaintNotCloseCaseReasonDTO();
        saleComplaintNotCloseCaseReasonDTO.setComplaintInfoId(0L);
        saleComplaintNotCloseCaseReasonDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintNotCloseCaseReasonDTO.setFollower("follower");
        saleComplaintNotCloseCaseReasonDTO.setClassCode("classCode");
        saleComplaintNotCloseCaseReasonDTO.setClassName("className");
        saleComplaintNotCloseCaseReasonDTO.setOther("input1");
        saleComplaintNotCloseCaseReasonDTO.setDuration(0);
        saleComplaintNotCloseCaseReasonDTO.setFollowerName("followerName");

        when(mockSaleComplaintNotCloseCaseReasonMapper.insert(any(SaleComplaintNotCloseCaseReasonPO.class)))
                .thenReturn(0);

        // Run the test
        final int result = saleComplaintNotCloseCaseReasonServiceImplUnderTest.insert(
                saleComplaintNotCloseCaseReasonDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final SaleComplaintNotCloseCaseReasonDTO saleComplaintNotCloseCaseReasonDTO = new SaleComplaintNotCloseCaseReasonDTO();
        saleComplaintNotCloseCaseReasonDTO.setComplaintInfoId(0L);
        saleComplaintNotCloseCaseReasonDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintNotCloseCaseReasonDTO.setFollower("follower");
        saleComplaintNotCloseCaseReasonDTO.setClassCode("classCode");
        saleComplaintNotCloseCaseReasonDTO.setClassName("className");
        saleComplaintNotCloseCaseReasonDTO.setOther("input1");
        saleComplaintNotCloseCaseReasonDTO.setDuration(0);
        saleComplaintNotCloseCaseReasonDTO.setFollowerName("followerName");

        // Configure SaleComplaintNotCloseCaseReasonMapper.selectById(...).
        final SaleComplaintNotCloseCaseReasonPO saleComplaintNotCloseCaseReasonPO = new SaleComplaintNotCloseCaseReasonPO();
        saleComplaintNotCloseCaseReasonPO.setAppId("appId");
        saleComplaintNotCloseCaseReasonPO.setOwnerCode("ownerCode");
        saleComplaintNotCloseCaseReasonPO.setOwnerParCode("ownerParCode");
        saleComplaintNotCloseCaseReasonPO.setId(0L);
        saleComplaintNotCloseCaseReasonPO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockSaleComplaintNotCloseCaseReasonMapper.selectById(0L)).thenReturn(saleComplaintNotCloseCaseReasonPO);

        when(mockSaleComplaintNotCloseCaseReasonMapper.updateById(
                any(SaleComplaintNotCloseCaseReasonPO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintNotCloseCaseReasonServiceImplUnderTest.update(0L,
                saleComplaintNotCloseCaseReasonDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectPageBysql3() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintNotCloseCaseReasonTestDTO complaintNotCloseCaseReasonTestDTO = new ComplaintNotCloseCaseReasonTestDTO();
        complaintNotCloseCaseReasonTestDTO.setClassCode("classCode");
        complaintNotCloseCaseReasonTestDTO.setClassName("className");
        complaintNotCloseCaseReasonTestDTO.setAppId("appId");
        complaintNotCloseCaseReasonTestDTO.setOwnerCode("ownerCode");
        complaintNotCloseCaseReasonTestDTO.setFollowerName("followerName");

        // Configure SaleComplaintNotCloseCaseReasonMapper.selectPageBySql2(...).
        final ComplaintNotCloseCaseReasonTestPO complaintNotCloseCaseReasonTestPO = new ComplaintNotCloseCaseReasonTestPO();
        complaintNotCloseCaseReasonTestPO.setClassCode("classCode");
        complaintNotCloseCaseReasonTestPO.setClassName("className");
        complaintNotCloseCaseReasonTestPO.setSmallClassNameOther("smallClassNameOther");
        complaintNotCloseCaseReasonTestPO.setOwnerCode("ownerCode");
        complaintNotCloseCaseReasonTestPO.setFollowerName("followerName");
        final List<ComplaintNotCloseCaseReasonTestPO> complaintNotCloseCaseReasonTestPOS = Arrays.asList(
                complaintNotCloseCaseReasonTestPO);
        when(mockSaleComplaintNotCloseCaseReasonMapper.selectPageBySql2(any(Page.class),
                any(ComplaintNotCloseCaseReasonTestPO.class))).thenReturn(complaintNotCloseCaseReasonTestPOS);

        // Run the test
        final IPage<ComplaintNotCloseCaseReasonTestDTO> result = saleComplaintNotCloseCaseReasonServiceImplUnderTest.selectPageBysql3(
                page, complaintNotCloseCaseReasonTestDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql3_SaleComplaintNotCloseCaseReasonMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintNotCloseCaseReasonTestDTO complaintNotCloseCaseReasonTestDTO = new ComplaintNotCloseCaseReasonTestDTO();
        complaintNotCloseCaseReasonTestDTO.setClassCode("classCode");
        complaintNotCloseCaseReasonTestDTO.setClassName("className");
        complaintNotCloseCaseReasonTestDTO.setAppId("appId");
        complaintNotCloseCaseReasonTestDTO.setOwnerCode("ownerCode");
        complaintNotCloseCaseReasonTestDTO.setFollowerName("followerName");

        when(mockSaleComplaintNotCloseCaseReasonMapper.selectPageBySql2(any(Page.class),
                any(ComplaintNotCloseCaseReasonTestPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintNotCloseCaseReasonTestDTO> result = saleComplaintNotCloseCaseReasonServiceImplUnderTest.selectPageBysql3(
                page, complaintNotCloseCaseReasonTestDTO);

        // Verify the results
    }
}
