package com.yonyou.dmscus.customer.service.common.businessPlatform.dto;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

class VehicleOwnerDetailByIdListDTOTest {

    private VehicleOwnerDetailByIdListDTO vehicleOwnerDetailByIdListDTOUnderTest;

    @BeforeEach
    void setUp() {
        vehicleOwnerDetailByIdListDTOUnderTest = new VehicleOwnerDetailByIdListDTO();
    }

    @Test
    void testIdGetterAndSetter() {
        final Long id = 0L;
        vehicleOwnerDetailByIdListDTOUnderTest.setId(id);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getId()).isEqualTo(id);
    }

    @Test
    void testOneIdGetterAndSetter() {
        final Long oneId = 0L;
        vehicleOwnerDetailByIdListDTOUnderTest.setOneId(oneId);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getOneId()).isEqualTo(oneId);
    }

    @Test
    void testNameGetterAndSetter() {
        final String name = "name";
        vehicleOwnerDetailByIdListDTOUnderTest.setName(name);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getName()).isEqualTo(name);
    }

    @Test
    void testMobileGetterAndSetter() {
        final String mobile = "mobile";
        vehicleOwnerDetailByIdListDTOUnderTest.setMobile(mobile);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getMobile()).isEqualTo(mobile);
    }

    @Test
    void testContactNameGetterAndSetter() {
        final String contactName = "contactName";
        vehicleOwnerDetailByIdListDTOUnderTest.setContactName(contactName);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getContactName()).isEqualTo(contactName);
    }

    @Test
    void testContactorMobileGetterAndSetter() {
        final String contactorMobile = "contactorMobile";
        vehicleOwnerDetailByIdListDTOUnderTest.setContactorMobile(contactorMobile);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getContactorMobile()).isEqualTo(contactorMobile);
    }

    @Test
    void testContactorPhoneGetterAndSetter() {
        final String contactorPhone = "contactorPhone";
        vehicleOwnerDetailByIdListDTOUnderTest.setContactorPhone(contactorPhone);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getContactorPhone()).isEqualTo(contactorPhone);
    }

    @Test
    void testQqGetterAndSetter() {
        final String qq = "qq";
        vehicleOwnerDetailByIdListDTOUnderTest.setQq(qq);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getQq()).isEqualTo(qq);
    }

    @Test
    void testWeChatGetterAndSetter() {
        final String weChat = "weChat";
        vehicleOwnerDetailByIdListDTOUnderTest.setWeChat(weChat);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getWeChat()).isEqualTo(weChat);
    }

    @Test
    void testIndustryGetterAndSetter() {
        final Integer industry = 0;
        vehicleOwnerDetailByIdListDTOUnderTest.setIndustry(industry);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getIndustry()).isEqualTo(industry);
    }

    @Test
    void testEnterpriseTypeGetterAndSetter() {
        final Integer enterpriseType = 0;
        vehicleOwnerDetailByIdListDTOUnderTest.setEnterpriseType(enterpriseType);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getEnterpriseType()).isEqualTo(enterpriseType);
    }

    @Test
    void testBargainDateGetterAndSetter() {
        final LocalDateTime bargainDate = LocalDateTime.of(2020, 1, 1, 0, 0, 0);
        vehicleOwnerDetailByIdListDTOUnderTest.setBargainDate(bargainDate);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getBargainDate()).isEqualTo(bargainDate);
    }

    @Test
    void testCustomerTypeGetterAndSetter() {
        final Integer customerType = 0;
        vehicleOwnerDetailByIdListDTOUnderTest.setCustomerType(customerType);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getCustomerType()).isEqualTo(customerType);
    }

    @Test
    void testGenderGetterAndSetter() {
        final Integer gender = 0;
        vehicleOwnerDetailByIdListDTOUnderTest.setGender(gender);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getGender()).isEqualTo(gender);
    }

    @Test
    void testEducationGetterAndSetter() {
        final Integer education = 0;
        vehicleOwnerDetailByIdListDTOUnderTest.setEducation(education);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getEducation()).isEqualTo(education);
    }

    @Test
    void testOccupationGetterAndSetter() {
        final Integer occupation = 0;
        vehicleOwnerDetailByIdListDTOUnderTest.setOccupation(occupation);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getOccupation()).isEqualTo(occupation);
    }

    @Test
    void testBirthdayGetterAndSetter() {
        final LocalDate birthday = LocalDate.of(2020, 1, 1);
        vehicleOwnerDetailByIdListDTOUnderTest.setBirthday(birthday);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getBirthday()).isEqualTo(birthday);
    }

    @Test
    void testCtCodeGetterAndSetter() {
        final Integer ctCode = 0;
        vehicleOwnerDetailByIdListDTOUnderTest.setCtCode(ctCode);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getCtCode()).isEqualTo(ctCode);
    }

    @Test
    void testCertificateNoGetterAndSetter() {
        final String certificateNo = "certificateNo";
        vehicleOwnerDetailByIdListDTOUnderTest.setCertificateNo(certificateNo);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getCertificateNo()).isEqualTo(certificateNo);
    }

    @Test
    void testEMailGetterAndSetter() {
        final String eMail = "eMail";
        vehicleOwnerDetailByIdListDTOUnderTest.seteMail(eMail);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.geteMail()).isEqualTo(eMail);
    }

    @Test
    void testAddressGetterAndSetter() {
        final String address = "address";
        vehicleOwnerDetailByIdListDTOUnderTest.setAddress(address);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getAddress()).isEqualTo(address);
    }

    @Test
    void testZipCodeGetterAndSetter() {
        final String zipCode = "zipCode";
        vehicleOwnerDetailByIdListDTOUnderTest.setZipCode(zipCode);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getZipCode()).isEqualTo(zipCode);
    }

    @Test
    void testMaritalStatusGetterAndSetter() {
        final Integer maritalStatus = 0;
        vehicleOwnerDetailByIdListDTOUnderTest.setMaritalStatus(maritalStatus);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getMaritalStatus()).isEqualTo(maritalStatus);
    }

    @Test
    void testProvinceGetterAndSetter() {
        final Long province = 0L;
        vehicleOwnerDetailByIdListDTOUnderTest.setProvince(province);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getProvince()).isEqualTo(province);
    }

    @Test
    void testProvinceNameGetterAndSetter() {
        final String provinceName = "provinceName";
        vehicleOwnerDetailByIdListDTOUnderTest.setProvinceName(provinceName);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getProvinceName()).isEqualTo(provinceName);
    }

    @Test
    void testCityGetterAndSetter() {
        final Long city = 0L;
        vehicleOwnerDetailByIdListDTOUnderTest.setCity(city);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getCity()).isEqualTo(city);
    }

    @Test
    void testCityNameGetterAndSetter() {
        final String cityName = "cityName";
        vehicleOwnerDetailByIdListDTOUnderTest.setCityName(cityName);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getCityName()).isEqualTo(cityName);
    }

    @Test
    void testDistrictGetterAndSetter() {
        final Long district = 0L;
        vehicleOwnerDetailByIdListDTOUnderTest.setDistrict(district);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getDistrict()).isEqualTo(district);
    }

    @Test
    void testDistrictNameGetterAndSetter() {
        final String districtName = "districtName";
        vehicleOwnerDetailByIdListDTOUnderTest.setDistrictName(districtName);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getDistrictName()).isEqualTo(districtName);
    }

    @Test
    void testCreatedDateGetterAndSetter() {
        final LocalDateTime createdDate = LocalDateTime.of(2020, 1, 1, 0, 0, 0);
        vehicleOwnerDetailByIdListDTOUnderTest.setCreatedDate(createdDate);
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.getCreatedDate()).isEqualTo(createdDate);
    }

    @Test
    void testToString() {
        assertThat(vehicleOwnerDetailByIdListDTOUnderTest.toString()).isEqualTo("result");
    }
}
