package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillDealerMailInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillDealerMailInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.companySelectDTO;
import com.yonyou.dmscus.customer.entity.dto.goodwill.queryUserByOrgTypeDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillDealerMailInfoPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillDealerMailInfoServiceImplTest {

    @Mock
    private GoodwillDealerMailInfoMapper mockGoodwillDealerMailInfoMapper;
    @Mock
    private RestTemplate mockDirectRestTemplate;
    @Mock
    private MidUrlProperties mockMidUrlProperties;

    @InjectMocks
    private GoodwillDealerMailInfoServiceImpl goodwillDealerMailInfoServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        goodwillDealerMailInfoServiceImplUnderTest.goodwillDealerMailInfoMapper = mockGoodwillDealerMailInfoMapper;
        goodwillDealerMailInfoServiceImplUnderTest.request = new MockHttpServletRequest();
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final GoodwillDealerMailInfoDTO goodwillDealerMailInfoDTO = new GoodwillDealerMailInfoDTO();
        goodwillDealerMailInfoDTO.setDealerName("dealerName");
        goodwillDealerMailInfoDTO.setBloc("bloc");
        goodwillDealerMailInfoDTO.setAreaManage(0);
        goodwillDealerMailInfoDTO.setAppId("appId");
        goodwillDealerMailInfoDTO.setDealerCode("dealerCode");

        // Configure GoodwillDealerMailInfoMapper.selectListBySql(...).
        final GoodwillDealerMailInfoPO goodwillDealerMailInfoPO = new GoodwillDealerMailInfoPO();
        goodwillDealerMailInfoPO.setBloc("areaManage");
        goodwillDealerMailInfoPO.setDealerName("dealerName");
        goodwillDealerMailInfoPO.setAreaManage("areaManage");
        goodwillDealerMailInfoPO.setAppId("appId");
        goodwillDealerMailInfoPO.setOwnerCode("ownerCode");
        final List<GoodwillDealerMailInfoPO> goodwillDealerMailInfoPOS = Arrays.asList(goodwillDealerMailInfoPO);
        when(mockGoodwillDealerMailInfoMapper.selectListBySql(any(GoodwillDealerMailInfoPO.class)))
                .thenReturn(goodwillDealerMailInfoPOS);

        // Run the test
        final List<GoodwillDealerMailInfoDTO> result = goodwillDealerMailInfoServiceImplUnderTest.selectListBySql(
                goodwillDealerMailInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillDealerMailInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final GoodwillDealerMailInfoDTO goodwillDealerMailInfoDTO = new GoodwillDealerMailInfoDTO();
        goodwillDealerMailInfoDTO.setDealerName("dealerName");
        goodwillDealerMailInfoDTO.setBloc("bloc");
        goodwillDealerMailInfoDTO.setAreaManage(0);
        goodwillDealerMailInfoDTO.setAppId("appId");
        goodwillDealerMailInfoDTO.setDealerCode("dealerCode");

        when(mockGoodwillDealerMailInfoMapper.selectListBySql(any(GoodwillDealerMailInfoPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillDealerMailInfoDTO> result = goodwillDealerMailInfoServiceImplUnderTest.selectListBySql(
                goodwillDealerMailInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure GoodwillDealerMailInfoMapper.selectById(...).
        final GoodwillDealerMailInfoPO goodwillDealerMailInfoPO = new GoodwillDealerMailInfoPO();
        goodwillDealerMailInfoPO.setBloc("areaManage");
        goodwillDealerMailInfoPO.setDealerName("dealerName");
        goodwillDealerMailInfoPO.setAreaManage("areaManage");
        goodwillDealerMailInfoPO.setAppId("appId");
        goodwillDealerMailInfoPO.setOwnerCode("ownerCode");
        when(mockGoodwillDealerMailInfoMapper.selectById(0L)).thenReturn(goodwillDealerMailInfoPO);

        // Run the test
        final GoodwillDealerMailInfoDTO result = goodwillDealerMailInfoServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillDealerMailInfoMapperReturnsNull() {
        // Setup
        when(mockGoodwillDealerMailInfoMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillDealerMailInfoServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final GoodwillDealerMailInfoDTO goodwillDealerMailInfoDTO = new GoodwillDealerMailInfoDTO();
        goodwillDealerMailInfoDTO.setDealerName("dealerName");
        goodwillDealerMailInfoDTO.setBloc("bloc");
        goodwillDealerMailInfoDTO.setAreaManage(0);
        goodwillDealerMailInfoDTO.setAppId("appId");
        goodwillDealerMailInfoDTO.setDealerCode("dealerCode");

        when(mockGoodwillDealerMailInfoMapper.insert(any(GoodwillDealerMailInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillDealerMailInfoServiceImplUnderTest.insert(goodwillDealerMailInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final GoodwillDealerMailInfoDTO goodwillDealerMailInfoDTO = new GoodwillDealerMailInfoDTO();
        goodwillDealerMailInfoDTO.setDealerName("dealerName");
        goodwillDealerMailInfoDTO.setBloc("bloc");
        goodwillDealerMailInfoDTO.setAreaManage(0);
        goodwillDealerMailInfoDTO.setAppId("appId");
        goodwillDealerMailInfoDTO.setDealerCode("dealerCode");

        // Configure GoodwillDealerMailInfoMapper.selectById(...).
        final GoodwillDealerMailInfoPO goodwillDealerMailInfoPO = new GoodwillDealerMailInfoPO();
        goodwillDealerMailInfoPO.setBloc("areaManage");
        goodwillDealerMailInfoPO.setDealerName("dealerName");
        goodwillDealerMailInfoPO.setAreaManage("areaManage");
        goodwillDealerMailInfoPO.setAppId("appId");
        goodwillDealerMailInfoPO.setOwnerCode("ownerCode");
        when(mockGoodwillDealerMailInfoMapper.selectById(0L)).thenReturn(goodwillDealerMailInfoPO);

        when(mockGoodwillDealerMailInfoMapper.updateById(any(GoodwillDealerMailInfoPO.class))).thenReturn(0);
        when(mockGoodwillDealerMailInfoMapper.insert(any(GoodwillDealerMailInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillDealerMailInfoServiceImplUnderTest.update(0L, goodwillDealerMailInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testBlocSelect() {
        // Setup
        // Run the test
        final List<Map> result = goodwillDealerMailInfoServiceImplUnderTest.blocSelect();

        // Verify the results
    }

    @Test
    void testAreaManageSelect() {
        // Setup
        // Run the test
        final List<Map> result = goodwillDealerMailInfoServiceImplUnderTest.areaManageSelect();

        // Verify the results
    }
}
