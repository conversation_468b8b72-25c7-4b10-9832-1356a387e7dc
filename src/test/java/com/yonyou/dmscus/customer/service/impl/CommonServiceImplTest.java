package com.yonyou.dmscus.customer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.dto.*;
import com.yonyou.dmscus.customer.entity.dto.complaint.DepUserDataDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.GetDepUserDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.UserRoleDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.UserRoleDataDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.AppPushDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.MessageResponseDTO;
import com.yonyou.dmscus.customer.middleInterface.ResponseListDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CommonServiceImplTest {

    @Mock
    private RestTemplate mockDirectRestTemplate;
    @Mock
    private MidUrlProperties mockMidUrlProperties;

    @InjectMocks
    private CommonServiceImpl commonServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(commonServiceImplUnderTest, "appId", "appId");
        ReflectionTestUtils.setField(commonServiceImplUnderTest, "goodwillAppId", "goodwillAppId");
    }

    @Test
    void testMessageSendApp_RestTemplateThrowsRestClientException() {
        // Setup
        final AppPushDTO appPushDTO = new AppPushDTO();
        appPushDTO.setTargetCodes(new String[]{"targetCodes"});
        appPushDTO.setTargetType("targetType");
        appPushDTO.setContent("content");
        appPushDTO.setTitle("title");
        appPushDTO.setMode("mode");

        when(mockMidUrlProperties.getMidEndMessageCenter()).thenReturn("result");
        when(mockMidUrlProperties.getPushV1Apps()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final AppPushDTO appPushDTO1 = new AppPushDTO();
        appPushDTO1.setTargetCodes(new String[]{"targetCodes"});
        appPushDTO1.setTargetType("targetType");
        appPushDTO1.setContent("content");
        appPushDTO1.setTitle("title");
        appPushDTO1.setMode("mode");
        final HttpEntity<AppPushDTO> requestEntity = new HttpEntity<>(appPushDTO1, new HttpHeaders());
        when(mockDirectRestTemplate.exchange("url", HttpMethod.POST, requestEntity,
                MessageResponseDTO.class)).thenThrow(RestClientException.class);

        // Run the test
        assertThatThrownBy(() -> commonServiceImplUnderTest.messageSendApp(appPushDTO))
                .isInstanceOf(ServiceBizException.class);
    }
}
