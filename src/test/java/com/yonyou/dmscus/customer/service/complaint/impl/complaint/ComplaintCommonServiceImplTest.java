package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.yonyou.dmscus.customer.dao.complaint.ComplaintInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintClassificationDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintCommonServiceImplTest {

    @Mock
    private ComplaintInfoMapper mockComplaintInfoMapper;

    private ComplaintCommonServiceImpl complaintCommonServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        complaintCommonServiceImplUnderTest = new ComplaintCommonServiceImpl();
        complaintCommonServiceImplUnderTest.complaintInfoMapper = mockComplaintInfoMapper;
    }

    @Test
    void testGetCusRequirement() {
        assertThat(complaintCommonServiceImplUnderTest.getCusRequirement("cusRequirementData")).isEqualTo("result");
    }

    @Test
    void testGetProblemInf() {
        assertThat(complaintCommonServiceImplUnderTest.getProblemInf("problemInfoData")).isEqualTo("");
    }

    @Test
    void testGetSource() {
        assertThat(complaintCommonServiceImplUnderTest.getSource(0)).isEqualTo("");
    }

    @Test
    void testGetCategory2() {
        assertThat(complaintCommonServiceImplUnderTest.getCategory2("category2Data")).isEqualTo("");
    }

    @Test
    void testGetCategory3() {
        assertThat(complaintCommonServiceImplUnderTest.getCategory3("category3Data")).isEqualTo("");
    }
}
