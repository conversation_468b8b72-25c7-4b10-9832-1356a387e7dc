package com.yonyou.dmscus.customer.service.impl.talkskill;

import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.talkskill.TalkskillTagMapper;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillTagDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.TalkskillTagPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TalkskillTagServiceImplTest {

    @Mock
    private TalkskillTagMapper mockTalkskillTagMapper;

    private TalkskillTagServiceImpl talkskillTagServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        talkskillTagServiceImplUnderTest = new TalkskillTagServiceImpl();
        talkskillTagServiceImplUnderTest.talkskillTagMapper = mockTalkskillTagMapper;
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final TalkskillTagDTO talkskillTagDTO = new TalkskillTagDTO();
        talkskillTagDTO.setTagId(0L);
        talkskillTagDTO.setParentId(0);
        talkskillTagDTO.setTagCode("tagCode");
        talkskillTagDTO.setTagName("tagName");
        talkskillTagDTO.setLevel(0);

        final TalkskillTagPO talkskillTagPO = new TalkskillTagPO();
        talkskillTagPO.setTagId(0L);
        talkskillTagPO.setParentId(0);
        talkskillTagPO.setTagCode("tagCode");
        talkskillTagPO.setTagName("tagName");
        talkskillTagPO.setLevel(0);
        final List<TalkskillTagPO> expectedResult = Arrays.asList(talkskillTagPO);

        // Configure TalkskillTagMapper.selectListBySql(...).
        final TalkskillTagPO talkskillTagPO1 = new TalkskillTagPO();
        talkskillTagPO1.setTagId(0L);
        talkskillTagPO1.setParentId(0);
        talkskillTagPO1.setTagCode("tagCode");
        talkskillTagPO1.setTagName("tagName");
        talkskillTagPO1.setLevel(0);
        final List<TalkskillTagPO> talkskillTagPOS = Arrays.asList(talkskillTagPO1);
        final TalkskillTagPO t = new TalkskillTagPO();
        t.setTagId(0L);
        t.setParentId(0);
        t.setTagCode("tagCode");
        t.setTagName("tagName");
        t.setLevel(0);
        when(mockTalkskillTagMapper.selectListBySql(t)).thenReturn(talkskillTagPOS);

        // Run the test
        final List<TalkskillTagPO> result = talkskillTagServiceImplUnderTest.selectListBySql(talkskillTagDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectListBySql_TalkskillTagMapperReturnsNoItems() {
        // Setup
        final TalkskillTagDTO talkskillTagDTO = new TalkskillTagDTO();
        talkskillTagDTO.setTagId(0L);
        talkskillTagDTO.setParentId(0);
        talkskillTagDTO.setTagCode("tagCode");
        talkskillTagDTO.setTagName("tagName");
        talkskillTagDTO.setLevel(0);

        // Configure TalkskillTagMapper.selectListBySql(...).
        final TalkskillTagPO t = new TalkskillTagPO();
        t.setTagId(0L);
        t.setParentId(0);
        t.setTagCode("tagCode");
        t.setTagName("tagName");
        t.setLevel(0);
        when(mockTalkskillTagMapper.selectListBySql(t)).thenReturn(Collections.emptyList());

        // Run the test
        final List<TalkskillTagPO> result = talkskillTagServiceImplUnderTest.selectListBySql(talkskillTagDTO);

        // Verify the results
//        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        final TalkskillTagDTO expectedResult = new TalkskillTagDTO();
        expectedResult.setTagId(0L);
        expectedResult.setParentId(0);
        expectedResult.setTagCode("tagCode");
        expectedResult.setTagName("tagName");
        expectedResult.setLevel(0);

        // Configure TalkskillTagMapper.selectById(...).
        final TalkskillTagPO talkskillTagPO = new TalkskillTagPO();
        talkskillTagPO.setTagId(0L);
        talkskillTagPO.setParentId(0);
        talkskillTagPO.setTagCode("tagCode");
        talkskillTagPO.setTagName("tagName");
        talkskillTagPO.setLevel(0);
        when(mockTalkskillTagMapper.selectById(0L)).thenReturn(talkskillTagPO);

        // Run the test
        final TalkskillTagDTO result = talkskillTagServiceImplUnderTest.getById(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetById_TalkskillTagMapperReturnsNull() {
        // Setup
        when(mockTalkskillTagMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> talkskillTagServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final TalkskillTagDTO talkskillTagDTO = new TalkskillTagDTO();
        talkskillTagDTO.setTagId(0L);
        talkskillTagDTO.setParentId(0);
        talkskillTagDTO.setTagCode("tagCode");
        talkskillTagDTO.setTagName("tagName");
        talkskillTagDTO.setLevel(0);

        // Configure TalkskillTagMapper.insert(...).
        final TalkskillTagPO entity = new TalkskillTagPO();
        entity.setTagId(0L);
        entity.setParentId(0);
        entity.setTagCode("tagCode");
        entity.setTagName("tagName");
        entity.setLevel(0);
        when(mockTalkskillTagMapper.insert(entity)).thenReturn(0);

        // Run the test
        final int result = talkskillTagServiceImplUnderTest.insert(talkskillTagDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdateList() {
        // Setup
        final TalkskillTagDTO talkskillTagDTO1 = new TalkskillTagDTO();
        talkskillTagDTO1.setTagId(0L);
        talkskillTagDTO1.setParentId(0);
        talkskillTagDTO1.setTagCode("tagCode");
        talkskillTagDTO1.setTagName("tagName");
        talkskillTagDTO1.setLevel(0);
        final List<TalkskillTagDTO> talkskillTagDTO = Arrays.asList(talkskillTagDTO1);

        // Run the test
        final int result = talkskillTagServiceImplUnderTest.updateList(talkskillTagDTO);

        // Verify the results
//        assertThat(result).isEqualTo(0);

        // Confirm TalkskillTagMapper.insert(...).
        final TalkskillTagPO entity = new TalkskillTagPO();
        entity.setTagId(0L);
        entity.setParentId(0);
        entity.setTagCode("tagCode");
        entity.setTagName("tagName");
        entity.setLevel(0);
        verify(mockTalkskillTagMapper).insert(entity);

        // Confirm TalkskillTagMapper.updateById(...).
        final TalkskillTagPO entity1 = new TalkskillTagPO();
        entity1.setTagId(0L);
        entity1.setParentId(0);
        entity1.setTagCode("tagCode");
        entity1.setTagName("tagName");
        entity1.setLevel(0);
        verify(mockTalkskillTagMapper).updateById(entity1);
    }

    @Test
    void testUpdate() {
        // Setup
        final TalkskillTagDTO talkskillTagDTO = new TalkskillTagDTO();
        talkskillTagDTO.setTagId(0L);
        talkskillTagDTO.setParentId(0);
        talkskillTagDTO.setTagCode("tagCode");
        talkskillTagDTO.setTagName("tagName");
        talkskillTagDTO.setLevel(0);

        // Configure TalkskillTagMapper.selectById(...).
        final TalkskillTagPO talkskillTagPO = new TalkskillTagPO();
        talkskillTagPO.setTagId(0L);
        talkskillTagPO.setParentId(0);
        talkskillTagPO.setTagCode("tagCode");
        talkskillTagPO.setTagName("tagName");
        talkskillTagPO.setLevel(0);
        when(mockTalkskillTagMapper.selectById(0L)).thenReturn(talkskillTagPO);

        // Configure TalkskillTagMapper.updateById(...).
        final TalkskillTagPO entity = new TalkskillTagPO();
        entity.setTagId(0L);
        entity.setParentId(0);
        entity.setTagCode("tagCode");
        entity.setTagName("tagName");
        entity.setLevel(0);
        when(mockTalkskillTagMapper.updateById(entity)).thenReturn(0);

        // Run the test
        final int result = talkskillTagServiceImplUnderTest.update(0L, talkskillTagDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
