package com.yonyou.dmscus.customer.service.impl.inviteRule;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.inviteRule.InviteRuleChangedRecordMapper;
import com.yonyou.dmscus.customer.dao.inviteRule.InviteRuleMapper;
import com.yonyou.dmscus.customer.dto.InvitationRuleVcdcParamsVo;
import com.yonyou.dmscus.customer.entity.dto.inviteRule.InviteRuleDTO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRuleChangedRecordPO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRulePO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteRuleServiceImplTest {

    @Mock
    private InviteRuleMapper mockInviteRuleMapper;
    @Mock
    private InviteRuleChangedRecordMapper mockInviteRuleChangedRecordMapper;
    @Mock
    private BusinessPlatformService mockBusinessPlatformService;

    private InviteRuleServiceImpl inviteRuleServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteRuleServiceImplUnderTest = new InviteRuleServiceImpl();
        inviteRuleServiceImplUnderTest.inviteRuleMapper = mockInviteRuleMapper;
        inviteRuleServiceImplUnderTest.inviteRuleChangedRecordMapper = mockInviteRuleChangedRecordMapper;
        inviteRuleServiceImplUnderTest.businessPlatformService = mockBusinessPlatformService;
    }

    @Test
    void testGetById_InviteRuleMapperReturnsNull() {
        // Setup
        when(mockInviteRuleMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inviteRuleServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testGetInvitationRuleVcdc_BusinessPlatformServiceReturnsNoItems() {
        // Setup
        final InvitationRuleVcdcParamsVo vo = new InvitationRuleVcdcParamsVo();
        vo.setLargeAreaId("largeAreaId");
        vo.setAreaId("areaId");
        vo.setAreaManageId("areaId");
        vo.setDealerCode("dealerCode");
        vo.setDealerName("dealerName");

        when(mockBusinessPlatformService.getDealercodes("areaId", "largeAreaId", "dealerCode",
                "dealerName")).thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteRuleDTO> result = inviteRuleServiceImplUnderTest.getInvitationRuleVcdc(vo);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetRegularMaintainRule() {
        // Setup
        final InviteRulePO inviteRulePO = new InviteRulePO();
        inviteRulePO.setUpdatedBy("updatedBy");
        inviteRulePO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteRulePO.setId(0L);
        inviteRulePO.setDealerCode("VCDC");
        inviteRulePO.setInviteType(0);
        inviteRulePO.setInviteRule(0);
        inviteRulePO.setRuleValue(0);
        inviteRulePO.setRemindInterval(0);
        inviteRulePO.setCloseInterval(0);
        inviteRulePO.setIsUse(0);
        final List<InviteRulePO> expectedResult = Arrays.asList(inviteRulePO);

        // Configure InviteRuleMapper.getRegularMaintainRule(...).
        final InviteRulePO inviteRulePO1 = new InviteRulePO();
        inviteRulePO1.setUpdatedBy("updatedBy");
        inviteRulePO1.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteRulePO1.setId(0L);
        inviteRulePO1.setDealerCode("VCDC");
        inviteRulePO1.setInviteType(0);
        inviteRulePO1.setInviteRule(0);
        inviteRulePO1.setRuleValue(0);
        inviteRulePO1.setRemindInterval(0);
        inviteRulePO1.setCloseInterval(0);
        inviteRulePO1.setIsUse(0);
        final List<InviteRulePO> inviteRulePOS = Arrays.asList(inviteRulePO1);
        when(mockInviteRuleMapper.getRegularMaintainRule("dealerCode")).thenReturn(inviteRulePOS);

        // Run the test
        final List<InviteRulePO> result = inviteRuleServiceImplUnderTest.getRegularMaintainRule("dealerCode");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetRegularMaintainRule_InviteRuleMapperReturnsNoItems() {
        // Setup
        when(mockInviteRuleMapper.getRegularMaintainRule("dealerCode")).thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteRulePO> result = inviteRuleServiceImplUnderTest.getRegularMaintainRule("dealerCode");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
