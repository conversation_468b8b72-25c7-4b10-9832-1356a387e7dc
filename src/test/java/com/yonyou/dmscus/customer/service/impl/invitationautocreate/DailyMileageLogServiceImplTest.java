package com.yonyou.dmscus.customer.service.impl.invitationautocreate;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.invitationautocreate.DailyMileageLogMapper;
import com.yonyou.dmscus.customer.entity.dto.invitationautocreate.DailyMileageLogDTO;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.DailyMileageLogPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DailyMileageLogServiceImplTest {

    @Mock
    private DailyMileageLogMapper mockDailyMileageLogMapper;

    private DailyMileageLogServiceImpl dailyMileageLogServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        dailyMileageLogServiceImplUnderTest = new DailyMileageLogServiceImpl();
        dailyMileageLogServiceImplUnderTest.dailyMileageLogMapper = mockDailyMileageLogMapper;
    }

    @Test
    void testGetById_DailyMileageLogMapperReturnsNull() {
        // Setup
        when(mockDailyMileageLogMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> dailyMileageLogServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }
}
