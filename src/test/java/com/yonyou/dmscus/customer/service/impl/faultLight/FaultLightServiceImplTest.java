package com.yonyou.dmscus.customer.service.impl.faultLight;

import com.yonyou.cloud.common.beans.RestResultResponse;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.dao.faultLight.*;
import com.yonyou.dmscus.customer.dao.voicemanage.SaWorkNumberMapper;
import com.yonyou.dmscus.customer.entity.dto.faultLight.TtFaultCallDetailsDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.TtFaultLightClueDTO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightCluePO;
import com.yonyou.dmscus.customer.feign.DomainMaintainLeadFeign;
import com.yonyou.dmscus.customer.feign.DomainMaintainOrdersClient;
import com.yonyou.dmscus.customer.feign.RepairCommonClient;
import com.yonyou.dmscus.customer.feign.dto.FaultRepairOrderDto;
import com.yonyou.dmscus.customer.service.faultLight.CluesDiagnosticInfoRelationService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightDisposeService;
import com.yonyou.dmscus.customer.service.impl.voicemanage.WorkNumberServiceContext;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.messaging.Message;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FaultLightServiceImplTest {
    @Mock
    private RepairCommonClient mockRepairCommonClient;
    @Mock
    private RocketMQTemplate mockRocketMQTemplate;
    @Mock
    private WorkNumberServiceContext mockWorkNumberServiceContext;
    @Mock
    private FaultLightDisposeService mockFaultLightDisposeService;
    @Mock
    private TtFaultCallRegisterMapper mockTtFaultCallRegisterMapper;
    @Mock
    private SaWorkNumberMapper mockSaWorkNumberMapper;
    @Mock
    private TtFaultCallDetailsMapper mockTtFaultCallDetailsMapper;
    @Mock
    private TtFaultLightClueMapper mockTtFaultLightClueMapper;
    @Mock
    private RvdcVehOnlineOfflineChangeRecordMapper mockRvdcVehOnlineOfflineChangeRecordMapper;
    @Mock
    private TtFaultLightInvitationMapper mockTtFaultLightInvitationMapper;
    @Mock
    private TtFaultLightFollowRecordMapper mockTtFaultLightFollowRecordMapper;
    @Mock
    private ApplicationEventPublisher mockEventPublisher;
    @Mock(lenient = true)
    private DomainMaintainLeadFeign mockDomainMaintainLeadFeign;
    @Mock
    private MidUrlProperties mockMidUrlProperties;
    @Mock
    private CluesDiagnosticInfoRelationService mockCluesDiagnosticInfoRelationService;
    @Mock
    private DomainMaintainOrdersClient domainMaintainOrdersClient;


    @InjectMocks
    private FaultLightServiceImpl faultLightServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(faultLightServiceImplUnderTest, "faultLightTopic", "faultLightTopic");
        ReflectionTestUtils.setField(faultLightServiceImplUnderTest, "cdpFault", "cdpFault");
        ReflectionTestUtils.setField(faultLightServiceImplUnderTest, "ak", "ak");
        ReflectionTestUtils.setField(faultLightServiceImplUnderTest, "as", "as");
        ReflectionTestUtils.setField(faultLightServiceImplUnderTest, "queryDtcDiagnosisInfoUrl",
                "queryDtcDiagnosisInfoUrl");
        faultLightServiceImplUnderTest.URL_MAPPING_REGISTER = "URL_MAPPING_REGISTER";
    }

    @Test
    void testGetAge2() {
        assertThat(FaultLightServiceImpl.getAge("dateOfBirth")).isEqualTo(0);
    }

    @Test
    void testQueryCallDetails() {
        // Setup
        final TtFaultCallDetailsDTO dto = new TtFaultCallDetailsDTO();
        dto.setCusName("cusName");
        dto.setCusNumber("cusNumber");
        dto.setCallLength(0);
        dto.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setInviteId("inviteId");

        final TtFaultCallDetailsDTO ttFaultCallDetailsDTO = new TtFaultCallDetailsDTO();
        ttFaultCallDetailsDTO.setCusName("cusName");
        ttFaultCallDetailsDTO.setCusNumber("cusNumber");
        ttFaultCallDetailsDTO.setCallLength(0);
        ttFaultCallDetailsDTO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        ttFaultCallDetailsDTO.setInviteId("inviteId");
        final List<TtFaultCallDetailsDTO> expectedResult = Arrays.asList(ttFaultCallDetailsDTO);

        // Configure TtFaultCallDetailsMapper.queryCallDetails(...).
        final TtFaultCallDetailsDTO ttFaultCallDetailsDTO1 = new TtFaultCallDetailsDTO();
        ttFaultCallDetailsDTO1.setCusName("cusName");
        ttFaultCallDetailsDTO1.setCusNumber("cusNumber");
        ttFaultCallDetailsDTO1.setCallLength(0);
        ttFaultCallDetailsDTO1.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        ttFaultCallDetailsDTO1.setInviteId("inviteId");
        final List<TtFaultCallDetailsDTO> ttFaultCallDetailsDTOS = Arrays.asList(ttFaultCallDetailsDTO1);
        when(mockTtFaultCallDetailsMapper.queryCallDetails("inviteId")).thenReturn(ttFaultCallDetailsDTOS);

        // Run the test
        final List<TtFaultCallDetailsDTO> result = faultLightServiceImplUnderTest.queryCallDetails(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryCallDetails_TtFaultCallDetailsMapperReturnsNoItems() {
        // Setup
        final TtFaultCallDetailsDTO dto = new TtFaultCallDetailsDTO();
        dto.setCusName("cusName");
        dto.setCusNumber("cusNumber");
        dto.setCallLength(0);
        dto.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setInviteId("inviteId");

        when(mockTtFaultCallDetailsMapper.queryCallDetails("inviteId")).thenReturn(Collections.emptyList());

        // Run the test
        final List<TtFaultCallDetailsDTO> result = faultLightServiceImplUnderTest.queryCallDetails(dto);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testFaultLightStatusChange_TtFaultLightClueMapperGetFaultLightClueChangeReturnsNoItems() {
        // Setup
        when(mockTtFaultLightClueMapper.getFaultLightClueChange()).thenReturn(Collections.emptyList());

        // Run the test
        faultLightServiceImplUnderTest.faultLightStatusChange();

        // Verify the results
    }

    @Test
    void testFaultLightStatusChange_RepairCommonClientReturnsNoItems() {
        // Setup
        // Configure TtFaultLightClueMapper.getFaultLightClueChange(...).
        final List<TtFaultLightCluePO> ttFaultLightCluePOS = Arrays.asList(TtFaultLightCluePO.builder()
                .id(0L)
                .icmId(0L)
                .vin("vin")
                .dealerCode("dealerCode")
                .clueStatus(0)
                .followStatus(0)
                .clueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueCloTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueComTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .lightsUp(0)
                .wheRes(0)
                .forecastTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .build());
        when(mockTtFaultLightClueMapper.getFaultLightClueChange()).thenReturn(ttFaultLightCluePOS);

        when(mockRepairCommonClient.repairOrderChecker(Arrays.asList(TtFaultLightCluePO.builder()
                .id(0L)
                .icmId(0L)
                .vin("vin")
                .dealerCode("dealerCode")
                .clueStatus(0)
                .followStatus(0)
                .clueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueCloTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueComTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .lightsUp(0)
                .wheRes(0)
                .forecastTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .build()))).thenReturn(Collections.emptyList());

        // Run the test
        faultLightServiceImplUnderTest.faultLightStatusChange();

        // Verify the results
    }

    @Test
    void testQueryDealerList() {
        // Setup
        final TtFaultLightClueDTO dto = new TtFaultLightClueDTO();
        dto.setDealerCode("dealerCode");
        dto.setDealerName("dealerName");

        final TtFaultLightClueDTO ttFaultLightClueDTO = new TtFaultLightClueDTO();
        ttFaultLightClueDTO.setDealerCode("dealerCode");
        ttFaultLightClueDTO.setDealerName("dealerName");
        final List<TtFaultLightClueDTO> expectedResult = Arrays.asList(ttFaultLightClueDTO);

        // Configure TtFaultLightClueMapper.queryDealerList(...).
        final TtFaultLightClueDTO ttFaultLightClueDTO1 = new TtFaultLightClueDTO();
        ttFaultLightClueDTO1.setDealerCode("dealerCode");
        ttFaultLightClueDTO1.setDealerName("dealerName");
        final List<TtFaultLightClueDTO> ttFaultLightClueDTOS = Arrays.asList(ttFaultLightClueDTO1);
        final TtFaultLightClueDTO dto1 = new TtFaultLightClueDTO();
        dto1.setDealerCode("dealerCode");
        dto1.setDealerName("dealerName");
        when(mockTtFaultLightClueMapper.queryDealerList(dto1)).thenReturn(ttFaultLightClueDTOS);

        // Run the test
        final List<TtFaultLightClueDTO> result = faultLightServiceImplUnderTest.queryDealerList(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryDealerList_TtFaultLightClueMapperReturnsNoItems() {
        // Setup
        final TtFaultLightClueDTO dto = new TtFaultLightClueDTO();
        dto.setDealerCode("dealerCode");
        dto.setDealerName("dealerName");

        // Configure TtFaultLightClueMapper.queryDealerList(...).
        final TtFaultLightClueDTO dto1 = new TtFaultLightClueDTO();
        dto1.setDealerCode("dealerCode");
        dto1.setDealerName("dealerName");
        when(mockTtFaultLightClueMapper.queryDealerList(dto1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<TtFaultLightClueDTO> result = faultLightServiceImplUnderTest.queryDealerList(dto);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testPushMessage() {
        // Setup
        // Run the test
        faultLightServiceImplUnderTest.pushMessage(0L, "followUpStatus", "bizStatus",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        verify(mockRocketMQTemplate).asyncSend(eq("faultLightTopic"), any(Message.class), any(SendCallback.class));
    }

    @Test
    public void testParseDtcDiagnosisInfo() {
        List<String> dtcList = Collections.singletonList("CODE");
        RestResultResponse<Map<String, String>> dtcNameMapResult = new RestResultResponse<>();
        dtcNameMapResult.setSuccess(true);
        when(mockDomainMaintainLeadFeign.batchQueryDtcNameByCode(dtcList, 1)).thenReturn(dtcNameMapResult);

        String result = "{\"retCode\":\"00000\",\"retInfo\":\"success\",\"retResult\":{\"records\":[{\"ecu\":\"ECM\",\"dtcs\":{\"P050700\":{\"snapshots\":{\"20\":{\"strings\":{\"DD0B/Partial_Network_Hazard\":\"Deactivated\"}}},\"statusByExt\":{\"TestFailed\":false},\"strings\":{},\"statusIndicator\":{\"UnconfirmedThisOperationCycle\":false},\"doubles\":{\"TS20\":5.86369075E8},\"statusByMask\":{\"TestFailed\":false}}},\"ctUpload\":\"2024-05-30 08:32:40.19\",\"_etl_time\":\"2024-05-31 10:38:42\"}]}}";
        faultLightServiceImplUnderTest.parseDtcDiagnosisInfo(result);
    }

    @Test
    void testFaultLightOrderCorrelation() {
        // Setup
        // Configure TtFaultLightClueMapper.selectClueInfoList(...).
        final List<TtFaultLightCluePO> list = Arrays.asList(TtFaultLightCluePO.builder()
                        .id(12L)
                        .icmId(12L)
                        .vin("vin1")
                        .dealerCode("dealerCode2")
                        .clueStatus(0)
                        .followStatus(0)
                        .clueGenTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .roNo("roNo")
                        .roStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .roEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .clueCloTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .clueComTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .roType("repairTypeCode")
                        .roAmount("roAmount")
                        .lightsUp(0)
                        .wheRes(0)
                        .forecastTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build(),
                TtFaultLightCluePO.builder()
                        .id(13L)
                        .icmId(13L)
                        .vin("vin11")
                        .dealerCode("dealerCode21")
                        .clueStatus(0)
                        .followStatus(0)
                        .clueGenTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .roNo("roNo")
                        .roStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .roEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .clueCloTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .clueComTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .roType("repairTypeCode")
                        .roAmount("roAmount")
                        .lightsUp(0)
                        .wheRes(0)
                        .forecastTime(new GregorianCalendar(2025, Calendar.APRIL, 30).getTime())
                        .build(),
                TtFaultLightCluePO.builder()
                        .id(14L)
                        .icmId(14L)
                        .vin("vin14")
                        .dealerCode("dealerCode24")
                        .clueStatus(0)
                        .followStatus(0)
                        .clueGenTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .roNo("roNo")
                        .roStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .roEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .clueCloTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .clueComTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .roType("repairTypeCode")
                        .roAmount("roAmount")
                        .lightsUp(0)
                        .wheRes(0)
                        .forecastTime(null)
                        .build(),
                TtFaultLightCluePO.builder()
                        .id(14L)
                        .icmId(14L)
                        .vin("vin888")
                        .dealerCode("dealerCode888")
                        .clueStatus(0)
                        .followStatus(0)
                        .clueGenTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .roNo("roNo")
                        .roStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .roEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .clueCloTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .clueComTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .roType("repairTypeCode")
                        .roAmount("roAmount")
                        .lightsUp(0)
                        .wheRes(0)
                        .forecastTime(new GregorianCalendar(2023, Calendar.APRIL, 30).getTime())
                        .build(),
                TtFaultLightCluePO.builder()
                        .id(14L)
                        .icmId(14L)
                        .vin("vin999")
                        .dealerCode("dealerCode999")
                        .clueStatus(0)
                        .followStatus(0)
                        .clueGenTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .roNo("roNo")
                        .roStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .roEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .clueCloTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .clueComTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .roType("repairTypeCode")
                        .roAmount("roAmount")
                        .lightsUp(0)
                        .wheRes(0)
                        .forecastTime(new GregorianCalendar(2023, Calendar.APRIL, 30).getTime())
                        .build());

        when(mockTtFaultLightClueMapper.selectClueInfoList()).thenReturn(list);

        // Configure TtFaultLightClueMapper.selectClueArriveList(...).
        final List<TtFaultLightCluePO> list1 = Arrays.asList(TtFaultLightCluePO.builder()
                .id(0L)
                .icmId(0L)
                .vin("vin")
                .dealerCode("dealerCode")
                .clueStatus(0)
                .followStatus(0)
                .clueGenTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .roNo("roNo")
                .roStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .roEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueCloTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueComTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .roType("repairTypeCode")
                .roAmount("roAmount")
                .lightsUp(0)
                .wheRes(0)
                .forecastTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .build());
        when(mockTtFaultLightClueMapper.selectClueArriveList()).thenReturn(list1);

        RestResultResponse response = new RestResultResponse<>();

        final List<FaultRepairOrderDto> list3 = Arrays.asList(FaultRepairOrderDto.builder()
                .ownerCode("dealerCode")
                .roNo("roNo")
                .build());
        response.setData(list3);
        when(domainMaintainOrdersClient.queryVoidedOrder(any())).thenReturn(response);

        FaultRepairOrderDto orderDto1 = FaultRepairOrderDto.builder().roNo("2323").repairTypeCode("事故").createdAt(LocalDateTime.of(2024, 12, 10, 10, 39, 43)).build();
        RestResultResponse response1 = new RestResultResponse<>();
        response1.setData(orderDto1);
        when(domainMaintainOrdersClient.queryRoNoSpinner(eq("dealerCode2"), eq("vin1"), any(), any())).thenReturn(response1);
        FaultRepairOrderDto orderDto2 = FaultRepairOrderDto.builder().roNo("23232").repairTypeCode("事故2").createdAt(LocalDateTime.now()).build();

        RestResultResponse response2 = new RestResultResponse<>();
        response1.setData(orderDto2);
        when(domainMaintainOrdersClient.queryRoNoSpinner(eq("dealerCode"), eq("vin"), any(), any())).thenReturn(response2);

        when(domainMaintainOrdersClient.queryRoNoSpinner(eq("dealerCode888"), eq("vin888"), any(), any())).thenReturn(null);
        when(domainMaintainOrdersClient.queryRoNoSpinner(eq("dealerCode999"), eq("vin999"), any(), any())).thenReturn(null);
        when(domainMaintainOrdersClient.queryRoNoSpinner(eq(null), eq("vin888"), any(), any())).thenReturn(null);
        FaultRepairOrderDto orderDto3 = FaultRepairOrderDto.builder().roNo("weq").repairTypeCode("事故2").createdAt(LocalDateTime.now()).build();
        RestResultResponse response3 = new RestResultResponse<>();
        response1.setData(orderDto3);
        when(domainMaintainOrdersClient.queryRoNoSpinner(eq(null), eq("vin999"), any(), any())).thenReturn(response3);
        when(mockTtFaultLightClueMapper.batchUpdate(any())).thenReturn(true);
        when(mockTtFaultLightInvitationMapper.updateNoIntoById(any())).thenReturn(0);

        // Run the test
        faultLightServiceImplUnderTest.faultLightOrderCorrelation();
    }
}
