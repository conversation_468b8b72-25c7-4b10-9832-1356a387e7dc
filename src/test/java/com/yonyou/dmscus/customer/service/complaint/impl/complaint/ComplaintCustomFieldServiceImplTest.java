package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintCustomFieldMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintCustomFieldPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintCustomFieldServiceImplTest {

    @Mock
    private ComplaintCustomFieldMapper mockComplaintCustomFieldMapper;

    private ComplaintCustomFieldServiceImpl complaintCustomFieldServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        complaintCustomFieldServiceImplUnderTest = new ComplaintCustomFieldServiceImpl();
        complaintCustomFieldServiceImplUnderTest.complaintCustomFieldMapper = mockComplaintCustomFieldMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintCustomFieldDTO complaintCustomFieldDTO = new ComplaintCustomFieldDTO();
        complaintCustomFieldDTO.setAppId("appId");
        complaintCustomFieldDTO.setOwnerCode("ownerCode");
        complaintCustomFieldDTO.setOwnerParCode("ownerParCode");
        complaintCustomFieldDTO.setOrgId(0);
        complaintCustomFieldDTO.setId(0L);

        // Configure ComplaintCustomFieldMapper.selectPageBySql(...).
        final ComplaintCustomFieldPO complaintCustomFieldPO = new ComplaintCustomFieldPO();
        complaintCustomFieldPO.setOwnerCode("ownerCode");
        complaintCustomFieldPO.setOrgId(0);
        complaintCustomFieldPO.setId(0L);
        complaintCustomFieldPO.setTableName("tableName");
        complaintCustomFieldPO.setFieldName("fieldName");
        final List<ComplaintCustomFieldPO> complaintCustomFieldPOS = Arrays.asList(complaintCustomFieldPO);
        when(mockComplaintCustomFieldMapper.selectPageBySql(any(Page.class),
                any(ComplaintCustomFieldPO.class))).thenReturn(complaintCustomFieldPOS);

        // Run the test
        final IPage<ComplaintCustomFieldDTO> result = complaintCustomFieldServiceImplUnderTest.selectPageBysql(page,
                complaintCustomFieldDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintCustomFieldMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintCustomFieldDTO complaintCustomFieldDTO = new ComplaintCustomFieldDTO();
        complaintCustomFieldDTO.setAppId("appId");
        complaintCustomFieldDTO.setOwnerCode("ownerCode");
        complaintCustomFieldDTO.setOwnerParCode("ownerParCode");
        complaintCustomFieldDTO.setOrgId(0);
        complaintCustomFieldDTO.setId(0L);

        when(mockComplaintCustomFieldMapper.selectPageBySql(any(Page.class),
                any(ComplaintCustomFieldPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintCustomFieldDTO> result = complaintCustomFieldServiceImplUnderTest.selectPageBysql(page,
                complaintCustomFieldDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final ComplaintCustomFieldDTO complaintCustomFieldDTO = new ComplaintCustomFieldDTO();
        complaintCustomFieldDTO.setAppId("appId");
        complaintCustomFieldDTO.setOwnerCode("ownerCode");
        complaintCustomFieldDTO.setOwnerParCode("ownerParCode");
        complaintCustomFieldDTO.setOrgId(0);
        complaintCustomFieldDTO.setId(0L);

        // Configure ComplaintCustomFieldMapper.selectListBySql(...).
        final ComplaintCustomFieldPO complaintCustomFieldPO = new ComplaintCustomFieldPO();
        complaintCustomFieldPO.setOwnerCode("ownerCode");
        complaintCustomFieldPO.setOrgId(0);
        complaintCustomFieldPO.setId(0L);
        complaintCustomFieldPO.setTableName("tableName");
        complaintCustomFieldPO.setFieldName("fieldName");
        final List<ComplaintCustomFieldPO> complaintCustomFieldPOS = Arrays.asList(complaintCustomFieldPO);
        when(mockComplaintCustomFieldMapper.selectListBySql(any(ComplaintCustomFieldPO.class)))
                .thenReturn(complaintCustomFieldPOS);

        // Run the test
        final List<ComplaintCustomFieldDTO> result = complaintCustomFieldServiceImplUnderTest.selectListBySql(
                complaintCustomFieldDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintCustomFieldMapperReturnsNoItems() {
        // Setup
        final ComplaintCustomFieldDTO complaintCustomFieldDTO = new ComplaintCustomFieldDTO();
        complaintCustomFieldDTO.setAppId("appId");
        complaintCustomFieldDTO.setOwnerCode("ownerCode");
        complaintCustomFieldDTO.setOwnerParCode("ownerParCode");
        complaintCustomFieldDTO.setOrgId(0);
        complaintCustomFieldDTO.setId(0L);

        when(mockComplaintCustomFieldMapper.selectListBySql(any(ComplaintCustomFieldPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintCustomFieldDTO> result = complaintCustomFieldServiceImplUnderTest.selectListBySql(
                complaintCustomFieldDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure ComplaintCustomFieldMapper.selectById(...).
        final ComplaintCustomFieldPO complaintCustomFieldPO = new ComplaintCustomFieldPO();
        complaintCustomFieldPO.setOwnerCode("ownerCode");
        complaintCustomFieldPO.setOrgId(0);
        complaintCustomFieldPO.setId(0L);
        complaintCustomFieldPO.setTableName("tableName");
        complaintCustomFieldPO.setFieldName("fieldName");
        when(mockComplaintCustomFieldMapper.selectById(0L)).thenReturn(complaintCustomFieldPO);

        // Run the test
        final ComplaintCustomFieldDTO result = complaintCustomFieldServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintCustomFieldMapperReturnsNull() {
        // Setup
        when(mockComplaintCustomFieldMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintCustomFieldServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final ComplaintCustomFieldDTO complaintCustomFieldDTO = new ComplaintCustomFieldDTO();
        complaintCustomFieldDTO.setAppId("appId");
        complaintCustomFieldDTO.setOwnerCode("ownerCode");
        complaintCustomFieldDTO.setOwnerParCode("ownerParCode");
        complaintCustomFieldDTO.setOrgId(0);
        complaintCustomFieldDTO.setId(0L);

        when(mockComplaintCustomFieldMapper.insert(any(ComplaintCustomFieldPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintCustomFieldServiceImplUnderTest.insert(complaintCustomFieldDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final ComplaintCustomFieldDTO complaintCustomFieldDTO = new ComplaintCustomFieldDTO();
        complaintCustomFieldDTO.setAppId("appId");
        complaintCustomFieldDTO.setOwnerCode("ownerCode");
        complaintCustomFieldDTO.setOwnerParCode("ownerParCode");
        complaintCustomFieldDTO.setOrgId(0);
        complaintCustomFieldDTO.setId(0L);

        // Configure ComplaintCustomFieldMapper.selectById(...).
        final ComplaintCustomFieldPO complaintCustomFieldPO = new ComplaintCustomFieldPO();
        complaintCustomFieldPO.setOwnerCode("ownerCode");
        complaintCustomFieldPO.setOrgId(0);
        complaintCustomFieldPO.setId(0L);
        complaintCustomFieldPO.setTableName("tableName");
        complaintCustomFieldPO.setFieldName("fieldName");
        when(mockComplaintCustomFieldMapper.selectById(0L)).thenReturn(complaintCustomFieldPO);

        when(mockComplaintCustomFieldMapper.updateById(any(ComplaintCustomFieldPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintCustomFieldServiceImplUnderTest.update(0L, complaintCustomFieldDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
