package com.yonyou.dmscus.customer.service.inviteTag;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dao.InviteTag.InviteTagMapper;
import com.yonyou.dmscus.customer.dao.InviteTag.InviteVehicleTagMapper;
import com.yonyou.dmscus.customer.dao.common.VehicleMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dto.InviteTagDto;
import com.yonyou.dmscus.customer.dto.InviteVehicleTagDTO;
import com.yonyou.dmscus.customer.dto.InviteVehicleTagParamsDTO;
import com.yonyou.dmscus.customer.dto.RecVehicleTagDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.TmVehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.InviteClueParamDTO;
import com.yonyou.dmscus.customer.entity.dto.clue.InviteClueResultDTO;
import com.yonyou.dmscus.customer.entity.po.common.VehiclePO;
import com.yonyou.dmscus.customer.entity.po.inviteTag.InviteTagPO;
import com.yonyou.dmscus.customer.entity.po.inviteTag.InviteVehicleTagPO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteVehicleTagServiceImplTest {

    @Mock
    private BusinessPlatformService mockBusinessPlatformService;
    @Mock
    private InviteVehicleTagMapper mockInviteVehicleTagMapper;
    @Mock
    private InviteTagMapper mockInviteTagMapper;
    @Mock
    private VehicleMapper mockVehicleMapper;
    @Mock
    private InviteVehicleRecordMapper mockInviteVehicleRecordMapper;

    @InjectMocks
    private InviteVehicleTagServiceImpl inviteVehicleTagServiceImplUnderTest;

    @Test
    void testGetVinTag() {
        // Setup
        final InviteVehicleTagDTO expectedResult = new InviteVehicleTagDTO();
        expectedResult.setVin("vin");
        expectedResult.setTagName("tagName");
        expectedResult.setTagNameList(Arrays.asList("value"));
        final InviteTagDto inviteTagDto = new InviteTagDto();
        inviteTagDto.setName("name");
        expectedResult.setTagList(Arrays.asList(inviteTagDto));

        // Configure InviteVehicleTagMapper.selectVinTagByVIN(...).
        final InviteTagDto inviteTagDto1 = new InviteTagDto();
        inviteTagDto1.setName("name");
        inviteTagDto1.setTagDesc("tagDesc");
        inviteTagDto1.setTopic("topic");
        final List<InviteTagDto> inviteTagDtos = Arrays.asList(inviteTagDto1);
        when(mockInviteVehicleTagMapper.selectVinTagByVIN("vin")).thenReturn(inviteTagDtos);

        // Run the test
        final InviteVehicleTagDTO result = inviteVehicleTagServiceImplUnderTest.getVinTag("vin");

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetVinTag_InviteVehicleTagMapperReturnsNoItems() {
        // Setup
        final InviteVehicleTagDTO expectedResult = new InviteVehicleTagDTO();
        expectedResult.setVin("vin");
        expectedResult.setTagName("tagName");
        expectedResult.setTagNameList(Arrays.asList("value"));
        final InviteTagDto inviteTagDto = new InviteTagDto();
        inviteTagDto.setName("name");
        expectedResult.setTagList(Arrays.asList(inviteTagDto));

        when(mockInviteVehicleTagMapper.selectVinTagByVIN("vin")).thenReturn(Collections.emptyList());

        // Run the test
        final InviteVehicleTagDTO result = inviteVehicleTagServiceImplUnderTest.getVinTag("vin");

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testInitTagId() {
        // Setup
        final Map<String, Long> tagIdMap = new HashMap<>();

        // Configure InviteTagMapper.selectInviteTagByName(...).
        final InviteTagPO inviteTagPO = new InviteTagPO();
        inviteTagPO.setCreatedBy("createdBy");
        inviteTagPO.setAppId("appId");
        inviteTagPO.setId(0L);
        inviteTagPO.setName("name");
        inviteTagPO.setTagDesc("tagDesc");
        final List<InviteTagPO> inviteTagPOS = Arrays.asList(inviteTagPO);
        when(mockInviteTagMapper.selectInviteTagByName(new HashSet<>(Arrays.asList("value")), "vin"))
                .thenReturn(inviteTagPOS);

        // Run the test
        inviteVehicleTagServiceImplUnderTest.initTagId(new HashSet<>(Arrays.asList("value")), tagIdMap);

        // Verify the results
        verify(mockInviteTagMapper).insertBatchInviteTag(new HashSet<>(Arrays.asList("value")), "vin");
    }

    @Test
    void testInitTagId_InviteTagMapperSelectInviteTagByNameReturnsNoItems() {
        // Setup
        final Map<String, Long> tagIdMap = new HashMap<>();
        when(mockInviteTagMapper.selectInviteTagByName(new HashSet<>(Arrays.asList("value")), "vin"))
                .thenReturn(Collections.emptyList());

        // Run the test
        inviteVehicleTagServiceImplUnderTest.initTagId(new HashSet<>(Arrays.asList("value")), tagIdMap);

        // Verify the results
        verify(mockInviteTagMapper).insertBatchInviteTag(new HashSet<>(Arrays.asList("value")), "vin");
    }

    @Test
    void testGetAllTag() {
        // Setup
        final Map<String, String> expectedResult = new HashMap<>();

        // Configure InviteTagMapper.selectList(...).
        final InviteTagPO inviteTagPO = new InviteTagPO();
        inviteTagPO.setCreatedBy("createdBy");
        inviteTagPO.setAppId("appId");
        inviteTagPO.setId(0L);
        inviteTagPO.setName("name");
        inviteTagPO.setTagDesc("tagDesc");
        final List<InviteTagPO> inviteTagPOS = Arrays.asList(inviteTagPO);
        when(mockInviteTagMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(inviteTagPOS);

        // Run the test
        final Map<String, String> result = inviteVehicleTagServiceImplUnderTest.getAllTag();

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetAllTag_InviteTagMapperReturnsNoItems() {
        // Setup
        final Map<String, String> expectedResult = new HashMap<>();
        when(mockInviteTagMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, String> result = inviteVehicleTagServiceImplUnderTest.getAllTag();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectInviteClueTag() {
        // Setup
        final InviteClueParamDTO inviteClueParamDTO = new InviteClueParamDTO();
        inviteClueParamDTO.setPageSize(0);
        inviteClueParamDTO.setVin(Arrays.asList("value"));
        inviteClueParamDTO.setVerifyStatus(Arrays.asList(0));
        inviteClueParamDTO.setOrderStatus(Arrays.asList(0));
        inviteClueParamDTO.setCurrentPage(0);

        final InviteClueResultDTO inviteClueResultDTO = new InviteClueResultDTO();
        inviteClueResultDTO.setId(0L);
        inviteClueResultDTO.setAppId("appId");
        inviteClueResultDTO.setOwnerCode("ownerCode");
        inviteClueResultDTO.setSourceType(0);
        inviteClueResultDTO.setVin("vin");
        final List<InviteClueResultDTO> expectedResult = Arrays.asList(inviteClueResultDTO);

        // Configure InviteVehicleRecordMapper.selectInviteClue(...).
        final InviteClueResultDTO inviteClueResultDTO1 = new InviteClueResultDTO();
        inviteClueResultDTO1.setId(0L);
        inviteClueResultDTO1.setAppId("appId");
        inviteClueResultDTO1.setOwnerCode("ownerCode");
        inviteClueResultDTO1.setSourceType(0);
        inviteClueResultDTO1.setVin("vin");
        final List<InviteClueResultDTO> inviteClueResultDTOS = Arrays.asList(inviteClueResultDTO1);
        final InviteClueParamDTO params = new InviteClueParamDTO();
        params.setPageSize(0);
        params.setVin(Arrays.asList("value"));
        params.setVerifyStatus(Arrays.asList(0));
        params.setOrderStatus(Arrays.asList(0));
        params.setCurrentPage(0);
        when(mockInviteVehicleRecordMapper.selectInviteClue(any(Page.class), eq(params)))
                .thenReturn(inviteClueResultDTOS);

        // Run the test
        final List<InviteClueResultDTO> result = inviteVehicleTagServiceImplUnderTest.selectInviteClueTag(
                inviteClueParamDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectInviteClueTag_InviteVehicleRecordMapperReturnsNoItems() {
        // Setup
        final InviteClueParamDTO inviteClueParamDTO = new InviteClueParamDTO();
        inviteClueParamDTO.setPageSize(0);
        inviteClueParamDTO.setVin(Arrays.asList("value"));
        inviteClueParamDTO.setVerifyStatus(Arrays.asList(0));
        inviteClueParamDTO.setOrderStatus(Arrays.asList(0));
        inviteClueParamDTO.setCurrentPage(0);

        // Configure InviteVehicleRecordMapper.selectInviteClue(...).
        final InviteClueParamDTO params = new InviteClueParamDTO();
        params.setPageSize(0);
        params.setVin(Arrays.asList("value"));
        params.setVerifyStatus(Arrays.asList(0));
        params.setOrderStatus(Arrays.asList(0));
        params.setCurrentPage(0);
        when(mockInviteVehicleRecordMapper.selectInviteClue(any(Page.class), eq(params)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteClueResultDTO> result = inviteVehicleTagServiceImplUnderTest.selectInviteClueTag(
                inviteClueParamDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
