package com.yonyou.dmscus.customer.service.impl.faultLight;

import com.yonyou.dmscus.customer.dao.faultLight.TtFaultLightClueMapper;
import com.yonyou.dmscus.customer.dao.faultLight.TtFaultLightFollowRecordMapper;
import com.yonyou.dmscus.customer.dao.faultLight.TtFaultLightInvitationMapper;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.CompanyDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.TmVehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightFollowRecordDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightFollowSubDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.FaultLightParamDTO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightCluePO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightFollowRecordPO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightInvitationPO;
import com.yonyou.dmscus.customer.feign.RepairCommonClient;
import com.yonyou.dmscus.customer.feign.vo.PrintDataVo;
import com.yonyou.dmscus.customer.feign.vo.PrintParamVo;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightInvitationService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FaultLightFollowServiceImplTest {

    @Mock
    private TtFaultLightFollowRecordMapper mockTtFaultLightFollowRecordMapper;
    @Mock
    private TtFaultLightInvitationMapper mockTtFaultLightInvitationMapper;
    @Mock
    private TtFaultLightClueMapper mockTtFaultLightClueMapper;
    @Mock
    private RepairCommonClient mockRepairCommonClient;
    @Mock
    private BusinessPlatformService mockBusinessPlatformService;
    @Mock
    private FaultLightService mockFaultLightService;
    @Mock
    private FaultLightInvitationService mockFaultLightInvitationService;

    @InjectMocks
    private FaultLightFollowServiceImpl faultLightFollowServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(faultLightFollowServiceImplUnderTest, "mostDay", 1);
        ReflectionTestUtils.setField(faultLightFollowServiceImplUnderTest, "leastDay", 1);
    }

    @Test
    void testQueryFaultLightFollow() {
        // Setup
        final FaultLightFollowDTO expectedResult = new FaultLightFollowDTO();
        expectedResult.setGender("gender");
        expectedResult.setPlateNumber("plateNumber");
        expectedResult.setVin("vin");
        expectedResult.setModel("model");
        expectedResult.setComments("comments");

        // Configure TtFaultLightClueMapper.selectFaultLightFollow(...).
        final FaultLightFollowDTO faultLightFollowDTO = new FaultLightFollowDTO();
        faultLightFollowDTO.setGender("gender");
        faultLightFollowDTO.setPlateNumber("plateNumber");
        faultLightFollowDTO.setVin("vin");
        faultLightFollowDTO.setModel("model");
        faultLightFollowDTO.setComments("comments");
        when(mockTtFaultLightClueMapper.selectFaultLightFollow(0L)).thenReturn(faultLightFollowDTO);

        // Configure BusinessPlatformService.getVehicleByVIN(...).
        final TmVehicleDTO tmVehicleDTO = new TmVehicleDTO();
        tmVehicleDTO.setId(0L);
        tmVehicleDTO.setCustomerId(0L);
        tmVehicleDTO.setCompanyCode("companyCode");
        tmVehicleDTO.setPlateNumber("plateNumber");
        tmVehicleDTO.setModelName("model");
        when(mockBusinessPlatformService.getVehicleByVIN("vin")).thenReturn(tmVehicleDTO);

        // Run the test
        final FaultLightFollowDTO result = faultLightFollowServiceImplUnderTest.queryFaultLightFollow(0L);

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryFaultLightFollow_TtFaultLightClueMapperReturnsNull() {
        // Setup
        when(mockTtFaultLightClueMapper.selectFaultLightFollow(0L)).thenReturn(null);

        // Run the test
        final FaultLightFollowDTO result = faultLightFollowServiceImplUnderTest.queryFaultLightFollow(0L);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    void testQueryFollowSpinner() {
        // Setup
        final List<FaultLightParamDTO> expectedResult = Arrays.asList(new FaultLightParamDTO(0, 0, "name"));

        // Configure TtFaultLightClueMapper.selectById(...).
        final TtFaultLightCluePO ttFaultLightCluePO = TtFaultLightCluePO.builder()
                .id(0L)
                .icmId(0L)
                .vin("vin")
                .dealerCode("dealerCode")
                .faultId(0L)
                .faultCityName("faultCityName")
                .afClueStatus(0)
                .clueStatus(0)
                .followStatus(0)
                .clueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .inviteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .roNo("roNo")
                .roStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .roEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .repairComTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueCloTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .roType("roType")
                .roAmount("roAmount")
                .missParts(0)
                .noRepair(0)
                .inviteOvertime(0)
                .forecastTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .highlightFlag(0)
                .build();
        when(mockTtFaultLightClueMapper.selectById(0L)).thenReturn(ttFaultLightCluePO);

        // Run the test
        final List<FaultLightParamDTO> result = faultLightFollowServiceImplUnderTest.queryFollowSpinner(0L);

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryFollowRecord() {
        // Setup
        final FaultLightFollowRecordDTO dto = new FaultLightFollowRecordDTO();
        dto.setClueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setClueDisTimeStr("clueDisTimeStr");
        dto.setFollowTimeStr("followTimeStr");
        dto.setClueStatus(0);
        dto.setClueStatusStr("clueStatusStr");
        dto.setFollowStatus(0);
        dto.setFollowStatusStr("followStatusStr");

        final FaultLightFollowRecordDTO faultLightFollowRecordDTO = new FaultLightFollowRecordDTO();
        faultLightFollowRecordDTO.setClueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        faultLightFollowRecordDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        faultLightFollowRecordDTO.setClueDisTimeStr("clueDisTimeStr");
        faultLightFollowRecordDTO.setFollowTimeStr("followTimeStr");
        faultLightFollowRecordDTO.setClueStatus(0);
        faultLightFollowRecordDTO.setClueStatusStr("clueStatusStr");
        faultLightFollowRecordDTO.setFollowStatus(0);
        faultLightFollowRecordDTO.setFollowStatusStr("followStatusStr");
        final List<FaultLightFollowRecordDTO> expectedResult = Arrays.asList(faultLightFollowRecordDTO);

        // Configure TtFaultLightFollowRecordMapper.queryFollowRecord(...).
        final FaultLightFollowRecordDTO faultLightFollowRecordDTO1 = new FaultLightFollowRecordDTO();
        faultLightFollowRecordDTO1.setClueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        faultLightFollowRecordDTO1.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        faultLightFollowRecordDTO1.setClueDisTimeStr("clueDisTimeStr");
        faultLightFollowRecordDTO1.setFollowTimeStr("followTimeStr");
        faultLightFollowRecordDTO1.setClueStatus(0);
        faultLightFollowRecordDTO1.setClueStatusStr("clueStatusStr");
        faultLightFollowRecordDTO1.setFollowStatus(0);
        faultLightFollowRecordDTO1.setFollowStatusStr("followStatusStr");
        final List<FaultLightFollowRecordDTO> faultLightFollowRecordDTOS = Arrays.asList(faultLightFollowRecordDTO1);
        final FaultLightFollowRecordDTO params = new FaultLightFollowRecordDTO();
        params.setClueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        params.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        params.setClueDisTimeStr("clueDisTimeStr");
        params.setFollowTimeStr("followTimeStr");
        params.setClueStatus(0);
        params.setClueStatusStr("clueStatusStr");
        params.setFollowStatus(0);
        params.setFollowStatusStr("followStatusStr");
        when(mockTtFaultLightFollowRecordMapper.queryFollowRecord(params)).thenReturn(faultLightFollowRecordDTOS);

        // Run the test
        final List<FaultLightFollowRecordDTO> result = faultLightFollowServiceImplUnderTest.queryFollowRecord(dto);

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryFollowRecord_TtFaultLightFollowRecordMapperReturnsNoItems() {
        // Setup
        final FaultLightFollowRecordDTO dto = new FaultLightFollowRecordDTO();
        dto.setClueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setClueDisTimeStr("clueDisTimeStr");
        dto.setFollowTimeStr("followTimeStr");
        dto.setClueStatus(0);
        dto.setClueStatusStr("clueStatusStr");
        dto.setFollowStatus(0);
        dto.setFollowStatusStr("followStatusStr");

        // Configure TtFaultLightFollowRecordMapper.queryFollowRecord(...).
        final FaultLightFollowRecordDTO params = new FaultLightFollowRecordDTO();
        params.setClueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        params.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        params.setClueDisTimeStr("clueDisTimeStr");
        params.setFollowTimeStr("followTimeStr");
        params.setClueStatus(0);
        params.setClueStatusStr("clueStatusStr");
        params.setFollowStatus(0);
        params.setFollowStatusStr("followStatusStr");
        when(mockTtFaultLightFollowRecordMapper.queryFollowRecord(params)).thenReturn(Collections.emptyList());

        // Run the test
        final List<FaultLightFollowRecordDTO> result = faultLightFollowServiceImplUnderTest.queryFollowRecord(dto);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testQueryFaultLightFollowDailyMileIsEmpty() {
        // Setup
        final FaultLightFollowDTO expectedResult = new FaultLightFollowDTO();
        expectedResult.setGender("女士");
        expectedResult.setPlateNumber("plateNumber");
        expectedResult.setVin("vin");
        expectedResult.setModel("model");
        expectedResult.setDailyAverageMileage("dailyAverageMileage");
        expectedResult.setComments("comments");
        expectedResult.setDisplayMile("dailyAverageMileage");

        // Configure TtFaultLightClueMapper.selectFaultLightFollow(...).
        final FaultLightFollowDTO faultLightFollowDTO = new FaultLightFollowDTO();
        faultLightFollowDTO.setGender("100500002");
        faultLightFollowDTO.setPlateNumber("plateNumber");
        faultLightFollowDTO.setVin("vin");
        faultLightFollowDTO.setModel("model");
        faultLightFollowDTO.setDailyAverageMileage("dailyAverageMileage");
        faultLightFollowDTO.setComments("comments");
        faultLightFollowDTO.setDisplayMile("dailyAverageMileage");
        when(mockTtFaultLightClueMapper.selectFaultLightFollow(0L)).thenReturn(faultLightFollowDTO);

        // Configure BusinessPlatformService.getVehicleByVIN(...).
        final TmVehicleDTO tmVehicleDTO = new TmVehicleDTO();
        tmVehicleDTO.setId(0L);
        tmVehicleDTO.setCustomerId(0L);
        tmVehicleDTO.setCompanyCode("companyCode");
        tmVehicleDTO.setPlateNumber("plateNumber");
        tmVehicleDTO.setModelName("model");
        when(mockBusinessPlatformService.getVehicleByVIN("vin")).thenReturn(tmVehicleDTO);

        // Run the test
        final FaultLightFollowDTO result = faultLightFollowServiceImplUnderTest.queryFaultLightFollow(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryFaultLightFollowDailyMileIsNotEmpty() {
        // Setup
        final FaultLightFollowDTO expectedResult = new FaultLightFollowDTO();
        expectedResult.setGender("女士");
        expectedResult.setPlateNumber("plateNumber");
        expectedResult.setVin("vin");
        expectedResult.setModel("model");
        expectedResult.setDailyAverageMileage("dailyAverageMileage");
        expectedResult.setComments("comments");
        expectedResult.setDailyMile("dailyMile");
        expectedResult.setDisplayMile("dailyMile");

        final FaultLightFollowDTO faultLightFollowDTO = new FaultLightFollowDTO();
        faultLightFollowDTO.setGender("100500002");
        faultLightFollowDTO.setPlateNumber("plateNumber");
        faultLightFollowDTO.setVin("vin");
        faultLightFollowDTO.setModel("model");
        faultLightFollowDTO.setDailyAverageMileage("dailyAverageMileage");
        faultLightFollowDTO.setComments("comments");
        faultLightFollowDTO.setDailyMile("dailyMile");
        faultLightFollowDTO.setDisplayMile("dailyAverageMileage");
        when(mockTtFaultLightClueMapper.selectFaultLightFollow(0L)).thenReturn(faultLightFollowDTO);

        // Configure BusinessPlatformService.getVehicleByVIN(...).
        final TmVehicleDTO tmVehicleDTO = new TmVehicleDTO();
        tmVehicleDTO.setId(0L);
        tmVehicleDTO.setCustomerId(0L);
        tmVehicleDTO.setCompanyCode("companyCode");
        tmVehicleDTO.setPlateNumber("plateNumber");
        tmVehicleDTO.setModelName("model");
        when(mockBusinessPlatformService.getVehicleByVIN("vin")).thenReturn(tmVehicleDTO);

        // Run the test
        final FaultLightFollowDTO result = faultLightFollowServiceImplUnderTest.queryFaultLightFollow(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFaultLightFollowDTO() {
        FaultLightFollowDTO faultLightFollowDTO = new FaultLightFollowDTO();
        faultLightFollowDTO.setDailyMile("dailyMile");
        faultLightFollowDTO.setDisplayMile("displayMile");
        assertThat(faultLightFollowDTO.getDailyMile()).isEqualTo("dailyMile");
        assertThat(faultLightFollowDTO.getDisplayMile()).isEqualTo("displayMile");
    }
}
