package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintAssistDepartmentMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.*;
import com.yonyou.dmscus.customer.entity.dto.middleground.AppPushDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistDepartmentPO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAssistPO;
import com.yonyou.dmscus.customer.service.impl.CommonServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintAssistDepartmentServiceImplTest {

    @Mock
    private ComplaintAssistDepartmentMapper mockComplaintAssistDepartmentMapper;
    @Mock
    private CommonServiceImpl mockCommonService;

    private ComplaintAssistDepartmentServiceImpl complaintAssistDepartmentServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        complaintAssistDepartmentServiceImplUnderTest = new ComplaintAssistDepartmentServiceImpl();
        complaintAssistDepartmentServiceImplUnderTest.complaintAssistDepartmentMapper = mockComplaintAssistDepartmentMapper;
        complaintAssistDepartmentServiceImplUnderTest.commonService = mockCommonService;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO = new ComplaintAssistDepartmentDTO();
        complaintAssistDepartmentDTO.setAppId("appId");
        complaintAssistDepartmentDTO.setOwnerCode("ownerCode");
        complaintAssistDepartmentDTO.setComplaintInfoId(0L);
        complaintAssistDepartmentDTO.setAssistDepartment("assistDepartment");
        complaintAssistDepartmentDTO.setHopeReplyTime(0);

        // Configure ComplaintAssistDepartmentMapper.selectPageBySql(...).
        final ComplaintAssistDepartmentPO complaintAssistDepartmentPO = new ComplaintAssistDepartmentPO();
        complaintAssistDepartmentPO.setOwnerCode("ownerCode");
        complaintAssistDepartmentPO.setOrgId(0);
        complaintAssistDepartmentPO.setId(0L);
        complaintAssistDepartmentPO.setComplaintInfoId(0L);
        complaintAssistDepartmentPO.setObject("object");
        final List<ComplaintAssistDepartmentPO> complaintAssistDepartmentPOS = Arrays.asList(
                complaintAssistDepartmentPO);
        when(mockComplaintAssistDepartmentMapper.selectPageBySql(any(Page.class),
                any(ComplaintAssistDepartmentPO.class))).thenReturn(complaintAssistDepartmentPOS);

        // Run the test
        final IPage<ComplaintAssistDepartmentDTO> result = complaintAssistDepartmentServiceImplUnderTest.selectPageBysql(
                page, complaintAssistDepartmentDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintAssistDepartmentMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO = new ComplaintAssistDepartmentDTO();
        complaintAssistDepartmentDTO.setAppId("appId");
        complaintAssistDepartmentDTO.setOwnerCode("ownerCode");
        complaintAssistDepartmentDTO.setComplaintInfoId(0L);
        complaintAssistDepartmentDTO.setAssistDepartment("assistDepartment");
        complaintAssistDepartmentDTO.setHopeReplyTime(0);

        when(mockComplaintAssistDepartmentMapper.selectPageBySql(any(Page.class),
                any(ComplaintAssistDepartmentPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintAssistDepartmentDTO> result = complaintAssistDepartmentServiceImplUnderTest.selectPageBysql(
                page, complaintAssistDepartmentDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO = new ComplaintAssistDepartmentDTO();
        complaintAssistDepartmentDTO.setAppId("appId");
        complaintAssistDepartmentDTO.setOwnerCode("ownerCode");
        complaintAssistDepartmentDTO.setComplaintInfoId(0L);
        complaintAssistDepartmentDTO.setAssistDepartment("assistDepartment");
        complaintAssistDepartmentDTO.setHopeReplyTime(0);

        // Configure ComplaintAssistDepartmentMapper.selectListBySql(...).
        final ComplaintAssistDepartmentPO complaintAssistDepartmentPO = new ComplaintAssistDepartmentPO();
        complaintAssistDepartmentPO.setOwnerCode("ownerCode");
        complaintAssistDepartmentPO.setOrgId(0);
        complaintAssistDepartmentPO.setId(0L);
        complaintAssistDepartmentPO.setComplaintInfoId(0L);
        complaintAssistDepartmentPO.setObject("object");
        final List<ComplaintAssistDepartmentPO> complaintAssistDepartmentPOS = Arrays.asList(
                complaintAssistDepartmentPO);
        when(mockComplaintAssistDepartmentMapper.selectListBySql(any(ComplaintAssistDepartmentPO.class)))
                .thenReturn(complaintAssistDepartmentPOS);

        // Run the test
        final List<ComplaintAssistDepartmentDTO> result = complaintAssistDepartmentServiceImplUnderTest.selectListBySql(
                complaintAssistDepartmentDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintAssistDepartmentMapperReturnsNoItems() {
        // Setup
        final ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO = new ComplaintAssistDepartmentDTO();
        complaintAssistDepartmentDTO.setAppId("appId");
        complaintAssistDepartmentDTO.setOwnerCode("ownerCode");
        complaintAssistDepartmentDTO.setComplaintInfoId(0L);
        complaintAssistDepartmentDTO.setAssistDepartment("assistDepartment");
        complaintAssistDepartmentDTO.setHopeReplyTime(0);

        when(mockComplaintAssistDepartmentMapper.selectListBySql(any(ComplaintAssistDepartmentPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintAssistDepartmentDTO> result = complaintAssistDepartmentServiceImplUnderTest.selectListBySql(
                complaintAssistDepartmentDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure ComplaintAssistDepartmentMapper.selectById(...).
        final ComplaintAssistDepartmentPO complaintAssistDepartmentPO = new ComplaintAssistDepartmentPO();
        complaintAssistDepartmentPO.setOwnerCode("ownerCode");
        complaintAssistDepartmentPO.setOrgId(0);
        complaintAssistDepartmentPO.setId(0L);
        complaintAssistDepartmentPO.setComplaintInfoId(0L);
        complaintAssistDepartmentPO.setObject("object");
        when(mockComplaintAssistDepartmentMapper.selectById(0L)).thenReturn(complaintAssistDepartmentPO);

        // Run the test
        final ComplaintAssistDepartmentDTO result = complaintAssistDepartmentServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintAssistDepartmentMapperReturnsNull() {
        // Setup
        when(mockComplaintAssistDepartmentMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintAssistDepartmentServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO = new ComplaintAssistDepartmentDTO();
        complaintAssistDepartmentDTO.setAppId("appId");
        complaintAssistDepartmentDTO.setOwnerCode("ownerCode");
        complaintAssistDepartmentDTO.setComplaintInfoId(0L);
        complaintAssistDepartmentDTO.setAssistDepartment("assistDepartment");
        complaintAssistDepartmentDTO.setHopeReplyTime(0);

        when(mockComplaintAssistDepartmentMapper.insert(any(ComplaintAssistDepartmentPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintAssistDepartmentServiceImplUnderTest.insert(complaintAssistDepartmentDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO = new ComplaintAssistDepartmentDTO();
        complaintAssistDepartmentDTO.setAppId("appId");
        complaintAssistDepartmentDTO.setOwnerCode("ownerCode");
        complaintAssistDepartmentDTO.setComplaintInfoId(0L);
        complaintAssistDepartmentDTO.setAssistDepartment("assistDepartment");
        complaintAssistDepartmentDTO.setHopeReplyTime(0);

        // Configure ComplaintAssistDepartmentMapper.selectById(...).
        final ComplaintAssistDepartmentPO complaintAssistDepartmentPO = new ComplaintAssistDepartmentPO();
        complaintAssistDepartmentPO.setOwnerCode("ownerCode");
        complaintAssistDepartmentPO.setOrgId(0);
        complaintAssistDepartmentPO.setId(0L);
        complaintAssistDepartmentPO.setComplaintInfoId(0L);
        complaintAssistDepartmentPO.setObject("object");
        when(mockComplaintAssistDepartmentMapper.selectById(0L)).thenReturn(complaintAssistDepartmentPO);

        when(mockComplaintAssistDepartmentMapper.updateById(any(ComplaintAssistDepartmentPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintAssistDepartmentServiceImplUnderTest.update(0L, complaintAssistDepartmentDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectAssistByid() {
        // Setup
        final ComplaintAssistDTO complaintAssistDTO = new ComplaintAssistDTO();
        complaintAssistDTO.setAppId("appId");
        complaintAssistDTO.setOwnerCode("ownerCode");
        complaintAssistDTO.setOwnerParCode("ownerParCode");
        complaintAssistDTO.setOrgId(0);
        complaintAssistDTO.setId(0L);

        // Configure ComplaintAssistDepartmentMapper.selectAssistByid(...).
        final ComplaintAssistPO complaintAssistPO = new ComplaintAssistPO();
        complaintAssistPO.setFollowerName("followerName");
        complaintAssistPO.setOwnerCode("ownerCode");
        complaintAssistPO.setOrgId(0);
        complaintAssistPO.setId(0L);
        complaintAssistPO.setComplaintInfoId(0L);
        final List<ComplaintAssistPO> complaintAssistPOS = Arrays.asList(complaintAssistPO);
        when(mockComplaintAssistDepartmentMapper.selectAssistByid(any(ComplaintAssistPO.class)))
                .thenReturn(complaintAssistPOS);

        // Run the test
        final List<ComplaintAssistDTO> result = complaintAssistDepartmentServiceImplUnderTest.selectAssistByid(
                complaintAssistDTO);

        // Verify the results
    }

    @Test
    void testSelectAssistByid_ComplaintAssistDepartmentMapperReturnsNoItems() {
        // Setup
        final ComplaintAssistDTO complaintAssistDTO = new ComplaintAssistDTO();
        complaintAssistDTO.setAppId("appId");
        complaintAssistDTO.setOwnerCode("ownerCode");
        complaintAssistDTO.setOwnerParCode("ownerParCode");
        complaintAssistDTO.setOrgId(0);
        complaintAssistDTO.setId(0L);

        when(mockComplaintAssistDepartmentMapper.selectAssistByid(any(ComplaintAssistPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintAssistDTO> result = complaintAssistDepartmentServiceImplUnderTest.selectAssistByid(
                complaintAssistDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testUpdatestatus() {
        // Setup
        // Run the test
        complaintAssistDepartmentServiceImplUnderTest.updatestatus(0L);

        // Verify the results
        verify(mockComplaintAssistDepartmentMapper).updatestatus(0L);
    }

    @Test
    void testUpdatestatus1() {
        // Setup
        // Run the test
        complaintAssistDepartmentServiceImplUnderTest.updatestatus1(0L);

        // Verify the results
        verify(mockComplaintAssistDepartmentMapper).updatestatus1(0L);
    }

    @Test
    void testSelectAssistValidByid() {
        // Setup
        final ComplaintAssistDTO complaintAssistDTO = new ComplaintAssistDTO();
        complaintAssistDTO.setAppId("appId");
        complaintAssistDTO.setOwnerCode("ownerCode");
        complaintAssistDTO.setOwnerParCode("ownerParCode");
        complaintAssistDTO.setOrgId(0);
        complaintAssistDTO.setId(0L);

        // Configure ComplaintAssistDepartmentMapper.selectAssistValidByid(...).
        final ComplaintAssistPO complaintAssistPO = new ComplaintAssistPO();
        complaintAssistPO.setFollowerName("followerName");
        complaintAssistPO.setOwnerCode("ownerCode");
        complaintAssistPO.setOrgId(0);
        complaintAssistPO.setId(0L);
        complaintAssistPO.setComplaintInfoId(0L);
        final List<ComplaintAssistPO> complaintAssistPOS = Arrays.asList(complaintAssistPO);
        when(mockComplaintAssistDepartmentMapper.selectAssistValidByid(any(ComplaintAssistPO.class)))
                .thenReturn(complaintAssistPOS);

        // Run the test
        final List<ComplaintAssistDTO> result = complaintAssistDepartmentServiceImplUnderTest.selectAssistValidByid(
                complaintAssistDTO);

        // Verify the results
    }

    @Test
    void testSelectAssistValidByid_ComplaintAssistDepartmentMapperReturnsNoItems() {
        // Setup
        final ComplaintAssistDTO complaintAssistDTO = new ComplaintAssistDTO();
        complaintAssistDTO.setAppId("appId");
        complaintAssistDTO.setOwnerCode("ownerCode");
        complaintAssistDTO.setOwnerParCode("ownerParCode");
        complaintAssistDTO.setOrgId(0);
        complaintAssistDTO.setId(0L);

        when(mockComplaintAssistDepartmentMapper.selectAssistValidByid(any(ComplaintAssistPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintAssistDTO> result = complaintAssistDepartmentServiceImplUnderTest.selectAssistValidByid(
                complaintAssistDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectAssistList() {
        // Setup
        final ComplaintAssistDTO complaintAssistDTO = new ComplaintAssistDTO();
        complaintAssistDTO.setAppId("appId");
        complaintAssistDTO.setOwnerCode("ownerCode");
        complaintAssistDTO.setOwnerParCode("ownerParCode");
        complaintAssistDTO.setOrgId(0);
        complaintAssistDTO.setId(0L);

        // Configure ComplaintAssistDepartmentMapper.selectAssistList(...).
        final ComplaintAssistDepartmentDTO complaintAssistDepartmentDTO = new ComplaintAssistDepartmentDTO();
        complaintAssistDepartmentDTO.setAppId("appId");
        complaintAssistDepartmentDTO.setOwnerCode("ownerCode");
        complaintAssistDepartmentDTO.setComplaintInfoId(0L);
        complaintAssistDepartmentDTO.setAssistDepartment("assistDepartment");
        complaintAssistDepartmentDTO.setHopeReplyTime(0);
        final List<ComplaintAssistDepartmentDTO> complaintAssistDepartmentDTOS = Arrays.asList(
                complaintAssistDepartmentDTO);
        when(mockComplaintAssistDepartmentMapper.selectAssistList(any(ComplaintAssistDTO.class)))
                .thenReturn(complaintAssistDepartmentDTOS);

        // Run the test
        final List<ComplaintAssistDepartmentDTO> result = complaintAssistDepartmentServiceImplUnderTest.selectAssistList(
                complaintAssistDTO);

        // Verify the results
    }

    @Test
    void testSelectAssistList_ComplaintAssistDepartmentMapperReturnsNoItems() {
        // Setup
        final ComplaintAssistDTO complaintAssistDTO = new ComplaintAssistDTO();
        complaintAssistDTO.setAppId("appId");
        complaintAssistDTO.setOwnerCode("ownerCode");
        complaintAssistDTO.setOwnerParCode("ownerParCode");
        complaintAssistDTO.setOrgId(0);
        complaintAssistDTO.setId(0L);

        when(mockComplaintAssistDepartmentMapper.selectAssistList(any(ComplaintAssistDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintAssistDepartmentDTO> result = complaintAssistDepartmentServiceImplUnderTest.selectAssistList(
                complaintAssistDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSetAssistDepartmentNotEidt() {
        // Setup
        // Run the test
        complaintAssistDepartmentServiceImplUnderTest.setAssistDepartmentNotEidt(0L);

        // Verify the results
        verify(mockComplaintAssistDepartmentMapper).setAssistDepartmentNotEidt(0L);
    }

    @Test
    void testSelectAssist() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintAssistDTO complaintAssistDTO = new ComplaintAssistDTO();
        complaintAssistDTO.setAppId("appId");
        complaintAssistDTO.setOwnerCode("ownerCode");
        complaintAssistDTO.setOwnerParCode("ownerParCode");
        complaintAssistDTO.setOrgId(0);
        complaintAssistDTO.setId(0L);

        // Configure ComplaintAssistDepartmentMapper.selectAssist(...).
        final ComplaintAssistPO complaintAssistPO = new ComplaintAssistPO();
        complaintAssistPO.setFollowerName("followerName");
        complaintAssistPO.setOwnerCode("ownerCode");
        complaintAssistPO.setOrgId(0);
        complaintAssistPO.setId(0L);
        complaintAssistPO.setComplaintInfoId(0L);
        final List<ComplaintAssistPO> complaintAssistPOS = Arrays.asList(complaintAssistPO);
        when(mockComplaintAssistDepartmentMapper.selectAssist(any(Page.class),
                any(ComplaintAssistPO.class))).thenReturn(complaintAssistPOS);

        // Run the test
        final IPage<ComplaintAssistDTO> result = complaintAssistDepartmentServiceImplUnderTest.selectAssist(page,
                complaintAssistDTO);

        // Verify the results
    }

    @Test
    void testSelectAssist_ComplaintAssistDepartmentMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintAssistDTO complaintAssistDTO = new ComplaintAssistDTO();
        complaintAssistDTO.setAppId("appId");
        complaintAssistDTO.setOwnerCode("ownerCode");
        complaintAssistDTO.setOwnerParCode("ownerParCode");
        complaintAssistDTO.setOrgId(0);
        complaintAssistDTO.setId(0L);

        when(mockComplaintAssistDepartmentMapper.selectAssist(any(Page.class),
                any(ComplaintAssistPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintAssistDTO> result = complaintAssistDepartmentServiceImplUnderTest.selectAssist(page,
                complaintAssistDTO);

        // Verify the results
    }
}
