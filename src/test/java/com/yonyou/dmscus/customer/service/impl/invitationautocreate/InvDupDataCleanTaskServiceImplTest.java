package com.yonyou.dmscus.customer.service.impl.invitationautocreate;

import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.feign.MidEndCustomerCenterClient;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.feign.vo.InvDataCleanVO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.dto.VehicleOwnerDetailByIdListDTO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.dto.VehicleOwnerSelectByIdListDTO;
import com.yonyou.dmscus.customer.service.invitationautocreate.IncRepCleanDataService;
import com.yonyou.dmscus.customer.service.invitationautocreate.po.InviteVehiclePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InvDupDataCleanTaskServiceImplTest {

    @Mock
    private ReportCommonClient mockReportCommonClient;
    @Mock
    private MidEndCustomerCenterClient mockMidEndCustomerCenterClient;
    @Mock
    private IncRepCleanDataService mockIncRepCleanDataService;

    private InvDupDataCleanTaskServiceImpl invDupDataCleanTaskServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        invDupDataCleanTaskServiceImplUnderTest = new InvDupDataCleanTaskServiceImpl();
        invDupDataCleanTaskServiceImplUnderTest.reportCommonClient = mockReportCommonClient;
        invDupDataCleanTaskServiceImplUnderTest.midEndCustomerCenterClient = mockMidEndCustomerCenterClient;
        invDupDataCleanTaskServiceImplUnderTest.incRepCleanDataService = mockIncRepCleanDataService;
    }

    @Test
    void testGetDetailedAge() {
//        assertThat(InvDupDataCleanTaskServiceImpl.getDetailedAge(LocalDate.of(2020, 1, 1))).isEqualTo("result");
    }

    @Test
    void testDoIncRepTaskClean() {
        // Setup
        // Configure ReportCommonClient.queryIncRepTask(...).
        final InvDataCleanVO invDataCleanVO = new InvDataCleanVO();
        invDataCleanVO.setId(0L);
        invDataCleanVO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        invDataCleanVO.setVin("vin");
        invDataCleanVO.setDealerCode("dealerCode");
        invDataCleanVO.setInviteType(0);
        invDataCleanVO.setCustomerId(0L);
        invDataCleanVO.setTotalScore(0);
        invDataCleanVO.setFollowStatus(0);
        final List<InvDataCleanVO> invDataCleanVOS = Arrays.asList(invDataCleanVO);
        when(mockReportCommonClient.queryIncRepTask()).thenReturn(invDataCleanVOS);

        // Configure MidEndCustomerCenterClient.selectVehicleOwnerByOneIdList(...).
        final ResponseDTO<List<VehicleOwnerDetailByIdListDTO>> listResponseDTO = new ResponseDTO<>();
        final VehicleOwnerDetailByIdListDTO vehicleOwnerDetailByIdListDTO = new VehicleOwnerDetailByIdListDTO();
        vehicleOwnerDetailByIdListDTO.setOneId(0L);
        vehicleOwnerDetailByIdListDTO.setName("name");
        vehicleOwnerDetailByIdListDTO.setMobile("tel");
        vehicleOwnerDetailByIdListDTO.setGender(0);
        vehicleOwnerDetailByIdListDTO.setBirthday(LocalDate.of(2020, 1, 1));
        listResponseDTO.setData(Arrays.asList(vehicleOwnerDetailByIdListDTO));
        when(mockMidEndCustomerCenterClient.selectVehicleOwnerByOneIdList(
                any(VehicleOwnerSelectByIdListDTO.class))).thenReturn(listResponseDTO);

        // Run the test
        invDupDataCleanTaskServiceImplUnderTest.doIncRepTaskClean();

        // Verify the results
        // Confirm IncRepCleanDataService.doTaskClean(...).
        final InviteVehiclePO inviteVehiclePO = new InviteVehiclePO();
        inviteVehiclePO.setId(0L);
        inviteVehiclePO.setName("name");
        inviteVehiclePO.setTel("tel");
        inviteVehiclePO.setAge("age");
        inviteVehiclePO.setSex("sex");
        final List<InviteVehiclePO> cloClue = Arrays.asList(inviteVehiclePO);
        final InviteVehiclePO inviteVehiclePO1 = new InviteVehiclePO();
        inviteVehiclePO1.setId(0L);
        inviteVehiclePO1.setName("name");
        inviteVehiclePO1.setTel("tel");
        inviteVehiclePO1.setAge("age");
        inviteVehiclePO1.setSex("sex");
        final List<InviteVehiclePO> upClue = Arrays.asList(inviteVehiclePO1);
//        verify(mockIncRepCleanDataService)
        mockIncRepCleanDataService.doTaskClean(cloClue, upClue);

        // Confirm IncRepCleanDataService.doClueClean(...).
        final InviteVehiclePO inviteVehiclePO2 = new InviteVehiclePO();
        inviteVehiclePO2.setId(0L);
        inviteVehiclePO2.setName("name");
        inviteVehiclePO2.setTel("tel");
        inviteVehiclePO2.setAge("age");
        inviteVehiclePO2.setSex("sex");
        final List<InviteVehiclePO> cloClue1 = Arrays.asList(inviteVehiclePO2);
        final InviteVehiclePO inviteVehiclePO3 = new InviteVehiclePO();
        inviteVehiclePO3.setId(0L);
        inviteVehiclePO3.setName("name");
        inviteVehiclePO3.setTel("tel");
        inviteVehiclePO3.setAge("age");
        inviteVehiclePO3.setSex("sex");
        final List<InviteVehiclePO> upClue1 = Arrays.asList(inviteVehiclePO3);
        verify(mockIncRepCleanDataService).doClueClean(cloClue1, upClue1);
    }

    @Test
    void testDoIncRepTaskClean_ReportCommonClientReturnsNoItems() {
        // Setup
        when(mockReportCommonClient.queryIncRepTask()).thenReturn(Collections.emptyList());

        // Run the test
        invDupDataCleanTaskServiceImplUnderTest.doIncRepTaskClean();

        // Verify the results
    }

    @Test
    void testDoIncRepClueClean() {
        // Setup
        // Configure ReportCommonClient.queryIncClueTask(...).
        final InvDataCleanVO invDataCleanVO = new InvDataCleanVO();
        invDataCleanVO.setId(0L);
        invDataCleanVO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        invDataCleanVO.setVin("vin");
        invDataCleanVO.setDealerCode("dealerCode");
        invDataCleanVO.setInviteType(0);
        invDataCleanVO.setCustomerId(0L);
        invDataCleanVO.setTotalScore(0);
        invDataCleanVO.setFollowStatus(0);
        final List<InvDataCleanVO> invDataCleanVOS = Arrays.asList(invDataCleanVO);
        when(mockReportCommonClient.queryIncClueTask()).thenReturn(invDataCleanVOS);

        // Configure MidEndCustomerCenterClient.selectVehicleOwnerByOneIdList(...).
        final ResponseDTO<List<VehicleOwnerDetailByIdListDTO>> listResponseDTO = new ResponseDTO<>();
        final VehicleOwnerDetailByIdListDTO vehicleOwnerDetailByIdListDTO = new VehicleOwnerDetailByIdListDTO();
        vehicleOwnerDetailByIdListDTO.setOneId(0L);
        vehicleOwnerDetailByIdListDTO.setName("name");
        vehicleOwnerDetailByIdListDTO.setMobile("tel");
        vehicleOwnerDetailByIdListDTO.setGender(0);
        vehicleOwnerDetailByIdListDTO.setBirthday(LocalDate.of(2020, 1, 1));
        listResponseDTO.setData(Arrays.asList(vehicleOwnerDetailByIdListDTO));
        when(mockMidEndCustomerCenterClient.selectVehicleOwnerByOneIdList(
                any(VehicleOwnerSelectByIdListDTO.class))).thenReturn(listResponseDTO);

        // Run the test
        invDupDataCleanTaskServiceImplUnderTest.doIncRepClueClean();

        // Verify the results
        // Confirm IncRepCleanDataService.doTaskClean(...).
        final InviteVehiclePO inviteVehiclePO = new InviteVehiclePO();
        inviteVehiclePO.setId(0L);
        inviteVehiclePO.setName("name");
        inviteVehiclePO.setTel("tel");
        inviteVehiclePO.setAge("age");
        inviteVehiclePO.setSex("sex");
        final List<InviteVehiclePO> cloClue = Arrays.asList(inviteVehiclePO);
        final InviteVehiclePO inviteVehiclePO1 = new InviteVehiclePO();
        inviteVehiclePO1.setId(0L);
        inviteVehiclePO1.setName("name");
        inviteVehiclePO1.setTel("tel");
        inviteVehiclePO1.setAge("age");
        inviteVehiclePO1.setSex("sex");
        final List<InviteVehiclePO> upClue = Arrays.asList(inviteVehiclePO1);
//        verify(mockIncRepCleanDataService)
        mockIncRepCleanDataService.doTaskClean(cloClue, upClue);

        // Confirm IncRepCleanDataService.doClueClean(...).
        final InviteVehiclePO inviteVehiclePO2 = new InviteVehiclePO();
        inviteVehiclePO2.setId(0L);
        inviteVehiclePO2.setName("name");
        inviteVehiclePO2.setTel("tel");
        inviteVehiclePO2.setAge("age");
        inviteVehiclePO2.setSex("sex");
        final List<InviteVehiclePO> cloClue1 = Arrays.asList(inviteVehiclePO2);
        final InviteVehiclePO inviteVehiclePO3 = new InviteVehiclePO();
        inviteVehiclePO3.setId(0L);
        inviteVehiclePO3.setName("name");
        inviteVehiclePO3.setTel("tel");
        inviteVehiclePO3.setAge("age");
        inviteVehiclePO3.setSex("sex");
        final List<InviteVehiclePO> upClue1 = Arrays.asList(inviteVehiclePO3);
        verify(mockIncRepCleanDataService).doClueClean(cloClue1, upClue1);
    }

    @Test
    void testDoIncRepClueClean_ReportCommonClientReturnsNoItems() {
        // Setup
        when(mockReportCommonClient.queryIncClueTask()).thenReturn(Collections.emptyList());

        // Run the test
        invDupDataCleanTaskServiceImplUnderTest.doIncRepClueClean();

        // Verify the results
    }
}
