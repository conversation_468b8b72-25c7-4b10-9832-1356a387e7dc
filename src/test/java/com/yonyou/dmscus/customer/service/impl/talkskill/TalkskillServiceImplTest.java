package com.yonyou.dmscus.customer.service.impl.talkskill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.talkskill.*;
import com.yonyou.dmscus.customer.dto.OrgInfoDTO;
import com.yonyou.dmscus.customer.dto.OrgSearchParams;
import com.yonyou.dmscus.customer.entity.dto.talkskill.TalkskillDTO;
import com.yonyou.dmscus.customer.entity.po.talkskill.*;
import com.yonyou.dmscus.customer.service.CommonService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TalkskillServiceImplTest {

    @Mock
    private TalkskillMapper mockTalkskillMapper;
    @Mock
    private TalkskillKeywordMapper mockTalkskillKeywordMapper;
    @Mock
    private TalkskillKeywordLibMapper mockTalkskillKeywordLibMapper;
    @Mock
    private TalkskillTypeMapper mockTalkskillTypeMapper;
    @Mock
    private TalkskillTagMapper mockTalkskillTagMapper;
    @Mock
    private CommonService mockCommonService;

    private TalkskillServiceImpl talkskillServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        talkskillServiceImplUnderTest = new TalkskillServiceImpl();
        talkskillServiceImplUnderTest.talkskillMapper = mockTalkskillMapper;
        talkskillServiceImplUnderTest.talkskillKeywordMapper = mockTalkskillKeywordMapper;
        talkskillServiceImplUnderTest.talkskillKeywordLibMapper = mockTalkskillKeywordLibMapper;
        talkskillServiceImplUnderTest.talkskillTypeMapper = mockTalkskillTypeMapper;
        talkskillServiceImplUnderTest.talkskillTagMapper = mockTalkskillTagMapper;
        talkskillServiceImplUnderTest.commonService = mockCommonService;
    }

    @Test
    void testGetById_TalkskillMapperReturnsNull() {
        // Setup
        when(mockTalkskillMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> talkskillServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testQueryTalkskill1_TalkskillMapperReturnsNoItems() {
        // Setup
        when(mockTalkskillMapper.queryTalkskill("dealerCode", "type", "name")).thenReturn(Collections.emptyList());

        // Run the test
        final List<TalkskillDTO> result = talkskillServiceImplUnderTest.queryTalkskill("dealerCode", "type", "name");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testQueryTalkskill2_TalkskillMapperReturnsNoItems() {
        // Setup
        when(mockTalkskillMapper.queryTalkskill1("dealerCode", "keyword")).thenReturn(Collections.emptyList());

        // Run the test
        final List<TalkskillDTO> result = talkskillServiceImplUnderTest.queryTalkskill("dealerCode", "keyword");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetQwimTalkskills_TalkskillTagMapperReturnsNoItems() {
        // Setup
        // Configure TalkskillTypeMapper.selectOne(...).
        final TalkskillTypePO talkskillTypePO = new TalkskillTypePO();
        talkskillTypePO.setTypeId(0L);
        talkskillTypePO.setTypeCode("typeCode");
        talkskillTypePO.setTypeName("typeName");
        talkskillTypePO.setIsDeleted(0);
        talkskillTypePO.setIsValid(0);
        when(mockTalkskillTypeMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(talkskillTypePO);

        when(mockTalkskillTagMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<TalkskillDTO> result = talkskillServiceImplUnderTest.getQwimTalkskills("dealerCode",
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetQwimTalkskills_TalkskillMapperReturnsNoItems() {
        // Setup
        // Configure TalkskillTypeMapper.selectOne(...).
        final TalkskillTypePO talkskillTypePO = new TalkskillTypePO();
        talkskillTypePO.setTypeId(0L);
        talkskillTypePO.setTypeCode("typeCode");
        talkskillTypePO.setTypeName("typeName");
        talkskillTypePO.setIsDeleted(0);
        talkskillTypePO.setIsValid(0);
        when(mockTalkskillTypeMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(talkskillTypePO);

        // Configure TalkskillTagMapper.selectList(...).
        final TalkskillTagPO talkskillTagPO = new TalkskillTagPO();
        talkskillTagPO.setTagId(0L);
        talkskillTagPO.setParentId(0);
        talkskillTagPO.setTagCode("tagCode");
        talkskillTagPO.setIsDeleted(0);
        talkskillTagPO.setIsValid(0);
        final List<TalkskillTagPO> talkskillTagPOS = Arrays.asList(talkskillTagPO);
        when(mockTalkskillTagMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(talkskillTagPOS);

        when(mockTalkskillMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<TalkskillDTO> result = talkskillServiceImplUnderTest.getQwimTalkskills("dealerCode",
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
