package com.yonyou.dmscus.customer.service.impl.voicemanage;

import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.configuration.NewTelecomConfig;
import com.yonyou.dmscus.customer.utils.ai.NewTelecomHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class NewTelecomAXBWorkNumberServiceTest {

    @Mock
    private NewTelecomHelper mockNewTelecomHelper;
    @Mock
    private NewTelecomConfig mockNewTelecomConfig;

    @InjectMocks
    private NewTelecomAXBWorkNumberService newTelecomAXBWorkNumberServiceUnderTest;

    @Test
    void testSupport() {
        assertThat(newTelecomAXBWorkNumberServiceUnderTest.support("operator")).isFalse();
    }

    @Test
    void testIsSuccess() {
        // Setup
        final Map<String, Object> result1 = new HashMap<>();

        // Run the test
        final boolean result = newTelecomAXBWorkNumberServiceUnderTest.isSuccess("methodType", result1);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testRegister_NewTelecomHelperThrowsIOException() throws Exception {
        // Setup
        when(mockNewTelecomConfig.getInfo()).thenReturn("info");
        when(mockNewTelecomHelper.httpPost("info", "param")).thenThrow(IOException.class);

        // Run the test
        assertThatThrownBy(() -> newTelecomAXBWorkNumberServiceUnderTest.register("callId", "holderNumber", "extension",
                "customNumber", "")).isInstanceOf(ServiceBizException.class);
    }
}
