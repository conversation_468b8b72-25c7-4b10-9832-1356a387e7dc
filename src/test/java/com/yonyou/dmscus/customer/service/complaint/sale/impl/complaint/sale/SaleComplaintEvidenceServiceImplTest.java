package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintEvidenceMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintEvidenceDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintEvidencePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SaleComplaintEvidenceServiceImplTest {

    @Mock
    private SaleComplaintEvidenceMapper mockSaleComplaintEvidenceMapper;

    private SaleComplaintEvidenceServiceImpl saleComplaintEvidenceServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        saleComplaintEvidenceServiceImplUnderTest = new SaleComplaintEvidenceServiceImpl();
        saleComplaintEvidenceServiceImplUnderTest.saleComplaintEvidenceMapper = mockSaleComplaintEvidenceMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintEvidenceDTO saleComplaintEvidenceDTO = new SaleComplaintEvidenceDTO();
        saleComplaintEvidenceDTO.setAppId("appId");
        saleComplaintEvidenceDTO.setOwnerCode("ownerCode");
        saleComplaintEvidenceDTO.setOwnerParCode("ownerParCode");
        saleComplaintEvidenceDTO.setOrgId(0);
        saleComplaintEvidenceDTO.setId(0L);

        // Configure SaleComplaintEvidenceMapper.selectPageBySql(...).
        final SaleComplaintEvidencePO saleComplaintEvidencePO = new SaleComplaintEvidencePO();
        saleComplaintEvidencePO.setAppId("appId");
        saleComplaintEvidencePO.setOwnerCode("ownerCode");
        saleComplaintEvidencePO.setOwnerParCode("ownerParCode");
        saleComplaintEvidencePO.setOrgId(0);
        saleComplaintEvidencePO.setId(0L);
        final List<SaleComplaintEvidencePO> saleComplaintEvidencePOS = Arrays.asList(saleComplaintEvidencePO);
        when(mockSaleComplaintEvidenceMapper.selectPageBySql(any(Page.class),
                any(SaleComplaintEvidencePO.class))).thenReturn(saleComplaintEvidencePOS);

        // Run the test
        final IPage<SaleComplaintEvidenceDTO> result = saleComplaintEvidenceServiceImplUnderTest.selectPageBysql(page,
                saleComplaintEvidenceDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_SaleComplaintEvidenceMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintEvidenceDTO saleComplaintEvidenceDTO = new SaleComplaintEvidenceDTO();
        saleComplaintEvidenceDTO.setAppId("appId");
        saleComplaintEvidenceDTO.setOwnerCode("ownerCode");
        saleComplaintEvidenceDTO.setOwnerParCode("ownerParCode");
        saleComplaintEvidenceDTO.setOrgId(0);
        saleComplaintEvidenceDTO.setId(0L);

        when(mockSaleComplaintEvidenceMapper.selectPageBySql(any(Page.class),
                any(SaleComplaintEvidencePO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<SaleComplaintEvidenceDTO> result = saleComplaintEvidenceServiceImplUnderTest.selectPageBysql(page,
                saleComplaintEvidenceDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final SaleComplaintEvidenceDTO saleComplaintEvidenceDTO = new SaleComplaintEvidenceDTO();
        saleComplaintEvidenceDTO.setAppId("appId");
        saleComplaintEvidenceDTO.setOwnerCode("ownerCode");
        saleComplaintEvidenceDTO.setOwnerParCode("ownerParCode");
        saleComplaintEvidenceDTO.setOrgId(0);
        saleComplaintEvidenceDTO.setId(0L);

        // Configure SaleComplaintEvidenceMapper.selectListBySql(...).
        final SaleComplaintEvidencePO saleComplaintEvidencePO = new SaleComplaintEvidencePO();
        saleComplaintEvidencePO.setAppId("appId");
        saleComplaintEvidencePO.setOwnerCode("ownerCode");
        saleComplaintEvidencePO.setOwnerParCode("ownerParCode");
        saleComplaintEvidencePO.setOrgId(0);
        saleComplaintEvidencePO.setId(0L);
        final List<SaleComplaintEvidencePO> saleComplaintEvidencePOS = Arrays.asList(saleComplaintEvidencePO);
        when(mockSaleComplaintEvidenceMapper.selectListBySql(any(SaleComplaintEvidencePO.class)))
                .thenReturn(saleComplaintEvidencePOS);

        // Run the test
        final List<SaleComplaintEvidenceDTO> result = saleComplaintEvidenceServiceImplUnderTest.selectListBySql(
                saleComplaintEvidenceDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_SaleComplaintEvidenceMapperReturnsNoItems() {
        // Setup
        final SaleComplaintEvidenceDTO saleComplaintEvidenceDTO = new SaleComplaintEvidenceDTO();
        saleComplaintEvidenceDTO.setAppId("appId");
        saleComplaintEvidenceDTO.setOwnerCode("ownerCode");
        saleComplaintEvidenceDTO.setOwnerParCode("ownerParCode");
        saleComplaintEvidenceDTO.setOrgId(0);
        saleComplaintEvidenceDTO.setId(0L);

        when(mockSaleComplaintEvidenceMapper.selectListBySql(any(SaleComplaintEvidencePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<SaleComplaintEvidenceDTO> result = saleComplaintEvidenceServiceImplUnderTest.selectListBySql(
                saleComplaintEvidenceDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure SaleComplaintEvidenceMapper.selectById(...).
        final SaleComplaintEvidencePO saleComplaintEvidencePO = new SaleComplaintEvidencePO();
        saleComplaintEvidencePO.setAppId("appId");
        saleComplaintEvidencePO.setOwnerCode("ownerCode");
        saleComplaintEvidencePO.setOwnerParCode("ownerParCode");
        saleComplaintEvidencePO.setOrgId(0);
        saleComplaintEvidencePO.setId(0L);
        when(mockSaleComplaintEvidenceMapper.selectById(0L)).thenReturn(saleComplaintEvidencePO);

        // Run the test
        final SaleComplaintEvidenceDTO result = saleComplaintEvidenceServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_SaleComplaintEvidenceMapperReturnsNull() {
        // Setup
        when(mockSaleComplaintEvidenceMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> saleComplaintEvidenceServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final SaleComplaintEvidenceDTO saleComplaintEvidenceDTO = new SaleComplaintEvidenceDTO();
        saleComplaintEvidenceDTO.setAppId("appId");
        saleComplaintEvidenceDTO.setOwnerCode("ownerCode");
        saleComplaintEvidenceDTO.setOwnerParCode("ownerParCode");
        saleComplaintEvidenceDTO.setOrgId(0);
        saleComplaintEvidenceDTO.setId(0L);

        when(mockSaleComplaintEvidenceMapper.insert(any(SaleComplaintEvidencePO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintEvidenceServiceImplUnderTest.insert(saleComplaintEvidenceDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final SaleComplaintEvidenceDTO saleComplaintEvidenceDTO = new SaleComplaintEvidenceDTO();
        saleComplaintEvidenceDTO.setAppId("appId");
        saleComplaintEvidenceDTO.setOwnerCode("ownerCode");
        saleComplaintEvidenceDTO.setOwnerParCode("ownerParCode");
        saleComplaintEvidenceDTO.setOrgId(0);
        saleComplaintEvidenceDTO.setId(0L);

        // Configure SaleComplaintEvidenceMapper.selectById(...).
        final SaleComplaintEvidencePO saleComplaintEvidencePO = new SaleComplaintEvidencePO();
        saleComplaintEvidencePO.setAppId("appId");
        saleComplaintEvidencePO.setOwnerCode("ownerCode");
        saleComplaintEvidencePO.setOwnerParCode("ownerParCode");
        saleComplaintEvidencePO.setOrgId(0);
        saleComplaintEvidencePO.setId(0L);
        when(mockSaleComplaintEvidenceMapper.selectById(0L)).thenReturn(saleComplaintEvidencePO);

        when(mockSaleComplaintEvidenceMapper.updateById(any(SaleComplaintEvidencePO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintEvidenceServiceImplUnderTest.update(0L, saleComplaintEvidenceDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
