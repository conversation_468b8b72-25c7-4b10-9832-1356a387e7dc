package com.yonyou.dmscus.customer.service.impl.maintaininfo;

import com.yonyou.dmscus.customer.dao.maintaininfo.InviteMaintainInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.maintaininfo.MaintainInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.maintaininfo.RecMaintainInfoDTO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRulePO;
import com.yonyou.dmscus.customer.entity.po.maintaininfo.InviteMaintainInfoPO;
import com.yonyou.dmscus.customer.service.inviteRule.InviteRuleService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteMaintainInfoServiceImplTest {

    @Mock
    private InviteRuleService mockInviteRuleService;
    @Mock
    private InviteMaintainInfoMapper mockInviteMaintainInfoMapper;

    @InjectMocks
    private InviteMaintainInfoServiceImpl inviteMaintainInfoServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteMaintainInfoServiceImplUnderTest.inviteRuleService = mockInviteRuleService;
    }

    @Test
    void testInsertMaintainInfo_InviteRuleServiceReturnsNoItems() {
        // Setup
        final RecMaintainInfoDTO dto = new RecMaintainInfoDTO();
        final MaintainInfoDTO maintainInfoDTO = new MaintainInfoDTO();
        maintainInfoDTO.setRoNo("roNo");
        maintainInfoDTO.setOwnerCode("ownerCode");
        maintainInfoDTO.setVin("vin");
        maintainInfoDTO.setMaintainMileage("maintainMileage");
        maintainInfoDTO.setMaintainTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        maintainInfoDTO.setBeforeMaintainMileage("beforeMaintainMileage");
        maintainInfoDTO.setBeforeMaintainTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        maintainInfoDTO.setDiffMileage(0.0);
        maintainInfoDTO.setDiffMonth(1);
        dto.setList(Arrays.asList(maintainInfoDTO));

        when(mockInviteRuleService.getRegularMaintainRule("VCDC")).thenReturn(Collections.emptyList());

        // Run the test
        inviteMaintainInfoServiceImplUnderTest.insertMaintainInfo(dto);

        // Verify the results
    }

    @Test
    void testSelectMainInfoByVin() {
        // Setup
        final InviteMaintainInfoPO expectedResult = InviteMaintainInfoPO.builder()
                .ownerCode("ownerCode")
                .roNo("roNo")
                .vin("vin")
                .maintainMileage("maintainMileage")
                .maintainTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .beforeMaintainMileage("beforeMaintainMileage")
                .beforeMaintainTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .diffMileage(0.0)
                .diffMonth(1)
                .datumMileage(0.0)
                .datumMonth(1)
                .advanceMileage(0.0)
                .advanceMonth(1)
                .build();

        // Configure InviteMaintainInfoMapper.selectMainInfoByVin(...).
        final InviteMaintainInfoPO inviteMaintainInfoPO = InviteMaintainInfoPO.builder()
                .ownerCode("ownerCode")
                .roNo("roNo")
                .vin("vin")
                .maintainMileage("maintainMileage")
                .maintainTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .beforeMaintainMileage("beforeMaintainMileage")
                .beforeMaintainTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .diffMileage(0.0)
                .diffMonth(1)
                .datumMileage(0.0)
                .datumMonth(1)
                .advanceMileage(0.0)
                .advanceMonth(1)
                .build();
        when(mockInviteMaintainInfoMapper.selectMainInfoByVin("vin")).thenReturn(inviteMaintainInfoPO);

        // Run the test
        final InviteMaintainInfoPO result = inviteMaintainInfoServiceImplUnderTest.selectMainInfoByVin("vin");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
