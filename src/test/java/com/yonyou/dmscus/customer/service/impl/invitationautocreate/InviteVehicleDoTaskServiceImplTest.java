package com.yonyou.dmscus.customer.service.impl.invitationautocreate;

import com.yonyou.dmscus.customer.dao.invitationautocreate.DailyMileageLogMapper;
import com.yonyou.dmscus.customer.dto.RepairOrderVO;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.feign.RepairCommonClient;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.feign.vo.ClueParamVO;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.invitationautocreate.InviteVehicleTaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteVehicleDoTaskServiceImplTest {

    @Mock
    private RepairCommonClient mockRepairCommonClient;
    @Mock
    private ReportCommonClient mockReportCommonClient;
    @Mock
    private InviteVehicleTaskService mockInviteVehicleTaskService;
    @Mock
    private InviteVehicleRecordService mockInviteVehicleRecordService;
    @Mock
    private DailyMileageLogMapper mockDailyMileageLogMapper;

    private InviteVehicleDoTaskServiceImpl inviteVehicleDoTaskServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteVehicleDoTaskServiceImplUnderTest = new InviteVehicleDoTaskServiceImpl();
        inviteVehicleDoTaskServiceImplUnderTest.repairCommonClient = mockRepairCommonClient;
        inviteVehicleDoTaskServiceImplUnderTest.reportCommonClient = mockReportCommonClient;
        inviteVehicleDoTaskServiceImplUnderTest.inviteVehicleTaskService = mockInviteVehicleTaskService;
        inviteVehicleDoTaskServiceImplUnderTest.inviteVehicleRecordService = mockInviteVehicleRecordService;
        inviteVehicleDoTaskServiceImplUnderTest.dailyMileageLogMapper = mockDailyMileageLogMapper;
    }

    @Test
    void testInviteAutoCreateTask_ReportCommonClientReturnsNoItems() {
        // Setup
        when(mockReportCommonClient.queryRegularMaintain("createDate", 1)).thenReturn(Collections.emptyList());

        // Run the test
        inviteVehicleDoTaskServiceImplUnderTest.inviteAutoCreateTask("createDate", "ownerCode", "orderNo");

        // Verify the results
        verify(mockInviteVehicleTaskService).closeClues("createDate");
        verify(mockInviteVehicleTaskService).closeGuarantee("createDate");
        verify(mockInviteVehicleTaskService).createTyreTask("createDate");
    }

    @Test
    void testAddTaskByRepairOrder_ReportCommonClientReturnsNoItems() {
        // Setup
        when(mockReportCommonClient.queryRegularMaintain("createDate", 1)).thenReturn(Collections.emptyList());

        // Run the test
        inviteVehicleDoTaskServiceImplUnderTest.addTaskByRepairOrder("createDate", "ownerCode", "orderNo");

        // Verify the results
    }
}
