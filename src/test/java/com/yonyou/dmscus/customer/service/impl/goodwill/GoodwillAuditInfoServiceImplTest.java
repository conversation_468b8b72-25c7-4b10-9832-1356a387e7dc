package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillAuditInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillAuditInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillAuditInfoPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillAuditInfoServiceImplTest {

    @Mock
    private GoodwillAuditInfoMapper mockGoodwillAuditInfoMapper;

    private GoodwillAuditInfoServiceImpl goodwillAuditInfoServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        goodwillAuditInfoServiceImplUnderTest = new GoodwillAuditInfoServiceImpl();
        goodwillAuditInfoServiceImplUnderTest.goodwillAuditInfoMapper = mockGoodwillAuditInfoMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillAuditInfoDTO goodwillAuditInfoDTO = new GoodwillAuditInfoDTO();
        goodwillAuditInfoDTO.setRoleList("roleList");
        goodwillAuditInfoDTO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillAuditInfoDTO.setAppId("appId");
        goodwillAuditInfoDTO.setOwnerCode("ownerCode");
        goodwillAuditInfoDTO.setOwnerParCode("ownerParCode");

        // Configure GoodwillAuditInfoMapper.selectPageBySql(...).
        final GoodwillAuditInfoPO goodwillAuditInfoPO = new GoodwillAuditInfoPO();
        goodwillAuditInfoPO.setAuditName("auditName");
        goodwillAuditInfoPO.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillAuditInfoPO.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillAuditInfoPO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillAuditInfoPO.setAppId("appId");
        final List<GoodwillAuditInfoPO> goodwillAuditInfoPOS = Arrays.asList(goodwillAuditInfoPO);
        when(mockGoodwillAuditInfoMapper.selectPageBySql(any(Page.class), any(GoodwillAuditInfoPO.class)))
                .thenReturn(goodwillAuditInfoPOS);

        // Run the test
        final IPage<GoodwillAuditInfoDTO> result = goodwillAuditInfoServiceImplUnderTest.selectPageBysql(page,
                goodwillAuditInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillAuditInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillAuditInfoDTO goodwillAuditInfoDTO = new GoodwillAuditInfoDTO();
        goodwillAuditInfoDTO.setRoleList("roleList");
        goodwillAuditInfoDTO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillAuditInfoDTO.setAppId("appId");
        goodwillAuditInfoDTO.setOwnerCode("ownerCode");
        goodwillAuditInfoDTO.setOwnerParCode("ownerParCode");

        when(mockGoodwillAuditInfoMapper.selectPageBySql(any(Page.class), any(GoodwillAuditInfoPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillAuditInfoDTO> result = goodwillAuditInfoServiceImplUnderTest.selectPageBysql(page,
                goodwillAuditInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final GoodwillAuditInfoDTO goodwillAuditInfoDTO = new GoodwillAuditInfoDTO();
        goodwillAuditInfoDTO.setRoleList("roleList");
        goodwillAuditInfoDTO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillAuditInfoDTO.setAppId("appId");
        goodwillAuditInfoDTO.setOwnerCode("ownerCode");
        goodwillAuditInfoDTO.setOwnerParCode("ownerParCode");

        // Configure GoodwillAuditInfoMapper.selectListBySql(...).
        final GoodwillAuditInfoPO goodwillAuditInfoPO = new GoodwillAuditInfoPO();
        goodwillAuditInfoPO.setAuditName("auditName");
        goodwillAuditInfoPO.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillAuditInfoPO.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillAuditInfoPO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillAuditInfoPO.setAppId("appId");
        final List<GoodwillAuditInfoPO> goodwillAuditInfoPOS = Arrays.asList(goodwillAuditInfoPO);
        when(mockGoodwillAuditInfoMapper.selectListBySql(any(GoodwillAuditInfoPO.class)))
                .thenReturn(goodwillAuditInfoPOS);

        // Run the test
        final List<GoodwillAuditInfoDTO> result = goodwillAuditInfoServiceImplUnderTest.selectListBySql(
                goodwillAuditInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillAuditInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final GoodwillAuditInfoDTO goodwillAuditInfoDTO = new GoodwillAuditInfoDTO();
        goodwillAuditInfoDTO.setRoleList("roleList");
        goodwillAuditInfoDTO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillAuditInfoDTO.setAppId("appId");
        goodwillAuditInfoDTO.setOwnerCode("ownerCode");
        goodwillAuditInfoDTO.setOwnerParCode("ownerParCode");

        when(mockGoodwillAuditInfoMapper.selectListBySql(any(GoodwillAuditInfoPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillAuditInfoDTO> result = goodwillAuditInfoServiceImplUnderTest.selectListBySql(
                goodwillAuditInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure GoodwillAuditInfoMapper.selectById(...).
        final GoodwillAuditInfoPO goodwillAuditInfoPO = new GoodwillAuditInfoPO();
        goodwillAuditInfoPO.setAuditName("auditName");
        goodwillAuditInfoPO.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillAuditInfoPO.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillAuditInfoPO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillAuditInfoPO.setAppId("appId");
        when(mockGoodwillAuditInfoMapper.selectById(0L)).thenReturn(goodwillAuditInfoPO);

        // Run the test
        final GoodwillAuditInfoDTO result = goodwillAuditInfoServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillAuditInfoMapperReturnsNull() throws Exception {
        // Setup
        when(mockGoodwillAuditInfoMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillAuditInfoServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final GoodwillAuditInfoDTO goodwillAuditInfoDTO = new GoodwillAuditInfoDTO();
        goodwillAuditInfoDTO.setRoleList("roleList");
        goodwillAuditInfoDTO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillAuditInfoDTO.setAppId("appId");
        goodwillAuditInfoDTO.setOwnerCode("ownerCode");
        goodwillAuditInfoDTO.setOwnerParCode("ownerParCode");

        when(mockGoodwillAuditInfoMapper.insert(any(GoodwillAuditInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillAuditInfoServiceImplUnderTest.insert(goodwillAuditInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final GoodwillAuditInfoDTO goodwillAuditInfoDTO = new GoodwillAuditInfoDTO();
        goodwillAuditInfoDTO.setRoleList("roleList");
        goodwillAuditInfoDTO.setSettlementAmount(new BigDecimal("0.00"));
        goodwillAuditInfoDTO.setAppId("appId");
        goodwillAuditInfoDTO.setOwnerCode("ownerCode");
        goodwillAuditInfoDTO.setOwnerParCode("ownerParCode");

        // Configure GoodwillAuditInfoMapper.selectById(...).
        final GoodwillAuditInfoPO goodwillAuditInfoPO = new GoodwillAuditInfoPO();
        goodwillAuditInfoPO.setAuditName("auditName");
        goodwillAuditInfoPO.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillAuditInfoPO.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillAuditInfoPO.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodwillAuditInfoPO.setAppId("appId");
        when(mockGoodwillAuditInfoMapper.selectById(0L)).thenReturn(goodwillAuditInfoPO);

        when(mockGoodwillAuditInfoMapper.updateById(any(GoodwillAuditInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillAuditInfoServiceImplUnderTest.update(0L, goodwillAuditInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testQueryApplyAuditHistoryInfo() {
        // Setup
        when(mockGoodwillAuditInfoMapper.queryApplyHistory(0, 0L)).thenReturn(Arrays.asList(new HashMap<>()));

        // Run the test
        final List<Map> result = goodwillAuditInfoServiceImplUnderTest.queryApplyAuditHistoryInfo(0, 0L);

        // Verify the results
    }

    @Test
    void testQueryApplyAuditHistoryInfo_GoodwillAuditInfoMapperReturnsNoItems() {
        // Setup
        when(mockGoodwillAuditInfoMapper.queryApplyHistory(0, 0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<Map> result = goodwillAuditInfoServiceImplUnderTest.queryApplyAuditHistoryInfo(0, 0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testQueryReturnList() {
        // Setup
        final GoodwillAuditInfoDTO goodwillAuditInfoDto = new GoodwillAuditInfoDTO();
        goodwillAuditInfoDto.setRoleList("roleList");
        goodwillAuditInfoDto.setSettlementAmount(new BigDecimal("0.00"));
        goodwillAuditInfoDto.setAppId("appId");
        goodwillAuditInfoDto.setOwnerCode("ownerCode");
        goodwillAuditInfoDto.setOwnerParCode("ownerParCode");

        when(mockGoodwillAuditInfoMapper.queryReturnList(any(GoodwillAuditInfoDTO.class)))
                .thenReturn(Arrays.asList(new HashMap<>()));

        // Run the test
        final List<Map> result = goodwillAuditInfoServiceImplUnderTest.queryReturnList(goodwillAuditInfoDto);

        // Verify the results
    }

    @Test
    void testQueryReturnList_GoodwillAuditInfoMapperReturnsNoItems() {
        // Setup
        final GoodwillAuditInfoDTO goodwillAuditInfoDto = new GoodwillAuditInfoDTO();
        goodwillAuditInfoDto.setRoleList("roleList");
        goodwillAuditInfoDto.setSettlementAmount(new BigDecimal("0.00"));
        goodwillAuditInfoDto.setAppId("appId");
        goodwillAuditInfoDto.setOwnerCode("ownerCode");
        goodwillAuditInfoDto.setOwnerParCode("ownerParCode");

        when(mockGoodwillAuditInfoMapper.queryReturnList(any(GoodwillAuditInfoDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<Map> result = goodwillAuditInfoServiceImplUnderTest.queryReturnList(goodwillAuditInfoDto);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
