package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintAttachmentMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintAttachmentTestDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintAttachmentPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintAttachmentServiceImplTest {

    @Mock
    private ComplaintAttachmentMapper mockComplaintAttachmentMapper;

    private ComplaintAttachmentServiceImpl complaintAttachmentServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        complaintAttachmentServiceImplUnderTest = new ComplaintAttachmentServiceImpl();
        complaintAttachmentServiceImplUnderTest.complaintAttachmentMapper = mockComplaintAttachmentMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintAttachmentDTO complaintAttachmentDTO = new ComplaintAttachmentDTO();
        complaintAttachmentDTO.setAppId("appId");
        complaintAttachmentDTO.setOwnerCode("ownerCode");
        complaintAttachmentDTO.setOwnerParCode("ownerParCode");
        complaintAttachmentDTO.setOrgId(0);
        complaintAttachmentDTO.setId(0L);

        // Configure ComplaintAttachmentMapper.selectPageBySql(...).
        final ComplaintAttachmentPO complaintAttachmentPO = new ComplaintAttachmentPO();
        complaintAttachmentPO.setOwnerCode("ownerCode");
        complaintAttachmentPO.setOrgId(0);
        complaintAttachmentPO.setId(0L);
        complaintAttachmentPO.setComplaintInfoId(0L);
        complaintAttachmentPO.setObject("object");
        final List<ComplaintAttachmentPO> complaintAttachmentPOS = Arrays.asList(complaintAttachmentPO);
        when(mockComplaintAttachmentMapper.selectPageBySql(any(Page.class),
                any(ComplaintAttachmentPO.class))).thenReturn(complaintAttachmentPOS);

        // Run the test
        final IPage<ComplaintAttachmentDTO> result = complaintAttachmentServiceImplUnderTest.selectPageBysql(page,
                complaintAttachmentDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintAttachmentMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintAttachmentDTO complaintAttachmentDTO = new ComplaintAttachmentDTO();
        complaintAttachmentDTO.setAppId("appId");
        complaintAttachmentDTO.setOwnerCode("ownerCode");
        complaintAttachmentDTO.setOwnerParCode("ownerParCode");
        complaintAttachmentDTO.setOrgId(0);
        complaintAttachmentDTO.setId(0L);

        when(mockComplaintAttachmentMapper.selectPageBySql(any(Page.class),
                any(ComplaintAttachmentPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintAttachmentDTO> result = complaintAttachmentServiceImplUnderTest.selectPageBysql(page,
                complaintAttachmentDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final ComplaintAttachmentDTO complaintAttachmentDTO = new ComplaintAttachmentDTO();
        complaintAttachmentDTO.setAppId("appId");
        complaintAttachmentDTO.setOwnerCode("ownerCode");
        complaintAttachmentDTO.setOwnerParCode("ownerParCode");
        complaintAttachmentDTO.setOrgId(0);
        complaintAttachmentDTO.setId(0L);

        // Configure ComplaintAttachmentMapper.selectListBySql(...).
        final ComplaintAttachmentPO complaintAttachmentPO = new ComplaintAttachmentPO();
        complaintAttachmentPO.setOwnerCode("ownerCode");
        complaintAttachmentPO.setOrgId(0);
        complaintAttachmentPO.setId(0L);
        complaintAttachmentPO.setComplaintInfoId(0L);
        complaintAttachmentPO.setObject("object");
        final List<ComplaintAttachmentPO> complaintAttachmentPOS = Arrays.asList(complaintAttachmentPO);
        when(mockComplaintAttachmentMapper.selectListBySql(any(ComplaintAttachmentPO.class)))
                .thenReturn(complaintAttachmentPOS);

        // Run the test
        final List<ComplaintAttachmentDTO> result = complaintAttachmentServiceImplUnderTest.selectListBySql(
                complaintAttachmentDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintAttachmentMapperReturnsNoItems() {
        // Setup
        final ComplaintAttachmentDTO complaintAttachmentDTO = new ComplaintAttachmentDTO();
        complaintAttachmentDTO.setAppId("appId");
        complaintAttachmentDTO.setOwnerCode("ownerCode");
        complaintAttachmentDTO.setOwnerParCode("ownerParCode");
        complaintAttachmentDTO.setOrgId(0);
        complaintAttachmentDTO.setId(0L);

        when(mockComplaintAttachmentMapper.selectListBySql(any(ComplaintAttachmentPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintAttachmentDTO> result = complaintAttachmentServiceImplUnderTest.selectListBySql(
                complaintAttachmentDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure ComplaintAttachmentMapper.selectById(...).
        final ComplaintAttachmentPO complaintAttachmentPO = new ComplaintAttachmentPO();
        complaintAttachmentPO.setOwnerCode("ownerCode");
        complaintAttachmentPO.setOrgId(0);
        complaintAttachmentPO.setId(0L);
        complaintAttachmentPO.setComplaintInfoId(0L);
        complaintAttachmentPO.setObject("object");
        when(mockComplaintAttachmentMapper.selectById(0L)).thenReturn(complaintAttachmentPO);

        // Run the test
        final ComplaintAttachmentDTO result = complaintAttachmentServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintAttachmentMapperReturnsNull() {
        // Setup
        when(mockComplaintAttachmentMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintAttachmentServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final ComplaintAttachmentDTO complaintAttachmentDTO = new ComplaintAttachmentDTO();
        complaintAttachmentDTO.setAppId("appId");
        complaintAttachmentDTO.setOwnerCode("ownerCode");
        complaintAttachmentDTO.setOwnerParCode("ownerParCode");
        complaintAttachmentDTO.setOrgId(0);
        complaintAttachmentDTO.setId(0L);

        when(mockComplaintAttachmentMapper.insert(any(ComplaintAttachmentPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintAttachmentServiceImplUnderTest.insert(complaintAttachmentDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final ComplaintAttachmentDTO complaintAttachmentDTO = new ComplaintAttachmentDTO();
        complaintAttachmentDTO.setAppId("appId");
        complaintAttachmentDTO.setOwnerCode("ownerCode");
        complaintAttachmentDTO.setOwnerParCode("ownerParCode");
        complaintAttachmentDTO.setOrgId(0);
        complaintAttachmentDTO.setId(0L);

        // Configure ComplaintAttachmentMapper.selectById(...).
        final ComplaintAttachmentPO complaintAttachmentPO = new ComplaintAttachmentPO();
        complaintAttachmentPO.setOwnerCode("ownerCode");
        complaintAttachmentPO.setOrgId(0);
        complaintAttachmentPO.setId(0L);
        complaintAttachmentPO.setComplaintInfoId(0L);
        complaintAttachmentPO.setObject("object");
        when(mockComplaintAttachmentMapper.selectById(0L)).thenReturn(complaintAttachmentPO);

        when(mockComplaintAttachmentMapper.updateById(any(ComplaintAttachmentPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintAttachmentServiceImplUnderTest.update(0L, complaintAttachmentDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectPageBysql1() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintAttachmentDTO complaintAttachmentDTO = new ComplaintAttachmentDTO();
        complaintAttachmentDTO.setAppId("appId");
        complaintAttachmentDTO.setOwnerCode("ownerCode");
        complaintAttachmentDTO.setOwnerParCode("ownerParCode");
        complaintAttachmentDTO.setOrgId(0);
        complaintAttachmentDTO.setId(0L);

        // Configure ComplaintAttachmentMapper.selectPageBySql1(...).
        final ComplaintAttachmentPO complaintAttachmentPO = new ComplaintAttachmentPO();
        complaintAttachmentPO.setOwnerCode("ownerCode");
        complaintAttachmentPO.setOrgId(0);
        complaintAttachmentPO.setId(0L);
        complaintAttachmentPO.setComplaintInfoId(0L);
        complaintAttachmentPO.setObject("object");
        final List<ComplaintAttachmentPO> complaintAttachmentPOS = Arrays.asList(complaintAttachmentPO);
        when(mockComplaintAttachmentMapper.selectPageBySql1(any(Page.class),
                any(ComplaintAttachmentPO.class))).thenReturn(complaintAttachmentPOS);

        // Run the test
        final IPage<ComplaintAttachmentDTO> result = complaintAttachmentServiceImplUnderTest.selectPageBysql1(page,
                complaintAttachmentDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql1_ComplaintAttachmentMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintAttachmentDTO complaintAttachmentDTO = new ComplaintAttachmentDTO();
        complaintAttachmentDTO.setAppId("appId");
        complaintAttachmentDTO.setOwnerCode("ownerCode");
        complaintAttachmentDTO.setOwnerParCode("ownerParCode");
        complaintAttachmentDTO.setOrgId(0);
        complaintAttachmentDTO.setId(0L);

        when(mockComplaintAttachmentMapper.selectPageBySql1(any(Page.class),
                any(ComplaintAttachmentPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintAttachmentDTO> result = complaintAttachmentServiceImplUnderTest.selectPageBysql1(page,
                complaintAttachmentDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql1() {
        // Setup
        final ComplaintAttachmentDTO complaintAttachmentDTO = new ComplaintAttachmentDTO();
        complaintAttachmentDTO.setAppId("appId");
        complaintAttachmentDTO.setOwnerCode("ownerCode");
        complaintAttachmentDTO.setOwnerParCode("ownerParCode");
        complaintAttachmentDTO.setOrgId(0);
        complaintAttachmentDTO.setId(0L);

        // Configure ComplaintAttachmentMapper.selectListBySql1(...).
        final ComplaintAttachmentTestDTO complaintAttachmentTestDTO = new ComplaintAttachmentTestDTO();
        complaintAttachmentTestDTO.setUserId("userId");
        complaintAttachmentTestDTO.setAppId("appId");
        complaintAttachmentTestDTO.setOwnerCode("ownerCode");
        complaintAttachmentTestDTO.setOwnerParCode("ownerParCode");
        complaintAttachmentTestDTO.setOrgId(0);
        final List<ComplaintAttachmentTestDTO> complaintAttachmentTestDTOS = Arrays.asList(complaintAttachmentTestDTO);
        when(mockComplaintAttachmentMapper.selectListBySql1(any(ComplaintAttachmentPO.class)))
                .thenReturn(complaintAttachmentTestDTOS);

        // Run the test
        final List<ComplaintAttachmentTestDTO> result = complaintAttachmentServiceImplUnderTest.selectListBySql1(
                complaintAttachmentDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql1_ComplaintAttachmentMapperReturnsNoItems() {
        // Setup
        final ComplaintAttachmentDTO complaintAttachmentDTO = new ComplaintAttachmentDTO();
        complaintAttachmentDTO.setAppId("appId");
        complaintAttachmentDTO.setOwnerCode("ownerCode");
        complaintAttachmentDTO.setOwnerParCode("ownerParCode");
        complaintAttachmentDTO.setOrgId(0);
        complaintAttachmentDTO.setId(0L);

        when(mockComplaintAttachmentMapper.selectListBySql1(any(ComplaintAttachmentPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintAttachmentTestDTO> result = complaintAttachmentServiceImplUnderTest.selectListBySql1(
                complaintAttachmentDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testDelete() {
        // Setup
        // Run the test
        complaintAttachmentServiceImplUnderTest.delete(0L);

        // Verify the results
        verify(mockComplaintAttachmentMapper).deleteAct(0L);
    }
}
