package com.yonyou.dmscus.customer.service.impl.invitationCreate;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.service.CommonNoService;
import com.yonyou.dmscloud.framework.service.excel.ExcelRead;
import com.yonyou.dmscloud.framework.service.excel.impl.AbstractExcelReadCallBack;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.dao.invitationCreate.InviteVehicleDealerTaskImportMapper;
import com.yonyou.dmscus.customer.dao.invitationCreate.InviteVehicleDealerTaskMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dto.CustomerInfoCenterDTO;
import com.yonyou.dmscus.customer.dto.CustomerInfoListReturnDTO;
import com.yonyou.dmscus.customer.dto.InviteVehicleDealerTaskImportDTO;
import com.yonyou.dmscus.customer.dto.InviteVehicleVCDCTaskImportDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.invitationCreate.InviteVehicleDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationCreate.InviteVehicleDealerImportDTO;
import com.yonyou.dmscus.customer.entity.dto.invitationCreate.InviteVehicleDealerTaskDTO;
import com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskImportPO;
import com.yonyou.dmscus.customer.entity.po.invitationCreate.InviteVehicleDealerTaskPO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.middleInterface.ResponseDTO;
import com.yonyou.dmscus.customer.service.common.IMiddleGroundVehicleService;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteVehicleDealerTaskServiceImplTest {

    @Mock
    private InviteVehicleDealerTaskMapper mockInviteVehicleDealerTaskMapper;
    @Mock
    private CommonNoService mockCommonNoService;
    @Mock
    private InviteVehicleRecordMapper mockInviteVehicleRecordMapper;
    @Mock
    private ApplicationEventPublisher mockApplicationEventPublisher;
    @Mock
    private InviteVehicleDealerTaskImportMapper mockInviteVehicleDealerTaskImportMapper;
    @Mock
    private ExcelRead<InviteVehicleDealerTaskImportDTO> mockExcelReadServiceIs;
    @Mock
    private ExcelRead<InviteVehicleVCDCTaskImportDTO> mockExcelReadService;
    @Mock
    private BusinessPlatformService mockBusinessPlatformService;
    @Mock
    private InviteVehicleRecordService mockInviteVehicleRecordService;
    @Mock
    private ReportCommonClient mockReportCommonClient;
    @Mock
    private IMiddleGroundVehicleService mockIMiddleGroundVehicleService;

    @InjectMocks
    private InviteVehicleDealerTaskServiceImpl inviteVehicleDealerTaskServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteVehicleDealerTaskServiceImplUnderTest.inviteVehicleDealerTaskMapper = mockInviteVehicleDealerTaskMapper;
        inviteVehicleDealerTaskServiceImplUnderTest.commonNoService = mockCommonNoService;
        inviteVehicleDealerTaskServiceImplUnderTest.inviteVehicleRecordMapper = mockInviteVehicleRecordMapper;
        inviteVehicleDealerTaskServiceImplUnderTest.inviteVehicleDealerTaskImportMapper = mockInviteVehicleDealerTaskImportMapper;
        inviteVehicleDealerTaskServiceImplUnderTest.excelReadServiceIs = mockExcelReadServiceIs;
        inviteVehicleDealerTaskServiceImplUnderTest.excelReadService = mockExcelReadService;
        inviteVehicleDealerTaskServiceImplUnderTest.businessPlatformService = mockBusinessPlatformService;
        inviteVehicleDealerTaskServiceImplUnderTest.inviteVehicleRecordService = mockInviteVehicleRecordService;
        inviteVehicleDealerTaskServiceImplUnderTest.reportCommonClient = mockReportCommonClient;
        inviteVehicleDealerTaskServiceImplUnderTest.iMiddleGroundVehicleService = mockIMiddleGroundVehicleService;
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDTO = new InviteVehicleDealerTaskDTO();
        final InviteVehicleDTO inviteVehicleDTO = new InviteVehicleDTO();
        inviteVehicleDTO.setName("name");
        inviteVehicleDTO.setTel("tel");
        inviteVehicleDTO.setLicensePlateNum("licensePlateNum");
        inviteVehicleDTO.setVin("vin");
        inviteVehicleDTO.setSaId("saId");
        inviteVehicleDTO.setSaName("saName");
        inviteVehicleDealerTaskDTO.setVehList(Arrays.asList(inviteVehicleDTO));
        inviteVehicleDealerTaskDTO.setDealerCode("dealerCode");
        inviteVehicleDealerTaskDTO.setInviteType(0);
        inviteVehicleDealerTaskDTO.setInviteId(0L);

        // Configure InviteVehicleDealerTaskMapper.selectListBySql(...).
        final InviteVehicleDealerTaskPO inviteVehicleDealerTaskPO = new InviteVehicleDealerTaskPO();
        inviteVehicleDealerTaskPO.setCreatedName("createdName");
        inviteVehicleDealerTaskPO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleDealerTaskPO.setOwnerCode("dealerCode");
        inviteVehicleDealerTaskPO.setDealerCode("dealerCode");
        inviteVehicleDealerTaskPO.setPlanNo("planNo");
        inviteVehicleDealerTaskPO.setInviteType(0);
        inviteVehicleDealerTaskPO.setInviteId(0L);
        inviteVehicleDealerTaskPO.setDataSources(0);
        final List<InviteVehicleDealerTaskPO> inviteVehicleDealerTaskPOS = Arrays.asList(inviteVehicleDealerTaskPO);
        when(mockInviteVehicleDealerTaskMapper.selectListBySql(any(InviteVehicleDealerTaskPO.class)))
                .thenReturn(inviteVehicleDealerTaskPOS);

        // Run the test
        final List<InviteVehicleDealerTaskDTO> result = inviteVehicleDealerTaskServiceImplUnderTest.selectListBySql(
                inviteVehicleDealerTaskDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_InviteVehicleDealerTaskMapperReturnsNoItems() {
        // Setup
        final InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDTO = new InviteVehicleDealerTaskDTO();
        final InviteVehicleDTO inviteVehicleDTO = new InviteVehicleDTO();
        inviteVehicleDTO.setName("name");
        inviteVehicleDTO.setTel("tel");
        inviteVehicleDTO.setLicensePlateNum("licensePlateNum");
        inviteVehicleDTO.setVin("vin");
        inviteVehicleDTO.setSaId("saId");
        inviteVehicleDTO.setSaName("saName");
        inviteVehicleDealerTaskDTO.setVehList(Arrays.asList(inviteVehicleDTO));
        inviteVehicleDealerTaskDTO.setDealerCode("dealerCode");
        inviteVehicleDealerTaskDTO.setInviteType(0);
        inviteVehicleDealerTaskDTO.setInviteId(0L);

        when(mockInviteVehicleDealerTaskMapper.selectListBySql(any(InviteVehicleDealerTaskPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteVehicleDealerTaskDTO> result = inviteVehicleDealerTaskServiceImplUnderTest.selectListBySql(
                inviteVehicleDealerTaskDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure InviteVehicleDealerTaskMapper.selectById(...).
        final InviteVehicleDealerTaskPO inviteVehicleDealerTaskPO = new InviteVehicleDealerTaskPO();
        inviteVehicleDealerTaskPO.setCreatedName("createdName");
        inviteVehicleDealerTaskPO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleDealerTaskPO.setOwnerCode("dealerCode");
        inviteVehicleDealerTaskPO.setDealerCode("dealerCode");
        inviteVehicleDealerTaskPO.setPlanNo("planNo");
        inviteVehicleDealerTaskPO.setInviteType(0);
        inviteVehicleDealerTaskPO.setInviteId(0L);
        inviteVehicleDealerTaskPO.setDataSources(0);
        when(mockInviteVehicleDealerTaskMapper.selectById(0L)).thenReturn(inviteVehicleDealerTaskPO);

        // Run the test
        final InviteVehicleDealerTaskDTO result = inviteVehicleDealerTaskServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_InviteVehicleDealerTaskMapperReturnsNull() {
        // Setup
        when(mockInviteVehicleDealerTaskMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inviteVehicleDealerTaskServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDTO = new InviteVehicleDealerTaskDTO();
        final InviteVehicleDTO inviteVehicleDTO = new InviteVehicleDTO();
        inviteVehicleDTO.setName("name");
        inviteVehicleDTO.setTel("tel");
        inviteVehicleDTO.setLicensePlateNum("licensePlateNum");
        inviteVehicleDTO.setVin("vin");
        inviteVehicleDTO.setSaId("saId");
        inviteVehicleDTO.setSaName("saName");
        inviteVehicleDealerTaskDTO.setVehList(Arrays.asList(inviteVehicleDTO));
        inviteVehicleDealerTaskDTO.setDealerCode("dealerCode");
        inviteVehicleDealerTaskDTO.setInviteType(0);
        inviteVehicleDealerTaskDTO.setInviteId(0L);

        when(mockInviteVehicleDealerTaskMapper.insert(any(InviteVehicleDealerTaskPO.class))).thenReturn(0);

        // Run the test
        final int result = inviteVehicleDealerTaskServiceImplUnderTest.insert(inviteVehicleDealerTaskDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final InviteVehicleDealerTaskDTO inviteVehicleDealerTaskDTO = new InviteVehicleDealerTaskDTO();
        final InviteVehicleDTO inviteVehicleDTO = new InviteVehicleDTO();
        inviteVehicleDTO.setName("name");
        inviteVehicleDTO.setTel("tel");
        inviteVehicleDTO.setLicensePlateNum("licensePlateNum");
        inviteVehicleDTO.setVin("vin");
        inviteVehicleDTO.setSaId("saId");
        inviteVehicleDTO.setSaName("saName");
        inviteVehicleDealerTaskDTO.setVehList(Arrays.asList(inviteVehicleDTO));
        inviteVehicleDealerTaskDTO.setDealerCode("dealerCode");
        inviteVehicleDealerTaskDTO.setInviteType(0);
        inviteVehicleDealerTaskDTO.setInviteId(0L);

        // Configure InviteVehicleDealerTaskMapper.selectById(...).
        final InviteVehicleDealerTaskPO inviteVehicleDealerTaskPO = new InviteVehicleDealerTaskPO();
        inviteVehicleDealerTaskPO.setCreatedName("createdName");
        inviteVehicleDealerTaskPO.setAdviseInDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inviteVehicleDealerTaskPO.setOwnerCode("dealerCode");
        inviteVehicleDealerTaskPO.setDealerCode("dealerCode");
        inviteVehicleDealerTaskPO.setPlanNo("planNo");
        inviteVehicleDealerTaskPO.setInviteType(0);
        inviteVehicleDealerTaskPO.setInviteId(0L);
        inviteVehicleDealerTaskPO.setDataSources(0);
        when(mockInviteVehicleDealerTaskMapper.selectById(0L)).thenReturn(inviteVehicleDealerTaskPO);

        when(mockInviteVehicleDealerTaskMapper.updateById(any(InviteVehicleDealerTaskPO.class))).thenReturn(0);

        // Run the test
        final int result = inviteVehicleDealerTaskServiceImplUnderTest.update(0L, inviteVehicleDealerTaskDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
