package com.yonyou.dmscus.customer.service.common.ownervehicle;

import com.yonyou.dmscus.customer.dao.common.VehicleMapper;
import com.yonyou.dmscus.customer.entity.dto.common.VehicleDTO;
import com.yonyou.dmscus.customer.entity.po.common.VehiclePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class VehicleServiceImplTest {

    @Mock
    private VehicleMapper mockVehicleMapper;

    private VehicleServiceImpl vehicleServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        vehicleServiceImplUnderTest = new VehicleServiceImpl();
        vehicleServiceImplUnderTest.vehicleMapper = mockVehicleMapper;
    }

    @Test
    void testGetVehicleByVin() {
        // Setup
        final VehicleDTO expectedResult = new VehicleDTO();
        expectedResult.setAppId("appId");
        expectedResult.setOwnerCode("ownerCode");
        expectedResult.setOwnerParCode("ownerParCode");
        expectedResult.setOrgId(0);
        expectedResult.setId(0L);

        // Configure VehicleMapper.getVehicleByVin(...).
        final VehiclePO vehiclePO = new VehiclePO();
        vehiclePO.setOwnerCode("ownerCode");
        vehiclePO.setOrgId(0);
        vehiclePO.setId(0L);
        vehiclePO.setVin("vin");
        vehiclePO.setOwnerNo("ownerNo");
        when(mockVehicleMapper.getVehicleByVin("vin")).thenReturn(vehiclePO);

        // Run the test
        final VehicleDTO result = vehicleServiceImplUnderTest.getVehicleByVin("vin");

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetVehicleByVin_VehicleMapperReturnsNull() {
        // Setup
        final VehicleDTO expectedResult = new VehicleDTO();
        expectedResult.setAppId("appId");
        expectedResult.setOwnerCode("ownerCode");
        expectedResult.setOwnerParCode("ownerParCode");
        expectedResult.setOrgId(0);
        expectedResult.setId(0L);

        when(mockVehicleMapper.getVehicleByVin("vin")).thenReturn(null);

        // Run the test
        final VehicleDTO result = vehicleServiceImplUnderTest.getVehicleByVin("vin");

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectDateByVin() {
        // Setup
        final VehicleDTO expectedResult = new VehicleDTO();
        expectedResult.setAppId("appId");
        expectedResult.setOwnerCode("ownerCode");
        expectedResult.setOwnerParCode("ownerParCode");
        expectedResult.setOrgId(0);
        expectedResult.setId(0L);

        // Configure VehicleMapper.selectDateByVin(...).
        final VehiclePO vehiclePO = new VehiclePO();
        vehiclePO.setOwnerCode("ownerCode");
        vehiclePO.setOrgId(0);
        vehiclePO.setId(0L);
        vehiclePO.setVin("vin");
        vehiclePO.setOwnerNo("ownerNo");
        when(mockVehicleMapper.selectDateByVin("vin")).thenReturn(vehiclePO);

        // Run the test
        final VehicleDTO result = vehicleServiceImplUnderTest.selectDateByVin("vin");

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }
}
