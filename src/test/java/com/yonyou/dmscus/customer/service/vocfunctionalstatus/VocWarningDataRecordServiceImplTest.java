package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yonyou.dmscus.customer.dao.voc.VocWarningDataRecordMapper;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataLogPo;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocWarningDataRecordPo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class VocWarningDataRecordServiceImplTest {

    @Mock
    private VocWarningDataRecordMapper mockVocWarningDataRecordMapper;

    private VocWarningDataRecordServiceImpl vocWarningDataRecordServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        vocWarningDataRecordServiceImplUnderTest = new VocWarningDataRecordServiceImpl();
        vocWarningDataRecordServiceImplUnderTest.vocWarningDataRecordMapper = mockVocWarningDataRecordMapper;
    }

    @Test
    void testSelectListWarningDataRecord() {
        // Setup
        final VocWarningDataLogPo vocWarningDataLogPo = new VocWarningDataLogPo();
        vocWarningDataLogPo.setId(0L);
        vocWarningDataLogPo.setIsDeleted(0);
        vocWarningDataLogPo.setDt("dt");
        vocWarningDataLogPo.setVin("vin");
        vocWarningDataLogPo.setStatusName("statusName");
        final List<VocWarningDataLogPo> x = Arrays.asList(vocWarningDataLogPo);
        final VocWarningDataRecordPo vocWarningDataRecordPo = new VocWarningDataRecordPo();
        vocWarningDataRecordPo.setId(0L);
        vocWarningDataRecordPo.setIsDeleted(0);
        vocWarningDataRecordPo.setModifyTime("modifyTime");
        vocWarningDataRecordPo.setVin("vin");
        vocWarningDataRecordPo.setStatusValue(0);
        final List<VocWarningDataRecordPo> expectedResult = Arrays.asList(vocWarningDataRecordPo);

        // Configure VocWarningDataRecordMapper.selectListWarningDataRecord(...).
        final VocWarningDataRecordPo vocWarningDataRecordPo1 = new VocWarningDataRecordPo();
        vocWarningDataRecordPo1.setId(0L);
        vocWarningDataRecordPo1.setIsDeleted(0);
        vocWarningDataRecordPo1.setModifyTime("modifyTime");
        vocWarningDataRecordPo1.setVin("vin");
        vocWarningDataRecordPo1.setStatusValue(0);
        final List<VocWarningDataRecordPo> vocWarningDataRecordPos = Arrays.asList(vocWarningDataRecordPo1);
        final VocWarningDataLogPo vocWarningDataLogPo1 = new VocWarningDataLogPo();
        vocWarningDataLogPo1.setId(0L);
        vocWarningDataLogPo1.setIsDeleted(0);
        vocWarningDataLogPo1.setDt("dt");
        vocWarningDataLogPo1.setVin("vin");
        vocWarningDataLogPo1.setStatusName("statusName");
        final List<VocWarningDataLogPo> x1 = Arrays.asList(vocWarningDataLogPo1);
        when(mockVocWarningDataRecordMapper.selectListWarningDataRecord(x1)).thenReturn(vocWarningDataRecordPos);

        // Run the test
        final List<VocWarningDataRecordPo> result = vocWarningDataRecordServiceImplUnderTest.selectListWarningDataRecord(
                x);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectListWarningDataRecord_VocWarningDataRecordMapperReturnsNoItems() {
        // Setup
        final VocWarningDataLogPo vocWarningDataLogPo = new VocWarningDataLogPo();
        vocWarningDataLogPo.setId(0L);
        vocWarningDataLogPo.setIsDeleted(0);
        vocWarningDataLogPo.setDt("dt");
        vocWarningDataLogPo.setVin("vin");
        vocWarningDataLogPo.setStatusName("statusName");
        final List<VocWarningDataLogPo> x = Arrays.asList(vocWarningDataLogPo);

        // Configure VocWarningDataRecordMapper.selectListWarningDataRecord(...).
        final VocWarningDataLogPo vocWarningDataLogPo1 = new VocWarningDataLogPo();
        vocWarningDataLogPo1.setId(0L);
        vocWarningDataLogPo1.setIsDeleted(0);
        vocWarningDataLogPo1.setDt("dt");
        vocWarningDataLogPo1.setVin("vin");
        vocWarningDataLogPo1.setStatusName("statusName");
        final List<VocWarningDataLogPo> x1 = Arrays.asList(vocWarningDataLogPo1);
        when(mockVocWarningDataRecordMapper.selectListWarningDataRecord(x1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<VocWarningDataRecordPo> result = vocWarningDataRecordServiceImplUnderTest.selectListWarningDataRecord(
                x);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testInsertList() {
        // Setup
        final VocWarningDataRecordPo vocWarningDataRecordPo = new VocWarningDataRecordPo();
        vocWarningDataRecordPo.setId(0L);
        vocWarningDataRecordPo.setIsDeleted(0);
        vocWarningDataRecordPo.setModifyTime("modifyTime");
        vocWarningDataRecordPo.setVin("vin");
        vocWarningDataRecordPo.setStatusValue(0);
        final List<VocWarningDataRecordPo> insertList = Arrays.asList(vocWarningDataRecordPo);

        // Run the test
        final int result = vocWarningDataRecordServiceImplUnderTest.insertList(insertList);

        // Verify the results
//        assertThat(result).isEqualTo(0);

        // Confirm VocWarningDataRecordMapper.insertList(...).
        final VocWarningDataRecordPo vocWarningDataRecordPo1 = new VocWarningDataRecordPo();
        vocWarningDataRecordPo1.setId(0L);
        vocWarningDataRecordPo1.setIsDeleted(0);
        vocWarningDataRecordPo1.setModifyTime("modifyTime");
        vocWarningDataRecordPo1.setVin("vin");
        vocWarningDataRecordPo1.setStatusValue(0);
        final List<VocWarningDataRecordPo> insertList1 = Arrays.asList(vocWarningDataRecordPo1);
        verify(mockVocWarningDataRecordMapper).insertList(insertList1);
    }

    @Test
    void testUpdateList() {
        // Setup
        final VocWarningDataRecordPo vocWarningDataRecordPo = new VocWarningDataRecordPo();
        vocWarningDataRecordPo.setId(0L);
        vocWarningDataRecordPo.setIsDeleted(0);
        vocWarningDataRecordPo.setModifyTime("modifyTime");
        vocWarningDataRecordPo.setVin("vin");
        vocWarningDataRecordPo.setStatusValue(0);
        final List<VocWarningDataRecordPo> updateList = Arrays.asList(vocWarningDataRecordPo);

        // Run the test
        final int result = vocWarningDataRecordServiceImplUnderTest.updateList(updateList);

        // Verify the results
//        assertThat(result).isEqualTo(0);

        // Confirm VocWarningDataRecordMapper.updateList(...).
        final VocWarningDataRecordPo vocWarningDataRecordPo1 = new VocWarningDataRecordPo();
        vocWarningDataRecordPo1.setId(0L);
        vocWarningDataRecordPo1.setIsDeleted(0);
        vocWarningDataRecordPo1.setModifyTime("modifyTime");
        vocWarningDataRecordPo1.setVin("vin");
        vocWarningDataRecordPo1.setStatusValue(0);
        final List<VocWarningDataRecordPo> updateList1 = Arrays.asList(vocWarningDataRecordPo1);
        verify(mockVocWarningDataRecordMapper).updateList(updateList1);
    }

    @Test
    void testUpdateWarningIsExecute() {
        // Setup
        final VocWarningDataRecordPo vocWarningDataRecordPo = new VocWarningDataRecordPo();
        vocWarningDataRecordPo.setId(0L);
        vocWarningDataRecordPo.setIsDeleted(0);
        vocWarningDataRecordPo.setModifyTime("modifyTime");
        vocWarningDataRecordPo.setVin("vin");
        vocWarningDataRecordPo.setStatusValue(0);
        final List<VocWarningDataRecordPo> list = Arrays.asList(vocWarningDataRecordPo);

        // Configure VocWarningDataRecordMapper.updateWarningIsExecute(...).
        final VocWarningDataRecordPo vocWarningDataRecordPo1 = new VocWarningDataRecordPo();
        vocWarningDataRecordPo1.setId(0L);
        vocWarningDataRecordPo1.setIsDeleted(0);
        vocWarningDataRecordPo1.setModifyTime("modifyTime");
        vocWarningDataRecordPo1.setVin("vin");
        vocWarningDataRecordPo1.setStatusValue(0);
        final List<VocWarningDataRecordPo> list1 = Arrays.asList(vocWarningDataRecordPo1);
        when(mockVocWarningDataRecordMapper.updateWarningIsExecute(list1)).thenReturn(0);

        // Run the test
        final int result = vocWarningDataRecordServiceImplUnderTest.updateWarningIsExecute(list);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectVocWarningDataRecordPoByModfiyTime() {
        // Setup
        final VocWarningDataRecordPo vocWarningDataRecordPo = new VocWarningDataRecordPo();
        vocWarningDataRecordPo.setId(0L);
        vocWarningDataRecordPo.setIsDeleted(0);
        vocWarningDataRecordPo.setModifyTime("modifyTime");
        vocWarningDataRecordPo.setVin("vin");
        vocWarningDataRecordPo.setStatusValue(0);
        final List<VocWarningDataRecordPo> expectedResult = Arrays.asList(vocWarningDataRecordPo);

        // Configure VocWarningDataRecordMapper.selectList(...).
        final VocWarningDataRecordPo vocWarningDataRecordPo1 = new VocWarningDataRecordPo();
        vocWarningDataRecordPo1.setId(0L);
        vocWarningDataRecordPo1.setIsDeleted(0);
        vocWarningDataRecordPo1.setModifyTime("modifyTime");
        vocWarningDataRecordPo1.setVin("vin");
        vocWarningDataRecordPo1.setStatusValue(0);
        final List<VocWarningDataRecordPo> vocWarningDataRecordPos = Arrays.asList(vocWarningDataRecordPo1);
        when(mockVocWarningDataRecordMapper.selectList(any(QueryWrapper.class))).thenReturn(vocWarningDataRecordPos);

        // Run the test
        final List<VocWarningDataRecordPo> result = vocWarningDataRecordServiceImplUnderTest.selectVocWarningDataRecordPoByModfiyTime(
                "dateTime", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectVocWarningDataRecordPoByModfiyTime_VocWarningDataRecordMapperReturnsNoItems() {
        // Setup
        when(mockVocWarningDataRecordMapper.selectList(any(QueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<VocWarningDataRecordPo> result = vocWarningDataRecordServiceImplUnderTest.selectVocWarningDataRecordPoByModfiyTime(
                "dateTime", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectKmByVin() {
        // Setup
        when(mockVocWarningDataRecordMapper.selectOneByVinAndDate("vin", "datetime", "endtime")).thenReturn("result");

        // Run the test
        final String result = vocWarningDataRecordServiceImplUnderTest.selectKmByVin("vin", "datetime", "endtime");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    void testSelectByVin() {
        // Setup
        when(mockVocWarningDataRecordMapper.selectCount(any(QueryWrapper.class))).thenReturn(0);

        // Run the test
        final int result = vocWarningDataRecordServiceImplUnderTest.selectByVin("vin");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectWarningdailyReodeByVin() {
        // Setup
        // Configure VocWarningDataRecordMapper.selectList(...).
        final VocWarningDataRecordPo vocWarningDataRecordPo = new VocWarningDataRecordPo();
        vocWarningDataRecordPo.setId(0L);
        vocWarningDataRecordPo.setIsDeleted(0);
        vocWarningDataRecordPo.setModifyTime("modifyTime");
        vocWarningDataRecordPo.setVin("vin");
        vocWarningDataRecordPo.setStatusValue(0);
        final List<VocWarningDataRecordPo> vocWarningDataRecordPos = Arrays.asList(vocWarningDataRecordPo);
        when(mockVocWarningDataRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(vocWarningDataRecordPos);

        // Run the test
        final int result = vocWarningDataRecordServiceImplUnderTest.selectWarningdailyReodeByVin("vin");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectWarningdailyReodeByVin_VocWarningDataRecordMapperReturnsNoItems() {
        // Setup
        when(mockVocWarningDataRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final int result = vocWarningDataRecordServiceImplUnderTest.selectWarningdailyReodeByVin("vin");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectVocWarningDataRecordPo() {
        // Setup
        final VocWarningDataRecordPo expectedResult = new VocWarningDataRecordPo();
        expectedResult.setId(0L);
        expectedResult.setIsDeleted(0);
        expectedResult.setModifyTime("modifyTime");
        expectedResult.setVin("vin");
        expectedResult.setStatusValue(0);

        // Configure VocWarningDataRecordMapper.selectList(...).
        final VocWarningDataRecordPo vocWarningDataRecordPo = new VocWarningDataRecordPo();
        vocWarningDataRecordPo.setId(0L);
        vocWarningDataRecordPo.setIsDeleted(0);
        vocWarningDataRecordPo.setModifyTime("modifyTime");
        vocWarningDataRecordPo.setVin("vin");
        vocWarningDataRecordPo.setStatusValue(0);
        final List<VocWarningDataRecordPo> vocWarningDataRecordPos = Arrays.asList(vocWarningDataRecordPo);
        when(mockVocWarningDataRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(vocWarningDataRecordPos);

        // Run the test
        final VocWarningDataRecordPo result = vocWarningDataRecordServiceImplUnderTest.selectVocWarningDataRecordPo(
                "vin");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectVocWarningDataRecordPo_VocWarningDataRecordMapperReturnsNoItems() {
        // Setup
        final VocWarningDataRecordPo expectedResult = new VocWarningDataRecordPo();
        expectedResult.setId(0L);
        expectedResult.setIsDeleted(0);
        expectedResult.setModifyTime("modifyTime");
        expectedResult.setVin("vin");
        expectedResult.setStatusValue(0);

        when(mockVocWarningDataRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final VocWarningDataRecordPo result = vocWarningDataRecordServiceImplUnderTest.selectVocWarningDataRecordPo(
                "vin");

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectCountByVinAndModfiy() {
        // Setup
        when(mockVocWarningDataRecordMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final int result = vocWarningDataRecordServiceImplUnderTest.selectCountByVinAndModfiy("vin", "dateTime");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
