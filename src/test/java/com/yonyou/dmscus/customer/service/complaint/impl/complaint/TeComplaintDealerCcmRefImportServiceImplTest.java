package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.TeComplaintDealerCcmRefImportMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.TeComplaintDealerCcmRefImportDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.TeComplaintDealerCcmRefImportPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TeComplaintDealerCcmRefImportServiceImplTest {

    @Mock
    private TeComplaintDealerCcmRefImportMapper mockTeComplaintDealerCcmRefImportMapper;

    private TeComplaintDealerCcmRefImportServiceImpl teComplaintDealerCcmRefImportServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        teComplaintDealerCcmRefImportServiceImplUnderTest = new TeComplaintDealerCcmRefImportServiceImpl();
        teComplaintDealerCcmRefImportServiceImplUnderTest.teComplaintDealerCcmRefImportMapper = mockTeComplaintDealerCcmRefImportMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO = new TeComplaintDealerCcmRefImportDTO();
        teComplaintDealerCcmRefImportDTO.setAppId("appId");
        teComplaintDealerCcmRefImportDTO.setOwnerCode("ownerCode");
        teComplaintDealerCcmRefImportDTO.setOwnerParCode("ownerParCode");
        teComplaintDealerCcmRefImportDTO.setOrgId(0);
        teComplaintDealerCcmRefImportDTO.setId(0L);

        // Configure TeComplaintDealerCcmRefImportMapper.selectPageBySql(...).
        final TeComplaintDealerCcmRefImportPO teComplaintDealerCcmRefImportPO = new TeComplaintDealerCcmRefImportPO();
        teComplaintDealerCcmRefImportPO.setAppId("appId");
        teComplaintDealerCcmRefImportPO.setOwnerCode("ownerCode");
        teComplaintDealerCcmRefImportPO.setOwnerParCode("ownerParCode");
        teComplaintDealerCcmRefImportPO.setOrgId(0);
        teComplaintDealerCcmRefImportPO.setId(0L);
        final List<TeComplaintDealerCcmRefImportPO> teComplaintDealerCcmRefImportPOS = Arrays.asList(
                teComplaintDealerCcmRefImportPO);
        when(mockTeComplaintDealerCcmRefImportMapper.selectPageBySql(any(Page.class),
                any(TeComplaintDealerCcmRefImportPO.class))).thenReturn(teComplaintDealerCcmRefImportPOS);

        // Run the test
        final IPage<TeComplaintDealerCcmRefImportDTO> result = teComplaintDealerCcmRefImportServiceImplUnderTest.selectPageBysql(
                page, teComplaintDealerCcmRefImportDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_TeComplaintDealerCcmRefImportMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO = new TeComplaintDealerCcmRefImportDTO();
        teComplaintDealerCcmRefImportDTO.setAppId("appId");
        teComplaintDealerCcmRefImportDTO.setOwnerCode("ownerCode");
        teComplaintDealerCcmRefImportDTO.setOwnerParCode("ownerParCode");
        teComplaintDealerCcmRefImportDTO.setOrgId(0);
        teComplaintDealerCcmRefImportDTO.setId(0L);

        when(mockTeComplaintDealerCcmRefImportMapper.selectPageBySql(any(Page.class),
                any(TeComplaintDealerCcmRefImportPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<TeComplaintDealerCcmRefImportDTO> result = teComplaintDealerCcmRefImportServiceImplUnderTest.selectPageBysql(
                page, teComplaintDealerCcmRefImportDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO = new TeComplaintDealerCcmRefImportDTO();
        teComplaintDealerCcmRefImportDTO.setAppId("appId");
        teComplaintDealerCcmRefImportDTO.setOwnerCode("ownerCode");
        teComplaintDealerCcmRefImportDTO.setOwnerParCode("ownerParCode");
        teComplaintDealerCcmRefImportDTO.setOrgId(0);
        teComplaintDealerCcmRefImportDTO.setId(0L);

        // Configure TeComplaintDealerCcmRefImportMapper.selectListBySql(...).
        final TeComplaintDealerCcmRefImportPO teComplaintDealerCcmRefImportPO = new TeComplaintDealerCcmRefImportPO();
        teComplaintDealerCcmRefImportPO.setAppId("appId");
        teComplaintDealerCcmRefImportPO.setOwnerCode("ownerCode");
        teComplaintDealerCcmRefImportPO.setOwnerParCode("ownerParCode");
        teComplaintDealerCcmRefImportPO.setOrgId(0);
        teComplaintDealerCcmRefImportPO.setId(0L);
        final List<TeComplaintDealerCcmRefImportPO> teComplaintDealerCcmRefImportPOS = Arrays.asList(
                teComplaintDealerCcmRefImportPO);
        when(mockTeComplaintDealerCcmRefImportMapper.selectListBySql(
                any(TeComplaintDealerCcmRefImportPO.class))).thenReturn(teComplaintDealerCcmRefImportPOS);

        // Run the test
        final List<TeComplaintDealerCcmRefImportDTO> result = teComplaintDealerCcmRefImportServiceImplUnderTest.selectListBySql(
                teComplaintDealerCcmRefImportDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_TeComplaintDealerCcmRefImportMapperReturnsNoItems() {
        // Setup
        final TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO = new TeComplaintDealerCcmRefImportDTO();
        teComplaintDealerCcmRefImportDTO.setAppId("appId");
        teComplaintDealerCcmRefImportDTO.setOwnerCode("ownerCode");
        teComplaintDealerCcmRefImportDTO.setOwnerParCode("ownerParCode");
        teComplaintDealerCcmRefImportDTO.setOrgId(0);
        teComplaintDealerCcmRefImportDTO.setId(0L);

        when(mockTeComplaintDealerCcmRefImportMapper.selectListBySql(
                any(TeComplaintDealerCcmRefImportPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<TeComplaintDealerCcmRefImportDTO> result = teComplaintDealerCcmRefImportServiceImplUnderTest.selectListBySql(
                teComplaintDealerCcmRefImportDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure TeComplaintDealerCcmRefImportMapper.selectById(...).
        final TeComplaintDealerCcmRefImportPO teComplaintDealerCcmRefImportPO = new TeComplaintDealerCcmRefImportPO();
        teComplaintDealerCcmRefImportPO.setAppId("appId");
        teComplaintDealerCcmRefImportPO.setOwnerCode("ownerCode");
        teComplaintDealerCcmRefImportPO.setOwnerParCode("ownerParCode");
        teComplaintDealerCcmRefImportPO.setOrgId(0);
        teComplaintDealerCcmRefImportPO.setId(0L);
        when(mockTeComplaintDealerCcmRefImportMapper.selectById(0L)).thenReturn(teComplaintDealerCcmRefImportPO);

        // Run the test
        final TeComplaintDealerCcmRefImportDTO result = teComplaintDealerCcmRefImportServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_TeComplaintDealerCcmRefImportMapperReturnsNull() {
        // Setup
        when(mockTeComplaintDealerCcmRefImportMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> teComplaintDealerCcmRefImportServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO = new TeComplaintDealerCcmRefImportDTO();
        teComplaintDealerCcmRefImportDTO.setAppId("appId");
        teComplaintDealerCcmRefImportDTO.setOwnerCode("ownerCode");
        teComplaintDealerCcmRefImportDTO.setOwnerParCode("ownerParCode");
        teComplaintDealerCcmRefImportDTO.setOrgId(0);
        teComplaintDealerCcmRefImportDTO.setId(0L);

        when(mockTeComplaintDealerCcmRefImportMapper.insert(any(TeComplaintDealerCcmRefImportPO.class))).thenReturn(0);

        // Run the test
        final int result = teComplaintDealerCcmRefImportServiceImplUnderTest.insert(teComplaintDealerCcmRefImportDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final TeComplaintDealerCcmRefImportDTO teComplaintDealerCcmRefImportDTO = new TeComplaintDealerCcmRefImportDTO();
        teComplaintDealerCcmRefImportDTO.setAppId("appId");
        teComplaintDealerCcmRefImportDTO.setOwnerCode("ownerCode");
        teComplaintDealerCcmRefImportDTO.setOwnerParCode("ownerParCode");
        teComplaintDealerCcmRefImportDTO.setOrgId(0);
        teComplaintDealerCcmRefImportDTO.setId(0L);

        // Configure TeComplaintDealerCcmRefImportMapper.selectById(...).
        final TeComplaintDealerCcmRefImportPO teComplaintDealerCcmRefImportPO = new TeComplaintDealerCcmRefImportPO();
        teComplaintDealerCcmRefImportPO.setAppId("appId");
        teComplaintDealerCcmRefImportPO.setOwnerCode("ownerCode");
        teComplaintDealerCcmRefImportPO.setOwnerParCode("ownerParCode");
        teComplaintDealerCcmRefImportPO.setOrgId(0);
        teComplaintDealerCcmRefImportPO.setId(0L);
        when(mockTeComplaintDealerCcmRefImportMapper.selectById(0L)).thenReturn(teComplaintDealerCcmRefImportPO);

        when(mockTeComplaintDealerCcmRefImportMapper.updateById(any(TeComplaintDealerCcmRefImportPO.class)))
                .thenReturn(0);

        // Run the test
        final int result = teComplaintDealerCcmRefImportServiceImplUnderTest.update(0L,
                teComplaintDealerCcmRefImportDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
