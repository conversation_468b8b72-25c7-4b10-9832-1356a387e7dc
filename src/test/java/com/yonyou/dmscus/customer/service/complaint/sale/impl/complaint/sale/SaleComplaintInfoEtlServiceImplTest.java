package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintInfoEtlMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoEtlDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoEtlPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SaleComplaintInfoEtlServiceImplTest {

    @Mock
    private SaleComplaintInfoEtlMapper mockSaleComplaintInfoEtlMapper;

    private SaleComplaintInfoEtlServiceImpl saleComplaintInfoEtlServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        saleComplaintInfoEtlServiceImplUnderTest = new SaleComplaintInfoEtlServiceImpl();
        saleComplaintInfoEtlServiceImplUnderTest.saleComplaintInfoEtlMapper = mockSaleComplaintInfoEtlMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO = new SaleComplaintInfoEtlDTO();
        saleComplaintInfoEtlDTO.setId(0L);
        saleComplaintInfoEtlDTO.setCallTime("callTime");
        saleComplaintInfoEtlDTO.setComplaintId("complaintId");
        saleComplaintInfoEtlDTO.setWorkOrderNature("workOrderNature");
        saleComplaintInfoEtlDTO.setCategory1("category1");

        // Configure SaleComplaintInfoEtlMapper.selectPageBySql(...).
        final SaleComplaintInfoEtlPO saleComplaintInfoEtlPO = new SaleComplaintInfoEtlPO();
        saleComplaintInfoEtlPO.setId(0L);
        saleComplaintInfoEtlPO.setCallTime("callTime");
        saleComplaintInfoEtlPO.setComplaintId("complaintId");
        saleComplaintInfoEtlPO.setWorkOrderNature("workOrderNature");
        saleComplaintInfoEtlPO.setCategory1("category1");
        final List<SaleComplaintInfoEtlPO> saleComplaintInfoEtlPOS = Arrays.asList(saleComplaintInfoEtlPO);
        when(mockSaleComplaintInfoEtlMapper.selectPageBySql(any(Page.class),
                any(SaleComplaintInfoEtlPO.class))).thenReturn(saleComplaintInfoEtlPOS);

        // Run the test
        final IPage<SaleComplaintInfoEtlDTO> result = saleComplaintInfoEtlServiceImplUnderTest.selectPageBysql(page,
                saleComplaintInfoEtlDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_SaleComplaintInfoEtlMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO = new SaleComplaintInfoEtlDTO();
        saleComplaintInfoEtlDTO.setId(0L);
        saleComplaintInfoEtlDTO.setCallTime("callTime");
        saleComplaintInfoEtlDTO.setComplaintId("complaintId");
        saleComplaintInfoEtlDTO.setWorkOrderNature("workOrderNature");
        saleComplaintInfoEtlDTO.setCategory1("category1");

        when(mockSaleComplaintInfoEtlMapper.selectPageBySql(any(Page.class),
                any(SaleComplaintInfoEtlPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<SaleComplaintInfoEtlDTO> result = saleComplaintInfoEtlServiceImplUnderTest.selectPageBysql(page,
                saleComplaintInfoEtlDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO = new SaleComplaintInfoEtlDTO();
        saleComplaintInfoEtlDTO.setId(0L);
        saleComplaintInfoEtlDTO.setCallTime("callTime");
        saleComplaintInfoEtlDTO.setComplaintId("complaintId");
        saleComplaintInfoEtlDTO.setWorkOrderNature("workOrderNature");
        saleComplaintInfoEtlDTO.setCategory1("category1");

        // Configure SaleComplaintInfoEtlMapper.selectListBySql(...).
        final SaleComplaintInfoEtlPO saleComplaintInfoEtlPO = new SaleComplaintInfoEtlPO();
        saleComplaintInfoEtlPO.setId(0L);
        saleComplaintInfoEtlPO.setCallTime("callTime");
        saleComplaintInfoEtlPO.setComplaintId("complaintId");
        saleComplaintInfoEtlPO.setWorkOrderNature("workOrderNature");
        saleComplaintInfoEtlPO.setCategory1("category1");
        final List<SaleComplaintInfoEtlPO> saleComplaintInfoEtlPOS = Arrays.asList(saleComplaintInfoEtlPO);
        when(mockSaleComplaintInfoEtlMapper.selectListBySql(any(SaleComplaintInfoEtlPO.class)))
                .thenReturn(saleComplaintInfoEtlPOS);

        // Run the test
        final List<SaleComplaintInfoEtlDTO> result = saleComplaintInfoEtlServiceImplUnderTest.selectListBySql(
                saleComplaintInfoEtlDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_SaleComplaintInfoEtlMapperReturnsNoItems() {
        // Setup
        final SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO = new SaleComplaintInfoEtlDTO();
        saleComplaintInfoEtlDTO.setId(0L);
        saleComplaintInfoEtlDTO.setCallTime("callTime");
        saleComplaintInfoEtlDTO.setComplaintId("complaintId");
        saleComplaintInfoEtlDTO.setWorkOrderNature("workOrderNature");
        saleComplaintInfoEtlDTO.setCategory1("category1");

        when(mockSaleComplaintInfoEtlMapper.selectListBySql(any(SaleComplaintInfoEtlPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<SaleComplaintInfoEtlDTO> result = saleComplaintInfoEtlServiceImplUnderTest.selectListBySql(
                saleComplaintInfoEtlDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure SaleComplaintInfoEtlMapper.selectById(...).
        final SaleComplaintInfoEtlPO saleComplaintInfoEtlPO = new SaleComplaintInfoEtlPO();
        saleComplaintInfoEtlPO.setId(0L);
        saleComplaintInfoEtlPO.setCallTime("callTime");
        saleComplaintInfoEtlPO.setComplaintId("complaintId");
        saleComplaintInfoEtlPO.setWorkOrderNature("workOrderNature");
        saleComplaintInfoEtlPO.setCategory1("category1");
        when(mockSaleComplaintInfoEtlMapper.selectById(0L)).thenReturn(saleComplaintInfoEtlPO);

        // Run the test
        final SaleComplaintInfoEtlDTO result = saleComplaintInfoEtlServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_SaleComplaintInfoEtlMapperReturnsNull() {
        // Setup
        when(mockSaleComplaintInfoEtlMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> saleComplaintInfoEtlServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO = new SaleComplaintInfoEtlDTO();
        saleComplaintInfoEtlDTO.setId(0L);
        saleComplaintInfoEtlDTO.setCallTime("callTime");
        saleComplaintInfoEtlDTO.setComplaintId("complaintId");
        saleComplaintInfoEtlDTO.setWorkOrderNature("workOrderNature");
        saleComplaintInfoEtlDTO.setCategory1("category1");

        when(mockSaleComplaintInfoEtlMapper.insert(any(SaleComplaintInfoEtlPO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintInfoEtlServiceImplUnderTest.insert(saleComplaintInfoEtlDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO = new SaleComplaintInfoEtlDTO();
        saleComplaintInfoEtlDTO.setId(0L);
        saleComplaintInfoEtlDTO.setCallTime("callTime");
        saleComplaintInfoEtlDTO.setComplaintId("complaintId");
        saleComplaintInfoEtlDTO.setWorkOrderNature("workOrderNature");
        saleComplaintInfoEtlDTO.setCategory1("category1");

        // Configure SaleComplaintInfoEtlMapper.selectById(...).
        final SaleComplaintInfoEtlPO saleComplaintInfoEtlPO = new SaleComplaintInfoEtlPO();
        saleComplaintInfoEtlPO.setId(0L);
        saleComplaintInfoEtlPO.setCallTime("callTime");
        saleComplaintInfoEtlPO.setComplaintId("complaintId");
        saleComplaintInfoEtlPO.setWorkOrderNature("workOrderNature");
        saleComplaintInfoEtlPO.setCategory1("category1");
        when(mockSaleComplaintInfoEtlMapper.selectById(0L)).thenReturn(saleComplaintInfoEtlPO);

        when(mockSaleComplaintInfoEtlMapper.updateById(any(SaleComplaintInfoEtlPO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintInfoEtlServiceImplUnderTest.update(0L, saleComplaintInfoEtlDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testExportSaleComplaintHistory() {
        // Setup
        final SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO = new SaleComplaintInfoEtlDTO();
        saleComplaintInfoEtlDTO.setId(0L);
        saleComplaintInfoEtlDTO.setCallTime("callTime");
        saleComplaintInfoEtlDTO.setComplaintId("complaintId");
        saleComplaintInfoEtlDTO.setWorkOrderNature("workOrderNature");
        saleComplaintInfoEtlDTO.setCategory1("category1");

        when(mockSaleComplaintInfoEtlMapper.exportSaleComplaintHistory(any(SaleComplaintInfoEtlDTO.class)))
                .thenReturn(Arrays.asList(new HashMap<>()));

        // Run the test
        final List<Map> result = saleComplaintInfoEtlServiceImplUnderTest.exportSaleComplaintHistory(
                saleComplaintInfoEtlDTO);

        // Verify the results
    }

    @Test
    void testExportSaleComplaintHistory_SaleComplaintInfoEtlMapperReturnsNoItems() {
        // Setup
        final SaleComplaintInfoEtlDTO saleComplaintInfoEtlDTO = new SaleComplaintInfoEtlDTO();
        saleComplaintInfoEtlDTO.setId(0L);
        saleComplaintInfoEtlDTO.setCallTime("callTime");
        saleComplaintInfoEtlDTO.setComplaintId("complaintId");
        saleComplaintInfoEtlDTO.setWorkOrderNature("workOrderNature");
        saleComplaintInfoEtlDTO.setCategory1("category1");

        when(mockSaleComplaintInfoEtlMapper.exportSaleComplaintHistory(any(SaleComplaintInfoEtlDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<Map> result = saleComplaintInfoEtlServiceImplUnderTest.exportSaleComplaintHistory(
                saleComplaintInfoEtlDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
