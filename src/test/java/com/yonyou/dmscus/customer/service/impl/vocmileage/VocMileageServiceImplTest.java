package com.yonyou.dmscus.customer.service.impl.vocmileage;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.common.AjaxResponse;
import com.yonyou.dmscus.customer.dao.vocmileage.VocMileageMapper;
import com.yonyou.dmscus.customer.entity.dto.vocmileage.VocMileageDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class VocMileageServiceImplTest {

    @Mock
    private VocMileageMapper mockVocMileageMapper;

    @InjectMocks
    private VocMileageServiceImpl vocMileageServiceImplUnderTest;

    @Test
    void testImportExcelData() {
        // Setup
        final List<Map<Integer, String>> importDataList = Arrays.asList(new HashMap<>());

        // Run the test
        final AjaxResponse result = vocMileageServiceImplUnderTest.importExcelData(0L, "operationName", importDataList);

        // Verify the results
    }


}
