package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillInvoiceTitleInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillInvoiceTitleInfoDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillInvoiceTitleInfoPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillInvoiceTitleInfoServiceImplTest {

    @Mock
    private GoodwillInvoiceTitleInfoMapper mockGoodwillInvoiceTitleInfoMapper;

    private GoodwillInvoiceTitleInfoServiceImpl goodwillInvoiceTitleInfoServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        goodwillInvoiceTitleInfoServiceImplUnderTest = new GoodwillInvoiceTitleInfoServiceImpl();
        goodwillInvoiceTitleInfoServiceImplUnderTest.goodwillInvoiceTitleInfoMapper = mockGoodwillInvoiceTitleInfoMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO = new GoodwillInvoiceTitleInfoDTO();
        goodwillInvoiceTitleInfoDTO.setAppId("appId");
        goodwillInvoiceTitleInfoDTO.setOwnerCode("ownerCode");
        goodwillInvoiceTitleInfoDTO.setOwnerParCode("ownerParCode");
        goodwillInvoiceTitleInfoDTO.setId(0L);
        goodwillInvoiceTitleInfoDTO.setInvoiceTitle(0);

        // Configure GoodwillInvoiceTitleInfoMapper.selectPageBySql(...).
        final GoodwillInvoiceTitleInfoPO goodwillInvoiceTitleInfoPO = new GoodwillInvoiceTitleInfoPO();
        goodwillInvoiceTitleInfoPO.setAppId("appId");
        goodwillInvoiceTitleInfoPO.setOwnerCode("ownerCode");
        goodwillInvoiceTitleInfoPO.setOwnerParCode("ownerParCode");
        goodwillInvoiceTitleInfoPO.setOrgId(0);
        goodwillInvoiceTitleInfoPO.setIsValid(0);
        final List<GoodwillInvoiceTitleInfoPO> goodwillInvoiceTitleInfoPOS = Arrays.asList(goodwillInvoiceTitleInfoPO);
        when(mockGoodwillInvoiceTitleInfoMapper.selectPageBySql(any(Page.class),
                any(GoodwillInvoiceTitleInfoPO.class))).thenReturn(goodwillInvoiceTitleInfoPOS);

        // Run the test
        final IPage<GoodwillInvoiceTitleInfoDTO> result = goodwillInvoiceTitleInfoServiceImplUnderTest.selectPageBysql(
                page, goodwillInvoiceTitleInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillInvoiceTitleInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO = new GoodwillInvoiceTitleInfoDTO();
        goodwillInvoiceTitleInfoDTO.setAppId("appId");
        goodwillInvoiceTitleInfoDTO.setOwnerCode("ownerCode");
        goodwillInvoiceTitleInfoDTO.setOwnerParCode("ownerParCode");
        goodwillInvoiceTitleInfoDTO.setId(0L);
        goodwillInvoiceTitleInfoDTO.setInvoiceTitle(0);

        when(mockGoodwillInvoiceTitleInfoMapper.selectPageBySql(any(Page.class),
                any(GoodwillInvoiceTitleInfoPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillInvoiceTitleInfoDTO> result = goodwillInvoiceTitleInfoServiceImplUnderTest.selectPageBysql(
                page, goodwillInvoiceTitleInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO = new GoodwillInvoiceTitleInfoDTO();
        goodwillInvoiceTitleInfoDTO.setAppId("appId");
        goodwillInvoiceTitleInfoDTO.setOwnerCode("ownerCode");
        goodwillInvoiceTitleInfoDTO.setOwnerParCode("ownerParCode");
        goodwillInvoiceTitleInfoDTO.setId(0L);
        goodwillInvoiceTitleInfoDTO.setInvoiceTitle(0);

        // Configure GoodwillInvoiceTitleInfoMapper.selectListBySql(...).
        final GoodwillInvoiceTitleInfoPO goodwillInvoiceTitleInfoPO = new GoodwillInvoiceTitleInfoPO();
        goodwillInvoiceTitleInfoPO.setAppId("appId");
        goodwillInvoiceTitleInfoPO.setOwnerCode("ownerCode");
        goodwillInvoiceTitleInfoPO.setOwnerParCode("ownerParCode");
        goodwillInvoiceTitleInfoPO.setOrgId(0);
        goodwillInvoiceTitleInfoPO.setIsValid(0);
        final List<GoodwillInvoiceTitleInfoPO> goodwillInvoiceTitleInfoPOS = Arrays.asList(goodwillInvoiceTitleInfoPO);
        when(mockGoodwillInvoiceTitleInfoMapper.selectListBySql(any(GoodwillInvoiceTitleInfoPO.class)))
                .thenReturn(goodwillInvoiceTitleInfoPOS);

        // Run the test
        final List<GoodwillInvoiceTitleInfoDTO> result = goodwillInvoiceTitleInfoServiceImplUnderTest.selectListBySql(
                goodwillInvoiceTitleInfoDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillInvoiceTitleInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO = new GoodwillInvoiceTitleInfoDTO();
        goodwillInvoiceTitleInfoDTO.setAppId("appId");
        goodwillInvoiceTitleInfoDTO.setOwnerCode("ownerCode");
        goodwillInvoiceTitleInfoDTO.setOwnerParCode("ownerParCode");
        goodwillInvoiceTitleInfoDTO.setId(0L);
        goodwillInvoiceTitleInfoDTO.setInvoiceTitle(0);

        when(mockGoodwillInvoiceTitleInfoMapper.selectListBySql(any(GoodwillInvoiceTitleInfoPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillInvoiceTitleInfoDTO> result = goodwillInvoiceTitleInfoServiceImplUnderTest.selectListBySql(
                goodwillInvoiceTitleInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure GoodwillInvoiceTitleInfoMapper.selectById(...).
        final GoodwillInvoiceTitleInfoPO goodwillInvoiceTitleInfoPO = new GoodwillInvoiceTitleInfoPO();
        goodwillInvoiceTitleInfoPO.setAppId("appId");
        goodwillInvoiceTitleInfoPO.setOwnerCode("ownerCode");
        goodwillInvoiceTitleInfoPO.setOwnerParCode("ownerParCode");
        goodwillInvoiceTitleInfoPO.setOrgId(0);
        goodwillInvoiceTitleInfoPO.setIsValid(0);
        when(mockGoodwillInvoiceTitleInfoMapper.selectById(0L)).thenReturn(goodwillInvoiceTitleInfoPO);

        // Run the test
        final GoodwillInvoiceTitleInfoDTO result = goodwillInvoiceTitleInfoServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillInvoiceTitleInfoMapperReturnsNull() {
        // Setup
        when(mockGoodwillInvoiceTitleInfoMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillInvoiceTitleInfoServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO = new GoodwillInvoiceTitleInfoDTO();
        goodwillInvoiceTitleInfoDTO.setAppId("appId");
        goodwillInvoiceTitleInfoDTO.setOwnerCode("ownerCode");
        goodwillInvoiceTitleInfoDTO.setOwnerParCode("ownerParCode");
        goodwillInvoiceTitleInfoDTO.setId(0L);
        goodwillInvoiceTitleInfoDTO.setInvoiceTitle(0);

        when(mockGoodwillInvoiceTitleInfoMapper.insert(any(GoodwillInvoiceTitleInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillInvoiceTitleInfoServiceImplUnderTest.insert(goodwillInvoiceTitleInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final GoodwillInvoiceTitleInfoDTO goodwillInvoiceTitleInfoDTO = new GoodwillInvoiceTitleInfoDTO();
        goodwillInvoiceTitleInfoDTO.setAppId("appId");
        goodwillInvoiceTitleInfoDTO.setOwnerCode("ownerCode");
        goodwillInvoiceTitleInfoDTO.setOwnerParCode("ownerParCode");
        goodwillInvoiceTitleInfoDTO.setId(0L);
        goodwillInvoiceTitleInfoDTO.setInvoiceTitle(0);

        // Configure GoodwillInvoiceTitleInfoMapper.selectById(...).
        final GoodwillInvoiceTitleInfoPO goodwillInvoiceTitleInfoPO = new GoodwillInvoiceTitleInfoPO();
        goodwillInvoiceTitleInfoPO.setAppId("appId");
        goodwillInvoiceTitleInfoPO.setOwnerCode("ownerCode");
        goodwillInvoiceTitleInfoPO.setOwnerParCode("ownerParCode");
        goodwillInvoiceTitleInfoPO.setOrgId(0);
        goodwillInvoiceTitleInfoPO.setIsValid(0);
        when(mockGoodwillInvoiceTitleInfoMapper.selectById(0L)).thenReturn(goodwillInvoiceTitleInfoPO);

        when(mockGoodwillInvoiceTitleInfoMapper.updateById(any(GoodwillInvoiceTitleInfoPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillInvoiceTitleInfoServiceImplUnderTest.update(0L, goodwillInvoiceTitleInfoDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testQueryInvoiceInfo() throws Exception {
        // Setup
        // Configure GoodwillInvoiceTitleInfoMapper.queryInvoiceInfo(...).
        final GoodwillInvoiceTitleInfoPO goodwillInvoiceTitleInfoPO = new GoodwillInvoiceTitleInfoPO();
        goodwillInvoiceTitleInfoPO.setAppId("appId");
        goodwillInvoiceTitleInfoPO.setOwnerCode("ownerCode");
        goodwillInvoiceTitleInfoPO.setOwnerParCode("ownerParCode");
        goodwillInvoiceTitleInfoPO.setOrgId(0);
        goodwillInvoiceTitleInfoPO.setIsValid(0);
        when(mockGoodwillInvoiceTitleInfoMapper.queryInvoiceInfo(0)).thenReturn(goodwillInvoiceTitleInfoPO);

        // Run the test
        final GoodwillInvoiceTitleInfoDTO result = goodwillInvoiceTitleInfoServiceImplUnderTest.queryInvoiceInfo(0);

        // Verify the results
    }

    @Test
    void testQueryInvoiceInfo_GoodwillInvoiceTitleInfoMapperReturnsNull() {
        // Setup
        when(mockGoodwillInvoiceTitleInfoMapper.queryInvoiceInfo(0)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillInvoiceTitleInfoServiceImplUnderTest.queryInvoiceInfo(0))
                .isInstanceOf(DALException.class);
    }
}
