package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintFollowMapper;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintInfoMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintInfMoreDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintFollowDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintInfoDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintFollowPO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintInfoPO;
import com.yonyou.dmscus.customer.service.complaint.sale.SaleComplaintInfoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SaleComplaintFollowServiceImplTest {

    @Mock
    private SaleComplaintFollowMapper mockSaleComplaintFollowMapper;
    @Mock
    private SaleComplaintInfoService mockSaleComplaintInfoService;
    @Mock
    private SaleComplaintInfoMapper mockSaleComplaintInfoMapper;

    private SaleComplaintFollowServiceImpl saleComplaintFollowServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        saleComplaintFollowServiceImplUnderTest = new SaleComplaintFollowServiceImpl();
        saleComplaintFollowServiceImplUnderTest.saleComplaintFollowMapper = mockSaleComplaintFollowMapper;
        saleComplaintFollowServiceImplUnderTest.saleComplaintInfoService = mockSaleComplaintInfoService;
        saleComplaintFollowServiceImplUnderTest.saleComplaintInfoMapper = mockSaleComplaintInfoMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintFollowDTO saleComplaintFollowDTO = new SaleComplaintFollowDTO();
        saleComplaintFollowDTO.setComplaintInfoId(0L);
        saleComplaintFollowDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintFollowDTO.setFollower("follower");
        saleComplaintFollowDTO.setFollowContent("followContent");
        saleComplaintFollowDTO.setPlanFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintFollowDTO.setActuallFollowTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintFollowDTO.setFollowerName("followerName");
        saleComplaintFollowDTO.setCcmNotPublish(false);
        saleComplaintFollowDTO.setDealerNotPublish(false);

        // Configure SaleComplaintFollowMapper.selectPageBySql(...).
        final SaleComplaintFollowPO saleComplaintFollowPO = new SaleComplaintFollowPO();
        saleComplaintFollowPO.setAppId("appId");
        saleComplaintFollowPO.setOwnerCode("ownerCode");
        saleComplaintFollowPO.setOwnerParCode("ownerParCode");
        saleComplaintFollowPO.setOrgId(0);
        saleComplaintFollowPO.setId(0L);
        final List<SaleComplaintFollowPO> saleComplaintFollowPOS = Arrays.asList(saleComplaintFollowPO);
        when(mockSaleComplaintFollowMapper.selectPageBySql(any(Page.class),
                any(SaleComplaintFollowPO.class))).thenReturn(saleComplaintFollowPOS);

        // Run the test
        final IPage<SaleComplaintFollowDTO> result = saleComplaintFollowServiceImplUnderTest.selectPageBysql(page,
                saleComplaintFollowDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_SaleComplaintFollowMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintFollowDTO saleComplaintFollowDTO = new SaleComplaintFollowDTO();
        saleComplaintFollowDTO.setComplaintInfoId(0L);
        saleComplaintFollowDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintFollowDTO.setFollower("follower");
        saleComplaintFollowDTO.setFollowContent("followContent");
        saleComplaintFollowDTO.setPlanFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintFollowDTO.setActuallFollowTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintFollowDTO.setFollowerName("followerName");
        saleComplaintFollowDTO.setCcmNotPublish(false);
        saleComplaintFollowDTO.setDealerNotPublish(false);

        when(mockSaleComplaintFollowMapper.selectPageBySql(any(Page.class),
                any(SaleComplaintFollowPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<SaleComplaintFollowDTO> result = saleComplaintFollowServiceImplUnderTest.selectPageBysql(page,
                saleComplaintFollowDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_SaleComplaintFollowMapperSelectListByVcdcReturnsNoItems() {
        // Setup
        final SaleComplaintFollowDTO saleComplaintFollowDTO = new SaleComplaintFollowDTO();
        saleComplaintFollowDTO.setComplaintInfoId(0L);
        saleComplaintFollowDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintFollowDTO.setFollower("follower");
        saleComplaintFollowDTO.setFollowContent("followContent");
        saleComplaintFollowDTO.setPlanFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintFollowDTO.setActuallFollowTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintFollowDTO.setFollowerName("followerName");
        saleComplaintFollowDTO.setCcmNotPublish(false);
        saleComplaintFollowDTO.setDealerNotPublish(false);

        when(mockSaleComplaintFollowMapper.selectListByVcdc(any(SaleComplaintFollowPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<SaleComplaintFollowDTO> result = saleComplaintFollowServiceImplUnderTest.selectListBySql("flag",
                saleComplaintFollowDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure SaleComplaintFollowMapper.selectById(...).
        final SaleComplaintFollowPO saleComplaintFollowPO = new SaleComplaintFollowPO();
        saleComplaintFollowPO.setAppId("appId");
        saleComplaintFollowPO.setOwnerCode("ownerCode");
        saleComplaintFollowPO.setOwnerParCode("ownerParCode");
        saleComplaintFollowPO.setOrgId(0);
        saleComplaintFollowPO.setId(0L);
        when(mockSaleComplaintFollowMapper.selectById(0L)).thenReturn(saleComplaintFollowPO);

        // Run the test
        final SaleComplaintFollowDTO result = saleComplaintFollowServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_SaleComplaintFollowMapperReturnsNull() {
        // Setup
        when(mockSaleComplaintFollowMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> saleComplaintFollowServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final SaleComplaintFollowDTO saleComplaintFollowDTO = new SaleComplaintFollowDTO();
        saleComplaintFollowDTO.setComplaintInfoId(0L);
        saleComplaintFollowDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintFollowDTO.setFollower("follower");
        saleComplaintFollowDTO.setFollowContent("followContent");
        saleComplaintFollowDTO.setPlanFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintFollowDTO.setActuallFollowTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintFollowDTO.setFollowerName("followerName");
        saleComplaintFollowDTO.setCcmNotPublish(false);
        saleComplaintFollowDTO.setDealerNotPublish(false);

        when(mockSaleComplaintFollowMapper.insert(any(SaleComplaintFollowPO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintFollowServiceImplUnderTest.insert(saleComplaintFollowDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final SaleComplaintFollowDTO saleComplaintFollowDTO = new SaleComplaintFollowDTO();
        saleComplaintFollowDTO.setComplaintInfoId(0L);
        saleComplaintFollowDTO.setFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintFollowDTO.setFollower("follower");
        saleComplaintFollowDTO.setFollowContent("followContent");
        saleComplaintFollowDTO.setPlanFollowTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintFollowDTO.setActuallFollowTime2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        saleComplaintFollowDTO.setFollowerName("followerName");
        saleComplaintFollowDTO.setCcmNotPublish(false);
        saleComplaintFollowDTO.setDealerNotPublish(false);

        // Configure SaleComplaintFollowMapper.selectById(...).
        final SaleComplaintFollowPO saleComplaintFollowPO = new SaleComplaintFollowPO();
        saleComplaintFollowPO.setAppId("appId");
        saleComplaintFollowPO.setOwnerCode("ownerCode");
        saleComplaintFollowPO.setOwnerParCode("ownerParCode");
        saleComplaintFollowPO.setOrgId(0);
        saleComplaintFollowPO.setId(0L);
        when(mockSaleComplaintFollowMapper.selectById(0L)).thenReturn(saleComplaintFollowPO);

        when(mockSaleComplaintFollowMapper.updateById(any(SaleComplaintFollowPO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintFollowServiceImplUnderTest.update(0L, saleComplaintFollowDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testQueryNextFollowing() {
        // Setup
        // Configure SaleComplaintFollowMapper.queryNextFollowing(...).
        final ComplaintInfMoreDTO complaintInfMoreDTO = new ComplaintInfMoreDTO();
        complaintInfMoreDTO.setRiskType("riskType");
        complaintInfMoreDTO.setCaseCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        complaintInfMoreDTO.setInvalidCaseHidden(0);
        complaintInfMoreDTO.setWorkOrderStatusData("workOrderStatusData");
        complaintInfMoreDTO.setImportanceLevelData("importanceLevelData");
        final List<ComplaintInfMoreDTO> complaintInfMoreDTOS = Arrays.asList(complaintInfMoreDTO);
        when(mockSaleComplaintFollowMapper.queryNextFollowing()).thenReturn(complaintInfMoreDTOS);

        // Run the test
        final List<ComplaintInfMoreDTO> result = saleComplaintFollowServiceImplUnderTest.queryNextFollowing();

        // Verify the results
    }

    @Test
    void testQueryNextFollowing_SaleComplaintFollowMapperReturnsNoItems() {
        // Setup
        when(mockSaleComplaintFollowMapper.queryNextFollowing()).thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintInfMoreDTO> result = saleComplaintFollowServiceImplUnderTest.queryNextFollowing();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
