package com.yonyou.dmscus.customer.service.accidentClues;

import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.clue.AccidentClueAppPushEnum;
import com.yonyou.dmscus.customer.dto.SmsPushDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.common.QueryUserPositionDTO;
import com.yonyou.dmscus.customer.entity.dto.common.UserPositionOutDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.AppPushDTO;
import com.yonyou.dmscus.customer.entity.po.accidentClues.AccidentCluesPO;
import com.yonyou.dmscus.customer.feign.MidEndAuthCenterClient;
import com.yonyou.dmscus.customer.middleInterface.UserInfoOutDTO;
import com.yonyou.dmscus.customer.service.CommonService;
import com.yonyou.dmscus.customer.service.common.message.MessageSendService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AppPushServiceImplTest {

    @Mock
    private MessageSendService mockMessageSendService;
    @Mock
    private CommonService mockCommonService;
    @Mock
    private MidEndAuthCenterClient mockMidEndAuthCenterClient;
    @Mock
    private WhitelistCheckService mockWhitelistCheckService;

    @InjectMocks
    private AppPushServiceImpl appPushServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(appPushServiceImplUnderTest, "limitDate", "limitDate");
        appPushServiceImplUnderTest.templateCode = "templateCode";
        appPushServiceImplUnderTest.title = "title";
        appPushServiceImplUnderTest.detailPath = "detailPath";
        appPushServiceImplUnderTest.allotTemplateCode = "allotTemplateCode";
        appPushServiceImplUnderTest.followTemplateCode = "allotTemplateCode";
        appPushServiceImplUnderTest.timeOutTemplateCode = "allotTemplateCode";
        appPushServiceImplUnderTest.notComTemplateCode = "allotTemplateCode";
        appPushServiceImplUnderTest.whitelistCheckService = mockWhitelistCheckService;
    }

    @Test
    void testClueAllotAppPush() {
        // Setup
        final Map<Long, List<AccidentCluesPO>> appPushMap = new HashMap<>();

        // Configure MessageSendService.getUserPositionInfoList(...).
        final UserPositionOutDTO userPositionOutDTO = new UserPositionOutDTO();
        userPositionOutDTO.setUserId(0L);
        userPositionOutDTO.setUserOrgId(0L);
        userPositionOutDTO.setEmpId(0L);
        userPositionOutDTO.setOrgId(0L);
        userPositionOutDTO.setOrgName("orgName");
        final List<UserPositionOutDTO> userPositionOutDTOS = Arrays.asList(userPositionOutDTO);
        when(mockMessageSendService.getUserPositionInfoList(any(QueryUserPositionDTO.class)))
                .thenReturn(userPositionOutDTOS);

        // Configure MidEndAuthCenterClient.queryUserInfoList(...).
        final ResponseDTO<List<UserInfoOutDTO>> listResponseDTO = new ResponseDTO<>();
        listResponseDTO.setReturnCode("returnCode");
        listResponseDTO.setReturnMessage("returnMessage");
        final UserInfoOutDTO userInfoOutDTO = new UserInfoOutDTO();
        userInfoOutDTO.setUserId(0L);
        userInfoOutDTO.setPhone("phone");
        listResponseDTO.setData(Arrays.asList(userInfoOutDTO));
        when(mockMidEndAuthCenterClient.queryUserInfoList(Arrays.asList("value"))).thenReturn(listResponseDTO);

        // Run the test
        appPushServiceImplUnderTest.clueAllotAppPush(appPushMap, 0L, AccidentClueAppPushEnum.PUSH_TYPE_ALLOT);

        // Verify the results
        verify(mockMessageSendService).messageSendApp(any(AppPushDTO.class));

        // Confirm CommonService.sendMessage(...).
        final SmsPushDTO smsPushDTO = new SmsPushDTO();
        smsPushDTO.setOneIds("oneIds");
        smsPushDTO.setEmployeeIds("employeeIds");
        smsPushDTO.setMobiles("phone");
        smsPushDTO.setTemplateId("allotTemplateCode");
        smsPushDTO.setParamMap("paramMap");
        verify(mockCommonService).sendMessage(smsPushDTO);
    }

    @Test
    void testClueAllotAppPush_MessageSendServiceGetUserPositionInfoListReturnsNoItems() {
        // Setup
        final Map<Long, List<AccidentCluesPO>> appPushMap = new HashMap<>();
        when(mockMessageSendService.getUserPositionInfoList(any(QueryUserPositionDTO.class)))
                .thenReturn(Collections.emptyList());

        // Configure MidEndAuthCenterClient.queryUserInfoList(...).
        final ResponseDTO<List<UserInfoOutDTO>> listResponseDTO = new ResponseDTO<>();
        listResponseDTO.setReturnCode("returnCode");
        listResponseDTO.setReturnMessage("returnMessage");
        final UserInfoOutDTO userInfoOutDTO = new UserInfoOutDTO();
        userInfoOutDTO.setUserId(0L);
        userInfoOutDTO.setPhone("phone");
        listResponseDTO.setData(Arrays.asList(userInfoOutDTO));
        when(mockMidEndAuthCenterClient.queryUserInfoList(Arrays.asList("value"))).thenReturn(listResponseDTO);

        // Run the test
        appPushServiceImplUnderTest.clueAllotAppPush(appPushMap, 0L, AccidentClueAppPushEnum.PUSH_TYPE_ALLOT);

        // Verify the results
        verify(mockMessageSendService).messageSendApp(any(AppPushDTO.class));

        // Confirm CommonService.sendMessage(...).
        final SmsPushDTO smsPushDTO = new SmsPushDTO();
        smsPushDTO.setOneIds("oneIds");
        smsPushDTO.setEmployeeIds("employeeIds");
        smsPushDTO.setMobiles("phone");
        smsPushDTO.setTemplateId("allotTemplateCode");
        smsPushDTO.setParamMap("paramMap");
        verify(mockCommonService).sendMessage(smsPushDTO);
    }
}
