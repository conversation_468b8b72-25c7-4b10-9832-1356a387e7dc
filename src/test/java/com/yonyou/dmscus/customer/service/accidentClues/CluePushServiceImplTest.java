package com.yonyou.dmscus.customer.service.accidentClues;

import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.constants.RemotePushSinceTypeEnum;
import com.yonyou.dmscus.customer.constants.RemotePushStatusEnum;
import com.yonyou.dmscus.customer.dao.pushRecord.RemotePushRecordMapper;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.AccidentCluesDTO;
import com.yonyou.dmscus.customer.entity.dto.accidentClues.StatusChangePushDTO;
import com.yonyou.dmscus.customer.entity.po.pushRecord.RemotePushRecordPO;
import com.yonyou.dmscus.customer.feign.WhitelistQueryService;
import com.yonyou.dmscus.customer.rocketmq.AccidentCluePushLiteCrmMQTemplate;
import com.yonyou.dmscus.customer.service.pushRecord.RemotePushRecordService;
import org.apache.rocketmq.client.producer.SendResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.Message;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CluePushServiceImplTest {

    @Mock
    private AccidentCluePushLiteCrmMQTemplate mockAccidentCluePushLiteCrmMQTemplate;
    @Mock
    private RemotePushRecordService mockRemotePushRecordService;
    @Mock
    private WhitelistCheckService mockWhitelistCheckService;
    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private RemotePushRecordMapper mockRemotePushRecordMapper;
    @Mock
    private WhitelistQueryService mockWhitelistQueryService;
    @Mock
    private LiteCrmService mockLiteCrmService;

    @InjectMocks
    private CluePushServiceImpl cluePushServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(cluePushServiceImplUnderTest, "userName", "userName");
        ReflectionTestUtils.setField(cluePushServiceImplUnderTest, "password", "password");
        ReflectionTestUtils.setField(cluePushServiceImplUnderTest, "prefix", "prefix");
        ReflectionTestUtils.setField(cluePushServiceImplUnderTest, "login", "login");
        ReflectionTestUtils.setField(cluePushServiceImplUnderTest, "cluePush", "cluePush");
        ReflectionTestUtils.setField(cluePushServiceImplUnderTest, "liteCrmPushTopic", "liteCrmPushTopic");
    }

    @Test
    void testPushLiteCrmClueInfo_LiteCrmServiceThrowsServiceBizException() {
        // Setup
        final AccidentCluesDTO clueInfo = new AccidentCluesDTO();
        clueInfo.setAppId("appId");
        clueInfo.setOwnerCode("ownerCode");
        clueInfo.setOwnerParCode("ownerParCode");
        clueInfo.setOrgId(0);
        clueInfo.setAcId(0);

        when(mockLiteCrmService.getCrmToken()).thenThrow(ServiceBizException.class);

        // Run the test
        assertThatThrownBy(() -> cluePushServiceImplUnderTest.pushLiteCrmClueInfo(clueInfo))
                .isInstanceOf(AccidentCluePushFailException.class);
        verify(mockRemotePushRecordService).accidentClueLiteCrmPushRecord("reqParams", "respParams", 0,
                RemotePushSinceTypeEnum.CLUE_INFO, RemotePushStatusEnum.STATUS_PENDING);
    }

    @Test
    void testPushLiteCrmClueStatus_RemotePushRecordServiceThrowsServiceBizException() {
        // Setup
        final StatusChangePushDTO pushInfo = new StatusChangePushDTO();
        pushInfo.setId("id");
        pushInfo.setSourceClueId("sourceClueId");
        pushInfo.setBizStatus("bizStatus");
        pushInfo.setFollowUpStatus("followUpStatus");
        pushInfo.setLeadsType("leadsType");

        doThrow(ServiceBizException.class).when(mockRemotePushRecordService).accidentClueLiteCrmPushRecord("reqParams",
                "respParams", 0, RemotePushSinceTypeEnum.CLUE_INFO, RemotePushStatusEnum.STATUS_PENDING);

        // Run the test
        assertThatThrownBy(() -> cluePushServiceImplUnderTest.pushLiteCrmClueStatus(pushInfo))
                .isInstanceOf(AccidentCluePushFailException.class);
    }

    @Test
    void testCompensatePush() {
        // Setup
        // Configure RemotePushRecordMapper.queryAcRemotePushCompensateList(...).
        final RemotePushRecordPO remotePushRecordPO = new RemotePushRecordPO();
        remotePushRecordPO.setId(0L);
        remotePushRecordPO.setSinceType(0);
        remotePushRecordPO.setSubSinceType(0);
        remotePushRecordPO.setBizNo("bizNo");
        remotePushRecordPO.setReqParams("reqParams");
        final List<RemotePushRecordPO> remotePushRecordPOS = Arrays.asList(remotePushRecordPO);
        when(mockRemotePushRecordMapper.queryAcRemotePushCompensateList(0, 0, 0)).thenReturn(remotePushRecordPOS);

        when(mockLiteCrmService.getCrmToken()).thenReturn("result");
        when(mockAccidentCluePushLiteCrmMQTemplate.syncSend(eq("liteCrmPushTopic"), any(Message.class)))
                .thenReturn(new SendResult());

        // Run the test
        cluePushServiceImplUnderTest.compensatePush(0, 0, 0, 0);

        // Verify the results
        // Confirm RemotePushRecordService.updateAcCompensateRecord(...).
        final RemotePushRecordPO record = new RemotePushRecordPO();
        record.setId(0L);
        record.setSinceType(0);
        record.setSubSinceType(0);
        record.setBizNo("bizNo");
        record.setReqParams("reqParams");
        verify(mockRemotePushRecordService).updateAcCompensateRecord(record, "resp",
                RemotePushStatusEnum.STATUS_SUCCESS);
    }

    @Test
    void testCompensatePush_RemotePushRecordMapperReturnsNoItems() {
        // Setup
        when(mockRemotePushRecordMapper.queryAcRemotePushCompensateList(0, 0, 0)).thenReturn(Collections.emptyList());
        when(mockLiteCrmService.getCrmToken()).thenReturn("result");
        when(mockAccidentCluePushLiteCrmMQTemplate.syncSend(eq("liteCrmPushTopic"), any(Message.class)))
                .thenReturn(new SendResult());

        // Run the test
        cluePushServiceImplUnderTest.compensatePush(0, 0, 0, 0);

        // Verify the results
        // Confirm RemotePushRecordService.updateAcCompensateRecord(...).
        final RemotePushRecordPO record = new RemotePushRecordPO();
        record.setId(0L);
        record.setSinceType(0);
        record.setSubSinceType(0);
        record.setBizNo("bizNo");
        record.setReqParams("reqParams");
        verify(mockRemotePushRecordService).updateAcCompensateRecord(record, "resp",
                RemotePushStatusEnum.STATUS_SUCCESS);
    }

    @Test
    void testCompensatePush_LiteCrmServiceThrowsServiceBizException() {
        // Setup
        // Configure RemotePushRecordMapper.queryAcRemotePushCompensateList(...).
        final RemotePushRecordPO remotePushRecordPO = new RemotePushRecordPO();
        remotePushRecordPO.setId(0L);
        remotePushRecordPO.setSinceType(0);
        remotePushRecordPO.setSubSinceType(0);
        remotePushRecordPO.setBizNo("bizNo");
        remotePushRecordPO.setReqParams("reqParams");
        final List<RemotePushRecordPO> remotePushRecordPOS = Arrays.asList(remotePushRecordPO);
        when(mockRemotePushRecordMapper.queryAcRemotePushCompensateList(0, 0, 0)).thenReturn(remotePushRecordPOS);

        when(mockLiteCrmService.getCrmToken()).thenThrow(ServiceBizException.class);

        // Run the test
        assertThatThrownBy(() -> cluePushServiceImplUnderTest.compensatePush(0, 0, 0, 0))
                .isInstanceOf(ServiceBizException.class);
    }

    @Test
    void testCompensatePush_RemotePushRecordServiceThrowsServiceBizException() {
        // Setup
        // Configure RemotePushRecordMapper.queryAcRemotePushCompensateList(...).
        final RemotePushRecordPO remotePushRecordPO = new RemotePushRecordPO();
        remotePushRecordPO.setId(0L);
        remotePushRecordPO.setSinceType(0);
        remotePushRecordPO.setSubSinceType(0);
        remotePushRecordPO.setBizNo("bizNo");
        remotePushRecordPO.setReqParams("reqParams");
        final List<RemotePushRecordPO> remotePushRecordPOS = Arrays.asList(remotePushRecordPO);
        when(mockRemotePushRecordMapper.queryAcRemotePushCompensateList(0, 0, 0)).thenReturn(remotePushRecordPOS);

        when(mockLiteCrmService.getCrmToken()).thenReturn("result");

        // Configure RemotePushRecordService.updateAcCompensateRecord(...).
        final RemotePushRecordPO record = new RemotePushRecordPO();
        record.setId(0L);
        record.setSinceType(0);
        record.setSubSinceType(0);
        record.setBizNo("bizNo");
        record.setReqParams("reqParams");
        doThrow(ServiceBizException.class).when(mockRemotePushRecordService).updateAcCompensateRecord(record, "resp",
                RemotePushStatusEnum.STATUS_SUCCESS);

        // Run the test
        assertThatThrownBy(() -> cluePushServiceImplUnderTest.compensatePush(0, 0, 0, 0))
                .isInstanceOf(AccidentCluePushFailException.class);
    }
}
