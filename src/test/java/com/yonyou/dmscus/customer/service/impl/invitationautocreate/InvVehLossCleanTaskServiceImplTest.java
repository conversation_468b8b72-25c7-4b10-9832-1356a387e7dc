package com.yonyou.dmscus.customer.service.impl.invitationautocreate;

import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dao.invitationautocreate.InviteVehicleTaskMapper;
import com.yonyou.dmscus.customer.dao.inviteRule.InviteRuleMapper;
import com.yonyou.dmscus.customer.dao.voc.VocInviteVehicleTaskRecordMapper;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.InvVehLossCleanTaskPO;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InviteRulePO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocInviteVehicleTaskRecordPo;
import com.yonyou.dmscus.customer.service.invitationautocreate.InviteVehicleTaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InvVehLossCleanTaskServiceImplTest {

    @Mock
    private InviteVehicleRecordMapper mockInviteVehicleRecordMapper;
    @Mock
    private InviteVehicleTaskMapper mockInviteVehicleTaskMapper;
    @Mock
    private InviteRuleMapper mockInviteRuleMapper;
    @Mock
    private VocInviteVehicleTaskRecordMapper mockVocInviteVehicleTaskRecordMapper;
    @Mock
    private InviteVehicleTaskService mockInviteVehicleTaskService;

    @InjectMocks
    private InvVehLossCleanTaskServiceImpl invVehLossCleanTaskServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        invVehLossCleanTaskServiceImplUnderTest.inviteVehicleRecordMapper = mockInviteVehicleRecordMapper;
    }

    @Test
    void testDoCleanLossTaskByUnf_InviteVehicleRecordMapperReturnsNoItems() {
        // Setup
        when(mockInviteVehicleRecordMapper.selectDealerCodeGroupBy()).thenReturn(Collections.emptyList());

        // Run the test
        invVehLossCleanTaskServiceImplUnderTest.doCleanLossTaskByUnf();

        // Verify the results
    }

    @Test
    void testDoCleanLossTaskBySli_InviteVehicleRecordMapperReturnsNoItems() {
        // Setup
        when(mockInviteVehicleRecordMapper.selectDealerCodeGroupBy()).thenReturn(Collections.emptyList());

        // Run the test
        invVehLossCleanTaskServiceImplUnderTest.doCleanLossTaskBySli();

        // Verify the results
    }

    @Test
    void testAddInviteVehicleTaskRecord_InviteVehicleTaskMapperReturnsNoItems() {
        // Setup
        when(mockInviteVehicleTaskMapper.selectDeletionLossTask("startDate", "endDate"))
                .thenReturn(Collections.emptyList());

        // Run the test
        invVehLossCleanTaskServiceImplUnderTest.addInviteVehicleTaskRecord("startDate", "endDate");

        // Verify the results
    }
}
