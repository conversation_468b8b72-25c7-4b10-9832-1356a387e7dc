package com.yonyou.dmscus.customer.service.impl.inviteRule;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.inviteRule.InvitePartItemRuleChangedRecordMapper;
import com.yonyou.dmscus.customer.dao.inviteRule.InvitePartItemRuleMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteRule.InvitePartItemRuleDTO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InvitePartItemRuleChangedRecordPO;
import com.yonyou.dmscus.customer.entity.po.inviteRule.InvitePartItemRulePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InvitePartItemRuleServiceImplTest {

    @Mock
    private InvitePartItemRuleMapper mockInvitePartItemRuleMapper;
    @Mock
    private InvitePartItemRuleChangedRecordMapper mockInvitePartItemRuleChangedRecordMapper;

    private InvitePartItemRuleServiceImpl invitePartItemRuleServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        invitePartItemRuleServiceImplUnderTest = new InvitePartItemRuleServiceImpl();
        invitePartItemRuleServiceImplUnderTest.invitePartItemRuleMapper = mockInvitePartItemRuleMapper;
        invitePartItemRuleServiceImplUnderTest.invitePartItemRuleChangedRecordMapper = mockInvitePartItemRuleChangedRecordMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final InvitePartItemRuleDTO invitePartItemRuleDTO = new InvitePartItemRuleDTO();
        invitePartItemRuleDTO.setId(0L);
        invitePartItemRuleDTO.setRemindInterval(0);
        invitePartItemRuleDTO.setMileageInterval(0);
        invitePartItemRuleDTO.setDateInterval(0);
        invitePartItemRuleDTO.setVin("vin");
        invitePartItemRuleDTO.setModelCode("modelCode");
        invitePartItemRuleDTO.setModelYear("modelYear");
        invitePartItemRuleDTO.setEngineCode("engineCode");
        invitePartItemRuleDTO.setGearboxCode("gearboxCode");
        invitePartItemRuleDTO.setRuleRelationship(0);
        invitePartItemRuleDTO.setIsUse(0);

        // Configure InvitePartItemRuleMapper.selectPageBySql(...).
        final InvitePartItemRulePO invitePartItemRulePO = new InvitePartItemRulePO();
        invitePartItemRulePO.setId(0L);
        invitePartItemRulePO.setDealerCode("dealerCode");
        invitePartItemRulePO.setType(0);
        invitePartItemRulePO.setCode("code");
        invitePartItemRulePO.setName("name");
        invitePartItemRulePO.setRemindInterval(0);
        invitePartItemRulePO.setMileageInterval(0);
        invitePartItemRulePO.setDateInterval(0);
        invitePartItemRulePO.setVin("vin");
        invitePartItemRulePO.setModelCode("modelCode");
        invitePartItemRulePO.setModelYear("modelYear");
        invitePartItemRulePO.setEngineCode("engineCode");
        invitePartItemRulePO.setGearboxCode("gearboxCode");
        invitePartItemRulePO.setRuleRelationship(0);
        invitePartItemRulePO.setIsUse(0);
        final List<InvitePartItemRulePO> invitePartItemRulePOS = Arrays.asList(invitePartItemRulePO);
        when(mockInvitePartItemRuleMapper.selectPageBySql(any(Page.class), any(InvitePartItemRulePO.class)))
                .thenReturn(invitePartItemRulePOS);

        // Run the test
        final IPage<InvitePartItemRuleDTO> result = invitePartItemRuleServiceImplUnderTest.selectPageBysql(page,
                invitePartItemRuleDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_InvitePartItemRuleMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final InvitePartItemRuleDTO invitePartItemRuleDTO = new InvitePartItemRuleDTO();
        invitePartItemRuleDTO.setId(0L);
        invitePartItemRuleDTO.setRemindInterval(0);
        invitePartItemRuleDTO.setMileageInterval(0);
        invitePartItemRuleDTO.setDateInterval(0);
        invitePartItemRuleDTO.setVin("vin");
        invitePartItemRuleDTO.setModelCode("modelCode");
        invitePartItemRuleDTO.setModelYear("modelYear");
        invitePartItemRuleDTO.setEngineCode("engineCode");
        invitePartItemRuleDTO.setGearboxCode("gearboxCode");
        invitePartItemRuleDTO.setRuleRelationship(0);
        invitePartItemRuleDTO.setIsUse(0);

        when(mockInvitePartItemRuleMapper.selectPageBySql(any(Page.class), any(InvitePartItemRulePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final IPage<InvitePartItemRuleDTO> result = invitePartItemRuleServiceImplUnderTest.selectPageBysql(page,
                invitePartItemRuleDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final InvitePartItemRuleDTO invitePartItemRuleDTO = new InvitePartItemRuleDTO();
        invitePartItemRuleDTO.setId(0L);
        invitePartItemRuleDTO.setRemindInterval(0);
        invitePartItemRuleDTO.setMileageInterval(0);
        invitePartItemRuleDTO.setDateInterval(0);
        invitePartItemRuleDTO.setVin("vin");
        invitePartItemRuleDTO.setModelCode("modelCode");
        invitePartItemRuleDTO.setModelYear("modelYear");
        invitePartItemRuleDTO.setEngineCode("engineCode");
        invitePartItemRuleDTO.setGearboxCode("gearboxCode");
        invitePartItemRuleDTO.setRuleRelationship(0);
        invitePartItemRuleDTO.setIsUse(0);

        // Configure InvitePartItemRuleMapper.selectListBySql(...).
        final InvitePartItemRulePO invitePartItemRulePO = new InvitePartItemRulePO();
        invitePartItemRulePO.setId(0L);
        invitePartItemRulePO.setDealerCode("dealerCode");
        invitePartItemRulePO.setType(0);
        invitePartItemRulePO.setCode("code");
        invitePartItemRulePO.setName("name");
        invitePartItemRulePO.setRemindInterval(0);
        invitePartItemRulePO.setMileageInterval(0);
        invitePartItemRulePO.setDateInterval(0);
        invitePartItemRulePO.setVin("vin");
        invitePartItemRulePO.setModelCode("modelCode");
        invitePartItemRulePO.setModelYear("modelYear");
        invitePartItemRulePO.setEngineCode("engineCode");
        invitePartItemRulePO.setGearboxCode("gearboxCode");
        invitePartItemRulePO.setRuleRelationship(0);
        invitePartItemRulePO.setIsUse(0);
        final List<InvitePartItemRulePO> invitePartItemRulePOS = Arrays.asList(invitePartItemRulePO);
        when(mockInvitePartItemRuleMapper.selectListBySql(any(InvitePartItemRulePO.class)))
                .thenReturn(invitePartItemRulePOS);

        // Run the test
        final List<InvitePartItemRuleDTO> result = invitePartItemRuleServiceImplUnderTest.selectListBySql(
                invitePartItemRuleDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_InvitePartItemRuleMapperReturnsNoItems() {
        // Setup
        final InvitePartItemRuleDTO invitePartItemRuleDTO = new InvitePartItemRuleDTO();
        invitePartItemRuleDTO.setId(0L);
        invitePartItemRuleDTO.setRemindInterval(0);
        invitePartItemRuleDTO.setMileageInterval(0);
        invitePartItemRuleDTO.setDateInterval(0);
        invitePartItemRuleDTO.setVin("vin");
        invitePartItemRuleDTO.setModelCode("modelCode");
        invitePartItemRuleDTO.setModelYear("modelYear");
        invitePartItemRuleDTO.setEngineCode("engineCode");
        invitePartItemRuleDTO.setGearboxCode("gearboxCode");
        invitePartItemRuleDTO.setRuleRelationship(0);
        invitePartItemRuleDTO.setIsUse(0);

        when(mockInvitePartItemRuleMapper.selectListBySql(any(InvitePartItemRulePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InvitePartItemRuleDTO> result = invitePartItemRuleServiceImplUnderTest.selectListBySql(
                invitePartItemRuleDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure InvitePartItemRuleMapper.selectById(...).
        final InvitePartItemRulePO invitePartItemRulePO = new InvitePartItemRulePO();
        invitePartItemRulePO.setId(0L);
        invitePartItemRulePO.setDealerCode("dealerCode");
        invitePartItemRulePO.setType(0);
        invitePartItemRulePO.setCode("code");
        invitePartItemRulePO.setName("name");
        invitePartItemRulePO.setRemindInterval(0);
        invitePartItemRulePO.setMileageInterval(0);
        invitePartItemRulePO.setDateInterval(0);
        invitePartItemRulePO.setVin("vin");
        invitePartItemRulePO.setModelCode("modelCode");
        invitePartItemRulePO.setModelYear("modelYear");
        invitePartItemRulePO.setEngineCode("engineCode");
        invitePartItemRulePO.setGearboxCode("gearboxCode");
        invitePartItemRulePO.setRuleRelationship(0);
        invitePartItemRulePO.setIsUse(0);
        when(mockInvitePartItemRuleMapper.selectById(0L)).thenReturn(invitePartItemRulePO);

        // Run the test
        final InvitePartItemRuleDTO result = invitePartItemRuleServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_InvitePartItemRuleMapperReturnsNull() {
        // Setup
        when(mockInvitePartItemRuleMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> invitePartItemRuleServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final InvitePartItemRuleDTO invitePartItemRuleDTO = new InvitePartItemRuleDTO();
        invitePartItemRuleDTO.setId(0L);
        invitePartItemRuleDTO.setRemindInterval(0);
        invitePartItemRuleDTO.setMileageInterval(0);
        invitePartItemRuleDTO.setDateInterval(0);
        invitePartItemRuleDTO.setVin("vin");
        invitePartItemRuleDTO.setModelCode("modelCode");
        invitePartItemRuleDTO.setModelYear("modelYear");
        invitePartItemRuleDTO.setEngineCode("engineCode");
        invitePartItemRuleDTO.setGearboxCode("gearboxCode");
        invitePartItemRuleDTO.setRuleRelationship(0);
        invitePartItemRuleDTO.setIsUse(0);

        when(mockInvitePartItemRuleMapper.insert(any(InvitePartItemRulePO.class))).thenReturn(0);

        // Run the test
        final int result = invitePartItemRuleServiceImplUnderTest.insert(invitePartItemRuleDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final InvitePartItemRuleDTO invitePartItemRuleDTO = new InvitePartItemRuleDTO();
        invitePartItemRuleDTO.setId(0L);
        invitePartItemRuleDTO.setRemindInterval(0);
        invitePartItemRuleDTO.setMileageInterval(0);
        invitePartItemRuleDTO.setDateInterval(0);
        invitePartItemRuleDTO.setVin("vin");
        invitePartItemRuleDTO.setModelCode("modelCode");
        invitePartItemRuleDTO.setModelYear("modelYear");
        invitePartItemRuleDTO.setEngineCode("engineCode");
        invitePartItemRuleDTO.setGearboxCode("gearboxCode");
        invitePartItemRuleDTO.setRuleRelationship(0);
        invitePartItemRuleDTO.setIsUse(0);

        // Configure InvitePartItemRuleMapper.selectById(...).
        final InvitePartItemRulePO invitePartItemRulePO = new InvitePartItemRulePO();
        invitePartItemRulePO.setId(0L);
        invitePartItemRulePO.setDealerCode("dealerCode");
        invitePartItemRulePO.setType(0);
        invitePartItemRulePO.setCode("code");
        invitePartItemRulePO.setName("name");
        invitePartItemRulePO.setRemindInterval(0);
        invitePartItemRulePO.setMileageInterval(0);
        invitePartItemRulePO.setDateInterval(0);
        invitePartItemRulePO.setVin("vin");
        invitePartItemRulePO.setModelCode("modelCode");
        invitePartItemRulePO.setModelYear("modelYear");
        invitePartItemRulePO.setEngineCode("engineCode");
        invitePartItemRulePO.setGearboxCode("gearboxCode");
        invitePartItemRulePO.setRuleRelationship(0);
        invitePartItemRulePO.setIsUse(0);
        when(mockInvitePartItemRuleMapper.selectById(0L)).thenReturn(invitePartItemRulePO);

        when(mockInvitePartItemRuleMapper.updateById(any(InvitePartItemRulePO.class))).thenReturn(0);

        // Run the test
        final int result = invitePartItemRuleServiceImplUnderTest.update(0L, invitePartItemRuleDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }


}
