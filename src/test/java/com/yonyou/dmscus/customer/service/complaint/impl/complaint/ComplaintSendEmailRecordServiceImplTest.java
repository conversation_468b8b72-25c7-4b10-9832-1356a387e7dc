package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintSendEmailRecordMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintSendEmailRecordDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintSendEmailRecordPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintSendEmailRecordServiceImplTest {

    @Mock
    private ComplaintSendEmailRecordMapper mockComplaintSendEmailRecordMapper;

    private ComplaintSendEmailRecordServiceImpl complaintSendEmailRecordServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        complaintSendEmailRecordServiceImplUnderTest = new ComplaintSendEmailRecordServiceImpl();
        complaintSendEmailRecordServiceImplUnderTest.complaintSendEmailRecordMapper = mockComplaintSendEmailRecordMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO = new ComplaintSendEmailRecordDTO();
        complaintSendEmailRecordDTO.setReceipter("receipter");
        complaintSendEmailRecordDTO.setAppId("appId");
        complaintSendEmailRecordDTO.setOwnerCode("ownerCode");
        complaintSendEmailRecordDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailRecordDTO.setOrgId(0);

        // Configure ComplaintSendEmailRecordMapper.selectPageBySql(...).
        final ComplaintSendEmailRecordPO complaintSendEmailRecordPO = new ComplaintSendEmailRecordPO();
        complaintSendEmailRecordPO.setAppId("appId");
        complaintSendEmailRecordPO.setOwnerCode("ownerCode");
        complaintSendEmailRecordPO.setOwnerParCode("ownerParCode");
        complaintSendEmailRecordPO.setOrgId(0);
        complaintSendEmailRecordPO.setId(0L);
        final List<ComplaintSendEmailRecordPO> complaintSendEmailRecordPOS = Arrays.asList(complaintSendEmailRecordPO);
        when(mockComplaintSendEmailRecordMapper.selectPageBySql(any(Page.class),
                any(ComplaintSendEmailRecordPO.class))).thenReturn(complaintSendEmailRecordPOS);

        // Run the test
        final IPage<ComplaintSendEmailRecordDTO> result = complaintSendEmailRecordServiceImplUnderTest.selectPageBysql(
                page, complaintSendEmailRecordDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintSendEmailRecordMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO = new ComplaintSendEmailRecordDTO();
        complaintSendEmailRecordDTO.setReceipter("receipter");
        complaintSendEmailRecordDTO.setAppId("appId");
        complaintSendEmailRecordDTO.setOwnerCode("ownerCode");
        complaintSendEmailRecordDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailRecordDTO.setOrgId(0);

        when(mockComplaintSendEmailRecordMapper.selectPageBySql(any(Page.class),
                any(ComplaintSendEmailRecordPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintSendEmailRecordDTO> result = complaintSendEmailRecordServiceImplUnderTest.selectPageBysql(
                page, complaintSendEmailRecordDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() throws Exception {
        // Setup
        final ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO = new ComplaintSendEmailRecordDTO();
        complaintSendEmailRecordDTO.setReceipter("receipter");
        complaintSendEmailRecordDTO.setAppId("appId");
        complaintSendEmailRecordDTO.setOwnerCode("ownerCode");
        complaintSendEmailRecordDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailRecordDTO.setOrgId(0);

        // Configure ComplaintSendEmailRecordMapper.selectListBySql(...).
        final ComplaintSendEmailRecordPO complaintSendEmailRecordPO = new ComplaintSendEmailRecordPO();
        complaintSendEmailRecordPO.setAppId("appId");
        complaintSendEmailRecordPO.setOwnerCode("ownerCode");
        complaintSendEmailRecordPO.setOwnerParCode("ownerParCode");
        complaintSendEmailRecordPO.setOrgId(0);
        complaintSendEmailRecordPO.setId(0L);
        final List<ComplaintSendEmailRecordPO> complaintSendEmailRecordPOS = Arrays.asList(complaintSendEmailRecordPO);
        when(mockComplaintSendEmailRecordMapper.selectListBySql(any(ComplaintSendEmailRecordPO.class)))
                .thenReturn(complaintSendEmailRecordPOS);

        // Run the test
        final List<ComplaintSendEmailRecordDTO> result = complaintSendEmailRecordServiceImplUnderTest.selectListBySql(
                complaintSendEmailRecordDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintSendEmailRecordMapperReturnsNoItems() {
        // Setup
        final ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO = new ComplaintSendEmailRecordDTO();
        complaintSendEmailRecordDTO.setReceipter("receipter");
        complaintSendEmailRecordDTO.setAppId("appId");
        complaintSendEmailRecordDTO.setOwnerCode("ownerCode");
        complaintSendEmailRecordDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailRecordDTO.setOrgId(0);

        when(mockComplaintSendEmailRecordMapper.selectListBySql(any(ComplaintSendEmailRecordPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintSendEmailRecordDTO> result = complaintSendEmailRecordServiceImplUnderTest.selectListBySql(
                complaintSendEmailRecordDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure ComplaintSendEmailRecordMapper.selectById(...).
        final ComplaintSendEmailRecordPO complaintSendEmailRecordPO = new ComplaintSendEmailRecordPO();
        complaintSendEmailRecordPO.setAppId("appId");
        complaintSendEmailRecordPO.setOwnerCode("ownerCode");
        complaintSendEmailRecordPO.setOwnerParCode("ownerParCode");
        complaintSendEmailRecordPO.setOrgId(0);
        complaintSendEmailRecordPO.setId(0L);
        when(mockComplaintSendEmailRecordMapper.selectById(0L)).thenReturn(complaintSendEmailRecordPO);

        // Run the test
        final ComplaintSendEmailRecordDTO result = complaintSendEmailRecordServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintSendEmailRecordMapperReturnsNull() {
        // Setup
        when(mockComplaintSendEmailRecordMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintSendEmailRecordServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO = new ComplaintSendEmailRecordDTO();
        complaintSendEmailRecordDTO.setReceipter("receipter");
        complaintSendEmailRecordDTO.setAppId("appId");
        complaintSendEmailRecordDTO.setOwnerCode("ownerCode");
        complaintSendEmailRecordDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailRecordDTO.setOrgId(0);

        when(mockComplaintSendEmailRecordMapper.insert(any(ComplaintSendEmailRecordPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintSendEmailRecordServiceImplUnderTest.insert(complaintSendEmailRecordDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO = new ComplaintSendEmailRecordDTO();
        complaintSendEmailRecordDTO.setReceipter("receipter");
        complaintSendEmailRecordDTO.setAppId("appId");
        complaintSendEmailRecordDTO.setOwnerCode("ownerCode");
        complaintSendEmailRecordDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailRecordDTO.setOrgId(0);

        // Configure ComplaintSendEmailRecordMapper.selectById(...).
        final ComplaintSendEmailRecordPO complaintSendEmailRecordPO = new ComplaintSendEmailRecordPO();
        complaintSendEmailRecordPO.setAppId("appId");
        complaintSendEmailRecordPO.setOwnerCode("ownerCode");
        complaintSendEmailRecordPO.setOwnerParCode("ownerParCode");
        complaintSendEmailRecordPO.setOrgId(0);
        complaintSendEmailRecordPO.setId(0L);
        when(mockComplaintSendEmailRecordMapper.selectById(0L)).thenReturn(complaintSendEmailRecordPO);

        when(mockComplaintSendEmailRecordMapper.updateById(any(ComplaintSendEmailRecordPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintSendEmailRecordServiceImplUnderTest.update(0L, complaintSendEmailRecordDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectLastEmail() {
        // Setup
        // Configure ComplaintSendEmailRecordMapper.selectLastEmail(...).
        final ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO = new ComplaintSendEmailRecordDTO();
        complaintSendEmailRecordDTO.setReceipter("receipter");
        complaintSendEmailRecordDTO.setAppId("appId");
        complaintSendEmailRecordDTO.setOwnerCode("ownerCode");
        complaintSendEmailRecordDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailRecordDTO.setOrgId(0);
        when(mockComplaintSendEmailRecordMapper.selectLastEmail(0L)).thenReturn(complaintSendEmailRecordDTO);

        // Run the test
        final ComplaintSendEmailRecordDTO result = complaintSendEmailRecordServiceImplUnderTest.selectLastEmail(0L);

        // Verify the results
    }

    @Test
    void testSelectEmaillist() {
        // Setup
        // Configure ComplaintSendEmailRecordMapper.selectEmaillist(...).
        final ComplaintSendEmailRecordDTO complaintSendEmailRecordDTO = new ComplaintSendEmailRecordDTO();
        complaintSendEmailRecordDTO.setReceipter("receipter");
        complaintSendEmailRecordDTO.setAppId("appId");
        complaintSendEmailRecordDTO.setOwnerCode("ownerCode");
        complaintSendEmailRecordDTO.setOwnerParCode("ownerParCode");
        complaintSendEmailRecordDTO.setOrgId(0);
        final List<ComplaintSendEmailRecordDTO> complaintSendEmailRecordDTOS = Arrays.asList(
                complaintSendEmailRecordDTO);
        when(mockComplaintSendEmailRecordMapper.selectEmaillist(0L)).thenReturn(complaintSendEmailRecordDTOS);

        // Run the test
        final List<ComplaintSendEmailRecordDTO> result = complaintSendEmailRecordServiceImplUnderTest.selectEmaillist(
                0L);

        // Verify the results
    }

    @Test
    void testSelectEmaillist_ComplaintSendEmailRecordMapperReturnsNoItems() {
        // Setup
        when(mockComplaintSendEmailRecordMapper.selectEmaillist(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintSendEmailRecordDTO> result = complaintSendEmailRecordServiceImplUnderTest.selectEmaillist(
                0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
