package com.yonyou.dmscus.customer.service.impl.invitationautocreate;

import com.google.common.collect.Lists;
import com.yonyou.dmscus.customer.constants.CommonConstants;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dao.invitationautocreate.InviteVehicleTaskMapper;
import com.yonyou.dmscus.customer.dao.inviteRule.InviteRuleMapper;
import com.yonyou.dmscus.customer.dao.voc.VocInviteVehicleTaskRecordMapper;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.invitationautocreate.InviteVehicleTaskService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteVehicleTaskServiceImplTest {

    @Mock
    private VocInviteVehicleTaskRecordMapper mockVocInviteVehicleTaskRecordMapper;
    @Mock
    private InviteVehicleRecordMapper mockInviteVehicleRecordMapper;
    @Mock
    private InviteVehicleRecordService mockInviteVehicleRecordService;

    @InjectMocks
    private InviteVehicleTaskServiceImpl InviteVehicleTaskServiceImplTest;

    @Test
    void testCloseInvite() {
        // Setup
        when(mockInviteVehicleRecordService.getWhiteList(eq(CommonConstants.MOD_TYPE_91111015))).thenReturn(Lists.newArrayList("SHJ"));
        when(mockInviteVehicleRecordService.getWhiteList(eq(CommonConstants.MOD_TYPE_91111011))).thenReturn(Lists.newArrayList("SHG"));
        List<InviteVehicleTaskPO> list = new ArrayList<>();
        InviteVehicleTaskPO po1 = new InviteVehicleTaskPO();
        po1.setDealerCode("ABC");
        po1.setInviteType(82381001);
        InviteVehicleTaskPO po2 = new InviteVehicleTaskPO();
        po2.setDealerCode("CBA");
        po2.setSourceType(4);
        po2.setInviteType(82381002);
        InviteVehicleTaskPO po3 = new InviteVehicleTaskPO();
        po3.setDealerCode("SHJ");
        po3.setSourceType(4);
        po3.setInviteType(82381002);
        InviteVehicleTaskPO po4 = new InviteVehicleTaskPO();
        po4.setDealerCode("SHG");
        po4.setInviteType(82381002);
        list.add(po1);list.add(po2);list.add(po3);list.add(po4);
        when(mockInviteVehicleRecordService.getWaitCloseRecord(any())).thenReturn(list);

        when(mockVocInviteVehicleTaskRecordMapper.selectOne(any())).thenReturn(null);

        // Run the test
        final int result = InviteVehicleTaskServiceImplTest.closeInvite(null);

        // Verify the results
        assertEquals(1, result);
    }

    @Test
    void testCloseInvite_getWhiteList() {
        // Setup
        when(mockInviteVehicleRecordService.getWhiteList(eq(CommonConstants.MOD_TYPE_91111015))).thenReturn(null);
        when(mockInviteVehicleRecordService.getWhiteList(eq(CommonConstants.MOD_TYPE_91111011))).thenReturn(null);
        List<InviteVehicleTaskPO> list = new ArrayList<>();
        InviteVehicleTaskPO po2 = new InviteVehicleTaskPO();
        po2.setDealerCode("CBA");
        po2.setSourceType(4);
        InviteVehicleTaskPO po3 = new InviteVehicleTaskPO();
        po3.setDealerCode("SHJ");
        po3.setSourceType(4);
        list.add(po2);list.add(po3);
        when(mockInviteVehicleRecordService.getWaitCloseRecord(any())).thenReturn(list);

        // Run the test
        final int result = InviteVehicleTaskServiceImplTest.closeInvite(null);

        // Verify the results
        assertEquals(1, result);
    }
}
