package com.yonyou.dmscus.customer.service.impl.faultLight;

import com.yonyou.dmscus.customer.dao.faultLight.CluesDiagnosticInfoRelationMapper;
import com.yonyou.dmscus.customer.dao.faultLight.TtFaultLightClueMapper;
import com.yonyou.dmscus.customer.entity.dto.faultLight.HandleDimCluesDto;
import com.yonyou.dmscus.customer.entity.dto.faultLight.SourceClueIdRelationDto;
import com.yonyou.dmscus.customer.entity.po.faultLight.CluesDiagnosticInfoRelationPO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightCluePO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CluesDiagnosticInfoRelationServiceImplTest {
    @Mock(lenient = true)
    private TtFaultLightClueMapper mockTtFaultLightClueMapper;
    @Mock(lenient = true)
    private CluesDiagnosticInfoRelationMapper mockCluesDiagnosticInfoRelationMapper;

    @InjectMocks
    private CluesDiagnosticInfoRelationServiceImpl cluesDiagnosticInfoRelationServiceImplUnderTest;

    @Test
    public void testMarkIsDisplayDiagnosticInfoTroubleMoreThan0() {
        List<CluesDiagnosticInfoRelationPO> relationPOS = Collections.singletonList(CluesDiagnosticInfoRelationPO.builder()
                        .leadsPreprocessingId(1L)
                        .processingStatus(1)
                        .build());
        when(mockCluesDiagnosticInfoRelationMapper.batchUpdateProcessingStatusByIds(relationPOS)).thenReturn(1);

        List<TtFaultLightCluePO> faultLightCluePOS = Collections.singletonList(TtFaultLightCluePO.builder()
                .id(1L)
                .troubleCode("1")
                .build());
        when(mockTtFaultLightClueMapper.batchUpdateTroubleCodeBySourceClueId(faultLightCluePOS)).thenReturn(1);

        HandleDimCluesDto handleDimCluesDto = new HandleDimCluesDto();
        List<Long> leadsPreprocessingIds = Collections.singletonList(1L);
        handleDimCluesDto.setLeadsPreprocessingIds(leadsPreprocessingIds);
        List<Long> cluePrimaryIdList = Collections.singletonList(1L);
        handleDimCluesDto.setCluePrimaryIdList(cluePrimaryIdList);
        cluesDiagnosticInfoRelationServiceImplUnderTest.markIsDisplayDiagnosticInfo(handleDimCluesDto);
    }

    @Test
    public void testMarkIsDisplayDiagnosticInfoTroubleLessThan0() {
        List<CluesDiagnosticInfoRelationPO> relationPOS = Collections.singletonList(CluesDiagnosticInfoRelationPO.builder()
                .leadsPreprocessingId(1L)
                .processingStatus(1)
                .build());
        when(mockCluesDiagnosticInfoRelationMapper.batchUpdateProcessingStatusByIds(relationPOS)).thenReturn(1);

        List<TtFaultLightCluePO> faultLightCluePOS = Collections.singletonList(TtFaultLightCluePO.builder()
                .id(1L)
                .troubleCode("1")
                .build());
        when(mockTtFaultLightClueMapper.batchUpdateTroubleCodeBySourceClueId(faultLightCluePOS)).thenReturn(-1);

        HandleDimCluesDto handleDimCluesDto = new HandleDimCluesDto();
        List<Long> leadsPreprocessingIds = Collections.singletonList(1L);
        handleDimCluesDto.setLeadsPreprocessingIds(leadsPreprocessingIds);
        List<Long> cluePrimaryIdList = Collections.singletonList(1L);
        handleDimCluesDto.setCluePrimaryIdList(cluePrimaryIdList);
        cluesDiagnosticInfoRelationServiceImplUnderTest.markIsDisplayDiagnosticInfo(handleDimCluesDto);
    }

    @Test
    public void testRecordNumberOfExecutions() {

        List<CluesDiagnosticInfoRelationPO> relationPOS = Collections.singletonList(CluesDiagnosticInfoRelationPO.builder()
                .leadsPreprocessingId(1L)
                .processingStatus(1)
                .build());
        when(mockCluesDiagnosticInfoRelationMapper.batchUpdateProcessingStatusByIds(relationPOS)).thenReturn(1);

        List<CluesDiagnosticInfoRelationPO> relationPOList = Collections.singletonList(CluesDiagnosticInfoRelationPO.builder()
                        .numberOfExecutions(3)
                        .leadsPreprocessingId(1L)
                        .build());
        when(mockCluesDiagnosticInfoRelationMapper.batchUpdateNumberOfExecutionsByIds(relationPOList)).thenReturn(1);

        List<Long> sourceClueIds = Collections.singletonList(1L);
        List<SourceClueIdRelationDto> sourceClueIdRelations = Collections.singletonList(SourceClueIdRelationDto.builder()
                .sourceClueId(1L).cluePrimaryId(1L)
                .build());
        when(mockTtFaultLightClueMapper.queryRecordCountByIds(sourceClueIds)).thenReturn(sourceClueIdRelations);

        cluesDiagnosticInfoRelationServiceImplUnderTest.recordNumberOfExecutions(relationPOList);
    }

    @Test
    public void testQueryDiagnosticInfoRelationList() {
        List<CluesDiagnosticInfoRelationPO> relationPOS = new ArrayList<>();
        when(mockCluesDiagnosticInfoRelationMapper.queryDiagnosticInfoRelationList(0, 4, 2, "")).thenReturn(relationPOS);
        cluesDiagnosticInfoRelationServiceImplUnderTest.queryDiagnosticInfoRelationList(0, 4, "", 2);
    }

    @Test
    public void testQueryDiagnosticInfoRelationCount() {
        when(mockCluesDiagnosticInfoRelationMapper.queryDiagnosticInfoRelationCount("", 2)).thenReturn(1);
        cluesDiagnosticInfoRelationServiceImplUnderTest.queryDiagnosticInfoRelationCount("", 2);
    }
}
