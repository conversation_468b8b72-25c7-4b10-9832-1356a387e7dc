package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillMaterialUploadGuideMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillMaterialUploadGuideDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillMaterialUploadGuidePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillMaterialUploadGuideServiceImplTest {

    @Mock
    private GoodwillMaterialUploadGuideMapper mockGoodwillMaterialUploadGuideMapper;

    private GoodwillMaterialUploadGuideServiceImpl goodwillMaterialUploadGuideServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        goodwillMaterialUploadGuideServiceImplUnderTest = new GoodwillMaterialUploadGuideServiceImpl();
        goodwillMaterialUploadGuideServiceImplUnderTest.goodwillMaterialUploadGuideMapper = mockGoodwillMaterialUploadGuideMapper;
    }

    @Test
    void testSelectPageBysql() throws Exception {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO = new GoodwillMaterialUploadGuideDTO();
        goodwillMaterialUploadGuideDTO.setAppId("appId");
        goodwillMaterialUploadGuideDTO.setOwnerCode("ownerCode");
        goodwillMaterialUploadGuideDTO.setOwnerParCode("ownerParCode");
        goodwillMaterialUploadGuideDTO.setOrgId(0);
        goodwillMaterialUploadGuideDTO.setId(0L);

        // Configure GoodwillMaterialUploadGuideMapper.selectPageBySql(...).
        final GoodwillMaterialUploadGuidePO goodwillMaterialUploadGuidePO = new GoodwillMaterialUploadGuidePO();
        goodwillMaterialUploadGuidePO.setAppId("appId");
        goodwillMaterialUploadGuidePO.setOwnerCode("ownerCode");
        goodwillMaterialUploadGuidePO.setOwnerParCode("ownerParCode");
        goodwillMaterialUploadGuidePO.setOrgId(0);
        goodwillMaterialUploadGuidePO.setId(0L);
        final List<GoodwillMaterialUploadGuidePO> goodwillMaterialUploadGuidePOS = Arrays.asList(
                goodwillMaterialUploadGuidePO);
        when(mockGoodwillMaterialUploadGuideMapper.selectPageBySql(any(Page.class),
                any(GoodwillMaterialUploadGuidePO.class))).thenReturn(goodwillMaterialUploadGuidePOS);

        // Run the test
        final IPage<GoodwillMaterialUploadGuideDTO> result = goodwillMaterialUploadGuideServiceImplUnderTest.selectPageBysql(
                page, goodwillMaterialUploadGuideDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillMaterialUploadGuideMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO = new GoodwillMaterialUploadGuideDTO();
        goodwillMaterialUploadGuideDTO.setAppId("appId");
        goodwillMaterialUploadGuideDTO.setOwnerCode("ownerCode");
        goodwillMaterialUploadGuideDTO.setOwnerParCode("ownerParCode");
        goodwillMaterialUploadGuideDTO.setOrgId(0);
        goodwillMaterialUploadGuideDTO.setId(0L);

        when(mockGoodwillMaterialUploadGuideMapper.selectPageBySql(any(Page.class),
                any(GoodwillMaterialUploadGuidePO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillMaterialUploadGuideDTO> result = goodwillMaterialUploadGuideServiceImplUnderTest.selectPageBysql(
                page, goodwillMaterialUploadGuideDTO);

        // Verify the results
    }

    @Test
    void testSelecMaterialtInfo() {
        // Setup
        final GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO = new GoodwillMaterialUploadGuideDTO();
        goodwillMaterialUploadGuideDTO.setAppId("appId");
        goodwillMaterialUploadGuideDTO.setOwnerCode("ownerCode");
        goodwillMaterialUploadGuideDTO.setOwnerParCode("ownerParCode");
        goodwillMaterialUploadGuideDTO.setOrgId(0);
        goodwillMaterialUploadGuideDTO.setId(0L);

        // Configure GoodwillMaterialUploadGuideMapper.selecMaterialtInfo(...).
        final GoodwillMaterialUploadGuidePO goodwillMaterialUploadGuidePO = new GoodwillMaterialUploadGuidePO();
        goodwillMaterialUploadGuidePO.setAppId("appId");
        goodwillMaterialUploadGuidePO.setOwnerCode("ownerCode");
        goodwillMaterialUploadGuidePO.setOwnerParCode("ownerParCode");
        goodwillMaterialUploadGuidePO.setOrgId(0);
        goodwillMaterialUploadGuidePO.setId(0L);
        when(mockGoodwillMaterialUploadGuideMapper.selecMaterialtInfo(
                any(GoodwillMaterialUploadGuideDTO.class))).thenReturn(goodwillMaterialUploadGuidePO);

        // Run the test
        final GoodwillMaterialUploadGuideDTO result = goodwillMaterialUploadGuideServiceImplUnderTest.selecMaterialtInfo(
                goodwillMaterialUploadGuideDTO);

        // Verify the results
    }

    @Test
    void testSelecMaterialtInfo_GoodwillMaterialUploadGuideMapperReturnsNull() {
        // Setup
        final GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO = new GoodwillMaterialUploadGuideDTO();
        goodwillMaterialUploadGuideDTO.setAppId("appId");
        goodwillMaterialUploadGuideDTO.setOwnerCode("ownerCode");
        goodwillMaterialUploadGuideDTO.setOwnerParCode("ownerParCode");
        goodwillMaterialUploadGuideDTO.setOrgId(0);
        goodwillMaterialUploadGuideDTO.setId(0L);

        when(mockGoodwillMaterialUploadGuideMapper.selecMaterialtInfo(
                any(GoodwillMaterialUploadGuideDTO.class))).thenReturn(null);

        // Run the test
        final GoodwillMaterialUploadGuideDTO result = goodwillMaterialUploadGuideServiceImplUnderTest.selecMaterialtInfo(
                goodwillMaterialUploadGuideDTO);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    void testGetById() throws Exception {
        // Setup
        // Configure GoodwillMaterialUploadGuideMapper.selectById(...).
        final GoodwillMaterialUploadGuidePO goodwillMaterialUploadGuidePO = new GoodwillMaterialUploadGuidePO();
        goodwillMaterialUploadGuidePO.setAppId("appId");
        goodwillMaterialUploadGuidePO.setOwnerCode("ownerCode");
        goodwillMaterialUploadGuidePO.setOwnerParCode("ownerParCode");
        goodwillMaterialUploadGuidePO.setOrgId(0);
        goodwillMaterialUploadGuidePO.setId(0L);
        when(mockGoodwillMaterialUploadGuideMapper.selectById(0L)).thenReturn(goodwillMaterialUploadGuidePO);

        // Run the test
        final GoodwillMaterialUploadGuideDTO result = goodwillMaterialUploadGuideServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillMaterialUploadGuideMapperReturnsNull() {
        // Setup
        when(mockGoodwillMaterialUploadGuideMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillMaterialUploadGuideServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() throws Exception {
        // Setup
        final GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO = new GoodwillMaterialUploadGuideDTO();
        goodwillMaterialUploadGuideDTO.setAppId("appId");
        goodwillMaterialUploadGuideDTO.setOwnerCode("ownerCode");
        goodwillMaterialUploadGuideDTO.setOwnerParCode("ownerParCode");
        goodwillMaterialUploadGuideDTO.setOrgId(0);
        goodwillMaterialUploadGuideDTO.setId(0L);

        when(mockGoodwillMaterialUploadGuideMapper.insert(any(GoodwillMaterialUploadGuidePO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillMaterialUploadGuideServiceImplUnderTest.insert(goodwillMaterialUploadGuideDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() throws Exception {
        // Setup
        final GoodwillMaterialUploadGuideDTO goodwillMaterialUploadGuideDTO = new GoodwillMaterialUploadGuideDTO();
        goodwillMaterialUploadGuideDTO.setAppId("appId");
        goodwillMaterialUploadGuideDTO.setOwnerCode("ownerCode");
        goodwillMaterialUploadGuideDTO.setOwnerParCode("ownerParCode");
        goodwillMaterialUploadGuideDTO.setOrgId(0);
        goodwillMaterialUploadGuideDTO.setId(0L);

        // Configure GoodwillMaterialUploadGuideMapper.selectById(...).
        final GoodwillMaterialUploadGuidePO goodwillMaterialUploadGuidePO = new GoodwillMaterialUploadGuidePO();
        goodwillMaterialUploadGuidePO.setAppId("appId");
        goodwillMaterialUploadGuidePO.setOwnerCode("ownerCode");
        goodwillMaterialUploadGuidePO.setOwnerParCode("ownerParCode");
        goodwillMaterialUploadGuidePO.setOrgId(0);
        goodwillMaterialUploadGuidePO.setId(0L);
        when(mockGoodwillMaterialUploadGuideMapper.selectById(0L)).thenReturn(goodwillMaterialUploadGuidePO);

        when(mockGoodwillMaterialUploadGuideMapper.updateById(any(GoodwillMaterialUploadGuidePO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillMaterialUploadGuideServiceImplUnderTest.update(0L, goodwillMaterialUploadGuideDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
