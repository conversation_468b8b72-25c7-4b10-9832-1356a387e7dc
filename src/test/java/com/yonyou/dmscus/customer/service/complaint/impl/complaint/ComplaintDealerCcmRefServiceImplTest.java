package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.service.excel.ExcelRead;
import com.yonyou.dmscloud.framework.service.excel.impl.AbstractExcelReadCallBack;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.configuration.MidUrlProperties;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintDealerCcmRefMapper;
import com.yonyou.dmscus.customer.dao.complaint.TeComplaintDealerCcmRefImportMapper;
import com.yonyou.dmscus.customer.dto.OrgSearchParams;
import com.yonyou.dmscus.customer.dto.UserInfoOutDTO;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.IsExistByCodeDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.*;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintDealerCcmRefPO;
import com.yonyou.dmscus.customer.entity.po.complaint.TeComplaintDealerCcmRefImportPO;
import com.yonyou.dmscus.customer.middleInterface.CompanyDetailByCodeDTO;
import com.yonyou.dmscus.customer.middleInterface.ResponseDTO;
import com.yonyou.dmscus.customer.middleInterface.ResponseListDTO;
import com.yonyou.dmscus.customer.service.complaint.TeComplaintDealerCcmRefImportService;
import com.yonyou.dmscus.customer.service.impl.CommonServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintDealerCcmRefServiceImplTest {

    @Mock
    private ComplaintDealerCcmRefMapper mockComplaintDealerCcmRefMapper;
    @Mock
    private ExcelRead<CcmImportDTO> mockExcelReadService;
    @Mock
    private RestTemplate mockDirectRestTemplate;
    @Mock
    private CommonServiceImpl mockCommonService;
    @Mock
    private MidUrlProperties mockMidUrlProperties;
    @Mock
    private TeComplaintDealerCcmRefImportService mockTeComplaintDealerCcmRefImportService;
    @Mock
    private TeComplaintDealerCcmRefImportMapper mockTeComplaintDealerCcmRefImportMapper;

    @InjectMocks
    private ComplaintDealerCcmRefServiceImpl complaintDealerCcmRefServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        complaintDealerCcmRefServiceImplUnderTest.complaintDealerCcmRefMapper = mockComplaintDealerCcmRefMapper;
        complaintDealerCcmRefServiceImplUnderTest.excelReadService = mockExcelReadService;
        complaintDealerCcmRefServiceImplUnderTest.teComplaintDealerCcmRefImportService = mockTeComplaintDealerCcmRefImportService;
        complaintDealerCcmRefServiceImplUnderTest.teComplaintDealerCcmRefImportMapper = mockTeComplaintDealerCcmRefImportMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO = new ComplaintDealerCcmRefDTO();
        complaintDealerCcmRefDTO.setAppId("appId");
        complaintDealerCcmRefDTO.setOwnerCode("ownerCode");
        complaintDealerCcmRefDTO.setOwnerParCode("ownerParCode");
        complaintDealerCcmRefDTO.setOrgId(0);
        complaintDealerCcmRefDTO.setId(0L);

        // Configure ComplaintDealerCcmRefMapper.selectPageBySql(...).
        final ComplaintDealerCcmRefPO complaintDealerCcmRefPO = new ComplaintDealerCcmRefPO();
        complaintDealerCcmRefPO.setRegionId(0L);
        complaintDealerCcmRefPO.setRegionManagerId(0L);
        complaintDealerCcmRefPO.setOwnerCode("ownerCode");
        complaintDealerCcmRefPO.setOrgId(0);
        complaintDealerCcmRefPO.setId(0L);
        final List<ComplaintDealerCcmRefPO> complaintDealerCcmRefPOS = Arrays.asList(complaintDealerCcmRefPO);
        when(mockComplaintDealerCcmRefMapper.selectPageBySql(any(Page.class),
                any(ComplaintDealerCcmRefPO.class))).thenReturn(complaintDealerCcmRefPOS);

        // Run the test
        final IPage<ComplaintDealerCcmRefDTO> result = complaintDealerCcmRefServiceImplUnderTest.selectPageBysql(page,
                complaintDealerCcmRefDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintDealerCcmRefMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO = new ComplaintDealerCcmRefDTO();
        complaintDealerCcmRefDTO.setAppId("appId");
        complaintDealerCcmRefDTO.setOwnerCode("ownerCode");
        complaintDealerCcmRefDTO.setOwnerParCode("ownerParCode");
        complaintDealerCcmRefDTO.setOrgId(0);
        complaintDealerCcmRefDTO.setId(0L);

        when(mockComplaintDealerCcmRefMapper.selectPageBySql(any(Page.class),
                any(ComplaintDealerCcmRefPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintDealerCcmRefDTO> result = complaintDealerCcmRefServiceImplUnderTest.selectPageBysql(page,
                complaintDealerCcmRefDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO = new ComplaintDealerCcmRefDTO();
        complaintDealerCcmRefDTO.setAppId("appId");
        complaintDealerCcmRefDTO.setOwnerCode("ownerCode");
        complaintDealerCcmRefDTO.setOwnerParCode("ownerParCode");
        complaintDealerCcmRefDTO.setOrgId(0);
        complaintDealerCcmRefDTO.setId(0L);

        // Configure ComplaintDealerCcmRefMapper.selectListBySql(...).
        final ComplaintDealerCcmRefPO complaintDealerCcmRefPO = new ComplaintDealerCcmRefPO();
        complaintDealerCcmRefPO.setRegionId(0L);
        complaintDealerCcmRefPO.setRegionManagerId(0L);
        complaintDealerCcmRefPO.setOwnerCode("ownerCode");
        complaintDealerCcmRefPO.setOrgId(0);
        complaintDealerCcmRefPO.setId(0L);
        final List<ComplaintDealerCcmRefPO> complaintDealerCcmRefPOS = Arrays.asList(complaintDealerCcmRefPO);
        when(mockComplaintDealerCcmRefMapper.selectListBySql(any(ComplaintDealerCcmRefPO.class)))
                .thenReturn(complaintDealerCcmRefPOS);

        // Run the test
        final List<ComplaintDealerCcmRefDTO> result = complaintDealerCcmRefServiceImplUnderTest.selectListBySql(
                complaintDealerCcmRefDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintDealerCcmRefMapperReturnsNoItems() {
        // Setup
        final ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO = new ComplaintDealerCcmRefDTO();
        complaintDealerCcmRefDTO.setAppId("appId");
        complaintDealerCcmRefDTO.setOwnerCode("ownerCode");
        complaintDealerCcmRefDTO.setOwnerParCode("ownerParCode");
        complaintDealerCcmRefDTO.setOrgId(0);
        complaintDealerCcmRefDTO.setId(0L);

        when(mockComplaintDealerCcmRefMapper.selectListBySql(any(ComplaintDealerCcmRefPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintDealerCcmRefDTO> result = complaintDealerCcmRefServiceImplUnderTest.selectListBySql(
                complaintDealerCcmRefDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure ComplaintDealerCcmRefMapper.selectById(...).
        final ComplaintDealerCcmRefPO complaintDealerCcmRefPO = new ComplaintDealerCcmRefPO();
        complaintDealerCcmRefPO.setRegionId(0L);
        complaintDealerCcmRefPO.setRegionManagerId(0L);
        complaintDealerCcmRefPO.setOwnerCode("ownerCode");
        complaintDealerCcmRefPO.setOrgId(0);
        complaintDealerCcmRefPO.setId(0L);
        when(mockComplaintDealerCcmRefMapper.selectById(0L)).thenReturn(complaintDealerCcmRefPO);

        // Run the test
        final ComplaintDealerCcmRefDTO result = complaintDealerCcmRefServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintDealerCcmRefMapperReturnsNull() {
        // Setup
        when(mockComplaintDealerCcmRefMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintDealerCcmRefServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO = new ComplaintDealerCcmRefDTO();
        complaintDealerCcmRefDTO.setAppId("appId");
        complaintDealerCcmRefDTO.setOwnerCode("ownerCode");
        complaintDealerCcmRefDTO.setOwnerParCode("ownerParCode");
        complaintDealerCcmRefDTO.setOrgId(0);
        complaintDealerCcmRefDTO.setId(0L);

        when(mockComplaintDealerCcmRefMapper.insert(any(ComplaintDealerCcmRefPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintDealerCcmRefServiceImplUnderTest.insert(complaintDealerCcmRefDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO = new ComplaintDealerCcmRefDTO();
        complaintDealerCcmRefDTO.setAppId("appId");
        complaintDealerCcmRefDTO.setOwnerCode("ownerCode");
        complaintDealerCcmRefDTO.setOwnerParCode("ownerParCode");
        complaintDealerCcmRefDTO.setOrgId(0);
        complaintDealerCcmRefDTO.setId(0L);

        // Configure ComplaintDealerCcmRefMapper.selectById(...).
        final ComplaintDealerCcmRefPO complaintDealerCcmRefPO = new ComplaintDealerCcmRefPO();
        complaintDealerCcmRefPO.setRegionId(0L);
        complaintDealerCcmRefPO.setRegionManagerId(0L);
        complaintDealerCcmRefPO.setOwnerCode("ownerCode");
        complaintDealerCcmRefPO.setOrgId(0);
        complaintDealerCcmRefPO.setId(0L);
        when(mockComplaintDealerCcmRefMapper.selectById(0L)).thenReturn(complaintDealerCcmRefPO);

        when(mockComplaintDealerCcmRefMapper.updateById(any(ComplaintDealerCcmRefPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintDealerCcmRefServiceImplUnderTest.update(0L, complaintDealerCcmRefDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdateCcmAll() {
        // Setup
        final ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO = new ComplaintDealerCcmRefDTO();
        complaintDealerCcmRefDTO.setAppId("appId");
        complaintDealerCcmRefDTO.setOwnerCode("ownerCode");
        complaintDealerCcmRefDTO.setOwnerParCode("ownerParCode");
        complaintDealerCcmRefDTO.setOrgId(0);
        complaintDealerCcmRefDTO.setId(0L);

        when(mockComplaintDealerCcmRefMapper.updateCcmAll(any(ComplaintDealerCcmRefDTO.class))).thenReturn(false);

        // Run the test
        final boolean result = complaintDealerCcmRefServiceImplUnderTest.updateCcmAll(complaintDealerCcmRefDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testUpdateCcmAll_ComplaintDealerCcmRefMapperReturnsTrue() {
        // Setup
        final ComplaintDealerCcmRefDTO complaintDealerCcmRefDTO = new ComplaintDealerCcmRefDTO();
        complaintDealerCcmRefDTO.setAppId("appId");
        complaintDealerCcmRefDTO.setOwnerCode("ownerCode");
        complaintDealerCcmRefDTO.setOwnerParCode("ownerParCode");
        complaintDealerCcmRefDTO.setOrgId(0);
        complaintDealerCcmRefDTO.setId(0L);

        when(mockComplaintDealerCcmRefMapper.updateCcmAll(any(ComplaintDealerCcmRefDTO.class))).thenReturn(true);

        // Run the test
        final boolean result = complaintDealerCcmRefServiceImplUnderTest.updateCcmAll(complaintDealerCcmRefDTO);

        // Verify the results
        assertThat(result).isTrue();
    }
}
