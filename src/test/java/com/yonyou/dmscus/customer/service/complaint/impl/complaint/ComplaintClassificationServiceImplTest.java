package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintClassificationMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintClassificationDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintClassificationPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintClassificationServiceImplTest {

    @Mock
    private ComplaintClassificationMapper mockComplaintClassificationMapper;

    private ComplaintClassificationServiceImpl complaintClassificationServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        complaintClassificationServiceImplUnderTest = new ComplaintClassificationServiceImpl();
        complaintClassificationServiceImplUnderTest.complaintClassificationMapper = mockComplaintClassificationMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintClassificationDTO complaintClassificationDTO = new ComplaintClassificationDTO();
        complaintClassificationDTO.setParentId1("parentId1");
        complaintClassificationDTO.setAppId("appId");
        complaintClassificationDTO.setOwnerCode("ownerCode");
        complaintClassificationDTO.setOwnerParCode("ownerParCode");
        complaintClassificationDTO.setOrgId(0);

        // Configure ComplaintClassificationMapper.selectPageBySql(...).
        final ComplaintClassificationPO complaintClassificationPO = new ComplaintClassificationPO();
        complaintClassificationPO.setParentId1("parentId1");
        complaintClassificationPO.setAppId("appId");
        complaintClassificationPO.setOwnerCode("ownerCode");
        complaintClassificationPO.setOwnerParCode("ownerParCode");
        complaintClassificationPO.setOrgId(0);
        final List<ComplaintClassificationPO> complaintClassificationPOS = Arrays.asList(complaintClassificationPO);
        when(mockComplaintClassificationMapper.selectPageBySql(any(Page.class),
                any(ComplaintClassificationPO.class))).thenReturn(complaintClassificationPOS);

        // Run the test
        final IPage<ComplaintClassificationDTO> result = complaintClassificationServiceImplUnderTest.selectPageBysql(
                page, complaintClassificationDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintClassificationMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintClassificationDTO complaintClassificationDTO = new ComplaintClassificationDTO();
        complaintClassificationDTO.setParentId1("parentId1");
        complaintClassificationDTO.setAppId("appId");
        complaintClassificationDTO.setOwnerCode("ownerCode");
        complaintClassificationDTO.setOwnerParCode("ownerParCode");
        complaintClassificationDTO.setOrgId(0);

        when(mockComplaintClassificationMapper.selectPageBySql(any(Page.class),
                any(ComplaintClassificationPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintClassificationDTO> result = complaintClassificationServiceImplUnderTest.selectPageBysql(
                page, complaintClassificationDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final ComplaintClassificationDTO complaintClassificationDTO = new ComplaintClassificationDTO();
        complaintClassificationDTO.setParentId1("parentId1");
        complaintClassificationDTO.setAppId("appId");
        complaintClassificationDTO.setOwnerCode("ownerCode");
        complaintClassificationDTO.setOwnerParCode("ownerParCode");
        complaintClassificationDTO.setOrgId(0);

        // Configure ComplaintClassificationMapper.selectListBySql(...).
        final ComplaintClassificationPO complaintClassificationPO = new ComplaintClassificationPO();
        complaintClassificationPO.setParentId1("parentId1");
        complaintClassificationPO.setAppId("appId");
        complaintClassificationPO.setOwnerCode("ownerCode");
        complaintClassificationPO.setOwnerParCode("ownerParCode");
        complaintClassificationPO.setOrgId(0);
        final List<ComplaintClassificationPO> complaintClassificationPOS = Arrays.asList(complaintClassificationPO);
        when(mockComplaintClassificationMapper.selectListBySql(any(ComplaintClassificationPO.class)))
                .thenReturn(complaintClassificationPOS);

        // Run the test
        final List<ComplaintClassificationDTO> result = complaintClassificationServiceImplUnderTest.selectListBySql(
                complaintClassificationDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintClassificationMapperReturnsNoItems() {
        // Setup
        final ComplaintClassificationDTO complaintClassificationDTO = new ComplaintClassificationDTO();
        complaintClassificationDTO.setParentId1("parentId1");
        complaintClassificationDTO.setAppId("appId");
        complaintClassificationDTO.setOwnerCode("ownerCode");
        complaintClassificationDTO.setOwnerParCode("ownerParCode");
        complaintClassificationDTO.setOrgId(0);

        when(mockComplaintClassificationMapper.selectListBySql(any(ComplaintClassificationPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintClassificationDTO> result = complaintClassificationServiceImplUnderTest.selectListBySql(
                complaintClassificationDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure ComplaintClassificationMapper.selectById(...).
        final ComplaintClassificationPO complaintClassificationPO = new ComplaintClassificationPO();
        complaintClassificationPO.setParentId1("parentId1");
        complaintClassificationPO.setAppId("appId");
        complaintClassificationPO.setOwnerCode("ownerCode");
        complaintClassificationPO.setOwnerParCode("ownerParCode");
        complaintClassificationPO.setOrgId(0);
        when(mockComplaintClassificationMapper.selectById(0L)).thenReturn(complaintClassificationPO);

        // Run the test
        final ComplaintClassificationDTO result = complaintClassificationServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintClassificationMapperReturnsNull() {
        // Setup
        when(mockComplaintClassificationMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintClassificationServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final ComplaintClassificationDTO complaintClassificationDTO = new ComplaintClassificationDTO();
        complaintClassificationDTO.setParentId1("parentId1");
        complaintClassificationDTO.setAppId("appId");
        complaintClassificationDTO.setOwnerCode("ownerCode");
        complaintClassificationDTO.setOwnerParCode("ownerParCode");
        complaintClassificationDTO.setOrgId(0);

        when(mockComplaintClassificationMapper.insert(any(ComplaintClassificationPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintClassificationServiceImplUnderTest.insert(complaintClassificationDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final ComplaintClassificationDTO complaintClassificationDTO = new ComplaintClassificationDTO();
        complaintClassificationDTO.setParentId1("parentId1");
        complaintClassificationDTO.setAppId("appId");
        complaintClassificationDTO.setOwnerCode("ownerCode");
        complaintClassificationDTO.setOwnerParCode("ownerParCode");
        complaintClassificationDTO.setOrgId(0);

        // Configure ComplaintClassificationMapper.selectById(...).
        final ComplaintClassificationPO complaintClassificationPO = new ComplaintClassificationPO();
        complaintClassificationPO.setParentId1("parentId1");
        complaintClassificationPO.setAppId("appId");
        complaintClassificationPO.setOwnerCode("ownerCode");
        complaintClassificationPO.setOwnerParCode("ownerParCode");
        complaintClassificationPO.setOrgId(0);
        when(mockComplaintClassificationMapper.selectById(0L)).thenReturn(complaintClassificationPO);

        when(mockComplaintClassificationMapper.updateById(any(ComplaintClassificationPO.class))).thenReturn(0);

        // Run the test
        final int result = complaintClassificationServiceImplUnderTest.update(0L, complaintClassificationDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectComplaintCategory() {
        // Setup
        final ComplaintClassificationDTO complaintClassificationDTO = new ComplaintClassificationDTO();
        complaintClassificationDTO.setParentId1("parentId1");
        complaintClassificationDTO.setAppId("appId");
        complaintClassificationDTO.setOwnerCode("ownerCode");
        complaintClassificationDTO.setOwnerParCode("ownerParCode");
        complaintClassificationDTO.setOrgId(0);

        // Configure ComplaintClassificationMapper.selectComplaintCategory(...).
        final ComplaintClassificationDTO complaintClassificationDTO1 = new ComplaintClassificationDTO();
        complaintClassificationDTO1.setParentId1("parentId1");
        complaintClassificationDTO1.setAppId("appId");
        complaintClassificationDTO1.setOwnerCode("ownerCode");
        complaintClassificationDTO1.setOwnerParCode("ownerParCode");
        complaintClassificationDTO1.setOrgId(0);
        final List<ComplaintClassificationDTO> complaintClassificationDTOS = Arrays.asList(complaintClassificationDTO1);
        when(mockComplaintClassificationMapper.selectComplaintCategory(
                any(ComplaintClassificationDTO.class))).thenReturn(complaintClassificationDTOS);

        // Run the test
        final List<ComplaintClassificationDTO> result = complaintClassificationServiceImplUnderTest.selectComplaintCategory(
                complaintClassificationDTO);

        // Verify the results
    }

    @Test
    void testSelectComplaintCategory_ComplaintClassificationMapperReturnsNoItems() {
        // Setup
        final ComplaintClassificationDTO complaintClassificationDTO = new ComplaintClassificationDTO();
        complaintClassificationDTO.setParentId1("parentId1");
        complaintClassificationDTO.setAppId("appId");
        complaintClassificationDTO.setOwnerCode("ownerCode");
        complaintClassificationDTO.setOwnerParCode("ownerParCode");
        complaintClassificationDTO.setOrgId(0);

        when(mockComplaintClassificationMapper.selectComplaintCategory(
                any(ComplaintClassificationDTO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintClassificationDTO> result = complaintClassificationServiceImplUnderTest.selectComplaintCategory(
                complaintClassificationDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetByIds() {
        // Setup
        // Configure ComplaintClassificationMapper.getByIds(...).
        final ComplaintClassificationPO complaintClassificationPO = new ComplaintClassificationPO();
        complaintClassificationPO.setParentId1("parentId1");
        complaintClassificationPO.setAppId("appId");
        complaintClassificationPO.setOwnerCode("ownerCode");
        complaintClassificationPO.setOwnerParCode("ownerParCode");
        complaintClassificationPO.setOrgId(0);
        final List<ComplaintClassificationPO> complaintClassificationPOS = Arrays.asList(complaintClassificationPO);
        when(mockComplaintClassificationMapper.getByIds(Arrays.asList(0L))).thenReturn(complaintClassificationPOS);

        // Run the test
        final List<ComplaintClassificationPO> result = complaintClassificationServiceImplUnderTest.getByIds(
                Arrays.asList(0L));

        // Verify the results
    }

    @Test
    void testGetByIds_ComplaintClassificationMapperReturnsNoItems() {
        // Setup
        when(mockComplaintClassificationMapper.getByIds(Arrays.asList(0L))).thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintClassificationPO> result = complaintClassificationServiceImplUnderTest.getByIds(
                Arrays.asList(0L));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
