package com.yonyou.dmscus.customer.service.impl.faultLight;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscus.customer.dao.faultLight.TtFaultLightClueMapper;
import com.yonyou.dmscus.customer.dao.faultLight.TtFaultLightFollowRecordMapper;
import com.yonyou.dmscus.customer.dao.faultLight.TtFaultLightInfoMapper;
import com.yonyou.dmscus.customer.dao.faultLight.TtFaultLightInvitationMapper;
import com.yonyou.dmscus.customer.dto.CustomerInfoDto;
import com.yonyou.dmscus.customer.entity.dto.businessPlatform.CompanyDetailDTO;
import com.yonyou.dmscus.customer.entity.dto.faultLight.*;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.CallDetailsDTO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightCluePO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightFollowRecordPO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightInfoPO;
import com.yonyou.dmscus.customer.entity.po.faultLight.TtFaultLightInvitationPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.CdpTagTaskPo;
import com.yonyou.dmscus.customer.feign.RepairCommonClient;
import com.yonyou.dmscus.customer.service.cdpTagTask.qb.CdpTagTaskService;
import com.yonyou.dmscus.customer.service.clueMigrate.ITmClueMigrateTaskService;
import com.yonyou.dmscus.customer.service.faultLight.FaultLightService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.mock.web.MockHttpServletResponse;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class FaultLightClueServiceImplTest {

    @Mock
    private TtFaultLightClueMapper mockTtFaultLightClueMapper;
    @Mock
    private TtFaultLightInvitationMapper mockTtFaultLightInvitationMapper;
    @Mock
    private TtFaultLightFollowRecordMapper mockTtFaultLightFollowRecordMapper;
    @Mock
    private TtFaultLightInfoMapper mockTtFaultLightInfoMapper;
    @Mock
    private ITmClueMigrateTaskService mockClueMigrateTaskService;
    @Mock
    private FaultLightService mockFaultLightService;
    @Mock
    private FaultLightClueServiceHelper mockFaultLightClueServiceHelper;
    @Mock
    private RepairCommonClient mockRepairCommonClient;
    @Mock
    private CdpTagTaskService mockCdpTagTaskService;

    @InjectMocks
    private FaultLightClueServiceImpl faultLightClueServiceImplUnderTest;
    @Mock
    private FaultLightTopicDTO tdto;


    @Test
    void testSelectRoNoSpinner_RepairCommonClientReturnsNoItems() {
        // Setup
        // Configure TtFaultLightClueMapper.selectById(...).
        final TtFaultLightCluePO ttFaultLightCluePO = TtFaultLightCluePO.builder()
                .id(0L)
                .icmId(0L)
                .vin("vehicleVin")
                .dealerCode("dealerCode")
                .dealerName("dealerName")
                .faultId(0L)
                .alarmTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .warningName("warningName")
                .regionId(0L)
                .regionName("regionName")
                .cellId(0L)
                .cellName("cellName")
                .cityId(0L)
                .cityName("cityName")
                .faultCityName("faultCityName")
                .faultCityId(0L)
                .groupCompanyShortName("groupCompanyShortName")
                .clueStatus(0)
                .followStatus(0)
                .clueGenTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueComTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .wheRes(0)
                .build();
        when(mockTtFaultLightClueMapper.selectById(0L)).thenReturn(ttFaultLightCluePO);

        when(mockRepairCommonClient.queryRoNoSpinner("dealerCode", "vehicleVin",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = faultLightClueServiceImplUnderTest.selectRoNoSpinner(0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testStateEscape() {
        // Setup
        final ClueInfoQueryRespDTO clueInfoQueryRespDTO = new ClueInfoQueryRespDTO();
        clueInfoQueryRespDTO.setId(0L);
        clueInfoQueryRespDTO.setNumber(0);
        clueInfoQueryRespDTO.setClueStatus(0);
        clueInfoQueryRespDTO.setClueStatusCn("clueStatusCn");
        clueInfoQueryRespDTO.setFollowStatus(0);
        clueInfoQueryRespDTO.setFollowStatusCn("followStatusCn");
        clueInfoQueryRespDTO.setContactOvertime("contactOvertime");
        clueInfoQueryRespDTO.setContactResult("contactResult");
        clueInfoQueryRespDTO.setIsDlr("isDlr");
        clueInfoQueryRespDTO.setClueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        clueInfoQueryRespDTO.setInviteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        clueInfoQueryRespDTO.setInviteOvertime("code");
        clueInfoQueryRespDTO.setInviteResult("inviteResult");
        clueInfoQueryRespDTO.setIntoOnTime("intoOnTime");
        clueInfoQueryRespDTO.setNoInto("noInto");
        clueInfoQueryRespDTO.setSelfInto("selfInto");
        clueInfoQueryRespDTO.setMissParts("missParts");
        clueInfoQueryRespDTO.setNoRepair("noRepair");
        clueInfoQueryRespDTO.setLightsUp("lightsUp");
        clueInfoQueryRespDTO.setWheRes(0);
        clueInfoQueryRespDTO.setWheResCn("wheResCn");
        final List<ClueInfoQueryRespDTO> clueInfoQueryRespDTOS = Arrays.asList(clueInfoQueryRespDTO);

        // Run the test
        faultLightClueServiceImplUnderTest.stateEscape(clueInfoQueryRespDTOS);

        // Verify the results
    }

    @Test
    void testFactoryQueryClueInfoList() {
        // Setup
        final Page<ClueInfoQueryRespDTO> page = new Page<>(0L, 0L, 0L, false);
        final ClueInfoQueryRequestDTO dto = new ClueInfoQueryRequestDTO();
        dto.setId(0L);
        dto.setClueStatus(Arrays.asList(0));
        dto.setVin("vin");
        dto.setDealerCode(Arrays.asList("value"));
        dto.setWheRes(0);

        // Configure TtFaultLightClueMapper.queryClueInfoList(...).
        final ClueInfoQueryRespDTO clueInfoQueryRespDTO = new ClueInfoQueryRespDTO();
        clueInfoQueryRespDTO.setId(0L);
        clueInfoQueryRespDTO.setNumber(0);
        clueInfoQueryRespDTO.setClueStatus(0);
        clueInfoQueryRespDTO.setClueStatusCn("clueStatusCn");
        clueInfoQueryRespDTO.setFollowStatus(0);
        clueInfoQueryRespDTO.setFollowStatusCn("followStatusCn");
        clueInfoQueryRespDTO.setContactOvertime("contactOvertime");
        clueInfoQueryRespDTO.setContactResult("contactResult");
        clueInfoQueryRespDTO.setIsDlr("isDlr");
        clueInfoQueryRespDTO.setClueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        clueInfoQueryRespDTO.setInviteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        clueInfoQueryRespDTO.setInviteOvertime("code");
        clueInfoQueryRespDTO.setInviteResult("inviteResult");
        clueInfoQueryRespDTO.setIntoOnTime("intoOnTime");
        clueInfoQueryRespDTO.setNoInto("noInto");
        clueInfoQueryRespDTO.setSelfInto("selfInto");
        clueInfoQueryRespDTO.setMissParts("missParts");
        clueInfoQueryRespDTO.setNoRepair("noRepair");
        clueInfoQueryRespDTO.setLightsUp("lightsUp");
        clueInfoQueryRespDTO.setWheRes(0);
        clueInfoQueryRespDTO.setWheResCn("wheResCn");
        final List<ClueInfoQueryRespDTO> clueInfoQueryRespDTOS = Arrays.asList(clueInfoQueryRespDTO);
        final ClueInfoQueryRequestDTO params = new ClueInfoQueryRequestDTO();
        params.setId(0L);
        params.setClueStatus(Arrays.asList(0));
        params.setVin("vin");
        params.setDealerCode(Arrays.asList("value"));
        params.setWheRes(0);
        when(mockTtFaultLightClueMapper.queryClueInfoList(any(Page.class), eq(params)))
                .thenReturn(clueInfoQueryRespDTOS);

        // Run the test
        final IPage<ClueInfoQueryRespDTO> result = faultLightClueServiceImplUnderTest.factoryQueryClueInfoList(page,
                dto);

        // Verify the results
    }

    @Test
    void testFactoryQueryClueInfoList_TtFaultLightClueMapperReturnsNoItems() {
        // Setup
        final Page<ClueInfoQueryRespDTO> page = new Page<>(0L, 0L, 0L, false);
        final ClueInfoQueryRequestDTO dto = new ClueInfoQueryRequestDTO();
        dto.setId(0L);
        dto.setClueStatus(Arrays.asList(0));
        dto.setVin("vin");
        dto.setDealerCode(Arrays.asList("value"));
        dto.setWheRes(0);

        // Configure TtFaultLightClueMapper.queryClueInfoList(...).
        final ClueInfoQueryRequestDTO params = new ClueInfoQueryRequestDTO();
        params.setId(0L);
        params.setClueStatus(Arrays.asList(0));
        params.setVin("vin");
        params.setDealerCode(Arrays.asList("value"));
        params.setWheRes(0);
        when(mockTtFaultLightClueMapper.queryClueInfoList(any(Page.class), eq(params)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ClueInfoQueryRespDTO> result = faultLightClueServiceImplUnderTest.factoryQueryClueInfoList(page,
                dto);

        // Verify the results
    }

    @Test
    void testFactoryClueInfoDerive() {
        // Setup
        final ClueInfoQueryRequestDTO dto = new ClueInfoQueryRequestDTO();
        dto.setId(0L);
        dto.setClueStatus(Arrays.asList(0));
        dto.setVin("vin");
        dto.setDealerCode(Arrays.asList("value"));
        dto.setWheRes(0);

        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure TtFaultLightClueMapper.queryClueInfoList(...).
        final ClueInfoQueryRespDTO clueInfoQueryRespDTO = new ClueInfoQueryRespDTO();
        clueInfoQueryRespDTO.setId(0L);
        clueInfoQueryRespDTO.setNumber(0);
        clueInfoQueryRespDTO.setClueStatus(0);
        clueInfoQueryRespDTO.setClueStatusCn("clueStatusCn");
        clueInfoQueryRespDTO.setFollowStatus(0);
        clueInfoQueryRespDTO.setFollowStatusCn("followStatusCn");
        clueInfoQueryRespDTO.setContactOvertime("contactOvertime");
        clueInfoQueryRespDTO.setContactResult("contactResult");
        clueInfoQueryRespDTO.setIsDlr("isDlr");
        clueInfoQueryRespDTO.setClueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        clueInfoQueryRespDTO.setInviteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        clueInfoQueryRespDTO.setInviteOvertime("code");
        clueInfoQueryRespDTO.setInviteResult("inviteResult");
        clueInfoQueryRespDTO.setIntoOnTime("intoOnTime");
        clueInfoQueryRespDTO.setNoInto("noInto");
        clueInfoQueryRespDTO.setSelfInto("selfInto");
        clueInfoQueryRespDTO.setMissParts("missParts");
        clueInfoQueryRespDTO.setNoRepair("noRepair");
        clueInfoQueryRespDTO.setLightsUp("lightsUp");
        clueInfoQueryRespDTO.setWheRes(0);
        clueInfoQueryRespDTO.setWheResCn("wheResCn");
        final List<ClueInfoQueryRespDTO> clueInfoQueryRespDTOS = Arrays.asList(clueInfoQueryRespDTO);
        final ClueInfoQueryRequestDTO params = new ClueInfoQueryRequestDTO();
        params.setId(0L);
        params.setClueStatus(Arrays.asList(0));
        params.setVin("vin");
        params.setDealerCode(Arrays.asList("value"));
        params.setWheRes(0);
        when(mockTtFaultLightClueMapper.queryClueInfoList(any(Page.class), eq(params)))
                .thenReturn(clueInfoQueryRespDTOS);

        // Run the test
        faultLightClueServiceImplUnderTest.factoryClueInfoDerive(dto, response);

        // Verify the results
    }

    @Test
    void testFactoryClueInfoDerive_TtFaultLightClueMapperReturnsNoItems() {
        // Setup
        final ClueInfoQueryRequestDTO dto = new ClueInfoQueryRequestDTO();
        dto.setId(0L);
        dto.setClueStatus(Arrays.asList(0));
        dto.setVin("vin");
        dto.setDealerCode(Arrays.asList("value"));
        dto.setWheRes(0);

        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure TtFaultLightClueMapper.queryClueInfoList(...).
        final ClueInfoQueryRequestDTO params = new ClueInfoQueryRequestDTO();
        params.setId(0L);
        params.setClueStatus(Arrays.asList(0));
        params.setVin("vin");
        params.setDealerCode(Arrays.asList("value"));
        params.setWheRes(0);
        when(mockTtFaultLightClueMapper.queryClueInfoList(any(Page.class), eq(params)))
                .thenReturn(Collections.emptyList());

        // Run the test
        faultLightClueServiceImplUnderTest.factoryClueInfoDerive(dto, response);

        // Verify the results
    }


    @Test
    void testAddClueCompensate() {
        // Setup
        final ClueDataSynchroDTO dto = new ClueDataSynchroDTO();
        dto.setId(0L);
        dto.setDealerCode("dealerCode");
        dto.setDealerName("dealerName");
        dto.setVehicleVin("vehicleVin");
        dto.setRecommendDealerFlag(false);
        dto.setLeadsReceiveTime("leadsReceiveTime");
        final WarningInfoDTO warningInfo = new WarningInfoDTO();
        warningInfo.setWarningId("warningId");
        warningInfo.setWarningTime("warningTime");
        warningInfo.setWarningCityCN("faultCityName");
        warningInfo.setWarningCityId("warningCityId");
        dto.setWarningInfo(warningInfo);
        final ContactInfoDTO contactInfo = new ContactInfoDTO();
        contactInfo.setCustomerName("cusName");
        contactInfo.setCustomerMobile("cusPhone");
        contactInfo.setGender("gender");
        dto.setContactInfo(contactInfo);
        final CallInfoDTO callInfo = new CallInfoDTO();
        callInfo.setCallEndTime("callEndTime");
        callInfo.setFirstCallSeat("contactsName");
        callInfo.setCallResult("contactResult");
        callInfo.setComments("comments");
        dto.setCallInfo(callInfo);

        // Configure TtFaultLightInfoMapper.selectById(...).
        final TtFaultLightInfoPO ttFaultLightInfoPO = new TtFaultLightInfoPO();
        ttFaultLightInfoPO.setId(0L);
        ttFaultLightInfoPO.setSourceClueId("sourceClueId");
        ttFaultLightInfoPO.setVehicleVin("vehicleVin");
        ttFaultLightInfoPO.setDealerCode("dealerCode");
        ttFaultLightInfoPO.setDealerName("dealerName");
        when(mockTtFaultLightInfoMapper.selectById(0L)).thenReturn(ttFaultLightInfoPO);

        // Configure TtFaultLightInfoMapper.updateById(...).
        final TtFaultLightInfoPO entity = new TtFaultLightInfoPO();
        entity.setId(0L);
        entity.setSourceClueId("sourceClueId");
        entity.setVehicleVin("vehicleVin");
        entity.setDealerCode("dealerCode");
        entity.setDealerName("dealerName");
        when(mockTtFaultLightInfoMapper.updateById(entity)).thenReturn(0);

        // Run the test
        final int result = faultLightClueServiceImplUnderTest.addClueCompensate(dto);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testAddClueCompensate_TtFaultLightInfoMapperSelectByIdReturnsNull() {
        // Setup
        final ClueDataSynchroDTO dto = new ClueDataSynchroDTO();
        dto.setId(0L);
        dto.setDealerCode("dealerCode");
        dto.setDealerName("dealerName");
        dto.setVehicleVin("vehicleVin");
        dto.setRecommendDealerFlag(false);
        dto.setLeadsReceiveTime("leadsReceiveTime");
        final WarningInfoDTO warningInfo = new WarningInfoDTO();
        warningInfo.setWarningId("warningId");
        warningInfo.setWarningTime("warningTime");
        warningInfo.setWarningCityCN("faultCityName");
        warningInfo.setWarningCityId("warningCityId");
        dto.setWarningInfo(warningInfo);
        final ContactInfoDTO contactInfo = new ContactInfoDTO();
        contactInfo.setCustomerName("cusName");
        contactInfo.setCustomerMobile("cusPhone");
        contactInfo.setGender("gender");
        dto.setContactInfo(contactInfo);
        final CallInfoDTO callInfo = new CallInfoDTO();
        callInfo.setCallEndTime("callEndTime");
        callInfo.setFirstCallSeat("contactsName");
        callInfo.setCallResult("contactResult");
        callInfo.setComments("comments");
        dto.setCallInfo(callInfo);

        when(mockTtFaultLightInfoMapper.selectById(0L)).thenReturn(null);

        // Configure TtFaultLightInfoMapper.insert(...).
        final TtFaultLightInfoPO entity = new TtFaultLightInfoPO();
        entity.setId(0L);
        entity.setSourceClueId("sourceClueId");
        entity.setVehicleVin("vehicleVin");
        entity.setDealerCode("dealerCode");
        entity.setDealerName("dealerName");
        when(mockTtFaultLightInfoMapper.insert(entity)).thenReturn(0);

        // Run the test
        final int result = faultLightClueServiceImplUnderTest.addClueCompensate(dto);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdateClueAndCdpTagTask() {
        // Setup
        // Run the test
        faultLightClueServiceImplUnderTest.updateClueAndCdpTagTask(Arrays.asList(0L), Arrays.asList(0L));

        // Verify the results
        verify(mockTtFaultLightClueMapper).updateInviteOvertime(Arrays.asList(0L));
        verify(mockCdpTagTaskService).updateFaultLightClueById(Arrays.asList(0L));
    }

    @Test
    void testQueryFaultLight() {
        // Setup
        final CustomerInfoDto expectedResult = CustomerInfoDto.builder()
                .mobile("cusPhone")
                .customerName("cusName")
                .build();

        // Configure TtFaultLightClueMapper.selectOne(...).
        final TtFaultLightCluePO ttFaultLightCluePO = TtFaultLightCluePO.builder()
                .id(0L)
                .icmId(0L)
                .vin("vehicleVin")
                .dealerCode("dealerCode")
                .dealerName("dealerName")
                .faultId(0L)
                .alarmTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .warningName("warningName")
                .regionId(0L)
                .regionName("regionName")
                .cellId(0L)
                .cellName("cellName")
                .cityId(0L)
                .cityName("cityName")
                .faultCityName("faultCityName")
                .faultCityId(0L)
                .groupCompanyShortName("groupCompanyShortName")
                .clueStatus(0)
                .followStatus(0)
                .clueGenTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueComTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .wheRes(0)
                .build();
        when(mockTtFaultLightClueMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(ttFaultLightCluePO);

        // Configure TtFaultLightInvitationMapper.queryFaultLightInvitationByClurId(...).
        final TtFaultLightInvitationPO ttFaultLightInvitationPO = TtFaultLightInvitationPO.builder()
                .clueId(0L)
                .contactTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .contactsName("contactsName")
                .contactResTime("contactResTime")
                .contactOvertime(0)
                .contactResult("contactResult")
                .comments("comments")
                .dlr("dealerCode")
                .isDlr(0)
                .clueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .cusName("cusName")
                .cusPhone("cusPhone")
                .gender("gender")
                .build();
        when(mockTtFaultLightInvitationMapper.queryFaultLightInvitationByClurId(0L))
                .thenReturn(ttFaultLightInvitationPO);

        // Run the test
        final CustomerInfoDto result = faultLightClueServiceImplUnderTest.queryFaultLight("vin", "ownerCode");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testUpdateHighlights() {
        // Setup
        // Run the test
        faultLightClueServiceImplUnderTest.updateHighlights(Arrays.asList(0L));

        // Verify the results
        verify(mockTtFaultLightClueMapper).updateInviteOvertime(Arrays.asList(0L));
    }

    @Test
    void testUnHighlight() {
        // Setup
        final CallDetailsDTO callDetailsDTO = new CallDetailsDTO();
        callDetailsDTO.setAppId("appId");
        callDetailsDTO.setOwnerCode("ownerCode");
        callDetailsDTO.setOwnerParCode("ownerParCode");
        callDetailsDTO.setOrgId(0);
        callDetailsDTO.setCallId("callId");

        when(mockTtFaultLightClueMapper.queryIdByCallId("callId")).thenReturn(Arrays.asList(0L));

        // Run the test
        faultLightClueServiceImplUnderTest.unHighlight(callDetailsDTO);

        // Verify the results
        verify(mockTtFaultLightClueMapper).cancelHighlights(Arrays.asList(0L));
    }

    @Test
    void testUnHighlight_TtFaultLightClueMapperQueryIdByCallIdReturnsNoItems() {
        // Setup
        final CallDetailsDTO callDetailsDTO = new CallDetailsDTO();
        callDetailsDTO.setAppId("appId");
        callDetailsDTO.setOwnerCode("ownerCode");
        callDetailsDTO.setOwnerParCode("ownerParCode");
        callDetailsDTO.setOrgId(0);
        callDetailsDTO.setCallId("callId");

        when(mockTtFaultLightClueMapper.queryIdByCallId("callId")).thenReturn(Collections.emptyList());

        // Run the test
        faultLightClueServiceImplUnderTest.unHighlight(callDetailsDTO);

        // Verify the results
    }

    @Test
    void testCancelHighlights() {
        // Setup
        // Run the test
        faultLightClueServiceImplUnderTest.cancelHighlights(Arrays.asList(0L));

        // Verify the results
        verify(mockTtFaultLightClueMapper).cancelHighlights(Arrays.asList(0L));
    }

    @Test
    void testQuerySecondAppointmentId() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        when(mockTtFaultLightClueMapper.querySecondAppointment(any(Page.class), eq("createDate"),
                eq("endDate"))).thenReturn(Arrays.asList(0L));

        // Run the test
        faultLightClueServiceImplUnderTest.querySecondAppointmentId(page, "createDate", "endDate");

        // Verify the results
        verify(mockCdpTagTaskService).batchInsertHighlightFlagClueId(Arrays.asList(0L));
    }

    @Test
    void testBatchInsertHighlightFlagClueId() {
        // Setup
        // Run the test
        faultLightClueServiceImplUnderTest.batchInsertHighlightFlagClueId(Arrays.asList(0L));

        // Verify the results
        verify(mockCdpTagTaskService).batchInsertHighlightFlagClueId(Arrays.asList(0L));
    }

    @Test
    void testFaultLightConsumerStatus() {
        // Setup
        // Configure TtFaultLightClueMapper.selectCountByIcmId(...).
        final TtFaultLightCluePO ttFaultLightCluePO = TtFaultLightCluePO.builder()
                .id(0L)
                .icmId(0L)
                .vin("vehicleVin")
                .dealerCode("dealerCode")
                .dealerName("dealerName")
                .faultId(0L)
                .alarmTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .warningName("warningName")
                .regionId(0L)
                .regionName("regionName")
                .cellId(0L)
                .cellName("cellName")
                .cityId(0L)
                .cityName("cityName")
                .faultCityName("faultCityName")
                .faultCityId(0L)
                .groupCompanyShortName("groupCompanyShortName")
                .afClueStatus(0)
                .clueStatus(0)
                .followStatus(0)
                .clueGenTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueCloTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueComTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .wheRes(0)
                .updatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .build();
        when(mockTtFaultLightClueMapper.selectCountByIcmId(0L)).thenReturn(ttFaultLightCluePO);

        when(mockTtFaultLightClueMapper.updateFaultLightClueById(TtFaultLightCluePO.builder()
                .id(0L)
                .icmId(0L)
                .vin("vehicleVin")
                .dealerCode("dealerCode")
                .dealerName("dealerName")
                .faultId(0L)
                .alarmTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .warningName("warningName")
                .regionId(0L)
                .regionName("regionName")
                .cellId(0L)
                .cellName("cellName")
                .cityId(0L)
                .cityName("cityName")
                .faultCityName("faultCityName")
                .faultCityId(0L)
                .groupCompanyShortName("groupCompanyShortName")
                .afClueStatus(0)
                .clueStatus(0)
                .followStatus(0)
                .clueGenTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueCloTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueComTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .wheRes(0)
                .updatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .build())).thenReturn(0);

        // Configure FaultLightService.converClueToRecord(...).
        final TtFaultLightFollowRecordPO ttFaultLightFollowRecordPO = TtFaultLightFollowRecordPO.builder()
                .clueId(0L)
                .followTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .followName("followName")
                .build();
        when(mockFaultLightService.converClueToRecord(TtFaultLightCluePO.builder()
                .id(0L)
                .icmId(0L)
                .vin("vehicleVin")
                .dealerCode("dealerCode")
                .dealerName("dealerName")
                .faultId(0L)
                .alarmTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .warningName("warningName")
                .regionId(0L)
                .regionName("regionName")
                .cellId(0L)
                .cellName("cellName")
                .cityId(0L)
                .cityName("cityName")
                .faultCityName("faultCityName")
                .faultCityId(0L)
                .groupCompanyShortName("groupCompanyShortName")
                .afClueStatus(0)
                .clueStatus(0)
                .followStatus(0)
                .clueGenTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueDisTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueCloTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .clueComTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .wheRes(0)
                .updatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .build())).thenReturn(ttFaultLightFollowRecordPO);

        when(mockTtFaultLightFollowRecordMapper.insert(TtFaultLightFollowRecordPO.builder()
                .clueId(0L)
                .followTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .followName("followName")
                .build())).thenReturn(0);
        final FaultLightTopicDTO faultLightTopicDTO = new FaultLightTopicDTO();
        faultLightTopicDTO.setId(0L);
        faultLightTopicDTO.setBizStatus("0");
        faultLightTopicDTO.setFollowUpStatus("0");
        faultLightTopicDTO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        faultLightTopicDTO.setDealerCode("dealerCode");
        // Run the test
        faultLightClueServiceImplUnderTest.faultLightConsumerStatus(JSON.toJSONString(faultLightTopicDTO));

        // Verify the results
    }
}
