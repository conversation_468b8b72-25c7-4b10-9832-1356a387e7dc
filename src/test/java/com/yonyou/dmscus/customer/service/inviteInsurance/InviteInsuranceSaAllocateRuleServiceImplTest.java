package com.yonyou.dmscus.customer.service.inviteInsurance;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceSaAllocateRuleMapper;
import com.yonyou.dmscus.customer.dto.InvitationRuleVcdcParamsVo;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceSaAllocateRuleDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceSaAllocateRulePO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteInsuranceSaAllocateRuleServiceImplTest {

    @Mock
    private InviteInsuranceSaAllocateRuleMapper mockInviteInsuranceSaAllocateRuleMapper;
    @Mock
    private BusinessPlatformService mockBusinessPlatformService;

    private InviteInsuranceSaAllocateRuleServiceImpl inviteInsuranceSaAllocateRuleServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteInsuranceSaAllocateRuleServiceImplUnderTest = new InviteInsuranceSaAllocateRuleServiceImpl();
        inviteInsuranceSaAllocateRuleServiceImplUnderTest.inviteInsuranceSaAllocateRuleMapper = mockInviteInsuranceSaAllocateRuleMapper;
        inviteInsuranceSaAllocateRuleServiceImplUnderTest.businessPlatformService = mockBusinessPlatformService;
    }

    @Test
    void testGetInviteInsuranceSaAllocateRule() {
        // Setup
        final InvitationRuleVcdcParamsVo vo = new InvitationRuleVcdcParamsVo();
        vo.setLargeAreaId("largeAreaId");
        vo.setAreaId("areaId");
        vo.setAreaManageId("areaId");
        vo.setDealerCode("dealerCode");
        vo.setDealerName("dealerName");

        final InviteInsuranceSaAllocateRuleDTO inviteInsuranceSaAllocateRuleDTO = new InviteInsuranceSaAllocateRuleDTO();
        inviteInsuranceSaAllocateRuleDTO.setAppId("appId");
        inviteInsuranceSaAllocateRuleDTO.setOwnerCode("ownerCode");
        inviteInsuranceSaAllocateRuleDTO.setOwnerParCode("ownerParCode");
        inviteInsuranceSaAllocateRuleDTO.setOrgId(0);
        inviteInsuranceSaAllocateRuleDTO.setId(0L);
        final List<InviteInsuranceSaAllocateRuleDTO> expectedResult = Arrays.asList(inviteInsuranceSaAllocateRuleDTO);
        when(mockBusinessPlatformService.getDealercodes("areaId", "largeAreaId", "dealerCode",
                "dealerName")).thenReturn(Arrays.asList("value"));

        // Configure InviteInsuranceSaAllocateRuleMapper.getInviteInsuranceSaAllocateRule(...).
        final InviteInsuranceSaAllocateRulePO inviteInsuranceSaAllocateRulePO = new InviteInsuranceSaAllocateRulePO();
        inviteInsuranceSaAllocateRulePO.setOwnerCode("ownerCode");
        inviteInsuranceSaAllocateRulePO.setOwnerParCode("ownerParCode");
        inviteInsuranceSaAllocateRulePO.setOrgId(0);
        inviteInsuranceSaAllocateRulePO.setId(0L);
        inviteInsuranceSaAllocateRulePO.setDealerCode("dealerCode");
        final List<InviteInsuranceSaAllocateRulePO> inviteInsuranceSaAllocateRulePOS = Arrays.asList(
                inviteInsuranceSaAllocateRulePO);
        when(mockInviteInsuranceSaAllocateRuleMapper.getInviteInsuranceSaAllocateRule(
                Arrays.asList("value"))).thenReturn(inviteInsuranceSaAllocateRulePOS);

        // Run the test
        final List<InviteInsuranceSaAllocateRuleDTO> result = inviteInsuranceSaAllocateRuleServiceImplUnderTest.getInviteInsuranceSaAllocateRule(
                vo);

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetInviteInsuranceSaAllocateRule_InviteInsuranceSaAllocateRuleMapperReturnsNoItems() {
        // Setup
        final InvitationRuleVcdcParamsVo vo = new InvitationRuleVcdcParamsVo();
        vo.setLargeAreaId("largeAreaId");
        vo.setAreaId("areaId");
        vo.setAreaManageId("areaId");
        vo.setDealerCode("dealerCode");
        vo.setDealerName("dealerName");

        final InviteInsuranceSaAllocateRuleDTO inviteInsuranceSaAllocateRuleDTO = new InviteInsuranceSaAllocateRuleDTO();
        inviteInsuranceSaAllocateRuleDTO.setAppId("appId");
        inviteInsuranceSaAllocateRuleDTO.setOwnerCode("ownerCode");
        inviteInsuranceSaAllocateRuleDTO.setOwnerParCode("ownerParCode");
        inviteInsuranceSaAllocateRuleDTO.setOrgId(0);
        inviteInsuranceSaAllocateRuleDTO.setId(0L);
        final List<InviteInsuranceSaAllocateRuleDTO> expectedResult = Arrays.asList(inviteInsuranceSaAllocateRuleDTO);
        when(mockBusinessPlatformService.getDealercodes("areaId", "largeAreaId", "dealerCode",
                "dealerName")).thenReturn(Arrays.asList("value"));
        when(mockInviteInsuranceSaAllocateRuleMapper.getInviteInsuranceSaAllocateRule(
                Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<InviteInsuranceSaAllocateRuleDTO> result = inviteInsuranceSaAllocateRuleServiceImplUnderTest.getInviteInsuranceSaAllocateRule(
                vo);

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }
}
