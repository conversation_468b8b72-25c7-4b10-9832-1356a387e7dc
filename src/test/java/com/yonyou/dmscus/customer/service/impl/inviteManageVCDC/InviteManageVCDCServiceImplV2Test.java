package com.yonyou.dmscus.customer.service.impl.inviteManageVCDC;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.cloud.common.beans.RestResultResponse;
import com.yonyou.dmscus.customer.configuration.InnerUrlProperties;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDTO;
import com.yonyou.dmscus.customer.feign.DmscloudServiceClient;
import com.yonyou.dmscus.customer.feign.DownloadClient;
import com.yonyou.dmscus.customer.feign.dto.CommonConfigDTO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import com.yonyou.dmscus.customer.vo.DownloadCacheVO;

@ExtendWith(MockitoExtension.class)
class InviteManageVCDCServiceImplV2Test {
	
    @Mock
	private DownloadClient downloadClient;

    @Mock
    private InnerUrlProperties innerUrlProperties;
    
    @Mock
    private InviteVehicleRecordMapper inviteVehicleRecordMapper;
    
    @Mock
    private BusinessPlatformService businessPlatformService;
    
    @Mock
    private DmscloudServiceClient dmscloudServiceClient;

    @InjectMocks
    private InviteManageVCDCServiceImpl inviteManageVCDCServiceImplTest;

    @Test
    void testExportExcelByDown() {
    	when(innerUrlProperties.getDownloadInviteManageVCDCUrl()).thenReturn("1111");
    	RestResultResponse<DownloadCacheVO> result = new RestResultResponse<>();
    	result.setResultCode(200);
        when(downloadClient.downloadConGoodwillStamps(any())).thenReturn(result);
        inviteManageVCDCServiceImplTest.exportExcelByDown(new InviteVehicleRecordDTO());        
    }

    @Test
    void testDownLoadExportExcelForNull() {
    	Page<Map<String, Object>> page = new Page<>(1, 50);
    	List<Map> list = new ArrayList<>();
    	Map<String,Object> map = new HashMap<>();
    	map.put("111", "222");
    	list.add(map);
        when(inviteVehicleRecordMapper.exportExcelinviteVehicleDownlod(any(), any(), any())).thenReturn(list);
        List<Map> partInvoicingSummaryDetailsQuery = inviteManageVCDCServiceImplTest.downLoadExportExcel(null, 1, 10);
        assertTrue(!partInvoicingSummaryDetailsQuery.isEmpty());
    }
}
