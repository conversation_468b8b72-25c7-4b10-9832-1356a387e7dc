package com.yonyou.dmscus.customer.service.impl.goodwill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.goodwill.GoodwillApplyAuditDetailMapper;
import com.yonyou.dmscus.customer.entity.dto.goodwill.GoodwillApplyAuditDetailDTO;
import com.yonyou.dmscus.customer.entity.po.goodwill.GoodwillApplyAuditDetailPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GoodwillApplyAuditDetailServiceImplTest {

    @Mock
    private GoodwillApplyAuditDetailMapper mockGoodwillApplyAuditDetailMapper;

    private GoodwillApplyAuditDetailServiceImpl goodwillApplyAuditDetailServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        goodwillApplyAuditDetailServiceImplUnderTest = new GoodwillApplyAuditDetailServiceImpl();
        goodwillApplyAuditDetailServiceImplUnderTest.goodwillApplyAuditDetailMapper = mockGoodwillApplyAuditDetailMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO = new GoodwillApplyAuditDetailDTO();
        goodwillApplyAuditDetailDTO.setAppId("appId");
        goodwillApplyAuditDetailDTO.setOwnerCode("ownerCode");
        goodwillApplyAuditDetailDTO.setOwnerParCode("ownerParCode");
        goodwillApplyAuditDetailDTO.setOrgId(0);
        goodwillApplyAuditDetailDTO.setId(0L);

        // Configure GoodwillApplyAuditDetailMapper.selectPageBySql(...).
        final GoodwillApplyAuditDetailPO goodwillApplyAuditDetailPO = new GoodwillApplyAuditDetailPO();
        goodwillApplyAuditDetailPO.setAppId("appId");
        goodwillApplyAuditDetailPO.setOwnerCode("ownerCode");
        goodwillApplyAuditDetailPO.setOwnerParCode("ownerParCode");
        goodwillApplyAuditDetailPO.setOrgId(0);
        goodwillApplyAuditDetailPO.setId(0L);
        final List<GoodwillApplyAuditDetailPO> goodwillApplyAuditDetailPOS = Arrays.asList(goodwillApplyAuditDetailPO);
        when(mockGoodwillApplyAuditDetailMapper.selectPageBySql(any(Page.class),
                any(GoodwillApplyAuditDetailPO.class))).thenReturn(goodwillApplyAuditDetailPOS);

        // Run the test
        final IPage<GoodwillApplyAuditDetailDTO> result = goodwillApplyAuditDetailServiceImplUnderTest.selectPageBysql(
                page, goodwillApplyAuditDetailDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_GoodwillApplyAuditDetailMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO = new GoodwillApplyAuditDetailDTO();
        goodwillApplyAuditDetailDTO.setAppId("appId");
        goodwillApplyAuditDetailDTO.setOwnerCode("ownerCode");
        goodwillApplyAuditDetailDTO.setOwnerParCode("ownerParCode");
        goodwillApplyAuditDetailDTO.setOrgId(0);
        goodwillApplyAuditDetailDTO.setId(0L);

        when(mockGoodwillApplyAuditDetailMapper.selectPageBySql(any(Page.class),
                any(GoodwillApplyAuditDetailPO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<GoodwillApplyAuditDetailDTO> result = goodwillApplyAuditDetailServiceImplUnderTest.selectPageBysql(
                page, goodwillApplyAuditDetailDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO = new GoodwillApplyAuditDetailDTO();
        goodwillApplyAuditDetailDTO.setAppId("appId");
        goodwillApplyAuditDetailDTO.setOwnerCode("ownerCode");
        goodwillApplyAuditDetailDTO.setOwnerParCode("ownerParCode");
        goodwillApplyAuditDetailDTO.setOrgId(0);
        goodwillApplyAuditDetailDTO.setId(0L);

        // Configure GoodwillApplyAuditDetailMapper.selectListBySql(...).
        final GoodwillApplyAuditDetailPO goodwillApplyAuditDetailPO = new GoodwillApplyAuditDetailPO();
        goodwillApplyAuditDetailPO.setAppId("appId");
        goodwillApplyAuditDetailPO.setOwnerCode("ownerCode");
        goodwillApplyAuditDetailPO.setOwnerParCode("ownerParCode");
        goodwillApplyAuditDetailPO.setOrgId(0);
        goodwillApplyAuditDetailPO.setId(0L);
        final List<GoodwillApplyAuditDetailPO> goodwillApplyAuditDetailPOS = Arrays.asList(goodwillApplyAuditDetailPO);
        when(mockGoodwillApplyAuditDetailMapper.selectListBySql(any(GoodwillApplyAuditDetailPO.class)))
                .thenReturn(goodwillApplyAuditDetailPOS);

        // Run the test
        final List<GoodwillApplyAuditDetailDTO> result = goodwillApplyAuditDetailServiceImplUnderTest.selectListBySql(
                goodwillApplyAuditDetailDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_GoodwillApplyAuditDetailMapperReturnsNoItems() {
        // Setup
        final GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO = new GoodwillApplyAuditDetailDTO();
        goodwillApplyAuditDetailDTO.setAppId("appId");
        goodwillApplyAuditDetailDTO.setOwnerCode("ownerCode");
        goodwillApplyAuditDetailDTO.setOwnerParCode("ownerParCode");
        goodwillApplyAuditDetailDTO.setOrgId(0);
        goodwillApplyAuditDetailDTO.setId(0L);

        when(mockGoodwillApplyAuditDetailMapper.selectListBySql(any(GoodwillApplyAuditDetailPO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodwillApplyAuditDetailDTO> result = goodwillApplyAuditDetailServiceImplUnderTest.selectListBySql(
                goodwillApplyAuditDetailDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure GoodwillApplyAuditDetailMapper.selectById(...).
        final GoodwillApplyAuditDetailPO goodwillApplyAuditDetailPO = new GoodwillApplyAuditDetailPO();
        goodwillApplyAuditDetailPO.setAppId("appId");
        goodwillApplyAuditDetailPO.setOwnerCode("ownerCode");
        goodwillApplyAuditDetailPO.setOwnerParCode("ownerParCode");
        goodwillApplyAuditDetailPO.setOrgId(0);
        goodwillApplyAuditDetailPO.setId(0L);
        when(mockGoodwillApplyAuditDetailMapper.selectById(0L)).thenReturn(goodwillApplyAuditDetailPO);

        // Run the test
        final GoodwillApplyAuditDetailDTO result = goodwillApplyAuditDetailServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_GoodwillApplyAuditDetailMapperReturnsNull() {
        // Setup
        when(mockGoodwillApplyAuditDetailMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> goodwillApplyAuditDetailServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO = new GoodwillApplyAuditDetailDTO();
        goodwillApplyAuditDetailDTO.setAppId("appId");
        goodwillApplyAuditDetailDTO.setOwnerCode("ownerCode");
        goodwillApplyAuditDetailDTO.setOwnerParCode("ownerParCode");
        goodwillApplyAuditDetailDTO.setOrgId(0);
        goodwillApplyAuditDetailDTO.setId(0L);

        when(mockGoodwillApplyAuditDetailMapper.insert(any(GoodwillApplyAuditDetailPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillApplyAuditDetailServiceImplUnderTest.insert(goodwillApplyAuditDetailDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final GoodwillApplyAuditDetailDTO goodwillApplyAuditDetailDTO = new GoodwillApplyAuditDetailDTO();
        goodwillApplyAuditDetailDTO.setAppId("appId");
        goodwillApplyAuditDetailDTO.setOwnerCode("ownerCode");
        goodwillApplyAuditDetailDTO.setOwnerParCode("ownerParCode");
        goodwillApplyAuditDetailDTO.setOrgId(0);
        goodwillApplyAuditDetailDTO.setId(0L);

        // Configure GoodwillApplyAuditDetailMapper.selectById(...).
        final GoodwillApplyAuditDetailPO goodwillApplyAuditDetailPO = new GoodwillApplyAuditDetailPO();
        goodwillApplyAuditDetailPO.setAppId("appId");
        goodwillApplyAuditDetailPO.setOwnerCode("ownerCode");
        goodwillApplyAuditDetailPO.setOwnerParCode("ownerParCode");
        goodwillApplyAuditDetailPO.setOrgId(0);
        goodwillApplyAuditDetailPO.setId(0L);
        when(mockGoodwillApplyAuditDetailMapper.selectById(0L)).thenReturn(goodwillApplyAuditDetailPO);

        when(mockGoodwillApplyAuditDetailMapper.updateById(any(GoodwillApplyAuditDetailPO.class))).thenReturn(0);

        // Run the test
        final int result = goodwillApplyAuditDetailServiceImplUnderTest.update(0L, goodwillApplyAuditDetailDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
