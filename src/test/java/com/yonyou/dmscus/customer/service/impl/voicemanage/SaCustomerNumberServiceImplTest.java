package com.yonyou.dmscus.customer.service.impl.voicemanage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscloud.function.exception.ServiceBizException;
import com.yonyou.dmscus.customer.dao.voicemanage.SaCustomerNumberMapper;
import com.yonyou.dmscus.customer.dao.voicemanage.SaWorkNumberMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceCustomerInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.SaCustomerNumberDTO;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceCustomerInfoPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaCustomerNumberPO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaWorkNumberPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SaCustomerNumberServiceImplTest {

    @Mock
    private SaCustomerNumberMapper mockSaCustomerNumberMapper;
    @Mock
    private SaWorkNumberMapper mockSaWorkNumberMapper;
    @Mock
    private WorkNumberServiceContext mockWorkNumberServiceContext;

    private SaCustomerNumberServiceImpl saCustomerNumberServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        saCustomerNumberServiceImplUnderTest = new SaCustomerNumberServiceImpl();
        saCustomerNumberServiceImplUnderTest.saCustomerNumberMapper = mockSaCustomerNumberMapper;
        saCustomerNumberServiceImplUnderTest.saWorkNumberMapper = mockSaWorkNumberMapper;
        saCustomerNumberServiceImplUnderTest.workNumberServiceContext = mockWorkNumberServiceContext;
        saCustomerNumberServiceImplUnderTest.URL_MAPPING_REGISTER = "URL_MAPPING_REGISTER";
    }

    @Test
    void testGetById_SaCustomerNumberMapperReturnsNull() {
        // Setup
        when(mockSaCustomerNumberMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> saCustomerNumberServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testGetSaCustomerNumber_SaCustomerNumberMapperReturnsNull() {
        // Setup
        when(mockSaCustomerNumberMapper.getSaCustomerNumber(0L)).thenReturn(null);

        // Run the test
        final SaCustomerNumberDTO result = saCustomerNumberServiceImplUnderTest.getSaCustomerNumber(0L);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    void testDeleteInsuranceCustomerInfo() {
        // Setup
        // Run the test
        saCustomerNumberServiceImplUnderTest.deleteInsuranceCustomerInfo(0L);

        // Verify the results
        verify(mockSaCustomerNumberMapper).deleteInsuranceCustomerInfo(0L);
    }
}
