package com.yonyou.dmscus.customer.service.complaint.sale.impl.complaint.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.sale.SaleComplaintCustomFieldUseMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintCustomFieldTestDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.sale.SaleComplaintCustomFieldUseDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.sale.SaleComplaintCustomFieldUsePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SaleComplaintCustomFieldUseServiceImplTest {

    @Mock
    private SaleComplaintCustomFieldUseMapper mockSaleComplaintCustomFieldUseMapper;

    private SaleComplaintCustomFieldUseServiceImpl saleComplaintCustomFieldUseServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        saleComplaintCustomFieldUseServiceImplUnderTest = new SaleComplaintCustomFieldUseServiceImpl();
        saleComplaintCustomFieldUseServiceImplUnderTest.saleComplaintCustomFieldUseMapper = mockSaleComplaintCustomFieldUseMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO = new SaleComplaintCustomFieldUseDTO();
        saleComplaintCustomFieldUseDTO.setTableName("tableName");
        saleComplaintCustomFieldUseDTO.setFieldName("fieldName");
        saleComplaintCustomFieldUseDTO.setFieldDescribe("fieldDescribe");
        saleComplaintCustomFieldUseDTO.setIsQuery(false);
        saleComplaintCustomFieldUseDTO.setIsSort(0);
        saleComplaintCustomFieldUseDTO.setSortType("sortType");
        saleComplaintCustomFieldUseDTO.setUserId(0L);

        // Configure SaleComplaintCustomFieldUseMapper.selectPageBySql(...).
        final SaleComplaintCustomFieldUsePO saleComplaintCustomFieldUsePO = new SaleComplaintCustomFieldUsePO();
        saleComplaintCustomFieldUsePO.setAppId("appId");
        saleComplaintCustomFieldUsePO.setOwnerCode("ownerCode");
        saleComplaintCustomFieldUsePO.setOwnerParCode("ownerParCode");
        saleComplaintCustomFieldUsePO.setOrgId(0);
        saleComplaintCustomFieldUsePO.setId(0L);
        final List<SaleComplaintCustomFieldUsePO> saleComplaintCustomFieldUsePOS = Arrays.asList(
                saleComplaintCustomFieldUsePO);
        when(mockSaleComplaintCustomFieldUseMapper.selectPageBySql(any(Page.class),
                any(SaleComplaintCustomFieldUsePO.class))).thenReturn(saleComplaintCustomFieldUsePOS);

        // Run the test
        final IPage<SaleComplaintCustomFieldUseDTO> result = saleComplaintCustomFieldUseServiceImplUnderTest.selectPageBysql(
                page, saleComplaintCustomFieldUseDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_SaleComplaintCustomFieldUseMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO = new SaleComplaintCustomFieldUseDTO();
        saleComplaintCustomFieldUseDTO.setTableName("tableName");
        saleComplaintCustomFieldUseDTO.setFieldName("fieldName");
        saleComplaintCustomFieldUseDTO.setFieldDescribe("fieldDescribe");
        saleComplaintCustomFieldUseDTO.setIsQuery(false);
        saleComplaintCustomFieldUseDTO.setIsSort(0);
        saleComplaintCustomFieldUseDTO.setSortType("sortType");
        saleComplaintCustomFieldUseDTO.setUserId(0L);

        when(mockSaleComplaintCustomFieldUseMapper.selectPageBySql(any(Page.class),
                any(SaleComplaintCustomFieldUsePO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<SaleComplaintCustomFieldUseDTO> result = saleComplaintCustomFieldUseServiceImplUnderTest.selectPageBysql(
                page, saleComplaintCustomFieldUseDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO = new SaleComplaintCustomFieldUseDTO();
        saleComplaintCustomFieldUseDTO.setTableName("tableName");
        saleComplaintCustomFieldUseDTO.setFieldName("fieldName");
        saleComplaintCustomFieldUseDTO.setFieldDescribe("fieldDescribe");
        saleComplaintCustomFieldUseDTO.setIsQuery(false);
        saleComplaintCustomFieldUseDTO.setIsSort(0);
        saleComplaintCustomFieldUseDTO.setSortType("sortType");
        saleComplaintCustomFieldUseDTO.setUserId(0L);

        // Configure SaleComplaintCustomFieldUseMapper.selectListBySql(...).
        final SaleComplaintCustomFieldUsePO saleComplaintCustomFieldUsePO = new SaleComplaintCustomFieldUsePO();
        saleComplaintCustomFieldUsePO.setAppId("appId");
        saleComplaintCustomFieldUsePO.setOwnerCode("ownerCode");
        saleComplaintCustomFieldUsePO.setOwnerParCode("ownerParCode");
        saleComplaintCustomFieldUsePO.setOrgId(0);
        saleComplaintCustomFieldUsePO.setId(0L);
        final List<SaleComplaintCustomFieldUsePO> saleComplaintCustomFieldUsePOS = Arrays.asList(
                saleComplaintCustomFieldUsePO);
        when(mockSaleComplaintCustomFieldUseMapper.selectListBySql(
                any(SaleComplaintCustomFieldUsePO.class))).thenReturn(saleComplaintCustomFieldUsePOS);

        // Run the test
        final List<SaleComplaintCustomFieldUseDTO> result = saleComplaintCustomFieldUseServiceImplUnderTest.selectListBySql(
                saleComplaintCustomFieldUseDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_SaleComplaintCustomFieldUseMapperReturnsNoItems() {
        // Setup
        final SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO = new SaleComplaintCustomFieldUseDTO();
        saleComplaintCustomFieldUseDTO.setTableName("tableName");
        saleComplaintCustomFieldUseDTO.setFieldName("fieldName");
        saleComplaintCustomFieldUseDTO.setFieldDescribe("fieldDescribe");
        saleComplaintCustomFieldUseDTO.setIsQuery(false);
        saleComplaintCustomFieldUseDTO.setIsSort(0);
        saleComplaintCustomFieldUseDTO.setSortType("sortType");
        saleComplaintCustomFieldUseDTO.setUserId(0L);

        when(mockSaleComplaintCustomFieldUseMapper.selectListBySql(
                any(SaleComplaintCustomFieldUsePO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<SaleComplaintCustomFieldUseDTO> result = saleComplaintCustomFieldUseServiceImplUnderTest.selectListBySql(
                saleComplaintCustomFieldUseDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure SaleComplaintCustomFieldUseMapper.selectById(...).
        final SaleComplaintCustomFieldUsePO saleComplaintCustomFieldUsePO = new SaleComplaintCustomFieldUsePO();
        saleComplaintCustomFieldUsePO.setAppId("appId");
        saleComplaintCustomFieldUsePO.setOwnerCode("ownerCode");
        saleComplaintCustomFieldUsePO.setOwnerParCode("ownerParCode");
        saleComplaintCustomFieldUsePO.setOrgId(0);
        saleComplaintCustomFieldUsePO.setId(0L);
        when(mockSaleComplaintCustomFieldUseMapper.selectById(0L)).thenReturn(saleComplaintCustomFieldUsePO);

        // Run the test
        final SaleComplaintCustomFieldUseDTO result = saleComplaintCustomFieldUseServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_SaleComplaintCustomFieldUseMapperReturnsNull() {
        // Setup
        when(mockSaleComplaintCustomFieldUseMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> saleComplaintCustomFieldUseServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO = new SaleComplaintCustomFieldUseDTO();
        saleComplaintCustomFieldUseDTO.setTableName("tableName");
        saleComplaintCustomFieldUseDTO.setFieldName("fieldName");
        saleComplaintCustomFieldUseDTO.setFieldDescribe("fieldDescribe");
        saleComplaintCustomFieldUseDTO.setIsQuery(false);
        saleComplaintCustomFieldUseDTO.setIsSort(0);
        saleComplaintCustomFieldUseDTO.setSortType("sortType");
        saleComplaintCustomFieldUseDTO.setUserId(0L);

        when(mockSaleComplaintCustomFieldUseMapper.insert(any(SaleComplaintCustomFieldUsePO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintCustomFieldUseServiceImplUnderTest.insert(saleComplaintCustomFieldUseDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final SaleComplaintCustomFieldUseDTO saleComplaintCustomFieldUseDTO = new SaleComplaintCustomFieldUseDTO();
        saleComplaintCustomFieldUseDTO.setTableName("tableName");
        saleComplaintCustomFieldUseDTO.setFieldName("fieldName");
        saleComplaintCustomFieldUseDTO.setFieldDescribe("fieldDescribe");
        saleComplaintCustomFieldUseDTO.setIsQuery(false);
        saleComplaintCustomFieldUseDTO.setIsSort(0);
        saleComplaintCustomFieldUseDTO.setSortType("sortType");
        saleComplaintCustomFieldUseDTO.setUserId(0L);

        // Configure SaleComplaintCustomFieldUseMapper.selectById(...).
        final SaleComplaintCustomFieldUsePO saleComplaintCustomFieldUsePO = new SaleComplaintCustomFieldUsePO();
        saleComplaintCustomFieldUsePO.setAppId("appId");
        saleComplaintCustomFieldUsePO.setOwnerCode("ownerCode");
        saleComplaintCustomFieldUsePO.setOwnerParCode("ownerParCode");
        saleComplaintCustomFieldUsePO.setOrgId(0);
        saleComplaintCustomFieldUsePO.setId(0L);
        when(mockSaleComplaintCustomFieldUseMapper.selectById(0L)).thenReturn(saleComplaintCustomFieldUsePO);

        when(mockSaleComplaintCustomFieldUseMapper.updateById(any(SaleComplaintCustomFieldUsePO.class))).thenReturn(0);

        // Run the test
        final int result = saleComplaintCustomFieldUseServiceImplUnderTest.update(0L, saleComplaintCustomFieldUseDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

}
