package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yonyou.dmscus.customer.dao.voc.VocFunctionalStatusLogMapper;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusLogPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class VocFunctionalStatusLogServiceImplTest {

    @Mock
    private VocFunctionalStatusLogMapper mockVocFunctionalStatusLogMapper;

    private VocFunctionalStatusLogServiceImpl vocFunctionalStatusLogServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        vocFunctionalStatusLogServiceImplUnderTest = new VocFunctionalStatusLogServiceImpl();
        vocFunctionalStatusLogServiceImplUnderTest.vocFunctionalStatusLogMapper = mockVocFunctionalStatusLogMapper;
    }

    @Test
    void testInsertList() {
        // Setup
        final VocFunctionalStatusLogPO vocFunctionalStatusLogPO = new VocFunctionalStatusLogPO();
        vocFunctionalStatusLogPO.setId(0L);
        vocFunctionalStatusLogPO.setIsDeleted(0);
        vocFunctionalStatusLogPO.setDt("dt");
        vocFunctionalStatusLogPO.setVin("vin");
        vocFunctionalStatusLogPO.setActivatedState("activatedState");
        final List<VocFunctionalStatusLogPO> logPOSList = Arrays.asList(vocFunctionalStatusLogPO);

        // Run the test
        final int result = vocFunctionalStatusLogServiceImplUnderTest.insertList(logPOSList);

        // Verify the results
//        assertThat(result).isEqualTo(0);

        // Confirm VocFunctionalStatusLogMapper.insertList(...).
        final VocFunctionalStatusLogPO vocFunctionalStatusLogPO1 = new VocFunctionalStatusLogPO();
        vocFunctionalStatusLogPO1.setId(0L);
        vocFunctionalStatusLogPO1.setIsDeleted(0);
        vocFunctionalStatusLogPO1.setDt("dt");
        vocFunctionalStatusLogPO1.setVin("vin");
        vocFunctionalStatusLogPO1.setActivatedState("activatedState");
        final List<VocFunctionalStatusLogPO> list = Arrays.asList(vocFunctionalStatusLogPO1);
        verify(mockVocFunctionalStatusLogMapper).insertList(list);
    }

    @Test
    void testSelectListBydt() {
        // Setup
        final VocFunctionalStatusLogPO vocFunctionalStatusLogPO = new VocFunctionalStatusLogPO();
        vocFunctionalStatusLogPO.setId(0L);
        vocFunctionalStatusLogPO.setIsDeleted(0);
        vocFunctionalStatusLogPO.setDt("dt");
        vocFunctionalStatusLogPO.setVin("vin");
        vocFunctionalStatusLogPO.setActivatedState("activatedState");
        final List<VocFunctionalStatusLogPO> expectedResult = Arrays.asList(vocFunctionalStatusLogPO);
        when(mockVocFunctionalStatusLogMapper.selectList(any(Wrapper.class))).thenReturn(Arrays.asList());

        // Run the test
        final List<VocFunctionalStatusLogPO> result = vocFunctionalStatusLogServiceImplUnderTest.selectListBydt("data",
                0, 0);

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectListBydt_VocFunctionalStatusLogMapperReturnsNoItems() {
        // Setup
        when(mockVocFunctionalStatusLogMapper.selectList(any(Wrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<VocFunctionalStatusLogPO> result = vocFunctionalStatusLogServiceImplUnderTest.selectListBydt("data",
                0, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectVocFunctionalStatusLogByVin() {
        // Setup
        // Configure VocFunctionalStatusLogMapper.selectList(...).
        final VocFunctionalStatusLogPO vocFunctionalStatusLogPO = new VocFunctionalStatusLogPO();
        vocFunctionalStatusLogPO.setId(0L);
        vocFunctionalStatusLogPO.setIsDeleted(0);
        vocFunctionalStatusLogPO.setDt("dt");
        vocFunctionalStatusLogPO.setVin("vin");
        vocFunctionalStatusLogPO.setActivatedState("activatedState");
        final List<VocFunctionalStatusLogPO> vocFunctionalStatusLogPOS = Arrays.asList(vocFunctionalStatusLogPO);
        when(mockVocFunctionalStatusLogMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(vocFunctionalStatusLogPOS);

        // Run the test
        final int result = vocFunctionalStatusLogServiceImplUnderTest.selectVocFunctionalStatusLogByVin("vin");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectVocFunctionalStatusLogByVin_VocFunctionalStatusLogMapperReturnsNoItems() {
        // Setup
        when(mockVocFunctionalStatusLogMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final int result = vocFunctionalStatusLogServiceImplUnderTest.selectVocFunctionalStatusLogByVin("vin");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
