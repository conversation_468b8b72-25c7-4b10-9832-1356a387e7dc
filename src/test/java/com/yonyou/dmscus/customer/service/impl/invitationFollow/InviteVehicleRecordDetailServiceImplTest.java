package com.yonyou.dmscus.customer.service.impl.invitationFollow;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordDetailMapper;
import com.yonyou.dmscus.customer.entity.dto.invitationFollow.InviteVehicleRecordDetailDTO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordDetailPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteVehicleRecordDetailServiceImplTest {

    @Mock
    private InviteVehicleRecordDetailMapper mockInviteVehicleRecordDetailMapper;

    private InviteVehicleRecordDetailServiceImpl inviteVehicleRecordDetailServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteVehicleRecordDetailServiceImplUnderTest = new InviteVehicleRecordDetailServiceImpl();
        inviteVehicleRecordDetailServiceImplUnderTest.inviteVehicleRecordDetailMapper = mockInviteVehicleRecordDetailMapper;
    }

    @Test
    void testGetById_InviteVehicleRecordDetailMapperReturnsNull() {
        // Setup
        when(mockInviteVehicleRecordDetailMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inviteVehicleRecordDetailServiceImplUnderTest.getById(0L))
                .isInstanceOf(DALException.class);
    }

    @Test
    void testUpdate() {
        // Setup
        final InviteVehicleRecordDetailDTO inviteVehicleRecordDetailDTO = new InviteVehicleRecordDetailDTO();
        inviteVehicleRecordDetailDTO.setAppId("appId");
        inviteVehicleRecordDetailDTO.setOwnerCode("ownerCode");
        inviteVehicleRecordDetailDTO.setInviteId(0L);
        inviteVehicleRecordDetailDTO.setSaId("saId");
        inviteVehicleRecordDetailDTO.setSaName("saName");

        // Configure InviteVehicleRecordDetailMapper.selectById(...).
        final InviteVehicleRecordDetailPO inviteVehicleRecordDetailPO = new InviteVehicleRecordDetailPO();
        inviteVehicleRecordDetailPO.setOwnerCode("ownerCode");
        inviteVehicleRecordDetailPO.setId(0L);
        inviteVehicleRecordDetailPO.setInviteId(0L);
        inviteVehicleRecordDetailPO.setSaId("saId");
        inviteVehicleRecordDetailPO.setSaName("saName");
        when(mockInviteVehicleRecordDetailMapper.selectById(0L)).thenReturn(inviteVehicleRecordDetailPO);

        // Configure InviteVehicleRecordDetailMapper.updateById(...).
        final InviteVehicleRecordDetailPO entity = new InviteVehicleRecordDetailPO();
        entity.setOwnerCode("ownerCode");
        entity.setId(0L);
        entity.setInviteId(0L);
        entity.setSaId("saId");
        entity.setSaName("saName");
        when(mockInviteVehicleRecordDetailMapper.updateById(entity)).thenReturn(0);

        // Run the test
        final int result = inviteVehicleRecordDetailServiceImplUnderTest.update(0L, inviteVehicleRecordDetailDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectNumByCount() {
        // Setup
        when(mockInviteVehicleRecordDetailMapper.selectNumByCount(0L)).thenReturn(0);

        // Run the test
        final int result = inviteVehicleRecordDetailServiceImplUnderTest.selectNumByCount(0L);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
