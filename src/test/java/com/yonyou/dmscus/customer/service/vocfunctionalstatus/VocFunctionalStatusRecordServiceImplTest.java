package com.yonyou.dmscus.customer.service.vocfunctionalstatus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yonyou.dmscus.customer.dao.voc.VocFunctionalStatusRecordMapper;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusLogPO;
import com.yonyou.dmscus.customer.entity.po.vocfunctional.VocFunctionalStatusRecordPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class VocFunctionalStatusRecordServiceImplTest {

    @Mock
    private VocFunctionalStatusRecordMapper mockVocFunctionalStatusRecordMapper;

    private VocFunctionalStatusRecordServiceImpl vocFunctionalStatusRecordServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        vocFunctionalStatusRecordServiceImplUnderTest = new VocFunctionalStatusRecordServiceImpl();
        vocFunctionalStatusRecordServiceImplUnderTest.vocFunctionalStatusRecordMapper = mockVocFunctionalStatusRecordMapper;
    }

    @Test
    void testInsertList() {
        // Setup
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO.setId(0L);
        vocFunctionalStatusRecordPO.setIsDeleted(0);
        vocFunctionalStatusRecordPO.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO.setVin("vin");
        vocFunctionalStatusRecordPO.setIsExecute(0);
        vocFunctionalStatusRecordPO.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> list = Arrays.asList(vocFunctionalStatusRecordPO);

        // Run the test
        final int result = vocFunctionalStatusRecordServiceImplUnderTest.insertList(list);

        // Verify the results
//        assertThat(result).isEqualTo(0);

        // Confirm VocFunctionalStatusRecordMapper.insertList(...).
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO1 = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO1.setId(0L);
        vocFunctionalStatusRecordPO1.setIsDeleted(0);
        vocFunctionalStatusRecordPO1.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO1.setVin("vin");
        vocFunctionalStatusRecordPO1.setIsExecute(0);
        vocFunctionalStatusRecordPO1.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> list1 = Arrays.asList(vocFunctionalStatusRecordPO1);
        verify(mockVocFunctionalStatusRecordMapper).insertList(list1);
    }

    @Test
    void testUpdateList() {
        // Setup
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO.setId(0L);
        vocFunctionalStatusRecordPO.setIsDeleted(0);
        vocFunctionalStatusRecordPO.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO.setVin("vin");
        vocFunctionalStatusRecordPO.setIsExecute(0);
        vocFunctionalStatusRecordPO.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> updateList = Arrays.asList(vocFunctionalStatusRecordPO);

        // Run the test
        final int result = vocFunctionalStatusRecordServiceImplUnderTest.updateList(updateList);

        // Verify the results
//        assertThat(result).isEqualTo(0);

        // Confirm VocFunctionalStatusRecordMapper.updateList(...).
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO1 = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO1.setId(0L);
        vocFunctionalStatusRecordPO1.setIsDeleted(0);
        vocFunctionalStatusRecordPO1.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO1.setVin("vin");
        vocFunctionalStatusRecordPO1.setIsExecute(0);
        vocFunctionalStatusRecordPO1.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> list = Arrays.asList(vocFunctionalStatusRecordPO1);
        verify(mockVocFunctionalStatusRecordMapper).updateList(list);
    }

    @Test
    void testSelectListStatusRecord() {
        // Setup
        final VocFunctionalStatusLogPO vocFunctionalStatusLogPO = new VocFunctionalStatusLogPO();
        vocFunctionalStatusLogPO.setId(0L);
        vocFunctionalStatusLogPO.setIsDeleted(0);
        vocFunctionalStatusLogPO.setDt("dt");
        vocFunctionalStatusLogPO.setVin("vin");
        vocFunctionalStatusLogPO.setActivatedState("activatedState");
        final List<VocFunctionalStatusLogPO> x = Arrays.asList(vocFunctionalStatusLogPO);
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO.setId(0L);
        vocFunctionalStatusRecordPO.setIsDeleted(0);
        vocFunctionalStatusRecordPO.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO.setVin("vin");
        vocFunctionalStatusRecordPO.setIsExecute(0);
        vocFunctionalStatusRecordPO.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> expectedResult = Arrays.asList(vocFunctionalStatusRecordPO);

        // Configure VocFunctionalStatusRecordMapper.selectListStatusRecord(...).
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO1 = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO1.setId(0L);
        vocFunctionalStatusRecordPO1.setIsDeleted(0);
        vocFunctionalStatusRecordPO1.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO1.setVin("vin");
        vocFunctionalStatusRecordPO1.setIsExecute(0);
        vocFunctionalStatusRecordPO1.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> vocFunctionalStatusRecordPOS = Arrays.asList(
                vocFunctionalStatusRecordPO1);
        final VocFunctionalStatusLogPO vocFunctionalStatusLogPO1 = new VocFunctionalStatusLogPO();
        vocFunctionalStatusLogPO1.setId(0L);
        vocFunctionalStatusLogPO1.setIsDeleted(0);
        vocFunctionalStatusLogPO1.setDt("dt");
        vocFunctionalStatusLogPO1.setVin("vin");
        vocFunctionalStatusLogPO1.setActivatedState("activatedState");
        final List<VocFunctionalStatusLogPO> x1 = Arrays.asList(vocFunctionalStatusLogPO1);
        when(mockVocFunctionalStatusRecordMapper.selectListStatusRecord(x1)).thenReturn(vocFunctionalStatusRecordPOS);

        // Run the test
        final List<VocFunctionalStatusRecordPO> result = vocFunctionalStatusRecordServiceImplUnderTest.selectListStatusRecord(
                x);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectListStatusRecord_VocFunctionalStatusRecordMapperReturnsNoItems() {
        // Setup
        final VocFunctionalStatusLogPO vocFunctionalStatusLogPO = new VocFunctionalStatusLogPO();
        vocFunctionalStatusLogPO.setId(0L);
        vocFunctionalStatusLogPO.setIsDeleted(0);
        vocFunctionalStatusLogPO.setDt("dt");
        vocFunctionalStatusLogPO.setVin("vin");
        vocFunctionalStatusLogPO.setActivatedState("activatedState");
        final List<VocFunctionalStatusLogPO> x = Arrays.asList(vocFunctionalStatusLogPO);

        // Configure VocFunctionalStatusRecordMapper.selectListStatusRecord(...).
        final VocFunctionalStatusLogPO vocFunctionalStatusLogPO1 = new VocFunctionalStatusLogPO();
        vocFunctionalStatusLogPO1.setId(0L);
        vocFunctionalStatusLogPO1.setIsDeleted(0);
        vocFunctionalStatusLogPO1.setDt("dt");
        vocFunctionalStatusLogPO1.setVin("vin");
        vocFunctionalStatusLogPO1.setActivatedState("activatedState");
        final List<VocFunctionalStatusLogPO> x1 = Arrays.asList(vocFunctionalStatusLogPO1);
        when(mockVocFunctionalStatusRecordMapper.selectListStatusRecord(x1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<VocFunctionalStatusRecordPO> result = vocFunctionalStatusRecordServiceImplUnderTest.selectListStatusRecord(
                x);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectVocFunctionalStatusRecordByModfiyTime() {
        // Setup
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO.setId(0L);
        vocFunctionalStatusRecordPO.setIsDeleted(0);
        vocFunctionalStatusRecordPO.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO.setVin("vin");
        vocFunctionalStatusRecordPO.setIsExecute(0);
        vocFunctionalStatusRecordPO.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> expectedResult = Arrays.asList(vocFunctionalStatusRecordPO);

        // Configure VocFunctionalStatusRecordMapper.selectList(...).
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO1 = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO1.setId(0L);
        vocFunctionalStatusRecordPO1.setIsDeleted(0);
        vocFunctionalStatusRecordPO1.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO1.setVin("vin");
        vocFunctionalStatusRecordPO1.setIsExecute(0);
        vocFunctionalStatusRecordPO1.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> vocFunctionalStatusRecordPOS = Arrays.asList(
                vocFunctionalStatusRecordPO1);
        when(mockVocFunctionalStatusRecordMapper.selectList(any(QueryWrapper.class)))
                .thenReturn(vocFunctionalStatusRecordPOS);

        // Run the test
        final List<VocFunctionalStatusRecordPO> result = vocFunctionalStatusRecordServiceImplUnderTest.selectVocFunctionalStatusRecordByModfiyTime(
                "dateTime", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectVocFunctionalStatusRecordByModfiyTime_VocFunctionalStatusRecordMapperReturnsNoItems() {
        // Setup
        when(mockVocFunctionalStatusRecordMapper.selectList(any(QueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<VocFunctionalStatusRecordPO> result = vocFunctionalStatusRecordServiceImplUnderTest.selectVocFunctionalStatusRecordByModfiyTime(
                "dateTime", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectListByVins() {
        // Setup
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO.setId(0L);
        vocFunctionalStatusRecordPO.setIsDeleted(0);
        vocFunctionalStatusRecordPO.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO.setVin("vin");
        vocFunctionalStatusRecordPO.setIsExecute(0);
        vocFunctionalStatusRecordPO.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> expectedResult = Arrays.asList(vocFunctionalStatusRecordPO);

        // Configure VocFunctionalStatusRecordMapper.selectListByVins(...).
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO1 = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO1.setId(0L);
        vocFunctionalStatusRecordPO1.setIsDeleted(0);
        vocFunctionalStatusRecordPO1.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO1.setVin("vin");
        vocFunctionalStatusRecordPO1.setIsExecute(0);
        vocFunctionalStatusRecordPO1.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> vocFunctionalStatusRecordPOS = Arrays.asList(
                vocFunctionalStatusRecordPO1);
        when(mockVocFunctionalStatusRecordMapper.selectListByVins(Arrays.asList("value")))
                .thenReturn(vocFunctionalStatusRecordPOS);

        // Run the test
        final List<VocFunctionalStatusRecordPO> result = vocFunctionalStatusRecordServiceImplUnderTest.selectListByVins(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectListByVins_VocFunctionalStatusRecordMapperReturnsNoItems() {
        // Setup
        when(mockVocFunctionalStatusRecordMapper.selectListByVins(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<VocFunctionalStatusRecordPO> result = vocFunctionalStatusRecordServiceImplUnderTest.selectListByVins(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectByVin() {
        // Setup
        when(mockVocFunctionalStatusRecordMapper.selectCount(any(QueryWrapper.class))).thenReturn(0);

        // Run the test
        final int result = vocFunctionalStatusRecordServiceImplUnderTest.selectByVin("vin");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectIsStatusByVin() {
        // Setup
        // Configure VocFunctionalStatusRecordMapper.selectOne(...).
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO.setId(0L);
        vocFunctionalStatusRecordPO.setIsDeleted(0);
        vocFunctionalStatusRecordPO.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO.setVin("vin");
        vocFunctionalStatusRecordPO.setIsExecute(0);
        vocFunctionalStatusRecordPO.setActivatedState(0);
        when(mockVocFunctionalStatusRecordMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(vocFunctionalStatusRecordPO);

        // Run the test
        final int result = vocFunctionalStatusRecordServiceImplUnderTest.selectIsStatusByVin("vin");

        // Verify the results
//        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectVocFunctionalStatusRecordByVin() {
        // Setup
        when(mockVocFunctionalStatusRecordMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final int result = vocFunctionalStatusRecordServiceImplUnderTest.selectVocFunctionalStatusRecordByVin("vin");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSelectVocFunctionalStatusRecordPOByModfiyTime() {
        // Setup
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO.setId(0L);
        vocFunctionalStatusRecordPO.setIsDeleted(0);
        vocFunctionalStatusRecordPO.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO.setVin("vin");
        vocFunctionalStatusRecordPO.setIsExecute(0);
        vocFunctionalStatusRecordPO.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> expectedResult = Arrays.asList(vocFunctionalStatusRecordPO);

        // Configure VocFunctionalStatusRecordMapper.selectList(...).
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO1 = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO1.setId(0L);
        vocFunctionalStatusRecordPO1.setIsDeleted(0);
        vocFunctionalStatusRecordPO1.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO1.setVin("vin");
        vocFunctionalStatusRecordPO1.setIsExecute(0);
        vocFunctionalStatusRecordPO1.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> vocFunctionalStatusRecordPOS = Arrays.asList(
                vocFunctionalStatusRecordPO1);
        when(mockVocFunctionalStatusRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(vocFunctionalStatusRecordPOS);

        // Run the test
        final List<VocFunctionalStatusRecordPO> result = vocFunctionalStatusRecordServiceImplUnderTest.selectVocFunctionalStatusRecordPOByModfiyTime(
                "dateTime", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectVocFunctionalStatusRecordPOByModfiyTime_VocFunctionalStatusRecordMapperReturnsNoItems() {
        // Setup
        when(mockVocFunctionalStatusRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<VocFunctionalStatusRecordPO> result = vocFunctionalStatusRecordServiceImplUnderTest.selectVocFunctionalStatusRecordPOByModfiyTime(
                "dateTime", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testUpdateFunctionalIsExecute() {
        // Setup
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO.setId(0L);
        vocFunctionalStatusRecordPO.setIsDeleted(0);
        vocFunctionalStatusRecordPO.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO.setVin("vin");
        vocFunctionalStatusRecordPO.setIsExecute(0);
        vocFunctionalStatusRecordPO.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> list = Arrays.asList(vocFunctionalStatusRecordPO);

        // Configure VocFunctionalStatusRecordMapper.updateFunctionalIsExecute(...).
        final VocFunctionalStatusRecordPO vocFunctionalStatusRecordPO1 = new VocFunctionalStatusRecordPO();
        vocFunctionalStatusRecordPO1.setId(0L);
        vocFunctionalStatusRecordPO1.setIsDeleted(0);
        vocFunctionalStatusRecordPO1.setModifyTime("modifyTime");
        vocFunctionalStatusRecordPO1.setVin("vin");
        vocFunctionalStatusRecordPO1.setIsExecute(0);
        vocFunctionalStatusRecordPO1.setActivatedState(0);
        final List<VocFunctionalStatusRecordPO> list1 = Arrays.asList(vocFunctionalStatusRecordPO1);
        when(mockVocFunctionalStatusRecordMapper.updateFunctionalIsExecute(list1)).thenReturn(0);

        // Run the test
        final int result = vocFunctionalStatusRecordServiceImplUnderTest.updateFunctionalIsExecute(list);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
