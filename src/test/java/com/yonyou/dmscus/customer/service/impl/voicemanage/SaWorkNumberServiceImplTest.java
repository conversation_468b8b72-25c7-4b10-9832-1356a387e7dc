package com.yonyou.dmscus.customer.service.impl.voicemanage;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.voicemanage.SaWorkNumberMapper;
import com.yonyou.dmscus.customer.entity.dto.voicemanage.SaWorkNumberDTO;
import com.yonyou.dmscus.customer.entity.po.voicemanage.SaWorkNumberPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SaWorkNumberServiceImplTest {

    @Mock
    private SaWorkNumberMapper mockSaWorkNumberMapper;

    private SaWorkNumberServiceImpl saWorkNumberServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        saWorkNumberServiceImplUnderTest = new SaWorkNumberServiceImpl();
        saWorkNumberServiceImplUnderTest.saWorkNumberMapper = mockSaWorkNumberMapper;
        saWorkNumberServiceImplUnderTest.URL_MAPPING_WORKBIND = "URL_MAPPING_WORKBIND";
        saWorkNumberServiceImplUnderTest.URL_MAPPING_UNWORKBIND = "URL_MAPPING_UNWORKBIND";
    }

    @Test
    void testGetById() {
        // Setup
        final SaWorkNumberDTO expectedResult = new SaWorkNumberDTO();
        expectedResult.setId(0L);
        expectedResult.setSaId("saId");
        expectedResult.setSaName("saName");
        expectedResult.setSaNumber("saNumber");
        expectedResult.setWorkNumber("workNumber");

        // Configure SaWorkNumberMapper.selectById(...).
        final SaWorkNumberPO saWorkNumberPO = new SaWorkNumberPO();
        saWorkNumberPO.setOwnerCode("ownerCode");
        saWorkNumberPO.setSaId("saId");
        saWorkNumberPO.setSaName("saName");
        saWorkNumberPO.setWorkNumber("workNumber");
        saWorkNumberPO.setDealerCode("dealerCode");
        when(mockSaWorkNumberMapper.selectById(0L)).thenReturn(saWorkNumberPO);

        // Run the test
        final SaWorkNumberDTO result = saWorkNumberServiceImplUnderTest.getById(0L);

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetById_SaWorkNumberMapperReturnsNull() {
        // Setup
        when(mockSaWorkNumberMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> saWorkNumberServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }


}
