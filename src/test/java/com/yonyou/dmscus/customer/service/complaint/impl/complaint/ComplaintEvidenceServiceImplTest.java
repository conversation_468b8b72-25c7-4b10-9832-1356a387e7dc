package com.yonyou.dmscus.customer.service.complaint.impl.complaint;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.function.exception.DALException;
import com.yonyou.dmscus.customer.dao.complaint.ComplaintEvidenceMapper;
import com.yonyou.dmscus.customer.entity.dto.complaint.ComplaintEvidenceDTO;
import com.yonyou.dmscus.customer.entity.dto.complaint.InsertComplaitEvidenceDTO;
import com.yonyou.dmscus.customer.entity.po.complaint.ComplaintEvidencePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintEvidenceServiceImplTest {

    @Mock
    private ComplaintEvidenceMapper mockComplaintEvidenceMapper;

    private ComplaintEvidenceServiceImpl complaintEvidenceServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        complaintEvidenceServiceImplUnderTest = new ComplaintEvidenceServiceImpl();
        complaintEvidenceServiceImplUnderTest.complaintEvidenceMapper = mockComplaintEvidenceMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintEvidenceDTO complaintEvidenceDTO = new ComplaintEvidenceDTO();
        complaintEvidenceDTO.setId(0L);
        complaintEvidenceDTO.setComplaintInfoId(0L);
        complaintEvidenceDTO.setObject("object");
        complaintEvidenceDTO.setRoNo("roNo");
        complaintEvidenceDTO.setOperator("operator");

        // Configure ComplaintEvidenceMapper.selectPageBySql(...).
        final ComplaintEvidencePO complaintEvidencePO = new ComplaintEvidencePO();
        complaintEvidencePO.setOwnerCode("ownerCode");
        complaintEvidencePO.setOrgId(0);
        complaintEvidencePO.setId(0L);
        complaintEvidencePO.setComplaintInfoId(0L);
        complaintEvidencePO.setObject("object");
        final List<ComplaintEvidencePO> complaintEvidencePOS = Arrays.asList(complaintEvidencePO);
        when(mockComplaintEvidenceMapper.selectPageBySql(any(Page.class), any(ComplaintEvidencePO.class)))
                .thenReturn(complaintEvidencePOS);

        // Run the test
        final IPage<ComplaintEvidenceDTO> result = complaintEvidenceServiceImplUnderTest.selectPageBysql(page,
                complaintEvidenceDTO);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_ComplaintEvidenceMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final ComplaintEvidenceDTO complaintEvidenceDTO = new ComplaintEvidenceDTO();
        complaintEvidenceDTO.setId(0L);
        complaintEvidenceDTO.setComplaintInfoId(0L);
        complaintEvidenceDTO.setObject("object");
        complaintEvidenceDTO.setRoNo("roNo");
        complaintEvidenceDTO.setOperator("operator");

        when(mockComplaintEvidenceMapper.selectPageBySql(any(Page.class), any(ComplaintEvidencePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final IPage<ComplaintEvidenceDTO> result = complaintEvidenceServiceImplUnderTest.selectPageBysql(page,
                complaintEvidenceDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql() {
        // Setup
        final ComplaintEvidenceDTO complaintEvidenceDTO = new ComplaintEvidenceDTO();
        complaintEvidenceDTO.setId(0L);
        complaintEvidenceDTO.setComplaintInfoId(0L);
        complaintEvidenceDTO.setObject("object");
        complaintEvidenceDTO.setRoNo("roNo");
        complaintEvidenceDTO.setOperator("operator");

        // Configure ComplaintEvidenceMapper.selectListBySql(...).
        final ComplaintEvidencePO complaintEvidencePO = new ComplaintEvidencePO();
        complaintEvidencePO.setOwnerCode("ownerCode");
        complaintEvidencePO.setOrgId(0);
        complaintEvidencePO.setId(0L);
        complaintEvidencePO.setComplaintInfoId(0L);
        complaintEvidencePO.setObject("object");
        final List<ComplaintEvidencePO> complaintEvidencePOS = Arrays.asList(complaintEvidencePO);
        when(mockComplaintEvidenceMapper.selectListBySql(any(ComplaintEvidencePO.class)))
                .thenReturn(complaintEvidencePOS);

        // Run the test
        final List<ComplaintEvidenceDTO> result = complaintEvidenceServiceImplUnderTest.selectListBySql(
                complaintEvidenceDTO);

        // Verify the results
    }

    @Test
    void testSelectListBySql_ComplaintEvidenceMapperReturnsNoItems() {
        // Setup
        final ComplaintEvidenceDTO complaintEvidenceDTO = new ComplaintEvidenceDTO();
        complaintEvidenceDTO.setId(0L);
        complaintEvidenceDTO.setComplaintInfoId(0L);
        complaintEvidenceDTO.setObject("object");
        complaintEvidenceDTO.setRoNo("roNo");
        complaintEvidenceDTO.setOperator("operator");

        when(mockComplaintEvidenceMapper.selectListBySql(any(ComplaintEvidencePO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ComplaintEvidenceDTO> result = complaintEvidenceServiceImplUnderTest.selectListBySql(
                complaintEvidenceDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetById() {
        // Setup
        // Configure ComplaintEvidenceMapper.selectById(...).
        final ComplaintEvidencePO complaintEvidencePO = new ComplaintEvidencePO();
        complaintEvidencePO.setOwnerCode("ownerCode");
        complaintEvidencePO.setOrgId(0);
        complaintEvidencePO.setId(0L);
        complaintEvidencePO.setComplaintInfoId(0L);
        complaintEvidencePO.setObject("object");
        when(mockComplaintEvidenceMapper.selectById(0L)).thenReturn(complaintEvidencePO);

        // Run the test
        final ComplaintEvidenceDTO result = complaintEvidenceServiceImplUnderTest.getById(0L);

        // Verify the results
    }

    @Test
    void testGetById_ComplaintEvidenceMapperReturnsNull() {
        // Setup
        when(mockComplaintEvidenceMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> complaintEvidenceServiceImplUnderTest.getById(0L)).isInstanceOf(DALException.class);
    }

    @Test
    void testInsert() {
        // Setup
        final ComplaintEvidenceDTO complaintEvidenceDTO = new ComplaintEvidenceDTO();
        complaintEvidenceDTO.setId(0L);
        complaintEvidenceDTO.setComplaintInfoId(0L);
        complaintEvidenceDTO.setObject("object");
        complaintEvidenceDTO.setRoNo("roNo");
        complaintEvidenceDTO.setOperator("operator");

        when(mockComplaintEvidenceMapper.insert(any(ComplaintEvidencePO.class))).thenReturn(0);

        // Run the test
        final int result = complaintEvidenceServiceImplUnderTest.insert(complaintEvidenceDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdate() {
        // Setup
        final ComplaintEvidenceDTO complaintEvidenceDTO = new ComplaintEvidenceDTO();
        complaintEvidenceDTO.setId(0L);
        complaintEvidenceDTO.setComplaintInfoId(0L);
        complaintEvidenceDTO.setObject("object");
        complaintEvidenceDTO.setRoNo("roNo");
        complaintEvidenceDTO.setOperator("operator");

        // Configure ComplaintEvidenceMapper.selectById(...).
        final ComplaintEvidencePO complaintEvidencePO = new ComplaintEvidencePO();
        complaintEvidencePO.setOwnerCode("ownerCode");
        complaintEvidencePO.setOrgId(0);
        complaintEvidencePO.setId(0L);
        complaintEvidencePO.setComplaintInfoId(0L);
        complaintEvidencePO.setObject("object");
        when(mockComplaintEvidenceMapper.selectById(0L)).thenReturn(complaintEvidencePO);

        when(mockComplaintEvidenceMapper.updateById(any(ComplaintEvidencePO.class))).thenReturn(0);

        // Run the test
        final int result = complaintEvidenceServiceImplUnderTest.update(0L, complaintEvidenceDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

}
