package com.yonyou.dmscus.customer.service.common.message;

import com.yonyou.dmscus.customer.entity.dto.businessPlatform.ResponseDTO;
import com.yonyou.dmscus.customer.entity.dto.common.QueryUserPositionDTO;
import com.yonyou.dmscus.customer.entity.dto.common.UserPositionOutDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.AppPushDTO;
import com.yonyou.dmscus.customer.entity.dto.middleground.RequestDTO;
import com.yonyou.dmscus.customer.feign.MidEndAuthCenterClient;
import com.yonyou.dmscus.customer.rocketmq.AppPushRocketMQTemplate;
import org.apache.rocketmq.client.producer.SendCallback;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MessageSendServiceImplTest {

    @Mock
    private AppPushRocketMQTemplate mockAppPushRocketMQTemplate;
    @Mock
    private MidEndAuthCenterClient mockMidEndAuthCenterClient;

    @InjectMocks
    private MessageSendServiceImpl messageSendServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(messageSendServiceImplUnderTest, "topic", "topic");
        ReflectionTestUtils.setField(messageSendServiceImplUnderTest, "messageSendAppTag", "messageSendAppTag");
        ReflectionTestUtils.setField(messageSendServiceImplUnderTest, "messageSendAppEvent", "event");
        messageSendServiceImplUnderTest.volvoAppId = "appId";
    }

    @Test
    void testMessageSendApp() {
        // Setup
        final AppPushDTO appPushDTO = new AppPushDTO();
        appPushDTO.setTargetCodes(new String[]{"targetCodes"});
        appPushDTO.setTargetType("targetType");
        appPushDTO.setMode("mode");
        appPushDTO.setPriority(0L);
        appPushDTO.setAppId("appId");
        appPushDTO.setEvent("event");

        // Run the test
        final String result = messageSendServiceImplUnderTest.messageSendApp(appPushDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockAppPushRocketMQTemplate).asyncSend(eq("mqDestination"), any(Message.class), any(SendCallback.class));
    }

    @Test
    void testQueryEmpId() {
        // Setup
        final QueryUserPositionDTO empQueryDto = new QueryUserPositionDTO();
        empQueryDto.setIsOnjob("isOnjob");
        empQueryDto.setFlag(false);
        empQueryDto.setMenuId("menuId");
        empQueryDto.setRoleCodes(Arrays.asList("value"));
        empQueryDto.setUserIds(Arrays.asList(0L));

        // Configure MidEndAuthCenterClient.getEmpInfoList(...).
        final ResponseDTO<List<UserPositionOutDTO>> listResponseDTO = new ResponseDTO<>();
        listResponseDTO.setReturnCode("returnCode");
        listResponseDTO.setReturnMessage("returnMessage");
        final UserPositionOutDTO userPositionOutDTO = new UserPositionOutDTO();
        userPositionOutDTO.setUserId(0L);
        userPositionOutDTO.setEmpId(0L);
        listResponseDTO.setData(Arrays.asList(userPositionOutDTO));
        when(mockMidEndAuthCenterClient.getEmpInfoList(any(RequestDTO.class))).thenReturn(listResponseDTO);

        // Run the test
        final List<String> result = messageSendServiceImplUnderTest.queryEmpId(empQueryDto);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    void testGetUserPositionInfoList() {
        // Setup
        final QueryUserPositionDTO empQueryDto = new QueryUserPositionDTO();
        empQueryDto.setIsOnjob("isOnjob");
        empQueryDto.setFlag(false);
        empQueryDto.setMenuId("menuId");
        empQueryDto.setRoleCodes(Arrays.asList("value"));
        empQueryDto.setUserIds(Arrays.asList(0L));

        // Configure MidEndAuthCenterClient.getEmpInfoList(...).
        final ResponseDTO<List<UserPositionOutDTO>> listResponseDTO = new ResponseDTO<>();
        listResponseDTO.setReturnCode("returnCode");
        listResponseDTO.setReturnMessage("returnMessage");
        final UserPositionOutDTO userPositionOutDTO = new UserPositionOutDTO();
        userPositionOutDTO.setUserId(0L);
        userPositionOutDTO.setEmpId(0L);
        listResponseDTO.setData(Arrays.asList(userPositionOutDTO));
        when(mockMidEndAuthCenterClient.getEmpInfoList(any(RequestDTO.class))).thenReturn(listResponseDTO);

        // Run the test
        final List<UserPositionOutDTO> result = messageSendServiceImplUnderTest.getUserPositionInfoList(empQueryDto);

        // Verify the results
    }
}
