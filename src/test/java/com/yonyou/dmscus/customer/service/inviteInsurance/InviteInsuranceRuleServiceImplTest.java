package com.yonyou.dmscus.customer.service.inviteInsurance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceRuleMapper;
import com.yonyou.dmscus.customer.dao.inviteInsurance.InviteInsuranceVehicleTaskMapper;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceRuleDTO;
import com.yonyou.dmscus.customer.entity.dto.inviteInsurance.InviteInsuranceRuleVcdcParamsVo;
import com.yonyou.dmscus.customer.entity.po.inviteInsurance.InviteInsuranceRulePO;
import com.yonyou.dmscus.customer.service.common.businessPlatform.BusinessPlatformService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InviteInsuranceRuleServiceImplTest {

    @Mock
    private InviteInsuranceRuleMapper mockInviteInsuranceRuleMapper;
    @Mock
    private BusinessPlatformService mockBusinessPlatformService;
    @Mock
    private InviteInsuranceVehicleTaskMapper mockInviteInsuranceVehicleTaskMapper;

    private InviteInsuranceRuleServiceImpl inviteInsuranceRuleServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inviteInsuranceRuleServiceImplUnderTest = new InviteInsuranceRuleServiceImpl();
        inviteInsuranceRuleServiceImplUnderTest.inviteInsuranceRuleMapper = mockInviteInsuranceRuleMapper;
        inviteInsuranceRuleServiceImplUnderTest.businessPlatformService = mockBusinessPlatformService;
        inviteInsuranceRuleServiceImplUnderTest.inviteInsuranceVehicleTaskMapper = mockInviteInsuranceVehicleTaskMapper;
    }

    @Test
    void testGetInviteInsuranceRuleVcdc_BusinessPlatformServiceReturnsNoItems() {
        // Setup
        final InviteInsuranceRuleVcdcParamsVo vo = new InviteInsuranceRuleVcdcParamsVo();
        vo.setLargeAreaId("largeAreaId");
        vo.setAreaId("areaId");
        vo.setAreaManageId("areaId");
        vo.setDealerCode("dealerCode");
        vo.setDealerName("dealerName");
        vo.setCurrentPage(0);
        vo.setPageSize(0);

        when(mockBusinessPlatformService.getDealercodes("areaId", "largeAreaId", "dealerCode",
                "dealerName")).thenReturn(Collections.emptyList());

        // Run the test
        final IPage<InviteInsuranceRuleDTO> result = inviteInsuranceRuleServiceImplUnderTest.getInviteInsuranceRuleVcdc(
                vo);

        // Verify the results
    }
}
