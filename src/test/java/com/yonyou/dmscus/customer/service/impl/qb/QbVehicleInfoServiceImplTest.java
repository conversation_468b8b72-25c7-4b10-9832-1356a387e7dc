package com.yonyou.dmscus.customer.service.impl.qb;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yonyou.dmscloud.framework.service.excel.ExcelRead;
import com.yonyou.dmscloud.framework.service.excel.impl.AbstractExcelReadCallBack;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordDetailMapper;
import com.yonyou.dmscus.customer.dao.invitationFollow.InviteVehicleRecordMapper;
import com.yonyou.dmscus.customer.dao.invitationautocreate.InviteVehicleTaskMapper;
import com.yonyou.dmscus.customer.dao.inviteSaAllocateRule.InviteSaAllocateRuleDetailMapper;
import com.yonyou.dmscus.customer.dao.inviteSaAllocateRule.InviteSaAllocateRuleMapper;
import com.yonyou.dmscus.customer.dao.qb.InviteQBVehicleImportMapper;
import com.yonyou.dmscus.customer.dao.qb.QbVehicleInfoMapper;
import com.yonyou.dmscus.customer.dto.QBVehicleImportDTO;
import com.yonyou.dmscus.customer.dto.VehicleOwnerVO;
import com.yonyou.dmscus.customer.entity.dto.complaint.ImportTempResult;
import com.yonyou.dmscus.customer.entity.dto.inviteRule.InviteDuplicateRemovalRuleDTO;
import com.yonyou.dmscus.customer.entity.dto.qb.QbVehicleInfoDTO;
import com.yonyou.dmscus.customer.entity.dto.saSllocateDlr.SaSllocateDlrDto;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordDetailPO;
import com.yonyou.dmscus.customer.entity.po.invitationFollow.InviteVehicleRecordPO;
import com.yonyou.dmscus.customer.entity.po.invitationautocreate.InviteVehicleTaskPO;
import com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule.InviteSaAllocateRuleDetailPO;
import com.yonyou.dmscus.customer.entity.po.inviteSaAllocateRule.InviteSaAllocateRulePO;
import com.yonyou.dmscus.customer.entity.po.qb.InviteQBVehicleImportPO;
import com.yonyou.dmscus.customer.feign.ReportCommonClient;
import com.yonyou.dmscus.customer.middleInterface.ResponseDTO;
import com.yonyou.dmscus.customer.service.common.IMiddleGroundVehicleService;
import com.yonyou.dmscus.customer.service.invitationFollow.InviteVehicleRecordService;
import com.yonyou.dmscus.customer.service.inviteRule.InviteDuplicateRemovalRuleService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class QbVehicleInfoServiceImplTest {

    @Mock
    private QbVehicleInfoMapper mockQbVehicleInfoMapper;
    @Mock
    private ReportCommonClient mockReportCommonClient;
    @Mock
    private ExcelRead<QBVehicleImportDTO> mockExcelReadService;
    @Mock
    private IMiddleGroundVehicleService mockIMiddleGroundVehicleService;
    @Mock
    private InviteQBVehicleImportMapper mockInviteQBVehicleImportMapper;
    @Mock
    private InviteVehicleTaskMapper mockInviteVehicleTaskMapper;
    @Mock
    private InviteVehicleRecordMapper mockInviteVehicleRecordMapper;
    @Mock
    private InviteSaAllocateRuleMapper mockInviteSaAllocateRuleMapper;
    @Mock
    private InviteSaAllocateRuleDetailMapper mockInviteSaAllocateRuleDetailMapper;
    @Mock
    private InviteVehicleRecordService mockInviteVehicleRecordService;
    @Mock
    private InviteDuplicateRemovalRuleService mockInviteDuplicateRemovalRuleService;
    @Mock
    private InviteVehicleRecordDetailMapper mockInviteVehicleRecordDetailMapper;

    private QbVehicleInfoServiceImpl qbVehicleInfoServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        qbVehicleInfoServiceImplUnderTest = new QbVehicleInfoServiceImpl();
        qbVehicleInfoServiceImplUnderTest.qbVehicleInfoMapper = mockQbVehicleInfoMapper;
        qbVehicleInfoServiceImplUnderTest.reportCommonClient = mockReportCommonClient;
        qbVehicleInfoServiceImplUnderTest.excelReadService = mockExcelReadService;
        qbVehicleInfoServiceImplUnderTest.iMiddleGroundVehicleService = mockIMiddleGroundVehicleService;
        qbVehicleInfoServiceImplUnderTest.inviteQBVehicleImportMapper = mockInviteQBVehicleImportMapper;
        qbVehicleInfoServiceImplUnderTest.inviteVehicleTaskMapper = mockInviteVehicleTaskMapper;
        qbVehicleInfoServiceImplUnderTest.inviteVehicleRecordMapper = mockInviteVehicleRecordMapper;
        qbVehicleInfoServiceImplUnderTest.inviteSaAllocateRuleMapper = mockInviteSaAllocateRuleMapper;
        qbVehicleInfoServiceImplUnderTest.inviteSaAllocateRuleDetailMapper = mockInviteSaAllocateRuleDetailMapper;
        qbVehicleInfoServiceImplUnderTest.inviteVehicleRecordService = mockInviteVehicleRecordService;
        qbVehicleInfoServiceImplUnderTest.inviteDuplicateRemovalRuleService = mockInviteDuplicateRemovalRuleService;
        qbVehicleInfoServiceImplUnderTest.inviteVehicleRecordDetailMapper = mockInviteVehicleRecordDetailMapper;
    }

    @Test
    void testSelectPageBysql() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final QbVehicleInfoDTO dto = new QbVehicleInfoDTO();
        dto.setVin("vin");
        dto.setDealerCode("dealerCode");
        dto.setIsPerformed(0);
        dto.setQbNumber("qbNumber");
        dto.setIsClosed(0);

        // Configure QbVehicleInfoMapper.selectPageBySql(...).
        final QbVehicleInfoDTO qbVehicleInfoDTO = new QbVehicleInfoDTO();
        qbVehicleInfoDTO.setVin("vin");
        qbVehicleInfoDTO.setDealerCode("dealerCode");
        qbVehicleInfoDTO.setIsPerformed(0);
        qbVehicleInfoDTO.setQbNumber("qbNumber");
        qbVehicleInfoDTO.setIsClosed(0);
        final List<QbVehicleInfoDTO> qbVehicleInfoDTOS = Arrays.asList(qbVehicleInfoDTO);
        final QbVehicleInfoDTO qbVehicleInfoDTO1 = new QbVehicleInfoDTO();
        qbVehicleInfoDTO1.setVin("vin");
        qbVehicleInfoDTO1.setDealerCode("dealerCode");
        qbVehicleInfoDTO1.setIsPerformed(0);
        qbVehicleInfoDTO1.setQbNumber("qbNumber");
        qbVehicleInfoDTO1.setIsClosed(0);
        when(mockQbVehicleInfoMapper.selectPageBySql(any(Page.class), eq(qbVehicleInfoDTO1)))
                .thenReturn(qbVehicleInfoDTOS);

        // Run the test
        final IPage<QbVehicleInfoDTO> result = qbVehicleInfoServiceImplUnderTest.selectPageBysql(page, dto);

        // Verify the results
    }

    @Test
    void testSelectPageBysql_QbVehicleInfoMapperReturnsNoItems() {
        // Setup
        final Page page = new Page<>(0L, 0L, 0L, false);
        final QbVehicleInfoDTO dto = new QbVehicleInfoDTO();
        dto.setVin("vin");
        dto.setDealerCode("dealerCode");
        dto.setIsPerformed(0);
        dto.setQbNumber("qbNumber");
        dto.setIsClosed(0);

        // Configure QbVehicleInfoMapper.selectPageBySql(...).
        final QbVehicleInfoDTO qbVehicleInfoDTO = new QbVehicleInfoDTO();
        qbVehicleInfoDTO.setVin("vin");
        qbVehicleInfoDTO.setDealerCode("dealerCode");
        qbVehicleInfoDTO.setIsPerformed(0);
        qbVehicleInfoDTO.setQbNumber("qbNumber");
        qbVehicleInfoDTO.setIsClosed(0);
        when(mockQbVehicleInfoMapper.selectPageBySql(any(Page.class), eq(qbVehicleInfoDTO)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final IPage<QbVehicleInfoDTO> result = qbVehicleInfoServiceImplUnderTest.selectPageBysql(page, dto);

        // Verify the results
    }
}
