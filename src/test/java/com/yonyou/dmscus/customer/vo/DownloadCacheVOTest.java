package com.yonyou.dmscus.customer.vo;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class DownloadCacheVOTest {

    private DownloadCacheVO downloadCacheVOUnderTest;

    @BeforeEach
    void setUp() {
        downloadCacheVOUnderTest = new DownloadCacheVO();
    }

    @Test
    void testDownloadUrlGetterAndSetter() {
        final String downloadUrl = "downloadUrl";
        downloadCacheVOUnderTest.setDownloadUrl(downloadUrl);
        assertThat(downloadCacheVOUnderTest.getDownloadUrl()).isEqualTo(downloadUrl);
    }
}
