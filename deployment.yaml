apiVersion: apps/v1
kind: Deployment
metadata:
  name: dmscus-customer
  labels:
    app: dmscus-customer
spec:
  selector:
    matchLabels:
      app: dmscus-customer
  replicas: 1
  template:
    metadata:
      labels:
        app: dmscus-customer
    spec:
      nodeSelector:
        failure-domain.beta.kubernetes.io/zone: cn-shanghai-e
      imagePullSecrets:
        - name: nexus-key
      hostAliases:
        - hostnames:
            - newbie-test-devops.digitalvolvo.com
          ip: **************
      containers:
        - name: dmscus-customer
          image: "devops.digitalvolvo.com/nexus/repository/newbie-yongyou-docker-registry/dmscus-customer:1.0.0"
          imagePullPolicy: Always
          resources:
            limits:
              cpu: 0.5
              memory: 1024Mi
            requests:
              cpu: 0.1
              memory: 512Mi
          ports:
            - containerPort: 8002
              name: dmscus-customer
          env:
            - name: JAVA_OPTS
              value: >-
                -Dspring.cloud.consul.host=consul-cluster-client
                -Dspring.cloud.consul.port=8500
                -Dapp.id=dms-platform
                -Dapollo.meta=http://newbie-apollo-meta-server-dev-service.dasouche-dev:8080/apollo-meta
                -Dspring.cloud.config.enabled=false
                -Dspring.cloud.config.discovery.enabled=false
                -Dapollo.bootstrap.enabled=true
                -Dapollo.bootstrap.eagerLoad.enabled=true
                -Dapollo.bootstrap.namespaces=dmscus-customer.yml
            - name: SPRING_ACTIVE_ENV
              value: 'test'
            - name: SW_AGENT_NAME
              value: dmscus-customer
            - name: SW_AGENT_COLLECTOR_BACKEND_SERVICES
              valueFrom:
                configMapKeyRef:
                  key: SW_AGENT_COLLECTOR_BACKEND_SERVICES
                  name: skywalking-config
#          livenessProbe:
#            httpGet:
#              path: /actuator/health
#              port: 8002
#            initialDelaySeconds: 180
#            periodSeconds: 60
#            failureThreshold: 5
#            successThreshold: 1
#            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 8002
            initialDelaySeconds: 60
            periodSeconds: 10
            failureThreshold: 5
            successThreshold: 2
            timeoutSeconds: 10


---
apiVersion: v1
kind: Service
metadata:
  name: newbie-java-dmscus-customer-svc
  labels:
    app: newbie-java-dmscus-customer-svc
spec:
  ports:
    - port: 8002
      name: newbie-java-dmscus-customer-svc
  selector:
    app: dmscus-customer