<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.2.2.RELEASE</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.yonyou.dmscus</groupId>
	<artifactId>dmscus.customer</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>dmscus.customer</name>
	<packaging>jar</packaging>
	<description>Dmscus project for Spring Boot</description>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
		<start-class>com.yonyou.dmscus.customer.CustomerApplication</start-class>
		<!-- 相关依赖的版本 -->
		<!-- 二方库 -->
		<commonElegance.verison>0.0.3-SNAPSHOT</commonElegance.verison>
		<!-- 三方库 -->
		<mapper.version>3.4.2</mapper.version>
		<mapperStarter.version>1.1.3</mapperStarter.version>
		<druidStarter.version>1.1.4</druidStarter.version>
		<pagehelpStarter.version>1.2.3</pagehelpStarter.version>
		<spring-cloud.version>Hoxton.SR1</spring-cloud.version>
		<log4j2.version>2.17.1</log4j2.version>
	</properties>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- web 支持 1. web mvc 2. restful 3. jackson 4. aop ..... -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-config</artifactId>
		</dependency>
		<!-- 优雅编码包 -->
		<dependency>
			<groupId>com.yonyou.cloud</groupId>
			<artifactId>common-elegance</artifactId>
			<version>${commonElegance.verison}</version>
		</dependency>
		<!-- mysql 驱动 -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis-typehandlers-jsr310</artifactId>
			<version>1.0.1</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.41</version>
		</dependency>
		<dependency>
			<groupId>com.yonyou.f4</groupId>
			<artifactId>activejdbc-spring-boot-starter</artifactId>
			<version>1.0.0</version>
			<exclusions>
				<exclusion>
					<artifactId>hutool-all</artifactId>
					<groupId>cn.hutool</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>com.yonyou.dmscloud</groupId>
			<artifactId>dmscloud.framework</artifactId>
			<version>2.4.5-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>jcifs</groupId>
			<artifactId>jcifs</artifactId>
			<version>1.3.17</version>
		</dependency>

		<dependency>
			<groupId>com.yonyou.cyxdms</groupId>
			<artifactId>common</artifactId>
			<version>1.0.7-RELEASE</version>
			<exclusions>
				<exclusion>
					<artifactId>volvo-dms-framework-starter</artifactId>
					<groupId>com.yonyou.cyxdms</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.10.2</version>
		</dependency>

		<!-- kafka配置-->
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>


		<!-- 普罗米修斯监控-->
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>

		<dependency>
		   <groupId>com.souche</groupId>
		   <artifactId>souche-sdk</artifactId>
		   <version>1.0.1</version>
		</dependency>

		<!-- skywalking -->
		<dependency>
			<groupId>org.apache.skywalking</groupId>
			<artifactId>apm-toolkit-logback-1.x</artifactId>
			<version>7.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.ctrip.framework.apollo</groupId>
			<artifactId>apollo-client</artifactId>
			<version>1.6.0</version>
		</dependency>
		<dependency>
			<groupId>com.volvo.file</groupId>
			<artifactId>mid-end-file-api</artifactId>
			<version>2.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.volvo.file</groupId>
			<artifactId>mid-end-file-api</artifactId>
			<version>2.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-csv</artifactId>
			<version>1.3</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
			<version>2.2.5.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
			<version>2.2.5.RELEASE</version>
		</dependency>

		<!-- xxl -->
		<dependency>
			<groupId>com.xuxueli</groupId>
			<artifactId>xxl-job-core</artifactId>
			<version>2.3.0</version>
		</dependency>

		<!-- Logback Appender -->
		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java</artifactId>
			<version>2.5.0</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun.openservices</groupId>
			<artifactId>aliyun-log-logback-appender</artifactId>
			<version>0.1.18</version>
			<exclusions>
				<exclusion>
					<artifactId>logback-classic</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
			</exclusions>
		</dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>13.0</version>
            <scope>compile</scope>
        </dependency>

		<dependency>
			<groupId>org.apache.rocketmq</groupId>
			<artifactId>rocketmq-spring-boot-starter</artifactId>
			<version>2.2.2</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.3.10</version>
		</dependency>
		<!-- 单元测试需要引入的包，主要是为了走测试流程和生成测试覆盖率报告-->
		<dependency>
			<groupId>net.bytebuddy</groupId>
			<artifactId>byte-buddy-agent</artifactId>
			<version>1.9.10</version>
			<scope>test</scope>
		</dependency>
		<!--mock单元测试数据，主要是为了跳过数据真实查询,这里之所以引入是因为需要使用静态内联方法，否则不需要引入-->
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>3.9.0</version>
			<scope>test</scope>
		</dependency>
		<!--生成单元测试报告-->
		<dependency>
			<groupId>org.jacoco</groupId>
			<artifactId>org.jacoco.agent</artifactId>
			<version>0.8.7</version>
			<classifier>runtime</classifier>
		</dependency>
		<!-- mock静态方法需要引入这个依赖 -->
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-inline</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- 听云 -->
		<dependency>
			<groupId>com.tingyun</groupId>
			<artifactId>tingyun-agent-api</artifactId>
			<version>3.6.7</version>
		</dependency>
		<!-- 业务指标监控用户接入 -->
		<dependency>
			<groupId>com.volvo</groupId>
			<artifactId>vdevops-biz-log-utils</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>org.reflections</groupId>
			<artifactId>reflections</artifactId>
			<version>0.9.12</version>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>

			<plugin>
				<groupId>org.javalite</groupId>
				<artifactId>activejdbc-instrumentation</artifactId>
				<version>1.4.13</version>
			</plugin>
			<plugin>
				<groupId>org.sonarsource.scanner.maven</groupId>
				<artifactId>sonar-maven-plugin</artifactId>
				<version>3.7.0.1746</version>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.7</version>
				<configuration>
					<excludes>
						<exclude>com/yonyou/dmscloud/**</exclude>
<!--						<exclude>com/yonyou/dmscus/customer/entity/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/annotation/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/bean/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/common/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/constants/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/enums/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/util/**</exclude>-->
<!--&lt;!&ndash;						<exclude>com/yonyou/dmscus/customer/utils/**</exclude>&ndash;&gt;-->
<!--						<exclude>com/yonyou/dmscus/customer/dto/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/vo/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/feign/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/enevt/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/ossexcel/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/rocketmq/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/job/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/dao/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/entity/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/annotation/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/bean/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/common/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/constants/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/enums/**</exclude>-->
<!--&lt;!&ndash;						<exclude>com/yonyou/dmscus/customer/util/**</exclude>&ndash;&gt;-->
<!--&lt;!&ndash;						<exclude>com/yonyou/dmscus/customer/utils/**</exclude>&ndash;&gt;-->
<!--						<exclude>com/yonyou/dmscus/customer/dto/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/vo/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/feign/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/enevt/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/ossexcel/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/rocketmq/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/job/**</exclude>-->
<!--						<exclude>com/yonyou/dmscus/customer/dao/**</exclude>-->

						<!--<exclude>com/volvo/bff/volvoworks/aftersales/common/advice/**</exclude>
                        <exclude>com/volvo/bff/volvoworks/aftersales/common/aspect/**</exclude>
                        <exclude>com/volvo/bff/volvoworks/aftersales/common/config/**</exclude>
                        <exclude>com/volvo/bff/volvoworks/aftersales/common/exception/**</exclude>
                        <exclude>com/volvo/bff/volvoworks/aftersales/common/model/**</exclude>-->

					</excludes>
				</configuration>
				<executions>
					<execution>
						<id>default-prepare-agent</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
						<configuration>
							<propertyName>surfireArgLine</propertyName>
							<includes>
								<include>**/*</include>
							</includes>
						</configuration>
					</execution>
					<execution>
						<id>default-report</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<!--生成单元测试报告-->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<useSystemClassLoader>false</useSystemClassLoader>
					<forkMode>once</forkMode>
					<reuseForks>true</reuseForks>
					<!--suppress UnresolvedMavenProperty -->
					<argLine>-Dfile.encoding=UTF-8 ${surfireArgLine}</argLine>
					<testFailureIgnore>true</testFailureIgnore>
				</configuration>
			</plugin>
		</plugins>
	</build>


	<distributionManagement>
		<repository>
			<id>yongyou-release</id>
			<name>internal release</name>
			<url>https://devops.digitalvolvo.com/nexus/repository/newbie-yongyou-maven-releases/</url>
			<uniqueVersion>true</uniqueVersion>
		</repository>
		<snapshotRepository>
			<id>yongyou-snapshots</id>
			<name>internal snapshots</name>
			<url>https://devops.digitalvolvo.com/nexus/repository/newbie-yongyou-maven-snapshots/</url>
			<uniqueVersion>true</uniqueVersion>
		</snapshotRepository>
	</distributionManagement>

</project>
